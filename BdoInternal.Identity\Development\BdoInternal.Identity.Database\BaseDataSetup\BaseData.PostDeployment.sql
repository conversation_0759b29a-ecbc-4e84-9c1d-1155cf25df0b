MERGE INTO [dbo].[Language] AS Target
USING (VALUES
  (1,'en','English')
 ,(2,'fr','French')
) AS Source ([Id],[Code],[Name])
ON (Target.[Id] = Source.[Id])
WHEN MATCHED AND (Target.[Code] <> Source.[Code] OR Target.[Name] <> Source.[Name]) THEN
	UPDATE SET
		[Code] = Source.[Code], 
		[Name] = Source.[Name]
WHEN NOT MATCHED BY TARGET THEN
	INSERT([Id],[Code],[Name])
	VALUES(Source.[Id],Source.[Code],Source.[Name])
WHEN NOT MATCHED BY SOURCE THEN 
	DELETE;
GO

MERGE INTO [dbo].[Role] AS Target
USING (VALUES
  (1, 'PRPT Administrator')
 ,(2, 'Leadership Partner')
 ,(3, 'Active Partner')
 ,(4, 'APC Administrator')
 ,(5, 'Group Administrator')
 ,(6, 'Managing Partner')
 ,(7, 'Regulator')
 ,(8, 'CEO')
 ,(9, 'PR Administrator')
 ,(10, 'PRA Administrator')
 ,(11, 'Partner Administrator')
 ,(12, 'COO')
 ,(13, 'BDO Employee')
 ,(14, 'Delegate')
) AS Source ([Id],[Name])
ON (Target.[Id] = Source.[Id])
WHEN MATCHED AND (Target.[Name] <> Source.[Name]) THEN
	UPDATE SET
		[Name] = Source.[Name]		
WHEN NOT MATCHED BY TARGET THEN
	INSERT([Id],[Name])
	VALUES(Source.[Id],Source.[Name])
WHEN NOT MATCHED BY SOURCE THEN 
	DELETE;
GO

MERGE INTO [dbo].[Permission] AS Target
USING (VALUES
  (1, 'User Create')
 ,(2, 'User Read')
 ,(3, 'User Update')
 ,(4, 'User Delete')
 ,(5, 'Admin Access')
) AS Source ([Id],[Name])
ON (Target.[Id] = Source.[Id])
WHEN MATCHED AND (Target.[Name] <> Source.[Name]) THEN
	UPDATE SET
		[Name] = Source.[Name]		
WHEN NOT MATCHED BY TARGET THEN
	INSERT([Id],[Name])
	VALUES(Source.[Id],Source.[Name])
WHEN NOT MATCHED BY SOURCE THEN 
	DELETE;
GO

MERGE INTO [dbo].[RolePermission] AS Target
USING (VALUES
   (1, 1)
  ,(1, 2)
  ,(1, 3)
  ,(1, 4)
  ,(1, 5)
  ,(2, 2)
  ,(2, 3)
  ,(2, 5)
  ,(2, 5)
) AS Source ([RoleId],[PermissionId])
ON (Target.[RoleId] = Source.[RoleId] and Target.[PermissionId] = Source.[PermissionId])
--WHEN MATCHED AND (Target.[Name] <> Source.[Name]) THEN
--	UPDATE SET
--		[Name] = Source.[Name]		
WHEN NOT MATCHED BY TARGET THEN
	INSERT([RoleId],[PermissionId])
	VALUES(Source.[RoleId],Source.[PermissionId])
WHEN NOT MATCHED BY SOURCE THEN 
	DELETE;
GO

---- SET IDENTITY_INSERT [dbo].[Timezone] ON
 
--MERGE INTO [dbo].[Timezone] AS Target
--USING (VALUES
--  (1,'-12:00','Dateline Standard Time',-12.00)
-- ,(2,'-11:00','UTC-11',-11.00)
-- ,(3,'-10:00','Aleutian Standard Time',-10.00)
-- ,(4,'-09:30','Marquesas Standard Time',-9.50)
-- ,(5,'-09:00','UTC-09',-9.00)
-- ,(6,'-08:00','Pacific Standard Time',-8.00)
-- ,(7,'-07:00','Mountain Standard Time',-7.00)
-- ,(8,'-06:00','Canada Central Standard Time',-6.00)
-- ,(9,'-05:00','Eastern Standard Time',-5.00)
-- ,(10,'-04:00','Atlantic Standard Time',-4.00)
-- ,(11,'-03:30','Newfoundland Standard Time',-3.50)
-- ,(12,'-03:00','Tocantins Standard Time',-3.00)
-- ,(13,'-02:00','UTC-02',-2.00)
-- ,(14,'-01:00','Azores Standard Time',-1.00)
-- ,(15,'+00:00','GMT Standard Time',0.00)
-- ,(16,'+01:00','Morocco Standard Time',1.00)
-- ,(17,'+02:00','Libya Standard Time',2.00)
-- ,(18,'+03:00','Turkey Standard Time',3.00)
-- ,(19,'+03:30','Iran Standard Time',3.50)
-- ,(20,'+04:00','Astrakhan Standard Time',4.00)
-- ,(21,'+04:30','Afghanistan Standard Time',4.50)
-- ,(22,'+05:00','West Asia Standard Time',5.00)
-- ,(23,'+05:30','India Standard Time',5.50)
-- ,(24,'+05:45','Nepal Standard Time',5.75)
-- ,(25,'+06:00','Central Asia Standard Time',6.00)
-- ,(26,'+06:30','Myanmar Standard Time',6.50)
-- ,(27,'+07:00','Altai Standard Time',7.00)
-- ,(28,'+08:00','China Standard Time',8.00)
-- ,(29,'+08:45','Aus Central W. Standard Time',8.75)
-- ,(30,'+09:00','Transbaikal Standard Time',9.00)
-- ,(31,'+09:30','Cen. Australia Standard Time',9.50)
-- ,(32,'+10:00','E. Australia Standard Time',10.00)
-- ,(33,'+10:30','Lord Howe Standard Time',10.50)
-- ,(34,'+11:00','Bougainville Standard Time',11.00)
-- ,(35,'+12:00','New Zealand Standard Time',12.00)
-- ,(36,'+12:45','Chatham Islands Standard Time',12.75)
-- ,(37,'+13:00','Tonga Standard Time',13.00)
-- ,(38,'+14:00','Line Islands Standard Time',14.00)
--) AS Source ([Id],[Code],[Name],[Offset])
--ON (Target.[Id] = Source.[Id])
--WHEN MATCHED AND (Target.[Code] <> Source.[Code] OR Target.[Name] <> Source.[Name] OR Target.[Offset] <> Source.[Offset]) THEN
-- UPDATE SET
-- [Code] = Source.[Code], 
--[Name] = Source.[Name], 
--[Offset] = Source.[Offset]
--WHEN NOT MATCHED BY TARGET THEN
-- INSERT([Id],[Code],[Name],[Offset])
-- VALUES(Source.[Id],Source.[Code],Source.[Name],Source.[Offset])
--WHEN NOT MATCHED BY SOURCE THEN 
-- DELETE;

----SET IDENTITY_INSERT [dbo].[Timezone] OFF;
--GO

-- Note: AuthProvider settings corporate with startup.cs AddOpenIdConnect settings in Identity Server project.
MERGE INTO [identity].[AuthProvider] AS Target
USING (VALUES
  ('App', 'Default Database driving authentication', 1)
 ,('BDO-ITINV-AAD', 'BDO Canada LLP IT Innovation Azure AD', 1)
 ,('BDO-AAD', 'BDO Canada LLP Azure AD', 1)
) AS Source ([Id],[Name], [IsActive])
ON (Target.[Id] = Source.[Id])
WHEN MATCHED AND (Target.[Name] <> Source.[Name] or Target.[IsActive]<> Source.[IsActive]) THEN
	UPDATE SET
		[Name] = Source.[Name]		
WHEN NOT MATCHED BY TARGET THEN
	INSERT([Id],[Name],[IsActive])
	VALUES(Source.[Id],Source.[Name], Source.[IsActive])
WHEN NOT MATCHED BY SOURCE THEN 
	DELETE;
GO

--Insert test users (Commented out by David@2023-07-31)
--IF (CHARINDEX('prod', @@SERVERNAME) <= 0) AND (CHARINDEX('uat', @@SERVERNAME) <= 0)
--BEGIN
--	PRINT 'Insert Test Users'
--	-- password = "Password1"
--	MERGE INTO [dbo].[User] AS Target
--	USING (VALUES
--	  ('BFD02B5C-2408-4870-8E1F-06C7A72187F6', 'APP', 'prptadmin','',0x999000A49E8DD91ED5726EA0AF1745C8F712640D6563F7FDE1977013B730CB3C9D78F99EC07FC59B0ADEAB5CBA0380DA, 0xE73685F552,0,'Report','Administrator','<EMAIL>',1,1)  
--	  ,('7f5307f8-af26-ec11-96ec-98af65a69f68', 'APP', 'apcadmin','',0x999000A49E8DD91ED5726EA0AF1745C8F712640D6563F7FDE1977013B730CB3C9D78F99EC07FC59B0ADEAB5CBA0380DA, 0xE73685F552,0,'APC','Admin','<EMAIL>',1,1) -- site admin
--	  ,('7f5307f8-af26-ec11-96ec-98af65a69f69', 'APP', 'praadmin','',0x999000A49E8DD91ED5726EA0AF1745C8F712640D6563F7FDE1977013B730CB3C9D78F99EC07FC59B0ADEAB5CBA0380DA, 0xE73685F552,0,'PRA','Admin','<EMAIL>',1,1) -- site admin
--	  ,('7f5307f8-af26-ec11-96ec-98af65a69f67', 'APP', 'pradmin','',0x999000A49E8DD91ED5726EA0AF1745C8F712640D6563F7FDE1977013B730CB3C9D78F99EC07FC59B0ADEAB5CBA0380DA, 0xE73685F552,0,'PR','Admin','<EMAIL>',1,1) -- site admin
--	  ,('D3FB7F6A-6487-4190-9D9F-0ECD7A25E239', 'APP','partner','',0x999000A49E8DD91ED5726EA0AF1745C8F712640D6563F7FDE1977013B730CB3C9D78F99EC07FC59B0ADEAB5CBA0380DA, 0xE73685F552,0,'Partner','Partner','<EMAIL>',1,1)  
--	  ,('0386a900-b026-ec11-96ec-98af65a69f67', 'APP', 'edefreitas','2731f31e-162f-4219-bcb0-e1872dcf52df',0x999000A49E8DD91ED5726EA0AF1745C8F712640D6563F7FDE1977013B730CB3C9D78F99EC07FC59B0ADEAB5CBA0380DA, 0xE73685F552,0,'Eloisa','De Freitas','<EMAIL>',1,1) -- group admin
--	  ,('0386a900-b026-ec11-96ec-98af65a69f68', 'APP', 'ldunlop','EBA5A68E-944E-4678-917E-2160D6AB1A91',0x999000A49E8DD91ED5726EA0AF1745C8F712640D6563F7FDE1977013B730CB3C9D78F99EC07FC59B0ADEAB5CBA0380DA, 0xE73685F552,0,'Lenore','Dunlop','<EMAIL>',1,1) -- group admin
--	  ,('0386a900-b026-ec11-96ec-98af65a69f69', 'APP', 'kmckernan','45B6D9DA-F95C-42DA-9B04-A4C75E95400E',0x999000A49E8DD91ED5726EA0AF1745C8F712640D6563F7FDE1977013B730CB3C9D78F99EC07FC59B0ADEAB5CBA0380DA, 0xE73685F552,0,'Kirsten','McKernan','<EMAIL>',1,1) -- group admin  
--	  -- Tax Test group admin
--	  ,('0386a900-b026-ec11-96ec-98af65a69f70', 'APP', 'jcasey','6b5dbb4b-1822-465c-a699-3aaa02beefe3',0x999000A49E8DD91ED5726EA0AF1745C8F712640D6563F7FDE1977013B730CB3C9D78F99EC07FC59B0ADEAB5CBA0380DA, 0xE73685F552,0,'Jaclyn','Casey','<EMAIL>',1,1) -- group admin  

--	  ,('0ca84117-b026-ec11-96ec-98af65a69f68', 'APP', 'dsmuckler','5CA0DBA5-4E57-4ABE-BD30-557F7FCAE66B',0x999000A49E8DD91ED5726EA0AF1745C8F712640D6563F7FDE1977013B730CB3C9D78F99EC07FC59B0ADEAB5CBA0380DA, 0xE73685F552,0,'Daphna','Smuckler','<EMAIL>',1,1) --A&A MP
--	  ,('B6BDCA35-4D96-4C26-B6B2-D15E285147DB', 'APP', 'jpyzer','D71D6ABA-427A-4FA5-8EDA-0D1354B3B58C',0x999000A49E8DD91ED5726EA0AF1745C8F712640D6563F7FDE1977013B730CB3C9D78F99EC07FC59B0ADEAB5CBA0380DA, 0xE73685F552,0,'Jennifer','Pyzer','<EMAIL>',1,1) --partner
--	  ,('4556538a-9c41-ec11-96f2-98af65a69f68', 'APP', 'rschwartzentruber','3F1E9CBA-6F87-4752-911C-B5283D87D737',0x999000A49E8DD91ED5726EA0AF1745C8F712640D6563F7FDE1977013B730CB3C9D78F99EC07FC59B0ADEAB5CBA0380DA, 0xE73685F552,0,'Randy','Schwartzentruber','<EMAIL>',1,1) --partner
--	  ,('4656538a-9c41-ec11-96f2-98af65a69f68', 'APP', 'mpiroddi','7D18328F-7ED0-4459-91A0-FF04EB7796E1',0x999000A49E8DD91ED5726EA0AF1745C8F712640D6563F7FDE1977013B730CB3C9D78F99EC07FC59B0ADEAB5CBA0380DA, 0xE73685F552,0,'Mario','Piroddi','<EMAIL>',1,1) --partner

--	  ,('483f5164-b026-ec11-96ec-98af65a69f68', 'APP', 'regulator','',0x999000A49E8DD91ED5726EA0AF1745C8F712640D6563F7FDE1977013B730CB3C9D78F99EC07FC59B0ADEAB5CBA0380DA, 0xE73685F552,0,'Regulator','Regulator','<EMAIL>',1,1) -- regulator
--	  ,('484f5164-b026-ec11-96ec-98af65a69f68', 'APP', 'pkramer','FFEC1449-DB9E-4FCE-B9E1-9E8DC636994B',0x999000A49E8DD91ED5726EA0AF1745C8F712640D6563F7FDE1977013B730CB3C9D78F99EC07FC59B0ADEAB5CBA0380DA, 0xE73685F552,0,'Patrick','Kramer','<EMAIL>',1,1) -- CEO
  
--	  ,('412e8a1d-9c41-ec11-96f2-98af65a69f68', 'APP', 'dsimkins','61B03F52-704B-4F2C-A2D8-5473D3F6F094',0x999000A49E8DD91ED5726EA0AF1745C8F712640D6563F7FDE1977013B730CB3C9D78F99EC07FC59B0ADEAB5CBA0380DA, 0xE73685F552,0,'David','Simkins','<EMAIL>',1,1) -- HQ MP
--	  --,('dcd74d83-9c41-ec11-96f2-98af65a69f68', 'APP', 'dwalsh','07C52DD6-279F-4A96-B256-B376CF8F1B58',0x999000A49E8DD91ED5726EA0AF1745C8F712640D6563F7FDE1977013B730CB3C9D78F99EC07FC59B0ADEAB5CBA0380DA, 0xE73685F552,0,'Dave','Walsh','<EMAIL>',1,1) -- partner
--	  ,('ddd74d83-9c41-ec11-96f2-98af65a69f68', 'APP', 'rstpierre','51C9F6AB-3E69-4FD3-B7CE-401A5DED0DE0',0x999000A49E8DD91ED5726EA0AF1745C8F712640D6563F7FDE1977013B730CB3C9D78F99EC07FC59B0ADEAB5CBA0380DA, 0xE73685F552,0,'Ron','St. Pierre','<EMAIL>',1,1) -- partner
--	  ,('4456538a-9c41-ec11-96f2-98af65a69f68', 'APP', 'kficocelli','71135EA3-BAA6-454B-8CB6-02B7C8B47BF7',0x999000A49E8DD91ED5726EA0AF1745C8F712640D6563F7FDE1977013B730CB3C9D78F99EC07FC59B0ADEAB5CBA0380DA, 0xE73685F552,0,'Ken','Ficocelli','<EMAIL>',1,1) -- partner

--	  ,('412e8a1d-9c41-ec11-96f2-98af65a69f76', 'APP', 'dwalsh','07C52DD6-279F-4A96-B256-B376CF8F1B58',0x999000A49E8DD91ED5726EA0AF1745C8F712640D6563F7FDE1977013B730CB3C9D78F99EC07FC59B0ADEAB5CBA0380DA, 0xE73685F552,0,'Dave','Walsh','<EMAIL>',1,1) -- Tax MP
--	  ,('dcd74d83-9c41-ec11-96f2-98af65a69f77', 'APP', 'rtrowbridge','4630A722-E8DA-4BEC-ABBF-E4B721F631F7',0x999000A49E8DD91ED5726EA0AF1745C8F712640D6563F7FDE1977013B730CB3C9D78F99EC07FC59B0ADEAB5CBA0380DA, 0xE73685F552,0,'Rita','Trowbridge','<EMAIL>',1,1) -- partner
--	  ,('ddd74d83-9c41-ec11-96f2-98af65a69f78', 'APP', 'cneil','468AFAAB-C8FD-4A3F-86CB-A440D5557043',0x999000A49E8DD91ED5726EA0AF1745C8F712640D6563F7FDE1977013B730CB3C9D78F99EC07FC59B0ADEAB5CBA0380DA, 0xE73685F552,0,'Cory','Neil','<EMAIL>',1,1) -- partner
--	  ,('4456538a-9c41-ec11-96f2-98af65a69f79', 'APP', 'dgraystone','48B9D1CF-EEF4-4CFD-9CBA-05754B2DA009',0x999000A49E8DD91ED5726EA0AF1745C8F712640D6563F7FDE1977013B730CB3C9D78F99EC07FC59B0ADEAB5CBA0380DA, 0xE73685F552,0,'Deborah','Graystone','<EMAIL>',1,1) -- partner

--	  ,('412e8a1d-9c41-ec11-96f2-98af65a69f86', 'APP', 'dkeddy','B77C4B09-6CB4-4854-9EC8-3800475ADED4',0x999000A49E8DD91ED5726EA0AF1745C8F712640D6563F7FDE1977013B730CB3C9D78F99EC07FC59B0ADEAB5CBA0380DA, 0xE73685F552,0,'David','Keddy','<EMAIL>',1,1) -- Advisory MP
--	  ,('dcd74d83-9c41-ec11-96f2-98af65a69f87', 'APP', 'cgallo','0116ADC2-EFE3-4CB3-A07F-92D63C29B110',0x999000A49E8DD91ED5726EA0AF1745C8F712640D6563F7FDE1977013B730CB3C9D78F99EC07FC59B0ADEAB5CBA0380DA, 0xE73685F552,0,'Carrie','Gallo','<EMAIL>',1,1) -- partner
--	  ,('ddd74d83-9c41-ec11-96f2-98af65a69f88', 'APP', 'makelly','02901B1B-7153-4971-AF57-19CFE6F71438',0x999000A49E8DD91ED5726EA0AF1745C8F712640D6563F7FDE1977013B730CB3C9D78F99EC07FC59B0ADEAB5CBA0380DA, 0xE73685F552,0,'Marc','Kelly','<EMAIL>',1,1) -- partner
--	  ,('4456538a-9c41-ec11-96f2-98af65a69f89', 'APP', 'vsiciliano','03F84C28-272C-4013-A518-BE6D23E91ADA',0x999000A49E8DD91ED5726EA0AF1745C8F712640D6563F7FDE1977013B730CB3C9D78F99EC07FC59B0ADEAB5CBA0380DA, 0xE73685F552,0,'Vince','Siciliano','<EMAIL>',1,1) -- partner
--	  ,('c41284cc-3d79-ec11-9703-98af65a69f68', 'APP', 'JCSmith','42b0de01-8471-441b-ac1c-828e3f2dd1c8',0x999000A49E8DD91ED5726EA0AF1745C8F712640D6563F7FDE1977013B730CB3C9D78F99EC07FC59B0ADEAB5CBA0380DA, 0xE73685F552,0,'Jeffrey','Smith [RISK]','<EMAIL>',1,1) -- partner
--	  ,('4243a509-3e79-ec11-9703-98af65a69f68', 'APP', 'JWonfor','a8cc0bda-2b63-46cc-80e7-a8d22fb25d1b',0x999000A49E8DD91ED5726EA0AF1745C8F712640D6563F7FDE1977013B730CB3C9D78F99EC07FC59B0ADEAB5CBA0380DA, 0xE73685F552,0,'John','Wonfor','<EMAIL>',1,1) -- partner
--	  ,('e7f476bb-e87d-ec11-9706-98af65a69f68', 'APP', 'jstockton','28ee200f-9b5a-4b80-9f1b-30511eb638cd',0x999000A49E8DD91ED5726EA0AF1745C8F712640D6563F7FDE1977013B730CB3C9D78F99EC07FC59B0ADEAB5CBA0380DA, 0xE73685F552,0,'Janet','Stockton','<EMAIL>',1,1) -- partner
--	  ,('c73de215-e97d-ec11-9706-98af65a69f68', 'APP', 'NLakhani','4787ec8a-cf96-4ab5-9b05-003161d64f23',0x999000A49E8DD91ED5726EA0AF1745C8F712640D6563F7FDE1977013B730CB3C9D78F99EC07FC59B0ADEAB5CBA0380DA, 0xE73685F552,0,'Nazia','Lakhani','<EMAIL>',1,1) -- partner
--	  --,('412e8a1d-9c41-ec11-96f2-98af65a69f68', 'APP', 'pkramer','FFEC1449-DB9E-4FCE-B9E1-9E8DC636994B',0x999000A49E8DD91ED5726EA0AF1745C8F712640D6563F7FDE1977013B730CB3C9D78F99EC07FC59B0ADEAB5CBA0380DA, 0xE73685F552,0,'Patrick','Kramer','<EMAIL>',1,1) -- COO MP
--	  --,('412e8a1d-9c41-ec11-96f2-98af65a69f68', 'APP', 'dsimkins','61B03F52-704B-4F2C-A2D8-5473D3F6F094',0x999000A49E8DD91ED5726EA0AF1745C8F712640D6563F7FDE1977013B730CB3C9D78F99EC07FC59B0ADEAB5CBA0380DA, 0xE73685F552,0,'David','Simkins','<EMAIL>',1,1) -- HQ MP
  
--      ,('FE676148-33FF-4C2D-91DE-B7E2910114EB', 'APP', 'padmin','',0x999000A49E8DD91ED5726EA0AF1745C8F712640D6563F7FDE1977013B730CB3C9D78F99EC07FC59B0ADEAB5CBA0380DA, 0xE73685F552,0,'PRA','Admin','<EMAIL>',1,1) -- partner Admin

--	) AS Source ([Id], [AuthProviderId], [Username],[ObjectId],[Password],[Salt],[IsTempPasswordEnabled],[FirstName],[LastName],[Email],[LanguageId],[IsActive])
--	ON (Target.[Id] = Source.[Id] OR Target.[UserName] = Source.[UserName])
--	WHEN MATCHED AND (Target.[Username] <> Source.[Username] OR Target.[ObjectId] <> Source.[ObjectId] OR Target.[AuthProviderId]<> Source.[AuthProviderId] OR Target.[Password] <> Source.[Password] OR Target.[Salt] <> Source.[Salt] OR Target.[IsTempPasswordEnabled] <> Source.[IsTempPasswordEnabled] 
--	OR Target.[FirstName] <> Source.[FirstName] OR Target.[LastName] <> Source.[LastName] OR Target.[Email] <> Source.[Email] 
--	OR Target.[LanguageId] <> Source.[LanguageId] OR Target.[IsActive] <> Source.[IsActive] ) THEN
--	 UPDATE SET
--	 [Username] = Source.[Username], 
--	 [ObjectId] = Source.[ObjectId], 
--	[Password] = Source.[Password], 
--	[Salt] = Source.[Salt], 
--	[IsTempPasswordEnabled] = Source.[IsTempPasswordEnabled], 
--	[FirstName] = Source.[FirstName], 
--	[LastName] = Source.[LastName], 
--	[Email] = Source.[Email], 
--	[LanguageId] = Source.[LanguageId], 
--	[IsActive] = Source.[IsActive],
--	[AuthProviderId] = Source.[AuthProviderId]
--	WHEN NOT MATCHED BY TARGET THEN
--	 INSERT([Id],[AuthProviderId],[Username],[ObjectId],[Password],[Salt],[IsTempPasswordEnabled],[FirstName],[LastName],[Email],[LanguageId],[IsActive])
--	 VALUES(Source.[Id],Source.[AuthProviderId], Source.[Username], Source.[ObjectId],Source.[Password],Source.[Salt],Source.[IsTempPasswordEnabled],Source.[FirstName],Source.[LastName],Source.[Email],Source.[LanguageId],Source.[IsActive])
--	--WHEN NOT MATCHED BY SOURCE THEN 
--	-- DELETE;
--	;
--END
--GO

-- Insert test user roles (Commented out by David@2023-07-31)
--IF (CHARINDEX('prod', @@SERVERNAME) <= 0) AND (CHARINDEX('uat', @@SERVERNAME) <= 0)
--BEGIN
--	PRINT 'Insert Test User Roles'
--	 MERGE INTO [dbo].[UserRole] AS Target
--	USING (VALUES
--	   ('BFD02B5C-2408-4870-8E1F-06C7A72187F6', 1)
--	  ,('D3FB7F6A-6487-4190-9D9F-0ECD7A25E239', 2)
--	  ,('B6BDCA35-4D96-4C26-B6B2-D15E285147DB', 3)  
--	  ,('7f5307f8-af26-ec11-96ec-98af65a69f68', 4)  
--	  ,('7f5307f8-af26-ec11-96ec-98af65a69f69', 10) 
--	  ,('7f5307f8-af26-ec11-96ec-98af65a69f67', 9) 

--	  ,('0386a900-b026-ec11-96ec-98af65a69f67', 5)  
--	  ,('0386a900-b026-ec11-96ec-98af65a69f68', 5)  
--	  ,('0386a900-b026-ec11-96ec-98af65a69f69', 5) 
--	  ,('0386a900-b026-ec11-96ec-98af65a69f70', 5) 
  
--	  ,('0ca84117-b026-ec11-96ec-98af65a69f68', 6)  
--	  ,('483f5164-b026-ec11-96ec-98af65a69f68', 7)  
--	  ,('484f5164-b026-ec11-96ec-98af65a69f68', 8)  
--	  ,('412e8a1d-9c41-ec11-96f2-98af65a69f68', 6)  


--	  ,('412e8a1d-9c41-ec11-96f2-98af65a69f76', 3) 
--	  ,('ddd74d83-9c41-ec11-96f2-98af65a69f68', 3) 
--	  ,('4456538a-9c41-ec11-96f2-98af65a69f68', 3) 
--	  ,('4556538a-9c41-ec11-96f2-98af65a69f68', 3) 
--	  ,('4656538a-9c41-ec11-96f2-98af65a69f68', 3) 

--	  ,('412e8a1d-9c41-ec11-96f2-98af65a69f76', 6) 
--	  ,('dcd74d83-9c41-ec11-96f2-98af65a69f77', 3) 
--	  ,('ddd74d83-9c41-ec11-96f2-98af65a69f78', 3) 
--	  ,('4456538a-9c41-ec11-96f2-98af65a69f79', 3)
  
--	  ,('412e8a1d-9c41-ec11-96f2-98af65a69f86', 6) 
--	  ,('dcd74d83-9c41-ec11-96f2-98af65a69f87', 3) 
--	  ,('ddd74d83-9c41-ec11-96f2-98af65a69f88', 3) 
--	  ,('4456538a-9c41-ec11-96f2-98af65a69f89', 3) 

--	  ,('c41284cc-3d79-ec11-9703-98af65a69f68', 3) 
--	  ,('4243a509-3e79-ec11-9703-98af65a69f68', 3)
  
--	  ,('e7f476bb-e87d-ec11-9706-98af65a69f68', 3) 
--	  ,('c73de215-e97d-ec11-9706-98af65a69f68', 3)
--	  ,('FE676148-33FF-4C2D-91DE-B7E2910114EB',	11)
--	) AS Source ([UserId],[RoleId])
--	ON (Target.[RoleId] = Source.[RoleId] and Target.[UserId] = Source.[UserId])
--	--WHEN MATCHED AND (Target.[Name] <> Source.[Name]) THEN
--	--	UPDATE SET
--	--		[Name] = Source.[Name]		
--	WHEN NOT MATCHED BY TARGET AND (EXISTS (SELECT * FROM [User] WHERE [Id] = Source.[UserId])) THEN
--		INSERT([UserId], [RoleId]) VALUES(Source.[UserId], Source.[RoleId])
--	--WHEN NOT MATCHED BY SOURCE THEN 
--	--	DELETE;
--	;

--	-- Add employee role to user has no role
--	INSERT INTO UserRole (UserId, RoleId)
--	SELECT u.[Id], 13
--	  FROM [dbo].[User] u
--	  LEFT OUTER JOIN [UserRole] ur on ur.UserId = u.[Id]
--	  WHERE ur.[Id] is null AND u.AuthProviderId = 'APP'
--END
--GO

-- Test data only.

--insert into [dbo].[Notification] ([Message])
--values('Creating an inclusive environment where everyone can bring their genuine selves to work, participate fully and be positioned for success');

--insert into [dbo].[Notification] ([Message])
--values('Meet your Inclusion, Equity and Diversity Advisory Council');

--insert into [dbo].[Notification] ([Message])
--values('During the month of June, Indigenous History Month, we have seen tragic events unfold � the lives of 215 residential school children found in BC and 751 unmarked graves found in Saskatchewan');

--insert into [dbo].[Notification] ([Message])
--values('Include land acknowledgements in your Outlook email signature');

--insert into [dbo].[Notification] ([Message])
--values('Pride Season Event: We celebrated the kick-off to Pride Season on Thursday, June 17 with a special virtual Pride webcast. If you missed it, you can watch the webcast recording here');

--insert into [dbo].[Notification] ([Message])
--values('Change your BDO Outlook photo: Let�s add some colour to our conversations');

--insert into [dbo].[Notification] ([Message])
--values('Include your preferred pronoun in your Outlook email signature');

--insert into [dbo].[Notification] ([Message])
--values('BDO 100 Celebration');

--insert into [dbo].[Notification] ([Message])
--values('The New IE&D Advisory Council');

--insert into [dbo].[Notification] ([Message])
--values('Your Firm Engagement HUB');

--insert into [dbo].[Notification] ([Message])
--values('NEW AND IMPROVED MY BDO!');

--insert into [dbo].[Notification] ([Message])
--values('Congratulations - Chris Diepdael, CMC�BC Rising Star Award');

--insert into [dbo].[Notification] ([Message])
--values('[CAMPAIGN LAUNCH] Selling your business: now live!');

GO

-- Clean up test APP user for PRODUCTION
IF (CHARINDEX('prod', @@SERVERNAME) > 0)
BEGIN
	PRINT 'REMOVE APP (Debug Users)'
	DELETE [dbo].[UserRole] WHERE [UserId] IN (SELECT [ID] FROM [dbo].[User] WHERE [AuthProviderId] = 'APP')
	DELETE [dbo].[User] WHERE [AuthProviderId] = 'APP'
	UPDATE [identity].[AuthProvider] SET [IsActive] = 0 WHERE [Id] = 'App'
END