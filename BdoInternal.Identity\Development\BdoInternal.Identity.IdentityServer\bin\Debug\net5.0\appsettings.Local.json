{
  "Serilog": {
    "Using": [
      "Serilog.Sinks.Console",
      "Serilog.Sinks.ApplicationInsights"
    ],
    "MinimumLevel": {
      "Default": "Information"
    },
    "WriteTo": [
      //Note: ApplicationInsights setting has to stay as first section in "WriteTo".
      {
        "Name": "ApplicationInsights",
        "Args": {
          "restrictedToMinimumLevel": "Information",
          "instrumentationKey": "", //Note: always keep it as empty. The value will be replaced by APPINSIGHTS_INSTRUMENTATIONKEY
          "telemetryConverter": "Serilog.Sinks.ApplicationInsights.Sinks.ApplicationInsights.TelemetryConverters.TraceTelemetryConverter, Serilog.Sinks.ApplicationInsights"
        }
      },
      {
        "Name": "Console",
        "Args": {
          "theme": "Serilog.Sinks.SystemConsole.Themes.AnsiConsoleTheme::Code, Serilog.Sinks.Console"
        }
      },
      {
        "Name": "File",
        "Args": {
          "path": "logs/partner-identity-.log",
          "outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}",
          "rollingInterval": "Day",
          "shared": true
        }
      }
    ],
    "Enrich": [
      "FromLogContext",
      "WithMachineName",
      "WithThreadId"
    ]
  },
  //
  // Dev environment Azure Application Insights Instrumentation Key in "bdo-ca1-pid-ai-dev-01". Work for local development.
  // Note: When deploy app to Azure App Service, "APPINSIGHTS_INSTRUMENTATIONKEY" setting will be stored in Azure App Configuration 
  // and appsettings.QA.json, appsettings.UAT.json and appsettings.Production.json will not find "APPINSIGHTS_INSTRUMENTATIONKEY" setting in files.
  //
  "APPINSIGHTS_INSTRUMENTATIONKEY": "b5fb8f0e-a597-46e9-8e04-c60e6137d265",
  "AllowedHosts": "*",
  //
  // Set inside Azure App Services.
  //
  "ConnectionStrings": {
    //
    // Connection for sql server database. Local database.
    //
    //"DatabaseConnection": "Server=localhost;Database=BdoInternal.Identity.Database;Trusted_Connection=True;",
    "DatabaseConnection": "Data Source=bdo-ca1-partner-sql-dev-01.database.windows.net;Initial Catalog=Identity-DEV;Authentication=Active Directory Integrated;"
  },
  "IdentityServer": {
    //
    // Current Identity Server end point's domain.
    // Note: This is custom setting.
    //
    "IAMUri": "https://localhost:5000",
    //
    // Integer value of Minutes. Default value = 5 minutes. Note: This is important to solve problem in client's infinit looping in login which caused by client's local machine datetime setting is not correct.
    // Set in Web API resource portal.
    //
    "IdentityServerJwtValidationClockSkew": 5,
    //
    //Reference to enumeration defintion: IdentitySigningKeyProvider
    //
    "IdentitySigningKeyPrvider": 2, // 2 is SQL Server database keeps Signing Key.
    //
    // Note: Follow clients represent multiple hosting approaches for Angular and React single page applications.
    // For example, the single page app is able to host in seperated domain or in same domain with Web API portal.
    // Further, spa build is able to be stored in local static file system or Azure Blob Storage service.
    //
    "Clients": [
      //
      //ID4 PKCE Client setup. Reference: https://www.scottbrady91.com/Angular/Migrating-oidc-client-js-to-use-the-OpenID-Connect-Authorization-Code-Flow-and-PKCE
      // It is Angular SPA client.
      //
      {
        "ClientId": "angular_dev_cors_root", // Start home page from http://localhost:4200. debug local with vs code command: ng serve
        "ClientName": "Angular Application",
        "ClientUri": "http://localhost:4200", //It is client portal's domain. Note: current setting is for local machine debugging. 
        "RequireClientSecret": false, // for javascript app, set to false.
        //
        // Reference: http://docs.identityserver.io/en/release/topics/grant_types.html
        // Reference: https://referbruv.com/blog/posts/implementing-authorization-code-grant-using-identityserver4-with-pkce
        // Note: For PKCE, C# code GrantTypes.Code = "authorization_code" when setup clients with json file here 
        // when setup same setting through appsettings.json.
        //
        "AllowedGrantTypes": [ "authorization_code" ],
        "RequirePkce": true,
        "AllowRememberConsent": true,
        "AllowAccessTokensViaBrowser": true,
        "AlwaysIncludeUserClaimsInIdToken": false,
        "AccessTokenType": "jwt", // jwt is defualt token type of Identity Server.
        "RedirectUris": [ "http://localhost:4200/auth-callback", "http://localhost:4200/silent-refresh.html" ],
        "PostLogoutRedirectUris": [ "http://localhost:4200/" ], //Note: Don't forget to put "/" after domain.
        "AllowedCorsOrigins": [ "http://localhost:4200" ],

        "AllowedScopes": [ "openid", "profile", "resource-api", "admin-api", "custom_profile" ],
        // Settings: https://docs.identityserver.io/en/latest/reference/client.html
        "RequireConsent": false,
        //
        // Important. EnableLocalLogin = false, identity server will skip the login page and directly jump to external identity provider (Azure AD here) login page.
        // Set value = true, it uses local database user table for authentication.
        //
        "EnableLocalLogin": true
      },
      {
        "ClientId": "angular_dev_cors", // Start home page from http://localhost:4200/angular. debug local with vs code command: ng serve --deploy-url="/angular/"  
        "ClientName": "Angular Application",
        "ClientUri": "http://localhost:4200/angular", //It is client portal's domain. Note: current setting is for local machine debugging. 
        "RequireClientSecret": false, // for javascript app, set to false.
        //
        // Reference: http://docs.identityserver.io/en/release/topics/grant_types.html
        // Reference: https://referbruv.com/blog/posts/implementing-authorization-code-grant-using-identityserver4-with-pkce
        // Note: For PKCE, C# code GrantTypes.Code = "authorization_code" when setup clients with json file here 
        // when setup same setting through appsettings.json.
        //
        "AllowedGrantTypes": [ "authorization_code" ],
        "RequirePkce": true,
        "AllowRememberConsent": true,
        "AllowAccessTokensViaBrowser": true,
        "AlwaysIncludeUserClaimsInIdToken": false,
        "AccessTokenType": "jwt", // jwt is defualt token type of Identity Server.
        "RedirectUris": [ "http://localhost:4200/angular/auth-callback", "http://localhost:4200/angular/silent-refresh.html" ],
        "PostLogoutRedirectUris": [ "http://localhost:4200/angular/" ], //Note: Don't forget to put "/" after domain.
        "AllowedCorsOrigins": [ "http://localhost:4200" ],

        "AllowedScopes": [ "openid", "profile", "resource-api", "admin-api", "custom_profile" ],
        // Settings: https://docs.identityserver.io/en/latest/reference/client.html
        "RequireConsent": false,
        //
        // Important. EnableLocalLogin = false, identity server will skip the login page and directly jump to external identity provider (Azure AD here) login page.
        // Set value = true, it uses local database user table for authentication.
        //
        "EnableLocalLogin": true
      },
      {
        "ClientId": "angular_dev_nocors", // Start home page from https://localhost:5001/angular, angular build saved in Azure Blob Storage. It cannot be debugged local with vs code.
        "ClientName": "Angular Application",
        "ClientUri": "https://localhost:5001/angular", //It is client portal's domain. Note: current setting is for local machine debugging. 
        "RequireClientSecret": false, // for javascript app, set to false.
        //
        // Reference: http://docs.identityserver.io/en/release/topics/grant_types.html
        // Reference: https://referbruv.com/blog/posts/implementing-authorization-code-grant-using-identityserver4-with-pkce
        // Note: For PKCE, C# code GrantTypes.Code = "authorization_code" when setup clients with json file here 
        // when setup same setting through appsettings.json.
        //
        "AllowedGrantTypes": [ "authorization_code" ],
        "RequirePkce": true,
        "AllowRememberConsent": true,
        "AllowAccessTokensViaBrowser": true,
        "AlwaysIncludeUserClaimsInIdToken": false,
        "AccessTokenType": "jwt", // jwt is defualt token type of Identity Server.
        "RedirectUris": [ "https://localhost:5001/angular/auth-callback", "https://localhost:5001/angular/silent-refresh.html" ],
        "PostLogoutRedirectUris": [ "https://localhost:5001/angular/" ], //Note: Don't forget to put "/" after domain.
        "AllowedCorsOrigins": [ "https://localhost:5001" ],

        "AllowedScopes": [ "openid", "profile", "resource-api", "admin-api", "custom_profile" ],
        // Settings: https://docs.identityserver.io/en/latest/reference/client.html
        "RequireConsent": false,
        //
        // Important. EnableLocalLogin = false, identity server will skip the login page and directly jump to external identity provider (Azure AD here) login page.
        // Set value = true, it uses local database user table for authentication.
        //
        "EnableLocalLogin": true
      },

      //
      // Following is React clients
      //
      //ID4 PKCE Client setup. Reference: https://www.scottbrady91.com/Angular/Migrating-oidc-client-js-to-use-the-OpenID-Connect-Authorization-Code-Flow-and-PKCE
      // It is React SPA client.
      //
      {
        "ClientId": "report_dev_cors", // local debug React project with specified sub path. Start home page from http://localhost:3000/react
        "ClientName": "Partner Report",
        "ClientUri": "http://localhost:3000/prpt", //It is client portal's domain. Note: current setting is for local machine debugging. 
        "RequireClientSecret": false, // for javascript app, set to false.
        //
        // Reference: http://docs.identityserver.io/en/release/topics/grant_types.html
        // Reference: https://referbruv.com/blog/posts/implementing-authorization-code-grant-using-identityserver4-with-pkce
        // Note: For PKCE, C# code GrantTypes.Code = "authorization_code" when setup clients with json file here 
        // when setup same setting through appsettings.json.
        //
        "AllowedGrantTypes": [ "authorization_code" ],
        "RequirePkce": true,
        "AllowRememberConsent": true,
        "AllowAccessTokensViaBrowser": true,
        "AlwaysIncludeUserClaimsInIdToken": false,
        "AccessTokenType": "jwt", // jwt is defualt token type of Identity Server.
        "RedirectUris": [ "http://localhost:3000/prpt/auth-callback", "http://localhost:3000/prpt/silent-auth-callback" ],
        "PostLogoutRedirectUris": [ "http://localhost:3000/prpt/" ], //Note: Don't forget to put "/" after domain.
        "AllowedCorsOrigins": [ "http://localhost:3000" ],

        "AllowedScopes": [ "openid", "profile", "resource-api", "admin-api", "custom_profile" ],
        // Settings: https://docs.identityserver.io/en/latest/reference/client.html
        "RequireConsent": false,
        //
        // Important. EnableLocalLogin = false, identity server will skip the login page and directly jump to external identity provider (Azure AD here) login page.
        // Set value = true, it uses local database user table for authentication.
        //
        "EnableLocalLogin": true
      },
      {
        "ClientId": "apc_dev_cors", // local debug React project with specified sub path. Start home page from http://localhost:3000/react
        "ClientName": "Annual Partner Confirmation",
        "ClientUri": "http://localhost:3000/apc", //It is client portal's domain. Note: current setting is for local machine debugging. 
        "RequireClientSecret": false, // for javascript app, set to false.
        //
        // Reference: http://docs.identityserver.io/en/release/topics/grant_types.html
        // Reference: https://referbruv.com/blog/posts/implementing-authorization-code-grant-using-identityserver4-with-pkce
        // Note: For PKCE, C# code GrantTypes.Code = "authorization_code" when setup clients with json file here 
        // when setup same setting through appsettings.json.
        //
        "AllowedGrantTypes": [ "authorization_code" ],
        "RequirePkce": true,
        "AllowRememberConsent": true,
        "AllowAccessTokensViaBrowser": true,
        "AlwaysIncludeUserClaimsInIdToken": false,
        "AccessTokenType": "jwt", // jwt is defualt token type of Identity Server.
        "RedirectUris": [ "http://localhost:3000/apc/auth-callback", "http://localhost:3000/apc/silent-auth-callback" ],
        "PostLogoutRedirectUris": [ "http://localhost:3000/apc/" ], //Note: Don't forget to put "/" after domain.
        "AllowedCorsOrigins": [ "http://localhost:3000" ],

        "AllowedScopes": [ "openid", "profile", "resource-api", "admin-api", "custom_profile" ],
        // Settings: https://docs.identityserver.io/en/latest/reference/client.html
        "RequireConsent": false,
        //
        // Important. EnableLocalLogin = false, identity server will skip the login page and directly jump to external identity provider (Azure AD here) login page.
        // Set value = true, it uses local database user table for authentication.
        //
        "EnableLocalLogin": true
      },
      {
        "ClientId": "pr_dev_cors", // local debug React project with specified sub path. Start home page from http://localhost:3000/react
        "ClientName": "Partner Rotation",
        "ClientUri": "http://localhost:3000/pr", //It is client portal's domain. Note: current setting is for local machine debugging. 
        "RequireClientSecret": false, // for javascript app, set to false.
        //
        // Reference: http://docs.identityserver.io/en/release/topics/grant_types.html
        // Reference: https://referbruv.com/blog/posts/implementing-authorization-code-grant-using-identityserver4-with-pkce
        // Note: For PKCE, C# code GrantTypes.Code = "authorization_code" when setup clients with json file here 
        // when setup same setting through appsettings.json.
        //
        "AllowedGrantTypes": [ "authorization_code" ],
        "RequirePkce": true,
        "AllowRememberConsent": true,
        "AllowAccessTokensViaBrowser": true,
        "AlwaysIncludeUserClaimsInIdToken": false,
        "AccessTokenType": "jwt", // jwt is defualt token type of Identity Server.
        "RedirectUris": [ "http://localhost:3000/pr/auth-callback", "http://localhost:3000/pr/silent-auth-callback" ],
        "PostLogoutRedirectUris": [ "http://localhost:3000/pr/" ], //Note: Don't forget to put "/" after domain.
        "AllowedCorsOrigins": [ "http://localhost:3000" ],

        "AllowedScopes": [ "openid", "profile", "resource-api", "admin-api", "custom_profile" ],
        // Settings: https://docs.identityserver.io/en/latest/reference/client.html
        "RequireConsent": false,
        //
        // Important. EnableLocalLogin = false, identity server will skip the login page and directly jump to external identity provider (Azure AD here) login page.
        // Set value = true, it uses local database user table for authentication.
        //
        "EnableLocalLogin": true
      },
      {
        "ClientId": "pra_dev_cors", // local debug React project with specified sub path. Start home page from http://localhost:3000/react
        "ClientName": "Partner Retirement",
        "ClientUri": "http://localhost:3000/pra", //It is client portal's domain. Note: current setting is for local machine debugging. 
        "RequireClientSecret": false, // for javascript app, set to false.
        //
        // Reference: http://docs.identityserver.io/en/release/topics/grant_types.html
        // Reference: https://referbruv.com/blog/posts/implementing-authorization-code-grant-using-identityserver4-with-pkce
        // Note: For PKCE, C# code GrantTypes.Code = "authorization_code" when setup clients with json file here 
        // when setup same setting through appsettings.json.
        //
        "AllowedGrantTypes": [ "authorization_code" ],
        "RequirePkce": true,
        "AllowRememberConsent": true,
        "AllowAccessTokensViaBrowser": true,
        "AlwaysIncludeUserClaimsInIdToken": false,
        "AccessTokenType": "jwt", // jwt is defualt token type of Identity Server.
        "RedirectUris": [ "http://localhost:3000/pra/auth-callback", "http://localhost:3000/pra/silent-auth-callback" ],
        "PostLogoutRedirectUris": [ "http://localhost:3000/pra/" ], //Note: Don't forget to put "/" after domain.
        "AllowedCorsOrigins": [ "http://localhost:3000" ],

        "AllowedScopes": [ "openid", "profile", "resource-api", "admin-api", "custom_profile" ],
        // Settings: https://docs.identityserver.io/en/latest/reference/client.html
        "RequireConsent": false,
        //
        // Important. EnableLocalLogin = false, identity server will skip the login page and directly jump to external identity provider (Azure AD here) login page.
        // Set value = true, it uses local database user table for authentication.
        //
        "EnableLocalLogin": true
      },
      {
        "ClientId": "report_dev_cors_root", // local debug React project without sub path. start home page from http://localhost:3000
        "ClientName": "React Application",
        "ClientUri": "http://localhost:3000", //It is client portal's domain. Note: current setting is for local machine debugging. 
        "RequireClientSecret": false, // for javascript app, set to false.
        //
        // Reference: http://docs.identityserver.io/en/release/topics/grant_types.html
        // Reference: https://referbruv.com/blog/posts/implementing-authorization-code-grant-using-identityserver4-with-pkce
        // Note: For PKCE, C# code GrantTypes.Code = "authorization_code" when setup clients with json file here 
        // when setup same setting through appsettings.json.
        //
        "AllowedGrantTypes": [ "authorization_code" ],
        "RequirePkce": true,
        "AllowRememberConsent": true,
        "AllowAccessTokensViaBrowser": true,
        "AlwaysIncludeUserClaimsInIdToken": false,
        "AccessTokenType": "jwt", // jwt is defualt token type of Identity Server.
        "RedirectUris": [ "http://localhost:3000/auth-callback", "http://localhost:3000/silent-auth-callback" ],
        "PostLogoutRedirectUris": [ "http://localhost:3000/" ], //Note: Don't forget to put "/" after domain.
        "AllowedCorsOrigins": [ "http://localhost:3000" ],

        "AllowedScopes": [ "openid", "profile", "resource-api", "admin-api", "custom_profile" ],
        // Settings: https://docs.identityserver.io/en/latest/reference/client.html
        "RequireConsent": false,
        //
        // Important. EnableLocalLogin = false, identity server will skip the login page and directly jump to external identity provider (Azure AD here) login page.
        // Set value = true, it uses local database user table for authentication.
        //
        "EnableLocalLogin": true
      },
      {
        "ClientId": "report_dev_nocors", // Submit react build in Azure Blob Storage. React and Web API portal in same domain. Start home page in https://localhost:5001/react
        "ClientName": "React Application",
        "ClientUri": "https://localhost:5001/prpt", //It is client portal's domain. Note: current setting is for local machine debugging. 
        "RequireClientSecret": false, // for javascript app, set to false.
        //
        // Reference: http://docs.identityserver.io/en/release/topics/grant_types.html
        // Reference: https://referbruv.com/blog/posts/implementing-authorization-code-grant-using-identityserver4-with-pkce
        // Note: For PKCE, C# code GrantTypes.Code = "authorization_code" when setup clients with json file here 
        // when setup same setting through appsettings.json.
        //
        "AllowedGrantTypes": [ "authorization_code" ],
        "RequirePkce": true,
        "AllowRememberConsent": true,
        "AllowAccessTokensViaBrowser": true,
        "AlwaysIncludeUserClaimsInIdToken": false,
        "AccessTokenType": "jwt", // jwt is defualt token type of Identity Server.
        "RedirectUris": [ "https://localhost:5001/prpt/auth-callback", "https://localhost:5001/prpt/silent-auth-callback" ],
        "PostLogoutRedirectUris": [ "https://localhost:5001/prpt/" ], //Note: Don't forget to put "/" after domain.
        "AllowedCorsOrigins": [ "https://localhost:5001" ],

        "AllowedScopes": [ "openid", "profile", "resource-api", "admin-api", "custom_profile" ],
        // Settings: https://docs.identityserver.io/en/latest/reference/client.html
        "RequireConsent": false,
        //
        // Important. EnableLocalLogin = false, identity server will skip the login page and directly jump to external identity provider (Azure AD here) login page.
        // Set value = true, it uses local database user table for authentication.
        //
        "EnableLocalLogin": true
      },
      {
        "ClientId": "apc_dev_nocors", // Submit react build in Azure Blob Storage. React and Web API portal in same domain. Start home page in https://localhost:5001/react
        "ClientName": "React Application",
        "ClientUri": "https://localhost:5001/apc", //It is client portal's domain. Note: current setting is for local machine debugging. 
        "RequireClientSecret": false, // for javascript app, set to false.
        //
        // Reference: http://docs.identityserver.io/en/release/topics/grant_types.html
        // Reference: https://referbruv.com/blog/posts/implementing-authorization-code-grant-using-identityserver4-with-pkce
        // Note: For PKCE, C# code GrantTypes.Code = "authorization_code" when setup clients with json file here 
        // when setup same setting through appsettings.json.
        //
        "AllowedGrantTypes": [ "authorization_code" ],
        "RequirePkce": true,
        "AllowRememberConsent": true,
        "AllowAccessTokensViaBrowser": true,
        "AlwaysIncludeUserClaimsInIdToken": false,
        "AccessTokenType": "jwt", // jwt is defualt token type of Identity Server.
        "RedirectUris": [ "https://localhost:5001/apc/auth-callback", "https://localhost:5001/apc/silent-auth-callback" ],
        "PostLogoutRedirectUris": [ "https://localhost:5001/apc/" ], //Note: Don't forget to put "/" after domain.
        "AllowedCorsOrigins": [ "https://localhost:5001" ],

        "AllowedScopes": [ "openid", "profile", "resource-api", "admin-api", "custom_profile" ],
        // Settings: https://docs.identityserver.io/en/latest/reference/client.html
        "RequireConsent": false,
        //
        // Important. EnableLocalLogin = false, identity server will skip the login page and directly jump to external identity provider (Azure AD here) login page.
        // Set value = true, it uses local database user table for authentication.
        //
        "EnableLocalLogin": true
      },
      {
        "ClientId": "pps_dev_cors", // local debug React Partner Plans React front end project with seperated domain. Start home page from http://localhost:3000/pps
        "ClientName": "Partner Plans",
        "ClientUri": "http://localhost:3000/pps", //It is client portal's domain. Note: current setting is for local machine debugging. 
        "RequireClientSecret": false, // for javascript app, set to false.
        //
        // Reference: http://docs.identityserver.io/en/release/topics/grant_types.html
        // Reference: https://referbruv.com/blog/posts/implementing-authorization-code-grant-using-identityserver4-with-pkce
        // Note: For PKCE, C# code GrantTypes.Code = "authorization_code" when setup clients with json file here 
        // when setup same setting through appsettings.json.
        //
        "AllowedGrantTypes": [ "authorization_code" ],
        "RequirePkce": true,
        "AllowRememberConsent": true,
        "AllowAccessTokensViaBrowser": true,
        "AlwaysIncludeUserClaimsInIdToken": false,
        "AccessTokenType": "jwt", // jwt is defualt token type of Identity Server.
        "RedirectUris": [ "http://localhost:3000/pps/auth-callback", "http://localhost:3000/pps/silent-auth-callback" ],
        "PostLogoutRedirectUris": [ "http://localhost:3000/pps/" ], //Note: Don't forget to put "/" after domain.
        "AllowedCorsOrigins": [ "http://localhost:3000" ],

        "AllowedScopes": [ "openid", "profile", "resource-api", "admin-api", "custom_profile" ],
        // Settings: https://docs.identityserver.io/en/latest/reference/client.html
        "RequireConsent": false,
        //
        // Important. EnableLocalLogin = false, identity server will skip the login page and directly jump to external identity provider (Azure AD here) login page.
        // Set value = true, it uses local database user table for authentication.
        //
        "EnableLocalLogin": true
      },
      {
        "ClientId": "pps_dev_nocors", // local debug Partner Plans React front end project with same doman as resource api, and in specified sub path. Start home page from http://localhost:5001/pps
        "ClientName": "Partner Plans",
        "ClientUri": "https://localhost:5001/pps", //It is client portal's domain. Note: current setting is for local machine debugging. 
        "RequireClientSecret": false, // for javascript app, set to false.
        //
        // Reference: http://docs.identityserver.io/en/release/topics/grant_types.html
        // Reference: https://referbruv.com/blog/posts/implementing-authorization-code-grant-using-identityserver4-with-pkce
        // Note: For PKCE, C# code GrantTypes.Code = "authorization_code" when setup clients with json file here 
        // when setup same setting through appsettings.json.
        //
        "AllowedGrantTypes": [ "authorization_code" ],
        "RequirePkce": true,
        "AllowRememberConsent": true,
        "AllowAccessTokensViaBrowser": true,
        "AlwaysIncludeUserClaimsInIdToken": false,
        "AccessTokenType": "jwt", // jwt is defualt token type of Identity Server.
        "RedirectUris": [ "https://localhost:5001/pps/auth-callback", "https://localhost:5001/pps/silent-auth-callback" ],
        "PostLogoutRedirectUris": [ "https://localhost:5001/pps/" ], //Note: Don't forget to put "/" after domain.
        "AllowedCorsOrigins": [ "https://localhost:5001" ],

        "AllowedScopes": [ "openid", "profile", "resource-api", "admin-api", "custom_profile" ],
        // Settings: https://docs.identityserver.io/en/latest/reference/client.html
        "RequireConsent": false,
        //
        // Important. EnableLocalLogin = false, identity server will skip the login page and directly jump to external identity provider (Azure AD here) login page.
        // Set value = true, it uses local database user table for authentication.
        //
        "EnableLocalLogin": true
      }

    ],
    "ExternalIdentityProviders": [
        {
            "ProviderName": "BDO-AAD",
            "Description": "Sign-in with BDO Canada LLP Azure AD",
            "AzureADAuthority": "https://login.microsoftonline.com/bdocanada.onmicrosoft.com",
            "AzureADClientId": "506b6200-b004-42ea-8cec-3699c2446ff7",
            "AzureADTenant": "ee7a07de-4778-4cfe-8e91-8d9038ba72ec",
            "AzureADClientSecret": "****************************************", //"****************************************",
            "AzureADGraphVersion": "api-version=1.6",
            "CallbackPath": "/signin-oidc-bdo-llp-aad",
            "SignedOutCallbackPath": "/signout-callback-oidc",
            "RemoteSignOutPath": "/signout-oidc-bdo-llp-aad"
        }
    ]
    //"ExternalIdentityProviders": [
    //  {
    //    //
    //    // Auzre AD authentication setup. Note: We are introduced Azure AD as an external identity provider.
    //    // The current Identity Server 4 here works as a federal gateway.
    //    //
    //    // BDO Innovation Azure AD setup. Point to Azure AD Registered App called "BDO Single Sign On Application POC".
    //    "ProviderName": "BDO-ITINV-AAD",
    //    "Description": "Identity Server Local Development",
    //    "AzureADAuthority": "https://login.microsoftonline.com/bdoinnovation.onmicrosoft.com",
    //    "AzureADClientId": "d528b107-89d4-4fcc-a809-4e692eedc210",
    //    "AzureADTenant": "474b91dc-c25e-4ff0-9ef3-299fff8f5af4", //"bdoinnovation.onmicrosoft.com",
    //    //
    //    // Azure AD registered application's preset client secret.
    //    // Check this value under "Certificates & secrets" section under "First On Site Identity Server" application in Azure AD.
    //    //
    //    "AzureADClientSecret": "*************************************",
    //    // Work for call Azure Graph APIs.
    //    "AzureADGraphVersion": "api-version=1.6",
    //    "CallbackPath": "/signin-oidc-bdo-itinv-aad",
    //    "SignedOutCallbackPath": "/signout-callback-oidc",
    //    "RemoteSignOutPath": "/signout-oidc-bdo-itinv-aad"
    //  }
    //]
  },
  "App": {
    //
    // Corporate with Azure App Configuration service to refresh all settings. 
    //
    "Sentinel": "1",
    //
    // local database connection timeout setting.
    // It is integer value of seconds.
    //
    "DatabaseCommandTimeout": 3600,

    //
    // Azure Key Vaults access matters config settings section.
    // Corporate with Azure SQL Server always encrypted data access.
    //
    //
    "AzureKeyVaultConfig": {
      //
      // Get TenantId from Azure AD registed Application (App registration) Dashboard 
      // item called "Directory (tenant) ID". 
      // For example, the TenantId of "LocalDebugSolutionTemplateWebAPI" in "BDO IT Solutions Innovation" Azure AD.
      // Work for create token credential for Azure Key Vault access.
      //
      "TenantId": "ee7a07de-4778-4cfe-8e91-8d9038ba72ec",
      //
      // It is Azure Key Vault service uri. Get it from Azure Key Vault dashboard.
      //
      "AzureKeyVaultUri": "https://solutiontemplatekeyvault.vault.azure.net/"
    }
  }

  //
  // If "AppConfig" section is enabled, it means current application's config settings should be got from Azure App Configuration service.
  // If no "AppConfig" section or the "AppConfig" section got comment out, it means current application gets settings from appsettings.json. Note: It mostly is for Development enviornment.
  //
  // When "AppConfig" section is enabled, 
  // 
  // If "isConnectedWithConnectionString" = true, system will try to access Azure App Configuration service with "connection string" way. 
  // (Got "connection string" from App Configuraiton-> "Access Keys" section.) 
  // It is mostly working for local development environment to get settings from Azure App Configuraiton service, instead of getting settings from appsettings.json files.
  //
  // If "isConnectedWithConnectionString" = false, system will try to access Azure App Configuration service with "ManagedIdentityCredential".
  // Note: This option is only works for web portal which already deployed in Azure App Services. It does not work for local development environment.
  //
  // Note: If same setting existing in appsettings.json and Azure App Configuration, 
  // The settings in Azure App Configuration got precedence.
  //
  // Note: There is scenario that same setting existing in appsettings.[current environment].json and "AppConfig" section also is enabled (which means Azure App Configuration access enabled). 
  // If same setting existing in appsettings.[current environment].json and Azure App Configuration, 
  // The settings in Azure App Configuration got precedence.
  //
  //"AppConfig": {
  //  "IsConnectedWithConnectionString": true,

  //   It is Azure App Configuration Service endpoint. Note: It only works when "IsConnectedWithConnectionString" = false. and it corporate with "ManagedIdentityCredential" call.
  //   It only works for portal deployed in Azure App Services.

  //  "Endpoint": "https://solution-template-appsettings.azconfig.io"
  //}
}
