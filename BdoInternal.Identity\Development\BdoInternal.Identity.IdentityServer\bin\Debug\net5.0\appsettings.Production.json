{
  "Serilog": {
    "Using": [
      "Serilog.Sinks.Console",
      "Serilog.Sinks.ApplicationInsights"
    ],
    "MinimumLevel": {
      "Default": "Information"
    },
    "WriteTo": [
      //Note: ApplicationInsights setting has to stay as first section in "WriteTo".
      {
        "Name": "ApplicationInsights",
        "Args": {
          "restrictedToMinimumLevel": "Information",
          "instrumentationKey": "", //Note: always keep it as empty. The value will be replaced by APPINSIGHTS_INSTRUMENTATIONKEY
          "telemetryConverter": "Serilog.Sinks.ApplicationInsights.Sinks.ApplicationInsights.TelemetryConverters.TraceTelemetryConverter, Serilog.Sinks.ApplicationInsights"
        }
      },
      {
        "Name": "Console",
        "Args": {
          "theme": "Serilog.Sinks.SystemConsole.Themes.AnsiConsoleTheme::Code, Serilog.Sinks.Console"
        }
      }      
    ],
    "Enrich": [
      "FromLogContext",
      "WithMachineName",
      "WithThreadId"
    ]
  },
  "AllowedHosts": "*",
  "ConnectionStrings": {
    "DatabaseConnection": "data source= bdo-ca1-partner-sql-prod-01.database.windows.net; initial catalog =Identity; Column Encryption Setting=enabled;pooling=true;multipleactiveresultsets=False;connect timeout=120;Authentication=Active Directory Managed Identity"
  },
  "IdentityServer": {
    "IAMUri": "https://partner-auth.app.bdo.ca",
    "IdentityServerJwtValidationClockSkew": 5,
    "IdentitySigningKeyPrvider": 2,
    "Clients": [
      {
        "ClientId": "apc_prod_nocors",
        "ClientName": "Annual Partner Confirmation",
        "ClientUri": "https://apc.app.bdo.ca/apc",
        "RequireClientSecret": false,
        "AllowedGrantTypes": [ "authorization_code" ],
        "RequirePkce": true,
        "AllowRememberConsent": true,
        "AllowAccessTokensViaBrowser": true,
        "AlwaysIncludeUserClaimsInIdToken": false,
        "AccessTokenType": "jwt",
        "RedirectUris": [ "https://apc.app.bdo.ca/apc/auth-callback", "https://apc.app.bdo.ca/apc/silent-auth-callback" ],
        "PostLogoutRedirectUris": [ "https://apc.app.bdo.ca/apc/" ],
        "AllowedCorsOrigins": [ "https://apc.app.bdo.ca" ],

        "AllowedScopes": [ "openid", "profile", "resource-api", "admin-api", "custom_profile" ],
        "RequireConsent": false,
        "EnableLocalLogin": false
      },
      {
        "ClientId": "report_prod_nocors",
        "ClientName": "Partner Report",
        "ClientUri": "https://partnerreports.app.bdo.ca/prpt",
        "RequireClientSecret": false,
        "AllowedGrantTypes": [ "authorization_code" ],
        "RequirePkce": true,
        "AllowRememberConsent": true,
        "AllowAccessTokensViaBrowser": true,
        "AlwaysIncludeUserClaimsInIdToken": false,
        "AccessTokenType": "jwt",
        "RedirectUris": [ "https://partnerreports.app.bdo.ca/prpt/auth-callback", "https://partnerreports.app.bdo.ca/prpt/silent-auth-callback" ],
        "PostLogoutRedirectUris": [ "https://partnerreports.app.bdo.ca/prpt/" ],
        "AllowedCorsOrigins": [ "https://partnerreports.app.bdo.ca" ],

        "AllowedScopes": [ "openid", "profile", "resource-api", "admin-api", "custom_profile" ],
        "RequireConsent": false,
        "EnableLocalLogin": false
      },
      {
        "ClientId": "pr_prod_nocors",
        "ClientName": "Partner Rotation",
        "ClientUri": "https://pr.app.bdo.ca/pr",
        "RequireClientSecret": false,
        "AllowedGrantTypes": [ "authorization_code" ],
        "RequirePkce": true,
        "AllowRememberConsent": true,
        "AllowAccessTokensViaBrowser": true,
        "AlwaysIncludeUserClaimsInIdToken": false,
        "AccessTokenType": "jwt",
        "RedirectUris": [ "https://pr.app.bdo.ca/pr/auth-callback", "https://pr.app.bdo.ca/pr/silent-auth-callback" ],
        "PostLogoutRedirectUris": [ "https://pr.app.bdo.ca/pr/" ],
        "AllowedCorsOrigins": [ "https://pr.app.bdo.ca" ],

        "AllowedScopes": [ "openid", "profile", "resource-api", "admin-api", "custom_profile" ],
        "RequireConsent": false,
        "EnableLocalLogin": false
      },
      {
        "ClientId": "pra_prod_nocors",
        "ClientName": "Partner Retirement",
        "ClientUri": "https://pra.app.bdo.ca/pra",
        "RequireClientSecret": false,
        "AllowedGrantTypes": [ "authorization_code" ],
        "RequirePkce": true,
        "AllowRememberConsent": true,
        "AllowAccessTokensViaBrowser": true,
        "AlwaysIncludeUserClaimsInIdToken": false,
        "AccessTokenType": "jwt",
        "RedirectUris": [ "https://pra.app.bdo.ca/pra/auth-callback", "https://pra.app.bdo.ca/pra/silent-auth-callback" ],
        "PostLogoutRedirectUris": [ "https://pra.app.bdo.ca/pra/" ],
        "AllowedCorsOrigins": [ "https://pra.app.bdo.ca" ],
        "AllowedScopes": [ "openid", "profile", "resource-api", "admin-api", "custom_profile" ],
        "RequireConsent": false,
        "EnableLocalLogin": false
      },
      {
        "ClientId": "pps_prod_nocors",
        "ClientName": "Partner Plans",
        "ClientUri": "https://pps.app.bdo.ca/pps",
        "RequireClientSecret": false,
        "AllowedGrantTypes": [ "authorization_code" ],
        "RequirePkce": true,
        "AllowRememberConsent": true,
        "AllowAccessTokensViaBrowser": true,
        "AlwaysIncludeUserClaimsInIdToken": false,
        "AccessTokenType": "jwt",
        "RedirectUris": [ "https://pps.app.bdo.ca/pps/auth-callback", "https://pps.app.bdo.ca/pps/silent-auth-callback" ],
        "PostLogoutRedirectUris": [ "https://pps.app.bdo.ca/pps/" ],
        "AllowedCorsOrigins": [ "https://pps.app.bdo.ca" ],
        "AllowedScopes": [ "openid", "profile", "resource-api", "admin-api", "custom_profile" ],
        "RequireConsent": false,
        "EnableLocalLogin": false
      }
    ],
    "ExternalIdentityProviders": [
      {
        "ProviderName": "BDO-AAD",
        "Description": "Sign-in with BDO Canada LLP Azure AD",
        "AzureADAuthority": "https://login.microsoftonline.com/bdocanada.onmicrosoft.com",
        //"AzureADClientId": "506b6200-b004-42ea-8cec-3699c2446ff7",
        "AzureADTenant": "ee7a07de-4778-4cfe-8e91-8d9038ba72ec",
        //"AzureADClientSecret": "*************************************",
        "AzureADGraphVersion": "api-version=1.6",
        "CallbackPath": "/signin-oidc-bdo-llp-aad",
        "SignedOutCallbackPath": "/signout-callback-oidc",
        "RemoteSignOutPath": "/signout-oidc-bdo-llp-aad"
      }
    ]
  },
  "App": {
    "Sentinel": "1",
    "DatabaseCommandTimeout": 3600,
    "AzureKeyVaultConfig": {
      "TenantId": "ee7a07de-4778-4cfe-8e91-8d9038ba72ec",
      "AzureKeyVaultUri": "https://bdo-ca1-partner-kv-prod.vault.azure.net/"
    }
  }
}