{
  "Serilog": {
    "Using": [
      "Serilog.Sinks.Console",
      "Serilog.Sinks.ApplicationInsights"
    ],
    "MinimumLevel": {
      "Default": "Information"
    },
    "WriteTo": [
      //Note: ApplicationInsights setting has to stay as first section in "WriteTo".
      {
        "Name": "ApplicationInsights",
        "Args": {
          "restrictedToMinimumLevel": "Information",
          "instrumentationKey": "", //Note: always keep it as empty. The value will be replaced by APPINSIGHTS_INSTRUMENTATIONKEY
          "telemetryConverter": "Serilog.Sinks.ApplicationInsights.Sinks.ApplicationInsights.TelemetryConverters.TraceTelemetryConverter, Serilog.Sinks.ApplicationInsights"
        }
      },
      {
        "Name": "Console",
        "Args": {
          "theme": "Serilog.Sinks.SystemConsole.Themes.AnsiConsoleTheme::Code, Serilog.Sinks.Console"
        }
      }
    ],
    "Enrich": [
      "FromLogContext",
      "WithMachineName",
      "WithThreadId"
    ]
  },
  "AllowedHosts": "*",
  "ConnectionStrings": {
    "DatabaseConnection": "data source= bdo-ca1-partner-sql-uat-01.database.windows.net; initial catalog =Identity-UAT; Column Encryption Setting=enabled;pooling=true;multipleactiveresultsets=False;connect timeout=120;Authentication=Active Directory Managed Identity"
    //"Server=tcp:bdo-ca1-partner-sql-uat-01.database.windows.net,1433;Initial Catalog=Identity-uat;Persist Security Info=False;Trusted_Connection=True;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;"
  },
  "IdentityServer": {
    "IAMUri": "https://bdo-ca1-pid-web-uat-01.azurewebsites.net",
    "IdentityServerJwtValidationClockSkew": 5,
    "IdentitySigningKeyPrvider": 2,
    "Clients": [
      {
        "ClientId": "apc_prod_nocors",
        "ClientName": "Annual Confirmation",
        "ClientUri": "https://bdo-ca1-apc-web-uat-01.azurewebsites.net/apc",
        "RequireClientSecret": false,
        "AllowedGrantTypes": [ "authorization_code" ],
        "RequirePkce": true,
        "AllowRememberConsent": true,
        "AllowAccessTokensViaBrowser": true,
        "AlwaysIncludeUserClaimsInIdToken": false,
        "AccessTokenType": "jwt",
        "RedirectUris": [ "https://bdo-ca1-apc-web-uat-01.azurewebsites.net/apc/auth-callback", "https://bdo-ca1-apc-web-uat-01.azurewebsites.net/apc/silent-auth-callback" ],
        "PostLogoutRedirectUris": [ "https://bdo-ca1-apc-web-uat-01.azurewebsites.net/apc/" ],
        "AllowedCorsOrigins": [ "https://bdo-ca1-apc-web-uat-01.azurewebsites.net" ],

        "AllowedScopes": [ "openid", "profile", "resource-api", "admin-api", "custom_profile" ],
        "RequireConsent": false,
        "EnableLocalLogin": true
      },
      {
        "ClientId": "report_prod_nocors",
        "ClientName": "Partner Report",
        "ClientUri": "https://bdo-ca1-prpt-web-uat-01.azurewebsites.net/prpt",
        "RequireClientSecret": false,
        "AllowedGrantTypes": [ "authorization_code" ],
        "RequirePkce": true,
        "AllowRememberConsent": true,
        "AllowAccessTokensViaBrowser": true,
        "AlwaysIncludeUserClaimsInIdToken": false,
        "AccessTokenType": "jwt",
        "RedirectUris": [ "https://bdo-ca1-prpt-web-uat-01.azurewebsites.net/prpt/auth-callback", "https://bdo-ca1-prpt-web-uat-01.azurewebsites.net/prpt/silent-auth-callback" ],
        "PostLogoutRedirectUris": [ "https://bdo-ca1-prpt-web-uat-01.azurewebsites.net/prpt/" ],
        "AllowedCorsOrigins": [ "https://bdo-ca1-prpt-web-uat-01.azurewebsites.net" ],

        "AllowedScopes": [ "openid", "profile", "resource-api", "admin-api", "custom_profile" ],
        "RequireConsent": false,
        "EnableLocalLogin": true
      },
      {
        "ClientId": "pr_prod_nocors",
        "ClientName": "Partner Rotation",
        "ClientUri": "https://bdo-ca1-pr-web-uat-01.azurewebsites.net/pr",
        "RequireClientSecret": false,
        "AllowedGrantTypes": [ "authorization_code" ],
        "RequirePkce": true,
        "AllowRememberConsent": true,
        "AllowAccessTokensViaBrowser": true,
        "AlwaysIncludeUserClaimsInIdToken": false,
        "AccessTokenType": "jwt",
        "RedirectUris": [ "https://bdo-ca1-pr-web-uat-01.azurewebsites.net/pr/auth-callback", "https://bdo-ca1-pr-web-uat-01.azurewebsites.net/pr/silent-auth-callback" ],
        "PostLogoutRedirectUris": [ "https://bdo-ca1-pr-web-uat-01.azurewebsites.net/pr/" ],
        "AllowedCorsOrigins": [ "https://bdo-ca1-pr-web-uat-01.azurewebsites.net" ],

        "AllowedScopes": [ "openid", "profile", "resource-api", "admin-api", "custom_profile" ],
        "RequireConsent": false,
        "EnableLocalLogin": true
      },
      {
        "ClientId": "pra_prod_nocors",
        "ClientName": "Partner Retirement",
        "ClientUri": "https://bdo-ca1-pra-web-uat-01.azurewebsites.net/pra",
        "RequireClientSecret": false,
        "AllowedGrantTypes": [ "authorization_code" ],
        "RequirePkce": true,
        "AllowRememberConsent": true,
        "AllowAccessTokensViaBrowser": true,
        "AlwaysIncludeUserClaimsInIdToken": false,
        "AccessTokenType": "jwt",
        "RedirectUris": [ "https://bdo-ca1-pra-web-uat-01.azurewebsites.net/pra/auth-callback", "https://bdo-ca1-pra-web-uat-01.azurewebsites.net/pra/silent-auth-callback" ],
        "PostLogoutRedirectUris": [ "https://bdo-ca1-pra-web-uat-01.azurewebsites.net/pra/" ],
        "AllowedCorsOrigins": [ "https://bdo-ca1-pra-web-uat-01.azurewebsites.net" ],
"AllowedScopes": [ "openid", "profile", "resource-api", "admin-api", "custom_profile" ],
        "RequireConsent": false,
        "EnableLocalLogin": true
      },
      {
        "ClientId": "pps_prod_nocors",
        "ClientName": "Partner Plans",
        "ClientUri": "https://bdo-ca1-pps-web-uat-01.azurewebsites.net/pps",
        "RequireClientSecret": false,
        "AllowedGrantTypes": [ "authorization_code" ],
        "RequirePkce": true,
        "AllowRememberConsent": true,
        "AllowAccessTokensViaBrowser": true,
        "AlwaysIncludeUserClaimsInIdToken": false,
        "AccessTokenType": "jwt",
        "RedirectUris": [ "https://bdo-ca1-pps-web-uat-01.azurewebsites.net/pps/auth-callback", "https://bdo-ca1-pps-web-uat-01.azurewebsites.net/pps/silent-auth-callback" ],
        "PostLogoutRedirectUris": [ "https://bdo-ca1-pps-web-uat-01.azurewebsites.net/pps/" ],
        "AllowedCorsOrigins": [ "https://bdo-ca1-pps-web-uat-01.azurewebsites.net" ],
        "AllowedScopes": [ "openid", "profile", "resource-api", "admin-api", "custom_profile" ],
        "RequireConsent": false,
        "EnableLocalLogin": true
      }
    ],
    "ExternalIdentityProviders": [
      {
        "ProviderName": "BDO-AAD",
        "Description": "Sign-in with BDO Canada LLP Azure AD",
        "AzureADAuthority": "https://login.microsoftonline.com/bdocanada.onmicrosoft.com",
        //"AzureADClientId": "3d82c065-484d-44dc-a5dd-728cd44b77cd",
        "AzureADTenant": "ee7a07de-4778-4cfe-8e91-8d9038ba72ec",
        //"AzureADClientSecret": "*************************************",
        "AzureADGraphVersion": "api-version=1.6",
        "CallbackPath": "/signin-oidc-bdo-llp-aad",
        "SignedOutCallbackPath": "/signout-callback-oidc",
        "RemoteSignOutPath": "/signout-oidc-bdo-llp-aad"
      }
    ]
  },
  "App": {
    "Sentinel": "1",
    "DatabaseCommandTimeout": 3600,
    "AzureKeyVaultConfig": {
      "TenantId": "ee7a07de-4778-4cfe-8e91-8d9038ba72ec",
      "AzureKeyVaultUri": "https://bdo-ca1-partner-kv-uat.vault.azure.net/"
    }
  }
}