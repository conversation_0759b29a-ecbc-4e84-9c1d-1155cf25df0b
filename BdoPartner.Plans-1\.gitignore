Development/AngularClientApp/.angular/
Development/AngularClientApp/node_modules/
Development/AngularClientApp/dist/
Development/ReactClientApp/node_modules/
Development/ReactClientApp/build/
Development/BdoPartner.Plans.Web.API/obj/
Development/BdoPartner.Plans.Web.API/bin/
Development/BdoPartner.Plans.Web.API/Logs/
Development/BdoPartner.Plans.Business/bin/
Development/BdoPartner.Plans.Business/obj/
Development/BdoPartner.Plans.Business.Test/bin/
Development/BdoPartner.Plans.Business.Test/obj/
Development/BdoPartner.Plans.Web.Common/bin/
Development/BdoPartner.Plans.Web.Common/obj/
Development/BdoPartner.Plans.Business.Interface/bin/
Development/BdoPartner.Plans.Business.Interface/obj/
Development/BdoPartner.Plans.Database/obj/
Development/BdoPartner.Plans.Database/bin/
Development/BdoPartner.Plans.IdentityServer/bin/
Development/BdoPartner.Plans.IdentityServer/obj/


Development/BdoPartner.Plans.Model.DTO/bin/
Development/BdoPartner.Plans.Model.DTO/obj/
Development/BdoPartner.Plans.Model.Entity/bin/
Development/BdoPartner.Plans.Model.Entity/obj/
Development/BdoPartner.Plans.Model.Mapper/bin/
Development/BdoPartner.Plans.Model.Mapper/obj/
Development/BdoPartner.Plans.Common/bin/
Development/BdoPartner.Plans.Common/obj/
Development/BdoPartner.Plans.DataAccess/bin/
Development/BdoPartner.Plans.DataAccess/obj/
Development/BdoPartner.Plans.DataAccess.Common/bin/
Development/BdoPartner.Plans.DataAccess.Common/obj/

/Development/BdoPartner.Plans.Database/BdoPartner.Plans.Database.dbmdl
/Development/.vs
*/.vs
.vs/*
Development/BdoPartner.Plans.Database/BdoPartner.Plans.Database.jfm
Development/BdoPartner.Plans.Database/BdoPartner.Plans.Database.sqlproj.user
.augment/
.memory_bank/
.code_assistant/
Development/BdoPartner.Plans.Web.API/appsettings.Local.json
Development/BdoPartner.Plans.IdentityServer/appsettings.local.json
node_modules/
BdoPartner.Retirement/
