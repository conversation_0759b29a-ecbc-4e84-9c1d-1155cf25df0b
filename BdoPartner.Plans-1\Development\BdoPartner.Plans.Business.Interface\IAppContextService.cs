﻿using BdoPartner.Plans.Common;
using System;
using System.Collections.Generic;
using System.Text;
using DTO = BdoPartner.Plans.Model.DTO;

namespace BdoPartner.Plans.Business.Interface
{
    /// <summary>
    ///  Application runtime context matters, such as global variables process.
    /// </summary>
    public interface IAppContextService
    {

        /// <summary>
        ///  Initialize the AppContext settings.
        /// </summary>
        /// <returns></returns>
        BusinessResult<DTO.AppContext> InitAppContext();
    }
}