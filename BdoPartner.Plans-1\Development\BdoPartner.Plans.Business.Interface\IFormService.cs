using BdoPartner.Plans.Common;
using BdoPartner.Plans.Model.DTO;
using System;
using System.Collections.Generic;

namespace BdoPartner.Plans.Business.Interface
{
    /// <summary>
    /// Business service interface for Form entity operations
    /// </summary>
    public interface IFormService
    {
        /// <summary>
        /// Get all forms
        /// </summary>
        /// <returns>Collection of forms</returns>
        BusinessResult<ICollection<Form>> GetForms();

        /// <summary>
        /// Get forms with filtering and pagination
        /// </summary>
        /// <param name="searchTerm">Search term for filtering</param>
        /// <param name="questionnaireId">Filter by questionnaire ID</param>
        /// <param name="year">Filter by year</param>
        /// <param name="status">Filter by status</param>
        /// <param name="partnerObjectId">Filter by partner object ID</param>
        /// <param name="isActive">Filter by active status</param>
        /// <param name="pageNumber">Page number for pagination</param>
        /// <param name="pageSize">Page size for pagination</param>
        /// <returns>Collection of forms</returns>
        BusinessResult<ICollection<Form>> SearchForms(string searchTerm = null, Guid? questionnaireId = null, 
            short? year = null, byte? status = null, string partnerObjectId = null, 
            bool? isActive = null, int pageNumber = 1, int pageSize = 50);

        /// <summary>
        /// Get form by ID
        /// </summary>
        /// <param name="id">Form ID</param>
        /// <returns>Form object</returns>
        BusinessResult<Form> GetFormById(Guid id);

        /// <summary>
        /// Get forms by questionnaire ID
        /// </summary>
        /// <param name="questionnaireId">Questionnaire ID</param>
        /// <returns>Collection of forms</returns>
        BusinessResult<ICollection<Form>> GetFormsByQuestionnaireId(Guid questionnaireId);

        /// <summary>
        /// Get forms by partner object ID
        /// </summary>
        /// <param name="partnerObjectId">Partner object ID</param>
        /// <returns>Collection of forms</returns>
        BusinessResult<ICollection<Form>> GetFormsByPartnerObjectId(string partnerObjectId);

        /// <summary>
        /// Get forms by partner user ID
        /// </summary>
        /// <param name="partnerUserId">Partner user ID</param>
        /// <returns>Collection of forms</returns>
        BusinessResult<ICollection<Form>> GetFormsByPartnerUserId(Guid partnerUserId);

        /// <summary>
        /// Create new form
        /// </summary>
        /// <param name="form">Form object to create</param>
        /// <returns>Created form object</returns>
        BusinessResult<Form> CreateForm(Form form);

        /// <summary>
        /// Update existing form
        /// </summary>
        /// <param name="form">Form object to update</param>
        /// <returns>Updated form object</returns>
        BusinessResult<Form> UpdateForm(Form form);

        /// <summary>
        /// Delete form by ID
        /// </summary>
        /// <param name="id">Form ID</param>
        /// <returns>Success result</returns>
        BusinessResult<bool> DeleteForm(Guid id);

        /// <summary>
        /// Submit form (change status to submitted)
        /// </summary>
        /// <param name="id">Form ID</param>
        /// <returns>Updated form object</returns>
        BusinessResult<Form> SubmitForm(Guid id);

        /// <summary>
        /// Approve form (change status to approved)
        /// </summary>
        /// <param name="id">Form ID</param>
        /// <param name="approverComments">Approver comments</param>
        /// <returns>Updated form object</returns>
        BusinessResult<Form> ApproveForm(Guid id, string approverComments = null);

        /// <summary>
        /// Reject form (change status to rejected)
        /// </summary>
        /// <param name="id">Form ID</param>
        /// <param name="rejectionComments">Rejection comments</param>
        /// <returns>Updated form object</returns>
        BusinessResult<Form> RejectForm(Guid id, string rejectionComments);

        /// <summary>
        /// Get or create form for current user and questionnaire
        /// </summary>
        /// <param name="questionnaireId">Questionnaire ID</param>
        /// <param name="year">Year</param>
        /// <param name="partnerUserId">Partner user ID</param>
        /// <param name="partnerObjectId">Partner Azure AD Object ID</param>
        /// <param name="partnerName">Partner name</param>
        /// <returns>Form object (existing or newly created)</returns>
        BusinessResult<Form> GetOrCreateFormForUser(Guid questionnaireId, short year, Guid partnerUserId, string partnerObjectId, string partnerName);

        /// <summary>
        /// Get form with user answers for current user
        /// </summary>
        /// <param name="questionnaireId">Questionnaire ID</param>
        /// <param name="year">Year</param>
        /// <param name="partnerUserId">Partner user ID</param>
        /// <returns>Form with user answers</returns>
        BusinessResult<Form> GetFormWithUserAnswers(Guid questionnaireId, short year, Guid partnerUserId);

        /// <summary>
        /// Get current year active questionnaire
        /// </summary>
        /// <param name="year">Year (optional, defaults to current year)</param>
        /// <returns>Active questionnaire for the year</returns>
        BusinessResult<Questionnaire> GetCurrentYearActiveQuestionnaire(short? year = null);

        /// <summary>
        /// Get my plan - consolidated method that gets questionnaire, creates/gets form, and loads user answers
        /// </summary>
        /// <param name="year">Year (optional, defaults to current year)</param>
        /// <param name="partnerUserId">Partner user ID</param>
        /// <param name="partnerObjectId">Partner Azure AD Object ID</param>
        /// <param name="partnerName">Partner name</param>
        /// <returns>Complete plan data including questionnaire, form, and user answers</returns>
        BusinessResult<MyPlanData> GetMyPlan(short? year, Guid partnerUserId, string partnerObjectId, string partnerName);
    }
}
