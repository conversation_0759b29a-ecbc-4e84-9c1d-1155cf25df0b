﻿using BdoPartner.Plans.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using BdoPartner.Plans.Model.DTO.Identity;

namespace BdoPartner.Plans.Business.Interface
{
    /// <summary>
    ///  Reference: https://www.keithmsmith.com/get-started-microsoft-graph-api-calls-net-core-3/
    /// </summary>
    public interface IGraphService
    {
        /// <summary>
        /// Get current logon user profile from Azure AD.
        /// Called inside Identity Server project.
        /// </summary>
        /// <returns></returns>
        Task<BusinessResult<UserProfile>> GetMe();

        /// <summary>
        ///  Check if current Azure AD logon user is the specified logon user (object Id).
        ///  Work for Identity Server ProfileService. 
        /// </summary>
        /// <param name="objectId"></param>
        /// <returns></returns>
        Task<Boolean> IsMe(string objectId);

    }
}
