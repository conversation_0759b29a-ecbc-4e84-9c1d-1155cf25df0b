using BdoPartner.Plans.Common;
using BdoPartner.Plans.Model.DTO;
using System;
using System.Collections.Generic;
using System.Linq.Expressions;

namespace BdoPartner.Plans.Business.Interface
{
    /// <summary>
    /// Business service interface for Group entity operations
    /// </summary>
    public interface IGroupService
    {
        /// <summary>
        /// Get all groups
        /// </summary>
        /// <returns>Collection of groups</returns>
        BusinessResult<ICollection<Group>> GetGroups();

        /// <summary>
        /// Get groups with filtering and pagination
        /// </summary>
        /// <param name="searchTerm">Search term for filtering</param>
        /// <param name="isActive">Filter by active status</param>
        /// <param name="pageNumber">Page number for pagination</param>
        /// <param name="pageSize">Page size for pagination</param>
        /// <returns>Collection of groups</returns>
        BusinessResult<ICollection<Group>> SearchGroups(string searchTerm = null, bool? isActive = null, int pageNumber = 1, int pageSize = 50);

        /// <summary>
        /// Get group by ID
        /// </summary>
        /// <param name="id">Group ID</param>
        /// <returns>Group object</returns>
        BusinessResult<Group> GetGroupById(Guid id);

        /// <summary>
        /// Create new group
        /// </summary>
        /// <param name="group">Group object to create</param>
        /// <returns>Created group object</returns>
        BusinessResult<Group> CreateGroup(Group group);

        /// <summary>
        /// Update existing group
        /// </summary>
        /// <param name="group">Group object to update</param>
        /// <returns>Updated group object</returns>
        BusinessResult<Group> UpdateGroup(Group group);

        /// <summary>
        /// Delete group by ID
        /// </summary>
        /// <param name="id">Group ID</param>
        /// <returns>Success result</returns>
        BusinessResult<bool> DeleteGroup(Guid id);

        /// <summary>
        /// Get groups for lookup/dropdown purposes
        /// </summary>
        /// <param name="includeInactive">Include inactive groups</param>
        /// <returns>Collection of lookup items</returns>
        BusinessResult<ICollection<LookupNum>> GetGroupsLookup(bool includeInactive = false);
    }
}
