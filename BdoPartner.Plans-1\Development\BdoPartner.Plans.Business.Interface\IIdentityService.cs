﻿using BdoPartner.Plans.Common;
using BdoPartner.Plans.Model.DTO.Identity;
using System;
using System.Collections.Generic;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;
using DTO = BdoPartner.Plans.Model.DTO;

namespace BdoPartner.Plans.Business.Interface
{
    /// <summary>
    ///  Work for user authentication and authorization.
    ///  User credential update, forget password and reset password process.
    /// </summary>
    public interface IIdentityService
    {
        /// <summary>
        ///  Check login user with username and password.
        /// </summary>
        /// <param name="username"></param>
        /// <param name="password"></param>
        /// <returns></returns>
        BusinessResult<DTO.Identity.User> ValidateCredentials(string username, string password, string clientId);
        
        /// <summary>
        ///  update login user password.
        ///  Finally send out email.
        /// </summary>
        /// <param name="username">login user name</param>
        /// <param name="password">new password</param>
        /// <param name="isTempPassword">Value = true, set TempPassword field in dbo.User table. Work for forget password and reset password.</param>
        /// <returns>true means update password updated.</returns>
        BusinessResult UpdateCredentials(string username, string password, bool isTempPassword);

        /// <summary>
        ///  TODO. not implement yet.
        /// </summary>
        /// <param name="userName"></param>
        /// <param name="clientId"></param>
        /// <returns></returns>
        BusinessResult ForgotPassword(string userName, string clientId);

        /// <summary>
        ///  Get user additional authorization settings from local database.
        ///  Work for get logon user's custom authorization from local database.
        /// </summary>
        /// <param name="userName">Reference to field "UserName" in table dbo.User. It must be unique.</param>
        /// <returns></returns>
        BusinessResult<DTO.Identity.User> GetUser(string userName, string authProvider);

        /// <summary>
        ///  Called by Identity Server project "ProfileService". 
        ///  Get custom claims and shared with Client and Resource Web API.
        /// </summary>
        /// <param name="userId">Logon user's Id. string type with guid format. Keep in "subject" claim.</param>
        /// <param name="clientId">Current accessing client's ClientId for Identity Server.</param>
        /// <param name="isSimpleAccess">
        ///  If value = true, ignore the user roles and permissions information generate for performance.
        /// </param>
        /// <returns></returns>
        Task<BusinessResult<DTO.Identity.User>> GetUserBySubjectId(string userId, string clientId, Boolean isSimpleAccess = false);


        /// <summary>
        ///  Get one user by id (primary key)
        /// </summary>
        /// <param name="id">Refered to field "Id" in table [User].</param>
        /// <returns></returns>
        BusinessResult<DTO.Identity.User> GetUserById(Guid id);

        /// <summary>
        ///  Validate if the reset password http request (link) is available or not.
        ///  The link will be set as expired after 24 hours based on ticks and current datetime in server.
        /// </summary>
        /// <param name="ticks">The server side date time when generate the reset password link.</param>
        /// <param name="signature">Hash signature for ClientId + "|" + Ticks</param>
        /// <returns>Return success, the reset password link is still available. Return false, the link has expired.</returns>
        BusinessResult ValidateResetPasswordRequest(string clientId, string ticks, string userName, string signature);


        /// <summary>
        ///  Check database table [User], if no existing user for input claim principal, create new user in table.
        ///  Finally, return this user object from table. Note: This new created user record should no passoword and password salt and 
        ///  column "AuthProvider" should be "AzureAD = 2".
        ///  
        /// </summary>
        /// <param name="userClaims">Claims got for external authentication provider when user login from there.</param>
        /// <returns></returns>
        BusinessResult<DTO.Identity.User> GetUserForExternalProvider(ClaimsPrincipal userClaims, string strProvider);
    }
}