﻿using BdoPartner.Plans.Common;
using BdoPartner.Plans.Model.DTO;
using System;
using System.Collections.Generic;
using System.Text;
using DTO = BdoPartner.Plans.Model.DTO;

namespace BdoPartner.Plans.Business.Interface
{
    public interface ILookupService
    {
        List<LookupNum> GetLanguages(bool includeEmptyRow);
              
        /// <summary>
        /// Get all form statuses with localized display names based on current language
        /// </summary>
        /// <param name="includeEmptyRow">Include empty row for dropdown selection</param>
        /// <returns>List of form statuses</returns>
        List<LookupNum> GetFormStatuses(bool includeEmptyRow);

        /// <summary>
        /// Get all questionnaire statuses with localized display names based on current language
        /// </summary>
        /// <param name="includeEmptyRow">Include empty row for dropdown selection</param>
        /// <returns>List of questionnaire statuses</returns>
        List<LookupNum> GetQuestionnaireStatuses(bool includeEmptyRow);

        /// <summary>
        ///  Get all records from table dbo.Notification.
        ///  POC purpose only.
        /// </summary>
        /// <returns></returns>
        BusinessResult<ICollection<Notification>> GetNotifications();

    }
}
