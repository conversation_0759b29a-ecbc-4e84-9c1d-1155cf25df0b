using BdoPartner.Plans.Common;
using BdoPartner.Plans.DataAccess.Common.PagedList;
using BdoPartner.Plans.Model.DTO;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace BdoPartner.Plans.Business.Interface
{
    /// <summary>
    /// Business service interface for Partner Reference Data Upload operations
    /// </summary>
    public interface IPartnerReferenceDataUploadService
    {
        /// <summary>
        /// Get all partner reference data metadata
        /// </summary>
        /// <returns>Collection of partner reference data metadata</returns>
        BusinessResult<ICollection<PartnerReferenceDataMeta>> GetPartnerReferenceDataMetas();

        /// <summary>
        /// Get partner reference data metadata by ID
        /// </summary>
        /// <param name="id">Metadata ID</param>
        /// <returns>Partner reference data metadata object</returns>
        BusinessResult<PartnerReferenceDataMeta> GetPartnerReferenceDataMetaById(Guid id);

        /// <summary>
        /// Create or update partner reference data metadata from uploaded file
        /// </summary>
        /// <param name="file">Uploaded file</param>
        /// <param name="year">Year for the metadata</param>
        /// <param name="cycle">Cycle for the metadata</param>
        /// <returns>Created or updated metadata</returns>
        Task<BusinessResult<PartnerReferenceDataMeta>> ExtractMetadataFromFileAsync(IFormFile file, short year, byte cycle);

        /// <summary>
        /// Get all partner reference data uploads
        /// </summary>
        /// <returns>Collection of partner reference data uploads</returns>
        BusinessResult<ICollection<PartnerReferenceDataUpload>> GetPartnerReferenceDataUploads();

        /// <summary>
        /// Get partner reference data uploads with filtering and pagination
        /// </summary>
        /// <param name="year">Filter by year</param>
        /// <param name="cycle">Filter by cycle</param>
        /// <param name="status">Filter by status</param>
        /// <param name="pageIndex">Page index for pagination (0-based)</param>
        /// <param name="pageSize">Page size for pagination</param>
        /// <returns>Paginated collection of partner reference data uploads with metadata</returns>
        BusinessResult<IPagedList<PartnerReferenceDataUpload>> SearchPartnerReferenceDataUploads(short? year = null,
            byte? cycle = null, byte? status = null, int pageIndex = 0, int pageSize = 20);

        /// <summary>
        /// Get partner reference data upload by ID
        /// </summary>
        /// <param name="id">Upload ID</param>
        /// <returns>Partner reference data upload object</returns>
        BusinessResult<PartnerReferenceDataUpload> GetPartnerReferenceDataUploadById(Guid id);

        /// <summary>
        /// Get partner reference data upload details by upload ID
        /// </summary>
        /// <param name="uploadId">Upload ID</param>
        /// <param name="includeValidOnly">Include only valid records</param>
        /// <param name="includeInvalidOnly">Include only invalid records</param>
        /// <returns>Collection of upload details with metadata information</returns>
        BusinessResult<PartnerReferenceDataUploadDetailsResult> GetPartnerReferenceDataUploadDetails(Guid uploadId,
            bool includeValidOnly = false, bool includeInvalidOnly = false);

        /// <summary>
        /// Upload and process Excel/CSV file
        /// </summary>
        /// <param name="file">Uploaded file</param>
        /// <param name="year">Year for the upload</param>
        /// <param name="cycle">Cycle for the upload</param>
        /// <returns>Upload result with validation summary</returns>
        Task<BusinessResult<PartnerReferenceDataUpload>> UploadFileAsync(IFormFile file, short year, byte cycle);

        /// <summary>
        /// Validate uploaded data
        /// </summary>
        /// <param name="uploadId">Upload ID to validate</param>
        /// <returns>Validation result</returns>
        Task<BusinessResult<PartnerReferenceDataUpload>> ValidateUploadAsync(Guid uploadId);

        /// <summary>
        /// Submit validated data to final PartnerReferenceData table.
        /// When overwriteExisting = true (default), replaces existing data for the same partner/year/cycle.
        /// </summary>
        /// <param name="uploadId">Upload ID to submit</param>
        /// <param name="overwriteExisting">Default true. When true, replaces existing data for the same partner/year/cycle.</param>
        /// <returns>Submit result</returns>
        Task<BusinessResult<bool>> SubmitUploadAsync(Guid uploadId, bool overwriteExisting = true);

        /// <summary>
        /// Get partner reference data for a specific partner, year, and cycle
        /// </summary>
        /// <param name="partnerId">Partner ID</param>
        /// <param name="year">Year</param>
        /// <param name="cycle">Cycle</param>
        /// <returns>Partner reference data</returns>
        BusinessResult<PartnerReferenceData> GetPartnerReferenceData(Guid partnerId, short year, byte cycle);

        /// <summary>
        /// Get partner reference data with filtering and pagination
        /// </summary>
        /// <param name="year">Filter by year</param>
        /// <param name="cycle">Filter by cycle</param>
        /// <param name="partnerId">Filter by partner ID</param>
        /// <param name="pageIndex">Page index for pagination (0-based)</param>
        /// <param name="pageSize">Page size for pagination</param>
        /// <returns>Paginated collection of partner reference data</returns>
        BusinessResult<IPagedList<PartnerReferenceData>> SearchPartnerReferenceData(short? year = null,
            byte? cycle = null, Guid? partnerId = null, int pageIndex = 0, int pageSize = 20);

        /// <summary>
        /// Delete partner reference data upload
        /// </summary>
        /// <param name="uploadId">Upload ID to delete</param>
        /// <returns>Delete result</returns>
        BusinessResult<bool> DeleteUpload(Guid uploadId);

        /// <summary>
        /// Get upload template for partner reference data
        /// </summary>
        /// <returns>Excel template file as byte array</returns>
        BusinessResult<byte[]> GetUploadTemplate();

        /// <summary>
        /// Get available column names for Form Creator mapping based on questionnaire year
        /// </summary>
        /// <param name="year">Questionnaire year to get relevant column names for</param>
        /// <param name="includeCyclePrefixes">Whether to include cycle prefixes for disambiguation</param>
        /// <returns>List of available column names with cycle context</returns>
        BusinessResult<ICollection<FormCreatorColumnChoice>> GetAvailableColumnNamesForFormCreator(short year, bool includeCyclePrefixes = true);

        /// <summary>
        /// Get partner reference data metadata by year with all cycles
        /// </summary>
        /// <param name="year">Year to filter metadata</param>
        /// <returns>List of metadata for the specified year</returns>
        BusinessResult<ICollection<PartnerReferenceDataMeta>> GetPartnerReferenceDataMetasByYear(short year);
    }
}
