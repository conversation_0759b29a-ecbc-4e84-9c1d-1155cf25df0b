using BdoPartner.Plans.Common;
using BdoPartner.Plans.DataAccess.Common.PagedList;
using BdoPartner.Plans.Model.DTO;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace BdoPartner.Plans.Business.Interface
{
    /// <summary>
    /// Business service interface for Partner Reviewer Upload operations
    /// </summary>
    public interface IPartnerReviewerUploadService
    {
        /// <summary>
        /// Get all partner reviewer uploads
        /// </summary>
        /// <returns>Collection of partner reviewer uploads</returns>
        BusinessResult<ICollection<PartnerReviewerUpload>> GetPartnerReviewerUploads();

        /// <summary>
        /// Get partner reviewer uploads with filtering and pagination
        /// </summary>
        /// <param name="year">Filter by year</param>
        /// <param name="status">Filter by status</param>
        /// <param name="pageIndex">Page index for pagination (0-based)</param>
        /// <param name="pageSize">Page size for pagination</param>
        /// <returns>Paginated collection of partner reviewer uploads with metadata</returns>
        BusinessResult<IPagedList<PartnerReviewerUpload>> SearchPartnerReviewerUploads(short? year = null,
            byte? status = null, int pageIndex = 0, int pageSize = 20);

        /// <summary>
        /// Get partner reviewer upload by ID
        /// </summary>
        /// <param name="id">Upload ID</param>
        /// <returns>Partner reviewer upload object</returns>
        BusinessResult<PartnerReviewerUpload> GetPartnerReviewerUploadById(int id);

        /// <summary>
        /// Get partner reviewer upload details by upload ID
        /// </summary>
        /// <param name="uploadId">Upload ID</param>
        /// <param name="includeValidOnly">Include only valid records</param>
        /// <param name="includeInvalidOnly">Include only invalid records</param>
        /// <returns>Collection of upload details</returns>
        BusinessResult<ICollection<PartnerReviewerUploadDetails>> GetPartnerReviewerUploadDetails(int uploadId, 
            bool includeValidOnly = false, bool includeInvalidOnly = false);

        /// <summary>
        /// Upload and process Excel/CSV file
        /// </summary>
        /// <param name="file">Uploaded file</param>
        /// <param name="years">Years for the upload (comma-separated string, e.g., "2023,2024")</param>
        /// <returns>Upload result with validation summary</returns>
        Task<BusinessResult<PartnerReviewerUpload>> UploadFileAsync(IFormFile file, string years);

        /// <summary>
        /// Validate uploaded data
        /// </summary>
        /// <param name="uploadId">Upload ID to validate</param>
        /// <returns>Validation result</returns>
        Task<BusinessResult<PartnerReviewerUpload>> ValidateUploadAsync(int uploadId);

        /// <summary>
        /// Submit validated data to final PartnerReviewer table.
        /// When overwriteExisting = true (default), performs complete replacement:
        /// 1. Updates existing records that match staging data
        /// 2. Creates new records for partners in staging data
        /// 3. Deletes existing records for the same years that are NOT in staging data
        /// </summary>
        /// <param name="uploadId">Upload ID to submit</param>
        /// <param name="overwriteExisting">Default true. When true, performs complete replacement of data for the specified years.</param>
        /// <returns>Submit result</returns>
        Task<BusinessResult<bool>> SubmitUploadAsync(int uploadId, bool overwriteExisting = true);



        /// <summary>
        /// Get partner reviewers for a specific year
        /// </summary>
        /// <param name="year">Year to filter by</param>
        /// <returns>Collection of partner reviewers</returns>
        BusinessResult<ICollection<PartnerReviewer>> GetPartnerReviewersByYear(short year);

        /// <summary>
        /// Get partner reviewer by partner ID and year
        /// </summary>
        /// <param name="partnerId">Partner ID</param>
        /// <param name="year">Year</param>
        /// <returns>Partner reviewer object</returns>
        BusinessResult<PartnerReviewer> GetPartnerReviewerByPartnerAndYear(Guid partnerId, short year);

        /// <summary>
        /// Update partner reviewer assignment
        /// </summary>
        /// <param name="partnerReviewer">Partner reviewer object to update</param>
        /// <returns>Updated partner reviewer object</returns>
        BusinessResult<PartnerReviewer> UpdatePartnerReviewer(PartnerReviewer partnerReviewer);

        /// <summary>
        /// Delete partner reviewer assignment
        /// </summary>
        /// <param name="id">Partner reviewer ID</param>
        /// <returns>Success result</returns>
        BusinessResult<bool> DeletePartnerReviewer(Guid id);

        /// <summary>
        /// Delete partner reviewer upload and all its details
        /// </summary>
        /// <param name="uploadId">Upload ID to delete</param>
        /// <returns>Success result</returns>
        BusinessResult<bool> DeletePartnerReviewerUpload(int uploadId);

        /// <summary>
        /// Get upload template file
        /// </summary>
        /// <returns>Template file content</returns>
        BusinessResult<byte[]> GetUploadTemplate();

        /// <summary>
        /// Export partner reviewers to Excel
        /// </summary>
        /// <param name="year">Year to export</param>
        /// <returns>Excel file content</returns>
        BusinessResult<byte[]> ExportPartnerReviewersToExcel(short year);
    }
}
