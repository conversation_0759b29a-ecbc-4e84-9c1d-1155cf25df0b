using BdoPartner.Plans.Common;
using BdoPartner.Plans.Model.DTO;
using System;
using System.Collections.Generic;

namespace BdoPartner.Plans.Business.Interface
{
    /// <summary>
    /// Business service interface for Partner entity operations
    /// </summary>
    public interface IPartnerService
    {
        /// <summary>
        /// Get all partners
        /// </summary>
        /// <returns>Collection of partners</returns>
        BusinessResult<ICollection<Partner>> GetPartners();

        /// <summary>
        /// Get partners with filtering and pagination
        /// </summary>
        /// <param name="searchTerm">Search term for filtering</param>
        /// <param name="partnerType">Filter by partner type</param>
        /// <param name="department">Filter by department</param>
        /// <param name="location">Filter by location</param>
        /// <param name="isActive">Filter by active status</param>
        /// <param name="pageNumber">Page number for pagination</param>
        /// <param name="pageSize">Page size for pagination</param>
        /// <returns>Collection of partners</returns>
        BusinessResult<ICollection<Partner>> SearchPartners(string searchTerm = null,
            string partnerType = null, string department = null, string location = null,
            bool? isActive = null, int pageNumber = 1, int pageSize = 50);

        /// <summary>
        /// Get partner by ID
        /// </summary>
        /// <param name="id">Partner ID</param>
        /// <returns>Partner object</returns>
        BusinessResult<Partner> GetPartnerById(Guid id);

        /// <summary>
        /// Get partner by employee ID
        /// </summary>
        /// <param name="employeeId">Employee ID</param>
        /// <returns>Partner object</returns>
        BusinessResult<Partner> GetPartnerByEmployeeId(int employeeId);

        /// <summary>
        /// Create new partner
        /// </summary>
        /// <param name="partner">Partner object to create</param>
        /// <returns>Created partner object</returns>
        BusinessResult<Partner> CreatePartner(Partner partner);

        /// <summary>
        /// Update existing partner
        /// </summary>
        /// <param name="partner">Partner object to update</param>
        /// <returns>Updated partner object</returns>
        BusinessResult<Partner> UpdatePartner(Partner partner);

        /// <summary>
        /// Delete partner by ID
        /// </summary>
        /// <param name="id">Partner ID</param>
        /// <returns>Success result</returns>
        BusinessResult<bool> DeletePartner(Guid id);

        /// <summary>
        /// Get partners for lookup/dropdown purposes
        /// </summary>
        /// <param name="includeInactive">Include inactive partners</param>
        /// <returns>Collection of lookup items</returns>
        BusinessResult<ICollection<Lookup>> GetPartnersLookup(bool includeInactive = false);
    }
}
