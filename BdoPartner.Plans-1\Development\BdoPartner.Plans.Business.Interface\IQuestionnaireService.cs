using BdoPartner.Plans.Common;
using BdoPartner.Plans.DataAccess.Common.PagedList;
using BdoPartner.Plans.Model.DTO;
using System;
using System.Collections.Generic;

namespace BdoPartner.Plans.Business.Interface
{
    /// <summary>
    /// Business service interface for Questionnaire entity operations
    /// </summary>
    public interface IQuestionnaireService
    {
        /// <summary>
        /// Get all questionnaires
        /// </summary>
        /// <returns>Collection of questionnaires</returns>
        BusinessResult<ICollection<Questionnaire>> GetQuestionnaires();

        /// <summary>
        /// Get questionnaires with filtering and pagination
        /// Returns simplified DTO for list rendering (excludes heavy JSON fields for better performance)
        /// </summary>
        /// <param name="searchTerm">Search term for filtering</param>
        /// <param name="year">Filter by year</param>
        /// <param name="status">Filter by status</param>
        /// <param name="isActive">Filter by active status</param>
        /// <param name="pageIndex">Page index for pagination (0-based)</param>
        /// <param name="pageSize">Page size for pagination</param>
        /// <returns>Paginated collection of questionnaire list items</returns>
        BusinessResult<IPagedList<QuestionnaireListItem>> SearchQuestionnaires(string searchTerm = null, short? year = null,
            byte? status = null, bool? isActive = null, int pageIndex = 0, int pageSize = 20);

        /// <summary>
        /// Get questionnaire by ID
        /// </summary>
        /// <param name="id">Questionnaire ID</param>
        /// <returns>Questionnaire object</returns>
        BusinessResult<Questionnaire> GetQuestionnaireById(Guid id);

        /// <summary>
        /// Get questionnaires by year
        /// </summary>
        /// <param name="year">Year to filter by</param>
        /// <returns>Collection of questionnaires</returns>
        BusinessResult<ICollection<Questionnaire>> GetQuestionnairesByYear(short year);

        /// <summary>
        /// Create new questionnaire
        /// </summary>
        /// <param name="questionnaire">Questionnaire object to create</param>
        /// <returns>Created questionnaire object</returns>
        BusinessResult<Questionnaire> CreateQuestionnaire(Questionnaire questionnaire);

        /// <summary>
        /// Update existing questionnaire
        /// </summary>
        /// <param name="questionnaire">Questionnaire object to update</param>
        /// <returns>Updated questionnaire object</returns>
        BusinessResult<Questionnaire> UpdateQuestionnaire(Questionnaire questionnaire);

        /// <summary>
        /// Validate if a questionnaire can be deleted
        /// </summary>
        /// <param name="id">Questionnaire ID</param>
        /// <returns>Validation result</returns>
        BusinessResult<QuestionnaireDeleteValidation> ValidateQuestionnaireForDeletion(Guid id);

        /// <summary>
        /// Delete questionnaire by ID
        /// </summary>
        /// <param name="id">Questionnaire ID</param>
        /// <returns>Success result</returns>
        BusinessResult<bool> DeleteQuestionnaire(Guid id);

        /// <summary>
        /// Get questionnaires for lookup/dropdown purposes
        /// </summary>
        /// <param name="includeInactive">Include inactive questionnaires</param>
        /// <returns>Collection of lookup items</returns>
        BusinessResult<ICollection<Lookup>> GetQuestionnairesLookup(bool includeInactive = false);

        /// <summary>
        /// Check if there's already a published questionnaire in the specified year
        /// </summary>
        /// <param name="year">Year to check</param>
        /// <param name="excludeId">Optional questionnaire ID to exclude from the check</param>
        /// <returns>Boolean indicating if a published questionnaire exists in the year</returns>
        BusinessResult<bool> HasPublishedQuestionnaireInYear(short year, Guid? excludeId = null);

        /// <summary>
        /// Publish questionnaire (change status to active)
        /// </summary>
        /// <param name="id">Questionnaire ID</param>
        /// <returns>Updated questionnaire object</returns>
        BusinessResult<Questionnaire> PublishQuestionnaire(Guid id);

        /// <summary>
        /// Archive questionnaire (change status to inactive)
        /// </summary>
        /// <param name="id">Questionnaire ID</param>
        /// <returns>Updated questionnaire object</returns>
        BusinessResult<Questionnaire> ArchiveQuestionnaire(Guid id);
    }
}
