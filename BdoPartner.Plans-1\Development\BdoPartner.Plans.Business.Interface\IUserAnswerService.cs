using BdoPartner.Plans.Common;
using BdoPartner.Plans.Model.DTO;
using System;
using System.Collections.Generic;

namespace BdoPartner.Plans.Business.Interface
{
    /// <summary>
    /// Business service interface for UserAnswer entity operations
    /// </summary>
    public interface IUserAnswerService
    {
        /// <summary>
        /// Get all user answers
        /// </summary>
        /// <returns>Collection of user answers</returns>
        BusinessResult<ICollection<UserAnswer>> GetUserAnswers();

        /// <summary>
        /// Get user answers with filtering and pagination
        /// </summary>
        /// <param name="formId">Filter by form ID</param>
        /// <param name="isActive">Filter by active status</param>
        /// <param name="pageNumber">Page number for pagination</param>
        /// <param name="pageSize">Page size for pagination</param>
        /// <returns>Collection of user answers</returns>
        BusinessResult<ICollection<UserAnswer>> SearchUserAnswers(Guid? formId = null, 
            bool? isActive = null, int pageNumber = 1, int pageSize = 50);

        /// <summary>
        /// Get user answer by ID
        /// </summary>
        /// <param name="id">User answer ID</param>
        /// <returns>User answer object</returns>
        BusinessResult<UserAnswer> GetUserAnswerById(Guid id);

        /// <summary>
        /// Get user answers by form ID
        /// </summary>
        /// <param name="formId">Form ID</param>
        /// <returns>Collection of user answers</returns>
        BusinessResult<ICollection<UserAnswer>> GetUserAnswersByFormId(Guid formId);

        /// <summary>
        /// Create new user answer
        /// </summary>
        /// <param name="userAnswer">User answer object to create</param>
        /// <returns>Created user answer object</returns>
        BusinessResult<UserAnswer> CreateUserAnswer(UserAnswer userAnswer);

        /// <summary>
        /// Update existing user answer
        /// </summary>
        /// <param name="userAnswer">User answer object to update</param>
        /// <returns>Updated user answer object</returns>
        BusinessResult<UserAnswer> UpdateUserAnswer(UserAnswer userAnswer);

        /// <summary>
        /// Delete user answer by ID
        /// </summary>
        /// <param name="id">User answer ID</param>
        /// <returns>Success result</returns>
        BusinessResult<bool> DeleteUserAnswer(Guid id);

        /// <summary>
        /// Bulk create user answers for a form
        /// </summary>
        /// <param name="formId">Form ID</param>
        /// <param name="userAnswers">Collection of user answers to create</param>
        /// <returns>Collection of created user answers</returns>
        BusinessResult<ICollection<UserAnswer>> BulkCreateUserAnswers(Guid formId, ICollection<UserAnswer> userAnswers);

        /// <summary>
        /// Bulk update user answers for a form
        /// </summary>
        /// <param name="formId">Form ID</param>
        /// <param name="userAnswers">Collection of user answers to update</param>
        /// <returns>Collection of updated user answers</returns>
        BusinessResult<ICollection<UserAnswer>> BulkUpdateUserAnswers(Guid formId, ICollection<UserAnswer> userAnswers);

        /// <summary>
        /// Save or update user answer for a form (auto-save functionality)
        /// </summary>
        /// <param name="formId">Form ID</param>
        /// <param name="answerData">Survey answer data as JSON string</param>
        /// <returns>Saved user answer</returns>
        BusinessResult<UserAnswer> SaveOrUpdateUserAnswer(Guid formId, string answerData);
    }
}
