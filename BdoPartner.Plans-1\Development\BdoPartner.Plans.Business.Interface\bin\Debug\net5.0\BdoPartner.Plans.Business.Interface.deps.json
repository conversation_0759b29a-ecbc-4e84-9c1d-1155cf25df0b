{"runtimeTarget": {"name": ".NETCoreApp,Version=v5.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v5.0": {"BdoPartner.Plans.Business.Interface/1.0.0": {"dependencies": {"BdoPartner.Plans.Common": "1.0.0", "BdoPartner.Plans.DataAccess.Common": "1.0.0", "BdoPartner.Plans.Model.DTO": "1.0.0"}, "runtime": {"BdoPartner.Plans.Business.Interface.dll": {}}}, "AutoMapper/10.1.1": {"dependencies": {"Microsoft.CSharp": "4.7.0", "System.Reflection.Emit": "4.7.0"}, "runtime": {"lib/netstandard2.0/AutoMapper.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.1.1.0"}}}, "Azure.Core/1.6.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.0.0", "System.Buffers": "4.5.0", "System.Diagnostics.DiagnosticSource": "5.0.1", "System.Memory": "4.5.3", "System.Numerics.Vectors": "4.5.0", "System.Text.Json": "5.0.1", "System.Threading.Tasks.Extensions": "4.5.2"}, "runtime": {"lib/netstandard2.0/Azure.Core.dll": {"assemblyVersion": "1.6.0.0", "fileVersion": "1.600.20.52802"}}}, "Azure.Identity/1.3.0": {"dependencies": {"Azure.Core": "1.6.0", "Microsoft.Identity.Client": "4.22.0", "Microsoft.Identity.Client.Extensions.Msal": "2.16.5", "System.Memory": "4.5.3", "System.Security.Cryptography.ProtectedData": "4.7.0", "System.Text.Json": "5.0.1", "System.Threading.Tasks.Extensions": "4.5.2"}, "runtime": {"lib/netstandard2.0/Azure.Identity.dll": {"assemblyVersion": "1.3.0.0", "fileVersion": "1.300.20.56202"}}}, "Castle.Core/4.4.1": {"dependencies": {"NETStandard.Library": "1.6.1", "System.Collections.Specialized": "4.3.0", "System.ComponentModel": "4.3.0", "System.ComponentModel.TypeConverter": "4.3.0", "System.Diagnostics.TraceSource": "4.3.0", "System.Dynamic.Runtime": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.7.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Xml.XmlDocument": "4.3.0"}, "runtime": {"lib/netstandard1.5/Castle.Core.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "4.4.1.0"}}}, "IdentityModel/5.1.0": {"dependencies": {"System.Text.Encodings.Web": "5.0.1", "System.Text.Json": "5.0.1"}, "runtime": {"lib/net5.0/IdentityModel.dll": {"assemblyVersion": "5.1.0.0", "fileVersion": "5.1.0.0"}}}, "Microsoft.ApplicationInsights/2.17.0": {"dependencies": {"System.Diagnostics.DiagnosticSource": "5.0.1"}, "runtime": {"lib/netstandard2.0/Microsoft.ApplicationInsights.dll": {"assemblyVersion": "**********", "fileVersion": "**********"}}}, "Microsoft.AspNetCore.Authentication/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Core": "2.2.0", "Microsoft.AspNetCore.DataProtection": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0", "Microsoft.Extensions.WebEncoders": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Authentication.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Authentication.Core/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Core.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Cryptography.Internal/2.2.0": {"runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Cryptography.Internal.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.DataProtection/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "2.2.0", "Microsoft.AspNetCore.DataProtection.Abstractions": "2.2.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0", "Microsoft.Win32.Registry": "4.7.0", "System.Security.Cryptography.Xml": "4.5.0", "System.Security.Principal.Windows": "4.7.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.DataProtection.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.DataProtection.Abstractions/2.2.0": {"runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.DataProtection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Hosting/2.2.7": {"dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.Extensions.Configuration": "5.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "5.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "5.0.0", "Microsoft.Extensions.DependencyInjection": "5.0.2", "Microsoft.Extensions.FileProviders.Physical": "5.0.0", "Microsoft.Extensions.Hosting.Abstractions": "2.2.0", "Microsoft.Extensions.Logging": "5.0.0", "Microsoft.Extensions.Options": "5.0.0", "System.Diagnostics.DiagnosticSource": "5.0.1", "System.Reflection.Metadata": "1.6.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.7.19225"}}}, "Microsoft.AspNetCore.Hosting.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Hosting.Server.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Hosting.Abstractions": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "Microsoft.Extensions.Configuration.Abstractions": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Server.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Http/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.AspNetCore.WebUtilities": "2.2.0", "Microsoft.Extensions.ObjectPool": "2.2.0", "Microsoft.Extensions.Options": "5.0.0", "Microsoft.Net.Http.Headers": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "System.Text.Encodings.Web": "5.0.1"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Http.Extensions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.FileProviders.Abstractions": "5.0.0", "Microsoft.Net.Http.Headers": "2.2.0", "System.Buffers": "4.5.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.WebUtilities/2.2.0": {"dependencies": {"Microsoft.Net.Http.Headers": "2.2.0", "System.Text.Encodings.Web": "5.0.1"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.WebUtilities.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.Bcl.AsyncInterfaces/1.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "4.700.19.46214"}}}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.Data.SqlClient/3.0.0": {"dependencies": {"Azure.Identity": "1.3.0", "Microsoft.Data.SqlClient.SNI.runtime": "3.0.0", "Microsoft.Identity.Client": "4.22.0", "Microsoft.IdentityModel.JsonWebTokens": "6.8.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.8.0", "Microsoft.Win32.Registry": "4.7.0", "System.Configuration.ConfigurationManager": "4.7.0", "System.Diagnostics.DiagnosticSource": "5.0.1", "System.Runtime.Caching": "4.7.0", "System.Security.Principal.Windows": "4.7.0", "System.Text.Encoding.CodePages": "4.7.0", "System.Text.Encodings.Web": "5.0.1"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp3.1/Microsoft.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "*******"}, "runtimes/win/lib/netcoreapp3.1/Microsoft.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Data.SqlClient.SNI.runtime/3.0.0": {"runtimeTargets": {"runtimes/win-arm/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "*******"}}}, "Microsoft.EntityFrameworkCore/5.0.8": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "5.0.8", "Microsoft.EntityFrameworkCore.Analyzers": "5.0.8", "Microsoft.Extensions.Caching.Memory": "5.0.0", "Microsoft.Extensions.DependencyInjection": "5.0.2", "Microsoft.Extensions.Logging": "5.0.0", "System.Collections.Immutable": "5.0.0", "System.ComponentModel.Annotations": "5.0.0", "System.Diagnostics.DiagnosticSource": "5.0.1"}, "runtime": {"lib/netstandard2.1/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "5.0.8.0", "fileVersion": "5.0.821.31512"}}}, "Microsoft.EntityFrameworkCore.Abstractions/5.0.8": {"runtime": {"lib/netstandard2.1/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "5.0.8.0", "fileVersion": "5.0.821.31512"}}}, "Microsoft.EntityFrameworkCore.Analyzers/5.0.8": {}, "Microsoft.EntityFrameworkCore.InMemory/5.0.8": {"dependencies": {"Microsoft.EntityFrameworkCore": "5.0.8"}, "runtime": {"lib/netstandard2.1/Microsoft.EntityFrameworkCore.InMemory.dll": {"assemblyVersion": "5.0.8.0", "fileVersion": "5.0.821.31512"}}}, "Microsoft.EntityFrameworkCore.Proxies/5.0.8": {"dependencies": {"Castle.Core": "4.4.1", "Microsoft.EntityFrameworkCore": "5.0.8"}, "runtime": {"lib/netstandard2.1/Microsoft.EntityFrameworkCore.Proxies.dll": {"assemblyVersion": "5.0.8.0", "fileVersion": "5.0.821.31512"}}}, "Microsoft.EntityFrameworkCore.Relational/5.0.8": {"dependencies": {"Microsoft.EntityFrameworkCore": "5.0.8", "Microsoft.Extensions.Configuration.Abstractions": "5.0.0"}, "runtime": {"lib/netstandard2.1/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "5.0.8.0", "fileVersion": "5.0.821.31512"}}}, "Microsoft.EntityFrameworkCore.SqlServer/5.0.8": {"dependencies": {"Microsoft.Data.SqlClient": "3.0.0", "Microsoft.EntityFrameworkCore.Relational": "5.0.8"}, "runtime": {"lib/netstandard2.1/Microsoft.EntityFrameworkCore.SqlServer.dll": {"assemblyVersion": "5.0.8.0", "fileVersion": "5.0.821.31512"}}}, "Microsoft.Extensions.Caching.Abstractions/5.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.Caching.Memory/5.0.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "5.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0", "Microsoft.Extensions.Primitives": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.Configuration/5.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "5.0.0", "Microsoft.Extensions.Primitives": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.Configuration.Abstractions/5.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.Configuration.Binder/5.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/5.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "5.0.0", "Microsoft.Extensions.Configuration.Abstractions": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.Configuration.FileExtensions/5.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "5.0.0", "Microsoft.Extensions.Configuration.Abstractions": "5.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "5.0.0", "Microsoft.Extensions.FileProviders.Physical": "5.0.0", "Microsoft.Extensions.Primitives": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.Configuration.Json/5.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "5.0.0", "Microsoft.Extensions.Configuration.Abstractions": "5.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "5.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "5.0.0"}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.DependencyInjection/5.0.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0"}, "runtime": {"lib/net5.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.821.31504"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/5.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.FileProviders.Abstractions/5.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.FileProviders.Physical/5.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "5.0.0", "Microsoft.Extensions.FileSystemGlobbing": "5.0.0", "Microsoft.Extensions.Primitives": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.FileSystemGlobbing/5.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.Hosting.Abstractions/2.2.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "5.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "5.0.0", "Microsoft.Extensions.Logging.Abstractions": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.Extensions.Logging/5.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "5.0.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0"}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.Logging.Abstractions/5.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.Logging.ApplicationInsights/2.17.0": {"dependencies": {"Microsoft.ApplicationInsights": "2.17.0", "Microsoft.Extensions.Logging": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.ApplicationInsights.dll": {"assemblyVersion": "**********", "fileVersion": "**********"}}}, "Microsoft.Extensions.Logging.AzureAppServices/5.0.8": {"dependencies": {"Microsoft.Extensions.Configuration.EnvironmentVariables": "5.0.0", "Microsoft.Extensions.Configuration.Json": "5.0.0", "Microsoft.Extensions.Logging": "5.0.0", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Logging.Configuration": "5.0.0", "System.ValueTuple": "4.5.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.AzureAppServices.dll": {"assemblyVersion": "5.0.8.0", "fileVersion": "5.0.821.31805"}}}, "Microsoft.Extensions.Logging.Configuration/5.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "5.0.0", "Microsoft.Extensions.Configuration.Abstractions": "5.0.0", "Microsoft.Extensions.Configuration.Binder": "5.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Logging": "5.0.0", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.ObjectPool/2.2.0": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.ObjectPool.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18315"}}}, "Microsoft.Extensions.Options/5.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Primitives": "5.0.0"}, "runtime": {"lib/net5.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/5.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "5.0.0", "Microsoft.Extensions.Configuration.Binder": "5.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0", "Microsoft.Extensions.Primitives": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.Primitives/5.0.0": {"runtime": {"lib/netcoreapp3.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.WebEncoders/2.2.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0", "System.Text.Encodings.Web": "5.0.1"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.WebEncoders.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.Identity.Client/4.22.0": {"runtime": {"lib/netcoreapp2.1/Microsoft.Identity.Client.dll": {"assemblyVersion": "4.22.0.0", "fileVersion": "4.22.0.0"}}}, "Microsoft.Identity.Client.Extensions.Msal/2.16.5": {"dependencies": {"Microsoft.Identity.Client": "4.22.0", "System.Security.Cryptography.ProtectedData": "4.7.0"}, "runtime": {"lib/netcoreapp2.1/Microsoft.Identity.Client.Extensions.Msal.dll": {"assemblyVersion": "2.16.5.0", "fileVersion": "2.16.5.0"}}}, "Microsoft.IdentityModel.Clients.ActiveDirectory/5.2.9": {"dependencies": {"Microsoft.CSharp": "4.7.0", "NETStandard.Library": "1.6.1", "System.ComponentModel.TypeConverter": "4.3.0", "System.Dynamic.Runtime": "4.3.0", "System.Net.Http": "4.3.4", "System.Private.Uri": "4.3.2", "System.Runtime.Serialization.Formatters": "4.3.0", "System.Runtime.Serialization.Json": "4.3.0", "System.Runtime.Serialization.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Security.SecureString": "4.3.0", "System.Xml.XDocument": "4.3.0", "System.Xml.XmlDocument": "4.3.0"}, "runtime": {"lib/netstandard1.3/Microsoft.IdentityModel.Clients.ActiveDirectory.dll": {"assemblyVersion": "5.2.9.0", "fileVersion": "5.2.9.0"}}}, "Microsoft.IdentityModel.JsonWebTokens/6.8.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "6.8.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "6.8.0.0", "fileVersion": "6.8.0.11012"}}}, "Microsoft.IdentityModel.Logging/6.8.0": {"runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "6.8.0.0", "fileVersion": "6.8.0.11012"}}}, "Microsoft.IdentityModel.Protocols/6.8.0": {"dependencies": {"Microsoft.IdentityModel.Logging": "6.8.0", "Microsoft.IdentityModel.Tokens": "6.8.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "6.8.0.0", "fileVersion": "6.8.0.11012"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.8.0": {"dependencies": {"Microsoft.IdentityModel.Protocols": "6.8.0", "System.IdentityModel.Tokens.Jwt": "6.8.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "6.8.0.0", "fileVersion": "6.8.0.11012"}}}, "Microsoft.IdentityModel.Tokens/6.8.0": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Microsoft.IdentityModel.Logging": "6.8.0", "System.Security.Cryptography.Cng": "4.5.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "6.8.0.0", "fileVersion": "6.8.0.11012"}}}, "Microsoft.Net.Http.Headers/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "5.0.0", "System.Buffers": "4.5.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Net.Http.Headers.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.NETCore.Platforms/3.1.0": {}, "Microsoft.NETCore.Targets/1.1.3": {}, "Microsoft.Win32.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.0"}}, "Microsoft.Win32.Registry/4.7.0": {"dependencies": {"System.Security.AccessControl": "4.7.0", "System.Security.Principal.Windows": "4.7.0"}}, "Microsoft.Win32.SystemEvents/4.7.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp3.0/Microsoft.Win32.SystemEvents.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "NETStandard.Library/1.6.1": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.Win32.Primitives": "4.3.0", "System.AppContext": "4.3.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Console": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.Compression.ZipFile": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Net.Http": "4.3.4", "System.Net.Primitives": "4.3.0", "System.Net.Sockets": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.InteropServices.RuntimeInformation": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Timer": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XDocument": "4.3.0"}}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.native.System/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3"}}, "runtime.native.System.IO.Compression/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3"}}, "runtime.native.System.Net.Http/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3"}}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"dependencies": {"runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "4.3.0"}}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"dependencies": {"runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "System.AppContext/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Buffers/4.5.0": {}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.0"}}, "System.Collections.Concurrent/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Collections.Immutable/5.0.0": {}, "System.Collections.NonGeneric/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Collections.Specialized/4.3.0": {"dependencies": {"System.Collections.NonGeneric": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.ComponentModel/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.ComponentModel.Annotations/5.0.0": {}, "System.ComponentModel.Primitives/4.3.0": {"dependencies": {"System.ComponentModel": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}}, "System.ComponentModel.TypeConverter/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Collections.NonGeneric": "4.3.0", "System.Collections.Specialized": "4.3.0", "System.ComponentModel": "4.3.0", "System.ComponentModel.Primitives": "4.3.0", "System.Globalization": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Configuration.ConfigurationManager/4.7.0": {"dependencies": {"System.Security.Cryptography.ProtectedData": "4.7.0", "System.Security.Permissions": "4.7.0"}, "runtime": {"lib/netstandard2.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "System.Console/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.IO": "4.3.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.0"}}, "System.Diagnostics.DiagnosticSource/5.0.1": {"runtime": {"lib/net5.0/System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.220.61120"}}}, "System.Diagnostics.Tools/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.0"}}, "System.Diagnostics.TraceSource/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Diagnostics.Tracing/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.0"}}, "System.Drawing.Common/4.7.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.Win32.SystemEvents": "4.7.0"}, "runtime": {"lib/netstandard2.0/System.Drawing.Common.dll": {"assemblyVersion": "4.0.0.1", "fileVersion": "4.6.26919.2"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp3.0/System.Drawing.Common.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}, "runtimes/win/lib/netcoreapp3.0/System.Drawing.Common.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "System.Dynamic.Runtime/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.7.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.0"}}, "System.Globalization.Calendars/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Globalization": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Globalization.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}}, "System.IdentityModel.Tokens.Jwt/6.8.0": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "6.8.0", "Microsoft.IdentityModel.Tokens": "6.8.0"}, "runtime": {"lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "6.8.0.0", "fileVersion": "6.8.0.11012"}}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.Compression/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Buffers": "4.5.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.IO.Compression": "4.3.0"}}, "System.IO.Compression.ZipFile/4.3.0": {"dependencies": {"System.Buffers": "4.5.0", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.IO.FileSystem/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem.Primitives/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Linq.Expressions/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.7.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Memory/4.5.3": {}, "System.Net.Http/4.3.4": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.DiagnosticSource": "5.0.1", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Net.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Net.Sockets/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.IO": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Numerics.Vectors/4.5.0": {}, "System.ObjectModel/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}}, "System.Private.DataContractSerialization/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Serialization.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XDocument": "4.3.0", "System.Xml.XmlDocument": "4.3.0", "System.Xml.XmlSerializer": "4.3.0"}}, "System.Private.Uri/4.3.2": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3"}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit/4.7.0": {}, "System.Reflection.Emit.ILGeneration/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.Lightweight/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Metadata/1.6.0": {}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.0"}}, "System.Reflection.TypeExtensions/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3"}}, "System.Runtime.Caching/4.7.0": {"dependencies": {"System.Configuration.ConfigurationManager": "4.7.0"}, "runtime": {"lib/netstandard2.0/System.Runtime.Caching.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.Runtime.Caching.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.0"}}, "System.Runtime.Handles/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.0"}}, "System.Runtime.InteropServices/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Runtime.Numerics/4.3.0": {"dependencies": {"System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Runtime.Serialization.Formatters/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Serialization.Primitives": "4.3.0"}}, "System.Runtime.Serialization.Json/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Private.DataContractSerialization": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Runtime.Serialization.Primitives/4.3.0": {"dependencies": {"System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Security.AccessControl/4.7.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Security.Principal.Windows": "4.7.0"}}, "System.Security.Cryptography.Algorithms/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.Apple": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.Cng/4.5.0": {}, "System.Security.Cryptography.Csp/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0"}}, "System.Security.Cryptography.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Linq": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.OpenSsl/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.Pkcs/4.5.0": {"dependencies": {"System.Security.Cryptography.Cng": "4.5.0"}, "runtime": {"lib/netcoreapp2.1/System.Security.Cryptography.Pkcs.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.1/System.Security.Cryptography.Pkcs.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}}, "System.Security.Cryptography.Primitives/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Security.Cryptography.ProtectedData/4.7.0": {"runtime": {"lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "4.0.5.0", "fileVersion": "4.700.19.56404"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "4.0.5.0", "fileVersion": "4.700.19.56404"}}}, "System.Security.Cryptography.X509Certificates/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Cng": "4.5.0", "System.Security.Cryptography.Csp": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.Xml/4.5.0": {"dependencies": {"System.Security.Cryptography.Pkcs": "4.5.0", "System.Security.Permissions": "4.7.0"}, "runtime": {"lib/netstandard2.0/System.Security.Cryptography.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}}, "System.Security.Permissions/4.7.0": {"dependencies": {"System.Security.AccessControl": "4.7.0", "System.Windows.Extensions": "4.7.0"}, "runtime": {"lib/netcoreapp3.0/System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "System.Security.Principal.Windows/4.7.0": {}, "System.Security.SecureString/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0"}}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.0"}}, "System.Text.Encoding.CodePages/4.7.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0"}}, "System.Text.Encoding.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.Text.Encodings.Web/5.0.1": {"runtime": {"lib/netcoreapp3.0/System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.421.11614"}}}, "System.Text.Json/5.0.1": {"runtime": {"lib/netcoreapp3.0/System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.220.61120"}}}, "System.Text.RegularExpressions/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.0"}}, "System.Threading.Tasks.Extensions/4.5.2": {}, "System.Threading.Timer/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.0"}}, "System.ValueTuple/4.5.0": {}, "System.Windows.Extensions/4.7.0": {"dependencies": {"System.Drawing.Common": "4.7.0"}, "runtime": {"lib/netcoreapp3.0/System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp3.0/System.Windows.Extensions.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "System.Xml.ReaderWriter/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Tasks.Extensions": "4.5.2"}}, "System.Xml.XDocument/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}}, "System.Xml.XmlDocument/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}}, "System.Xml.XmlSerializer/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.7.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XmlDocument": "4.3.0"}}, "BdoPartner.Plans.Common/1.0.0": {"dependencies": {"IdentityModel": "5.1.0", "Microsoft.AspNetCore.Authentication": "2.2.0", "Microsoft.AspNetCore.Hosting": "2.2.7", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Configuration": "5.0.0", "Microsoft.Extensions.Configuration.Binder": "5.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "5.0.0", "Microsoft.Extensions.Configuration.Json": "5.0.0", "Microsoft.Extensions.DependencyInjection": "5.0.2", "Microsoft.Extensions.Logging": "5.0.0", "Microsoft.Extensions.Logging.ApplicationInsights": "2.17.0", "Microsoft.Extensions.Logging.AzureAppServices": "5.0.8", "Microsoft.Extensions.Options": "5.0.0", "Microsoft.IdentityModel.Clients.ActiveDirectory": "5.2.9"}, "runtime": {"BdoPartner.Plans.Common.dll": {}}, "resources": {"en/BdoPartner.Plans.Common.resources.dll": {"locale": "en"}, "fr/BdoPartner.Plans.Common.resources.dll": {"locale": "fr"}}}, "BdoPartner.Plans.DataAccess.Common/1.0.0": {"dependencies": {"AutoMapper": "10.1.1", "Microsoft.Data.SqlClient": "3.0.0", "Microsoft.EntityFrameworkCore": "5.0.8", "Microsoft.EntityFrameworkCore.InMemory": "5.0.8", "Microsoft.EntityFrameworkCore.Proxies": "5.0.8", "Microsoft.EntityFrameworkCore.SqlServer": "5.0.8"}, "runtime": {"BdoPartner.Plans.DataAccess.Common.dll": {}}}, "BdoPartner.Plans.Model.DTO/1.0.0": {"dependencies": {"BdoPartner.Plans.Common": "1.0.0"}, "runtime": {"BdoPartner.Plans.Model.DTO.dll": {}}}}}, "libraries": {"BdoPartner.Plans.Business.Interface/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "AutoMapper/10.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-uMgbqOdu9ZG5cIOty0C85hzzayBH2i9BthnS5FlMqKtMSHDv4ts81a2jS1VFaDBVhlBeIqJ/kQKjQY95BZde9w==", "path": "automapper/10.1.1", "hashPath": "automapper.10.1.1.nupkg.sha512"}, "Azure.Core/1.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-kI4m2NsODPOrxo0OoKjk6B3ADbdovhDQIEmI4039upjjZKRaewVLx/Uz4DfRa/NtnIRZQPUALe1yvdHWAoRt4w==", "path": "azure.core/1.6.0", "hashPath": "azure.core.1.6.0.nupkg.sha512"}, "Azure.Identity/1.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-l1SYfZKOFBuUFG7C2SWHmJcrQQaiXgBdVCycx4vcZQkC6efDVt7mzZ5pfJAFEJDBUq7mjRQ0RPq9ZDGdSswqMg==", "path": "azure.identity/1.3.0", "hashPath": "azure.identity.1.3.0.nupkg.sha512"}, "Castle.Core/4.4.1": {"type": "package", "serviceable": true, "sha512": "sha512-zanbjWC0Y05gbx4eGXkzVycOQqVOFVeCjVsDSyuao9P4mtN1w3WxxTo193NGC7j3o2u3AJRswaoC6hEbnGACnQ==", "path": "castle.core/4.4.1", "hashPath": "castle.core.4.4.1.nupkg.sha512"}, "IdentityModel/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-Gcv4YYXncvUvTNLF8lDJyItZzaEDFcEi3geMDtylGcl/Rx90vcV7Kv3yAofktJBW7GvmIjQgC8LtUq0s9XQT2w==", "path": "identitymodel/5.1.0", "hashPath": "identitymodel.5.1.0.nupkg.sha512"}, "Microsoft.ApplicationInsights/2.17.0": {"type": "package", "serviceable": true, "sha512": "sha512-moAOrjhwiCWdg8I4fXPEd+bnnyCSRxo6wmYQ0HuNrWJUctzZEiyVTbJ8QTS6+dBOFTxpI6x+OY5wHPHrgWOk1Q==", "path": "microsoft.applicationinsights/2.17.0", "hashPath": "microsoft.applicationinsights.2.17.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-b0R9X7L6zMqNsssKDvhYHuNi5x0s4DyHTeXybIAyGaitKiW1Q5aAGKdV2codHPiePv9yHfC9hAMyScXQ/xXhPw==", "path": "microsoft.aspnetcore.authentication/2.2.0", "hashPath": "microsoft.aspnetcore.authentication.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-VloMLDJMf3n/9ic5lCBOa42IBYJgyB1JhzLsL68Zqg+2bEPWfGBj/xCJy/LrKTArN0coOcZp3wyVTZlx0y9pHQ==", "path": "microsoft.aspnetcore.authentication.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.authentication.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.Core/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-XlVJzJ5wPOYW+Y0J6Q/LVTEyfS4ssLXmt60T0SPP+D8abVhBTl+cgw2gDHlyKYIkcJg7btMVh383NDkMVqD/fg==", "path": "microsoft.aspnetcore.authentication.core/2.2.0", "hashPath": "microsoft.aspnetcore.authentication.core.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Cryptography.Internal/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-GXmMD8/vuTLPLvKzKEPz/4vapC5e0cwx1tUVd83ePRyWF9CCrn/pg4/1I+tGkQqFLPvi3nlI2QtPtC6MQN8Nww==", "path": "microsoft.aspnetcore.cryptography.internal/2.2.0", "hashPath": "microsoft.aspnetcore.cryptography.internal.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.DataProtection/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-G6dvu5Nd2vjpYbzazZ//qBFbSEf2wmBUbyAR7E4AwO3gWjhoJD5YxpThcGJb7oE3VUcW65SVMXT+cPCiiBg8Sg==", "path": "microsoft.aspnetcore.dataprotection/2.2.0", "hashPath": "microsoft.aspnetcore.dataprotection.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.DataProtection.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-seANFXmp8mb5Y12m1ShiElJ3ZdOT3mBN3wA1GPhHJIvZ/BxOCPyqEOR+810OWsxEZwA5r5fDRNpG/CqiJmQnJg==", "path": "microsoft.aspnetcore.dataprotection.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.dataprotection.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting/2.2.7": {"type": "package", "serviceable": true, "sha512": "sha512-O0ZBE53Fa9bVGXykDzvgMFW0Pe1QyPPFg1pazN8l3RUFSWBsDJ9/iD1LHXgADA8+ZD3R/1zkvraPa9SZdievxQ==", "path": "microsoft.aspnetcore.hosting/2.2.7", "hashPath": "microsoft.aspnetcore.hosting.2.2.7.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ubycklv+ZY7Kutdwuy1W4upWcZ6VFR8WUXU7l7B2+mvbDBBPAcfpi+E+Y5GFe+Q157YfA3C49D2GCjAZc7Mobw==", "path": "microsoft.aspnetcore.hosting.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.hosting.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-1PMijw8RMtuQF60SsD/JlKtVfvh4NORAhF4wjysdABhlhTrYmtgssqyncR0Stq5vqtjplZcj6kbT4LRTglt9IQ==", "path": "microsoft.aspnetcore.hosting.server.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.hosting.server.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-YogBSMotWPAS/X5967pZ+yyWPQkThxhmzAwyCHCSSldzYBkW5W5d6oPfBaPqQOnSHYTpSOSOkpZoAce0vwb6+A==", "path": "microsoft.aspnetcore.http/2.2.0", "hashPath": "microsoft.aspnetcore.http.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nxs7Z1q3f1STfLYKJSVXCs1iBl+Ya6E8o4Oy1bCxJ/rNI44E/0f6tbsrVqAWfB7jlnJfyaAtIalBVxPKUPQb4Q==", "path": "microsoft.aspnetcore.http.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.http.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Extensions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-2DgZ9rWrJtuR7RYiew01nGRzuQBDaGHGmK56Rk54vsLLsCdzuFUPqbDTJCS1qJQWTbmbIQ9wGIOjpxA1t0l7/w==", "path": "microsoft.aspnetcore.http.extensions/2.2.0", "hashPath": "microsoft.aspnetcore.http.extensions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ziFz5zH8f33En4dX81LW84I6XrYXKf9jg6aM39cM+LffN9KJahViKZ61dGMSO2gd3e+qe5yBRwsesvyqlZaSMg==", "path": "microsoft.aspnetcore.http.features/2.2.0", "hashPath": "microsoft.aspnetcore.http.features.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.WebUtilities/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-9ErxAAKaDzxXASB/b5uLEkLgUWv1QbeVxyJYEHQwMaxXOeFFVkQxiq8RyfVcifLU7NR0QY0p3acqx4ZpYfhHDg==", "path": "microsoft.aspnetcore.webutilities/2.2.0", "hashPath": "microsoft.aspnetcore.webutilities.2.2.0.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-K63Y4hORbBcKLWH5wnKgzyn7TOfYzevIEwIedQHBIkmkEBA9SCqgvom+XTuE+fAFGvINGkhFItaZ2dvMGdT5iw==", "path": "microsoft.bcl.asyncinterfaces/1.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.1.0.0.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.Data.SqlClient/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MUauWfCLsZQQMUR/wZhec5MH6+NTPmPp9i/OsjIMmIu2ICYUGOVm1x7RTqKxq19UWxXMSG03/O0FyXQJrpDs9A==", "path": "microsoft.data.sqlclient/3.0.0", "hashPath": "microsoft.data.sqlclient.3.0.0.nupkg.sha512"}, "Microsoft.Data.SqlClient.SNI.runtime/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-n1sNyjJgu2pYWKgw3ZPikw3NiRvG4kt7Ya5MK8u77Rgj/1bTFqO/eDF4k5W9H5GXplMZCpKkNbp5kNBICgSB0w==", "path": "microsoft.data.sqlclient.sni.runtime/3.0.0", "hashPath": "microsoft.data.sqlclient.sni.runtime.3.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/5.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-UzHZDWma5jJaDMBGnfsBC1h0+1bPzAZmEGnmEMy9xdn4UkO9yvrG7m0deRazvU98Ezoa9pqi4Bsp07WR8p8HUA==", "path": "microsoft.entityframeworkcore/5.0.8", "hashPath": "microsoft.entityframeworkcore.5.0.8.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/5.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-twdaHC6Jy3Gzcr7qHBvgQi0q1mWJVJtrqkFcw21IFE/+1KkMTGOamV88vgPZY1NBZqnbdv1K83otDsUg9EO5ow==", "path": "microsoft.entityframeworkcore.abstractions/5.0.8", "hashPath": "microsoft.entityframeworkcore.abstractions.5.0.8.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/5.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-iXH3FyGochg9ZePG1VQwVT3YGsvhn8jmQkhi7iIKfa16xgSevpa9jhKDflFSMZ9xI9xWAsOAiG+H+pG6Jsw/3w==", "path": "microsoft.entityframeworkcore.analyzers/5.0.8", "hashPath": "microsoft.entityframeworkcore.analyzers.5.0.8.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.InMemory/5.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-LV9/hBseBO3kZKa/MPlWfLvfKL64Q7y2ZW2euW+kcNhhAQWsl52JIQHSL1Gu3CnzfQVbGUDkusnN4OWh6k/ALw==", "path": "microsoft.entityframeworkcore.inmemory/5.0.8", "hashPath": "microsoft.entityframeworkcore.inmemory.5.0.8.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Proxies/5.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-I4te83hQbgRVbG/0gyo2kpr93cJeGZTZTmg2z4PaAKDpFGlWpSUU9OKPRZcKggN4+15aQLF8oLZ8rKuThbJrLA==", "path": "microsoft.entityframeworkcore.proxies/5.0.8", "hashPath": "microsoft.entityframeworkcore.proxies.5.0.8.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/5.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-TGXfQdij08bjxpXNzC/LxOf1Q18eLdACZZpWUCHcVKssk6750ElE0MhX3eBmvRnSof1FrjsWJkYEchV6PSjGOg==", "path": "microsoft.entityframeworkcore.relational/5.0.8", "hashPath": "microsoft.entityframeworkcore.relational.5.0.8.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.SqlServer/5.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-kQbUHiJDqIm+LdY7SS/fm/W/eHCXUJjAJhMvTzSGtPuFGBN4S+RKfs8FiXXk0WKWxmDjXo4Dck4WDJLCXdv/Sw==", "path": "microsoft.entityframeworkcore.sqlserver/5.0.8", "hashPath": "microsoft.entityframeworkcore.sqlserver.5.0.8.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bu8As90/SBAouMZ6fJ+qRNo1X+KgHGrVueFhhYi+E5WqEhcnp2HoWRFnMzXQ6g4RdZbvPowFerSbKNH4Dtg5yg==", "path": "microsoft.extensions.caching.abstractions/5.0.0", "hashPath": "microsoft.extensions.caching.abstractions.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/1qPCleFOkJe0O+xmFqCNLFYQZTJz965sVw8CUB/BQgsApBwzAUsL2BUkDvQW+geRUVTXUS9zLa0pBjC2VJ1gA==", "path": "microsoft.extensions.caching.memory/5.0.0", "hashPath": "microsoft.extensions.caching.memory.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-LN322qEKHjuVEhhXueTUe7RNePooZmS8aGid5aK2woX3NPjSnONFyKUc6+JknOS6ce6h2tCLfKPTBXE3mN/6Ag==", "path": "microsoft.extensions.configuration/5.0.0", "hashPath": "microsoft.extensions.configuration.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ETjSBHMp3OAZ4HxGQYpwyGsD8Sw5FegQXphi0rpoGMT74S4+I2mm7XJEswwn59XAaKOzC15oDSOWEE8SzDCd6Q==", "path": "microsoft.extensions.configuration.abstractions/5.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Of1Irt1+NzWO+yEYkuDh5TpT4On7LKl98Q9iLqCdOZps6XXEWDj3AKtmyvzJPVXZe4apmkJJIiDL7rR1yC+hjQ==", "path": "microsoft.extensions.configuration.binder/5.0.0", "hashPath": "microsoft.extensions.configuration.binder.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fqh6y6hAi0Z0fRsb4B/mP9OkKkSlifh5osa+N/YSQ+/S2a//+zYApZMUC1XeP9fdjlgZoPQoZ72Q2eLHyKLddQ==", "path": "microsoft.extensions.configuration.environmentvariables/5.0.0", "hashPath": "microsoft.extensions.configuration.environmentvariables.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rRdspYKA18ViPOISwAihhCMbusHsARCOtDMwa23f+BGEdIjpKPlhs3LLjmKlxfhpGXBjIsS0JpXcChjRUN+PAw==", "path": "microsoft.extensions.configuration.fileextensions/5.0.0", "hashPath": "microsoft.extensions.configuration.fileextensions.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Pak8ymSUfdzPfBTLHxeOwcR32YDbuVfhnH2hkfOLnJNQd19ItlBdpMjIDY9C5O/nS2Sn9bzDMai0ZrvF7KyY/Q==", "path": "microsoft.extensions.configuration.json/5.0.0", "hashPath": "microsoft.extensions.configuration.json.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/5.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-xzFW00AZEvOXM1OX+0+AYH5op/Hf3u//e6wszBd/rK72sypD+jx5CtsHxM4BVuFBEs8SajfO4QzSJtrQaHDr4A==", "path": "microsoft.extensions.dependencyinjection/5.0.2", "hashPath": "microsoft.extensions.dependencyinjection.5.0.2.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ORj7Zh81gC69TyvmcUm9tSzytcy8AVousi+IVRAI8nLieQjOFryRusSFh7+aLk16FN9pQNqJAiMd7BTKINK0kA==", "path": "microsoft.extensions.dependencyinjection.abstractions/5.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-iuZIiZ3mteEb+nsUqpGXKx2cGF+cv6gWPd5jqQI4hzqdiJ6I94ddLjKhQOuRW1lueHwocIw30xbSHGhQj0zjdQ==", "path": "microsoft.extensions.fileproviders.abstractions/5.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-1rkd8UO2qf21biwO7X0hL9uHP7vtfmdv/NLvKgCRHkdz1XnW8zVQJXyEYiN68WYpExgtVWn55QF0qBzgfh1mGg==", "path": "microsoft.extensions.fileproviders.physical/5.0.0", "hashPath": "microsoft.extensions.fileproviders.physical.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ArliS8lGk8sWRtrWpqI8yUVYJpRruPjCDT+EIjrgkA/AAPRctlAkRISVZ334chAKktTLzD1+PK8F5IZpGedSqA==", "path": "microsoft.extensions.filesystemglobbing/5.0.0", "hashPath": "microsoft.extensions.filesystemglobbing.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-+k4AEn68HOJat5gj1TWa6X28WlirNQO9sPIIeQbia+91n03esEtMSSoekSTpMjUzjqtJWQN3McVx0GvSPFHF/Q==", "path": "microsoft.extensions.hosting.abstractions/2.2.0", "hashPath": "microsoft.extensions.hosting.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MgOwK6tPzB6YNH21wssJcw/2MKwee8b2gI7SllYfn6rvTpIrVvVS5HAjSU2vqSku1fwqRvWP0MdIi14qjd93Aw==", "path": "microsoft.extensions.logging/5.0.0", "hashPath": "microsoft.extensions.logging.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NxP6ahFcBnnSfwNBi2KH2Oz8Xl5Sm2krjId/jRR3I7teFphwiUoUeZPwTNA21EX+5PtjqmyAvKaOeBXcJjcH/w==", "path": "microsoft.extensions.logging.abstractions/5.0.0", "hashPath": "microsoft.extensions.logging.abstractions.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.ApplicationInsights/2.17.0": {"type": "package", "serviceable": true, "sha512": "sha512-no6gCIpGCdTtdrvcXoL2tU1BaiG+Z+VM73feCp1hfNIBQ0lDrblxCWS3ReLBgj/WhofUQHpl3qwMXtINaiUycg==", "path": "microsoft.extensions.logging.applicationinsights/2.17.0", "hashPath": "microsoft.extensions.logging.applicationinsights.2.17.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.AzureAppServices/5.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-1UBubx6t6F4VhiUVO8dp9QI/IWLm8euf0fkOCopgO5kMQpA4tBdjATjF2S37w3/QCsGDUbcVmBXRmCJnpJNvbg==", "path": "microsoft.extensions.logging.azureappservices/5.0.8", "hashPath": "microsoft.extensions.logging.azureappservices.5.0.8.nupkg.sha512"}, "Microsoft.Extensions.Logging.Configuration/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-N3/d0HeMRnBekadbZlmbp+In8EvNNkQHSdbtRzjrGVckdZWpYs5GNrAfaYqVplDFW0WUedSaFJ3khB50BWYGsw==", "path": "microsoft.extensions.logging.configuration/5.0.0", "hashPath": "microsoft.extensions.logging.configuration.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.ObjectPool/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-gA8H7uQOnM5gb+L0uTNjViHYr+hRDqCdfugheGo/MxQnuHzmhhzCBTIPm19qL1z1Xe0NEMabfcOBGv9QghlZ8g==", "path": "microsoft.extensions.objectpool/2.2.0", "hashPath": "microsoft.extensions.objectpool.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Options/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-CBvR92TCJ5uBIdd9/HzDSrxYak+0W/3+yxrNg8Qm6Bmrkh5L+nu6m3WeazQehcZ5q1/6dDA7J5YdQjim0165zg==", "path": "microsoft.extensions.options/5.0.0", "hashPath": "microsoft.extensions.options.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-280RxNJqOeQqq47aJLy5D9LN61CAWeuRA83gPToQ8B9jl9SNdQ5EXjlfvF66zQI5AXMl+C/3hGnbtIEN+X3mqA==", "path": "microsoft.extensions.options.configurationextensions/5.0.0", "hashPath": "microsoft.extensions.options.configurationextensions.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cI/VWn9G1fghXrNDagX9nYaaB/nokkZn0HYAawGaELQrl8InSezfe9OnfPZLcJq3esXxygh3hkq2c3qoV3SDyQ==", "path": "microsoft.extensions.primitives/5.0.0", "hashPath": "microsoft.extensions.primitives.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.WebEncoders/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-V8XcqYcpcdBAxUhLeyYcuKmxu4CtNQA9IphTnARpQGhkop4A93v2XgM3AtaVVJo3H2cDWxWM6aeO8HxkifREqw==", "path": "microsoft.extensions.webencoders/2.2.0", "hashPath": "microsoft.extensions.webencoders.2.2.0.nupkg.sha512"}, "Microsoft.Identity.Client/4.22.0": {"type": "package", "serviceable": true, "sha512": "sha512-GlamU9rs8cSVIx9WSGv5QKpt66KkE+ImxNa/wNZZUJ3knt3PM98T9sOY8B7NcEfhw7NoxU2/0TSOcmnRSJQgqw==", "path": "microsoft.identity.client/4.22.0", "hashPath": "microsoft.identity.client.4.22.0.nupkg.sha512"}, "Microsoft.Identity.Client.Extensions.Msal/2.16.5": {"type": "package", "serviceable": true, "sha512": "sha512-VlGUZEpF8KP/GCfFI59sdE0WA0o9quqwM1YQY0dSp6jpGy5EOBkureaybLfpwCuYUUjQbLkN2p7neUIcQCfbzA==", "path": "microsoft.identity.client.extensions.msal/2.16.5", "hashPath": "microsoft.identity.client.extensions.msal.2.16.5.nupkg.sha512"}, "Microsoft.IdentityModel.Clients.ActiveDirectory/5.2.9": {"type": "package", "serviceable": true, "sha512": "sha512-WhBAG/9hWiMHIXve4ZgwXP3spRwf7kFFfejf76QA5BvumgnPp8iDkDCiJugzAcpW1YaHB526z1UVxHhVT1E5qw==", "path": "microsoft.identitymodel.clients.activedirectory/5.2.9", "hashPath": "microsoft.identitymodel.clients.activedirectory.5.2.9.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/6.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-+7JIww64PkMt7NWFxoe4Y/joeF7TAtA/fQ0b2GFGcagzB59sKkTt/sMZWR6aSZht5YC7SdHi3W6yM1yylRGJCQ==", "path": "microsoft.identitymodel.jsonwebtokens/6.8.0", "hashPath": "microsoft.identitymodel.jsonwebtokens.6.8.0.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/6.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-Rfh/p4MaN4gkmhPxwbu8IjrmoDncGfHHPh1sTnc0AcM/Oc39/fzC9doKNWvUAjzFb8LqA6lgZyblTrIsX/wDXg==", "path": "microsoft.identitymodel.logging/6.8.0", "hashPath": "microsoft.identitymodel.logging.6.8.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/6.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-OJZx5nPdiH+MEkwCkbJrTAUiO/YzLe0VSswNlDxJsJD9bhOIdXHufh650pfm59YH1DNevp3/bXzukKrG57gA1w==", "path": "microsoft.identitymodel.protocols/6.8.0", "hashPath": "microsoft.identitymodel.protocols.6.8.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-X/PiV5l3nYYsodtrNMrNQIVlDmHpjQQ5w48E+o/D5H4es2+4niEyQf3l03chvZGWNzBRhfSstaXr25/Ye4AeYw==", "path": "microsoft.identitymodel.protocols.openidconnect/6.8.0", "hashPath": "microsoft.identitymodel.protocols.openidconnect.6.8.0.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/6.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-gTqzsGcmD13HgtNePPcuVHZ/NXWmyV+InJgalW/FhWpII1D7V1k0obIseGlWMeA4G+tZfeGMfXr0klnWbMR/mQ==", "path": "microsoft.identitymodel.tokens/6.8.0", "hashPath": "microsoft.identitymodel.tokens.6.8.0.nupkg.sha512"}, "Microsoft.Net.Http.Headers/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-iZNkjYqlo8sIOI0bQfpsSoMTmB/kyvmV2h225ihyZT33aTp48ZpF6qYnXxzSXmHt8DpBAwBTX+1s1UFLbYfZKg==", "path": "microsoft.net.http.headers/2.2.0", "hashPath": "microsoft.net.http.headers.2.2.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-z7aeg8oHln2CuNulfhiLYxCVMPEwBl3rzicjvIX+4sUuCwvXw5oXQEtbiU2c0z4qYL5L3Kmx0mMA/+t/SbY67w==", "path": "microsoft.netcore.platforms/3.1.0", "hashPath": "microsoft.netcore.platforms.3.1.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-3Wrmi0kJDzClwAC+iBdUBpEKmEle8FQNsCs77fkiOIw/9oYA07bL1EZNX0kQ2OMN3xpwvl0vAtOCYY3ndDNlhQ==", "path": "microsoft.netcore.targets/1.1.3", "hashPath": "microsoft.netcore.targets.1.1.3.nupkg.sha512"}, "Microsoft.Win32.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-9ZQKCWxH7Ijp9BfahvL2Zyf1cJIk8XYLF6Yjzr2yi0b2cOut/HQ31qf1ThHAgCc3WiZMdnWcfJCgN82/0UunxA==", "path": "microsoft.win32.primitives/4.3.0", "hashPath": "microsoft.win32.primitives.4.3.0.nupkg.sha512"}, "Microsoft.Win32.Registry/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-KSrRMb5vNi0CWSGG1++id2ZOs/1QhRqROt+qgbEAdQuGjGrFcl4AOl4/exGPUYz2wUnU42nvJqon1T3U0kPXLA==", "path": "microsoft.win32.registry/4.7.0", "hashPath": "microsoft.win32.registry.4.7.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-mtVirZr++rq+XCDITMUdnETD59XoeMxSpLRIII7JRI6Yj0LEDiO1pPn0ktlnIj12Ix8bfvQqQDMMIF9wC98oCA==", "path": "microsoft.win32.systemevents/4.7.0", "hashPath": "microsoft.win32.systemevents.4.7.0.nupkg.sha512"}, "NETStandard.Library/1.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-WcSp3+vP+yHNgS8EV5J7pZ9IRpeDuARBPN28by8zqff1wJQXm26PVU8L3/fYLBJVU7BtDyqNVWq2KlCVvSSR4A==", "path": "netstandard.library/1.6.1", "hashPath": "netstandard.library.1.6.1.nupkg.sha512"}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-7VSGO0URRKoMEAq0Sc9cRz8mb6zbyx/BZDEWhgPdzzpmFhkam3fJ1DAGWFXBI4nGlma+uPKpfuMQP5LXRnOH5g==", "path": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-0oAaTAm6e2oVH+/Zttt0cuhGaePQYKII1dY8iaqP7CvOpVKgLybKRFvQjXR2LtxXOXTVPNv14j0ot8uV+HrUmw==", "path": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-G24ibsCNi5Kbz0oXWynBoRgtGvsw5ZSVEWjv13/KiCAM8C6wz9zzcCniMeQFIkJ2tasjo2kXlvlBZhplL51kGg==", "path": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.native.System/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c/qWt2LieNZIj1jGnVNsE2Kl23Ya2aSTBuXMD6V7k9KWr6l16Tqdwq+hJScEpWER9753NWC8h96PaVNY5Ld7Jw==", "path": "runtime.native.system/4.3.0", "hashPath": "runtime.native.system.4.3.0.nupkg.sha512"}, "runtime.native.System.IO.Compression/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-INBPonS5QPEgn7naufQFXJEp3zX6L4bwHgJ/ZH78aBTpeNfQMtf7C6VrAFhlq2xxWBveIOWyFzQjJ8XzHMhdOQ==", "path": "runtime.native.system.io.compression/4.3.0", "hashPath": "runtime.native.system.io.compression.4.3.0.nupkg.sha512"}, "runtime.native.System.Net.Http/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZVuZJqnnegJhd2k/PtAbbIcZ3aZeITq3sj06oKfMBSfphW3HDmk/t4ObvbOk/JA/swGR0LNqMksAh/f7gpTROg==", "path": "runtime.native.system.net.http/4.3.0", "hashPath": "runtime.native.system.net.http.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DloMk88juo0OuOWr56QG7MNchmafTLYWvABy36izkrLI5VledI0rq28KGs1i9wbpeT9NPQrx/wTf8U2vazqQ3Q==", "path": "runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-QR1OwtwehHxSeQvZKXe+iSd+d3XZNkEcuWMFYa2i0aG1l+lR739HPicKMlTbJst3spmeekDVBUS7SeS26s4U/g==", "path": "runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-I+GNKGg2xCHueRd1m9PzeEW7WLbNNLznmTuEi8/vZX71HudUbx1UTwlGkiwMri7JLl8hGaIAWnA/GONhu+LOyQ==", "path": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-1Z3TAq1ytS1IBRtPXJvEUZdVsfWfeNEhBkbiOCGEl9wwAfsjP2lz3ZFDx5tq8p60/EqbS0HItG5piHuB71RjoA==", "path": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kVXCuMTrTlxq4XOOMAysuNwsXWpYeboGddNGpIgNSZmv1b6r/s/DPk0fYMB7Q5Qo4bY68o48jt4T4y5BVecbCQ==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-6mU/cVmmHtQiDXhnzUImxIcDL48GbTk+TsptXyJA+MIOG9LRjPoAQC/qBFB7X+UNyK86bmvGwC8t+M66wsYC8w==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-vjwG0GGcTW/PPg6KVud8F9GLWYuAV1rrw1BKAqY0oh4jcUqg15oYF1+qkGR2x2ZHM4DQnWKQ7cJgYbfncz/lYg==", "path": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-7KMFpTkHC/zoExs+PwP8jDCWcrK9H6L7soowT80CUx3e+nxP/AFnq0AQAW5W76z2WYbLAYCRyPfwYFG6zkvQRw==", "path": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-xrlmRCnKZJLHxyyLIqkZjNXqgxnKdZxfItrPkjI+6pkRo5lHX8YvSZlWrSI5AVwLMi4HbNWP7064hcAWeZKp5w==", "path": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-leXiwfiIkW7Gmn7cgnNcdtNAU70SjmKW3jxGj1iKHOvdn0zRWsgv/l2OJUO5zdGdiv2VRFnAsxxhDgMzofPdWg==", "path": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "System.AppContext/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-fKC+rmaLfeIzUhagxY17Q9siv/sPrjjKcfNg1Ic8IlQkZLipo8ljcaZQu4VtI4Jqbzjc2VTjzGLF6WmsRXAEgA==", "path": "system.appcontext/4.3.0", "hashPath": "system.appcontext.4.3.0.nupkg.sha512"}, "System.Buffers/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-pL2ChpaRRWI/p4LXyy4RgeWlYF2sgfj/pnVMvBqwNFr5cXg7CXNnWZWxrOONLg8VGdFB8oB+EG2Qw4MLgTOe+A==", "path": "system.buffers/4.5.0", "hashPath": "system.buffers.4.5.0.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Collections.Concurrent/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ztl69Xp0Y/UXCL+3v3tEU+lIy+bvjKNUmopn1wep/a291pVPK7dxBd6T7WnlQqRog+d1a/hSsgRsmFnIBKTPLQ==", "path": "system.collections.concurrent/4.3.0", "hashPath": "system.collections.concurrent.4.3.0.nupkg.sha512"}, "System.Collections.Immutable/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FXkLXiK0sVVewcso0imKQoOxjoPAj42R8HtjjbSjVPAzwDfzoyoznWxgA3c38LDbN9SJux1xXoXYAhz98j7r2g==", "path": "system.collections.immutable/5.0.0", "hashPath": "system.collections.immutable.5.0.0.nupkg.sha512"}, "System.Collections.NonGeneric/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-prtjIEMhGUnQq6RnPEYLpFt8AtLbp9yq2zxOSrY7KJJZrw25Fi97IzBqY7iqssbM61Ek5b8f3MG/sG1N2sN5KA==", "path": "system.collections.nongeneric/4.3.0", "hashPath": "system.collections.nongeneric.4.3.0.nupkg.sha512"}, "System.Collections.Specialized/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Epx8PoVZR0iuOnJJDzp7pWvdfMMOAvpUo95pC4ScH2mJuXkKA2Y4aR3cG9qt2klHgSons1WFh4kcGW7cSXvrxg==", "path": "system.collections.specialized/4.3.0", "hashPath": "system.collections.specialized.4.3.0.nupkg.sha512"}, "System.ComponentModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VyGn1jGRZVfxnh8EdvDCi71v3bMXrsu8aYJOwoV7SNDLVhiEqwP86pPMyRGsDsxhXAm2b3o9OIqeETfN5qfezw==", "path": "system.componentmodel/4.3.0", "hashPath": "system.componentmodel.4.3.0.nupkg.sha512"}, "System.ComponentModel.Annotations/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dMkqfy2el8A8/I76n2Hi1oBFEbG1SfxD2l5nhwXV3XjlnOmwxJlQbYpJH4W51odnU9sARCSAgv7S3CyAFMkpYg==", "path": "system.componentmodel.annotations/5.0.0", "hashPath": "system.componentmodel.annotations.5.0.0.nupkg.sha512"}, "System.ComponentModel.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-j8GUkCpM8V4d4vhLIIoBLGey2Z5bCkMVNjEZseyAlm4n5arcsJOeI3zkUP+zvZgzsbLTYh4lYeP/ZD/gdIAPrw==", "path": "system.componentmodel.primitives/4.3.0", "hashPath": "system.componentmodel.primitives.4.3.0.nupkg.sha512"}, "System.ComponentModel.TypeConverter/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-16pQ6P+EdhcXzPiEK4kbA953Fu0MNG2ovxTZU81/qsCd1zPRsKc3uif5NgvllCY598k6bI0KUyKW8fanlfaDQg==", "path": "system.componentmodel.typeconverter/4.3.0", "hashPath": "system.componentmodel.typeconverter.4.3.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-/anOTeSZCNNI2zDilogWrZ8pNqCmYbzGNexUnNhjW8k0sHqEZ2nHJBp147jBV3hGYswu5lINpNg1vxR7bnqvVA==", "path": "system.configuration.configurationmanager/4.7.0", "hashPath": "system.configuration.configurationmanager.4.7.0.nupkg.sha512"}, "System.Console/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DHDrIxiqk1h03m6khKWV2X8p/uvN79rgSqpilL6uzpmSfxfU5ng8VcPtW4qsDsQDHiTv6IPV9TmD5M/vElPNLg==", "path": "system.console/4.3.0", "hashPath": "system.console.4.3.0.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-uXQEYqav2V3zP6OwkOKtLv+qIi6z3m1hsGyKwXX7ZA7htT4shoVccGxnJ9kVRFPNAsi1ArZTq2oh7WOto6GbkQ==", "path": "system.diagnostics.diagnosticsource/5.0.1", "hashPath": "system.diagnostics.diagnosticsource.5.0.1.nupkg.sha512"}, "System.Diagnostics.Tools/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-UUvkJfSYJMM6x527dJg2VyWPSRqIVB0Z7dbjHst1zmwTXz5CcXSYJFWRpuigfbO1Lf7yfZiIaEUesfnl/g5EyA==", "path": "system.diagnostics.tools/4.3.0", "hashPath": "system.diagnostics.tools.4.3.0.nupkg.sha512"}, "System.Diagnostics.TraceSource/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VnYp1NxGx8Ww731y2LJ1vpfb/DKVNKEZ8Jsh5SgQTZREL/YpWRArgh9pI8CDLmgHspZmLL697CaLvH85qQpRiw==", "path": "system.diagnostics.tracesource/4.3.0", "hashPath": "system.diagnostics.tracesource.4.3.0.nupkg.sha512"}, "System.Diagnostics.Tracing/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rswfv0f/Cqkh78rA5S8eN8Neocz234+emGCtTF3lxPY96F+mmmUen6tbn0glN6PMvlKQb9bPAY5e9u7fgPTkKw==", "path": "system.diagnostics.tracing/4.3.0", "hashPath": "system.diagnostics.tracing.4.3.0.nupkg.sha512"}, "System.Drawing.Common/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-v+XbyYHaZjDfn0ENmJEV1VYLgGgCTx1gnfOBcppowbpOAriglYgGCvFCPr2EEZyBvXlpxbEsTwkOlInl107ahA==", "path": "system.drawing.common/4.7.0", "hashPath": "system.drawing.common.4.7.0.nupkg.sha512"}, "System.Dynamic.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-SNVi1E/vfWUAs/WYKhE9+qlS6KqK0YVhnlT0HQtr8pMIA8YX3lwy3uPMownDwdYISBdmAF/2holEIldVp85Wag==", "path": "system.dynamic.runtime/4.3.0", "hashPath": "system.dynamic.runtime.4.3.0.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.Globalization.Calendars/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GUlBtdOWT4LTV3I+9/PJW+56AnnChTaOqqTLFtdmype/L500M2LIyXgmtd9X2P2VOkmJd5c67H5SaC2QcL1bFA==", "path": "system.globalization.calendars/4.3.0", "hashPath": "system.globalization.calendars.4.3.0.nupkg.sha512"}, "System.Globalization.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-FhKmdR6MPG+pxow6wGtNAWdZh7noIOpdD5TwQ3CprzgIE1bBBoim0vbR1+AWsWjQmU7zXHgQo4TWSP6lCeiWcQ==", "path": "system.globalization.extensions/4.3.0", "hashPath": "system.globalization.extensions.4.3.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/6.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-5tBCjAub2Bhd5qmcd0WhR5s354e4oLYa//kOWrkX+6/7ZbDDJjMTfwLSOiZ/MMpWdE4DWPLOfTLOq/juj9CKzA==", "path": "system.identitymodel.tokens.jwt/6.8.0", "hashPath": "system.identitymodel.tokens.jwt.6.8.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.Compression/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YHndyoiV90iu4iKG115ibkhrG+S3jBm8Ap9OwoUAzO5oPDAWcr0SFwQFm0HjM8WkEZWo0zvLTyLmbvTkW1bXgg==", "path": "system.io.compression/4.3.0", "hashPath": "system.io.compression.4.3.0.nupkg.sha512"}, "System.IO.Compression.ZipFile/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-G4HwjEsgIwy3JFBduZ9quBkAu+eUwjIdJleuNSgmUojbH6O3mlvEIme+GHx/cLlTAPcrnnL7GqvB9pTlWRfhOg==", "path": "system.io.compression.zipfile/4.3.0", "hashPath": "system.io.compression.zipfile.4.3.0.nupkg.sha512"}, "System.IO.FileSystem/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3wEMARTnuio+ulnvi+hkRNROYwa1kylvYahhcLk4HSoVdl+xxTFVeVlYOfLwrDPImGls0mDqbMhrza8qnWPTdA==", "path": "system.io.filesystem/4.3.0", "hashPath": "system.io.filesystem.4.3.0.nupkg.sha512"}, "System.IO.FileSystem.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-6QOb2XFLch7bEc4lIcJH49nJN2HV+OC3fHDgsLVsBVBk3Y4hFAnOBGzJ2lUu7CyDDFo9IBWkSsnbkT6IBwwiMw==", "path": "system.io.filesystem.primitives/4.3.0", "hashPath": "system.io.filesystem.primitives.4.3.0.nupkg.sha512"}, "System.Linq/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "path": "system.linq/4.3.0", "hashPath": "system.linq.4.3.0.nupkg.sha512"}, "System.Linq.Expressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-PGKkrd2khG4CnlyJwxwwaWWiSiWFNBGlgXvJpeO0xCXrZ89ODrQ6tjEWS/kOqZ8GwEOUATtKtzp1eRgmYNfclg==", "path": "system.linq.expressions/4.3.0", "hashPath": "system.linq.expressions.4.3.0.nupkg.sha512"}, "System.Memory/4.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-3oDzvc/zzetpTKWMShs1AADwZjQ/36HnsufHRPcOjyRAAMLDlu2iD33MBI2opxnezcVUtXyqDXXjoFMOU9c7SA==", "path": "system.memory/4.5.3", "hashPath": "system.memory.4.5.3.nupkg.sha512"}, "System.Net.Http/4.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-aOa2d51SEbmM+H+Csw7yJOuNZoHkrP2XnAurye5HWYgGVVU54YZDvsLUYRv6h18X3sPnjNCANmN7ZhIPiqMcjA==", "path": "system.net.http/4.3.4", "hashPath": "system.net.http.4.3.4.nupkg.sha512"}, "System.Net.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-qOu+hDwFwoZPbzPvwut2qATe3ygjeQBDQj91xlsaqGFQUI5i4ZnZb8yyQuLGpDGivEPIt8EJkd1BVzVoP31FXA==", "path": "system.net.primitives/4.3.0", "hashPath": "system.net.primitives.4.3.0.nupkg.sha512"}, "System.Net.Sockets/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-m6icV6TqQOAdgt5N/9I5KNpjom/5NFtkmGseEH+AK/hny8XrytLH3+b5M8zL/Ycg3fhIocFpUMyl/wpFnVRvdw==", "path": "system.net.sockets/4.3.0", "hashPath": "system.net.sockets.4.3.0.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.ObjectModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-bdX+80eKv9bN6K4N+d77OankKHGn6CH711a6fcOpMQu2Fckp/Ft4L/kW9WznHpyR0NRAvJutzOMHNNlBGvxQzQ==", "path": "system.objectmodel/4.3.0", "hashPath": "system.objectmodel.4.3.0.nupkg.sha512"}, "System.Private.DataContractSerialization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-yDaJ2x3mMmjdZEDB4IbezSnCsnjQ4BxinKhRAaP6kEgL6Bb6jANWphs5SzyD8imqeC/3FxgsuXT6ykkiH1uUmA==", "path": "system.private.datacontractserialization/4.3.0", "hashPath": "system.private.datacontractserialization.4.3.0.nupkg.sha512"}, "System.Private.Uri/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-o1+7RJnu3Ik3PazR7Z7tJhjPdE000Eq2KGLLWhqJJKXj04wrS8lwb1OFtDF9jzXXADhUuZNJZlPc98uwwqmpFA==", "path": "system.private.uri/4.3.2", "hashPath": "system.private.uri.4.3.2.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Emit/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-VR4kk8XLKebQ4MZuKuIni/7oh+QGFmZW3qORd1GvBq/8026OpW501SzT/oypwiQl4TvT8ErnReh/NzY9u+C6wQ==", "path": "system.reflection.emit/4.7.0", "hashPath": "system.reflection.emit.4.7.0.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "path": "system.reflection.emit.ilgeneration/4.3.0", "hashPath": "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-oadVHGSMsTmZsAF864QYN1t1QzZjIcuKU3l2S9cZOwDdDueNTrqq1yRj7koFfIGEnKpt6NjpL3rOzRhs4ryOgA==", "path": "system.reflection.emit.lightweight/4.3.0", "hashPath": "system.reflection.emit.lightweight.4.3.0.nupkg.sha512"}, "System.Reflection.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "path": "system.reflection.extensions/4.3.0", "hashPath": "system.reflection.extensions.4.3.0.nupkg.sha512"}, "System.Reflection.Metadata/1.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-COC1aiAJjCoA5GBF+QKL2uLqEBew4JsCkQmoHKbN3TlOZKa2fKLz5CpiRQKDz0RsAOEGsVKqOD5bomsXq/4STQ==", "path": "system.reflection.metadata/1.6.0", "hashPath": "system.reflection.metadata.1.6.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7u6ulLcZbyxB5Gq0nMkQttcdBTx57ibzw+4IOXEfR+sXYQoHvjW5LTLyNr8O22UIMrqYbchJQJnos4eooYzYJA==", "path": "system.reflection.typeextensions/4.3.0", "hashPath": "system.reflection.typeextensions.4.3.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.Caching/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-NdvNRjTPxYvIEhXQszT9L9vJhdQoX6AQ0AlhjTU+5NqFQVuacJTfhPVAvtGWNA2OJCqRiR/okBcZgMwI6MqcZg==", "path": "system.runtime.caching/4.7.0", "hashPath": "system.runtime.caching.4.7.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.Handles/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "path": "system.runtime.handles/4.3.0", "hashPath": "system.runtime.handles.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "path": "system.runtime.interopservices/4.3.0", "hashPath": "system.runtime.interopservices.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-cbz4YJMqRDR7oLeMRbdYv7mYzc++17lNhScCX0goO2XpGWdvAt60CGN+FHdePUEHCe/Jy9jUlvNAiNdM+7jsOw==", "path": "system.runtime.interopservices.runtimeinformation/4.3.0", "hashPath": "system.runtime.interopservices.runtimeinformation.4.3.0.nupkg.sha512"}, "System.Runtime.Numerics/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-yMH+MfdzHjy17l2KESnPiF2dwq7T+xLnSJar7slyimAkUh/gTrS9/UQOtv7xarskJ2/XDSNvfLGOBQPjL7PaHQ==", "path": "system.runtime.numerics/4.3.0", "hashPath": "system.runtime.numerics.4.3.0.nupkg.sha512"}, "System.Runtime.Serialization.Formatters/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KT591AkTNFOTbhZlaeMVvfax3RqhH1EJlcwF50Wm7sfnBLuHiOeZRRKrr1ns3NESkM20KPZ5Ol/ueMq5vg4QoQ==", "path": "system.runtime.serialization.formatters/4.3.0", "hashPath": "system.runtime.serialization.formatters.4.3.0.nupkg.sha512"}, "System.Runtime.Serialization.Json/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-CpVfOH0M/uZ5PH+M9+Gu56K0j9lJw3M+PKRegTkcrY/stOIvRUeonggxNrfBYLA5WOHL2j15KNJuTuld3x4o9w==", "path": "system.runtime.serialization.json/4.3.0", "hashPath": "system.runtime.serialization.json.4.3.0.nupkg.sha512"}, "System.Runtime.Serialization.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Wz+0KOukJGAlXjtKr+5Xpuxf8+c8739RI1C+A2BoQZT+wMCCoMDDdO8/4IRHfaVINqL78GO8dW8G2lW/e45Mcw==", "path": "system.runtime.serialization.primitives/4.3.0", "hashPath": "system.runtime.serialization.primitives.4.3.0.nupkg.sha512"}, "System.Security.AccessControl/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-JECvTt5aFF3WT3gHpfofL2MNNP6v84sxtXxpqhLBCcDRzqsPBmHhQ6shv4DwwN2tRlzsUxtb3G9M3763rbXKDg==", "path": "system.security.accesscontrol/4.7.0", "hashPath": "system.security.accesscontrol.4.7.0.nupkg.sha512"}, "System.Security.Cryptography.Algorithms/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-W1kd2Y8mYSCgc3ULTAZ0hOP2dSdG5YauTb1089T0/kRcN2MpSAW1izOFROrJgxSlMn3ArsgHXagigyi+ibhevg==", "path": "system.security.cryptography.algorithms/4.3.0", "hashPath": "system.security.cryptography.algorithms.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-WG3r7EyjUe9CMPFSs6bty5doUqT+q9pbI80hlNzo2SkPkZ4VTuZkGWjpp77JB8+uaL4DFPRdBsAY+DX3dBK92A==", "path": "system.security.cryptography.cng/4.5.0", "hashPath": "system.security.cryptography.cng.4.5.0.nupkg.sha512"}, "System.Security.Cryptography.Csp/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-X4s/FCkEUnRGnwR3aSfVIkldBmtURMhmexALNTwpjklzxWU7yjMk7GHLKOZTNkgnWnE0q7+BCf9N2LVRWxewaA==", "path": "system.security.cryptography.csp/4.3.0", "hashPath": "system.security.cryptography.csp.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-1DEWjZZly9ae9C79vFwqaO5kaOlI5q+3/55ohmq/7dpDyDfc8lYe7YVxJUZ5MF/NtbkRjwFRo14yM4OEo9EmDw==", "path": "system.security.cryptography.encoding/4.3.0", "hashPath": "system.security.cryptography.encoding.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-h4CEgOgv5PKVF/HwaHzJRiVboL2THYCou97zpmhjghx5frc7fIvlkY1jL+lnIQyChrJDMNEXS6r7byGif8Cy4w==", "path": "system.security.cryptography.openssl/4.3.0", "hashPath": "system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Pkcs/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-TGQX51gxpY3K3I6LJlE2LAftVlIMqJf0cBGhz68Y89jjk3LJCB6SrwiD+YN1fkqemBvWGs+GjyMJukl6d6goyQ==", "path": "system.security.cryptography.pkcs/4.5.0", "hashPath": "system.security.cryptography.pkcs.4.5.0.nupkg.sha512"}, "System.Security.Cryptography.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7bDIyVFNL/xKeFHjhobUAQqSpJq9YTOpbEs6mR233Et01STBMXNAc/V+BM6dwYGc95gVh/Zf+iVXWzj3mE8DWg==", "path": "system.security.cryptography.primitives/4.3.0", "hashPath": "system.security.cryptography.primitives.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-ehYW0m9ptxpGWvE4zgqongBVWpSDU/JCFD4K7krxkQwSz/sFQjEXCUqpvencjy6DYDbn7Ig09R8GFffu8TtneQ==", "path": "system.security.cryptography.protecteddata/4.7.0", "hashPath": "system.security.cryptography.protecteddata.4.7.0.nupkg.sha512"}, "System.Security.Cryptography.X509Certificates/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-t2Tmu6Y2NtJ2um0RtcuhP7ZdNNxXEgUm2JeoA/0NvlMjAhKCnM1NX07TDl3244mVp3QU6LPEhT3HTtH1uF7IYw==", "path": "system.security.cryptography.x509certificates/4.3.0", "hashPath": "system.security.cryptography.x509certificates.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Xml/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-i2Jn6rGXR63J0zIklImGRkDIJL4b1NfPSEbIVHBlqoIb12lfXIigCbDRpDmIEzwSo/v1U5y/rYJdzZYSyCWxvg==", "path": "system.security.cryptography.xml/4.5.0", "hashPath": "system.security.cryptography.xml.4.5.0.nupkg.sha512"}, "System.Security.Permissions/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-dkOV6YYVBnYRa15/yv004eCGRBVADXw8qRbbNiCn/XpdJSUXkkUeIvdvFHkvnko4CdKMqG8yRHC4ox83LSlMsQ==", "path": "system.security.permissions/4.7.0", "hashPath": "system.security.permissions.4.7.0.nupkg.sha512"}, "System.Security.Principal.Windows/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-ojD0PX0XhneCsUbAZVKdb7h/70vyYMDYs85lwEI+LngEONe/17A0cFaRFqZU+sOEidcVswYWikYOQ9PPfjlbtQ==", "path": "system.security.principal.windows/4.7.0", "hashPath": "system.security.principal.windows.4.7.0.nupkg.sha512"}, "System.Security.SecureString/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-PnXp38O9q/2Oe4iZHMH60kinScv6QiiL2XH54Pj2t0Y6c2zKPEiAZsM/M3wBOHLNTBDFP0zfy13WN2M0qFz5jg==", "path": "system.security.securestring/4.3.0", "hashPath": "system.security.securestring.4.3.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-aeu4FlaUTemuT1qOd1MyU4T516QR4Fy+9yDbwWMPHOHy7U8FD6SgTzdZFO7gHcfAPHtECqInbwklVvUK4RHcNg==", "path": "system.text.encoding.codepages/4.7.0", "hashPath": "system.text.encoding.codepages.4.7.0.nupkg.sha512"}, "System.Text.Encoding.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YVMK0Bt/A43RmwizJoZ22ei2nmrhobgeiYwFzC4YAN+nue8RF6djXDMog0UCn+brerQoYVyaS+ghy9P/MUVcmw==", "path": "system.text.encoding.extensions/4.3.0", "hashPath": "system.text.encoding.extensions.4.3.0.nupkg.sha512"}, "System.Text.Encodings.Web/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-KmJ+CJXizDofbq6mpqDoRRLcxgOd2z9X3XoFNULSbvbqVRZkFX3istvr+MUjL6Zw1RT+RNdoI4GYidIINtgvqQ==", "path": "system.text.encodings.web/5.0.1", "hashPath": "system.text.encodings.web.5.0.1.nupkg.sha512"}, "System.Text.Json/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-/UM3UK1dXKl8Ybysg/21gM4S8DJgkR+yLU8JwqCVbuNqQNImelntgYFAN5QxR8sJJ1kMx//hOUdf0lltosi8cQ==", "path": "system.text.json/5.0.1", "hashPath": "system.text.json.5.0.1.nupkg.sha512"}, "System.Text.RegularExpressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-RpT2DA+L660cBt1FssIE9CAGpLFdFPuheB7pLpKpn6ZXNby7jDERe8Ua/Ne2xGiwLVG2JOqziiaVCGDon5sKFA==", "path": "system.text.regularexpressions/4.3.0", "hashPath": "system.text.regularexpressions.4.3.0.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.2": {"type": "package", "serviceable": true, "sha512": "sha512-BG/TNxDFv0svAzx8OiMXDlsHfGw623BZ8tCXw4YLhDFDvDhNUEV58jKYMGRnkbJNm7c3JNNJDiN7JBMzxRBR2w==", "path": "system.threading.tasks.extensions/4.5.2", "hashPath": "system.threading.tasks.extensions.4.5.2.nupkg.sha512"}, "System.Threading.Timer/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Z6YfyYTCg7lOZjJzBjONJTFKGN9/NIYKSxhU5GRd+DTwHSZyvWp1xuI5aR+dLg+ayyC5Xv57KiY4oJ0tMO89fQ==", "path": "system.threading.timer/4.3.0", "hashPath": "system.threading.timer.4.3.0.nupkg.sha512"}, "System.ValueTuple/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-okurQJO6NRE/apDIP23ajJ0hpiNmJ+f0BwOlB/cSqTLQlw5upkf+5+96+iG2Jw40G1fCVCyPz/FhIABUjMR+RQ==", "path": "system.valuetuple/4.5.0", "hashPath": "system.valuetuple.4.5.0.nupkg.sha512"}, "System.Windows.Extensions/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-CeWTdRNfRaSh0pm2gDTJFwVaXfTq6Xwv/sA887iwPTneW7oMtMlpvDIO+U60+3GWTB7Aom6oQwv5VZVUhQRdPQ==", "path": "system.windows.extensions/4.7.0", "hashPath": "system.windows.extensions.4.7.0.nupkg.sha512"}, "System.Xml.ReaderWriter/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GrprA+Z0RUXaR4N7/eW71j1rgMnEnEVlgii49GZyAjTH7uliMnrOU3HNFBr6fEDBCJCIdlVNq9hHbaDR621XBA==", "path": "system.xml.readerwriter/4.3.0", "hashPath": "system.xml.readerwriter.4.3.0.nupkg.sha512"}, "System.Xml.XDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5zJ0XDxAIg8iy+t4aMnQAu0MqVbqyvfoUVl1yDV61xdo3Vth45oA2FoY4pPkxYAH5f8ixpmTqXeEIya95x0aCQ==", "path": "system.xml.xdocument/4.3.0", "hashPath": "system.xml.xdocument.4.3.0.nupkg.sha512"}, "System.Xml.XmlDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-lJ8AxvkX7GQxpC6GFCeBj8ThYVyQczx2+f/cWHJU8tjS7YfI6Cv6bon70jVEgs2CiFbmmM8b9j1oZVx0dSI2Ww==", "path": "system.xml.xmldocument/4.3.0", "hashPath": "system.xml.xmldocument.4.3.0.nupkg.sha512"}, "System.Xml.XmlSerializer/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-MYoTCP7EZ98RrANESW05J5ZwskKDoN0AuZ06ZflnowE50LTpbR5yRg3tHckTVm5j/m47stuGgCrCHWePyHS70Q==", "path": "system.xml.xmlserializer/4.3.0", "hashPath": "system.xml.xmlserializer.4.3.0.nupkg.sha512"}, "BdoPartner.Plans.Common/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "BdoPartner.Plans.DataAccess.Common/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "BdoPartner.Plans.Model.DTO/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}