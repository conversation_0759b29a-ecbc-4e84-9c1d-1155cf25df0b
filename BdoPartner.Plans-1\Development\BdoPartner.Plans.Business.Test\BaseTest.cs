﻿
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using BdoPartner.Plans.Common;
using BdoPartner.Plans.DataAccess;
using System.IO;
using BdoPartner.Plans.Model.Mapper;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using BdoPartner.Plans.Common.Config;
using Moq;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Hosting;
using BdoPartner.Plans.Business.Interface;

namespace BdoPartner.Plans.Business.Test
{
    /// <summary>
    /// Base unit test class.
    /// Test will be happened in memory database.
    /// </summary>
    /// <typeparam name="T">T is type of Service you are going to test. Note: we only support test on service in one unit test this time.</typeparam>
    public class BaseTest<T> where T : class
    {
        protected IConfiguration config;
        protected IConfigSettings configSettings;
        /// <summary>
        ///  It is database connection string got from appsettings.json.
        /// </summary>
        protected string dbConnection = string.Empty;
        protected IUnitOfWork uow = null;
        protected DatabaseContext dbContext = null;
        protected IMapper mapper = null;
        protected IHttpContextAccessor httpContextAccessor = null;
        protected Mock<IWebHostEnvironment> environment;
        protected Mock<ILogger<T>> logger;
     
        /// <summary>
        ///  Constructor. Register all required dependency injections.
        /// </summary>
        public BaseTest()
        {
            //
            // Try to get database connection string setting from appsettings.json.
            //
            string direct = Directory.GetCurrentDirectory();
            this.config = ConfigurationHelper.GetApplicationConfiguration(direct, "appsettings.json");
            this.configSettings = new ConfigSettings(this.config);

            this.environment = new Mock<IWebHostEnvironment>();

            this.dbConnection = this.configSettings.DatabaseConnection;
            var optionBuilder = new Microsoft.EntityFrameworkCore.DbContextOptionsBuilder<DatabaseContext>();

            // Register Mapper profiles.
            var mapperConfig = new MapperConfiguration(cfg =>
            {
               // cfg.AddProfile(typeof(UserProfile));
            });

            this.mapper = new Mapper(mapperConfig);

          
            if (!this.configSettings.IsInMemoryDatabase)
            {
                //
                // Try to connect sql server database.
                //
                optionBuilder.UseSqlServer(this.dbConnection).UseLazyLoadingProxies(false);

                //
                // Try to connect to mysql database with connection string setting in appsettings.json.
                //
                //optionBuilder.UseMySql(this.dbConnection).UseLazyLoadingProxies(false);

            }
            else
            {
                //
                // Try to use in memory database to do unit test instead of accessing data from on premise sql server database.
                //
                optionBuilder.UseInMemoryDatabase("BdoPartner.Plans.Database").UseLazyLoadingProxies(false);
            }

            this.httpContextAccessor = new HttpContextAccessor();

            this.logger = new Mock<ILogger<T>>();
            var uowLogger = new Mock<ILogger<UnitOfWork>>();
            var dbLogger = new Mock<ILogger<DatabaseContext>>();
            //
            // Walk around dependency injection.
            //
            this.dbContext = new DatabaseContext(optionBuilder.Options, config);
            this.uow = new UnitOfWork(this.dbContext, this.mapper, uowLogger.Object);
        }
    }
}
