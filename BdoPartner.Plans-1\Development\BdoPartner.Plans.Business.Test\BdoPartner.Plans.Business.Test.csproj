<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net5.0</TargetFramework>

    <IsPackable>false</IsPackable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="16.9.4" />
    <PackageReference Include="Moq" Version="4.16.1" />
    <PackageReference Include="NUnit" Version="3.13.1" />
    <PackageReference Include="NUnit3TestAdapter" Version="3.17.0" />
    <PackageReference Include="coverlet.collector" Version="3.0.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\BdoPartner.Plans.Business.Interface\BdoPartner.Plans.Business.Interface.csproj" />
    <ProjectReference Include="..\BdoPartner.Plans.Business\BdoPartner.Plans.Business.csproj" />
    <ProjectReference Include="..\BdoPartner.Plans.Common\BdoPartner.Plans.Common.csproj" />
    <ProjectReference Include="..\BdoPartner.Plans.DataAccess\BdoPartner.Plans.DataAccess.csproj" />
    <ProjectReference Include="..\BdoPartner.Plans.Model.DTO\BdoPartner.Plans.Model.DTO.csproj" />
    <ProjectReference Include="..\BdoPartner.Plans.Model.Entity\BdoPartner.Plans.Model.Entity.csproj" />
    <ProjectReference Include="..\BdoPartner.Plans.Model.Mapper\BdoPartner.Plans.Model.Mapper.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="appsettings.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
