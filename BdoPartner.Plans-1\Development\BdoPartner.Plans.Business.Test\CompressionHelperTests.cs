using BdoPartner.Plans.Common.Helpers;
using NUnit.Framework;

namespace BdoPartner.Plans.Business.Test
{
    [TestFixture]
    public class CompressionHelperTests
    {
        [Test]
        public void CompressAndEncode_ValidJsonString_ReturnsBase64String()
        {
            // Arrange
            string jsonString = @"{""name"":""Test Questionnaire"",""pages"":[{""name"":""page1"",""elements"":[{""type"":""text"",""name"":""question1"",""title"":""What is your name?""}]}]}";

            // Act
            string compressed = CompressionHelper.CompressAndEncode(jsonString);

            // Assert
            Assert.IsFalse(string.IsNullOrEmpty(compressed));
            Assert.IsTrue(CompressionHelper.IsBase64Encoded(compressed));
        }

        [Test]
        public void DecodeAndDecompress_ValidBase64CompressedString_ReturnsOriginalJson()
        {
            // Arrange
            string originalJson = @"{""name"":""Test Questionnaire"",""pages"":[{""name"":""page1"",""elements"":[{""type"":""text"",""name"":""question1"",""title"":""What is your name?""}]}]}";
            string compressed = CompressionHelper.CompressAndEncode(originalJson);

            // Act
            string decompressed = CompressionHelper.DecodeAndDecompress(compressed);

            // Assert
            Assert.AreEqual(originalJson, decompressed);
        }

        [Test]
        public void SafeDecompressJson_PlainJsonString_ReturnsUnchanged()
        {
            // Arrange
            string plainJson = @"{""name"":""Test"",""value"":123}";

            // Act
            string result = CompressionHelper.SafeDecompressJson(plainJson);

            // Assert
            Assert.AreEqual(plainJson, result);
        }

        [Test]
        public void SafeDecompressJson_CompressedJsonString_ReturnsDecompressed()
        {
            // Arrange
            string originalJson = @"{""name"":""Test Questionnaire"",""pages"":[{""name"":""page1"",""elements"":[{""type"":""text"",""name"":""question1"",""title"":""What is your name?""}]}]}";
            string compressed = CompressionHelper.CompressAndEncode(originalJson);

            // Act
            string result = CompressionHelper.SafeDecompressJson(compressed);

            // Assert
            Assert.AreEqual(originalJson, result);
        }

        [Test]
        public void CompressAndEncode_EmptyString_ReturnsEmpty()
        {
            // Arrange
            string emptyString = "";

            // Act
            string result = CompressionHelper.CompressAndEncode(emptyString);

            // Assert
            Assert.AreEqual("", result);
        }

        [Test]
        public void DecodeAndDecompress_EmptyString_ReturnsEmpty()
        {
            // Arrange
            string emptyString = "";

            // Act
            string result = CompressionHelper.DecodeAndDecompress(emptyString);

            // Assert
            Assert.AreEqual("", result);
        }

        [Test]
        public void IsBase64Encoded_ValidBase64_ReturnsTrue()
        {
            // Arrange
            string validBase64 = "SGVsbG8gV29ybGQ="; // "Hello World" in base64

            // Act
            bool result = CompressionHelper.IsBase64Encoded(validBase64);

            // Assert
            Assert.IsTrue(result);
        }

        [Test]
        public void IsBase64Encoded_InvalidBase64_ReturnsFalse()
        {
            // Arrange
            string invalidBase64 = "This is not base64!";

            // Act
            bool result = CompressionHelper.IsBase64Encoded(invalidBase64);

            // Assert
            Assert.IsFalse(result);
        }

        [Test]
        public void IsBase64Encoded_JsonString_ReturnsFalse()
        {
            // Arrange
            string jsonString = @"{""name"":""test""}";

            // Act
            bool result = CompressionHelper.IsBase64Encoded(jsonString);

            // Assert
            Assert.IsFalse(result);
        }

        [Test]
        public void CompressionReducesSize_LargeJsonString_CompressedIsSmallerThanOriginal()
        {
            // Arrange
            string largeJson = @"{""questionnaire"":{""title"":""Partner Plan Questionnaire"",""description"":""This is a comprehensive questionnaire for partner planning purposes."",""pages"":[{""name"":""generalInfo"",""title"":""General Information"",""elements"":[{""type"":""text"",""name"":""partnerName"",""title"":""Partner Name"",""isRequired"":true},{""type"":""text"",""name"":""contactEmail"",""title"":""Contact Email"",""isRequired"":true,""inputType"":""email""},{""type"":""dropdown"",""name"":""businessType"",""title"":""Business Type"",""choices"":[""Corporation"",""Partnership"",""LLC"",""Sole Proprietorship""]}]},{""name"":""financialInfo"",""title"":""Financial Information"",""elements"":[{""type"":""text"",""name"":""annualRevenue"",""title"":""Annual Revenue"",""inputType"":""number""},{""type"":""text"",""name"":""employeeCount"",""title"":""Number of Employees"",""inputType"":""number""},{""type"":""comment"",""name"":""additionalNotes"",""title"":""Additional Notes""}]}]}}";

            // Act
            string compressed = CompressionHelper.CompressAndEncode(largeJson);

            // Assert
            Assert.IsTrue(compressed.Length < largeJson.Length, "Compressed string should be smaller than original");
        }

        [Test]
        public void CompressAndEncode_AlreadyCompressedData_DoesNotDoubleCompress()
        {
            // Arrange
            string originalJson = @"{""name"":""Test"",""value"":123}";
            string alreadyCompressed = CompressionHelper.CompressAndEncode(originalJson);

            // Act - try to compress already compressed data
            string result = CompressionHelper.CompressAndEncode(alreadyCompressed);

            // Assert - should return the same compressed data, not double-compress
            Assert.AreEqual(alreadyCompressed, result);
        }

        [Test]
        public void SafeDecompressJson_HandlesNullAndEmpty()
        {
            // Test null
            string nullResult = CompressionHelper.SafeDecompressJson(null);
            Assert.AreEqual(string.Empty, nullResult);

            // Test empty string
            string emptyResult = CompressionHelper.SafeDecompressJson("");
            Assert.AreEqual(string.Empty, emptyResult);

            // Test whitespace
            string whitespaceResult = CompressionHelper.SafeDecompressJson("   ");
            Assert.AreEqual("   ", whitespaceResult);
        }
    }
}
