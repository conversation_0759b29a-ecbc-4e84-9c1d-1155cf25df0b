using NUnit.Framework;
using BdoPartner.Plans.Business;
using BdoPartner.Plans.Business.Interface;
using BdoPartner.Plans.Common;
using System.Reflection;
using System.IO;
using System.Text;
using System.Collections.Generic;
using System.Linq;
using Entity = BdoPartner.Plans.Model.Entity;

namespace BdoPartner.Plans.Business.Test
{
    /// <summary>
    /// Unit tests for PartnerReferenceDataUploadService
    /// </summary>
    public class PartnerReferenceDataUploadServiceTest : BaseTest<PartnerReferenceDataUploadService>
    {
        private IPartnerReferenceDataUploadService _service;

        [SetUp]
        public void Setup()
        {
            _service = new PartnerReferenceDataUploadService(
                this.uow,
                this.httpContextAccessor,
                this.configSettings,
                this.logger.Object,
                this.mapper
            );
        }

        [Test]
        public void NormalizeColumnNameToCamelCase_WithSpacesAndSpecialCharacters_ShouldReturnCamelCaseString()
        {
            // Arrange
            var testCases = new[]
            {
                new { Input = "Employee ID", Expected = "employeeId" },
                new { Input = "First Name", Expected = "firstName" },
                new { Input = "Total Amount ($)", Expected = "totalAmount" },
                new { Input = "Year-to-Date Revenue", Expected = "yearToDateRevenue" },
                new { Input = "Partner's Performance %", Expected = "partnerSPerformance" },
                new { Input = "Office Location (City)", Expected = "officeLocationCity" },
                new { Input = "2024 Q1 Results", Expected = "2024Q1Results" },
                new { Input = "Revenue_Target", Expected = "revenueTarget" },
                new { Input = "Client-Count", Expected = "clientCount" },
                new { Input = "Margin %", Expected = "margin" },
                new { Input = "Margin $", Expected = "margin" },
                new { Input = "Est Rev Won - Originator", Expected = "estRevWonOriginator" },
                new { Input = "Net Production by PMP", Expected = "netProductionByPmp" },
                new { Input = "", Expected = "" },
                new { Input = (string)null, Expected = "" }
            };

            // Act & Assert
            foreach (var testCase in testCases)
            {
                var result = InvokePrivateMethod<string>(_service, "NormalizeColumnNameToCamelCase", testCase.Input);
                Assert.AreEqual(testCase.Expected, result, $"Failed for input: '{testCase.Input}'");
            }
        }

        [Test]
        public void NormalizeColumnNames_WithDuplicateNames_ShouldReturnUniqueNames()
        {
            // Arrange
            var columnNames = new List<string>
            {
                "Employee ID",
                "Employee Name",
                "Margin %",
                "Margin $",
                "Total",
                "Total Amount",
                "Total",  // Duplicate
                "",       // Empty
                "   ",    // Whitespace only
                "Margin %" // Another duplicate
            };

            // Act
            var result = InvokePrivateMethod<Dictionary<string, string>>(_service, "NormalizeColumnNames", columnNames);

            // Assert
            Assert.AreEqual(columnNames.Count, result.Count, "Should return mapping for all input columns");

            // Check specific expected mappings
            Assert.AreEqual("employeeId", result["Employee ID"]);
            Assert.AreEqual("employeeName", result["Employee Name"]);
            Assert.AreEqual("margin", result["Margin %"]);
            Assert.AreEqual("margin2", result["Margin $"]); // Should get sequence number
            Assert.AreEqual("total", result["Total"]);
            Assert.AreEqual("totalAmount", result["Total Amount"]);
            Assert.AreEqual("total2", result[columnNames[6]]); // Second "Total" should get sequence number
            Assert.AreEqual("margin3", result[columnNames[9]]); // Third "Margin %" should get sequence number

            // Verify all values are unique
            var normalizedValues = result.Values.ToList();
            var uniqueValues = normalizedValues.Distinct().ToList();
            Assert.AreEqual(normalizedValues.Count, uniqueValues.Count, "All normalized names should be unique");
        }

        [Test]
        public void DoMetadataMatch_WithMatchingMetadata_ShouldReturnTrue()
        {
            // Arrange
            var existingMetaDetails = new List<Entity.PartnerReferenceDataMetaDetails>
            {
                new Entity.PartnerReferenceDataMetaDetails { ColumnName = "Employee ID", NormalizedColumnName = "employeeId", ColumnOrder = 1 },
                new Entity.PartnerReferenceDataMetaDetails { ColumnName = "Employee Name", NormalizedColumnName = "employeeName", ColumnOrder = 2 },
                new Entity.PartnerReferenceDataMetaDetails { ColumnName = "Margin %", NormalizedColumnName = "margin", ColumnOrder = 3 }
            };

            var currentColumnHeaders = new List<string> { "Employee ID", "Employee Name", "Margin %" };
            var currentNormalizedNames = new Dictionary<string, string>
            {
                ["Employee ID"] = "employeeId",
                ["Employee Name"] = "employeeName",
                ["Margin %"] = "margin"
            };

            // Act
            var result = InvokePrivateMethod<bool>(_service, "DoMetadataMatch", existingMetaDetails, currentColumnHeaders, currentNormalizedNames);

            // Assert
            Assert.IsTrue(result, "Should return true when metadata matches exactly");
        }

        [Test]
        public void DoMetadataMatch_WithDifferentColumnNames_ShouldReturnFalse()
        {
            // Arrange
            var existingMetaDetails = new List<Entity.PartnerReferenceDataMetaDetails>
            {
                new Entity.PartnerReferenceDataMetaDetails { ColumnName = "Employee ID", NormalizedColumnName = "employeeId", ColumnOrder = 1 },
                new Entity.PartnerReferenceDataMetaDetails { ColumnName = "Employee Name", NormalizedColumnName = "employeeName", ColumnOrder = 2 }
            };

            var currentColumnHeaders = new List<string> { "Employee ID", "Full Name" }; // Different column name
            var currentNormalizedNames = new Dictionary<string, string>
            {
                ["Employee ID"] = "employeeId",
                ["Full Name"] = "fullName"
            };

            // Act
            var result = InvokePrivateMethod<bool>(_service, "DoMetadataMatch", existingMetaDetails, currentColumnHeaders, currentNormalizedNames);

            // Assert
            Assert.IsFalse(result, "Should return false when column names differ");
        }

        [Test]
        public void DoMetadataMatch_WithDifferentNormalizedNames_ShouldReturnFalse()
        {
            // Arrange
            var existingMetaDetails = new List<Entity.PartnerReferenceDataMetaDetails>
            {
                new Entity.PartnerReferenceDataMetaDetails { ColumnName = "Employee ID", NormalizedColumnName = "employeeid", ColumnOrder = 1 }, // Old lowercase normalization
                new Entity.PartnerReferenceDataMetaDetails { ColumnName = "Employee Name", NormalizedColumnName = "employeename", ColumnOrder = 2 }
            };

            var currentColumnHeaders = new List<string> { "Employee ID", "Employee Name" };
            var currentNormalizedNames = new Dictionary<string, string>
            {
                ["Employee ID"] = "employeeId",     // New camelCase normalization
                ["Employee Name"] = "employeeName"
            };

            // Act
            var result = InvokePrivateMethod<bool>(_service, "DoMetadataMatch", existingMetaDetails, currentColumnHeaders, currentNormalizedNames);

            // Assert
            Assert.IsFalse(result, "Should return false when normalized names differ (e.g., old lowercase vs new camelCase)");
        }

        [Test]
        public void UpdateExistingMetadataAsync_ShouldUpdateMetadataAndEnableAudit()
        {
            // This test would require more complex setup with mocked UOW and file handling
            // For now, we'll focus on the core logic tests above
            // In a real implementation, you would:
            // 1. Mock the UOW.Commit method to verify audit=true is passed
            // 2. Mock file processing methods
            // 3. Verify that old metadata details are deleted and new ones are created
            // 4. Verify that the main metadata record is updated with new filename and timestamps

            Assert.Pass("UpdateExistingMetadataAsync integration test would be implemented with proper mocking setup");
        }

        [Test]
        public void IsNumericValue_WithNumericValues_ShouldReturnTrue()
        {
            // Arrange
            var numericValues = new[]
            {
                "123",
                "123.45",
                "$1,234.56",
                "45%",
                "0.0761",
                "1737498.63",
                "-123.45",
                "1,000,000"
            };

            // Act & Assert
            foreach (var value in numericValues)
            {
                var result = InvokePrivateMethod<bool>(_service, "IsNumericValue", value);
                Assert.IsTrue(result, $"Value '{value}' should be detected as numeric");
            }
        }

        [Test]
        public void IsNumericValue_WithTextValues_ShouldReturnFalse()
        {
            // Arrange
            var textValues = new[]
            {
                "Employee Name",
                "John Doe",
                "Department",
                "Guidance Text",
                "N/A",
                "abc123",
                "123abc",
                ""
            };

            // Act & Assert
            foreach (var value in textValues)
            {
                var result = InvokePrivateMethod<bool>(_service, "IsNumericValue", value);
                Assert.IsFalse(result, $"Value '{value}' should be detected as text");
            }
        }

        [Test]
        public void ParseCsvLine_WithQuotedFields_ShouldParseCorrectly()
        {
            // Arrange
            var testCases = new[]
            {
                new {
                    Input = "11,Stephen,N,,85,Sylvain,89,Scott",
                    Expected = new[] { "11", "Stephen", "N", "", "85", "Sylvain", "89", "Scott" }
                },
                new {
                    Input = "11,\"Meek, Stephen\",N,,85,\"Guindon, Sylvain\",89,\"Rodie, Scott\"",
                    Expected = new[] { "11", "Meek, Stephen", "N", "", "85", "Guindon, Sylvain", "89", "Rodie, Scott" }
                },
                new {
                    Input = "EmployeeID,EmployeeName,Exempt,LeadershipRole",
                    Expected = new[] { "EmployeeID", "EmployeeName", "Exempt", "LeadershipRole" }
                }
            };

            // Act & Assert
            foreach (var testCase in testCases)
            {
                var result = InvokePrivateMethod<string[]>(_service, "ParseCsvLine", testCase.Input);
                Assert.AreEqual(testCase.Expected, result, $"Failed for input: '{testCase.Input}'");
            }
        }

        [Test]
        public void ExtractHeadersFromCsv_WithValidCsvStream_ShouldReturnHeaders()
        {
            // Arrange
            var csvContent = "EmployeeID,EmployeeName,Department,Total Amount\n11,John Doe,IT,50000";
            var stream = new MemoryStream(Encoding.UTF8.GetBytes(csvContent));

            // Act
            var result = InvokePrivateMethod<List<string>>(_service, "ExtractHeadersFromCsv", stream);

            // Assert
            Assert.AreEqual(4, result.Count);
            Assert.AreEqual("EmployeeID", result[0]);
            Assert.AreEqual("EmployeeName", result[1]);
            Assert.AreEqual("Department", result[2]);
            Assert.AreEqual("Total Amount", result[3]);
        }

        [Test]
        public void DetermineColumnDataTypeFromCsv_WithTypeIndicators_ShouldReturnCorrectType()
        {
            // Arrange - CSV content similar to the sample file structure
            var csvContent = @"Employee ID,Employee Name,Total Amount,Department
,,,
,Numeric,Numeric,Text
XXXXX,John Doe,321651,IT
YYYYY,Jane Smith,456789,HR";

            // Act & Assert - Create new stream for each test to avoid disposal issues
            using (var stream1 = new MemoryStream(Encoding.UTF8.GetBytes(csvContent)))
            {
                var result0 = InvokePrivateMethod<byte>(_service, "DetermineColumnDataTypeFromCsv", stream1, 0); // Employee ID
                Assert.AreEqual((byte)BdoPartner.Plans.Common.Enumerations.PartnerReferenceDataColumnType.Text, result0);
            }

            using (var stream2 = new MemoryStream(Encoding.UTF8.GetBytes(csvContent)))
            {
                var result1 = InvokePrivateMethod<byte>(_service, "DetermineColumnDataTypeFromCsv", stream2, 1); // Employee Name
                Assert.AreEqual((byte)BdoPartner.Plans.Common.Enumerations.PartnerReferenceDataColumnType.Numeric, result1);
            }

            using (var stream3 = new MemoryStream(Encoding.UTF8.GetBytes(csvContent)))
            {
                var result2 = InvokePrivateMethod<byte>(_service, "DetermineColumnDataTypeFromCsv", stream3, 2); // Total Amount
                Assert.AreEqual((byte)BdoPartner.Plans.Common.Enumerations.PartnerReferenceDataColumnType.Numeric, result2);
            }

            using (var stream4 = new MemoryStream(Encoding.UTF8.GetBytes(csvContent)))
            {
                var result3 = InvokePrivateMethod<byte>(_service, "DetermineColumnDataTypeFromCsv", stream4, 3); // Department
                Assert.AreEqual((byte)BdoPartner.Plans.Common.Enumerations.PartnerReferenceDataColumnType.Text, result3);
            }
        }

        [Test]
        public void DetermineColumnDataTypeFromCsv_WithSampleFile_ShouldReturnCorrectTypes()
        {
            // Arrange - Using the actual sample file structure
            var csvContent = @"Employee ID,Employee Name,Sub-Service Line,Est Rev Won - Originator,Est Rev Won - Partner Lead,Est Rev Won - Team,New Sales,Recurring Sales,NP Growth by PMP,Net Production by PMP,Margin %,Margin $,Partner's Billable Hours,WIP Aging Days,AR Aging Days,Bad Debt (%),CVI Score,File Volume,Market Share,Net Fee Revenue
(All Billed Hours),Private Households,Financial Services,Technology Media and Telecommunications,Consumer Business,Professional Services,Manufacturing,Not-For-Profit & Education,Real Estate & Construction,Agriculture,Natural Resources,Cannabis,Public Sector,Private Equity,Primary Industry,Secondary Industry,Talent Dev Hours,% outside discipline,Est Rev Won - Originator Guidance,Est Rev Won - Partner Lead Guidance
,,,Numeric,Numeric,Numeric,Numeric,Numeric,Numeric,Numeric,Numeric,Numeric,Numeric,Numeric,Numeric,Numeric,Numeric,Numeric,Numeric,Numeric,Numeric,Numeric,Numeric,Numeric,Numeric,Numeric,Numeric,Numeric,Numeric,Numeric,Numeric,Numeric,Numeric,Numeric,Numeric,Numeric,Numeric,Numeric,Text,Text
XXXXX,,,321651,321651,82700,909761,1227393,0.0761,1737498.63,0.6629,1150695.98,799.9,60.5,66.38,0,36.1,,,,11432,6%,12%,10%,23%,4%,11%,6%,7%,2%,7%,0%,0%,11%,Consumer Business,Manufacturing,125.81,42%,Guidance Text,Guidance Text";

            // Test a few key columns
            using (var stream1 = new MemoryStream(Encoding.UTF8.GetBytes(csvContent)))
            {
                var result = InvokePrivateMethod<byte>(_service, "DetermineColumnDataTypeFromCsv", stream1, 0); // Employee ID
                Assert.AreEqual((byte)BdoPartner.Plans.Common.Enumerations.PartnerReferenceDataColumnType.Text, result, "Employee ID should be Text");
            }

            using (var stream2 = new MemoryStream(Encoding.UTF8.GetBytes(csvContent)))
            {
                var result = InvokePrivateMethod<byte>(_service, "DetermineColumnDataTypeFromCsv", stream2, 3); // Est Rev Won - Originator
                Assert.AreEqual((byte)BdoPartner.Plans.Common.Enumerations.PartnerReferenceDataColumnType.Numeric, result, "Est Rev Won - Originator should be Numeric");
            }

            using (var stream3 = new MemoryStream(Encoding.UTF8.GetBytes(csvContent)))
            {
                var result = InvokePrivateMethod<byte>(_service, "DetermineColumnDataTypeFromCsv", stream3, 38); // Est Rev Won - Originator Guidance
                Assert.AreEqual((byte)BdoPartner.Plans.Common.Enumerations.PartnerReferenceDataColumnType.Text, result, "Guidance columns should be Text");
            }
        }

        /// <summary>
        /// Helper method to invoke private methods for testing
        /// </summary>
        private T InvokePrivateMethod<T>(object obj, string methodName, params object[] parameters)
        {
            var method = obj.GetType().GetMethod(methodName, BindingFlags.NonPublic | BindingFlags.Instance);
            Assert.IsNotNull(method, $"Method '{methodName}' not found");

            var result = method.Invoke(obj, parameters);
            return (T)result;
        }
    }
}
