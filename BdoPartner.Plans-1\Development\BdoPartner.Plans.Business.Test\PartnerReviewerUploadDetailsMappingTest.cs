using AutoMapper;
using BdoPartner.Plans.Model.Mapper;
using NUnit.Framework;
using Entity = BdoPartner.Plans.Model.Entity;
using DTO = BdoPartner.Plans.Model.DTO;
using System;

namespace BdoPartner.Plans.Business.Test
{
    /// <summary>
    /// Unit test to verify PartnerReviewerUploadDetails entity to DTO mapping works correctly including RowId
    /// </summary>
    [TestFixture]
    public class PartnerReviewerUploadDetailsMappingTest
    {
        private IMapper _mapper;

        [SetUp]
        public void Setup()
        {
            // Configure AutoMapper with EntityProfile
            var mapperConfig = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile(typeof(EntityProfile));
            });

            _mapper = new Mapper(mapperConfig);
        }

        [Test]
        public void TestEntityToDtoMapping_IncludesRowId()
        {
            // Arrange
            var entity = new Entity.PartnerReviewerUploadDetails
            {
                Id = 1,
                PartnerReviewerUploadId = 100,
                RowId = 5,
                EmployeeId = "12345",
                EmployeeName = "<PERSON>",
                Exempt = "N",
                LeadershipRole = "SLT",
                PrimaryReviewerId = "67890",
                PrimaryReviewerName = "Jane Smith",
                SecondaryReviewerId = "11111",
                SecondaryReviewerName = "Bob Johnson",
                ValidationError = null,
                CreatedBy = Guid.NewGuid(),
                CreatedByName = "<EMAIL>",
                CreatedOn = DateTime.UtcNow,
                ModifiedBy = Guid.NewGuid(),
                ModifiedByName = "<EMAIL>",
                ModifiedOn = DateTime.UtcNow
            };

            // Act
            var dto = _mapper.Map<DTO.PartnerReviewerUploadDetails>(entity);

            // Assert
            Assert.IsNotNull(dto);
            Assert.AreEqual(entity.Id, dto.Id);
            Assert.AreEqual(entity.PartnerReviewerUploadId, dto.PartnerReviewerUploadId);
            Assert.AreEqual(entity.RowId, dto.RowId);
            Assert.AreEqual(entity.EmployeeId, dto.EmployeeId);
            Assert.AreEqual(entity.EmployeeName, dto.EmployeeName);
            Assert.AreEqual(entity.Exempt, dto.Exempt);
            Assert.AreEqual(entity.LeadershipRole, dto.LeadershipRole);
            Assert.AreEqual(entity.PrimaryReviewerId, dto.PrimaryReviewerId);
            Assert.AreEqual(entity.PrimaryReviewerName, dto.PrimaryReviewerName);
            Assert.AreEqual(entity.SecondaryReviewerId, dto.SecondaryReviewerId);
            Assert.AreEqual(entity.SecondaryReviewerName, dto.SecondaryReviewerName);
            Assert.AreEqual(entity.ValidationError, dto.ValidationError);
            
            // Verify computed properties
            Assert.IsTrue(dto.IsValid); // ValidationError is null
            Assert.IsFalse(dto.ExemptBoolean); // Exempt is "N"
        }

        [Test]
        public void TestDtoToEntityMapping_IncludesRowId()
        {
            // Arrange
            var dto = new DTO.PartnerReviewerUploadDetails
            {
                Id = 1,
                PartnerReviewerUploadId = 100,
                RowId = 3,
                EmployeeId = "12345",
                EmployeeName = "John Doe",
                Exempt = "Y",
                LeadershipRole = "ELT",
                PrimaryReviewerId = "67890",
                PrimaryReviewerName = "Jane Smith",
                SecondaryReviewerId = "11111",
                SecondaryReviewerName = "Bob Johnson",
                ValidationError = "Some error",
                CreatedBy = Guid.NewGuid(),
                CreatedByName = "<EMAIL>",
                CreatedOn = DateTime.UtcNow,
                ModifiedBy = Guid.NewGuid(),
                ModifiedByName = "<EMAIL>",
                ModifiedOn = DateTime.UtcNow
            };

            // Act
            var entity = _mapper.Map<Entity.PartnerReviewerUploadDetails>(dto);

            // Assert
            Assert.IsNotNull(entity);
            Assert.AreEqual(dto.Id, entity.Id);
            Assert.AreEqual(dto.PartnerReviewerUploadId, entity.PartnerReviewerUploadId);
            Assert.AreEqual(dto.RowId, entity.RowId);
            Assert.AreEqual(dto.EmployeeId, entity.EmployeeId);
            Assert.AreEqual(dto.EmployeeName, entity.EmployeeName);
            Assert.AreEqual(dto.Exempt, entity.Exempt);
            Assert.AreEqual(dto.LeadershipRole, entity.LeadershipRole);
            Assert.AreEqual(dto.PrimaryReviewerId, entity.PrimaryReviewerId);
            Assert.AreEqual(dto.PrimaryReviewerName, entity.PrimaryReviewerName);
            Assert.AreEqual(dto.SecondaryReviewerId, entity.SecondaryReviewerId);
            Assert.AreEqual(dto.SecondaryReviewerName, entity.SecondaryReviewerName);
            Assert.AreEqual(dto.ValidationError, entity.ValidationError);
        }

        [Test]
        public void TestExemptBooleanMapping()
        {
            // Test "Y" maps to true
            var entityY = new Entity.PartnerReviewerUploadDetails { Exempt = "Y" };
            var dtoY = _mapper.Map<DTO.PartnerReviewerUploadDetails>(entityY);
            Assert.IsTrue(dtoY.ExemptBoolean);

            // Test "N" maps to false
            var entityN = new Entity.PartnerReviewerUploadDetails { Exempt = "N" };
            var dtoN = _mapper.Map<DTO.PartnerReviewerUploadDetails>(entityN);
            Assert.IsFalse(dtoN.ExemptBoolean);

            // Test null/empty maps to false
            var entityNull = new Entity.PartnerReviewerUploadDetails { Exempt = null };
            var dtoNull = _mapper.Map<DTO.PartnerReviewerUploadDetails>(entityNull);
            Assert.IsFalse(dtoNull.ExemptBoolean);
        }

        [Test]
        public void TestIsValidMapping()
        {
            // Test null ValidationError maps to true
            var entityValid = new Entity.PartnerReviewerUploadDetails { ValidationError = null };
            var dtoValid = _mapper.Map<DTO.PartnerReviewerUploadDetails>(entityValid);
            Assert.IsTrue(dtoValid.IsValid);

            // Test empty ValidationError maps to true
            var entityValidEmpty = new Entity.PartnerReviewerUploadDetails { ValidationError = "" };
            var dtoValidEmpty = _mapper.Map<DTO.PartnerReviewerUploadDetails>(entityValidEmpty);
            Assert.IsTrue(dtoValidEmpty.IsValid);

            // Test non-empty ValidationError maps to false
            var entityInvalid = new Entity.PartnerReviewerUploadDetails { ValidationError = "Error message" };
            var dtoInvalid = _mapper.Map<DTO.PartnerReviewerUploadDetails>(entityInvalid);
            Assert.IsFalse(dtoInvalid.IsValid);
        }
    }
}
