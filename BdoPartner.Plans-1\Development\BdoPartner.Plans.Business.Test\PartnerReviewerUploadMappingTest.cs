using AutoMapper;
using BdoPartner.Plans.Common;
using BdoPartner.Plans.Model.Mapper;
using NUnit.Framework;
using System;
using Entity = BdoPartner.Plans.Model.Entity;
using DTO = BdoPartner.Plans.Model.DTO;

namespace BdoPartner.Plans.Business.Test
{
    /// <summary>
    /// Unit test to verify PartnerReviewerUpload entity to DTO mapping works correctly with enumeration status
    /// </summary>
    [TestFixture]
    public class PartnerReviewerUploadMappingTest
    {
        private IMapper _mapper;

        [SetUp]
        public void Setup()
        {
            // Configure AutoMapper with EntityProfile
            var mapperConfig = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile(typeof(EntityProfile));
            });

            _mapper = new Mapper(mapperConfig);
        }

        [Test]
        public void TestEntityToDtoMapping_StatusEnumeration()
        {
            // Arrange
            var entity = new Entity.PartnerReviewerUpload
            {
                Id = 1,
                Years = "2024",
                UploadFileName = "test.xlsx",
                ValidationSummary = "Test summary",
                Status = (byte)Enumerations.PartnerReviewerUploadStatus.ValidationPassed, // byte value 3
                CreatedBy = Guid.NewGuid(),
                CreatedOn = DateTime.UtcNow,
                ModifiedBy = Guid.NewGuid(),
                ModifiedOn = DateTime.UtcNow
            };

            // Act
            var dto = _mapper.Map<DTO.PartnerReviewerUpload>(entity);

            // Assert
            Assert.IsNotNull(dto);
            Assert.AreEqual(entity.Id, dto.Id);
            Assert.AreEqual(entity.Years, dto.Years);
            Assert.AreEqual(entity.UploadFileName, dto.UploadFileName);
            Assert.AreEqual(entity.ValidationSummary, dto.ValidationSummary);
            
            // Verify status enumeration mapping
            Assert.AreEqual(Enumerations.PartnerReviewerUploadStatus.ValidationPassed, dto.Status);
            Assert.AreEqual("Validation Passed", dto.StatusString);
            
            Assert.AreEqual(entity.CreatedBy, dto.CreatedBy);
            Assert.AreEqual(entity.CreatedOn, dto.CreatedOn);
            Assert.AreEqual(entity.ModifiedBy, dto.ModifiedBy);
            Assert.AreEqual(entity.ModifiedOn, dto.ModifiedOn);
        }

        [Test]
        public void TestDtoToEntityMapping_StatusEnumeration()
        {
            // Arrange
            var dto = new DTO.PartnerReviewerUpload
            {
                Id = 1,
                Years = "2024",
                UploadFileName = "test.xlsx",
                ValidationSummary = "Test summary",
                Status = Enumerations.PartnerReviewerUploadStatus.Submitted, // enum value
                StatusString = "Submitted", // This should be ignored in reverse mapping
                CreatedBy = Guid.NewGuid(),
                CreatedOn = DateTime.UtcNow,
                ModifiedBy = Guid.NewGuid(),
                ModifiedOn = DateTime.UtcNow
            };

            // Act
            var entity = _mapper.Map<Entity.PartnerReviewerUpload>(dto);

            // Assert
            Assert.IsNotNull(entity);
            Assert.AreEqual(dto.Id, entity.Id);
            Assert.AreEqual(dto.Years, entity.Years);
            Assert.AreEqual(dto.UploadFileName, entity.UploadFileName);
            Assert.AreEqual(dto.ValidationSummary, entity.ValidationSummary);
            
            // Verify status byte mapping (enum value 5 should become byte 5)
            Assert.AreEqual((byte)Enumerations.PartnerReviewerUploadStatus.Submitted, entity.Status);
            Assert.AreEqual(5, entity.Status);
            
            Assert.AreEqual(dto.CreatedBy, entity.CreatedBy);
            Assert.AreEqual(dto.CreatedOn, entity.CreatedOn);
            Assert.AreEqual(dto.ModifiedBy, entity.ModifiedBy);
            Assert.AreEqual(dto.ModifiedOn, entity.ModifiedOn);
        }

        [Test]
        public void TestAllStatusValues_EntityToDto()
        {
            // Test all enumeration values to ensure they map correctly
            var statusValues = new[]
            {
                (byte)Enumerations.PartnerReviewerUploadStatus.Uploading,
                (byte)Enumerations.PartnerReviewerUploadStatus.Uploaded,
                (byte)Enumerations.PartnerReviewerUploadStatus.Validating,
                (byte)Enumerations.PartnerReviewerUploadStatus.ValidationPassed,
                (byte)Enumerations.PartnerReviewerUploadStatus.ValidationFailed,
                (byte)Enumerations.PartnerReviewerUploadStatus.Submitted
            };

            var expectedEnumValues = new[]
            {
                Enumerations.PartnerReviewerUploadStatus.Uploading,
                Enumerations.PartnerReviewerUploadStatus.Uploaded,
                Enumerations.PartnerReviewerUploadStatus.Validating,
                Enumerations.PartnerReviewerUploadStatus.ValidationPassed,
                Enumerations.PartnerReviewerUploadStatus.ValidationFailed,
                Enumerations.PartnerReviewerUploadStatus.Submitted
            };

            var expectedStatusStrings = new[]
            {
                "Uploading",
                "Uploaded",
                "Validating",
                "Validation Passed",
                "Validation Failed",
                "Submitted"
            };

            for (int i = 0; i < statusValues.Length; i++)
            {
                // Arrange
                var entity = new Entity.PartnerReviewerUpload
                {
                    Id = i + 1,
                    Years = "2024",
                    Status = statusValues[i]
                };

                // Act
                var dto = _mapper.Map<DTO.PartnerReviewerUpload>(entity);

                // Assert
                Assert.AreEqual(expectedEnumValues[i], dto.Status, $"Failed for status value {statusValues[i]}");
                Assert.AreEqual(expectedStatusStrings[i], dto.StatusString, $"Failed for status string {statusValues[i]}");
            }
        }

        [Test]
        public void TestAllStatusValues_DtoToEntity()
        {
            var enumValues = new[]
            {
                Enumerations.PartnerReviewerUploadStatus.Uploading,
                Enumerations.PartnerReviewerUploadStatus.Uploaded,
                Enumerations.PartnerReviewerUploadStatus.Validating,
                Enumerations.PartnerReviewerUploadStatus.ValidationPassed,
                Enumerations.PartnerReviewerUploadStatus.ValidationFailed,
                Enumerations.PartnerReviewerUploadStatus.Submitted
            };

            var expectedByteValues = new byte[] { 0, 1, 2, 3, 4, 5 };

            for (int i = 0; i < enumValues.Length; i++)
            {
                // Arrange
                var dto = new DTO.PartnerReviewerUpload
                {
                    Id = i + 1,
                    Years = "2024",
                    Status = enumValues[i]
                };

                // Act
                var entity = _mapper.Map<Entity.PartnerReviewerUpload>(dto);

                // Assert
                Assert.AreEqual(expectedByteValues[i], entity.Status, $"Failed for enum value {enumValues[i]}");
            }
        }
    }
}
