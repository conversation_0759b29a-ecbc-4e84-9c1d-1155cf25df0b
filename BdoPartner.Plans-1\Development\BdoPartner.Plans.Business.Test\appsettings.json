﻿{
  "ConnectionStrings": {
    //
    // it is local sql server or sql server expression database.
    //
    "DatabaseConnection": "Server=.\\SQLExpress;Database=BdoPartner.Plans.Database;Trusted_Connection=True;"
    //
    // Connect to MySQL database.
    //
    // "DatabaseConnection": "User Id=root;Host=localhost;Database=TestDB;Password=Password1;TreatTinyAsBoolean=false"
  },
  "App": {
    //
    // Define if unit test using EntityFrameworkCore in memory sql server database or on-premise sql server database.
    //
    "IsInMemoryDatabase": true

  },
  "Logging": {
    "IncludeScopes": false,
    "LogLevel": {
      "Default": "Debug",
      "System": "Information",
      "Microsoft": "Information"
    }
  }
}
