{"format": 1, "restore": {"C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Business.Test\\BdoPartner.Plans.Business.Test.csproj": {}}, "projects": {"C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Business.Interface\\BdoPartner.Plans.Business.Interface.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Business.Interface\\BdoPartner.Plans.Business.Interface.csproj", "projectName": "BdoPartner.Plans.Business.Interface", "projectPath": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Business.Interface\\BdoPartner.Plans.Business.Interface.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Business.Interface\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net5.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net5.0": {"targetAlias": "net5.0", "projectReferences": {"C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Common\\BdoPartner.Plans.Common.csproj": {"projectPath": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Common\\BdoPartner.Plans.Common.csproj"}, "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.DataAccess.Common\\BdoPartner.Plans.DataAccess.Common.csproj": {"projectPath": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.DataAccess.Common\\BdoPartner.Plans.DataAccess.Common.csproj"}, "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Model.DTO\\BdoPartner.Plans.Model.DTO.csproj": {"projectPath": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Model.DTO\\BdoPartner.Plans.Model.DTO.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net5.0": {"targetAlias": "net5.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.300\\RuntimeIdentifierGraph.json"}}}, "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Business.Test\\BdoPartner.Plans.Business.Test.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Business.Test\\BdoPartner.Plans.Business.Test.csproj", "projectName": "BdoPartner.Plans.Business.Test", "projectPath": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Business.Test\\BdoPartner.Plans.Business.Test.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Business.Test\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net5.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net5.0": {"targetAlias": "net5.0", "projectReferences": {"C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Business.Interface\\BdoPartner.Plans.Business.Interface.csproj": {"projectPath": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Business.Interface\\BdoPartner.Plans.Business.Interface.csproj"}, "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Business\\BdoPartner.Plans.Business.csproj": {"projectPath": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Business\\BdoPartner.Plans.Business.csproj"}, "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Common\\BdoPartner.Plans.Common.csproj": {"projectPath": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Common\\BdoPartner.Plans.Common.csproj"}, "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.DataAccess\\BdoPartner.Plans.DataAccess.csproj": {"projectPath": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.DataAccess\\BdoPartner.Plans.DataAccess.csproj"}, "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Model.DTO\\BdoPartner.Plans.Model.DTO.csproj": {"projectPath": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Model.DTO\\BdoPartner.Plans.Model.DTO.csproj"}, "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Model.Entity\\BdoPartner.Plans.Model.Entity.csproj": {"projectPath": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Model.Entity\\BdoPartner.Plans.Model.Entity.csproj"}, "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Model.Mapper\\BdoPartner.Plans.Model.Mapper.csproj": {"projectPath": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Model.Mapper\\BdoPartner.Plans.Model.Mapper.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net5.0": {"targetAlias": "net5.0", "dependencies": {"Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[16.9.4, )"}, "Moq": {"target": "Package", "version": "[4.16.1, )"}, "NUnit": {"target": "Package", "version": "[3.13.1, )"}, "NUnit3TestAdapter": {"target": "Package", "version": "[3.17.0, )"}, "coverlet.collector": {"target": "Package", "version": "[3.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.300\\RuntimeIdentifierGraph.json"}}}, "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Business\\BdoPartner.Plans.Business.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Business\\BdoPartner.Plans.Business.csproj", "projectName": "BdoPartner.Plans.Business", "projectPath": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Business\\BdoPartner.Plans.Business.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Business\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net5.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net5.0": {"targetAlias": "net5.0", "projectReferences": {"C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Business.Interface\\BdoPartner.Plans.Business.Interface.csproj": {"projectPath": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Business.Interface\\BdoPartner.Plans.Business.Interface.csproj"}, "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Common\\BdoPartner.Plans.Common.csproj": {"projectPath": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Common\\BdoPartner.Plans.Common.csproj"}, "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.DataAccess\\BdoPartner.Plans.DataAccess.csproj": {"projectPath": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.DataAccess\\BdoPartner.Plans.DataAccess.csproj"}, "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Model.Mapper\\BdoPartner.Plans.Model.Mapper.csproj": {"projectPath": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Model.Mapper\\BdoPartner.Plans.Model.Mapper.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net5.0": {"targetAlias": "net5.0", "dependencies": {"EPPlus": {"target": "Package", "version": "[5.8.4, )"}, "IdentityServer4": {"target": "Package", "version": "[4.1.2, )"}, "Microsoft.AspNetCore.Http": {"target": "Package", "version": "[2.2.2, )"}, "Microsoft.Identity.Web.MicrosoftGraph": {"target": "Package", "version": "[1.14.0, )"}, "Microsoft.IdentityModel.Clients.ActiveDirectory": {"target": "Package", "version": "[5.2.9, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.300\\RuntimeIdentifierGraph.json"}}}, "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Common\\BdoPartner.Plans.Common.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Common\\BdoPartner.Plans.Common.csproj", "projectName": "BdoPartner.Plans.Common", "projectPath": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Common\\BdoPartner.Plans.Common.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Common\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net5.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net5.0": {"targetAlias": "net5.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net5.0": {"targetAlias": "net5.0", "dependencies": {"IdentityModel": {"target": "Package", "version": "[5.1.0, )"}, "Microsoft.AspNetCore.Authentication": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.AspNetCore.Hosting": {"target": "Package", "version": "[2.2.7, )"}, "Microsoft.AspNetCore.Http.Abstractions": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[5.0.0, )"}, "Microsoft.Extensions.Configuration.Binder": {"target": "Package", "version": "[5.0.0, )"}, "Microsoft.Extensions.Configuration.EnvironmentVariables": {"target": "Package", "version": "[5.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[5.0.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[5.0.2, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[5.0.0, )"}, "Microsoft.Extensions.Logging.ApplicationInsights": {"target": "Package", "version": "[2.17.0, )"}, "Microsoft.Extensions.Logging.AzureAppServices": {"target": "Package", "version": "[5.0.8, )"}, "Microsoft.Extensions.Options": {"target": "Package", "version": "[5.0.0, )"}, "Microsoft.IdentityModel.Clients.ActiveDirectory": {"target": "Package", "version": "[5.2.9, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.300\\RuntimeIdentifierGraph.json"}}}, "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.DataAccess.Common\\BdoPartner.Plans.DataAccess.Common.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.DataAccess.Common\\BdoPartner.Plans.DataAccess.Common.csproj", "projectName": "BdoPartner.Plans.DataAccess.Common", "projectPath": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.DataAccess.Common\\BdoPartner.Plans.DataAccess.Common.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.DataAccess.Common\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net5.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net5.0": {"targetAlias": "net5.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net5.0": {"targetAlias": "net5.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[10.1.1, )"}, "Microsoft.Data.SqlClient": {"target": "Package", "version": "[3.0.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[5.0.8, )"}, "Microsoft.EntityFrameworkCore.InMemory": {"target": "Package", "version": "[5.0.8, )"}, "Microsoft.EntityFrameworkCore.Proxies": {"target": "Package", "version": "[5.0.8, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[5.0.8, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.300\\RuntimeIdentifierGraph.json"}}}, "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.DataAccess\\BdoPartner.Plans.DataAccess.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.DataAccess\\BdoPartner.Plans.DataAccess.csproj", "projectName": "BdoPartner.Plans.DataAccess", "projectPath": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.DataAccess\\BdoPartner.Plans.DataAccess.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.DataAccess\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net5.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net5.0": {"targetAlias": "net5.0", "projectReferences": {"C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.DataAccess.Common\\BdoPartner.Plans.DataAccess.Common.csproj": {"projectPath": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.DataAccess.Common\\BdoPartner.Plans.DataAccess.Common.csproj"}, "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Model.Entity\\BdoPartner.Plans.Model.Entity.csproj": {"projectPath": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Model.Entity\\BdoPartner.Plans.Model.Entity.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net5.0": {"targetAlias": "net5.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.300\\RuntimeIdentifierGraph.json"}}}, "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Model.DTO\\BdoPartner.Plans.Model.DTO.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Model.DTO\\BdoPartner.Plans.Model.DTO.csproj", "projectName": "BdoPartner.Plans.Model.DTO", "projectPath": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Model.DTO\\BdoPartner.Plans.Model.DTO.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Model.DTO\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net5.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net5.0": {"targetAlias": "net5.0", "projectReferences": {"C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Common\\BdoPartner.Plans.Common.csproj": {"projectPath": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Common\\BdoPartner.Plans.Common.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net5.0": {"targetAlias": "net5.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.300\\RuntimeIdentifierGraph.json"}}}, "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Model.Entity\\BdoPartner.Plans.Model.Entity.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Model.Entity\\BdoPartner.Plans.Model.Entity.csproj", "projectName": "BdoPartner.Plans.Model.Entity", "projectPath": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Model.Entity\\BdoPartner.Plans.Model.Entity.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Model.Entity\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net5.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net5.0": {"targetAlias": "net5.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net5.0": {"targetAlias": "net5.0", "dependencies": {"Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[5.0.7, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[5.0.7, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[5.0.7, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.300\\RuntimeIdentifierGraph.json"}}}, "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Model.Mapper\\BdoPartner.Plans.Model.Mapper.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Model.Mapper\\BdoPartner.Plans.Model.Mapper.csproj", "projectName": "BdoPartner.Plans.Model.Mapper", "projectPath": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Model.Mapper\\BdoPartner.Plans.Model.Mapper.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Model.Mapper\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net5.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net5.0": {"targetAlias": "net5.0", "projectReferences": {"C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Model.DTO\\BdoPartner.Plans.Model.DTO.csproj": {"projectPath": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Model.DTO\\BdoPartner.Plans.Model.DTO.csproj"}, "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Model.Entity\\BdoPartner.Plans.Model.Entity.csproj": {"projectPath": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Model.Entity\\BdoPartner.Plans.Model.Entity.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net5.0": {"targetAlias": "net5.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[10.1.1, )"}, "AutoMapper.Extensions.Microsoft.DependencyInjection": {"target": "Package", "version": "[8.1.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.300\\RuntimeIdentifierGraph.json"}}}}}