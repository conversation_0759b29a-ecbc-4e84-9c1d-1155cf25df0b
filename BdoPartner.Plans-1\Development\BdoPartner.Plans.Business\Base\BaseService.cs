﻿using System;
using System.Collections.Generic;
using BdoPartner.Plans.DataAccess;
using DTO = BdoPartner.Plans.Model.DTO;
using Microsoft.AspNetCore.Http;
using BdoPartner.Plans.Common;
using System.Linq;
using IdentityModel;
using Microsoft.Extensions.Logging;
using BdoPartner.Plans.Common.Config;
using System.Threading.Tasks;
using System.Net.Http;
using System.Net.Http.Headers;
using Newtonsoft.Json;
using System.Net;
using BdoPartner.Plans.Model.DTO.Identity;

namespace BdoPartner.Plans.Business
{
    /// <summary>
    ///  Business layer base class. shared methods and properties for child business service stay here. 
    /// </summary>
    public class BaseService : IDisposable
    {
        private IUnitOfWork _unitOfWork;

        protected IHttpContextAccessor _httpContextAccessor = null;
        private readonly ILogger<BaseService> _logger = null;
        private readonly IConfigSettings _config = null;

        /// <summary>
        ///  Dependency inject unit of work. Work for web api environment.
        /// </summary>
        /// <param name="uow">UnitOfWork dependency injection for database.</param>
        /// <param name="httpContextAccessor">HttpContext dependency injection.</param>
        /// <param name="logger">File logger dependency injection.</param>
        public BaseService(IUnitOfWork uow, IHttpContextAccessor httpContextAccessor, IConfigSettings config, ILogger<BaseService> logger)
        {
            this._unitOfWork = uow;

            this._httpContextAccessor = httpContextAccessor;
            this._logger = logger;
            this._config = config;
        }

        /// <summary>
        ///  Dependency inject unit of work. Work for none web environment such as window service, window console apps.
        /// </summary>
        /// <param name="uow">UnitOfWork dependency injection for database.</param>
        /// <param name="config"></param>
        /// <param name="logger"></param>
        public BaseService(IUnitOfWork uow, IConfigSettings config, ILogger<BaseService> logger)
        {
            this._unitOfWork = uow;

            this._logger = logger;
            this._config = config;
        }

        public void Dispose()
        {
            if (this._unitOfWork != null)
            {
                this._unitOfWork.Dispose();
            }
        }

        /// <summary>
        ///  File logger. Corporate with appsettings.json, setcion "Logging:File".
        /// </summary>
        protected ILogger Logger { get { return this._logger; } }


        /// <summary>
        /// It is Unit Of Work for sql server database in this project. 
        /// </summary>
        protected IUnitOfWork UOW { get { return this._unitOfWork; } }


        protected IConfigSettings Config { get { return this._config; } }

        /// <summary>
        /// Get current processing web site's shared information. 
        /// Such as Application Id, selected language, selected Site etc.
        /// </summary>
        protected DTO.AppContext CurrentAppContext
        {
            get
            {
                DTO.AppContext result = null;

                if (_httpContextAccessor != null && _httpContextAccessor.HttpContext.Items.TryGetValue(Constants.CURRENT_APP_CONTEXT, out var tmpResult))
                {
                    if (tmpResult != null)
                    {
                        result = (DTO.AppContext)tmpResult;
                    }
                }

                return result;
            }
        }

        /// <summary>
        ///  Get current logon user from HttpContext.User. Web associated.
        /// </summary>
        protected DTO.Identity.User CurrentUser
        {
            get
            {
                DTO.Identity.User result = new DTO.Identity.User();

                if (_httpContextAccessor != null)
                {
                    result = this._httpContextAccessor.HttpContext.User.ToIdentityUser();
                }
                return result;
            }

        }

        /// <summary>
        ///  Current UTC datetime. 
        /// </summary>
        protected DateTime CurrentDateTime
        {
            get { return DateTime.UtcNow; }
        }       
    }
}
