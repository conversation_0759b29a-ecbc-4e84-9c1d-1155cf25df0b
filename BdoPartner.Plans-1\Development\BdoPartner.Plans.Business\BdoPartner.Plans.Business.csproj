﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net5.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <Folder Include="Base\" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="EPPlus" Version="5.8.4" />
    <PackageReference Include="IdentityServer4" Version="4.1.2" />
    <PackageReference Include="Microsoft.AspNetCore.Http" Version="2.2.2" />
    <PackageReference Include="Microsoft.Identity.Web.MicrosoftGraph" Version="1.14.0" />
    <PackageReference Include="Microsoft.IdentityModel.Clients.ActiveDirectory" Version="5.2.9" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\BdoPartner.Plans.Business.Interface\BdoPartner.Plans.Business.Interface.csproj" />
    <ProjectReference Include="..\BdoPartner.Plans.Common\BdoPartner.Plans.Common.csproj" />
    <ProjectReference Include="..\BdoPartner.Plans.DataAccess\BdoPartner.Plans.DataAccess.csproj" />
    <ProjectReference Include="..\BdoPartner.Plans.Model.Mapper\BdoPartner.Plans.Model.Mapper.csproj" />
  </ItemGroup>

</Project>
