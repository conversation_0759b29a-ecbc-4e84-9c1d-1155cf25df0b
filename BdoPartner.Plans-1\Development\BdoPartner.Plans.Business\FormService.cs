using AutoMapper;
using BdoPartner.Plans.Business.Interface;
using BdoPartner.Plans.Common;
using BdoPartner.Plans.Common.Config;
using BdoPartner.Plans.Common.Helpers;
using BdoPartner.Plans.DataAccess;
using BdoPartner.Plans.Model.DTO;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using Entity = BdoPartner.Plans.Model.Entity;

namespace BdoPartner.Plans.Business
{
    /// <summary>
    /// Business service implementation for Form entity operations
    /// </summary>
    public class FormService : BaseService, IFormService
    {
        private readonly IMapper _mapper;

        public FormService(IUnitOfWork uow, IConfigSettings config, ILogger<BaseService> logger, 
            IHttpContextAccessor httpContextAccessor, IMapper mapper) 
            : base(uow, httpContextAccessor, config, logger)
        {
            _mapper = mapper;
        }

        public BusinessResult<ICollection<Form>> GetForms()
        {
            var result = new BusinessResult<ICollection<Form>>();
            try
            {
                var forms = UOW.Forms.GetAll().Where(f => f.IsActive == true).ToList();
                result.Item = _mapper.Map<ICollection<Form>>(forms);
                result.ResultStatus = ResultStatus.Success;
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
            }
            return result;
        }

        public BusinessResult<ICollection<Form>> SearchForms(string searchTerm = null, Guid? questionnaireId = null, 
            short? year = null, byte? status = null, string partnerObjectId = null, 
            bool? isActive = null, int pageNumber = 1, int pageSize = 50)
        {
            var result = new BusinessResult<ICollection<Form>>();
            try
            {
                var query = UOW.Forms.GetAll();

                if (!string.IsNullOrEmpty(searchTerm))
                {
                    query = query.Where(f => f.PartnerName.Contains(searchTerm) ||
                                           f.Comments.Contains(searchTerm) ||
                                           f.PartnerEmail.Contains(searchTerm));
                }

                if (questionnaireId.HasValue)
                    query = query.Where(f => f.QuestionnaireId == questionnaireId.Value);

                if (year.HasValue)
                    query = query.Where(f => f.Year == year.Value);

                if (status.HasValue)
                    query = query.Where(f => f.Status == status.Value);

                if (!string.IsNullOrEmpty(partnerObjectId))
                    query = query.Where(f => f.PartnerObjectId == partnerObjectId);

                if (isActive.HasValue)
                    query = query.Where(f => f.IsActive == isActive.Value);

                var forms = query.Skip((pageNumber - 1) * pageSize).Take(pageSize).ToList();
                result.Item = _mapper.Map<ICollection<Form>>(forms);
                result.ResultStatus = ResultStatus.Success;
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
            }
            return result;
        }

        public BusinessResult<Form> GetFormById(Guid id)
        {
            var result = new BusinessResult<Form>();
            try
            {
                var form = UOW.Forms.GetById(id);
                if (form != null)
                {
                    result.Item = _mapper.Map<Form>(form);
                    result.ResultStatus = ResultStatus.Success;
                }
                else
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "Form not found";
                }
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
            }
            return result;
        }

        public BusinessResult<ICollection<Form>> GetFormsByQuestionnaireId(Guid questionnaireId)
        {
            var result = new BusinessResult<ICollection<Form>>();
            try
            {
                var forms = UOW.Forms.GetAll()
                    .Where(f => f.QuestionnaireId == questionnaireId && f.IsActive == true).ToList();
                result.Item = _mapper.Map<ICollection<Form>>(forms);
                result.ResultStatus = ResultStatus.Success;
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
            }
            return result;
        }

        public BusinessResult<ICollection<Form>> GetFormsByPartnerUserId(Guid partnerUserId)
        {
            var result = new BusinessResult<ICollection<Form>>();
            try
            {
                var forms = UOW.Forms.GetAll().Where(f => f.PartnerUserId == partnerUserId && f.IsActive == true).ToList();
                result.Item = _mapper.Map<ICollection<Form>>(forms);
                result.ResultStatus = ResultStatus.Success;
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
            }
            return result;
        }

        public BusinessResult<ICollection<Form>> GetFormsByPartnerObjectId(string partnerObjectId)
        {
            var result = new BusinessResult<ICollection<Form>>();
            try
            {
                var forms = UOW.Forms.GetAll()
                    .Where(f => f.PartnerObjectId == partnerObjectId && f.IsActive == true).ToList();
                result.Item = _mapper.Map<ICollection<Form>>(forms);
                result.ResultStatus = ResultStatus.Success;
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
            }
            return result;
        }

        public BusinessResult<Form> CreateForm(Form form)
        {
            var result = new BusinessResult<Form>();
            try
            {
                var entity = _mapper.Map<Entity.Form>(form);
                entity.Id = Guid.NewGuid();
                entity.CreatedBy = CurrentUser?.Id;
                entity.CreatedByName = CurrentUser?.Email;
                entity.CreatedOn = CurrentDateTime;
                entity.IsActive = true;

                UOW.Forms.Add(entity);

                if (UOW.Commit() > 0)
                {
                    result.Item = _mapper.Map<Form>(entity);
                    result.ResultStatus = ResultStatus.Success;
                }
                else
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "Failed to create form";
                }
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
            }
            return result;
        }

        public BusinessResult<Form> UpdateForm(Form form)
        {
            var result = new BusinessResult<Form>();
            try
            {
                var existingEntity = UOW.Forms.GetById(form.Id);
                if (existingEntity != null)
                {
                    _mapper.Map(form, existingEntity);
                    existingEntity.ModifiedBy = CurrentUser?.Id;
                    existingEntity.ModifiedOn = CurrentDateTime;

                    UOW.Forms.Update(existingEntity);

                    if (UOW.Commit() > 0)
                    {
                        result.Item = _mapper.Map<Form>(existingEntity);
                        result.ResultStatus = ResultStatus.Success;
                    }
                    else
                    {
                        result.ResultStatus = ResultStatus.Failure;
                        result.Message = "Failed to update form";
                    }
                }
                else
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "Form not found";
                }
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
            }
            return result;
        }

        public BusinessResult<bool> DeleteForm(Guid id)
        {
            var result = new BusinessResult<bool>();
            try
            {
                var entity = UOW.Forms.GetById(id);
                if (entity != null)
                {
                    entity.IsActive = false;
                    entity.ModifiedBy = CurrentUser?.Id;
                    entity.ModifiedOn = CurrentDateTime;

                    UOW.Forms.Update(entity);

                    if (UOW.Commit() > 0)
                    {
                        result.Item = true;
                        result.ResultStatus = ResultStatus.Success;
                    }
                    else
                    {
                        result.ResultStatus = ResultStatus.Failure;
                        result.Message = "Failed to delete form";
                    }
                }
                else
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "Form not found";
                }
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
            }
            return result;
        }

        public BusinessResult<Form> SubmitForm(Guid id)
        {
            var result = new BusinessResult<Form>();
            try
            {
                var entity = UOW.Forms.GetById(id);
                if (entity != null)
                {
                    entity.Status = (byte)Enumerations.FormStatus.Submitted;
                    entity.PartnerSubmittionDate = CurrentDateTime;
                    entity.ModifiedBy = CurrentUser?.Id;
                    entity.ModifiedOn = CurrentDateTime;

                    UOW.Forms.Update(entity);

                    if (UOW.Commit() > 0)
                    {
                        result.Item = _mapper.Map<Form>(entity);
                        result.ResultStatus = ResultStatus.Success;
                    }
                    else
                    {
                        result.ResultStatus = ResultStatus.Failure;
                        result.Message = "Failed to submit form";
                    }
                }
                else
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "Form not found";
                }
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
            }
            return result;
        }

        public BusinessResult<Form> ApproveForm(Guid id, string approverComments = null)
        {
            var result = new BusinessResult<Form>();
            try
            {
                var entity = UOW.Forms.GetById(id);
                if (entity != null)
                {
                    entity.Status = (byte)Enumerations.FormStatus.Approved;
                    entity.ModifiedBy = CurrentUser?.Id;
                    entity.ModifiedOn = CurrentDateTime;

                    // Note: approverComments parameter is kept for interface compatibility
                    // but not stored as the MpoverallComment field was removed from the entity

                    UOW.Forms.Update(entity);

                    if (UOW.Commit() > 0)
                    {
                        result.Item = _mapper.Map<Form>(entity);
                        result.ResultStatus = ResultStatus.Success;
                    }
                    else
                    {
                        result.ResultStatus = ResultStatus.Failure;
                        result.Message = "Failed to approve form";
                    }
                }
                else
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "Form not found";
                }
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
            }
            return result;
        }

        public BusinessResult<Form> RejectForm(Guid id, string rejectionComments)
        {
            var result = new BusinessResult<Form>();
            try
            {
                var entity = UOW.Forms.GetById(id);
                if (entity != null)
                {
                    entity.Status = (byte)Enumerations.FormStatus.Rejected;
                    entity.ModifiedBy = CurrentUser?.Id;
                    entity.ModifiedOn = CurrentDateTime;

                    // Note: rejectionComments parameter is kept for interface compatibility
                    // but not stored as the MpoverallComment field was removed from the entity

                    UOW.Forms.Update(entity);

                    if (UOW.Commit() > 0)
                    {
                        result.Item = _mapper.Map<Form>(entity);
                        result.ResultStatus = ResultStatus.Success;
                    }
                    else
                    {
                        result.ResultStatus = ResultStatus.Failure;
                        result.Message = "Failed to reject form";
                    }
                }
                else
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "Form not found";
                }
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
            }
            return result;
        }

        public BusinessResult<Form> GetOrCreateFormForUser(Guid questionnaireId, short year, Guid partnerUserId, string partnerObjectId, string partnerName)
        {
            var result = new BusinessResult<Form>();
            try
            {
                // First, try to find existing form for this user and questionnaire
                var existingForm = UOW.Forms.GetAll()
                    .Where(f => f.QuestionnaireId == questionnaireId &&
                               f.Year == year &&
                               f.PartnerUserId == partnerUserId &&
                               f.IsActive == true)
                    .FirstOrDefault();

                if (existingForm != null)
                {
                    // Return existing form
                    result.Item = _mapper.Map<Form>(existingForm);
                    result.ResultStatus = ResultStatus.Success;

                    // Update PartnerEmail if it's missing (for existing forms created before this fix)
                    if (string.IsNullOrEmpty(existingForm.PartnerEmail) && !string.IsNullOrEmpty(CurrentUser?.Email))
                    {
                        existingForm.PartnerEmail = CurrentUser.Email;
                        existingForm.ModifiedBy = CurrentUser.Id;
                        existingForm.ModifiedOn = CurrentDateTime;
                        UOW.Forms.Update(existingForm);
                        UOW.Commit();

                        // Update the DTO as well
                        result.Item.PartnerEmail = CurrentUser.Email;
                    }
                }
                else
                {
                    // Create new form
                    var newForm = new Entity.Form
                    {
                        Id = Guid.NewGuid(),
                        QuestionnaireId = questionnaireId,
                        Year = year,
                        PartnerUserId = partnerUserId,
                        PartnerObjectId = partnerObjectId,
                        PartnerName = partnerName,
                        PartnerEmail = CurrentUser?.Email, // Set the partner email from current user
                        Status = (byte)Enumerations.FormStatus.Draft,
                        IsActive = true,
                        CreatedBy = CurrentUser?.Id,
                        CreatedByName = CurrentUser?.Email,
                        CreatedOn = CurrentDateTime
                    };

                    UOW.Forms.Add(newForm);

                    if (UOW.Commit() > 0)
                    {
                        result.Item = _mapper.Map<Form>(newForm);
                        result.ResultStatus = ResultStatus.Success;
                    }
                    else
                    {
                        result.ResultStatus = ResultStatus.Failure;
                        result.Message = "Failed to create form";
                    }
                }
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
            }
            return result;
        }

        public BusinessResult<Form> GetFormWithUserAnswers(Guid questionnaireId, short year, Guid partnerUserId)
        {
            var result = new BusinessResult<Form>();
            try
            {
                var form = UOW.Forms.GetAll()
                    .Where(f => f.QuestionnaireId == questionnaireId &&
                               f.Year == year &&
                               f.PartnerUserId == partnerUserId &&
                               f.IsActive == true)
                    .FirstOrDefault();

                if (form != null)
                {
                    // Load user answers for this form
                    var userAnswers = UOW.UserAnswers.GetAll()
                        .Where(ua => ua.FormId == form.Id && ua.IsActive == true)
                        .ToList();

                    var formDto = _mapper.Map<Form>(form);
                    formDto.UserAnswers = _mapper.Map<ICollection<UserAnswer>>(userAnswers);

                    result.Item = formDto;
                    result.ResultStatus = ResultStatus.Success;
                }
                else
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "Form not found";
                }
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
            }
            return result;
        }

        public BusinessResult<Questionnaire> GetCurrentYearActiveQuestionnaire(short? year = null)
        {
            var result = new BusinessResult<Questionnaire>();
            try
            {
                var targetYear = year ?? (short)DateTime.Now.Year;

                var questionnaire = UOW.Questionnaires.GetAll()
                    .Where(q => q.Year == targetYear &&
                               q.IsActive == true &&
                               q.Status == (byte)Enumerations.QuestionnaireStatus.Published)
                    .FirstOrDefault();

                if (questionnaire != null)
                {
                    result.Item = _mapper.Map<Questionnaire>(questionnaire);

                    // Decompress JSON fields if they are compressed
                    DecompressQuestionnaireJsonFields(result.Item);

                    result.ResultStatus = ResultStatus.Success;
                }
                else
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = $"The {targetYear} year's partner plan is not ready, please contact administrator.";
                }
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
            }
            return result;
        }

        public BusinessResult<MyPlanData> GetMyPlan(short? year, Guid partnerUserId, string partnerObjectId, string partnerName)
        {
            var result = new BusinessResult<MyPlanData>();
            try
            {
                var targetYear = year ?? (short)DateTime.Now.Year;
                var planData = new MyPlanData
                {
                    Year = targetYear
                };

                // Step 1: Get the current year active questionnaire
                var questionnaire = UOW.Questionnaires.GetAll()
                    .Where(q => q.Year == targetYear &&
                               q.IsActive == true &&
                               q.Status == (byte)Enumerations.QuestionnaireStatus.Published)
                    .FirstOrDefault();

                if (questionnaire == null)
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = $"The {targetYear} year's partner plan is not ready, please contact administrator.";
                    return result;
                }

                planData.Questionnaire = _mapper.Map<Questionnaire>(questionnaire);

                // Decompress JSON fields if they are compressed
                DecompressQuestionnaireJsonFields(planData.Questionnaire);

                // Step 2: Get partner details from Partner table based on current user's email
                Entity.Partner partner = null;
                Entity.PartnerReviewer partnerReviewer = null;

                if (!string.IsNullOrEmpty(CurrentUser?.Email))
                {
                    partner = UOW.Partners.GetAll()
                        .Where(p => p.Mail == CurrentUser.Email && p.IsActive == true)
                        .FirstOrDefault();

                    if (partner != null)
                    {
                        // Get reviewer information for this partner and year
                        partnerReviewer = UOW.PartnerReviewers.GetAll()
                            .Where(pr => pr.PartnerId == partner.Id && pr.Year == targetYear)
                            .FirstOrDefault();
                    }
                }

                // Step 3: Get or create form for the user
                var existingForm = UOW.Forms.GetAll()
                    .Where(f => f.QuestionnaireId == questionnaire.Id &&
                               f.Year == targetYear &&
                               f.PartnerUserId == partnerUserId &&
                               f.IsActive == true)
                    .FirstOrDefault();

                if (existingForm != null)
                {
                    // Use existing form
                    planData.Form = _mapper.Map<Form>(existingForm);
                    planData.IsNewForm = false;

                    // Update PartnerEmail if it's missing (for existing forms created before this fix)
                    if (string.IsNullOrEmpty(existingForm.PartnerEmail) && !string.IsNullOrEmpty(CurrentUser?.Email))
                    {
                        existingForm.PartnerEmail = CurrentUser.Email;
                        existingForm.ModifiedBy = CurrentUser.Id;
                        existingForm.ModifiedOn = CurrentDateTime;
                        UOW.Forms.Update(existingForm);
                        UOW.Commit();

                        // Update the DTO as well
                        planData.Form.PartnerEmail = CurrentUser.Email;
                    }

                    // Enhance form with partner details
                    if (partner != null)
                    {
                        planData.Form.ServiceLine = partner.ServiceLine;
                        planData.Form.SubServiceLine = partner.SubServiceLine;
                        planData.Form.Location = partner.Location;
                        planData.Form.Department = partner.Department;
                        planData.Form.EmployeeId = partner.EmployeeId;

                        // Use partner's display name if available, otherwise keep existing
                        if (!string.IsNullOrEmpty(partner.DisplayName))
                        {
                            planData.Form.PartnerName = partner.DisplayName;
                        }

                        // Ensure email is set from partner record
                        if (!string.IsNullOrEmpty(partner.Mail))
                        {
                            planData.Form.PartnerEmail = partner.Mail;
                        }
                    }

                    // Add reviewer information
                    if (partnerReviewer != null)
                    {
                        planData.Form.PrimaryReviewerName = partnerReviewer.PrimaryReviewerName;
                        planData.Form.SecondaryReviewerName = partnerReviewer.SecondaryReviewerName;
                    }
                }
                else
                {
                    // Create new form - use partner details if available
                    var formPartnerName = partner?.DisplayName ?? partnerName;
                    var formPartnerEmail = partner?.Mail ?? CurrentUser?.Email;

                    var newForm = new Entity.Form
                    {
                        Id = Guid.NewGuid(),
                        QuestionnaireId = questionnaire.Id,
                        Year = targetYear,
                        PartnerUserId = partnerUserId,
                        PartnerObjectId = partnerObjectId,
                        PartnerName = formPartnerName,
                        PartnerEmail = formPartnerEmail,
                        Status = (byte)Enumerations.FormStatus.Draft,
                        IsActive = true,
                        CreatedBy = CurrentUser?.Id,
                        CreatedByName = CurrentUser?.Email,
                        CreatedOn = CurrentDateTime
                    };

                    UOW.Forms.Add(newForm);

                    if (UOW.Commit() > 0)
                    {
                        planData.Form = _mapper.Map<Form>(newForm);
                        planData.IsNewForm = true;

                        // Enhance form with partner details
                        if (partner != null)
                        {
                            planData.Form.ServiceLine = partner.ServiceLine;
                            planData.Form.SubServiceLine = partner.SubServiceLine;
                            planData.Form.Location = partner.Location;
                            planData.Form.Department = partner.Department;
                            planData.Form.EmployeeId = partner.EmployeeId;
                        }

                        // Add reviewer information
                        if (partnerReviewer != null)
                        {
                            planData.Form.PrimaryReviewerName = partnerReviewer.PrimaryReviewerName;
                            planData.Form.SecondaryReviewerName = partnerReviewer.SecondaryReviewerName;
                        }
                    }
                    else
                    {
                        result.ResultStatus = ResultStatus.Failure;
                        result.Message = "Failed to create form";
                        return result;
                    }
                }

                // Step 4: Get existing user answers if available
                var userAnswer = UOW.UserAnswers.GetAll()
                    .Where(ua => ua.FormId == planData.Form.Id && ua.IsActive == true)
                    .FirstOrDefault();

                if (userAnswer != null)
                {
                    planData.UserAnswer = _mapper.Map<UserAnswer>(userAnswer);
                }

                // Step 5: Determine if form is editable
                planData.IsEditable = planData.Form.Status == (byte)Enumerations.FormStatus.Draft ||
                                     planData.Form.Status == (byte)Enumerations.FormStatus.Reopened;

                result.Item = planData;
                result.ResultStatus = ResultStatus.Success;
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
            }
            return result;
        }

        /// <summary>
        /// Helper method to decompress JSON fields in questionnaire DTOs
        /// </summary>
        /// <param name="questionnaire">The questionnaire DTO to process</param>
        private void DecompressQuestionnaireJsonFields(Questionnaire questionnaire)
        {
            if (questionnaire == null) return;

            try
            {
                // Decompress DefinitionJson if it exists and appears to be compressed
                if (!string.IsNullOrEmpty(questionnaire.DefinitionJson))
                {
                    questionnaire.DefinitionJson = CompressionHelper.SafeDecompressJson(questionnaire.DefinitionJson);
                }

                // Decompress DraftDefinitionJson if it exists and appears to be compressed
                if (!string.IsNullOrEmpty(questionnaire.DraftDefinitionJson))
                {
                    questionnaire.DraftDefinitionJson = CompressionHelper.SafeDecompressJson(questionnaire.DraftDefinitionJson);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, $"Error decompressing JSON fields for questionnaire {questionnaire.Id}");
                // Continue execution - don't fail the entire operation due to decompression issues
            }
        }
    }
}
