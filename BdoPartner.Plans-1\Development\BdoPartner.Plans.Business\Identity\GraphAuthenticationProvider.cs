﻿using BdoPartner.Plans.Common.Config;
using Microsoft.Extensions.Configuration;
using Microsoft.Graph;
using Microsoft.IdentityModel.Clients.ActiveDirectory;
using System;
using System.Collections.Generic;
//using System.Linq;
//using System.Net.Http;
//using System.Text;
//using System.Threading.Tasks;

//namespace BdoPartner.Plans.Business.Identity
//{
//    /// <summary>
//    ///  Microsoft Graphy API call required Authentication Provider.
//    ///  Reference: https://www.keithmsmith.com/get-started-microsoft-graph-api-calls-net-core-3/
//    /// </summary>
//    public class GraphAuthenticationProvider : IAuthenticationProvider
//    {
//        public const string GRAPH_URI = "https://graph.microsoft.com/";
//        private string _tenantId { get; set; }
//        private string _clientId { get; set; }
//        private string _clientSecret { get; set; }

//        public GraphAuthenticationProvider(IConfigSettings configSettings)
//        {
//            _tenantId = configSettings.AzureADTenant; //configuration.GetValue<string>("TenantId");
//            _clientId = configSettings.AzureADClientId; //configuration.GetValue<string>("ClientId");
//            _clientSecret = configSettings.AzureADClientSecret; //configuration.GetValue<string>("ClientSecret");
//        }

//        public async Task AuthenticateRequestAsync(HttpRequestMessage request)
//        {
//            AuthenticationContext authContext = new AuthenticationContext($"https://login.microsoftonline.com/{_tenantId}");

//            ClientCredential creds = new ClientCredential(_clientId, _clientSecret);

//            AuthenticationResult authResult = await authContext.AcquireTokenAsync(GRAPH_URI, creds);

//            request.Headers.Add("Authorization", "Bearer " + authResult.AccessToken);
//        }
//    }
//}
