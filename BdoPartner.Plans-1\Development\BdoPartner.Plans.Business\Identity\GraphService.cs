﻿using BdoPartner.Plans.Business.Interface;
using BdoPartner.Plans.Common;
using BdoPartner.Plans.Model.DTO.Identity;
using Microsoft.Graph;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BdoPartner.Plans.Business.Identity
{
    public class GraphService : IGraphService
    {
        private readonly IGraphServiceClient _graphServiceClient;
        public GraphService(IGraphServiceClient graphServiceClient) {
            this._graphServiceClient = graphServiceClient;
        }

        /// <summary>
        ///  Get current logon user profile from Azure AD.
        ///  Called inside Identity Server project.
        /// </summary>
        /// <returns></returns>
        public async Task<BusinessResult<UserProfile>> GetMe() {
            BusinessResult<UserProfile> result = new BusinessResult<UserProfile>();

            Microsoft.Graph.User user = await this._graphServiceClient.Me.Request().GetAsync();

            if (user!=null) {
                UserProfile userProfile = new UserProfile();
                userProfile.ObjectId = user.Id;
                userProfile.UserName = user.UserPrincipalName;
                userProfile.FamilyName = user.Surname;
                userProfile.GivenName = user.GivenName;
                userProfile.JobTitle = user.JobTitle;
                userProfile.EmployeeId = user.EmployeeId;
                userProfile.Department = user.Department;
                userProfile.StreetAddress = user.StreetAddress;
                userProfile.City = user.City;
                userProfile.State = user.State;
                userProfile.PostalCode = user.PostalCode;
                userProfile.Country = user.Country;
                userProfile.Mobilephone = user.MobilePhone;
                userProfile.DisplayName = user.DisplayName;
                userProfile.Email = user.Mail;

                result.Item = userProfile;
                result.ResultStatus = ResultStatus.Success;
            }
            else
            {
                result.Message = "Specified user does not login.";
                result.ResultStatus = ResultStatus.Failure;
            }
            
            return result;
        }

        /// <summary>
        ///  Check if current Azure AD logon user is the specified logon user (object Id).
        ///  Work for Identity Server ProfileService.
        /// </summary>
        /// <param name="objectId">Azure User's object Id.</param>
        /// <returns></returns>
        public async Task<Boolean> IsMe(string objectId) {
            Boolean result = false;
            Microsoft.Graph.User user = await this._graphServiceClient.Me.Request().GetAsync();

            if (user != null && user.Id == objectId) {
                result = true;
            }
            
            return result;
               
        }
    }
}
