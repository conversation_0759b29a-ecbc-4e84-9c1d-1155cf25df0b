﻿using IdentityServer4.Extensions;
using IdentityServer4.Models;
using IdentityServer4.Stores;
using BdoPartner.Plans.Common.Config;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Data;
using Microsoft.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BdoPartner.Plans.Business.Identity
{
    /// <summary>
    ///  Store Identity Server 4 Grand in sql server database table "IdentityServerPersistedGrant".
    /// </summary>
    public class IdentityServerPersistedGrantStore: IPersistedGrantStore
    {
        IConfigSettings _config;
        public IdentityServerPersistedGrantStore(IConfigSettings config)
        {
            _config = config;
        }


        /// <inheritdoc/>
        public async Task StoreAsync(PersistedGrant grant)
        {
            using (var conn = new SqlConnection(_config.DatabaseConnection))
            {
                await RemoveAsync(conn, grant.Key);

                var cmd = conn.CreateCommand();
                cmd.CommandText = @"INSERT INTO [identity].[PersistedGrant]
                                       ([Key]
                                       ,[Type]
                                       ,[SubjectId]
                                       ,[SessionId]
                                       ,[ClientId]
                                       ,[Description]
                                       ,[CreationTime]
                                       ,[Expiration]
                                       ,[ConsumedTime]
                                       ,[Data])
                                 VALUES
                                       (@Key
                                       ,@Type
                                       ,@SubjectId
                                       ,@SessionId
                                       ,@ClientId
                                       ,@Description
                                       ,@CreationTime
                                       ,@Expiration
                                       ,@ConsumedTime
                                       ,@Data)";
                cmd.CommandType = System.Data.CommandType.Text;
                cmd.Parameters.AddRange(new SqlParameter[]
                {
                    new SqlParameter("@Key", SqlDbType.NVarChar,200){ Value= grant.Key},
                    new SqlParameter("@Type", SqlDbType.NVarChar,50){ Value= grant.Type},
                    new SqlParameter("@SubjectId", SqlDbType.NVarChar,200){ Value= string.IsNullOrEmpty(grant.SubjectId)? DBNull.Value:grant.SubjectId},
                    new SqlParameter("@SessionId", SqlDbType.NVarChar,100){ Value= string.IsNullOrEmpty(grant.SessionId)? DBNull.Value:grant.SessionId},
                    new SqlParameter("@ClientId", SqlDbType.NVarChar,200){ Value= grant.ClientId},
                    new SqlParameter("@Description", SqlDbType.NVarChar,200){ Value= string.IsNullOrEmpty(grant.Description)? DBNull.Value:grant.Description},
                    new SqlParameter("@CreationTime", SqlDbType.DateTime2){ Value= grant.CreationTime},
                    new SqlParameter("@Expiration", SqlDbType.DateTime2){ Value= grant.Expiration.HasValue?grant.Expiration: DBNull.Value},
                    new SqlParameter("@ConsumedTime", SqlDbType.DateTime2){ Value= grant.ConsumedTime.HasValue?grant.ConsumedTime:DBNull.Value},
                    new SqlParameter("@Data", SqlDbType.NVarChar, grant.Data.Length){ Value= grant.Data},
                });
                if (conn.State != System.Data.ConnectionState.Open)
                {
                    conn.Open();
                }
                await cmd.ExecuteNonQueryAsync();
            }
        }

        /// <inheritdoc/>
        public async Task<PersistedGrant> GetAsync(string key)
        {
            using (var conn = new SqlConnection(_config.DatabaseConnection))
            {
                var cmd = conn.CreateCommand();
                cmd.CommandText = @"SELECT * FROM [identity].[PersistedGrant] WHERE [Key]=@Key ";
                cmd.CommandType = System.Data.CommandType.Text;
                cmd.Parameters.Add(new SqlParameter("@Key", SqlDbType.NVarChar, 200) { Value = key });
                if (conn.State != System.Data.ConnectionState.Open)
                {
                    conn.Open();
                }
                using (SqlDataReader sdr = await cmd.ExecuteReaderAsync())
                {
                    return GetAll(sdr).FirstOrDefault();
                }
            }
        }

        /// <inheritdoc/>
        public async Task<IEnumerable<PersistedGrant>> GetAllAsync(PersistedGrantFilter filter)
        {
            filter.Validate();

            var sqlAndParam = Filter(filter);
            using (var conn = new SqlConnection(_config.DatabaseConnection))
            {
                var cmd = conn.CreateCommand();
                cmd.CommandText = @"SELECT * FROM [identity].[PersistedGrant] " + sqlAndParam.sql;
                cmd.CommandType = System.Data.CommandType.Text;
                cmd.Parameters.AddRange(sqlAndParam.paramValue);
                if (conn.State != System.Data.ConnectionState.Open)
                {
                    conn.Open();
                }
                using(SqlDataReader sdr = await cmd.ExecuteReaderAsync())
                {
                    return GetAll(sdr);
                }
            }
        }

        private IEnumerable<PersistedGrant> GetAll(SqlDataReader sdr)
        {
            List<PersistedGrant> items = new List<PersistedGrant>();

            while (sdr.Read())
            {
                PersistedGrant it = new PersistedGrant();
                it.Key = sdr.GetString("Key");
                it.Type = sdr.GetString("Type");
                //
                // Note: It is for distinguish external or internal PTAC website clients.
                // value = "internalweb" or "externalweb".
                it.ClientId = sdr.GetString("ClientId");
                it.CreationTime = sdr.GetDateTime("CreationTime");
                it.Data = sdr.GetString("Data");
                //
                // Note: SubjectId reference to dbo.User table primary key column "user_id". Integer value.
                //
                it.SubjectId = sdr["SubjectId"] != DBNull.Value ? sdr.GetString("SubjectId") : string.Empty;
                it.SessionId = sdr["SessionId"] != DBNull.Value ? sdr.GetString("SessionId") : string.Empty;
                it.Description = sdr["Description"] != DBNull.Value ? sdr.GetString("Description") : string.Empty;
                it.Expiration = sdr["Expiration"] != DBNull.Value ? (DateTime?) sdr.GetDateTime("Expiration") : null;
                it.ConsumedTime = sdr["ConsumedTime"] != DBNull.Value ? (DateTime?)sdr.GetDateTime("ConsumedTime") : null;

                items.Add(it);
            }

            return items;
        }

        /// <inheritdoc/>
        public async Task RemoveAsync(string key)
        {
            using (var conn = new SqlConnection(_config.DatabaseConnection))
            {
                await RemoveAsync(conn, key);
            }
        }

        private async Task RemoveAsync(SqlConnection conn, string key)
        {
            var cmd = conn.CreateCommand();
            cmd.CommandText = @"DELETE [identity].[PersistedGrant] where [Key]=@Key";
            cmd.CommandType = System.Data.CommandType.Text;
            cmd.Parameters.AddRange(new SqlParameter[]
            {
                    new SqlParameter("@Key", SqlDbType.NVarChar,200){ Value= key}
            });
            if (conn.State != System.Data.ConnectionState.Open)
            {
                conn.Open();
            }
            await cmd.ExecuteNonQueryAsync();
        }

        /// <inheritdoc/>
        public async Task RemoveAllAsync(PersistedGrantFilter filter)
        {
            filter.Validate();

            var sqlAndParam = Filter(filter);

            using (var conn = new SqlConnection(_config.DatabaseConnection))
            {
                var cmd = conn.CreateCommand();
                cmd.CommandText = @"DELETE [identity].[PersistedGrant] " + sqlAndParam.sql;
                cmd.CommandType = System.Data.CommandType.Text;
                cmd.Parameters.AddRange(sqlAndParam.paramValue);
                if (conn.State != System.Data.ConnectionState.Open)
                {
                    conn.Open();
                }
                await cmd.ExecuteNonQueryAsync();
            }
        }

        private (string sql, SqlParameter[] paramValue) Filter(PersistedGrantFilter filter)
        {
            string sql = " where 1=1 ";
            List<SqlParameter> paramValues = new List<SqlParameter>();

            if (!String.IsNullOrWhiteSpace(filter.ClientId))
            {
                sql += " AND  [ClientId]=@ClientId";
                paramValues.Add(new SqlParameter("@ClientId", SqlDbType.NVarChar, 200) { Value = filter.ClientId });
            }
            if (!String.IsNullOrWhiteSpace(filter.SessionId))
            {
                sql += " AND  [SessionId]=@SessionId";
                paramValues.Add(new SqlParameter("@SessionId", SqlDbType.NVarChar, 100) { Value = filter.SessionId });
            }
            if (!String.IsNullOrWhiteSpace(filter.SubjectId))
            {
                sql += " AND  [SubjectId]=@SubjectId";
                paramValues.Add(new SqlParameter("@SubjectId", SqlDbType.NVarChar, 200) { Value = filter.SubjectId });
            }
            if (!String.IsNullOrWhiteSpace(filter.Type))
            {
                sql += " AND  [Type]=@Type";
                paramValues.Add(new SqlParameter("@Type", SqlDbType.NVarChar, 50) { Value = filter.Type });
            }

            return new(sql, paramValues.ToArray());
        }
    }
}
