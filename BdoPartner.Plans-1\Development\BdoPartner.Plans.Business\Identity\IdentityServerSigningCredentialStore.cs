﻿using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json;
using BdoPartner.Plans.Common.Config;
using BdoPartner.Plans.DataAccess;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Data.SqlClient;
using IdentityServer4.Stores;
using IdentityServer4.Configuration;
using IdentityServer4.Models;

namespace BdoPartner.Plans.Business.Identity
{
    /// <summary>
    ///  Store Identity Server 4 Signing Credentials into Sql Server database tables 
    ///  [identity].[SigningCredential] and [identity].[PersistedGrant]. 
    ///  Note: This class no need to modify. 
    /// </summary>
    public class IdentityServerSigningCredentialStore : ISigningCredentialStore, IValidationKeysStore
    {
        IConfigSettings _config;
        SigningCredentials _signingCredentials;
        SecurityKeyInfo _securityKeyInfo;

        public IdentityServerSigningCredentialStore(IConfigSettings config)
        {
            _config = config;
        }

        public async Task<SigningCredentials> GetSigningCredentialsAsync()
        {
            await MakesureSigningCredentials();

            return _signingCredentials;
        }

        public async Task<IEnumerable<SecurityKeyInfo>> GetValidationKeysAsync()
        {
            await MakesureSigningCredentials();

            return new[] { _securityKeyInfo };
        }


        private async Task MakesureSigningCredentials()
        {
            if (_signingCredentials == null)
            {
                Microsoft.IdentityModel.Tokens.JsonWebKey jwk = null;

                string signingCredential = await GetSigningCredentialFromDb();
                if (string.IsNullOrWhiteSpace(signingCredential))
                {
                    var key = CryptoHelper.CreateRsaSecurityKey();
                    jwk = JsonWebKeyConverter.ConvertFromRSASecurityKey(key);
                    jwk.Alg = "RS256";

                    signingCredential = JsonConvert.SerializeObject(jwk);
                    await StoreSigningCredentialToDb(signingCredential);
                }
                else
                {
                    jwk = new Microsoft.IdentityModel.Tokens.JsonWebKey(signingCredential);
                }

                _signingCredentials = new SigningCredentials(jwk, jwk.Alg);


                _securityKeyInfo = new SecurityKeyInfo
                {
                    Key = _signingCredentials.Key,
                    SigningAlgorithm = _signingCredentials.Algorithm
                };
            }
        }

        private async Task<string> GetSigningCredentialFromDb()
        {
            using(var conn = new SqlConnection(_config.DatabaseConnection))
            {
                var cmd = conn.CreateCommand();
                cmd.CommandText = "select Value from [identity].[SigningCredential]";
                cmd.CommandType = System.Data.CommandType.Text;
                if(conn.State!= System.Data.ConnectionState.Open)
                {
                    conn.Open();
                }
                object dbValue = await cmd.ExecuteScalarAsync();
                if(dbValue!=null && dbValue!= DBNull.Value)
                {
                    return dbValue.ToString();
                }
            }
            return null;
        }

        private async Task StoreSigningCredentialToDb(string signingCredential)
        {
            using (var conn = new SqlConnection(_config.DatabaseConnection))
            {
                var cmd = conn.CreateCommand();
                cmd.CommandText = "insert into [identity].[SigningCredential](Id,Value) values(@Id,@Value)";
                cmd.CommandType = System.Data.CommandType.Text;
                cmd.Parameters.Add(new SqlParameter("@Id", System.Data.SqlDbType.UniqueIdentifier) { Value = Guid.NewGuid() });
                cmd.Parameters.Add(new SqlParameter("@Value", System.Data.SqlDbType.VarChar, 5000) { Value = signingCredential });
                if (conn.State != System.Data.ConnectionState.Open)
                {
                    conn.Open();
                }
                await cmd.ExecuteNonQueryAsync();
            }
        }
    }
}
