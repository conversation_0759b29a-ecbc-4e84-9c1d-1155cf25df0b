﻿using AutoMapper;
using BdoPartner.Plans.Business.Interface;
using BdoPartner.Plans.Common;
using BdoPartner.Plans.Common.Config;
using BdoPartner.Plans.DataAccess;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Text;
using DTO = BdoPartner.Plans.Model.DTO;
using System.Linq;
using BdoPartner.Plans.Model.DTO;

namespace BdoPartner.Plans.Business
{
    public class LookupService : BaseService, ILookupService
    {
        private readonly IMapper _mapper;
     
        /// <summary>
        ///  Dependency inject IUnitOfWork which accessing FirstOnSite.DocumentManagementOrders database.
        /// </summary>
        /// <param name="uow"></param>
        public LookupService(IUnitOfWork uow, IMapper mapper, IHttpContextAccessor httpContextAccessor, IConfigSettings config, ILogger<LookupService> logger)
            : base(uow, httpContextAccessor, config, logger)
        {
            this._mapper = mapper;
        }    

        public List<LookupNum> GetLanguages(bool includeEmptyRow)
        {
            List<LookupNum> result = this.UOW.Languages.GetAll<LookupNum>().OrderBy(r => r.Key).ToList();
            if (includeEmptyRow)
            {
                LookupNum emptyRow = new LookupNum() { Value = string.Empty };
                result.Insert(0, emptyRow);
            }
            return result;
        }

         
        /// <summary>
        /// Get all form statuses with localized display names based on current language
        /// </summary>
        /// <param name="includeEmptyRow">Include empty row for dropdown selection</param>
        /// <returns>List of form statuses</returns>
        public List<LookupNum> GetFormStatuses(bool includeEmptyRow)
        {
            var formStatuses = this.UOW.FormStatuses.GetAll().OrderBy(fs => fs.Id).ToList();
            var currentLanguage = this.CurrentAppContext?.Language ?? Enumerations.Language.EN;

            List<LookupNum> result = formStatuses.Select(fs => new LookupNum
            {
                Key = (int)fs.Id,
                Value = GetLocalizedDisplayName(fs.Name, fs.EnglishDislayName, fs.FrenchDisplayName, currentLanguage)
            }).ToList();

            if (includeEmptyRow)
            {
                LookupNum emptyRow = new LookupNum() { Value = string.Empty };
                result.Insert(0, emptyRow);
            }

            return result;
        }

        /// <summary>
        /// Get all questionnaire statuses with localized display names based on current language
        /// </summary>
        /// <param name="includeEmptyRow">Include empty row for dropdown selection</param>
        /// <returns>List of questionnaire statuses</returns>
        public List<LookupNum> GetQuestionnaireStatuses(bool includeEmptyRow)
        {
            var questionnaireStatuses = this.UOW.QuestionnaireStatuses.GetAll().OrderBy(qs => qs.Id).ToList();
            var currentLanguage = this.CurrentAppContext?.Language ?? Enumerations.Language.EN;

            List<LookupNum> result = questionnaireStatuses.Select(qs => new LookupNum
            {
                Key = (int)qs.Id,
                Value = GetLocalizedDisplayName(qs.Name, qs.EnglishDislayName, qs.FrenchDisplayName, currentLanguage)
            }).ToList();

            if (includeEmptyRow)
            {
                LookupNum emptyRow = new LookupNum() { Value = string.Empty };
                result.Insert(0, emptyRow);
            }

            return result;
        }

        /// <summary>
        /// Get localized display name based on current language
        /// </summary>
        /// <param name="defaultName">Default name (fallback)</param>
        /// <param name="englishName">English display name</param>
        /// <param name="frenchName">French display name</param>
        /// <param name="currentLanguage">Current language setting</param>
        /// <returns>Localized display name</returns>
        private string GetLocalizedDisplayName(string defaultName, string englishName, string frenchName, Enumerations.Language currentLanguage)
        {
            switch (currentLanguage)
            {
                case Enumerations.Language.EN:
                    return !string.IsNullOrWhiteSpace(englishName) ? englishName : defaultName;
                case Enumerations.Language.FR:
                    return !string.IsNullOrWhiteSpace(frenchName) ? frenchName : defaultName;
                default:
                    return !string.IsNullOrWhiteSpace(englishName) ? englishName : defaultName;
            }
        }

        /// <summary>
        ///  Get all records from table dbo.Notification.
        ///  POC purpose only
        /// </summary>
        /// <returns></returns>
        public BusinessResult<ICollection<Notification>> GetNotifications() {
            BusinessResult<ICollection<Notification>> result = new BusinessResult<ICollection<Notification>>();

            result.Item = this.UOW.Notifications.GetAll<Notification>().OrderBy(n => n.Id).ToList();
            result.ResultStatus = ResultStatus.Success;

            return result;
        }
    }
 }
