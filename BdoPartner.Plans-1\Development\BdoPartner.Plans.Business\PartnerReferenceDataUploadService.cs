using AutoMapper;
using BdoPartner.Plans.Business.Interface;
using BdoPartner.Plans.Common;
using BdoPartner.Plans.Common.Config;
using BdoPartner.Plans.DataAccess;
using BdoPartner.Plans.DataAccess.Common.PagedList;
using BdoPartner.Plans.Model.DTO;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using OfficeOpenXml;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Entity = BdoPartner.Plans.Model.Entity;

namespace BdoPartner.Plans.Business
{
    /// <summary>
    /// Business service for Partner Reference Data Upload operations
    /// </summary>
    public class PartnerReferenceDataUploadService : BaseService, IPartnerReferenceDataUploadService
    {
        private readonly IMapper _mapper;
        
        /// <summary>
        /// Constructor with dependency injection
        /// </summary>
        /// <param name="uow">Unit of Work</param>
        /// <param name="httpContextAccessor">HTTP Context Accessor</param>
        /// <param name="config">Configuration Settings</param>
        /// <param name="logger">Logger</param>
        /// <param name="mapper">AutoMapper</param>
        public PartnerReferenceDataUploadService(IUnitOfWork uow, IHttpContextAccessor httpContextAccessor, 
            IConfigSettings config, ILogger<BaseService> logger, IMapper mapper) 
            : base(uow, httpContextAccessor, config, logger)
        {
            _mapper = mapper;
        }

        #region Metadata Operations

        /// <summary>
        /// Get all partner reference data metadata
        /// </summary>
        /// <returns>Collection of partner reference data metadata</returns>
        public BusinessResult<ICollection<PartnerReferenceDataMeta>> GetPartnerReferenceDataMetas()
        {
            var result = new BusinessResult<ICollection<PartnerReferenceDataMeta>>();
            try
            {
                var entities = UOW.PartnerReferenceDataMetas.GetAll().OrderByDescending(x => x.CreatedOn).ToList();
                result.Item = _mapper.Map<ICollection<PartnerReferenceDataMeta>>(entities);
                result.ResultStatus = ResultStatus.Success;
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
            }
            return result;
        }

        /// <summary>
        /// Get partner reference data metadata by ID
        /// </summary>
        /// <param name="id">Metadata ID</param>
        /// <returns>Partner reference data metadata object</returns>
        public BusinessResult<PartnerReferenceDataMeta> GetPartnerReferenceDataMetaById(Guid id)
        {
            var result = new BusinessResult<PartnerReferenceDataMeta>();
            try
            {
                var entity = UOW.PartnerReferenceDataMetas
                    .Query(x => x.Id == id)
                    .Include(x => x.PartnerReferenceDataMetaDetails)
                    .FirstOrDefault();

                if (entity != null)
                {
                    result.Item = _mapper.Map<PartnerReferenceDataMeta>(entity);
                    result.ResultStatus = ResultStatus.Success;
                }
                else
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "Partner reference data metadata not found";
                }
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
            }
            return result;
        }

        #endregion

        #region Upload Operations

        /// <summary>
        /// Get all partner reference data uploads
        /// </summary>
        /// <returns>Collection of partner reference data uploads</returns>
        public BusinessResult<ICollection<PartnerReferenceDataUpload>> GetPartnerReferenceDataUploads()
        {
            var result = new BusinessResult<ICollection<PartnerReferenceDataUpload>>();
            try
            {
                var entities = UOW.PartnerReferenceDataUploads.GetAll()
                    .OrderByDescending(x => x.CreatedOn)
                    .ToList();
                
                var dtos = _mapper.Map<ICollection<PartnerReferenceDataUpload>>(entities);
                
                // Populate additional properties
                foreach (var dto in dtos)
                {
                    PopulateUploadProperties(dto);
                    PopulateUploadStatistics(dto);
                    PopulateUploadDetailsUserInfo(dto);
                }
                
                result.Item = dtos;
                result.ResultStatus = ResultStatus.Success;
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
            }
            return result;
        }

        /// <summary>
        /// Get partner reference data uploads with filtering and pagination
        /// </summary>
        /// <param name="year">Filter by year</param>
        /// <param name="cycle">Filter by cycle</param>
        /// <param name="status">Filter by status</param>
        /// <param name="pageIndex">Page index for pagination (0-based)</param>
        /// <param name="pageSize">Page size for pagination</param>
        /// <returns>Paginated collection of partner reference data uploads with metadata</returns>
        public BusinessResult<IPagedList<PartnerReferenceDataUpload>> SearchPartnerReferenceDataUploads(short? year = null,
            byte? cycle = null, byte? status = null, int pageIndex = 0, int pageSize = 20)
        {
            var result = new BusinessResult<IPagedList<PartnerReferenceDataUpload>>();
            try
            {
                var query = UOW.PartnerReferenceDataUploads.GetAll();

                // Apply filters
                if (year.HasValue)
                    query = query.Where(x => x.Year == year.Value);

                if (cycle.HasValue)
                    query = query.Where(x => x.Cycle == cycle.Value);

                if (status.HasValue)
                    query = query.Where(x => x.Status == status.Value);

                // Order by creation date descending and apply pagination with AutoMapper integration
                var orderedQuery = query.OrderByDescending(x => x.CreatedOn);
                var pagedDtos = orderedQuery.ToPagedList<PartnerReferenceDataUpload, Entity.PartnerReferenceDataUpload>(
                    _mapper, pageIndex, pageSize);

                // Populate additional properties
                foreach (var dto in pagedDtos.Items)
                {
                    PopulateUploadProperties(dto);
                    PopulateUploadStatistics(dto);
                    PopulateUploadDetailsUserInfo(dto);
                }

                result.Item = pagedDtos;
                result.ResultStatus = ResultStatus.Success;
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
            }
            return result;
        }

        /// <summary>
        /// Get partner reference data upload by ID
        /// </summary>
        /// <param name="id">Upload ID</param>
        /// <returns>Partner reference data upload object</returns>
        public BusinessResult<PartnerReferenceDataUpload> GetPartnerReferenceDataUploadById(Guid id)
        {
            var result = new BusinessResult<PartnerReferenceDataUpload>();
            try
            {
                var entity = UOW.PartnerReferenceDataUploads.GetById(id);
                if (entity != null)
                {
                    var dto = _mapper.Map<PartnerReferenceDataUpload>(entity);
                    PopulateUploadProperties(dto);
                    PopulateUploadStatistics(dto);
                    PopulateUploadDetailsUserInfo(dto);
                    result.Item = dto;
                    result.ResultStatus = ResultStatus.Success;
                }
                else
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "Partner reference data upload not found";
                }
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
            }
            return result;
        }

        /// <summary>
        /// Get partner reference data upload details by upload ID
        /// </summary>
        /// <param name="uploadId">Upload ID</param>
        /// <param name="includeValidOnly">Include only valid records</param>
        /// <param name="includeInvalidOnly">Include only invalid records</param>
        /// <returns>Collection of upload details with metadata information</returns>
        public BusinessResult<PartnerReferenceDataUploadDetailsResult> GetPartnerReferenceDataUploadDetails(Guid uploadId,
            bool includeValidOnly = false, bool includeInvalidOnly = false)
        {
            var result = new BusinessResult<PartnerReferenceDataUploadDetailsResult>();
            try
            {
                // First get the upload to access metadata
                var upload = UOW.PartnerReferenceDataUploads
                    .Query(x => x.Id == uploadId)
                    .Include(x => x.Meta)
                    .ThenInclude(x => x.PartnerReferenceDataMetaDetails)
                    .FirstOrDefault();

                if (upload == null)
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "Upload not found";
                    return result;
                }

                // Get upload details
                var query = UOW.PartnerReferenceDataUploadDetails.Query(x => x.PartnerReferenceDataUploadId == uploadId);

                // Apply filters
                if (includeValidOnly)
                    query = query.Where(x => string.IsNullOrEmpty(x.ValidationError));
                else if (includeInvalidOnly)
                    query = query.Where(x => !string.IsNullOrEmpty(x.ValidationError));

                var entities = query.OrderBy(x => x.RowId).ToList();
                var dtos = _mapper.Map<ICollection<PartnerReferenceDataUploadDetails>>(entities);

                // Get metadata details ordered by column order
                var metaDetails = upload.Meta.PartnerReferenceDataMetaDetails
                    .OrderBy(x => x.ColumnOrder)
                    .ToList();

                // Populate dynamic column data for each upload detail
                foreach (var dto in dtos)
                {
                    PopulateUploadDetailPropertiesWithMetadata(dto, metaDetails);
                }

                // Create result with both data and metadata
                var uploadDetailsResult = new PartnerReferenceDataUploadDetailsResult
                {
                    UploadDetails = dtos,
                    MetaDetails = _mapper.Map<ICollection<PartnerReferenceDataMetaDetails>>(metaDetails)
                };

                result.Item = uploadDetailsResult;
                result.ResultStatus = ResultStatus.Success;
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
            }
            return result;
        }

        /// <summary>
        /// Delete partner reference data upload
        /// </summary>
        /// <param name="uploadId">Upload ID to delete</param>
        /// <returns>Delete result</returns>
        public BusinessResult<bool> DeleteUpload(Guid uploadId)
        {
            var result = new BusinessResult<bool>();
            try
            {
                var upload = UOW.PartnerReferenceDataUploads.GetById(uploadId);
                if (upload == null)
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "Upload not found";
                    return result;
                }

                // Delete upload details first
                var details = UOW.PartnerReferenceDataUploadDetails
                    .Query(x => x.PartnerReferenceDataUploadId == uploadId).ToList();
                foreach (var detail in details)
                {
                    UOW.PartnerReferenceDataUploadDetails.Delete(detail);
                }

                // Delete upload
                UOW.PartnerReferenceDataUploads.Delete(upload);

                if (UOW.Commit() > 0)
                {
                    result.Item = true;
                    result.ResultStatus = ResultStatus.Success;
                }
                else
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "Failed to delete upload";
                }
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
            }
            return result;
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Populate upload statistics for a DTO
        /// </summary>
        /// <param name="dto">Upload DTO</param>
        private void PopulateUploadStatistics(PartnerReferenceDataUpload dto)
        {
            try
            {
                var details = UOW.PartnerReferenceDataUploadDetails
                    .Query(x => x.PartnerReferenceDataUploadId == dto.Id)
                    .ToList();

                dto.TotalRecords = details.Count;
                dto.InvalidRecords = details.Count(x => !string.IsNullOrEmpty(x.ValidationError));
                dto.ValidRecords = dto.TotalRecords - dto.InvalidRecords;
            }
            catch (Exception ex)
            {
                // Log error but don't throw - this is a helper method for display purposes
                Logger?.LogError(ex, "Error populating upload statistics for upload ID: {UploadId}", dto.Id);
            }
        }

        /// <summary>
        /// Populate upload detail properties with metadata-based dynamic columns
        /// </summary>
        /// <param name="dto">Upload detail DTO</param>
        /// <param name="metaDetails">Metadata details for column definitions</param>
        private void PopulateUploadDetailPropertiesWithMetadata(PartnerReferenceDataUploadDetails dto,
            ICollection<Entity.PartnerReferenceDataMetaDetails> metaDetails)
        {
            try
            {
                if (!string.IsNullOrEmpty(dto.Data))
                {
                    var data = JsonConvert.DeserializeObject<Dictionary<string, object>>(dto.Data);
                    if (data != null)
                    {
                        // Populate dynamic column data based on metadata
                        foreach (var metaDetail in metaDetails)
                        {
                            var value = "";
                            if (data.ContainsKey(metaDetail.NormalizedColumnName))
                            {
                                var rawValue = data[metaDetail.NormalizedColumnName];
                                // Convert all values to string representation
                                value = rawValue?.ToString() ?? "";
                            }

                            dto.ColumnData[metaDetail.NormalizedColumnName] = value;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Log error but don't throw - this is a helper method for display purposes
                Logger?.LogError(ex, "Error populating upload detail properties with metadata for detail ID: {DetailId}", dto.Id);
            }
        }



        /// <summary>
        /// Populate partner reference data properties for a DTO
        /// </summary>
        /// <param name="dto">Partner reference data DTO</param>
        private void PopulatePartnerReferenceDataProperties(PartnerReferenceData dto)
        {
            try
            {
                // Populate cycle string
                dto.CycleString = dto.Cycle.ToString();

                // Populate partner information and parse JSON data
                PopulatePartnerInformation(dto);
            }
            catch (Exception ex)
            {
                // Log error but don't throw - this is a helper method for display purposes
                Logger?.LogError(ex, "Error populating partner reference data properties for ID: {Id}", dto.Id);
            }
        }

        /// <summary>
        /// Populate partner information and parse JSON data
        /// </summary>
        /// <param name="dto">Partner reference data DTO</param>
        private void PopulatePartnerInformation(PartnerReferenceData dto)
        {
            try
            {
                // Get partner information
                var partner = UOW.Partners.Query(x => x.Id == dto.PartnerId).FirstOrDefault();
                if (partner != null)
                {
                    dto.Partner = _mapper.Map<Partner>(partner);
                    dto.PartnerName = partner.DisplayName;
                }

                // Parse JSON data to extract employee ID and other information
                if (!string.IsNullOrEmpty(dto.Data))
                {
                    var data = JsonConvert.DeserializeObject<Dictionary<string, object>>(dto.Data);
                    if (data != null)
                    {
                        // Try to extract employee ID from various possible field names (including camelCase)
                        var employeeIdKeys = new[] { "employeeid", "employee_id", "empid", "emp_id", "employeeId" };
                        foreach (var key in employeeIdKeys)
                        {
                            if (data.ContainsKey(key))
                            {
                                dto.EmployeeId = data[key]?.ToString();
                                break;
                            }
                        }

                        // If we couldn't get partner name from Partner table, try to get it from the data
                        if (string.IsNullOrEmpty(dto.PartnerName))
                        {
                            var partnerNameKeys = new[] { "partnername", "partner_name", "name", "fullname", "full_name" };
                            foreach (var key in partnerNameKeys)
                            {
                                if (data.ContainsKey(key))
                                {
                                    dto.PartnerName = data[key]?.ToString();
                                    break;
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Log error but don't throw - this is a helper method for display purposes
                Logger?.LogError(ex, "Error populating partner information for partner reference data ID: {Id}", dto.Id);
            }
        }



        /// <summary>
        /// Normalize column name for JSON property names
        /// </summary>
        /// <param name="columnName">Original column name</param>
        /// <returns>Normalized column name</returns>
        private string NormalizeColumnName(string columnName)
        {
            if (string.IsNullOrEmpty(columnName))
                return string.Empty;

            // Remove spaces, special characters, and convert to lowercase
            var normalized = Regex.Replace(columnName, @"[^a-zA-Z0-9]", "").ToLowerInvariant();
            return normalized;
        }

        /// <summary>
        /// Normalize column names for JSON property names with camelCase and uniqueness handling
        /// </summary>
        /// <param name="columnNames">List of original column names</param>
        /// <returns>Dictionary mapping original names to normalized camelCase names</returns>
        private Dictionary<string, string> NormalizeColumnNames(List<string> columnNames)
        {
            var result = new Dictionary<string, string>();
            var usedNames = new HashSet<string>(StringComparer.OrdinalIgnoreCase);

            foreach (var columnName in columnNames)
            {
                var normalized = NormalizeColumnNameToCamelCase(columnName);

                // Ensure uniqueness by adding sequence numbers if needed
                var uniqueName = EnsureUniqueName(normalized, usedNames);

                result[columnName] = uniqueName;
                usedNames.Add(uniqueName);
            }

            return result;
        }

        /// <summary>
        /// Normalize a single column name to camelCase format
        /// </summary>
        /// <param name="columnName">Original column name</param>
        /// <returns>Normalized camelCase column name</returns>
        private string NormalizeColumnNameToCamelCase(string columnName)
        {
            if (string.IsNullOrEmpty(columnName))
                return string.Empty;

            // Remove special characters and split by spaces/separators
            var cleanName = Regex.Replace(columnName, @"[^a-zA-Z0-9\s]", " ");
            var words = cleanName.Split(new char[] { ' ', '\t', '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries);

            if (words.Length == 0)
                return string.Empty;

            var result = new StringBuilder();

            // First word is lowercase
            result.Append(words[0].ToLowerInvariant());

            // Subsequent words are title case (first letter uppercase, rest lowercase)
            for (int i = 1; i < words.Length; i++)
            {
                if (words[i].Length > 0)
                {
                    result.Append(char.ToUpperInvariant(words[i][0]));
                    if (words[i].Length > 1)
                    {
                        result.Append(words[i].Substring(1).ToLowerInvariant());
                    }
                }
            }

            return result.ToString();
        }

        /// <summary>
        /// Ensure the normalized name is unique by adding sequence numbers if needed
        /// </summary>
        /// <param name="baseName">Base normalized name</param>
        /// <param name="usedNames">Set of already used names</param>
        /// <returns>Unique normalized name</returns>
        private string EnsureUniqueName(string baseName, HashSet<string> usedNames)
        {
            if (string.IsNullOrEmpty(baseName))
                return "column1"; // Default name for empty columns

            var candidateName = baseName;
            var counter = 1;

            // Keep adding numbers until we find a unique name
            while (usedNames.Contains(candidateName))
            {
                counter++;
                candidateName = $"{baseName}{counter}";
            }

            return candidateName;
        }

        /// <summary>
        /// Extract column headers from uploaded file
        /// </summary>
        /// <param name="file">Uploaded file</param>
        /// <param name="extension">File extension</param>
        /// <returns>List of column headers</returns>
        private async Task<List<string>> ExtractColumnHeadersFromFileAsync(IFormFile file, string extension)
        {
            var headers = new List<string>();

            try
            {
                using (var stream = file.OpenReadStream())
                {
                    if (extension == ".xlsx")
                    {
                        headers = await ExtractHeadersFromExcelAsync(stream);
                    }
                    else if (extension == ".csv")
                    {
                        headers = ExtractHeadersFromCsv(stream);
                    }
                }
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "Error extracting headers from file: {FileName}", file.FileName);
            }

            return headers;
        }

        /// <summary>
        /// Extract headers from Excel file
        /// </summary>
        /// <param name="stream">File stream</param>
        /// <returns>List of column headers</returns>
        private async Task<List<string>> ExtractHeadersFromExcelAsync(Stream stream)
        {
            var headers = new List<string>();

            using (var package = new ExcelPackage(stream))
            {
                var worksheet = package.Workbook.Worksheets.FirstOrDefault();
                if (worksheet != null && worksheet.Dimension != null)
                {
                    // Read first row as headers
                    for (int col = 1; col <= worksheet.Dimension.Columns; col++)
                    {
                        var headerValue = worksheet.Cells[1, col].Value?.ToString()?.Trim();
                        if (!string.IsNullOrEmpty(headerValue))
                        {
                            headers.Add(headerValue);
                        }
                    }
                }
            }

            return headers;
        }

        /// <summary>
        /// Extract headers from CSV file
        /// </summary>
        /// <param name="stream">File stream</param>
        /// <returns>List of column headers</returns>
        private List<string> ExtractHeadersFromCsv(Stream stream)
        {
            var headers = new List<string>();

            stream.Position = 0;
            using (var reader = new StreamReader(stream))
            {
                // Parse CSV properly handling quoted fields with newlines
                var csvRows = ParseCsvWithQuotedFields(reader);

                if (csvRows.Count > 0)
                {
                    // First row contains headers
                    var headerValues = csvRows[0];
                    headers.AddRange(headerValues.Where(h => !string.IsNullOrEmpty(h?.Trim())));
                }
            }

            return headers;
        }

        /// <summary>
        /// Determine column data type based on file content analysis
        /// </summary>
        /// <param name="file">The uploaded file</param>
        /// <param name="columnIndex">Zero-based column index</param>
        /// <param name="extension">File extension</param>
        /// <returns>Column data type</returns>
        private async Task<byte> DetermineColumnDataTypeAsync(IFormFile file, int columnIndex, string extension)
        {
            try
            {
                using (var stream = file.OpenReadStream())
                {
                    if (extension == ".xlsx")
                    {
                        return await DetermineColumnDataTypeFromExcelAsync(stream, columnIndex);
                    }
                    else if (extension == ".csv")
                    {
                        return DetermineColumnDataTypeFromCsv(stream, columnIndex);
                    }
                }
            }
            catch (Exception ex)
            {
                Logger?.LogWarning(ex, "Error determining column data type for column {ColumnIndex}, defaulting to Text", columnIndex);
            }

            // Default to text if unable to determine
            return (byte)Enumerations.PartnerReferenceDataColumnType.Text;
        }

        /// <summary>
        /// Determine column data type from Excel file
        /// </summary>
        /// <param name="stream">File stream</param>
        /// <param name="columnIndex">Zero-based column index</param>
        /// <returns>Column data type</returns>
        private async Task<byte> DetermineColumnDataTypeFromExcelAsync(Stream stream, int columnIndex)
        {
            using (var package = new ExcelPackage(stream))
            {
                var worksheet = package.Workbook.Worksheets.FirstOrDefault();
                if (worksheet?.Dimension == null)
                    return (byte)Enumerations.PartnerReferenceDataColumnType.Text;

                var excelColumnIndex = columnIndex + 1; // Excel is 1-based

                // Check if row 3 has data type indicators (like "Numeric", "Text")
                if (worksheet.Dimension.Rows >= 3)
                {
                    var typeIndicator = worksheet.Cells[3, excelColumnIndex].Value?.ToString()?.Trim().ToLowerInvariant();
                    if (!string.IsNullOrEmpty(typeIndicator))
                    {
                        if (typeIndicator == "numeric")
                            return (byte)Enumerations.PartnerReferenceDataColumnType.Numeric;
                        if (typeIndicator == "text")
                            return (byte)Enumerations.PartnerReferenceDataColumnType.Text;
                    }
                }

                // Analyze actual data values starting from row 4 (or 2 if no type indicators)
                var startRow = worksheet.Dimension.Rows >= 3 ? 4 : 2;
                return AnalyzeColumnDataType(worksheet, excelColumnIndex, startRow, worksheet.Dimension.Rows);
            }
        }

        /// <summary>
        /// Determine column data type from CSV file
        /// </summary>
        /// <param name="stream">File stream</param>
        /// <param name="columnIndex">Zero-based column index</param>
        /// <returns>Column data type</returns>
        private byte DetermineColumnDataTypeFromCsv(Stream stream, int columnIndex)
        {
            stream.Position = 0;
            using (var reader = new StreamReader(stream))
            {
                // Parse CSV properly handling quoted fields with newlines
                var csvRows = ParseCsvWithQuotedFields(reader);

                if (csvRows.Count < 2)
                    return (byte)Enumerations.PartnerReferenceDataColumnType.Text;

                // Check if row 2 (index 1) has data type indicators
                if (csvRows.Count >= 2)
                {
                    var typeRow = csvRows[1]; // Second row should contain type indicators
                    if (columnIndex < typeRow.Length)
                    {
                        var typeIndicator = typeRow[columnIndex]?.Trim().ToLowerInvariant();
                        if (!string.IsNullOrEmpty(typeIndicator))
                        {
                            if (typeIndicator == "numeric")
                                return (byte)Enumerations.PartnerReferenceDataColumnType.Numeric;
                            if (typeIndicator == "text")
                                return (byte)Enumerations.PartnerReferenceDataColumnType.Text;
                        }
                    }
                }

                // Analyze actual data values starting from row 3 (index 2) or row 2 (index 1) if no type indicators
                var startIndex = csvRows.Count >= 3 ? 2 : 1;
                return AnalyzeColumnDataTypeFromCsvRows(csvRows, columnIndex, startIndex);
            }
        }

        /// <summary>
        /// Parse CSV content properly handling quoted fields with newlines
        /// </summary>
        /// <param name="reader">StreamReader for CSV content</param>
        /// <returns>List of parsed CSV rows</returns>
        private List<string[]> ParseCsvWithQuotedFields(StreamReader reader)
        {
            var rows = new List<string[]>();
            var currentRow = new List<string>();
            var currentField = new StringBuilder();
            bool inQuotes = false;
            bool fieldStarted = false;

            string line;
            while ((line = reader.ReadLine()) != null)
            {
                for (int i = 0; i < line.Length; i++)
                {
                    char c = line[i];

                    if (c == '"')
                    {
                        if (!fieldStarted)
                        {
                            inQuotes = true;
                            fieldStarted = true;
                        }
                        else if (inQuotes)
                        {
                            if (i + 1 < line.Length && line[i + 1] == '"')
                            {
                                currentField.Append('"');
                                i++; // Skip the next quote
                            }
                            else
                            {
                                inQuotes = false;
                            }
                        }
                        else
                        {
                            currentField.Append(c);
                        }
                    }
                    else if (c == ',' && !inQuotes)
                    {
                        currentRow.Add(currentField.ToString().Trim());
                        currentField.Clear();
                        fieldStarted = false;
                    }
                    else
                    {
                        currentField.Append(c);
                        if (!fieldStarted) fieldStarted = true;
                    }
                }

                // If we're in quotes, add a newline and continue to next line
                if (inQuotes)
                {
                    currentField.AppendLine();
                }
                else
                {
                    // End of row
                    currentRow.Add(currentField.ToString().Trim());
                    rows.Add(currentRow.ToArray());
                    currentRow.Clear();
                    currentField.Clear();
                    fieldStarted = false;
                }
            }

            // Handle case where file doesn't end with newline
            if (currentRow.Count > 0 || currentField.Length > 0)
            {
                currentRow.Add(currentField.ToString().Trim());
                rows.Add(currentRow.ToArray());
            }

            return rows;
        }

        /// <summary>
        /// Analyze column data type from parsed CSV rows
        /// </summary>
        /// <param name="csvRows">Parsed CSV rows</param>
        /// <param name="columnIndex">Zero-based column index</param>
        /// <param name="startIndex">Starting row index for analysis</param>
        /// <returns>Column data type</returns>
        private byte AnalyzeColumnDataTypeFromCsvRows(List<string[]> csvRows, int columnIndex, int startIndex)
        {
            int numericCount = 0;
            int textCount = 0;
            int totalValues = 0;

            for (int i = startIndex; i < Math.Min(csvRows.Count, startIndex + 20); i++) // Analyze up to 20 rows
            {
                if (i >= csvRows.Count || columnIndex >= csvRows[i].Length) continue;

                var value = csvRows[i][columnIndex]?.Trim();
                if (string.IsNullOrEmpty(value)) continue;

                totalValues++;

                if (IsNumericValue(value))
                    numericCount++;
                else
                    textCount++;
            }

            // If more than 70% of values are numeric, consider it numeric
            if (totalValues > 0 && (double)numericCount / totalValues >= 0.7)
                return (byte)Enumerations.PartnerReferenceDataColumnType.Numeric;

            return (byte)Enumerations.PartnerReferenceDataColumnType.Text;
        }

        /// <summary>
        /// Analyze column data type from Excel worksheet data
        /// </summary>
        /// <param name="worksheet">Excel worksheet</param>
        /// <param name="columnIndex">1-based Excel column index</param>
        /// <param name="startRow">Starting row for analysis</param>
        /// <param name="endRow">Ending row for analysis</param>
        /// <returns>Column data type</returns>
        private byte AnalyzeColumnDataType(ExcelWorksheet worksheet, int columnIndex, int startRow, int endRow)
        {
            var numericCount = 0;
            var textCount = 0;
            var totalValues = 0;

            for (int row = startRow; row <= Math.Min(endRow, startRow + 20); row++) // Analyze up to 20 rows
            {
                var cellValue = worksheet.Cells[row, columnIndex].Value;
                if (cellValue == null) continue;

                var stringValue = cellValue.ToString().Trim();
                if (string.IsNullOrEmpty(stringValue)) continue;

                totalValues++;

                if (IsNumericValue(stringValue))
                    numericCount++;
                else
                    textCount++;
            }

            // If more than 70% of values are numeric, consider it numeric
            if (totalValues > 0 && (double)numericCount / totalValues >= 0.7)
                return (byte)Enumerations.PartnerReferenceDataColumnType.Numeric;

            return (byte)Enumerations.PartnerReferenceDataColumnType.Text;
        }

        /// <summary>
        /// Analyze column data type from CSV lines
        /// </summary>
        /// <param name="lines">CSV lines</param>
        /// <param name="columnIndex">Zero-based column index</param>
        /// <param name="startIndex">Starting line index for analysis</param>
        /// <returns>Column data type</returns>
        private byte AnalyzeColumnDataTypeFromCsvLines(List<string> lines, int columnIndex, int startIndex)
        {
            var numericCount = 0;
            var textCount = 0;
            var totalValues = 0;

            for (int i = startIndex; i < Math.Min(lines.Count, startIndex + 20); i++) // Analyze up to 20 rows
            {
                var fields = ParseCsvLine(lines[i]);
                if (columnIndex >= fields.Length) continue;

                var value = fields[columnIndex]?.Trim();
                if (string.IsNullOrEmpty(value)) continue;

                totalValues++;

                if (IsNumericValue(value))
                    numericCount++;
                else
                    textCount++;
            }

            // If more than 70% of values are numeric, consider it numeric
            if (totalValues > 0 && (double)numericCount / totalValues >= 0.7)
                return (byte)Enumerations.PartnerReferenceDataColumnType.Numeric;

            return (byte)Enumerations.PartnerReferenceDataColumnType.Text;
        }

        /// <summary>
        /// Check if a string value represents a numeric value
        /// </summary>
        /// <param name="value">String value to check</param>
        /// <returns>True if numeric, false otherwise</returns>
        private bool IsNumericValue(string value)
        {
            if (string.IsNullOrWhiteSpace(value))
                return false;

            // Remove common formatting characters
            var cleanValue = value.Replace(",", "").Replace("$", "").Replace("%", "").Trim();

            // Try to parse as decimal
            return decimal.TryParse(cleanValue, out _);
        }

        /// <summary>
        /// Parse a CSV line handling quoted fields that may contain commas
        /// </summary>
        /// <param name="line">CSV line to parse</param>
        /// <returns>Array of field values</returns>
        private string[] ParseCsvLine(string line)
        {
            var fields = new List<string>();
            var currentField = new StringBuilder();
            bool inQuotes = false;
            bool fieldStarted = false;

            for (int i = 0; i < line.Length; i++)
            {
                char c = line[i];

                if (c == '"')
                {
                    if (!fieldStarted)
                    {
                        // Starting a quoted field
                        inQuotes = true;
                        fieldStarted = true;
                    }
                    else if (inQuotes)
                    {
                        // Check if this is an escaped quote (double quote)
                        if (i + 1 < line.Length && line[i + 1] == '"')
                        {
                            // Escaped quote - add one quote to the field
                            currentField.Append('"');
                            i++; // Skip the next quote
                        }
                        else
                        {
                            // End of quoted field
                            inQuotes = false;
                        }
                    }
                    else
                    {
                        // Quote in the middle of unquoted field - treat as regular character
                        currentField.Append(c);
                    }
                }
                else if (c == ',' && !inQuotes)
                {
                    // Field separator - end current field
                    fields.Add(currentField.ToString().Trim());
                    currentField.Clear();
                    fieldStarted = false;
                }
                else
                {
                    // Regular character
                    currentField.Append(c);
                    if (!fieldStarted)
                        fieldStarted = true;
                }
            }

            // Add the last field
            fields.Add(currentField.ToString().Trim());

            return fields.ToArray();
        }

        /// <summary>
        /// Get or create metadata for the given year and cycle, updating if file structure has changed
        /// </summary>
        /// <param name="file">Uploaded file</param>
        /// <param name="year">Year</param>
        /// <param name="cycle">Cycle</param>
        /// <returns>Metadata result</returns>
        private async Task<BusinessResult<Entity.PartnerReferenceDataMeta>> GetOrCreateMetadataAsync(IFormFile file, short year, byte cycle)
        {
            var result = new BusinessResult<Entity.PartnerReferenceDataMeta>();

            try
            {
                // Extract column headers from current file
                var extension = Path.GetExtension(file.FileName).ToLowerInvariant();
                var currentColumnHeaders = await ExtractColumnHeadersFromFileAsync(file, extension);
                if (!currentColumnHeaders.Any())
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "No column headers found in the uploaded file";
                    return result;
                }

                // Try to get existing active metadata for this year and cycle
                var existingMeta = UOW.PartnerReferenceDataMetas
                    .Query(x => x.Year == year && x.Cycle == cycle && x.IsActive)
                    .Include(x => x.PartnerReferenceDataMetaDetails)
                    .FirstOrDefault();

                if (existingMeta != null)
                {
                    // Check if the existing metadata matches the current file structure
                    var existingMetaDetails = existingMeta.PartnerReferenceDataMetaDetails
                        .OrderBy(x => x.ColumnOrder)
                        .ToList();

                    // Generate normalized names for current file columns
                    var currentNormalizedNames = NormalizeColumnNames(currentColumnHeaders);

                    // Compare both original column names and normalized column names
                    if (DoMetadataMatch(existingMetaDetails, currentColumnHeaders, currentNormalizedNames))
                    {
                        // Structure matches, use existing metadata
                        result.Item = existingMeta;
                        result.ResultStatus = ResultStatus.Success;
                        result.Message = "Using existing metadata - file structure and normalization match";
                        return result;
                    }
                    else
                    {
                        // Structure has changed, update existing metadata instead of creating new
                        Logger?.LogInformation("File structure or normalization changed for Year: {Year}, Cycle: {Cycle}. Updating existing metadata.", year, cycle);

                        var updateResult = await UpdateExistingMetadataAsync(existingMeta, file, currentColumnHeaders, currentNormalizedNames);
                        if (updateResult.ResultStatus == ResultStatus.Success)
                        {
                            result.Item = updateResult.Item;
                            result.ResultStatus = ResultStatus.Success;
                            result.Message = "Metadata updated due to file structure change";
                            return result;
                        }
                        else
                        {
                            result.ResultStatus = updateResult.ResultStatus;
                            result.Message = updateResult.Message;
                            return result;
                        }
                    }
                }

                // Create new metadata from current file
                var extractResult = await ExtractMetadataFromFileAsync(file, year, cycle);
                if (extractResult.ResultStatus != ResultStatus.Success)
                {
                    result.ResultStatus = extractResult.ResultStatus;
                    result.Message = extractResult.Message;
                    return result;
                }

                // Get the entity version of the created metadata
                var metaEntity = UOW.PartnerReferenceDataMetas
                    .Query(x => x.Id == extractResult.Item.Id)
                    .Include(x => x.PartnerReferenceDataMetaDetails)
                    .FirstOrDefault();

                result.Item = metaEntity;
                result.ResultStatus = ResultStatus.Success;
                result.Message = existingMeta != null ? "Metadata updated due to file structure change" : "New metadata created";
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
                Logger?.LogError(ex, "Error getting or creating metadata for Year: {Year}, Cycle: {Cycle}", year, cycle);
            }

            return result;
        }

        /// <summary>
        /// Update existing metadata with new file structure
        /// </summary>
        /// <param name="existingMeta">Existing metadata entity</param>
        /// <param name="file">Uploaded file</param>
        /// <param name="currentColumnHeaders">Current column headers from file</param>
        /// <param name="currentNormalizedNames">Current normalized column names</param>
        /// <returns>Updated metadata result</returns>
        private async Task<BusinessResult<Entity.PartnerReferenceDataMeta>> UpdateExistingMetadataAsync(
            Entity.PartnerReferenceDataMeta existingMeta, IFormFile file,
            List<string> currentColumnHeaders, Dictionary<string, string> currentNormalizedNames)
        {
            var result = new BusinessResult<Entity.PartnerReferenceDataMeta>();

            try
            {
                // Update the main metadata record
                existingMeta.FileName = file.FileName;
                existingMeta.ModifiedBy = CurrentUser?.Id;
                existingMeta.ModifiedByName = CurrentUser?.Email;
                existingMeta.ModifiedOn = CurrentDateTime;
                UOW.PartnerReferenceDataMetas.Update(existingMeta);

                // Remove all existing metadata details
                var existingDetails = existingMeta.PartnerReferenceDataMetaDetails.ToList();
                foreach (var detail in existingDetails)
                {
                    UOW.PartnerReferenceDataMetaDetails.Delete(detail);
                }

                // Create new metadata details based on current file structure
                var extension = Path.GetExtension(file.FileName).ToLowerInvariant();
                for (int i = 0; i < currentColumnHeaders.Count; i++)
                {
                    var columnName = currentColumnHeaders[i];
                    var columnDataType = await DetermineColumnDataTypeAsync(file, i, extension);

                    var metaDetail = new Entity.PartnerReferenceDataMetaDetails
                    {
                        Id = SequentialGuid.NewGuid(),
                        MetaId = existingMeta.Id,
                        ColumnName = columnName,
                        NormalizedColumnName = currentNormalizedNames[columnName],
                        ColumnDataType = columnDataType,
                        ColumnOrder = (short)(i + 1),
                        CreatedBy = CurrentUser?.Id,
                        CreatedByName = CurrentUser?.Email,
                        CreatedOn = CurrentDateTime
                    };

                    UOW.PartnerReferenceDataMetaDetails.Add(metaDetail);
                }

                // Commit with audit enabled to log the changes
                var commitResult = UOW.Commit(enableAuditing: true, logonUser: CurrentUser?.Id.ToString() ?? "System");
                if (commitResult > 0)
                {
                    // Reload the updated metadata with details
                    var updatedMeta = UOW.PartnerReferenceDataMetas
                        .Query(x => x.Id == existingMeta.Id)
                        .Include(x => x.PartnerReferenceDataMetaDetails)
                        .FirstOrDefault();

                    result.Item = updatedMeta;
                    result.ResultStatus = ResultStatus.Success;
                    result.Message = "Metadata updated successfully with audit logging";
                }
                else
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "Failed to commit metadata updates";
                }
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
                Logger?.LogError(ex, "Error updating existing metadata for MetaId: {MetaId}", existingMeta.Id);
            }

            return result;
        }

        /// <summary>
        /// Compare existing metadata with current file structure including both original and normalized column names
        /// </summary>
        /// <param name="existingMetaDetails">Existing metadata details from database</param>
        /// <param name="currentColumnHeaders">Current column headers from uploaded file</param>
        /// <param name="currentNormalizedNames">Current normalized column names mapping</param>
        /// <returns>True if metadata matches, false otherwise</returns>
        private bool DoMetadataMatch(List<Entity.PartnerReferenceDataMetaDetails> existingMetaDetails,
            List<string> currentColumnHeaders, Dictionary<string, string> currentNormalizedNames)
        {
            // Check if column count matches
            if (existingMetaDetails.Count != currentColumnHeaders.Count)
                return false;

            // Compare each column in order
            for (int i = 0; i < existingMetaDetails.Count; i++)
            {
                var existingDetail = existingMetaDetails[i];
                var currentColumnName = currentColumnHeaders[i];
                var currentNormalizedName = currentNormalizedNames[currentColumnName];

                // Check if original column name matches
                if (!string.Equals(existingDetail.ColumnName, currentColumnName, StringComparison.OrdinalIgnoreCase))
                    return false;

                // Check if normalized column name matches
                if (!string.Equals(existingDetail.NormalizedColumnName, currentNormalizedName, StringComparison.OrdinalIgnoreCase))
                    return false;
            }

            return true;
        }

        /// <summary>
        /// Compare two column lists to determine if they match (legacy method for backward compatibility)
        /// </summary>
        /// <param name="existingColumns">Existing column names from metadata</param>
        /// <param name="currentColumns">Current column names from uploaded file</param>
        /// <returns>True if columns match, false otherwise</returns>
        private bool DoColumnsMatch(List<string> existingColumns, List<string> currentColumns)
        {
            if (existingColumns.Count != currentColumns.Count)
                return false;

            for (int i = 0; i < existingColumns.Count; i++)
            {
                if (!string.Equals(existingColumns[i], currentColumns[i], StringComparison.OrdinalIgnoreCase))
                    return false;
            }

            return true;
        }

        /// <summary>
        /// Process file data and create upload details
        /// </summary>
        /// <param name="file">Uploaded file</param>
        /// <param name="uploadId">Upload ID</param>
        /// <param name="meta">Metadata</param>
        /// <returns>Processing result</returns>
        private async Task<BusinessResult<string>> ProcessFileDataAsync(IFormFile file, Guid uploadId, Entity.PartnerReferenceDataMeta meta)
        {
            var result = new BusinessResult<string>();

            try
            {
                var extension = Path.GetExtension(file.FileName).ToLowerInvariant();
                var uploadDetails = new List<Entity.PartnerReferenceDataUploadDetails>();

                using (var stream = file.OpenReadStream())
                {
                    if (extension == ".xlsx")
                    {
                        uploadDetails = await ProcessExcelDataAsync(stream, uploadId, meta);
                    }
                    else if (extension == ".csv")
                    {
                        uploadDetails = ProcessCsvData(stream, uploadId, meta);
                    }
                }

                // Save upload details
                foreach (var detail in uploadDetails)
                {
                    UOW.PartnerReferenceDataUploadDetails.Add(detail);
                }

                result.ResultStatus = ResultStatus.Success;
                result.Item = $"Processed {uploadDetails.Count} rows";
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
                Logger?.LogError(ex, "Error processing file data for upload: {UploadId}", uploadId);
            }

            return result;
        }

        /// <summary>
        /// Process Excel file data and create upload details
        /// </summary>
        /// <param name="stream">File stream</param>
        /// <param name="uploadId">Upload ID</param>
        /// <param name="meta">Metadata</param>
        /// <returns>List of upload details</returns>
        private Task<List<Entity.PartnerReferenceDataUploadDetails>> ProcessExcelDataAsync(Stream stream, Guid uploadId, Entity.PartnerReferenceDataMeta meta)
        {
            var uploadDetails = new List<Entity.PartnerReferenceDataUploadDetails>();

            using (var package = new ExcelPackage(stream))
            {
                var worksheet = package.Workbook.Worksheets.FirstOrDefault();
                if (worksheet?.Dimension == null)
                    return Task.FromResult(uploadDetails);

                // Get column mappings from metadata
                var columnMappings = meta.PartnerReferenceDataMetaDetails
                    .OrderBy(x => x.ColumnOrder)
                    .ToDictionary(x => x.ColumnOrder - 1, x => x); // Convert to 0-based index

                // Process data rows (skip header rows)
                var startRow = worksheet.Dimension.Rows >= 3 ? 4 : 2; // Skip headers and type indicators if present
                for (int row = startRow; row <= worksheet.Dimension.Rows; row++)
                {
                    var rowData = new Dictionary<string, object>();
                    bool hasData = false;

                    // Process each column
                    for (int col = 1; col <= worksheet.Dimension.Columns; col++)
                    {
                        var colIndex = col - 1; // Convert to 0-based
                        if (!columnMappings.ContainsKey(colIndex))
                            continue;

                        var metaDetail = columnMappings[colIndex];
                        var cellValue = worksheet.Cells[row, col].Value;

                        if (cellValue != null)
                        {
                            var processedValue = ProcessCellValue(cellValue.ToString(), metaDetail.ColumnDataType);
                            // Include all processed values, even if null, to ensure complete data extraction
                            rowData[metaDetail.NormalizedColumnName] = processedValue;
                            hasData = true;
                        }
                        else
                        {
                            // Include null values for empty cells to maintain column structure
                            rowData[metaDetail.NormalizedColumnName] = null;
                        }
                    }

                    // Only add rows that have data
                    if (hasData)
                    {
                        var uploadDetail = new Entity.PartnerReferenceDataUploadDetails
                        {
                            Id = SequentialGuid.NewGuid(),
                            PartnerReferenceDataUploadId = uploadId,
                            RowId = row,
                            Data = JsonConvert.SerializeObject(rowData),
                            CreatedBy = CurrentUser?.Id,
                            CreatedByName = CurrentUser?.Email,
                            CreatedOn = CurrentDateTime
                        };

                        uploadDetails.Add(uploadDetail);
                    }
                }
            }

            return Task.FromResult(uploadDetails);
        }

        /// <summary>
        /// Process CSV file data and create upload details
        /// </summary>
        /// <param name="stream">File stream</param>
        /// <param name="uploadId">Upload ID</param>
        /// <param name="meta">Metadata</param>
        /// <returns>List of upload details</returns>
        private List<Entity.PartnerReferenceDataUploadDetails> ProcessCsvData(Stream stream, Guid uploadId, Entity.PartnerReferenceDataMeta meta)
        {
            var uploadDetails = new List<Entity.PartnerReferenceDataUploadDetails>();

            stream.Position = 0;
            using (var reader = new StreamReader(stream))
            {
                // Parse CSV properly handling quoted fields with newlines
                var csvRows = ParseCsvWithQuotedFields(reader);

                if (csvRows.Count == 0)
                    return uploadDetails;

                // Get column mappings from metadata
                var columnMappings = meta.PartnerReferenceDataMetaDetails
                    .OrderBy(x => x.ColumnOrder)
                    .ToDictionary(x => x.ColumnOrder - 1, x => x); // Convert to 0-based index

                // Process data rows (skip header and type indicator rows)
                var startIndex = csvRows.Count >= 3 ? 2 : 1; // Skip headers and type indicators if present
                for (int i = startIndex; i < csvRows.Count; i++)
                {
                    var fields = csvRows[i];
                    var rowData = new Dictionary<string, object>();
                    bool hasData = false;

                    // Process each field
                    for (int j = 0; j < fields.Length; j++)
                    {
                        if (!columnMappings.ContainsKey(j))
                            continue;

                        var metaDetail = columnMappings[j];
                        var fieldValue = fields[j]?.Trim();

                        // Process all fields, including empty ones, to ensure complete data extraction
                        var processedValue = ProcessCellValue(fieldValue, metaDetail.ColumnDataType);
                        rowData[metaDetail.NormalizedColumnName] = processedValue;

                        // Mark as having data if we have any non-null values
                        if (processedValue != null)
                        {
                            hasData = true;
                        }
                    }

                    // Only add rows that have data
                    if (hasData)
                    {
                        var uploadDetail = new Entity.PartnerReferenceDataUploadDetails
                        {
                            Id = SequentialGuid.NewGuid(),
                            PartnerReferenceDataUploadId = uploadId,
                            RowId = i + 1, // 1-based row number
                            Data = JsonConvert.SerializeObject(rowData),
                            CreatedBy = CurrentUser?.Id,
                            CreatedByName = CurrentUser?.Email,
                            CreatedOn = CurrentDateTime
                        };

                        uploadDetails.Add(uploadDetail);
                    }
                }
            }

            return uploadDetails;
        }

        /// <summary>
        /// Process cell value based on column data type
        /// </summary>
        /// <param name="value">Raw cell value</param>
        /// <param name="dataType">Column data type</param>
        /// <returns>Processed value</returns>
        private object ProcessCellValue(string value, byte dataType)
        {
            if (string.IsNullOrWhiteSpace(value))
                return null;

            var trimmedValue = value.Trim();

            if (dataType == (byte)Enumerations.PartnerReferenceDataColumnType.Numeric)
            {
                // Remove common formatting characters
                var cleanValue = trimmedValue.Replace(",", "").Replace("$", "").Replace("%", "");

                if (decimal.TryParse(cleanValue, out decimal numericValue))
                {
                    // Round to 2 decimal places as per requirements
                    return Math.Round(numericValue, 2);
                }
                // If numeric parsing fails, return as string (this ensures we don't lose data)
                return trimmedValue;
            }

            // Return as string for text columns
            return trimmedValue;
        }

        /// <summary>
        /// Populate upload properties for a DTO
        /// </summary>
        /// <param name="dto">Upload DTO</param>
        private void PopulateUploadProperties(PartnerReferenceDataUpload dto)
        {
            try
            {
                // Populate cycle string
                dto.CycleString = dto.Cycle.ToString();

                // Populate status string
                dto.StatusString = dto.Status.ToString();
            }
            catch (Exception ex)
            {
                // Log error but don't throw - this is a helper method for display purposes
                Logger?.LogError(ex, "Error populating upload properties for upload ID: {UploadId}", dto.Id);
            }
        }



        /// <summary>
        /// Populate upload details user information (last modified user from upload details)
        /// </summary>
        /// <param name="dto">Upload DTO</param>
        private void PopulateUploadDetailsUserInfo(PartnerReferenceDataUpload dto)
        {
            try
            {
                // Get the most recently modified upload detail record
                var lastModifiedDetail = UOW.PartnerReferenceDataUploadDetails
                    .Query(x => x.PartnerReferenceDataUploadId == dto.Id && x.ModifiedBy.HasValue)
                    .OrderByDescending(x => x.ModifiedOn)
                    .FirstOrDefault();

                if (lastModifiedDetail?.ModifiedBy.HasValue == true)
                {
                    // Override the ModifiedByName with the user who last modified the details
                    dto.ModifiedByName = lastModifiedDetail.ModifiedByName;
                    dto.ModifiedBy = lastModifiedDetail.ModifiedBy;
                    dto.ModifiedOn = lastModifiedDetail.ModifiedOn;
                }
            }
            catch (Exception ex)
            {
                // Log error but don't throw - this is a helper method for display purposes
                Logger?.LogError(ex, "Error populating upload details user info for upload ID: {UploadId}", dto.Id);
            }
        }
             
        /// <summary>
        /// Validate upload detail record
        /// </summary>
        /// <param name="detail">Upload detail to validate</param>
        /// <param name="partnerDict">Dictionary of partners by employee ID</param>
        /// <returns>List of validation errors</returns>
        private async Task<List<string>> ValidateUploadDetailAsync(Entity.PartnerReferenceDataUploadDetails detail, Dictionary<string, Entity.Partner> partnerDict)
        {
            var errors = new List<string>();

            try
            {
                if (string.IsNullOrEmpty(detail.Data))
                {
                    errors.Add("Row data is empty");
                    return errors;
                }

                // Parse JSON data
                var data = JsonConvert.DeserializeObject<Dictionary<string, object>>(detail.Data);
                if (data == null)
                {
                    errors.Add("Invalid row data format");
                    return errors;
                }

                // Get the upload record to access metadata
                var upload = UOW.PartnerReferenceDataUploads.Query(x => x.Id == detail.PartnerReferenceDataUploadId)
                    .Include(x => x.Meta)
                    .Include(x => x.Meta.PartnerReferenceDataMetaDetails)
                    .FirstOrDefault();

                if (upload?.Meta?.PartnerReferenceDataMetaDetails == null)
                {
                    errors.Add("Upload metadata not found");
                    return errors;
                }

                // Find the Employee ID field from metadata (look for column name containing "Employee ID")
                var employeeIdMetaDetail = upload.Meta.PartnerReferenceDataMetaDetails
                    .FirstOrDefault(x => x.ColumnName.Contains("Employee ID", StringComparison.OrdinalIgnoreCase) ||
                                        x.ColumnName.Contains("EmployeeID", StringComparison.OrdinalIgnoreCase) ||
                                        x.ColumnName.Contains("Emp ID", StringComparison.OrdinalIgnoreCase));

                if (employeeIdMetaDetail == null)
                {
                    errors.Add("Employee ID column not found in metadata");
                    return errors;
                }

                // Validate Employee ID (required field) using the normalized column name from metadata
                var employeeIdKey = employeeIdMetaDetail.NormalizedColumnName;
                if (!data.ContainsKey(employeeIdKey) || data[employeeIdKey] == null)
                {
                    errors.Add("Employee ID is required");
                }
                else
                {
                    var employeeId = data[employeeIdKey].ToString().Trim();
                    if (string.IsNullOrEmpty(employeeId))
                    {
                        errors.Add("Employee ID is required");
                    }
                    else if (!partnerDict.ContainsKey(employeeId))
                    {
                        errors.Add($"Invalid Employee ID {employeeId}");
                    }
                }

                // Validate numeric fields (check for negative values and proper format)
                foreach (var kvp in data)
                {
                    if (kvp.Key == employeeIdKey) continue; // Skip employee ID

                    var value = kvp.Value?.ToString()?.Trim();
                    if (string.IsNullOrEmpty(value)) continue;

                    // Check if this should be a numeric field based on metadata
                    var isNumericField = await IsNumericFieldAsync(detail.PartnerReferenceDataUploadId, kvp.Key);
                    if (isNumericField)
                    {
                        if (!decimal.TryParse(value.Replace(",", "").Replace("$", "").Replace("%", ""), out decimal numericValue))
                        {
                            errors.Add($"Invalid numeric value for {kvp.Key}: {value}");
                        }
                        else if (numericValue < 0)
                        {
                            errors.Add($"Negative values not allowed for {kvp.Key}: {value}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                errors.Add($"Error validating row data: {ex.Message}");
                Logger?.LogError(ex, "Error validating upload detail for row: {RowId}", detail.RowId);
            }

            return errors;
        }

 
        /// <summary>
        /// Check if a field should be numeric based on metadata
        /// </summary>
        /// <param name="uploadId">Upload ID</param>
        /// <param name="normalizedColumnName">Normalized column name</param>
        /// <returns>True if field should be numeric</returns>
        private Task<bool> IsNumericFieldAsync(Guid uploadId, string normalizedColumnName)
        {
            try
            {
                var upload = UOW.PartnerReferenceDataUploads.GetById(uploadId);
                if (upload == null) return Task.FromResult(false);

                var metaDetail = UOW.PartnerReferenceDataMetaDetails
                    .Query(x => x.MetaId == upload.MetaId && x.NormalizedColumnName == normalizedColumnName)
                    .FirstOrDefault();

                return Task.FromResult(metaDetail?.ColumnDataType == (byte)Enumerations.PartnerReferenceDataColumnType.Numeric);
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "Error checking if field is numeric: {ColumnName}", normalizedColumnName);
                return Task.FromResult(false);
            }
        }

        #endregion

        #region Placeholder Methods - To be implemented

        /// <summary>
        /// Extract metadata from uploaded file
        /// </summary>
        public async Task<BusinessResult<PartnerReferenceDataMeta>> ExtractMetadataFromFileAsync(IFormFile file, short year, byte cycle)
        {
            var result = new BusinessResult<PartnerReferenceDataMeta>();

            try
            {
                if (file == null || file.Length == 0)
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "File is required and cannot be empty";
                    return result;
                }

                var extension = Path.GetExtension(file.FileName).ToLowerInvariant();
                if (extension != ".xlsx" && extension != ".csv")
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "Only Excel (.xlsx) and CSV (.csv) files are supported";
                    return result;
                }

                // Extract column headers from file
                var columnHeaders = await ExtractColumnHeadersFromFileAsync(file, extension);
                if (!columnHeaders.Any())
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "No column headers found in the file";
                    return result;
                }

                // Create metadata record
                var metaEntity = new Entity.PartnerReferenceDataMeta
                {
                    Id = SequentialGuid.NewGuid(), // Use SequentialGuid for better database performance
                    FileName = file.FileName,
                    Year = year,
                    Cycle = cycle,
                    IsActive = true,
                    CreatedBy = CurrentUser?.Id,
                    CreatedByName = CurrentUser?.Email,
                    CreatedOn = CurrentDateTime
                };

                UOW.PartnerReferenceDataMetas.Add(metaEntity);

                // First commit the meta record to ensure it exists in the database
                if (UOW.Commit() > 0)
                {
                    // Normalize all column names to ensure uniqueness
                    var normalizedNames = NormalizeColumnNames(columnHeaders);

                    // Now create metadata details for each column
                    var metaDetails = new List<Entity.PartnerReferenceDataMetaDetails>();
                    for (int i = 0; i < columnHeaders.Count; i++)
                    {
                        var columnName = columnHeaders[i];
                        var columnDataType = await DetermineColumnDataTypeAsync(file, i, extension);

                        var metaDetail = new Entity.PartnerReferenceDataMetaDetails
                        {
                            Id = SequentialGuid.NewGuid(), // Use SequentialGuid for better database performance
                            MetaId = metaEntity.Id,
                            ColumnName = columnName,
                            NormalizedColumnName = normalizedNames[columnName], // Use the new camelCase normalization with uniqueness
                            ColumnDataType = columnDataType,
                            ColumnOrder = (short)(i + 1),
                            CreatedBy = CurrentUser?.Id,
                            CreatedByName = CurrentUser?.Email,
                            CreatedOn = CurrentDateTime
                        };

                        metaDetails.Add(metaDetail);
                        UOW.PartnerReferenceDataMetaDetails.Add(metaDetail);
                    }

                    // Commit the meta details
                    if (UOW.Commit() > 0)
                    {
                        // Load the created entity with its details
                        var createdMeta = UOW.PartnerReferenceDataMetas.Query(x => x.Id == metaEntity.Id)
                            .Include(x => x.PartnerReferenceDataMetaDetails)
                            .FirstOrDefault();

                        result.Item = _mapper.Map<PartnerReferenceDataMeta>(createdMeta);
                        result.ResultStatus = ResultStatus.Success;
                        result.Message = $"Metadata extracted successfully. Found {columnHeaders.Count} columns.";
                    }
                    else
                    {
                        result.ResultStatus = ResultStatus.Failure;
                        result.Message = "Failed to save metadata details to database";
                    }
                }
                else
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "Failed to save metadata to database";
                }
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "Error extracting metadata from file: {FileName}", file.FileName);
                result.ResultStatus = ResultStatus.Failure;
                result.Message = $"Error extracting metadata: {ex.Message}";
            }

            return result;
        }

        /// <summary>
        /// Upload and process Excel/CSV file
        /// </summary>
        public async Task<BusinessResult<PartnerReferenceDataUpload>> UploadFileAsync(IFormFile file, short year, byte cycle)
        {
            var result = new BusinessResult<PartnerReferenceDataUpload>();

            try
            {
                // Validate input parameters
                if (file == null || file.Length == 0)
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "File is required and cannot be empty";
                    return result;
                }

                var extension = Path.GetExtension(file.FileName).ToLowerInvariant();
                if (extension != ".xlsx" && extension != ".csv")
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "Only Excel (.xlsx) and CSV (.csv) files are supported";
                    return result;
                }

                // Get or create metadata for this year and cycle
                var metaResult = await GetOrCreateMetadataAsync(file, year, cycle);
                if (metaResult.ResultStatus != ResultStatus.Success)
                {
                    result.ResultStatus = metaResult.ResultStatus;
                    result.Message = metaResult.Message;
                    return result;
                }

                var meta = metaResult.Item;

                // Create upload record
                var uploadEntity = new Entity.PartnerReferenceDataUpload
                {
                    Id = SequentialGuid.NewGuid(),
                    UploadFileName = file.FileName,
                    Year = year,
                    Cycle = cycle,
                    MetaId = meta.Id,
                    Status = (byte)Enumerations.PartnerReferenceDataUploadStatus.Uploading,
                    CreatedBy = CurrentUser?.Id,
                    CreatedByName = CurrentUser?.Email,
                    CreatedOn = CurrentDateTime,
                    ModifiedBy = CurrentUser?.Id,
                    ModifiedByName = CurrentUser?.Email,
                    ModifiedOn = CurrentDateTime
                };

                // Read and store file content
                using (var memoryStream = new MemoryStream())
                {
                    await file.CopyToAsync(memoryStream);
                    uploadEntity.FileContent = memoryStream.ToArray();
                }

                UOW.PartnerReferenceDataUploads.Add(uploadEntity);

                // Process file data
                var processResult = await ProcessFileDataAsync(file, uploadEntity.Id, meta);
                if (processResult.ResultStatus != ResultStatus.Success)
                {
                    result.ResultStatus = processResult.ResultStatus;
                    result.Message = processResult.Message;
                    return result;
                }

                // Update upload status (no need to call Update since entity is already being tracked)
                uploadEntity.Status = (byte)Enumerations.PartnerReferenceDataUploadStatus.Uploaded;
                uploadEntity.ValidationSummary = processResult.Message;
                uploadEntity.ModifiedBy = CurrentUser?.Id;
                uploadEntity.ModifiedByName = CurrentUser?.Email;
                uploadEntity.ModifiedOn = CurrentDateTime;

                // Commit all changes (upload entity and upload details)
                UOW.Commit();

                // Automatically trigger validation after successful file processing
                try
                {
                    await ValidateUploadAsync(uploadEntity.Id);
                }
                catch (Exception validationEx)
                {
                    Logger?.LogWarning(validationEx, "Automatic validation failed for upload ID: {UploadId}, but upload was successful", uploadEntity.Id);
                }

                // Reload the entity with navigation properties for proper mapping
                var reloadedEntity = UOW.PartnerReferenceDataUploads
                    .Query(x => x.Id == uploadEntity.Id)
                    .Include(x => x.Meta)
                    .ThenInclude(x => x.PartnerReferenceDataMetaDetails)
                    .FirstOrDefault();

                // Map to DTO and populate additional properties
                var uploadDto = _mapper.Map<PartnerReferenceDataUpload>(reloadedEntity ?? uploadEntity);
                PopulateUploadProperties(uploadDto);
                PopulateUploadStatistics(uploadDto);

                result.Item = uploadDto;
                result.ResultStatus = ResultStatus.Success;
                result.Message = "File uploaded successfully";

                Logger?.LogInformation("File uploaded successfully: {FileName} for Year: {Year}, Cycle: {Cycle}",
                    file.FileName, year, cycle);
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
                Logger?.LogError(ex, "Error uploading file: {FileName} for Year: {Year}, Cycle: {Cycle}",
                    file?.FileName, year, cycle);
            }

            return result;
        }

        /// <summary>
        /// Validate uploaded data
        /// </summary>
        public async Task<BusinessResult<PartnerReferenceDataUpload>> ValidateUploadAsync(Guid uploadId)
        {
            var result = new BusinessResult<PartnerReferenceDataUpload>();

            try
            {
                // Get the upload record
                var upload = UOW.PartnerReferenceDataUploads.GetById(uploadId);
                if (upload == null)
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "Upload not found";
                    return result;
                }

                // Update status to validating
                upload.Status = (byte)Enumerations.PartnerReferenceDataUploadStatus.Validating;
                upload.ModifiedBy = CurrentUser?.Id;
                upload.ModifiedByName = CurrentUser?.Email;
                upload.ModifiedOn = CurrentDateTime;
                UOW.PartnerReferenceDataUploads.Update(upload);
                UOW.Commit();

                // Get upload details to validate
                var uploadDetails = UOW.PartnerReferenceDataUploadDetails
                    .Query(x => x.PartnerReferenceDataUploadId == uploadId)
                    .ToList();

                if (!uploadDetails.Any())
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "No upload details found to validate";
                    return result;
                }

                // Get all partners for validation (cache for performance)
                var partners = UOW.Partners.Query(x => x.IsActive == true).ToList();
                var partnerDict = partners.Where(p => p.EmployeeId.HasValue)
                    .ToDictionary(p => p.EmployeeId.Value.ToString(), p => p);

                // Validate each upload detail
                var validRecords = 0;
                var invalidRecords = 0;

                foreach (var detail in uploadDetails)
                {
                    var validationErrors = await ValidateUploadDetailAsync(detail, partnerDict);

                    if (validationErrors.Any())
                    {
                        detail.ValidationError = string.Join("; ", validationErrors);
                        invalidRecords++;
                    }
                    else
                    {
                        detail.ValidationError = null;
                        validRecords++;
                    }

                    detail.ModifiedBy = CurrentUser?.Id;
                    detail.ModifiedByName = CurrentUser?.Email;
                    detail.ModifiedOn = CurrentDateTime;
                    UOW.PartnerReferenceDataUploadDetails.Update(detail);
                }

                // Update upload status and summary
                upload.Status = invalidRecords > 0
                    ? (byte)Enumerations.PartnerReferenceDataUploadStatus.ValidationFailed
                    : (byte)Enumerations.PartnerReferenceDataUploadStatus.ValidationPassed;

                upload.ValidationSummary = $"Total: {uploadDetails.Count}, Valid: {validRecords}, Invalid: {invalidRecords}";
                upload.ModifiedBy = CurrentUser?.Id;
                upload.ModifiedByName = CurrentUser?.Email;
                upload.ModifiedOn = CurrentDateTime;

                UOW.PartnerReferenceDataUploads.Update(upload);
                UOW.Commit();

                // Map to DTO and populate additional properties
                var uploadDto = _mapper.Map<PartnerReferenceDataUpload>(upload);
                PopulateUploadProperties(uploadDto);
                PopulateUploadStatistics(uploadDto);

                result.Item = uploadDto;
                result.ResultStatus = ResultStatus.Success;
                result.Message = invalidRecords > 0
                    ? $"Validation completed with {invalidRecords} errors"
                    : "Validation completed successfully";

                Logger?.LogInformation("Upload validation completed for ID: {UploadId}, Valid: {ValidRecords}, Invalid: {InvalidRecords}",
                    uploadId, validRecords, invalidRecords);
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
                Logger?.LogError(ex, "Error validating upload: {UploadId}", uploadId);
            }

            return result;
        }

        /// <summary>
        /// Submit validated data to final table
        /// </summary>
        public Task<BusinessResult<bool>> SubmitUploadAsync(Guid uploadId, bool overwriteExisting = true)
        {
            var result = new BusinessResult<bool>();

            try
            {
                // Get the upload record
                var upload = UOW.PartnerReferenceDataUploads.GetById(uploadId);
                if (upload == null)
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "Upload not found";
                    return Task.FromResult(result);
                }

                // Check if upload has passed validation
                if (upload.Status != (byte)Enumerations.PartnerReferenceDataUploadStatus.ValidationPassed)
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "Upload must pass validation before submission";
                    return Task.FromResult(result);
                }

                // Get valid upload details (only those without validation errors)
                var validUploadDetails = UOW.PartnerReferenceDataUploadDetails
                    .Query(x => x.PartnerReferenceDataUploadId == uploadId && string.IsNullOrEmpty(x.ValidationError))
                    .ToList();

                if (!validUploadDetails.Any())
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "No valid records found to submit";
                    return Task.FromResult(result);
                }

                // Get all partners for mapping employee IDs to partner IDs
                var partners = UOW.Partners.Query(x => x.IsActive == true).ToList();
                var partnerDict = partners.Where(p => p.EmployeeId.HasValue)
                    .ToDictionary(p => p.EmployeeId.Value.ToString(), p => p);

                var submittedRecords = 0;
                var skippedRecords = 0;

                // Get the upload record with metadata to find Employee ID field
                var uploadWithMeta = UOW.PartnerReferenceDataUploads.Query(x => x.Id == uploadId)
                    .Include(x => x.Meta)
                    .Include(x => x.Meta.PartnerReferenceDataMetaDetails)
                    .FirstOrDefault();

                if (uploadWithMeta?.Meta?.PartnerReferenceDataMetaDetails == null)
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "Upload metadata not found";
                    return Task.FromResult(result);
                }

                // Find the Employee ID field from metadata
                var employeeIdMetaDetail = uploadWithMeta.Meta.PartnerReferenceDataMetaDetails
                    .FirstOrDefault(x => x.ColumnName.Contains("Employee ID", StringComparison.OrdinalIgnoreCase) ||
                                        x.ColumnName.Contains("EmployeeID", StringComparison.OrdinalIgnoreCase) ||
                                        x.ColumnName.Contains("Emp ID", StringComparison.OrdinalIgnoreCase));

                if (employeeIdMetaDetail == null)
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "Employee ID column not found in metadata";
                    return Task.FromResult(result);
                }

                var employeeIdKey = employeeIdMetaDetail.NormalizedColumnName;

                foreach (var uploadDetail in validUploadDetails)
                {
                    try
                    {
                        // Parse the JSON data
                        var data = JsonConvert.DeserializeObject<Dictionary<string, object>>(uploadDetail.Data);
                        if (data == null || !data.ContainsKey(employeeIdKey))
                        {
                            skippedRecords++;
                            continue;
                        }

                        var employeeId = data[employeeIdKey].ToString().Trim();
                        if (!partnerDict.ContainsKey(employeeId))
                        {
                            skippedRecords++;
                            continue;
                        }

                        var partner = partnerDict[employeeId];

                        // Check if data already exists for this partner/year/cycle
                        var existingData = UOW.PartnerReferenceData
                            .Query(x => x.PartnerId == partner.Id && x.Year == upload.Year && x.Cycle == upload.Cycle)
                            .FirstOrDefault();

                        if (existingData != null)
                        {
                            if (overwriteExisting)
                            {
                                // Update existing record
                                existingData.Data = uploadDetail.Data;
                                existingData.MetaId = upload.MetaId;
                                existingData.ModifiedBy = CurrentUser?.Id;
                                existingData.ModifiedByName = CurrentUser?.Email;
                                existingData.ModifiedOn = CurrentDateTime;
                                UOW.PartnerReferenceData.Update(existingData);
                                submittedRecords++;
                            }
                            else
                            {
                                skippedRecords++;
                            }
                        }
                        else
                        {
                            // Create new record
                            var newData = new Entity.PartnerReferenceData
                            {
                                Id = SequentialGuid.NewGuid(),
                                PartnerId = partner.Id,
                                Year = upload.Year,
                                Cycle = upload.Cycle,
                                MetaId = upload.MetaId,
                                Data = uploadDetail.Data,
                                CreatedBy = CurrentUser?.Id,
                                CreatedByName = CurrentUser?.Email,
                                CreatedOn = CurrentDateTime,
                                ModifiedBy = CurrentUser?.Id,
                                ModifiedByName = CurrentUser?.Email,
                                ModifiedOn = CurrentDateTime
                            };

                            UOW.PartnerReferenceData.Add(newData);
                            submittedRecords++;
                        }
                    }
                    catch (Exception ex)
                    {
                        Logger?.LogError(ex, "Error processing upload detail for row: {RowId}", uploadDetail.RowId);
                        skippedRecords++;
                    }
                }

                // Update upload status
                upload.Status = (byte)Enumerations.PartnerReferenceDataUploadStatus.Submitted;
                upload.ValidationSummary += $" | Submitted: {submittedRecords}, Skipped: {skippedRecords}";
                upload.ModifiedBy = CurrentUser?.Id;
                upload.ModifiedByName = CurrentUser?.Email;
                upload.ModifiedOn = CurrentDateTime;
                UOW.PartnerReferenceDataUploads.Update(upload);

                // Commit all changes
                var commitResult = UOW.Commit();
                if (commitResult > 0)
                {
                    result.Item = true;
                    result.ResultStatus = ResultStatus.Success;
                    result.Message = $"Successfully submitted {submittedRecords} records. {skippedRecords} records were skipped.";

                    Logger?.LogInformation("Upload submitted successfully: {UploadId}, Submitted: {SubmittedRecords}, Skipped: {SkippedRecords}",
                        uploadId, submittedRecords, skippedRecords);
                }
                else
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "Failed to commit changes to database";
                }
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
                Logger?.LogError(ex, "Error submitting upload: {UploadId}", uploadId);
            }

            return Task.FromResult(result);
        }

        /// <summary>
        /// Get partner reference data
        /// </summary>
        public BusinessResult<PartnerReferenceData> GetPartnerReferenceData(Guid partnerId, short year, byte cycle)
        {
            var result = new BusinessResult<PartnerReferenceData>();

            try
            {
                // Get partner reference data for the specified partner, year, and cycle
                var entity = UOW.PartnerReferenceData
                    .Query(x => x.PartnerId == partnerId && x.Year == year && x.Cycle == cycle)
                    .Include(x => x.Partner)
                    .Include(x => x.Meta)
                    .FirstOrDefault();

                if (entity != null)
                {
                    // Map to DTO
                    var dto = _mapper.Map<PartnerReferenceData>(entity);

                    // Populate additional properties
                    PopulatePartnerReferenceDataProperties(dto);

                    result.Item = dto;
                    result.ResultStatus = ResultStatus.Success;
                }
                else
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = $"No partner reference data found for Partner ID: {partnerId}, Year: {year}, Cycle: {cycle}";
                }
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
                Logger?.LogError(ex, "Error getting partner reference data for Partner ID: {PartnerId}, Year: {Year}, Cycle: {Cycle}",
                    partnerId, year, cycle);
            }

            return result;
        }

        /// <summary>
        /// Search partner reference data with filtering and pagination
        /// </summary>
        /// <param name="year">Filter by year</param>
        /// <param name="cycle">Filter by cycle</param>
        /// <param name="partnerId">Filter by partner ID</param>
        /// <param name="pageIndex">Page index for pagination (0-based)</param>
        /// <param name="pageSize">Page size for pagination</param>
        /// <returns>Paginated collection of partner reference data</returns>
        public BusinessResult<IPagedList<PartnerReferenceData>> SearchPartnerReferenceData(short? year = null,
            byte? cycle = null, Guid? partnerId = null, int pageIndex = 0, int pageSize = 20)
        {
            var result = new BusinessResult<IPagedList<PartnerReferenceData>>();
            try
            {
                IQueryable<Entity.PartnerReferenceData> query = UOW.PartnerReferenceData.GetAll()
                    .Include(x => x.Partner)
                    .Include(x => x.Meta);

                // Apply filters
                if (year.HasValue)
                    query = query.Where(x => x.Year == year.Value);

                if (cycle.HasValue)
                    query = query.Where(x => x.Cycle == cycle.Value);

                if (partnerId.HasValue)
                    query = query.Where(x => x.PartnerId == partnerId.Value);

                // Order by employee ID ascending for consistent sorting and apply pagination with AutoMapper integration
                var orderedQuery = query.OrderBy(x => x.Partner.EmployeeId);
                var pagedDtos = orderedQuery.ToPagedList<PartnerReferenceData, Entity.PartnerReferenceData>(
                    _mapper, pageIndex, pageSize);

                // Populate additional properties
                foreach (var dto in pagedDtos.Items)
                {
                    PopulatePartnerReferenceDataProperties(dto);
                }

                result.Item = pagedDtos;
                result.ResultStatus = ResultStatus.Success;
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
                Logger?.LogError(ex, "Error searching partner reference data with filters - Year: {Year}, Cycle: {Cycle}, PartnerId: {PartnerId}",
                    year, cycle, partnerId);
            }
            return result;
        }

        /// <summary>
        /// Get upload template - Placeholder implementation
        /// </summary>
        public BusinessResult<byte[]> GetUploadTemplate()
        {
            // TODO: Implement template generation logic
            throw new NotImplementedException("GetUploadTemplate method needs to be implemented");
        }

        /// <summary>
        /// Get available column names for Form Creator mapping based on questionnaire year
        /// </summary>
        /// <param name="year">Questionnaire year to get relevant column names for</param>
        /// <param name="includeCyclePrefixes">Whether to include cycle prefixes for disambiguation</param>
        /// <returns>List of available column names with cycle context</returns>
        public BusinessResult<ICollection<FormCreatorColumnChoice>> GetAvailableColumnNamesForFormCreator(short year, bool includeCyclePrefixes = true)
        {
            var result = new BusinessResult<ICollection<FormCreatorColumnChoice>>();
            try
            {
                // Get all active metadata for the specified year
                var metadataList = UOW.PartnerReferenceDataMetas
                    .Query(x => x.Year == year && x.IsActive == true)
                    .Include(x => x.PartnerReferenceDataMetaDetails)
                    .OrderBy(x => x.Cycle)
                    .ToList();

                if (!metadataList.Any())
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = $"No active partner reference data metadata found for year {year}";
                    result.Item = new List<FormCreatorColumnChoice>();
                    return result;
                }

                var columnChoices = new List<FormCreatorColumnChoice>();
                var uniqueColumns = new HashSet<string>();

                foreach (var meta in metadataList)
                {
                    var cyclePrefix = GetCyclePrefixForFormCreator(meta.Cycle);
                    var cycleName = GetPartnerReferenceDataCycleString(meta.Cycle);

                    if (meta.PartnerReferenceDataMetaDetails != null)
                    {
                        foreach (var detail in meta.PartnerReferenceDataMetaDetails.OrderBy(d => d.ColumnOrder))
                        {
                            if (string.IsNullOrWhiteSpace(detail.ColumnName))
                                continue;

                            var columnName = detail.ColumnName.Trim();
                            var displayName = columnName;
                            var value = columnName;

                            if (includeCyclePrefixes)
                            {
                                // Add cycle prefix for disambiguation
                                displayName = $"({cyclePrefix}) - {columnName}";
                                value = $"{cyclePrefix}_{detail.NormalizedColumnName}";
                            }

                            // Avoid duplicates when not using prefixes
                            var uniqueKey = includeCyclePrefixes ? value : columnName;
                            if (uniqueColumns.Add(uniqueKey))
                            {
                                columnChoices.Add(new FormCreatorColumnChoice
                                {
                                    Value = value,
                                    Text = displayName,
                                    Cycle = meta.Cycle,
                                    CycleName = cycleName,
                                    OriginalColumnName = columnName,
                                    NormalizedColumnName = detail.NormalizedColumnName,
                                    ColumnDataType = (Enumerations.PartnerReferenceDataColumnType)detail.ColumnDataType,
                                    MetaId = meta.Id,
                                    ColumnDataTypeString = GetColumnDataTypeString(detail.ColumnDataType)
                                });
                            }
                        }
                    }
                }

                // Sort by display name for better UX
                var sortedChoices = columnChoices
                    .OrderBy(c => c.Cycle)
                    .ThenBy(c => c.Text)
                    .ToList();

                result.Item = sortedChoices;
                result.ResultStatus = ResultStatus.Success;

                Logger?.LogInformation("Retrieved {Count} column choices for Form Creator (Year: {Year}, IncludePrefixes: {IncludePrefixes})",
                    sortedChoices.Count, year, includeCyclePrefixes);
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
                Logger?.LogError(ex, "Error getting column names for Form Creator - Year: {Year}", year);
            }
            return result;
        }

        /// <summary>
        /// Get partner reference data metadata by year with all cycles
        /// </summary>
        /// <param name="year">Year to filter metadata</param>
        /// <returns>List of metadata for the specified year</returns>
        public BusinessResult<ICollection<PartnerReferenceDataMeta>> GetPartnerReferenceDataMetasByYear(short year)
        {
            var result = new BusinessResult<ICollection<PartnerReferenceDataMeta>>();
            try
            {
                var entities = UOW.PartnerReferenceDataMetas
                    .Query(x => x.Year == year && x.IsActive == true)
                    .Include(x => x.PartnerReferenceDataMetaDetails)
                    .OrderBy(x => x.Cycle)
                    .ToList();

                var dtos = entities.Select(entity => _mapper.Map<PartnerReferenceDataMeta>(entity)).ToList();

                // Populate additional properties
                foreach (var dto in dtos)
                {
                    PopulatePartnerReferenceDataMetaProperties(dto);
                }

                result.Item = dtos;
                result.ResultStatus = ResultStatus.Success;

                Logger?.LogInformation("Retrieved {Count} metadata records for year {Year}", dtos.Count, year);
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
                Logger?.LogError(ex, "Error getting metadata by year - Year: {Year}", year);
            }
            return result;
        }

        /// <summary>
        /// Get cycle prefix for Form Creator column naming
        /// </summary>
        /// <param name="cycle">Cycle value</param>
        /// <returns>Short prefix for the cycle</returns>
        private static string GetCyclePrefixForFormCreator(byte cycle)
        {
            return cycle switch
            {
                (byte)Enumerations.PartnerReferenceDataCycle.Planning => "Plan",
                (byte)Enumerations.PartnerReferenceDataCycle.MidYearReview => "Mid",
                (byte)Enumerations.PartnerReferenceDataCycle.EndYearReview => "End",
                _ => "Unk"
            };
        }

        /// <summary>
        /// Populate additional properties for PartnerReferenceDataMeta
        /// </summary>
        /// <param name="dto">DTO to populate</param>
        private void PopulatePartnerReferenceDataMetaProperties(PartnerReferenceDataMeta dto)
        {
            if (dto != null)
            {
                dto.CycleString = GetPartnerReferenceDataCycleString((byte)dto.Cycle);
                // Additional property population can be added here
            }
        }

        /// <summary>
        /// Get cycle string for PartnerReferenceDataMeta
        /// </summary>
        /// <param name="cycle">Cycle byte value</param>
        /// <returns>Cycle string</returns>
        private static string GetPartnerReferenceDataCycleString(byte cycle)
        {
            return cycle switch
            {
                (byte)Enumerations.PartnerReferenceDataCycle.Planning => "Planning",
                (byte)Enumerations.PartnerReferenceDataCycle.MidYearReview => "Mid Year Review",
                (byte)Enumerations.PartnerReferenceDataCycle.EndYearReview => "End Year Review",
                _ => "Unknown"
            };
        }

        /// <summary>
        /// Get column data type string for display
        /// </summary>
        /// <param name="columnDataType">Column data type byte value</param>
        /// <returns>Column data type string</returns>
        private static string GetColumnDataTypeString(byte columnDataType)
        {
            return columnDataType switch
            {
                (byte)Enumerations.PartnerReferenceDataColumnType.Text => "Text",
                (byte)Enumerations.PartnerReferenceDataColumnType.Numeric => "Numeric",
                (byte)Enumerations.PartnerReferenceDataColumnType.Blank => "Blank",
                _ => "Unknown"
            };
        }

        #endregion
    }
}
