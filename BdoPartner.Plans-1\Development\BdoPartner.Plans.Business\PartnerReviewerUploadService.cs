using AutoMapper;
using BdoPartner.Plans.Business.Interface;
using BdoPartner.Plans.Common;
using BdoPartner.Plans.Common.Config;
using BdoPartner.Plans.DataAccess;
using BdoPartner.Plans.DataAccess.Common.PagedList;
using BdoPartner.Plans.Model.DTO;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Entity = BdoPartner.Plans.Model.Entity;
using OfficeOpenXml;
using System.Text;

namespace BdoPartner.Plans.Business
{
    /// <summary>
    /// Business service implementation for Partner Reviewer Upload operations
    /// </summary>
    public class PartnerReviewerUploadService : BaseService, IPartnerReviewerUploadService
    {
        private readonly IMapper _mapper;

        public PartnerReviewerUploadService(IUnitOfWork uow, IConfigSettings config, ILogger<BaseService> logger,
            IHttpContextAccessor httpContextAccessor, IMapper mapper)
            : base(uow, httpContextAccessor, config, logger)
        {
            _mapper = mapper;
            // Set EPPlus license context for non-commercial use
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

        }

        public BusinessResult<ICollection<PartnerReviewerUpload>> GetPartnerReviewerUploads()
        {
            var result = new BusinessResult<ICollection<PartnerReviewerUpload>>();
            try
            {
                var uploads = UOW.PartnerReviewerUploads.GetAll().OrderByDescending(u => u.CreatedOn).ToList();
                var mappedUploads = _mapper.Map<ICollection<PartnerReviewerUpload>>(uploads);

                result.Item = mappedUploads;
                result.ResultStatus = ResultStatus.Success;
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
                Logger.LogError(ex, "Error getting partner reviewer uploads");
            }
            return result;
        }

        public BusinessResult<IPagedList<PartnerReviewerUpload>> SearchPartnerReviewerUploads(short? year = null,
            byte? status = null, int pageIndex = 0, int pageSize = 20)
        {
            var result = new BusinessResult<IPagedList<PartnerReviewerUpload>>();
            try
            {
                var query = UOW.PartnerReviewerUploads.GetAll().AsQueryable();

                if (year.HasValue)
                    query = query.Where(u => u.Years.Contains(year.Value.ToString()));

                if (status.HasValue)
                    query = query.Where(u => u.Status == status.Value);

                // Apply ordering and use PagedList for better pagination
                var orderedQuery = query.OrderByDescending(u => u.CreatedOn);

                // Use the PagedList extension with AutoMapper integration
                var pagedUploads = orderedQuery.ToPagedList<PartnerReviewerUpload, Entity.PartnerReviewerUpload>(
                    _mapper, pageIndex, pageSize);

                result.Item = pagedUploads;

                result.ResultStatus = ResultStatus.Success;
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
                Logger.LogError(ex, "Error searching partner reviewer uploads");
            }
            return result;
        }

        public BusinessResult<PartnerReviewerUpload> GetPartnerReviewerUploadById(int id)
        {
            var result = new BusinessResult<PartnerReviewerUpload>();
            try
            {
                var upload = UOW.PartnerReviewerUploads.GetById(id);
                if (upload != null)
                {
                    result.Item = _mapper.Map<PartnerReviewerUpload>(upload);
                    result.ResultStatus = ResultStatus.Success;
                }
                else
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "Upload not found";
                }
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
                Logger.LogError(ex, $"Error getting partner reviewer upload by id: {id}");
            }
            return result;
        }


        public BusinessResult<ICollection<PartnerReviewerUploadDetails>> GetPartnerReviewerUploadDetails(int uploadId,
            bool includeValidOnly = false, bool includeInvalidOnly = false)
        {
            var result = new BusinessResult<ICollection<PartnerReviewerUploadDetails>>();
            try
            {
                // Get entities first (without ProjectTo to avoid SQL translation issues)
                var query = UOW.PartnerReviewerUploadDetailses.Query(d => d.PartnerReviewerUploadId == uploadId);

                if (includeValidOnly)
                    query = query.Where(d => string.IsNullOrEmpty(d.ValidationError));
                else if (includeInvalidOnly)
                    query = query.Where(d => !string.IsNullOrEmpty(d.ValidationError));

                // Execute query to get entities
                var entities = query.OrderBy(d => d.RowId).ToList();

                // Map entities to DTOs in memory
                result.Item = _mapper.Map<ICollection<PartnerReviewerUploadDetails>>(entities);
                result.ResultStatus = ResultStatus.Success;
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
                Logger.LogError(ex, $"Error getting partner reviewer upload details for upload: {uploadId}");
            }
            return result;
        }

        public async Task<BusinessResult<PartnerReviewerUpload>> UploadFileAsync(IFormFile file, string years)
        {
            var result = new BusinessResult<PartnerReviewerUpload>();
            try
            {
                // Validate file
                if (file == null || file.Length == 0)
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "No file uploaded";
                    return result;
                }

                // Validate file extension
                var extension = Path.GetExtension(file.FileName).ToLower();
                if (extension != ".xlsx" && extension != ".csv")
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "Only .xlsx and .csv files are supported";
                    return result;
                }

                // Create upload record
                var uploadEntity = new Entity.PartnerReviewerUpload
                {
                    Years = years,
                    UploadFileName = file.FileName,
                    Status = (byte)Enumerations.PartnerReviewerUploadStatus.Uploading,
                    CreatedBy = CurrentUser?.Id,
                    CreatedByName = CurrentUser?.Email,
                    CreatedOn = CurrentDateTime
                };

                // Read file content
                using (var memoryStream = new MemoryStream())
                {
                    await file.CopyToAsync(memoryStream);
                    uploadEntity.FileContent = memoryStream.ToArray();
                }

                UOW.PartnerReviewerUploads.Add(uploadEntity);

                if (UOW.Commit() > 0)
                {
                    // Process file content
                    await ProcessFileContentAsync(uploadEntity.Id, uploadEntity.FileContent, extension);
                    
                    result.Item = _mapper.Map<PartnerReviewerUpload>(uploadEntity);
                    result.ResultStatus = ResultStatus.Success;
                }
                else
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "Failed to save upload";
                }
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
                Logger.LogError(ex, "Error uploading file");
            }
            return result;
        }

        private async Task ProcessFileContentAsync(int uploadId, byte[] fileContent, string extension)
        {
            try
            {
                await ProcessFileContentInternalAsync(uploadId, fileContent, extension);

                // Automatically trigger validation after successful file processing
                await ValidateUploadInternalAsync(uploadId);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, $"Error processing file content for upload: {uploadId}");

                // Update upload status to failed
                var upload = UOW.PartnerReviewerUploads.GetById(uploadId);
                if (upload != null)
                {
                    upload.Status = (byte)Enumerations.PartnerReviewerUploadStatus.ValidationFailed;
                    upload.ValidationSummary = $"File processing failed: {ex.Message}";
                    upload.ModifiedBy = CurrentUser?.Id;
                    upload.ModifiedByName = CurrentUser?.Email;
                    upload.ModifiedOn = CurrentDateTime;
                    UOW.PartnerReviewerUploads.Update(upload);
                    UOW.Commit();
                }
            }
        }

        private async Task ProcessFileContentInternalAsync(int uploadId, byte[] fileContent, string extension)
        {
            List<Entity.PartnerReviewerUploadDetails> details = new List<Entity.PartnerReviewerUploadDetails>();

            if (extension == ".xlsx")
            {
                details = await ProcessExcelFileAsync(fileContent);
            }
            else if (extension == ".csv")
            {
                details = ProcessCsvFile(fileContent);
            }

            // Set upload ID for all details
            foreach (var detail in details)
            {
                detail.PartnerReviewerUploadId = uploadId;
            }

            // Save details
            foreach (var detail in details)
            {
                UOW.PartnerReviewerUploadDetailses.Add(detail);
            }

            // Update upload status
            var upload = UOW.PartnerReviewerUploads.GetById(uploadId);
            if (upload != null)
            {
                upload.Status = (byte)Enumerations.PartnerReviewerUploadStatus.Uploaded;
                upload.ModifiedBy = CurrentUser?.Id;
                upload.ModifiedByName = CurrentUser?.Email;
                upload.ModifiedOn = CurrentDateTime;
                UOW.PartnerReviewerUploads.Update(upload);
            }

            UOW.Commit();
        }

        private async Task<List<Entity.PartnerReviewerUploadDetails>> ProcessExcelFileAsync(byte[] fileContent)
        {
            var details = new List<Entity.PartnerReviewerUploadDetails>();

            using (var stream = new MemoryStream(fileContent))
            using (var package = new ExcelPackage(stream))
            {
                var worksheet = package.Workbook.Worksheets.FirstOrDefault();
                if (worksheet == null)
                {
                    throw new InvalidOperationException("No worksheet found in Excel file");
                }

                // Validate required columns
                var requiredColumns = new[] { "EmployeeID", "EmployeeName", "Exempt", "LeadershipRole", "PrimaryReviewerID", "PrimaryReviewerName", "SecondaryReviewerID", "SecondaryReviewerName" };
                var headerRow = 1;
                var columnMapping = new Dictionary<string, int>();

                for (int col = 1; col <= worksheet.Dimension.Columns; col++)
                {
                    var headerValue = worksheet.Cells[headerRow, col].Value?.ToString();
                    if (!string.IsNullOrEmpty(headerValue))
                    {
                        columnMapping[headerValue] = col;
                    }
                }

                // Check for missing required columns
                var missingColumns = requiredColumns.Where(col => !columnMapping.ContainsKey(col)).ToList();
                if (missingColumns.Any())
                {
                    throw new InvalidOperationException($"Missing required columns: {string.Join(", ", missingColumns)}");
                }

                // Process data rows
                for (int row = headerRow + 1; row <= worksheet.Dimension.Rows; row++)
                {
                    var validationErrors = new List<string>();

                    var detail = new Entity.PartnerReviewerUploadDetails
                    {
                        RowId = row,
                        EmployeeId = SafeTruncateString(worksheet.Cells[row, columnMapping["EmployeeID"]].Value?.ToString(), 50, "EmployeeID", validationErrors),
                        EmployeeName = SafeTruncateString(columnMapping.ContainsKey("EmployeeName") ? worksheet.Cells[row, columnMapping["EmployeeName"]].Value?.ToString() : null, 100, "EmployeeName", validationErrors),
                        Exempt = SafeTruncateString(worksheet.Cells[row, columnMapping["Exempt"]].Value?.ToString(), 10, "Exempt", validationErrors),
                        LeadershipRole = SafeTruncateString(worksheet.Cells[row, columnMapping["LeadershipRole"]].Value?.ToString(), 100, "LeadershipRole", validationErrors),
                        PrimaryReviewerId = SafeTruncateString(worksheet.Cells[row, columnMapping["PrimaryReviewerID"]].Value?.ToString(), 50, "PrimaryReviewerID", validationErrors),
                        PrimaryReviewerName = SafeTruncateString(columnMapping.ContainsKey("PrimaryReviewerName") ? worksheet.Cells[row, columnMapping["PrimaryReviewerName"]].Value?.ToString() : null, 100, "PrimaryReviewerName", validationErrors),
                        SecondaryReviewerId = SafeTruncateString(worksheet.Cells[row, columnMapping["SecondaryReviewerID"]].Value?.ToString(), 50, "SecondaryReviewerID", validationErrors),
                        SecondaryReviewerName = SafeTruncateString(columnMapping.ContainsKey("SecondaryReviewerName") ? worksheet.Cells[row, columnMapping["SecondaryReviewerName"]].Value?.ToString() : null, 100, "SecondaryReviewerName", validationErrors),
                        CreatedBy = CurrentUser?.Id,
                        CreatedByName = CurrentUser?.Email,
                        CreatedOn = CurrentDateTime
                    };

                    // Set initial validation errors if any data was truncated or had issues
                    if (validationErrors.Any())
                    {
                        detail.ValidationError = string.Join("; ", validationErrors);
                    }

                    // Skip empty rows
                    if (string.IsNullOrEmpty(detail.EmployeeId))
                        continue;

                    details.Add(detail);
                }
            }

            return details;
        }

        private List<Entity.PartnerReviewerUploadDetails> ProcessCsvFile(byte[] fileContent)
        {
            var details = new List<Entity.PartnerReviewerUploadDetails>();
            var csvContent = Encoding.UTF8.GetString(fileContent);
            var lines = csvContent.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);

            if (lines.Length < 2) // Header + at least one data row
            {
                throw new InvalidOperationException("CSV file must contain header and at least one data row");
            }

            // Parse header
            var headers = ParseCsvLine(lines[0]);
            var requiredColumns = new[] { "EmployeeID", "EmployeeName", "Exempt", "LeadershipRole", "PrimaryReviewerID", "PrimaryReviewerName", "SecondaryReviewerID", "SecondaryReviewerName" };
            var columnMapping = new Dictionary<string, int>();

            for (int i = 0; i < headers.Length; i++)
            {
                columnMapping[headers[i]] = i;
            }

            // Check for missing required columns
            var missingColumns = requiredColumns.Where(col => !columnMapping.ContainsKey(col)).ToList();
            if (missingColumns.Any())
            {
                throw new InvalidOperationException($"Missing required columns: {string.Join(", ", missingColumns)}");
            }

            // Process data rows
            for (int i = 1; i < lines.Length; i++)
            {
                var values = ParseCsvLine(lines[i]);
                var validationErrors = new List<string>();

                var detail = new Entity.PartnerReviewerUploadDetails
                {
                    RowId = i + 1, // i starts from 1 (first data row), so i+1 gives us the actual row number in the file
                    EmployeeId = SafeTruncateString(GetCsvValue(values, columnMapping, "EmployeeID"), 50, "EmployeeID", validationErrors),
                    EmployeeName = SafeTruncateString(GetCsvValue(values, columnMapping, "EmployeeName"), 100, "EmployeeName", validationErrors),
                    Exempt = SafeTruncateString(GetCsvValue(values, columnMapping, "Exempt"), 10, "Exempt", validationErrors),
                    LeadershipRole = SafeTruncateString(GetCsvValue(values, columnMapping, "LeadershipRole"), 100, "LeadershipRole", validationErrors),
                    PrimaryReviewerId = SafeTruncateString(GetCsvValue(values, columnMapping, "PrimaryReviewerID"), 50, "PrimaryReviewerID", validationErrors),
                    PrimaryReviewerName = SafeTruncateString(GetCsvValue(values, columnMapping, "PrimaryReviewerName"), 100, "PrimaryReviewerName", validationErrors),
                    SecondaryReviewerId = SafeTruncateString(GetCsvValue(values, columnMapping, "SecondaryReviewerID"), 50, "SecondaryReviewerID", validationErrors),
                    SecondaryReviewerName = SafeTruncateString(GetCsvValue(values, columnMapping, "SecondaryReviewerName"), 100, "SecondaryReviewerName", validationErrors),
                    CreatedBy = CurrentUser?.Id,
                    CreatedByName = CurrentUser?.Email,
                    CreatedOn = CurrentDateTime
                };

                // Set initial validation errors if any data was truncated or had issues
                if (validationErrors.Any())
                {
                    detail.ValidationError = string.Join("; ", validationErrors);
                }

                // Skip empty rows
                if (string.IsNullOrEmpty(detail.EmployeeId))
                    continue;

                details.Add(detail);
            }

            return details;
        }

        private string GetCsvValue(string[] values, Dictionary<string, int> columnMapping, string columnName)
        {
            if (columnMapping.ContainsKey(columnName) && columnMapping[columnName] < values.Length)
            {
                return values[columnMapping[columnName]];
            }
            return null;
        }

        /// <summary>
        /// Safely truncate string to fit database column limits and collect validation errors
        /// </summary>
        /// <param name="value">Original value</param>
        /// <param name="maxLength">Maximum allowed length</param>
        /// <param name="fieldName">Field name for error reporting</param>
        /// <param name="validationErrors">List to collect validation errors</param>
        /// <returns>Truncated value or null if original was null/empty</returns>
        private string SafeTruncateString(string value, int maxLength, string fieldName, List<string> validationErrors)
        {
            if (string.IsNullOrEmpty(value))
            {
                return null;
            }

            value = value.Trim();

            if (value.Length > maxLength)
            {
                validationErrors.Add($"{fieldName} exceeds maximum length of {maxLength} characters (was {value.Length}). Value truncated.");
                return value.Substring(0, maxLength);
            }

            return value;
        }

        /// <summary>
        /// Parse a CSV line handling quoted fields that may contain commas
        /// </summary>
        /// <param name="line">CSV line to parse</param>
        /// <returns>Array of field values</returns>
        private string[] ParseCsvLine(string line)
        {
            var fields = new List<string>();
            var currentField = new StringBuilder();
            bool inQuotes = false;
            bool fieldStarted = false;

            for (int i = 0; i < line.Length; i++)
            {
                char c = line[i];

                if (c == '"')
                {
                    if (!fieldStarted)
                    {
                        // Starting a quoted field
                        inQuotes = true;
                        fieldStarted = true;
                    }
                    else if (inQuotes)
                    {
                        // Check if this is an escaped quote (double quote)
                        if (i + 1 < line.Length && line[i + 1] == '"')
                        {
                            // Escaped quote - add one quote to the field
                            currentField.Append('"');
                            i++; // Skip the next quote
                        }
                        else
                        {
                            // End of quoted field
                            inQuotes = false;
                        }
                    }
                    else
                    {
                        // Quote in the middle of unquoted field - treat as regular character
                        currentField.Append(c);
                    }
                }
                else if (c == ',' && !inQuotes)
                {
                    // Field separator - end current field
                    fields.Add(currentField.ToString().Trim());
                    currentField.Clear();
                    fieldStarted = false;
                }
                else
                {
                    // Regular character
                    currentField.Append(c);
                    if (!fieldStarted)
                        fieldStarted = true;
                }
            }

            // Add the last field
            fields.Add(currentField.ToString().Trim());

            return fields.ToArray();
        }

        public async Task<BusinessResult<PartnerReviewerUpload>> ValidateUploadAsync(int uploadId)
        {
            var result = new BusinessResult<PartnerReviewerUpload>();
            try
            {
                var upload = await ValidateUploadInternalAsync(uploadId);
                result.Item = _mapper.Map<PartnerReviewerUpload>(upload);
                result.ResultStatus = ResultStatus.Success;
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
                Logger.LogError(ex, $"Error validating upload: {uploadId}");
            }
            return result;
        }

        private async Task<Entity.PartnerReviewerUpload> ValidateUploadInternalAsync(int uploadId)
        {
            var upload = UOW.PartnerReviewerUploads.GetById(uploadId);
            if (upload == null)
            {
                throw new InvalidOperationException("Upload not found");
            }

            // Update status to validating
            upload.Status = (byte)Enumerations.PartnerReviewerUploadStatus.Validating;
            upload.ModifiedBy = CurrentUser?.Id;
            upload.ModifiedByName = CurrentUser?.Email;
            upload.ModifiedOn = CurrentDateTime;
            UOW.PartnerReviewerUploads.Update(upload);
            UOW.Commit();

            // Get upload details
            var details = UOW.PartnerReviewerUploadDetailses.Query(d => d.PartnerReviewerUploadId == uploadId).ToList();

            // Check if details are empty but file content exists - try to regenerate details
            if (!details.Any() && upload.FileContent != null && upload.FileContent.Length > 0)
            {
                try
                {
                    // Get file extension from upload filename
                    var extension = Path.GetExtension(upload.UploadFileName)?.ToLower();
                    if (!string.IsNullOrEmpty(extension) && (extension == ".xlsx" || extension == ".csv"))
                    {
                        // Regenerate details from file content (but don't trigger validation recursively)
                        await ProcessFileContentInternalAsync(uploadId, upload.FileContent, extension);

                        // Refresh details after regeneration
                        details = UOW.PartnerReviewerUploadDetailses.Query(d => d.PartnerReviewerUploadId == uploadId).ToList();
                    }
                }
                catch (Exception ex)
                {
                    Logger.LogWarning(ex, $"Failed to regenerate details from file content for upload: {uploadId}");
                    // Continue with validation even if regeneration fails
                }
            }

            var validationErrors = new List<string>();
            var validRecords = 0;
            var invalidRecords = 0;

            // Get all partners for validation
            var partners = UOW.Partners.Query(p => p.IsActive == true).ToList();
            var partnerDict = partners.ToDictionary(p => p.EmployeeId?.ToString(), p => p);

            foreach (var detail in details)
            {
                var errors = ValidateUploadDetail(detail, partnerDict);

                // Merge existing validation errors (from truncation) with new validation errors
                var allErrors = new List<string>();
                if (!string.IsNullOrEmpty(detail.ValidationError))
                {
                    allErrors.Add(detail.ValidationError);
                }
                allErrors.AddRange(errors);

                detail.ValidationError = allErrors.Any() ? string.Join("; ", allErrors) : null;

                if (allErrors.Any())
                {
                    invalidRecords++;
                }
                else
                {
                    validRecords++;
                }

                UOW.PartnerReviewerUploadDetailses.Update(detail);
            }

            // Update upload status and summary
            upload.Status = validRecords > 0 && invalidRecords == 0
                ? (byte)Enumerations.PartnerReviewerUploadStatus.ValidationPassed
                : (byte)Enumerations.PartnerReviewerUploadStatus.ValidationFailed;

            upload.ValidationSummary = $"Total: {details.Count}, Valid: {validRecords}, Invalid: {invalidRecords}";
            upload.ModifiedBy = CurrentUser?.Id;
            upload.ModifiedByName = CurrentUser?.Email;
            upload.ModifiedOn = CurrentDateTime;
            UOW.PartnerReviewerUploads.Update(upload);

            UOW.Commit();

            return upload;
        }

        private List<string> ValidateUploadDetail(Entity.PartnerReviewerUploadDetails detail, Dictionary<string, Entity.Partner> partnerDict)
        {
            var errors = new List<string>();

            // Validate Employee ID and Name
            if (string.IsNullOrEmpty(detail.EmployeeId))
            {
                errors.Add("Employee ID is required");
            }
            else if (!partnerDict.ContainsKey(detail.EmployeeId))
            {
                errors.Add($"Invalid Employee ID {detail.EmployeeId}");
            }
            else
            {
                // Validate employee name matches partner record
                var partner = partnerDict[detail.EmployeeId];
                if (!string.IsNullOrEmpty(detail.EmployeeName) && !IsNameMatch(detail.EmployeeName, partner.FirstName, partner.LastName, partner.DisplayName))
                {
                    errors.Add($"Employee name '{detail.EmployeeName}' does not match partner record for Employee ID {detail.EmployeeId}");
                }
            }

            // Validate Exempt field
            if (string.IsNullOrEmpty(detail.Exempt) || (detail.Exempt != "Y" && detail.Exempt != "N"))
            {
                errors.Add("Exempt field must be Y or N");
            }

            // If not exempt, validate reviewers
            if (detail.Exempt != "Y")
            {
                // Check for circular dependencies
                if (detail.EmployeeId == detail.PrimaryReviewerId)
                {
                    errors.Add($"Reviewer cannot be same as Employee for ID {detail.EmployeeId}");
                }
                if (detail.EmployeeId == detail.SecondaryReviewerId)
                {
                    errors.Add($"Reviewer cannot be same as Employee for ID {detail.EmployeeId}");
                }

                // Validate primary reviewer
                if (string.IsNullOrEmpty(detail.PrimaryReviewerId))
                {
                    errors.Add($"Primary Reviewer is required for non-exempt employee {detail.EmployeeId}");
                }
                else if (!partnerDict.ContainsKey(detail.PrimaryReviewerId))
                {
                    errors.Add($"Invalid Primary Reviewer ID {detail.PrimaryReviewerId} for employee {detail.EmployeeId}");
                }
                else
                {
                    // Validate primary reviewer name matches partner record
                    var primaryReviewer = partnerDict[detail.PrimaryReviewerId];
                    if (!string.IsNullOrEmpty(detail.PrimaryReviewerName) && !IsNameMatch(detail.PrimaryReviewerName, primaryReviewer.FirstName, primaryReviewer.LastName, primaryReviewer.DisplayName))
                    {
                        errors.Add($"Primary Reviewer name '{detail.PrimaryReviewerName}' does not match partner record for Primary Reviewer ID {detail.PrimaryReviewerId}");
                    }
                }

                // Validate secondary reviewer
                if (string.IsNullOrEmpty(detail.SecondaryReviewerId))
                {
                    errors.Add($"Secondary Reviewer is required for non-exempt employee {detail.EmployeeId}");
                }
                else if (!partnerDict.ContainsKey(detail.SecondaryReviewerId))
                {
                    errors.Add($"Invalid Secondary Reviewer ID {detail.SecondaryReviewerId} for employee {detail.EmployeeId}");
                }
                else
                {
                    // Validate secondary reviewer name matches partner record
                    var secondaryReviewer = partnerDict[detail.SecondaryReviewerId];
                    if (!string.IsNullOrEmpty(detail.SecondaryReviewerName) && !IsNameMatch(detail.SecondaryReviewerName, secondaryReviewer.FirstName, secondaryReviewer.LastName, secondaryReviewer.DisplayName))
                    {
                        errors.Add($"Secondary Reviewer name '{detail.SecondaryReviewerName}' does not match partner record for Secondary Reviewer ID {detail.SecondaryReviewerId}");
                    }
                }
            }
            else
            {
                // If exempt, leadership role should be populated
                if (string.IsNullOrEmpty(detail.LeadershipRole))
                {
                    errors.Add($"Leadership Role missing for Exempt partner {detail.EmployeeId}");
                }
            }

            return errors;
        }

        /// <summary>
        /// Checks if the provided name matches any of the supported patterns from partner record
        /// </summary>
        /// <param name="providedName">The name from the upload file</param>
        /// <param name="firstName">First name from partner record</param>
        /// <param name="lastName">Last name from partner record</param>
        /// <param name="displayName">Display name from partner record (format: "LastName, FirstName")</param>
        /// <returns>True if the name matches any supported pattern</returns>
        private bool IsNameMatch(string providedName, string firstName, string lastName, string displayName)
        {
            if (string.IsNullOrWhiteSpace(providedName))
            {
                return false;
            }

            var cleanProvidedName = CleanNameForComparison(providedName);

            // Check against DisplayName first (if available)
            if (!string.IsNullOrWhiteSpace(displayName))
            {
                var cleanDisplayName = CleanNameForComparison(displayName);
                if (string.Equals(cleanProvidedName, cleanDisplayName, StringComparison.OrdinalIgnoreCase))
                {
                    return true;
                }
            }

            // Check against FirstName and LastName patterns (if available)
            if (!string.IsNullOrWhiteSpace(firstName) && !string.IsNullOrWhiteSpace(lastName))
            {
                var cleanFirstName = CleanNameForComparison(firstName);
                var cleanLastName = CleanNameForComparison(lastName);

                // Pattern 1: "FirstName LastName"
                var firstLastPattern = $"{cleanFirstName} {cleanLastName}";
                if (string.Equals(cleanProvidedName, firstLastPattern, StringComparison.OrdinalIgnoreCase))
                {
                    return true;
                }

                // Pattern 2: "LastName FirstName" (without comma)
                var lastFirstPattern = $"{cleanLastName} {cleanFirstName}";
                if (string.Equals(cleanProvidedName, lastFirstPattern, StringComparison.OrdinalIgnoreCase))
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// Cleans a name string by removing special characters and normalizing whitespace for comparison
        /// </summary>
        /// <param name="name">The name to clean</param>
        /// <returns>Cleaned name with only letters and single spaces</returns>
        private string CleanNameForComparison(string name)
        {
            if (string.IsNullOrWhiteSpace(name))
            {
                return string.Empty;
            }

            // Remove common special characters and replace with space
            var specialChars = new char[] { ',', '.', '/', '-', '_', '#', '@', '(', ')', '[', ']', '{', '}', '|', '\\', ':', ';', '"', '\'', '!', '?', '*', '&', '%', '$', '+', '=', '<', '>', '~', '`' };
            var cleaned = name;

            foreach (var specialChar in specialChars)
            {
                cleaned = cleaned.Replace(specialChar, ' ');
            }

            // Normalize whitespace: trim and replace multiple spaces with single space
            cleaned = System.Text.RegularExpressions.Regex.Replace(cleaned.Trim(), @"\s+", " ");

            return cleaned;
        }

        /// <summary>
        /// Submit the validated upload to the final PartnerReviewer table.
        /// When overwriteExisting = true (default), it will:
        /// 1. Update existing records that match the staging data
        /// 2. Create new records for partners in staging data that don't exist
        /// 3. Delete existing records for the same years that are NOT in the staging data
        /// </summary>
        /// <param name="uploadId">The upload ID to submit</param>
        /// <param name="overwriteExisting">Default value = true. When true, performs complete replacement of data for the specified years.</param>
        /// <returns>BusinessResult indicating success or failure</returns>
        public async Task<BusinessResult<bool>> SubmitUploadAsync(int uploadId, bool overwriteExisting = true)
        {
            var result = new BusinessResult<bool>();
            try
            {
                var upload = UOW.PartnerReviewerUploads.GetById(uploadId);
                if (upload == null)
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "Upload not found";
                    return result;
                }

                if (upload.Status != (byte)Enumerations.PartnerReviewerUploadStatus.ValidationPassed)
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "Upload must pass validation before submission";
                    return result;
                }

                // Get valid upload details
                var validDetails = UOW.PartnerReviewerUploadDetailses.Query(d => d.PartnerReviewerUploadId == uploadId && string.IsNullOrEmpty(d.ValidationError))
                    .ToList();

                if (!validDetails.Any())
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "No valid records to submit";
                    return result;
                }

                // Get partners dictionary
                var partners = UOW.Partners.Query(p => p.IsActive == true).ToList();
                var partnerDict = partners.ToDictionary(p => p.EmployeeId?.ToString(), p => p);

                // Parse years from the upload
                var yearsList = upload.Years.Split(',')
                    .Select(y => y.Trim())
                    .Where(y => short.TryParse(y, out _))
                    .Select(y => short.Parse(y))
                    .ToList();

                if (overwriteExisting)
                {
                    // When overwriting, first delete existing records for these years that are NOT in the staging data
                    var stagingPartnerIds = validDetails.Select(d => partnerDict[d.EmployeeId].Id).Distinct().ToList();

                    foreach (var year in yearsList)
                    {
                        // Find existing records for this year that are NOT in the staging data
                        var recordsToDelete = UOW.PartnerReviewers.GetAll()
                            .Where(pr => pr.Year == year && !stagingPartnerIds.Contains(pr.PartnerId))
                            .ToList();

                        // Delete records that are no longer in the upload
                        foreach (var recordToDelete in recordsToDelete)
                        {
                            UOW.PartnerReviewers.Delete(recordToDelete);
                        }
                    }
                }

                // Process each valid detail for each year
                foreach (var detail in validDetails)
                {
                    var partner = partnerDict[detail.EmployeeId];

                    foreach (var year in yearsList)
                    {
                        // Check if record already exists for this partner and year
                        var existingRecord = UOW.PartnerReviewers.GetAll()
                            .FirstOrDefault(pr => pr.PartnerId == partner.Id && pr.Year == year);

                        if (existingRecord != null)
                        {
                            if (overwriteExisting)
                            {
                                // Update existing record
                                UpdatePartnerReviewerFromDetail(existingRecord, detail, partnerDict);
                                existingRecord.ModifiedBy = CurrentUser?.Id;
                                existingRecord.ModifiedByName = CurrentUser?.Email;
                                existingRecord.ModifiedOn = CurrentDateTime;
                                UOW.PartnerReviewers.Update(existingRecord);
                            }
                            // Skip if not overwriting
                        }
                        else
                        {
                            // Create new record
                            var newRecord = CreatePartnerReviewerFromDetail(detail, partner, year, partnerDict);
                            UOW.PartnerReviewers.Add(newRecord);
                        }
                    }
                }

                // Update upload status
                upload.Status = (byte)Enumerations.PartnerReviewerUploadStatus.Submitted;
                upload.ModifiedBy = CurrentUser?.Id;
                upload.ModifiedByName = CurrentUser?.Email;
                upload.ModifiedOn = CurrentDateTime;
                UOW.PartnerReviewerUploads.Update(upload);

                UOW.Commit();

                result.Item = true;
                result.ResultStatus = ResultStatus.Success;
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
                Logger.LogError(ex, $"Error submitting upload: {uploadId}");
            }
            return result;
        }

        private void UpdatePartnerReviewerFromDetail(Entity.PartnerReviewer partnerReviewer,
            Entity.PartnerReviewerUploadDetails detail, Dictionary<string, Entity.Partner> partnerDict)
        {
            partnerReviewer.Exempt = detail.Exempt == "Y";
            partnerReviewer.LeadershipRole = detail.LeadershipRole;

            //
            // Note: Doesn't matter if exempt, we still set reviewers if there is data in the detail
            //
            if (!string.IsNullOrEmpty(detail.PrimaryReviewerId) && partnerDict.ContainsKey(detail.PrimaryReviewerId))
            {
                partnerReviewer.PrimaryReviewerId = partnerDict[detail.PrimaryReviewerId].Id;
                partnerReviewer.PrimaryReviewerName = partnerDict[detail.PrimaryReviewerId].DisplayName;
            }

            if (!string.IsNullOrEmpty(detail.SecondaryReviewerId) && partnerDict.ContainsKey(detail.SecondaryReviewerId))
            {
                partnerReviewer.SecondaryReviewerId = partnerDict[detail.SecondaryReviewerId].Id;
                partnerReviewer.SecondaryReviewerName = partnerDict[detail.SecondaryReviewerId].DisplayName;
            }

        }

        private Entity.PartnerReviewer CreatePartnerReviewerFromDetail(Entity.PartnerReviewerUploadDetails detail,
            Entity.Partner partner, short year, Dictionary<string, Entity.Partner> partnerDict)
        {
            var partnerReviewer = new Entity.PartnerReviewer
            {
                Id = Guid.NewGuid(),
                Year = year,
                PartnerId = partner.Id,
                Exempt = detail.Exempt == "Y",
                LeadershipRole = detail.LeadershipRole,
                CreatedBy = CurrentUser?.Id,
                CreatedByName = CurrentUser?.Email,
                CreatedOn = CurrentDateTime
            };

            if (!string.IsNullOrEmpty(detail.PrimaryReviewerId) && partnerDict.ContainsKey(detail.PrimaryReviewerId))
            {
                partnerReviewer.PrimaryReviewerId = partnerDict[detail.PrimaryReviewerId].Id;
                partnerReviewer.PrimaryReviewerName = partnerDict[detail.PrimaryReviewerId].DisplayName;
            }

            if (!string.IsNullOrEmpty(detail.SecondaryReviewerId) && partnerDict.ContainsKey(detail.SecondaryReviewerId))
            {
                partnerReviewer.SecondaryReviewerId = partnerDict[detail.SecondaryReviewerId].Id;
                partnerReviewer.SecondaryReviewerName = partnerDict[detail.SecondaryReviewerId].DisplayName;
            }

            return partnerReviewer;
        }



        public BusinessResult<ICollection<PartnerReviewer>> GetPartnerReviewersByYear(short year)
        {
            var result = new BusinessResult<ICollection<PartnerReviewer>>();
            try
            {
                var partnerReviewers = UOW.PartnerReviewers.Query(pr => pr.Year == year)
                    .Include(pr => pr.Partner)
                    .Include(pr => pr.PrimaryReviewer)
                    .Include(pr => pr.SecondaryReviewer)
                    .OrderBy(pr=> pr.Year).ThenBy(pr => pr.Partner.EmployeeId)
                    .ToList();
                result.Item = _mapper.Map<ICollection<PartnerReviewer>>(partnerReviewers);
                result.ResultStatus = ResultStatus.Success;
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
                Logger.LogError(ex, $"Error getting partner reviewers for year: {year}");
            }
            return result;
        }

        public BusinessResult<PartnerReviewer> GetPartnerReviewerByPartnerAndYear(Guid partnerId, short year)
        {
            var result = new BusinessResult<PartnerReviewer>();
            try
            {
                var partnerReviewer = UOW.PartnerReviewers.GetAll()
                    .FirstOrDefault(pr => pr.PartnerId == partnerId && pr.Year == year);

                if (partnerReviewer != null)
                {
                    result.Item = _mapper.Map<PartnerReviewer>(partnerReviewer);
                    result.ResultStatus = ResultStatus.Success;
                }
                else
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "Partner reviewer assignment not found";
                }
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
                Logger.LogError(ex, $"Error getting partner reviewer for partner: {partnerId}, year: {year}");
            }
            return result;
        }

        public BusinessResult<PartnerReviewer> UpdatePartnerReviewer(PartnerReviewer partnerReviewer)
        {
            var result = new BusinessResult<PartnerReviewer>();
            try
            {
                var entity = UOW.PartnerReviewers.GetById(partnerReviewer.Id);
                if (entity == null)
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "Partner reviewer not found";
                    return result;
                }

                // Update entity properties
                entity.Exempt = partnerReviewer.Exempt;
                entity.LeadershipRole = partnerReviewer.LeadershipRole;
                entity.PrimaryReviewerId = partnerReviewer.PrimaryReviewerId;
                entity.PrimaryReviewerName = partnerReviewer.PrimaryReviewerName;
                entity.SecondaryReviewerId = partnerReviewer.SecondaryReviewerId;
                entity.SecondaryReviewerName = partnerReviewer.SecondaryReviewerName;
                entity.ModifiedBy = CurrentUser?.Id;
                entity.ModifiedByName = CurrentUser?.Email;
                entity.ModifiedOn = CurrentDateTime;

                UOW.PartnerReviewers.Update(entity);

                if (UOW.Commit() > 0)
                {
                    result.Item = _mapper.Map<PartnerReviewer>(entity);
                    result.ResultStatus = ResultStatus.Success;
                }
                else
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "Failed to update partner reviewer";
                }
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
                Logger.LogError(ex, $"Error updating partner reviewer: {partnerReviewer.Id}");
            }
            return result;
        }

        public BusinessResult<bool> DeletePartnerReviewer(Guid id)
        {
            var result = new BusinessResult<bool>();
            try
            {
                var entity = UOW.PartnerReviewers.GetById(id);
                if (entity == null)
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "Partner reviewer not found";
                    return result;
                }

                UOW.PartnerReviewers.Delete(entity);

                if (UOW.Commit() > 0)
                {
                    result.Item = true;
                    result.ResultStatus = ResultStatus.Success;
                }
                else
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "Failed to delete partner reviewer";
                }
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
                Logger.LogError(ex, $"Error deleting partner reviewer: {id}");
            }
            return result;
        }

        public BusinessResult<bool> DeletePartnerReviewerUpload(int uploadId)
        {
            var result = new BusinessResult<bool>();
            try
            {
                // First check if upload exists
                var upload = UOW.PartnerReviewerUploads.GetById(uploadId);
                if (upload == null)
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "Upload not found";
                    return result;
                }

                // Check if upload can be deleted (only allow deletion of failed validation uploads)
                if (upload.Status != (byte)Enumerations.PartnerReviewerUploadStatus.ValidationFailed)
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "Only uploads with validation failed status can be deleted";
                    return result;
                }

                // Delete all upload details first (due to foreign key constraint)
                var uploadDetails = UOW.PartnerReviewerUploadDetailses.Query(d => d.PartnerReviewerUploadId == uploadId).ToList();
                foreach (var detail in uploadDetails)
                {
                    UOW.PartnerReviewerUploadDetailses.Delete(detail);
                }

                // Delete the upload record
                UOW.PartnerReviewerUploads.Delete(upload);

                if (UOW.Commit() > 0)
                {
                    result.Item = true;
                    result.ResultStatus = ResultStatus.Success;
                    result.Message = "Upload and all related details deleted successfully";
                }
                else
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "Failed to delete upload";
                }
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
                Logger.LogError(ex, $"Error deleting partner reviewer upload: {uploadId}");
            }
            return result;
        }

        public BusinessResult<byte[]> GetUploadTemplate()
        {
            var result = new BusinessResult<byte[]>();
            try
            {
                using (var package = new ExcelPackage())
                {
                    var worksheet = package.Workbook.Worksheets.Add("Partner Reviewer Template");

                    // Add headers
                    worksheet.Cells[1, 1].Value = "EmployeeID";
                    worksheet.Cells[1, 2].Value = "EmployeeName";
                    worksheet.Cells[1, 3].Value = "Exempt";
                    worksheet.Cells[1, 4].Value = "LeadershipRole";
                    worksheet.Cells[1, 5].Value = "PrimaryReviewerID";
                    worksheet.Cells[1, 6].Value = "PrimaryReviewerName";
                    worksheet.Cells[1, 7].Value = "SecondaryReviewerID";
                    worksheet.Cells[1, 8].Value = "SecondaryReviewerName";

                    // Add sample data
                    worksheet.Cells[2, 1].Value = "62000";
                    worksheet.Cells[2, 2].Value = "Partner Name";
                    worksheet.Cells[2, 3].Value = "Y";
                    worksheet.Cells[2, 4].Value = "SLT, SSLL";
                    worksheet.Cells[2, 5].Value = "50000";
                    worksheet.Cells[2, 6].Value = "Reviewer Name";
                    worksheet.Cells[2, 7].Value = "900";
                    worksheet.Cells[2, 8].Value = "Reviewer2 Name";

                    // Format headers
                    using (var range = worksheet.Cells[1, 1, 1, 8])
                    {
                        range.Style.Font.Bold = true;
                        range.Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                        range.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
                    }

                    worksheet.Cells.AutoFitColumns();

                    result.Item = package.GetAsByteArray();
                    result.ResultStatus = ResultStatus.Success;
                }
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
                Logger.LogError(ex, "Error generating upload template");
            }
            return result;
        }

        public BusinessResult<byte[]> ExportPartnerReviewersToExcel(short year)
        {
            var result = new BusinessResult<byte[]>();
            try
            {
                var partnerReviewers = UOW.PartnerReviewers.GetAll()
                    .Where(pr => pr.Year == year)
                    .Include(pr => pr.Partner)
                    .Include(pr => pr.PrimaryReviewer)
                    .Include(pr => pr.SecondaryReviewer)
                    .ToList();

                using (var package = new ExcelPackage())
                {
                    var worksheet = package.Workbook.Worksheets.Add($"Partner Reviewers {year}");

                    // Add headers - matching template column names
                    worksheet.Cells[1, 1].Value = "EmployeeID";
                    worksheet.Cells[1, 2].Value = "EmployeeName";
                    worksheet.Cells[1, 3].Value = "Exempt";
                    worksheet.Cells[1, 4].Value = "LeadershipRole";
                    worksheet.Cells[1, 5].Value = "PrimaryReviewerID";
                    worksheet.Cells[1, 6].Value = "PrimaryReviewerName";
                    worksheet.Cells[1, 7].Value = "SecondaryReviewerID";
                    worksheet.Cells[1, 8].Value = "SecondaryReviewerName";

                    // Add data
                    for (int i = 0; i < partnerReviewers.Count; i++)
                    {
                        var pr = partnerReviewers[i];
                        var row = i + 2;

                        worksheet.Cells[row, 1].Value = pr.Partner?.EmployeeId?.ToString();
                        worksheet.Cells[row, 2].Value = pr.Partner?.DisplayName;
                        worksheet.Cells[row, 3].Value = pr.Exempt ? "Y" : "N";
                        worksheet.Cells[row, 4].Value = pr.LeadershipRole;
                        worksheet.Cells[row, 5].Value = pr.PrimaryReviewer?.EmployeeId?.ToString();
                        worksheet.Cells[row, 6].Value = pr.PrimaryReviewerName;
                        worksheet.Cells[row, 7].Value = pr.SecondaryReviewer?.EmployeeId?.ToString();
                        worksheet.Cells[row, 8].Value = pr.SecondaryReviewerName;
                    }

                    // Format headers
                    using (var range = worksheet.Cells[1, 1, 1, 8])
                    {
                        range.Style.Font.Bold = true;
                        range.Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                        range.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
                    }

                    worksheet.Cells.AutoFitColumns();

                    result.Item = package.GetAsByteArray();
                    result.ResultStatus = ResultStatus.Success;
                }
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
                Logger.LogError(ex, $"Error exporting partner reviewers for year: {year}");
            }
            return result;
        }
    }
}
