using AutoMapper;
using BdoPartner.Plans.Business.Interface;
using BdoPartner.Plans.Common;
using BdoPartner.Plans.Common.Config;
using BdoPartner.Plans.DataAccess;
using BdoPartner.Plans.Model.DTO;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using Entity = BdoPartner.Plans.Model.Entity;

namespace BdoPartner.Plans.Business
{
    /// <summary>
    /// Business service implementation for Partner entity operations
    /// </summary>
    public class PartnerService : BaseService, IPartnerService
    {
        private readonly IMapper _mapper;

        public PartnerService(IUnitOfWork uow, IConfigSettings config, ILogger<BaseService> logger, 
            IHttpContextAccessor httpContextAccessor, IMapper mapper) 
            : base(uow, httpContextAccessor, config, logger)
        {
            _mapper = mapper;
        }

        public BusinessResult<ICollection<Partner>> GetPartners()
        {
            var result = new BusinessResult<ICollection<Partner>>();
            try
            {
                var partners = UOW.Partners.GetAll().Where(p => p.IsActive == true).ToList();
                result.Item = _mapper.Map<ICollection<Partner>>(partners);
                result.ResultStatus = ResultStatus.Success;
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
            }
            return result;
        }

        public BusinessResult<ICollection<Partner>> SearchPartners(string searchTerm = null,
            string partnerType = null, string department = null, string location = null,
            bool? isActive = null, int pageNumber = 1, int pageSize = 50)
        {
            var result = new BusinessResult<ICollection<Partner>>();
            try
            {
                var query = UOW.Partners.GetAll();

                if (!string.IsNullOrEmpty(searchTerm))
                {
                    query = query.Where(p => p.FirstName.Contains(searchTerm) || p.LastName.Contains(searchTerm) ||
                                           p.DisplayName.Contains(searchTerm) || p.Mail.Contains(searchTerm));
                }

                // GroupId filter removed as GroupId column no longer exists in Partner table

                if (!string.IsNullOrEmpty(partnerType))
                    query = query.Where(p => p.PartnerType == partnerType);

                if (!string.IsNullOrEmpty(department))
                    query = query.Where(p => p.Department == department);

                if (!string.IsNullOrEmpty(location))
                    query = query.Where(p => p.Location == location);

                if (isActive.HasValue)
                    query = query.Where(p => p.IsActive == isActive.Value);

                var partners = query.Skip((pageNumber - 1) * pageSize).Take(pageSize).ToList();
                result.Item = _mapper.Map<ICollection<Partner>>(partners);
                result.ResultStatus = ResultStatus.Success;
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
            }
            return result;
        }

        public BusinessResult<Partner> GetPartnerById(Guid id)
        {
            var result = new BusinessResult<Partner>();
            try
            {
                var partner = UOW.Partners.FirstOrDefault(p => p.Id == id);
                if (partner != null)
                {
                    result.Item = _mapper.Map<Partner>(partner);
                    result.ResultStatus = ResultStatus.Success;
                }
                else
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "Partner not found";
                }
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
            }
            return result;
        }

        public BusinessResult<Partner> GetPartnerByEmployeeId(int employeeId)
        {
            var result = new BusinessResult<Partner>();
            try
            {
                var partner = UOW.Partners.FirstOrDefault(p => p.EmployeeId == employeeId);
                if (partner != null)
                {
                    result.Item = _mapper.Map<Partner>(partner);
                    result.ResultStatus = ResultStatus.Success;
                }
                else
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "Partner not found";
                }
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
            }
            return result;
        }

        public BusinessResult<Partner> CreatePartner(Partner partner)
        {
            var result = new BusinessResult<Partner>();
            try
            {
                var entity = _mapper.Map<Entity.Partner>(partner);
                entity.CreatedBy = CurrentUser?.Id;
                entity.CreatedByName = CurrentUser?.Email;
                entity.CreatedOn = CurrentDateTime;
                entity.IsActive = true;

                UOW.Partners.Add(entity);

                if (UOW.Commit() > 0)
                {
                    result.Item = _mapper.Map<Partner>(entity);
                    result.ResultStatus = ResultStatus.Success;
                }
                else
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "Failed to create partner";
                }
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
            }
            return result;
        }

        public BusinessResult<Partner> UpdatePartner(Partner partner)
        {
            var result = new BusinessResult<Partner>();
            try
            {
                var existingEntity = UOW.Partners.FirstOrDefault(p => p.Id == partner.Id);
                if (existingEntity != null)
                {
                    _mapper.Map(partner, existingEntity);
                    existingEntity.ModifiedBy = CurrentUser?.Id;
                    existingEntity.ModifiedOn = CurrentDateTime;

                    UOW.Partners.Update(existingEntity);

                    if (UOW.Commit() > 0)
                    {
                        result.Item = _mapper.Map<Partner>(existingEntity);
                        result.ResultStatus = ResultStatus.Success;
                    }
                    else
                    {
                        result.ResultStatus = ResultStatus.Failure;
                        result.Message = "Failed to update partner";
                    }
                }
                else
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "Partner not found";
                }
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
            }
            return result;
        }

        public BusinessResult<bool> DeletePartner(Guid id)
        {
            var result = new BusinessResult<bool>();
            try
            {
                var entity = UOW.Partners.FirstOrDefault(p => p.Id == id);
                if (entity != null)
                {
                    entity.IsActive = false;
                    entity.ModifiedBy = CurrentUser?.Id;
                    entity.ModifiedOn = CurrentDateTime;

                    UOW.Partners.Update(entity);

                    if (UOW.Commit() > 0)
                    {
                        result.Item = true;
                        result.ResultStatus = ResultStatus.Success;
                    }
                    else
                    {
                        result.ResultStatus = ResultStatus.Failure;
                        result.Message = "Failed to delete partner";
                    }
                }
                else
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "Partner not found";
                }
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
            }
            return result;
        }

        public BusinessResult<ICollection<Lookup>> GetPartnersLookup(bool includeInactive = false)
        {
            var result = new BusinessResult<ICollection<Lookup>>();
            try
            {
                var query = UOW.Partners.GetAll();

                if (!includeInactive)
                {
                    query = query.Where(p => p.IsActive == true);
                }

                var lookupItems = query.Select(p => new Lookup
                {
                    Key = p.Id.ToString(),
                    Value = p.DisplayName ?? $"{p.FirstName} {p.LastName}"
                }).ToList();

                result.Item = lookupItems;
                result.ResultStatus = ResultStatus.Success;
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
            }
            return result;
        }
    }
}
