using AutoMapper;
using BdoPartner.Plans.Business.Interface;
using BdoPartner.Plans.Common;
using BdoPartner.Plans.Common.Config;
using BdoPartner.Plans.Common.Helpers;
using BdoPartner.Plans.DataAccess;
using BdoPartner.Plans.DataAccess.Common.PagedList;
using BdoPartner.Plans.Model.DTO;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using Entity = BdoPartner.Plans.Model.Entity;

namespace BdoPartner.Plans.Business
{
    /// <summary>
    /// Business service implementation for Questionnaire entity operations
    /// </summary>
    public class QuestionnaireService : BaseService, IQuestionnaireService
    {
        private readonly IMapper _mapper;

        public QuestionnaireService(IUnitOfWork uow, IConfigSettings config, ILogger<BaseService> logger,
            IHttpContextAccessor httpContextAccessor, IMapper mapper)
            : base(uow, httpContextAccessor, config, logger)
        {
            _mapper = mapper;
        }

        /// <summary>
        /// Helper method to decompress JSON fields in questionnaire DTOs
        /// </summary>
        /// <param name="questionnaire">The questionnaire DTO to process</param>
        private void DecompressQuestionnaireJsonFields(Questionnaire questionnaire)
        {
            if (questionnaire == null) return;

            try
            {
                // Decompress DefinitionJson if it exists and appears to be compressed
                if (!string.IsNullOrEmpty(questionnaire.DefinitionJson))
                {
                    questionnaire.DefinitionJson = CompressionHelper.SafeDecompressJson(questionnaire.DefinitionJson);
                }

                // Decompress DraftDefinitionJson if it exists and appears to be compressed
                if (!string.IsNullOrEmpty(questionnaire.DraftDefinitionJson))
                {
                    questionnaire.DraftDefinitionJson = CompressionHelper.SafeDecompressJson(questionnaire.DraftDefinitionJson);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, $"Error decompressing JSON fields for questionnaire {questionnaire.Id}");
                // Continue execution - don't fail the entire operation due to decompression issues
            }
        }

        /// <summary>
        /// Helper method to decompress JSON fields for a collection of questionnaires
        /// </summary>
        /// <param name="questionnaires">The collection of questionnaire DTOs to process</param>
        private void DecompressQuestionnaireJsonFields(ICollection<Questionnaire> questionnaires)
        {
            if (questionnaires == null) return;

            foreach (var questionnaire in questionnaires)
            {
                DecompressQuestionnaireJsonFields(questionnaire);
            }
        }

        /// <summary>
        /// Helper method to compress JSON fields in questionnaire DTOs before saving to database
        /// </summary>
        /// <param name="questionnaire">The questionnaire DTO to process</param>
        private void CompressQuestionnaireJsonFields(Questionnaire questionnaire)
        {
            if (questionnaire == null) return;

            try
            {
                // Compress DefinitionJson if it exists and is not already compressed
                if (!string.IsNullOrEmpty(questionnaire.DefinitionJson))
                {
                    // Only compress if it's not already base64 encoded (i.e., it's raw JSON)
                    if (!CompressionHelper.IsBase64Encoded(questionnaire.DefinitionJson))
                    {
                        questionnaire.DefinitionJson = CompressionHelper.CompressAndEncode(questionnaire.DefinitionJson);
                    }
                }

                // Compress DraftDefinitionJson if it exists and is not already compressed
                if (!string.IsNullOrEmpty(questionnaire.DraftDefinitionJson))
                {
                    // Only compress if it's not already base64 encoded (i.e., it's raw JSON)
                    if (!CompressionHelper.IsBase64Encoded(questionnaire.DraftDefinitionJson))
                    {
                        questionnaire.DraftDefinitionJson = CompressionHelper.CompressAndEncode(questionnaire.DraftDefinitionJson);
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, $"Error compressing JSON fields for questionnaire {questionnaire.Id}");
                // Continue execution - don't fail the entire operation due to compression issues
            }
        }

        public BusinessResult<ICollection<Questionnaire>> GetQuestionnaires()
        {
            var result = new BusinessResult<ICollection<Questionnaire>>();
            try
            {
                var questionnaires = UOW.Questionnaires.GetAll().Where(q => q.IsActive == true).ToList();
                result.Item = _mapper.Map<ICollection<Questionnaire>>(questionnaires);

                // Decompress JSON fields before returning
                DecompressQuestionnaireJsonFields(result.Item);

                result.ResultStatus = ResultStatus.Success;
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
            }
            return result;
        }

        /// <summary>
        ///  Searches for questionnaires based on various filters and returns a paginated list.
        ///  Uses QuestionnaireListItem DTO to skip heavy JSON fields for better performance.
        /// </summary>
        /// <param name="searchTerm"></param>
        /// <param name="year"></param>
        /// <param name="status"></param>
        /// <param name="isActive"></param>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        public BusinessResult<IPagedList<QuestionnaireListItem>> SearchQuestionnaires(string searchTerm = null, short? year = null,
            byte? status = null, bool? isActive = null, int pageIndex = 0, int pageSize = 20)
        {
            var result = new BusinessResult<IPagedList<QuestionnaireListItem>>();
            try
            {
                var query = UOW.Questionnaires.GetAll().AsQueryable();

                if (!string.IsNullOrEmpty(searchTerm))
                {
                    query = query.Where(q => q.Name.Contains(searchTerm));
                }

                if (year.HasValue)
                    query = query.Where(q => q.Year == year.Value);

                if (status.HasValue)
                    query = query.Where(q => q.Status == status.Value);

                if (isActive.HasValue)
                    query = query.Where(q => q.IsActive == isActive.Value);

                // Apply ordering and use PagedList for better performance
                var orderedQuery = query.OrderByDescending(q => q.ModifiedOn ?? q.CreatedOn);

                // Use the PagedList extension with AutoMapper integration for the simple DTO
                var pagedQuestionnaires = orderedQuery.ToPagedList<QuestionnaireListItem, Entity.Questionnaire>(
                    _mapper, pageIndex, pageSize);

                result.Item = pagedQuestionnaires;
                result.ResultStatus = ResultStatus.Success;
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
                Logger.LogError(ex, "Error searching questionnaires");
            }
            return result;
        }

        public BusinessResult<Questionnaire> GetQuestionnaireById(Guid id)
        {
            var result = new BusinessResult<Questionnaire>();
            try
            {
                var questionnaire = UOW.Questionnaires.GetById(id);
                if (questionnaire != null)
                {
                    result.Item = _mapper.Map<Questionnaire>(questionnaire);

                    // Decompress JSON fields before returning
                    DecompressQuestionnaireJsonFields(result.Item);

                    result.ResultStatus = ResultStatus.Success;
                }
                else
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "Questionnaire not found";
                }
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
            }
            return result;
        }

        public BusinessResult<ICollection<Questionnaire>> GetQuestionnairesByYear(short year)
        {
            var result = new BusinessResult<ICollection<Questionnaire>>();
            try
            {
                var questionnaires = UOW.Questionnaires.GetAll()
                    .Where(q => q.Year == year && q.IsActive == true).ToList();
                result.Item = _mapper.Map<ICollection<Questionnaire>>(questionnaires);

                // Decompress JSON fields before returning
                DecompressQuestionnaireJsonFields(result.Item);

                result.ResultStatus = ResultStatus.Success;
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
            }
            return result;
        }

        public BusinessResult<Questionnaire> CreateQuestionnaire(Questionnaire questionnaire)
        {
            var result = new BusinessResult<Questionnaire>();
            try
            {
                // Create a copy of the questionnaire to avoid modifying the original input
                var questionnaireToSave = new Questionnaire
                {
                    Id = questionnaire.Id,
                    Name = questionnaire.Name,
                    Year = questionnaire.Year,
                    DefinitionJson = questionnaire.DefinitionJson,
                    DraftDefinitionJson = questionnaire.DraftDefinitionJson,
                    FormSystemVersion = questionnaire.FormSystemVersion,
                    Acknowledgement = questionnaire.Acknowledgement,
                    AcknowledgementText = questionnaire.AcknowledgementText,
                    GeneralComments = questionnaire.GeneralComments,
                    GeneralCommentsText = questionnaire.GeneralCommentsText,
                    Status = questionnaire.Status,
                    IsActive = questionnaire.IsActive,
                    CreatedBy = questionnaire.CreatedBy,
                    CreatedOn = questionnaire.CreatedOn,
                    ModifiedBy = questionnaire.ModifiedBy,
                    ModifiedOn = questionnaire.ModifiedOn
                };

                // Compress JSON fields before saving to database
                CompressQuestionnaireJsonFields(questionnaireToSave);

                var entity = _mapper.Map<Entity.Questionnaire>(questionnaireToSave);
                entity.Id = Guid.NewGuid();
                entity.CreatedBy = CurrentUser?.Id;
                entity.CreatedByName = CurrentUser?.Email;
                entity.CreatedOn = CurrentDateTime;
                entity.IsActive = true;

                UOW.Questionnaires.Add(entity);

                // Commit with audit enabled to track questionnaire creation
                var commitResult = UOW.Commit(enableAuditing: true, logonUser: CurrentUser?.Email ?? CurrentUser?.Id.ToString() ?? "System");
                if (commitResult > 0)
                {
                    result.Item = _mapper.Map<Questionnaire>(entity);

                    // Decompress JSON fields before returning
                    DecompressQuestionnaireJsonFields(result.Item);

                    result.ResultStatus = ResultStatus.Success;

                    // Log the creation action for audit purposes
                    Logger?.LogInformation($"Questionnaire '{entity.Name}' (ID: {entity.Id}) was created by user {CurrentUser?.Email ?? "System"} at {CurrentDateTime}");
                }
                else
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "Failed to create questionnaire";
                    Logger?.LogError($"Failed to create questionnaire '{entity.Name}' by user {CurrentUser?.Email ?? "System"}");
                }
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
            }
            return result;
        }

        public BusinessResult<Questionnaire> UpdateQuestionnaire(Questionnaire questionnaire)
        {
            var result = new BusinessResult<Questionnaire>();
            try
            {
                var existingEntity = UOW.Questionnaires.GetById(questionnaire.Id);
                if (existingEntity != null)
                {
                    // Create a copy of the questionnaire to avoid modifying the original input
                    var questionnaireToSave = new Questionnaire
                    {
                        Id = questionnaire.Id,
                        Name = questionnaire.Name,
                        Year = questionnaire.Year,
                        DefinitionJson = questionnaire.DefinitionJson,
                        DraftDefinitionJson = questionnaire.DraftDefinitionJson,
                        FormSystemVersion = questionnaire.FormSystemVersion,
                        Acknowledgement = questionnaire.Acknowledgement,
                        AcknowledgementText = questionnaire.AcknowledgementText,
                        GeneralComments = questionnaire.GeneralComments,
                        GeneralCommentsText = questionnaire.GeneralCommentsText,
                        Status = questionnaire.Status,
                        IsActive = questionnaire.IsActive,
                        CreatedBy = questionnaire.CreatedBy,
                        CreatedOn = questionnaire.CreatedOn,
                        ModifiedBy = questionnaire.ModifiedBy,
                        ModifiedOn = questionnaire.ModifiedOn
                    };

                    // Compress JSON fields before saving to database
                    CompressQuestionnaireJsonFields(questionnaireToSave);

                    _mapper.Map(questionnaireToSave, existingEntity);
                    existingEntity.ModifiedBy = CurrentUser?.Id;
                    existingEntity.ModifiedOn = CurrentDateTime;

                    UOW.Questionnaires.Update(existingEntity);

                    // Commit with audit enabled to track questionnaire updates
                    var commitResult = UOW.Commit(enableAuditing: true, logonUser: CurrentUser?.Email ?? CurrentUser?.Id.ToString() ?? "System");
                    if (commitResult > 0)
                    {
                        result.Item = _mapper.Map<Questionnaire>(existingEntity);

                        // Decompress JSON fields before returning
                        DecompressQuestionnaireJsonFields(result.Item);

                        result.ResultStatus = ResultStatus.Success;

                        // Log the update action for audit purposes
                        Logger?.LogInformation($"Questionnaire '{existingEntity.Name}' (ID: {existingEntity.Id}) was updated by user {CurrentUser?.Email ?? "System"} at {CurrentDateTime}");
                    }
                    else
                    {
                        result.ResultStatus = ResultStatus.Failure;
                        result.Message = "Failed to update questionnaire";
                        Logger?.LogError($"Failed to update questionnaire '{existingEntity.Name}' (ID: {existingEntity.Id}) by user {CurrentUser?.Email ?? "System"}");
                    }
                }
                else
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "Questionnaire not found";
                }
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
            }
            return result;
        }

        /// <summary>
        /// Check if a questionnaire can be deleted (not published and no forms reference it)
        /// </summary>
        /// <param name="id">Questionnaire ID</param>
        /// <returns>Validation result with details about why deletion is not allowed</returns>
        public BusinessResult<QuestionnaireDeleteValidation> ValidateQuestionnaireForDeletion(Guid id)
        {
            var result = new BusinessResult<QuestionnaireDeleteValidation>();
            try
            {
                var validation = new QuestionnaireDeleteValidation
                {
                    CanDelete = true,
                    IsPublished = false,
                    HasFormReferences = false,
                    FormReferenceCount = 0
                };

                var entity = UOW.Questionnaires.GetById(id);
                if (entity == null)
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "Questionnaire not found";
                    return result;
                }

                // Check if questionnaire is published
                if (entity.Status == (byte)Enumerations.QuestionnaireStatus.Published)
                {
                    validation.IsPublished = true;
                    validation.CanDelete = false;
                }

                // Check if any forms reference this questionnaire
                var formCount = UOW.Forms.GetAll()
                    .Where(f => f.QuestionnaireId == id && f.IsActive == true)
                    .Count();

                if (formCount > 0)
                {
                    validation.HasFormReferences = true;
                    validation.FormReferenceCount = formCount;
                    validation.CanDelete = false;
                }

                result.Item = validation;
                result.ResultStatus = ResultStatus.Success;
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
            }
            return result;
        }

        public BusinessResult<bool> DeleteQuestionnaire(Guid id)
        {
            var result = new BusinessResult<bool>();
            try
            {
                // First validate if the questionnaire can be deleted
                var validationResult = ValidateQuestionnaireForDeletion(id);
                if (validationResult.ResultStatus != ResultStatus.Success)
                {
                    result.ResultStatus = validationResult.ResultStatus;
                    result.Message = validationResult.Message;
                    return result;
                }

                var validation = validationResult.Item;
                if (!validation.CanDelete)
                {
                    result.ResultStatus = ResultStatus.Failure;

                    if (validation.IsPublished)
                    {
                        result.Message = "Cannot delete a published questionnaire. Please archive it first if you want to disable it.";
                    }
                    else if (validation.HasFormReferences)
                    {
                        result.Message = $"Cannot delete questionnaire because it is referenced by {validation.FormReferenceCount} form(s). Please remove or reassign these forms first.";
                    }

                    return result;
                }

                var entity = UOW.Questionnaires.GetById(id);
                if (entity != null)
                {
                    entity.IsActive = false;
                    entity.ModifiedBy = CurrentUser?.Id;
                    entity.ModifiedOn = CurrentDateTime;

                    UOW.Questionnaires.Update(entity);

                    // Commit with audit enabled to track questionnaire deletion (soft delete)
                    var commitResult = UOW.Commit(enableAuditing: true, logonUser: CurrentUser?.Email ?? CurrentUser?.Id.ToString() ?? "System");
                    if (commitResult > 0)
                    {
                        result.Item = true;
                        result.ResultStatus = ResultStatus.Success;

                        // Log the deletion action for audit purposes
                        Logger?.LogInformation($"Questionnaire '{entity.Name}' (ID: {entity.Id}) was deleted (soft delete) by user {CurrentUser?.Email ?? "System"} at {CurrentDateTime}");
                    }
                    else
                    {
                        result.ResultStatus = ResultStatus.Failure;
                        result.Message = "Failed to delete questionnaire";
                        Logger?.LogError($"Failed to delete questionnaire '{entity.Name}' (ID: {entity.Id}) by user {CurrentUser?.Email ?? "System"}");
                    }
                }
                else
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "Questionnaire not found";
                }
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
            }
            return result;
        }

        public BusinessResult<ICollection<Lookup>> GetQuestionnairesLookup(bool includeInactive = false)
        {
            var result = new BusinessResult<ICollection<Lookup>>();
            try
            {
                var query = UOW.Questionnaires.GetAll();
                
                if (!includeInactive)
                {
                    query = query.Where(q => q.IsActive == true);
                }

                var lookupItems = query.Select(q => new Lookup
                {
                    Key = q.Id.ToString(),
                    Value = $"{q.Name} ({q.Year})"
                }).ToList();

                result.Item = lookupItems;
                result.ResultStatus = ResultStatus.Success;
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
            }
            return result;
        }

        public BusinessResult<bool> HasPublishedQuestionnaireInYear(short year, Guid? excludeId = null)
        {
            var result = new BusinessResult<bool>();
            try
            {
                var query = UOW.Questionnaires.GetAll()
                    .Where(q => q.Year == year &&
                               q.Status == (byte)Enumerations.QuestionnaireStatus.Published &&
                               q.IsActive == true);

                // Exclude the current questionnaire if provided (for update scenarios)
                if (excludeId.HasValue)
                {
                    query = query.Where(q => q.Id != excludeId.Value);
                }

                result.Item = query.Any();
                result.ResultStatus = ResultStatus.Success;
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
            }
            return result;
        }

        public BusinessResult<Questionnaire> PublishQuestionnaire(Guid id)
        {
            var result = new BusinessResult<Questionnaire>();
            try
            {
                var entity = UOW.Questionnaires.GetById(id);
                if (entity != null)
                {
                    // Check if there's already a different published questionnaire in the same year
                    // Allow republishing the same questionnaire (same ID)
                    var hasPublishedInYear = HasPublishedQuestionnaireInYear(entity.Year, id);
                    if (hasPublishedInYear.ResultStatus == ResultStatus.Success && hasPublishedInYear.Item)
                    {
                        result.ResultStatus = ResultStatus.Failure;
                        result.Message = $"{entity.Year} year already has published Partner Plan, you cannot publish new Partner Plan in same year.";
                        return result;
                    }

                    // If republishing the same questionnaire, copy DraftDefinitionJson to DefinitionJson
                    if (entity.Status == (byte)Enumerations.QuestionnaireStatus.Published)
                    {
                        // This is a republish of the same questionnaire
                        if (!string.IsNullOrEmpty(entity.DraftDefinitionJson))
                        {
                            entity.DefinitionJson = entity.DraftDefinitionJson;
                        }
                    }
                    else
                    {
                        // First time publishing - copy DraftDefinitionJson to DefinitionJson
                        if (!string.IsNullOrEmpty(entity.DraftDefinitionJson))
                        {
                            entity.DefinitionJson = entity.DraftDefinitionJson;
                        }
                    }

                    entity.Status = (byte)Enumerations.QuestionnaireStatus.Published;
                    entity.ModifiedBy = CurrentUser?.Id;
                    entity.ModifiedByName = CurrentUser?.Email;
                    entity.ModifiedOn = CurrentDateTime;

                    UOW.Questionnaires.Update(entity);

                    // Commit with audit enabled to track questionnaire publish/republish actions
                    var commitResult = UOW.Commit(enableAuditing: true, logonUser: CurrentUser?.Email ?? CurrentUser?.Id.ToString() ?? "System");
                    if (commitResult > 0)
                    {
                        result.Item = _mapper.Map<Questionnaire>(entity);

                        // Decompress JSON fields before returning
                        DecompressQuestionnaireJsonFields(result.Item);

                        result.ResultStatus = ResultStatus.Success;

                        // Log the publish action for audit purposes
                        Logger?.LogInformation($"Questionnaire '{entity.Name}' (ID: {entity.Id}) was {(entity.Status == (byte)Enumerations.QuestionnaireStatus.Published ? "published" : "republished")} by user {CurrentUser?.Email ?? "System"} at {CurrentDateTime}");
                    }
                    else
                    {
                        result.ResultStatus = ResultStatus.Failure;
                        result.Message = "Failed to publish questionnaire";
                        Logger?.LogError($"Failed to publish questionnaire '{entity.Name}' (ID: {entity.Id}) by user {CurrentUser?.Email ?? "System"}");
                    }
                }
                else
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "Questionnaire not found";
                }
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
            }
            return result;
        }

        public BusinessResult<Questionnaire> ArchiveQuestionnaire(Guid id)
        {
            var result = new BusinessResult<Questionnaire>();
            try
            {
                var entity = UOW.Questionnaires.GetById(id);
                if (entity != null)
                {
                    entity.Status = (byte)Enumerations.QuestionnaireStatus.Archived;
                    entity.ModifiedBy = CurrentUser?.Id;
                    entity.ModifiedOn = CurrentDateTime;

                    UOW.Questionnaires.Update(entity);

                    // Commit with audit enabled to track questionnaire archiving
                    var commitResult = UOW.Commit(enableAuditing: true, logonUser: CurrentUser?.Email ?? CurrentUser?.Id.ToString() ?? "System");
                    if (commitResult > 0)
                    {
                        result.Item = _mapper.Map<Questionnaire>(entity);

                        // Decompress JSON fields before returning
                        DecompressQuestionnaireJsonFields(result.Item);

                        result.ResultStatus = ResultStatus.Success;

                        // Log the archive action for audit purposes
                        Logger?.LogInformation($"Questionnaire '{entity.Name}' (ID: {entity.Id}) was archived by user {CurrentUser?.Email ?? "System"} at {CurrentDateTime}");
                    }
                    else
                    {
                        result.ResultStatus = ResultStatus.Failure;
                        result.Message = "Failed to archive questionnaire";
                        Logger?.LogError($"Failed to archive questionnaire '{entity.Name}' (ID: {entity.Id}) by user {CurrentUser?.Email ?? "System"}");
                    }
                }
                else
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "Questionnaire not found";
                }
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
            }
            return result;
        }
    }
}
