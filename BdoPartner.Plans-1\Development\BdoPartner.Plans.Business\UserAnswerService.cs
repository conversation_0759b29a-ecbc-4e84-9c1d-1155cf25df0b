using AutoMapper;
using BdoPartner.Plans.Business.Interface;
using BdoPartner.Plans.Common;
using BdoPartner.Plans.Common.Config;
using BdoPartner.Plans.DataAccess;
using BdoPartner.Plans.Model.DTO;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using Entity = BdoPartner.Plans.Model.Entity;

namespace BdoPartner.Plans.Business
{
    /// <summary>
    /// Business service implementation for UserAnswer entity operations
    /// </summary>
    public class UserAnswerService : BaseService, IUserAnswerService
    {
        private readonly IMapper _mapper;

        public UserAnswerService(IUnitOfWork uow, IConfigSettings config, ILogger<BaseService> logger, 
            IHttpContextAccessor httpContextAccessor, IMapper mapper) 
            : base(uow, httpContextAccessor, config, logger)
        {
            _mapper = mapper;
        }

        public BusinessResult<ICollection<UserAnswer>> GetUserAnswers()
        {
            var result = new BusinessResult<ICollection<UserAnswer>>();
            try
            {
                var userAnswers = UOW.UserAnswers.GetAll().Where(ua => ua.IsActive == true).ToList();
                result.Item = _mapper.Map<ICollection<UserAnswer>>(userAnswers);
                result.ResultStatus = ResultStatus.Success;
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
            }
            return result;
        }

        public BusinessResult<ICollection<UserAnswer>> SearchUserAnswers(Guid? formId = null,
            bool? isActive = null, int pageNumber = 1, int pageSize = 50)
        {
            var result = new BusinessResult<ICollection<UserAnswer>>();
            try
            {
                var query = UOW.UserAnswers.GetAll();

                if (formId.HasValue)
                    query = query.Where(ua => ua.FormId == formId.Value);

                if (isActive.HasValue)
                    query = query.Where(ua => ua.IsActive == isActive.Value);

                var userAnswers = query.Skip((pageNumber - 1) * pageSize).Take(pageSize).ToList();
                result.Item = _mapper.Map<ICollection<UserAnswer>>(userAnswers);
                result.ResultStatus = ResultStatus.Success;
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
            }
            return result;
        }

        public BusinessResult<UserAnswer> GetUserAnswerById(Guid id)
        {
            var result = new BusinessResult<UserAnswer>();
            try
            {
                var userAnswer = UOW.UserAnswers.GetById(id);
                if (userAnswer != null)
                {
                    result.Item = _mapper.Map<UserAnswer>(userAnswer);
                    result.ResultStatus = ResultStatus.Success;
                }
                else
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "User answer not found";
                }
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
            }
            return result;
        }

        public BusinessResult<ICollection<UserAnswer>> GetUserAnswersByFormId(Guid formId)
        {
            var result = new BusinessResult<ICollection<UserAnswer>>();
            try
            {
                var userAnswers = UOW.UserAnswers.GetAll()
                    .Where(ua => ua.FormId == formId && ua.IsActive == true).ToList();
                result.Item = _mapper.Map<ICollection<UserAnswer>>(userAnswers);
                result.ResultStatus = ResultStatus.Success;
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
            }
            return result;
        }

        public BusinessResult<UserAnswer> CreateUserAnswer(UserAnswer userAnswer)
        {
            var result = new BusinessResult<UserAnswer>();
            try
            {
                var entity = _mapper.Map<Entity.UserAnswer>(userAnswer);
                entity.Id = Guid.NewGuid();
                entity.CreatedBy = CurrentUser?.Id;
                entity.CreatedByName = CurrentUser?.Email;
                entity.CreatedOn = CurrentDateTime;
                entity.IsActive = true;

                UOW.UserAnswers.Add(entity);

                if (UOW.Commit() > 0)
                {
                    result.Item = _mapper.Map<UserAnswer>(entity);
                    result.ResultStatus = ResultStatus.Success;
                }
                else
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "Failed to create user answer";
                }
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
            }
            return result;
        }

        public BusinessResult<UserAnswer> UpdateUserAnswer(UserAnswer userAnswer)
        {
            var result = new BusinessResult<UserAnswer>();
            try
            {
                var existingEntity = UOW.UserAnswers.GetById(userAnswer.Id);
                if (existingEntity != null)
                {
                    _mapper.Map(userAnswer, existingEntity);
                    existingEntity.ModifiedBy = CurrentUser?.Id;
                    existingEntity.ModifiedOn = CurrentDateTime;

                    UOW.UserAnswers.Update(existingEntity);

                    if (UOW.Commit() > 0)
                    {
                        result.Item = _mapper.Map<UserAnswer>(existingEntity);
                        result.ResultStatus = ResultStatus.Success;
                    }
                    else
                    {
                        result.ResultStatus = ResultStatus.Failure;
                        result.Message = "Failed to update user answer";
                    }
                }
                else
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "User answer not found";
                }
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
            }
            return result;
        }

        public BusinessResult<bool> DeleteUserAnswer(Guid id)
        {
            var result = new BusinessResult<bool>();
            try
            {
                var entity = UOW.UserAnswers.GetById(id);
                if (entity != null)
                {
                    entity.IsActive = false;
                    entity.ModifiedBy = CurrentUser?.Id;
                    entity.ModifiedOn = CurrentDateTime;

                    UOW.UserAnswers.Update(entity);

                    if (UOW.Commit() > 0)
                    {
                        result.Item = true;
                        result.ResultStatus = ResultStatus.Success;
                    }
                    else
                    {
                        result.ResultStatus = ResultStatus.Failure;
                        result.Message = "Failed to delete user answer";
                    }
                }
                else
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "User answer not found";
                }
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
            }
            return result;
        }

        public BusinessResult<ICollection<UserAnswer>> BulkCreateUserAnswers(Guid formId, ICollection<UserAnswer> userAnswers)
        {
            var result = new BusinessResult<ICollection<UserAnswer>>();
            try
            {
                var entities = new List<Entity.UserAnswer>();
                
                foreach (var userAnswer in userAnswers)
                {
                    var entity = _mapper.Map<Entity.UserAnswer>(userAnswer);
                    entity.Id = Guid.NewGuid();
                    entity.FormId = formId;
                    entity.CreatedBy = CurrentUser?.Id;
                    entity.CreatedOn = CurrentDateTime;
                    entity.IsActive = true;
                    
                    entities.Add(entity);
                    UOW.UserAnswers.Add(entity);
                }

                if (UOW.Commit() > 0)
                {
                    result.Item = _mapper.Map<ICollection<UserAnswer>>(entities);
                    result.ResultStatus = ResultStatus.Success;
                }
                else
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "Failed to create user answers";
                }
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
            }
            return result;
        }

        public BusinessResult<ICollection<UserAnswer>> BulkUpdateUserAnswers(Guid formId, ICollection<UserAnswer> userAnswers)
        {
            var result = new BusinessResult<ICollection<UserAnswer>>();
            try
            {
                var updatedEntities = new List<Entity.UserAnswer>();
                
                foreach (var userAnswer in userAnswers)
                {
                    var existingEntity = UOW.UserAnswers.GetById(userAnswer.Id);
                    if (existingEntity != null && existingEntity.FormId == formId)
                    {
                        _mapper.Map(userAnswer, existingEntity);
                        existingEntity.ModifiedBy = CurrentUser?.Id;
                        existingEntity.ModifiedOn = CurrentDateTime;
                        
                        UOW.UserAnswers.Update(existingEntity);
                        updatedEntities.Add(existingEntity);
                    }
                }

                if (UOW.Commit() > 0)
                {
                    result.Item = _mapper.Map<ICollection<UserAnswer>>(updatedEntities);
                    result.ResultStatus = ResultStatus.Success;
                }
                else
                {
                    result.ResultStatus = ResultStatus.Failure;
                    result.Message = "Failed to update user answers";
                }
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
            }
            return result;
        }

        public BusinessResult<UserAnswer> SaveOrUpdateUserAnswer(Guid formId, string answerData)
        {
            var result = new BusinessResult<UserAnswer>();
            try
            {
                // Check if there's already a user answer for this form
                var existingAnswer = UOW.UserAnswers.GetAll()
                    .Where(ua => ua.FormId == formId && ua.IsActive == true)
                    .FirstOrDefault();

                if (existingAnswer != null)
                {
                    // Update existing answer
                    existingAnswer.Answer = answerData;
                    existingAnswer.ModifiedBy = CurrentUser?.Id;
                    existingAnswer.ModifiedByName = CurrentUser?.Email;
                    existingAnswer.ModifiedOn = CurrentDateTime;

                    UOW.UserAnswers.Update(existingAnswer);

                    if (UOW.Commit() > 0)
                    {
                        result.Item = _mapper.Map<UserAnswer>(existingAnswer);
                        result.ResultStatus = ResultStatus.Success;
                    }
                    else
                    {
                        result.ResultStatus = ResultStatus.Failure;
                        result.Message = "Failed to update user answer";
                    }
                }
                else
                {
                    // Create new answer
                    var newAnswer = new Entity.UserAnswer
                    {
                        Id = Guid.NewGuid(),
                        FormId = formId,
                        Answer = answerData,
                        IsActive = true,
                        CreatedBy = CurrentUser?.Id,
                        CreatedByName = CurrentUser?.Email,
                        CreatedOn = CurrentDateTime
                    };

                    UOW.UserAnswers.Add(newAnswer);

                    if (UOW.Commit() > 0)
                    {
                        result.Item = _mapper.Map<UserAnswer>(newAnswer);
                        result.ResultStatus = ResultStatus.Success;
                    }
                    else
                    {
                        result.ResultStatus = ResultStatus.Failure;
                        result.Message = "Failed to create user answer";
                    }
                }
            }
            catch (Exception ex)
            {
                result.ResultStatus = ResultStatus.Failure;
                result.Message = ex.Message;
            }
            return result;
        }
    }
}
