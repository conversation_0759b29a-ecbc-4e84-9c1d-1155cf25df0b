{"version": 3, "targets": {"net5.0": {"AutoMapper/10.1.1": {"type": "package", "dependencies": {"Microsoft.CSharp": "4.7.0", "System.Reflection.Emit": "4.7.0"}, "compile": {"lib/netstandard2.0/AutoMapper.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/AutoMapper.dll": {"related": ".xml"}}}, "AutoMapper.Extensions.Microsoft.DependencyInjection/8.1.1": {"type": "package", "dependencies": {"AutoMapper": "[10.1.1, 11.0.0)", "Microsoft.Extensions.DependencyInjection.Abstractions": "3.0.0", "Microsoft.Extensions.Options": "3.0.0"}, "compile": {"lib/netstandard2.0/AutoMapper.Extensions.Microsoft.DependencyInjection.dll": {}}, "runtime": {"lib/netstandard2.0/AutoMapper.Extensions.Microsoft.DependencyInjection.dll": {}}}, "Azure.Core/1.6.0": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.0.0", "System.Buffers": "4.5.0", "System.Diagnostics.DiagnosticSource": "4.6.0", "System.Memory": "4.5.3", "System.Numerics.Vectors": "4.5.0", "System.Text.Json": "4.6.0", "System.Threading.Tasks.Extensions": "4.5.2"}, "compile": {"lib/netstandard2.0/Azure.Core.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Azure.Core.dll": {"related": ".xml"}}}, "Azure.Identity/1.3.0": {"type": "package", "dependencies": {"Azure.Core": "1.6.0", "Microsoft.Identity.Client": "4.22.0", "Microsoft.Identity.Client.Extensions.Msal": "2.16.5", "System.Memory": "4.5.3", "System.Security.Cryptography.ProtectedData": "4.5.0", "System.Text.Json": "4.6.0", "System.Threading.Tasks.Extensions": "4.5.2"}, "compile": {"lib/netstandard2.0/Azure.Identity.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Azure.Identity.dll": {"related": ".xml"}}}, "Azure.Security.KeyVault.Certificates/4.1.0": {"type": "package", "dependencies": {"Azure.Core": "1.0.2", "System.Memory": "4.5.3", "System.Text.Json": "4.6.0", "System.Threading.Tasks.Extensions": "4.5.2"}, "compile": {"lib/netstandard2.0/Azure.Security.KeyVault.Certificates.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Azure.Security.KeyVault.Certificates.dll": {"related": ".xml"}}}, "Azure.Security.KeyVault.Secrets/4.1.0": {"type": "package", "dependencies": {"Azure.Core": "1.0.2", "System.Memory": "4.5.3", "System.Text.Json": "4.6.0", "System.Threading.Tasks.Extensions": "4.5.2"}, "compile": {"lib/netstandard2.0/Azure.Security.KeyVault.Secrets.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Azure.Security.KeyVault.Secrets.dll": {"related": ".xml"}}}, "Castle.Core/4.4.1": {"type": "package", "dependencies": {"NETStandard.Library": "1.6.1", "System.Collections.Specialized": "4.3.0", "System.ComponentModel": "4.3.0", "System.ComponentModel.TypeConverter": "4.3.0", "System.Diagnostics.TraceSource": "4.3.0", "System.Dynamic.Runtime": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Xml.XmlDocument": "4.3.0"}, "compile": {"lib/netstandard1.5/Castle.Core.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.5/Castle.Core.dll": {"related": ".xml"}}}, "EPPlus/5.8.4": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Json": "5.0.0", "Microsoft.IO.RecyclableMemoryStream": "1.4.1", "System.ComponentModel.Annotations": "5.0.0", "System.ComponentModel.TypeConverter": "4.3.0", "System.Data.Common": "4.3.0", "System.Drawing.Common": "5.0.0", "System.Reflection": "4.3.0", "System.Security.Claims": "4.3.0", "System.Security.Cryptography.Pkcs": "5.0.0", "System.Security.Cryptography.X509Certificates": "4.3.2", "System.Text.Encoding.CodePages": "5.0.0", "System.Xml.XPath.XmlDocument": "4.3.0", "System.Xml.XmlDocument": "4.3.0"}, "compile": {"lib/net5.0/EPPlus.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/EPPlus.dll": {"related": ".xml"}}}, "IdentityModel/5.1.0": {"type": "package", "dependencies": {"System.Text.Encodings.Web": "5.0.1", "System.Text.Json": "5.0.1"}, "compile": {"lib/net5.0/IdentityModel.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net5.0/IdentityModel.dll": {"related": ".pdb;.xml"}}}, "IdentityServer4/4.1.2": {"type": "package", "dependencies": {"IdentityModel": "4.4.0", "IdentityServer4.Storage": "4.1.2", "Microsoft.AspNetCore.Authentication.OpenIdConnect": "3.1.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "5.6.0", "Newtonsoft.Json": "12.0.2"}, "compile": {"lib/netcoreapp3.1/IdentityServer4.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/IdentityServer4.dll": {"related": ".pdb;.xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "IdentityServer4.Storage/4.1.2": {"type": "package", "dependencies": {"IdentityModel": "4.4.0"}, "compile": {"lib/netstandard2.0/IdentityServer4.Storage.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/IdentityServer4.Storage.dll": {"related": ".pdb;.xml"}}}, "Microsoft.ApplicationInsights/2.17.0": {"type": "package", "dependencies": {"System.Diagnostics.DiagnosticSource": "5.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.ApplicationInsights.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.ApplicationInsights.dll": {"related": ".pdb;.xml"}}}, "Microsoft.AspNetCore.Authentication/2.2.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Authentication.Core": "2.2.0", "Microsoft.AspNetCore.DataProtection": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0", "Microsoft.Extensions.WebEncoders": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Authentication.Abstractions/2.2.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Authentication.Core/2.2.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Authentication.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Core.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Core.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Authentication.JwtBearer/5.0.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.7.1"}, "compile": {"lib/net5.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Microsoft.AspNetCore.Authentication.OpenIdConnect/5.0.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.7.1"}, "compile": {"lib/net5.0/Microsoft.AspNetCore.Authentication.OpenIdConnect.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/Microsoft.AspNetCore.Authentication.OpenIdConnect.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Microsoft.AspNetCore.Cryptography.Internal/2.2.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Cryptography.Internal.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Cryptography.Internal.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.DataProtection/2.2.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "2.2.0", "Microsoft.AspNetCore.DataProtection.Abstractions": "2.2.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0", "Microsoft.Win32.Registry": "4.5.0", "System.Security.Cryptography.Xml": "4.5.0", "System.Security.Principal.Windows": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.DataProtection.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.DataProtection.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.DataProtection.Abstractions/2.2.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.DataProtection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.DataProtection.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Hosting/2.2.7": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.Extensions.Configuration": "2.2.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "2.2.4", "Microsoft.Extensions.Configuration.FileExtensions": "2.2.0", "Microsoft.Extensions.DependencyInjection": "2.2.0", "Microsoft.Extensions.FileProviders.Physical": "2.2.0", "Microsoft.Extensions.Hosting.Abstractions": "2.2.0", "Microsoft.Extensions.Logging": "2.2.0", "Microsoft.Extensions.Options": "2.2.0", "System.Diagnostics.DiagnosticSource": "4.5.1", "System.Reflection.Metadata": "1.6.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Hosting.Abstractions/2.2.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Hosting.Server.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Hosting.Abstractions": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.2.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "Microsoft.Extensions.Configuration.Abstractions": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Server.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Server.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Http/2.2.2": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.AspNetCore.WebUtilities": "2.2.0", "Microsoft.Extensions.ObjectPool": "2.2.0", "Microsoft.Extensions.Options": "2.2.0", "Microsoft.Net.Http.Headers": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "System.Text.Encodings.Web": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Http.Extensions/2.2.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.FileProviders.Abstractions": "2.2.0", "Microsoft.Net.Http.Headers": "2.2.0", "System.Buffers": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Extensions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.WebUtilities/2.2.0": {"type": "package", "dependencies": {"Microsoft.Net.Http.Headers": "2.2.0", "System.Text.Encodings.Web": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.WebUtilities.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.WebUtilities.dll": {"related": ".xml"}}}, "Microsoft.Bcl.AsyncInterfaces/1.0.0": {"type": "package", "compile": {"ref/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {}}, "runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}}, "Microsoft.CSharp/4.7.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "Microsoft.Data.SqlClient/3.0.0": {"type": "package", "dependencies": {"Azure.Identity": "1.3.0", "Microsoft.Data.SqlClient.SNI.runtime": "3.0.0", "Microsoft.Identity.Client": "4.22.0", "Microsoft.IdentityModel.JsonWebTokens": "6.8.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.8.0", "Microsoft.Win32.Registry": "4.7.0", "System.Configuration.ConfigurationManager": "4.7.0", "System.Diagnostics.DiagnosticSource": "4.7.0", "System.Runtime.Caching": "4.7.0", "System.Security.Principal.Windows": "4.7.0", "System.Text.Encoding.CodePages": "4.7.0", "System.Text.Encodings.Web": "4.7.2"}, "compile": {"ref/netcoreapp3.1/Microsoft.Data.SqlClient.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/Microsoft.Data.SqlClient.dll": {"related": ".pdb;.xml"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp3.1/Microsoft.Data.SqlClient.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netcoreapp3.1/Microsoft.Data.SqlClient.dll": {"assetType": "runtime", "rid": "win"}}}, "Microsoft.Data.SqlClient.SNI.runtime/3.0.0": {"type": "package", "runtimeTargets": {"runtimes/win-arm/native/Microsoft.Data.SqlClient.SNI.dll": {"assetType": "native", "rid": "win-arm"}, "runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll": {"assetType": "native", "rid": "win-x86"}}}, "Microsoft.EntityFrameworkCore/5.0.8": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "5.0.8", "Microsoft.EntityFrameworkCore.Analyzers": "5.0.8", "Microsoft.Extensions.Caching.Memory": "5.0.0", "Microsoft.Extensions.DependencyInjection": "5.0.2", "Microsoft.Extensions.Logging": "5.0.0", "System.Collections.Immutable": "5.0.0", "System.ComponentModel.Annotations": "5.0.0", "System.Diagnostics.DiagnosticSource": "5.0.1"}, "compile": {"lib/netstandard2.1/Microsoft.EntityFrameworkCore.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.EntityFrameworkCore.dll": {"related": ".xml"}}}, "Microsoft.EntityFrameworkCore.Abstractions/5.0.8": {"type": "package", "compile": {"lib/netstandard2.1/Microsoft.EntityFrameworkCore.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.EntityFrameworkCore.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.EntityFrameworkCore.Analyzers/5.0.8": {"type": "package", "compile": {"lib/netstandard2.0/_._": {}}, "runtime": {"lib/netstandard2.0/_._": {}}}, "Microsoft.EntityFrameworkCore.InMemory/5.0.8": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore": "5.0.8"}, "compile": {"lib/netstandard2.1/Microsoft.EntityFrameworkCore.InMemory.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.EntityFrameworkCore.InMemory.dll": {"related": ".xml"}}}, "Microsoft.EntityFrameworkCore.Proxies/5.0.8": {"type": "package", "dependencies": {"Castle.Core": "4.4.1", "Microsoft.EntityFrameworkCore": "5.0.8"}, "compile": {"lib/netstandard2.1/Microsoft.EntityFrameworkCore.Proxies.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.EntityFrameworkCore.Proxies.dll": {"related": ".xml"}}}, "Microsoft.EntityFrameworkCore.Relational/5.0.8": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore": "5.0.8", "Microsoft.Extensions.Configuration.Abstractions": "5.0.0"}, "compile": {"lib/netstandard2.1/Microsoft.EntityFrameworkCore.Relational.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.EntityFrameworkCore.Relational.dll": {"related": ".xml"}}}, "Microsoft.EntityFrameworkCore.SqlServer/5.0.8": {"type": "package", "dependencies": {"Microsoft.Data.SqlClient": "2.0.1", "Microsoft.EntityFrameworkCore.Relational": "5.0.8"}, "compile": {"lib/netstandard2.1/Microsoft.EntityFrameworkCore.SqlServer.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.EntityFrameworkCore.SqlServer.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Caching.Abstractions/5.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "5.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Caching.Memory/5.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Caching.Abstractions": "5.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0", "Microsoft.Extensions.Primitives": "5.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration/5.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "5.0.0", "Microsoft.Extensions.Primitives": "5.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration.Abstractions/5.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "5.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration.Binder/5.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "5.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/5.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "5.0.0", "Microsoft.Extensions.Configuration.Abstractions": "5.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration.FileExtensions/5.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "5.0.0", "Microsoft.Extensions.Configuration.Abstractions": "5.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "5.0.0", "Microsoft.Extensions.FileProviders.Physical": "5.0.0", "Microsoft.Extensions.Primitives": "5.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration.Json/5.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "5.0.0", "Microsoft.Extensions.Configuration.Abstractions": "5.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "5.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "5.0.0"}, "compile": {"lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}}, "Microsoft.Extensions.DependencyInjection/5.0.2": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0"}, "compile": {"lib/net5.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/5.0.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.FileProviders.Abstractions/5.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "5.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.FileProviders.Physical/5.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "5.0.0", "Microsoft.Extensions.FileSystemGlobbing": "5.0.0", "Microsoft.Extensions.Primitives": "5.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}}, "Microsoft.Extensions.FileSystemGlobbing/5.0.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Hosting.Abstractions/2.2.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "2.2.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.2.0", "Microsoft.Extensions.FileProviders.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Logging/5.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection": "5.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0"}, "compile": {"lib/netstandard2.1/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Logging.Abstractions/5.0.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Logging.ApplicationInsights/2.17.0": {"type": "package", "dependencies": {"Microsoft.ApplicationInsights": "2.17.0", "Microsoft.Extensions.Logging": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Logging.ApplicationInsights.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.ApplicationInsights.dll": {"related": ".pdb;.xml"}}}, "Microsoft.Extensions.Logging.AzureAppServices/5.0.8": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.EnvironmentVariables": "5.0.0", "Microsoft.Extensions.Configuration.Json": "5.0.0", "Microsoft.Extensions.Logging": "5.0.0", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Logging.Configuration": "5.0.0", "System.ValueTuple": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Logging.AzureAppServices.dll": {}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.AzureAppServices.dll": {}}}, "Microsoft.Extensions.Logging.Configuration/5.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "5.0.0", "Microsoft.Extensions.Configuration.Abstractions": "5.0.0", "Microsoft.Extensions.Configuration.Binder": "5.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Logging": "5.0.0", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "5.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Configuration.dll": {"related": ".xml"}}}, "Microsoft.Extensions.ObjectPool/2.2.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.Extensions.ObjectPool.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.ObjectPool.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Options/5.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Primitives": "5.0.0"}, "compile": {"lib/net5.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/5.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "5.0.0", "Microsoft.Extensions.Configuration.Binder": "5.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0", "Microsoft.Extensions.Primitives": "5.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Primitives/5.0.0": {"type": "package", "compile": {"lib/netcoreapp3.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}}, "Microsoft.Extensions.WebEncoders/2.2.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0", "System.Text.Encodings.Web": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.WebEncoders.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.WebEncoders.dll": {"related": ".xml"}}}, "Microsoft.Graph/3.35.0": {"type": "package", "dependencies": {"Microsoft.Graph.Core": "1.25.1"}, "compile": {"lib/netstandard2.0/Microsoft.Graph.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Graph.dll": {"related": ".xml"}}}, "Microsoft.Graph.Core/1.25.1": {"type": "package", "dependencies": {"NETStandard.Library": "1.6.0", "Newtonsoft.Json": "9.0.1", "System.Diagnostics.DiagnosticSource": "4.7.1", "System.Net.Http": "4.3.4", "System.ValueTuple": "4.3.0"}, "compile": {"lib/netstandard1.1/Microsoft.Graph.Core.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.1/Microsoft.Graph.Core.dll": {"related": ".xml"}}}, "Microsoft.Identity.Client/4.33.0": {"type": "package", "compile": {"lib/netcoreapp2.1/Microsoft.Identity.Client.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp2.1/Microsoft.Identity.Client.dll": {"related": ".xml"}}}, "Microsoft.Identity.Client.Extensions.Msal/2.16.5": {"type": "package", "dependencies": {"Microsoft.Identity.Client": "4.22.0", "System.Security.Cryptography.ProtectedData": "4.5.0"}, "compile": {"lib/netcoreapp2.1/Microsoft.Identity.Client.Extensions.Msal.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp2.1/Microsoft.Identity.Client.Extensions.Msal.dll": {"related": ".xml"}}}, "Microsoft.Identity.Web/1.14.0": {"type": "package", "dependencies": {"Azure.Identity": "1.3.0", "Azure.Security.KeyVault.Certificates": "4.1.0", "Azure.Security.KeyVault.Secrets": "4.1.0", "Microsoft.AspNetCore.Authentication.JwtBearer": "5.0.0", "Microsoft.AspNetCore.Authentication.OpenIdConnect": "5.0.0", "Microsoft.Identity.Client": "4.33.0", "Microsoft.IdentityModel.Logging": "6.11.1", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.11.1", "System.IdentityModel.Tokens.Jwt": "6.11.1", "System.Text.Encodings.Web": "5.0.1"}, "compile": {"lib/net5.0/Microsoft.Identity.Web.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/Microsoft.Identity.Web.dll": {"related": ".xml"}}}, "Microsoft.Identity.Web.MicrosoftGraph/1.14.0": {"type": "package", "dependencies": {"Microsoft.Graph": "3.35.0", "Microsoft.Identity.Web": "1.14.0"}, "compile": {"lib/net5.0/Microsoft.Identity.Web.MicrosoftGraph.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/Microsoft.Identity.Web.MicrosoftGraph.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Clients.ActiveDirectory/5.2.9": {"type": "package", "dependencies": {"Microsoft.CSharp": "4.3.0", "NETStandard.Library": "1.6.1", "System.ComponentModel.TypeConverter": "4.3.0", "System.Dynamic.Runtime": "4.3.0", "System.Net.Http": "4.3.4", "System.Private.Uri": "4.3.2", "System.Runtime.Serialization.Formatters": "4.3.0", "System.Runtime.Serialization.Json": "4.3.0", "System.Runtime.Serialization.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Security.SecureString": "4.3.0", "System.Xml.XDocument": "4.3.0", "System.Xml.XmlDocument": "4.3.0"}, "compile": {"lib/netstandard1.3/Microsoft.IdentityModel.Clients.ActiveDirectory.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/Microsoft.IdentityModel.Clients.ActiveDirectory.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.JsonWebTokens/6.11.1": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Tokens": "6.11.1"}, "compile": {"lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Logging/6.11.1": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.IdentityModel.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Logging.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Protocols/6.11.1": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Logging": "6.11.1", "Microsoft.IdentityModel.Tokens": "6.11.1"}, "compile": {"lib/netstandard2.0/Microsoft.IdentityModel.Protocols.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Protocols.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.11.1": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Protocols": "6.11.1", "System.IdentityModel.Tokens.Jwt": "6.11.1"}, "compile": {"lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Tokens/6.11.1": {"type": "package", "dependencies": {"Microsoft.CSharp": "4.5.0", "Microsoft.IdentityModel.Logging": "6.11.1", "System.Security.Cryptography.Cng": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.IdentityModel.Tokens.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Tokens.dll": {"related": ".xml"}}}, "Microsoft.IO.RecyclableMemoryStream/1.4.1": {"type": "package", "compile": {"lib/netcoreapp2.1/Microsoft.IO.RecyclableMemoryStream.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp2.1/Microsoft.IO.RecyclableMemoryStream.dll": {"related": ".xml"}}}, "Microsoft.Net.Http.Headers/2.2.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "2.2.0", "System.Buffers": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.Net.Http.Headers.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Net.Http.Headers.dll": {"related": ".xml"}}}, "Microsoft.NETCore.Platforms/5.0.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.NETCore.Targets/1.1.3": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.Win32.Primitives/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/Microsoft.Win32.Primitives.dll": {"related": ".xml"}}}, "Microsoft.Win32.Registry/4.7.0": {"type": "package", "dependencies": {"System.Security.AccessControl": "4.7.0", "System.Security.Principal.Windows": "4.7.0"}, "compile": {"ref/netstandard2.0/Microsoft.Win32.Registry.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"assetType": "runtime", "rid": "win"}}}, "Microsoft.Win32.SystemEvents/5.0.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "5.0.0"}, "compile": {"ref/netstandard2.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Win32.SystemEvents.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp3.0/Microsoft.Win32.SystemEvents.dll": {"assetType": "runtime", "rid": "win"}}}, "NETStandard.Library/1.6.1": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.Win32.Primitives": "4.3.0", "System.AppContext": "4.3.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Console": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.Compression.ZipFile": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Net.Http": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Net.Sockets": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.InteropServices.RuntimeInformation": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Timer": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XDocument": "4.3.0"}}, "Newtonsoft.Json/12.0.2": {"type": "package", "compile": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"related": ".xml"}}}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "runtimeTargets": {"runtimes/debian.8-x64/native/System.Security.Cryptography.Native.OpenSsl.so": {"assetType": "native", "rid": "debian.8-x64"}}}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "runtimeTargets": {"runtimes/fedora.23-x64/native/System.Security.Cryptography.Native.OpenSsl.so": {"assetType": "native", "rid": "fedora.23-x64"}}}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "runtimeTargets": {"runtimes/fedora.24-x64/native/System.Security.Cryptography.Native.OpenSsl.so": {"assetType": "native", "rid": "fedora.24-x64"}}}, "runtime.native.System/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}, "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "runtime.native.System.IO.Compression/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}, "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "runtime.native.System.Net.Http/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}, "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "dependencies": {"runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "4.3.0"}, "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "dependencies": {"runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}, "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "runtimeTargets": {"runtimes/opensuse.13.2-x64/native/System.Security.Cryptography.Native.OpenSsl.so": {"assetType": "native", "rid": "opensuse.13.2-x64"}}}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "runtimeTargets": {"runtimes/opensuse.42.1-x64/native/System.Security.Cryptography.Native.OpenSsl.so": {"assetType": "native", "rid": "opensuse.42.1-x64"}}}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "runtimeTargets": {"runtimes/osx.10.10-x64/native/System.Security.Cryptography.Native.Apple.dylib": {"assetType": "native", "rid": "osx.10.10-x64"}}}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "runtimeTargets": {"runtimes/osx.10.10-x64/native/System.Security.Cryptography.Native.OpenSsl.dylib": {"assetType": "native", "rid": "osx.10.10-x64"}}}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "runtimeTargets": {"runtimes/rhel.7-x64/native/System.Security.Cryptography.Native.OpenSsl.so": {"assetType": "native", "rid": "rhel.7-x64"}}}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "runtimeTargets": {"runtimes/ubuntu.14.04-x64/native/System.Security.Cryptography.Native.OpenSsl.so": {"assetType": "native", "rid": "ubuntu.14.04-x64"}}}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "runtimeTargets": {"runtimes/ubuntu.16.04-x64/native/System.Security.Cryptography.Native.OpenSsl.so": {"assetType": "native", "rid": "ubuntu.16.04-x64"}}}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "runtimeTargets": {"runtimes/ubuntu.16.10-x64/native/System.Security.Cryptography.Native.OpenSsl.so": {"assetType": "native", "rid": "ubuntu.16.10-x64"}}}, "System.AppContext/4.3.0": {"type": "package", "dependencies": {"System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.6/System.AppContext.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.6/System.AppContext.dll": {}}}, "System.Buffers/4.5.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.Collections/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Collections.dll": {"related": ".xml"}}}, "System.Collections.Concurrent/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Collections.Concurrent.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Collections.Concurrent.dll": {}}}, "System.Collections.Immutable/5.0.0": {"type": "package", "compile": {"lib/netstandard2.0/System.Collections.Immutable.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Collections.Immutable.dll": {"related": ".xml"}}}, "System.Collections.NonGeneric/4.3.0": {"type": "package", "dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Collections.NonGeneric.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Collections.NonGeneric.dll": {}}}, "System.Collections.Specialized/4.3.0": {"type": "package", "dependencies": {"System.Collections.NonGeneric": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Collections.Specialized.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Collections.Specialized.dll": {}}}, "System.ComponentModel/4.3.0": {"type": "package", "dependencies": {"System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/System.ComponentModel.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.ComponentModel.dll": {}}}, "System.ComponentModel.Annotations/5.0.0": {"type": "package", "compile": {"ref/netstandard2.1/System.ComponentModel.Annotations.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/System.ComponentModel.Annotations.dll": {"related": ".xml"}}}, "System.ComponentModel.Primitives/4.3.0": {"type": "package", "dependencies": {"System.ComponentModel": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/System.ComponentModel.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.0/System.ComponentModel.Primitives.dll": {}}}, "System.ComponentModel.TypeConverter/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Collections.NonGeneric": "4.3.0", "System.Collections.Specialized": "4.3.0", "System.ComponentModel": "4.3.0", "System.ComponentModel.Primitives": "4.3.0", "System.Globalization": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}, "compile": {"ref/netstandard1.5/System.ComponentModel.TypeConverter.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.5/System.ComponentModel.TypeConverter.dll": {}}}, "System.Configuration.ConfigurationManager/4.7.0": {"type": "package", "dependencies": {"System.Security.Cryptography.ProtectedData": "4.7.0", "System.Security.Permissions": "4.7.0"}, "compile": {"ref/netstandard2.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}}, "System.Console/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Console.dll": {"related": ".xml"}}}, "System.Data.Common/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.2/System.Data.Common.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.2/System.Data.Common.dll": {}}}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Diagnostics.Debug.dll": {"related": ".xml"}}}, "System.Diagnostics.DiagnosticSource/5.0.1": {"type": "package", "compile": {"lib/net5.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}}, "System.Diagnostics.Tools/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/System.Diagnostics.Tools.dll": {"related": ".xml"}}}, "System.Diagnostics.TraceSource/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Diagnostics.TraceSource.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.3/System.Diagnostics.TraceSource.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.3/System.Diagnostics.TraceSource.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Diagnostics.Tracing/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/System.Diagnostics.Tracing.dll": {"related": ".xml"}}}, "System.Drawing.Common/5.0.0": {"type": "package", "dependencies": {"Microsoft.Win32.SystemEvents": "5.0.0"}, "compile": {"ref/netcoreapp3.0/System.Drawing.Common.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/System.Drawing.Common.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp3.0/System.Drawing.Common.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netcoreapp3.0/System.Drawing.Common.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Dynamic.Runtime/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Dynamic.Runtime.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Dynamic.Runtime.dll": {}}}, "System.Formats.Asn1/5.0.0": {"type": "package", "compile": {"lib/netstandard2.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Formats.Asn1.dll": {"related": ".xml"}}}, "System.Globalization/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Globalization.dll": {"related": ".xml"}}}, "System.Globalization.Calendars/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Globalization.Calendars.dll": {"related": ".xml"}}}, "System.Globalization.Extensions/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.3/System.Globalization.Extensions.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.3/System.Globalization.Extensions.dll": {"assetType": "runtime", "rid": "win"}}}, "System.IdentityModel.Tokens.Jwt/6.11.1": {"type": "package", "dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "6.11.1", "Microsoft.IdentityModel.Tokens": "6.11.1"}, "compile": {"lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.dll": {"related": ".xml"}}}, "System.IO/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.5/System.IO.dll": {"related": ".xml"}}}, "System.IO.Compression/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Buffers": "4.3.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.IO.Compression": "4.3.0"}, "compile": {"ref/netstandard1.3/System.IO.Compression.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.3/System.IO.Compression.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.3/System.IO.Compression.dll": {"assetType": "runtime", "rid": "win"}}}, "System.IO.Compression.ZipFile/4.3.0": {"type": "package", "dependencies": {"System.Buffers": "4.3.0", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0"}, "compile": {"ref/netstandard1.3/System.IO.Compression.ZipFile.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.IO.Compression.ZipFile.dll": {}}}, "System.IO.FileSystem/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.3/System.IO.FileSystem.dll": {"related": ".xml"}}}, "System.IO.FileSystem.Primitives/4.3.0": {"type": "package", "dependencies": {"System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.IO.FileSystem.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.IO.FileSystem.Primitives.dll": {}}}, "System.Linq/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}, "compile": {"ref/netstandard1.6/System.Linq.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.6/System.Linq.dll": {}}}, "System.Linq.Expressions/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}, "compile": {"ref/netstandard1.6/System.Linq.Expressions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.6/System.Linq.Expressions.dll": {}}}, "System.Memory/4.5.3": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "System.Net.Http/4.3.4": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.DiagnosticSource": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}, "compile": {"ref/netstandard1.3/System.Net.Http.dll": {}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.6/System.Net.Http.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.3/System.Net.Http.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Net.Primitives/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Net.Primitives.dll": {"related": ".xml"}}}, "System.Net.Sockets/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Net.Sockets.dll": {"related": ".xml"}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.ObjectModel/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}, "compile": {"ref/netstandard1.3/System.ObjectModel.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.ObjectModel.dll": {}}}, "System.Private.DataContractSerialization/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Serialization.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XDocument": "4.3.0", "System.Xml.XmlDocument": "4.3.0", "System.Xml.XmlSerializer": "4.3.0"}, "compile": {"ref/netstandard/_._": {}}, "runtime": {"lib/netstandard1.3/System.Private.DataContractSerialization.dll": {}}}, "System.Private.Uri/4.3.2": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3"}, "compile": {"ref/netstandard/_._": {}}}, "System.Reflection/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/System.Reflection.dll": {"related": ".xml"}}}, "System.Reflection.Emit/4.7.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Reflection.Emit.ILGeneration.dll": {}}}, "System.Reflection.Emit.Lightweight/4.3.0": {"type": "package", "dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Reflection.Emit.Lightweight.dll": {}}}, "System.Reflection.Extensions/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/System.Reflection.Extensions.dll": {"related": ".xml"}}}, "System.Reflection.Metadata/1.6.0": {"type": "package", "compile": {"lib/netstandard2.0/System.Reflection.Metadata.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Reflection.Metadata.dll": {"related": ".xml"}}}, "System.Reflection.Primitives/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/System.Reflection.Primitives.dll": {"related": ".xml"}}}, "System.Reflection.TypeExtensions/4.3.0": {"type": "package", "dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/System.Reflection.TypeExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.5/System.Reflection.TypeExtensions.dll": {}}}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/System.Resources.ResourceManager.dll": {"related": ".xml"}}}, "System.Runtime/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}, "compile": {"ref/netstandard1.5/System.Runtime.dll": {"related": ".xml"}}}, "System.Runtime.Caching/4.7.0": {"type": "package", "dependencies": {"System.Configuration.ConfigurationManager": "4.7.0"}, "compile": {"ref/netstandard2.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Runtime.Caching.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.Runtime.Caching.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Runtime.Extensions/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/System.Runtime.Extensions.dll": {"related": ".xml"}}}, "System.Runtime.Handles/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Runtime.Handles.dll": {"related": ".xml"}}}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}, "compile": {"ref/netcoreapp1.1/System.Runtime.InteropServices.dll": {}}}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"type": "package", "dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0"}, "compile": {"ref/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll": {}}, "runtime": {"lib/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll": {}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Runtime.Numerics/4.3.0": {"type": "package", "dependencies": {"System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}, "compile": {"ref/netstandard1.1/System.Runtime.Numerics.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Runtime.Numerics.dll": {}}}, "System.Runtime.Serialization.Formatters/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Serialization.Primitives": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Runtime.Serialization.Formatters.dll": {}}, "runtime": {"lib/netstandard1.4/System.Runtime.Serialization.Formatters.dll": {}}}, "System.Runtime.Serialization.Json/4.3.0": {"type": "package", "dependencies": {"System.IO": "4.3.0", "System.Private.DataContractSerialization": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/System.Runtime.Serialization.Json.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Runtime.Serialization.Json.dll": {}}}, "System.Runtime.Serialization.Primitives/4.3.0": {"type": "package", "dependencies": {"System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Runtime.Serialization.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Runtime.Serialization.Primitives.dll": {}}}, "System.Security.AccessControl/4.7.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Security.Principal.Windows": "4.7.0"}, "compile": {"ref/netstandard2.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Claims/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Security.Principal": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Security.Claims.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Security.Claims.dll": {}}}, "System.Security.Cryptography.Algorithms/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.Apple": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}, "compile": {"ref/netstandard1.6/System.Security.Cryptography.Algorithms.dll": {}}, "runtimeTargets": {"runtimes/osx/lib/netstandard1.6/System.Security.Cryptography.Algorithms.dll": {"assetType": "runtime", "rid": "osx"}, "runtimes/unix/lib/netstandard1.6/System.Security.Cryptography.Algorithms.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.6/System.Security.Cryptography.Algorithms.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.Cng/5.0.0": {"type": "package", "dependencies": {"System.Formats.Asn1": "5.0.0"}, "compile": {"ref/netcoreapp3.0/System.Security.Cryptography.Cng.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/System.Security.Cryptography.Cng.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp3.0/System.Security.Cryptography.Cng.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.Csp/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.3/System.Security.Cryptography.Csp.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.3/System.Security.Cryptography.Csp.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.Encoding/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Linq": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Security.Cryptography.Encoding.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.3/System.Security.Cryptography.Encoding.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.3/System.Security.Cryptography.Encoding.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}, "compile": {"ref/netstandard1.6/_._": {}}, "runtime": {"lib/netstandard1.6/System.Security.Cryptography.OpenSsl.dll": {}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.6/System.Security.Cryptography.OpenSsl.dll": {"assetType": "runtime", "rid": "unix"}}}, "System.Security.Cryptography.Pkcs/5.0.0": {"type": "package", "dependencies": {"System.Formats.Asn1": "5.0.0", "System.Security.Cryptography.Cng": "5.0.0"}, "compile": {"ref/netcoreapp3.0/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp3.0/System.Security.Cryptography.Pkcs.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.Primitives/4.3.0": {"type": "package", "dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Security.Cryptography.Primitives.dll": {}}, "runtime": {"lib/netstandard1.3/System.Security.Cryptography.Primitives.dll": {}}}, "System.Security.Cryptography.ProtectedData/4.7.0": {"type": "package", "compile": {"ref/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.X509Certificates/4.3.2": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Cng": "4.3.0", "System.Security.Cryptography.Csp": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}, "compile": {"ref/netstandard1.4/System.Security.Cryptography.X509Certificates.dll": {}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.6/System.Security.Cryptography.X509Certificates.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.6/System.Security.Cryptography.X509Certificates.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.Xml/4.5.0": {"type": "package", "dependencies": {"System.Security.Cryptography.Pkcs": "4.5.0", "System.Security.Permissions": "4.5.0"}, "compile": {"ref/netstandard2.0/System.Security.Cryptography.Xml.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.Cryptography.Xml.dll": {}}}, "System.Security.Permissions/4.7.0": {"type": "package", "dependencies": {"System.Security.AccessControl": "4.7.0", "System.Windows.Extensions": "4.7.0"}, "compile": {"ref/netcoreapp3.0/System.Security.Permissions.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/System.Security.Permissions.dll": {"related": ".xml"}}}, "System.Security.Principal/4.3.0": {"type": "package", "dependencies": {"System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/System.Security.Principal.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.0/System.Security.Principal.dll": {}}}, "System.Security.Principal.Windows/4.7.0": {"type": "package", "compile": {"ref/netcoreapp3.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.SecureString/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Security.SecureString.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.3/System.Security.SecureString.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.3/System.Security.SecureString.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Text.Encoding/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Text.Encoding.dll": {"related": ".xml"}}}, "System.Text.Encoding.CodePages/5.0.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "5.0.0"}, "compile": {"lib/netstandard2.0/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/System.Text.Encoding.CodePages.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Text.Encoding.Extensions/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Text.Encoding.Extensions.dll": {"related": ".xml"}}}, "System.Text.Encodings.Web/5.0.1": {"type": "package", "compile": {"lib/netcoreapp3.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}}, "System.Text.Json/5.0.1": {"type": "package", "compile": {"lib/netcoreapp3.0/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/System.Text.Json.dll": {"related": ".xml"}}}, "System.Text.RegularExpressions/4.3.0": {"type": "package", "dependencies": {"System.Runtime": "4.3.0"}, "compile": {"ref/netcoreapp1.1/System.Text.RegularExpressions.dll": {}}, "runtime": {"lib/netstandard1.6/System.Text.RegularExpressions.dll": {}}}, "System.Threading/4.3.0": {"type": "package", "dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Threading.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Threading.dll": {}}}, "System.Threading.Tasks/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Threading.Tasks.dll": {"related": ".xml"}}}, "System.Threading.Tasks.Extensions/4.5.2": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "System.Threading.Timer/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.2/System.Threading.Timer.dll": {"related": ".xml"}}}, "System.ValueTuple/4.5.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.Windows.Extensions/4.7.0": {"type": "package", "dependencies": {"System.Drawing.Common": "4.7.0"}, "compile": {"ref/netcoreapp3.0/System.Windows.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/System.Windows.Extensions.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp3.0/System.Windows.Extensions.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Xml.ReaderWriter/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Tasks.Extensions": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Xml.ReaderWriter.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Xml.ReaderWriter.dll": {}}}, "System.Xml.XDocument/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Xml.XDocument.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Xml.XDocument.dll": {}}}, "System.Xml.XmlDocument/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Xml.XmlDocument.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Xml.XmlDocument.dll": {}}}, "System.Xml.XmlSerializer/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XmlDocument": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Xml.XmlSerializer.dll": {}}}, "System.Xml.XPath/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Xml.XPath.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Xml.XPath.dll": {}}}, "System.Xml.XPath.XmlDocument/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XPath": "4.3.0", "System.Xml.XmlDocument": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Xml.XPath.XmlDocument.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Xml.XPath.XmlDocument.dll": {}}}, "BdoPartner.Plans.Business.Interface/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v5.0", "dependencies": {"BdoPartner.Plans.Common": "1.0.0", "BdoPartner.Plans.DataAccess.Common": "1.0.0", "BdoPartner.Plans.Model.DTO": "1.0.0"}, "compile": {"bin/placeholder/BdoPartner.Plans.Business.Interface.dll": {}}, "runtime": {"bin/placeholder/BdoPartner.Plans.Business.Interface.dll": {}}}, "BdoPartner.Plans.Common/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v5.0", "dependencies": {"IdentityModel": "5.1.0", "Microsoft.AspNetCore.Authentication": "2.2.0", "Microsoft.AspNetCore.Hosting": "2.2.7", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Configuration": "5.0.0", "Microsoft.Extensions.Configuration.Binder": "5.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "5.0.0", "Microsoft.Extensions.Configuration.Json": "5.0.0", "Microsoft.Extensions.DependencyInjection": "5.0.2", "Microsoft.Extensions.Logging": "5.0.0", "Microsoft.Extensions.Logging.ApplicationInsights": "2.17.0", "Microsoft.Extensions.Logging.AzureAppServices": "5.0.8", "Microsoft.Extensions.Options": "5.0.0", "Microsoft.IdentityModel.Clients.ActiveDirectory": "5.2.9"}, "compile": {"bin/placeholder/BdoPartner.Plans.Common.dll": {}}, "runtime": {"bin/placeholder/BdoPartner.Plans.Common.dll": {}}}, "BdoPartner.Plans.DataAccess/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v5.0", "dependencies": {"BdoPartner.Plans.DataAccess.Common": "1.0.0", "BdoPartner.Plans.Model.Entity": "1.0.0"}, "compile": {"bin/placeholder/BdoPartner.Plans.DataAccess.dll": {}}, "runtime": {"bin/placeholder/BdoPartner.Plans.DataAccess.dll": {}}}, "BdoPartner.Plans.DataAccess.Common/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v5.0", "dependencies": {"AutoMapper": "10.1.1", "Microsoft.Data.SqlClient": "3.0.0", "Microsoft.EntityFrameworkCore": "5.0.8", "Microsoft.EntityFrameworkCore.InMemory": "5.0.8", "Microsoft.EntityFrameworkCore.Proxies": "5.0.8", "Microsoft.EntityFrameworkCore.SqlServer": "5.0.8"}, "compile": {"bin/placeholder/BdoPartner.Plans.DataAccess.Common.dll": {}}, "runtime": {"bin/placeholder/BdoPartner.Plans.DataAccess.Common.dll": {}}}, "BdoPartner.Plans.Model.DTO/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v5.0", "dependencies": {"BdoPartner.Plans.Common": "1.0.0"}, "compile": {"bin/placeholder/BdoPartner.Plans.Model.DTO.dll": {}}, "runtime": {"bin/placeholder/BdoPartner.Plans.Model.DTO.dll": {}}}, "BdoPartner.Plans.Model.Entity/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v5.0", "dependencies": {"Microsoft.EntityFrameworkCore.SqlServer": "5.0.7"}, "compile": {"bin/placeholder/BdoPartner.Plans.Model.Entity.dll": {}}, "runtime": {"bin/placeholder/BdoPartner.Plans.Model.Entity.dll": {}}}, "BdoPartner.Plans.Model.Mapper/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v5.0", "dependencies": {"AutoMapper": "10.1.1", "AutoMapper.Extensions.Microsoft.DependencyInjection": "8.1.1", "BdoPartner.Plans.Model.DTO": "1.0.0", "BdoPartner.Plans.Model.Entity": "1.0.0"}, "compile": {"bin/placeholder/BdoPartner.Plans.Model.Mapper.dll": {}}, "runtime": {"bin/placeholder/BdoPartner.Plans.Model.Mapper.dll": {}}}}}, "libraries": {"AutoMapper/10.1.1": {"sha512": "uMgbqOdu9ZG5cIOty0C85hzzayBH2i9BthnS5FlMqKtMSHDv4ts81a2jS1VFaDBVhlBeIqJ/kQKjQY95BZde9w==", "type": "package", "path": "automapper/10.1.1", "files": [".nupkg.metadata", ".signature.p7s", "automapper.10.1.1.nupkg.sha512", "automapper.nuspec", "icon.png", "lib/net461/AutoMapper.dll", "lib/net461/AutoMapper.xml", "lib/netstandard2.0/AutoMapper.dll", "lib/netstandard2.0/AutoMapper.xml"]}, "AutoMapper.Extensions.Microsoft.DependencyInjection/8.1.1": {"sha512": "xSWoVzOipuDU4PeZcUfaZQ+xqXU8QmGv5jrdlxt3MYm9xaOmrefqcfzGQ3SQ+D+8wfBa/ZwSuL0qKOVj080inA==", "type": "package", "path": "automapper.extensions.microsoft.dependencyinjection/8.1.1", "files": [".nupkg.metadata", ".signature.p7s", "automapper.extensions.microsoft.dependencyinjection.8.1.1.nupkg.sha512", "automapper.extensions.microsoft.dependencyinjection.nuspec", "icon.png", "lib/netstandard2.0/AutoMapper.Extensions.Microsoft.DependencyInjection.dll"]}, "Azure.Core/1.6.0": {"sha512": "kI4m2NsODPOrxo0OoKjk6B3ADbdovhDQIEmI4039upjjZKRaewVLx/Uz4DfRa/NtnIRZQPUALe1yvdHWAoRt4w==", "type": "package", "path": "azure.core/1.6.0", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "README.md", "azure.core.1.6.0.nupkg.sha512", "azure.core.nuspec", "lib/net461/Azure.Core.dll", "lib/net461/Azure.Core.xml", "lib/netstandard2.0/Azure.Core.dll", "lib/netstandard2.0/Azure.Core.xml", "pkgicon.png"]}, "Azure.Identity/1.3.0": {"sha512": "l1SYfZKOFBuUFG7C2SWHmJcrQQaiXgBdVCycx4vcZQkC6efDVt7mzZ5pfJAFEJDBUq7mjRQ0RPq9ZDGdSswqMg==", "type": "package", "path": "azure.identity/1.3.0", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "README.md", "azure.identity.1.3.0.nupkg.sha512", "azure.identity.nuspec", "lib/netstandard2.0/Azure.Identity.dll", "lib/netstandard2.0/Azure.Identity.xml", "pkgicon.png"]}, "Azure.Security.KeyVault.Certificates/4.1.0": {"sha512": "I+irkjO1JBzJWYBLhW835/b7GllxkjMbQwrirhxUJsf6FQnH+eIGin4T/jBLgyuu1zPsn2AxiUFju6Shb/uiNA==", "type": "package", "path": "azure.security.keyvault.certificates/4.1.0", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "README.md", "azure.security.keyvault.certificates.4.1.0.nupkg.sha512", "azure.security.keyvault.certificates.nuspec", "lib/netstandard2.0/Azure.Security.KeyVault.Certificates.dll", "lib/netstandard2.0/Azure.Security.KeyVault.Certificates.xml", "pkgicon.png"]}, "Azure.Security.KeyVault.Secrets/4.1.0": {"sha512": "SK4XTPEaXI5UTdtSkr8TN4ZKnvazNHhybnnkxHgOyRAhV9ObcIjbrNlhS4ZeR8XtI+HidL+v/WIIVfR1+jKB8Q==", "type": "package", "path": "azure.security.keyvault.secrets/4.1.0", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "README.md", "azure.security.keyvault.secrets.4.1.0.nupkg.sha512", "azure.security.keyvault.secrets.nuspec", "lib/netstandard2.0/Azure.Security.KeyVault.Secrets.dll", "lib/netstandard2.0/Azure.Security.KeyVault.Secrets.xml", "pkgicon.png"]}, "Castle.Core/4.4.1": {"sha512": "zanbjWC0Y05gbx4eGXkzVycOQqVOFVeCjVsDSyuao9P4mtN1w3WxxTo193NGC7j3o2u3AJRswaoC6hEbnGACnQ==", "type": "package", "path": "castle.core/4.4.1", "files": [".nupkg.metadata", ".signature.p7s", "ASL - Apache Software Foundation License.txt", "CHANGELOG.md", "LICENSE", "castle.core.4.4.1.nupkg.sha512", "castle.core.nuspec", "lib/net35/Castle.Core.dll", "lib/net35/Castle.Core.xml", "lib/net40/Castle.Core.dll", "lib/net40/Castle.Core.xml", "lib/net45/Castle.Core.dll", "lib/net45/Castle.Core.xml", "lib/netstandard1.3/Castle.Core.dll", "lib/netstandard1.3/Castle.Core.xml", "lib/netstandard1.5/Castle.Core.dll", "lib/netstandard1.5/Castle.Core.xml", "readme.txt"]}, "EPPlus/5.8.4": {"sha512": "fehxwwo/K9FIGcuHEQSAQmOd455xCewUzafeLtXS0QpfmimrO3037UF27VcIgj3zhS1gkqTeP7OHqxh2ORSqEQ==", "type": "package", "path": "epplus/5.8.4", "files": [".nupkg.metadata", ".signature.p7s", "EPPlusLogo.png", "epplus.5.8.4.nupkg.sha512", "epplus.nuspec", "lib/net35/EPPlus.dll", "lib/net35/EPPlus.xml", "lib/net40/EPPlus.dll", "lib/net40/EPPlus.xml", "lib/net45/EPPlus.dll", "lib/net45/EPPlus.xml", "lib/net5.0/EPPlus.dll", "lib/net5.0/EPPlus.xml", "lib/netstandard2.0/EPPlus.dll", "lib/netstandard2.0/EPPlus.xml", "lib/netstandard2.1/EPPlus.dll", "lib/netstandard2.1/EPPlus.xml", "license.md", "readme.txt"]}, "IdentityModel/5.1.0": {"sha512": "Gcv4YYXncvUvTNLF8lDJyItZzaEDFcEi3geMDtylGcl/Rx90vcV7Kv3yAofktJBW7GvmIjQgC8LtUq0s9XQT2w==", "type": "package", "path": "identitymodel/5.1.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.jpg", "identitymodel.5.1.0.nupkg.sha512", "identitymodel.nuspec", "lib/net461/IdentityModel.dll", "lib/net461/IdentityModel.pdb", "lib/net461/IdentityModel.xml", "lib/net472/IdentityModel.dll", "lib/net472/IdentityModel.pdb", "lib/net472/IdentityModel.xml", "lib/net5.0/IdentityModel.dll", "lib/net5.0/IdentityModel.pdb", "lib/net5.0/IdentityModel.xml", "lib/netstandard2.0/IdentityModel.dll", "lib/netstandard2.0/IdentityModel.pdb", "lib/netstandard2.0/IdentityModel.xml"]}, "IdentityServer4/4.1.2": {"sha512": "blaxxGuOA7v/w1q+fxn97wZ+x2ecG1ZD4mc/N/ZOXMNeFZZhqv+4LF26Gecyik3nWrJPmbMEtQbLmRsKG8k61w==", "type": "package", "path": "identityserver4/4.1.2", "files": [".nupkg.metadata", ".signature.p7s", "icon.jpg", "identityserver4.4.1.2.nupkg.sha512", "identityserver4.nuspec", "lib/netcoreapp3.1/IdentityServer4.dll", "lib/netcoreapp3.1/IdentityServer4.pdb", "lib/netcoreapp3.1/IdentityServer4.xml"]}, "IdentityServer4.Storage/4.1.2": {"sha512": "KoSffyZyyeCNTIyJiZnCuPakJ1QbCHlpty6gbWUj/7yl+w0PXIchgmmJnJSvddzBb8iZ2xew/vGlxWUIP17P2g==", "type": "package", "path": "identityserver4.storage/4.1.2", "files": [".nupkg.metadata", ".signature.p7s", "icon.jpg", "identityserver4.storage.4.1.2.nupkg.sha512", "identityserver4.storage.nuspec", "lib/netstandard2.0/IdentityServer4.Storage.dll", "lib/netstandard2.0/IdentityServer4.Storage.pdb", "lib/netstandard2.0/IdentityServer4.Storage.xml"]}, "Microsoft.ApplicationInsights/2.17.0": {"sha512": "moAOrjhwiCWdg8I4fXPEd+bnnyCSRxo6wmYQ0HuNrWJUctzZEiyVTbJ8QTS6+dBOFTxpI6x+OY5wHPHrgWOk1Q==", "type": "package", "path": "microsoft.applicationinsights/2.17.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net452/Microsoft.ApplicationInsights.dll", "lib/net452/Microsoft.ApplicationInsights.pdb", "lib/net452/Microsoft.ApplicationInsights.xml", "lib/net46/Microsoft.ApplicationInsights.dll", "lib/net46/Microsoft.ApplicationInsights.pdb", "lib/net46/Microsoft.ApplicationInsights.xml", "lib/netstandard2.0/Microsoft.ApplicationInsights.dll", "lib/netstandard2.0/Microsoft.ApplicationInsights.pdb", "lib/netstandard2.0/Microsoft.ApplicationInsights.xml", "microsoft.applicationinsights.2.17.0.nupkg.sha512", "microsoft.applicationinsights.nuspec"]}, "Microsoft.AspNetCore.Authentication/2.2.0": {"sha512": "b0R9X7L6zMqNsssKDvhYHuNi5x0s4DyHTeXybIAyGaitKiW1Q5aAGKdV2codHPiePv9yHfC9hAMyScXQ/xXhPw==", "type": "package", "path": "microsoft.aspnetcore.authentication/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Authentication.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Authentication.xml", "microsoft.aspnetcore.authentication.2.2.0.nupkg.sha512", "microsoft.aspnetcore.authentication.nuspec"]}, "Microsoft.AspNetCore.Authentication.Abstractions/2.2.0": {"sha512": "VloMLDJMf3n/9ic5lCBOa42IBYJgyB1JhzLsL68Zqg+2bEPWfGBj/xCJy/LrKTArN0coOcZp3wyVTZlx0y9pHQ==", "type": "package", "path": "microsoft.aspnetcore.authentication.abstractions/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Abstractions.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Abstractions.xml", "microsoft.aspnetcore.authentication.abstractions.2.2.0.nupkg.sha512", "microsoft.aspnetcore.authentication.abstractions.nuspec"]}, "Microsoft.AspNetCore.Authentication.Core/2.2.0": {"sha512": "XlVJzJ5wPOYW+Y0J6Q/LVTEyfS4ssLXmt60T0SPP+D8abVhBTl+cgw2gDHlyKYIkcJg7btMVh383NDkMVqD/fg==", "type": "package", "path": "microsoft.aspnetcore.authentication.core/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Core.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Core.xml", "microsoft.aspnetcore.authentication.core.2.2.0.nupkg.sha512", "microsoft.aspnetcore.authentication.core.nuspec"]}, "Microsoft.AspNetCore.Authentication.JwtBearer/5.0.0": {"sha512": "26WlfJsOB4QP3VsEVu5GGDSGm5wu6ikQqRUOaZ3BUmyGcMWsD28xFxPxFw4OszYgKpj54QYlyec19KcrEGkqDw==", "type": "package", "path": "microsoft.aspnetcore.authentication.jwtbearer/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net5.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll", "lib/net5.0/Microsoft.AspNetCore.Authentication.JwtBearer.xml", "microsoft.aspnetcore.authentication.jwtbearer.5.0.0.nupkg.sha512", "microsoft.aspnetcore.authentication.jwtbearer.nuspec"]}, "Microsoft.AspNetCore.Authentication.OpenIdConnect/5.0.0": {"sha512": "YzUMJYSVWEEQmHpOhEzOcZ2/ltQ/uP1s8DHK8oaobgsjKUdIeRvEwtVQ5D6zO0vdlj4j9Mw0JD1FrZPGI07fHw==", "type": "package", "path": "microsoft.aspnetcore.authentication.openidconnect/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net5.0/Microsoft.AspNetCore.Authentication.OpenIdConnect.dll", "lib/net5.0/Microsoft.AspNetCore.Authentication.OpenIdConnect.xml", "microsoft.aspnetcore.authentication.openidconnect.5.0.0.nupkg.sha512", "microsoft.aspnetcore.authentication.openidconnect.nuspec"]}, "Microsoft.AspNetCore.Cryptography.Internal/2.2.0": {"sha512": "GXmMD8/vuTLPLvKzKEPz/4vapC5e0cwx1tUVd83ePRyWF9CCrn/pg4/1I+tGkQqFLPvi3nlI2QtPtC6MQN8Nww==", "type": "package", "path": "microsoft.aspnetcore.cryptography.internal/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Cryptography.Internal.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Cryptography.Internal.xml", "microsoft.aspnetcore.cryptography.internal.2.2.0.nupkg.sha512", "microsoft.aspnetcore.cryptography.internal.nuspec"]}, "Microsoft.AspNetCore.DataProtection/2.2.0": {"sha512": "G6dvu5Nd2vjpYbzazZ//qBFbSEf2wmBUbyAR7E4AwO3gWjhoJD5YxpThcGJb7oE3VUcW65SVMXT+cPCiiBg8Sg==", "type": "package", "path": "microsoft.aspnetcore.dataprotection/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.DataProtection.dll", "lib/netstandard2.0/Microsoft.AspNetCore.DataProtection.xml", "microsoft.aspnetcore.dataprotection.2.2.0.nupkg.sha512", "microsoft.aspnetcore.dataprotection.nuspec"]}, "Microsoft.AspNetCore.DataProtection.Abstractions/2.2.0": {"sha512": "seANFXmp8mb5Y12m1ShiElJ3ZdOT3mBN3wA1GPhHJIvZ/BxOCPyqEOR+810OWsxEZwA5r5fDRNpG/CqiJmQnJg==", "type": "package", "path": "microsoft.aspnetcore.dataprotection.abstractions/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.DataProtection.Abstractions.dll", "lib/netstandard2.0/Microsoft.AspNetCore.DataProtection.Abstractions.xml", "microsoft.aspnetcore.dataprotection.abstractions.2.2.0.nupkg.sha512", "microsoft.aspnetcore.dataprotection.abstractions.nuspec"]}, "Microsoft.AspNetCore.Hosting/2.2.7": {"sha512": "O0ZBE53Fa9bVGXykDzvgMFW0Pe1QyPPFg1pazN8l3RUFSWBsDJ9/iD1LHXgADA8+ZD3R/1zkvraPa9SZdievxQ==", "type": "package", "path": "microsoft.aspnetcore.hosting/2.2.7", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Hosting.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Hosting.xml", "microsoft.aspnetcore.hosting.2.2.7.nupkg.sha512", "microsoft.aspnetcore.hosting.nuspec"]}, "Microsoft.AspNetCore.Hosting.Abstractions/2.2.0": {"sha512": "ubycklv+ZY7Kutdwuy1W4upWcZ6VFR8WUXU7l7B2+mvbDBBPAcfpi+E+Y5GFe+Q157YfA3C49D2GCjAZc7Mobw==", "type": "package", "path": "microsoft.aspnetcore.hosting.abstractions/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Abstractions.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Abstractions.xml", "microsoft.aspnetcore.hosting.abstractions.2.2.0.nupkg.sha512", "microsoft.aspnetcore.hosting.abstractions.nuspec"]}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.2.0": {"sha512": "1PMijw8RMtuQF60SsD/JlKtVfvh4NORAhF4wjysdABhlhTrYmtgssqyncR0Stq5vqtjplZcj6kbT4LRTglt9IQ==", "type": "package", "path": "microsoft.aspnetcore.hosting.server.abstractions/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Server.Abstractions.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Server.Abstractions.xml", "microsoft.aspnetcore.hosting.server.abstractions.2.2.0.nupkg.sha512", "microsoft.aspnetcore.hosting.server.abstractions.nuspec"]}, "Microsoft.AspNetCore.Http/2.2.2": {"sha512": "BAibpoItxI5puk7YJbIGj95arZueM8B8M5xT1fXBn3hb3L2G3ucrZcYXv1gXdaroLbntUs8qeV8iuBrpjQsrKw==", "type": "package", "path": "microsoft.aspnetcore.http/2.2.2", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Http.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Http.xml", "microsoft.aspnetcore.http.2.2.2.nupkg.sha512", "microsoft.aspnetcore.http.nuspec"]}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"sha512": "Nxs7Z1q3f1STfLYKJSVXCs1iBl+Ya6E8o4Oy1bCxJ/rNI44E/0f6tbsrVqAWfB7jlnJfyaAtIalBVxPKUPQb4Q==", "type": "package", "path": "microsoft.aspnetcore.http.abstractions/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.xml", "microsoft.aspnetcore.http.abstractions.2.2.0.nupkg.sha512", "microsoft.aspnetcore.http.abstractions.nuspec"]}, "Microsoft.AspNetCore.Http.Extensions/2.2.0": {"sha512": "2DgZ9rWrJtuR7RYiew01nGRzuQBDaGHGmK56Rk54vsLLsCdzuFUPqbDTJCS1qJQWTbmbIQ9wGIOjpxA1t0l7/w==", "type": "package", "path": "microsoft.aspnetcore.http.extensions/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Extensions.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Extensions.xml", "microsoft.aspnetcore.http.extensions.2.2.0.nupkg.sha512", "microsoft.aspnetcore.http.extensions.nuspec"]}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"sha512": "ziFz5zH8f33En4dX81LW84I6XrYXKf9jg6aM39cM+LffN9KJahViKZ61dGMSO2gd3e+qe5yBRwsesvyqlZaSMg==", "type": "package", "path": "microsoft.aspnetcore.http.features/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.xml", "microsoft.aspnetcore.http.features.2.2.0.nupkg.sha512", "microsoft.aspnetcore.http.features.nuspec"]}, "Microsoft.AspNetCore.WebUtilities/2.2.0": {"sha512": "9ErxAAKaDzxXASB/b5uLEkLgUWv1QbeVxyJYEHQwMaxXOeFFVkQxiq8RyfVcifLU7NR0QY0p3acqx4ZpYfhHDg==", "type": "package", "path": "microsoft.aspnetcore.webutilities/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.WebUtilities.dll", "lib/netstandard2.0/Microsoft.AspNetCore.WebUtilities.xml", "microsoft.aspnetcore.webutilities.2.2.0.nupkg.sha512", "microsoft.aspnetcore.webutilities.nuspec"]}, "Microsoft.Bcl.AsyncInterfaces/1.0.0": {"sha512": "K63Y4hORbBcKLWH5wnKgzyn7TOfYzevIEwIedQHBIkmkEBA9SCqgvom+XTuE+fAFGvINGkhFItaZ2dvMGdT5iw==", "type": "package", "path": "microsoft.bcl.asyncinterfaces/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Bcl.AsyncInterfaces.dll", "lib/net461/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.xml", "microsoft.bcl.asyncinterfaces.1.0.0.nupkg.sha512", "microsoft.bcl.asyncinterfaces.nuspec", "ref/net461/Microsoft.Bcl.AsyncInterfaces.dll", "ref/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll", "ref/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.CSharp/4.7.0": {"sha512": "pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "type": "package", "path": "microsoft.csharp/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/Microsoft.CSharp.dll", "lib/netcoreapp2.0/_._", "lib/netstandard1.3/Microsoft.CSharp.dll", "lib/netstandard2.0/Microsoft.CSharp.dll", "lib/netstandard2.0/Microsoft.CSharp.xml", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/uap10.0.16299/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "microsoft.csharp.4.7.0.nupkg.sha512", "microsoft.csharp.nuspec", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/Microsoft.CSharp.dll", "ref/netcore50/Microsoft.CSharp.xml", "ref/netcore50/de/Microsoft.CSharp.xml", "ref/netcore50/es/Microsoft.CSharp.xml", "ref/netcore50/fr/Microsoft.CSharp.xml", "ref/netcore50/it/Microsoft.CSharp.xml", "ref/netcore50/ja/Microsoft.CSharp.xml", "ref/netcore50/ko/Microsoft.CSharp.xml", "ref/netcore50/ru/Microsoft.CSharp.xml", "ref/netcore50/zh-hans/Microsoft.CSharp.xml", "ref/netcore50/zh-hant/Microsoft.CSharp.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/Microsoft.CSharp.dll", "ref/netstandard1.0/Microsoft.CSharp.xml", "ref/netstandard1.0/de/Microsoft.CSharp.xml", "ref/netstandard1.0/es/Microsoft.CSharp.xml", "ref/netstandard1.0/fr/Microsoft.CSharp.xml", "ref/netstandard1.0/it/Microsoft.CSharp.xml", "ref/netstandard1.0/ja/Microsoft.CSharp.xml", "ref/netstandard1.0/ko/Microsoft.CSharp.xml", "ref/netstandard1.0/ru/Microsoft.CSharp.xml", "ref/netstandard1.0/zh-hans/Microsoft.CSharp.xml", "ref/netstandard1.0/zh-hant/Microsoft.CSharp.xml", "ref/netstandard2.0/Microsoft.CSharp.dll", "ref/netstandard2.0/Microsoft.CSharp.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/uap10.0.16299/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Data.SqlClient/3.0.0": {"sha512": "MUauWfCLsZQQMUR/wZhec5MH6+NTPmPp9i/OsjIMmIu2ICYUGOVm1x7RTqKxq19UWxXMSG03/O0FyXQJrpDs9A==", "type": "package", "path": "microsoft.data.sqlclient/3.0.0", "files": [".nupkg.metadata", ".signature.p7s", "dotnet.png", "lib/net461/Microsoft.Data.SqlClient.dll", "lib/net461/Microsoft.Data.SqlClient.pdb", "lib/net461/Microsoft.Data.SqlClient.xml", "lib/net461/de/Microsoft.Data.SqlClient.resources.dll", "lib/net461/es/Microsoft.Data.SqlClient.resources.dll", "lib/net461/fr/Microsoft.Data.SqlClient.resources.dll", "lib/net461/it/Microsoft.Data.SqlClient.resources.dll", "lib/net461/ja/Microsoft.Data.SqlClient.resources.dll", "lib/net461/ko/Microsoft.Data.SqlClient.resources.dll", "lib/net461/pt-BR/Microsoft.Data.SqlClient.resources.dll", "lib/net461/ru/Microsoft.Data.SqlClient.resources.dll", "lib/net461/zh-<PERSON>/Microsoft.Data.SqlClient.resources.dll", "lib/net461/zh-Hant/Microsoft.Data.SqlClient.resources.dll", "lib/netcoreapp2.1/Microsoft.Data.SqlClient.dll", "lib/netcoreapp2.1/Microsoft.Data.SqlClient.pdb", "lib/netcoreapp2.1/Microsoft.Data.SqlClient.xml", "lib/netcoreapp3.1/Microsoft.Data.SqlClient.dll", "lib/netcoreapp3.1/Microsoft.Data.SqlClient.pdb", "lib/netcoreapp3.1/Microsoft.Data.SqlClient.xml", "lib/netstandard2.0/Microsoft.Data.SqlClient.dll", "lib/netstandard2.0/Microsoft.Data.SqlClient.pdb", "lib/netstandard2.0/Microsoft.Data.SqlClient.xml", "lib/netstandard2.1/Microsoft.Data.SqlClient.dll", "lib/netstandard2.1/Microsoft.Data.SqlClient.pdb", "lib/netstandard2.1/Microsoft.Data.SqlClient.xml", "microsoft.data.sqlclient.3.0.0.nupkg.sha512", "microsoft.data.sqlclient.nuspec", "ref/net461/Microsoft.Data.SqlClient.dll", "ref/net461/Microsoft.Data.SqlClient.pdb", "ref/net461/Microsoft.Data.SqlClient.xml", "ref/netcoreapp2.1/Microsoft.Data.SqlClient.dll", "ref/netcoreapp2.1/Microsoft.Data.SqlClient.pdb", "ref/netcoreapp2.1/Microsoft.Data.SqlClient.xml", "ref/netcoreapp3.1/Microsoft.Data.SqlClient.dll", "ref/netcoreapp3.1/Microsoft.Data.SqlClient.pdb", "ref/netcoreapp3.1/Microsoft.Data.SqlClient.xml", "ref/netstandard2.0/Microsoft.Data.SqlClient.dll", "ref/netstandard2.0/Microsoft.Data.SqlClient.pdb", "ref/netstandard2.0/Microsoft.Data.SqlClient.xml", "ref/netstandard2.1/Microsoft.Data.SqlClient.dll", "ref/netstandard2.1/Microsoft.Data.SqlClient.pdb", "ref/netstandard2.1/Microsoft.Data.SqlClient.xml", "runtimes/unix/lib/netcoreapp2.1/Microsoft.Data.SqlClient.dll", "runtimes/unix/lib/netcoreapp2.1/Microsoft.Data.SqlClient.pdb", "runtimes/unix/lib/netcoreapp3.1/Microsoft.Data.SqlClient.dll", "runtimes/unix/lib/netcoreapp3.1/Microsoft.Data.SqlClient.pdb", "runtimes/unix/lib/netstandard2.0/Microsoft.Data.SqlClient.dll", "runtimes/unix/lib/netstandard2.0/Microsoft.Data.SqlClient.pdb", "runtimes/unix/lib/netstandard2.1/Microsoft.Data.SqlClient.dll", "runtimes/unix/lib/netstandard2.1/Microsoft.Data.SqlClient.pdb", "runtimes/win/lib/net461/Microsoft.Data.SqlClient.dll", "runtimes/win/lib/net461/Microsoft.Data.SqlClient.pdb", "runtimes/win/lib/netcoreapp2.1/Microsoft.Data.SqlClient.dll", "runtimes/win/lib/netcoreapp2.1/Microsoft.Data.SqlClient.pdb", "runtimes/win/lib/netcoreapp3.1/Microsoft.Data.SqlClient.dll", "runtimes/win/lib/netcoreapp3.1/Microsoft.Data.SqlClient.pdb", "runtimes/win/lib/netstandard2.0/Microsoft.Data.SqlClient.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Data.SqlClient.pdb", "runtimes/win/lib/netstandard2.1/Microsoft.Data.SqlClient.dll", "runtimes/win/lib/netstandard2.1/Microsoft.Data.SqlClient.pdb"]}, "Microsoft.Data.SqlClient.SNI.runtime/3.0.0": {"sha512": "n1sNyjJgu2pYWKgw3ZPikw3NiRvG4kt7Ya5MK8u77Rgj/1bTFqO/eDF4k5W9H5GXplMZCpKkNbp5kNBICgSB0w==", "type": "package", "path": "microsoft.data.sqlclient.sni.runtime/3.0.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "dotnet.png", "microsoft.data.sqlclient.sni.runtime.3.0.0.nupkg.sha512", "microsoft.data.sqlclient.sni.runtime.nuspec", "runtimes/win-arm/native/Microsoft.Data.SqlClient.SNI.dll", "runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll", "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll", "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll"]}, "Microsoft.EntityFrameworkCore/5.0.8": {"sha512": "UzHZDWma5jJaDMBGnfsBC1h0+1bPzAZmEGnmEMy9xdn4UkO9yvrG7m0deRazvU98Ezoa9pqi4Bsp07WR8p8HUA==", "type": "package", "path": "microsoft.entityframeworkcore/5.0.8", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/netstandard2.1/Microsoft.EntityFrameworkCore.dll", "lib/netstandard2.1/Microsoft.EntityFrameworkCore.xml", "microsoft.entityframeworkcore.5.0.8.nupkg.sha512", "microsoft.entityframeworkcore.nuspec"]}, "Microsoft.EntityFrameworkCore.Abstractions/5.0.8": {"sha512": "twdaHC6Jy3Gzcr7qHBvgQi0q1mWJVJtrqkFcw21IFE/+1KkMTGOamV88vgPZY1NBZqnbdv1K83otDsUg9EO5ow==", "type": "package", "path": "microsoft.entityframeworkcore.abstractions/5.0.8", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/netstandard2.1/Microsoft.EntityFrameworkCore.Abstractions.dll", "lib/netstandard2.1/Microsoft.EntityFrameworkCore.Abstractions.xml", "microsoft.entityframeworkcore.abstractions.5.0.8.nupkg.sha512", "microsoft.entityframeworkcore.abstractions.nuspec"]}, "Microsoft.EntityFrameworkCore.Analyzers/5.0.8": {"sha512": "iXH3FyGochg9ZePG1VQwVT3YGsvhn8jmQkhi7iIKfa16xgSevpa9jhKDflFSMZ9xI9xWAsOAiG+H+pG6Jsw/3w==", "type": "package", "path": "microsoft.entityframeworkcore.analyzers/5.0.8", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "analyzers/dotnet/cs/Microsoft.EntityFrameworkCore.Analyzers.dll", "lib/netstandard2.0/_._", "microsoft.entityframeworkcore.analyzers.5.0.8.nupkg.sha512", "microsoft.entityframeworkcore.analyzers.nuspec"]}, "Microsoft.EntityFrameworkCore.InMemory/5.0.8": {"sha512": "LV9/hBseBO3kZKa/MPlWfLvfKL64Q7y2ZW2euW+kcNhhAQWsl52JIQHSL1Gu3CnzfQVbGUDkusnN4OWh6k/ALw==", "type": "package", "path": "microsoft.entityframeworkcore.inmemory/5.0.8", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/netstandard2.1/Microsoft.EntityFrameworkCore.InMemory.dll", "lib/netstandard2.1/Microsoft.EntityFrameworkCore.InMemory.xml", "microsoft.entityframeworkcore.inmemory.5.0.8.nupkg.sha512", "microsoft.entityframeworkcore.inmemory.nuspec"]}, "Microsoft.EntityFrameworkCore.Proxies/5.0.8": {"sha512": "I4te83hQbgRVbG/0gyo2kpr93cJeGZTZTmg2z4PaAKDpFGlWpSUU9OKPRZcKggN4+15aQLF8oLZ8rKuThbJrLA==", "type": "package", "path": "microsoft.entityframeworkcore.proxies/5.0.8", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/netstandard2.1/Microsoft.EntityFrameworkCore.Proxies.dll", "lib/netstandard2.1/Microsoft.EntityFrameworkCore.Proxies.xml", "microsoft.entityframeworkcore.proxies.5.0.8.nupkg.sha512", "microsoft.entityframeworkcore.proxies.nuspec"]}, "Microsoft.EntityFrameworkCore.Relational/5.0.8": {"sha512": "TGXfQdij08bjxpXNzC/LxOf1Q18eLdACZZpWUCHcVKssk6750ElE0MhX3eBmvRnSof1FrjsWJkYEchV6PSjGOg==", "type": "package", "path": "microsoft.entityframeworkcore.relational/5.0.8", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/netstandard2.1/Microsoft.EntityFrameworkCore.Relational.dll", "lib/netstandard2.1/Microsoft.EntityFrameworkCore.Relational.xml", "microsoft.entityframeworkcore.relational.5.0.8.nupkg.sha512", "microsoft.entityframeworkcore.relational.nuspec"]}, "Microsoft.EntityFrameworkCore.SqlServer/5.0.8": {"sha512": "kQbUHiJDqIm+LdY7SS/fm/W/eHCXUJjAJhMvTzSGtPuFGBN4S+RKfs8FiXXk0WKWxmDjXo4Dck4WDJLCXdv/Sw==", "type": "package", "path": "microsoft.entityframeworkcore.sqlserver/5.0.8", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/netstandard2.1/Microsoft.EntityFrameworkCore.SqlServer.dll", "lib/netstandard2.1/Microsoft.EntityFrameworkCore.SqlServer.xml", "microsoft.entityframeworkcore.sqlserver.5.0.8.nupkg.sha512", "microsoft.entityframeworkcore.sqlserver.nuspec"]}, "Microsoft.Extensions.Caching.Abstractions/5.0.0": {"sha512": "bu8As90/SBAouMZ6fJ+qRNo1X+KgHGrVueFhhYi+E5WqEhcnp2HoWRFnMzXQ6g4RdZbvPowFerSbKNH4Dtg5yg==", "type": "package", "path": "microsoft.extensions.caching.abstractions/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net461/Microsoft.Extensions.Caching.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.xml", "microsoft.extensions.caching.abstractions.5.0.0.nupkg.sha512", "microsoft.extensions.caching.abstractions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.Caching.Memory/5.0.0": {"sha512": "/1qPCleFOkJe0O+xmFqCNLFYQZTJz965sVw8CUB/BQgsApBwzAUsL2BUkDvQW+geRUVTXUS9zLa0pBjC2VJ1gA==", "type": "package", "path": "microsoft.extensions.caching.memory/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Caching.Memory.dll", "lib/net461/Microsoft.Extensions.Caching.Memory.xml", "lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll", "lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.xml", "microsoft.extensions.caching.memory.5.0.0.nupkg.sha512", "microsoft.extensions.caching.memory.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.Configuration/5.0.0": {"sha512": "LN322qEKHjuVEhhXueTUe7RNePooZmS8aGid5aK2woX3NPjSnONFyKUc6+JknOS6ce6h2tCLfKPTBXE3mN/6Ag==", "type": "package", "path": "microsoft.extensions.configuration/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Configuration.dll", "lib/net461/Microsoft.Extensions.Configuration.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.xml", "microsoft.extensions.configuration.5.0.0.nupkg.sha512", "microsoft.extensions.configuration.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.Configuration.Abstractions/5.0.0": {"sha512": "ETjSBHMp3OAZ4HxGQYpwyGsD8Sw5FegQXphi0rpoGMT74S4+I2mm7XJEswwn59XAaKOzC15oDSOWEE8SzDCd6Q==", "type": "package", "path": "microsoft.extensions.configuration.abstractions/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net461/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.xml", "microsoft.extensions.configuration.abstractions.5.0.0.nupkg.sha512", "microsoft.extensions.configuration.abstractions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.Configuration.Binder/5.0.0": {"sha512": "Of1Irt1+NzWO+yEYkuDh5TpT4On7LKl98Q9iLqCdOZps6XXEWDj3AKtmyvzJPVXZe4apmkJJIiDL7rR1yC+hjQ==", "type": "package", "path": "microsoft.extensions.configuration.binder/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Configuration.Binder.dll", "lib/net461/Microsoft.Extensions.Configuration.Binder.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.xml", "microsoft.extensions.configuration.binder.5.0.0.nupkg.sha512", "microsoft.extensions.configuration.binder.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.Configuration.EnvironmentVariables/5.0.0": {"sha512": "fqh6y6hAi0Z0fRsb4B/mP9OkKkSlifh5osa+N/YSQ+/S2a//+zYApZMUC1XeP9fdjlgZoPQoZ72Q2eLHyKLddQ==", "type": "package", "path": "microsoft.extensions.configuration.environmentvariables/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/net461/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "microsoft.extensions.configuration.environmentvariables.5.0.0.nupkg.sha512", "microsoft.extensions.configuration.environmentvariables.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.Configuration.FileExtensions/5.0.0": {"sha512": "rRdspYKA18ViPOISwAihhCMbusHsARCOtDMwa23f+BGEdIjpKPlhs3LLjmKlxfhpGXBjIsS0JpXcChjRUN+PAw==", "type": "package", "path": "microsoft.extensions.configuration.fileextensions/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net461/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "microsoft.extensions.configuration.fileextensions.5.0.0.nupkg.sha512", "microsoft.extensions.configuration.fileextensions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.Configuration.Json/5.0.0": {"sha512": "Pak8ymSUfdzPfBTLHxeOwcR32YDbuVfhnH2hkfOLnJNQd19ItlBdpMjIDY9C5O/nS2Sn9bzDMai0ZrvF7KyY/Q==", "type": "package", "path": "microsoft.extensions.configuration.json/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Configuration.Json.dll", "lib/net461/Microsoft.Extensions.Configuration.Json.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.xml", "lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.dll", "lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.xml", "microsoft.extensions.configuration.json.5.0.0.nupkg.sha512", "microsoft.extensions.configuration.json.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.DependencyInjection/5.0.2": {"sha512": "xzFW00AZEvOXM1OX+0+AYH5op/Hf3u//e6wszBd/rK72sypD+jx5CtsHxM4BVuFBEs8SajfO4QzSJtrQaHDr4A==", "type": "package", "path": "microsoft.extensions.dependencyinjection/5.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.DependencyInjection.dll", "lib/net461/Microsoft.Extensions.DependencyInjection.xml", "lib/net5.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net5.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.xml", "microsoft.extensions.dependencyinjection.5.0.2.nupkg.sha512", "microsoft.extensions.dependencyinjection.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/5.0.0": {"sha512": "ORj7Zh81gC69TyvmcUm9tSzytcy8AVousi+IVRAI8nLieQjOFryRusSFh7+aLk16FN9pQNqJAiMd7BTKINK0kA==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net461/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.5.0.0.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.FileProviders.Abstractions/5.0.0": {"sha512": "iuZIiZ3mteEb+nsUqpGXKx2cGF+cv6gWPd5jqQI4hzqdiJ6I94ddLjKhQOuRW1lueHwocIw30xbSHGhQj0zjdQ==", "type": "package", "path": "microsoft.extensions.fileproviders.abstractions/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net461/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "microsoft.extensions.fileproviders.abstractions.5.0.0.nupkg.sha512", "microsoft.extensions.fileproviders.abstractions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.FileProviders.Physical/5.0.0": {"sha512": "1rkd8UO2qf21biwO7X0hL9uHP7vtfmdv/NLvKgCRHkdz1XnW8zVQJXyEYiN68WYpExgtVWn55QF0qBzgfh1mGg==", "type": "package", "path": "microsoft.extensions.fileproviders.physical/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net461/Microsoft.Extensions.FileProviders.Physical.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.xml", "microsoft.extensions.fileproviders.physical.5.0.0.nupkg.sha512", "microsoft.extensions.fileproviders.physical.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.FileSystemGlobbing/5.0.0": {"sha512": "ArliS8lGk8sWRtrWpqI8yUVYJpRruPjCDT+EIjrgkA/AAPRctlAkRISVZ334chAKktTLzD1+PK8F5IZpGedSqA==", "type": "package", "path": "microsoft.extensions.filesystemglobbing/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net461/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.xml", "microsoft.extensions.filesystemglobbing.5.0.0.nupkg.sha512", "microsoft.extensions.filesystemglobbing.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.Hosting.Abstractions/2.2.0": {"sha512": "+k4AEn68HOJat5gj1TWa6X28WlirNQO9sPIIeQbia+91n03esEtMSSoekSTpMjUzjqtJWQN3McVx0GvSPFHF/Q==", "type": "package", "path": "microsoft.extensions.hosting.abstractions/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.xml", "microsoft.extensions.hosting.abstractions.2.2.0.nupkg.sha512", "microsoft.extensions.hosting.abstractions.nuspec"]}, "Microsoft.Extensions.Logging/5.0.0": {"sha512": "MgOwK6tPzB6YNH21wssJcw/2MKwee8b2gI7SllYfn6rvTpIrVvVS5HAjSU2vqSku1fwqRvWP0MdIi14qjd93Aw==", "type": "package", "path": "microsoft.extensions.logging/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Logging.dll", "lib/net461/Microsoft.Extensions.Logging.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.1/Microsoft.Extensions.Logging.dll", "lib/netstandard2.1/Microsoft.Extensions.Logging.xml", "microsoft.extensions.logging.5.0.0.nupkg.sha512", "microsoft.extensions.logging.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.Logging.Abstractions/5.0.0": {"sha512": "NxP6ahFcBnnSfwNBi2KH2Oz8Xl5Sm2krjId/jRR3I7teFphwiUoUeZPwTNA21EX+5PtjqmyAvKaOeBXcJjcH/w==", "type": "package", "path": "microsoft.extensions.logging.abstractions/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net461/Microsoft.Extensions.Logging.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml", "microsoft.extensions.logging.abstractions.5.0.0.nupkg.sha512", "microsoft.extensions.logging.abstractions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.Logging.ApplicationInsights/2.17.0": {"sha512": "no6gCIpGCdTtdrvcXoL2tU1BaiG+Z+VM73feCp1hfNIBQ0lDrblxCWS3ReLBgj/WhofUQHpl3qwMXtINaiUycg==", "type": "package", "path": "microsoft.extensions.logging.applicationinsights/2.17.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/netstandard2.0/Microsoft.Extensions.Logging.ApplicationInsights.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.ApplicationInsights.pdb", "lib/netstandard2.0/Microsoft.Extensions.Logging.ApplicationInsights.xml", "microsoft.extensions.logging.applicationinsights.2.17.0.nupkg.sha512", "microsoft.extensions.logging.applicationinsights.nuspec"]}, "Microsoft.Extensions.Logging.AzureAppServices/5.0.8": {"sha512": "1UBubx6t6F4VhiUVO8dp9QI/IWLm8euf0fkOCopgO5kMQpA4tBdjATjF2S37w3/QCsGDUbcVmBXRmCJnpJNvbg==", "type": "package", "path": "microsoft.extensions.logging.azureappservices/5.0.8", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Logging.AzureAppServices.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.AzureAppServices.dll", "microsoft.extensions.logging.azureappservices.5.0.8.nupkg.sha512", "microsoft.extensions.logging.azureappservices.nuspec"]}, "Microsoft.Extensions.Logging.Configuration/5.0.0": {"sha512": "N3/d0HeMRnBekadbZlmbp+In8EvNNkQHSdbtRzjrGVckdZWpYs5GNrAfaYqVplDFW0WUedSaFJ3khB50BWYGsw==", "type": "package", "path": "microsoft.extensions.logging.configuration/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Logging.Configuration.dll", "lib/net461/Microsoft.Extensions.Logging.Configuration.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Configuration.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Configuration.xml", "microsoft.extensions.logging.configuration.5.0.0.nupkg.sha512", "microsoft.extensions.logging.configuration.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.ObjectPool/2.2.0": {"sha512": "gA8H7uQOnM5gb+L0uTNjViHYr+hRDqCdfugheGo/MxQnuHzmhhzCBTIPm19qL1z1Xe0NEMabfcOBGv9QghlZ8g==", "type": "package", "path": "microsoft.extensions.objectpool/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.Extensions.ObjectPool.dll", "lib/netstandard2.0/Microsoft.Extensions.ObjectPool.xml", "microsoft.extensions.objectpool.2.2.0.nupkg.sha512", "microsoft.extensions.objectpool.nuspec"]}, "Microsoft.Extensions.Options/5.0.0": {"sha512": "CBvR92TCJ5uBIdd9/HzDSrxYak+0W/3+yxrNg8Qm6Bmrkh5L+nu6m3WeazQehcZ5q1/6dDA7J5YdQjim0165zg==", "type": "package", "path": "microsoft.extensions.options/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Options.dll", "lib/net461/Microsoft.Extensions.Options.xml", "lib/net5.0/Microsoft.Extensions.Options.dll", "lib/net5.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.xml", "microsoft.extensions.options.5.0.0.nupkg.sha512", "microsoft.extensions.options.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.Options.ConfigurationExtensions/5.0.0": {"sha512": "280RxNJqOeQqq47aJLy5D9LN61CAWeuRA83gPToQ8B9jl9SNdQ5EXjlfvF66zQI5AXMl+C/3hGnbtIEN+X3mqA==", "type": "package", "path": "microsoft.extensions.options.configurationextensions/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net461/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "microsoft.extensions.options.configurationextensions.5.0.0.nupkg.sha512", "microsoft.extensions.options.configurationextensions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.Primitives/5.0.0": {"sha512": "cI/VWn9G1fghXrNDagX9nYaaB/nokkZn0HYAawGaELQrl8InSezfe9OnfPZLcJq3esXxygh3hkq2c3qoV3SDyQ==", "type": "package", "path": "microsoft.extensions.primitives/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Primitives.dll", "lib/net461/Microsoft.Extensions.Primitives.xml", "lib/netcoreapp3.0/Microsoft.Extensions.Primitives.dll", "lib/netcoreapp3.0/Microsoft.Extensions.Primitives.xml", "lib/netstandard2.0/Microsoft.Extensions.Primitives.dll", "lib/netstandard2.0/Microsoft.Extensions.Primitives.xml", "microsoft.extensions.primitives.5.0.0.nupkg.sha512", "microsoft.extensions.primitives.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.WebEncoders/2.2.0": {"sha512": "V8XcqYcpcdBAxUhLeyYcuKmxu4CtNQA9IphTnARpQGhkop4A93v2XgM3AtaVVJo3H2cDWxWM6aeO8HxkifREqw==", "type": "package", "path": "microsoft.extensions.webencoders/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.Extensions.WebEncoders.dll", "lib/netstandard2.0/Microsoft.Extensions.WebEncoders.xml", "microsoft.extensions.webencoders.2.2.0.nupkg.sha512", "microsoft.extensions.webencoders.nuspec"]}, "Microsoft.Graph/3.35.0": {"sha512": "pQhtA6wX2xHOL+4i0GuzhX2H+D/d/6ofQISjrUu4urCimTk2n51LfVzymV16OiCUfglGni1Km2l6djFziAso3A==", "type": "package", "path": "microsoft.graph/3.35.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "lib/net461/Microsoft.Graph.dll", "lib/net461/Microsoft.Graph.xml", "lib/netstandard2.0/Microsoft.Graph.dll", "lib/netstandard2.0/Microsoft.Graph.xml", "microsoft.graph.3.35.0.nupkg.sha512", "microsoft.graph.nuspec"]}, "Microsoft.Graph.Core/1.25.1": {"sha512": "K06ws9CP5oUi5tFSrxYqyWXwnzjYogo/tqe79+9Ns3IV4CD/bMZumVfTvs9SzwcnkCilea24RWm9RLIW9dYl7Q==", "type": "package", "path": "microsoft.graph.core/1.25.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "lib/monoandroid70/Microsoft.Graph.Core.dll", "lib/monoandroid70/Microsoft.Graph.Core.xml", "lib/net45/Microsoft.Graph.Core.dll", "lib/net45/Microsoft.Graph.Core.xml", "lib/netstandard1.1/Microsoft.Graph.Core.dll", "lib/netstandard1.1/Microsoft.Graph.Core.xml", "lib/xamarinios10/Microsoft.Graph.Core.dll", "lib/xamarinios10/Microsoft.Graph.Core.xml", "lib/xamarinmac20/Microsoft.Graph.Core.dll", "lib/xamarinmac20/Microsoft.Graph.Core.xml", "microsoft.graph.core.1.25.1.nupkg.sha512", "microsoft.graph.core.nuspec"]}, "Microsoft.Identity.Client/4.33.0": {"sha512": "6ScMUJapsQff2lrtbyA13pPfySScJUCWTm/5Xtm5J1En48JbtXqDqZMfBPr2Yrq/OF2gVwtguOUXDkl+keXumg==", "type": "package", "path": "microsoft.identity.client/4.33.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/monoandroid10.0/Microsoft.Identity.Client.dll", "lib/monoandroid10.0/Microsoft.Identity.Client.xml", "lib/monoandroid90/Microsoft.Identity.Client.dll", "lib/monoandroid90/Microsoft.Identity.Client.xml", "lib/net45/Microsoft.Identity.Client.dll", "lib/net45/Microsoft.Identity.Client.xml", "lib/net461/Microsoft.Identity.Client.dll", "lib/net461/Microsoft.Identity.Client.xml", "lib/net5.0-windows10.0.17763/Microsoft.Identity.Client.dll", "lib/net5.0-windows10.0.17763/Microsoft.Identity.Client.xml", "lib/netcoreapp2.1/Microsoft.Identity.Client.dll", "lib/netcoreapp2.1/Microsoft.Identity.Client.xml", "lib/netstandard1.3/Microsoft.Identity.Client.dll", "lib/netstandard1.3/Microsoft.Identity.Client.xml", "lib/uap10.0/Microsoft.Identity.Client.dll", "lib/uap10.0/Microsoft.Identity.Client.pri", "lib/uap10.0/Microsoft.Identity.Client.xml", "lib/xamarinios10/Microsoft.Identity.Client.dll", "lib/xamarinios10/Microsoft.Identity.Client.xml", "lib/xamarinmac20/Microsoft.Identity.Client.dll", "lib/xamarinmac20/Microsoft.Identity.Client.xml", "microsoft.identity.client.4.33.0.nupkg.sha512", "microsoft.identity.client.nuspec"]}, "Microsoft.Identity.Client.Extensions.Msal/2.16.5": {"sha512": "VlGUZEpF8KP/GCfFI59sdE0WA0o9quqwM1YQY0dSp6jpGy5EOBkureaybLfpwCuYUUjQbLkN2p7neUIcQCfbzA==", "type": "package", "path": "microsoft.identity.client.extensions.msal/2.16.5", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.Identity.Client.Extensions.Msal.dll", "lib/net45/Microsoft.Identity.Client.Extensions.Msal.xml", "lib/netcoreapp2.1/Microsoft.Identity.Client.Extensions.Msal.dll", "lib/netcoreapp2.1/Microsoft.Identity.Client.Extensions.Msal.xml", "lib/netstandard2.0/Microsoft.Identity.Client.Extensions.Msal.dll", "lib/netstandard2.0/Microsoft.Identity.Client.Extensions.Msal.xml", "microsoft.identity.client.extensions.msal.2.16.5.nupkg.sha512", "microsoft.identity.client.extensions.msal.nuspec"]}, "Microsoft.Identity.Web/1.14.0": {"sha512": "rGQAnKo4R+HAehpouj0KQr9V4kuY3JCqwAP42Y4QsH499CsW0kfEW2L478Hyr3cTWXJPxZnWfmFKWPNgzAShgQ==", "type": "package", "path": "microsoft.identity.web/1.14.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "lib/net462/Microsoft.Identity.Web.dll", "lib/net462/Microsoft.Identity.Web.xml", "lib/net472/Microsoft.Identity.Web.dll", "lib/net472/Microsoft.Identity.Web.xml", "lib/net5.0/Microsoft.Identity.Web.dll", "lib/net5.0/Microsoft.Identity.Web.xml", "lib/netcoreapp3.1/Microsoft.Identity.Web.dll", "lib/netcoreapp3.1/Microsoft.Identity.Web.xml", "microsoft.identity.web.1.14.0.nupkg.sha512", "microsoft.identity.web.nuspec"]}, "Microsoft.Identity.Web.MicrosoftGraph/1.14.0": {"sha512": "Kk58GLlD4DEDLCQfXtBdUQ7hTTLGQaoLeTPn6Gbt+8ASO5Op9okm59bX3mTP0aI6QaUirzXgzjsEOA6sHqOoCg==", "type": "package", "path": "microsoft.identity.web.microsoftgraph/1.14.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "lib/net5.0/Microsoft.Identity.Web.MicrosoftGraph.dll", "lib/net5.0/Microsoft.Identity.Web.MicrosoftGraph.xml", "lib/netcoreapp3.1/Microsoft.Identity.Web.MicrosoftGraph.dll", "lib/netcoreapp3.1/Microsoft.Identity.Web.MicrosoftGraph.xml", "microsoft.identity.web.microsoftgraph.1.14.0.nupkg.sha512", "microsoft.identity.web.microsoftgraph.nuspec"]}, "Microsoft.IdentityModel.Clients.ActiveDirectory/5.2.9": {"sha512": "WhBAG/9hWiMHIXve4ZgwXP3spRwf7kFFfejf76QA5BvumgnPp8iDkDCiJugzAcpW1YaHB526z1UVxHhVT1E5qw==", "type": "package", "path": "microsoft.identitymodel.clients.activedirectory/5.2.9", "files": [".nupkg.metadata", ".signature.p7s", "lib/monoandroid81/Microsoft.IdentityModel.Clients.ActiveDirectory.dll", "lib/monoandroid81/Microsoft.IdentityModel.Clients.ActiveDirectory.xml", "lib/net45/Microsoft.IdentityModel.Clients.ActiveDirectory.dll", "lib/net45/Microsoft.IdentityModel.Clients.ActiveDirectory.xml", "lib/netstandard1.3/Microsoft.IdentityModel.Clients.ActiveDirectory.dll", "lib/netstandard1.3/Microsoft.IdentityModel.Clients.ActiveDirectory.xml", "lib/uap10.0/Microsoft.IdentityModel.Clients.ActiveDirectory.dll", "lib/uap10.0/Microsoft.IdentityModel.Clients.ActiveDirectory.pri", "lib/uap10.0/Microsoft.IdentityModel.Clients.ActiveDirectory.xml", "lib/xamarinios10/Microsoft.IdentityModel.Clients.ActiveDirectory.dll", "lib/xamarinios10/Microsoft.IdentityModel.Clients.ActiveDirectory.xml", "microsoft.identitymodel.clients.activedirectory.5.2.9.nupkg.sha512", "microsoft.identitymodel.clients.activedirectory.nuspec"]}, "Microsoft.IdentityModel.JsonWebTokens/6.11.1": {"sha512": "WviaeOAlwxxDXMBj9GN4dKvKXHqEPqN7LtQdwjMdNvd/NR1ry43BpqIMk4MyHBhgwBCcXlq8079CzHhGxb3Prw==", "type": "package", "path": "microsoft.identitymodel.jsonwebtokens/6.11.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net45/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net461/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net461/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net472/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net472/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.xml", "microsoft.identitymodel.jsonwebtokens.6.11.1.nupkg.sha512", "microsoft.identitymodel.jsonwebtokens.nuspec"]}, "Microsoft.IdentityModel.Logging/6.11.1": {"sha512": "8G/kXAoKVCvTJyB4Ld8me4oDV/E9uWD+Rk86JBy7nfYpzVsrxvqG5svSlwaIltQFjVD5VOHwq1o+9kMw3Q7tGg==", "type": "package", "path": "microsoft.identitymodel.logging/6.11.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.IdentityModel.Logging.dll", "lib/net45/Microsoft.IdentityModel.Logging.xml", "lib/net461/Microsoft.IdentityModel.Logging.dll", "lib/net461/Microsoft.IdentityModel.Logging.xml", "lib/net472/Microsoft.IdentityModel.Logging.dll", "lib/net472/Microsoft.IdentityModel.Logging.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Logging.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Logging.xml", "microsoft.identitymodel.logging.6.11.1.nupkg.sha512", "microsoft.identitymodel.logging.nuspec"]}, "Microsoft.IdentityModel.Protocols/6.11.1": {"sha512": "2Bu9Y0munbF0cJfRn+Dra3XnnRvLG0UbIV29MrHKVqlfl5Ag5rmyMMFwfcQpp04kDaphgL4yUJY8pAsULNzIdw==", "type": "package", "path": "microsoft.identitymodel.protocols/6.11.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.IdentityModel.Protocols.dll", "lib/net45/Microsoft.IdentityModel.Protocols.xml", "lib/net461/Microsoft.IdentityModel.Protocols.dll", "lib/net461/Microsoft.IdentityModel.Protocols.xml", "lib/net472/Microsoft.IdentityModel.Protocols.dll", "lib/net472/Microsoft.IdentityModel.Protocols.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.xml", "microsoft.identitymodel.protocols.6.11.1.nupkg.sha512", "microsoft.identitymodel.protocols.nuspec"]}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.11.1": {"sha512": "M6A83RWtyABbloksL1RJD9yQw50OLkkyPfsr6MjCPxybaqKcGFD40hjq5CG/ip4IZMOGLf+iSyGu788x07o2tg==", "type": "package", "path": "microsoft.identitymodel.protocols.openidconnect/6.11.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net45/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net461/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net461/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net472/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net472/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "microsoft.identitymodel.protocols.openidconnect.6.11.1.nupkg.sha512", "microsoft.identitymodel.protocols.openidconnect.nuspec"]}, "Microsoft.IdentityModel.Tokens/6.11.1": {"sha512": "B7vWX+WevuluZDjsf4Br2Zb7k20ps1D3RMX/J8DKsrkzJ4F3fcsLIEknI98JNYKPlN9VcqEZ1qeeEKEuwU3XcA==", "type": "package", "path": "microsoft.identitymodel.tokens/6.11.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.IdentityModel.Tokens.dll", "lib/net45/Microsoft.IdentityModel.Tokens.xml", "lib/net461/Microsoft.IdentityModel.Tokens.dll", "lib/net461/Microsoft.IdentityModel.Tokens.xml", "lib/net472/Microsoft.IdentityModel.Tokens.dll", "lib/net472/Microsoft.IdentityModel.Tokens.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Tokens.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Tokens.xml", "microsoft.identitymodel.tokens.6.11.1.nupkg.sha512", "microsoft.identitymodel.tokens.nuspec"]}, "Microsoft.IO.RecyclableMemoryStream/1.4.1": {"sha512": "6A0fyZkxoUUj1dpXzLAWwI89YmKZ+ZSp1DCg+gN6llcXJwfYo1IIQZoCkuo6T7vUxw/w1CSk/Pl04NQ4fno+DQ==", "type": "package", "path": "microsoft.io.recyclablememorystream/1.4.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net40/Microsoft.IO.RecyclableMemoryStream.dll", "lib/net40/Microsoft.IO.RecyclableMemoryStream.xml", "lib/net45/Microsoft.IO.RecyclableMemoryStream.dll", "lib/net45/Microsoft.IO.RecyclableMemoryStream.xml", "lib/net46/Microsoft.IO.RecyclableMemoryStream.dll", "lib/net46/Microsoft.IO.RecyclableMemoryStream.xml", "lib/netcoreapp2.1/Microsoft.IO.RecyclableMemoryStream.dll", "lib/netcoreapp2.1/Microsoft.IO.RecyclableMemoryStream.xml", "lib/netstandard1.4/Microsoft.IO.RecyclableMemoryStream.dll", "lib/netstandard1.4/Microsoft.IO.RecyclableMemoryStream.xml", "lib/netstandard2.0/Microsoft.IO.RecyclableMemoryStream.dll", "lib/netstandard2.0/Microsoft.IO.RecyclableMemoryStream.xml", "lib/netstandard2.1/Microsoft.IO.RecyclableMemoryStream.dll", "lib/netstandard2.1/Microsoft.IO.RecyclableMemoryStream.xml", "microsoft.io.recyclablememorystream.1.4.1.nupkg.sha512", "microsoft.io.recyclablememorystream.nuspec"]}, "Microsoft.Net.Http.Headers/2.2.0": {"sha512": "iZNkjYqlo8sIOI0bQfpsSoMTmB/kyvmV2h225ihyZT33aTp48ZpF6qYnXxzSXmHt8DpBAwBTX+1s1UFLbYfZKg==", "type": "package", "path": "microsoft.net.http.headers/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.Net.Http.Headers.dll", "lib/netstandard2.0/Microsoft.Net.Http.Headers.xml", "microsoft.net.http.headers.2.2.0.nupkg.sha512", "microsoft.net.http.headers.nuspec"]}, "Microsoft.NETCore.Platforms/5.0.0": {"sha512": "VyPlqzH2wavqquTcYpkIIAQ6WdenuKoFN0BdYBbCWsclXacSOHNQn66Gt4z5NBqEYW0FAPm5rlvki9ZiCij5xQ==", "type": "package", "path": "microsoft.netcore.platforms/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard1.0/_._", "microsoft.netcore.platforms.5.0.0.nupkg.sha512", "microsoft.netcore.platforms.nuspec", "runtime.json", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.NETCore.Targets/1.1.3": {"sha512": "3Wrmi0kJDzClwAC+iBdUBpEKmEle8FQNsCs77fkiOIw/9oYA07bL1EZNX0kQ2OMN3xpwvl0vAtOCYY3ndDNlhQ==", "type": "package", "path": "microsoft.netcore.targets/1.1.3", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "microsoft.netcore.targets.1.1.3.nupkg.sha512", "microsoft.netcore.targets.nuspec", "runtime.json"]}, "Microsoft.Win32.Primitives/4.3.0": {"sha512": "9ZQKCWxH7Ijp9BfahvL2Zyf1cJIk8XYLF6Yjzr2yi0b2cOut/HQ31qf1ThHAgCc3WiZMdnWcfJCgN82/0UunxA==", "type": "package", "path": "microsoft.win32.primitives/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/Microsoft.Win32.Primitives.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "microsoft.win32.primitives.4.3.0.nupkg.sha512", "microsoft.win32.primitives.nuspec", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/Microsoft.Win32.Primitives.dll", "ref/netstandard1.3/Microsoft.Win32.Primitives.dll", "ref/netstandard1.3/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/de/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/es/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/fr/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/it/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/ja/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/ko/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/ru/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/zh-hans/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/zh-hant/Microsoft.Win32.Primitives.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "Microsoft.Win32.Registry/4.7.0": {"sha512": "KSrRMb5vNi0CWSGG1++id2ZOs/1QhRqROt+qgbEAdQuGjGrFcl4AOl4/exGPUYz2wUnU42nvJqon1T3U0kPXLA==", "type": "package", "path": "microsoft.win32.registry/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/Microsoft.Win32.Registry.dll", "lib/net461/Microsoft.Win32.Registry.dll", "lib/net461/Microsoft.Win32.Registry.xml", "lib/netstandard1.3/Microsoft.Win32.Registry.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.xml", "microsoft.win32.registry.4.7.0.nupkg.sha512", "microsoft.win32.registry.nuspec", "ref/net46/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.xml", "ref/net472/Microsoft.Win32.Registry.dll", "ref/net472/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/Microsoft.Win32.Registry.dll", "ref/netstandard1.3/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/de/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/es/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/fr/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/it/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ja/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ko/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ru/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hans/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hant/Microsoft.Win32.Registry.xml", "ref/netstandard2.0/Microsoft.Win32.Registry.dll", "ref/netstandard2.0/Microsoft.Win32.Registry.xml", "runtimes/unix/lib/netstandard2.0/Microsoft.Win32.Registry.dll", "runtimes/unix/lib/netstandard2.0/Microsoft.Win32.Registry.xml", "runtimes/win/lib/net46/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net461/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net461/Microsoft.Win32.Registry.xml", "runtimes/win/lib/netstandard1.3/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.xml", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Win32.SystemEvents/5.0.0": {"sha512": "Bh6blKG8VAKvXiLe2L+sEsn62nc1Ij34MrNxepD2OCrS5cpCwQa9MeLyhVQPQ/R4Wlzwuy6wMK8hLb11QPDRsQ==", "type": "package", "path": "microsoft.win32.systemevents/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Win32.SystemEvents.dll", "lib/net461/Microsoft.Win32.SystemEvents.xml", "lib/netstandard2.0/Microsoft.Win32.SystemEvents.dll", "lib/netstandard2.0/Microsoft.Win32.SystemEvents.xml", "microsoft.win32.systemevents.5.0.0.nupkg.sha512", "microsoft.win32.systemevents.nuspec", "ref/net461/Microsoft.Win32.SystemEvents.dll", "ref/net461/Microsoft.Win32.SystemEvents.xml", "ref/netstandard2.0/Microsoft.Win32.SystemEvents.dll", "ref/netstandard2.0/Microsoft.Win32.SystemEvents.xml", "runtimes/win/lib/netcoreapp2.0/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/netcoreapp2.0/Microsoft.Win32.SystemEvents.xml", "runtimes/win/lib/netcoreapp3.0/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/netcoreapp3.0/Microsoft.Win32.SystemEvents.xml", "useSharedDesignerContext.txt", "version.txt"]}, "NETStandard.Library/1.6.1": {"sha512": "WcSp3+vP+yHNgS8EV5J7pZ9IRpeDuARBPN28by8zqff1wJQXm26PVU8L3/fYLBJVU7BtDyqNVWq2KlCVvSSR4A==", "type": "package", "path": "netstandard.library/1.6.1", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "netstandard.library.1.6.1.nupkg.sha512", "netstandard.library.nuspec"]}, "Newtonsoft.Json/12.0.2": {"sha512": "rTK0s2EKlfHsQsH6Yx2smvcTCeyoDNgCW7FEYyV01drPlh2T243PR2DiDXqtC5N4GDm4Ma/lkxfW5a/4793vbA==", "type": "package", "path": "newtonsoft.json/12.0.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "lib/portable-net40+sl5+win8+wp8+wpa81/Newtonsoft.Json.dll", "lib/portable-net40+sl5+win8+wp8+wpa81/Newtonsoft.Json.xml", "lib/portable-net45+win8+wp8+wpa81/Newtonsoft.Json.dll", "lib/portable-net45+win8+wp8+wpa81/Newtonsoft.Json.xml", "newtonsoft.json.12.0.2.nupkg.sha512", "newtonsoft.json.nuspec"]}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"sha512": "7VSGO0URRKoMEAq0Sc9cRz8mb6zbyx/BZDEWhgPdzzpmFhkam3fJ1DAGWFXBI4nGlma+uPKpfuMQP5LXRnOH5g==", "type": "package", "path": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.nuspec", "runtimes/debian.8-x64/native/System.Security.Cryptography.Native.OpenSsl.so"]}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"sha512": "0oAaTAm6e2oVH+/Zttt0cuhGaePQYKII1dY8iaqP7CvOpVKgLybKRFvQjXR2LtxXOXTVPNv14j0ot8uV+HrUmw==", "type": "package", "path": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.nuspec", "runtimes/fedora.23-x64/native/System.Security.Cryptography.Native.OpenSsl.so"]}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"sha512": "G24ibsCNi5Kbz0oXWynBoRgtGvsw5ZSVEWjv13/KiCAM8C6wz9zzcCniMeQFIkJ2tasjo2kXlvlBZhplL51kGg==", "type": "package", "path": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.nuspec", "runtimes/fedora.24-x64/native/System.Security.Cryptography.Native.OpenSsl.so"]}, "runtime.native.System/4.3.0": {"sha512": "c/qWt2LieNZIj1jGnVNsE2Kl23Ya2aSTBuXMD6V7k9KWr6l16Tqdwq+hJScEpWER9753NWC8h96PaVNY5Ld7Jw==", "type": "package", "path": "runtime.native.system/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "runtime.native.system.4.3.0.nupkg.sha512", "runtime.native.system.nuspec"]}, "runtime.native.System.IO.Compression/4.3.0": {"sha512": "INBPonS5QPEgn7naufQFXJEp3zX6L4bwHgJ/ZH78aBTpeNfQMtf7C6VrAFhlq2xxWBveIOWyFzQjJ8XzHMhdOQ==", "type": "package", "path": "runtime.native.system.io.compression/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "runtime.native.system.io.compression.4.3.0.nupkg.sha512", "runtime.native.system.io.compression.nuspec"]}, "runtime.native.System.Net.Http/4.3.0": {"sha512": "ZVuZJqnnegJhd2k/PtAbbIcZ3aZeITq3sj06oKfMBSfphW3HDmk/t4ObvbOk/JA/swGR0LNqMksAh/f7gpTROg==", "type": "package", "path": "runtime.native.system.net.http/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "runtime.native.system.net.http.4.3.0.nupkg.sha512", "runtime.native.system.net.http.nuspec"]}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"sha512": "DloMk88juo0OuOWr56QG7MNchmafTLYWvABy36izkrLI5VledI0rq28KGs1i9wbpeT9NPQrx/wTf8U2vazqQ3Q==", "type": "package", "path": "runtime.native.system.security.cryptography.apple/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512", "runtime.native.system.security.cryptography.apple.nuspec"]}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"sha512": "QR1OwtwehHxSeQvZKXe+iSd+d3XZNkEcuWMFYa2i0aG1l+lR739HPicKMlTbJst3spmeekDVBUS7SeS26s4U/g==", "type": "package", "path": "runtime.native.system.security.cryptography.openssl/4.3.2", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "runtime.native.system.security.cryptography.openssl.nuspec"]}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"sha512": "I+GNKGg2xCHueRd1m9PzeEW7WLbNNLznmTuEi8/vZX71HudUbx1UTwlGkiwMri7JLl8hGaIAWnA/GONhu+LOyQ==", "type": "package", "path": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.nuspec", "runtimes/opensuse.13.2-x64/native/System.Security.Cryptography.Native.OpenSsl.so"]}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"sha512": "1Z3TAq1ytS1IBRtPXJvEUZdVsfWfeNEhBkbiOCGEl9wwAfsjP2lz3ZFDx5tq8p60/EqbS0HItG5piHuB71RjoA==", "type": "package", "path": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.nuspec", "runtimes/opensuse.42.1-x64/native/System.Security.Cryptography.Native.OpenSsl.so"]}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {"sha512": "kVXCuMTrTlxq4XOOMAysuNwsXWpYeboGddNGpIgNSZmv1b6r/s/DPk0fYMB7Q5Qo4bY68o48jt4T4y5BVecbCQ==", "type": "package", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512", "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.nuspec", "runtimes/osx.10.10-x64/native/System.Security.Cryptography.Native.Apple.dylib"]}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"sha512": "6mU/cVmmHtQiDXhnzUImxIcDL48GbTk+TsptXyJA+MIOG9LRjPoAQC/qBFB7X+UNyK86bmvGwC8t+M66wsYC8w==", "type": "package", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.nuspec", "runtimes/osx.10.10-x64/native/System.Security.Cryptography.Native.OpenSsl.dylib"]}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"sha512": "vjwG0GGcTW/PPg6KVud8F9GLWYuAV1rrw1BKAqY0oh4jcUqg15oYF1+qkGR2x2ZHM4DQnWKQ7cJgYbfncz/lYg==", "type": "package", "path": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.nuspec", "runtimes/rhel.7-x64/native/System.Security.Cryptography.Native.OpenSsl.so"]}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"sha512": "7KMFpTkHC/zoExs+PwP8jDCWcrK9H6L7soowT80CUx3e+nxP/AFnq0AQAW5W76z2WYbLAYCRyPfwYFG6zkvQRw==", "type": "package", "path": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.nuspec", "runtimes/ubuntu.14.04-x64/native/System.Security.Cryptography.Native.OpenSsl.so"]}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"sha512": "xrlmRCnKZJLHxyyLIqkZjNXqgxnKdZxfItrPkjI+6pkRo5lHX8YvSZlWrSI5AVwLMi4HbNWP7064hcAWeZKp5w==", "type": "package", "path": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.nuspec", "runtimes/ubuntu.16.04-x64/native/System.Security.Cryptography.Native.OpenSsl.so"]}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"sha512": "leXiwfiIkW7Gmn7cgnNcdtNAU70SjmKW3jxGj1iKHOvdn0zRWsgv/l2OJUO5zdGdiv2VRFnAsxxhDgMzofPdWg==", "type": "package", "path": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.nuspec", "runtimes/ubuntu.16.10-x64/native/System.Security.Cryptography.Native.OpenSsl.so"]}, "System.AppContext/4.3.0": {"sha512": "fKC+rmaLfeIzUhagxY17Q9siv/sPrjjKcfNg1Ic8IlQkZLipo8ljcaZQu4VtI4Jqbzjc2VTjzGLF6WmsRXAEgA==", "type": "package", "path": "system.appcontext/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.AppContext.dll", "lib/net463/System.AppContext.dll", "lib/netcore50/System.AppContext.dll", "lib/netstandard1.6/System.AppContext.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.AppContext.dll", "ref/net463/System.AppContext.dll", "ref/netstandard/_._", "ref/netstandard1.3/System.AppContext.dll", "ref/netstandard1.3/System.AppContext.xml", "ref/netstandard1.3/de/System.AppContext.xml", "ref/netstandard1.3/es/System.AppContext.xml", "ref/netstandard1.3/fr/System.AppContext.xml", "ref/netstandard1.3/it/System.AppContext.xml", "ref/netstandard1.3/ja/System.AppContext.xml", "ref/netstandard1.3/ko/System.AppContext.xml", "ref/netstandard1.3/ru/System.AppContext.xml", "ref/netstandard1.3/zh-hans/System.AppContext.xml", "ref/netstandard1.3/zh-hant/System.AppContext.xml", "ref/netstandard1.6/System.AppContext.dll", "ref/netstandard1.6/System.AppContext.xml", "ref/netstandard1.6/de/System.AppContext.xml", "ref/netstandard1.6/es/System.AppContext.xml", "ref/netstandard1.6/fr/System.AppContext.xml", "ref/netstandard1.6/it/System.AppContext.xml", "ref/netstandard1.6/ja/System.AppContext.xml", "ref/netstandard1.6/ko/System.AppContext.xml", "ref/netstandard1.6/ru/System.AppContext.xml", "ref/netstandard1.6/zh-hans/System.AppContext.xml", "ref/netstandard1.6/zh-hant/System.AppContext.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.AppContext.dll", "system.appcontext.4.3.0.nupkg.sha512", "system.appcontext.nuspec"]}, "System.Buffers/4.5.0": {"sha512": "pL2ChpaRRWI/p4LXyy4RgeWlYF2sgfj/pnVMvBqwNFr5cXg7CXNnWZWxrOONLg8VGdFB8oB+EG2Qw4MLgTOe+A==", "type": "package", "path": "system.buffers/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netcoreapp2.0/_._", "lib/netstandard1.1/System.Buffers.dll", "lib/netstandard1.1/System.Buffers.xml", "lib/netstandard2.0/System.Buffers.dll", "lib/netstandard2.0/System.Buffers.xml", "lib/uap10.0.16299/_._", "ref/net45/System.Buffers.dll", "ref/net45/System.Buffers.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.1/System.Buffers.dll", "ref/netstandard1.1/System.Buffers.xml", "ref/netstandard2.0/System.Buffers.dll", "ref/netstandard2.0/System.Buffers.xml", "ref/uap10.0.16299/_._", "system.buffers.4.5.0.nupkg.sha512", "system.buffers.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Collections/4.3.0": {"sha512": "3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "type": "package", "path": "system.collections/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Collections.dll", "ref/netcore50/System.Collections.xml", "ref/netcore50/de/System.Collections.xml", "ref/netcore50/es/System.Collections.xml", "ref/netcore50/fr/System.Collections.xml", "ref/netcore50/it/System.Collections.xml", "ref/netcore50/ja/System.Collections.xml", "ref/netcore50/ko/System.Collections.xml", "ref/netcore50/ru/System.Collections.xml", "ref/netcore50/zh-hans/System.Collections.xml", "ref/netcore50/zh-hant/System.Collections.xml", "ref/netstandard1.0/System.Collections.dll", "ref/netstandard1.0/System.Collections.xml", "ref/netstandard1.0/de/System.Collections.xml", "ref/netstandard1.0/es/System.Collections.xml", "ref/netstandard1.0/fr/System.Collections.xml", "ref/netstandard1.0/it/System.Collections.xml", "ref/netstandard1.0/ja/System.Collections.xml", "ref/netstandard1.0/ko/System.Collections.xml", "ref/netstandard1.0/ru/System.Collections.xml", "ref/netstandard1.0/zh-hans/System.Collections.xml", "ref/netstandard1.0/zh-hant/System.Collections.xml", "ref/netstandard1.3/System.Collections.dll", "ref/netstandard1.3/System.Collections.xml", "ref/netstandard1.3/de/System.Collections.xml", "ref/netstandard1.3/es/System.Collections.xml", "ref/netstandard1.3/fr/System.Collections.xml", "ref/netstandard1.3/it/System.Collections.xml", "ref/netstandard1.3/ja/System.Collections.xml", "ref/netstandard1.3/ko/System.Collections.xml", "ref/netstandard1.3/ru/System.Collections.xml", "ref/netstandard1.3/zh-hans/System.Collections.xml", "ref/netstandard1.3/zh-hant/System.Collections.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.collections.4.3.0.nupkg.sha512", "system.collections.nuspec"]}, "System.Collections.Concurrent/4.3.0": {"sha512": "ztl69Xp0Y/UXCL+3v3tEU+lIy+bvjKNUmopn1wep/a291pVPK7dxBd6T7WnlQqRog+d1a/hSsgRsmFnIBKTPLQ==", "type": "package", "path": "system.collections.concurrent/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Collections.Concurrent.dll", "lib/netstandard1.3/System.Collections.Concurrent.dll", "lib/portable-net45+win8+wpa81/_._", "lib/win8/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Collections.Concurrent.dll", "ref/netcore50/System.Collections.Concurrent.xml", "ref/netcore50/de/System.Collections.Concurrent.xml", "ref/netcore50/es/System.Collections.Concurrent.xml", "ref/netcore50/fr/System.Collections.Concurrent.xml", "ref/netcore50/it/System.Collections.Concurrent.xml", "ref/netcore50/ja/System.Collections.Concurrent.xml", "ref/netcore50/ko/System.Collections.Concurrent.xml", "ref/netcore50/ru/System.Collections.Concurrent.xml", "ref/netcore50/zh-hans/System.Collections.Concurrent.xml", "ref/netcore50/zh-hant/System.Collections.Concurrent.xml", "ref/netstandard1.1/System.Collections.Concurrent.dll", "ref/netstandard1.1/System.Collections.Concurrent.xml", "ref/netstandard1.1/de/System.Collections.Concurrent.xml", "ref/netstandard1.1/es/System.Collections.Concurrent.xml", "ref/netstandard1.1/fr/System.Collections.Concurrent.xml", "ref/netstandard1.1/it/System.Collections.Concurrent.xml", "ref/netstandard1.1/ja/System.Collections.Concurrent.xml", "ref/netstandard1.1/ko/System.Collections.Concurrent.xml", "ref/netstandard1.1/ru/System.Collections.Concurrent.xml", "ref/netstandard1.1/zh-hans/System.Collections.Concurrent.xml", "ref/netstandard1.1/zh-hant/System.Collections.Concurrent.xml", "ref/netstandard1.3/System.Collections.Concurrent.dll", "ref/netstandard1.3/System.Collections.Concurrent.xml", "ref/netstandard1.3/de/System.Collections.Concurrent.xml", "ref/netstandard1.3/es/System.Collections.Concurrent.xml", "ref/netstandard1.3/fr/System.Collections.Concurrent.xml", "ref/netstandard1.3/it/System.Collections.Concurrent.xml", "ref/netstandard1.3/ja/System.Collections.Concurrent.xml", "ref/netstandard1.3/ko/System.Collections.Concurrent.xml", "ref/netstandard1.3/ru/System.Collections.Concurrent.xml", "ref/netstandard1.3/zh-hans/System.Collections.Concurrent.xml", "ref/netstandard1.3/zh-hant/System.Collections.Concurrent.xml", "ref/portable-net45+win8+wpa81/_._", "ref/win8/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.collections.concurrent.4.3.0.nupkg.sha512", "system.collections.concurrent.nuspec"]}, "System.Collections.Immutable/5.0.0": {"sha512": "FXkLXiK0sVVewcso0imKQoOxjoPAj42R8HtjjbSjVPAzwDfzoyoznWxgA3c38LDbN9SJux1xXoXYAhz98j7r2g==", "type": "package", "path": "system.collections.immutable/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Collections.Immutable.dll", "lib/net461/System.Collections.Immutable.xml", "lib/netstandard1.0/System.Collections.Immutable.dll", "lib/netstandard1.0/System.Collections.Immutable.xml", "lib/netstandard1.3/System.Collections.Immutable.dll", "lib/netstandard1.3/System.Collections.Immutable.xml", "lib/netstandard2.0/System.Collections.Immutable.dll", "lib/netstandard2.0/System.Collections.Immutable.xml", "lib/portable-net45+win8+wp8+wpa81/System.Collections.Immutable.dll", "lib/portable-net45+win8+wp8+wpa81/System.Collections.Immutable.xml", "system.collections.immutable.5.0.0.nupkg.sha512", "system.collections.immutable.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Collections.NonGeneric/4.3.0": {"sha512": "prtjIEMhGUnQq6RnPEYLpFt8AtLbp9yq2zxOSrY7KJJZrw25Fi97IzBqY7iqssbM61Ek5b8f3MG/sG1N2sN5KA==", "type": "package", "path": "system.collections.nongeneric/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Collections.NonGeneric.dll", "lib/netstandard1.3/System.Collections.NonGeneric.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Collections.NonGeneric.dll", "ref/netstandard1.3/System.Collections.NonGeneric.dll", "ref/netstandard1.3/System.Collections.NonGeneric.xml", "ref/netstandard1.3/de/System.Collections.NonGeneric.xml", "ref/netstandard1.3/es/System.Collections.NonGeneric.xml", "ref/netstandard1.3/fr/System.Collections.NonGeneric.xml", "ref/netstandard1.3/it/System.Collections.NonGeneric.xml", "ref/netstandard1.3/ja/System.Collections.NonGeneric.xml", "ref/netstandard1.3/ko/System.Collections.NonGeneric.xml", "ref/netstandard1.3/ru/System.Collections.NonGeneric.xml", "ref/netstandard1.3/zh-hans/System.Collections.NonGeneric.xml", "ref/netstandard1.3/zh-hant/System.Collections.NonGeneric.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.collections.nongeneric.4.3.0.nupkg.sha512", "system.collections.nongeneric.nuspec"]}, "System.Collections.Specialized/4.3.0": {"sha512": "Epx8PoVZR0iuOnJJDzp7pWvdfMMOAvpUo95pC4ScH2mJuXkKA2Y4aR3cG9qt2klHgSons1WFh4kcGW7cSXvrxg==", "type": "package", "path": "system.collections.specialized/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Collections.Specialized.dll", "lib/netstandard1.3/System.Collections.Specialized.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Collections.Specialized.dll", "ref/netstandard1.3/System.Collections.Specialized.dll", "ref/netstandard1.3/System.Collections.Specialized.xml", "ref/netstandard1.3/de/System.Collections.Specialized.xml", "ref/netstandard1.3/es/System.Collections.Specialized.xml", "ref/netstandard1.3/fr/System.Collections.Specialized.xml", "ref/netstandard1.3/it/System.Collections.Specialized.xml", "ref/netstandard1.3/ja/System.Collections.Specialized.xml", "ref/netstandard1.3/ko/System.Collections.Specialized.xml", "ref/netstandard1.3/ru/System.Collections.Specialized.xml", "ref/netstandard1.3/zh-hans/System.Collections.Specialized.xml", "ref/netstandard1.3/zh-hant/System.Collections.Specialized.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.collections.specialized.4.3.0.nupkg.sha512", "system.collections.specialized.nuspec"]}, "System.ComponentModel/4.3.0": {"sha512": "VyGn1jGRZVfxnh8EdvDCi71v3bMXrsu8aYJOwoV7SNDLVhiEqwP86pPMyRGsDsxhXAm2b3o9OIqeETfN5qfezw==", "type": "package", "path": "system.componentmodel/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.ComponentModel.dll", "lib/netstandard1.3/System.ComponentModel.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.ComponentModel.dll", "ref/netcore50/System.ComponentModel.xml", "ref/netcore50/de/System.ComponentModel.xml", "ref/netcore50/es/System.ComponentModel.xml", "ref/netcore50/fr/System.ComponentModel.xml", "ref/netcore50/it/System.ComponentModel.xml", "ref/netcore50/ja/System.ComponentModel.xml", "ref/netcore50/ko/System.ComponentModel.xml", "ref/netcore50/ru/System.ComponentModel.xml", "ref/netcore50/zh-hans/System.ComponentModel.xml", "ref/netcore50/zh-hant/System.ComponentModel.xml", "ref/netstandard1.0/System.ComponentModel.dll", "ref/netstandard1.0/System.ComponentModel.xml", "ref/netstandard1.0/de/System.ComponentModel.xml", "ref/netstandard1.0/es/System.ComponentModel.xml", "ref/netstandard1.0/fr/System.ComponentModel.xml", "ref/netstandard1.0/it/System.ComponentModel.xml", "ref/netstandard1.0/ja/System.ComponentModel.xml", "ref/netstandard1.0/ko/System.ComponentModel.xml", "ref/netstandard1.0/ru/System.ComponentModel.xml", "ref/netstandard1.0/zh-hans/System.ComponentModel.xml", "ref/netstandard1.0/zh-hant/System.ComponentModel.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.componentmodel.4.3.0.nupkg.sha512", "system.componentmodel.nuspec"]}, "System.ComponentModel.Annotations/5.0.0": {"sha512": "dMkqfy2el8A8/I76n2Hi1oBFEbG1SfxD2l5nhwXV3XjlnOmwxJlQbYpJH4W51odnU9sARCSAgv7S3CyAFMkpYg==", "type": "package", "path": "system.componentmodel.annotations/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net461/System.ComponentModel.Annotations.dll", "lib/netcore50/System.ComponentModel.Annotations.dll", "lib/netstandard1.4/System.ComponentModel.Annotations.dll", "lib/netstandard2.0/System.ComponentModel.Annotations.dll", "lib/netstandard2.1/System.ComponentModel.Annotations.dll", "lib/netstandard2.1/System.ComponentModel.Annotations.xml", "lib/portable-net45+win8/_._", "lib/win8/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net461/System.ComponentModel.Annotations.dll", "ref/net461/System.ComponentModel.Annotations.xml", "ref/netcore50/System.ComponentModel.Annotations.dll", "ref/netcore50/System.ComponentModel.Annotations.xml", "ref/netcore50/de/System.ComponentModel.Annotations.xml", "ref/netcore50/es/System.ComponentModel.Annotations.xml", "ref/netcore50/fr/System.ComponentModel.Annotations.xml", "ref/netcore50/it/System.ComponentModel.Annotations.xml", "ref/netcore50/ja/System.ComponentModel.Annotations.xml", "ref/netcore50/ko/System.ComponentModel.Annotations.xml", "ref/netcore50/ru/System.ComponentModel.Annotations.xml", "ref/netcore50/zh-hans/System.ComponentModel.Annotations.xml", "ref/netcore50/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/System.ComponentModel.Annotations.dll", "ref/netstandard1.1/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/de/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/es/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/fr/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/it/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/ja/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/ko/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/ru/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/zh-hans/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/System.ComponentModel.Annotations.dll", "ref/netstandard1.3/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/de/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/es/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/fr/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/it/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/ja/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/ko/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/ru/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/zh-hans/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/System.ComponentModel.Annotations.dll", "ref/netstandard1.4/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/de/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/es/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/fr/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/it/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/ja/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/ko/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/ru/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/zh-hans/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard2.0/System.ComponentModel.Annotations.dll", "ref/netstandard2.0/System.ComponentModel.Annotations.xml", "ref/netstandard2.1/System.ComponentModel.Annotations.dll", "ref/netstandard2.1/System.ComponentModel.Annotations.xml", "ref/portable-net45+win8/_._", "ref/win8/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.componentmodel.annotations.5.0.0.nupkg.sha512", "system.componentmodel.annotations.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.ComponentModel.Primitives/4.3.0": {"sha512": "j8GUkCpM8V4d4vhLIIoBLGey2Z5bCkMVNjEZseyAlm4n5arcsJOeI3zkUP+zvZgzsbLTYh4lYeP/ZD/gdIAPrw==", "type": "package", "path": "system.componentmodel.primitives/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/System.ComponentModel.Primitives.dll", "lib/netstandard1.0/System.ComponentModel.Primitives.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/System.ComponentModel.Primitives.dll", "ref/netstandard1.0/System.ComponentModel.Primitives.dll", "ref/netstandard1.0/System.ComponentModel.Primitives.xml", "ref/netstandard1.0/de/System.ComponentModel.Primitives.xml", "ref/netstandard1.0/es/System.ComponentModel.Primitives.xml", "ref/netstandard1.0/fr/System.ComponentModel.Primitives.xml", "ref/netstandard1.0/it/System.ComponentModel.Primitives.xml", "ref/netstandard1.0/ja/System.ComponentModel.Primitives.xml", "ref/netstandard1.0/ko/System.ComponentModel.Primitives.xml", "ref/netstandard1.0/ru/System.ComponentModel.Primitives.xml", "ref/netstandard1.0/zh-hans/System.ComponentModel.Primitives.xml", "ref/netstandard1.0/zh-hant/System.ComponentModel.Primitives.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.componentmodel.primitives.4.3.0.nupkg.sha512", "system.componentmodel.primitives.nuspec"]}, "System.ComponentModel.TypeConverter/4.3.0": {"sha512": "16pQ6P+EdhcXzPiEK4kbA953Fu0MNG2ovxTZU81/qsCd1zPRsKc3uif5NgvllCY598k6bI0KUyKW8fanlfaDQg==", "type": "package", "path": "system.componentmodel.typeconverter/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/System.ComponentModel.TypeConverter.dll", "lib/net462/System.ComponentModel.TypeConverter.dll", "lib/netstandard1.0/System.ComponentModel.TypeConverter.dll", "lib/netstandard1.5/System.ComponentModel.TypeConverter.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/System.ComponentModel.TypeConverter.dll", "ref/net462/System.ComponentModel.TypeConverter.dll", "ref/netstandard1.0/System.ComponentModel.TypeConverter.dll", "ref/netstandard1.0/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.0/de/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.0/es/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.0/fr/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.0/it/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.0/ja/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.0/ko/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.0/ru/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.0/zh-hans/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.0/zh-hant/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/System.ComponentModel.TypeConverter.dll", "ref/netstandard1.5/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/de/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/es/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/fr/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/it/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/ja/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/ko/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/ru/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/zh-hans/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/zh-hant/System.ComponentModel.TypeConverter.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.componentmodel.typeconverter.4.3.0.nupkg.sha512", "system.componentmodel.typeconverter.nuspec"]}, "System.Configuration.ConfigurationManager/4.7.0": {"sha512": "/anOTeSZCNNI2zDilogWrZ8pNqCmYbzGNexUnNhjW8k0sHqEZ2nHJBp147jBV3hGYswu5lINpNg1vxR7bnqvVA==", "type": "package", "path": "system.configuration.configurationmanager/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Configuration.ConfigurationManager.dll", "lib/net461/System.Configuration.ConfigurationManager.xml", "lib/netstandard2.0/System.Configuration.ConfigurationManager.dll", "lib/netstandard2.0/System.Configuration.ConfigurationManager.xml", "ref/net461/System.Configuration.ConfigurationManager.dll", "ref/net461/System.Configuration.ConfigurationManager.xml", "ref/netstandard2.0/System.Configuration.ConfigurationManager.dll", "ref/netstandard2.0/System.Configuration.ConfigurationManager.xml", "system.configuration.configurationmanager.4.7.0.nupkg.sha512", "system.configuration.configurationmanager.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Console/4.3.0": {"sha512": "DHDrIxiqk1h03m6khKWV2X8p/uvN79rgSqpilL6uzpmSfxfU5ng8VcPtW4qsDsQDHiTv6IPV9TmD5M/vElPNLg==", "type": "package", "path": "system.console/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Console.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Console.dll", "ref/netstandard1.3/System.Console.dll", "ref/netstandard1.3/System.Console.xml", "ref/netstandard1.3/de/System.Console.xml", "ref/netstandard1.3/es/System.Console.xml", "ref/netstandard1.3/fr/System.Console.xml", "ref/netstandard1.3/it/System.Console.xml", "ref/netstandard1.3/ja/System.Console.xml", "ref/netstandard1.3/ko/System.Console.xml", "ref/netstandard1.3/ru/System.Console.xml", "ref/netstandard1.3/zh-hans/System.Console.xml", "ref/netstandard1.3/zh-hant/System.Console.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.console.4.3.0.nupkg.sha512", "system.console.nuspec"]}, "System.Data.Common/4.3.0": {"sha512": "lm6E3T5u7BOuEH0u18JpbJHxBfOJPuCyl4Kg1RH10ktYLp5uEEE1xKrHW56/We4SnZpGAuCc9N0MJpSDhTHZGQ==", "type": "package", "path": "system.data.common/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net451/System.Data.Common.dll", "lib/netstandard1.2/System.Data.Common.dll", "lib/portable-net451+win8+wp8+wpa81/System.Data.Common.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net451/System.Data.Common.dll", "ref/netstandard1.2/System.Data.Common.dll", "ref/netstandard1.2/System.Data.Common.xml", "ref/netstandard1.2/de/System.Data.Common.xml", "ref/netstandard1.2/es/System.Data.Common.xml", "ref/netstandard1.2/fr/System.Data.Common.xml", "ref/netstandard1.2/it/System.Data.Common.xml", "ref/netstandard1.2/ja/System.Data.Common.xml", "ref/netstandard1.2/ko/System.Data.Common.xml", "ref/netstandard1.2/ru/System.Data.Common.xml", "ref/netstandard1.2/zh-hans/System.Data.Common.xml", "ref/netstandard1.2/zh-hant/System.Data.Common.xml", "ref/portable-net451+win8+wp8+wpa81/System.Data.Common.dll", "ref/portable-net451+win8+wp8+wpa81/System.Data.Common.xml", "ref/portable-net451+win8+wp8+wpa81/de/System.Data.Common.xml", "ref/portable-net451+win8+wp8+wpa81/es/System.Data.Common.xml", "ref/portable-net451+win8+wp8+wpa81/fr/System.Data.Common.xml", "ref/portable-net451+win8+wp8+wpa81/it/System.Data.Common.xml", "ref/portable-net451+win8+wp8+wpa81/ja/System.Data.Common.xml", "ref/portable-net451+win8+wp8+wpa81/ko/System.Data.Common.xml", "ref/portable-net451+win8+wp8+wpa81/ru/System.Data.Common.xml", "ref/portable-net451+win8+wp8+wpa81/zh-hans/System.Data.Common.xml", "ref/portable-net451+win8+wp8+wpa81/zh-hant/System.Data.Common.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.data.common.4.3.0.nupkg.sha512", "system.data.common.nuspec"]}, "System.Diagnostics.Debug/4.3.0": {"sha512": "ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "type": "package", "path": "system.diagnostics.debug/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Diagnostics.Debug.dll", "ref/netcore50/System.Diagnostics.Debug.xml", "ref/netcore50/de/System.Diagnostics.Debug.xml", "ref/netcore50/es/System.Diagnostics.Debug.xml", "ref/netcore50/fr/System.Diagnostics.Debug.xml", "ref/netcore50/it/System.Diagnostics.Debug.xml", "ref/netcore50/ja/System.Diagnostics.Debug.xml", "ref/netcore50/ko/System.Diagnostics.Debug.xml", "ref/netcore50/ru/System.Diagnostics.Debug.xml", "ref/netcore50/zh-hans/System.Diagnostics.Debug.xml", "ref/netcore50/zh-hant/System.Diagnostics.Debug.xml", "ref/netstandard1.0/System.Diagnostics.Debug.dll", "ref/netstandard1.0/System.Diagnostics.Debug.xml", "ref/netstandard1.0/de/System.Diagnostics.Debug.xml", "ref/netstandard1.0/es/System.Diagnostics.Debug.xml", "ref/netstandard1.0/fr/System.Diagnostics.Debug.xml", "ref/netstandard1.0/it/System.Diagnostics.Debug.xml", "ref/netstandard1.0/ja/System.Diagnostics.Debug.xml", "ref/netstandard1.0/ko/System.Diagnostics.Debug.xml", "ref/netstandard1.0/ru/System.Diagnostics.Debug.xml", "ref/netstandard1.0/zh-hans/System.Diagnostics.Debug.xml", "ref/netstandard1.0/zh-hant/System.Diagnostics.Debug.xml", "ref/netstandard1.3/System.Diagnostics.Debug.dll", "ref/netstandard1.3/System.Diagnostics.Debug.xml", "ref/netstandard1.3/de/System.Diagnostics.Debug.xml", "ref/netstandard1.3/es/System.Diagnostics.Debug.xml", "ref/netstandard1.3/fr/System.Diagnostics.Debug.xml", "ref/netstandard1.3/it/System.Diagnostics.Debug.xml", "ref/netstandard1.3/ja/System.Diagnostics.Debug.xml", "ref/netstandard1.3/ko/System.Diagnostics.Debug.xml", "ref/netstandard1.3/ru/System.Diagnostics.Debug.xml", "ref/netstandard1.3/zh-hans/System.Diagnostics.Debug.xml", "ref/netstandard1.3/zh-hant/System.Diagnostics.Debug.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.diagnostics.debug.4.3.0.nupkg.sha512", "system.diagnostics.debug.nuspec"]}, "System.Diagnostics.DiagnosticSource/5.0.1": {"sha512": "uXQEYqav2V3zP6OwkOKtLv+qIi6z3m1hsGyKwXX7ZA7htT4shoVccGxnJ9kVRFPNAsi1ArZTq2oh7WOto6GbkQ==", "type": "package", "path": "system.diagnostics.diagnosticsource/5.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net45/System.Diagnostics.DiagnosticSource.dll", "lib/net45/System.Diagnostics.DiagnosticSource.xml", "lib/net46/System.Diagnostics.DiagnosticSource.dll", "lib/net46/System.Diagnostics.DiagnosticSource.xml", "lib/net5.0/System.Diagnostics.DiagnosticSource.dll", "lib/net5.0/System.Diagnostics.DiagnosticSource.xml", "lib/netstandard1.1/System.Diagnostics.DiagnosticSource.dll", "lib/netstandard1.1/System.Diagnostics.DiagnosticSource.xml", "lib/netstandard1.3/System.Diagnostics.DiagnosticSource.dll", "lib/netstandard1.3/System.Diagnostics.DiagnosticSource.xml", "lib/portable-net45+win8+wpa81/System.Diagnostics.DiagnosticSource.dll", "lib/portable-net45+win8+wpa81/System.Diagnostics.DiagnosticSource.xml", "system.diagnostics.diagnosticsource.5.0.1.nupkg.sha512", "system.diagnostics.diagnosticsource.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Diagnostics.Tools/4.3.0": {"sha512": "UUvkJfSYJMM6x527dJg2VyWPSRqIVB0Z7dbjHst1zmwTXz5CcXSYJFWRpuigfbO1Lf7yfZiIaEUesfnl/g5EyA==", "type": "package", "path": "system.diagnostics.tools/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Diagnostics.Tools.dll", "ref/netcore50/System.Diagnostics.Tools.xml", "ref/netcore50/de/System.Diagnostics.Tools.xml", "ref/netcore50/es/System.Diagnostics.Tools.xml", "ref/netcore50/fr/System.Diagnostics.Tools.xml", "ref/netcore50/it/System.Diagnostics.Tools.xml", "ref/netcore50/ja/System.Diagnostics.Tools.xml", "ref/netcore50/ko/System.Diagnostics.Tools.xml", "ref/netcore50/ru/System.Diagnostics.Tools.xml", "ref/netcore50/zh-hans/System.Diagnostics.Tools.xml", "ref/netcore50/zh-hant/System.Diagnostics.Tools.xml", "ref/netstandard1.0/System.Diagnostics.Tools.dll", "ref/netstandard1.0/System.Diagnostics.Tools.xml", "ref/netstandard1.0/de/System.Diagnostics.Tools.xml", "ref/netstandard1.0/es/System.Diagnostics.Tools.xml", "ref/netstandard1.0/fr/System.Diagnostics.Tools.xml", "ref/netstandard1.0/it/System.Diagnostics.Tools.xml", "ref/netstandard1.0/ja/System.Diagnostics.Tools.xml", "ref/netstandard1.0/ko/System.Diagnostics.Tools.xml", "ref/netstandard1.0/ru/System.Diagnostics.Tools.xml", "ref/netstandard1.0/zh-hans/System.Diagnostics.Tools.xml", "ref/netstandard1.0/zh-hant/System.Diagnostics.Tools.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.diagnostics.tools.4.3.0.nupkg.sha512", "system.diagnostics.tools.nuspec"]}, "System.Diagnostics.TraceSource/4.3.0": {"sha512": "VnYp1NxGx8Ww731y2LJ1vpfb/DKVNKEZ8Jsh5SgQTZREL/YpWRArgh9pI8CDLmgHspZmLL697CaLvH85qQpRiw==", "type": "package", "path": "system.diagnostics.tracesource/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Diagnostics.TraceSource.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Diagnostics.TraceSource.dll", "ref/netstandard1.3/System.Diagnostics.TraceSource.dll", "ref/netstandard1.3/System.Diagnostics.TraceSource.xml", "ref/netstandard1.3/de/System.Diagnostics.TraceSource.xml", "ref/netstandard1.3/es/System.Diagnostics.TraceSource.xml", "ref/netstandard1.3/fr/System.Diagnostics.TraceSource.xml", "ref/netstandard1.3/it/System.Diagnostics.TraceSource.xml", "ref/netstandard1.3/ja/System.Diagnostics.TraceSource.xml", "ref/netstandard1.3/ko/System.Diagnostics.TraceSource.xml", "ref/netstandard1.3/ru/System.Diagnostics.TraceSource.xml", "ref/netstandard1.3/zh-hans/System.Diagnostics.TraceSource.xml", "ref/netstandard1.3/zh-hant/System.Diagnostics.TraceSource.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netstandard1.3/System.Diagnostics.TraceSource.dll", "runtimes/win/lib/net46/System.Diagnostics.TraceSource.dll", "runtimes/win/lib/netstandard1.3/System.Diagnostics.TraceSource.dll", "system.diagnostics.tracesource.4.3.0.nupkg.sha512", "system.diagnostics.tracesource.nuspec"]}, "System.Diagnostics.Tracing/4.3.0": {"sha512": "rswfv0f/Cqkh78rA5S8eN8Neocz234+emGCtTF3lxPY96F+mmmUen6tbn0glN6PMvlKQb9bPAY5e9u7fgPTkKw==", "type": "package", "path": "system.diagnostics.tracing/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Diagnostics.Tracing.dll", "lib/portable-net45+win8+wpa81/_._", "lib/win8/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Diagnostics.Tracing.dll", "ref/netcore50/System.Diagnostics.Tracing.dll", "ref/netcore50/System.Diagnostics.Tracing.xml", "ref/netcore50/de/System.Diagnostics.Tracing.xml", "ref/netcore50/es/System.Diagnostics.Tracing.xml", "ref/netcore50/fr/System.Diagnostics.Tracing.xml", "ref/netcore50/it/System.Diagnostics.Tracing.xml", "ref/netcore50/ja/System.Diagnostics.Tracing.xml", "ref/netcore50/ko/System.Diagnostics.Tracing.xml", "ref/netcore50/ru/System.Diagnostics.Tracing.xml", "ref/netcore50/zh-hans/System.Diagnostics.Tracing.xml", "ref/netcore50/zh-hant/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/System.Diagnostics.Tracing.dll", "ref/netstandard1.1/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/de/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/es/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/fr/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/it/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/ja/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/ko/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/ru/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/zh-hans/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/zh-hant/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/System.Diagnostics.Tracing.dll", "ref/netstandard1.2/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/de/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/es/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/fr/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/it/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/ja/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/ko/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/ru/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/zh-hans/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/zh-hant/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/System.Diagnostics.Tracing.dll", "ref/netstandard1.3/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/de/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/es/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/fr/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/it/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/ja/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/ko/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/ru/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/zh-hans/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/zh-hant/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/System.Diagnostics.Tracing.dll", "ref/netstandard1.5/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/de/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/es/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/fr/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/it/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/ja/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/ko/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/ru/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/zh-hans/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/zh-hant/System.Diagnostics.Tracing.xml", "ref/portable-net45+win8+wpa81/_._", "ref/win8/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.diagnostics.tracing.4.3.0.nupkg.sha512", "system.diagnostics.tracing.nuspec"]}, "System.Drawing.Common/5.0.0": {"sha512": "SztFwAnpfKC8+sEKXAFxCBWhKQaEd97EiOL7oZJZP56zbqnLpmxACWA8aGseaUExciuEAUuR9dY8f7HkTRAdnw==", "type": "package", "path": "system.drawing.common/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.Drawing.Common.dll", "lib/netcoreapp3.0/System.Drawing.Common.dll", "lib/netcoreapp3.0/System.Drawing.Common.xml", "lib/netstandard2.0/System.Drawing.Common.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net461/System.Drawing.Common.dll", "ref/netcoreapp3.0/System.Drawing.Common.dll", "ref/netcoreapp3.0/System.Drawing.Common.xml", "ref/netstandard2.0/System.Drawing.Common.dll", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netcoreapp2.0/System.Drawing.Common.dll", "runtimes/unix/lib/netcoreapp3.0/System.Drawing.Common.dll", "runtimes/unix/lib/netcoreapp3.0/System.Drawing.Common.xml", "runtimes/win/lib/netcoreapp2.0/System.Drawing.Common.dll", "runtimes/win/lib/netcoreapp3.0/System.Drawing.Common.dll", "runtimes/win/lib/netcoreapp3.0/System.Drawing.Common.xml", "system.drawing.common.5.0.0.nupkg.sha512", "system.drawing.common.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Dynamic.Runtime/4.3.0": {"sha512": "SNVi1E/vfWUAs/WYKhE9+qlS6KqK0YVhnlT0HQtr8pMIA8YX3lwy3uPMownDwdYISBdmAF/2holEIldVp85Wag==", "type": "package", "path": "system.dynamic.runtime/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Dynamic.Runtime.dll", "lib/netstandard1.3/System.Dynamic.Runtime.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Dynamic.Runtime.dll", "ref/netcore50/System.Dynamic.Runtime.xml", "ref/netcore50/de/System.Dynamic.Runtime.xml", "ref/netcore50/es/System.Dynamic.Runtime.xml", "ref/netcore50/fr/System.Dynamic.Runtime.xml", "ref/netcore50/it/System.Dynamic.Runtime.xml", "ref/netcore50/ja/System.Dynamic.Runtime.xml", "ref/netcore50/ko/System.Dynamic.Runtime.xml", "ref/netcore50/ru/System.Dynamic.Runtime.xml", "ref/netcore50/zh-hans/System.Dynamic.Runtime.xml", "ref/netcore50/zh-hant/System.Dynamic.Runtime.xml", "ref/netstandard1.0/System.Dynamic.Runtime.dll", "ref/netstandard1.0/System.Dynamic.Runtime.xml", "ref/netstandard1.0/de/System.Dynamic.Runtime.xml", "ref/netstandard1.0/es/System.Dynamic.Runtime.xml", "ref/netstandard1.0/fr/System.Dynamic.Runtime.xml", "ref/netstandard1.0/it/System.Dynamic.Runtime.xml", "ref/netstandard1.0/ja/System.Dynamic.Runtime.xml", "ref/netstandard1.0/ko/System.Dynamic.Runtime.xml", "ref/netstandard1.0/ru/System.Dynamic.Runtime.xml", "ref/netstandard1.0/zh-hans/System.Dynamic.Runtime.xml", "ref/netstandard1.0/zh-hant/System.Dynamic.Runtime.xml", "ref/netstandard1.3/System.Dynamic.Runtime.dll", "ref/netstandard1.3/System.Dynamic.Runtime.xml", "ref/netstandard1.3/de/System.Dynamic.Runtime.xml", "ref/netstandard1.3/es/System.Dynamic.Runtime.xml", "ref/netstandard1.3/fr/System.Dynamic.Runtime.xml", "ref/netstandard1.3/it/System.Dynamic.Runtime.xml", "ref/netstandard1.3/ja/System.Dynamic.Runtime.xml", "ref/netstandard1.3/ko/System.Dynamic.Runtime.xml", "ref/netstandard1.3/ru/System.Dynamic.Runtime.xml", "ref/netstandard1.3/zh-hans/System.Dynamic.Runtime.xml", "ref/netstandard1.3/zh-hant/System.Dynamic.Runtime.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Dynamic.Runtime.dll", "system.dynamic.runtime.4.3.0.nupkg.sha512", "system.dynamic.runtime.nuspec"]}, "System.Formats.Asn1/5.0.0": {"sha512": "MTvUIktmemNB+El0Fgw9egyqT9AYSIk6DTJeoDSpc3GIHxHCMo8COqkWT1mptX5tZ1SlQ6HJZ0OsSvMth1c12w==", "type": "package", "path": "system.formats.asn1/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Formats.Asn1.dll", "lib/net461/System.Formats.Asn1.xml", "lib/netstandard2.0/System.Formats.Asn1.dll", "lib/netstandard2.0/System.Formats.Asn1.xml", "system.formats.asn1.5.0.0.nupkg.sha512", "system.formats.asn1.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Globalization/4.3.0": {"sha512": "kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "type": "package", "path": "system.globalization/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Globalization.dll", "ref/netcore50/System.Globalization.xml", "ref/netcore50/de/System.Globalization.xml", "ref/netcore50/es/System.Globalization.xml", "ref/netcore50/fr/System.Globalization.xml", "ref/netcore50/it/System.Globalization.xml", "ref/netcore50/ja/System.Globalization.xml", "ref/netcore50/ko/System.Globalization.xml", "ref/netcore50/ru/System.Globalization.xml", "ref/netcore50/zh-hans/System.Globalization.xml", "ref/netcore50/zh-hant/System.Globalization.xml", "ref/netstandard1.0/System.Globalization.dll", "ref/netstandard1.0/System.Globalization.xml", "ref/netstandard1.0/de/System.Globalization.xml", "ref/netstandard1.0/es/System.Globalization.xml", "ref/netstandard1.0/fr/System.Globalization.xml", "ref/netstandard1.0/it/System.Globalization.xml", "ref/netstandard1.0/ja/System.Globalization.xml", "ref/netstandard1.0/ko/System.Globalization.xml", "ref/netstandard1.0/ru/System.Globalization.xml", "ref/netstandard1.0/zh-hans/System.Globalization.xml", "ref/netstandard1.0/zh-hant/System.Globalization.xml", "ref/netstandard1.3/System.Globalization.dll", "ref/netstandard1.3/System.Globalization.xml", "ref/netstandard1.3/de/System.Globalization.xml", "ref/netstandard1.3/es/System.Globalization.xml", "ref/netstandard1.3/fr/System.Globalization.xml", "ref/netstandard1.3/it/System.Globalization.xml", "ref/netstandard1.3/ja/System.Globalization.xml", "ref/netstandard1.3/ko/System.Globalization.xml", "ref/netstandard1.3/ru/System.Globalization.xml", "ref/netstandard1.3/zh-hans/System.Globalization.xml", "ref/netstandard1.3/zh-hant/System.Globalization.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.globalization.4.3.0.nupkg.sha512", "system.globalization.nuspec"]}, "System.Globalization.Calendars/4.3.0": {"sha512": "GUlBtdOWT4LTV3I+9/PJW+56AnnChTaOqqTLFtdmype/L500M2LIyXgmtd9X2P2VOkmJd5c67H5SaC2QcL1bFA==", "type": "package", "path": "system.globalization.calendars/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Globalization.Calendars.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Globalization.Calendars.dll", "ref/netstandard1.3/System.Globalization.Calendars.dll", "ref/netstandard1.3/System.Globalization.Calendars.xml", "ref/netstandard1.3/de/System.Globalization.Calendars.xml", "ref/netstandard1.3/es/System.Globalization.Calendars.xml", "ref/netstandard1.3/fr/System.Globalization.Calendars.xml", "ref/netstandard1.3/it/System.Globalization.Calendars.xml", "ref/netstandard1.3/ja/System.Globalization.Calendars.xml", "ref/netstandard1.3/ko/System.Globalization.Calendars.xml", "ref/netstandard1.3/ru/System.Globalization.Calendars.xml", "ref/netstandard1.3/zh-hans/System.Globalization.Calendars.xml", "ref/netstandard1.3/zh-hant/System.Globalization.Calendars.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.globalization.calendars.4.3.0.nupkg.sha512", "system.globalization.calendars.nuspec"]}, "System.Globalization.Extensions/4.3.0": {"sha512": "FhKmdR6MPG+pxow6wGtNAWdZh7noIOpdD5TwQ3CprzgIE1bBBoim0vbR1+AWsWjQmU7zXHgQo4TWSP6lCeiWcQ==", "type": "package", "path": "system.globalization.extensions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Globalization.Extensions.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Globalization.Extensions.dll", "ref/netstandard1.3/System.Globalization.Extensions.dll", "ref/netstandard1.3/System.Globalization.Extensions.xml", "ref/netstandard1.3/de/System.Globalization.Extensions.xml", "ref/netstandard1.3/es/System.Globalization.Extensions.xml", "ref/netstandard1.3/fr/System.Globalization.Extensions.xml", "ref/netstandard1.3/it/System.Globalization.Extensions.xml", "ref/netstandard1.3/ja/System.Globalization.Extensions.xml", "ref/netstandard1.3/ko/System.Globalization.Extensions.xml", "ref/netstandard1.3/ru/System.Globalization.Extensions.xml", "ref/netstandard1.3/zh-hans/System.Globalization.Extensions.xml", "ref/netstandard1.3/zh-hant/System.Globalization.Extensions.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netstandard1.3/System.Globalization.Extensions.dll", "runtimes/win/lib/net46/System.Globalization.Extensions.dll", "runtimes/win/lib/netstandard1.3/System.Globalization.Extensions.dll", "system.globalization.extensions.4.3.0.nupkg.sha512", "system.globalization.extensions.nuspec"]}, "System.IdentityModel.Tokens.Jwt/6.11.1": {"sha512": "xh6/P+XdsUZoTbN1MgRg1zONdHTVfH8TDFSnPwXF4p8qhKvmUdXtE6UmTDN8HLe8jveY7Bjdjr+YP7sm2l7ufA==", "type": "package", "path": "system.identitymodel.tokens.jwt/6.11.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/System.IdentityModel.Tokens.Jwt.dll", "lib/net45/System.IdentityModel.Tokens.Jwt.xml", "lib/net461/System.IdentityModel.Tokens.Jwt.dll", "lib/net461/System.IdentityModel.Tokens.Jwt.xml", "lib/net472/System.IdentityModel.Tokens.Jwt.dll", "lib/net472/System.IdentityModel.Tokens.Jwt.xml", "lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.dll", "lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.xml", "system.identitymodel.tokens.jwt.6.11.1.nupkg.sha512", "system.identitymodel.tokens.jwt.nuspec"]}, "System.IO/4.3.0": {"sha512": "3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "type": "package", "path": "system.io/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.IO.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.IO.dll", "ref/netcore50/System.IO.dll", "ref/netcore50/System.IO.xml", "ref/netcore50/de/System.IO.xml", "ref/netcore50/es/System.IO.xml", "ref/netcore50/fr/System.IO.xml", "ref/netcore50/it/System.IO.xml", "ref/netcore50/ja/System.IO.xml", "ref/netcore50/ko/System.IO.xml", "ref/netcore50/ru/System.IO.xml", "ref/netcore50/zh-hans/System.IO.xml", "ref/netcore50/zh-hant/System.IO.xml", "ref/netstandard1.0/System.IO.dll", "ref/netstandard1.0/System.IO.xml", "ref/netstandard1.0/de/System.IO.xml", "ref/netstandard1.0/es/System.IO.xml", "ref/netstandard1.0/fr/System.IO.xml", "ref/netstandard1.0/it/System.IO.xml", "ref/netstandard1.0/ja/System.IO.xml", "ref/netstandard1.0/ko/System.IO.xml", "ref/netstandard1.0/ru/System.IO.xml", "ref/netstandard1.0/zh-hans/System.IO.xml", "ref/netstandard1.0/zh-hant/System.IO.xml", "ref/netstandard1.3/System.IO.dll", "ref/netstandard1.3/System.IO.xml", "ref/netstandard1.3/de/System.IO.xml", "ref/netstandard1.3/es/System.IO.xml", "ref/netstandard1.3/fr/System.IO.xml", "ref/netstandard1.3/it/System.IO.xml", "ref/netstandard1.3/ja/System.IO.xml", "ref/netstandard1.3/ko/System.IO.xml", "ref/netstandard1.3/ru/System.IO.xml", "ref/netstandard1.3/zh-hans/System.IO.xml", "ref/netstandard1.3/zh-hant/System.IO.xml", "ref/netstandard1.5/System.IO.dll", "ref/netstandard1.5/System.IO.xml", "ref/netstandard1.5/de/System.IO.xml", "ref/netstandard1.5/es/System.IO.xml", "ref/netstandard1.5/fr/System.IO.xml", "ref/netstandard1.5/it/System.IO.xml", "ref/netstandard1.5/ja/System.IO.xml", "ref/netstandard1.5/ko/System.IO.xml", "ref/netstandard1.5/ru/System.IO.xml", "ref/netstandard1.5/zh-hans/System.IO.xml", "ref/netstandard1.5/zh-hant/System.IO.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.io.4.3.0.nupkg.sha512", "system.io.nuspec"]}, "System.IO.Compression/4.3.0": {"sha512": "YHndyoiV90iu4iKG115ibkhrG+S3jBm8Ap9OwoUAzO5oPDAWcr0SFwQFm0HjM8WkEZWo0zvLTyLmbvTkW1bXgg==", "type": "package", "path": "system.io.compression/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net46/System.IO.Compression.dll", "lib/portable-net45+win8+wpa81/_._", "lib/win8/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net46/System.IO.Compression.dll", "ref/netcore50/System.IO.Compression.dll", "ref/netcore50/System.IO.Compression.xml", "ref/netcore50/de/System.IO.Compression.xml", "ref/netcore50/es/System.IO.Compression.xml", "ref/netcore50/fr/System.IO.Compression.xml", "ref/netcore50/it/System.IO.Compression.xml", "ref/netcore50/ja/System.IO.Compression.xml", "ref/netcore50/ko/System.IO.Compression.xml", "ref/netcore50/ru/System.IO.Compression.xml", "ref/netcore50/zh-hans/System.IO.Compression.xml", "ref/netcore50/zh-hant/System.IO.Compression.xml", "ref/netstandard1.1/System.IO.Compression.dll", "ref/netstandard1.1/System.IO.Compression.xml", "ref/netstandard1.1/de/System.IO.Compression.xml", "ref/netstandard1.1/es/System.IO.Compression.xml", "ref/netstandard1.1/fr/System.IO.Compression.xml", "ref/netstandard1.1/it/System.IO.Compression.xml", "ref/netstandard1.1/ja/System.IO.Compression.xml", "ref/netstandard1.1/ko/System.IO.Compression.xml", "ref/netstandard1.1/ru/System.IO.Compression.xml", "ref/netstandard1.1/zh-hans/System.IO.Compression.xml", "ref/netstandard1.1/zh-hant/System.IO.Compression.xml", "ref/netstandard1.3/System.IO.Compression.dll", "ref/netstandard1.3/System.IO.Compression.xml", "ref/netstandard1.3/de/System.IO.Compression.xml", "ref/netstandard1.3/es/System.IO.Compression.xml", "ref/netstandard1.3/fr/System.IO.Compression.xml", "ref/netstandard1.3/it/System.IO.Compression.xml", "ref/netstandard1.3/ja/System.IO.Compression.xml", "ref/netstandard1.3/ko/System.IO.Compression.xml", "ref/netstandard1.3/ru/System.IO.Compression.xml", "ref/netstandard1.3/zh-hans/System.IO.Compression.xml", "ref/netstandard1.3/zh-hant/System.IO.Compression.xml", "ref/portable-net45+win8+wpa81/_._", "ref/win8/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netstandard1.3/System.IO.Compression.dll", "runtimes/win/lib/net46/System.IO.Compression.dll", "runtimes/win/lib/netstandard1.3/System.IO.Compression.dll", "system.io.compression.4.3.0.nupkg.sha512", "system.io.compression.nuspec"]}, "System.IO.Compression.ZipFile/4.3.0": {"sha512": "G4HwjEsgIwy3JFBduZ9quBkAu+eUwjIdJleuNSgmUojbH6O3mlvEIme+GHx/cLlTAPcrnnL7GqvB9pTlWRfhOg==", "type": "package", "path": "system.io.compression.zipfile/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.IO.Compression.ZipFile.dll", "lib/netstandard1.3/System.IO.Compression.ZipFile.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.IO.Compression.ZipFile.dll", "ref/netstandard1.3/System.IO.Compression.ZipFile.dll", "ref/netstandard1.3/System.IO.Compression.ZipFile.xml", "ref/netstandard1.3/de/System.IO.Compression.ZipFile.xml", "ref/netstandard1.3/es/System.IO.Compression.ZipFile.xml", "ref/netstandard1.3/fr/System.IO.Compression.ZipFile.xml", "ref/netstandard1.3/it/System.IO.Compression.ZipFile.xml", "ref/netstandard1.3/ja/System.IO.Compression.ZipFile.xml", "ref/netstandard1.3/ko/System.IO.Compression.ZipFile.xml", "ref/netstandard1.3/ru/System.IO.Compression.ZipFile.xml", "ref/netstandard1.3/zh-hans/System.IO.Compression.ZipFile.xml", "ref/netstandard1.3/zh-hant/System.IO.Compression.ZipFile.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.io.compression.zipfile.4.3.0.nupkg.sha512", "system.io.compression.zipfile.nuspec"]}, "System.IO.FileSystem/4.3.0": {"sha512": "3wEMARTnuio+ulnvi+hkRNROYwa1kylvYahhcLk4HSoVdl+xxTFVeVlYOfLwrDPImGls0mDqbMhrza8qnWPTdA==", "type": "package", "path": "system.io.filesystem/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.IO.FileSystem.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.IO.FileSystem.dll", "ref/netstandard1.3/System.IO.FileSystem.dll", "ref/netstandard1.3/System.IO.FileSystem.xml", "ref/netstandard1.3/de/System.IO.FileSystem.xml", "ref/netstandard1.3/es/System.IO.FileSystem.xml", "ref/netstandard1.3/fr/System.IO.FileSystem.xml", "ref/netstandard1.3/it/System.IO.FileSystem.xml", "ref/netstandard1.3/ja/System.IO.FileSystem.xml", "ref/netstandard1.3/ko/System.IO.FileSystem.xml", "ref/netstandard1.3/ru/System.IO.FileSystem.xml", "ref/netstandard1.3/zh-hans/System.IO.FileSystem.xml", "ref/netstandard1.3/zh-hant/System.IO.FileSystem.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.io.filesystem.4.3.0.nupkg.sha512", "system.io.filesystem.nuspec"]}, "System.IO.FileSystem.Primitives/4.3.0": {"sha512": "6QOb2XFLch7bEc4lIcJH49nJN2HV+OC3fHDgsLVsBVBk3Y4hFAnOBGzJ2lUu7CyDDFo9IBWkSsnbkT6IBwwiMw==", "type": "package", "path": "system.io.filesystem.primitives/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.IO.FileSystem.Primitives.dll", "lib/netstandard1.3/System.IO.FileSystem.Primitives.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.IO.FileSystem.Primitives.dll", "ref/netstandard1.3/System.IO.FileSystem.Primitives.dll", "ref/netstandard1.3/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/de/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/es/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/fr/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/it/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/ja/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/ko/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/ru/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/zh-hans/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/zh-hant/System.IO.FileSystem.Primitives.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.io.filesystem.primitives.4.3.0.nupkg.sha512", "system.io.filesystem.primitives.nuspec"]}, "System.Linq/4.3.0": {"sha512": "5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "type": "package", "path": "system.linq/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net463/System.Linq.dll", "lib/netcore50/System.Linq.dll", "lib/netstandard1.6/System.Linq.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net463/System.Linq.dll", "ref/netcore50/System.Linq.dll", "ref/netcore50/System.Linq.xml", "ref/netcore50/de/System.Linq.xml", "ref/netcore50/es/System.Linq.xml", "ref/netcore50/fr/System.Linq.xml", "ref/netcore50/it/System.Linq.xml", "ref/netcore50/ja/System.Linq.xml", "ref/netcore50/ko/System.Linq.xml", "ref/netcore50/ru/System.Linq.xml", "ref/netcore50/zh-hans/System.Linq.xml", "ref/netcore50/zh-hant/System.Linq.xml", "ref/netstandard1.0/System.Linq.dll", "ref/netstandard1.0/System.Linq.xml", "ref/netstandard1.0/de/System.Linq.xml", "ref/netstandard1.0/es/System.Linq.xml", "ref/netstandard1.0/fr/System.Linq.xml", "ref/netstandard1.0/it/System.Linq.xml", "ref/netstandard1.0/ja/System.Linq.xml", "ref/netstandard1.0/ko/System.Linq.xml", "ref/netstandard1.0/ru/System.Linq.xml", "ref/netstandard1.0/zh-hans/System.Linq.xml", "ref/netstandard1.0/zh-hant/System.Linq.xml", "ref/netstandard1.6/System.Linq.dll", "ref/netstandard1.6/System.Linq.xml", "ref/netstandard1.6/de/System.Linq.xml", "ref/netstandard1.6/es/System.Linq.xml", "ref/netstandard1.6/fr/System.Linq.xml", "ref/netstandard1.6/it/System.Linq.xml", "ref/netstandard1.6/ja/System.Linq.xml", "ref/netstandard1.6/ko/System.Linq.xml", "ref/netstandard1.6/ru/System.Linq.xml", "ref/netstandard1.6/zh-hans/System.Linq.xml", "ref/netstandard1.6/zh-hant/System.Linq.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.linq.4.3.0.nupkg.sha512", "system.linq.nuspec"]}, "System.Linq.Expressions/4.3.0": {"sha512": "PGKkrd2khG4CnlyJwxwwaWWiSiWFNBGlgXvJpeO0xCXrZ89ODrQ6tjEWS/kOqZ8GwEOUATtKtzp1eRgmYNfclg==", "type": "package", "path": "system.linq.expressions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net463/System.Linq.Expressions.dll", "lib/netcore50/System.Linq.Expressions.dll", "lib/netstandard1.6/System.Linq.Expressions.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net463/System.Linq.Expressions.dll", "ref/netcore50/System.Linq.Expressions.dll", "ref/netcore50/System.Linq.Expressions.xml", "ref/netcore50/de/System.Linq.Expressions.xml", "ref/netcore50/es/System.Linq.Expressions.xml", "ref/netcore50/fr/System.Linq.Expressions.xml", "ref/netcore50/it/System.Linq.Expressions.xml", "ref/netcore50/ja/System.Linq.Expressions.xml", "ref/netcore50/ko/System.Linq.Expressions.xml", "ref/netcore50/ru/System.Linq.Expressions.xml", "ref/netcore50/zh-hans/System.Linq.Expressions.xml", "ref/netcore50/zh-hant/System.Linq.Expressions.xml", "ref/netstandard1.0/System.Linq.Expressions.dll", "ref/netstandard1.0/System.Linq.Expressions.xml", "ref/netstandard1.0/de/System.Linq.Expressions.xml", "ref/netstandard1.0/es/System.Linq.Expressions.xml", "ref/netstandard1.0/fr/System.Linq.Expressions.xml", "ref/netstandard1.0/it/System.Linq.Expressions.xml", "ref/netstandard1.0/ja/System.Linq.Expressions.xml", "ref/netstandard1.0/ko/System.Linq.Expressions.xml", "ref/netstandard1.0/ru/System.Linq.Expressions.xml", "ref/netstandard1.0/zh-hans/System.Linq.Expressions.xml", "ref/netstandard1.0/zh-hant/System.Linq.Expressions.xml", "ref/netstandard1.3/System.Linq.Expressions.dll", "ref/netstandard1.3/System.Linq.Expressions.xml", "ref/netstandard1.3/de/System.Linq.Expressions.xml", "ref/netstandard1.3/es/System.Linq.Expressions.xml", "ref/netstandard1.3/fr/System.Linq.Expressions.xml", "ref/netstandard1.3/it/System.Linq.Expressions.xml", "ref/netstandard1.3/ja/System.Linq.Expressions.xml", "ref/netstandard1.3/ko/System.Linq.Expressions.xml", "ref/netstandard1.3/ru/System.Linq.Expressions.xml", "ref/netstandard1.3/zh-hans/System.Linq.Expressions.xml", "ref/netstandard1.3/zh-hant/System.Linq.Expressions.xml", "ref/netstandard1.6/System.Linq.Expressions.dll", "ref/netstandard1.6/System.Linq.Expressions.xml", "ref/netstandard1.6/de/System.Linq.Expressions.xml", "ref/netstandard1.6/es/System.Linq.Expressions.xml", "ref/netstandard1.6/fr/System.Linq.Expressions.xml", "ref/netstandard1.6/it/System.Linq.Expressions.xml", "ref/netstandard1.6/ja/System.Linq.Expressions.xml", "ref/netstandard1.6/ko/System.Linq.Expressions.xml", "ref/netstandard1.6/ru/System.Linq.Expressions.xml", "ref/netstandard1.6/zh-hans/System.Linq.Expressions.xml", "ref/netstandard1.6/zh-hant/System.Linq.Expressions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Linq.Expressions.dll", "system.linq.expressions.4.3.0.nupkg.sha512", "system.linq.expressions.nuspec"]}, "System.Memory/4.5.3": {"sha512": "3oDzvc/zzetpTKWMShs1AADwZjQ/36HnsufHRPcOjyRAAMLDlu2iD33MBI2opxnezcVUtXyqDXXjoFMOU9c7SA==", "type": "package", "path": "system.memory/4.5.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netcoreapp2.1/_._", "lib/netstandard1.1/System.Memory.dll", "lib/netstandard1.1/System.Memory.xml", "lib/netstandard2.0/System.Memory.dll", "lib/netstandard2.0/System.Memory.xml", "ref/netcoreapp2.1/_._", "system.memory.4.5.3.nupkg.sha512", "system.memory.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Net.Http/4.3.4": {"sha512": "aOa2d51SEbmM+H+Csw7yJOuNZoHkrP2XnAurye5HWYgGVVU54YZDvsLUYRv6h18X3sPnjNCANmN7ZhIPiqMcjA==", "type": "package", "path": "system.net.http/4.3.4", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/Xamarinmac20/_._", "lib/monoandroid10/_._", "lib/monotouch10/_._", "lib/net45/_._", "lib/net46/System.Net.Http.dll", "lib/portable-net45+win8+wpa81/_._", "lib/win8/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/Xamarinmac20/_._", "ref/monoandroid10/_._", "ref/monotouch10/_._", "ref/net45/_._", "ref/net46/System.Net.Http.dll", "ref/netcore50/System.Net.Http.dll", "ref/netstandard1.1/System.Net.Http.dll", "ref/netstandard1.3/System.Net.Http.dll", "ref/portable-net45+win8+wpa81/_._", "ref/win8/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netstandard1.6/System.Net.Http.dll", "runtimes/win/lib/net46/System.Net.Http.dll", "runtimes/win/lib/netcore50/System.Net.Http.dll", "runtimes/win/lib/netstandard1.3/System.Net.Http.dll", "system.net.http.4.3.4.nupkg.sha512", "system.net.http.nuspec"]}, "System.Net.Primitives/4.3.0": {"sha512": "qOu+hDwFwoZPbzPvwut2qATe3ygjeQBDQj91xlsaqGFQUI5i4ZnZb8yyQuLGpDGivEPIt8EJkd1BVzVoP31FXA==", "type": "package", "path": "system.net.primitives/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Net.Primitives.dll", "ref/netcore50/System.Net.Primitives.xml", "ref/netcore50/de/System.Net.Primitives.xml", "ref/netcore50/es/System.Net.Primitives.xml", "ref/netcore50/fr/System.Net.Primitives.xml", "ref/netcore50/it/System.Net.Primitives.xml", "ref/netcore50/ja/System.Net.Primitives.xml", "ref/netcore50/ko/System.Net.Primitives.xml", "ref/netcore50/ru/System.Net.Primitives.xml", "ref/netcore50/zh-hans/System.Net.Primitives.xml", "ref/netcore50/zh-hant/System.Net.Primitives.xml", "ref/netstandard1.0/System.Net.Primitives.dll", "ref/netstandard1.0/System.Net.Primitives.xml", "ref/netstandard1.0/de/System.Net.Primitives.xml", "ref/netstandard1.0/es/System.Net.Primitives.xml", "ref/netstandard1.0/fr/System.Net.Primitives.xml", "ref/netstandard1.0/it/System.Net.Primitives.xml", "ref/netstandard1.0/ja/System.Net.Primitives.xml", "ref/netstandard1.0/ko/System.Net.Primitives.xml", "ref/netstandard1.0/ru/System.Net.Primitives.xml", "ref/netstandard1.0/zh-hans/System.Net.Primitives.xml", "ref/netstandard1.0/zh-hant/System.Net.Primitives.xml", "ref/netstandard1.1/System.Net.Primitives.dll", "ref/netstandard1.1/System.Net.Primitives.xml", "ref/netstandard1.1/de/System.Net.Primitives.xml", "ref/netstandard1.1/es/System.Net.Primitives.xml", "ref/netstandard1.1/fr/System.Net.Primitives.xml", "ref/netstandard1.1/it/System.Net.Primitives.xml", "ref/netstandard1.1/ja/System.Net.Primitives.xml", "ref/netstandard1.1/ko/System.Net.Primitives.xml", "ref/netstandard1.1/ru/System.Net.Primitives.xml", "ref/netstandard1.1/zh-hans/System.Net.Primitives.xml", "ref/netstandard1.1/zh-hant/System.Net.Primitives.xml", "ref/netstandard1.3/System.Net.Primitives.dll", "ref/netstandard1.3/System.Net.Primitives.xml", "ref/netstandard1.3/de/System.Net.Primitives.xml", "ref/netstandard1.3/es/System.Net.Primitives.xml", "ref/netstandard1.3/fr/System.Net.Primitives.xml", "ref/netstandard1.3/it/System.Net.Primitives.xml", "ref/netstandard1.3/ja/System.Net.Primitives.xml", "ref/netstandard1.3/ko/System.Net.Primitives.xml", "ref/netstandard1.3/ru/System.Net.Primitives.xml", "ref/netstandard1.3/zh-hans/System.Net.Primitives.xml", "ref/netstandard1.3/zh-hant/System.Net.Primitives.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.net.primitives.4.3.0.nupkg.sha512", "system.net.primitives.nuspec"]}, "System.Net.Sockets/4.3.0": {"sha512": "m6icV6TqQOAdgt5N/9I5KNpjom/5NFtkmGseEH+AK/hny8XrytLH3+b5M8zL/Ycg3fhIocFpUMyl/wpFnVRvdw==", "type": "package", "path": "system.net.sockets/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Net.Sockets.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Net.Sockets.dll", "ref/netstandard1.3/System.Net.Sockets.dll", "ref/netstandard1.3/System.Net.Sockets.xml", "ref/netstandard1.3/de/System.Net.Sockets.xml", "ref/netstandard1.3/es/System.Net.Sockets.xml", "ref/netstandard1.3/fr/System.Net.Sockets.xml", "ref/netstandard1.3/it/System.Net.Sockets.xml", "ref/netstandard1.3/ja/System.Net.Sockets.xml", "ref/netstandard1.3/ko/System.Net.Sockets.xml", "ref/netstandard1.3/ru/System.Net.Sockets.xml", "ref/netstandard1.3/zh-hans/System.Net.Sockets.xml", "ref/netstandard1.3/zh-hant/System.Net.Sockets.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.net.sockets.4.3.0.nupkg.sha512", "system.net.sockets.nuspec"]}, "System.Numerics.Vectors/4.5.0": {"sha512": "QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "type": "package", "path": "system.numerics.vectors/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Numerics.Vectors.dll", "lib/net46/System.Numerics.Vectors.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.Numerics.Vectors.dll", "lib/netstandard1.0/System.Numerics.Vectors.xml", "lib/netstandard2.0/System.Numerics.Vectors.dll", "lib/netstandard2.0/System.Numerics.Vectors.xml", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.dll", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/System.Numerics.Vectors.dll", "ref/net45/System.Numerics.Vectors.xml", "ref/net46/System.Numerics.Vectors.dll", "ref/net46/System.Numerics.Vectors.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/System.Numerics.Vectors.dll", "ref/netstandard1.0/System.Numerics.Vectors.xml", "ref/netstandard2.0/System.Numerics.Vectors.dll", "ref/netstandard2.0/System.Numerics.Vectors.xml", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.numerics.vectors.4.5.0.nupkg.sha512", "system.numerics.vectors.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.ObjectModel/4.3.0": {"sha512": "bdX+80eKv9bN6K4N+d77OankKHGn6CH711a6fcOpMQu2Fckp/Ft4L/kW9WznHpyR0NRAvJutzOMHNNlBGvxQzQ==", "type": "package", "path": "system.objectmodel/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.ObjectModel.dll", "lib/netstandard1.3/System.ObjectModel.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.ObjectModel.dll", "ref/netcore50/System.ObjectModel.xml", "ref/netcore50/de/System.ObjectModel.xml", "ref/netcore50/es/System.ObjectModel.xml", "ref/netcore50/fr/System.ObjectModel.xml", "ref/netcore50/it/System.ObjectModel.xml", "ref/netcore50/ja/System.ObjectModel.xml", "ref/netcore50/ko/System.ObjectModel.xml", "ref/netcore50/ru/System.ObjectModel.xml", "ref/netcore50/zh-hans/System.ObjectModel.xml", "ref/netcore50/zh-hant/System.ObjectModel.xml", "ref/netstandard1.0/System.ObjectModel.dll", "ref/netstandard1.0/System.ObjectModel.xml", "ref/netstandard1.0/de/System.ObjectModel.xml", "ref/netstandard1.0/es/System.ObjectModel.xml", "ref/netstandard1.0/fr/System.ObjectModel.xml", "ref/netstandard1.0/it/System.ObjectModel.xml", "ref/netstandard1.0/ja/System.ObjectModel.xml", "ref/netstandard1.0/ko/System.ObjectModel.xml", "ref/netstandard1.0/ru/System.ObjectModel.xml", "ref/netstandard1.0/zh-hans/System.ObjectModel.xml", "ref/netstandard1.0/zh-hant/System.ObjectModel.xml", "ref/netstandard1.3/System.ObjectModel.dll", "ref/netstandard1.3/System.ObjectModel.xml", "ref/netstandard1.3/de/System.ObjectModel.xml", "ref/netstandard1.3/es/System.ObjectModel.xml", "ref/netstandard1.3/fr/System.ObjectModel.xml", "ref/netstandard1.3/it/System.ObjectModel.xml", "ref/netstandard1.3/ja/System.ObjectModel.xml", "ref/netstandard1.3/ko/System.ObjectModel.xml", "ref/netstandard1.3/ru/System.ObjectModel.xml", "ref/netstandard1.3/zh-hans/System.ObjectModel.xml", "ref/netstandard1.3/zh-hant/System.ObjectModel.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.objectmodel.4.3.0.nupkg.sha512", "system.objectmodel.nuspec"]}, "System.Private.DataContractSerialization/4.3.0": {"sha512": "yDaJ2x3mMmjdZEDB4IbezSnCsnjQ4BxinKhRAaP6kEgL6Bb6jANWphs5SzyD8imqeC/3FxgsuXT6ykkiH1uUmA==", "type": "package", "path": "system.private.datacontractserialization/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.3/System.Private.DataContractSerialization.dll", "ref/netstandard/_._", "runtimes/aot/lib/netcore50/System.Private.DataContractSerialization.dll", "system.private.datacontractserialization.4.3.0.nupkg.sha512", "system.private.datacontractserialization.nuspec"]}, "System.Private.Uri/4.3.2": {"sha512": "o1+7RJnu3Ik3PazR7Z7tJhjPdE000Eq2KGLLWhqJJKXj04wrS8lwb1OFtDF9jzXXADhUuZNJZlPc98uwwqmpFA==", "type": "package", "path": "system.private.uri/4.3.2", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "ref/netstandard/_._", "system.private.uri.4.3.2.nupkg.sha512", "system.private.uri.nuspec"]}, "System.Reflection/4.3.0": {"sha512": "KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "type": "package", "path": "system.reflection/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Reflection.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Reflection.dll", "ref/netcore50/System.Reflection.dll", "ref/netcore50/System.Reflection.xml", "ref/netcore50/de/System.Reflection.xml", "ref/netcore50/es/System.Reflection.xml", "ref/netcore50/fr/System.Reflection.xml", "ref/netcore50/it/System.Reflection.xml", "ref/netcore50/ja/System.Reflection.xml", "ref/netcore50/ko/System.Reflection.xml", "ref/netcore50/ru/System.Reflection.xml", "ref/netcore50/zh-hans/System.Reflection.xml", "ref/netcore50/zh-hant/System.Reflection.xml", "ref/netstandard1.0/System.Reflection.dll", "ref/netstandard1.0/System.Reflection.xml", "ref/netstandard1.0/de/System.Reflection.xml", "ref/netstandard1.0/es/System.Reflection.xml", "ref/netstandard1.0/fr/System.Reflection.xml", "ref/netstandard1.0/it/System.Reflection.xml", "ref/netstandard1.0/ja/System.Reflection.xml", "ref/netstandard1.0/ko/System.Reflection.xml", "ref/netstandard1.0/ru/System.Reflection.xml", "ref/netstandard1.0/zh-hans/System.Reflection.xml", "ref/netstandard1.0/zh-hant/System.Reflection.xml", "ref/netstandard1.3/System.Reflection.dll", "ref/netstandard1.3/System.Reflection.xml", "ref/netstandard1.3/de/System.Reflection.xml", "ref/netstandard1.3/es/System.Reflection.xml", "ref/netstandard1.3/fr/System.Reflection.xml", "ref/netstandard1.3/it/System.Reflection.xml", "ref/netstandard1.3/ja/System.Reflection.xml", "ref/netstandard1.3/ko/System.Reflection.xml", "ref/netstandard1.3/ru/System.Reflection.xml", "ref/netstandard1.3/zh-hans/System.Reflection.xml", "ref/netstandard1.3/zh-hant/System.Reflection.xml", "ref/netstandard1.5/System.Reflection.dll", "ref/netstandard1.5/System.Reflection.xml", "ref/netstandard1.5/de/System.Reflection.xml", "ref/netstandard1.5/es/System.Reflection.xml", "ref/netstandard1.5/fr/System.Reflection.xml", "ref/netstandard1.5/it/System.Reflection.xml", "ref/netstandard1.5/ja/System.Reflection.xml", "ref/netstandard1.5/ko/System.Reflection.xml", "ref/netstandard1.5/ru/System.Reflection.xml", "ref/netstandard1.5/zh-hans/System.Reflection.xml", "ref/netstandard1.5/zh-hant/System.Reflection.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.4.3.0.nupkg.sha512", "system.reflection.nuspec"]}, "System.Reflection.Emit/4.7.0": {"sha512": "VR4kk8XLKebQ4MZuKuIni/7oh+QGFmZW3qORd1GvBq/8026OpW501SzT/oypwiQl4TvT8ErnReh/NzY9u+C6wQ==", "type": "package", "path": "system.reflection.emit/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Reflection.Emit.dll", "lib/netcoreapp2.0/_._", "lib/netstandard1.1/System.Reflection.Emit.dll", "lib/netstandard1.1/System.Reflection.Emit.xml", "lib/netstandard1.3/System.Reflection.Emit.dll", "lib/netstandard2.0/System.Reflection.Emit.dll", "lib/netstandard2.0/System.Reflection.Emit.xml", "lib/netstandard2.1/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcoreapp2.0/_._", "ref/netstandard1.1/System.Reflection.Emit.dll", "ref/netstandard1.1/System.Reflection.Emit.xml", "ref/netstandard1.1/de/System.Reflection.Emit.xml", "ref/netstandard1.1/es/System.Reflection.Emit.xml", "ref/netstandard1.1/fr/System.Reflection.Emit.xml", "ref/netstandard1.1/it/System.Reflection.Emit.xml", "ref/netstandard1.1/ja/System.Reflection.Emit.xml", "ref/netstandard1.1/ko/System.Reflection.Emit.xml", "ref/netstandard1.1/ru/System.Reflection.Emit.xml", "ref/netstandard1.1/zh-hans/System.Reflection.Emit.xml", "ref/netstandard1.1/zh-hant/System.Reflection.Emit.xml", "ref/netstandard2.0/System.Reflection.Emit.dll", "ref/netstandard2.0/System.Reflection.Emit.xml", "ref/netstandard2.1/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Reflection.Emit.dll", "runtimes/aot/lib/netcore50/System.Reflection.Emit.xml", "system.reflection.emit.4.7.0.nupkg.sha512", "system.reflection.emit.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Reflection.Emit.ILGeneration/4.3.0": {"sha512": "59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "type": "package", "path": "system.reflection.emit.ilgeneration/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Reflection.Emit.ILGeneration.dll", "lib/netstandard1.3/System.Reflection.Emit.ILGeneration.dll", "lib/portable-net45+wp8/_._", "lib/wp80/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netstandard1.0/System.Reflection.Emit.ILGeneration.dll", "ref/netstandard1.0/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/de/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/es/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/fr/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/it/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/ja/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/ko/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/ru/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Emit.ILGeneration.xml", "ref/portable-net45+wp8/_._", "ref/wp80/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/_._", "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512", "system.reflection.emit.ilgeneration.nuspec"]}, "System.Reflection.Emit.Lightweight/4.3.0": {"sha512": "oadVHGSMsTmZsAF864QYN1t1QzZjIcuKU3l2S9cZOwDdDueNTrqq1yRj7koFfIGEnKpt6NjpL3rOzRhs4ryOgA==", "type": "package", "path": "system.reflection.emit.lightweight/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Reflection.Emit.Lightweight.dll", "lib/netstandard1.3/System.Reflection.Emit.Lightweight.dll", "lib/portable-net45+wp8/_._", "lib/wp80/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netstandard1.0/System.Reflection.Emit.Lightweight.dll", "ref/netstandard1.0/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/de/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/es/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/fr/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/it/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/ja/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/ko/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/ru/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Emit.Lightweight.xml", "ref/portable-net45+wp8/_._", "ref/wp80/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/_._", "system.reflection.emit.lightweight.4.3.0.nupkg.sha512", "system.reflection.emit.lightweight.nuspec"]}, "System.Reflection.Extensions/4.3.0": {"sha512": "rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "type": "package", "path": "system.reflection.extensions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Reflection.Extensions.dll", "ref/netcore50/System.Reflection.Extensions.xml", "ref/netcore50/de/System.Reflection.Extensions.xml", "ref/netcore50/es/System.Reflection.Extensions.xml", "ref/netcore50/fr/System.Reflection.Extensions.xml", "ref/netcore50/it/System.Reflection.Extensions.xml", "ref/netcore50/ja/System.Reflection.Extensions.xml", "ref/netcore50/ko/System.Reflection.Extensions.xml", "ref/netcore50/ru/System.Reflection.Extensions.xml", "ref/netcore50/zh-hans/System.Reflection.Extensions.xml", "ref/netcore50/zh-hant/System.Reflection.Extensions.xml", "ref/netstandard1.0/System.Reflection.Extensions.dll", "ref/netstandard1.0/System.Reflection.Extensions.xml", "ref/netstandard1.0/de/System.Reflection.Extensions.xml", "ref/netstandard1.0/es/System.Reflection.Extensions.xml", "ref/netstandard1.0/fr/System.Reflection.Extensions.xml", "ref/netstandard1.0/it/System.Reflection.Extensions.xml", "ref/netstandard1.0/ja/System.Reflection.Extensions.xml", "ref/netstandard1.0/ko/System.Reflection.Extensions.xml", "ref/netstandard1.0/ru/System.Reflection.Extensions.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Extensions.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Extensions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.extensions.4.3.0.nupkg.sha512", "system.reflection.extensions.nuspec"]}, "System.Reflection.Metadata/1.6.0": {"sha512": "COC1aiAJjCoA5GBF+QKL2uLqEBew4JsCkQmoHKbN3TlOZKa2fKLz5CpiRQKDz0RsAOEGsVKqOD5bomsXq/4STQ==", "type": "package", "path": "system.reflection.metadata/1.6.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard1.1/System.Reflection.Metadata.dll", "lib/netstandard1.1/System.Reflection.Metadata.xml", "lib/netstandard2.0/System.Reflection.Metadata.dll", "lib/netstandard2.0/System.Reflection.Metadata.xml", "lib/portable-net45+win8/System.Reflection.Metadata.dll", "lib/portable-net45+win8/System.Reflection.Metadata.xml", "system.reflection.metadata.1.6.0.nupkg.sha512", "system.reflection.metadata.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Reflection.Primitives/4.3.0": {"sha512": "5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "type": "package", "path": "system.reflection.primitives/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Reflection.Primitives.dll", "ref/netcore50/System.Reflection.Primitives.xml", "ref/netcore50/de/System.Reflection.Primitives.xml", "ref/netcore50/es/System.Reflection.Primitives.xml", "ref/netcore50/fr/System.Reflection.Primitives.xml", "ref/netcore50/it/System.Reflection.Primitives.xml", "ref/netcore50/ja/System.Reflection.Primitives.xml", "ref/netcore50/ko/System.Reflection.Primitives.xml", "ref/netcore50/ru/System.Reflection.Primitives.xml", "ref/netcore50/zh-hans/System.Reflection.Primitives.xml", "ref/netcore50/zh-hant/System.Reflection.Primitives.xml", "ref/netstandard1.0/System.Reflection.Primitives.dll", "ref/netstandard1.0/System.Reflection.Primitives.xml", "ref/netstandard1.0/de/System.Reflection.Primitives.xml", "ref/netstandard1.0/es/System.Reflection.Primitives.xml", "ref/netstandard1.0/fr/System.Reflection.Primitives.xml", "ref/netstandard1.0/it/System.Reflection.Primitives.xml", "ref/netstandard1.0/ja/System.Reflection.Primitives.xml", "ref/netstandard1.0/ko/System.Reflection.Primitives.xml", "ref/netstandard1.0/ru/System.Reflection.Primitives.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Primitives.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Primitives.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.primitives.4.3.0.nupkg.sha512", "system.reflection.primitives.nuspec"]}, "System.Reflection.TypeExtensions/4.3.0": {"sha512": "7u6ulLcZbyxB5Gq0nMkQttcdBTx57ibzw+4IOXEfR+sXYQoHvjW5LTLyNr8O22UIMrqYbchJQJnos4eooYzYJA==", "type": "package", "path": "system.reflection.typeextensions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Reflection.TypeExtensions.dll", "lib/net462/System.Reflection.TypeExtensions.dll", "lib/netcore50/System.Reflection.TypeExtensions.dll", "lib/netstandard1.5/System.Reflection.TypeExtensions.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Reflection.TypeExtensions.dll", "ref/net462/System.Reflection.TypeExtensions.dll", "ref/netstandard1.3/System.Reflection.TypeExtensions.dll", "ref/netstandard1.3/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/de/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/es/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/fr/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/it/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/ja/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/ko/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/ru/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/zh-hans/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/zh-hant/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/System.Reflection.TypeExtensions.dll", "ref/netstandard1.5/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/de/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/es/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/fr/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/it/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/ja/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/ko/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/ru/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/zh-hans/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/zh-hant/System.Reflection.TypeExtensions.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Reflection.TypeExtensions.dll", "system.reflection.typeextensions.4.3.0.nupkg.sha512", "system.reflection.typeextensions.nuspec"]}, "System.Resources.ResourceManager/4.3.0": {"sha512": "/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "type": "package", "path": "system.resources.resourcemanager/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Resources.ResourceManager.dll", "ref/netcore50/System.Resources.ResourceManager.xml", "ref/netcore50/de/System.Resources.ResourceManager.xml", "ref/netcore50/es/System.Resources.ResourceManager.xml", "ref/netcore50/fr/System.Resources.ResourceManager.xml", "ref/netcore50/it/System.Resources.ResourceManager.xml", "ref/netcore50/ja/System.Resources.ResourceManager.xml", "ref/netcore50/ko/System.Resources.ResourceManager.xml", "ref/netcore50/ru/System.Resources.ResourceManager.xml", "ref/netcore50/zh-hans/System.Resources.ResourceManager.xml", "ref/netcore50/zh-hant/System.Resources.ResourceManager.xml", "ref/netstandard1.0/System.Resources.ResourceManager.dll", "ref/netstandard1.0/System.Resources.ResourceManager.xml", "ref/netstandard1.0/de/System.Resources.ResourceManager.xml", "ref/netstandard1.0/es/System.Resources.ResourceManager.xml", "ref/netstandard1.0/fr/System.Resources.ResourceManager.xml", "ref/netstandard1.0/it/System.Resources.ResourceManager.xml", "ref/netstandard1.0/ja/System.Resources.ResourceManager.xml", "ref/netstandard1.0/ko/System.Resources.ResourceManager.xml", "ref/netstandard1.0/ru/System.Resources.ResourceManager.xml", "ref/netstandard1.0/zh-hans/System.Resources.ResourceManager.xml", "ref/netstandard1.0/zh-hant/System.Resources.ResourceManager.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.resources.resourcemanager.4.3.0.nupkg.sha512", "system.resources.resourcemanager.nuspec"]}, "System.Runtime/4.3.0": {"sha512": "JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "type": "package", "path": "system.runtime/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.dll", "lib/portable-net45+win8+wp80+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.dll", "ref/netcore50/System.Runtime.dll", "ref/netcore50/System.Runtime.xml", "ref/netcore50/de/System.Runtime.xml", "ref/netcore50/es/System.Runtime.xml", "ref/netcore50/fr/System.Runtime.xml", "ref/netcore50/it/System.Runtime.xml", "ref/netcore50/ja/System.Runtime.xml", "ref/netcore50/ko/System.Runtime.xml", "ref/netcore50/ru/System.Runtime.xml", "ref/netcore50/zh-hans/System.Runtime.xml", "ref/netcore50/zh-hant/System.Runtime.xml", "ref/netstandard1.0/System.Runtime.dll", "ref/netstandard1.0/System.Runtime.xml", "ref/netstandard1.0/de/System.Runtime.xml", "ref/netstandard1.0/es/System.Runtime.xml", "ref/netstandard1.0/fr/System.Runtime.xml", "ref/netstandard1.0/it/System.Runtime.xml", "ref/netstandard1.0/ja/System.Runtime.xml", "ref/netstandard1.0/ko/System.Runtime.xml", "ref/netstandard1.0/ru/System.Runtime.xml", "ref/netstandard1.0/zh-hans/System.Runtime.xml", "ref/netstandard1.0/zh-hant/System.Runtime.xml", "ref/netstandard1.2/System.Runtime.dll", "ref/netstandard1.2/System.Runtime.xml", "ref/netstandard1.2/de/System.Runtime.xml", "ref/netstandard1.2/es/System.Runtime.xml", "ref/netstandard1.2/fr/System.Runtime.xml", "ref/netstandard1.2/it/System.Runtime.xml", "ref/netstandard1.2/ja/System.Runtime.xml", "ref/netstandard1.2/ko/System.Runtime.xml", "ref/netstandard1.2/ru/System.Runtime.xml", "ref/netstandard1.2/zh-hans/System.Runtime.xml", "ref/netstandard1.2/zh-hant/System.Runtime.xml", "ref/netstandard1.3/System.Runtime.dll", "ref/netstandard1.3/System.Runtime.xml", "ref/netstandard1.3/de/System.Runtime.xml", "ref/netstandard1.3/es/System.Runtime.xml", "ref/netstandard1.3/fr/System.Runtime.xml", "ref/netstandard1.3/it/System.Runtime.xml", "ref/netstandard1.3/ja/System.Runtime.xml", "ref/netstandard1.3/ko/System.Runtime.xml", "ref/netstandard1.3/ru/System.Runtime.xml", "ref/netstandard1.3/zh-hans/System.Runtime.xml", "ref/netstandard1.3/zh-hant/System.Runtime.xml", "ref/netstandard1.5/System.Runtime.dll", "ref/netstandard1.5/System.Runtime.xml", "ref/netstandard1.5/de/System.Runtime.xml", "ref/netstandard1.5/es/System.Runtime.xml", "ref/netstandard1.5/fr/System.Runtime.xml", "ref/netstandard1.5/it/System.Runtime.xml", "ref/netstandard1.5/ja/System.Runtime.xml", "ref/netstandard1.5/ko/System.Runtime.xml", "ref/netstandard1.5/ru/System.Runtime.xml", "ref/netstandard1.5/zh-hans/System.Runtime.xml", "ref/netstandard1.5/zh-hant/System.Runtime.xml", "ref/portable-net45+win8+wp80+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.4.3.0.nupkg.sha512", "system.runtime.nuspec"]}, "System.Runtime.Caching/4.7.0": {"sha512": "NdvNRjTPxYvIEhXQszT9L9vJhdQoX6AQ0AlhjTU+5NqFQVuacJTfhPVAvtGWNA2OJCqRiR/okBcZgMwI6MqcZg==", "type": "package", "path": "system.runtime.caching/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netstandard2.0/System.Runtime.Caching.dll", "lib/netstandard2.0/System.Runtime.Caching.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netstandard2.0/System.Runtime.Caching.dll", "ref/netstandard2.0/System.Runtime.Caching.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/win/lib/net45/_._", "runtimes/win/lib/netstandard2.0/System.Runtime.Caching.dll", "runtimes/win/lib/netstandard2.0/System.Runtime.Caching.xml", "system.runtime.caching.4.7.0.nupkg.sha512", "system.runtime.caching.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Runtime.Extensions/4.3.0": {"sha512": "guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "type": "package", "path": "system.runtime.extensions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.Extensions.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.Extensions.dll", "ref/netcore50/System.Runtime.Extensions.dll", "ref/netcore50/System.Runtime.Extensions.xml", "ref/netcore50/de/System.Runtime.Extensions.xml", "ref/netcore50/es/System.Runtime.Extensions.xml", "ref/netcore50/fr/System.Runtime.Extensions.xml", "ref/netcore50/it/System.Runtime.Extensions.xml", "ref/netcore50/ja/System.Runtime.Extensions.xml", "ref/netcore50/ko/System.Runtime.Extensions.xml", "ref/netcore50/ru/System.Runtime.Extensions.xml", "ref/netcore50/zh-hans/System.Runtime.Extensions.xml", "ref/netcore50/zh-hant/System.Runtime.Extensions.xml", "ref/netstandard1.0/System.Runtime.Extensions.dll", "ref/netstandard1.0/System.Runtime.Extensions.xml", "ref/netstandard1.0/de/System.Runtime.Extensions.xml", "ref/netstandard1.0/es/System.Runtime.Extensions.xml", "ref/netstandard1.0/fr/System.Runtime.Extensions.xml", "ref/netstandard1.0/it/System.Runtime.Extensions.xml", "ref/netstandard1.0/ja/System.Runtime.Extensions.xml", "ref/netstandard1.0/ko/System.Runtime.Extensions.xml", "ref/netstandard1.0/ru/System.Runtime.Extensions.xml", "ref/netstandard1.0/zh-hans/System.Runtime.Extensions.xml", "ref/netstandard1.0/zh-hant/System.Runtime.Extensions.xml", "ref/netstandard1.3/System.Runtime.Extensions.dll", "ref/netstandard1.3/System.Runtime.Extensions.xml", "ref/netstandard1.3/de/System.Runtime.Extensions.xml", "ref/netstandard1.3/es/System.Runtime.Extensions.xml", "ref/netstandard1.3/fr/System.Runtime.Extensions.xml", "ref/netstandard1.3/it/System.Runtime.Extensions.xml", "ref/netstandard1.3/ja/System.Runtime.Extensions.xml", "ref/netstandard1.3/ko/System.Runtime.Extensions.xml", "ref/netstandard1.3/ru/System.Runtime.Extensions.xml", "ref/netstandard1.3/zh-hans/System.Runtime.Extensions.xml", "ref/netstandard1.3/zh-hant/System.Runtime.Extensions.xml", "ref/netstandard1.5/System.Runtime.Extensions.dll", "ref/netstandard1.5/System.Runtime.Extensions.xml", "ref/netstandard1.5/de/System.Runtime.Extensions.xml", "ref/netstandard1.5/es/System.Runtime.Extensions.xml", "ref/netstandard1.5/fr/System.Runtime.Extensions.xml", "ref/netstandard1.5/it/System.Runtime.Extensions.xml", "ref/netstandard1.5/ja/System.Runtime.Extensions.xml", "ref/netstandard1.5/ko/System.Runtime.Extensions.xml", "ref/netstandard1.5/ru/System.Runtime.Extensions.xml", "ref/netstandard1.5/zh-hans/System.Runtime.Extensions.xml", "ref/netstandard1.5/zh-hant/System.Runtime.Extensions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.extensions.4.3.0.nupkg.sha512", "system.runtime.extensions.nuspec"]}, "System.Runtime.Handles/4.3.0": {"sha512": "OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "type": "package", "path": "system.runtime.handles/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/_._", "ref/netstandard1.3/System.Runtime.Handles.dll", "ref/netstandard1.3/System.Runtime.Handles.xml", "ref/netstandard1.3/de/System.Runtime.Handles.xml", "ref/netstandard1.3/es/System.Runtime.Handles.xml", "ref/netstandard1.3/fr/System.Runtime.Handles.xml", "ref/netstandard1.3/it/System.Runtime.Handles.xml", "ref/netstandard1.3/ja/System.Runtime.Handles.xml", "ref/netstandard1.3/ko/System.Runtime.Handles.xml", "ref/netstandard1.3/ru/System.Runtime.Handles.xml", "ref/netstandard1.3/zh-hans/System.Runtime.Handles.xml", "ref/netstandard1.3/zh-hant/System.Runtime.Handles.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.handles.4.3.0.nupkg.sha512", "system.runtime.handles.nuspec"]}, "System.Runtime.InteropServices/4.3.0": {"sha512": "uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "type": "package", "path": "system.runtime.interopservices/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.InteropServices.dll", "lib/net463/System.Runtime.InteropServices.dll", "lib/portable-net45+win8+wpa81/_._", "lib/win8/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.InteropServices.dll", "ref/net463/System.Runtime.InteropServices.dll", "ref/netcore50/System.Runtime.InteropServices.dll", "ref/netcore50/System.Runtime.InteropServices.xml", "ref/netcore50/de/System.Runtime.InteropServices.xml", "ref/netcore50/es/System.Runtime.InteropServices.xml", "ref/netcore50/fr/System.Runtime.InteropServices.xml", "ref/netcore50/it/System.Runtime.InteropServices.xml", "ref/netcore50/ja/System.Runtime.InteropServices.xml", "ref/netcore50/ko/System.Runtime.InteropServices.xml", "ref/netcore50/ru/System.Runtime.InteropServices.xml", "ref/netcore50/zh-hans/System.Runtime.InteropServices.xml", "ref/netcore50/zh-hant/System.Runtime.InteropServices.xml", "ref/netcoreapp1.1/System.Runtime.InteropServices.dll", "ref/netstandard1.1/System.Runtime.InteropServices.dll", "ref/netstandard1.1/System.Runtime.InteropServices.xml", "ref/netstandard1.1/de/System.Runtime.InteropServices.xml", "ref/netstandard1.1/es/System.Runtime.InteropServices.xml", "ref/netstandard1.1/fr/System.Runtime.InteropServices.xml", "ref/netstandard1.1/it/System.Runtime.InteropServices.xml", "ref/netstandard1.1/ja/System.Runtime.InteropServices.xml", "ref/netstandard1.1/ko/System.Runtime.InteropServices.xml", "ref/netstandard1.1/ru/System.Runtime.InteropServices.xml", "ref/netstandard1.1/zh-hans/System.Runtime.InteropServices.xml", "ref/netstandard1.1/zh-hant/System.Runtime.InteropServices.xml", "ref/netstandard1.2/System.Runtime.InteropServices.dll", "ref/netstandard1.2/System.Runtime.InteropServices.xml", "ref/netstandard1.2/de/System.Runtime.InteropServices.xml", "ref/netstandard1.2/es/System.Runtime.InteropServices.xml", "ref/netstandard1.2/fr/System.Runtime.InteropServices.xml", "ref/netstandard1.2/it/System.Runtime.InteropServices.xml", "ref/netstandard1.2/ja/System.Runtime.InteropServices.xml", "ref/netstandard1.2/ko/System.Runtime.InteropServices.xml", "ref/netstandard1.2/ru/System.Runtime.InteropServices.xml", "ref/netstandard1.2/zh-hans/System.Runtime.InteropServices.xml", "ref/netstandard1.2/zh-hant/System.Runtime.InteropServices.xml", "ref/netstandard1.3/System.Runtime.InteropServices.dll", "ref/netstandard1.3/System.Runtime.InteropServices.xml", "ref/netstandard1.3/de/System.Runtime.InteropServices.xml", "ref/netstandard1.3/es/System.Runtime.InteropServices.xml", "ref/netstandard1.3/fr/System.Runtime.InteropServices.xml", "ref/netstandard1.3/it/System.Runtime.InteropServices.xml", "ref/netstandard1.3/ja/System.Runtime.InteropServices.xml", "ref/netstandard1.3/ko/System.Runtime.InteropServices.xml", "ref/netstandard1.3/ru/System.Runtime.InteropServices.xml", "ref/netstandard1.3/zh-hans/System.Runtime.InteropServices.xml", "ref/netstandard1.3/zh-hant/System.Runtime.InteropServices.xml", "ref/netstandard1.5/System.Runtime.InteropServices.dll", "ref/netstandard1.5/System.Runtime.InteropServices.xml", "ref/netstandard1.5/de/System.Runtime.InteropServices.xml", "ref/netstandard1.5/es/System.Runtime.InteropServices.xml", "ref/netstandard1.5/fr/System.Runtime.InteropServices.xml", "ref/netstandard1.5/it/System.Runtime.InteropServices.xml", "ref/netstandard1.5/ja/System.Runtime.InteropServices.xml", "ref/netstandard1.5/ko/System.Runtime.InteropServices.xml", "ref/netstandard1.5/ru/System.Runtime.InteropServices.xml", "ref/netstandard1.5/zh-hans/System.Runtime.InteropServices.xml", "ref/netstandard1.5/zh-hant/System.Runtime.InteropServices.xml", "ref/portable-net45+win8+wpa81/_._", "ref/win8/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.interopservices.4.3.0.nupkg.sha512", "system.runtime.interopservices.nuspec"]}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"sha512": "cbz4YJMqRDR7oLeMRbdYv7mYzc++17lNhScCX0goO2XpGWdvAt60CGN+FHdePUEHCe/Jy9jUlvNAiNdM+7jsOw==", "type": "package", "path": "system.runtime.interopservices.runtimeinformation/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/System.Runtime.InteropServices.RuntimeInformation.dll", "lib/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll", "lib/win8/System.Runtime.InteropServices.RuntimeInformation.dll", "lib/wpa81/System.Runtime.InteropServices.RuntimeInformation.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Runtime.InteropServices.RuntimeInformation.dll", "runtimes/unix/lib/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll", "runtimes/win/lib/net45/System.Runtime.InteropServices.RuntimeInformation.dll", "runtimes/win/lib/netcore50/System.Runtime.InteropServices.RuntimeInformation.dll", "runtimes/win/lib/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll", "system.runtime.interopservices.runtimeinformation.4.3.0.nupkg.sha512", "system.runtime.interopservices.runtimeinformation.nuspec"]}, "System.Runtime.Numerics/4.3.0": {"sha512": "yMH+MfdzHjy17l2KESnPiF2dwq7T+xLnSJar7slyimAkUh/gTrS9/UQOtv7xarskJ2/XDSNvfLGOBQPjL7PaHQ==", "type": "package", "path": "system.runtime.numerics/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Runtime.Numerics.dll", "lib/netstandard1.3/System.Runtime.Numerics.dll", "lib/portable-net45+win8+wpa81/_._", "lib/win8/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Runtime.Numerics.dll", "ref/netcore50/System.Runtime.Numerics.xml", "ref/netcore50/de/System.Runtime.Numerics.xml", "ref/netcore50/es/System.Runtime.Numerics.xml", "ref/netcore50/fr/System.Runtime.Numerics.xml", "ref/netcore50/it/System.Runtime.Numerics.xml", "ref/netcore50/ja/System.Runtime.Numerics.xml", "ref/netcore50/ko/System.Runtime.Numerics.xml", "ref/netcore50/ru/System.Runtime.Numerics.xml", "ref/netcore50/zh-hans/System.Runtime.Numerics.xml", "ref/netcore50/zh-hant/System.Runtime.Numerics.xml", "ref/netstandard1.1/System.Runtime.Numerics.dll", "ref/netstandard1.1/System.Runtime.Numerics.xml", "ref/netstandard1.1/de/System.Runtime.Numerics.xml", "ref/netstandard1.1/es/System.Runtime.Numerics.xml", "ref/netstandard1.1/fr/System.Runtime.Numerics.xml", "ref/netstandard1.1/it/System.Runtime.Numerics.xml", "ref/netstandard1.1/ja/System.Runtime.Numerics.xml", "ref/netstandard1.1/ko/System.Runtime.Numerics.xml", "ref/netstandard1.1/ru/System.Runtime.Numerics.xml", "ref/netstandard1.1/zh-hans/System.Runtime.Numerics.xml", "ref/netstandard1.1/zh-hant/System.Runtime.Numerics.xml", "ref/portable-net45+win8+wpa81/_._", "ref/win8/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.numerics.4.3.0.nupkg.sha512", "system.runtime.numerics.nuspec"]}, "System.Runtime.Serialization.Formatters/4.3.0": {"sha512": "KT591AkTNFOTbhZlaeMVvfax3RqhH1EJlcwF50Wm7sfnBLuHiOeZRRKrr1ns3NESkM20KPZ5Ol/ueMq5vg4QoQ==", "type": "package", "path": "system.runtime.serialization.formatters/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Runtime.Serialization.Formatters.dll", "lib/netstandard1.4/System.Runtime.Serialization.Formatters.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Runtime.Serialization.Formatters.dll", "ref/netstandard1.3/System.Runtime.Serialization.Formatters.dll", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.serialization.formatters.4.3.0.nupkg.sha512", "system.runtime.serialization.formatters.nuspec"]}, "System.Runtime.Serialization.Json/4.3.0": {"sha512": "CpVfOH0M/uZ5PH+M9+Gu56K0j9lJw3M+PKRegTkcrY/stOIvRUeonggxNrfBYLA5WOHL2j15KNJuTuld3x4o9w==", "type": "package", "path": "system.runtime.serialization.json/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Runtime.Serialization.Json.dll", "lib/netstandard1.3/System.Runtime.Serialization.Json.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Runtime.Serialization.Json.dll", "ref/netcore50/System.Runtime.Serialization.Json.xml", "ref/netcore50/de/System.Runtime.Serialization.Json.xml", "ref/netcore50/es/System.Runtime.Serialization.Json.xml", "ref/netcore50/fr/System.Runtime.Serialization.Json.xml", "ref/netcore50/it/System.Runtime.Serialization.Json.xml", "ref/netcore50/ja/System.Runtime.Serialization.Json.xml", "ref/netcore50/ko/System.Runtime.Serialization.Json.xml", "ref/netcore50/ru/System.Runtime.Serialization.Json.xml", "ref/netcore50/zh-hans/System.Runtime.Serialization.Json.xml", "ref/netcore50/zh-hant/System.Runtime.Serialization.Json.xml", "ref/netstandard1.0/System.Runtime.Serialization.Json.dll", "ref/netstandard1.0/System.Runtime.Serialization.Json.xml", "ref/netstandard1.0/de/System.Runtime.Serialization.Json.xml", "ref/netstandard1.0/es/System.Runtime.Serialization.Json.xml", "ref/netstandard1.0/fr/System.Runtime.Serialization.Json.xml", "ref/netstandard1.0/it/System.Runtime.Serialization.Json.xml", "ref/netstandard1.0/ja/System.Runtime.Serialization.Json.xml", "ref/netstandard1.0/ko/System.Runtime.Serialization.Json.xml", "ref/netstandard1.0/ru/System.Runtime.Serialization.Json.xml", "ref/netstandard1.0/zh-hans/System.Runtime.Serialization.Json.xml", "ref/netstandard1.0/zh-hant/System.Runtime.Serialization.Json.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.serialization.json.4.3.0.nupkg.sha512", "system.runtime.serialization.json.nuspec"]}, "System.Runtime.Serialization.Primitives/4.3.0": {"sha512": "Wz+0KOukJGAlXjtKr+5Xpuxf8+c8739RI1C+A2BoQZT+wMCCoMDDdO8/4IRHfaVINqL78GO8dW8G2lW/e45Mcw==", "type": "package", "path": "system.runtime.serialization.primitives/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net46/System.Runtime.Serialization.Primitives.dll", "lib/netcore50/System.Runtime.Serialization.Primitives.dll", "lib/netstandard1.3/System.Runtime.Serialization.Primitives.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net46/System.Runtime.Serialization.Primitives.dll", "ref/netcore50/System.Runtime.Serialization.Primitives.dll", "ref/netcore50/System.Runtime.Serialization.Primitives.xml", "ref/netcore50/de/System.Runtime.Serialization.Primitives.xml", "ref/netcore50/es/System.Runtime.Serialization.Primitives.xml", "ref/netcore50/fr/System.Runtime.Serialization.Primitives.xml", "ref/netcore50/it/System.Runtime.Serialization.Primitives.xml", "ref/netcore50/ja/System.Runtime.Serialization.Primitives.xml", "ref/netcore50/ko/System.Runtime.Serialization.Primitives.xml", "ref/netcore50/ru/System.Runtime.Serialization.Primitives.xml", "ref/netcore50/zh-hans/System.Runtime.Serialization.Primitives.xml", "ref/netcore50/zh-hant/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/System.Runtime.Serialization.Primitives.dll", "ref/netstandard1.0/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/de/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/es/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/fr/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/it/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/ja/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/ko/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/ru/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/zh-hans/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/zh-hant/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/System.Runtime.Serialization.Primitives.dll", "ref/netstandard1.3/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/de/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/es/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/fr/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/it/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/ja/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/ko/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/ru/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/zh-hans/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/zh-hant/System.Runtime.Serialization.Primitives.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Runtime.Serialization.Primitives.dll", "system.runtime.serialization.primitives.4.3.0.nupkg.sha512", "system.runtime.serialization.primitives.nuspec"]}, "System.Security.AccessControl/4.7.0": {"sha512": "JECvTt5aFF3WT3gHpfofL2MNNP6v84sxtXxpqhLBCcDRzqsPBmHhQ6shv4DwwN2tRlzsUxtb3G9M3763rbXKDg==", "type": "package", "path": "system.security.accesscontrol/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.xml", "lib/netstandard1.3/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.xml", "lib/uap10.0.16299/_._", "ref/net46/System.Security.AccessControl.dll", "ref/net461/System.Security.AccessControl.dll", "ref/net461/System.Security.AccessControl.xml", "ref/netstandard1.3/System.Security.AccessControl.dll", "ref/netstandard1.3/System.Security.AccessControl.xml", "ref/netstandard1.3/de/System.Security.AccessControl.xml", "ref/netstandard1.3/es/System.Security.AccessControl.xml", "ref/netstandard1.3/fr/System.Security.AccessControl.xml", "ref/netstandard1.3/it/System.Security.AccessControl.xml", "ref/netstandard1.3/ja/System.Security.AccessControl.xml", "ref/netstandard1.3/ko/System.Security.AccessControl.xml", "ref/netstandard1.3/ru/System.Security.AccessControl.xml", "ref/netstandard1.3/zh-hans/System.Security.AccessControl.xml", "ref/netstandard1.3/zh-hant/System.Security.AccessControl.xml", "ref/netstandard2.0/System.Security.AccessControl.dll", "ref/netstandard2.0/System.Security.AccessControl.xml", "ref/uap10.0.16299/_._", "runtimes/win/lib/net46/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.xml", "runtimes/win/lib/netstandard1.3/System.Security.AccessControl.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.accesscontrol.4.7.0.nupkg.sha512", "system.security.accesscontrol.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.Claims/4.3.0": {"sha512": "P/+BR/2lnc4PNDHt/TPBAWHVMLMRHsyYZbU1NphW4HIWzCggz8mJbTQQ3MKljFE7LS3WagmVFuBgoLcFzYXlkA==", "type": "package", "path": "system.security.claims/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Claims.dll", "lib/netstandard1.3/System.Security.Claims.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Claims.dll", "ref/netstandard1.3/System.Security.Claims.dll", "ref/netstandard1.3/System.Security.Claims.xml", "ref/netstandard1.3/de/System.Security.Claims.xml", "ref/netstandard1.3/es/System.Security.Claims.xml", "ref/netstandard1.3/fr/System.Security.Claims.xml", "ref/netstandard1.3/it/System.Security.Claims.xml", "ref/netstandard1.3/ja/System.Security.Claims.xml", "ref/netstandard1.3/ko/System.Security.Claims.xml", "ref/netstandard1.3/ru/System.Security.Claims.xml", "ref/netstandard1.3/zh-hans/System.Security.Claims.xml", "ref/netstandard1.3/zh-hant/System.Security.Claims.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.security.claims.4.3.0.nupkg.sha512", "system.security.claims.nuspec"]}, "System.Security.Cryptography.Algorithms/4.3.0": {"sha512": "W1kd2Y8mYSCgc3ULTAZ0hOP2dSdG5YauTb1089T0/kRcN2MpSAW1izOFROrJgxSlMn3ArsgHXagigyi+ibhevg==", "type": "package", "path": "system.security.cryptography.algorithms/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.Algorithms.dll", "lib/net461/System.Security.Cryptography.Algorithms.dll", "lib/net463/System.Security.Cryptography.Algorithms.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.Algorithms.dll", "ref/net461/System.Security.Cryptography.Algorithms.dll", "ref/net463/System.Security.Cryptography.Algorithms.dll", "ref/netstandard1.3/System.Security.Cryptography.Algorithms.dll", "ref/netstandard1.4/System.Security.Cryptography.Algorithms.dll", "ref/netstandard1.6/System.Security.Cryptography.Algorithms.dll", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/osx/lib/netstandard1.6/System.Security.Cryptography.Algorithms.dll", "runtimes/unix/lib/netstandard1.6/System.Security.Cryptography.Algorithms.dll", "runtimes/win/lib/net46/System.Security.Cryptography.Algorithms.dll", "runtimes/win/lib/net461/System.Security.Cryptography.Algorithms.dll", "runtimes/win/lib/net463/System.Security.Cryptography.Algorithms.dll", "runtimes/win/lib/netcore50/System.Security.Cryptography.Algorithms.dll", "runtimes/win/lib/netstandard1.6/System.Security.Cryptography.Algorithms.dll", "system.security.cryptography.algorithms.4.3.0.nupkg.sha512", "system.security.cryptography.algorithms.nuspec"]}, "System.Security.Cryptography.Cng/5.0.0": {"sha512": "jIMXsKn94T9JY7PvPq/tMfqa6GAaHpElRDpmG+SuL+D3+sTw2M8VhnibKnN8Tq+4JqbPJ/f+BwtLeDMEnzAvRg==", "type": "package", "path": "system.security.cryptography.cng/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.Cng.dll", "lib/net461/System.Security.Cryptography.Cng.dll", "lib/net461/System.Security.Cryptography.Cng.xml", "lib/net462/System.Security.Cryptography.Cng.dll", "lib/net462/System.Security.Cryptography.Cng.xml", "lib/net47/System.Security.Cryptography.Cng.dll", "lib/net47/System.Security.Cryptography.Cng.xml", "lib/netcoreapp2.1/System.Security.Cryptography.Cng.dll", "lib/netcoreapp3.0/System.Security.Cryptography.Cng.dll", "lib/netcoreapp3.0/System.Security.Cryptography.Cng.xml", "lib/netstandard1.3/System.Security.Cryptography.Cng.dll", "lib/netstandard1.4/System.Security.Cryptography.Cng.dll", "lib/netstandard1.6/System.Security.Cryptography.Cng.dll", "lib/netstandard2.0/System.Security.Cryptography.Cng.dll", "lib/netstandard2.0/System.Security.Cryptography.Cng.xml", "lib/netstandard2.1/System.Security.Cryptography.Cng.dll", "lib/netstandard2.1/System.Security.Cryptography.Cng.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.Cng.dll", "ref/net461/System.Security.Cryptography.Cng.dll", "ref/net461/System.Security.Cryptography.Cng.xml", "ref/net462/System.Security.Cryptography.Cng.dll", "ref/net462/System.Security.Cryptography.Cng.xml", "ref/net47/System.Security.Cryptography.Cng.dll", "ref/net47/System.Security.Cryptography.Cng.xml", "ref/netcoreapp2.0/System.Security.Cryptography.Cng.dll", "ref/netcoreapp2.0/System.Security.Cryptography.Cng.xml", "ref/netcoreapp2.1/System.Security.Cryptography.Cng.dll", "ref/netcoreapp2.1/System.Security.Cryptography.Cng.xml", "ref/netcoreapp3.0/System.Security.Cryptography.Cng.dll", "ref/netcoreapp3.0/System.Security.Cryptography.Cng.xml", "ref/netstandard1.3/System.Security.Cryptography.Cng.dll", "ref/netstandard1.4/System.Security.Cryptography.Cng.dll", "ref/netstandard1.6/System.Security.Cryptography.Cng.dll", "ref/netstandard2.0/System.Security.Cryptography.Cng.dll", "ref/netstandard2.0/System.Security.Cryptography.Cng.xml", "ref/netstandard2.1/System.Security.Cryptography.Cng.dll", "ref/netstandard2.1/System.Security.Cryptography.Cng.xml", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/win/lib/net46/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/net461/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/net461/System.Security.Cryptography.Cng.xml", "runtimes/win/lib/net462/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/net462/System.Security.Cryptography.Cng.xml", "runtimes/win/lib/net47/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/net47/System.Security.Cryptography.Cng.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/netcoreapp2.1/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/netcoreapp3.0/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/netcoreapp3.0/System.Security.Cryptography.Cng.xml", "runtimes/win/lib/netstandard1.4/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/netstandard1.6/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.cryptography.cng.5.0.0.nupkg.sha512", "system.security.cryptography.cng.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.Cryptography.Csp/4.3.0": {"sha512": "X4s/FCkEUnRGnwR3aSfVIkldBmtURMhmexALNTwpjklzxWU7yjMk7GHLKOZTNkgnWnE0q7+BCf9N2LVRWxewaA==", "type": "package", "path": "system.security.cryptography.csp/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.Csp.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.Csp.dll", "ref/netstandard1.3/System.Security.Cryptography.Csp.dll", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netstandard1.3/System.Security.Cryptography.Csp.dll", "runtimes/win/lib/net46/System.Security.Cryptography.Csp.dll", "runtimes/win/lib/netcore50/_._", "runtimes/win/lib/netstandard1.3/System.Security.Cryptography.Csp.dll", "system.security.cryptography.csp.4.3.0.nupkg.sha512", "system.security.cryptography.csp.nuspec"]}, "System.Security.Cryptography.Encoding/4.3.0": {"sha512": "1DEWjZZly9ae9C79vFwqaO5kaOlI5q+3/55ohmq/7dpDyDfc8lYe7YVxJUZ5MF/NtbkRjwFRo14yM4OEo9EmDw==", "type": "package", "path": "system.security.cryptography.encoding/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.Encoding.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.Encoding.dll", "ref/netstandard1.3/System.Security.Cryptography.Encoding.dll", "ref/netstandard1.3/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/de/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/es/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/fr/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/it/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/ja/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/ko/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/ru/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/zh-hans/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/zh-hant/System.Security.Cryptography.Encoding.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netstandard1.3/System.Security.Cryptography.Encoding.dll", "runtimes/win/lib/net46/System.Security.Cryptography.Encoding.dll", "runtimes/win/lib/netstandard1.3/System.Security.Cryptography.Encoding.dll", "system.security.cryptography.encoding.4.3.0.nupkg.sha512", "system.security.cryptography.encoding.nuspec"]}, "System.Security.Cryptography.OpenSsl/4.3.0": {"sha512": "h4CEgOgv5PKVF/HwaHzJRiVboL2THYCou97zpmhjghx5frc7fIvlkY1jL+lnIQyChrJDMNEXS6r7byGif8Cy4w==", "type": "package", "path": "system.security.cryptography.openssl/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.6/System.Security.Cryptography.OpenSsl.dll", "ref/netstandard1.6/System.Security.Cryptography.OpenSsl.dll", "runtimes/unix/lib/netstandard1.6/System.Security.Cryptography.OpenSsl.dll", "system.security.cryptography.openssl.4.3.0.nupkg.sha512", "system.security.cryptography.openssl.nuspec"]}, "System.Security.Cryptography.Pkcs/5.0.0": {"sha512": "9TPLGjBCGKmNvG8pjwPeuYy0SMVmGZRwlTZvyPHDbYv/DRkoeumJdfumaaDNQzVGMEmbWtg07zUpSW9q70IlDQ==", "type": "package", "path": "system.security.cryptography.pkcs/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.Cryptography.Pkcs.dll", "lib/net461/System.Security.Cryptography.Pkcs.dll", "lib/net461/System.Security.Cryptography.Pkcs.xml", "lib/netcoreapp2.1/System.Security.Cryptography.Pkcs.dll", "lib/netcoreapp3.0/System.Security.Cryptography.Pkcs.dll", "lib/netcoreapp3.0/System.Security.Cryptography.Pkcs.xml", "lib/netstandard1.3/System.Security.Cryptography.Pkcs.dll", "lib/netstandard2.0/System.Security.Cryptography.Pkcs.dll", "lib/netstandard2.0/System.Security.Cryptography.Pkcs.xml", "lib/netstandard2.1/System.Security.Cryptography.Pkcs.dll", "lib/netstandard2.1/System.Security.Cryptography.Pkcs.xml", "ref/net46/System.Security.Cryptography.Pkcs.dll", "ref/net461/System.Security.Cryptography.Pkcs.dll", "ref/net461/System.Security.Cryptography.Pkcs.xml", "ref/netcoreapp2.1/System.Security.Cryptography.Pkcs.dll", "ref/netcoreapp2.1/System.Security.Cryptography.Pkcs.xml", "ref/netcoreapp3.0/System.Security.Cryptography.Pkcs.dll", "ref/netcoreapp3.0/System.Security.Cryptography.Pkcs.xml", "ref/netstandard1.3/System.Security.Cryptography.Pkcs.dll", "ref/netstandard2.0/System.Security.Cryptography.Pkcs.dll", "ref/netstandard2.0/System.Security.Cryptography.Pkcs.xml", "ref/netstandard2.1/System.Security.Cryptography.Pkcs.dll", "ref/netstandard2.1/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/net46/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/net461/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/net461/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/netcoreapp2.1/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/netcoreapp3.0/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/netcoreapp3.0/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/netstandard1.3/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/netstandard2.0/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/netstandard2.0/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/netstandard2.1/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/netstandard2.1/System.Security.Cryptography.Pkcs.xml", "system.security.cryptography.pkcs.5.0.0.nupkg.sha512", "system.security.cryptography.pkcs.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.Cryptography.Primitives/4.3.0": {"sha512": "7bDIyVFNL/xKeFHjhobUAQqSpJq9YTOpbEs6mR233Et01STBMXNAc/V+BM6dwYGc95gVh/Zf+iVXWzj3mE8DWg==", "type": "package", "path": "system.security.cryptography.primitives/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.Primitives.dll", "lib/netstandard1.3/System.Security.Cryptography.Primitives.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.Primitives.dll", "ref/netstandard1.3/System.Security.Cryptography.Primitives.dll", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.security.cryptography.primitives.4.3.0.nupkg.sha512", "system.security.cryptography.primitives.nuspec"]}, "System.Security.Cryptography.ProtectedData/4.7.0": {"sha512": "ehYW0m9ptxpGWvE4zgqongBVWpSDU/JCFD4K7krxkQwSz/sFQjEXCUqpvencjy6DYDbn7Ig09R8GFffu8TtneQ==", "type": "package", "path": "system.security.cryptography.protecteddata/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.ProtectedData.dll", "lib/net461/System.Security.Cryptography.ProtectedData.dll", "lib/net461/System.Security.Cryptography.ProtectedData.xml", "lib/netstandard1.3/System.Security.Cryptography.ProtectedData.dll", "lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll", "lib/netstandard2.0/System.Security.Cryptography.ProtectedData.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.ProtectedData.dll", "ref/net461/System.Security.Cryptography.ProtectedData.dll", "ref/net461/System.Security.Cryptography.ProtectedData.xml", "ref/netstandard1.3/System.Security.Cryptography.ProtectedData.dll", "ref/netstandard2.0/System.Security.Cryptography.ProtectedData.dll", "ref/netstandard2.0/System.Security.Cryptography.ProtectedData.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/win/lib/net46/System.Security.Cryptography.ProtectedData.dll", "runtimes/win/lib/net461/System.Security.Cryptography.ProtectedData.dll", "runtimes/win/lib/net461/System.Security.Cryptography.ProtectedData.xml", "runtimes/win/lib/netstandard1.3/System.Security.Cryptography.ProtectedData.dll", "runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll", "runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.xml", "system.security.cryptography.protecteddata.4.7.0.nupkg.sha512", "system.security.cryptography.protecteddata.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.Cryptography.X509Certificates/4.3.2": {"sha512": "uwlfOnvJd7rXRvP3aV126Q9XebIIEGEaZ245Rd5/ZwOg7U7AU+AmpE0vRh2F0DFjfOTuk7MAexv4nYiNP/RYnQ==", "type": "package", "path": "system.security.cryptography.x509certificates/4.3.2", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.X509Certificates.dll", "lib/net461/System.Security.Cryptography.X509Certificates.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.X509Certificates.dll", "ref/net461/System.Security.Cryptography.X509Certificates.dll", "ref/netstandard1.3/System.Security.Cryptography.X509Certificates.dll", "ref/netstandard1.4/System.Security.Cryptography.X509Certificates.dll", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netstandard1.6/System.Security.Cryptography.X509Certificates.dll", "runtimes/win/lib/net46/System.Security.Cryptography.X509Certificates.dll", "runtimes/win/lib/net461/System.Security.Cryptography.X509Certificates.dll", "runtimes/win/lib/netcore50/System.Security.Cryptography.X509Certificates.dll", "runtimes/win/lib/netstandard1.6/System.Security.Cryptography.X509Certificates.dll", "system.security.cryptography.x509certificates.4.3.2.nupkg.sha512", "system.security.cryptography.x509certificates.nuspec"]}, "System.Security.Cryptography.Xml/4.5.0": {"sha512": "i2Jn6rGXR63J0zIklImGRkDIJL4b1NfPSEbIVHBlqoIb12lfXIigCbDRpDmIEzwSo/v1U5y/rYJdzZYSyCWxvg==", "type": "package", "path": "system.security.cryptography.xml/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Security.Cryptography.Xml.dll", "lib/netstandard2.0/System.Security.Cryptography.Xml.dll", "ref/net461/System.Security.Cryptography.Xml.dll", "ref/net461/System.Security.Cryptography.Xml.xml", "ref/netstandard2.0/System.Security.Cryptography.Xml.dll", "ref/netstandard2.0/System.Security.Cryptography.Xml.xml", "system.security.cryptography.xml.4.5.0.nupkg.sha512", "system.security.cryptography.xml.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.Permissions/4.7.0": {"sha512": "dkOV6YYVBnYRa15/yv004eCGRBVADXw8qRbbNiCn/XpdJSUXkkUeIvdvFHkvnko4CdKMqG8yRHC4ox83LSlMsQ==", "type": "package", "path": "system.security.permissions/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Security.Permissions.dll", "lib/net461/System.Security.Permissions.xml", "lib/netcoreapp3.0/System.Security.Permissions.dll", "lib/netcoreapp3.0/System.Security.Permissions.xml", "lib/netstandard2.0/System.Security.Permissions.dll", "lib/netstandard2.0/System.Security.Permissions.xml", "ref/net461/System.Security.Permissions.dll", "ref/net461/System.Security.Permissions.xml", "ref/netcoreapp3.0/System.Security.Permissions.dll", "ref/netcoreapp3.0/System.Security.Permissions.xml", "ref/netstandard2.0/System.Security.Permissions.dll", "ref/netstandard2.0/System.Security.Permissions.xml", "system.security.permissions.4.7.0.nupkg.sha512", "system.security.permissions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.Principal/4.3.0": {"sha512": "I1tkfQlAoMM2URscUtpcRo/hX0jinXx6a/KUtEQoz3owaYwl3qwsO8cbzYVVnjxrzxjHo3nJC+62uolgeGIS9A==", "type": "package", "path": "system.security.principal/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Security.Principal.dll", "lib/netstandard1.0/System.Security.Principal.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Security.Principal.dll", "ref/netcore50/System.Security.Principal.xml", "ref/netcore50/de/System.Security.Principal.xml", "ref/netcore50/es/System.Security.Principal.xml", "ref/netcore50/fr/System.Security.Principal.xml", "ref/netcore50/it/System.Security.Principal.xml", "ref/netcore50/ja/System.Security.Principal.xml", "ref/netcore50/ko/System.Security.Principal.xml", "ref/netcore50/ru/System.Security.Principal.xml", "ref/netcore50/zh-hans/System.Security.Principal.xml", "ref/netcore50/zh-hant/System.Security.Principal.xml", "ref/netstandard1.0/System.Security.Principal.dll", "ref/netstandard1.0/System.Security.Principal.xml", "ref/netstandard1.0/de/System.Security.Principal.xml", "ref/netstandard1.0/es/System.Security.Principal.xml", "ref/netstandard1.0/fr/System.Security.Principal.xml", "ref/netstandard1.0/it/System.Security.Principal.xml", "ref/netstandard1.0/ja/System.Security.Principal.xml", "ref/netstandard1.0/ko/System.Security.Principal.xml", "ref/netstandard1.0/ru/System.Security.Principal.xml", "ref/netstandard1.0/zh-hans/System.Security.Principal.xml", "ref/netstandard1.0/zh-hant/System.Security.Principal.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.security.principal.4.3.0.nupkg.sha512", "system.security.principal.nuspec"]}, "System.Security.Principal.Windows/4.7.0": {"sha512": "ojD0PX0XhneCsUbAZVKdb7h/70vyYMDYs85lwEI+LngEONe/17A0cFaRFqZU+sOEidcVswYWikYOQ9PPfjlbtQ==", "type": "package", "path": "system.security.principal.windows/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.xml", "lib/netstandard1.3/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.xml", "lib/uap10.0.16299/_._", "ref/net46/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.xml", "ref/netcoreapp3.0/System.Security.Principal.Windows.dll", "ref/netcoreapp3.0/System.Security.Principal.Windows.xml", "ref/netstandard1.3/System.Security.Principal.Windows.dll", "ref/netstandard1.3/System.Security.Principal.Windows.xml", "ref/netstandard1.3/de/System.Security.Principal.Windows.xml", "ref/netstandard1.3/es/System.Security.Principal.Windows.xml", "ref/netstandard1.3/fr/System.Security.Principal.Windows.xml", "ref/netstandard1.3/it/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ja/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ko/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ru/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hans/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hant/System.Security.Principal.Windows.xml", "ref/netstandard2.0/System.Security.Principal.Windows.dll", "ref/netstandard2.0/System.Security.Principal.Windows.xml", "ref/uap10.0.16299/_._", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/net46/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/netstandard1.3/System.Security.Principal.Windows.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.principal.windows.4.7.0.nupkg.sha512", "system.security.principal.windows.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.SecureString/4.3.0": {"sha512": "PnXp38O9q/2Oe4iZHMH60kinScv6QiiL2XH54Pj2t0Y6c2zKPEiAZsM/M3wBOHLNTBDFP0zfy13WN2M0qFz5jg==", "type": "package", "path": "system.security.securestring/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.SecureString.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.SecureString.dll", "ref/netstandard1.3/System.Security.SecureString.dll", "ref/netstandard1.3/System.Security.SecureString.xml", "ref/netstandard1.3/de/System.Security.SecureString.xml", "ref/netstandard1.3/es/System.Security.SecureString.xml", "ref/netstandard1.3/fr/System.Security.SecureString.xml", "ref/netstandard1.3/it/System.Security.SecureString.xml", "ref/netstandard1.3/ja/System.Security.SecureString.xml", "ref/netstandard1.3/ko/System.Security.SecureString.xml", "ref/netstandard1.3/ru/System.Security.SecureString.xml", "ref/netstandard1.3/zh-hans/System.Security.SecureString.xml", "ref/netstandard1.3/zh-hant/System.Security.SecureString.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netstandard1.3/System.Security.SecureString.dll", "runtimes/win/lib/net46/System.Security.SecureString.dll", "runtimes/win/lib/netstandard1.3/System.Security.SecureString.dll", "system.security.securestring.4.3.0.nupkg.sha512", "system.security.securestring.nuspec"]}, "System.Text.Encoding/4.3.0": {"sha512": "BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "type": "package", "path": "system.text.encoding/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Text.Encoding.dll", "ref/netcore50/System.Text.Encoding.xml", "ref/netcore50/de/System.Text.Encoding.xml", "ref/netcore50/es/System.Text.Encoding.xml", "ref/netcore50/fr/System.Text.Encoding.xml", "ref/netcore50/it/System.Text.Encoding.xml", "ref/netcore50/ja/System.Text.Encoding.xml", "ref/netcore50/ko/System.Text.Encoding.xml", "ref/netcore50/ru/System.Text.Encoding.xml", "ref/netcore50/zh-hans/System.Text.Encoding.xml", "ref/netcore50/zh-hant/System.Text.Encoding.xml", "ref/netstandard1.0/System.Text.Encoding.dll", "ref/netstandard1.0/System.Text.Encoding.xml", "ref/netstandard1.0/de/System.Text.Encoding.xml", "ref/netstandard1.0/es/System.Text.Encoding.xml", "ref/netstandard1.0/fr/System.Text.Encoding.xml", "ref/netstandard1.0/it/System.Text.Encoding.xml", "ref/netstandard1.0/ja/System.Text.Encoding.xml", "ref/netstandard1.0/ko/System.Text.Encoding.xml", "ref/netstandard1.0/ru/System.Text.Encoding.xml", "ref/netstandard1.0/zh-hans/System.Text.Encoding.xml", "ref/netstandard1.0/zh-hant/System.Text.Encoding.xml", "ref/netstandard1.3/System.Text.Encoding.dll", "ref/netstandard1.3/System.Text.Encoding.xml", "ref/netstandard1.3/de/System.Text.Encoding.xml", "ref/netstandard1.3/es/System.Text.Encoding.xml", "ref/netstandard1.3/fr/System.Text.Encoding.xml", "ref/netstandard1.3/it/System.Text.Encoding.xml", "ref/netstandard1.3/ja/System.Text.Encoding.xml", "ref/netstandard1.3/ko/System.Text.Encoding.xml", "ref/netstandard1.3/ru/System.Text.Encoding.xml", "ref/netstandard1.3/zh-hans/System.Text.Encoding.xml", "ref/netstandard1.3/zh-hant/System.Text.Encoding.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.text.encoding.4.3.0.nupkg.sha512", "system.text.encoding.nuspec"]}, "System.Text.Encoding.CodePages/5.0.0": {"sha512": "NyscU59xX6Uo91qvhOs2Ccho3AR2TnZPomo1Z0K6YpyztBPM/A5VbkzOO19sy3A3i1TtEnTxA7bCe3Us+r5MWg==", "type": "package", "path": "system.text.encoding.codepages/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Text.Encoding.CodePages.dll", "lib/net461/System.Text.Encoding.CodePages.dll", "lib/net461/System.Text.Encoding.CodePages.xml", "lib/netstandard1.3/System.Text.Encoding.CodePages.dll", "lib/netstandard2.0/System.Text.Encoding.CodePages.dll", "lib/netstandard2.0/System.Text.Encoding.CodePages.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/win/lib/net461/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/net461/System.Text.Encoding.CodePages.xml", "runtimes/win/lib/netcoreapp2.0/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/netcoreapp2.0/System.Text.Encoding.CodePages.xml", "runtimes/win/lib/netstandard1.3/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/netstandard2.0/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/netstandard2.0/System.Text.Encoding.CodePages.xml", "system.text.encoding.codepages.5.0.0.nupkg.sha512", "system.text.encoding.codepages.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Text.Encoding.Extensions/4.3.0": {"sha512": "YVMK0Bt/A43RmwizJoZ22ei2nmrhobgeiYwFzC4YAN+nue8RF6djXDMog0UCn+brerQoYVyaS+ghy9P/MUVcmw==", "type": "package", "path": "system.text.encoding.extensions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Text.Encoding.Extensions.dll", "ref/netcore50/System.Text.Encoding.Extensions.xml", "ref/netcore50/de/System.Text.Encoding.Extensions.xml", "ref/netcore50/es/System.Text.Encoding.Extensions.xml", "ref/netcore50/fr/System.Text.Encoding.Extensions.xml", "ref/netcore50/it/System.Text.Encoding.Extensions.xml", "ref/netcore50/ja/System.Text.Encoding.Extensions.xml", "ref/netcore50/ko/System.Text.Encoding.Extensions.xml", "ref/netcore50/ru/System.Text.Encoding.Extensions.xml", "ref/netcore50/zh-hans/System.Text.Encoding.Extensions.xml", "ref/netcore50/zh-hant/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/System.Text.Encoding.Extensions.dll", "ref/netstandard1.0/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/de/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/es/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/fr/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/it/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/ja/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/ko/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/ru/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/zh-hans/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/zh-hant/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/System.Text.Encoding.Extensions.dll", "ref/netstandard1.3/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/de/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/es/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/fr/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/it/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/ja/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/ko/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/ru/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/zh-hans/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/zh-hant/System.Text.Encoding.Extensions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.text.encoding.extensions.4.3.0.nupkg.sha512", "system.text.encoding.extensions.nuspec"]}, "System.Text.Encodings.Web/5.0.1": {"sha512": "KmJ+CJXizDofbq6mpqDoRRLcxgOd2z9X3XoFNULSbvbqVRZkFX3istvr+MUjL6Zw1RT+RNdoI4GYidIINtgvqQ==", "type": "package", "path": "system.text.encodings.web/5.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Text.Encodings.Web.dll", "lib/net461/System.Text.Encodings.Web.xml", "lib/netcoreapp3.0/System.Text.Encodings.Web.dll", "lib/netcoreapp3.0/System.Text.Encodings.Web.xml", "lib/netstandard1.0/System.Text.Encodings.Web.dll", "lib/netstandard1.0/System.Text.Encodings.Web.xml", "lib/netstandard2.0/System.Text.Encodings.Web.dll", "lib/netstandard2.0/System.Text.Encodings.Web.xml", "lib/netstandard2.1/System.Text.Encodings.Web.dll", "lib/netstandard2.1/System.Text.Encodings.Web.xml", "system.text.encodings.web.5.0.1.nupkg.sha512", "system.text.encodings.web.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Text.Json/5.0.1": {"sha512": "/UM3UK1dXKl8Ybysg/21gM4S8DJgkR+yLU8JwqCVbuNqQNImelntgYFAN5QxR8sJJ1kMx//hOUdf0lltosi8cQ==", "type": "package", "path": "system.text.json/5.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Text.Json.dll", "lib/net461/System.Text.Json.xml", "lib/netcoreapp3.0/System.Text.Json.dll", "lib/netcoreapp3.0/System.Text.Json.xml", "lib/netstandard2.0/System.Text.Json.dll", "lib/netstandard2.0/System.Text.Json.xml", "system.text.json.5.0.1.nupkg.sha512", "system.text.json.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Text.RegularExpressions/4.3.0": {"sha512": "RpT2DA+L660cBt1FssIE9CAGpLFdFPuheB7pLpKpn6ZXNby7jDERe8Ua/Ne2xGiwLVG2JOqziiaVCGDon5sKFA==", "type": "package", "path": "system.text.regularexpressions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net463/System.Text.RegularExpressions.dll", "lib/netcore50/System.Text.RegularExpressions.dll", "lib/netstandard1.6/System.Text.RegularExpressions.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net463/System.Text.RegularExpressions.dll", "ref/netcore50/System.Text.RegularExpressions.dll", "ref/netcore50/System.Text.RegularExpressions.xml", "ref/netcore50/de/System.Text.RegularExpressions.xml", "ref/netcore50/es/System.Text.RegularExpressions.xml", "ref/netcore50/fr/System.Text.RegularExpressions.xml", "ref/netcore50/it/System.Text.RegularExpressions.xml", "ref/netcore50/ja/System.Text.RegularExpressions.xml", "ref/netcore50/ko/System.Text.RegularExpressions.xml", "ref/netcore50/ru/System.Text.RegularExpressions.xml", "ref/netcore50/zh-hans/System.Text.RegularExpressions.xml", "ref/netcore50/zh-hant/System.Text.RegularExpressions.xml", "ref/netcoreapp1.1/System.Text.RegularExpressions.dll", "ref/netstandard1.0/System.Text.RegularExpressions.dll", "ref/netstandard1.0/System.Text.RegularExpressions.xml", "ref/netstandard1.0/de/System.Text.RegularExpressions.xml", "ref/netstandard1.0/es/System.Text.RegularExpressions.xml", "ref/netstandard1.0/fr/System.Text.RegularExpressions.xml", "ref/netstandard1.0/it/System.Text.RegularExpressions.xml", "ref/netstandard1.0/ja/System.Text.RegularExpressions.xml", "ref/netstandard1.0/ko/System.Text.RegularExpressions.xml", "ref/netstandard1.0/ru/System.Text.RegularExpressions.xml", "ref/netstandard1.0/zh-hans/System.Text.RegularExpressions.xml", "ref/netstandard1.0/zh-hant/System.Text.RegularExpressions.xml", "ref/netstandard1.3/System.Text.RegularExpressions.dll", "ref/netstandard1.3/System.Text.RegularExpressions.xml", "ref/netstandard1.3/de/System.Text.RegularExpressions.xml", "ref/netstandard1.3/es/System.Text.RegularExpressions.xml", "ref/netstandard1.3/fr/System.Text.RegularExpressions.xml", "ref/netstandard1.3/it/System.Text.RegularExpressions.xml", "ref/netstandard1.3/ja/System.Text.RegularExpressions.xml", "ref/netstandard1.3/ko/System.Text.RegularExpressions.xml", "ref/netstandard1.3/ru/System.Text.RegularExpressions.xml", "ref/netstandard1.3/zh-hans/System.Text.RegularExpressions.xml", "ref/netstandard1.3/zh-hant/System.Text.RegularExpressions.xml", "ref/netstandard1.6/System.Text.RegularExpressions.dll", "ref/netstandard1.6/System.Text.RegularExpressions.xml", "ref/netstandard1.6/de/System.Text.RegularExpressions.xml", "ref/netstandard1.6/es/System.Text.RegularExpressions.xml", "ref/netstandard1.6/fr/System.Text.RegularExpressions.xml", "ref/netstandard1.6/it/System.Text.RegularExpressions.xml", "ref/netstandard1.6/ja/System.Text.RegularExpressions.xml", "ref/netstandard1.6/ko/System.Text.RegularExpressions.xml", "ref/netstandard1.6/ru/System.Text.RegularExpressions.xml", "ref/netstandard1.6/zh-hans/System.Text.RegularExpressions.xml", "ref/netstandard1.6/zh-hant/System.Text.RegularExpressions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.text.regularexpressions.4.3.0.nupkg.sha512", "system.text.regularexpressions.nuspec"]}, "System.Threading/4.3.0": {"sha512": "VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "type": "package", "path": "system.threading/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Threading.dll", "lib/netstandard1.3/System.Threading.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Threading.dll", "ref/netcore50/System.Threading.xml", "ref/netcore50/de/System.Threading.xml", "ref/netcore50/es/System.Threading.xml", "ref/netcore50/fr/System.Threading.xml", "ref/netcore50/it/System.Threading.xml", "ref/netcore50/ja/System.Threading.xml", "ref/netcore50/ko/System.Threading.xml", "ref/netcore50/ru/System.Threading.xml", "ref/netcore50/zh-hans/System.Threading.xml", "ref/netcore50/zh-hant/System.Threading.xml", "ref/netstandard1.0/System.Threading.dll", "ref/netstandard1.0/System.Threading.xml", "ref/netstandard1.0/de/System.Threading.xml", "ref/netstandard1.0/es/System.Threading.xml", "ref/netstandard1.0/fr/System.Threading.xml", "ref/netstandard1.0/it/System.Threading.xml", "ref/netstandard1.0/ja/System.Threading.xml", "ref/netstandard1.0/ko/System.Threading.xml", "ref/netstandard1.0/ru/System.Threading.xml", "ref/netstandard1.0/zh-hans/System.Threading.xml", "ref/netstandard1.0/zh-hant/System.Threading.xml", "ref/netstandard1.3/System.Threading.dll", "ref/netstandard1.3/System.Threading.xml", "ref/netstandard1.3/de/System.Threading.xml", "ref/netstandard1.3/es/System.Threading.xml", "ref/netstandard1.3/fr/System.Threading.xml", "ref/netstandard1.3/it/System.Threading.xml", "ref/netstandard1.3/ja/System.Threading.xml", "ref/netstandard1.3/ko/System.Threading.xml", "ref/netstandard1.3/ru/System.Threading.xml", "ref/netstandard1.3/zh-hans/System.Threading.xml", "ref/netstandard1.3/zh-hant/System.Threading.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Threading.dll", "system.threading.4.3.0.nupkg.sha512", "system.threading.nuspec"]}, "System.Threading.Tasks/4.3.0": {"sha512": "LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "type": "package", "path": "system.threading.tasks/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Threading.Tasks.dll", "ref/netcore50/System.Threading.Tasks.xml", "ref/netcore50/de/System.Threading.Tasks.xml", "ref/netcore50/es/System.Threading.Tasks.xml", "ref/netcore50/fr/System.Threading.Tasks.xml", "ref/netcore50/it/System.Threading.Tasks.xml", "ref/netcore50/ja/System.Threading.Tasks.xml", "ref/netcore50/ko/System.Threading.Tasks.xml", "ref/netcore50/ru/System.Threading.Tasks.xml", "ref/netcore50/zh-hans/System.Threading.Tasks.xml", "ref/netcore50/zh-hant/System.Threading.Tasks.xml", "ref/netstandard1.0/System.Threading.Tasks.dll", "ref/netstandard1.0/System.Threading.Tasks.xml", "ref/netstandard1.0/de/System.Threading.Tasks.xml", "ref/netstandard1.0/es/System.Threading.Tasks.xml", "ref/netstandard1.0/fr/System.Threading.Tasks.xml", "ref/netstandard1.0/it/System.Threading.Tasks.xml", "ref/netstandard1.0/ja/System.Threading.Tasks.xml", "ref/netstandard1.0/ko/System.Threading.Tasks.xml", "ref/netstandard1.0/ru/System.Threading.Tasks.xml", "ref/netstandard1.0/zh-hans/System.Threading.Tasks.xml", "ref/netstandard1.0/zh-hant/System.Threading.Tasks.xml", "ref/netstandard1.3/System.Threading.Tasks.dll", "ref/netstandard1.3/System.Threading.Tasks.xml", "ref/netstandard1.3/de/System.Threading.Tasks.xml", "ref/netstandard1.3/es/System.Threading.Tasks.xml", "ref/netstandard1.3/fr/System.Threading.Tasks.xml", "ref/netstandard1.3/it/System.Threading.Tasks.xml", "ref/netstandard1.3/ja/System.Threading.Tasks.xml", "ref/netstandard1.3/ko/System.Threading.Tasks.xml", "ref/netstandard1.3/ru/System.Threading.Tasks.xml", "ref/netstandard1.3/zh-hans/System.Threading.Tasks.xml", "ref/netstandard1.3/zh-hant/System.Threading.Tasks.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.tasks.4.3.0.nupkg.sha512", "system.threading.tasks.nuspec"]}, "System.Threading.Tasks.Extensions/4.5.2": {"sha512": "BG/TNxDFv0svAzx8OiMXDlsHfGw623BZ8tCXw4YLhDFDvDhNUEV58jKYMGRnkbJNm7c3JNNJDiN7JBMzxRBR2w==", "type": "package", "path": "system.threading.tasks.extensions/4.5.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/netcoreapp2.1/_._", "lib/netstandard1.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard1.0/System.Threading.Tasks.Extensions.xml", "lib/netstandard2.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard2.0/System.Threading.Tasks.Extensions.xml", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.dll", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/netcoreapp2.1/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.tasks.extensions.4.5.2.nupkg.sha512", "system.threading.tasks.extensions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Threading.Timer/4.3.0": {"sha512": "Z6YfyYTCg7lOZjJzBjONJTFKGN9/NIYKSxhU5GRd+DTwHSZyvWp1xuI5aR+dLg+ayyC5Xv57KiY4oJ0tMO89fQ==", "type": "package", "path": "system.threading.timer/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net451/_._", "lib/portable-net451+win81+wpa81/_._", "lib/win81/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net451/_._", "ref/netcore50/System.Threading.Timer.dll", "ref/netcore50/System.Threading.Timer.xml", "ref/netcore50/de/System.Threading.Timer.xml", "ref/netcore50/es/System.Threading.Timer.xml", "ref/netcore50/fr/System.Threading.Timer.xml", "ref/netcore50/it/System.Threading.Timer.xml", "ref/netcore50/ja/System.Threading.Timer.xml", "ref/netcore50/ko/System.Threading.Timer.xml", "ref/netcore50/ru/System.Threading.Timer.xml", "ref/netcore50/zh-hans/System.Threading.Timer.xml", "ref/netcore50/zh-hant/System.Threading.Timer.xml", "ref/netstandard1.2/System.Threading.Timer.dll", "ref/netstandard1.2/System.Threading.Timer.xml", "ref/netstandard1.2/de/System.Threading.Timer.xml", "ref/netstandard1.2/es/System.Threading.Timer.xml", "ref/netstandard1.2/fr/System.Threading.Timer.xml", "ref/netstandard1.2/it/System.Threading.Timer.xml", "ref/netstandard1.2/ja/System.Threading.Timer.xml", "ref/netstandard1.2/ko/System.Threading.Timer.xml", "ref/netstandard1.2/ru/System.Threading.Timer.xml", "ref/netstandard1.2/zh-hans/System.Threading.Timer.xml", "ref/netstandard1.2/zh-hant/System.Threading.Timer.xml", "ref/portable-net451+win81+wpa81/_._", "ref/win81/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.timer.4.3.0.nupkg.sha512", "system.threading.timer.nuspec"]}, "System.ValueTuple/4.5.0": {"sha512": "okurQJO6NRE/apDIP23ajJ0hpiNmJ+f0BwOlB/cSqTLQlw5upkf+5+96+iG2Jw40G1fCVCyPz/FhIABUjMR+RQ==", "type": "package", "path": "system.valuetuple/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.ValueTuple.dll", "lib/net461/System.ValueTuple.xml", "lib/net47/System.ValueTuple.dll", "lib/net47/System.ValueTuple.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.ValueTuple.dll", "lib/netstandard1.0/System.ValueTuple.xml", "lib/netstandard2.0/_._", "lib/portable-net40+sl4+win8+wp8/System.ValueTuple.dll", "lib/portable-net40+sl4+win8+wp8/System.ValueTuple.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net461/System.ValueTuple.dll", "ref/net47/System.ValueTuple.dll", "ref/netcoreapp2.0/_._", "ref/netstandard2.0/_._", "ref/portable-net40+sl4+win8+wp8/System.ValueTuple.dll", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.valuetuple.4.5.0.nupkg.sha512", "system.valuetuple.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Windows.Extensions/4.7.0": {"sha512": "CeWTdRNfRaSh0pm2gDTJFwVaXfTq6Xwv/sA887iwPTneW7oMtMlpvDIO+U60+3GWTB7Aom6oQwv5VZVUhQRdPQ==", "type": "package", "path": "system.windows.extensions/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netcoreapp3.0/System.Windows.Extensions.dll", "lib/netcoreapp3.0/System.Windows.Extensions.xml", "ref/netcoreapp3.0/System.Windows.Extensions.dll", "ref/netcoreapp3.0/System.Windows.Extensions.xml", "runtimes/win/lib/netcoreapp3.0/System.Windows.Extensions.dll", "runtimes/win/lib/netcoreapp3.0/System.Windows.Extensions.xml", "system.windows.extensions.4.7.0.nupkg.sha512", "system.windows.extensions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Xml.ReaderWriter/4.3.0": {"sha512": "GrprA+Z0RUXaR4N7/eW71j1rgMnEnEVlgii49GZyAjTH7uliMnrOU3HNFBr6fEDBCJCIdlVNq9hHbaDR621XBA==", "type": "package", "path": "system.xml.readerwriter/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net46/System.Xml.ReaderWriter.dll", "lib/netcore50/System.Xml.ReaderWriter.dll", "lib/netstandard1.3/System.Xml.ReaderWriter.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net46/System.Xml.ReaderWriter.dll", "ref/netcore50/System.Xml.ReaderWriter.dll", "ref/netcore50/System.Xml.ReaderWriter.xml", "ref/netcore50/de/System.Xml.ReaderWriter.xml", "ref/netcore50/es/System.Xml.ReaderWriter.xml", "ref/netcore50/fr/System.Xml.ReaderWriter.xml", "ref/netcore50/it/System.Xml.ReaderWriter.xml", "ref/netcore50/ja/System.Xml.ReaderWriter.xml", "ref/netcore50/ko/System.Xml.ReaderWriter.xml", "ref/netcore50/ru/System.Xml.ReaderWriter.xml", "ref/netcore50/zh-hans/System.Xml.ReaderWriter.xml", "ref/netcore50/zh-hant/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/System.Xml.ReaderWriter.dll", "ref/netstandard1.0/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/de/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/es/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/fr/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/it/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/ja/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/ko/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/ru/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/zh-hans/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/zh-hant/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/System.Xml.ReaderWriter.dll", "ref/netstandard1.3/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/de/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/es/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/fr/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/it/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/ja/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/ko/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/ru/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/zh-hans/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/zh-hant/System.Xml.ReaderWriter.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.xml.readerwriter.4.3.0.nupkg.sha512", "system.xml.readerwriter.nuspec"]}, "System.Xml.XDocument/4.3.0": {"sha512": "5zJ0XDxAIg8iy+t4aMnQAu0MqVbqyvfoUVl1yDV61xdo3Vth45oA2FoY4pPkxYAH5f8ixpmTqXeEIya95x0aCQ==", "type": "package", "path": "system.xml.xdocument/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Xml.XDocument.dll", "lib/netstandard1.3/System.Xml.XDocument.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Xml.XDocument.dll", "ref/netcore50/System.Xml.XDocument.xml", "ref/netcore50/de/System.Xml.XDocument.xml", "ref/netcore50/es/System.Xml.XDocument.xml", "ref/netcore50/fr/System.Xml.XDocument.xml", "ref/netcore50/it/System.Xml.XDocument.xml", "ref/netcore50/ja/System.Xml.XDocument.xml", "ref/netcore50/ko/System.Xml.XDocument.xml", "ref/netcore50/ru/System.Xml.XDocument.xml", "ref/netcore50/zh-hans/System.Xml.XDocument.xml", "ref/netcore50/zh-hant/System.Xml.XDocument.xml", "ref/netstandard1.0/System.Xml.XDocument.dll", "ref/netstandard1.0/System.Xml.XDocument.xml", "ref/netstandard1.0/de/System.Xml.XDocument.xml", "ref/netstandard1.0/es/System.Xml.XDocument.xml", "ref/netstandard1.0/fr/System.Xml.XDocument.xml", "ref/netstandard1.0/it/System.Xml.XDocument.xml", "ref/netstandard1.0/ja/System.Xml.XDocument.xml", "ref/netstandard1.0/ko/System.Xml.XDocument.xml", "ref/netstandard1.0/ru/System.Xml.XDocument.xml", "ref/netstandard1.0/zh-hans/System.Xml.XDocument.xml", "ref/netstandard1.0/zh-hant/System.Xml.XDocument.xml", "ref/netstandard1.3/System.Xml.XDocument.dll", "ref/netstandard1.3/System.Xml.XDocument.xml", "ref/netstandard1.3/de/System.Xml.XDocument.xml", "ref/netstandard1.3/es/System.Xml.XDocument.xml", "ref/netstandard1.3/fr/System.Xml.XDocument.xml", "ref/netstandard1.3/it/System.Xml.XDocument.xml", "ref/netstandard1.3/ja/System.Xml.XDocument.xml", "ref/netstandard1.3/ko/System.Xml.XDocument.xml", "ref/netstandard1.3/ru/System.Xml.XDocument.xml", "ref/netstandard1.3/zh-hans/System.Xml.XDocument.xml", "ref/netstandard1.3/zh-hant/System.Xml.XDocument.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.xml.xdocument.4.3.0.nupkg.sha512", "system.xml.xdocument.nuspec"]}, "System.Xml.XmlDocument/4.3.0": {"sha512": "lJ8AxvkX7GQxpC6GFCeBj8ThYVyQczx2+f/cWHJU8tjS7YfI6Cv6bon70jVEgs2CiFbmmM8b9j1oZVx0dSI2Ww==", "type": "package", "path": "system.xml.xmldocument/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Xml.XmlDocument.dll", "lib/netstandard1.3/System.Xml.XmlDocument.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Xml.XmlDocument.dll", "ref/netstandard1.3/System.Xml.XmlDocument.dll", "ref/netstandard1.3/System.Xml.XmlDocument.xml", "ref/netstandard1.3/de/System.Xml.XmlDocument.xml", "ref/netstandard1.3/es/System.Xml.XmlDocument.xml", "ref/netstandard1.3/fr/System.Xml.XmlDocument.xml", "ref/netstandard1.3/it/System.Xml.XmlDocument.xml", "ref/netstandard1.3/ja/System.Xml.XmlDocument.xml", "ref/netstandard1.3/ko/System.Xml.XmlDocument.xml", "ref/netstandard1.3/ru/System.Xml.XmlDocument.xml", "ref/netstandard1.3/zh-hans/System.Xml.XmlDocument.xml", "ref/netstandard1.3/zh-hant/System.Xml.XmlDocument.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.xml.xmldocument.4.3.0.nupkg.sha512", "system.xml.xmldocument.nuspec"]}, "System.Xml.XmlSerializer/4.3.0": {"sha512": "MYoTCP7EZ98RrANESW05J5ZwskKDoN0AuZ06ZflnowE50LTpbR5yRg3tHckTVm5j/m47stuGgCrCHWePyHS70Q==", "type": "package", "path": "system.xml.xmlserializer/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Xml.XmlSerializer.dll", "lib/netstandard1.3/System.Xml.XmlSerializer.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Xml.XmlSerializer.dll", "ref/netcore50/System.Xml.XmlSerializer.xml", "ref/netcore50/de/System.Xml.XmlSerializer.xml", "ref/netcore50/es/System.Xml.XmlSerializer.xml", "ref/netcore50/fr/System.Xml.XmlSerializer.xml", "ref/netcore50/it/System.Xml.XmlSerializer.xml", "ref/netcore50/ja/System.Xml.XmlSerializer.xml", "ref/netcore50/ko/System.Xml.XmlSerializer.xml", "ref/netcore50/ru/System.Xml.XmlSerializer.xml", "ref/netcore50/zh-hans/System.Xml.XmlSerializer.xml", "ref/netcore50/zh-hant/System.Xml.XmlSerializer.xml", "ref/netstandard1.0/System.Xml.XmlSerializer.dll", "ref/netstandard1.0/System.Xml.XmlSerializer.xml", "ref/netstandard1.0/de/System.Xml.XmlSerializer.xml", "ref/netstandard1.0/es/System.Xml.XmlSerializer.xml", "ref/netstandard1.0/fr/System.Xml.XmlSerializer.xml", "ref/netstandard1.0/it/System.Xml.XmlSerializer.xml", "ref/netstandard1.0/ja/System.Xml.XmlSerializer.xml", "ref/netstandard1.0/ko/System.Xml.XmlSerializer.xml", "ref/netstandard1.0/ru/System.Xml.XmlSerializer.xml", "ref/netstandard1.0/zh-hans/System.Xml.XmlSerializer.xml", "ref/netstandard1.0/zh-hant/System.Xml.XmlSerializer.xml", "ref/netstandard1.3/System.Xml.XmlSerializer.dll", "ref/netstandard1.3/System.Xml.XmlSerializer.xml", "ref/netstandard1.3/de/System.Xml.XmlSerializer.xml", "ref/netstandard1.3/es/System.Xml.XmlSerializer.xml", "ref/netstandard1.3/fr/System.Xml.XmlSerializer.xml", "ref/netstandard1.3/it/System.Xml.XmlSerializer.xml", "ref/netstandard1.3/ja/System.Xml.XmlSerializer.xml", "ref/netstandard1.3/ko/System.Xml.XmlSerializer.xml", "ref/netstandard1.3/ru/System.Xml.XmlSerializer.xml", "ref/netstandard1.3/zh-hans/System.Xml.XmlSerializer.xml", "ref/netstandard1.3/zh-hant/System.Xml.XmlSerializer.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Xml.XmlSerializer.dll", "system.xml.xmlserializer.4.3.0.nupkg.sha512", "system.xml.xmlserializer.nuspec"]}, "System.Xml.XPath/4.3.0": {"sha512": "v1JQ5SETnQusqmS3RwStF7vwQ3L02imIzl++sewmt23VGygix04pEH+FCj1yWb+z4GDzKiljr1W7Wfvrx0YwgA==", "type": "package", "path": "system.xml.xpath/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Xml.XPath.dll", "lib/netstandard1.3/System.Xml.XPath.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Xml.XPath.dll", "ref/netstandard1.3/System.Xml.XPath.dll", "ref/netstandard1.3/System.Xml.XPath.xml", "ref/netstandard1.3/de/System.Xml.XPath.xml", "ref/netstandard1.3/es/System.Xml.XPath.xml", "ref/netstandard1.3/fr/System.Xml.XPath.xml", "ref/netstandard1.3/it/System.Xml.XPath.xml", "ref/netstandard1.3/ja/System.Xml.XPath.xml", "ref/netstandard1.3/ko/System.Xml.XPath.xml", "ref/netstandard1.3/ru/System.Xml.XPath.xml", "ref/netstandard1.3/zh-hans/System.Xml.XPath.xml", "ref/netstandard1.3/zh-hant/System.Xml.XPath.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.xml.xpath.4.3.0.nupkg.sha512", "system.xml.xpath.nuspec"]}, "System.Xml.XPath.XmlDocument/4.3.0": {"sha512": "A/uxsWi/Ifzkmd4ArTLISMbfFs6XpRPsXZonrIqyTY70xi8t+mDtvSM5Os0RqyRDobjMBwIDHDL4NOIbkDwf7A==", "type": "package", "path": "system.xml.xpath.xmldocument/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Xml.XPath.XmlDocument.dll", "lib/netstandard1.3/System.Xml.XPath.XmlDocument.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/netstandard1.3/System.Xml.XPath.XmlDocument.dll", "ref/netstandard1.3/System.Xml.XPath.XmlDocument.xml", "ref/netstandard1.3/de/System.Xml.XPath.XmlDocument.xml", "ref/netstandard1.3/es/System.Xml.XPath.XmlDocument.xml", "ref/netstandard1.3/fr/System.Xml.XPath.XmlDocument.xml", "ref/netstandard1.3/it/System.Xml.XPath.XmlDocument.xml", "ref/netstandard1.3/ja/System.Xml.XPath.XmlDocument.xml", "ref/netstandard1.3/ko/System.Xml.XPath.XmlDocument.xml", "ref/netstandard1.3/ru/System.Xml.XPath.XmlDocument.xml", "ref/netstandard1.3/zh-hans/System.Xml.XPath.XmlDocument.xml", "ref/netstandard1.3/zh-hant/System.Xml.XPath.XmlDocument.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.xml.xpath.xmldocument.4.3.0.nupkg.sha512", "system.xml.xpath.xmldocument.nuspec"]}, "BdoPartner.Plans.Business.Interface/1.0.0": {"type": "project", "path": "../BdoPartner.Plans.Business.Interface/BdoPartner.Plans.Business.Interface.csproj", "msbuildProject": "../BdoPartner.Plans.Business.Interface/BdoPartner.Plans.Business.Interface.csproj"}, "BdoPartner.Plans.Common/1.0.0": {"type": "project", "path": "../BdoPartner.Plans.Common/BdoPartner.Plans.Common.csproj", "msbuildProject": "../BdoPartner.Plans.Common/BdoPartner.Plans.Common.csproj"}, "BdoPartner.Plans.DataAccess/1.0.0": {"type": "project", "path": "../BdoPartner.Plans.DataAccess/BdoPartner.Plans.DataAccess.csproj", "msbuildProject": "../BdoPartner.Plans.DataAccess/BdoPartner.Plans.DataAccess.csproj"}, "BdoPartner.Plans.DataAccess.Common/1.0.0": {"type": "project", "path": "../BdoPartner.Plans.DataAccess.Common/BdoPartner.Plans.DataAccess.Common.csproj", "msbuildProject": "../BdoPartner.Plans.DataAccess.Common/BdoPartner.Plans.DataAccess.Common.csproj"}, "BdoPartner.Plans.Model.DTO/1.0.0": {"type": "project", "path": "../BdoPartner.Plans.Model.DTO/BdoPartner.Plans.Model.DTO.csproj", "msbuildProject": "../BdoPartner.Plans.Model.DTO/BdoPartner.Plans.Model.DTO.csproj"}, "BdoPartner.Plans.Model.Entity/1.0.0": {"type": "project", "path": "../BdoPartner.Plans.Model.Entity/BdoPartner.Plans.Model.Entity.csproj", "msbuildProject": "../BdoPartner.Plans.Model.Entity/BdoPartner.Plans.Model.Entity.csproj"}, "BdoPartner.Plans.Model.Mapper/1.0.0": {"type": "project", "path": "../BdoPartner.Plans.Model.Mapper/BdoPartner.Plans.Model.Mapper.csproj", "msbuildProject": "../BdoPartner.Plans.Model.Mapper/BdoPartner.Plans.Model.Mapper.csproj"}}, "projectFileDependencyGroups": {"net5.0": ["BdoPartner.Plans.Business.Interface >= 1.0.0", "BdoPartner.Plans.Common >= 1.0.0", "BdoPartner.Plans.DataAccess >= 1.0.0", "BdoPartner.Plans.Model.Mapper >= 1.0.0", "EPPlus >= 5.8.4", "IdentityServer4 >= 4.1.2", "Microsoft.AspNetCore.Http >= 2.2.2", "Microsoft.Identity.Web.MicrosoftGraph >= 1.14.0", "Microsoft.IdentityModel.Clients.ActiveDirectory >= 5.2.9"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Business\\BdoPartner.Plans.Business.csproj", "projectName": "BdoPartner.Plans.Business", "projectPath": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Business\\BdoPartner.Plans.Business.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Business\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net5.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net5.0": {"targetAlias": "net5.0", "projectReferences": {"C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Business.Interface\\BdoPartner.Plans.Business.Interface.csproj": {"projectPath": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Business.Interface\\BdoPartner.Plans.Business.Interface.csproj"}, "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Common\\BdoPartner.Plans.Common.csproj": {"projectPath": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Common\\BdoPartner.Plans.Common.csproj"}, "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.DataAccess\\BdoPartner.Plans.DataAccess.csproj": {"projectPath": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.DataAccess\\BdoPartner.Plans.DataAccess.csproj"}, "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Model.Mapper\\BdoPartner.Plans.Model.Mapper.csproj": {"projectPath": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Model.Mapper\\BdoPartner.Plans.Model.Mapper.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net5.0": {"targetAlias": "net5.0", "dependencies": {"EPPlus": {"target": "Package", "version": "[5.8.4, )"}, "IdentityServer4": {"target": "Package", "version": "[4.1.2, )"}, "Microsoft.AspNetCore.Http": {"target": "Package", "version": "[2.2.2, )"}, "Microsoft.Identity.Web.MicrosoftGraph": {"target": "Package", "version": "[1.14.0, )"}, "Microsoft.IdentityModel.Clients.ActiveDirectory": {"target": "Package", "version": "[5.2.9, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.300\\RuntimeIdentifierGraph.json"}}}, "logs": [{"code": "NU1902", "level": "Warning", "warningLevel": 1, "message": "Package 'IdentityServer4' 4.1.2 has a known moderate severity vulnerability, https://github.com/advisories/GHSA-55p7-v223-x366", "libraryId": "IdentityServer4", "targetGraphs": ["net5.0"]}, {"code": "NU1902", "level": "Warning", "warningLevel": 1, "message": "Package 'IdentityServer4' 4.1.2 has a known moderate severity vulnerability, https://github.com/advisories/GHSA-ff4q-64jc-gx98", "libraryId": "IdentityServer4", "targetGraphs": ["net5.0"]}]}