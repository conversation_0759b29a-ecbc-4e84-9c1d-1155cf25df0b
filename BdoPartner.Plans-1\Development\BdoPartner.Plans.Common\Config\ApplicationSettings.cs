﻿using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BdoPartner.Plans.Common.Config
{
    public class ApplicationSettings
    {
        private IConfiguration _config;

        public ApplicationSettings(IConfiguration config)
        {
            _config = config;
        }

        public string ClientId 
        { 
            get {
                return this._config.GetSection("App:Environment:ClientId").Value;
            } 
        }
        public string AppDomain
        {
            get
            {
                return this._config.GetSection("App:Environment:AppDomain").Value;
            }
        }
        public string IamDomain
        {
            get
            {
                return this._config.GetSection("App:Environment:IamDomain").Value;
            }
        }
        public string ApiDomain
        {
            get
            {
                return this._config.GetSection("App:Environment:ApiDomain").Value;
            }
        }
        public string IamScope
        {
            get
            {
                return this._config.GetSection("App:Environment:IamScope").Value;
            }
        }

        public string BasePath
        {
            get
            {
                return this._config.GetSection("App:Environment:BasePath").Value;
            }
        }

        public bool ShowNavBar
        {
            get
            {
                return bool.TryParse(this._config.GetSection("App:Environment:ShowNavBar").Value, out var value) && value;
            }
        }
    }
}

