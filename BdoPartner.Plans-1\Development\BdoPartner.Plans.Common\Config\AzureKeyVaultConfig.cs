﻿using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BdoPartner.Plans.Common.Config
{
    /// <summary>
    /// Azure Key Vaults access matters config settings section.
    /// Corporate with Azure SQL Server always encrypted data access.
    ///
    /// It is Optional config setting section.
    /// For development environment, it keeps in appsettings.Development.json.
    /// For production environment, it keeps in Azure App Configuration.
    /// </summary>
    public class AzureKeyVaultConfig
    {
        private IConfiguration _config;

        public AzureKeyVaultConfig(IConfiguration config)
        {
            _config = config;
        }

        /// <summary>
        /// Get TenantId from Azure AD registed Application (App registration) Dashboard 
        /// item called "Directory (tenant) ID". 
        /// For example, the TenantId of "LocalDebugSolutionTemplateWebAPI" in "BDO IT Solutions Innovation" Azure AD.
        /// It is guid data format.
        /// </summary>
        public string TenantId
        {
            get
            {
                return this._config.GetSection("App:AzureKeyVaultConfig:TenantId").Value;
            }
        }

        /// <summary>
        /// It is Azure Key Vault service uri. Get it from Azure Key Vault dashboard.
        /// </summary>
        public string AzureKeyVaultUri
        {
            get
            {
                return this._config.GetSection("App:AzureKeyVaultConfig:AzureKeyVaultUri").Value;
            }

        }
    }
}
