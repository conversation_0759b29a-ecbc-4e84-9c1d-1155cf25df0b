﻿using System;
using System.Collections.Generic;
using System.Text;
using Microsoft.Extensions.Configuration;
using System.Linq;

namespace BdoPartner.Plans.Common.Config
{
    /// <summary>
    ///  Note: This class need to corporate with Dependency inject IConfiguration which is registered in Web API project's StartUp.cs.
    ///  That is why it cannot be static.
    ///  Reference: https://blogs.technet.microsoft.com/<PERSON><PERSON><PERSON><PERSON><PERSON>/tip-of-the-week-how-to-access-configuration-from-controller-in-asp-net-core-2-0/
    /// </summary>
    public class ConfigSettings : IConfigSettings
    {
        private IConfiguration _config;

        public ConfigSettings(IConfiguration config)
        {
            _config = config;
        }

        public IConfiguration Config { get { return this._config; } }


        #region SQL Server Database connections

        /// <summary>
        ///  Database connection for MySql/SqlServer database.
        /// </summary>
        public string DatabaseConnection
        {
            get
            {
                return _config.GetConnectionString("DatabaseConnection");
            }
        }
           
        #endregion
                     
        /// <summary>
        ///  Public WEB Api Server address.
        /// </summary>
        public string ApiRootUrl
        {
            get
            {
                return _config.GetSection("App")["ApiRootUrl"];
            }
        }

        /// <summary>
        ///  Config setting in Web API or client portal. 
        ///  Corporate with Identity Server 4, setting called ApiName.
        /// </summary>
        public string ApplicationCode
        {
            get {
                return _config.GetSection("App")["ApplicationCode"];
            }
        }

        /// <summary>
        ///  allow accessing api portals domains. Same settings as records in table dbo.ClientDomain.
        /// </summary>
        public string[] AllowedDomains
        {
            get
            {
                return _config.GetSection("App")["AllowedDomains"].Split(",".ToCharArray(), StringSplitOptions.RemoveEmptyEntries);
            }
        }
        

        #region Email Setting. Work for company portal and Identity server portal.
        public string FromEmail
        {
            get
            {
                return _config.GetSection("App")["FromEmail"];
            }
        }

        public string MailServer
        {
            get
            {
                return _config.GetSection("App")["MailServer"];
            }
        }

        public int SMTPServerPort
        {
            get
            {
                int port = 0;

                string portString = _config.GetSection("App")["SMTPServerPort"];

                if (int.TryParse(portString, out port))
                    return port;
                else
                    return 25;
            }
        }

        public string SMTPUser
        {
            get
            {
                return _config.GetSection("App")["SMTPUser"];
            }
        }

        public string SMTPPassword
        {
            get
            {
                return _config.GetSection("App")["SMTPPassword"];
            }
        }

        public bool SMTPEnableSsL
        {
            get
            {
                bool enableSSL = false;
                string enableSSLString = _config.GetSection("App")["SMTPEnableSsL"];
                if (bool.TryParse(enableSSLString, out enableSSL))
                    return enableSSL;
                else
                    return false;
            }
        }

        /// <summary>
        /// If IsNotificationTest = true, system will send email to "AdministratorEmail" only and will not send email to real email receiver.
        /// Work for BdoPartner.Plans.TaskScheduler.Notification and other notifications.
        /// Corporate with EmailHelper.
        /// </summary>
        public bool IsNotificationTest
        {
            get
            {
                bool result = false;

                string isNotificationTest = _config.GetSection("App")["IsNotificationTest"];

                if (!string.IsNullOrEmpty(isNotificationTest))
                {
                    bool.TryParse(isNotificationTest, out result);
                }

                return result;
            }
        }
               

        #endregion
               
     

        /// <summary>
        ///  local database connection timeout setting.
        ///  It is integer value of seconds.
        ///  Default value = 360 seconds.
        /// </summary>
        public int DatabaseCommandTimeout
        {
            get
            {

                int result = 360;

                int.TryParse(_config.GetSection("App")["DatabaseCommandTimeout"], out result);

                return result;
            }
        }

        /// <summary>
        /// Work for local host test only. Check domain from field "TestDomain" in table dbo.ClientDomain.
        /// Work for accessing domain validation in identity server.
        /// Note: For QA, UAT, Production server, this setting should be false.
        /// Only work for developer debuging codes in localhost.
        /// </summary>
        public bool IsLocalHostTest
        {
            get
            {
                bool result = false;

                bool.TryParse(_config.GetSection("App")["IsLocalHostTest"], out result);

                return result;
            }
        }
              

        #region Google ReCaptcha
        /// <summary>
        ///  Define enable or disable Google reCaptcha Secret. Work for forgot password UI. Note: For Localhost debugging, set value = false.
        /// </summary>
        public bool EnableReCaptcha
        {
            get
            {
                bool result = false;
                string strResult = _config.GetSection("App")["EnableReCaptcha"];
                bool.TryParse(strResult, out result);
                return result;
            }
        }

        /// <summary>
        ///  Google reCaptcha Secret key. Work for forgot password UI.
        /// </summary>
        public string ReCaptchaSecret
        {
            get
            {
                return _config.GetSection("App")["ReCaptchaSecret"];
            }
        }

        /// <summary>
        /// Google reCaptcha SiteKey. Work for forgot password UI.
        /// </summary>
        public string ReCaptchaSiteKey
        {
            get
            {
                return _config.GetSection("App")["ReCaptchaSiteKey"];
            }
        }


        #endregion
       
        #region Azure Storage Account Settings. Get Information from Azure Portal, Storage Accounts -> Access keys.  Note: Diabled in First On site solution.
        public string AzureStorageAccount {
            get {
                return this._config.GetSection("App")["AzureStorage_Account"];
            }
        }

        public string AzureStorageKey1 {
            get {
                return this._config.GetSection("App")["AzureStorage_Key1"];
            }
        }

        public string AzureStorageConnection1 {
            get {
                return this._config.GetSection("App")["AzureStorage_Connection1"];
            }
        }

        public string AzureStorageKey2
        {
            get
            {
                return this._config.GetSection("App")["AzureStorage_Key2"];
            }
        }

        public string AzureStorageConnection2
        {
            get
            {
                return this._config.GetSection("App")["AzureStorage_Connection2"];
            }
        }

        public string AzureStorageContainerName
        {
            get
            {
                return this._config.GetSection("App")["AzureStorage_ContainerName"];
            }
        }

        #endregion

        #region Unit Test Associate

        /// <summary>
        /// Define if unit test using EntityFrameworkCore in memory sql server database or on-premise sql server database.
        /// Only works for unit test project.
        /// </summary>
        public Boolean IsInMemoryDatabase
        {
            get {
                return Boolean.Parse(this._config.GetSection("App")["IsInMemoryDatabase"]);
            }
        }
        #endregion


        /// <summary>
        ///  Reference to section "App:SPAConfig".
        ///  It works single page application deployment setting.
        /// </summary>
        public SPAConfig SPAConfig
        {
            get
            {
                SPAConfig config = new SPAConfig(_config);
                return config;
            }
        }


        /// <summary>
        ///  Reference to section "IdentityServer" in appsettings.json.
        ///  Work for identity server signle sign on config.
        /// </summary>
        public IdentityServerConfig IdentityServerConfig {
            get {
                IdentityServerConfig config = new IdentityServerConfig(this._config);
                return config;
            }        
        }


        /// <summary>
        ///  Refered to section called "App:AzureKeyVaultConfig" in appsetting.json. 
        ///  Work for Azure Key Vault service access.
        ///  
        /// Reference: https://medium.com/volosoft/using-azure-key-vault-with-asp-net-core-in-development-environment-105d60945f2#:~:text=Azure%20Key%20Vault%20is%20a,%2C%20certificates%2C%20and%20other%20secrets.&text=Azure%20Key%20Vault%20service%20is,Vault%20from%20the%20development%20environment.
        /// </summary>
        public AzureKeyVaultConfig AzureKeyVaultConfig
        { 
            get {
                AzureKeyVaultConfig config = new AzureKeyVaultConfig(this._config);
                return config;
            }
        }

        /// <summary>
        ///  Test Azure App Configuration Key Vault Reference setting.
        /// </summary>
        public string KeyVaultMessage { get {
                return this._config.GetSection("App:KeyVaultMessage").Value;
            } }

        public ApplicationSettings ApplicationSettings
        {
            get
            {
                ApplicationSettings config = new ApplicationSettings(this._config);
                return config;
            }
        }

        /// <summary>
        /// DocuSignConfig load from appsettings
        /// </summary>
        public DocuSignConfig DocuSignSettings
        {
            get
            {
                return new DocuSignConfig(this._config);
            }
        }
    }
}

