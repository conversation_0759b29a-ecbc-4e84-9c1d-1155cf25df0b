﻿using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BdoPartner.Plans.Common.Config
{
    public class DocuSignConfig
    {
        const string SectionRootName = "DocuSignSettings";

        private IConfiguration _config;
        public DocuSignConfig(IConfiguration config)
        {
            _config = config;
        }

        public string Username { get
            {
                return _config.GetSection($"App:{SectionRootName}:Username")?.Value;
            } 
        }

        public string Password
        {
            get
            {
                return _config.GetSection($"App:{SectionRootName}:Password")?.Value;
            }
        }

        public string IntegratorKey
        {
            get
            {
                return _config.GetSection($"App:{SectionRootName}:integratorKey")?.Value;
            }
        }

        public bool UseKeyVault
        {
            get
            {
                bool value;

                return (_config.GetSection($"App:{SectionRootName}:useKeyVault") != null)
                    && bool.TryParse(_config.GetSection($"App:{SectionRootName}:useKeyVault").Value, out value)
                    && value;
            }
        }

        public string EnvironmentUrl
        {
            get
            {
                return _config.GetSection($"App:{SectionRootName}:environmentUrl")?.Value;
            }
        }

        public bool UseOverrideEmail
        {
            get
            {
                var result = false;

                return (_config.GetSection($"App:{SectionRootName}:UseOverrideEmail") != null)
                    && bool.TryParse(_config.GetSection($"App:{SectionRootName}:UseOverrideEmail").Value, out result)
                    && result
                    && !string.IsNullOrEmpty($"{_config.GetSection($"App:{SectionRootName}:OverrideEmails")}");
            }
        }

        public string[] OvOverrideEmails
        {
            get
            {
                return _config.GetSection($"App:{SectionRootName}:OverrideEmails").Value.Split(';');
            }
        }
    }
}
