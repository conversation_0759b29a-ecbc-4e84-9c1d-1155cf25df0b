﻿using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Text;

namespace BdoPartner.Plans.Common.Config
{
    /// <summary>
    /// Config settings from appsettings.json or Azure App Configuration service.
    ///  Reference: https://blogs.technet.microsoft.com/<PERSON><PERSON><PERSON><PERSON><PERSON>/tip-of-the-week-how-to-access-configuration-from-controller-in-asp-net-core-2-0/
    /// </summary>
    public interface IConfigSettings
    {
        IConfiguration Config { get; }
          
        #region SQL Server Database Connections

        /// <summary>
        ///  Database connection for MySql/Sql Server database.
        /// </summary>
        string DatabaseConnection { get; }
            

        #endregion

        /// <summary>
        ///  Config setting in Web API or client portal. 
        ///  Corporate with Identity Server 4, setting called ApiName.
        /// </summary>
        string ApplicationCode { get; }

        /// <summary>
        ///  Public WEB Api Server address.
        /// </summary>
        string ApiRootUrl { get; }

        /// <summary>
        ///  Allow accessing web api portal domains. Same settings as records in table dbo.ClientDomain.
        /// </summary>
        string[] AllowedDomains { get; }

        /// <summary>
        /// Work for local host test only. Check domain from field "TestDomain" in table dbo.ClientDomain.
        /// Work for accessing domain validation in identity server.
        /// Note: For QA, UAT, Production server, this setting should be false.
        /// Only work for developer debuging codes in localhost.
        /// </summary>
        bool IsLocalHostTest { get; }
               

        #region Email Setting
        string FromEmail { get; }

        string MailServer { get; }

        int SMTPServerPort { get; }

        string SMTPUser { get; }

        string SMTPPassword { get; }

        bool SMTPEnableSsL { get; }
        
        /// <summary>
        /// If IsNotificationTest = true, system will send email to "AdministratorEmail" only and will not send email to real email receiver.
        /// Work for TaskScheduler.Notification and other notifications.
        /// Corporate with EmailHelper.
        /// </summary>
        bool IsNotificationTest { get; }


        /// <summary>
        ///  local database connection timeout setting.
        ///  It is integer value of seconds.
        ///  Default value = 360 seconds.
        /// </summary>
        int DatabaseCommandTimeout { get; }

        #endregion

        #region Google ReCaptcha
        /// <summary>
        ///  Define enable or disable Google reCaptcha Secret. Work for forgot password UI. Note: For Localhost debugging, set value = false.
        /// </summary>
        bool EnableReCaptcha { get; }

        /// <summary>
        ///  Google reCaptcha Secret. Work for forgot password UI.
        /// </summary>
        string ReCaptchaSecret { get; }

        /// <summary>
        /// Google reCaptcha SiteKey. Work for forgot password UI.
        /// </summary>
        string ReCaptchaSiteKey { get; }

        #endregion


        #region Azure Storage Account Settings. Get Information from Azure Portal, Storage Accounts -> Access keys.  Note: Disabled in First ON Site Solution.
        string AzureStorageAccount { get; }        

        string AzureStorageKey1 { get; }
        
        string AzureStorageConnection1 { get; }
        
        string AzureStorageKey2 { get; }
        
        string AzureStorageConnection2 { get; }

        string AzureStorageContainerName { get; }
        #endregion


        #region Unit Test Associate

        /// <summary>
        /// Define if unit test using EntityFrameworkCore in memory sql server database or on-premise sql server database.
        /// Only works for unit test project.
        /// </summary>
        Boolean IsInMemoryDatabase { get; }
        #endregion

        /// <summary>
        ///  Reference to section "App:SPAConfig".
        ///  It works for single page application deployment setting.
        /// </summary>
       SPAConfig SPAConfig { get; }

        /// <summary>
        ///  Refered to section "App:IdentityServer" in appsettings.json.
        ///  Work for identity server signle sign on config.
        /// </summary>
        IdentityServerConfig IdentityServerConfig { get; }

        /// <summary>
        ///  Refered to section called "App:AzureKeyVaultConfig" in appsetting.json. 
        ///  Work for Azure Key Vault service access.
        ///  
        /// Reference: https://medium.com/volosoft/using-azure-key-vault-with-asp-net-core-in-development-environment-105d60945f2#:~:text=Azure%20Key%20Vault%20is%20a,%2C%20certificates%2C%20and%20other%20secrets.&text=Azure%20Key%20Vault%20service%20is,Vault%20from%20the%20development%20environment.
        /// </summary>
        AzureKeyVaultConfig AzureKeyVaultConfig { get; }

        /// <summary>
        ///  Test Azure App Configuration Key Vault Reference setting.
        /// </summary>
        string KeyVaultMessage { get; }
        /// <summary>
        /// UI Application Settings
        /// </summary>
        ApplicationSettings ApplicationSettings { get; }
        /// <summary>
        /// DocuSign Settings
        /// </summary>
        DocuSignConfig DocuSignSettings { get; }
    }
}
