﻿using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BdoPartner.Plans.Common.Config
{
    /// <summary>
    /// 
    ///  Reference: https://www.scottbrady91.com/Identity-Server/Outsourcing-IdentityServer4-Token-Signing-to-Azure-Key-Vault
    /// </summary>
    public enum IdentitySigningKeyProvider {
        /// <summary>
        ///  Only works for local machine development and debuging.
        /// </summary>
        DeveloperSigningCredential = 1,
        /// <summary>
        ///  Keep Signing Key in SQL Server Database table [identity].[SigningCredential]
        /// </summary>
        Database = 2,
        /// <summary>
        ///  Keep Signing Key in Azure Key Vault.
        /// </summary>
        AzureKeyValue = 3
    }

    /// <summary>
    ///  Matching appsettings section IdentityServer
    /// </summary>
    public class IdentityServerConfig
    {
        private IConfiguration _config;

        public IdentityServerConfig(IConfiguration config)
        {
            this.IdentitySigningKeyProvider = IdentitySigningKeyProvider.DeveloperSigningCredential;
            this._config = config;

            this.IAMUri = _config.GetSection("IdentityServer:IAMUri").Value;
            this.ExternalIdentityProviders = _config.GetSection("IdentityServer:ExternalIdentityProviders").Get<List<ExternalIdentityProvider>>();
            this.Clients = _config.GetSection("IdentityServer:Clients").Get<List<Client>>();

            int clockSkew = 5;
            int.TryParse(_config.GetSection("IdentityServer:IdentityServerJwtValidationClockSkew").Value, out clockSkew);
            this.IdentityServerJwtValidationClockSkew = clockSkew;
        }

        /// <summary>
        ///  Current Identity Server end point's domain.
        /// </summary>
        public string IAMUri { get; set; }

        /// <summary>
        /// Integer value of Minutes. Default value = 5 minutes. Note: This is important to solve problem in client's infinit looping in login which caused by client's local machine datetime setting is not correct.
        /// Set in Web API resource portal.
        /// Reference: https://github.com/IdentityServer/IdentityServer4.AccessTokenValidation/issues/90
        /// https://github.com/IdentityServer/IdentityServer4/issues/497
        /// 
        /// </summary>
        public int IdentityServerJwtValidationClockSkew { get; set; }

        /// <summary>
        ///  Reference to Identity Server built in setting section "IdentityServer:Clients".
        /// </summary>
        public List<Client> Clients { get; set; }

        /// <summary>
        ///  Custom settings for External Identity Providers. 
        /// </summary>
        public List<ExternalIdentityProvider> ExternalIdentityProviders { get; set; }

        public IdentitySigningKeyProvider IdentitySigningKeyProvider { get; set; }
    }

    /// <summary>
    ///  Matching appsettings section "IdentitySever:Clients"
    /// </summary>
    public class Client { 
      public string ClientId { get; set; }

      public string ClientName { get; set; }

      public string ClientUri { get; set; }
    }

    /// <summary>
    ///  Matching appsettings section "IdentityServer:ExternalIdentityProviders"
    /// </summary>
    public class ExternalIdentityProvider
    {
        public string ProviderName {get; set;}

        public string Description { get; set; }

        public string AzureADAuthority { get; set; }
        

        public string AzureADClientId { get; set; }

        public string AzureADTenant { get; set; }

        /// <summary>
        /// Azure AD registered application's preset client secret.
        /// Check this value under "Certificates & secrets" section under "Identity Server" application in Azure AD.
        /// </summary>
        public string AzureADClientSecret { get; set; }

        /// <summary>
        /// Work for call Azure Graph APIs. It defined API version.
        /// Note:  For B2C user managment, be sure to use the 1.6 Graph API version.
        /// Reference: https://github.com/AzureADQuickStarts/B2C-GraphAPI-DotNet/blob/master/B2CGraphClient/B2CGraphClient.cs
        /// https://github.com/AzureADQuickStarts/B2C-GraphAPI-DotNet/blob/master/B2CGraphClient/Globals.cs
        /// </summary>
        public string AzureADGraphVersion { get; set; }

        public string CallbackPath { get; set; }

        public string SignedOutCallbackPath { get; set; }

        public string RemoteSignOutPath { get; set; }
    }
}
