﻿using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BdoPartner.Plans.Common.Config
{
    /// <summary>
    ///  It is container for keeping all config settings associated to hosting Single Page Application (Angular or React) build
    ///  in web api portal (server side end points) sub directory (url routing matter), and the SPA physical build files could be stored in web api portal's static file folder or Azure Blob Storage container.
    ///  
    ///  Associated settings is in "SPAConfig" section in appsettings.json file. 
    ///  Note: In local development environment, please check appsettings.development.json in BdoPartner.Plans.Web.API project.
    ///  In Azure App Service hosting portals, please check 
    /// </summary>
    public class SPAConfig
    {
        private IConfiguration _config;

        public SPAConfig(IConfiguration config)
        {
            _config = config;
        }


        /// <summary>
        ///
        /// Work for scenario that Angular/React single page applications 
        /// need to be hosted under web api portal and shared same domain.
        ///
        /// If value = true, Angular/React build files will be kept in Azure Blob Storage.
        /// If value = false, Angular/React build files will be  kept in Web API local static folders.
        /// Coporate with SPAPaths, SPAHostingAzureBlobStorageAccessKey settings.
        ///  
        /// </summary>
        public Boolean SPAHostingInAzureBlobStorage
        {
            get
            {
                bool result = false;

                if (_config.GetSection("App:SPAConfig:SPAHostingInAzureBlobStorage") != null)
                {

                    bool.TryParse(_config.GetSection("App:SPAConfig:SPAHostingInAzureBlobStorage").Value, out result);
                }

                return result;
            }
        }


        /// <summary>
        /// Reference to setting called "SPAPaths" in appsettings.json.
        ///
        /// When "SAPHostingInAzureBlobStorage" = false,
        ///
        /// Below is sub folder paths under web api hosting folder which are used to keep SPA sites' associated build files, 
        /// and their alter url for routing.
        /// Note: System supports multiple Angular or React hosting inside same domain with different sub paths.
        /// Data format: "[SPA1 alter url]|[SPA1 files folder path],[SPA2 alter url]|[SPA2 files folder path],..."
        ///
        /// Note: file alter url has to have "/" at the beginning, 
        /// and file's file path has to have ".\\" at the beginning, 
        ///  if the Angular built files are kept in the local static folders under the web api portal.
        /// 
        ///  Here Demo hosting two Single Page Applications (Angular and React) inside the Web API portal.
        ///
        /// **********************************************************************************************
        ///
        /// When "SPHostingInAzureBlobStorage" = true
        /// Data format: "[SPA1 alter url]|[SPA1 files Azure Blob Storage container name],[SPA2 alter url]|[SPA2 files Azure Blob Storage container name],..."
        /// Note: In Azure Blob Storage service, one container only keep one single page application build and the SPA build files need to be saved in root folder ("/") of the container.
        ///
        /// 
        /// </summary>
        public Dictionary<string, string> SPAPaths
        {
            get
            {
                Dictionary<string, string> result = new Dictionary<string, string>();

                try
                {
                    if (_config.GetSection("App:SPAConfig:SPAPaths") != null)
                    {
                        var apps = _config.GetSection("App:SPAConfig:SPAPaths").Value.Split(",".ToCharArray(), StringSplitOptions.RemoveEmptyEntries);
                        if (apps != null)
                        {
                            foreach (var app in apps)
                            {
                                var paths = app.Split("|".ToCharArray(), StringSplitOptions.RemoveEmptyEntries);
                                result.Add(paths[0].ToLower(), paths[1].ToLower()); // Note: Url Alter as key and file relative path as value.
                            }
                        }
                    }
                }
                catch
                {
                    // Do nothing.
                }

                return result;
            }
        }


        /// <summary>
        ///
        /// There is another option for single page application build files hosting.
        /// We can save the Angular/Rect build files in Azure Blob Storage and access files there.
        /// "SPAHostingAzureBlobStorageConnection" is connection string for Azure blob storage access.
        /// Note: This setting works with SPAHostingInAzureBlobStorage = true.
        /// 
        /// </summary>
        public string SPAHostingAzureBlobStorageConnection
        {
            get {
                return _config.GetSection("App:SPAConfig:SPAHostingAzureBlobStorageConnection").Value;
            }           
        }
    }
}