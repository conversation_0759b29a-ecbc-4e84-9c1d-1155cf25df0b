﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;

namespace BdoPartner.Plans.Common
{
    public static class Enumerations
    {
        /// <summary>
        ///  Reference to table Gender.
        /// </summary>
        public enum Gender
        {
            [Description("Female")]
            Female = 1,
            [Description("Male")]
            Maile = 2
        }

     
        /// <summary>
        ///
        ///  Note: <PERSON><PERSON><PERSON> is able to modify following definitions based on business logic.
        ///
        ///  Corporate with OneUIAuthorizeAttribute.cs
        ///
        ///  Reference to IAM server database table [dbo].[Role].
        ///  Note: multiple BDO partner sites projects shared same BDO.InternalIdentity solution, that is why the role Id is not sequence.
        /// </summary>
        public enum Role
        {
            /// <summary>
            /// Partner Plans Administrator - has full access to all system functions
            /// Note: Create/Update/Delete user account need to go through Azure AD.
            /// </summary>
            [Description("Partner Plans Administrator")]
            PPAdministrator = 15,

            /// <summary>
            /// Partner role - can manage their own partner plans and assessments
            /// </summary>
            [Description("Partner")]
            Partner = 3,

            /// <summary>
            /// New Partner role - To be Partner in next (plan) year
            /// </summary>
            [Description("New Partner")]
            NewPartner = 16,

            /// <summary>
            /// Partner Plans Executive Leadership - has executive level access
            /// </summary>
            [Description("Partner Plans Executive Leadership")]
            PPExecutiveLeadership = 17
        }

        /// <summary>
        /// Note: Developer is able to modify following definitions based on business logic.
        ///
        /// Reference to IAM server database table [Permission].
        /// </summary>
        public enum Permission
        {
            // Partner Plan Management Permissions
            Login = 1,
            TrackOwnPartnerPlan = 2,
            TrackAllPartnerPlans = 3,
            DraftSubmitPartnerPlan = 4,
            EditPartnerPlansUnderReview = 5,
            PartnerPlansFinalSubmission = 6,
            MidEndYearSelfAssessment = 7,
            MidEndYearReviewerAssessment = 8,
            ViewSubmittedPartnerPlans = 9,
            EditSubmittedPartnerPlans = 10,
            ExportPlanDataToExcel = 11,
            ManagePartnerReviewerRelationships = 12,
            UploadKPIData = 13,
            EditPublishInputForm = 14
        }

        /// <summary>
        ///  Multiple languages support. Corporate with dbo.Localization table.
        ///  Note: Records same value as table [dbo].[Language].
        /// </summary>
        public enum Language
        {
            /// <summary>
            /// Default is en-us. United State English. en-us
            /// </summary>
            [Description("English")]
            EN = 1,
            /// <summary>
            ///  Canadian French. fr-ca
            /// </summary>
            [Description("French")]
            FR = 2,
        }

        /// <summary>
        ///  Refer to Status of Form.
        ///  Matches with FormStatus in the database.
        /// </summary>
        public enum FormStatus {

            /// <summary>
            ///  Form is in draft state.
            /// </summary>
            [Description("Draft")]
            Draft = 0,
            /// <summary>
            ///  Form is submitted for review.
            /// </summary>
            [Description("Submitted")]
            Submitted = 1,
            /// <summary>
            ///  Form is approved.
            /// </summary>
            [Description("Approved")]
            Approved = 2,
            /// <summary>
            ///  Form is rejected.
            /// </summary>
            [Description("Rejected")]
            Rejected = 3,
            /// <summary>
            ///  Form is reopened for further action.
            /// </summary>
            [Description("Reopened")]
            Reopened = 4
        }

        /// <summary>
        ///  Refer to Status of Questionnaire.
        ///  Matches with QuestionnaireStatus in the database.
        /// </summary>
        public enum QuestionnaireStatus
        {
            /// <summary>
            ///  Questionnaire is in draft state.
            /// </summary>
            [Description("Draft")]
            Draft = 0,
            /// <summary>
            ///  Questionnaire is published and active.
            /// </summary>
            [Description("Published")]
            Published = 1,
            /// <summary>
            ///  Questionnaire is archived and no longer active.
            /// </summary>
            [Description("Archived")]
            Archived = 2
        }

        /// <summary>
        ///  Refer to Status of Partner Reviewer Upload.
        ///  Matches with Status column in PartnerReviewerUpload table.
        /// </summary>
        public enum PartnerReviewerUploadStatus
        {
            /// <summary>
            ///  Upload is in progress.
            /// </summary>
            [Description("Uploading")]
            Uploading = 0,
            /// <summary>
            ///  File has been uploaded successfully.
            /// </summary>
            [Description("Uploaded")]
            Uploaded = 1,
            /// <summary>
            ///  Data validation is in progress.
            /// </summary>
            [Description("Validating")]
            Validating = 2,
            /// <summary>
            ///  Data validation passed successfully.
            /// </summary>
            [Description("Validation Passed")]
            ValidationPassed = 3,
            /// <summary>
            ///  Data validation failed with errors.
            /// </summary>
            [Description("Validation Failed")]
            ValidationFailed = 4,
            /// <summary>
            ///  Data has been submitted and processed.
            /// </summary>
            [Description("Submitted")]
            Submitted = 5
        }

        /// <summary>
        ///  Refer to Status of Partner Reference Data Upload.
        ///  Matches with Status column in PartnerReferenceDataUpload table.
        /// </summary>
        public enum PartnerReferenceDataUploadStatus
        {
            /// <summary>
            ///  Upload is in progress.
            /// </summary>
            [Description("Uploading")]
            Uploading = 0,
            /// <summary>
            ///  File has been uploaded successfully.
            /// </summary>
            [Description("Uploaded")]
            Uploaded = 1,
            /// <summary>
            ///  Data validation is in progress.
            /// </summary>
            [Description("Validating")]
            Validating = 2,
            /// <summary>
            ///  Data validation passed successfully.
            /// </summary>
            [Description("Validation Passed")]
            ValidationPassed = 3,
            /// <summary>
            ///  Data validation failed with errors.
            /// </summary>
            [Description("Validation Failed")]
            ValidationFailed = 4,
            /// <summary>
            ///  Data has been submitted and processed.
            /// </summary>
            [Description("Submitted")]
            Submitted = 5,
           
        }

        /// <summary>
        ///  Refer to Cycle types for Partner Reference Data.
        ///  Matches with Cycle column in PartnerReferenceData related tables.
        /// </summary>
        public enum PartnerReferenceDataCycle
        {
            /// <summary>
            ///  Planning cycle.
            /// </summary>
            [Description("Planning")]
            Planning = 0,
            /// <summary>
            ///  Mid Year Review cycle.
            /// </summary>
            [Description("Mid Year Review")]
            MidYearReview = 1,
            /// <summary>
            ///  End Year Review cycle.
            /// </summary>
            [Description("End Year Review")]
            EndYearReview = 2
        }

        /// <summary>
        ///  Refer to Column Data Types for Partner Reference Data.
        ///  Matches with ColumnDataType column in PartnerReferenceDataMetaDetails table.
        /// </summary>
        public enum PartnerReferenceDataColumnType
        {
            /// <summary>
            ///  Text data type.
            /// </summary>
            [Description("Text")]
            Text = 0,
            /// <summary>
            ///  Numeric data type.
            /// </summary>
            [Description("Numeric")]
            Numeric = 1,
            /// <summary>
            ///  Blank or default data type.
            /// </summary>
            [Description("Blank")]
            Blank = 2
        }

    }
}
