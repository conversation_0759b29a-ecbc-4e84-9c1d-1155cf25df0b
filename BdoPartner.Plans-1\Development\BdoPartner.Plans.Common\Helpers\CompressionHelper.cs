using System;
using System.IO;
using System.IO.Compression;
using System.Text;
using System.Text.Json;

namespace BdoPartner.Plans.Common.Helpers
{
    /// <summary>
    /// Helper class for compressing and decompressing JSON data with base64 encoding
    /// </summary>
    public static class CompressionHelper
    {
        /// <summary>
        /// Compresses a JSON string using GZip compression and encodes it as base64
        /// </summary>
        /// <param name="jsonString">The JSON string to compress</param>
        /// <returns>Base64 encoded compressed string</returns>
        public static string CompressAndEncode(string jsonString)
        {
            if (string.IsNullOrEmpty(jsonString))
                return string.Empty;

            try
            {
                byte[] jsonBytes = Encoding.UTF8.GetBytes(jsonString);
                
                using (var memoryStream = new MemoryStream())
                {
                    using (var gzipStream = new GZipStream(memoryStream, CompressionMode.Compress))
                    {
                        gzipStream.Write(jsonBytes, 0, jsonBytes.Length);
                    }
                    
                    byte[] compressedBytes = memoryStream.ToArray();
                    return Convert.ToBase64String(compressedBytes);
                }
            }
            catch (Exception ex)
            {
                // Log the exception if needed
                System.Diagnostics.Trace.TraceError($"Error compressing JSON: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// Decodes a base64 string and decompresses it using GZip decompression
        /// </summary>
        /// <param name="base64CompressedString">The base64 encoded compressed string</param>
        /// <returns>Decompressed JSON string</returns>
        public static string DecodeAndDecompress(string base64CompressedString)
        {
            if (string.IsNullOrEmpty(base64CompressedString))
                return string.Empty;

            try
            {
                byte[] compressedBytes = Convert.FromBase64String(base64CompressedString);
                
                using (var memoryStream = new MemoryStream(compressedBytes))
                {
                    using (var gzipStream = new GZipStream(memoryStream, CompressionMode.Decompress))
                    {
                        using (var reader = new StreamReader(gzipStream, Encoding.UTF8))
                        {
                            return reader.ReadToEnd();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Log the exception if needed
                System.Diagnostics.Trace.TraceError($"Error decompressing JSON: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// Checks if a string appears to be base64 encoded compressed data
        /// This is a simple heuristic check - not foolproof
        /// </summary>
        /// <param name="input">The string to check</param>
        /// <returns>True if it appears to be base64 encoded, false otherwise</returns>
        public static bool IsBase64Encoded(string input)
        {
            if (string.IsNullOrEmpty(input))
                return false;

            try
            {
                // Basic check: base64 strings should be divisible by 4 after padding
                string trimmed = input.Trim();
                if (trimmed.Length % 4 != 0)
                    return false;

                // Try to decode - if it fails, it's not valid base64
                Convert.FromBase64String(trimmed);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Safely processes JSON data - if it's compressed and base64 encoded, decompress it.
        /// If it's already plain JSON, return as is.
        /// </summary>
        /// <param name="jsonData">The JSON data that might be compressed</param>
        /// <returns>Plain JSON string</returns>
        public static string SafeDecompressJson(string jsonData)
        {
            if (string.IsNullOrEmpty(jsonData))
                return string.Empty;

            // If it looks like JSON (starts with { or [), return as is
            string trimmed = jsonData.Trim();
            if (trimmed.StartsWith("{") || trimmed.StartsWith("["))
                return jsonData;

            // If it looks like base64, try to decompress
            if (IsBase64Encoded(trimmed))
            {
                string decompressed = DecodeAndDecompress(trimmed);
                var deserialized = JsonSerializer.Deserialize<object>(decompressed);

                // Step 3: Serialize back to valid JSON
                var serializeOptions = new JsonSerializerOptions
                {
                    WriteIndented = true // Optional: for readable output
                };
                decompressed = JsonSerializer.Serialize(deserialized, serializeOptions);

                return !string.IsNullOrEmpty(decompressed) ? decompressed : jsonData;
            }

            // Return original if we can't determine the format
            return jsonData;
        }
    }
}
