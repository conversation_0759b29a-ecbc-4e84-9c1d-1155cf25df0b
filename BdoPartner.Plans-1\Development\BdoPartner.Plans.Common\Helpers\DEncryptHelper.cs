﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;

namespace BdoPartner.Plans.Common.Helpers
{
    /// <summary>
    ///  Shared method for encryption and decription.
    /// </summary>
    public static class DEncryptHelper
    {
        private static string key = "ts7IOebAUdmXJrlqWyTE62rctxFWj8HLKXb3eu7klfs=";

        public static string Encrypt(object original)
        {
            try
            {
                if (original == null) return "";
                byte[] buff = Encoding.UTF8.GetBytes(original.ToString());
                byte[] kb = Encoding.UTF8.GetBytes(key);
                string str = Convert.ToBase64String(Encrypt(buff, kb));
                return str.TrimEnd('=').Replace('+', '-').Replace('/', '_');
            }
            catch
            {
                return string.Empty;
            }
        }

        public static string Decrypt(string encrypted)
        {
            try
            {
                if (string.IsNullOrEmpty(encrypted)) return "";
                encrypted = encrypted.Replace('-', '+').Replace('_', '/');
                var padding = 3 - ((encrypted.Length + 3) % 4);
                if (padding != 0)
                {
                    encrypted = encrypted + new string('=', padding);
                }
                byte[] buff = Convert.FromBase64String(encrypted);
                byte[] kb = Encoding.UTF8.GetBytes(key);
                return Encoding.UTF8.GetString(Decrypt(buff, kb));
            }
            catch
            {
                return string.Empty;
            }
        }

        public static int DecryptInt(string encrypted)
        {
            string str = Decrypt(encrypted);
            int result = 0;
            if(int.TryParse(str, out result))
            {
                return result;
            }
            return 0;
        }

        private static byte[] Encrypt(byte[] original, byte[] key)
        {
            TripleDESCryptoServiceProvider des = new TripleDESCryptoServiceProvider();
            des.Key = MakeMD5(key);
            des.Mode = CipherMode.ECB;
            return des.CreateEncryptor().TransformFinalBlock(original, 0, original.Length);
        }

        private static byte[] Decrypt(byte[] encrypted, byte[] key)
        {
            TripleDESCryptoServiceProvider des = new TripleDESCryptoServiceProvider();
            des.Key = MakeMD5(key);
            des.Mode = CipherMode.ECB;
            return des.CreateDecryptor().TransformFinalBlock(encrypted, 0, encrypted.Length);
        }

        private static byte[] MakeMD5(byte[] original)
        {
            MD5CryptoServiceProvider hashmd5 = new MD5CryptoServiceProvider();
            byte[] keyhash = hashmd5.ComputeHash(original);
            hashmd5 = null;
            return keyhash;
        }

        public static string MD5Encrypt64(string str)
        {
            MD5 md5 = MD5.Create();
            byte[] data = md5.ComputeHash(Encoding.UTF8.GetBytes(str));
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < data.Length; i++)
            {
                sb.Append(data[i].ToString("x2"));
            }
            string key = sb.ToString().Replace("+", "-").Replace("%", "_").Replace("@", "").Replace("&", "");
            if(key.Length > 24)
            {
                key = key.Substring(8, 16);
            };
            return key;
            //return Convert.ToBase64String(data);
        }

    }
}
