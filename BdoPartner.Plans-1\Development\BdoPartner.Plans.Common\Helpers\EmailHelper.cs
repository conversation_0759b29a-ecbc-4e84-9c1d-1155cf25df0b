﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using System.Text;
using System.Xml;
using System.Xml.Xsl;
using System.Web;
using System.Net.Mail;
using BdoPartner.Plans.Common.Config;

namespace BdoPartner.Plans.Common.Helpers
{
    /// <summary>
    ///  Value is same as email template's file name.
    /// </summary>
    public enum EmailType
    {
        UserAddAccountRequestNotification,
        NewUserSetPasswordNotification,
        ForgotPasswordNotification,
        /// <summary>
        ///  Send email to system administrator when new user registration submit.
        /// </summary>
        UserRegisterEmail,
        AccountRequestStatusChangeNotification,
        UserRegisterNotification
    }


    public class EmailHelper
    {
        #region private variable
        private string FromEmail { get; set; }
        private string MailServer { get; set; }
        private int SMTPServerPort { get; set; }
        private string SMTPUser { get; set; }
        private string SMTPPassword { get; set; }
        private bool SMTPEnableSsL { get; set; }
        public string IdentityServerUrl { get; set; }
      
        #endregion


        /// <summary>
        /// constructor method
        /// </summary>
        /// <param name="emailType"></param>
        public EmailHelper(IConfigSettings config)
        {
            FromEmail = config.FromEmail;
            MailServer = config.MailServer;
            SMTPServerPort = config.SMTPServerPort;
            SMTPUser = config.SMTPUser;
            SMTPPassword = config.SMTPPassword;
            SMTPEnableSsL = config.SMTPEnableSsL;
            IdentityServerUrl = config.IdentityServerConfig.IAMUri;
        }


        public void Send(string mailTo, string subject, string body)
        {
            this.SendEmail(mailTo, null, subject, body, null);
        }

        public void Send(string mailTo, string subject, EmailType emailType, Dictionary<string, string> parameters = null, Dictionary<string, byte[]> attachment = null, string mailFrom = null)
        {
            string emailBody = GetEmailTemplate(emailType);
            if (!string.IsNullOrEmpty(emailBody))
            {
                if (parameters != null && parameters.Count > 0)
                {
                    foreach (var item in parameters)
                    {
                        emailBody = emailBody.Replace("$" + item.Key + "$", item.Value);
                    }
                }

                emailBody = emailBody.Replace("$IdentityServerUrl$", IdentityServerUrl);
              
            }
            else
            {
                emailBody = "";
            }
            this.SendEmail(mailTo, mailFrom, subject, emailBody, attachment);
        }

        public void Send(string mailTo, string subject, EmailType emailType, Object contentEntity = null, Dictionary<string, byte[]> attachment = null, string mailFrom = null)
        {
            Dictionary<string, string> parameters = new Dictionary<string, string>();
            if (contentEntity != null)
            {
                Type type = contentEntity.GetType();
                PropertyInfo[] properties = type.GetProperties();
                foreach (var prop in properties)
                {
                    object propValue = prop.GetValue(this, null);
                    if (propValue != null)
                    {
                        parameters.Add(prop.Name, propValue.ToString());
                    }
                }
            }
            Send(mailTo, subject, emailType, parameters, attachment, mailFrom);
        }


        /// <summary>
        /// get email teamplate for special email type
        /// </summary>
        /// <param name="emailType"></param>
        /// <returns></returns>
        private string GetEmailTemplate(EmailType emailType)
        {
            try
            {
                string templatePath = Path.Combine("EmailTemplate", string.Format("{0}.html", emailType.ToString()));
                return File.ReadAllText(templatePath); ;
            }
            catch
            {
                return string.Empty;
            }
        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="mailfrom"></param>
        /// <param name="mailto">Support multiple recipents. Email splited by ';'. Dataformat: email1;email2;...</param>
        /// <param name="subject"></param>
        /// <param name="content"></param>
        /// <param name="attachment"></param>
        /// <param name="smtp"></param>
        /// <param name="smtp_userid"></param>
        /// <param name="smtp_password"></param>
        private void SendEmail(string mailto, string sender, string subject, string content, Dictionary<string, byte[]> attachment)
        {

            string mailFrom = sender;
            //if sender no provided, get the value from configuration
            if (string.IsNullOrEmpty(mailFrom))
                mailFrom = FromEmail;

            string[] mail_to = mailto.Split(";".ToCharArray(), StringSplitOptions.RemoveEmptyEntries);
            MailMessage email = new MailMessage();

            email.From = new MailAddress(mailFrom);

            foreach (string to in mail_to)
            {
                if (string.IsNullOrEmpty(to) || to.Length < 3) continue;
                email.To.Add(new MailAddress(to));
            }

            email.BodyEncoding = Encoding.UTF8;
            email.SubjectEncoding = Encoding.UTF8;
            email.Priority = MailPriority.Normal;
            email.IsBodyHtml = true;
            email.Subject = subject;
            email.Body = content;

            if (attachment != null && attachment.Count > 0)
            {
                foreach (string key in attachment.Keys)
                {
                    Attachment emaiAttach = new Attachment(new MemoryStream(attachment[key]), key);
                    email.Attachments.Add(emaiAttach);
                }
            }

            //handle GMail server.
            SmtpClient smtp_client = new SmtpClient(MailServer, SMTPServerPort);
            if (!string.IsNullOrEmpty(SMTPUser) && !string.IsNullOrEmpty(SMTPPassword))
            {
                smtp_client.UseDefaultCredentials = (MailServer.ToLower() == "smtp.gmail.com" || mailFrom.ToLower().EndsWith("@gmail.com"));
                smtp_client.Credentials = new System.Net.NetworkCredential(SMTPUser, SMTPPassword);
            }
            
            smtp_client.EnableSsl = SMTPEnableSsL;
            smtp_client.Timeout = 30000;
            try
            {
                smtp_client.Send(email);
            }
            catch
            {
                throw;
            }           
            finally
            {
                //release the attacthment file
                foreach (Attachment item in email.Attachments)
                {
                    item.Dispose();
                }

                if (smtp_client != null)
                {
                    smtp_client.Dispose();
                }
            }
        }
    }
}