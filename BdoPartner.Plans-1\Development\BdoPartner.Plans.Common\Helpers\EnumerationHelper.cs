﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace BdoPartner.Plans.Common.Helpers
{
    public static class EnumerationHelper<T>
    {
        private static IList<T> GetValues(Enum value)
        {
            var enumValues = new List<T>();
            foreach (FieldInfo fi in value.GetType().GetFields(BindingFlags.Static | BindingFlags.Public))
            {
                enumValues.Add((T)Enum.Parse(value.GetType(), fi.Name, false));
            }
            return enumValues;
        }

        public static T Parse(string value)
        {
            return (T)Enum.Parse(typeof(T), value, true);
        }

        private static IList<string> GetNames(Enum value)
        {
            return value.GetType().GetFields(BindingFlags.Static | BindingFlags.Public).Select(fi => fi.Name).ToList();
        }

        /// <summary>
        /// get enum list for drop down list binding
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        public static IList<KeyValuePair<int, string>> GetDisplayValues(Enum value, bool IncludeUnknown = false)
        {
            List<KeyValuePair<int, string>> result = new List<KeyValuePair<int, string>>();
            IList<T> values = GetValues(value);
            foreach (T v in values)
            {
                if (IncludeUnknown || (!IncludeUnknown && (int)(v.GetHashCode()) > 0))
                    result.Add(new KeyValuePair<int, string>((int)(v.GetHashCode()), GetDisplayValue(v)));

            }
            return result;
        }


        public static IList<KeyValuePair<string, string>> GetDisplayNameList(Enum value, bool IncludeEmpty = false)
        {
            List<KeyValuePair<string, string>> result = new List<KeyValuePair<string, string>>();
            IList<T> values = GetValues(value);
            if (IncludeEmpty)
            {
                result.Add(new KeyValuePair<string, string>("", ""));
            }
            foreach (T v in values)
            {
                result.Add(new KeyValuePair<string, string>((v.ToString()), GetDisplayValue(v)));
            }
            return result;
        }


        private static string lookupResource(Type resourceManagerProvider, string resourceKey)
        {
            foreach (PropertyInfo staticProperty in resourceManagerProvider.GetProperties(BindingFlags.Static | BindingFlags.NonPublic | BindingFlags.Public))
            {
                if (staticProperty.PropertyType == typeof(System.Resources.ResourceManager))
                {
                    System.Resources.ResourceManager resourceManager = (System.Resources.ResourceManager)staticProperty.GetValue(null, null);
                    return resourceManager.GetString(resourceKey);
                }
            }

            return resourceKey; // Fallback with the key name
        }

        private static string GetDisplayValue(T value)
        {
            FieldInfo fi = value.GetType().GetField(value.ToString());
            DescriptionAttribute[] attributes = (DescriptionAttribute[])fi.GetCustomAttributes(typeof(DescriptionAttribute), false);

            if (attributes != null && attributes.Length > 0)
                return attributes[0].Description;
            else
                return value.ToString();
        }
    }

    public static class EnumerationDisplayHelper
    {
        public static string GetDescription(this Enum val)
        {
            try
            {
                var attributes = (DescriptionAttribute[])val.GetType().GetField(val.ToString()).GetCustomAttributes(typeof(DescriptionAttribute), false);

                return attributes.Length > 0 ? attributes[0].Description : val.ToString();
            }
            catch (Exception)
            {
                return val.ToString();
            }
        }
    }
}
