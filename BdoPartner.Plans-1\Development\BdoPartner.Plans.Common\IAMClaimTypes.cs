﻿using System;
using System.Collections.Generic;
using System.Text;

namespace BdoPartner.Plans.Common
{
    /// <summary>
    ///  First On Site Identity Server 4 customized claim types.
    ///  Work for customized authorization logic.
    /// </summary>
    public static class IAMClaimTypes
    {
        /// <summary>
        ///  claim "roles".  
        /// </summary>
        public const string Roles = "roles";
        /// <summary>
        ///  claim "permissions".
        /// </summary>
        public const string Permissions = "permissions";

        /// <summary>
        ///  Claim "applications"
        /// </summary>
        public const string Applications = "applications";

        /// <summary>
        ///  user display name. For information purpose. it is not unique.
        ///  Value assigned to User.DisplayName.
        ///  Claim value got from Azure AD user account property "name" (Note: The first "name" claim).
        /// 
        /// </summary>
        public const string DisplayName = "displayname";
           
        /// <summary>
        ///  Reference to Azure AD user's claim called "oid" which contains user profile's "object id".
        ///  Which is unique identify each Azure AD user.
        /// </summary>
        public const string AzureADObjectId = "oid";
    }
}
