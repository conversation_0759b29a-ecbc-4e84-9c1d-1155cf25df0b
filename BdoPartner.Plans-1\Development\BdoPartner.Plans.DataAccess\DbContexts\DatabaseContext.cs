﻿using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;
using BdoPartner.Plans.Model.Entity;
using BdoPartner.Plans.DataAccess.Common;
using BdoPartner.Plans.DataAccess.Common.Audit;

#nullable disable

namespace BdoPartner.Plans.DataAccess
{
    public partial class DatabaseContext : BaseDbContext
    {
        //public BdoPartner.PlansDatabaseContext()
        //{
        //}

        //public BdoPartner.PlansDatabaseContext(DbContextOptions<BdoPartner.PlansDatabaseContext> options)
        //    : base(options)
        //{
        //}

        public virtual DbSet<DataAudit> DataAudits { get; set; }
        public virtual DbSet<Form> Forms { get; set; }
        public virtual DbSet<FormStatus> FormStatuses { get; set; }
        public virtual DbSet<Language> Languages { get; set; }
        public virtual DbSet<Notification> Notifications { get; set; }
        public virtual DbSet<Partner> Partners { get; set; }
        public virtual DbSet<PersistedGrant> PersistedGrants { get; set; }
        public virtual DbSet<Questionnaire> Questionnaires { get; set; }
        public virtual DbSet<QuestionnaireStatus> QuestionnaireStatuses { get; set; }
        public virtual DbSet<SigningCredential> SigningCredentials { get; set; }
        public virtual DbSet<UserAnswer> UserAnswers { get; set; }
        public virtual DbSet<PartnerReviewer> PartnerReviewers { get; set; }
        public virtual DbSet<PartnerReviewerUpload> PartnerReviewerUploads { get; set; }
        public virtual DbSet<PartnerReviewerUploadDetails> PartnerReviewerUploadDetails { get; set; }
        public virtual DbSet<PartnerReferenceDataMeta> PartnerReferenceDataMetas { get; set; }
        public virtual DbSet<PartnerReferenceDataMetaDetails> PartnerReferenceDataMetaDetails { get; set; }
        public virtual DbSet<PartnerReferenceDataUpload> PartnerReferenceDataUploads { get; set; }
        public virtual DbSet<PartnerReferenceDataUploadDetails> PartnerReferenceDataUploadDetails { get; set; }
        public virtual DbSet<PartnerReferenceData> PartnerReferenceData { get; set; }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
#warning To protect potentially sensitive information in your connection string, you should move it out of source code. You can avoid scaffolding the connection string by using the Name= syntax to read it from configuration - see https://go.microsoft.com/fwlink/?linkid=2131148. For more guidance on storing connection strings, see http://go.microsoft.com/fwlink/?LinkId=723263.
                optionsBuilder.UseSqlServer("Server=localhost\\SQLEXPRESS;Database=BdoPartner.Plans.Database;Trusted_Connection=True;");
            }
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.HasAnnotation("Relational:Collation", "SQL_Latin1_General_CP1_CI_AS");
                     
            modelBuilder.Entity<DataAudit>(entity =>
            {
                entity.ToTable("DataAudit", "audit");

                entity.Property(e => e.CreatedBy).HasMaxLength(50);

                entity.Property(e => e.CreatedOn).HasDefaultValueSql("(getutcdate())");

                entity.Property(e => e.FieldName)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.PrimaryKeyField).HasMaxLength(50);

                entity.Property(e => e.PrimaryKeyValue).HasMaxLength(50);

                entity.Property(e => e.TableName)
                    .IsRequired()
                    .HasMaxLength(50);
            });

            modelBuilder.Entity<Language>(entity =>
            {
                entity.ToTable("Language");

                entity.Property(e => e.Id).ValueGeneratedNever();

                entity.Property(e => e.Code)
                    .IsRequired()
                    .HasMaxLength(20);

                entity.Property(e => e.IsActive)
                    .IsRequired()
                    .HasDefaultValueSql("((1))");

                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(50);
            });

          
            modelBuilder.Entity<Notification>(entity =>
            {
                entity.ToTable("Notification");

                entity.Property(e => e.CreatedOn).HasDefaultValueSql("(getutcdate())");

                entity.Property(e => e.Message).IsRequired();
            });

            
            modelBuilder.Entity<PersistedGrant>(entity =>
            {
                entity.HasKey(e => e.Key);

                entity.ToTable("PersistedGrant", "identity");

                entity.Property(e => e.Key).HasMaxLength(100);

                entity.Property(e => e.ClientId)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.Data).IsRequired();

                entity.Property(e => e.Description).HasMaxLength(200);

                entity.Property(e => e.SessionId).HasMaxLength(100);

                entity.Property(e => e.SubjectId).HasMaxLength(100);

                entity.Property(e => e.Type)
                    .IsRequired()
                    .HasMaxLength(50);
            });

                       
            modelBuilder.Entity<SigningCredential>(entity =>
            {
                entity.ToTable("SigningCredential", "identity");

                entity.Property(e => e.Id).ValueGeneratedNever();

                entity.Property(e => e.Value).IsRequired();
            });
                       
            modelBuilder.Entity<Form>(entity =>
            {
                entity.ToTable("Form");

                entity.Property(e => e.Id).HasDefaultValueSql("(newsequentialid())");

                entity.Property(e => e.CreatedByName).HasMaxLength(100);

                entity.Property(e => e.CreatedOn).HasDefaultValueSql("(getutcdate())");

                entity.Property(e => e.ModifiedByName).HasMaxLength(100);

                entity.Property(e => e.IsActive)
                    .IsRequired()
                    .HasDefaultValueSql("((1))");

                entity.Property(e => e.PartnerObjectId).HasMaxLength(100);

                entity.Property(e => e.PartnerName).HasMaxLength(100);

                entity.Property(e => e.PartnerEmail).HasMaxLength(100);

                entity.HasOne(d => d.FormStatus)
                    .WithMany(p => p.Forms)
                    .HasForeignKey(d => d.Status)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Form_Status");

                entity.HasOne(d => d.Questionnaire)
                    .WithMany(p => p.Forms)
                    .HasForeignKey(d => d.QuestionnaireId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Form_Questionnaire");
            });

            modelBuilder.Entity<FormStatus>(entity =>
            {
                entity.ToTable("FormStatus");

                entity.Property(e => e.Id).ValueGeneratedNever();

                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.EnglishDislayName).HasMaxLength(50);

                entity.Property(e => e.FrenchDisplayName).HasMaxLength(50);
            });
                        
            modelBuilder.Entity<Partner>(entity =>
            {
                entity.ToTable("Partner");

                entity.Property(e => e.Id).HasDefaultValueSql("(newsequentialid())");

                entity.Property(e => e.FirstName).HasMaxLength(150);

                entity.Property(e => e.LastName).HasMaxLength(150);

                entity.Property(e => e.DisplayName).HasMaxLength(300);

                entity.Property(e => e.Dob)
                    .HasColumnType("date")
                    .HasColumnName("DOB");

                entity.Property(e => e.Mail).HasMaxLength(200);

                entity.Property(e => e.PartnerType).HasMaxLength(100);

                entity.Property(e => e.Department).HasMaxLength(200);

                entity.Property(e => e.Location).HasMaxLength(200);

                entity.Property(e => e.LocationId).HasMaxLength(200);

                entity.Property(e => e.Wgroup)
                    .HasMaxLength(200)
                    .HasColumnName("WGroup");

                entity.Property(e => e.WgroupId)
                    .HasMaxLength(200)
                    .HasColumnName("WGroupId");

                entity.Property(e => e.ServiceLine).HasMaxLength(200);

                entity.Property(e => e.ServiceLineId).HasMaxLength(200);

                entity.Property(e => e.SubServiceLine).HasMaxLength(200);

                entity.Property(e => e.SubServiceLineId).HasMaxLength(200);

                entity.Property(e => e.CreatedByName).HasMaxLength(100);

                entity.Property(e => e.CreatedOn).HasDefaultValueSql("(getutcdate())");

                entity.Property(e => e.ModifiedByName).HasMaxLength(100);

                entity.Property(e => e.IsActive)
                    .IsRequired()
                    .HasDefaultValueSql("((1))");
            });

            modelBuilder.Entity<Questionnaire>(entity =>
            {
                entity.ToTable("Questionnaire");

                entity.Property(e => e.Id).HasDefaultValueSql("(newsequentialid())");

                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(100);

                entity.Property(e => e.FormSystemVersion).HasDefaultValueSql("((0))");

                entity.Property(e => e.Acknowledgement).HasDefaultValueSql("((0))");

                entity.Property(e => e.AcknowledgementText).HasMaxLength(1500);

                entity.Property(e => e.GeneralComments).HasDefaultValueSql("((0))");

                entity.Property(e => e.Status).HasDefaultValueSql("((0))");

                entity.Property(e => e.CreatedByName).HasMaxLength(100);

                entity.Property(e => e.CreatedOn).HasDefaultValueSql("(getutcdate())");

                entity.Property(e => e.ModifiedByName).HasMaxLength(100);

                entity.Property(e => e.IsActive)
                    .IsRequired()
                    .HasDefaultValueSql("((1))");

                entity.HasOne(d => d.QuestionnaireStatus)
                    .WithMany(p => p.Questionnaires)
                    .HasForeignKey(d => d.Status)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Questionnaire_Status");
            });

            modelBuilder.Entity<QuestionnaireStatus>(entity =>
            {
                entity.ToTable("QuestionnaireStatus");

                entity.Property(e => e.Id).ValueGeneratedNever();

                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.EnglishDislayName).HasMaxLength(50);

                entity.Property(e => e.FrenchDisplayName).HasMaxLength(50);
            });

            modelBuilder.Entity<UserAnswer>(entity =>
            {
                entity.ToTable("UserAnswer");

                entity.Property(e => e.Id).HasDefaultValueSql("(newsequentialid())");

                entity.Property(e => e.CreatedByName).HasMaxLength(100);

                entity.Property(e => e.CreatedOn).HasDefaultValueSql("(getutcdate())");

                entity.Property(e => e.ModifiedByName).HasMaxLength(100);

                entity.Property(e => e.IsActive)
                    .IsRequired()
                    .HasDefaultValueSql("((1))");

                entity.HasOne(d => d.Form)
                    .WithMany(p => p.UserAnswers)
                    .HasForeignKey(d => d.FormId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_UserAnswer_Form");
            });

            modelBuilder.Entity<PartnerReviewer>(entity =>
            {
                entity.ToTable("PartnerReviewer");

                entity.Property(e => e.Id).HasDefaultValueSql("(newsequentialid())");

                entity.Property(e => e.LeadershipRole).HasMaxLength(100);

                entity.Property(e => e.PrimaryReviewerName).HasMaxLength(500);

                entity.Property(e => e.SecondaryReviewerName).HasMaxLength(500);

                entity.Property(e => e.CreatedByName).HasMaxLength(100);

                entity.Property(e => e.CreatedOn).HasDefaultValueSql("(getutcdate())");

                entity.Property(e => e.ModifiedByName).HasMaxLength(100);

                entity.HasIndex(e => new { e.PartnerId, e.Year }, "AK_PartnerReviewer_PartnerYear")
                    .IsUnique();

                entity.HasOne(d => d.Partner)
                    .WithMany()
                    .HasForeignKey(d => d.PartnerId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_PartnerReviewer_Partner");

                entity.HasOne(d => d.PrimaryReviewer)
                    .WithMany()
                    .HasForeignKey(d => d.PrimaryReviewerId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_PartnerReviewer_PrimaryReviewer");

                entity.HasOne(d => d.SecondaryReviewer)
                    .WithMany()
                    .HasForeignKey(d => d.SecondaryReviewerId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_PartnerReviewer_SecondaryReviewer");
            });

            modelBuilder.Entity<PartnerReviewerUpload>(entity =>
            {
                entity.ToTable("PartnerReviewerUpload");

                entity.Property(e => e.Id).ValueGeneratedOnAdd();

                entity.Property(e => e.Years)
                    .IsRequired()
                    .HasMaxLength(100);

                entity.Property(e => e.UploadFileName).HasMaxLength(500);

                entity.Property(e => e.CreatedByName).HasMaxLength(100);

                entity.Property(e => e.CreatedOn).HasDefaultValueSql("(getutcdate())");

                entity.Property(e => e.ModifiedByName).HasMaxLength(100);
            });

            modelBuilder.Entity<PartnerReviewerUploadDetails>(entity =>
            {
                entity.ToTable("PartnerReviewerUploadDetails");

                entity.Property(e => e.Id).ValueGeneratedOnAdd();

                entity.Property(e => e.RowId).IsRequired();

                entity.Property(e => e.EmployeeId).HasMaxLength(50);

                entity.Property(e => e.EmployeeName).HasMaxLength(100);

                entity.Property(e => e.Exempt).HasMaxLength(10);

                entity.Property(e => e.LeadershipRole).HasMaxLength(100);

                entity.Property(e => e.PrimaryReviewerId).HasMaxLength(50);

                entity.Property(e => e.PrimaryReviewerName).HasMaxLength(100);

                entity.Property(e => e.SecondaryReviewerId).HasMaxLength(50);

                entity.Property(e => e.SecondaryReviewerName).HasMaxLength(100);

                entity.Property(e => e.ValidationError).HasMaxLength(500);

                entity.Property(e => e.CreatedByName).HasMaxLength(100);

                entity.Property(e => e.CreatedOn).HasDefaultValueSql("(getutcdate())");

                entity.Property(e => e.ModifiedByName).HasMaxLength(100);

                entity.HasOne(d => d.PartnerReviewerUpload)
                    .WithMany(x=>x.PartnerReviewerUploadDetails)
                    .HasForeignKey(d => d.PartnerReviewerUploadId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_PartnerReviewerUploadDetails_PartnerReviewerUpload");
            });

            modelBuilder.Entity<PartnerReferenceDataMeta>(entity =>
            {
                entity.ToTable("PartnerReferenceDataMeta");

                entity.Property(e => e.Id).HasDefaultValueSql("(newsequentialid())");

                entity.Property(e => e.FileName)
                    .IsRequired()
                    .HasMaxLength(500);

                entity.Property(e => e.Year).IsRequired();

                entity.Property(e => e.Cycle).IsRequired();

                entity.Property(e => e.IsActive).HasDefaultValue(true);

                entity.Property(e => e.CreatedByName).HasMaxLength(100);

                entity.Property(e => e.CreatedOn).HasDefaultValueSql("(getutcdate())");

                entity.Property(e => e.ModifiedByName).HasMaxLength(100);
            });

            modelBuilder.Entity<PartnerReferenceDataMetaDetails>(entity =>
            {
                entity.ToTable("PartnerReferenceDataMetaDetails");

                entity.Property(e => e.Id).HasDefaultValueSql("(newsequentialid())");

                entity.Property(e => e.MetaId).IsRequired();

                entity.Property(e => e.ColumnName)
                    .IsRequired()
                    .HasMaxLength(200);

                entity.Property(e => e.NormalizedColumnName)
                    .IsRequired()
                    .HasMaxLength(200);

                entity.Property(e => e.ColumnDataType).IsRequired();

                entity.Property(e => e.ColumnOrder).IsRequired();

                entity.Property(e => e.CreatedByName).HasMaxLength(100);

                entity.Property(e => e.CreatedOn).HasDefaultValueSql("(getutcdate())");

                entity.Property(e => e.ModifiedByName).HasMaxLength(100);

                entity.HasOne(d => d.Meta)
                    .WithMany(x => x.PartnerReferenceDataMetaDetails)
                    .HasForeignKey(d => d.MetaId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_PartnerReferenceDataMetaDetails_Meta");
            });

            modelBuilder.Entity<PartnerReferenceDataUpload>(entity =>
            {
                entity.ToTable("PartnerReferenceDataUpload");

                entity.Property(e => e.Id).HasDefaultValueSql("(newsequentialid())");

                entity.Property(e => e.UploadFileName)
                    .IsRequired()
                    .HasMaxLength(500);

                entity.Property(e => e.Year).IsRequired();

                entity.Property(e => e.Cycle).IsRequired();

                entity.Property(e => e.MetaId).IsRequired();

                entity.Property(e => e.Status).HasDefaultValue((byte)0);

                entity.Property(e => e.CreatedByName).HasMaxLength(100);

                entity.Property(e => e.CreatedOn).HasDefaultValueSql("(getutcdate())");

                entity.Property(e => e.ModifiedByName).HasMaxLength(100);

                entity.HasOne(d => d.Meta)
                    .WithMany(x => x.PartnerReferenceDataUploads)
                    .HasForeignKey(d => d.MetaId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_PartnerReferenceDataUpload_Meta");
            });

            modelBuilder.Entity<PartnerReferenceDataUploadDetails>(entity =>
            {
                entity.ToTable("PartnerReferenceDataUploadDetails");

                entity.Property(e => e.Id).HasDefaultValueSql("(newsequentialid())");

                entity.Property(e => e.PartnerReferenceDataUploadId).IsRequired();

                entity.Property(e => e.RowId).IsRequired();

                entity.Property(e => e.Data).IsRequired();

                entity.Property(e => e.ValidationError).HasMaxLength(500);

                entity.Property(e => e.CreatedByName).HasMaxLength(100);

                entity.Property(e => e.CreatedOn).HasDefaultValueSql("(getutcdate())");

                entity.Property(e => e.ModifiedByName).HasMaxLength(100);

                entity.HasOne(d => d.PartnerReferenceDataUpload)
                    .WithMany(x => x.PartnerReferenceDataUploadDetails)
                    .HasForeignKey(d => d.PartnerReferenceDataUploadId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_PartnerReferenceDataUploadDetails_Upload");
            });

            modelBuilder.Entity<PartnerReferenceData>(entity =>
            {
                entity.ToTable("PartnerReferenceData");

                entity.Property(e => e.Id).HasDefaultValueSql("(newsequentialid())");

                entity.Property(e => e.PartnerId).IsRequired();

                entity.Property(e => e.Year).IsRequired();

                entity.Property(e => e.Cycle).IsRequired();

                entity.Property(e => e.MetaId).IsRequired();

                entity.Property(e => e.Data).IsRequired();

                entity.Property(e => e.CreatedByName).HasMaxLength(100);

                entity.Property(e => e.CreatedOn).HasDefaultValueSql("(getutcdate())");

                entity.Property(e => e.ModifiedByName).HasMaxLength(100);

                entity.HasIndex(e => new { e.PartnerId, e.Year, e.Cycle, e.MetaId })
                    .IsUnique()
                    .HasDatabaseName("UK_PartnerReferenceData_Partner_Year_Cycle_Meta");

                entity.HasOne(d => d.Partner)
                    .WithMany()
                    .HasForeignKey(d => d.PartnerId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_PartnerReferenceData_Partner");

                entity.HasOne(d => d.Meta)
                    .WithMany(x => x.PartnerReferenceData)
                    .HasForeignKey(d => d.MetaId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_PartnerReferenceData_Meta");
            });

            OnModelCreatingPartial(modelBuilder);
        }

        partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
    }


}
