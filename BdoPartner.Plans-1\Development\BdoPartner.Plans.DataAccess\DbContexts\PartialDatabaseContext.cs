﻿using BdoPartner.Plans.DataAccess.Common;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Text;

namespace BdoPartner.Plans.DataAccess
{
    /// <summary>
    /// Connect to one Sql Server database or MySql database instance.
    /// </summary>
    public partial class DatabaseContext : BaseDbContext
    {
        public DatabaseContext(DbContextOptions<DatabaseContext> options, IConfiguration config)
           : base(options, config)
        {
        }

    }
}
