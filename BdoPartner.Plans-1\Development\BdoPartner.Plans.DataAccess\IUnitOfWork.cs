﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using BdoPartner.Plans.DataAccess.Common.Audit;
using BdoPartner.Plans.DataAccess.Common.Interface;
using BdoPartner.Plans.Model.Entity;
//using BdoPartner.Plans.Model.Entity;


namespace BdoPartner.Plans.DataAccess
{
    /// <summary>
    ///  Note: As for database's tables, each table needs to define one Repository here.
    /// </summary>
    public interface IUnitOfWork : IBaseUnitOfWork
    {
        IBaseRepository<DataAudit> DataAudits { get; }
        IBaseRepository<Form> Forms { get; }
        IBaseRepository<FormStatus> FormStatuses { get; }
        IBaseRepository<Language> Languages { get; }
        IBaseRepository<Notification> Notifications { get; }
        IBaseRepository<Partner> Partners { get; }
        IBaseRepository<Questionnaire> Questionnaires { get; }
        IBaseRepository<QuestionnaireStatus> QuestionnaireStatuses { get; }
        IBaseRepository<UserAnswer> UserAnswers { get; }
        IBaseRepository<PartnerReviewer> PartnerReviewers { get; }
        IBaseRepository<PartnerReviewerUpload> PartnerReviewerUploads { get; }
        IBaseRepository<PartnerReviewerUploadDetails> PartnerReviewerUploadDetailses { get; }
        IBaseRepository<PartnerReferenceDataMeta> PartnerReferenceDataMetas { get; }
        IBaseRepository<PartnerReferenceDataMetaDetails> PartnerReferenceDataMetaDetails { get; }
        IBaseRepository<PartnerReferenceDataUpload> PartnerReferenceDataUploads { get; }
        IBaseRepository<PartnerReferenceDataUploadDetails> PartnerReferenceDataUploadDetails { get; }
        IBaseRepository<PartnerReferenceData> PartnerReferenceData { get; }
    }
}
