﻿using AutoMapper;
using BdoPartner.Plans.DataAccess.Common.Audit;
using BdoPartner.Plans.DataAccess.Common.Interface;
using BdoPartner.Plans.DataAccess.Common.Repository;
using BdoPartner.Plans.Model.Entity;
using Microsoft.Extensions.Logging;
using System;

namespace BdoPartner.Plans.DataAccess
{
    public class UnitOfWork : BaseUnitOfWork, IUnitOfWork
    {
        private ILogger<UnitOfWork> _logger;

        /// <summary>
        ///  Dependency Injection dbContext from outside.
        ///  
        ///  Note: One UnitOfWork is only able to work for one database (dbContext).
        ///  
        ///  If the solution needs to support multiple databases, developer needs to define multiple UnitOfWork entities.
        /// </summary>
        /// <param name="dbContext">Represent one region database.</param>
        /// <param name="mapper">Dependency inject auto mapp.</param>
        /// <param name="logger">Dependency inject file logger.</param>
        public UnitOfWork(DatabaseContext dbContext, I<PERSON><PERSON>per mapper, ILogger<UnitOfWork> logger) : base(dbContext, mapper)
        {
            _logger = logger;
        }
                public IBaseRepository<DataAudit> DataAudits { get { return this.GetRepository<DataAudit>(); } }
        public IBaseRepository<Form> Forms { get { return this.GetRepository<Form>(); } }
        public IBaseRepository<FormStatus> FormStatuses { get { return this.GetRepository<FormStatus>(); } }
        public IBaseRepository<Language> Languages { get { return this.GetRepository<Language>(); } }
        public IBaseRepository<Notification> Notifications { get { return this.GetRepository<Notification>(); } }
        public IBaseRepository<Partner> Partners { get { return this.GetRepository<Partner>(); } }
        public IBaseRepository<Questionnaire> Questionnaires { get { return this.GetRepository<Questionnaire>(); } }
        public IBaseRepository<QuestionnaireStatus> QuestionnaireStatuses { get { return this.GetRepository<QuestionnaireStatus>(); } }
        public IBaseRepository<UserAnswer> UserAnswers { get { return this.GetRepository<UserAnswer>(); } }
        public IBaseRepository<PartnerReviewer> PartnerReviewers { get { return this.GetRepository<PartnerReviewer>(); } }
        public IBaseRepository<PartnerReviewerUpload> PartnerReviewerUploads { get { return this.GetRepository<PartnerReviewerUpload>(); } }
        public IBaseRepository<PartnerReviewerUploadDetails> PartnerReviewerUploadDetailses { get { return this.GetRepository<PartnerReviewerUploadDetails>(); } }
        public IBaseRepository<PartnerReferenceDataMeta> PartnerReferenceDataMetas { get { return this.GetRepository<PartnerReferenceDataMeta>(); } }
        public IBaseRepository<PartnerReferenceDataMetaDetails> PartnerReferenceDataMetaDetails { get { return this.GetRepository<PartnerReferenceDataMetaDetails>(); } }
        public IBaseRepository<PartnerReferenceDataUpload> PartnerReferenceDataUploads { get { return this.GetRepository<PartnerReferenceDataUpload>(); } }
        public IBaseRepository<PartnerReferenceDataUploadDetails> PartnerReferenceDataUploadDetails { get { return this.GetRepository<PartnerReferenceDataUploadDetails>(); } }
        public IBaseRepository<PartnerReferenceData> PartnerReferenceData { get { return this.GetRepository<PartnerReferenceData>(); } }

    }
}
