﻿ Database First Metholodogy to generate DbContext, Entity Models and assocaited UnitOfWork. 

1. Following the readme in the Model.Entity Project to create the entities and move the DBContext back to here

2. Generate content of the IUnitOfWork by followig SQL statment
 
 
Select Case When UPPER(Right(Table_Name,1)) = 'Y'  
Then 'IBaseRepository<' + Table_Name + '> ' + SUBSTRING(Table_Name, 1, LEN(Table_Name)-1) + 'ies { get; }'
Else  'IBaseRepository<' + Table_Name + '> ' + Table_Name + 's { get; }' End
From INFORMATION_SCHEMA.TABLES Order by TABLE_NAME


3. Generate content of the UnitOfWork by followig SQL statment

Select Case When UPPER(Right(Table_Name,1)) = 'Y'  
Then 'public IBaseRepository<' + Table_Name + '> '+ SUBSTRING(Table_Name, 1, LEN(Table_Name)-1)  + 'ies  {  get   {  return this.GetRepository<'+ Table_Name + '>();  }  }'
Else  'public IBaseRepository<' + Table_Name + '> '+ Table_Name + 's  {  get   {  return this.GetRepository<'+ Table_Name + '>();  }  }' End
From INFORMATION_SCHEMA.TABLES Order by TABLE_NAME


Please be reminded that you will need a set of DBContext, IUnitofWork and UnitOfWork per database. So if you need to access 3 databases the 3 sets will be required 
thus please name them according to the database.


* Reverse Engineering Existing Databases in Entity Framework Core 
https://docs.microsoft.com/en-us/ef/core/get-started/aspnetcore/existing-db
Install-Package Microsoft.EntityFrameworkCore.SqlServer
Install-Package Microsoft.EntityFrameworkCore.Tools
Install-Package Microsoft.VisualStudio.Web.CodeGeneration.Design
Tools –> NuGet Package Manager –> Package Manager Console
Scaffold-DbContext "Server=.;Database=BdoPartner.Plans.Database;Trusted_Connection=True;" Microsoft.EntityFrameworkCore.SqlServer -OutputDir Models -force


References: 
https://docs.microsoft.com/en-us/ef/core/get-started/aspnetcore/new-db

