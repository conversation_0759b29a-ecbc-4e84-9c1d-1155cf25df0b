{"title": "{currentYear} Partner Plan Tool", "pages": [{"name": "page1", "visible": false, "title": "Partner Details", "elements": [{"type": "panel", "name": "section1", "visibleIf": "{mode} = 'print'", "title": "Partner Details", "elements": [{"type": "text", "name": "partner<PERSON>ame", "title": "Partner Name", "mapFrom": "Employee Name", "exportColumnName": "Employee Name"}, {"type": "text", "name": "employeeNumber", "title": "Employee Number", "readOnly": true, "mapFrom": "Employee ID", "exportColumnName": "Employee ID"}, {"type": "text", "name": "subServiceLine", "title": "Sub-Service Line", "readOnly": true, "mapFrom": "Sub-Service Line", "exportColumnName": "Sub-Service Line"}, {"type": "text", "name": "location", "visible": false, "title": "Location", "readOnly": true, "mapFrom": "Partner Loction", "exportColumnName": "Location"}, {"type": "text", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "Primary Reviewer", "readOnly": true, "mapFrom": "Primary Reviewer", "exportColumnName": "Primary Reviewer"}, {"type": "text", "name": "secondaryReviewer", "title": "Secondary Reviewer", "readOnly": true, "mapFrom": "Secondary Reviewer", "exportColumnName": "Secondary Reviewer"}]}]}, {"name": "page2", "title": "PossibilityX Capabilities", "elements": [{"type": "html", "name": "section2AInstructions", "html": "Instructions: Please create one commitment for {currentYear} that will support each of the capabilities. Author your commitment based on your circumstances, interests, and what you believe will be impactful. Please use the blue boxes below to articulate these commitments. Example commitments are available for your reference."}, {"type": "panel", "name": "capability1", "requiredIf": "{capability1Commitment} = ''", "title": "Experts in our craft", "state": "expanded", "elements": [{"type": "panel", "name": "capability1_overview_panel", "title": "Definition and Example Commitments", "state": "collapsed", "elements": [{"type": "html", "name": "capability1_overview", "html": "<ul>\n<li>We are experts at our craft and at understanding our work in the context of a client’s business and industry to deliver proactive solutions and value, not just a service. </li>\n<li>Our people are experts in a particular service line/sub-service line and understand how that domain expertise fits in and adds value to the client holistically. They also understand these business issues in the context of a client’s industry (T-shaped people). </li>\n<li>Our culture values and prides itself on an extreme level of collaboration, enabling frictionless combination of services to delivered in a seamless way. Rewards systems reinforce client profiles and activities that enable solutions selling and service delivery. As such, we see more services and revenue per client which will require a more sophisticated and higher caliber client base.</li>\n<li>The pro-serve market tends to deliver in silos leading to a clunky experience full of friction. Our approach will be distinct from this approach.</li>\n</ul>\n"}, {"type": "html", "name": "capability1_example_commitments", "title": "Example Commitments Table", "html": "<table border=\"1\">\n<thead>\n<tr>\n<th style=\"width:50%\">Example Commitments</th>\n<th>Goal of Focus</th>\n<th>Commitment Rating</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td style=\"width:50%\">I will complete a minimum of two industry-specific training programs or certifications relevant to my primary industry. I will share key takeaways with my team through an internal presentation or client-focused knowledge session, showcasing how these insights can proactively address emerging client needs.</td>\n<td>Develop people, partners, and leaders</td>\n<td>High - This commitment is ambitious to achieve and has a strong impact on this PossibilityX strategy</td>\n</tr>\n<tr>\n<td style=\"width:50%\">I will perform a comprehensive business and industry analysis for my top five clients, focusing on understanding their strategic goals, market dynamics, and pain points. I will leverage this analysis to provide three proactive, industry-specific insights or recommendations, strengthening your role as a trusted industry advisor.</td>\n<td>Profitably grow to $1.5b in revenue based on targeting PTK-Driven Sophisticated Clients</td>\n<td>High - This commitment is ambitious to achieve and has a strong impact on this PossibilityX strategy</td>\n</tr>\n<tr>\n<td style=\"width:50%\">Create a customized industry knowledge development plan for each of my direct reports. This includes assigning one industry-specific learning resource (e.g., webinars, articles, courses) per quarter and setting up check-in sessions to discuss how the insights can be applied in client engagements. By year-end, I will ensure that each direct report has presented at least one industry-specific insight or solution to a client or internal team.</td>\n<td>Develop people, partners, and leaders</td>\n<td>Very High - This commitment is very ambitious to achieve and has a very strong impact on this PossibilityX strategy</td>\n</tr>\n<tr>\n<td style=\"width:50%\">Collaborate with at least two colleagues from different service lines to develop a holistic view of industry challenges faced by your clients. Present a combined industry insight report to three clients, offering a multi-disciplinary perspective that showcases our firm’s unique, integrated expertise.</td>\n<td>Profitably grow to $1.5b in revenue based on targeting PTK-Driven Sophisticated Clients</td>\n<td>Very High - This commitment is very ambitious to achieve and has a very strong impact on this PossibilityX strategy</td>\n</tr>\n<tr>\n<td style=\"width:50%\">Establish a functional learning plan for each direct report focused on deepening their technical or service line expertise. Assign two advanced courses or workshops aligned with their specialization, ensuring they complete one by mid-year and the second by year-end. Facilitate monthly knowledge-sharing sessions where direct reports present key learnings, including how these skills can be applied to enhance client solutions and provide greater value within their specific functional area.</td>\n<td>Develop people, partners, and leaders</td>\n<td>Very High - This commitment is very ambitious to achieve and has a very strong impact on this PossibilityX strategy </td>\n</tr>\n</tbody>\n</table>"}]}, {"type": "comment", "name": "capability1Commitment", "width": "65%", "title": "My {currentYear} Commitment", "enableIf": "{role} <> 'ELT' and {state} = 'inprogress'", "isRequired": true, "autoGrow": true}, {"type": "panel", "name": "panel1", "startWithNewLine": false, "elements": [{"type": "dropdown", "name": "capability1Goal", "title": "Goal of Focus", "enableIf": "{role} <> 'ELT' and {state} = 'inprogress'", "isRequired": true, "choices": ["Develop people, partners, and leaders", "Improve people, partner, and client satisfaction", "Profitably grow to $1.5b in revenue based on targeting PTK-Driven Sophisticated Clients", "Increase firm value", "Improve diversity in senior roles – 50% gender and 30% representation from combined diversity pillars", "Tracking to cut greenhouse gases to 50% of 'net zero' by 2030"]}, {"type": "dropdown", "name": "capability1Rating", "title": "Commitment Impact Rating", "enableIf": "{role} <> 'ELT' and {state} = 'inprogress'", "isRequired": true, "choices": ["Moderate - This commitment is relatively simple to achieve and has a moderate impact on this PossibilityX strategy", "High - This commitment is ambitious to achieve and has a strong impact on this PossibilityX strategy", "Very High - This commitment is very ambitious to achieve and has a very strong impact on this PossibilityX strategy"]}]}, {"type": "panel", "name": "capability1MidYearReview", "visibleIf": "{state} != 'inprogress' and {state} != 'submitted'", "title": "Mid-Year Review for Experts in our craft", "description": "- Mid-Year Review: Provide assessments and update comments for this capability.\n", "state": "expanded", "elements": [{"type": "panel", "name": "capability1PartnerMidYearGroup", "title": "Partner Mid-Year Review", "elements": [{"type": "dropdown", "name": "capability1PartnerMidYearAssessment", "title": "Partner Mid-Year Assessment", "enableIf": "{role} != 'Reviewer' and {role} != 'ELT' and ({state} = 'midtermreviewing' or {state} = 'midtermreviewed')", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{capability1PartnerMidYearAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}, {"type": "panel", "name": "capability1ReviewerMidYearGroup", "title": "Reviewer Mid-Year Review", "startWithNewLine": false, "elements": [{"type": "dropdown", "name": "capability1ReviewerMidYearAssessment", "title": "Reviewer Mid-Year Assessment", "enableIf": "{role} != 'Partner' and {role} != 'ELT' and ({state} = 'midtermreviewing' or {state} = 'midtermreviewed')", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{capability1ReviewerMidYearAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}]}, {"type": "panel", "name": "capability1YearEndReview", "visibleIf": "{state} = 'yearendreviewing' or {state} = 'yearendreviewed'", "title": "Year-End Review for Experts in our craft", "description": "- Year-End Review: Provide assessments and update comments for this capability.\n", "state": "expanded", "elements": [{"type": "panel", "name": "capability1PartnerYearEndGroup", "title": "Partner Year-End Review", "elements": [{"type": "dropdown", "name": "capability1PartnerYearEndAssessment", "title": "Partner Year-End Assessment", "enableIf": "{role} != 'Reviewer' and {role} != 'ELT'", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{capability1PartnerYearEndAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}, {"type": "panel", "name": "capability1ReviewerYearEndGroup", "title": "Reviewer Year-End Review", "startWithNewLine": false, "elements": [{"type": "dropdown", "name": "capability1ReviewerYearEndAssessment", "title": "Reviewer Year-End Assessment", "enableIf": "{role} != 'Partner' and {role} != 'ELT'", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{capability1ReviewerYearEndAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}]}]}, {"type": "panel", "name": "capability2", "requiredIf": "{capability2Commitment} = ''", "title": "Deep Client Understanding", "state": "expanded", "elements": [{"type": "panel", "name": "capability2_overview_panel", "title": "Overview and Example Commitments", "state": "collapsed", "elements": [{"type": "html", "name": "capability2_overview", "html": "<ul>\n<li>We go beyond understanding our client’s operations to the heart of their motivations, allowing us to create a distinct client experience. </li>\n<li>We know what clients want in an experience. We listen to clients, design accordingly and deliver consistently. We have a clear definition of each of PTK based on client research. </li>\n<li>We know how to weave between technology and human interaction in a way that has client feel - we are modern and progressive but care about a deep relationship with them. \nDelighting clients through their experience creates trust and credibility such that they will be open to other services we provide. </li>\n<li>We will listen, design based on what we hear and operationalize with a degree of client-centricity that doesn’t exist in the market  today. </li>\n</ul>\n"}, {"type": "html", "name": "capability2_example_commitments", "html": "<table border=1>\n<thead>\n<tr>\n<th style=\"width:50%\">Example Commitments</th>\n<th>Goal of Focus</th>\n<th>Commitment Rating</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td style=\"width:50%\">I will conduct quarterly client feedback sessions with my top five clients to understand their experience preferences, motivations, and evolving needs. I will use this feedback to implement at least two client-driven improvements in service delivery, ensuring alignment with what clients value most in their interactions with us.</td>\n<td>Improve people, partner, and client satisfaction</td>\n<td>Very High - This commitment is very ambitious to achieve and has a very strong impact on this PossibilityX strategy</td>\n</tr>\n<tr>\n<td style=\"width:50%\">I will identify one client service process that can be redesigned based on direct client input. I will work with the relevant internal teams to develop a plan to implement the improvement to remove friction and enhancing the client’s experience. I will measure the impact through follow-up client discussions.</td>\n<td>Improve people, partner, and client satisfaction</td>\n<td>High - This commitment is ambitious to achieve and has a strong impact on this PossibilityX strategy</td>\n</tr>\n<tr>\n<td style=\"width:50%\">I will regularly review the strategic goals and current challenges of my top three clients, identifying potential issues or opportunities before they surface. For each of these clients I will offer at least one unexpected, proactive solution or insight per quarter, even if it’s outside the immediate scope of the engagement. This could include industry benchmarking data, a custom analysis, or a brief, complimentary consultation session to help the client address an emerging need.</td>\n<td>Profitably grow to $1.5b in revenue based on targeting PTK-Driven Sophisticated Clients</td>\n<td>Very High - This commitment is very ambitious to achieve and has a very strong impact on this PossibilityX strategy</td>\n</tr>\n<tr>\n<td style=\"width:50%\">I will create and execute a personalized \"Signature Moment\" for five key clients, going beyond standard service delivery to offer a memorable, value-added experience. This could involve hosting a tailored workshop, providing a unique industry report specific to their business, or surprising the client with a thoughtful gesture that demonstrates a deep understanding of their values and priorities. The goal is to leave clients feeling genuinely cared for and to strengthen the trust in our relationship.</td>\n<td>Improve people, partner, and client satisfaction</td>\n<td>Very High - This commitment is very ambitious to achieve and has a very strong impact on this PossibilityX strategy</td>\n</tr>\n</tbody>\n</table>"}]}, {"type": "comment", "name": "capability2Commitment", "width": "65%", "title": "My {currentYear} Commitment", "enableIf": "{role} <> 'ELT' and {state} = 'inprogress'", "isRequired": true}, {"type": "panel", "name": "panel6", "startWithNewLine": false, "elements": [{"type": "dropdown", "name": "capability2Goal", "startWithNewLine": false, "title": "Goal of Focus", "enableIf": "{role} <> 'ELT' and {state} = 'inprogress'", "isRequired": true, "choices": ["Develop people, partners, and leaders", "Improve people, partner, and client satisfaction", "Profitably grow to $1.5b in revenue based on targeting PTK-Driven Sophisticated Clients", "Increase firm value", "Improve diversity in senior roles – 50% gender and 30% representation from combined diversity pillars", "Tracking to cut greenhouse gases to 50% of 'net zero' by 2030"]}, {"type": "dropdown", "name": "capability2Rating", "title": "Commitment Impact Rating", "enableIf": "{role} <> 'ELT' and {state} = 'inprogress'", "isRequired": true, "choices": ["Moderate - This commitment is relatively simple to achieve and has a moderate impact on this PossibilityX strategy", "High - This commitment is ambitious to achieve and has a strong impact on this PossibilityX strategy", "Very High - This commitment is very ambitious to achieve and has a very strong impact on this PossibilityX strategy"]}]}, {"type": "panel", "name": "capability2MidYearReview", "visibleIf": "{state} != 'inprogress' and {state} != 'submitted'", "title": "Mid-Year Review for Deep Client Understanding", "description": "- Mid-Year Review: Provide assessments and update comments for this capability.\n", "state": "expanded", "elements": [{"type": "panel", "name": "capability2PartnerMidYearGroup", "title": "Partner Mid-Year Review", "elements": [{"type": "dropdown", "name": "capability2PartnerMidYearAssessment", "title": "Partner Mid-Year Assessment", "enableIf": "{role} != 'Reviewer' and {role} != 'ELT' and ({state} = 'midtermreviewing' or {state} = 'midtermreviewed')", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{capability2PartnerMidYearAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}, {"type": "panel", "name": "capability2ReviewerMidYearGroup", "title": "Reviewer Mid-Year Review", "startWithNewLine": false, "elements": [{"type": "dropdown", "name": "capability2ReviewerMidYearAssessment", "title": "Reviewer Mid-Year Assessment", "enableIf": "{role} != 'Partner' and {role} != 'ELT' and ({state} = 'midtermreviewing' or {state} = 'midtermreviewed')", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{capability2ReviewerMidYearAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}]}, {"type": "panel", "name": "capability2YearEndReview", "visibleIf": "{state} = 'yearendreviewing' or {state} = 'yearendreviewed'", "title": "Year-End Review for Deep Client Understanding", "description": "- Year-End Review: Provide assessments and update comments for this capability.\n", "state": "expanded", "elements": [{"type": "panel", "name": "capability2PartnerYearEndGroup", "title": "Partner Year-End Review", "elements": [{"type": "dropdown", "name": "capability2PartnerYearEndAssessment", "title": "Partner Year-End Assessment", "enableIf": "{role} != 'Reviewer' and {role} != 'ELT'", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{capability2PartnerYearEndAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}, {"type": "panel", "name": "capability2ReviewerYearEndGroup", "title": "Reviewer Year-End Review", "startWithNewLine": false, "elements": [{"type": "dropdown", "name": "capability2ReviewerYearEndAssessment", "title": "Reviewer Year-End Assessment", "enableIf": "{role} != 'Partner' and {role} != 'ELT'", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{capability2ReviewerYearEndAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}]}]}, {"type": "panel", "name": "capability3", "requiredIf": "{capability3Commitment} = ''", "title": "Collaboration", "state": "expanded", "elements": [{"type": "panel", "name": "capability3_overview_panel", "title": "Overview and Example Commitments", "state": "collapsed", "elements": [{"type": "html", "name": "capability3_overview", "html": "<ul>\n<li>Collaboration is a critical capability that enhances client outcomes and elevates our distinct approach to service delivery. </li>\n<li>Our team operates with a seamless blend of diverse expertise, ensuring that no client need goes unaddressed due to siloed thinking or fragmented service lines. </li>\n<li>We prioritize cross-functional and cross-industry teamwork that leads to truly integrated solutions.</li>\n<li>Our clients experience the advantage of this collaboration through customized, multi-disciplinary solutions that feel cohesive and fully aligned with their complex needs. </li>\n<li>Our collaborative capability helps us strengthen relationships, uncover new opportunities for growth, and achieve a higher level of client satisfaction and loyalty. </li>\n<li>By embedding collaboration deeply into our processes and culture, we deliver client experiences that are seamless, holistic, and remarkably effective.</li>\n</ul>\n"}, {"type": "html", "name": "capability3_example_commitments", "startWithNewLine": false, "html": "<table border=1>\n<thead>\n<tr>\n<th style=\"width:50%\">Example Commitments</th>\n<th>Goal of Focus</th>\n<th>Commitment Rating</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td style=\"width:50%\">Lead or participate in at least three client projects that involve collaboration across multiple service lines. Coordinate regular joint planning meetings with relevant teams to align on client objectives and develop a cohesive, multi-disciplinary solution. Measure success through client feedback and identify areas for further improving integration.</td>\n<td>Profitably grow to $1.5b in revenue based on targeting PTK-Driven Sophisticated Clients</td>\n<td>Very High - This commitment is very ambitious to achieve and has a very strong impact on this PossibilityX strategy</td>\n</tr>\n<tr>\n<td style=\"width:50%\">For my top three clients, I will establish a dedicated cross-functional team comprising experts from different service lines. I will schedule monthly collaboration sessions focused on sharing insights, identifying emerging client needs, and developing integrated service recommendations that demonstrate a unified approach.</td>\n<td>Improve people, partner, and client satisfaction</td>\n<td>High - This commitment is ambitious to achieve and has a strong impact on this PossibilityX strategy</td>\n</tr>\n<tr>\n<td style=\"width:50%\">I will partner with colleagues from two other service lines to create a comprehensive client strategy plan for one key client. This plan will include input from all relevant functions and outline how we can deliver a seamless, coordinated service experience. I will present the plan to the client, highlighting our unique ability to integrate diverse expertise into a single, effective solution.</td>\n<td>Improve people, partner, and client satisfaction</td>\n<td>Very High - This commitment is very ambitious to achieve and has a very strong impact on this PossibilityX strategy</td>\n</tr>\n<tr>\n<td style=\"width:50%\">I will organize and lead a quarterly knowledge-sharing session within my team, focused on recent client projects where collaboration was key to success. I will invite colleagues from different service lines to present, and capture best practices to be shared firm-wide, helping to strengthen collaborative behaviors across the organization.</td>\n<td>Develop people, partners, and leaders</td>\n<td>High - This commitment is ambitious to achieve and has a strong impact on this PossibilityX strategy</td>\n</tr>\n<tr>\n<td style=\"width:50%\">I will conduct joint client review meetings with at least two other service lines for my top five clients. I will use these sessions to identify service gaps, cross-sell opportunities, and areas where a multi-disciplinary approach could add value. The goal is to introduce at least one new, collaborative service offering per client by year-end, based on insights gained during these reviews.</td>\n<td>Profitably grow to $1.5b in revenue based on targeting PTK-Driven Sophisticated Clients</td>\n<td>Very High - This commitment is very ambitious to achieve and has a very strong impact on this PossibilityX strategy</td>\n</tr>\n</tbody>\n</table>"}]}, {"type": "comment", "name": "capability3Commitment", "width": "65%", "title": "My {currentYear} Commitment", "enableIf": "{role} <> 'ELT' and {state} = 'inprogress'", "isRequired": true}, {"type": "panel", "name": "panel8", "startWithNewLine": false, "elements": [{"type": "dropdown", "name": "capability3Goal", "title": "Goal of Focus", "enableIf": "{role} <> 'ELT' and {state} = 'inprogress'", "isRequired": true, "choices": ["Develop people, partners, and leaders", "Improve people, partner, and client satisfaction", "Profitably grow to $1.5b in revenue based on targeting PTK-Driven Sophisticated Clients", "Increase firm value", "Improve diversity in senior roles – 50% gender and 30% representation from combined diversity pillars", "Tracking to cut greenhouse gases to 50% of 'net zero' by 2030"]}, {"type": "dropdown", "name": "capability3Rating", "title": "Commitment Impact Rating", "enableIf": "{role} <> 'ELT' and {state} = 'inprogress'", "isRequired": true, "choices": ["Moderate - This commitment is relatively simple to achieve and has a moderate impact on this PossibilityX strategy", "High - This commitment is ambitious to achieve and has a strong impact on this PossibilityX strategy", "Very High - This commitment is very ambitious to achieve and has a very strong impact on this PossibilityX strategy"]}]}, {"type": "panel", "name": "capability3MidYearReview", "visibleIf": "{state} != 'inprogress' and {state} != 'submitted'", "title": "Mid-Year Review for Collaboration", "description": "- Mid-Year Review: Provide assessments and update comments for this capability.\n", "state": "expanded", "elements": [{"type": "panel", "name": "capability3PartnerMidYearGroup", "title": "Partner Mid-Year Review", "elements": [{"type": "dropdown", "name": "capability3PartnerMidYearAssessment", "title": "Partner Mid-Year Assessment", "enableIf": "{role} != 'Reviewer' and {role} != 'ELT' and ({state} = 'midtermreviewing' or {state} = 'midtermreviewed')", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{capability3PartnerMidYearAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}, {"type": "panel", "name": "capability3ReviewerMidYearGroup", "title": "Reviewer Mid-Year Review", "startWithNewLine": false, "elements": [{"type": "dropdown", "name": "capability3ReviewerMidYearAssessment", "title": "Reviewer Mid-Year Assessment", "enableIf": "{role} != 'Partner' and {role} != 'ELT' and ({state} = 'midtermreviewing' or {state} = 'midtermreviewed')", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{capability3ReviewerMidYearAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}]}, {"type": "panel", "name": "capability3YearEndReview", "visibleIf": "{state} = 'yearendreviewing' or {state} = 'yearendreviewed'", "title": "Year-End Review for Collaboration", "description": "- Year-End Review: Provide assessments and update comments for this capability.\n", "state": "expanded", "elements": [{"type": "panel", "name": "capability3PartnerYearEndGroup", "title": "Partner Year-End Review", "elements": [{"type": "dropdown", "name": "capability3PartnerYearEndAssessment", "title": "Partner Year-End Assessment", "enableIf": "{role} != 'Reviewer' and {role} != 'ELT'", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{capability3PartnerYearEndAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}, {"type": "panel", "name": "capability3ReviewerYearEndGroup", "title": "Reviewer Year-End Review", "startWithNewLine": false, "elements": [{"type": "dropdown", "name": "capability3ReviewerYearEndAssessment", "title": "Reviewer Year-End Assessment", "enableIf": "{role} != 'Partner' and {role} != 'ELT'", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{capability3ReviewerYearEndAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}]}]}, {"type": "panel", "name": "capability4", "requiredIf": "{capability4Commitment} = ''", "title": "Accelerated career path", "state": "expanded", "elements": [{"type": "panel", "name": "capability4_overview_panel", "title": "Overview and Example Commitments", "state": "collapsed", "elements": [{"type": "html", "name": "capability4_Overview", "html": "<ul>\n<li>We can identify and develop potential in professionals better than comp, allowing us to hire younger and with less experience. This will help us win with students and experienced hires looking for a faster path. </li>\n<li>While experience matters, we will purposefully design career opportunities for people to gain experience faster. We will assess promotions based on both potential and experience. </li>\n<li>This will require us to hire people who are ambitious, growth oriented, learners, and have a solid work ethic. </li>\n<li>Our competitors will be challenged to match the pace at which we are able to offer people career growth given our growth trajectory and a culture that focus on merit and </li>\n</ul>\n"}, {"type": "html", "name": "capability4_Example_Commitments", "html": "<table border=\"1\">\n    <colgroup>\n        <col style=\"width:50%\">\n        <col>\n        <col>\n    </colgroup>\n    <thead>\n        <tr>\n            <th>Example Commitments</th>\n            <th>Goal of Focus</th>\n            <th>Commitment Rating</th>\n        </tr>\n    </thead>\n    <tbody>\n        <tr>\n            <td>I will mentor two junior professionals by providing practical, actionable advice to help them navigate their career paths. I will share real-world scenarios and technical insights to enhance their ability to make meaningful contributions early in their careers while fostering confidence and kindness in their professional development.</td>\n            <td>Develop people, partners, and leaders</td>\n            <td>High - This commitment is ambitious to achieve and has a strong impact on this PossibilityX strategy</td>\n        </tr>\n        <tr>\n            <td>I will design and lead a technical upskilling initiative for high-potential team members, ensuring they gain advanced technical knowledge in a compressed timeframe. I will pair this with practical applications, such as project simulations or client-facing opportunities, to ensure they can immediately apply what they learn.</td>\n            <td>Develop people, partners, and leaders</td>\n            <td>Very High - This commitment is very ambitious to achieve and has a very strong impact on this PossibilityX strategy</td>\n        </tr>\n        <tr>\n            <td>I will actively identify and mentor at least two high-potential women within the organization to support their development toward senior leadership roles. I will work with them to create tailored development plans that address their career goals, provide practical opportunities for visibility and impact, and ensure they are equipped with the technical expertise and leadership skills required for advancement. I will also advocate for inclusive practices within promotion and talent review processes to help improve the representation of women at senior levels.</td>\n            <td>Improve diversity in senior roles – 50% gender and 30% representation from combined diversity pillars</td>\n            <td>Very High - This commitment is very ambitious to achieve and has a very strong impact on this PossibilityX strategy</td>\n</tr>\n        <tr>\n            <td>I will work with each team member to identify one specific, practical milestone that will accelerate their career growth (e.g., leading a client meeting, mastering a technical skill, or completing a certification). I will provide the resources and guidance needed to achieve this milestone within the year, blending practicality with kindness in my approach.</td>\n            <td>Develop people, partners, and leaders</td>\n            <td>Moderate - This commitment is relatively simple to achieve and has a moderate impact on this PossibilityX strategy</td>\n        </tr>\n        <tr>\n            <td>I will partner with recruitment to showcase our accelerated career path during at least three campus or industry hiring events. Share success stories and tangible examples of how employees have advanced quickly within the firm to attract candidates who are ambitious, growth-oriented, and eager to learn.</td>\n            <td>Increase firm value</td>\n            <td>High - This commitment is ambitious to achieve and has a strong impact on this PossibilityX strategy</td>\n        </tr>\n    </tbody>\n</table>"}]}, {"type": "comment", "name": "capability4Commitment", "width": "65%", "title": "My {currentYear} Commitment", "enableIf": "{role} <> 'ELT' and {state} = 'inprogress'", "isRequired": true}, {"type": "panel", "name": "panel10", "startWithNewLine": false, "elements": [{"type": "dropdown", "name": "capability4Goal", "title": "Goal of Focus", "enableIf": "{role} <> 'ELT' and {state} = 'inprogress'", "isRequired": true, "choices": ["Develop people, partners, and leaders", "Improve people, partner, and client satisfaction", "Profitably grow to $1.5b in revenue based on targeting PTK-Driven Sophisticated Clients", "Increase firm value", "Improve diversity in senior roles – 50% gender and 30% representation from combined diversity pillars", "Tracking to cut greenhouse gases to 50% of 'net zero' by 2030"]}, {"type": "dropdown", "name": "capability4Rating", "title": "Commitment Impact Rating", "enableIf": "{role} <> 'ELT' and {state} = 'inprogress'", "isRequired": true, "choices": ["Moderate - This commitment is relatively simple to achieve and has a moderate impact on this PossibilityX strategy", "High - This commitment is ambitious to achieve and has a strong impact on this PossibilityX strategy", "Very High - This commitment is very ambitious to achieve and has a very strong impact on this PossibilityX strategy"]}]}, {"type": "panel", "name": "capability4MidYearReview", "visibleIf": "{state} != 'inprogress' and {state} != 'submitted'", "title": "Mid-Year Review for Accelerated career path", "description": "- Mid-Year Review: Provide assessments and update comments for this capability.\n", "state": "expanded", "elements": [{"type": "panel", "name": "capability4PartnerMidYearGroup", "title": "Partner Mid-Year Review", "elements": [{"type": "dropdown", "name": "capability4PartnerMidYearAssessment", "title": "Partner Mid-Year Assessment", "enableIf": "{role} != 'Reviewer' and {role} != 'ELT' and ({state} = 'midtermreviewing' or {state} = 'midtermreviewed')", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{capability4PartnerMidYearAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}, {"type": "panel", "name": "capability4ReviewerMidYearGroup", "title": "Reviewer Mid-Year Review", "startWithNewLine": false, "elements": [{"type": "dropdown", "name": "capability4ReviewerMidYearAssessment", "title": "Reviewer Mid-Year Assessment", "enableIf": "{role} != 'Partner' {role} != 'ELT' and ({state} = 'midtermreviewing' or {state} = 'midtermreviewed')", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{capability4ReviewerMidYearAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}]}, {"type": "panel", "name": "capability4YearEndReview", "visibleIf": "{state} = 'yearendreviewing' or {state} = 'yearendreviewed'", "title": "Year-End Review for Accelerated career path", "description": "- Year-End Review: Provide assessments and update comments for this capability.\n", "state": "expanded", "elements": [{"type": "panel", "name": "capability4PartnerYearEndGroup", "title": "Partner Year-End Review", "elements": [{"type": "dropdown", "name": "capability4PartnerYearEndAssessment", "title": "Partner Year-End Assessment", "enableIf": "{role} != 'Reviewer' and {role} != 'ELT'", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{capability4PartnerYearEndAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}, {"type": "panel", "name": "capability4ReviewerYearEndGroup", "title": "Reviewer Year-End Review", "startWithNewLine": false, "elements": [{"type": "dropdown", "name": "capability4ReviewerYearEndAssessment", "title": "Reviewer Year-End Assessment", "enableIf": "{role} != 'Partner' and {role} != 'ELT'", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{capability4ReviewerYearEndAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}]}]}, {"type": "panel", "name": "capability5", "requiredIf": "{capability5Commitment} = ''", "title": "AI Integration", "state": "expanded", "elements": [{"type": "panel", "name": "capability5_overview_panel", "title": "Overview and Example Commitments", "state": "collapsed", "elements": [{"type": "html", "name": "capability5_overview", "html": "<ul>\n<li>We keep pace with our tech partners. As the likes of Microsoft, GCP, and others advance AI, we implement the relevant AI advancements as a firm and for our clients at a pace that is ahead of the competition. </li>\n<li>Internal and client service teams are intimately connected. We benefit from seeing material advancements in AI based on substantive - work our client-facing teams are delivering for clients and implement to our benefit. Our internal team deploys AI in a way that can create use cases for our external team. Our internal team leverages the skill and scale of our external team. </li>\n<li>We can build our AIQ to enable deployment, usage, and ROI. We will have a people-base ready to adopt and use AI in a productive way. </li>\n<li>Our tech feels human. We will not lose sight of the fact that our distinctness is based on a care that can be lost with tech. </li>\n</ul>\n"}, {"type": "html", "name": "capability5_example_commitments", "html": "<table border=\"1\">\n    <colgroup>\n        <col style=\"width:50%\">\n        <col>\n        <col>\n    </colgroup>\n    <thead>\n        <tr>\n            <th>Example Commitments</th>\n            <th>Goal of Focus</th>\n            <th>Commitment Rating</th>\n        </tr>\n    </thead>\n    <tbody>\n        <tr>\n            <td>I will spend at least 10 hours each quarter learning and experimenting with AI tools relevant to my role. This includes understanding their features, applications, and limitations of the tools I can gain access to (MS Copilot, ChatGPT Ent, BDO <PERSON>, INSIGHTSai) so I can confidently incorporate them into my work and model their use for my team.</td>\n            <td>Develop people, partners, and leaders</td>\n            <td>Moderate - This commitment is relatively simple to achieve and has a moderate impact on this PossibilityX strategy</td>\n        </tr>\n        <tr>\n            <td>I will identify and nominate at least one team member for the FutureCraft AI program, actively mentoring them throughout the process and leverage them with evidence of GPT builds for their function or the team. I will ensure they have the resources and support to apply their learnings and share insights with the broader team to enhance our collective AI capabilities.</td>\n            <td>Develop people, partners, and leaders</td>\n            <td>High - This commitment is ambitious to achieve and has a strong impact on this PossibilityX strategy</td>\n        </tr>\n        <tr>\n            <td>I will track the impact of the use of AI tools used by my team to measure quality improvement, productivity gains, improved insights or enhanced client outcomes / experieince.  I will review and share these insights quarterly to identify further opportunities for leveraging AI.</td>\n            <td>Increase firm value</td>\n            <td>Very High - This commitment is very ambitious to achieve and has a very strong impact on this PossibilityX strategy</td>\n        </tr>\n        <tr>\n            <td>I will collaborate with both internal and external client-facing teams to create at least two practical AI use cases that demonstrate how AI tools can improve productivity, quality, decision-making, or client outcomes. I will share these use cases across the firm through edison365 to encourage widespread adoption and integration.</td>\n            <td>Increase firm value</td>\n            <td>Very High - This commitment is very ambitious to achieve and has a very strong impact on this PossibilityX strategy</td>\n        </tr>\n        <tr>\n            <td>I will identify one specific area of the client experience where AI can provide a significant improvement, such as streamlining communication, enhancing personalization, or improving response times. I will collaborate with internal and external teams to bring the necessary resources, including AI tools and expertise, to implement a solution. By year-end, I will measure and report on the impact of this improvement, focusing on how it has enhanced client satisfaction and strengthened our relationship.</td>\n            <td>Improve people, partner, and client satisfaction</td>\n            <td>Very High - This commitment is very ambitious to achieve and has a very strong impact on this PossibilityX strategy</td>\n        </tr>\n    </tbody>\n</table>"}]}, {"type": "comment", "name": "capability5Commitment", "width": "65%", "title": "My {currentYear} Commitment", "enableIf": "{role} <> 'ELT' and {state} = 'inprogress'", "isRequired": true}, {"type": "panel", "name": "panel2", "startWithNewLine": false, "elements": [{"type": "dropdown", "name": "capability5Goal", "startWithNewLine": false, "title": "Goal of Focus", "enableIf": "{role} <> 'ELT' and {state} = 'inprogress'", "isRequired": true, "choices": ["Develop people, partners, and leaders", "Improve people, partner, and client satisfaction", "Profitably grow to $1.5b in revenue based on targeting PTK-Driven Sophisticated Clients", "Increase firm value", "Improve diversity in senior roles – 50% gender and 30% representation from combined diversity pillars", "Tracking to cut greenhouse gases to 50% of 'net zero' by 2030"]}, {"type": "dropdown", "name": "capability5Rating", "title": "Commitment Impact Rating", "enableIf": "{role} <> 'ELT' and {state} = 'inprogress'", "isRequired": true, "choices": ["Moderate - This commitment is relatively simple to achieve and has a moderate impact on this PossibilityX strategy", "High - This commitment is ambitious to achieve and has a strong impact on this PossibilityX strategy", "Very High - This commitment is very ambitious to achieve and has a very strong impact on this PossibilityX strategy"]}]}, {"type": "panel", "name": "capability5MidYearReview", "visibleIf": "{state} != 'inprogress' and {state} != 'submitted'", "title": "Mid-Year Review for AI Integration", "description": "- Mid-Year Review: Provide assessments and update comments for this capability.\n", "state": "expanded", "elements": [{"type": "panel", "name": "capability5PartnerMidYearGroup", "title": "Partner Mid-Year Review", "elements": [{"type": "dropdown", "name": "capability5PartnerMidYearAssessment", "title": "Partner Mid-Year Assessment", "enableIf": "{role} != 'Reviewer' and {role} != 'ELT' and ({state} = 'midtermreviewing' or {state} = 'midtermreviewed')", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{capability5PartnerMidYearAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}, {"type": "panel", "name": "capability5ReviewerMidYearGroup", "title": "Reviewer Mid-Year Review", "startWithNewLine": false, "elements": [{"type": "dropdown", "name": "capability5ReviewerMidYearAssessment", "title": "Reviewer Mid-Year Assessment", "enableIf": "{role} != 'Partner' and {role} != 'ELT' and ({state} = 'midtermreviewing' or {state} = 'midtermreviewed')", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{capability5ReviewerMidYearAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}]}, {"type": "panel", "name": "capability5YearEndReview", "visibleIf": "{state} = 'yearendreviewing' or {state} = 'yearendreviewed'", "title": "Year-End Review for AI Integration", "description": "- Year-End Review: Provide assessments and update comments for this capability.\n", "state": "expanded", "elements": [{"type": "panel", "name": "capability5PartnerYearEndGroup", "title": "Partner Year-End Review", "elements": [{"type": "dropdown", "name": "capability5PartnerYearEndAssessment", "title": "Partner Year-End Assessment", "enableIf": "{role} != 'Reviewer' and {role} != 'ELT'", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{capability5PartnerYearEndAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}, {"type": "panel", "name": "capability5ReviewerYearEndGroup", "title": "Reviewer Year-End Review", "startWithNewLine": false, "elements": [{"type": "dropdown", "name": "capability5ReviewerYearEndAssessment", "title": "Reviewer Year-End Assessment", "enableIf": "{role} != 'Partner' and {role} != 'ELT'", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{capability5ReviewerYearEndAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}]}]}]}, {"name": "page31", "title": "{currentYear} Personal KPI Targets", "elements": [{"type": "panel", "name": "kpi7", "title": "Sale", "description": "- Sales - Note that these metrics come from OneSource inputs. Click on the link to the right to review your current data and current pipeline. [Link to PowerBI Reports including OneSource reporting](https://analytics.app.bdo.ca/)\n", "state": "collapsed", "elements": [{"type": "panel", "name": "kpi1", "requiredIf": "{kpi1TargetcurrentYear} = '' or {kpi1PartnerComments} = ''", "title": "New Sales - Originator", "description": "- Sum of 'won' opportunities in CRM where Partner is listed as in the 'originator' field\n", "state": "expanded", "elements": [{"type": "panel", "name": "kpi1TargetPanel", "width": "65%", "elements": [{"type": "comment", "name": "kpi1TargetGuidance", "title": "Target Guidance", "defaultValue": "Based on your service line growth targets and historic metrics, a recommended target for New Sales - Originator in 2025 is $20,000,000", "readOnly": true, "mapFrom": "Est Rev Won - Originator G<PERSON><PERSON>"}, {"type": "comment", "name": "kpi1PartnerComments", "title": "Partner Comments", "enableIf": "{role} <> 'ELT' and {state} = 'inprogress'", "isRequired": true}]}, {"type": "panel", "name": "panel4", "startWithNewLine": false, "elements": [{"type": "text", "name": "kpi1YtdPrevious", "visibleIf": "{state} = 'inprogress' or {state} = 'submitted'", "startWithNewLine": false, "title": "{previousYear} YTD", "readOnly": true, "mapFrom": "Est Rev Won - Originator", "exportColumnName": "Est Rev Won - Originator"}, {"type": "text", "name": "kpi1TargetcurrentYear", "title": "{currentYear} Target", "enableIf": "{role} <> 'ELT' and {state} = 'inprogress'", "isRequired": true, "exportColumnName": "<PERSON> Won - Originator", "min": 0, "max": 99999999, "maskType": "currency", "maskSettings": {"prefix": "$"}}]}, {"type": "panel", "name": "kpi1MidYearReview", "visibleIf": "{state} != 'inprogress' and {state} != 'submitted'", "title": "Mid-Year Review for New Sales - Originator", "description": "- Mid-Year Review: Provide assessments and update comments for this KPI.\n", "state": "expanded", "elements": [{"type": "text", "name": "kpi1MidYearYtd", "title": "Mid-Year YTD", "defaultValue": "#N/A", "readOnly": true}, {"type": "panel", "name": "kpi1PartnerMidYearGroup", "title": "Partner Mid-Year Review", "elements": [{"type": "dropdown", "name": "kpi1PartnerMidYearAssessment", "title": "Partner Mid-Year Assessment", "enableIf": "{role} != 'Reviewer' and {role} != 'ELT' and ({state} = 'midtermreviewing' or {state} = 'midtermreviewed')", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{kpi1PartnerMidYearAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}, {"type": "panel", "name": "kpi1ReviewerMidYearGroup", "title": "Reviewer Mid-Year Review", "startWithNewLine": false, "elements": [{"type": "dropdown", "name": "kpi1ReviewerMidYearAssessment", "title": "Reviewer Mid-Year Assessment", "enableIf": "{role} != 'Partner' and {role} != 'ELT' and ({state} = 'midtermreviewing' or {state} = 'midtermreviewed')", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{kpi1ReviewerMidYearAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}]}, {"type": "panel", "name": "kpi1YearEndReview", "visibleIf": "{state} = 'yearendreviewing' or {state} = 'yearendreviewed'", "title": "Year-End Review for New Sales - Originator", "description": "- Year-End Review: Provide assessments and update comments for this KPI.\n", "state": "expanded", "elements": [{"type": "text", "name": "kpi1YearEndYtd", "title": "Year-End YTD", "defaultValue": "#N/A", "readOnly": true}, {"type": "panel", "name": "kpi1PartnerYearEndGroup", "title": "Partner Year-End Review", "elements": [{"type": "dropdown", "name": "kpi1PartnerYearEndAssessment", "title": "Partner Year-End Assessment", "enableIf": "{role} != 'Reviewer' and {role} != 'ELT'", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{kpi1PartnerYearEndAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}, {"type": "panel", "name": "kpi1ReviewerYearEndGroup", "title": "Reviewer Year-End Review", "startWithNewLine": false, "elements": [{"type": "dropdown", "name": "kpi1ReviewerYearEndAssessment", "title": "Reviewer Year-End Assessment", "enableIf": "{role} != 'Partner' and {role} != 'ELT'", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{kpi1ReviewerYearEndAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}]}]}, {"type": "panel", "name": "kpi2", "requiredIf": "{kpi2TargetcurrentYear} = '' or {kpi2PartnerComments} = ''", "title": "New Sales - Lead", "description": "- Sum of 'won' opportunities in CRM where Partner name is listed in the 'team' field that are non-recurring\n", "state": "expanded", "elements": [{"type": "panel", "name": "panel122", "width": "65%", "elements": [{"type": "comment", "name": "kpi2TargetGuidance", "title": "Target Guidance", "defaultValue": "Based on your service line growth targets and historic metrics, a recommended target for New Sales - Lead in 2025 is $15,000,000", "readOnly": true, "mapFrom": "Est Rev Won - Partner Lead Guidance"}, {"type": "comment", "name": "kpi2PartnerComments", "title": "Partner Comments", "enableIf": "{role} <> 'ELT' and {state} = 'inprogress'", "isRequired": true}]}, {"type": "panel", "name": "panel121", "startWithNewLine": false, "elements": [{"type": "text", "name": "kpi2YtdPrevious", "visibleIf": "{state} = 'inprogress' or {state} = 'submitted'", "title": "{previousYear} YTD", "defaultValue": "#N/A", "readOnly": true, "mapFrom": "Est <PERSON> Won - Partner Lead", "exportColumnName": "Est <PERSON> Won - Partner Lead"}, {"type": "text", "name": "kpi2TargetcurrentYear", "title": "{currentYear} Target", "enableIf": "{role} <> 'ELT' and {state} = 'inprogress'", "isRequired": true, "exportColumnName": "<PERSON> Won - Partner Lead", "min": 0, "max": 99999999, "maskType": "currency", "maskSettings": {"prefix": "$"}}]}, {"type": "panel", "name": "kpi2MidYearReview", "visibleIf": "{state} != 'inprogress' and {state} != 'submitted'", "title": "Mid-Year Review for New Sales - Lead", "description": "- Mid-Year Review: Provide assessments and update comments for this KPI.\n", "state": "expanded", "elements": [{"type": "text", "name": "kpi2MidYearYtd", "title": "Mid-Year YTD", "defaultValue": "#N/A", "readOnly": true}, {"type": "panel", "name": "kpi2PartnerMidYearGroup", "title": "Partner Mid-Year Review", "elements": [{"type": "dropdown", "name": "kpi2PartnerMidYearAssessment", "title": "Partner Mid-Year Assessment", "enableIf": "{role} != 'Reviewer' and {role} != 'ELT' and ({state} = 'midtermreviewing' or {state} = 'midtermreviewed')", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{kpi2PartnerMidYearAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}, {"type": "panel", "name": "kpi2ReviewerMidYearGroup", "title": "Reviewer Mid-Year Review", "startWithNewLine": false, "elements": [{"type": "dropdown", "name": "kpi2ReviewerMidYearAssessment", "title": "Reviewer Mid-Year Assessment", "enableIf": "{role} != 'Partner' and {role} != 'ELT' and ({state} = 'midtermreviewing' or {state} = 'midtermreviewed')", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{kpi2ReviewerMidYearAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}]}, {"type": "panel", "name": "kpi2YearEndReview", "visibleIf": "{state} = 'yearendreviewing' or {state} = 'yearendreviewed'", "title": "Year-End Review for New Sales - Lead", "description": "- Year-End Review: Provide assessments and updatecomments for this KPI.\n", "state": "expanded", "elements": [{"type": "text", "name": "kpi2YearEndYtd", "title": "Year-End YTD", "defaultValue": "#N/A", "readOnly": true}, {"type": "panel", "name": "kpi2PartnerYearEndGroup", "title": "Partner Year-End Review", "elements": [{"type": "dropdown", "name": "kpi2PartnerYearEndAssessment", "title": "Partner Year-End Assessment", "enableIf": "{role} != 'Reviewer' and {role} != 'ELT'", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{kpi2PartnerYearEndAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}, {"type": "panel", "name": "kpi2ReviewerYearEndGroup", "title": "Reviewer Year-End Review", "startWithNewLine": false, "elements": [{"type": "dropdown", "name": "kpi2ReviewerYearEndAssessment", "title": "Reviewer Year-End Assessment", "enableIf": "{role} != 'Partner' and {role} != 'ELT'", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{kpi2ReviewerYearEndAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}]}]}, {"type": "panel", "name": "kpi3", "requiredIf": "{kpi3TargetcurrentYear} = '' or {kpi3PartnerComments} = ''", "title": "New Sales - Team", "description": "- Sum of 'won' opportunities in CRM where Partner is listed as in the 'participator' field\n", "state": "expanded", "elements": [{"type": "panel", "name": "kpi3TargetPanel", "width": "65%", "elements": [{"type": "comment", "name": "kpi3TargetGuidance", "title": "Target Guidance", "defaultValue": "Based on your service line growth targets and historic metrics, a recommended target for New Sales - Team in 2025 is $27,000,000", "readOnly": true, "mapFrom": "Est Rev Won - Team Guidance"}, {"type": "comment", "name": "kpi3PartnerComments", "title": "Partner Comments", "enableIf": "{role} <> 'ELT' and {state} = 'inprogress'", "isRequired": true}]}, {"type": "panel", "name": "kpi_targetpanel", "startWithNewLine": false, "elements": [{"type": "text", "name": "kpi3YtdPrevious", "visibleIf": "{state} = 'inprogress' or {state} = 'submitted'", "title": "{previousYear} YTD", "defaultValue": "#N/A", "readOnly": true, "mapFrom": "Est Rev Won - Team", "exportColumnName": "Est Rev Won - Team"}, {"type": "text", "name": "kpi3TargetcurrentYear", "title": "{currentYear} Target", "enableIf": "{role} <> 'ELT' and {state} = 'inprogress'", "isRequired": true, "exportColumnName": "Rev Won - Team", "min": 0, "max": 99999999, "maskType": "currency", "maskSettings": {"prefix": "$"}}]}, {"type": "panel", "name": "kpi3MidYearReview", "visibleIf": "{state} != 'inprogress' and {state} != 'submitted'", "title": "Mid-Year Review for New Sales - Participator", "description": "- Mid-Year Review: Provide assessments and update comments for this KPI.\n", "state": "expanded", "elements": [{"type": "text", "name": "kpi3MidYearYtd", "title": "Mid-Year YTD", "defaultValue": "#N/A", "readOnly": true}, {"type": "panel", "name": "kpi3PartnerMidYearGroup", "title": "Partner Mid-Year Review", "elements": [{"type": "dropdown", "name": "kpi3PartnerMidYearAssessment", "title": "Partner Mid-Year Assessment", "enableIf": "{role} != 'Reviewer' and {role} != 'ELT' and ({state} = 'midtermreviewing' or {state} = 'midtermreviewed')", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{kpi3PartnerMidYearAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}, {"type": "panel", "name": "kpi3ReviewerMidYearGroup", "title": "Reviewer Mid-Year Review", "startWithNewLine": false, "elements": [{"type": "dropdown", "name": "kpi3ReviewerMidYearAssessment", "title": "Reviewer Mid-Year Assessment", "enableIf": "{role} != 'ELT' and {role} != 'Partner' and ({state} = 'midtermreviewing' or {state} = 'midtermreviewed')", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{kpi3ReviewerMidYearAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}]}, {"type": "panel", "name": "kpi3YearEndReview", "visibleIf": "{state} = 'yearendreviewing' or {state} = 'yearendreviewed'", "title": "Year-End Review for New Sales - Participator", "description": "- Year-End Review: Provide assessments and update comments for this KPI.\n", "state": "expanded", "elements": [{"type": "text", "name": "kpi3YearEndYtd", "title": "Year-End YTD", "defaultValue": "#N/A", "readOnly": true}, {"type": "panel", "name": "kpi3PartnerYearEndGroup", "title": "Partner Year-End Review", "elements": [{"type": "dropdown", "name": "kpi3PartnerYearEndAssessment", "title": "Partner Year-End Assessment", "enableIf": "{role} != 'Reviewer' and {role} != 'ELT'", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{kpi3PartnerYearEndAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}, {"type": "panel", "name": "kpi3ReviewerYearEndGroup", "title": "Reviewer Year-End Review", "startWithNewLine": false, "elements": [{"type": "dropdown", "name": "kpi3ReviewerYearEndAssessment", "title": "Reviewer Year-End Assessment", "enableIf": "{role} != 'Partner' and {role} != 'ELT'", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{kpi3ReviewerYearEndAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}]}]}, {"type": "panel", "name": "kpi4", "requiredIf": "{kpi4TargetcurrentYear} = '' or {kpi4PartnerComments} = ''", "title": "New Sales - Total", "description": "- Sum of Originator, Lead, and Team New Sales\n", "state": "expanded", "elements": [{"type": "panel", "name": "kpi4TargetPanel", "width": "65%", "elements": [{"type": "comment", "name": "kpi4TargetGuidance", "title": "Target Guidance", "defaultValue": "Based on your service line growth targets and historic metrics, a recommended target for New Sales - Total in 2025 is $62,400,000", "readOnly": true, "mapFrom": "New Sales Guidance"}, {"type": "comment", "name": "kpi4PartnerComments", "title": "Partner Comments", "enableIf": "{role} <> 'ELT' and {state} = 'inprogress'", "isRequired": true}]}, {"type": "panel", "name": "kpi4TargetPanel2", "startWithNewLine": false, "elements": [{"type": "text", "name": "kpi4YtdPrevious", "visibleIf": "{state} = 'inprogress' or {state} = 'submitted'", "title": "{previousYear} YTD", "readOnly": true, "mapFrom": "New Sales", "exportColumnName": "Est New Sales"}, {"type": "text", "name": "kpi4TargetcurrentYear", "title": "{currentYear} Target", "enableIf": "{role} <> 'ELT' and {state} = 'inprogress'", "defaultValueExpression": "{kpi3TargetcurrentYear}+{kpi2TargetcurrentYear}+{kpi1TargetcurrentYear}", "isRequired": true, "exportColumnName": "New Sales", "min": 0, "max": 99999999, "maskType": "currency", "maskSettings": {"prefix": "$"}}]}, {"type": "panel", "name": "kpi4MidYearReview", "visibleIf": "{state} != 'inprogress' and {state} != 'submitted'", "title": "Mid-Year Review for New Sales - Originator and Lead", "description": "- Mid-Year Review: Provide assessments and update comments for this KPI.\n", "state": "expanded", "elements": [{"type": "text", "name": "kpi4MidYearYtd", "title": "Mid-Year YTD", "defaultValueExpression": "{kpi3MidYearYtd}+{kpi2MidYearYtd}+{kpi1MidYearYtd}", "readOnly": true}, {"type": "panel", "name": "kpi4PartnerMidYearGroup", "title": "Partner Mid-Year Review", "elements": [{"type": "dropdown", "name": "kpi4PartnerMidYearAssessment", "title": "Partner Mid-Year Assessment", "enableIf": "{role} != 'Reviewer' and {role} != 'ELT' and ({state} = 'midtermreviewing' or {state} = 'midtermreviewed')", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{kpi4PartnerMidYearAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}, {"type": "panel", "name": "kpi4ReviewerMidYearGroup", "title": "Reviewer Mid-Year Review", "startWithNewLine": false, "elements": [{"type": "dropdown", "name": "kpi4ReviewerMidYearAssessment", "title": "Reviewer Mid-Year Assessment", "enableIf": "{role} != 'ELT' and {role} != 'Partner' and ({state} = 'midtermreviewing' or {state} = 'midtermreviewed')", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{kpi4ReviewerMidYearAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}]}, {"type": "panel", "name": "kpi4YearEndReview", "visibleIf": "{state} = 'yearendreviewing' or {state} = 'yearendreviewed'", "title": "Year-End Review for New Sales - Originator and Lead", "description": "- Year-End Review: Provide assessments and update comments for this KPI.\n", "state": "expanded", "elements": [{"type": "text", "name": "kpi4YearEndYtd", "title": "Year-End YTD", "defaultValueExpression": "{kpi3YearEndYtd}+{kpi2YearEndYtd}+{kpi1YearEndYtd}", "readOnly": true}, {"type": "panel", "name": "kpi4PartnerYearEndGroup", "title": "Partner Year-End Review", "elements": [{"type": "dropdown", "name": "kpi4PartnerYearEndAssessment", "title": "Partner Year-End Assessment", "enableIf": "{role} != 'Reviewer' and {role} != 'ELT'", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{kpi4PartnerYearEndAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}, {"type": "panel", "name": "kpi4ReviewerYearEndGroup", "title": "Reviewer Year-End Review", "startWithNewLine": false, "elements": [{"type": "dropdown", "name": "kpi4ReviewerYearEndAssessment", "title": "Reviewer Year-End Assessment", "enableIf": "{role} != 'Partner' and {role} != 'ELT'", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{kpi4ReviewerYearEndAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}]}]}, {"type": "panel", "name": "kpi5", "requiredIf": "{kpi5TargetcurrentYear} = '' or {kpi5PartnerComments} = ''", "title": "Recurring Sales", "description": "- Sales from ongoing client contracts or repeated business\n", "state": "expanded", "width": "65%", "elements": [{"type": "panel", "name": "kpi5TargetPanel", "width": "65%", "elements": [{"type": "comment", "name": "kpi5TargetGuidance", "title": "Target Guidance", "defaultValue": "Based on your service line growth targets and historic metrics, a recommended target for Recurring Sales in 2025 is $600,000", "readOnly": true, "mapFrom": "Recurring Sales Guidance"}, {"type": "comment", "name": "kpi5PartnerComments", "title": "Partner Comments", "enableIf": "{role} <> 'ELT' and {state} = 'inprogress'", "isRequired": true}]}, {"type": "panel", "name": "kpi5TargetPanel2", "startWithNewLine": false, "elements": [{"type": "text", "name": "kpi5YtdPrevious", "visibleIf": "{state} = 'inprogress' or {state} = 'submitted'", "title": "{previousYear} YTD", "defaultValue": "#N/A", "readOnly": true, "mapFrom": "Recurring Sales", "exportColumnName": "Est Recurring Sales"}, {"type": "text", "name": "kpi5TargetcurrentYear", "title": "{currentYear} Target", "enableIf": "{role} <> 'ELT' and {state} = 'inprogress'", "isRequired": true, "exportColumnName": "Recurring Sales", "min": 0, "max": 99999999, "maskType": "currency", "maskSettings": {"prefix": "$"}}]}, {"type": "panel", "name": "kpi5MidYearReview", "visibleIf": "{state} != 'inprogress' and {state} != 'submitted'", "title": "Mid-Year Review for Recurring Sales", "description": "- Mid-Year Review: Provide assessments and update comments for this KPI.\n", "state": "expanded", "elements": [{"type": "text", "name": "kpi5MidYearYtd", "title": "Mid-Year YTD", "defaultValue": "#N/A", "readOnly": true}, {"type": "panel", "name": "kpi5PartnerMidYearGroup", "title": "Partner Mid-Year Review", "elements": [{"type": "dropdown", "name": "kpi5PartnerMidYearAssessment", "title": "Partner Mid-Year Assessment", "enableIf": "{role} != 'Reviewer' and {role} != 'ELT' and ({state} = 'midtermreviewing' or {state} = 'midtermreviewed')", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{kpi5PartnerMidYearAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}, {"type": "panel", "name": "kpi5ReviewerMidYearGroup", "title": "Reviewer Mid-Year Review", "startWithNewLine": false, "elements": [{"type": "dropdown", "name": "kpi5ReviewerMidYearAssessment", "title": "Reviewer Mid-Year Assessment", "enableIf": "{role} != 'ELT' and {role} != 'Partner' and ({state} = 'midtermreviewing' or {state} = 'midtermreviewed')", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{kpi5ReviewerMidYearAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}]}, {"type": "panel", "name": "kpi5YearEndReview", "visibleIf": "{state} = 'yearendreviewing' or {state} = 'yearendreviewed'", "title": "Year-End Review for Recurring Sales", "description": "- Year-End Review: Provide assessments and update comments for this KPI.\n", "state": "expanded", "elements": [{"type": "text", "name": "kpi5YearEndYtd", "title": "Year-End YTD", "defaultValue": "#N/A", "readOnly": true}, {"type": "panel", "name": "kpi5PartnerYearEndGroup", "title": "Partner Year-End Review", "elements": [{"type": "dropdown", "name": "kpi5PartnerYearEndAssessment", "title": "Partner Year-End Assessment", "enableIf": "{role} != 'Reviewer' and {role} != 'ELT'", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{kpi5PartnerYearEndAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}, {"type": "panel", "name": "kpi5ReviewerYearEndGroup", "title": "Reviewer Year-End Review", "startWithNewLine": false, "elements": [{"type": "dropdown", "name": "kpi5ReviewerYearEndAssessment", "title": "Reviewer Year-End Assessment", "enableIf": "{role} != 'Partner' and {role} != 'ELT'", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{kpi5ReviewerYearEndAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}]}]}, {"type": "panel", "name": "kpi6", "requiredIf": "{kpi6TargetcurrentYear} = '' or {kpi6PartnerComments} = ''", "title": "Collaborative Sales", "description": "- Percentage of all sales (originator, lead, and team) outside your discipline (i.e. outside your PMP)\n", "state": "expanded", "elements": [{"type": "panel", "name": "kpi6TargetPanel", "width": "65%", "elements": [{"type": "comment", "name": "kpi6TargetGuidance", "title": "Target Guidance", "defaultValue": "The goal for this year is to increase your collaborative sales rate by 5%.  Therefore, a suggested target is 05%", "readOnly": true, "mapFrom": "NP Growth by PMP Guidance"}, {"type": "comment", "name": "kpi6PartnerComments", "title": "Partner Comments", "enableIf": "{role} <> 'ELT' and {state} = 'inprogress'", "isRequired": true}]}, {"type": "panel", "name": "kpi6TargetPanel2", "startWithNewLine": false, "elements": [{"type": "text", "name": "kpi6YtdPrevious", "visibleIf": "{state} = 'inprogress' or {state} = 'submitted'", "title": "{previousYear} YTD", "defaultValue": "#N/A", "readOnly": true, "mapFrom": "NP Growth by PMP", "exportColumnName": "NP Growth by PMP"}, {"type": "text", "name": "kpi6TargetcurrentYear", "title": "{currentYear} Target", "enableIf": "{role} <> 'ELT' and {state} = 'inprogress'", "isRequired": true, "exportColumnName": "NP Growth by PMP Target", "maskType": "currency", "maskSettings": {"allowNegativeValues": false, "precision": 0, "min": 0, "max": 100, "prefix": "%"}}]}, {"type": "panel", "name": "kpi6MidYearReview", "visibleIf": "{state} != 'inprogress' and {state} != 'submitted'", "title": "Mid-Year Review for Collaborative Sales", "description": "- Mid-Year Review: Provide assessments and update comments for this KPI.\n", "state": "expanded", "elements": [{"type": "text", "name": "kpi6MidYearYtd", "title": "Mid-Year YTD", "defaultValue": "#N/A", "readOnly": true}, {"type": "panel", "name": "kpi6PartnerMidYearGroup", "title": "Partner Mid-Year Review", "elements": [{"type": "dropdown", "name": "kpi6PartnerMidYearAssessment", "title": "Partner Mid-Year Assessment", "enableIf": "{role} != 'Reviewer' and {role} != 'ELT' and ({state} = 'midtermreviewing' or {state} = 'midtermreviewed')", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{kpi6PartnerMidYearAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}, {"type": "panel", "name": "kpi6ReviewerMidYearGroup", "title": "Reviewer Mid-Year Review", "startWithNewLine": false, "elements": [{"type": "dropdown", "name": "kpi6ReviewerMidYearAssessment", "title": "Reviewer Mid-Year Assessment", "enableIf": "{role} != 'ELT' and {role} != 'Partner' and ({state} = 'midtermreviewing' or {state} = 'midtermreviewed')", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{kpi6ReviewerMidYearAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}]}, {"type": "panel", "name": "kpi6YearEndReview", "visibleIf": "{state} = 'yearendreviewing' or {state} = 'yearendreviewed'", "title": "Year-End Review for Collaborative Sales", "description": "- Year-End Review: Provide assessments and update comments for this KPI.\n", "state": "expanded", "elements": [{"type": "text", "name": "kpi6YearEndYtd", "title": "Year-End YTD", "defaultValue": "#N/A", "readOnly": true}, {"type": "panel", "name": "kpi6PartnerYearEndGroup", "title": "Partner Year-End Review", "elements": [{"type": "dropdown", "name": "kpi6PartnerYearEndAssessment", "title": "Partner Year-End Assessment", "enableIf": "{role} != 'Reviewer' and {role} != 'ELT'", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{kpi6PartnerYearEndAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}, {"type": "panel", "name": "kpi6ReviewerYearEndGroup", "title": "Reviewer Year-End Review", "startWithNewLine": false, "elements": [{"type": "dropdown", "name": "kpi6ReviewerYearEndAssessment", "title": "Reviewer Year-End Assessment", "enableIf": "{role} != 'Partner' and {role} != 'ELT'", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{kpi6ReviewerYearEndAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}]}]}]}, {"type": "panel", "name": "kpi72", "title": "Revenue", "state": "collapsed", "elements": [{"type": "panel", "name": "kpi7p", "title": "Net Production Revenue by PMP (% Growth)", "state": "expanded", "elements": [{"type": "panel", "name": "kpi7TargetPanel", "width": "65%", "elements": [{"type": "comment", "name": "kpi7TargetGuidance", "title": "Target Guidance", "defaultValue": "The firm is budgeting revenue growth at 7.1% in 2025. The Business Services & Outsourcing (BSO) service line is budgeting revenue growth at 5.8% in 2025.", "readOnly": true, "mapFrom": "NP Growth by PMP Guidance", "rows": 6}, {"type": "comment", "name": "kpi7PartnerComments", "title": "Partner Comments", "enableIf": "{role} <> 'ELT' and {state} = 'inprogress'", "isRequired": true, "rows": 6}]}, {"type": "panel", "name": "kpi7TargetPanel2", "startWithNewLine": false, "elements": [{"type": "text", "name": "kpi7YtdPrevious", "visibleIf": "{state} = 'inprogress' or {state} = 'submitted'", "title": "{previousYear} YTD", "defaultValue": "#N/A", "readOnly": true, "mapFrom": "NP Growth by PMP", "exportColumnName": "NP Growth by PMP"}, {"type": "text", "name": "kpi7TargetcurrentYear", "title": "{currentYear} Target", "enableIf": "{role} <> 'ELT' and {state} = 'inprogress'", "isRequired": true, "exportColumnName": "NP Growth by PMP Target", "maskType": "currency", "maskSettings": {"allowNegativeValues": false, "precision": 0, "min": 0, "max": 100, "prefix": "%"}}]}, {"type": "panel", "name": "kpi7MidYearReview", "visibleIf": "{state} != 'inprogress' and {state} != 'submitted'", "title": "Mid-Year Review for Net Production Revenue by PMP (% Growth)", "description": "- Mid-Year Review: Provide assessments and update comments for this KPI.\n", "state": "expanded", "elements": [{"type": "text", "name": "kpi7MidYearYtd", "title": "Mid-Year YTD", "defaultValue": "#N/A", "readOnly": true}, {"type": "panel", "name": "kpi7PartnerMidYearGroup", "title": "Partner Mid-Year Review", "elements": [{"type": "dropdown", "name": "kpi7PartnerMidYearAssessment", "title": "Partner Mid-Year Assessment", "enableIf": "{role} != 'Reviewer' and {role} != 'ELT' and ({state} = 'midtermreviewing' or {state} = 'midtermreviewed')", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{kpi7PartnerMidYearAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}, {"type": "panel", "name": "kpi7ReviewerMidYearGroup", "title": "Reviewer Mid-Year Review", "startWithNewLine": false, "elements": [{"type": "dropdown", "name": "kpi7ReviewerMidYearAssessment", "title": "Reviewer Mid-Year Assessment", "enableIf": "{role} != 'ELT' and {role} != 'Partner' and ({state} = 'midtermreviewing' or {state} = 'midtermreviewed')", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{kpi7ReviewerMidYearAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}]}, {"type": "panel", "name": "kpi7YearEndReview", "visibleIf": "{state} = 'yearendreviewing' or {state} = 'yearendreviewed'", "title": "Year-End Review for Net Production Revenue by PMP (% Growth)", "description": "- Year-End Review: Provide assessments and update comments for this KPI.\n", "state": "expanded", "elements": [{"type": "text", "name": "kpi7YearEndYtd", "title": "Year-End YTD", "defaultValue": "#N/A", "readOnly": true}, {"type": "panel", "name": "kpi7PartnerYearEndGroup", "title": "Partner Year-End Review", "elements": [{"type": "dropdown", "name": "kpi7PartnerYearEndAssessment", "title": "Partner Year-End Assessment", "enableIf": "{role} != 'Reviewer' and {role} != 'ELT'", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{kpi7PartnerYearEndAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}, {"type": "panel", "name": "kpi7ReviewerYearEndGroup", "title": "Reviewer Year-End Review", "startWithNewLine": false, "elements": [{"type": "dropdown", "name": "kpi7ReviewerYearEndAssessment", "title": "Reviewer Year-End Assessment", "enableIf": "{role} != 'Partner' and {role} != 'ELT'", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{kpi7ReviewerYearEndAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}]}]}, {"type": "panel", "name": "kpi8", "requiredIf": "{kpi8TargetcurrentYear} = '' or {kpi8PartnerComments} = ''", "title": "Net Production Revenue by PMP", "description": "- Total production revenue for all projects where Partner is tagged as the PMP (excl. Admin <PERSON>)", "state": "expanded", "elements": [{"type": "panel", "name": "kpi8TargetPanel", "width": "65%", "elements": [{"type": "comment", "name": "kpi8TargetGuidance", "title": "Target Guidance", "defaultValue": "Based on your service line growth targets and historic metrics, a recommended target for Net Production Revenue by PMP in 2025 is $3,000,000", "readOnly": true, "mapFrom": "Net Production by PMP Guidance"}, {"type": "comment", "name": "kpi8PartnerComments", "title": "Partner Comments", "enableIf": "{role} <> 'ELT' and {state} = 'inprogress'", "isRequired": true}]}, {"type": "panel", "name": "kpi8TargetPanel2", "startWithNewLine": false, "elements": [{"type": "text", "name": "kpi8YtdPrevious", "visibleIf": "{state} = 'inprogress' or {state} = 'submitted'", "title": "{previousYear} YTD", "defaultValue": "#N/A", "readOnly": true, "mapFrom": "Net Production by PMP", "exportColumnName": "Net Production by PMP"}, {"type": "text", "name": "kpi8TargetcurrentYear", "title": "{currentYear} Target", "enableIf": "{role} <> 'ELT' and {state} = 'inprogress'", "isRequired": true, "exportColumnName": "Net Production by PMP Target", "min": 0, "max": 99999999, "maskType": "currency", "maskSettings": {"prefix": "$"}}]}, {"type": "panel", "name": "kpi8MidYearReview", "visibleIf": "{state} != 'inprogress' and {state} != 'submitted'", "title": "Mid-Year Review for Net Production Revenue by PMP", "description": "- Mid-Year Review: Provide assessments and update comments for this KPI.\n", "state": "expanded", "elements": [{"type": "text", "name": "kpi8MidYearYtd", "title": "Mid-Year YTD", "defaultValue": "#N/A", "readOnly": true}, {"type": "panel", "name": "kpi8PartnerMidYearGroup", "title": "Partner Mid-Year Review", "elements": [{"type": "dropdown", "name": "kpi8PartnerMidYearAssessment", "title": "Partner Mid-Year Assessment", "enableIf": "{role} != 'Reviewer' and {role} != 'ELT' and ({state} = 'midtermreviewing' or {state} = 'midtermreviewed')", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{kpi8PartnerMidYearAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}, {"type": "panel", "name": "kpi8ReviewerMidYearGroup", "title": "Reviewer Mid-Year Review", "startWithNewLine": false, "elements": [{"type": "dropdown", "name": "kpi8ReviewerMidYearAssessment", "title": "Reviewer Mid-Year Assessment", "enableIf": "{role} != 'ELT' and {role} != 'Partner' and ({state} = 'midtermreviewing' or {state} = 'midtermreviewed')", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{kpi8ReviewerMidYearAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}]}, {"type": "panel", "name": "kpi8YearEndReview", "visibleIf": "{state} = 'yearendreviewing' or {state} = 'yearendreviewed'", "title": "Year-End Review for Net Production Revenue by PMP", "description": "- Year-End Review: Provide assessments and update comments for this KPI.\n", "state": "expanded", "elements": [{"type": "text", "name": "kpi8YearEndYtd", "title": "Year-End YTD", "defaultValue": "#N/A", "readOnly": true}, {"type": "panel", "name": "kpi8PartnerYearEndGroup", "title": "Partner Year-End Review", "elements": [{"type": "dropdown", "name": "kpi8PartnerYearEndAssessment", "title": "Partner Year-End Assessment", "enableIf": "{role} != 'Reviewer' and {role} != 'ELT'", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{kpi8PartnerYearEndAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}, {"type": "panel", "name": "kpi8ReviewerYearEndGroup", "title": "Reviewer Year-End Review", "startWithNewLine": false, "elements": [{"type": "dropdown", "name": "kpi8ReviewerYearEndAssessment", "title": "Reviewer Year-End Assessment", "enableIf": "{role} != 'Partner' and {role} != 'ELT'", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{kpi8ReviewerYearEndAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}]}]}]}, {"type": "panel", "name": "kpi73", "title": "<PERSON><PERSON>", "state": "collapsed", "elements": [{"type": "panel", "name": "kpi9", "requiredIf": "{kpi9TargetcurrentYear} = '' or {kpi9PartnerComments} = ''", "title": "Margin %", "description": "- Margin (Net Production Revenue - Costs) as a percentage of Net Revenue (excluding Partner costs)", "state": "expanded", "elements": [{"type": "panel", "name": "kpi9TargetPanel", "width": "65%", "elements": [{"type": "comment", "name": "kpi9TargetGuidance", "title": "Target Guidance", "defaultValue": "The firm is budgeting margin growth at 6.3% in 2025. The Business Services & Outsourcing (BSO) service line is budgeting margin growth at 4.0% in 2025.", "readOnly": true, "mapFrom": "Margin % Guidance", "rows": 6}, {"type": "comment", "name": "kpi9PartnerComments", "title": "Partner Comments", "enableIf": "{role} <> 'ELT' and {state} = 'inprogress'", "isRequired": true}]}, {"type": "panel", "name": "kpi9TargetPanel2", "startWithNewLine": false, "elements": [{"type": "text", "name": "kpi9YtdPrevious", "visibleIf": "{state} = 'inprogress' or {state} = 'submitted'", "title": "{previousYear} YTD", "defaultValue": "#N/A", "readOnly": true, "mapFrom": "Margin %", "exportColumnName": "Margin %"}, {"type": "text", "name": "kpi9TargetcurrentYear", "title": "{currentYear} Target", "enableIf": "{role} <> 'ELT' and {state} = 'inprogress'", "isRequired": true, "exportColumnName": "Margin % Target", "maskType": "currency", "maskSettings": {"allowNegativeValues": false, "precision": 0, "min": 0, "max": 100, "prefix": "%"}}]}, {"type": "panel", "name": "kpi9MidYearReview", "visibleIf": "{state} != 'inprogress' and {state} != 'submitted'", "title": "Mid-Year Review for Margin %", "description": "- Mid-Year Review: Provide assessments and update comments for this KPI.\n", "state": "expanded", "elements": [{"type": "text", "name": "kpi9MidYearYtd", "title": "Mid-Year YTD", "defaultValue": "#N/A", "readOnly": true}, {"type": "panel", "name": "kpi9PartnerMidYearGroup", "title": "Partner Mid-Year Review", "elements": [{"type": "dropdown", "name": "kpi9PartnerMidYearAssessment", "title": "Partner Mid-Year Assessment", "enableIf": "{role} != 'Reviewer' and {role} != 'ELT' and ({state} = 'midtermreviewing' or {state} = 'midtermreviewed')", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{kpi9PartnerMidYearAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}, {"type": "panel", "name": "kpi9ReviewerMidYearGroup", "title": "Reviewer Mid-Year Review", "startWithNewLine": false, "elements": [{"type": "dropdown", "name": "kpi9ReviewerMidYearAssessment", "title": "Reviewer Mid-Year Assessment", "enableIf": "{role} != 'ELT' and {role} != 'Partner' and ({state} = 'midtermreviewing' or {state} = 'midtermreviewed')", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{kpi9ReviewerMidYearAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}]}, {"type": "panel", "name": "kpi9YearEndReview", "visibleIf": "{state} = 'yearendreviewing' or {state} = 'yearendreviewed'", "title": "Year-End Review for Margin %", "description": "- Year-End Review: Provide assessments and update comments for this KPI.\n", "state": "expanded", "elements": [{"type": "text", "name": "kpi9YearEndYtd", "title": "Year-End YTD", "defaultValue": "#N/A", "readOnly": true}, {"type": "panel", "name": "kpi9PartnerYearEndGroup", "title": "Partner Year-End Review", "elements": [{"type": "dropdown", "name": "kpi9PartnerYearEndAssessment", "title": "Partner Year-End Assessment", "enableIf": "{role} != 'Reviewer' and {role} != 'ELT'", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{kpi9PartnerYearEndAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}, {"type": "panel", "name": "kpi9ReviewerYearEndGroup", "title": "Reviewer Year-End Review", "startWithNewLine": false, "elements": [{"type": "dropdown", "name": "kpi9ReviewerYearEndAssessment", "title": "Reviewer Year-End Assessment", "enableIf": "{role} != 'Partner' and {role} != 'ELT'", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{kpi9ReviewerYearEndAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}]}]}, {"type": "panel", "name": "kpi10", "requiredIf": "{kpi10TargetcurrentYear} = '' or {kpi10PartnerComments} = ''", "title": "Margin $", "description": "- Net Production Revenue by PMP less Standard Cost (Excluding Partner Costs)", "state": "expanded", "elements": [{"type": "panel", "name": "kpi10TargetPanel", "width": "65%", "elements": [{"type": "comment", "name": "kpi10TargetGuidance", "title": "Target Guidance", "defaultValue": "Based on your service line growth targets and historic metrics, a recommended target for Margin $ in 2025 is $1,000,000", "readOnly": true, "mapFrom": "Margin $ Guidance"}, {"type": "comment", "name": "kpi10PartnerComments", "title": "Partner Comments", "enableIf": "{role} <> 'ELT' and {state} = 'inprogress'", "isRequired": true}]}, {"type": "panel", "name": "kpi10TargetPanel2", "startWithNewLine": false, "elements": [{"type": "text", "name": "kpi10YtdPrevious", "visibleIf": "{state} = 'inprogress' or {state} = 'submitted'", "title": "{previousYear} YTD", "defaultValue": "#N/A", "readOnly": true, "mapFrom": "Margin $", "exportColumnName": "Margin $"}, {"type": "text", "name": "kpi10TargetcurrentYear", "title": "{currentYear} Target", "enableIf": "{role} <> 'ELT' and {state} = 'inprogress'", "isRequired": true, "exportColumnName": "Margin $ Target", "maskType": "currency", "maskSettings": {"allowNegativeValues": false, "min": 0, "max": 99999999999, "prefix": "$"}}]}, {"type": "panel", "name": "kpi10MidYearReview", "visibleIf": "{state} != 'inprogress' and {state} != 'submitted'", "title": "Mid-Year Review for Margin $", "description": "- Mid-Year Review: Provide assessments and update comments for this KPI.\n", "state": "expanded", "elements": [{"type": "text", "name": "kpi10MidYearYtd", "title": "Mid-Year YTD", "defaultValue": "#N/A", "readOnly": true}, {"type": "panel", "name": "kpi10PartnerMidYearGroup", "title": "Partner Mid-Year Review", "elements": [{"type": "dropdown", "name": "kpi10PartnerMidYearAssessment", "title": "Partner Mid-Year Assessment", "enableIf": "{role} != 'Reviewer' and {role} != 'ELT' and ({state} = 'midtermreviewing' or {state} = 'midtermreviewed')", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{kpi10PartnerMidYearAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}, {"type": "panel", "name": "kpi10ReviewerMidYearGroup", "title": "Reviewer Mid-Year Review", "startWithNewLine": false, "elements": [{"type": "dropdown", "name": "kpi10ReviewerMidYearAssessment", "title": "Reviewer Mid-Year Assessment", "enableIf": "{role} != 'ELT' and {role} != 'Partner' and ({state} = 'midtermreviewing' or {state} = 'midtermreviewed')", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{kpi10ReviewerMidYearAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}]}, {"type": "panel", "name": "kpi10YearEndReview", "visibleIf": "{state} = 'yearendreviewing' or {state} = 'yearendreviewed'", "title": "Year-End Review for Margin $", "description": "- Year-End Review: Provide assessments and update comments for this KPI.\n", "state": "expanded", "elements": [{"type": "text", "name": "kpi10YearEndYtd", "title": "Year-End YTD", "defaultValue": "#N/A", "readOnly": true}, {"type": "panel", "name": "kpi10PartnerYearEndGroup", "title": "Partner Year-End Review", "elements": [{"type": "dropdown", "name": "kpi10PartnerYearEndAssessment", "title": "Partner Year-End Assessment", "enableIf": "{role} != 'Reviewer' and {role} != 'ELT'", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{kpi10PartnerYearEndAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}, {"type": "panel", "name": "kpi10ReviewerYearEndGroup", "title": "Reviewer Year-End Review", "startWithNewLine": false, "elements": [{"type": "dropdown", "name": "kpi10ReviewerYearEndAssessment", "title": "Reviewer Year-End Assessment", "enableIf": "{role} != 'Partner' and {role} != 'ELT'", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{kpi10ReviewerYearEndAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}]}]}]}, {"type": "panel", "name": "kpi74", "title": "Productivity", "state": "collapsed", "elements": [{"type": "panel", "name": "kpi11", "requiredIf": "{kpi11TargetcurrentYear} = '' or {kpi11PartnerComments} = ''", "title": "Productivity - Total Billable Hours", "description": "- Total Hours Billed by Partner", "state": "expanded", "elements": [{"type": "panel", "name": "kpi11TargetPanel", "width": "65%", "elements": [{"type": "comment", "name": "kpi11TargetGuidance", "title": "Target Guidance", "defaultValue": "Based on your service line growth targets and historic metrics, a recommended target for Productivity - Total Billable Hours in 2025 is 1,500 hrs", "readOnly": true, "mapFrom": "Partner's Billable Hours Guidance"}, {"type": "comment", "name": "kpi11PartnerComments", "title": "Partner Comments", "enableIf": "{role} <> 'ELT' and {state} = 'inprogress'", "isRequired": true}]}, {"type": "panel", "name": "kpi11TargetPanel2", "startWithNewLine": false, "elements": [{"type": "text", "name": "kpi11YtdPrevious", "visibleIf": "{state} = 'inprogress' or {state} = 'submitted'", "title": "{previousYear} YTD", "defaultValue": "#N/A", "readOnly": true, "mapFrom": "Partner's Billable Hours", "exportColumnName": "Partner's Billable Hours"}, {"type": "text", "name": "kpi11TargetcurrentYear", "title": "{currentYear} Target", "enableIf": "{role} <> 'ELT' and {state} = 'inprogress'", "isRequired": true, "exportColumnName": "Productivity - Total Billable Hours Target", "maskType": "currency", "maskSettings": {"allowNegativeValues": false, "precision": 0, "min": 0, "max": 9999, "suffix": "hrs"}}]}, {"type": "panel", "name": "kpi11MidYearReview", "visibleIf": "{state} != 'inprogress' and {state} != 'submitted'", "title": "Mid-Year Review for Productivity - Total Billable Hours", "description": "- Mid-Year Review: Provide assessments and update comments for this KPI.\n", "state": "expanded", "elements": [{"type": "text", "name": "kpi11MidYearYtd", "title": "Mid-Year YTD", "defaultValue": "#N/A", "readOnly": true}, {"type": "panel", "name": "kpi11PartnerMidYearGroup", "title": "Partner Mid-Year Review", "elements": [{"type": "dropdown", "name": "kpi11PartnerMidYearAssessment", "title": "Partner Mid-Year Assessment", "enableIf": "{role} != 'Reviewer' and {role} != 'ELT' and ({state} = 'midtermreviewing' or {state} = 'midtermreviewed')", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{kpi11PartnerMidYearAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}, {"type": "panel", "name": "kpi11ReviewerMidYearGroup", "title": "Reviewer Mid-Year Review", "startWithNewLine": false, "elements": [{"type": "dropdown", "name": "kpi11ReviewerMidYearAssessment", "title": "Reviewer Mid-Year Assessment", "enableIf": "{role} != 'ELT' and {role} != 'Partner' and ({state} = 'midtermreviewing' or {state} = 'midtermreviewed')", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{kpi11ReviewerMidYearAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}]}, {"type": "panel", "name": "kpi11YearEndReview", "visibleIf": "{state} = 'yearendreviewing' or {state} = 'yearendreviewed'", "title": "Year-End Review for Productivity - Total Billable Hours", "description": "- Year-End Review: Provide assessments and update comments for this KPI.\n", "state": "expanded", "elements": [{"type": "text", "name": "kpi11YearEndYtd", "title": "Year-End YTD", "defaultValue": "#N/A", "readOnly": true}, {"type": "panel", "name": "kpi11PartnerYearEndGroup", "title": "Partner Year-End Review", "elements": [{"type": "dropdown", "name": "kpi11PartnerYearEndAssessment", "title": "Partner Year-End Assessment", "enableIf": "{role} != 'Reviewer' and {role} != 'ELT'", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{kpi11PartnerYearEndAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}, {"type": "panel", "name": "kpi11ReviewerYearEndGroup", "title": "Reviewer Year-End Review", "startWithNewLine": false, "elements": [{"type": "dropdown", "name": "kpi11ReviewerYearEndAssessment", "title": "Reviewer Year-End Assessment", "enableIf": "{role} != 'Partner' and {role} != 'ELT'", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{kpi11ReviewerYearEndAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}]}]}]}, {"type": "panel", "name": "kpicore", "title": "Core Partner Expectations", "description": "- Note that these targets are pre-populated based on your service line as these are uniform expectations of all partners.", "state": "collapsed", "elements": [{"type": "panel", "name": "kpi12", "requiredIf": "{kpi12TargetcurrentYear} = '' or {kpi12PartnerComments} = ''", "title": "Average WIP Aging", "description": "- Average age of WIP based on calculation: (month-end balance *365)/12-month net revenue", "state": "expanded", "elements": [{"type": "panel", "name": "kpi12TargetPanel", "width": "65%", "elements": [{"type": "comment", "name": "kpi12TargetGuidance", "title": "Target Guidance", "defaultValue": "It is expected to meet these targets.", "readOnly": true, "mapFrom": "WIP Aging Days Guidance"}, {"type": "comment", "name": "kpi12PartnerComments", "title": "Partner Comments", "enableIf": "{role} <> 'ELT' and {state} = 'inprogress'", "isRequired": true}]}, {"type": "panel", "name": "kpi12TargetPanel2", "startWithNewLine": false, "elements": [{"type": "text", "name": "kpi12YtdPrevious", "visibleIf": "{state} = 'inprogress' or {state} = 'submitted'", "title": "{previousYear} YTD", "defaultValue": "#N/A", "readOnly": true, "mapFrom": "WIP Aging Days", "exportColumnName": "WIP Aging Days"}, {"type": "text", "name": "kpi12TargetcurrentYear", "title": "{currentYear} Target", "enableIf": "{role} <> 'ELT' and {state} = 'inprogress'", "isRequired": true, "exportColumnName": "WIP Aging Target", "maskType": "numeric", "maskSettings": {"allowNegativeValues": false, "precision": 1, "min": 0, "max": 99999999999}}]}, {"type": "panel", "name": "kpi12MidYearReview", "visibleIf": "{state} != 'inprogress' and {state} != 'submitted'", "title": "Mid-Year Review for Average WIP Aging", "description": "- Mid-Year Review: Provide assessments and update comments for this KPI.\n", "state": "expanded", "elements": [{"type": "text", "name": "kpi12MidYearYtd", "title": "Mid-Year YTD", "defaultValue": "#N/A", "readOnly": true}, {"type": "panel", "name": "kpi12PartnerMidYearGroup", "title": "Partner Mid-Year Review", "elements": [{"type": "dropdown", "name": "kpi12PartnerMidYearAssessment", "title": "Partner Mid-Year Assessment", "enableIf": "{role} != 'Reviewer' and {role} != 'ELT' and ({state} = 'midtermreviewing' or {state} = 'midtermreviewed')", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{kpi12PartnerMidYearAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}, {"type": "panel", "name": "kpi12ReviewerMidYearGroup", "title": "Reviewer Mid-Year Review", "startWithNewLine": false, "elements": [{"type": "dropdown", "name": "kpi12ReviewerMidYearAssessment", "title": "Reviewer Mid-Year Assessment", "enableIf": "{role} != 'ELT' and {role} != 'Partner' and ({state} = 'midtermreviewing' or {state} = 'midtermreviewed')", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{kpi12ReviewerMidYearAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}]}, {"type": "panel", "name": "kpi12YearEndReview", "visibleIf": "{state} = 'yearendreviewing' or {state} = 'yearendreviewed'", "title": "Year-End Review for Average WIP Aging", "description": "- Year-End Review: Provide assessments and update comments for this KPI.\n", "state": "expanded", "elements": [{"type": "text", "name": "kpi12YearEndYtd", "title": "Year-End YTD", "defaultValue": "#N/A", "readOnly": true}, {"type": "panel", "name": "kpi12PartnerYearEndGroup", "title": "Partner Year-End Review", "elements": [{"type": "dropdown", "name": "kpi12PartnerYearEndAssessment", "title": "Partner Year-End Assessment", "enableIf": "{role} != 'Reviewer' and {role} != 'ELT'", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{kpi12PartnerYearEndAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}, {"type": "panel", "name": "kpi12ReviewerYearEndGroup", "title": "Reviewer Year-End Review", "startWithNewLine": false, "elements": [{"type": "dropdown", "name": "kpi12ReviewerYearEndAssessment", "title": "Reviewer Year-End Assessment", "enableIf": "{role} != 'Partner' and {role} != 'ELT'", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{kpi12ReviewerYearEndAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}]}]}, {"type": "panel", "name": "kpi13", "requiredIf": "{kpi13TargetcurrentYear} = '' or {kpi13PartnerComments} = ''", "title": "Average AR Aging", "description": "- Average age of AR based on calculation: (month-end balance *365)/12-month net revenue", "state": "expanded", "width": "65%", "elements": [{"type": "panel", "name": "kpi13TargetPanel", "width": "65%", "elements": [{"type": "comment", "name": "kpi13TargetGuidance", "title": "Target Guidance", "defaultValue": "It is expected to meet these targets.", "readOnly": true, "mapFrom": "AR Aging Days Guidance"}, {"type": "comment", "name": "kpi13PartnerComments", "title": "Partner Comments", "enableIf": "{role} <> 'ELT' and {state} = 'inprogress'", "isRequired": true}]}, {"type": "panel", "name": "kpi13TargetPanel2", "startWithNewLine": false, "elements": [{"type": "text", "name": "kpi13YtdPrevious", "visibleIf": "{state} = 'inprogress' or {state} = 'submitted'", "title": "{previousYear} YTD", "defaultValue": "#N/A", "readOnly": true, "mapFrom": "AR Aging Days", "exportColumnName": "AR Aging Days"}, {"type": "text", "name": "kpi13TargetcurrentYear", "title": "{currentYear} Target", "enableIf": "{role} <> 'ELT' and {state} = 'inprogress'", "isRequired": true, "exportColumnName": "AR Aging Target", "maskType": "numeric", "maskSettings": {"allowNegativeValues": false, "precision": 1, "min": 0, "max": 99999999999}}]}, {"type": "panel", "name": "kpi13MidYearReview", "visibleIf": "{state} != 'inprogress' and {state} != 'submitted'", "title": "Mid-Year Review for Average AR Aging", "description": "- Mid-Year Review: Provide assessments and update comments for this KPI.\n", "state": "expanded", "elements": [{"type": "text", "name": "kpi13MidYearYtd", "title": "Mid-Year YTD", "defaultValue": "#N/A", "readOnly": true}, {"type": "panel", "name": "kpi13PartnerMidYearGroup", "title": "Partner Mid-Year Review", "elements": [{"type": "dropdown", "name": "kpi13PartnerMidYearAssessment", "title": "Partner Mid-Year Assessment", "enableIf": "{role} != 'Reviewer' and {role} != 'ELT' and ({state} = 'midtermreviewing' or {state} = 'midtermreviewed')", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{kpi13PartnerMidYearAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}, {"type": "panel", "name": "kpi13ReviewerMidYearGroup", "title": "Reviewer Mid-Year Review", "startWithNewLine": false, "elements": [{"type": "dropdown", "name": "kpi13ReviewerMidYearAssessment", "title": "Reviewer Mid-Year Assessment", "enableIf": "{role} != 'ELT' and {role} != 'Partner' and ({state} = 'midtermreviewing' or {state} = 'midtermreviewed')", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{kpi13ReviewerMidYearAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}]}, {"type": "panel", "name": "kpi13YearEndReview", "visibleIf": "{state} = 'yearendreviewing' or {state} = 'yearendreviewed'", "title": "Year-End Review for Average AR Aging", "description": "- Year-End Review: Provide assessments and update comments for this KPI.\n", "state": "expanded", "elements": [{"type": "text", "name": "kpi13YearEndYtd", "title": "Year-End YTD", "defaultValue": "#N/A", "readOnly": true}, {"type": "panel", "name": "kpi13PartnerYearEndGroup", "title": "Partner Year-End Review", "elements": [{"type": "dropdown", "name": "kpi13PartnerYearEndAssessment", "title": "Partner Year-End Assessment", "enableIf": "{role} != 'Reviewer' and {role} != 'ELT'", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{kpi13PartnerYearEndAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}, {"type": "panel", "name": "kpi13ReviewerYearEndGroup", "title": "Reviewer Year-End Review", "startWithNewLine": false, "elements": [{"type": "dropdown", "name": "kpi13ReviewerYearEndAssessment", "title": "Reviewer Year-End Assessment", "enableIf": "{role} != 'Partner' and {role} != 'ELT'", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{kpi13ReviewerYearEndAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}]}]}, {"type": "panel", "name": "kpi14", "requiredIf": "{kpi14TargetcurrentYear} = '' or {kpi14PartnerComments} = ''", "title": "Bad Debts", "description": "- Bad Debts by CRP as a percentage of CRP Revenue", "state": "expanded", "elements": [{"type": "panel", "name": "kpi14TargetPanel", "width": "65%", "elements": [{"type": "comment", "name": "kpi14TargetGuidance", "title": "Target Guidance", "defaultValue": "It is expected to meet these targets.", "readOnly": true, "mapFrom": "Bad Debt (%) Guidance"}, {"type": "comment", "name": "kpi14PartnerComments", "title": "Partner Comments", "enableIf": "{role} <> 'ELT' and {state} = 'inprogress'", "isRequired": true}]}, {"type": "panel", "name": "kpi14TargetPanel2", "startWithNewLine": false, "elements": [{"type": "text", "name": "kpi14YtdPrevious", "visibleIf": "{state} = 'inprogress' or {state} = 'submitted'", "title": "{previousYear} YTD", "defaultValue": "#N/A", "readOnly": true, "mapFrom": "Bad Debt (%)", "exportColumnName": "Bad Debt (%)"}, {"type": "text", "name": "kpi14TargetcurrentYear", "title": "{currentYear} Target", "enableIf": "{role} <> 'ELT' and {state} = 'inprogress'", "isRequired": true, "exportColumnName": "Bad Debt Target", "maskType": "numeric", "maskSettings": {"allowNegativeValues": false, "precision": 1, "min": 0, "max": 99999999999}}]}, {"type": "panel", "name": "kpi14MidYearReview", "visibleIf": "{state} != 'inprogress' and {state} != 'submitted'", "title": "Mid-Year Review for Utilization Rate", "description": "- Mid-Year Review: Provide assessments and update comments for this KPI.\n", "state": "expanded", "elements": [{"type": "text", "name": "kpi14MidYearYtd", "title": "Mid-Year YTD", "defaultValue": "#N/A", "readOnly": true}, {"type": "panel", "name": "kpi14PartnerMidYearGroup", "title": "Partner Mid-Year Review", "elements": [{"type": "dropdown", "name": "kpi14PartnerMidYearAssessment", "title": "Partner Mid-Year Assessment", "enableIf": "{role} != 'Reviewer' and {role} != 'ELT' and ({state} = 'midtermreviewing' or {state} = 'midtermreviewed')", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{kpi14PartnerMidYearAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}, {"type": "panel", "name": "kpi14ReviewerMidYearGroup", "title": "Reviewer Mid-Year Review", "startWithNewLine": false, "elements": [{"type": "dropdown", "name": "kpi14ReviewerMidYearAssessment", "title": "Reviewer Mid-Year Assessment", "enableIf": "{role} != 'ELT' and {role} != 'Partner' and ({state} = 'midtermreviewing' or {state} = 'midtermreviewed')", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{kpi14ReviewerMidYearAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}]}, {"type": "panel", "name": "kpi14YearEndReview", "visibleIf": "{state} = 'yearendreviewing' or {state} = 'yearendreviewed'", "title": "Year-End Review for Utilization Rate", "description": "- Year-End Review: Provide assessments and update comments for this KPI.\n", "state": "expanded", "elements": [{"type": "text", "name": "kpi14YearEndYtd", "title": "Year-End YTD", "defaultValue": "#N/A", "readOnly": true}, {"type": "panel", "name": "kpi14PartnerYearEndGroup", "title": "Partner Year-End Review", "elements": [{"type": "dropdown", "name": "kpi14PartnerYearEndAssessment", "title": "Partner Year-End Assessment", "enableIf": "{role} != 'Reviewer' and {role} != 'ELT'", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{kpi14PartnerYearEndAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}, {"type": "panel", "name": "kpi14ReviewerYearEndGroup", "title": "Reviewer Year-End Review", "startWithNewLine": false, "elements": [{"type": "dropdown", "name": "kpi14ReviewerYearEndAssessment", "title": "Reviewer Year-End Assessment", "enableIf": "{role} != 'Partner' and {role} != 'ELT'", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{kpi14ReviewerYearEndAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}]}]}]}]}, {"name": "page4", "title": "Technical Mastery", "elements": [{"type": "panel", "name": "industryFocus_panel", "title": "3A) Industry Focus", "description": "Instructions:\nPlease select your Industry Major and Industry Minor.  Keep in mind that the firm's industries of focus are: Consumer Business; Financial Services; Manufacturing; Real Estate & Construction; Technology, Media, & Telecommunications; and Private Equity", "state": "expanded", "elements": [{"type": "panel", "name": "industryMajor_panel", "title": "Major Focus", "description": "Industry Major: Industry where you see yourself spending approximately 75% of your time. You would be working towards reaching your goal in coming years.", "state": "expanded", "elements": [{"type": "panel", "name": "panel5", "width": "65%", "elements": [{"type": "comment", "name": "industryMajorTarget", "title": "{currentYear} Target", "enableIf": "{role} <> 'ELT' and {state} = 'inprogress'", "isRequired": true}, {"type": "comment", "name": "industryMajorComments", "title": "Partner Comments", "enableIf": "{role} <> 'ELT' and {state} = 'inprogress'", "isRequired": true}]}, {"type": "panel", "name": "panel3", "startWithNewLine": false, "elements": [{"type": "dropdown", "name": "industryMajorSelection", "startWithNewLine": false, "title": "{currentYear} Industry Selection", "isRequired": true, "choices": ["Agriculture", "Consumer Business", "Financial Services", "Manufacturing", "Natural Resources", "Not-for-Profit & Education", "Private Equity", "Professional Services", "Public Sector", "Real Estate & Construction", "Technology, Media and Telecommunications"]}, {"type": "text", "name": "industryMajorHours", "title": "% of PMP Hours in {previousYear}", "readOnly": true}]}, {"type": "panel", "name": "industryMajorMidYearReview", "visibleIf": "{state} != 'inprogress' and {state} != 'submitted'", "title": "Mid-Year Review for Industry Major", "description": "- Mid-Year Review: Provide assessments and update comments for this impact item.\n", "state": "expanded", "elements": [{"type": "panel", "name": "industryMajorPartnerMidYearGroup", "title": "Partner Mid-Year Review", "elements": [{"type": "dropdown", "name": "industryMajorPartnerMidYearAssessment", "title": "Partner Mid-Year Assessment", "enableIf": "{role} != 'Reviewer' and {role} != 'ELT' and ({state} = 'midtermreviewing' or {state} = 'midtermreviewed')", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{industryMajorPartnerMidYearAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}, {"type": "panel", "name": "industryMajorReviewerMidYearGroup", "title": "Reviewer Mid-Year Review", "startWithNewLine": false, "elements": [{"type": "dropdown", "name": "industryMajorReviewerMidYearAssessment", "title": "Reviewer Mid-Year Assessment", "enableIf": "{role} != 'ELT' and {role} != 'Partner' and ({state} = 'midtermreviewing' or {state} = 'midtermreviewed')", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{industryMajorReviewerMidYearAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}]}, {"type": "panel", "name": "industryMajorYearEndReview", "visibleIf": "{state} = 'yearendreviewing' or {state} = 'yearendreviewed'", "title": "Year-End Review for Industry Major", "description": "- Year-End Review: Provide assessments and update comments for this impact item.\n", "state": "expanded", "elements": [{"type": "panel", "name": "industryMajorPartnerYearEndGroup", "title": "Partner Year-End Review", "elements": [{"type": "dropdown", "name": "industryMajorPartnerYearEndAssessment", "title": "Partner Year-End Assessment", "enableIf": "{role} != 'Reviewer' and {role} != 'ELT'", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{industryMajorPartnerYearEndAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}, {"type": "panel", "name": "industryMajorReviewerYearEndGroup", "title": "Reviewer Year-End Review", "startWithNewLine": false, "elements": [{"type": "dropdown", "name": "industryMajorReviewerYearEndAssessment", "title": "Reviewer Year-End Assessment", "enableIf": "{role} != 'Partner' and {role} != 'ELT'", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{industryMajorReviewerYearEndAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}]}]}, {"type": "panel", "name": "industryMinor_panel", "title": "Minor Focus", "description": "Industry Minor: Industry where you see yourself spending approximately 25% of your time. You would be working towards reaching your goal in coming years.", "state": "expanded", "elements": [{"type": "panel", "name": "panel9", "width": "65%", "elements": [{"type": "comment", "name": "industryMinorTarget", "title": "{currentYear} Target", "enableIf": "{role} <> 'ELT' and {state} = 'inprogress'", "isRequired": true}, {"type": "comment", "name": "industryMinorComments", "title": "Partner Comments", "enableIf": "{role} <> 'ELT' and {state} = 'inprogress'", "isRequired": true}]}, {"type": "panel", "name": "panel11", "startWithNewLine": false, "elements": [{"type": "dropdown", "name": "industryMinorSelection", "startWithNewLine": false, "title": "{currentYear} Industry Selection", "enableIf": "{role} <> 'ELT' and {state} = 'inprogress'", "isRequired": true, "choices": ["Agriculture", "Consumer Business", "Financial Services", "Manufacturing", "Natural Resources", "Not-for-Profit & Education", "Private Equity", "Professional Services", "Public Sector", "Real Estate & Construction", "Technology, Media and Telecommunications"]}, {"type": "text", "name": "industryMinorHours", "title": "% of PMP Hours in {previousYear}", "readOnly": true}]}, {"type": "panel", "name": "industryMinorMidYearReview", "visibleIf": "{state} != 'inprogress' and {state} != 'submitted'", "title": "Mid-Year Review for Industry Minor", "description": "- Mid-Year Review: Provide assessments and update comments for this impact item.\n", "state": "expanded", "elements": [{"type": "panel", "name": "industryMinorPartnerMidYearGroup", "title": "Partner Mid-Year Review", "width": "65%", "elements": [{"type": "dropdown", "name": "industryMinorPartnerMidYearAssessment", "title": "Partner Mid-Year Assessment", "enableIf": "{role} != 'Reviewer' and {role} != 'ELT' and ({state} = 'midtermreviewing' or {state} = 'midtermreviewed')", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{industryMinorPartnerMidYearAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}, {"type": "panel", "name": "industryMinorReviewerMidYearGroup", "title": "Reviewer Mid-Year Review", "startWithNewLine": false, "elements": [{"type": "dropdown", "name": "industryMinorReviewerMidYearAssessment", "title": "Reviewer Mid-Year Assessment", "enableIf": "{role} != 'ELT' and {role} != 'Partner' and ({state} = 'midtermreviewing' or {state} = 'midtermreviewed')", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{industryMinorReviewerMidYearAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}]}, {"type": "panel", "name": "industryMinorYearEndReview", "visibleIf": "{state} = 'yearendreviewing' or {state} = 'yearendreviewed'", "title": "Year-End Review for Industry Minor", "description": "- Year-End Review: Provide assessments and update comments for this impact item.\n", "state": "expanded", "elements": [{"type": "panel", "name": "industryMinorPartnerYearEndGroup", "title": "Partner Year-End Review", "width": "65%", "elements": [{"type": "dropdown", "name": "industryMinorPartnerYearEndAssessment", "title": "Partner Year-End Assessment", "enableIf": "{role} != 'Reviewer' and {role} != 'ELT'", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{industryMinorPartnerYearEndAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}, {"type": "panel", "name": "industryMinorReviewerYearEndGroup", "title": "Reviewer Year-End Review", "startWithNewLine": false, "elements": [{"type": "dropdown", "name": "industryMinorReviewerYearEndAssessment", "title": "Reviewer Year-End Assessment", "enableIf": "{role} != 'Partner' and {role} != 'ELT'", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{industryMinorReviewerYearEndAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}]}]}]}, {"type": "panel", "name": "industryQuality_panel", "elements": [{"type": "panel", "name": "industryQualityTopic1", "title": "Promotion of Quality\n", "elements": [{"type": "comment", "name": "industryQualityGuidance1", "title": "Overview / Guidance", "readOnly": true}, {"type": "comment", "name": "industryQualityCommitment1", "title": "My {currentYear} Commitment", "enableIf": "{role} <> 'ELT' and {state} = 'inprogress'", "isRequired": true}]}, {"type": "panel", "name": "industryQualityTopic2", "title": "Quality on Engagements\n", "elements": [{"type": "comment", "name": "industryQualityGuidance2", "title": "Overview / Guidance", "readOnly": true}, {"type": "comment", "name": "industryQualityCommitment2", "title": "My {currentYear} Commitment", "enableIf": "{role} <> 'ELT' and {state} = 'inprogress'", "isRequired": true}]}]}]}, {"name": "page5", "title": "Kindness", "elements": [{"type": "panel", "name": "section4a", "title": "4A) Talent Growth & Development", "description": "Instructions:\nNo targets or comments required in this section.\n", "state": "expanded", "elements": [{"type": "html", "name": "question5", "html": "<table border=1>\n    <tr>\n        <th></th>\n        <th>2024 YTD (Average number of learning hours for people who report to you)</th>\n        <th>2024 YTD Firm Average</th>\n        <th>Target Guidance</th>\n        <th>Partner Comments</th>\n    </tr>\n    <tr>\n        <td>Talent Development Hours<br><span>Definition: Total learning hours for employees that report to you</span></td>\n        <td>108</td>\n        <td>64</td>\n        <td>This is FYI-only at this time.<br>In 2025, we will be tracking non-technical hours for all employees reporting to you.</td>\n        <td>No comments required</td>\n    </tr>\n</table>"}]}, {"type": "panel", "name": "section4", "requiredIf": "{kindnessTopic} = '' or {kindnessGuidance} = '' or {kindnessCommitment} = ''", "title": "4B) Partner Contributions", "description": "Instructions: Over and above running your practice, how are you contributing to goals / other behaviors important to the firm?\nSelect from the dropdown below. You can add upto 5 contributions.\n", "state": "expanded", "elements": [{"type": "dropdown", "name": "kindnessTopic", "title": "Topic", "enableIf": "{role} <> 'ELT' and {state} = 'inprogress'", "isRequired": true, "choices": ["Coaching", "Culture development", "Diversity improvements", "Environmental Impact", "International Referrals", "KAP National Partner", "KAP National Service Line Partner", "KAP Service/Industry/Regional Partner", "Market eminence", "Membership", "New Partner Development", "Revenue Generation", "Strategic communication", "Technical Excellence", "Other"]}, {"type": "comment", "name": "kindnessGuidance", "width": "70%", "startWithNewLine": false, "title": "Overview / Guidance", "readOnly": true}, {"type": "comment", "name": "kindnessCommitment", "title": "My {currentYear} Commitment", "enableIf": "{role} <> 'ELT' and {state} = 'inprogress'", "isRequired": true}, {"type": "panel", "name": "developmentMidYearReview", "visibleIf": "{state} != 'inprogress' and {state} != 'submitted'", "title": "Mid-Year Review for Personal Development Plan", "description": "- Mid-Year Review: Provide assessments and update comments for this development plan.\n", "state": "expanded", "elements": [{"type": "panel", "name": "developmentPartnerMidYearGroup", "title": "Partner Mid-Year Review", "elements": [{"type": "dropdown", "name": "developmentPartnerMidYearAssessment", "title": "Partner Mid-Year Assessment", "enableIf": "{role} != 'Reviewer' and {role} != 'ELT' and ({state} = 'midtermreviewing' or {state} = 'midtermreviewed')", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{developmentPartnerMidYearAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}, {"type": "panel", "name": "developmentReviewerMidYearGroup", "title": "Reviewer Mid-Year Review", "elements": [{"type": "dropdown", "name": "developmentReviewerMidYearAssessment", "title": "Reviewer Mid-Year Assessment", "enableIf": "{role} != 'ELT' and {role} != 'Partner' and ({state} = 'midtermreviewing' or {state} = 'midtermreviewed')", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{developmentReviewerMidYearAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}]}, {"type": "panel", "name": "developmentYearEndReview", "visibleIf": "{state} = 'yearendreviewing' or {state} = 'yearendreviewed'", "title": "Year-End Review for Personal Development Plan", "description": "- Year-End Review: Provide assessments and update comments for this development plan.\n", "state": "expanded", "elements": [{"type": "panel", "name": "developmentPartnerYearEndGroup", "title": "Partner Year-End Review", "elements": [{"type": "dropdown", "name": "developmentPartnerYearEndAssessment", "title": "Partner Year-End Assessment", "enableIf": "{role} != 'Reviewer' and {role} != 'ELT'", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{developmentPartnerYearEndAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}, {"type": "panel", "name": "developmentReviewerYearEndGroup", "title": "Reviewer Year-End Review", "startWithNewLine": false, "elements": [{"type": "dropdown", "name": "developmentReviewerYearEndAssessment", "title": "Reviewer Year-End Assessment", "enableIf": "{role} != 'Partner' and {role} != 'ELT'", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{developmentReviewerYearEndAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}]}]}]}, {"name": "page6", "title": "Sophisticated", "elements": [{"type": "panel", "name": "panel7", "title": "Client Value Index", "description": "- The ratio of the revenue from clients to the costs incurred.", "state": "expanded", "elements": [{"type": "panel", "name": "question1", "width": "65%", "elements": [{"type": "comment", "name": "question3", "title": "Target Guidance", "readOnly": true}, {"type": "comment", "name": "question4", "title": "Partner Comments", "enableIf": "{role} <> 'ELT' and {state} = 'inprogress'", "isRequired": true}]}, {"type": "panel", "name": "question2", "startWithNewLine": false, "elements": [{"type": "text", "name": "question9", "title": "{previousYear} YTD", "readOnly": true}, {"type": "text", "name": "question10", "title": "{currentYear} Target", "enableIf": "{role} <> 'ELT' and {state} = 'inprogress'", "isRequired": true, "inputType": "number"}]}, {"type": "panel", "name": "cviMidYearReview", "visibleIf": "{state} != 'inprogress' and {state} != 'submitted'", "title": "Mid-Year Review for Client Value Index", "description": "- Mid-Year Review: Provide assessments and update comments for this KPI.\n", "state": "expanded", "elements": [{"type": "text", "name": "cviMidYearYtd", "title": "Mid-Year YTD", "defaultValue": "#N/A", "readOnly": true}, {"type": "panel", "name": "cviPartnerMidYearGroup", "title": "Partner Mid-Year Review", "elements": [{"type": "dropdown", "name": "cviPartnerMidYearAssessment", "title": "Partner Mid-Year Assessment", "enableIf": "{role} != 'Reviewer' and {role} != 'ELT' and ({state} = 'midtermreviewing' or {state} = 'midtermreviewed')", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{cviPartnerMidYearAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}, {"type": "panel", "name": "cviReviewerMidYearGroup", "title": "Reviewer Mid-Year Review", "startWithNewLine": false, "elements": [{"type": "dropdown", "name": "cviReviewerMidYearAssessment", "title": "Reviewer Mid-Year Assessment", "enableIf": "{role} != 'ELT' and {role} != 'Partner' and ({state} = 'midtermreviewing' or {state} = 'midtermreviewed')", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{cviReviewerMidYearAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}]}, {"type": "panel", "name": "cviYearEndReview", "visibleIf": "{state} = 'yearendreviewing' or {state} = 'yearendreviewed'", "title": "Year-End Review for Client Value Index", "description": "- Year-End Review: Provide assessments and update comments for this KPI.\n", "state": "expanded", "elements": [{"type": "text", "name": "cviYearEndYtd", "title": "Year-End YTD", "defaultValue": "#N/A", "readOnly": true}, {"type": "panel", "name": "cviPartnerYearEndGroup", "title": "Partner Year-End Review", "elements": [{"type": "dropdown", "name": "cviPartnerYearEndAssessment", "title": "Partner Year-End Assessment", "enableIf": "{role} != 'Reviewer' and {role} != 'ELT'", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{cviPartnerYearEndAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}, {"type": "panel", "name": "cviReviewerYearEndGroup", "title": "Reviewer Year-End Review", "startWithNewLine": false, "elements": [{"type": "dropdown", "name": "cviReviewerYearEndAssessment", "title": "Reviewer Year-End Assessment", "enableIf": "{role} != 'Partner' and {role} != 'ELT'", "isRequired": true, "validators": [{"type": "expression", "text": "Update comment is required", "expression": "{cviReviewerYearEndAssessment-comment} notempty"}], "showCommentArea": true, "commentText": "Update Comment", "choices": ["Exceeds Expectations", "Meets Expectations", "Partially Meets Expectations", "Does Not Meet Expectations"]}]}]}]}]}, {"name": "settingsPage", "visible": false, "title": "Settings", "elements": [{"type": "panel", "name": "settings", "title": "Form Settings", "elements": [{"type": "dropdown", "name": "state", "title": "Form State", "defaultValue": "inprogress", "choices": ["inprogress", "submitted", "midtermreviewing", "midtermreviewed", "yearendreviewing", "yearendreviewed"]}, {"type": "dropdown", "name": "role", "title": "User Role", "defaultValue": "Partner", "choices": ["Partner", "Reviewer", "Admin", "ELT"]}, {"type": "text", "name": "currentYear", "title": "Year", "defaultValueExpression": "2025"}, {"type": "text", "name": "previousYear", "title": "Previous Year", "setValueExpression": "{currentYear} - 1"}, {"type": "text", "name": "mode", "title": "mode"}]}]}], "showPrevButton": false, "showTitle": false, "progressBarLocation": "bottom", "progressBarShowPageTitles": true, "checkErrorsMode": "onComplete", "completeText": "Submit", "showPreviewBeforeComplete": true, "headerView": "advanced"}