﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="4.0">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <Name>BdoPartner.Plans.Database</Name>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectVersion>4.1</ProjectVersion>
    <ProjectGuid>{996c1b0a-31a4-420a-9eae-66648dc0bdd9}</ProjectGuid>
    <DSP>Microsoft.Data.Tools.Schema.Sql.SqlAzureV12DatabaseSchemaProvider</DSP>
    <OutputType>Database</OutputType>
    <RootPath>
    </RootPath>
    <RootNamespace>BdoPartner.Plans.Database</RootNamespace>
    <AssemblyName>BdoPartner.Plans.Database</AssemblyName>
    <ModelCollation>1033, CI</ModelCollation>
    <DefaultFileStructure>BySchemaAndSchemaType</DefaultFileStructure>
    <DeployToDatabase>True</DeployToDatabase>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <TargetLanguage>CS</TargetLanguage>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <SqlServerVerification>False</SqlServerVerification>
    <IncludeCompositeObjects>True</IncludeCompositeObjects>
    <TargetDatabaseSet>True</TargetDatabaseSet>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <OutputPath>bin\</OutputPath>
    <BuildScriptName>$(MSBuildProjectName).sql</BuildScriptName>
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <OutputPath>bin\</OutputPath>
    <BuildScriptName>$(MSBuildProjectName).sql</BuildScriptName>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">11.0</VisualStudioVersion>
    <!-- Default to the v11.0 targets path if the targets file for the current VS version is not found -->
    <SSDTExists Condition="Exists('$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\SSDT\Microsoft.Data.Tools.Schema.SqlTasks.targets')">True</SSDTExists>
    <VisualStudioVersion Condition="'$(SSDTExists)' == ''">11.0</VisualStudioVersion>
  </PropertyGroup>
  <Import Condition="'$(SQLDBExtensionsRefPath)' != ''" Project="$(SQLDBExtensionsRefPath)\Microsoft.Data.Tools.Schema.SqlTasks.targets" />
  <Import Condition="'$(SQLDBExtensionsRefPath)' == ''" Project="$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\SSDT\Microsoft.Data.Tools.Schema.SqlTasks.targets" />
  <ItemGroup>
    <Folder Include="Properties" />
    <Folder Include="dbo" />
    <Folder Include="BaseDataSetup" />
    <Folder Include="audit" />
    <Folder Include="scrtips" />
  </ItemGroup>
  <ItemGroup>
    <Build Include="dbo\Language.sql" />
    <Build Include="audit\audit.sql" />
    <Build Include="audit\DataAudit.sql" />
    <Build Include="dbo\Notification.sql" />
    <Build Include="dbo\Form.sql" />
    <Build Include="dbo\Partner.sql" />
    <Build Include="dbo\Questionnaire.sql" />
    <Build Include="dbo\UserAnswer.sql" />
    <Build Include="dbo\FormStatus.sql" />
    <Build Include="dbo\QuestionnaireStatus.sql" />
    <Build Include="dbo\PartnerReviewerUpload.sql" />
    <Build Include="dbo\PartnerReviewerUploadDetails.sql" />
    <Build Include="dbo\PartnerReviewer.sql" />
    <Build Include="dbo\PartnerReferenceDataMeta.sql" />
    <Build Include="dbo\PartnerReferenceDataMetaDetails.sql" />
    <Build Include="dbo\PartnerReferenceDataUpload.sql" />
    <Build Include="dbo\PartnerReferenceDataUploadDetails.sql" />
    <Build Include="dbo\PartnerReferenceData.sql" />
    <None Include="scrtips\MergePartnersFromTestData.sql" />
    <PostDeploy Include="BaseDataSetup\MasterPostDeployment.sql" />
  </ItemGroup>
  <ItemGroup>
    <RefactorLog Include="BdoPartner.Plans.Database.refactorlog" />
  </ItemGroup>
  <ItemGroup>
    <None Include="BaseDataSetup\BaseData.PostDeployment.sql" />
    <None Include="BaseDataSetup\Questionnaire.PostDeployment.sql" />
  </ItemGroup>
  <ItemGroup>
    <None Include="BdoPartner.Plans.Database.publish.xml" />
  </ItemGroup>
</Project>