﻿-- Corporate with DataAccess.Common Audit functions to tracking specified table record's modification 
-- when commit update through Entity Framework "SaveChange".
CREATE TABLE [audit].[DataAudit]
(
	[Id] [bigint] IDENTITY(1,1) NOT NULL,
	[AuditGroupId] [uniqueidentifier] NOT NULL,
	[EntityState] [int] NOT NULL,
	[TableName] [nvarchar](50) NOT NULL,
	[PrimaryKeyField] [nvarchar](50) NULL,
	[PrimaryKeyValue] [nvarchar](50) NULL,
	[FieldName] [nvarchar](50) NOT NULL,
	[OriginalValue] [nvarchar](max) NULL,
	[CurrentValue] [nvarchar](max) NULL,
	[CreatedOn] [datetime2](7) NOT NULL DEFAULT getutcdate(),
	[CreatedBy] [nvarchar](50) NULL, 
    CONSTRAINT [PK_DataAudit] PRIMARY KEY ([Id]),
)
