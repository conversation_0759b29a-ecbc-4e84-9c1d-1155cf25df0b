﻿-- Form table is palce to store the form data submitted by partners.
CREATE TABLE [dbo].[Form]
(
	[Id] UNIQUEIDENTIFIER NOT NULL DEFAULT newsequentialid(), 
    [QuestionnaireId] UNIQUEIDENTIFIER NOT NULL, -- Refer to Questionnaire table. It is the most current form template that the partner filled out. 
    [Year] SMALLINT NOT NULL, 
    [Comments] NVARCHAR(max) NULL, 
    [PartnerObjectId] NVARCHAR(100) NULL, -- Refer to Partner end user's Azure AD Object ID.  
    [PartnerUserId] uniqueidentifier NULL, -- Refer to Partner's user Id in Identity database. 
    [PartnerSubmittionDate] DATETIME2 NULL, 
    [PartnerName] NVARCHAR(100) NULL,  -- Duplicate the partner name here for easy access and tracking.
    [PartnerEmail] nvarchar(100) null, -- Duplicate the partner email here for easy access and tracking.
    [Pdf] VARBINARY(MAX) NULL, -- Keey current user's form PDF file binary data here. It is mapping the user answer data filled in the form. 
    [Status] TINYINT NOT NULL DEFAULT 0, -- 0: Draft, 1: Submitted, 2: Approved, 3: Rejected, 4: Reopened, 5: Closed. 
    [IsActive] BIT NOT NULL DEFAULT 1, 
    [CreatedBy] UNIQUEIDENTIFIER NULL , 
    [CreatedByName] NVARCHAR(100) NULL, -- Store the email of the user who created the form. It is used to track who created the form.
    [CreatedOn] DATETIME2 NULL DEFAULT getutcdate(),    
    [ModifiedBy] UNIQUEIDENTIFIER NULL, 
    [ModifiedByName] NVARCHAR(100) NULL, -- Store the email of the user who modified the form last time.
    [ModifiedOn] DATETIME2 NULL,
  CONSTRAINT [PK_Form] PRIMARY KEY ([Id]), 
    CONSTRAINT [FK_Form_Questionnaire] FOREIGN KEY ([QuestionnaireId]) REFERENCES [Questionnaire]([Id]),
    CONSTRAINT [FK_Form_Status] FOREIGN KEY ([Status]) REFERENCES [FormStatus]([Id]) 
)
