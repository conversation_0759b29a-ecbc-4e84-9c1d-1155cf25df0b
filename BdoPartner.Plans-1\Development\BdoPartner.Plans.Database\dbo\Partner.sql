CREATE TABLE [dbo].[Partner]
(
	[Id] UNIQUEIDENTIFIER NOT NULL DEFAULT newsequentialid() , 
    [EmployeeId] INT NULL,        
    [FirstName] NVARCHAR(150) NULL,
    [LastName] NVARCHAR(150) NULL,
    [DisplayName] NVARCHAR(300) NULL,
    [DOB] Date NULL,
    [Mail] NVARCHAR(200) NULL,
    [PartnerType] NVARCHAR(100) NULL,
    [Department] NVARCHAR(200) NULL,
    [Location] NVARCHAR(200) NULL,
    [LocationId] NVARCHAR(200) NULL,
    [WGroup] NVARCHAR(200) NULL,
    [WGroupId] NVARCHAR(200) NULL,
    [ServiceLine] NVARCHAR(200) NULL,
    [ServiceLineId] NVARCHAR(200) NULL,
    [SubServiceLine] NVARCHAR(200) NULL,
    [SubServiceLineId] NVARCHAR(200) NULL,
	[IsActive] BIT NOT NULL DEFAULT 1, 
    [CreatedBy] UNIQUEIDENTIFIER NULL , 
    [CreatedByName] NVARCHAR(100) NULL, -- Store the email of the user who created the partner record.
    [CreatedOn] DATETIME2 NULL DEFAULT getutcdate(), 
    [ModifiedBy] UNIQUEIDENTIFIER NULL, 
    [ModifiedByName] NVARCHAR(100) NULL, -- Store the email of the user who modified the partner record last time.
    [ModifiedOn] DATETIME2 NULL, 
	CONSTRAINT [PK_Partner] PRIMARY KEY ([Id]) 
)
