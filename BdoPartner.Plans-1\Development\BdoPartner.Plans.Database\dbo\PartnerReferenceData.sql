-- Final table for Partner Reference Data after validation and processing
CREATE TABLE [dbo].[PartnerReferenceData]
(
	[Id] UNIQUEIDENTIFIER NOT NULL DEFAULT newsequentialid(),
	[PartnerId] UNIQUEIDENTIFIER NOT NULL, -- Foreign key to Partner table
	[Year] SMALLINT NOT NULL, -- The year this data applies to (e.g., 2025)
	[Cycle] TINYINT NOT NULL, -- 0 = Planning, 1 = Mid Year Review, 2 = End Year Review
	[MetaId] UNIQUEIDENTIFIER NOT NULL, -- Foreign key to PartnerReferenceDataMeta table
	[Data] NVARCHAR(MAX) NULL, -- JSON string of the row data (mapping to PartnerReferenceDataMetaDetails.NormalizedColumnName as JSON object property names)
	[CreatedBy] UNIQUEIDENTIFIER NULL,
	[CreatedByName] NVARCHAR(100) NULL, -- Store the email of the user who created the reference data.
	[CreatedOn] DATETIME2 NULL DEFAULT getutcdate(),
	[ModifiedBy] UNIQUEIDENTIFIER NULL,
	[ModifiedByName] NVARCHAR(100) NULL, -- Store the email of the user who modified the reference data last time.
	[ModifiedOn] DATETIME2 NULL,
	CONSTRAINT [PK_PartnerReferenceData] PRIMARY KEY ([Id]),
	CONSTRAINT [FK_PartnerReferenceData_Partner] FOREIGN KEY ([PartnerId]) REFERENCES [Partner]([Id]),
	CONSTRAINT [FK_PartnerReferenceData_Meta] FOREIGN KEY ([MetaId]) REFERENCES [PartnerReferenceDataMeta]([Id]),
	-- Unique constraint to prevent duplicate data for same partner, year, cycle, and metadata
	CONSTRAINT [UK_PartnerReferenceData_Partner_Year_Cycle_Meta] UNIQUE ([PartnerId], [Year], [Cycle], [MetaId])
)
