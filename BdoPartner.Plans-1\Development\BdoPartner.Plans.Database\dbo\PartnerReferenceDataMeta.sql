-- Master table for Partner Reference Data Metadata
CREATE TABLE [dbo].[PartnerReferenceDataMeta]
(
	[Id] UNIQUEIDENTIFIER NOT NULL DEFAULT newsequentialid(),
	[FileName] NVARCHAR(500) NOT NULL, -- The file name of the template
	[Year] SMALLINT NOT NULL, -- The year this metadata applies to (e.g., 2025)
	[Cycle] TINYINT NOT NULL, -- 0 = Planning, 1 = Mid Year Review, 2 = End Year Review
	[IsActive] BIT NOT NULL DEFAULT 1, -- Whether this metadata is currently active
	[CreatedBy] UNIQUEIDENTIFIER NULL,
	[CreatedByName] NVARCHAR(100) NULL, -- Store the email of the user who created the metadata.
	[CreatedOn] DATETIME2 NULL DEFAULT getutcdate(),
	[ModifiedBy] UNIQUEIDENTIFIER NULL,
	[ModifiedByName] NVARCHAR(100) NULL, -- Store the email of the user who modified the metadata last time.
	[ModifiedOn] DATETIME2 NULL,
	CONSTRAINT [PK_PartnerReferenceDataMeta] PRIMARY KEY ([Id])
)
