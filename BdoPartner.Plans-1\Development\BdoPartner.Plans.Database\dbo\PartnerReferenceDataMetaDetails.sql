-- Child table for Partner Reference Data Column Metadata
CREATE TABLE [dbo].[PartnerReferenceDataMetaDetails]
(
	[Id] UNIQUEIDENTIFIER NOT NULL DEFAULT newsequentialid(),
	[MetaId] UNIQUEIDENTIFIER NOT NULL, -- Foreign key to PartnerReferenceDataMeta table
	[ColumnName] NVARCHAR(200) NOT NULL, -- Original column name from the import file
	[NormalizedColumnName] NVARCHAR(200) NOT NULL, -- Normalized column name (remove space, special characters, convert to lower case) for JSON property names
	[ColumnDataType] TINYINT NOT NULL, -- 0 = Text, 1 = Numeric, 2 = Blank/Default
	[ColumnOrder] SMALLINT NOT NULL, -- The order of the column in the import file
	[CreatedBy] UNIQUEIDENTIFIER NULL,
	[CreatedByName] NVARCHAR(100) NULL, -- Store the email of the user who created the metadata details.
	[CreatedOn] DATETIME2 NULL DEFAULT getutcdate(),
	[ModifiedBy] UNIQUEIDENTIFIER NULL,
	[ModifiedByName] NVARCHAR(100) NULL, -- Store the email of the user who modified the metadata details last time.
	[ModifiedOn] DATETIME2 NULL,
	CONSTRAINT [PK_PartnerReferenceDataMetaDetails] PRIMARY KEY ([Id]),
	CONSTRAINT [FK_PartnerReferenceDataMetaDetails_Meta] FOREIGN KEY ([MetaId]) REFERENCES [PartnerReferenceDataMeta]([Id])
)
