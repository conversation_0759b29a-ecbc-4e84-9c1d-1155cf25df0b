-- Master table for Partner Reference Data Uploads
CREATE TABLE [dbo].[PartnerReferenceDataUpload]
(
	[Id] UNIQUEIDENTIFIER NOT NULL DEFAULT newsequentialid(),
	[UploadFileName] NVARCHAR(500) NOT NULL, -- The file name of the uploaded file
	[FileContent] VARBINARY(MAX) NULL, -- The binary content of the uploaded file
	[Year] SMALLINT NOT NULL, -- The year this upload applies to (e.g., 2025)
	[Cycle] TINYINT NOT NULL, -- 0 = Planning, 1 = Mid Year Review, 2 = End Year Review
	[MetaId] UNIQUEIDENTIFIER NOT NULL, -- Foreign key to PartnerReferenceDataMeta table
	[Status] TINYINT NOT NULL DEFAULT 0, -- 0 = Uploading, 1 = Uploaded, 2 = Validating, 3 = ValidationPassed, 4 = ValidationFailed, 5 = Submitted
	[ValidationSummary] NVARCHAR(MAX) NULL, -- Summary of validation results
	[CreatedBy] UNIQUEIDENTIFIER NULL,
	[CreatedByName] NVARCHAR(100) NULL, -- Store the email of the user who created the upload record.
	[CreatedOn] DATETIME2 NULL DEFAULT getutcdate(),
	[ModifiedBy] UNIQUEIDENTIFIER NULL,
	[ModifiedByName] NVARCHAR(100) NULL, -- Store the email of the user who modified the upload record last time.
	[ModifiedOn] DATETIME2 NULL,
	CONSTRAINT [PK_PartnerReferenceDataUpload] PRIMARY KEY ([Id]),
	CONSTRAINT [FK_PartnerReferenceDataUpload_Meta] FOREIGN KEY ([MetaId]) REFERENCES [PartnerReferenceDataMeta]([Id])
)
