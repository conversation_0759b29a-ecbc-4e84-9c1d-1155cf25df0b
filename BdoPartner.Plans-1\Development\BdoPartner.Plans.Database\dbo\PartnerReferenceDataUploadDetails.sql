-- Staging table for Partner Reference Data Upload Details
CREATE TABLE [dbo].[PartnerReferenceDataUploadDetails]
(
	[Id] UNIQUEIDENTIFIER NOT NULL DEFAULT newsequentialid(),
	[PartnerReferenceDataUploadId] UNIQUEIDENTIFIER NOT NULL, -- Foreign key to PartnerReferenceDataUpload table
	[RowId] INT NOT NULL, -- The row number in the uploaded file (starting from 3, as row 1 is headers, row 2 is data types)
	[Data] NVARCHAR(MAX) NULL, -- JSON string of the row data (mapping to PartnerReferenceDataMetaDetails.NormalizedColumnName as JSON object property names)
	[ValidationError] NVARCHAR(500) NULL, -- Validation error message if any
	[CreatedBy] UNIQUEIDENTIFIER NULL,
	[CreatedByName] NVARCHAR(100) NULL, -- Store the email of the user who created the upload details.
	[CreatedOn] DATETIME2 NULL DEFAULT getutcdate(),
	[ModifiedBy] UNIQUEIDENTIFIER NULL,
	[ModifiedByName] NVARCHAR(100) NULL, -- Store the email of the user who modified the upload details last time.
	[ModifiedOn] DATETIME2 NULL,
	CONSTRAINT [PK_PartnerReferenceDataUploadDetails] PRIMARY KEY ([Id]),
	CONSTRAINT [FK_PartnerReferenceDataUploadDetails_Upload] FOREIGN KEY ([PartnerReferenceDataUploadId]) REFERENCES [PartnerReferenceDataUpload]([Id])
)
