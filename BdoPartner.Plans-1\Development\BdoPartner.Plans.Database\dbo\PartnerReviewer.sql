﻿CREATE TABLE [dbo].[Partner<PERSON><PERSON>iewer]
(
	[Id] UNIQUEIDENTIFIER NOT NULL  DEFAULT newsequentialid(),
	[Year] smallint not null,
	[PartnerId] UNIQUEIDENTIFIER NOT NULL, -- Refer to Partner table Id field.
	[Exempt] bit NOT NULL DEFAULT 0, -- 0: Not Exempt, 1: Exempt. If the partner is exempted from the review process, this field will be set to 1.
	[LeadershipRole] nvarchar(100) NULL, -- The leadership role codes of the partner, it could be multiple roles separated by comma. Example: SLT,SSLL
	[PrimaryReviewerId] UNIQUEIDENTIFIER NULL, -- Refer to Partner table Id field.
	[PrimaryReviewerName] NVARCHAR(500) NULL, -- Duplicate the reviewer name here for easy access and tracking.
    [SecondaryReviewerId] uniqueidentifier NULL, -- Refer to Partner table Id field.
	[SecondaryReviewerName] NVARCHAR(500) NULL, -- Duplicate the reviewer name here for easy access and tracking.
 	[CreatedBy] UNIQUEIDENTIFIER NULL , 
	[CreatedByName] NVARCHAR(100) NULL, -- Store the email of the user who created the reviewer record. It is used to track who created the reviewer.
    [CreatedOn] DATETIME2 NULL DEFAULT getutcdate(), 
    [ModifiedBy] UNIQUEIDENTIFIER NULL, 
	[ModifiedByName] NVARCHAR(100) NULL, -- Store the email of the user who modified the reviewer record last time.
    [ModifiedOn] DATETIME2 NULL, 
	CONSTRAINT [PK_PartnerReviewer] PRIMARY KEY ([Id]), 
    CONSTRAINT [AK_PartnerReviewer_PartnerYear] UNIQUE ([PartnerId], [Year]), 
    CONSTRAINT [FK_PartnerReviewer_Partner] FOREIGN KEY ([PartnerId]) REFERENCES [Partner]([Id]), 
    CONSTRAINT [FK_PartnerReviewer_PrimaryReviewer] FOREIGN KEY ([PrimaryReviewerId]) REFERENCES [Partner]([Id]), 
    CONSTRAINT [FK_PartnerReviewer_SecondaryReviewer] FOREIGN KEY ([SecondaryReviewerId]) REFERENCES [Partner]([Id])
)
