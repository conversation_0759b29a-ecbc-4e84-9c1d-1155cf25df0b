﻿-- Staging table for Partner Reviewer Uploads
CREATE TABLE [dbo].[PartnerReviewerUpload]
(
	[Id] INT NOT NULL Identity(1,1),
	[Years] NVARCHAR(100) not null, -- The upload excel file work for specified years, e.g., "2023,2024". When submit staging data to PartnerReviewer table, 
	                                -- the years will be split and inserted into PartnerReviewer table as separate rows, 
									-- apply the int value to "Year" column in PartnerReviewer table.
	[UploadFileName] NVARCHAR(500) NULL, -- The file name of the uploaded file.
	[ValidationSummary] NVARCHAR(Max) NULL, -- Comments or notes about the upload.
	[FileContent] VARBINARY(MAX) NULL, -- The binary content of the uploaded file.
	[Status] tinyint NOT NULL DEFAULT 0, -- 0 Uploading, 1 Uploaded, 2 Validating, 3 ValidationPassed, 4 ValidationFailed, 5 Submitted, 6 Cancelled
	[CreatedBy] UNIQUEIDENTIFIER NULL ,
	[CreatedByName] NVARCHAR(100) NULL, -- Store the email of the user who created the upload record.
    [CreatedOn] DATETIME2 NULL DEFAULT getutcdate(),
    [ModifiedBy] UNIQUEIDENTIFIER NULL,
	[ModifiedByName] NVARCHAR(100) NULL, -- Store the email of the user who modified the upload record last time.
    [ModifiedOn] DATETIME2 NULL,
	CONSTRAINT [PK_PartnerReviewerUpload] PRIMARY KEY ([Id])
)
