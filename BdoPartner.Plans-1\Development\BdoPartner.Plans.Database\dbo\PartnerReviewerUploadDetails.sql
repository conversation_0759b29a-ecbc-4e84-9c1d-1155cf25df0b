﻿CREATE TABLE [dbo].[PartnerReviewerUploadDetails]
(
	[Id] BIGINT NOT NULL Identity(1,1),
	[PartnerReviewerUploadId] INT NOT NULL, -- Foreign key to PartnerReviewerUpload table
	[RowId] int not null, -- The row id in the uploaded file, it is used to track the row in the uploaded file.
	[EmployeeId] NVARCHAR(50) NULL, -- Refer to Partner table EmployeeId field
	[EmployeeName] NVARCHAR(100) NULL,
	[Exempt] Nvarchar(10) null, -- Y / N
	[LeadershipRole] nvarchar(100) NULL, -- The leadership role codes of the partner, it could be multiple roles separated by comma. Example: SLT,SSLL
	[PrimaryReviewerId] nvarchar(50) null, -- Refer to Partner table EmployeeId field. It is primary reviewer's employee id.
	[PrimaryReviewerName] NVARCHAR(100) NULL, -- Duplicate the reviewer name here for easy access and tracking.
    [SecondaryReviewerId] nvarchar(50) NULL, -- Refer to Partner table EmployeeId field.
	[SecondaryReviewerName] NVARCHAR(100) NULL, -- Duplicate the reviewer name here for easy access and tracking.
	[ValidationError] NVARCHAR(500) NULL, -- Validation error message if any.
	[CreatedBy] UNIQUEIDENTIFIER NULL ,
	[CreatedByName] NVARCHAR(100) NULL, -- Store the email of the user who created the upload record.
    [CreatedOn] DATETIME2 NULL DEFAULT getutcdate(),
    [ModifiedBy] UNIQUEIDENTIFIER NULL,
	[ModifiedByName] NVARCHAR(100) NULL, -- Store the email of the user who modified the upload record last time.
    [ModifiedOn] DATETIME2 NULL,
	CONSTRAINT [PK_PartnerReviewerUploadDetails] PRIMARY KEY ([Id]),
	CONSTRAINT [FK_PartnerReviewerUploadDetails_PartnerReviewerUpload] FOREIGN KEY ([PartnerReviewerUploadId]) REFERENCES [PartnerReviewerUpload]([Id])
)
