﻿--
-- Keep Survey.js form's template definition
--
CREATE TABLE [dbo].[Questionnaire]
(
	[Id] UNIQUEIDENTIFIER NOT NULL DEFAULT newsequentialid(), -- Primary key.
    [Name] NVARCHAR(100) NOT NULL, -- Form template name.
    [Year] SMALLINT NOT NULL, -- Year of the form template. Used to identify the form template for a specific year.
    [DefinitionJson] NVARCHAR(MAX) NULL, -- Keep survey.js form's template JSON here. Finalize form definition.
    [DraftDefinitionJson] NVARCHAR(MAX) NULL, -- Keep survey.js form's template JSON here. Draft form definition. Work for drafting the form template and rollback. 
                                              -- If status = 0 (Draft), Rendering this template data in UI. 
                                              -- If status = 1 (Published), Rendering DefinitionJson data in UI.
                                              -- When click publish button, copy DraftDefinition<PERSON>son to Definition<PERSON>son and set status = 1 (Published).
    [FormSystemVersion] INT NOT NULL DEFAULT 0, -- Version of the current form template, incremented on each update. Information for tracking changes and rollback changes.
    [Acknowledgement] BIT NOT NULL DEFAULT 0, -- Controls whether the acknowledgement feature is enabled for a questionnaire
    [AcknowledgementText] NVARCHAR(1500) NULL, -- Contains the actual acknowledgement text that users must read and agree to. 
                                              -- Form Completion Flow: When a user completes a questionnaire, 
                                              -- if acknowledgement is enabled, a popup dialog appears showing the acknowledgement text.
                                              -- system ensures that partners provide proper legal consent and acknowledgement when submitting their questionnaire responses, 
                                              -- which is important for compliance with partnership agreements and privacy policies.
    [GeneralComments] BIT NOT NULL DEFAULT 0,  -- Controls whether the acknowl comments info feature is enabled for a questionnaire
    [GeneralCommentsText] NVARCHAR(max) NULL, -- Form Completion Dialog: When a user completes a questionnaire, if general comments are enabled, a section appears in the completion dialog
                                               -- Administrators can configure general comments when editing questionnaire
    [Status] TINYINT NOT NULL DEFAULT 0, -- 0: Draft, 1: Published, 2: Archived. Enumeration definition. refer to QuestionnaireStatus lookup table values.
    [IsActive] BIT NOT NULL DEFAULT 1, -- Is the questionnaire active or not. Allow temporary disable/enable the questionnaire.
    [CreatedBy] UNIQUEIDENTIFIER NULL , 
    [CreatedByName] NVARCHAR(100) NULL, -- Store the email of the user who created the questionnaire. It is used to track who created the questionnaire.
    [CreatedOn] DATETIME2 NULL DEFAULT getutcdate(), 
    [ModifiedBy] UNIQUEIDENTIFIER NULL, 
    [ModifiedByName] NVARCHAR(100) NULL, -- Store the email of the user who modified the questionnaire last time.
    [ModifiedOn] DATETIME2 NULL,
    CONSTRAINT [PK_Questionnaire] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Questionnaire_Status] FOREIGN KEY ([Status]) REFERENCES [QuestionnaireStatus]([Id]) 
)
