﻿-- User filled answer in survey.js form. One Form one JSON data for one use﻿
CREATE TABLE [dbo].[UserAnswer]
(
	[Id] UNIQUEIDENTIFIER NOT NULL DEFAULT newsequentialid(), 
    [FormId] UNIQUEIDENTIFIER Not NULL, -- Refer to Form table. Note: [Form] table works as parent to contain essential 
                                        -- information of the form, such as the year, comments, status, etc. 
                                        -- The [UserAnswer] table works as child to contain the user filled form data corporate with Survey.js form.
    [Answer] NVARCHAR(MAX) NULL, -- JSON data of the user filled form. 
    [IsActive] BIT NOT NULL DEFAULT 1, 
    [CreatedBy] UNIQUEIDENTIFIER NULL , 
    [CreatedByName] NVARCHAR(100) NULL, -- Store the email of the user who created the answer. It is used to track who created the answer.
    [CreatedOn] DATETIME2 NULL DEFAULT getutcdate(), 
    [ModifiedBy] UNIQUEIDENTIFIER NULL, 
    [ModifiedByName] NVARCHAR(100) NULL, -- Store the email of the user who modified the answer last time.
    [ModifiedOn] DATETIME2 NULL, 
    CONSTRAINT [PK_UserAnswer] PRIMARY KEY ([Id]), 
    CONSTRAINT [FK_UserAnswer_Form] FOREIGN KEY ([FormId]) REFERENCES [Form]([Id]), 
)

