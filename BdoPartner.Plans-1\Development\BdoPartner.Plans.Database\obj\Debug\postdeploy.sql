﻿MERGE INTO [dbo].[Language] AS Target
USING (VALUES
  (1,'en','English')
 ,(2,'fr','French')
) AS Source ([Id],[Code],[Name])
ON (Target.[Id] = Source.[Id])
WHEN MATCHED AND (Target.[Code] <> Source.[Code] OR Target.[Name] <> Source.[Name]) THEN
	UPDATE SET
		[Code] = Source.[Code], 
		[Name] = Source.[Name]
WHEN NOT MATCHED BY TARGET THEN
	INSERT([Id],[Code],[Name])
	VALUES(Source.[Id],Source.[Code],Source.[Name])
WHEN NOT MATCHED BY SOURCE THEN 
	DELETE;
GO

-- Delete RolePermission records for roles not in the source dataset
--DELETE [dbo].[RolePermission]

-- Delete UserRole records for roles not in the source dataset
--DELETE [dbo].[UserRole]

--MERGE INTO [dbo].[Role] AS Target
--USING (VALUES
--  (15, 'Partner Plans Administrator')
-- ,(3, 'Active Partner')
-- ,(16, 'New Partner')
-- ,(17, 'Partner Plans Executive Leadership')
--) AS Source ([Id],[Name])
--ON (Target.[Id] = Source.[Id])
--WHEN MATCHED AND (Target.[Name] <> Source.[Name]) THEN
--	UPDATE SET
--		[Name] = Source.[Name]
--WHEN NOT MATCHED BY TARGET THEN
--	INSERT([Id],[Name])
--	VALUES(Source.[Id],Source.[Name])
--WHEN NOT MATCHED BY SOURCE THEN
--	DELETE;
--GO


--MERGE INTO [dbo].[Permission] AS Target
--USING (VALUES
--  (6, 'Partner Plans Login')
-- ,(7, 'Track Own Partner Plan')
-- ,(8, 'Track All Partner Plans')
-- ,(9, 'Draft Submit Partner Plan')
-- ,(10, 'Edit Partner Plans Under Review')
-- ,(11, 'Partner Plans Final Submission')
-- ,(12, 'Mid End Year Self Assessment')
-- ,(13, 'Mid End Year Reviewer Assessment')
-- ,(14, 'View Submitted Partner Plans')
-- ,(15, 'Edit Submitted Partner Plans')
-- ,(16, 'Export Plan Data To Excel')
-- ,(17, 'Manage Partner Reviewer Relationships')
-- ,(18, 'Upload KPI Data')
-- ,(19, 'Edit Publish Input Form')
--) AS Source ([Id],[Name])
--ON (Target.[Id] = Source.[Id])
--WHEN MATCHED AND (Target.[Name] <> Source.[Name]) THEN
--	UPDATE SET
--		[Name] = Source.[Name]
--WHEN NOT MATCHED BY TARGET THEN
--	INSERT([Id],[Name])
--	VALUES(Source.[Id],Source.[Name])
--WHEN NOT MATCHED BY SOURCE THEN
--	DELETE;
--GO


---- password = "Password1"
--MERGE INTO [dbo].[User] AS Target
--USING (VALUES
--  ('BFD02B5C-2408-4870-8E1F-06C7A72187F6', 'APP', 'ppadmin',0x999000A49E8DD91ED5726EA0AF1745C8F712640D6563F7FDE1977013B730CB3C9D78F99EC07FC59B0ADEAB5CBA0380DA, 0xE73685F552,0,'PP','Administrator','<EMAIL>',1,1)
--  ,('D3FB7F6A-6487-4190-9D9F-0ECD7A25E239', 'APP','partner1',0x999000A49E8DD91ED5726EA0AF1745C8F712640D6563F7FDE1977013B730CB3C9D78F99EC07FC59B0ADEAB5CBA0380DA, 0xE73685F552,0,'Partner','User','<EMAIL>',1,1)
--  ,('B6BDCA35-4D96-4C26-B6B2-D15E285147DB', 'APP', 'newpartner',0x999000A49E8DD91ED5726EA0AF1745C8F712640D6563F7FDE1977013B730CB3C9D78F99EC07FC59B0ADEAB5CBA0380DA, 0xE73685F552,0,'New','Partner','<EMAIL>',1,1)
--  ,('A1B2C3D4-5E6F-7890-ABCD-EF1234567890', 'APP', 'ppexec',0x999000A49E8DD91ED5726EA0AF1745C8F712640D6563F7FDE1977013B730CB3C9D78F99EC07FC59B0ADEAB5CBA0380DA, 0xE73685F552,0,'PP Executive','Leadership','<EMAIL>',1,1)

--) AS Source ([Id], [AuthProviderId], [Username],[Password],[Salt],[IsTempPasswordEnabled],[FirstName],[LastName],[Email],[LanguageId],[IsActive])
--ON (Target.[Id] = Source.[Id])
--WHEN MATCHED AND (Target.[Username] <> Source.[Username] OR Target.[AuthProviderId]<> Source.[AuthProviderId] OR Target.[Password] <> Source.[Password] OR Target.[Salt] <> Source.[Salt] OR Target.[IsTempPasswordEnabled] <> Source.[IsTempPasswordEnabled] 
--OR Target.[FirstName] <> Source.[FirstName] OR Target.[LastName] <> Source.[LastName] OR Target.[Email] <> Source.[Email] 
--OR Target.[LanguageId] <> Source.[LanguageId] OR Target.[IsActive] <> Source.[IsActive] ) THEN
-- UPDATE SET
-- [Username] = Source.[Username], 
--[Password] = Source.[Password], 
--[Salt] = Source.[Salt], 
--[IsTempPasswordEnabled] = Source.[IsTempPasswordEnabled], 
--[FirstName] = Source.[FirstName], 
--[LastName] = Source.[LastName], 
--[Email] = Source.[Email], 
--[LanguageId] = Source.[LanguageId], 
--[IsActive] = Source.[IsActive],
--[AuthProviderId] = Source.[AuthProviderId]
--WHEN NOT MATCHED BY TARGET THEN
-- INSERT([Id],[AuthProviderId],[Username],[Password],[Salt],[IsTempPasswordEnabled],[FirstName],[LastName],[Email],[LanguageId],[IsActive])
-- VALUES(Source.[Id],Source.[AuthProviderId], Source.[Username],Source.[Password],Source.[Salt],Source.[IsTempPasswordEnabled],Source.[FirstName],Source.[LastName],Source.[Email],Source.[LanguageId],Source.[IsActive]);
---- WHEN NOT MATCHED BY SOURCE THEN 
---- DELETE;
--GO

-- MERGE INTO [dbo].[UserRole] AS Target
--USING (VALUES
--   ('BFD02B5C-2408-4870-8E1F-06C7A72187F6', 15)  -- PP Administrator
--  ,('D3FB7F6A-6487-4190-9D9F-0ECD7A25E239', 3)  -- Active Partner
--  ,('B6BDCA35-4D96-4C26-B6B2-D15E285147DB', 16)  -- New Partner
--  ,('A1B2C3D4-5E6F-7890-ABCD-EF1234567890', 17)  -- PP Executive Leadership
--) AS Source ([UserId],[RoleId])
--ON (Target.[RoleId] = Source.[RoleId] and Target.[UserId] = Source.[UserId])
----WHEN MATCHED AND (Target.[Name] <> Source.[Name]) THEN
----	UPDATE SET
----		[Name] = Source.[Name]		
--WHEN NOT MATCHED BY TARGET THEN
--	INSERT([UserId], [RoleId])
--	VALUES(Source.[UserId], Source.[RoleId]);
----WHEN NOT MATCHED BY SOURCE THEN 
----	DELETE;
--GO

-- FormStatus lookup table seed data
MERGE INTO [dbo].[FormStatus] AS Target
USING (VALUES
  (0, 'Draft', 'Draft', 'Brouillon')
 ,(1, 'Submitted', 'Submitted', 'Soumis')
 ,(2, 'Approved', 'Approved', 'Approuvé')
 ,(3, 'Rejected', 'Rejected', 'Rejeté')
 ,(4, 'Reopened', 'Reopened', 'Rouvert')
 ,(5, 'Closed', 'Closed', 'Fermé')
) AS Source ([Id],[Name],[EnglishDislayName],[FrenchDisplayName])
ON (Target.[Id] = Source.[Id])
WHEN MATCHED AND (Target.[Name] <> Source.[Name] OR Target.[EnglishDislayName] <> Source.[EnglishDislayName] OR Target.[FrenchDisplayName] <> Source.[FrenchDisplayName]) THEN
	UPDATE SET
		[Name] = Source.[Name],
		[EnglishDislayName] = Source.[EnglishDislayName],
		[FrenchDisplayName] = Source.[FrenchDisplayName]
WHEN NOT MATCHED BY TARGET THEN
	INSERT([Id],[Name],[EnglishDislayName],[FrenchDisplayName])
	VALUES(Source.[Id],Source.[Name],Source.[EnglishDislayName],Source.[FrenchDisplayName])
WHEN NOT MATCHED BY SOURCE THEN
	DELETE;
GO

-- QuestionnaireStatus lookup table seed data
MERGE INTO [dbo].[QuestionnaireStatus] AS Target
USING (VALUES
  (0, 'Draft', 'Draft', 'Brouillon')
 ,(1, 'Published', 'Published', 'Publié')
 ,(2, 'Archived', 'Archived', 'Archivé')
) AS Source ([Id],[Name],[EnglishDislayName],[FrenchDisplayName])
ON (Target.[Id] = Source.[Id])
WHEN MATCHED AND (Target.[Name] <> Source.[Name] OR Target.[EnglishDislayName] <> Source.[EnglishDislayName] OR Target.[FrenchDisplayName] <> Source.[FrenchDisplayName]) THEN
	UPDATE SET
		[Name] = Source.[Name],
		[EnglishDislayName] = Source.[EnglishDislayName],
		[FrenchDisplayName] = Source.[FrenchDisplayName]
WHEN NOT MATCHED BY TARGET THEN
	INSERT([Id],[Name],[EnglishDislayName],[FrenchDisplayName])
	VALUES(Source.[Id],Source.[Name],Source.[EnglishDislayName],Source.[FrenchDisplayName])
WHEN NOT MATCHED BY SOURCE THEN
	DELETE;
GO

-- Test data only - Insert notification messages if they don't already exist

-- Check and insert notification messages only if they don't exist
IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'Creating an inclusive environment where everyone can bring their genuine selves to work, participate fully and be positioned for success')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('Creating an inclusive environment where everyone can bring their genuine selves to work, participate fully and be positioned for success');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'Meet your Inclusion, Equity and Diversity Advisory Council')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('Meet your Inclusion, Equity and Diversity Advisory Council');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'During the month of June, Indigenous History Month, we have seen tragic events unfold � the lives of 215 residential school children found in BC and 751 unmarked graves found in Saskatchewan')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('During the month of June, Indigenous History Month, we have seen tragic events unfold � the lives of 215 residential school children found in BC and 751 unmarked graves found in Saskatchewan');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'Include land acknowledgements in your Outlook email signature')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('Include land acknowledgements in your Outlook email signature');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'Pride Season Event: We celebrated the kick-off to Pride Season on Thursday, June 17 with a special virtual Pride webcast. If you missed it, you can watch the webcast recording here')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('Pride Season Event: We celebrated the kick-off to Pride Season on Thursday, June 17 with a special virtual Pride webcast. If you missed it, you can watch the webcast recording here');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'Change your BDO Outlook photo: Let�s add some colour to our conversations')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('Change your BDO Outlook photo: Let�s add some colour to our conversations');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'Include your preferred pronoun in your Outlook email signature')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('Include your preferred pronoun in your Outlook email signature');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'BDO 100 Celebration')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('BDO 100 Celebration');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'The New IE&D Advisory Council')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('The New IE&D Advisory Council');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'Your Firm Engagement HUB')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('Your Firm Engagement HUB');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'NEW AND IMPROVED MY BDO!')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('NEW AND IMPROVED MY BDO!');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'Congratulations - Chris Diepdael, CMC�BC Rising Star Award')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('Congratulations - Chris Diepdael, CMC�BC Rising Star Award');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = '[CAMPAIGN LAUNCH] Selling your business: now live!')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('[CAMPAIGN LAUNCH] Selling your business: now live!');
END

GO
DECLARE @formId  NVARCHAR(255) = 'bdf37b4f-cc42-431a-a587-ac2ced9efc0b';
DECLARE @FormName NVARCHAR(255) = N'Partner Planning Form 2026';
DECLARE @form2025_version INT = 11;
DECLARE @form2025_ack NVARCHAR(MAX) = N'Acknowledgment text for Partner Planning Form 2025';
DECLARE @form2025_generalComments NVARCHAR(MAX) = N'General comments for Partner Planning Form 2025';

-- Combine pages into the final JSON, matching the specified root structure
declare @FinalJson nvarchar(MAX) =
N'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'

-- Check for existing form version
DECLARE @currentform_version INT = NULL;
SELECT @currentform_version = [FormSystemVersion] 
FROM [dbo].[Questionnaire] 
WHERE [Id] = @formId;

PRINT @currentform_version;
PRINT @form2025_version;

-- Insert or update the form in the Questionnaire table
IF @currentform_version IS NULL
BEGIN 
    INSERT INTO [dbo].[Questionnaire]
    (
      [Id], [Name], [Year], [Status], [IsActive], [Acknowledgement], [AcknowledgementText], [GeneralComments], [GeneralCommentsText],
      [CreatedOn], [ModifiedOn], [DefinitionJson], [DraftDefinitionJson], [FormSystemVersion]
    )
    VALUES
    (
      @formId, @FormName, 2025, 1, 1, 1, @form2025_ack, 1, @form2025_generalComments,
      GETUTCDATE(), GETUTCDATE(), @FinalJson, @FinalJson, @form2025_version
    );
END
ELSE IF (@currentform_version < @form2025_version) 
BEGIN
    UPDATE [dbo].[Questionnaire]
    SET
      [DefinitionJson] = @FinalJson,
      [DraftDefinitionJson] = @FinalJson,
      [Name] = @FormName,
      [Status] = 1,
      [IsActive] = 1,
      [Acknowledgement] = 1,
      [AcknowledgementText] = @form2025_ack,
      [GeneralComments] = 1,
      [GeneralCommentsText] = @form2025_generalComments,
      [FormSystemVersion] = @form2025_version,
      [ModifiedOn] = GETUTCDATE()
    WHERE
      [Id] = @formId;
END;


-- Verify the insertion/update
SELECT [Id], [Name], [Year], [FormSystemVersion], [CreatedOn], [ModifiedOn]
FROM [dbo].[Questionnaire]
WHERE [Id] = @formId;



GO
