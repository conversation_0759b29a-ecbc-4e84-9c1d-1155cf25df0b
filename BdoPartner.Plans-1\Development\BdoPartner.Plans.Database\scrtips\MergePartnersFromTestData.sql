-- =============================================
-- Script: Merge Partners from Test Data CSV
-- Description: Merges partner records from Partners_Test_Data.csv into Partner table
-- Created: 2025-07-25
-- =============================================

-- Create a temporary table to hold the CSV data
IF OBJECT_ID('tempdb..#TempPartners') IS NOT NULL
    DROP TABLE #TempPartners;

CREATE TABLE #TempPartners (
    EmployeeId INT,
    FirstName NVARCHAR(150),
    LastName NVARCHAR(150),
    DisplayName NVARCHAR(300),
    DOB DATE,
    Mail NVARCHAR(200),
    PartnerType NVARCHAR(100),
    Department NVARCHAR(200),
    Location NVARCHAR(200),
    LocationId NVARCHAR(200),
    WGroup NVARCHAR(200),
    WGroupId NVARCHAR(200),
    ServiceLine NVARCHAR(200),
    ServiceLineId NVARCHAR(200),
    SubServiceLine NVARCHAR(200),
    SubServiceLineId NVARCHAR(200),
    IsActive BIT,
    CreatedBy UNIQUEIDENTIFIER,
    CreatedOn DATETIME2,
    ModifiedBy UNIQUEIDENTIFIER,
    ModifiedOn DATETIME2
);

-- Insert the test data into temporary table
INSERT INTO #TempPartners (EmployeeId, FirstName, LastName, DisplayName, DOB, Mail, PartnerType, Department, Location, LocationId, WGroup, WGroupId, ServiceLine, ServiceLineId, SubServiceLine, SubServiceLineId, IsActive, CreatedBy, CreatedOn, ModifiedBy, ModifiedOn)
VALUES
(11, 'Stephen', 'Meek', 'Meek, Stephen', NULL, NULL, 'Partner - Income', NULL, 'Markham', NULL, NULL, NULL, 'A&A', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:42:54.9', NULL, NULL),
(12, 'James', 'Booth', 'Booth, James', NULL, NULL, 'Partner - Equity', NULL, 'Oakville', NULL, NULL, NULL, 'A&A', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:43:00.0', NULL, NULL),
(63, 'Michael', 'Jones', 'Jones, Michael', NULL, NULL, 'Partner - Equity', NULL, 'Barrie', NULL, NULL, NULL, 'Tax', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:43:06.0', NULL, NULL),
(67, 'Taylor', 'Gray', 'Gray, Taylor', NULL, NULL, 'Partner - Equity', NULL, 'Calgary - 8th Ave SW', NULL, NULL, NULL, 'A&A', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:43:12.0', NULL, NULL),
(68, 'Mario', 'Torre', 'Torre, Mario', NULL, NULL, 'Partner - Equity', NULL, 'Montreal', NULL, NULL, NULL, 'A&A', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:43:18.0', NULL, NULL),
(85, 'Sylvain', 'Guindon', 'Guindon, Sylvain', '1960-09-05', '<EMAIL>', 'Partner - Equity', 'Tax - Canadian Tax Services', 'Montreal', '1250_Montreal', NULL, NULL, 'Tax', NULL, 'Tax - Canadian Tax Services', NULL, 0, '00000000-0000-0000-0000-000000000000', '2025-01-01 15:33:50.9', '00000000-0000-0000-0000-000000000000', '2025-01-01 15:01:54.4'),
(89, 'Scott', 'Rodie', 'Rodie, Scott', '1962-11-08', '<EMAIL>', 'Partner - Equity', 'A&A - Assurance', 'Montreal', '1250_Montreal', NULL, NULL, 'A&A', NULL, 'A&A - Assurance', NULL, 0, '00000000-0000-0000-0000-000000000000', '2025-01-01 15:29:41.4', '00000000-0000-0000-0000-000000000000', '2025-01-01 15:04:32.8'),
(117, 'Sandra', 'Santos', 'Santos, Sandra', NULL, '<EMAIL>', NULL, 'A&A - Core', 'Montreal', '1250_Montreal', 'Community of Offices', 'LOCATION_HIERARCHY-3-179', 'A&A', 'CCH_A&A', 'A&A - Core', 'CUSTOM_ORGANIZATION-3-1234', 0, '00000000-0000-0000-0000-000000000000', '2025-01-01 15:29:07.2', NULL, NULL),
(120, 'Nazia', 'Lakhani', 'Lakhani, Nazia', '1970-04-18', '<EMAIL>', 'Partner - Equity', 'FWS - Firm A&A', 'Toronto - Wellington St', '1925_Toronto_-_Wellington_St', NULL, NULL, 'FWS', NULL, 'FWS - Firm A&A', NULL, 0, '00000000-0000-0000-0000-000000000000', '2025-01-01 15:28:28.3', '00000000-0000-0000-0000-000000000000', '2025-01-01 15:03:50.0'),
(125, 'Christian', 'Lapointe', 'Lapointe, Christian', '1975-07-20', '<EMAIL>', 'Partner - Equity', 'A&A - Assurance', 'Montreal - 1000 Rue De La Gauchetière Ouest', '1250_Montreal', NULL, NULL, 'A&A', NULL, 'A&A - Assurance', NULL, 0, '00000000-0000-0000-0000-000000000000', '2025-01-01 15:33:11.1', '00000000-0000-0000-0000-000000000000', '2025-01-01 15:03:50.1'),
(219, 'John', 'Wade', 'Wade, John', NULL, NULL, 'Partner - Income', NULL, 'Burlington', NULL, NULL, NULL, 'A&A', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:43:48.0', NULL, NULL),
(250, 'Brian', 'Clarence', 'Clarence, Brian', NULL, NULL, 'Partner - Income', NULL, 'Mississauga', NULL, NULL, NULL, 'A&A', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:44:00.0', NULL, NULL),
(255, 'Angus', 'Mumby', 'Mumby, Angus', NULL, NULL, 'Partner - Income', NULL, 'Sarnia', NULL, NULL, NULL, 'A&A', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:44:06.0', NULL, NULL),
(302, 'Joseph', 'Marinich', 'Marinich, Joseph', NULL, NULL, 'Partner - Income', NULL, 'Sault Ste. Marie', NULL, NULL, NULL, 'A&A', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:44:12.0', NULL, NULL),
(320, 'J.   Alex', 'Richardson', 'Richardson, J.   Alex', NULL, NULL, 'Partner - Income', NULL, 'Lindsay', NULL, NULL, NULL, 'A&A', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:44:18.0', NULL, NULL),
(323, 'Donald', 'Dafoe', 'Dafoe, Donald', NULL, NULL, 'Partner - Income', NULL, 'Sarnia', NULL, NULL, NULL, 'A&A', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:44:24.0', NULL, NULL),
(333, 'Werner', 'Penner', 'Penner, Werner', NULL, NULL, 'Partner - Income', NULL, 'Mississauga', NULL, NULL, NULL, 'A&A', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:44:30.0', NULL, NULL),
(339, 'Wayne', 'Etches', 'Etches, Wayne', NULL, NULL, 'Partner - Income', NULL, 'Lethbridge', NULL, NULL, NULL, 'A&A', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:44:36.0', NULL, NULL),
(342, 'Ken', 'Karakashian', 'Karakashian, Ken', '1960-01-01', '<EMAIL>', 'Partner - Equity', 'Tax - Canadian Tax Services', 'Markham', '3100_Toronto North', NULL, NULL, 'Tax', NULL, 'Tax - Canadian Tax Services', NULL, 0, '00000000-0000-0000-0000-000000000000', '2025-01-01 15:28:32.5', '00000000-0000-0000-0000-000000000000', '2025-01-01 15:03:50.0'),
(350, 'Dale', 'Hajdu', 'Hajdu, Dale', NULL, NULL, 'Partner - Income', NULL, 'Mississauga', NULL, NULL, NULL, 'A&A', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:44:42.0', NULL, NULL),
(359, 'John', 'Wilkey', 'Wilkey, John', NULL, NULL, 'Partner - Income', NULL, 'Revelstoke', NULL, NULL, NULL, 'A&A', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:44:48.0', NULL, NULL),
(366, 'Frank', 'Hutcheson', 'Hutcheson, Frank', NULL, NULL, 'Partner - Income', NULL, 'Burlington', NULL, NULL, NULL, 'A&A', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:44:54.0', NULL, NULL),
(367, 'Bruce', 'Nicholson', 'Nicholson, Bruce', NULL, NULL, 'Partner - Income', NULL, 'Burlington', NULL, NULL, NULL, 'A&A', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:45:00.0', NULL, NULL),
(369, 'Dan', 'Cremasco', 'Cremasco, Dan', NULL, NULL, 'Partner - Equity', NULL, 'Guelph - Hanlon Creek', NULL, NULL, NULL, 'A&A', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:45:06.0', NULL, NULL),
(373, 'Pierre', 'Vaillancourt', 'Vaillancourt, Pierre', NULL, NULL, 'Partner - Income', NULL, 'Alexandria', NULL, NULL, NULL, 'A&A', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:45:12.0', NULL, NULL),
(381, 'Thomas', 'Ambeault', 'Ambeault, Thomas', NULL, NULL, 'Partner - Income', NULL, 'Sault Ste. Marie', NULL, NULL, NULL, 'A&A', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:45:18.0', NULL, NULL),
(385, 'Gary', 'Munro', 'Munro, Gary', NULL, NULL, 'Partner - Income', NULL, 'Walkerton', NULL, NULL, NULL, 'Tax', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:45:24.0', NULL, NULL),
(388, 'Marco', 'Zuiani', 'Zuiani, Marco', NULL, NULL, 'Partner - Equity', NULL, 'Burlington', NULL, NULL, NULL, 'Tax', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:45:30.0', NULL, NULL),
(394, 'Harvey', 'Husk', 'Husk, Harvey', NULL, NULL, 'Partner - Income', NULL, 'Woodstock', NULL, NULL, NULL, 'A&A', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:45:30.0', NULL, NULL),
(395, 'Steven', 'Watson', 'Watson, Steven', NULL, NULL, 'Partner - Equity', NULL, 'Kincardine', NULL, NULL, NULL, 'A&A', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:45:36.0', NULL, NULL),
(424, 'Peter', 'Campbell', 'Campbell, Peter', NULL, NULL, 'Partner - Equity', NULL, 'Mississauga', NULL, NULL, NULL, 'A&A', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:45:42.0', NULL, NULL),
(429, 'Steven', 'Stein', 'Stein, Steven', NULL, NULL, 'Partner - Equity', NULL, 'Toronto - Bay St', NULL, NULL, NULL, 'A&A', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:46:06.0', NULL, NULL),
(434, 'Anthony', 'Tartaglia', 'Tartaglia, Anthony', NULL, NULL, 'Partner - Equity', NULL, 'Burlington', NULL, NULL, NULL, 'A&A', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:46:12.0', NULL, NULL),
(437, 'Robert', 'Wilkinson', 'Wilkinson, Robert', NULL, NULL, 'Partner - Equity', NULL, 'Walkerton', NULL, NULL, NULL, 'A&A', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:46:18.0', NULL, NULL),
(450, 'David', 'Saunders', 'Saunders, David', '1957-08-01', '<EMAIL>', 'Partner - Equity', 'Tax - International', 'Sault Ste. Marie', '2525_Sault Ste. Marie', NULL, NULL, 'Tax', NULL, 'Tax - International', NULL, 0, '00000000-0000-0000-0000-000000000000', '2025-01-01 15:34:25.4', '00000000-0000-0000-0000-000000000000', '2025-01-01 15:01:46.6'),
(463, 'Brad', 'Carnahan', 'Carnahan, Brad', '1959-02-01', '<EMAIL>', 'Partner - Income', 'Tax - Canadian Tax Services', 'Orillia', '2150_Orillia', NULL, NULL, 'Tax', NULL, 'Tax - Canadian Tax Services', NULL, 0, '00000000-0000-0000-0000-000000000000', '2025-01-01 15:34:09.3', '00000000-0000-0000-0000-000000000000', '2025-01-01 15:03:50.1'),
(468, 'Pierre', 'Bourgon', 'Bourgon, Pierre', '1958-10-23', '<EMAIL>', 'Partner - Equity', 'A&A - Assurance', 'Embrun', '2375_Embrun', NULL, NULL, 'A&A', NULL, 'A&A - Assurance', NULL, 0, '00000000-0000-0000-0000-000000000000', '2025-01-01 15:28:56.7', '00000000-0000-0000-0000-000000000000', '2025-01-01 15:01:46.5'),
(478, 'Ashif', 'Somani', 'Somani, Ashif', NULL, NULL, 'Partner - Equity', NULL, 'Markham', NULL, NULL, NULL, 'A&A', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:46:30.0', NULL, NULL),
(483, 'Allan', 'Rudolph', 'Rudolph, Allan', NULL, NULL, 'Partner - Equity', NULL, 'Markham', NULL, NULL, NULL, 'A&A', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:46:36.0', NULL, NULL),
(485, 'Clarke', 'McLeod', 'McLeod, Clarke', NULL, NULL, 'Partner - Equity', NULL, 'Owen Sound', NULL, NULL, NULL, 'Advisory', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:46:42.0', NULL, NULL),
(494, 'Jean-Paul', 'Lafrance', 'Lafrance, Jean-Paul', NULL, NULL, 'Partner - Income', NULL, 'Ottawa - St. Laurent Blvd', NULL, NULL, NULL, 'A&A', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:46:48.0', NULL, NULL),
(501, 'Rob', 'Thomas', 'Thomas, Rob', NULL, NULL, 'Partner - Income', NULL, 'Walkerton', NULL, NULL, NULL, 'A&A', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:46:54.0', NULL, NULL),
(503, 'Frank', 'Delzotto', 'Delzotto, Frank', NULL, NULL, 'Partner - Equity', NULL, 'Markham', NULL, NULL, NULL, 'A&A', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:47:00.0', NULL, NULL),
(511, 'Diane', 'Smith', 'Smith, Diane', NULL, NULL, 'Partner - Income', NULL, 'Woodstock', NULL, NULL, NULL, 'A&A', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:47:06.0', NULL, NULL),
(517, 'Patrick', 'Kramer', 'Kramer, Patrick', '1958-04-02', '<EMAIL>', 'Partner - Equity', 'FWS - Executive', 'Toronto - Wellington St', '1925_Toronto_-_Wellington_St', NULL, NULL, 'FWS', NULL, 'FWS - Executive', NULL, 0, '00000000-0000-0000-0000-000000000000', '2025-01-01 15:35:32.0', '00000000-0000-0000-0000-000000000000', '2025-01-01 15:01:46.7'),
(520, 'Paul', 'Taylor', 'Taylor, Paul', NULL, NULL, 'Partner - Equity', NULL, 'Orillia', NULL, NULL, NULL, 'Tax', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:47:18.0', NULL, NULL),
(530, 'Randy', 'Hickey', 'Hickey, Randy', NULL, NULL, 'Partner - Equity', NULL, 'Uxbridge', NULL, NULL, NULL, 'A&A', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:47:24.0', NULL, NULL),
(531, 'Janet', 'Stockton', 'Stockton, Janet', '1958-10-03', '<EMAIL>', 'Partner - Fixed Equity', 'FWS - Firm A&A', 'Toronto - Wellington St', '1925_Toronto_-_Wellington_St', NULL, NULL, 'FWS', NULL, 'FWS - Firm A&A', NULL, 0, '00000000-0000-0000-0000-000000000000', '2025-01-01 15:27:32.1', '00000000-0000-0000-0000-000000000000', '2025-01-01 15:03:50.0'),
(537, 'Len', 'Vandenberg', 'Vandenberg, Len', NULL, NULL, 'Partner - Income', NULL, 'Kelowna', NULL, NULL, NULL, 'Tax', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:47:30.0', NULL, NULL),
(540, 'Kent', 'Botham', 'Botham, Kent', '1960-04-18', '<EMAIL>', 'Partner - Equity', 'A&A - Assurance', 'Markham', '3100_Toronto North', NULL, NULL, 'A&A', NULL, 'A&A - Assurance', NULL, 0, '00000000-0000-0000-0000-000000000000', '2025-01-01 15:31:22.3', '00000000-0000-0000-0000-000000000000', '2025-01-01 15:03:50.0'),
(544, 'Brian', 'Pritchard', 'Pritchard, Brian', NULL, '<EMAIL>', NULL, 'Adv - PDS - Personal Debt Solutions', 'Oshawa', '2175_Oshawa', 'Community of Offices', 'LOCATION_HIERARCHY-3-179', 'Advisory', 'CCH_Advisory', 'Adv - PDS', 'CCH_Adv - FRS', 0, '00000000-0000-0000-0000-000000000000', '2025-01-01 15:27:02.8', NULL, NULL),
(552, 'Michael', 'Laycock', 'Laycock, Michael', NULL, NULL, 'Partner - Income', NULL, 'Barrie', NULL, NULL, NULL, 'Tax', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:47:36.0', NULL, NULL),
(558, 'Douglas', 'Johnston', 'Johnston, Douglas', NULL, NULL, 'Partner - Equity', NULL, 'Sarnia', NULL, NULL, NULL, 'A&A', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:47:42.0', NULL, NULL),
(559, 'Peter', 'Smith', 'Smith, Peter', NULL, NULL, 'Partner - Equity', NULL, 'Dryden', NULL, NULL, NULL, 'A&A', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:47:48.0', NULL, NULL),
(561, 'John', 'Hunt', 'Hunt, John', NULL, NULL, 'Partner - Equity', NULL, 'Hanover', NULL, NULL, NULL, 'A&A', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:47:54.0', NULL, NULL),
(566, 'Peter', 'Schwarzl', 'Schwarzl, Peter', NULL, NULL, 'Partner - Equity', NULL, 'Huntsville', NULL, NULL, NULL, 'A&A', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:48:00.0', NULL, NULL),
(570, 'Vince', 'Siciliano', 'Siciliano, Vince', '1959-01-22', '<EMAIL>', 'Partner - Equity', 'Adv - FAS - Business Restructuring and Turnaround Services', 'Oakville', '4875_Oakville', NULL, NULL, 'Advisory', NULL, 'Adv - FAS', NULL, 0, '00000000-0000-0000-0000-000000000000', '2025-01-01 15:26:29.3', '00000000-0000-0000-0000-000000000000', '2025-01-01 15:01:54.1'),
(574, 'Claudine', 'Cordeiro', 'Cordeiro, Claudine', '1961-09-16', '<EMAIL>', 'Partner - Equity', 'A&A - Assurance', 'Kenora', '1150_Kenora', NULL, NULL, 'A&A', NULL, 'A&A - Assurance', NULL, 0, '00000000-0000-0000-0000-000000000000', '2025-01-01 15:34:15.5', '00000000-0000-0000-0000-000000000000', '2025-01-01 15:01:46.6'),
(589, 'Ross', 'Park', 'Park, Ross', '1964-01-29', '<EMAIL>', 'Partner - Equity', 'A&A - Assurance', 'Oakville', '4875_Oakville', NULL, NULL, 'A&A', NULL, 'A&A - Assurance', NULL, 0, '00000000-0000-0000-0000-000000000000', '2025-01-01 15:26:37.5', '00000000-0000-0000-0000-000000000000', '2025-01-01 15:03:49.9'),
(592, 'Ian', 'Crockford', 'Crockford, Ian', NULL, '<EMAIL>', NULL, 'A&A - Core', 'Lindsay', '2100_Lindsay', 'Community of Offices', 'LOCATION_HIERARCHY-3-179', 'A&A', 'CCH_A&A', 'A&A - Core', 'CUSTOM_ORGANIZATION-3-1234', 0, '00000000-0000-0000-0000-000000000000', '2025-01-01 15:27:43.0', NULL, NULL),
(593, 'Dale', 'Williams', 'Williams, Dale', NULL, NULL, 'Partner - Income', NULL, 'Burlington', NULL, NULL, NULL, 'A&A', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:48:12.0', NULL, NULL),
(594, 'Michael', 'Machon', 'Machon, Michael', NULL, NULL, 'Partner - Equity', NULL, 'Cobourg', NULL, NULL, NULL, 'A&A', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:48:18.0', NULL, NULL),
(595, 'Erica', 'Teklits', 'Teklits, Erica', NULL, NULL, 'Partner - Equity', NULL, 'Oakville', NULL, NULL, NULL, 'A&A', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:48:24.0', NULL, NULL),
(601, 'Stephen', 'Spiers', 'Spiers, Stephen', NULL, '<EMAIL>', NULL, 'A&A - Core', 'Toronto - Bay St', '2975_Toronto_-_Bay_St', 'Community of Offices', 'LOCATION_HIERARCHY-3-179', 'A&A', 'CCH_A&A', 'A&A - Core', 'CUSTOM_ORGANIZATION-3-1234', 0, '00000000-0000-0000-0000-000000000000', '2025-01-01 15:28:58.9', NULL, NULL),
(603, 'David', 'Eckert', 'Eckert, David', NULL, NULL, 'Partner - Income', NULL, 'Wiarton', NULL, NULL, NULL, 'A&A', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:48:30.0', NULL, NULL),
(604, 'Mike', 'Vriend', 'Vriend, Mike', '1968-12-17', '<EMAIL>', 'Partner - Equity', 'A&A - Assurance', 'Oakville', '4875_Oakville', NULL, NULL, 'A&A', NULL, 'A&A - Assurance', NULL, 0, '00000000-0000-0000-0000-000000000000', '2025-01-01 15:31:55.7', '00000000-0000-0000-0000-000000000000', '2025-01-01 15:03:50.1'),
(607, 'Michael', 'Bolton', 'Bolton, Michael', NULL, NULL, 'Partner - Equity', NULL, 'Port Elgin', NULL, NULL, NULL, 'A&A', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:48:36.0', NULL, NULL),
(610, 'Rob', 'Wilkes', 'Wilkes, Rob', '1966-03-08', '<EMAIL>', 'Partner - Equity', 'A&A - Assurance', 'Oakville', '4875_Oakville', NULL, NULL, 'A&A', NULL, 'A&A - Assurance', NULL, 0, '00000000-0000-0000-0000-000000000000', '2025-01-01 15:27:19.4', '00000000-0000-0000-0000-000000000000', '2025-01-01 15:03:49.9'),
(613, 'Kevin', 'Kieffer', 'Kieffer, Kevin', '1964-11-20', '<EMAIL>', 'Partner - Equity', 'A&A - Assurance', 'Mount Forest', '1500_Mount Forest', NULL, NULL, 'A&A', NULL, 'A&A - Assurance', NULL, 0, '00000000-0000-0000-0000-000000000000', '2025-01-01 15:35:21.3', '00000000-0000-0000-0000-000000000000', '2025-01-01 15:03:50.2'),
(630, 'David', 'Richardson', 'Richardson, David', '1959-05-20', '<EMAIL>', 'Partner - Equity', 'Tax - Canadian Tax Services', 'North Bay - McIntyre Street West', '2125_North Bay', NULL, NULL, 'Tax', NULL, 'Tax - Canadian Tax Services', NULL, 0, '00000000-0000-0000-0000-000000000000', '2025-01-01 15:31:51.4', '00000000-0000-0000-0000-000000000000', '2025-01-01 15:03:50.1'),
(632, 'Joseph', 'Gipp', 'Gipp, Joseph', NULL, NULL, 'Partner - Income', NULL, 'Toronto - Bay St', NULL, NULL, NULL, 'A&A', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:48:42.0', NULL, NULL),
(633, 'James', 'Corbett', 'Corbett, James', NULL, NULL, 'Partner - Equity', NULL, 'Kenora', NULL, NULL, NULL, 'A&A', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:48:54.0', NULL, NULL),
(637, 'Dianne', 'McMullen', 'McMullen, Dianne', NULL, NULL, 'Partner - Income', NULL, 'Toronto - Bay St', NULL, NULL, NULL, 'Tax', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:48:54.0', NULL, NULL),
(640, 'Jeff', 'Watson', 'Watson, Jeff', NULL, NULL, 'Partner - Equity', NULL, 'Guelph - Hanlon Creek', NULL, NULL, NULL, 'A&A', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:49:00.0', NULL, NULL),
(650, 'Mark', 'Chow', 'Chow, Mark', NULL, NULL, 'Partner - Equity', NULL, 'Toronto - Wellington St', NULL, NULL, NULL, 'Advisory', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:49:06.0', NULL, NULL),
(651, 'Barry', 'Heaney', 'Heaney, Barry', '1972-04-28', '<EMAIL>', 'Partner - Equity', 'A&A - Assurance', 'Hanover', '1450_Hanover', NULL, NULL, 'A&A', NULL, 'A&A - Assurance', NULL, 0, '00000000-0000-0000-0000-000000000000', '2025-01-01 15:31:28.4', '00000000-0000-0000-0000-000000000000', '2025-01-01 15:03:50.0'),
(659, 'Giselle', 'Bodkin', 'Bodkin, Giselle', '1962-09-11', '<EMAIL>', 'Partner - Equity', 'A&A - Assurance', 'Barrie', '1975_Barrie', NULL, NULL, 'A&A', NULL, 'A&A - Assurance', NULL, 0, '00000000-0000-0000-0000-000000000000', '2025-01-01 15:33:39.9', '00000000-0000-0000-0000-000000000000', '2025-01-01 15:03:50.1'),
(660, 'Tom', 'Murray', 'Murray, Tom', '1964-06-09', '<EMAIL>', 'Partner - Equity', 'Tax - Canadian Tax Services', 'Sault Ste. Marie', '2525_Sault Ste. Marie', NULL, NULL, 'Tax', NULL, 'Tax - Canadian Tax Services', NULL, 0, '00000000-0000-0000-0000-000000000000', '2025-01-01 15:34:10.5', '00000000-0000-0000-0000-000000000000', '2025-01-01 15:03:50.1'),
(670, 'Gilles', 'Roy', 'Roy, Gilles', '1973-07-25', '<EMAIL>', 'Partner - Equity', 'Tax - Canadian Tax Services', 'Ottawa - Coventry Rd', '5050_Ottawa_Coventry_Rd', NULL, NULL, 'Tax', NULL, 'Tax - Canadian Tax Services', NULL, 0, '00000000-0000-0000-0000-000000000000', '2025-01-01 15:26:51.0', '00000000-0000-0000-0000-000000000000', '2025-01-01 15:03:49.9'),
(673, 'Rino', 'Bellavia', 'Bellavia, Rino', NULL, NULL, 'Partner - Equity', NULL, 'Burlington', NULL, NULL, NULL, 'Tax', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:49:12.0', NULL, NULL),
(676, 'Dom', 'Cocco', 'Cocco, Dom', NULL, '<EMAIL>', NULL, 'Tax - International', 'Oakville', '4875_Oakville', 'Community of Offices', 'LOCATION_HIERARCHY-3-179', 'Tax', 'CCH_Tax', 'Tax - International', 'CCH_Tax - International', 0, '00000000-0000-0000-0000-000000000000', '2025-01-01 15:27:50.4', NULL, NULL),
(677, 'John', 'Wonfor', 'Wonfor, John', '1961-12-15', '<EMAIL>', 'Partner - Equity', 'FWS - Tax', 'Toronto - Wellington St', '1925_Toronto_-_Wellington_St', NULL, NULL, 'FWS', NULL, 'FWS - Tax', NULL, 0, '00000000-0000-0000-0000-000000000000', '2025-01-01 15:31:42.8', '00000000-0000-0000-0000-000000000000', '2025-01-01 15:03:50.0'),
(678, 'Tim', 'Taylor', 'Taylor, Tim', '1964-11-27', '<EMAIL>', 'Partner - Equity', 'Tax - Canadian Tax Services', 'Barrie', '1975_Barrie', NULL, NULL, 'Tax', NULL, 'Tax - Canadian Tax Services', NULL, 0, '00000000-0000-0000-0000-000000000000', '2025-01-01 15:35:11.7', '00000000-0000-0000-0000-000000000000', '2025-01-01 15:03:50.2'),
(689, 'James', 'Lockhart', 'Lockhart, James', NULL, NULL, 'Partner - Equity', NULL, 'Kenora', NULL, NULL, NULL, 'Tax', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:49:18.0', NULL, NULL),
(692, 'Cindy', 'Ditner', 'Ditner, Cindy', '1961-04-14', '<EMAIL>', 'Partner - Fixed Equity', 'FWS - Firm A&A', 'Toronto - Wellington St', '1925_Toronto_-_Wellington_St', NULL, NULL, 'FWS', NULL, 'FWS - Firm A&A', NULL, 0, '00000000-0000-0000-0000-000000000000', '2025-01-01 15:31:33.7', '00000000-0000-0000-0000-000000000000', '2025-01-01 15:01:46.6'),
(693, 'Sally', 'Slumskie', 'Slumskie, Sally', NULL, NULL, 'Partner - Equity', NULL, 'Orangeville', NULL, NULL, NULL, 'A&A', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:49:30.0', NULL, NULL),
(694, 'Dave', 'Kubinec', 'Kubinec, Dave', '1967-04-25', '<EMAIL>', 'Partner - Equity', 'A&A - Assurance', 'Thunder Bay', '2475_Thunder Bay', NULL, NULL, 'A&A', NULL, 'A&A - Assurance', NULL, 0, '00000000-0000-0000-0000-000000000000', '2025-01-01 15:27:35.4', '00000000-0000-0000-0000-000000000000', '2025-01-01 15:03:50.0'),
(696, 'Rob', 'Coburn', 'Coburn, Rob', NULL, NULL, 'Partner - Equity', NULL, 'Owen Sound', NULL, NULL, NULL, 'Tax', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:49:30.0', NULL, NULL),
(698, 'Linda', 'Bross', 'Bross, Linda', '1965-03-03', '<EMAIL>', 'Partner - Equity', 'A&A - Assurance', 'Hanover', '1450_Hanover', NULL, NULL, 'A&A', NULL, 'A&A - Assurance', NULL, 0, '00000000-0000-0000-0000-000000000000', '2025-01-01 15:34:24.2', '00000000-0000-0000-0000-000000000000', '2025-01-01 15:03:50.1'),
(700, 'Kurt', 'Oelschlagel', 'Oelschlagel, Kurt', '1964-07-16', '<EMAIL>', 'Partner - Equity', 'Tax - Canadian Tax Services', 'Hanover', '1450_Hanover', NULL, NULL, 'Tax', NULL, 'Tax - Canadian Tax Services', NULL, 0, '00000000-0000-0000-0000-000000000000', '2025-01-01 15:30:28.3', '00000000-0000-0000-0000-000000000000', '2025-01-01 15:03:50.0'),
(703, 'Doug', 'Jones', 'Jones, Doug', '1961-07-16', '<EMAIL>', 'Partner - Equity', 'Adv - PDS - Personal Debt Solutions', 'Barrie', '1975_Barrie', NULL, NULL, 'Advisory', NULL, 'Adv - PDS', NULL, 0, '00000000-0000-0000-0000-000000000000', '2025-01-01 15:29:59.6', '00000000-0000-0000-0000-000000000000', '2025-01-01 15:03:50.0'),
(705, 'Colin', 'Wilson', 'Wilson, Colin', '1962-11-29', '<EMAIL>', 'Partner - Income', 'A&A - Infinity - Financial Reporting & Insights', 'Oakville', '4875_Oakville', NULL, NULL, 'A&A', NULL, 'A&A - Infinity', NULL, 0, '00000000-0000-0000-0000-000000000000', '2025-01-01 15:28:31.5', '00000000-0000-0000-0000-000000000000', '2025-01-01 15:03:50.0'),
(708, 'Sandy', 'Hale', 'Hale, Sandy', '1963-08-08', '<EMAIL>', 'Partner - Equity', 'Tax - Canadian Tax Services', 'Oakville', '4875_Oakville', NULL, NULL, 'Tax', NULL, 'Tax - Canadian Tax Services', NULL, 0, '00000000-0000-0000-0000-000000000000', '2025-01-01 15:29:56.5', '00000000-0000-0000-0000-000000000000', '2025-01-01 15:03:50.0'),
(715, 'Douglas', 'Ball', 'Ball, Douglas', NULL, NULL, 'Partner - Equity', NULL, 'National', NULL, NULL, NULL, 'Tax', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:49:48.0', NULL, NULL),
(723, 'Eugene', 'Migus', 'Migus, Eugene', NULL, NULL, 'Partner - Equity', NULL, 'Mississauga - City Centre Drive', NULL, NULL, NULL, 'Advisory', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:49:54.0', NULL, NULL),
(727, 'Pete', 'Barnes', 'Barnes, Pete', '1964-10-29', '<EMAIL>', 'Partner - Income', 'A&A - Assurance', 'Sarnia', '2800_Sarnia', NULL, NULL, 'A&A', NULL, 'A&A - Assurance', NULL, 0, '00000000-0000-0000-0000-000000000000', '2025-01-01 15:29:24.1', '00000000-0000-0000-0000-000000000000', '2025-01-01 15:03:50.0'),
(732, 'Stéphane', 'Savage', 'Savage, Stephane', '1973-10-05', '<EMAIL>', 'Partner - Equity', 'A&A - Assurance', 'Ottawa - Coventry Rd', '5050_Ottawa_Coventry_Rd', NULL, NULL, 'A&A', NULL, 'A&A - Assurance', NULL, 0, '00000000-0000-0000-0000-000000000000', '2025-01-01 15:29:26.3', '00000000-0000-0000-0000-000000000000', '2025-01-01 15:01:46.5'),
(744, 'Walter', 'Flasza', 'Flasza, Walter', NULL, NULL, 'Partner - Equity', NULL, 'Thunder Bay', NULL, NULL, NULL, 'A&A', NULL, NULL, NULL, 0, NULL, '2025-01-01 15:50:00.0', NULL, NULL),
(753, 'Donald', 'Yurkiw', 'Yurkiw, Donald', NULL, '<EMAIL>', NULL, 'A&A - Core', 'Dryden', '1175_Dryden', 'Community of Offices', 'LOCATION_HIERARCHY-3-179', 'A&A', 'CCH_A&A', 'A&A - Core', 'CUSTOM_ORGANIZATION-3-1234', 0, '00000000-0000-0000-0000-000000000000', '2025-01-01 15:27:52.4', NULL, NULL),
(763, 'Nicole', 'White', 'White, Nicole', '1966-12-29', '<EMAIL>', 'Partner - Income', 'A&A - Assurance', 'Bracebridge', '2000_Bracebridge', NULL, NULL, 'A&A', NULL, 'A&A - Assurance', NULL, 0, '00000000-0000-0000-0000-000000000000', '2025-01-01 15:29:55.6', '00000000-0000-0000-0000-000000000000', '2025-01-01 15:03:50.0');

-- Execute the MERGE statement with results tracking
DECLARE @MergeResults TABLE (Action NVARCHAR(10));

MERGE [dbo].[Partner] AS target
USING #TempPartners AS source
ON target.EmployeeId = source.EmployeeId
WHEN MATCHED THEN
    UPDATE SET
        FirstName = source.FirstName,
        LastName = source.LastName,
        DisplayName = source.DisplayName,
        DOB = source.DOB,
        Mail = source.Mail,
        PartnerType = source.PartnerType,
        Department = source.Department,
        Location = source.Location,
        LocationId = source.LocationId,
        WGroup = source.WGroup,
        WGroupId = source.WGroupId,
        ServiceLine = source.ServiceLine,
        ServiceLineId = source.ServiceLineId,
        SubServiceLine = source.SubServiceLine,
        SubServiceLineId = source.SubServiceLineId,
        IsActive = COALESCE(source.IsActive, 1),
        ModifiedBy = COALESCE(source.ModifiedBy, '00000000-0000-0000-0000-000000000000'),
        ModifiedOn = COALESCE(source.ModifiedOn, GETUTCDATE())
WHEN NOT MATCHED THEN
    INSERT (EmployeeId, FirstName, LastName, DisplayName, DOB, Mail, PartnerType, Department, Location, LocationId, WGroup, WGroupId, ServiceLine, ServiceLineId, SubServiceLine, SubServiceLineId, IsActive, CreatedBy, CreatedOn, ModifiedBy, ModifiedOn)
    VALUES (source.EmployeeId, source.FirstName, source.LastName, source.DisplayName, source.DOB, source.Mail, source.PartnerType, source.Department, source.Location, source.LocationId, source.WGroup, source.WGroupId, source.ServiceLine, source.ServiceLineId, source.SubServiceLine, source.SubServiceLineId, 1, COALESCE(source.CreatedBy, '00000000-0000-0000-0000-000000000000'), COALESCE(source.CreatedOn, GETUTCDATE()), COALESCE(source.ModifiedBy, '00000000-0000-0000-0000-000000000000'), COALESCE(source.ModifiedOn, GETUTCDATE()))
OUTPUT $action INTO @MergeResults;

-- Display merge results summary
SELECT
    Action,
    COUNT(*) as Count
FROM @MergeResults
GROUP BY Action;

-- Clean up
DROP TABLE #TempPartners;

PRINT 'Partner merge completed successfully.';
