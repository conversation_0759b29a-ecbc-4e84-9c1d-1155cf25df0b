﻿using BdoPartner.Plans.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BdoPartner.Plans.Model.DTO
{
    /// <summary>
    ///  Note: This context constains processing data for current logon user and his/her current processing required basic parameters.
    ///  Web api application should not maintain processing state in backend. Stateless.
    /// </summary>    
    public class AppContext
    {
        public AppContext()
        {
            this.Language = Enumerations.Language.EN;
            this.TimeZoneOffset = 0;
        }

        /// <summary>
        /// Current language's enumeration definition.
        /// 
        /// Default language is en-us
        /// Note: It could be different with current logon use's prefer language. 
        /// Corporate with process like end user pickup one langauge from drop down in UI.
        /// And the selected language setting need to pass back to server side in Http Request call.
        /// Corporate with HttpRequest header parameter "currentLanguage".
        /// Corporate with middleware called "RequestCultureMiddleware".
        /// </summary>
        public Enumerations.Language Language { get; set; }
        
        /// <summary>
        /// client browser timezone offset.
        /// It is value of hours offset for timezone. negative value. for example value = -5 for Eastern Standard Time to UTC.
        /// based on javascript client side method "-(new Date().getTimezoneOffset()/60)".
        /// Default value = 0.
        /// </summary>
        public int TimeZoneOffset { get; set; }

    }
}
