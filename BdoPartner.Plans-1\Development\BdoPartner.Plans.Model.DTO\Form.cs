using System;
using System.Collections.Generic;

#nullable disable

namespace BdoPartner.Plans.Model.DTO
{
    public partial class Form
    {
        public Guid Id { get; set; }
        public Guid QuestionnaireId { get; set; }
        public short Year { get; set; }
        public string Comments { get; set; }
        public byte Status { get; set; }
        public bool? IsActive { get; set; }
        public Guid? CreatedBy { get; set; }
        public string CreatedByName { get; set; }
        public DateTime? CreatedOn { get; set; }
        public Guid? ModifiedBy { get; set; }
        public string ModifiedByName { get; set; }
        public DateTime? ModifiedOn { get; set; }
        public string PartnerObjectId { get; set; }
        public Guid? PartnerUserId { get; set; }
        public DateTime? PartnerSubmittionDate { get; set; }
        public string PartnerName { get; set; }
        public string PartnerEmail { get; set; }
        public byte[] Pdf { get; set; }

        // Additional partner details from Partner table
        public string ServiceLine { get; set; }
        public string SubServiceLine { get; set; }
        public string Location { get; set; }
        public string Department { get; set; }
        public int? EmployeeId { get; set; }

        // Reviewer information from PartnerReviewer table
        public string PrimaryReviewerName { get; set; }
        public string SecondaryReviewerName { get; set; }

        // Navigation properties
        public string QuestionnaireName { get; set; }
        public string StatusString { get; set; }
        public UserAnswer UserAnswer { get; set; }
        public ICollection<UserAnswer> UserAnswers { get; set; }
    }
}
