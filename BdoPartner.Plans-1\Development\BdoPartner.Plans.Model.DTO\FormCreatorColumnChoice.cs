using System;
using BdoPartner.Plans.Common;

#nullable disable

namespace BdoPartner.Plans.Model.DTO
{
    /// <summary>
    /// DTO for Form Creator column choice with cycle context
    /// Used to provide column mapping options in the SurveyJS Form Creator
    /// </summary>
    public partial class FormCreatorColumnChoice
    {
        /// <summary>
        /// The value to be stored when this option is selected
        /// Format: "CyclePrefix_NormalizedColumnName" when using cycle prefixes
        /// </summary>
        public string Value { get; set; }

        /// <summary>
        /// The display text shown to the user
        /// Format: "CyclePrefix ColumnName" when using cycle prefixes
        /// </summary>
        public string Text { get; set; }

        /// <summary>
        /// The cycle this column belongs to (0=Planning, 1=Mid Year Review, 2=End Year Review)
        /// </summary>
        public byte Cycle { get; set; }

        /// <summary>
        /// Human-readable cycle name
        /// </summary>
        public string CycleName { get; set; }

        /// <summary>
        /// Original column name from the uploaded file
        /// </summary>
        public string OriginalColumnName { get; set; }

        /// <summary>
        /// Normalized column name used in JSON data storage
        /// </summary>
        public string NormalizedColumnName { get; set; }

        /// <summary>
        /// Column data type (0=Text, 1=Numeric, 2=Blank)
        /// </summary>
        public Enumerations.PartnerReferenceDataColumnType ColumnDataType { get; set; }

        /// <summary>
        /// Metadata ID this column belongs to
        /// </summary>
        public Guid MetaId { get; set; }

        /// <summary>
        /// Additional properties for display
        /// </summary>
        public string ColumnDataTypeString { get; set; }
    }
}
