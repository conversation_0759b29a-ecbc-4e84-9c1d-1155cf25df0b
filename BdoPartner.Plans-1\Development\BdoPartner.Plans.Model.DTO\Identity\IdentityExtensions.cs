﻿using IdentityModel;
using BdoPartner.Plans.Common;
using BdoPartner.Plans.Model.DTO.Identity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;

namespace BdoPartner.Plans.Model.DTO.Identity
{
    /// <summary>
    ///  Central place to handle identity user claims convert to DTO.Identity.User object
    ///  or convert DTO.Identity.User properties to custom claims for id token.
    ///  Work for Authorization with Identity Server.
    /// </summary>
    public static class IdentityExtensions
    {
        /// <summary>
        ///  Convert logon user entity's properties to claims. 
        ///  
        ///  Note: As for logon user associated settings which are required by Authenticaiton and Authorization, 
        ///  developer needs to set those information as Claims and let Identity Server 4 central management the cliams.
        ///  
        ///  Developers should avoid each portal, resource web Apis directly access above information from database directly. 
        ///  Since this wayis anti single signon strategy.
        /// </summary>
        /// <param name="user"></param>
        /// <returns></returns>
        public static IEnumerable<Claim> ToClaims(this User user)
        {
            var claims = new List<Claim>();
            //
            //Note: Id and UserName have to have values.
            //
            claims.Add(new Claim(JwtClaimTypes.Id, user.Id.ToString())); 
            claims.Add(new Claim(JwtClaimTypes.Name, user.UserName));
            
            if (!string.IsNullOrEmpty(user.FirstName)) claims.Add(new Claim(JwtClaimTypes.GivenName, user.FirstName));
            if (!string.IsNullOrEmpty(user.LastName)) claims.Add(new Claim(JwtClaimTypes.FamilyName, user.LastName));
            if (!string.IsNullOrEmpty(user.Email)) claims.Add(new Claim(JwtClaimTypes.Email, user.Email));
            
            claims.Add(new Claim(IAMClaimTypes.DisplayName, user.DisplayName??user.UserName));

            if (user.Roles!=null && user.Roles.Count>0) claims.Add(new Claim(JwtClaimTypes.Role, string.Join(',', user.Roles.Select(r=> (int)r))));
            if (user.Permissions != null && user.Permissions.Count > 0) claims.Add(new Claim(IAMClaimTypes.Permissions, string.Join(',', user.Permissions.Select(p=> (int)p))));
            return claims;
        }

        /// <summary>
        ///  Convert logon user's claims to user entity's properties.
        ///  
        ///  Note: As for logon user associated settings which are required by Authenticaiton and Authorization, 
        ///  developer needs to set those information as Claims and let Identity Server 4 central management the cliams.
        ///  
        ///  Developers should avoid each portal, resource web Apis directly access above information from database directly. 
        ///  Since this wayis anti single signon strategy.

        /// </summary>
        /// <param name="pri"></param>
        /// <returns></returns>
        public static User ToIdentityUser(this ClaimsPrincipal pri)
        {
            User user = new User();
            if (pri != null && pri.Identity != null && pri.Identity.IsAuthenticated)
            {
                user.IsAuthenticated = pri.Identity.IsAuthenticated;

                var claims = (from c in pri.Claims select new { c.Type, c.Value }).ToList();

                var idClaim = claims.FirstOrDefault(c => c.Type == JwtClaimTypes.Subject);
                if (idClaim != null)
                {
                    // Reference to column "Id" (Primary key) in table dbo.User.
                    user.Id = Guid.Parse(idClaim.Value);
                }

                // Work for Azure AD user's property "ObjectId".
                var objectIdClaim = claims.FirstOrDefault(c => c.Type == "http://schemas.microsoft.com/identity/claims/objectidentifier");
                if (objectIdClaim != null)
                {
                    user.ObjectId = objectIdClaim.Value;
                }

                // try to get Azure AD principal name first. Note: Some time, the "name" cliam keeps only the "Display name", cannot use as identity name.
                var nameClaim = claims.FirstOrDefault(c => (c.Type== "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name" ||
                                                        c.Type == JwtClaimTypes.Name ||
                                                        c.Type == ClaimTypes.Name) && !c.Value.Contains(",") && !c.Value.Contains(" "));
                if (nameClaim != null)
                {
                    user.UserName = nameClaim.Value;
                }

                var firstNameClaim = claims.FirstOrDefault(c => c.Type == JwtClaimTypes.GivenName || 
                c.Type == ClaimTypes.GivenName || c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname");
                if (firstNameClaim != null)
                {
                    user.FirstName = firstNameClaim.Value;
                }

                var lastNameClaim = claims.FirstOrDefault(c => c.Type == JwtClaimTypes.FamilyName || 
                c.Type == ClaimTypes.Surname || c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname");
               
                if (lastNameClaim != null)
                {
                    user.LastName = lastNameClaim.Value;
                }

                var displayNameClaim = claims.FirstOrDefault(c => c.Type == JwtClaimTypes.Name ||
                c.Type == ClaimTypes.Name || c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name");

                if (displayNameClaim != null)
                {
                    user.DisplayName = displayNameClaim.Value;
                }

                var emailClaim = claims.FirstOrDefault(c => c.Type == JwtClaimTypes.Email ||
                c.Type == ClaimTypes.Email || c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress");

                if (emailClaim != null)
                {
                    user.Email = emailClaim.Value;
                }
                                
                var roleClaims = claims.FirstOrDefault(c => c.Type == JwtClaimTypes.Role);

                if (roleClaims!=null) {
                    string roles = roleClaims.Value;

                    if (!string.IsNullOrEmpty(roles)) {
                        user.Roles = roles.Split(",".ToCharArray(), StringSplitOptions.RemoveEmptyEntries).Select(a => (Enumerations.Role)Enum.Parse(typeof(Enumerations.Role), a)).ToList();
                    }
                }

                var permissionClaims = claims.FirstOrDefault(c => c.Type == IAMClaimTypes.Permissions);

                if (permissionClaims != null)
                {
                    string permissions = permissionClaims.Value;

                    if (!string.IsNullOrEmpty(permissions))
                    {
                        user.Permissions = permissions.Split(",".ToCharArray(), StringSplitOptions.RemoveEmptyEntries).Select(a => (Enumerations.Permission)Enum.Parse(typeof(Enumerations.Permission), a)).ToList();
                    }
                }              

            }

            return user;
        }
    }
}
