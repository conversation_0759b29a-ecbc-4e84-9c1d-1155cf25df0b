﻿using BdoPartner.Plans.Common;
using IdentityModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;

namespace BdoPartner.Plans.Model.DTO.Identity
{
    /// <summary>
    ///  Logon user model.
    ///  Note: It refers to Azure AD user profile or table dbo.User record.
    ///  Note: This entity no password information keeping.
    /// </summary>
    public class User
    {
        public User()
        {
            this.Language = Enumerations.Language.EN;
            this.Roles = new List<Enumerations.Role>();
            this.Permissions = new List<Enumerations.Permission>();
        }
    

        /// <summary>
        ///  User Id. Primary key "Id" in table dbo.User.
        ///  Note: it is value for claim called "sub" in Identity.ClaimPrincipal.
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        ///  Reference to "object Id" in User Profile in Azure AD.
        ///  It is unique identify each user.
        /// </summary>
        public string ObjectId { get; set; }

        /// <summary>
        ///  Column "AuthProvider" in table dbo.User.
        ///  Define current logon user's Authenticaiton provider.
        ///  
        /// </summary>
        public string AuthProviderId { get; set; }

        /// <summary>
        ///  Reference to field UserName in table dbo.User and Azure AD user account property "User Name".
        ///  This is unique field for Azure AD user account identity.
        ///  Note: Refer to login user claim called "name" which passing through ID4.
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        ///  Field "Name" in table dbo.User and Azure AD user account property "Name".
        ///  This is not unique field. Only for display user name information purpose.
        ///  Note: Reference to login user claim "dislayname".
        ///  Reference to field "DisplayName" in table dbo.User.
        /// </summary>
        public string DisplayName { get; set; }

        public string FirstName { get; set; }
        public string LastName { get; set; }

        /// <summary>
        ///  Column "Email" in table dbo.User.
        ///  It could be not unique. Nullable.
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        ///  Reference to field "IsActive" in table dbo.User.
        ///  Work for disable user process.
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        ///  Prefer time zone. Work for timezone.
        ///  Time zone codes: https://en.wikipedia.org/wiki/List_of_tz_database_time_zones
        ///  such as "America/New_York", "America/Montreal", "America/Toronto", "Asia/Shanghai", 
        ///  "Asia/Singapore", "Asia/Taipei", "Europe/Berlin", "Europe/London", "Europe/Moscow"
        /// </summary>
        //public string TimeZone { get; set; }

        /// <summary>
        ///  Work for multiple langauges support.
        ///  Value as "en", 'fr'.
        ///  By default, it is united state english. "en".
        /// </summary>
        public Enumerations.Language Language { get; set; }

        /// <summary>
        /// Reference to records in table dbo.[UserRole] -> dbo.Role.
        /// </summary>
        public ICollection<Enumerations.Role> Roles { get; set; }

        /// <summary>
        ///  Reference to records in table dbo.UserRole -> dbo.Role -> dbo.RolePermission -> Permission.
        /// </summary>
        public ICollection<Enumerations.Permission> Permissions { get; set; }
        
        /// <summary>
        ///  Mapping HttpContext.user.Identity.IsAuthenticated property.
        ///  Note: this property only work for "CurrentUser" property in BaseService and BaseController.
        /// </summary>
        public bool IsAuthenticated { get; set; }
              

    }
}
