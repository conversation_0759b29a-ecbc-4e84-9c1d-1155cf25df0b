﻿using System;
using System.Collections.Generic;
using System.Text;

namespace BdoPartner.Plans.Model.DTO.Identity
{
    /// <summary>
    ///  Reference to Azure AD User Profile properties.
    ///  Work for Identity Server authentication check through Azure AD.
    ///  Note: Azure AD user account information needs to be sync to sql server database table [User]. 
    ///  TODO. We need to use datbase desgined tables [User], [UserRole], [RolePermission], [Role], [Permission] to support customized authorization process.
    /// </summary>
    public class UserProfile
    {
        /// <summary>
        ///  Reference to database table [User].Id. It is string type of Guid value.
        /// </summary>
        public string SubjectId { get; set; }
        /// <summary>
        ///  Reference to Azure AD user's object Id property.
        /// </summary>
        public string ObjectId { get; set; }

        public string Email { get; set; }

        public string DisplayName { get; set; }
 
        /// <summary>
        ///  Reference to database table [User].Username.
        ///  In Azure AD User, it is principal user name.
        /// </summary>
        public string UserName { get; set; }
               
        public string FamilyName { get; set; }
    
        public string GivenName { get; set; }

        public string EmployeeId { get; set; }

        public string JobTitle { get; set; }

        public string Department { get; set; }

        public string StreetAddress { get; set; }

        public string City { get; set; }

        public string State { get; set; }

        public string PostalCode { get; set; }

        public string Country { get; set; }

        public string Mobilephone { get; set; }
    }
}
