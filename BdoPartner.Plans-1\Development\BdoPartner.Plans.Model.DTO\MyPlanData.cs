using System;
using System.Collections.Generic;

#nullable disable

namespace BdoPartner.Plans.Model.DTO
{
    /// <summary>
    /// Consolidated data transfer object for My Plan functionality
    /// Contains questionnaire, form, and user answers in a single response
    /// </summary>
    public partial class MyPlanData
    {
        /// <summary>
        /// The active questionnaire for the year
        /// </summary>
        public Questionnaire Questionnaire { get; set; }

        /// <summary>
        /// The user's form (existing or newly created)
        /// </summary>
        public Form Form { get; set; }

        /// <summary>
        /// The user's existing answers (if any)
        /// </summary>
        public UserAnswer UserAnswer { get; set; }

        /// <summary>
        /// Indicates if this is a new form (just created)
        /// </summary>
        public bool IsNewForm { get; set; }

        /// <summary>
        /// Indicates if the form is editable based on its status
        /// </summary>
        public bool IsEditable { get; set; }

        /// <summary>
        /// The year for this plan
        /// </summary>
        public short Year { get; set; }

        /// <summary>
        /// Any additional messages or warnings
        /// </summary>
        public string Message { get; set; }
    }
}
