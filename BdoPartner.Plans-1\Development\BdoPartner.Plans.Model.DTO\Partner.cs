using System;
using System.Collections.Generic;

#nullable disable

namespace BdoPartner.Plans.Model.DTO
{
    public partial class Partner
    {
        public Guid Id { get; set; }
        public int? EmployeeId { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string DisplayName { get; set; }
        public DateTime? Dob { get; set; }
        public string Mail { get; set; }
        public string PartnerType { get; set; }
        public string Department { get; set; }
        public string Location { get; set; }
        public string LocationId { get; set; }
        public string Wgroup { get; set; }
        public string WgroupId { get; set; }
        public string ServiceLine { get; set; }
        public string ServiceLineId { get; set; }
        public string SubServiceLine { get; set; }
        public string SubServiceLineId { get; set; }
        public bool? IsActive { get; set; }
        public Guid? CreatedBy { get; set; }
        public string CreatedByName { get; set; }
        public DateTime? CreatedOn { get; set; }
        public Guid? ModifiedBy { get; set; }
        public string ModifiedByName { get; set; }
        public DateTime? ModifiedOn { get; set; }

        // Navigation properties
        public string GroupName { get; set; }
    }
}
