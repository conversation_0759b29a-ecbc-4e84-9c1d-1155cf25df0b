using System;
using BdoPartner.Plans.Common;

#nullable disable

namespace BdoPartner.Plans.Model.DTO
{
    public partial class PartnerReferenceData
    {
        public Guid Id { get; set; }
        public Guid PartnerId { get; set; }
        public short Year { get; set; }
        public Enumerations.PartnerReferenceDataCycle Cycle { get; set; }
        public Guid MetaId { get; set; }
        /// <summary>
        ///  Note: It is json format string. json object properties refer to PartnerReferenceDataMetaDetails.NormalizedColumnName
        /// </summary>
        public string Data { get; set; }
        public Guid? CreatedBy { get; set; }
        public DateTime? CreatedOn { get; set; }
        public Guid? ModifiedBy { get; set; }
        public DateTime? ModifiedOn { get; set; }

        // Additional properties for display
        public string CycleString { get; set; }
        public string CreatedByName { get; set; }
        public string ModifiedByName { get; set; }
        
        // Navigation properties
        public Partner Partner { get; set; }
        public PartnerReferenceDataMeta Meta { get; set; }
        
        // Parsed data properties for easier access
        public string PartnerName { get; set; }
        public string EmployeeId { get; set; }
    }
}
