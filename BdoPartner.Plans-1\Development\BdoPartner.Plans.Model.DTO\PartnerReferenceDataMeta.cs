using System;
using System.Collections.Generic;
using BdoPartner.Plans.Common;

#nullable disable

namespace BdoPartner.Plans.Model.DTO
{
    public partial class PartnerReferenceDataMeta
    {
        public Guid Id { get; set; }
        public string FileName { get; set; }
        public short Year { get; set; }
        public Enumerations.PartnerReferenceDataCycle Cycle { get; set; }
        public bool IsActive { get; set; }
        public Guid? CreatedBy { get; set; }
        public DateTime? CreatedOn { get; set; }
        public Guid? ModifiedBy { get; set; }
        public DateTime? ModifiedOn { get; set; }

        // Additional properties for display
        public string CycleString { get; set; }
        public string CreatedByName { get; set; }
        public string ModifiedByName { get; set; }

        // Navigation properties
        public List<PartnerReferenceDataMetaDetails> PartnerReferenceDataMetaDetails { get; set; }
    }
}
