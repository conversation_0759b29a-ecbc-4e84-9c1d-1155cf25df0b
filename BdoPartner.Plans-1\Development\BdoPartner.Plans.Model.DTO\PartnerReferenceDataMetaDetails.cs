using System;
using BdoPartner.Plans.Common;

#nullable disable

namespace BdoPartner.Plans.Model.DTO
{
    public partial class PartnerReferenceDataMetaDetails
    {
        public Guid Id { get; set; }
        public Guid MetaId { get; set; }
        public string ColumnName { get; set; }
        public string NormalizedColumnName { get; set; }
        public Enumerations.PartnerReferenceDataColumnType ColumnDataType { get; set; }
        public short ColumnOrder { get; set; }
        public Guid? CreatedBy { get; set; }
        public DateTime? CreatedOn { get; set; }
        public Guid? ModifiedBy { get; set; }
        public DateTime? ModifiedOn { get; set; }

        // Additional properties for display
        public string ColumnDataTypeString { get; set; }
        public string CreatedByName { get; set; }
        public string ModifiedByName { get; set; }
    }
}
