using System;
using System.Collections.Generic;
using BdoPartner.Plans.Common;

#nullable disable

namespace BdoPartner.Plans.Model.DTO
{
    public partial class PartnerReferenceDataUpload
    {
        public Guid Id { get; set; }
        public string UploadFileName { get; set; }
        // As for performance, we are not binding the file content directly in the DTO, but we can keep it commented out
        //public byte[] FileContent { get; set; }
        public short Year { get; set; }
        public Enumerations.PartnerReferenceDataCycle Cycle { get; set; }
        public Guid MetaId { get; set; }
        public Enumerations.PartnerReferenceDataUploadStatus Status { get; set; }
        public string ValidationSummary { get; set; }
        public Guid? CreatedBy { get; set; }
        public DateTime? CreatedOn { get; set; }
        public Guid? ModifiedBy { get; set; }
        public DateTime? ModifiedOn { get; set; }

        // Additional properties for display
        public string StatusString { get; set; }
        public string CycleString { get; set; }
        public string CreatedByName { get; set; }
        public string ModifiedByName { get; set; }
        public int TotalRecords { get; set; }
        public int ValidRecords { get; set; }
        public int InvalidRecords { get; set; }

        // Navigation properties
        public List<PartnerReferenceDataUploadDetails> PartnerReferenceDataUploadDetails { get; set; }
        public PartnerReferenceDataMeta Meta { get; set; }
    }
}
