using System;
using System.Collections.Generic;

#nullable disable

namespace BdoPartner.Plans.Model.DTO
{
    public partial class PartnerReferenceDataUploadDetails
    {
        public Guid Id { get; set; }
        public Guid PartnerReferenceDataUploadId { get; set; }
        public int RowId { get; set; }
        public string Data { get; set; }
        public string ValidationError { get; set; }
        public Guid? CreatedBy { get; set; }
        public DateTime? CreatedOn { get; set; }
        public Guid? ModifiedBy { get; set; }
        public DateTime? ModifiedOn { get; set; }

        // Additional properties for display
        public string CreatedByName { get; set; }
        public string ModifiedByName { get; set; }
        public bool HasValidationError => !string.IsNullOrEmpty(ValidationError);
        public bool IsValid => string.IsNullOrEmpty(ValidationError);

        // Dynamic column data based on metadata - key is NormalizedColumnName, value is string representation
        public Dictionary<string, string> ColumnData { get; set; } = new Dictionary<string, string>();
    }
}
