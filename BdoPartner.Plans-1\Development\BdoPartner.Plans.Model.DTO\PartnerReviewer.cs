using System;
using System.Collections.Generic;

#nullable disable

namespace BdoPartner.Plans.Model.DTO
{
    public partial class PartnerReviewer
    {
        public Guid Id { get; set; }
        public short Year { get; set; }
        public Guid PartnerId { get; set; }
        public bool Exempt { get; set; }
        public string LeadershipRole { get; set; }
        public Guid? PrimaryReviewerId { get; set; }
        public string PrimaryReviewerName { get; set; }
        public Guid? SecondaryReviewerId { get; set; }
        public string SecondaryReviewerName { get; set; }
        public Guid? CreatedBy { get; set; }
        public DateTime? CreatedOn { get; set; }
        public Guid? ModifiedBy { get; set; }
        public DateTime? ModifiedOn { get; set; }

        // Additional properties for display
        public string PartnerDisplayName { get; set; }
        public string PartnerEmployeeId { get; set; }
        public string PrimaryReviewerDisplayName { get; set; }
        public string PrimaryReviewerEmployeeId { get; set; }
        public string SecondaryReviewerDisplayName { get; set; }
        public string SecondaryReviewerEmployeeId { get; set; }
        public string CreatedByName { get; set; }
        public string ModifiedByName { get; set; }

        // Navigation properties
        public Partner Partner { get; set; }
        public Partner PrimaryReviewer { get; set; }
        public Partner SecondaryReviewer { get; set; }
    }
}
