using System;
using System.Collections.Generic;

#nullable disable

namespace BdoPartner.Plans.Model.DTO
{
    public partial class PartnerReviewerUploadDetails
    {
        public long Id { get; set; }

        public int PartnerReviewerUploadId { get; set; }

        public int RowId { get; set; }

        public string EmployeeId { get; set; }
        public string EmployeeName { get; set; }
        public string Exempt { get; set; }
        public string LeadershipRole { get; set; }
        public string PrimaryReviewerId { get; set; }
        public string PrimaryReviewerName { get; set; }
        public string SecondaryReviewerId { get; set; }
        public string SecondaryReviewerName { get; set; }
        public string ValidationError { get; set; }
        public Guid? CreatedBy { get; set; }
        public string CreatedByName { get; set; }
        public DateTime? CreatedOn { get; set; }
        public Guid? ModifiedBy { get; set; }
        public string ModifiedByName { get; set; }
        public DateTime? ModifiedOn { get; set; }

        // Additional properties for display
        public bool IsValid { get; set; }
        public bool ExemptBoolean { get; set; }
        public string PartnerDisplayName { get; set; }
        public string PrimaryReviewerDisplayName { get; set; }
        public string SecondaryReviewerDisplayName { get; set; }

        // Navigation properties
        //public PartnerReviewerUpload PartnerReviewerUpload { get; set; }
    }
}
