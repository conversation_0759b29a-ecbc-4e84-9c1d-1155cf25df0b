using System;
using System.Collections.Generic;

#nullable disable

namespace BdoPartner.Plans.Model.DTO
{
    public partial class Questionnaire
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public short Year { get; set; }
        public string DefinitionJson { get; set; }
        public string DraftDefinitionJson { get; set; }
        public int FormSystemVersion { get; set; }
        public bool Acknowledgement { get; set; }
        public string AcknowledgementText { get; set; }
        public bool GeneralComments { get; set; }
        public string GeneralCommentsText { get; set; }
        public byte Status { get; set; }
        public bool? IsActive { get; set; }
        public Guid? CreatedBy { get; set; }
        public string CreatedByName { get; set; }
        public DateTime? CreatedOn { get; set; }
        public Guid? ModifiedBy { get; set; }
        public string ModifiedByName { get; set; }
        public DateTime? ModifiedOn { get; set; }

        // Additional properties for display
        public string StatusString { get; set; }
        public int FormCount { get; set; }
    }
}
