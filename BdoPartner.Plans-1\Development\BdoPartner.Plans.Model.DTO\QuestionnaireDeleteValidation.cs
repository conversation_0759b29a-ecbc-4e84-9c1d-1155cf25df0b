using System;

namespace BdoPartner.Plans.Model.DTO
{
    /// <summary>
    /// DTO for questionnaire deletion validation results
    /// </summary>
    public class QuestionnaireDeleteValidation
    {
        /// <summary>
        /// Whether the questionnaire can be deleted
        /// </summary>
        public bool CanDelete { get; set; }

        /// <summary>
        /// Whether the questionnaire is published
        /// </summary>
        public bool IsPublished { get; set; }

        /// <summary>
        /// Whether there are forms that reference this questionnaire
        /// </summary>
        public bool HasFormReferences { get; set; }

        /// <summary>
        /// Number of forms that reference this questionnaire
        /// </summary>
        public int FormReferenceCount { get; set; }
    }
}
