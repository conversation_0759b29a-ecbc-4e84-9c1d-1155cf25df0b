using System;

namespace BdoPartner.Plans.Model.DTO
{
    /// <summary>
    /// Simple DTO for questionnaire list rendering.
    /// Excludes heavy JSON fields for better performance.
    /// </summary>
    public class QuestionnaireListItem
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public short Year { get; set; }
        public int FormSystemVersion { get; set; }
        public byte Status { get; set; }
        public bool? IsActive { get; set; }
        public Guid? CreatedBy { get; set; }
        public string CreatedByName { get; set; }
        public DateTime? CreatedOn { get; set; }
        public Guid? ModifiedBy { get; set; }
        public string ModifiedByName { get; set; }
        public DateTime? ModifiedOn { get; set; }
    }
}
