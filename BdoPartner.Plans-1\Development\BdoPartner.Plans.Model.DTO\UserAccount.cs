﻿using BdoPartner.Plans.Common;
using System;
using System.Collections.Generic;
using System.Text;

namespace BdoPartner.Plans.Model.DTO
{
    /// <summary>
    ///  User information included readable password.
    ///  Work for change password, reset password.
    ///  Note: This entity should not expose to client side for security reason. 
    ///  Get information from dbo.User table.
    /// </summary>
    public class UserAccount
    {
        public UserAccount()
        {
            this.Language = Enumerations.Language.EN;
            this.Roles = new List<Enumerations.Role>();
        }

        /// <summary>
        ///  User Id. Primary key in table dbo.User.
        ///  Note: it is value for claim called "sub" in Identity.ClaimPrincipal.
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        ///  Reference to "object Id" in User Profile in Azure AD.
        ///  It must be unique for each user from Azure AD authentication provider.
        ///  Note: For database driving use account (AuthProviderId = "APP"), this field's value is null.
        ///  Note: It should not editable with Admin.
        /// </summary>
        public string ObjectId { get; set; }

        /// <summary>
        ///  Column "AuthProvider" in table dbo.User.
        ///  Define current logon user's Authenticaiton provider.
        ///  Note: It should not be editable by Admin. For Database driving account, system by default set it as "APP".
        /// </summary>
        public string AuthProviderId { get; set; }

        /// <summary>
        ///  User name must be unique. Reference to column "UserName" in table dbo.User.
        ///  Note: It should not be editable with Admin.
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        ///  Refer to column "DisplayName" in table dbo.User.
        /// </summary>
        public string DisplayName { get; set; }

        public string FirstName { get; set; }

        public string LastName { get; set; }

        /// <summary>
        ///  It could be not unique. Nullable.
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        ///  Work for multiple langauges support.
        ///  Value as "en", 'fr'.
        ///  By default, it is united state english. "en".
        /// </summary>
        public Enumerations.Language Language { get; set; }

        /// <summary>
        /// Reference to records in table dbo.[UserRole] -> dbo.Role.
        /// </summary>
        public ICollection<Enumerations.Role> Roles { get; set; }

        /// <summary>
        ///  Reference to field "IsActive" in table dbo.User.
        ///  Work for disable user process.
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        ///  Reference to field "Password" in table dbo.User.
        /// </summary>
        public string Password { get; set; }

        /// <summary>
        ///  Re-enter password in UI. Note: It should be same value as Password.
        /// </summary>
        public string ConfirmedPassword { get; set; }

    }
}
