{"format": 1, "restore": {"C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Model.DTO\\BdoPartner.Plans.Model.DTO.csproj": {}}, "projects": {"C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Common\\BdoPartner.Plans.Common.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Common\\BdoPartner.Plans.Common.csproj", "projectName": "BdoPartner.Plans.Common", "projectPath": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Common\\BdoPartner.Plans.Common.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Common\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net5.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net5.0": {"targetAlias": "net5.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net5.0": {"targetAlias": "net5.0", "dependencies": {"IdentityModel": {"target": "Package", "version": "[5.1.0, )"}, "Microsoft.AspNetCore.Authentication": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.AspNetCore.Hosting": {"target": "Package", "version": "[2.2.7, )"}, "Microsoft.AspNetCore.Http.Abstractions": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[5.0.0, )"}, "Microsoft.Extensions.Configuration.Binder": {"target": "Package", "version": "[5.0.0, )"}, "Microsoft.Extensions.Configuration.EnvironmentVariables": {"target": "Package", "version": "[5.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[5.0.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[5.0.2, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[5.0.0, )"}, "Microsoft.Extensions.Logging.ApplicationInsights": {"target": "Package", "version": "[2.17.0, )"}, "Microsoft.Extensions.Logging.AzureAppServices": {"target": "Package", "version": "[5.0.8, )"}, "Microsoft.Extensions.Options": {"target": "Package", "version": "[5.0.0, )"}, "Microsoft.IdentityModel.Clients.ActiveDirectory": {"target": "Package", "version": "[5.2.9, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.300\\RuntimeIdentifierGraph.json"}}}, "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Model.DTO\\BdoPartner.Plans.Model.DTO.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Model.DTO\\BdoPartner.Plans.Model.DTO.csproj", "projectName": "BdoPartner.Plans.Model.DTO", "projectPath": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Model.DTO\\BdoPartner.Plans.Model.DTO.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Model.DTO\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net5.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net5.0": {"targetAlias": "net5.0", "projectReferences": {"C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Common\\BdoPartner.Plans.Common.csproj": {"projectPath": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Common\\BdoPartner.Plans.Common.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net5.0": {"targetAlias": "net5.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.300\\RuntimeIdentifierGraph.json"}}}}}