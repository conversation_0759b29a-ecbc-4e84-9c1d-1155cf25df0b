﻿//using System;
//using System.Collections.Generic;

//#nullable disable

//namespace BdoPartner.Plans.Model.Entity
//{
//    public partial class DataAudit
//    {
//        public long Id { get; set; }
//        public Guid AuditGroupId { get; set; }
//        public int EntityState { get; set; }
//        public string TableName { get; set; }
//        public string PrimaryKeyField { get; set; }
//        public string PrimaryKeyValue { get; set; }
//        public string FieldName { get; set; }
//        public string OriginalValue { get; set; }
//        public string CurrentValue { get; set; }
//        public DateTime CreatedOn { get; set; }
//        public string CreatedBy { get; set; }
//    }
//}
