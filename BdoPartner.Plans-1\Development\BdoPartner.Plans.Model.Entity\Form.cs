using System;
using System.Collections.Generic;

#nullable disable

namespace BdoPartner.Plans.Model.Entity
{
    public partial class Form
    {
        public Form()
        {
            UserAnswers = new HashSet<UserAnswer>();
        }

        public Guid Id { get; set; }
        public Guid QuestionnaireId { get; set; }
        public short Year { get; set; }
        public string Comments { get; set; }
        public byte Status { get; set; }
        public bool? IsActive { get; set; }
        public Guid? CreatedBy { get; set; }
        public string CreatedByName { get; set; }
        public DateTime? CreatedOn { get; set; }
        public Guid? ModifiedBy { get; set; }
        public string ModifiedByName { get; set; }
        public DateTime? ModifiedOn { get; set; }
        public string PartnerObjectId { get; set; }
        public Guid? PartnerUserId { get; set; }
        public DateTime? PartnerSubmittionDate { get; set; }
        public string PartnerName { get; set; }
        public string PartnerEmail { get; set; }
        public byte[] Pdf { get; set; }

        public virtual FormStatus FormStatus { get; set; }
        public virtual Questionnaire Questionnaire { get; set; }
        public virtual ICollection<UserAnswer> UserAnswers { get; set; }
    }
}
