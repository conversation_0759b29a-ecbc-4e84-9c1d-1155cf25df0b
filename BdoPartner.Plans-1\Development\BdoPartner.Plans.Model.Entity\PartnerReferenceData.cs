using System;
using System.Collections.Generic;

#nullable disable

namespace BdoPartner.Plans.Model.Entity
{
    public partial class PartnerReferenceData
    {
        public Guid Id { get; set; }
        public Guid PartnerId { get; set; }
        public short Year { get; set; }
        public byte Cycle { get; set; }
        public Guid MetaId { get; set; }
        public string Data { get; set; }
        public Guid? CreatedBy { get; set; }
        public string CreatedByName { get; set; }
        public DateTime? CreatedOn { get; set; }
        public Guid? ModifiedBy { get; set; }
        public string ModifiedByName { get; set; }
        public DateTime? ModifiedOn { get; set; }

        public virtual Partner Partner { get; set; }
        public virtual PartnerReferenceDataMeta Meta { get; set; }
    }
}
