using System;
using System.Collections.Generic;

#nullable disable

namespace BdoPartner.Plans.Model.Entity
{
    public partial class PartnerReferenceDataMeta
    {
        public PartnerReferenceDataMeta()
        {
            PartnerReferenceDataMetaDetails = new HashSet<PartnerReferenceDataMetaDetails>();
            PartnerReferenceDataUploads = new HashSet<PartnerReferenceDataUpload>();
            PartnerReferenceData = new HashSet<PartnerReferenceData>();
        }

        public Guid Id { get; set; }
        public string FileName { get; set; }
        public short Year { get; set; }
        public byte Cycle { get; set; }
        public bool IsActive { get; set; }
        public Guid? CreatedBy { get; set; }
        public string CreatedByName { get; set; }
        public DateTime? CreatedOn { get; set; }
        public Guid? ModifiedBy { get; set; }
        public string ModifiedByName { get; set; }
        public DateTime? ModifiedOn { get; set; }

        public virtual ICollection<PartnerReferenceDataMetaDetails> PartnerReferenceDataMetaDetails { get; set; }
        public virtual ICollection<PartnerReferenceDataUpload> PartnerReferenceDataUploads { get; set; }
        public virtual ICollection<PartnerReferenceData> PartnerReferenceData { get; set; }
    }
}
