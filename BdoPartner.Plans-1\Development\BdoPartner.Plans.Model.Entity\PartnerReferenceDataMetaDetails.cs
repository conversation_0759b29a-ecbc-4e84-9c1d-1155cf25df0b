using System;
using System.Collections.Generic;

#nullable disable

namespace BdoPartner.Plans.Model.Entity
{
    public partial class PartnerReferenceDataMetaDetails
    {
        public Guid Id { get; set; }
        public Guid MetaId { get; set; }
        public string ColumnName { get; set; }
        public string NormalizedColumnName { get; set; }
        public byte ColumnDataType { get; set; }
        public short ColumnOrder { get; set; }
        public Guid? CreatedBy { get; set; }
        public string CreatedByName { get; set; }
        public DateTime? CreatedOn { get; set; }
        public Guid? ModifiedBy { get; set; }
        public string ModifiedByName { get; set; }
        public DateTime? ModifiedOn { get; set; }

        public virtual PartnerReferenceDataMeta Meta { get; set; }
    }
}
