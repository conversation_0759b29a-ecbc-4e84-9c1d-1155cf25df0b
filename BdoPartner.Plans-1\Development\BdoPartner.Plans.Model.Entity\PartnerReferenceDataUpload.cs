using System;
using System.Collections.Generic;

#nullable disable

namespace BdoPartner.Plans.Model.Entity
{
    public partial class PartnerReferenceDataUpload
    {
        public PartnerReferenceDataUpload()
        {
            PartnerReferenceDataUploadDetails = new HashSet<PartnerReferenceDataUploadDetails>();
        }

        public Guid Id { get; set; }
        public string UploadFileName { get; set; }
        public byte[] FileContent { get; set; }
        public short Year { get; set; }
        public byte Cycle { get; set; }
        public Guid MetaId { get; set; }
        public byte Status { get; set; }
        public string ValidationSummary { get; set; }
        public Guid? CreatedBy { get; set; }
        public string CreatedByName { get; set; }
        public DateTime? CreatedOn { get; set; }
        public Guid? ModifiedBy { get; set; }
        public string ModifiedByName { get; set; }
        public DateTime? ModifiedOn { get; set; }

        public virtual PartnerReferenceDataMeta Meta { get; set; }
        public virtual ICollection<PartnerReferenceDataUploadDetails> PartnerReferenceDataUploadDetails { get; set; }
    }
}
