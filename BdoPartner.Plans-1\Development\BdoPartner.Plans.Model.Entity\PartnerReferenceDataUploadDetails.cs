using System;
using System.Collections.Generic;

#nullable disable

namespace BdoPartner.Plans.Model.Entity
{
    public partial class PartnerReferenceDataUploadDetails
    {
        public Guid Id { get; set; }
        public Guid PartnerReferenceDataUploadId { get; set; }
        public int RowId { get; set; }
        public string Data { get; set; }
        public string ValidationError { get; set; }
        public Guid? CreatedBy { get; set; }
        public string CreatedByName { get; set; }
        public DateTime? CreatedOn { get; set; }
        public Guid? ModifiedBy { get; set; }
        public string ModifiedByName { get; set; }
        public DateTime? ModifiedOn { get; set; }

        public virtual PartnerReferenceDataUpload PartnerReferenceDataUpload { get; set; }
    }
}
