using System;
using System.Collections.Generic;

#nullable disable

namespace BdoPartner.Plans.Model.Entity
{
    public partial class PartnerReviewer
    {
        public Guid Id { get; set; }
        public short Year { get; set; }
        public Guid PartnerId { get; set; }
        public bool Exempt { get; set; }
        public string LeadershipRole { get; set; }
        public Guid? PrimaryReviewerId { get; set; }
        public string PrimaryReviewerName { get; set; }
        public Guid? SecondaryReviewerId { get; set; }
        public string SecondaryReviewerName { get; set; }
        public Guid? CreatedBy { get; set; }
        public string CreatedByName { get; set; }
        public DateTime? CreatedOn { get; set; }
        public Guid? ModifiedBy { get; set; }
        public string ModifiedByName { get; set; }
        public DateTime? ModifiedOn { get; set; }

        public virtual Partner Partner { get; set; }
        public virtual Partner PrimaryReviewer { get; set; }
        public virtual Partner SecondaryReviewer { get; set; }
    }
}
