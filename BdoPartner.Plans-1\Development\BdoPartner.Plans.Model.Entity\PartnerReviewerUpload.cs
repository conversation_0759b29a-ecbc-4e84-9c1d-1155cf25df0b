using System;
using System.Collections.Generic;

#nullable disable

namespace BdoPartner.Plans.Model.Entity
{
    public partial class PartnerReviewerUpload
    {
        public PartnerReviewerUpload()
        {
            PartnerReviewerUploadDetails = new HashSet<PartnerReviewerUploadDetails>();
        }

        public int Id { get; set; }
        public string Years { get; set; }
        public string UploadFileName { get; set; }
        public string ValidationSummary { get; set; }
        public byte[] FileContent { get; set; }
        public byte Status { get; set; }
        public Guid? CreatedBy { get; set; }
        public string CreatedByName { get; set; }
        public DateTime? CreatedOn { get; set; }
        public Guid? ModifiedBy { get; set; }
        public string ModifiedByName { get; set; }
        public DateTime? ModifiedOn { get; set; }

        public virtual ICollection<PartnerReviewerUploadDetails> PartnerReviewerUploadDetails { get; set; }
    }
}
