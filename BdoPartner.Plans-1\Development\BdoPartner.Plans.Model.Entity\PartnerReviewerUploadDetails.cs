using System;
using System.Collections.Generic;

#nullable disable

namespace BdoPartner.Plans.Model.Entity
{
    public partial class PartnerReviewerUploadDetails
    {
        public long Id { get; set; }
        public int PartnerReviewerUploadId { get; set; }
        public int RowId { get; set; }
        public string EmployeeId { get; set; }
        public string EmployeeName { get; set; }
        public string Exempt { get; set; }
        public string LeadershipRole { get; set; }
        public string PrimaryReviewerId { get; set; }
        public string PrimaryReviewerName { get; set; }
        public string SecondaryReviewerId { get; set; }
        public string SecondaryReviewerName { get; set; }
        public string ValidationError { get; set; }
        public Guid? CreatedBy { get; set; }
        public string CreatedByName { get; set; }
        public DateTime? CreatedOn { get; set; }
        public Guid? ModifiedBy { get; set; }
        public string ModifiedByName { get; set; }
        public DateTime? ModifiedOn { get; set; }

        public virtual PartnerReviewerUpload PartnerReviewerUpload { get; set; }
    }
}
