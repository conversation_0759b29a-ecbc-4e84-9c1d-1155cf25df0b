{"format": 1, "restore": {"C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Model.Entity\\BdoPartner.Plans.Model.Entity.csproj": {}}, "projects": {"C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Model.Entity\\BdoPartner.Plans.Model.Entity.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Model.Entity\\BdoPartner.Plans.Model.Entity.csproj", "projectName": "BdoPartner.Plans.Model.Entity", "projectPath": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Model.Entity\\BdoPartner.Plans.Model.Entity.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\git\\Partner-Site\\BdoPartner.Plans-1\\Development\\BdoPartner.Plans.Model.Entity\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net5.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net5.0": {"targetAlias": "net5.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net5.0": {"targetAlias": "net5.0", "dependencies": {"Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[5.0.7, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[5.0.7, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[5.0.7, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.300\\RuntimeIdentifierGraph.json"}}}}}