﻿* Reverse Engineering Existing Databases in Entity Framework Core

1. Install following packages into Model.Entity project.

Install-Package Microsoft.EntityFrameworkCore.SqlServer
Install-Package Microsoft.EntityFrameworkCore.Tools
Install-Package Microsoft.VisualStudio.Web.CodeGeneration.Design


2. Set BdoPartner.Plans.Model.Entity as startup project.


3. Run following command:

Tools –> NuGet Package Manager –> Package Manager Console:

-- Script for sql server database
Scaffold-DbContext "Server=localhost\SQLEXPRESS;Database=BdoPartner.Plans.Database;Trusted_Connection=True;" Microsoft.EntityFrameworkCore.SqlServer -force


-- Script for mysql database.
Scaffold-DbContext "User Id=root;Host=localhost;Database=testdb;Password=Password1;TreatTinyAsBoolean=false" Pomelo.EntityFrameworkCore.MySql -force
  or 
Scaffold-DbContext "User Id=root;Host=localhost;Database=testdb;Password=Password1;TreatTinyAsBoolean=true" Pomelo.EntityFrameworkCore.MySql -force


Reference: 
https://docs.microsoft.com/en-us/aspnet/web-api/overview/data/using-web-api-with-entity-framework/part-5
https://docs.microsoft.com/en-us/ef/core/get-started/aspnetcore/existing-db
