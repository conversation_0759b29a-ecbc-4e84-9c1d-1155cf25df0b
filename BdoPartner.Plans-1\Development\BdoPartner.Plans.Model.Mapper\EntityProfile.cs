using AutoMapper;
using System.Linq;

namespace BdoPartner.Plans.Model.Mapper
{
    /// <summary>
    /// AutoMapper profile for mapping between Entity and DTO models for new entities
    /// </summary>
    public class EntityProfile : Profile
    {
        public EntityProfile()
        {
           
            // Partner mappings
            CreateMap<Entity.Partner, DTO.Partner>()
                .ForMember(d => d.Wgroup, m => m.MapFrom(e => e.Wgroup))
                .ForMember(d => d.WgroupId, m => m.MapFrom(e => e.WgroupId))
                .ForMember(d => d.GroupName, m => m.Ignore()); // GroupName is no longer available

            CreateMap<DTO.Partner, Entity.Partner>()
                .ForMember(e => e.Wgroup, m => m.MapFrom(d => d.Wgroup))
                .ForMember(e => e.WgroupId, m => m.MapFrom(d => d.WgroupId));

            // FormStatus mappings
            CreateMap<Entity.FormStatus, DTO.FormStatus>();
            CreateMap<DTO.FormStatus, Entity.FormStatus>()
                .ForMember(e => e.Forms, m => m.Ignore()); // Ignore navigation properties

            // QuestionnaireStatus mappings
            CreateMap<Entity.QuestionnaireStatus, DTO.QuestionnaireStatus>();
            CreateMap<DTO.QuestionnaireStatus, Entity.QuestionnaireStatus>()
                .ForMember(e => e.Questionnaires, m => m.Ignore()); // Ignore navigation properties

            // Questionnaire mappings
            CreateMap<Entity.Questionnaire, DTO.Questionnaire>()
                .ForMember(d => d.StatusString, m => m.MapFrom(e => GetQuestionnaireStatusString(e.Status)))
                .ForMember(d => d.FormCount, m => m.MapFrom(e => e.Forms != null ? e.Forms.Count : 0));

            CreateMap<DTO.Questionnaire, Entity.Questionnaire>()
                .ForMember(e => e.QuestionnaireStatus, m => m.Ignore()) // Ignore navigation properties
                .ForMember(e => e.Forms, m => m.Ignore()); // Ignore navigation properties

            // QuestionnaireListItem mappings for list rendering (excludes heavy JSON fields)
            CreateMap<Entity.Questionnaire, DTO.QuestionnaireListItem>();

            CreateMap<DTO.QuestionnaireListItem, Entity.Questionnaire>()
                .ForMember(e => e.QuestionnaireStatus, m => m.Ignore()) // Ignore navigation properties
                .ForMember(e => e.Forms, m => m.Ignore()) // Ignore navigation properties
                .ForMember(e => e.DefinitionJson, m => m.Ignore()) // Ignore heavy JSON fields
                .ForMember(e => e.DraftDefinitionJson, m => m.Ignore()) // Ignore heavy JSON fields
                .ForMember(e => e.AcknowledgementText, m => m.Ignore()) // Ignore heavy text fields
                .ForMember(e => e.GeneralCommentsText, m => m.Ignore()); // Ignore heavy text fields

            // Form mappings
            CreateMap<Entity.Form, DTO.Form>()
                .ForMember(d => d.QuestionnaireName, m => m.MapFrom(e => e.Questionnaire != null ? e.Questionnaire.Name : null))
                .ForMember(d => d.StatusString, m => m.MapFrom(e => GetFormStatusString(e.Status)))
                .ForMember(d => d.UserAnswers, m => m.MapFrom(e => e.UserAnswers));

            CreateMap<DTO.Form, Entity.Form>()
                .ForMember(e => e.FormStatus, m => m.Ignore()) // Ignore navigation properties
                .ForMember(e => e.Questionnaire, m => m.Ignore()) // Ignore navigation properties
                .ForMember(e => e.UserAnswers, m => m.Ignore()); // Ignore navigation properties

            // UserAnswer mappings
            CreateMap<Entity.UserAnswer, DTO.UserAnswer>()
                .ForMember(d => d.FormName, m => m.MapFrom(e => e.Form != null && e.Form.Questionnaire != null ? e.Form.Questionnaire.Name : null))
                .ForMember(d => d.PartnerName, m => m.MapFrom(e => e.Form != null ? e.Form.PartnerName : null));

            CreateMap<DTO.UserAnswer, Entity.UserAnswer>()
                .ForMember(e => e.Form, m => m.Ignore()); // Ignore navigation properties

            // PartnerReviewerUpload mappings
            CreateMap<Entity.PartnerReviewerUpload, DTO.PartnerReviewerUpload>()
                .ForMember(d => d.Status, m => m.MapFrom(e => (BdoPartner.Plans.Common.Enumerations.PartnerReviewerUploadStatus)e.Status))
                .ForMember(d => d.StatusString, m => m.MapFrom(e => GetPartnerReviewerUploadStatusString(e.Status)))
                .ForMember(d => d.TotalRecords, m => m.MapFrom(e => e.PartnerReviewerUploadDetails != null ? e.PartnerReviewerUploadDetails.Count : 0))
                .ForMember(d => d.ValidRecords, m => m.MapFrom(e => e.PartnerReviewerUploadDetails != null ? e.PartnerReviewerUploadDetails.Count(x => string.IsNullOrEmpty(x.ValidationError)) : 0))
                .ForMember(d => d.InvalidRecords, m => m.MapFrom(e => e.PartnerReviewerUploadDetails != null ? e.PartnerReviewerUploadDetails.Count(x => !string.IsNullOrEmpty(x.ValidationError)) : 0));

            CreateMap<DTO.PartnerReviewerUpload, Entity.PartnerReviewerUpload>()
                .ForMember(e => e.Status, m => m.MapFrom(d => (byte)d.Status))
                .ForMember(e => e.PartnerReviewerUploadDetails, m => m.Ignore()); // Ignore navigation properties

            // PartnerReviewerUploadDetails mappings
            CreateMap<Entity.PartnerReviewerUploadDetails, DTO.PartnerReviewerUploadDetails>()
                .ForMember(d => d.IsValid, m => m.MapFrom(e => string.IsNullOrEmpty(e.ValidationError)))
                .ForMember(d => d.ExemptBoolean, m => m.MapFrom(e => e.Exempt == "Y"))
                .ForMember(d => d.PartnerDisplayName, m => m.Ignore()) // Will be populated by service layer
                .ForMember(d => d.PrimaryReviewerDisplayName, m => m.Ignore()) // Will be populated by service layer
                .ForMember(d => d.SecondaryReviewerDisplayName, m => m.Ignore()); // Will be populated by service layer

            CreateMap<DTO.PartnerReviewerUploadDetails, Entity.PartnerReviewerUploadDetails>()
                .ForMember(e => e.PartnerReviewerUpload, m => m.Ignore()); // Ignore navigation properties

            // PartnerReviewer mappings
            CreateMap<Entity.PartnerReviewer, DTO.PartnerReviewer>()
                .ForMember(d => d.PartnerDisplayName, m => m.MapFrom(e => e.Partner != null ? e.Partner.DisplayName : null))
                .ForMember(d => d.PartnerEmployeeId, m => m.MapFrom(e => e.Partner != null ? e.Partner.EmployeeId.ToString() : null))
                .ForMember(d => d.PrimaryReviewerDisplayName, m => m.MapFrom(e => e.PrimaryReviewer != null ? e.PrimaryReviewer.DisplayName : null))
                .ForMember(d => d.PrimaryReviewerEmployeeId, m => m.MapFrom(e => e.PrimaryReviewer != null ? e.PrimaryReviewer.EmployeeId.ToString() : null))
                .ForMember(d => d.SecondaryReviewerDisplayName, m => m.MapFrom(e => e.SecondaryReviewer != null ? e.SecondaryReviewer.DisplayName : null))
                .ForMember(d => d.SecondaryReviewerEmployeeId, m => m.MapFrom(e => e.SecondaryReviewer != null ? e.SecondaryReviewer.EmployeeId.ToString() : null));

            CreateMap<DTO.PartnerReviewer, Entity.PartnerReviewer>()
                .ForMember(e => e.Partner, m => m.Ignore()) // Ignore navigation properties
                .ForMember(e => e.PrimaryReviewer, m => m.Ignore()) // Ignore navigation properties
                .ForMember(e => e.SecondaryReviewer, m => m.Ignore()); // Ignore navigation properties

            // PartnerReferenceDataMeta mappings
            CreateMap<Entity.PartnerReferenceDataMeta, DTO.PartnerReferenceDataMeta>()
                .ForMember(d => d.Cycle, m => m.MapFrom(e => (BdoPartner.Plans.Common.Enumerations.PartnerReferenceDataCycle)e.Cycle))
                .ForMember(d => d.CycleString, m => m.MapFrom(e => GetPartnerReferenceDataCycleString(e.Cycle)))
                .ForMember(d => d.PartnerReferenceDataMetaDetails, m => m.MapFrom(e => e.PartnerReferenceDataMetaDetails));

            CreateMap<DTO.PartnerReferenceDataMeta, Entity.PartnerReferenceDataMeta>()
                .ForMember(e => e.Cycle, m => m.MapFrom(d => (byte)d.Cycle))
                .ForMember(e => e.PartnerReferenceDataMetaDetails, m => m.Ignore()) // Ignore navigation properties
                .ForMember(e => e.PartnerReferenceDataUploads, m => m.Ignore()) // Ignore navigation properties
                .ForMember(e => e.PartnerReferenceData, m => m.Ignore()); // Ignore navigation properties

            // PartnerReferenceDataMetaDetails mappings
            CreateMap<Entity.PartnerReferenceDataMetaDetails, DTO.PartnerReferenceDataMetaDetails>()
                .ForMember(d => d.ColumnDataType, m => m.MapFrom(e => (BdoPartner.Plans.Common.Enumerations.PartnerReferenceDataColumnType)e.ColumnDataType))
                .ForMember(d => d.ColumnDataTypeString, m => m.MapFrom(e => GetPartnerReferenceDataColumnTypeString(e.ColumnDataType)));

            CreateMap<DTO.PartnerReferenceDataMetaDetails, Entity.PartnerReferenceDataMetaDetails>()
                .ForMember(e => e.ColumnDataType, m => m.MapFrom(d => (byte)d.ColumnDataType))
                .ForMember(e => e.Meta, m => m.Ignore()); // Ignore navigation properties

            // PartnerReferenceDataUpload mappings
            CreateMap<Entity.PartnerReferenceDataUpload, DTO.PartnerReferenceDataUpload>()
                .ForMember(d => d.Cycle, m => m.MapFrom(e => (BdoPartner.Plans.Common.Enumerations.PartnerReferenceDataCycle)e.Cycle))
                .ForMember(d => d.Status, m => m.MapFrom(e => (BdoPartner.Plans.Common.Enumerations.PartnerReferenceDataUploadStatus)e.Status))
                .ForMember(d => d.StatusString, m => m.MapFrom(e => GetPartnerReferenceDataUploadStatusString(e.Status)))
                .ForMember(d => d.CycleString, m => m.MapFrom(e => GetPartnerReferenceDataCycleString(e.Cycle)))
                .ForMember(d => d.TotalRecords, m => m.Ignore()) // Will be populated by service layer
                .ForMember(d => d.ValidRecords, m => m.Ignore()) // Will be populated by service layer
                .ForMember(d => d.InvalidRecords, m => m.Ignore()) // Will be populated by service layer
                .ForMember(d => d.PartnerReferenceDataUploadDetails, m => m.MapFrom(e => e.PartnerReferenceDataUploadDetails))
                .ForMember(d => d.Meta, m => m.MapFrom(e => e.Meta));

            CreateMap<DTO.PartnerReferenceDataUpload, Entity.PartnerReferenceDataUpload>()
                .ForMember(e => e.Cycle, m => m.MapFrom(d => (byte)d.Cycle))
                .ForMember(e => e.Status, m => m.MapFrom(d => (byte)d.Status))
                .ForMember(e => e.Meta, m => m.Ignore()) // Ignore navigation properties
                .ForMember(e => e.PartnerReferenceDataUploadDetails, m => m.Ignore()); // Ignore navigation properties

            // PartnerReferenceDataUploadDetails mappings
            CreateMap<Entity.PartnerReferenceDataUploadDetails, DTO.PartnerReferenceDataUploadDetails>()
                .ForMember(d => d.ColumnData, m => m.Ignore()); // Will be populated by service layer

            CreateMap<DTO.PartnerReferenceDataUploadDetails, Entity.PartnerReferenceDataUploadDetails>()
                .ForMember(e => e.PartnerReferenceDataUpload, m => m.Ignore()); // Ignore navigation properties

            // PartnerReferenceData mappings
            CreateMap<Entity.PartnerReferenceData, DTO.PartnerReferenceData>()
                .ForMember(d => d.Cycle, m => m.MapFrom(e => (BdoPartner.Plans.Common.Enumerations.PartnerReferenceDataCycle)e.Cycle))
                .ForMember(d => d.CycleString, m => m.MapFrom(e => GetPartnerReferenceDataCycleString(e.Cycle)))
                .ForMember(d => d.CreatedByName, m => m.Ignore()) // Will be populated by service layer
                .ForMember(d => d.ModifiedByName, m => m.Ignore()) // Will be populated by service layer
                .ForMember(d => d.Partner, m => m.MapFrom(e => e.Partner))
                .ForMember(d => d.Meta, m => m.MapFrom(e => e.Meta))
                .ForMember(d => d.PartnerName, m => m.MapFrom(e => e.Partner != null ? e.Partner.DisplayName : null))
                .ForMember(d => d.EmployeeId, m => m.MapFrom(e => e.Partner != null ? e.Partner.EmployeeId.ToString() : null));

            CreateMap<DTO.PartnerReferenceData, Entity.PartnerReferenceData>()
                .ForMember(e => e.Cycle, m => m.MapFrom(d => (byte)d.Cycle))
                .ForMember(e => e.Partner, m => m.Ignore()) // Ignore navigation properties
                .ForMember(e => e.Meta, m => m.Ignore()); // Ignore navigation properties
        }

        /// <summary>
        /// Get status string for Questionnaire
        /// </summary>
        /// <param name="status">Status byte value</param>
        /// <returns>Status string</returns>
        private static string GetQuestionnaireStatusString(byte status)
        {
            return status switch
            {
                (byte)BdoPartner.Plans.Common.Enumerations.QuestionnaireStatus.Draft => "Draft",
                (byte)BdoPartner.Plans.Common.Enumerations.QuestionnaireStatus.Published => "Published",
                (byte)BdoPartner.Plans.Common.Enumerations.QuestionnaireStatus.Archived => "Archived",
                _ => "Unknown"
            };
        }

        /// <summary>
        /// Get status string for Form
        /// </summary>
        /// <param name="status">Status byte value</param>
        /// <returns>Status string</returns>
        private static string GetFormStatusString(byte status)
        {
            return status switch
            {
                (byte)BdoPartner.Plans.Common.Enumerations.FormStatus.Draft => "Draft",
                (byte)BdoPartner.Plans.Common.Enumerations.FormStatus.Submitted => "Submitted",
                (byte)BdoPartner.Plans.Common.Enumerations.FormStatus.Approved => "Approved",
                (byte)BdoPartner.Plans.Common.Enumerations.FormStatus.Rejected => "Rejected",
                (byte)BdoPartner.Plans.Common.Enumerations.FormStatus.Reopened => "Reopened",
                _ => "Unknown"
            };
        }

        /// <summary>
        /// Get status string for PartnerReviewerUpload
        /// </summary>
        /// <param name="status">Status byte value</param>
        /// <returns>Status string</returns>
        private static string GetPartnerReviewerUploadStatusString(byte status)
        {
            return status switch
            {
                (byte)BdoPartner.Plans.Common.Enumerations.PartnerReviewerUploadStatus.Uploading => "Uploading",
                (byte)BdoPartner.Plans.Common.Enumerations.PartnerReviewerUploadStatus.Uploaded => "Uploaded",
                (byte)BdoPartner.Plans.Common.Enumerations.PartnerReviewerUploadStatus.Validating => "Validating",
                (byte)BdoPartner.Plans.Common.Enumerations.PartnerReviewerUploadStatus.ValidationPassed => "Validation Passed",
                (byte)BdoPartner.Plans.Common.Enumerations.PartnerReviewerUploadStatus.ValidationFailed => "Validation Failed",
                (byte)BdoPartner.Plans.Common.Enumerations.PartnerReviewerUploadStatus.Submitted => "Submitted",
                _ => "Unknown"
            };
        }

        /// <summary>
        /// Get status string for PartnerReferenceDataUpload
        /// </summary>
        /// <param name="status">Status byte value</param>
        /// <returns>Status string</returns>
        private static string GetPartnerReferenceDataUploadStatusString(byte status)
        {
            return status switch
            {
                (byte)BdoPartner.Plans.Common.Enumerations.PartnerReferenceDataUploadStatus.Uploading => "Uploading",
                (byte)BdoPartner.Plans.Common.Enumerations.PartnerReferenceDataUploadStatus.Uploaded => "Uploaded",
                (byte)BdoPartner.Plans.Common.Enumerations.PartnerReferenceDataUploadStatus.Validating => "Validating",
                (byte)BdoPartner.Plans.Common.Enumerations.PartnerReferenceDataUploadStatus.ValidationPassed => "Validation Passed",
                (byte)BdoPartner.Plans.Common.Enumerations.PartnerReferenceDataUploadStatus.ValidationFailed => "Validation Failed",
                (byte)BdoPartner.Plans.Common.Enumerations.PartnerReferenceDataUploadStatus.Submitted => "Submitted",
                _ => "Unknown"
            };
        }

        /// <summary>
        /// Get cycle string for PartnerReferenceData
        /// </summary>
        /// <param name="cycle">Cycle byte value</param>
        /// <returns>Cycle string</returns>
        private static string GetPartnerReferenceDataCycleString(byte cycle)
        {
            return cycle switch
            {
                (byte)BdoPartner.Plans.Common.Enumerations.PartnerReferenceDataCycle.Planning => "Planning",
                (byte)BdoPartner.Plans.Common.Enumerations.PartnerReferenceDataCycle.MidYearReview => "Mid Year Review",
                (byte)BdoPartner.Plans.Common.Enumerations.PartnerReferenceDataCycle.EndYearReview => "End Year Review",
                _ => "Unknown"
            };
        }

        /// <summary>
        /// Get column data type string for PartnerReferenceDataMetaDetails
        /// </summary>
        /// <param name="columnDataType">Column data type byte value</param>
        /// <returns>Column data type string</returns>
        private static string GetPartnerReferenceDataColumnTypeString(byte columnDataType)
        {
            return columnDataType switch
            {
                (byte)BdoPartner.Plans.Common.Enumerations.PartnerReferenceDataColumnType.Text => "Text",
                (byte)BdoPartner.Plans.Common.Enumerations.PartnerReferenceDataColumnType.Numeric => "Numeric",
                (byte)BdoPartner.Plans.Common.Enumerations.PartnerReferenceDataColumnType.Blank => "Blank",
                _ => "Unknown"
            };
        }
    }
}
