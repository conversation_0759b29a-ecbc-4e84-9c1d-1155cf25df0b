﻿using AutoMapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BdoPartner.Plans.Model.Mapper
{
    /// <summary>
    ///  Work for lookup data binding
    /// </summary>
    public class LookupProfile : Profile
    {
        public LookupProfile()
        {
            CreateMap<Entity.Language, DTO.LookupNum>()
                   .ForMember(dto => dto.Key, conf => conf.MapFrom(e => e.Id))
                   .ForMember(dto => dto.Value, conf => conf.MapFrom(e => e.Name));
                      
            CreateMap<Entity.Notification, DTO.Notification>();

            CreateMap<DTO.Notification, Entity.Notification>();
                      
            // Partner lookup mappings
            CreateMap<Entity.Partner, DTO.Lookup>()
                   .ForMember(dto => dto.Key, conf => conf.MapFrom(e => e.Id.ToString()))
                   .ForMember(dto => dto.Value, conf => conf.MapFrom(e => e.DisplayName ?? $"{e.FirstName} {e.LastName}"));

            // Questionnaire lookup mappings
            CreateMap<Entity.Questionnaire, DTO.Lookup>()
                   .ForMember(dto => dto.Key, conf => conf.MapFrom(e => e.Id.ToString()))
                   .ForMember(dto => dto.Value, conf => conf.MapFrom(e => $"{e.Name} ({e.Year})"));

            // Form lookup mappings (if needed for dropdowns)
            CreateMap<Entity.Form, DTO.Lookup>()
                   .ForMember(dto => dto.Key, conf => conf.MapFrom(e => e.Id.ToString()))
                   .ForMember(dto => dto.Value, conf => conf.MapFrom(e => $"{e.PartnerName} - {e.Year}"));

        }
    }
}
