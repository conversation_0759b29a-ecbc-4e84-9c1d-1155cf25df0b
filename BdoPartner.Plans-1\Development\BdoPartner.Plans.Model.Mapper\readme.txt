﻿Auto Mapper Reference: 
https://automapper.org/
https://docs.automapper.org/en/latest/Getting-started.html

AutoMapper is an object-object mapper. Object-object mapping works by transforming an input object of one type 
into an output object of a different type. 

What makes AutoMapper interesting is that it provides some interesting conventions to take the dirty work out of figuring out 
how to map type A to type B. As long as type B follows AutoMapper’s established convention, 
almost zero configuration is needed to map two types.