﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net5.0</TargetFramework>
    <UserSecretsId>fff211c1-584b-41a5-9fe6-9f6bb6728da4</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
	<SpaRoot>SPA\</SpaRoot>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Logs\**" />
    <Content Remove="Logs\**" />
    <EmbeddedResource Remove="Logs\**" />
    <None Remove="Logs\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.10.14" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\BdoPartner.Plans.Web.Common\BdoPartner.Plans.Web.Common.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="appsettings.Production.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Update="SPA\AngularApp\assets\config\config.dev.json">
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </Content>
    <Content Update="SPA\AngularApp\assets\config\config.prod.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <None Update="SPA\AngularApp\assets\images\logo.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SPA\AngularApp\index.html">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SPA\AngularApp\readme.txt">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SPA\ReactApp\readme.txt">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <Target Name="PublishRunWebpack" AfterTargets="ComputeFilesToPublish">
		<!-- Include the newly-built files in the publish output -->
		<ItemGroup>
			<DistFiles Include="$(SpaRoot)\**;" />
			<ResolvedFileToPublish Include="@(DistFiles->'%(FullPath)')" Exclude="@(ResolvedFileToPublish)">
				<RelativePath>%(DistFiles.Identity)</RelativePath>
				<CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
				<ExcludeFromSingleFile>true</ExcludeFromSingleFile>
			</ResolvedFileToPublish>
		</ItemGroup>
   </Target>
</Project>
