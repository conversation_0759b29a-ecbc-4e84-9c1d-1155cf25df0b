﻿using System;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using BdoPartner.Plans.Common.Config;
using DTO = BdoPartner.Plans.Model.DTO;
using System.Linq;
using IdentityModel;
using Microsoft.AspNetCore.Http;
using BdoPartner.Plans.Model.DTO.Identity;

namespace BdoPartner.Plans.Web.API.Controllers
{
   
    public class BaseController : ControllerBase
    {
        #region Basic Properties
        private readonly ILogger<BaseController> _logger;
        protected ILogger Logger { get { return _logger; } }

        private readonly IConfigSettings _config;
        protected IConfigSettings Config { get { return _config; } }

        protected IHttpContextAccessor httpContextAccessor = null;
        #endregion

        public BaseController(IHttpContextAccessor httpContextAccessor, ILogger<BaseController> logger, IConfigSettings config)
        {
            this.httpContextAccessor = httpContextAccessor;
            this._logger = logger;
            this._config = config;
        }

        /// <summary>
        ///  Get current logon user from HttpContext.User. Web associated.
        /// </summary>
        protected DTO.Identity.User CurrentUser
        {
            get
            {
                if (this.User.Identity.IsAuthenticated)
                {
                    return this.User.ToIdentityUser();
                }
                else
                {
                    return null;
                }
            }
        }

        /// <summary>
        ///  Central handle current date and time.
        /// </summary>
        protected DateTime CurrentDateTime
        {
            get { return DateTime.Now; }
        }
    }
}