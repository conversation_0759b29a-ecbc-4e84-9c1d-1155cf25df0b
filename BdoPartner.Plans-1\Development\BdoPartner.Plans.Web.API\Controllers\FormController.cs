using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using BdoPartner.Plans.Business.Interface;
using BdoPartner.Plans.Common;
using BdoPartner.Plans.Common.Config;
using BdoPartner.Plans.Model.DTO;
using BdoPartner.Plans.Web.Common;
using System;
using System.Collections.Generic;
using System.Linq;

namespace BdoPartner.Plans.Web.API.Controllers
{
    /// <summary>
    /// API Controller for Form management operations
    /// </summary>
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class FormController : BaseController
    {
        private readonly IFormService _formService;

        public FormController(IFormService formService, IHttpContextAccessor httpContextAccessor, 
            ILogger<FormController> logger, IConfigSettings config) : 
            base(httpContextAccessor, logger, config)
        {
            _formService = formService;
        }

        /// <summary>
        /// Get all active forms
        /// </summary>
        /// <returns>List of forms</returns>
        //[BDOAuthorize(Enumerations.Permission.AdminAccess)]
        [Route("[Action]")]
        [HttpGet]
        public ActionResult GetForms()
        {
            return Ok(_formService.GetForms());
        }

        /// <summary>
        /// Search forms with advanced filtering and pagination
        /// </summary>
        /// <param name="searchTerm">Search term for partner name</param>
        /// <param name="questionnaireId">Filter by questionnaire ID</param>
        /// <param name="year">Filter by year</param>
        /// <param name="status">Filter by status (0=Draft, 1=Submitted, 2=Approved, 3=Rejected, 4=Reopened)</param>
        /// <param name="partnerObjectId">Filter by partner object ID</param>
        /// <param name="isActive">Filter by active status</param>
        /// <param name="pageNumber">Page number (default: 1)</param>
        /// <param name="pageSize">Page size (default: 50)</param>
        /// <returns>Paginated list of forms</returns>
        [Authorize]
        [Route("[Action]")]
        [HttpGet]
        public ActionResult SearchForms(string searchTerm = null, Guid? questionnaireId = null,
            short? year = null, byte? status = null, string partnerObjectId = null,
            bool? isActive = null, int pageNumber = 1, int pageSize = 50)
        {
            return Ok(_formService.SearchForms(searchTerm, questionnaireId,
                year, status, partnerObjectId, isActive, pageNumber, pageSize));
        }

        /// <summary>
        /// Get form by ID
        /// </summary>
        /// <param name="id">Form ID</param>
        /// <returns>Form details</returns>
        [Authorize]
        [Route("[Action]")]
        [HttpGet]
        public ActionResult GetFormById(Guid id)
        {
            return Ok(_formService.GetFormById(id));
        }

        /// <summary>
        /// Get forms by questionnaire ID
        /// </summary>
        /// <param name="questionnaireId">Questionnaire ID</param>
        /// <returns>List of forms for the questionnaire</returns>
        [Authorize]
        [Route("[Action]")]
        [HttpGet]
        public ActionResult GetFormsByQuestionnaireId(Guid questionnaireId)
        {
            return Ok(_formService.GetFormsByQuestionnaireId(questionnaireId));
        }

        /// <summary>
        /// Get forms by partner object ID
        /// </summary>
        /// <param name="partnerObjectId">Partner object ID</param>
        /// <returns>List of forms for the partner</returns>
        [Authorize]
        [Route("[Action]")]
        [HttpGet]
        public ActionResult GetFormsByPartnerObjectId(string partnerObjectId)
        {
            return Ok(_formService.GetFormsByPartnerObjectId(partnerObjectId));
        }

        /// <summary>
        /// Get forms by partner user ID
        /// </summary>
        /// <param name="partnerUserId">Partner user ID</param>
        /// <returns>List of forms for the partner user</returns>
        [Authorize]
        [Route("[Action]")]
        [HttpGet]
        public ActionResult GetFormsByPartnerUserId(Guid partnerUserId)
        {
            return Ok(_formService.GetFormsByPartnerUserId(partnerUserId));
        }

        /// <summary>
        /// Create a new form
        /// </summary>
        /// <param name="form">Form data</param>
        /// <returns>Created form</returns>
        [Authorize]
        [Route("[Action]")]
        [HttpPost]
        public ActionResult CreateForm([FromBody] Form form)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var result = _formService.CreateForm(form);
            return Ok(result);
        }

        /// <summary>
        /// Update an existing form
        /// </summary>
        /// <param name="form">Updated form data</param>
        /// <returns>Updated form</returns>
        [Authorize]
        [Route("[Action]")]
        [HttpPost]
        public ActionResult UpdateForm([FromBody] Form form)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var result = _formService.UpdateForm(form);
            return Ok(result);
        }

        /// <summary>
        /// Delete a form (soft delete)
        /// </summary>
        /// <param name="id">Form ID</param>
        /// <returns>Deletion result</returns>
        [BDOAuthorize(Enumerations.Role.PPAdministrator)]
        [Route("[Action]")]
        [HttpPost]
        public ActionResult DeleteForm(Guid id)
        {
            var result = _formService.DeleteForm(id);
            return Ok(result);
        }

        /// <summary>
        /// Submit a form (change status to Submitted)
        /// </summary>
        /// <param name="id">Form ID</param>
        /// <returns>Submitted form</returns>
        [Authorize]
        [Route("[Action]")]
        [HttpPost]
        public ActionResult SubmitForm(Guid id)
        {
            var result = _formService.SubmitForm(id);
            return Ok(result);
        }

        /// <summary>
        /// Approve a form (change status to Approved)
        /// </summary>
        /// <param name="id">Form ID</param>
        /// <param name="approverComments">Optional approver comments</param>
        /// <returns>Approved form</returns>
        [BDOAuthorize(Enumerations.Role.PPAdministrator)]
        [Route("[Action]")]
        [HttpPost]
        public ActionResult ApproveForm(Guid id, [FromBody] string approverComments = null)
        {
            var result = _formService.ApproveForm(id, approverComments);
            return Ok(result);
        }

        /// <summary>
        /// Reject a form (change status to Rejected)
        /// </summary>
        /// <param name="id">Form ID</param>
        /// <param name="rejectionComments">Rejection comments</param>
        /// <returns>Rejected form</returns>
        [BDOAuthorize(Enumerations.Role.PPAdministrator)]
        [Route("[Action]")]
        [HttpPost]
        public ActionResult RejectForm(Guid id, [FromBody] string rejectionComments)
        {
            if (string.IsNullOrEmpty(rejectionComments))
            {
                return BadRequest("Rejection comments are required");
            }

            var result = _formService.RejectForm(id, rejectionComments);
            return Ok(result);
        }

        /// <summary>
        /// Get or create form for current user and questionnaire
        /// </summary>
        /// <param name="questionnaireId">Questionnaire ID</param>
        /// <param name="year">Year (optional, defaults to current year)</param>
        /// <returns>Form object (existing or newly created)</returns>
        [Authorize]
        [Route("[Action]")]
        [HttpGet]
        public ActionResult GetOrCreateFormForCurrentUser(Guid questionnaireId, short? year = null)
        {
            try
            {
                var targetYear = year ?? (short)DateTime.Now.Year;
                var partnerUserId = CurrentUser?.Id ?? Guid.Empty;
                var partnerObjectId = GetUserClaim("oid"); // Azure AD Object ID
                var partnerName = CurrentUser?.DisplayName ?? GetUserClaim("name");

                if (partnerUserId == Guid.Empty)
                {
                    return BadRequest("User ID not found");
                }

                var result = _formService.GetOrCreateFormForUser(questionnaireId, targetYear, partnerUserId, partnerObjectId, partnerName);
                return Ok(result);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error getting or creating form for current user");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get form with user answers for current user
        /// </summary>
        /// <param name="questionnaireId">Questionnaire ID</param>
        /// <param name="year">Year (optional, defaults to current year)</param>
        /// <returns>Form with user answers</returns>
        [Authorize]
        [Route("[Action]")]
        [HttpGet]
        public ActionResult GetFormWithUserAnswersForCurrentUser(Guid questionnaireId, short? year = null)
        {
            try
            {
                var targetYear = year ?? (short)DateTime.Now.Year;
                var partnerUserId = CurrentUser?.Id ?? Guid.Empty;

                if (partnerUserId == Guid.Empty)
                {
                    return BadRequest("User ID not found");
                }

                var result = _formService.GetFormWithUserAnswers(questionnaireId, targetYear, partnerUserId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error getting form with user answers for current user");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get current year active questionnaire
        /// </summary>
        /// <param name="year">Year (optional, defaults to current year)</param>
        /// <returns>Active questionnaire for the year</returns>
        [Authorize]
        [Route("[Action]")]
        [HttpGet]
        public ActionResult GetCurrentYearActiveQuestionnaire(short? year = null)
        {
            try
            {
                var result = _formService.GetCurrentYearActiveQuestionnaire(year);
                return Ok(result);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error getting current year active questionnaire");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get my plan - consolidated method that gets questionnaire, creates/gets form, and loads user answers
        /// </summary>
        /// <param name="year">Year (optional, defaults to current year)</param>
        /// <returns>Complete plan data including questionnaire, form, and user answers</returns>
        [Authorize]
        [Route("[Action]")]
        [HttpGet]
        public ActionResult GetMyPlan(short? year = null)
        {
            try
            {
                var targetYear = year ?? (short)DateTime.Now.Year;
                var partnerUserId = CurrentUser?.Id ?? Guid.Empty;
                var partnerObjectId = GetUserClaim("oid"); // Azure AD Object ID
                var partnerName = CurrentUser?.DisplayName ?? GetUserClaim("name");

                if (partnerUserId == Guid.Empty)
                {
                    return BadRequest("User ID not found");
                }

                var result = _formService.GetMyPlan(targetYear, partnerUserId, partnerObjectId, partnerName);
                return Ok(result);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error getting my plan");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Helper method to get user claim value
        /// </summary>
        /// <param name="claimType">Claim type</param>
        /// <returns>Claim value</returns>
        private string GetUserClaim(string claimType)
        {
            return HttpContext.User?.Claims?.FirstOrDefault(c => c.Type == claimType)?.Value;
        }
    }
}
