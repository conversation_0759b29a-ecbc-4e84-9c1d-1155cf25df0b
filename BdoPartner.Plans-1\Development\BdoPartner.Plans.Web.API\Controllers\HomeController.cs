﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BdoPartner.Plans.Web.API.Controllers
{
    /// <summary>
    ///  Showing general information when resource web api is up.
    /// </summary>
    public class HomeController : Microsoft.AspNetCore.Mvc.Controller
    {
        private readonly ILogger<HomeController> _logger;

        public HomeController(ILogger<HomeController> logger)
        {
            _logger = logger;
        }

        /// <summary>
        ///  Test purpose only.
        /// </summary>
        /// <returns></returns>
        public IActionResult Index()
        {
            return Content("BdoPartner.Plans Resource Web API");
        }

        /// <summary>
        /// Shows the error page
        /// </summary>
        public IActionResult Error(string errorId)
        {
            return Content("Internal exception happened.");
        }
    }
}
