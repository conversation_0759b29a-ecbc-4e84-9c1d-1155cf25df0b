﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using BdoPartner.Plans.Business.Interface;
using BdoPartner.Plans.Common;
using BdoPartner.Plans.Common.Config;
using BdoPartner.Plans.Model.DTO;
using BdoPartner.Plans.Web.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BdoPartner.Plans.Web.API.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class LookupController : BaseController
    {
        ILookupService _lookupService;
        public LookupController(ILookupService lookupService, IHttpContextAccessor httpContextAccessor, ILogger<LookupController> logger, IConfigSettings config) : 
            base(httpContextAccessor, logger, config)
        {
            _lookupService = lookupService;
        }

        [Route("[Action]")]
        [HttpGet()]
        public ActionResult GetLanguages()
        {
            return Ok(_lookupService.GetLanguages(false));
        }


        [Route("[Action]")]
        [HttpGet()]
        public ActionResult GetNotifications()
        {
            return Ok(_lookupService.GetNotifications());
        }
                

        /// <summary>
        /// Get all form statuses with localized display names
        /// </summary>
        /// <param name="includeEmptyRow">Include empty row for dropdown selection</param>
        /// <returns>List of form statuses</returns>
        [Route("[Action]")]
        [HttpGet()]
        public ActionResult GetFormStatuses([FromQuery] bool includeEmptyRow = false)
        {
            try
            {
                var result = _lookupService.GetFormStatuses(includeEmptyRow);
                return Ok(new BusinessResult<List<LookupNum>>
                {
                    Item = result,
                    ResultStatus = ResultStatus.Success
                });
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error getting form statuses");
                return Ok(new BusinessResult<List<LookupNum>>
                {
                    ResultStatus = ResultStatus.Failure,
                    Message = "Error retrieving form statuses"
                });
            }
        }

        /// <summary>
        /// Get all questionnaire statuses with localized display names
        /// </summary>
        /// <param name="includeEmptyRow">Include empty row for dropdown selection</param>
        /// <returns>List of questionnaire statuses</returns>
        [Route("[Action]")]
        [HttpGet()]
        public ActionResult GetQuestionnaireStatuses([FromQuery] bool includeEmptyRow = false)
        {
            try
            {
                var result = _lookupService.GetQuestionnaireStatuses(includeEmptyRow);
                return Ok(new BusinessResult<List<LookupNum>>
                {
                    Item = result,
                    ResultStatus = ResultStatus.Success
                });
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error getting questionnaire statuses");
                return Ok(new BusinessResult<List<LookupNum>>
                {
                    ResultStatus = ResultStatus.Failure,
                    Message = "Error retrieving questionnaire statuses"
                });
            }
        }

    }
}
