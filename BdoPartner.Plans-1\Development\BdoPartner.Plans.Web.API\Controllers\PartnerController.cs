using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using BdoPartner.Plans.Business.Interface;
using BdoPartner.Plans.Common;
using BdoPartner.Plans.Common.Config;
using BdoPartner.Plans.Model.DTO;
using BdoPartner.Plans.Web.Common;
using System;
using System.Collections.Generic;

namespace BdoPartner.Plans.Web.API.Controllers
{
    /// <summary>
    /// API Controller for Partner management operations
    /// </summary>
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class PartnerController : BaseController
    {
        private readonly IPartnerService _partnerService;

        public PartnerController(IPartnerService partnerService, IHttpContextAccessor httpContextAccessor, 
            ILogger<PartnerController> logger, IConfigSettings config) : 
            base(httpContextAccessor, logger, config)
        {
            _partnerService = partnerService;
        }

        /// <summary>
        /// Get all active partners
        /// </summary>
        /// <returns>List of partners</returns>
        [BDOAuthorize(Enumerations.Role.PPAdministrator)]
        [Route("[Action]")]
        [HttpGet]
        public ActionResult GetPartners()
        {
            return Ok(_partnerService.GetPartners());
        }

        /// <summary>
        /// Search partners with advanced filtering and pagination
        /// </summary>
        /// <param name="searchTerm">Search term for partner name or email</param>
        /// <param name="partnerType">Filter by partner type</param>
        /// <param name="department">Filter by department</param>
        /// <param name="location">Filter by location</param>
        /// <param name="isActive">Filter by active status</param>
        /// <param name="pageNumber">Page number (default: 1)</param>
        /// <param name="pageSize">Page size (default: 50)</param>
        /// <returns>Paginated list of partners</returns>
        [BDOAuthorize(Enumerations.Role.PPAdministrator)]
        [Route("[Action]")]
        [HttpGet]
        public ActionResult SearchPartners(string searchTerm = null,
            string partnerType = null, string department = null, string location = null,
            bool? isActive = null, int pageNumber = 1, int pageSize = 50)
        {
            return Ok(_partnerService.SearchPartners(searchTerm, partnerType,
                department, location, isActive, pageNumber, pageSize));
        }

        /// <summary>
        /// Get partner by ID
        /// </summary>
        /// <param name="id">Partner ID</param>
        /// <returns>Partner details</returns>
        [Authorize]
        [Route("[Action]")]
        [HttpGet]
        public ActionResult GetPartnerById(Guid id)
        {
            return Ok(_partnerService.GetPartnerById(id));
        }

        /// <summary>
        /// Get partner by employee ID
        /// </summary>
        /// <param name="employeeId">Employee ID</param>
        /// <returns>Partner details</returns>
        [Authorize]
        [Route("[Action]")]
        [HttpGet]
        public ActionResult GetPartnerByEmployeeId(int employeeId)
        {
            return Ok(_partnerService.GetPartnerByEmployeeId(employeeId));
        }

        /// <summary>
        /// Create a new partner
        /// </summary>
        /// <param name="partner">Partner data</param>
        /// <returns>Created partner</returns>
        [BDOAuthorize(Enumerations.Role.PPAdministrator)]
        [Route("[Action]")]
        [HttpPost]
        public ActionResult CreatePartner([FromBody] Partner partner)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var result = _partnerService.CreatePartner(partner);
            return Ok(result);
        }

        /// <summary>
        /// Update an existing partner
        /// </summary>
        /// <param name="partner">Updated partner data</param>
        /// <returns>Updated partner</returns>
        [BDOAuthorize(Enumerations.Role.PPAdministrator)]
        [Route("[Action]")]
        [HttpPost]
        public ActionResult UpdatePartner([FromBody] Partner partner)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var result = _partnerService.UpdatePartner(partner);
            return Ok(result);
        }

        /// <summary>
        /// Delete a partner (soft delete)
        /// </summary>
        /// <param name="id">Partner ID</param>
        /// <returns>Deletion result</returns>
        [BDOAuthorize(Enumerations.Role.PPAdministrator)]
        [Route("[Action]")]
        [HttpPost]
        public ActionResult DeletePartner(Guid id)
        {
            var result = _partnerService.DeletePartner(id);
            return Ok(result);
        }

        /// <summary>
        /// Get partners for lookup/dropdown purposes
        /// </summary>
        /// <param name="includeInactive">Include inactive partners</param>
        /// <returns>List of lookup items</returns>
        [Authorize]
        [Route("[Action]")]
        [HttpGet]
        public ActionResult GetPartnersLookup(bool includeInactive = false)
        {
            return Ok(_partnerService.GetPartnersLookup(includeInactive));
        }
    }
}
