using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using BdoPartner.Plans.Business.Interface;
using BdoPartner.Plans.Common;
using BdoPartner.Plans.Common.Config;
using BdoPartner.Plans.Model.DTO;
using BdoPartner.Plans.Web.Common;
using System;
using BdoPartner.Plans.DataAccess.Common.PagedList;
using System.Threading.Tasks;

namespace BdoPartner.Plans.Web.API.Controllers
{
    /// <summary>
    /// API Controller for Partner Reference Data Upload management operations
    /// Only System Administrators can access these endpoints
    /// </summary>
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class PartnerReferenceDataUploadController : BaseController
    {
        private readonly IPartnerReferenceDataUploadService _partnerReferenceDataUploadService;

        public PartnerReferenceDataUploadController(IPartnerReferenceDataUploadService partnerReferenceDataUploadService, 
            IHttpContextAccessor httpContextAccessor, ILogger<PartnerReferenceDataUploadController> logger, 
            IConfigSettings config) : base(httpContextAccessor, logger, config)
        {
            _partnerReferenceDataUploadService = partnerReferenceDataUploadService;
        }

        #region Metadata Operations

        /// <summary>
        /// Get all partner reference data metadata
        /// </summary>
        /// <returns>List of metadata</returns>
        [BDOAuthorize(Enumerations.Role.PPAdministrator)]
        [Route("[Action]")]
        [HttpGet]
        public ActionResult GetPartnerReferenceDataMetas()
        {
            return Ok(_partnerReferenceDataUploadService.GetPartnerReferenceDataMetas());
        }

        /// <summary>
        /// Get partner reference data metadata by ID
        /// </summary>
        /// <param name="id">Metadata ID</param>
        /// <returns>Metadata object</returns>
        [BDOAuthorize(Enumerations.Role.PPAdministrator)]
        [Route("[Action]")]
        [HttpGet]
        public ActionResult GetPartnerReferenceDataMetaById(Guid id)
        {
            return Ok(_partnerReferenceDataUploadService.GetPartnerReferenceDataMetaById(id));
        }

        /// <summary>
        /// Extract metadata from uploaded file
        /// </summary>
        /// <param name="file">Excel or CSV file</param>
        /// <param name="year">Year for the metadata</param>
        /// <param name="cycle">Cycle for the metadata</param>
        /// <returns>Extracted metadata</returns>
        [BDOAuthorize(Enumerations.Role.PPAdministrator)]
        [Route("[Action]")]
        [HttpPost]
        public async Task<ActionResult> ExtractMetadataFromFile([FromForm] IFormFile file, [FromForm] short year, [FromForm] byte cycle)
        {
            var result = await _partnerReferenceDataUploadService.ExtractMetadataFromFileAsync(file, year, cycle);
            return Ok(result);
        }

        #endregion

        #region Upload Operations

        /// <summary>
        /// Get all partner reference data uploads
        /// </summary>
        /// <returns>List of uploads</returns>
        [BDOAuthorize(Enumerations.Role.PPAdministrator)]
        [Route("[Action]")]
        [HttpGet]
        public ActionResult GetPartnerReferenceDataUploads()
        {
            return Ok(_partnerReferenceDataUploadService.GetPartnerReferenceDataUploads());
        }

        /// <summary>
        /// Search partner reference data uploads with filtering and pagination
        /// </summary>
        /// <param name="year">Filter by year</param>
        /// <param name="cycle">Filter by cycle</param>
        /// <param name="status">Filter by status</param>
        /// <param name="pageIndex">Page index (0-based)</param>
        /// <param name="pageSize">Page size</param>
        /// <returns>Paginated list of uploads</returns>
        [BDOAuthorize(Enumerations.Role.PPAdministrator)]
        [Route("[Action]")]
        [HttpGet]
        public ActionResult SearchPartnerReferenceDataUploads(short? year = null, byte? cycle = null, 
            byte? status = null, int pageIndex = 0, int pageSize = 20)
        {
            return Ok(_partnerReferenceDataUploadService.SearchPartnerReferenceDataUploads(year, cycle, status, pageIndex, pageSize));
        }

        /// <summary>
        /// Get partner reference data upload by ID
        /// </summary>
        /// <param name="id">Upload ID</param>
        /// <returns>Upload object</returns>
        [BDOAuthorize(Enumerations.Role.PPAdministrator)]
        [Route("[Action]")]
        [HttpGet]
        public ActionResult GetPartnerReferenceDataUploadById(Guid id)
        {
            return Ok(_partnerReferenceDataUploadService.GetPartnerReferenceDataUploadById(id));
        }

        /// <summary>
        /// Get partner reference data upload details by upload ID
        /// </summary>
        /// <param name="uploadId">Upload ID</param>
        /// <param name="includeValidOnly">Include only valid records</param>
        /// <param name="includeInvalidOnly">Include only invalid records</param>
        /// <returns>List of upload details</returns>
        [BDOAuthorize(Enumerations.Role.PPAdministrator)]
        [Route("[Action]")]
        [HttpGet]
        public ActionResult GetPartnerReferenceDataUploadDetails(Guid uploadId, 
            bool includeValidOnly = false, bool includeInvalidOnly = false)
        {
            return Ok(_partnerReferenceDataUploadService.GetPartnerReferenceDataUploadDetails(uploadId, includeValidOnly, includeInvalidOnly));
        }

        /// <summary>
        /// Upload Excel or CSV file for partner reference data
        /// </summary>
        /// <param name="file">Excel or CSV file</param>
        /// <param name="year">Year for the upload</param>
        /// <param name="cycle">Cycle for the upload</param>
        /// <returns>Upload result</returns>
        [BDOAuthorize(Enumerations.Role.PPAdministrator)]
        [Route("[Action]")]
        [HttpPost]
        public async Task<ActionResult> UploadFile([FromForm] IFormFile file, [FromForm] short year, [FromForm] byte cycle)
        {
            var result = await _partnerReferenceDataUploadService.UploadFileAsync(file, year, cycle);
            return Ok(result);
        }

        /// <summary>
        /// Validate uploaded data
        /// </summary>
        /// <param name="uploadId">Upload ID to validate</param>
        /// <returns>Validation result</returns>
        [BDOAuthorize(Enumerations.Role.PPAdministrator)]
        [Route("[Action]")]
        [HttpGet]
        public async Task<ActionResult> ValidateUpload(Guid uploadId)
        {
            var result = await _partnerReferenceDataUploadService.ValidateUploadAsync(uploadId);
            return Ok(result);
        }

        /// <summary>
        /// Submit validated data to final PartnerReferenceData table.
        /// By default (overwriteExisting = true), replaces existing data for the same partner/year/cycle.
        /// </summary>
        /// <param name="uploadId">Upload ID to submit</param>
        /// <param name="overwriteExisting">Default true. When true, replaces existing data for the same partner/year/cycle.</param>
        /// <returns>Submit result</returns>
        [BDOAuthorize(Enumerations.Role.PPAdministrator)]
        [Route("[Action]")]
        [HttpPost]
        public async Task<ActionResult> SubmitUpload(Guid uploadId, bool overwriteExisting = true)
        {
            var result = await _partnerReferenceDataUploadService.SubmitUploadAsync(uploadId, overwriteExisting);
            return Ok(result);
        }

        /// <summary>
        /// Delete partner reference data upload
        /// </summary>
        /// <param name="uploadId">Upload ID to delete</param>
        /// <returns>Delete result</returns>
        [BDOAuthorize(Enumerations.Role.PPAdministrator)]
        [Route("[Action]")]
        [HttpDelete]
        public ActionResult DeleteUpload(Guid uploadId)
        {
            return Ok(_partnerReferenceDataUploadService.DeleteUpload(uploadId));
        }

        #endregion

        #region Reference Data Operations

        /// <summary>
        /// Get partner reference data for a specific partner, year, and cycle
        /// </summary>
        /// <param name="partnerId">Partner ID</param>
        /// <param name="year">Year</param>
        /// <param name="cycle">Cycle</param>
        /// <returns>Partner reference data</returns>
        [BDOAuthorize(Enumerations.Role.PPAdministrator)]
        [Route("[Action]")]
        [HttpGet]
        public ActionResult GetPartnerReferenceData(Guid partnerId, short year, byte cycle)
        {
            return Ok(_partnerReferenceDataUploadService.GetPartnerReferenceData(partnerId, year, cycle));
        }

        /// <summary>
        /// Search partner reference data with filtering and pagination
        /// </summary>
        /// <param name="year">Filter by year</param>
        /// <param name="cycle">Filter by cycle</param>
        /// <param name="partnerId">Filter by partner ID</param>
        /// <param name="pageIndex">Page index (0-based)</param>
        /// <param name="pageSize">Page size</param>
        /// <returns>Paginated list of partner reference data</returns>
        [BDOAuthorize(Enumerations.Role.PPAdministrator)]
        [Route("[Action]")]
        [HttpGet]
        public ActionResult SearchPartnerReferenceData(short? year = null, byte? cycle = null,
            Guid? partnerId = null, int pageIndex = 0, int pageSize = 20)
        {
            return Ok(_partnerReferenceDataUploadService.SearchPartnerReferenceData(year, cycle, partnerId, pageIndex, pageSize));
        }

        /// <summary>
        /// Get upload template for partner reference data
        /// </summary>
        /// <returns>Excel template file</returns>
        [BDOAuthorize(Enumerations.Role.PPAdministrator)]
        [Route("[Action]")]
        [HttpGet]
        public ActionResult GetUploadTemplate()
        {
            var result = _partnerReferenceDataUploadService.GetUploadTemplate();
            if (result.ResultStatus == ResultStatus.Success)
            {
                return File(result.Item, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    "PartnerReferenceDataTemplate.xlsx");
            }
            return Ok(result);
        }

        /// <summary>
        /// Get available column names for Form Creator mapping based on questionnaire year
        /// </summary>
        /// <param name="year">Questionnaire year to get relevant column names for</param>
        /// <param name="includeCyclePrefixes">Whether to include cycle prefixes for disambiguation (default: true)</param>
        /// <returns>List of available column names with cycle context</returns>
        [BDOAuthorize(Enumerations.Role.PPAdministrator)]
        [Route("[Action]")]
        [HttpGet]
        public ActionResult GetAvailableColumnNamesForFormCreator(short year, bool includeCyclePrefixes = true)
        {
            return Ok(_partnerReferenceDataUploadService.GetAvailableColumnNamesForFormCreator(year, includeCyclePrefixes));
        }

        /// <summary>
        /// Get partner reference data metadata by year with all cycles
        /// </summary>
        /// <param name="year">Year to filter metadata</param>
        /// <returns>List of metadata for the specified year</returns>
        [BDOAuthorize(Enumerations.Role.PPAdministrator)]
        [Route("[Action]")]
        [HttpGet]
        public ActionResult GetPartnerReferenceDataMetasByYear(short year)
        {
            return Ok(_partnerReferenceDataUploadService.GetPartnerReferenceDataMetasByYear(year));
        }

        #endregion
    }
}
