using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using BdoPartner.Plans.Business.Interface;
using BdoPartner.Plans.Common;
using BdoPartner.Plans.Common.Config;
using BdoPartner.Plans.Model.DTO;
using BdoPartner.Plans.Web.Common;
using System;
using System.Threading.Tasks;

namespace BdoPartner.Plans.Web.API.Controllers
{
    /// <summary>
    /// API Controller for Partner Reviewer Upload management operations
    /// Only System Administrators can access these endpoints
    /// </summary>
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class PartnerReviewerUploadController : BaseController
    {
        private readonly IPartnerReviewerUploadService _partnerReviewerUploadService;

        public PartnerReviewerUploadController(IPartnerReviewerUploadService partnerReviewerUploadService, 
            IHttpContextAccessor httpContextAccessor, ILogger<PartnerReviewerUploadController> logger, 
            IConfigSettings config) : base(httpContextAccessor, logger, config)
        {
            _partnerReviewerUploadService = partnerReviewerUploadService;
        }

        /// <summary>
        /// Get all partner reviewer uploads
        /// </summary>
        /// <returns>List of uploads</returns>
        [BDOAuthorize(Enumerations.Role.PPAdministrator)]
        [Route("[Action]")]
        [HttpGet]
        public ActionResult GetPartnerReviewerUploads()
        {
            return Ok(_partnerReviewerUploadService.GetPartnerReviewerUploads());
        }

        /// <summary>
        /// Search partner reviewer uploads with filtering and pagination
        /// </summary>
        /// <param name="year">Filter by year</param>
        /// <param name="status">Filter by status</param>
        /// <param name="pageIndex">Page index (0-based, default: 0)</param>
        /// <param name="pageSize">Page size (default: 20)</param>
        /// <returns>Paginated list of uploads with metadata</returns>
        [BDOAuthorize(Enumerations.Role.PPAdministrator)]
        [Route("[Action]")]
        [HttpGet]
        public ActionResult SearchPartnerReviewerUploads(short? year = null, byte? status = null,
            int pageIndex = 0, int pageSize = 20)
        {
            return Ok(_partnerReviewerUploadService.SearchPartnerReviewerUploads(year, status, pageIndex, pageSize));
        }

        /// <summary>
        /// Get partner reviewer upload by ID
        /// </summary>
        /// <param name="id">Upload ID</param>
        /// <returns>Upload details</returns>
        [BDOAuthorize(Enumerations.Role.PPAdministrator)]
        [Route("[Action]")]
        [HttpGet]
        public ActionResult GetPartnerReviewerUploadById(int id)
        {
            return Ok(_partnerReviewerUploadService.GetPartnerReviewerUploadById(id));
        }

        /// <summary>
        /// Get partner reviewer upload details
        /// </summary>
        /// <param name="uploadId">Upload ID</param>
        /// <param name="includeValidOnly">Include only valid records</param>
        /// <param name="includeInvalidOnly">Include only invalid records</param>
        /// <returns>List of upload details</returns>
        [BDOAuthorize(Enumerations.Role.PPAdministrator)]
        [Route("[Action]")]
        [HttpGet]
        public ActionResult GetPartnerReviewerUploadDetails(int uploadId, bool includeValidOnly = false,
            bool includeInvalidOnly = false)
        {
            return Ok(_partnerReviewerUploadService.GetPartnerReviewerUploadDetails(uploadId, includeValidOnly, includeInvalidOnly));
        }

        /// <summary>
        /// Upload Excel or CSV file for partner reviewer assignments
        /// </summary>
        /// <param name="file">Excel or CSV file</param>
        /// <param name="years">Years for the assignments (comma-separated string, e.g., "2023,2024")</param>
        /// <returns>Upload result</returns>
        [BDOAuthorize(Enumerations.Role.PPAdministrator)]
        [Route("[Action]")]
        [HttpPost]
        public async Task<ActionResult> UploadFile([FromForm] IFormFile file, [FromForm] string years)
        {
            var result = await _partnerReviewerUploadService.UploadFileAsync(file, years);
            return Ok(result);
        }

        /// <summary>
        /// Validate uploaded data
        /// </summary>
        /// <param name="uploadId">Upload ID to validate</param>
        /// <returns>Validation result</returns>
        [BDOAuthorize(Enumerations.Role.PPAdministrator)]
        [Route("[Action]")]
        [HttpGet]
        public async Task<ActionResult> ValidateUpload(int uploadId)
        {
            var result = await _partnerReviewerUploadService.ValidateUploadAsync(uploadId);
            return Ok(result);
        }

        /// <summary>
        /// Submit validated data to final PartnerReviewer table.
        /// By default (overwriteExisting = true), performs complete replacement of data for the specified years.
        /// </summary>
        /// <param name="uploadId">Upload ID to submit</param>
        /// <param name="overwriteExisting">Default true. When true, replaces all data for the specified years with staging data.</param>
        /// <returns>Submit result</returns>
        [BDOAuthorize(Enumerations.Role.PPAdministrator)]
        [Route("[Action]")]
        [HttpPost]
        public async Task<ActionResult> SubmitUpload(int uploadId, bool overwriteExisting = true)
        {
            var result = await _partnerReviewerUploadService.SubmitUploadAsync(uploadId, overwriteExisting);
            return Ok(result);
        }



        /// <summary>
        /// Get partner reviewers for a specific year
        /// </summary>
        /// <param name="year">Year to filter by</param>
        /// <returns>List of partner reviewers</returns>
        [BDOAuthorize(Enumerations.Role.PPAdministrator)]
        [Route("[Action]")]
        [HttpGet]
        public ActionResult GetPartnerReviewersByYear(short year)
        {
            return Ok(_partnerReviewerUploadService.GetPartnerReviewersByYear(year));
        }

        /// <summary>
        /// Get partner reviewer by partner ID and year
        /// </summary>
        /// <param name="partnerId">Partner ID</param>
        /// <param name="year">Year</param>
        /// <returns>Partner reviewer details</returns>
        [BDOAuthorize(Enumerations.Role.PPAdministrator)]
        [Route("[Action]")]
        [HttpGet]
        public ActionResult GetPartnerReviewerByPartnerAndYear(Guid partnerId, short year)
        {
            return Ok(_partnerReviewerUploadService.GetPartnerReviewerByPartnerAndYear(partnerId, year));
        }

        /// <summary>
        /// Update partner reviewer assignment
        /// </summary>
        /// <param name="partnerReviewer">Partner reviewer data</param>
        /// <returns>Updated partner reviewer</returns>
        [BDOAuthorize(Enumerations.Role.PPAdministrator)]
        [Route("[Action]")]
        [HttpPut]
        public ActionResult UpdatePartnerReviewer([FromBody] PartnerReviewer partnerReviewer)
        {
            return Ok(_partnerReviewerUploadService.UpdatePartnerReviewer(partnerReviewer));
        }

        /// <summary>
        /// Delete partner reviewer assignment
        /// </summary>
        /// <param name="id">Partner reviewer ID</param>
        /// <returns>Delete result</returns>
        [BDOAuthorize(Enumerations.Role.PPAdministrator)]
        [Route("[Action]")]
        [HttpDelete]
        public ActionResult DeletePartnerReviewer(Guid id)
        {
            return Ok(_partnerReviewerUploadService.DeletePartnerReviewer(id));
        }

        /// <summary>
        /// Delete partner reviewer upload and all its details
        /// Only uploads with validation failed status can be deleted
        /// </summary>
        /// <param name="uploadId">Upload ID to delete</param>
        /// <returns>Delete result</returns>
        [BDOAuthorize(Enumerations.Role.PPAdministrator)]
        [Route("[Action]")]
        [HttpDelete]
        public ActionResult DeletePartnerReviewerUpload(int uploadId)
        {
            return Ok(_partnerReviewerUploadService.DeletePartnerReviewerUpload(uploadId));
        }

        /// <summary>
        /// Download upload template file
        /// </summary>
        /// <returns>Excel template file</returns>
        [BDOAuthorize(Enumerations.Role.PPAdministrator)]
        [Route("[Action]")]
        [HttpGet]
        public ActionResult GetUploadTemplate()
        {
            var result = _partnerReviewerUploadService.GetUploadTemplate();
            if (result.ResultStatus == ResultStatus.Success)
            {
                return File(result.Item, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    "PartnerReviewerUploadTemplate.xlsx");
            }
            return Ok(result);
        }

        /// <summary>
        /// Export partner reviewers to Excel
        /// </summary>
        /// <param name="year">Year to export</param>
        /// <returns>Excel file with partner reviewers</returns>
        [BDOAuthorize(Enumerations.Role.PPAdministrator)]
        [Route("[Action]")]
        [HttpGet]
        public ActionResult ExportPartnerReviewersToExcel(short year)
        {
            var result = _partnerReviewerUploadService.ExportPartnerReviewersToExcel(year);
            if (result.ResultStatus == ResultStatus.Success)
            {
                return File(result.Item, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", 
                    $"PartnerReviewers_{year}.xlsx");
            }
            return Ok(result);
        }

        /// <summary>
        /// Request model for ValidateUpload action
        /// </summary>
        public class ValidateUploadRequest
        {
            public int UploadId { get; set; }
        }
    }  
}
