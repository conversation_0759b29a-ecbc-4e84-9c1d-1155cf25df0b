using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using BdoPartner.Plans.Business.Interface;
using BdoPartner.Plans.Common;
using BdoPartner.Plans.Common.Config;
using BdoPartner.Plans.Model.DTO;
using BdoPartner.Plans.Web.Common;
using System;
using System.Collections.Generic;

namespace BdoPartner.Plans.Web.API.Controllers
{
    /// <summary>
    /// API Controller for Questionnaire management operations
    /// </summary>
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class QuestionnaireController : BaseController
    {
        private readonly IQuestionnaireService _questionnaireService;

        public QuestionnaireController(IQuestionnaireService questionnaireService, IHttpContextAccessor httpContextAccessor, 
            ILogger<QuestionnaireController> logger, IConfigSettings config) : 
            base(httpContextAccessor, logger, config)
        {
            _questionnaireService = questionnaireService;
        }

        /// <summary>
        /// Get all active questionnaires
        /// </summary>
        /// <returns>List of questionnaires</returns>
        [BDOAuthorize(Enumerations.Role.PPAdministrator)]
        [Route("[Action]")]
        [HttpGet]
        public ActionResult GetQuestionnaires()
        {
            return Ok(_questionnaireService.GetQuestionnaires());
        }

        /// <summary>
        /// Search questionnaires with filtering and pagination
        /// Returns simplified DTO for list rendering (excludes heavy JSON fields for better performance)
        /// </summary>
        /// <param name="searchTerm">Search term for questionnaire name</param>
        /// <param name="year">Filter by year</param>
        /// <param name="status">Filter by status (0=Draft, 1=Published, 2=Archived)</param>
        /// <param name="isActive">Filter by active status</param>
        /// <param name="pageIndex">Page index (0-based, default: 0)</param>
        /// <param name="pageSize">Page size (default: 20)</param>
        /// <returns>Paginated list of questionnaire list items</returns>
        [BDOAuthorize(Enumerations.Role.PPAdministrator)]
        [Route("[Action]")]
        [HttpGet]
        public ActionResult SearchQuestionnaires(string searchTerm = null, short? year = null,
            byte? status = null, bool? isActive = null, int pageIndex = 0, int pageSize = 20)
        {
            return Ok(_questionnaireService.SearchQuestionnaires(searchTerm, year, status, isActive, pageIndex, pageSize));
        }

        /// <summary>
        /// Get questionnaire by ID
        /// </summary>
        /// <param name="id">Questionnaire ID</param>
        /// <returns>Questionnaire details</returns>
        [Authorize]
        [Route("[Action]")]
        [HttpGet]
        public ActionResult GetQuestionnaireById(Guid id)
        {
            return Ok(_questionnaireService.GetQuestionnaireById(id));
        }

        /// <summary>
        /// Get questionnaires by year
        /// </summary>
        /// <param name="year">Year to filter by</param>
        /// <returns>List of questionnaires for the specified year</returns>
        [Authorize]
        [Route("[Action]")]
        [HttpGet]
        public ActionResult GetQuestionnairesByYear(short year)
        {
            return Ok(_questionnaireService.GetQuestionnairesByYear(year));
        }

        /// <summary>
        /// Create a new questionnaire
        /// </summary>
        /// <param name="questionnaire">Questionnaire data</param>
        /// <returns>Created questionnaire</returns>
        [BDOAuthorize(Enumerations.Role.PPAdministrator)]
        [Route("[Action]")]
        [HttpPost]
        public ActionResult CreateQuestionnaire([FromBody] Questionnaire questionnaire)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var result = _questionnaireService.CreateQuestionnaire(questionnaire);
            return Ok(result);
        }

        /// <summary>
        /// Update an existing questionnaire
        /// </summary>
        /// <param name="questionnaire">Updated questionnaire data</param>
        /// <returns>Updated questionnaire</returns>
        [BDOAuthorize(Enumerations.Role.PPAdministrator)]
        [Route("[Action]")]
        [HttpPost]
        public ActionResult UpdateQuestionnaire([FromBody] Questionnaire questionnaire)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var result = _questionnaireService.UpdateQuestionnaire(questionnaire);
            return Ok(result);
        }

        /// <summary>
        /// Validate if a questionnaire can be deleted
        /// </summary>
        /// <param name="id">Questionnaire ID</param>
        /// <returns>Validation result</returns>
        [BDOAuthorize(Enumerations.Role.PPAdministrator)]
        [Route("[Action]")]
        [HttpGet]
        public ActionResult ValidateQuestionnaireForDeletion(Guid id)
        {
            var result = _questionnaireService.ValidateQuestionnaireForDeletion(id);
            return Ok(result);
        }

        /// <summary>
        /// Delete a questionnaire (soft delete)
        /// </summary>
        /// <param name="id">Questionnaire ID</param>
        /// <returns>Deletion result</returns>
        [BDOAuthorize(Enumerations.Role.PPAdministrator)]
        [Route("[Action]")]
        [HttpPost]
        public ActionResult DeleteQuestionnaire(Guid id)
        {
            var result = _questionnaireService.DeleteQuestionnaire(id);
            return Ok(result);
        }

        /// <summary>
        /// Check if there's already a published questionnaire in the specified year
        /// </summary>
        /// <param name="year">Year to check</param>
        /// <param name="excludeId">Optional questionnaire ID to exclude from the check</param>
        /// <returns>Boolean indicating if a published questionnaire exists in the year</returns>
        [BDOAuthorize(Enumerations.Role.PPAdministrator)]
        [Route("[Action]")]
        [HttpGet]
        public ActionResult HasPublishedQuestionnaireInYear(short year, Guid? excludeId = null)
        {
            var result = _questionnaireService.HasPublishedQuestionnaireInYear(year, excludeId);
            return Ok(result);
        }

        /// <summary>
        /// Publish a questionnaire (change status to Published)
        /// Enables audit logging to track publish/republish actions
        /// </summary>
        /// <param name="id">Questionnaire ID</param>
        /// <returns>Published questionnaire</returns>
        [BDOAuthorize(Enumerations.Role.PPAdministrator)]
        [Route("[Action]")]
        [HttpPost]
        public ActionResult PublishQuestionnaire(Guid id)
        {
            var result = _questionnaireService.PublishQuestionnaire(id);
            return Ok(result);
        }

        /// <summary>
        /// Archive a questionnaire (change status to Archived)
        /// </summary>
        /// <param name="id">Questionnaire ID</param>
        /// <returns>Archived questionnaire</returns>
        [BDOAuthorize(Enumerations.Role.PPAdministrator)]
        [Route("[Action]")]
        [HttpPost]
        public ActionResult ArchiveQuestionnaire(Guid id)
        {
            var result = _questionnaireService.ArchiveQuestionnaire(id);
            return Ok(result);
        }

        /// <summary>
        /// Get questionnaires for lookup/dropdown purposes
        /// </summary>
        /// <param name="includeInactive">Include inactive questionnaires</param>
        /// <returns>List of lookup items</returns>
        [Authorize]
        [Route("[Action]")]
        [HttpGet]
        public ActionResult GetQuestionnairesLookup(bool includeInactive = false)
        {
            return Ok(_questionnaireService.GetQuestionnairesLookup(includeInactive));
        }
    }
}
