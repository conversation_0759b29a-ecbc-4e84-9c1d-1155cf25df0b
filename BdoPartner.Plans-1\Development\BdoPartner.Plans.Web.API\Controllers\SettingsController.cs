﻿using BdoPartner.Plans.Common.Config;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace BdoPartner.Plans.Web.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class SettingsController : BaseController
    {
        private IConfiguration _configuration;
        private IConfigSettings _config;

        public SettingsController( IHttpContextAccessor httpContextAccessor, ILogger<SettingsController> logger, IConfigSettings config, IConfiguration configuration) :
            base(httpContextAccessor, logger, config)
        {
            _configuration = configuration;
            _config = config;
        }
        [Route("[Action]")]
        [HttpGet()]
        public ActionResult GetEnvironment()
        {
            return Ok(_configuration["ASPNETCORE_ENVIRONMENT"] ?? "Local");
        }

        [Route("[Action]")]
        [HttpGet()]
        public ActionResult GetIDSSettings()
        {
            return Ok(_config.ApplicationSettings);
        }      
    }
}
