using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using BdoPartner.Plans.Business.Interface;
using BdoPartner.Plans.Common;
using BdoPartner.Plans.Common.Config;
using BdoPartner.Plans.Model.DTO;
using BdoPartner.Plans.Web.Common;
using System;
using System.Collections.Generic;

namespace BdoPartner.Plans.Web.API.Controllers
{
    /// <summary>
    /// API Controller for UserAnswer management operations
    /// </summary>
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class UserAnswerController : BaseController
    {
        private readonly IUserAnswerService _userAnswerService;

        public UserAnswerController(IUserAnswerService userAnswerService, IHttpContextAccessor httpContextAccessor, 
            ILogger<UserAnswerController> logger, IConfigSettings config) : 
            base(httpContextAccessor, logger, config)
        {
            _userAnswerService = userAnswerService;
        }

        /// <summary>
        /// Get all active user answers
        /// </summary>
        /// <returns>List of user answers</returns>
        [BDOAuthorize(Enumerations.Role.PPAdministrator)]
        [Route("[Action]")]
        [HttpGet]
        public ActionResult GetUserAnswers()
        {
            return Ok(_userAnswerService.GetUserAnswers());
        }

        /// <summary>
        /// Search user answers with filtering and pagination
        /// </summary>
        /// <param name="formId">Filter by form ID</param>
        /// <param name="isActive">Filter by active status</param>
        /// <param name="pageNumber">Page number (default: 1)</param>
        /// <param name="pageSize">Page size (default: 50)</param>
        /// <returns>Paginated list of user answers</returns>
        [Authorize]
        [Route("[Action]")]
        [HttpGet]
        public ActionResult SearchUserAnswers(Guid? formId = null, bool? isActive = null, 
            int pageNumber = 1, int pageSize = 50)
        {
            return Ok(_userAnswerService.SearchUserAnswers(formId, isActive, pageNumber, pageSize));
        }

        /// <summary>
        /// Get user answer by ID
        /// </summary>
        /// <param name="id">User answer ID</param>
        /// <returns>User answer details</returns>
        [Authorize]
        [Route("[Action]")]
        [HttpGet]
        public ActionResult GetUserAnswerById(Guid id)
        {
            return Ok(_userAnswerService.GetUserAnswerById(id));
        }

        /// <summary>
        /// Get user answers by form ID
        /// </summary>
        /// <param name="formId">Form ID</param>
        /// <returns>List of user answers for the form</returns>
        [Authorize]
        [Route("[Action]")]
        [HttpGet]
        public ActionResult GetUserAnswersByFormId(Guid formId)
        {
            return Ok(_userAnswerService.GetUserAnswersByFormId(formId));
        }

        /// <summary>
        /// Create a new user answer
        /// </summary>
        /// <param name="userAnswer">User answer data</param>
        /// <returns>Created user answer</returns>
        [Authorize]
        [Route("[Action]")]
        [HttpPost]
        public ActionResult CreateUserAnswer([FromBody] UserAnswer userAnswer)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var result = _userAnswerService.CreateUserAnswer(userAnswer);
            return Ok(result);
        }

        /// <summary>
        /// Update an existing user answer
        /// </summary>
        /// <param name="userAnswer">Updated user answer data</param>
        /// <returns>Updated user answer</returns>
        [Authorize]
        [Route("[Action]")]
        [HttpPost]
        public ActionResult UpdateUserAnswer([FromBody] UserAnswer userAnswer)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var result = _userAnswerService.UpdateUserAnswer(userAnswer);
            return Ok(result);
        }

        /// <summary>
        /// Delete a user answer (soft delete)
        /// </summary>
        /// <param name="id">User answer ID</param>
        /// <returns>Deletion result</returns>
        [Authorize]
        [Route("[Action]")]
        [HttpPost]
        public ActionResult DeleteUserAnswer(Guid id)
        {
            var result = _userAnswerService.DeleteUserAnswer(id);
            return Ok(result);
        }

        /// <summary>
        /// Bulk create user answers for a form
        /// </summary>
        /// <param name="formId">Form ID</param>
        /// <param name="userAnswers">Collection of user answers</param>
        /// <returns>Created user answers</returns>
        [Authorize]
        [Route("[Action]")]
        [HttpPost]
        public ActionResult BulkCreateUserAnswers(Guid formId, [FromBody] ICollection<UserAnswer> userAnswers)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            if (userAnswers == null || userAnswers.Count == 0)
            {
                return BadRequest("User answers collection cannot be empty");
            }

            var result = _userAnswerService.BulkCreateUserAnswers(formId, userAnswers);
            return Ok(result);
        }

        /// <summary>
        /// Bulk update user answers for a form
        /// </summary>
        /// <param name="formId">Form ID</param>
        /// <param name="userAnswers">Collection of user answers to update</param>
        /// <returns>Updated user answers</returns>
        [Authorize]
        [Route("[Action]")]
        [HttpPost]
        public ActionResult BulkUpdateUserAnswers(Guid formId, [FromBody] ICollection<UserAnswer> userAnswers)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            if (userAnswers == null || userAnswers.Count == 0)
            {
                return BadRequest("User answers collection cannot be empty");
            }

            var result = _userAnswerService.BulkUpdateUserAnswers(formId, userAnswers);
            return Ok(result);
        }

        /// <summary>
        /// Save or update user answer for auto-save functionality
        /// </summary>
        /// <param name="formId">Form ID</param>
        /// <param name="answerData">Survey answer data as JSON string</param>
        /// <returns>Saved user answer</returns>
        [Authorize]
        [Route("[Action]")]
        [HttpPost]
        public ActionResult SaveUserAnswer(Guid formId, [FromBody] string answerData)
        {
            if (string.IsNullOrEmpty(answerData))
            {
                return BadRequest("Answer data cannot be empty");
            }

            var result = _userAnswerService.SaveOrUpdateUserAnswer(formId, answerData);
            return Ok(result);
        }

        /// <summary>
        /// Auto-save user answer (for background saving)
        /// </summary>
        /// <param name="formId">Form ID</param>
        /// <param name="answerData">Survey answer data as JSON string</param>
        /// <returns>Auto-save result</returns>
        [Authorize]
        [Route("[Action]")]
        [HttpPost]
        public ActionResult AutoSaveUserAnswer(Guid formId, [FromBody] string answerData)
        {
            try
            {
                if (string.IsNullOrEmpty(answerData))
                {
                    return Ok(new { success = false, message = "Answer data cannot be empty" });
                }

                var result = _userAnswerService.SaveOrUpdateUserAnswer(formId, answerData);

                if (result.ResultStatus == ResultStatus.Success)
                {
                    return Ok(new { success = true, message = "Auto-saved successfully" });
                }
                else
                {
                    return Ok(new { success = false, message = result.Message });
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error during auto-save");
                return Ok(new { success = false, message = "Auto-save failed" });
            }
        }
    }
}
