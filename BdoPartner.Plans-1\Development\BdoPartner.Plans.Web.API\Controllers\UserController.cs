﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using BdoPartner.Plans.Business.Interface;
using BdoPartner.Plans.Common;
using BdoPartner.Plans.Common.Config;
using BdoPartner.Plans.Model.DTO;
using BdoPartner.Plans.Web.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BdoPartner.Plans.Web.API.Controllers
{
    /// <summary>
    ///  Note: System requires "AdminAccess" permisson to access "api/user" endpoint.
    /// </summary>
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class UserController : BaseController
    {
        
        public UserController(IHttpContextAccessor httpContextAccessor, ILogger<UserController> logger, IConfigSettings config) : 
            base(httpContextAccessor, logger, config)
        {
        }              

        
    }
}
