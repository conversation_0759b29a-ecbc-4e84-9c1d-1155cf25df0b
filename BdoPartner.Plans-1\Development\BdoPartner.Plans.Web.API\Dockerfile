#See https://aka.ms/containerfastmode to understand how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/aspnet:5.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:5.0 AS build
WORKDIR /src
COPY ["BdoPartner.Plans.Web.API/BdoPartner.Plans.Web.API.csproj", "BdoPartner.Plans.Web.API/"]
RUN dotnet restore "BdoPartner.Plans.Web.API/BdoPartner.Plans.Web.API.csproj"
COPY . .
WORKDIR "/src/BdoPartner.Plans.Web.API"
RUN dotnet build "BdoPartner.Plans.Web.API.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "BdoPartner.Plans.Web.API.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "BdoPartner.Plans.Web.API.dll"]