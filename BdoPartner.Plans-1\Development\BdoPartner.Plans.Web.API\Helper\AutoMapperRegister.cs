﻿using Microsoft.Extensions.DependencyInjection;
using BdoPartner.Plans.Model.Mapper;

namespace BdoPartner.Plans.Web.API.Helper
{
    public static class AutoMapperRegister
    {
        /// <summary>
        ///  Register AutoMapper with sepcified mapper profiles.
        ///  http://docs.automapper.org/en/stable/Configuration.html#profile-instances
        ///  http://docs.automapper.org/en/stable/Dependency-injection.html
        ///  
        /// </summary>
        /// <param name="services"></param>
        /// <returns></returns>
        public static IServiceCollection RegisterAutoMapperProfiles(this IServiceCollection services)
        {
            services.AddAutoMapper(typeof(LookupProfile));
            services.AddAutoMapper(typeof(EntityProfile));
            return services;
        }
    }
}
