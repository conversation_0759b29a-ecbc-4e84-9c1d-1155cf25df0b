﻿using Microsoft.Extensions.DependencyInjection;
using BdoPartner.Plans.Business.Interface;
using BdoPartner.Plans.Common.Config;
using BdoPartner.Plans.Business;

namespace BdoPartner.Plans.Web.API.Helper
{
    /// <summary>
    /// Register Business Layers services.
    /// </summary>
    public static class BusinessLayerRegister
    {
        /// <summary>
        ///  Dependency Injection register Business layer services in Startup.cs.
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configSettings"></param>
        /// <returns></returns>
        public static IServiceCollection RegisterBusinessLayer(this IServiceCollection services, IConfigSettings configSettings)
        {
                  
            services.AddScoped<ILookupService, LookupService>();
            services.AddScoped<IQuestionnaireService, QuestionnaireService>();
            services.AddScoped<IFormService, FormService>();
            services.AddScoped<IUserAnswerService, UserAnswerService>();
            services.AddScoped<IPartnerReviewerUploadService, PartnerReviewerUploadService>();
            services.AddScoped<IPartnerReferenceDataUploadService, PartnerReferenceDataUploadService>();
            services.AddScoped<IPartnerService, PartnerService>();

            return services;
        }
    }
}
