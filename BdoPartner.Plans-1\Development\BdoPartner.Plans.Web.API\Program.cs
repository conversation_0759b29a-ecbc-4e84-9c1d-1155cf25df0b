﻿using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.ApplicationInsights;
using Azure.Identity;
using Microsoft.Extensions.Configuration.AzureAppConfiguration;
using System;
using System.Security.Cryptography.X509Certificates;
using System.Linq;
using Microsoft.Azure.Services.AppAuthentication;
using Microsoft.Azure.KeyVault;
using Microsoft.Extensions.Configuration.AzureKeyVault;
using Azure.Core;
using BdoPartner.Plans.Web.Common;
using System.Security.Cryptography;

namespace BdoPartner.Plans.Web.API
{
    public class Program
    {
        public static void Main(string[] args)
        {
            CreateHostBuilder(args).Build().Run();
        }

        public static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
                .ConfigureWebHostDefaults(webBuilder =>
                {
                    webBuilder.UseStartup<Startup>();
                })
                .ConfigureAppConfiguration((hostingContext, config) =>
                {
                    if (!hostingContext.HostingEnvironment.IsEnvironment("Local"))
                    {
                        var builtConfig = config.Build();
                        var azureServiceTokenProvider = new AzureServiceTokenProvider();
                        var keyVaultClient = new KeyVaultClient(
                            new KeyVaultClient.AuthenticationCallback(
                                azureServiceTokenProvider.KeyVaultTokenCallback));
                        string keyUri = builtConfig["App:AzureKeyVaultConfig:AzureKeyVaultUri"];
                        config.AddAzureKeyVault(
                            keyUri,
                            keyVaultClient,
                            new DefaultKeyVaultSecretManager());
                    }
                    WebHelper.AppConfigurationRegistration(hostingContext, ref config, "WebAPI");
                })
                .ConfigureLogging((context, logging) =>
                       {
                           //
                           // Reference: https://docs.microsoft.com/en-us/aspnet/core/fundamentals/logging/?view=aspnetcore-5.0
                           // https://mderriey.com/2020/08/08/a-look-at-the-aspnet-core-logging-provider-for-app-service/
                           //
                           logging.ClearProviders();
                           logging.AddConsole();

                           //
                           // Only work when application deployed to Azure App Services.
                           //
                           //if (!context.HostingEnvironment.IsDevelopment())
                           if (!context.HostingEnvironment.IsEnvironment("Local"))
                           {
                               //
                               // Enable the file logging in Azure App Serices.
                               // Note: Azure Log stream integration is linked to the file system logging
                               // Reference: https://mderriey.com/2020/08/08/a-look-at-the-aspnet-core-logging-provider-for-app-service/
                               // Note: AzureWebAppDiagnostics feature only enabled when the app runs on the Azure App Service,
                               // which is detected through a number of well-known environment variables defined in App Service.
                               //
                               // The App Service file logger writes log entries to files in the %HOME%\LogFiles\Application
                               //
                               // Note: In BDO environment, general development team does not have permission to enable file logging feature in Azure (UAT, Production).
                               // So, in BDO, we don't prefer to apply file logging feature to the Web Applications in Azure App Services,
                               // instead, BDO development team prefer to enable "Application Insights" feature for Application logging and performance monitor.
                               //
                               logging.AddAzureWebAppDiagnostics();

                               //
                               // Only work when web application deployed to Azure App Services has enabled "Application Insight" .
                               //

                               if (context.Configuration["APPINSIGHTS_CONNECTIONSTRING"] != null)
                               {
                                   //
                                   // Providing an instrumentation key is required if you're using the
                                   // standalone Microsoft.Extensions.Logging.ApplicationInsights package,
                                   // or when you need to capture logs during application startup, for example
                                   // in the Program.cs or Startup.cs itself.
                                   //
                                   logging.AddApplicationInsights(
                                   context.Configuration["APPINSIGHTS_CONNECTIONSTRING"]);

                                   //
                                   // Capture all log-level entries from Program
                                   //
                                   logging.AddFilter<ApplicationInsightsLoggerProvider>(
                                       typeof(Program).FullName, LogLevel.Trace);
                                   //
                                   // Capture all log-level entries from Startup
                                   //
                                   logging.AddFilter<ApplicationInsightsLoggerProvider>(
                                       typeof(Startup).FullName, LogLevel.Trace);
                               }
                           }
                       }
                );
    }   
}
