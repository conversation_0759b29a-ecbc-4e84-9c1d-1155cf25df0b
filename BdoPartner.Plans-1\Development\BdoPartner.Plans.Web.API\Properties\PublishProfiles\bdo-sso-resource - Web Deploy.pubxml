<?xml version="1.0" encoding="utf-8"?>
<!--
This file is used by the publish/package process of your Web project. You can customize the behavior of this process
by editing this MSBuild file. In order to learn more about this please visit https://go.microsoft.com/fwlink/?LinkID=208121. 
-->
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <WebPublishMethod>MSDeploy</WebPublishMethod>
    <ResourceId>/subscriptions/afecc720-5dfb-4159-90b8-d649aa831bda/resourceGroups/solution-template-design/providers/Microsoft.Web/sites/bdo-sso-resource</ResourceId>
    <ResourceGroup>solution-template-design</ResourceGroup>
    <PublishProvider>AzureWebSite</PublishProvider>
    <LastUsedBuildConfiguration>Release</LastUsedBuildConfiguration>
    <LastUsedPlatform>Any CPU</LastUsedPlatform>
    <SiteUrlToLaunchAfterPublish>http://bdo-sso-resource.azurewebsites.net</SiteUrlToLaunchAfterPublish>
    <LaunchSiteAfterPublish>True</LaunchSiteAfterPublish>
    <ExcludeApp_Data>False</ExcludeApp_Data>
    <ProjectGuid>596d501b-2256-473e-827f-f77aa0ba58ea</ProjectGuid>
    <MSDeployServiceURL>bdo-sso-resource.scm.azurewebsites.net:443</MSDeployServiceURL>
    <DeployIisAppPath>bdo-sso-resource</DeployIisAppPath>
    <RemoteSitePhysicalPath />
    <SkipExtraFilesOnServer>False</SkipExtraFilesOnServer>
    <MSDeployPublishMethod>WMSVC</MSDeployPublishMethod>
    <EnableMSDeployBackup>True</EnableMSDeployBackup>
    <UserName>$bdo-sso-resource</UserName>
    <_SavePWD>True</_SavePWD>
    <_DestinationType>AzureWebSite</_DestinationType>
    <TargetFramework>net5.0</TargetFramework>
    <SelfContained>true</SelfContained>
    <InstallAspNetCoreSiteExtension>False</InstallAspNetCoreSiteExtension>
    <RuntimeIdentifier>win-x86</RuntimeIdentifier>
  </PropertyGroup>
</Project>