(self.webpackChunkbdoclient_app=self.webpackChunkbdoclient_app||[]).push([[123],{7123:(e,t,s)=>{"use strict";s.r(t),s.d(t,{AdminModule:()=>A});var i=s(5427),r=s(6252),n=s(7673);const u={lang:"en",data:{Admin:{TestLabel:"Test",TestLabel2:"Test"}}},a={lang:"fr",data:{Admin:{TestLabel:"Test",TestLabel2:"Test"}}};var o=s(8619),c=s(7407),l=s(4729),d=s(4693),p=s(6304),g=s(7759),m=s(1170),Z=s(2693);let v=(()=>{class e extends g.b{constructor(e){super(e)}getUsers(){var e=this;return(0,p.Z)(function*(){let t={};const s=yield e.http.get(e.adminApiUrl+"user/GetUsers").toPromise();return s.resultStatus===m.$.Success&&(t=s.item||{}),t})()}getRoles(){var e=this;return(0,p.Z)(function*(){let t=[];const s=yield e.http.get(e.adminApiUrl+"lookup/GetRoles").toPromise();return s.resultStatus===m.$.Success&&(t=s.item||{}),t})()}getPermissions(){var e=this;return(0,p.Z)(function*(){let t=[];const s=yield e.http.get(e.adminApiUrl+"lookup/GetPermissions").toPromise();return s.resultStatus===m.$.Success&&(t=s.item||{}),t})()}}return e.\u0275fac=function(t){return new(t||e)(o.LFG(Z.eN))},e.\u0275prov=o.Yz7({token:e,factory:e.\u0275fac}),e})();var f=s(1116);function h(e,t){1&e&&(o.TgZ(0,"h2"),o._uU(1,"Users List"),o.qZA())}function T(e,t){if(1&e&&(o.TgZ(0,"div",1),o.TgZ(1,"div",2),o._uU(2),o.qZA(),o.TgZ(3,"div",2),o._uU(4),o.qZA(),o.TgZ(5,"div",2),o._uU(6),o.qZA(),o.TgZ(7,"div",2),o._uU(8),o.qZA(),o.TgZ(9,"div",2),o._uU(10),o.qZA(),o.qZA()),2&e){const e=t.$implicit,s=t.index;o.xp6(2),o.Oqu(s),o.xp6(2),o.Oqu(e.authProviderId),o.xp6(2),o.Oqu(e.userName),o.xp6(2),o.Oqu(e.displayName),o.xp6(2),o.Oqu(e.email)}}const U=[{path:"**",component:(()=>{class e extends n.H{constructor(e,t,s,i){super(e,t),this.translateLoader=s,this.adminService=i,this.title="DBO Client",this.users=[],this.translateLoader.loadTranslations(u,a)}ngOnInit(){this.adminService.getUsers().then(e=>{this.users=e})}}return e.\u0275fac=function(t){return new(t||e)(o.Y36(c.U),o.Y36(l.e),o.Y36(d.i),o.Y36(v))},e.\u0275cmp=o.Xpm({type:e,selectors:[["app-admin"]],features:[o.qOj],decls:12,vars:2,consts:[[4,"ngIf"],[1,"p-grid"],[1,"p-col"],["class","p-grid",4,"ngFor","ngForOf"]],template:function(e,t){1&e&&(o.YNc(0,h,2,0,"h2",0),o.TgZ(1,"div",1),o._UZ(2,"div",2),o.TgZ(3,"div",2),o._uU(4,"Identity Provider"),o.qZA(),o.TgZ(5,"div",2),o._uU(6,"User Name"),o.qZA(),o.TgZ(7,"div",2),o._uU(8,"Display Name"),o.qZA(),o.TgZ(9,"div",2),o._uU(10,"Email"),o.qZA(),o.qZA(),o.YNc(11,T,11,5,"div",3)),2&e&&(o.Q6J("ngIf",t.isLogin),o.xp6(11),o.Q6J("ngForOf",t.users))},directives:[f.O5,f.sg],styles:[""]}),e})(),children:[]}];let A=(()=>{class e{}return e.\u0275fac=function(t){return new(t||e)},e.\u0275mod=o.oAB({type:e}),e.\u0275inj=o.cJS({providers:[v],imports:[[r.p,i.Bz.forChild(U)]]}),e})()}}]);