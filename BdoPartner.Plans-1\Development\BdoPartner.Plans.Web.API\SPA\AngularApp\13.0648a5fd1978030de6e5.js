(self.webpackChunkbdoclient_app=self.webpackChunkbdoclient_app||[]).push([[13],{5013:(s,e,t)=>{"use strict";t.r(e),t.d(e,{HomeModule:()=>q});var o=t(5427),i=t(6252);const n={lang:"en",data:{Home:{TestLabel:"Test",TestLabel2:"Test"}}},a={lang:"fr",data:{Home:{TestLabel:"Test",TestLabel2:"Test"}}};var r=t(7673),c=t(592),u=t(8619),l=t(7407),g=t(4729),h=t(4693),T=t(6304),f=t(7759),m=t(1170),Z=t(2693);let p=(()=>{class s extends f.b{constructor(s){super(s)}getNotifications(){var s=this;return(0,T.Z)(function*(){let e=[];const t=yield s.http.get(s.baseApiUrl+"lookup/getnotifications").toPromise();return t.resultStatus===m.$.Success&&(e=t.item),e})()}}return s.\u0275fac=function(e){return new(e||s)(u.LFG(Z.eN))},s.\u0275prov=u.Yz7({token:s,factory:s.\u0275fac}),s})();var b=t(1116),d=t(6726);function w(s,e){if(1&s&&(u.TgZ(0,"h1"),u._uU(1),u.qZA()),2&s){const s=u.oxw();u.xp6(1),u.hij("Hello ",(null==s.currentUser?null:s.currentUser.displayName)+"!"," Welcome to BDO Home")}}function k(s,e){1&s&&(u.TgZ(0,"h2"),u._uU(1,"BDO Daily News"),u.qZA())}function A(s,e){if(1&s&&(u.TgZ(0,"li"),u._uU(1),u.qZA()),2&s){const s=e.$implicit,t=e.index;u.xp6(1),u.AsE("",t," - ",s.message,"")}}const D=[{path:"**",component:(()=>{class s extends r.H{constructor(s,e,t,o){super(s,e),this.translateLoader=t,this.homeService=o,this.title="DBO Client",this.notifications=[],this.translateLoader.loadTranslations(n,a)}ngOnInit(){this.isLogin&&this.homeService.getNotifications().then(s=>{this.notifications=s})}showMessages(){this.errorMessage("This is an error!"),this.infoMessage("This is generic information."),this.warnMessage("this is warn message!"),this.successMessage("This is success message"),this.emitMessages()}showToasts(){this.errorToast("This is an error toast!"),this.infoToast("This is generic information toast."),this.warnToast("this is warn toast!"),this.successToast("This is success toast"),this.emitMessages()}showMessage(s){switch(s){case c.u.Warn:this.warnMessage("This is waning message");break;case c.u.Info:this.infoMessage("This is general message");break;case c.u.Success:this.successMessage("This is success message");break;case c.u.Error:this.errorMessage("This is error message")}this.emitMessages()}showToast(s){switch(s){case c.u.Warn:this.warnToast("This is waning message toast");break;case c.u.Info:this.infoToast("This is general message toast");break;case c.u.Success:this.successToast("This is success message toast");break;case c.u.Error:this.errorToast("This is error message toast")}this.emitMessages()}showDialog(s){switch(s){case c.u.Warn:this.warnDialog("This is waning message dialog");break;case c.u.Info:this.infoDialog("This is general message dialog");break;case c.u.Success:this.successDialog("This is success message dialog");break;case c.u.Error:this.errorDialog("This is error message dialog")}}showConfirmDialog(){this.confirmDialog("Please confirm your choice",s=>{this.infoToast("Choiced: "+s)})}}return s.\u0275fac=function(e){return new(e||s)(u.Y36(l.U),u.Y36(g.e),u.Y36(h.i),u.Y36(p))},s.\u0275cmp=u.Xpm({type:s,selectors:[["app-home"]],features:[u.qOj],decls:32,vars:3,consts:[[4,"ngIf"],[4,"ngFor","ngForOf"],[1,"p-d-flex","p-flex-wrap"],[1,"p-mb-2","p-mr-2"],["pButton","","type","button","label","Demo Message Box for multiple messages at one time",3,"click"],["pButton","","type","button","label","Demo Message Box for one success message",3,"click"],["pButton","","type","button","label","Demo Message Box for one error message",3,"click"],["pButton","","type","button","label","Demo Toast for multiple messages at one time",3,"click"],["pButton","","type","button","label","Demo Toast for one success message",3,"click"],["pButton","","type","button","label","Demo Toast for one error message",3,"click"],["pButton","","type","button","label","Demo Info Dialog Box",3,"click"],["pButton","","type","button","label","Demo Warn Dialog Box",3,"click"],["pButton","","type","button","label","Demo Error Dialog Box",3,"click"],["pButton","","type","button","label","Demo Success Dialog Box",3,"click"],["pButton","","type","button","label","Demo Confirm Dialog Box",3,"click"]],template:function(s,e){1&s&&(u.YNc(0,w,2,1,"h1",0),u.YNc(1,k,2,0,"h2",0),u.TgZ(2,"ul"),u.YNc(3,A,2,2,"li",1),u.qZA(),u.TgZ(4,"div",2),u.TgZ(5,"div",3),u.TgZ(6,"button",4),u.NdJ("click",function(){return e.showMessages()}),u.qZA(),u.qZA(),u.TgZ(7,"div",3),u.TgZ(8,"button",5),u.NdJ("click",function(){return e.showMessage("success")}),u.qZA(),u.qZA(),u.TgZ(9,"div",3),u.TgZ(10,"button",6),u.NdJ("click",function(){return e.showMessage("error")}),u.qZA(),u.qZA(),u.qZA(),u._UZ(11,"hr"),u.TgZ(12,"div",2),u.TgZ(13,"div",3),u.TgZ(14,"button",7),u.NdJ("click",function(){return e.showToasts()}),u.qZA(),u.qZA(),u.TgZ(15,"div",3),u.TgZ(16,"button",8),u.NdJ("click",function(){return e.showToast("success")}),u.qZA(),u.qZA(),u.TgZ(17,"div",3),u.TgZ(18,"button",9),u.NdJ("click",function(){return e.showToast("error")}),u.qZA(),u.qZA(),u.qZA(),u._UZ(19,"hr"),u.TgZ(20,"div",2),u.TgZ(21,"div",3),u.TgZ(22,"button",10),u.NdJ("click",function(){return e.showDialog("info")}),u.qZA(),u.qZA(),u.TgZ(23,"div",3),u.TgZ(24,"button",11),u.NdJ("click",function(){return e.showDialog("warn")}),u.qZA(),u.qZA(),u.TgZ(25,"div",3),u.TgZ(26,"button",12),u.NdJ("click",function(){return e.showDialog("error")}),u.qZA(),u.qZA(),u.TgZ(27,"div",3),u.TgZ(28,"button",13),u.NdJ("click",function(){return e.showDialog("success")}),u.qZA(),u.qZA(),u.TgZ(29,"div",3),u.TgZ(30,"button",14),u.NdJ("click",function(){return e.showConfirmDialog()}),u.qZA(),u.qZA(),u.qZA(),u._UZ(31,"hr")),2&s&&(u.Q6J("ngIf",e.isLogin),u.xp6(1),u.Q6J("ngIf",e.isLogin),u.xp6(2),u.Q6J("ngForOf",e.notifications))},directives:[b.O5,b.sg,d.Hq],styles:[""]}),s})(),children:[]}];let q=(()=>{class s{}return s.\u0275fac=function(e){return new(e||s)},s.\u0275mod=u.oAB({type:s}),s.\u0275inj=u.cJS({providers:[p],imports:[[i.p,o.Bz.forChild(D)]]}),s})()}}]);