(self.webpackChunkbdoclient_app=self.webpackChunkbdoclient_app||[]).push([[632],{632:(e,t,r)=>{"use strict";r.r(t),r.d(t,{AccountModule:()=>N});var i=r(5427),n=r(649),o=r(6252),s=r(7673);const l={lang:"en",data:{Account:{MyProfile:"My Profile"}}},a={lang:"fr",data:{Account:{MyProfile:"My Profile"}}};var u=r(8619),c=r(7407),d=r(4729),p=r(4693),f=r(442);let g=(()=>{class e extends s.H{constructor(e,t,r,i,n,o){super(t,r),this.router=e,this.securityService=t,this.messageService=r,this.translateLoader=i,this.translateService=n,this.appContextService=o,this.translateLoader.loadTranslations(l,a)}}return e.\u0275fac=function(t){return new(t||e)(u.Y36(i.F0),u.Y36(c.U),u.Y36(d.e),u.Y36(p.i),u.Y36(n.sK),u.Y36(f.i))},e.\u0275cmp=u.Xpm({type:e,selectors:[["app-account"]],features:[u.qOj],decls:1,vars:0,template:function(e,t){1&e&&u._UZ(0,"router-outlet")},directives:[i.lC],styles:[""]}),e})();var Z=r(6304),h=r(1170),m=r(7759),v=r(2693);let M=(()=>{class e extends m.b{constructor(e,t){super(e),this.messageService=t}getMyProfile(){var e=this;return(0,Z.Z)(function*(){let t=null;const r=yield e.http.get(`${e.adminApiUrl}User/GetMyProfile`).toPromise();return r.resultStatus===h.$.Success?t=r.item||{}:e.messageService.error(r.message),t})()}updateMyProfile(e){var t=this;return(0,Z.Z)(function*(){let r=null;const i=yield t.http.post(`${t.adminApiUrl}User/UpdateMyProfile`,e).toPromise();return i.resultStatus===h.$.Success?r=i.item:t.messageService.error(i.message),r})()}}return e.\u0275fac=function(t){return new(t||e)(u.LFG(v.eN),u.LFG(d.e))},e.\u0275prov=u.Yz7({token:e,factory:e.\u0275fac}),e})();var y=r(1116),P=r(1041),A=r(9892);function T(e,t){if(1&e){const e=u.EpF();u.TgZ(0,"div",1),u.TgZ(1,"div",2),u.TgZ(2,"label",3),u._uU(3,"User Name"),u.qZA(),u.TgZ(4,"input",4),u.NdJ("ngModelChange",function(t){return u.CHM(e),u.oxw().userProfile.userName=t}),u.qZA(),u.qZA(),u.TgZ(5,"div",2),u.TgZ(6,"label",3),u._uU(7,"First Name"),u.qZA(),u.TgZ(8,"input",5),u.NdJ("ngModelChange",function(t){return u.CHM(e),u.oxw().userProfile.firstName=t}),u.qZA(),u.qZA(),u.TgZ(9,"div",2),u.TgZ(10,"label",3),u._uU(11,"Last Name"),u.qZA(),u.TgZ(12,"input",5),u.NdJ("ngModelChange",function(t){return u.CHM(e),u.oxw().userProfile.lastName=t}),u.qZA(),u.qZA(),u.TgZ(13,"div",2),u.TgZ(14,"label",3),u._uU(15,"Display Name"),u.qZA(),u.TgZ(16,"input",5),u.NdJ("ngModelChange",function(t){return u.CHM(e),u.oxw().userProfile.displayName=t}),u.qZA(),u.qZA(),u.TgZ(17,"div",2),u.TgZ(18,"label",3),u._uU(19,"Email"),u.qZA(),u.TgZ(20,"input",5),u.NdJ("ngModelChange",function(t){return u.CHM(e),u.oxw().userProfile.email=t}),u.qZA(),u.qZA(),u.TgZ(21,"div",2),u.TgZ(22,"label",3),u._uU(23,"Identity Provider"),u.qZA(),u.TgZ(24,"input",4),u.NdJ("ngModelChange",function(t){return u.CHM(e),u.oxw().userProfile.authProviderId=t}),u.qZA(),u.qZA(),u.TgZ(25,"div",6),u.TgZ(26,"span"),u._uU(27,"Note: As for user login with three party external identity provider, please modify the user profile in your original identity provider."),u.qZA(),u.qZA(),u.qZA()}if(2&e){const e=u.oxw();u.xp6(4),u.Q6J("ngModel",e.userProfile.userName),u.xp6(4),u.Q6J("ngModel",e.userProfile.firstName),u.xp6(4),u.Q6J("ngModel",e.userProfile.lastName),u.xp6(4),u.Q6J("ngModel",e.userProfile.displayName),u.xp6(4),u.Q6J("ngModel",e.userProfile.email),u.xp6(4),u.Q6J("ngModel",e.userProfile.authProviderId)}}let q=(()=>{class e extends s.H{constructor(e,t,r,i,n){super(t,r),this.router=e,this.translate=i,this.accountService=n,this.userProfile={}}getMyProfile(){var e=this;return(0,Z.Z)(function*(){const t=yield e.accountService.getMyProfile();null!=t&&(e.userProfile=t)})()}ngOnInit(){this.getMyProfile()}}return e.\u0275fac=function(t){return new(t||e)(u.Y36(i.F0),u.Y36(c.U),u.Y36(d.e),u.Y36(n.sK),u.Y36(M))},e.\u0275cmp=u.Xpm({type:e,selectors:[["app-user-profile"]],features:[u.qOj],decls:3,vars:1,consts:[["class","p-fluid",4,"ngIf"],[1,"p-fluid"],[1,"p-field"],["for","fieldId"],["id","fieldId","type","text","pInputText","","readonly","true",3,"ngModel","ngModelChange"],["id","fieldId","type","text","pInputText","",3,"ngModel","ngModelChange"],[1,"p=field"]],template:function(e,t){1&e&&(u.TgZ(0,"h2"),u._uU(1,"My Profile"),u.qZA(),u.YNc(2,T,28,6,"div",0)),2&e&&(u.xp6(2),u.Q6J("ngIf",t.isLogin&&t.userProfile))},directives:[y.O5,P.Fj,A.o,P.JJ,P.On],styles:[""]}),e})();const x=[{path:"**",component:g,children:[{path:"UserProfile",component:q},{path:"**",component:q}]}];let N=(()=>{class e{}return e.\u0275fac=function(t){return new(t||e)},e.\u0275mod=u.oAB({type:e}),e.\u0275inj=u.cJS({providers:[M],imports:[[o.p,n.aw.forRoot(),i.Bz.forChild(x)]]}),e})()}}]);