(self.webpackChunkbdoclient_app=self.webpackChunkbdoclient_app||[]).push([[429],{2167:()=>{"use strict";!function(e){const t=e.performance;function n(e){t&&t.mark&&t.mark(e)}function o(e,n){t&&t.measure&&t.measure(e,n)}n("Zone");const r=e.__Zone_symbol_prefix||"__zone_symbol__";function s(e){return r+e}const a=!0===e[s("forceDuplicateZoneCheck")];if(e.Zone){if(a||"function"!=typeof e.Zone.__symbol__)throw new Error("Zone already loaded.");return e.Zone}class i{constructor(e,t){this._parent=e,this._name=t?t.name||"unnamed":"<root>",this._properties=t&&t.properties||{},this._zoneDelegate=new l(this,this._parent&&this._parent._zoneDelegate,t)}static assertZonePatched(){if(e.Promise!==O.ZoneAwarePromise)throw new Error("Zone.js has detected that ZoneAwarePromise `(window|global).Promise` has been overwritten.\nMost likely cause is that a Promise polyfill has been loaded after Zone.js (Polyfilling Promise api is not necessary when zone.js is loaded. If you must load one, do so before loading zone.js.)")}static get root(){let e=i.current;for(;e.parent;)e=e.parent;return e}static get current(){return z.zone}static get currentTask(){return j}static __load_patch(t,r,s=!1){if(O.hasOwnProperty(t)){if(!s&&a)throw Error("Already loaded patch: "+t)}else if(!e["__Zone_disable_"+t]){const s="Zone:"+t;n(s),O[t]=r(e,i,C),o(s,s)}}get parent(){return this._parent}get name(){return this._name}get(e){const t=this.getZoneWith(e);if(t)return t._properties[e]}getZoneWith(e){let t=this;for(;t;){if(t._properties.hasOwnProperty(e))return t;t=t._parent}return null}fork(e){if(!e)throw new Error("ZoneSpec required!");return this._zoneDelegate.fork(this,e)}wrap(e,t){if("function"!=typeof e)throw new Error("Expecting function got: "+e);const n=this._zoneDelegate.intercept(this,e,t),o=this;return function(){return o.runGuarded(n,this,arguments,t)}}run(e,t,n,o){z={parent:z,zone:this};try{return this._zoneDelegate.invoke(this,e,t,n,o)}finally{z=z.parent}}runGuarded(e,t=null,n,o){z={parent:z,zone:this};try{try{return this._zoneDelegate.invoke(this,e,t,n,o)}catch(r){if(this._zoneDelegate.handleError(this,r))throw r}}finally{z=z.parent}}runTask(e,t,n){if(e.zone!=this)throw new Error("A task can only be run in the zone of creation! (Creation: "+(e.zone||y).name+"; Execution: "+this.name+")");if(e.state===v&&(e.type===P||e.type===D))return;const o=e.state!=E;o&&e._transitionTo(E,b),e.runCount++;const r=j;j=e,z={parent:z,zone:this};try{e.type==D&&e.data&&!e.data.isPeriodic&&(e.cancelFn=void 0);try{return this._zoneDelegate.invokeTask(this,e,t,n)}catch(s){if(this._zoneDelegate.handleError(this,s))throw s}}finally{e.state!==v&&e.state!==Z&&(e.type==P||e.data&&e.data.isPeriodic?o&&e._transitionTo(b,E):(e.runCount=0,this._updateTaskCount(e,-1),o&&e._transitionTo(v,E,v))),z=z.parent,j=r}}scheduleTask(e){if(e.zone&&e.zone!==this){let t=this;for(;t;){if(t===e.zone)throw Error(`can not reschedule task to ${this.name} which is descendants of the original zone ${e.zone.name}`);t=t.parent}}e._transitionTo(T,v);const t=[];e._zoneDelegates=t,e._zone=this;try{e=this._zoneDelegate.scheduleTask(this,e)}catch(n){throw e._transitionTo(Z,T,v),this._zoneDelegate.handleError(this,n),n}return e._zoneDelegates===t&&this._updateTaskCount(e,1),e.state==T&&e._transitionTo(b,T),e}scheduleMicroTask(e,t,n,o){return this.scheduleTask(new u(S,e,t,n,o,void 0))}scheduleMacroTask(e,t,n,o,r){return this.scheduleTask(new u(D,e,t,n,o,r))}scheduleEventTask(e,t,n,o,r){return this.scheduleTask(new u(P,e,t,n,o,r))}cancelTask(e){if(e.zone!=this)throw new Error("A task can only be cancelled in the zone of creation! (Creation: "+(e.zone||y).name+"; Execution: "+this.name+")");e._transitionTo(w,b,E);try{this._zoneDelegate.cancelTask(this,e)}catch(t){throw e._transitionTo(Z,w),this._zoneDelegate.handleError(this,t),t}return this._updateTaskCount(e,-1),e._transitionTo(v,w),e.runCount=0,e}_updateTaskCount(e,t){const n=e._zoneDelegates;-1==t&&(e._zoneDelegates=null);for(let o=0;o<n.length;o++)n[o]._updateTaskCount(e.type,t)}}i.__symbol__=s;const c={name:"",onHasTask:(e,t,n,o)=>e.hasTask(n,o),onScheduleTask:(e,t,n,o)=>e.scheduleTask(n,o),onInvokeTask:(e,t,n,o,r,s)=>e.invokeTask(n,o,r,s),onCancelTask:(e,t,n,o)=>e.cancelTask(n,o)};class l{constructor(e,t,n){this._taskCounts={microTask:0,macroTask:0,eventTask:0},this.zone=e,this._parentDelegate=t,this._forkZS=n&&(n&&n.onFork?n:t._forkZS),this._forkDlgt=n&&(n.onFork?t:t._forkDlgt),this._forkCurrZone=n&&(n.onFork?this.zone:t._forkCurrZone),this._interceptZS=n&&(n.onIntercept?n:t._interceptZS),this._interceptDlgt=n&&(n.onIntercept?t:t._interceptDlgt),this._interceptCurrZone=n&&(n.onIntercept?this.zone:t._interceptCurrZone),this._invokeZS=n&&(n.onInvoke?n:t._invokeZS),this._invokeDlgt=n&&(n.onInvoke?t:t._invokeDlgt),this._invokeCurrZone=n&&(n.onInvoke?this.zone:t._invokeCurrZone),this._handleErrorZS=n&&(n.onHandleError?n:t._handleErrorZS),this._handleErrorDlgt=n&&(n.onHandleError?t:t._handleErrorDlgt),this._handleErrorCurrZone=n&&(n.onHandleError?this.zone:t._handleErrorCurrZone),this._scheduleTaskZS=n&&(n.onScheduleTask?n:t._scheduleTaskZS),this._scheduleTaskDlgt=n&&(n.onScheduleTask?t:t._scheduleTaskDlgt),this._scheduleTaskCurrZone=n&&(n.onScheduleTask?this.zone:t._scheduleTaskCurrZone),this._invokeTaskZS=n&&(n.onInvokeTask?n:t._invokeTaskZS),this._invokeTaskDlgt=n&&(n.onInvokeTask?t:t._invokeTaskDlgt),this._invokeTaskCurrZone=n&&(n.onInvokeTask?this.zone:t._invokeTaskCurrZone),this._cancelTaskZS=n&&(n.onCancelTask?n:t._cancelTaskZS),this._cancelTaskDlgt=n&&(n.onCancelTask?t:t._cancelTaskDlgt),this._cancelTaskCurrZone=n&&(n.onCancelTask?this.zone:t._cancelTaskCurrZone),this._hasTaskZS=null,this._hasTaskDlgt=null,this._hasTaskDlgtOwner=null,this._hasTaskCurrZone=null;const o=n&&n.onHasTask;(o||t&&t._hasTaskZS)&&(this._hasTaskZS=o?n:c,this._hasTaskDlgt=t,this._hasTaskDlgtOwner=this,this._hasTaskCurrZone=e,n.onScheduleTask||(this._scheduleTaskZS=c,this._scheduleTaskDlgt=t,this._scheduleTaskCurrZone=this.zone),n.onInvokeTask||(this._invokeTaskZS=c,this._invokeTaskDlgt=t,this._invokeTaskCurrZone=this.zone),n.onCancelTask||(this._cancelTaskZS=c,this._cancelTaskDlgt=t,this._cancelTaskCurrZone=this.zone))}fork(e,t){return this._forkZS?this._forkZS.onFork(this._forkDlgt,this.zone,e,t):new i(e,t)}intercept(e,t,n){return this._interceptZS?this._interceptZS.onIntercept(this._interceptDlgt,this._interceptCurrZone,e,t,n):t}invoke(e,t,n,o,r){return this._invokeZS?this._invokeZS.onInvoke(this._invokeDlgt,this._invokeCurrZone,e,t,n,o,r):t.apply(n,o)}handleError(e,t){return!this._handleErrorZS||this._handleErrorZS.onHandleError(this._handleErrorDlgt,this._handleErrorCurrZone,e,t)}scheduleTask(e,t){let n=t;if(this._scheduleTaskZS)this._hasTaskZS&&n._zoneDelegates.push(this._hasTaskDlgtOwner),n=this._scheduleTaskZS.onScheduleTask(this._scheduleTaskDlgt,this._scheduleTaskCurrZone,e,t),n||(n=t);else if(t.scheduleFn)t.scheduleFn(t);else{if(t.type!=S)throw new Error("Task is missing scheduleFn.");k(t)}return n}invokeTask(e,t,n,o){return this._invokeTaskZS?this._invokeTaskZS.onInvokeTask(this._invokeTaskDlgt,this._invokeTaskCurrZone,e,t,n,o):t.callback.apply(n,o)}cancelTask(e,t){let n;if(this._cancelTaskZS)n=this._cancelTaskZS.onCancelTask(this._cancelTaskDlgt,this._cancelTaskCurrZone,e,t);else{if(!t.cancelFn)throw Error("Task is not cancelable");n=t.cancelFn(t)}return n}hasTask(e,t){try{this._hasTaskZS&&this._hasTaskZS.onHasTask(this._hasTaskDlgt,this._hasTaskCurrZone,e,t)}catch(n){this.handleError(e,n)}}_updateTaskCount(e,t){const n=this._taskCounts,o=n[e],r=n[e]=o+t;if(r<0)throw new Error("More tasks executed then were scheduled.");0!=o&&0!=r||this.hasTask(this.zone,{microTask:n.microTask>0,macroTask:n.macroTask>0,eventTask:n.eventTask>0,change:e})}}class u{constructor(t,n,o,r,s,a){if(this._zone=null,this.runCount=0,this._zoneDelegates=null,this._state="notScheduled",this.type=t,this.source=n,this.data=r,this.scheduleFn=s,this.cancelFn=a,!o)throw new Error("callback is not defined");this.callback=o;const i=this;this.invoke=t===P&&r&&r.useG?u.invokeTask:function(){return u.invokeTask.call(e,i,this,arguments)}}static invokeTask(e,t,n){e||(e=this),I++;try{return e.runCount++,e.zone.runTask(e,t,n)}finally{1==I&&m(),I--}}get zone(){return this._zone}get state(){return this._state}cancelScheduleRequest(){this._transitionTo(v,T)}_transitionTo(e,t,n){if(this._state!==t&&this._state!==n)throw new Error(`${this.type} '${this.source}': can not transition to '${e}', expecting state '${t}'${n?" or '"+n+"'":""}, was '${this._state}'.`);this._state=e,e==v&&(this._zoneDelegates=null)}toString(){return this.data&&void 0!==this.data.handleId?this.data.handleId.toString():Object.prototype.toString.call(this)}toJSON(){return{type:this.type,state:this.state,source:this.source,zone:this.zone.name,runCount:this.runCount}}}const h=s("setTimeout"),p=s("Promise"),f=s("then");let d,g=[],_=!1;function k(t){if(0===I&&0===g.length)if(d||e[p]&&(d=e[p].resolve(0)),d){let e=d[f];e||(e=d.then),e.call(d,m)}else e[h](m,0);t&&g.push(t)}function m(){if(!_){for(_=!0;g.length;){const t=g;g=[];for(let n=0;n<t.length;n++){const o=t[n];try{o.zone.runTask(o,null,null)}catch(e){C.onUnhandledError(e)}}}C.microtaskDrainDone(),_=!1}}const y={name:"NO ZONE"},v="notScheduled",T="scheduling",b="scheduled",E="running",w="canceling",Z="unknown",S="microTask",D="macroTask",P="eventTask",O={},C={symbol:s,currentZoneFrame:()=>z,onUnhandledError:R,microtaskDrainDone:R,scheduleMicroTask:k,showUncaughtError:()=>!i[s("ignoreConsoleErrorUncaughtError")],patchEventTarget:()=>[],patchOnProperties:R,patchMethod:()=>R,bindArguments:()=>[],patchThen:()=>R,patchMacroTask:()=>R,patchEventPrototype:()=>R,isIEOrEdge:()=>!1,getGlobalObjects:()=>{},ObjectDefineProperty:()=>R,ObjectGetOwnPropertyDescriptor:()=>{},ObjectCreate:()=>{},ArraySlice:()=>[],patchClass:()=>R,wrapWithCurrentZone:()=>R,filterProperties:()=>[],attachOriginToPatched:()=>R,_redefineProperty:()=>R,patchCallbacks:()=>R};let z={parent:null,zone:new i(null,null)},j=null,I=0;function R(){}o("Zone","Zone"),e.Zone=i}("undefined"!=typeof window&&window||"undefined"!=typeof self&&self||global);const e=Object.getOwnPropertyDescriptor,t=Object.defineProperty,n=Object.getPrototypeOf,o=Object.create,r=Array.prototype.slice,s="addEventListener",a="removeEventListener",i=Zone.__symbol__(s),c=Zone.__symbol__(a),l="true",u="false",h=Zone.__symbol__("");function p(e,t){return Zone.current.wrap(e,t)}function f(e,t,n,o,r){return Zone.current.scheduleMacroTask(e,t,n,o,r)}const d=Zone.__symbol__,g="undefined"!=typeof window,_=g?window:void 0,k=g&&_||"object"==typeof self&&self||global,m=[null];function y(e,t){for(let n=e.length-1;n>=0;n--)"function"==typeof e[n]&&(e[n]=p(e[n],t+"_"+n));return e}function v(e){return!e||!1!==e.writable&&!("function"==typeof e.get&&void 0===e.set)}const T="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope,b=!("nw"in k)&&void 0!==k.process&&"[object process]"==={}.toString.call(k.process),E=!b&&!T&&!(!g||!_.HTMLElement),w=void 0!==k.process&&"[object process]"==={}.toString.call(k.process)&&!T&&!(!g||!_.HTMLElement),Z={},S=function(e){if(!(e=e||k.event))return;let t=Z[e.type];t||(t=Z[e.type]=d("ON_PROPERTY"+e.type));const n=this||e.target||k,o=n[t];let r;if(E&&n===_&&"error"===e.type){const t=e;r=o&&o.call(this,t.message,t.filename,t.lineno,t.colno,t.error),!0===r&&e.preventDefault()}else r=o&&o.apply(this,arguments),null==r||r||e.preventDefault();return r};function D(n,o,r){let s=e(n,o);if(!s&&r&&e(r,o)&&(s={enumerable:!0,configurable:!0}),!s||!s.configurable)return;const a=d("on"+o+"patched");if(n.hasOwnProperty(a)&&n[a])return;delete s.writable,delete s.value;const i=s.get,c=s.set,l=o.substr(2);let u=Z[l];u||(u=Z[l]=d("ON_PROPERTY"+l)),s.set=function(e){let t=this;t||n!==k||(t=k),t&&(t[u]&&t.removeEventListener(l,S),c&&c.apply(t,m),"function"==typeof e?(t[u]=e,t.addEventListener(l,S,!1)):t[u]=null)},s.get=function(){let e=this;if(e||n!==k||(e=k),!e)return null;const t=e[u];if(t)return t;if(i){let t=i&&i.call(this);if(t)return s.set.call(this,t),"function"==typeof e.removeAttribute&&e.removeAttribute(o),t}return null},t(n,o,s),n[a]=!0}function P(e,t,n){if(t)for(let o=0;o<t.length;o++)D(e,"on"+t[o],n);else{const t=[];for(const n in e)"on"==n.substr(0,2)&&t.push(n);for(let o=0;o<t.length;o++)D(e,t[o],n)}}const O=d("originalInstance");function C(e){const n=k[e];if(!n)return;k[d(e)]=n,k[e]=function(){const t=y(arguments,e);switch(t.length){case 0:this[O]=new n;break;case 1:this[O]=new n(t[0]);break;case 2:this[O]=new n(t[0],t[1]);break;case 3:this[O]=new n(t[0],t[1],t[2]);break;case 4:this[O]=new n(t[0],t[1],t[2],t[3]);break;default:throw new Error("Arg list too long.")}},I(k[e],n);const o=new n(function(){});let r;for(r in o)"XMLHttpRequest"===e&&"responseBlob"===r||function(n){"function"==typeof o[n]?k[e].prototype[n]=function(){return this[O][n].apply(this[O],arguments)}:t(k[e].prototype,n,{set:function(t){"function"==typeof t?(this[O][n]=p(t,e+"."+n),I(this[O][n],t)):this[O][n]=t},get:function(){return this[O][n]}})}(r);for(r in n)"prototype"!==r&&n.hasOwnProperty(r)&&(k[e][r]=n[r])}function z(t,o,r){let s=t;for(;s&&!s.hasOwnProperty(o);)s=n(s);!s&&t[o]&&(s=t);const a=d(o);let i=null;if(s&&(!(i=s[a])||!s.hasOwnProperty(a))&&(i=s[a]=s[o],v(s&&e(s,o)))){const e=r(i,a,o);s[o]=function(){return e(this,arguments)},I(s[o],i)}return i}function j(e,t,n){let o=null;function r(e){const t=e.data;return t.args[t.cbIdx]=function(){e.invoke.apply(this,arguments)},o.apply(t.target,t.args),e}o=z(e,t,e=>function(t,o){const s=n(t,o);return s.cbIdx>=0&&"function"==typeof o[s.cbIdx]?f(s.name,o[s.cbIdx],s,r):e.apply(t,o)})}function I(e,t){e[d("OriginalDelegate")]=t}let R=!1,M=!1;function N(){try{const e=_.navigator.userAgent;if(-1!==e.indexOf("MSIE ")||-1!==e.indexOf("Trident/"))return!0}catch(e){}return!1}function x(){if(R)return M;R=!0;try{const e=_.navigator.userAgent;-1===e.indexOf("MSIE ")&&-1===e.indexOf("Trident/")&&-1===e.indexOf("Edge/")||(M=!0)}catch(e){}return M}Zone.__load_patch("ZoneAwarePromise",(e,t,n)=>{const o=Object.getOwnPropertyDescriptor,r=Object.defineProperty,s=n.symbol,a=[],i=!0===e[s("DISABLE_WRAPPING_UNCAUGHT_PROMISE_REJECTION")],c=s("Promise"),l=s("then");n.onUnhandledError=e=>{if(n.showUncaughtError()){const t=e&&e.rejection;t?console.error("Unhandled Promise rejection:",t instanceof Error?t.message:t,"; Zone:",e.zone.name,"; Task:",e.task&&e.task.source,"; Value:",t,t instanceof Error?t.stack:void 0):console.error(e)}},n.microtaskDrainDone=()=>{for(;a.length;){const t=a.shift();try{t.zone.runGuarded(()=>{if(t.throwOriginal)throw t.rejection;throw t})}catch(e){h(e)}}};const u=s("unhandledPromiseRejectionHandler");function h(e){n.onUnhandledError(e);try{const n=t[u];"function"==typeof n&&n.call(this,e)}catch(o){}}function p(e){return e&&e.then}function f(e){return e}function d(e){return C.reject(e)}const g=s("state"),_=s("value"),k=s("finally"),m=s("parentPromiseValue"),y=s("parentPromiseState"),v=null,T=!0,b=!1;function E(e,t){return n=>{try{Z(e,t,n)}catch(o){Z(e,!1,o)}}}const w=s("currentTaskTrace");function Z(e,o,s){const c=function(){let e=!1;return function(t){return function(){e||(e=!0,t.apply(null,arguments))}}}();if(e===s)throw new TypeError("Promise resolved with itself");if(e[g]===v){let h=null;try{"object"!=typeof s&&"function"!=typeof s||(h=s&&s.then)}catch(u){return c(()=>{Z(e,!1,u)})(),e}if(o!==b&&s instanceof C&&s.hasOwnProperty(g)&&s.hasOwnProperty(_)&&s[g]!==v)D(s),Z(e,s[g],s[_]);else if(o!==b&&"function"==typeof h)try{h.call(s,c(E(e,o)),c(E(e,!1)))}catch(u){c(()=>{Z(e,!1,u)})()}else{e[g]=o;const c=e[_];if(e[_]=s,e[k]===k&&o===T&&(e[g]=e[y],e[_]=e[m]),o===b&&s instanceof Error){const e=t.currentTask&&t.currentTask.data&&t.currentTask.data.__creationTrace__;e&&r(s,w,{configurable:!0,enumerable:!1,writable:!0,value:e})}for(let t=0;t<c.length;)P(e,c[t++],c[t++],c[t++],c[t++]);if(0==c.length&&o==b){e[g]=0;let o=s;try{throw new Error("Uncaught (in promise): "+((l=s)&&l.toString===Object.prototype.toString?(l.constructor&&l.constructor.name||"")+": "+JSON.stringify(l):l?l.toString():Object.prototype.toString.call(l))+(s&&s.stack?"\n"+s.stack:""))}catch(u){o=u}i&&(o.throwOriginal=!0),o.rejection=s,o.promise=e,o.zone=t.current,o.task=t.currentTask,a.push(o),n.scheduleMicroTask()}}}var l;return e}const S=s("rejectionHandledHandler");function D(e){if(0===e[g]){try{const n=t[S];n&&"function"==typeof n&&n.call(this,{rejection:e[_],promise:e})}catch(n){}e[g]=b;for(let t=0;t<a.length;t++)e===a[t].promise&&a.splice(t,1)}}function P(e,t,n,o,r){D(e);const s=e[g],a=s?"function"==typeof o?o:f:"function"==typeof r?r:d;t.scheduleMicroTask("Promise.then",()=>{try{const o=e[_],r=!!n&&k===n[k];r&&(n[m]=o,n[y]=s);const i=t.run(a,void 0,r&&a!==d&&a!==f?[]:[o]);Z(n,!0,i)}catch(o){Z(n,!1,o)}},n)}const O=function(){};class C{static toString(){return"function ZoneAwarePromise() { [native code] }"}static resolve(e){return Z(new this(null),T,e)}static reject(e){return Z(new this(null),b,e)}static race(e){let t,n,o=new this((e,o)=>{t=e,n=o});function r(e){t(e)}function s(e){n(e)}for(let a of e)p(a)||(a=this.resolve(a)),a.then(r,s);return o}static all(e){return C.allWithCallback(e)}static allSettled(e){return(this&&this.prototype instanceof C?this:C).allWithCallback(e,{thenCallback:e=>({status:"fulfilled",value:e}),errorCallback:e=>({status:"rejected",reason:e})})}static allWithCallback(e,t){let n,o,r=new this((e,t)=>{n=e,o=t}),s=2,a=0;const i=[];for(let l of e){p(l)||(l=this.resolve(l));const e=a;try{l.then(o=>{i[e]=t?t.thenCallback(o):o,s--,0===s&&n(i)},r=>{t?(i[e]=t.errorCallback(r),s--,0===s&&n(i)):o(r)})}catch(c){o(c)}s++,a++}return s-=2,0===s&&n(i),r}constructor(e){const t=this;if(!(t instanceof C))throw new Error("Must be an instanceof Promise.");t[g]=v,t[_]=[];try{e&&e(E(t,T),E(t,b))}catch(n){Z(t,!1,n)}}get[Symbol.toStringTag](){return"Promise"}get[Symbol.species](){return C}then(e,n){let o=this.constructor[Symbol.species];o&&"function"==typeof o||(o=this.constructor||C);const r=new o(O),s=t.current;return this[g]==v?this[_].push(s,r,e,n):P(this,s,r,e,n),r}catch(e){return this.then(null,e)}finally(e){let n=this.constructor[Symbol.species];n&&"function"==typeof n||(n=C);const o=new n(O);o[k]=k;const r=t.current;return this[g]==v?this[_].push(r,o,e,e):P(this,r,o,e,e),o}}C.resolve=C.resolve,C.reject=C.reject,C.race=C.race,C.all=C.all;const j=e[c]=e.Promise;e.Promise=C;const I=s("thenPatched");function R(e){const t=e.prototype,n=o(t,"then");if(n&&(!1===n.writable||!n.configurable))return;const r=t.then;t[l]=r,e.prototype.then=function(e,t){return new C((e,t)=>{r.call(this,e,t)}).then(e,t)},e[I]=!0}return n.patchThen=R,j&&(R(j),z(e,"fetch",e=>{return t=e,function(e,n){let o=t.apply(e,n);if(o instanceof C)return o;let r=o.constructor;return r[I]||R(r),o};var t})),Promise[t.__symbol__("uncaughtPromiseErrors")]=a,C}),Zone.__load_patch("toString",e=>{const t=Function.prototype.toString,n=d("OriginalDelegate"),o=d("Promise"),r=d("Error"),s=function(){if("function"==typeof this){const s=this[n];if(s)return"function"==typeof s?t.call(s):Object.prototype.toString.call(s);if(this===Promise){const n=e[o];if(n)return t.call(n)}if(this===Error){const n=e[r];if(n)return t.call(n)}}return t.call(this)};s[n]=t,Function.prototype.toString=s;const a=Object.prototype.toString;Object.prototype.toString=function(){return"function"==typeof Promise&&this instanceof Promise?"[object Promise]":a.call(this)}});let L=!1;if("undefined"!=typeof window)try{const e=Object.defineProperty({},"passive",{get:function(){L=!0}});window.addEventListener("test",e,e),window.removeEventListener("test",e,e)}catch(he){L=!1}const A={useG:!0},H={},F={},q=new RegExp("^"+h+"(\\w+)(true|false)$"),G=d("propagationStopped");function B(e,t){const n=(t?t(e):e)+u,o=(t?t(e):e)+l,r=h+n,s=h+o;H[e]={},H[e].false=r,H[e].true=s}function W(e,t,o){const r=o&&o.add||s,i=o&&o.rm||a,c=o&&o.listeners||"eventListeners",p=o&&o.rmAll||"removeAllListeners",f=d(r),g="."+r+":",_=function(e,t,n){if(e.isRemoved)return;const o=e.callback;"object"==typeof o&&o.handleEvent&&(e.callback=e=>o.handleEvent(e),e.originalDelegate=o),e.invoke(e,t,[n]);const r=e.options;r&&"object"==typeof r&&r.once&&t[i].call(t,n.type,e.originalDelegate?e.originalDelegate:e.callback,r)},k=function(t){if(!(t=t||e.event))return;const n=this||t.target||e,o=n[H[t.type].false];if(o)if(1===o.length)_(o[0],n,t);else{const e=o.slice();for(let o=0;o<e.length&&(!t||!0!==t[G]);o++)_(e[o],n,t)}},m=function(t){if(!(t=t||e.event))return;const n=this||t.target||e,o=n[H[t.type].true];if(o)if(1===o.length)_(o[0],n,t);else{const e=o.slice();for(let o=0;o<e.length&&(!t||!0!==t[G]);o++)_(e[o],n,t)}};function y(t,o){if(!t)return!1;let s=!0;o&&void 0!==o.useG&&(s=o.useG);const a=o&&o.vh;let _=!0;o&&void 0!==o.chkDup&&(_=o.chkDup);let y=!1;o&&void 0!==o.rt&&(y=o.rt);let v=t;for(;v&&!v.hasOwnProperty(r);)v=n(v);if(!v&&t[r]&&(v=t),!v)return!1;if(v[f])return!1;const T=o&&o.eventNameToString,E={},w=v[f]=v[r],Z=v[d(i)]=v[i],S=v[d(c)]=v[c],D=v[d(p)]=v[p];let P;function O(e,t){return!L&&"object"==typeof e&&e?!!e.capture:L&&t?"boolean"==typeof e?{capture:e,passive:!0}:e?"object"==typeof e&&!1!==e.passive?Object.assign(Object.assign({},e),{passive:!0}):e:{passive:!0}:e}o&&o.prepend&&(P=v[d(o.prepend)]=v[o.prepend]);const C=s?function(e){if(!E.isExisting)return w.call(E.target,E.eventName,E.capture?m:k,E.options)}:function(e){return w.call(E.target,E.eventName,e.invoke,E.options)},z=s?function(e){if(!e.isRemoved){const t=H[e.eventName];let n;t&&(n=t[e.capture?l:u]);const o=n&&e.target[n];if(o)for(let r=0;r<o.length;r++)if(o[r]===e){o.splice(r,1),e.isRemoved=!0,0===o.length&&(e.allRemoved=!0,e.target[n]=null);break}}if(e.allRemoved)return Z.call(e.target,e.eventName,e.capture?m:k,e.options)}:function(e){return Z.call(e.target,e.eventName,e.invoke,e.options)},j=o&&o.diff?o.diff:function(e,t){const n=typeof t;return"function"===n&&e.callback===t||"object"===n&&e.originalDelegate===t},R=Zone[d("UNPATCHED_EVENTS")],M=e[d("PASSIVE_EVENTS")],N=function(t,n,r,i,c=!1,h=!1){return function(){const p=this||e;let f=arguments[0];o&&o.transferEventName&&(f=o.transferEventName(f));let d=arguments[1];if(!d)return t.apply(this,arguments);if(b&&"uncaughtException"===f)return t.apply(this,arguments);let g=!1;if("function"!=typeof d){if(!d.handleEvent)return t.apply(this,arguments);g=!0}if(a&&!a(t,d,p,arguments))return;const k=L&&!!M&&-1!==M.indexOf(f),m=O(arguments[2],k);if(R)for(let e=0;e<R.length;e++)if(f===R[e])return k?t.call(p,f,d,m):t.apply(this,arguments);const y=!!m&&("boolean"==typeof m||m.capture),v=!(!m||"object"!=typeof m)&&m.once,w=Zone.current;let Z=H[f];Z||(B(f,T),Z=H[f]);const S=Z[y?l:u];let D,P=p[S],C=!1;if(P){if(C=!0,_)for(let e=0;e<P.length;e++)if(j(P[e],d))return}else P=p[S]=[];const z=p.constructor.name,I=F[z];I&&(D=I[f]),D||(D=z+n+(T?T(f):f)),E.options=m,v&&(E.options.once=!1),E.target=p,E.capture=y,E.eventName=f,E.isExisting=C;const N=s?A:void 0;N&&(N.taskData=E);const x=w.scheduleEventTask(D,d,N,r,i);return E.target=null,N&&(N.taskData=null),v&&(m.once=!0),(L||"boolean"!=typeof x.options)&&(x.options=m),x.target=p,x.capture=y,x.eventName=f,g&&(x.originalDelegate=d),h?P.unshift(x):P.push(x),c?p:void 0}};return v[r]=N(w,g,C,z,y),P&&(v.prependListener=N(P,".prependListener:",function(e){return P.call(E.target,E.eventName,e.invoke,E.options)},z,y,!0)),v[i]=function(){const t=this||e;let n=arguments[0];o&&o.transferEventName&&(n=o.transferEventName(n));const r=arguments[2],s=!!r&&("boolean"==typeof r||r.capture),i=arguments[1];if(!i)return Z.apply(this,arguments);if(a&&!a(Z,i,t,arguments))return;const c=H[n];let p;c&&(p=c[s?l:u]);const f=p&&t[p];if(f)for(let e=0;e<f.length;e++){const o=f[e];if(j(o,i))return f.splice(e,1),o.isRemoved=!0,0===f.length&&(o.allRemoved=!0,t[p]=null,"string"==typeof n)&&(t[h+"ON_PROPERTY"+n]=null),o.zone.cancelTask(o),y?t:void 0}return Z.apply(this,arguments)},v[c]=function(){const t=this||e;let n=arguments[0];o&&o.transferEventName&&(n=o.transferEventName(n));const r=[],s=U(t,T?T(n):n);for(let e=0;e<s.length;e++){const t=s[e];r.push(t.originalDelegate?t.originalDelegate:t.callback)}return r},v[p]=function(){const t=this||e;let n=arguments[0];if(n){o&&o.transferEventName&&(n=o.transferEventName(n));const e=H[n];if(e){const o=t[e.false],r=t[e.true];if(o){const e=o.slice();for(let t=0;t<e.length;t++){const o=e[t];this[i].call(this,n,o.originalDelegate?o.originalDelegate:o.callback,o.options)}}if(r){const e=r.slice();for(let t=0;t<e.length;t++){const o=e[t];this[i].call(this,n,o.originalDelegate?o.originalDelegate:o.callback,o.options)}}}}else{const e=Object.keys(t);for(let t=0;t<e.length;t++){const n=q.exec(e[t]);let o=n&&n[1];o&&"removeListener"!==o&&this[p].call(this,o)}this[p].call(this,"removeListener")}if(y)return this},I(v[r],w),I(v[i],Z),D&&I(v[p],D),S&&I(v[c],S),!0}let v=[];for(let n=0;n<t.length;n++)v[n]=y(t[n],o);return v}function U(e,t){if(!t){const n=[];for(let o in e){const r=q.exec(o);let s=r&&r[1];if(s&&(!t||s===t)){const t=e[o];if(t)for(let e=0;e<t.length;e++)n.push(t[e])}}return n}let n=H[t];n||(B(t),n=H[t]);const o=e[n.false],r=e[n.true];return o?r?o.concat(r):o.slice():r?r.slice():[]}function V(e,t){const n=e.Event;n&&n.prototype&&t.patchMethod(n.prototype,"stopImmediatePropagation",e=>function(t,n){t[G]=!0,e&&e.apply(t,n)})}function $(e,t,n,o,r){const s=Zone.__symbol__(o);if(t[s])return;const a=t[s]=t[o];t[o]=function(s,i,c){return i&&i.prototype&&r.forEach(function(t){const r=`${n}.${o}::`+t,s=i.prototype;if(s.hasOwnProperty(t)){const n=e.ObjectGetOwnPropertyDescriptor(s,t);n&&n.value?(n.value=e.wrapWithCurrentZone(n.value,r),e._redefineProperty(i.prototype,t,n)):s[t]&&(s[t]=e.wrapWithCurrentZone(s[t],r))}else s[t]&&(s[t]=e.wrapWithCurrentZone(s[t],r))}),a.call(t,s,i,c)},e.attachOriginToPatched(t[o],a)}const X=["absolutedeviceorientation","afterinput","afterprint","appinstalled","beforeinstallprompt","beforeprint","beforeunload","devicelight","devicemotion","deviceorientation","deviceorientationabsolute","deviceproximity","hashchange","languagechange","message","mozbeforepaint","offline","online","paint","pageshow","pagehide","popstate","rejectionhandled","storage","unhandledrejection","unload","userproximity","vrdisplayconnected","vrdisplaydisconnected","vrdisplaypresentchange"],Y=["encrypted","waitingforkey","msneedkey","mozinterruptbegin","mozinterruptend"],J=["load"],K=["blur","error","focus","load","resize","scroll","messageerror"],Q=["bounce","finish","start"],ee=["loadstart","progress","abort","error","load","progress","timeout","loadend","readystatechange"],te=["upgradeneeded","complete","abort","success","error","blocked","versionchange","close"],ne=["close","error","open","message"],oe=["error","message"],re=["abort","animationcancel","animationend","animationiteration","auxclick","beforeinput","blur","cancel","canplay","canplaythrough","change","compositionstart","compositionupdate","compositionend","cuechange","click","close","contextmenu","curechange","dblclick","drag","dragend","dragenter","dragexit","dragleave","dragover","drop","durationchange","emptied","ended","error","focus","focusin","focusout","gotpointercapture","input","invalid","keydown","keypress","keyup","load","loadstart","loadeddata","loadedmetadata","lostpointercapture","mousedown","mouseenter","mouseleave","mousemove","mouseout","mouseover","mouseup","mousewheel","orientationchange","pause","play","playing","pointercancel","pointerdown","pointerenter","pointerleave","pointerlockchange","mozpointerlockchange","webkitpointerlockerchange","pointerlockerror","mozpointerlockerror","webkitpointerlockerror","pointermove","pointout","pointerover","pointerup","progress","ratechange","reset","resize","scroll","seeked","seeking","select","selectionchange","selectstart","show","sort","stalled","submit","suspend","timeupdate","volumechange","touchcancel","touchmove","touchstart","touchend","transitioncancel","transitionend","waiting","wheel"].concat(["webglcontextrestored","webglcontextlost","webglcontextcreationerror"],["autocomplete","autocompleteerror"],["toggle"],["afterscriptexecute","beforescriptexecute","DOMContentLoaded","freeze","fullscreenchange","mozfullscreenchange","webkitfullscreenchange","msfullscreenchange","fullscreenerror","mozfullscreenerror","webkitfullscreenerror","msfullscreenerror","readystatechange","visibilitychange","resume"],X,["beforecopy","beforecut","beforepaste","copy","cut","paste","dragstart","loadend","animationstart","search","transitionrun","transitionstart","webkitanimationend","webkitanimationiteration","webkitanimationstart","webkittransitionend"],["activate","afterupdate","ariarequest","beforeactivate","beforedeactivate","beforeeditfocus","beforeupdate","cellchange","controlselect","dataavailable","datasetchanged","datasetcomplete","errorupdate","filterchange","layoutcomplete","losecapture","move","moveend","movestart","propertychange","resizeend","resizestart","rowenter","rowexit","rowsdelete","rowsinserted","command","compassneedscalibration","deactivate","help","mscontentzoom","msmanipulationstatechanged","msgesturechange","msgesturedoubletap","msgestureend","msgesturehold","msgesturestart","msgesturetap","msgotpointercapture","msinertiastart","mslostpointercapture","mspointercancel","mspointerdown","mspointerenter","mspointerhover","mspointerleave","mspointermove","mspointerout","mspointerover","mspointerup","pointerout","mssitemodejumplistitemremoved","msthumbnailclick","stop","storagecommit"]);function se(e,t,n){if(!n||0===n.length)return t;const o=n.filter(t=>t.target===e);if(!o||0===o.length)return t;const r=o[0].ignoreProperties;return t.filter(e=>-1===r.indexOf(e))}function ae(e,t,n,o){e&&P(e,se(e,t,n),o)}function ie(e,t){if(b&&!w)return;if(Zone[e.symbol("patchEvents")])return;const o="undefined"!=typeof WebSocket,r=t.__Zone_ignore_on_properties;if(E){const e=window,t=N()?[{target:e,ignoreProperties:["error"]}]:[];ae(e,re.concat(["messageerror"]),r?r.concat(t):r,n(e)),ae(Document.prototype,re,r),void 0!==e.SVGElement&&ae(e.SVGElement.prototype,re,r),ae(Element.prototype,re,r),ae(HTMLElement.prototype,re,r),ae(HTMLMediaElement.prototype,Y,r),ae(HTMLFrameSetElement.prototype,X.concat(K),r),ae(HTMLBodyElement.prototype,X.concat(K),r),ae(HTMLFrameElement.prototype,J,r),ae(HTMLIFrameElement.prototype,J,r);const o=e.HTMLMarqueeElement;o&&ae(o.prototype,Q,r);const s=e.Worker;s&&ae(s.prototype,oe,r)}const s=t.XMLHttpRequest;s&&ae(s.prototype,ee,r);const a=t.XMLHttpRequestEventTarget;a&&ae(a&&a.prototype,ee,r),"undefined"!=typeof IDBIndex&&(ae(IDBIndex.prototype,te,r),ae(IDBRequest.prototype,te,r),ae(IDBOpenDBRequest.prototype,te,r),ae(IDBDatabase.prototype,te,r),ae(IDBTransaction.prototype,te,r),ae(IDBCursor.prototype,te,r)),o&&ae(WebSocket.prototype,ne,r)}Zone.__load_patch("util",(n,i,c)=>{c.patchOnProperties=P,c.patchMethod=z,c.bindArguments=y,c.patchMacroTask=j;const f=i.__symbol__("BLACK_LISTED_EVENTS"),d=i.__symbol__("UNPATCHED_EVENTS");n[d]&&(n[f]=n[d]),n[f]&&(i[f]=i[d]=n[f]),c.patchEventPrototype=V,c.patchEventTarget=W,c.isIEOrEdge=x,c.ObjectDefineProperty=t,c.ObjectGetOwnPropertyDescriptor=e,c.ObjectCreate=o,c.ArraySlice=r,c.patchClass=C,c.wrapWithCurrentZone=p,c.filterProperties=se,c.attachOriginToPatched=I,c._redefineProperty=Object.defineProperty,c.patchCallbacks=$,c.getGlobalObjects=()=>({globalSources:F,zoneSymbolEventNames:H,eventNames:re,isBrowser:E,isMix:w,isNode:b,TRUE_STR:l,FALSE_STR:u,ZONE_SYMBOL_PREFIX:h,ADD_EVENT_LISTENER_STR:s,REMOVE_EVENT_LISTENER_STR:a})});const ce=d("zoneTask");function le(e,t,n,o){let r=null,s=null;n+=o;const a={};function i(t){const n=t.data;return n.args[0]=function(){return t.invoke.apply(this,arguments)},n.handleId=r.apply(e,n.args),t}function c(t){return s.call(e,t.data.handleId)}r=z(e,t+=o,n=>function(r,s){if("function"==typeof s[0]){const e={isPeriodic:"Interval"===o,delay:"Timeout"===o||"Interval"===o?s[1]||0:void 0,args:s},n=s[0];s[0]=function(){try{return n.apply(this,arguments)}finally{e.isPeriodic||("number"==typeof e.handleId?delete a[e.handleId]:e.handleId&&(e.handleId[ce]=null))}};const r=f(t,s[0],e,i,c);if(!r)return r;const l=r.data.handleId;return"number"==typeof l?a[l]=r:l&&(l[ce]=r),l&&l.ref&&l.unref&&"function"==typeof l.ref&&"function"==typeof l.unref&&(r.ref=l.ref.bind(l),r.unref=l.unref.bind(l)),"number"==typeof l||l?l:r}return n.apply(e,s)}),s=z(e,n,t=>function(n,o){const r=o[0];let s;"number"==typeof r?s=a[r]:(s=r&&r[ce],s||(s=r)),s&&"string"==typeof s.type?"notScheduled"!==s.state&&(s.cancelFn&&s.data.isPeriodic||0===s.runCount)&&("number"==typeof r?delete a[r]:r&&(r[ce]=null),s.zone.cancelTask(s)):t.apply(e,o)})}function ue(e,t){if(Zone[t.symbol("patchEventTarget")])return;const{eventNames:n,zoneSymbolEventNames:o,TRUE_STR:r,FALSE_STR:s,ZONE_SYMBOL_PREFIX:a}=t.getGlobalObjects();for(let c=0;c<n.length;c++){const e=n[c],t=a+(e+s),i=a+(e+r);o[e]={},o[e][s]=t,o[e][r]=i}const i=e.EventTarget;return i&&i.prototype?(t.patchEventTarget(e,[i&&i.prototype]),!0):void 0}Zone.__load_patch("legacy",e=>{const t=e[Zone.__symbol__("legacyPatch")];t&&t()}),Zone.__load_patch("queueMicrotask",(e,t,n)=>{n.patchMethod(e,"queueMicrotask",e=>function(e,n){t.current.scheduleMicroTask("queueMicrotask",n[0])})}),Zone.__load_patch("timers",e=>{const t="set",n="clear";le(e,t,n,"Timeout"),le(e,t,n,"Interval"),le(e,t,n,"Immediate")}),Zone.__load_patch("requestAnimationFrame",e=>{le(e,"request","cancel","AnimationFrame"),le(e,"mozRequest","mozCancel","AnimationFrame"),le(e,"webkitRequest","webkitCancel","AnimationFrame")}),Zone.__load_patch("blocking",(e,t)=>{const n=["alert","prompt","confirm"];for(let o=0;o<n.length;o++)z(e,n[o],(n,o,r)=>function(o,s){return t.current.run(n,e,s,r)})}),Zone.__load_patch("EventTarget",(e,t,n)=>{!function(e,t){t.patchEventPrototype(e,t)}(e,n),ue(e,n);const o=e.XMLHttpRequestEventTarget;o&&o.prototype&&n.patchEventTarget(e,[o.prototype])}),Zone.__load_patch("MutationObserver",(e,t,n)=>{C("MutationObserver"),C("WebKitMutationObserver")}),Zone.__load_patch("IntersectionObserver",(e,t,n)=>{C("IntersectionObserver")}),Zone.__load_patch("FileReader",(e,t,n)=>{C("FileReader")}),Zone.__load_patch("on_property",(e,t,n)=>{ie(n,e)}),Zone.__load_patch("customElements",(e,t,n)=>{!function(e,t){const{isBrowser:n,isMix:o}=t.getGlobalObjects();(n||o)&&e.customElements&&"customElements"in e&&t.patchCallbacks(t,e.customElements,"customElements","define",["connectedCallback","disconnectedCallback","adoptedCallback","attributeChangedCallback"])}(e,n)}),Zone.__load_patch("XHR",(e,t)=>{!function(e){const u=e.XMLHttpRequest;if(!u)return;const h=u.prototype;let p=h[i],g=h[c];if(!p){const t=e.XMLHttpRequestEventTarget;if(t){const e=t.prototype;p=e[i],g=e[c]}}const _="readystatechange",k="scheduled";function m(e){const o=e.data,a=o.target;a[s]=!1,a[l]=!1;const u=a[r];p||(p=a[i],g=a[c]),u&&g.call(a,_,u);const h=a[r]=()=>{if(a.readyState===a.DONE)if(!o.aborted&&a[s]&&e.state===k){const n=a[t.__symbol__("loadfalse")];if(0!==a.status&&n&&n.length>0){const r=e.invoke;e.invoke=function(){const n=a[t.__symbol__("loadfalse")];for(let t=0;t<n.length;t++)n[t]===e&&n.splice(t,1);o.aborted||e.state!==k||r.call(e)},n.push(e)}else e.invoke()}else o.aborted||!1!==a[s]||(a[l]=!0)};return p.call(a,_,h),a[n]||(a[n]=e),w.apply(a,o.args),a[s]=!0,e}function y(){}function v(e){const t=e.data;return t.aborted=!0,Z.apply(t.target,t.args)}const T=z(h,"open",()=>function(e,t){return e[o]=0==t[2],e[a]=t[1],T.apply(e,t)}),b=d("fetchTaskAborting"),E=d("fetchTaskScheduling"),w=z(h,"send",()=>function(e,n){if(!0===t.current[E])return w.apply(e,n);if(e[o])return w.apply(e,n);{const t={target:e,url:e[a],isPeriodic:!1,args:n,aborted:!1},o=f("XMLHttpRequest.send",y,t,m,v);e&&!0===e[l]&&!t.aborted&&o.state===k&&o.invoke()}}),Z=z(h,"abort",()=>function(e,o){const r=e[n];if(r&&"string"==typeof r.type){if(null==r.cancelFn||r.data&&r.data.aborted)return;r.zone.cancelTask(r)}else if(!0===t.current[b])return Z.apply(e,o)})}(e);const n=d("xhrTask"),o=d("xhrSync"),r=d("xhrListener"),s=d("xhrScheduled"),a=d("xhrURL"),l=d("xhrErrorBeforeScheduled")}),Zone.__load_patch("geolocation",t=>{t.navigator&&t.navigator.geolocation&&function(t,n){const o=t.constructor.name;for(let r=0;r<n.length;r++){const s=n[r],a=t[s];if(a){if(!v(e(t,s)))continue;t[s]=(e=>{const t=function(){return e.apply(this,y(arguments,o+"."+s))};return I(t,e),t})(a)}}}(t.navigator.geolocation,["getCurrentPosition","watchPosition"])}),Zone.__load_patch("PromiseRejectionEvent",(e,t)=>{function n(t){return function(n){U(e,t).forEach(o=>{const r=e.PromiseRejectionEvent;if(r){const e=new r(t,{promise:n.promise,reason:n.rejection});o.invoke(e)}})}}e.PromiseRejectionEvent&&(t[d("unhandledPromiseRejectionHandler")]=n("unhandledrejection"),t[d("rejectionHandledHandler")]=n("rejectionhandled"))})},1796:(e,t,n)=>{"use strict";n(2167)}},e=>{"use strict";e(e.s=1796)}]);