using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using BdoPartner.Plans.Web.Common;
using BdoPartner.Plans.Web.API.Helper;

namespace BdoPartner.Plans.Web.API
{
    public class Startup : BaseStartup
    {

        public Startup(IWebHostEnvironment env, IConfiguration configuration) : base(env, configuration)
        {
        }


        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            this.BaseConfigureServices(services);
            //
            // Dependency Injection register DataAccess Layer DbContexts, UnitOfWorks in Web application's Startup.cs.
            //
            services.RegisterDataAccessLayer(this.ConfigSettings);

            //
            // Dependency Injection register Business Layer Services in Web Application's Startup.cs. 
            //
            services.RegisterBusinessLayer(this.ConfigSettings);

            //
            // Register AutoMapper.
            // http://docs.automapper.org/en/stable/Dependency-injection.html
            //
            services.RegisterAutoMapperProfiles();
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            this.BaseConfigure(app, env);
        }
    }
}
