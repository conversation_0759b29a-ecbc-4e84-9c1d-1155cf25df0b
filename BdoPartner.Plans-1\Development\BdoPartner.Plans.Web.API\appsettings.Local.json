{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "DevopsIntegrationAPIKey": "fea4eb99-5c65-4f19-aa5e-f291753b87f2",
  "ConnectionStrings": {
    //
    // Connection for sql server database. Local machine development settings.
    //
    "DatabaseConnection": "Server=localhost;Database=BdoPartner.Plans.Database;Integrated Security=True;Trusted_Connection=True;"
    //"DatabaseConnection": "Server=tcp:bdo-ca1-partner-sql-dev-01.database.windows.net,1433;Initial Catalog=PartnerPlans-DEV;Persist Security Info=False;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;Column Encryption Setting=enabled;Authentication=Active Directory Managed Identity;",
    //"DatabaseConnection": "Server=tcp:bdo-ca1-partner-sql-tst-01.database.windows.net,1433;Initial Catalog=PartnerPlans-TST;Persist Security Info=False;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;Column Encryption Setting=enabled;Authentication=Active Directory Managed Identity;",

  },

  "IdentityServer": {
    //
    // Current Identity Server end point's domain.
    // Note: This is custom setting.
    //
    "IAMUri": "https://localhost:5000",
    //
    // Integer value of Minutes. Default value = 5 minutes. Note: This is important to solve problem in client's infinit looping in login which caused by client's local machine datetime setting is not correct.
    // Set in Web API resource portal.
    //
    "IdentityServerJwtValidationClockSkew": 5,

    "ExternalIdentityProviders": [
      {
        //
        // Auzre AD authentication setup. Note: We are introduced Azure AD as an external identity provider.
        // The current Identity Server 4 here works as a federal gateway.
        //
        // BDO Innovation Azure AD setup. Point to Azure AD Registered App called "BDO Single Sign On Application POC".
        "ProviderName": "BDO-AAD",
        "Description": "Sign-in with BDO Canada LLP IT Innovation Azure AD",
        "AzureADAuthority": "https://login.microsoftonline.com/bdocanada.onmicrosoft.com",
        //"AzureADClientId": "************************************",
        //"AzureADTenant": "ee7a07de-4778-4cfe-8e91-8d9038ba72ec", //"bdoinnovation.onmicrosoft.com",
        //
        // Azure AD registered application's preset client secret.
        // Check this value under "Certificates & secrets" section under "First On Site Identity Server" application in Azure AD.
        //
        //"AzureADClientSecret": "*************************************",
        // Work for call Azure Graph APIs.
        "AzureADClientId": "************************************",
        "AzureADTenant": "ee7a07de-4778-4cfe-8e91-8d9038ba72ec",
        "AzureADClientSecret": "*************************************",

        "AzureADGraphVersion": "api-version=1.6",
        "CallbackPath": "/signin-oidc-bdo-itinv-aad",
        "SignedOutCallbackPath": "/signout-callback-oidc",
        "RemoteSignOutPath": "/signout-oidc-bdo-itinv-aad"
      }
    ]
  },
  "SENDGRID_API_KEY": "*********************************************************************",
  //"BDOAPICertificateName": "bdoca-gwy-uat-api-partner-bdoca",
  //"DocuSign": {
  //  "ClientId": "************************************",
  //  "ClientSecret": "65b85b76-8007-4f02-a692-d020f353ef7c",
  //  "APIAccountId": "************************************",
  //  "AuthorizationEndpoint": "https://account-d.docusign.com/oauth/auth",
  //  "TokenEndpoint": "https://account-d.docusign.com/oauth/token",
  //  "UserInformationEndpoint": "https://account-d.docusign.com/oauth/userinfo",
  //  "AppUrl": "http://localhost:3000/pra",
  //  "RoomsApiEndpoint": "https://demo.rooms.docusign.com",
  //  "AdminApiEndpoint": "https://api-d.docusign.net/management",
  //  "SignerEmail": "<EMAIL>",
  //  "SignerName": "Prithu Ahmed",
  //  "GatewayAccountId": "{GATEWAY_ACCOUNT_ID}",
  //  "GatewayName": "TestApplication",
  //  "GatewayDisplayName": "TestApplication"
  //},
  //"DocuSignJWT": {
  //  "ClientId": "************************************",
  //  "ImpersonatedUserId": "************************************",
  //  "AuthServer": "account-d.docusign.com",
  //  "PrivateKeyFile": "private.key"
  //},
  "App": {
    //
    // local database connection timeout setting.
    // It is integer value of seconds.
    //
    "DatabaseCommandTimeout": 3600,
    //
    // Corporate with Identity Setup Resource Scope definition 
    // (check IdentityConfig.cs in IdentityServer project). 
    // It is current portal's definition in Identity Server Resource Scopes.
    //
    "ApplicationCode": "resource-api",

    //
    // Company domains. work for allow accessing from company domains.
    // Note: All urls has to be lower case.
    // Note: field "Domain" in table [ClientDomain], string value also needs to be lower case.
    // Splitor ","
    "AllowedDomains": "https://localhost:4200,http://localhost:4200,https://localhost:5000,http://localhost:3000",

    //
    // If system is required to host Single Page Application in sub directory under web api domain,
    // "SPAConfig" section is required. 
    // If single page application is hosted in seperated independent domin, "
    // SPAConfig" section need to be removed or commented out.
    //
    "SPAConfig": {
      //
      // Work for scenario that Angular/React single page applications 
      // need to be hosted under web api portal and shared same domain.
      //
      // If value = true, Angular/React build files will be kept in Azure Blob Storage.
      // If value = false, Angular/React build files will be kept in Web API local static folders.
      // Coporate with SPAPaths, SPAHostingAzureBlobStorageAccessKey settings.
      //
      "SPAHostingInAzureBlobStorage": false,

      //
      //
      // Work for scenario that Angular/React single page applications 
      // need to be hosted under web api portal and shared same domain.
      //
      // **********************************************************************************************
      //
      // When "SAPHostingInAzureBlobStorage" = false,
      //
      // Below is sub folder paths under web api hosting folder which are used to keep SPA sites' associated build files, 
      // and their alter url for routing.
      // Note: System supports multiple Angular or React hosting inside same domain with different sub paths.
      // Data format: "[SPA1 alter url]|[SPA1 files folder path],[SPA2 alter url]|[SPA2 files folder path],..."
      //
      // Note: file alter url has to have "/" at the beginning, 
      ///and file's file path has to have ".\\" at the beginning, 
      // if the Angular built files are kept in the local static folders under the web api portal.
      // 
      // Here Demo hosting two Single Page Applications (Angular and React) inside the Web API portal.
      //
      // **********************************************************************************************
      //
      // When "SPHostingInAzureBlobStorage" = true
      // Data format: "[SPA1 alter url]|[SPA1 files Azure Blob Storage container name],[SPA2 alter url]|[SPA2 files Azure Blob Storage container name],..."
      // Note: In Azure Blob Storage service, one container only keep one single page application build and the SPA build files need to be saved in root folder ("/") of the container.
      //
      "SPAPaths": "/pps|..\\ReactClientApp\\build", // File system pathes setup.
      //"SPAPaths": "/angular|angulardevnocors,/react|reactdevnocors", // Azure Blog Storage Service pathes setup.

      //
      // There is another option for single page application build files hosting.
      // We can save the Angular/Rect build files in Azure Blob Storage and access files there.
      // "SPAHostingAzureBlobStorageConnection" is connection string for Azure blob storage access.
      // Note: This setting works with SPAHostingInAzureBlobStorage = true.
      //
      "SPAHostingAzureBlobStorageConnection": "DefaultEndpointsProtocol=https;AccountName=solutiontemplatestorage;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net"
    },

    //
    // Below section will be loaded into React Application as its local config through web api endpoing: api/settings/GetIDSSettings
    //
    // Work for local machine react app debugging with command: "npm run build".
    //"Environment": {
    //  "ClientId": "pps_dev_nocors",
    //  /** Current React App hosting domain. */
    //  "AppDomain": "https://localhost:5001",
    //  "IamDomain": "https://localhost:5000",
    //  "ApiDomain": "https://localhost:5001",
    //  "IamScope": "openid profile resource-api admin-api custom_profile",
    //  "BasePath": "/pps"
    //},
    // Work for local machine react app debugging with command: "npm start".
    "Environment": {
      "ClientId": "pps_dev_cors",
      /** Current React App hosting domain. */
      "AppDomain": "http://localhost:3000",
      "IamDomain": "https://localhost:5000",
      "ApiDomain": "https://localhost:5001",
      "IamScope": "openid profile resource-api admin-api custom_profile",
      "BasePath": "/pps",
      "ShowNavBar": true
    },
    "TestEmail": {
      "Enable": true,
      "Email": ""
    },
    //
    // Azure Key Vaults access matters config settings section.
    // Corporate with Azure SQL Server always encrypted data access.
    //
    //
    "AzureKeyVaultConfig": {
      //
      // Get TenantId from Azure AD registed Application (App registration) Dashboard 
      // item called "Directory (tenant) ID". 
      // For example, the TenantId of "LocalDebugSolutionTemplateWebAPI" in "BDO IT Solutions Innovation" Azure AD.
      // Work for create token credential for Azure Key Vault access.
      //
      "TenantId": "ee7a07de-4778-4cfe-8e91-8d9038ba72ec",
      //
      // It is Azure Key Vault service uri. Get it from Azure Key Vault dashboard.
      //
      "AzureKeyVaultUri": "https://bdo-ca1-partner-kv-dev.vault.azure.net/"
    },
    //
    //
    // DocuSign settings
    //
    //
    "DocuSignSettings": {
      //"Username": "<EMAIL>",
      //"Password": "prith11",
      "Username": "************************************",
*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
      "IntegratorKey": "987d5ded-db3a-42ca-835f-52f3e5bda546",
      "UseKeyVault": false,
      "EnvironmentUrl": "https://demo.docusign.net/restapi",
      //"Username": "<EMAIL>",
      //"Password": "SD=e1S_(AMWW]Mso#Z",
      //"IntegratorKey": "89a36975-ee90-45ef-8109-49b79f36b16b",
      "UseOverrideEmail": false, // true if override emails addresses by the following emails for development
      // if use override email (for debug/testing), please provider 3 emails separated by ";"
      "OverrideEmails": "<EMAIL>;<EMAIL>;<EMAIL>"
    }
  }


  //
  // If "AppConfig" section is enabled, it means current application's config settings should be got from Azure App Configuration service.
  // If no "AppConfig" section or the "AppConfig" section got comment out, it means current application gets settings from appsettings.json. Note: It mostly is for Development enviornment.
  //
  // When "AppConfig" section is enabled, developer needs to comment out other sections in current appsettings.[environment].json file.
  // 
  // If "IsConnectedWithConnectionString" = true, system will try to access Azure App Configuration service with "connection string" way. 
  // (Got "connection string" from App Configuraiton-> "Access Keys" section.) 
  // It is mostly working for local development environment to get settings from Azure App Configuraiton service, instead of getting settings from appsettings.json files.
  // When debug web API with visual studio 2019 in local machine, developer need to set "IsConnectedWithConnectionString" = true.
  // And the associated Azure App Configuration connection string is kept in Web API portal's "Manage User Secrets" option, item called "ConnectionStrings:AppConfig".
  //
  // If "IsConnectedWithConnectionString" = false, system will try to access Azure App Configuration service with "ManagedIdentityCredential".
  // Note: This option is only works for web portal which already deployed in Azure App Services. It does not work for local development environment.
  // 
  // Note: There is scenario that same setting existing in appsettings.[current environment].json and "AppConfig" section also is enabled (which means Azure App Configuration access enabled). 
  // If same setting existing in appsettings.[current environment].json and Azure App Configuration, 
  // The settings in Azure App Configuration got precedence.
  //
  //"AppConfig": {
  //  "IsConnectedWithConnectionString": true,
  //  //
  //  // It is Azure App Configuration Service endpoint. Note: It only works when "IsConnectedWithConnectionString" = false. and it corporate with "ManagedIdentityCredential" call.
  //  // It only works for portal deployed in Azure App Services.
  //  //
  //  "Endpoint": "https://solution-template-appsettings.azconfig.io"
  //}
}


