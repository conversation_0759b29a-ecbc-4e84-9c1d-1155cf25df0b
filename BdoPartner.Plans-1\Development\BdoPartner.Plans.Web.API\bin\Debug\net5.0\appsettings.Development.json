{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "AllowedHosts": "*",
  "ConnectionStrings": {
    "DatabaseConnection": "Server=tcp:bdo-ca1-partner-sql-dev-01.database.windows.net,1433;Initial Catalog=PartnerPlans-DEV;Persist Security Info=False;User ID=appDev;Password=*********;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;Column Encryption Setting=enabled;",
  },
  "DevopsIntegrationAPIKey": "fea4eb99-5c65-4f19-aa5e-f291753b87f2",
  "BDOAPICertificateName": "bdoca-gwy-uat-api-partner-bdoca",
  "IdentityServer": {
    "IAMUri": "https://bdo-ca1-pid-web-dev-01.azurewebsites.net",
    "IdentityServerJwtValidationClockSkew": 5,
    "ExternalIdentityProviders": [
      {
        "ProviderName": "BDO-AAD",
        "Description": "Sign-in with BDO Canada LLP Azure AD",
        "AzureADAuthority": "https://login.microsoftonline.com/bdocanada.onmicrosoft.com",
        //"AzureADClientId": "728a952f-bb3b-4141-9b74-d0d07bcc31af",
        "AzureADTenant": "ee7a07de-4778-4cfe-8e91-8d9038ba72ec",
        //"AzureADClientSecret": "*************************************",
        "AzureADGraphVersion": "api-version=1.6",
        "CallbackPath": "/signin-oidc-bdo-aad",
        "SignedOutCallbackPath": "/signout-callback-oidc",
        "RemoteSignOutPath": "/signout-oidc-bdo-aad"
      }
    ]
  },
  "App": {
    "DatabaseCommandTimeout": 3600,
    "ApplicationCode": "resource-api",
    "AllowedDomains": "https://bdo-ca1-pid-web-dev-01.azurewebsites.net",
    "SPAConfig": {
      "SPAHostingInAzureBlobStorage": true,
      "SPAPaths": "/pps|partnerplansnocors"
      //"SPAHostingAzureBlobStorageConnection": "DefaultEndpointsProtocol=https;AccountName=bdoca1partnerbuilddevst0;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net"
    },
    "AzureKeyVaultConfig": {
      "TenantId": "ee7a07de-4778-4cfe-8e91-8d9038ba72ec",
      "AzureKeyVaultUri": "https://bdo-ca1-partner-kv-dev.vault.azure.net/"
    },
    "Environment": {
      // Corporate withe appsetting.development.json settings in BDO.Internal.Identity server project.
      // Work for deploy partner plans to Azure Dev environment.
      "ClientId": "pps_prod_nocors",
      // /** Current React App hosting domain. */
      "AppDomain": "https://bdo-ca1-pps-web-dev-01.azurewebsites.net",
      "IamDomain": "https://bdo-ca1-pid-web-dev-01.azurewebsites.net",
      "ApiDomain": "https://bdo-ca1-pps-web-dev-01.azurewebsites.net",
      "IamScope": "openid profile resource-api admin-api custom_profile",
      "BasePath": "/pps",
      "ShowNavBar": true
    },
    "TestEmail": {
      "Enable": true,
      "Email": ""
    }
    ////
    ////
    //// DocuSign settings
    ////
    ////
    //"DocuSignSettings": {
    //  "Username": "DocuSignUserName",
    //  "Password": "DocuSignPassword",
    //  "IntegratorKey": "DocuSignIntegratorKey",
    //  "EnvironmentUrl": "https://demo.docusign.net/restapi",
    //  "UseKeyVault": true,
    //  "UseOverrideEmail": false, // true if override emails addresses by the following emails for development
    //  // if use override email (for debug/testing), please provider 3 emails separated by ";"
    //  "OverrideEmails": ""
    //}
  }

  //
  // If "AppConfig" section is enabled, it means current application's config settings should be got from Azure App Configuration service.
  // If no "AppConfig" section or the "AppConfig" section got comment out, it means current application gets settings from appsettings.json. Note: It mostly is for Development enviornment.
  //
  // When "AppConfig" section is enabled, 
  // 
  // If "isConnectedWithConnectionString" = true, system will try to access Azure App Configuration service with "connection string" way. 
  // (Got "connection string" from App Configuraiton-> "Access Keys" section.) 
  // It is mostly working for local development environment to get settings from Azure App Configuraiton service, instead of getting settings from appsettings.json files.
  //
  // If "isConnectedWithConnectionString" = false, system will try to access Azure App Configuration service with "ManagedIdentityCredential".
  // Note: This option is only works for web portal which already deployed in Azure App Services. It does not work for local development environment.
  //
  //"AppConfig": {
  //  "IsConnectedWithConnectionString": false,
  //  //
  //  // It is Azure App Configuration Service endpoint. Note: It only works when "IsConnectedWithConnectionString" = false. and it corporate with "ManagedIdentityCredential" call.
  //  // It only works for portal deployed in Azure App Services.
  //  //
  //  "Endpoint": "https://solution-template-appsettings.azconfig.io"
  //}
}

