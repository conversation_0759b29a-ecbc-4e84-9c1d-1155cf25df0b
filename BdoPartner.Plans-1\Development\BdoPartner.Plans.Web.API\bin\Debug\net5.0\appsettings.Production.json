{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "AllowedHosts": "*",
  "ConnectionStrings": {
    "DatabaseConnection": "data source= bdo-ca1-partner-sql-prod-01.database.windows.net; initial catalog =PartnerPlans; Column Encryption Setting=enabled;pooling=true;multipleactiveresultsets=False;connect timeout=1200;Authentication=Active Directory Managed Identity",
  },
  "DevopsIntegrationAPIKey": "9747CE85-CAAE-EC11-997E-0050F249E149",
  "BDOAPICertificateName": "bdoca-gwy-prod-api-partner-bdoca",
  "IdentityServer": {
    "IAMUri": "https://partner-auth.app.bdo.ca",
    "IdentityServerJwtValidationClockSkew": 5,
    "ExternalIdentityProviders": [
      {
        "ProviderName": "BDO-AAD",
        "Description": "Sign-in with BDO Canada LLP Azure AD",
        "AzureADAuthority": "https://login.microsoftonline.com/bdocanada.onmicrosoft.com",
        //"AzureADClientId": "cc576148-04a2-4762-ac5f-5aaeb08d9a3a",
        "AzureADTenant": "ee7a07de-4778-4cfe-8e91-8d9038ba72ec",
        //"AzureADClientSecret": "*************************************",
        "AzureADGraphVersion": "api-version=1.6",
        "CallbackPath": "/signin-oidc-bdo-aad",
        "SignedOutCallbackPath": "/signout-callback-oidc",
        "RemoteSignOutPath": "/signout-oidc-bdo-aad"
      }
    ]
  },
  "App": {
    "DatabaseCommandTimeout": 3600,
    "ApplicationCode": "resource-api",
    "AllowedDomains": "https://partner-auth.app.bdo.ca",
    "SPAConfig": {
      "SPAHostingInAzureBlobStorage": true,
      "SPAPaths": "/pps|partnerplansnocors"
      //"SPAHostingAzureBlobStorageConnection": "DefaultEndpointsProtocol=https;AccountName=bdoca1partnerbuildprodst0;AccountKey=n87E6UinlCcmaIE5rmmu9lQ7v19z8xbQtNYU0ZffAKhzatMIXOrYYJU1lMofkX7V9TSHpNHeTxTiQ2rADl8INQ==;EndpointSuffix=core.windows.net"
    },
    "AzureKeyVaultConfig": {
      "TenantId": "ee7a07de-4778-4cfe-8e91-8d9038ba72ec",
      "AzureKeyVaultUri": "https://bdo-ca1-partner-kv-prod.vault.azure.net/"
    },
    "Environment": {
      "ClientId": "pps_prod_nocors",
      // /** Current React App hosting domain. */
      "AppDomain": "https://pps.app.bdo.ca",
      "IamDomain": "https://partner-auth.app.bdo.ca",
      "ApiDomain": "https://pps.app.bdo.ca",
      "IamScope": "openid profile resource-api admin-api custom_profile",
      "BasePath": "/pps",
      "ShowNavBar": false
    },
    "TestEmail": {
      "Enable": false,
      "Email": ""
    },
    //
    //
    // DocuSign settings
    //
    //
    "DocuSignSettings": {
      "Username": "DocuSignUserGuid",
      "Password": "DocuSignRSAKey",
      "IntegratorKey": "DocuSignIntegrationKey",
      "EnvironmentUrl": "https://ca.docusign.net/restapi",
      "UseKeyVault": true,
      "UseOverrideEmail": false, // true if override emails addresses by the following emails for development
      // if use override email (for debug/testing), please provider 3 emails separated by ";"
      "OverrideEmails": ""
    }
  }

  //
  // If "AppConfig" section is enabled, it means current application's config settings should be got from Azure App Configuration service.
  // If no "AppConfig" section or the "AppConfig" section got comment out, it means current application gets settings from appsettings.json. Note: It mostly is for Development enviornment.
  //
  // When "AppConfig" section is enabled, 
  // 
  // If "isConnectedWithConnectionString" = true, system will try to access Azure App Configuration service with "connection string" way. 
  // (Got "connection string" from App Configuraiton-> "Access Keys" section.) 
  // It is mostly working for local development environment to get settings from Azure App Configuraiton service, instead of getting settings from appsettings.json files.
  //
  // If "isConnectedWithConnectionString" = false, system will try to access Azure App Configuration service with "ManagedIdentityCredential".
  // Note: This option is only works for web portal which already deployed in Azure App Services. It does not work for local development environment.
  //
  //"AppConfig": {
  //  "IsConnectedWithConnectionString": false,
  //  //
  //  // It is Azure App Configuration Service endpoint. Note: It only works when "IsConnectedWithConnectionString" = false. and it corporate with "ManagedIdentityCredential" call.
  //  // It only works for portal deployed in Azure App Services.
  //  //
  //  "Endpoint": "https://solution-template-appsettings.azconfig.io"
  //}
}

