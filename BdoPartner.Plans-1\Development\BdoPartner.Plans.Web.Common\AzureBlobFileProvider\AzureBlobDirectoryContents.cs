﻿using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using Microsoft.Extensions.FileProviders;

namespace BdoPartner.Plans.Web.Common
{
    /// <summary>
    ///  Custom Implementation for IDirectoryContents. 
    /// </summary>
    public class AzureBlobDirectoryContents : IDirectoryContents
    {
        private readonly List<BlobHierarchyItem> _blobs = new List<BlobHierarchyItem>();
        private readonly BlobContainerClient _container = null;
      
        public bool Exists { get; set; }

        public AzureBlobDirectoryContents(List<BlobHierarchyItem> blobs, BlobContainerClient container)
        {
            this._blobs = blobs;
            this._container = container;
           
            if (blobs != null && blobs.Count>0)
            {
                this.Exists = true;
            }
            else {
                this.Exists = false;
            }
        }

        public IEnumerator<IFileInfo> GetEnumerator()
        {            
            return _blobs.Select(blob => new AzureBlobFileInfo(blob, this._container)).GetEnumerator();
        }

        IEnumerator IEnumerable.GetEnumerator()
        {
            return GetEnumerator();
        }
    }
}

