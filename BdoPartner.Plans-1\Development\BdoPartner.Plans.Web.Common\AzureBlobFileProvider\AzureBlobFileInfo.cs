﻿using System;
using System.IO;
using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using Microsoft.Extensions.FileProviders;

namespace BdoPartner.Plans.Web.Common
{
    public class AzureBlobFileInfo : IFileInfo
    {
        private readonly BlobItem _blob = null;
        private readonly BlobContainerClient _container = null;
     
        public AzureBlobFileInfo(BlobHierarchyItem blob, BlobContainerClient container)
        {
            if (blob != null) {

                this._container = container;
                                
               if  (blob.IsPrefix)
                {
                    //
                    // Serve as "Directory".
                    //
                    this.Exists = true;
                    this.IsDirectory = true;
                    this.Name = blob.Prefix.TrimEnd('/');
                }
                else if (blob.IsBlob) {
                    //
                    // Serve as "File".
                    //
                    this.IsDirectory = false;
                    if (blob.Blob != null)
                    {
                        this._blob = blob.Blob;
                        this.Name = blob.Blob.Name; //Note: Name has to be same as Blob name, otherwise, method "CreateReadStream" cannot be called. 
                        this.Length = (long)blob.Blob.Properties.ContentLength;
                        this.LastModified = blob.Blob.Properties.LastModified?? DateTimeOffset.MinValue;
                        this.Exists = true;
                    }
                    else {
                        this.Exists = false;
                        this.Length = -1;
                        //
                        //Note: TODO. PhysicalPath should maybe also be null for blobs that do exist but that would be a potentially breaking change.
                        //
                        this.PhysicalPath = null;
                     }
                }
            }
        }

        /// <summary>
        ///  IFileInfo build in property.
        /// </summary>
        /// <returns></returns>
        public Stream CreateReadStream()
        {
            var stream = new MemoryStream();

            if (this._blob != null)
            {
                var client = this._container.GetBlobClient(this._blob.Name);
                client.DownloadToAsync(stream).Wait();
                stream.Position = 0;
            }
            return stream;
        }

        public bool Exists { get; }
        public long Length { get; }
        public string PhysicalPath { get; }
        public string Name { get; }
        public DateTimeOffset LastModified { get; }
        public bool IsDirectory { get; }
    }
}

