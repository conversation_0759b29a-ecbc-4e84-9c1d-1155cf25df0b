﻿using System;
using Microsoft.Extensions.FileProviders;
using Microsoft.Extensions.Primitives;
using System.Linq;

namespace BdoPartner.Plans.Web.Common
{
    /// <summary>
    /// AzureBlobFileProvider supported latest version "Azure.Storage.Blobs" library.
    /// 
    /// Original version source codes refered to: 
    /// https://github.com/filipw/Strathweb.AspNetCore.AzureBlobFileProvider
    /// </summary>
    public class AzureBlobFileProvider : IFileProvider
    {
        private readonly IBlobContainerFactory _blobContainerFactory;
        /// <summary>
        ///  It is container name. Get it from appsettings.json section "SPAConfig" item called "SPAPaths".
        /// </summary>
        private readonly string _containerName; 

        /// <summary>
        /// 
        /// </summary>
        /// <param name="blobContainerFactory"></param>
        /// <param name="containerName">
        /// Name of container in current connected Azure Blob Storage service. 
        /// data format example: "react" or "angular". 
        /// </param>
        public AzureBlobFileProvider(IBlobContainerFactory blobContainerFactory, string containerName)
        {
            this._blobContainerFactory = blobContainerFactory;
            this._containerName = containerName;
        }

        /// <summary>
        ///  
        /// </summary>
        /// <param name="azureBlobOptions"></param>
        /// <param name="containerName">
        /// Name of container in current connected Azure Blob Storage service. 
        /// data format example: "react" or "angular". 
        /// </param>
        public AzureBlobFileProvider(AzureBlobOptions azureBlobOptions, string containerName)
        {
            this._blobContainerFactory = new DefaultBlobContainerFactory(azureBlobOptions);
            this._containerName = containerName;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="subpath">A path under the root directory. Leading slashes are ignored.</param>
        /// <returns></returns>
        public IDirectoryContents GetDirectoryContents(string subpath)
        {            
            var container = _blobContainerFactory.GetContainer(this._containerName);
            var blobs = container.GetBlobsByHierarchy(prefix: _blobContainerFactory.TransformPath(subpath)).ToList();
            return new AzureBlobDirectoryContents(blobs, container);
        }

        /// <summary>
        ///   
        /// </summary>
        /// <param name="filepath"></param>
        /// <returns></returns>
        public IFileInfo GetFileInfo(string filepath)
        {
            var container = _blobContainerFactory.GetContainer(this._containerName);
            var blob = container.GetBlobsByHierarchy(delimiter: "/", prefix: _blobContainerFactory.TransformPath(filepath)).FirstOrDefault();
            return new AzureBlobFileInfo(blob, container);
        }

        public IChangeToken Watch(string filter) => throw new NotImplementedException();
    }
}

