﻿using System;

namespace BdoPartner.Plans.Web.Common
{
    public class AzureBlobOptions
    {
        /// <summary>
        ///  It is Azure Blob Storage Service endpoint uri. Get it from "Settings -> Endpoints" section.
        ///  Such as: https://solutiontemplatestorage.blob.core.windows.net/
        /// </summary>
        public Uri BaseUri { get; set; }

        /// <summary>
        /// Corporate with Web API portal which has hosted in Azure App Service.
        /// It uses "DefaultAzureCredential" to authorize if the Web Portal be able to access files in Azure Blob Storage service or not.
        /// Note: To enable the Azure Storage access for the specified portal hosting in App Service, developer needs to go to Azure Storage Access Control (IAM) to enable access for spcified App Sevice Identity.
        /// https://docs.microsoft.com/en-us/dotnet/api/azure.identity.defaultazurecredential?view=azure-dotnet
        /// 
        /// Data format: use portal name, for example: "bdo-sso-resource" 
        /// or use portal object Id, for example: "e4b0d54a-c308-4dce-ad61-cf18a000e5ac".
        /// 
        /// TODO. not applied yet. need to be tested.
        /// </summary>
        public string ClientId { get; set; }
                
        /// <summary>
        ///  Azure Blob Storage connection string. Get it from "Security + networking -> Access Keys" section.
        /// </summary>
        public string ConnectionString { get; set; }
    }
}