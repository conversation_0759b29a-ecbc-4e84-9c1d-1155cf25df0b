﻿using System;
using Azure.Identity;
using Azure.Storage.Blobs;

namespace BdoPartner.Plans.Web.Common
{
    public class DefaultBlobContainerFactory : IBlobContainerFactory
    {
        private readonly BlobServiceClient _blobServiceClient;

        public DefaultBlobContainerFactory(AzureBlobOptions azureBlobOptions)
        {            
            if (!string.IsNullOrEmpty(azureBlobOptions.ConnectionString))
            {
                //
                // Access Azure Blob Storage service with connection string.
                // Work for development or production environment.
                //
                this._blobServiceClient = new BlobServiceClient(azureBlobOptions.ConnectionString);
            }
            else if (azureBlobOptions.BaseUri!=null && !string.IsNullOrEmpty(azureBlobOptions.ClientId))
            {
                //
                // Only work for web api portal which already hosted in Azure App Service. (Associated to Identity and Access control setup)
                // Reference: https://docs.microsoft.com/en-us/dotnet/api/azure.identity.defaultazurecredential?view=azure-dotnet
                //
                var credential = new DefaultAzureCredential(new DefaultAzureCredentialOptions { ManagedIdentityClientId = azureBlobOptions.ClientId });

                this._blobServiceClient = new BlobServiceClient(azureBlobOptions.BaseUri, credential);
            }
            else
            {
                throw new ArgumentException("One of the following must be set: 'ConnectionString' or 'BaseUri'+'ClientId'!");
            }
        }

        /// <summary>
        ///  Return a generated BlobContainerClient instance for specified container name.
        /// </summary>
        /// <param name="containerName">name of container which inside current connected Azure Blob Storage service.</param>
        /// <returns></returns>
        public BlobContainerClient GetContainer(string containerName)
        {
            return this._blobServiceClient.GetBlobContainerClient(containerName);
        }

        public string TransformPath(string subpath)
        {
            return subpath.TrimStart('/').TrimEnd('/');
        }
    }
}
