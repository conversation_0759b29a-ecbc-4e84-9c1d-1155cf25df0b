﻿using BdoPartner.Plans.Common;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;
using IdentityModel;

namespace BdoPartner.Plans.Web.Common
{
    public enum AuthorizeOption
    {
        /// <summary>
        ///  Require user login only. No further role checking
        /// </summary>
        Login,
        
        /// <summary>
        ///  Need user login system and also need further security role checking.
        ///  
        ///  Roles based on Enumerations.Role definitions.
        /// </summary>
        Role,

        /// <summary>
        ///  Check based on Permission.
        ///  AccessRight based on Enumerations.Permission definitions.
        /// </summary>
        Permission
    }

    /// <summary>
    ///  Check current logon user's claims such as "roles" "permissions" which passing from Identity Server 4.
    ///  Check each controller method with preset permission.
    /// </summary>
    public class BDOAuthorizeAttribute : TypeFilterAttribute
    {      

        /// <summary>
        ///  roles checking. 
        /// </summary>
        /// <param name="roles"><PERSON><PERSON><PERSON> has to assign at least one Role</param>
        public BDOAuthorizeAttribute(params Enumerations.Role[] roles)
        : base(typeof(CustomAuthorizeRoleActionFilter))
        {
            this.Arguments = new object[] { roles };
        }

        /// <summary>
        ///  Check with permissions. 
        /// </summary>
        /// <param name="permissions"></param>
        public BDOAuthorizeAttribute(params Enumerations.Permission[] permissions)
        : base(typeof(CustomAuthorizePermissionActionFilter))
        {
            this.Arguments = new object[] { permissions };
        }

    }


    /// <summary>
    ///  Reference: https://stackoverflow.com/questions/31464359/how-do-you-create-a-custom-authorizeattribute-in-asp-net-core
    /// </summary>
    public class CustomAuthorizeGroupsActionFilter : BaseAuthorizeActionFilter, IAsyncActionFilter
    {
        public async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
        {
            bool isAuthorized = this.Authorize(context);

            if (!isAuthorized)
            {
                context.Result = new CustomUnauthorizedResult("Unauthorized access is denied by system.");
            }
            else
            {
                await next();
            }
        }
    }

    public class CustomAuthorizeRoleActionFilter : BaseAuthorizeActionFilter, IAsyncActionFilter
    {        
        public CustomAuthorizeRoleActionFilter(params Enumerations.Role[] roles)
        {
            _roles = roles;
            _authorizeOption = AuthorizeOption.Role;
        }

        public async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
        {
            bool isAuthorized = this.Authorize(context);

            if (!isAuthorized)
            {
                context.Result = new CustomUnauthorizedResult("Unauthorized access is denied by system.");
            }
            else
            {
                await next();
            }
        }       
    }

    public class CustomAuthorizePermissionActionFilter : BaseAuthorizeActionFilter, IAsyncActionFilter
    {
        public CustomAuthorizePermissionActionFilter(params Enumerations.Permission[] permissions)
        {
            _permissions = permissions;
            _authorizeOption = AuthorizeOption.Permission;
        }

        public async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
        {
            bool isAuthorized = this.Authorize(context);

            if (!isAuthorized)
            {
                context.Result = new CustomUnauthorizedResult("Unauthorized access is denied by system.");
            }
            else
            {
                await next();
            }
        }
    }

    public class BaseAuthorizeActionFilter {
               
        /// <summary>
        ///  They are required Roles.
        /// </summary>
        protected Enumerations.Role[] _roles;

        /// <summary>
        ///  They are required permissions. Work for external portal. 
        ///  Replace old method called "HasPermissionTo" in old Transfer project.
        /// </summary>
        protected Enumerations.Permission[] _permissions;

        /// <summary>
        ///  Default is Login option. Means only required user login. No further authorization need.
        /// </summary>
        protected AuthorizeOption _authorizeOption = AuthorizeOption.Login;


        /// <summary>
        ///  Curreng logon user's associated claim identity.
        ///  Note: If no authorized logon user, return null.
        /// </summary>
        protected ClaimsIdentity CurrentAppContext(ActionExecutingContext context)
        {
            if (context.HttpContext.User != null
                && context.HttpContext.User.Identity != null
                && context.HttpContext.User.Identity.IsAuthenticated)
                return context.HttpContext.User.Identity as ClaimsIdentity;
            else
                return null;
        }

        protected bool Authorize(ActionExecutingContext context)
        {
            bool result = false;

            switch (_authorizeOption)
            {
                case AuthorizeOption.Login: // Client is required to login system first before access page.
                    if (this.CurrentAppContext(context) != null && this.CurrentAppContext(context).IsAuthenticated)
                    {
                        result = true;
                    }
                    break;              
                case AuthorizeOption.Role: // After Client login system, check logon user's Roles.       
                    if (this.CurrentAppContext(context) != null && this.CurrentAppContext(context).IsAuthenticated)
                    {
                        // Further checking based on Roles.
                        result = this.LogonUserRolesCheck(context);

                    }
                    break;
                case AuthorizeOption.Permission: // After Client login system, check logon user's Permissions.       
                    if (this.CurrentAppContext(context) != null && this.CurrentAppContext(context).IsAuthenticated)
                    {
                        // Further checking based on Permissions.
                        result = this.LogonUserPermissionsCheck(context);

                    }
                    break;

                default:
                    break;
            }

            return result;
        }
             

        /// <summary>
        /// Check if logon user has specified role.
        /// </summary>
        /// <returns>return true means logon user has associated Roles to access current action.</returns>
        protected bool LogonUserRolesCheck(ActionExecutingContext context)
        {
            bool result = false;

            try
            {
                if (this.CurrentAppContext(context) != null && this.CurrentAppContext(context).IsAuthenticated)
                {
                    if (_roles != null && _roles.Length > 0)
                    {
                        var logonUserRoles = this.CurrentAppContextRoles(context);

                        if (logonUserRoles != null && logonUserRoles.Count > 0)
                        {
                            result = logonUserRoles.Any(r => _roles.Contains(r));
                        }
                        else
                            result = false;
                    }
                    else
                        result = true;
                }
            }
            catch
            {
                result = false;
            }

            return result;
        }



        /// <summary>
        /// Check if logon user has specified permission.
        /// </summary>
        /// <returns>return true means logon user has associated Permissions to access current action.</returns>
        protected bool LogonUserPermissionsCheck(ActionExecutingContext context)
        {
            bool result = false;

            try
            {
                if (this.CurrentAppContext(context) != null && this.CurrentAppContext(context).IsAuthenticated)
                {
                    if (this._permissions != null && this._permissions.Length > 0)
                    {
                        var logonUserPermissions = this.CurrentAppContextPermissions(context);

                        if (logonUserPermissions != null && logonUserPermissions.Count > 0)
                        {
                            result = logonUserPermissions.Any(r => this._permissions.Contains(r));
                        }
                        else
                            result = false;
                    }
                    else
                        result = true;
                }
            }
            catch
            {
                result = false;
            }

            return result;
        }
             
        /// <summary>
        /// Current logon user's associated roles. 
        /// Note: If no logon user or logon user no roles, return an empty collection.
        /// </summary>
        protected List<Enumerations.Role> CurrentAppContextRoles(ActionExecutingContext context)
        {
            if (this.CurrentAppContext(context) != null && this.CurrentAppContext(context).IsAuthenticated)
            {
                var rolesClaim = CurrentAppContext(context).FindFirst(JwtClaimTypes.Role);

                if (rolesClaim != null)
                {
                    string[] rolesArry = rolesClaim.Value.Split(',');

                    if (rolesArry != null && rolesArry.Length > 0)
                        return rolesArry.Select(r => (Enumerations.Role)Enum.Parse(typeof(Enumerations.Role), r)).ToList();
                    else
                        return new List<Enumerations.Role>();
                }
                else
                {
                    return new List<Enumerations.Role>();
                }
            }
            else
            {
                return new List<Enumerations.Role>();
            }
        }


        /// <summary>
        /// Current logon user's associated permissions. 
        /// Note: If no logon user or logon user no permissions, return an empty collection.
        /// </summary>
        protected List<Enumerations.Permission> CurrentAppContextPermissions(ActionExecutingContext context)
        {
            if (this.CurrentAppContext(context) != null && this.CurrentAppContext(context).IsAuthenticated)
            {
                var permissionsClaim = CurrentAppContext(context).FindFirst(IAMClaimTypes.Permissions);

                if (permissionsClaim != null)
                {
                    string[] permissionsArry = permissionsClaim.Value.Split(',');

                    if (permissionsArry != null && permissionsArry.Length > 0)
                        return permissionsArry.Select(r => (Enumerations.Permission)Enum.Parse(typeof(Enumerations.Permission), r)).ToList();
                    else
                        return new List<Enumerations.Permission>();
                }
                else
                {
                    return new List<Enumerations.Permission>();
                }
            }
            else
            {
                return new List<Enumerations.Permission>();
            }
        }
    }

    public class CustomUnauthorizedResult : JsonResult
    {
        public CustomUnauthorizedResult(string message) : base(new CustomError(message))
        {
            StatusCode = StatusCodes.Status401Unauthorized;
        }      
    }

    public class CustomError
    {
        public string Error { get; }

        public CustomError(string message)
        {
            Error = message;
        }
    }
}
