﻿using IdentityServer4.AccessTokenValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.IdentityModel.Logging;
using Microsoft.OpenApi.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.FileProviders;
using System.Threading.Tasks;
using Azure.Storage.Blobs;
using BdoPartner.Plans.Web.Common.Middlewares.RequestCulture;
using BdoPartner.Plans.Common.Config;

namespace BdoPartner.Plans.Web.Common
{
    /// <summary>
    ///  Startup process and settings which can be used by general Web API applications.
    ///  It is Startup.cs file's codes.
    ///  
    ///  Note: As for Identity Server, its startup.cs does not inherit from this base Startup class.
    /// </summary>
    public class BaseStartup
    {
        public IConfiguration Configuration { get; }
        public IWebHostEnvironment CurrentEnvironment;

        /// <summary>
        ///  Wrapper for IConfiguration.
        /// </summary>
        public IConfigSettings ConfigSettings;

        public BaseStartup(IWebHostEnvironment env, IConfiguration configuration)
        {
            this.Configuration = configuration;
            this.CurrentEnvironment = env;
            this.ConfigSettings = new ConfigSettings(configuration);

        }

        // This method gets called by the runtime. Use this method to add services to the container.
        protected void BaseConfigureServices(IServiceCollection services)
        {
            services.AddControllers();
            services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new OpenApiInfo { Title = "BdoPartner.Plans.Web.API", Version = "v1" });
            });

            #region Custom Configuration for the solution.
            //
            // Added for investigate more details errors associated to error happened in Identity Server end point.
            //
            IdentityModelEventSource.ShowPII = true;

            //
            // Reference: https://docs.microsoft.com/en-us/aspnet/core/fundamentals/http-context?view=aspnetcore-2.1
            // Register HttpContextAccessor, thus, business layer service can dependency inject HttpContext.
            // Work for getting userContext which generated in "RequestUserContextMiddleware".
            //
            services.AddHttpContextAccessor();
            //
            // Corporate with Identity Server 4 and Angular OIDC client.
            //
            services.AddAuthentication(IdentityServerAuthenticationDefaults.AuthenticationScheme) // AuthenticationScheme same as "Bearer".
            .AddIdentityServerAuthentication(IdentityServerAuthenticationDefaults.AuthenticationScheme, options =>
            {
                options.Authority = this.ConfigSettings.IdentityServerConfig.IAMUri; // host url of Identity Server.
                options.RequireHttpsMetadata = true;
                //options.Audience = this.ConfigSettings.ApplicationCode;
                //
                // Refernce: https://github.com/IdentityServer/IdentityServer4/issues/2349
                // Work for SignalR Hub Authentication.
                //
                //options.TokenRetriever = CustomTokenRetriever.FromHeaderAndQueryString;
                options.ApiName = this.ConfigSettings.ApplicationCode; // Corporate with Clients AllowedScopes settings in appsettings.json in BdoPartner.Plans.IdentityServer.
                // options.ApiSecret = "BD0lLpSecret@20zO"; //Note: This settings not be applied to implicit flow.   
                //
                //Note: This is important to solve problem in client's infinit looping in login which caused by client's local machine datetime setting is not correct.
                //Reference: https://github.com/IdentityServer/IdentityServer4/issues/962
                //
                options.JwtValidationClockSkew = TimeSpan.FromMinutes(this.ConfigSettings.IdentityServerConfig.IdentityServerJwtValidationClockSkew);

            });

            //
            // Setup CORS policy called "default".
            // Reference: http://docs.identityserver.io/en/release/quickstarts/7_javascript_client.html
            //
            services.AddCors(options =>
            {
                //
                // this defines a CORS policy called "default". Allow access from OneUI Angular SPA.
                //
                options.AddPolicy("CorsPolicy", policy =>
                {
                    policy.WithOrigins(this.ConfigSettings.AllowedDomains)
                        .AllowAnyHeader()
                        .AllowAnyMethod()
                        .AllowCredentials();
                });
            });

            //
            // Dependency inject Configuration. 
            // Reference: https://blogs.technet.microsoft.com/dariuszporowski/tip-of-the-week-how-to-access-configuration-from-controller-in-asp-net-core-2-0/
            // Using built-in support for Dependency Injection, you can inject configuration data to Controller. 
            // Use AddSingleton method to add a singleton service in Startup.cs file.
            //
            services.AddSingleton<IConfiguration>(this.Configuration);
            //
            // Singletone register a custom config settting entity which is warpper of IConfiguration.
            //
            services.AddSingleton<IConfigSettings>(this.ConfigSettings);

            if (!string.IsNullOrEmpty(this.Configuration["APPLICATIONINSIGHTS_CONNECTION_STRING"]))
            {
                //
                // Apply Azure Application Insights for application logging and performance monitor.
                //
                services.AddApplicationInsightsTelemetry(this.Configuration["APPLICATIONINSIGHTS_CONNECTION_STRING"]);
                // example to send trace log to ApplicationInsight
                System.Diagnostics.Trace.WriteLine("Application Insight Enabled");
            }

            //
            // Work for store Single Page Application (Angular/React) build in Azure Blob Storage.
            //
            // Reference: https://github.com/filipw/Strathweb.AspNetCore.AzureBlobFileProvider
            //
            // Note: We already rewrited the "AzureBlobFileProvider" to support latest version Azure.Storage.Blobs library.
            //
            if (this.ConfigSettings.SPAConfig != null && this.ConfigSettings.SPAConfig.SPAHostingInAzureBlobStorage)
            {
                var storageConnection = this.Configuration.GetValue<string>("StorageConnectionString") ?? this.ConfigSettings.SPAConfig.SPAHostingAzureBlobStorageConnection;
                var blobOptions = new AzureBlobOptions
                {
                    ConnectionString = storageConnection,
                };

                var defaultBlobContainerFactory = new DefaultBlobContainerFactory(blobOptions);
                //
                // Work for read index.html file from single page application build in Azure Blob Storage.
                //
                services.AddSingleton(defaultBlobContainerFactory);
            }

            #endregion
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        protected void BaseConfigure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            //
            //Note: Only local development enable the swagger feature.
            //
            if (env.IsEnvironment("Local") || env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
                app.UseSwagger();
                app.UseSwaggerUI(c => c.SwaggerEndpoint("/swagger/v1/swagger.json", "BdoPartner.Plans.Web.API v1"));
            }
            else
            {
                app.UseExceptionHandler("/Home/Error");
                // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
                app.UseHsts();
            }

            app.UseCors("CorsPolicy");
            app.UseHttpsRedirection();
            app.UseRouting();
            app.UseAuthentication();
            app.UseAuthorization();
            //
            // Work for multiple language support in server side.
            // Corporate with header called "currentLanguage" assiged in Single Page Application.
            //
            app.UseRequestCulture();

            app.UseEndpoints(endpoints =>
            {
                //
                // Enable API controllers mapping for Web API endpoints
                //
                endpoints.MapControllers();
                //
                // By default when Resource Web API is up, showing a home page with proper information.
                //
                endpoints.MapControllerRoute(
                    name: "default",
                    pattern: "{controller=Home}/{action=Index}/{id?}");
            });

            //
            //Note: Important!!!, "RegisterSinglePageApplication" has to be placed after "UseEndpoints".
            //
            //
            // If Angular Single Page Application is required to be hosting inside same domain and same port number with Web API endpoint. (Angular built dist files need to be saved inside Web API Static files folder).
            // Corporate with setting called "SPAPaths" in appsettings.json.
            //
            if (this.ConfigSettings.SPAConfig != null)
            {
                //
                // Work for hosting Angular App or React App as part of Web API. (In same domain)
                //
                this.RegisterSinglePageApplication(app, this.ConfigSettings.SPAConfig);
            }
        }

        #region Angular/React single page application build host and routing matter.

        /// <summary>
        ///  By default, Angular App and React App's boostrap page is index.html.
        /// </summary>
        private const string defaultSPAIndexHtmlFileName = "index.html";

        /// <summary>
        ///  Work for requirement that host single page application as sub directory under Web API portal domain.
        ///  There are two options:
        ///  1: Keep single page application build in Web API static file folder. (File system approach).
        ///  2: Keep single page application bild in Azure Blob Storage. (Azure Storage approach).
        /// </summary>
        /// <param name="app"></param>
        /// <param name="config"></param>
        /// <param name="fileName"></param>
        /// <param name="including_wwwroot"></param>
        public void RegisterSinglePageApplication(IApplicationBuilder app, SPAConfig config, string fileName = defaultSPAIndexHtmlFileName, bool including_wwwroot = false)
        {
            this.RegisterServeStaticFiles(app, config, including_wwwroot);
            this.RegisterSPAIndexFallback(app, fileName);
        }

        /// <summary>
        /// Add Angular Index html Fallback
        /// </summary>
        /// <param name="app"></param>
        /// <param name="path"></param>
        /// <param name="fileName"></param>
        /// <remarks>This must be use in last line of Configure</remarks>
        private void RegisterSPAIndexFallback(IApplicationBuilder app, string fileName = defaultSPAIndexHtmlFileName)
        {
            app.Run(async (context) =>
            {
                //
                // Get current request url's associated SPA file path.
                //
                var path = context.Request.Path;

                //
                // For normal image, document files, ignore this process.
                //
                if (string.IsNullOrEmpty(Path.GetExtension(path)))
                {
                    //
                    //Note: it could be physical file path in file system or sub path in Azure Blob storage container.
                    //
                    string spaPath = GetSPAIndexPath(path.Value);

                    if (!string.IsNullOrEmpty(spaPath))
                    {
                        context.Response.ContentType = "text/html";
                        context.Response.StatusCode = 200;

                        // Do not cache index.html file
                        //
                        if (fileName.Equals(defaultSPAIndexHtmlFileName))
                        {
                            context.Response.Headers.Add("Cache-Control", "no-cache, no-store");
                            context.Response.Headers.Add("Expires", "-1");
                        }


                        if (!this.ConfigSettings.SPAConfig.SPAHostingInAzureBlobStorage)
                        {
                            //
                            // It the path is local file system path.
                            //
                            if (spaPath.StartsWith("."))
                            {
                                spaPath = Path.Combine(Directory.GetCurrentDirectory(), spaPath);
                            }

                            await context.Response.SendFileAsync(Path.Combine(spaPath, fileName));
                        }
                        else
                        {
                            //
                            // It is path in Azure Blog Storage.
                            // Note: As for spaPath, the first node is container.
                            //
                            var blobFilePath = string.Format("{0}/{1}", spaPath, fileName).Replace("//", "/");
                            await SendFileFromBlob(context.Response, app, this.ConfigSettings.SPAConfig, blobFilePath);
                        }
                    }
                }
            });
        }


        private async Task SendFileFromBlob(HttpResponse response, IApplicationBuilder app, SPAConfig config, string filePath)
        {
            try
            {
                string container = filePath.Split("/".ToCharArray(), StringSplitOptions.RemoveEmptyEntries)[0];
                string subPath = filePath.Replace(container, "");
                var blobClient = GetFileFromBlob(app, config, container, subPath);
                if (blobClient != null)
                {
                    await blobClient.DownloadToAsync(response.Body);
                }
                return;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Trace.TraceError(ex.ToString());
            }
        }

        private BlobClient GetFileFromBlob(IApplicationBuilder app, SPAConfig config, string container, string filePath)
        {
            try
            {
                var containerFactory = app.ApplicationServices.GetRequiredService<DefaultBlobContainerFactory>();
                var containerClient = containerFactory.GetContainer(container);
                return containerClient.GetBlobClient(filePath);

            }
            catch (Exception ex)
            {
                System.Diagnostics.Trace.TraceError(ex.ToString());
            }

            return null;
        }


        /// <summary>
        ///  Static collection to keey pair of "spa url (sub path)" and "spa relative physical file path".
        ///  Init value should get from ConfigSttings.SPAPaths
        /// </summary>
        private static Dictionary<string, string> staticSPAPaths = new Dictionary<string, string>();

        /// <summary>
        ///  Get physical relative file path based on input request url.
        ///  Note: Current url route rule: https://[Domain]:[Port Number]/[spa url]/../..
        ///  The SPA routing path must be in fist level sub path.
        /// </summary>
        /// <param name="requestPath"></param>
        /// <returns></returns>
        private string GetSPAIndexPath(string requestPath)
        {
            var path = staticSPAPaths.FirstOrDefault(k => requestPath.Contains(k.Key));

            if (!string.IsNullOrEmpty(path.Value))
            {
                return path.Value;
            }
            else
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// Enable and register serve static files from Web API.
        /// Have to corporate with config setting called "SPAPaths".
        /// Note: Only add this to web project's Startup.Configure() 
        /// if web api need to serve static file a.k.a. Angular App.
        /// </summary>
        /// <param name="app"></param>
        /// <param name="including_wwwroot">
        ///   For MVC portal. Value = true. 
        ///   By default, value = false for Web API portal.
        /// </param>
        /// <param name="config">
        /// It is section called "SPAConfig" in appsettings.development.json 
        /// or same section in Azure App Configuration for Azure App Service hosting. 
        /// </param>
        private void RegisterServeStaticFiles(IApplicationBuilder app, SPAConfig config, bool including_wwwroot = false)
        {
            if (including_wwwroot)
            {
                app.UseStaticFiles(); // For the wwwroot folder
            }

            //
            // Keep the SPAPaths config settings into static global collection.
            // Save performance.
            //
            if (staticSPAPaths == null || staticSPAPaths.Count == 0)
            {
                staticSPAPaths = config.SPAPaths;
            }

            if (config.SPAHostingInAzureBlobStorage)
            {
                //
                // Regist Azure Blob Storage as Web API's Static Files provider.
                // Work for hosting Single Page Application build in Azure Storage and access them as sub directory in Web API domain.
                // Note: Work for scenario that client do not allow cross accessing multiple domains.
                // Reference: https://github.com/filipw/Strathweb.AspNetCore.AzureBlobFileProvider
                //
                //
                if (staticSPAPaths != null)
                {                  
                    var containerFactory = app.ApplicationServices.GetRequiredService<DefaultBlobContainerFactory>();

                    foreach (var path in staticSPAPaths)
                    {
                        var blobFileProvider = new AzureBlobFileProvider(containerFactory, path.Value); // Path.Value is container name.

                        app.UseStaticFiles(new StaticFileOptions()
                        {
                            FileProvider = blobFileProvider,
                            RequestPath = path.Key // It is url route.
                        });

                        //
                        // Only for test Azure Blob Storage files online browsing purpose.
                        // Note: Don't enable UseDirectoryBrowser if we are running single page app in sub path routing in web api portal.
                        //
                        //app.UseDirectoryBrowser(new DirectoryBrowserOptions
                        //{
                        //    FileProvider = blobFileProvider,
                        //    RequestPath = path.Key
                        //});
                    }
                }
            }
            else
            {
                //
                // Keep the single page application build files in local sub directory under web api portal (file system).
                //
                if (staticSPAPaths != null)
                {
                    foreach (var path in staticSPAPaths)
                    {
                        //
                        // Directly point to the React App project "build" folder.
                        //
                        string fullPath = Path.GetFullPath(Path.Combine(Directory.GetCurrentDirectory(), @path.Value)); //string.Format("{0}{1}", Directory.GetCurrentDirectory(), path.Value);

                        app.UseStaticFiles(new StaticFileOptions
                        {
                            FileProvider = new PhysicalFileProvider(fullPath),
                            RequestPath = path.Key // It is url route.
                        }); ;

                    }
                }
            }
        }

        #endregion

    }

}
