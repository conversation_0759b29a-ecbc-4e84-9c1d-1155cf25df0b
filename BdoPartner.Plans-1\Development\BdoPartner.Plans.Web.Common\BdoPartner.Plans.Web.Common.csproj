﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net5.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Azure.Identity" Version="1.4.1" />
    <PackageReference Include="Azure.Storage.Blobs" Version="12.9.1" />
    <PackageReference Include="Azure.Storage.Common" Version="12.8.0" />
    <PackageReference Include="IdentityModel.AspNetCore.OAuth2Introspection" Version="5.1.0" />
    <PackageReference Include="IdentityServer4.AccessTokenValidation" Version="3.0.1" />
    <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.18.0" />
    <PackageReference Include="Microsoft.AspNetCore.AzureAppServices.HostingStartup" Version="5.0.9" />
    <PackageReference Include="Microsoft.AspNetCore.AzureAppServicesIntegration" Version="5.0.9" />
    <PackageReference Include="Microsoft.AspNetCore.Http" Version="2.2.2" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc" Version="2.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR" Version="1.1.0" />
    <PackageReference Include="Microsoft.Azure.AppConfiguration.AspNetCore" Version="4.5.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.AzureAppConfiguration" Version="4.5.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.AzureKeyVault" Version="3.1.17" />
    <PackageReference Include="Microsoft.IdentityModel.Clients.ActiveDirectory" Version="5.2.9" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.1.5" />
    <PackageReference Include="System.Net.Http" Version="4.3.4" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\BdoPartner.Plans.Business.Interface\BdoPartner.Plans.Business.Interface.csproj" />
    <ProjectReference Include="..\BdoPartner.Plans.Business\BdoPartner.Plans.Business.csproj" />
  </ItemGroup>

</Project>
