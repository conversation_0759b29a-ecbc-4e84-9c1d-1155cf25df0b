﻿using Microsoft.AspNetCore.Html;
using Microsoft.AspNetCore.Mvc.Rendering;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;

namespace BdoPartner.Plans.Web.Common.Helper
{
    public static class MvcHtmlExtension
    {
        public static IHtmlContent MenuLinkSelect(this IHtmlHelper helper, string controller, string action = "")
        {
            var routeData = helper.ViewContext.RouteData.Values;
            var currentController = routeData["controller"];
            var currentAction = routeData["action"];
            if (currentController != null && String.Equals(controller, currentController as string, StringComparison.OrdinalIgnoreCase))
            {
                if (string.IsNullOrEmpty(action) || String.Equals(action, currentAction as string, StringComparison.OrdinalIgnoreCase))
                {
                    return new HtmlString("select");
                }
            }
            else
            {
                var currentPage = routeData["page"];
                if (currentPage != null)
                {
                    string currentPath = $"/{controller}/{action}".ToLower();
                    string curPage = currentPage.ToString().ToLower() + "/";
                    if (curPage.Substring(0, 1) != "/") { curPage = $"/{curPage}"; }
                    if (curPage.ToString().ToLower().StartsWith(currentPath))
                    {
                        return new HtmlString("select");
                    }
                }
            }
            return new HtmlString("");
        }

        public static IHtmlContent MenuLinkSelectExternal(this IHtmlHelper helper, string url)
        {
            if (!string.IsNullOrEmpty(url))
            {
                string[] items = url.Split('/', StringSplitOptions.RemoveEmptyEntries);

                if (items != null && items.Length > 0)
                {
                    string controller = items[0];
                    string action = "";
                    if (items.Length >= 2) action = items[1];

                    //return helper.MenuLinkSelect(controller, action);
                    return helper.MenuLinkSelect(controller);
                }
                else
                {
                    return helper.MenuLinkSelect(url);
                }
            }
            else
            {
                return new HtmlString("");
            }
        }


        public static IHtmlContent MenuLinkSelectInternal(this IHtmlHelper helper, string url)
        {
            if (!string.IsNullOrEmpty(url))
            {
                string[] items = url.Split('/', StringSplitOptions.RemoveEmptyEntries);
                if (items != null && items.Length > 0)
                {
                    string controller = items[0];
                    string action = "";
                    if (items.Length >= 2) action = items[1];
                    if (url.ToLower().Contains("transfer/search") || url.ToLower().Contains("transfer/detail"))
                    {
                        return helper.MenuLinkSelect(controller, action);
                    }
                    return helper.MenuLinkSelect(controller);
                }
                else
                {
                    return helper.MenuLinkSelect(url);
                }
            }
            else
            {
                return new HtmlString("");
            }
        }


        /// <summary>
        ///  Work for extract href url information from hyperlink html content.
        ///  Work for quick link feature in menus in External Portal.
        /// </summary>
        /// <param name="helper"></param>
        /// <param name="inputHtml"></param>
        /// <returns></returns>
        public static IHtmlContent HyperLinkUrl(this IHtmlHelper helper, string inputHtml) {

            if (!string.IsNullOrEmpty(inputHtml)) {
                inputHtml = inputHtml.Trim().ToLower();
                Regex regex = new Regex("href\\s*=\\s*(?:\"(?<1>[^\"]*)\"|(?<1>\\S+))", RegexOptions.IgnoreCase);
                Match match = regex.Match(inputHtml);
                if (match.Success) {
                    return new HtmlString(match.Value);
                }
            }

            return new HtmlString("");
        }

        /// <summary>
        ///  Work for extract hyperlink link text information from hyperlink html content.
        ///  Work for quick link feature in menus in External Portal.
        /// </summary>
        /// <param name="helper"></param>
        /// <param name="inputHtml"></param>
        /// <returns></returns>
        public static IHtmlContent HyperLinkText(this IHtmlHelper helper, string inputHtml)
        {

            if (!string.IsNullOrEmpty(inputHtml))
            {
                inputHtml = inputHtml.Trim().ToLower();
                Regex regex = new Regex("<a[^>]*? href=\"(?<url>[^\"]+)\"[^>]*?>(?<text>.*?)</a>", RegexOptions.IgnoreCase);
                Match match = regex.Match(inputHtml);
                if (match.Success)
                {
                    return new HtmlString(match.Groups["text"].Value);
                }
            }

            return new HtmlString("");
        }

    }
}