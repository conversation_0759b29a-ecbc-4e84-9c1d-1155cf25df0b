﻿using BdoPartner.Plans.Common;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;

using System.Net;
using IdentityModel.Client;
using System.Security.Claims;
using System.Linq;
using Microsoft.AspNetCore.Authentication;

namespace BdoPartner.Plans.Web.Common.Helper
{
    /// <summary>
    ///  Corporate with Identity Server single signon, inject access token into http request header.
    ///  Work for Asp.net MVC site call Resource Web API with Http Request.
    ///  Reference: https://docs.identityserver.io/en/latest/quickstarts/3_aspnetcore_and_apis.html
    /// </summary>
    public static class HttpHelper
    {

        /// <summary>
        ///  Http Post to call web api and return response. For create new entity process. Included accessToken and corporate wiht Identity Server 4 Single Signon.
        ///  Reference: https://docs.microsoft.com/en-us/aspnet/web-api/overview/advanced/calling-a-web-api-from-a-net-client?WT.mc_id=DT-MVP-5003235
        /// </summary>
        /// <typeparam name="T">Define the returned result's entity type.</typeparam>
        /// <typeparam name="TT">Define the input parameter's entity type.</typeparam>
        /// <param name="entity">Post an entity object as parameter.</param>
        /// <param name="rootUrl">Root url for http request.</param>
        /// <param name="url">Relative web api method call url.</param>
        /// <returns></returns>
        public static async Task<BusinessResult<T>> Post<T, TT>(this HttpContext httpContext, string rootUrl, string url, TT entity)
        {
            BusinessResult<T> result = new BusinessResult<T>(ResultStatus.Failure);
            var accessToken = await httpContext.GetTokenAsync("assess_token");

            using (HttpClient client = GetHttpJsonClient(rootUrl, accessToken))
            {                
                HttpResponseMessage response = await client.PostAsJsonAsync<TT>(url, entity);

                if (response.IsSuccessStatusCode)
                {
                    result = await response.Content.ReadAsJsonAsync<BusinessResult<T>>();
                }

                return result;
            }
        }

        /// <summary>
        ///  Http Get to call web api and return response. Included accessToken and corporate wiht Identity Server 4 Single Signon.
        ///  Reference: https://docs.microsoft.com/en-us/aspnet/web-api/overview/advanced/calling-a-web-api-from-a-net-client?WT.mc_id=DT-MVP-5003235
        /// </summary>
        /// <param name="rootUrl">Root url for http request.</param>
        /// <param name="url">Relative web api method call url.</param>
        /// <typeparam name="T">Define the returned result's entity type.</typeparam>
        /// <returns></returns>
        public static async Task<BusinessResult<T>> Get<T>(this HttpContext httpContext, string rootUrl, string url)
        {
            BusinessResult<T> result = new BusinessResult<T>(ResultStatus.Failure);
            var accessToken = await httpContext.GetTokenAsync("access_token");

            using (HttpClient client = GetHttpJsonClient(rootUrl, accessToken))
            {
                HttpResponseMessage response = await client.GetAsync(url);

                if (response.IsSuccessStatusCode)
                {
                    result = await response.Content.ReadAsJsonAsync<BusinessResult<T>>();
                }

                return result;
            }
        }

        /// <summary>
        ///  Http Get to call web api and return response. Included accessToken and corporate wiht Identity Server 4 Single Signon.
        ///  Reference: https://docs.microsoft.com/en-us/aspnet/web-api/overview/advanced/calling-a-web-api-from-a-net-client?WT.mc_id=DT-MVP-5003235
        /// </summary>
        /// <param name="url">Relative web api method call url.</param>
        /// <typeparam name="T">Define the returned result's entity type.</typeparam>
        /// <param name="parameters">Input parameters collection, which will be passed into web api through url parameters.</param>
        /// <returns></returns>
        public static async Task<BusinessResult<T>> Get<T>(this HttpContext httpContext, string rootUrl, string url, Dictionary<string, string> parameters)
        {
            BusinessResult<T> result = new BusinessResult<T>(ResultStatus.Failure);
            string query = string.Empty;
            if (parameters != null)
            {
                query = new FormUrlEncodedContent(parameters).ReadAsStringAsync().Result;
            }

            if (!string.IsNullOrEmpty(query))
            {
                url += "?" + query;
            }

            result = await httpContext.Get<T>(rootUrl, url);

            return result;
        }


        /// <summary>
        ///  Http Put to call web api and return response. For update entity process. Included accessToken and corporate wiht Identity Server 4 Single Signon.
        ///  Reference: https://docs.microsoft.com/en-us/aspnet/web-api/overview/advanced/calling-a-web-api-from-a-net-client?WT.mc_id=DT-MVP-5003235
        /// </summary>
        /// <typeparam name="T">Define the returned result's entity type.</typeparam>
        /// <typeparam name="TT">Define the input parameter's entity type.</typeparam>
        /// <param name="entity">Put an entity for update porcess.</param>
        /// <param name="url">Relative web api method call url.</param>
        /// <returns></returns>
        public static async Task<BusinessResult<T>> Put<T, TT>(this HttpContext httpContext, string rootUrl, string url, TT entity)
        {
            BusinessResult<T> result = new BusinessResult<T>(ResultStatus.Failure);
            var accessToken = await httpContext.GetTokenAsync("assess_token");

            using (HttpClient client = GetHttpJsonClient(rootUrl, accessToken))
            {

                HttpResponseMessage response = await client.PutAsJsonAsync<TT>(url, entity);

                if (response.IsSuccessStatusCode)
                {
                    result = await response.Content.ReadAsJsonAsync<BusinessResult<T>>();
                }

                return result;
            }
        }


        /// <summary>
        ///  Http Delete to call web api and return response. For delete entity process. Included accessToken and corporate wiht Identity Server 4 Single Signon.
        ///  Reference: https://docs.microsoft.com/en-us/aspnet/web-api/overview/advanced/calling-a-web-api-from-a-net-client?WT.mc_id=DT-MVP-5003235
        /// </summary>
        /// <typeparam name="T">Define the returned result's entity type.</typeparam>
        /// <param name="rootUrl">Http request root url.</param>
        /// <param name="url">Relative web api method call url.</param>
        /// <returns></returns>
        public static async Task<BusinessResult<T>> Delete<T>(this HttpContext httpContext, string rootUrl, string url)
        {
            BusinessResult<T> result = new BusinessResult<T>(ResultStatus.Failure);
            var accessToken = await httpContext.GetTokenAsync("assess_token");

            using (HttpClient client = GetHttpJsonClient(rootUrl, accessToken))
            {
                HttpResponseMessage response = await client.DeleteAsync(url);

                if (response.IsSuccessStatusCode)
                {
                    result = await response.Content.ReadAsJsonAsync<BusinessResult<T>>();
                }

                return result;
            }
        }

        /// <summary>
        ///  Included accessToken and corporate wiht Identity Server 4 Single Signon.
        /// </summary>
        /// <param name="apiRootUrl"></param>
        /// <param name="accessToken"></param>
        /// <returns></returns>
        public static HttpClient GetHttpJsonClient(string apiRootUrl, string accessToken)
        {
            HttpClient client = new HttpClient();
            client.BaseAddress = new Uri(apiRootUrl);
            client.DefaultRequestHeaders.Accept.Clear();
            client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

            return client;
        }

        /// <summary>
        ///  Http Post with Json object.
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="httpClient"></param>
        /// <param name="url">It is relative http request url.</param>
        /// <param name="data"></param>
        /// <returns></returns>
        public static Task<HttpResponseMessage> PostAsJsonAsync<T>(
           this HttpClient httpClient, string url, T data)
        {
            var dataAsString = JsonConvert.SerializeObject(data);
            var content = new StringContent(dataAsString);
            // content.Headers.ContentType = new MediaTypeHeaderValue("application/json");
            return httpClient.PostAsync(url, content);
        }

        /// <summary>
        /// Http Put with Json object.
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="httpClient"></param>
        /// <param name="url">It is relative http request url.</param>
        /// <param name="data"></param>
        /// <returns></returns>
        public static Task<HttpResponseMessage> PutAsJsonAsync<T>(
           this HttpClient httpClient, string url, T data)
        {
            var dataAsString = JsonConvert.SerializeObject(data);
            var content = new StringContent(dataAsString);
            // content.Headers.ContentType = new MediaTypeHeaderValue("application/json");
            return httpClient.PutAsync(url, content);
        }

        public static async Task<T> ReadAsJsonAsync<T>(this HttpContent content)
        {
            string json = await content.ReadAsStringAsync();
            T value = JsonConvert.DeserializeObject<T>(json);
            return value;
        }


        /// <summary>
        ///  Work for logging http request information.
        ///  Called by user login process.
        ///  Note: HttpContext.GetServerVariable method could return null, 
        ///  this caused by the hosting server setup.
        ///  Note: In localhost debug, the IP and Browser always return null. But this is not a bug.
        ///  
        /// https://stackoverflow.com/questions/54872276/retrieve-servervariables-in-asp-net-core
        /// </summary>
        /// <param name="httpContext"></param>
        /// <returns>Request IP and browser info.</returns>
        public static (string, string) GenerateHttpRequestLog(this HttpContext httpContext, bool allowForwarded = true) {
            string result_ip = "";
            string browser = "";


            if (allowForwarded)
            {
                string header = (httpContext.Request.Headers["CF-Connecting-IP"].FirstOrDefault() ?? httpContext.Request.Headers["X-Forwarded-For"].FirstOrDefault());
                if (IPAddress.TryParse(header, out IPAddress ip))
                {
                    result_ip = ip.ToString();
                }
            }
            else
            {
                result_ip = httpContext.Connection.RemoteIpAddress.ToString();
            }

            browser = httpContext.Request.Headers["HTTP_USER_AGENT"].FirstOrDefault() ;

            return (result_ip, browser);
        }

        /// <summary>
        ///  After current logon user's associated authorization setup changed on run time, 
        ///  developer needs to call this method on portals to refresh
        ///  logon identity's associated access_token, refresh_token, id_token and claims through Identity Server.
        ///  Further, the portal is able to use new access_token to access web api endpoint 
        ///  and the web api endpoint's CurrentUser identity and claims also got refreshed.
        /// 
        ///  Reference: 
        ///  https://code-maze.com/identityserver4-authorization/
        ///  https://stackoverflow.com/questions/47320537/net-core-2-identity-server-4-refresh-all-of-the-claims
        ///  https://stackoverflow.com/questions/44175115/how-to-use-refresh-token-in-identityserver-4
        ///  
        /// Note: Developer needs to have a way to refresh javascript "GLOBAL_API_TOKENVALUE" variable in _jsGlobal.cshtml component after call this method.
        /// For example, use redirection page.
        /// </summary>
        /// <param name="httpContext"></param>
        /// <param name="identityServerURL"></param>
        /// <returns></returns>
        public static async Task<BusinessResult> RefreshIdentityClaimsForClient(this HttpContext httpContext, 
              string identityServerUrl, string currentClientId, string currentClientSecret, string clientScopes) 
        {
            BusinessResult result = new BusinessResult();
                        
            var idToken = await httpContext.GetTokenAsync("id_token"); //oidc supported.
            var accessToken = await httpContext.GetTokenAsync("access_token");
            var refreshToken = await httpContext.GetTokenAsync("refresh_token");

            string new_id_token = "";
            string new_access_token = "";
            string new_refresh_token = "";

            using (HttpClient client = new HttpClient())
            {
                //
                // Note: We cannot call GetDiscoveryDocumentAssync() method to get TokenEndPoint and UserInfoEndPoint in FireFox.
                // So, here, directly assign token url and user info url to Refresh Token call and Get user info call through Identity Server4.
                //
                //
                // Try to discovery the Identity Server endpoint first.
                //
                //var discoveryResponse = await client.GetDiscoveryDocumentAsync(new DiscoveryDocumentRequest
                //{
                //    Address = identityServerUrl,
                //    Policy = {
                //       RequireHttps = false,
                //       ValidateEndpoints = false,
                //       ValidateIssuerName = false
                //    }
                //});

                //string tokenAddress = discoveryResponse.TokenEndpoint;
                                
                //
                // Request new refresh_token and access_token. claims will got refreshed at this stage.
                //
                var refreshTokenResult = await client.RequestRefreshTokenAsync(new RefreshTokenRequest
                    {
                        Address = string.Format("{0}{1}", identityServerUrl, "/connect/token"),  // discoveryResponse.TokenEndpoint,
                        ClientId = currentClientId,
                        ClientSecret = currentClientSecret,
                        RefreshToken = refreshToken,
                        Scope = clientScopes // porporate with client's scope setting. 
                    });

                if (!refreshTokenResult.IsError)
                {
                    new_access_token = refreshTokenResult.AccessToken;
                    new_refresh_token = refreshTokenResult.RefreshToken;
                    new_id_token = refreshTokenResult.IdentityToken;

                    var tokens = new List<AuthenticationToken>();
                    tokens.Add(new AuthenticationToken { Name = "id_token", Value = new_id_token });
                    tokens.Add(new AuthenticationToken { Name = "access_token", Value = new_access_token });
                    tokens.Add(new AuthenticationToken { Name = "refresh_token", Value = new_refresh_token });
                    tokens.Add(new AuthenticationToken { Name = "token_type", Value = "Bearer" });

                    DateTime expiresAt = DateTime.UtcNow.AddSeconds(refreshTokenResult.ExpiresIn);
                    tokens.Add(new AuthenticationToken { Name = "expires_at", Value = expiresAt.ToString("yyyy-MM-ddTHH:mm:ss") + ".0000000+00:00" });
                    var authResult = await httpContext.AuthenticateAsync("Cookies");

                    var tokenType = authResult.Properties.Items.FirstOrDefault(it => it.Key == ".Token.token_type");
                    var tokenValue = $"{{\"accessToken\":\"{new_access_token}\",\"type\":\"{tokenType.Value}\"}}";

                    //
                    // Note: "TokenValue" corporates with Javascript library to assign access token to http request when calling resource web api.
                    // Corporate with _jsGlobal.cshtml and RefreshTokenMiddleware which associated to "Refresh_Token" process.
                    // Note: Developer needs to have a way to refresh "GLOBAL_API_TOKENVALUE" variable in _jsGlobal.cshtml component after call this method.
                    // For example, use redirection page.
                    //
                    httpContext.Items["TokenValue"] = tokenValue;

                    //
                    // Update Cookies storage with new generated tokens (id_token, access_token, refresh_token) with new expiration date time.
                    // Note: Old tokens will go clean up first -> StoreTokens.
                    //
                    authResult.Properties.StoreTokens(tokens);

                    if (!string.IsNullOrEmpty(new_access_token))
                    {
                        using (HttpClient newClient = GetHttpJsonClient(identityServerUrl, new_access_token))
                        {
                            //
                            // Note: We cannot call GetDiscoveryDocumentAssync() method to get TokenEndPoint and UserInfoEndPoint in FireFox.
                            // So, here, directly assign token url and user info url to Refresh Token call and Get user info call through Identity Server4.
                            //
                            //var newMetaDataResponse = await newClient.GetDiscoveryDocumentAsync(identityServerUrl);

                            var response = await client.GetUserInfoAsync(new UserInfoRequest
                            {
                                Address = string.Format("{0}{1}", identityServerUrl, "/connect/userinfo"), //newMetaDataResponse.UserInfoEndpoint,
                                Token = new_access_token
                            });

                            if (response != null)
                            {
                                if (response.IsError)
                                {
                                    result.Message = "RefreshIdentityClaimsForClient exception. Problem while fetching data from the UserInfo endpoint: " + response.Exception.ToString();
                                }
                                else
                                {
                                    //
                                    // Update current logon user's associated custom claims with new claims value in HTTPContext.
                                    //
                                    if (response.Claims != null && response.Claims.Count() > 0)
                                    {
                                        var currentIdentity = authResult.Principal.Identity as ClaimsIdentity;

                                        foreach (var claim in response.Claims)
                                        {
                                            var existClaim = currentIdentity.Claims.FirstOrDefault(c => c.Type == claim.Type);
                                            if (existClaim != null)
                                            {
                                                currentIdentity.RemoveClaim(existClaim);
                                            }

                                            currentIdentity.AddClaim(claim);
                                        }

                                        //
                                        // Update the cookies with the new principal and identity
                                        // Name "Cookies" need to corporate with Startup.cs "AddCookie" in ExternalWeb and InternalWeb project.
                                        //
                                        await httpContext.SignInAsync("Cookies", authResult.Principal, authResult.Properties);

                                        result.ResultStatus = ResultStatus.Success;

                                    }
                                    else
                                    {
                                        result.Message = " RefreshIdentityClaimsForClient exception. GetUserInfoAsync call return response without claims.";
                                    }
                                }
                            }
                            else
                            {
                                result.Message = " RefreshIdentityClaimsForClient exception. GetUserInfoAsync call return null response.";
                            }
                        }
                    }
                }
                else {
                    result.Message = "Identity Server refresh token process failed.";
                }                
            }
           
            return result;
        }
    }
}
