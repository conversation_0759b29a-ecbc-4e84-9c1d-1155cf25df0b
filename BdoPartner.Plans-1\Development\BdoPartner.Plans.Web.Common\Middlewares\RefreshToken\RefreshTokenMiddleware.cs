﻿using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Http;
using System.Globalization;
using System.Threading.Tasks;
using System.Linq;
using Microsoft.Extensions.Configuration;
using System.Net.Http;
using IdentityModel.Client;
using System;

namespace BdoPartner.Plans.Web.Common.Middlewares
{
    public class RefreshTokenMiddleware
    {

        private readonly RequestDelegate _next;
        private readonly IConfiguration _config;

        public RefreshTokenMiddleware(RequestDelegate next, IConfiguration configuration)
        {
            _next = next;
            _config = configuration;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            string tokenValue = "{}";
            //try to refresh token on every request
            if (context.User.Identity.IsAuthenticated)
            {
                var authResult = await context.AuthenticateAsync();
                if (authResult.Succeeded)
                {
                    var tokenExpired = authResult.Properties.Items.FirstOrDefault(it => it.Key == ".Token.expires_at");
                    var tokenType = authResult.Properties.Items.FirstOrDefault(it => it.Key == ".Token.token_type");
                    if (!string.IsNullOrWhiteSpace(tokenExpired.Value) && !string.IsNullOrWhiteSpace(tokenType.Value))
                    {
                        //format:2020-12-29T09:02:08
                        DateTime dtmExp = DateTime.MaxValue;
                        DateTime.TryParseExact(tokenExpired.Value.Substring(0, 19), "yyyy-MM-ddTHH:mm:ss",
                            System.Globalization.CultureInfo.InvariantCulture, DateTimeStyles.AdjustToUniversal,
                            out dtmExp);
                        DateTime now = DateTime.UtcNow;
                        if (dtmExp <= now)
                        {
                            //has expired, need a refresh
                            string refreshToken = authResult.Properties.GetTokenValue("refresh_token");
                            var client = new HttpClient();

                            var response = await client.RequestRefreshTokenAsync(new RefreshTokenRequest
                            {
                                Address = _config.GetSection("IdentityServer:Authority").Value + "/connect/token",

                                ClientId = _config.GetSection("IdentityServer:ClientId").Value,
                                ClientSecret = _config.GetSection("IdentityServer:ClientSecret").Value,

                                RefreshToken = refreshToken
                            });

                            if (response.HttpStatusCode == System.Net.HttpStatusCode.OK)
                            {
                                authResult.Properties.Items[".Token.expires_at"] = now.AddSeconds(response.ExpiresIn).ToString("yyyy-MM-ddTHH:mm:ss") + ".0000000+00:00";
                                authResult.Properties.UpdateTokenValue("access_token", response.AccessToken);
                                authResult.Properties.UpdateTokenValue("refresh_token", response.RefreshToken);
                                authResult.Properties.UpdateTokenValue("id_token", response.IdentityToken);
                                await context.SignInAsync(authResult.Principal, authResult.Properties);

                                tokenValue= $"{{\"accessToken\":\"{response.AccessToken}\",\"type\":\"{tokenType.Value}\"}}";
                            }
                        }
                        else
                        {
                            string accessToken = authResult.Properties.GetTokenValue("access_token");
                            tokenValue = $"{{\"accessToken\":\"{accessToken}\",\"type\":\"{tokenType.Value}\"}}";
                        }
                    }
                }
            }

            context.Items["TokenValue"] = tokenValue;

            if (context.Request.Path.ToString().StartsWith("/GetOrRefreshAccessToken", StringComparison.InvariantCultureIgnoreCase))
            {
                await context.Response.WriteAsync(tokenValue);
            }
            else
            {
                // Call the next delegate/middleware in the pipeline
                await this._next(context);
            }
        }
    }
}
