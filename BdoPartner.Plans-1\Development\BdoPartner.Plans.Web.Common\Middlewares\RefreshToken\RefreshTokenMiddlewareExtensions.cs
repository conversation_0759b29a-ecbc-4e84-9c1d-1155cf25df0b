﻿using Microsoft.AspNetCore.Builder;
using System;
using System.Collections.Generic;
using System.Text;

namespace BdoPartner.Plans.Web.Common.Middlewares
{

    public static class RefreshTokenMiddlewareExtensions
    {
        /// <summary>
        ///  exposes the request culture middle ware. work for localization.
        /// </summary>
        /// <param name="builder"></param>
        /// <returns></returns>
        public static IApplicationBuilder UseRefreshToken(
            this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<RefreshTokenMiddleware>();
        }
    }
}
