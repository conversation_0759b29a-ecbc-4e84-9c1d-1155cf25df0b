﻿using Microsoft.AspNetCore.Http;
using System.Globalization;
using System.Threading.Tasks;
using BdoPartner.Plans.Common;
using Microsoft.Extensions.Caching.Memory;
using DTO = BdoPartner.Plans.Model.DTO;
using System;

/// <summary>
/// Creating a middleware pipeline with IApplicationBuilder
/// The ASP.NET Core request pipeline consists of a sequence of request delegates, called one after the other, as this diagram shows (the thread of execution follows the black arrows):
/// Request processing pattern showing a request arriving, processing through three middlewares, and the response leaving the application.
/// Each middleware runs its logic and hands off the request to the next middleware at the next() statement.
/// After the third middleware processes the request, 
/// the request passes back through the prior two middlewares in reverse order for additional processing after their next() statements before leaving the application as a response to the client.
/// Each delegate can perform operations before and after the next delegate. 
/// A delegate can also decide to not pass a request to the next delegate, which is called short-circuiting the request pipeline. 
/// Short-circuiting is often desirable because it avoids unnecessary work.
/// For example, the static file middleware can return a request for a static file and short-circuit the rest of 
/// the pipeline.Exception-handling delegates need to be called early in the pipeline, so they can catch exceptions that occur in later stages of the pipeline.
/// The simplest possible ASP.NET Core app sets up a single request delegate that handles all requests. 
/// This case doesn't include an actual request pipeline. Instead, a single anonymous function is called in response to every HTTP request
/// 
///  References: https://docs.microsoft.com/en-us/aspnet/core/fundamentals/middleware/?view=aspnetcore-2.1&tabs=aspnetcore2x
/// </summary>
namespace BdoPartner.Plans.Web.Common.Middlewares.RequestAppContext
{
    /// <summary>
    ///  Work for get blobal settings.
    ///  Corporate with "security.oidc.interceptor.ts"
    ///  
    /// </summary>
    public class RequestAppContextMiddleware
    {
        private readonly RequestDelegate _next;
        // private IMemoryCache _cache;

        public RequestAppContextMiddleware(RequestDelegate next
            //, IMemoryCache memoryCache
        )
        {
            _next = next;
            // _cache = memoryCache;
        }

        public Task InvokeAsync(HttpContext context)
        {            
            string culture = context.Request.Headers["currentLanguage"];
                        
            Enumerations.Language langauge = Enumerations.Language.EN;

            if (string.IsNullOrWhiteSpace(culture))
            {
                culture = "en-us";
            }

            if (culture == "en-us")
                langauge = Enumerations.Language.EN;
            else if (culture == "fr-ca")
                langauge = Enumerations.Language.FR;
            else
                langauge = Enumerations.Language.EN;

            
            DTO.AppContext appContext = new DTO.AppContext();

            
            appContext.Language = langauge;
          
            string timeZoneOffset = context.Request.Headers["timeZoneOffset"];
            if (!string.IsNullOrWhiteSpace(timeZoneOffset))
            {
                int iTimeZoneOffset = 0;
                int.TryParse(timeZoneOffset, out iTimeZoneOffset);
                appContext.TimeZoneOffset = iTimeZoneOffset;
            }
              

            //
            // Save the appContext as session in short life.
            //
            context.Items[Constants.CURRENT_APP_CONTEXT] = appContext;

            // Call the next delegate/middleware in the pipeline
            return this._next(context);
        }
        
    }
}
