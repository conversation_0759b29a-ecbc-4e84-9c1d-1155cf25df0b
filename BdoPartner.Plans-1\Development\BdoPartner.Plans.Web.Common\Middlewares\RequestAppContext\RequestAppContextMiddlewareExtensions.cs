﻿using Microsoft.AspNetCore.Builder;
using System;
using System.Collections.Generic;
using System.Text;

namespace BdoPartner.Plans.Web.Common.Middlewares.RequestAppContext
{
    /// <summary>
    /// Reference: https://docs.microsoft.com/en-us/aspnet/core/fundamentals/middleware/?view=aspnetcore-2.1&tabs=aspnetcore2x
    /// </summary>
    public static class RequestAppContextMiddlewareExtensions
    {
        /// <summary>
        ///  exposes the request appcontext middleware. work for get global info and other shared information.
        /// </summary>
        /// <param name="builder"></param>
        /// <returns></returns>
        public static IApplicationBuilder UseRequestAppContext(
            this IApplicationBuilder builder)
        {           
            return builder.UseMiddleware<RequestAppContextMiddleware>();
        }
    }
}
