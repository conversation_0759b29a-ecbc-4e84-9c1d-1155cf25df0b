﻿using Microsoft.AspNetCore.Http;
using System.Globalization;
using System.Threading.Tasks;

/// <summary>
/// Creating a middleware pipeline with IApplicationBuilder
/// The ASP.NET Core request pipeline consists of a sequence of request delegates, called one after the other, as this diagram shows (the thread of execution follows the black arrows):
/// Request processing pattern showing a request arriving, processing through three middlewares, and the response leaving the application.
/// Each middleware runs its logic and hands off the request to the next middleware at the next() statement.
/// After the third middleware processes the request, 
/// the request passes back through the prior two middlewares in reverse order for additional processing after their next() statements before leaving the application as a response to the client.
/// Each delegate can perform operations before and after the next delegate. 
/// A delegate can also decide to not pass a request to the next delegate, which is called short-circuiting the request pipeline. 
/// Short-circuiting is often desirable because it avoids unnecessary work.
/// For example, the static file middleware can return a request for a static file and short-circuit the rest of 
/// the pipeline.Exception-handling delegates need to be called early in the pipeline, so they can catch exceptions that occur in later stages of the pipeline.
/// The simplest possible ASP.NET Core app sets up a single request delegate that handles all requests. 
/// This case doesn't include an actual request pipeline. Instead, a single anonymous function is called in response to every HTTP request
/// 
///  References: https://docs.microsoft.com/en-us/aspnet/core/fundamentals/middleware/?view=aspnetcore-2.1&tabs=aspnetcore2x
/// </summary>
namespace BdoPartner.Plans.Web.Common.Middlewares.RequestCulture
{
    /// <summary>
    /// Work for get culture code from each request's header. Coproate with One UI Angular CLI project. 
    /// https://docs.microsoft.com/en-us/aspnet/core/fundamentals/middleware/?view=aspnetcore-2.1&tabs=aspnetcore2x
    /// Note: The single page application will inject a custom header information for language code.
    /// such as "currentLanguage" = "en-ca".
    /// When requestion send to server side, system will try to catch this header information and based on it setup the current thread's culture.
    /// Work for multiple languages support.
    /// </summary>
    public class RequestCultureMiddleware
    {

        private readonly RequestDelegate _next;

        public RequestCultureMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public Task InvokeAsync(HttpContext context)
        {
            string cultureQuery = context.Request.Headers["currentLanguage"];

            if (string.IsNullOrWhiteSpace(cultureQuery)) {
                cultureQuery = "en-us";
            }

            var culture = new CultureInfo(cultureQuery);

            CultureInfo.CurrentCulture = culture;
            CultureInfo.CurrentUICulture = culture;
            
            // Call the next delegate/middleware in the pipeline
            return this._next(context);
        }
    }
}
