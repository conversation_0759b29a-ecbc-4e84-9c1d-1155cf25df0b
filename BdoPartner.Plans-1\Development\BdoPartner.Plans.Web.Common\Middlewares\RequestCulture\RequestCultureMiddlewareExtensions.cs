﻿using Microsoft.AspNetCore.Builder;
using System;
using System.Collections.Generic;
using System.Text;

namespace BdoPartner.Plans.Web.Common.Middlewares.RequestCulture
{
    /// <summary>
    /// Reference: https://docs.microsoft.com/en-us/aspnet/core/fundamentals/middleware/?view=aspnetcore-2.1&tabs=aspnetcore2x
    /// </summary>
    public static class RequestCultureMiddlewareExtensions
    {
        /// <summary>
        ///  exposes the request culture middle ware. work for localization.
        /// </summary>
        /// <param name="builder"></param>
        /// <returns></returns>
        public static IApplicationBuilder UseRequestCulture(
            this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<RequestCultureMiddleware>();
        }
    }
}
