﻿using IdentityModel;
using Microsoft.AspNetCore.SignalR;
using System;
using System.Linq;

namespace BdoPartner.Plans.Web.Common
{
    /// <summary>
    ///  Work for Signalr and Identity server logon user.
    ///  Signalr need to send message based on logon user Id.
    ///  Reference to field "Id" in table dbo.User. It is primary key.
    ///  
    ///  Reference: https://stackoverflow.com/questions/19522103/signalr-sending-a-message-to-a-specific-user-using-iuseridprovider-new-2-0/21355406#21355406
    /// </summary>
    public class SignalRUserIdProvider: IUserIdProvider
    {
        public string GetUserId(HubConnectionContext request)
        {
            string userId = string.Empty;             
            var user = request.User;

            //
            // If there is user login from Identity Server.
            //
            if (user != null && user.Identity != null && user.Identity.IsAuthenticated)
            {
                //var claims = (from c in user.Claims select new { c.Type, c.Value }).ToList();
                // result.UserName = claims.FirstOrDefault(c => c.Type == JwtClaimTypes.Name).Value;
                //result.Email = claims.FirstOrDefault(c => c.Type == JwtClaimTypes.Email).Value;
                ////result.FirstName = claims.FirstOrDefault(c => c.Type == JwtClaimTypes.GivenName).Value;
                ////result.LastName = claims.FirstOrDefault(c => c.Type == JwtClaimTypes.FamilyName).Value;
                //userId = claims.FirstOrDefault(c => c.Type == "sub").Value;
                //result.Language = claims.FirstOrDefault(c => c.Type == JwtClaimTypes.Locale).Value;
                //result.Roles = claims.FirstOrDefault(c => c.Type == JwtClaimTypes.Role).Value;
                //result.IsAuthenticated = true;
                userId = user.Identity.Name;
            }

            return userId;
        }
    }
}
