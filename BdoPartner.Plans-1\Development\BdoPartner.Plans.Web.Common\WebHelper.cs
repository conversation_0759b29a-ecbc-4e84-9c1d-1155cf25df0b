﻿using Microsoft.AspNetCore.Mvc.ModelBinding;
using System;
using System.Collections.Generic;
using System.Linq;
using Azure.Core;
using Azure.Identity;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Configuration.AzureAppConfiguration;
using Microsoft.Extensions.Hosting.Internal;

namespace BdoPartner.Plans.Web.Common
{
    public static class WebHelper
    {
        /// <summary>
        ///  Collect errors from ModelState.
        /// </summary>
        /// <param name="modelState"></param>
        /// <returns></returns>
        public static Dictionary<string, string> GetModelStateErrors(this ModelStateDictionary modelState)
        {
            if (modelState != null)
            {
                var errors = new Dictionary<string, string>();
                modelState.Where(k => k.Value.Errors.Count > 0).ToList().ForEach(i =>
                {
                    var er = string.Join(" ", i.Value.Errors.Select(e => e.ErrorMessage).ToArray());
                    errors.Add(i.Key, er);
                });
                return errors;
            }
            else
                return null;
        }

        /// <summary>
        ///  Helper to create TokenCredential for localhost access Azure Key Vaults secrets through Azure App Configuration.
        ///  Reference: https://jan-v.nl/post/2021/using-key-vault-with-azure-app-configuration/
        ///  
        /// </summary>
        /// <param name="defaultTenantId">
        ///  Get TenantId from Azure AD registed Application (App registration) Dashboard 
        ///  item called "Directory (tenant) ID". 
        ///  For example, the TenantId of "LocalDebugSolutionTemplateWebAPI" in "BDO IT Solutions Innovation" Azure AD.
        ///  It is guid data format.
        /// </param>
        /// <param name="isLocalDebug">
        ///   If value = true, work for developer debugging web api portal in local machine. Access through https://localhost.
        ///   If value = false, work for web api portal which has deployed in Azure App Configuration service. 
        /// </param>
        /// <returns></returns>
        public static TokenCredential GetAzureCredentials(string defaultTenantId, bool isLocalDebug)
        {
            return new DefaultAzureCredential(
                new DefaultAzureCredentialOptions
                {
                    // Prevent deployed instances from trying things that don't work and generally take too long
                    ExcludeInteractiveBrowserCredential = !isLocalDebug,
                    ExcludeVisualStudioCodeCredential = !isLocalDebug,
                    ExcludeVisualStudioCredential = !isLocalDebug,
                    ExcludeSharedTokenCacheCredential = !isLocalDebug,
                    ExcludeAzureCliCredential = !isLocalDebug,
                    ExcludeManagedIdentityCredential = false,
                    Retry =
                    {
				        // Reduce retries and timeouts to get faster failures
				        MaxRetries = 2,
                        NetworkTimeout = TimeSpan.FromSeconds(5),
                        MaxDelay = TimeSpan.FromSeconds(5)
                    },

                    // this helps devs use the right tenant
                    InteractiveBrowserTenantId = defaultTenantId,
                    SharedTokenCacheTenantId = defaultTenantId,
                    VisualStudioCodeTenantId = defaultTenantId,
                    VisualStudioTenantId = defaultTenantId
                }
            );
        }

        public static void AppConfigurationRegistration(HostBuilderContext context, ref IConfigurationBuilder config, string preSetLabelFilter)
        {

            //
            // Here we support three approaches for config setting setup and consume.
            // Wiki: https://dev.azure.com/BDOInternal/SPARQ/_wiki/wikis/BEMP.wiki/118/Setup-App-Config-Settings-with-Multiple-Ways 
            //
            var env = context.HostingEnvironment;

            //
            // System still needs to use appsettings.json to load some of basic settings for startup.
            // For example, even using Azure App Configuration, it still needs to use appsettings.json to contain Azure App Configuraiton service's endpoint info.
            //                    
            if (env.IsDevelopment())
            {
                //
                // Load different appsetting.json settings based on currnt development environment setup.
                // Corporate with different developers' local development environment.
                // Note: the next config settings will override the previous one.
                config.AddJsonFile("appsettings.json", optional: true, reloadOnChange: true)
                    .AddJsonFile($"appsettings.{env.EnvironmentName}.json", optional: true, reloadOnChange: true)
                    .AddJsonFile($"appsettings.{System.Environment.MachineName}.json", optional: true, reloadOnChange: true)
                    .AddJsonFile($"appsettings.{System.Environment.UserName}.json", optional: true, reloadOnChange: true);
            }
            else if (env.IsEnvironment("Local")) {
                config.AddJsonFile("appsettings.json", optional: true, reloadOnChange: true)
                    .AddJsonFile($"appsettings.{env.EnvironmentName}.json", optional: true, reloadOnChange: true)
                    .AddJsonFile($"appsettings.{System.Environment.MachineName}.json", optional: true, reloadOnChange: true)
                    .AddJsonFile($"appsettings.{System.Environment.UserName}.json", optional: true, reloadOnChange: true);
            }
            else
            {
                //
                // When deploy application in Azure App Service, system only use two appsettings.json and appsettings.[environment].json files
                // Note: In Azure App Services, most config settings will be kept in Azure App Configuration,
                // and system only keeps the App Configuration service endpoint in appsetings.json, section called "AppConfig".
                // 
                config.AddJsonFile("appsettings.json", optional: true, reloadOnChange: true)
                    .AddJsonFile($"appsettings.{env.EnvironmentName}.json", optional: true, reloadOnChange: true);
            }

            //
            // For QA, UAT, Production deployment, we are using Azure App Configuration.
            //
            // Reference:
            // https://docs.microsoft.com/en-us/azure/azure-app-configuration/quickstart-aspnet-core-app?tabs=core5x
            //
            // Corporate with envionment setup "ASPNETCORE_ENVIRONMENT".
            //
            // Note: Corporate with "Manage User Secrets" option of current project.
            // Right click Web Project, select menu "Manage User Secrets".
            // Settings such as section called "ConnectionStrings:DatabaseConnection", "IdentityServer" are moved into Azure App Configuration.  
            //

            var settings = config.Build();

            // 
            // Section called "AppConfig" has values in appsettings.[current environment].json.
            // It means system is using Azure App Configuration service to store config settings.
            //
            if (!string.IsNullOrEmpty(settings["AppConfig:IsConnectedWithConnectionString"]))
            {
                //
                // Define "label" based on current project name and environment variable. Work for filter settings in Azure App Configuration.
                // Note: When import appsettings.Production.json into Azure App Configuration, label define needs to corporate code here.
                // Wiki: https://dev.azure.com/BDOInternal/SPARQ/_wiki/wikis/BEMP.wiki/123/Create-Azure-App-Configuration
                //
                var labelFilter = preSetLabelFilter + env.EnvironmentName;

                config.AddAzureAppConfiguration(options =>
                {
                    if (bool.Parse(settings["AppConfig:IsConnectedWithConnectionString"]))
                    {
                        //
                        // Note: connection string value stored in "Manage User Secrets" for current long user in local machine.
                        //
                        options.Connect(settings.GetConnectionString("AppConfig"));
                    }
                    else
                    {
                        //
                        // ManagedIdentityCredential() works for Web Portal which already deployed in Azure App Service.
                        //
                        // https://github.com/MicrosoftDocs/azure-docs/blob/master/articles/azure-app-configuration/howto-integrate-azure-managed-service-identity.md
                        //
                        // 
                        options.Connect(new Uri(settings["AppConfig:Endpoint"]), new ManagedIdentityCredential());
                    }

                    options.Select(KeyFilter.Any, LabelFilter.Null)
                    //
                    // Filter Azure App Configuration settings by environment variable.
                    //
                    .Select(KeyFilter.Any, labelFilter)
                    //
                    // Indeicates that all settings should be refreshed when the giving key has changed.
                    //
                    .ConfigureRefresh(refresh =>
                    {
                        refresh.Register(key: "App:Sentinel", label: LabelFilter.Null, refreshAll: true).SetCacheExpiration(TimeSpan.FromSeconds(10));
                    })
                    //
                    // It is about Azure Key Vaults settting.
                    // Work for Sql Server Database always encryption process and keep sensitive config settings in Azure Key Valuts.
                    //
                    .ConfigureKeyVault(kv =>
                    {
                        if (env.IsDevelopment())
                        {
                            //
                            // Work for localhost access Azure Key Vaults service.
                            // Work for local development.
                            //
                            var tenantId = settings["App:AzureKeyVaultConfig:TenantId"];
                            //
                            // Note: When web portal running in development environment, pass "true" to method "GetAzureCredentials".
                            // When web portal running in Azure App Service, pass "false" to method "GetAzureCredentials".
                            //
                            var credentials = WebHelper.GetAzureCredentials(tenantId, env.IsDevelopment());
                            kv.SetCredential(credentials);

                        }
                        else
                        {
                            //
                            // Work for portal hosted in Azure App Service.
                            // In Azure App Service, portal access Azure Key Vaults with its identity which already granted role/permissions in Azure Key Vaults Access control (IAM).
                            //
                            kv.SetCredential(new DefaultAzureCredential());
                        }
                    });
                });
            }

            config.AddEnvironmentVariables();
        }

    }
}
