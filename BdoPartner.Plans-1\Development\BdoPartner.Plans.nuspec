﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2010/07/nuspec.xsd">
  <metadata>
    <id>BdoPartner.Plans</id>
    <version>1.0.6</version>
    <title>.Net 5 Web API solution template</title>
    <authors>BDO LLP Canada</authors>
    <requireLicenseAcceptance>false</requireLicenseAcceptance>
    <projectUrl>https://dev.azure.com/BDOInternal/SPARQ</projectUrl>
    <description>.Net 5 Web API n-tier solution template with single sigon feature.</description>
    <summary>This package is used to create .Net 5 n-tier web application solution, included one Identity Server 4 end point, one Resource Web API, one Razor Page portal and one Angular Single Page Application.</summary>
    <tags>.Net 5 Solution Template</tags>
    <packageTypes>
      <packageType name="Template" />
    </packageTypes>
  </metadata>
</package>