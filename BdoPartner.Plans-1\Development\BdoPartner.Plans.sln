﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.13.35825.156
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "3. Database Access Layer", "3. Database Access Layer", "{D3495B1A-6BB1-4ABC-88AA-6ABCFA464CC0}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "5. Business Services Layer", "5. Business Services Layer", "{2A7135A6-C662-4DBD-BBEB-A653B98A7547}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "2. Entity Data Model Layer", "2. Entity Data Model Layer", "{C4454772-379C-4C69-A7D8-33EE0C8D250D}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "7. Web API Layer", "7. Web API Layer", "{A7DB9456-6CFF-404D-97CF-B49FF21529CD}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BdoPartner.Plans.Model.DTO", "BdoPartner.Plans.Model.DTO\BdoPartner.Plans.Model.DTO.csproj", "{3B496934-4D07-4FDE-B42B-A06973B6B29E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BdoPartner.Plans.Model.Entity", "BdoPartner.Plans.Model.Entity\BdoPartner.Plans.Model.Entity.csproj", "{F6146A22-FB6A-4C0B-9A26-B568B1A1DB7F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BdoPartner.Plans.Model.Mapper", "BdoPartner.Plans.Model.Mapper\BdoPartner.Plans.Model.Mapper.csproj", "{F2399D3C-5127-49E0-983F-882DFA119BD4}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BdoPartner.Plans.Web.Common", "BdoPartner.Plans.Web.Common\BdoPartner.Plans.Web.Common.csproj", "{DB96769B-E262-4AC6-83AE-02637228C40E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BdoPartner.Plans.Web.API", "BdoPartner.Plans.Web.API\BdoPartner.Plans.Web.API.csproj", "{596D501B-2256-473E-827F-F77AA0BA58EA}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BdoPartner.Plans.Business", "BdoPartner.Plans.Business\BdoPartner.Plans.Business.csproj", "{C6C66CB3-2316-4DB4-8C5A-6689ED956D3E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BdoPartner.Plans.Business.Interface", "BdoPartner.Plans.Business.Interface\BdoPartner.Plans.Business.Interface.csproj", "{F8DC8E3B-FC1D-442D-8140-DE48C348D4F6}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BdoPartner.Plans.Business.Test", "BdoPartner.Plans.Business.Test\BdoPartner.Plans.Business.Test.csproj", "{CB9A10B0-6451-47AF-BF9B-D3FF020950B2}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BdoPartner.Plans.Common", "BdoPartner.Plans.Common\BdoPartner.Plans.Common.csproj", "{1AD6BC0A-C1C6-47A3-8D1B-3949FBADE957}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BdoPartner.Plans.DataAccess", "BdoPartner.Plans.DataAccess\BdoPartner.Plans.DataAccess.csproj", "{8A77CA52-F7B3-4744-B65A-6767D5B77172}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BdoPartner.Plans.DataAccess.Common", "BdoPartner.Plans.DataAccess.Common\BdoPartner.Plans.DataAccess.Common.csproj", "{5C33E392-B151-4ADA-BE45-223872A51EC2}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "1. Generic Common Library Layer", "1. Generic Common Library Layer", "{8D761359-369C-4CF3-82DE-BB94A94E2C64}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "4. Data Transfer Layer", "4. Data Transfer Layer", "{0574CD66-DA51-4994-8B32-B3B275F3811A}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "6. Web Common Library Layer", "6. Web Common Library Layer", "{2068F470-4C90-4D02-8F66-58F36925B7BE}"
EndProject
Project("{00D1A9C2-B5F0-4AF3-8072-F6C62B433612}") = "BdoPartner.Plans.Database", "BdoPartner.Plans.Database\BdoPartner.Plans.Database.sqlproj", "{996C1B0A-31A4-420A-9EAE-66648DC0BDD9}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{3B496934-4D07-4FDE-B42B-A06973B6B29E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3B496934-4D07-4FDE-B42B-A06973B6B29E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3B496934-4D07-4FDE-B42B-A06973B6B29E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3B496934-4D07-4FDE-B42B-A06973B6B29E}.Release|Any CPU.Build.0 = Release|Any CPU
		{F6146A22-FB6A-4C0B-9A26-B568B1A1DB7F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F6146A22-FB6A-4C0B-9A26-B568B1A1DB7F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F6146A22-FB6A-4C0B-9A26-B568B1A1DB7F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F6146A22-FB6A-4C0B-9A26-B568B1A1DB7F}.Release|Any CPU.Build.0 = Release|Any CPU
		{F2399D3C-5127-49E0-983F-882DFA119BD4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F2399D3C-5127-49E0-983F-882DFA119BD4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F2399D3C-5127-49E0-983F-882DFA119BD4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F2399D3C-5127-49E0-983F-882DFA119BD4}.Release|Any CPU.Build.0 = Release|Any CPU
		{DB96769B-E262-4AC6-83AE-02637228C40E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DB96769B-E262-4AC6-83AE-02637228C40E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DB96769B-E262-4AC6-83AE-02637228C40E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DB96769B-E262-4AC6-83AE-02637228C40E}.Release|Any CPU.Build.0 = Release|Any CPU
		{596D501B-2256-473E-827F-F77AA0BA58EA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{596D501B-2256-473E-827F-F77AA0BA58EA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{596D501B-2256-473E-827F-F77AA0BA58EA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{596D501B-2256-473E-827F-F77AA0BA58EA}.Release|Any CPU.Build.0 = Release|Any CPU
		{C6C66CB3-2316-4DB4-8C5A-6689ED956D3E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C6C66CB3-2316-4DB4-8C5A-6689ED956D3E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C6C66CB3-2316-4DB4-8C5A-6689ED956D3E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C6C66CB3-2316-4DB4-8C5A-6689ED956D3E}.Release|Any CPU.Build.0 = Release|Any CPU
		{F8DC8E3B-FC1D-442D-8140-DE48C348D4F6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F8DC8E3B-FC1D-442D-8140-DE48C348D4F6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F8DC8E3B-FC1D-442D-8140-DE48C348D4F6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F8DC8E3B-FC1D-442D-8140-DE48C348D4F6}.Release|Any CPU.Build.0 = Release|Any CPU
		{CB9A10B0-6451-47AF-BF9B-D3FF020950B2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CB9A10B0-6451-47AF-BF9B-D3FF020950B2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CB9A10B0-6451-47AF-BF9B-D3FF020950B2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CB9A10B0-6451-47AF-BF9B-D3FF020950B2}.Release|Any CPU.Build.0 = Release|Any CPU
		{1AD6BC0A-C1C6-47A3-8D1B-3949FBADE957}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1AD6BC0A-C1C6-47A3-8D1B-3949FBADE957}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1AD6BC0A-C1C6-47A3-8D1B-3949FBADE957}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1AD6BC0A-C1C6-47A3-8D1B-3949FBADE957}.Release|Any CPU.Build.0 = Release|Any CPU
		{8A77CA52-F7B3-4744-B65A-6767D5B77172}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8A77CA52-F7B3-4744-B65A-6767D5B77172}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8A77CA52-F7B3-4744-B65A-6767D5B77172}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8A77CA52-F7B3-4744-B65A-6767D5B77172}.Release|Any CPU.Build.0 = Release|Any CPU
		{5C33E392-B151-4ADA-BE45-223872A51EC2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5C33E392-B151-4ADA-BE45-223872A51EC2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5C33E392-B151-4ADA-BE45-223872A51EC2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5C33E392-B151-4ADA-BE45-223872A51EC2}.Release|Any CPU.Build.0 = Release|Any CPU
		{996C1B0A-31A4-420A-9EAE-66648DC0BDD9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{996C1B0A-31A4-420A-9EAE-66648DC0BDD9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{996C1B0A-31A4-420A-9EAE-66648DC0BDD9}.Debug|Any CPU.Deploy.0 = Debug|Any CPU
		{996C1B0A-31A4-420A-9EAE-66648DC0BDD9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{996C1B0A-31A4-420A-9EAE-66648DC0BDD9}.Release|Any CPU.Build.0 = Release|Any CPU
		{996C1B0A-31A4-420A-9EAE-66648DC0BDD9}.Release|Any CPU.Deploy.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{3B496934-4D07-4FDE-B42B-A06973B6B29E} = {0574CD66-DA51-4994-8B32-B3B275F3811A}
		{F6146A22-FB6A-4C0B-9A26-B568B1A1DB7F} = {C4454772-379C-4C69-A7D8-33EE0C8D250D}
		{F2399D3C-5127-49E0-983F-882DFA119BD4} = {0574CD66-DA51-4994-8B32-B3B275F3811A}
		{DB96769B-E262-4AC6-83AE-02637228C40E} = {2068F470-4C90-4D02-8F66-58F36925B7BE}
		{596D501B-2256-473E-827F-F77AA0BA58EA} = {A7DB9456-6CFF-404D-97CF-B49FF21529CD}
		{C6C66CB3-2316-4DB4-8C5A-6689ED956D3E} = {2A7135A6-C662-4DBD-BBEB-A653B98A7547}
		{F8DC8E3B-FC1D-442D-8140-DE48C348D4F6} = {2A7135A6-C662-4DBD-BBEB-A653B98A7547}
		{CB9A10B0-6451-47AF-BF9B-D3FF020950B2} = {2A7135A6-C662-4DBD-BBEB-A653B98A7547}
		{1AD6BC0A-C1C6-47A3-8D1B-3949FBADE957} = {8D761359-369C-4CF3-82DE-BB94A94E2C64}
		{8A77CA52-F7B3-4744-B65A-6767D5B77172} = {D3495B1A-6BB1-4ABC-88AA-6ABCFA464CC0}
		{5C33E392-B151-4ADA-BE45-223872A51EC2} = {D3495B1A-6BB1-4ABC-88AA-6ABCFA464CC0}
		{996C1B0A-31A4-420A-9EAE-66648DC0BDD9} = {D3495B1A-6BB1-4ABC-88AA-6ABCFA464CC0}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {0DCAC53C-1C8B-4648-940A-3474E61F65B5}
	EndGlobalSection
EndGlobal
