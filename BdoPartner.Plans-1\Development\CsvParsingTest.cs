using System;
using System.Collections.Generic;
using System.Text;

namespace CsvParsingTest
{
    /// <summary>
    /// Simple test program to verify CSV parsing functionality
    /// This replicates the ParseCsvLine method from PartnerReviewerUploadService
    /// </summary>
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("CSV Parsing Test Program");
            Console.WriteLine("========================");

            // Test cases
            var testCases = new[]
            {
                new { Name = "Simple fields", Input = "11,<PERSON>,<PERSON>,,85,<PERSON><PERSON><PERSON><PERSON>,89,<PERSON>" },
                new { Name = "Quoted fields with commas", Input = "11,\"<PERSON><PERSON>, <PERSON>\",N,,85,\"<PERSON><PERSON><PERSON>, <PERSON>yl<PERSON><PERSON>\",89,\"<PERSON><PERSON>, <PERSON>\"" },
                new { Name = "Mixed quoted and unquoted", Input = "12,\"<PERSON>, <PERSON>\",N,,125,\"Lapointe, Christian\",342,\"Kara<PERSON><PERSON>, <PERSON>\"" },
                new { Name = "Empty quoted fields", Input = "85,\"<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>\",Y,ELT,\"\",\"\",\"\",\"\"" },
                new { Name = "Escaped quotes", Input = "11,\"Meek \"\"Steve\"\" Smith\",N,,85,\"Guindon, Sylvain\",89,\"Rodie, Scott\"" },
                new { Name = "Header row", Input = "EmployeeID,EmployeeName,Exempt,LeadershipRole,PrimaryReviewerID,PrimaryReviewerName,SecondaryReviewerID,SecondaryReviewerName" },
                new { Name = "Whitespace handling", Input = " 11 , \"Meek, Stephen\" , N ,  , 85 , \"Guindon, Sylvain\" , 89 , \"Rodie, Scott\" " }
            };

            foreach (var testCase in testCases)
            {
                Console.WriteLine($"\nTest: {testCase.Name}");
                Console.WriteLine($"Input: {testCase.Input}");
                
                try
                {
                    var result = ParseCsvLine(testCase.Input);
                    Console.WriteLine($"Output: [{string.Join("] [", result)}]");
                    Console.WriteLine($"Field count: {result.Length}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error: {ex.Message}");
                }
            }

            Console.WriteLine("\nPress any key to exit...");
            Console.ReadKey();
        }

        /// <summary>
        /// Parse a CSV line handling quoted fields that may contain commas
        /// This is a copy of the method from PartnerReviewerUploadService
        /// </summary>
        /// <param name="line">CSV line to parse</param>
        /// <returns>Array of field values</returns>
        private static string[] ParseCsvLine(string line)
        {
            var fields = new List<string>();
            var currentField = new StringBuilder();
            bool inQuotes = false;
            bool fieldStarted = false;

            for (int i = 0; i < line.Length; i++)
            {
                char c = line[i];

                if (c == '"')
                {
                    if (!fieldStarted)
                    {
                        // Starting a quoted field
                        inQuotes = true;
                        fieldStarted = true;
                    }
                    else if (inQuotes)
                    {
                        // Check if this is an escaped quote (double quote)
                        if (i + 1 < line.Length && line[i + 1] == '"')
                        {
                            // Escaped quote - add one quote to the field
                            currentField.Append('"');
                            i++; // Skip the next quote
                        }
                        else
                        {
                            // End of quoted field
                            inQuotes = false;
                        }
                    }
                    else
                    {
                        // Quote in the middle of unquoted field - treat as regular character
                        currentField.Append(c);
                    }
                }
                else if (c == ',' && !inQuotes)
                {
                    // Field separator - end current field
                    fields.Add(currentField.ToString().Trim());
                    currentField.Clear();
                    fieldStarted = false;
                }
                else
                {
                    // Regular character
                    currentField.Append(c);
                    if (!fieldStarted)
                        fieldStarted = true;
                }
            }

            // Add the last field
            fields.Add(currentField.ToString().Trim());

            return fields.ToArray();
        }
    }
}
