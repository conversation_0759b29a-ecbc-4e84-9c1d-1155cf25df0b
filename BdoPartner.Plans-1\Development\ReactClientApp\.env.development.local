/**
 * Define current running React application's environment. Value = "dev" or "prod" or "qa" or "uat".
 * Corporate with core/config/appConfig.jsx. 
 * Reference: https://www.pluralsight.com/guides/how-to-store-and-read-configuration-files-using-react 
 * https://stackoverflow.com/questions/49579028/adding-an-env-file-to-react-project
 
 * Files priority:
 * npm start: .env.development.local, .env.development, .env.local, .env
 * npm run build: .env.production.local, .env.production, .env.local, .env
 * npm test: .env.test.local, .env.test, .env (note .env.local is missing)

 * accepted value as dev_cors_root, dev_cors, dev_nocors
 *
 * Note: When switch REACT_APP_ENV setting, developer needs to check setting called "homepage" in package.json. value in "homepage" item should be same as value of "basePath" in the defined appConfig setting in appConfig.js
 * 
 * Below settings corporate with core/config/appConfig.js
 * Get config settings from web api end point defined in "REACT_APP_CONFIG_API"

 * Work for scenario of running react app with "npm start".
 * As for dev_cors, in local development, react app is hosting in seperated domain (or different port number) compare to web api.
 * for exmaple, react app is hosting in "http://localhost:3000", and web api is hosting in "https://localhost:5001", and Identity Server is hosting in "https://localhost:5000"
 * 
 * Note: "npm run build" by default using .env.production.local as environment file.
 *
 * Get config settings from web api end point defined in "REACT_APP_CONFIG_API"
 * Setings stay in web api's appsettings.[environment].json file, section "Environment".
 **/
REACT_APP_ENV = 'dev_cors'
REACT_APP_CONFIG_API = 'https://localhost:5001/api/Settings/GetIDSSettings'