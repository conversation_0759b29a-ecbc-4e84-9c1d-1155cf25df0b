/**
 * Define current running React application's environment. Value = "dev" or "prod" or "qa" or "uat".
 * Corporate with core/config/appConfig.jsx. 
 * Reference: https://www.pluralsight.com/guides/how-to-store-and-read-configuration-files-using-react 
 * https://stackoverflow.com/questions/49579028/adding-an-env-file-to-react-project
 *
 * Files priority:
 * npm start: .env.development.local, .env.development, .env.local, .env
 * npm run build: .env.production.local, .env.production, .env.local, .env
 * npm test: .env.test.local, .env.test, .env (note .env.local is missing)
 *
 * accepted value as dev_cors_root, dev_cors, dev_nocors, prod_nocors
 *
 * Note: When switch REACT_APP_ENV setting, developer needs to check setting called "homepage" in package.json. value in "homepage" item should be same as value of "basePath" in the defined appConfig setting in appConfig.js
 *
 * Work for scenario of running react app with "npm run build".
 * As for prod_nocors, work for scenario of hosting react app under web api domain's sub path.
 *
 * Note: "npm start" by default using .env.development.local as environment file.

 * Get config settings from web api end point defined in "REACT_APP_CONFIG_API"
 * Setings stay in web api's appsettings.[environment].json file, section "Environment".
 *
 **/
REACT_APP_ENV = 'prod_nocors'
REACT_APP_CONFIG_API = '/api/Settings/GetIDSSettings'