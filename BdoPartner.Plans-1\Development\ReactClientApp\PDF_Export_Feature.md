# PDF Export Feature for Partner Planning Tool

## Overview
The PDF export feature has been implemented in the Partner Planning Tool using Survey.js's built-in PDF export functionality. This allows users to export their completed or partially completed partner plan forms as PDF documents.

## Implementation Details

### Dependencies
- **survey-pdf**: Version 2.2.2 (already included in package.json)
- **survey-core**: Version 2.2.2 (already included)
- **survey-react-ui**: Version 2.2.2 (already included)

### Key Features
1. **Export Button**: Located in the header of the Partner Planning Tool form
2. **BDO Branding**: PDF maintains the same BDO red theme (#ED1A3B) as the web form
3. **Partner Information**: PDF includes partner details in the header (name, service line, location)
4. **Current Form Data**: Exports the form with all currently filled answers
5. **Professional Formatting**: A4 format with proper margins and typography

### Technical Implementation

#### Location
- File: `Development/ReactClientApp/src/components/questionnaire/PartnerPlanQuestionnaire.jsx`
- Function: `handleExportToPDF`

#### Key Components
1. **Import Statement**: Added `SurveyPDF` import from 'survey-pdf'
2. **Export Function**: `handleExportToPDF` callback function
3. **Button Handler**: Connected to the existing "Export to PDF" button

#### PDF Configuration
```javascript
const options = {
  fontSize: 11,
  margins: { left: 15, right: 15, top: 20, bot: 20 },
  format: 'a4',
  haveCommercialLicense: false,
  headerHeight: 60,
  footerHeight: 30
};
```

#### Custom Header
The PDF includes a custom header with:
- BDO Partner Planning Tool title
- Partner name
- Service line
- Location
- Generation date

### Usage
1. User fills out the partner planning form
2. User has two PDF export options in the header:
   - **"Export to PDF"** - Uses Survey.js built-in PDF export (primary method)
   - **"Print/PDF"** - Uses browser's print functionality as alternative (fallback method)
3. System generates PDF with current form data
4. PDF is automatically downloaded with filename format: `Partner_Plan_[PartnerName]_[Date].pdf`

### Export Methods

#### Primary Method: Survey.js PDF Export
- Uses Survey.js's built-in PDF generation
- Maintains survey formatting and styling
- Includes BDO branding and colors
- Automatic file download

#### Alternative Method: Browser Print
- Opens a formatted print dialog
- User can choose "Save as PDF" in browser
- Works when Survey.js PDF export fails
- Compatible with all browsers

### Error Handling
- Validates that survey model is loaded before export
- Validates survey JSON structure before PDF generation
- Cleans and sanitizes survey data to prevent undefined property errors
- Shows loading message during PDF generation
- Displays success/error messages using the existing message service
- Handles special characters in partner names for filename generation
- Provides specific error messages for common issues (e.g., indexOf errors)
- Filters out invalid survey elements and pages

### User Experience
- Loading indicator during PDF generation
- Success toast notification upon completion
- Error handling with user-friendly messages
- Maintains form state during export process

## Testing
The feature has been tested with:
- Build compilation (successful)
- Import validation
- Function integration with existing codebase
- Error handling for malformed survey data
- Data sanitization and cleaning

## Troubleshooting

### Common Issues and Solutions

1. **"Cannot read properties of undefined (reading 'indexOf')" Error**
   - **Cause**: Survey data contains undefined or null values that Survey.js PDF export cannot handle
   - **Solution**: The implementation now includes data cleaning and validation to prevent this error
   - **Prevention**: Survey data is sanitized before being passed to SurveyPDF

2. **"Invalid survey structure for PDF export" Error**
   - **Cause**: Survey JSON is missing required properties (pages, elements)
   - **Solution**: The implementation validates and reconstructs the survey structure
   - **Prevention**: Survey JSON is cleaned and validated before PDF generation

3. **PDF Generation Fails Silently**
   - **Cause**: Browser may block PDF downloads or have insufficient memory
   - **Solution**: Check browser console for errors and ensure pop-up blockers allow downloads
   - **Prevention**: User receives loading and success/error messages

4. **Special Characters in Filename**
   - **Cause**: Partner names with special characters can cause download issues
   - **Solution**: Partner names are sanitized for safe filename generation
   - **Prevention**: Only alphanumeric characters and underscores are used in filenames

5. **Success Message But No File Downloaded**
   - **Cause**: Browser may block downloads, or Survey.js PDF generation failed silently
   - **Solution**:
     - Check browser's download folder and download history
     - Check browser console for errors (F12 → Console tab)
     - Try the alternative "Print/PDF" button
     - Ensure pop-up blockers are disabled for the site
   - **Prevention**: The implementation now includes fallback PDF generation and detailed logging

### Debugging Steps

If PDF export is not working:

1. **Open Browser Console** (F12 → Console tab)
2. **Click Export to PDF** and check for error messages
3. **Look for these log messages**:
   - "Attempting to generate PDF with filename: ..."
   - "PDF save method completed successfully"
   - "Survey data being exported: ..."
4. **Check Browser Downloads**:
   - Look in browser's download folder
   - Check download history (Ctrl+Shift+Delete → Downloads)
5. **Try Alternative Method**:
   - Use the "Print/PDF" button
   - In print dialog, select "Save as PDF"

## Future Enhancements
Potential improvements could include:
1. Commercial license integration for removing Survey.js watermark
2. Custom PDF templates with more BDO branding
3. Email integration to send PDF directly
4. Batch export for administrators
5. PDF password protection options

## Notes
- The feature uses Survey.js's built-in PDF export which may include a watermark in the free version
- PDF generation happens client-side, no server-side processing required
- Compatible with all modern browsers that support PDF generation
