{"name": "reactclientapp", "version": "0.1.0", "private": true, "homepage": "/pps", "dependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "ajv": "^8.17.1", "axios": "^1.10.0", "i18next": "^23.16.8", "i18next-browser-languagedetector": "^8.2.0", "i18next-http-backend": "^3.0.2", "oidc-client": "^1.11.5", "primeflex": "^4.0.0", "primeicons": "^7.0.0", "primereact": "^10.9.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-i18next": "^14.1.3", "react-redux": "^9.2.0", "react-router-dom": "^7.6.3", "react-scripts": "^5.0.1", "react-transition-group": "^4.4.2", "redux": "^5.0.1", "redux-logger": "^3.0.6", "redux-persist": "^6.0.0", "redux-thunk": "^3.1.0", "rxjs": "^7.2.0", "sass": "^1.89.2", "survey-core": "^2.2.2", "survey-creator-core": "^2.2.2", "survey-creator-react": "^2.2.2", "survey-pdf": "^2.2.2", "survey-react-ui": "^2.2.2", "uuid": "^11.1.0", "web-vitals": "^5.0.3"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "build:local_nocors": "dotenv -e .env.local.nocors npm run build", "build:dev": "dotenv -e .env.development npm run build", "build:prod": "dotenv -e .env.production npm run build", "build:qa": "dotenv -e .env.qa npm run build"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"typescript": "^4.9.5"}}