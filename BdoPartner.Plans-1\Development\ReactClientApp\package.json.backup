{"name": "reactclientapp", "version": "0.1.0", "private": true, "homepage": "/react", "dependencies": {"@testing-library/jest-dom": "^5.14.1", "@testing-library/react": "^11.2.7", "@testing-library/user-event": "^12.8.3", "axios": "^0.21.1", "i18next": "^20.3.5", "i18next-browser-languagedetector": "^6.1.2", "i18next-http-backend": "^1.3.0", "node-sass": "^6.0.1", "oidc-client": "^1.11.5", "primeflex": "^3.0.1", "primeicons": "^4.1.0", "primereact": "^6.5.0", "react": "^17.0.2", "react-dom": "^17.0.2", "react-i18next": "^11.11.4", "react-redux": "^7.2.4", "react-router-dom": "^5.2.0", "react-scripts": "4.0.3", "react-transition-group": "^4.4.2", "redux": "^4.1.0", "redux-logger": "^3.0.6", "redux-persist": "^6.0.0", "redux-thunk": "^2.3.0", "rxjs": "^7.2.0", "survey-creator": "^1.8.75", "survey-pdf": "^1.8.76", "survey-react": "^1.8.76", "uuid": "^8.3.2", "web-vitals": "^1.1.2"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}