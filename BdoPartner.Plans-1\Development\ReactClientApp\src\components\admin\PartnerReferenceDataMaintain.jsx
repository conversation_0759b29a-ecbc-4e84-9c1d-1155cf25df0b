import React, { useState, useEffect, useRef } from "react";
import { Card } from "primereact/card";
import { But<PERSON> } from "primereact/button";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import { InputText } from "primereact/inputtext";
import { Dropdown } from "primereact/dropdown";
import { Toast } from "primereact/toast";
import { Dialog } from "primereact/dialog";
import partnerReferenceDataUploadService from "../../services/partnerReferenceDataUploadService";
import { messageService } from "../../core/message/messageService";
import { PartnerReferenceDataCycle, getCycleOptions, getCycleDisplayName } from "../../core/enumertions/partnerReferenceDataCycle";
import { useLoadingControl } from "../../core/loading/hooks/useLoadingControl";


export const PartnerReferenceDataMaintain = () => {
  const [partnerReferenceData, setPartnerReferenceData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [totalRecords, setTotalRecords] = useState(0);
  const [first, setFirst] = useState(0);
  const [rows, setRows] = useState(10);
  const [globalFilter, setGlobalFilter] = useState("");
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const [selectedCycle, setSelectedCycle] = useState(null);
  const [selectedPartnerId, setSelectedPartnerId] = useState(null);

  // Data detail dialog state
  const [showDataDialog, setShowDataDialog] = useState(false);
  const [selectedRowData, setSelectedRowData] = useState(null);
  const [parsedData, setParsedData] = useState({});
  const [metaDetails, setMetaDetails] = useState([]);

  const toast = useRef(null);

  // Year options
  const currentYear = new Date().getFullYear();
  const yearOptions = [];
  for (let i = currentYear - 2; i <= currentYear + 2; i++) {
    yearOptions.push({ label: i.toString(), value: i });
  }

  // Cycle options
  const cycleOptions = [
    { label: 'All Cycles', value: null },
    ...getCycleOptions()
  ];

  // Disable loading interceptor for survey component
  useLoadingControl('survey', true);
   

  useEffect(() => {
    loadPartnerReferenceData();
  }, [selectedYear, selectedCycle, selectedPartnerId, first, rows]); // eslint-disable-line react-hooks/exhaustive-deps

  
  const loadPartnerReferenceData = async () => {
    setLoading(true);
    try {
      const pageIndex = Math.floor(first / rows);
      

      const result = await partnerReferenceDataUploadService.searchPartnerReferenceData(
        selectedYear,
        selectedCycle,
        selectedPartnerId,
        pageIndex,
        rows
      );
    

      setPartnerReferenceData(result.item?.items || []);
      setTotalRecords(result.item?.totalCount || 0);

    } catch (error) {
      console.error('Error loading partner reference data:', error);
      messageService.errorToast("Failed to load partner reference data");
    } finally {
      setLoading(false);
    }
  };

  const onPageChange = (event) => {
    setFirst(event.first);
    setRows(event.rows);
  };

  const handleExport = async () => {
    try {
      const blob = await partnerReferenceDataUploadService.exportPartnerReferenceDataToExcel(
        selectedYear,
        selectedCycle
      );
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `PartnerReferenceData_${selectedYear}${selectedCycle !== null ? `_${getCycleDisplayName(selectedCycle)}` : ''}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      messageService.successToast("Export completed successfully");
    } catch (error) {
      messageService.errorToast("Export failed");
    }
  };

  const handleViewData = async (rowData) => {
    setSelectedRowData(rowData);

    // Parse JSON data
    try {
      const data = JSON.parse(rowData.data || '{}');
      setParsedData(data);
    } catch (error) {
      console.error("Error parsing JSON data:", error);
      setParsedData({});
    }

    // Fetch metadata details if metaId is available
    if (rowData.metaId) {
      try {
        const metadata = await partnerReferenceDataUploadService.getPartnerReferenceDataMetaById(rowData.metaId);
        if (metadata && metadata.partnerReferenceDataMetaDetails) {
          // Sort by column order
          const sortedDetails = metadata.partnerReferenceDataMetaDetails.sort((a, b) => a.columnOrder - b.columnOrder);
          setMetaDetails(sortedDetails);
        } else {
          setMetaDetails([]);
        }
      } catch (error) {
        console.error("Error fetching metadata details:", error);
        setMetaDetails([]);
      }
    } else {
      setMetaDetails([]);
    }

    setShowDataDialog(true);
  };

  // Column renderers
  const cycleBodyTemplate = (rowData) => {
    return getCycleDisplayName(rowData.cycle);
  };

  const dataBodyTemplate = (rowData) => {
    const dataPreview = rowData.data ?
      (rowData.data.length > 100 ? rowData.data.substring(0, 100) + '...' : rowData.data) :
      'No data';

    return <span>{dataPreview}</span>;
  };

  const actionBodyTemplate = (rowData) => {
    return (
      <Button
        icon="pi pi-eye"
        className="p-button-text p-button-sm"
        tooltip="View Details"
        onClick={() => handleViewData(rowData)}
      />
    );
  };

  const dateBodyTemplate = (rowData, field) => {
    if (!rowData[field.field]) return null;
    return new Date(rowData[field.field]).toLocaleString();
  };

  const header = (
    <div className="management-header">
      <div className="filter-section">
        <div className="year-filter-field">
          <label htmlFor="year">Year:</label>
          <Dropdown
            id="year"
            value={selectedYear}
            options={yearOptions}
            onChange={(e) => setSelectedYear(e.value)}
          />
        </div>
        <div className="cycle-filter-field">
          <label htmlFor="cycle">Cycle:</label>
          <Dropdown
            id="cycle"
            value={selectedCycle}
            options={cycleOptions}
            onChange={(e) => setSelectedCycle(e.value)}
            placeholder="All Cycles"
          />
        </div>
      </div>
      <div className="action-section">
        <InputText
          type="search"
          onInput={(e) => setGlobalFilter(e.target.value)}
          placeholder="Search partner reference data..."
        />
        <Button
          icon="pi pi-download"
          label="Export to Excel"
          className="p-button-red"
          rounded
          onClick={handleExport}
        />
      </div>
    </div>
  );

  return (
    <div className="partner-reference-data-maintain">
      <Toast ref={toast} />
      <Card>
        <DataTable
          value={partnerReferenceData}
          loading={loading}
          header={header}
          globalFilter={globalFilter}
          emptyMessage="No partner reference data found"
          sortMode="multiple"
          paginator
          lazy
          rows={rows}
          first={first}
          totalRecords={totalRecords}
          onPage={onPageChange}
          rowsPerPageOptions={[10, 25, 50, 100]}
        >
          <Column field="employeeId" header="Employee ID" sortable />
          <Column field="partnerName" header="Partner Name" sortable />
          <Column field="year" header="Year" sortable />
          <Column field="cycle" header="Cycle" sortable body={cycleBodyTemplate} />
          <Column field="data" header="Reference Data" body={dataBodyTemplate} />
          <Column field="modifiedByName" header="Modified By" sortable />
          <Column
            field="modifiedOn"
            header="Modified On"
            sortable
            body={(rowData) => dateBodyTemplate(rowData, { field: 'modifiedOn' })}
          />
          <Column
            header="Actions"
            body={actionBodyTemplate}
            style={{ width: '100px', textAlign: 'center' }}
          />
        </DataTable>
      </Card>

      {/* Data Detail Dialog */}
      <Dialog
        header={`Reference Data Details - ${selectedRowData?.partnerName || ''}`}
        visible={showDataDialog}
        style={{ width: '90vw', height: '80vh' }}
        modal
        maximizable
        onHide={() => {
          setShowDataDialog(false);
          setSelectedRowData(null);
          setParsedData({});
          setMetaDetails([]);
        }}
      >
        {selectedRowData && (
          <div className="p-fluid" style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            {/* Header Information - Single Row */}
            <div style={{
              display: 'grid',
              gridTemplateColumns: '1fr 1fr 1fr',
              gap: '20px',
              marginBottom: '20px',
              padding: '15px',
              backgroundColor: '#f8f9fa',
              borderRadius: '6px',
              border: '1px solid #e9ecef'
            }}>
              <div>
                <label style={{ fontWeight: 'bold', color: '#495057', fontSize: '16px' }}>Partner Name:</label>
                <div style={{ marginTop: '4px', fontSize: '16px' }}>{selectedRowData.partnerName}</div>
              </div>
              <div>
                <label style={{ fontWeight: 'bold', color: '#495057', fontSize: '16px' }}>Year:</label>
                <div style={{ marginTop: '4px', fontSize: '16px' }}>{selectedRowData.year}</div>
              </div>
              <div>
                <label style={{ fontWeight: 'bold', color: '#495057', fontSize: '16px' }}>Cycle:</label>
                <div style={{ marginTop: '4px', fontSize: '16px' }}>{getCycleDisplayName(selectedRowData.cycle)}</div>
              </div>
            </div>

            {/* Reference Data - Four Column Layout */}
            <div style={{ flex: 1, overflow: 'hidden' }}>
              <label style={{ fontWeight: 'bold', color: '#495057', marginBottom: '10px', display: 'block', fontSize: '18px' }}>Reference Data:</label>
              <div style={{
                display: 'grid',
                gridTemplateColumns: '1fr 1fr 1fr 1fr',
                gap: '15px',
                height: '100%',
                overflow: 'auto',
                padding: '15px',
                backgroundColor: '#f8f9fa',
                borderRadius: '6px',
                border: '1px solid #e9ecef'
              }}>
                {metaDetails.length > 0 ? (
                  metaDetails.map((metaDetail, index) => {
                    const value = parsedData[metaDetail.normalizedColumnName];
                    return (
                      <div key={metaDetail.id || index} style={{
                        marginBottom: '12px',
                        padding: '8px',
                        backgroundColor: '#ffffff',
                        borderRadius: '4px',
                        border: '1px solid #dee2e6'
                      }}>
                        <div style={{
                          fontWeight: 'bold',
                          color: '#495057',
                          marginBottom: '4px',
                          fontSize: '16px'
                        }}>
                          {metaDetail.columnName || metaDetail.normalizedColumnName}:
                        </div>
                        <div style={{
                          fontSize: '16px',
                          color: '#6c757d',
                          wordBreak: 'break-word'
                        }}>
                          {value !== null && value !== undefined ? value.toString() : 'N/A'}
                        </div>
                      </div>
                    );
                  })
                ) : Object.keys(parsedData).length > 0 ? (
                  // Fallback to original display if no metadata details
                  Object.entries(parsedData).map(([key, value], index) => (
                    <div key={key} style={{
                      marginBottom: '12px',
                      padding: '8px',
                      backgroundColor: '#ffffff',
                      borderRadius: '4px',
                      border: '1px solid #dee2e6'
                    }}>
                      <div style={{
                        fontWeight: 'bold',
                        color: '#495057',
                        marginBottom: '4px',
                        fontSize: '15px'
                      }}>
                        {key}:
                      </div>
                      <div style={{
                        fontSize: '14px',
                        color: '#6c757d',
                        wordBreak: 'break-word'
                      }}>
                        {value !== null && value !== undefined ? value.toString() : 'N/A'}
                      </div>
                    </div>
                  ))
                ) : (
                  <div style={{
                    gridColumn: '1 / -1',
                    textAlign: 'center',
                    color: '#6c757d',
                    padding: '20px'
                  }}>
                    No data available
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </Dialog>
    </div>
  );
};