import React, { useState, useEffect, useRef } from "react";
import { Card } from "primereact/card";
import { But<PERSON> } from "primereact/button";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import { InputText } from "primereact/inputtext";
import { Dropdown } from "primereact/dropdown";
import { Toast } from "primereact/toast";
import { ConfirmDialog, confirmDialog } from "primereact/confirmdialog";
import { Dialog } from "primereact/dialog";
import { Checkbox } from "primereact/checkbox";
import partnerReviewerUploadService from "../../services/partnerReviewerUploadService";
import { messageService } from "../../core/message/messageService";
import { PartnerAutocomplete } from "../common/PartnerAutocomplete";
import { useLoadingControl } from "../../core/loading/hooks/useLoadingControl";

export const PartnerReviewerManagement = () => {
  const [partnerReviewers, setPartnerReviewers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [globalFilter, setGlobalFilter] = useState("");
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const [selectedPartnerReviewer, setSelectedPartnerReviewer] = useState(null);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [editFormData, setEditFormData] = useState({});
  const [saving, setSaving] = useState(false);

  const toast = useRef(null);

  // Year options
  const currentYear = new Date().getFullYear();
  const yearOptions = [];
  for (let i = currentYear - 2; i <= currentYear + 2; i++) {
    yearOptions.push({ label: i.toString(), value: i });
  }

  // Disable loading interceptor for survey component
  useLoadingControl('survey', true);
  
  useEffect(() => {
    loadPartnerReviewers();
  }, [selectedYear]);

  const loadPartnerReviewers = async () => {
    setLoading(true);
    try {
      const result = await partnerReviewerUploadService.getPartnerReviewersByYear(selectedYear);
      setPartnerReviewers(result || []);
    } catch (error) {
      messageService.errorToast("Failed to load partner reviewers");
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (partnerReviewer) => {
    setSelectedPartnerReviewer(partnerReviewer);

    // Create partner objects for autocomplete components
    const primaryReviewer = partnerReviewer.primaryReviewerId ? {
      id: partnerReviewer.primaryReviewerId,
      displayName: partnerReviewer.primaryReviewerName || partnerReviewer.primaryReviewerId
    } : null;

    const secondaryReviewer = partnerReviewer.secondaryReviewerId ? {
      id: partnerReviewer.secondaryReviewerId,
      displayName: partnerReviewer.secondaryReviewerName || partnerReviewer.secondaryReviewerId
    } : null;

    setEditFormData({
      id: partnerReviewer.id,
      partnerId: partnerReviewer.partnerId,
      partnerName: partnerReviewer.partnerDisplayName,
      primaryReviewer: primaryReviewer,
      primaryReviewerId: partnerReviewer.primaryReviewerId,
      primaryReviewerName: partnerReviewer.primaryReviewerName,
      secondaryReviewer: secondaryReviewer,
      secondaryReviewerId: partnerReviewer.secondaryReviewerId,
      secondaryReviewerName: partnerReviewer.secondaryReviewerName,
      year: partnerReviewer.year,
      exempt: partnerReviewer.exempt,
      leadershipRole: partnerReviewer.leadershipRole
    });
    setShowEditDialog(true);
  };

  const handleSave = async () => {
    setSaving(true);
    try {
      // Prepare data for API call - extract IDs and names from partner objects
      const dataToSave = {
        id: editFormData.id,
        partnerId: editFormData.partnerId,
        partnerName: editFormData.partnerName,
        primaryReviewerId: editFormData.primaryReviewer?.id || null,
        primaryReviewerName: editFormData.primaryReviewer?.displayName || null,
        secondaryReviewerId: editFormData.secondaryReviewer?.id || null,
        secondaryReviewerName: editFormData.secondaryReviewer?.displayName || null,
        year: editFormData.year,
        exempt: editFormData.exempt,
        leadershipRole: editFormData.leadershipRole
      };

      await partnerReviewerUploadService.updatePartnerReviewer(dataToSave);
      messageService.successToast("Partner reviewer updated successfully");
      setShowEditDialog(false);
      loadPartnerReviewers();
    } catch (error) {
      messageService.errorToast(error.message || "Update failed");
    } finally {
      setSaving(false);
    }
  };

  const handleDelete = (partnerReviewer) => {
    confirmDialog({
      message: `Are you sure you want to delete the reviewer assignment for ${partnerReviewer.partnerDisplayName}?`,
      header: 'Confirm Delete',
      icon: 'pi pi-exclamation-triangle',
      accept: async () => {
        try {
          await partnerReviewerUploadService.deletePartnerReviewer(partnerReviewer.id);
          messageService.successToast("Partner reviewer deleted successfully");
          loadPartnerReviewers();
        } catch (error) {
          messageService.errorToast(error.message || "Delete failed");
        }
      }
    });
  };

  const handleExport = async () => {
    try {
      const blob = await partnerReviewerUploadService.exportPartnerReviewersToExcel(selectedYear);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `PartnerReviewers_${selectedYear}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      messageService.successToast("Export completed successfully");
    } catch (error) {
      messageService.errorToast("Export failed");
    }
  };

  // Column renderers
  const exemptBodyTemplate = (rowData) => {
    return rowData.exempt ? 'Yes' : 'No';
  };

  const actionBodyTemplate = (rowData) => {
    return (
      <div className="p-d-flex p-ai-center">
        <Button
          icon="pi pi-pencil"
          className="p-button-text p-button-sm p-mr-2"
          tooltip="Edit"
          onClick={() => handleEdit(rowData)}
        />
        <Button
          icon="pi pi-trash"
          className="p-button-text p-button-danger p-button-sm"
          tooltip="Delete"
          onClick={() => handleDelete(rowData)}
        />
      </div>
    );
  };

  const header = (
    <div className="management-header">
      <div className="filter-section">
        {/* <h3>Partner Reviewer Management</h3> */}
        <div className="year-filter-field">
          <label htmlFor="year">Year:</label>
          <Dropdown
            id="year"
            value={selectedYear}
            options={yearOptions}
            onChange={(e) => setSelectedYear(e.value)}
          />
        </div>
      </div>
      <div className="action-section">
        <InputText
          type="search"
          onInput={(e) => setGlobalFilter(e.target.value)}
          placeholder="Search partner reviewers..."
        />
        <Button
          icon="pi pi-download"
          label="Export to Excel"
          className="p-button-red"
          rounded
          onClick={handleExport}
        />
      </div>
    </div>
  );

  return (
    <div className="partner-reviewer-management">
      <Toast ref={toast} />
      <ConfirmDialog />
      <Card>
        <DataTable
          value={partnerReviewers}
          loading={loading}
          header={header}
          globalFilter={globalFilter}
          emptyMessage="No partner reviewers found"
          sortMode="multiple"
          paginator
          rows={10}
          rowsPerPageOptions={[10, 25, 50, 100]}
        >
          <Column field="partnerEmployeeId" header="Partner Employee ID" sortable />
          <Column field="partnerDisplayName" header="Partner Name" sortable />
          <Column field="exempt" header="Exempt" sortable body={exemptBodyTemplate} />
          <Column field="leadershipRole" header="Leadership Role" sortable />
          <Column field="primaryReviewerName" header="Primary Reviewer Name" sortable />
          <Column field="secondaryReviewerName" header="Secondary Reviewer Name" sortable />
          <Column field="year" header="Year" sortable />
          <Column
            header="Actions"
            body={actionBodyTemplate}
            style={{ width: '120px' }}
          />
        </DataTable>
      </Card>

      {/* Edit Dialog */}
      <Dialog
        header="Edit Partner Reviewer Assignment"
        visible={showEditDialog}
        style={{ width: '600px' }}
        modal
        onHide={() => {
          setShowEditDialog(false);
          setSelectedPartnerReviewer(null);
          setEditFormData({});
        }}
      >
        <div className="p-fluid">
          <div className="p-field p-mb-3">
            <label htmlFor="partnerName">Partner Name:</label>
            <InputText
              id="partnerName"
              value={editFormData.partnerName || ''}
              disabled
              className="p-mt-2"
            />
          </div>

          <div className="p-field p-mb-3">
            <label htmlFor="exempt">Exempt:</label>
            <div className="p-mt-2">
              <Checkbox
                id="exempt"
                checked={editFormData.exempt || false}
                onChange={(e) => setEditFormData({...editFormData, exempt: e.checked})}
              />
            </div>
          </div>

          <div className="p-field p-mb-3">
            <label htmlFor="leadershipRole">Leadership Role:</label>
            <InputText
              id="leadershipRole"
              value={editFormData.leadershipRole || ''}
              onChange={(e) => setEditFormData({...editFormData, leadershipRole: e.target.value})}
              className="p-mt-2"
            />
          </div>

          <div className="p-field p-mb-3">
            <label htmlFor="primaryReviewer">Primary Reviewer:</label>
            <PartnerAutocomplete
              id="primaryReviewer"
              value={editFormData.primaryReviewer}
              onChange={(e) => setEditFormData({...editFormData, primaryReviewer: e.target.value})}
              placeholder="Search for primary reviewer..."
              className="p-mt-2"
            />
          </div>

          <div className="p-field p-mb-3">
            <label htmlFor="secondaryReviewer">Secondary Reviewer:</label>
            <PartnerAutocomplete
              id="secondaryReviewer"
              value={editFormData.secondaryReviewer}
              onChange={(e) => setEditFormData({...editFormData, secondaryReviewer: e.target.value})}
              placeholder="Search for secondary reviewer..."
              className="p-mt-2"
            />
          </div>

          <div style={{
            display: 'flex',
            justifyContent: 'flex-end',
            alignItems: 'center',
            gap: '12px',
            marginTop: '20px'
          }}>
            <Button
              label="Cancel"
              icon="pi pi-times"
              className="p-button-text"
              onClick={() => {
                setShowEditDialog(false);
                setSelectedPartnerReviewer(null);
                setEditFormData({});
              }}
            />
            <Button
              label="Save"
              icon="pi pi-check"
              className="action"
              loading={saving}
              onClick={handleSave}
            />
          </div>
        </div>
      </Dialog>
    </div>
  );
};