import React, { useState, useEffect, useRef } from "react";
import { Card } from "primereact/card";
import { <PERSON><PERSON> } from "primereact/button";
import { FileUpload } from "primereact/fileupload";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import { Dialog } from "primereact/dialog";
import { Dropdown } from "primereact/dropdown";
import { InputText } from "primereact/inputtext";
import { Paginator } from "primereact/paginator";
import { Toast } from "primereact/toast";
import { ConfirmDialog, confirmDialog } from "primereact/confirmdialog";
import { Badge } from "primereact/badge";
import { Tooltip } from "primereact/tooltip";
import partnerReferenceDataUploadService from "../../services/partnerReferenceDataUploadService";
import { messageService } from "../../core/message/messageService";
import { PartnerReferenceDataUploadStatus } from "../../core/enumertions/partnerReferenceDataUploadStatus";
import { PartnerReferenceDataCycle, getCycleOptions, getCycleDisplayName } from "../../core/enumertions/partnerReferenceDataCycle";
import { useLoadingControl } from "../../core/loading/hooks/useLoadingControl";

export const UploadPartnerReferenceData = () => {
  const [uploads, setUploads] = useState([]);
  const [loading, setLoading] = useState(false);
  const [totalRecords, setTotalRecords] = useState(0);
  const [first, setFirst] = useState(0);
  const [rows, setRows] = useState(10);
  const [globalFilter, setGlobalFilter] = useState("");

  // Upload dialog state
  const [showUploadDialog, setShowUploadDialog] = useState(false);
  const [selectedFile, setSelectedFile] = useState(null);
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const [selectedCycle, setSelectedCycle] = useState(PartnerReferenceDataCycle.Planning);
  const [uploading, setUploading] = useState(false);

  // Details dialog state
  const [showDetailsDialog, setShowDetailsDialog] = useState(false);
  const [selectedUpload, setSelectedUpload] = useState(null);
  const [uploadDetails, setUploadDetails] = useState([]);
  const [metaDetails, setMetaDetails] = useState([]);
  const [detailsLoading, setDetailsLoading] = useState(false);
  const [detailsFilter, setDetailsFilter] = useState('all'); // 'all', 'valid', 'invalid'

  const toast = useRef(null);
  const fileUploadRef = useRef(null);

  // Year options for the dropdown
  const currentYear = new Date().getFullYear();
  const yearOptions = [];
  for (let i = currentYear; i <= currentYear + 1; i++) {
    yearOptions.push({ label: i.toString(), value: i });
  }

  // Cycle options
  const cycleOptions = getCycleOptions();

  // Disable loading interceptor for survey component
  useLoadingControl('survey', true);

  useEffect(() => {
    loadUploads();
  }, [first, rows]); // eslint-disable-line react-hooks/exhaustive-deps

  const loadUploads = async () => {
    setLoading(true);
    try {
      const pageIndex = Math.floor(first / rows);
      const result = await partnerReferenceDataUploadService.searchPartnerReferenceDataUploads(
        null, // year filter
        null, // status filter
        pageIndex,
        rows
      );
      setUploads(result.items || []);
      setTotalRecords(result.totalCount || 0);
    } catch (error) {
      messageService.errorToast("Failed to load uploads");
    } finally {
      setLoading(false);
    }
  };

  const onPageChange = (event) => {
    setFirst(event.first);
    setRows(event.rows);
  };

  const handleFileSelect = (event) => {
    const file = event.files[0];
    if (file) {
      // Validate file type
      const allowedTypes = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
        'application/vnd.ms-excel', // .xls
        'text/csv' // .csv
      ];

      if (!allowedTypes.includes(file.type)) {
        messageService.errorToast("Only Excel (.xlsx, .xls) and CSV files are allowed");
        fileUploadRef.current.clear();
        return;
      }

      setSelectedFile(file);
      setShowUploadDialog(true);
    }
  };

  const handleUpload = async () => {
    if (!selectedFile) {
      messageService.warnToast("Please select a file");
      return;
    }

    setUploading(true);
    try {
      await partnerReferenceDataUploadService.uploadFile(selectedFile, selectedYear, selectedCycle);

      messageService.successToast("File uploaded successfully");
      setShowUploadDialog(false);
      setSelectedFile(null);
      setSelectedYear(new Date().getFullYear());
      setSelectedCycle(PartnerReferenceDataCycle.Planning);
      fileUploadRef.current.clear();
      loadUploads();
    } catch (error) {
      messageService.errorToast(error.message || "Upload failed");
    } finally {
      setUploading(false);
    }
  };

  const handleDownloadTemplate = async () => {
    try {
      const blob = await partnerReferenceDataUploadService.getUploadTemplate();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = 'PartnerReferenceDataUploadTemplate.xlsx';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      messageService.errorToast("Failed to download template");
    }
  };

  const handleViewDetails = async (upload) => {
    setSelectedUpload(upload);
    setDetailsLoading(true);
    setShowDetailsDialog(true);
    setDetailsFilter('all'); // Reset filter to "Show All" when opening dialog

    await loadUploadDetails(upload.id, 'all');
  };

  const loadUploadDetails = async (uploadId, filter) => {
    setDetailsLoading(true);
    try {
      let includeValidOnly = false;
      let includeInvalidOnly = false;

      if (filter === 'valid') {
        includeValidOnly = true;
      } else if (filter === 'invalid') {
        includeInvalidOnly = true;
      }

      const result = await partnerReferenceDataUploadService.getPartnerReferenceDataUploadDetails(
        uploadId,
        includeValidOnly,
        includeInvalidOnly
      );

      // Handle the new structure with uploadDetails and metaDetails
      if (result && result.uploadDetails && result.metaDetails) {
        setUploadDetails(result.uploadDetails);
        setMetaDetails(result.metaDetails);
      } else {
        // Fallback for backward compatibility
        setUploadDetails(result || []);
        setMetaDetails([]);
      }
    } catch (error) {
      messageService.errorToast("Failed to load upload details");
    } finally {
      setDetailsLoading(false);
    }
  };

  const handleDetailsFilterChange = (e) => {
    const newFilter = e.value;
    setDetailsFilter(newFilter);
    if (selectedUpload) {
      loadUploadDetails(selectedUpload.id, newFilter);
    }
  };

  // Filter options for the details dialog
  const filterOptions = [
    { label: 'Show All', value: 'all' },
    { label: 'Only Valid', value: 'valid' },
    { label: 'Only Invalid', value: 'invalid' }
  ];

  const handleSubmit = async (uploadId) => {
    confirmDialog({
      message: 'Are you sure you want to submit this upload? This will update the partner reference data.',
      header: 'Confirm Submit',
      icon: 'pi pi-exclamation-triangle',
      accept: async () => {
        try {
          await partnerReferenceDataUploadService.submitUpload(uploadId);
          messageService.successToast("Upload submitted successfully");
          loadUploads();
        } catch (error) {
          messageService.errorToast(error.message || "Submit failed");
        }
      }
    });
  };

  const handleRetry = async (uploadId) => {
    try {
      await partnerReferenceDataUploadService.validateUpload(uploadId);
      messageService.successToast("Validation retried successfully");
      loadUploads();
    } catch (error) {
      messageService.errorToast(error.message || "Retry failed");
    }
  };

  const handleDelete = async (uploadId, fileName) => {
    confirmDialog({
      message: `Are you sure you want to delete the upload "${fileName}"? This action cannot be undone and will remove all associated data.`,
      header: 'Confirm Delete',
      icon: 'pi pi-exclamation-triangle',
      acceptClassName: 'p-button-danger',
      accept: async () => {
        try {
          await partnerReferenceDataUploadService.deleteUpload(uploadId);
          messageService.successToast("Upload deleted successfully");
          loadUploads();
        } catch (error) {
          messageService.errorToast(error.message || "Delete failed");
        }
      }
    });
  };

  // Column renderers
  const statusBodyTemplate = (rowData) => {
    const statusMap = {
      [PartnerReferenceDataUploadStatus.Uploading]: { label: 'Uploading', severity: 'info' },
      [PartnerReferenceDataUploadStatus.Uploaded]: { label: 'Uploaded', severity: 'warning' },
      [PartnerReferenceDataUploadStatus.Validating]: { label: 'Validating', severity: 'info' },
      [PartnerReferenceDataUploadStatus.ValidationPassed]: { label: 'Validation Passed', severity: 'success' },
      [PartnerReferenceDataUploadStatus.ValidationFailed]: { label: 'Validation Failed', severity: 'danger' },
      [PartnerReferenceDataUploadStatus.Submitted]: { label: 'Submitted', severity: 'success' }
    };

    const status = statusMap[rowData.status] || { label: 'Unknown', severity: 'secondary' };
    return <Badge value={status.label} severity={status.severity} />;
  };

  const cycleBodyTemplate = (rowData) => {
    return getCycleDisplayName(rowData.cycle);
  };

  const validationSummaryBodyTemplate = (rowData) => {
    if (!rowData.validationSummary) return null;

    const truncated = rowData.validationSummary.length > 50
      ? rowData.validationSummary.substring(0, 50) + '...'
      : rowData.validationSummary;

    return (
      <div>
        <span>{truncated}</span>
        {rowData.validationSummary.length > 50 && (
          <>
            <Button
              icon="pi pi-info-circle"
              className="p-button-text p-button-sm"
              tooltip="View full validation summary"
              onClick={() => {
                messageService.infoDialog(rowData.validationSummary);
              }}
            />
          </>
        )}
      </div>
    );
  };

  const actionBodyTemplate = (rowData) => {
    return (
      <div className="p-d-flex p-ai-center">
        <Button
          icon="pi pi-eye"
          className="p-button-text p-button-sm p-mr-2"
          tooltip="View Details"
          onClick={() => handleViewDetails(rowData)}
        />

        {rowData.status === PartnerReferenceDataUploadStatus.ValidationPassed && (
          <Button
            icon="pi pi-check"
            className="p-button-text p-button-success p-button-sm p-mr-2"
            tooltip="Submit"
            onClick={() => handleSubmit(rowData.id)}
          />
        )}

        {(rowData.status === PartnerReferenceDataUploadStatus.ValidationFailed
          || rowData.status === PartnerReferenceDataUploadStatus.Uploaded
          || rowData.status === PartnerReferenceDataUploadStatus.Uploading
          || rowData.status === PartnerReferenceDataUploadStatus.Validating) && (
          <Button
            icon="pi pi-refresh"
            className="p-button-text p-button-warning p-button-sm p-mr-2"
            tooltip="Retry"
            onClick={() => handleRetry(rowData.id)}
          />
        )}

        {rowData.status === PartnerReferenceDataUploadStatus.ValidationFailed && (
          <Button
            icon="pi pi-trash"
            className="p-button-text p-button-danger p-button-sm"
            tooltip="Delete Upload"
            onClick={() => handleDelete(rowData.id, rowData.uploadFileName)}
          />
        )}
      </div>
    );
  };

  const dateBodyTemplate = (rowData, field) => {
    if (!rowData[field.field]) return null;
    return new Date(rowData[field.field]).toLocaleString();
  };

  const rowIdBodyTemplate = (rowData) => {
    return (
      <div style={{ textAlign: 'center', fontWeight: 'bold', color: '#6366f1' }}>
        {rowData.rowId}
      </div>
    );
  };

  const rowIdHeaderTemplate = () => {
    return (
      <div>
        Row ID
        <Tooltip target=".row-id-header" content="The row number from the original uploaded file" position="top" />
        <i className="pi pi-info-circle row-id-header p-ml-1" style={{ fontSize: '0.8rem', color: '#6c757d' }}></i>
      </div>
    );
  };

  const header = (
    <div className="upload-history-header">
      <div className="section-header">Upload History</div>
      <div className="upload-history-actions">
        <InputText
          type="search"
          onInput={(e) => setGlobalFilter(e.target.value)}
          placeholder="Search uploads..."
          className="upload-search-input"
        />
        <Button
          icon="pi pi-download"
          label="Download Template"
          className="p-button-red"
          rounded
          onClick={handleDownloadTemplate}
        />
      </div>
    </div>
  );

  return (
    <div className="upload-partner-reviewer">
      <Toast ref={toast} />
      <ConfirmDialog />

      {/* Upload Section */}
      <Card className="p-mb-4 upload-section">
        {/* <div className="p-card-title">
          <h5>Upload Partner Reference Data File</h5>
        </div> */}
        <div className="p-card-content">
          <FileUpload
            ref={fileUploadRef}
            mode="basic"
            name="file"
            accept=".xlsx,.xls,.csv"
            maxFileSize={10000000} // 10MB
            onSelect={handleFileSelect}
            chooseLabel="Select File"
            className="p-mr-2"
            auto={false}
          />
          <small className="p-d-block p-mt-2">
            Supported formats: Excel (.xlsx, .xls) and CSV files. Maximum size: 10MB
          </small>
        </div>
      </Card>

      {/* Upload History Table */}
      <Card className="upload-history">
        <DataTable
          value={uploads}
          loading={loading}
          header={header}
          globalFilter={globalFilter}
          emptyMessage="No uploads found"
          scrollable
        >
          <Column field="uploadFileName" header="File Name" sortable />
          <Column field="year" header="Year" sortable />
          <Column field="cycle" header="Cycle" sortable body={cycleBodyTemplate} />
          <Column field="modifiedByName" header="Updated By" sortable />
          <Column
            field="modifiedOn"
            header="Updated On"
            sortable
            body={(rowData) => dateBodyTemplate(rowData, { field: 'modifiedOn' })}
          />
          <Column
            field="validationSummary"
            header="Validation Summary"
            body={validationSummaryBodyTemplate}
          />
          <Column
            field="status"
            header="Status"
            sortable
            body={statusBodyTemplate}
          />
          <Column
            header="Actions"
            body={actionBodyTemplate}
            style={{ width: '150px' }}
          />
        </DataTable>

        <Paginator
          first={first}
          rows={rows}
          totalRecords={totalRecords}
          rowsPerPageOptions={[10, 20, 50]}
          onPageChange={onPageChange}
          className="p-mt-3"
        />
      </Card>

      {/* Upload Dialog */}
      <Dialog
        header="Select Year and Cycle for Upload"
        visible={showUploadDialog}
        style={{ width: '450px' }}
        modal
        onHide={() => {
          setShowUploadDialog(false);
          setSelectedFile(null);
          setSelectedYear(new Date().getFullYear());
          setSelectedCycle(PartnerReferenceDataCycle.Planning);
          fileUploadRef.current?.clear();
        }}
      >
        <div className="p-fluid">
          <div className="p-field p-mb-3">
            <label htmlFor="selectedFile">Selected File:</label>
            <div className="p-mt-2">
              <strong>{selectedFile?.name}</strong>
            </div>
          </div>

          <div className="p-field p-mb-3">
            <label htmlFor="year">Year:</label>
            <Dropdown
              id="year"
              value={selectedYear}
              options={yearOptions}
              onChange={(e) => setSelectedYear(e.value)}
              placeholder="Select year"
              className="p-mt-2"
            />
          </div>

          <div className="p-field p-mb-4">
            <label htmlFor="cycle">Cycle:</label>
            <Dropdown
              id="cycle"
              value={selectedCycle}
              options={cycleOptions}
              onChange={(e) => setSelectedCycle(e.value)}
              placeholder="Select cycle"
              className="p-mt-2"
            />
          </div>

          <div style={{
            display: 'flex',
            justifyContent: 'flex-end',
            alignItems: 'center',
            gap: '12px',
            marginTop: '20px'
          }}>
            <Button
              label="Cancel"
              icon="pi pi-times"
              className="p-button-text"
              onClick={() => {
                setShowUploadDialog(false);
                setSelectedFile(null);
                setSelectedYear(new Date().getFullYear());
                setSelectedCycle(PartnerReferenceDataCycle.Planning);
                fileUploadRef.current?.clear();
              }}
            />
            <Button
              label="Upload"
              icon="pi pi-upload"
              className="p-button-red"
              loading={uploading}
              onClick={handleUpload}
            />
          </div>
        </div>
      </Dialog>

      {/* Details Dialog */}
      <Dialog
        header={`Upload Details - ${selectedUpload?.uploadFileName || ''}`}
        visible={showDetailsDialog}
        style={{ width: '80vw', height: '80vh' }}
        modal
        onHide={() => {
          setShowDetailsDialog(false);
          setSelectedUpload(null);
          setUploadDetails([]);
          setDetailsFilter('all');
        }}
      >
        {/* Filter Controls */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '12px',
          marginBottom: '16px',
          padding: '12px',
          backgroundColor: '#f8f9fa',
          borderRadius: '6px',
          border: '1px solid #e9ecef'
        }}>
          <label htmlFor="details-filter" style={{
            fontWeight: '600',
            color: '#495057',
            minWidth: '60px'
          }}>
            Filter:
          </label>
          <Dropdown
            id="details-filter"
            value={detailsFilter}
            options={filterOptions}
            onChange={handleDetailsFilterChange}
            placeholder="Select filter"
            style={{ minWidth: '150px' }}
          />
          <span style={{
            color: '#6c757d',
            fontSize: '0.9rem',
            marginLeft: '8px'
          }}>
            Showing {uploadDetails.length} record(s)
          </span>
          {selectedUpload?.validationSummary && (
            <div style={{
              marginLeft: '16px',
              padding: '8px 12px',
              backgroundColor: '#fff3cd',
              border: '1px solid #ffeaa7',
              borderRadius: '4px',
              fontSize: '0.9rem',
              color: '#856404',
              maxWidth: '400px',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap'
            }}>
              <strong>Validation Summary:</strong> {selectedUpload.validationSummary}
            </div>
          )}
        </div>

        <DataTable
          value={uploadDetails}
          loading={detailsLoading}
          scrollable
          scrollHeight="500px"
          emptyMessage="No details found"
        >
          <Column
            field="rowId"
            header={rowIdHeaderTemplate}
            sortable
            style={{ width: '80px' }}
            body={rowIdBodyTemplate}
          />
          {/* Dynamic columns based on metadata */}
          {metaDetails
            .sort((a, b) => a.columnOrder - b.columnOrder)
            .map((metaDetail) => (
              <Column
                key={metaDetail.normalizedColumnName}
                field={`columnData.${metaDetail.normalizedColumnName}`}
                header={metaDetail.columnName}
                sortable
                body={(rowData) => {
                  const value = rowData.columnData?.[metaDetail.normalizedColumnName] || '';
                  return (
                    <span title={value} style={{
                      display: 'block',
                      maxWidth: '200px',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap'
                    }}>
                      {value}
                    </span>
                  );
                }}
              />
            ))}
          <Column
            field="isValid"
            header="Valid"
            sortable
            style={{ width: '80px' }}
            body={(rowData) => (
              <Badge
                value={rowData.isValid ? 'Valid' : 'Invalid'}
                severity={rowData.isValid ? 'success' : 'danger'}
              />
            )}
          />
          <Column
            field="validationError"
            header="Validation Error"
            style={{ minWidth: '200px' }}
            body={(rowData) => (
              <span title={rowData.validationError} style={{
                display: 'block',
                maxWidth: '300px',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap'
              }}>
                {rowData.validationError}
              </span>
            )}
          />
        </DataTable>
      </Dialog>
    </div>
  );
};