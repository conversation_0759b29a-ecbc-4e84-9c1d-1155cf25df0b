import React, { useState, useEffect, useRef } from "react";
import { AutoComplete } from "primereact/autocomplete";
import partnerService from "../../services/partnerService";

/**
 * PartnerAutocomplete Component
 * 
 * A reusable autocomplete component for selecting partners.
 * Provides search functionality by first name, last name, display name, or email.
 * 
 * @param {Object} props - Component props
 * @param {Object} props.value - Selected partner object with id and displayName
 * @param {Function} props.onChange - Callback function when selection changes
 * @param {string} props.placeholder - Placeholder text for the input
 * @param {boolean} props.disabled - Whether the component is disabled
 * @param {string} props.className - Additional CSS classes
 * @param {string} props.id - HTML id attribute
 * @param {boolean} props.required - Whether the field is required
 * @param {Function} props.onBlur - Callback function when input loses focus
 * @param {Function} props.onFocus - Callback function when input gains focus
 */
export const PartnerAutocomplete = ({
  value,
  onChange,
  placeholder = "Search partners...",
  disabled = false,
  className = "",
  id,
  required = false,
  onBlur,
  onFocus
}) => {
  const [suggestions, setSuggestions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const searchTimeoutRef = useRef(null);

  // Initialize display value from the selected partner
  useEffect(() => {
    if (value && value.displayName) {
      setSearchTerm(value.displayName);
    } else if (value && typeof value === 'string') {
      // Handle case where value is just a string (for backward compatibility)
      setSearchTerm(value);
    } else {
      setSearchTerm("");
    }
  }, [value]);

  /**
   * Search for partners based on the input term
   * @param {string} query - Search query
   */
  const searchPartners = async (query) => {
    if (!query || query.trim().length < 2) {
      setSuggestions([]);
      return;
    }

    setLoading(true);
    try {
      const partners = await partnerService.searchPartnersForAutocomplete(query);
      setSuggestions(partners);
    } catch (error) {
      console.error("Error searching partners:", error);
      setSuggestions([]);
    } finally {
      setLoading(false);
    }
  };

  /**
   * Handle input change with debouncing
   * @param {Object} event - Input change event
   */
  const handleInputChange = (event) => {
    const query = event.query || event.target.value || "";
    setSearchTerm(query);

    // Clear previous timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // Set new timeout for debounced search
    searchTimeoutRef.current = setTimeout(() => {
      searchPartners(query);
    }, 300); // 300ms debounce
  };

  /**
   * Handle partner selection
   * @param {Object} event - Selection event
   */
  const handleSelect = (event) => {
    const selectedPartner = event.value;
    
    if (selectedPartner && selectedPartner.id) {
      // Update search term to show selected partner's name
      setSearchTerm(selectedPartner.displayName);
      
      // Call onChange with the selected partner object
      if (onChange) {
        onChange({
          target: {
            value: selectedPartner
          }
        });
      }
    }
  };

  /**
   * Handle manual input (when user types without selecting from dropdown)
   * @param {Object} event - Blur event
   */
  const handleBlur = (event) => {
    const inputValue = event.target.value;
    
    // If the input value doesn't match the current selection, clear the selection
    if (value && value.displayName !== inputValue) {
      if (onChange) {
        onChange({
          target: {
            value: null
          }
        });
      }
    }

    if (onBlur) {
      onBlur(event);
    }
  };

  /**
   * Handle focus event
   * @param {Object} event - Focus event
   */
  const handleFocus = (event) => {
    if (onFocus) {
      onFocus(event);
    }
  };

  /**
   * Template for displaying partner suggestions
   * @param {Object} partner - Partner object
   * @returns {JSX.Element} Rendered suggestion item
   */
  const itemTemplate = (partner) => {
    return (
      <div className="partner-suggestion-item">
        <div className="partner-name">
          {partner.displayName}
        </div>
        <div className="partner-details">
          {partner.employeeId && <span>ID: {partner.employeeId}</span>}
          {partner.department && <span>{partner.department}</span>}
          {partner.location && <span>{partner.location}</span>}
        </div>
        {partner.mail && (
          <div className="partner-email">
            {partner.mail}
          </div>
        )}
      </div>
    );
  };

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  return (
    <div className={`partner-autocomplete ${className}`}>
      <AutoComplete
        id={id}
        value={searchTerm}
        suggestions={suggestions}
        completeMethod={handleInputChange}
        onSelect={handleSelect}
        onBlur={handleBlur}
        onFocus={handleFocus}
        onChange={(e) => setSearchTerm(e.target.value)}
        placeholder={placeholder}
        disabled={disabled}
        itemTemplate={itemTemplate}
        minLength={2}
        delay={300}
        loading={loading}
        dropdown={false}
        forceSelection={false}
        className={`w-full ${loading ? 'p-autocomplete-loading' : ''}`}
        inputClassName="w-full"
        panelClassName="partner-autocomplete-panel"
        emptyMessage="No partners found. Try a different search term."
        required={required}
        scrollHeight="320px"
      />
      

    </div>
  );
};
