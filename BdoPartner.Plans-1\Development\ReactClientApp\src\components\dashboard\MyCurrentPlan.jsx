import { Card } from 'primereact/card';
import { Button } from 'primereact/button';
import { useNavigate } from 'react-router-dom';

const MyCurrentPlan = () => {
  const navigate = useNavigate();
  const currentYear = new Date().getFullYear();

  const handleViewMyPlan = () => {
    navigate(`/my-partner-plan?year=${currentYear}`);
  };

  return (
    <div className="my-current-plan">
      <h2 className="section-title">My Current Plan</h2>
      <p className="section-description">
        A detailed view of your current 2025 plan, allowing you to track progress, make updates, and ensure alignment with personal and organizational goals. It serves as your hub for managing and refining your active plan for the year.
      </p>

      <Card className="current-plan-card">
        <div className="plan-details">
          <div className="plan-info-grid">
            <div className="plan-info-item">
              <label className="plan-label">Year</label>
              <span className="plan-value">2025</span>
            </div>

            <div className="plan-info-item">
              <label className="plan-label">Plan Status</label>
              <span className="plan-value status-submitted">Submitted</span>
            </div>

            <div className="plan-info-item">
              <label className="plan-label">Reviewers</label>
              <div className="reviewers-list">
                <div className="reviewer-item">
                  <span className="reviewer-label">Primary Reviewer:</span>
                  <span className="reviewer-name">Michael Sampson</span>
                </div>
                <div className="reviewer-item">
                  <span className="reviewer-label">Secondary Reviewer:</span>
                  <span className="reviewer-name">Brian Johnson</span>
                </div>
              </div>
            </div>

            <div className="plan-info-item">
              <label className="plan-label">Review Status</label>
              <span className="plan-value status-not-started">Not Started</span>
            </div>
          </div>

          <div className="plan-actions">
            <Button
              label="View My Plan"
              className="p-button-red view-plan-btn"
              icon="pi pi-eye"
              rounded
              onClick={handleViewMyPlan}
            />
          </div>
        </div>
      </Card>
    </div>
  );
};

export default MyCurrentPlan;