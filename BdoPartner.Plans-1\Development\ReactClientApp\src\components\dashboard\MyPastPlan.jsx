import { Card } from 'primereact/card';
import { Button } from 'primereact/button';
import { Dropdown } from 'primereact/dropdown';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import APP_CONFIG from '../../core/config/appConfig';

const MyPastPlans = () => {
  const navigate = useNavigate();
  const [selectedYear, setSelectedYear] = useState(null);

  const yearOptions = [
    { label: 'Select', value: null },
    { label: '2024', value: '2024' },
    { label: '2023', value: '2023' },
    { label: '2022', value: '2022' }
  ];

  const handleViewPlan = () => {
    if (selectedYear) {
      navigate(`/my-partner-plan?year=${selectedYear}`);
    }
  };

  return (
    <div className="my-past-plans">
      <h2 className="section-title">My Past Plans</h2>
      <p className="section-description">
        Access a historical archive of your previously submitted annual plans. Review past submissions to analyze trends, reference prior strategies, and ensure consistency in planning across years.
      </p>

      <Card className="past-plans-card">
        <div className="past-plans-header">
          <div className="filter-section">
            <label htmlFor="year-select">Select Year:</label>
            <Dropdown
              id="year-select"
              value={selectedYear}
              options={yearOptions}
              onChange={(e) => setSelectedYear(e.value)}
              placeholder="Select"
              className="year-dropdown"
            />
          </div>

          <div className="action-section">
            <Button
              label="View Plan"
              className="p-button-red view-plan-btn"
              icon="pi pi-eye"
              rounded
              onClick={handleViewPlan}
              disabled={!selectedYear}
            />
          </div>
        </div>
      </Card>
    </div>
  );
};

export default MyPastPlans;