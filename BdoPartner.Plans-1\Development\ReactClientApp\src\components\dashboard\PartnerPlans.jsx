import { Card } from 'primereact/card';
import { Button } from 'primereact/button';

const PartnerPlans = () => {
  return (
    <div className="partner-plans">
      <h2 className="section-title">Partner Plans</h2>

      {/* My 2025 Reviews Section */}
      <div className="reviews-section">
        <h3 className="subsection-title">My 2025 Reviews:</h3>
        <p className="subsection-description">
          Access partner plans for which you are responsible for reviewing enabling you to prioritize and manage your review responsibilities efficiently.
        </p>

        <Card className="reviews-card">
          <div className="reviews-content">
            <div className="status-info">
              <span className="status-label">Status:</span>
              <div className="status-counts">
                <span className="status-item">
                  <span className="status-text">Not Started:</span>
                  <span className="status-count not-started">5</span>
                </span>
                <span className="status-item">
                  <span className="status-text">In Progress:</span>
                  <span className="status-count in-progress">5</span>
                </span>
                <span className="status-item">
                  <span className="status-text">Completed:</span>
                  <span className="status-count completed">8</span>
                </span>
              </div>
            </div>

            <div className="reviews-actions">
              <Button
                label="View Plans"
                className="p-button-red view-plans-btn"
                icon="pi pi-arrow-right"
                rounded
              />
            </div>
          </div>
        </Card>
      </div>

      {/* All Partner Plans Section */}
      <div className="all-plans-section">
        <h3 className="subsection-title">All Partner Plans:</h3>
        <p className="subsection-description">
          Explore a comprehensive overview of all partner plans giving you full visibility into the planning progress across all service lines.
        </p>

        <Card className="all-plans-card">
          <div className="all-plans-content">
            <div className="status-info">
              <span className="status-label">Status:</span>
              <div className="status-counts">
                <span className="status-item">
                  <span className="status-text">Not Started:</span>
                  <span className="status-count not-started">00</span>
                </span>
                <span className="status-item">
                  <span className="status-text">In Progress:</span>
                  <span className="status-count in-progress">000</span>
                </span>
                <span className="status-item">
                  <span className="status-text">Completed:</span>
                  <span className="status-count completed">000</span>
                </span>
              </div>
            </div>

            <div className="all-plans-actions">
              <Button
                label="View Plans"
                className="p-button-red view-plans-btn"
                icon="pi pi-arrow-right"
                rounded
              />
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default PartnerPlans;