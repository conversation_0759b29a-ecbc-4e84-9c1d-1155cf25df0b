# Dashboard Components

This directory contains the dashboard components for the BDO Partner Planning application.

## Components

### Dashboard (pages/dashboard.jsx)
The main dashboard page that serves as the centralized hub for managing and monitoring partner-submitted plans.

**Features:**
- Welcome section with user greeting
- Organized layout with three main sections
- Responsive design following BDO design guidelines

### MyCurrentPlan.jsx
Displays the user's current 2025 plan information.

**Features:**
- Plan year and status display
- Reviewer information (Primary and Secondary)
- Review status indicator
- "View My Plan" action button
- Color-coded status indicators

### MyPastPlan.jsx (MyPastPlans)
Provides access to historical archive of previously submitted annual plans.

**Features:**
- Year selection dropdown
- "View Plan" action button
- Clean, minimal interface for historical data access

### PartnerPlans.jsx
Shows partner plans overview with two main sections.

**Features:**
- **My 2025 Reviews**: Plans assigned for review with status counts
- **All Partner Plans**: Comprehensive overview of all partner plans
- Status counters for Not Started, In Progress, and Completed
- "View Plans" action buttons for each section

## Styling

The dashboard components use BDO's design system with:
- **Primary Color**: #ED1A3B (BDO Red)
- **Background**: #f3f2f1 (Light gray)
- **Cards**: White background with subtle shadows
- **Typography**: Segoe UI font family
- **Status Colors**:
  - Not Started: #ED1A3B (Red)
  - In Progress: #ffc107 (Yellow)
  - Completed: #28a745 (Green)

## Navigation

The dashboard is accessible via:
- URL: `/dashboard`
- Navbar: "Dashboard" menu item (visible to authenticated users)

## Responsive Design

All components are responsive and adapt to mobile devices with:
- Flexible grid layouts
- Stacked elements on smaller screens
- Adjusted padding and spacing
- Maintained readability across all screen sizes

## Usage

```jsx
import Dashboard from '../pages/dashboard';

// Use in routing
<Route path="/dashboard" element={<Dashboard />} />
```

The dashboard automatically loads all three child components and displays them in a structured layout following the provided wireframe design.
