/* Partner Plan Questionnaire Component Styles */

.survey-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0;
  background: #f8f9fa;
}

/* Full-screen layout when navbar is hidden - only for questionnaire pages */
.partner-questionnaire-fullscreen .body-area {
  padding-top: 0 !important;
  width: 100% !important;
  margin: 0 !important;
}

/* Ensure survey container has proper spacing when navbar is hidden */
.partner-questionnaire-fullscreen .survey-container {
  max-width: none !important;
  margin: 0 80px !important;
  padding: 0 !important;
}

/* Adjust header and content spacing for fullscreen mode */
.partner-questionnaire-fullscreen .partner-planning-header {
  margin: 0 80px !important;
  /* padding: 20px 40px !important; */
}

.partner-questionnaire-fullscreen .partner-details-section {
  margin: 0 80px !important;
  /* padding: 20px 40px !important; */
}

/* Add spacing to survey wrapper - align with Partner Details section */
.partner-questionnaire-fullscreen .survey-wrapper {
  margin: 0 100px !important; 
  border-radius: 8px !important;
  overflow-x: hidden !important;
}

/* Ensure survey body content is properly aligned */
.partner-questionnaire-fullscreen .sv-body {
  padding: 20px 40px !important;
  margin: 0 !important;
}

/* Ensure survey pages align with container */
.partner-questionnaire-fullscreen .sv-page {
  padding: 0 !important;
  margin: 0 !important;
}

/* Align survey header and progress with container */
.partner-questionnaire-fullscreen .sv-header {
  padding: 20px 40px !important;
  margin: 0 !important;
}

/* Align wizard progress info with other sections */
.partner-questionnaire-fullscreen .wizard-progress-info {
  margin: 0 100px !important;
  border-radius: 8px !important;
}

.survey-container h2 {
  color: #ED1A3B;
  margin-bottom: 20px;
  font-weight: 600;
}

/* Header Section */
.partner-planning-header {
  background: white;
  padding-top:20px;
  padding-left: 20px;
  padding-right: 20px;
  /* border-bottom: 1px solid #dee2e6; */
  margin-bottom: 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.bdo-logo {
  display: flex;
  align-items: center;
  gap: 15px;
}

.bdo-logo-image {
  height: 30px;
  width: auto;
  margin-right: 5px;
}

.partner-planning-text {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.export-pdf-btn, .return-home-btn {
  padding: 8px 16px;
  border: 1px solid #ccc;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 5px;
}

.export-pdf-btn:hover, .return-home-btn:hover {
  background: #f8f9fa;
}

.form-description {
  color: #666;
  font-size: 0.95rem;
  line-height: 1.4;
}

/* Partner Details Section - Wireframe Layout */
.partner-details-section {
  background: white;
  padding-top: 10px;
  padding-bottom: 10px;
  padding-left:20px;
  padding-right: 20px;
  margin-bottom: 20px;
  /* border-bottom: 1px solid #dee2e6; */
}

.section-title {
  font-size: 1.0rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 20px;
  border-bottom: 2px solid #ED1A3B;
  padding-bottom: 8px;
}

/* Wireframe-style table layout */
.partner-details-table {
  display: flex;
  flex-direction: column;
  gap: 0;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  overflow: hidden;
}

.partner-details-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr 200px;
  /* border-bottom: 1px solid #dee2e6; */
  background: #f8f9fa;
}

.partner-details-row:last-child {
  border-bottom: none;
}

.detail-item {
  padding: 6px 8px;
  border-right: 1px solid #dee2e6;
  display: flex;
  flex-direction: column;
  gap: 4px;
  background: white;
}

.detail-item:last-child {
  border-right: none;
}

.detail-label {
  font-size: 0.80rem;
  font-weight: 600;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detail-value {
  font-size: 0.80rem;
  color: #333;
  font-weight: 500;
  min-height: 20px;
}

/* Status container styling */
.status-container {
  padding: 12px 15px;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-badge {
  background: #495057;
  color: white;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.status-text {
  font-size: 0.75rem;
  opacity: 0.9;
}

.status-value {
  font-size: 0.8rem;
  font-weight: 700;
}

/* Autosaved container */
.autosaved-container {
  padding: 12px 15px;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
}

.autosaved-info {
  font-size: 0.75rem;
  color: #666;
  font-style: italic;
  text-align: center;
}



/* Form Status Bar */
.form-status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  margin-bottom: 20px;
}

.form-info {
  display: flex;
  gap: 20px;
  align-items: center;
}

.form-status {
  font-weight: 600;
  color: #495057;
}

.form-year {
  color: #6c757d;
}

.form-submitted-date {
  color: #6c757d;
  font-size: 0.9em;
}

.form-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

/* Auto-save indicator */
.auto-save-indicator {
  text-align: center;
  padding: 10px;
}

.auto-save-indicator small {
  color: #6c757d;
  font-style: italic;
}

/* Status value colors for new design */
.status-value.draft {
  color: #856404;
}

.status-value.submitted {
  color: #0c5460;
}

.status-value.approved {
  color: #155724;
}

.status-value.rejected {
  color: #721c24;
}

/* Read-only message styling */
.readonly-message {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  color: #856404;
  padding: 15px 20px;
  border-radius: 8px;
  margin: 20px 0;
  display: flex;
  align-items: center;
  gap: 10px;
}

.readonly-message i {
  font-size: 1.2rem;
}

/* Survey wrapper styling */
.survey-wrapper {
  background: white;
  padding: 0;
  margin: 0;
}

/* Wizard progress info styling */
.wizard-progress-info {
  background: #f8f9fa;
  /* padding: 2.5px 5px; */
  border-bottom: 1px solid #dee2e6;
  margin: 0;
}

.progress-text {
  color: #666;
  font-size: 0.9rem;
}

.current-step {
  color: #ED1A3B;
  font-weight: 600;
}

.page-title {
  color: #333;
  font-weight: 500;
}

/* Additional styling improvements */
.info-value {
  font-weight: 500;
}

.partner-details-section:hover {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transition: box-shadow 0.3s ease;
}

/* Responsive Design for Wireframe Layout */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }

  /* Mobile wireframe layout */
  .partner-details-row {
    grid-template-columns: 1fr;
    gap: 0;
  }

  .detail-item {
    border-right: none;
    border-bottom: 1px solid #dee2e6;
    padding: 10px 15px;
  }

  .detail-item:last-child {
    border-bottom: none;
  }

  .status-container,
  .autosaved-container {
    padding: 10px 15px;
    border-bottom: 1px solid #dee2e6;
  }

  .status-container:last-child,
  .autosaved-container:last-child {
    border-bottom: none;
  }

  /* Mobile fullscreen adjustments */
  .partner-questionnaire-fullscreen .survey-container {
    margin: 0 30px !important;
  }

  .partner-questionnaire-fullscreen .partner-planning-header {
    margin: 0 30px !important;
    padding: 15px 20px !important;
  }

  .partner-questionnaire-fullscreen .partner-details-section {
    margin: 0 30px !important;
    padding: 15px 20px !important;
  }

  .partner-questionnaire-fullscreen .survey-wrapper {
    margin: 0 40px !important;
  }

  .partner-questionnaire-fullscreen .wizard-progress-info {
    margin: 0 40px !important;
  }
}

/* Tablet responsive design */
@media (max-width: 1024px) and (min-width: 769px) {
  .partner-questionnaire-fullscreen .survey-container {
    margin: 0 80px !important;
  }

  .partner-questionnaire-fullscreen .partner-planning-header {
    margin: 0 80px !important;
  }

  .partner-questionnaire-fullscreen .partner-details-section {
    margin: 0 80px !important;
  }

  .partner-questionnaire-fullscreen .survey-wrapper {
    margin: 0 100px !important;
  }

  .partner-questionnaire-fullscreen .wizard-progress-info {
    margin: 0 100px !important;
  }

  /* Tablet layout for partner details - stack some columns */
  .partner-details-row {
    grid-template-columns: 1fr 1fr 1fr 200px;
    gap: 0;
  }

  .partner-details-row .detail-item:nth-child(n+4) {
    grid-column: span 1;
  }
}

@media (max-width: 480px) {
  .partner-planning-header {
    padding: 15px;
  }

  .bdo-logo {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }

  .bdo-logo-image {
    height: 30px;
    margin-right: 0;
  }

  .partner-planning-text {
    font-size: 1.2rem;
  }

  .header-actions {
    flex-direction: column;
    width: 100%;
  }

  .export-pdf-btn, .return-home-btn {
    width: 100%;
    justify-content: center;
  }
}

/* Wizard Progress Indicator */
.wizard-progress-info {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 5px 5px;
  text-align: center;
}

.wizard-progress-info .progress-text {
  font-size: 1.1rem;
  color: #495057;
}

.wizard-progress-info .current-step {
  font-weight: 700;
  color: #ED1A3B;
  font-size: 1.2rem;
}

.wizard-progress-info .total-steps {
  font-weight: 600;
  color: #6c757d;
}

.wizard-progress-info .page-title {
  font-weight: 600;
  color: #343a40;
  margin-left: 5px;
}

/* Survey wrapper */
.survey-wrapper {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow-x: hidden;
}

/* Custom button styles to match PrimeReact */
.p-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  border-radius: 6px;
  border: 1px solid transparent;
  cursor: pointer;
  text-decoration: none;
  transition: all 0.2s;
  font-weight: 500;
}

.p-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.p-button-outlined {
  background: transparent;
  color: #ED1A3B;
  border-color: #ED1A3B;
}

.p-button-outlined:hover:not(:disabled) {
  background: #ED1A3B;
  color: white;
}

.p-button-icon {
  margin-right: 0.5rem;
}

.p-button-label {
  font-weight: 500;
}

/* Status indicators */
.status-draft {
  color: #ffc107;
  font-weight: 600;
}

.status-submitted {
  color: #28a745;
  font-weight: 600;
}

.status-approved {
  color: #007bff;
  font-weight: 600;
}

.status-rejected {
  color: #dc3545;
  font-weight: 600;
}

.status-reopened {
  color: #fd7e14;
  font-weight: 600;
}

.status-closed {
  color: #6c757d;
  font-weight: 600;
}

/* Loading and error states */
.loading-container,
.error-container {
  text-align: center;
  padding: 40px 20px;
}

.error-container .p-message {
  margin-bottom: 20px;
}

/* Responsive design */
@media (max-width: 768px) {
  .survey-container {
    padding: 10px;
  }

  .form-status-bar {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .form-info {
    justify-content: center;
  }

  .form-actions {
    justify-content: center;
  }

  /* Mobile wizard adjustments */
  .wizard-progress-info {
    padding: 12px 15px;
  }

  .wizard-progress-info .progress-text {
    font-size: 1rem;
  }

  .wizard-progress-info .current-step {
    font-size: 1.1rem;
  }

  .wizard-progress-info .page-title {
    display: block;
    margin-top: 5px;
    margin-left: 0;
  }

  /* Mobile navigation buttons */
  .partner-plan-survey .sv-nav {
    flex-direction: column;
    gap: 15px;
  }

  .partner-plan-survey .sv-btn {
    width: 100% !important;
    min-width: auto !important;
  }

  /* Mobile progress bar */
  .partner-plan-survey .sv-progress {
    height: 8px;
    margin-bottom: 20px;
  }

  /* Mobile page titles */
  .partner-plan-survey .sv-page__title {
    font-size: 1.3rem;
  }
}

/* Survey.js custom overrides for better integration */
.sv-root {
  font-family: inherit;
}

.sv-header {
  background: #ED1A3B;
  color: white;
  padding: 20px;
  margin-bottom: 20px;
}

.sv-body {
  padding: 20px;
}

.sv-footer {
  padding: 20px;
  border-top: 1px solid #dee2e6;
  background: #f8f9fa;
}

/* Wizard-specific styles */
.partner-plan-survey .sv-progress {
  background: #e9ecef;
  height: 12px;
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 30px;
}

.partner-plan-survey .sv-progress__bar {
  background: linear-gradient(90deg, #ED1A3B 0%, #AF273C 100%);
  height: 100%;
  transition: width 0.4s ease;
  border-radius: 6px;
}

/* Page navigation */
.partner-plan-survey .sv-page {
  min-height: 400px;
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateX(20px); }
  to { opacity: 1; transform: translateX(0); }
}

/* Page title styling */
.partner-plan-survey .sv-page__title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #ED1A3B;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #ED1A3B;
}

/* Page description */
.partner-plan-survey .sv-page__description {
  font-size: 1rem;
  color: #6c757d;
  margin-bottom: 25px;
  line-height: 1.5;
}

/* Navigation buttons */
.partner-plan-survey .sv-nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #dee2e6;
}

.partner-plan-survey .sv-btn {
  background: #ED1A3B !important;
  border-color: #ED1A3B !important;
  color: white !important;
  padding: 12px 24px !important;
  font-weight: 600 !important;
  border-radius: 6px !important;
  transition: all 0.2s !important;
  min-width: 120px !important;
}

.partner-plan-survey .sv-btn:hover:not(:disabled) {
  background: #AF273C !important;
  border-color: #AF273C !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(237, 26, 59, 0.3) !important;
}

.partner-plan-survey .sv-btn:disabled {
  opacity: 0.6 !important;
  cursor: not-allowed !important;
  transform: none !important;
  box-shadow: none !important;
}

/* Previous button styling */
.partner-plan-survey .sv-btn--prev {
  background: transparent !important;
  color: #ED1A3B !important;
  border: 2px solid #ED1A3B !important;
}

.partner-plan-survey .sv-btn--prev:hover:not(:disabled) {
  background: #ED1A3B !important;
  color: white !important;
}

/* Page indicator */
.partner-plan-survey .sv-page-indicator {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 0.9rem;
  color: #6c757d;
}

.partner-plan-survey .sv-page-indicator .current-page {
  font-weight: 600;
  color: #ED1A3B;
}

/* Question numbering within pages */
.partner-plan-survey .sv-question__num {
  background: #ED1A3B;
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: 600;
  margin-right: 10px;
}

/* Progress bar customization */
.sv-progress {
  background: #e9ecef;
  height: 8px;
  border-radius: 4px;
  overflow: hidden;
}

.sv-progress__bar {
  background: #ED1A3B;
  height: 100%;
  transition: width 0.3s ease;
}

/* Custom complete button styling */
.sv-btn {
  background: #ED1A3B !important;
  border-color: #ED1A3B !important;
  color: white !important;
  padding: 12px 24px !important;
  font-weight: 600 !important;
  border-radius: 6px !important;
  transition: all 0.2s !important;
}

.sv-btn:hover {
  background: #AF273C !important;
  border-color: #AF273C !important;
}

.sv-btn:disabled {
  opacity: 0.6 !important;
  cursor: not-allowed !important;
}

/* Read-only survey styles */
.survey-readonly {
  opacity: 0.8;
}

.survey-readonly .sv-body {
  background: #f8f9fa;
}

.survey-readonly .sv-footer {
  display: none; /* Hide submit button in read-only mode */
}

/* Read-only form message */
.readonly-message {
  background: #e9ecef;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 15px;
  margin-bottom: 20px;
  text-align: center;
  color: #495057;
}

.readonly-message .pi {
  margin-right: 8px;
  color: #6c757d;
}

.survey-wrapper .sd-progress-buttons__connector,
.survey-wrapper .sd-progress-buttons__page-description,
.survey-wrapper .sd-progress-buttons__button {
  display: none !important;
}

.survey-wrapper .sd-progress-buttons__list-element--current .sd-progress-buttons__page-title {
  color: #ed1a3b !important;
  text-shadow: 0 0 .25px currentColor;
}


.survey-wrapper .sd-progress-buttons__page-title {
  border-bottom-color: #ccc;
  border-bottom-style: solid;
  padding-bottom: 5px;
}

.survey-wrapper .sd-progress-buttons__list-element--current .sd-progress-buttons__page-title {
  border-bottom-color: #ed1a3b;
  border-bottom-style: solid;
  padding-bottom: 5px;
}

.survey-wrapper .sd-progress-buttons__container-center .sd-progress-buttons .sd-progress-buttons--top .sd-progress-buttons--with-titles {
  padding-top: 15px;
  padding-bottom: 10px;
}

.survey-wrapper .sd-page .sd-body__page {
  margin-bottom: 20px;
}

.survey-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  /* Full viewport height; adjust if parent isn't full screen */
  overflow: hidden;
  /* Prevent overflow from parent */
}

.survey-wrapper {
  flex-grow: 1;
  /* Grow to fill remaining space */
  overflow-y: auto;
  /* Add scrollbar if content overflows */
  max-height: none;
  /* Remove or override the 500px limit */
}