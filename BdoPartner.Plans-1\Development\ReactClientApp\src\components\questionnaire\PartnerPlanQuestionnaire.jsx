import { useState, useEffect, useContext, useCallback } from "react";
import { Survey } from "survey-react-ui";
import { Model } from "survey-core";
import { SurveyPDF } from "survey-pdf";
import { <PERSON><PERSON> } from 'primereact/button';
import { useNavigate } from 'react-router-dom';
import { AuthContext } from "../../core/auth/components/authProvider";
import { messageService } from "../../core/message/messageService";
import { useLoadingControl } from "../../core/loading/hooks/useLoadingControl";
import formService from "../../services/formService";
import { FormStatus, getFormStatusName, getFormStatusClass } from "../../core/enumertions/formStatus";
import http from '../../core/http/httpClient';
import APP_CONFIG from "../../core/config/appConfig";

// Import the latest Survey.js CSS themes
import "survey-core/survey-core.css";
import "../../survey-v2-styles.css"; // Import our custom Survey.js v2 styles
import "./PartnerPlanQuestionnaire.css"; // Import component-specific styles

export const PartnerPlanQuestionnaire = ({ year, viewer_role, backHandler }) => {
  const [surveyModel, setSurveyModel] = useState(null);
  const [questionnaire, setQuestionnaire] = useState(null);
  const [form, setForm] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [hasLoaded, setHasLoaded] = useState(false); // Track if data has been loaded
  const [currentPageInfo, setCurrentPageInfo] = useState({ pageNo: 0, pageCount: 0, pageTitle: '' });

  const authService = useContext(AuthContext);
  const navigate = useNavigate();

  // Disable loading interceptor for survey component
  useLoadingControl('survey', true);

  // Reset hasLoaded when year changes to trigger data reload
  useEffect(() => {
    setHasLoaded(false);
  }, [year]);

  // Use useEffect directly instead of useCallback to avoid dependency issues
  useEffect(() => {
    // Prevent multiple calls if already loaded or not authenticated
    if (hasLoaded || !authService || !authService.isAuthenticated()) {
      return;
    }

    const loadData = async () => {
      try {
        console.log('🔄 Loading my plan data - consolidated API call');
        setLoading(true);
        setError(null);

        // Get all plan data in a single API call
        const targetYear = year ? parseInt(year) : new Date().getFullYear();
        const planData = await formService.getMyPlan(targetYear);

        if (planData) {
          // Set all the data from the consolidated response
          setQuestionnaire(planData.questionnaire);
          setForm(planData.form);

          // Debug: Log the form data to verify partner details are received
          console.log('📋 Form data received:', {
            partnerName: planData.form?.partnerName,
            serviceLine: planData.form?.serviceLine,
            subServiceLine: planData.form?.subServiceLine,
            location: planData.form?.location,
            primaryReviewerName: planData.form?.primaryReviewerName,
            secondaryReviewerName: planData.form?.secondaryReviewerName,
            employeeId: planData.form?.employeeId,
            department: planData.form?.department
          });

          // Get existing answers if available
          const existingAnswers = planData.userAnswer?.answer;

          // Parse the DefinitionJson to create the survey model
          if (planData.questionnaire.definitionJson) {
            try {
              console.log("Raw survey definition JSON:", planData.questionnaire.definitionJson);
              const surveyJson = JSON.parse(planData.questionnaire.definitionJson);
              console.log("Parsed survey JSON:", surveyJson);

              // Validate that the parsed JSON is a valid object
              if (!surveyJson || typeof surveyJson !== 'object') {
                throw new Error("Invalid survey JSON: not a valid object");
              }

              // Ensure the survey JSON has required properties
              if (!surveyJson.pages && !surveyJson.elements) {
                throw new Error("Invalid survey JSON: missing pages or elements");
              }

              const survey = new Model(surveyJson);
              console.log("Created survey model:", survey);
              console.log("Survey pages:", survey.pages);
              console.log("Survey pageCount:", survey.pageCount);

              // Validate that the survey was created successfully
              if (!survey) {
                throw new Error("Failed to create survey model");
              }

              // Validate that the survey has pages
              if (!survey.pages || survey.pages.length === 0) {
                throw new Error("Survey has no pages");
              }

              // Load existing answers if available
              if (existingAnswers) {
                try {
                  const parsedAnswers = JSON.parse(existingAnswers);
                  survey.data = parsedAnswers;
                } catch (parseError) {
                  console.warn("Failed to parse existing answers:", parseError);
                }
              }

                // Configure survey settings for wizard mode
                survey.clearInvisibleValues = false;
                // survey.allowShowPreview = true;
                survey.showPreviewBeforeComplete = "showAllQuestions";
                survey.showProgressBar = true;
                survey.progressBarLocation = "top";
                // survey.showQuestionNumbers = "onPage";
                survey.questionsOnPageMode = "standard"; // Enable multi-page wizard mode
                survey.showPageNumbers = false;
                survey.showPageTitles = true;
                survey.pagePrevText = "Previous";
                survey.pageNextText = "Next";
                survey.goNextPageAutomatic = false; // Don't auto-advance pages

              // Check if form is editable
              const formEditable = planData.isEditable;

              if (!formEditable) {
                // Set survey to read-only mode
                survey.mode = "display";
                survey.showPreviewBeforeComplete = "noPreview";
              } else {
                // Customize button text for editable forms
                survey.completeText = "Submit Partner Plan";
                survey.previewText = "Preview";
                survey.editText = "Previous"; // Change Edit button text to Previous for clarity
                // Ensure navigation buttons are shown in preview mode
                survey.showPrevButton = true;
                // Ensure preview mode shows navigation buttons
                survey.showPreviewBeforeComplete = "showAllQuestions";
                // survey.allowShowPreview = true;
              }
                // Add custom CSS classes
                survey.css = {
                  ...survey.css,
                  root: `sv-root partner-plan-survey ${!formEditable ? 'survey-readonly' : ''}`,
                  header: "sv-header",
                  body: "sv-body",
                  footer: "sv-footer"
                };

              // Apply modern theme for better styling
              // Apply BDO red theme using modern Survey.js v2.2.2 approach
              survey.applyTheme({
                "themeName": "default-light",
                "colorPalette": "light",
                "isPanelless": false,
                // Custom CSS variables for BDO branding (equivalent to old StylesManager)
                "cssVariables": {
                  "--sjs-primary-backcolor": "#ED1A3B",           // Main color
                  "--sjs-primary-forecolor": "#FFFFFF",           // Text on primary
                  "--sjs-primary-backcolor-light": "#F5E6EA",     // Light variant
                  "--sjs-primary-backcolor-dark": "#AF273C",      // Hover color
                  "--sjs-secondary-backcolor": "#ED1A3B",         // Secondary color
                  "--sjs-secondary-forecolor": "#FFFFFF",         // Text on secondary
                  "--sjs-general-backcolor-dim": "#F3F2F1",       // Background
                  "--sjs-border-default": "#959597",              // Border color
                  "--sjs-border-light": "#D4D4D4",                 // Light border
                  "--ctr-font-family": "primeicons",
                  "--lbr-font-family": "primeicons",
                  "--sjs-font-family": "primeicons",
                  "--font-family": "primeicons",
                  "--sjs-font-pagetitle-family": "primeicons",
                  "--sjs-default-font-family": "primeicons",

                  "--sjs-article-font-default": "24",

                  "--sd-base-padding": "20px",
                  "--sd-base-vertical-padding": "calc(1.4 * var(--sjs-base-unit, var(--base-unit, 8px)))",
                  "--sd-page-vertical-padding": "calc(1.2 * var(--sjs-base-unit, var(--base-unit, 8px)))"
                }
              });

                // Create a completion handler for form submission
                const completionHandler = async (sender, options) => {
                  // Prevent default completion
                  options.allowComplete = false;

                  // Show confirmation dialog
                  const confirmed = window.confirm(
                    "Are you sure you want to submit your partner plan? Once submitted, you may not be able to make changes."
                  );

                  if (!confirmed) {
                    return;
                  }

                  try {
                    // Save the final answers
                    await formService.saveUserAnswer(planData.form.id, JSON.stringify(sender.data));

                    // Submit the form
                    await formService.submitForm(planData.form.id);

                    console.log("Survey completed and submitted successfully");
                    messageService.successToast("Partner plan submitted successfully!");

                    // Update form status locally
                    setForm(prev => ({
                      ...prev,
                      status: FormStatus.Submitted,
                      statusString: getFormStatusName(FormStatus.Submitted)
                    }));

                    // Complete the survey
                    sender.doComplete();

                  } catch (error) {
                    console.error("Error handling survey completion:", error);
                    messageService.errorToast("Failed to submit partner plan. Please try again.");
                  }
                };

                // Set up survey completion handler
                survey.onComplete.add(completionHandler);

              // Set up immediate save on value change (following Retirement project pattern)
              survey.onValueChanged.add(surveyValueChanged);

                // Set up page change handler for wizard navigation
                survey.onCurrentPageChanged.add((sender, options) => {
                  handlePageChanged(sender, options);
                });

                // Add page validation handler
                survey.onCurrentPageChanging.add((_, options) => {
                  // You can add custom validation logic here if needed
                  console.log(`Navigating from page ${options.oldCurrentPage?.name} to ${options.newCurrentPage?.name}`);
                });

                // Add preview mode handler to ensure navigation buttons are shown
                // Note: onShowPreview might not be available in all SurveyJS versions
                if (survey.onShowPreview) {
                  survey.onShowPreview.add((sender) => {
                    console.log("Survey entered preview mode");
                    // Ensure navigation buttons are visible in preview mode
                    sender.showNavigationButtons = "both";
                    sender.showPrevButton = true;
                  });
                }

              // Set completion message
              survey.completedHtml = `<h3>Thank you for completing ${survey.title || planData.questionnaire.name}!</h3>`;

              setSurveyModel(survey);

              // Initialize page info for wizard
              if (survey.pageCount > 1) {
                setCurrentPageInfo({
                  pageNo: survey.currentPageNo,
                  pageCount: survey.pageCount,
                  pageTitle: survey.currentPage?.title || survey.currentPage?.name || ''
                });
              }

              setHasLoaded(true); // Mark as loaded
            } catch (parseError) {
              console.error("Error parsing survey JSON:", parseError);
              setError("Failed to load survey form. Invalid survey definition.");
            }
          } else {
            setError("No survey definition found for the current year.");
          }
        } else {
          setError(planData?.message || "Failed to load partner plan data.");
        }
      } catch (err) {
        console.error("Error loading my plan:", err);
        setError(err.message || "Failed to load partner plan. Please try again later.");
      } finally {
        setLoading(false);
      }
    };

    loadData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [hasLoaded, year]); // Depend on hasLoaded and year to reload when year changes

  // Create a separate function for retry that can be called from the button
  const retryLoadQuestionnaire = useCallback(() => {
    setHasLoaded(false); // This will trigger the useEffect above
  }, []);

  // Handle return to home navigation
  const handleReturnToHome = useCallback(() => {
    navigate('/home');
  }, [navigate]);

  // Handle PDF export
  const handleExportToPDF = useCallback(async () => {
    if (!surveyModel) {
      messageService.errorToast("Survey not loaded. Please try again.");
      return;
    }

    try {
      // Show loading message
      messageService.infoToast("Generating PDF... Please wait.");

      // Get the survey JSON and ensure it's properly formatted
      const surveyJson = surveyModel.toJSON();

      // Validate survey JSON structure
      if (!surveyJson || !surveyJson.pages || surveyJson.pages.length === 0) {
        throw new Error("Invalid survey structure for PDF export");
      }

      // Create a minimal, clean survey JSON for PDF export
      const cleanSurveyJson = {
        title: surveyJson.title || "Partner Planning Survey",
        pages: surveyJson.pages.filter(page => page && page.elements && page.elements.length > 0).map((page, pageIndex) => ({
          name: page.name || `page${pageIndex + 1}`,
          title: page.title || page.name || `Page ${pageIndex + 1}`,
          elements: page.elements.filter(element => element && element.type).map((element, elementIndex) => {
            const cleanElement = {
              type: element.type,
              name: element.name || `question${elementIndex + 1}`,
              title: element.title || element.name || `Question ${elementIndex + 1}`
            };

            // Add common properties if they exist
            if (element.isRequired) cleanElement.isRequired = element.isRequired;
            if (element.choices) cleanElement.choices = element.choices;
            if (element.rateMin !== undefined) cleanElement.rateMin = element.rateMin;
            if (element.rateMax !== undefined) cleanElement.rateMax = element.rateMax;
            if (element.rateStep !== undefined) cleanElement.rateStep = element.rateStep;

            return cleanElement;
          })
        }))
      };

      // Create PDF export options with minimal configuration
      const options = {
        fontSize: 11,
        margins: {
          left: 15,
          right: 15,
          top: 20,
          bot: 20
        },
        format: 'a4'
      };

      // Create SurveyPDF instance with cleaned JSON
      const surveyPDF = new SurveyPDF(cleanSurveyJson, options);

      // Set the survey data for the PDF if available
      const surveyData = surveyModel.data || {};
      if (Object.keys(surveyData).length > 0) {
        // Clean the data to ensure all values are serializable and safe
        const cleanData = {};
        Object.keys(surveyData).forEach(key => {
          const value = surveyData[key];
          if (value !== undefined && value !== null && key && typeof key === 'string') {
            if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {
              cleanData[key] = value;
            } else if (Array.isArray(value)) {
              cleanData[key] = value.join(', ');
            } else if (typeof value === 'object') {
              cleanData[key] = JSON.stringify(value);
            }
          }
        });
        surveyPDF.data = cleanData;
      }

      // Generate and download the PDF
      const partnerName = form?.partnerName ?
        form.partnerName.replace(/[^a-zA-Z0-9\s]/g, '').replace(/\s+/g, '_').substring(0, 50) :
        'Survey';
      const fileName = `Partner_Plan_${partnerName}_${new Date().toISOString().split('T')[0]}.pdf`;

      console.log("Attempting to generate PDF with filename:", fileName);
      console.log("Survey data being exported:", surveyPDF.data);

      // Try to generate the PDF
      try {
        await surveyPDF.save(fileName);
        console.log("PDF save method completed successfully");
        messageService.successToast("PDF exported successfully!");
      } catch (saveError) {
        console.error("Error in surveyPDF.save():", saveError);

        // Fallback: Try to generate PDF as blob and download manually
        try {
          console.log("Attempting fallback PDF generation...");
          const pdfBlob = await surveyPDF.raw();

          if (pdfBlob) {
            // Create download link manually
            const url = window.URL.createObjectURL(pdfBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = fileName;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);

            console.log("Fallback PDF download completed");
            messageService.successToast("PDF exported successfully using fallback method!");
          } else {
            throw new Error("Failed to generate PDF blob");
          }
        } catch (fallbackError) {
          console.error("Fallback PDF generation also failed:", fallbackError);
          throw saveError; // Re-throw original error
        }
      }

    } catch (error) {
      console.error("Error exporting to PDF:", error);
      console.error("Survey model data:", surveyModel?.data);
      console.error("Survey model JSON:", surveyModel?.toJSON());

      // Provide more specific error messages
      let errorMessage = "Failed to export PDF. ";
      if (error.message && error.message.includes('indexOf')) {
        errorMessage += "There may be an issue with the survey data format.";
      } else if (error.message) {
        errorMessage += error.message;
      } else {
        errorMessage += "Please try again or contact support if the issue persists.";
      }

      messageService.errorToast(errorMessage);
    }
  }, [surveyModel, form]);

  // Alternative PDF export method using browser's print functionality
  const handleAlternativePDFExport = useCallback(() => {
    if (!surveyModel) {
      messageService.errorToast("Survey not loaded. Please try again.");
      return;
    }

    try {
      // Create a new window with the survey content
      const printWindow = window.open('', '_blank');
      if (!printWindow) {
        messageService.errorToast("Please allow pop-ups for PDF export to work.");
        return;
      }

      // Get survey data
      const surveyData = surveyModel.data || {};
      const surveyJson = surveyModel.toJSON();

      // Create HTML content for printing
      let htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <title>Partner Plan - ${form?.partnerName || 'Survey'}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { color: #ED1A3B; border-bottom: 2px solid #ED1A3B; padding-bottom: 10px; margin-bottom: 20px; }
            .partner-info { background: #f8f9fa; padding: 15px; margin-bottom: 20px; border-radius: 5px; }
            .question { margin-bottom: 15px; }
            .question-title { font-weight: bold; color: #333; margin-bottom: 5px; }
            .answer { background: #fff; padding: 8px; border: 1px solid #ddd; border-radius: 3px; }
            .page-title { color: #ED1A3B; font-size: 18px; margin: 20px 0 10px 0; border-bottom: 1px solid #ED1A3B; }
            @media print { body { margin: 0; } }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>BDO Partner Planning Tool - 2025</h1>
          </div>
          <div class="partner-info">
            <strong>Partner:</strong> ${form?.partnerName || 'N/A'}<br>
            <strong>Service Line:</strong> ${form?.serviceLine || 'N/A'}<br>
            <strong>Location:</strong> ${form?.location || 'N/A'}<br>
            <strong>Generated:</strong> ${new Date().toLocaleDateString()}
          </div>
      `;

      // Add survey content
      if (surveyJson.pages) {
        surveyJson.pages.forEach(page => {
          if (page.elements && page.elements.length > 0) {
            htmlContent += `<div class="page-title">${page.title || page.name || 'Survey Page'}</div>`;

            page.elements.forEach(element => {
              const answer = surveyData[element.name] || 'No answer provided';
              htmlContent += `
                <div class="question">
                  <div class="question-title">${element.title || element.name}</div>
                  <div class="answer">${Array.isArray(answer) ? answer.join(', ') : answer}</div>
                </div>
              `;
            });
          }
        });
      }

      htmlContent += `
        </body>
        </html>
      `;

      // Write content to new window and trigger print
      printWindow.document.write(htmlContent);
      printWindow.document.close();

      // Wait for content to load, then print
      printWindow.onload = () => {
        setTimeout(() => {
          printWindow.print();
          printWindow.close();
        }, 500);
      };

      messageService.infoToast("Print dialog opened. Choose 'Save as PDF' in your browser's print options.");

    } catch (error) {
      console.error("Error with alternative PDF export:", error);
      messageService.errorToast("Failed to open print dialog. Please try the main PDF export.");
    }
  }, [surveyModel, form]);

  const handlePageChanged = (sender, options) => {
    console.log("Survey page changed:", options);

    // Update page information for better UX
    if (sender && sender.currentPage) {
      const pageInfo = {
        pageNo: sender.currentPageNo,
        pageCount: sender.pageCount,
        pageTitle: sender.currentPage.title || sender.currentPage.name
      };
      setCurrentPageInfo(pageInfo);
      console.log(`Now on page: ${pageInfo.pageTitle}`);
      console.log(`Page ${pageInfo.pageNo + 1} of ${pageInfo.pageCount}`);
    }
  };

  // Save questionnaire data without submitting (following Retirement project pattern)
  const saveQuestionnaire = (callback) => {
    function verifyAnswer() {
      var previousFormIssue = form.issue;
      form.issue = false;

      if (form.issue !== previousFormIssue || form.status === 0) {
        // need to save the form
        if (form.status === 0) {
          form.status = 1;
        }

        http.put(APP_CONFIG.apiDomain + "/api/Form/UpdateForm", form, {
          headers: {
            'Content-Type': 'application/json'
          }
        }).then((response) => {
          // update form with response
          if (response.data && response.data.item) {
            setForm(response.data.item);
          }
          callback && callback();
        }).catch((error) => {
          console.warn("Form update failed:", error);
          callback && callback();
        });
      } else {
        callback && callback();
      }
    }

    // Save survey data to user answer
    form.userAnswer.answer = JSON.stringify(surveyModel.data);

    http.put(APP_CONFIG.apiDomain + "/api/UserAnswer/UpdateUserAnswer", form.userAnswer, {
      headers: {
        'Content-Type': 'application/json'
      }
    }).then((response) => {
      // update user answer with response
      if (response.data && response.data.item) {
        const savedUserAnswer = response.data.item;
        setForm(prev => ({
          ...prev,
          userAnswer: savedUserAnswer
        }));
      }
      verifyAnswer(callback);
    }).catch((error) => {
      console.warn("User answer save failed:", error);
      verifyAnswer(callback);
    });
  };

  // Survey value changed handler - immediate save (following Retirement project pattern)
  function surveyValueChanged(sender, options) {
    console.log("Survey value changed:", options);

    // Add any special business logic here if needed (like in Retirement project)
    // For now, just save immediately
    saveQuestionnaire();
  }

  // Admin reopen function following Retirement project pattern
  const adminReopen = () => {
    function returnForm(confirmed) {
      if (!confirmed) {
        return;
      }
      http.put(APP_CONFIG.apiDomain + "/api/Form/AdminReopenForms", [form.id], {
        headers: {
          'Content-Type': 'application/json'
        }
      }).then((response) => {
        // popup success if OK
        if (response.data.resultStatus === 1) {
          messageService.successDialog("Questionnaire reopened.");
          backHandler && backHandler();
        } else {
          messageService.errorDialog(response.data.message);
        }
      }).catch(() => {
        messageService.errorDialog("Failed to reopen questionnaire.");
      });
    }

    const surveyTitle = questionnaire ? questionnaire.name : "Partner Plan";
    const partnerName = form ? form.partnerName : "Partner";
    messageService.confirmDialog(`Are you sure to reopen the ${surveyTitle} to ${partnerName}`, returnForm);
  };

  if (!authService || !authService.isAuthenticated()) {
    return (
      <div className="survey-container">
          <h2>Partner Plans</h2>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="survey-container">
        <div className="p-4">
          <h2>Partner Plans</h2>
          <p>Loading plan...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div id ="surveyContainer">
        <div>
          <div className="p-message p-message-error">
            <div className="p-message-wrapper">
              <span className="p-message-icon pi pi-times-circle"></span>
              <div className="p-message-text">
                <span className="p-message-summary">Error</span>
                <div className="p-message-detail">{error}</div>
              </div>
            </div>
          </div>
          <button
            className="p-button p-component p-button-outlined mt-3"
            onClick={retryLoadQuestionnaire}
          >
            <span className="p-button-icon pi pi-refresh"></span>
            <span className="p-button-label">Retry</span>
          </button>
        </div>
      </div>
    );
  }

  if (!surveyModel) {
    return (
      <div className="survey-container">
        <div className="p-4">
          <h2>Partner Plans</h2>
          <p>No plan available at this time.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="survey-container">
        {/* Header Section */}
        <div className="partner-planning-header">
          <div className="header-content">
            <div className="bdo-logo">
              <img
                alt="BDO logo"
                src={`${APP_CONFIG.basePath}/logo.png`}
                onError={(e) => (e.target.src = `/images/BDO.png`)}
                className="bdo-logo-image"
              />
              <span className="partner-planning-text">2025 Partner Planning Tool</span>
            </div>
            <div className="header-actions">
              <button className="export-pdf-btn" onClick={handleExportToPDF}>
                <i className="pi pi-file-pdf"></i>
                Export to PDF
              </button>
              <button className="return-home-btn" onClick={handleReturnToHome}>
                Return to Home
              </button>
            </div>
          </div>
          <div className="form-description">
            This form guides partners in creating a personal plan to achieve the firm's 2025 strategy. Complete all sections to set qualitative and quantitative targets.
          </div>
        </div>

        {/* Partner Details Section - Wireframe Layout */}
        <div className="partner-details-section">
          <h3 className="section-title">Partner Details</h3>
          <div className="partner-details-table">
            <div className="partner-details-row">
              <div className="detail-item">
                <span className="detail-label">Partner Name:</span>
                <span className="detail-value">{form?.partnerName || ''}</span>
              </div>
              <div className="detail-item">
                <span className="detail-label">Service Line:</span>
                <span className="detail-value">{form?.serviceLine || ''}</span>
              </div>
              <div className="detail-item">
                <span className="detail-label">Sub-service Line:</span>
                <span className="detail-value">{form?.subServiceLine || ''}</span>
              </div>
              <div className="detail-item">
                <span className="detail-label">Location:</span>
                <span className="detail-value">{form?.location || ''}</span>
              </div>
              <div className="detail-item">
                <span className="detail-label">Primary Reviewer:</span>
                <span className="detail-value">{form?.primaryReviewerName || ''}</span>
              </div>
              <div className="detail-item">
                <span className="detail-label">Secondary Reviewer:</span>
                <span className="detail-value">{form?.secondaryReviewerName || ''}</span>
              </div>
              <div className="status-container">
                <div className="status-badge">
                  <span className="status-text">STATUS: </span>
                  <span className={`status-value ${getFormStatusClass(form?.status)}`}>
                    {form?.statusString || getFormStatusName(form?.status) || 'DRAFT'}
                  </span>
                </div>
                <div className="autosaved-container">
                {form?.partnerSubmittionDate ? (
                  <div className="autosaved-info">
                    Autosaved: {new Date(form.partnerSubmittionDate).toLocaleDateString()} at {new Date(form.partnerSubmittionDate).toLocaleTimeString()}
                  </div>
                ) : (
                  <div className="autosaved-info">
                    {/* Show blank when no autosave date available */}
                  </div>
                )}
              </div>
              </div>
            </div>            
          </div>
        </div>

        {/* Read-only message */}
        {form && (form.status !== FormStatus.Draft && form.status !== FormStatus.Reopened) && (
          <div className="readonly-message">
            <i className="pi pi-info-circle"></i>
            This form has been {getFormStatusName(form.status).toLowerCase()} and is now read-only.
            {form.status === FormStatus.Submitted && " Your submission is being reviewed."}
            {form.status === FormStatus.Approved && " Your plan has been approved."}
            {form.status === FormStatus.Rejected && " Please contact your reviewer for feedback."}
          </div>
        )}

        {/* Wizard Progress Indicator */}
        {surveyModel && surveyModel.pageCount > 1 && (
          <div className="wizard-progress-info">
            <div className="progress-text">
              <span className="current-step">Step {currentPageInfo.pageNo + 1}</span>
              <span className="total-steps"> of {currentPageInfo.pageCount}</span>
              {currentPageInfo.pageTitle && (
                <span className="page-title">: {currentPageInfo.pageTitle}</span>
              )}
            </div>
          </div>
        )}

        <div className="survey-wrapper mt-4">
          <Survey
            model={surveyModel}
          />
        </div>

        {/* Auto-save indicator - only show for editable forms */}
        {form && (form.status === FormStatus.Draft || form.status === FormStatus.Reopened) && (
          <div className="auto-save-indicator mt-2">
            <small className="text-muted">
              <i className="pi pi-info-circle mr-1"></i>
              Your progress is automatically saved as you work
            </small>
          </div>
        )}

        {/* Action buttons section following Retirement project pattern */}
        <div className="flexbox flexbox--flex-end" style={{marginTop: "30px", marginRight:"17px"}}>
          {viewer_role === 'PPAdministrator' && (
            <>
              <Button className="action"
                disabled={!(form && form.status !== FormStatus.NotStarted && form.status !== FormStatus.Draft && form.status !== FormStatus.Reopened)}
                label="Reopen"
                style={{ padding: '15px 20px' }}
                onClick={adminReopen} />
            </>
          )}
          {backHandler && (
            <>
              <Button className="action"
                label="Exit"
                style={{ padding: '15px 20px', marginLeft: '10px'}}
                onClick={() => {backHandler();}} />
            </>
          )}
        </div>
    </div>
  );
};

export default PartnerPlanQuestionnaire;
