# Form Creator Custom Properties Implementation

## Overview

This implementation adds custom properties to the SurveyJS Form Creator to enable mapping form questions to partner reference data columns and specifying custom export column names.

## Features Added

### 1. Custom Properties

#### mapFrom Property
- **Type**: Dropdown
- **Purpose**: Maps form questions to partner reference data columns
- **Data Source**: PartnerReferenceDataMetaDetails.ColumnName values
- **Location**: Custom Settings category in Property Grid

#### exportColumnName Property  
- **Type**: Text Input
- **Purpose**: Specifies custom column names for data export
- **Location**: Custom Settings category in Property Grid

### 2. Custom Settings Category
- **Icon**: Custom gear/settings SVG icon
- **Styling**: BDO-themed with red accent color (#ED1A3B)
- **Organization**: Groups custom properties together

## Implementation Details

### API Integration
- **Frontend Service**: `partnerReferenceDataUploadService.js`
- **Enhanced Method**: `getAvailableColumnNamesForMapping(year, includeCyclePrefixes)`
- **Backend API**: `GetAvailableColumnNamesForFormCreator(year, includeCyclePrefixes)`
- **Year-Specific Data**: Filters columns based on questionnaire year
- **Cycle Disambiguation**: Adds prefixes like "Plan_", "Mid_", "End_" to column names
- **Fallback**: `getFallbackColumnNames()` for offline/error scenarios

### SurveyJS Integration
- **Serializer**: Uses `Survey.Serializer.addProperty()` to add properties
- **Category**: Custom "customSettings" category with localization
- **Persistence**: Properties automatically saved in survey JSON

### UI Enhancements
- **Custom Icon**: SVG gear icon injected into DOM
- **Styling**: CSS styling for better visual integration
- **Theme**: Consistent with BDO branding

## Usage Instructions

### For Form Designers

1. **Open Form Creator**: Navigate to Questionnaire Designer
2. **Select Question**: Click on any question in the form
3. **Find Custom Settings**: Look for "Custom Settings" tab in Property Grid
4. **Set mapFrom**: Choose a partner reference data column from dropdown
5. **Set exportColumnName**: Enter a custom export column name (optional)
6. **Save**: Properties are automatically saved with the form

### For Developers

#### Adding New Custom Properties
```javascript
Survey.Serializer.addProperty("question", {
  name: "newProperty",
  displayName: "New Property",
  category: "customSettings",
  type: "text", // or "dropdown", "boolean", etc.
  visibleIndex: 3 // Order in category
});
```

#### Accessing Custom Properties
```javascript
// In survey JSON
const question = survey.getQuestionByName("questionName");
const mapFromValue = question.mapFrom;
const exportColumnName = question.exportColumnName;

// In form processing
const surveyData = survey.data;
// Custom properties are available on question objects
```

## Data Flow

1. **Initialization**: Form Creator loads and calls `setupCustomProperties()`
2. **API Call**: Fetches available column names from partner reference data
3. **Property Registration**: Adds custom properties to SurveyJS serializer
4. **UI Setup**: Injects custom icon and styles
5. **User Interaction**: Designer selects values in Property Grid
6. **Persistence**: Values saved in survey JSON definition
7. **Runtime**: Properties available when form is used

## Enhanced Backend Features

### Year-Cycle Context Support
- **Year Filtering**: Only shows columns relevant to the questionnaire year
- **Cycle Disambiguation**: Handles multiple cycles (Planning, Mid Year Review, End Year Review)
- **Prefix System**: Adds cycle prefixes to avoid column name conflicts
  - Planning: "Plan_" prefix
  - Mid Year Review: "Mid_" prefix
  - End Year Review: "End_" prefix

### New Backend APIs
1. **GetAvailableColumnNamesForFormCreator(year, includeCyclePrefixes)**
   - Returns enhanced column choices with cycle context
   - Includes metadata like cycle, data type, normalized names
   - Supports prefix toggling for disambiguation

2. **GetPartnerReferenceDataMetasByYear(year)**
   - Returns all metadata for a specific year
   - Includes all cycles for that year
   - Ordered by cycle for consistent processing

### Enhanced Data Structure
```json
{
  "value": "Plan_employee_id",
  "text": "Plan Employee ID",
  "cycle": 0,
  "cycleName": "Planning",
  "originalColumnName": "Employee ID",
  "normalizedColumnName": "employee_id",
  "columnDataType": "Text",
  "metaId": "guid-here"
}
```

## Available Column Names

The dropdown includes columns from partner reference data such as:
- Employee ID, Employee Name, Sub-Service Line
- Revenue metrics (Est Rev Won - Originator, New Sales, etc.)
- Performance metrics (Margin %, Billable Hours, etc.)
- Industry classifications (Financial Services, Manufacturing, etc.)
- Guidance fields for various metrics

### With Cycle Prefixes (Default)
- Plan Employee ID, Mid Employee ID, End Employee ID
- Plan New Sales, Mid New Sales, End New Sales
- Plan Margin %, Mid Margin %, End Margin %

### Without Cycle Prefixes
- Employee ID, New Sales, Margin % (deduplicated across cycles)

## Error Handling

- **API Failures**: Falls back to static column list
- **Missing Data**: Uses predefined fallback columns
- **Initialization Errors**: Logs errors and continues with fallback
- **Property Conflicts**: SurveyJS handles property name conflicts

## Testing

Use the `FormCreatorCustomPropertiesTest` component to validate:
- API service functionality
- Fallback data availability
- Survey serializer integration
- JSON persistence
- Icon creation

## File Structure

```
src/
├── pages/admin/questionnaireDesigner.jsx     # Main Form Creator component
├── services/partnerReferenceDataUploadService.js  # API service
├── components/test/
│   ├── FormCreatorCustomPropertiesTest.jsx  # Test component
│   └── FormCreatorCustomProperties.md       # This documentation
```

## Browser Compatibility

- Modern browsers supporting ES6+
- SVG support required for custom icons
- SurveyJS Creator compatibility requirements

## Future Enhancements

Potential improvements:
- Real-time column name updates when reference data changes
- Validation of mapFrom selections
- Bulk property assignment for multiple questions
- Export format customization based on exportColumnName
- Integration with form submission processing

## Troubleshooting

### Common Issues

1. **Custom properties not appearing**
   - Check browser console for errors
   - Verify API connectivity
   - Ensure SurveyJS Creator is properly initialized

2. **Dropdown empty**
   - Check partner reference data upload status
   - Verify API endpoint accessibility
   - Fallback data should still be available

3. **Properties not saving**
   - Check survey JSON in browser dev tools
   - Verify SurveyJS Creator auto-save is enabled
   - Check for JavaScript errors during save

4. **Icon not displaying**
   - Check if SVG symbol was injected into DOM
   - Verify CSS styling is applied
   - Check browser SVG support

### Debug Steps

1. Open browser developer tools
2. Check console for error messages
3. Verify network requests to API endpoints
4. Inspect DOM for custom icon and styles
5. Check survey JSON for custom property values
