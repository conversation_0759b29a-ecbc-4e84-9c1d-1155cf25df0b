import React, { useState, useEffect } from 'react';
import { SurveyModel, Serializer } from 'survey-core';
import { Button } from 'primereact/button';
import { Card } from 'primereact/card';
import { Message } from 'primereact/message';
import partnerReferenceDataUploadService from '../../services/partnerReferenceDataUploadService';

/**
 * Test component to validate Form Creator custom properties functionality
 * This component tests the custom properties setup without requiring the full SurveyJS Creator
 */
export const FormCreatorCustomPropertiesTest = () => {
  const [testResults, setTestResults] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  const addTestResult = (testName, passed, message) => {
    setTestResults(prev => [...prev, { testName, passed, message, timestamp: new Date() }]);
  };

  const runTests = async () => {
    setIsLoading(true);
    setTestResults([]);

    try {
      // Test 1: API Service Method (Legacy)
      console.log("Running Test 1: API Service Method (Legacy)");
      try {
        const columnChoices = await partnerReferenceDataUploadService.getAvailableColumnNamesForMapping();
        if (Array.isArray(columnChoices) && columnChoices.length > 0) {
          addTestResult("API Service Method (Legacy)", true, `Successfully retrieved ${columnChoices.length} column choices`);
        } else {
          addTestResult("API Service Method (Legacy)", false, "No column choices returned from API");
        }
      } catch (error) {
        addTestResult("API Service Method (Legacy)", false, `API call failed: ${error.message}`);
      }

      // Test 1b: Enhanced API Service Method with Year
      console.log("Running Test 1b: Enhanced API Service Method with Year");
      try {
        const currentYear = new Date().getFullYear();
        const columnChoices = await partnerReferenceDataUploadService.getAvailableColumnNamesForMapping(currentYear, true);
        if (Array.isArray(columnChoices) && columnChoices.length > 0) {
          // Check if enhanced format is returned
          const firstChoice = columnChoices[0];
          if (firstChoice.value && firstChoice.text && firstChoice.cycle !== undefined) {
            addTestResult("Enhanced API with Year", true, `Retrieved ${columnChoices.length} enhanced column choices with cycle context`);
          } else {
            addTestResult("Enhanced API with Year", true, `Retrieved ${columnChoices.length} column choices (fallback format)`);
          }
        } else {
          addTestResult("Enhanced API with Year", false, "No column choices returned from enhanced API");
        }
      } catch (error) {
        addTestResult("Enhanced API with Year", false, `Enhanced API call failed: ${error.message}`);
      }

      // Test 1c: Metadata by Year API
      console.log("Running Test 1c: Metadata by Year API");
      try {
        const currentYear = new Date().getFullYear();
        const metadata = await partnerReferenceDataUploadService.getPartnerReferenceDataMetasByYear(currentYear);
        if (Array.isArray(metadata)) {
          addTestResult("Metadata by Year API", true, `Retrieved ${metadata.length} metadata records for year ${currentYear}`);
        } else {
          addTestResult("Metadata by Year API", false, "Invalid response from metadata by year API");
        }
      } catch (error) {
        addTestResult("Metadata by Year API", false, `Metadata by year API failed: ${error.message}`);
      }

      // Test 2: Fallback Data
      console.log("Running Test 2: Fallback Data");
      try {
        const fallbackChoices = partnerReferenceDataUploadService.getFallbackColumnNames();
        if (Array.isArray(fallbackChoices) && fallbackChoices.length > 0) {
          addTestResult("Fallback Data", true, `Fallback data contains ${fallbackChoices.length} choices`);
        } else {
          addTestResult("Fallback Data", false, "Fallback data is empty or invalid");
        }
      } catch (error) {
        addTestResult("Fallback Data", false, `Fallback data failed: ${error.message}`);
      }

      // Test 3: Survey Serializer Property Addition
      console.log("Running Test 3: Survey Serializer Property Addition");
      try {
        // Add test properties to verify Serializer works
        Serializer.addProperty("question", {
          name: "testMapFrom",
          displayName: "Test Map From",
          category: "testCustomSettings",
          type: "dropdown",
          choices: [{ value: "test1", text: "Test 1" }, { value: "test2", text: "Test 2" }]
        });

        Serializer.addProperty("question", {
          name: "testExportColumnName",
          displayName: "Test Export Column Name",
          category: "testCustomSettings",
          type: "text"
        });

        // Check if properties were added
        const questionClass = Serializer.findClass("question");
        const mapFromProperty = questionClass.findProperty("testMapFrom");
        const exportProperty = questionClass.findProperty("testExportColumnName");

        if (mapFromProperty && exportProperty) {
          addTestResult("Survey Serializer", true, "Custom properties successfully added to Serializer");
        } else {
          addTestResult("Survey Serializer", false, "Failed to add custom properties to Serializer");
        }
      } catch (error) {
        addTestResult("Survey Serializer", false, `Serializer test failed: ${error.message}`);
      }

      // Test 4: JSON Persistence
      console.log("Running Test 4: JSON Persistence");
      try {
        // Create a test survey with custom properties
        const testSurveyJson = {
          title: "Test Survey",
          pages: [{
            name: "page1",
            elements: [{
              type: "text",
              name: "testQuestion",
              title: "Test Question",
              testMapFrom: "Employee ID",
              testExportColumnName: "emp_id"
            }]
          }]
        };

        // Create survey model and check if custom properties persist
        const survey = new SurveyModel(testSurveyJson);
        const question = survey.getQuestionByName("testQuestion");
        
        if (question && question.testMapFrom === "Employee ID" && question.testExportColumnName === "emp_id") {
          addTestResult("JSON Persistence", true, "Custom properties persist in survey JSON");
        } else {
          addTestResult("JSON Persistence", false, "Custom properties do not persist in survey JSON");
        }
      } catch (error) {
        addTestResult("JSON Persistence", false, `JSON persistence test failed: ${error.message}`);
      }

      // Test 5: Icon Creation
      console.log("Running Test 5: Icon Creation");
      try {
        // Test icon creation function
        const testIconId = 'test-icon-customsettings';
        
        // Create test SVG symbol
        const svgSymbol = document.createElementNS('http://www.w3.org/2000/svg', 'symbol');
        svgSymbol.id = testIconId;
        svgSymbol.setAttribute('viewBox', '0 0 24 24');
        
        const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
        path.setAttribute('d', 'M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z');
        svgSymbol.appendChild(path);
        
        let svgContainer = document.querySelector('svg[style*="display: none"]');
        if (!svgContainer) {
          svgContainer = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
          svgContainer.style.display = 'none';
          document.body.appendChild(svgContainer);
        }
        
        svgContainer.appendChild(svgSymbol);
        
        // Check if icon was created
        const createdIcon = document.getElementById(testIconId);
        if (createdIcon) {
          addTestResult("Icon Creation", true, "SVG icon successfully created and added to DOM");
        } else {
          addTestResult("Icon Creation", false, "Failed to create SVG icon");
        }
      } catch (error) {
        addTestResult("Icon Creation", false, `Icon creation test failed: ${error.message}`);
      }

    } finally {
      setIsLoading(false);
    }
  };

  const clearTests = () => {
    setTestResults([]);
  };

  return (
    <div className="p-4">
      <Card title="Form Creator Custom Properties Test Suite" className="mb-4">
        <div className="flex gap-2 mb-4">
          <Button 
            label="Run Tests" 
            icon="pi pi-play" 
            onClick={runTests} 
            loading={isLoading}
            disabled={isLoading}
          />
          <Button 
            label="Clear Results" 
            icon="pi pi-trash" 
            onClick={clearTests}
            className="p-button-secondary"
            disabled={testResults.length === 0}
          />
        </div>

        <div className="test-results">
          {testResults.length === 0 && !isLoading && (
            <Message severity="info" text="Click 'Run Tests' to validate the custom properties implementation" />
          )}
          
          {isLoading && (
            <Message severity="info" text="Running tests..." />
          )}

          {testResults.map((result, index) => (
            <div key={index} className="mb-2">
              <Message 
                severity={result.passed ? "success" : "error"}
                text={`${result.testName}: ${result.message}`}
              />
            </div>
          ))}
        </div>

        {testResults.length > 0 && (
          <div className="mt-4 p-3 bg-gray-50 border-round">
            <h4>Test Summary</h4>
            <p>
              <strong>Passed:</strong> {testResults.filter(r => r.passed).length} / {testResults.length}
            </p>
            <p>
              <strong>Success Rate:</strong> {Math.round((testResults.filter(r => r.passed).length / testResults.length) * 100)}%
            </p>
          </div>
        )}
      </Card>

      <Card title="Test Documentation" className="mt-4">
        <div className="text-sm">
          <h5>What this test validates:</h5>
          <ul>
            <li><strong>API Service Method:</strong> Tests the getAvailableColumnNamesForMapping() method</li>
            <li><strong>Fallback Data:</strong> Validates the getFallbackColumnNames() method</li>
            <li><strong>Survey Serializer:</strong> Confirms Survey.Serializer.addProperty() works correctly</li>
            <li><strong>JSON Persistence:</strong> Verifies custom properties persist in survey JSON</li>
            <li><strong>Icon Creation:</strong> Tests SVG icon creation and DOM injection</li>
          </ul>
          
          <h5 className="mt-3">Expected Results:</h5>
          <p>All tests should pass for the custom properties implementation to be considered successful.</p>
        </div>
      </Card>
    </div>
  );
};

export default FormCreatorCustomPropertiesTest;
