import React, { useState, useCallback } from "react";
import { Card } from "primereact/card";
import { <PERSON><PERSON> } from "primereact/button";
import { InputText } from "primereact/inputtext";
import { Survey } from "survey-react-ui";
import { Model } from "survey-core";
import { SurveyPDF } from "survey-pdf";
import { messageService } from "../../core/message/messageService";

/**
 * Test component for PDF Export functionality
 * This component demonstrates how to use the Survey.js PDF export feature
 */
export const PDFExportTest = () => {
  const [surveyModel, setSurveyModel] = useState(null);
  const [partnerName, setPartnerName] = useState("Test Partner");
  const [serviceLine, setServiceLine] = useState("Audit");
  const [location, setLocation] = useState("Toronto");

  // Sample survey definition for testing
  const sampleSurveyJson = {
    title: "Test Partner Plan Survey",
    pages: [
      {
        name: "page1",
        title: "Possibility Capabilities",
        elements: [
          {
            type: "text",
            name: "expertiseArea",
            title: "What is your primary area of expertise?",
            isRequired: true
          },
          {
            type: "rating",
            name: "confidenceLevel",
            title: "How confident are you in this area?",
            rateMin: 1,
            rateMax: 5,
            rateStep: 1
          },
          {
            type: "comment",
            name: "additionalComments",
            title: "Additional comments or notes:"
          }
        ]
      }
    ]
  };

  // Initialize test survey
  const initializeTestSurvey = useCallback(() => {
    try {
      const survey = new Model(sampleSurveyJson);
      
      // Apply BDO theme
      survey.applyTheme({
        "themeName": "default-light",
        "colorPalette": "light",
        "isPanelless": false,
        "cssVariables": {
          "--sjs-primary-backcolor": "#ED1A3B",
          "--sjs-primary-forecolor": "#FFFFFF",
          "--sjs-primary-backcolor-light": "#F5E6EA",
          "--sjs-primary-backcolor-dark": "#AF273C",
          "--sjs-secondary-backcolor": "#ED1A3B",
          "--sjs-secondary-forecolor": "#FFFFFF",
          "--sjs-general-backcolor-dim": "#F3F2F1",
        }
      });

      // Set some sample data
      survey.data = {
        expertiseArea: "Financial Auditing",
        confidenceLevel: 4,
        additionalComments: "This is a test comment for PDF export functionality."
      };

      setSurveyModel(survey);
      messageService.successToast("Test survey initialized successfully!");
    } catch (error) {
      console.error("Error initializing test survey:", error);
      messageService.errorToast("Failed to initialize test survey.");
    }
  }, []);

  // Handle PDF export (similar to the main implementation)
  const handleTestPDFExport = useCallback(async () => {
    if (!surveyModel) {
      messageService.errorToast("Please initialize the test survey first.");
      return;
    }

    try {
      messageService.infoToast("Generating test PDF... Please wait.");

      // Create a new survey model for PDF export with current data
      const pdfSurvey = new Model(surveyModel.toJSON());
      
      // Set the survey data (answers) if available
      if (surveyModel.data && Object.keys(surveyModel.data).length > 0) {
        pdfSurvey.data = surveyModel.data;
      }

      // Apply the same BDO theme for consistency
      pdfSurvey.applyTheme({
        "themeName": "default-light",
        "colorPalette": "light",
        "isPanelless": false,
        "cssVariables": {
          "--sjs-primary-backcolor": "#ED1A3B",
          "--sjs-primary-forecolor": "#FFFFFF",
          "--sjs-primary-backcolor-light": "#F5E6EA",
          "--sjs-primary-backcolor-dark": "#AF273C",
          "--sjs-secondary-backcolor": "#ED1A3B",
          "--sjs-secondary-forecolor": "#FFFFFF",
          "--sjs-general-backcolor-dim": "#F3F2F1",
        }
      });

      // Create PDF export options with better formatting
      const options = {
        fontSize: 11,
        margins: {
          left: 15,
          right: 15,
          top: 20,
          bot: 20
        },
        format: 'a4',
        haveCommercialLicense: false,
        headerHeight: 60,
        footerHeight: 30
      };

      // Create SurveyPDF instance
      const surveyPDF = new SurveyPDF(pdfSurvey.toJSON(), options);
      
      // Set the survey data for the PDF
      if (pdfSurvey.data && Object.keys(pdfSurvey.data).length > 0) {
        surveyPDF.data = pdfSurvey.data;
      }

      // Add custom header with test partner information
      surveyPDF.onRenderHeader.add((_, canvas) => {
        const headerText = [
          'BDO Partner Planning Tool - 2025 (TEST)',
          `Partner: ${partnerName}`,
          `Service Line: ${serviceLine}`,
          `Location: ${location}`,
          `Generated: ${new Date().toLocaleDateString()}`
        ];
        
        canvas.fontSize = 10;
        canvas.fillColor = '#333333';
        let yPosition = 15;
        
        headerText.forEach((text, index) => {
          if (index === 0) {
            canvas.fontSize = 12;
            canvas.fillColor = '#ED1A3B';
          } else {
            canvas.fontSize = 9;
            canvas.fillColor = '#666666';
          }
          canvas.drawText(text, 15, yPosition);
          yPosition += 10;
        });
      });

      // Generate and download the PDF
      const fileName = `TEST_Partner_Plan_${partnerName.replace(/[^a-zA-Z0-9]/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`;
      
      await surveyPDF.save(fileName);
      
      messageService.successToast("Test PDF exported successfully!");
      
    } catch (error) {
      console.error("Error exporting test PDF:", error);
      messageService.errorToast("Failed to export test PDF. Please try again.");
    }
  }, [surveyModel, partnerName, serviceLine, location]);

  return (
    <div className="p-4">
      <Card title="PDF Export Test Component" className="mb-4">
        <div className="p-fluid">
          <div className="field-grid">
            <div className="field col-12 md:col-4">
              <label htmlFor="partnerName">Partner Name</label>
              <InputText
                id="partnerName"
                value={partnerName}
                onChange={(e) => setPartnerName(e.target.value)}
                placeholder="Enter partner name"
              />
            </div>
            <div className="field col-12 md:col-4">
              <label htmlFor="serviceLine">Service Line</label>
              <InputText
                id="serviceLine"
                value={serviceLine}
                onChange={(e) => setServiceLine(e.target.value)}
                placeholder="Enter service line"
              />
            </div>
            <div className="field col-12 md:col-4">
              <label htmlFor="location">Location</label>
              <InputText
                id="location"
                value={location}
                onChange={(e) => setLocation(e.target.value)}
                placeholder="Enter location"
              />
            </div>
          </div>
          
          <div className="flex gap-2 mt-3">
            <Button
              label="Initialize Test Survey"
              icon="pi pi-play"
              onClick={initializeTestSurvey}
              className="p-button-success"
            />
            <Button
              label="Export to PDF"
              icon="pi pi-file-pdf"
              onClick={handleTestPDFExport}
              disabled={!surveyModel}
              className="p-button-info"
            />
          </div>
        </div>
      </Card>

      {surveyModel && (
        <Card title="Test Survey Preview" className="mt-4">
          <Survey model={surveyModel} />
        </Card>
      )}
    </div>
  );
};

export default PDFExportTest;
