import React, { useState } from "react";
import { Card } from "primereact/card";
import { Button } from "primereact/button";
import { PartnerAutocomplete } from "../common/PartnerAutocomplete";

/**
 * Test component for PartnerAutocomplete functionality
 * This component demonstrates how to use the PartnerAutocomplete component
 */
export const PartnerAutocompleteTest = () => {
  const [primaryReviewer, setPrimaryReviewer] = useState(null);
  const [secondaryReviewer, setSecondaryReviewer] = useState(null);

  const handlePrimaryReviewerChange = (e) => {
    setPrimaryReviewer(e.target.value);
    console.log("Primary Reviewer Selected:", e.target.value);
  };

  const handleSecondaryReviewerChange = (e) => {
    setSecondaryReviewer(e.target.value);
    console.log("Secondary Reviewer Selected:", e.target.value);
  };

  const handleClear = () => {
    setPrimaryReviewer(null);
    setSecondaryReviewer(null);
  };

  const handleShowValues = () => {
    console.log("Current Values:", {
      primaryReviewer,
      secondaryReviewer
    });
    alert(`Primary: ${primaryReviewer?.displayName || 'None'}\nSecondary: ${secondaryReviewer?.displayName || 'None'}`);
  };

  return (
    <div className="partner-autocomplete-test" style={{ padding: '20px' }}>
      <Card title="Partner Autocomplete Test">
        <div className="p-fluid">
          <div className="p-field p-mb-3">
            <label htmlFor="primaryReviewer">Primary Reviewer:</label>
            <PartnerAutocomplete
              id="primaryReviewer"
              value={primaryReviewer}
              onChange={handlePrimaryReviewerChange}
              placeholder="Search for primary reviewer..."
              className="p-mt-2"
            />
            {primaryReviewer && (
              <div className="p-mt-2">
                <small>
                  Selected: <strong>{primaryReviewer.displayName}</strong> 
                  {primaryReviewer.employeeId && ` (ID: ${primaryReviewer.employeeId})`}
                  {primaryReviewer.department && ` - ${primaryReviewer.department}`}
                </small>
              </div>
            )}
          </div>

          <div className="p-field p-mb-3">
            <label htmlFor="secondaryReviewer">Secondary Reviewer:</label>
            <PartnerAutocomplete
              id="secondaryReviewer"
              value={secondaryReviewer}
              onChange={handleSecondaryReviewerChange}
              placeholder="Search for secondary reviewer..."
              className="p-mt-2"
            />
            {secondaryReviewer && (
              <div className="p-mt-2">
                <small>
                  Selected: <strong>{secondaryReviewer.displayName}</strong>
                  {secondaryReviewer.employeeId && ` (ID: ${secondaryReviewer.employeeId})`}
                  {secondaryReviewer.department && ` - ${secondaryReviewer.department}`}
                </small>
              </div>
            )}
          </div>

          <div className="p-field" style={{ display: 'flex', gap: '10px', marginTop: '20px' }}>
            <Button 
              label="Show Values" 
              onClick={handleShowValues}
              className="p-button-info"
            />
            <Button 
              label="Clear All" 
              onClick={handleClear}
              className="p-button-secondary"
            />
          </div>
        </div>
      </Card>
    </div>
  );
};
