/**
 * Form Status enumeration definitions.
 * Reference to Enumerations.FormStatus in server side.
 * Reference to records in table dbo.[FormStatus].
 * Work for form status management and display.
 */
export const FormStatus = {
  /**
   * Draft status - Form is in draft state
   */
  Draft: 0,
  
  /**
   * Submitted status - Form has been submitted by partner
   */
  Submitted: 1,
  
  /**
   * Approved status - Form has been approved by manager
   */
  Approved: 2,
  
  /**
   * Rejected status - Form has been rejected
   */
  Rejected: 3,
  
  /**
   * Reopened status - Form has been reopened for editing
   */
  Reopened: 4,
  
  /**
   * Closed status - Form has been closed/finalized
   */
  Closed: 5
};

/**
 * Get form status display name
 * @param {number} statusId - The form status ID
 * @returns {string} Display name of the form status
 */
export const getFormStatusName = (statusId) => {
  switch (statusId) {
    case FormStatus.Draft:
      return 'Draft';
    case FormStatus.Submitted:
      return 'Submitted';
    case FormStatus.Approved:
      return 'Approved';
    case FormStatus.Rejected:
      return 'Rejected';
    case FormStatus.Reopened:
      return 'Reopened';
    case FormStatus.Closed:
      return 'Closed';
    default:
      return 'Unknown';
  }
};

/**
 * Get form status CSS class for styling
 * @param {number} statusId - The form status ID
 * @returns {string} CSS class name for the status
 */
export const getFormStatusClass = (statusId) => {
  switch (statusId) {
    case FormStatus.Draft:
      return 'status-draft';
    case FormStatus.Submitted:
      return 'status-submitted';
    case FormStatus.Approved:
      return 'status-approved';
    case FormStatus.Rejected:
      return 'status-rejected';
    case FormStatus.Reopened:
      return 'status-reopened';
    case FormStatus.Closed:
      return 'status-closed';
    default:
      return 'status-unknown';
  }
};

/**
 * Check if form status allows editing
 * @param {number} statusId - The form status ID
 * @returns {boolean} True if form can be edited
 */
export const isFormEditable = (statusId) => {
  return statusId === FormStatus.Draft || statusId === FormStatus.Reopened;
};

/**
 * Check if form is in final state
 * @param {number} statusId - The form status ID
 * @returns {boolean} True if form is in final state
 */
export const isFormFinal = (statusId) => {
  return statusId === FormStatus.Approved || statusId === FormStatus.Closed;
};
