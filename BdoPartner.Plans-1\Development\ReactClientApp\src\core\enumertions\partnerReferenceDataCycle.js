/** Corporate with server side project, Enumerations.PartnerReferenceDataCycle definitions. */
export const PartnerReferenceDataCycle = {
  /** Planning cycle. 0 */
  Planning: 0,
  /** Mid Year Review cycle. 1 */
  MidYearReview: 1,
  /** End Year Review cycle. 2 */
  EndYearReview: 2,
};

/** Helper function to get cycle display name */
export const getCycleDisplayName = (cycle) => {
  switch (cycle) {
    case PartnerReferenceDataCycle.Planning:
      return 'Planning';
    case PartnerReferenceDataCycle.MidYearReview:
      return 'Mid Year Review';
    case PartnerReferenceDataCycle.EndYearReview:
      return 'End Year Review';
    default:
      return 'Unknown';
  }
};

/** Get cycle options for dropdowns */
export const getCycleOptions = () => [
  { label: 'Planning', value: PartnerReferenceDataCycle.Planning },
  { label: 'Mid Year Review', value: PartnerReferenceDataCycle.MidYearReview },
  { label: 'End Year Review', value: PartnerReferenceDataCycle.EndYearReview },
];
