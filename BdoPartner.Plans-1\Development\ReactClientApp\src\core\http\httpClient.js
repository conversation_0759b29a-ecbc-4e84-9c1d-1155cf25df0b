import axios from "axios";
import { loadingService } from "../loading/loadingService";
import APP_CONFIG from "../config/appConfig";
import { messageService } from "../message/messageService";
/**
 * It is instance of axios Http client, which works for http call.
 * Corporate with loading.jsx for a global progress bar process.
 * Corporate with loadingReducer.js
 *
 * Note: It is single tone.
 *
 * Reference: https://sumn2u.medium.com/global-progress-bar-on-api-s-call-in-react-5133f818d12a
 *
 */
const http = axios.create();

/**
 * Corporate with http const to inject access_token to http request header.
 * Work for Identity Server 4 single sign on.
 * @returns Bearer token header string.
 */
const getAccessTokenHeader = () => {
  const oidc = JSON.parse(
    /**
     * Note: It is OIDC-Client library default storage.
     * The key format is defined by oidc-client library.
     * Note: Developer no needs to modify the session key.
     * */
    sessionStorage.getItem(
      `oidc.user:${APP_CONFIG.iamDomain}:${APP_CONFIG.clientId}`
    )
  );
  if (!!oidc && !!oidc.access_token && !oidc.expired) {
    return `Bearer ${oidc.access_token}`;
  } else return "";
};

/** Get current language codes. */
export const getLanguageCode = () => {
  //
  // corporate with Redux/store.js
  //
  var languageCode = localStorage.getItem(
    `${APP_CONFIG.clientId}_CurrentLanguage`
  );

  if (languageCode && languageCode.length > 0) return languageCode;
  else return "en";
};

http.interceptors.request.use(
  (request) => {
    // Notify loading service to show progress spinner.
    loadingService.httpRequestSent();
    const tokenHeader = getAccessTokenHeader();
    if (tokenHeader && tokenHeader.length > 0) {
      request.headers["authorization"] = tokenHeader;
    }
    const languageCode = getLanguageCode();
    //
    // Corporate with web api project middleware called "RequestCultureMiddleware.cs"
    //
    request.headers["currentLanguage"] = languageCode;
    return request;
  },
  (error) => {
    loadingService.error();
    // Notify loading service to hide progress spinner.
    return Promise.reject(error);
  }
);

http.interceptors.response.use(
  (response) => {
    // Notify loding service to hide progress spinner.
    loadingService.httpResponseReceived();
    return response;
  },
  (error) => {
    // Notify loading service to hide progress spinner.
    loadingService.error();

    if (error.response) {
      switch (error.response.status) {
        case 400:
          messageService.errorToast("It is Bad Request");
          break;
        case 401:
          messageService.errorToast("Unauthorized Access.");
          break;
        //case 404:
        //  messageService.errorToast("Required access endpoint is No Found");
        //  break;
        case 406:
          messageService.errorToast("Request Not Acceptable");
          break;
        case 403:
          messageService.errorToast("Request is Forbiddden");
          break;
        case 408:
          messageService.errorToast("Request is Timeout");
          break;
        default:
          messageService.errorToast("Request process failed.");
          break;
      }
    }
    return Promise.reject(error);
  }
);

export default http;
