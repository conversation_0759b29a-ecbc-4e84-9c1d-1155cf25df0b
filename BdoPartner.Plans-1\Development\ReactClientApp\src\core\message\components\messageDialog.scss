.appDialog-error {
  .p-dialog-header {
    background-color: #ffcdd2;
  }
  .appDialogHeader {
    font-size: 1.2em;
    font-weight: bold;
    color: #73000c;
  }

  #appDialogContent {
    padding-top: 1em;
    .pi {
      font-size: 2rem;
      color: #73000c;
    }
  }
}

.appDialog-warn {
  .p-dialog-header {
    background-color: #ffecb3;
  }
  .appDialogHeader {
    font-size: 1.2em;
    font-weight: bold;
    color: #6d5100;
  }

  #appDialogContent {
    padding-top: 1em;
    .pi {
      font-size: 2rem;
      color: #6d5100;
    }
  }
}

.appDialog-info {
  .p-dialog-header {
    background-color: #b3e5fc;
  }
  .appDialogHeader {
    font-size: 1.2em;
    font-weight: bold;
    color: #044868;
  }

  #appDialogContent {
    padding-top: 1em;
    .pi {
      font-size: 2rem;
      color: #044868;
    }
  }
}

.appDialog-success {
  .p-dialog-header {
    background-color: #c8e6c9;
  }
  .appDialogHeader {
    font-size: 1.2em;
    font-weight: bold;
    color: #224a23;
  }

  #appDialogContent {
    padding-top: 1em;
    .pi {
      font-size: 2rem;
      color: #224a23;
    }
  }
}

.appDialog-confirmation {
  .p-dialog-header {
    background-color: #b3e5fc;
  }
  .appDialogHeader {
    font-size: 1.2em;
    font-weight: bold;
    color: #044868;
  }

  #appDialogContent {
    padding-top: 1em;
    .pi {
      font-size: 2rem;
      color: #044868;
    }
  }
}
