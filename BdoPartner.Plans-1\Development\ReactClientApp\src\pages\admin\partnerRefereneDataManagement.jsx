import React, { useState, useEffect } from "react";
import { TabView, TabPanel } from "primereact/tabview";
import { Card } from "primereact/card";
import { UploadPartnerReviewerAssignment } from "../../components/admin/UploadPartnerReviewerAssignment";
import { PartnerReviewerManagement } from "../../components/admin/PartnerReviewerManagement";
import { UploadPartnerReferenceData } from "../../components/admin/UploadPartnerReferenceData";
import { PartnerReferenceDataMaintain } from "../../components/admin/PartnerReferenceDataMaintain";

export const PartnerReferenceDataManagement = () => {
  // Local storage key for persisting the selected tab
  const STORAGE_KEY = 'partnerReferenceDataManagement_activeTab';

  // Initialize activeIndex from localStorage or default to 0
  const [activeIndex, setActiveIndex] = useState(() => {
    const savedIndex = localStorage.getItem(STORAGE_KEY);
    return savedIndex !== null ? parseInt(savedIndex, 10) : 0;
  });

  // Save activeIndex to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem(STORAGE_KEY, activeIndex.toString());
  }, [activeIndex]);

  return (
    <div>
      <div className="banner">
        <div className="banner__site-title-area">
          <div class="page-title">Partner Reference Data Management</div>
        </div>
      </div>

      <Card>
        <TabView
          activeIndex={activeIndex}
          onTabChange={(e) => setActiveIndex(e.index)}
        >
          <TabPanel header="Upload Partner Reviewer Assignment" leftIcon="pi pi-upload">
            <UploadPartnerReviewerAssignment />
          </TabPanel>
          <TabPanel header="Manage Partner Reviewer" leftIcon="pi pi-users">
            <PartnerReviewerManagement />
          </TabPanel>
          <TabPanel header="Upload Partner Reference Data" leftIcon="pi pi-cloud-upload">
            <UploadPartnerReferenceData />
          </TabPanel>
          <TabPanel header="Manage Partner Reference Data" leftIcon="pi pi-database">
            <PartnerReferenceDataMaintain />
          </TabPanel>
        </TabView>
      </Card>
    </div>
  );
};