import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { SurveyCreatorComponent, SurveyCreator } from 'survey-creator-react';
import { Action, SurveyModel, Serializer, SvgRegistry } from 'survey-core'; // Added SvgRegistry import
import { InputText } from 'primereact/inputtext';
import { Dropdown } from 'primereact/dropdown';
import { Button } from 'primereact/button';
import { Toast } from 'primereact/toast';
import { Dialog } from 'primereact/dialog';
import { ConfirmDialog, confirmDialog } from 'primereact/confirmdialog';
import questionnaireService from '../../services/questionnaireService';
import partnerReferenceDataUploadService from '../../services/partnerReferenceDataUploadService';
import { loadingService } from '../../core/loading/loadingService';
import { messageService } from '../../core/message/messageService';

// Import Survey.js CSS
import 'survey-creator-core/survey-creator-core.css';

/**
 * Setup custom properties for SurveyJS Creator
 * Adds mapFrom dropdown and exportColumnName text properties
 * @param {number} questionnaireYear - The year of the questionnaire being designed
 */
const setupCustomProperties = async (questionnaireYear = null) => {
  try {
    // Get available column names for mapping based on questionnaire year
    const columnChoices = await partnerReferenceDataUploadService.getAvailableColumnNamesForMapping(questionnaireYear, true);

    // Register custom category (no icon here; handled via event)
    Serializer.addProperty("question", {
      name: "mapFrom",
      displayName: "Map From",
      category: "customSettings",
      categoryDisplayName: "Custom Settings",
      type: "dropdown",
      choices: columnChoices,
      visibleIndex: 1,
      description: "Select a partner reference data column to map this question to"
    });

    // Add exportColumnName property as text input
    Serializer.addProperty("question", {
      name: "exportColumnName",
      displayName: "Export Column Name",
      category: "customSettings",
      categoryDisplayName: "Custom Settings",
      type: "text",
      visibleIndex: 2,
      description: "Specify a custom column name for data export"
    });

    console.log("Custom properties setup completed with", columnChoices.length, "column choices");

  } catch (error) {
    console.error("Error setting up custom properties:", error);

    // Setup with fallback data if API fails
    const fallbackChoices = partnerReferenceDataUploadService.getFallbackColumnNames();

    Serializer.addProperty("question", {
      name: "mapFrom",
      displayName: "Map From",
      category: "customSettings",
      categoryDisplayName: "Custom Settings",
      type: "dropdown",
      choices: fallbackChoices,
      visibleIndex: 1,
      description: "Select a partner reference data column to map this question to"
    });

    Serializer.addProperty("question", {
      name: "exportColumnName",
      displayName: "Export Column Name",
      category: "customSettings",
      categoryDisplayName: "Custom Settings",
      type: "text",
      visibleIndex: 2,
      description: "Specify a custom column name for data export"
    });

    console.log("Custom properties setup completed with fallback data");
  }
};

/**
 * Register custom SVG icon for Custom Settings category
 */
const registerCustomSettingsIcon = () => {
  // Register the icon with SurveyJS SvgRegistry
  SvgRegistry.registerIconFromSvg(
    'icon-customsettings',
    '<svg viewBox="0 0 24 24"><path d="M21,9L17,5V8H10V10H17V13M7,11L3,15L7,19V16H14V14H7V11Z"/></svg>'
  );
};

export const QuestionnaireDesigner = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [questionnaire, setQuestionnaire] = useState(null);
  const [surveyName, setSurveyName] = useState('');
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const [loading, setLoading] = useState(true);
  const [showPublishDialog, setShowPublishDialog] = useState(false);
  const [isQuestionnaireLoaded, setIsQuestionnaireLoaded] = useState(false);
  const toast = useRef(null);
  const creatorRef = useRef(null);
  const autoSaveTimeoutRef = useRef(null);

  // Year options for dropdown
  const currentYear = new Date().getFullYear();
  const yearOptions = [];
  for (let i = currentYear - 5; i <= currentYear + 5; i++) {
    yearOptions.push({ label: i.toString(), value: i });
  }

  useEffect(() => {
    // Initialize Survey Creator with BDO theme first
    const creator = new SurveyCreator({
      showLogicTab: true,
      showTranslationTab: false,
      showEmbeddedSurveyTab: false,
      showJSONEditorTab: true,
      showTestSurveyTab: true,
      showPropertyGrid: true,
      showToolbox: true,
      isAutoSave: true
    });

    // Apply BDO theme colors
    creator.theme = {
      cssVariables: {
        '--sjs-primary-backcolor': '#ED1A3B',
        '--sjs-primary-forecolor': '#ffffff',
        '--sjs-secondary-backcolor': '#f3f2f1',
        '--sjs-secondary-forecolor': '#1f1f1f',
        '--sjs-shadow-small': '0px 1px 2px 0px rgba(0, 0, 0, 0.15)',
        '--sjs-shadow-medium': '0px 2px 6px 0px rgba(0, 0, 0, 0.1)',
        '--sjs-shadow-large': '0px 8px 16px 0px rgba(0, 0, 0, 0.1)',
        '--sjs-border-default': '#e0e0e0',
        '--sjs-border-light': '#f0f0f0'
      }
    };

    // Disable custom toolbar buttons (they are now external)
    const saveAction = new Action({
      id: 'save-survey',
      visible: false, // Disabled - now external
      title: 'Save',
      tooltip: 'Save questionnaire as draft',
      action: () => {
        console.log("Internal save button clicked, creator instance:", creator);
        console.log("Creator JSON at save time:", creator?.JSON);
        console.log("Creator ref at save time:", creatorRef.current);
        console.log("Creator ref JSON at save time:", creatorRef.current?.JSON);

        // Use the creator instance directly instead of the ref
        saveSurveyWithCreator(creator);
      }
    });

    creator.toolbarItems.push(saveAction);

    creator.toolbarItems.push(new Action({
      id: 'publish-survey',
      visible: false, // Disabled - now external
      title: 'Publish',
      tooltip: 'Publish questionnaire',
      action: () => setShowPublishDialog(true)
    }));

    creator.toolbarItems.push(new Action({
      id: 'back-to-list',
      visible: false, // Disabled - now external
      title: 'Back to List',
      tooltip: 'Return to questionnaire list',
      action: () => handleBackToList()
    }));

    // Enable auto-save
    creator.onModified.add(() => {
      clearAutoSaveTimeout();
      autoSaveTimeoutRef.current = setTimeout(() => {
        saveSurvey(false); // Auto-save without showing success message
      }, 10000); // Auto-save after 10 seconds of inactivity
    });

    // Register the custom icon
    registerCustomSettingsIcon();

    // Assign icon to the custom category in the property grid
    creator.onSurveyInstanceCreated.add((sender, options) => {
      if (options.area === "property-grid") {
        const customCategory = options.survey.getPageByName("customSettings");
        if (customCategory) {
          customCategory.iconName = "icon-customsettings";
          console.log("Assigned custom icon to Custom Settings category");
        }
      }
    });

    // Setup custom properties for Form Creator with questionnaire year
    setupCustomProperties(selectedYear);

    creatorRef.current = creator;
    console.log("Creator initialized and assigned to ref:", creator);

    return () => {
      clearAutoSaveTimeout();
    };
  }, []);

  // Load questionnaire data after creator is initialized
  useEffect(() => {
    if (id && creatorRef.current) {
      loadQuestionnaire();
    }
  }, [id]);

  // Load questionnaire data into creator when both are available
  useEffect(() => {
    if (creatorRef.current && questionnaire) {
      const surveyJson = questionnaire.draftDefinitionJson ?
        JSON.parse(questionnaire.draftDefinitionJson) :
        { title: questionnaire.name, pages: [] };

      creatorRef.current.JSON = surveyJson;
      console.log("Loaded questionnaire data into creator via useEffect:", surveyJson);
    }
  }, [questionnaire]);

  // Update save button enabled state based on questionnaire loading
  useEffect(() => {
    if (creatorRef.current) {
      const saveAction = creatorRef.current.toolbarItems.find(item => item.id === 'save-survey');
      if (saveAction) {
        saveAction.enabled = isQuestionnaireLoaded && !!questionnaire && !!questionnaire.id;
        console.log("Save button enabled state:", saveAction.enabled, {
          isQuestionnaireLoaded,
          hasQuestionnaire: !!questionnaire,
          hasId: !!questionnaire?.id
        });
      }
    }
  }, [isQuestionnaireLoaded, questionnaire]);

  const clearAutoSaveTimeout = () => {
    if (autoSaveTimeoutRef.current) {
      clearTimeout(autoSaveTimeoutRef.current);
      autoSaveTimeoutRef.current = null;
    }
  };

  const loadQuestionnaire = async () => {
    try {
      setLoading(true);
      setIsQuestionnaireLoaded(false);
      loadingService.httpRequestSent();

      const data = await questionnaireService.getQuestionnaireById(id);

      if (data) {
        setQuestionnaire(data);
        setSurveyName(data.name);
        setSelectedYear(data.year);

        // Load the survey definition into the creator
        if (creatorRef.current) {
          const surveyJson = data.draftDefinitionJson ?
            JSON.parse(data.draftDefinitionJson) :
            { title: data.name, pages: [] };

          creatorRef.current.JSON = surveyJson;
          console.log("Loaded survey JSON into creator:", surveyJson);

          // Set loaded state after a small delay to ensure everything is properly initialized
          setTimeout(() => {
            setIsQuestionnaireLoaded(true);
            console.log("Questionnaire marked as loaded");
          }, 100);
        } else {
          console.warn("Creator not ready when loading questionnaire data");
          setIsQuestionnaireLoaded(true); // Still mark as loaded even if creator isn't ready
        }
      } else {
        messageService.errorToast("Failed to load questionnaire");
        navigate('/admin/questionnaire-management');
      }
    } catch (error) {
      console.error("Error loading questionnaire:", error);
      messageService.errorToast("Error loading questionnaire");
      navigate('/admin/questionnaire-management');
    } finally {
      setLoading(false);
      loadingService.httpResponseReceived();
    }
  };

  // Save survey using creator reference (for external save button)
  const saveSurvey = async (showMessage = true) => {
    if (!creatorRef.current) {
      console.error("Creator reference is null - cannot save survey");
      if (showMessage) {
        messageService.errorToast("Form designer not ready. Please try again.");
      }
      return;
    }

    return await saveSurveyWithCreator(creatorRef.current, showMessage);
  };

  // Save survey using creator instance (for internal save button)
  const saveSurveyWithCreator = async (creator, showMessage = true) => {
    console.log("Save attempt - Creator:", !!creator, "Questionnaire:", !!questionnaire, "Loaded:", isQuestionnaireLoaded);
    console.log("Save attempt - Survey name:", surveyName, "Year:", selectedYear);

    if (!creator) {
      console.error("Creator instance is null - cannot save survey");
      if (showMessage) {
        messageService.errorToast("Form designer not ready. Please try again.");
      }
      return;
    }

    // Check if questionnaire data is loaded
    if (!questionnaire || !isQuestionnaireLoaded || !questionnaire.id) {
      console.error("Questionnaire data not loaded or invalid - cannot save survey", {
        questionnaire: !!questionnaire,
        isQuestionnaireLoaded,
        questionnaireId: questionnaire?.id
      });
      if (showMessage) {
        messageService.errorToast("Questionnaire data not loaded or invalid. Please wait and try again.");
      }
      return;
    }

    // Check if survey name and year are available
    if (!surveyName || !selectedYear) {
      console.error("Survey name or year not available - cannot save survey", {
        surveyName,
        selectedYear
      });
      if (showMessage) {
        messageService.errorToast("Survey name or year not available. Please check the form fields.");
      }
      return;
    }

    try {
      if (showMessage) {
        loadingService.httpRequestSent();
      }

      // Get the current survey JSON from the creator
      const surveyJson = creator.JSON;
      console.log("Current survey JSON:", surveyJson);

      // Ensure we have valid survey data
      if (!surveyJson || Object.keys(surveyJson).length === 0) {
        console.error("Survey JSON is empty or invalid");
        if (showMessage) {
          messageService.errorToast("No form data to save. Please add some questions first.");
        }
        return;
      }

      const definitionJson = JSON.stringify(surveyJson);
      console.log("Definition JSON to save:", definitionJson);
      console.log("Questionnaire state:", questionnaire);
      console.log("Survey name state:", surveyName);
      console.log("Selected year state:", selectedYear);

      const updatedQuestionnaire = await questionnaireService.saveDraft(
        questionnaire,
        surveyName,
        selectedYear,
        definitionJson
      );

      if (showMessage) {
        messageService.successToast("Questionnaire saved successfully");
      }
      setQuestionnaire(updatedQuestionnaire);
    } catch (error) {
      console.error("Error saving questionnaire:", error);
      if (showMessage) {
        messageService.errorToast(error.message || "Error saving questionnaire");
      }
    } finally {
      if (showMessage) {
        loadingService.httpResponseReceived();
      }
    }
  };

  const publishSurvey = async () => {
    if (!creatorRef.current) return;

    try {
      loadingService.httpRequestSent();

      const isRepublish = questionnaire?.status === 1;

      if (!isRepublish) {
        // For first-time publishing, check if there's already a different published questionnaire in the same year
        const hasPublishedInYear = await questionnaireService.hasPublishedQuestionnaireInYear(selectedYear, questionnaire?.id);

        if (hasPublishedInYear) {
          messageService.errorToast(`${selectedYear} year already has published Partner Plan, you cannot publish new Partner Plan in same year.`);
          return;
        }
      }

      const definitionJson = JSON.stringify(creatorRef.current.JSON);

      const publishedQuestionnaire = await questionnaireService.publishWithDefinition(
        questionnaire,
        surveyName,
        selectedYear,
        definitionJson
      );

      const successMessage = isRepublish
        ? "Questionnaire republished successfully"
        : "Questionnaire published successfully";

      messageService.successToast(successMessage);
      setQuestionnaire(publishedQuestionnaire);
      setShowPublishDialog(false);
    } catch (error) {
      console.error("Error publishing questionnaire:", error);
      messageService.errorToast(error.message || "Error publishing questionnaire");
    } finally {
      loadingService.httpResponseReceived();
    }
  };

  const handleBackToList = () => {
    if (creatorRef.current && creatorRef.current.isModified) {
      confirmDialog({
        message: 'You have unsaved changes. Do you want to save before leaving?',
        header: 'Unsaved Changes',
        icon: 'pi pi-exclamation-triangle',
        accept: async () => {
          await saveSurvey();
          navigate('/admin/questionnaire-management');
        },
        reject: () => {
          navigate('/admin/questionnaire-management');
        }
      });
    } else {
      navigate('/admin/questionnaire-management');
    }
  };

  const handleNameChange = (e) => {
    setSurveyName(e.target.value);
    // Update the survey title in the creator
    if (creatorRef.current) {
      const currentJson = creatorRef.current.JSON;
      currentJson.title = e.target.value;
      creatorRef.current.JSON = currentJson;
    }
  };

  if (loading) {
    return (
      <div className="flex justify-content-center align-items-center" style={{ height: '400px' }}>
        <i className="pi pi-spinner pi-spin" style={{ fontSize: '2rem' }}></i>
        <span className="ml-2">Loading questionnaire...</span>
      </div>
    );
  }

  return (
    <div>
      <Toast ref={toast} />
      <ConfirmDialog />
      
      {/* Header with questionnaire details */}
      <div className="profile-card mb-3">
        <div className="flex align-items-center gap-3">
          <div className="flex flex-column">
            <label className="font-semibold mb-1">Name *</label>
            <InputText
              value={surveyName}
              onChange={handleNameChange}
              placeholder="Enter questionnaire name"
              style={{ width: '300px' }}
            />
          </div>
          <div className="flex flex-column">
            <label className="font-semibold mb-1">Year *</label>
            <Dropdown
              value={selectedYear}
              options={yearOptions}
              onChange={(e) => setSelectedYear(e.value)}
              placeholder="Select year"
              style={{ width: '150px' }}
            />
          </div>
          <div className="flex align-items-end">
            <Button
              label="Save"
              icon="pi pi-save"
              onClick={() => {
                console.log("External save button clicked, creator ref:", creatorRef.current);
                console.log("Creator JSON at external save time:", creatorRef.current?.JSON);
                saveSurvey();
              }}
              className="mr-2"
              disabled={!isQuestionnaireLoaded || !questionnaire || !questionnaire.id}
            />
            <Button
              label={questionnaire?.status === 1 ? "Republish" : "Publish"}
              icon="pi pi-send"
              onClick={() => setShowPublishDialog(true)}
              className="mr-2"
              disabled={!isQuestionnaireLoaded || !questionnaire || !questionnaire.id}
            />
            <Button
              label="Back to List"
              icon="pi pi-arrow-left"
              onClick={handleBackToList}
              className="p-button-secondary"
            />
          </div>
        </div>
      </div>

      {/* Survey Creator */}
      <div style={{ height: 'calc(100vh - 200px)' }}>
        <SurveyCreatorComponent creator={creatorRef.current} />
      </div>

      {/* Publish Confirmation Dialog */}
      <Dialog
        header={questionnaire?.status === 1 ? "Republish Questionnaire" : "Publish Questionnaire"}
        visible={showPublishDialog}
        style={{ width: '450px' }}
        onHide={() => setShowPublishDialog(false)}
        footer={
          <div>
            <Button
              label="Cancel"
              icon="pi pi-times"
              onClick={() => setShowPublishDialog(false)}
              className="p-button-text"
            />
            <Button
              label={questionnaire?.status === 1 ? "Republish" : "Publish"}
              icon="pi pi-send"
              onClick={publishSurvey}
              className="action"
            />
          </div>
        }
      >
        <p>
          {questionnaire?.status === 1
            ? "Are you sure you want to republish this questionnaire? This will update the published version with your current draft changes."
            : "Are you sure you want to publish this questionnaire? This will make it available for use and cannot be easily undone."
          }
        </p>
      </Dialog>
    </div>
  );
};