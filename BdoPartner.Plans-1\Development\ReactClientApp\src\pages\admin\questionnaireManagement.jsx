import React, { useState, useEffect, useRef, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { Card } from "primereact/card";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import { <PERSON><PERSON> } from "primereact/button";
import { Dialog } from "primereact/dialog";
import { InputText } from "primereact/inputtext";
import { Dropdown } from "primereact/dropdown";
import { Toast } from "primereact/toast";
import { ConfirmDialog, confirmDialog } from "primereact/confirmdialog";
import { Tag } from "primereact/tag";
import questionnaireService from "../../services/questionnaireService";
import { messageService } from "../../core/message/messageService";

export const QuestionnaireManagement = () => {
  const navigate = useNavigate();
  const [questionnaires, setQuestionnaires] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [newQuestionnaire, setNewQuestionnaire] = useState({
    name: "",
    year: new Date().getFullYear()
  });
  const [globalFilter, setGlobalFilter] = useState("");
  const [totalRecords, setTotalRecords] = useState(0);
  const [first, setFirst] = useState(0);
  const [rows, setRows] = useState(10);
  const toast = useRef(null);
  const loadingRef = useRef(false);

  // Year options for dropdown
  const currentYear = new Date().getFullYear();
  const yearOptions = [];
  for (let i = currentYear - 5; i <= currentYear + 5; i++) {
    yearOptions.push({ label: i.toString(), value: i });
  }

  const loadQuestionnaires = useCallback(async (pageIndex = 0, pageSize = 10, searchTerm = "") => {
    // Prevent duplicate calls if already loading
    if (loading || loadingRef.current) {
      return;
    }

    try {
      setLoading(true);
      loadingRef.current = true;

      const data = await questionnaireService.searchQuestionnaires(
        searchTerm,
        null, // year filter
        null, // status filter
        null, // isActive filter
        pageIndex,
        pageSize
      );

      setQuestionnaires(data.items);
      setTotalRecords(data.totalCount);
    } catch (error) {
      console.error("Error loading questionnaires:", error);
      messageService.errorToast("Error loading questionnaires");
      setQuestionnaires([]);
      setTotalRecords(0);
    } finally {
      setLoading(false);
      loadingRef.current = false;
    }
  }, [loading]);

  useEffect(() => {
    loadQuestionnaires(0, rows, globalFilter);
  }, []); // Only run on mount to prevent duplicate calls

  const onPageChange = (event) => {
    const newFirst = event.first;
    const newRows = event.rows;
    const newPageIndex = Math.floor(newFirst / newRows); // 0-based page index

    setFirst(newFirst);
    setRows(newRows);
    loadQuestionnaires(newPageIndex, newRows, globalFilter);
  };

  const onGlobalFilterChange = (value) => {
    setGlobalFilter(value);
    setFirst(0); // Reset to first page when filtering
    loadQuestionnaires(0, rows, value); // Start from page index 0
  };

  const handleCreateQuestionnaire = async () => {
    if (!newQuestionnaire.name.trim()) {
      messageService.warnToast("Please enter a questionnaire name");
      return;
    }

    try {
      const questionnaireData = {
        name: newQuestionnaire.name.trim(),
        year: newQuestionnaire.year,
        status: 0, // Draft status
        isActive: true,
        draftDefinitionJson: JSON.stringify({}),
        acknowledgement: false,
        acknowledgementText: "",
        generalComments: false,
        generalCommentsText: "",
        formSystemVersion: 1
      };

      const createdQuestionnaire = await questionnaireService.createQuestionnaire(questionnaireData);

      messageService.successToast("Questionnaire created successfully");
      setShowCreateDialog(false);
      setNewQuestionnaire({ name: "", year: currentYear });
      loadQuestionnaires(Math.floor(first / rows), rows, globalFilter);

      // Navigate to edit the new questionnaire
      navigate(`/admin/questionnaire-designer/${createdQuestionnaire.id}`);
    } catch (error) {
      console.error("Error creating questionnaire:", error);
      messageService.errorToast(error.message || "Error creating questionnaire");
    }
  };

  const handleEditQuestionnaire = (questionnaire) => {
    navigate(`/admin/questionnaire-designer/${questionnaire.id}`);
  };

  const handleViewQuestionnaire = (questionnaire) => {
    navigate(`/admin/questionnaire-viewer/${questionnaire.id}`);
  };

  const handlePublishQuestionnaire = async (questionnaire) => {
    try {
      const isRepublish = questionnaire.status === 1;

      if (!isRepublish) {
        // For first-time publishing, check if there's already a different published questionnaire in the same year
        const hasPublishedInYear = await questionnaireService.hasPublishedQuestionnaireInYear(questionnaire.year, questionnaire.id);

        if (hasPublishedInYear) {
          messageService.errorToast(`${questionnaire.year} year already has published Partner Plan, you cannot publish new Partner Plan in same year.`);
          return;
        }
      }

      // Show appropriate confirmation message
      const confirmMessage = isRepublish
        ? `Are you sure you want to republish "${questionnaire.name}"? This will update the published version with the current draft changes.`
        : `Are you sure you want to publish "${questionnaire.name}"? This will make it available for use.`;

      const confirmHeader = isRepublish ? "Confirm Republish" : "Confirm Publish";

      confirmDialog({
        message: confirmMessage,
        header: confirmHeader,
        icon: "pi pi-exclamation-triangle",
        accept: async () => {
          try {
            await questionnaireService.publishQuestionnaire(questionnaire.id);

            const successMessage = isRepublish
              ? "Questionnaire republished successfully"
              : "Questionnaire published successfully";

            messageService.successToast(successMessage);
            loadQuestionnaires(Math.floor(first / rows), rows, globalFilter);
          } catch (error) {
            console.error("Error publishing questionnaire:", error);
            messageService.errorToast(error.message || "Error publishing questionnaire");
          }
        }
      });
    } catch (error) {
      console.error("Error checking for published questionnaire:", error);
      messageService.errorToast("Error checking for existing published questionnaire");
    }
  };

  const handleDeleteQuestionnaire = async (questionnaire) => {
    try {
      // First validate if the questionnaire can be deleted
      const validation = await questionnaireService.validateQuestionnaireForDeletion(questionnaire.id);

      if (!validation.canDelete) {
        let warningMessage = "";

        if (validation.isPublished) {
          warningMessage = "Cannot delete a published questionnaire. Published questionnaires cannot be deleted to maintain data integrity. Please archive it first if you want to disable it.";
        } else if (validation.hasFormReferences) {
          warningMessage = `Cannot delete this questionnaire because it is referenced by ${validation.formReferenceCount} form(s). Please remove or reassign these forms first before deleting the questionnaire.`;
        }

        messageService.warnToast(warningMessage);
        return;
      }

      // If validation passes, show confirmation dialog
      confirmDialog({
        message: `Are you sure you want to delete "${questionnaire.name}"? This action cannot be undone.`,
        header: "Confirm Delete",
        icon: "pi pi-exclamation-triangle",
        acceptClassName: "p-button-danger",
        accept: async () => {
          try {
            await questionnaireService.deleteQuestionnaire(questionnaire.id);
            messageService.successToast("Questionnaire deleted successfully");
            loadQuestionnaires(Math.floor(first / rows), rows, globalFilter);
          } catch (error) {
            console.error("Error deleting questionnaire:", error);
            messageService.errorToast(error.message || "Error deleting questionnaire");
          }
        }
      });
    } catch (error) {
      console.error("Error validating questionnaire for deletion:", error);
      messageService.errorToast("Error validating questionnaire for deletion");
    }
  };

  const statusBodyTemplate = (rowData) => {
    const getStatusInfo = (status) => {
      switch (status) {
        case 0:
          return { label: "Draft", severity: "warning" };
        case 1:
          return { label: "Published", severity: "success" };
        case 2:
          return { label: "Archived", severity: "secondary" };
        default:
          return { label: "Unknown", severity: "secondary" };
      }
    };

    const statusInfo = getStatusInfo(rowData.status);
    return <Tag value={statusInfo.label} severity={statusInfo.severity} />;
  };



  const dateBodyTemplate = (rowData) => {
    if (!rowData.modifiedOn) return "-";
    return new Date(rowData.modifiedOn).toLocaleDateString();
  };

  const actionsBodyTemplate = (rowData) => {
    return (
      <div className="flex gap-2">
        <Button
          icon="pi pi-eye"
          className="p-button-rounded p-button-text"
          onClick={() => handleViewQuestionnaire(rowData)}
          tooltip="View"
          tooltipOptions={{ position: "top" }}
        />
        <Button
          icon="pi pi-pencil"
          className="p-button-rounded p-button-text"
          onClick={() => handleEditQuestionnaire(rowData)}
          tooltip="Edit"
          tooltipOptions={{ position: "top" }}
        />
        {(rowData.status === 0 || rowData.status === 1) && (
          <Button
            icon="pi pi-send"
            className="p-button-rounded p-button-text"
            onClick={() => handlePublishQuestionnaire(rowData)}
            tooltip={rowData.status === 1 ? "Republish" : "Publish"}
            tooltipOptions={{ position: "top" }}
          />
        )}
        {rowData.status === 0 && (
          <Button
            icon="pi pi-trash"
            className="p-button-rounded p-button-text p-button-danger"
            onClick={() => handleDeleteQuestionnaire(rowData)}
            tooltip="Delete"
            tooltipOptions={{ position: "top" }}
          />
        )}
      </div>
    );
  };

  const header = (
    <div className="flex justify-content-between align-items-center">
      <div className="flex align-items-center gap-2">
        <span className="p-input-icon-left">
          <i className="pi pi-search" />
          <InputText
            type="search"
            value={globalFilter}
            onChange={(e) => onGlobalFilterChange(e.target.value)}
            placeholder="Search questionnaires..."
          />
        </span>
      </div>
      <Button
        label="Create New Questionnaire"
        icon="pi pi-plus"
        className="action"
        onClick={() => setShowCreateDialog(true)}
      />
    </div>
  );

  return (
    <div>
      <div className="banner">
        <div className="banner__site-title-area">
          <div className="page-title">Questionnaire Management</div>
        </div>
      </div>

      <Card>
        <Toast ref={toast} />
        <ConfirmDialog />
        
        <DataTable
          value={questionnaires}
          loading={loading}
          header={header}
          emptyMessage="No questionnaires found"
          sortMode="multiple"
          paginator
          lazy
          rows={rows}
          first={first}
          totalRecords={totalRecords}
          onPage={onPageChange}
          rowsPerPageOptions={[5, 10, 25, 50]}
          className="p-datatable-gridlines"
        >
          <Column field="name" header="Name" sortable />
          <Column field="year" header="Year" sortable />
          <Column field="status" header="Status" body={statusBodyTemplate} sortable />
          <Column field="modifiedOn" header="Modified On" body={dateBodyTemplate} sortable />
          <Column field="modifiedByName" header="Modified By" sortable />
          <Column header="Actions" body={actionsBodyTemplate} style={{ width: "180px" }} />
        </DataTable>

        {/* Create New Questionnaire Dialog */}
        <Dialog
          header="Create New Questionnaire"
          visible={showCreateDialog}
          style={{ width: "450px" }}
          onHide={() => {
            setShowCreateDialog(false);
            setNewQuestionnaire({ name: "", year: currentYear });
          }}
          footer={
            <div>
              <Button
                label="Cancel"
                icon="pi pi-times"
                onClick={() => {
                  setShowCreateDialog(false);
                  setNewQuestionnaire({ name: "", year: currentYear });
                }}
                className="p-button-text"
              />
              <Button
                label="Create"
                icon="pi pi-check"
                onClick={handleCreateQuestionnaire}
                className="action"
              />
            </div>
          }
        >
          <div className="field">
            <label htmlFor="questionnaire-name" className="block">
              Name <span className="text-red-500">*</span>
            </label>
            <InputText
              id="questionnaire-name"
              value={newQuestionnaire.name}
              onChange={(e) => setNewQuestionnaire({ ...newQuestionnaire, name: e.target.value })}
              placeholder="Enter questionnaire name"
              className="w-full"
              autoFocus
            />
          </div>
          <div className="field">
            <label htmlFor="questionnaire-year" className="block">
              Year <span className="text-red-500">*</span>
            </label>
            <Dropdown
              id="questionnaire-year"
              value={newQuestionnaire.year}
              options={yearOptions}
              onChange={(e) => setNewQuestionnaire({ ...newQuestionnaire, year: e.value })}
              placeholder="Select year"
              className="w-full"
            />
          </div>
        </Dialog>
      </Card>
    </div>
  );
};
