import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Survey } from 'survey-react-ui';
import { Model } from 'survey-core';
import { But<PERSON> } from 'primereact/button';
import { Toast } from 'primereact/toast';
import { Card } from 'primereact/card';
import questionnaireService from '../../services/questionnaireService';
import { loadingService } from '../../core/loading/loadingService';
import { messageService } from '../../core/message/messageService';

// Import Survey.js CSS
import 'survey-core/survey-core.min.css';

export const QuestionnaireViewer = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [questionnaire, setQuestionnaire] = useState(null);
  const [survey, setSurvey] = useState(null);
  const [loading, setLoading] = useState(true);
  const toast = useRef(null);

  useEffect(() => {
    if (id) {
      loadQuestionnaire();
    }
  }, [id]);

  const loadQuestionnaire = async () => {
    try {
      setLoading(true);
      loadingService.httpRequestSent();

      const data = await questionnaireService.getQuestionnaireById(id);

      if (data) {
        setQuestionnaire(data);

        // Create survey model from the definition
        const surveyJson = data.definitionJson ?
          JSON.parse(data.definitionJson) :
          (data.draftDefinitionJson ? JSON.parse(data.draftDefinitionJson) : { title: data.name, pages: [] });

        // Apply BDO theme
        const surveyModel = new Model(surveyJson);

        // Configure survey settings
        surveyModel.showProgressBar = "top";
        surveyModel.showQuestionNumbers = "on";
        surveyModel.questionsOnPageMode = "singlePage";
        surveyModel.showPreviewBeforeComplete = "showAllQuestions";
        surveyModel.mode = "display"; // Read-only mode for viewing

        // Apply BDO styling
        surveyModel.applyTheme({
          cssVariables: {
            '--sjs-primary-backcolor': '#ED1A3B',
            '--sjs-primary-forecolor': '#ffffff',
            '--sjs-secondary-backcolor': '#f3f2f1',
            '--sjs-secondary-forecolor': '#1f1f1f'
          }
        });

        setSurvey(surveyModel);
      } else {
        messageService.errorToast("Failed to load questionnaire");
        navigate('/admin/questionnaire-management');
      }
    } catch (error) {
      console.error("Error loading questionnaire:", error);
      messageService.errorToast("Error loading questionnaire");
      navigate('/admin/questionnaire-management');
    } finally {
      setLoading(false);
      loadingService.httpResponseReceived();
    }
  };

  const handleBackToList = () => {
    navigate('/admin/questionnaire-management');
  };

  const handleEditQuestionnaire = () => {
    navigate(`/admin/questionnaire-designer/${id}`);
  };

  const getStatusInfo = (status) => {
    switch (status) {
      case 0:
        return { label: "Draft", className: "p-tag p-tag-warning" };
      case 1:
        return { label: "Published", className: "p-tag p-tag-success" };
      case 2:
        return { label: "Archived", className: "p-tag p-tag-secondary" };
      default:
        return { label: "Unknown", className: "p-tag p-tag-secondary" };
    }
  };

  if (loading) {
    return (
      <div className="flex justify-content-center align-items-center" style={{ height: '400px' }}>
        <i className="pi pi-spinner pi-spin" style={{ fontSize: '2rem' }}></i>
        <span className="ml-2">Loading questionnaire...</span>
      </div>
    );
  }

  if (!questionnaire) {
    return (
      <div className="flex justify-content-center align-items-center" style={{ height: '400px' }}>
        <span>Questionnaire not found</span>
      </div>
    );
  }

  const statusInfo = getStatusInfo(questionnaire.status);

  return (
    <div>
      <Toast ref={toast} />
      
      {/* Header */}
      <div className="banner">
        <div className="banner__site-title-area">
          <div className="page-title">View Questionnaire</div>
        </div>
      </div>

      <Card>
        {/* Questionnaire Info */}
        <div className="mb-4 p-3" style={{ backgroundColor: '#f8f9fa', borderRadius: '6px' }}>
          <div className="flex justify-content-between align-items-start">
            <div>
              <h3 className="mt-0 mb-2">{questionnaire.name}</h3>
              <div className="flex align-items-center gap-3 mb-2">
                <span><strong>Year:</strong> {questionnaire.year}</span>
                <span className={statusInfo.className}>{statusInfo.label}</span>
                <span><strong>Active:</strong> {questionnaire.isActive ? 'Yes' : 'No'}</span>
              </div>
              {questionnaire.modifiedOn && (
                <div className="text-sm text-600">
                  <strong>Last Modified:</strong> {new Date(questionnaire.modifiedOn).toLocaleString()}
                  {questionnaire.modifiedByName && ` by ${questionnaire.modifiedByName}`}
                </div>
              )}
            </div>
            <div className="flex gap-2">
              <Button
                label="Edit"
                icon="pi pi-pencil"
                onClick={handleEditQuestionnaire}
                className="action"
              />
              <Button
                label="Back to List"
                icon="pi pi-arrow-left"
                onClick={handleBackToList}
                className="p-button-secondary"
              />
            </div>
          </div>
        </div>

        {/* Survey Preview */}
        <div className="survey-container">
          {survey ? (
            <Survey model={survey} />
          ) : (
            <div className="text-center p-4">
              <i className="pi pi-info-circle" style={{ fontSize: '2rem', color: '#6c757d' }}></i>
              <p className="mt-2 text-600">No survey content available</p>
            </div>
          )}
        </div>
      </Card>
    </div>
  );
};
