import { useContext } from 'react';
import MyCurrentPlan from '../components/dashboard/MyCurrentPlan';
import MyPastPlans from '../components/dashboard/MyPastPlan';
import PartnerPlans from '../components/dashboard/PartnerPlans';
import { AuthContext } from '../core/auth/components/authProvider';
import { Role } from '../core/enumertions/role';

const Dashboard = () => {
  const authService = useContext(AuthContext);
  const user = authService.getUser();
  // Since this component is now protected by PrivateRoute, user is guaranteed to be authenticated

  // Check if user has roles that can access Partner Plans section
  const canAccessPartnerPlans = user?.roles && (
    user.roles.includes(Role.PPAdministrator) ||
    user.roles.includes(Role.PPExecutiveLeadership)
  );

  return (
    <div className="dashboard-container">
      {/* Welcome Section */}
      <div className="welcome-section">
        <h1 className="welcome-title">Welcome {user?.displayName || 'User'}</h1>
        <p className="welcome-description">
          This landing page is your centralized hub for managing and monitoring partner-submitted plans. Whether you're reviewing submissions, tracking progress, or analyzing trends, this page provides the tools and insights you need to drive effective planning.
        </p>
      </div>

      {/* Dashboard Content */}
      <div className="dashboard-content">
        {/* My Current Plan Section - Available to all authenticated users */}
        <div className="dashboard-section">
          <MyCurrentPlan />
        </div>

        {/* My Past Plans Section - Available to all authenticated users */}
        <div className="dashboard-section">
          <MyPastPlans />
        </div>

        {/* Partner Plans Section - Only for PPAdministrator and PPExecutiveLeadership roles */}
        {canAccessPartnerPlans && (
          <div className="dashboard-section">
            <PartnerPlans />
          </div>
        )}
      </div>
    </div>
  );
};

export default Dashboard;