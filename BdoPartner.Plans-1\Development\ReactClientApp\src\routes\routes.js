//import * as React from "react";
import { Route, Routes } from "react-router-dom";
import { Callback } from "../core/auth/components/callback";
import { Logout } from "../core/auth/components/logout";
import { LogoutCallback } from "../core/auth/components/logoutCallback";
import { PrivateRoute } from "./privateRoute";
import { Sample} from "../pages/sample";
import { SilentRenew } from "../core/auth/components/silentRenew";
import { UserProfile } from "../pages/userprofile";
import { Role } from "../core/enumertions/role";
import { UserManagement } from "../pages/usermanagement";
import { NotFound } from "../pages/notfound";
import { MyPartnerPlan } from "../pages/myPartnerPlan";
import { PartnerReferenceDataManagement } from "../pages/admin/partnerRefereneDataManagement";
import { QuestionnaireManagement } from "../pages/admin/questionnaireManagement";
import { QuestionnaireDesigner } from "../pages/admin/questionnaireDesigner";
import { QuestionnaireViewer } from "../pages/admin/questionnaireViewer";
import Dashboard from "../pages/dashboard";

/** Global routes definition.
 *  Link specified url to specified component.
 *  Note: Here no need to add APP_CONFIG.basePath since BrowserRouter "baseName" already be assigned by APP_CONFIG.basePath.
 */
export const AppRoutes = () => (
  <Routes>
    <Route path="/auth-callback" element={<Callback />} />
    <Route path="/silent-auth-callback" element={<SilentRenew />} />
    <Route path="/logout" element={<Logout />} />
    <Route path="/logout/callback" element={<LogoutCallback />} />
    <Route path="/userprofile" element={<PrivateRoute component={UserProfile} />} />
    <Route
      path="/usermanagement"
      element={<PrivateRoute component={UserManagement} roles={[Role.PPAdministrator]} />}
    />
    <Route
      path="/admin/partner-reference-data-management"
      element={<PrivateRoute component={PartnerReferenceDataManagement} roles={[Role.PPAdministrator]} />}
    />
    <Route
      path="/admin/questionnaire-management"
      element={<PrivateRoute component={QuestionnaireManagement} roles={[Role.PPAdministrator]} />}
    />
    <Route
      path="/admin/questionnaire-designer/:id"
      element={<PrivateRoute component={QuestionnaireDesigner} roles={[Role.PPAdministrator]} />}
    />
    <Route
      path="/admin/questionnaire-viewer/:id"
      element={<PrivateRoute component={QuestionnaireViewer} roles={[Role.PPAdministrator]} />}
    />

    {/* Note: All routes now require authentication - redirect to login if not authenticated */}
    <Route path="/dashboard" element={<PrivateRoute component={Dashboard} />} />
    <Route path="/home" element={<PrivateRoute component={Dashboard} />} />
    <Route path="/" element={<PrivateRoute component={Dashboard} />} />
    <Route path="/my-partner-plan" element={<PrivateRoute component={MyPartnerPlan} />} />
    {/* <Route path="/sample" element={<Sample />} /> */}
    <Route path="*" element={<NotFound />} />
  </Routes>
);
