import http from "../core/http/httpClient";
import APP_CONFIG from "../core/config/appConfig";
import { ResultStatus } from "../core/enumertions/resultStatus";

/**
 * Form Service for handling form-related API calls
 * Provides methods to manage partner plan forms and user answers
 */
class FormService {
  /**
   * Get my plan - consolidated method that gets questionnaire, creates/gets form, and loads user answers
   * @param {number} year - The year (optional, defaults to current year)
   * @returns {Promise<Object|null>} Complete plan data or null if failed
   */
  async getMyPlan(year = null) {
    try {
      const params = new URLSearchParams();
      if (year) params.append('year', year);

      const response = await http.get(
        `${APP_CONFIG.apiDomain}/api/form/getmyplan?${params.toString()}`
      );

      if (response.data && response.data.resultStatus === ResultStatus.Success) {
        return response.data.item;
      } else {
        console.error("Failed to get my plan:", response.data?.message);
        throw new Error(response.data?.message || "Failed to load partner plan");
      }
    } catch (error) {
      console.error("Error getting my plan:", error);
      throw error;
    }
  }

  /**
   * Get or create form for current user and questionnaire
   * @param {string} questionnaireId - The questionnaire ID
   * @param {number} year - The year (optional, defaults to current year)
   * @returns {Promise<Object|null>} Form object or null if failed
   * @deprecated Use getMyPlan() instead for better performance
   */
  async getOrCreateFormForCurrentUser(questionnaireId, year = null) {
    try {
      const params = new URLSearchParams();
      params.append('questionnaireId', questionnaireId);
      if (year) params.append('year', year);

      const response = await http.get(
        `${APP_CONFIG.apiDomain}/api/form/getorcreateformforcurrentuser?${params.toString()}`
      );

      if (response.data && response.data.resultStatus === ResultStatus.Success) {
        return response.data.item;
      } else {
        console.error("Failed to get or create form:", response.data?.message);
        return null;
      }
    } catch (error) {
      console.error("Error getting or creating form:", error);
      return null;
    }
  }

  /**
   * Get form with user answers for current user
   * @param {string} questionnaireId - The questionnaire ID
   * @param {number} year - The year (optional, defaults to current year)
   * @returns {Promise<Object|null>} Form with user answers or null if failed
   */
  async getFormWithUserAnswersForCurrentUser(questionnaireId, year = null) {
    try {
      const params = new URLSearchParams();
      params.append('questionnaireId', questionnaireId);
      if (year) params.append('year', year);

      const response = await http.get(
        `${APP_CONFIG.apiDomain}/api/form/getformwithuseranswersforcurrentuser?${params.toString()}`
      );
      
      if (response.data && response.data.resultStatus === ResultStatus.Success) {
        return response.data.item;
      } else {
        console.error("Failed to get form with user answers:", response.data?.message);
        return null;
      }
    } catch (error) {
      console.error("Error getting form with user answers:", error);
      return null;
    }
  }

  /**
   * Get current year active questionnaire
   * @param {number} year - The year (optional, defaults to current year)
   * @returns {Promise<Object|null>} Active questionnaire or null if failed
   */
  async getCurrentYearActiveQuestionnaire(year = null) {
    try {
      const params = new URLSearchParams();
      if (year) params.append('year', year);

      const response = await http.get(
        `${APP_CONFIG.apiDomain}/api/form/getcurrentyearactivequestionnaire?${params.toString()}`
      );
      
      if (response.data && response.data.resultStatus === ResultStatus.Success) {
        return response.data.item;
      } else {
        console.error("Failed to get current year questionnaire:", response.data?.message);
        return null;
      }
    } catch (error) {
      console.error("Error getting current year questionnaire:", error);
      return null;
    }
  }

  /**
   * Submit form
   * @param {string} formId - The form ID
   * @returns {Promise<Object|null>} Submitted form or null if failed
   */
  async submitForm(formId) {
    try {
      const response = await http.post(
        `${APP_CONFIG.apiDomain}/api/form/submitform?id=${formId}`
      );
      
      if (response.data && response.data.resultStatus === ResultStatus.Success) {
        return response.data.item;
      } else {
        console.error("Failed to submit form:", response.data?.message);
        throw new Error(response.data?.message || "Failed to submit form");
      }
    } catch (error) {
      console.error("Error submitting form:", error);
      throw error;
    }
  }

  /**
   * Save user answer (manual save)
   * @param {string} formId - The form ID
   * @param {string} answerData - Survey answer data as JSON string
   * @returns {Promise<Object|null>} Saved user answer or null if failed
   */
  async saveUserAnswer(formId, answerData) {
    try {
      const response = await http.post(
        `${APP_CONFIG.apiDomain}/api/useranswer/saveuseranswer?formId=${formId}`,
        JSON.stringify(answerData),
        {
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );
      
      if (response.data && response.data.resultStatus === ResultStatus.Success) {
        return response.data.item;
      } else {
        console.error("Failed to save user answer:", response.data?.message);
        throw new Error(response.data?.message || "Failed to save user answer");
      }
    } catch (error) {
      console.error("Error saving user answer:", error);
      throw error;
    }
  }

  /**
   * Auto-save user answer (background save)
   * @param {string} formId - The form ID
   * @param {string} answerData - Survey answer data as JSON string
   * @returns {Promise<boolean>} True if successful, false otherwise
   */
  async autoSaveUserAnswer(formId, answerData) {
    try {
      const response = await http.post(
        `${APP_CONFIG.apiDomain}/api/useranswer/autosaveuseranswer?formId=${formId}`,
        JSON.stringify(answerData),
        {
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );
      
      if (response.data && response.data.success) {
        return true;
      } else {
        console.warn("Auto-save failed:", response.data?.message);
        return false;
      }
    } catch (error) {
      console.warn("Auto-save error:", error);
      return false;
    }
  }
}

// Export a singleton instance
export const formService = new FormService();
export default formService;
