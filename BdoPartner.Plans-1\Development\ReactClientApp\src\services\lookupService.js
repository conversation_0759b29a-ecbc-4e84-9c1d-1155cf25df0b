import http from "../core/http/httpClient";
import APP_CONFIG from "../core/config/appConfig";
import { ResultStatus } from "../core/enumertions/resultStatus";

/**
 * Lookup Service for handling lookup data API calls
 * Provides methods to fetch lookup data from the backend API with multi-language support
 */
class LookupService {
  /**
   * Get all languages
   * @param {boolean} includeEmptyRow - Include empty row for dropdown selection
   * @returns {Promise<Array>} Array of language lookup items
   */
  async getLanguages(includeEmptyRow = false) {
    try {
      const response = await http.get(
        `${APP_CONFIG.apiDomain}/api/lookup/getlanguages`
      );
      
      if (response.data && response.data.resultStatus === ResultStatus.Success) {
        return response.data.item || [];
      } else {
        console.error("Failed to fetch languages:", response.data?.message);
        return [];
      }
    } catch (error) {
      console.error("Error fetching languages:", error);
      return [];
    }
  }

  /**
   * Get all roles
   * @param {boolean} includeEmptyRow - Include empty row for dropdown selection
   * @returns {Promise<Array>} Array of role lookup items
   */
  async getRoles(includeEmptyRow = false) {
    try {
      const response = await http.get(
        `${APP_CONFIG.apiDomain}/api/lookup/getroles`
      );
      
      if (response.data && response.data.resultStatus === ResultStatus.Success) {
        return response.data.item || [];
      } else {
        console.error("Failed to fetch roles:", response.data?.message);
        return [];
      }
    } catch (error) {
      console.error("Error fetching roles:", error);
      return [];
    }
  }

  /**
   * Get all permissions
   * @param {boolean} includeEmptyRow - Include empty row for dropdown selection
   * @returns {Promise<Array>} Array of permission lookup items
   */
  async getPermissions(includeEmptyRow = false) {
    try {
      const response = await http.get(
        `${APP_CONFIG.apiDomain}/api/lookup/getpermissions`
      );
      
      if (response.data && response.data.resultStatus === ResultStatus.Success) {
        return response.data.item || [];
      } else {
        console.error("Failed to fetch permissions:", response.data?.message);
        return [];
      }
    } catch (error) {
      console.error("Error fetching permissions:", error);
      return [];
    }
  }

  /**
   * Get all form statuses with localized display names
   * @param {boolean} includeEmptyRow - Include empty row for dropdown selection
   * @returns {Promise<Array>} Array of form status lookup items
   */
  async getFormStatuses(includeEmptyRow = false) {
    try {
      const params = new URLSearchParams();
      if (includeEmptyRow) params.append('includeEmptyRow', includeEmptyRow);

      const response = await http.get(
        `${APP_CONFIG.apiDomain}/api/lookup/getformstatuses?${params.toString()}`
      );
      
      if (response.data && response.data.resultStatus === ResultStatus.Success) {
        return response.data.item || [];
      } else {
        console.error("Failed to fetch form statuses:", response.data?.message);
        return [];
      }
    } catch (error) {
      console.error("Error fetching form statuses:", error);
      return [];
    }
  }

  /**
   * Get all questionnaire statuses with localized display names
   * @param {boolean} includeEmptyRow - Include empty row for dropdown selection
   * @returns {Promise<Array>} Array of questionnaire status lookup items
   */
  async getQuestionnaireStatuses(includeEmptyRow = false) {
    try {
      const params = new URLSearchParams();
      if (includeEmptyRow) params.append('includeEmptyRow', includeEmptyRow);

      const response = await http.get(
        `${APP_CONFIG.apiDomain}/api/lookup/getquestionnairestatuses?${params.toString()}`
      );
      
      if (response.data && response.data.resultStatus === ResultStatus.Success) {
        return response.data.item || [];
      } else {
        console.error("Failed to fetch questionnaire statuses:", response.data?.message);
        return [];
      }
    } catch (error) {
      console.error("Error fetching questionnaire statuses:", error);
      return [];
    }
  }

  /**
   * Get all notifications
   * @returns {Promise<Array>} Array of notification items
   */
  async getNotifications() {
    try {
      const response = await http.get(
        `${APP_CONFIG.apiDomain}/api/lookup/getnotifications`
      );
      
      if (response.data && response.data.resultStatus === ResultStatus.Success) {
        return response.data.item || [];
      } else {
        console.error("Failed to fetch notifications:", response.data?.message);
        return [];
      }
    } catch (error) {
      console.error("Error fetching notifications:", error);
      return [];
    }
  }

  /**
   * Get form status display name by ID
   * @param {number} statusId - The form status ID
   * @returns {Promise<string>} Display name of the form status
   */
  async getFormStatusDisplayName(statusId) {
    try {
      const formStatuses = await this.getFormStatuses(false);
      const status = formStatuses.find(s => s.key === statusId);
      return status ? status.value : 'Unknown';
    } catch (error) {
      console.error("Error getting form status display name:", error);
      return 'Unknown';
    }
  }

  /**
   * Get questionnaire status display name by ID
   * @param {number} statusId - The questionnaire status ID
   * @returns {Promise<string>} Display name of the questionnaire status
   */
  async getQuestionnaireStatusDisplayName(statusId) {
    try {
      const questionnaireStatuses = await this.getQuestionnaireStatuses(false);
      const status = questionnaireStatuses.find(s => s.key === statusId);
      return status ? status.value : 'Unknown';
    } catch (error) {
      console.error("Error getting questionnaire status display name:", error);
      return 'Unknown';
    }
  }

  /**
   * Get all lookup data in a single call (for initialization)
   * @returns {Promise<Object>} Object containing all lookup data
   */
  async getAllLookupData() {
    try {
      const [
        languages,
        formStatuses,
        questionnaireStatuses,
        roles,
        permissions
      ] = await Promise.all([
        this.getLanguages(),
        this.getFormStatuses(),
        this.getQuestionnaireStatuses(),
        this.getRoles(),
        this.getPermissions()
      ]);

      return {
        languages,
        formStatuses,
        questionnaireStatuses,
        roles,
        permissions
      };
    } catch (error) {
      console.error("Error fetching all lookup data:", error);
      return {
        languages: [],
        formStatuses: [],
        questionnaireStatuses: [],
        roles: [],
        permissions: []
      };
    }
  }
}

// Export a singleton instance
export const lookupService = new LookupService();
export default lookupService;
