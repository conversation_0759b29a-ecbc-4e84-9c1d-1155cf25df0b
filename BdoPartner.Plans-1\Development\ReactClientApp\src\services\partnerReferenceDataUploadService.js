import http from "../core/http/httpClient";
import APP_CONFIG from "../core/config/appConfig";
import { ResultStatus } from "../core/enumertions/resultStatus";

/**
 * Partner Reference Data Upload Service for handling partner reference data upload API calls
 * Provides methods to manage partner reference data uploads, validation, and processing
 */
class PartnerReferenceDataUploadService {
  /**
   * Search partner reference data uploads with filtering and pagination
   * @param {number} year - Filter by year
   * @param {number} status - Filter by status
   * @param {number} pageIndex - Page index (0-based, default: 0)
   * @param {number} pageSize - Page size (default: 20)
   * @returns {Promise<Object>} Paginated list of uploads with metadata
   */
  async searchPartnerReferenceDataUploads(year = null, status = null, pageIndex = 0, pageSize = 20) {
    try {
      const params = new URLSearchParams();
      if (year) params.append('year', year);
      if (status !== null && status !== undefined) params.append('status', status);
      params.append('pageIndex', pageIndex);
      params.append('pageSize', pageSize);

      const response = await http.get(
        `${APP_CONFIG.apiDomain}/api/partnerreferencedataupload/searchpartnerreferencedatauploads?${params.toString()}`
      );
      
      if (response.data && response.data.resultStatus === ResultStatus.Success) {
        return response.data.item || { items: [], totalCount: 0 };
      } else {
        console.error("Failed to search uploads:", response.data?.message);
        return { items: [], totalCount: 0 };
      }
    } catch (error) {
      console.error("Error searching uploads:", error);
      return { items: [], totalCount: 0 };
    }
  }

  /**
   * Get all partner reference data metadata
   * @returns {Promise<Array>} List of all metadata objects
   */
  async getPartnerReferenceDataMetas() {
    try {
      const response = await http.get(
        `${APP_CONFIG.apiDomain}/api/partnerreferencedataupload/getpartnerreferencedatametas`
      );

      if (response.data && response.data.resultStatus === ResultStatus.Success) {
        return response.data.item || [];
      } else {
        console.error("Failed to get metadata list:", response.data?.message);
        return [];
      }
    } catch (error) {
      console.error("Error getting metadata list:", error);
      return [];
    }
  }

  /**
   * Get partner reference data metadata by ID
   * @param {string} metaId - Metadata ID (GUID)
   * @returns {Promise<Object>} Metadata object with details
   */
  async getPartnerReferenceDataMetaById(metaId) {
    try {
      const response = await http.get(
        `${APP_CONFIG.apiDomain}/api/partnerreferencedataupload/getpartnerreferencedatametabyid?id=${metaId}`
      );

      if (response.data && response.data.resultStatus === ResultStatus.Success) {
        return response.data.item || null;
      } else {
        console.error("Failed to get metadata:", response.data?.message);
        return null;
      }
    } catch (error) {
      console.error("Error getting metadata:", error);
      return null;
    }
  }

  /**
   * Get partner reference data upload details with optional filtering
   * @param {string} uploadId - Upload ID (GUID)
   * @param {boolean} includeValidOnly - Include only valid records
   * @param {boolean} includeInvalidOnly - Include only invalid records
   * @returns {Promise<Array>} List of upload details
   */
  async getPartnerReferenceDataUploadDetails(uploadId, includeValidOnly = false, includeInvalidOnly = false) {
    try {
      const params = new URLSearchParams();
      params.append('uploadId', uploadId);
      if (includeValidOnly) params.append('includeValidOnly', 'true');
      if (includeInvalidOnly) params.append('includeInvalidOnly', 'true');

      const response = await http.get(
        `${APP_CONFIG.apiDomain}/api/partnerreferencedataupload/getpartnerreferencedatauploaddetails?${params.toString()}`
      );
      
      if (response.data && response.data.resultStatus === ResultStatus.Success) {
        return response.data.item || [];
      } else {
        console.error("Failed to get upload details:", response.data?.message);
        return [];
      }
    } catch (error) {
      console.error("Error getting upload details:", error);
      return [];
    }
  }

  /**
   * Upload Excel or CSV file for partner reference data
   * @param {File} file - Excel or CSV file
   * @param {number} year - Year for the upload
   * @param {number} cycle - Cycle for the upload (0=Planning, 1=Mid Year Review, 2=End Year Review)
   * @returns {Promise<Object>} Upload result
   */
  async uploadFile(file, year, cycle) {
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('year', year);
      formData.append('cycle', cycle);

      const response = await http.post(
        `${APP_CONFIG.apiDomain}/api/partnerreferencedataupload/uploadfile`,
        formData
        // Note: Don't set Content-Type header manually for FormData
        // The browser will set it automatically with the correct boundary
      );
      
      if (response.data && response.data.resultStatus === ResultStatus.Success) {
        return response.data.item || {};
      } else {
        throw new Error(response.data?.message || "Upload failed");
      }
    } catch (error) {
      console.error("Error uploading file:", error);
      throw error;
    }
  }

  /**
   * Validate uploaded data
   * @param {string} uploadId - Upload ID (GUID) to validate
   * @returns {Promise<Object>} Validation result
   */
  async validateUpload(uploadId) {
    try {
      const response = await http.get(
        `${APP_CONFIG.apiDomain}/api/partnerreferencedataupload/validateupload?uploadId=${uploadId}`
      );
      
      if (response.data && response.data.resultStatus === ResultStatus.Success) {
        return response.data.item || {};
      } else {
        throw new Error(response.data?.message || "Validation failed");
      }
    } catch (error) {
      console.error("Error validating upload:", error);
      throw error;
    }
  }

  /**
   * Submit validated data to final PartnerReferenceData table
   * @param {string} uploadId - Upload ID (GUID) to submit
   * @param {boolean} overwriteExisting - Default true. When true, replaces existing data for the same partner/year/cycle
   * @returns {Promise<Object>} Submit result
   */
  async submitUpload(uploadId, overwriteExisting = true) {
    try {
      const params = new URLSearchParams();
      params.append('uploadId', uploadId);
      params.append('overwriteExisting', overwriteExisting);

      const response = await http.post(
        `${APP_CONFIG.apiDomain}/api/partnerreferencedataupload/submitupload?${params.toString()}`
      );
      
      if (response.data && response.data.resultStatus === ResultStatus.Success) {
        return response.data.item || {};
      } else {
        throw new Error(response.data?.message || "Submit failed");
      }
    } catch (error) {
      console.error("Error submitting upload:", error);
      throw error;
    }
  }

  /**
   * Delete an upload record
   * @param {string} uploadId - Upload ID (GUID) to delete
   * @returns {Promise<Object>} Delete result
   */
  async deleteUpload(uploadId) {
    try {
      const params = new URLSearchParams();
      params.append('uploadId', uploadId);

      const response = await http.delete(
        `${APP_CONFIG.apiDomain}/api/partnerreferencedataupload/deleteupload?${params.toString()}`
      );
      
      if (response.data && response.data.resultStatus === ResultStatus.Success) {
        return response.data.item || {};
      } else {
        throw new Error(response.data?.message || "Delete failed");
      }
    } catch (error) {
      console.error("Error deleting upload:", error);
      throw error;
    }
  }

  /**
   * Get upload template file
   * @returns {Promise<Blob>} Template file blob
   */
  async getUploadTemplate() {
    try {
      const response = await http.get(
        `${APP_CONFIG.apiDomain}/api/partnerreferencedataupload/getuploadtemplate`,
        { responseType: 'blob' }
      );
      return response.data;
    } catch (error) {
      console.error("Error getting upload template:", error);
      throw error;
    }
  }

  /**
   * Search partner reference data with filtering and pagination
   * @param {number} year - Filter by year
   * @param {number} cycle - Filter by cycle
   * @param {string} partnerId - Filter by partner ID (GUID)
   * @param {number} pageIndex - Page index (0-based, default: 0)
   * @param {number} pageSize - Page size (default: 20)
   * @returns {Promise<Object>} Paginated list of partner reference data
   */
  async searchPartnerReferenceData(year = null, cycle = null, partnerId = null, pageIndex = 0, pageSize = 20) {
    try {
      const params = new URLSearchParams();
      if (year) params.append('year', year);
      if (cycle !== null && cycle !== undefined) params.append('cycle', cycle);
      if (partnerId) params.append('partnerId', partnerId);
      params.append('pageIndex', pageIndex);
      params.append('pageSize', pageSize);

      const response = await http.get(
        `${APP_CONFIG.apiDomain}/api/partnerreferencedataupload/searchpartnerreferencedata?${params.toString()}`
      );
      
      if (response.data && response.data.resultStatus === ResultStatus.Success) {
        return response.data || { item: { items: [], totalCount: 0 } };
      } else {
        return { item: { items: [], totalCount: 0 } };
      }
    } catch (error) {
      console.error("Error searching partner reference data:", error);
      return { item: { items: [], totalCount: 0 } };
    }
  }

  /**
   * Export partner reference data to Excel
   * @param {number} year - Year to export
   * @param {number} cycle - Cycle to export
   * @returns {Promise<Blob>} Excel file blob
   */
  async exportPartnerReferenceDataToExcel(year, cycle) {
    try {
      const params = new URLSearchParams();
      if (year) params.append('year', year);
      if (cycle !== null && cycle !== undefined) params.append('cycle', cycle);

      const response = await http.get(
        `${APP_CONFIG.apiDomain}/api/partnerreferencedataupload/exportpartnerreferencedatatoexcel?${params.toString()}`,
        { responseType: 'blob' }
      );
      return response.data;
    } catch (error) {
      console.error("Error exporting partner reference data:", error);
      throw error;
    }
  }

  /**
   * Get available column names for Form Creator mapping based on questionnaire year
   * @param {number} year - Questionnaire year to get relevant column names for
   * @param {boolean} includeCyclePrefixes - Whether to include cycle prefixes for disambiguation (default: true)
   * @returns {Promise<Array>} Array of column name objects with value and text properties
   */
  async getAvailableColumnNamesForMapping(year = null, includeCyclePrefixes = true) {
    try {
      // If year is provided, use the new backend API
      if (year) {
        const response = await http.get(
          `${APP_CONFIG.apiDomain}/api/partnerreferencedataupload/getavailablecolumnnamesforformcreator?year=${year}&includeCyclePrefixes=${includeCyclePrefixes}`
        );

        if (response.data && response.data.resultStatus === ResultStatus.Success) {
          const columnChoices = response.data.item || [];
          console.log(`Found ${columnChoices.length} column names for mapping (Year: ${year})`);
          return columnChoices;
        } else {
          console.warn("Failed to get column names from backend, using fallback");
          return this.getFallbackColumnNames();
        }
      }

      // Fallback to old implementation for backward compatibility
      const metadataList = await this.getPartnerReferenceDataMetas();

      if (!metadataList || metadataList.length === 0) {
        console.warn("No partner reference data metadata found");
        return this.getFallbackColumnNames();
      }

      // Extract column names from all active metadata
      const columnNamesSet = new Set();

      for (const meta of metadataList) {
        if (meta.isActive && meta.partnerReferenceDataMetaDetails) {
          for (const detail of meta.partnerReferenceDataMetaDetails) {
            if (detail.columnName && detail.columnName.trim()) {
              columnNamesSet.add(detail.columnName.trim());
            }
          }
        }
      }

      // Convert to array and sort alphabetically
      const columnNames = Array.from(columnNamesSet)
        .sort((a, b) => a.localeCompare(b))
        .map(name => ({
          value: name,
          text: name
        }));

      // If no columns found, return fallback
      if (columnNames.length === 0) {
        console.warn("No column names found in metadata, using fallback");
        return this.getFallbackColumnNames();
      }

      console.log(`Found ${columnNames.length} column names for mapping`);
      return columnNames;

    } catch (error) {
      console.error("Error getting column names for mapping:", error);
      return this.getFallbackColumnNames();
    }
  }

  /**
   * Get partner reference data metadata by year with all cycles
   * @param {number} year - Year to filter metadata
   * @returns {Promise<Array>} Array of metadata objects for the specified year
   */
  async getPartnerReferenceDataMetasByYear(year) {
    try {
      const response = await http.get(
        `${APP_CONFIG.apiDomain}/api/partnerreferencedataupload/getpartnerreferencedatametasbyyear?year=${year}`
      );

      if (response.data && response.data.resultStatus === ResultStatus.Success) {
        return response.data.item || [];
      } else {
        console.error("Failed to get metadata by year:", response.data?.message);
        return [];
      }
    } catch (error) {
      console.error("Error getting metadata by year:", error);
      return [];
    }
  }

  /**
   * Get fallback column names when API fails or no data is available
   * @returns {Array} Array of fallback column name objects
   */
  getFallbackColumnNames() {
    const fallbackColumns = [
      "Employee ID", "Employee Name", "Sub-Service Line",
      "Est Rev Won - Originator", "Est Rev Won - Partner Lead", "Est Rev Won - Team",
      "New Sales", "Recurring Sales", "NP Growth by PMP", "Net Production by PMP",
      "Margin %", "Margin $", "Partner's Billable Hours",
      "WIP Aging Days", "AR Aging Days", "Bad Debt (%)", "CVI Score",
      "File Volume", "Market Share", "Net Fee Revenue",
      "Primary Industry", "Secondary Industry", "Talent Dev Hours",
      "Financial Services", "Technology, Media and Telecommunications",
      "Consumer Business", "Professional Services", "Manufacturing",
      "Not-For-Profit & Education", "Real Estate & Construction",
      "Agriculture", "Natural Resources", "Public Sector", "Private Equity"
    ];

    return fallbackColumns
      .sort((a, b) => a.localeCompare(b))
      .map(name => ({
        value: name,
        text: name
      }));
  }
}

// Export singleton instance
const partnerReferenceDataUploadService = new PartnerReferenceDataUploadService();
export default partnerReferenceDataUploadService;
