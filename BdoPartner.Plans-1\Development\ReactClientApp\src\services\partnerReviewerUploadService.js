import http from "../core/http/httpClient";
import APP_CONFIG from "../core/config/appConfig";
import { ResultStatus } from "../core/enumertions/resultStatus";

/**
 * Partner Reviewer Upload Service for handling partner reviewer upload API calls
 * Provides methods to manage partner reviewer uploads, validation, and processing
 */
class PartnerReviewerUploadService {
  /**
   * Search partner reviewer uploads with filtering and pagination
   * @param {number} year - Filter by year
   * @param {number} status - Filter by status
   * @param {number} pageIndex - Page index (0-based, default: 0)
   * @param {number} pageSize - Page size (default: 20)
   * @returns {Promise<Object>} Paginated list of uploads with metadata
   */
  async searchPartnerReviewerUploads(year = null, status = null, pageIndex = 0, pageSize = 20) {
    try {
      const params = new URLSearchParams();
      if (year) params.append('year', year);
      if (status !== null && status !== undefined) params.append('status', status);
      params.append('pageIndex', pageIndex);
      params.append('pageSize', pageSize);

      const response = await http.get(
        `${APP_CONFIG.apiDomain}/api/partnerreviewerupload/searchpartnerrevieweruploads?${params.toString()}`
      );
      
      if (response.data && response.data.resultStatus === ResultStatus.Success) {
        return response.data.item || {};
      } else {
        console.error("Failed to search partner reviewer uploads:", response.data?.message);
        return { items: [], totalCount: 0 };
      }
    } catch (error) {
      console.error("Error searching partner reviewer uploads:", error);
      return { items: [], totalCount: 0 };
    }
  }

  /**
   * Get partner reviewer upload details
   * @param {number} uploadId - Upload ID
   * @param {boolean} includeValidOnly - Include only valid records
   * @param {boolean} includeInvalidOnly - Include only invalid records
   * @returns {Promise<Array>} List of upload details
   */
  async getPartnerReviewerUploadDetails(uploadId, includeValidOnly = false, includeInvalidOnly = false) {
    try {
      const params = new URLSearchParams();
      params.append('uploadId', uploadId);
      if (includeValidOnly) params.append('includeValidOnly', includeValidOnly);
      if (includeInvalidOnly) params.append('includeInvalidOnly', includeInvalidOnly);

      const response = await http.get(
        `${APP_CONFIG.apiDomain}/api/partnerreviewerupload/getpartnerrevieweruploaddetails?${params.toString()}`
      );
      
      if (response.data && response.data.resultStatus === ResultStatus.Success) {
        return response.data.item || [];
      } else {
        console.error("Failed to get upload details:", response.data?.message);
        return [];
      }
    } catch (error) {
      console.error("Error getting upload details:", error);
      return [];
    }
  }

  /**
   * Upload Excel or CSV file for partner reviewer assignments
   * @param {File} file - Excel or CSV file
   * @param {string} years - Years for the assignments (comma-separated string, e.g., "2023,2024")
   * @returns {Promise<Object>} Upload result
   */
  async uploadFile(file, years) {
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('years', years);

      const response = await http.post(
        `${APP_CONFIG.apiDomain}/api/partnerreviewerupload/uploadfile`,
        formData
        // Note: Don't set Content-Type header manually for FormData
        // The browser will set it automatically with the correct boundary
      );
      
      if (response.data && response.data.resultStatus === ResultStatus.Success) {
        return response.data.item || {};
      } else {
        throw new Error(response.data?.message || "Upload failed");
      }
    } catch (error) {
      console.error("Error uploading file:", error);
      throw error;
    }
  }

  /**
   * Validate uploaded data
   * @param {number} uploadId - Upload ID to validate
   * @returns {Promise<Object>} Validation result
   */
  async validateUpload(uploadId) {
    try {
      const response = await http.get(
        `${APP_CONFIG.apiDomain}/api/partnerreviewerupload/validateupload?uploadId=${uploadId}`
      );

      if (response.data && response.data.resultStatus === ResultStatus.Success) {
        return response.data.item || {};
      } else {
        throw new Error(response.data?.message || "Validation failed");
      }
    } catch (error) {
      console.error("Error validating upload:", error);
      throw error;
    }
  }

  /**
   * Submit validated data to final PartnerReviewer table
   * @param {number} uploadId - Upload ID to submit
   * @param {boolean} overwriteExisting - Default true. When true, replaces all data for the specified years
   * @returns {Promise<Object>} Submit result
   */
  async submitUpload(uploadId, overwriteExisting = true) {
    try {
      const params = new URLSearchParams();
      params.append('uploadId', uploadId);
      params.append('overwriteExisting', overwriteExisting);

      const response = await http.post(
        `${APP_CONFIG.apiDomain}/api/partnerreviewerupload/submitupload?${params.toString()}`
      );
      
      if (response.data && response.data.resultStatus === ResultStatus.Success) {
        return response.data.item || {};
      } else {
        throw new Error(response.data?.message || "Submit failed");
      }
    } catch (error) {
      console.error("Error submitting upload:", error);
      throw error;
    }
  }



  /**
   * Get partner reviewers for a specific year
   * @param {number} year - Year to filter by
   * @returns {Promise<Array>} List of partner reviewers
   */
  async getPartnerReviewersByYear(year) {
    try {
      const response = await http.get(
        `${APP_CONFIG.apiDomain}/api/partnerreviewerupload/getpartnerreviewersbyyear?year=${year}`
      );
      
      if (response.data && response.data.resultStatus === ResultStatus.Success) {
        return response.data.item || [];
      } else {
        console.error("Failed to get partner reviewers:", response.data?.message);
        return [];
      }
    } catch (error) {
      console.error("Error getting partner reviewers:", error);
      return [];
    }
  }

  /**
   * Download upload template file
   * @returns {Promise<Blob>} Template file blob
   */
  async getUploadTemplate() {
    try {
      const response = await http.get(
        `${APP_CONFIG.apiDomain}/api/partnerreviewerupload/getuploadtemplate`,
        {
          responseType: 'blob',
        }
      );
      
      return response.data;
    } catch (error) {
      console.error("Error downloading template:", error);
      throw error;
    }
  }

  /**
   * Export partner reviewers to Excel
   * @param {number} year - Year to export
   * @returns {Promise<Blob>} Excel file blob
   */
  async exportPartnerReviewersToExcel(year) {
    try {
      const response = await http.get(
        `${APP_CONFIG.apiDomain}/api/partnerreviewerupload/exportpartnerreviewerstoexcel?year=${year}`,
        {
          responseType: 'blob',
        }
      );
      
      return response.data;
    } catch (error) {
      console.error("Error exporting to Excel:", error);
      throw error;
    }
  }

  /**
   * Update partner reviewer assignment
   * @param {Object} partnerReviewer - Partner reviewer object to update
   * @returns {Promise<Object>} Updated partner reviewer object
   */
  async updatePartnerReviewer(partnerReviewer) {
    try {
      const response = await http.put(
        `${APP_CONFIG.apiDomain}/api/partnerreviewerupload/updatepartnerreviewer`,
        partnerReviewer
      );
      
      if (response.data && response.data.resultStatus === ResultStatus.Success) {
        return response.data.item || {};
      } else {
        throw new Error(response.data?.message || "Update failed");
      }
    } catch (error) {
      console.error("Error updating partner reviewer:", error);
      throw error;
    }
  }

  /**
   * Delete partner reviewer assignment
   * @param {string} id - Partner reviewer ID
   * @returns {Promise<Object>} Success result
   */
  async deletePartnerReviewer(id) {
    try {
      const response = await http.delete(
        `${APP_CONFIG.apiDomain}/api/partnerreviewerupload/deletepartnerreviewer?id=${id}`
      );

      if (response.data && response.data.resultStatus === ResultStatus.Success) {
        return response.data.item || {};
      } else {
        throw new Error(response.data?.message || "Delete failed");
      }
    } catch (error) {
      console.error("Error deleting partner reviewer:", error);
      throw error;
    }
  }

  /**
   * Delete partner reviewer upload and all its details
   * Only uploads with validation failed status can be deleted
   * @param {number} uploadId - Upload ID to delete
   * @returns {Promise<Object>} Success result
   */
  async deleteUpload(uploadId) {
    try {
      const response = await http.delete(
        `${APP_CONFIG.apiDomain}/api/partnerreviewerupload/deletepartnerreviewerupload?uploadId=${uploadId}`
      );

      if (response.data && response.data.resultStatus === ResultStatus.Success) {
        return response.data.item || {};
      } else {
        throw new Error(response.data?.message || "Delete upload failed");
      }
    } catch (error) {
      console.error("Error deleting upload:", error);
      throw error;
    }
  }
}

// Export singleton instance
const partnerReviewerUploadService = new PartnerReviewerUploadService();
export default partnerReviewerUploadService;
