import http from "../core/http/httpClient";
import APP_CONFIG from "../core/config/appConfig";
import { ResultStatus } from "../core/enumertions/resultStatus";

/**
 * Partner Service for handling partner-related API calls
 * Provides methods to search and retrieve partner data from the backend API
 */
class PartnerService {
  /**
   * Search partners with filtering and pagination
   * @param {string} searchTerm - Search term for partner name or email
   * @param {string} partnerType - Filter by partner type
   * @param {string} department - Filter by department
   * @param {string} location - Filter by location
   * @param {boolean} isActive - Filter by active status
   * @param {number} pageNumber - Page number (default: 1)
   * @param {number} pageSize - Page size (default: 50)
   * @returns {Promise<Array>} Array of partners matching the search criteria
   */
  async searchPartners(searchTerm = null, partnerType = null, department = null, 
                      location = null, isActive = null, pageNumber = 1, pageSize = 50) {
    try {
      const params = new URLSearchParams();
      
      if (searchTerm) params.append('searchTerm', searchTerm);
      if (partnerType) params.append('partnerType', partnerType);
      if (department) params.append('department', department);
      if (location) params.append('location', location);
      if (isActive !== null && isActive !== undefined) params.append('isActive', isActive);
      params.append('pageNumber', pageNumber);
      params.append('pageSize', pageSize);

      const response = await http.get(
        `${APP_CONFIG.apiDomain}/api/partner/searchpartners?${params.toString()}`
      );
      
      if (response.data && response.data.resultStatus === ResultStatus.Success) {
        return response.data.item || [];
      } else {
        console.error("Failed to search partners:", response.data?.message);
        return [];
      }
    } catch (error) {
      console.error("Error searching partners:", error);
      return [];
    }
  }

  /**
   * Search partners for autocomplete (simplified search with smaller page size)
   * @param {string} searchTerm - Search term for partner name
   * @param {number} pageSize - Page size (default: 20 for autocomplete)
   * @returns {Promise<Array>} Array of partners for autocomplete suggestions
   */
  async searchPartnersForAutocomplete(searchTerm, pageSize = 20) {
    try {
      if (!searchTerm || searchTerm.trim().length < 2) {
        return [];
      }

      const partners = await this.searchPartners(
        searchTerm.trim(), 
        null, // partnerType
        null, // department
        null, // location
        true, // isActive - only show active partners
        1,    // pageNumber
        pageSize
      );

      // Transform to autocomplete format with display name and ID
      return partners.map(partner => ({
        id: partner.id,
        employeeId: partner.employeeId,
        displayName: partner.displayName || `${partner.firstName} ${partner.lastName}`,
        firstName: partner.firstName,
        lastName: partner.lastName,
        mail: partner.mail,
        department: partner.department,
        location: partner.location
      }));
    } catch (error) {
      console.error("Error searching partners for autocomplete:", error);
      return [];
    }
  }

  /**
   * Get partner by ID
   * @param {string} id - Partner ID
   * @returns {Promise<Object|null>} Partner details or null if not found
   */
  async getPartnerById(id) {
    try {
      if (!id) return null;

      const response = await http.get(
        `${APP_CONFIG.apiDomain}/api/partner/getpartnerbyid?id=${id}`
      );
      
      if (response.data && response.data.resultStatus === ResultStatus.Success) {
        return response.data.item || null;
      } else {
        console.error("Failed to get partner by ID:", response.data?.message);
        return null;
      }
    } catch (error) {
      console.error("Error getting partner by ID:", error);
      return null;
    }
  }

  /**
   * Get partner by employee ID
   * @param {number} employeeId - Employee ID
   * @returns {Promise<Object|null>} Partner details or null if not found
   */
  async getPartnerByEmployeeId(employeeId) {
    try {
      if (!employeeId) return null;

      const response = await http.get(
        `${APP_CONFIG.apiDomain}/api/partner/getpartnerbyemployeeid?employeeId=${employeeId}`
      );
      
      if (response.data && response.data.resultStatus === ResultStatus.Success) {
        return response.data.item || null;
      } else {
        console.error("Failed to get partner by employee ID:", response.data?.message);
        return null;
      }
    } catch (error) {
      console.error("Error getting partner by employee ID:", error);
      return null;
    }
  }

  /**
   * Get partners for lookup/dropdown purposes
   * @param {boolean} includeInactive - Include inactive partners
   * @returns {Promise<Array>} Array of lookup items with key-value pairs
   */
  async getPartnersLookup(includeInactive = false) {
    try {
      const response = await http.get(
        `${APP_CONFIG.apiDomain}/api/partner/getpartnerslookup?includeInactive=${includeInactive}`
      );
      
      if (response.data && response.data.resultStatus === ResultStatus.Success) {
        return response.data.item || [];
      } else {
        console.error("Failed to get partners lookup:", response.data?.message);
        return [];
      }
    } catch (error) {
      console.error("Error getting partners lookup:", error);
      return [];
    }
  }
}

// Export singleton instance
const partnerService = new PartnerService();
export default partnerService;
