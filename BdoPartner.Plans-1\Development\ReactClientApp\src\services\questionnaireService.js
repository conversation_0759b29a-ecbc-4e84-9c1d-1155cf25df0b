import http from "../core/http/httpClient";
import APP_CONFIG from "../core/config/appConfig";
import { ResultStatus } from "../core/enumertions/resultStatus";
import requestDeduplication from "../core/http/requestDeduplication";

/**
 * Questionnaire Service for handling questionnaire API calls
 * Provides methods to manage questionnaires, including CRUD operations and publishing
 */
class QuestionnaireService {
  /**
   * Search questionnaires with filtering and pagination
   * @param {string} searchTerm - Search term for filtering
   * @param {number} year - Filter by year
   * @param {number} status - Filter by status
   * @param {boolean} isActive - Filter by active status
   * @param {number} pageIndex - Page index (0-based, default: 0)
   * @param {number} pageSize - Page size (default: 20)
   * @returns {Promise<Object>} Paginated list of questionnaires with metadata
   */
  async searchQuestionnaires(searchTerm = null, year = null, status = null, isActive = null, pageIndex = 0, pageSize = 20) {
    const params = new URLSearchParams();
    if (searchTerm && searchTerm.trim()) {
      params.append('searchTerm', searchTerm.trim());
    }
    if (year !== null && year !== undefined) params.append('year', year);
    if (status !== null && status !== undefined) params.append('status', status);
    if (isActive !== null && isActive !== undefined) params.append('isActive', isActive);
    params.append('pageIndex', pageIndex.toString());
    params.append('pageSize', pageSize.toString());

    const queryString = params.toString();
    const url = `${APP_CONFIG.apiDomain}/api/Questionnaire/SearchQuestionnaires${queryString ? `?${queryString}` : ''}`;

    // Create unique key for request deduplication
    const requestKey = requestDeduplication.createRequestKey('GET', url, {});

    return requestDeduplication.execute(requestKey, async () => {
      try {
        const response = await http.get(url);

        if (response.data && response.data.resultStatus === ResultStatus.Success) {
          const data = response.data.item;
          // Handle IPagedList response structure
          if (data && data.items && Array.isArray(data.items)) {
            return {
              items: data.items,
              totalCount: data.totalCount || 0,
              pageIndex: data.pageIndex || 0,
              pageSize: data.pageSize || pageSize,
              totalPages: data.totalPages || 0,
              hasPreviousPage: data.hasPreviousPage || false,
              hasNextPage: data.hasNextPage || false
            };
          } else {
            return { items: [], totalCount: 0 };
          }
        } else {
          console.error("Failed to search questionnaires:", response.data?.message);
          return { items: [], totalCount: 0 };
        }
      } catch (error) {
        console.error("Error searching questionnaires:", error);
        return { items: [], totalCount: 0 };
      }
    });
  }

  /**
   * Get questionnaire by ID
   * @param {string} id - Questionnaire ID
   * @returns {Promise<Object|null>} Questionnaire object or null if not found
   */
  async getQuestionnaireById(id) {
    const url = `${APP_CONFIG.apiDomain}/api/Questionnaire/GetQuestionnaireById?id=${id}`;

    // Create unique key for request deduplication
    const requestKey = requestDeduplication.createRequestKey('GET', url, {});

    return requestDeduplication.execute(requestKey, async () => {
      try {
        const response = await http.get(url);

        if (response.data && response.data.resultStatus === ResultStatus.Success) {
          return response.data.item || null;
        } else {
          console.error("Failed to get questionnaire:", response.data?.message);
          return null;
        }
      } catch (error) {
        console.error("Error getting questionnaire:", error);
        return null;
      }
    });
  }

  /**
   * Create a new questionnaire
   * @param {Object} questionnaireData - Questionnaire data
   * @param {string} questionnaireData.name - Questionnaire name
   * @param {number} questionnaireData.year - Questionnaire year
   * @param {number} questionnaireData.status - Status (0=Draft, 1=Published, 2=Archived)
   * @param {boolean} questionnaireData.isActive - Active status
   * @param {string} questionnaireData.draftDefinitionJson - Draft definition JSON
   * @param {boolean} questionnaireData.acknowledgement - Acknowledgement required
   * @param {string} questionnaireData.acknowledgementText - Acknowledgement text
   * @param {boolean} questionnaireData.generalComments - General comments enabled
   * @param {string} questionnaireData.generalCommentsText - General comments text
   * @param {number} questionnaireData.formSystemVersion - Form system version
   * @returns {Promise<Object>} Created questionnaire object
   */
  async createQuestionnaire(questionnaireData) {
    try {
      const response = await http.post(`${APP_CONFIG.apiDomain}/api/Questionnaire/CreateQuestionnaire`, questionnaireData);
      
      if (response.data && response.data.resultStatus === ResultStatus.Success) {
        return response.data.item || {};
      } else {
        throw new Error(response.data?.message || "Failed to create questionnaire");
      }
    } catch (error) {
      console.error("Error creating questionnaire:", error);
      throw error;
    }
  }

  /**
   * Update an existing questionnaire
   * @param {Object} questionnaireData - Updated questionnaire data
   * @returns {Promise<Object>} Updated questionnaire object
   */
  async updateQuestionnaire(questionnaireData) {
    try {
      const response = await http.post(`${APP_CONFIG.apiDomain}/api/Questionnaire/UpdateQuestionnaire`, questionnaireData);
      
      if (response.data && response.data.resultStatus === ResultStatus.Success) {
        return response.data.item || {};
      } else {
        throw new Error(response.data?.message || "Failed to update questionnaire");
      }
    } catch (error) {
      console.error("Error updating questionnaire:", error);
      throw error;
    }
  }

  /**
   * Check if there's already a published questionnaire in the specified year
   * @param {number} year - Year to check
   * @param {string} excludeId - Optional questionnaire ID to exclude from the check
   * @returns {Promise<boolean>} True if a published questionnaire exists in the year
   */
  async hasPublishedQuestionnaireInYear(year, excludeId = null) {
    try {
      const params = new URLSearchParams({ year: year.toString() });
      if (excludeId) {
        params.append('excludeId', excludeId);
      }

      const response = await http.get(`${APP_CONFIG.apiDomain}/api/Questionnaire/HasPublishedQuestionnaireInYear?${params}`);

      if (response.data && response.data.resultStatus === ResultStatus.Success) {
        return response.data.item || false;
      } else {
        throw new Error(response.data?.message || "Failed to check for published questionnaire");
      }
    } catch (error) {
      console.error("Error checking for published questionnaire:", error);
      throw error;
    }
  }

  /**
   * Publish a questionnaire by ID
   * @param {string} questionnaireId - Questionnaire ID to publish
   * @returns {Promise<Object>} Published questionnaire object
   */
  async publishQuestionnaire(questionnaireId) {
    try {
      const response = await http.post(`${APP_CONFIG.apiDomain}/api/Questionnaire/PublishQuestionnaire/${questionnaireId}`);

      if (response.data && response.data.resultStatus === ResultStatus.Success) {
        return response.data.item || {};
      } else {
        throw new Error(response.data?.message || "Failed to publish questionnaire");
      }
    } catch (error) {
      console.error("Error publishing questionnaire:", error);
      throw error;
    }
  }

  /**
   * Save questionnaire as draft (convenience method for updating with draft status)
   * @param {Object} questionnaire - Questionnaire object
   * @param {string} surveyName - Survey name
   * @param {number} selectedYear - Selected year
   * @param {string} definitionJson - Survey definition JSON
   * @returns {Promise<Object>} Updated questionnaire object
   */
  async saveDraft(questionnaire, surveyName, selectedYear, definitionJson) {
    try {
      const updateData = {
        ...questionnaire,
        name: surveyName,
        year: selectedYear,
        draftDefinitionJson: definitionJson
      };

      return await this.updateQuestionnaire(updateData);
    } catch (error) {
      console.error("Error saving draft:", error);
      throw error;
    }
  }

  /**
   * Publish questionnaire with updated definition (convenience method)
   * @param {Object} questionnaire - Questionnaire object
   * @param {string} surveyName - Survey name
   * @param {number} selectedYear - Selected year
   * @param {string} definitionJson - Survey definition JSON
   * @returns {Promise<Object>} Published questionnaire object
   */
  async publishWithDefinition(questionnaire, surveyName, selectedYear, definitionJson) {
    try {
      const updateData = {
        ...questionnaire,
        name: surveyName,
        year: selectedYear,
        draftDefinitionJson: definitionJson,
        definitionJson: definitionJson, // Also update the published definition
        status: 1 // Published status
      };

      return await this.updateQuestionnaire(updateData);
    } catch (error) {
      console.error("Error publishing questionnaire:", error);
      throw error;
    }
  }

  /**
   * Validate if a questionnaire can be deleted
   * @param {string} questionnaireId - Questionnaire ID to validate
   * @returns {Promise<Object>} Validation result
   */
  async validateQuestionnaireForDeletion(questionnaireId) {
    try {
      const response = await http.get(`${APP_CONFIG.apiDomain}/api/Questionnaire/ValidateQuestionnaireForDeletion?id=${questionnaireId}`);

      if (response.data && response.data.resultStatus === ResultStatus.Success) {
        return response.data.item || {};
      } else {
        throw new Error(response.data?.message || "Failed to validate questionnaire for deletion");
      }
    } catch (error) {
      console.error("Error validating questionnaire for deletion:", error);
      throw error;
    }
  }

  /**
   * Delete a questionnaire by ID
   * @param {string} questionnaireId - Questionnaire ID to delete
   * @returns {Promise<Object>} Success result
   */
  async deleteQuestionnaire(questionnaireId) {
    try {
      const response = await http.post(`${APP_CONFIG.apiDomain}/api/Questionnaire/DeleteQuestionnaire?id=${questionnaireId}`);

      if (response.data && response.data.resultStatus === ResultStatus.Success) {
        return response.data.item || {};
      } else {
        throw new Error(response.data?.message || "Failed to delete questionnaire");
      }
    } catch (error) {
      console.error("Error deleting questionnaire:", error);
      throw error;
    }
  }

  /**
   * Get all questionnaires (without pagination)
   * @returns {Promise<Array>} List of all questionnaires
   */
  async getAllQuestionnaires() {
    try {
      const response = await http.get(`${APP_CONFIG.apiDomain}/api/Questionnaire/GetAllQuestionnaires`);
      
      if (response.data && response.data.resultStatus === ResultStatus.Success) {
        return response.data.item || [];
      } else {
        console.error("Failed to get all questionnaires:", response.data?.message);
        return [];
      }
    } catch (error) {
      console.error("Error getting all questionnaires:", error);
      return [];
    }
  }
}

// Export singleton instance
const questionnaireService = new QuestionnaireService();
export default questionnaireService;
