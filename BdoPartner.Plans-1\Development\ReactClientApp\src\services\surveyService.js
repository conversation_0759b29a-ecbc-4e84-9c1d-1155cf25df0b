import http from "../core/http/httpClient";
import APP_CONFIG from "../core/config/appConfig";
import { ResultStatus } from "../core/enumertions/resultStatus";

/**
 * Survey Service for handling questionnaire-related API calls
 * Provides methods to fetch questionnaire data from the backend API
 */
class SurveyService {
  /**
   * Get questionnaires by year
   * @param {number} year - The year to filter questionnaires by
   * @returns {Promise<Array>} Array of questionnaires for the specified year
   */
  async getQuestionnairesByYear(year) {
    try {
      const response = await http.get(
        `${APP_CONFIG.apiDomain}/api/questionnaire/getquestionnairesbyyear?year=${year}`
      );
      
      if (response.data && response.data.resultStatus === ResultStatus.Success) {
        return response.data.item || [];
      } else {
        console.error("Failed to fetch questionnaires:", response.data?.message);
        return [];
      }
    } catch (error) {
      console.error("Error fetching questionnaires by year:", error);
      return [];
    }
  }

  /**
   * Get questionnaire by ID
   * @param {string} id - The questionnaire ID
   * @returns {Promise<Object|null>} Questionnaire object or null if not found
   */
  async getQuestionnaireById(id) {
    try {
      const response = await http.get(
        `${APP_CONFIG.apiDomain}/api/questionnaire/getquestionnairebyid?id=${id}`
      );
      
      if (response.data && response.data.resultStatus === ResultStatus.Success) {
        return response.data.item;
      } else {
        console.error("Failed to fetch questionnaire:", response.data?.message);
        return null;
      }
    } catch (error) {
      console.error("Error fetching questionnaire by ID:", error);
      return null;
    }
  }

  /**
   * Get all active questionnaires
   * @returns {Promise<Array>} Array of active questionnaires
   */
  async getActiveQuestionnaires() {
    try {
      const response = await http.get(
        `${APP_CONFIG.apiDomain}/api/questionnaire/getquestionnaires`
      );
      
      if (response.data && response.data.resultStatus === ResultStatus.Success) {
        return response.data.item || [];
      } else {
        console.error("Failed to fetch active questionnaires:", response.data?.message);
        return [];
      }
    } catch (error) {
      console.error("Error fetching active questionnaires:", error);
      return [];
    }
  }

  /**
   * Search questionnaires with filters
   * @param {Object} filters - Search filters
   * @param {string} filters.searchTerm - Search term for questionnaire name
   * @param {number} filters.year - Year filter
   * @param {number} filters.status - Status filter
   * @param {boolean} filters.isActive - Active status filter
   * @param {number} filters.pageNumber - Page number for pagination
   * @param {number} filters.pageSize - Page size for pagination
   * @returns {Promise<Array>} Array of filtered questionnaires
   */
  async searchQuestionnaires(filters = {}) {
    try {
      const params = new URLSearchParams();
      
      if (filters.searchTerm) params.append('searchTerm', filters.searchTerm);
      if (filters.year) params.append('year', filters.year);
      if (filters.status !== undefined) params.append('status', filters.status);
      if (filters.isActive !== undefined) params.append('isActive', filters.isActive);
      if (filters.pageNumber) params.append('pageNumber', filters.pageNumber);
      if (filters.pageSize) params.append('pageSize', filters.pageSize);

      const response = await http.get(
        `${APP_CONFIG.apiDomain}/api/questionnaire/searchquestionnaires?${params.toString()}`
      );
      
      if (response.data && response.data.resultStatus === ResultStatus.Success) {
        return response.data.item || [];
      } else {
        console.error("Failed to search questionnaires:", response.data?.message);
        return [];
      }
    } catch (error) {
      console.error("Error searching questionnaires:", error);
      return [];
    }
  }

  /**
   * Get the current year's questionnaire (for 2025)
   * @returns {Promise<Object|null>} Current year's questionnaire or null if not found
   */
  async getCurrentYearQuestionnaire() {
    const currentYear = new Date().getFullYear();
    const questionnaires = await this.getQuestionnairesByYear(currentYear);
    
    // Return the first active questionnaire for the current year
    return questionnaires.length > 0 ? questionnaires[0] : null;
  }
}

// Export a singleton instance
export const surveyService = new SurveyService();
export default surveyService;
