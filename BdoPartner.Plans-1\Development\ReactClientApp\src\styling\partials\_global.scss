html,
body,
#root {
  height: 100%;
  min-height: 100%;
  background-color: #f3f2f1;
}

.app {
  min-height: 100%;
  height: 100%;
}

.body-area {
  padding-top: 1%;
  width: 90%;
  margin: auto;
}

/* Default styles for buttons and dropdowns */
.p-button,
.p-dropdown {
  background-color: #e5e5ea;
  color: #1f1f1f;
  outline: none;
  border: none;

  &:hover,
  &:active,
  &:focus {
    background-color: #c9c9dd !important;
    color: #1f1f1f !important;
    border: none !important;
    box-shadow: none !important;
  }
}

/* Adding styles for red action buttons */
.p-button.action {
  background-color: #ed1a3b !important;
  color: white !important;
  border-radius: 0;
  &:focus,
  &:hover {
    background-color: #af273c !important;
    color: white !important;
  }
}

/* Red action button disabled */

.p-button.action.p-disabled {
  background-color: #c9c9dd !important;
  color: black;
}

/*  Pagination styles */
button.p-paginator-page.p-highlight {
  background-color: #ed1a3b !important;
  color: white !important;
}

.p-radiobutton .p-radiobutton-box.p-highlight {
  border-color: #ed1a3b;
  background: #ed1a3b;
}


.p-inputtext:enabled:focus,
.p-inputtext:enabled:hover,
.p-inputtext {
  background-color: transparent;
  outline: none;
  border: 1px solid #959597;
  &:hover,
  &:active,
  &:focus {
    outline: #959597;
    box-shadow: none;
  }
}

/** override padding in the Grid */
.p-datatable .p-datatable-tbody > tr > td
{
    padding: 0.55rem 0.55rem !important;
}

/* Tabs */
.p-tabview .p-tabview-nav li.p-highlight .p-tabview-nav-link {
  background-color: transparent !important;
  border-color: red !important;
  color: #6c757d;
}

.p-tabview-nav,
.p-tabview .p-tabview-nav li .p-tabview-nav-link,
.p-tabview .p-tabview-nav li.p-highlight .p-tabview-nav-link,
.p-tabview .p-tabview-panels {
  background-color: transparent !important;
}

/** Button */
.p-button-gray {
  background-color: #e8e8e8 !important; // Light background
  border: 1px solid #e8e8e8 !important; // Subtle border
  color: black !important; // Dark icon color

  &:hover {
    background-color: white !important; // BDO red on hover
    border-color: white !important;
    color: Black !important; // White icon on hover
  }

  &:focus {
    box-shadow: 0 0 0 2px rgba(223, 220, 220, 0.2) !important; // BDO red focus ring
  }

  .p-button-icon {
    font-size: 1rem !important; // Appropriate icon size
  }
}

.p-button-red {
  background-color: #ED1A3B !important; // BDO red background
  border: 1px solid #ED1A3B !important; // BDO red border
  color: white !important; // White text

  &:hover {
    background-color: #af273c !important; // Darker red on hover
    border-color: #af273c !important;
    color: white !important; // Keep white text on hover
  }

  &:focus {
    box-shadow: 0 0 0 2px rgba(237, 26, 59, 0.2) !important; // BDO red focus ring
  }

  .p-button-icon {
    font-size: 1rem !important; // Appropriate icon size
  }

  // Disabled state
  &.p-disabled {
    background-color: #c9c9dd !important;
    border-color: #c9c9dd !important;
    color: black !important;
  }
}

.hometab-status {
  color: #ED1A3B;
  text-transform: uppercase
}


/** Page title font style */
.page-title {
  margin-top: 0 !important;
  margin-bottom: 0.5rem !important;
  font-size: 1.3rem !important;
  font-weight: 600 !important;
  color: black !important;
}

.section-header {
  margin-top: 0 !important;
  margin-bottom: 0.5rem !important;
  font-size: 1.0rem !important;
  font-weight: 600 !important;
  color: black !important;
}
