/* Dashboard Component Styling */

.dashboard-container {
  margin: 0 auto;
  background-color: #f3f2f1;
  min-height: 100vh;
}

/* Welcome Section */
.welcome-section {
  margin-bottom: 3rem;

  .welcome-title {
    font-size: 2rem;
    font-weight: 600;
    color: #1f1f1f;
    margin-bottom: 1rem;
  }

  .welcome-description {
    color: #666;
    line-height: 1.6;
    font-size: 1rem;
    max-width: 800px;
  }
}

/* Dashboard Content */
.dashboard-content {
  display: flex;
  flex-direction: column;
  gap: 3rem;
}

.dashboard-section {
  background-color: white;
  border-radius: 8px;
  padding: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Section Titles */
.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f1f1f;
  margin-bottom: 1rem;
}

.section-description {
  color: #666;
  line-height: 1.6;
  margin-bottom: 2rem;
  font-size: 0.95rem;
}

.subsection-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #1f1f1f;
  margin-bottom: 0.5rem;
}

.subsection-description {
  color: #666;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  font-size: 0.9rem;
}

/* My Current Plan Styles */
.my-current-plan {
  .current-plan-card {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    
    .p-card-body {
      padding: 1.5rem;
    }
    
    .plan-details {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      gap: 2rem;
      
      .plan-info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        flex: 1;
        
        .plan-info-item {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;
          
          .plan-label {
            font-weight: 600;
            color: #1f1f1f;
            font-size: 0.9rem;
          }
          
          .plan-value {
            font-size: 1rem;
            color: #333;
            
            &.status-submitted {
              color: #28a745;
              font-weight: 500;
            }
            
            &.status-not-started {
              color: #ED1A3B;
              font-weight: 500;
              text-transform: uppercase;
            }
          }
          
          .reviewers-list {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
            
            .reviewer-item {
              display: flex;
              gap: 0.5rem;
              
              .reviewer-label {
                font-size: 0.85rem;
                color: #666;
              }
              
              .reviewer-name {
                font-size: 0.85rem;
                color: #333;
                font-weight: 500;
              }
            }
          }
        }
      }
      
      .plan-actions {
        display: flex;
        align-items: center;
        
        .view-plan-btn {
          white-space: nowrap;
        }
      }
    }
  }
}

/* My Past Plans Styles */
.my-past-plans {
  .past-plans-card {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    
    .p-card-body {
      padding: 1.5rem;
    }
    
    .past-plans-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 2rem;
      
      .filter-section {
        display: flex;
        align-items: center;
        gap: 1rem;
        
        label {
          font-weight: 600;
          color: #1f1f1f;
          font-size: 0.9rem;
          white-space: nowrap;
        }
        
        .year-dropdown {
          min-width: 120px;
        }
      }
      
      .action-section {
        display: flex;
        align-items: center;
      }
    }
  }
}

/* Partner Plans Styles */
.partner-plans {
  .reviews-section,
  .all-plans-section {
    margin-bottom: 2.5rem;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .reviews-card,
  .all-plans-card {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    
    .p-card-body {
      padding: 1.5rem;
    }
    
    .reviews-content,
    .all-plans-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 2rem;
      
      .status-info {
        display: flex;
        align-items: center;
        gap: 1rem;
        flex: 1;
        
        .status-label {
          font-weight: 600;
          color: #1f1f1f;
          font-size: 0.9rem;
        }
        
        .status-counts {
          display: flex;
          gap: 2rem;
          
          .status-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            
            .status-text {
              font-size: 0.85rem;
              color: #666;
            }
            
            .status-count {
              font-weight: 600;
              font-size: 0.9rem;
              
              &.not-started {
                color: #ED1A3B;
              }
              
              &.in-progress {
                color: #ffc107;
              }
              
              &.completed {
                color: #28a745;
              }
            }
          }
        }
      }
      
      .reviews-actions,
      .all-plans-actions {
        display: flex;
        align-items: center;
      }
    }
  }
}

/* Status styling */
.hometab-status {
  color: #ED1A3B;
  text-transform: uppercase;
  font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-container {
    padding: 1rem 0.5rem;
  }

  .dashboard-section {
    padding: 1.5rem;
  }
  
  .my-current-plan .current-plan-card .plan-details {
    flex-direction: column;
    gap: 1.5rem;
  }
  
  .my-past-plans .past-plans-card .past-plans-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .partner-plans .reviews-content,
  .partner-plans .all-plans-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .status-counts {
    flex-direction: column !important;
    gap: 0.5rem !important;
  }
}
