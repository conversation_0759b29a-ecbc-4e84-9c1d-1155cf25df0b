.subtitle {
  &--center {
    text-align: center;
    color: grey;
  }

  &--left {
    text-align: left;
    color: grey;
  }
}

.margin {
  &__bottom {
    &--deleted {
      margin-bottom: 0px;
    }
  }
}

.padding {
  &__section {
    padding: 20px 20px;
  }
  &__subsection {
    padding: 10px 0px;
  }
}

.background {
  &--white {
    background-color: white;
  }
  &--none {
    background-color: none;
  }
}

.flexbox {
  display: flex;
  &--space-between {
    justify-content: space-between;
  }
  &--vertical {
    flex-direction: column;
  }
  &--horizontal {
    flex-direction: row;
  }
  &--flex-start {
    justify-content: flex-start;
  }
  &--flex-end {
    justify-content: flex-end;
  }
}

.p-editor-toolbar,
.ql-toolbar,
.ql-snow {
  width: 50vw;
}

.p-editor-container {
  .p-editor-content {
    .ql-editor {
      background-color: transparent;
    }
  }
}

// .p-inputtext:enabled:focus,
// .p-inputtext:enabled:hover,
// .p-inputtext {
//   border: none;
// }

.p-button {
  background-color: transparent;
}
