/* Partner Reference Data Management Component Styling */

.partner-reference-data-maintain {
  .management-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    margin-bottom: 1rem;
    
    h3 {
      color: #ed1a3b;
      margin: 0;
    }
    
    .filter-section {
      display: flex;
      align-items: center;
      gap: 1rem;

      label {
        font-weight: 600;
        color: #1f1f1f;
      }

      .year-filter-field,
      .cycle-filter-field {
        display: flex;
        align-items: center;
        gap: 0.5rem;

        label {
          margin: 0;
          white-space: nowrap;
        }

        .p-dropdown {
          min-width: 120px;
        }
      }
    }
    
    .action-section {
      display: flex;
      align-items: center;
      gap: 1rem;

      .p-inputtext {
        min-width: 250px;
      }
    }
  }
  
  .p-datatable {
    .p-datatable-thead th {
      background-color: #f3f2f1 !important;
      color: #1f1f1f;
      font-weight: 600;
      border-bottom: 2px solid #ed1a3b;
    }
    
    .p-datatable-tbody tr:nth-child(even) {
      background-color: #f8f8f8 !important;
    }
    
    .p-datatable-tbody tr:hover {
      background-color: #e5e5ea !important;
    }
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .partner-reference-data-maintain .management-header {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
    
    .filter-section {
      flex-wrap: wrap;
      justify-content: center;
      gap: 0.5rem;
      
      .year-filter-field,
      .cycle-filter-field {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
        
        .p-dropdown {
          width: 100%;
          min-width: 150px;
        }
      }
    }
    
    .action-section {
      flex-direction: column;
      gap: 0.5rem;
      
      .p-inputtext {
        width: 100%;
        min-width: auto;
      }
    }
  }
}

/* Upload Partner Reference Data Component Styling - Using same styling as Upload Partner Reviewer Assignment */
.upload-partner-reference-data {
  .p-fileupload {
    .p-fileupload-choose {
      background-color: #e5e5ea !important;
      color: #1f1f1f !important;
      border: none !important;

      &:hover {
        background-color: #c9c9dd !important;
      }
    }
  }

  .upload-section {
    margin-bottom: 1rem;

    .p-card-title h5 {
      color: #ed1a3b;
      margin-bottom: 1rem;
    }
  }

  .upload-history {
    .p-datatable {
      .p-datatable-thead th {
        background-color: #f3f2f1 !important;
        color: #1f1f1f;
        font-weight: 600;
        border-bottom: 2px solid #ed1a3b;
      }

      .p-datatable-tbody tr:nth-child(even) {
        background-color: #f8f8f8 !important;
      }

      .p-datatable-tbody tr:hover {
        background-color: #e5e5ea !important;
      }
    }
  }
}

/* Upload History Header Styling - Matching Upload Partner Reviewer Assignment */
.upload-history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
  padding: 0.5rem 0;

  h3 {
    margin: 0;
    color: #ed1a3b;
  }
}

.upload-history-actions {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.upload-search-input {
  min-width: 350px !important;
  width: 350px !important;
}

/* Responsive design for upload history header - Matching Upload Partner Reviewer Assignment */
@media (max-width: 768px) {
  .upload-history-header {
    flex-direction: column;
    align-items: stretch;
  }

  .upload-history-actions {
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 1rem;
  }

  .upload-search-input {
    min-width: 250px !important;
    width: 100% !important;
    flex: 1;
  }
}
