/* Partner Reviewer Management Component Styling */

.partner-reviewer-management {
  .management-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    
    h3 {
      color: #ed1a3b;
      margin: 0;
    }
    
    .filter-section {
      display: flex;
      align-items: center;
      gap: 1rem;

      label {
        font-weight: 600;
        color: #1f1f1f;
      }

      .year-filter-field {
        display: flex;
        align-items: center;
        gap: 0.5rem;

        label {
          margin: 0;
          white-space: nowrap;
        }
      }
    }
    
    .action-section {
      display: flex;
      align-items: center;
      gap: 1rem;
    }
  }
  
  .p-datatable {
    .p-datatable-thead th {
      background-color: #f3f2f1 !important;
      color: #1f1f1f;
      font-weight: 600;
      border-bottom: 2px solid #ed1a3b;
    }
    
    .p-datatable-tbody tr:nth-child(even) {
      background-color: #f8f8f8 !important;
    }
    
    .p-datatable-tbody tr:hover {
      background-color: #e5e5ea !important;
    }
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .partner-reviewer-management .management-header {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
    
    .filter-section,
    .action-section {
      justify-content: center;
    }
  }
}
