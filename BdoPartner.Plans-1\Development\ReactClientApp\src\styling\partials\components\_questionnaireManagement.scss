/* Questionnaire Management Styling */

/* Survey Creator Styling */
.svc-creator {
  font-family: "Segoe UI", "Segoe UI Web (West European)", "Segoe UI", -apple-system, BlinkMacSystemFont, Roboto, "Helvetica Neue", sans-serif !important;
  
  .svc-creator__toolbar {
    background-color: #f8f9fa !important;
    border-bottom: 1px solid #e0e0e0 !important;
    
    .svc-toolbar-button {
      background-color: #e5e5ea !important;
      color: #1f1f1f !important;
      border: none !important;
      border-radius: 4px !important;
      padding: 8px 16px !important;
      margin: 0 4px !important;
      font-weight: 500 !important;
      
      &:hover {
        background-color: #c9c9dd !important;
        color: #1f1f1f !important;
      }
      
      &.svc-toolbar-button--primary {
        background-color: #ed1a3b !important;
        color: white !important;
        
        &:hover {
          background-color: #af273c !important;
          color: white !important;
        }
      }
    }
  }
  
  .svc-creator__content-wrapper {
    background-color: #ffffff !important;
  }
  
  .svc-toolbox {
    background-color: #f8f9fa !important;
    border-right: 1px solid #e0e0e0 !important;
    
    .svc-toolbox__category-header {
      background-color: #ed1a3b !important;
      color: white !important;
      font-weight: 600 !important;
    }
    
    .svc-toolbox__item {
      border: 1px solid #e0e0e0 !important;
      border-radius: 4px !important;
      margin: 4px !important;
      
      &:hover {
        border-color: #ed1a3b !important;
        background-color: rgba(237, 26, 59, 0.1) !important;
      }
    }
  }
  
  .svc-property-panel {
    background-color: #f8f9fa !important;
    border-left: 1px solid #e0e0e0 !important;
    
    .svc-property-panel__header {
      background-color: #ed1a3b !important;
      color: white !important;
      font-weight: 600 !important;
    }
  }
}

/* Survey Preview Styling */
.sv-root {
  font-family: "Segoe UI", "Segoe UI Web (West European)", "Segoe UI", -apple-system, BlinkMacSystemFont, Roboto, "Helvetica Neue", sans-serif !important;
  
  .sv-header {
    background-color: #ed1a3b !important;
    color: white !important;
    padding: 1rem !important;
    border-radius: 6px 6px 0 0 !important;
    
    .sv-header__title {
      font-size: 1.5rem !important;
      font-weight: 600 !important;
      margin: 0 !important;
    }
  }
  
  .sv-body {
    background-color: white !important;
    padding: 1.5rem !important;
    border: 1px solid #e0e0e0 !important;
    border-top: none !important;
    border-radius: 0 0 6px 6px !important;
  }
  
  .sv-question {
    margin-bottom: 1.5rem !important;
    
    .sv-question__title {
      font-weight: 600 !important;
      color: #495057 !important;
      margin-bottom: 0.5rem !important;
    }
    
    .sv-question__content {
      .sv-selectbase {
        .sv-selectbase__item {
          border: 1px solid #e0e0e0 !important;
          border-radius: 4px !important;
          padding: 0.5rem !important;
          margin-bottom: 0.5rem !important;
          
          &:hover {
            border-color: #ed1a3b !important;
            background-color: rgba(237, 26, 59, 0.05) !important;
          }
          
          &.sv-selectbase__item--checked {
            border-color: #ed1a3b !important;
            background-color: rgba(237, 26, 59, 0.1) !important;
          }
        }
      }
      
      .sv-text {
        border: 1px solid #e0e0e0 !important;
        border-radius: 4px !important;
        padding: 0.5rem !important;
        font-size: 0.9rem !important;
        
        &:focus {
          border-color: #ed1a3b !important;
          box-shadow: 0 0 0 2px rgba(237, 26, 59, 0.2) !important;
          outline: none !important;
        }
      }
      
      .sv-dropdown {
        border: 1px solid #e0e0e0 !important;
        border-radius: 4px !important;
        
        &:focus-within {
          border-color: #ed1a3b !important;
          box-shadow: 0 0 0 2px rgba(237, 26, 59, 0.2) !important;
        }
      }
    }
  }
  
  .sv-progress {
    background-color: #f8f9fa !important;
    border-radius: 10px !important;
    height: 8px !important;
    margin-bottom: 1rem !important;
    
    .sv-progress__bar {
      background-color: #ed1a3b !important;
      border-radius: 10px !important;
      height: 100% !important;
    }
  }
  
  .sv-action-bar {
    padding: 1rem 0 !important;
    border-top: 1px solid #e0e0e0 !important;
    margin-top: 1rem !important;
    
    .sv-btn {
      background-color: #ed1a3b !important;
      color: white !important;
      border: none !important;
      border-radius: 4px !important;
      padding: 0.5rem 1rem !important;
      font-weight: 500 !important;
      margin-right: 0.5rem !important;
      
      &:hover {
        background-color: #af273c !important;
      }
      
      &.sv-btn--secondary {
        background-color: #e5e5ea !important;
        color: #1f1f1f !important;
        
        &:hover {
          background-color: #c9c9dd !important;
        }
      }
    }
  }
}

/* Questionnaire List Styling */
.questionnaire-management {
  .p-datatable {
    .p-datatable-header {
      background-color: #f8f9fa !important;
      border-bottom: 2px solid #ed1a3b !important;
      padding: 1rem !important;
    }
    
    .p-datatable-thead > tr > th {
      background-color: #ed1a3b !important;
      color: white !important;
      font-weight: 600 !important;
      border: none !important;
      padding: 1rem !important;
    }
    
    .p-datatable-tbody > tr {
      &:hover {
        background-color: rgba(237, 26, 59, 0.05) !important;
      }
      
      > td {
        padding: 1rem !important;
        border-bottom: 1px solid #e0e0e0 !important;
      }
    }
  }
  
  .p-tag {
    font-weight: 500 !important;
    padding: 0.25rem 0.5rem !important;
    border-radius: 4px !important;
    
    &.p-tag-success {
      background-color: #28a745 !important;
      color: white !important;
    }
    
    &.p-tag-warning {
      background-color: #ffc107 !important;
      color: #212529 !important;
    }
    
    &.p-tag-secondary {
      background-color: #6c757d !important;
      color: white !important;
    }
  }
}

/* Profile Card for Questionnaire Designer */
.profile-card {
  background-color: white !important;
  border: 1px solid #e0e0e0 !important;
  border-radius: 6px !important;
  padding: 1rem !important;
  margin-bottom: 1rem !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  
  .field {
    margin-bottom: 1rem !important;
    
    label {
      display: block !important;
      margin-bottom: 0.5rem !important;
      font-weight: 600 !important;
      color: #495057 !important;
    }
    
    .p-inputtext,
    .p-dropdown {
      width: 100% !important;
      border: 1px solid #e0e0e0 !important;
      border-radius: 4px !important;
      padding: 0.5rem !important;
      
      &:focus {
        border-color: #ed1a3b !important;
        box-shadow: 0 0 0 2px rgba(237, 26, 59, 0.2) !important;
        outline: none !important;
      }
    }
  }
}

/* Survey Container */
.survey-container {
  background-color: white !important;
  border: 1px solid #e0e0e0 !important;
  border-radius: 6px !important;
  padding: 1rem !important;
  min-height: 400px !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .svc-creator {
    .svc-creator__toolbar {
      flex-wrap: wrap !important;
      
      .svc-toolbar-button {
        margin: 2px !important;
        padding: 6px 12px !important;
        font-size: 0.85rem !important;
      }
    }
  }
  
  .questionnaire-management {
    .p-datatable {
      font-size: 0.85rem !important;
      
      .p-datatable-thead > tr > th,
      .p-datatable-tbody > tr > td {
        padding: 0.5rem !important;
      }
    }
  }
  
  .profile-card {
    padding: 0.75rem !important;
    
    .flex {
      flex-direction: column !important;
      gap: 1rem !important;
      
      .flex {
        flex-direction: row !important;
      }
    }
  }
}
