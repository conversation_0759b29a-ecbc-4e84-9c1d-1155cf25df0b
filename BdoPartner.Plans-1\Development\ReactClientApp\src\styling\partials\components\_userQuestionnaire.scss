#surveyContainer {
  //progress bar styling
  .sv_custom_header {
    background-color: transparent;
  }
  .sv_progress-buttons__container {
    margin-bottom: 20px;
  }

  .sv_main.sv_default_css
    .sv_progress-buttons__list
    li.sv_progress-buttons__list-element--current::before {
    background-color: #ed1a3b ;
    border-color: #ed1a3b ;
  }

  .sv_main.sv_default_css .sv_progress-buttons__list li::before {
    background-color: transparent;
    border-color: #ed1a3b ;
  }

  .sv_main.sv_default_css
    .sv_progress-buttons__list
    li.sv_progress-buttons__list-element--passed
    + li::after {
    background-color: rgb(212, 212, 212);
  }

  .sv_main.sv_default_css .sv_progress-buttons__list .sv_progress-buttons__page-title {
    display: none;
  }

  //form body styling

  .sv_main .sv_container .sv_body .sv_p_root .sv_page_title {
    background-color: #f3f2f1;
    padding: 1.4px 0 18.62px;
    margin: 0;
    .sv-string-viewer {
      font-size: 2rem;
      font-weight: bold;
    }
  }

  // .sv_p_container {
  //   background-color: #f3f2f1;
  // }

  .sv_main .sv_container .sv_body {
    padding-left: 0;
    background-color: transparent;
    background-color: #f3f2f1;
    border: none;
  }

  //button styling
  .sv_main .sv-boolean .sv-boolean__switch {
    background-color: #ed1a3b ;
  }

  .sv_main.sv_main .sv-boolean--disabled .sv-boolean__switch {
    background-color: rgb(159, 159, 159);
  }

  .sv_main .sv_nav input[type="button"] {
    background-color: #ed1a3b ;
    height: 50px;
    margin-top: 20px;
  }

  .sv_main .sv_container .sv_body .sv_nav .sv_prev_btn {
    background-color: transparent;
    border: 1px solid black;
    color: black;
  }

  .sv_main.sv_main
    .sv-action
    .sv-action__content
    .sv-action-bar-item.sv_edit_btn {
    background-color: #ed1a3b ;
  }

  .sv_p_container
    .sv_row
    .sv_p_container
    .sv_row
    .sv_q
    .sv_qstn
    .sv_q--disabled
    .sv_qcbc
    .sv_qbln
    .sv-boolean
    .sv-boolean--disabled
    .sv-boolean__switch {
    background-color: grey;
  }

  // header styling
  .sv_main.sv_default_css .sv_header {
    background-color: #f3f2f1;
  }

  .sv_main.sv_default_css
    form
    .sv_container
    .sv_header
    .sv_header__text
    .sv-string-viewer {
    font-weight: bold;
    font-size: 30px;
  }
}
