.banner {
  height: 30px;
  display: flex;
  margin-bottom: 20px;

  
  .banner__logo img{
    width: 80px;
    height: 30px;
  }

  .banner__site-title-area{
    //margin-left: 50px;
  }

  .banner__site-title-area h1{
    margin: 0;
    font-size: 22px;
    color: #ed1a3b;
  }

  .banner__btn-return{
    margin-left: auto;
    .p-button{
      background-color: #ed1a3b !important;
      color: white !important;
    }

    .p-button:hover, .p-button:focus{
      background-color: #af273c !important;
      color: white !important;

    }
  }

  // .banner__banner-top {
  //   background-color: #ed1a3b;
  //   height: 40px;
  //   display: block;
  //   display: flex;

  //   .banner__link-container {
  //     margin-left: auto;
  //     margin-right: 20%;
  //   }
  // }

  // .banner__image-holder {
  //   display: flex;
  //   height: 90px;

  //   .banner__logo-area img {
  //     margin: 10px 30px 0px 20px;
  //   }

  //   .banner__page-title {
  //     color: white;
  //   }
  // }

  // .banner__banner-bottom {
  //   display: block;
  //   height: 40px;
  //   background-color: #333333;
  // }
}
