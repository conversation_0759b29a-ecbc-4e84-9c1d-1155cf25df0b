/* Navbar Brand Styling */
.navbar-brand-container {
  display: flex !important;
  align-items: left !important;
  padding: 0.5rem 0 !important;

  img {
    height: 25px !important; // Smaller logo size
    width: auto !important;
    margin-right: 12px !important;
  }
}

.navbar-brand-text {
  font-size: 25px !important; // Larger text size
  font-weight: 600 !important;
  color: #ED1A3B !important; // BDO red color
  margin-left: 8px !important;
  font-family: "Segoe UI", "Segoe UI Web (West European)", "Segoe UI", -apple-system, BlinkMacSystemFont, Roboto, "Helvetica Neue", sans-serif !important;
  white-space: nowrap !important;
}

/* Ensure navbar brand container doesn't get overridden by PrimeReact styles */
.p-menubar .navbar-brand-container {
  display: flex !important;
  align-items: center !important;
}

/* Navbar wrapper styling */
.navbar-wrapper {
  background-color: #ffffff !important;
  border-bottom: 1px solid #e0e0e0;

  .navbar-container {
    width: 92% !important; // Match body-area width
    margin: 0 auto !important; // Center align like body-area

    .p-menubar {
      border: none !important;
      border-radius: 0 !important;
      background-color: transparent !important;
      padding: 0.75rem 1rem !important;
      display: flex !important;
      align-items: center !important;

      .p-menubar-start {
        display: flex !important;
        align-items: center !important;
        margin-right: 0 !important;
      }

      .p-menubar-root-list {
        margin-left: 2rem !important; // Add space between brand and menu items
        display: flex !important;
        align-items: center !important;
      }

      .p-menubar-end {
        margin-left: auto !important;
        display: flex !important;
        align-items: center !important;

        // Style the username text
        span {
          display: flex !important;
          align-items: center !important;
          font-weight: 500 !important;
          color: #1f1f1f !important;
          margin-right: 0.5rem !important;
          height: 100% !important;
        }

        // Specifically target the username span with p-2 class
        .p-2 {
          padding: 0.5rem !important;
          display: flex !important;
          align-items: center !important;
          height: auto !important;
          line-height: 1 !important;
        }

       
      }

      // Style menu items to align properly
      .p-menuitem {
        .p-menuitem-link {
          padding: 0.5rem 1rem !important;
          color: #1f1f1f !important;
          font-weight: 500 !important;

          &:hover {
            background-color: #f5f5f5 !important;
            color: #ED1A3B !important;
          }
        }
      }
    }
  }
}
