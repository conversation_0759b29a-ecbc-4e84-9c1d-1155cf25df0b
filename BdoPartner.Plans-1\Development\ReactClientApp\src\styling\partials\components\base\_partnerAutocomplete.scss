// Global styles for partner suggestion items
.partner-suggestion-item {
  padding: 14px 16px !important;
  border-bottom: 1px solid #e9ecef !important;
  cursor: pointer !important;
  transition: all 0.25s ease !important;
  background-color: #ffffff !important;
  position: relative !important;
  border-left: 3px solid transparent !important;
  display: block !important;
  width: 100% !important;

  &:last-child {
    border-bottom: none !important;
  }

  &:first-child {
    border-top-left-radius: 6px !important;
    border-top-right-radius: 6px !important;
  }

  &:last-child {
    border-bottom-left-radius: 6px !important;
    border-bottom-right-radius: 6px !important;
  }

  // Add a subtle gradient overlay
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, rgba(0, 123, 255, 0.02) 0%, transparent 100%);
    pointer-events: none;
    border-radius: inherit;
    opacity: 0;
    transition: opacity 0.25s ease;
  }
}

.partner-autocomplete {
  .partner-suggestion-item {
    // Inherit global styles but allow local overrides
  }

// Partner name styling
.partner-name {
  margin-bottom: 6px !important;
  font-weight: 600 !important;
  color: #2c3e50 !important;
  font-size: 0.95rem !important;
  line-height: 1.2 !important;
  display: flex !important;
  align-items: center !important;

  &::after {
    content: '' !important;
    flex: 1 !important;
    height: 1px !important;
    background: linear-gradient(to right, rgba(0, 123, 255, 0.2), transparent) !important;
    margin-left: 12px !important;
  }
}

// Partner details styling
.partner-details {
  font-size: 0.8rem !important;
  color: #6c757d !important;
  margin-bottom: 4px !important;
  line-height: 1.3 !important;
  display: flex !important;
  flex-wrap: wrap !important;
  gap: 8px !important;

  span {
    display: inline-flex !important;
    align-items: center !important;
    padding: 2px 8px !important;
    background-color: #f8f9fa !important;
    border-radius: 12px !important;
    font-size: 0.75rem !important;
    font-weight: 500 !important;
    border: 1px solid #e9ecef !important;

    &:first-child {
      background-color: #e3f2fd !important;
      color: #1976d2 !important;
      border-color: #bbdefb !important;
    }
  }
}

// Partner email styling
.partner-email {
  font-size: 0.75rem !important;
  color: #868e96 !important;
  font-style: italic !important;
  margin-top: 4px !important;
  display: flex !important;
  align-items: center !important;

  &::before {
    content: '✉' !important;
    margin-right: 6px !important;
    color: #007bff !important;
    font-style: normal !important;
  }
}
}

// Target the PrimeReact autocomplete panel directly
.p-autocomplete-panel.partner-autocomplete-panel {
  max-height: 320px !important;
  overflow-y: auto !important;
  border: 1px solid #dee2e6 !important;
  border-radius: 6px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  background-color: #ffffff !important;
  margin-top: 2px !important;
  padding: 0 !important;

  .p-autocomplete-items {
    padding: 0 !important;
  }

  .p-autocomplete-item {
    padding: 0 !important;
    border: none !important;
    margin: 0 !important;
    background-color: transparent !important;

    // Alternating background colors for better visual distinction
    &:nth-child(even) .partner-suggestion-item {
      background-color: #f1f3f4 !important;
      border-left-color: #e8eaed !important;
    }

    &:nth-child(odd) .partner-suggestion-item {
      background-color: #ffffff !important;
      border-left-color: transparent !important;
    }

    // Add distinct colors for every 3rd item to create more variety
    &:nth-child(3n) .partner-suggestion-item {
      background-color: #e8f5e8 !important;
      border-left-color: #c8e6c9 !important;
    }

    &:nth-child(4n) .partner-suggestion-item {
      background-color: #fff3e0 !important;
      border-left-color: #ffcc02 !important;
    }

    &:hover {
      background-color: transparent !important;

      .partner-suggestion-item {
        background-color: #e3f2fd !important;
        border-left-color: #007bff !important;
        transform: translateX(2px) !important;
        box-shadow: 0 4px 12px rgba(0, 123, 255, 0.2) !important;
      }
    }

    &.p-highlight {
      background-color: transparent !important;

      .partner-suggestion-item {
        background-color: #007bff !important;
        border-left-color: #0056b3 !important;
        transform: translateX(2px) !important;
        box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3) !important;

        &::before {
          background: linear-gradient(90deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%) !important;
        }

        .partner-name {
          color: white !important;

          &::after {
            background: linear-gradient(to right, rgba(255, 255, 255, 0.3), transparent) !important;
          }
        }

        .partner-details {
          span {
            background-color: rgba(255, 255, 255, 0.2) !important;
            color: white !important;
            border-color: rgba(255, 255, 255, 0.3) !important;

            &:first-child {
              background-color: rgba(255, 255, 255, 0.25) !important;
              color: white !important;
              border-color: rgba(255, 255, 255, 0.4) !important;
            }
          }
        }

        .partner-email {
          color: rgba(255, 255, 255, 0.9) !important;

          &::before {
            color: rgba(255, 255, 255, 0.8) !important;
          }
        }
      }
    }
  }

  // Custom scrollbar styling
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }
}

// Override PrimeReact AutoComplete styles
.p-autocomplete {
  .p-autocomplete-input {
    width: 100%;
    border: 1px solid #ced4da;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 0.875rem;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;

    &:focus {
      border-color: #007bff;
      box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
      outline: none;
    }

    &::placeholder {
      color: #6c757d;
      font-style: italic;
    }
  }

  .p-autocomplete-panel {
    border: none !important;
    box-shadow: none !important;
    background: transparent !important;

    .p-autocomplete-items {
      padding: 0;
      border: none;
    }

    .p-autocomplete-item {
      padding: 0;
      border: none;
      margin: 0;
    }
  }

  // Loading state
  &.p-autocomplete-loading {
    .p-autocomplete-input {
      background-image: url("data:image/svg+xml,%3csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M10 3.5a6.5 6.5 0 1 0 6.5 6.5h-2a4.5 4.5 0 1 1-4.5-4.5V3.5z' fill='%23007bff'/%3e%3c/svg%3e");
      background-repeat: no-repeat;
      background-position: right 12px center;
      background-size: 16px;
      animation: spin 1s linear infinite;
    }
  }
}

// Loading animation
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// Empty message styling
.p-autocomplete-empty-message {
  padding: 16px;
  text-align: center;
  color: #6c757d;
  font-style: italic;
  font-size: 0.875rem;
  background-color: #f8f9fa;
  border-radius: 4px;
  margin: 8px;
}

// Additional high-specificity styles to ensure they are applied
.p-autocomplete-panel .p-autocomplete-item .partner-suggestion-item {
  padding: 14px 16px !important;
  border-bottom: 1px solid #e9ecef !important;
  cursor: pointer !important;
  transition: all 0.25s ease !important;
  position: relative !important;
  border-left: 3px solid transparent !important;
  display: block !important;
  width: 100% !important;
  box-sizing: border-box !important;
}

// Force alternating colors with high specificity
.p-autocomplete-panel .p-autocomplete-item:nth-child(even) .partner-suggestion-item {
  background-color: #f1f3f4 !important;
}

.p-autocomplete-panel .p-autocomplete-item:nth-child(odd) .partner-suggestion-item {
  background-color: #ffffff !important;
}

.p-autocomplete-panel .p-autocomplete-item:nth-child(3n) .partner-suggestion-item {
  background-color: #e8f5e8 !important;
}

.p-autocomplete-panel .p-autocomplete-item:nth-child(4n) .partner-suggestion-item {
  background-color: #fff3e0 !important;
}

// Hover states with high specificity
.p-autocomplete-panel .p-autocomplete-item:hover .partner-suggestion-item {
  background-color: #e3f2fd !important;
  border-left-color: #007bff !important;
  transform: translateX(2px) !important;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.2) !important;
}
