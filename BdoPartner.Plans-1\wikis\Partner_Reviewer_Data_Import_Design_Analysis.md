# Partner Reviewer Data Import System - Design Analysis

## Overview

The Partner Reviewer data import system is designed to handle Excel/CSV file uploads containing partner reviewer assignments. The system follows a staged approach with validation and final submission to the production table.

## System Architecture

### Core Components

1. **PartnerReviewerUploadService** - Main business logic service
2. **PartnerReviewerUploadController** - Web API controller
3. **Data Models** - Entity and DTO models for staging and final data
4. **Validation Engine** - Comprehensive data validation logic

### Data Flow

```
File Upload → Staging Tables → Validation → Final Table
     ↓              ↓             ↓           ↓
  File Parse → Upload Details → Error Check → PartnerReviewer
```

## Database Schema

### Staging Tables

#### PartnerReviewerUpload (Master)
- **Id**: INT (Primary Key)
- **Years**: NVARCHAR(100) - Comma-separated years (e.g., "2023,2024")
- **UploadFileName**: NVARCHAR(500) - Original filename
- **ValidationSummary**: NVARCHAR(MAX) - Validation results summary
- **FileContent**: VARBINARY(MAX) - Binary file content
- **Status**: TINYINT - Upload status (0-5)
- **Audit Fields**: CreatedBy, CreatedOn, ModifiedBy, ModifiedOn

#### PartnerReviewerUploadDetails (Detail)
- **Id**: BIGINT (Primary Key)
- **PartnerReviewerUploadId**: INT (Foreign Key)
- **RowId**: INT - Row number in uploaded file
- **EmployeeId**: NVARCHAR(50) - Partner's employee ID
- **EmployeeName**: NVARCHAR(100) - Partner's name
- **Exempt**: NVARCHAR(10) - Y/N exemption status
- **LeadershipRole**: NVARCHAR(100) - Leadership role codes
- **PrimaryReviewerId**: NVARCHAR(50) - Primary reviewer's employee ID
- **PrimaryReviewerName**: NVARCHAR(100) - Primary reviewer's name
- **SecondaryReviewerId**: NVARCHAR(50) - Secondary reviewer's employee ID
- **SecondaryReviewerName**: NVARCHAR(100) - Secondary reviewer's name
- **ValidationError**: NVARCHAR(500) - Validation error messages

### Final Table

#### PartnerReviewer (Production)
- **Id**: UNIQUEIDENTIFIER (Primary Key)
- **Year**: SMALLINT - Single year value
- **PartnerId**: UNIQUEIDENTIFIER - Partner's GUID
- **Exempt**: BIT - Boolean exemption status
- **LeadershipRole**: NVARCHAR - Leadership role
- **PrimaryReviewerId**: UNIQUEIDENTIFIER - Primary reviewer's GUID
- **PrimaryReviewerName**: NVARCHAR - Primary reviewer's name
- **SecondaryReviewerId**: UNIQUEIDENTIFIER - Secondary reviewer's GUID
- **SecondaryReviewerName**: NVARCHAR - Secondary reviewer's name
- **Audit Fields**: CreatedBy, CreatedOn, ModifiedBy, ModifiedOn

## Status Flow

### Upload Status Enumeration
```
0 - Uploading: File upload in progress
1 - Uploaded: File successfully uploaded and parsed
2 - Validating: Data validation in progress
3 - ValidationPassed: All validation rules passed
4 - ValidationFailed: Validation errors found
5 - Submitted: Data successfully transferred to final table
```

## File Processing Logic

### Supported File Formats
- **Excel (.xlsx)**: Uses EPPlus library for parsing
- **CSV (.csv)**: Custom CSV parser with quote handling

### Required Columns
1. **EmployeeID** - Partner's employee identifier
2. **EmployeeName** - Partner's full name
3. **Exempt** - Y/N exemption flag
4. **LeadershipRole** - Leadership role codes
5. **PrimaryReviewerID** - Primary reviewer's employee ID
6. **PrimaryReviewerName** - Primary reviewer's name
7. **SecondaryReviewerID** - Secondary reviewer's employee ID
8. **SecondaryReviewerName** - Secondary reviewer's name

### File Processing Steps
1. **File Validation**: Check file format and size
2. **Header Extraction**: Parse column headers from first row
3. **Column Mapping**: Map headers to required fields
4. **Data Extraction**: Parse each data row into staging records
5. **Automatic Validation**: Trigger validation after parsing

## Validation Logic

### Core Validation Rules

#### Employee Validation
- **Employee ID Required**: Must not be empty
- **Employee ID Exists**: Must exist in Partner table
- **Name Matching**: Employee name must match partner record

#### Exemption Validation
- **Exempt Field**: Must be "Y" or "N"
- **Reviewer Requirements**: Non-exempt employees must have both reviewers

#### Reviewer Validation (Non-Exempt Only)
- **Primary Reviewer Required**: Must have valid primary reviewer ID
- **Secondary Reviewer Required**: Must have valid secondary reviewer ID
- **Reviewer ID Exists**: Both reviewer IDs must exist in Partner table
- **Name Matching**: Reviewer names must match partner records
- **Circular Dependency**: Employee cannot be their own reviewer

### Name Matching Algorithm

The system uses sophisticated name matching logic:

#### Supported Name Patterns
1. **Display Name**: Direct match with partner's DisplayName
2. **First Last**: "FirstName LastName" format
3. **Last First**: "LastName FirstName" format (without comma)

#### Name Cleaning Process
- Remove special characters: `,`, `.`, `/`, `-`, `_`, `#`, `@`, etc.
- Normalize whitespace: Replace multiple spaces with single space
- Case-insensitive comparison

### Error Handling
- **Multiple Errors**: Concatenated with semicolon separator
- **Existing Errors**: Preserved from file parsing (e.g., truncation)
- **Detailed Messages**: Include specific field values and context

## Submit Logic

### Data Transfer Process
1. **Validation Check**: Only ValidationPassed uploads can be submitted
2. **Valid Records Only**: Transfer only records without validation errors
3. **Year Expansion**: Split comma-separated years into individual records
4. **Partner ID Resolution**: Convert employee IDs to partner GUIDs

### Overwrite Behavior (Default: True)
When `overwriteExisting = true`:
1. **Update Existing**: Modify records that match staging data
2. **Create New**: Add records for new partners in staging
3. **Delete Orphaned**: Remove existing records not in staging data

### Data Transformation
- **Employee ID → Partner GUID**: Lookup partner by employee ID
- **String → Boolean**: Convert "Y"/"N" to true/false for Exempt field
- **Year Splitting**: Create separate records for each year
- **Reviewer Resolution**: Convert reviewer employee IDs to GUIDs

## Error Scenarios

### File Processing Errors
- Invalid file format
- Missing required columns
- File parsing failures
- Empty or corrupted files

### Validation Errors
- Invalid employee IDs
- Name mismatches
- Missing reviewers for non-exempt employees
- Circular reviewer dependencies
- Invalid exemption values

### Submit Errors
- Upload not in ValidationPassed status
- No valid records to submit
- Database constraint violations
- Partner lookup failures

## Performance Considerations

### Batch Processing
- Process files in chunks for large uploads
- Bulk database operations for efficiency
- Memory management for large files

### Caching
- Partner dictionary lookup for validation
- Minimize database queries during processing

### Error Recovery
- Preserve file content for reprocessing
- Detailed error logging for troubleshooting
- Graceful handling of partial failures

## Security & Authorization

### Access Control
- **PPAdministrator Role**: Required for all upload operations
- **User Context**: Audit trail with current user information
- **File Content Storage**: Secure binary storage in database

### Data Validation
- Input sanitization for all text fields
- SQL injection prevention through parameterized queries
- File type validation and size limits

## Integration Points

### Dependencies
- **Partner Table**: Source of truth for employee/reviewer data
- **User Table**: For audit trail and user name resolution
- **AutoMapper**: Entity-DTO mapping
- **EPPlus**: Excel file processing
- **Entity Framework**: Data access layer

### API Endpoints
- `POST /api/partnerreviewerupload/uploadfile` - File upload
- `POST /api/partnerreviewerupload/validate` - Manual validation trigger
- `POST /api/partnerreviewerupload/submitupload` - Submit to final table
- `GET /api/partnerreviewerupload/uploads` - List uploads
- `GET /api/partnerreviewerupload/details` - Get upload details

## Detailed Validation Rules & Evaluation Logic

### Employee Data Validation

#### Employee ID Validation
```csharp
// Rule: Employee ID is required
if (string.IsNullOrEmpty(detail.EmployeeId))
    errors.Add("Employee ID is required");

// Rule: Employee ID must exist in Partner table
else if (!partnerDict.ContainsKey(detail.EmployeeId))
    errors.Add($"Invalid Employee ID {detail.EmployeeId}");
```

#### Employee Name Validation
```csharp
// Rule: Employee name must match partner record (if provided)
if (!string.IsNullOrEmpty(detail.EmployeeName) &&
    !IsNameMatch(detail.EmployeeName, partner.FirstName, partner.LastName, partner.DisplayName))
{
    errors.Add($"Employee name '{detail.EmployeeName}' does not match partner record for Employee ID {detail.EmployeeId}");
}
```

### Exemption Status Validation

#### Exempt Field Validation
```csharp
// Rule: Exempt field must be Y or N
if (string.IsNullOrEmpty(detail.Exempt) || (detail.Exempt != "Y" && detail.Exempt != "N"))
    errors.Add("Exempt field must be Y or N");
```

### Reviewer Validation (Non-Exempt Partners Only)

#### Circular Dependency Prevention
```csharp
// Rule: Employee cannot be their own reviewer
if (detail.EmployeeId == detail.PrimaryReviewerId)
    errors.Add($"Reviewer cannot be same as Employee for ID {detail.EmployeeId}");

if (detail.EmployeeId == detail.SecondaryReviewerId)
    errors.Add($"Reviewer cannot be same as Employee for ID {detail.EmployeeId}");
```

#### Primary Reviewer Validation
```csharp
// Rule: Primary reviewer is required for non-exempt employees
if (string.IsNullOrEmpty(detail.PrimaryReviewerId))
    errors.Add($"Primary Reviewer is required for non-exempt employee {detail.EmployeeId}");

// Rule: Primary reviewer ID must exist in Partner table
else if (!partnerDict.ContainsKey(detail.PrimaryReviewerId))
    errors.Add($"Invalid Primary Reviewer ID {detail.PrimaryReviewerId} for employee {detail.EmployeeId}");

// Rule: Primary reviewer name must match partner record (if provided)
else if (!string.IsNullOrEmpty(detail.PrimaryReviewerName) &&
         !IsNameMatch(detail.PrimaryReviewerName, primaryReviewer.FirstName, primaryReviewer.LastName, primaryReviewer.DisplayName))
{
    errors.Add($"Primary Reviewer name '{detail.PrimaryReviewerName}' does not match partner record for Primary Reviewer ID {detail.PrimaryReviewerId}");
}
```

#### Secondary Reviewer Validation
```csharp
// Rule: Secondary reviewer is required for non-exempt employees
if (string.IsNullOrEmpty(detail.SecondaryReviewerId))
    errors.Add($"Secondary Reviewer is required for non-exempt employee {detail.EmployeeId}");

// Rule: Secondary reviewer ID must exist in Partner table
else if (!partnerDict.ContainsKey(detail.SecondaryReviewerId))
    errors.Add($"Invalid Secondary Reviewer ID {detail.SecondaryReviewerId} for employee {detail.EmployeeId}");

// Rule: Secondary reviewer name must match partner record (if provided)
else if (!string.IsNullOrEmpty(detail.SecondaryReviewerName) &&
         !IsNameMatch(detail.SecondaryReviewerName, secondaryReviewer.FirstName, secondaryReviewer.LastName, secondaryReviewer.DisplayName))
{
    errors.Add($"Secondary Reviewer name '{detail.SecondaryReviewerName}' does not match partner record for Secondary Reviewer ID {detail.SecondaryReviewerId}");
}
```

### Name Matching Algorithm Details

#### Name Cleaning Function
```csharp
private string CleanNameForComparison(string name)
{
    // Remove special characters: , . / - _ # @ ( ) [ ] { } | \ : ; " ' ! ? * & % $ + = < > ~ `
    var specialChars = new char[] { ',', '.', '/', '-', '_', '#', '@', '(', ')', '[', ']',
                                   '{', '}', '|', '\\', ':', ';', '"', '\'', '!', '?', '*',
                                   '&', '%', '$', '+', '=', '<', '>', '~', '`' };

    foreach (var specialChar in specialChars)
        cleaned = cleaned.Replace(specialChar, ' ');

    // Normalize whitespace: trim and replace multiple spaces with single space
    return Regex.Replace(cleaned.Trim(), @"\s+", " ");
}
```

#### Name Matching Patterns
```csharp
private bool IsNameMatch(string providedName, string firstName, string lastName, string displayName)
{
    var cleanProvidedName = CleanNameForComparison(providedName);

    // Pattern 1: Match against DisplayName (e.g., "Smith, John")
    if (!string.IsNullOrWhiteSpace(displayName))
    {
        var cleanDisplayName = CleanNameForComparison(displayName);
        if (string.Equals(cleanProvidedName, cleanDisplayName, StringComparison.OrdinalIgnoreCase))
            return true;
    }

    // Pattern 2: Match "FirstName LastName"
    if (!string.IsNullOrWhiteSpace(firstName) && !string.IsNullOrWhiteSpace(lastName))
    {
        var firstLastPattern = $"{CleanNameForComparison(firstName)} {CleanNameForComparison(lastName)}";
        if (string.Equals(cleanProvidedName, firstLastPattern, StringComparison.OrdinalIgnoreCase))
            return true;

        // Pattern 3: Match "LastName FirstName" (without comma)
        var lastFirstPattern = $"{CleanNameForComparison(lastName)} {CleanNameForComparison(firstName)}";
        if (string.Equals(cleanProvidedName, lastFirstPattern, StringComparison.OrdinalIgnoreCase))
            return true;
    }

    return false;
}
```

### Validation Summary Generation

#### Status Determination Logic
```csharp
// Determine final upload status based on validation results
upload.Status = validRecords > 0 && invalidRecords == 0
    ? (byte)Enumerations.PartnerReviewerUploadStatus.ValidationPassed
    : (byte)Enumerations.PartnerReviewerUploadStatus.ValidationFailed;

// Generate validation summary
upload.ValidationSummary = $"Total: {details.Count}, Valid: {validRecords}, Invalid: {invalidRecords}";
```

#### Error Aggregation
```csharp
// Merge existing validation errors with new validation errors
var allErrors = new List<string>();
if (!string.IsNullOrEmpty(detail.ValidationError))
    allErrors.Add(detail.ValidationError);
allErrors.AddRange(errors);

detail.ValidationError = allErrors.Any() ? string.Join("; ", allErrors) : null;
```

## Data Transformation Logic

### Year Processing
```csharp
// Split comma-separated years into individual integers
var yearsList = upload.Years.Split(',')
    .Select(y => y.Trim())
    .Where(y => short.TryParse(y, out _))
    .Select(y => short.Parse(y))
    .ToList();
```

### Partner ID Resolution
```csharp
// Build partner dictionary for efficient lookup
var partnerDict = UOW.Partners.GetAll()
    .Where(p => !string.IsNullOrEmpty(p.EmployeeId))
    .ToDictionary(p => p.EmployeeId.ToString(), p => p);
```

### Data Mapping (Staging to Final)
```csharp
// Transform staging record to final PartnerReviewer entity
var partnerReviewer = new Entity.PartnerReviewer
{
    Id = Guid.NewGuid(),
    Year = year,
    PartnerId = partner.Id,
    Exempt = detail.Exempt == "Y",
    LeadershipRole = detail.LeadershipRole,
    PrimaryReviewerId = primaryReviewer?.Id,
    PrimaryReviewerName = detail.PrimaryReviewerName,
    SecondaryReviewerId = secondaryReviewer?.Id,
    SecondaryReviewerName = detail.SecondaryReviewerName,
    CreatedBy = CurrentUser?.Id,
    CreatedOn = CurrentDateTime
};
```

## Future Enhancements

### Potential Improvements
1. **Async Processing**: Background job processing for large files
2. **Progress Tracking**: Real-time upload/validation progress
3. **Template Generation**: Dynamic Excel template creation
4. **Bulk Operations**: Enhanced bulk update capabilities
5. **Audit Trail**: Detailed change tracking
6. **Data Export**: Export functionality for processed data
7. **Advanced Validation**: Cross-reference validation rules
8. **Notification System**: Email alerts for validation results
9. **Data Versioning**: Track changes over time
10. **Performance Optimization**: Caching and indexing improvements
