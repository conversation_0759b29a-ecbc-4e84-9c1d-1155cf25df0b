﻿/*
Deployment script for BdoPartner.Plans.Database

This code was generated by a tool.
Changes to this file may cause incorrect behavior and will be lost if
the code is regenerated.
*/

GO
SET ANSI_NULLS, ANSI_PADDING, ANSI_WARNINGS, ARITHABORT, CONCAT_NULL_YIELDS_NULL, QUOTED_IDENTIFIER ON;

SET NUMERIC_ROUNDABORT OFF;


GO
:setvar DatabaseName "BdoPartner.Plans.Database"
:setvar Default<PERSON>ile<PERSON>refix "BdoPartner.Plans.Database"
:setvar DefaultDataPath "C:\Program Files\Microsoft SQL Server\MSSQL16.MSSQLSERVER\MSSQL\DATA\"
:setvar DefaultLogPath "C:\Program Files\Microsoft SQL Server\MSSQL16.MSSQLSERVER\MSSQL\DATA\"

GO
:on error exit
GO
/*
Detect SQLCMD mode and disable script execution if SQLCMD mode is not supported.
To re-enable the script after enabling SQLCMD mode, execute the following:
SET NOEXEC OFF; 
*/
:setvar __IsSqlCmdEnabled "True"
GO
IF N'$(__IsSqlCmdEnabled)' NOT LIKE N'True'
    BEGIN
        PRINT N'SQLCMD mode must be enabled to successfully execute this script.';
        SET NOEXEC ON;
    END


GO
USE [$(DatabaseName)];


GO
-- Declare variables for form metadata
DECLARE @formId  NVARCHAR(255) = 'bdf37b4f-cc42-431a-a587-ac2ced9efc0b';
DECLARE @FormName NVARCHAR(255) = N'Partner Planning Form 2026';
DECLARE @form2025_version INT = 5;
DECLARE @form2025_ack NVARCHAR(MAX) = N'Acknowledgment text for Partner Planning Form 2025';
DECLARE @form2025_generalComments NVARCHAR(MAX) = N'General comments for Partner Planning Form 2025';
-- Combine pages into the final JSON, matching the specified root structure
declare @FinalJson nvarchar(MAX) =
N'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'


-- Check for existing form version
DECLARE @currentform_version INT = NULL;
SELECT @currentform_version = [FormSystemVersion] 
FROM [dbo].[Questionnaire] 
WHERE [Id] = @formId;

PRINT @currentform_version;
PRINT @form2025_version;

-- Insert or update the form in the Questionnaire table
IF @currentform_version IS NULL
BEGIN 
    INSERT INTO [dbo].[Questionnaire]
    (
      [Id], [Name], [Year], [Status], [IsActive], [Acknowledgment], [AcknowledgmentText], [GeneralComments], [GeneralCommentsText],
      [CreatedOn], [ModifiedOn], [DefinitionJson], [DraftDefinitionJson], [FormSystemVersion]
    )
    VALUES
    (
      @formId, @FormName, 2025, 1, 1, 1, @form2025_ack, 1, @form2025_generalComments,
      GETUTCDATE(), GETUTCDATE(), @FinalJson, @FinalJson, @form2025_version
    );
END
ELSE IF (@currentform_version < @form2025_version) 
BEGIN
    UPDATE [dbo].[Questionnaire]
    SET
      [DefinitionJson] = @FinalJson,
      [DraftDefinitionJson] = @FinalJson,
      [Name] = @FormName,
      [Status] = 1,
      [IsActive] = 1,
      [Acknowledgment] = 1,
      [AcknowledgmentText] = @form2025_ack,
      [GeneralComments] = 1,
      [GeneralCommentsText] = @form2025_generalComments,
      [FormSystemVersion] = @form2025_version,
      [ModifiedOn] = GETUTCDATE()
    WHERE
      [Id] = @formId;
END;

-- Verify the insertion/update
SELECT [Id], [Name], [Year], [FormSystemVersion], [CreatedOn], [ModifiedOn]
FROM [dbo].[Questionnaire]
WHERE [Id] = @formId;
GO

GO
PRINT N'Update complete.';


GO
