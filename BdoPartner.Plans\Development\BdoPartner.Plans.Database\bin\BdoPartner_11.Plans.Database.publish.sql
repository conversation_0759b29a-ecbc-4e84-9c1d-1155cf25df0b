﻿/*
Deployment script for Partner<PERSON>lans-DEV

This code was generated by a tool.
Changes to this file may cause incorrect behavior and will be lost if
the code is regenerated.
*/

GO
SET ANSI_NULLS, ANSI_PADDING, ANSI_WARNINGS, ARITHABORT, CONCAT_NULL_YIELDS_NULL, QUOTED_IDENTIFIER ON;

SET NUMERIC_ROUNDABORT OFF;


GO
:setvar DatabaseName "PartnerPlans-DEV"
:setvar DefaultFilePrefix "PartnerPlans-DEV"
:setvar DefaultDataPath ""
:setvar DefaultLogPath ""

GO
:on error exit
GO
/*
Detect SQLCMD mode and disable script execution if SQLCMD mode is not supported.
To re-enable the script after enabling SQLCMD mode, execute the following:
SET NOEXEC OFF; 
*/
:setvar __IsSqlCmdEnabled "True"
GO
IF N'$(__IsSqlCmdEnabled)' NOT LIKE N'True'
    BEGIN
        PRINT N'SQLCMD mode must be enabled to successfully execute this script.';
        SET NOEXEC ON;
    END


GO
IF EXISTS (SELECT 1
           FROM   [sys].[databases]
           WHERE  [name] = N'$(DatabaseName)')
    BEGIN
        ALTER DATABASE [$(DatabaseName)]
            SET QUERY_STORE = OFF 
            WITH ROLLBACK IMMEDIATE;
    END


GO
IF EXISTS (SELECT 1
           FROM   [sys].[databases]
           WHERE  [name] = N'$(DatabaseName)')
    BEGIN
        ALTER DATABASE [$(DatabaseName)]
            SET TEMPORAL_HISTORY_RETENTION OFF 
            WITH ROLLBACK IMMEDIATE;
    END


GO
MERGE INTO [dbo].[Language] AS Target
USING (VALUES
  (1,'en','English')
 ,(2,'fr','French')
) AS Source ([Id],[Code],[Name])
ON (Target.[Id] = Source.[Id])
WHEN MATCHED AND (Target.[Code] <> Source.[Code] OR Target.[Name] <> Source.[Name]) THEN
	UPDATE SET
		[Code] = Source.[Code], 
		[Name] = Source.[Name]
WHEN NOT MATCHED BY TARGET THEN
	INSERT([Id],[Code],[Name])
	VALUES(Source.[Id],Source.[Code],Source.[Name])
WHEN NOT MATCHED BY SOURCE THEN 
	DELETE;
GO

-- Delete RolePermission records for roles not in the source dataset
DELETE [dbo].[RolePermission]
WHERE [RoleId] NOT IN (3, 15, 16, 17);

-- Delete UserRole records for roles not in the source dataset
DELETE [dbo].[UserRole]
WHERE [RoleId] NOT IN (3, 15, 16, 17);

MERGE INTO [dbo].[Role] AS Target
USING (VALUES
  (15, 'Partner Plans Administrator')
 ,(3, 'Active Partner')
 ,(16, 'New Partner')
 ,(17, 'Partner Plans Executive Leadership')
) AS Source ([Id],[Name])
ON (Target.[Id] = Source.[Id])
WHEN MATCHED AND (Target.[Name] <> Source.[Name]) THEN
	UPDATE SET
		[Name] = Source.[Name]
WHEN NOT MATCHED BY TARGET THEN
	INSERT([Id],[Name])
	VALUES(Source.[Id],Source.[Name])
WHEN NOT MATCHED BY SOURCE THEN
	DELETE;
GO


MERGE INTO [dbo].[Permission] AS Target
USING (VALUES
  (6, 'Partner Plans Login')
 ,(7, 'Track Own Partner Plan')
 ,(8, 'Track All Partner Plans')
 ,(9, 'Draft Submit Partner Plan')
 ,(10, 'Edit Partner Plans Under Review')
 ,(11, 'Partner Plans Final Submission')
 ,(12, 'Mid End Year Self Assessment')
 ,(13, 'Mid End Year Reviewer Assessment')
 ,(14, 'View Submitted Partner Plans')
 ,(15, 'Edit Submitted Partner Plans')
 ,(16, 'Export Plan Data To Excel')
 ,(17, 'Manage Partner Reviewer Relationships')
 ,(18, 'Upload KPI Data')
 ,(19, 'Edit Publish Input Form')
) AS Source ([Id],[Name])
ON (Target.[Id] = Source.[Id])
WHEN MATCHED AND (Target.[Name] <> Source.[Name]) THEN
	UPDATE SET
		[Name] = Source.[Name]
WHEN NOT MATCHED BY TARGET THEN
	INSERT([Id],[Name])
	VALUES(Source.[Id],Source.[Name])
WHEN NOT MATCHED BY SOURCE THEN
	DELETE;
GO

MERGE INTO [dbo].[RolePermission] AS Target
USING (VALUES
   -- PPAdministrator (Role ID 15) - Full administrative permissions
   (15, 6)   -- Login
  ,(15, 7)   -- Track Own Partner Plan
  ,(15, 8)   -- Track All Partner Plans
  ,(15, 9)   -- Draft Submit Partner Plan
  ,(15, 10)   -- Edit Partner Plans Under Review
  ,(15, 11)   -- Partner Plans Final Submission
  ,(15, 12)   -- Mid End Year Self Assessment
  ,(15, 13)   -- Mid End Year Reviewer Assessment
  ,(15, 14)   -- View Submitted Partner Plans
  ,(15, 15)  -- Edit Submitted Partner Plans
  ,(15, 16)  -- Export Plan Data To Excel
  ,(15, 17)  -- Manage Partner Reviewer Relationships
  ,(15, 18)  -- Upload KPI Data
  ,(15, 19)  -- Edit Publish Input Form
   -- Partner (Role ID 3) - Standard Active partner permissions
  ,(3, 6)   -- Login
  ,(3, 7)   -- Track Own Partner Plan
  ,(3, 9)   -- Draft Submit Partner Plan
  ,(3, 10)   -- Edit Partner Plans Under Review (own)
  ,(3, 12)   -- Mid End Year Self Assessment (own)
  ,(3, 14)   -- View Submitted Partner Plans (own)
   -- NewPartner (Role ID 16) - Limited permissions for new partners
  ,(16, 6)   -- Login
  ,(16, 7)   -- Track Own Partner Plan
  ,(16, 9)   -- Draft Submit Partner Plan
  ,(16, 12)   -- Mid End Year Self Assessment (own)
  ,(16, 14)   -- View Submitted Partner Plans (own)
   -- PPExecutiveLeadership (Role ID 17) - Executive level permissions
  ,(17, 6)   -- Login
  ,(17, 7)   -- Track Own Partner Plan
  ,(17, 8)   -- Track All Partner Plans
  ,(17, 10)   -- Edit Partner Plans Under Review
  ,(17, 11)   -- Partner Plans Final Submission
  ,(17, 13)   -- Mid End Year Reviewer Assessment
  ,(17, 14)   -- View Submitted Partner Plans
  ,(17, 15)  -- Edit Submitted Partner Plans
  ,(17, 16)  -- Export Plan Data To Excel
) AS Source ([RoleId],[PermissionId])
ON (Target.[RoleId] = Source.[RoleId] and Target.[PermissionId] = Source.[PermissionId])
--WHEN MATCHED AND (Target.[Name] <> Source.[Name]) THEN
--	UPDATE SET
--		[Name] = Source.[Name]
WHEN NOT MATCHED BY TARGET THEN
	INSERT([RoleId],[PermissionId])
	VALUES(Source.[RoleId],Source.[PermissionId])
WHEN NOT MATCHED BY SOURCE THEN
	DELETE;
GO

---- SET IDENTITY_INSERT [dbo].[Timezone] ON
 
--MERGE INTO [dbo].[Timezone] AS Target
--USING (VALUES
--  (1,'-12:00','Dateline Standard Time',-12.00)
-- ,(2,'-11:00','UTC-11',-11.00)
-- ,(3,'-10:00','Aleutian Standard Time',-10.00)
-- ,(4,'-09:30','Marquesas Standard Time',-9.50)
-- ,(5,'-09:00','UTC-09',-9.00)
-- ,(6,'-08:00','Pacific Standard Time',-8.00)
-- ,(7,'-07:00','Mountain Standard Time',-7.00)
-- ,(8,'-06:00','Canada Central Standard Time',-6.00)
-- ,(9,'-05:00','Eastern Standard Time',-5.00)
-- ,(10,'-04:00','Atlantic Standard Time',-4.00)
-- ,(11,'-03:30','Newfoundland Standard Time',-3.50)
-- ,(12,'-03:00','Tocantins Standard Time',-3.00)
-- ,(13,'-02:00','UTC-02',-2.00)
-- ,(14,'-01:00','Azores Standard Time',-1.00)
-- ,(15,'+00:00','GMT Standard Time',0.00)
-- ,(16,'+01:00','Morocco Standard Time',1.00)
-- ,(17,'+02:00','Libya Standard Time',2.00)
-- ,(18,'+03:00','Turkey Standard Time',3.00)
-- ,(19,'+03:30','Iran Standard Time',3.50)
-- ,(20,'+04:00','Astrakhan Standard Time',4.00)
-- ,(21,'+04:30','Afghanistan Standard Time',4.50)
-- ,(22,'+05:00','West Asia Standard Time',5.00)
-- ,(23,'+05:30','India Standard Time',5.50)
-- ,(24,'+05:45','Nepal Standard Time',5.75)
-- ,(25,'+06:00','Central Asia Standard Time',6.00)
-- ,(26,'+06:30','Myanmar Standard Time',6.50)
-- ,(27,'+07:00','Altai Standard Time',7.00)
-- ,(28,'+08:00','China Standard Time',8.00)
-- ,(29,'+08:45','Aus Central W. Standard Time',8.75)
-- ,(30,'+09:00','Transbaikal Standard Time',9.00)
-- ,(31,'+09:30','Cen. Australia Standard Time',9.50)
-- ,(32,'+10:00','E. Australia Standard Time',10.00)
-- ,(33,'+10:30','Lord Howe Standard Time',10.50)
-- ,(34,'+11:00','Bougainville Standard Time',11.00)
-- ,(35,'+12:00','New Zealand Standard Time',12.00)
-- ,(36,'+12:45','Chatham Islands Standard Time',12.75)
-- ,(37,'+13:00','Tonga Standard Time',13.00)
-- ,(38,'+14:00','Line Islands Standard Time',14.00)
--) AS Source ([Id],[Code],[Name],[Offset])
--ON (Target.[Id] = Source.[Id])
--WHEN MATCHED AND (Target.[Code] <> Source.[Code] OR Target.[Name] <> Source.[Name] OR Target.[Offset] <> Source.[Offset]) THEN
-- UPDATE SET
-- [Code] = Source.[Code], 
--[Name] = Source.[Name], 
--[Offset] = Source.[Offset]
--WHEN NOT MATCHED BY TARGET THEN
-- INSERT([Id],[Code],[Name],[Offset])
-- VALUES(Source.[Id],Source.[Code],Source.[Name],Source.[Offset])
--WHEN NOT MATCHED BY SOURCE THEN 
-- DELETE;

----SET IDENTITY_INSERT [dbo].[Timezone] OFF;
--GO

-- Note: AuthProvider settings corporate with startup.cs AddOpenIdConnect settings in Identity Server project.
MERGE INTO [identity].[AuthProvider] AS Target
USING (VALUES
  ('App', 'Default Database driving authentication', 1)
 ,('BDO-ITINV-AAD', 'BDO Canada LLP IT Innovation Azure AD', 1)
 ,('BDO-AAD', 'BDO Canada LLP Azure AD', 1)
 ,('FELIX-AAD', 'Felix Personal Azure AD', 1)
) AS Source ([Id],[Name], [IsActive])
ON (Target.[Id] = Source.[Id])
WHEN MATCHED AND (Target.[Name] <> Source.[Name] or Target.[IsActive]<> Source.[IsActive]) THEN
	UPDATE SET
		[Name] = Source.[Name]		
WHEN NOT MATCHED BY TARGET THEN
	INSERT([Id],[Name],[IsActive])
	VALUES(Source.[Id],Source.[Name], Source.[IsActive])
WHEN NOT MATCHED BY SOURCE THEN 
	DELETE;
GO

-- password = "Password1"
MERGE INTO [dbo].[User] AS Target
USING (VALUES
  ('BFD02B5C-2408-4870-8E1F-06C7A72187F6', 'APP', 'ppadmin',0x999000A49E8DD91ED5726EA0AF1745C8F712640D6563F7FDE1977013B730CB3C9D78F99EC07FC59B0ADEAB5CBA0380DA, 0xE73685F552,0,'PP','Administrator','<EMAIL>',1,1)
  ,('D3FB7F6A-6487-4190-9D9F-0ECD7A25E239', 'APP','partner1',0x999000A49E8DD91ED5726EA0AF1745C8F712640D6563F7FDE1977013B730CB3C9D78F99EC07FC59B0ADEAB5CBA0380DA, 0xE73685F552,0,'Partner','User','<EMAIL>',1,1)
  ,('B6BDCA35-4D96-4C26-B6B2-D15E285147DB', 'APP', 'newpartner',0x999000A49E8DD91ED5726EA0AF1745C8F712640D6563F7FDE1977013B730CB3C9D78F99EC07FC59B0ADEAB5CBA0380DA, 0xE73685F552,0,'New','Partner','<EMAIL>',1,1)
  ,('A1B2C3D4-5E6F-7890-ABCD-EF1234567890', 'APP', 'ppexec',0x999000A49E8DD91ED5726EA0AF1745C8F712640D6563F7FDE1977013B730CB3C9D78F99EC07FC59B0ADEAB5CBA0380DA, 0xE73685F552,0,'PP Executive','Leadership','<EMAIL>',1,1)

) AS Source ([Id], [AuthProviderId], [Username],[Password],[Salt],[IsTempPasswordEnabled],[FirstName],[LastName],[Email],[LanguageId],[IsActive])
ON (Target.[Id] = Source.[Id])
WHEN MATCHED AND (Target.[Username] <> Source.[Username] OR Target.[AuthProviderId]<> Source.[AuthProviderId] OR Target.[Password] <> Source.[Password] OR Target.[Salt] <> Source.[Salt] OR Target.[IsTempPasswordEnabled] <> Source.[IsTempPasswordEnabled] 
OR Target.[FirstName] <> Source.[FirstName] OR Target.[LastName] <> Source.[LastName] OR Target.[Email] <> Source.[Email] 
OR Target.[LanguageId] <> Source.[LanguageId] OR Target.[IsActive] <> Source.[IsActive] ) THEN
 UPDATE SET
 [Username] = Source.[Username], 
[Password] = Source.[Password], 
[Salt] = Source.[Salt], 
[IsTempPasswordEnabled] = Source.[IsTempPasswordEnabled], 
[FirstName] = Source.[FirstName], 
[LastName] = Source.[LastName], 
[Email] = Source.[Email], 
[LanguageId] = Source.[LanguageId], 
[IsActive] = Source.[IsActive],
[AuthProviderId] = Source.[AuthProviderId]
WHEN NOT MATCHED BY TARGET THEN
 INSERT([Id],[AuthProviderId],[Username],[Password],[Salt],[IsTempPasswordEnabled],[FirstName],[LastName],[Email],[LanguageId],[IsActive])
 VALUES(Source.[Id],Source.[AuthProviderId], Source.[Username],Source.[Password],Source.[Salt],Source.[IsTempPasswordEnabled],Source.[FirstName],Source.[LastName],Source.[Email],Source.[LanguageId],Source.[IsActive]);
-- WHEN NOT MATCHED BY SOURCE THEN 
-- DELETE;
GO

 MERGE INTO [dbo].[UserRole] AS Target
USING (VALUES
   ('BFD02B5C-2408-4870-8E1F-06C7A72187F6', 15)  -- PP Administrator
  ,('D3FB7F6A-6487-4190-9D9F-0ECD7A25E239', 3)  -- Active Partner
  ,('B6BDCA35-4D96-4C26-B6B2-D15E285147DB', 16)  -- New Partner
  ,('A1B2C3D4-5E6F-7890-ABCD-EF1234567890', 17)  -- PP Executive Leadership
) AS Source ([UserId],[RoleId])
ON (Target.[RoleId] = Source.[RoleId] and Target.[UserId] = Source.[UserId])
--WHEN MATCHED AND (Target.[Name] <> Source.[Name]) THEN
--	UPDATE SET
--		[Name] = Source.[Name]		
WHEN NOT MATCHED BY TARGET THEN
	INSERT([UserId], [RoleId])
	VALUES(Source.[UserId], Source.[RoleId]);
--WHEN NOT MATCHED BY SOURCE THEN 
--	DELETE;
GO

-- FormStatus lookup table seed data
MERGE INTO [dbo].[FormStatus] AS Target
USING (VALUES
  (0, 'Draft', 'Draft', 'Brouillon')
 ,(1, 'Submitted', 'Submitted', 'Soumis')
 ,(2, 'Approved', 'Approved', 'Approuvé')
 ,(3, 'Rejected', 'Rejected', 'Rejeté')
 ,(4, 'Reopened', 'Reopened', 'Rouvert')
 ,(5, 'Closed', 'Closed', 'Fermé')
) AS Source ([Id],[Name],[EnglishDislayName],[FrenchDisplayName])
ON (Target.[Id] = Source.[Id])
WHEN MATCHED AND (Target.[Name] <> Source.[Name] OR Target.[EnglishDislayName] <> Source.[EnglishDislayName] OR Target.[FrenchDisplayName] <> Source.[FrenchDisplayName]) THEN
	UPDATE SET
		[Name] = Source.[Name],
		[EnglishDislayName] = Source.[EnglishDislayName],
		[FrenchDisplayName] = Source.[FrenchDisplayName]
WHEN NOT MATCHED BY TARGET THEN
	INSERT([Id],[Name],[EnglishDislayName],[FrenchDisplayName])
	VALUES(Source.[Id],Source.[Name],Source.[EnglishDislayName],Source.[FrenchDisplayName])
WHEN NOT MATCHED BY SOURCE THEN
	DELETE;
GO

-- QuestionnaireStatus lookup table seed data
MERGE INTO [dbo].[QuestionnaireStatus] AS Target
USING (VALUES
  (0, 'Draft', 'Draft', 'Brouillon')
 ,(1, 'Published', 'Published', 'Publié')
 ,(2, 'Archived', 'Archived', 'Archivé')
) AS Source ([Id],[Name],[EnglishDislayName],[FrenchDisplayName])
ON (Target.[Id] = Source.[Id])
WHEN MATCHED AND (Target.[Name] <> Source.[Name] OR Target.[EnglishDislayName] <> Source.[EnglishDislayName] OR Target.[FrenchDisplayName] <> Source.[FrenchDisplayName]) THEN
	UPDATE SET
		[Name] = Source.[Name],
		[EnglishDislayName] = Source.[EnglishDislayName],
		[FrenchDisplayName] = Source.[FrenchDisplayName]
WHEN NOT MATCHED BY TARGET THEN
	INSERT([Id],[Name],[EnglishDislayName],[FrenchDisplayName])
	VALUES(Source.[Id],Source.[Name],Source.[EnglishDislayName],Source.[FrenchDisplayName])
WHEN NOT MATCHED BY SOURCE THEN
	DELETE;
GO

-- Test data only - Insert notification messages if they don't already exist

-- Check and insert notification messages only if they don't exist
IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'Creating an inclusive environment where everyone can bring their genuine selves to work, participate fully and be positioned for success')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('Creating an inclusive environment where everyone can bring their genuine selves to work, participate fully and be positioned for success');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'Meet your Inclusion, Equity and Diversity Advisory Council')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('Meet your Inclusion, Equity and Diversity Advisory Council');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'During the month of June, Indigenous History Month, we have seen tragic events unfold � the lives of 215 residential school children found in BC and 751 unmarked graves found in Saskatchewan')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('During the month of June, Indigenous History Month, we have seen tragic events unfold � the lives of 215 residential school children found in BC and 751 unmarked graves found in Saskatchewan');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'Include land acknowledgements in your Outlook email signature')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('Include land acknowledgements in your Outlook email signature');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'Pride Season Event: We celebrated the kick-off to Pride Season on Thursday, June 17 with a special virtual Pride webcast. If you missed it, you can watch the webcast recording here')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('Pride Season Event: We celebrated the kick-off to Pride Season on Thursday, June 17 with a special virtual Pride webcast. If you missed it, you can watch the webcast recording here');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'Change your BDO Outlook photo: Let�s add some colour to our conversations')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('Change your BDO Outlook photo: Let�s add some colour to our conversations');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'Include your preferred pronoun in your Outlook email signature')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('Include your preferred pronoun in your Outlook email signature');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'BDO 100 Celebration')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('BDO 100 Celebration');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'The New IE&D Advisory Council')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('The New IE&D Advisory Council');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'Your Firm Engagement HUB')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('Your Firm Engagement HUB');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'NEW AND IMPROVED MY BDO!')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('NEW AND IMPROVED MY BDO!');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'Congratulations - Chris Diepdael, CMC�BC Rising Star Award')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('Congratulations - Chris Diepdael, CMC�BC Rising Star Award');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = '[CAMPAIGN LAUNCH] Selling your business: now live!')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('[CAMPAIGN LAUNCH] Selling your business: now live!');
END

GO

GO
PRINT N'Update complete.';


GO
