﻿/*
Deployment script for BdoPartner.Plans.Database

This code was generated by a tool.
Changes to this file may cause incorrect behavior and will be lost if
the code is regenerated.
*/

GO
SET ANSI_NULLS, ANSI_PADDING, ANSI_WARNINGS, ARITHABORT, CONCAT_NULL_YIELDS_NULL, QUOTED_IDENTIFIER ON;

SET NUMERIC_ROUNDABORT OFF;


GO
:setvar DatabaseName "BdoPartner.Plans.Database"
:setvar Default<PERSON>ile<PERSON>refix "BdoPartner.Plans.Database"
:setvar DefaultDataPath "C:\Program Files\Microsoft SQL Server\MSSQL16.MSSQLSERVER\MSSQL\DATA\"
:setvar DefaultLogPath "C:\Program Files\Microsoft SQL Server\MSSQL16.MSSQLSERVER\MSSQL\DATA\"

GO
:on error exit
GO
/*
Detect SQLCMD mode and disable script execution if SQLCMD mode is not supported.
To re-enable the script after enabling SQLCMD mode, execute the following:
SET NOEXEC OFF; 
*/
:setvar __IsSqlCmdEnabled "True"
GO
IF N'$(__IsSqlCmdEnabled)' NOT LIKE N'True'
    BEGIN
        PRINT N'SQLCMD mode must be enabled to successfully execute this script.';
        SET NOEXEC ON;
    END


GO
USE [$(DatabaseName)];


GO
PRINT N'Dropping Default Constraint unnamed constraint on [dbo].[Form]...';


GO
ALTER TABLE [dbo].[Form] DROP CONSTRAINT [DF__tmp_ms_xx_Fo__Id__160F4887];


GO
PRINT N'Dropping Default Constraint unnamed constraint on [dbo].[Form]...';


GO
ALTER TABLE [dbo].[Form] DROP CONSTRAINT [DF__tmp_ms_xx__Statu__17036CC0];


GO
PRINT N'Dropping Default Constraint unnamed constraint on [dbo].[Form]...';


GO
ALTER TABLE [dbo].[Form] DROP CONSTRAINT [DF__tmp_ms_xx__IsAct__17F790F9];


GO
PRINT N'Dropping Default Constraint unnamed constraint on [dbo].[Form]...';


GO
ALTER TABLE [dbo].[Form] DROP CONSTRAINT [DF__tmp_ms_xx__Creat__18EBB532];


GO
PRINT N'Dropping Default Constraint unnamed constraint on [dbo].[Notification]...';


GO
ALTER TABLE [dbo].[Notification] DROP CONSTRAINT [DF__tmp_ms_xx__Creat__1BC821DD];


GO
PRINT N'Dropping Default Constraint unnamed constraint on [dbo].[Partner]...';


GO
ALTER TABLE [dbo].[Partner] DROP CONSTRAINT [DF__tmp_ms_xx_Pa__Id__1EA48E88];


GO
PRINT N'Dropping Default Constraint unnamed constraint on [dbo].[Partner]...';


GO
ALTER TABLE [dbo].[Partner] DROP CONSTRAINT [DF__tmp_ms_xx__IsAct__1F98B2C1];


GO
PRINT N'Dropping Default Constraint unnamed constraint on [dbo].[Partner]...';


GO
ALTER TABLE [dbo].[Partner] DROP CONSTRAINT [DF__tmp_ms_xx__Creat__208CD6FA];


GO
PRINT N'Dropping Default Constraint unnamed constraint on [dbo].[PartnerReferenceData]...';


GO
ALTER TABLE [dbo].[PartnerReferenceData] DROP CONSTRAINT [DF__PartnerRefer__Id__3F115E1A];


GO
PRINT N'Dropping Default Constraint unnamed constraint on [dbo].[PartnerReferenceData]...';


GO
ALTER TABLE [dbo].[PartnerReferenceData] DROP CONSTRAINT [DF__PartnerRe__Creat__40058253];


GO
PRINT N'Dropping Default Constraint unnamed constraint on [dbo].[PartnerReferenceDataMeta]...';


GO
ALTER TABLE [dbo].[PartnerReferenceDataMeta] DROP CONSTRAINT [DF__PartnerRefer__Id__40F9A68C];


GO
PRINT N'Dropping Default Constraint unnamed constraint on [dbo].[PartnerReferenceDataMeta]...';


GO
ALTER TABLE [dbo].[PartnerReferenceDataMeta] DROP CONSTRAINT [DF__PartnerRe__IsAct__41EDCAC5];


GO
PRINT N'Dropping Default Constraint unnamed constraint on [dbo].[PartnerReferenceDataMeta]...';


GO
ALTER TABLE [dbo].[PartnerReferenceDataMeta] DROP CONSTRAINT [DF__PartnerRe__Creat__42E1EEFE];


GO
PRINT N'Dropping Default Constraint unnamed constraint on [dbo].[PartnerReferenceDataMetaDetails]...';


GO
ALTER TABLE [dbo].[PartnerReferenceDataMetaDetails] DROP CONSTRAINT [DF__PartnerRefer__Id__43D61337];


GO
PRINT N'Dropping Default Constraint unnamed constraint on [dbo].[PartnerReferenceDataMetaDetails]...';


GO
ALTER TABLE [dbo].[PartnerReferenceDataMetaDetails] DROP CONSTRAINT [DF__PartnerRe__Creat__44CA3770];


GO
PRINT N'Dropping Default Constraint unnamed constraint on [dbo].[PartnerReferenceDataUpload]...';


GO
ALTER TABLE [dbo].[PartnerReferenceDataUpload] DROP CONSTRAINT [DF__PartnerRefer__Id__45BE5BA9];


GO
PRINT N'Dropping Default Constraint unnamed constraint on [dbo].[PartnerReferenceDataUpload]...';


GO
ALTER TABLE [dbo].[PartnerReferenceDataUpload] DROP CONSTRAINT [DF__PartnerRe__Statu__46B27FE2];


GO
PRINT N'Dropping Default Constraint unnamed constraint on [dbo].[PartnerReferenceDataUpload]...';


GO
ALTER TABLE [dbo].[PartnerReferenceDataUpload] DROP CONSTRAINT [DF__PartnerRe__Creat__47A6A41B];


GO
PRINT N'Dropping Default Constraint unnamed constraint on [dbo].[PartnerReferenceDataUploadDetails]...';


GO
ALTER TABLE [dbo].[PartnerReferenceDataUploadDetails] DROP CONSTRAINT [DF__PartnerRefer__Id__489AC854];


GO
PRINT N'Dropping Default Constraint unnamed constraint on [dbo].[PartnerReferenceDataUploadDetails]...';


GO
ALTER TABLE [dbo].[PartnerReferenceDataUploadDetails] DROP CONSTRAINT [DF__PartnerRe__Creat__498EEC8D];


GO
PRINT N'Dropping Default Constraint unnamed constraint on [dbo].[PartnerReviewer]...';


GO
ALTER TABLE [dbo].[PartnerReviewer] DROP CONSTRAINT [DF__PartnerRevie__Id__4A8310C6];


GO
PRINT N'Dropping Default Constraint unnamed constraint on [dbo].[PartnerReviewer]...';


GO
ALTER TABLE [dbo].[PartnerReviewer] DROP CONSTRAINT [DF__PartnerRe__Exemp__4B7734FF];


GO
PRINT N'Dropping Default Constraint unnamed constraint on [dbo].[PartnerReviewer]...';


GO
ALTER TABLE [dbo].[PartnerReviewer] DROP CONSTRAINT [DF__PartnerRe__Creat__4C6B5938];


GO
PRINT N'Dropping Default Constraint unnamed constraint on [dbo].[PartnerReviewerUpload]...';


GO
ALTER TABLE [dbo].[PartnerReviewerUpload] DROP CONSTRAINT [DF__PartnerRe__Statu__4D5F7D71];


GO
PRINT N'Dropping Default Constraint unnamed constraint on [dbo].[PartnerReviewerUpload]...';


GO
ALTER TABLE [dbo].[PartnerReviewerUpload] DROP CONSTRAINT [DF__PartnerRe__Creat__4E53A1AA];


GO
PRINT N'Dropping Default Constraint unnamed constraint on [dbo].[Questionnaire]...';


GO
ALTER TABLE [dbo].[Questionnaire] DROP CONSTRAINT [DF__tmp_ms_xx_Qu__Id__236943A5];


GO
PRINT N'Dropping Default Constraint unnamed constraint on [dbo].[Questionnaire]...';


GO
ALTER TABLE [dbo].[Questionnaire] DROP CONSTRAINT [DF__tmp_ms_xx__FormS__245D67DE];


GO
PRINT N'Dropping Default Constraint unnamed constraint on [dbo].[Questionnaire]...';


GO
ALTER TABLE [dbo].[Questionnaire] DROP CONSTRAINT [DF__tmp_ms_xx__Ackno__25518C17];


GO
PRINT N'Dropping Default Constraint unnamed constraint on [dbo].[Questionnaire]...';


GO
ALTER TABLE [dbo].[Questionnaire] DROP CONSTRAINT [DF__tmp_ms_xx__Gener__2645B050];


GO
PRINT N'Dropping Default Constraint unnamed constraint on [dbo].[Questionnaire]...';


GO
ALTER TABLE [dbo].[Questionnaire] DROP CONSTRAINT [DF__tmp_ms_xx__Statu__2739D489];


GO
PRINT N'Dropping Default Constraint unnamed constraint on [dbo].[Questionnaire]...';


GO
ALTER TABLE [dbo].[Questionnaire] DROP CONSTRAINT [DF__tmp_ms_xx__IsAct__282DF8C2];


GO
PRINT N'Dropping Default Constraint unnamed constraint on [dbo].[Questionnaire]...';


GO
ALTER TABLE [dbo].[Questionnaire] DROP CONSTRAINT [DF__tmp_ms_xx__Creat__29221CFB];


GO
PRINT N'Dropping Default Constraint unnamed constraint on [dbo].[UserAnswer]...';


GO
ALTER TABLE [dbo].[UserAnswer] DROP CONSTRAINT [DF__UserAnswer__Id__73BA3083];


GO
PRINT N'Dropping Default Constraint unnamed constraint on [dbo].[UserAnswer]...';


GO
ALTER TABLE [dbo].[UserAnswer] DROP CONSTRAINT [DF__UserAnswe__IsAct__74AE54BC];


GO
PRINT N'Dropping Default Constraint unnamed constraint on [dbo].[UserAnswer]...';


GO
ALTER TABLE [dbo].[UserAnswer] DROP CONSTRAINT [DF__UserAnswe__Creat__75A278F5];


GO
PRINT N'Dropping Foreign Key [dbo].[FK_Form_Questionnaire]...';


GO
ALTER TABLE [dbo].[Form] DROP CONSTRAINT [FK_Form_Questionnaire];


GO
PRINT N'Dropping Foreign Key [dbo].[FK_Form_Status]...';


GO
ALTER TABLE [dbo].[Form] DROP CONSTRAINT [FK_Form_Status];


GO
PRINT N'Dropping Foreign Key [dbo].[FK_UserAnswer_Form]...';


GO
ALTER TABLE [dbo].[UserAnswer] DROP CONSTRAINT [FK_UserAnswer_Form];


GO
PRINT N'Dropping Foreign Key [dbo].[FK_PartnerReferenceData_Partner]...';


GO
ALTER TABLE [dbo].[PartnerReferenceData] DROP CONSTRAINT [FK_PartnerReferenceData_Partner];


GO
PRINT N'Dropping Foreign Key [dbo].[FK_PartnerReviewer_Partner]...';


GO
ALTER TABLE [dbo].[PartnerReviewer] DROP CONSTRAINT [FK_PartnerReviewer_Partner];


GO
PRINT N'Dropping Foreign Key [dbo].[FK_PartnerReviewer_PrimaryReviewer]...';


GO
ALTER TABLE [dbo].[PartnerReviewer] DROP CONSTRAINT [FK_PartnerReviewer_PrimaryReviewer];


GO
PRINT N'Dropping Foreign Key [dbo].[FK_PartnerReviewer_SecondaryReviewer]...';


GO
ALTER TABLE [dbo].[PartnerReviewer] DROP CONSTRAINT [FK_PartnerReviewer_SecondaryReviewer];


GO
PRINT N'Dropping Foreign Key [dbo].[FK_PartnerReferenceData_Meta]...';


GO
ALTER TABLE [dbo].[PartnerReferenceData] DROP CONSTRAINT [FK_PartnerReferenceData_Meta];


GO
PRINT N'Dropping Foreign Key [dbo].[FK_PartnerReferenceDataMetaDetails_Meta]...';


GO
ALTER TABLE [dbo].[PartnerReferenceDataMetaDetails] DROP CONSTRAINT [FK_PartnerReferenceDataMetaDetails_Meta];


GO
PRINT N'Dropping Foreign Key [dbo].[FK_PartnerReferenceDataUpload_Meta]...';


GO
ALTER TABLE [dbo].[PartnerReferenceDataUpload] DROP CONSTRAINT [FK_PartnerReferenceDataUpload_Meta];


GO
PRINT N'Dropping Foreign Key [dbo].[FK_PartnerReferenceDataUploadDetails_Upload]...';


GO
ALTER TABLE [dbo].[PartnerReferenceDataUploadDetails] DROP CONSTRAINT [FK_PartnerReferenceDataUploadDetails_Upload];


GO
PRINT N'Dropping Foreign Key [dbo].[FK_PartnerReviewerUploadDetails_PartnerReviewerUpload]...';


GO
ALTER TABLE [dbo].[PartnerReviewerUploadDetails] DROP CONSTRAINT [FK_PartnerReviewerUploadDetails_PartnerReviewerUpload];


GO
PRINT N'Dropping Foreign Key [dbo].[FK_Questionnaire_Status]...';


GO
ALTER TABLE [dbo].[Questionnaire] DROP CONSTRAINT [FK_Questionnaire_Status];


GO
PRINT N'Starting rebuilding table [dbo].[Form]...';


GO
BEGIN TRANSACTION;

SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;

SET XACT_ABORT ON;

CREATE TABLE [dbo].[tmp_ms_xx_Form] (
    [Id]                    UNIQUEIDENTIFIER DEFAULT newsequentialid() NOT NULL,
    [QuestionnaireId]       UNIQUEIDENTIFIER NOT NULL,
    [Year]                  SMALLINT         NOT NULL,
    [Comments]              NVARCHAR (MAX)   NULL,
    [Status]                TINYINT          DEFAULT 0 NOT NULL,
    [IsActive]              BIT              DEFAULT 1 NOT NULL,
    [CreatedBy]             UNIQUEIDENTIFIER NULL,
    [CreatedByName]         NVARCHAR (100)   NULL,
    [CreatedOn]             DATETIME2 (7)    DEFAULT getutcdate() NULL,
    [ModifiedBy]            UNIQUEIDENTIFIER NULL,
    [ModifiedByName]        NVARCHAR (100)   NULL,
    [ModifiedOn]            DATETIME2 (7)    NULL,
    [PartnerObjectId]       NVARCHAR (100)   NOT NULL,
    [PartnerSubmittionDate] DATETIME2 (7)    NULL,
    [PartnerName]           NVARCHAR (500)   NULL,
    [Pdf]                   VARBINARY (MAX)  NULL,
    CONSTRAINT [tmp_ms_xx_constraint_PK_Form1] PRIMARY KEY CLUSTERED ([Id] ASC)
);

IF EXISTS (SELECT TOP 1 1 
           FROM   [dbo].[Form])
    BEGIN
        INSERT INTO [dbo].[tmp_ms_xx_Form] ([Id], [QuestionnaireId], [Year], [Comments], [Status], [IsActive], [CreatedBy], [CreatedOn], [ModifiedBy], [ModifiedOn], [PartnerObjectId], [PartnerSubmittionDate], [PartnerName], [Pdf])
        SELECT   [Id],
                 [QuestionnaireId],
                 [Year],
                 [Comments],
                 [Status],
                 [IsActive],
                 [CreatedBy],
                 [CreatedOn],
                 [ModifiedBy],
                 [ModifiedOn],
                 [PartnerObjectId],
                 [PartnerSubmittionDate],
                 [PartnerName],
                 [Pdf]
        FROM     [dbo].[Form]
        ORDER BY [Id] ASC;
    END

DROP TABLE [dbo].[Form];

EXECUTE sp_rename N'[dbo].[tmp_ms_xx_Form]', N'Form';

EXECUTE sp_rename N'[dbo].[tmp_ms_xx_constraint_PK_Form1]', N'PK_Form', N'OBJECT';

COMMIT TRANSACTION;

SET TRANSACTION ISOLATION LEVEL READ COMMITTED;


GO
PRINT N'Starting rebuilding table [dbo].[Notification]...';


GO
BEGIN TRANSACTION;

SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;

SET XACT_ABORT ON;

CREATE TABLE [dbo].[tmp_ms_xx_Notification] (
    [Id]            BIGINT           IDENTITY (1, 1) NOT NULL,
    [Message]       NVARCHAR (MAX)   NOT NULL,
    [CreatedBy]     UNIQUEIDENTIFIER NULL,
    [CreatedByName] NVARCHAR (100)   NULL,
    [CreatedOn]     DATETIME2 (7)    DEFAULT getutcdate() NOT NULL,
    CONSTRAINT [tmp_ms_xx_constraint_PK_Notification1] PRIMARY KEY CLUSTERED ([Id] ASC)
);

IF EXISTS (SELECT TOP 1 1 
           FROM   [dbo].[Notification])
    BEGIN
        SET IDENTITY_INSERT [dbo].[tmp_ms_xx_Notification] ON;
        INSERT INTO [dbo].[tmp_ms_xx_Notification] ([Id], [Message], [CreatedBy], [CreatedOn])
        SELECT   [Id],
                 [Message],
                 [CreatedBy],
                 [CreatedOn]
        FROM     [dbo].[Notification]
        ORDER BY [Id] ASC;
        SET IDENTITY_INSERT [dbo].[tmp_ms_xx_Notification] OFF;
    END

DROP TABLE [dbo].[Notification];

EXECUTE sp_rename N'[dbo].[tmp_ms_xx_Notification]', N'Notification';

EXECUTE sp_rename N'[dbo].[tmp_ms_xx_constraint_PK_Notification1]', N'PK_Notification', N'OBJECT';

COMMIT TRANSACTION;

SET TRANSACTION ISOLATION LEVEL READ COMMITTED;


GO
PRINT N'Starting rebuilding table [dbo].[Partner]...';


GO
BEGIN TRANSACTION;

SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;

SET XACT_ABORT ON;

CREATE TABLE [dbo].[tmp_ms_xx_Partner] (
    [Id]               UNIQUEIDENTIFIER DEFAULT newsequentialid() NOT NULL,
    [EmployeeId]       INT              NULL,
    [FirstName]        NVARCHAR (150)   NULL,
    [LastName]         NVARCHAR (150)   NULL,
    [DisplayName]      NVARCHAR (300)   NULL,
    [DOB]              DATE             NULL,
    [Mail]             NVARCHAR (200)   NULL,
    [PartnerType]      NVARCHAR (100)   NULL,
    [Department]       NVARCHAR (200)   NULL,
    [Location]         NVARCHAR (200)   NULL,
    [LocationId]       NVARCHAR (200)   NULL,
    [WGroup]           NVARCHAR (200)   NULL,
    [WGroupId]         NVARCHAR (200)   NULL,
    [ServiceLine]      NVARCHAR (200)   NULL,
    [ServiceLineId]    NVARCHAR (200)   NULL,
    [SubServiceLine]   NVARCHAR (200)   NULL,
    [SubServiceLineId] NVARCHAR (200)   NULL,
    [IsActive]         BIT              DEFAULT 1 NOT NULL,
    [CreatedBy]        UNIQUEIDENTIFIER NULL,
    [CreatedByName]    NVARCHAR (100)   NULL,
    [CreatedOn]        DATETIME2 (7)    DEFAULT getutcdate() NULL,
    [ModifiedBy]       UNIQUEIDENTIFIER NULL,
    [ModifiedByName]   NVARCHAR (100)   NULL,
    [ModifiedOn]       DATETIME2 (7)    NULL,
    CONSTRAINT [tmp_ms_xx_constraint_PK_Partner1] PRIMARY KEY CLUSTERED ([Id] ASC)
);

IF EXISTS (SELECT TOP 1 1 
           FROM   [dbo].[Partner])
    BEGIN
        INSERT INTO [dbo].[tmp_ms_xx_Partner] ([Id], [EmployeeId], [FirstName], [LastName], [DisplayName], [DOB], [Mail], [PartnerType], [Department], [Location], [LocationId], [WGroup], [WGroupId], [ServiceLine], [ServiceLineId], [SubServiceLine], [SubServiceLineId], [IsActive], [CreatedBy], [CreatedOn], [ModifiedBy], [ModifiedOn])
        SELECT   [Id],
                 [EmployeeId],
                 [FirstName],
                 [LastName],
                 [DisplayName],
                 [DOB],
                 [Mail],
                 [PartnerType],
                 [Department],
                 [Location],
                 [LocationId],
                 [WGroup],
                 [WGroupId],
                 [ServiceLine],
                 [ServiceLineId],
                 [SubServiceLine],
                 [SubServiceLineId],
                 [IsActive],
                 [CreatedBy],
                 [CreatedOn],
                 [ModifiedBy],
                 [ModifiedOn]
        FROM     [dbo].[Partner]
        ORDER BY [Id] ASC;
    END

DROP TABLE [dbo].[Partner];

EXECUTE sp_rename N'[dbo].[tmp_ms_xx_Partner]', N'Partner';

EXECUTE sp_rename N'[dbo].[tmp_ms_xx_constraint_PK_Partner1]', N'PK_Partner', N'OBJECT';

COMMIT TRANSACTION;

SET TRANSACTION ISOLATION LEVEL READ COMMITTED;


GO
PRINT N'Starting rebuilding table [dbo].[PartnerReferenceData]...';


GO
BEGIN TRANSACTION;

SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;

SET XACT_ABORT ON;

CREATE TABLE [dbo].[tmp_ms_xx_PartnerReferenceData] (
    [Id]             UNIQUEIDENTIFIER DEFAULT newsequentialid() NOT NULL,
    [PartnerId]      UNIQUEIDENTIFIER NOT NULL,
    [Year]           SMALLINT         NOT NULL,
    [Cycle]          TINYINT          NOT NULL,
    [MetaId]         UNIQUEIDENTIFIER NOT NULL,
    [Data]           NVARCHAR (MAX)   NULL,
    [CreatedBy]      UNIQUEIDENTIFIER NULL,
    [CreatedByName]  NVARCHAR (100)   NULL,
    [CreatedOn]      DATETIME2 (7)    DEFAULT getutcdate() NULL,
    [ModifiedBy]     UNIQUEIDENTIFIER NULL,
    [ModifiedByName] NVARCHAR (100)   NULL,
    [ModifiedOn]     DATETIME2 (7)    NULL,
    CONSTRAINT [tmp_ms_xx_constraint_PK_PartnerReferenceData1] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [tmp_ms_xx_constraint_UK_PartnerReferenceData_Partner_Year_Cycle_Meta1] UNIQUE NONCLUSTERED ([PartnerId] ASC, [Year] ASC, [Cycle] ASC, [MetaId] ASC)
);

IF EXISTS (SELECT TOP 1 1 
           FROM   [dbo].[PartnerReferenceData])
    BEGIN
        INSERT INTO [dbo].[tmp_ms_xx_PartnerReferenceData] ([Id], [PartnerId], [Year], [Cycle], [MetaId], [Data], [CreatedBy], [CreatedOn], [ModifiedBy], [ModifiedOn])
        SELECT   [Id],
                 [PartnerId],
                 [Year],
                 [Cycle],
                 [MetaId],
                 [Data],
                 [CreatedBy],
                 [CreatedOn],
                 [ModifiedBy],
                 [ModifiedOn]
        FROM     [dbo].[PartnerReferenceData]
        ORDER BY [Id] ASC;
    END

DROP TABLE [dbo].[PartnerReferenceData];

EXECUTE sp_rename N'[dbo].[tmp_ms_xx_PartnerReferenceData]', N'PartnerReferenceData';

EXECUTE sp_rename N'[dbo].[tmp_ms_xx_constraint_PK_PartnerReferenceData1]', N'PK_PartnerReferenceData', N'OBJECT';

EXECUTE sp_rename N'[dbo].[tmp_ms_xx_constraint_UK_PartnerReferenceData_Partner_Year_Cycle_Meta1]', N'UK_PartnerReferenceData_Partner_Year_Cycle_Meta', N'OBJECT';

COMMIT TRANSACTION;

SET TRANSACTION ISOLATION LEVEL READ COMMITTED;


GO
PRINT N'Starting rebuilding table [dbo].[PartnerReferenceDataMeta]...';


GO
BEGIN TRANSACTION;

SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;

SET XACT_ABORT ON;

CREATE TABLE [dbo].[tmp_ms_xx_PartnerReferenceDataMeta] (
    [Id]             UNIQUEIDENTIFIER DEFAULT newsequentialid() NOT NULL,
    [FileName]       NVARCHAR (500)   NOT NULL,
    [Year]           SMALLINT         NOT NULL,
    [Cycle]          TINYINT          NOT NULL,
    [IsActive]       BIT              DEFAULT 1 NOT NULL,
    [CreatedBy]      UNIQUEIDENTIFIER NULL,
    [CreatedByName]  NVARCHAR (100)   NULL,
    [CreatedOn]      DATETIME2 (7)    DEFAULT getutcdate() NULL,
    [ModifiedBy]     UNIQUEIDENTIFIER NULL,
    [ModifiedByName] NVARCHAR (100)   NULL,
    [ModifiedOn]     DATETIME2 (7)    NULL,
    CONSTRAINT [tmp_ms_xx_constraint_PK_PartnerReferenceDataMeta1] PRIMARY KEY CLUSTERED ([Id] ASC)
);

IF EXISTS (SELECT TOP 1 1 
           FROM   [dbo].[PartnerReferenceDataMeta])
    BEGIN
        INSERT INTO [dbo].[tmp_ms_xx_PartnerReferenceDataMeta] ([Id], [FileName], [Year], [Cycle], [IsActive], [CreatedBy], [CreatedOn], [ModifiedBy], [ModifiedOn])
        SELECT   [Id],
                 [FileName],
                 [Year],
                 [Cycle],
                 [IsActive],
                 [CreatedBy],
                 [CreatedOn],
                 [ModifiedBy],
                 [ModifiedOn]
        FROM     [dbo].[PartnerReferenceDataMeta]
        ORDER BY [Id] ASC;
    END

DROP TABLE [dbo].[PartnerReferenceDataMeta];

EXECUTE sp_rename N'[dbo].[tmp_ms_xx_PartnerReferenceDataMeta]', N'PartnerReferenceDataMeta';

EXECUTE sp_rename N'[dbo].[tmp_ms_xx_constraint_PK_PartnerReferenceDataMeta1]', N'PK_PartnerReferenceDataMeta', N'OBJECT';

COMMIT TRANSACTION;

SET TRANSACTION ISOLATION LEVEL READ COMMITTED;


GO
PRINT N'Starting rebuilding table [dbo].[PartnerReferenceDataMetaDetails]...';


GO
BEGIN TRANSACTION;

SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;

SET XACT_ABORT ON;

CREATE TABLE [dbo].[tmp_ms_xx_PartnerReferenceDataMetaDetails] (
    [Id]                   UNIQUEIDENTIFIER DEFAULT newsequentialid() NOT NULL,
    [MetaId]               UNIQUEIDENTIFIER NOT NULL,
    [ColumnName]           NVARCHAR (200)   NOT NULL,
    [NormalizedColumnName] NVARCHAR (200)   NOT NULL,
    [ColumnDataType]       TINYINT          NOT NULL,
    [ColumnOrder]          SMALLINT         NOT NULL,
    [CreatedBy]            UNIQUEIDENTIFIER NULL,
    [CreatedByName]        NVARCHAR (100)   NULL,
    [CreatedOn]            DATETIME2 (7)    DEFAULT getutcdate() NULL,
    [ModifiedBy]           UNIQUEIDENTIFIER NULL,
    [ModifiedByName]       NVARCHAR (100)   NULL,
    [ModifiedOn]           DATETIME2 (7)    NULL,
    CONSTRAINT [tmp_ms_xx_constraint_PK_PartnerReferenceDataMetaDetails1] PRIMARY KEY CLUSTERED ([Id] ASC)
);

IF EXISTS (SELECT TOP 1 1 
           FROM   [dbo].[PartnerReferenceDataMetaDetails])
    BEGIN
        INSERT INTO [dbo].[tmp_ms_xx_PartnerReferenceDataMetaDetails] ([Id], [MetaId], [ColumnName], [NormalizedColumnName], [ColumnDataType], [ColumnOrder], [CreatedBy], [CreatedOn], [ModifiedBy], [ModifiedOn])
        SELECT   [Id],
                 [MetaId],
                 [ColumnName],
                 [NormalizedColumnName],
                 [ColumnDataType],
                 [ColumnOrder],
                 [CreatedBy],
                 [CreatedOn],
                 [ModifiedBy],
                 [ModifiedOn]
        FROM     [dbo].[PartnerReferenceDataMetaDetails]
        ORDER BY [Id] ASC;
    END

DROP TABLE [dbo].[PartnerReferenceDataMetaDetails];

EXECUTE sp_rename N'[dbo].[tmp_ms_xx_PartnerReferenceDataMetaDetails]', N'PartnerReferenceDataMetaDetails';

EXECUTE sp_rename N'[dbo].[tmp_ms_xx_constraint_PK_PartnerReferenceDataMetaDetails1]', N'PK_PartnerReferenceDataMetaDetails', N'OBJECT';

COMMIT TRANSACTION;

SET TRANSACTION ISOLATION LEVEL READ COMMITTED;


GO
PRINT N'Starting rebuilding table [dbo].[PartnerReferenceDataUpload]...';


GO
BEGIN TRANSACTION;

SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;

SET XACT_ABORT ON;

CREATE TABLE [dbo].[tmp_ms_xx_PartnerReferenceDataUpload] (
    [Id]                UNIQUEIDENTIFIER DEFAULT newsequentialid() NOT NULL,
    [UploadFileName]    NVARCHAR (500)   NOT NULL,
    [FileContent]       VARBINARY (MAX)  NULL,
    [Year]              SMALLINT         NOT NULL,
    [Cycle]             TINYINT          NOT NULL,
    [MetaId]            UNIQUEIDENTIFIER NOT NULL,
    [Status]            TINYINT          DEFAULT 0 NOT NULL,
    [ValidationSummary] NVARCHAR (MAX)   NULL,
    [CreatedBy]         UNIQUEIDENTIFIER NULL,
    [CreatedByName]     NVARCHAR (100)   NULL,
    [CreatedOn]         DATETIME2 (7)    DEFAULT getutcdate() NULL,
    [ModifiedBy]        UNIQUEIDENTIFIER NULL,
    [ModifiedByName]    NVARCHAR (100)   NULL,
    [ModifiedOn]        DATETIME2 (7)    NULL,
    CONSTRAINT [tmp_ms_xx_constraint_PK_PartnerReferenceDataUpload1] PRIMARY KEY CLUSTERED ([Id] ASC)
);

IF EXISTS (SELECT TOP 1 1 
           FROM   [dbo].[PartnerReferenceDataUpload])
    BEGIN
        INSERT INTO [dbo].[tmp_ms_xx_PartnerReferenceDataUpload] ([Id], [UploadFileName], [FileContent], [Year], [Cycle], [MetaId], [Status], [ValidationSummary], [CreatedBy], [CreatedOn], [ModifiedBy], [ModifiedOn])
        SELECT   [Id],
                 [UploadFileName],
                 [FileContent],
                 [Year],
                 [Cycle],
                 [MetaId],
                 [Status],
                 [ValidationSummary],
                 [CreatedBy],
                 [CreatedOn],
                 [ModifiedBy],
                 [ModifiedOn]
        FROM     [dbo].[PartnerReferenceDataUpload]
        ORDER BY [Id] ASC;
    END

DROP TABLE [dbo].[PartnerReferenceDataUpload];

EXECUTE sp_rename N'[dbo].[tmp_ms_xx_PartnerReferenceDataUpload]', N'PartnerReferenceDataUpload';

EXECUTE sp_rename N'[dbo].[tmp_ms_xx_constraint_PK_PartnerReferenceDataUpload1]', N'PK_PartnerReferenceDataUpload', N'OBJECT';

COMMIT TRANSACTION;

SET TRANSACTION ISOLATION LEVEL READ COMMITTED;


GO
PRINT N'Starting rebuilding table [dbo].[PartnerReferenceDataUploadDetails]...';


GO
BEGIN TRANSACTION;

SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;

SET XACT_ABORT ON;

CREATE TABLE [dbo].[tmp_ms_xx_PartnerReferenceDataUploadDetails] (
    [Id]                           UNIQUEIDENTIFIER DEFAULT newsequentialid() NOT NULL,
    [PartnerReferenceDataUploadId] UNIQUEIDENTIFIER NOT NULL,
    [RowId]                        INT              NOT NULL,
    [Data]                         NVARCHAR (MAX)   NULL,
    [ValidationError]              NVARCHAR (500)   NULL,
    [CreatedBy]                    UNIQUEIDENTIFIER NULL,
    [CreatedByName]                NVARCHAR (100)   NULL,
    [CreatedOn]                    DATETIME2 (7)    DEFAULT getutcdate() NULL,
    [ModifiedBy]                   UNIQUEIDENTIFIER NULL,
    [ModifiedByName]               NVARCHAR (100)   NULL,
    [ModifiedOn]                   DATETIME2 (7)    NULL,
    CONSTRAINT [tmp_ms_xx_constraint_PK_PartnerReferenceDataUploadDetails1] PRIMARY KEY CLUSTERED ([Id] ASC)
);

IF EXISTS (SELECT TOP 1 1 
           FROM   [dbo].[PartnerReferenceDataUploadDetails])
    BEGIN
        INSERT INTO [dbo].[tmp_ms_xx_PartnerReferenceDataUploadDetails] ([Id], [PartnerReferenceDataUploadId], [RowId], [Data], [ValidationError], [CreatedBy], [CreatedOn], [ModifiedBy], [ModifiedOn])
        SELECT   [Id],
                 [PartnerReferenceDataUploadId],
                 [RowId],
                 [Data],
                 [ValidationError],
                 [CreatedBy],
                 [CreatedOn],
                 [ModifiedBy],
                 [ModifiedOn]
        FROM     [dbo].[PartnerReferenceDataUploadDetails]
        ORDER BY [Id] ASC;
    END

DROP TABLE [dbo].[PartnerReferenceDataUploadDetails];

EXECUTE sp_rename N'[dbo].[tmp_ms_xx_PartnerReferenceDataUploadDetails]', N'PartnerReferenceDataUploadDetails';

EXECUTE sp_rename N'[dbo].[tmp_ms_xx_constraint_PK_PartnerReferenceDataUploadDetails1]', N'PK_PartnerReferenceDataUploadDetails', N'OBJECT';

COMMIT TRANSACTION;

SET TRANSACTION ISOLATION LEVEL READ COMMITTED;


GO
PRINT N'Starting rebuilding table [dbo].[PartnerReviewer]...';


GO
BEGIN TRANSACTION;

SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;

SET XACT_ABORT ON;

CREATE TABLE [dbo].[tmp_ms_xx_PartnerReviewer] (
    [Id]                    UNIQUEIDENTIFIER DEFAULT newsequentialid() NOT NULL,
    [Year]                  SMALLINT         NOT NULL,
    [PartnerId]             UNIQUEIDENTIFIER NOT NULL,
    [Exempt]                BIT              DEFAULT 0 NOT NULL,
    [LeadershipRole]        NVARCHAR (100)   NULL,
    [PrimaryReviewerId]     UNIQUEIDENTIFIER NULL,
    [PrimaryReviewerName]   NVARCHAR (500)   NULL,
    [SecondaryReviewerId]   UNIQUEIDENTIFIER NULL,
    [SecondaryReviewerName] NVARCHAR (500)   NULL,
    [CreatedBy]             UNIQUEIDENTIFIER NULL,
    [CreatedByName]         NVARCHAR (100)   NULL,
    [CreatedOn]             DATETIME2 (7)    DEFAULT getutcdate() NULL,
    [ModifiedBy]            UNIQUEIDENTIFIER NULL,
    [ModifiedByName]        NVARCHAR (100)   NULL,
    [ModifiedOn]            DATETIME2 (7)    NULL,
    CONSTRAINT [tmp_ms_xx_constraint_PK_PartnerReviewer1] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [tmp_ms_xx_constraint_AK_PartnerReviewer_PartnerYear1] UNIQUE NONCLUSTERED ([PartnerId] ASC, [Year] ASC)
);

IF EXISTS (SELECT TOP 1 1 
           FROM   [dbo].[PartnerReviewer])
    BEGIN
        INSERT INTO [dbo].[tmp_ms_xx_PartnerReviewer] ([Id], [Year], [PartnerId], [Exempt], [LeadershipRole], [PrimaryReviewerId], [PrimaryReviewerName], [SecondaryReviewerId], [SecondaryReviewerName], [CreatedBy], [CreatedOn], [ModifiedBy], [ModifiedOn])
        SELECT   [Id],
                 [Year],
                 [PartnerId],
                 [Exempt],
                 [LeadershipRole],
                 [PrimaryReviewerId],
                 [PrimaryReviewerName],
                 [SecondaryReviewerId],
                 [SecondaryReviewerName],
                 [CreatedBy],
                 [CreatedOn],
                 [ModifiedBy],
                 [ModifiedOn]
        FROM     [dbo].[PartnerReviewer]
        ORDER BY [Id] ASC;
    END

DROP TABLE [dbo].[PartnerReviewer];

EXECUTE sp_rename N'[dbo].[tmp_ms_xx_PartnerReviewer]', N'PartnerReviewer';

EXECUTE sp_rename N'[dbo].[tmp_ms_xx_constraint_PK_PartnerReviewer1]', N'PK_PartnerReviewer', N'OBJECT';

EXECUTE sp_rename N'[dbo].[tmp_ms_xx_constraint_AK_PartnerReviewer_PartnerYear1]', N'AK_PartnerReviewer_PartnerYear', N'OBJECT';

COMMIT TRANSACTION;

SET TRANSACTION ISOLATION LEVEL READ COMMITTED;


GO
PRINT N'Starting rebuilding table [dbo].[PartnerReviewerUpload]...';


GO
BEGIN TRANSACTION;

SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;

SET XACT_ABORT ON;

CREATE TABLE [dbo].[tmp_ms_xx_PartnerReviewerUpload] (
    [Id]                INT              IDENTITY (1, 1) NOT NULL,
    [Years]             NVARCHAR (100)   NOT NULL,
    [UploadFileName]    NVARCHAR (500)   NULL,
    [ValidationSummary] NVARCHAR (MAX)   NULL,
    [FileContent]       VARBINARY (MAX)  NULL,
    [Status]            TINYINT          DEFAULT 0 NOT NULL,
    [CreatedBy]         UNIQUEIDENTIFIER NULL,
    [CreatedByName]     NVARCHAR (100)   NULL,
    [CreatedOn]         DATETIME2 (7)    DEFAULT getutcdate() NULL,
    [ModifiedBy]        UNIQUEIDENTIFIER NULL,
    [ModifiedByName]    NVARCHAR (100)   NULL,
    [ModifiedOn]        DATETIME2 (7)    NULL,
    CONSTRAINT [tmp_ms_xx_constraint_PK_PartnerReviewerUpload1] PRIMARY KEY CLUSTERED ([Id] ASC)
);

IF EXISTS (SELECT TOP 1 1 
           FROM   [dbo].[PartnerReviewerUpload])
    BEGIN
        SET IDENTITY_INSERT [dbo].[tmp_ms_xx_PartnerReviewerUpload] ON;
        INSERT INTO [dbo].[tmp_ms_xx_PartnerReviewerUpload] ([Id], [Years], [UploadFileName], [ValidationSummary], [FileContent], [Status], [CreatedBy], [CreatedOn], [ModifiedBy], [ModifiedOn])
        SELECT   [Id],
                 [Years],
                 [UploadFileName],
                 [ValidationSummary],
                 [FileContent],
                 [Status],
                 [CreatedBy],
                 [CreatedOn],
                 [ModifiedBy],
                 [ModifiedOn]
        FROM     [dbo].[PartnerReviewerUpload]
        ORDER BY [Id] ASC;
        SET IDENTITY_INSERT [dbo].[tmp_ms_xx_PartnerReviewerUpload] OFF;
    END

DROP TABLE [dbo].[PartnerReviewerUpload];

EXECUTE sp_rename N'[dbo].[tmp_ms_xx_PartnerReviewerUpload]', N'PartnerReviewerUpload';

EXECUTE sp_rename N'[dbo].[tmp_ms_xx_constraint_PK_PartnerReviewerUpload1]', N'PK_PartnerReviewerUpload', N'OBJECT';

COMMIT TRANSACTION;

SET TRANSACTION ISOLATION LEVEL READ COMMITTED;


GO
PRINT N'Altering Table [dbo].[PartnerReviewerUploadDetails]...';


GO
ALTER TABLE [dbo].[PartnerReviewerUploadDetails]
    ADD [CreatedBy]      UNIQUEIDENTIFIER NULL,
        [CreatedByName]  NVARCHAR (100)   NULL,
        [CreatedOn]      DATETIME2 (7)    DEFAULT getutcdate() NULL,
        [ModifiedBy]     UNIQUEIDENTIFIER NULL,
        [ModifiedByName] NVARCHAR (100)   NULL,
        [ModifiedOn]     DATETIME2 (7)    NULL;


GO
PRINT N'Starting rebuilding table [dbo].[Questionnaire]...';


GO
BEGIN TRANSACTION;

SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;

SET XACT_ABORT ON;

CREATE TABLE [dbo].[tmp_ms_xx_Questionnaire] (
    [Id]                  UNIQUEIDENTIFIER DEFAULT newsequentialid() NOT NULL,
    [Name]                NVARCHAR (100)   NOT NULL,
    [Year]                SMALLINT         NOT NULL,
    [DefinitionJson]      NVARCHAR (MAX)   NULL,
    [DraftDefinitionJson] NVARCHAR (MAX)   NULL,
    [FormSystemVersion]   INT              DEFAULT 0 NOT NULL,
    [Acknowledgement]     BIT              DEFAULT 0 NOT NULL,
    [AcknowledgementText] NVARCHAR (1500)  NULL,
    [GeneralComments]     BIT              DEFAULT 0 NOT NULL,
    [GeneralCommentsText] NVARCHAR (MAX)   NULL,
    [Status]              TINYINT          DEFAULT 0 NOT NULL,
    [IsActive]            BIT              DEFAULT 1 NOT NULL,
    [CreatedBy]           UNIQUEIDENTIFIER NULL,
    [CreatedByName]       NVARCHAR (100)   NULL,
    [CreatedOn]           DATETIME2 (7)    DEFAULT getutcdate() NULL,
    [ModifiedBy]          UNIQUEIDENTIFIER NULL,
    [ModifiedByName]      NVARCHAR (100)   NULL,
    [ModifiedOn]          DATETIME2 (7)    NULL,
    CONSTRAINT [tmp_ms_xx_constraint_PK_Questionnaire1] PRIMARY KEY CLUSTERED ([Id] ASC)
);

IF EXISTS (SELECT TOP 1 1 
           FROM   [dbo].[Questionnaire])
    BEGIN
        INSERT INTO [dbo].[tmp_ms_xx_Questionnaire] ([Id], [Name], [Year], [DefinitionJson], [DraftDefinitionJson], [FormSystemVersion], [Acknowledgement], [AcknowledgementText], [GeneralComments], [GeneralCommentsText], [Status], [IsActive], [CreatedBy], [CreatedOn], [ModifiedBy], [ModifiedOn])
        SELECT   [Id],
                 [Name],
                 [Year],
                 [DefinitionJson],
                 [DraftDefinitionJson],
                 [FormSystemVersion],
                 [Acknowledgement],
                 [AcknowledgementText],
                 [GeneralComments],
                 [GeneralCommentsText],
                 [Status],
                 [IsActive],
                 [CreatedBy],
                 [CreatedOn],
                 [ModifiedBy],
                 [ModifiedOn]
        FROM     [dbo].[Questionnaire]
        ORDER BY [Id] ASC;
    END

DROP TABLE [dbo].[Questionnaire];

EXECUTE sp_rename N'[dbo].[tmp_ms_xx_Questionnaire]', N'Questionnaire';

EXECUTE sp_rename N'[dbo].[tmp_ms_xx_constraint_PK_Questionnaire1]', N'PK_Questionnaire', N'OBJECT';

COMMIT TRANSACTION;

SET TRANSACTION ISOLATION LEVEL READ COMMITTED;


GO
PRINT N'Starting rebuilding table [dbo].[UserAnswer]...';


GO
BEGIN TRANSACTION;

SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;

SET XACT_ABORT ON;

CREATE TABLE [dbo].[tmp_ms_xx_UserAnswer] (
    [Id]             UNIQUEIDENTIFIER DEFAULT newsequentialid() NOT NULL,
    [FormId]         UNIQUEIDENTIFIER NOT NULL,
    [Answer]         NVARCHAR (MAX)   NULL,
    [IsActive]       BIT              DEFAULT 1 NOT NULL,
    [CreatedBy]      UNIQUEIDENTIFIER NULL,
    [CreatedByName]  NVARCHAR (100)   NULL,
    [CreatedOn]      DATETIME2 (7)    DEFAULT getutcdate() NULL,
    [ModifiedBy]     UNIQUEIDENTIFIER NULL,
    [ModifiedByName] NVARCHAR (100)   NULL,
    [ModifiedOn]     DATETIME2 (7)    NULL,
    CONSTRAINT [tmp_ms_xx_constraint_PK_UserAnswer1] PRIMARY KEY CLUSTERED ([Id] ASC)
);

IF EXISTS (SELECT TOP 1 1 
           FROM   [dbo].[UserAnswer])
    BEGIN
        INSERT INTO [dbo].[tmp_ms_xx_UserAnswer] ([Id], [FormId], [Answer], [IsActive], [CreatedBy], [CreatedOn], [ModifiedBy], [ModifiedOn])
        SELECT   [Id],
                 [FormId],
                 [Answer],
                 [IsActive],
                 [CreatedBy],
                 [CreatedOn],
                 [ModifiedBy],
                 [ModifiedOn]
        FROM     [dbo].[UserAnswer]
        ORDER BY [Id] ASC;
    END

DROP TABLE [dbo].[UserAnswer];

EXECUTE sp_rename N'[dbo].[tmp_ms_xx_UserAnswer]', N'UserAnswer';

EXECUTE sp_rename N'[dbo].[tmp_ms_xx_constraint_PK_UserAnswer1]', N'PK_UserAnswer', N'OBJECT';

COMMIT TRANSACTION;

SET TRANSACTION ISOLATION LEVEL READ COMMITTED;


GO
PRINT N'Creating Foreign Key [dbo].[FK_Form_Questionnaire]...';


GO
ALTER TABLE [dbo].[Form] WITH NOCHECK
    ADD CONSTRAINT [FK_Form_Questionnaire] FOREIGN KEY ([QuestionnaireId]) REFERENCES [dbo].[Questionnaire] ([Id]);


GO
PRINT N'Creating Foreign Key [dbo].[FK_Form_Status]...';


GO
ALTER TABLE [dbo].[Form] WITH NOCHECK
    ADD CONSTRAINT [FK_Form_Status] FOREIGN KEY ([Status]) REFERENCES [dbo].[FormStatus] ([Id]);


GO
PRINT N'Creating Foreign Key [dbo].[FK_UserAnswer_Form]...';


GO
ALTER TABLE [dbo].[UserAnswer] WITH NOCHECK
    ADD CONSTRAINT [FK_UserAnswer_Form] FOREIGN KEY ([FormId]) REFERENCES [dbo].[Form] ([Id]);


GO
PRINT N'Creating Foreign Key [dbo].[FK_PartnerReferenceData_Partner]...';


GO
ALTER TABLE [dbo].[PartnerReferenceData] WITH NOCHECK
    ADD CONSTRAINT [FK_PartnerReferenceData_Partner] FOREIGN KEY ([PartnerId]) REFERENCES [dbo].[Partner] ([Id]);


GO
PRINT N'Creating Foreign Key [dbo].[FK_PartnerReviewer_Partner]...';


GO
ALTER TABLE [dbo].[PartnerReviewer] WITH NOCHECK
    ADD CONSTRAINT [FK_PartnerReviewer_Partner] FOREIGN KEY ([PartnerId]) REFERENCES [dbo].[Partner] ([Id]);


GO
PRINT N'Creating Foreign Key [dbo].[FK_PartnerReviewer_PrimaryReviewer]...';


GO
ALTER TABLE [dbo].[PartnerReviewer] WITH NOCHECK
    ADD CONSTRAINT [FK_PartnerReviewer_PrimaryReviewer] FOREIGN KEY ([PrimaryReviewerId]) REFERENCES [dbo].[Partner] ([Id]);


GO
PRINT N'Creating Foreign Key [dbo].[FK_PartnerReviewer_SecondaryReviewer]...';


GO
ALTER TABLE [dbo].[PartnerReviewer] WITH NOCHECK
    ADD CONSTRAINT [FK_PartnerReviewer_SecondaryReviewer] FOREIGN KEY ([SecondaryReviewerId]) REFERENCES [dbo].[Partner] ([Id]);


GO
PRINT N'Creating Foreign Key [dbo].[FK_PartnerReferenceData_Meta]...';


GO
ALTER TABLE [dbo].[PartnerReferenceData] WITH NOCHECK
    ADD CONSTRAINT [FK_PartnerReferenceData_Meta] FOREIGN KEY ([MetaId]) REFERENCES [dbo].[PartnerReferenceDataMeta] ([Id]);


GO
PRINT N'Creating Foreign Key [dbo].[FK_PartnerReferenceDataMetaDetails_Meta]...';


GO
ALTER TABLE [dbo].[PartnerReferenceDataMetaDetails] WITH NOCHECK
    ADD CONSTRAINT [FK_PartnerReferenceDataMetaDetails_Meta] FOREIGN KEY ([MetaId]) REFERENCES [dbo].[PartnerReferenceDataMeta] ([Id]);


GO
PRINT N'Creating Foreign Key [dbo].[FK_PartnerReferenceDataUpload_Meta]...';


GO
ALTER TABLE [dbo].[PartnerReferenceDataUpload] WITH NOCHECK
    ADD CONSTRAINT [FK_PartnerReferenceDataUpload_Meta] FOREIGN KEY ([MetaId]) REFERENCES [dbo].[PartnerReferenceDataMeta] ([Id]);


GO
PRINT N'Creating Foreign Key [dbo].[FK_PartnerReferenceDataUploadDetails_Upload]...';


GO
ALTER TABLE [dbo].[PartnerReferenceDataUploadDetails] WITH NOCHECK
    ADD CONSTRAINT [FK_PartnerReferenceDataUploadDetails_Upload] FOREIGN KEY ([PartnerReferenceDataUploadId]) REFERENCES [dbo].[PartnerReferenceDataUpload] ([Id]);


GO
PRINT N'Creating Foreign Key [dbo].[FK_PartnerReviewerUploadDetails_PartnerReviewerUpload]...';


GO
ALTER TABLE [dbo].[PartnerReviewerUploadDetails] WITH NOCHECK
    ADD CONSTRAINT [FK_PartnerReviewerUploadDetails_PartnerReviewerUpload] FOREIGN KEY ([PartnerReviewerUploadId]) REFERENCES [dbo].[PartnerReviewerUpload] ([Id]);


GO
PRINT N'Creating Foreign Key [dbo].[FK_Questionnaire_Status]...';


GO
ALTER TABLE [dbo].[Questionnaire] WITH NOCHECK
    ADD CONSTRAINT [FK_Questionnaire_Status] FOREIGN KEY ([Status]) REFERENCES [dbo].[QuestionnaireStatus] ([Id]);


GO
MERGE INTO [dbo].[Language] AS Target
USING (VALUES
  (1,'en','English')
 ,(2,'fr','French')
) AS Source ([Id],[Code],[Name])
ON (Target.[Id] = Source.[Id])
WHEN MATCHED AND (Target.[Code] <> Source.[Code] OR Target.[Name] <> Source.[Name]) THEN
	UPDATE SET
		[Code] = Source.[Code], 
		[Name] = Source.[Name]
WHEN NOT MATCHED BY TARGET THEN
	INSERT([Id],[Code],[Name])
	VALUES(Source.[Id],Source.[Code],Source.[Name])
WHEN NOT MATCHED BY SOURCE THEN 
	DELETE;
GO

-- Delete RolePermission records for roles not in the source dataset
--DELETE [dbo].[RolePermission]

-- Delete UserRole records for roles not in the source dataset
--DELETE [dbo].[UserRole]

--MERGE INTO [dbo].[Role] AS Target
--USING (VALUES
--  (15, 'Partner Plans Administrator')
-- ,(3, 'Active Partner')
-- ,(16, 'New Partner')
-- ,(17, 'Partner Plans Executive Leadership')
--) AS Source ([Id],[Name])
--ON (Target.[Id] = Source.[Id])
--WHEN MATCHED AND (Target.[Name] <> Source.[Name]) THEN
--	UPDATE SET
--		[Name] = Source.[Name]
--WHEN NOT MATCHED BY TARGET THEN
--	INSERT([Id],[Name])
--	VALUES(Source.[Id],Source.[Name])
--WHEN NOT MATCHED BY SOURCE THEN
--	DELETE;
--GO


--MERGE INTO [dbo].[Permission] AS Target
--USING (VALUES
--  (6, 'Partner Plans Login')
-- ,(7, 'Track Own Partner Plan')
-- ,(8, 'Track All Partner Plans')
-- ,(9, 'Draft Submit Partner Plan')
-- ,(10, 'Edit Partner Plans Under Review')
-- ,(11, 'Partner Plans Final Submission')
-- ,(12, 'Mid End Year Self Assessment')
-- ,(13, 'Mid End Year Reviewer Assessment')
-- ,(14, 'View Submitted Partner Plans')
-- ,(15, 'Edit Submitted Partner Plans')
-- ,(16, 'Export Plan Data To Excel')
-- ,(17, 'Manage Partner Reviewer Relationships')
-- ,(18, 'Upload KPI Data')
-- ,(19, 'Edit Publish Input Form')
--) AS Source ([Id],[Name])
--ON (Target.[Id] = Source.[Id])
--WHEN MATCHED AND (Target.[Name] <> Source.[Name]) THEN
--	UPDATE SET
--		[Name] = Source.[Name]
--WHEN NOT MATCHED BY TARGET THEN
--	INSERT([Id],[Name])
--	VALUES(Source.[Id],Source.[Name])
--WHEN NOT MATCHED BY SOURCE THEN
--	DELETE;
--GO


---- password = "Password1"
--MERGE INTO [dbo].[User] AS Target
--USING (VALUES
--  ('BFD02B5C-2408-4870-8E1F-06C7A72187F6', 'APP', 'ppadmin',0x999000A49E8DD91ED5726EA0AF1745C8F712640D6563F7FDE1977013B730CB3C9D78F99EC07FC59B0ADEAB5CBA0380DA, 0xE73685F552,0,'PP','Administrator','<EMAIL>',1,1)
--  ,('D3FB7F6A-6487-4190-9D9F-0ECD7A25E239', 'APP','partner1',0x999000A49E8DD91ED5726EA0AF1745C8F712640D6563F7FDE1977013B730CB3C9D78F99EC07FC59B0ADEAB5CBA0380DA, 0xE73685F552,0,'Partner','User','<EMAIL>',1,1)
--  ,('B6BDCA35-4D96-4C26-B6B2-D15E285147DB', 'APP', 'newpartner',0x999000A49E8DD91ED5726EA0AF1745C8F712640D6563F7FDE1977013B730CB3C9D78F99EC07FC59B0ADEAB5CBA0380DA, 0xE73685F552,0,'New','Partner','<EMAIL>',1,1)
--  ,('A1B2C3D4-5E6F-7890-ABCD-EF1234567890', 'APP', 'ppexec',0x999000A49E8DD91ED5726EA0AF1745C8F712640D6563F7FDE1977013B730CB3C9D78F99EC07FC59B0ADEAB5CBA0380DA, 0xE73685F552,0,'PP Executive','Leadership','<EMAIL>',1,1)

--) AS Source ([Id], [AuthProviderId], [Username],[Password],[Salt],[IsTempPasswordEnabled],[FirstName],[LastName],[Email],[LanguageId],[IsActive])
--ON (Target.[Id] = Source.[Id])
--WHEN MATCHED AND (Target.[Username] <> Source.[Username] OR Target.[AuthProviderId]<> Source.[AuthProviderId] OR Target.[Password] <> Source.[Password] OR Target.[Salt] <> Source.[Salt] OR Target.[IsTempPasswordEnabled] <> Source.[IsTempPasswordEnabled] 
--OR Target.[FirstName] <> Source.[FirstName] OR Target.[LastName] <> Source.[LastName] OR Target.[Email] <> Source.[Email] 
--OR Target.[LanguageId] <> Source.[LanguageId] OR Target.[IsActive] <> Source.[IsActive] ) THEN
-- UPDATE SET
-- [Username] = Source.[Username], 
--[Password] = Source.[Password], 
--[Salt] = Source.[Salt], 
--[IsTempPasswordEnabled] = Source.[IsTempPasswordEnabled], 
--[FirstName] = Source.[FirstName], 
--[LastName] = Source.[LastName], 
--[Email] = Source.[Email], 
--[LanguageId] = Source.[LanguageId], 
--[IsActive] = Source.[IsActive],
--[AuthProviderId] = Source.[AuthProviderId]
--WHEN NOT MATCHED BY TARGET THEN
-- INSERT([Id],[AuthProviderId],[Username],[Password],[Salt],[IsTempPasswordEnabled],[FirstName],[LastName],[Email],[LanguageId],[IsActive])
-- VALUES(Source.[Id],Source.[AuthProviderId], Source.[Username],Source.[Password],Source.[Salt],Source.[IsTempPasswordEnabled],Source.[FirstName],Source.[LastName],Source.[Email],Source.[LanguageId],Source.[IsActive]);
---- WHEN NOT MATCHED BY SOURCE THEN 
---- DELETE;
--GO

-- MERGE INTO [dbo].[UserRole] AS Target
--USING (VALUES
--   ('BFD02B5C-2408-4870-8E1F-06C7A72187F6', 15)  -- PP Administrator
--  ,('D3FB7F6A-6487-4190-9D9F-0ECD7A25E239', 3)  -- Active Partner
--  ,('B6BDCA35-4D96-4C26-B6B2-D15E285147DB', 16)  -- New Partner
--  ,('A1B2C3D4-5E6F-7890-ABCD-EF1234567890', 17)  -- PP Executive Leadership
--) AS Source ([UserId],[RoleId])
--ON (Target.[RoleId] = Source.[RoleId] and Target.[UserId] = Source.[UserId])
----WHEN MATCHED AND (Target.[Name] <> Source.[Name]) THEN
----	UPDATE SET
----		[Name] = Source.[Name]		
--WHEN NOT MATCHED BY TARGET THEN
--	INSERT([UserId], [RoleId])
--	VALUES(Source.[UserId], Source.[RoleId]);
----WHEN NOT MATCHED BY SOURCE THEN 
----	DELETE;
--GO

-- FormStatus lookup table seed data
MERGE INTO [dbo].[FormStatus] AS Target
USING (VALUES
  (0, 'Draft', 'Draft', 'Brouillon')
 ,(1, 'Submitted', 'Submitted', 'Soumis')
 ,(2, 'Approved', 'Approved', 'Approuvé')
 ,(3, 'Rejected', 'Rejected', 'Rejeté')
 ,(4, 'Reopened', 'Reopened', 'Rouvert')
 ,(5, 'Closed', 'Closed', 'Fermé')
) AS Source ([Id],[Name],[EnglishDislayName],[FrenchDisplayName])
ON (Target.[Id] = Source.[Id])
WHEN MATCHED AND (Target.[Name] <> Source.[Name] OR Target.[EnglishDislayName] <> Source.[EnglishDislayName] OR Target.[FrenchDisplayName] <> Source.[FrenchDisplayName]) THEN
	UPDATE SET
		[Name] = Source.[Name],
		[EnglishDislayName] = Source.[EnglishDislayName],
		[FrenchDisplayName] = Source.[FrenchDisplayName]
WHEN NOT MATCHED BY TARGET THEN
	INSERT([Id],[Name],[EnglishDislayName],[FrenchDisplayName])
	VALUES(Source.[Id],Source.[Name],Source.[EnglishDislayName],Source.[FrenchDisplayName])
WHEN NOT MATCHED BY SOURCE THEN
	DELETE;
GO

-- QuestionnaireStatus lookup table seed data
MERGE INTO [dbo].[QuestionnaireStatus] AS Target
USING (VALUES
  (0, 'Draft', 'Draft', 'Brouillon')
 ,(1, 'Published', 'Published', 'Publié')
 ,(2, 'Archived', 'Archived', 'Archivé')
) AS Source ([Id],[Name],[EnglishDislayName],[FrenchDisplayName])
ON (Target.[Id] = Source.[Id])
WHEN MATCHED AND (Target.[Name] <> Source.[Name] OR Target.[EnglishDislayName] <> Source.[EnglishDislayName] OR Target.[FrenchDisplayName] <> Source.[FrenchDisplayName]) THEN
	UPDATE SET
		[Name] = Source.[Name],
		[EnglishDislayName] = Source.[EnglishDislayName],
		[FrenchDisplayName] = Source.[FrenchDisplayName]
WHEN NOT MATCHED BY TARGET THEN
	INSERT([Id],[Name],[EnglishDislayName],[FrenchDisplayName])
	VALUES(Source.[Id],Source.[Name],Source.[EnglishDislayName],Source.[FrenchDisplayName])
WHEN NOT MATCHED BY SOURCE THEN
	DELETE;
GO

-- Test data only - Insert notification messages if they don't already exist

-- Check and insert notification messages only if they don't exist
IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'Creating an inclusive environment where everyone can bring their genuine selves to work, participate fully and be positioned for success')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('Creating an inclusive environment where everyone can bring their genuine selves to work, participate fully and be positioned for success');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'Meet your Inclusion, Equity and Diversity Advisory Council')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('Meet your Inclusion, Equity and Diversity Advisory Council');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'During the month of June, Indigenous History Month, we have seen tragic events unfold � the lives of 215 residential school children found in BC and 751 unmarked graves found in Saskatchewan')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('During the month of June, Indigenous History Month, we have seen tragic events unfold � the lives of 215 residential school children found in BC and 751 unmarked graves found in Saskatchewan');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'Include land acknowledgements in your Outlook email signature')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('Include land acknowledgements in your Outlook email signature');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'Pride Season Event: We celebrated the kick-off to Pride Season on Thursday, June 17 with a special virtual Pride webcast. If you missed it, you can watch the webcast recording here')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('Pride Season Event: We celebrated the kick-off to Pride Season on Thursday, June 17 with a special virtual Pride webcast. If you missed it, you can watch the webcast recording here');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'Change your BDO Outlook photo: Let�s add some colour to our conversations')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('Change your BDO Outlook photo: Let�s add some colour to our conversations');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'Include your preferred pronoun in your Outlook email signature')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('Include your preferred pronoun in your Outlook email signature');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'BDO 100 Celebration')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('BDO 100 Celebration');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'The New IE&D Advisory Council')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('The New IE&D Advisory Council');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'Your Firm Engagement HUB')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('Your Firm Engagement HUB');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'NEW AND IMPROVED MY BDO!')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('NEW AND IMPROVED MY BDO!');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'Congratulations - Chris Diepdael, CMC�BC Rising Star Award')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('Congratulations - Chris Diepdael, CMC�BC Rising Star Award');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = '[CAMPAIGN LAUNCH] Selling your business: now live!')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('[CAMPAIGN LAUNCH] Selling your business: now live!');
END

GO
DECLARE @formId  NVARCHAR(255) = 'bdf37b4f-cc42-431a-a587-ac2ced9efc0b';
DECLARE @FormName NVARCHAR(255) = N'Partner Planning Form 2026';
DECLARE @form2025_version INT = 10;
DECLARE @form2025_ack NVARCHAR(MAX) = N'Acknowledgment text for Partner Planning Form 2025';
DECLARE @form2025_generalComments NVARCHAR(MAX) = N'General comments for Partner Planning Form 2025';

-- Combine pages into the final JSON, matching the specified root structure
declare @FinalJson nvarchar(MAX) =
N'H4sIAAAAAAAAA+196ZIcx5Hmfz1FTI8oArvdjT4BAqI4hotUGwmwB2hJKxvKaFlZUVVJZGWU8uhGSSazeYf9ta83T7LuceR91tHM7PQyIwFURUTG4eFH5PeF//M3jLGD0AldfvDi4J925PvcC//KLf9f7NryQ4/77Nq1PHYjhHtwKEuvrDkPDl78F/4DPv/Uf+JPnrXEdrDEqSqtf7h1AmeCz5hZbsDTv5hnm6e94aHluEGmNnf5ErqVemjh0aa19Up1wONuuols/wJuh47wTktK6I5ezXA6lmLK/8X+wL5c+Y4XfllSvEXvG8ZQMZLseEL+OSy2mR3USnXhPf6rqmi+u7WFl9bqW18sofjb5coVa87ry/PPK+GHr4UbLb33qk/ZiiX1/lXW2A4mg+sHv4+WE+43zkfSz/ryPremP3ru+uBF6Ee8w8Rdvdlo2qDaPU5aEE0+cv/WsfkPjtcsRB+jyZEuz2ordJ211g2XTF2h7j3OnytsC/VKZblKLZh9op7fH5qa6zqvZtdDw7Xtlkxr3Jd7nE5QukvLX3/gtw6/a7GJr1V51lih87y1bbhk4gp173M/c1t40y5T+NHU2P0kdmi6bFsXa5dNZP67v/2m4mfzQzz1FV7MWcYPiSVNBLCRHdcJ1/+HvbZWlvy7w7f0WhbhstlpOXt55QUw5fIfZY6GbOXFQbrUC/DhuBVwZsOqhZwJD/4qlksnxA6ymfBZ1vULF1bI7hzXZUG0wsVg3LIXTMzgF6iaGvExexmFC2hgLSI/3egEnjeFJ+kfHN+OlkFoeTYPDhl4U9znQQh/tbwpu8PHQTk24a7Db7l69IQzZ7my7HAWucdmBBH8h32YuBH8T3zmAVYSdywUDNSbY0cujhCKBOkxQj/ffrbArGa+hBqcWbfgsVmgluU8yN76fAbdg64eZ6Ust1E3dT/j+VuXeaA+/3vk+HyqXNBU2ddxx6VPWueOvv284j6Mz/GYnH3fmpVoiwNYj1B6S5+hx1M+3bXbWjEPlbPxs7hF083vfq6vaYb5hs8cz0ERl2JkVjiZqJL9kRu6LVzXWgVlY281A7XzkJ2N8u2dKVkzJQ019a7/OnK/+cn72nW++QuX0s21JMAGiyVBThZ8EcGa+7gnp443lz/fCf8TCo3c5cJDI4Ob3mI27Esv/J///n+w3aIA/KogkK043jQCPbPG7TeFzQudZStfwKaFv7IAVLhUQLLsrQV79pB5ImS/QCVoNVCO2jH7+gn0WPX7R+jGigtcx3T/oVMWW5kd7puqzIW+PAHH9Sj9hXxcMjq2QP2AOmYqlha0pBp1QEPMHN02zsh0GqhO4mjkFMhRQ3XXCeDBluuuj9nNgq8Z+G8i/QSlb+KpcYIg4kHTTMaT9+jmKFhYK9CXauSPCzMCgw4jmA7ZPTWd4B5N8RlhwN0Zk1sABhb6IK3MBSXq4vOkeE+EL523Q8Y9UHS42DPfkabBxd6CRpw4niyCdfRMBqk15VO1AAG3lrLKnQUz8YHfWT7MWbAOQr4MQG06HuhQO544kISZ4+r+SpmQNkOthexLWkZgHLJvWNispn4+POwlFIjsxSG7gzoQGi2Fz5OuYh0fBu3B4sHamg7cLRwwW9KaaLUKg1A1xWqhFjWEwUkhceYLrGm5ziRpAc3YcWoxYPVxWFLcoBOW/4mHLOTeND1dOFmB44oAFsKSmwutEzQZeZ/WSvocNC8MLJtcJrMcxwwX21rJLbSIzeAUe+rZYKnBl4LZc4K4TNy3J3Lnl3lF6lPqZO5de3Glkn+2m1WyeVxswgrKnN2gxLRUhKGULhB92KN/+Ong9KcDnCXYjtZU/sVX/2RBuHY5FLhzpuHixeXJF1Cw5NlfPwkXqsY33wlLLtq3wo7S3yel2QfYTN7c/PZEPexJ8vCJmK5TvZiW9uJKrT9MHfQllJIL1m4ZLaUfdidi/XEUrLjtzBybhT6oN5Q3EI65b8GeBJfGRl03c1QAh7sUtIMFnQSZXK6ZjrLixo6Zfi5oJNgnn0DZhdYnbsGOh23vwIxBpRD0AMihL6L5AvWO9Oc8mJYVeHUwAVqXmF10NMOpgn32yRN3Lp/OceeCC40qKQDlbFsBdlqpaVSkjhfAboQVt6Hx2J64a9TRPuofUHL+HOvobepxPg1gL4RqfqffvEENKFZaoR4yfTqmHU7clvCPVPk/wvPYESh3J0h7sbjTQDnCcCK5wWHHSQdVKgwrQJUY+gL6oTxVVMNye2YiBCgCWma+No/T8tBm8UFTgEZdovIAMfD5gsPM3PIKI2zBEqwDeDx6srhMMAEzLK5mCQYvF0Jaey9n/mHiHd90FURpDlIOFbSGm65hczu2nr4VGtGVcKRfrXuKJseHiEmrJ9MTmDNYv1swVSgvnCereVgiv/G6C3TCcR1Asyq5PcS+cW8O/ZQSrhx1gW4CLgPEOkHI03MxvXUC4adl4hrNEeqFNZv7KmT47enx5QTVtTEecdgSWv6c4y5m1zffH73xocce+5ixGq/VrPZYil6reA+kByZFLJ1/pGco2YxTtVlkX1d42o/yYyI+kKMpGE7ols8xFAyO1fjAILkReiAWdHLuKaHiJYsKm82XP8POhUUDs/eIH8+P0ZKj2yH3JPp1LkaGNpQA3fBYWvG/R/AD/Kk8glCuRrRi9oLbn47QxiolokyvE8AgA6NEsioEbCgYTFgu6cdonQHCBPKqo8NXa7aGfh6BvB0akYa9hj6X8lZwNjLzIFdO6zsu/WmMUMOKSdDdQcE2Ho9xCmRnhJ8oUdSu2+iyP4PDxKpFEf2pJnmUZfYllLFPypVBiacOjRp6rNyao5crvZ2pM5PBeJhx+bW3pebFij10hpESCm0s5PYCvHZYaWzOsmGdJmt9IqE27zG7VkuoVCwIZHqLmFXTKy7DAlRisT4V2DnptLIluOjOEYqhA5LmoU0FGUYBkLGQlCJt7KAz2IWZ4y9lIBB5zt8jro5F5r7ULXGIcr8KrOei8zbA0TsByAz4zp70mS030TBZ5ZXdrsYHgc5MOV8pK6KsXsjthYfxndye6UAyWQYIQFDNSRkF24IHWVOjrbAahs6wvCuYAhfKwY9StrVZRTUA3/8jjsNAs+jnrxMPD1UHyOfSmR6hMmI6smTqIFeKrtZRx+xby8aJxE20hPhy4aYUOkaTsvlYQd5BaMNzetwoL+njmSmUh3Oo2LMOWfAJFGJBl8L6c2+BU2HUWDbiN5Z/Ls2Qr4NrnBcVGZuZQQWZWk7wO62B6T+Wk+InsYv/RAYi39QEZuW//K3k601eFCgnqvFdQek5Y2UluSeh1tPLLxoP6d6tc4fLLdp3gg/6FLT+9YIVheI70IT1pdRJgzpPRY/xX+zf/sC+fHvzw5dyxf8pzwLlmarjwZ6AyAm2zZe7elPT6hBUFio5DdaloId++BfYN+/5nXyrWf8GcWdHllNfrKbirvJVYXYQKRnCGLlloJ8JpxvqtJQLXXrDda9v1F4IPHGqm1pdspXaqn8aNnO1RDXK882oVozaBbsSzKza17pJi3tyI1oMxcMXUPLYFWJaaQ3aj3+KJ2sBCJc8XQPzLVT8F7D/+e//y8A/YHOOEa2cl3P4J5i59GmE9CZjFy9pbQWWzWqzEDe+ZX/Sh3l2FKJVA+sn8I3UXPp08D12A2T5Sw9i5n9wX3yJRvvs5PykWv+zcl2vPtue2m28gdUJVsstnDr5ulLGslX1vezPbiqi/W5+J0C20Nsq9Sl87lr6iCpw5NlhqVuxNI208SmaJfI+jhuae/EruFub7qb9ulpdX3e+c6boESlEQxNoSG0TvSX+Te8JvSUy2wV/C6IJrADo5srdkrhnEGxgLzSwQkZP7V4f64amPLB9ZyVNz4uDI5Zr8AUE2CoKsAIMR/SLd3xBt5riVtAOqjq0lEueTNHxT9UopeaX17rgzryh2vXVRYurrFFXerHBW41WLdWqwWvlZrRJq7YYbotBm750sSO6TtMcvIwFoVVzldPRsZ1yc2PwRHoTldihRylDBKE5BLJLX1YCC/clhv5Vv8P2e9yqZ51slq4D3pMD+0f4LRZafVost25bLzrsLF8dILQZhamLwLQXB3/KbG5lIvUQ27eV6kAWClMlT0f6ef9ClAFfrsJ6SxF/KgxD+vO3NouC53uvVRdegqfbfjV1v28yk/e6PnTPNtDWkdHl33628ZWZVPW28pCbXWBd9x3n4WY1cd0chFGwzdt4I8DVfi9C2US2hRar2FCkVhD256ZvZlmM6trAtJiqHW1Lt3MIXamPBik3dRtapOIs7sQkae1GFilpu/cWqVKgyCSRSar97Msk9SvWxU3x1ptuEOuCDsO3XtybVuq4zO9tgl7szRF0Z2dBb65BCnqVCdOrvknQm5vR4Qa9eg62DHrj6dh/0EsOwh5D1oI0kH9A/kHt50GGrBsYhjjY6mYZHlzIup09Kc7i3kNWsif7DDjJoCQNkEEZSMCZ/y7H0941zfaspFAVzfasA832DecrjX5hf0qzKfrPtT3ryrX9URd/qEzbs22YtnPBJnwtMqxTw6lNET7FivuahaUZpgvQ3qHm1Ts+W4rQuTV8F9AR4k5SHmRx23A5Yjag4THEfMIMbRS6hYBgRa7XgHV2h8QvR3NFk2p/QbhzEHLJSYjB7VMuEc+WbQsfh4P8KxiiITna0EtZKURaLDSxsG4l1wSxxFAq5mbD8AyZ5Prm+wRapvuPIC3LtxelvV8ofNodx7YnPLzj2EnEawtXzFWHFtHScNAUBE6RVe+stULdI8ZEP2vGucuOkEOK5DYJx/EMVFliKRS5CiZMXg0wERESAxAvriE+MOaFs4qx3ctj9pP3BiZkvggTOloQc+PUsqYYn2oVA8VXUhA+UEIa4CIZrqrPEhhu+J8gN3JlRIgM1Zj0escNwjo/dYqRJRcnXsZ42qVA3CnZkz2IxRIR6oaTAdUQ4CZJxIrEZ8P/fMfGfmoyNQ88kGuUQHiUYTtrxhj0d4ok4T7zU8824KeW8ktPB8st9aYRaBJNtIINnuyT6cSyP6UYBIb6meMUolxmifBZgV/Fl2kgmzCj36A4vxXuLW4cReA0zCt1ywcSGU0/4CkSVyenIEsYUtI5VchUR6FF1eGqBIpmieQp7oXkacj25NgyelIxFZYiFmvHTysYPR1RhnLaFag7HNqNXhVQNF7ozNbq/hg9ID2/MHQb+ahSM2iaCCg2qXqQdWWUj6ag6NqOt4rCeNXl7RNGsyY85QwvLkf8koSfjGxI7l8iBPijz5e4LoZkrwRP8lY0BSdto9NmUfdrCbKmiIAxcNPo95lAI32EpEQ1Ik1DRBnZkWz0jVN6Ze5TmONFHKAy1GsXRVTK0ofV6BT3I83EU6xS1CQ5Op2RMMnlEuhaQKRkbtMQPlgqZC5FnrpGYsJnQi0LWk4f+X3HoDn99GVFQaKodMclZS9L14w8LkMoPj0sub1EcTMVETDFSgVNIhXOjDnau4vCQPGcUUyWYNXRXwtsMK/m5qSEcnpsFjRyp4ZKm/AOJyB/CzSkOBEQB1uHMYc3plYfYr8sNgFhnR0qDpmDLSPxEF2zyNXAd63CUUgW3F2lbzYxdHp0Bw2jHjUxkQ5LbKX2v1F5fOZ2JH1xZHhqv2nKfjr4CNrOkhe1vBPY/Z8O5As+aS+RZRcL+lzgHOmYQVpOyy/eeiJ9PiWuoISWwkef41AZpyNYO8MT1eoqI1FoWMHHE0Go6Kl4L6nAi1wMUfFQO4/qZ0VBTSRQkyaTmyWEtoLm5gEpfrDrVr4TZNWocSDDBapIvL2LwaYPYyr1FEbiqYUIjHOdC5tMPJS97kb4+soxvIQFFQxTFwy4Mjgw2xxdfGxkDpIJPUWfxsJx40JID0WkbhKQ3Va+uH7vm3bzH4x1HzAdMXUe86vREZMzb7YLpkrhsPs+MRmy0NMHwzQ8a8E03OgV0H3SE3ciVbpV4icSP/Hh8xPP7oOfOKztTATFql4QQXHDd0S9JSh2ePGmWyOWYrpocamJpZifA2Ip5npGEJ3ytqogOpXyRAgdQujUfh4S5POMWIobGyRiKdZ3jCxSeVuVFolYisUGyCQNBDR6DwFv/1mK20e+RFUsjfrGTFU8I6pirs6ovQSiKhYaICdhtHErURUb6jROHVEVx21PiKpYbIAMykCizvx3e6YqnnegKp53oCq+TicC7D898ZzoiVUT0pWemFl5Cb1gNgJpMd1J0r7JySizecR0ORGFoOo0AlfyMUKduyYmIcbJChFXW8jamEtjKfPGKboZDwxOOE4qCcYa2WgzDX5KZV/JZEyBfnqG9iPR6myOOibyNJod0VMqhyfmYMQ0JQvHk8AohGL71hxXkU+z+YTyDDoDN/4HUvZEEByl85NI1h5+maQ/hTFI+orsHmLnFMXTj9x1Oq1PnB/lOJ/gU6OXU8wphCbLTDMhplWTmGiJXUkvp+GhJPm9DssSESVpWWT/JAvSFgsu6Y44HExEuS7LXKNy03w2/Kx8r5PO3PK0NCHRIEDiagpqnUZWB4ewXjaKNLR8l+N14EETAv/CxaHOHKrhOCZHZ5LetAgvVOBFsbbcMCt/r9aMLyd8KhHm2VlECLpaJyGFW5OZuCGwyNyrMvNozH3NU2/1zFoyKamS58M4LdWhzk+KjA4Ja+SzmUoL1ev0necb0CM3imEeBqfyByiNKkZlKXZWEkbnpciKKa4TitgvIAJabAxdIyuWllQyaj/j8HIK67WQtGx8jCZjsV8wJ6MkxcnsVksukbFa0yaJPw2PTu73FBNbTH5RYhlooreh2RldUatcjsHFU0S5ILI1GVBrpxypVCatNGRCzPak9vws8iXBWdH3cABGc2LrRElKRO3bJL9njkFnUhemEqWBN6fHWbBk0h7LzKKKxGOybddl3ksSxNoLPo3cJP9YVnpj5nAq8ZrJS2aSM2YZf6VJXQ/TopiWiZQFz6UKLdCMFL9phinLUnmTHzA5U49A7ft8RkUkTWduEkgyKsZ3TGSzzZo5MMnO4jR7SJ5MeG2agSZ/VYThmNe4inTyaktSR7UiMpKo1A04mzLnHl75cMclidiYWytlVO1Y7SUCUELaNcnt0IsyLOGEo3YoXYn42gY0+poBF/vDIpG0okOqfAXoFl6Lge6pMeiJNnwo3LE28ib8ueWhr2yYI/DY5E6ByryEJhWgzih9mFYVsKclazhnLlUiw6ymuYNhyjzVwlieWArAsDoh75JUVMuNXhtrJXmLE9Cn8Auujq2KTbhKkD2VXJEjmBNYV/R6NQEj5fdmneQJX1i3EFsExryjUOr5Kxi6B5LF2lwzodyT+NoXGa9nfZTMzQ5lSqois3Xu5giZsjKVHDh2NkxjcwtDEGUPA44SnI5AdNghHRMlcKXpXePQV3FuIfxUnKECQRVG7QuYAZ6lnUPkc5iTDtO/OK/sKgk2JukUxbF7E+c5nluKOBQnFg24nuNgbL7TgOmuqTM9orvu4EhRFvrqwdBdz3uWWJOYq5kWiblKzNVs6eIOJuZqviQxV6t6QczVDV8f9pa52vAeVrdAbNV00eLyEls1PwfEVs31jFBa5W1VobQq5YlAWgTSqv08JNTvObFVNzZIxFat7xhZpPK2Ki0SsVWLDZBJGghu+B6C3P6zVTeLdomhWhrpjZmhek4M1VydUXsGxFAtNECOwWhjVWKoNtRpnDpiqI7bnhBDtdgAGZSBRJr57/bMUL3owFC96MBQfWnbYCkUsB8zdnBk0oSL/nNVL4irmp+QHzfkqv5Fwf4TblCKhZTKB+UhHnymNDmmmZrwMOQY+lueZC4UcmcuQDjZWkTeXKOYJNhYkk4LCXQkjFgmSorwHwqkzoIwmsbnDkmdqWw6YK4QEsAkk8ywmRWEWnyzvNKF46bJCmxpYccDSS1UrIXIX4mAKzqmTtyoN0ORI6lwazg+xP6m21UdkEk5ZbPq4ARnDTPwISg6RqFNBIwumdvs8DK9T2ZHb/f05Oq+3C2E5EDGoJRDzeRkAluUGbYwPWiC1tNJQwMB7ofKAMfDhWOXUE2XKw47CJHzJi9mnFJMZvWBybRV6riVpaDWd9DQwiT7lNzCOKGS7q+eW93HuYTgIRlEfxH6FtIOhK9E0TJcUE2jRWAlzuESpksl8+wznfPiZ61rfm6ha3I7NMPM/Ong9KcDGB6W+Bp00RwDD/1v810pHSBbpPyfXz/JtRhzQOPCEuSd7iayOas5oPmSZXzQfJkqbmhc5knciYQnqipr6Hldb6ffxJkFvRDPLO8E+yWSuMucYlunknRp/onlwsZRBD5cE2sqCQOp5G5L5lm3zhwdPs3hTqxpir2H7BXYyZZ7BLvOnbLAhmjEd4RScTLFrrwjICYYwCP0/QC63RRVaml94pgnEWmnmPDLFpgrdqJJ51zmCYwTaar+ILUC1eFMoK5SpGxvhqrfVvg1UKhTzDSWVExPjrELMpmexv/nZ7kLfSZf9x6pNCVC1Sg5JjdzwrdKVixaBZ+gjCJLQlcVtQQtBpLejhJlL+mfS2TF45ykrljga2VS5PUDaOWSxmMyl0qyrIiC8tKF0FnymQ9aJ+HgWY4+kJc2NBZgZM64CHFWiWBl1mMrMCQvhHlG+qoAfCmhE7zOLJmuM0fUkUJpsnOulfdgci+6kqID/7+LMypL27MPcfn1SC+bSI/KcIl7Mu1paW2U4WDlJOYO0xoa2l6euaYIeHJ99IZN7VH48U7lOJR6Ts8m5tKWUPPyJLDLFCk1TmCYbhSZnebqBZ3LMqPzZBpSk+mQpySw6E3J92RKoUl+ulwBnaU4JWGoNdH9Wa1SF2Wk9kdCEU0JDQ5T7snk2EK5imp/SR1mJgA6LPAHgRQAWUrSaCURN6Eg6iWIPTqltC03xaxLbrEwxkHnxdXpdTMYfrCHam2tMFkimOegarPcE3mgd9utyzZLZFlmpE2p2wwpEbmAJtHmYUpGl2AegxB/lQKud66JknOCrhzWR/x4fixdbJ3XU9NYNMvyEAy1sbZpiyFlU2b0VPe96JyhNkryTCvqxylqtdpNSowC8JdtfWPEPHKm0kFA6r7yys3Ey4lNRpRSIfim+VBd/pNxc1AK5OSlHYHlukDh36Uav38qwhZaPMPy97ntR7qrqIcX4s5GQhBGM1b50YrhiuZvKAFHOgpU1mN9vRGEePKChlvFtf0ofUdzywcsqO9oAQgtb45oA6ZvjpG5npHQz+FfYs3hCxXwGccClKH9yV2n5UFSmHBiwxBFAW36FI+mUetVhZdHSXgpFbY1V3us1t6XkKZ66QwOmc+aOgEkPusOjh1lodOTB0NovSBCKyNCa8lQiNBa+ukfofWCCK35kkRoreoFEVo3fMfYW0Jr69e2ui2itqaLFheaqK35OSBqa65nBO8qb6sK3lUpT4TuInRX7echwYUviNq6sUEaBrWVrBEbgjUiWmuxATJHAwEb30Oo239a67YxLxFcS+O9MRNcL4jgmqszah+BCK6FBshFGG3ESgTXhjqNU0cE13HbEyK4FhsggzKQmDP/3Z4JrpcdCK6XXQiuV+wqyWfWf17rJfFaqyZkA17rJ85Xipgo4bpCZkO1FzHU6xhMq4S/us4nBZt952B+GjELD9l3r68ViEumxAkMfJa9vJLUUYkiUchfCcnW6bVA4FIEg4BJ+ITEU8mUn5i/K5V5FCkUqoMqJSF8s5AZDWcqb5bmX8pEfyluJoq0H2dF1RgzncVGJRpEwK7jhc5SUXJs4XmggPhU0lMn3OMzRycHCziXaQsthKhjk+neOx4OKAabBdEExMaT1KYjhbRPRmPIQkkHdCYxw9GNB62YHmb2VPpP3adjhrxTx4xPovenHGHMAfZE0p/uLJ05F4lHmqiCKC9borzMFIONStooaxapFr4150oCJBpfdi2wLVemfi1pJZuvFp8/iRx3Kou+vPpPxYySFEHVaRzhIXQOHqNk6cOPVwlDWNNwFXLwCGcZuYFTSe6zpmKl6K04NDP0lUxpJOcfZqEk1S8INyaaDdgiWlpe8ihwNJgrMEMT8gqNfCEkUU1lOr2wgv2n6MqWPHBL5nzCsa1QM3Hgmce95uBebpBSlTi4e+XgBivMOh1zIE5P2AIkMFCUHZ3ETvEIJFci5sdjx/AbKXmwKUIhJL3LJFkVyFXB2EbfK6CTIWKiakTzhpaiuygWzww0R+TLzGMZaqSE/zowDYYOqTaLetaV3AGKqKkTrgr26N1HmP+V4wrY7a9hm3x3fcPe4tZ/9eZH9krAZjlkV+8/Xn33x5uPlvOYBUI3ZOi3oeTq2sJfCcMzWqp8hzAgqWglWVCAStW9R7VgErOhbho7MSfDqvTEUiXpzeR+S3PB1Kk+Z99GKAOvfWsmbbeE7GFWxJitqQiaWmqWJr2uiHSWS8X305OpDIoqqChot5pbDTKEMiGthXmlIGVQZ+NkujvKzJiEsikKpLQVRfJXTP0UmnarmjVbRxdaKHdAc8pjAuXEF7j8al5STHOhs4zr7Jovr5JXHw6vJCc+YOI3cqI+ySkzD1IqAfcg/DVWRDKP5mRt9qTURzo9Myg1Sa7TdFDlGMTmHH9BnYLJeVWBabJeIBp6ZWJnD8QPpglk4Im5OMSRN4cYydFs1GTxVWrCuMkkT2iaE2lyQRd5ulq0cRe8vNqe2jUs4naSLVLHEfL6Fiftg8duYpkrnMrxm2Z5J5xTkJ/Efy3kUUYmXyxhimuvGScp6Tk0AnYIdW0HT6OOltYnST+NXe9YbHIXUijhSLqQSpKqeIE6pTgHD1F4508vlbKwQU+gusM8rMHKlzcioNdqmNElicTHIjKlNGOZ3DQO7dSKpO4PUklPXyq/wNB9Mdvx3JN8YBxoWnmYGxww6621dB1PXeexXEae9mUOtd7QOU0DvMJDXxsgpSLJ+Q5GZQUCx+V9Eol4FCS/XOhjMZ8YO8kg2gSziAlbY3t1qL0xpUS0PMeuHbL35c0SSWRopbLcv0rnYY0vclGqFdvwuTSDwitoafQC09MmLxCSl1p4cmtBHIzikdewacqW0qNxcmGu4j3l/8CsLZxVE2G/YxLqXgv/kJmxqaNDYsbu4OBSFjp7MMTYyxbE2I3eVhKblti0xKZVn/6xaS+JTZsvSWzaql4Qm3bDN5v9ZdM2vCPWTRCJNl20uL5Eos3PAZFocz0jCFl5W1UQskp5IgQZIchqPw8JknxJJNqNDdIwSLRkkUzbvbdIRKQtNkAmaSCg5nuIcgdApN0s3CX+bGmoN2b+7CXxZ3N1Ru0aEH+20AB5BqMNVok/21CnceqIPztue0L82WIDZFAGEmrmv8vxZ39T+CG2VqkdnMCs5vz8ND11sbbLws+uNcqTfX99xW4kmCVgR+yj5WawJ4XgDgtgQZhwzfNSmOAlD33Hlu+4ucKU/OjxjxLPyRxvFWFikteuY38yyEvX8T7hS2/JVJBUM/iHRqOvJa9AdZeB6FsK1KO/WDkrDrX5MfuvH3Qj1wJ2/6srsIII7gxS+NGkFwr4Cd/97dEiDFfBiydPLJiAdQjdPrZWq+PJVBzb1pPH2ZCz0v5tSmX+tHJOS37NcpixkFqVzKohh1lG+vi7tjNaKwSNDGcw+vHy/QhTjrwX4ZdUKKx5tJTonTvhfZmD/Dsee/3hnUYkm/gMtLvrBAiBskx6QPaliJ/4JZs53J2WRfb9IFknk39dW7wN/nNnZw8NCNaqEXyncz61dCxVJday1pTPrMgN/ywRay8OXhlwnNy/hmaMWzXO2KoVjUS4ODI1kW1UxyGzME2SHKbMTqW6godC5ZKLonV2cnaJ8vbbs5PDkxP5X0OfkX3wo+eu22CrltbqW1BlMLS3QYguNvsLjC7Th3im9hZcdF74nF7oeNbUstp+YGctEMrq04ezXFmoJN+6LvVrQZqlx91aWP4aTq/R6opGlHHFSXQabpU9g24GW+mWtwJK/3Olu68s5F9v3tyrAmjaJp/RWr4WbrT03qtZr2xpbxqkm0QU3I6WCiTrX6pWBoB0XTrg5Jw0icHngxfP9aepaPDpRituOR92A1JU1vjIQ/RMYfM3ReUHMLCZA905+G1teFMjM6xcLjvKZA8UMEprb7Gibb1t3dTuMaMQ2PXr7Vk3NaQXFuxTS/0Tz1izCcj5rf/+/snLTkajDwfJKU9vfEDa4uAJQZvrGR0il7eVPUSuEyQ6PKbD49rP8N9GovgTZra77SGwbH3HyPiUt1U0PoSSLTZA1mcgry73Gdj3Hx67ZYS/PUx24BG+XuH2EX48Y6OL8MeHHS4OnkDDI3WRCC1caIA8pPHF5wQTbqhTPWeEDx6p6SBgcLEBsh0Dia7z3+0lsQ7skpJbGYto1LMGNOrZNmjUHyD22gMOFYeYAqMaJCpeQqsxqArBjDcbe8I78jkOz/Hm/YWmykKnZ9UXafYak3r2QDGpKL8ZNOrp5T2iUY24y170D4+a1wyER92vbighF+hyg0CknvUBkboxuHTbc8ftNcC2gNRMW3tTIt1EgiCpxTLjgqS2lMoeaGGU10GAUuuUBcFRZcnikhIcNV+06OiNEo56RnDU2p7RmWV5W4Uzy0pBoiNLOrKs/TyI111nBEftbnsIjlrfMTI+5W0VjQ/BUYsNkPUZyAuzfYb0w4KjdortOwJRH1xoTzjUiqLF6HaUONQzwqHm6ozTNyIcaqEBco3GF5gTDrWhTvWcEQ51pKaDcKjFBsh2DCSszn+3LxzqeRsc6nkDDvV8GxzqDbeWe8Chlt6HivmqHdtZDeVG1PPB34h6/kDRpyi12btQn90j+lQ+vX+o07weINTp/lTDz0ow64sPAnx6TuDTyrJ1CmBb0KlsY2+6o5sIENi0WGZcYNMGaeyH0j0fBMj0OuXlEti0rQoisGlF0aJ/N0qw6TmBTWt7RgeT5W0VDiYrBYnOJelcsvbzIN5pnRPYtLvt6TXYNAmGKt5zkfHpj/EhsGmxAbI+A3krts/Qflhg041i/LHffmrWmFCn+aLFMHeUqNNzQp3m6ozTSSLUaaEB8pHGF6ET6rShTvWcEep0pKaDUKfFBsh2DCS+zn+3L9RpSQLnIur0ogF1erEV6lSEVlkfK2CnSaqRQ8nzPJSaVMLw4lZ7jSS9GDyS9OKhIklREjNQ0qdnhxd7hJImD+8fgjS/pwlBej8qofpu40FASC8GDSHdchtvhgNNqu9t83dbwnuEgKbV/NuCB190O/53xWXs8vvT4veEQB0OArXNPuiJvh4E+DTxlOVT6MLTLjrwvjCoRaWXPFlru/wXp6kvhvhe62LM2NULwq7W9oyON8vbKhxvVgoSnW7S6Wbt50G8Gbsg7Gp320PY1fqOkfEpb6tofAi7WmyArM9A3q3t82RgWNjVbY4Ixg5hNUu9dwhr8YggebI+Ish/cZr6YshHBKOEvl4Q9DVXZ5w+FkFfCw2QizW+AJ+grw11queMoK8jNR0EfS02QLZjIOF5/rt9QV8v20BfLxugr5ddoa8fOLYFwX0VZKgIfJXB+swXSya8ucCqtuugurCFF/qWDXICvfH5ilt4y+okCsB4BRujYGuxp/eDkb0cPEb28gFhZHMim0XI7hEem39u/0Cy+d1PINn70QkDB8leDhoke1/3rDZayky9yktWs43sTXV0EwC6ZbVYZgwY1/bi2BOl21uka0vtQMhWWbK4onS7ar5o0a0bJUL1khCqtT2jU8jytgqnkJWCRIeQdAhZ+3kQL7AuCaHa3fYQQrW+Y2R8ytsqGh9CqBYbIOszkFdg+4zo+49Q3TS0Hzsi1SwtXaqaL1qMbkeJLL0kZGmuzjh9I0KWFhog12h8gTkhSxvqVM8ZIUtHajoIWVpsgGzHQMLq/Hf7QpY+bYMsfdqALH3aFVn6WriuNRE+rMgtb40uvea+Dc1bc463q4KAsECC7R6J1EWrbnzRasit5WMmojDAwFoiDadOYDsriTN85Bzz4+yv1++uH/f6Ptang8eaPr1/rOnNgrO5sNzk6ARPftDKhII5ng0aONACYGfEUgmXjzp2smaXXxwzBm35HNrhCEENovmcB2ECQIUmT6onXfduw3sbr9l3ChgLXQE57SHANK8ECGB6P4pg4ADTpwQwrSxbvf+7I0wLLexNcXRb/t7CS7sjN/cESO2GBwW3SNy953Npw6RcBm1CeKwKXbIdFUHVA2Blt5pxsqr3nw9enJ40F4xxrF/s+q7WvOnUstJ7Hd9bPGsr7123Q5hWVrqqhGnNFy16kqPEtD4lTGttz+j8s7ytwvlnpSDR8Scdf9Z+HsSrs6eEae1uewjTWt8xMj7lbRWND2Faiw2Q9RnIy7d9RvX9x7RuE96PHddqlpdwrfmixQh3lLjWp4RrzdUZp39EuNZCA+QejS84J1xrQ53qOSNc60hNB+Faiw2Q7RhIaJ3/Lodr/U3hh9gwpbau0Yora87PM/inCnjINfcD4VkuRpf6/T/mFIGdxL0ogyesVPlbYGyftcHYPmvA2D7rirF9D/IB0fc0slE8zFgNCOLRFxoVUXLw2Ru867PB412f/Tp415njL9HeTKIpNIT0ZF8vv75e1QrZs+PTL8z9qMeIbGWv9G287KO6kDVgv2M/RmEgIt/GNh69+vjj4+xtrQ3PuDz+KnnGDoFx8ABYy6eboud6iJ7Nb+/9oGf7cMGiXru+H9Q+ezgQ22cEsa0sOwKIbdGzIIhtsR8EsTWf8UBsn/UWYru5864bJ9wtK11qwt3mixZ90FHibp8R7ra2Z3Q+W95W4Xy2UpDoeJaOZ2s/D+LV3jPC3Xa3PYS7re8YGZ/ytorGh3C3xQbI+gzk5eA+Q/3+4253HvOPHYxr1pzAuPmixbB3lGDcZwTGzdUZp9NEYNxCA+QzjS9iJzBuQ53qOSMw7khNB4Fxiw2Q7RhIvJ3/bl+XzH7VBgD7VQMA9qudAmBL6uXD5xsRWi5bJS0YfCOGx3j7LPz0C6x4wO7wWlBm4gC8WdSaz/kU4mwIo7kK3/ln2z1mL6dLx2Pfch70GXf71eBxt1/dP+72lRXAkoOYyKtkMyhZDYgNNfAcrfLCCcBUOjZb8hD+CPBCWZ+rYU6TK2Wbz4U0uhal7rfnhycnJ/jffgBy2X70FkqbVxT7gdLSRbR5TTFwlOxXhJKtLNugBDaAypY2szcV0k0QeouX3RP6tRlmqjCmz/WnqWhXUG9H+G0MY/3trmGspQZuIFjWr4aJZSUAa1utRADWiqJFz2+UANavCMBa2zM60yxvq3CmWSlIdKRJR5q1nwfxOuwrArB2tz0EYK3vGBmf8raKxocArMUGyPoM5IXaPuP7gQNYCbXa+hyaUKvlRYux7ihRq18RajVXZ5yeEqFWCw2QozS+MJ1Qqw11queMUKsjNR2EWi02QLZjIEF2/rvtr5A9T09dtytk38HfnMzG3scNss/bAGifNwBon3cF0KqxsRJ4Z/HtvCr6qOII4Ai2YhAGjxERa7EV923oAMw8EzN5amDKSZRsNMXLRE18YsuKPQbLPh88WPZ5Xy6pXSopSu6PfXp8vuM7aguPuDg+6cMVtWav9RBPm9cbhKe9H2UycDztc8LTVpYt7vvuENq45t4URbfl7i1qlm6ZrS08mFtmYxM5EETu894icpuUDoFvZcniUhL4Nl+06CaOEnz7nMC3tT2jo9nytgpHs5WCRCezdDJb+3kQb/WeE/i2u+0h8G19x8j4lLdVND4Evi02QNZnIO8F9xnK9x982zmmHzvO1qwp4WzzRYth7Shxts8JZ5urM06niHC2hQbIJxpfRE4424Y61XNGONuRmg7C2RYbINsxkHg6/92+boc9LbmusohuPT1pgLeenmyIb/1tC3xr/R2crgQkhqBPLX8qoa7s0dsCkvV135GsZooHDGU1Q3gQF78a+czc8Xq61zte40f2D4Za2N+EQ70nVTBwIOrpCSFRK8sWt/7GSNSa6zjvFYla4ikQFLXYj/uBonZBlz5PPo0V9nYJbGwBB4IyPT3pO8y0UqMQzFSWLFlLwpnmi5b4gaMEmuZHT0jTXM/ocLK8rcLhZLUk0eEkHU7Wfh7Ei63TE8KabmB+CGxa3zGyP+VtldgfQpsWGyADNJC3Y3sN6QcDN20f248dbhovKuFN80VLottRAk7zoyfE6Vg9I4KcFhogx2iEkTlhThvq1EwagU7Haj0IdVpsgMzHQOLq/Hfb3+56kZ66bre7GsjnrRNmgBr7uOP19LQVDPa0CQZ72hUGmx4kjPlGhDAPrxzXRT3O/igiv0T4CvG9qiZLy8p8KtGxqi99Br2eDh/0evqAQK8tpDGNhz09vDw5YYsyEc10fzNMnPFagnwPegiPze97gsfek9IYOjz2lOCxlWWbVUF3uGxlS3vTJR3lobfw2f2BYbuBch/ATa6tQLZBNFMg20WddLJNYLZtzPxQELinvUXgbuTa63YJnMvKl5nAufmiJV7oOMG5pwTOre0ZHeKWt1U8xK2UJDrDpTPc2s/DeAV4SuDcDcwPgXPrO0b2p7ytEvtD4NxiA2SABvISca/Rfv/BubsM+0eP2zXrTbjdfNGSwHecuN1Twu3m6ozUaSLcbqEB8plGGLQTbrehTs2kEW53rNaDcLvFBsh8DCTkzn+3PW7XFn4GlNgNuvsaasdXr1YtSPGeV9hfELVaIfyPBzwBMUJjsF+PVmIVuSDEUzapBkBaga6NtSLPgUh4yXiqC0zMGIgLW6nuBZm883vBFpdg1EqwxWdN2OKzrtjil7fch7Vkf7m6Zi/njjdvgSQ2dfA/mCisGk+2bbk2LgCUfcEeLYUXLo64h6vhIhyT/a/zp5ePn5yeHcmfmAdbyVd39fYZgnw2fAjy2f1DkK+k6VX7CjHEgi1RdWY27nFDk5thAWNpZm+sdS/BwPmNSmDge9q+QwcDnxEYuLJslQLoDgHO1d+b3ui49r0F/qYgul605L5j/yqa6VfB/Z42Ft7wjt268t0BvYlIDwW2e9Zb2G4Lr1m3QiBdVr6oBNLNFy3xDscJ0j0jkG5tz+jEtryt4oltpSTRgS0d2NZ+Hsb7vjMC6W5gfgikW98xsj/lbZXYHwLpFhsgAzSQN4Z7je37D9LdPMgfPSTXrC5BcvNFS8LccUJyzwiSm6szUheJILmFBshDGmGITpDchjo1k0aQ3LFaD4LkFhsg8zGQADv/XQ6Sm52FbdCl563QpedN6NLzTdGlLz9sCi6Fmr8atrQW0HlPyNPz4SNPz8eEPDWS3lvgaX4PE/D0nnbv0IGn5wQ8rSxbsf+7406z1femNTquPMFOm7tBsNMGiR4K6vS896jTam9aN0KgU1a+pgQ6zRct8QzHCTo9J9Bpbc/oTLK8reKZZKUk0ZEkHUnWfh7GG61zAp1uYH4IdFrfMbI/5W2V2B8CnRYbIAM0kHdiew3thwM67Rzjjx5zahaXMKf5oiVR7jgxp+eEOc3VGamHRJjTQgPkII0wQifMaUOdmkkjzOlYrQdhTosNkPkYSHyd/25vmNOLVpjTiybM6UVXzOkra8re8EkZRK0QNMdl2WTNXn+4xpthLbbivg2P0ghU/PpD/68ovRg+UPRiTEBRI3rs0ReP+4gTze87wone0+YdOk70gnCilWXLt393mGim9t50Rsd1J5RoczcIJdog0ENBiV70FiX6p9BxnX/IkIR9QOe07Ruk0aNELwglWlG0xC8cJ0r0glCitT2jU8TytoqniJWSRIeIdIhY+3kY76AuCCW6gfkhlGh9x8j+lLdVYn8IJVpsgAzQQN5i7TW07z9KdOMYf/QoUbO4hBLNFy2JcseJEr0glGiuzkg9JEKJFhogB2mEETqhRBvq1EwaoUTHaj0IJVpsgMzHQOLr/Hc5lOhvCj/Etim1d41eXFlznkGDJtg9bi88x7Zc9s4KQu6vM6WqFPumqFTHm0ZB6K+/FXYU/FxV2PTt/OVjdqVrMFmlEVB65UHhyJbr/OIn79rlVsBZAOOwQ7YWkZ80+M76BSJqVMPJV44n/GPGvud8xRyPLaG/EHNbEo7IZo6//DJgegwOiJeYQVQO3WKWz1/APvQCBLewV1EAljMIfs++dTzLs0GU2Ufu3+LO+z0814tmlh1GvuPNfw9WGX59K4N29jvZhhnA75lcHOGK+foQxHjqWIdQ5AYHA1s+wmWTA/29HMW179xiI29BdYbrkpnqBVDWSICc/SoJyMmBWqkKAagQg/Qiv0hW+G7BfY6CADIh//RBNmYsWHFvilekWauVLz47S5gp0D7PLr/ANZZyEzpLfsz+CjXvRORO2YTDX/xPWCkUd5Y/Ratl2Qv8QlaYC1hYECJYK/kdGKFqIOuvcGrTwReVRS8byrZANncYQouB6Na6wGt1lTJZbIN6M0+sh8118g13CG/TbacdRFZ7pqU+9VAxdq+L0A6zbJ65EeBZV+7VCvQ+GJVFS64wz5QdduiYkcOP0mFoGcxsNHDT4VJVEpurbv3YJDTsGhC8nPuOHbngvTTwPJIqBb+obcWi99Q69Ej7WG0rvbegPDzsAw/Afnd4GkQZRzPhH137YuaE4KK9nUbKNWsd8DT4blW1xEyFvBtM0XU0cR0bqtmhaMCJJ5WqndW2LRRcWqltiz7tLoK0HRq25ldcunyZNvkjupmdTJp0O6/fXTNZFb3ILI2iVWMt32ypT/9tUGZKW2G/df39IMDNOGpw4NkopPHN5a6x4M5yBSqQOSFf1rwv1o9vG3/o4nsx2y2EQVcoE4nOqGjz8G2w0bqN9tOhPp2PV7t4NLpm8xx1O9A2HdoNglq31lscte7fBi6Vrtn9zF19WouGfs4W5++mhd2dwusWs2fxrSRw0yN59WlxMK8+bY7n9SA2PaTX1bc9qjfNdPPPda0tju11CxsfvOv6OzjC1y1tdZCvPk2eovq0ebtzjy7ndtawO7bbPH0rhLduZIuIeCimdCscuOnRjtDgurm+YsJ198iW7tiW7hw/rj5kTLO1yJhmPvdnTId1JNEKs67r7xm5bgZUg1/f6mxiewz7OM8m2sP4zMO3wXTrNobiUG2D+TYd2g3yW7e2J/y3bp28of2cLOwM7Kc+5Axla5EzlPmM+GRhY2O2GTJdNzKik4VdWMJtUey6uf1g2XXjZAn3dC5AprBtM2QKh2QKNzwXKP/lXsnt8TZFoHlb9DOW3RD9jFU3Rj+fEfo5AT8+f7joZxSS3aGfmxvoFfS2J+BnXAMCPxc+vTjllUVPT8eBfkZBHCb6+V6FmqDVBK2urkzQ6vuAVqOqImh1sXNbvMfEKR0YtBq7TNDqyta2PvLF+f01odWtAytdfginvMU5JSh2Y//omHiHx8R1EkinxBXN0CnxkE6Je/HCFPcZQbELLe/WlBIUm2xpSQv3aUsJik3G9CEb02EdYQwVir3BWQZBsWsq1MTdvwoU+4GfZRB0u6kmeU91JxGEV2vbDDlPQ3KeencSQdDtuOX9nEQQdLupJlnC2nMEMoVtmyFTOCRTuOE5Qvkvm9ybnp3abe8x/88I1Fi4rrzJ/P7u09Y9uRErx66EKiZRli+WQuYuEzOmq9akFNsZ6roVRLZiaN9FYDA8mzcBMc0Yf7xF2Ba/Y0+YqdpQ855SgW0zCaggnRCrt52Gd2uWhVcmTTQd3uwDUNkBH9wnwkZ6e501bi9dmsH+euvNrbnaPUPZYJXjy43ygW+wttMwjg3WYFZ/U/ihKR1JJp1APJnfw3Lkccw7z0ISKFT5hVVSxHTk4uVjdmO56OpDmHwXLtjv2Bt+y12xKl/Z+kQk7wULJUMmwPcI8SG8CSEQhioP43XXSg/i957AYxEuGxXi3yMeYA8rs0GoVl4cfB2icLOJ8Kfc/8PpNz95+OvXoa//pv61+ObrJ/C/7FdnJ2cXmOuTPXoJKgZkhXnRcgKRMDgrLmwxDxllCwnfxbcYKy5W8KS7hYD5XAk/ZKFAEtrjuqa/dfwl082XlFN0plivlZTIE29SReCvfsV4p99ooUrJkkIifz3xv/k6gIX95g2fOZ6Dk/yC3YjQcssGDSGgK9YcXH+ZnyYz8K+fyHagG9Pcw09Pvir59ulFyZc38t1QwL7969WRAJ3NZBYc+EJSAbG3Vx6D6bw8ZHcw+Y7rIikw9C1bsgI94R2FcW6hpN8Q8qT6rrqtWITY8+OSjsDWKeyXVDE92fAHyts3ZTHP/cQERquUlDDdVqcwn7SOk86E1O7q5aL53ghdyU+JaVE/1imwV49ZIqNe6DuTqCK6rFNdDK28tEbWRNxy5keeF5M8V7DaIYTfh7DAd5gESXJL7fhpal2RChqAkyDCBTQ14Qvr1hG+fPEIi295UmxNgqX/+MlTtB82gxhFfm3O5qCqK+4UC9W2PGZN8a0m1L1MPRL6/Osoz6YTxDhFYXr1Gz3J+lI7diBa05zanLocvBaKD1zjrhy8VnQnNq2zrEnxNw4IY4CeNciOD+LYRHk8eOvdOr7wsByooSv5qruu/JUXct+TxyeSrjTjvg/SW1fl+5fX7L2poXdc6/KaWsTwPLxtZV3nicENPPnA562f/s7yP4Fh40t4YL3DfvCOo9ENFk7dO4KD9xAAGD1T6yAlVT5AMS/i7DsOlZrIXAcfwaaEMEKbZWhMdVWSpHZ40Oe6TSP9ETVTxWnZrsLNhqAkrx6aIqoYS/DspBJL0PGNyyaBXXNIdw9z1RxwbROs7V/L3ueBRkrTtiJ67YniVUvuugatI/VZ2km+dq1q27p7hldqntgKHl0Djer37RWpgXSmcW1H4OoPu71yDjbMDb0l4aq/VCvKLl3RVvaVbQt5ohTTlGK69jN8yHVqF3SnOG1JbuqlbdmKh7QzBlJvuUdkXCraqjQuO2cVkXUh6yI/A75DMbU/WvF89s3wqeX2bB/Kbk/wecChbHsg83b8nV66G9uwbnbFt9kX04Z8hYq2mgLRnQGHyVUgV0F+BhmIbmAYNuO2DPzez+qp2zZ63a1B2ZavQvakoq3G2JMMStIAGZSBxJ7577ZGqz4tRat+FCsIsEJEJGQ33c4hq/LnZzVwr9eug0rgz5YbcbxRgn9uxHkdsZsFZxKAgchKhFr5GpkhsVe2bDIw6CxbBCHelypfn09L7vLfO8Sq1fGAAahWk2Ba3MLwKwHyTd/PW7osOZTqANH3ZsQlqMls2xtddz8ohP2+zsvMFFfTVjrChHa2N5pvfs6PoSkLR3yhfOZKZwR892NvdB/x6UnbIXdOx3Ffu6OhuLeKwhs1PQrmX+1i9GE72bdOb4FTbZwA3czuEVPfX1/162S5/WZL1vSvYdOVVoX5b1YuUz6zIjeUywI1//39k5f9UEcdzlhgikYLHyuMnWBjuZ7R6Up5W9nTlRo5okMVOlSp/Qz/lB6k//5hYsM+nS9OGWHKch0jy1PeVsHyEJas2ACZnoGc5+8xmO8/hmyLqH578Niwo3q9uO2j+njCxhbVjw9JVxg7IejG6RkRcq7QADlGo4vJCTHXUKdyyggpN067QQi5YgNkOAYSUee/2wohF/AQL/sKrq15BoRkwuaihk8gdLrqXtFzQclDcv34VvhLVtaZhi5VdCzbvbZ3g6louumMQHW1tmguNk0gB5U12tzo1dwMlooxDHWF8ofBrcvWN5s/j2ldFprd73FUWxFAg90oAX8KwG34UFcyJwANF4O1Wv0Wl4sZi1hX5uV06dRe3vX2h5s9r0XdEVLs5iVoqlaHdq1W4m3ahcC7M0tvq9zTiNKQuMYhXevCrLY06NXiuLJAtCN2ep+DXIpp8/aRhXZjFPFP5ZNJrwun7VUUhjgRicmTP92oh6e+Ncr0leX/IPSldi8OXgmovjwoFPmIzYN9le0EKZ8ONi+3P731ffC+3+HIXhwID5y2lcuNhUDHTv5Te3YfpYo+yPYbdu4rPhM+j+umnrHg1pT7f8bg7sWBNb1FuDGozH/9f+yxgvbVRwQA'

-- Check for existing form version
DECLARE @currentform_version INT = NULL;
SELECT @currentform_version = [FormSystemVersion] 
FROM [dbo].[Questionnaire] 
WHERE [Id] = @formId;

PRINT @currentform_version;
PRINT @form2025_version;

-- Insert or update the form in the Questionnaire table
IF @currentform_version IS NULL
BEGIN 
    INSERT INTO [dbo].[Questionnaire]
    (
      [Id], [Name], [Year], [Status], [IsActive], [Acknowledgement], [AcknowledgementText], [GeneralComments], [GeneralCommentsText],
      [CreatedOn], [ModifiedOn], [DefinitionJson], [DraftDefinitionJson], [FormSystemVersion]
    )
    VALUES
    (
      @formId, @FormName, 2025, 1, 1, 1, @form2025_ack, 1, @form2025_generalComments,
      GETUTCDATE(), GETUTCDATE(), @FinalJson, @FinalJson, @form2025_version
    );
END
ELSE IF (@currentform_version < @form2025_version) 
BEGIN
    UPDATE [dbo].[Questionnaire]
    SET
      [DefinitionJson] = @FinalJson,
      [DraftDefinitionJson] = @FinalJson,
      [Name] = @FormName,
      [Status] = 1,
      [IsActive] = 1,
      [Acknowledgement] = 1,
      [AcknowledgementText] = @form2025_ack,
      [GeneralComments] = 1,
      [GeneralCommentsText] = @form2025_generalComments,
      [FormSystemVersion] = @form2025_version,
      [ModifiedOn] = GETUTCDATE()
    WHERE
      [Id] = @formId;
END;


-- Verify the insertion/update
SELECT [Id], [Name], [Year], [FormSystemVersion], [CreatedOn], [ModifiedOn]
FROM [dbo].[Questionnaire]
WHERE [Id] = @formId;



GO

GO
PRINT N'Checking existing data against newly created constraints';


GO
USE [$(DatabaseName)];


GO
ALTER TABLE [dbo].[Form] WITH CHECK CHECK CONSTRAINT [FK_Form_Questionnaire];

ALTER TABLE [dbo].[Form] WITH CHECK CHECK CONSTRAINT [FK_Form_Status];

ALTER TABLE [dbo].[UserAnswer] WITH CHECK CHECK CONSTRAINT [FK_UserAnswer_Form];

ALTER TABLE [dbo].[PartnerReferenceData] WITH CHECK CHECK CONSTRAINT [FK_PartnerReferenceData_Partner];

ALTER TABLE [dbo].[PartnerReviewer] WITH CHECK CHECK CONSTRAINT [FK_PartnerReviewer_Partner];

ALTER TABLE [dbo].[PartnerReviewer] WITH CHECK CHECK CONSTRAINT [FK_PartnerReviewer_PrimaryReviewer];

ALTER TABLE [dbo].[PartnerReviewer] WITH CHECK CHECK CONSTRAINT [FK_PartnerReviewer_SecondaryReviewer];

ALTER TABLE [dbo].[PartnerReferenceData] WITH CHECK CHECK CONSTRAINT [FK_PartnerReferenceData_Meta];

ALTER TABLE [dbo].[PartnerReferenceDataMetaDetails] WITH CHECK CHECK CONSTRAINT [FK_PartnerReferenceDataMetaDetails_Meta];

ALTER TABLE [dbo].[PartnerReferenceDataUpload] WITH CHECK CHECK CONSTRAINT [FK_PartnerReferenceDataUpload_Meta];

ALTER TABLE [dbo].[PartnerReferenceDataUploadDetails] WITH CHECK CHECK CONSTRAINT [FK_PartnerReferenceDataUploadDetails_Upload];

ALTER TABLE [dbo].[PartnerReviewerUploadDetails] WITH CHECK CHECK CONSTRAINT [FK_PartnerReviewerUploadDetails_PartnerReviewerUpload];

ALTER TABLE [dbo].[Questionnaire] WITH CHECK CHECK CONSTRAINT [FK_Questionnaire_Status];


GO
PRINT N'Update complete.';


GO
