﻿/*
Deployment script for BdoPartner.Plans.Database

This code was generated by a tool.
Changes to this file may cause incorrect behavior and will be lost if
the code is regenerated.
*/

GO
SET ANSI_NULLS, ANSI_PADDING, ANSI_WARNINGS, ARITHABORT, CONCAT_NULL_YIELDS_NULL, QUOTED_IDENTIFIER ON;

SET NUMERIC_ROUNDABORT OFF;


GO
:setvar DatabaseName "BdoPartner.Plans.Database"
:setvar Default<PERSON>ile<PERSON>refix "BdoPartner.Plans.Database"
:setvar DefaultDataPath "C:\Program Files\Microsoft SQL Server\MSSQL16.MSSQLSERVER\MSSQL\DATA\"
:setvar DefaultLogPath "C:\Program Files\Microsoft SQL Server\MSSQL16.MSSQLSERVER\MSSQL\DATA\"

GO
:on error exit
GO
/*
Detect SQLCMD mode and disable script execution if SQLCMD mode is not supported.
To re-enable the script after enabling SQLCMD mode, execute the following:
SET NOEXEC OFF; 
*/
:setvar __IsSqlCmdEnabled "True"
GO
IF N'$(__IsSqlCmdEnabled)' NOT LIKE N'True'
    BEGIN
        PRINT N'SQLCMD mode must be enabled to successfully execute this script.';
        SET NOEXEC ON;
    END


GO
USE [$(DatabaseName)];


GO
/*
Master Pre-Deployment Script
This script runs before the main database deployment to handle schema migrations
that could cause data loss warnings during deployment.
*/

PRINT 'Starting pre-deployment scripts...'

-- Execute Form table status consolidation migration
/*
Pre-deployment script to handle Form table column changes
This script:
1. Drops unused columns: PlanStatus, MidYearStatus, YearEndStatus, PartnerSubmittionDate
2. Adds Status column if it doesn't exist
*/

PRINT 'Starting Form table column updates...'

-- Check if Form table exists
IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = 'dbo' AND TABLE_NAME = 'Form')
BEGIN
    -- Drop index IX_Form_Year_Partner_Status if it exists (depends on PlanStatus column)
    IF EXISTS (SELECT 1 FROM sys.indexes WHERE object_id = OBJECT_ID('dbo.Form') AND name = 'IX_Form_Year_Partner_Status')
    BEGIN
        PRINT 'Dropping index IX_Form_Year_Partner_Status...'
        DROP INDEX [IX_Form_Year_Partner_Status] ON [dbo].[Form]
    END

    -- Drop PlanStatus column if it exists
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = 'dbo' AND TABLE_NAME = 'Form' AND COLUMN_NAME = 'PlanStatus')
    BEGIN
        PRINT 'Dropping PlanStatus column...'
        ALTER TABLE [dbo].[Form] DROP COLUMN [PlanStatus]
    END

    -- Drop MidYearStatus column if it exists
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = 'dbo' AND TABLE_NAME = 'Form' AND COLUMN_NAME = 'MidYearStatus')
    BEGIN
        PRINT 'Dropping MidYearStatus column...'
        ALTER TABLE [dbo].[Form] DROP COLUMN [MidYearStatus]
    END

    -- Drop YearEndStatus column if it exists
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = 'dbo' AND TABLE_NAME = 'Form' AND COLUMN_NAME = 'YearEndStatus')
    BEGIN
        PRINT 'Dropping YearEndStatus column...'
        ALTER TABLE [dbo].[Form] DROP COLUMN [YearEndStatus]
    END

    -- Drop PartnerSubmittionDate column if it exists
    IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = 'dbo' AND TABLE_NAME = 'Form' AND COLUMN_NAME = 'PartnerSubmittionDate')
    BEGIN
        PRINT 'Dropping PartnerSubmittionDate column...'
        ALTER TABLE [dbo].[Form] DROP COLUMN [PartnerSubmittionDate]
    END

    -- Add Status column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = 'dbo' AND TABLE_NAME = 'Form' AND COLUMN_NAME = 'Status')
    BEGIN
        PRINT 'Adding Status column...'
        ALTER TABLE [dbo].[Form] ADD [Status] TINYINT NOT NULL DEFAULT 0
    END

    -- Recreate the index with the new Status column
    IF NOT EXISTS (SELECT 1 FROM sys.indexes WHERE object_id = OBJECT_ID('dbo.Form') AND name = 'IX_Form_Year_Partner_Status')
    BEGIN
        PRINT 'Creating index IX_Form_Year_Partner_Status...'
        CREATE INDEX [IX_Form_Year_Partner_Status] ON [dbo].[Form] ([Year], [PartnerUserId], [Status])
    END

    PRINT 'Form table column updates completed'
END
ELSE
BEGIN
    PRINT 'Form table does not exist'
END

PRINT 'Form table column update script completed'


PRINT 'Pre-deployment scripts completed.'
GO

GO
MERGE INTO [dbo].[Language] AS Target
USING (VALUES
  (1,'en','English')
 ,(2,'fr','French')
) AS Source ([Id],[Code],[Name])
ON (Target.[Id] = Source.[Id])
WHEN MATCHED AND (Target.[Code] <> Source.[Code] OR Target.[Name] <> Source.[Name]) THEN
	UPDATE SET
		[Code] = Source.[Code], 
		[Name] = Source.[Name]
WHEN NOT MATCHED BY TARGET THEN
	INSERT([Id],[Code],[Name])
	VALUES(Source.[Id],Source.[Code],Source.[Name])
WHEN NOT MATCHED BY SOURCE THEN 
	DELETE;
GO

-- Delete RolePermission records for roles not in the source dataset
--DELETE [dbo].[RolePermission]

-- Delete UserRole records for roles not in the source dataset
--DELETE [dbo].[UserRole]

--MERGE INTO [dbo].[Role] AS Target
--USING (VALUES
--  (15, 'Partner Plans Administrator')
-- ,(3, 'Active Partner')
-- ,(16, 'New Partner')
-- ,(17, 'Partner Plans Executive Leadership')
--) AS Source ([Id],[Name])
--ON (Target.[Id] = Source.[Id])
--WHEN MATCHED AND (Target.[Name] <> Source.[Name]) THEN
--	UPDATE SET
--		[Name] = Source.[Name]
--WHEN NOT MATCHED BY TARGET THEN
--	INSERT([Id],[Name])
--	VALUES(Source.[Id],Source.[Name])
--WHEN NOT MATCHED BY SOURCE THEN
--	DELETE;
--GO


--MERGE INTO [dbo].[Permission] AS Target
--USING (VALUES
--  (6, 'Partner Plans Login')
-- ,(7, 'Track Own Partner Plan')
-- ,(8, 'Track All Partner Plans')
-- ,(9, 'Draft Submit Partner Plan')
-- ,(10, 'Edit Partner Plans Under Review')
-- ,(11, 'Partner Plans Final Submission')
-- ,(12, 'Mid End Year Self Assessment')
-- ,(13, 'Mid End Year Reviewer Assessment')
-- ,(14, 'View Submitted Partner Plans')
-- ,(15, 'Edit Submitted Partner Plans')
-- ,(16, 'Export Plan Data To Excel')
-- ,(17, 'Manage Partner Reviewer Relationships')
-- ,(18, 'Upload KPI Data')
-- ,(19, 'Edit Publish Input Form')
--) AS Source ([Id],[Name])
--ON (Target.[Id] = Source.[Id])
--WHEN MATCHED AND (Target.[Name] <> Source.[Name]) THEN
--	UPDATE SET
--		[Name] = Source.[Name]
--WHEN NOT MATCHED BY TARGET THEN
--	INSERT([Id],[Name])
--	VALUES(Source.[Id],Source.[Name])
--WHEN NOT MATCHED BY SOURCE THEN
--	DELETE;
--GO


---- password = "Password1"
--MERGE INTO [dbo].[User] AS Target
--USING (VALUES
--  ('BFD02B5C-2408-4870-8E1F-06C7A72187F6', 'APP', 'ppadmin',0x999000A49E8DD91ED5726EA0AF1745C8F712640D6563F7FDE1977013B730CB3C9D78F99EC07FC59B0ADEAB5CBA0380DA, 0xE73685F552,0,'PP','Administrator','<EMAIL>',1,1)
--  ,('D3FB7F6A-6487-4190-9D9F-0ECD7A25E239', 'APP','partner1',0x999000A49E8DD91ED5726EA0AF1745C8F712640D6563F7FDE1977013B730CB3C9D78F99EC07FC59B0ADEAB5CBA0380DA, 0xE73685F552,0,'Partner','User','<EMAIL>',1,1)
--  ,('B6BDCA35-4D96-4C26-B6B2-D15E285147DB', 'APP', 'newpartner',0x999000A49E8DD91ED5726EA0AF1745C8F712640D6563F7FDE1977013B730CB3C9D78F99EC07FC59B0ADEAB5CBA0380DA, 0xE73685F552,0,'New','Partner','<EMAIL>',1,1)
--  ,('A1B2C3D4-5E6F-7890-ABCD-EF1234567890', 'APP', 'ppexec',0x999000A49E8DD91ED5726EA0AF1745C8F712640D6563F7FDE1977013B730CB3C9D78F99EC07FC59B0ADEAB5CBA0380DA, 0xE73685F552,0,'PP Executive','Leadership','<EMAIL>',1,1)

--) AS Source ([Id], [AuthProviderId], [Username],[Password],[Salt],[IsTempPasswordEnabled],[FirstName],[LastName],[Email],[LanguageId],[IsActive])
--ON (Target.[Id] = Source.[Id])
--WHEN MATCHED AND (Target.[Username] <> Source.[Username] OR Target.[AuthProviderId]<> Source.[AuthProviderId] OR Target.[Password] <> Source.[Password] OR Target.[Salt] <> Source.[Salt] OR Target.[IsTempPasswordEnabled] <> Source.[IsTempPasswordEnabled] 
--OR Target.[FirstName] <> Source.[FirstName] OR Target.[LastName] <> Source.[LastName] OR Target.[Email] <> Source.[Email] 
--OR Target.[LanguageId] <> Source.[LanguageId] OR Target.[IsActive] <> Source.[IsActive] ) THEN
-- UPDATE SET
-- [Username] = Source.[Username], 
--[Password] = Source.[Password], 
--[Salt] = Source.[Salt], 
--[IsTempPasswordEnabled] = Source.[IsTempPasswordEnabled], 
--[FirstName] = Source.[FirstName], 
--[LastName] = Source.[LastName], 
--[Email] = Source.[Email], 
--[LanguageId] = Source.[LanguageId], 
--[IsActive] = Source.[IsActive],
--[AuthProviderId] = Source.[AuthProviderId]
--WHEN NOT MATCHED BY TARGET THEN
-- INSERT([Id],[AuthProviderId],[Username],[Password],[Salt],[IsTempPasswordEnabled],[FirstName],[LastName],[Email],[LanguageId],[IsActive])
-- VALUES(Source.[Id],Source.[AuthProviderId], Source.[Username],Source.[Password],Source.[Salt],Source.[IsTempPasswordEnabled],Source.[FirstName],Source.[LastName],Source.[Email],Source.[LanguageId],Source.[IsActive]);
---- WHEN NOT MATCHED BY SOURCE THEN 
---- DELETE;
--GO

-- MERGE INTO [dbo].[UserRole] AS Target
--USING (VALUES
--   ('BFD02B5C-2408-4870-8E1F-06C7A72187F6', 15)  -- PP Administrator
--  ,('D3FB7F6A-6487-4190-9D9F-0ECD7A25E239', 3)  -- Active Partner
--  ,('B6BDCA35-4D96-4C26-B6B2-D15E285147DB', 16)  -- New Partner
--  ,('A1B2C3D4-5E6F-7890-ABCD-EF1234567890', 17)  -- PP Executive Leadership
--) AS Source ([UserId],[RoleId])
--ON (Target.[RoleId] = Source.[RoleId] and Target.[UserId] = Source.[UserId])
----WHEN MATCHED AND (Target.[Name] <> Source.[Name]) THEN
----	UPDATE SET
----		[Name] = Source.[Name]		
--WHEN NOT MATCHED BY TARGET THEN
--	INSERT([UserId], [RoleId])
--	VALUES(Source.[UserId], Source.[RoleId]);
----WHEN NOT MATCHED BY SOURCE THEN 
----	DELETE;
--GO

-- FormStatus lookup table seed data
-- Updated to match new FormStatus enum with cycle-specific statuses: PlanningNotStarted=0 to YearEndReviewCompleted=14
-- Description fields match the [Description] attributes from the enumeration
MERGE INTO [dbo].[FormStatus] AS Target
USING (VALUES
  (0, 'PlanningNotStarted', 'Not Started', 'Non commencé')
 ,(1, 'PlanningDraft', 'Draft', 'Brouillon')
 ,(2, 'PlanningReopened', 'Reopened', 'Réouvert')
 ,(3, 'PlanningUnderReview', 'Under Review', 'En révision')
 ,(4, 'PlanningCompleted', 'Completed', 'Terminé')
 ,(5, 'MidYearReviewNotStarted', 'Not Started', 'Non commencé')
 ,(6, 'MidYearReviewDraft', 'Draft', 'Brouillon')
 ,(7, 'MidYearReviewReopened', 'Reopened', 'Réouvert')
 ,(8, 'MidYearReviewUnderReview', 'Under Review', 'En révision')
 ,(9, 'MidYearReviewCompleted', 'Completed', 'Terminé')
 ,(10, 'YearEndReviewNotStarted', 'Not Started', 'Non commencé')
 ,(11, 'YearEndReviewDraft', 'Draft', 'Brouillon')
 ,(12, 'YearEndReviewReopened', 'Reopened', 'Réouvert')
 ,(13, 'YearEndReviewUnderReview', 'Under Review', 'En révision')
 ,(14, 'YearEndReviewCompleted', 'Completed', 'Terminé')
) AS Source ([Id],[Name],[EnglishDislayName],[FrenchDisplayName])
ON (Target.[Id] = Source.[Id])
WHEN MATCHED AND (Target.[Name] <> Source.[Name] OR Target.[EnglishDislayName] <> Source.[EnglishDislayName] OR Target.[FrenchDisplayName] <> Source.[FrenchDisplayName]) THEN
	UPDATE SET
		[Name] = Source.[Name],
		[EnglishDislayName] = Source.[EnglishDislayName],
		[FrenchDisplayName] = Source.[FrenchDisplayName]
WHEN NOT MATCHED BY TARGET THEN
	INSERT([Id],[Name],[EnglishDislayName],[FrenchDisplayName])
	VALUES(Source.[Id],Source.[Name],Source.[EnglishDislayName],Source.[FrenchDisplayName])
WHEN NOT MATCHED BY SOURCE THEN
	DELETE;
GO

-- QuestionnaireStatus lookup table seed data
MERGE INTO [dbo].[QuestionnaireStatus] AS Target
USING (VALUES
  (0, 'Draft', 'Draft', 'Brouillon')
 ,(1, 'Published', 'Published', 'Publié')
 ,(2, 'Closed', 'Closed', 'Fermé')
) AS Source ([Id],[Name],[EnglishDislayName],[FrenchDisplayName])
ON (Target.[Id] = Source.[Id])
WHEN MATCHED AND (Target.[Name] <> Source.[Name] OR Target.[EnglishDislayName] <> Source.[EnglishDislayName] OR Target.[FrenchDisplayName] <> Source.[FrenchDisplayName]) THEN
	UPDATE SET
		[Name] = Source.[Name],
		[EnglishDislayName] = Source.[EnglishDislayName],
		[FrenchDisplayName] = Source.[FrenchDisplayName]
WHEN NOT MATCHED BY TARGET THEN
	INSERT([Id],[Name],[EnglishDislayName],[FrenchDisplayName])
	VALUES(Source.[Id],Source.[Name],Source.[EnglishDislayName],Source.[FrenchDisplayName])
WHEN NOT MATCHED BY SOURCE THEN
	DELETE;
GO

-- Test data only - Insert notification messages if they don't already exist

-- Check and insert notification messages only if they don't exist
IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'Creating an inclusive environment where everyone can bring their genuine selves to work, participate fully and be positioned for success')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('Creating an inclusive environment where everyone can bring their genuine selves to work, participate fully and be positioned for success');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'Meet your Inclusion, Equity and Diversity Advisory Council')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('Meet your Inclusion, Equity and Diversity Advisory Council');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'During the month of June, Indigenous History Month, we have seen tragic events unfold � the lives of 215 residential school children found in BC and 751 unmarked graves found in Saskatchewan')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('During the month of June, Indigenous History Month, we have seen tragic events unfold � the lives of 215 residential school children found in BC and 751 unmarked graves found in Saskatchewan');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'Include land acknowledgements in your Outlook email signature')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('Include land acknowledgements in your Outlook email signature');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'Pride Season Event: We celebrated the kick-off to Pride Season on Thursday, June 17 with a special virtual Pride webcast. If you missed it, you can watch the webcast recording here')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('Pride Season Event: We celebrated the kick-off to Pride Season on Thursday, June 17 with a special virtual Pride webcast. If you missed it, you can watch the webcast recording here');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'Change your BDO Outlook photo: Let�s add some colour to our conversations')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('Change your BDO Outlook photo: Let�s add some colour to our conversations');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'Include your preferred pronoun in your Outlook email signature')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('Include your preferred pronoun in your Outlook email signature');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'BDO 100 Celebration')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('BDO 100 Celebration');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'The New IE&D Advisory Council')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('The New IE&D Advisory Council');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'Your Firm Engagement HUB')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('Your Firm Engagement HUB');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'NEW AND IMPROVED MY BDO!')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('NEW AND IMPROVED MY BDO!');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'Congratulations - Chris Diepdael, CMC�BC Rising Star Award')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('Congratulations - Chris Diepdael, CMC�BC Rising Star Award');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = '[CAMPAIGN LAUNCH] Selling your business: now live!')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('[CAMPAIGN LAUNCH] Selling your business: now live!');
END

GO

Delete from [dbo].[FormAccessConfig] ;

GO

INSERT INTO FormAccessConfig(Cycle, FormStatus, UserRole, PlanPartnerQuestion, PlanReviwerQuestion, MidYearPartnerQuestion, MidYearReviewerQuestion, YearEndPartnerQuestion, YearEndReviewerQuestion, PlanPanel, MidYearPanel, YearEndPanel) VALUES
-- Planning Cycle
-- Not Started
(0, 0, 1, 2, 0, 0, 0, 0, 0, 2, 0, 0), -- Plan Owner (partner)
(0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0), -- Reviewer
(0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0), -- ELT
(0, 0, 3, 1, 0, 0, 0, 0, 0, 1, 0, 0), -- Admin
   -- Draft
(0, 1, 1, 2, 0, 0, 0, 0, 0, 2, 0, 0), -- Plan Owner (partner)
(0, 1, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0), -- Reviewer
(0, 1, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0), -- ELT
(0, 1, 3, 1, 0, 0, 0, 0, 0, 1, 0, 0), -- Admin
   -- Reopened
(0, 2, 1, 2, 0, 0, 0, 0, 0, 2, 0, 0), -- Plan Owner (partner)
(0, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0), -- Reviewer
(0, 2, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0), -- ELT
(0, 2, 3, 1, 0, 0, 0, 0, 0, 1, 0, 0), -- Admin
   -- Under Review
(0, 3, 1, 2, 0, 0, 0, 0, 0, 2, 0, 0), -- Plan Owner (partner)
(0, 3, 2, 2, 2, 0, 0, 0, 0, 2, 0, 0), -- Reviewer
(0, 3, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0), -- ELT
(0, 3, 3, 1, 1, 0, 0, 0, 0, 1, 0, 0), -- Admin
   -- Completed
(0, 4, 1, 1, 1, 0, 0, 0, 0, 1, 0, 0), -- Plan Owner (partner)
(0, 4, 2, 1, 1, 0, 0, 0, 0, 1, 0, 0), -- Reviewer
(0, 4, 4, 1, 1, 0, 0, 0, 0, 1, 0, 0), -- ELT
(0, 4, 3, 2, 2, 0, 0, 0, 0, 2, 0, 0), -- Admin
-- Mid Year Cycle
   -- Not Started
(1, 5, 1, 1, 1, 2, 0, 0, 0, 1, 2, 0), -- Plan Owner (partner)
(1, 5, 2, 1, 1, 0, 0, 0, 0, 0, 0, 0), -- Reviewer
(1, 5, 4, 1, 1, 0, 0, 0, 0, 1, 0, 0), -- ELT
(1, 5, 3, 2, 2, 1, 0, 0, 0, 2, 1, 0), -- Admin
   -- Draft
(1, 6, 1, 1, 1, 2, 0, 0, 0, 1, 2, 0), -- Plan Owner (partner)
(1, 6, 2, 1, 1, 0, 0, 0, 0, 0, 0, 0), -- Reviewer
(1, 6, 4, 1, 1, 0, 0, 0, 0, 1, 0, 0), -- ELT
(1, 6, 3, 2, 2, 1, 0, 0, 0, 2, 1, 0), -- Admin
   -- Reopened
(1, 7, 1, 1, 1, 2, 0, 0, 0, 1, 2, 0), -- Plan Owner (partner)
(1, 7, 2, 1, 1, 1, 1, 0, 0, 0, 1, 0), -- Reviewer
(1, 7, 4, 1, 1, 0, 0, 0, 0, 1, 0, 0), -- ELT
(1, 7, 3, 2, 2, 1, 0, 0, 0, 2, 1, 0), -- Admin
   -- Under Review
(1, 8, 1, 1, 1, 1, 0, 0, 0, 1, 1, 0), -- Plan Owner (partner)
(1, 8, 2, 1, 1, 1, 2, 0, 0, 0, 2, 0), -- Reviewer
(1, 8, 4, 1, 1, 0, 0, 0, 0, 1, 0, 0), -- ELT
(1, 8, 3, 2, 2, 1, 1, 0, 0, 2, 1, 0), -- Admin
   -- Completed
(1, 9, 1, 1, 1, 1, 1, 0, 0, 1, 1, 0), -- Plan Owner (partner)
(1, 9, 2, 1, 1, 1, 1, 0, 0, 0, 1, 0), -- Reviewer
(1, 9, 4, 1, 1, 1, 1, 0, 0, 1, 1, 0), -- ELT
(1, 9, 3, 2, 2, 2, 2, 0, 0, 2, 2, 0), -- Admin
-- Year End Review Cycle
-- Not Started
(2, 10, 1, 1, 1, 0, 0, 2, 0, 1, 0, 2), -- Plan Owner (partner)
(2, 10, 2, 1, 1, 0, 0, 0, 0, 1, 0, 0), -- Reviewer
(2, 10, 4, 1, 1, 1, 1, 0, 0, 1, 1, 0), -- ELT
(2, 10, 3, 2, 2, 2, 2, 1, 1, 2, 2, 1), -- Admin
   -- Draft
(2, 11, 1, 1, 1, 0, 0, 2, 0, 1, 0, 2), -- Plan Owner (partner)
(2, 11, 2, 1, 1, 0, 0, 0, 0, 1, 0, 0), -- Reviewer
(2, 11, 4, 1, 1, 1, 1, 0, 0, 1, 1, 0), -- ELT
(2, 11, 3, 2, 2, 2, 2, 1, 1, 2, 2, 1), -- Admin
       -- Reopened
(2, 12, 1, 1, 1, 0, 0, 2, 0, 1, 0, 2), -- Plan Owner (partner)
(2, 12, 2, 1, 1, 0, 0, 1, 1, 1, 0, 1), -- Reviewer
(2, 12, 4, 1, 1, 1, 1, 0, 0, 1, 1, 0), -- ELT
(2, 12, 3, 2, 2, 2, 2, 1, 1, 2, 2, 1), -- Admin
   -- Under Review
(2, 13, 1, 1, 1, 0, 0, 1, 0, 1, 0, 1), -- Plan Owner (partner)
(2, 13, 2, 1, 1, 0, 0, 1, 2, 1, 0, 2), -- Reviewer
(2, 13, 4, 1, 1, 1, 1, 0, 0, 1, 1, 0), -- ELT
(2, 13, 3, 2, 2, 2, 2, 1, 1, 2, 2, 1), -- Admin
   -- Completed
(2, 14, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1), -- Plan Owner (partner)
(2, 14, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1), -- Reviewer
(2, 14, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1), -- ELT
(2, 14, 3, 2, 2, 2, 2, 2, 2, 2, 2, 2) -- Admin
GO
-- Note: Apply json string compress with on line tool: https://www.zickty.com/texttogzip
DECLARE @FormName NVARCHAR(255) = N'Partner Planning Form 2026';
DECLARE @FormYear smallint = 2026;
DECLARE @form_version INT = 26; -- Update based on "Bug 12533: Planning form - Step 2 layout needs to be fixed; And Bug 12353: PDS Questions (KPIs) Missing from the Planning Form"
DECLARE @form_ack NVARCHAR(MAX) = N'Acknowledgment text for Partner Planning Form 2026';
DECLARE @form_generalComments NVARCHAR(MAX) = N'General comments for Partner Planning Form 2026';

-- Combine pages into the final JSON, matching the specified root structure
declare @FinalJson nvarchar(MAX) =
-- Version 22 script.
-- N'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'
-- Version 23 script
-- N'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'
-- Version 24 script
-- N'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'
-- Version 25 script
-- N'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'
-- Version 26 script
N'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'

-- Check for existing form version
DECLARE @currentform_version INT = NULL;
SELECT @currentform_version = [FormSystemVersion] 
FROM [dbo].[Questionnaire] 
WHERE [Year] = @FormYear

PRINT @currentform_version;
PRINT @form_version;

-- Insert or update the form in the Questionnaire table
IF @currentform_version IS NULL
BEGIN 
    INSERT INTO [dbo].[Questionnaire]
    (
      [Id], [Name], [Year], [Status], [IsActive], [Acknowledgement], [AcknowledgementText], [GeneralComments], [GeneralCommentsText],
      [CreatedOn], [ModifiedOn], [DefinitionJson], [DraftDefinitionJson], [FormSystemVersion]
    )
    VALUES
    (
      NEWID(), @FormName, @FormYear, 1, 1, 1, @form_ack, 1, @form_generalComments,
      GETUTCDATE(), GETUTCDATE(), @FinalJson, @FinalJson, @form_version
    );
END
ELSE IF (@currentform_version < @form_version) 
BEGIN
    UPDATE [dbo].[Questionnaire]
    SET
      [DefinitionJson] = @FinalJson,
      [DraftDefinitionJson] = @FinalJson,
      [Name] = @FormName,
      [Status] = 1,
      [IsActive] = 1,
      [Acknowledgement] = 1,
      [AcknowledgementText] = @form_ack,
      [GeneralComments] = 1,
      [GeneralCommentsText] = @form_generalComments,
      [FormSystemVersion] = @form_version,
      [ModifiedOn] = GETUTCDATE()
    WHERE
      [Year] = @FormYear;
END;


-- Verify the insertion/update
SELECT [Id], [Name], [Year], [FormSystemVersion], [CreatedOn], [ModifiedOn]
FROM [dbo].[Questionnaire]
WHERE [Year] = @FormYear;



GO

GO
PRINT N'Update complete.';


GO
