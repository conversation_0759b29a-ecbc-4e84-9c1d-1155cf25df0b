﻿/*
Deployment script for BdoPartner.Plans.Database

This code was generated by a tool.
Changes to this file may cause incorrect behavior and will be lost if
the code is regenerated.
*/

GO
SET ANSI_NULLS, ANSI_PADDING, ANSI_WARNINGS, ARITHABORT, CONCAT_NULL_YIELDS_NULL, QUOTED_IDENTIFIER ON;

SET NUMERIC_ROUNDABORT OFF;


GO
:setvar DatabaseName "BdoPartner.Plans.Database"
:setvar Default<PERSON>ile<PERSON>refix "BdoPartner.Plans.Database"
:setvar DefaultDataPath "C:\Program Files\Microsoft SQL Server\MSSQL16.MSSQLSERVER\MSSQL\DATA\"
:setvar DefaultLogPath "C:\Program Files\Microsoft SQL Server\MSSQL16.MSSQLSERVER\MSSQL\DATA\"

GO
:on error exit
GO
/*
Detect SQLCMD mode and disable script execution if SQLCMD mode is not supported.
To re-enable the script after enabling SQLCMD mode, execute the following:
SET NOEXEC OFF; 
*/
:setvar __IsSqlCmdEnabled "True"
GO
IF N'$(__IsSqlCmdEnabled)' NOT LIKE N'True'
    BEGIN
        PRINT N'SQLCMD mode must be enabled to successfully execute this script.';
        SET NOEXEC ON;
    END


GO
USE [$(DatabaseName)];


GO
/*
The column [dbo].[Form].[AdminReopenDate] is being dropped, data loss could occur.

The column [dbo].[Form].[CEOCommentDate] is being dropped, data loss could occur.

The column [dbo].[Form].[CEOName] is being dropped, data loss could occur.

The column [dbo].[Form].[CEOReplyComment ] is being dropped, data loss could occur.

The column [dbo].[Form].[CommentedManagePartnerName] is being dropped, data loss could occur.

The column [dbo].[Form].[DocuSignUrl] is being dropped, data loss could occur.

The column [dbo].[Form].[EnvelopeId] is being dropped, data loss could occur.

The column [dbo].[Form].[Filename] is being dropped, data loss could occur.

The column [dbo].[Form].[Issue] is being dropped, data loss could occur.

The column [dbo].[Form].[ManagePartnerApprovedDate] is being dropped, data loss could occur.

The column [dbo].[Form].[ManagePartnerCommentDate] is being dropped, data loss could occur.

The column [dbo].[Form].[ManagePartnerRejectDate] is being dropped, data loss could occur.

The column [dbo].[Form].[ManagePartnerSignature] is being dropped, data loss could occur.

The column [dbo].[Form].[MPOverallComment] is being dropped, data loss could occur.

The column [dbo].[Form].[OldAgreementForm] is being dropped, data loss could occur.

The column [dbo].[Form].[PartnerSignature] is being dropped, data loss could occur.

The column [dbo].[Form].[ReopenedByName] is being dropped, data loss could occur.

The column [dbo].[Form].[SendToCEOCommentDate] is being dropped, data loss could occur.

The type for column Pdf in table [dbo].[Form] is currently  NVARCHAR (MAX) NULL but is being changed to  VARBINARY (MAX) NULL. Data loss could occur and deployment may fail if the column contains data that is incompatible with type  VARBINARY (MAX) NULL.
*/

IF EXISTS (select top 1 1 from [dbo].[Form])
    RAISERROR (N'Rows were detected. The schema update is terminating because data loss might occur.', 16, 127) WITH NOWAIT

GO
/*
The column [dbo].[Partner].[GroupId] is being dropped, data loss could occur.

The type for column Id in table [dbo].[Partner] is currently  NVARCHAR (100) NOT NULL but is being changed to  UNIQUEIDENTIFIER NOT NULL. Data loss could occur and deployment may fail if the column contains data that is incompatible with type  UNIQUEIDENTIFIER NOT NULL.
*/

IF EXISTS (select top 1 1 from [dbo].[Partner])
    RAISERROR (N'Rows were detected. The schema update is terminating because data loss might occur.', 16, 127) WITH NOWAIT

GO
/*
The column [dbo].[Questionnaire].[Acknowledgment] is being dropped, data loss could occur.

The column [dbo].[Questionnaire].[AcknowledgmentText] is being dropped, data loss could occur.
*/

IF EXISTS (select top 1 1 from [dbo].[Questionnaire])
    RAISERROR (N'Rows were detected. The schema update is terminating because data loss might occur.', 16, 127) WITH NOWAIT

GO
/*
The column FormId on table [dbo].[UserAnswer] must be changed from NULL to NOT NULL. If the table contains data, the ALTER script may not work. To avoid this issue, you must add values to this column for all rows or mark it as allowing NULL values, or enable the generation of smart-defaults as a deployment option.
*/

IF EXISTS (select top 1 1 from [dbo].[UserAnswer])
    RAISERROR (N'Rows were detected. The schema update is terminating because data loss might occur.', 16, 127) WITH NOWAIT

GO
PRINT N'Rename refactoring operation with key d75b347a-e19d-46a4-8342-7fc16c6c4a9c is skipped, element [dbo].[PartnerReviewerUpload].[Year] (SqlSimpleColumn) will not be renamed to Years';


GO
PRINT N'Dropping Default Constraint unnamed constraint on [dbo].[Form]...';


GO
ALTER TABLE [dbo].[Form] DROP CONSTRAINT [DF__Form__Id__59063A47];


GO
PRINT N'Dropping Default Constraint unnamed constraint on [dbo].[Form]...';


GO
ALTER TABLE [dbo].[Form] DROP CONSTRAINT [DF__Form__Status__59FA5E80];


GO
PRINT N'Dropping Default Constraint unnamed constraint on [dbo].[Form]...';


GO
ALTER TABLE [dbo].[Form] DROP CONSTRAINT [DF__Form__Issue__5AEE82B9];


GO
PRINT N'Dropping Default Constraint unnamed constraint on [dbo].[Form]...';


GO
ALTER TABLE [dbo].[Form] DROP CONSTRAINT [DF__Form__IsActive__5BE2A6F2];


GO
PRINT N'Dropping Default Constraint unnamed constraint on [dbo].[Form]...';


GO
ALTER TABLE [dbo].[Form] DROP CONSTRAINT [DF__Form__CreatedOn__5CD6CB2B];


GO
PRINT N'Dropping Default Constraint unnamed constraint on [dbo].[Form]...';


GO
ALTER TABLE [dbo].[Form] DROP CONSTRAINT [DF__Form__CommentedM__5DCAEF64];


GO
PRINT N'Dropping Default Constraint unnamed constraint on [dbo].[Notification]...';


GO
ALTER TABLE [dbo].[Notification] DROP CONSTRAINT [DF__Notificat__Creat__628FA481];


GO
PRINT N'Dropping Default Constraint unnamed constraint on [dbo].[Partner]...';


GO
ALTER TABLE [dbo].[Partner] DROP CONSTRAINT [DF__Partner__IsActiv__6383C8BA];


GO
PRINT N'Dropping Default Constraint unnamed constraint on [dbo].[Partner]...';


GO
ALTER TABLE [dbo].[Partner] DROP CONSTRAINT [DF__Partner__Created__6477ECF3];


GO
PRINT N'Dropping Default Constraint unnamed constraint on [dbo].[Questionnaire]...';


GO
ALTER TABLE [dbo].[Questionnaire] DROP CONSTRAINT [DF__Questionnair__Id__66603565];


GO
PRINT N'Dropping Default Constraint unnamed constraint on [dbo].[Questionnaire]...';


GO
ALTER TABLE [dbo].[Questionnaire] DROP CONSTRAINT [DF__Questionn__Ackno__6754599E];


GO
PRINT N'Dropping Default Constraint unnamed constraint on [dbo].[Questionnaire]...';


GO
ALTER TABLE [dbo].[Questionnaire] DROP CONSTRAINT [DF__Questionn__Gener__68487DD7];


GO
PRINT N'Dropping Default Constraint unnamed constraint on [dbo].[Questionnaire]...';


GO
ALTER TABLE [dbo].[Questionnaire] DROP CONSTRAINT [DF__Questionn__FormS__693CA210];


GO
PRINT N'Dropping Default Constraint unnamed constraint on [dbo].[Questionnaire]...';


GO
ALTER TABLE [dbo].[Questionnaire] DROP CONSTRAINT [DF__Questionn__Statu__6A30C649];


GO
PRINT N'Dropping Default Constraint unnamed constraint on [dbo].[Questionnaire]...';


GO
ALTER TABLE [dbo].[Questionnaire] DROP CONSTRAINT [DF__Questionn__IsAct__6B24EA82];


GO
PRINT N'Dropping Default Constraint unnamed constraint on [dbo].[Questionnaire]...';


GO
ALTER TABLE [dbo].[Questionnaire] DROP CONSTRAINT [DF__Questionn__Creat__6C190EBB];


GO
PRINT N'Dropping Foreign Key [dbo].[FK_Form_Questionnaire]...';


GO
ALTER TABLE [dbo].[Form] DROP CONSTRAINT [FK_Form_Questionnaire];


GO
PRINT N'Dropping Foreign Key [dbo].[FK_UserAnswer_Form]...';


GO
ALTER TABLE [dbo].[UserAnswer] DROP CONSTRAINT [FK_UserAnswer_Form];


GO
PRINT N'Dropping Foreign Key [dbo].[FK_Partner_Group]...';


GO
ALTER TABLE [dbo].[Partner] DROP CONSTRAINT [FK_Partner_Group];


GO
PRINT N'Starting rebuilding table [dbo].[Form]...';


GO
BEGIN TRANSACTION;

SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;

SET XACT_ABORT ON;

CREATE TABLE [dbo].[tmp_ms_xx_Form] (
    [Id]                    UNIQUEIDENTIFIER DEFAULT newsequentialid() NOT NULL,
    [QuestionnaireId]       UNIQUEIDENTIFIER NOT NULL,
    [Year]                  SMALLINT         NOT NULL,
    [Comments]              NVARCHAR (MAX)   NULL,
    [Status]                TINYINT          DEFAULT 0 NOT NULL,
    [IsActive]              BIT              DEFAULT 1 NOT NULL,
    [CreatedBy]             UNIQUEIDENTIFIER NULL,
    [CreatedOn]             DATETIME2 (7)    DEFAULT getutcdate() NULL,
    [ModifiedBy]            UNIQUEIDENTIFIER NULL,
    [ModifiedOn]            DATETIME2 (7)    NULL,
    [PartnerObjectId]       NVARCHAR (100)   NOT NULL,
    [PartnerSubmittionDate] DATETIME2 (7)    NULL,
    [PartnerName]           NVARCHAR (500)   NULL,
    [Pdf]                   VARBINARY (MAX)  NULL,
    CONSTRAINT [tmp_ms_xx_constraint_PK_Form1] PRIMARY KEY CLUSTERED ([Id] ASC)
);

IF EXISTS (SELECT TOP 1 1 
           FROM   [dbo].[Form])
    BEGIN
        INSERT INTO [dbo].[tmp_ms_xx_Form] ([Id], [QuestionnaireId], [Year], [Comments], [Status], [IsActive], [CreatedBy], [CreatedOn], [ModifiedBy], [ModifiedOn], [PartnerObjectId], [PartnerSubmittionDate], [PartnerName], [Pdf])
        SELECT   [Id],
                 [QuestionnaireId],
                 [Year],
                 [Comments],
                 [Status],
                 [IsActive],
                 [CreatedBy],
                 [CreatedOn],
                 [ModifiedBy],
                 [ModifiedOn],
                 [PartnerObjectId],
                 [PartnerSubmittionDate],
                 [PartnerName],
                 CAST ([Pdf] AS VARBINARY (MAX))
        FROM     [dbo].[Form]
        ORDER BY [Id] ASC;
    END

DROP TABLE [dbo].[Form];

EXECUTE sp_rename N'[dbo].[tmp_ms_xx_Form]', N'Form';

EXECUTE sp_rename N'[dbo].[tmp_ms_xx_constraint_PK_Form1]', N'PK_Form', N'OBJECT';

COMMIT TRANSACTION;

SET TRANSACTION ISOLATION LEVEL READ COMMITTED;


GO
PRINT N'Starting rebuilding table [dbo].[Notification]...';


GO
BEGIN TRANSACTION;

SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;

SET XACT_ABORT ON;

CREATE TABLE [dbo].[tmp_ms_xx_Notification] (
    [Id]        BIGINT           IDENTITY (1, 1) NOT NULL,
    [Message]   NVARCHAR (MAX)   NOT NULL,
    [CreatedBy] UNIQUEIDENTIFIER NULL,
    [CreatedOn] DATETIME2 (7)    DEFAULT getutcdate() NOT NULL,
    CONSTRAINT [tmp_ms_xx_constraint_PK_Notification1] PRIMARY KEY CLUSTERED ([Id] ASC)
);

IF EXISTS (SELECT TOP 1 1 
           FROM   [dbo].[Notification])
    BEGIN
        SET IDENTITY_INSERT [dbo].[tmp_ms_xx_Notification] ON;
        INSERT INTO [dbo].[tmp_ms_xx_Notification] ([Id], [Message], [CreatedBy], [CreatedOn])
        SELECT   [Id],
                 [Message],
                 [CreatedBy],
                 [CreatedOn]
        FROM     [dbo].[Notification]
        ORDER BY [Id] ASC;
        SET IDENTITY_INSERT [dbo].[tmp_ms_xx_Notification] OFF;
    END

DROP TABLE [dbo].[Notification];

EXECUTE sp_rename N'[dbo].[tmp_ms_xx_Notification]', N'Notification';

EXECUTE sp_rename N'[dbo].[tmp_ms_xx_constraint_PK_Notification1]', N'PK_Notification', N'OBJECT';

COMMIT TRANSACTION;

SET TRANSACTION ISOLATION LEVEL READ COMMITTED;


GO
PRINT N'Starting rebuilding table [dbo].[Partner]...';


GO
BEGIN TRANSACTION;

SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;

SET XACT_ABORT ON;

CREATE TABLE [dbo].[tmp_ms_xx_Partner] (
    [Id]               UNIQUEIDENTIFIER DEFAULT newsequentialid() NOT NULL,
    [EmployeeId]       INT              NULL,
    [FirstName]        NVARCHAR (150)   NULL,
    [LastName]         NVARCHAR (150)   NULL,
    [DisplayName]      NVARCHAR (300)   NULL,
    [DOB]              DATE             NULL,
    [Mail]             NVARCHAR (200)   NULL,
    [PartnerType]      NVARCHAR (100)   NULL,
    [Department]       NVARCHAR (200)   NULL,
    [Location]         NVARCHAR (200)   NULL,
    [LocationId]       NVARCHAR (200)   NULL,
    [WGroup]           NVARCHAR (200)   NULL,
    [WGroupId]         NVARCHAR (200)   NULL,
    [ServiceLine]      NVARCHAR (200)   NULL,
    [ServiceLineId]    NVARCHAR (200)   NULL,
    [SubServiceLine]   NVARCHAR (200)   NULL,
    [SubServiceLineId] NVARCHAR (200)   NULL,
    [IsActive]         BIT              DEFAULT 1 NOT NULL,
    [CreatedBy]        UNIQUEIDENTIFIER NULL,
    [CreatedOn]        DATETIME2 (7)    DEFAULT getutcdate() NULL,
    [ModifiedBy]       UNIQUEIDENTIFIER NULL,
    [ModifiedOn]       DATETIME2 (7)    NULL,
    CONSTRAINT [tmp_ms_xx_constraint_PK_Partner1] PRIMARY KEY CLUSTERED ([Id] ASC)
);

IF EXISTS (SELECT TOP 1 1 
           FROM   [dbo].[Partner])
    BEGIN
        INSERT INTO [dbo].[tmp_ms_xx_Partner] ([Id], [EmployeeId], [FirstName], [LastName], [DisplayName], [DOB], [Mail], [PartnerType], [Department], [Location], [LocationId], [WGroup], [WGroupId], [ServiceLine], [ServiceLineId], [SubServiceLine], [SubServiceLineId], [IsActive], [CreatedBy], [CreatedOn], [ModifiedBy], [ModifiedOn])
        SELECT   [Id],
                 [EmployeeId],
                 [FirstName],
                 [LastName],
                 [DisplayName],
                 [DOB],
                 [Mail],
                 [PartnerType],
                 [Department],
                 [Location],
                 [LocationId],
                 [WGroup],
                 [WGroupId],
                 [ServiceLine],
                 [ServiceLineId],
                 [SubServiceLine],
                 [SubServiceLineId],
                 [IsActive],
                 [CreatedBy],
                 [CreatedOn],
                 [ModifiedBy],
                 [ModifiedOn]
        FROM     [dbo].[Partner]
        ORDER BY [Id] ASC;
    END

DROP TABLE [dbo].[Partner];

EXECUTE sp_rename N'[dbo].[tmp_ms_xx_Partner]', N'Partner';

EXECUTE sp_rename N'[dbo].[tmp_ms_xx_constraint_PK_Partner1]', N'PK_Partner', N'OBJECT';

COMMIT TRANSACTION;

SET TRANSACTION ISOLATION LEVEL READ COMMITTED;


GO
PRINT N'Starting rebuilding table [dbo].[Questionnaire]...';


GO
BEGIN TRANSACTION;

SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;

SET XACT_ABORT ON;

CREATE TABLE [dbo].[tmp_ms_xx_Questionnaire] (
    [Id]                  UNIQUEIDENTIFIER DEFAULT newsequentialid() NOT NULL,
    [Name]                NVARCHAR (100)   NOT NULL,
    [Year]                SMALLINT         NOT NULL,
    [DefinitionJson]      NVARCHAR (MAX)   NULL,
    [DraftDefinitionJson] NVARCHAR (MAX)   NULL,
    [FormSystemVersion]   INT              DEFAULT 0 NOT NULL,
    [Acknowledgement]     BIT              DEFAULT 0 NOT NULL,
    [AcknowledgementText] NVARCHAR (1500)  NULL,
    [GeneralComments]     BIT              DEFAULT 0 NOT NULL,
    [GeneralCommentsText] NVARCHAR (MAX)   NULL,
    [Status]              TINYINT          DEFAULT 0 NOT NULL,
    [IsActive]            BIT              DEFAULT 1 NOT NULL,
    [CreatedBy]           UNIQUEIDENTIFIER NULL,
    [CreatedOn]           DATETIME2 (7)    DEFAULT getutcdate() NULL,
    [ModifiedBy]          UNIQUEIDENTIFIER NULL,
    [ModifiedOn]          DATETIME2 (7)    NULL,
    CONSTRAINT [tmp_ms_xx_constraint_PK_Questionnaire1] PRIMARY KEY CLUSTERED ([Id] ASC)
);

IF EXISTS (SELECT TOP 1 1 
           FROM   [dbo].[Questionnaire])
    BEGIN
        INSERT INTO [dbo].[tmp_ms_xx_Questionnaire] ([Id], [Name], [Year], [DefinitionJson], [DraftDefinitionJson], [GeneralComments], [GeneralCommentsText], [FormSystemVersion], [Status], [IsActive], [CreatedBy], [CreatedOn], [ModifiedBy], [ModifiedOn])
        SELECT   [Id],
                 [Name],
                 [Year],
                 [DefinitionJson],
                 [DraftDefinitionJson],
                 [GeneralComments],
                 [GeneralCommentsText],
                 [FormSystemVersion],
                 [Status],
                 [IsActive],
                 [CreatedBy],
                 [CreatedOn],
                 [ModifiedBy],
                 [ModifiedOn]
        FROM     [dbo].[Questionnaire]
        ORDER BY [Id] ASC;
    END

DROP TABLE [dbo].[Questionnaire];

EXECUTE sp_rename N'[dbo].[tmp_ms_xx_Questionnaire]', N'Questionnaire';

EXECUTE sp_rename N'[dbo].[tmp_ms_xx_constraint_PK_Questionnaire1]', N'PK_Questionnaire', N'OBJECT';

COMMIT TRANSACTION;

SET TRANSACTION ISOLATION LEVEL READ COMMITTED;


GO
PRINT N'Altering Table [dbo].[UserAnswer]...';


GO
ALTER TABLE [dbo].[UserAnswer] ALTER COLUMN [FormId] UNIQUEIDENTIFIER NOT NULL;


GO
PRINT N'Creating Table [dbo].[FormStatus]...';


GO
CREATE TABLE [dbo].[FormStatus] (
    [Id]                TINYINT       NOT NULL,
    [Name]              NVARCHAR (50) NOT NULL,
    [EnglishDislayName] NVARCHAR (50) NULL,
    [FrenchDisplayName] NVARCHAR (50) NULL,
    PRIMARY KEY CLUSTERED ([Id] ASC)
);


GO
PRINT N'Creating Table [dbo].[PartnerReferenceData]...';


GO
CREATE TABLE [dbo].[PartnerReferenceData] (
    [Id]         UNIQUEIDENTIFIER NOT NULL,
    [PartnerId]  UNIQUEIDENTIFIER NOT NULL,
    [Year]       SMALLINT         NOT NULL,
    [Cycle]      TINYINT          NOT NULL,
    [MetaId]     UNIQUEIDENTIFIER NOT NULL,
    [Data]       NVARCHAR (MAX)   NULL,
    [CreatedBy]  UNIQUEIDENTIFIER NULL,
    [CreatedOn]  DATETIME2 (7)    NULL,
    [ModifiedBy] UNIQUEIDENTIFIER NULL,
    [ModifiedOn] DATETIME2 (7)    NULL,
    CONSTRAINT [PK_PartnerReferenceData] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [UK_PartnerReferenceData_Partner_Year_Cycle_Meta] UNIQUE NONCLUSTERED ([PartnerId] ASC, [Year] ASC, [Cycle] ASC, [MetaId] ASC)
);


GO
PRINT N'Creating Table [dbo].[PartnerReferenceDataMeta]...';


GO
CREATE TABLE [dbo].[PartnerReferenceDataMeta] (
    [Id]         UNIQUEIDENTIFIER NOT NULL,
    [FileName]   NVARCHAR (500)   NOT NULL,
    [Year]       SMALLINT         NOT NULL,
    [Cycle]      TINYINT          NOT NULL,
    [IsActive]   BIT              NOT NULL,
    [CreatedBy]  UNIQUEIDENTIFIER NULL,
    [CreatedOn]  DATETIME2 (7)    NULL,
    [ModifiedBy] UNIQUEIDENTIFIER NULL,
    [ModifiedOn] DATETIME2 (7)    NULL,
    CONSTRAINT [PK_PartnerReferenceDataMeta] PRIMARY KEY CLUSTERED ([Id] ASC)
);


GO
PRINT N'Creating Table [dbo].[PartnerReferenceDataMetaDetails]...';


GO
CREATE TABLE [dbo].[PartnerReferenceDataMetaDetails] (
    [Id]                   UNIQUEIDENTIFIER NOT NULL,
    [MetaId]               UNIQUEIDENTIFIER NOT NULL,
    [ColumnName]           NVARCHAR (200)   NOT NULL,
    [NormalizedColumnName] NVARCHAR (200)   NOT NULL,
    [ColumnDataType]       TINYINT          NOT NULL,
    [ColumnOrder]          SMALLINT         NOT NULL,
    [CreatedBy]            UNIQUEIDENTIFIER NULL,
    [CreatedOn]            DATETIME2 (7)    NULL,
    [ModifiedBy]           UNIQUEIDENTIFIER NULL,
    [ModifiedOn]           DATETIME2 (7)    NULL,
    CONSTRAINT [PK_PartnerReferenceDataMetaDetails] PRIMARY KEY CLUSTERED ([Id] ASC)
);


GO
PRINT N'Creating Table [dbo].[PartnerReferenceDataUpload]...';


GO
CREATE TABLE [dbo].[PartnerReferenceDataUpload] (
    [Id]                UNIQUEIDENTIFIER NOT NULL,
    [UploadFileName]    NVARCHAR (500)   NOT NULL,
    [FileContent]       VARBINARY (MAX)  NULL,
    [Year]              SMALLINT         NOT NULL,
    [Cycle]             TINYINT          NOT NULL,
    [MetaId]            UNIQUEIDENTIFIER NOT NULL,
    [Status]            TINYINT          NOT NULL,
    [ValidationSummary] NVARCHAR (MAX)   NULL,
    [CreatedBy]         UNIQUEIDENTIFIER NULL,
    [CreatedOn]         DATETIME2 (7)    NULL,
    [ModifiedBy]        UNIQUEIDENTIFIER NULL,
    [ModifiedOn]        DATETIME2 (7)    NULL,
    CONSTRAINT [PK_PartnerReferenceDataUpload] PRIMARY KEY CLUSTERED ([Id] ASC)
);


GO
PRINT N'Creating Table [dbo].[PartnerReferenceDataUploadDetails]...';


GO
CREATE TABLE [dbo].[PartnerReferenceDataUploadDetails] (
    [Id]                           UNIQUEIDENTIFIER NOT NULL,
    [PartnerReferenceDataUploadId] UNIQUEIDENTIFIER NOT NULL,
    [RowId]                        INT              NOT NULL,
    [Data]                         NVARCHAR (MAX)   NULL,
    [ValidationError]              NVARCHAR (500)   NULL,
    [CreatedBy]                    UNIQUEIDENTIFIER NULL,
    [CreatedOn]                    DATETIME2 (7)    NULL,
    [ModifiedBy]                   UNIQUEIDENTIFIER NULL,
    [ModifiedOn]                   DATETIME2 (7)    NULL,
    CONSTRAINT [PK_PartnerReferenceDataUploadDetails] PRIMARY KEY CLUSTERED ([Id] ASC)
);


GO
PRINT N'Creating Table [dbo].[PartnerReviewer]...';


GO
CREATE TABLE [dbo].[PartnerReviewer] (
    [Id]                    UNIQUEIDENTIFIER NOT NULL,
    [Year]                  SMALLINT         NOT NULL,
    [PartnerId]             UNIQUEIDENTIFIER NOT NULL,
    [Exempt]                BIT              NOT NULL,
    [LeadershipRole]        NVARCHAR (100)   NULL,
    [PrimaryReviewerId]     UNIQUEIDENTIFIER NULL,
    [PrimaryReviewerName]   NVARCHAR (500)   NULL,
    [SecondaryReviewerId]   UNIQUEIDENTIFIER NULL,
    [SecondaryReviewerName] NVARCHAR (500)   NULL,
    [CreatedBy]             UNIQUEIDENTIFIER NULL,
    [CreatedOn]             DATETIME2 (7)    NULL,
    [ModifiedBy]            UNIQUEIDENTIFIER NULL,
    [ModifiedOn]            DATETIME2 (7)    NULL,
    CONSTRAINT [PK_PartnerReviewer] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [AK_PartnerReviewer_PartnerYear] UNIQUE NONCLUSTERED ([PartnerId] ASC, [Year] ASC)
);


GO
PRINT N'Creating Table [dbo].[PartnerReviewerUpload]...';


GO
CREATE TABLE [dbo].[PartnerReviewerUpload] (
    [Id]                INT              IDENTITY (1, 1) NOT NULL,
    [Years]             NVARCHAR (100)   NOT NULL,
    [UploadFileName]    NVARCHAR (500)   NULL,
    [ValidationSummary] NVARCHAR (MAX)   NULL,
    [FileContent]       VARBINARY (MAX)  NULL,
    [Status]            TINYINT          NOT NULL,
    [CreatedBy]         UNIQUEIDENTIFIER NULL,
    [CreatedOn]         DATETIME2 (7)    NULL,
    [ModifiedBy]        UNIQUEIDENTIFIER NULL,
    [ModifiedOn]        DATETIME2 (7)    NULL,
    CONSTRAINT [PK_PartnerReviewerUpload] PRIMARY KEY CLUSTERED ([Id] ASC)
);


GO
PRINT N'Creating Table [dbo].[PartnerReviewerUploadDetails]...';


GO
CREATE TABLE [dbo].[PartnerReviewerUploadDetails] (
    [Id]                      BIGINT         IDENTITY (1, 1) NOT NULL,
    [PartnerReviewerUploadId] INT            NOT NULL,
    [RowId]                   INT            NOT NULL,
    [EmployeeId]              NVARCHAR (50)  NULL,
    [EmployeeName]            NVARCHAR (100) NULL,
    [Exempt]                  NVARCHAR (10)  NULL,
    [LeadershipRole]          NVARCHAR (100) NULL,
    [PrimaryReviewerId]       NVARCHAR (50)  NULL,
    [PrimaryReviewerName]     NVARCHAR (100) NULL,
    [SecondaryReviewerId]     NVARCHAR (50)  NULL,
    [SecondaryReviewerName]   NVARCHAR (100) NULL,
    [ValidationError]         NVARCHAR (500) NULL,
    CONSTRAINT [PK_PartnerReviewerUploadDetails] PRIMARY KEY CLUSTERED ([Id] ASC)
);


GO
PRINT N'Creating Table [dbo].[QuestionnaireStatus]...';


GO
CREATE TABLE [dbo].[QuestionnaireStatus] (
    [Id]                TINYINT       NOT NULL,
    [Name]              NVARCHAR (50) NOT NULL,
    [EnglishDislayName] NVARCHAR (50) NULL,
    [FrenchDisplayName] NVARCHAR (50) NULL,
    PRIMARY KEY CLUSTERED ([Id] ASC)
);


GO
PRINT N'Creating Default Constraint unnamed constraint on [dbo].[PartnerReferenceData]...';


GO
ALTER TABLE [dbo].[PartnerReferenceData]
    ADD DEFAULT newsequentialid() FOR [Id];


GO
PRINT N'Creating Default Constraint unnamed constraint on [dbo].[PartnerReferenceData]...';


GO
ALTER TABLE [dbo].[PartnerReferenceData]
    ADD DEFAULT getutcdate() FOR [CreatedOn];


GO
PRINT N'Creating Default Constraint unnamed constraint on [dbo].[PartnerReferenceDataMeta]...';


GO
ALTER TABLE [dbo].[PartnerReferenceDataMeta]
    ADD DEFAULT newsequentialid() FOR [Id];


GO
PRINT N'Creating Default Constraint unnamed constraint on [dbo].[PartnerReferenceDataMeta]...';


GO
ALTER TABLE [dbo].[PartnerReferenceDataMeta]
    ADD DEFAULT 1 FOR [IsActive];


GO
PRINT N'Creating Default Constraint unnamed constraint on [dbo].[PartnerReferenceDataMeta]...';


GO
ALTER TABLE [dbo].[PartnerReferenceDataMeta]
    ADD DEFAULT getutcdate() FOR [CreatedOn];


GO
PRINT N'Creating Default Constraint unnamed constraint on [dbo].[PartnerReferenceDataMetaDetails]...';


GO
ALTER TABLE [dbo].[PartnerReferenceDataMetaDetails]
    ADD DEFAULT newsequentialid() FOR [Id];


GO
PRINT N'Creating Default Constraint unnamed constraint on [dbo].[PartnerReferenceDataMetaDetails]...';


GO
ALTER TABLE [dbo].[PartnerReferenceDataMetaDetails]
    ADD DEFAULT getutcdate() FOR [CreatedOn];


GO
PRINT N'Creating Default Constraint unnamed constraint on [dbo].[PartnerReferenceDataUpload]...';


GO
ALTER TABLE [dbo].[PartnerReferenceDataUpload]
    ADD DEFAULT newsequentialid() FOR [Id];


GO
PRINT N'Creating Default Constraint unnamed constraint on [dbo].[PartnerReferenceDataUpload]...';


GO
ALTER TABLE [dbo].[PartnerReferenceDataUpload]
    ADD DEFAULT 0 FOR [Status];


GO
PRINT N'Creating Default Constraint unnamed constraint on [dbo].[PartnerReferenceDataUpload]...';


GO
ALTER TABLE [dbo].[PartnerReferenceDataUpload]
    ADD DEFAULT getutcdate() FOR [CreatedOn];


GO
PRINT N'Creating Default Constraint unnamed constraint on [dbo].[PartnerReferenceDataUploadDetails]...';


GO
ALTER TABLE [dbo].[PartnerReferenceDataUploadDetails]
    ADD DEFAULT newsequentialid() FOR [Id];


GO
PRINT N'Creating Default Constraint unnamed constraint on [dbo].[PartnerReferenceDataUploadDetails]...';


GO
ALTER TABLE [dbo].[PartnerReferenceDataUploadDetails]
    ADD DEFAULT getutcdate() FOR [CreatedOn];


GO
PRINT N'Creating Default Constraint unnamed constraint on [dbo].[PartnerReviewer]...';


GO
ALTER TABLE [dbo].[PartnerReviewer]
    ADD DEFAULT newsequentialid() FOR [Id];


GO
PRINT N'Creating Default Constraint unnamed constraint on [dbo].[PartnerReviewer]...';


GO
ALTER TABLE [dbo].[PartnerReviewer]
    ADD DEFAULT 0 FOR [Exempt];


GO
PRINT N'Creating Default Constraint unnamed constraint on [dbo].[PartnerReviewer]...';


GO
ALTER TABLE [dbo].[PartnerReviewer]
    ADD DEFAULT getutcdate() FOR [CreatedOn];


GO
PRINT N'Creating Default Constraint unnamed constraint on [dbo].[PartnerReviewerUpload]...';


GO
ALTER TABLE [dbo].[PartnerReviewerUpload]
    ADD DEFAULT 0 FOR [Status];


GO
PRINT N'Creating Default Constraint unnamed constraint on [dbo].[PartnerReviewerUpload]...';


GO
ALTER TABLE [dbo].[PartnerReviewerUpload]
    ADD DEFAULT getutcdate() FOR [CreatedOn];


GO
PRINT N'Creating Foreign Key [dbo].[FK_Form_Questionnaire]...';


GO
ALTER TABLE [dbo].[Form] WITH NOCHECK
    ADD CONSTRAINT [FK_Form_Questionnaire] FOREIGN KEY ([QuestionnaireId]) REFERENCES [dbo].[Questionnaire] ([Id]);


GO
PRINT N'Creating Foreign Key [dbo].[FK_UserAnswer_Form]...';


GO
ALTER TABLE [dbo].[UserAnswer] WITH NOCHECK
    ADD CONSTRAINT [FK_UserAnswer_Form] FOREIGN KEY ([FormId]) REFERENCES [dbo].[Form] ([Id]);


GO
PRINT N'Creating Foreign Key [dbo].[FK_Form_Status]...';


GO
ALTER TABLE [dbo].[Form] WITH NOCHECK
    ADD CONSTRAINT [FK_Form_Status] FOREIGN KEY ([Status]) REFERENCES [dbo].[FormStatus] ([Id]);


GO
PRINT N'Creating Foreign Key [dbo].[FK_Questionnaire_Status]...';


GO
ALTER TABLE [dbo].[Questionnaire] WITH NOCHECK
    ADD CONSTRAINT [FK_Questionnaire_Status] FOREIGN KEY ([Status]) REFERENCES [dbo].[QuestionnaireStatus] ([Id]);


GO
PRINT N'Creating Foreign Key [dbo].[FK_PartnerReferenceData_Partner]...';


GO
ALTER TABLE [dbo].[PartnerReferenceData] WITH NOCHECK
    ADD CONSTRAINT [FK_PartnerReferenceData_Partner] FOREIGN KEY ([PartnerId]) REFERENCES [dbo].[Partner] ([Id]);


GO
PRINT N'Creating Foreign Key [dbo].[FK_PartnerReferenceData_Meta]...';


GO
ALTER TABLE [dbo].[PartnerReferenceData] WITH NOCHECK
    ADD CONSTRAINT [FK_PartnerReferenceData_Meta] FOREIGN KEY ([MetaId]) REFERENCES [dbo].[PartnerReferenceDataMeta] ([Id]);


GO
PRINT N'Creating Foreign Key [dbo].[FK_PartnerReferenceDataMetaDetails_Meta]...';


GO
ALTER TABLE [dbo].[PartnerReferenceDataMetaDetails] WITH NOCHECK
    ADD CONSTRAINT [FK_PartnerReferenceDataMetaDetails_Meta] FOREIGN KEY ([MetaId]) REFERENCES [dbo].[PartnerReferenceDataMeta] ([Id]);


GO
PRINT N'Creating Foreign Key [dbo].[FK_PartnerReferenceDataUpload_Meta]...';


GO
ALTER TABLE [dbo].[PartnerReferenceDataUpload] WITH NOCHECK
    ADD CONSTRAINT [FK_PartnerReferenceDataUpload_Meta] FOREIGN KEY ([MetaId]) REFERENCES [dbo].[PartnerReferenceDataMeta] ([Id]);


GO
PRINT N'Creating Foreign Key [dbo].[FK_PartnerReferenceDataUploadDetails_Upload]...';


GO
ALTER TABLE [dbo].[PartnerReferenceDataUploadDetails] WITH NOCHECK
    ADD CONSTRAINT [FK_PartnerReferenceDataUploadDetails_Upload] FOREIGN KEY ([PartnerReferenceDataUploadId]) REFERENCES [dbo].[PartnerReferenceDataUpload] ([Id]);


GO
PRINT N'Creating Foreign Key [dbo].[FK_PartnerReviewer_Partner]...';


GO
ALTER TABLE [dbo].[PartnerReviewer] WITH NOCHECK
    ADD CONSTRAINT [FK_PartnerReviewer_Partner] FOREIGN KEY ([PartnerId]) REFERENCES [dbo].[Partner] ([Id]);


GO
PRINT N'Creating Foreign Key [dbo].[FK_PartnerReviewer_PrimaryReviewer]...';


GO
ALTER TABLE [dbo].[PartnerReviewer] WITH NOCHECK
    ADD CONSTRAINT [FK_PartnerReviewer_PrimaryReviewer] FOREIGN KEY ([PrimaryReviewerId]) REFERENCES [dbo].[Partner] ([Id]);


GO
PRINT N'Creating Foreign Key [dbo].[FK_PartnerReviewer_SecondaryReviewer]...';


GO
ALTER TABLE [dbo].[PartnerReviewer] WITH NOCHECK
    ADD CONSTRAINT [FK_PartnerReviewer_SecondaryReviewer] FOREIGN KEY ([SecondaryReviewerId]) REFERENCES [dbo].[Partner] ([Id]);


GO
PRINT N'Creating Foreign Key [dbo].[FK_PartnerReviewerUploadDetails_PartnerReviewerUpload]...';


GO
ALTER TABLE [dbo].[PartnerReviewerUploadDetails] WITH NOCHECK
    ADD CONSTRAINT [FK_PartnerReviewerUploadDetails_PartnerReviewerUpload] FOREIGN KEY ([PartnerReviewerUploadId]) REFERENCES [dbo].[PartnerReviewerUpload] ([Id]);


GO
-- Refactoring step to update target server with deployed transaction logs
IF NOT EXISTS (SELECT OperationKey FROM [dbo].[__RefactorLog] WHERE OperationKey = 'd75b347a-e19d-46a4-8342-7fc16c6c4a9c')
INSERT INTO [dbo].[__RefactorLog] (OperationKey) values ('d75b347a-e19d-46a4-8342-7fc16c6c4a9c')

GO

GO
MERGE INTO [dbo].[Language] AS Target
USING (VALUES
  (1,'en','English')
 ,(2,'fr','French')
) AS Source ([Id],[Code],[Name])
ON (Target.[Id] = Source.[Id])
WHEN MATCHED AND (Target.[Code] <> Source.[Code] OR Target.[Name] <> Source.[Name]) THEN
	UPDATE SET
		[Code] = Source.[Code], 
		[Name] = Source.[Name]
WHEN NOT MATCHED BY TARGET THEN
	INSERT([Id],[Code],[Name])
	VALUES(Source.[Id],Source.[Code],Source.[Name])
WHEN NOT MATCHED BY SOURCE THEN 
	DELETE;
GO

-- Delete RolePermission records for roles not in the source dataset
DELETE FROM [dbo].[RolePermission]
WHERE [RoleId] NOT IN (3, 15, 16, 17);

MERGE INTO [dbo].[Role] AS Target
USING (VALUES
  (15, 'Partner Plans Administrator')
 ,(3, 'Active Partner')
 ,(16, 'New Partner')
 ,(17, 'Partner Plans Executive Leadership')
) AS Source ([Id],[Name])
ON (Target.[Id] = Source.[Id])
WHEN MATCHED AND (Target.[Name] <> Source.[Name]) THEN
	UPDATE SET
		[Name] = Source.[Name]
WHEN NOT MATCHED BY TARGET THEN
	INSERT([Id],[Name])
	VALUES(Source.[Id],Source.[Name])
WHEN NOT MATCHED BY SOURCE THEN
	DELETE;
GO


MERGE INTO [dbo].[Permission] AS Target
USING (VALUES
  (6, 'Partner Plans Login')
 ,(7, 'Track Own Partner Plan')
 ,(8, 'Track All Partner Plans')
 ,(9, 'Draft Submit Partner Plan')
 ,(10, 'Edit Partner Plans Under Review')
 ,(11, 'Partner Plans Final Submission')
 ,(12, 'Mid End Year Self Assessment')
 ,(13, 'Mid End Year Reviewer Assessment')
 ,(14, 'View Submitted Partner Plans')
 ,(15, 'Edit Submitted Partner Plans')
 ,(16, 'Export Plan Data To Excel')
 ,(17, 'Manage Partner Reviewer Relationships')
 ,(18, 'Upload KPI Data')
 ,(19, 'Edit Publish Input Form')
) AS Source ([Id],[Name])
ON (Target.[Id] = Source.[Id])
WHEN MATCHED AND (Target.[Name] <> Source.[Name]) THEN
	UPDATE SET
		[Name] = Source.[Name]
WHEN NOT MATCHED BY TARGET THEN
	INSERT([Id],[Name])
	VALUES(Source.[Id],Source.[Name])
WHEN NOT MATCHED BY SOURCE THEN
	DELETE;
GO

MERGE INTO [dbo].[RolePermission] AS Target
USING (VALUES
   -- PPAdministrator (Role ID 15) - Full administrative permissions
   (15, 6)   -- Login
  ,(15, 7)   -- Track Own Partner Plan
  ,(15, 8)   -- Track All Partner Plans
  ,(15, 9)   -- Draft Submit Partner Plan
  ,(15, 10)   -- Edit Partner Plans Under Review
  ,(15, 11)   -- Partner Plans Final Submission
  ,(15, 12)   -- Mid End Year Self Assessment
  ,(15, 13)   -- Mid End Year Reviewer Assessment
  ,(15, 14)   -- View Submitted Partner Plans
  ,(15, 15)  -- Edit Submitted Partner Plans
  ,(15, 16)  -- Export Plan Data To Excel
  ,(15, 17)  -- Manage Partner Reviewer Relationships
  ,(15, 18)  -- Upload KPI Data
  ,(15, 19)  -- Edit Publish Input Form
   -- Partner (Role ID 3) - Standard Active partner permissions
  ,(3, 6)   -- Login
  ,(3, 7)   -- Track Own Partner Plan
  ,(3, 9)   -- Draft Submit Partner Plan
  ,(3, 10)   -- Edit Partner Plans Under Review (own)
  ,(3, 12)   -- Mid End Year Self Assessment (own)
  ,(3, 14)   -- View Submitted Partner Plans (own)
   -- NewPartner (Role ID 16) - Limited permissions for new partners
  ,(16, 6)   -- Login
  ,(16, 7)   -- Track Own Partner Plan
  ,(16, 9)   -- Draft Submit Partner Plan
  ,(16, 12)   -- Mid End Year Self Assessment (own)
  ,(16, 14)   -- View Submitted Partner Plans (own)
   -- PPExecutiveLeadership (Role ID 17) - Executive level permissions
  ,(17, 6)   -- Login
  ,(17, 7)   -- Track Own Partner Plan
  ,(17, 8)   -- Track All Partner Plans
  ,(17, 10)   -- Edit Partner Plans Under Review
  ,(17, 11)   -- Partner Plans Final Submission
  ,(17, 13)   -- Mid End Year Reviewer Assessment
  ,(17, 14)   -- View Submitted Partner Plans
  ,(17, 15)  -- Edit Submitted Partner Plans
  ,(17, 16)  -- Export Plan Data To Excel
) AS Source ([RoleId],[PermissionId])
ON (Target.[RoleId] = Source.[RoleId] and Target.[PermissionId] = Source.[PermissionId])
--WHEN MATCHED AND (Target.[Name] <> Source.[Name]) THEN
--	UPDATE SET
--		[Name] = Source.[Name]
WHEN NOT MATCHED BY TARGET THEN
	INSERT([RoleId],[PermissionId])
	VALUES(Source.[RoleId],Source.[PermissionId])
WHEN NOT MATCHED BY SOURCE THEN
	DELETE;
GO

---- SET IDENTITY_INSERT [dbo].[Timezone] ON
 
--MERGE INTO [dbo].[Timezone] AS Target
--USING (VALUES
--  (1,'-12:00','Dateline Standard Time',-12.00)
-- ,(2,'-11:00','UTC-11',-11.00)
-- ,(3,'-10:00','Aleutian Standard Time',-10.00)
-- ,(4,'-09:30','Marquesas Standard Time',-9.50)
-- ,(5,'-09:00','UTC-09',-9.00)
-- ,(6,'-08:00','Pacific Standard Time',-8.00)
-- ,(7,'-07:00','Mountain Standard Time',-7.00)
-- ,(8,'-06:00','Canada Central Standard Time',-6.00)
-- ,(9,'-05:00','Eastern Standard Time',-5.00)
-- ,(10,'-04:00','Atlantic Standard Time',-4.00)
-- ,(11,'-03:30','Newfoundland Standard Time',-3.50)
-- ,(12,'-03:00','Tocantins Standard Time',-3.00)
-- ,(13,'-02:00','UTC-02',-2.00)
-- ,(14,'-01:00','Azores Standard Time',-1.00)
-- ,(15,'+00:00','GMT Standard Time',0.00)
-- ,(16,'+01:00','Morocco Standard Time',1.00)
-- ,(17,'+02:00','Libya Standard Time',2.00)
-- ,(18,'+03:00','Turkey Standard Time',3.00)
-- ,(19,'+03:30','Iran Standard Time',3.50)
-- ,(20,'+04:00','Astrakhan Standard Time',4.00)
-- ,(21,'+04:30','Afghanistan Standard Time',4.50)
-- ,(22,'+05:00','West Asia Standard Time',5.00)
-- ,(23,'+05:30','India Standard Time',5.50)
-- ,(24,'+05:45','Nepal Standard Time',5.75)
-- ,(25,'+06:00','Central Asia Standard Time',6.00)
-- ,(26,'+06:30','Myanmar Standard Time',6.50)
-- ,(27,'+07:00','Altai Standard Time',7.00)
-- ,(28,'+08:00','China Standard Time',8.00)
-- ,(29,'+08:45','Aus Central W. Standard Time',8.75)
-- ,(30,'+09:00','Transbaikal Standard Time',9.00)
-- ,(31,'+09:30','Cen. Australia Standard Time',9.50)
-- ,(32,'+10:00','E. Australia Standard Time',10.00)
-- ,(33,'+10:30','Lord Howe Standard Time',10.50)
-- ,(34,'+11:00','Bougainville Standard Time',11.00)
-- ,(35,'+12:00','New Zealand Standard Time',12.00)
-- ,(36,'+12:45','Chatham Islands Standard Time',12.75)
-- ,(37,'+13:00','Tonga Standard Time',13.00)
-- ,(38,'+14:00','Line Islands Standard Time',14.00)
--) AS Source ([Id],[Code],[Name],[Offset])
--ON (Target.[Id] = Source.[Id])
--WHEN MATCHED AND (Target.[Code] <> Source.[Code] OR Target.[Name] <> Source.[Name] OR Target.[Offset] <> Source.[Offset]) THEN
-- UPDATE SET
-- [Code] = Source.[Code], 
--[Name] = Source.[Name], 
--[Offset] = Source.[Offset]
--WHEN NOT MATCHED BY TARGET THEN
-- INSERT([Id],[Code],[Name],[Offset])
-- VALUES(Source.[Id],Source.[Code],Source.[Name],Source.[Offset])
--WHEN NOT MATCHED BY SOURCE THEN 
-- DELETE;

----SET IDENTITY_INSERT [dbo].[Timezone] OFF;
--GO

-- Note: AuthProvider settings corporate with startup.cs AddOpenIdConnect settings in Identity Server project.
MERGE INTO [identity].[AuthProvider] AS Target
USING (VALUES
  ('App', 'Default Database driving authentication', 1)
 ,('BDO-ITINV-AAD', 'BDO Canada LLP IT Innovation Azure AD', 1)
 ,('BDO-AAD', 'BDO Canada LLP Azure AD', 1)
 ,('FELIX-AAD', 'Felix Personal Azure AD', 1)
) AS Source ([Id],[Name], [IsActive])
ON (Target.[Id] = Source.[Id])
WHEN MATCHED AND (Target.[Name] <> Source.[Name] or Target.[IsActive]<> Source.[IsActive]) THEN
	UPDATE SET
		[Name] = Source.[Name]		
WHEN NOT MATCHED BY TARGET THEN
	INSERT([Id],[Name],[IsActive])
	VALUES(Source.[Id],Source.[Name], Source.[IsActive])
WHEN NOT MATCHED BY SOURCE THEN 
	DELETE;
GO

-- password = "Password1"
MERGE INTO [dbo].[User] AS Target
USING (VALUES
  ('BFD02B5C-2408-4870-8E1F-06C7A72187F6', 'APP', 'ppadmin',0x999000A49E8DD91ED5726EA0AF1745C8F712640D6563F7FDE1977013B730CB3C9D78F99EC07FC59B0ADEAB5CBA0380DA, 0xE73685F552,0,'PP','Administrator','<EMAIL>',1,1)
  ,('D3FB7F6A-6487-4190-9D9F-0ECD7A25E239', 'APP','partner1',0x999000A49E8DD91ED5726EA0AF1745C8F712640D6563F7FDE1977013B730CB3C9D78F99EC07FC59B0ADEAB5CBA0380DA, 0xE73685F552,0,'Partner','User','<EMAIL>',1,1)
  ,('B6BDCA35-4D96-4C26-B6B2-D15E285147DB', 'APP', 'newpartner',0x999000A49E8DD91ED5726EA0AF1745C8F712640D6563F7FDE1977013B730CB3C9D78F99EC07FC59B0ADEAB5CBA0380DA, 0xE73685F552,0,'New','Partner','<EMAIL>',1,1)
  ,('A1B2C3D4-5E6F-7890-ABCD-EF1234567890', 'APP', 'ppexec',0x999000A49E8DD91ED5726EA0AF1745C8F712640D6563F7FDE1977013B730CB3C9D78F99EC07FC59B0ADEAB5CBA0380DA, 0xE73685F552,0,'PP Executive','Leadership','<EMAIL>',1,1)

) AS Source ([Id], [AuthProviderId], [Username],[Password],[Salt],[IsTempPasswordEnabled],[FirstName],[LastName],[Email],[LanguageId],[IsActive])
ON (Target.[Id] = Source.[Id])
WHEN MATCHED AND (Target.[Username] <> Source.[Username] OR Target.[AuthProviderId]<> Source.[AuthProviderId] OR Target.[Password] <> Source.[Password] OR Target.[Salt] <> Source.[Salt] OR Target.[IsTempPasswordEnabled] <> Source.[IsTempPasswordEnabled] 
OR Target.[FirstName] <> Source.[FirstName] OR Target.[LastName] <> Source.[LastName] OR Target.[Email] <> Source.[Email] 
OR Target.[LanguageId] <> Source.[LanguageId] OR Target.[IsActive] <> Source.[IsActive] ) THEN
 UPDATE SET
 [Username] = Source.[Username], 
[Password] = Source.[Password], 
[Salt] = Source.[Salt], 
[IsTempPasswordEnabled] = Source.[IsTempPasswordEnabled], 
[FirstName] = Source.[FirstName], 
[LastName] = Source.[LastName], 
[Email] = Source.[Email], 
[LanguageId] = Source.[LanguageId], 
[IsActive] = Source.[IsActive],
[AuthProviderId] = Source.[AuthProviderId]
WHEN NOT MATCHED BY TARGET THEN
 INSERT([Id],[AuthProviderId],[Username],[Password],[Salt],[IsTempPasswordEnabled],[FirstName],[LastName],[Email],[LanguageId],[IsActive])
 VALUES(Source.[Id],Source.[AuthProviderId], Source.[Username],Source.[Password],Source.[Salt],Source.[IsTempPasswordEnabled],Source.[FirstName],Source.[LastName],Source.[Email],Source.[LanguageId],Source.[IsActive]);
-- WHEN NOT MATCHED BY SOURCE THEN 
-- DELETE;
GO

 MERGE INTO [dbo].[UserRole] AS Target
USING (VALUES
   ('BFD02B5C-2408-4870-8E1F-06C7A72187F6', 15)  -- PP Administrator
  ,('D3FB7F6A-6487-4190-9D9F-0ECD7A25E239', 3)  -- Active Partner
  ,('B6BDCA35-4D96-4C26-B6B2-D15E285147DB', 16)  -- New Partner
  ,('A1B2C3D4-5E6F-7890-ABCD-EF1234567890', 17)  -- PP Executive Leadership
) AS Source ([UserId],[RoleId])
ON (Target.[RoleId] = Source.[RoleId] and Target.[UserId] = Source.[UserId])
--WHEN MATCHED AND (Target.[Name] <> Source.[Name]) THEN
--	UPDATE SET
--		[Name] = Source.[Name]		
WHEN NOT MATCHED BY TARGET THEN
	INSERT([UserId], [RoleId])
	VALUES(Source.[UserId], Source.[RoleId]);
--WHEN NOT MATCHED BY SOURCE THEN 
--	DELETE;
GO

-- FormStatus lookup table seed data
MERGE INTO [dbo].[FormStatus] AS Target
USING (VALUES
  (0, 'Draft', 'Draft', 'Brouillon')
 ,(1, 'Submitted', 'Submitted', 'Soumis')
 ,(2, 'Approved', 'Approved', 'Approuvé')
 ,(3, 'Rejected', 'Rejected', 'Rejeté')
 ,(4, 'Reopened', 'Reopened', 'Rouvert')
 ,(5, 'Closed', 'Closed', 'Fermé')
) AS Source ([Id],[Name],[EnglishDislayName],[FrenchDisplayName])
ON (Target.[Id] = Source.[Id])
WHEN MATCHED AND (Target.[Name] <> Source.[Name] OR Target.[EnglishDislayName] <> Source.[EnglishDislayName] OR Target.[FrenchDisplayName] <> Source.[FrenchDisplayName]) THEN
	UPDATE SET
		[Name] = Source.[Name],
		[EnglishDislayName] = Source.[EnglishDislayName],
		[FrenchDisplayName] = Source.[FrenchDisplayName]
WHEN NOT MATCHED BY TARGET THEN
	INSERT([Id],[Name],[EnglishDislayName],[FrenchDisplayName])
	VALUES(Source.[Id],Source.[Name],Source.[EnglishDislayName],Source.[FrenchDisplayName])
WHEN NOT MATCHED BY SOURCE THEN
	DELETE;
GO

-- QuestionnaireStatus lookup table seed data
MERGE INTO [dbo].[QuestionnaireStatus] AS Target
USING (VALUES
  (0, 'Draft', 'Draft', 'Brouillon')
 ,(1, 'Published', 'Published', 'Publié')
 ,(2, 'Archived', 'Archived', 'Archivé')
) AS Source ([Id],[Name],[EnglishDislayName],[FrenchDisplayName])
ON (Target.[Id] = Source.[Id])
WHEN MATCHED AND (Target.[Name] <> Source.[Name] OR Target.[EnglishDislayName] <> Source.[EnglishDislayName] OR Target.[FrenchDisplayName] <> Source.[FrenchDisplayName]) THEN
	UPDATE SET
		[Name] = Source.[Name],
		[EnglishDislayName] = Source.[EnglishDislayName],
		[FrenchDisplayName] = Source.[FrenchDisplayName]
WHEN NOT MATCHED BY TARGET THEN
	INSERT([Id],[Name],[EnglishDislayName],[FrenchDisplayName])
	VALUES(Source.[Id],Source.[Name],Source.[EnglishDislayName],Source.[FrenchDisplayName])
WHEN NOT MATCHED BY SOURCE THEN
	DELETE;
GO

-- Test data only - Insert notification messages if they don't already exist

-- Check and insert notification messages only if they don't exist
IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'Creating an inclusive environment where everyone can bring their genuine selves to work, participate fully and be positioned for success')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('Creating an inclusive environment where everyone can bring their genuine selves to work, participate fully and be positioned for success');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'Meet your Inclusion, Equity and Diversity Advisory Council')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('Meet your Inclusion, Equity and Diversity Advisory Council');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'During the month of June, Indigenous History Month, we have seen tragic events unfold � the lives of 215 residential school children found in BC and 751 unmarked graves found in Saskatchewan')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('During the month of June, Indigenous History Month, we have seen tragic events unfold � the lives of 215 residential school children found in BC and 751 unmarked graves found in Saskatchewan');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'Include land acknowledgements in your Outlook email signature')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('Include land acknowledgements in your Outlook email signature');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'Pride Season Event: We celebrated the kick-off to Pride Season on Thursday, June 17 with a special virtual Pride webcast. If you missed it, you can watch the webcast recording here')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('Pride Season Event: We celebrated the kick-off to Pride Season on Thursday, June 17 with a special virtual Pride webcast. If you missed it, you can watch the webcast recording here');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'Change your BDO Outlook photo: Let�s add some colour to our conversations')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('Change your BDO Outlook photo: Let�s add some colour to our conversations');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'Include your preferred pronoun in your Outlook email signature')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('Include your preferred pronoun in your Outlook email signature');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'BDO 100 Celebration')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('BDO 100 Celebration');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'The New IE&D Advisory Council')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('The New IE&D Advisory Council');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'Your Firm Engagement HUB')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('Your Firm Engagement HUB');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'NEW AND IMPROVED MY BDO!')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('NEW AND IMPROVED MY BDO!');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = 'Congratulations - Chris Diepdael, CMC�BC Rising Star Award')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('Congratulations - Chris Diepdael, CMC�BC Rising Star Award');
END

IF NOT EXISTS (SELECT 1 FROM [dbo].[Notification] WHERE [Message] = '[CAMPAIGN LAUNCH] Selling your business: now live!')
BEGIN
    INSERT INTO [dbo].[Notification] ([Message])
    VALUES('[CAMPAIGN LAUNCH] Selling your business: now live!');
END

GO

GO
PRINT N'Checking existing data against newly created constraints';


GO
USE [$(DatabaseName)];


GO
ALTER TABLE [dbo].[Form] WITH CHECK CHECK CONSTRAINT [FK_Form_Questionnaire];

ALTER TABLE [dbo].[UserAnswer] WITH CHECK CHECK CONSTRAINT [FK_UserAnswer_Form];

ALTER TABLE [dbo].[Form] WITH CHECK CHECK CONSTRAINT [FK_Form_Status];

ALTER TABLE [dbo].[Questionnaire] WITH CHECK CHECK CONSTRAINT [FK_Questionnaire_Status];

ALTER TABLE [dbo].[PartnerReferenceData] WITH CHECK CHECK CONSTRAINT [FK_PartnerReferenceData_Partner];

ALTER TABLE [dbo].[PartnerReferenceData] WITH CHECK CHECK CONSTRAINT [FK_PartnerReferenceData_Meta];

ALTER TABLE [dbo].[PartnerReferenceDataMetaDetails] WITH CHECK CHECK CONSTRAINT [FK_PartnerReferenceDataMetaDetails_Meta];

ALTER TABLE [dbo].[PartnerReferenceDataUpload] WITH CHECK CHECK CONSTRAINT [FK_PartnerReferenceDataUpload_Meta];

ALTER TABLE [dbo].[PartnerReferenceDataUploadDetails] WITH CHECK CHECK CONSTRAINT [FK_PartnerReferenceDataUploadDetails_Upload];

ALTER TABLE [dbo].[PartnerReviewer] WITH CHECK CHECK CONSTRAINT [FK_PartnerReviewer_Partner];

ALTER TABLE [dbo].[PartnerReviewer] WITH CHECK CHECK CONSTRAINT [FK_PartnerReviewer_PrimaryReviewer];

ALTER TABLE [dbo].[PartnerReviewer] WITH CHECK CHECK CONSTRAINT [FK_PartnerReviewer_SecondaryReviewer];

ALTER TABLE [dbo].[PartnerReviewerUploadDetails] WITH CHECK CHECK CONSTRAINT [FK_PartnerReviewerUploadDetails_PartnerReviewerUpload];


GO
PRINT N'Update complete.';


GO
