﻿** Highlights
     Tables that will be recreated with data migrated
       [dbo].[Form]
       [dbo].[FormReviewLog]
       [dbo].[Partner]
       [dbo].[PartnerReferenceDataMetaDetails]
     Clustered indexes that will be dropped
       None
     Clustered indexes that will be created
       None
     Possible data issues
       The column [audit].[DataAudit].[CreatedBy] is being dropped, data loss could occur.
       The column [audit].[DataAudit].[CreatedOn] is being dropped, data loss could occur.

** User actions
     Drop
       unnamed constraint on [dbo].[Form] (Default Constraint)
       unnamed constraint on [dbo].[Form] (Default Constraint)
       unnamed constraint on [dbo].[Form] (Default Constraint)
       unnamed constraint on [dbo].[Form] (Default Constraint)
       unnamed constraint on [dbo].[Partner] (Default Constraint)
       unnamed constraint on [dbo].[Partner] (Default Constraint)
       unnamed constraint on [dbo].[Partner] (Default Constraint)
       unnamed constraint on [dbo].[PartnerReferenceDataMetaDetails] (Default Constraint)
       unnamed constraint on [dbo].[PartnerReferenceDataMetaDetails] (Default Constraint)
       unnamed constraint on [audit].[DataAudit] (Default Constraint)
     Alter
       [audit].[DataAudit] (Table)
       [dbo].[vw_FormReviewLogLatestStatus] (View)
     Create
       [audit].[DataAudit].[IX_DataAudit_AuditGroupId] (Index)
       [dbo].[Form].[IX_Form_Year_PartnerObjectId] (Index)
       [dbo].[FormReviewLog].[IX_FormReviewLog_YearCycleSatus] (Index)
       [dbo].[FormReviewLog].[IX_FormReviewLog_FormId] (Index)
       [audit].[DataAuditMain] (Table)
       [audit].[DataAuditMain].[IX_DataAuditMain_FormId] (Index)
       [audit].[DataAuditMain].[IX_DataAuditMain_CreatedOn] (Index)
       [dbo].[UserAnswer].[IX_UserAnswer_FormId] (Index)
       Default Constraint: unnamed constraint on [audit].[DataAuditMain] (Default Constraint)
       Default Constraint: unnamed constraint on [audit].[DataAuditMain] (Default Constraint)
       [audit].[FK_DataAudit_DataAuditMain] (Foreign Key)
       [dbo].[vw_PartnerAnnualPlansSearch] (View)
       [dbo].[sp_GetAggregatedAuditLog] (Procedure)
     Recreate table
       [dbo].[Form] (Table)
       [dbo].[FormReviewLog] (Table)
       [dbo].[Partner] (Table)
       [dbo].[PartnerReferenceDataMetaDetails] (Table)

** Supporting actions
     Drop
       [dbo].[FK_UserAnswer_Form] (Foreign Key)
       [dbo].[FK_Form_Questionnaire] (Foreign Key)
       [dbo].[FK_FormReviewLog_Form] (Foreign Key)
       [dbo].[FK_PartnerReviewer_Partner] (Foreign Key)
       [dbo].[FK_PartnerReviewer_PrimaryReviewer] (Foreign Key)
       [dbo].[FK_PartnerReviewer_SecondaryReviewer] (Foreign Key)
       [dbo].[FK_PartnerReferenceData_Partner] (Foreign Key)
       [dbo].[FK_PartnerReferenceDataMetaDetails_Meta] (Foreign Key)
     Create
       [dbo].[Form].[IX_Form_Year_Partner_Status] (Index)
       [dbo].[Partner].[IX_Partner_EmployeeId] (Index)
       [dbo].[Partner].[IX_Partner_ObjectId] (Index)
       [dbo].[FK_UserAnswer_Form] (Foreign Key)
       [dbo].[FK_Form_Questionnaire] (Foreign Key)
       [dbo].[FK_FormReviewLog_Form] (Foreign Key)
       [dbo].[FK_PartnerReviewer_Partner] (Foreign Key)
       [dbo].[FK_PartnerReviewer_PrimaryReviewer] (Foreign Key)
       [dbo].[FK_PartnerReviewer_SecondaryReviewer] (Foreign Key)
       [dbo].[FK_PartnerReferenceData_Partner] (Foreign Key)
       [dbo].[FK_PartnerReferenceDataMetaDetails_Meta] (Foreign Key)

Object audit.DataAuditMain.IX_DataAuditMain_FormId is defined with online keyword. Deployment will continue and applicable changes to object audit.DataAuditMain.IX_DataAuditMain_FormId will be made according to definition.
Object audit.DataAuditMain.IX_DataAuditMain_CreatedOn is defined with online keyword. Deployment will continue and applicable changes to object audit.DataAuditMain.IX_DataAuditMain_CreatedOn will be made according to definition.
Object audit.DataAudit.IX_DataAudit_AuditGroupId is defined with online keyword. Deployment will continue and applicable changes to object audit.DataAudit.IX_DataAudit_AuditGroupId will be made according to definition.
The column [audit].[DataAudit].[CreatedBy] is being dropped, data loss could occur.
The column [audit].[DataAudit].[CreatedOn] is being dropped, data loss could occur.

