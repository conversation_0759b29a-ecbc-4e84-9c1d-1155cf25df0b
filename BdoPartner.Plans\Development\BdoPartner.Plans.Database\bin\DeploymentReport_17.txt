﻿** Highlights
     Tables that will be recreated with data migrated
       [dbo].[Form]
       [dbo].[Notification]
       [dbo].[Partner]
       [dbo].[PartnerReferenceData]
       [dbo].[PartnerReferenceDataMeta]
       [dbo].[PartnerReferenceDataMetaDetails]
       [dbo].[PartnerReferenceDataUpload]
       [dbo].[PartnerReferenceDataUploadDetails]
       [dbo].[PartnerReviewer]
       [dbo].[PartnerReviewerUpload]
       [dbo].[Questionnaire]
       [dbo].[UserAnswer]
     Clustered indexes that will be dropped
       None
     Clustered indexes that will be created
       None
     Possible data issues
       None

** User actions
     Drop
       unnamed constraint on [dbo].[Form] (Default Constraint)
       unnamed constraint on [dbo].[Form] (Default Constraint)
       unnamed constraint on [dbo].[Form] (Default Constraint)
       unnamed constraint on [dbo].[Form] (Default Constraint)
       unnamed constraint on [dbo].[Notification] (Default Constraint)
       unnamed constraint on [dbo].[Partner] (Default Constraint)
       unnamed constraint on [dbo].[Partner] (Default Constraint)
       unnamed constraint on [dbo].[Partner] (Default Constraint)
       unnamed constraint on [dbo].[PartnerReferenceData] (Default Constraint)
       unnamed constraint on [dbo].[PartnerReferenceData] (Default Constraint)
       unnamed constraint on [dbo].[PartnerReferenceDataMeta] (Default Constraint)
       unnamed constraint on [dbo].[PartnerReferenceDataMeta] (Default Constraint)
       unnamed constraint on [dbo].[PartnerReferenceDataMeta] (Default Constraint)
       unnamed constraint on [dbo].[PartnerReferenceDataMetaDetails] (Default Constraint)
       unnamed constraint on [dbo].[PartnerReferenceDataMetaDetails] (Default Constraint)
       unnamed constraint on [dbo].[PartnerReferenceDataUpload] (Default Constraint)
       unnamed constraint on [dbo].[PartnerReferenceDataUpload] (Default Constraint)
       unnamed constraint on [dbo].[PartnerReferenceDataUpload] (Default Constraint)
       unnamed constraint on [dbo].[PartnerReferenceDataUploadDetails] (Default Constraint)
       unnamed constraint on [dbo].[PartnerReferenceDataUploadDetails] (Default Constraint)
       unnamed constraint on [dbo].[PartnerReviewer] (Default Constraint)
       unnamed constraint on [dbo].[PartnerReviewer] (Default Constraint)
       unnamed constraint on [dbo].[PartnerReviewer] (Default Constraint)
       unnamed constraint on [dbo].[PartnerReviewerUpload] (Default Constraint)
       unnamed constraint on [dbo].[PartnerReviewerUpload] (Default Constraint)
       unnamed constraint on [dbo].[Questionnaire] (Default Constraint)
       unnamed constraint on [dbo].[Questionnaire] (Default Constraint)
       unnamed constraint on [dbo].[Questionnaire] (Default Constraint)
       unnamed constraint on [dbo].[Questionnaire] (Default Constraint)
       unnamed constraint on [dbo].[Questionnaire] (Default Constraint)
       unnamed constraint on [dbo].[Questionnaire] (Default Constraint)
       unnamed constraint on [dbo].[Questionnaire] (Default Constraint)
       unnamed constraint on [dbo].[UserAnswer] (Default Constraint)
       unnamed constraint on [dbo].[UserAnswer] (Default Constraint)
       unnamed constraint on [dbo].[UserAnswer] (Default Constraint)
     Recreate table
       [dbo].[Form] (Table)
       [dbo].[Notification] (Table)
       [dbo].[Partner] (Table)
       [dbo].[PartnerReferenceData] (Table)
       [dbo].[PartnerReferenceDataMeta] (Table)
       [dbo].[PartnerReferenceDataMetaDetails] (Table)
       [dbo].[PartnerReferenceDataUpload] (Table)
       [dbo].[PartnerReferenceDataUploadDetails] (Table)
       [dbo].[PartnerReviewer] (Table)
       [dbo].[PartnerReviewerUpload] (Table)
       [dbo].[Questionnaire] (Table)
       [dbo].[UserAnswer] (Table)
     Alter
       [dbo].[PartnerReviewerUploadDetails] (Table)

** Supporting actions
     Drop
       [dbo].[FK_Form_Questionnaire] (Foreign Key)
       [dbo].[FK_Form_Status] (Foreign Key)
       [dbo].[FK_UserAnswer_Form] (Foreign Key)
       [dbo].[FK_PartnerReferenceData_Partner] (Foreign Key)
       [dbo].[FK_PartnerReviewer_Partner] (Foreign Key)
       [dbo].[FK_PartnerReviewer_PrimaryReviewer] (Foreign Key)
       [dbo].[FK_PartnerReviewer_SecondaryReviewer] (Foreign Key)
       [dbo].[FK_PartnerReferenceData_Meta] (Foreign Key)
       [dbo].[FK_PartnerReferenceDataMetaDetails_Meta] (Foreign Key)
       [dbo].[FK_PartnerReferenceDataUpload_Meta] (Foreign Key)
       [dbo].[FK_PartnerReferenceDataUploadDetails_Upload] (Foreign Key)
       [dbo].[FK_PartnerReviewerUploadDetails_PartnerReviewerUpload] (Foreign Key)
       [dbo].[FK_Questionnaire_Status] (Foreign Key)
     Create
       [dbo].[FK_Form_Questionnaire] (Foreign Key)
       [dbo].[FK_Form_Status] (Foreign Key)
       [dbo].[FK_UserAnswer_Form] (Foreign Key)
       [dbo].[FK_PartnerReferenceData_Partner] (Foreign Key)
       [dbo].[FK_PartnerReviewer_Partner] (Foreign Key)
       [dbo].[FK_PartnerReviewer_PrimaryReviewer] (Foreign Key)
       [dbo].[FK_PartnerReviewer_SecondaryReviewer] (Foreign Key)
       [dbo].[FK_PartnerReferenceData_Meta] (Foreign Key)
       [dbo].[FK_PartnerReferenceDataMetaDetails_Meta] (Foreign Key)
       [dbo].[FK_PartnerReferenceDataUpload_Meta] (Foreign Key)
       [dbo].[FK_PartnerReferenceDataUploadDetails_Upload] (Foreign Key)
       [dbo].[FK_PartnerReviewerUploadDetails_PartnerReviewerUpload] (Foreign Key)
       [dbo].[FK_Questionnaire_Status] (Foreign Key)
