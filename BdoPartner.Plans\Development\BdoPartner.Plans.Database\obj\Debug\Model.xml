<?xml version="1.0" encoding="utf-8"?>
<DataSchemaModel FileFormatVersion="1.2" SchemaVersion="3.5" DspName="Microsoft.Data.Tools.Schema.Sql.SqlAzureV12DatabaseSchemaProvider" CollationLcid="1033" CollationCaseSensitive="False" xmlns="http://schemas.microsoft.com/sqlserver/dac/Serialization/2012/02">
	<Header>
		<CustomData Category="AnsiNulls">
			<Metadata Name="AnsiNulls" Value="True" />
		</CustomData>
		<CustomData Category="QuotedIdentifier">
			<Metadata Name="QuotedIdentifier" Value="True" />
		</CustomData>
		<CustomData Category="CompatibilityMode">
			<Metadata Name="CompatibilityMode" Value="150" />
		</CustomData>
		<CustomData Category="Reference" Type="Assembly">
			<Metadata Name="LogicalName" Value="BdoPartner.Plans.Database.dll" />
			<Metadata Name="FileName" Value="C:\GIT\PARTNER-SITE\BDOPARTNER.PLANS\DEVELOPMENT\BDOPARTNER.PLANS.DATABASE\OBJ\DEBUG\BDOPARTNER.PLANS.DATABASE.DLL" />
			<Metadata Name="AssemblyName" Value="BdoPartner.Plans.Database" />
			<Metadata Name="PermissionSet" Value="SAFE" />
			<Metadata Name="Owner" Value="" />
			<Metadata Name="GenerateSqlClrDdl" Value="True" />
			<Metadata Name="IsVisible" Value="True" />
			<Metadata Name="IsModelAware" Value="True" />
			<Metadata Name="SkipCreationIfEmpty" Value="True" />
			<Metadata Name="AssemblySymbolsName" Value="C:\git\Partner-Site\BdoPartner.Plans\Development\BdoPartner.Plans.Database\obj\Debug\BdoPartner.Plans.Database.pdb" />
		</CustomData>
		<CustomData Category="SqlCmdVariables" Type="SqlCmdVariable" />
	</Header>
	<Model>
		<Element Type="SqlDatabaseOptions">
			<Property Name="Collation" Value="SQL_Latin1_General_CP1_CI_AS" />
			<Property Name="IsAnsiNullDefaultOn" Value="True" />
			<Property Name="IsAnsiNullsOn" Value="True" />
			<Property Name="IsAnsiWarningsOn" Value="True" />
			<Property Name="IsArithAbortOn" Value="True" />
			<Property Name="IsConcatNullYieldsNullOn" Value="True" />
			<Property Name="IsTornPageProtectionOn" Value="False" />
			<Property Name="IsFullTextEnabled" Value="True" />
			<Property Name="PageVerifyMode" Value="3" />
			<Property Name="DefaultLanguage" Value="" />
			<Property Name="DefaultFullTextLanguage" Value="" />
			<Property Name="QueryStoreDesiredState" Value="0" />
			<Property Name="QueryStoreCaptureMode" Value="1" />
			<Property Name="QueryStoreStaleQueryThreshold" Value="367" />
			<Property Name="MaxDop" Value="0" />
			<Property Name="TemporalHistoryRetentionEnabled" Value="False" />
		</Element>
		<Element Type="SqlDefaultConstraint">
			<Property Name="DefaultExpressionScript">
				<Value><![CDATA[0]]></Value>
			</Property>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[PartnerReviewerUpload]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForColumn">
				<Entry>
					<References Name="[dbo].[PartnerReviewerUpload].[Status]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="3" />
		</Element>
		<Element Type="SqlDefaultConstraint">
			<Property Name="DefaultExpressionScript">
				<Value><![CDATA[getutcdate()]]></Value>
			</Property>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[PartnerReviewerUpload]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForColumn">
				<Entry>
					<References Name="[dbo].[PartnerReviewerUpload].[CreatedOn]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="4" />
		</Element>
		<Element Type="SqlDefaultConstraint">
			<Property Name="DefaultExpressionScript">
				<Value><![CDATA[newsequentialid()]]></Value>
			</Property>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[UserAnswer]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForColumn">
				<Entry>
					<References Name="[dbo].[UserAnswer].[Id]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="5" />
		</Element>
		<Element Type="SqlDefaultConstraint">
			<Property Name="DefaultExpressionScript">
				<Value><![CDATA[1]]></Value>
			</Property>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[UserAnswer]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForColumn">
				<Entry>
					<References Name="[dbo].[UserAnswer].[IsActive]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="6" />
		</Element>
		<Element Type="SqlDefaultConstraint">
			<Property Name="DefaultExpressionScript">
				<Value><![CDATA[getutcdate()]]></Value>
			</Property>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[UserAnswer]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForColumn">
				<Entry>
					<References Name="[dbo].[UserAnswer].[CreatedOn]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="7" />
		</Element>
		<Element Type="SqlDefaultConstraint">
			<Property Name="DefaultExpressionScript">
				<Value><![CDATA[newsequentialid()]]></Value>
			</Property>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[Questionnaire]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForColumn">
				<Entry>
					<References Name="[dbo].[Questionnaire].[Id]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="8" />
		</Element>
		<Element Type="SqlDefaultConstraint">
			<Property Name="DefaultExpressionScript">
				<Value><![CDATA[0]]></Value>
			</Property>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[Questionnaire]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForColumn">
				<Entry>
					<References Name="[dbo].[Questionnaire].[FormSystemVersion]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="9" />
		</Element>
		<Element Type="SqlDefaultConstraint">
			<Property Name="DefaultExpressionScript">
				<Value><![CDATA[0]]></Value>
			</Property>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[Questionnaire]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForColumn">
				<Entry>
					<References Name="[dbo].[Questionnaire].[Acknowledgement]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="10" />
		</Element>
		<Element Type="SqlDefaultConstraint">
			<Property Name="DefaultExpressionScript">
				<Value><![CDATA[0]]></Value>
			</Property>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[Questionnaire]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForColumn">
				<Entry>
					<References Name="[dbo].[Questionnaire].[GeneralComments]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="11" />
		</Element>
		<Element Type="SqlDefaultConstraint">
			<Property Name="DefaultExpressionScript">
				<Value><![CDATA[0]]></Value>
			</Property>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[Questionnaire]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForColumn">
				<Entry>
					<References Name="[dbo].[Questionnaire].[Status]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="12" />
		</Element>
		<Element Type="SqlDefaultConstraint">
			<Property Name="DefaultExpressionScript">
				<Value><![CDATA[1]]></Value>
			</Property>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[Questionnaire]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForColumn">
				<Entry>
					<References Name="[dbo].[Questionnaire].[IsActive]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="13" />
		</Element>
		<Element Type="SqlDefaultConstraint">
			<Property Name="DefaultExpressionScript">
				<Value><![CDATA[getutcdate()]]></Value>
			</Property>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[Questionnaire]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForColumn">
				<Entry>
					<References Name="[dbo].[Questionnaire].[CreatedOn]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="14" />
		</Element>
		<Element Type="SqlDefaultConstraint">
			<Property Name="DefaultExpressionScript">
				<Value><![CDATA[newsequentialid()]]></Value>
			</Property>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[Partner]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForColumn">
				<Entry>
					<References Name="[dbo].[Partner].[Id]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="15" />
		</Element>
		<Element Type="SqlDefaultConstraint">
			<Property Name="DefaultExpressionScript">
				<Value><![CDATA[1]]></Value>
			</Property>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[Partner]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForColumn">
				<Entry>
					<References Name="[dbo].[Partner].[IsActive]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="16" />
		</Element>
		<Element Type="SqlDefaultConstraint">
			<Property Name="DefaultExpressionScript">
				<Value><![CDATA[getutcdate()]]></Value>
			</Property>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[Partner]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForColumn">
				<Entry>
					<References Name="[dbo].[Partner].[CreatedOn]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="17" />
		</Element>
		<Element Type="SqlDefaultConstraint">
			<Property Name="DefaultExpressionScript">
				<Value><![CDATA[newsequentialid()]]></Value>
			</Property>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[Form]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForColumn">
				<Entry>
					<References Name="[dbo].[Form].[Id]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="18" />
		</Element>
		<Element Type="SqlDefaultConstraint">
			<Property Name="DefaultExpressionScript">
				<Value><![CDATA[0]]></Value>
			</Property>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[Form]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForColumn">
				<Entry>
					<References Name="[dbo].[Form].[Status]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="19" />
		</Element>
		<Element Type="SqlDefaultConstraint">
			<Property Name="DefaultExpressionScript">
				<Value><![CDATA[1]]></Value>
			</Property>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[Form]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForColumn">
				<Entry>
					<References Name="[dbo].[Form].[IsActive]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="20" />
		</Element>
		<Element Type="SqlDefaultConstraint">
			<Property Name="DefaultExpressionScript">
				<Value><![CDATA[getutcdate()]]></Value>
			</Property>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[Form]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForColumn">
				<Entry>
					<References Name="[dbo].[Form].[CreatedOn]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="21" />
		</Element>
		<Element Type="SqlDefaultConstraint">
			<Property Name="DefaultExpressionScript">
				<Value><![CDATA[getutcdate()]]></Value>
			</Property>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[Notification]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForColumn">
				<Entry>
					<References Name="[dbo].[Notification].[CreatedOn]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="22" />
		</Element>
		<Element Type="SqlDefaultConstraint">
			<Property Name="DefaultExpressionScript">
				<Value><![CDATA[1]]></Value>
			</Property>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[identity].[AuthProvider]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForColumn">
				<Entry>
					<References Name="[identity].[AuthProvider].[IsActive]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="23" />
		</Element>
		<Element Type="SqlDefaultConstraint">
			<Property Name="DefaultExpressionScript">
				<Value><![CDATA[getutcdate()]]></Value>
			</Property>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[audit].[DataAudit]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForColumn">
				<Entry>
					<References Name="[audit].[DataAudit].[CreatedOn]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="24" />
		</Element>
		<Element Type="SqlDefaultConstraint">
			<Property Name="DefaultExpressionScript">
				<Value><![CDATA[newsequentialid()]]></Value>
			</Property>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[UserRole]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForColumn">
				<Entry>
					<References Name="[dbo].[UserRole].[Id]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="25" />
		</Element>
		<Element Type="SqlDefaultConstraint">
			<Property Name="DefaultExpressionScript">
				<Value><![CDATA[1]]></Value>
			</Property>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[Language]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForColumn">
				<Entry>
					<References Name="[dbo].[Language].[IsActive]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="26" />
		</Element>
		<Element Type="SqlDefaultConstraint">
			<Property Name="DefaultExpressionScript">
				<Value><![CDATA[newsequentialid()]]></Value>
			</Property>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[RolePermission]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForColumn">
				<Entry>
					<References Name="[dbo].[RolePermission].[Id]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="27" />
		</Element>
		<Element Type="SqlDefaultConstraint">
			<Property Name="DefaultExpressionScript">
				<Value><![CDATA[1]]></Value>
			</Property>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[Permission]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForColumn">
				<Entry>
					<References Name="[dbo].[Permission].[IsActive]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="28" />
		</Element>
		<Element Type="SqlDefaultConstraint">
			<Property Name="DefaultExpressionScript">
				<Value><![CDATA[1]]></Value>
			</Property>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[Role]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForColumn">
				<Entry>
					<References Name="[dbo].[Role].[IsActive]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="29" />
		</Element>
		<Element Type="SqlDefaultConstraint">
			<Property Name="DefaultExpressionScript">
				<Value><![CDATA[(newsequentialid())]]></Value>
			</Property>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[User]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForColumn">
				<Entry>
					<References Name="[dbo].[User].[Id]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="30" />
		</Element>
		<Element Type="SqlDefaultConstraint">
			<Property Name="DefaultExpressionScript">
				<Value><![CDATA[0]]></Value>
			</Property>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[User]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForColumn">
				<Entry>
					<References Name="[dbo].[User].[IsTempPasswordEnabled]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="31" />
		</Element>
		<Element Type="SqlDefaultConstraint">
			<Property Name="DefaultExpressionScript">
				<Value><![CDATA[1]]></Value>
			</Property>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[User]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForColumn">
				<Entry>
					<References Name="[dbo].[User].[LanguageId]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="32" />
		</Element>
		<Element Type="SqlDefaultConstraint">
			<Property Name="DefaultExpressionScript">
				<Value><![CDATA[1]]></Value>
			</Property>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[User]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForColumn">
				<Entry>
					<References Name="[dbo].[User].[IsActive]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="33" />
		</Element>
		<Element Type="SqlDefaultConstraint">
			<Property Name="DefaultExpressionScript">
				<Value><![CDATA[getutcdate()]]></Value>
			</Property>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[User]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForColumn">
				<Entry>
					<References Name="[dbo].[User].[CreatedOn]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="34" />
		</Element>
		<Element Type="SqlDefaultConstraint">
			<Property Name="DefaultExpressionScript">
				<Value><![CDATA[newsequentialid()]]></Value>
			</Property>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[PartnerReferenceData]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForColumn">
				<Entry>
					<References Name="[dbo].[PartnerReferenceData].[Id]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="35" />
		</Element>
		<Element Type="SqlDefaultConstraint">
			<Property Name="DefaultExpressionScript">
				<Value><![CDATA[getutcdate()]]></Value>
			</Property>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[PartnerReferenceData]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForColumn">
				<Entry>
					<References Name="[dbo].[PartnerReferenceData].[CreatedOn]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="36" />
		</Element>
		<Element Type="SqlDefaultConstraint">
			<Property Name="DefaultExpressionScript">
				<Value><![CDATA[newsequentialid()]]></Value>
			</Property>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[PartnerReferenceDataUploadDetails]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForColumn">
				<Entry>
					<References Name="[dbo].[PartnerReferenceDataUploadDetails].[Id]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="37" />
		</Element>
		<Element Type="SqlDefaultConstraint">
			<Property Name="DefaultExpressionScript">
				<Value><![CDATA[getutcdate()]]></Value>
			</Property>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[PartnerReferenceDataUploadDetails]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForColumn">
				<Entry>
					<References Name="[dbo].[PartnerReferenceDataUploadDetails].[CreatedOn]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="38" />
		</Element>
		<Element Type="SqlDefaultConstraint">
			<Property Name="DefaultExpressionScript">
				<Value><![CDATA[newsequentialid()]]></Value>
			</Property>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[PartnerReferenceDataUpload]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForColumn">
				<Entry>
					<References Name="[dbo].[PartnerReferenceDataUpload].[Id]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="39" />
		</Element>
		<Element Type="SqlDefaultConstraint">
			<Property Name="DefaultExpressionScript">
				<Value><![CDATA[0]]></Value>
			</Property>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[PartnerReferenceDataUpload]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForColumn">
				<Entry>
					<References Name="[dbo].[PartnerReferenceDataUpload].[Status]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="40" />
		</Element>
		<Element Type="SqlDefaultConstraint">
			<Property Name="DefaultExpressionScript">
				<Value><![CDATA[getutcdate()]]></Value>
			</Property>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[PartnerReferenceDataUpload]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForColumn">
				<Entry>
					<References Name="[dbo].[PartnerReferenceDataUpload].[CreatedOn]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="41" />
		</Element>
		<Element Type="SqlDefaultConstraint">
			<Property Name="DefaultExpressionScript">
				<Value><![CDATA[newsequentialid()]]></Value>
			</Property>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[PartnerReferenceDataMetaDetails]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForColumn">
				<Entry>
					<References Name="[dbo].[PartnerReferenceDataMetaDetails].[Id]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="42" />
		</Element>
		<Element Type="SqlDefaultConstraint">
			<Property Name="DefaultExpressionScript">
				<Value><![CDATA[getutcdate()]]></Value>
			</Property>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[PartnerReferenceDataMetaDetails]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForColumn">
				<Entry>
					<References Name="[dbo].[PartnerReferenceDataMetaDetails].[CreatedOn]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="43" />
		</Element>
		<Element Type="SqlDefaultConstraint">
			<Property Name="DefaultExpressionScript">
				<Value><![CDATA[newsequentialid()]]></Value>
			</Property>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[PartnerReferenceDataMeta]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForColumn">
				<Entry>
					<References Name="[dbo].[PartnerReferenceDataMeta].[Id]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="44" />
		</Element>
		<Element Type="SqlDefaultConstraint">
			<Property Name="DefaultExpressionScript">
				<Value><![CDATA[1]]></Value>
			</Property>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[PartnerReferenceDataMeta]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForColumn">
				<Entry>
					<References Name="[dbo].[PartnerReferenceDataMeta].[IsActive]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="45" />
		</Element>
		<Element Type="SqlDefaultConstraint">
			<Property Name="DefaultExpressionScript">
				<Value><![CDATA[getutcdate()]]></Value>
			</Property>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[PartnerReferenceDataMeta]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForColumn">
				<Entry>
					<References Name="[dbo].[PartnerReferenceDataMeta].[CreatedOn]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="46" />
		</Element>
		<Element Type="SqlDefaultConstraint">
			<Property Name="DefaultExpressionScript">
				<Value><![CDATA[newsequentialid()]]></Value>
			</Property>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[PartnerReviewer]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForColumn">
				<Entry>
					<References Name="[dbo].[PartnerReviewer].[Id]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="47" />
		</Element>
		<Element Type="SqlDefaultConstraint">
			<Property Name="DefaultExpressionScript">
				<Value><![CDATA[0]]></Value>
			</Property>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[PartnerReviewer]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForColumn">
				<Entry>
					<References Name="[dbo].[PartnerReviewer].[Exempt]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="48" />
		</Element>
		<Element Type="SqlDefaultConstraint">
			<Property Name="DefaultExpressionScript">
				<Value><![CDATA[getutcdate()]]></Value>
			</Property>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[PartnerReviewer]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForColumn">
				<Entry>
					<References Name="[dbo].[PartnerReviewer].[CreatedOn]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="49" />
		</Element>
		<Element Type="SqlPrimaryKeyConstraint">
			<Relationship Name="ColumnSpecifications">
				<Entry>
					<Element Type="SqlIndexedColumnSpecification">
						<Relationship Name="Column">
							<Entry>
								<References Name="[dbo].[QuestionnaireStatus].[Id]" />
							</Entry>
						</Relationship>
					</Element>
				</Entry>
			</Relationship>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[QuestionnaireStatus]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="50" />
		</Element>
		<Element Type="SqlPrimaryKeyConstraint">
			<Relationship Name="ColumnSpecifications">
				<Entry>
					<Element Type="SqlIndexedColumnSpecification">
						<Relationship Name="Column">
							<Entry>
								<References Name="[dbo].[FormStatus].[Id]" />
							</Entry>
						</Relationship>
					</Element>
				</Entry>
			</Relationship>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[FormStatus]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="51" />
		</Element>
		<Element Type="SqlSchema" Name="[audit]">
			<Relationship Name="Authorizer">
				<Entry>
					<References ExternalSource="BuiltIns" Name="[dbo]" />
				</Entry>
			</Relationship>
		</Element>
		<Element Type="SqlTable" Name="[audit].[DataAudit]">
			<Property Name="IsAnsiNullsOn" Value="True" />
			<Relationship Name="Columns">
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[audit].[DataAudit].[Id]">
						<Property Name="IsNullable" Value="False" />
						<Property Name="IsIdentity" Value="True" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[bigint]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[audit].[DataAudit].[AuditGroupId]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[uniqueidentifier]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[audit].[DataAudit].[EntityState]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[int]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[audit].[DataAudit].[TableName]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="50" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[audit].[DataAudit].[PrimaryKeyField]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="50" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[audit].[DataAudit].[PrimaryKeyValue]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="50" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[audit].[DataAudit].[FieldName]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="50" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[audit].[DataAudit].[OriginalValue]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="IsMax" Value="True" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[audit].[DataAudit].[CurrentValue]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="IsMax" Value="True" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[audit].[DataAudit].[CreatedOn]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Scale" Value="7" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[datetime2]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
						<AttachedAnnotation Disambiguator="24" />
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[audit].[DataAudit].[CreatedBy]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="50" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
			</Relationship>
			<Relationship Name="Schema">
				<Entry>
					<References Name="[audit]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="52" />
		</Element>
		<Element Type="SqlTable" Name="[audit].[LoginAudit]">
			<Property Name="IsAnsiNullsOn" Value="True" />
			<Relationship Name="Columns">
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[audit].[LoginAudit].[Id]">
						<Property Name="IsNullable" Value="False" />
						<Property Name="IsIdentity" Value="True" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[bigint]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
			</Relationship>
			<Relationship Name="Schema">
				<Entry>
					<References Name="[audit]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="53" />
		</Element>
		<Element Type="SqlPrimaryKeyConstraint" Name="[audit].[PK_DataAudit]">
			<Relationship Name="ColumnSpecifications">
				<Entry>
					<Element Type="SqlIndexedColumnSpecification">
						<Relationship Name="Column">
							<Entry>
								<References Name="[audit].[DataAudit].[Id]" />
							</Entry>
						</Relationship>
					</Element>
				</Entry>
			</Relationship>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[audit].[DataAudit]" />
				</Entry>
			</Relationship>
			<AttachedAnnotation Disambiguator="52" />
		</Element>
		<Element Type="SqlPrimaryKeyConstraint" Name="[audit].[PK_LoginAudit]">
			<Relationship Name="ColumnSpecifications">
				<Entry>
					<Element Type="SqlIndexedColumnSpecification">
						<Relationship Name="Column">
							<Entry>
								<References Name="[audit].[LoginAudit].[Id]" />
							</Entry>
						</Relationship>
					</Element>
				</Entry>
			</Relationship>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[audit].[LoginAudit]" />
				</Entry>
			</Relationship>
			<AttachedAnnotation Disambiguator="53" />
		</Element>
		<Element Type="SqlUniqueConstraint" Name="[dbo].[AK_PartnerReviewer_PartnerYear]">
			<Relationship Name="ColumnSpecifications">
				<Entry>
					<Element Type="SqlIndexedColumnSpecification">
						<Relationship Name="Column">
							<Entry>
								<References Name="[dbo].[PartnerReviewer].[PartnerId]" />
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlIndexedColumnSpecification">
						<Relationship Name="Column">
							<Entry>
								<References Name="[dbo].[PartnerReviewer].[Year]" />
							</Entry>
						</Relationship>
					</Element>
				</Entry>
			</Relationship>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[PartnerReviewer]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="54" />
		</Element>
		<Element Type="SqlUniqueConstraint" Name="[dbo].[AK_User_AuthProviderUserName]">
			<Relationship Name="ColumnSpecifications">
				<Entry>
					<Element Type="SqlIndexedColumnSpecification">
						<Relationship Name="Column">
							<Entry>
								<References Name="[dbo].[User].[AuthProviderId]" />
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlIndexedColumnSpecification">
						<Relationship Name="Column">
							<Entry>
								<References Name="[dbo].[User].[Username]" />
							</Entry>
						</Relationship>
					</Element>
				</Entry>
			</Relationship>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[User]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="55" />
		</Element>
		<Element Type="SqlForeignKeyConstraint" Name="[dbo].[FK_Form_Questionnaire]">
			<Relationship Name="Columns">
				<Entry>
					<References Name="[dbo].[Form].[QuestionnaireId]" />
				</Entry>
			</Relationship>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[Form]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForeignColumns">
				<Entry>
					<References Name="[dbo].[Questionnaire].[Id]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForeignTable">
				<Entry>
					<References Name="[dbo].[Questionnaire]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="56" />
		</Element>
		<Element Type="SqlForeignKeyConstraint" Name="[dbo].[FK_Form_Status]">
			<Relationship Name="Columns">
				<Entry>
					<References Name="[dbo].[Form].[Status]" />
				</Entry>
			</Relationship>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[Form]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForeignColumns">
				<Entry>
					<References Name="[dbo].[FormStatus].[Id]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForeignTable">
				<Entry>
					<References Name="[dbo].[FormStatus]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="57" />
		</Element>
		<Element Type="SqlForeignKeyConstraint" Name="[dbo].[FK_PartnerReferenceData_Meta]">
			<Relationship Name="Columns">
				<Entry>
					<References Name="[dbo].[PartnerReferenceData].[MetaId]" />
				</Entry>
			</Relationship>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[PartnerReferenceData]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForeignColumns">
				<Entry>
					<References Name="[dbo].[PartnerReferenceDataMeta].[Id]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForeignTable">
				<Entry>
					<References Name="[dbo].[PartnerReferenceDataMeta]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="58" />
		</Element>
		<Element Type="SqlForeignKeyConstraint" Name="[dbo].[FK_PartnerReferenceData_Partner]">
			<Relationship Name="Columns">
				<Entry>
					<References Name="[dbo].[PartnerReferenceData].[PartnerId]" />
				</Entry>
			</Relationship>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[PartnerReferenceData]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForeignColumns">
				<Entry>
					<References Name="[dbo].[Partner].[Id]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForeignTable">
				<Entry>
					<References Name="[dbo].[Partner]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="59" />
		</Element>
		<Element Type="SqlForeignKeyConstraint" Name="[dbo].[FK_PartnerReferenceDataMetaDetails_Meta]">
			<Relationship Name="Columns">
				<Entry>
					<References Name="[dbo].[PartnerReferenceDataMetaDetails].[MetaId]" />
				</Entry>
			</Relationship>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[PartnerReferenceDataMetaDetails]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForeignColumns">
				<Entry>
					<References Name="[dbo].[PartnerReferenceDataMeta].[Id]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForeignTable">
				<Entry>
					<References Name="[dbo].[PartnerReferenceDataMeta]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="60" />
		</Element>
		<Element Type="SqlForeignKeyConstraint" Name="[dbo].[FK_PartnerReferenceDataUpload_Meta]">
			<Relationship Name="Columns">
				<Entry>
					<References Name="[dbo].[PartnerReferenceDataUpload].[MetaId]" />
				</Entry>
			</Relationship>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[PartnerReferenceDataUpload]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForeignColumns">
				<Entry>
					<References Name="[dbo].[PartnerReferenceDataMeta].[Id]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForeignTable">
				<Entry>
					<References Name="[dbo].[PartnerReferenceDataMeta]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="61" />
		</Element>
		<Element Type="SqlForeignKeyConstraint" Name="[dbo].[FK_PartnerReferenceDataUploadDetails_Upload]">
			<Relationship Name="Columns">
				<Entry>
					<References Name="[dbo].[PartnerReferenceDataUploadDetails].[PartnerReferenceDataUploadId]" />
				</Entry>
			</Relationship>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[PartnerReferenceDataUploadDetails]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForeignColumns">
				<Entry>
					<References Name="[dbo].[PartnerReferenceDataUpload].[Id]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForeignTable">
				<Entry>
					<References Name="[dbo].[PartnerReferenceDataUpload]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="62" />
		</Element>
		<Element Type="SqlForeignKeyConstraint" Name="[dbo].[FK_PartnerReviewer_Partner]">
			<Relationship Name="Columns">
				<Entry>
					<References Name="[dbo].[PartnerReviewer].[PartnerId]" />
				</Entry>
			</Relationship>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[PartnerReviewer]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForeignColumns">
				<Entry>
					<References Name="[dbo].[Partner].[Id]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForeignTable">
				<Entry>
					<References Name="[dbo].[Partner]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="63" />
		</Element>
		<Element Type="SqlForeignKeyConstraint" Name="[dbo].[FK_PartnerReviewer_PrimaryReviewer]">
			<Relationship Name="Columns">
				<Entry>
					<References Name="[dbo].[PartnerReviewer].[PrimaryReviewerId]" />
				</Entry>
			</Relationship>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[PartnerReviewer]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForeignColumns">
				<Entry>
					<References Name="[dbo].[Partner].[Id]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForeignTable">
				<Entry>
					<References Name="[dbo].[Partner]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="64" />
		</Element>
		<Element Type="SqlForeignKeyConstraint" Name="[dbo].[FK_PartnerReviewer_SecondaryReviewer]">
			<Relationship Name="Columns">
				<Entry>
					<References Name="[dbo].[PartnerReviewer].[SecondaryReviewerId]" />
				</Entry>
			</Relationship>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[PartnerReviewer]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForeignColumns">
				<Entry>
					<References Name="[dbo].[Partner].[Id]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForeignTable">
				<Entry>
					<References Name="[dbo].[Partner]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="65" />
		</Element>
		<Element Type="SqlForeignKeyConstraint" Name="[dbo].[FK_PartnerReviewerUploadDetails_PartnerReviewerUpload]">
			<Relationship Name="Columns">
				<Entry>
					<References Name="[dbo].[PartnerReviewerUploadDetails].[PartnerReviewerUploadId]" />
				</Entry>
			</Relationship>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[PartnerReviewerUploadDetails]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForeignColumns">
				<Entry>
					<References Name="[dbo].[PartnerReviewerUpload].[Id]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForeignTable">
				<Entry>
					<References Name="[dbo].[PartnerReviewerUpload]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="66" />
		</Element>
		<Element Type="SqlForeignKeyConstraint" Name="[dbo].[FK_Questionnaire_Status]">
			<Relationship Name="Columns">
				<Entry>
					<References Name="[dbo].[Questionnaire].[Status]" />
				</Entry>
			</Relationship>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[Questionnaire]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForeignColumns">
				<Entry>
					<References Name="[dbo].[QuestionnaireStatus].[Id]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForeignTable">
				<Entry>
					<References Name="[dbo].[QuestionnaireStatus]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="67" />
		</Element>
		<Element Type="SqlForeignKeyConstraint" Name="[dbo].[FK_RolePermission_Permission]">
			<Relationship Name="Columns">
				<Entry>
					<References Name="[dbo].[RolePermission].[PermissionId]" />
				</Entry>
			</Relationship>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[RolePermission]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForeignColumns">
				<Entry>
					<References Name="[dbo].[Permission].[Id]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForeignTable">
				<Entry>
					<References Name="[dbo].[Permission]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="68" />
		</Element>
		<Element Type="SqlForeignKeyConstraint" Name="[dbo].[FK_RolePermission_Role]">
			<Relationship Name="Columns">
				<Entry>
					<References Name="[dbo].[RolePermission].[RoleId]" />
				</Entry>
			</Relationship>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[RolePermission]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForeignColumns">
				<Entry>
					<References Name="[dbo].[Role].[Id]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForeignTable">
				<Entry>
					<References Name="[dbo].[Role]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="69" />
		</Element>
		<Element Type="SqlForeignKeyConstraint" Name="[dbo].[FK_User_AuthorProvider]">
			<Relationship Name="Columns">
				<Entry>
					<References Name="[dbo].[User].[AuthProviderId]" />
				</Entry>
			</Relationship>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[User]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForeignColumns">
				<Entry>
					<References Name="[identity].[AuthProvider].[Id]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForeignTable">
				<Entry>
					<References Name="[identity].[AuthProvider]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="70" />
		</Element>
		<Element Type="SqlForeignKeyConstraint" Name="[dbo].[FK_User_Language]">
			<Relationship Name="Columns">
				<Entry>
					<References Name="[dbo].[User].[LanguageId]" />
				</Entry>
			</Relationship>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[User]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForeignColumns">
				<Entry>
					<References Name="[dbo].[Language].[Id]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForeignTable">
				<Entry>
					<References Name="[dbo].[Language]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="71" />
		</Element>
		<Element Type="SqlForeignKeyConstraint" Name="[dbo].[FK_UserAnswer_Form]">
			<Relationship Name="Columns">
				<Entry>
					<References Name="[dbo].[UserAnswer].[FormId]" />
				</Entry>
			</Relationship>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[UserAnswer]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForeignColumns">
				<Entry>
					<References Name="[dbo].[Form].[Id]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForeignTable">
				<Entry>
					<References Name="[dbo].[Form]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="72" />
		</Element>
		<Element Type="SqlForeignKeyConstraint" Name="[dbo].[FK_UserRole_Role]">
			<Relationship Name="Columns">
				<Entry>
					<References Name="[dbo].[UserRole].[RoleId]" />
				</Entry>
			</Relationship>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[UserRole]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForeignColumns">
				<Entry>
					<References Name="[dbo].[Role].[Id]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForeignTable">
				<Entry>
					<References Name="[dbo].[Role]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="73" />
		</Element>
		<Element Type="SqlForeignKeyConstraint" Name="[dbo].[FK_UserRole_User]">
			<Relationship Name="Columns">
				<Entry>
					<References Name="[dbo].[UserRole].[UserId]" />
				</Entry>
			</Relationship>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[UserRole]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForeignColumns">
				<Entry>
					<References Name="[dbo].[User].[Id]" />
				</Entry>
			</Relationship>
			<Relationship Name="ForeignTable">
				<Entry>
					<References Name="[dbo].[User]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="74" />
		</Element>
		<Element Type="SqlTable" Name="[dbo].[Form]">
			<Property Name="IsAnsiNullsOn" Value="True" />
			<Relationship Name="Columns">
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Form].[Id]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[uniqueidentifier]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
						<AttachedAnnotation Disambiguator="18" />
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Form].[QuestionnaireId]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[uniqueidentifier]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Form].[Year]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[smallint]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Form].[Comments]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="IsMax" Value="True" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Form].[Status]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[tinyint]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
						<AttachedAnnotation Disambiguator="19" />
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Form].[IsActive]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[bit]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
						<AttachedAnnotation Disambiguator="20" />
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Form].[CreatedBy]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[uniqueidentifier]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Form].[CreatedOn]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Scale" Value="7" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[datetime2]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
						<AttachedAnnotation Disambiguator="21" />
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Form].[ModifiedBy]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[uniqueidentifier]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Form].[ModifiedOn]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Scale" Value="7" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[datetime2]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Form].[PartnerObjectId]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="100" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Form].[PartnerSubmittionDate]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Scale" Value="7" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[datetime2]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Form].[PartnerName]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="500" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Form].[Pdf]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="IsMax" Value="True" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[varbinary]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
			</Relationship>
			<Relationship Name="Schema">
				<Entry>
					<References ExternalSource="BuiltIns" Name="[dbo]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="75" />
			<AttachedAnnotation Disambiguator="56" />
			<AttachedAnnotation Disambiguator="57" />
		</Element>
		<Element Type="SqlTable" Name="[dbo].[FormStatus]">
			<Property Name="IsAnsiNullsOn" Value="True" />
			<Relationship Name="Columns">
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[FormStatus].[Id]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[tinyint]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
						<AttachedAnnotation Disambiguator="51" />
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[FormStatus].[Name]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="50" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[FormStatus].[EnglishDislayName]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="50" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[FormStatus].[FrenchDisplayName]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="50" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
			</Relationship>
			<Relationship Name="Schema">
				<Entry>
					<References ExternalSource="BuiltIns" Name="[dbo]" />
				</Entry>
			</Relationship>
		</Element>
		<Element Type="SqlTable" Name="[dbo].[Language]">
			<Property Name="IsAnsiNullsOn" Value="True" />
			<Relationship Name="Columns">
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Language].[Id]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[int]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Language].[Name]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="50" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Language].[Code]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="20" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Language].[IsActive]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[bit]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
						<AttachedAnnotation Disambiguator="26" />
					</Element>
				</Entry>
			</Relationship>
			<Relationship Name="Schema">
				<Entry>
					<References ExternalSource="BuiltIns" Name="[dbo]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="76" />
		</Element>
		<Element Type="SqlTable" Name="[dbo].[Notification]">
			<Property Name="IsAnsiNullsOn" Value="True" />
			<Relationship Name="Columns">
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Notification].[Id]">
						<Property Name="IsNullable" Value="False" />
						<Property Name="IsIdentity" Value="True" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[bigint]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Notification].[Message]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="IsMax" Value="True" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Notification].[CreatedBy]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[uniqueidentifier]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Notification].[CreatedOn]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Scale" Value="7" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[datetime2]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
						<AttachedAnnotation Disambiguator="22" />
					</Element>
				</Entry>
			</Relationship>
			<Relationship Name="Schema">
				<Entry>
					<References ExternalSource="BuiltIns" Name="[dbo]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="77" />
		</Element>
		<Element Type="SqlTable" Name="[dbo].[Partner]">
			<Property Name="IsAnsiNullsOn" Value="True" />
			<Relationship Name="Columns">
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Partner].[Id]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[uniqueidentifier]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
						<AttachedAnnotation Disambiguator="15" />
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Partner].[EmployeeId]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[int]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Partner].[FirstName]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="150" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Partner].[LastName]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="150" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Partner].[DisplayName]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="300" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Partner].[DOB]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[date]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Partner].[Mail]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="200" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Partner].[PartnerType]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="100" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Partner].[Department]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="200" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Partner].[Location]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="200" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Partner].[LocationId]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="200" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Partner].[WGroup]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="200" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Partner].[WGroupId]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="200" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Partner].[ServiceLine]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="200" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Partner].[ServiceLineId]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="200" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Partner].[SubServiceLine]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="200" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Partner].[SubServiceLineId]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="200" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Partner].[IsActive]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[bit]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
						<AttachedAnnotation Disambiguator="16" />
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Partner].[CreatedBy]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[uniqueidentifier]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Partner].[CreatedOn]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Scale" Value="7" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[datetime2]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
						<AttachedAnnotation Disambiguator="17" />
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Partner].[ModifiedBy]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[uniqueidentifier]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Partner].[ModifiedOn]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Scale" Value="7" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[datetime2]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
			</Relationship>
			<Relationship Name="Schema">
				<Entry>
					<References ExternalSource="BuiltIns" Name="[dbo]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="78" />
		</Element>
		<Element Type="SqlTable" Name="[dbo].[PartnerReferenceData]">
			<Property Name="IsAnsiNullsOn" Value="True" />
			<Relationship Name="Columns">
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReferenceData].[Id]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[uniqueidentifier]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
						<AttachedAnnotation Disambiguator="35" />
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReferenceData].[PartnerId]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[uniqueidentifier]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReferenceData].[Year]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[smallint]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReferenceData].[Cycle]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[tinyint]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReferenceData].[MetaId]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[uniqueidentifier]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReferenceData].[Data]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="IsMax" Value="True" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReferenceData].[CreatedBy]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[uniqueidentifier]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReferenceData].[CreatedOn]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Scale" Value="7" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[datetime2]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
						<AttachedAnnotation Disambiguator="36" />
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReferenceData].[ModifiedBy]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[uniqueidentifier]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReferenceData].[ModifiedOn]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Scale" Value="7" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[datetime2]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
			</Relationship>
			<Relationship Name="Schema">
				<Entry>
					<References ExternalSource="BuiltIns" Name="[dbo]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="79" />
			<AttachedAnnotation Disambiguator="59" />
			<AttachedAnnotation Disambiguator="58" />
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="80" />
		</Element>
		<Element Type="SqlTable" Name="[dbo].[PartnerReferenceDataMeta]">
			<Property Name="IsAnsiNullsOn" Value="True" />
			<Relationship Name="Columns">
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReferenceDataMeta].[Id]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[uniqueidentifier]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
						<AttachedAnnotation Disambiguator="44" />
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReferenceDataMeta].[FileName]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="500" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReferenceDataMeta].[Year]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[smallint]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReferenceDataMeta].[Cycle]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[tinyint]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReferenceDataMeta].[IsActive]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[bit]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
						<AttachedAnnotation Disambiguator="45" />
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReferenceDataMeta].[CreatedBy]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[uniqueidentifier]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReferenceDataMeta].[CreatedOn]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Scale" Value="7" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[datetime2]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
						<AttachedAnnotation Disambiguator="46" />
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReferenceDataMeta].[ModifiedBy]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[uniqueidentifier]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReferenceDataMeta].[ModifiedOn]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Scale" Value="7" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[datetime2]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
			</Relationship>
			<Relationship Name="Schema">
				<Entry>
					<References ExternalSource="BuiltIns" Name="[dbo]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="81" />
		</Element>
		<Element Type="SqlTable" Name="[dbo].[PartnerReferenceDataMetaDetails]">
			<Property Name="IsAnsiNullsOn" Value="True" />
			<Relationship Name="Columns">
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReferenceDataMetaDetails].[Id]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[uniqueidentifier]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
						<AttachedAnnotation Disambiguator="42" />
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReferenceDataMetaDetails].[MetaId]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[uniqueidentifier]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReferenceDataMetaDetails].[ColumnName]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="200" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReferenceDataMetaDetails].[NormalizedColumnName]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="200" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReferenceDataMetaDetails].[ColumnDataType]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[tinyint]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReferenceDataMetaDetails].[ColumnOrder]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[smallint]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReferenceDataMetaDetails].[CreatedBy]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[uniqueidentifier]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReferenceDataMetaDetails].[CreatedOn]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Scale" Value="7" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[datetime2]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
						<AttachedAnnotation Disambiguator="43" />
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReferenceDataMetaDetails].[ModifiedBy]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[uniqueidentifier]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReferenceDataMetaDetails].[ModifiedOn]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Scale" Value="7" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[datetime2]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
			</Relationship>
			<Relationship Name="Schema">
				<Entry>
					<References ExternalSource="BuiltIns" Name="[dbo]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="82" />
			<AttachedAnnotation Disambiguator="60" />
		</Element>
		<Element Type="SqlTable" Name="[dbo].[PartnerReferenceDataUpload]">
			<Property Name="IsAnsiNullsOn" Value="True" />
			<Relationship Name="Columns">
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReferenceDataUpload].[Id]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[uniqueidentifier]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
						<AttachedAnnotation Disambiguator="39" />
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReferenceDataUpload].[UploadFileName]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="500" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReferenceDataUpload].[FileContent]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="IsMax" Value="True" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[varbinary]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReferenceDataUpload].[Year]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[smallint]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReferenceDataUpload].[Cycle]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[tinyint]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReferenceDataUpload].[MetaId]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[uniqueidentifier]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReferenceDataUpload].[Status]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[tinyint]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
						<AttachedAnnotation Disambiguator="40" />
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReferenceDataUpload].[ValidationSummary]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="IsMax" Value="True" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReferenceDataUpload].[CreatedBy]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[uniqueidentifier]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReferenceDataUpload].[CreatedOn]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Scale" Value="7" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[datetime2]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
						<AttachedAnnotation Disambiguator="41" />
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReferenceDataUpload].[ModifiedBy]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[uniqueidentifier]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReferenceDataUpload].[ModifiedOn]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Scale" Value="7" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[datetime2]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
			</Relationship>
			<Relationship Name="Schema">
				<Entry>
					<References ExternalSource="BuiltIns" Name="[dbo]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="83" />
			<AttachedAnnotation Disambiguator="61" />
		</Element>
		<Element Type="SqlTable" Name="[dbo].[PartnerReferenceDataUploadDetails]">
			<Property Name="IsAnsiNullsOn" Value="True" />
			<Relationship Name="Columns">
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReferenceDataUploadDetails].[Id]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[uniqueidentifier]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
						<AttachedAnnotation Disambiguator="37" />
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReferenceDataUploadDetails].[PartnerReferenceDataUploadId]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[uniqueidentifier]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReferenceDataUploadDetails].[RowId]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[int]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReferenceDataUploadDetails].[Data]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="IsMax" Value="True" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReferenceDataUploadDetails].[ValidationError]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="500" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReferenceDataUploadDetails].[CreatedBy]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[uniqueidentifier]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReferenceDataUploadDetails].[CreatedOn]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Scale" Value="7" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[datetime2]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
						<AttachedAnnotation Disambiguator="38" />
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReferenceDataUploadDetails].[ModifiedBy]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[uniqueidentifier]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReferenceDataUploadDetails].[ModifiedOn]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Scale" Value="7" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[datetime2]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
			</Relationship>
			<Relationship Name="Schema">
				<Entry>
					<References ExternalSource="BuiltIns" Name="[dbo]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="84" />
			<AttachedAnnotation Disambiguator="62" />
		</Element>
		<Element Type="SqlTable" Name="[dbo].[PartnerReviewer]">
			<Property Name="IsAnsiNullsOn" Value="True" />
			<Relationship Name="Columns">
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReviewer].[Id]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[uniqueidentifier]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
						<AttachedAnnotation Disambiguator="47" />
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReviewer].[Year]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[smallint]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReviewer].[PartnerId]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[uniqueidentifier]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReviewer].[Exempt]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[bit]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
						<AttachedAnnotation Disambiguator="48" />
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReviewer].[LeadershipRole]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="100" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReviewer].[PrimaryReviewerId]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[uniqueidentifier]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReviewer].[PrimaryReviewerName]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="500" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReviewer].[SecondaryReviewerId]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[uniqueidentifier]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReviewer].[SecondaryReviewerName]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="500" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReviewer].[CreatedBy]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[uniqueidentifier]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReviewer].[CreatedOn]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Scale" Value="7" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[datetime2]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
						<AttachedAnnotation Disambiguator="49" />
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReviewer].[ModifiedBy]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[uniqueidentifier]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReviewer].[ModifiedOn]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Scale" Value="7" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[datetime2]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
			</Relationship>
			<Relationship Name="Schema">
				<Entry>
					<References ExternalSource="BuiltIns" Name="[dbo]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="85" />
			<AttachedAnnotation Disambiguator="54" />
			<AttachedAnnotation Disambiguator="63" />
			<AttachedAnnotation Disambiguator="64" />
			<AttachedAnnotation Disambiguator="65" />
		</Element>
		<Element Type="SqlTable" Name="[dbo].[PartnerReviewerUpload]">
			<Property Name="IsAnsiNullsOn" Value="True" />
			<Relationship Name="Columns">
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReviewerUpload].[Id]">
						<Property Name="IsNullable" Value="False" />
						<Property Name="IsIdentity" Value="True" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[int]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReviewerUpload].[Years]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="100" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReviewerUpload].[UploadFileName]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="500" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReviewerUpload].[ValidationSummary]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="IsMax" Value="True" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReviewerUpload].[FileContent]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="IsMax" Value="True" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[varbinary]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReviewerUpload].[Status]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[tinyint]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
						<AttachedAnnotation Disambiguator="3" />
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReviewerUpload].[CreatedBy]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[uniqueidentifier]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReviewerUpload].[CreatedOn]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Scale" Value="7" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[datetime2]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
						<AttachedAnnotation Disambiguator="4" />
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReviewerUpload].[ModifiedBy]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[uniqueidentifier]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReviewerUpload].[ModifiedOn]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Scale" Value="7" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[datetime2]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
			</Relationship>
			<Relationship Name="Schema">
				<Entry>
					<References ExternalSource="BuiltIns" Name="[dbo]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="86" />
		</Element>
		<Element Type="SqlTable" Name="[dbo].[PartnerReviewerUploadDetails]">
			<Property Name="IsAnsiNullsOn" Value="True" />
			<Relationship Name="Columns">
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReviewerUploadDetails].[Id]">
						<Property Name="IsNullable" Value="False" />
						<Property Name="IsIdentity" Value="True" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[bigint]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReviewerUploadDetails].[PartnerReviewerUploadId]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[int]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReviewerUploadDetails].[RowId]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[int]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReviewerUploadDetails].[EmployeeId]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="50" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReviewerUploadDetails].[EmployeeName]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="100" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReviewerUploadDetails].[Exempt]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="10" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReviewerUploadDetails].[LeadershipRole]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="100" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReviewerUploadDetails].[PrimaryReviewerId]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="50" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReviewerUploadDetails].[PrimaryReviewerName]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="100" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReviewerUploadDetails].[SecondaryReviewerId]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="50" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReviewerUploadDetails].[SecondaryReviewerName]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="100" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[PartnerReviewerUploadDetails].[ValidationError]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="500" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
			</Relationship>
			<Relationship Name="Schema">
				<Entry>
					<References ExternalSource="BuiltIns" Name="[dbo]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="87" />
			<AttachedAnnotation Disambiguator="66" />
		</Element>
		<Element Type="SqlTable" Name="[dbo].[Permission]">
			<Property Name="IsAnsiNullsOn" Value="True" />
			<Relationship Name="Columns">
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Permission].[Id]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[int]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Permission].[Name]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="50" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Permission].[IsActive]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[bit]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
						<AttachedAnnotation Disambiguator="28" />
					</Element>
				</Entry>
			</Relationship>
			<Relationship Name="Schema">
				<Entry>
					<References ExternalSource="BuiltIns" Name="[dbo]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="88" />
		</Element>
		<Element Type="SqlPrimaryKeyConstraint" Name="[dbo].[PK_Form]">
			<Relationship Name="ColumnSpecifications">
				<Entry>
					<Element Type="SqlIndexedColumnSpecification">
						<Relationship Name="Column">
							<Entry>
								<References Name="[dbo].[Form].[Id]" />
							</Entry>
						</Relationship>
					</Element>
				</Entry>
			</Relationship>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[Form]" />
				</Entry>
			</Relationship>
			<AttachedAnnotation Disambiguator="75" />
		</Element>
		<Element Type="SqlPrimaryKeyConstraint" Name="[dbo].[PK_Language]">
			<Relationship Name="ColumnSpecifications">
				<Entry>
					<Element Type="SqlIndexedColumnSpecification">
						<Relationship Name="Column">
							<Entry>
								<References Name="[dbo].[Language].[Id]" />
							</Entry>
						</Relationship>
					</Element>
				</Entry>
			</Relationship>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[Language]" />
				</Entry>
			</Relationship>
			<AttachedAnnotation Disambiguator="76" />
		</Element>
		<Element Type="SqlPrimaryKeyConstraint" Name="[dbo].[PK_Notification]">
			<Relationship Name="ColumnSpecifications">
				<Entry>
					<Element Type="SqlIndexedColumnSpecification">
						<Relationship Name="Column">
							<Entry>
								<References Name="[dbo].[Notification].[Id]" />
							</Entry>
						</Relationship>
					</Element>
				</Entry>
			</Relationship>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[Notification]" />
				</Entry>
			</Relationship>
			<AttachedAnnotation Disambiguator="77" />
		</Element>
		<Element Type="SqlPrimaryKeyConstraint" Name="[dbo].[PK_Partner]">
			<Relationship Name="ColumnSpecifications">
				<Entry>
					<Element Type="SqlIndexedColumnSpecification">
						<Relationship Name="Column">
							<Entry>
								<References Name="[dbo].[Partner].[Id]" />
							</Entry>
						</Relationship>
					</Element>
				</Entry>
			</Relationship>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[Partner]" />
				</Entry>
			</Relationship>
			<AttachedAnnotation Disambiguator="78" />
		</Element>
		<Element Type="SqlPrimaryKeyConstraint" Name="[dbo].[PK_PartnerReferenceData]">
			<Relationship Name="ColumnSpecifications">
				<Entry>
					<Element Type="SqlIndexedColumnSpecification">
						<Relationship Name="Column">
							<Entry>
								<References Name="[dbo].[PartnerReferenceData].[Id]" />
							</Entry>
						</Relationship>
					</Element>
				</Entry>
			</Relationship>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[PartnerReferenceData]" />
				</Entry>
			</Relationship>
			<AttachedAnnotation Disambiguator="79" />
		</Element>
		<Element Type="SqlPrimaryKeyConstraint" Name="[dbo].[PK_PartnerReferenceDataMeta]">
			<Relationship Name="ColumnSpecifications">
				<Entry>
					<Element Type="SqlIndexedColumnSpecification">
						<Relationship Name="Column">
							<Entry>
								<References Name="[dbo].[PartnerReferenceDataMeta].[Id]" />
							</Entry>
						</Relationship>
					</Element>
				</Entry>
			</Relationship>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[PartnerReferenceDataMeta]" />
				</Entry>
			</Relationship>
			<AttachedAnnotation Disambiguator="81" />
		</Element>
		<Element Type="SqlPrimaryKeyConstraint" Name="[dbo].[PK_PartnerReferenceDataMetaDetails]">
			<Relationship Name="ColumnSpecifications">
				<Entry>
					<Element Type="SqlIndexedColumnSpecification">
						<Relationship Name="Column">
							<Entry>
								<References Name="[dbo].[PartnerReferenceDataMetaDetails].[Id]" />
							</Entry>
						</Relationship>
					</Element>
				</Entry>
			</Relationship>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[PartnerReferenceDataMetaDetails]" />
				</Entry>
			</Relationship>
			<AttachedAnnotation Disambiguator="82" />
		</Element>
		<Element Type="SqlPrimaryKeyConstraint" Name="[dbo].[PK_PartnerReferenceDataUpload]">
			<Relationship Name="ColumnSpecifications">
				<Entry>
					<Element Type="SqlIndexedColumnSpecification">
						<Relationship Name="Column">
							<Entry>
								<References Name="[dbo].[PartnerReferenceDataUpload].[Id]" />
							</Entry>
						</Relationship>
					</Element>
				</Entry>
			</Relationship>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[PartnerReferenceDataUpload]" />
				</Entry>
			</Relationship>
			<AttachedAnnotation Disambiguator="83" />
		</Element>
		<Element Type="SqlPrimaryKeyConstraint" Name="[dbo].[PK_PartnerReferenceDataUploadDetails]">
			<Relationship Name="ColumnSpecifications">
				<Entry>
					<Element Type="SqlIndexedColumnSpecification">
						<Relationship Name="Column">
							<Entry>
								<References Name="[dbo].[PartnerReferenceDataUploadDetails].[Id]" />
							</Entry>
						</Relationship>
					</Element>
				</Entry>
			</Relationship>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[PartnerReferenceDataUploadDetails]" />
				</Entry>
			</Relationship>
			<AttachedAnnotation Disambiguator="84" />
		</Element>
		<Element Type="SqlPrimaryKeyConstraint" Name="[dbo].[PK_PartnerReviewer]">
			<Relationship Name="ColumnSpecifications">
				<Entry>
					<Element Type="SqlIndexedColumnSpecification">
						<Relationship Name="Column">
							<Entry>
								<References Name="[dbo].[PartnerReviewer].[Id]" />
							</Entry>
						</Relationship>
					</Element>
				</Entry>
			</Relationship>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[PartnerReviewer]" />
				</Entry>
			</Relationship>
			<AttachedAnnotation Disambiguator="85" />
		</Element>
		<Element Type="SqlPrimaryKeyConstraint" Name="[dbo].[PK_PartnerReviewerUpload]">
			<Relationship Name="ColumnSpecifications">
				<Entry>
					<Element Type="SqlIndexedColumnSpecification">
						<Relationship Name="Column">
							<Entry>
								<References Name="[dbo].[PartnerReviewerUpload].[Id]" />
							</Entry>
						</Relationship>
					</Element>
				</Entry>
			</Relationship>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[PartnerReviewerUpload]" />
				</Entry>
			</Relationship>
			<AttachedAnnotation Disambiguator="86" />
		</Element>
		<Element Type="SqlPrimaryKeyConstraint" Name="[dbo].[PK_PartnerReviewerUploadDetails]">
			<Relationship Name="ColumnSpecifications">
				<Entry>
					<Element Type="SqlIndexedColumnSpecification">
						<Relationship Name="Column">
							<Entry>
								<References Name="[dbo].[PartnerReviewerUploadDetails].[Id]" />
							</Entry>
						</Relationship>
					</Element>
				</Entry>
			</Relationship>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[PartnerReviewerUploadDetails]" />
				</Entry>
			</Relationship>
			<AttachedAnnotation Disambiguator="87" />
		</Element>
		<Element Type="SqlPrimaryKeyConstraint" Name="[dbo].[PK_Permission]">
			<Relationship Name="ColumnSpecifications">
				<Entry>
					<Element Type="SqlIndexedColumnSpecification">
						<Relationship Name="Column">
							<Entry>
								<References Name="[dbo].[Permission].[Id]" />
							</Entry>
						</Relationship>
					</Element>
				</Entry>
			</Relationship>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[Permission]" />
				</Entry>
			</Relationship>
			<AttachedAnnotation Disambiguator="88" />
		</Element>
		<Element Type="SqlPrimaryKeyConstraint" Name="[dbo].[PK_Questionnaire]">
			<Relationship Name="ColumnSpecifications">
				<Entry>
					<Element Type="SqlIndexedColumnSpecification">
						<Relationship Name="Column">
							<Entry>
								<References Name="[dbo].[Questionnaire].[Id]" />
							</Entry>
						</Relationship>
					</Element>
				</Entry>
			</Relationship>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[Questionnaire]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="89" />
		</Element>
		<Element Type="SqlPrimaryKeyConstraint" Name="[dbo].[PK_Role]">
			<Relationship Name="ColumnSpecifications">
				<Entry>
					<Element Type="SqlIndexedColumnSpecification">
						<Relationship Name="Column">
							<Entry>
								<References Name="[dbo].[Role].[Id]" />
							</Entry>
						</Relationship>
					</Element>
				</Entry>
			</Relationship>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[Role]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="90" />
		</Element>
		<Element Type="SqlPrimaryKeyConstraint" Name="[dbo].[PK_RolePermission]">
			<Relationship Name="ColumnSpecifications">
				<Entry>
					<Element Type="SqlIndexedColumnSpecification">
						<Relationship Name="Column">
							<Entry>
								<References Name="[dbo].[RolePermission].[Id]" />
							</Entry>
						</Relationship>
					</Element>
				</Entry>
			</Relationship>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[RolePermission]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="91" />
		</Element>
		<Element Type="SqlPrimaryKeyConstraint" Name="[dbo].[PK_User]">
			<Relationship Name="ColumnSpecifications">
				<Entry>
					<Element Type="SqlIndexedColumnSpecification">
						<Relationship Name="Column">
							<Entry>
								<References Name="[dbo].[User].[Id]" />
							</Entry>
						</Relationship>
					</Element>
				</Entry>
			</Relationship>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[User]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="92" />
		</Element>
		<Element Type="SqlPrimaryKeyConstraint" Name="[dbo].[PK_UserAnswer]">
			<Relationship Name="ColumnSpecifications">
				<Entry>
					<Element Type="SqlIndexedColumnSpecification">
						<Relationship Name="Column">
							<Entry>
								<References Name="[dbo].[UserAnswer].[Id]" />
							</Entry>
						</Relationship>
					</Element>
				</Entry>
			</Relationship>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[UserAnswer]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="93" />
		</Element>
		<Element Type="SqlPrimaryKeyConstraint" Name="[dbo].[PK_UserRole]">
			<Relationship Name="ColumnSpecifications">
				<Entry>
					<Element Type="SqlIndexedColumnSpecification">
						<Relationship Name="Column">
							<Entry>
								<References Name="[dbo].[UserRole].[Id]" />
							</Entry>
						</Relationship>
					</Element>
				</Entry>
			</Relationship>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[UserRole]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="94" />
		</Element>
		<Element Type="SqlTable" Name="[dbo].[Questionnaire]">
			<Property Name="IsAnsiNullsOn" Value="True" />
			<Relationship Name="Columns">
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Questionnaire].[Id]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[uniqueidentifier]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
						<AttachedAnnotation Disambiguator="8" />
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Questionnaire].[Name]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="100" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Questionnaire].[Year]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[smallint]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Questionnaire].[DefinitionJson]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="IsMax" Value="True" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Questionnaire].[DraftDefinitionJson]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="IsMax" Value="True" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Questionnaire].[FormSystemVersion]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[int]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
						<AttachedAnnotation Disambiguator="9" />
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Questionnaire].[Acknowledgement]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[bit]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
						<AttachedAnnotation Disambiguator="10" />
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Questionnaire].[AcknowledgementText]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="1500" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Questionnaire].[GeneralComments]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[bit]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
						<AttachedAnnotation Disambiguator="11" />
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Questionnaire].[GeneralCommentsText]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="IsMax" Value="True" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Questionnaire].[Status]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[tinyint]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
						<AttachedAnnotation Disambiguator="12" />
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Questionnaire].[IsActive]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[bit]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
						<AttachedAnnotation Disambiguator="13" />
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Questionnaire].[CreatedBy]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[uniqueidentifier]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Questionnaire].[CreatedOn]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Scale" Value="7" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[datetime2]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
						<AttachedAnnotation Disambiguator="14" />
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Questionnaire].[ModifiedBy]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[uniqueidentifier]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Questionnaire].[ModifiedOn]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Scale" Value="7" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[datetime2]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
			</Relationship>
			<Relationship Name="Schema">
				<Entry>
					<References ExternalSource="BuiltIns" Name="[dbo]" />
				</Entry>
			</Relationship>
			<AttachedAnnotation Disambiguator="89" />
			<AttachedAnnotation Disambiguator="67" />
		</Element>
		<Element Type="SqlTable" Name="[dbo].[QuestionnaireStatus]">
			<Property Name="IsAnsiNullsOn" Value="True" />
			<Relationship Name="Columns">
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[QuestionnaireStatus].[Id]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[tinyint]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
						<AttachedAnnotation Disambiguator="50" />
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[QuestionnaireStatus].[Name]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="50" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[QuestionnaireStatus].[EnglishDislayName]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="50" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[QuestionnaireStatus].[FrenchDisplayName]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="50" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
			</Relationship>
			<Relationship Name="Schema">
				<Entry>
					<References ExternalSource="BuiltIns" Name="[dbo]" />
				</Entry>
			</Relationship>
		</Element>
		<Element Type="SqlTable" Name="[dbo].[Role]">
			<Property Name="IsAnsiNullsOn" Value="True" />
			<Relationship Name="Columns">
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Role].[Id]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[int]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Role].[Name]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="50" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[Role].[IsActive]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[bit]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
						<AttachedAnnotation Disambiguator="29" />
					</Element>
				</Entry>
			</Relationship>
			<Relationship Name="Schema">
				<Entry>
					<References ExternalSource="BuiltIns" Name="[dbo]" />
				</Entry>
			</Relationship>
			<AttachedAnnotation Disambiguator="90" />
		</Element>
		<Element Type="SqlTable" Name="[dbo].[RolePermission]">
			<Property Name="IsAnsiNullsOn" Value="True" />
			<Relationship Name="Columns">
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[RolePermission].[Id]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[uniqueidentifier]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
						<AttachedAnnotation Disambiguator="27" />
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[RolePermission].[RoleId]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[int]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[RolePermission].[PermissionId]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[int]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
			</Relationship>
			<Relationship Name="Schema">
				<Entry>
					<References ExternalSource="BuiltIns" Name="[dbo]" />
				</Entry>
			</Relationship>
			<AttachedAnnotation Disambiguator="91" />
			<AttachedAnnotation Disambiguator="69" />
			<AttachedAnnotation Disambiguator="68" />
		</Element>
		<Element Type="SqlUniqueConstraint" Name="[dbo].[UK_PartnerReferenceData_Partner_Year_Cycle_Meta]">
			<Relationship Name="ColumnSpecifications">
				<Entry>
					<Element Type="SqlIndexedColumnSpecification">
						<Relationship Name="Column">
							<Entry>
								<References Name="[dbo].[PartnerReferenceData].[PartnerId]" />
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlIndexedColumnSpecification">
						<Relationship Name="Column">
							<Entry>
								<References Name="[dbo].[PartnerReferenceData].[Year]" />
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlIndexedColumnSpecification">
						<Relationship Name="Column">
							<Entry>
								<References Name="[dbo].[PartnerReferenceData].[Cycle]" />
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlIndexedColumnSpecification">
						<Relationship Name="Column">
							<Entry>
								<References Name="[dbo].[PartnerReferenceData].[MetaId]" />
							</Entry>
						</Relationship>
					</Element>
				</Entry>
			</Relationship>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[dbo].[PartnerReferenceData]" />
				</Entry>
			</Relationship>
			<AttachedAnnotation Disambiguator="80" />
		</Element>
		<Element Type="SqlTable" Name="[dbo].[User]">
			<Property Name="IsAnsiNullsOn" Value="True" />
			<Relationship Name="Columns">
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[User].[Id]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[uniqueidentifier]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
						<AttachedAnnotation Disambiguator="30" />
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[User].[AuthProviderId]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="50" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[User].[Username]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="50" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[User].[ObjectId]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="100" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[User].[Password]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="64" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[varbinary]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[User].[Salt]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="16" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[varbinary]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[User].[IsTempPasswordEnabled]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[bit]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
						<AttachedAnnotation Disambiguator="31" />
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[User].[DisplayName]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="50" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[User].[FirstName]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="50" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[User].[LastName]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="50" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[User].[Email]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="100" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[User].[LanguageId]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[int]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
						<AttachedAnnotation Disambiguator="32" />
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[User].[IsActive]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[bit]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
						<AttachedAnnotation Disambiguator="33" />
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[User].[CreatedBy]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[uniqueidentifier]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[User].[CreatedOn]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Scale" Value="7" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[datetime2]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
						<AttachedAnnotation Disambiguator="34" />
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[User].[ModifiedBy]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[uniqueidentifier]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[User].[ModifiedOn]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Scale" Value="7" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[datetime2]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
			</Relationship>
			<Relationship Name="Schema">
				<Entry>
					<References ExternalSource="BuiltIns" Name="[dbo]" />
				</Entry>
			</Relationship>
			<AttachedAnnotation Disambiguator="92" />
			<AttachedAnnotation Disambiguator="55" />
			<AttachedAnnotation Disambiguator="71" />
			<AttachedAnnotation Disambiguator="70" />
		</Element>
		<Element Type="SqlTable" Name="[dbo].[UserAnswer]">
			<Property Name="IsAnsiNullsOn" Value="True" />
			<Relationship Name="Columns">
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[UserAnswer].[Id]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[uniqueidentifier]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
						<AttachedAnnotation Disambiguator="5" />
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[UserAnswer].[FormId]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[uniqueidentifier]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[UserAnswer].[Answer]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="IsMax" Value="True" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[UserAnswer].[IsActive]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[bit]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
						<AttachedAnnotation Disambiguator="6" />
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[UserAnswer].[CreatedBy]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[uniqueidentifier]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[UserAnswer].[CreatedOn]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Scale" Value="7" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[datetime2]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
						<AttachedAnnotation Disambiguator="7" />
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[UserAnswer].[ModifiedBy]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[uniqueidentifier]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[UserAnswer].[ModifiedOn]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Scale" Value="7" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[datetime2]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
			</Relationship>
			<Relationship Name="Schema">
				<Entry>
					<References ExternalSource="BuiltIns" Name="[dbo]" />
				</Entry>
			</Relationship>
			<AttachedAnnotation Disambiguator="93" />
			<AttachedAnnotation Disambiguator="72" />
		</Element>
		<Element Type="SqlTable" Name="[dbo].[UserRole]">
			<Property Name="IsAnsiNullsOn" Value="True" />
			<Relationship Name="Columns">
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[UserRole].[Id]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[uniqueidentifier]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
						<AttachedAnnotation Disambiguator="25" />
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[UserRole].[UserId]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[uniqueidentifier]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[dbo].[UserRole].[RoleId]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[int]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
			</Relationship>
			<Relationship Name="Schema">
				<Entry>
					<References ExternalSource="BuiltIns" Name="[dbo]" />
				</Entry>
			</Relationship>
			<AttachedAnnotation Disambiguator="94" />
			<AttachedAnnotation Disambiguator="74" />
			<AttachedAnnotation Disambiguator="73" />
		</Element>
		<Element Type="SqlSchema" Name="[identity]">
			<Relationship Name="Authorizer">
				<Entry>
					<References ExternalSource="BuiltIns" Name="[dbo]" />
				</Entry>
			</Relationship>
		</Element>
		<Element Type="SqlTable" Name="[identity].[AuthProvider]">
			<Property Name="IsAnsiNullsOn" Value="True" />
			<Relationship Name="Columns">
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[identity].[AuthProvider].[Id]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="50" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[identity].[AuthProvider].[Name]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="100" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[identity].[AuthProvider].[IsActive]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[bit]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
						<AttachedAnnotation Disambiguator="23" />
					</Element>
				</Entry>
			</Relationship>
			<Relationship Name="Schema">
				<Entry>
					<References Name="[identity]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="95" />
		</Element>
		<Element Type="SqlTable" Name="[identity].[PersistedGrant]">
			<Property Name="IsAnsiNullsOn" Value="True" />
			<Relationship Name="Columns">
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[identity].[PersistedGrant].[Key]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="100" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[identity].[PersistedGrant].[Type]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="50" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[identity].[PersistedGrant].[SubjectId]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="100" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[identity].[PersistedGrant].[SessionId]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="100" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[identity].[PersistedGrant].[ClientId]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="50" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[identity].[PersistedGrant].[Description]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Length" Value="200" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[identity].[PersistedGrant].[CreationTime]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Scale" Value="7" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[datetime2]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[identity].[PersistedGrant].[Expiration]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Scale" Value="7" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[datetime2]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[identity].[PersistedGrant].[ConsumedTime]">
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="Scale" Value="7" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[datetime2]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[identity].[PersistedGrant].[Data]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="IsMax" Value="True" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
			</Relationship>
			<Relationship Name="Schema">
				<Entry>
					<References Name="[identity]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="96" />
		</Element>
		<Element Type="SqlPrimaryKeyConstraint" Name="[identity].[PK_AuthProvider]">
			<Relationship Name="ColumnSpecifications">
				<Entry>
					<Element Type="SqlIndexedColumnSpecification">
						<Relationship Name="Column">
							<Entry>
								<References Name="[identity].[AuthProvider].[Id]" />
							</Entry>
						</Relationship>
					</Element>
				</Entry>
			</Relationship>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[identity].[AuthProvider]" />
				</Entry>
			</Relationship>
			<AttachedAnnotation Disambiguator="95" />
		</Element>
		<Element Type="SqlPrimaryKeyConstraint" Name="[identity].[PK_PersistedGrant]">
			<Relationship Name="ColumnSpecifications">
				<Entry>
					<Element Type="SqlIndexedColumnSpecification">
						<Relationship Name="Column">
							<Entry>
								<References Name="[identity].[PersistedGrant].[Key]" />
							</Entry>
						</Relationship>
					</Element>
				</Entry>
			</Relationship>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[identity].[PersistedGrant]" />
				</Entry>
			</Relationship>
			<AttachedAnnotation Disambiguator="96" />
		</Element>
		<Element Type="SqlPrimaryKeyConstraint" Name="[identity].[PK_SigningCredential]">
			<Relationship Name="ColumnSpecifications">
				<Entry>
					<Element Type="SqlIndexedColumnSpecification">
						<Relationship Name="Column">
							<Entry>
								<References Name="[identity].[SigningCredential].[Id]" />
							</Entry>
						</Relationship>
					</Element>
				</Entry>
			</Relationship>
			<Relationship Name="DefiningTable">
				<Entry>
					<References Name="[identity].[SigningCredential]" />
				</Entry>
			</Relationship>
			<Annotation Type="SqlInlineConstraintAnnotation" Disambiguator="97" />
		</Element>
		<Element Type="SqlTable" Name="[identity].[SigningCredential]">
			<Property Name="IsAnsiNullsOn" Value="True" />
			<Relationship Name="Columns">
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[identity].[SigningCredential].[Id]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[uniqueidentifier]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
				<Entry>
					<Element Type="SqlSimpleColumn" Name="[identity].[SigningCredential].[Value]">
						<Property Name="IsNullable" Value="False" />
						<Relationship Name="TypeSpecifier">
							<Entry>
								<Element Type="SqlTypeSpecifier">
									<Property Name="IsMax" Value="True" />
									<Relationship Name="Type">
										<Entry>
											<References ExternalSource="BuiltIns" Name="[nvarchar]" />
										</Entry>
									</Relationship>
								</Element>
							</Entry>
						</Relationship>
					</Element>
				</Entry>
			</Relationship>
			<Relationship Name="Schema">
				<Entry>
					<References Name="[identity]" />
				</Entry>
			</Relationship>
			<AttachedAnnotation Disambiguator="97" />
		</Element>
	</Model>
</DataSchemaModel>