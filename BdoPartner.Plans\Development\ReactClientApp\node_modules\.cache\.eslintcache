[{"C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\index.js": "1", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\App.js": "2", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\redux\\store.js": "3", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\config\\i18nConfig.js": "4", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\config\\appConfig.js": "5", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\routes\\routes.js": "6", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\redux\\reducers\\rootReducer.js": "7", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\auth\\components\\authProvider.jsx": "8", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\message\\components\\messageBox.jsx": "9", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\message\\components\\messageDialog.jsx": "10", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\message\\components\\messageToast.jsx": "11", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\components\\base\\navbar.jsx": "12", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\loading\\components\\appProgress.jsx": "13", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\redux\\reducers\\languageReducer.js": "14", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\routes\\privateRoute.js": "15", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\enumertions\\role.js": "16", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\auth\\components\\logout.jsx": "17", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\auth\\components\\logoutCallback.jsx": "18", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\auth\\components\\callback.jsx": "19", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\auth\\components\\silentRenew.jsx": "20", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\pages\\usermanagement.jsx": "21", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\auth\\authService.js": "22", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\pages\\userprofile.jsx": "23", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\pages\\sample.jsx": "24", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\pages\\notfound.jsx": "25", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\pages\\myPartnerPlan.jsx": "26", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\pages\\partnerPlan.jsx": "27", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\pages\\admin\\questionnaireManagement.jsx": "28", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\pages\\admin\\partnerRefereneDataManagement.jsx": "29", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\pages\\admin\\questionnaireViewer.jsx": "30", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\pages\\dashboard.jsx": "31", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\pages\\admin\\partnerAnnualPlansReviewer.jsx": "32", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\pages\\admin\\questionnaireDesigner.jsx": "33", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\message\\messageService.js": "34", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\pages\\admin\\partnerAnnualPlansAdmin.jsx": "35", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\redux\\actionCreatorsExport.js": "36", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\loading\\loadingService.js": "37", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\enumertions\\resultStatus.js": "38", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\enumertions\\language.js": "39", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\config\\identityClientConfig.js": "40", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\redux\\actions.js": "41", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\config\\identityMetaConfig.js": "42", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\http\\httpClient.js": "43", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\enumertions\\questionnaireStatus.js": "44", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\enumertions\\partnerPlanCycle.js": "45", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\services\\formService.js": "46", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\surveyjs\\licenseConfig.js": "47", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\components\\questionnaire\\PartnerPlanQuestionnaire.jsx": "48", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\components\\dashboard\\MyPastPlan.jsx": "49", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\services\\questionnaireService.js": "50", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\components\\dashboard\\AdminPartnerPlans.jsx": "51", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\components\\dashboard\\MyCurrentPlan.jsx": "52", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\components\\dashboard\\ReviewerPartnerPlans.jsx": "53", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\components\\admin\\CopyQuestionnaireModal.jsx": "54", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\components\\admin\\UploadPartnerReviewerAssignment.jsx": "55", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\components\\admin\\batchReopenPopup.jsx": "56", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\components\\admin\\PartnerReviewerManagement.jsx": "57", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\components\\admin\\PartnerAnnualPlansTable.jsx": "58", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\components\\admin\\UploadPartnerReferenceData.jsx": "59", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\components\\admin\\QuestionnaireDesignerCore.jsx": "60", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\components\\admin\\CyclePublishConfirmationPopup.jsx": "61", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\components\\admin\\QuestionnaireDesignerHeader.jsx": "62", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\components\\admin\\PartnerReferenceDataMaintain.jsx": "63", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\redux\\actionCreators\\languageCreator.js": "64", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\services\\partnerReferenceDataUploadService.js": "65", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\enumertions\\formStatus.js": "66", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\enumertions\\userFormRole.js": "67", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\components\\questionnaire\\formAnswerUtilities.js": "68", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\components\\questionnaire\\pdfExportUtilities.js": "69", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\services\\partnerAnnualPlanService.js": "70", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\http\\requestDeduplication.js": "71", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\loading\\hooks\\useLoadingControl.js": "72", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\enumertions\\partnerReviewerUploadStatus.js": "73", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\utils\\dateUtils.js": "74", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\services\\partnerReviewerUploadService.js": "75", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\utils\\surveyCustomPropertiesUtils.js": "76", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\enumertions\\partnerReferenceDataUploadStatus.js": "77", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\components\\common\\ReviewerCommentsDialog.jsx": "78", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\components\\common\\PartnerAutocomplete.jsx": "79", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\components\\audit\\AdminModificationAuditHistory.jsx": "80", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\components\\admin\\PartnerPlanDetailsDialog.jsx": "81", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\services\\lookupService.js": "82", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\services\\auditService.js": "83", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\services\\partnerService.js": "84", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\utils\\auditUtils.js": "85"}, {"size": 1414, "mtime": 1757526528126, "results": "86", "hashOfConfig": "87"}, {"size": 3061, "mtime": 1757526528066, "results": "88", "hashOfConfig": "87"}, {"size": 1783, "mtime": 1757526528143, "results": "89", "hashOfConfig": "87"}, {"size": 1668, "mtime": 1757526528101, "results": "90", "hashOfConfig": "87"}, {"size": 2950, "mtime": 1757526528100, "results": "91", "hashOfConfig": "87"}, {"size": 3683, "mtime": 1757526528148, "results": "92", "hashOfConfig": "87"}, {"size": 404, "mtime": 1757526528142, "results": "93", "hashOfConfig": "87"}, {"size": 1815, "mtime": 1757526528097, "results": "94", "hashOfConfig": "87"}, {"size": 2031, "mtime": 1757526528118, "results": "95", "hashOfConfig": "87"}, {"size": 5123, "mtime": 1757526528118, "results": "96", "hashOfConfig": "87"}, {"size": 1673, "mtime": 1757526528120, "results": "97", "hashOfConfig": "87"}, {"size": 4315, "mtime": 1757526528082, "results": "98", "hashOfConfig": "87"}, {"size": 1781, "mtime": 1757526528114, "results": "99", "hashOfConfig": "87"}, {"size": 986, "mtime": 1757526528142, "results": "100", "hashOfConfig": "87"}, {"size": 2439, "mtime": 1757526528147, "results": "101", "hashOfConfig": "87"}, {"size": 366, "mtime": 1757526528109, "results": "102", "hashOfConfig": "87"}, {"size": 281, "mtime": 1757526528098, "results": "103", "hashOfConfig": "87"}, {"size": 348, "mtime": 1757526528098, "results": "104", "hashOfConfig": "87"}, {"size": 1582, "mtime": 1757526528097, "results": "105", "hashOfConfig": "87"}, {"size": 316, "mtime": 1757526528099, "results": "106", "hashOfConfig": "87"}, {"size": 857, "mtime": 1757526528136, "results": "107", "hashOfConfig": "87"}, {"size": 12517, "mtime": 1757526528096, "results": "108", "hashOfConfig": "87"}, {"size": 342, "mtime": 1757526528137, "results": "109", "hashOfConfig": "87"}, {"size": 8679, "mtime": 1757526528135, "results": "110", "hashOfConfig": "87"}, {"size": 123, "mtime": 1757526528133, "results": "111", "hashOfConfig": "87"}, {"size": 436, "mtime": 1757526528132, "results": "112", "hashOfConfig": "87"}, {"size": 1430, "mtime": 1757526528134, "results": "113", "hashOfConfig": "87"}, {"size": 23142, "mtime": 1757526528131, "results": "114", "hashOfConfig": "87"}, {"size": 2178, "mtime": 1757526528129, "results": "115", "hashOfConfig": "87"}, {"size": 6781, "mtime": 1757526528131, "results": "116", "hashOfConfig": "87"}, {"size": 4218, "mtime": 1757526528132, "results": "117", "hashOfConfig": "87"}, {"size": 1439, "mtime": 1757526528127, "results": "118", "hashOfConfig": "87"}, {"size": 17301, "mtime": 1757526528130, "results": "119", "hashOfConfig": "87"}, {"size": 6583, "mtime": 1757526528120, "results": "120", "hashOfConfig": "87"}, {"size": 1664, "mtime": 1757526528127, "results": "121", "hashOfConfig": "87"}, {"size": 67, "mtime": 1757526528139, "results": "122", "hashOfConfig": "87"}, {"size": 5792, "mtime": 1757526528116, "results": "123", "hashOfConfig": "87"}, {"size": 204, "mtime": 1757526528109, "results": "124", "hashOfConfig": "87"}, {"size": 72, "mtime": 1757526528104, "results": "125", "hashOfConfig": "87"}, {"size": 2426, "mtime": 1757526528101, "results": "126", "hashOfConfig": "87"}, {"size": 196, "mtime": 1757526528140, "results": "127", "hashOfConfig": "87"}, {"size": 870, "mtime": 1757526528102, "results": "128", "hashOfConfig": "87"}, {"size": 4860, "mtime": 1757526528111, "results": "129", "hashOfConfig": "87"}, {"size": 2933, "mtime": 1757526528107, "results": "130", "hashOfConfig": "87"}, {"size": 954, "mtime": 1757526528104, "results": "131", "hashOfConfig": "87"}, {"size": 10890, "mtime": 1757526528150, "results": "132", "hashOfConfig": "87"}, {"size": 726, "mtime": 1757526528122, "results": "133", "hashOfConfig": "87"}, {"size": 70742, "mtime": 1757539283516, "results": "134", "hashOfConfig": "87"}, {"size": 2871, "mtime": 1757526528087, "results": "135", "hashOfConfig": "87"}, {"size": 19771, "mtime": 1757526528154, "results": "136", "hashOfConfig": "87"}, {"size": 8962, "mtime": 1757526528086, "results": "137", "hashOfConfig": "87"}, {"size": 10049, "mtime": 1757526528086, "results": "138", "hashOfConfig": "87"}, {"size": 8116, "mtime": 1757526528088, "results": "139", "hashOfConfig": "87"}, {"size": 3957, "mtime": 1757526528068, "results": "140", "hashOfConfig": "87"}, {"size": 19951, "mtime": 1757526528078, "results": "141", "hashOfConfig": "87"}, {"size": 9788, "mtime": 1757526528078, "results": "142", "hashOfConfig": "87"}, {"size": 14784, "mtime": 1757526528074, "results": "143", "hashOfConfig": "87"}, {"size": 31398, "mtime": 1757526528071, "results": "144", "hashOfConfig": "87"}, {"size": 22815, "mtime": 1757526528077, "results": "145", "hashOfConfig": "87"}, {"size": 15021, "mtime": 1757526528075, "results": "146", "hashOfConfig": "87"}, {"size": 6337, "mtime": 1757526528069, "results": "147", "hashOfConfig": "87"}, {"size": 9223, "mtime": 1757526528076, "results": "148", "hashOfConfig": "87"}, {"size": 14075, "mtime": 1757526528073, "results": "149", "hashOfConfig": "87"}, {"size": 711, "mtime": 1757526528138, "results": "150", "hashOfConfig": "87"}, {"size": 17379, "mtime": 1757526528152, "results": "151", "hashOfConfig": "87"}, {"size": 6548, "mtime": 1757526528103, "results": "152", "hashOfConfig": "87"}, {"size": 3087, "mtime": 1757526528110, "results": "153", "hashOfConfig": "87"}, {"size": 694, "mtime": 1757526528093, "results": "154", "hashOfConfig": "87"}, {"size": 22506, "mtime": 1757526528094, "results": "155", "hashOfConfig": "87"}, {"size": 4923, "mtime": 1757526528151, "results": "156", "hashOfConfig": "87"}, {"size": 2595, "mtime": 1757526528112, "results": "157", "hashOfConfig": "87"}, {"size": 2261, "mtime": 1757526528116, "results": "158", "hashOfConfig": "87"}, {"size": 538, "mtime": 1757526528106, "results": "159", "hashOfConfig": "87"}, {"size": 6352, "mtime": 1757526528123, "results": "160", "hashOfConfig": "87"}, {"size": 9884, "mtime": 1757526528153, "results": "161", "hashOfConfig": "87"}, {"size": 15932, "mtime": 1757526528124, "results": "162", "hashOfConfig": "87"}, {"size": 551, "mtime": 1757526528105, "results": "163", "hashOfConfig": "87"}, {"size": 3717, "mtime": 1757526528084, "results": "164", "hashOfConfig": "87"}, {"size": 6116, "mtime": 1757526528083, "results": "165", "hashOfConfig": "87"}, {"size": 8595, "mtime": 1757526528081, "results": "166", "hashOfConfig": "87"}, {"size": 8788, "mtime": 1757526528072, "results": "167", "hashOfConfig": "87"}, {"size": 8593, "mtime": 1757526528150, "results": "168", "hashOfConfig": "87"}, {"size": 4770, "mtime": 1757526528149, "results": "169", "hashOfConfig": "87"}, {"size": 5864, "mtime": 1757526528153, "results": "170", "hashOfConfig": "87"}, {"size": 5696, "mtime": 1757526528123, "results": "171", "hashOfConfig": "87"}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "10r84jr", {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\index.js", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\App.js", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\redux\\store.js", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\config\\i18nConfig.js", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\config\\appConfig.js", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\routes\\routes.js", ["427"], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\redux\\reducers\\rootReducer.js", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\auth\\components\\authProvider.jsx", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\message\\components\\messageBox.jsx", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\message\\components\\messageDialog.jsx", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\message\\components\\messageToast.jsx", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\components\\base\\navbar.jsx", ["428"], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\loading\\components\\appProgress.jsx", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\redux\\reducers\\languageReducer.js", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\routes\\privateRoute.js", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\enumertions\\role.js", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\auth\\components\\logout.jsx", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\auth\\components\\logoutCallback.jsx", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\auth\\components\\callback.jsx", ["429"], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\auth\\components\\silentRenew.jsx", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\pages\\usermanagement.jsx", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\auth\\authService.js", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\pages\\userprofile.jsx", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\pages\\sample.jsx", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\pages\\notfound.jsx", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\pages\\myPartnerPlan.jsx", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\pages\\partnerPlan.jsx", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\pages\\admin\\questionnaireManagement.jsx", ["430", "431", "432"], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\pages\\admin\\partnerRefereneDataManagement.jsx", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\pages\\admin\\questionnaireViewer.jsx", ["433"], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\pages\\dashboard.jsx", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\pages\\admin\\partnerAnnualPlansReviewer.jsx", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\pages\\admin\\questionnaireDesigner.jsx", [], ["434"], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\message\\messageService.js", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\pages\\admin\\partnerAnnualPlansAdmin.jsx", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\redux\\actionCreatorsExport.js", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\loading\\loadingService.js", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\enumertions\\resultStatus.js", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\enumertions\\language.js", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\config\\identityClientConfig.js", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\redux\\actions.js", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\config\\identityMetaConfig.js", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\http\\httpClient.js", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\enumertions\\questionnaireStatus.js", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\enumertions\\partnerPlanCycle.js", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\services\\formService.js", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\surveyjs\\licenseConfig.js", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\components\\questionnaire\\PartnerPlanQuestionnaire.jsx", ["435", "436", "437", "438", "439", "440", "441", "442", "443", "444"], ["445"], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\components\\dashboard\\MyPastPlan.jsx", ["446"], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\services\\questionnaireService.js", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\components\\dashboard\\AdminPartnerPlans.jsx", [], ["447"], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\components\\dashboard\\MyCurrentPlan.jsx", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\components\\dashboard\\ReviewerPartnerPlans.jsx", ["448"], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\components\\admin\\CopyQuestionnaireModal.jsx", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\components\\admin\\UploadPartnerReviewerAssignment.jsx", ["449"], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\components\\admin\\batchReopenPopup.jsx", ["450"], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\components\\admin\\PartnerReviewerManagement.jsx", ["451", "452"], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\components\\admin\\PartnerAnnualPlansTable.jsx", ["453", "454", "455", "456", "457", "458"], ["459", "460"], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\components\\admin\\UploadPartnerReferenceData.jsx", [], ["461"], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\components\\admin\\QuestionnaireDesignerCore.jsx", [], ["462"], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\components\\admin\\CyclePublishConfirmationPopup.jsx", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\components\\admin\\QuestionnaireDesignerHeader.jsx", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\components\\admin\\PartnerReferenceDataMaintain.jsx", ["463", "464"], ["465"], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\redux\\actionCreators\\languageCreator.js", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\services\\partnerReferenceDataUploadService.js", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\enumertions\\formStatus.js", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\enumertions\\userFormRole.js", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\components\\questionnaire\\formAnswerUtilities.js", ["466"], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\components\\questionnaire\\pdfExportUtilities.js", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\services\\partnerAnnualPlanService.js", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\http\\requestDeduplication.js", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\loading\\hooks\\useLoadingControl.js", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\enumertions\\partnerReviewerUploadStatus.js", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\utils\\dateUtils.js", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\services\\partnerReviewerUploadService.js", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\utils\\surveyCustomPropertiesUtils.js", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\enumertions\\partnerReferenceDataUploadStatus.js", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\components\\common\\ReviewerCommentsDialog.jsx", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\components\\common\\PartnerAutocomplete.jsx", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\components\\audit\\AdminModificationAuditHistory.jsx", ["467", "468", "469", "470"], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\components\\admin\\PartnerPlanDetailsDialog.jsx", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\services\\lookupService.js", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\services\\auditService.js", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\services\\partnerService.js", [], [], "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\src\\core\\utils\\auditUtils.js", [], [], {"ruleId": "471", "severity": 1, "message": "472", "line": 7, "column": 10, "nodeType": "473", "messageId": "474", "endLine": 7, "endColumn": 16}, {"ruleId": "471", "severity": 1, "message": "475", "line": 88, "column": 9, "nodeType": "473", "messageId": "474", "endLine": 88, "endColumn": 25}, {"ruleId": "471", "severity": 1, "message": "476", "line": 7, "column": 12, "nodeType": "473", "messageId": "474", "endLine": 7, "endColumn": 17}, {"ruleId": "477", "severity": 1, "message": "478", "line": 83, "column": 6, "nodeType": "479", "endLine": 83, "endColumn": 8, "suggestions": "480"}, {"ruleId": "471", "severity": 1, "message": "481", "line": 181, "column": 9, "nodeType": "473", "messageId": "474", "endLine": 181, "endColumn": 35}, {"ruleId": "471", "severity": 1, "message": "482", "line": 288, "column": 9, "nodeType": "473", "messageId": "474", "endLine": 288, "endColumn": 37}, {"ruleId": "477", "severity": 1, "message": "483", "line": 34, "column": 6, "nodeType": "479", "endLine": 34, "endColumn": 10, "suggestions": "484"}, {"ruleId": "477", "severity": 1, "message": "485", "line": 46, "column": 6, "nodeType": "479", "endLine": 46, "endColumn": 10, "suggestions": "486", "suppressions": "487"}, {"ruleId": "471", "severity": 1, "message": "488", "line": 12, "column": 61, "nodeType": "473", "messageId": "474", "endLine": 12, "endColumn": 82}, {"ruleId": "471", "severity": 1, "message": "489", "line": 454, "column": 10, "nodeType": "473", "messageId": "474", "endLine": 454, "endColumn": 25}, {"ruleId": "471", "severity": 1, "message": "490", "line": 467, "column": 10, "nodeType": "473", "messageId": "474", "endLine": 467, "endColumn": 26}, {"ruleId": "471", "severity": 1, "message": "491", "line": 473, "column": 10, "nodeType": "473", "messageId": "474", "endLine": 473, "endColumn": 29}, {"ruleId": "471", "severity": 1, "message": "492", "line": 485, "column": 9, "nodeType": "473", "messageId": "474", "endLine": 485, "endColumn": 24}, {"ruleId": "471", "severity": 1, "message": "493", "line": 486, "column": 9, "nodeType": "473", "messageId": "474", "endLine": 486, "endColumn": 22}, {"ruleId": "471", "severity": 1, "message": "494", "line": 618, "column": 23, "nodeType": "473", "messageId": "474", "endLine": 618, "endColumn": 36}, {"ruleId": "471", "severity": 1, "message": "495", "line": 622, "column": 23, "nodeType": "473", "messageId": "474", "endLine": 622, "endColumn": 33}, {"ruleId": "471", "severity": 1, "message": "496", "line": 1056, "column": 9, "nodeType": "473", "messageId": "474", "endLine": 1056, "endColumn": 26}, {"ruleId": "471", "severity": 1, "message": "497", "line": 1111, "column": 9, "nodeType": "473", "messageId": "474", "endLine": 1111, "endColumn": 20}, {"ruleId": "477", "severity": 1, "message": "498", "line": 946, "column": 6, "nodeType": "479", "endLine": 946, "endColumn": 31, "suggestions": "499", "suppressions": "500"}, {"ruleId": "471", "severity": 1, "message": "501", "line": 6, "column": 8, "nodeType": "473", "messageId": "474", "endLine": 6, "endColumn": 18}, {"ruleId": "477", "severity": 1, "message": "502", "line": 50, "column": 6, "nodeType": "479", "endLine": 50, "endColumn": 8, "suggestions": "503", "suppressions": "504"}, {"ruleId": "477", "severity": 1, "message": "505", "line": 44, "column": 6, "nodeType": "479", "endLine": 44, "endColumn": 8, "suggestions": "506"}, {"ruleId": "477", "severity": 1, "message": "507", "line": 57, "column": 6, "nodeType": "479", "endLine": 57, "endColumn": 19, "suggestions": "508"}, {"ruleId": "477", "severity": 1, "message": "509", "line": 97, "column": 6, "nodeType": "479", "endLine": 97, "endColumn": 15, "suggestions": "510"}, {"ruleId": "471", "severity": 1, "message": "511", "line": 22, "column": 10, "nodeType": "473", "messageId": "474", "endLine": 22, "endColumn": 33}, {"ruleId": "477", "severity": 1, "message": "512", "line": 42, "column": 6, "nodeType": "479", "endLine": 42, "endColumn": 20, "suggestions": "513"}, {"ruleId": "471", "severity": 1, "message": "514", "line": 15, "column": 10, "nodeType": "473", "messageId": "474", "endLine": 15, "endColumn": 28}, {"ruleId": "471", "severity": 1, "message": "515", "line": 249, "column": 9, "nodeType": "473", "messageId": "474", "endLine": 249, "endColumn": 21}, {"ruleId": "471", "severity": 1, "message": "516", "line": 436, "column": 9, "nodeType": "473", "messageId": "474", "endLine": 436, "endColumn": 30}, {"ruleId": "471", "severity": 1, "message": "517", "line": 447, "column": 9, "nodeType": "473", "messageId": "474", "endLine": 447, "endColumn": 31}, {"ruleId": "471", "severity": 1, "message": "518", "line": 458, "column": 9, "nodeType": "473", "messageId": "474", "endLine": 458, "endColumn": 35}, {"ruleId": "477", "severity": 1, "message": "519", "line": 626, "column": 5, "nodeType": "479", "endLine": 626, "endColumn": 34, "suggestions": "520"}, {"ruleId": "477", "severity": 1, "message": "521", "line": 246, "column": 6, "nodeType": "479", "endLine": 246, "endColumn": 8, "suggestions": "522", "suppressions": "523"}, {"ruleId": "477", "severity": 1, "message": "524", "line": 259, "column": 6, "nodeType": "479", "endLine": 259, "endColumn": 121, "suggestions": "525", "suppressions": "526"}, {"ruleId": "477", "severity": 1, "message": "507", "line": 62, "column": 6, "nodeType": "479", "endLine": 62, "endColumn": 19, "suggestions": "527", "suppressions": "528"}, {"ruleId": "477", "severity": 1, "message": "529", "line": 174, "column": 8, "nodeType": "479", "endLine": 174, "endColumn": 20, "suggestions": "530", "suppressions": "531"}, {"ruleId": "471", "severity": 1, "message": "532", "line": 12, "column": 10, "nodeType": "473", "messageId": "474", "endLine": 12, "endColumn": 26}, {"ruleId": "471", "severity": 1, "message": "533", "line": 25, "column": 29, "nodeType": "473", "messageId": "474", "endLine": 25, "endColumn": 49}, {"ruleId": "477", "severity": 1, "message": "534", "line": 54, "column": 6, "nodeType": "479", "endLine": 54, "endColumn": 67, "suggestions": "535", "suppressions": "536"}, {"ruleId": "537", "severity": 1, "message": "538", "line": 9, "column": 16, "nodeType": "539", "messageId": "540", "endLine": 9, "endColumn": 17, "suggestions": "541"}, {"ruleId": "477", "severity": 1, "message": "542", "line": 32, "column": 6, "nodeType": "479", "endLine": 32, "endColumn": 23, "suggestions": "543"}, {"ruleId": "471", "severity": 1, "message": "544", "line": 67, "column": 9, "nodeType": "473", "messageId": "474", "endLine": 67, "endColumn": 27}, {"ruleId": "471", "severity": 1, "message": "545", "line": 73, "column": 9, "nodeType": "473", "messageId": "474", "endLine": 73, "endColumn": 26}, {"ruleId": "471", "severity": 1, "message": "546", "line": 79, "column": 9, "nodeType": "473", "messageId": "474", "endLine": 79, "endColumn": 31}, "no-unused-vars", "'Sample' is defined but never used.", "Identifier", "unusedVar", "'languageDropdown' is assigned a value but never used.", "'error' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'globalFilter', 'loadQuestionnaires', and 'rows'. Either include them or remove the dependency array.", "ArrayExpression", ["547"], "'handlePublishQuestionnaire' is assigned a value but never used.", "'handleUnpublishQuestionnaire' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadQuestionnaire'. Either include it or remove the dependency array.", ["548"], "React Hook useEffect has missing dependencies: 'loadQuestionnaire' and 'navigate'. Either include them or remove the dependency array.", ["549"], ["550"], "'isFormEditableByOwner' is defined but never used.", "'currentPageInfo' is assigned a value but never used.", "'formAccessConfig' is assigned a value but never used.", "'isSubmissionAllowed' is assigned a value but never used.", "'isMyPartnerPlan' is assigned a value but never used.", "'isPartnerPlan' is assigned a value but never used.", "'firstQuestion' is assigned a value but never used.", "'firstPanel' is assigned a value but never used.", "'handleExportToPDF' is assigned a value but never used.", "'adminReopen' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'authService' and 'surveyValueChanged'. Either include them or remove the dependency array.", ["551"], ["552"], "'APP_CONFIG' is defined but never used.", "React Hook useEffect has missing dependencies: 'authService' and 'user.roles'. Either include them or remove the dependency array.", ["553"], ["554"], "React Hook useEffect has a missing dependency: 'authService'. Either include it or remove the dependency array.", ["555"], "React Hook useEffect has a missing dependency: 'loadUploads'. Either include it or remove the dependency array.", ["556"], "React Hook useEffect has a missing dependency: 'fetchCompletedCount'. Either include it or remove the dependency array.", ["557"], "'selectedPartnerReviewer' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadPartnerReviewers'. Either include it or remove the dependency array.", ["558"], "'getFormStatusClass' is defined but never used.", "'handleSearch' is assigned a value but never used.", "'createdByBodyTemplate' is assigned a value but never used.", "'modifiedByBodyTemplate' is assigned a value but never used.", "'planSubmissionBodyTemplate' is assigned a value but never used.", "React Hook useCallback has missing dependencies: 'canSendBackToPartner', 'handleShowPlanDetails', and 'handleViewPlan'. Either include them or remove the dependency array.", ["559"], "React Hook useEffect has missing dependencies: 'globalFilter', 'loadFilterOptions', 'loadPartnerPlans', and 'rows'. Either include them or remove the dependency array.", ["560"], ["561"], "React Hook useEffect has missing dependencies: 'globalFilter', 'loadPartnerPlans', and 'rows'. Either include them or remove the dependency array.", ["562"], ["563"], ["564"], ["565"], "React Hook useEffect has missing dependencies: 'handleBackToList', 'saveSurveyWithCreator', and 'selectedYear'. Either include them or remove the dependency array.", ["566"], ["567"], "'PartnerPlanCycle' is defined but never used.", "'setSelectedPartnerId' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadPartnerReferenceData'. Either include it or remove the dependency array.", ["568"], ["569"], "no-useless-escape", "Unnecessary escape character: \\>.", "Literal", "unnecessaryEscape", ["570", "571"], "React Hook useEffect has a missing dependency: 'loadAuditHistory'. Either include it or remove the dependency array.", ["572"], "'actionBodyTemplate' is assigned a value but never used.", "'cycleBodyTemplate' is assigned a value but never used.", "'formStatusBodyTemplate' is assigned a value but never used.", {"desc": "573", "fix": "574"}, {"desc": "575", "fix": "576"}, {"desc": "577", "fix": "578"}, {"kind": "579", "justification": "580"}, {"desc": "581", "fix": "582"}, {"kind": "579", "justification": "580"}, {"desc": "583", "fix": "584"}, {"kind": "579", "justification": "580"}, {"desc": "585", "fix": "586"}, {"desc": "587", "fix": "588"}, {"desc": "589", "fix": "590"}, {"desc": "591", "fix": "592"}, {"desc": "593", "fix": "594"}, {"desc": "595", "fix": "596"}, {"kind": "579", "justification": "580"}, {"desc": "597", "fix": "598"}, {"kind": "579", "justification": "580"}, {"desc": "587", "fix": "599"}, {"kind": "579", "justification": "580"}, {"desc": "600", "fix": "601"}, {"kind": "579", "justification": "580"}, {"desc": "602", "fix": "603"}, {"kind": "579", "justification": "580"}, {"messageId": "604", "fix": "605", "desc": "606"}, {"messageId": "607", "fix": "608", "desc": "609"}, {"desc": "610", "fix": "611"}, "Update the dependencies array to be: [globalFilter, loadQuestionnaires, rows]", {"range": "612", "text": "613"}, "Update the dependencies array to be: [id, loadQuestionnaire]", {"range": "614", "text": "615"}, "Update the dependencies array to be: [id, loadQuestionnaire, navigate]", {"range": "616", "text": "617"}, "directive", "", "Update the dependencies array to be: [hasLoaded, year, formId, authService, surveyValueChanged]", {"range": "618", "text": "619"}, "Update the dependencies array to be: [authService, user.roles]", {"range": "620", "text": "621"}, "Update the dependencies array to be: [authService]", {"range": "622", "text": "623"}, "Update the dependencies array to be: [first, loadUploads, rows]", {"range": "624", "text": "625"}, "Update the dependencies array to be: [fetchCompletedCount, visible]", {"range": "626", "text": "627"}, "Update the dependencies array to be: [loadPartnerReviewers, selectedYear]", {"range": "628", "text": "629"}, "Update the dependencies array to be: [canSendBackToPartner, handleViewPlan, handleShowPlanDetails]", {"range": "630", "text": "631"}, "Update the dependencies array to be: [globalFilter, loadFilterOptions, loadPartnerPlans, rows]", {"range": "632", "text": "633"}, "Update the dependencies array to be: [selectedYear, selectedCycle, selectedPlanStatus, selectedServiceLine, selectedSubServiceLine, assignedReviewsOnly, loadPartnerPlans, rows, globalFilter]", {"range": "634", "text": "635"}, {"range": "636", "text": "625"}, "Update the dependencies array to be: [handleBack<PERSON>o<PERSON><PERSON>, is<PERSON><PERSON><PERSON><PERSON>, saveSurveyWithCreator, selectedYear]", {"range": "637", "text": "638"}, "Update the dependencies array to be: [selectedYear, selectedCycle, selectedPartnerId, first, rows, loadPartnerReferenceData]", {"range": "639", "text": "640"}, "removeEscape", {"range": "641", "text": "580"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "642", "text": "643"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", "Update the dependencies array to be: [formId, loadAuditHistory, visible]", {"range": "644", "text": "645"}, [3256, 3258], "[globalFilter, loadQuestionnaires, rows]", [1285, 1289], "[id, loadQuestionnaire]", [2550, 2554], "[id, loadQuestionnaire, navigate]", [42627, 42652], "[hasLoaded, year, formId, authService, surveyValueChanged]", [2113, 2115], "[authService, user.roles]", [1647, 1649], "[authService]", [2585, 2598], "[first, loadUploads, rows]", [3907, 3916], "[fetchCompletedCount, visible]", [1890, 1904], "[load<PERSON><PERSON>nerRevie<PERSON><PERSON>, selectedYear]", [23784, 23813], "[canSendBackToP<PERSON>ner, handleViewPlan, handleShowPlanDetails]", [10480, 10482], "[globalFilter, loadFilterOptions, loadPartnerPlans, rows]", [10885, 11000], "[selected<PERSON><PERSON>, selectedCycle, selectedPlanStatus, selectedServiceLine, selectedSubServiceLine, assignedReviewsOnly, loadPartnerPlans, rows, globalFilter]", [2838, 2851], [6961, 6973], "[handleBack<PERSON><PERSON><PERSON><PERSON>, is<PERSON><PERSON><PERSON><PERSON>, saveSurveyWithCreator, selectedYear]", [2189, 2250], "[selectedYear, selectedCycle, selectedPartnerId, first, rows, loadPartnerReferenceData]", [247, 248], [247, 247], "\\", [1384, 1401], "[formId, loadAuditHistory, visible]"]