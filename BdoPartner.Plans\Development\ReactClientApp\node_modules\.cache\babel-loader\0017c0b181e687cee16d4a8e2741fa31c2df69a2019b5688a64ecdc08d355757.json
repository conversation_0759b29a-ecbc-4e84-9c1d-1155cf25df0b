{"ast": null, "code": "import APP_CONFIG from \"./appConfig\";\n\n/**\r\n *  Identity Server 4 endpoints config.\r\n *  Reference: https://medium.com/@franciscopa91/how-to-implement-oidc-authentication-with-react-context-api-and-react-router-205e13f2d49\r\n */\nexport const IDENTITY_META_CONFIG = {\n  issuer: APP_CONFIG.iamDomain,\n  jwks_uri: APP_CONFIG.iamDomain + \"/.well-known/openid-configuration/jwks\",\n  authorization_endpoint: APP_CONFIG.iamDomain + \"/connect/authorize\",\n  token_endpoint: APP_CONFIG.iamDomain + \"/connect/token\",\n  userinfo_endpoint: APP_CONFIG.iamDomain + \"/connect/userinfo\",\n  end_session_endpoint: APP_CONFIG.iamDomain + \"/connect/endsession\",\n  check_session_iframe: APP_CONFIG.iamDomain + \"/connect/checksession\",\n  revocation_endpoint: APP_CONFIG.iamDomain + \"/connect/revocation\",\n  introspection_endpoint: APP_CONFIG.iamDomain + \"/connect/introspect\"\n};", "map": {"version": 3, "names": ["APP_CONFIG", "IDENTITY_META_CONFIG", "issuer", "iamDomain", "jwks_uri", "authorization_endpoint", "token_endpoint", "userinfo_endpoint", "end_session_endpoint", "check_session_iframe", "revocation_endpoint", "introspection_endpoint"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/core/config/identityMetaConfig.js"], "sourcesContent": ["import APP_CONFIG from \"./appConfig\";\r\n\r\n/**\r\n *  Identity Server 4 endpoints config.\r\n *  Reference: https://medium.com/@franciscopa91/how-to-implement-oidc-authentication-with-react-context-api-and-react-router-205e13f2d49\r\n */\r\nexport const IDENTITY_META_CONFIG = {\r\n  issuer: APP_CONFIG.iamDomain,\r\n  jwks_uri: APP_CONFIG.iamDomain + \"/.well-known/openid-configuration/jwks\",\r\n  authorization_endpoint: APP_CONFIG.iamDomain + \"/connect/authorize\",\r\n  token_endpoint: APP_CONFIG.iamDomain + \"/connect/token\",\r\n  userinfo_endpoint: APP_CONFIG.iamDomain + \"/connect/userinfo\",\r\n  end_session_endpoint: APP_CONFIG.iamDomain + \"/connect/endsession\",\r\n  check_session_iframe: APP_CONFIG.iamDomain + \"/connect/checksession\",\r\n  revocation_endpoint: APP_CONFIG.iamDomain + \"/connect/revocation\",\r\n  introspection_endpoint: APP_CONFIG.iamDomain + \"/connect/introspect\",\r\n};\r\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,aAAa;;AAEpC;AACA;AACA;AACA;AACA,OAAO,MAAMC,oBAAoB,GAAG;EAClCC,MAAM,EAAEF,UAAU,CAACG,SAAS;EAC5BC,QAAQ,EAAEJ,UAAU,CAACG,SAAS,GAAG,wCAAwC;EACzEE,sBAAsB,EAAEL,UAAU,CAACG,SAAS,GAAG,oBAAoB;EACnEG,cAAc,EAAEN,UAAU,CAACG,SAAS,GAAG,gBAAgB;EACvDI,iBAAiB,EAAEP,UAAU,CAACG,SAAS,GAAG,mBAAmB;EAC7DK,oBAAoB,EAAER,UAAU,CAACG,SAAS,GAAG,qBAAqB;EAClEM,oBAAoB,EAAET,UAAU,CAACG,SAAS,GAAG,uBAAuB;EACpEO,mBAAmB,EAAEV,UAAU,CAACG,SAAS,GAAG,qBAAqB;EACjEQ,sBAAsB,EAAEX,UAAU,CAACG,SAAS,GAAG;AACjD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}