{"ast": null, "code": "import { switchMap } from './switchMap';\nimport { operate } from '../util/lift';\nexport function switchScan(accumulator, seed) {\n  return operate(function (source, subscriber) {\n    var state = seed;\n    switchMap(function (value, index) {\n      return accumulator(state, value, index);\n    }, function (_, innerValue) {\n      return state = innerValue, innerValue;\n    })(source).subscribe(subscriber);\n    return function () {\n      state = null;\n    };\n  });\n}", "map": {"version": 3, "names": ["switchMap", "operate", "switchScan", "accumulator", "seed", "source", "subscriber", "state", "value", "index", "_", "innerValue", "subscribe"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\switchScan.ts"], "sourcesContent": ["import { ObservableInput, ObservedValueOf, OperatorFunction } from '../types';\nimport { switchMap } from './switchMap';\nimport { operate } from '../util/lift';\n\n// TODO: Generate a marble diagram for these docs.\n\n/**\n * Applies an accumulator function over the source Observable where the\n * accumulator function itself returns an Observable, emitting values\n * only from the most recently returned Observable.\n *\n * <span class=\"informal\">It's like {@link mergeScan}, but only the most recent\n * Observable returned by the accumulator is merged into the outer Observable.</span>\n *\n * @see {@link scan}\n * @see {@link mergeScan}\n * @see {@link switchMap}\n *\n * @param accumulator\n * The accumulator function called on each source value.\n * @param seed The initial accumulation value.\n * @return A function that returns an observable of the accumulated values.\n */\nexport function switchScan<T, R, O extends ObservableInput<any>>(\n  accumulator: (acc: R, value: T, index: number) => O,\n  seed: R\n): OperatorFunction<T, ObservedValueOf<O>> {\n  return operate((source, subscriber) => {\n    // The state we will keep up to date to pass into our\n    // accumulator function at each new value from the source.\n    let state = seed;\n\n    // Use `switchMap` on our `source` to do the work of creating\n    // this operator. Note the backwards order here of `switchMap()(source)`\n    // to avoid needing to use `pipe` unnecessarily\n    switchMap(\n      // On each value from the source, call the accumulator with\n      // our previous state, the value and the index.\n      (value: T, index) => accumulator(state, value, index),\n      // Using the deprecated result selector here as a dirty trick\n      // to update our state with the flattened value.\n      (_, innerValue) => ((state = innerValue), innerValue)\n    )(source).subscribe(subscriber);\n\n    return () => {\n      // Release state on finalization\n      state = null!;\n    };\n  });\n}\n"], "mappings": "AACA,SAASA,SAAS,QAAQ,aAAa;AACvC,SAASC,OAAO,QAAQ,cAAc;AAqBtC,OAAM,SAAUC,UAAUA,CACxBC,WAAmD,EACnDC,IAAO;EAEP,OAAOH,OAAO,CAAC,UAACI,MAAM,EAAEC,UAAU;IAGhC,IAAIC,KAAK,GAAGH,IAAI;IAKhBJ,SAAS,CAGP,UAACQ,KAAQ,EAAEC,KAAK;MAAK,OAAAN,WAAW,CAACI,KAAK,EAAEC,KAAK,EAAEC,KAAK,CAAC;IAAhC,CAAgC,EAGrD,UAACC,CAAC,EAAEC,UAAU;MAAK,OAAEJ,KAAK,GAAGI,UAAU,EAAGA,UAAU;IAAjC,CAAkC,CACtD,CAACN,MAAM,CAAC,CAACO,SAAS,CAACN,UAAU,CAAC;IAE/B,OAAO;MAELC,KAAK,GAAG,IAAK;IACf,CAAC;EACH,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}