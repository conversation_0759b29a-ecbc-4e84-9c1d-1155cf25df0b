{"ast": null, "code": "import { isFunction } from './isFunction';\nexport function isScheduler(value) {\n  return value && isFunction(value.schedule);\n}", "map": {"version": 3, "names": ["isFunction", "isScheduler", "value", "schedule"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\util\\isScheduler.ts"], "sourcesContent": ["import { SchedulerLike } from '../types';\nimport { isFunction } from './isFunction';\n\nexport function isScheduler(value: any): value is SchedulerLike {\n  return value && isFunction(value.schedule);\n}\n"], "mappings": "AACA,SAASA,UAAU,QAAQ,cAAc;AAEzC,OAAM,SAAUC,WAAWA,CAACC,KAAU;EACpC,OAAOA,KAAK,IAAIF,UAAU,CAACE,KAAK,CAACC,QAAQ,CAAC;AAC5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}