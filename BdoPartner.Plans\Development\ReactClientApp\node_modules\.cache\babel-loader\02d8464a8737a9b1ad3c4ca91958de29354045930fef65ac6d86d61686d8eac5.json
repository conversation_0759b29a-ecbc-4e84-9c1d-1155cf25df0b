{"ast": null, "code": "import http from \"../core/http/httpClient\";\nimport APP_CONFIG from \"../core/config/appConfig\";\n\n/**\r\n * Service for Partner Annual Plans Admin operations\r\n */\nclass PartnerAnnualPlanService {\n  constructor() {\n    this.baseUrl = `${APP_CONFIG.apiDomain}/api/PartnerAnnualPlan`;\n  }\n\n  /**\r\n   * Search partner annual plans with filtering and pagination\r\n   * @param {Object} searchRequest - Search parameters\r\n   * @returns {Promise} API response with paginated results\r\n   */\n  async searchPartnerAnnualPlans(searchRequest = {}) {\n    try {\n      const response = await http.post(`${this.baseUrl}/SearchPartnerAnnualPlans`, searchRequest);\n      return response.data;\n    } catch (error) {\n      console.error(\"Error searching partner annual plans:\", error);\n      throw error;\n    }\n  }\n\n  /**\r\n   * Get partner annual plan by ID\r\n   * @param {string} id - Form ID\r\n   * @returns {Promise} Partner annual plan details\r\n   */\n  async getPartnerAnnualPlanById(id) {\n    try {\n      const response = await http.get(`${this.baseUrl}/GetPartnerAnnualPlanById?id=${id}`);\n      return response.data;\n    } catch (error) {\n      console.error(\"Error getting partner annual plan:\", error);\n      throw error;\n    }\n  }\n\n  /**\r\n   * Export partner annual plans to Excel\r\n   * @param {Object} searchRequest - Search parameters for export\r\n   * @returns {Promise} Excel file blob\r\n   */\n  async exportToExcel(searchRequest = {}) {\n    try {\n      const response = await http.post(`${this.baseUrl}/ExportToExcel`, searchRequest, {\n        responseType: \"blob\"\n      });\n      return response.data;\n    } catch (error) {\n      console.error(\"Error exporting to Excel:\", error);\n      throw error;\n    }\n  }\n\n  /**\r\n   * Export partner annual plans to CSV\r\n   * @param {Object} searchRequest - Search parameters for export\r\n   * @returns {Promise} CSV file blob\r\n   */\n  async exportToCsv(searchRequest = {}) {\n    try {\n      const response = await http.post(`${this.baseUrl}/ExportToCsv`, searchRequest, {\n        responseType: \"blob\"\n      });\n      return response.data;\n    } catch (error) {\n      console.error(\"Error exporting to CSV:\", error);\n      throw error;\n    }\n  }\n\n  /**\r\n   * Get filter options for dropdowns\r\n   * @param {number|null} selectedCycle - Selected cycle to filter status options (null for all cycles)\r\n   * @returns {Promise} Filter options (years, statuses, partner types, etc.)\r\n   */\n  async getFilterOptions(selectedCycle = null) {\n    try {\n      const params = selectedCycle !== null ? {\n        selectedCycle\n      } : {};\n      const response = await http.get(`${this.baseUrl}/GetFilterOptions`, {\n        params\n      });\n      return response.data;\n    } catch (error) {\n      console.error(\"Error getting filter options:\", error);\n      throw error;\n    }\n  }\n\n  /**\r\n   * Get admin dashboard summary statistics for all partner plans\r\n   * @returns {Promise} Dashboard summary with status counts for all partner plans grouped by year\r\n   */\n  async getAdminDashboardSummary() {\n    try {\n      const response = await http.get(`${this.baseUrl}/GetAdminDashboardSummary`);\n      return response.data;\n    } catch (error) {\n      console.error(\"Error getting admin dashboard summary:\", error);\n      throw error;\n    }\n  }\n\n  /**\r\n   * Get dashboard summary statistics for partner plans assigned to current user as reviewer\r\n   * @returns {Promise} Dashboard summary with status counts for reviewer's assigned plans grouped by year\r\n   */\n  async getReviewerDashboardSummary() {\n    try {\n      const response = await http.get(`${this.baseUrl}/GetReviewerDashboardSummary`);\n      return response.data;\n    } catch (error) {\n      console.error(\"Error getting reviewer dashboard summary:\", error);\n      throw error;\n    }\n  }\n\n  /**\r\n   * Get dashboard summary statistics for all partner plans (ELT/Admin view)\r\n   * @returns {Promise} Dashboard summary with status counts for all partner plans grouped by year\r\n   */\n  async getAllPartnerPlansDashboardSummary() {\n    try {\n      const response = await http.get(`${this.baseUrl}/GetAllPartnerPlansDashboardSummary`);\n      return response.data;\n    } catch (error) {\n      console.error(\"Error getting all partner plans dashboard summary:\", error);\n      throw error;\n    }\n  }\n\n  /**\r\n   * Helper method to download file from blob\r\n   * @param {Blob} blob - File blob\r\n   * @param {string} filename - Filename for download\r\n   */\n  downloadFile(blob, filename) {\n    const url = window.URL.createObjectURL(blob);\n    const link = document.createElement(\"a\");\n    link.href = url;\n    link.download = filename;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    window.URL.revokeObjectURL(url);\n  }\n}\nconst partnerAnnualPlanService = new PartnerAnnualPlanService();\nexport default partnerAnnualPlanService;", "map": {"version": 3, "names": ["http", "APP_CONFIG", "PartnerAnnualPlanService", "constructor", "baseUrl", "apiDomain", "searchPartnerAnnualPlans", "searchRequest", "response", "post", "data", "error", "console", "getPartnerAnnualPlanById", "id", "get", "exportToExcel", "responseType", "exportToCsv", "getFilterOptions", "selectedCycle", "params", "getAdminDashboardSummary", "getReviewerDashboardSummary", "getAllPartnerPlansDashboardSummary", "downloadFile", "blob", "filename", "url", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "partnerAnnualPlanService"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/services/partnerAnnualPlanService.js"], "sourcesContent": ["import http from \"../core/http/httpClient\";\r\nimport APP_CONFIG from \"../core/config/appConfig\";\r\n\r\n/**\r\n * Service for Partner Annual Plans Admin operations\r\n */\r\nclass PartnerAnnualPlanService {\r\n  constructor() {\r\n    this.baseUrl = `${APP_CONFIG.apiDomain}/api/PartnerAnnualPlan`;\r\n  }\r\n\r\n  /**\r\n   * Search partner annual plans with filtering and pagination\r\n   * @param {Object} searchRequest - Search parameters\r\n   * @returns {Promise} API response with paginated results\r\n   */\r\n  async searchPartnerAnnualPlans(searchRequest = {}) {\r\n    try {\r\n      const response = await http.post(`${this.baseUrl}/SearchPartnerAnnualPlans`, searchRequest);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error searching partner annual plans:\", error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get partner annual plan by ID\r\n   * @param {string} id - Form ID\r\n   * @returns {Promise} Partner annual plan details\r\n   */\r\n  async getPartnerAnnualPlanById(id) {\r\n    try {\r\n      const response = await http.get(`${this.baseUrl}/GetPartnerAnnualPlanById?id=${id}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error getting partner annual plan:\", error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Export partner annual plans to Excel\r\n   * @param {Object} searchRequest - Search parameters for export\r\n   * @returns {Promise} Excel file blob\r\n   */\r\n  async exportToExcel(searchRequest = {}) {\r\n    try {\r\n      const response = await http.post(`${this.baseUrl}/ExportToExcel`, searchRequest, {\r\n        responseType: \"blob\",\r\n      });\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error exporting to Excel:\", error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Export partner annual plans to CSV\r\n   * @param {Object} searchRequest - Search parameters for export\r\n   * @returns {Promise} CSV file blob\r\n   */\r\n  async exportToCsv(searchRequest = {}) {\r\n    try {\r\n      const response = await http.post(`${this.baseUrl}/ExportToCsv`, searchRequest, {\r\n        responseType: \"blob\",\r\n      });\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error exporting to CSV:\", error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get filter options for dropdowns\r\n   * @param {number|null} selectedCycle - Selected cycle to filter status options (null for all cycles)\r\n   * @returns {Promise} Filter options (years, statuses, partner types, etc.)\r\n   */\r\n  async getFilterOptions(selectedCycle = null) {\r\n    try {\r\n      const params = selectedCycle !== null ? { selectedCycle } : {};\r\n      const response = await http.get(`${this.baseUrl}/GetFilterOptions`, { params });\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error getting filter options:\", error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get admin dashboard summary statistics for all partner plans\r\n   * @returns {Promise} Dashboard summary with status counts for all partner plans grouped by year\r\n   */\r\n  async getAdminDashboardSummary() {\r\n    try {\r\n      const response = await http.get(`${this.baseUrl}/GetAdminDashboardSummary`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error getting admin dashboard summary:\", error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get dashboard summary statistics for partner plans assigned to current user as reviewer\r\n   * @returns {Promise} Dashboard summary with status counts for reviewer's assigned plans grouped by year\r\n   */\r\n  async getReviewerDashboardSummary() {\r\n    try {\r\n      const response = await http.get(`${this.baseUrl}/GetReviewerDashboardSummary`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error getting reviewer dashboard summary:\", error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get dashboard summary statistics for all partner plans (ELT/Admin view)\r\n   * @returns {Promise} Dashboard summary with status counts for all partner plans grouped by year\r\n   */\r\n  async getAllPartnerPlansDashboardSummary() {\r\n    try {\r\n      const response = await http.get(`${this.baseUrl}/GetAllPartnerPlansDashboardSummary`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error getting all partner plans dashboard summary:\", error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Helper method to download file from blob\r\n   * @param {Blob} blob - File blob\r\n   * @param {string} filename - Filename for download\r\n   */\r\n  downloadFile(blob, filename) {\r\n    const url = window.URL.createObjectURL(blob);\r\n    const link = document.createElement(\"a\");\r\n    link.href = url;\r\n    link.download = filename;\r\n    document.body.appendChild(link);\r\n    link.click();\r\n    document.body.removeChild(link);\r\n    window.URL.revokeObjectURL(url);\r\n  }\r\n}\r\n\r\nconst partnerAnnualPlanService = new PartnerAnnualPlanService();\r\nexport default partnerAnnualPlanService;\r\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,yBAAyB;AAC1C,OAAOC,UAAU,MAAM,0BAA0B;;AAEjD;AACA;AACA;AACA,MAAMC,wBAAwB,CAAC;EAC7BC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,OAAO,GAAG,GAAGH,UAAU,CAACI,SAAS,wBAAwB;EAChE;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAMC,wBAAwBA,CAACC,aAAa,GAAG,CAAC,CAAC,EAAE;IACjD,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMR,IAAI,CAACS,IAAI,CAAC,GAAG,IAAI,CAACL,OAAO,2BAA2B,EAAEG,aAAa,CAAC;MAC3F,OAAOC,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7D,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAME,wBAAwBA,CAACC,EAAE,EAAE;IACjC,IAAI;MACF,MAAMN,QAAQ,GAAG,MAAMR,IAAI,CAACe,GAAG,CAAC,GAAG,IAAI,CAACX,OAAO,gCAAgCU,EAAE,EAAE,CAAC;MACpF,OAAON,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAMK,aAAaA,CAACT,aAAa,GAAG,CAAC,CAAC,EAAE;IACtC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMR,IAAI,CAACS,IAAI,CAAC,GAAG,IAAI,CAACL,OAAO,gBAAgB,EAAEG,aAAa,EAAE;QAC/EU,YAAY,EAAE;MAChB,CAAC,CAAC;MACF,OAAOT,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAMO,WAAWA,CAACX,aAAa,GAAG,CAAC,CAAC,EAAE;IACpC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMR,IAAI,CAACS,IAAI,CAAC,GAAG,IAAI,CAACL,OAAO,cAAc,EAAEG,aAAa,EAAE;QAC7EU,YAAY,EAAE;MAChB,CAAC,CAAC;MACF,OAAOT,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAMQ,gBAAgBA,CAACC,aAAa,GAAG,IAAI,EAAE;IAC3C,IAAI;MACF,MAAMC,MAAM,GAAGD,aAAa,KAAK,IAAI,GAAG;QAAEA;MAAc,CAAC,GAAG,CAAC,CAAC;MAC9D,MAAMZ,QAAQ,GAAG,MAAMR,IAAI,CAACe,GAAG,CAAC,GAAG,IAAI,CAACX,OAAO,mBAAmB,EAAE;QAAEiB;MAAO,CAAC,CAAC;MAC/E,OAAOb,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;EACE,MAAMW,wBAAwBA,CAAA,EAAG;IAC/B,IAAI;MACF,MAAMd,QAAQ,GAAG,MAAMR,IAAI,CAACe,GAAG,CAAC,GAAG,IAAI,CAACX,OAAO,2BAA2B,CAAC;MAC3E,OAAOI,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAC9D,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;EACE,MAAMY,2BAA2BA,CAAA,EAAG;IAClC,IAAI;MACF,MAAMf,QAAQ,GAAG,MAAMR,IAAI,CAACe,GAAG,CAAC,GAAG,IAAI,CAACX,OAAO,8BAA8B,CAAC;MAC9E,OAAOI,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACjE,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;EACE,MAAMa,kCAAkCA,CAAA,EAAG;IACzC,IAAI;MACF,MAAMhB,QAAQ,GAAG,MAAMR,IAAI,CAACe,GAAG,CAAC,GAAG,IAAI,CAACX,OAAO,qCAAqC,CAAC;MACrF,OAAOI,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oDAAoD,EAAEA,KAAK,CAAC;MAC1E,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;EACEc,YAAYA,CAACC,IAAI,EAAEC,QAAQ,EAAE;IAC3B,MAAMC,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;IAC5C,MAAMM,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCF,IAAI,CAACG,IAAI,GAAGP,GAAG;IACfI,IAAI,CAACI,QAAQ,GAAGT,QAAQ;IACxBM,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;IAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;IACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;IAC/BH,MAAM,CAACC,GAAG,CAACW,eAAe,CAACb,GAAG,CAAC;EACjC;AACF;AAEA,MAAMc,wBAAwB,GAAG,IAAIxC,wBAAwB,CAAC,CAAC;AAC/D,eAAewC,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}