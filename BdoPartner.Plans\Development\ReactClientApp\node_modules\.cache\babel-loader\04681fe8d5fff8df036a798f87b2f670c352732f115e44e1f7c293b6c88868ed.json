{"ast": null, "code": "import { EmptyError } from './util/EmptyError';\nimport { SafeSubscriber } from './Subscriber';\nexport function firstValueFrom(source, config) {\n  var hasConfig = typeof config === 'object';\n  return new Promise(function (resolve, reject) {\n    var subscriber = new SafeSubscriber({\n      next: function (value) {\n        resolve(value);\n        subscriber.unsubscribe();\n      },\n      error: reject,\n      complete: function () {\n        if (hasConfig) {\n          resolve(config.defaultValue);\n        } else {\n          reject(new EmptyError());\n        }\n      }\n    });\n    source.subscribe(subscriber);\n  });\n}", "map": {"version": 3, "names": ["EmptyError", "SafeSubscriber", "firstValueFrom", "source", "config", "hasConfig", "Promise", "resolve", "reject", "subscriber", "next", "value", "unsubscribe", "error", "complete", "defaultValue", "subscribe"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\firstValueFrom.ts"], "sourcesContent": ["import { Observable } from './Observable';\nimport { EmptyError } from './util/EmptyError';\nimport { SafeSubscriber } from './Subscriber';\n\nexport interface FirstValueFromConfig<T> {\n  defaultValue: T;\n}\n\nexport function firstValueFrom<T, D>(source: Observable<T>, config: FirstValueFromConfig<D>): Promise<T | D>;\nexport function firstValueFrom<T>(source: Observable<T>): Promise<T>;\n\n/**\n * Converts an observable to a promise by subscribing to the observable,\n * and returning a promise that will resolve as soon as the first value\n * arrives from the observable. The subscription will then be closed.\n *\n * If the observable stream completes before any values were emitted, the\n * returned promise will reject with {@link EmptyError} or will resolve\n * with the default value if a default was specified.\n *\n * If the observable stream emits an error, the returned promise will reject\n * with that error.\n *\n * **WARNING**: Only use this with observables you *know* will emit at least one value,\n * *OR* complete. If the source observable does not emit one value or complete, you will\n * end up with a promise that is hung up, and potentially all of the state of an\n * async function hanging out in memory. To avoid this situation, look into adding\n * something like {@link timeout}, {@link take}, {@link takeWhile}, or {@link takeUntil}\n * amongst others.\n *\n * ## Example\n *\n * Wait for the first value from a stream and emit it from a promise in\n * an async function\n *\n * ```ts\n * import { interval, firstValueFrom } from 'rxjs';\n *\n * async function execute() {\n *   const source$ = interval(2000);\n *   const firstNumber = await firstValueFrom(source$);\n *   console.log(`The first number is ${ firstNumber }`);\n * }\n *\n * execute();\n *\n * // Expected output:\n * // 'The first number is 0'\n * ```\n *\n * @see {@link lastValueFrom}\n *\n * @param source the observable to convert to a promise\n * @param config a configuration object to define the `defaultValue` to use if the source completes without emitting a value\n */\nexport function firstValueFrom<T, D>(source: Observable<T>, config?: FirstValueFromConfig<D>): Promise<T | D> {\n  const hasConfig = typeof config === 'object';\n  return new Promise<T | D>((resolve, reject) => {\n    const subscriber = new SafeSubscriber<T>({\n      next: (value) => {\n        resolve(value);\n        subscriber.unsubscribe();\n      },\n      error: reject,\n      complete: () => {\n        if (hasConfig) {\n          resolve(config!.defaultValue);\n        } else {\n          reject(new EmptyError());\n        }\n      },\n    });\n    source.subscribe(subscriber);\n  });\n}\n"], "mappings": "AACA,SAASA,UAAU,QAAQ,mBAAmB;AAC9C,SAASC,cAAc,QAAQ,cAAc;AAqD7C,OAAM,SAAUC,cAAcA,CAAOC,MAAqB,EAAEC,MAAgC;EAC1F,IAAMC,SAAS,GAAG,OAAOD,MAAM,KAAK,QAAQ;EAC5C,OAAO,IAAIE,OAAO,CAAQ,UAACC,OAAO,EAAEC,MAAM;IACxC,IAAMC,UAAU,GAAG,IAAIR,cAAc,CAAI;MACvCS,IAAI,EAAE,SAAAA,CAACC,KAAK;QACVJ,OAAO,CAACI,KAAK,CAAC;QACdF,UAAU,CAACG,WAAW,EAAE;MAC1B,CAAC;MACDC,KAAK,EAAEL,MAAM;MACbM,QAAQ,EAAE,SAAAA,CAAA;QACR,IAAIT,SAAS,EAAE;UACbE,OAAO,CAACH,MAAO,CAACW,YAAY,CAAC;SAC9B,MAAM;UACLP,MAAM,CAAC,IAAIR,UAAU,EAAE,CAAC;;MAE5B;KACD,CAAC;IACFG,MAAM,CAACa,SAAS,CAACP,UAAU,CAAC;EAC9B,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}