{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nexport function sequenceEqual(compareTo, comparator) {\n  if (comparator === void 0) {\n    comparator = function (a, b) {\n      return a === b;\n    };\n  }\n  return operate(function (source, subscriber) {\n    var aState = createState();\n    var bState = createState();\n    var emit = function (isEqual) {\n      subscriber.next(isEqual);\n      subscriber.complete();\n    };\n    var createSubscriber = function (selfState, otherState) {\n      var sequenceEqualSubscriber = createOperatorSubscriber(subscriber, function (a) {\n        var buffer = otherState.buffer,\n          complete = otherState.complete;\n        if (buffer.length === 0) {\n          complete ? emit(false) : selfState.buffer.push(a);\n        } else {\n          !comparator(a, buffer.shift()) && emit(false);\n        }\n      }, function () {\n        selfState.complete = true;\n        var complete = otherState.complete,\n          buffer = otherState.buffer;\n        complete && emit(buffer.length === 0);\n        sequenceEqualSubscriber === null || sequenceEqualSubscriber === void 0 ? void 0 : sequenceEqualSubscriber.unsubscribe();\n      });\n      return sequenceEqualSubscriber;\n    };\n    source.subscribe(createSubscriber(aState, bState));\n    innerFrom(compareTo).subscribe(createSubscriber(bState, aState));\n  });\n}\nfunction createState() {\n  return {\n    buffer: [],\n    complete: false\n  };\n}", "map": {"version": 3, "names": ["operate", "createOperatorSubscriber", "innerFrom", "sequenceEqual", "compareTo", "comparator", "a", "b", "source", "subscriber", "aState", "createState", "bState", "emit", "isEqual", "next", "complete", "createSubscriber", "selfState", "otherState", "sequenceEqualSubscriber", "buffer", "length", "push", "shift", "unsubscribe", "subscribe"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\sequenceEqual.ts"], "sourcesContent": ["import { OperatorFunction, ObservableInput } from '../types';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\n\n/**\n * Compares all values of two observables in sequence using an optional comparator function\n * and returns an observable of a single boolean value representing whether or not the two sequences\n * are equal.\n *\n * <span class=\"informal\">Checks to see of all values emitted by both observables are equal, in order.</span>\n *\n * ![](sequenceEqual.png)\n *\n * `sequenceEqual` subscribes to source observable and `compareTo` `ObservableInput` (that internally\n * gets converted to an observable) and buffers incoming values from each observable. Whenever either\n * observable emits a value, the value is buffered and the buffers are shifted and compared from the bottom\n * up; If any value pair doesn't match, the returned observable will emit `false` and complete. If one of the\n * observables completes, the operator will wait for the other observable to complete; If the other\n * observable emits before completing, the returned observable will emit `false` and complete. If one observable never\n * completes or emits after the other completes, the returned observable will never complete.\n *\n * ## Example\n *\n * Figure out if the Konami code matches\n *\n * ```ts\n * import { from, fromEvent, map, bufferCount, mergeMap, sequenceEqual } from 'rxjs';\n *\n * const codes = from([\n *   'ArrowUp',\n *   'ArrowUp',\n *   'ArrowDown',\n *   'ArrowDown',\n *   'ArrowLeft',\n *   'ArrowRight',\n *   'ArrowLeft',\n *   'ArrowRight',\n *   'KeyB',\n *   'KeyA',\n *   'Enter', // no start key, clearly.\n * ]);\n *\n * const keys = fromEvent<KeyboardEvent>(document, 'keyup').pipe(map(e => e.code));\n * const matches = keys.pipe(\n *   bufferCount(11, 1),\n *   mergeMap(last11 => from(last11).pipe(sequenceEqual(codes)))\n * );\n * matches.subscribe(matched => console.log('Successful cheat at Contra? ', matched));\n * ```\n *\n * @see {@link combineLatest}\n * @see {@link zip}\n * @see {@link withLatestFrom}\n *\n * @param compareTo The `ObservableInput` sequence to compare the source sequence to.\n * @param comparator An optional function to compare each value pair.\n *\n * @return A function that returns an Observable that emits a single boolean\n * value representing whether or not the values emitted by the source\n * Observable and provided `ObservableInput` were equal in sequence.\n */\nexport function sequenceEqual<T>(\n  compareTo: ObservableInput<T>,\n  comparator: (a: T, b: T) => boolean = (a, b) => a === b\n): OperatorFunction<T, boolean> {\n  return operate((source, subscriber) => {\n    // The state for the source observable\n    const aState = createState<T>();\n    // The state for the compareTo observable;\n    const bState = createState<T>();\n\n    /** A utility to emit and complete */\n    const emit = (isEqual: boolean) => {\n      subscriber.next(isEqual);\n      subscriber.complete();\n    };\n\n    /**\n     * Creates a subscriber that subscribes to one of the sources, and compares its collected\n     * state -- `selfState` -- to the other source's collected state -- `otherState`. This\n     * is used for both streams.\n     */\n    const createSubscriber = (selfState: SequenceState<T>, otherState: SequenceState<T>) => {\n      const sequenceEqualSubscriber = createOperatorSubscriber(\n        subscriber,\n        (a: T) => {\n          const { buffer, complete } = otherState;\n          if (buffer.length === 0) {\n            // If there's no values in the other buffer\n            // and the other stream is complete, we know\n            // this isn't a match, because we got one more value.\n            // Otherwise, we push onto our buffer, so when the other\n            // stream emits, it can pull this value off our buffer and check it\n            // at the appropriate time.\n            complete ? emit(false) : selfState.buffer.push(a);\n          } else {\n            // If the other stream *does* have values in its buffer,\n            // pull the oldest one off so we can compare it to what we\n            // just got. If it wasn't a match, emit `false` and complete.\n            !comparator(a, buffer.shift()!) && emit(false);\n          }\n        },\n        () => {\n          // Or observable completed\n          selfState.complete = true;\n          const { complete, buffer } = otherState;\n          // If the other observable is also complete, and there's\n          // still stuff left in their buffer, it doesn't match, if their\n          // buffer is empty, then it does match. This is because we can't\n          // possibly get more values here anymore.\n          complete && emit(buffer.length === 0);\n          // Be sure to clean up our stream as soon as possible if we can.\n          sequenceEqualSubscriber?.unsubscribe();\n        }\n      );\n\n      return sequenceEqualSubscriber;\n    };\n\n    // Subscribe to each source.\n    source.subscribe(createSubscriber(aState, bState));\n    innerFrom(compareTo).subscribe(createSubscriber(bState, aState));\n  });\n}\n\n/**\n * A simple structure for the data used to test each sequence\n */\ninterface SequenceState<T> {\n  /** A temporary store for arrived values before they are checked */\n  buffer: T[];\n  /** Whether or not the sequence source has completed. */\n  complete: boolean;\n}\n\n/**\n * Creates a simple structure that is used to represent\n * data used to test each sequence.\n */\nfunction createState<T>(): SequenceState<T> {\n  return {\n    buffer: [],\n    complete: false,\n  };\n}\n"], "mappings": "AACA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,SAASC,SAAS,QAAQ,yBAAyB;AA2DnD,OAAM,SAAUC,aAAaA,CAC3BC,SAA6B,EAC7BC,UAAuD;EAAvD,IAAAA,UAAA;IAAAA,UAAA,YAAAA,CAAuCC,CAAC,EAAEC,CAAC;MAAK,OAAAD,CAAC,KAAKC,CAAC;IAAP,CAAO;EAAA;EAEvD,OAAOP,OAAO,CAAC,UAACQ,MAAM,EAAEC,UAAU;IAEhC,IAAMC,MAAM,GAAGC,WAAW,EAAK;IAE/B,IAAMC,MAAM,GAAGD,WAAW,EAAK;IAG/B,IAAME,IAAI,GAAG,SAAAA,CAACC,OAAgB;MAC5BL,UAAU,CAACM,IAAI,CAACD,OAAO,CAAC;MACxBL,UAAU,CAACO,QAAQ,EAAE;IACvB,CAAC;IAOD,IAAMC,gBAAgB,GAAG,SAAAA,CAACC,SAA2B,EAAEC,UAA4B;MACjF,IAAMC,uBAAuB,GAAGnB,wBAAwB,CACtDQ,UAAU,EACV,UAACH,CAAI;QACK,IAAAe,MAAM,GAAeF,UAAU,CAAAE,MAAzB;UAAEL,QAAQ,GAAKG,UAAU,CAAAH,QAAf;QACxB,IAAIK,MAAM,CAACC,MAAM,KAAK,CAAC,EAAE;UAOvBN,QAAQ,GAAGH,IAAI,CAAC,KAAK,CAAC,GAAGK,SAAS,CAACG,MAAM,CAACE,IAAI,CAACjB,CAAC,CAAC;SAClD,MAAM;UAIL,CAACD,UAAU,CAACC,CAAC,EAAEe,MAAM,CAACG,KAAK,EAAG,CAAC,IAAIX,IAAI,CAAC,KAAK,CAAC;;MAElD,CAAC,EACD;QAEEK,SAAS,CAACF,QAAQ,GAAG,IAAI;QACjB,IAAAA,QAAQ,GAAaG,UAAU,CAAAH,QAAvB;UAAEK,MAAM,GAAKF,UAAU,CAAAE,MAAf;QAKxBL,QAAQ,IAAIH,IAAI,CAACQ,MAAM,CAACC,MAAM,KAAK,CAAC,CAAC;QAErCF,uBAAuB,aAAvBA,uBAAuB,uBAAvBA,uBAAuB,CAAEK,WAAW,EAAE;MACxC,CAAC,CACF;MAED,OAAOL,uBAAuB;IAChC,CAAC;IAGDZ,MAAM,CAACkB,SAAS,CAACT,gBAAgB,CAACP,MAAM,EAAEE,MAAM,CAAC,CAAC;IAClDV,SAAS,CAACE,SAAS,CAAC,CAACsB,SAAS,CAACT,gBAAgB,CAACL,MAAM,EAAEF,MAAM,CAAC,CAAC;EAClE,CAAC,CAAC;AACJ;AAgBA,SAASC,WAAWA,CAAA;EAClB,OAAO;IACLU,MAAM,EAAE,EAAE;IACVL,QAAQ,EAAE;GACX;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}