{"ast": null, "code": "var _jsxFileName = \"C:\\\\git\\\\Partner-Site\\\\BdoPartner.Plans\\\\Development\\\\ReactClientApp\\\\src\\\\components\\\\dashboard\\\\ReviewerPartnerPlans.jsx\",\n  _s = $RefreshSig$();\nimport { Card } from 'primereact/card';\nimport { Button } from 'primereact/button';\nimport { useContext, useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { AuthContext } from '../../core/auth/components/authProvider';\nimport partnerAnnualPlanService from '../../services/partnerAnnualPlanService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ReviewerPartnerPlans = () => {\n  _s();\n  const authService = useContext(AuthContext);\n  const navigate = useNavigate();\n\n  // State for dashboard summary data - now an array of summaries by year\n  const [yearSummaries, setYearSummaries] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  // Load reviewer dashboard summary data\n  useEffect(() => {\n    const loadReviewerDashboardSummary = async () => {\n      try {\n        setLoading(true);\n        const response = await partnerAnnualPlanService.getReviewerDashboardSummary();\n        if (response.resultStatus === 1 || response.resultStatus === \"Success\") {\n          // Response.item is now a collection of summaries grouped by year\n          setYearSummaries(response.item || []);\n        } else {\n          console.error('Error loading reviewer dashboard summary:', response.message);\n          setYearSummaries([]);\n        }\n      } catch (error) {\n        console.error('Error loading reviewer dashboard summary:', error);\n        setYearSummaries([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    // Only load data if user is authenticated\n    if (authService !== null && authService !== void 0 && authService.isAuthenticated()) {\n      loadReviewerDashboardSummary();\n    } else {\n      setLoading(false);\n    }\n  }, []); // Empty dependency array to run only once on mount\n\n  // Handle View Plans button click for My Reviews section\n  const handleMyReviewsViewClick = year => {\n    // Navigate to reviewer plans page with specified year\n    navigate(`/reviewer/partner-annual-plans?year=${year}`);\n  };\n\n  // Helper function to get status color class\n  const getStatusColorClass = statusType => {\n    switch (statusType) {\n      case 'pending':\n        return 'status-pending';\n      // red\n      case 'ready-planning':\n        return 'status-ready-planning';\n      // orange\n      case 'ready-midyear':\n        return 'status-ready-midyear';\n      // yellow\n      case 'ready-yearend':\n        return 'status-ready-yearend';\n      // yellow\n      case 'completed':\n        return 'status-completed';\n      // green\n      case 'exempt':\n        return 'status-exempt';\n      // black\n      default:\n        return '';\n    }\n  };\n\n  // If no reviewer assignments found, hide the section\n  if (!loading && (!yearSummaries || yearSummaries.length === 0)) {\n    return null; // Section should be hidden\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"reviewer-partner-plans\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"section-title\",\n      children: \"My Reviews\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"section-description\",\n      children: \"Access partner plans for which you are responsible for reviewing enabling you to prioritize and manage your review responsibilities efficiently.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 7\n    }, this), loading ? /*#__PURE__*/_jsxDEV(Card, {\n      className: \"reviews-card\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"pi pi-spinner pi-spin\",\n          style: {\n            fontSize: '2rem'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Loading review data...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 9\n    }, this) : yearSummaries.map((summary, index) => /*#__PURE__*/_jsxDEV(Card, {\n      className: \"reviews-card\",\n      style: {\n        marginBottom: index < yearSummaries.length - 1 ? '2rem' : '0'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"reviews-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"plan-info-grid\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"plan-info-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"plan-label\",\n              children: \"Year\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"plan-value\",\n              children: summary.year\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"plan-info-item cycles-column\",\n            children: [summary.isPlanningCycleEnabled && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"cycle-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"cycle-label\",\n                children: \"PLANNING:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 100,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"status-text\",\n                children: \"Pending Submisson :\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 101,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `status-count ${getStatusColorClass('pending')}`,\n                children: summary.planningNotStarted + summary.planningDraft + summary.planningReopened || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 102,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"status-text\",\n                children: \"Ready for Review :\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 105,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `status-count ${getStatusColorClass('ready-planning')}`,\n                children: summary.planningUnderReview || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 106,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"status-text\",\n                children: \"Completed:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 109,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `status-count ${getStatusColorClass('completed')}`,\n                children: summary.planningCompleted || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"status-text\",\n                children: \"Exempt: \"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 113,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `status-count ${getStatusColorClass('exempt')}`,\n                children: summary.exemptPartners || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 114,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 21\n            }, this), summary.isMidYearCycleEnabled && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"cycle-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"cycle-label\",\n                children: \"MID YEAR:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 123,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"status-text\",\n                children: \"Pending Submisson :\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 124,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `status-count ${getStatusColorClass('pending')}`,\n                children: summary.midYearReviewNotStarted + summary.midYearReviewDraft + summary.midYearReviewReopened || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 125,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"status-text\",\n                children: \"Ready for Review :\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 128,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `status-count ${getStatusColorClass('ready-midyear')}`,\n                children: summary.midYearReviewUnderReview || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"status-text\",\n                children: \"Completed:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `status-count ${getStatusColorClass('completed')}`,\n                children: summary.midYearReviewCompleted || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 21\n            }, this), summary.isYearEndCycleEnabled && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"cycle-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"cycle-label\",\n                children: \"YEAR END:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 142,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"status-text\",\n                children: \"Pending Submisson :\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `status-count ${getStatusColorClass('pending')}`,\n                children: summary.yearEndReviewNotStarted + summary.yearEndReviewDraft + summary.yearEndReviewReopened || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"status-text\",\n                children: \"Ready for Review :\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `status-count ${getStatusColorClass('ready-yearend')}`,\n                children: summary.yearEndReviewUnderReview || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 148,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"status-text\",\n                children: \"Completed:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `status-count ${getStatusColorClass('completed')}`,\n                children: summary.yearEndReviewCompleted || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"plan-actions\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            label: \"View Plans\",\n            className: \"p-button-primary-rounded\",\n            icon: \"pi pi-eye\",\n            rounded: true,\n            onClick: () => handleMyReviewsViewClick(summary.year)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 13\n      }, this)\n    }, `${summary.year}-${summary.questionnaireName}`, false, {\n      fileName: _jsxFileName,\n      lineNumber: 86,\n      columnNumber: 11\n    }, this))]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 71,\n    columnNumber: 5\n  }, this);\n};\n_s(ReviewerPartnerPlans, \"S5aJukfvCqPrcWe4emUsjBOOtOs=\", false, function () {\n  return [useNavigate];\n});\n_c = ReviewerPartnerPlans;\nexport default ReviewerPartnerPlans;\nvar _c;\n$RefreshReg$(_c, \"ReviewerPartnerPlans\");", "map": {"version": 3, "names": ["Card", "<PERSON><PERSON>", "useContext", "useState", "useEffect", "useNavigate", "AuthContext", "partnerAnnualPlanService", "jsxDEV", "_jsxDEV", "ReviewerPartnerPlans", "_s", "authService", "navigate", "yearSummaries", "setYearSummaries", "loading", "setLoading", "loadReviewerDashboardSummary", "response", "getReviewerDashboardSummary", "resultStatus", "item", "console", "error", "message", "isAuthenticated", "handleMyReviewsViewClick", "year", "getStatusColorClass", "statusType", "length", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "fontSize", "map", "summary", "index", "marginBottom", "isPlanningCycleEnabled", "planningNotStarted", "planningDraft", "planningReopened", "planningUnderReview", "planningCompleted", "exemptPartners", "isMidYearCycleEnabled", "midYearReviewNotStarted", "midYearReviewDraft", "midYearReviewReopened", "midYearReviewUnderReview", "midYearReviewCompleted", "isYearEndCycleEnabled", "yearEndReviewNotStarted", "yearEndReviewDraft", "yearEndReviewReopened", "yearEndReviewUnderReview", "yearEndReviewCompleted", "label", "icon", "rounded", "onClick", "questionnaire<PERSON>ame", "_c", "$RefreshReg$"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/components/dashboard/ReviewerPartnerPlans.jsx"], "sourcesContent": ["import { Card } from 'primereact/card';\r\nimport { Button } from 'primereact/button';\r\nimport { useContext, useState, useEffect } from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport { AuthContext } from '../../core/auth/components/authProvider';\r\nimport partnerAnnualPlanService from '../../services/partnerAnnualPlanService';\r\n\r\nconst ReviewerPartnerPlans = () => {\r\n  const authService = useContext(AuthContext);\r\n  const navigate = useNavigate();\r\n\r\n  // State for dashboard summary data - now an array of summaries by year\r\n  const [yearSummaries, setYearSummaries] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n\r\n  // Load reviewer dashboard summary data\r\n  useEffect(() => {\r\n    const loadReviewerDashboardSummary = async () => {\r\n      try {\r\n        setLoading(true);\r\n        const response = await partnerAnnualPlanService.getReviewerDashboardSummary();\r\n\r\n        if (response.resultStatus === 1 || response.resultStatus === \"Success\") {\r\n          // Response.item is now a collection of summaries grouped by year\r\n          setYearSummaries(response.item || []);\r\n        } else {\r\n          console.error('Error loading reviewer dashboard summary:', response.message);\r\n          setYearSummaries([]);\r\n        }\r\n      } catch (error) {\r\n        console.error('Error loading reviewer dashboard summary:', error);\r\n        setYearSummaries([]);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    // Only load data if user is authenticated\r\n    if (authService?.isAuthenticated()) {\r\n      loadReviewerDashboardSummary();\r\n    } else {\r\n      setLoading(false);\r\n    }\r\n  }, []); // Empty dependency array to run only once on mount\r\n\r\n  // Handle View Plans button click for My Reviews section\r\n  const handleMyReviewsViewClick = (year) => {\r\n    // Navigate to reviewer plans page with specified year\r\n    navigate(`/reviewer/partner-annual-plans?year=${year}`);\r\n  };\r\n\r\n  // Helper function to get status color class\r\n  const getStatusColorClass = (statusType) => {\r\n    switch (statusType) {\r\n      case 'pending': return 'status-pending'; // red\r\n      case 'ready-planning': return 'status-ready-planning'; // orange\r\n      case 'ready-midyear': return 'status-ready-midyear'; // yellow\r\n      case 'ready-yearend': return 'status-ready-yearend'; // yellow\r\n      case 'completed': return 'status-completed'; // green\r\n      case 'exempt': return 'status-exempt'; // black\r\n      default: return '';\r\n    }\r\n  };\r\n\r\n  // If no reviewer assignments found, hide the section\r\n  if (!loading && (!yearSummaries || yearSummaries.length === 0)) {\r\n    return null; // Section should be hidden\r\n  }\r\n\r\n  return (\r\n    <div className=\"reviewer-partner-plans\">\r\n      <h2 className=\"section-title\">My Reviews</h2>\r\n      <p className=\"section-description\">\r\n        Access partner plans for which you are responsible for reviewing enabling you to prioritize and manage your review responsibilities efficiently.\r\n      </p>\r\n\r\n      {loading ? (\r\n        <Card className=\"reviews-card\">\r\n          <div className=\"loading-content\">\r\n            <i className=\"pi pi-spinner pi-spin\" style={{ fontSize: '2rem' }}></i>\r\n            <p>Loading review data...</p>\r\n          </div>\r\n        </Card>\r\n      ) : (\r\n        yearSummaries.map((summary, index) => (\r\n          <Card key={`${summary.year}-${summary.questionnaireName}`} className=\"reviews-card\" style={{ marginBottom: index < yearSummaries.length - 1 ? '2rem' : '0' }}>\r\n            <div className=\"reviews-content\">\r\n              <div className=\"plan-info-grid\">\r\n                {/* Year Column */}\r\n                <div className=\"plan-info-item\">\r\n                  <label className=\"plan-label\">Year</label>\r\n                  <span className=\"plan-value\">{summary.year}</span>\r\n                </div>\r\n\r\n                {/* Cycles Column - All cycles stacked vertically */}\r\n                <div className=\"plan-info-item cycles-column\">\r\n                  {/* Planning Cycle Row */}\r\n                  {summary.isPlanningCycleEnabled && (\r\n                    <div className=\"cycle-row\">\r\n                      <span className=\"cycle-label\">PLANNING:</span>\r\n                      <span className=\"status-text\">Pending Submisson :</span>\r\n                      <span className={`status-count ${getStatusColorClass('pending')}`}>\r\n                        {(summary.planningNotStarted + summary.planningDraft + summary.planningReopened) || 0}\r\n                      </span>\r\n                      <span className=\"status-text\">Ready for Review :</span>\r\n                      <span className={`status-count ${getStatusColorClass('ready-planning')}`}>\r\n                        {summary.planningUnderReview || 0}\r\n                      </span>\r\n                      <span className=\"status-text\">Completed:</span>\r\n                      <span className={`status-count ${getStatusColorClass('completed')}`}>\r\n                        {summary.planningCompleted || 0}\r\n                      </span>\r\n                      <span className=\"status-text\">Exempt: </span>\r\n                      <span className={`status-count ${getStatusColorClass('exempt')}`}>\r\n                        {summary.exemptPartners || 0}\r\n                      </span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {/* Mid Year Cycle Row */}\r\n                  {summary.isMidYearCycleEnabled && (\r\n                    <div className=\"cycle-row\">\r\n                      <span className=\"cycle-label\">MID YEAR:</span>\r\n                      <span className=\"status-text\">Pending Submisson :</span>\r\n                      <span className={`status-count ${getStatusColorClass('pending')}`}>\r\n                        {(summary.midYearReviewNotStarted + summary.midYearReviewDraft + summary.midYearReviewReopened) || 0}\r\n                      </span>\r\n                      <span className=\"status-text\">Ready for Review :</span>\r\n                      <span className={`status-count ${getStatusColorClass('ready-midyear')}`}>\r\n                        {summary.midYearReviewUnderReview || 0}\r\n                      </span>\r\n                      <span className=\"status-text\">Completed:</span>\r\n                      <span className={`status-count ${getStatusColorClass('completed')}`}>\r\n                        {summary.midYearReviewCompleted || 0}\r\n                      </span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {/* Year End Cycle Row */}\r\n                  {summary.isYearEndCycleEnabled && (\r\n                    <div className=\"cycle-row\">\r\n                      <span className=\"cycle-label\">YEAR END:</span>\r\n                      <span className=\"status-text\">Pending Submisson :</span>\r\n                      <span className={`status-count ${getStatusColorClass('pending')}`}>\r\n                        {(summary.yearEndReviewNotStarted + summary.yearEndReviewDraft + summary.yearEndReviewReopened) || 0}\r\n                      </span>\r\n                      <span className=\"status-text\">Ready for Review :</span>\r\n                      <span className={`status-count ${getStatusColorClass('ready-yearend')}`}>\r\n                        {summary.yearEndReviewUnderReview || 0}\r\n                      </span>\r\n                      <span className=\"status-text\">Completed:</span>\r\n                      <span className={`status-count ${getStatusColorClass('completed')}`}>\r\n                        {summary.yearEndReviewCompleted || 0}\r\n                      </span>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n\r\n              \r\n              </div>\r\n\r\n              <div className=\"plan-actions\">\r\n                <Button\r\n                  label=\"View Plans\"\r\n                  className=\"p-button-primary-rounded\"\r\n                  icon=\"pi pi-eye\"\r\n                  rounded\r\n                  onClick={() => handleMyReviewsViewClick(summary.year)}\r\n                />\r\n              </div>\r\n            </div>\r\n          </Card>\r\n        ))\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ReviewerPartnerPlans;\r\n"], "mappings": ";;AAAA,SAASA,IAAI,QAAQ,iBAAiB;AACtC,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AACvD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,QAAQ,yCAAyC;AACrE,OAAOC,wBAAwB,MAAM,yCAAyC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/E,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAMC,WAAW,GAAGV,UAAU,CAACI,WAAW,CAAC;EAC3C,MAAMO,QAAQ,GAAGR,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM,CAACS,aAAa,EAAEC,gBAAgB,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACAC,SAAS,CAAC,MAAM;IACd,MAAMc,4BAA4B,GAAG,MAAAA,CAAA,KAAY;MAC/C,IAAI;QACFD,UAAU,CAAC,IAAI,CAAC;QAChB,MAAME,QAAQ,GAAG,MAAMZ,wBAAwB,CAACa,2BAA2B,CAAC,CAAC;QAE7E,IAAID,QAAQ,CAACE,YAAY,KAAK,CAAC,IAAIF,QAAQ,CAACE,YAAY,KAAK,SAAS,EAAE;UACtE;UACAN,gBAAgB,CAACI,QAAQ,CAACG,IAAI,IAAI,EAAE,CAAC;QACvC,CAAC,MAAM;UACLC,OAAO,CAACC,KAAK,CAAC,2CAA2C,EAAEL,QAAQ,CAACM,OAAO,CAAC;UAC5EV,gBAAgB,CAAC,EAAE,CAAC;QACtB;MACF,CAAC,CAAC,OAAOS,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;QACjET,gBAAgB,CAAC,EAAE,CAAC;MACtB,CAAC,SAAS;QACRE,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;;IAED;IACA,IAAIL,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEc,eAAe,CAAC,CAAC,EAAE;MAClCR,4BAA4B,CAAC,CAAC;IAChC,CAAC,MAAM;MACLD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER;EACA,MAAMU,wBAAwB,GAAIC,IAAI,IAAK;IACzC;IACAf,QAAQ,CAAC,uCAAuCe,IAAI,EAAE,CAAC;EACzD,CAAC;;EAED;EACA,MAAMC,mBAAmB,GAAIC,UAAU,IAAK;IAC1C,QAAQA,UAAU;MAChB,KAAK,SAAS;QAAE,OAAO,gBAAgB;MAAE;MACzC,KAAK,gBAAgB;QAAE,OAAO,uBAAuB;MAAE;MACvD,KAAK,eAAe;QAAE,OAAO,sBAAsB;MAAE;MACrD,KAAK,eAAe;QAAE,OAAO,sBAAsB;MAAE;MACrD,KAAK,WAAW;QAAE,OAAO,kBAAkB;MAAE;MAC7C,KAAK,QAAQ;QAAE,OAAO,eAAe;MAAE;MACvC;QAAS,OAAO,EAAE;IACpB;EACF,CAAC;;EAED;EACA,IAAI,CAACd,OAAO,KAAK,CAACF,aAAa,IAAIA,aAAa,CAACiB,MAAM,KAAK,CAAC,CAAC,EAAE;IAC9D,OAAO,IAAI,CAAC,CAAC;EACf;EAEA,oBACEtB,OAAA;IAAKuB,SAAS,EAAC,wBAAwB;IAAAC,QAAA,gBACrCxB,OAAA;MAAIuB,SAAS,EAAC,eAAe;MAAAC,QAAA,EAAC;IAAU;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC7C5B,OAAA;MAAGuB,SAAS,EAAC,qBAAqB;MAAAC,QAAA,EAAC;IAEnC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,EAEHrB,OAAO,gBACNP,OAAA,CAACT,IAAI;MAACgC,SAAS,EAAC,cAAc;MAAAC,QAAA,eAC5BxB,OAAA;QAAKuB,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BxB,OAAA;UAAGuB,SAAS,EAAC,uBAAuB;UAACM,KAAK,EAAE;YAAEC,QAAQ,EAAE;UAAO;QAAE;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtE5B,OAAA;UAAAwB,QAAA,EAAG;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,GAEPvB,aAAa,CAAC0B,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBAC/BjC,OAAA,CAACT,IAAI;MAAsDgC,SAAS,EAAC,cAAc;MAACM,KAAK,EAAE;QAAEK,YAAY,EAAED,KAAK,GAAG5B,aAAa,CAACiB,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG;MAAI,CAAE;MAAAE,QAAA,eAC3JxB,OAAA;QAAKuB,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BxB,OAAA;UAAKuB,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAE7BxB,OAAA;YAAKuB,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BxB,OAAA;cAAOuB,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1C5B,OAAA;cAAMuB,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAEQ,OAAO,CAACb;YAAI;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,eAGN5B,OAAA;YAAKuB,SAAS,EAAC,8BAA8B;YAAAC,QAAA,GAE1CQ,OAAO,CAACG,sBAAsB,iBAC7BnC,OAAA;cAAKuB,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBxB,OAAA;gBAAMuB,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC9C5B,OAAA;gBAAMuB,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxD5B,OAAA;gBAAMuB,SAAS,EAAE,gBAAgBH,mBAAmB,CAAC,SAAS,CAAC,EAAG;gBAAAI,QAAA,EAC9DQ,OAAO,CAACI,kBAAkB,GAAGJ,OAAO,CAACK,aAAa,GAAGL,OAAO,CAACM,gBAAgB,IAAK;cAAC;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjF,CAAC,eACP5B,OAAA;gBAAMuB,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvD5B,OAAA;gBAAMuB,SAAS,EAAE,gBAAgBH,mBAAmB,CAAC,gBAAgB,CAAC,EAAG;gBAAAI,QAAA,EACtEQ,OAAO,CAACO,mBAAmB,IAAI;cAAC;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC,eACP5B,OAAA;gBAAMuB,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/C5B,OAAA;gBAAMuB,SAAS,EAAE,gBAAgBH,mBAAmB,CAAC,WAAW,CAAC,EAAG;gBAAAI,QAAA,EACjEQ,OAAO,CAACQ,iBAAiB,IAAI;cAAC;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC,eACP5B,OAAA;gBAAMuB,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC7C5B,OAAA;gBAAMuB,SAAS,EAAE,gBAAgBH,mBAAmB,CAAC,QAAQ,CAAC,EAAG;gBAAAI,QAAA,EAC9DQ,OAAO,CAACS,cAAc,IAAI;cAAC;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACN,EAGAI,OAAO,CAACU,qBAAqB,iBAC5B1C,OAAA;cAAKuB,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBxB,OAAA;gBAAMuB,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC9C5B,OAAA;gBAAMuB,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxD5B,OAAA;gBAAMuB,SAAS,EAAE,gBAAgBH,mBAAmB,CAAC,SAAS,CAAC,EAAG;gBAAAI,QAAA,EAC9DQ,OAAO,CAACW,uBAAuB,GAAGX,OAAO,CAACY,kBAAkB,GAAGZ,OAAO,CAACa,qBAAqB,IAAK;cAAC;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChG,CAAC,eACP5B,OAAA;gBAAMuB,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvD5B,OAAA;gBAAMuB,SAAS,EAAE,gBAAgBH,mBAAmB,CAAC,eAAe,CAAC,EAAG;gBAAAI,QAAA,EACrEQ,OAAO,CAACc,wBAAwB,IAAI;cAAC;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC,eACP5B,OAAA;gBAAMuB,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/C5B,OAAA;gBAAMuB,SAAS,EAAE,gBAAgBH,mBAAmB,CAAC,WAAW,CAAC,EAAG;gBAAAI,QAAA,EACjEQ,OAAO,CAACe,sBAAsB,IAAI;cAAC;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACN,EAGAI,OAAO,CAACgB,qBAAqB,iBAC5BhD,OAAA;cAAKuB,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBxB,OAAA;gBAAMuB,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC9C5B,OAAA;gBAAMuB,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxD5B,OAAA;gBAAMuB,SAAS,EAAE,gBAAgBH,mBAAmB,CAAC,SAAS,CAAC,EAAG;gBAAAI,QAAA,EAC9DQ,OAAO,CAACiB,uBAAuB,GAAGjB,OAAO,CAACkB,kBAAkB,GAAGlB,OAAO,CAACmB,qBAAqB,IAAK;cAAC;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChG,CAAC,eACP5B,OAAA;gBAAMuB,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvD5B,OAAA;gBAAMuB,SAAS,EAAE,gBAAgBH,mBAAmB,CAAC,eAAe,CAAC,EAAG;gBAAAI,QAAA,EACrEQ,OAAO,CAACoB,wBAAwB,IAAI;cAAC;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC,eACP5B,OAAA;gBAAMuB,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/C5B,OAAA;gBAAMuB,SAAS,EAAE,gBAAgBH,mBAAmB,CAAC,WAAW,CAAC,EAAG;gBAAAI,QAAA,EACjEQ,OAAO,CAACqB,sBAAsB,IAAI;cAAC;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGH,CAAC,eAEN5B,OAAA;UAAKuB,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3BxB,OAAA,CAACR,MAAM;YACL8D,KAAK,EAAC,YAAY;YAClB/B,SAAS,EAAC,0BAA0B;YACpCgC,IAAI,EAAC,WAAW;YAChBC,OAAO;YACPC,OAAO,EAAEA,CAAA,KAAMvC,wBAAwB,CAACc,OAAO,CAACb,IAAI;UAAE;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC,GArFG,GAAGI,OAAO,CAACb,IAAI,IAAIa,OAAO,CAAC0B,iBAAiB,EAAE;MAAAjC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAsFnD,CACP,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC1B,EAAA,CAzKID,oBAAoB;EAAA,QAEPL,WAAW;AAAA;AAAA+D,EAAA,GAFxB1D,oBAAoB;AA2K1B,eAAeA,oBAAoB;AAAC,IAAA0D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}