{"ast": null, "code": "import { AsapAction } from './AsapAction';\nimport { AsapScheduler } from './AsapScheduler';\nexport var asapScheduler = new AsapScheduler(AsapAction);\nexport var asap = asapScheduler;", "map": {"version": 3, "names": ["AsapAction", "AsapScheduler", "asapScheduler", "asap"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\scheduler\\asap.ts"], "sourcesContent": ["import { AsapAction } from './AsapAction';\nimport { AsapScheduler } from './AsapScheduler';\n\n/**\n *\n * Asap Scheduler\n *\n * <span class=\"informal\">Perform task as fast as it can be performed asynchronously</span>\n *\n * `asap` scheduler behaves the same as {@link asyncScheduler} scheduler when you use it to delay task\n * in time. If however you set delay to `0`, `asap` will wait for current synchronously executing\n * code to end and then it will try to execute given task as fast as possible.\n *\n * `asap` scheduler will do its best to minimize time between end of currently executing code\n * and start of scheduled task. This makes it best candidate for performing so called \"deferring\".\n * Traditionally this was achieved by calling `setTimeout(deferredTask, 0)`, but that technique involves\n * some (although minimal) unwanted delay.\n *\n * Note that using `asap` scheduler does not necessarily mean that your task will be first to process\n * after currently executing code. In particular, if some task was also scheduled with `asap` before,\n * that task will execute first. That being said, if you need to schedule task asynchronously, but\n * as soon as possible, `asap` scheduler is your best bet.\n *\n * ## Example\n * Compare async and asap scheduler<\n * ```ts\n * import { asapScheduler, asyncScheduler } from 'rxjs';\n *\n * asyncScheduler.schedule(() => console.log('async')); // scheduling 'async' first...\n * asapScheduler.schedule(() => console.log('asap'));\n *\n * // Logs:\n * // \"asap\"\n * // \"async\"\n * // ... but 'asap' goes first!\n * ```\n */\n\nexport const asapScheduler = new AsapScheduler(AsapAction);\n\n/**\n * @deprecated Renamed to {@link asapScheduler}. Will be removed in v8.\n */\nexport const asap = asapScheduler;\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,cAAc;AACzC,SAASC,aAAa,QAAQ,iBAAiB;AAqC/C,OAAO,IAAMC,aAAa,GAAG,IAAID,aAAa,CAACD,UAAU,CAAC;AAK1D,OAAO,IAAMG,IAAI,GAAGD,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}