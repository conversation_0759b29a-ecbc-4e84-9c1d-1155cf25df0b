{"ast": null, "code": "import { Observable } from '../Observable';\nimport { innerFrom } from './innerFrom';\nimport { EMPTY } from './empty';\nexport function using(resourceFactory, observableFactory) {\n  return new Observable(function (subscriber) {\n    var resource = resourceFactory();\n    var result = observableFactory(resource);\n    var source = result ? innerFrom(result) : EMPTY;\n    source.subscribe(subscriber);\n    return function () {\n      if (resource) {\n        resource.unsubscribe();\n      }\n    };\n  });\n}", "map": {"version": 3, "names": ["Observable", "innerFrom", "EMPTY", "using", "resourceFactory", "observableFactory", "subscriber", "resource", "result", "source", "subscribe", "unsubscribe"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\observable\\using.ts"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { Unsubscribable, ObservableInput, ObservedValueOf } from '../types';\nimport { innerFrom } from './innerFrom';\nimport { EMPTY } from './empty';\n\n/**\n * Creates an Observable that uses a resource which will be disposed at the same time as the Observable.\n *\n * <span class=\"informal\">Use it when you catch yourself cleaning up after an Observable.</span>\n *\n * `using` is a factory operator, which accepts two functions. First function returns a disposable resource.\n * It can be an arbitrary object that implements `unsubscribe` method. Second function will be injected with\n * that object and should return an Observable. That Observable can use resource object during its execution.\n * Both functions passed to `using` will be called every time someone subscribes - neither an Observable nor\n * resource object will be shared in any way between subscriptions.\n *\n * When Observable returned by `using` is subscribed, Observable returned from the second function will be subscribed\n * as well. All its notifications (nexted values, completion and error events) will be emitted unchanged by the output\n * Observable. If however someone unsubscribes from the Observable or source Observable completes or errors by itself,\n * the `unsubscribe` method on resource object will be called. This can be used to do any necessary clean up, which\n * otherwise would have to be handled by hand. Note that complete or error notifications are not emitted when someone\n * cancels subscription to an Observable via `unsubscribe`, so `using` can be used as a hook, allowing you to make\n * sure that all resources which need to exist during an Observable execution will be disposed at appropriate time.\n *\n * @see {@link defer}\n *\n * @param resourceFactory A function which creates any resource object that implements `unsubscribe` method.\n * @param observableFactory A function which creates an Observable, that can use injected resource object.\n * @return An Observable that behaves the same as Observable returned by `observableFactory`, but\n * which - when completed, errored or unsubscribed - will also call `unsubscribe` on created resource object.\n */\nexport function using<T extends ObservableInput<any>>(\n  resourceFactory: () => Unsubscribable | void,\n  observableFactory: (resource: Unsubscribable | void) => T | void\n): Observable<ObservedValueOf<T>> {\n  return new Observable<ObservedValueOf<T>>((subscriber) => {\n    const resource = resourceFactory();\n    const result = observableFactory(resource);\n    const source = result ? innerFrom(result) : EMPTY;\n    source.subscribe(subscriber);\n    return () => {\n      // NOTE: Optional chaining did not work here.\n      // Related TS Issue: https://github.com/microsoft/TypeScript/issues/40818\n      if (resource) {\n        resource.unsubscribe();\n      }\n    };\n  });\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAE1C,SAASC,SAAS,QAAQ,aAAa;AACvC,SAASC,KAAK,QAAQ,SAAS;AA4B/B,OAAM,SAAUC,KAAKA,CACnBC,eAA4C,EAC5CC,iBAAgE;EAEhE,OAAO,IAAIL,UAAU,CAAqB,UAACM,UAAU;IACnD,IAAMC,QAAQ,GAAGH,eAAe,EAAE;IAClC,IAAMI,MAAM,GAAGH,iBAAiB,CAACE,QAAQ,CAAC;IAC1C,IAAME,MAAM,GAAGD,MAAM,GAAGP,SAAS,CAACO,MAAM,CAAC,GAAGN,KAAK;IACjDO,MAAM,CAACC,SAAS,CAACJ,UAAU,CAAC;IAC5B,OAAO;MAGL,IAAIC,QAAQ,EAAE;QACZA,QAAQ,CAACI,WAAW,EAAE;;IAE1B,CAAC;EACH,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}