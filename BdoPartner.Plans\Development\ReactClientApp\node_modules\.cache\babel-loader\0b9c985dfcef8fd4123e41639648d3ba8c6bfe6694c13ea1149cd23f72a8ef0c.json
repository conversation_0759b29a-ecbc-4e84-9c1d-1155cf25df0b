{"ast": null, "code": "export function createObject(keys, values) {\n  return keys.reduce(function (result, key, i) {\n    return result[key] = values[i], result;\n  }, {});\n}", "map": {"version": 3, "names": ["createObject", "keys", "values", "reduce", "result", "key", "i"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\util\\createObject.ts"], "sourcesContent": ["export function createObject(keys: string[], values: any[]) {\n  return keys.reduce((result, key, i) => ((result[key] = values[i]), result), {} as any);\n}\n"], "mappings": "AAAA,OAAM,SAAUA,YAAYA,CAACC,IAAc,EAAEC,MAAa;EACxD,OAAOD,IAAI,CAACE,MAAM,CAAC,UAACC,MAAM,EAAEC,GAAG,EAAEC,CAAC;IAAK,OAAEF,MAAM,CAACC,GAAG,CAAC,GAAGH,MAAM,CAACI,CAAC,CAAC,EAAGF,MAAM;EAAlC,CAAmC,EAAE,EAAS,CAAC;AACxF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}