{"ast": null, "code": "import { ReplaySubject } from '../ReplaySubject';\nimport { multicast } from './multicast';\nimport { isFunction } from '../util/isFunction';\nexport function publishReplay(bufferSize, windowTime, selectorOrScheduler, timestampProvider) {\n  if (selectorOrScheduler && !isFunction(selectorOrScheduler)) {\n    timestampProvider = selectorOrScheduler;\n  }\n  var selector = isFunction(selectorOrScheduler) ? selectorOrScheduler : undefined;\n  return function (source) {\n    return multicast(new ReplaySubject(bufferSize, windowTime, timestampProvider), selector)(source);\n  };\n}", "map": {"version": 3, "names": ["ReplaySubject", "multicast", "isFunction", "publishReplay", "bufferSize", "windowTime", "selectorOrScheduler", "timestampProvider", "selector", "undefined", "source"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\publishReplay.ts"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { ReplaySubject } from '../ReplaySubject';\nimport { multicast } from './multicast';\nimport { MonoTypeOperatorFunction, OperatorFunction, TimestampProvider, ObservableInput, ObservedValueOf } from '../types';\nimport { isFunction } from '../util/isFunction';\n\n/**\n * Creates a {@link ConnectableObservable} that uses a {@link ReplaySubject}\n * internally.\n *\n * @param bufferSize The buffer size for the underlying {@link ReplaySubject}.\n * @param windowTime The window time for the underlying {@link ReplaySubject}.\n * @param timestampProvider The timestamp provider for the underlying {@link ReplaySubject}.\n * @deprecated Will be removed in v8. To create a connectable observable that uses a\n * {@link ReplaySubject} under the hood, use {@link connectable}.\n * `source.pipe(publishReplay(size, time, scheduler))` is equivalent to\n * `connectable(source, { connector: () => new ReplaySubject(size, time, scheduler), resetOnDisconnect: false })`.\n * If you're using {@link refCount} after `publishReplay`, use the {@link share} operator instead.\n * `publishReplay(size, time, scheduler), refCount()` is equivalent to\n * `share({ connector: () => new ReplaySubject(size, time, scheduler), resetOnError: false, resetOnComplete: false, resetOnRefCountZero: false })`.\n * Details: https://rxjs.dev/deprecations/multicasting\n */\nexport function publishReplay<T>(\n  bufferSize?: number,\n  windowTime?: number,\n  timestampProvider?: TimestampProvider\n): MonoTypeOperatorFunction<T>;\n\n/**\n * Creates an observable, that when subscribed to, will create a {@link ReplaySubject},\n * and pass an observable from it (using [asObservable](api/index/class/Subject#asObservable)) to\n * the `selector` function, which then returns an observable that is subscribed to before\n * \"connecting\" the source to the internal `ReplaySubject`.\n *\n * Since this is deprecated, for additional details see the documentation for {@link connect}.\n *\n * @param bufferSize The buffer size for the underlying {@link ReplaySubject}.\n * @param windowTime The window time for the underlying {@link ReplaySubject}.\n * @param selector A function used to setup the multicast.\n * @param timestampProvider The timestamp provider for the underlying {@link ReplaySubject}.\n * @deprecated Will be removed in v8. Use the {@link connect} operator instead.\n * `source.pipe(publishReplay(size, window, selector, scheduler))` is equivalent to\n * `source.pipe(connect(selector, { connector: () => new ReplaySubject(size, window, scheduler) }))`.\n * Details: https://rxjs.dev/deprecations/multicasting\n */\nexport function publishReplay<T, O extends ObservableInput<any>>(\n  bufferSize: number | undefined,\n  windowTime: number | undefined,\n  selector: (shared: Observable<T>) => O,\n  timestampProvider?: TimestampProvider\n): OperatorFunction<T, ObservedValueOf<O>>;\n\n/**\n * Creates a {@link ConnectableObservable} that uses a {@link ReplaySubject}\n * internally.\n *\n * @param bufferSize The buffer size for the underlying {@link ReplaySubject}.\n * @param windowTime The window time for the underlying {@link ReplaySubject}.\n * @param selector Passing `undefined` here determines that this operator will return a {@link ConnectableObservable}.\n * @param timestampProvider The timestamp provider for the underlying {@link ReplaySubject}.\n * @deprecated Will be removed in v8. To create a connectable observable that uses a\n * {@link ReplaySubject} under the hood, use {@link connectable}.\n * `source.pipe(publishReplay(size, time, scheduler))` is equivalent to\n * `connectable(source, { connector: () => new ReplaySubject(size, time, scheduler), resetOnDisconnect: false })`.\n * If you're using {@link refCount} after `publishReplay`, use the {@link share} operator instead.\n * `publishReplay(size, time, scheduler), refCount()` is equivalent to\n * `share({ connector: () => new ReplaySubject(size, time, scheduler), resetOnError: false, resetOnComplete: false, resetOnRefCountZero: false })`.\n * Details: https://rxjs.dev/deprecations/multicasting\n */\nexport function publishReplay<T, O extends ObservableInput<any>>(\n  bufferSize: number | undefined,\n  windowTime: number | undefined,\n  selector: undefined,\n  timestampProvider: TimestampProvider\n): OperatorFunction<T, ObservedValueOf<O>>;\n\n/**\n * @deprecated Will be removed in v8. Use the {@link connectable} observable, the {@link connect} operator or the\n * {@link share} operator instead. See the overloads below for equivalent replacement examples of this operator's\n * behaviors.\n * Details: https://rxjs.dev/deprecations/multicasting\n */\nexport function publishReplay<T, R>(\n  bufferSize?: number,\n  windowTime?: number,\n  selectorOrScheduler?: TimestampProvider | OperatorFunction<T, R>,\n  timestampProvider?: TimestampProvider\n) {\n  if (selectorOrScheduler && !isFunction(selectorOrScheduler)) {\n    timestampProvider = selectorOrScheduler;\n  }\n  const selector = isFunction(selectorOrScheduler) ? selectorOrScheduler : undefined;\n  // Note, we're passing `selector!` here, because at runtime, `undefined` is an acceptable argument\n  // but it makes our TypeScript signature for `multicast` unhappy (as it should, because it's gross).\n  return (source: Observable<T>) => multicast(new ReplaySubject<T>(bufferSize, windowTime, timestampProvider), selector!)(source);\n}\n"], "mappings": "AACA,SAASA,aAAa,QAAQ,kBAAkB;AAChD,SAASC,SAAS,QAAQ,aAAa;AAEvC,SAASC,UAAU,QAAQ,oBAAoB;AA8E/C,OAAM,SAAUC,aAAaA,CAC3BC,UAAmB,EACnBC,UAAmB,EACnBC,mBAAgE,EAChEC,iBAAqC;EAErC,IAAID,mBAAmB,IAAI,CAACJ,UAAU,CAACI,mBAAmB,CAAC,EAAE;IAC3DC,iBAAiB,GAAGD,mBAAmB;;EAEzC,IAAME,QAAQ,GAAGN,UAAU,CAACI,mBAAmB,CAAC,GAAGA,mBAAmB,GAAGG,SAAS;EAGlF,OAAO,UAACC,MAAqB;IAAK,OAAAT,SAAS,CAAC,IAAID,aAAa,CAAII,UAAU,EAAEC,UAAU,EAAEE,iBAAiB,CAAC,EAAEC,QAAS,CAAC,CAACE,MAAM,CAAC;EAA7F,CAA6F;AACjI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}