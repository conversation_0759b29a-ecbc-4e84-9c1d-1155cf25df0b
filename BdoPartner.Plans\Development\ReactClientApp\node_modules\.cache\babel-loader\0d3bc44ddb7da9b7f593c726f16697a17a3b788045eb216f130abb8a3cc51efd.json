{"ast": null, "code": "/**\n * Checks if a given element has a CSS class.\n * \n * @param element the element\n * @param className the CSS class name\n */\nexport default function hasClass(element, className) {\n  if (element.classList) return !!className && element.classList.contains(className);\n  return (\" \" + (element.className.baseVal || element.className) + \" \").indexOf(\" \" + className + \" \") !== -1;\n}", "map": {"version": 3, "names": ["hasClass", "element", "className", "classList", "contains", "baseVal", "indexOf"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/node_modules/dom-helpers/esm/hasClass.js"], "sourcesContent": ["/**\n * Checks if a given element has a CSS class.\n * \n * @param element the element\n * @param className the CSS class name\n */\nexport default function hasClass(element, className) {\n  if (element.classList) return !!className && element.classList.contains(className);\n  return (\" \" + (element.className.baseVal || element.className) + \" \").indexOf(\" \" + className + \" \") !== -1;\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASA,QAAQA,CAACC,OAAO,EAAEC,SAAS,EAAE;EACnD,IAAID,OAAO,CAACE,SAAS,EAAE,OAAO,CAAC,CAACD,SAAS,IAAID,OAAO,CAACE,SAAS,CAACC,QAAQ,CAACF,SAAS,CAAC;EAClF,OAAO,CAAC,GAAG,IAAID,OAAO,CAACC,SAAS,CAACG,OAAO,IAAIJ,OAAO,CAACC,SAAS,CAAC,GAAG,GAAG,EAAEI,OAAO,CAAC,GAAG,GAAGJ,SAAS,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;AAC7G", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}