{"ast": null, "code": "const makeHtml = text => {\n  if (!text) return text;\n  const htmlText = text.replace(/^### (.*$)/gim, '<h3>$1</h3>').replace(/^## (.*$)/gim, '<h2>$1</h2>').replace(/^# (.*$)/gim, '<h1>$1</h1>').replace(/^\\> (.*$)/gim, '<blockquote>$1</blockquote>').replace(/\\*\\*(.*)\\*\\*/gim, '<b>$1</b>').replace(/\\*(.*)\\*/gim, '<i>$1</i>').replace(/!\\[(.*?)\\]\\((.*?)\\)/gim, \"<img alt='$1' src='$2' />\").replace(/\\[(.*?)\\]\\((.*?)\\)/gim, \"<a href='$2' target='_blank'>$1</a>\").replace(/\\n$/gim, '<br />');\n  return htmlText.trim();\n};\nconst FormAnswerUtility = {\n  convertToHtml: makeHtml\n};\nexport default FormAnswerUtility;", "map": {"version": 3, "names": ["makeHtml", "text", "htmlText", "replace", "trim", "FormAnswerUtility", "convertToHtml"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/components/questionnaire/formAnswerUtilities.js"], "sourcesContent": ["const makeHtml = (text) => {\r\n    if (!text)\r\n        return text;\r\n\r\n    const htmlText = text\r\n    .replace(/^### (.*$)/gim, '<h3>$1</h3>')\r\n    .replace(/^## (.*$)/gim, '<h2>$1</h2>')\r\n    .replace(/^# (.*$)/gim, '<h1>$1</h1>')\r\n    .replace(/^\\> (.*$)/gim, '<blockquote>$1</blockquote>')\r\n    .replace(/\\*\\*(.*)\\*\\*/gim, '<b>$1</b>')\r\n    .replace(/\\*(.*)\\*/gim, '<i>$1</i>')\r\n    .replace(/!\\[(.*?)\\]\\((.*?)\\)/gim, \"<img alt='$1' src='$2' />\")\r\n    .replace(/\\[(.*?)\\]\\((.*?)\\)/gim, \"<a href='$2' target='_blank'>$1</a>\")\r\n    .replace(/\\n$/gim, '<br />')\r\n\r\n    return htmlText.trim();\r\n}\r\n\r\nconst FormAnswerUtility = {\r\n    convertToHtml: makeHtml\r\n}\r\n\r\nexport default FormAnswerUtility;"], "mappings": "AAAA,MAAMA,QAAQ,GAAIC,IAAI,IAAK;EACvB,IAAI,CAACA,IAAI,EACL,OAAOA,IAAI;EAEf,MAAMC,QAAQ,GAAGD,IAAI,CACpBE,OAAO,CAAC,eAAe,EAAE,aAAa,CAAC,CACvCA,OAAO,CAAC,cAAc,EAAE,aAAa,CAAC,CACtCA,OAAO,CAAC,aAAa,EAAE,aAAa,CAAC,CACrCA,OAAO,CAAC,cAAc,EAAE,6BAA6B,CAAC,CACtDA,OAAO,CAAC,iBAAiB,EAAE,WAAW,CAAC,CACvCA,OAAO,CAAC,aAAa,EAAE,WAAW,CAAC,CACnCA,OAAO,CAAC,wBAAwB,EAAE,2BAA2B,CAAC,CAC9DA,OAAO,CAAC,uBAAuB,EAAE,qCAAqC,CAAC,CACvEA,OAAO,CAAC,QAAQ,EAAE,QAAQ,CAAC;EAE5B,OAAOD,QAAQ,CAACE,IAAI,CAAC,CAAC;AAC1B,CAAC;AAED,MAAMC,iBAAiB,GAAG;EACtBC,aAAa,EAAEN;AACnB,CAAC;AAED,eAAeK,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}