{"ast": null, "code": "var now = require('performance-now'),\n  root = typeof window === 'undefined' ? global : window,\n  vendors = ['moz', 'webkit'],\n  suffix = 'AnimationFrame',\n  raf = root['request' + suffix],\n  caf = root['cancel' + suffix] || root['cancelRequest' + suffix];\nfor (var i = 0; !raf && i < vendors.length; i++) {\n  raf = root[vendors[i] + 'Request' + suffix];\n  caf = root[vendors[i] + 'Cancel' + suffix] || root[vendors[i] + 'CancelRequest' + suffix];\n}\n\n// Some versions of FF have rAF but not cAF\nif (!raf || !caf) {\n  var last = 0,\n    id = 0,\n    queue = [],\n    frameDuration = 1000 / 60;\n  raf = function (callback) {\n    if (queue.length === 0) {\n      var _now = now(),\n        next = Math.max(0, frameDuration - (_now - last));\n      last = next + _now;\n      setTimeout(function () {\n        var cp = queue.slice(0);\n        // Clear queue here to prevent\n        // callbacks from appending listeners\n        // to the current frame's queue\n        queue.length = 0;\n        for (var i = 0; i < cp.length; i++) {\n          if (!cp[i].cancelled) {\n            try {\n              cp[i].callback(last);\n            } catch (e) {\n              setTimeout(function () {\n                throw e;\n              }, 0);\n            }\n          }\n        }\n      }, Math.round(next));\n    }\n    queue.push({\n      handle: ++id,\n      callback: callback,\n      cancelled: false\n    });\n    return id;\n  };\n  caf = function (handle) {\n    for (var i = 0; i < queue.length; i++) {\n      if (queue[i].handle === handle) {\n        queue[i].cancelled = true;\n      }\n    }\n  };\n}\nmodule.exports = function (fn) {\n  // Wrap in a new function to prevent\n  // `cancel` potentially being assigned\n  // to the native rAF function\n  return raf.call(root, fn);\n};\nmodule.exports.cancel = function () {\n  caf.apply(root, arguments);\n};\nmodule.exports.polyfill = function (object) {\n  if (!object) {\n    object = root;\n  }\n  object.requestAnimationFrame = raf;\n  object.cancelAnimationFrame = caf;\n};", "map": {"version": 3, "names": ["now", "require", "root", "window", "global", "vendors", "suffix", "raf", "caf", "i", "length", "last", "id", "queue", "frameDuration", "callback", "_now", "next", "Math", "max", "setTimeout", "cp", "slice", "cancelled", "e", "round", "push", "handle", "module", "exports", "fn", "call", "cancel", "apply", "arguments", "polyfill", "object", "requestAnimationFrame", "cancelAnimationFrame"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/node_modules/raf/index.js"], "sourcesContent": ["var now = require('performance-now')\n  , root = typeof window === 'undefined' ? global : window\n  , vendors = ['moz', 'webkit']\n  , suffix = 'AnimationFrame'\n  , raf = root['request' + suffix]\n  , caf = root['cancel' + suffix] || root['cancelRequest' + suffix]\n\nfor(var i = 0; !raf && i < vendors.length; i++) {\n  raf = root[vendors[i] + 'Request' + suffix]\n  caf = root[vendors[i] + 'Cancel' + suffix]\n      || root[vendors[i] + 'CancelRequest' + suffix]\n}\n\n// Some versions of FF have rAF but not cAF\nif(!raf || !caf) {\n  var last = 0\n    , id = 0\n    , queue = []\n    , frameDuration = 1000 / 60\n\n  raf = function(callback) {\n    if(queue.length === 0) {\n      var _now = now()\n        , next = Math.max(0, frameDuration - (_now - last))\n      last = next + _now\n      setTimeout(function() {\n        var cp = queue.slice(0)\n        // Clear queue here to prevent\n        // callbacks from appending listeners\n        // to the current frame's queue\n        queue.length = 0\n        for(var i = 0; i < cp.length; i++) {\n          if(!cp[i].cancelled) {\n            try{\n              cp[i].callback(last)\n            } catch(e) {\n              setTimeout(function() { throw e }, 0)\n            }\n          }\n        }\n      }, Math.round(next))\n    }\n    queue.push({\n      handle: ++id,\n      callback: callback,\n      cancelled: false\n    })\n    return id\n  }\n\n  caf = function(handle) {\n    for(var i = 0; i < queue.length; i++) {\n      if(queue[i].handle === handle) {\n        queue[i].cancelled = true\n      }\n    }\n  }\n}\n\nmodule.exports = function(fn) {\n  // Wrap in a new function to prevent\n  // `cancel` potentially being assigned\n  // to the native rAF function\n  return raf.call(root, fn)\n}\nmodule.exports.cancel = function() {\n  caf.apply(root, arguments)\n}\nmodule.exports.polyfill = function(object) {\n  if (!object) {\n    object = root;\n  }\n  object.requestAnimationFrame = raf\n  object.cancelAnimationFrame = caf\n}\n"], "mappings": "AAAA,IAAIA,GAAG,GAAGC,OAAO,CAAC,iBAAiB,CAAC;EAChCC,IAAI,GAAG,OAAOC,MAAM,KAAK,WAAW,GAAGC,MAAM,GAAGD,MAAM;EACtDE,OAAO,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC;EAC3BC,MAAM,GAAG,gBAAgB;EACzBC,GAAG,GAAGL,IAAI,CAAC,SAAS,GAAGI,MAAM,CAAC;EAC9BE,GAAG,GAAGN,IAAI,CAAC,QAAQ,GAAGI,MAAM,CAAC,IAAIJ,IAAI,CAAC,eAAe,GAAGI,MAAM,CAAC;AAEnE,KAAI,IAAIG,CAAC,GAAG,CAAC,EAAE,CAACF,GAAG,IAAIE,CAAC,GAAGJ,OAAO,CAACK,MAAM,EAAED,CAAC,EAAE,EAAE;EAC9CF,GAAG,GAAGL,IAAI,CAACG,OAAO,CAACI,CAAC,CAAC,GAAG,SAAS,GAAGH,MAAM,CAAC;EAC3CE,GAAG,GAAGN,IAAI,CAACG,OAAO,CAACI,CAAC,CAAC,GAAG,QAAQ,GAAGH,MAAM,CAAC,IACnCJ,IAAI,CAACG,OAAO,CAACI,CAAC,CAAC,GAAG,eAAe,GAAGH,MAAM,CAAC;AACpD;;AAEA;AACA,IAAG,CAACC,GAAG,IAAI,CAACC,GAAG,EAAE;EACf,IAAIG,IAAI,GAAG,CAAC;IACRC,EAAE,GAAG,CAAC;IACNC,KAAK,GAAG,EAAE;IACVC,aAAa,GAAG,IAAI,GAAG,EAAE;EAE7BP,GAAG,GAAG,SAAAA,CAASQ,QAAQ,EAAE;IACvB,IAAGF,KAAK,CAACH,MAAM,KAAK,CAAC,EAAE;MACrB,IAAIM,IAAI,GAAGhB,GAAG,CAAC,CAAC;QACZiB,IAAI,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEL,aAAa,IAAIE,IAAI,GAAGL,IAAI,CAAC,CAAC;MACrDA,IAAI,GAAGM,IAAI,GAAGD,IAAI;MAClBI,UAAU,CAAC,YAAW;QACpB,IAAIC,EAAE,GAAGR,KAAK,CAACS,KAAK,CAAC,CAAC,CAAC;QACvB;QACA;QACA;QACAT,KAAK,CAACH,MAAM,GAAG,CAAC;QAChB,KAAI,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,EAAE,CAACX,MAAM,EAAED,CAAC,EAAE,EAAE;UACjC,IAAG,CAACY,EAAE,CAACZ,CAAC,CAAC,CAACc,SAAS,EAAE;YACnB,IAAG;cACDF,EAAE,CAACZ,CAAC,CAAC,CAACM,QAAQ,CAACJ,IAAI,CAAC;YACtB,CAAC,CAAC,OAAMa,CAAC,EAAE;cACTJ,UAAU,CAAC,YAAW;gBAAE,MAAMI,CAAC;cAAC,CAAC,EAAE,CAAC,CAAC;YACvC;UACF;QACF;MACF,CAAC,EAAEN,IAAI,CAACO,KAAK,CAACR,IAAI,CAAC,CAAC;IACtB;IACAJ,KAAK,CAACa,IAAI,CAAC;MACTC,MAAM,EAAE,EAAEf,EAAE;MACZG,QAAQ,EAAEA,QAAQ;MAClBQ,SAAS,EAAE;IACb,CAAC,CAAC;IACF,OAAOX,EAAE;EACX,CAAC;EAEDJ,GAAG,GAAG,SAAAA,CAASmB,MAAM,EAAE;IACrB,KAAI,IAAIlB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGI,KAAK,CAACH,MAAM,EAAED,CAAC,EAAE,EAAE;MACpC,IAAGI,KAAK,CAACJ,CAAC,CAAC,CAACkB,MAAM,KAAKA,MAAM,EAAE;QAC7Bd,KAAK,CAACJ,CAAC,CAAC,CAACc,SAAS,GAAG,IAAI;MAC3B;IACF;EACF,CAAC;AACH;AAEAK,MAAM,CAACC,OAAO,GAAG,UAASC,EAAE,EAAE;EAC5B;EACA;EACA;EACA,OAAOvB,GAAG,CAACwB,IAAI,CAAC7B,IAAI,EAAE4B,EAAE,CAAC;AAC3B,CAAC;AACDF,MAAM,CAACC,OAAO,CAACG,MAAM,GAAG,YAAW;EACjCxB,GAAG,CAACyB,KAAK,CAAC/B,IAAI,EAAEgC,SAAS,CAAC;AAC5B,CAAC;AACDN,MAAM,CAACC,OAAO,CAACM,QAAQ,GAAG,UAASC,MAAM,EAAE;EACzC,IAAI,CAACA,MAAM,EAAE;IACXA,MAAM,GAAGlC,IAAI;EACf;EACAkC,MAAM,CAACC,qBAAqB,GAAG9B,GAAG;EAClC6B,MAAM,CAACE,oBAAoB,GAAG9B,GAAG;AACnC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}