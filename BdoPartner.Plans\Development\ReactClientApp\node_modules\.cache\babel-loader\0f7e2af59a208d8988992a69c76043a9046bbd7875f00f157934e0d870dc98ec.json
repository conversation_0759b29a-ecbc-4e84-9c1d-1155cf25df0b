{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport { PrimeReactContext } from 'primereact/api';\nimport { ComponentBase, useHandleStyle } from 'primereact/componentbase';\nimport { CSSTransition } from 'primereact/csstransition';\nimport { useMergeProps, useMountEffect } from 'primereact/hooks';\nimport { ChevronDownIcon } from 'primereact/icons/chevrondown';\nimport { ChevronRightIcon } from 'primereact/icons/chevronright';\nimport { ObjectUtils, classNames, UniqueComponentId, IconUtils, DomHandler } from 'primereact/utils';\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nfunction _arrayLikeToArray(r, a) {\n  (null == a || a > r.length) && (a = r.length);\n  for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e];\n  return n;\n}\nfunction _arrayWithoutHoles(r) {\n  if (Array.isArray(r)) return _arrayLikeToArray(r);\n}\nfunction _iterableToArray(r) {\n  if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r);\n}\nfunction _unsupportedIterableToArray(r, a) {\n  if (r) {\n    if (\"string\" == typeof r) return _arrayLikeToArray(r, a);\n    var t = {}.toString.call(r).slice(8, -1);\n    return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0;\n  }\n}\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _toConsumableArray(r) {\n  return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread();\n}\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nfunction toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction toPropertyKey(t) {\n  var i = toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nfunction _defineProperty(e, r, t) {\n  return (r = toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _arrayWithHoles(r) {\n  if (Array.isArray(r)) return r;\n}\nfunction _iterableToArrayLimit(r, l) {\n  var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"];\n  if (null != t) {\n    var e,\n      n,\n      i,\n      u,\n      a = [],\n      f = !0,\n      o = !1;\n    try {\n      if (i = (t = t.call(r)).next, 0 === l) {\n        if (Object(t) !== t) return;\n        f = !1;\n      } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0);\n    } catch (r) {\n      o = !0, n = r;\n    } finally {\n      try {\n        if (!f && null != t[\"return\"] && (u = t[\"return\"](), Object(u) !== u)) return;\n      } finally {\n        if (o) throw n;\n      }\n    }\n    return a;\n  }\n}\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _slicedToArray(r, e) {\n  return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest();\n}\nvar classes = {\n  root: 'p-accordion p-component',\n  accordiontab: {\n    root: function root(_ref) {\n      var selected = _ref.selected;\n      return classNames('p-accordion-tab', {\n        'p-accordion-tab-active': selected\n      });\n    },\n    content: 'p-accordion-content',\n    header: function header(_ref2) {\n      var selected = _ref2.selected,\n        getTabProp = _ref2.getTabProp,\n        tab = _ref2.tab;\n      return classNames('p-accordion-header', {\n        'p-highlight': selected,\n        'p-disabled': getTabProp(tab, 'disabled')\n      });\n    },\n    headeraction: 'p-accordion-header-link',\n    headericon: 'p-accordion-toggle-icon',\n    headertitle: 'p-accordion-header-text',\n    toggleablecontent: 'p-toggleable-content',\n    transition: 'p-toggleable-content'\n  }\n};\nvar styles = \"\\n@layer primereact {\\n    .p-accordion-header-link {\\n        cursor: pointer;\\n        display: flex;\\n        align-items: center;\\n        user-select: none;\\n        position: relative;\\n        text-decoration: none;\\n    }\\n    \\n    .p-accordion-header-link:focus {\\n        z-index: 1;\\n    }\\n    \\n    .p-accordion-header-text {\\n        line-height: 1;\\n        width: 100%;\\n    }\\n}\\n\";\nvar AccordionBase = ComponentBase.extend({\n  defaultProps: {\n    __TYPE: 'Accordion',\n    id: null,\n    activeIndex: null,\n    className: null,\n    style: null,\n    multiple: false,\n    expandIcon: null,\n    collapseIcon: null,\n    transitionOptions: null,\n    onTabOpen: null,\n    onTabClose: null,\n    onTabChange: null,\n    children: undefined\n  },\n  css: {\n    classes: classes,\n    styles: styles\n  }\n});\nvar AccordionTabBase = ComponentBase.extend({\n  defaultProps: {\n    __TYPE: 'AccordionTab',\n    className: null,\n    contentClassName: null,\n    contentStyle: null,\n    disabled: false,\n    header: null,\n    headerClassName: null,\n    headerStyle: null,\n    headerTemplate: null,\n    style: null,\n    tabIndex: 0,\n    children: undefined\n  },\n  getCProp: function getCProp(tab, name) {\n    return ObjectUtils.getComponentProp(tab, name, AccordionTabBase.defaultProps);\n  },\n  getCProps: function getCProps(tab) {\n    return ObjectUtils.getComponentProps(tab, AccordionTabBase.defaultProps);\n  },\n  getCOtherProps: function getCOtherProps(tab) {\n    return ObjectUtils.getComponentDiffProps(tab, AccordionTabBase.defaultProps);\n  }\n});\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nvar AccordionTab = function AccordionTab() {};\nvar Accordion = /*#__PURE__*/React.forwardRef(function (inProps, ref) {\n  var mergeProps = useMergeProps();\n  var context = React.useContext(PrimeReactContext);\n  var props = AccordionBase.getProps(inProps, context);\n  var _React$useState = React.useState(props.id),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    idState = _React$useState2[0],\n    setIdState = _React$useState2[1];\n  var _React$useState3 = React.useState(props.activeIndex),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    activeIndexState = _React$useState4[0],\n    setActiveIndexState = _React$useState4[1];\n  var elementRef = React.useRef(null);\n  var activeIndex = props.onTabChange ? props.activeIndex : activeIndexState;\n  var count = React.Children.count(props.children);\n  var metaData = {\n    props: props,\n    state: {\n      id: idState,\n      activeIndex: activeIndexState\n    }\n  };\n  var _AccordionBase$setMet = AccordionBase.setMetaData(_objectSpread({}, metaData)),\n    ptm = _AccordionBase$setMet.ptm,\n    ptmo = _AccordionBase$setMet.ptmo,\n    cx = _AccordionBase$setMet.cx,\n    isUnstyled = _AccordionBase$setMet.isUnstyled;\n  useHandleStyle(AccordionBase.css.styles, isUnstyled, {\n    name: 'accordion'\n  });\n  var getTabProp = function getTabProp(tab, name) {\n    return AccordionTabBase.getCProp(tab, name);\n  };\n  var getTabPT = function getTabPT(tab, key, index) {\n    var tabMetaData = {\n      // props: atProps, /* @todo */\n      parent: metaData,\n      context: {\n        index: index,\n        count: count,\n        first: index === 0,\n        last: index === count - 1,\n        selected: isSelected(index),\n        disabled: getTabProp(tab, 'disabled')\n      }\n    };\n    return mergeProps(ptm(\"tab.\".concat(key), {\n      tab: tabMetaData\n    }), ptm(\"accordiontab.\".concat(key), {\n      accordiontab: tabMetaData\n    }), ptm(\"accordiontab.\".concat(key), tabMetaData), ptmo(getTabProp(tab, 'pt'), key, tabMetaData));\n  };\n  var onTabHeaderClick = function onTabHeaderClick(event, tab, index) {\n    changeActiveIndex(event, tab, index);\n  };\n  var changeActiveIndex = function changeActiveIndex(event, tab, index) {\n    if (!getTabProp(tab, 'disabled')) {\n      var selected = isSelected(index);\n      var newActiveIndex = null;\n      if (props.multiple) {\n        var indexes = activeIndex || [];\n        newActiveIndex = selected ? indexes.filter(function (i) {\n          return i !== index;\n        }) : [].concat(_toConsumableArray(indexes), [index]);\n      } else {\n        newActiveIndex = selected ? null : index;\n      }\n      var callback = selected ? props.onTabClose : props.onTabOpen;\n      callback && callback({\n        originalEvent: event,\n        index: index\n      });\n      if (props.onTabChange) {\n        props.onTabChange({\n          originalEvent: event,\n          index: newActiveIndex\n        });\n      } else {\n        setActiveIndexState(newActiveIndex);\n      }\n    }\n    event.preventDefault();\n  };\n  var onTabHeaderKeyDown = function onTabHeaderKeyDown(event, tab, index) {\n    switch (event.code) {\n      case 'ArrowDown':\n        onTabArrowDownKey(event);\n        break;\n      case 'ArrowUp':\n        onTabArrowUpKey(event);\n        break;\n      case 'Home':\n        onTabHomeKey(event);\n        break;\n      case 'End':\n        onTabEndKey(event);\n        break;\n      case 'Enter':\n      case 'NumpadEnter':\n      case 'Space':\n        onTabEnterKey(event, tab, index);\n        break;\n    }\n  };\n  var onTabArrowDownKey = function onTabArrowDownKey(event) {\n    var nextHeaderAction = _findNextHeaderAction(event.target.parentElement.parentElement);\n    nextHeaderAction ? changeFocusedTab(nextHeaderAction) : onTabHomeKey(event);\n    event.preventDefault();\n  };\n  var onTabArrowUpKey = function onTabArrowUpKey(event) {\n    var prevHeaderAction = _findPrevHeaderAction(event.target.parentElement.parentElement);\n    prevHeaderAction ? changeFocusedTab(prevHeaderAction) : onTabEndKey(event);\n    event.preventDefault();\n  };\n  var onTabHomeKey = function onTabHomeKey(event) {\n    var firstHeaderAction = findFirstHeaderAction();\n    changeFocusedTab(firstHeaderAction);\n    event.preventDefault();\n  };\n  var onTabEndKey = function onTabEndKey(event) {\n    var lastHeaderAction = findLastHeaderAction();\n    changeFocusedTab(lastHeaderAction);\n    event.preventDefault();\n  };\n  var onTabEnterKey = function onTabEnterKey(event, tab, index) {\n    changeActiveIndex(event, tab, index);\n    event.preventDefault();\n  };\n  var _findNextHeaderAction = function findNextHeaderAction(tabElement) {\n    var selfCheck = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    var nextTabElement = selfCheck ? tabElement : tabElement.nextElementSibling;\n    var headerElement = DomHandler.findSingle(nextTabElement, '[data-pc-section=\"header\"]');\n    return headerElement ? DomHandler.getAttribute(headerElement, 'data-p-disabled') ? _findNextHeaderAction(headerElement.parentElement) : DomHandler.findSingle(headerElement, '[data-pc-section=\"headeraction\"]') : null;\n  };\n  var _findPrevHeaderAction = function findPrevHeaderAction(tabElement) {\n    var selfCheck = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    var prevTabElement = selfCheck ? tabElement : tabElement.previousElementSibling;\n    var headerElement = DomHandler.findSingle(prevTabElement, '[data-pc-section=\"header\"]');\n    return headerElement ? DomHandler.getAttribute(headerElement, 'data-p-disabled') ? _findPrevHeaderAction(headerElement.parentElement) : DomHandler.findSingle(headerElement, '[data-pc-section=\"headeraction\"]') : null;\n  };\n  var findFirstHeaderAction = function findFirstHeaderAction() {\n    return _findNextHeaderAction(elementRef.current.firstElementChild, true);\n  };\n  var findLastHeaderAction = function findLastHeaderAction() {\n    return _findPrevHeaderAction(elementRef.current.lastElementChild, true);\n  };\n  var changeFocusedTab = function changeFocusedTab(element) {\n    if (element) {\n      DomHandler.focus(element);\n    }\n  };\n  var isSelected = function isSelected(index) {\n    return props.multiple && Array.isArray(activeIndex) ? activeIndex && activeIndex.some(function (i) {\n      return i === index;\n    }) : activeIndex === index;\n  };\n  React.useImperativeHandle(ref, function () {\n    return {\n      props: props,\n      getElement: function getElement() {\n        return elementRef.current;\n      }\n    };\n  });\n  useMountEffect(function () {\n    if (!idState) {\n      setIdState(UniqueComponentId());\n    }\n  });\n  if (!idState) {\n    return null;\n  }\n  var createTabHeader = function createTabHeader(tab, selected, index) {\n    var style = _objectSpread(_objectSpread({}, getTabProp(tab, 'style') || {}), getTabProp(tab, 'headerStyle') || {});\n    var headerId = idState + '_header_' + index;\n    var ariaControls = idState + '_content_' + index;\n    var tabIndex = getTabProp(tab, 'disabled') ? -1 : getTabProp(tab, 'tabIndex');\n    var headerTitleProps = mergeProps({\n      className: cx('accordiontab.headertitle')\n    }, getTabPT(tab, 'headertitle', index));\n    var tabCProps = AccordionTabBase.getCProps(tab);\n    var header = getTabProp(tab, 'headerTemplate') ? ObjectUtils.getJSXElement(getTabProp(tab, 'headerTemplate'), tabCProps) : /*#__PURE__*/React.createElement(\"span\", headerTitleProps, ObjectUtils.getJSXElement(getTabProp(tab, 'header'), tabCProps));\n    var headerIconProps = mergeProps({\n      'aria-hidden': 'true',\n      className: cx('accordiontab.headericon')\n    }, getTabPT(tab, 'headericon', index));\n    var icon = selected ? props.collapseIcon || /*#__PURE__*/React.createElement(ChevronDownIcon, headerIconProps) : props.expandIcon || /*#__PURE__*/React.createElement(ChevronRightIcon, headerIconProps);\n    var toggleIcon = IconUtils.getJSXIcon(icon, _objectSpread({}, headerIconProps), {\n      props: props,\n      selected: selected\n    });\n    var headerProps = mergeProps({\n      className: classNames(getTabProp(tab, 'headerClassName'), getTabProp(tab, 'className'), cx('accordiontab.header', {\n        selected: selected,\n        getTabProp: getTabProp,\n        tab: tab\n      })),\n      style: style,\n      'data-p-highlight': selected,\n      'data-p-disabled': getTabProp(tab, 'disabled')\n    }, getTabPT(tab, 'header', index));\n    var headerActionProps = mergeProps({\n      id: headerId,\n      href: '#' + ariaControls,\n      className: cx('accordiontab.headeraction'),\n      role: 'button',\n      tabIndex: tabIndex,\n      onClick: function onClick(e) {\n        return onTabHeaderClick(e, tab, index);\n      },\n      onKeyDown: function onKeyDown(e) {\n        return onTabHeaderKeyDown(e, tab, index);\n      },\n      'aria-disabled': getTabProp(tab, 'disabled'),\n      'aria-controls': ariaControls,\n      'aria-expanded': selected\n    }, getTabPT(tab, 'headeraction', index));\n    return /*#__PURE__*/React.createElement(\"div\", headerProps, /*#__PURE__*/React.createElement(\"a\", headerActionProps, toggleIcon, header));\n  };\n  var createTabContent = function createTabContent(tab, selected, index) {\n    var style = _objectSpread(_objectSpread({}, getTabProp(tab, 'style') || {}), getTabProp(tab, 'contentStyle') || {});\n    var contentId = idState + '_content_' + index;\n    var ariaLabelledby = idState + '_header_' + index;\n    var contentRef = /*#__PURE__*/React.createRef();\n    var toggleableContentProps = mergeProps({\n      id: contentId,\n      ref: contentRef,\n      className: classNames(getTabProp(tab, 'contentClassName'), getTabProp(tab, 'className'), cx('accordiontab.toggleablecontent')),\n      style: style,\n      role: 'region',\n      'aria-labelledby': ariaLabelledby\n    }, getTabPT(tab, 'toggleablecontent', index));\n    var contentProps = mergeProps({\n      className: cx('accordiontab.content')\n    }, getTabPT(tab, 'content', index));\n    var transitionProps = mergeProps({\n      classNames: cx('accordiontab.transition'),\n      timeout: {\n        enter: 1000,\n        exit: 450\n      },\n      \"in\": selected,\n      unmountOnExit: true,\n      options: props.transitionOptions\n    }, getTabPT(tab, 'transition', index));\n    return /*#__PURE__*/React.createElement(CSSTransition, _extends({\n      nodeRef: contentRef\n    }, transitionProps), /*#__PURE__*/React.createElement(\"div\", toggleableContentProps, /*#__PURE__*/React.createElement(\"div\", contentProps, getTabProp(tab, 'children'))));\n  };\n  var createTab = function createTab(tab, index) {\n    if (ObjectUtils.isValidChild(tab, 'AccordionTab')) {\n      var key = idState + '_' + index;\n      var selected = isSelected(index);\n      var tabHeader = createTabHeader(tab, selected, index);\n      var tabContent = createTabContent(tab, selected, index);\n      var _rootProps = mergeProps({\n        key: key,\n        className: cx('accordiontab.root', {\n          selected: selected\n        })\n      }, AccordionTabBase.getCOtherProps(tab), getTabPT(tab, 'root', index));\n      return /*#__PURE__*/React.createElement(\"div\", _rootProps, tabHeader, tabContent);\n    }\n    return null;\n  };\n  var createTabs = function createTabs() {\n    return React.Children.map(props.children, createTab);\n  };\n  var tabs = createTabs();\n  var rootProps = mergeProps({\n    className: classNames(props.className, cx('root')),\n    style: props.style\n  }, AccordionBase.getOtherProps(props), ptm('root'));\n  return /*#__PURE__*/React.createElement(\"div\", _extends({\n    id: idState,\n    ref: elementRef\n  }, rootProps), tabs);\n});\nAccordionTab.displayName = 'AccordionTab';\nAccordion.displayName = 'Accordion';\nexport { Accordion, AccordionTab };", "map": {"version": 3, "names": ["React", "PrimeReactContext", "ComponentBase", "useHandleStyle", "CSSTransition", "useMergeProps", "useMountEffect", "ChevronDownIcon", "ChevronRightIcon", "ObjectUtils", "classNames", "UniqueComponentId", "IconUtils", "<PERSON><PERSON><PERSON><PERSON>", "_extends", "Object", "assign", "bind", "n", "e", "arguments", "length", "t", "r", "hasOwnProperty", "call", "apply", "_arrayLikeToArray", "a", "Array", "_arrayWithoutHoles", "isArray", "_iterableToArray", "Symbol", "iterator", "from", "_unsupportedIterableToArray", "toString", "slice", "constructor", "name", "test", "_nonIterableSpread", "TypeError", "_toConsumableArray", "_typeof", "o", "prototype", "toPrimitive", "i", "String", "Number", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_defineProperty", "defineProperty", "value", "enumerable", "configurable", "writable", "_arrayWithHoles", "_iterableToArrayLimit", "l", "u", "f", "next", "done", "push", "_nonIterableRest", "_slicedToArray", "classes", "root", "accordiontab", "_ref", "selected", "content", "header", "_ref2", "getTabProp", "tab", "headeraction", "headericon", "headertitle", "toggleablecontent", "transition", "styles", "AccordionBase", "extend", "defaultProps", "__TYPE", "id", "activeIndex", "className", "style", "multiple", "expandIcon", "collapseIcon", "transitionOptions", "onTabOpen", "onTabClose", "onTabChange", "children", "undefined", "css", "AccordionTabBase", "contentClassName", "contentStyle", "disabled", "headerClassName", "headerStyle", "headerTemplate", "tabIndex", "getCProp", "getComponentProp", "getCProps", "getComponentProps", "getCOtherProps", "getComponentDiffProps", "ownKeys", "keys", "getOwnPropertySymbols", "filter", "getOwnPropertyDescriptor", "_objectSpread", "for<PERSON>ach", "getOwnPropertyDescriptors", "defineProperties", "AccordionTab", "Accordion", "forwardRef", "inProps", "ref", "mergeProps", "context", "useContext", "props", "getProps", "_React$useState", "useState", "_React$useState2", "idState", "setIdState", "_React$useState3", "_React$useState4", "activeIndexState", "setActiveIndexState", "elementRef", "useRef", "count", "Children", "metaData", "state", "_AccordionBase$setMet", "setMetaData", "ptm", "ptmo", "cx", "isUnstyled", "getTabPT", "key", "index", "tabMetaData", "parent", "first", "last", "isSelected", "concat", "onTabHeaderClick", "event", "changeActiveIndex", "newActiveIndex", "indexes", "callback", "originalEvent", "preventDefault", "onTabHeaderKeyDown", "code", "onTabArrowDownKey", "onTabArrowUpKey", "onTabHomeKey", "onTabEndKey", "onTabEnterKey", "nextHeaderAction", "_findNextHeaderAction", "target", "parentElement", "changeFocusedTab", "prevHeaderAction", "_findPrevHeaderAction", "firstHeaderAction", "findFirstHeaderAction", "lastHeaderAction", "findLastHeaderAction", "findNextHeaderAction", "tabElement", "<PERSON><PERSON><PERSON><PERSON>", "nextTabElement", "nextElement<PERSON><PERSON>ling", "headerElement", "findSingle", "getAttribute", "findPrevHeaderAction", "prevTabElement", "previousElementSibling", "current", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "element", "focus", "some", "useImperativeHandle", "getElement", "createTabHeader", "headerId", "ariaControls", "headerTitleProps", "tabCProps", "getJSXElement", "createElement", "headerIconProps", "icon", "toggleIcon", "getJSXIcon", "headerProps", "headerActionProps", "href", "role", "onClick", "onKeyDown", "createTabContent", "contentId", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "contentRef", "createRef", "toggleableContentProps", "contentProps", "transitionProps", "timeout", "enter", "exit", "unmountOnExit", "options", "nodeRef", "createTab", "is<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tabHeader", "tab<PERSON>ontent", "_rootProps", "createTabs", "map", "tabs", "rootProps", "getOtherProps", "displayName"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/node_modules/primereact/accordion/accordion.esm.js"], "sourcesContent": ["'use client';\nimport * as React from 'react';\nimport { PrimeReactContext } from 'primereact/api';\nimport { ComponentBase, useHandleStyle } from 'primereact/componentbase';\nimport { CSSTransition } from 'primereact/csstransition';\nimport { useMergeProps, useMountEffect } from 'primereact/hooks';\nimport { ChevronDownIcon } from 'primereact/icons/chevrondown';\nimport { ChevronRightIcon } from 'primereact/icons/chevronright';\nimport { ObjectUtils, classNames, UniqueComponentId, IconUtils, DomHandler } from 'primereact/utils';\n\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\n\nfunction _arrayLikeToArray(r, a) {\n  (null == a || a > r.length) && (a = r.length);\n  for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e];\n  return n;\n}\n\nfunction _arrayWithoutHoles(r) {\n  if (Array.isArray(r)) return _arrayLikeToArray(r);\n}\n\nfunction _iterableToArray(r) {\n  if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r);\n}\n\nfunction _unsupportedIterableToArray(r, a) {\n  if (r) {\n    if (\"string\" == typeof r) return _arrayLikeToArray(r, a);\n    var t = {}.toString.call(r).slice(8, -1);\n    return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0;\n  }\n}\n\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nfunction _toConsumableArray(r) {\n  return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread();\n}\n\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\n\nfunction toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\n\nfunction toPropertyKey(t) {\n  var i = toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\n\nfunction _defineProperty(e, r, t) {\n  return (r = toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\n\nfunction _arrayWithHoles(r) {\n  if (Array.isArray(r)) return r;\n}\n\nfunction _iterableToArrayLimit(r, l) {\n  var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"];\n  if (null != t) {\n    var e,\n      n,\n      i,\n      u,\n      a = [],\n      f = !0,\n      o = !1;\n    try {\n      if (i = (t = t.call(r)).next, 0 === l) {\n        if (Object(t) !== t) return;\n        f = !1;\n      } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0);\n    } catch (r) {\n      o = !0, n = r;\n    } finally {\n      try {\n        if (!f && null != t[\"return\"] && (u = t[\"return\"](), Object(u) !== u)) return;\n      } finally {\n        if (o) throw n;\n      }\n    }\n    return a;\n  }\n}\n\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nfunction _slicedToArray(r, e) {\n  return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest();\n}\n\nvar classes = {\n  root: 'p-accordion p-component',\n  accordiontab: {\n    root: function root(_ref) {\n      var selected = _ref.selected;\n      return classNames('p-accordion-tab', {\n        'p-accordion-tab-active': selected\n      });\n    },\n    content: 'p-accordion-content',\n    header: function header(_ref2) {\n      var selected = _ref2.selected,\n        getTabProp = _ref2.getTabProp,\n        tab = _ref2.tab;\n      return classNames('p-accordion-header', {\n        'p-highlight': selected,\n        'p-disabled': getTabProp(tab, 'disabled')\n      });\n    },\n    headeraction: 'p-accordion-header-link',\n    headericon: 'p-accordion-toggle-icon',\n    headertitle: 'p-accordion-header-text',\n    toggleablecontent: 'p-toggleable-content',\n    transition: 'p-toggleable-content'\n  }\n};\nvar styles = \"\\n@layer primereact {\\n    .p-accordion-header-link {\\n        cursor: pointer;\\n        display: flex;\\n        align-items: center;\\n        user-select: none;\\n        position: relative;\\n        text-decoration: none;\\n    }\\n    \\n    .p-accordion-header-link:focus {\\n        z-index: 1;\\n    }\\n    \\n    .p-accordion-header-text {\\n        line-height: 1;\\n        width: 100%;\\n    }\\n}\\n\";\nvar AccordionBase = ComponentBase.extend({\n  defaultProps: {\n    __TYPE: 'Accordion',\n    id: null,\n    activeIndex: null,\n    className: null,\n    style: null,\n    multiple: false,\n    expandIcon: null,\n    collapseIcon: null,\n    transitionOptions: null,\n    onTabOpen: null,\n    onTabClose: null,\n    onTabChange: null,\n    children: undefined\n  },\n  css: {\n    classes: classes,\n    styles: styles\n  }\n});\nvar AccordionTabBase = ComponentBase.extend({\n  defaultProps: {\n    __TYPE: 'AccordionTab',\n    className: null,\n    contentClassName: null,\n    contentStyle: null,\n    disabled: false,\n    header: null,\n    headerClassName: null,\n    headerStyle: null,\n    headerTemplate: null,\n    style: null,\n    tabIndex: 0,\n    children: undefined\n  },\n  getCProp: function getCProp(tab, name) {\n    return ObjectUtils.getComponentProp(tab, name, AccordionTabBase.defaultProps);\n  },\n  getCProps: function getCProps(tab) {\n    return ObjectUtils.getComponentProps(tab, AccordionTabBase.defaultProps);\n  },\n  getCOtherProps: function getCOtherProps(tab) {\n    return ObjectUtils.getComponentDiffProps(tab, AccordionTabBase.defaultProps);\n  }\n});\n\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nvar AccordionTab = function AccordionTab() {};\nvar Accordion = /*#__PURE__*/React.forwardRef(function (inProps, ref) {\n  var mergeProps = useMergeProps();\n  var context = React.useContext(PrimeReactContext);\n  var props = AccordionBase.getProps(inProps, context);\n  var _React$useState = React.useState(props.id),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    idState = _React$useState2[0],\n    setIdState = _React$useState2[1];\n  var _React$useState3 = React.useState(props.activeIndex),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    activeIndexState = _React$useState4[0],\n    setActiveIndexState = _React$useState4[1];\n  var elementRef = React.useRef(null);\n  var activeIndex = props.onTabChange ? props.activeIndex : activeIndexState;\n  var count = React.Children.count(props.children);\n  var metaData = {\n    props: props,\n    state: {\n      id: idState,\n      activeIndex: activeIndexState\n    }\n  };\n  var _AccordionBase$setMet = AccordionBase.setMetaData(_objectSpread({}, metaData)),\n    ptm = _AccordionBase$setMet.ptm,\n    ptmo = _AccordionBase$setMet.ptmo,\n    cx = _AccordionBase$setMet.cx,\n    isUnstyled = _AccordionBase$setMet.isUnstyled;\n  useHandleStyle(AccordionBase.css.styles, isUnstyled, {\n    name: 'accordion'\n  });\n  var getTabProp = function getTabProp(tab, name) {\n    return AccordionTabBase.getCProp(tab, name);\n  };\n  var getTabPT = function getTabPT(tab, key, index) {\n    var tabMetaData = {\n      // props: atProps, /* @todo */\n      parent: metaData,\n      context: {\n        index: index,\n        count: count,\n        first: index === 0,\n        last: index === count - 1,\n        selected: isSelected(index),\n        disabled: getTabProp(tab, 'disabled')\n      }\n    };\n    return mergeProps(ptm(\"tab.\".concat(key), {\n      tab: tabMetaData\n    }), ptm(\"accordiontab.\".concat(key), {\n      accordiontab: tabMetaData\n    }), ptm(\"accordiontab.\".concat(key), tabMetaData), ptmo(getTabProp(tab, 'pt'), key, tabMetaData));\n  };\n  var onTabHeaderClick = function onTabHeaderClick(event, tab, index) {\n    changeActiveIndex(event, tab, index);\n  };\n  var changeActiveIndex = function changeActiveIndex(event, tab, index) {\n    if (!getTabProp(tab, 'disabled')) {\n      var selected = isSelected(index);\n      var newActiveIndex = null;\n      if (props.multiple) {\n        var indexes = activeIndex || [];\n        newActiveIndex = selected ? indexes.filter(function (i) {\n          return i !== index;\n        }) : [].concat(_toConsumableArray(indexes), [index]);\n      } else {\n        newActiveIndex = selected ? null : index;\n      }\n      var callback = selected ? props.onTabClose : props.onTabOpen;\n      callback && callback({\n        originalEvent: event,\n        index: index\n      });\n      if (props.onTabChange) {\n        props.onTabChange({\n          originalEvent: event,\n          index: newActiveIndex\n        });\n      } else {\n        setActiveIndexState(newActiveIndex);\n      }\n    }\n    event.preventDefault();\n  };\n  var onTabHeaderKeyDown = function onTabHeaderKeyDown(event, tab, index) {\n    switch (event.code) {\n      case 'ArrowDown':\n        onTabArrowDownKey(event);\n        break;\n      case 'ArrowUp':\n        onTabArrowUpKey(event);\n        break;\n      case 'Home':\n        onTabHomeKey(event);\n        break;\n      case 'End':\n        onTabEndKey(event);\n        break;\n      case 'Enter':\n      case 'NumpadEnter':\n      case 'Space':\n        onTabEnterKey(event, tab, index);\n        break;\n    }\n  };\n  var onTabArrowDownKey = function onTabArrowDownKey(event) {\n    var nextHeaderAction = _findNextHeaderAction(event.target.parentElement.parentElement);\n    nextHeaderAction ? changeFocusedTab(nextHeaderAction) : onTabHomeKey(event);\n    event.preventDefault();\n  };\n  var onTabArrowUpKey = function onTabArrowUpKey(event) {\n    var prevHeaderAction = _findPrevHeaderAction(event.target.parentElement.parentElement);\n    prevHeaderAction ? changeFocusedTab(prevHeaderAction) : onTabEndKey(event);\n    event.preventDefault();\n  };\n  var onTabHomeKey = function onTabHomeKey(event) {\n    var firstHeaderAction = findFirstHeaderAction();\n    changeFocusedTab(firstHeaderAction);\n    event.preventDefault();\n  };\n  var onTabEndKey = function onTabEndKey(event) {\n    var lastHeaderAction = findLastHeaderAction();\n    changeFocusedTab(lastHeaderAction);\n    event.preventDefault();\n  };\n  var onTabEnterKey = function onTabEnterKey(event, tab, index) {\n    changeActiveIndex(event, tab, index);\n    event.preventDefault();\n  };\n  var _findNextHeaderAction = function findNextHeaderAction(tabElement) {\n    var selfCheck = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    var nextTabElement = selfCheck ? tabElement : tabElement.nextElementSibling;\n    var headerElement = DomHandler.findSingle(nextTabElement, '[data-pc-section=\"header\"]');\n    return headerElement ? DomHandler.getAttribute(headerElement, 'data-p-disabled') ? _findNextHeaderAction(headerElement.parentElement) : DomHandler.findSingle(headerElement, '[data-pc-section=\"headeraction\"]') : null;\n  };\n  var _findPrevHeaderAction = function findPrevHeaderAction(tabElement) {\n    var selfCheck = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    var prevTabElement = selfCheck ? tabElement : tabElement.previousElementSibling;\n    var headerElement = DomHandler.findSingle(prevTabElement, '[data-pc-section=\"header\"]');\n    return headerElement ? DomHandler.getAttribute(headerElement, 'data-p-disabled') ? _findPrevHeaderAction(headerElement.parentElement) : DomHandler.findSingle(headerElement, '[data-pc-section=\"headeraction\"]') : null;\n  };\n  var findFirstHeaderAction = function findFirstHeaderAction() {\n    return _findNextHeaderAction(elementRef.current.firstElementChild, true);\n  };\n  var findLastHeaderAction = function findLastHeaderAction() {\n    return _findPrevHeaderAction(elementRef.current.lastElementChild, true);\n  };\n  var changeFocusedTab = function changeFocusedTab(element) {\n    if (element) {\n      DomHandler.focus(element);\n    }\n  };\n  var isSelected = function isSelected(index) {\n    return props.multiple && Array.isArray(activeIndex) ? activeIndex && activeIndex.some(function (i) {\n      return i === index;\n    }) : activeIndex === index;\n  };\n  React.useImperativeHandle(ref, function () {\n    return {\n      props: props,\n      getElement: function getElement() {\n        return elementRef.current;\n      }\n    };\n  });\n  useMountEffect(function () {\n    if (!idState) {\n      setIdState(UniqueComponentId());\n    }\n  });\n  if (!idState) {\n    return null;\n  }\n  var createTabHeader = function createTabHeader(tab, selected, index) {\n    var style = _objectSpread(_objectSpread({}, getTabProp(tab, 'style') || {}), getTabProp(tab, 'headerStyle') || {});\n    var headerId = idState + '_header_' + index;\n    var ariaControls = idState + '_content_' + index;\n    var tabIndex = getTabProp(tab, 'disabled') ? -1 : getTabProp(tab, 'tabIndex');\n    var headerTitleProps = mergeProps({\n      className: cx('accordiontab.headertitle')\n    }, getTabPT(tab, 'headertitle', index));\n    var tabCProps = AccordionTabBase.getCProps(tab);\n    var header = getTabProp(tab, 'headerTemplate') ? ObjectUtils.getJSXElement(getTabProp(tab, 'headerTemplate'), tabCProps) : /*#__PURE__*/React.createElement(\"span\", headerTitleProps, ObjectUtils.getJSXElement(getTabProp(tab, 'header'), tabCProps));\n    var headerIconProps = mergeProps({\n      'aria-hidden': 'true',\n      className: cx('accordiontab.headericon')\n    }, getTabPT(tab, 'headericon', index));\n    var icon = selected ? props.collapseIcon || /*#__PURE__*/React.createElement(ChevronDownIcon, headerIconProps) : props.expandIcon || /*#__PURE__*/React.createElement(ChevronRightIcon, headerIconProps);\n    var toggleIcon = IconUtils.getJSXIcon(icon, _objectSpread({}, headerIconProps), {\n      props: props,\n      selected: selected\n    });\n    var headerProps = mergeProps({\n      className: classNames(getTabProp(tab, 'headerClassName'), getTabProp(tab, 'className'), cx('accordiontab.header', {\n        selected: selected,\n        getTabProp: getTabProp,\n        tab: tab\n      })),\n      style: style,\n      'data-p-highlight': selected,\n      'data-p-disabled': getTabProp(tab, 'disabled')\n    }, getTabPT(tab, 'header', index));\n    var headerActionProps = mergeProps({\n      id: headerId,\n      href: '#' + ariaControls,\n      className: cx('accordiontab.headeraction'),\n      role: 'button',\n      tabIndex: tabIndex,\n      onClick: function onClick(e) {\n        return onTabHeaderClick(e, tab, index);\n      },\n      onKeyDown: function onKeyDown(e) {\n        return onTabHeaderKeyDown(e, tab, index);\n      },\n      'aria-disabled': getTabProp(tab, 'disabled'),\n      'aria-controls': ariaControls,\n      'aria-expanded': selected\n    }, getTabPT(tab, 'headeraction', index));\n    return /*#__PURE__*/React.createElement(\"div\", headerProps, /*#__PURE__*/React.createElement(\"a\", headerActionProps, toggleIcon, header));\n  };\n  var createTabContent = function createTabContent(tab, selected, index) {\n    var style = _objectSpread(_objectSpread({}, getTabProp(tab, 'style') || {}), getTabProp(tab, 'contentStyle') || {});\n    var contentId = idState + '_content_' + index;\n    var ariaLabelledby = idState + '_header_' + index;\n    var contentRef = /*#__PURE__*/React.createRef();\n    var toggleableContentProps = mergeProps({\n      id: contentId,\n      ref: contentRef,\n      className: classNames(getTabProp(tab, 'contentClassName'), getTabProp(tab, 'className'), cx('accordiontab.toggleablecontent')),\n      style: style,\n      role: 'region',\n      'aria-labelledby': ariaLabelledby\n    }, getTabPT(tab, 'toggleablecontent', index));\n    var contentProps = mergeProps({\n      className: cx('accordiontab.content')\n    }, getTabPT(tab, 'content', index));\n    var transitionProps = mergeProps({\n      classNames: cx('accordiontab.transition'),\n      timeout: {\n        enter: 1000,\n        exit: 450\n      },\n      \"in\": selected,\n      unmountOnExit: true,\n      options: props.transitionOptions\n    }, getTabPT(tab, 'transition', index));\n    return /*#__PURE__*/React.createElement(CSSTransition, _extends({\n      nodeRef: contentRef\n    }, transitionProps), /*#__PURE__*/React.createElement(\"div\", toggleableContentProps, /*#__PURE__*/React.createElement(\"div\", contentProps, getTabProp(tab, 'children'))));\n  };\n  var createTab = function createTab(tab, index) {\n    if (ObjectUtils.isValidChild(tab, 'AccordionTab')) {\n      var key = idState + '_' + index;\n      var selected = isSelected(index);\n      var tabHeader = createTabHeader(tab, selected, index);\n      var tabContent = createTabContent(tab, selected, index);\n      var _rootProps = mergeProps({\n        key: key,\n        className: cx('accordiontab.root', {\n          selected: selected\n        })\n      }, AccordionTabBase.getCOtherProps(tab), getTabPT(tab, 'root', index));\n      return /*#__PURE__*/React.createElement(\"div\", _rootProps, tabHeader, tabContent);\n    }\n    return null;\n  };\n  var createTabs = function createTabs() {\n    return React.Children.map(props.children, createTab);\n  };\n  var tabs = createTabs();\n  var rootProps = mergeProps({\n    className: classNames(props.className, cx('root')),\n    style: props.style\n  }, AccordionBase.getOtherProps(props), ptm('root'));\n  return /*#__PURE__*/React.createElement(\"div\", _extends({\n    id: idState,\n    ref: elementRef\n  }, rootProps), tabs);\n});\nAccordionTab.displayName = 'AccordionTab';\nAccordion.displayName = 'Accordion';\n\nexport { Accordion, AccordionTab };\n"], "mappings": "AAAA,YAAY;;AACZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,iBAAiB,QAAQ,gBAAgB;AAClD,SAASC,aAAa,EAAEC,cAAc,QAAQ,0BAA0B;AACxE,SAASC,aAAa,QAAQ,0BAA0B;AACxD,SAASC,aAAa,EAAEC,cAAc,QAAQ,kBAAkB;AAChE,SAASC,eAAe,QAAQ,8BAA8B;AAC9D,SAASC,gBAAgB,QAAQ,+BAA+B;AAChE,SAASC,WAAW,EAAEC,UAAU,EAAEC,iBAAiB,EAAEC,SAAS,EAAEC,UAAU,QAAQ,kBAAkB;AAEpG,SAASC,QAAQA,CAAA,EAAG;EAClB,OAAOA,QAAQ,GAAGC,MAAM,CAACC,MAAM,GAAGD,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUC,CAAC,EAAE;IACpE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MACzC,IAAIG,CAAC,GAAGF,SAAS,CAACD,CAAC,CAAC;MACpB,KAAK,IAAII,CAAC,IAAID,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEE,cAAc,CAACC,IAAI,CAACH,CAAC,EAAEC,CAAC,CAAC,KAAKL,CAAC,CAACK,CAAC,CAAC,GAAGD,CAAC,CAACC,CAAC,CAAC,CAAC;IAClE;IACA,OAAOL,CAAC;EACV,CAAC,EAAEJ,QAAQ,CAACY,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;AACpC;AAEA,SAASO,iBAAiBA,CAACJ,CAAC,EAAEK,CAAC,EAAE;EAC/B,CAAC,IAAI,IAAIA,CAAC,IAAIA,CAAC,GAAGL,CAAC,CAACF,MAAM,MAAMO,CAAC,GAAGL,CAAC,CAACF,MAAM,CAAC;EAC7C,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAED,CAAC,GAAGW,KAAK,CAACD,CAAC,CAAC,EAAET,CAAC,GAAGS,CAAC,EAAET,CAAC,EAAE,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGI,CAAC,CAACJ,CAAC,CAAC;EACrD,OAAOD,CAAC;AACV;AAEA,SAASY,kBAAkBA,CAACP,CAAC,EAAE;EAC7B,IAAIM,KAAK,CAACE,OAAO,CAACR,CAAC,CAAC,EAAE,OAAOI,iBAAiB,CAACJ,CAAC,CAAC;AACnD;AAEA,SAASS,gBAAgBA,CAACT,CAAC,EAAE;EAC3B,IAAI,WAAW,IAAI,OAAOU,MAAM,IAAI,IAAI,IAAIV,CAAC,CAACU,MAAM,CAACC,QAAQ,CAAC,IAAI,IAAI,IAAIX,CAAC,CAAC,YAAY,CAAC,EAAE,OAAOM,KAAK,CAACM,IAAI,CAACZ,CAAC,CAAC;AACjH;AAEA,SAASa,2BAA2BA,CAACb,CAAC,EAAEK,CAAC,EAAE;EACzC,IAAIL,CAAC,EAAE;IACL,IAAI,QAAQ,IAAI,OAAOA,CAAC,EAAE,OAAOI,iBAAiB,CAACJ,CAAC,EAAEK,CAAC,CAAC;IACxD,IAAIN,CAAC,GAAG,CAAC,CAAC,CAACe,QAAQ,CAACZ,IAAI,CAACF,CAAC,CAAC,CAACe,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACxC,OAAO,QAAQ,KAAKhB,CAAC,IAAIC,CAAC,CAACgB,WAAW,KAAKjB,CAAC,GAAGC,CAAC,CAACgB,WAAW,CAACC,IAAI,CAAC,EAAE,KAAK,KAAKlB,CAAC,IAAI,KAAK,KAAKA,CAAC,GAAGO,KAAK,CAACM,IAAI,CAACZ,CAAC,CAAC,GAAG,WAAW,KAAKD,CAAC,IAAI,0CAA0C,CAACmB,IAAI,CAACnB,CAAC,CAAC,GAAGK,iBAAiB,CAACJ,CAAC,EAAEK,CAAC,CAAC,GAAG,KAAK,CAAC;EAC7N;AACF;AAEA,SAASc,kBAAkBA,CAAA,EAAG;EAC5B,MAAM,IAAIC,SAAS,CAAC,sIAAsI,CAAC;AAC7J;AAEA,SAASC,kBAAkBA,CAACrB,CAAC,EAAE;EAC7B,OAAOO,kBAAkB,CAACP,CAAC,CAAC,IAAIS,gBAAgB,CAACT,CAAC,CAAC,IAAIa,2BAA2B,CAACb,CAAC,CAAC,IAAImB,kBAAkB,CAAC,CAAC;AAC/G;AAEA,SAASG,OAAOA,CAACC,CAAC,EAAE;EAClB,yBAAyB;;EAEzB,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOZ,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUY,CAAC,EAAE;IAChG,OAAO,OAAOA,CAAC;EACjB,CAAC,GAAG,UAAUA,CAAC,EAAE;IACf,OAAOA,CAAC,IAAI,UAAU,IAAI,OAAOb,MAAM,IAAIa,CAAC,CAACP,WAAW,KAAKN,MAAM,IAAIa,CAAC,KAAKb,MAAM,CAACc,SAAS,GAAG,QAAQ,GAAG,OAAOD,CAAC;EACrH,CAAC,EAAED,OAAO,CAACC,CAAC,CAAC;AACf;AAEA,SAASE,WAAWA,CAAC1B,CAAC,EAAEC,CAAC,EAAE;EACzB,IAAI,QAAQ,IAAIsB,OAAO,CAACvB,CAAC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAC1C,IAAIH,CAAC,GAAGG,CAAC,CAACW,MAAM,CAACe,WAAW,CAAC;EAC7B,IAAI,KAAK,CAAC,KAAK7B,CAAC,EAAE;IAChB,IAAI8B,CAAC,GAAG9B,CAAC,CAACM,IAAI,CAACH,CAAC,EAAEC,CAAC,IAAI,SAAS,CAAC;IACjC,IAAI,QAAQ,IAAIsB,OAAO,CAACI,CAAC,CAAC,EAAE,OAAOA,CAAC;IACpC,MAAM,IAAIN,SAAS,CAAC,8CAA8C,CAAC;EACrE;EACA,OAAO,CAAC,QAAQ,KAAKpB,CAAC,GAAG2B,MAAM,GAAGC,MAAM,EAAE7B,CAAC,CAAC;AAC9C;AAEA,SAAS8B,aAAaA,CAAC9B,CAAC,EAAE;EACxB,IAAI2B,CAAC,GAAGD,WAAW,CAAC1B,CAAC,EAAE,QAAQ,CAAC;EAChC,OAAO,QAAQ,IAAIuB,OAAO,CAACI,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAC5C;AAEA,SAASI,eAAeA,CAAClC,CAAC,EAAEI,CAAC,EAAED,CAAC,EAAE;EAChC,OAAO,CAACC,CAAC,GAAG6B,aAAa,CAAC7B,CAAC,CAAC,KAAKJ,CAAC,GAAGJ,MAAM,CAACuC,cAAc,CAACnC,CAAC,EAAEI,CAAC,EAAE;IAC/DgC,KAAK,EAAEjC,CAAC;IACRkC,UAAU,EAAE,CAAC,CAAC;IACdC,YAAY,EAAE,CAAC,CAAC;IAChBC,QAAQ,EAAE,CAAC;EACb,CAAC,CAAC,GAAGvC,CAAC,CAACI,CAAC,CAAC,GAAGD,CAAC,EAAEH,CAAC;AAClB;AAEA,SAASwC,eAAeA,CAACpC,CAAC,EAAE;EAC1B,IAAIM,KAAK,CAACE,OAAO,CAACR,CAAC,CAAC,EAAE,OAAOA,CAAC;AAChC;AAEA,SAASqC,qBAAqBA,CAACrC,CAAC,EAAEsC,CAAC,EAAE;EACnC,IAAIvC,CAAC,GAAG,IAAI,IAAIC,CAAC,GAAG,IAAI,GAAG,WAAW,IAAI,OAAOU,MAAM,IAAIV,CAAC,CAACU,MAAM,CAACC,QAAQ,CAAC,IAAIX,CAAC,CAAC,YAAY,CAAC;EAChG,IAAI,IAAI,IAAID,CAAC,EAAE;IACb,IAAIH,CAAC;MACHD,CAAC;MACD+B,CAAC;MACDa,CAAC;MACDlC,CAAC,GAAG,EAAE;MACNmC,CAAC,GAAG,CAAC,CAAC;MACNjB,CAAC,GAAG,CAAC,CAAC;IACR,IAAI;MACF,IAAIG,CAAC,GAAG,CAAC3B,CAAC,GAAGA,CAAC,CAACG,IAAI,CAACF,CAAC,CAAC,EAAEyC,IAAI,EAAE,CAAC,KAAKH,CAAC,EAAE;QACrC,IAAI9C,MAAM,CAACO,CAAC,CAAC,KAAKA,CAAC,EAAE;QACrByC,CAAC,GAAG,CAAC,CAAC;MACR,CAAC,MAAM,OAAO,EAAEA,CAAC,GAAG,CAAC5C,CAAC,GAAG8B,CAAC,CAACxB,IAAI,CAACH,CAAC,CAAC,EAAE2C,IAAI,CAAC,KAAKrC,CAAC,CAACsC,IAAI,CAAC/C,CAAC,CAACoC,KAAK,CAAC,EAAE3B,CAAC,CAACP,MAAM,KAAKwC,CAAC,CAAC,EAAEE,CAAC,GAAG,CAAC,CAAC,CAAC;IACzF,CAAC,CAAC,OAAOxC,CAAC,EAAE;MACVuB,CAAC,GAAG,CAAC,CAAC,EAAE5B,CAAC,GAAGK,CAAC;IACf,CAAC,SAAS;MACR,IAAI;QACF,IAAI,CAACwC,CAAC,IAAI,IAAI,IAAIzC,CAAC,CAAC,QAAQ,CAAC,KAAKwC,CAAC,GAAGxC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAEP,MAAM,CAAC+C,CAAC,CAAC,KAAKA,CAAC,CAAC,EAAE;MACzE,CAAC,SAAS;QACR,IAAIhB,CAAC,EAAE,MAAM5B,CAAC;MAChB;IACF;IACA,OAAOU,CAAC;EACV;AACF;AAEA,SAASuC,gBAAgBA,CAAA,EAAG;EAC1B,MAAM,IAAIxB,SAAS,CAAC,2IAA2I,CAAC;AAClK;AAEA,SAASyB,cAAcA,CAAC7C,CAAC,EAAEJ,CAAC,EAAE;EAC5B,OAAOwC,eAAe,CAACpC,CAAC,CAAC,IAAIqC,qBAAqB,CAACrC,CAAC,EAAEJ,CAAC,CAAC,IAAIiB,2BAA2B,CAACb,CAAC,EAAEJ,CAAC,CAAC,IAAIgD,gBAAgB,CAAC,CAAC;AACrH;AAEA,IAAIE,OAAO,GAAG;EACZC,IAAI,EAAE,yBAAyB;EAC/BC,YAAY,EAAE;IACZD,IAAI,EAAE,SAASA,IAAIA,CAACE,IAAI,EAAE;MACxB,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;MAC5B,OAAO/D,UAAU,CAAC,iBAAiB,EAAE;QACnC,wBAAwB,EAAE+D;MAC5B,CAAC,CAAC;IACJ,CAAC;IACDC,OAAO,EAAE,qBAAqB;IAC9BC,MAAM,EAAE,SAASA,MAAMA,CAACC,KAAK,EAAE;MAC7B,IAAIH,QAAQ,GAAGG,KAAK,CAACH,QAAQ;QAC3BI,UAAU,GAAGD,KAAK,CAACC,UAAU;QAC7BC,GAAG,GAAGF,KAAK,CAACE,GAAG;MACjB,OAAOpE,UAAU,CAAC,oBAAoB,EAAE;QACtC,aAAa,EAAE+D,QAAQ;QACvB,YAAY,EAAEI,UAAU,CAACC,GAAG,EAAE,UAAU;MAC1C,CAAC,CAAC;IACJ,CAAC;IACDC,YAAY,EAAE,yBAAyB;IACvCC,UAAU,EAAE,yBAAyB;IACrCC,WAAW,EAAE,yBAAyB;IACtCC,iBAAiB,EAAE,sBAAsB;IACzCC,UAAU,EAAE;EACd;AACF,CAAC;AACD,IAAIC,MAAM,GAAG,gZAAgZ;AAC7Z,IAAIC,aAAa,GAAGnF,aAAa,CAACoF,MAAM,CAAC;EACvCC,YAAY,EAAE;IACZC,MAAM,EAAE,WAAW;IACnBC,EAAE,EAAE,IAAI;IACRC,WAAW,EAAE,IAAI;IACjBC,SAAS,EAAE,IAAI;IACfC,KAAK,EAAE,IAAI;IACXC,QAAQ,EAAE,KAAK;IACfC,UAAU,EAAE,IAAI;IAChBC,YAAY,EAAE,IAAI;IAClBC,iBAAiB,EAAE,IAAI;IACvBC,SAAS,EAAE,IAAI;IACfC,UAAU,EAAE,IAAI;IAChBC,WAAW,EAAE,IAAI;IACjBC,QAAQ,EAAEC;EACZ,CAAC;EACDC,GAAG,EAAE;IACHjC,OAAO,EAAEA,OAAO;IAChBe,MAAM,EAAEA;EACV;AACF,CAAC,CAAC;AACF,IAAImB,gBAAgB,GAAGrG,aAAa,CAACoF,MAAM,CAAC;EAC1CC,YAAY,EAAE;IACZC,MAAM,EAAE,cAAc;IACtBG,SAAS,EAAE,IAAI;IACfa,gBAAgB,EAAE,IAAI;IACtBC,YAAY,EAAE,IAAI;IAClBC,QAAQ,EAAE,KAAK;IACf/B,MAAM,EAAE,IAAI;IACZgC,eAAe,EAAE,IAAI;IACrBC,WAAW,EAAE,IAAI;IACjBC,cAAc,EAAE,IAAI;IACpBjB,KAAK,EAAE,IAAI;IACXkB,QAAQ,EAAE,CAAC;IACXV,QAAQ,EAAEC;EACZ,CAAC;EACDU,QAAQ,EAAE,SAASA,QAAQA,CAACjC,GAAG,EAAEtC,IAAI,EAAE;IACrC,OAAO/B,WAAW,CAACuG,gBAAgB,CAAClC,GAAG,EAAEtC,IAAI,EAAE+D,gBAAgB,CAAChB,YAAY,CAAC;EAC/E,CAAC;EACD0B,SAAS,EAAE,SAASA,SAASA,CAACnC,GAAG,EAAE;IACjC,OAAOrE,WAAW,CAACyG,iBAAiB,CAACpC,GAAG,EAAEyB,gBAAgB,CAAChB,YAAY,CAAC;EAC1E,CAAC;EACD4B,cAAc,EAAE,SAASA,cAAcA,CAACrC,GAAG,EAAE;IAC3C,OAAOrE,WAAW,CAAC2G,qBAAqB,CAACtC,GAAG,EAAEyB,gBAAgB,CAAChB,YAAY,CAAC;EAC9E;AACF,CAAC,CAAC;AAEF,SAAS8B,OAAOA,CAAClG,CAAC,EAAEI,CAAC,EAAE;EAAE,IAAID,CAAC,GAAGP,MAAM,CAACuG,IAAI,CAACnG,CAAC,CAAC;EAAE,IAAIJ,MAAM,CAACwG,qBAAqB,EAAE;IAAE,IAAIzE,CAAC,GAAG/B,MAAM,CAACwG,qBAAqB,CAACpG,CAAC,CAAC;IAAEI,CAAC,KAAKuB,CAAC,GAAGA,CAAC,CAAC0E,MAAM,CAAC,UAAUjG,CAAC,EAAE;MAAE,OAAOR,MAAM,CAAC0G,wBAAwB,CAACtG,CAAC,EAAEI,CAAC,CAAC,CAACiC,UAAU;IAAE,CAAC,CAAC,CAAC,EAAElC,CAAC,CAAC4C,IAAI,CAACxC,KAAK,CAACJ,CAAC,EAAEwB,CAAC,CAAC;EAAE;EAAE,OAAOxB,CAAC;AAAE;AAC9P,SAASoG,aAAaA,CAACvG,CAAC,EAAE;EAAE,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,SAAS,CAACC,MAAM,EAAEE,CAAC,EAAE,EAAE;IAAE,IAAID,CAAC,GAAG,IAAI,IAAIF,SAAS,CAACG,CAAC,CAAC,GAAGH,SAAS,CAACG,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAG8F,OAAO,CAACtG,MAAM,CAACO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACqG,OAAO,CAAC,UAAUpG,CAAC,EAAE;MAAE8B,eAAe,CAAClC,CAAC,EAAEI,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGR,MAAM,CAAC6G,yBAAyB,GAAG7G,MAAM,CAAC8G,gBAAgB,CAAC1G,CAAC,EAAEJ,MAAM,CAAC6G,yBAAyB,CAACtG,CAAC,CAAC,CAAC,GAAG+F,OAAO,CAACtG,MAAM,CAACO,CAAC,CAAC,CAAC,CAACqG,OAAO,CAAC,UAAUpG,CAAC,EAAE;MAAER,MAAM,CAACuC,cAAc,CAACnC,CAAC,EAAEI,CAAC,EAAER,MAAM,CAAC0G,wBAAwB,CAACnG,CAAC,EAAEC,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOJ,CAAC;AAAE;AACtb,IAAI2G,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAG,CAAC,CAAC;AAC7C,IAAIC,SAAS,GAAG,aAAa/H,KAAK,CAACgI,UAAU,CAAC,UAAUC,OAAO,EAAEC,GAAG,EAAE;EACpE,IAAIC,UAAU,GAAG9H,aAAa,CAAC,CAAC;EAChC,IAAI+H,OAAO,GAAGpI,KAAK,CAACqI,UAAU,CAACpI,iBAAiB,CAAC;EACjD,IAAIqI,KAAK,GAAGjD,aAAa,CAACkD,QAAQ,CAACN,OAAO,EAAEG,OAAO,CAAC;EACpD,IAAII,eAAe,GAAGxI,KAAK,CAACyI,QAAQ,CAACH,KAAK,CAAC7C,EAAE,CAAC;IAC5CiD,gBAAgB,GAAGtE,cAAc,CAACoE,eAAe,EAAE,CAAC,CAAC;IACrDG,OAAO,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC7BE,UAAU,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAClC,IAAIG,gBAAgB,GAAG7I,KAAK,CAACyI,QAAQ,CAACH,KAAK,CAAC5C,WAAW,CAAC;IACtDoD,gBAAgB,GAAG1E,cAAc,CAACyE,gBAAgB,EAAE,CAAC,CAAC;IACtDE,gBAAgB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACtCE,mBAAmB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAC3C,IAAIG,UAAU,GAAGjJ,KAAK,CAACkJ,MAAM,CAAC,IAAI,CAAC;EACnC,IAAIxD,WAAW,GAAG4C,KAAK,CAACnC,WAAW,GAAGmC,KAAK,CAAC5C,WAAW,GAAGqD,gBAAgB;EAC1E,IAAII,KAAK,GAAGnJ,KAAK,CAACoJ,QAAQ,CAACD,KAAK,CAACb,KAAK,CAAClC,QAAQ,CAAC;EAChD,IAAIiD,QAAQ,GAAG;IACbf,KAAK,EAAEA,KAAK;IACZgB,KAAK,EAAE;MACL7D,EAAE,EAAEkD,OAAO;MACXjD,WAAW,EAAEqD;IACf;EACF,CAAC;EACD,IAAIQ,qBAAqB,GAAGlE,aAAa,CAACmE,WAAW,CAAC9B,aAAa,CAAC,CAAC,CAAC,EAAE2B,QAAQ,CAAC,CAAC;IAChFI,GAAG,GAAGF,qBAAqB,CAACE,GAAG;IAC/BC,IAAI,GAAGH,qBAAqB,CAACG,IAAI;IACjCC,EAAE,GAAGJ,qBAAqB,CAACI,EAAE;IAC7BC,UAAU,GAAGL,qBAAqB,CAACK,UAAU;EAC/CzJ,cAAc,CAACkF,aAAa,CAACiB,GAAG,CAAClB,MAAM,EAAEwE,UAAU,EAAE;IACnDpH,IAAI,EAAE;EACR,CAAC,CAAC;EACF,IAAIqC,UAAU,GAAG,SAASA,UAAUA,CAACC,GAAG,EAAEtC,IAAI,EAAE;IAC9C,OAAO+D,gBAAgB,CAACQ,QAAQ,CAACjC,GAAG,EAAEtC,IAAI,CAAC;EAC7C,CAAC;EACD,IAAIqH,QAAQ,GAAG,SAASA,QAAQA,CAAC/E,GAAG,EAAEgF,GAAG,EAAEC,KAAK,EAAE;IAChD,IAAIC,WAAW,GAAG;MAChB;MACAC,MAAM,EAAEZ,QAAQ;MAChBjB,OAAO,EAAE;QACP2B,KAAK,EAAEA,KAAK;QACZZ,KAAK,EAAEA,KAAK;QACZe,KAAK,EAAEH,KAAK,KAAK,CAAC;QAClBI,IAAI,EAAEJ,KAAK,KAAKZ,KAAK,GAAG,CAAC;QACzB1E,QAAQ,EAAE2F,UAAU,CAACL,KAAK,CAAC;QAC3BrD,QAAQ,EAAE7B,UAAU,CAACC,GAAG,EAAE,UAAU;MACtC;IACF,CAAC;IACD,OAAOqD,UAAU,CAACsB,GAAG,CAAC,MAAM,CAACY,MAAM,CAACP,GAAG,CAAC,EAAE;MACxChF,GAAG,EAAEkF;IACP,CAAC,CAAC,EAAEP,GAAG,CAAC,eAAe,CAACY,MAAM,CAACP,GAAG,CAAC,EAAE;MACnCvF,YAAY,EAAEyF;IAChB,CAAC,CAAC,EAAEP,GAAG,CAAC,eAAe,CAACY,MAAM,CAACP,GAAG,CAAC,EAAEE,WAAW,CAAC,EAAEN,IAAI,CAAC7E,UAAU,CAACC,GAAG,EAAE,IAAI,CAAC,EAAEgF,GAAG,EAAEE,WAAW,CAAC,CAAC;EACnG,CAAC;EACD,IAAIM,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,KAAK,EAAEzF,GAAG,EAAEiF,KAAK,EAAE;IAClES,iBAAiB,CAACD,KAAK,EAAEzF,GAAG,EAAEiF,KAAK,CAAC;EACtC,CAAC;EACD,IAAIS,iBAAiB,GAAG,SAASA,iBAAiBA,CAACD,KAAK,EAAEzF,GAAG,EAAEiF,KAAK,EAAE;IACpE,IAAI,CAAClF,UAAU,CAACC,GAAG,EAAE,UAAU,CAAC,EAAE;MAChC,IAAIL,QAAQ,GAAG2F,UAAU,CAACL,KAAK,CAAC;MAChC,IAAIU,cAAc,GAAG,IAAI;MACzB,IAAInC,KAAK,CAACzC,QAAQ,EAAE;QAClB,IAAI6E,OAAO,GAAGhF,WAAW,IAAI,EAAE;QAC/B+E,cAAc,GAAGhG,QAAQ,GAAGiG,OAAO,CAAClD,MAAM,CAAC,UAAUvE,CAAC,EAAE;UACtD,OAAOA,CAAC,KAAK8G,KAAK;QACpB,CAAC,CAAC,GAAG,EAAE,CAACM,MAAM,CAACzH,kBAAkB,CAAC8H,OAAO,CAAC,EAAE,CAACX,KAAK,CAAC,CAAC;MACtD,CAAC,MAAM;QACLU,cAAc,GAAGhG,QAAQ,GAAG,IAAI,GAAGsF,KAAK;MAC1C;MACA,IAAIY,QAAQ,GAAGlG,QAAQ,GAAG6D,KAAK,CAACpC,UAAU,GAAGoC,KAAK,CAACrC,SAAS;MAC5D0E,QAAQ,IAAIA,QAAQ,CAAC;QACnBC,aAAa,EAAEL,KAAK;QACpBR,KAAK,EAAEA;MACT,CAAC,CAAC;MACF,IAAIzB,KAAK,CAACnC,WAAW,EAAE;QACrBmC,KAAK,CAACnC,WAAW,CAAC;UAChByE,aAAa,EAAEL,KAAK;UACpBR,KAAK,EAAEU;QACT,CAAC,CAAC;MACJ,CAAC,MAAM;QACLzB,mBAAmB,CAACyB,cAAc,CAAC;MACrC;IACF;IACAF,KAAK,CAACM,cAAc,CAAC,CAAC;EACxB,CAAC;EACD,IAAIC,kBAAkB,GAAG,SAASA,kBAAkBA,CAACP,KAAK,EAAEzF,GAAG,EAAEiF,KAAK,EAAE;IACtE,QAAQQ,KAAK,CAACQ,IAAI;MAChB,KAAK,WAAW;QACdC,iBAAiB,CAACT,KAAK,CAAC;QACxB;MACF,KAAK,SAAS;QACZU,eAAe,CAACV,KAAK,CAAC;QACtB;MACF,KAAK,MAAM;QACTW,YAAY,CAACX,KAAK,CAAC;QACnB;MACF,KAAK,KAAK;QACRY,WAAW,CAACZ,KAAK,CAAC;QAClB;MACF,KAAK,OAAO;MACZ,KAAK,aAAa;MAClB,KAAK,OAAO;QACVa,aAAa,CAACb,KAAK,EAAEzF,GAAG,EAAEiF,KAAK,CAAC;QAChC;IACJ;EACF,CAAC;EACD,IAAIiB,iBAAiB,GAAG,SAASA,iBAAiBA,CAACT,KAAK,EAAE;IACxD,IAAIc,gBAAgB,GAAGC,qBAAqB,CAACf,KAAK,CAACgB,MAAM,CAACC,aAAa,CAACA,aAAa,CAAC;IACtFH,gBAAgB,GAAGI,gBAAgB,CAACJ,gBAAgB,CAAC,GAAGH,YAAY,CAACX,KAAK,CAAC;IAC3EA,KAAK,CAACM,cAAc,CAAC,CAAC;EACxB,CAAC;EACD,IAAII,eAAe,GAAG,SAASA,eAAeA,CAACV,KAAK,EAAE;IACpD,IAAImB,gBAAgB,GAAGC,qBAAqB,CAACpB,KAAK,CAACgB,MAAM,CAACC,aAAa,CAACA,aAAa,CAAC;IACtFE,gBAAgB,GAAGD,gBAAgB,CAACC,gBAAgB,CAAC,GAAGP,WAAW,CAACZ,KAAK,CAAC;IAC1EA,KAAK,CAACM,cAAc,CAAC,CAAC;EACxB,CAAC;EACD,IAAIK,YAAY,GAAG,SAASA,YAAYA,CAACX,KAAK,EAAE;IAC9C,IAAIqB,iBAAiB,GAAGC,qBAAqB,CAAC,CAAC;IAC/CJ,gBAAgB,CAACG,iBAAiB,CAAC;IACnCrB,KAAK,CAACM,cAAc,CAAC,CAAC;EACxB,CAAC;EACD,IAAIM,WAAW,GAAG,SAASA,WAAWA,CAACZ,KAAK,EAAE;IAC5C,IAAIuB,gBAAgB,GAAGC,oBAAoB,CAAC,CAAC;IAC7CN,gBAAgB,CAACK,gBAAgB,CAAC;IAClCvB,KAAK,CAACM,cAAc,CAAC,CAAC;EACxB,CAAC;EACD,IAAIO,aAAa,GAAG,SAASA,aAAaA,CAACb,KAAK,EAAEzF,GAAG,EAAEiF,KAAK,EAAE;IAC5DS,iBAAiB,CAACD,KAAK,EAAEzF,GAAG,EAAEiF,KAAK,CAAC;IACpCQ,KAAK,CAACM,cAAc,CAAC,CAAC;EACxB,CAAC;EACD,IAAIS,qBAAqB,GAAG,SAASU,oBAAoBA,CAACC,UAAU,EAAE;IACpE,IAAIC,SAAS,GAAG9K,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKiF,SAAS,GAAGjF,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;IACzF,IAAI+K,cAAc,GAAGD,SAAS,GAAGD,UAAU,GAAGA,UAAU,CAACG,kBAAkB;IAC3E,IAAIC,aAAa,GAAGxL,UAAU,CAACyL,UAAU,CAACH,cAAc,EAAE,4BAA4B,CAAC;IACvF,OAAOE,aAAa,GAAGxL,UAAU,CAAC0L,YAAY,CAACF,aAAa,EAAE,iBAAiB,CAAC,GAAGf,qBAAqB,CAACe,aAAa,CAACb,aAAa,CAAC,GAAG3K,UAAU,CAACyL,UAAU,CAACD,aAAa,EAAE,kCAAkC,CAAC,GAAG,IAAI;EACzN,CAAC;EACD,IAAIV,qBAAqB,GAAG,SAASa,oBAAoBA,CAACP,UAAU,EAAE;IACpE,IAAIC,SAAS,GAAG9K,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKiF,SAAS,GAAGjF,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;IACzF,IAAIqL,cAAc,GAAGP,SAAS,GAAGD,UAAU,GAAGA,UAAU,CAACS,sBAAsB;IAC/E,IAAIL,aAAa,GAAGxL,UAAU,CAACyL,UAAU,CAACG,cAAc,EAAE,4BAA4B,CAAC;IACvF,OAAOJ,aAAa,GAAGxL,UAAU,CAAC0L,YAAY,CAACF,aAAa,EAAE,iBAAiB,CAAC,GAAGV,qBAAqB,CAACU,aAAa,CAACb,aAAa,CAAC,GAAG3K,UAAU,CAACyL,UAAU,CAACD,aAAa,EAAE,kCAAkC,CAAC,GAAG,IAAI;EACzN,CAAC;EACD,IAAIR,qBAAqB,GAAG,SAASA,qBAAqBA,CAAA,EAAG;IAC3D,OAAOP,qBAAqB,CAACrC,UAAU,CAAC0D,OAAO,CAACC,iBAAiB,EAAE,IAAI,CAAC;EAC1E,CAAC;EACD,IAAIb,oBAAoB,GAAG,SAASA,oBAAoBA,CAAA,EAAG;IACzD,OAAOJ,qBAAqB,CAAC1C,UAAU,CAAC0D,OAAO,CAACE,gBAAgB,EAAE,IAAI,CAAC;EACzE,CAAC;EACD,IAAIpB,gBAAgB,GAAG,SAASA,gBAAgBA,CAACqB,OAAO,EAAE;IACxD,IAAIA,OAAO,EAAE;MACXjM,UAAU,CAACkM,KAAK,CAACD,OAAO,CAAC;IAC3B;EACF,CAAC;EACD,IAAI1C,UAAU,GAAG,SAASA,UAAUA,CAACL,KAAK,EAAE;IAC1C,OAAOzB,KAAK,CAACzC,QAAQ,IAAIhE,KAAK,CAACE,OAAO,CAAC2D,WAAW,CAAC,GAAGA,WAAW,IAAIA,WAAW,CAACsH,IAAI,CAAC,UAAU/J,CAAC,EAAE;MACjG,OAAOA,CAAC,KAAK8G,KAAK;IACpB,CAAC,CAAC,GAAGrE,WAAW,KAAKqE,KAAK;EAC5B,CAAC;EACD/J,KAAK,CAACiN,mBAAmB,CAAC/E,GAAG,EAAE,YAAY;IACzC,OAAO;MACLI,KAAK,EAAEA,KAAK;MACZ4E,UAAU,EAAE,SAASA,UAAUA,CAAA,EAAG;QAChC,OAAOjE,UAAU,CAAC0D,OAAO;MAC3B;IACF,CAAC;EACH,CAAC,CAAC;EACFrM,cAAc,CAAC,YAAY;IACzB,IAAI,CAACqI,OAAO,EAAE;MACZC,UAAU,CAACjI,iBAAiB,CAAC,CAAC,CAAC;IACjC;EACF,CAAC,CAAC;EACF,IAAI,CAACgI,OAAO,EAAE;IACZ,OAAO,IAAI;EACb;EACA,IAAIwE,eAAe,GAAG,SAASA,eAAeA,CAACrI,GAAG,EAAEL,QAAQ,EAAEsF,KAAK,EAAE;IACnE,IAAInE,KAAK,GAAG8B,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE7C,UAAU,CAACC,GAAG,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAED,UAAU,CAACC,GAAG,EAAE,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;IAClH,IAAIsI,QAAQ,GAAGzE,OAAO,GAAG,UAAU,GAAGoB,KAAK;IAC3C,IAAIsD,YAAY,GAAG1E,OAAO,GAAG,WAAW,GAAGoB,KAAK;IAChD,IAAIjD,QAAQ,GAAGjC,UAAU,CAACC,GAAG,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC,GAAGD,UAAU,CAACC,GAAG,EAAE,UAAU,CAAC;IAC7E,IAAIwI,gBAAgB,GAAGnF,UAAU,CAAC;MAChCxC,SAAS,EAAEgE,EAAE,CAAC,0BAA0B;IAC1C,CAAC,EAAEE,QAAQ,CAAC/E,GAAG,EAAE,aAAa,EAAEiF,KAAK,CAAC,CAAC;IACvC,IAAIwD,SAAS,GAAGhH,gBAAgB,CAACU,SAAS,CAACnC,GAAG,CAAC;IAC/C,IAAIH,MAAM,GAAGE,UAAU,CAACC,GAAG,EAAE,gBAAgB,CAAC,GAAGrE,WAAW,CAAC+M,aAAa,CAAC3I,UAAU,CAACC,GAAG,EAAE,gBAAgB,CAAC,EAAEyI,SAAS,CAAC,GAAG,aAAavN,KAAK,CAACyN,aAAa,CAAC,MAAM,EAAEH,gBAAgB,EAAE7M,WAAW,CAAC+M,aAAa,CAAC3I,UAAU,CAACC,GAAG,EAAE,QAAQ,CAAC,EAAEyI,SAAS,CAAC,CAAC;IACtP,IAAIG,eAAe,GAAGvF,UAAU,CAAC;MAC/B,aAAa,EAAE,MAAM;MACrBxC,SAAS,EAAEgE,EAAE,CAAC,yBAAyB;IACzC,CAAC,EAAEE,QAAQ,CAAC/E,GAAG,EAAE,YAAY,EAAEiF,KAAK,CAAC,CAAC;IACtC,IAAI4D,IAAI,GAAGlJ,QAAQ,GAAG6D,KAAK,CAACvC,YAAY,IAAI,aAAa/F,KAAK,CAACyN,aAAa,CAAClN,eAAe,EAAEmN,eAAe,CAAC,GAAGpF,KAAK,CAACxC,UAAU,IAAI,aAAa9F,KAAK,CAACyN,aAAa,CAACjN,gBAAgB,EAAEkN,eAAe,CAAC;IACxM,IAAIE,UAAU,GAAGhN,SAAS,CAACiN,UAAU,CAACF,IAAI,EAAEjG,aAAa,CAAC,CAAC,CAAC,EAAEgG,eAAe,CAAC,EAAE;MAC9EpF,KAAK,EAAEA,KAAK;MACZ7D,QAAQ,EAAEA;IACZ,CAAC,CAAC;IACF,IAAIqJ,WAAW,GAAG3F,UAAU,CAAC;MAC3BxC,SAAS,EAAEjF,UAAU,CAACmE,UAAU,CAACC,GAAG,EAAE,iBAAiB,CAAC,EAAED,UAAU,CAACC,GAAG,EAAE,WAAW,CAAC,EAAE6E,EAAE,CAAC,qBAAqB,EAAE;QAChHlF,QAAQ,EAAEA,QAAQ;QAClBI,UAAU,EAAEA,UAAU;QACtBC,GAAG,EAAEA;MACP,CAAC,CAAC,CAAC;MACHc,KAAK,EAAEA,KAAK;MACZ,kBAAkB,EAAEnB,QAAQ;MAC5B,iBAAiB,EAAEI,UAAU,CAACC,GAAG,EAAE,UAAU;IAC/C,CAAC,EAAE+E,QAAQ,CAAC/E,GAAG,EAAE,QAAQ,EAAEiF,KAAK,CAAC,CAAC;IAClC,IAAIgE,iBAAiB,GAAG5F,UAAU,CAAC;MACjC1C,EAAE,EAAE2H,QAAQ;MACZY,IAAI,EAAE,GAAG,GAAGX,YAAY;MACxB1H,SAAS,EAAEgE,EAAE,CAAC,2BAA2B,CAAC;MAC1CsE,IAAI,EAAE,QAAQ;MACdnH,QAAQ,EAAEA,QAAQ;MAClBoH,OAAO,EAAE,SAASA,OAAOA,CAAC/M,CAAC,EAAE;QAC3B,OAAOmJ,gBAAgB,CAACnJ,CAAC,EAAE2D,GAAG,EAAEiF,KAAK,CAAC;MACxC,CAAC;MACDoE,SAAS,EAAE,SAASA,SAASA,CAAChN,CAAC,EAAE;QAC/B,OAAO2J,kBAAkB,CAAC3J,CAAC,EAAE2D,GAAG,EAAEiF,KAAK,CAAC;MAC1C,CAAC;MACD,eAAe,EAAElF,UAAU,CAACC,GAAG,EAAE,UAAU,CAAC;MAC5C,eAAe,EAAEuI,YAAY;MAC7B,eAAe,EAAE5I;IACnB,CAAC,EAAEoF,QAAQ,CAAC/E,GAAG,EAAE,cAAc,EAAEiF,KAAK,CAAC,CAAC;IACxC,OAAO,aAAa/J,KAAK,CAACyN,aAAa,CAAC,KAAK,EAAEK,WAAW,EAAE,aAAa9N,KAAK,CAACyN,aAAa,CAAC,GAAG,EAAEM,iBAAiB,EAAEH,UAAU,EAAEjJ,MAAM,CAAC,CAAC;EAC3I,CAAC;EACD,IAAIyJ,gBAAgB,GAAG,SAASA,gBAAgBA,CAACtJ,GAAG,EAAEL,QAAQ,EAAEsF,KAAK,EAAE;IACrE,IAAInE,KAAK,GAAG8B,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE7C,UAAU,CAACC,GAAG,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAED,UAAU,CAACC,GAAG,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;IACnH,IAAIuJ,SAAS,GAAG1F,OAAO,GAAG,WAAW,GAAGoB,KAAK;IAC7C,IAAIuE,cAAc,GAAG3F,OAAO,GAAG,UAAU,GAAGoB,KAAK;IACjD,IAAIwE,UAAU,GAAG,aAAavO,KAAK,CAACwO,SAAS,CAAC,CAAC;IAC/C,IAAIC,sBAAsB,GAAGtG,UAAU,CAAC;MACtC1C,EAAE,EAAE4I,SAAS;MACbnG,GAAG,EAAEqG,UAAU;MACf5I,SAAS,EAAEjF,UAAU,CAACmE,UAAU,CAACC,GAAG,EAAE,kBAAkB,CAAC,EAAED,UAAU,CAACC,GAAG,EAAE,WAAW,CAAC,EAAE6E,EAAE,CAAC,gCAAgC,CAAC,CAAC;MAC9H/D,KAAK,EAAEA,KAAK;MACZqI,IAAI,EAAE,QAAQ;MACd,iBAAiB,EAAEK;IACrB,CAAC,EAAEzE,QAAQ,CAAC/E,GAAG,EAAE,mBAAmB,EAAEiF,KAAK,CAAC,CAAC;IAC7C,IAAI2E,YAAY,GAAGvG,UAAU,CAAC;MAC5BxC,SAAS,EAAEgE,EAAE,CAAC,sBAAsB;IACtC,CAAC,EAAEE,QAAQ,CAAC/E,GAAG,EAAE,SAAS,EAAEiF,KAAK,CAAC,CAAC;IACnC,IAAI4E,eAAe,GAAGxG,UAAU,CAAC;MAC/BzH,UAAU,EAAEiJ,EAAE,CAAC,yBAAyB,CAAC;MACzCiF,OAAO,EAAE;QACPC,KAAK,EAAE,IAAI;QACXC,IAAI,EAAE;MACR,CAAC;MACD,IAAI,EAAErK,QAAQ;MACdsK,aAAa,EAAE,IAAI;MACnBC,OAAO,EAAE1G,KAAK,CAACtC;IACjB,CAAC,EAAE6D,QAAQ,CAAC/E,GAAG,EAAE,YAAY,EAAEiF,KAAK,CAAC,CAAC;IACtC,OAAO,aAAa/J,KAAK,CAACyN,aAAa,CAACrN,aAAa,EAAEU,QAAQ,CAAC;MAC9DmO,OAAO,EAAEV;IACX,CAAC,EAAEI,eAAe,CAAC,EAAE,aAAa3O,KAAK,CAACyN,aAAa,CAAC,KAAK,EAAEgB,sBAAsB,EAAE,aAAazO,KAAK,CAACyN,aAAa,CAAC,KAAK,EAAEiB,YAAY,EAAE7J,UAAU,CAACC,GAAG,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC;EAC3K,CAAC;EACD,IAAIoK,SAAS,GAAG,SAASA,SAASA,CAACpK,GAAG,EAAEiF,KAAK,EAAE;IAC7C,IAAItJ,WAAW,CAAC0O,YAAY,CAACrK,GAAG,EAAE,cAAc,CAAC,EAAE;MACjD,IAAIgF,GAAG,GAAGnB,OAAO,GAAG,GAAG,GAAGoB,KAAK;MAC/B,IAAItF,QAAQ,GAAG2F,UAAU,CAACL,KAAK,CAAC;MAChC,IAAIqF,SAAS,GAAGjC,eAAe,CAACrI,GAAG,EAAEL,QAAQ,EAAEsF,KAAK,CAAC;MACrD,IAAIsF,UAAU,GAAGjB,gBAAgB,CAACtJ,GAAG,EAAEL,QAAQ,EAAEsF,KAAK,CAAC;MACvD,IAAIuF,UAAU,GAAGnH,UAAU,CAAC;QAC1B2B,GAAG,EAAEA,GAAG;QACRnE,SAAS,EAAEgE,EAAE,CAAC,mBAAmB,EAAE;UACjClF,QAAQ,EAAEA;QACZ,CAAC;MACH,CAAC,EAAE8B,gBAAgB,CAACY,cAAc,CAACrC,GAAG,CAAC,EAAE+E,QAAQ,CAAC/E,GAAG,EAAE,MAAM,EAAEiF,KAAK,CAAC,CAAC;MACtE,OAAO,aAAa/J,KAAK,CAACyN,aAAa,CAAC,KAAK,EAAE6B,UAAU,EAAEF,SAAS,EAAEC,UAAU,CAAC;IACnF;IACA,OAAO,IAAI;EACb,CAAC;EACD,IAAIE,UAAU,GAAG,SAASA,UAAUA,CAAA,EAAG;IACrC,OAAOvP,KAAK,CAACoJ,QAAQ,CAACoG,GAAG,CAAClH,KAAK,CAAClC,QAAQ,EAAE8I,SAAS,CAAC;EACtD,CAAC;EACD,IAAIO,IAAI,GAAGF,UAAU,CAAC,CAAC;EACvB,IAAIG,SAAS,GAAGvH,UAAU,CAAC;IACzBxC,SAAS,EAAEjF,UAAU,CAAC4H,KAAK,CAAC3C,SAAS,EAAEgE,EAAE,CAAC,MAAM,CAAC,CAAC;IAClD/D,KAAK,EAAE0C,KAAK,CAAC1C;EACf,CAAC,EAAEP,aAAa,CAACsK,aAAa,CAACrH,KAAK,CAAC,EAAEmB,GAAG,CAAC,MAAM,CAAC,CAAC;EACnD,OAAO,aAAazJ,KAAK,CAACyN,aAAa,CAAC,KAAK,EAAE3M,QAAQ,CAAC;IACtD2E,EAAE,EAAEkD,OAAO;IACXT,GAAG,EAAEe;EACP,CAAC,EAAEyG,SAAS,CAAC,EAAED,IAAI,CAAC;AACtB,CAAC,CAAC;AACF3H,YAAY,CAAC8H,WAAW,GAAG,cAAc;AACzC7H,SAAS,CAAC6H,WAAW,GAAG,WAAW;AAEnC,SAAS7H,SAAS,EAAED,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}