{"ast": null, "code": "var _jsxFileName = \"C:\\\\git\\\\Partner-Site\\\\BdoPartner.Plans\\\\Development\\\\ReactClientApp\\\\src\\\\pages\\\\admin\\\\partnerAnnualPlansAdmin.jsx\",\n  _s = $RefreshSig$();\nimport React from \"react\";\nimport { Card } from \"primereact/card\";\nimport { Button } from \"primereact/button\";\nimport { useSearchParams } from \"react-router-dom\";\nimport PartnerAnnualPlansTable from \"../../components/admin/PartnerAnnualPlansTable\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const PartnerAnnualPlansAdmin = () => {\n  _s();\n  const [searchParams] = useSearchParams();\n  const year = searchParams.get('year');\n  const status = searchParams.get('status');\n  const serviceLine = searchParams.get('serviceLine');\n  const subServiceLine = searchParams.get('subServiceLine');\n  const handleBack = () => {\n    window.history.back();\n  };\n\n  // Set initial filters from URL parameters\n  const initialFilters = {\n    year: year ? parseInt(year) : new Date().getFullYear(),\n    status: status || null,\n    serviceLine: serviceLine || null,\n    subServiceLine: subServiceLine || null\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"banner\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"banner__site-title-area\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"page-title\",\n          children: \"Partner Annual Plans\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-content-wrapper\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"back-button-container\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          icon: \"pi pi-arrow-left\",\n          label: \"Back\",\n          className: \"p-button-outlined p-button-sm back-button\",\n          onClick: handleBack\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        children: /*#__PURE__*/_jsxDEV(PartnerAnnualPlansTable, {\n          onBack: handleBack,\n          initialPageSize: 10,\n          initialFilters: initialFilters\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 27,\n    columnNumber: 5\n  }, this);\n};\n_s(PartnerAnnualPlansAdmin, \"HWxNQEGJGSlsPJ3ubBB3081mtng=\", false, function () {\n  return [useSearchParams];\n});\n_c = PartnerAnnualPlansAdmin;\nexport default PartnerAnnualPlansAdmin;\nvar _c;\n$RefreshReg$(_c, \"PartnerAnnualPlansAdmin\");", "map": {"version": 3, "names": ["React", "Card", "<PERSON><PERSON>", "useSearchParams", "PartnerAnnualPlansTable", "jsxDEV", "_jsxDEV", "PartnerAnnualPlansAdmin", "_s", "searchParams", "year", "get", "status", "serviceLine", "subServiceLine", "handleBack", "window", "history", "back", "initialFilters", "parseInt", "Date", "getFullYear", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "icon", "label", "onClick", "onBack", "initialPageSize", "_c", "$RefreshReg$"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/pages/admin/partnerAnnualPlansAdmin.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Card } from \"primereact/card\";\r\nimport { Button } from \"primereact/button\";\r\nimport { useSearchParams } from \"react-router-dom\";\r\nimport PartnerAnnualPlansTable from \"../../components/admin/PartnerAnnualPlansTable\";\r\n\r\nexport const PartnerAnnualPlansAdmin = () => {\r\n  const [searchParams] = useSearchParams();\r\n  const year = searchParams.get('year');\r\n  const status = searchParams.get('status');\r\n  const serviceLine = searchParams.get('serviceLine');\r\n  const subServiceLine = searchParams.get('subServiceLine');\r\n\r\n  const handleBack = () => {\r\n    window.history.back();\r\n  };\r\n\r\n  // Set initial filters from URL parameters\r\n  const initialFilters = {\r\n    year: year ? parseInt(year) : new Date().getFullYear(),\r\n    status: status || null,\r\n    serviceLine: serviceLine || null,\r\n    subServiceLine: subServiceLine || null\r\n  };\r\n\r\n  return (\r\n    <div>\r\n      <div className=\"banner\">\r\n        <div className=\"banner__site-title-area\">\r\n          <div className=\"page-title\">Partner Annual Plans</div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"page-content-wrapper\">\r\n        <div className=\"back-button-container\">\r\n          <Button\r\n            icon=\"pi pi-arrow-left\"\r\n            label=\"Back\"\r\n            className=\"p-button-outlined p-button-sm back-button\"\r\n            onClick={handleBack}\r\n          />\r\n        </div>\r\n        <Card>\r\n          <PartnerAnnualPlansTable\r\n            onBack={handleBack}\r\n            initialPageSize={10}\r\n            initialFilters={initialFilters}\r\n          />\r\n        </Card>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PartnerAnnualPlansAdmin;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,iBAAiB;AACtC,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,eAAe,QAAQ,kBAAkB;AAClD,OAAOC,uBAAuB,MAAM,gDAAgD;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErF,OAAO,MAAMC,uBAAuB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3C,MAAM,CAACC,YAAY,CAAC,GAAGN,eAAe,CAAC,CAAC;EACxC,MAAMO,IAAI,GAAGD,YAAY,CAACE,GAAG,CAAC,MAAM,CAAC;EACrC,MAAMC,MAAM,GAAGH,YAAY,CAACE,GAAG,CAAC,QAAQ,CAAC;EACzC,MAAME,WAAW,GAAGJ,YAAY,CAACE,GAAG,CAAC,aAAa,CAAC;EACnD,MAAMG,cAAc,GAAGL,YAAY,CAACE,GAAG,CAAC,gBAAgB,CAAC;EAEzD,MAAMI,UAAU,GAAGA,CAAA,KAAM;IACvBC,MAAM,CAACC,OAAO,CAACC,IAAI,CAAC,CAAC;EACvB,CAAC;;EAED;EACA,MAAMC,cAAc,GAAG;IACrBT,IAAI,EAAEA,IAAI,GAAGU,QAAQ,CAACV,IAAI,CAAC,GAAG,IAAIW,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;IACtDV,MAAM,EAAEA,MAAM,IAAI,IAAI;IACtBC,WAAW,EAAEA,WAAW,IAAI,IAAI;IAChCC,cAAc,EAAEA,cAAc,IAAI;EACpC,CAAC;EAED,oBACER,OAAA;IAAAiB,QAAA,gBACEjB,OAAA;MAAKkB,SAAS,EAAC,QAAQ;MAAAD,QAAA,eACrBjB,OAAA;QAAKkB,SAAS,EAAC,yBAAyB;QAAAD,QAAA,eACtCjB,OAAA;UAAKkB,SAAS,EAAC,YAAY;UAAAD,QAAA,EAAC;QAAoB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENtB,OAAA;MAAKkB,SAAS,EAAC,sBAAsB;MAAAD,QAAA,gBACnCjB,OAAA;QAAKkB,SAAS,EAAC,uBAAuB;QAAAD,QAAA,eACpCjB,OAAA,CAACJ,MAAM;UACL2B,IAAI,EAAC,kBAAkB;UACvBC,KAAK,EAAC,MAAM;UACZN,SAAS,EAAC,2CAA2C;UACrDO,OAAO,EAAEhB;QAAW;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNtB,OAAA,CAACL,IAAI;QAAAsB,QAAA,eACHjB,OAAA,CAACF,uBAAuB;UACtB4B,MAAM,EAAEjB,UAAW;UACnBkB,eAAe,EAAE,EAAG;UACpBd,cAAc,EAAEA;QAAe;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpB,EAAA,CA9CWD,uBAAuB;EAAA,QACXJ,eAAe;AAAA;AAAA+B,EAAA,GAD3B3B,uBAAuB;AAgDpC,eAAeA,uBAAuB;AAAC,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}