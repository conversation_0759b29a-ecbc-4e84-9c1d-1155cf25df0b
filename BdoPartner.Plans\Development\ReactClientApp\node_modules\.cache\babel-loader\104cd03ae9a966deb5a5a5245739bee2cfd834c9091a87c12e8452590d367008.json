{"ast": null, "code": "var _jsxFileName = \"C:\\\\git\\\\Partner-Site\\\\BdoPartner.Plans\\\\Development\\\\ReactClientApp\\\\src\\\\core\\\\message\\\\components\\\\messageBox.jsx\";\nimport React, { Component } from \"react\";\nimport { messageService } from \"../messageService\";\nimport { Messages } from \"primereact/messages\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass MessageBox extends Component {\n  constructor(...args) {\n    super(...args);\n    this.timeout = 5000;\n    this.messages = [];\n    this.messageBoxTimmer = {};\n    /**\r\n     * Clean up the messages from collection, hide the messages in UI.\r\n     */\n    this.cleanupMessages = () => {\n      this.message = [];\n      if (this.msgs) this.msgs.clear();\n    };\n  }\n  componentDidMount() {\n    this.subscription = messageService.get().subscribe(message => {\n      if (message && message.modalType === \"snackbar\") {\n        if (message.content !== \"IsEmitNotify\") {\n          //\n          // Save the received message into temporary message collection.\n          //\n          this.messages.push(message);\n          if (message.isEmit) {\n            //\n            // Show the messages in the snackbars message boxes.\n            //\n            this.showMessages();\n          }\n        } else {\n          this.showMessages();\n        }\n      }\n    });\n  }\n  showMessages() {\n    if (this.messages.length > 0) {\n      let primengMessages = [];\n      this.messages.map(message => primengMessages.push({\n        severity: message.messageType,\n        sticky: true,\n        detail: message.content\n      }));\n      this.msgs.show(primengMessages);\n      this.messages = [];\n\n      //\n      // show the messages on messagebox for several seconds then close the messages boxes.\n      //\n      if (this.messageBoxTimmer) {\n        clearTimeout(this.messageBoxTimmer);\n      }\n      this.messageBoxTimmer = setTimeout(() => {\n        this.cleanupMessages();\n      }, this.timeout);\n    }\n  }\n  componentWillUnmount() {\n    // unsubscribe to ensure no memory leaks\n    this.subscription.unsubscribe();\n  }\n  render() {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(Messages, {\n        ref: el => this.msgs = el\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 7\n    }, this);\n  }\n}\nexport default MessageBox;", "map": {"version": 3, "names": ["React", "Component", "messageService", "Messages", "jsxDEV", "_jsxDEV", "MessageBox", "constructor", "args", "timeout", "messages", "messageBoxTimmer", "cleanupMessages", "message", "msgs", "clear", "componentDidMount", "subscription", "get", "subscribe", "modalType", "content", "push", "isEmit", "showMessages", "length", "primengMessages", "map", "severity", "messageType", "sticky", "detail", "show", "clearTimeout", "setTimeout", "componentWillUnmount", "unsubscribe", "render", "Fragment", "children", "ref", "el", "fileName", "_jsxFileName", "lineNumber", "columnNumber"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/core/message/components/messageBox.jsx"], "sourcesContent": ["import React, { Component } from \"react\";\r\nimport { messageService } from \"../messageService\";\r\nimport { Messages } from \"primereact/messages\";\r\n\r\n\r\nclass MessageBox extends Component {\r\n  timeout = 5000;\r\n  messages = [];\r\n\r\n  messageBoxTimmer = {} ;\r\n\r\n  componentDidMount() {\r\n    this.subscription = messageService.get().subscribe((message) => {\r\n      if (message && message.modalType === \"snackbar\") {\r\n        if (message.content !== \"IsEmitNotify\") {\r\n          //\r\n          // Save the received message into temporary message collection.\r\n          //\r\n          this.messages.push(message);\r\n          if (message.isEmit) {\r\n            //\r\n            // Show the messages in the snackbars message boxes.\r\n            //\r\n            this.showMessages();\r\n          }\r\n        } else {\r\n          this.showMessages();\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  showMessages() {\r\n    if (this.messages.length > 0) {\r\n      let primengMessages = [];\r\n      this.messages.map((message) => (\r\n        primengMessages.push({\r\n          severity: message.messageType,\r\n          sticky: true,\r\n          detail: message.content,\r\n        })\r\n      ));\r\n      this.msgs.show(primengMessages);\r\n      this.messages = [];\r\n\r\n      //\r\n      // show the messages on messagebox for several seconds then close the messages boxes.\r\n      //\r\n      if (this.messageBoxTimmer){\r\n          clearTimeout(this.messageBoxTimmer);\r\n      }\r\n\r\n      this.messageBoxTimmer = setTimeout(() => {\r\n        this.cleanupMessages();\r\n      }, this.timeout);\r\n    }\r\n  }\r\n\r\n  componentWillUnmount() {\r\n    // unsubscribe to ensure no memory leaks\r\n    this.subscription.unsubscribe();\r\n  }\r\n\r\n  /**\r\n   * Clean up the messages from collection, hide the messages in UI.\r\n   */\r\n  cleanupMessages = () => {\r\n    this.message = [];\r\n    if (this.msgs) this.msgs.clear();\r\n  };\r\n\r\n  render() {\r\n    return (\r\n      <React.Fragment>\r\n        <Messages ref={(el) => (this.msgs = el)}/>\r\n      </React.Fragment>\r\n    );\r\n  }\r\n}\r\n\r\nexport default MessageBox;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,QAAQ,QAAQ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAG/C,MAAMC,UAAU,SAASL,SAAS,CAAC;EAAAM,YAAA,GAAAC,IAAA;IAAA,SAAAA,IAAA;IAAA,KACjCC,OAAO,GAAG,IAAI;IAAA,KACdC,QAAQ,GAAG,EAAE;IAAA,KAEbC,gBAAgB,GAAG,CAAC,CAAC;IAsDrB;AACF;AACA;IAFE,KAGAC,eAAe,GAAG,MAAM;MACtB,IAAI,CAACC,OAAO,GAAG,EAAE;MACjB,IAAI,IAAI,CAACC,IAAI,EAAE,IAAI,CAACA,IAAI,CAACC,KAAK,CAAC,CAAC;IAClC,CAAC;EAAA;EA1DDC,iBAAiBA,CAAA,EAAG;IAClB,IAAI,CAACC,YAAY,GAAGf,cAAc,CAACgB,GAAG,CAAC,CAAC,CAACC,SAAS,CAAEN,OAAO,IAAK;MAC9D,IAAIA,OAAO,IAAIA,OAAO,CAACO,SAAS,KAAK,UAAU,EAAE;QAC/C,IAAIP,OAAO,CAACQ,OAAO,KAAK,cAAc,EAAE;UACtC;UACA;UACA;UACA,IAAI,CAACX,QAAQ,CAACY,IAAI,CAACT,OAAO,CAAC;UAC3B,IAAIA,OAAO,CAACU,MAAM,EAAE;YAClB;YACA;YACA;YACA,IAAI,CAACC,YAAY,CAAC,CAAC;UACrB;QACF,CAAC,MAAM;UACL,IAAI,CAACA,YAAY,CAAC,CAAC;QACrB;MACF;IACF,CAAC,CAAC;EACJ;EAEAA,YAAYA,CAAA,EAAG;IACb,IAAI,IAAI,CAACd,QAAQ,CAACe,MAAM,GAAG,CAAC,EAAE;MAC5B,IAAIC,eAAe,GAAG,EAAE;MACxB,IAAI,CAAChB,QAAQ,CAACiB,GAAG,CAAEd,OAAO,IACxBa,eAAe,CAACJ,IAAI,CAAC;QACnBM,QAAQ,EAAEf,OAAO,CAACgB,WAAW;QAC7BC,MAAM,EAAE,IAAI;QACZC,MAAM,EAAElB,OAAO,CAACQ;MAClB,CAAC,CACF,CAAC;MACF,IAAI,CAACP,IAAI,CAACkB,IAAI,CAACN,eAAe,CAAC;MAC/B,IAAI,CAAChB,QAAQ,GAAG,EAAE;;MAElB;MACA;MACA;MACA,IAAI,IAAI,CAACC,gBAAgB,EAAC;QACtBsB,YAAY,CAAC,IAAI,CAACtB,gBAAgB,CAAC;MACvC;MAEA,IAAI,CAACA,gBAAgB,GAAGuB,UAAU,CAAC,MAAM;QACvC,IAAI,CAACtB,eAAe,CAAC,CAAC;MACxB,CAAC,EAAE,IAAI,CAACH,OAAO,CAAC;IAClB;EACF;EAEA0B,oBAAoBA,CAAA,EAAG;IACrB;IACA,IAAI,CAAClB,YAAY,CAACmB,WAAW,CAAC,CAAC;EACjC;EAUAC,MAAMA,CAAA,EAAG;IACP,oBACEhC,OAAA,CAACL,KAAK,CAACsC,QAAQ;MAAAC,QAAA,eACblC,OAAA,CAACF,QAAQ;QAACqC,GAAG,EAAGC,EAAE,IAAM,IAAI,CAAC3B,IAAI,GAAG2B;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CAAC;EAErB;AACF;AAEA,eAAevC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}