{"ast": null, "code": "import { setLicenseKey } from 'survey-core';\n\n/**\r\n * Configure Survey.js commercial license key\r\n * This should be called before initializing any Survey.js components\r\n */\nexport const configureSurveyJSLicense = () => {\n  const licenseKey = process.env.REACT_APP_SURVEYJS_LICENSE_KEY;\n  if (licenseKey && licenseKey !== 'YOUR_LICENSE_KEY_HERE') {\n    try {\n      setLicenseKey(licenseKey);\n      console.log('Survey.js commercial license applied successfully');\n    } catch (error) {\n      console.error('Failed to apply Survey.js license key:', error);\n    }\n  } else {\n    console.warn('Survey.js license key not configured. Please set REACT_APP_SURVEYJS_LICENSE_KEY in your environment file.');\n  }\n};", "map": {"version": 3, "names": ["setLicenseKey", "configureSurveyJSLicense", "licenseKey", "process", "env", "REACT_APP_SURVEYJS_LICENSE_KEY", "console", "log", "error", "warn"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/core/surveyjs/licenseConfig.js"], "sourcesContent": ["import { setLicenseKey } from 'survey-core';\r\n\r\n/**\r\n * Configure Survey.js commercial license key\r\n * This should be called before initializing any Survey.js components\r\n */\r\nexport const configureSurveyJSLicense = () => {\r\n  const licenseKey = process.env.REACT_APP_SURVEYJS_LICENSE_KEY;\r\n  \r\n  if (licenseKey && licenseKey !== 'YOUR_LICENSE_KEY_HERE') {\r\n    try {\r\n      setLicenseKey(licenseKey);\r\n      console.log('Survey.js commercial license applied successfully');\r\n    } catch (error) {\r\n      console.error('Failed to apply Survey.js license key:', error);\r\n    }\r\n  } else {\r\n    console.warn('Survey.js license key not configured. Please set REACT_APP_SURVEYJS_LICENSE_KEY in your environment file.');\r\n  }\r\n};\r\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,aAAa;;AAE3C;AACA;AACA;AACA;AACA,OAAO,MAAMC,wBAAwB,GAAGA,CAAA,KAAM;EAC5C,MAAMC,UAAU,GAAGC,OAAO,CAACC,GAAG,CAACC,8BAA8B;EAE7D,IAAIH,UAAU,IAAIA,UAAU,KAAK,uBAAuB,EAAE;IACxD,IAAI;MACFF,aAAa,CAACE,UAAU,CAAC;MACzBI,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;IAClE,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;IAChE;EACF,CAAC,MAAM;IACLF,OAAO,CAACG,IAAI,CAAC,2GAA2G,CAAC;EAC3H;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}