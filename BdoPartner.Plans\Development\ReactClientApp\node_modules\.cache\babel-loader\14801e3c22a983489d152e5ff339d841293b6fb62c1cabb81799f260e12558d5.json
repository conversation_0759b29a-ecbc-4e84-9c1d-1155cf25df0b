{"ast": null, "code": "var _jsxFileName = \"C:\\\\git\\\\Partner-Site\\\\BdoPartner.Plans\\\\Development\\\\ReactClientApp\\\\src\\\\components\\\\dashboard\\\\MyCurrentPlan.jsx\",\n  _s = $RefreshSig$();\nimport { Card } from \"primereact/card\";\nimport { But<PERSON> } from \"primereact/button\";\nimport { useNavigate } from \"react-router-dom\";\nimport { useState, useEffect, useContext } from \"react\";\nimport { ProgressSpinner } from \"primereact/progressspinner\";\nimport { Message } from \"primereact/message\";\nimport formService from \"../../services/formService\";\nimport { AuthContext } from \"../../core/auth/components/authProvider\";\nimport { FormStatus } from \"../../core/enumertions/formStatus\";\nimport { PartnerPlanCycle } from \"../../core/enumertions/partnerPlanCycle\";\n\n/**\r\n * Updated Display Logic\r\n   Planning Status: Always displayed\r\n   Mid Year Status: Displayed when Mid Year Review cycle is enabled in questionnaire\r\n   Year End Status: Displayed when Year End Review cycle is enabled in questionnaire\r\n\r\n   The component now uses the EnabledCycles property from the questionnaire to determine\r\n   which cycle sections to show, rather than relying on current cycle and completion status.\r\n * @returns\r\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MyCurrentPlan = () => {\n  _s();\n  const navigate = useNavigate();\n  const authService = useContext(AuthContext);\n\n  // State management\n  const [plansData, setPlansData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  // Data fetching effect\n  useEffect(() => {\n    const fetchPlansData = async () => {\n      if (!authService || !authService.isAuthenticated()) {\n        setError(\"User not authenticated\");\n        setLoading(false);\n        return;\n      }\n      try {\n        setLoading(true);\n        setError(null);\n        const data = await formService.getMyAvailablePlans();\n        if (data && Array.isArray(data)) {\n          setPlansData(data);\n        } else {\n          setError(\"Failed to load plans data\");\n        }\n      } catch (err) {\n        console.error(\"Error fetching plans data:\", err);\n        setError(err.message || \"Failed to load plans data\");\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchPlansData();\n  }, [authService]);\n  const handleViewMyPlan = year => {\n    navigate(`/my-partner-plan?year=${year}`);\n  };\n\n  // Loading state\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"my-current-plan\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"section-title\",\n        children: \"My Available Plans\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        className: \"current-plan-card\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"plan-details\",\n          style: {\n            textAlign: \"center\",\n            padding: \"2rem\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(ProgressSpinner, {\n            style: {\n              width: \"50px\",\n              height: \"50px\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Loading your available plans...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Error state\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"my-current-plan\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"section-title\",\n        children: \"My Available Plans\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        className: \"current-plan-card\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"plan-details\",\n          children: /*#__PURE__*/_jsxDEV(Message, {\n            severity: \"error\",\n            text: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Helper function to get review status based on form status\n  const getReviewStatusForPlan = formStatus => {\n    switch (formStatus) {\n      case FormStatus.PlanningNotStarted:\n      case FormStatus.MidYearReviewNotStarted:\n      case FormStatus.YearEndReviewNotStarted:\n        return {\n          text: \"Not Started\",\n          class: \"status-not-started\"\n        };\n      case FormStatus.PlanningDraft:\n      case FormStatus.PlanningReopened:\n      case FormStatus.MidYearReviewDraft:\n      case FormStatus.MidYearReviewReopened:\n      case FormStatus.YearEndReviewDraft:\n      case FormStatus.YearEndReviewReopened:\n        return {\n          text: \"In Progress\",\n          class: \"status-in-progress\"\n        };\n      case FormStatus.PlanningUnderReview:\n      case FormStatus.MidYearReviewUnderReview:\n      case FormStatus.YearEndReviewUnderReview:\n        return {\n          text: \"Under Review\",\n          class: \"status-under-review\"\n        };\n      case FormStatus.PlanningCompleted:\n      case FormStatus.MidYearReviewCompleted:\n      case FormStatus.YearEndReviewCompleted:\n        return {\n          text: \"Completed\",\n          class: \"status-completed\"\n        };\n      default:\n        return {\n          text: \"Not Started\",\n          class: \"status-not-started\"\n        };\n    }\n  };\n\n  // Show message if no plans available\n  if (!plansData || plansData.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"my-current-plan\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"section-title\",\n        children: \"My Available Plans\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"section-description\",\n        children: \"View and manage your available partner plans across different years. Track progress, make updates, and ensure alignment with personal and organizational goals.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        className: \"current-plan-card\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"plan-details\",\n          style: {\n            textAlign: \"center\",\n            padding: \"2rem\"\n          },\n          children: /*#__PURE__*/_jsxDEV(Message, {\n            severity: \"info\",\n            text: \"No plans available at this time.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"my-current-plan\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"section-title\",\n      children: \"My Available Plans\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"section-description\",\n      children: \"View and manage your available partner plans across different years. Track progress, make updates, and ensure alignment with personal and organizational goals.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 7\n    }, this), plansData.map((planData, index) => {\n      const primaryReviewer = planData.primaryReviewer || \"Not Assigned\";\n      const secondaryReviewer = planData.secondaryReviewer || \"Not Assigned\";\n\n      // Check if no reviewers are assigned\n      const noReviewersAssigned = primaryReviewer === \"Not Assigned\" && secondaryReviewer === \"Not Assigned\";\n\n      // Get status information for each cycle\n      const planningStatus = getReviewStatusForPlan(planData.planStatus);\n      const midYearStatus = getReviewStatusForPlan(planData.midYearStatus);\n      const yearEndStatus = getReviewStatusForPlan(planData.yearEndStatus);\n\n      // Determine which status columns to show based on EnabledCycles from questionnaire\n      // Show cycles based on what's enabled in the questionnaire, not just current cycle\n      const enabledCycles = planData.enabledCycles || [];\n\n      // Always show Planning status (cycle 0 is always enabled)\n      const showMidYearStatus = enabledCycles.includes(PartnerPlanCycle.MidYearReview);\n      const showYearEndStatus = enabledCycles.includes(PartnerPlanCycle.YearEndReview);\n      return /*#__PURE__*/_jsxDEV(Card, {\n        className: \"current-plan-card\",\n        style: {\n          marginBottom: index < plansData.length - 1 ? \"2rem\" : \"0\"\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"plan-details\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"plan-info-grid\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"plan-info-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"plan-label\",\n                children: \"Year\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"plan-value\",\n                children: planData.year\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"plan-info-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"plan-label\",\n                children: \"Planning Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `plan-value ${planningStatus.class}`,\n                children: planData.planStatusDisplayName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 17\n            }, this), showMidYearStatus && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"plan-info-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"plan-label\",\n                children: \"Mid Year Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `plan-value ${midYearStatus.class}`,\n                children: planData.midYearStatusDisplayName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 19\n            }, this), showYearEndStatus && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"plan-info-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"plan-label\",\n                children: \"Year End Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `plan-value ${yearEndStatus.class}`,\n                children: planData.yearEndStatusDisplayName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"plan-info-item\",\n              style: {\n                flex: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"plan-label\",\n                children: \"Reviewers\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `reviewers-list ${noReviewersAssigned ? \"reviewers-warning\" : \"\"}`,\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"reviewer-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"reviewer-label\",\n                    children: \"Primary Reviewer:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 199,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `reviewer-name ${primaryReviewer === \"Not Assigned\" ? \"reviewer-not-assigned\" : \"\"}`,\n                    children: primaryReviewer\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 200,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 198,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"reviewer-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"reviewer-label\",\n                    children: \"Secondary Reviewer:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 203,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `reviewer-name ${secondaryReviewer === \"Not Assigned\" ? \"reviewer-not-assigned\" : \"\"}`,\n                    children: secondaryReviewer\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 204,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 21\n                }, this), noReviewersAssigned && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"reviewer-warning-message\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"warning-content\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"pi pi-exclamation-triangle warning-icon\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 211,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"warning-text\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Unable to Submit Partner Plan\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 213,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        children: \"You cannot submit your partner plan until reviewers are assigned to your plan. Please contact your system administrator.\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 214,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 212,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 210,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 209,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"plan-actions\",\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              label: \"View My Plan\",\n              className: \"p-button-primary-rounded\",\n              icon: \"pi pi-eye\",\n              rounded: true,\n              onClick: () => handleViewMyPlan(planData.year)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 13\n        }, this)\n      }, planData.formId || index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 11\n      }, this);\n    })]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 140,\n    columnNumber: 5\n  }, this);\n};\n_s(MyCurrentPlan, \"kowpkksvpL9o4L3uhqN3cshOueA=\", false, function () {\n  return [useNavigate];\n});\n_c = MyCurrentPlan;\nexport default MyCurrentPlan;\nvar _c;\n$RefreshReg$(_c, \"MyCurrentPlan\");", "map": {"version": 3, "names": ["Card", "<PERSON><PERSON>", "useNavigate", "useState", "useEffect", "useContext", "ProgressSpinner", "Message", "formService", "AuthContext", "FormStatus", "PartnerPlanCycle", "jsxDEV", "_jsxDEV", "MyCurrentPlan", "_s", "navigate", "authService", "plansData", "setPlansData", "loading", "setLoading", "error", "setError", "fetchPlansData", "isAuthenticated", "data", "getMyAvailablePlans", "Array", "isArray", "err", "console", "message", "handleViewMyPlan", "year", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "textAlign", "padding", "width", "height", "severity", "text", "getReviewStatusForPlan", "formStatus", "PlanningNotStarted", "MidYearReviewNotStarted", "YearEndReviewNotStarted", "class", "PlanningDraft", "PlanningReopened", "MidYearReviewDraft", "MidYearReviewReopened", "YearEndReviewDraft", "YearEndReviewReopened", "PlanningUnderReview", "MidYearReviewUnderReview", "YearEndReviewUnderReview", "PlanningCompleted", "MidYearReviewCompleted", "YearEndReviewCompleted", "length", "map", "planData", "index", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "secondaryReviewer", "noReviewersAssigned", "planningStatus", "planStatus", "midYearStatus", "yearEndStatus", "enabledCycles", "showMidYearStatus", "includes", "MidYearReview", "showYearEndStatus", "YearEndReview", "marginBottom", "planStatusDisplayName", "midYearStatusDisplayName", "yearEndStatusDisplayName", "flex", "label", "icon", "rounded", "onClick", "formId", "_c", "$RefreshReg$"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/components/dashboard/MyCurrentPlan.jsx"], "sourcesContent": ["import { Card } from \"primereact/card\";\r\nimport { <PERSON><PERSON> } from \"primereact/button\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { useState, useEffect, useContext } from \"react\";\r\nimport { ProgressSpinner } from \"primereact/progressspinner\";\r\nimport { Message } from \"primereact/message\";\r\nimport formService from \"../../services/formService\";\r\nimport { AuthContext } from \"../../core/auth/components/authProvider\";\r\nimport { FormStatus } from \"../../core/enumertions/formStatus\";\r\nimport { PartnerPlanCycle } from \"../../core/enumertions/partnerPlanCycle\";\r\n\r\n/**\r\n * Updated Display Logic\r\n   Planning Status: Always displayed\r\n   Mid Year Status: Displayed when Mid Year Review cycle is enabled in questionnaire\r\n   Year End Status: Displayed when Year End Review cycle is enabled in questionnaire\r\n\r\n   The component now uses the EnabledCycles property from the questionnaire to determine\r\n   which cycle sections to show, rather than relying on current cycle and completion status.\r\n * @returns\r\n */\r\nconst MyCurrentPlan = () => {\r\n  const navigate = useNavigate();\r\n  const authService = useContext(AuthContext);\r\n\r\n  // State management\r\n  const [plansData, setPlansData] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n\r\n  // Data fetching effect\r\n  useEffect(() => {\r\n    const fetchPlansData = async () => {\r\n      if (!authService || !authService.isAuthenticated()) {\r\n        setError(\"User not authenticated\");\r\n        setLoading(false);\r\n        return;\r\n      }\r\n\r\n      try {\r\n        setLoading(true);\r\n        setError(null);\r\n\r\n        const data = await formService.getMyAvailablePlans();\r\n        if (data && Array.isArray(data)) {\r\n          setPlansData(data);\r\n        } else {\r\n          setError(\"Failed to load plans data\");\r\n        }\r\n      } catch (err) {\r\n        console.error(\"Error fetching plans data:\", err);\r\n        setError(err.message || \"Failed to load plans data\");\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchPlansData();\r\n  }, [authService]);\r\n\r\n  const handleViewMyPlan = (year) => {\r\n    navigate(`/my-partner-plan?year=${year}`);\r\n  };\r\n\r\n  // Loading state\r\n  if (loading) {\r\n    return (\r\n      <div className=\"my-current-plan\">\r\n        <h2 className=\"section-title\">My Available Plans</h2>\r\n        <Card className=\"current-plan-card\">\r\n          <div className=\"plan-details\" style={{ textAlign: \"center\", padding: \"2rem\" }}>\r\n            <ProgressSpinner style={{ width: \"50px\", height: \"50px\" }} />\r\n            <p>Loading your available plans...</p>\r\n          </div>\r\n        </Card>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Error state\r\n  if (error) {\r\n    return (\r\n      <div className=\"my-current-plan\">\r\n        <h2 className=\"section-title\">My Available Plans</h2>\r\n        <Card className=\"current-plan-card\">\r\n          <div className=\"plan-details\">\r\n            <Message severity=\"error\" text={error} />\r\n          </div>\r\n        </Card>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Helper function to get review status based on form status\r\n  const getReviewStatusForPlan = (formStatus) => {\r\n    switch (formStatus) {\r\n      case FormStatus.PlanningNotStarted:\r\n      case FormStatus.MidYearReviewNotStarted:\r\n      case FormStatus.YearEndReviewNotStarted:\r\n        return { text: \"Not Started\", class: \"status-not-started\" };\r\n      case FormStatus.PlanningDraft:\r\n      case FormStatus.PlanningReopened:\r\n      case FormStatus.MidYearReviewDraft:\r\n      case FormStatus.MidYearReviewReopened:\r\n      case FormStatus.YearEndReviewDraft:\r\n      case FormStatus.YearEndReviewReopened:\r\n        return { text: \"In Progress\", class: \"status-in-progress\" };\r\n      case FormStatus.PlanningUnderReview:\r\n      case FormStatus.MidYearReviewUnderReview:\r\n      case FormStatus.YearEndReviewUnderReview:\r\n        return { text: \"Under Review\", class: \"status-under-review\" };\r\n      case FormStatus.PlanningCompleted:\r\n      case FormStatus.MidYearReviewCompleted:\r\n      case FormStatus.YearEndReviewCompleted:\r\n        return { text: \"Completed\", class: \"status-completed\" };\r\n      default:\r\n        return { text: \"Not Started\", class: \"status-not-started\" };\r\n    }\r\n  };\r\n\r\n  // Show message if no plans available\r\n  if (!plansData || plansData.length === 0) {\r\n    return (\r\n      <div className=\"my-current-plan\">\r\n        <h2 className=\"section-title\">My Available Plans</h2>\r\n        <p className=\"section-description\">\r\n          View and manage your available partner plans across different years. Track progress, make updates, and ensure alignment with personal and\r\n          organizational goals.\r\n        </p>\r\n        <Card className=\"current-plan-card\">\r\n          <div className=\"plan-details\" style={{ textAlign: \"center\", padding: \"2rem\" }}>\r\n            <Message severity=\"info\" text=\"No plans available at this time.\" />\r\n          </div>\r\n        </Card>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"my-current-plan\">\r\n      <h2 className=\"section-title\">My Available Plans</h2>\r\n      <p className=\"section-description\">\r\n        View and manage your available partner plans across different years. Track progress, make updates, and ensure alignment with personal and\r\n        organizational goals.\r\n      </p>\r\n\r\n      {plansData.map((planData, index) => {\r\n        const primaryReviewer = planData.primaryReviewer || \"Not Assigned\";\r\n        const secondaryReviewer = planData.secondaryReviewer || \"Not Assigned\";\r\n\r\n        // Check if no reviewers are assigned\r\n        const noReviewersAssigned = primaryReviewer === \"Not Assigned\" && secondaryReviewer === \"Not Assigned\";\r\n\r\n        // Get status information for each cycle\r\n        const planningStatus = getReviewStatusForPlan(planData.planStatus);\r\n        const midYearStatus = getReviewStatusForPlan(planData.midYearStatus);\r\n        const yearEndStatus = getReviewStatusForPlan(planData.yearEndStatus);\r\n\r\n        // Determine which status columns to show based on EnabledCycles from questionnaire\r\n        // Show cycles based on what's enabled in the questionnaire, not just current cycle\r\n        const enabledCycles = planData.enabledCycles || [];\r\n\r\n        // Always show Planning status (cycle 0 is always enabled)\r\n        const showMidYearStatus = enabledCycles.includes(PartnerPlanCycle.MidYearReview);\r\n        const showYearEndStatus = enabledCycles.includes(PartnerPlanCycle.YearEndReview);\r\n\r\n        return (\r\n          <Card key={planData.formId || index} className=\"current-plan-card\" style={{ marginBottom: index < plansData.length - 1 ? \"2rem\" : \"0\" }}>\r\n            <div className=\"plan-details\">\r\n              <div className=\"plan-info-grid\">\r\n                <div className=\"plan-info-item\">\r\n                  <label className=\"plan-label\">Year</label>\r\n                  <span className=\"plan-value\">{planData.year}</span>\r\n                </div>\r\n\r\n                <div className=\"plan-info-item\">\r\n                  <label className=\"plan-label\">Planning Status</label>\r\n                  <span className={`plan-value ${planningStatus.class}`}>{planData.planStatusDisplayName}</span>\r\n                </div>\r\n\r\n                {showMidYearStatus && (\r\n                  <div className=\"plan-info-item\">\r\n                    <label className=\"plan-label\">Mid Year Status</label>\r\n                    <span className={`plan-value ${midYearStatus.class}`}>{planData.midYearStatusDisplayName}</span>\r\n                  </div>\r\n                )}\r\n\r\n                {showYearEndStatus && (\r\n                  <div className=\"plan-info-item\">\r\n                    <label className=\"plan-label\">Year End Status</label>\r\n                    <span className={`plan-value ${yearEndStatus.class}`}>{planData.yearEndStatusDisplayName}</span>\r\n                  </div>\r\n                )}\r\n\r\n                <div className=\"plan-info-item\" style={{ flex: 1 }}>\r\n                  <label className=\"plan-label\">Reviewers</label>\r\n                  <div className={`reviewers-list ${noReviewersAssigned ? \"reviewers-warning\" : \"\"}`}>\r\n                    <div className=\"reviewer-item\">\r\n                      <span className=\"reviewer-label\">Primary Reviewer:</span>\r\n                      <span className={`reviewer-name ${primaryReviewer === \"Not Assigned\" ? \"reviewer-not-assigned\" : \"\"}`}>{primaryReviewer}</span>\r\n                    </div>\r\n                    <div className=\"reviewer-item\">\r\n                      <span className=\"reviewer-label\">Secondary Reviewer:</span>\r\n                      <span className={`reviewer-name ${secondaryReviewer === \"Not Assigned\" ? \"reviewer-not-assigned\" : \"\"}`}>\r\n                        {secondaryReviewer}\r\n                      </span>\r\n                    </div>\r\n                    {noReviewersAssigned && (\r\n                      <div className=\"reviewer-warning-message\">\r\n                        <div className=\"warning-content\">\r\n                          <i className=\"pi pi-exclamation-triangle warning-icon\"></i>\r\n                          <div className=\"warning-text\">\r\n                            <strong>Unable to Submit Partner Plan</strong>\r\n                            <p>\r\n                              You cannot submit your partner plan until reviewers are assigned to your plan. Please contact your system administrator.\r\n                            </p>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"plan-actions\">\r\n                <Button\r\n                  label=\"View My Plan\"\r\n                  className=\"p-button-primary-rounded\"\r\n                  icon=\"pi pi-eye\"\r\n                  rounded\r\n                  onClick={() => handleViewMyPlan(planData.year)}\r\n                />\r\n              </div>\r\n            </div>\r\n          </Card>\r\n        );\r\n      })}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default MyCurrentPlan;\r\n"], "mappings": ";;AAAA,SAASA,IAAI,QAAQ,iBAAiB;AACtC,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,QAAQ,OAAO;AACvD,SAASC,eAAe,QAAQ,4BAA4B;AAC5D,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,OAAOC,WAAW,MAAM,4BAA4B;AACpD,SAASC,WAAW,QAAQ,yCAAyC;AACrE,SAASC,UAAU,QAAQ,mCAAmC;AAC9D,SAASC,gBAAgB,QAAQ,yCAAyC;;AAE1E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA,SAAAC,MAAA,IAAAC,OAAA;AAUA,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAMC,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAC9B,MAAMe,WAAW,GAAGZ,UAAU,CAACI,WAAW,CAAC;;EAE3C;EACA,MAAM,CAACS,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmB,KAAK,EAAEC,QAAQ,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;;EAExC;EACAC,SAAS,CAAC,MAAM;IACd,MAAMoB,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC,IAAI,CAACP,WAAW,IAAI,CAACA,WAAW,CAACQ,eAAe,CAAC,CAAC,EAAE;QAClDF,QAAQ,CAAC,wBAAwB,CAAC;QAClCF,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;MAEA,IAAI;QACFA,UAAU,CAAC,IAAI,CAAC;QAChBE,QAAQ,CAAC,IAAI,CAAC;QAEd,MAAMG,IAAI,GAAG,MAAMlB,WAAW,CAACmB,mBAAmB,CAAC,CAAC;QACpD,IAAID,IAAI,IAAIE,KAAK,CAACC,OAAO,CAACH,IAAI,CAAC,EAAE;UAC/BP,YAAY,CAACO,IAAI,CAAC;QACpB,CAAC,MAAM;UACLH,QAAQ,CAAC,2BAA2B,CAAC;QACvC;MACF,CAAC,CAAC,OAAOO,GAAG,EAAE;QACZC,OAAO,CAACT,KAAK,CAAC,4BAA4B,EAAEQ,GAAG,CAAC;QAChDP,QAAQ,CAACO,GAAG,CAACE,OAAO,IAAI,2BAA2B,CAAC;MACtD,CAAC,SAAS;QACRX,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDG,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACP,WAAW,CAAC,CAAC;EAEjB,MAAMgB,gBAAgB,GAAIC,IAAI,IAAK;IACjClB,QAAQ,CAAC,yBAAyBkB,IAAI,EAAE,CAAC;EAC3C,CAAC;;EAED;EACA,IAAId,OAAO,EAAE;IACX,oBACEP,OAAA;MAAKsB,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BvB,OAAA;QAAIsB,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrD3B,OAAA,CAACb,IAAI;QAACmC,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eACjCvB,OAAA;UAAKsB,SAAS,EAAC,cAAc;UAACM,KAAK,EAAE;YAAEC,SAAS,EAAE,QAAQ;YAAEC,OAAO,EAAE;UAAO,CAAE;UAAAP,QAAA,gBAC5EvB,OAAA,CAACP,eAAe;YAACmC,KAAK,EAAE;cAAEG,KAAK,EAAE,MAAM;cAAEC,MAAM,EAAE;YAAO;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7D3B,OAAA;YAAAuB,QAAA,EAAG;UAA+B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAEV;;EAEA;EACA,IAAIlB,KAAK,EAAE;IACT,oBACET,OAAA;MAAKsB,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BvB,OAAA;QAAIsB,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrD3B,OAAA,CAACb,IAAI;QAACmC,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eACjCvB,OAAA;UAAKsB,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3BvB,OAAA,CAACN,OAAO;YAACuC,QAAQ,EAAC,OAAO;YAACC,IAAI,EAAEzB;UAAM;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAEV;;EAEA;EACA,MAAMQ,sBAAsB,GAAIC,UAAU,IAAK;IAC7C,QAAQA,UAAU;MAChB,KAAKvC,UAAU,CAACwC,kBAAkB;MAClC,KAAKxC,UAAU,CAACyC,uBAAuB;MACvC,KAAKzC,UAAU,CAAC0C,uBAAuB;QACrC,OAAO;UAAEL,IAAI,EAAE,aAAa;UAAEM,KAAK,EAAE;QAAqB,CAAC;MAC7D,KAAK3C,UAAU,CAAC4C,aAAa;MAC7B,KAAK5C,UAAU,CAAC6C,gBAAgB;MAChC,KAAK7C,UAAU,CAAC8C,kBAAkB;MAClC,KAAK9C,UAAU,CAAC+C,qBAAqB;MACrC,KAAK/C,UAAU,CAACgD,kBAAkB;MAClC,KAAKhD,UAAU,CAACiD,qBAAqB;QACnC,OAAO;UAAEZ,IAAI,EAAE,aAAa;UAAEM,KAAK,EAAE;QAAqB,CAAC;MAC7D,KAAK3C,UAAU,CAACkD,mBAAmB;MACnC,KAAKlD,UAAU,CAACmD,wBAAwB;MACxC,KAAKnD,UAAU,CAACoD,wBAAwB;QACtC,OAAO;UAAEf,IAAI,EAAE,cAAc;UAAEM,KAAK,EAAE;QAAsB,CAAC;MAC/D,KAAK3C,UAAU,CAACqD,iBAAiB;MACjC,KAAKrD,UAAU,CAACsD,sBAAsB;MACtC,KAAKtD,UAAU,CAACuD,sBAAsB;QACpC,OAAO;UAAElB,IAAI,EAAE,WAAW;UAAEM,KAAK,EAAE;QAAmB,CAAC;MACzD;QACE,OAAO;UAAEN,IAAI,EAAE,aAAa;UAAEM,KAAK,EAAE;QAAqB,CAAC;IAC/D;EACF,CAAC;;EAED;EACA,IAAI,CAACnC,SAAS,IAAIA,SAAS,CAACgD,MAAM,KAAK,CAAC,EAAE;IACxC,oBACErD,OAAA;MAAKsB,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BvB,OAAA;QAAIsB,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrD3B,OAAA;QAAGsB,SAAS,EAAC,qBAAqB;QAAAC,QAAA,EAAC;MAGnC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJ3B,OAAA,CAACb,IAAI;QAACmC,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eACjCvB,OAAA;UAAKsB,SAAS,EAAC,cAAc;UAACM,KAAK,EAAE;YAAEC,SAAS,EAAE,QAAQ;YAAEC,OAAO,EAAE;UAAO,CAAE;UAAAP,QAAA,eAC5EvB,OAAA,CAACN,OAAO;YAACuC,QAAQ,EAAC,MAAM;YAACC,IAAI,EAAC;UAAkC;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAEV;EAEA,oBACE3B,OAAA;IAAKsB,SAAS,EAAC,iBAAiB;IAAAC,QAAA,gBAC9BvB,OAAA;MAAIsB,SAAS,EAAC,eAAe;MAAAC,QAAA,EAAC;IAAkB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACrD3B,OAAA;MAAGsB,SAAS,EAAC,qBAAqB;MAAAC,QAAA,EAAC;IAGnC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,EAEHtB,SAAS,CAACiD,GAAG,CAAC,CAACC,QAAQ,EAAEC,KAAK,KAAK;MAClC,MAAMC,eAAe,GAAGF,QAAQ,CAACE,eAAe,IAAI,cAAc;MAClE,MAAMC,iBAAiB,GAAGH,QAAQ,CAACG,iBAAiB,IAAI,cAAc;;MAEtE;MACA,MAAMC,mBAAmB,GAAGF,eAAe,KAAK,cAAc,IAAIC,iBAAiB,KAAK,cAAc;;MAEtG;MACA,MAAME,cAAc,GAAGzB,sBAAsB,CAACoB,QAAQ,CAACM,UAAU,CAAC;MAClE,MAAMC,aAAa,GAAG3B,sBAAsB,CAACoB,QAAQ,CAACO,aAAa,CAAC;MACpE,MAAMC,aAAa,GAAG5B,sBAAsB,CAACoB,QAAQ,CAACQ,aAAa,CAAC;;MAEpE;MACA;MACA,MAAMC,aAAa,GAAGT,QAAQ,CAACS,aAAa,IAAI,EAAE;;MAElD;MACA,MAAMC,iBAAiB,GAAGD,aAAa,CAACE,QAAQ,CAACpE,gBAAgB,CAACqE,aAAa,CAAC;MAChF,MAAMC,iBAAiB,GAAGJ,aAAa,CAACE,QAAQ,CAACpE,gBAAgB,CAACuE,aAAa,CAAC;MAEhF,oBACErE,OAAA,CAACb,IAAI;QAAgCmC,SAAS,EAAC,mBAAmB;QAACM,KAAK,EAAE;UAAE0C,YAAY,EAAEd,KAAK,GAAGnD,SAAS,CAACgD,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG;QAAI,CAAE;QAAA9B,QAAA,eACtIvB,OAAA;UAAKsB,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BvB,OAAA;YAAKsB,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BvB,OAAA;cAAKsB,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BvB,OAAA;gBAAOsB,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1C3B,OAAA;gBAAMsB,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAEgC,QAAQ,CAAClC;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eAEN3B,OAAA;cAAKsB,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BvB,OAAA;gBAAOsB,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrD3B,OAAA;gBAAMsB,SAAS,EAAE,cAAcsC,cAAc,CAACpB,KAAK,EAAG;gBAAAjB,QAAA,EAAEgC,QAAQ,CAACgB;cAAqB;gBAAA/C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3F,CAAC,EAELsC,iBAAiB,iBAChBjE,OAAA;cAAKsB,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BvB,OAAA;gBAAOsB,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrD3B,OAAA;gBAAMsB,SAAS,EAAE,cAAcwC,aAAa,CAACtB,KAAK,EAAG;gBAAAjB,QAAA,EAAEgC,QAAQ,CAACiB;cAAwB;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7F,CACN,EAEAyC,iBAAiB,iBAChBpE,OAAA;cAAKsB,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BvB,OAAA;gBAAOsB,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrD3B,OAAA;gBAAMsB,SAAS,EAAE,cAAcyC,aAAa,CAACvB,KAAK,EAAG;gBAAAjB,QAAA,EAAEgC,QAAQ,CAACkB;cAAwB;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7F,CACN,eAED3B,OAAA;cAAKsB,SAAS,EAAC,gBAAgB;cAACM,KAAK,EAAE;gBAAE8C,IAAI,EAAE;cAAE,CAAE;cAAAnD,QAAA,gBACjDvB,OAAA;gBAAOsB,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/C3B,OAAA;gBAAKsB,SAAS,EAAE,kBAAkBqC,mBAAmB,GAAG,mBAAmB,GAAG,EAAE,EAAG;gBAAApC,QAAA,gBACjFvB,OAAA;kBAAKsB,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5BvB,OAAA;oBAAMsB,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,EAAC;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACzD3B,OAAA;oBAAMsB,SAAS,EAAE,iBAAiBmC,eAAe,KAAK,cAAc,GAAG,uBAAuB,GAAG,EAAE,EAAG;oBAAAlC,QAAA,EAAEkC;kBAAe;oBAAAjC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5H,CAAC,eACN3B,OAAA;kBAAKsB,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5BvB,OAAA;oBAAMsB,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,EAAC;kBAAmB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC3D3B,OAAA;oBAAMsB,SAAS,EAAE,iBAAiBoC,iBAAiB,KAAK,cAAc,GAAG,uBAAuB,GAAG,EAAE,EAAG;oBAAAnC,QAAA,EACrGmC;kBAAiB;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,EACLgC,mBAAmB,iBAClB3D,OAAA;kBAAKsB,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,eACvCvB,OAAA;oBAAKsB,SAAS,EAAC,iBAAiB;oBAAAC,QAAA,gBAC9BvB,OAAA;sBAAGsB,SAAS,EAAC;oBAAyC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC3D3B,OAAA;sBAAKsB,SAAS,EAAC,cAAc;sBAAAC,QAAA,gBAC3BvB,OAAA;wBAAAuB,QAAA,EAAQ;sBAA6B;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAC9C3B,OAAA;wBAAAuB,QAAA,EAAG;sBAEH;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN3B,OAAA;YAAKsB,SAAS,EAAC,cAAc;YAAAC,QAAA,eAC3BvB,OAAA,CAACZ,MAAM;cACLuF,KAAK,EAAC,cAAc;cACpBrD,SAAS,EAAC,0BAA0B;cACpCsD,IAAI,EAAC,WAAW;cAChBC,OAAO;cACPC,OAAO,EAAEA,CAAA,KAAM1D,gBAAgB,CAACmC,QAAQ,CAAClC,IAAI;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,GAlEG4B,QAAQ,CAACwB,MAAM,IAAIvB,KAAK;QAAAhC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAmE7B,CAAC;IAEX,CAAC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACzB,EAAA,CA1NID,aAAa;EAAA,QACAZ,WAAW;AAAA;AAAA2F,EAAA,GADxB/E,aAAa;AA4NnB,eAAeA,aAAa;AAAC,IAAA+E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}