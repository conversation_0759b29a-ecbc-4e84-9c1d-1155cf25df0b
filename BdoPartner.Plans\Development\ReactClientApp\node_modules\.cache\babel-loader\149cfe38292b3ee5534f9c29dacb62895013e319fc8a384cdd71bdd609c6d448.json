{"ast": null, "code": "import { KEY_PREFIX } from './constants';\nexport default function purgeStoredState(config) {\n  var storage = config.storage;\n  var storageKey = \"\".concat(config.keyPrefix !== undefined ? config.keyPrefix : KEY_PREFIX).concat(config.key);\n  return storage.removeItem(storageKey, warnIfRemoveError);\n}\nfunction warnIfRemoveError(err) {\n  if (err && process.env.NODE_ENV !== 'production') {\n    console.error('redux-persist/purgeStoredState: Error purging data stored state', err);\n  }\n}", "map": {"version": 3, "names": ["KEY_PREFIX", "purgeStoredState", "config", "storage", "storageKey", "concat", "keyPrefix", "undefined", "key", "removeItem", "warnIfRemoveError", "err", "process", "env", "NODE_ENV", "console", "error"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/node_modules/redux-persist/es/purgeStoredState.js"], "sourcesContent": ["import { KEY_PREFIX } from './constants';\nexport default function purgeStoredState(config) {\n  var storage = config.storage;\n  var storageKey = \"\".concat(config.keyPrefix !== undefined ? config.keyPrefix : KEY_PREFIX).concat(config.key);\n  return storage.removeItem(storageKey, warnIfRemoveError);\n}\n\nfunction warnIfRemoveError(err) {\n  if (err && process.env.NODE_ENV !== 'production') {\n    console.error('redux-persist/purgeStoredState: Error purging data stored state', err);\n  }\n}"], "mappings": "AAAA,SAASA,UAAU,QAAQ,aAAa;AACxC,eAAe,SAASC,gBAAgBA,CAACC,MAAM,EAAE;EAC/C,IAAIC,OAAO,GAAGD,MAAM,CAACC,OAAO;EAC5B,IAAIC,UAAU,GAAG,EAAE,CAACC,MAAM,CAACH,MAAM,CAACI,SAAS,KAAKC,SAAS,GAAGL,MAAM,CAACI,SAAS,GAAGN,UAAU,CAAC,CAACK,MAAM,CAACH,MAAM,CAACM,GAAG,CAAC;EAC7G,OAAOL,OAAO,CAACM,UAAU,CAACL,UAAU,EAAEM,iBAAiB,CAAC;AAC1D;AAEA,SAASA,iBAAiBA,CAACC,GAAG,EAAE;EAC9B,IAAIA,GAAG,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IAChDC,OAAO,CAACC,KAAK,CAAC,iEAAiE,EAAEL,GAAG,CAAC;EACvF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}