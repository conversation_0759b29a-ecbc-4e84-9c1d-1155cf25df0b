{"ast": null, "code": "import { concatMap } from './concatMap';\nimport { isFunction } from '../util/isFunction';\nexport function concatMapTo(innerObservable, resultSelector) {\n  return isFunction(resultSelector) ? concatMap(function () {\n    return innerObservable;\n  }, resultSelector) : concatMap(function () {\n    return innerObservable;\n  });\n}", "map": {"version": 3, "names": ["concatMap", "isFunction", "concatMapTo", "innerObservable", "resultSelector"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\concatMapTo.ts"], "sourcesContent": ["import { concatMap } from './concatMap';\nimport { ObservableInput, OperatorFunction, ObservedValueOf } from '../types';\nimport { isFunction } from '../util/isFunction';\n\n/** @deprecated Will be removed in v9. Use {@link concatMap} instead: `concatMap(() => result)` */\nexport function concatMapTo<O extends ObservableInput<unknown>>(observable: O): OperatorFunction<unknown, ObservedValueOf<O>>;\n/** @deprecated The `resultSelector` parameter will be removed in v8. Use an inner `map` instead. Details: https://rxjs.dev/deprecations/resultSelector */\nexport function concatMapTo<O extends ObservableInput<unknown>>(\n  observable: O,\n  resultSelector: undefined\n): OperatorFunction<unknown, ObservedValueOf<O>>;\n/** @deprecated The `resultSelector` parameter will be removed in v8. Use an inner `map` instead. Details: https://rxjs.dev/deprecations/resultSelector */\nexport function concatMapTo<T, R, O extends ObservableInput<unknown>>(\n  observable: O,\n  resultSelector: (outerValue: T, innerValue: ObservedValueOf<O>, outerIndex: number, innerIndex: number) => R\n): OperatorFunction<T, R>;\n\n/**\n * Projects each source value to the same Observable which is merged multiple\n * times in a serialized fashion on the output Observable.\n *\n * <span class=\"informal\">It's like {@link concatMap}, but maps each value\n * always to the same inner Observable.</span>\n *\n * ![](concatMapTo.png)\n *\n * Maps each source value to the given Observable `innerObservable` regardless\n * of the source value, and then flattens those resulting Observables into one\n * single Observable, which is the output Observable. Each new `innerObservable`\n * instance emitted on the output Observable is concatenated with the previous\n * `innerObservable` instance.\n *\n * __Warning:__ if source values arrive endlessly and faster than their\n * corresponding inner Observables can complete, it will result in memory issues\n * as inner Observables amass in an unbounded buffer waiting for their turn to\n * be subscribed to.\n *\n * Note: `concatMapTo` is equivalent to `mergeMapTo` with concurrency parameter\n * set to `1`.\n *\n * ## Example\n *\n * For each click event, tick every second from 0 to 3, with no concurrency\n *\n * ```ts\n * import { fromEvent, concatMapTo, interval, take } from 'rxjs';\n *\n * const clicks = fromEvent(document, 'click');\n * const result = clicks.pipe(\n *   concatMapTo(interval(1000).pipe(take(4)))\n * );\n * result.subscribe(x => console.log(x));\n *\n * // Results in the following:\n * // (results are not concurrent)\n * // For every click on the \"document\" it will emit values 0 to 3 spaced\n * // on a 1000ms interval\n * // one click = 1000ms-> 0 -1000ms-> 1 -1000ms-> 2 -1000ms-> 3\n * ```\n *\n * @see {@link concat}\n * @see {@link concatAll}\n * @see {@link concatMap}\n * @see {@link mergeMapTo}\n * @see {@link switchMapTo}\n *\n * @param innerObservable An `ObservableInput` to replace each value from the\n * source Observable.\n * @return A function that returns an Observable of values merged together by\n * joining the passed Observable with itself, one after the other, for each\n * value emitted from the source.\n * @deprecated Will be removed in v9. Use {@link concatMap} instead: `concatMap(() => result)`\n */\nexport function concatMapTo<T, R, O extends ObservableInput<unknown>>(\n  innerObservable: O,\n  resultSelector?: (outerValue: T, innerValue: ObservedValueOf<O>, outerIndex: number, innerIndex: number) => R\n): OperatorFunction<T, ObservedValueOf<O> | R> {\n  return isFunction(resultSelector) ? concatMap(() => innerObservable, resultSelector) : concatMap(() => innerObservable);\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,aAAa;AAEvC,SAASC,UAAU,QAAQ,oBAAoB;AAuE/C,OAAM,SAAUC,WAAWA,CACzBC,eAAkB,EAClBC,cAA6G;EAE7G,OAAOH,UAAU,CAACG,cAAc,CAAC,GAAGJ,SAAS,CAAC;IAAM,OAAAG,eAAe;EAAf,CAAe,EAAEC,cAAc,CAAC,GAAGJ,SAAS,CAAC;IAAM,OAAAG,eAAe;EAAf,CAAe,CAAC;AACzH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}