{"ast": null, "code": "var _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport { useEffect } from 'react';\nimport { loadingService } from '../loadingService';\n\n/**\r\n * Custom hook to control loading interceptor for a specific context\r\n * @param {string} context - The context identifier (e.g., 'survey', 'form', etc.)\r\n * @param {boolean} disabled - Whether to disable loading for this context (default: true)\r\n * @returns {object} Object with methods to control loading interceptor\r\n */\nexport const useLoadingControl = (context, disabled = true) => {\n  _s();\n  useEffect(() => {\n    if (disabled) {\n      loadingService.disableForContext(context);\n    } else {\n      loadingService.enableForContext(context);\n    }\n\n    // Cleanup: re-enable when component unmounts\n    return () => {\n      if (disabled) {\n        loadingService.enableForContext(context);\n      }\n    };\n  }, [context, disabled]);\n  return {\n    // Manual control methods\n    disable: () => loadingService.disableForContext(context),\n    enable: () => loadingService.enableForContext(context),\n    isDisabled: () => loadingService.isContextDisabled(context),\n    // Global control methods\n    setGlobalEnabled: enabled => loadingService.setInterceptorEnabled(enabled),\n    isGlobalEnabled: () => loadingService.isInterceptorEnabled(),\n    // Context management\n    clearAllDisabled: () => loadingService.clearDisabledContexts(),\n    getDisabledContexts: () => loadingService.getDisabledContexts()\n  };\n};\n\n/**\r\n * Hook to globally enable/disable loading interceptor\r\n * @param {boolean} enabled - Whether loading interceptor should be enabled\r\n * @returns {object} Object with methods to control global loading interceptor\r\n */\n_s(useLoadingControl, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\nexport const useGlobalLoadingControl = (enabled = true) => {\n  _s2();\n  useEffect(() => {\n    loadingService.setInterceptorEnabled(enabled);\n\n    // Cleanup: restore to enabled state when component unmounts\n    return () => {\n      loadingService.setInterceptorEnabled(true);\n    };\n  }, [enabled]);\n  return {\n    setEnabled: enabled => loadingService.setInterceptorEnabled(enabled),\n    isEnabled: () => loadingService.isInterceptorEnabled(),\n    disable: () => loadingService.setInterceptorEnabled(false),\n    enable: () => loadingService.setInterceptorEnabled(true)\n  };\n};\n_s2(useGlobalLoadingControl, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");", "map": {"version": 3, "names": ["useEffect", "loadingService", "useLoadingControl", "context", "disabled", "_s", "disableForContext", "enableForContext", "disable", "enable", "isDisabled", "isContextDisabled", "setGlobalEnabled", "enabled", "setInterceptorEnabled", "isGlobalEnabled", "isInterceptorEnabled", "clearAllDisabled", "clearDisabledContexts", "getDisabledContexts", "useGlobalLoadingControl", "_s2", "setEnabled", "isEnabled"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/core/loading/hooks/useLoadingControl.js"], "sourcesContent": ["import { useEffect } from 'react';\r\nimport { loadingService } from '../loadingService';\r\n\r\n/**\r\n * Custom hook to control loading interceptor for a specific context\r\n * @param {string} context - The context identifier (e.g., 'survey', 'form', etc.)\r\n * @param {boolean} disabled - Whether to disable loading for this context (default: true)\r\n * @returns {object} Object with methods to control loading interceptor\r\n */\r\nexport const useLoadingControl = (context, disabled = true) => {\r\n  useEffect(() => {\r\n    if (disabled) {\r\n      loadingService.disableForContext(context);\r\n    } else {\r\n      loadingService.enableForContext(context);\r\n    }\r\n\r\n    // Cleanup: re-enable when component unmounts\r\n    return () => {\r\n      if (disabled) {\r\n        loadingService.enableForContext(context);\r\n      }\r\n    };\r\n  }, [context, disabled]);\r\n\r\n  return {\r\n    // Manual control methods\r\n    disable: () => loadingService.disableForContext(context),\r\n    enable: () => loadingService.enableForContext(context),\r\n    isDisabled: () => loadingService.isContextDisabled(context),\r\n    \r\n    // Global control methods\r\n    setGlobalEnabled: (enabled) => loadingService.setInterceptorEnabled(enabled),\r\n    isGlobalEnabled: () => loadingService.isInterceptorEnabled(),\r\n    \r\n    // Context management\r\n    clearAllDisabled: () => loadingService.clearDisabledContexts(),\r\n    getDisabledContexts: () => loadingService.getDisabledContexts()\r\n  };\r\n};\r\n\r\n/**\r\n * Hook to globally enable/disable loading interceptor\r\n * @param {boolean} enabled - Whether loading interceptor should be enabled\r\n * @returns {object} Object with methods to control global loading interceptor\r\n */\r\nexport const useGlobalLoadingControl = (enabled = true) => {\r\n  useEffect(() => {\r\n    loadingService.setInterceptorEnabled(enabled);\r\n    \r\n    // Cleanup: restore to enabled state when component unmounts\r\n    return () => {\r\n      loadingService.setInterceptorEnabled(true);\r\n    };\r\n  }, [enabled]);\r\n\r\n  return {\r\n    setEnabled: (enabled) => loadingService.setInterceptorEnabled(enabled),\r\n    isEnabled: () => loadingService.isInterceptorEnabled(),\r\n    disable: () => loadingService.setInterceptorEnabled(false),\r\n    enable: () => loadingService.setInterceptorEnabled(true)\r\n  };\r\n};\r\n"], "mappings": ";;AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,cAAc,QAAQ,mBAAmB;;AAElD;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,iBAAiB,GAAGA,CAACC,OAAO,EAAEC,QAAQ,GAAG,IAAI,KAAK;EAAAC,EAAA;EAC7DL,SAAS,CAAC,MAAM;IACd,IAAII,QAAQ,EAAE;MACZH,cAAc,CAACK,iBAAiB,CAACH,OAAO,CAAC;IAC3C,CAAC,MAAM;MACLF,cAAc,CAACM,gBAAgB,CAACJ,OAAO,CAAC;IAC1C;;IAEA;IACA,OAAO,MAAM;MACX,IAAIC,QAAQ,EAAE;QACZH,cAAc,CAACM,gBAAgB,CAACJ,OAAO,CAAC;MAC1C;IACF,CAAC;EACH,CAAC,EAAE,CAACA,OAAO,EAAEC,QAAQ,CAAC,CAAC;EAEvB,OAAO;IACL;IACAI,OAAO,EAAEA,CAAA,KAAMP,cAAc,CAACK,iBAAiB,CAACH,OAAO,CAAC;IACxDM,MAAM,EAAEA,CAAA,KAAMR,cAAc,CAACM,gBAAgB,CAACJ,OAAO,CAAC;IACtDO,UAAU,EAAEA,CAAA,KAAMT,cAAc,CAACU,iBAAiB,CAACR,OAAO,CAAC;IAE3D;IACAS,gBAAgB,EAAGC,OAAO,IAAKZ,cAAc,CAACa,qBAAqB,CAACD,OAAO,CAAC;IAC5EE,eAAe,EAAEA,CAAA,KAAMd,cAAc,CAACe,oBAAoB,CAAC,CAAC;IAE5D;IACAC,gBAAgB,EAAEA,CAAA,KAAMhB,cAAc,CAACiB,qBAAqB,CAAC,CAAC;IAC9DC,mBAAmB,EAAEA,CAAA,KAAMlB,cAAc,CAACkB,mBAAmB,CAAC;EAChE,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AAJAd,EAAA,CAhCaH,iBAAiB;AAqC9B,OAAO,MAAMkB,uBAAuB,GAAGA,CAACP,OAAO,GAAG,IAAI,KAAK;EAAAQ,GAAA;EACzDrB,SAAS,CAAC,MAAM;IACdC,cAAc,CAACa,qBAAqB,CAACD,OAAO,CAAC;;IAE7C;IACA,OAAO,MAAM;MACXZ,cAAc,CAACa,qBAAqB,CAAC,IAAI,CAAC;IAC5C,CAAC;EACH,CAAC,EAAE,CAACD,OAAO,CAAC,CAAC;EAEb,OAAO;IACLS,UAAU,EAAGT,OAAO,IAAKZ,cAAc,CAACa,qBAAqB,CAACD,OAAO,CAAC;IACtEU,SAAS,EAAEA,CAAA,KAAMtB,cAAc,CAACe,oBAAoB,CAAC,CAAC;IACtDR,OAAO,EAAEA,CAAA,KAAMP,cAAc,CAACa,qBAAqB,CAAC,KAAK,CAAC;IAC1DL,MAAM,EAAEA,CAAA,KAAMR,cAAc,CAACa,qBAAqB,CAAC,IAAI;EACzD,CAAC;AACH,CAAC;AAACO,GAAA,CAhBWD,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}