{"ast": null, "code": "import { __extends, __values } from \"tslib\";\nimport { Observable } from './Observable';\nimport { Subscription, EMPTY_SUBSCRIPTION } from './Subscription';\nimport { ObjectUnsubscribedError } from './util/ObjectUnsubscribedError';\nimport { arrRemove } from './util/arrRemove';\nimport { errorContext } from './util/errorContext';\nvar Subject = function (_super) {\n  __extends(Subject, _super);\n  function Subject() {\n    var _this = _super.call(this) || this;\n    _this.closed = false;\n    _this.currentObservers = null;\n    _this.observers = [];\n    _this.isStopped = false;\n    _this.hasError = false;\n    _this.thrownError = null;\n    return _this;\n  }\n  Subject.prototype.lift = function (operator) {\n    var subject = new AnonymousSubject(this, this);\n    subject.operator = operator;\n    return subject;\n  };\n  Subject.prototype._throwIfClosed = function () {\n    if (this.closed) {\n      throw new ObjectUnsubscribedError();\n    }\n  };\n  Subject.prototype.next = function (value) {\n    var _this = this;\n    errorContext(function () {\n      var e_1, _a;\n      _this._throwIfClosed();\n      if (!_this.isStopped) {\n        if (!_this.currentObservers) {\n          _this.currentObservers = Array.from(_this.observers);\n        }\n        try {\n          for (var _b = __values(_this.currentObservers), _c = _b.next(); !_c.done; _c = _b.next()) {\n            var observer = _c.value;\n            observer.next(value);\n          }\n        } catch (e_1_1) {\n          e_1 = {\n            error: e_1_1\n          };\n        } finally {\n          try {\n            if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n          } finally {\n            if (e_1) throw e_1.error;\n          }\n        }\n      }\n    });\n  };\n  Subject.prototype.error = function (err) {\n    var _this = this;\n    errorContext(function () {\n      _this._throwIfClosed();\n      if (!_this.isStopped) {\n        _this.hasError = _this.isStopped = true;\n        _this.thrownError = err;\n        var observers = _this.observers;\n        while (observers.length) {\n          observers.shift().error(err);\n        }\n      }\n    });\n  };\n  Subject.prototype.complete = function () {\n    var _this = this;\n    errorContext(function () {\n      _this._throwIfClosed();\n      if (!_this.isStopped) {\n        _this.isStopped = true;\n        var observers = _this.observers;\n        while (observers.length) {\n          observers.shift().complete();\n        }\n      }\n    });\n  };\n  Subject.prototype.unsubscribe = function () {\n    this.isStopped = this.closed = true;\n    this.observers = this.currentObservers = null;\n  };\n  Object.defineProperty(Subject.prototype, \"observed\", {\n    get: function () {\n      var _a;\n      return ((_a = this.observers) === null || _a === void 0 ? void 0 : _a.length) > 0;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Subject.prototype._trySubscribe = function (subscriber) {\n    this._throwIfClosed();\n    return _super.prototype._trySubscribe.call(this, subscriber);\n  };\n  Subject.prototype._subscribe = function (subscriber) {\n    this._throwIfClosed();\n    this._checkFinalizedStatuses(subscriber);\n    return this._innerSubscribe(subscriber);\n  };\n  Subject.prototype._innerSubscribe = function (subscriber) {\n    var _this = this;\n    var _a = this,\n      hasError = _a.hasError,\n      isStopped = _a.isStopped,\n      observers = _a.observers;\n    if (hasError || isStopped) {\n      return EMPTY_SUBSCRIPTION;\n    }\n    this.currentObservers = null;\n    observers.push(subscriber);\n    return new Subscription(function () {\n      _this.currentObservers = null;\n      arrRemove(observers, subscriber);\n    });\n  };\n  Subject.prototype._checkFinalizedStatuses = function (subscriber) {\n    var _a = this,\n      hasError = _a.hasError,\n      thrownError = _a.thrownError,\n      isStopped = _a.isStopped;\n    if (hasError) {\n      subscriber.error(thrownError);\n    } else if (isStopped) {\n      subscriber.complete();\n    }\n  };\n  Subject.prototype.asObservable = function () {\n    var observable = new Observable();\n    observable.source = this;\n    return observable;\n  };\n  Subject.create = function (destination, source) {\n    return new AnonymousSubject(destination, source);\n  };\n  return Subject;\n}(Observable);\nexport { Subject };\nvar AnonymousSubject = function (_super) {\n  __extends(AnonymousSubject, _super);\n  function AnonymousSubject(destination, source) {\n    var _this = _super.call(this) || this;\n    _this.destination = destination;\n    _this.source = source;\n    return _this;\n  }\n  AnonymousSubject.prototype.next = function (value) {\n    var _a, _b;\n    (_b = (_a = this.destination) === null || _a === void 0 ? void 0 : _a.next) === null || _b === void 0 ? void 0 : _b.call(_a, value);\n  };\n  AnonymousSubject.prototype.error = function (err) {\n    var _a, _b;\n    (_b = (_a = this.destination) === null || _a === void 0 ? void 0 : _a.error) === null || _b === void 0 ? void 0 : _b.call(_a, err);\n  };\n  AnonymousSubject.prototype.complete = function () {\n    var _a, _b;\n    (_b = (_a = this.destination) === null || _a === void 0 ? void 0 : _a.complete) === null || _b === void 0 ? void 0 : _b.call(_a);\n  };\n  AnonymousSubject.prototype._subscribe = function (subscriber) {\n    var _a, _b;\n    return (_b = (_a = this.source) === null || _a === void 0 ? void 0 : _a.subscribe(subscriber)) !== null && _b !== void 0 ? _b : EMPTY_SUBSCRIPTION;\n  };\n  return AnonymousSubject;\n}(Subject);\nexport { AnonymousSubject };", "map": {"version": 3, "names": ["Observable", "Subscription", "EMPTY_SUBSCRIPTION", "ObjectUnsubscribedError", "arr<PERSON><PERSON><PERSON>", "errorContext", "Subject", "_super", "__extends", "_this", "call", "closed", "currentObservers", "observers", "isStopped", "<PERSON><PERSON><PERSON><PERSON>", "thrownError", "prototype", "lift", "operator", "subject", "AnonymousSubject", "_throwIfClosed", "next", "value", "Array", "from", "_b", "__values", "_c", "done", "observer", "error", "err", "length", "shift", "complete", "unsubscribe", "Object", "defineProperty", "get", "_a", "_trySubscribe", "subscriber", "_subscribe", "_checkFinalizedStatuses", "_innerSubscribe", "push", "asObservable", "observable", "source", "create", "destination", "subscribe"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\Subject.ts"], "sourcesContent": ["import { Operator } from './Operator';\nimport { Observable } from './Observable';\nimport { Subscriber } from './Subscriber';\nimport { Subscription, EMPTY_SUBSCRIPTION } from './Subscription';\nimport { Observer, SubscriptionLike, TeardownLogic } from './types';\nimport { ObjectUnsubscribedError } from './util/ObjectUnsubscribedError';\nimport { arrRemove } from './util/arrRemove';\nimport { errorContext } from './util/errorContext';\n\n/**\n * A Subject is a special type of Observable that allows values to be\n * multicasted to many Observers. Subjects are like EventEmitters.\n *\n * Every Subject is an Observable and an Observer. You can subscribe to a\n * Subject, and you can call next to feed values as well as error and complete.\n */\nexport class Subject<T> extends Observable<T> implements SubscriptionLike {\n  closed = false;\n\n  private currentObservers: Observer<T>[] | null = null;\n\n  /** @deprecated Internal implementation detail, do not use directly. Will be made internal in v8. */\n  observers: Observer<T>[] = [];\n  /** @deprecated Internal implementation detail, do not use directly. Will be made internal in v8. */\n  isStopped = false;\n  /** @deprecated Internal implementation detail, do not use directly. Will be made internal in v8. */\n  hasError = false;\n  /** @deprecated Internal implementation detail, do not use directly. Will be made internal in v8. */\n  thrownError: any = null;\n\n  /**\n   * Creates a \"subject\" by basically gluing an observer to an observable.\n   *\n   * @deprecated Recommended you do not use. Will be removed at some point in the future. Plans for replacement still under discussion.\n   */\n  static create: (...args: any[]) => any = <T>(destination: Observer<T>, source: Observable<T>): AnonymousSubject<T> => {\n    return new AnonymousSubject<T>(destination, source);\n  };\n\n  constructor() {\n    // NOTE: This must be here to obscure Observable's constructor.\n    super();\n  }\n\n  /** @deprecated Internal implementation detail, do not use directly. Will be made internal in v8. */\n  lift<R>(operator: Operator<T, R>): Observable<R> {\n    const subject = new AnonymousSubject(this, this);\n    subject.operator = operator as any;\n    return subject as any;\n  }\n\n  /** @internal */\n  protected _throwIfClosed() {\n    if (this.closed) {\n      throw new ObjectUnsubscribedError();\n    }\n  }\n\n  next(value: T) {\n    errorContext(() => {\n      this._throwIfClosed();\n      if (!this.isStopped) {\n        if (!this.currentObservers) {\n          this.currentObservers = Array.from(this.observers);\n        }\n        for (const observer of this.currentObservers) {\n          observer.next(value);\n        }\n      }\n    });\n  }\n\n  error(err: any) {\n    errorContext(() => {\n      this._throwIfClosed();\n      if (!this.isStopped) {\n        this.hasError = this.isStopped = true;\n        this.thrownError = err;\n        const { observers } = this;\n        while (observers.length) {\n          observers.shift()!.error(err);\n        }\n      }\n    });\n  }\n\n  complete() {\n    errorContext(() => {\n      this._throwIfClosed();\n      if (!this.isStopped) {\n        this.isStopped = true;\n        const { observers } = this;\n        while (observers.length) {\n          observers.shift()!.complete();\n        }\n      }\n    });\n  }\n\n  unsubscribe() {\n    this.isStopped = this.closed = true;\n    this.observers = this.currentObservers = null!;\n  }\n\n  get observed() {\n    return this.observers?.length > 0;\n  }\n\n  /** @internal */\n  protected _trySubscribe(subscriber: Subscriber<T>): TeardownLogic {\n    this._throwIfClosed();\n    return super._trySubscribe(subscriber);\n  }\n\n  /** @internal */\n  protected _subscribe(subscriber: Subscriber<T>): Subscription {\n    this._throwIfClosed();\n    this._checkFinalizedStatuses(subscriber);\n    return this._innerSubscribe(subscriber);\n  }\n\n  /** @internal */\n  protected _innerSubscribe(subscriber: Subscriber<any>) {\n    const { hasError, isStopped, observers } = this;\n    if (hasError || isStopped) {\n      return EMPTY_SUBSCRIPTION;\n    }\n    this.currentObservers = null;\n    observers.push(subscriber);\n    return new Subscription(() => {\n      this.currentObservers = null;\n      arrRemove(observers, subscriber);\n    });\n  }\n\n  /** @internal */\n  protected _checkFinalizedStatuses(subscriber: Subscriber<any>) {\n    const { hasError, thrownError, isStopped } = this;\n    if (hasError) {\n      subscriber.error(thrownError);\n    } else if (isStopped) {\n      subscriber.complete();\n    }\n  }\n\n  /**\n   * Creates a new Observable with this Subject as the source. You can do this\n   * to create custom Observer-side logic of the Subject and conceal it from\n   * code that uses the Observable.\n   * @return Observable that this Subject casts to.\n   */\n  asObservable(): Observable<T> {\n    const observable: any = new Observable<T>();\n    observable.source = this;\n    return observable;\n  }\n}\n\nexport class AnonymousSubject<T> extends Subject<T> {\n  constructor(\n    /** @deprecated Internal implementation detail, do not use directly. Will be made internal in v8. */\n    public destination?: Observer<T>,\n    source?: Observable<T>\n  ) {\n    super();\n    this.source = source;\n  }\n\n  next(value: T) {\n    this.destination?.next?.(value);\n  }\n\n  error(err: any) {\n    this.destination?.error?.(err);\n  }\n\n  complete() {\n    this.destination?.complete?.();\n  }\n\n  /** @internal */\n  protected _subscribe(subscriber: Subscriber<T>): Subscription {\n    return this.source?.subscribe(subscriber) ?? EMPTY_SUBSCRIPTION;\n  }\n}\n"], "mappings": ";AACA,SAASA,UAAU,QAAQ,cAAc;AAEzC,SAASC,YAAY,EAAEC,kBAAkB,QAAQ,gBAAgB;AAEjE,SAASC,uBAAuB,QAAQ,gCAAgC;AACxE,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,YAAY,QAAQ,qBAAqB;AASlD,IAAAC,OAAA,aAAAC,MAAA;EAAgCC,SAAA,CAAAF,OAAA,EAAAC,MAAA;EAuB9B,SAAAD,QAAA;IAAA,IAAAG,KAAA,GAEEF,MAAA,CAAAG,IAAA,MAAO;IAxBTD,KAAA,CAAAE,MAAM,GAAG,KAAK;IAENF,KAAA,CAAAG,gBAAgB,GAAyB,IAAI;IAGrDH,KAAA,CAAAI,SAAS,GAAkB,EAAE;IAE7BJ,KAAA,CAAAK,SAAS,GAAG,KAAK;IAEjBL,KAAA,CAAAM,QAAQ,GAAG,KAAK;IAEhBN,KAAA,CAAAO,WAAW,GAAQ,IAAI;;EAcvB;EAGAV,OAAA,CAAAW,SAAA,CAAAC,IAAI,GAAJ,UAAQC,QAAwB;IAC9B,IAAMC,OAAO,GAAG,IAAIC,gBAAgB,CAAC,IAAI,EAAE,IAAI,CAAC;IAChDD,OAAO,CAACD,QAAQ,GAAGA,QAAe;IAClC,OAAOC,OAAc;EACvB,CAAC;EAGSd,OAAA,CAAAW,SAAA,CAAAK,cAAc,GAAxB;IACE,IAAI,IAAI,CAACX,MAAM,EAAE;MACf,MAAM,IAAIR,uBAAuB,EAAE;;EAEvC,CAAC;EAEDG,OAAA,CAAAW,SAAA,CAAAM,IAAI,GAAJ,UAAKC,KAAQ;IAAb,IAAAf,KAAA;IACEJ,YAAY,CAAC;;MACXI,KAAI,CAACa,cAAc,EAAE;MACrB,IAAI,CAACb,KAAI,CAACK,SAAS,EAAE;QACnB,IAAI,CAACL,KAAI,CAACG,gBAAgB,EAAE;UAC1BH,KAAI,CAACG,gBAAgB,GAAGa,KAAK,CAACC,IAAI,CAACjB,KAAI,CAACI,SAAS,CAAC;;;UAEpD,KAAuB,IAAAc,EAAA,GAAAC,QAAA,CAAAnB,KAAI,CAACG,gBAAgB,GAAAiB,EAAA,GAAAF,EAAA,CAAAJ,IAAA,KAAAM,EAAA,CAAAC,IAAA,EAAAD,EAAA,GAAAF,EAAA,CAAAJ,IAAA,IAAE;YAAzC,IAAMQ,QAAQ,GAAAF,EAAA,CAAAL,KAAA;YACjBO,QAAQ,CAACR,IAAI,CAACC,KAAK,CAAC;;;;;;;;;;;;;;IAG1B,CAAC,CAAC;EACJ,CAAC;EAEDlB,OAAA,CAAAW,SAAA,CAAAe,KAAK,GAAL,UAAMC,GAAQ;IAAd,IAAAxB,KAAA;IACEJ,YAAY,CAAC;MACXI,KAAI,CAACa,cAAc,EAAE;MACrB,IAAI,CAACb,KAAI,CAACK,SAAS,EAAE;QACnBL,KAAI,CAACM,QAAQ,GAAGN,KAAI,CAACK,SAAS,GAAG,IAAI;QACrCL,KAAI,CAACO,WAAW,GAAGiB,GAAG;QACd,IAAApB,SAAS,GAAKJ,KAAI,CAAAI,SAAT;QACjB,OAAOA,SAAS,CAACqB,MAAM,EAAE;UACvBrB,SAAS,CAACsB,KAAK,EAAG,CAACH,KAAK,CAACC,GAAG,CAAC;;;IAGnC,CAAC,CAAC;EACJ,CAAC;EAED3B,OAAA,CAAAW,SAAA,CAAAmB,QAAQ,GAAR;IAAA,IAAA3B,KAAA;IACEJ,YAAY,CAAC;MACXI,KAAI,CAACa,cAAc,EAAE;MACrB,IAAI,CAACb,KAAI,CAACK,SAAS,EAAE;QACnBL,KAAI,CAACK,SAAS,GAAG,IAAI;QACb,IAAAD,SAAS,GAAKJ,KAAI,CAAAI,SAAT;QACjB,OAAOA,SAAS,CAACqB,MAAM,EAAE;UACvBrB,SAAS,CAACsB,KAAK,EAAG,CAACC,QAAQ,EAAE;;;IAGnC,CAAC,CAAC;EACJ,CAAC;EAED9B,OAAA,CAAAW,SAAA,CAAAoB,WAAW,GAAX;IACE,IAAI,CAACvB,SAAS,GAAG,IAAI,CAACH,MAAM,GAAG,IAAI;IACnC,IAAI,CAACE,SAAS,GAAG,IAAI,CAACD,gBAAgB,GAAG,IAAK;EAChD,CAAC;EAED0B,MAAA,CAAAC,cAAA,CAAIjC,OAAA,CAAAW,SAAA,YAAQ;SAAZ,SAAAuB,CAAA;;MACE,OAAO,EAAAC,EAAA,OAAI,CAAC5B,SAAS,cAAA4B,EAAA,uBAAAA,EAAA,CAAEP,MAAM,IAAG,CAAC;IACnC,CAAC;;;;EAGS5B,OAAA,CAAAW,SAAA,CAAAyB,aAAa,GAAvB,UAAwBC,UAAyB;IAC/C,IAAI,CAACrB,cAAc,EAAE;IACrB,OAAOf,MAAA,CAAAU,SAAA,CAAMyB,aAAa,CAAAhC,IAAA,OAACiC,UAAU,CAAC;EACxC,CAAC;EAGSrC,OAAA,CAAAW,SAAA,CAAA2B,UAAU,GAApB,UAAqBD,UAAyB;IAC5C,IAAI,CAACrB,cAAc,EAAE;IACrB,IAAI,CAACuB,uBAAuB,CAACF,UAAU,CAAC;IACxC,OAAO,IAAI,CAACG,eAAe,CAACH,UAAU,CAAC;EACzC,CAAC;EAGSrC,OAAA,CAAAW,SAAA,CAAA6B,eAAe,GAAzB,UAA0BH,UAA2B;IAArD,IAAAlC,KAAA;IACQ,IAAAgC,EAAA,GAAqC,IAAI;MAAvC1B,QAAQ,GAAA0B,EAAA,CAAA1B,QAAA;MAAED,SAAS,GAAA2B,EAAA,CAAA3B,SAAA;MAAED,SAAS,GAAA4B,EAAA,CAAA5B,SAAS;IAC/C,IAAIE,QAAQ,IAAID,SAAS,EAAE;MACzB,OAAOZ,kBAAkB;;IAE3B,IAAI,CAACU,gBAAgB,GAAG,IAAI;IAC5BC,SAAS,CAACkC,IAAI,CAACJ,UAAU,CAAC;IAC1B,OAAO,IAAI1C,YAAY,CAAC;MACtBQ,KAAI,CAACG,gBAAgB,GAAG,IAAI;MAC5BR,SAAS,CAACS,SAAS,EAAE8B,UAAU,CAAC;IAClC,CAAC,CAAC;EACJ,CAAC;EAGSrC,OAAA,CAAAW,SAAA,CAAA4B,uBAAuB,GAAjC,UAAkCF,UAA2B;IACrD,IAAAF,EAAA,GAAuC,IAAI;MAAzC1B,QAAQ,GAAA0B,EAAA,CAAA1B,QAAA;MAAEC,WAAW,GAAAyB,EAAA,CAAAzB,WAAA;MAAEF,SAAS,GAAA2B,EAAA,CAAA3B,SAAS;IACjD,IAAIC,QAAQ,EAAE;MACZ4B,UAAU,CAACX,KAAK,CAAChB,WAAW,CAAC;KAC9B,MAAM,IAAIF,SAAS,EAAE;MACpB6B,UAAU,CAACP,QAAQ,EAAE;;EAEzB,CAAC;EAQD9B,OAAA,CAAAW,SAAA,CAAA+B,YAAY,GAAZ;IACE,IAAMC,UAAU,GAAQ,IAAIjD,UAAU,EAAK;IAC3CiD,UAAU,CAACC,MAAM,GAAG,IAAI;IACxB,OAAOD,UAAU;EACnB,CAAC;EAxHM3C,OAAA,CAAA6C,MAAM,GAA4B,UAAIC,WAAwB,EAAEF,MAAqB;IAC1F,OAAO,IAAI7B,gBAAgB,CAAI+B,WAAW,EAAEF,MAAM,CAAC;EACrD,CAAC;EAuHH,OAAA5C,OAAC;CAAA,CA5I+BN,UAAU;SAA7BM,OAAO;AA8IpB,IAAAe,gBAAA,aAAAd,MAAA;EAAyCC,SAAA,CAAAa,gBAAA,EAAAd,MAAA;EACvC,SAAAc,iBAES+B,WAAyB,EAChCF,MAAsB;IAHxB,IAAAzC,KAAA,GAKEF,MAAA,CAAAG,IAAA,MAAO;IAHAD,KAAA,CAAA2C,WAAW,GAAXA,WAAW;IAIlB3C,KAAI,CAACyC,MAAM,GAAGA,MAAM;;EACtB;EAEA7B,gBAAA,CAAAJ,SAAA,CAAAM,IAAI,GAAJ,UAAKC,KAAQ;;IACX,CAAAG,EAAA,IAAAc,EAAA,OAAI,CAACW,WAAW,cAAAX,EAAA,uBAAAA,EAAA,CAAElB,IAAI,cAAAI,EAAA,uBAAAA,EAAA,CAAAjB,IAAA,CAAA+B,EAAA,EAAGjB,KAAK,CAAC;EACjC,CAAC;EAEDH,gBAAA,CAAAJ,SAAA,CAAAe,KAAK,GAAL,UAAMC,GAAQ;;IACZ,CAAAN,EAAA,IAAAc,EAAA,OAAI,CAACW,WAAW,cAAAX,EAAA,uBAAAA,EAAA,CAAET,KAAK,cAAAL,EAAA,uBAAAA,EAAA,CAAAjB,IAAA,CAAA+B,EAAA,EAAGR,GAAG,CAAC;EAChC,CAAC;EAEDZ,gBAAA,CAAAJ,SAAA,CAAAmB,QAAQ,GAAR;;IACE,CAAAT,EAAA,IAAAc,EAAA,OAAI,CAACW,WAAW,cAAAX,EAAA,uBAAAA,EAAA,CAAEL,QAAQ,cAAAT,EAAA,uBAAAA,EAAA,CAAAjB,IAAA,CAAA+B,EAAA,CAAI;EAChC,CAAC;EAGSpB,gBAAA,CAAAJ,SAAA,CAAA2B,UAAU,GAApB,UAAqBD,UAAyB;;IAC5C,OAAO,CAAAhB,EAAA,IAAAc,EAAA,OAAI,CAACS,MAAM,cAAAT,EAAA,uBAAAA,EAAA,CAAEY,SAAS,CAACV,UAAU,CAAC,cAAAhB,EAAA,cAAAA,EAAA,GAAIzB,kBAAkB;EACjE,CAAC;EACH,OAAAmB,gBAAC;AAAD,CAAC,CA1BwCf,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}