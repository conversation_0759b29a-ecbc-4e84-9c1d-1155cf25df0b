{"ast": null, "code": "import { __read, __spreadArray, __values } from \"tslib\";\nimport { isFunction } from './util/isFunction';\nimport { UnsubscriptionError } from './util/UnsubscriptionError';\nimport { arrRemove } from './util/arrRemove';\nvar Subscription = function () {\n  function Subscription(initialTeardown) {\n    this.initialTeardown = initialTeardown;\n    this.closed = false;\n    this._parentage = null;\n    this._finalizers = null;\n  }\n  Subscription.prototype.unsubscribe = function () {\n    var e_1, _a, e_2, _b;\n    var errors;\n    if (!this.closed) {\n      this.closed = true;\n      var _parentage = this._parentage;\n      if (_parentage) {\n        this._parentage = null;\n        if (Array.isArray(_parentage)) {\n          try {\n            for (var _parentage_1 = __values(_parentage), _parentage_1_1 = _parentage_1.next(); !_parentage_1_1.done; _parentage_1_1 = _parentage_1.next()) {\n              var parent_1 = _parentage_1_1.value;\n              parent_1.remove(this);\n            }\n          } catch (e_1_1) {\n            e_1 = {\n              error: e_1_1\n            };\n          } finally {\n            try {\n              if (_parentage_1_1 && !_parentage_1_1.done && (_a = _parentage_1.return)) _a.call(_parentage_1);\n            } finally {\n              if (e_1) throw e_1.error;\n            }\n          }\n        } else {\n          _parentage.remove(this);\n        }\n      }\n      var initialFinalizer = this.initialTeardown;\n      if (isFunction(initialFinalizer)) {\n        try {\n          initialFinalizer();\n        } catch (e) {\n          errors = e instanceof UnsubscriptionError ? e.errors : [e];\n        }\n      }\n      var _finalizers = this._finalizers;\n      if (_finalizers) {\n        this._finalizers = null;\n        try {\n          for (var _finalizers_1 = __values(_finalizers), _finalizers_1_1 = _finalizers_1.next(); !_finalizers_1_1.done; _finalizers_1_1 = _finalizers_1.next()) {\n            var finalizer = _finalizers_1_1.value;\n            try {\n              execFinalizer(finalizer);\n            } catch (err) {\n              errors = errors !== null && errors !== void 0 ? errors : [];\n              if (err instanceof UnsubscriptionError) {\n                errors = __spreadArray(__spreadArray([], __read(errors)), __read(err.errors));\n              } else {\n                errors.push(err);\n              }\n            }\n          }\n        } catch (e_2_1) {\n          e_2 = {\n            error: e_2_1\n          };\n        } finally {\n          try {\n            if (_finalizers_1_1 && !_finalizers_1_1.done && (_b = _finalizers_1.return)) _b.call(_finalizers_1);\n          } finally {\n            if (e_2) throw e_2.error;\n          }\n        }\n      }\n      if (errors) {\n        throw new UnsubscriptionError(errors);\n      }\n    }\n  };\n  Subscription.prototype.add = function (teardown) {\n    var _a;\n    if (teardown && teardown !== this) {\n      if (this.closed) {\n        execFinalizer(teardown);\n      } else {\n        if (teardown instanceof Subscription) {\n          if (teardown.closed || teardown._hasParent(this)) {\n            return;\n          }\n          teardown._addParent(this);\n        }\n        (this._finalizers = (_a = this._finalizers) !== null && _a !== void 0 ? _a : []).push(teardown);\n      }\n    }\n  };\n  Subscription.prototype._hasParent = function (parent) {\n    var _parentage = this._parentage;\n    return _parentage === parent || Array.isArray(_parentage) && _parentage.includes(parent);\n  };\n  Subscription.prototype._addParent = function (parent) {\n    var _parentage = this._parentage;\n    this._parentage = Array.isArray(_parentage) ? (_parentage.push(parent), _parentage) : _parentage ? [_parentage, parent] : parent;\n  };\n  Subscription.prototype._removeParent = function (parent) {\n    var _parentage = this._parentage;\n    if (_parentage === parent) {\n      this._parentage = null;\n    } else if (Array.isArray(_parentage)) {\n      arrRemove(_parentage, parent);\n    }\n  };\n  Subscription.prototype.remove = function (teardown) {\n    var _finalizers = this._finalizers;\n    _finalizers && arrRemove(_finalizers, teardown);\n    if (teardown instanceof Subscription) {\n      teardown._removeParent(this);\n    }\n  };\n  Subscription.EMPTY = function () {\n    var empty = new Subscription();\n    empty.closed = true;\n    return empty;\n  }();\n  return Subscription;\n}();\nexport { Subscription };\nexport var EMPTY_SUBSCRIPTION = Subscription.EMPTY;\nexport function isSubscription(value) {\n  return value instanceof Subscription || value && 'closed' in value && isFunction(value.remove) && isFunction(value.add) && isFunction(value.unsubscribe);\n}\nfunction execFinalizer(finalizer) {\n  if (isFunction(finalizer)) {\n    finalizer();\n  } else {\n    finalizer.unsubscribe();\n  }\n}", "map": {"version": 3, "names": ["isFunction", "UnsubscriptionError", "arr<PERSON><PERSON><PERSON>", "Subscription", "initialTeardown", "closed", "_parentage", "_finalizers", "prototype", "unsubscribe", "errors", "Array", "isArray", "_parentage_1", "__values", "_parentage_1_1", "next", "done", "parent_1", "value", "remove", "initialFinalizer", "e", "_finalizers_1", "_finalizers_1_1", "finalizer", "execFinalizer", "err", "__spread<PERSON><PERSON>y", "__read", "push", "add", "teardown", "_hasParent", "_addParent", "_a", "parent", "includes", "_removeParent", "EMPTY", "empty", "EMPTY_SUBSCRIPTION", "isSubscription"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\Subscription.ts"], "sourcesContent": ["import { isFunction } from './util/isFunction';\nimport { UnsubscriptionError } from './util/UnsubscriptionError';\nimport { SubscriptionLike, TeardownLogic, Unsubscribable } from './types';\nimport { arrRemove } from './util/arrRemove';\n\n/**\n * Represents a disposable resource, such as the execution of an Observable. A\n * Subscription has one important method, `unsubscribe`, that takes no argument\n * and just disposes the resource held by the subscription.\n *\n * Additionally, subscriptions may be grouped together through the `add()`\n * method, which will attach a child Subscription to the current Subscription.\n * When a Subscription is unsubscribed, all its children (and its grandchildren)\n * will be unsubscribed as well.\n */\nexport class Subscription implements SubscriptionLike {\n  public static EMPTY = (() => {\n    const empty = new Subscription();\n    empty.closed = true;\n    return empty;\n  })();\n\n  /**\n   * A flag to indicate whether this Subscription has already been unsubscribed.\n   */\n  public closed = false;\n\n  private _parentage: Subscription[] | Subscription | null = null;\n\n  /**\n   * The list of registered finalizers to execute upon unsubscription. Adding and removing from this\n   * list occurs in the {@link #add} and {@link #remove} methods.\n   */\n  private _finalizers: Exclude<TeardownLogic, void>[] | null = null;\n\n  /**\n   * @param initialTeardown A function executed first as part of the finalization\n   * process that is kicked off when {@link #unsubscribe} is called.\n   */\n  constructor(private initialTeardown?: () => void) {}\n\n  /**\n   * Disposes the resources held by the subscription. May, for instance, cancel\n   * an ongoing Observable execution or cancel any other type of work that\n   * started when the Subscription was created.\n   */\n  unsubscribe(): void {\n    let errors: any[] | undefined;\n\n    if (!this.closed) {\n      this.closed = true;\n\n      // Remove this from it's parents.\n      const { _parentage } = this;\n      if (_parentage) {\n        this._parentage = null;\n        if (Array.isArray(_parentage)) {\n          for (const parent of _parentage) {\n            parent.remove(this);\n          }\n        } else {\n          _parentage.remove(this);\n        }\n      }\n\n      const { initialTeardown: initialFinalizer } = this;\n      if (isFunction(initialFinalizer)) {\n        try {\n          initialFinalizer();\n        } catch (e) {\n          errors = e instanceof UnsubscriptionError ? e.errors : [e];\n        }\n      }\n\n      const { _finalizers } = this;\n      if (_finalizers) {\n        this._finalizers = null;\n        for (const finalizer of _finalizers) {\n          try {\n            execFinalizer(finalizer);\n          } catch (err) {\n            errors = errors ?? [];\n            if (err instanceof UnsubscriptionError) {\n              errors = [...errors, ...err.errors];\n            } else {\n              errors.push(err);\n            }\n          }\n        }\n      }\n\n      if (errors) {\n        throw new UnsubscriptionError(errors);\n      }\n    }\n  }\n\n  /**\n   * Adds a finalizer to this subscription, so that finalization will be unsubscribed/called\n   * when this subscription is unsubscribed. If this subscription is already {@link #closed},\n   * because it has already been unsubscribed, then whatever finalizer is passed to it\n   * will automatically be executed (unless the finalizer itself is also a closed subscription).\n   *\n   * Closed Subscriptions cannot be added as finalizers to any subscription. Adding a closed\n   * subscription to a any subscription will result in no operation. (A noop).\n   *\n   * Adding a subscription to itself, or adding `null` or `undefined` will not perform any\n   * operation at all. (A noop).\n   *\n   * `Subscription` instances that are added to this instance will automatically remove themselves\n   * if they are unsubscribed. Functions and {@link Unsubscribable} objects that you wish to remove\n   * will need to be removed manually with {@link #remove}\n   *\n   * @param teardown The finalization logic to add to this subscription.\n   */\n  add(teardown: TeardownLogic): void {\n    // Only add the finalizer if it's not undefined\n    // and don't add a subscription to itself.\n    if (teardown && teardown !== this) {\n      if (this.closed) {\n        // If this subscription is already closed,\n        // execute whatever finalizer is handed to it automatically.\n        execFinalizer(teardown);\n      } else {\n        if (teardown instanceof Subscription) {\n          // We don't add closed subscriptions, and we don't add the same subscription\n          // twice. Subscription unsubscribe is idempotent.\n          if (teardown.closed || teardown._hasParent(this)) {\n            return;\n          }\n          teardown._addParent(this);\n        }\n        (this._finalizers = this._finalizers ?? []).push(teardown);\n      }\n    }\n  }\n\n  /**\n   * Checks to see if a this subscription already has a particular parent.\n   * This will signal that this subscription has already been added to the parent in question.\n   * @param parent the parent to check for\n   */\n  private _hasParent(parent: Subscription) {\n    const { _parentage } = this;\n    return _parentage === parent || (Array.isArray(_parentage) && _parentage.includes(parent));\n  }\n\n  /**\n   * Adds a parent to this subscription so it can be removed from the parent if it\n   * unsubscribes on it's own.\n   *\n   * NOTE: THIS ASSUMES THAT {@link _hasParent} HAS ALREADY BEEN CHECKED.\n   * @param parent The parent subscription to add\n   */\n  private _addParent(parent: Subscription) {\n    const { _parentage } = this;\n    this._parentage = Array.isArray(_parentage) ? (_parentage.push(parent), _parentage) : _parentage ? [_parentage, parent] : parent;\n  }\n\n  /**\n   * Called on a child when it is removed via {@link #remove}.\n   * @param parent The parent to remove\n   */\n  private _removeParent(parent: Subscription) {\n    const { _parentage } = this;\n    if (_parentage === parent) {\n      this._parentage = null;\n    } else if (Array.isArray(_parentage)) {\n      arrRemove(_parentage, parent);\n    }\n  }\n\n  /**\n   * Removes a finalizer from this subscription that was previously added with the {@link #add} method.\n   *\n   * Note that `Subscription` instances, when unsubscribed, will automatically remove themselves\n   * from every other `Subscription` they have been added to. This means that using the `remove` method\n   * is not a common thing and should be used thoughtfully.\n   *\n   * If you add the same finalizer instance of a function or an unsubscribable object to a `Subscription` instance\n   * more than once, you will need to call `remove` the same number of times to remove all instances.\n   *\n   * All finalizer instances are removed to free up memory upon unsubscription.\n   *\n   * @param teardown The finalizer to remove from this subscription\n   */\n  remove(teardown: Exclude<TeardownLogic, void>): void {\n    const { _finalizers } = this;\n    _finalizers && arrRemove(_finalizers, teardown);\n\n    if (teardown instanceof Subscription) {\n      teardown._removeParent(this);\n    }\n  }\n}\n\nexport const EMPTY_SUBSCRIPTION = Subscription.EMPTY;\n\nexport function isSubscription(value: any): value is Subscription {\n  return (\n    value instanceof Subscription ||\n    (value && 'closed' in value && isFunction(value.remove) && isFunction(value.add) && isFunction(value.unsubscribe))\n  );\n}\n\nfunction execFinalizer(finalizer: Unsubscribable | (() => void)) {\n  if (isFunction(finalizer)) {\n    finalizer();\n  } else {\n    finalizer.unsubscribe();\n  }\n}\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,mBAAmB;AAC9C,SAASC,mBAAmB,QAAQ,4BAA4B;AAEhE,SAASC,SAAS,QAAQ,kBAAkB;AAY5C,IAAAC,YAAA;EAwBE,SAAAA,aAAoBC,eAA4B;IAA5B,KAAAA,eAAe,GAAfA,eAAe;IAd5B,KAAAC,MAAM,GAAG,KAAK;IAEb,KAAAC,UAAU,GAAyC,IAAI;IAMvD,KAAAC,WAAW,GAA0C,IAAI;EAMd;EAOnDJ,YAAA,CAAAK,SAAA,CAAAC,WAAW,GAAX;;IACE,IAAIC,MAAyB;IAE7B,IAAI,CAAC,IAAI,CAACL,MAAM,EAAE;MAChB,IAAI,CAACA,MAAM,GAAG,IAAI;MAGV,IAAAC,UAAU,GAAK,IAAI,CAAAA,UAAT;MAClB,IAAIA,UAAU,EAAE;QACd,IAAI,CAACA,UAAU,GAAG,IAAI;QACtB,IAAIK,KAAK,CAACC,OAAO,CAACN,UAAU,CAAC,EAAE;;YAC7B,KAAqB,IAAAO,YAAA,GAAAC,QAAA,CAAAR,UAAU,GAAAS,cAAA,GAAAF,YAAA,CAAAG,IAAA,KAAAD,cAAA,CAAAE,IAAA,EAAAF,cAAA,GAAAF,YAAA,CAAAG,IAAA,IAAE;cAA5B,IAAME,QAAM,GAAAH,cAAA,CAAAI,KAAA;cACfD,QAAM,CAACE,MAAM,CAAC,IAAI,CAAC;;;;;;;;;;;;;SAEtB,MAAM;UACLd,UAAU,CAACc,MAAM,CAAC,IAAI,CAAC;;;MAInB,IAAiBC,gBAAgB,GAAK,IAAI,CAAAjB,eAAT;MACzC,IAAIJ,UAAU,CAACqB,gBAAgB,CAAC,EAAE;QAChC,IAAI;UACFA,gBAAgB,EAAE;SACnB,CAAC,OAAOC,CAAC,EAAE;UACVZ,MAAM,GAAGY,CAAC,YAAYrB,mBAAmB,GAAGqB,CAAC,CAACZ,MAAM,GAAG,CAACY,CAAC,CAAC;;;MAItD,IAAAf,WAAW,GAAK,IAAI,CAAAA,WAAT;MACnB,IAAIA,WAAW,EAAE;QACf,IAAI,CAACA,WAAW,GAAG,IAAI;;UACvB,KAAwB,IAAAgB,aAAA,GAAAT,QAAA,CAAAP,WAAW,GAAAiB,eAAA,GAAAD,aAAA,CAAAP,IAAA,KAAAQ,eAAA,CAAAP,IAAA,EAAAO,eAAA,GAAAD,aAAA,CAAAP,IAAA,IAAE;YAAhC,IAAMS,SAAS,GAAAD,eAAA,CAAAL,KAAA;YAClB,IAAI;cACFO,aAAa,CAACD,SAAS,CAAC;aACzB,CAAC,OAAOE,GAAG,EAAE;cACZjB,MAAM,GAAGA,MAAM,aAANA,MAAM,cAANA,MAAM,GAAI,EAAE;cACrB,IAAIiB,GAAG,YAAY1B,mBAAmB,EAAE;gBACtCS,MAAM,GAAAkB,aAAA,CAAAA,aAAA,KAAAC,MAAA,CAAOnB,MAAM,IAAAmB,MAAA,CAAKF,GAAG,CAACjB,MAAM,EAAC;eACpC,MAAM;gBACLA,MAAM,CAACoB,IAAI,CAACH,GAAG,CAAC;;;;;;;;;;;;;;;;MAMxB,IAAIjB,MAAM,EAAE;QACV,MAAM,IAAIT,mBAAmB,CAACS,MAAM,CAAC;;;EAG3C,CAAC;EAoBDP,YAAA,CAAAK,SAAA,CAAAuB,GAAG,GAAH,UAAIC,QAAuB;;IAGzB,IAAIA,QAAQ,IAAIA,QAAQ,KAAK,IAAI,EAAE;MACjC,IAAI,IAAI,CAAC3B,MAAM,EAAE;QAGfqB,aAAa,CAACM,QAAQ,CAAC;OACxB,MAAM;QACL,IAAIA,QAAQ,YAAY7B,YAAY,EAAE;UAGpC,IAAI6B,QAAQ,CAAC3B,MAAM,IAAI2B,QAAQ,CAACC,UAAU,CAAC,IAAI,CAAC,EAAE;YAChD;;UAEFD,QAAQ,CAACE,UAAU,CAAC,IAAI,CAAC;;QAE3B,CAAC,IAAI,CAAC3B,WAAW,GAAG,CAAA4B,EAAA,OAAI,CAAC5B,WAAW,cAAA4B,EAAA,cAAAA,EAAA,GAAI,EAAE,EAAEL,IAAI,CAACE,QAAQ,CAAC;;;EAGhE,CAAC;EAOO7B,YAAA,CAAAK,SAAA,CAAAyB,UAAU,GAAlB,UAAmBG,MAAoB;IAC7B,IAAA9B,UAAU,GAAK,IAAI,CAAAA,UAAT;IAClB,OAAOA,UAAU,KAAK8B,MAAM,IAAKzB,KAAK,CAACC,OAAO,CAACN,UAAU,CAAC,IAAIA,UAAU,CAAC+B,QAAQ,CAACD,MAAM,CAAE;EAC5F,CAAC;EASOjC,YAAA,CAAAK,SAAA,CAAA0B,UAAU,GAAlB,UAAmBE,MAAoB;IAC7B,IAAA9B,UAAU,GAAK,IAAI,CAAAA,UAAT;IAClB,IAAI,CAACA,UAAU,GAAGK,KAAK,CAACC,OAAO,CAACN,UAAU,CAAC,IAAIA,UAAU,CAACwB,IAAI,CAACM,MAAM,CAAC,EAAE9B,UAAU,IAAIA,UAAU,GAAG,CAACA,UAAU,EAAE8B,MAAM,CAAC,GAAGA,MAAM;EAClI,CAAC;EAMOjC,YAAA,CAAAK,SAAA,CAAA8B,aAAa,GAArB,UAAsBF,MAAoB;IAChC,IAAA9B,UAAU,GAAK,IAAI,CAAAA,UAAT;IAClB,IAAIA,UAAU,KAAK8B,MAAM,EAAE;MACzB,IAAI,CAAC9B,UAAU,GAAG,IAAI;KACvB,MAAM,IAAIK,KAAK,CAACC,OAAO,CAACN,UAAU,CAAC,EAAE;MACpCJ,SAAS,CAACI,UAAU,EAAE8B,MAAM,CAAC;;EAEjC,CAAC;EAgBDjC,YAAA,CAAAK,SAAA,CAAAY,MAAM,GAAN,UAAOY,QAAsC;IACnC,IAAAzB,WAAW,GAAK,IAAI,CAAAA,WAAT;IACnBA,WAAW,IAAIL,SAAS,CAACK,WAAW,EAAEyB,QAAQ,CAAC;IAE/C,IAAIA,QAAQ,YAAY7B,YAAY,EAAE;MACpC6B,QAAQ,CAACM,aAAa,CAAC,IAAI,CAAC;;EAEhC,CAAC;EAjLanC,YAAA,CAAAoC,KAAK,GAAI;IACrB,IAAMC,KAAK,GAAG,IAAIrC,YAAY,EAAE;IAChCqC,KAAK,CAACnC,MAAM,GAAG,IAAI;IACnB,OAAOmC,KAAK;EACd,CAAC,CAAC,CAAE;EA8KN,OAAArC,YAAC;CAAA,CAnLD;SAAaA,YAAY;AAqLzB,OAAO,IAAMsC,kBAAkB,GAAGtC,YAAY,CAACoC,KAAK;AAEpD,OAAM,SAAUG,cAAcA,CAACvB,KAAU;EACvC,OACEA,KAAK,YAAYhB,YAAY,IAC5BgB,KAAK,IAAI,QAAQ,IAAIA,KAAK,IAAInB,UAAU,CAACmB,KAAK,CAACC,MAAM,CAAC,IAAIpB,UAAU,CAACmB,KAAK,CAACY,GAAG,CAAC,IAAI/B,UAAU,CAACmB,KAAK,CAACV,WAAW,CAAE;AAEtH;AAEA,SAASiB,aAAaA,CAACD,SAAwC;EAC7D,IAAIzB,UAAU,CAACyB,SAAS,CAAC,EAAE;IACzBA,SAAS,EAAE;GACZ,MAAM;IACLA,SAAS,CAAChB,WAAW,EAAE;;AAE3B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}