{"ast": null, "code": "import { Observable } from '../Observable';\nimport { noop } from '../util/noop';\nexport var NEVER = new Observable(noop);\nexport function never() {\n  return NEVER;\n}", "map": {"version": 3, "names": ["Observable", "noop", "NEVER", "never"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\observable\\never.ts"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { noop } from '../util/noop';\n\n/**\n * An Observable that emits no items to the Observer and never completes.\n *\n * ![](never.png)\n *\n * A simple Observable that emits neither values nor errors nor the completion\n * notification. It can be used for testing purposes or for composing with other\n * Observables. Please note that by never emitting a complete notification, this\n * Observable keeps the subscription from being disposed automatically.\n * Subscriptions need to be manually disposed.\n *\n * ##  Example\n *\n * Emit the number 7, then never emit anything else (not even complete)\n *\n * ```ts\n * import { NEVER, startWith } from 'rxjs';\n *\n * const info = () => console.log('Will not be called');\n *\n * const result = NEVER.pipe(startWith(7));\n * result.subscribe({\n *   next: x => console.log(x),\n *   error: info,\n *   complete: info\n * });\n * ```\n *\n * @see {@link Observable}\n * @see {@link EMPTY}\n * @see {@link of}\n * @see {@link throwError}\n */\nexport const NEVER = new Observable<never>(noop);\n\n/**\n * @deprecated Replaced with the {@link NEVER} constant. Will be removed in v8.\n */\nexport function never() {\n  return NEVER;\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,IAAI,QAAQ,cAAc;AAmCnC,OAAO,IAAMC,KAAK,GAAG,IAAIF,UAAU,CAAQC,IAAI,CAAC;AAKhD,OAAM,SAAUE,KAAKA,CAAA;EACnB,OAAOD,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}