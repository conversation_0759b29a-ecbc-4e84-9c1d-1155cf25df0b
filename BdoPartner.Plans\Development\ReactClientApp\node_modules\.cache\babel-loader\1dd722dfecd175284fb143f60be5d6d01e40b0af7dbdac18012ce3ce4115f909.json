{"ast": null, "code": "import { mergeMap } from './mergeMap';\nimport { isFunction } from '../util/isFunction';\nexport function mergeMapTo(innerObservable, resultSelector, concurrent) {\n  if (concurrent === void 0) {\n    concurrent = Infinity;\n  }\n  if (isFunction(resultSelector)) {\n    return mergeMap(function () {\n      return innerObservable;\n    }, resultSelector, concurrent);\n  }\n  if (typeof resultSelector === 'number') {\n    concurrent = resultSelector;\n  }\n  return mergeMap(function () {\n    return innerObservable;\n  }, concurrent);\n}", "map": {"version": 3, "names": ["mergeMap", "isFunction", "mergeMapTo", "innerObservable", "resultSelector", "concurrent", "Infinity"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\mergeMapTo.ts"], "sourcesContent": ["import { OperatorFunction, ObservedValueOf, ObservableInput } from '../types';\nimport { mergeMap } from './mergeMap';\nimport { isFunction } from '../util/isFunction';\n\n/** @deprecated Will be removed in v9. Use {@link mergeMap} instead: `mergeMap(() => result)` */\nexport function mergeMapTo<O extends ObservableInput<unknown>>(\n  innerObservable: O,\n  concurrent?: number\n): OperatorFunction<unknown, ObservedValueOf<O>>;\n/**\n * @deprecated The `resultSelector` parameter will be removed in v8. Use an inner `map` instead.\n * Details: https://rxjs.dev/deprecations/resultSelector\n */\nexport function mergeMapTo<T, R, O extends ObservableInput<unknown>>(\n  innerObservable: O,\n  resultSelector: (outerValue: T, innerValue: ObservedValueOf<O>, outerIndex: number, innerIndex: number) => R,\n  concurrent?: number\n): OperatorFunction<T, R>;\n/* tslint:enable:max-line-length */\n\n/**\n * Projects each source value to the same Observable which is merged multiple\n * times in the output Observable.\n *\n * <span class=\"informal\">It's like {@link mergeMap}, but maps each value always\n * to the same inner Observable.</span>\n *\n * ![](mergeMapTo.png)\n *\n * Maps each source value to the given Observable `innerObservable` regardless\n * of the source value, and then merges those resulting Observables into one\n * single Observable, which is the output Observable.\n *\n * ## Example\n *\n * For each click event, start an interval Observable ticking every 1 second\n *\n * ```ts\n * import { fromEvent, mergeMapTo, interval } from 'rxjs';\n *\n * const clicks = fromEvent(document, 'click');\n * const result = clicks.pipe(mergeMapTo(interval(1000)));\n *\n * result.subscribe(x => console.log(x));\n * ```\n *\n * @see {@link concatMapTo}\n * @see {@link merge}\n * @see {@link mergeAll}\n * @see {@link mergeMap}\n * @see {@link mergeScan}\n * @see {@link switchMapTo}\n *\n * @param innerObservable An `ObservableInput` to replace each value from the\n * source Observable.\n * @param concurrent Maximum number of input Observables being subscribed to\n * concurrently.\n * @return A function that returns an Observable that emits items from the\n * given `innerObservable`.\n * @deprecated Will be removed in v9. Use {@link mergeMap} instead: `mergeMap(() => result)`\n */\nexport function mergeMapTo<T, R, O extends ObservableInput<unknown>>(\n  innerObservable: O,\n  resultSelector?: ((outerValue: T, innerValue: ObservedValueOf<O>, outerIndex: number, innerIndex: number) => R) | number,\n  concurrent: number = Infinity\n): OperatorFunction<T, ObservedValueOf<O> | R> {\n  if (isFunction(resultSelector)) {\n    return mergeMap(() => innerObservable, resultSelector, concurrent);\n  }\n  if (typeof resultSelector === 'number') {\n    concurrent = resultSelector;\n  }\n  return mergeMap(() => innerObservable, concurrent);\n}\n"], "mappings": "AACA,SAASA,QAAQ,QAAQ,YAAY;AACrC,SAASC,UAAU,QAAQ,oBAAoB;AA2D/C,OAAM,SAAUC,UAAUA,CACxBC,eAAkB,EAClBC,cAAwH,EACxHC,UAA6B;EAA7B,IAAAA,UAAA;IAAAA,UAAA,GAAAC,QAA6B;EAAA;EAE7B,IAAIL,UAAU,CAACG,cAAc,CAAC,EAAE;IAC9B,OAAOJ,QAAQ,CAAC;MAAM,OAAAG,eAAe;IAAf,CAAe,EAAEC,cAAc,EAAEC,UAAU,CAAC;;EAEpE,IAAI,OAAOD,cAAc,KAAK,QAAQ,EAAE;IACtCC,UAAU,GAAGD,cAAc;;EAE7B,OAAOJ,QAAQ,CAAC;IAAM,OAAAG,eAAe;EAAf,CAAe,EAAEE,UAAU,CAAC;AACpD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}