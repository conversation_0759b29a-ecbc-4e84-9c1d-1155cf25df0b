{"ast": null, "code": "var _jsxFileName = \"C:\\\\git\\\\Partner-Site\\\\BdoPartner.Plans\\\\Development\\\\ReactClientApp\\\\src\\\\core\\\\loading\\\\components\\\\appProgress.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from \"react\";\nimport { ProgressSpinner } from \"primereact/progressspinner\";\nimport { BlockUI } from \"primereact/blockui\";\nimport \"./appProgress.scss\";\nimport { loadingService } from \"../loadingService\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AppProgress = () => {\n  _s();\n  const [isLoading, setIsLoading] = useState(false);\n  const subscriptionRef = useRef(null);\n  const timeoutRef = useRef(null);\n  const mountedRef = useRef(true);\n  useEffect(() => {\n    // Subscribe to loading service\n    subscriptionRef.current = loadingService.get().subscribe(data => {\n      // Clear any pending timeout\n      if (timeoutRef.current) {\n        clearTimeout(timeoutRef.current);\n      }\n\n      // Add a small delay to prevent rapid state changes that can cause BlockUI issues\n      timeoutRef.current = setTimeout(() => {\n        if (mountedRef.current) {\n          setIsLoading(data);\n        }\n      }, 20000); // Increased delay to better handle rapid changes\n    });\n\n    // Cleanup function\n    return () => {\n      mountedRef.current = false;\n\n      // Clear any pending timeout\n      if (timeoutRef.current) {\n        clearTimeout(timeoutRef.current);\n      }\n\n      // Unsubscribe to ensure no memory leaks\n      if (subscriptionRef.current) {\n        subscriptionRef.current.unsubscribe();\n      }\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(BlockUI, {\n      blocked: isLoading,\n      fullScreen: true\n    }, `blockui-${isLoading}`, false, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 7\n    }, this), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"progress-spinner-center\",\n      children: /*#__PURE__*/_jsxDEV(ProgressSpinner, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true);\n};\n_s(AppProgress, \"S7Z+YjO9AHpFLZ2Memt0mhgqDfg=\");\n_c = AppProgress;\nexport default AppProgress;\nvar _c;\n$RefreshReg$(_c, \"AppProgress\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "ProgressSpinner", "BlockUI", "loadingService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AppProgress", "_s", "isLoading", "setIsLoading", "subscriptionRef", "timeoutRef", "mountedRef", "current", "get", "subscribe", "data", "clearTimeout", "setTimeout", "unsubscribe", "children", "blocked", "fullScreen", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "_c", "$RefreshReg$"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/core/loading/components/appProgress.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from \"react\";\r\nimport { ProgressSpinner } from \"primereact/progressspinner\";\r\nimport { BlockUI } from \"primereact/blockui\";\r\nimport \"./appProgress.scss\";\r\nimport { loadingService } from \"../loadingService\";\r\n\r\nconst AppProgress = () => {\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const subscriptionRef = useRef(null);\r\n  const timeoutRef = useRef(null);\r\n  const mountedRef = useRef(true);\r\n\r\n  useEffect(() => {\r\n    // Subscribe to loading service\r\n    subscriptionRef.current = loadingService.get().subscribe((data) => {\r\n      // Clear any pending timeout\r\n      if (timeoutRef.current) {\r\n        clearTimeout(timeoutRef.current);\r\n      }\r\n\r\n      // Add a small delay to prevent rapid state changes that can cause BlockUI issues\r\n      timeoutRef.current = setTimeout(() => {\r\n        if (mountedRef.current) {\r\n          setIsLoading(data);\r\n        }\r\n      }, 20000); // Increased delay to better handle rapid changes\r\n    });\r\n\r\n    // Cleanup function\r\n    return () => {\r\n      mountedRef.current = false;\r\n\r\n      // Clear any pending timeout\r\n      if (timeoutRef.current) {\r\n        clearTimeout(timeoutRef.current);\r\n      }\r\n\r\n      // Unsubscribe to ensure no memory leaks\r\n      if (subscriptionRef.current) {\r\n        subscriptionRef.current.unsubscribe();\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  return (\r\n    <>\r\n      {/* Use a key to force re-render when loading state changes to prevent BlockUI issues */}\r\n      <BlockUI\r\n        key={`blockui-${isLoading}`}\r\n        blocked={isLoading}\r\n        fullScreen\r\n      />\r\n      {isLoading && (\r\n        <div className=\"progress-spinner-center\">\r\n          <ProgressSpinner />\r\n        </div>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default AppProgress;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,eAAe,QAAQ,4BAA4B;AAC5D,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,OAAO,oBAAoB;AAC3B,SAASC,cAAc,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEnD,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAMc,eAAe,GAAGZ,MAAM,CAAC,IAAI,CAAC;EACpC,MAAMa,UAAU,GAAGb,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAMc,UAAU,GAAGd,MAAM,CAAC,IAAI,CAAC;EAE/BD,SAAS,CAAC,MAAM;IACd;IACAa,eAAe,CAACG,OAAO,GAAGZ,cAAc,CAACa,GAAG,CAAC,CAAC,CAACC,SAAS,CAAEC,IAAI,IAAK;MACjE;MACA,IAAIL,UAAU,CAACE,OAAO,EAAE;QACtBI,YAAY,CAACN,UAAU,CAACE,OAAO,CAAC;MAClC;;MAEA;MACAF,UAAU,CAACE,OAAO,GAAGK,UAAU,CAAC,MAAM;QACpC,IAAIN,UAAU,CAACC,OAAO,EAAE;UACtBJ,YAAY,CAACO,IAAI,CAAC;QACpB;MACF,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;IACb,CAAC,CAAC;;IAEF;IACA,OAAO,MAAM;MACXJ,UAAU,CAACC,OAAO,GAAG,KAAK;;MAE1B;MACA,IAAIF,UAAU,CAACE,OAAO,EAAE;QACtBI,YAAY,CAACN,UAAU,CAACE,OAAO,CAAC;MAClC;;MAEA;MACA,IAAIH,eAAe,CAACG,OAAO,EAAE;QAC3BH,eAAe,CAACG,OAAO,CAACM,WAAW,CAAC,CAAC;MACvC;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEhB,OAAA,CAAAE,SAAA;IAAAe,QAAA,gBAEEjB,OAAA,CAACH,OAAO;MAENqB,OAAO,EAAEb,SAAU;MACnBc,UAAU;IAAA,GAFL,WAAWd,SAAS,EAAE;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAG5B,CAAC,EACDlB,SAAS,iBACRL,OAAA;MAAKwB,SAAS,EAAC,yBAAyB;MAAAP,QAAA,eACtCjB,OAAA,CAACJ,eAAe;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CACN;EAAA,eACD,CAAC;AAEP,CAAC;AAACnB,EAAA,CArDID,WAAW;AAAAsB,EAAA,GAAXtB,WAAW;AAuDjB,eAAeA,WAAW;AAAC,IAAAsB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}