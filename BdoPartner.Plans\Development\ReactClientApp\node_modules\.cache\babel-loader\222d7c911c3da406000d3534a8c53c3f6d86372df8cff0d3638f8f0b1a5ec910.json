{"ast": null, "code": "import { EmptyError } from '../util/EmptyError';\nimport { SequenceError } from '../util/SequenceError';\nimport { NotFoundError } from '../util/NotFoundError';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function single(predicate) {\n  return operate(function (source, subscriber) {\n    var hasValue = false;\n    var singleValue;\n    var seenValue = false;\n    var index = 0;\n    source.subscribe(createOperatorSubscriber(subscriber, function (value) {\n      seenValue = true;\n      if (!predicate || predicate(value, index++, source)) {\n        hasValue && subscriber.error(new SequenceError('Too many matching values'));\n        hasValue = true;\n        singleValue = value;\n      }\n    }, function () {\n      if (hasValue) {\n        subscriber.next(singleValue);\n        subscriber.complete();\n      } else {\n        subscriber.error(seenValue ? new NotFoundError('No matching values') : new EmptyError());\n      }\n    }));\n  });\n}", "map": {"version": 3, "names": ["EmptyError", "SequenceError", "NotFoundError", "operate", "createOperatorSubscriber", "single", "predicate", "source", "subscriber", "hasValue", "singleValue", "seenValue", "index", "subscribe", "value", "error", "next", "complete"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\single.ts"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { EmptyError } from '../util/EmptyError';\n\nimport { MonoTypeOperatorFunction, OperatorFunction, TruthyTypesOf } from '../types';\nimport { SequenceError } from '../util/SequenceError';\nimport { NotFoundError } from '../util/NotFoundError';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\n\nexport function single<T>(predicate: BooleanConstructor): OperatorFunction<T, TruthyTypesOf<T>>;\nexport function single<T>(predicate?: (value: T, index: number, source: Observable<T>) => boolean): MonoTypeOperatorFunction<T>;\n\n/**\n * Returns an observable that asserts that only one value is\n * emitted from the observable that matches the predicate. If no\n * predicate is provided, then it will assert that the observable\n * only emits one value.\n *\n * If the source Observable did not emit `next` before completion, it\n * will emit an {@link EmptyError} to the Observer's `error` callback.\n *\n * In the event that two values are found that match the predicate,\n * or when there are two values emitted and no predicate, it will\n * emit a {@link SequenceError} to the Observer's `error` callback.\n *\n * In the event that no values match the predicate, if one is provided,\n * it will emit a {@link NotFoundError} to the Observer's `error` callback.\n *\n * ## Example\n *\n * Expect only `name` beginning with `'B'`\n *\n * ```ts\n * import { of, single } from 'rxjs';\n *\n * const source1 = of(\n *  { name: 'Ben' },\n *  { name: 'Tracy' },\n *  { name: 'Laney' },\n *  { name: 'Lily' }\n * );\n *\n * source1\n *   .pipe(single(x => x.name.startsWith('B')))\n *   .subscribe(x => console.log(x));\n * // Emits 'Ben'\n *\n *\n * const source2 = of(\n *  { name: 'Ben' },\n *  { name: 'Tracy' },\n *  { name: 'Bradley' },\n *  { name: 'Lincoln' }\n * );\n *\n * source2\n *   .pipe(single(x => x.name.startsWith('B')))\n *   .subscribe({ error: err => console.error(err) });\n * // Error emitted: SequenceError('Too many values match')\n *\n *\n * const source3 = of(\n *  { name: 'Laney' },\n *  { name: 'Tracy' },\n *  { name: 'Lily' },\n *  { name: 'Lincoln' }\n * );\n *\n * source3\n *   .pipe(single(x => x.name.startsWith('B')))\n *   .subscribe({ error: err => console.error(err) });\n * // Error emitted: NotFoundError('No values match')\n * ```\n *\n * @see {@link first}\n * @see {@link find}\n * @see {@link findIndex}\n * @see {@link elementAt}\n *\n * @throws {NotFoundError} Delivers a `NotFoundError` to the Observer's `error`\n * callback if the Observable completes before any `next` notification was sent.\n * @throws {SequenceError} Delivers a `SequenceError` if more than one value is\n * emitted that matches the provided predicate. If no predicate is provided, it\n * will deliver a `SequenceError` if more than one value comes from the source.\n * @throws {EmptyError} Delivers an `EmptyError` if no values were `next`ed prior\n * to completion.\n *\n * @param predicate A predicate function to evaluate items emitted by the source\n * Observable.\n * @return A function that returns an Observable that emits the single item\n * emitted by the source Observable that matches the predicate.\n */\nexport function single<T>(predicate?: (value: T, index: number, source: Observable<T>) => boolean): MonoTypeOperatorFunction<T> {\n  return operate((source, subscriber) => {\n    let hasValue = false;\n    let singleValue: T;\n    let seenValue = false;\n    let index = 0;\n    source.subscribe(\n      createOperatorSubscriber(\n        subscriber,\n        (value) => {\n          seenValue = true;\n          if (!predicate || predicate(value, index++, source)) {\n            hasValue && subscriber.error(new SequenceError('Too many matching values'));\n            hasValue = true;\n            singleValue = value;\n          }\n        },\n        () => {\n          if (hasValue) {\n            subscriber.next(singleValue);\n            subscriber.complete();\n          } else {\n            subscriber.error(seenValue ? new NotFoundError('No matching values') : new EmptyError());\n          }\n        }\n      )\n    );\n  });\n}\n"], "mappings": "AACA,SAASA,UAAU,QAAQ,oBAAoB;AAG/C,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAqF/D,OAAM,SAAUC,MAAMA,CAAIC,SAAuE;EAC/F,OAAOH,OAAO,CAAC,UAACI,MAAM,EAAEC,UAAU;IAChC,IAAIC,QAAQ,GAAG,KAAK;IACpB,IAAIC,WAAc;IAClB,IAAIC,SAAS,GAAG,KAAK;IACrB,IAAIC,KAAK,GAAG,CAAC;IACbL,MAAM,CAACM,SAAS,CACdT,wBAAwB,CACtBI,UAAU,EACV,UAACM,KAAK;MACJH,SAAS,GAAG,IAAI;MAChB,IAAI,CAACL,SAAS,IAAIA,SAAS,CAACQ,KAAK,EAAEF,KAAK,EAAE,EAAEL,MAAM,CAAC,EAAE;QACnDE,QAAQ,IAAID,UAAU,CAACO,KAAK,CAAC,IAAId,aAAa,CAAC,0BAA0B,CAAC,CAAC;QAC3EQ,QAAQ,GAAG,IAAI;QACfC,WAAW,GAAGI,KAAK;;IAEvB,CAAC,EACD;MACE,IAAIL,QAAQ,EAAE;QACZD,UAAU,CAACQ,IAAI,CAACN,WAAW,CAAC;QAC5BF,UAAU,CAACS,QAAQ,EAAE;OACtB,MAAM;QACLT,UAAU,CAACO,KAAK,CAACJ,SAAS,GAAG,IAAIT,aAAa,CAAC,oBAAoB,CAAC,GAAG,IAAIF,UAAU,EAAE,CAAC;;IAE5F,CAAC,CACF,CACF;EACH,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}