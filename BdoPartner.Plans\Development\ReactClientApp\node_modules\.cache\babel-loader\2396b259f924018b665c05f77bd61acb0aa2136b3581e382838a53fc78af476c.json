{"ast": null, "code": "'use client';\n\nimport { EventBus } from 'primereact/utils';\nvar OverlayService = EventBus();\nexport { OverlayService };", "map": {"version": 3, "names": ["EventBus", "OverlayService"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/node_modules/primereact/overlayservice/overlayservice.esm.js"], "sourcesContent": ["'use client';\nimport { EventBus } from 'primereact/utils';\n\nvar OverlayService = EventBus();\n\nexport { OverlayService };\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,QAAQ,QAAQ,kBAAkB;AAE3C,IAAIC,cAAc,GAAGD,QAAQ,CAAC,CAAC;AAE/B,SAASC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}