{"ast": null, "code": "import { dateTimestampProvider } from './scheduler/dateTimestampProvider';\nvar Scheduler = function () {\n  function Scheduler(schedulerActionCtor, now) {\n    if (now === void 0) {\n      now = Scheduler.now;\n    }\n    this.schedulerActionCtor = schedulerActionCtor;\n    this.now = now;\n  }\n  Scheduler.prototype.schedule = function (work, delay, state) {\n    if (delay === void 0) {\n      delay = 0;\n    }\n    return new this.schedulerActionCtor(this, work).schedule(state, delay);\n  };\n  Scheduler.now = dateTimestampProvider.now;\n  return Scheduler;\n}();\nexport { Scheduler };", "map": {"version": 3, "names": ["dateTimestampProvider", "Scheduler", "schedulerActionCtor", "now", "prototype", "schedule", "work", "delay", "state"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\Scheduler.ts"], "sourcesContent": ["import { Action } from './scheduler/Action';\nimport { Subscription } from './Subscription';\nimport { SchedulerLike, SchedulerAction } from './types';\nimport { dateTimestampProvider } from './scheduler/dateTimestampProvider';\n\n/**\n * An execution context and a data structure to order tasks and schedule their\n * execution. Provides a notion of (potentially virtual) time, through the\n * `now()` getter method.\n *\n * Each unit of work in a Scheduler is called an `Action`.\n *\n * ```ts\n * class Scheduler {\n *   now(): number;\n *   schedule(work, delay?, state?): Subscription;\n * }\n * ```\n *\n * @deprecated Scheduler is an internal implementation detail of RxJS, and\n * should not be used directly. Rather, create your own class and implement\n * {@link SchedulerLike}. Will be made internal in v8.\n */\nexport class Scheduler implements SchedulerLike {\n  public static now: () => number = dateTimestampProvider.now;\n\n  constructor(private schedulerActionCtor: typeof Action, now: () => number = Scheduler.now) {\n    this.now = now;\n  }\n\n  /**\n   * A getter method that returns a number representing the current time\n   * (at the time this function was called) according to the scheduler's own\n   * internal clock.\n   * @return A number that represents the current time. May or may not\n   * have a relation to wall-clock time. May or may not refer to a time unit\n   * (e.g. milliseconds).\n   */\n  public now: () => number;\n\n  /**\n   * Schedules a function, `work`, for execution. May happen at some point in\n   * the future, according to the `delay` parameter, if specified. May be passed\n   * some context object, `state`, which will be passed to the `work` function.\n   *\n   * The given arguments will be processed an stored as an Action object in a\n   * queue of actions.\n   *\n   * @param work A function representing a task, or some unit of work to be\n   * executed by the Scheduler.\n   * @param delay Time to wait before executing the work, where the time unit is\n   * implicit and defined by the Scheduler itself.\n   * @param state Some contextual data that the `work` function uses when called\n   * by the Scheduler.\n   * @return A subscription in order to be able to unsubscribe the scheduled work.\n   */\n  public schedule<T>(work: (this: SchedulerAction<T>, state?: T) => void, delay: number = 0, state?: T): Subscription {\n    return new this.schedulerActionCtor<T>(this, work).schedule(state, delay);\n  }\n}\n"], "mappings": "AAGA,SAASA,qBAAqB,QAAQ,mCAAmC;AAoBzE,IAAAC,SAAA;EAGE,SAAAA,UAAoBC,mBAAkC,EAAEC,GAAiC;IAAjC,IAAAA,GAAA;MAAAA,GAAA,GAAoBF,SAAS,CAACE,GAAG;IAAA;IAArE,KAAAD,mBAAmB,GAAnBA,mBAAmB;IACrC,IAAI,CAACC,GAAG,GAAGA,GAAG;EAChB;EA4BOF,SAAA,CAAAG,SAAA,CAAAC,QAAQ,GAAf,UAAmBC,IAAmD,EAAEC,KAAiB,EAAEC,KAAS;IAA5B,IAAAD,KAAA;MAAAA,KAAA,IAAiB;IAAA;IACvF,OAAO,IAAI,IAAI,CAACL,mBAAmB,CAAI,IAAI,EAAEI,IAAI,CAAC,CAACD,QAAQ,CAACG,KAAK,EAAED,KAAK,CAAC;EAC3E,CAAC;EAlCaN,SAAA,CAAAE,GAAG,GAAiBH,qBAAqB,CAACG,GAAG;EAmC7D,OAAAF,SAAC;CAAA,CApCD;SAAaA,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}