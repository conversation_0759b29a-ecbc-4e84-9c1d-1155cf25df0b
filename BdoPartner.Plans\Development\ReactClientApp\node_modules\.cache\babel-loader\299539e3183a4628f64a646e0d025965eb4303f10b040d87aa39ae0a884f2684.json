{"ast": null, "code": "var _jsxFileName = \"C:\\\\git\\\\Partner-Site\\\\BdoPartner.Plans\\\\Development\\\\ReactClientApp\\\\src\\\\pages\\\\sample.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useContext } from \"react\";\nimport { messageService } from \"../core/message/messageService\";\nimport { Button } from \"primereact/button\";\nimport APP_CONFIG from \"../core/config/appConfig\";\nimport { ResultStatus } from \"../core/enumertions/resultStatus\";\nimport http from \"../core/http/httpClient\";\nimport { AuthContext } from \"../core/auth/components/authProvider\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport const Sample = () => {\n  _s();\n  const [keyVaultMessage, setMessage] = useState(\"\");\n  const [translateMessage, setTranslateMessage] = useState(\"\");\n  const [notifications, setNotifications] = useState([]);\n  const [invoices, setInvoices] = useState([]);\n  const authService = useContext(AuthContext);\n  useEffect(() => {\n    if (authService && authService.isAuthenticated()) {\n      http.get(APP_CONFIG.apiDomain + \"/api/lookup/getnotifications\").then(response => {\n        if (response.data && response.data.resultStatus === ResultStatus.Success) {\n          setNotifications(response.data.item);\n        }\n      });\n      http.get(APP_CONFIG.apiDomain + \"/api/invoice/getinvoices\").then(response => {\n        if (response.data && response.data.resultStatus === ResultStatus.Success) {\n          setInvoices(response.data.item);\n        }\n      });\n    }\n  }, [authService]);\n\n  /***\r\n   *  Same as code in: constructor(props) { super(props); this.showMessages = this.showMessages.bind(this);}\r\n   */\n  const showMessages = () => {\n    messageService.info(\"Information Test\", false);\n    messageService.warn(\"Warn Test\", false);\n    messageService.error(\"Error Test\", false);\n    messageService.success(\"Success Test\", false);\n    messageService.emit();\n  };\n  const showMessage = messageType => {\n    if (messageType === \"success\") {\n      messageService.success(\"test success message.\");\n    } else if (messageType === \"error\") {\n      messageService.error(\"test error message.\");\n    }\n  };\n  const showToast = messageType => {\n    if (messageType === \"success\") {\n      messageService.successToast(\"test success toast message.\");\n    } else if (messageType === \"error\") {\n      messageService.errorToast(\"test error toast message.\");\n    }\n  };\n  const showKeyVaultMessage = () => {\n    http.get(APP_CONFIG.apiDomain + \"/api/lookup/getkeyvaultmessage\").then(response => {\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\n        setMessage(response.data.item);\n      }\n    });\n  };\n  const showTranslateMessage = () => {\n    http.get(APP_CONFIG.apiDomain + \"/api/lookup/gettranslatemessage\").then(response => {\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\n        setTranslateMessage(response.data.item);\n      }\n    });\n  };\n  const showConfirmDialog = message => {\n    messageService.confirmDialog(message, response => {\n      messageService.infoToast(\"Choiced: \" + response);\n    });\n  };\n  const showDeleteConfirmDialog = message => {\n    messageService.confirmDeletionDialog(message, response => {\n      messageService.infoToast(\"Choiced: \" + response);\n    });\n  };\n  const showDialog = (message, messageType) => {\n    switch (messageType) {\n      case \"success\":\n        messageService.successDialog(message);\n        break;\n      case \"error\":\n        messageService.errorDialog(message);\n        break;\n      case \"warn\":\n        messageService.warnDialog(message);\n        break;\n      case \"info\":\n        messageService.infoDialog(message);\n        break;\n      default:\n        messageService.infoDialog(message);\n        break;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Sample Page\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 132,\n      columnNumber: 7\n    }, this), authService.isAuthenticated() && /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Today's DBO News (Demo Remote Web API call)\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n        children: notifications.map(item => {\n          return /*#__PURE__*/_jsxDEV(\"li\", {\n            children: item.message\n          }, item.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 22\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Encrypted Invoice Records Display Demo:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n        children: invoices.map(item => {\n          return /*#__PURE__*/_jsxDEV(\"li\", {\n            children: [item.invoiceNumber, \" - \", item.vendorName, \"- \", item.invoiceContent, \" - \", item.amount]\n          }, item.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 22\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-wrap\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-2 mr-2\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          className: \"btn btn-primary\",\n          onClick: showMessages,\n          children: \"Demo Message Box for multiple messages at one time\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-2 mr-2\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          className: \"btn btn-primary\",\n          onClick: () => showMessage(\"success\"),\n          children: \"Demo Message Box for one success message\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-2 mr-2\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          className: \"btn btn-primary\",\n          onClick: () => showMessage(\"error\"),\n          children: \"Demo Message Box for one error message\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-wrap\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-2 mr-2\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          className: \"btn btn-primary\",\n          onClick: () => showToast(\"error\"),\n          children: \"Demo error Toast\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-2 mr-2\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          className: \"btn btn-primary\",\n          onClick: () => showToast(\"success\"),\n          children: \"Demo Success Toast\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 172,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-wrap\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-2 mr-2\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          className: \"btn btn-primary\",\n          onClick: () => showConfirmDialog(\"Are you sure you want to continue the process? please click 'Yes' to continue, or click 'No' to stop the process.\"),\n          children: \"Demo Confirmation Dialog\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-2 mr-2\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          className: \"btn btn-primary\",\n          onClick: () => showDeleteConfirmDialog(\"Are you sure you want to delete the item? please click 'Yes' to delete, or click 'No' to cancel the deletion.\"),\n          children: \"Demo Confirm Deletion Dialog\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-2 mr-2\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          className: \"btn btn-primary\",\n          onClick: () => showDialog(\"Success Dialog Message\", \"success\"),\n          children: \"Demo Success Message Dialog\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-2 mr-2\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          className: \"btn btn-primary\",\n          onClick: () => showDialog(\"Error Dialog Message\", \"error\"),\n          children: \"Demo Error Message Dialog\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-2 mr-2\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          className: \"btn btn-primary\",\n          onClick: () => showDialog(\"Warn Dialog Message\", \"warn\"),\n          children: \"Demo Warn Message Dialog\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-2 mr-2\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          className: \"btn btn-primary\",\n          onClick: () => showDialog(\"Info Dialog Message\", \"info\"),\n          children: \"Demo Info Message Dialog\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-wrap\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-2 mr-2\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          className: \"btn btn-primary\",\n          onClick: () => showKeyVaultMessage(),\n          children: \"Demo Show Azure Key Vault Message\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-2 mr-2\",\n        children: keyVaultMessage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 248,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-wrap\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-2 mr-2\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          className: \"btn btn-primary\",\n          onClick: () => showTranslateMessage(),\n          children: \"Demo Show Server Side Message based on selected language\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 260,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-2 mr-2\",\n        children: translateMessage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 259,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n      children: \"Current Environment.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 270,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n      children: process.env.REACT_APP_ENV\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 271,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n      children: \"Current config settings:\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 272,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n      children: JSON.stringify(APP_CONFIG)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 273,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(Sample, \"jfZUpMZS+lJcx5lFcdkV1tyYPKY=\");\n_c = Sample;\nvar _c;\n$RefreshReg$(_c, \"Sample\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useContext", "messageService", "<PERSON><PERSON>", "APP_CONFIG", "ResultStatus", "http", "AuthContext", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON>", "_s", "keyVaultMessage", "setMessage", "translateMessage", "setTranslateMessage", "notifications", "setNotifications", "invoices", "setInvoices", "authService", "isAuthenticated", "get", "apiDomain", "then", "response", "data", "resultStatus", "Success", "item", "showMessages", "info", "warn", "error", "success", "emit", "showMessage", "messageType", "showToast", "successToast", "errorToast", "showKeyVaultMessage", "showTranslateMessage", "showConfirmDialog", "message", "confirmDialog", "infoToast", "showDeleteConfirmDialog", "confirmDeletionDialog", "showDialog", "successDialog", "errorDialog", "warnDialog", "infoDialog", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "id", "invoiceNumber", "vendorName", "invoiceContent", "amount", "className", "onClick", "process", "env", "REACT_APP_ENV", "JSON", "stringify", "_c", "$RefreshReg$"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/pages/sample.jsx"], "sourcesContent": ["import React, { useState, useEffect, useContext } from \"react\";\r\nimport { messageService } from \"../core/message/messageService\";\r\nimport { <PERSON><PERSON> } from \"primereact/button\";\r\nimport APP_CONFIG from \"../core/config/appConfig\";\r\nimport { ResultStatus } from \"../core/enumertions/resultStatus\";\r\nimport http from \"../core/http/httpClient\";\r\nimport { AuthContext } from \"../core/auth/components/authProvider\";\r\n\r\nexport const Sample = () => {\r\n  const [keyVaultMessage, setMessage] = useState(\"\");\r\n  const [translateMessage, setTranslateMessage] = useState(\"\");\r\n  const [notifications, setNotifications] = useState([]);\r\n  const [invoices, setInvoices] = useState([]);\r\n\r\n  const authService = useContext(AuthContext);\r\n\r\n  useEffect(() => {\r\n    if (authService && authService.isAuthenticated()) {\r\n      http\r\n        .get(APP_CONFIG.apiDomain + \"/api/lookup/getnotifications\")\r\n        .then((response) => {\r\n          if (\r\n            response.data &&\r\n            response.data.resultStatus === ResultStatus.Success\r\n          ) {\r\n            setNotifications(response.data.item);\r\n          }\r\n        });\r\n\r\n        http\r\n        .get(APP_CONFIG.apiDomain + \"/api/invoice/getinvoices\")\r\n        .then((response) => {\r\n          if (\r\n            response.data &&\r\n            response.data.resultStatus === ResultStatus.Success\r\n          ) {\r\n            setInvoices(response.data.item);\r\n          }\r\n        });\r\n   \r\n    }\r\n  }, [authService]);\r\n\r\n  /***\r\n   *  Same as code in: constructor(props) { super(props); this.showMessages = this.showMessages.bind(this);}\r\n   */\r\n  const showMessages = () => {\r\n    messageService.info(\"Information Test\", false);\r\n    messageService.warn(\"Warn Test\", false);\r\n    messageService.error(\"Error Test\", false);\r\n    messageService.success(\"Success Test\", false);\r\n    messageService.emit();\r\n  };\r\n\r\n  const showMessage = (messageType) => {\r\n    if (messageType === \"success\") {\r\n      messageService.success(\"test success message.\");\r\n    } else if (messageType === \"error\") {\r\n      messageService.error(\"test error message.\");\r\n    }\r\n  };\r\n\r\n  const showToast = (messageType) => {\r\n    if (messageType === \"success\") {\r\n      messageService.successToast(\"test success toast message.\");\r\n    } else if (messageType === \"error\") {\r\n      messageService.errorToast(\"test error toast message.\");\r\n    }\r\n  };\r\n\r\n  const showKeyVaultMessage = () => {\r\n    http\r\n      .get(APP_CONFIG.apiDomain + \"/api/lookup/getkeyvaultmessage\")\r\n      .then((response) => {\r\n        if (\r\n          response.data &&\r\n          response.data.resultStatus === ResultStatus.Success\r\n        ) {\r\n          setMessage(response.data.item);\r\n        }\r\n      });\r\n  };\r\n\r\n  const showTranslateMessage = () => {\r\n    http\r\n      .get(APP_CONFIG.apiDomain + \"/api/lookup/gettranslatemessage\")\r\n      .then((response) => {\r\n        if (\r\n          response.data &&\r\n          response.data.resultStatus === ResultStatus.Success\r\n        ) {\r\n          setTranslateMessage(response.data.item);\r\n        }\r\n      });\r\n  };\r\n\r\n\r\n  const showConfirmDialog = (message) => {\r\n    messageService.confirmDialog(message, (response) => {\r\n      messageService.infoToast(\"Choiced: \" + response);\r\n    });\r\n  };\r\n\r\n  const showDeleteConfirmDialog = (message) => {\r\n    messageService.confirmDeletionDialog(message, (response) => {\r\n      messageService.infoToast(\"Choiced: \" + response);\r\n    });\r\n  };\r\n\r\n  const showDialog = (message, messageType) => {\r\n    switch (messageType) {\r\n      case \"success\":\r\n        messageService.successDialog(message);\r\n        break;\r\n      case \"error\":\r\n        messageService.errorDialog(message);\r\n        break;\r\n      case \"warn\":\r\n        messageService.warnDialog(message);\r\n        break;\r\n      case \"info\":\r\n        messageService.infoDialog(message);\r\n        break;\r\n      default:\r\n        messageService.infoDialog(message);\r\n        break;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <h2>Sample Page</h2>\r\n      {authService.isAuthenticated() && (\r\n        <div>\r\n          <h3>Today's DBO News (Demo Remote Web API call)</h3>\r\n          <ul>\r\n            {notifications.map((item) => {\r\n              return <li key={item.id}>{item.message}</li>;\r\n            })}\r\n          </ul>\r\n          <h3>Encrypted Invoice Records Display Demo:</h3>\r\n          <ul>\r\n           {invoices.map((item) => {\r\n              return <li key={item.id}>{item.invoiceNumber} - {item.vendorName}- {item.invoiceContent} - {item.amount}</li>;\r\n            })}\r\n           </ul>\r\n        </div>\r\n      )}\r\n      <div className=\"flex flex-wrap\">\r\n        <div className=\"mb-2 mr-2\">\r\n          <Button className=\"btn btn-primary\" onClick={showMessages}>\r\n            Demo Message Box for multiple messages at one time\r\n          </Button>\r\n        </div>\r\n        <div className=\"mb-2 mr-2\">\r\n          <Button\r\n            className=\"btn btn-primary\"\r\n            onClick={() => showMessage(\"success\")}\r\n          >\r\n            Demo Message Box for one success message\r\n          </Button>\r\n        </div>\r\n        <div className=\"mb-2 mr-2\">\r\n          <Button\r\n            className=\"btn btn-primary\"\r\n            onClick={() => showMessage(\"error\")}\r\n          >\r\n            Demo Message Box for one error message\r\n          </Button>\r\n        </div>\r\n      </div>\r\n      <div className=\"flex flex-wrap\">\r\n        <div className=\"mb-2 mr-2\">\r\n          <Button\r\n            className=\"btn btn-primary\"\r\n            onClick={() => showToast(\"error\")}\r\n          >\r\n            Demo error Toast\r\n          </Button>\r\n        </div>\r\n        <div className=\"mb-2 mr-2\">\r\n          <Button\r\n            className=\"btn btn-primary\"\r\n            onClick={() => showToast(\"success\")}\r\n          >\r\n            Demo Success Toast\r\n          </Button>\r\n        </div>\r\n      </div>\r\n      <div className=\"flex flex-wrap\">\r\n        <div className=\"mb-2 mr-2\">\r\n          <Button\r\n            className=\"btn btn-primary\"\r\n            onClick={() =>\r\n              showConfirmDialog(\r\n                \"Are you sure you want to continue the process? please click 'Yes' to continue, or click 'No' to stop the process.\"\r\n              )\r\n            }\r\n          >\r\n            Demo Confirmation Dialog\r\n          </Button>\r\n        </div>\r\n        <div className=\"mb-2 mr-2\">\r\n          <Button\r\n            className=\"btn btn-primary\"\r\n            onClick={() =>\r\n              showDeleteConfirmDialog(\r\n                \"Are you sure you want to delete the item? please click 'Yes' to delete, or click 'No' to cancel the deletion.\"\r\n              )\r\n            }\r\n          >\r\n            Demo Confirm Deletion Dialog\r\n          </Button>\r\n        </div>\r\n        <div className=\"mb-2 mr-2\">\r\n          <Button\r\n            className=\"btn btn-primary\"\r\n            onClick={() => showDialog(\"Success Dialog Message\", \"success\")}\r\n          >\r\n            Demo Success Message Dialog\r\n          </Button>\r\n        </div>\r\n        <div className=\"mb-2 mr-2\">\r\n          <Button\r\n            className=\"btn btn-primary\"\r\n            onClick={() => showDialog(\"Error Dialog Message\", \"error\")}\r\n          >\r\n            Demo Error Message Dialog\r\n          </Button>\r\n        </div>\r\n        <div className=\"mb-2 mr-2\">\r\n          <Button\r\n            className=\"btn btn-primary\"\r\n            onClick={() => showDialog(\"Warn Dialog Message\", \"warn\")}\r\n          >\r\n            Demo Warn Message Dialog\r\n          </Button>\r\n        </div>\r\n        <div className=\"mb-2 mr-2\">\r\n          <Button\r\n            className=\"btn btn-primary\"\r\n            onClick={() => showDialog(\"Info Dialog Message\", \"info\")}\r\n          >\r\n            Demo Info Message Dialog\r\n          </Button>\r\n        </div>\r\n      </div>\r\n      <div className=\"flex flex-wrap\">\r\n        <div className=\"mb-2 mr-2\">\r\n          <Button\r\n            className=\"btn btn-primary\"\r\n            onClick={() => showKeyVaultMessage()}\r\n          >\r\n            Demo Show Azure Key Vault Message\r\n          </Button>\r\n        </div>\r\n        <div className=\"mb-2 mr-2\">{keyVaultMessage}</div>\r\n      </div>\r\n      <div className=\"flex flex-wrap\">\r\n        <div className=\"mb-2 mr-2\">\r\n          <Button\r\n            className=\"btn btn-primary\"\r\n            onClick={() => showTranslateMessage()}\r\n          >\r\n            Demo Show Server Side Message based on selected language\r\n          </Button>\r\n        </div>\r\n        <div className=\"mb-2 mr-2\">{translateMessage}</div>\r\n      </div>\r\n      <h4>Current Environment.</h4>\r\n      <span>{process.env.REACT_APP_ENV}</span>\r\n      <h4>Current config settings:</h4>\r\n      <span>{JSON.stringify(APP_CONFIG)}</span>\r\n    </>\r\n  );\r\n};\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,QAAQ,OAAO;AAC9D,SAASC,cAAc,QAAQ,gCAAgC;AAC/D,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,OAAOC,UAAU,MAAM,0BAA0B;AACjD,SAASC,YAAY,QAAQ,kCAAkC;AAC/D,OAAOC,IAAI,MAAM,yBAAyB;AAC1C,SAASC,WAAW,QAAQ,sCAAsC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEnE,OAAO,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM,CAACC,eAAe,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACiB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACmB,aAAa,EAAEC,gBAAgB,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACqB,QAAQ,EAAEC,WAAW,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAE5C,MAAMuB,WAAW,GAAGrB,UAAU,CAACM,WAAW,CAAC;EAE3CP,SAAS,CAAC,MAAM;IACd,IAAIsB,WAAW,IAAIA,WAAW,CAACC,eAAe,CAAC,CAAC,EAAE;MAChDjB,IAAI,CACDkB,GAAG,CAACpB,UAAU,CAACqB,SAAS,GAAG,8BAA8B,CAAC,CAC1DC,IAAI,CAAEC,QAAQ,IAAK;QAClB,IACEA,QAAQ,CAACC,IAAI,IACbD,QAAQ,CAACC,IAAI,CAACC,YAAY,KAAKxB,YAAY,CAACyB,OAAO,EACnD;UACAX,gBAAgB,CAACQ,QAAQ,CAACC,IAAI,CAACG,IAAI,CAAC;QACtC;MACF,CAAC,CAAC;MAEFzB,IAAI,CACHkB,GAAG,CAACpB,UAAU,CAACqB,SAAS,GAAG,0BAA0B,CAAC,CACtDC,IAAI,CAAEC,QAAQ,IAAK;QAClB,IACEA,QAAQ,CAACC,IAAI,IACbD,QAAQ,CAACC,IAAI,CAACC,YAAY,KAAKxB,YAAY,CAACyB,OAAO,EACnD;UACAT,WAAW,CAACM,QAAQ,CAACC,IAAI,CAACG,IAAI,CAAC;QACjC;MACF,CAAC,CAAC;IAEN;EACF,CAAC,EAAE,CAACT,WAAW,CAAC,CAAC;;EAEjB;AACF;AACA;EACE,MAAMU,YAAY,GAAGA,CAAA,KAAM;IACzB9B,cAAc,CAAC+B,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC;IAC9C/B,cAAc,CAACgC,IAAI,CAAC,WAAW,EAAE,KAAK,CAAC;IACvChC,cAAc,CAACiC,KAAK,CAAC,YAAY,EAAE,KAAK,CAAC;IACzCjC,cAAc,CAACkC,OAAO,CAAC,cAAc,EAAE,KAAK,CAAC;IAC7ClC,cAAc,CAACmC,IAAI,CAAC,CAAC;EACvB,CAAC;EAED,MAAMC,WAAW,GAAIC,WAAW,IAAK;IACnC,IAAIA,WAAW,KAAK,SAAS,EAAE;MAC7BrC,cAAc,CAACkC,OAAO,CAAC,uBAAuB,CAAC;IACjD,CAAC,MAAM,IAAIG,WAAW,KAAK,OAAO,EAAE;MAClCrC,cAAc,CAACiC,KAAK,CAAC,qBAAqB,CAAC;IAC7C;EACF,CAAC;EAED,MAAMK,SAAS,GAAID,WAAW,IAAK;IACjC,IAAIA,WAAW,KAAK,SAAS,EAAE;MAC7BrC,cAAc,CAACuC,YAAY,CAAC,6BAA6B,CAAC;IAC5D,CAAC,MAAM,IAAIF,WAAW,KAAK,OAAO,EAAE;MAClCrC,cAAc,CAACwC,UAAU,CAAC,2BAA2B,CAAC;IACxD;EACF,CAAC;EAED,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAChCrC,IAAI,CACDkB,GAAG,CAACpB,UAAU,CAACqB,SAAS,GAAG,gCAAgC,CAAC,CAC5DC,IAAI,CAAEC,QAAQ,IAAK;MAClB,IACEA,QAAQ,CAACC,IAAI,IACbD,QAAQ,CAACC,IAAI,CAACC,YAAY,KAAKxB,YAAY,CAACyB,OAAO,EACnD;QACAf,UAAU,CAACY,QAAQ,CAACC,IAAI,CAACG,IAAI,CAAC;MAChC;IACF,CAAC,CAAC;EACN,CAAC;EAED,MAAMa,oBAAoB,GAAGA,CAAA,KAAM;IACjCtC,IAAI,CACDkB,GAAG,CAACpB,UAAU,CAACqB,SAAS,GAAG,iCAAiC,CAAC,CAC7DC,IAAI,CAAEC,QAAQ,IAAK;MAClB,IACEA,QAAQ,CAACC,IAAI,IACbD,QAAQ,CAACC,IAAI,CAACC,YAAY,KAAKxB,YAAY,CAACyB,OAAO,EACnD;QACAb,mBAAmB,CAACU,QAAQ,CAACC,IAAI,CAACG,IAAI,CAAC;MACzC;IACF,CAAC,CAAC;EACN,CAAC;EAGD,MAAMc,iBAAiB,GAAIC,OAAO,IAAK;IACrC5C,cAAc,CAAC6C,aAAa,CAACD,OAAO,EAAGnB,QAAQ,IAAK;MAClDzB,cAAc,CAAC8C,SAAS,CAAC,WAAW,GAAGrB,QAAQ,CAAC;IAClD,CAAC,CAAC;EACJ,CAAC;EAED,MAAMsB,uBAAuB,GAAIH,OAAO,IAAK;IAC3C5C,cAAc,CAACgD,qBAAqB,CAACJ,OAAO,EAAGnB,QAAQ,IAAK;MAC1DzB,cAAc,CAAC8C,SAAS,CAAC,WAAW,GAAGrB,QAAQ,CAAC;IAClD,CAAC,CAAC;EACJ,CAAC;EAED,MAAMwB,UAAU,GAAGA,CAACL,OAAO,EAAEP,WAAW,KAAK;IAC3C,QAAQA,WAAW;MACjB,KAAK,SAAS;QACZrC,cAAc,CAACkD,aAAa,CAACN,OAAO,CAAC;QACrC;MACF,KAAK,OAAO;QACV5C,cAAc,CAACmD,WAAW,CAACP,OAAO,CAAC;QACnC;MACF,KAAK,MAAM;QACT5C,cAAc,CAACoD,UAAU,CAACR,OAAO,CAAC;QAClC;MACF,KAAK,MAAM;QACT5C,cAAc,CAACqD,UAAU,CAACT,OAAO,CAAC;QAClC;MACF;QACE5C,cAAc,CAACqD,UAAU,CAACT,OAAO,CAAC;QAClC;IACJ;EACF,CAAC;EAED,oBACErC,OAAA,CAAAE,SAAA;IAAA6C,QAAA,gBACE/C,OAAA;MAAA+C,QAAA,EAAI;IAAW;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EACnBtC,WAAW,CAACC,eAAe,CAAC,CAAC,iBAC5Bd,OAAA;MAAA+C,QAAA,gBACE/C,OAAA;QAAA+C,QAAA,EAAI;MAA2C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpDnD,OAAA;QAAA+C,QAAA,EACGtC,aAAa,CAAC2C,GAAG,CAAE9B,IAAI,IAAK;UAC3B,oBAAOtB,OAAA;YAAA+C,QAAA,EAAmBzB,IAAI,CAACe;UAAO,GAAtBf,IAAI,CAAC+B,EAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC;QAC9C,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACLnD,OAAA;QAAA+C,QAAA,EAAI;MAAuC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChDnD,OAAA;QAAA+C,QAAA,EACEpC,QAAQ,CAACyC,GAAG,CAAE9B,IAAI,IAAK;UACrB,oBAAOtB,OAAA;YAAA+C,QAAA,GAAmBzB,IAAI,CAACgC,aAAa,EAAC,KAAG,EAAChC,IAAI,CAACiC,UAAU,EAAC,IAAE,EAACjC,IAAI,CAACkC,cAAc,EAAC,KAAG,EAAClC,IAAI,CAACmC,MAAM;UAAA,GAAvFnC,IAAI,CAAC+B,EAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAqF,CAAC;QAC/G,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eACDnD,OAAA;MAAK0D,SAAS,EAAC,gBAAgB;MAAAX,QAAA,gBAC7B/C,OAAA;QAAK0D,SAAS,EAAC,WAAW;QAAAX,QAAA,eACxB/C,OAAA,CAACN,MAAM;UAACgE,SAAS,EAAC,iBAAiB;UAACC,OAAO,EAAEpC,YAAa;UAAAwB,QAAA,EAAC;QAE3D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACNnD,OAAA;QAAK0D,SAAS,EAAC,WAAW;QAAAX,QAAA,eACxB/C,OAAA,CAACN,MAAM;UACLgE,SAAS,EAAC,iBAAiB;UAC3BC,OAAO,EAAEA,CAAA,KAAM9B,WAAW,CAAC,SAAS,CAAE;UAAAkB,QAAA,EACvC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACNnD,OAAA;QAAK0D,SAAS,EAAC,WAAW;QAAAX,QAAA,eACxB/C,OAAA,CAACN,MAAM;UACLgE,SAAS,EAAC,iBAAiB;UAC3BC,OAAO,EAAEA,CAAA,KAAM9B,WAAW,CAAC,OAAO,CAAE;UAAAkB,QAAA,EACrC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNnD,OAAA;MAAK0D,SAAS,EAAC,gBAAgB;MAAAX,QAAA,gBAC7B/C,OAAA;QAAK0D,SAAS,EAAC,WAAW;QAAAX,QAAA,eACxB/C,OAAA,CAACN,MAAM;UACLgE,SAAS,EAAC,iBAAiB;UAC3BC,OAAO,EAAEA,CAAA,KAAM5B,SAAS,CAAC,OAAO,CAAE;UAAAgB,QAAA,EACnC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACNnD,OAAA;QAAK0D,SAAS,EAAC,WAAW;QAAAX,QAAA,eACxB/C,OAAA,CAACN,MAAM;UACLgE,SAAS,EAAC,iBAAiB;UAC3BC,OAAO,EAAEA,CAAA,KAAM5B,SAAS,CAAC,SAAS,CAAE;UAAAgB,QAAA,EACrC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNnD,OAAA;MAAK0D,SAAS,EAAC,gBAAgB;MAAAX,QAAA,gBAC7B/C,OAAA;QAAK0D,SAAS,EAAC,WAAW;QAAAX,QAAA,eACxB/C,OAAA,CAACN,MAAM;UACLgE,SAAS,EAAC,iBAAiB;UAC3BC,OAAO,EAAEA,CAAA,KACPvB,iBAAiB,CACf,mHACF,CACD;UAAAW,QAAA,EACF;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACNnD,OAAA;QAAK0D,SAAS,EAAC,WAAW;QAAAX,QAAA,eACxB/C,OAAA,CAACN,MAAM;UACLgE,SAAS,EAAC,iBAAiB;UAC3BC,OAAO,EAAEA,CAAA,KACPnB,uBAAuB,CACrB,+GACF,CACD;UAAAO,QAAA,EACF;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACNnD,OAAA;QAAK0D,SAAS,EAAC,WAAW;QAAAX,QAAA,eACxB/C,OAAA,CAACN,MAAM;UACLgE,SAAS,EAAC,iBAAiB;UAC3BC,OAAO,EAAEA,CAAA,KAAMjB,UAAU,CAAC,wBAAwB,EAAE,SAAS,CAAE;UAAAK,QAAA,EAChE;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACNnD,OAAA;QAAK0D,SAAS,EAAC,WAAW;QAAAX,QAAA,eACxB/C,OAAA,CAACN,MAAM;UACLgE,SAAS,EAAC,iBAAiB;UAC3BC,OAAO,EAAEA,CAAA,KAAMjB,UAAU,CAAC,sBAAsB,EAAE,OAAO,CAAE;UAAAK,QAAA,EAC5D;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACNnD,OAAA;QAAK0D,SAAS,EAAC,WAAW;QAAAX,QAAA,eACxB/C,OAAA,CAACN,MAAM;UACLgE,SAAS,EAAC,iBAAiB;UAC3BC,OAAO,EAAEA,CAAA,KAAMjB,UAAU,CAAC,qBAAqB,EAAE,MAAM,CAAE;UAAAK,QAAA,EAC1D;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACNnD,OAAA;QAAK0D,SAAS,EAAC,WAAW;QAAAX,QAAA,eACxB/C,OAAA,CAACN,MAAM;UACLgE,SAAS,EAAC,iBAAiB;UAC3BC,OAAO,EAAEA,CAAA,KAAMjB,UAAU,CAAC,qBAAqB,EAAE,MAAM,CAAE;UAAAK,QAAA,EAC1D;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNnD,OAAA;MAAK0D,SAAS,EAAC,gBAAgB;MAAAX,QAAA,gBAC7B/C,OAAA;QAAK0D,SAAS,EAAC,WAAW;QAAAX,QAAA,eACxB/C,OAAA,CAACN,MAAM;UACLgE,SAAS,EAAC,iBAAiB;UAC3BC,OAAO,EAAEA,CAAA,KAAMzB,mBAAmB,CAAC,CAAE;UAAAa,QAAA,EACtC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACNnD,OAAA;QAAK0D,SAAS,EAAC,WAAW;QAAAX,QAAA,EAAE1C;MAAe;QAAA2C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/C,CAAC,eACNnD,OAAA;MAAK0D,SAAS,EAAC,gBAAgB;MAAAX,QAAA,gBAC7B/C,OAAA;QAAK0D,SAAS,EAAC,WAAW;QAAAX,QAAA,eACxB/C,OAAA,CAACN,MAAM;UACLgE,SAAS,EAAC,iBAAiB;UAC3BC,OAAO,EAAEA,CAAA,KAAMxB,oBAAoB,CAAC,CAAE;UAAAY,QAAA,EACvC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACNnD,OAAA;QAAK0D,SAAS,EAAC,WAAW;QAAAX,QAAA,EAAExC;MAAgB;QAAAyC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD,CAAC,eACNnD,OAAA;MAAA+C,QAAA,EAAI;IAAoB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC7BnD,OAAA;MAAA+C,QAAA,EAAOa,OAAO,CAACC,GAAG,CAACC;IAAa;MAAAd,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eACxCnD,OAAA;MAAA+C,QAAA,EAAI;IAAwB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACjCnD,OAAA;MAAA+C,QAAA,EAAOgB,IAAI,CAACC,SAAS,CAACrE,UAAU;IAAC;MAAAqD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC;EAAA,eACzC,CAAC;AAEP,CAAC;AAAC/C,EAAA,CA3QWD,MAAM;AAAA8D,EAAA,GAAN9D,MAAM;AAAA,IAAA8D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}