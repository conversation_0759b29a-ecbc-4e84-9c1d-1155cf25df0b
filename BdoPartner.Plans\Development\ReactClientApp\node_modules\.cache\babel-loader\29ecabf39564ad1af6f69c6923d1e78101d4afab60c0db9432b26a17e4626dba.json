{"ast": null, "code": "import http from \"../core/http/httpClient\";\nimport APP_CONFIG from \"../core/config/appConfig\";\nimport { ResultStatus } from \"../core/enumertions/resultStatus\";\n\n/**\r\n * Form Service for handling form-related API calls\r\n * Provides methods to manage partner plan forms and user answers\r\n */\nclass FormService {\n  /**\r\n   * Get my plan - consolidated method that gets questionnaire, creates/gets form, and loads user answers\r\n   * @param {number} year - The year (optional, defaults to current year)\r\n   * @returns {Promise<Object|null>} Complete plan data or null if failed\r\n   */\n  async getMyPlan(year = null) {\n    try {\n      const params = new URLSearchParams();\n      if (year) params.append(\"year\", year);\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/form/getmyplan?${params.toString()}`);\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\n        return response.data.item;\n      } else {\n        var _response$data, _response$data2;\n        console.error(\"Failed to get my plan:\", (_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.message);\n        throw new Error(((_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : _response$data2.message) || \"Failed to load partner plan\");\n      }\n    } catch (error) {\n      console.error(\"Error getting my plan:\", error);\n      throw error;\n    }\n  }\n\n  /**\r\n   * Get partner plan by form ID - for Admin, ELT and Reviewer to view other partner's plan\r\n   * @param {string} formId - The form ID\r\n   * @returns {Promise<Object|null>} Complete plan data or null if failed\r\n   */\n  async getPartnerPlan(formId) {\n    try {\n      if (!formId) {\n        throw new Error(\"Form ID is required\");\n      }\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/form/getpartnerplan?formId=${formId}`);\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\n        return response.data.item;\n      } else {\n        var _response$data3, _response$data4;\n        console.error(\"Failed to get partner plan:\", (_response$data3 = response.data) === null || _response$data3 === void 0 ? void 0 : _response$data3.message);\n        throw new Error(((_response$data4 = response.data) === null || _response$data4 === void 0 ? void 0 : _response$data4.message) || \"Failed to load partner plan\");\n      }\n    } catch (error) {\n      console.error(\"Error getting partner plan:\", error);\n      throw error;\n    }\n  }\n\n  /**\r\n   * Get my available plans - gets all available plans for the current user\r\n   * @returns {Promise<Array|null>} Array of BasicPartnerPlanData or null if failed\r\n   */\n  async getMyAvailablePlans() {\n    try {\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/form/getmyavailableplans`);\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\n        return response.data.item || [];\n      } else {\n        var _response$data5, _response$data6;\n        console.error(\"Failed to get my available plans:\", (_response$data5 = response.data) === null || _response$data5 === void 0 ? void 0 : _response$data5.message);\n        throw new Error(((_response$data6 = response.data) === null || _response$data6 === void 0 ? void 0 : _response$data6.message) || \"Failed to load available plans\");\n      }\n    } catch (error) {\n      console.error(\"Error getting my available plans:\", error);\n      throw error;\n    }\n  }\n\n  /**\r\n   * Submit form\r\n   * @param {string} formId - The form ID\r\n   * @returns {Promise<Object|null>} Submitted form or null if failed\r\n   */\n  async submitForm(formId) {\n    try {\n      const response = await http.post(`${APP_CONFIG.apiDomain}/api/form/submitform?id=${formId}`);\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\n        return response.data.item;\n      } else {\n        var _response$data7, _response$data8;\n        console.error(\"Failed to submit form:\", (_response$data7 = response.data) === null || _response$data7 === void 0 ? void 0 : _response$data7.message);\n        throw new Error(((_response$data8 = response.data) === null || _response$data8 === void 0 ? void 0 : _response$data8.message) || \"Failed to submit form\");\n      }\n    } catch (error) {\n      console.error(\"Error submitting form:\", error);\n      throw error;\n    }\n  }\n\n  /**\r\n   * Send form back to partner with comments (Reviewer action only)\r\n   * @param {string} formId - The form ID\r\n   * @param {string} comments - Required comments explaining why form needs revision\r\n   * @returns {Promise<Object|null>} Updated form or null if failed\r\n   */\n  async sendBackToPartner(formId, comments) {\n    try {\n      const response = await http.post(`${APP_CONFIG.apiDomain}/api/form/sendbacktopartner`, {\n        formId: formId,\n        comments: comments\n      });\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\n        return response.data.item;\n      } else {\n        var _response$data9, _response$data0;\n        console.error(\"Failed to send form back to partner:\", (_response$data9 = response.data) === null || _response$data9 === void 0 ? void 0 : _response$data9.message);\n        throw new Error(((_response$data0 = response.data) === null || _response$data0 === void 0 ? void 0 : _response$data0.message) || \"Failed to send form back to partner\");\n      }\n    } catch (error) {\n      console.error(\"Error sending form back to partner:\", error);\n      throw error;\n    }\n  }\n\n  /**\r\n   * Get reviewer comments history for a form\r\n   * @param {string} formId - The form ID\r\n   * @returns {Promise<Array>} Array of reviewer comments or empty array if none\r\n   */\n  async getReviewerCommentsHistory(formId) {\n    try {\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/form/getreviewercommentshistory?formId=${formId}`);\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\n        return response.data.item || [];\n      } else {\n        var _response$data1;\n        console.error(\"Failed to get reviewer comments history:\", (_response$data1 = response.data) === null || _response$data1 === void 0 ? void 0 : _response$data1.message);\n        return [];\n      }\n    } catch (error) {\n      console.error(\"Error getting reviewer comments history:\", error);\n      return [];\n    }\n  }\n\n  /**\r\n   * Get current user's form role for a specific form\r\n   * @param {string} formId - The form ID\r\n   * @returns {Promise<number|null>} User form role ID or null if failed\r\n   */\n  async getCurrentUserFormRole(formId) {\n    try {\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/form/getCurrentUserFormRole?formId=${formId}`);\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\n        return response.data.item;\n      } else {\n        var _response$data10;\n        console.error(\"Failed to get current user form role:\", (_response$data10 = response.data) === null || _response$data10 === void 0 ? void 0 : _response$data10.message);\n        return null;\n      }\n    } catch (error) {\n      console.error(\"Error getting current user form role:\", error);\n      return null;\n    }\n  }\n\n  /**\r\n   * Save user answer (manual save)\r\n   * @param {string} formId - The form ID\r\n   * @param {string} answerData - Survey answer data as JSON string\r\n   * @returns {Promise<Object|null>} Saved user answer or null if failed\r\n   */\n  async saveUserAnswer(formId, answerData) {\n    try {\n      const response = await http.post(`${APP_CONFIG.apiDomain}/api/useranswer/saveuseranswer?formId=${formId}`, answerData, {\n        headers: {\n          \"Content-Type\": \"application/json\"\n        }\n      });\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\n        return response.data.item;\n      } else {\n        var _response$data11, _response$data12;\n        console.error(\"Failed to save user answer:\", (_response$data11 = response.data) === null || _response$data11 === void 0 ? void 0 : _response$data11.message);\n        throw new Error(((_response$data12 = response.data) === null || _response$data12 === void 0 ? void 0 : _response$data12.message) || \"Failed to save user answer\");\n      }\n    } catch (error) {\n      console.error(\"Error saving user answer:\", error);\n      throw error;\n    }\n  }\n\n  /**\r\n   * Auto-save user answer (background save)\r\n   * @param {string} formId - The form ID\r\n   * @param {string} answerData - Survey answer data as JSON string\r\n   * @returns {Promise<boolean>} True if successful, false otherwise\r\n   */\n  async autoSaveUserAnswer(formId, answerData) {\n    try {\n      const response = await http.post(`${APP_CONFIG.apiDomain}/api/useranswer/autosaveuseranswer?formId=${formId}`, answerData, {\n        headers: {\n          \"Content-Type\": \"application/json\"\n        }\n      });\n      if (response.data && response.data.success) {\n        return true;\n      } else {\n        var _response$data13;\n        console.warn(\"Auto-save failed:\", (_response$data13 = response.data) === null || _response$data13 === void 0 ? void 0 : _response$data13.message);\n        return false;\n      }\n    } catch (error) {\n      console.warn(\"Auto-save error:\", error);\n      return false;\n    }\n  }\n\n  /**\r\n   * Get tag configuration for a specific form\r\n   * Returns visibility and readonly rules for each tag value\r\n   * @param {string} formId - The form ID\r\n   * @returns {Promise<Object|null>} Tag configuration object or null if failed\r\n   */\n  async getFormAccessConfiguration(formId) {\n    try {\n      if (!formId) {\n        console.warn(\"Form ID is required for tag configuration\");\n        return null;\n      }\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/form/getFormAccessConfiguration?formId=${formId}`);\n      if (response.data) {\n        return response.data;\n      } else {\n        // If the API returns a 404 or no configuration, return null (not an error)\n        console.log(\"No form access configuration found for form:\", formId);\n        return null;\n      }\n    } catch (error) {\n      // Don't throw error - let the form load with default behavior\n      console.warn(\"Error getting form access configuration:\", error);\n      return null;\n    }\n  }\n\n  /**\r\n   * Get landing page access information for the current user\r\n   * Determines which sections should be shown/hidden based on user roles and partner relationships\r\n   * @returns {Promise<Object|null>} LandingPageAccess object or null if failed\r\n   */\n  async getLandingPageAccess() {\n    try {\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/form/GetLandingPageAccess`);\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\n        return response.data.item;\n      } else {\n        var _response$data14, _response$data15;\n        console.error(\"Failed to get landing page access:\", (_response$data14 = response.data) === null || _response$data14 === void 0 ? void 0 : _response$data14.message);\n        throw new Error(((_response$data15 = response.data) === null || _response$data15 === void 0 ? void 0 : _response$data15.message) || \"Failed to get landing page access\");\n      }\n    } catch (error) {\n      console.error(\"Error getting landing page access:\", error);\n      throw error;\n    }\n  }\n\n  /**\r\n   * Get unique leadership roles collection from table dbo.PartnerReviewer based on questionnaire year\r\n   * Used by Form Designer to populate leadership roles dropdown for panel visibility configuration\r\n   * @param {number} year - Questionnaire year\r\n   * @returns {Promise<Array<Object>>} List of unique leadership roles as Lookup objects with normalized values, sorted by display text ascending\r\n   */\n  async getUniqueLeadershipRoles(year) {\n    try {\n      if (!year) {\n        throw new Error(\"Year is required\");\n      }\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/form/GetUniqueLeadershipRoles?year=${year}`);\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\n        return response.data.item || [];\n      } else {\n        var _response$data16, _response$data17;\n        console.error(\"Failed to get unique leadership roles:\", (_response$data16 = response.data) === null || _response$data16 === void 0 ? void 0 : _response$data16.message);\n        throw new Error(((_response$data17 = response.data) === null || _response$data17 === void 0 ? void 0 : _response$data17.message) || \"Failed to get unique leadership roles\");\n      }\n    } catch (error) {\n      console.error(\"Error getting unique leadership roles:\", error);\n      throw error;\n    }\n  }\n}\n\n// Export a singleton instance\nexport const formService = new FormService();\nexport default formService;", "map": {"version": 3, "names": ["http", "APP_CONFIG", "ResultStatus", "FormService", "getMyPlan", "year", "params", "URLSearchParams", "append", "response", "get", "apiDomain", "toString", "data", "resultStatus", "Success", "item", "_response$data", "_response$data2", "console", "error", "message", "Error", "getPartnerPlan", "formId", "_response$data3", "_response$data4", "getMyAvailablePlans", "_response$data5", "_response$data6", "submitForm", "post", "_response$data7", "_response$data8", "sendBackToPartner", "comments", "_response$data9", "_response$data0", "getReviewerCommentsHistory", "_response$data1", "getCurrentUserFormRole", "_response$data10", "saveUserAnswer", "answerData", "headers", "_response$data11", "_response$data12", "autoSaveUserAnswer", "success", "_response$data13", "warn", "getFormAccessConfiguration", "log", "getLandingPageAccess", "_response$data14", "_response$data15", "getUniqueLeadershipRoles", "_response$data16", "_response$data17", "formService"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/services/formService.js"], "sourcesContent": ["import http from \"../core/http/httpClient\";\r\nimport APP_CONFIG from \"../core/config/appConfig\";\r\nimport { ResultStatus } from \"../core/enumertions/resultStatus\";\r\n\r\n/**\r\n * Form Service for handling form-related API calls\r\n * Provides methods to manage partner plan forms and user answers\r\n */\r\nclass FormService {\r\n  /**\r\n   * Get my plan - consolidated method that gets questionnaire, creates/gets form, and loads user answers\r\n   * @param {number} year - The year (optional, defaults to current year)\r\n   * @returns {Promise<Object|null>} Complete plan data or null if failed\r\n   */\r\n  async getMyPlan(year = null) {\r\n    try {\r\n      const params = new URLSearchParams();\r\n      if (year) params.append(\"year\", year);\r\n\r\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/form/getmyplan?${params.toString()}`);\r\n\r\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\r\n        return response.data.item;\r\n      } else {\r\n        console.error(\"Failed to get my plan:\", response.data?.message);\r\n        throw new Error(response.data?.message || \"Failed to load partner plan\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error getting my plan:\", error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get partner plan by form ID - for Admin, ELT and Reviewer to view other partner's plan\r\n   * @param {string} formId - The form ID\r\n   * @returns {Promise<Object|null>} Complete plan data or null if failed\r\n   */\r\n  async getPartnerPlan(formId) {\r\n    try {\r\n      if (!formId) {\r\n        throw new Error(\"Form ID is required\");\r\n      }\r\n\r\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/form/getpartnerplan?formId=${formId}`);\r\n\r\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\r\n        return response.data.item;\r\n      } else {\r\n        console.error(\"Failed to get partner plan:\", response.data?.message);\r\n        throw new Error(response.data?.message || \"Failed to load partner plan\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error getting partner plan:\", error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get my available plans - gets all available plans for the current user\r\n   * @returns {Promise<Array|null>} Array of BasicPartnerPlanData or null if failed\r\n   */\r\n  async getMyAvailablePlans() {\r\n    try {\r\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/form/getmyavailableplans`);\r\n\r\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\r\n        return response.data.item || [];\r\n      } else {\r\n        console.error(\"Failed to get my available plans:\", response.data?.message);\r\n        throw new Error(response.data?.message || \"Failed to load available plans\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error getting my available plans:\", error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Submit form\r\n   * @param {string} formId - The form ID\r\n   * @returns {Promise<Object|null>} Submitted form or null if failed\r\n   */\r\n  async submitForm(formId) {\r\n    try {\r\n      const response = await http.post(`${APP_CONFIG.apiDomain}/api/form/submitform?id=${formId}`);\r\n\r\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\r\n        return response.data.item;\r\n      } else {\r\n        console.error(\"Failed to submit form:\", response.data?.message);\r\n        throw new Error(response.data?.message || \"Failed to submit form\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error submitting form:\", error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Send form back to partner with comments (Reviewer action only)\r\n   * @param {string} formId - The form ID\r\n   * @param {string} comments - Required comments explaining why form needs revision\r\n   * @returns {Promise<Object|null>} Updated form or null if failed\r\n   */\r\n  async sendBackToPartner(formId, comments) {\r\n    try {\r\n      const response = await http.post(`${APP_CONFIG.apiDomain}/api/form/sendbacktopartner`, {\r\n        formId: formId,\r\n        comments: comments,\r\n      });\r\n\r\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\r\n        return response.data.item;\r\n      } else {\r\n        console.error(\"Failed to send form back to partner:\", response.data?.message);\r\n        throw new Error(response.data?.message || \"Failed to send form back to partner\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error sending form back to partner:\", error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get reviewer comments history for a form\r\n   * @param {string} formId - The form ID\r\n   * @returns {Promise<Array>} Array of reviewer comments or empty array if none\r\n   */\r\n  async getReviewerCommentsHistory(formId) {\r\n    try {\r\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/form/getreviewercommentshistory?formId=${formId}`);\r\n\r\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\r\n        return response.data.item || [];\r\n      } else {\r\n        console.error(\"Failed to get reviewer comments history:\", response.data?.message);\r\n        return [];\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error getting reviewer comments history:\", error);\r\n      return [];\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get current user's form role for a specific form\r\n   * @param {string} formId - The form ID\r\n   * @returns {Promise<number|null>} User form role ID or null if failed\r\n   */\r\n  async getCurrentUserFormRole(formId) {\r\n    try {\r\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/form/getCurrentUserFormRole?formId=${formId}`);\r\n\r\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\r\n        return response.data.item;\r\n      } else {\r\n        console.error(\"Failed to get current user form role:\", response.data?.message);\r\n        return null;\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error getting current user form role:\", error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Save user answer (manual save)\r\n   * @param {string} formId - The form ID\r\n   * @param {string} answerData - Survey answer data as JSON string\r\n   * @returns {Promise<Object|null>} Saved user answer or null if failed\r\n   */\r\n  async saveUserAnswer(formId, answerData) {\r\n    try {\r\n      const response = await http.post(`${APP_CONFIG.apiDomain}/api/useranswer/saveuseranswer?formId=${formId}`, answerData, {\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n      });\r\n\r\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\r\n        return response.data.item;\r\n      } else {\r\n        console.error(\"Failed to save user answer:\", response.data?.message);\r\n        throw new Error(response.data?.message || \"Failed to save user answer\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error saving user answer:\", error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Auto-save user answer (background save)\r\n   * @param {string} formId - The form ID\r\n   * @param {string} answerData - Survey answer data as JSON string\r\n   * @returns {Promise<boolean>} True if successful, false otherwise\r\n   */\r\n  async autoSaveUserAnswer(formId, answerData) {\r\n    try {\r\n      const response = await http.post(`${APP_CONFIG.apiDomain}/api/useranswer/autosaveuseranswer?formId=${formId}`, answerData, {\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n      });\r\n\r\n      if (response.data && response.data.success) {\r\n        return true;\r\n      } else {\r\n        console.warn(\"Auto-save failed:\", response.data?.message);\r\n        return false;\r\n      }\r\n    } catch (error) {\r\n      console.warn(\"Auto-save error:\", error);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get tag configuration for a specific form\r\n   * Returns visibility and readonly rules for each tag value\r\n   * @param {string} formId - The form ID\r\n   * @returns {Promise<Object|null>} Tag configuration object or null if failed\r\n   */\r\n  async getFormAccessConfiguration(formId) {\r\n    try {\r\n      if (!formId) {\r\n        console.warn(\"Form ID is required for tag configuration\");\r\n        return null;\r\n      }\r\n\r\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/form/getFormAccessConfiguration?formId=${formId}`);\r\n\r\n      if (response.data) {\r\n        return response.data;\r\n      } else {\r\n        // If the API returns a 404 or no configuration, return null (not an error)\r\n        console.log(\"No form access configuration found for form:\", formId);\r\n        return null;\r\n      }\r\n    } catch (error) {\r\n      // Don't throw error - let the form load with default behavior\r\n      console.warn(\"Error getting form access configuration:\", error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get landing page access information for the current user\r\n   * Determines which sections should be shown/hidden based on user roles and partner relationships\r\n   * @returns {Promise<Object|null>} LandingPageAccess object or null if failed\r\n   */\r\n  async getLandingPageAccess() {\r\n    try {\r\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/form/GetLandingPageAccess`);\r\n\r\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\r\n        return response.data.item;\r\n      } else {\r\n        console.error(\"Failed to get landing page access:\", response.data?.message);\r\n        throw new Error(response.data?.message || \"Failed to get landing page access\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error getting landing page access:\", error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get unique leadership roles collection from table dbo.PartnerReviewer based on questionnaire year\r\n   * Used by Form Designer to populate leadership roles dropdown for panel visibility configuration\r\n   * @param {number} year - Questionnaire year\r\n   * @returns {Promise<Array<Object>>} List of unique leadership roles as Lookup objects with normalized values, sorted by display text ascending\r\n   */\r\n  async getUniqueLeadershipRoles(year) {\r\n    try {\r\n      if (!year) {\r\n        throw new Error(\"Year is required\");\r\n      }\r\n\r\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/form/GetUniqueLeadershipRoles?year=${year}`);\r\n\r\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\r\n        return response.data.item || [];\r\n      } else {\r\n        console.error(\"Failed to get unique leadership roles:\", response.data?.message);\r\n        throw new Error(response.data?.message || \"Failed to get unique leadership roles\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error getting unique leadership roles:\", error);\r\n      throw error;\r\n    }\r\n  }\r\n}\r\n\r\n// Export a singleton instance\r\nexport const formService = new FormService();\r\nexport default formService;\r\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,yBAAyB;AAC1C,OAAOC,UAAU,MAAM,0BAA0B;AACjD,SAASC,YAAY,QAAQ,kCAAkC;;AAE/D;AACA;AACA;AACA;AACA,MAAMC,WAAW,CAAC;EAChB;AACF;AACA;AACA;AACA;EACE,MAAMC,SAASA,CAACC,IAAI,GAAG,IAAI,EAAE;IAC3B,IAAI;MACF,MAAMC,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;MACpC,IAAIF,IAAI,EAAEC,MAAM,CAACE,MAAM,CAAC,MAAM,EAAEH,IAAI,CAAC;MAErC,MAAMI,QAAQ,GAAG,MAAMT,IAAI,CAACU,GAAG,CAAC,GAAGT,UAAU,CAACU,SAAS,uBAAuBL,MAAM,CAACM,QAAQ,CAAC,CAAC,EAAE,CAAC;MAElG,IAAIH,QAAQ,CAACI,IAAI,IAAIJ,QAAQ,CAACI,IAAI,CAACC,YAAY,KAAKZ,YAAY,CAACa,OAAO,EAAE;QACxE,OAAON,QAAQ,CAACI,IAAI,CAACG,IAAI;MAC3B,CAAC,MAAM;QAAA,IAAAC,cAAA,EAAAC,eAAA;QACLC,OAAO,CAACC,KAAK,CAAC,wBAAwB,GAAAH,cAAA,GAAER,QAAQ,CAACI,IAAI,cAAAI,cAAA,uBAAbA,cAAA,CAAeI,OAAO,CAAC;QAC/D,MAAM,IAAIC,KAAK,CAAC,EAAAJ,eAAA,GAAAT,QAAQ,CAACI,IAAI,cAAAK,eAAA,uBAAbA,eAAA,CAAeG,OAAO,KAAI,6BAA6B,CAAC;MAC1E;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAMG,cAAcA,CAACC,MAAM,EAAE;IAC3B,IAAI;MACF,IAAI,CAACA,MAAM,EAAE;QACX,MAAM,IAAIF,KAAK,CAAC,qBAAqB,CAAC;MACxC;MAEA,MAAMb,QAAQ,GAAG,MAAMT,IAAI,CAACU,GAAG,CAAC,GAAGT,UAAU,CAACU,SAAS,mCAAmCa,MAAM,EAAE,CAAC;MAEnG,IAAIf,QAAQ,CAACI,IAAI,IAAIJ,QAAQ,CAACI,IAAI,CAACC,YAAY,KAAKZ,YAAY,CAACa,OAAO,EAAE;QACxE,OAAON,QAAQ,CAACI,IAAI,CAACG,IAAI;MAC3B,CAAC,MAAM;QAAA,IAAAS,eAAA,EAAAC,eAAA;QACLP,OAAO,CAACC,KAAK,CAAC,6BAA6B,GAAAK,eAAA,GAAEhB,QAAQ,CAACI,IAAI,cAAAY,eAAA,uBAAbA,eAAA,CAAeJ,OAAO,CAAC;QACpE,MAAM,IAAIC,KAAK,CAAC,EAAAI,eAAA,GAAAjB,QAAQ,CAACI,IAAI,cAAAa,eAAA,uBAAbA,eAAA,CAAeL,OAAO,KAAI,6BAA6B,CAAC;MAC1E;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;EACE,MAAMO,mBAAmBA,CAAA,EAAG;IAC1B,IAAI;MACF,MAAMlB,QAAQ,GAAG,MAAMT,IAAI,CAACU,GAAG,CAAC,GAAGT,UAAU,CAACU,SAAS,+BAA+B,CAAC;MAEvF,IAAIF,QAAQ,CAACI,IAAI,IAAIJ,QAAQ,CAACI,IAAI,CAACC,YAAY,KAAKZ,YAAY,CAACa,OAAO,EAAE;QACxE,OAAON,QAAQ,CAACI,IAAI,CAACG,IAAI,IAAI,EAAE;MACjC,CAAC,MAAM;QAAA,IAAAY,eAAA,EAAAC,eAAA;QACLV,OAAO,CAACC,KAAK,CAAC,mCAAmC,GAAAQ,eAAA,GAAEnB,QAAQ,CAACI,IAAI,cAAAe,eAAA,uBAAbA,eAAA,CAAeP,OAAO,CAAC;QAC1E,MAAM,IAAIC,KAAK,CAAC,EAAAO,eAAA,GAAApB,QAAQ,CAACI,IAAI,cAAAgB,eAAA,uBAAbA,eAAA,CAAeR,OAAO,KAAI,gCAAgC,CAAC;MAC7E;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAMU,UAAUA,CAACN,MAAM,EAAE;IACvB,IAAI;MACF,MAAMf,QAAQ,GAAG,MAAMT,IAAI,CAAC+B,IAAI,CAAC,GAAG9B,UAAU,CAACU,SAAS,2BAA2Ba,MAAM,EAAE,CAAC;MAE5F,IAAIf,QAAQ,CAACI,IAAI,IAAIJ,QAAQ,CAACI,IAAI,CAACC,YAAY,KAAKZ,YAAY,CAACa,OAAO,EAAE;QACxE,OAAON,QAAQ,CAACI,IAAI,CAACG,IAAI;MAC3B,CAAC,MAAM;QAAA,IAAAgB,eAAA,EAAAC,eAAA;QACLd,OAAO,CAACC,KAAK,CAAC,wBAAwB,GAAAY,eAAA,GAAEvB,QAAQ,CAACI,IAAI,cAAAmB,eAAA,uBAAbA,eAAA,CAAeX,OAAO,CAAC;QAC/D,MAAM,IAAIC,KAAK,CAAC,EAAAW,eAAA,GAAAxB,QAAQ,CAACI,IAAI,cAAAoB,eAAA,uBAAbA,eAAA,CAAeZ,OAAO,KAAI,uBAAuB,CAAC;MACpE;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,MAAMc,iBAAiBA,CAACV,MAAM,EAAEW,QAAQ,EAAE;IACxC,IAAI;MACF,MAAM1B,QAAQ,GAAG,MAAMT,IAAI,CAAC+B,IAAI,CAAC,GAAG9B,UAAU,CAACU,SAAS,6BAA6B,EAAE;QACrFa,MAAM,EAAEA,MAAM;QACdW,QAAQ,EAAEA;MACZ,CAAC,CAAC;MAEF,IAAI1B,QAAQ,CAACI,IAAI,IAAIJ,QAAQ,CAACI,IAAI,CAACC,YAAY,KAAKZ,YAAY,CAACa,OAAO,EAAE;QACxE,OAAON,QAAQ,CAACI,IAAI,CAACG,IAAI;MAC3B,CAAC,MAAM;QAAA,IAAAoB,eAAA,EAAAC,eAAA;QACLlB,OAAO,CAACC,KAAK,CAAC,sCAAsC,GAAAgB,eAAA,GAAE3B,QAAQ,CAACI,IAAI,cAAAuB,eAAA,uBAAbA,eAAA,CAAef,OAAO,CAAC;QAC7E,MAAM,IAAIC,KAAK,CAAC,EAAAe,eAAA,GAAA5B,QAAQ,CAACI,IAAI,cAAAwB,eAAA,uBAAbA,eAAA,CAAehB,OAAO,KAAI,qCAAqC,CAAC;MAClF;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAMkB,0BAA0BA,CAACd,MAAM,EAAE;IACvC,IAAI;MACF,MAAMf,QAAQ,GAAG,MAAMT,IAAI,CAACU,GAAG,CAAC,GAAGT,UAAU,CAACU,SAAS,+CAA+Ca,MAAM,EAAE,CAAC;MAE/G,IAAIf,QAAQ,CAACI,IAAI,IAAIJ,QAAQ,CAACI,IAAI,CAACC,YAAY,KAAKZ,YAAY,CAACa,OAAO,EAAE;QACxE,OAAON,QAAQ,CAACI,IAAI,CAACG,IAAI,IAAI,EAAE;MACjC,CAAC,MAAM;QAAA,IAAAuB,eAAA;QACLpB,OAAO,CAACC,KAAK,CAAC,0CAA0C,GAAAmB,eAAA,GAAE9B,QAAQ,CAACI,IAAI,cAAA0B,eAAA,uBAAbA,eAAA,CAAelB,OAAO,CAAC;QACjF,OAAO,EAAE;MACX;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChE,OAAO,EAAE;IACX;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAMoB,sBAAsBA,CAAChB,MAAM,EAAE;IACnC,IAAI;MACF,MAAMf,QAAQ,GAAG,MAAMT,IAAI,CAACU,GAAG,CAAC,GAAGT,UAAU,CAACU,SAAS,2CAA2Ca,MAAM,EAAE,CAAC;MAE3G,IAAIf,QAAQ,CAACI,IAAI,IAAIJ,QAAQ,CAACI,IAAI,CAACC,YAAY,KAAKZ,YAAY,CAACa,OAAO,EAAE;QACxE,OAAON,QAAQ,CAACI,IAAI,CAACG,IAAI;MAC3B,CAAC,MAAM;QAAA,IAAAyB,gBAAA;QACLtB,OAAO,CAACC,KAAK,CAAC,uCAAuC,GAAAqB,gBAAA,GAAEhC,QAAQ,CAACI,IAAI,cAAA4B,gBAAA,uBAAbA,gBAAA,CAAepB,OAAO,CAAC;QAC9E,OAAO,IAAI;MACb;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7D,OAAO,IAAI;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,MAAMsB,cAAcA,CAAClB,MAAM,EAAEmB,UAAU,EAAE;IACvC,IAAI;MACF,MAAMlC,QAAQ,GAAG,MAAMT,IAAI,CAAC+B,IAAI,CAAC,GAAG9B,UAAU,CAACU,SAAS,yCAAyCa,MAAM,EAAE,EAAEmB,UAAU,EAAE;QACrHC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEF,IAAInC,QAAQ,CAACI,IAAI,IAAIJ,QAAQ,CAACI,IAAI,CAACC,YAAY,KAAKZ,YAAY,CAACa,OAAO,EAAE;QACxE,OAAON,QAAQ,CAACI,IAAI,CAACG,IAAI;MAC3B,CAAC,MAAM;QAAA,IAAA6B,gBAAA,EAAAC,gBAAA;QACL3B,OAAO,CAACC,KAAK,CAAC,6BAA6B,GAAAyB,gBAAA,GAAEpC,QAAQ,CAACI,IAAI,cAAAgC,gBAAA,uBAAbA,gBAAA,CAAexB,OAAO,CAAC;QACpE,MAAM,IAAIC,KAAK,CAAC,EAAAwB,gBAAA,GAAArC,QAAQ,CAACI,IAAI,cAAAiC,gBAAA,uBAAbA,gBAAA,CAAezB,OAAO,KAAI,4BAA4B,CAAC;MACzE;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,MAAM2B,kBAAkBA,CAACvB,MAAM,EAAEmB,UAAU,EAAE;IAC3C,IAAI;MACF,MAAMlC,QAAQ,GAAG,MAAMT,IAAI,CAAC+B,IAAI,CAAC,GAAG9B,UAAU,CAACU,SAAS,6CAA6Ca,MAAM,EAAE,EAAEmB,UAAU,EAAE;QACzHC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEF,IAAInC,QAAQ,CAACI,IAAI,IAAIJ,QAAQ,CAACI,IAAI,CAACmC,OAAO,EAAE;QAC1C,OAAO,IAAI;MACb,CAAC,MAAM;QAAA,IAAAC,gBAAA;QACL9B,OAAO,CAAC+B,IAAI,CAAC,mBAAmB,GAAAD,gBAAA,GAAExC,QAAQ,CAACI,IAAI,cAAAoC,gBAAA,uBAAbA,gBAAA,CAAe5B,OAAO,CAAC;QACzD,OAAO,KAAK;MACd;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdD,OAAO,CAAC+B,IAAI,CAAC,kBAAkB,EAAE9B,KAAK,CAAC;MACvC,OAAO,KAAK;IACd;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,MAAM+B,0BAA0BA,CAAC3B,MAAM,EAAE;IACvC,IAAI;MACF,IAAI,CAACA,MAAM,EAAE;QACXL,OAAO,CAAC+B,IAAI,CAAC,2CAA2C,CAAC;QACzD,OAAO,IAAI;MACb;MAEA,MAAMzC,QAAQ,GAAG,MAAMT,IAAI,CAACU,GAAG,CAAC,GAAGT,UAAU,CAACU,SAAS,+CAA+Ca,MAAM,EAAE,CAAC;MAE/G,IAAIf,QAAQ,CAACI,IAAI,EAAE;QACjB,OAAOJ,QAAQ,CAACI,IAAI;MACtB,CAAC,MAAM;QACL;QACAM,OAAO,CAACiC,GAAG,CAAC,8CAA8C,EAAE5B,MAAM,CAAC;QACnE,OAAO,IAAI;MACb;IACF,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACd;MACAD,OAAO,CAAC+B,IAAI,CAAC,0CAA0C,EAAE9B,KAAK,CAAC;MAC/D,OAAO,IAAI;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAMiC,oBAAoBA,CAAA,EAAG;IAC3B,IAAI;MACF,MAAM5C,QAAQ,GAAG,MAAMT,IAAI,CAACU,GAAG,CAAC,GAAGT,UAAU,CAACU,SAAS,gCAAgC,CAAC;MAExF,IAAIF,QAAQ,CAACI,IAAI,IAAIJ,QAAQ,CAACI,IAAI,CAACC,YAAY,KAAKZ,YAAY,CAACa,OAAO,EAAE;QACxE,OAAON,QAAQ,CAACI,IAAI,CAACG,IAAI;MAC3B,CAAC,MAAM;QAAA,IAAAsC,gBAAA,EAAAC,gBAAA;QACLpC,OAAO,CAACC,KAAK,CAAC,oCAAoC,GAAAkC,gBAAA,GAAE7C,QAAQ,CAACI,IAAI,cAAAyC,gBAAA,uBAAbA,gBAAA,CAAejC,OAAO,CAAC;QAC3E,MAAM,IAAIC,KAAK,CAAC,EAAAiC,gBAAA,GAAA9C,QAAQ,CAACI,IAAI,cAAA0C,gBAAA,uBAAbA,gBAAA,CAAelC,OAAO,KAAI,mCAAmC,CAAC;MAChF;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,MAAMoC,wBAAwBA,CAACnD,IAAI,EAAE;IACnC,IAAI;MACF,IAAI,CAACA,IAAI,EAAE;QACT,MAAM,IAAIiB,KAAK,CAAC,kBAAkB,CAAC;MACrC;MAEA,MAAMb,QAAQ,GAAG,MAAMT,IAAI,CAACU,GAAG,CAAC,GAAGT,UAAU,CAACU,SAAS,2CAA2CN,IAAI,EAAE,CAAC;MAEzG,IAAII,QAAQ,CAACI,IAAI,IAAIJ,QAAQ,CAACI,IAAI,CAACC,YAAY,KAAKZ,YAAY,CAACa,OAAO,EAAE;QACxE,OAAON,QAAQ,CAACI,IAAI,CAACG,IAAI,IAAI,EAAE;MACjC,CAAC,MAAM;QAAA,IAAAyC,gBAAA,EAAAC,gBAAA;QACLvC,OAAO,CAACC,KAAK,CAAC,wCAAwC,GAAAqC,gBAAA,GAAEhD,QAAQ,CAACI,IAAI,cAAA4C,gBAAA,uBAAbA,gBAAA,CAAepC,OAAO,CAAC;QAC/E,MAAM,IAAIC,KAAK,CAAC,EAAAoC,gBAAA,GAAAjD,QAAQ,CAACI,IAAI,cAAA6C,gBAAA,uBAAbA,gBAAA,CAAerC,OAAO,KAAI,uCAAuC,CAAC;MACpF;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAC9D,MAAMA,KAAK;IACb;EACF;AACF;;AAEA;AACA,OAAO,MAAMuC,WAAW,GAAG,IAAIxD,WAAW,CAAC,CAAC;AAC5C,eAAewD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}