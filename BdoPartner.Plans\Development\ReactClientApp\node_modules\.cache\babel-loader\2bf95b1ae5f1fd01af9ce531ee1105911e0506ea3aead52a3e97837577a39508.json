{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { AsyncScheduler } from './AsyncScheduler';\nvar AnimationFrameScheduler = function (_super) {\n  __extends(AnimationFrameScheduler, _super);\n  function AnimationFrameScheduler() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  AnimationFrameScheduler.prototype.flush = function (action) {\n    this._active = true;\n    var flushId;\n    if (action) {\n      flushId = action.id;\n    } else {\n      flushId = this._scheduled;\n      this._scheduled = undefined;\n    }\n    var actions = this.actions;\n    var error;\n    action = action || actions.shift();\n    do {\n      if (error = action.execute(action.state, action.delay)) {\n        break;\n      }\n    } while ((action = actions[0]) && action.id === flushId && actions.shift());\n    this._active = false;\n    if (error) {\n      while ((action = actions[0]) && action.id === flushId && actions.shift()) {\n        action.unsubscribe();\n      }\n      throw error;\n    }\n  };\n  return AnimationFrameScheduler;\n}(AsyncScheduler);\nexport { AnimationFrameScheduler };", "map": {"version": 3, "names": ["AsyncScheduler", "AnimationFrameScheduler", "_super", "__extends", "prototype", "flush", "action", "_active", "flushId", "id", "_scheduled", "undefined", "actions", "error", "shift", "execute", "state", "delay", "unsubscribe"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\scheduler\\AnimationFrameScheduler.ts"], "sourcesContent": ["import { AsyncAction } from './AsyncAction';\nimport { AsyncScheduler } from './AsyncScheduler';\n\nexport class AnimationFrameScheduler extends AsyncScheduler {\n  public flush(action?: AsyncAction<any>): void {\n    this._active = true;\n    // The async id that effects a call to flush is stored in _scheduled.\n    // Before executing an action, it's necessary to check the action's async\n    // id to determine whether it's supposed to be executed in the current\n    // flush.\n    // Previous implementations of this method used a count to determine this,\n    // but that was unsound, as actions that are unsubscribed - i.e. cancelled -\n    // are removed from the actions array and that can shift actions that are\n    // scheduled to be executed in a subsequent flush into positions at which\n    // they are executed within the current flush.\n    let flushId;\n    if (action) {\n      flushId = action.id;\n    } else {\n      flushId = this._scheduled;\n      this._scheduled = undefined;\n    }\n\n    const { actions } = this;\n    let error: any;\n    action = action || actions.shift()!;\n\n    do {\n      if ((error = action.execute(action.state, action.delay))) {\n        break;\n      }\n    } while ((action = actions[0]) && action.id === flushId && actions.shift());\n\n    this._active = false;\n\n    if (error) {\n      while ((action = actions[0]) && action.id === flushId && actions.shift()) {\n        action.unsubscribe();\n      }\n      throw error;\n    }\n  }\n}\n"], "mappings": ";AACA,SAASA,cAAc,QAAQ,kBAAkB;AAEjD,IAAAC,uBAAA,aAAAC,MAAA;EAA6CC,SAAA,CAAAF,uBAAA,EAAAC,MAAA;EAA7C,SAAAD,wBAAA;;EAuCA;EAtCSA,uBAAA,CAAAG,SAAA,CAAAC,KAAK,GAAZ,UAAaC,MAAyB;IACpC,IAAI,CAACC,OAAO,GAAG,IAAI;IAUnB,IAAIC,OAAO;IACX,IAAIF,MAAM,EAAE;MACVE,OAAO,GAAGF,MAAM,CAACG,EAAE;KACpB,MAAM;MACLD,OAAO,GAAG,IAAI,CAACE,UAAU;MACzB,IAAI,CAACA,UAAU,GAAGC,SAAS;;IAGrB,IAAAC,OAAO,GAAK,IAAI,CAAAA,OAAT;IACf,IAAIC,KAAU;IACdP,MAAM,GAAGA,MAAM,IAAIM,OAAO,CAACE,KAAK,EAAG;IAEnC,GAAG;MACD,IAAKD,KAAK,GAAGP,MAAM,CAACS,OAAO,CAACT,MAAM,CAACU,KAAK,EAAEV,MAAM,CAACW,KAAK,CAAC,EAAG;QACxD;;KAEH,QAAQ,CAACX,MAAM,GAAGM,OAAO,CAAC,CAAC,CAAC,KAAKN,MAAM,CAACG,EAAE,KAAKD,OAAO,IAAII,OAAO,CAACE,KAAK,EAAE;IAE1E,IAAI,CAACP,OAAO,GAAG,KAAK;IAEpB,IAAIM,KAAK,EAAE;MACT,OAAO,CAACP,MAAM,GAAGM,OAAO,CAAC,CAAC,CAAC,KAAKN,MAAM,CAACG,EAAE,KAAKD,OAAO,IAAII,OAAO,CAACE,KAAK,EAAE,EAAE;QACxER,MAAM,CAACY,WAAW,EAAE;;MAEtB,MAAML,KAAK;;EAEf,CAAC;EACH,OAAAZ,uBAAC;AAAD,CAAC,CAvC4CD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}