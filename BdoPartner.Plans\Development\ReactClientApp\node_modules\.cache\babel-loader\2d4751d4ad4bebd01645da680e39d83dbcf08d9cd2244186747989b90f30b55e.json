{"ast": null, "code": "import { distinctUntilChanged } from './distinctUntilChanged';\nexport function distinctUntilKeyChanged(key, compare) {\n  return distinctUntilChanged(function (x, y) {\n    return compare ? compare(x[key], y[key]) : x[key] === y[key];\n  });\n}", "map": {"version": 3, "names": ["distinctUntilChanged", "distinctUntilKeyChanged", "key", "compare", "x", "y"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\distinctUntilKeyChanged.ts"], "sourcesContent": ["import { distinctUntilChanged } from './distinctUntilChanged';\nimport { MonoTypeOperatorFunction } from '../types';\n\nexport function distinctUntilKeyChanged<T>(key: keyof T): MonoTypeOperatorFunction<T>;\nexport function distinctUntilKeyChanged<T, K extends keyof T>(key: K, compare: (x: T[K], y: T[K]) => boolean): MonoTypeOperatorFunction<T>;\n\n/**\n * Returns an Observable that emits all items emitted by the source Observable that\n * are distinct by comparison from the previous item, using a property accessed by\n * using the key provided to check if the two items are distinct.\n *\n * If a comparator function is provided, then it will be called for each item to\n * test for whether that value should be emitted or not.\n *\n * If a comparator function is not provided, an equality check is used by default.\n *\n * ## Examples\n *\n * An example comparing the name of persons\n *\n * ```ts\n * import { of, distinctUntilKeyChanged } from 'rxjs';\n *\n * of(\n *   { age: 4, name: '<PERSON>oo' },\n *   { age: 7, name: '<PERSON>' },\n *   { age: 5, name: 'Foo' },\n *   { age: 6, name: 'Foo' }\n * ).pipe(\n *   distinctUntilKeyChanged('name')\n * )\n * .subscribe(x => console.log(x));\n *\n * // displays:\n * // { age: 4, name: 'Foo' }\n * // { age: 7, name: 'Bar' }\n * // { age: 5, name: 'Foo' }\n * ```\n *\n * An example comparing the first letters of the name\n *\n * ```ts\n * import { of, distinctUntilKeyChanged } from 'rxjs';\n *\n * of(\n *   { age: 4, name: 'Foo1' },\n *   { age: 7, name: 'Bar' },\n *   { age: 5, name: 'Foo2' },\n *   { age: 6, name: 'Foo3' }\n * ).pipe(\n *   distinctUntilKeyChanged('name', (x, y) => x.substring(0, 3) === y.substring(0, 3))\n * )\n * .subscribe(x => console.log(x));\n *\n * // displays:\n * // { age: 4, name: 'Foo1' }\n * // { age: 7, name: 'Bar' }\n * // { age: 5, name: 'Foo2' }\n * ```\n *\n * @see {@link distinct}\n * @see {@link distinctUntilChanged}\n *\n * @param key String key for object property lookup on each item.\n * @param compare Optional comparison function called to test if an item is distinct\n * from the previous item in the source.\n * @return A function that returns an Observable that emits items from the source\n * Observable with distinct values based on the key specified.\n */\nexport function distinctUntilKeyChanged<T, K extends keyof T>(\n  key: K,\n  compare?: (x: T[K], y: T[K]) => boolean\n): MonoTypeOperatorFunction<T> {\n  return distinctUntilChanged((x: T, y: T) => (compare ? compare(x[key], y[key]) : x[key] === y[key]));\n}\n"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,wBAAwB;AAqE7D,OAAM,SAAUC,uBAAuBA,CACrCC,GAAM,EACNC,OAAuC;EAEvC,OAAOH,oBAAoB,CAAC,UAACI,CAAI,EAAEC,CAAI;IAAK,OAACF,OAAO,GAAGA,OAAO,CAACC,CAAC,CAACF,GAAG,CAAC,EAAEG,CAAC,CAACH,GAAG,CAAC,CAAC,GAAGE,CAAC,CAACF,GAAG,CAAC,KAAKG,CAAC,CAACH,GAAG,CAAC;EAAtD,CAAuD,CAAC;AACtG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}