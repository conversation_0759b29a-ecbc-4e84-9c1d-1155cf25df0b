{"ast": null, "code": "import { __read, __spreadArray } from \"tslib\";\nimport { map } from \"../operators/map\";\nvar isArray = Array.isArray;\nfunction callOrApply(fn, args) {\n  return isArray(args) ? fn.apply(void 0, __spreadArray([], __read(args))) : fn(args);\n}\nexport function mapOneOrManyArgs(fn) {\n  return map(function (args) {\n    return callOrApply(fn, args);\n  });\n}", "map": {"version": 3, "names": ["map", "isArray", "Array", "callOrApply", "fn", "args", "apply", "__spread<PERSON><PERSON>y", "__read", "mapOneOrManyArgs"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\util\\mapOneOrManyArgs.ts"], "sourcesContent": ["import { OperatorFunction } from \"../types\";\nimport { map } from \"../operators/map\";\n\nconst { isArray } = Array;\n\nfunction callOrApply<T, R>(fn: ((...values: T[]) => R), args: T|T[]): R {\n    return isArray(args) ? fn(...args) : fn(args);\n}\n\n/**\n * Used in several -- mostly deprecated -- situations where we need to \n * apply a list of arguments or a single argument to a result selector.\n */\nexport function mapOneOrManyArgs<T, R>(fn: ((...values: T[]) => R)): OperatorFunction<T|T[], R> {\n    return map(args => callOrApply(fn, args))\n}"], "mappings": ";AACA,SAASA,GAAG,QAAQ,kBAAkB;AAE9B,IAAAC,OAAO,GAAKC,KAAK,CAAAD,OAAV;AAEf,SAASE,WAAWA,CAAOC,EAA2B,EAAEC,IAAW;EAC/D,OAAOJ,OAAO,CAACI,IAAI,CAAC,GAAGD,EAAE,CAAAE,KAAA,SAAAC,aAAA,KAAAC,MAAA,CAAIH,IAAI,MAAID,EAAE,CAACC,IAAI,CAAC;AACjD;AAMA,OAAM,SAAUI,gBAAgBA,CAAOL,EAA2B;EAC9D,OAAOJ,GAAG,CAAC,UAAAK,IAAI;IAAI,OAAAF,WAAW,CAACC,EAAE,EAAEC,IAAI,CAAC;EAArB,CAAqB,CAAC;AAC7C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}