{"ast": null, "code": "import { __read, __spreadArray } from \"tslib\";\nimport { merge } from './merge';\nexport function mergeWith() {\n  var otherSources = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    otherSources[_i] = arguments[_i];\n  }\n  return merge.apply(void 0, __spreadArray([], __read(otherSources)));\n}", "map": {"version": 3, "names": ["merge", "mergeWith", "otherSources", "_i", "arguments", "length", "apply", "__spread<PERSON><PERSON>y", "__read"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\mergeWith.ts"], "sourcesContent": ["import { ObservableInputTuple, OperatorFunction } from '../types';\nimport { merge } from './merge';\n\n/**\n * Merge the values from all observables to a single observable result.\n *\n * Creates an observable, that when subscribed to, subscribes to the source\n * observable, and all other sources provided as arguments. All values from\n * every source are emitted from the resulting subscription.\n *\n * When all sources complete, the resulting observable will complete.\n *\n * When any source errors, the resulting observable will error.\n *\n * ## Example\n *\n * Joining all outputs from multiple user input event streams\n *\n * ```ts\n * import { fromEvent, map, mergeWith } from 'rxjs';\n *\n * const clicks$ = fromEvent(document, 'click').pipe(map(() => 'click'));\n * const mousemoves$ = fromEvent(document, 'mousemove').pipe(map(() => 'mousemove'));\n * const dblclicks$ = fromEvent(document, 'dblclick').pipe(map(() => 'dblclick'));\n *\n * mousemoves$\n *   .pipe(mergeWith(clicks$, dblclicks$))\n *   .subscribe(x => console.log(x));\n *\n * // result (assuming user interactions)\n * // 'mousemove'\n * // 'mousemove'\n * // 'mousemove'\n * // 'click'\n * // 'click'\n * // 'dblclick'\n * ```\n *\n * @see {@link merge}\n *\n * @param otherSources the sources to combine the current source with.\n * @return A function that returns an Observable that merges the values from\n * all given Observables.\n */\nexport function mergeWith<T, A extends readonly unknown[]>(\n  ...otherSources: [...ObservableInputTuple<A>]\n): OperatorFunction<T, T | A[number]> {\n  return merge(...otherSources);\n}\n"], "mappings": ";AACA,SAASA,KAAK,QAAQ,SAAS;AA2C/B,OAAM,SAAUC,SAASA,CAAA;EACvB,IAAAC,YAAA;OAAA,IAAAC,EAAA,IAA6C,EAA7CA,EAAA,GAAAC,SAAA,CAAAC,MAA6C,EAA7CF,EAAA,EAA6C;IAA7CD,YAAA,CAAAC,EAAA,IAAAC,SAAA,CAAAD,EAAA;;EAEA,OAAOH,KAAK,CAAAM,KAAA,SAAAC,aAAA,KAAAC,MAAA,CAAIN,YAAY;AAC9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}