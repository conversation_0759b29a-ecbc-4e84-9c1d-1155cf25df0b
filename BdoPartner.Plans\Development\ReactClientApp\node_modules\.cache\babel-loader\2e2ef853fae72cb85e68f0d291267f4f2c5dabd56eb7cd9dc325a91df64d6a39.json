{"ast": null, "code": "import { __read, __spreadArray } from \"tslib\";\nimport { argsOrArgArray } from '../util/argsOrArgArray';\nimport { onErrorResumeNext as oERNCreate } from '../observable/onErrorResumeNext';\nexport function onErrorResumeNextWith() {\n  var sources = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    sources[_i] = arguments[_i];\n  }\n  var nextSources = argsOrArgArray(sources);\n  return function (source) {\n    return oERNCreate.apply(void 0, __spreadArray([source], __read(nextSources)));\n  };\n}\nexport var onErrorResumeNext = onErrorResumeNextWith;", "map": {"version": 3, "names": ["argsOrArgArray", "onErrorResumeNext", "oERNCreate", "onErrorResumeNextWith", "sources", "_i", "arguments", "length", "nextSources", "source", "apply", "__spread<PERSON><PERSON>y", "__read"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\onErrorResumeNextWith.ts"], "sourcesContent": ["import { ObservableInputTuple, OperatorFunction } from '../types';\nimport { argsOrArgArray } from '../util/argsOrArgArray';\nimport { onErrorResumeNext as oERNCreate } from '../observable/onErrorResumeNext';\n\nexport function onErrorResumeNextWith<T, A extends readonly unknown[]>(\n  sources: [...ObservableInputTuple<A>]\n): OperatorFunction<T, T | A[number]>;\nexport function onErrorResumeNextWith<T, A extends readonly unknown[]>(\n  ...sources: [...ObservableInputTuple<A>]\n): OperatorFunction<T, T | A[number]>;\n\n/**\n * When any of the provided Observable emits an complete or error notification, it immediately subscribes to the next one\n * that was passed.\n *\n * <span class=\"informal\">Execute series of Observables, subscribes to next one on error or complete.</span>\n *\n * ![](onErrorResumeNext.png)\n *\n * `onErrorResumeNext` is an operator that accepts a series of Observables, provided either directly as\n * arguments or as an array. If no single Observable is provided, returned Observable will simply behave the same\n * as the source.\n *\n * `onErrorResumeNext` returns an Observable that starts by subscribing and re-emitting values from the source Observable.\n * When its stream of values ends - no matter if Observable completed or emitted an error - `onErrorResumeNext`\n * will subscribe to the first Observable that was passed as an argument to the method. It will start re-emitting\n * its values as well and - again - when that stream ends, `onErrorResumeNext` will proceed to subscribing yet another\n * Observable in provided series, no matter if previous Observable completed or ended with an error. This will\n * be happening until there is no more Observables left in the series, at which point returned Observable will\n * complete - even if the last subscribed stream ended with an error.\n *\n * `onErrorResumeNext` can be therefore thought of as version of {@link concat} operator, which is more permissive\n * when it comes to the errors emitted by its input Observables. While `concat` subscribes to the next Observable\n * in series only if previous one successfully completed, `onErrorResumeNext` subscribes even if it ended with\n * an error.\n *\n * Note that you do not get any access to errors emitted by the Observables. In particular do not\n * expect these errors to appear in error callback passed to {@link Observable#subscribe}. If you want to take\n * specific actions based on what error was emitted by an Observable, you should try out {@link catchError} instead.\n *\n *\n * ## Example\n *\n * Subscribe to the next Observable after map fails\n *\n * ```ts\n * import { of, onErrorResumeNext, map } from 'rxjs';\n *\n * of(1, 2, 3, 0)\n *   .pipe(\n *     map(x => {\n *       if (x === 0) {\n *         throw Error();\n *       }\n *\n *       return 10 / x;\n *     }),\n *     onErrorResumeNext(of(1, 2, 3))\n *   )\n *   .subscribe({\n *     next: val => console.log(val),\n *     error: err => console.log(err),          // Will never be called.\n *     complete: () => console.log('that\\'s it!')\n *   });\n *\n * // Logs:\n * // 10\n * // 5\n * // 3.3333333333333335\n * // 1\n * // 2\n * // 3\n * // 'that's it!'\n * ```\n *\n * @see {@link concat}\n * @see {@link catchError}\n *\n * @param sources `ObservableInput`s passed either directly or as an array.\n * @return A function that returns an Observable that emits values from source\n * Observable, but - if it errors - subscribes to the next passed Observable\n * and so on, until it completes or runs out of Observables.\n */\nexport function onErrorResumeNextWith<T, A extends readonly unknown[]>(\n  ...sources: [[...ObservableInputTuple<A>]] | [...ObservableInputTuple<A>]\n): OperatorFunction<T, T | A[number]> {\n  // For some reason, TS 4.1 RC gets the inference wrong here and infers the\n  // result to be `A[number][]` - completely dropping the ObservableInput part\n  // of the type. This makes no sense whatsoever. As a workaround, the type is\n  // asserted explicitly.\n  const nextSources = argsOrArgArray(sources) as unknown as ObservableInputTuple<A>;\n\n  return (source) => oERNCreate(source, ...nextSources);\n}\n\n/**\n * @deprecated Renamed. Use {@link onErrorResumeNextWith} instead. Will be removed in v8.\n */\nexport const onErrorResumeNext = onErrorResumeNextWith;\n"], "mappings": ";AACA,SAASA,cAAc,QAAQ,wBAAwB;AACvD,SAASC,iBAAiB,IAAIC,UAAU,QAAQ,iCAAiC;AAiFjF,OAAM,SAAUC,qBAAqBA,CAAA;EACnC,IAAAC,OAAA;OAAA,IAAAC,EAAA,IAAyE,EAAzEA,EAAA,GAAAC,SAAA,CAAAC,MAAyE,EAAzEF,EAAA,EAAyE;IAAzED,OAAA,CAAAC,EAAA,IAAAC,SAAA,CAAAD,EAAA;;EAMA,IAAMG,WAAW,GAAGR,cAAc,CAACI,OAAO,CAAuC;EAEjF,OAAO,UAACK,MAAM;IAAK,OAAAP,UAAU,CAAAQ,KAAA,SAAAC,aAAA,EAACF,MAAM,GAAAG,MAAA,CAAKJ,WAAW;EAAjC,CAAkC;AACvD;AAKA,OAAO,IAAMP,iBAAiB,GAAGE,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}