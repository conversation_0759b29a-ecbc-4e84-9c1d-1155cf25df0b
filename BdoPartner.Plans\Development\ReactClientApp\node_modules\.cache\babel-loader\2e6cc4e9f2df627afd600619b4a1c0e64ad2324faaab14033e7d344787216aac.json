{"ast": null, "code": "import { identity } from '../util/identity';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function distinctUntilChanged(comparator, keySelector) {\n  if (keySelector === void 0) {\n    keySelector = identity;\n  }\n  comparator = comparator !== null && comparator !== void 0 ? comparator : defaultCompare;\n  return operate(function (source, subscriber) {\n    var previousKey;\n    var first = true;\n    source.subscribe(createOperatorSubscriber(subscriber, function (value) {\n      var currentKey = keySelector(value);\n      if (first || !comparator(previousKey, currentKey)) {\n        first = false;\n        previousKey = currentKey;\n        subscriber.next(value);\n      }\n    }));\n  });\n}\nfunction defaultCompare(a, b) {\n  return a === b;\n}", "map": {"version": 3, "names": ["identity", "operate", "createOperatorSubscriber", "distinctUntilChanged", "comparator", "keySelector", "defaultCompare", "source", "subscriber", "previousKey", "first", "subscribe", "value", "current<PERSON><PERSON>", "next", "a", "b"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\distinctUntilChanged.ts"], "sourcesContent": ["import { MonoTypeOperatorFunction } from '../types';\nimport { identity } from '../util/identity';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\n\nexport function distinctUntilChanged<T>(comparator?: (previous: T, current: T) => boolean): MonoTypeOperatorFunction<T>;\nexport function distinctUntilChanged<T, K>(\n  comparator: (previous: K, current: K) => boolean,\n  keySelector: (value: T) => K\n): MonoTypeOperatorFunction<T>;\n\n/**\n * Returns a result {@link Observable} that emits all values pushed by the source observable if they\n * are distinct in comparison to the last value the result observable emitted.\n *\n * When provided without parameters or with the first parameter (`{@link distinctUntilChanged#comparator comparator}`),\n * it behaves like this:\n *\n * 1. It will always emit the first value from the source.\n * 2. For all subsequent values pushed by the source, they will be compared to the previously emitted values\n *    using the provided `comparator` or an `===` equality check.\n * 3. If the value pushed by the source is determined to be unequal by this check, that value is emitted and\n *    becomes the new \"previously emitted value\" internally.\n *\n * When the second parameter (`{@link distinctUntilChanged#keySelector keySelector}`) is provided, the behavior\n * changes:\n *\n * 1. It will always emit the first value from the source.\n * 2. The `keySelector` will be run against all values, including the first value.\n * 3. For all values after the first, the selected key will be compared against the key selected from\n *    the previously emitted value using the `comparator`.\n * 4. If the keys are determined to be unequal by this check, the value (not the key), is emitted\n *    and the selected key from that value is saved for future comparisons against other keys.\n *\n * ## Examples\n *\n * A very basic example with no `{@link distinctUntilChanged#comparator comparator}`. Note that `1` is emitted more than once,\n * because it's distinct in comparison to the _previously emitted_ value,\n * not in comparison to _all other emitted values_.\n *\n * ```ts\n * import { of, distinctUntilChanged } from 'rxjs';\n *\n * of(1, 1, 1, 2, 2, 2, 1, 1, 3, 3)\n *   .pipe(distinctUntilChanged())\n *   .subscribe(console.log);\n * // Logs: 1, 2, 1, 3\n * ```\n *\n * With a `{@link distinctUntilChanged#comparator comparator}`, you can do custom comparisons. Let's say\n * you only want to emit a value when all of its components have\n * changed:\n *\n * ```ts\n * import { of, distinctUntilChanged } from 'rxjs';\n *\n * const totallyDifferentBuilds$ = of(\n *   { engineVersion: '1.1.0', transmissionVersion: '1.2.0' },\n *   { engineVersion: '1.1.0', transmissionVersion: '1.4.0' },\n *   { engineVersion: '1.3.0', transmissionVersion: '1.4.0' },\n *   { engineVersion: '1.3.0', transmissionVersion: '1.5.0' },\n *   { engineVersion: '2.0.0', transmissionVersion: '1.5.0' }\n * ).pipe(\n *   distinctUntilChanged((prev, curr) => {\n *     return (\n *       prev.engineVersion === curr.engineVersion ||\n *       prev.transmissionVersion === curr.transmissionVersion\n *     );\n *   })\n * );\n *\n * totallyDifferentBuilds$.subscribe(console.log);\n *\n * // Logs:\n * // { engineVersion: '1.1.0', transmissionVersion: '1.2.0' }\n * // { engineVersion: '1.3.0', transmissionVersion: '1.4.0' }\n * // { engineVersion: '2.0.0', transmissionVersion: '1.5.0' }\n * ```\n *\n * You can also provide a custom `{@link distinctUntilChanged#comparator comparator}` to check that emitted\n * changes are only in one direction. Let's say you only want to get\n * the next record temperature:\n *\n * ```ts\n * import { of, distinctUntilChanged } from 'rxjs';\n *\n * const temps$ = of(30, 31, 20, 34, 33, 29, 35, 20);\n *\n * const recordHighs$ = temps$.pipe(\n *   distinctUntilChanged((prevHigh, temp) => {\n *     // If the current temp is less than\n *     // or the same as the previous record,\n *     // the record hasn't changed.\n *     return temp <= prevHigh;\n *   })\n * );\n *\n * recordHighs$.subscribe(console.log);\n * // Logs: 30, 31, 34, 35\n * ```\n *\n * Selecting update events only when the `updatedBy` field shows\n * the account changed hands.\n *\n * ```ts\n * import { of, distinctUntilChanged } from 'rxjs';\n *\n * // A stream of updates to a given account\n * const accountUpdates$ = of(\n *   { updatedBy: 'blesh', data: [] },\n *   { updatedBy: 'blesh', data: [] },\n *   { updatedBy: 'ncjamieson', data: [] },\n *   { updatedBy: 'ncjamieson', data: [] },\n *   { updatedBy: 'blesh', data: [] }\n * );\n *\n * // We only want the events where it changed hands\n * const changedHands$ = accountUpdates$.pipe(\n *   distinctUntilChanged(undefined, update => update.updatedBy)\n * );\n *\n * changedHands$.subscribe(console.log);\n * // Logs:\n * // { updatedBy: 'blesh', data: Array[0] }\n * // { updatedBy: 'ncjamieson', data: Array[0] }\n * // { updatedBy: 'blesh', data: Array[0] }\n * ```\n *\n * @see {@link distinct}\n * @see {@link distinctUntilKeyChanged}\n *\n * @param comparator A function used to compare the previous and current keys for\n * equality. Defaults to a `===` check.\n * @param keySelector Used to select a key value to be passed to the `comparator`.\n *\n * @return A function that returns an Observable that emits items from the\n * source Observable with distinct values.\n */\nexport function distinctUntilChanged<T, K>(\n  comparator?: (previous: K, current: K) => boolean,\n  keySelector: (value: T) => K = identity as (value: T) => K\n): MonoTypeOperatorFunction<T> {\n  // We've been allowing `null` do be passed as the `compare`, so we can't do\n  // a default value for the parameter, because that will only work\n  // for `undefined`.\n  comparator = comparator ?? defaultCompare;\n\n  return operate((source, subscriber) => {\n    // The previous key, used to compare against keys selected\n    // from new arrivals to determine \"distinctiveness\".\n    let previousKey: K;\n    // Whether or not this is the first value we've gotten.\n    let first = true;\n\n    source.subscribe(\n      createOperatorSubscriber(subscriber, (value) => {\n        // We always call the key selector.\n        const currentKey = keySelector(value);\n\n        // If it's the first value, we always emit it.\n        // Otherwise, we compare this key to the previous key, and\n        // if the comparer returns false, we emit.\n        if (first || !comparator!(previousKey, currentKey)) {\n          // Update our state *before* we emit the value\n          // as emission can be the source of re-entrant code\n          // in functional libraries like this. We only really\n          // need to do this if it's the first value, or if the\n          // key we're tracking in previous needs to change.\n          first = false;\n          previousKey = currentKey;\n\n          // Emit the value!\n          subscriber.next(value);\n        }\n      })\n    );\n  });\n}\n\nfunction defaultCompare(a: any, b: any) {\n  return a === b;\n}\n"], "mappings": "AACA,SAASA,QAAQ,QAAQ,kBAAkB;AAC3C,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAuI/D,OAAM,SAAUC,oBAAoBA,CAClCC,UAAiD,EACjDC,WAA0D;EAA1D,IAAAA,WAAA;IAAAA,WAAA,GAA+BL,QAA2B;EAAA;EAK1DI,UAAU,GAAGA,UAAU,aAAVA,UAAU,cAAVA,UAAU,GAAIE,cAAc;EAEzC,OAAOL,OAAO,CAAC,UAACM,MAAM,EAAEC,UAAU;IAGhC,IAAIC,WAAc;IAElB,IAAIC,KAAK,GAAG,IAAI;IAEhBH,MAAM,CAACI,SAAS,CACdT,wBAAwB,CAACM,UAAU,EAAE,UAACI,KAAK;MAEzC,IAAMC,UAAU,GAAGR,WAAW,CAACO,KAAK,CAAC;MAKrC,IAAIF,KAAK,IAAI,CAACN,UAAW,CAACK,WAAW,EAAEI,UAAU,CAAC,EAAE;QAMlDH,KAAK,GAAG,KAAK;QACbD,WAAW,GAAGI,UAAU;QAGxBL,UAAU,CAACM,IAAI,CAACF,KAAK,CAAC;;IAE1B,CAAC,CAAC,CACH;EACH,CAAC,CAAC;AACJ;AAEA,SAASN,cAAcA,CAACS,CAAM,EAAEC,CAAM;EACpC,OAAOD,CAAC,KAAKC,CAAC;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}