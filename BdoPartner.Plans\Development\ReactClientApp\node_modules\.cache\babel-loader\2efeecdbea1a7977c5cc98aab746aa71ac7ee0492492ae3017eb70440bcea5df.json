{"ast": null, "code": "export function createErrorClass(createImpl) {\n  var _super = function (instance) {\n    Error.call(instance);\n    instance.stack = new Error().stack;\n  };\n  var ctorFunc = createImpl(_super);\n  ctorFunc.prototype = Object.create(Error.prototype);\n  ctorFunc.prototype.constructor = ctorFunc;\n  return ctorFunc;\n}", "map": {"version": 3, "names": ["createErrorClass", "createImpl", "_super", "instance", "Error", "call", "stack", "ctorFunc", "prototype", "Object", "create", "constructor"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\util\\createErrorClass.ts"], "sourcesContent": ["/**\n * Used to create Error subclasses until the community moves away from ES5.\n *\n * This is because compiling from TypeScript down to ES5 has issues with subclassing Errors\n * as well as other built-in types: https://github.com/Microsoft/TypeScript/issues/12123\n *\n * @param createImpl A factory function to create the actual constructor implementation. The returned\n * function should be a named function that calls `_super` internally.\n */\nexport function createErrorClass<T>(createImpl: (_super: any) => any): T {\n  const _super = (instance: any) => {\n    Error.call(instance);\n    instance.stack = new Error().stack;\n  };\n\n  const ctorFunc = createImpl(_super);\n  ctorFunc.prototype = Object.create(Error.prototype);\n  ctorFunc.prototype.constructor = ctorFunc;\n  return ctorFunc;\n}\n"], "mappings": "AASA,OAAM,SAAUA,gBAAgBA,CAAIC,UAAgC;EAClE,IAAMC,MAAM,GAAG,SAAAA,CAACC,QAAa;IAC3BC,KAAK,CAACC,IAAI,CAACF,QAAQ,CAAC;IACpBA,QAAQ,CAACG,KAAK,GAAG,IAAIF,KAAK,EAAE,CAACE,KAAK;EACpC,CAAC;EAED,IAAMC,QAAQ,GAAGN,UAAU,CAACC,MAAM,CAAC;EACnCK,QAAQ,CAACC,SAAS,GAAGC,MAAM,CAACC,MAAM,CAACN,KAAK,CAACI,SAAS,CAAC;EACnDD,QAAQ,CAACC,SAAS,CAACG,WAAW,GAAGJ,QAAQ;EACzC,OAAOA,QAAQ;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}