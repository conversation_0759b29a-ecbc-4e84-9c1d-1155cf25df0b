{"ast": null, "code": "/** Corporate with server side project, Enumerations.PartnerPlanCycle definitions. */\nexport const PartnerPlanCycle = {\n  /** Planning cycle. 0 */\n  Planning: 0,\n  /** Mid Year Review cycle. 1 */\n  MidYearReview: 1,\n  /** Year End Review cycle. 2 */\n  YearEndReview: 2\n};\n\n/** Helper function to get cycle display name */\nexport const getCycleDisplayName = cycle => {\n  switch (cycle) {\n    case PartnerPlanCycle.Planning:\n      return 'Planning';\n    case PartnerPlanCycle.MidYearReview:\n      return 'Mid Year Review';\n    case PartnerPlanCycle.YearEndReview:\n      return 'Year End Review';\n    default:\n      return 'Unknown';\n  }\n};\n\n/** Get cycle options for dropdowns */\nexport const getCycleOptions = () => [{\n  label: 'Planning',\n  value: PartnerPlanCycle.Planning\n}, {\n  label: 'Mid Year Review',\n  value: PartnerPlanCycle.MidYearReview\n}, {\n  label: 'Year End Review',\n  value: PartnerPlanCycle.YearEndReview\n}];", "map": {"version": 3, "names": ["PartnerPlanCycle", "Planning", "MidYearReview", "YearEndReview", "getCycleDisplayName", "cycle", "getCycleOptions", "label", "value"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/core/enumertions/partnerPlanCycle.js"], "sourcesContent": ["/** Corporate with server side project, Enumerations.PartnerPlanCycle definitions. */\r\nexport const PartnerPlanCycle = {\r\n  /** Planning cycle. 0 */\r\n  Planning: 0,\r\n  /** Mid Year Review cycle. 1 */\r\n  MidYearReview: 1,\r\n  /** Year End Review cycle. 2 */\r\n  YearEndReview: 2,\r\n};\r\n\r\n/** Helper function to get cycle display name */\r\nexport const getCycleDisplayName = (cycle) => {\r\n  switch (cycle) {\r\n    case PartnerPlanCycle.Planning:\r\n      return 'Planning';\r\n    case PartnerPlanCycle.MidYearReview:\r\n      return 'Mid Year Review';\r\n    case PartnerPlanCycle.YearEndReview:\r\n      return 'Year End Review';\r\n    default:\r\n      return 'Unknown';\r\n  }\r\n};\r\n\r\n/** Get cycle options for dropdowns */\r\nexport const getCycleOptions = () => [\r\n  { label: 'Planning', value: PartnerPlanCycle.Planning },\r\n  { label: 'Mid Year Review', value: PartnerPlanCycle.MidYearReview },\r\n  { label: 'Year End Review', value: PartnerPlanCycle.YearEndReview },\r\n];\r\n"], "mappings": "AAAA;AACA,OAAO,MAAMA,gBAAgB,GAAG;EAC9B;EACAC,QAAQ,EAAE,CAAC;EACX;EACAC,aAAa,EAAE,CAAC;EAChB;EACAC,aAAa,EAAE;AACjB,CAAC;;AAED;AACA,OAAO,MAAMC,mBAAmB,GAAIC,KAAK,IAAK;EAC5C,QAAQA,KAAK;IACX,KAAKL,gBAAgB,CAACC,QAAQ;MAC5B,OAAO,UAAU;IACnB,KAAKD,gBAAgB,CAACE,aAAa;MACjC,OAAO,iBAAiB;IAC1B,KAAKF,gBAAgB,CAACG,aAAa;MACjC,OAAO,iBAAiB;IAC1B;MACE,OAAO,SAAS;EACpB;AACF,CAAC;;AAED;AACA,OAAO,MAAMG,eAAe,GAAGA,CAAA,KAAM,CACnC;EAAEC,KAAK,EAAE,UAAU;EAAEC,KAAK,EAAER,gBAAgB,CAACC;AAAS,CAAC,EACvD;EAAEM,KAAK,EAAE,iBAAiB;EAAEC,KAAK,EAAER,gBAAgB,CAACE;AAAc,CAAC,EACnE;EAAEK,KAAK,EAAE,iBAAiB;EAAEC,KAAK,EAAER,gBAAgB,CAACG;AAAc,CAAC,CACpE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}