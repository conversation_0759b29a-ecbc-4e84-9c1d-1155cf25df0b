{"ast": null, "code": "import { Fragment, isValidElement, cloneElement, createElement, Children } from 'react';\nimport HTML from 'html-parse-stringify';\nimport { isObject, isString, warn, warnOnce } from './utils.js';\nimport { getDefaults } from './defaults.js';\nimport { getI18n } from './i18nInstance.js';\nconst hasChildren = (node, checkLength) => {\n  if (!node) return false;\n  const base = node.props ? node.props.children : node.children;\n  if (checkLength) return base.length > 0;\n  return !!base;\n};\nconst getChildren = node => {\n  if (!node) return [];\n  const children = node.props ? node.props.children : node.children;\n  return node.props && node.props.i18nIsDynamicList ? getAsArray(children) : children;\n};\nconst hasValidReactChildren = children => Array.isArray(children) && children.every(isValidElement);\nconst getAsArray = data => Array.isArray(data) ? data : [data];\nconst mergeProps = (source, target) => {\n  const newTarget = {\n    ...target\n  };\n  newTarget.props = Object.assign(source.props, target.props);\n  return newTarget;\n};\nexport const nodesToString = (children, i18nOptions) => {\n  if (!children) return '';\n  let stringNode = '';\n  const childrenArray = getAsArray(children);\n  const keepArray = i18nOptions.transSupportBasicHtmlNodes && i18nOptions.transKeepBasicHtmlNodesFor ? i18nOptions.transKeepBasicHtmlNodesFor : [];\n  childrenArray.forEach((child, childIndex) => {\n    if (isString(child)) {\n      stringNode += `${child}`;\n    } else if (isValidElement(child)) {\n      const {\n        props,\n        type\n      } = child;\n      const childPropsCount = Object.keys(props).length;\n      const shouldKeepChild = keepArray.indexOf(type) > -1;\n      const childChildren = props.children;\n      if (!childChildren && shouldKeepChild && !childPropsCount) {\n        stringNode += `<${type}/>`;\n      } else if (!childChildren && (!shouldKeepChild || childPropsCount) || props.i18nIsDynamicList) {\n        stringNode += `<${childIndex}></${childIndex}>`;\n      } else if (shouldKeepChild && childPropsCount === 1 && isString(childChildren)) {\n        stringNode += `<${type}>${childChildren}</${type}>`;\n      } else {\n        const content = nodesToString(childChildren, i18nOptions);\n        stringNode += `<${childIndex}>${content}</${childIndex}>`;\n      }\n    } else if (child === null) {\n      warn(`Trans: the passed in value is invalid - seems you passed in a null child.`);\n    } else if (isObject(child)) {\n      const {\n        format,\n        ...clone\n      } = child;\n      const keys = Object.keys(clone);\n      if (keys.length === 1) {\n        const value = format ? `${keys[0]}, ${format}` : keys[0];\n        stringNode += `{{${value}}}`;\n      } else {\n        warn(`react-i18next: the passed in object contained more than one variable - the object should look like {{ value, format }} where format is optional.`, child);\n      }\n    } else {\n      warn(`Trans: the passed in value is invalid - seems you passed in a variable like {number} - please pass in variables for interpolation as full objects like {{number}}.`, child);\n    }\n  });\n  return stringNode;\n};\nconst renderNodes = (children, targetString, i18n, i18nOptions, combinedTOpts, shouldUnescape) => {\n  if (targetString === '') return [];\n  const keepArray = i18nOptions.transKeepBasicHtmlNodesFor || [];\n  const emptyChildrenButNeedsHandling = targetString && new RegExp(keepArray.map(keep => `<${keep}`).join('|')).test(targetString);\n  if (!children && !emptyChildrenButNeedsHandling && !shouldUnescape) return [targetString];\n  const data = {};\n  const getData = childs => {\n    const childrenArray = getAsArray(childs);\n    childrenArray.forEach(child => {\n      if (isString(child)) return;\n      if (hasChildren(child)) getData(getChildren(child));else if (isObject(child) && !isValidElement(child)) Object.assign(data, child);\n    });\n  };\n  getData(children);\n  const ast = HTML.parse(`<0>${targetString}</0>`);\n  const opts = {\n    ...data,\n    ...combinedTOpts\n  };\n  const renderInner = (child, node, rootReactNode) => {\n    const childs = getChildren(child);\n    const mappedChildren = mapAST(childs, node.children, rootReactNode);\n    return hasValidReactChildren(childs) && mappedChildren.length === 0 || child.props && child.props.i18nIsDynamicList ? childs : mappedChildren;\n  };\n  const pushTranslatedJSX = (child, inner, mem, i, isVoid) => {\n    if (child.dummy) {\n      child.children = inner;\n      mem.push(cloneElement(child, {\n        key: i\n      }, isVoid ? undefined : inner));\n    } else {\n      mem.push(...Children.map([child], c => {\n        const props = {\n          ...c.props\n        };\n        delete props.i18nIsDynamicList;\n        return createElement(c.type, {\n          ...props,\n          key: i,\n          ref: c.ref\n        }, isVoid ? null : inner);\n      }));\n    }\n  };\n  const mapAST = (reactNode, astNode, rootReactNode) => {\n    const reactNodes = getAsArray(reactNode);\n    const astNodes = getAsArray(astNode);\n    return astNodes.reduce((mem, node, i) => {\n      const translationContent = node.children && node.children[0] && node.children[0].content && i18n.services.interpolator.interpolate(node.children[0].content, opts, i18n.language);\n      if (node.type === 'tag') {\n        let tmp = reactNodes[parseInt(node.name, 10)];\n        if (rootReactNode.length === 1 && !tmp) tmp = rootReactNode[0][node.name];\n        if (!tmp) tmp = {};\n        const child = Object.keys(node.attrs).length !== 0 ? mergeProps({\n          props: node.attrs\n        }, tmp) : tmp;\n        const isElement = isValidElement(child);\n        const isValidTranslationWithChildren = isElement && hasChildren(node, true) && !node.voidElement;\n        const isEmptyTransWithHTML = emptyChildrenButNeedsHandling && isObject(child) && child.dummy && !isElement;\n        const isKnownComponent = isObject(children) && Object.hasOwnProperty.call(children, node.name);\n        if (isString(child)) {\n          const value = i18n.services.interpolator.interpolate(child, opts, i18n.language);\n          mem.push(value);\n        } else if (hasChildren(child) || isValidTranslationWithChildren) {\n          const inner = renderInner(child, node, rootReactNode);\n          pushTranslatedJSX(child, inner, mem, i);\n        } else if (isEmptyTransWithHTML) {\n          const inner = mapAST(reactNodes, node.children, rootReactNode);\n          pushTranslatedJSX(child, inner, mem, i);\n        } else if (Number.isNaN(parseFloat(node.name))) {\n          if (isKnownComponent) {\n            const inner = renderInner(child, node, rootReactNode);\n            pushTranslatedJSX(child, inner, mem, i, node.voidElement);\n          } else if (i18nOptions.transSupportBasicHtmlNodes && keepArray.indexOf(node.name) > -1) {\n            if (node.voidElement) {\n              mem.push(createElement(node.name, {\n                key: `${node.name}-${i}`\n              }));\n            } else {\n              const inner = mapAST(reactNodes, node.children, rootReactNode);\n              mem.push(createElement(node.name, {\n                key: `${node.name}-${i}`\n              }, inner));\n            }\n          } else if (node.voidElement) {\n            mem.push(`<${node.name} />`);\n          } else {\n            const inner = mapAST(reactNodes, node.children, rootReactNode);\n            mem.push(`<${node.name}>${inner}</${node.name}>`);\n          }\n        } else if (isObject(child) && !isElement) {\n          const content = node.children[0] ? translationContent : null;\n          if (content) mem.push(content);\n        } else {\n          pushTranslatedJSX(child, translationContent, mem, i, node.children.length !== 1 || !translationContent);\n        }\n      } else if (node.type === 'text') {\n        const wrapTextNodes = i18nOptions.transWrapTextNodes;\n        const content = shouldUnescape ? i18nOptions.unescape(i18n.services.interpolator.interpolate(node.content, opts, i18n.language)) : i18n.services.interpolator.interpolate(node.content, opts, i18n.language);\n        if (wrapTextNodes) {\n          mem.push(createElement(wrapTextNodes, {\n            key: `${node.name}-${i}`\n          }, content));\n        } else {\n          mem.push(content);\n        }\n      }\n      return mem;\n    }, []);\n  };\n  const result = mapAST([{\n    dummy: true,\n    children: children || []\n  }], ast, getAsArray(children || []));\n  return getChildren(result[0]);\n};\nexport function Trans(_ref) {\n  let {\n    children,\n    count,\n    parent,\n    i18nKey,\n    context,\n    tOptions = {},\n    values,\n    defaults,\n    components,\n    ns,\n    i18n: i18nFromProps,\n    t: tFromProps,\n    shouldUnescape,\n    ...additionalProps\n  } = _ref;\n  const i18n = i18nFromProps || getI18n();\n  if (!i18n) {\n    warnOnce('You will need to pass in an i18next instance by using i18nextReactModule');\n    return children;\n  }\n  const t = tFromProps || i18n.t.bind(i18n) || (k => k);\n  const reactI18nextOptions = {\n    ...getDefaults(),\n    ...(i18n.options && i18n.options.react)\n  };\n  let namespaces = ns || t.ns || i18n.options && i18n.options.defaultNS;\n  namespaces = isString(namespaces) ? [namespaces] : namespaces || ['translation'];\n  const nodeAsString = nodesToString(children, reactI18nextOptions);\n  const defaultValue = defaults || nodeAsString || reactI18nextOptions.transEmptyNodeValue || i18nKey;\n  const {\n    hashTransKey\n  } = reactI18nextOptions;\n  const key = i18nKey || (hashTransKey ? hashTransKey(nodeAsString || defaultValue) : nodeAsString || defaultValue);\n  if (i18n.options && i18n.options.interpolation && i18n.options.interpolation.defaultVariables) {\n    values = values && Object.keys(values).length > 0 ? {\n      ...values,\n      ...i18n.options.interpolation.defaultVariables\n    } : {\n      ...i18n.options.interpolation.defaultVariables\n    };\n  }\n  const interpolationOverride = values || count !== undefined || !children ? tOptions.interpolation : {\n    interpolation: {\n      ...tOptions.interpolation,\n      prefix: '#$?',\n      suffix: '?$#'\n    }\n  };\n  const combinedTOpts = {\n    ...tOptions,\n    context: context || tOptions.context,\n    count,\n    ...values,\n    ...interpolationOverride,\n    defaultValue,\n    ns: namespaces\n  };\n  const translation = key ? t(key, combinedTOpts) : defaultValue;\n  if (components) {\n    Object.keys(components).forEach(c => {\n      const comp = components[c];\n      if (typeof comp.type === 'function' || !comp.props || !comp.props.children || translation.indexOf(`${c}/>`) < 0 && translation.indexOf(`${c} />`) < 0) return;\n      function Componentized() {\n        return createElement(Fragment, null, comp);\n      }\n      components[c] = createElement(Componentized);\n    });\n  }\n  const content = renderNodes(components || children, translation, i18n, reactI18nextOptions, combinedTOpts, shouldUnescape);\n  const useAsParent = parent !== undefined ? parent : reactI18nextOptions.defaultTransParent;\n  return useAsParent ? createElement(useAsParent, additionalProps, content) : content;\n}", "map": {"version": 3, "names": ["Fragment", "isValidElement", "cloneElement", "createElement", "Children", "HTML", "isObject", "isString", "warn", "warnOnce", "getDefaults", "getI18n", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "node", "checkLength", "base", "props", "children", "length", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i18nIsDynamicList", "getAsArray", "hasValidReactChildren", "Array", "isArray", "every", "data", "mergeProps", "source", "target", "newTarget", "Object", "assign", "nodesToString", "i18nOptions", "stringNode", "childrenA<PERSON>y", "keepArray", "transSupportBasicHtmlNodes", "transKeepBasicHtmlNodesFor", "for<PERSON>ach", "child", "childIndex", "type", "childPropsCount", "keys", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indexOf", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "content", "format", "clone", "value", "renderNodes", "targetString", "i18n", "combinedTOpts", "shouldUnescape", "emptyChildrenButNeedsHandling", "RegExp", "map", "keep", "join", "test", "getData", "childs", "ast", "parse", "opts", "renderInner", "rootReactNode", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mapAST", "pushTranslatedJSX", "inner", "mem", "i", "isVoid", "dummy", "push", "key", "undefined", "c", "ref", "reactNode", "astNode", "reactNodes", "astNodes", "reduce", "translationContent", "services", "interpolator", "interpolate", "language", "tmp", "parseInt", "name", "attrs", "isElement", "isValidTranslationWithChildren", "voidElement", "isEmptyTransWithHTML", "isKnownComponent", "hasOwnProperty", "call", "Number", "isNaN", "parseFloat", "wrapTextNodes", "transWrapTextNodes", "unescape", "result", "Trans", "_ref", "count", "parent", "i18nKey", "context", "tOptions", "values", "defaults", "components", "ns", "i18nFromProps", "t", "tFromProps", "additionalProps", "bind", "k", "reactI18nextOptions", "options", "react", "namespaces", "defaultNS", "nodeAsString", "defaultValue", "transEmptyNodeValue", "hashTransKey", "interpolation", "defaultVariables", "interpolationOverride", "prefix", "suffix", "translation", "comp", "Componentized", "useAsParent", "defaultTransParent"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/node_modules/react-i18next/dist/es/TransWithoutContext.js"], "sourcesContent": ["import { Fragment, isValidElement, cloneElement, createElement, Children } from 'react';\nimport HTML from 'html-parse-stringify';\nimport { isObject, isString, warn, warnOnce } from './utils.js';\nimport { getDefaults } from './defaults.js';\nimport { getI18n } from './i18nInstance.js';\nconst hasChildren = (node, checkLength) => {\n  if (!node) return false;\n  const base = node.props ? node.props.children : node.children;\n  if (checkLength) return base.length > 0;\n  return !!base;\n};\nconst getChildren = node => {\n  if (!node) return [];\n  const children = node.props ? node.props.children : node.children;\n  return node.props && node.props.i18nIsDynamicList ? getAsArray(children) : children;\n};\nconst hasValidReactChildren = children => Array.isArray(children) && children.every(isValidElement);\nconst getAsArray = data => Array.isArray(data) ? data : [data];\nconst mergeProps = (source, target) => {\n  const newTarget = {\n    ...target\n  };\n  newTarget.props = Object.assign(source.props, target.props);\n  return newTarget;\n};\nexport const nodesToString = (children, i18nOptions) => {\n  if (!children) return '';\n  let stringNode = '';\n  const childrenArray = getAsArray(children);\n  const keepArray = i18nOptions.transSupportBasicHtmlNodes && i18nOptions.transKeepBasicHtmlNodesFor ? i18nOptions.transKeepBasicHtmlNodesFor : [];\n  childrenArray.forEach((child, childIndex) => {\n    if (isString(child)) {\n      stringNode += `${child}`;\n    } else if (isValidElement(child)) {\n      const {\n        props,\n        type\n      } = child;\n      const childPropsCount = Object.keys(props).length;\n      const shouldKeepChild = keepArray.indexOf(type) > -1;\n      const childChildren = props.children;\n      if (!childChildren && shouldKeepChild && !childPropsCount) {\n        stringNode += `<${type}/>`;\n      } else if (!childChildren && (!shouldKeepChild || childPropsCount) || props.i18nIsDynamicList) {\n        stringNode += `<${childIndex}></${childIndex}>`;\n      } else if (shouldKeepChild && childPropsCount === 1 && isString(childChildren)) {\n        stringNode += `<${type}>${childChildren}</${type}>`;\n      } else {\n        const content = nodesToString(childChildren, i18nOptions);\n        stringNode += `<${childIndex}>${content}</${childIndex}>`;\n      }\n    } else if (child === null) {\n      warn(`Trans: the passed in value is invalid - seems you passed in a null child.`);\n    } else if (isObject(child)) {\n      const {\n        format,\n        ...clone\n      } = child;\n      const keys = Object.keys(clone);\n      if (keys.length === 1) {\n        const value = format ? `${keys[0]}, ${format}` : keys[0];\n        stringNode += `{{${value}}}`;\n      } else {\n        warn(`react-i18next: the passed in object contained more than one variable - the object should look like {{ value, format }} where format is optional.`, child);\n      }\n    } else {\n      warn(`Trans: the passed in value is invalid - seems you passed in a variable like {number} - please pass in variables for interpolation as full objects like {{number}}.`, child);\n    }\n  });\n  return stringNode;\n};\nconst renderNodes = (children, targetString, i18n, i18nOptions, combinedTOpts, shouldUnescape) => {\n  if (targetString === '') return [];\n  const keepArray = i18nOptions.transKeepBasicHtmlNodesFor || [];\n  const emptyChildrenButNeedsHandling = targetString && new RegExp(keepArray.map(keep => `<${keep}`).join('|')).test(targetString);\n  if (!children && !emptyChildrenButNeedsHandling && !shouldUnescape) return [targetString];\n  const data = {};\n  const getData = childs => {\n    const childrenArray = getAsArray(childs);\n    childrenArray.forEach(child => {\n      if (isString(child)) return;\n      if (hasChildren(child)) getData(getChildren(child));else if (isObject(child) && !isValidElement(child)) Object.assign(data, child);\n    });\n  };\n  getData(children);\n  const ast = HTML.parse(`<0>${targetString}</0>`);\n  const opts = {\n    ...data,\n    ...combinedTOpts\n  };\n  const renderInner = (child, node, rootReactNode) => {\n    const childs = getChildren(child);\n    const mappedChildren = mapAST(childs, node.children, rootReactNode);\n    return hasValidReactChildren(childs) && mappedChildren.length === 0 || child.props && child.props.i18nIsDynamicList ? childs : mappedChildren;\n  };\n  const pushTranslatedJSX = (child, inner, mem, i, isVoid) => {\n    if (child.dummy) {\n      child.children = inner;\n      mem.push(cloneElement(child, {\n        key: i\n      }, isVoid ? undefined : inner));\n    } else {\n      mem.push(...Children.map([child], c => {\n        const props = {\n          ...c.props\n        };\n        delete props.i18nIsDynamicList;\n        return createElement(c.type, {\n          ...props,\n          key: i,\n          ref: c.ref\n        }, isVoid ? null : inner);\n      }));\n    }\n  };\n  const mapAST = (reactNode, astNode, rootReactNode) => {\n    const reactNodes = getAsArray(reactNode);\n    const astNodes = getAsArray(astNode);\n    return astNodes.reduce((mem, node, i) => {\n      const translationContent = node.children && node.children[0] && node.children[0].content && i18n.services.interpolator.interpolate(node.children[0].content, opts, i18n.language);\n      if (node.type === 'tag') {\n        let tmp = reactNodes[parseInt(node.name, 10)];\n        if (rootReactNode.length === 1 && !tmp) tmp = rootReactNode[0][node.name];\n        if (!tmp) tmp = {};\n        const child = Object.keys(node.attrs).length !== 0 ? mergeProps({\n          props: node.attrs\n        }, tmp) : tmp;\n        const isElement = isValidElement(child);\n        const isValidTranslationWithChildren = isElement && hasChildren(node, true) && !node.voidElement;\n        const isEmptyTransWithHTML = emptyChildrenButNeedsHandling && isObject(child) && child.dummy && !isElement;\n        const isKnownComponent = isObject(children) && Object.hasOwnProperty.call(children, node.name);\n        if (isString(child)) {\n          const value = i18n.services.interpolator.interpolate(child, opts, i18n.language);\n          mem.push(value);\n        } else if (hasChildren(child) || isValidTranslationWithChildren) {\n          const inner = renderInner(child, node, rootReactNode);\n          pushTranslatedJSX(child, inner, mem, i);\n        } else if (isEmptyTransWithHTML) {\n          const inner = mapAST(reactNodes, node.children, rootReactNode);\n          pushTranslatedJSX(child, inner, mem, i);\n        } else if (Number.isNaN(parseFloat(node.name))) {\n          if (isKnownComponent) {\n            const inner = renderInner(child, node, rootReactNode);\n            pushTranslatedJSX(child, inner, mem, i, node.voidElement);\n          } else if (i18nOptions.transSupportBasicHtmlNodes && keepArray.indexOf(node.name) > -1) {\n            if (node.voidElement) {\n              mem.push(createElement(node.name, {\n                key: `${node.name}-${i}`\n              }));\n            } else {\n              const inner = mapAST(reactNodes, node.children, rootReactNode);\n              mem.push(createElement(node.name, {\n                key: `${node.name}-${i}`\n              }, inner));\n            }\n          } else if (node.voidElement) {\n            mem.push(`<${node.name} />`);\n          } else {\n            const inner = mapAST(reactNodes, node.children, rootReactNode);\n            mem.push(`<${node.name}>${inner}</${node.name}>`);\n          }\n        } else if (isObject(child) && !isElement) {\n          const content = node.children[0] ? translationContent : null;\n          if (content) mem.push(content);\n        } else {\n          pushTranslatedJSX(child, translationContent, mem, i, node.children.length !== 1 || !translationContent);\n        }\n      } else if (node.type === 'text') {\n        const wrapTextNodes = i18nOptions.transWrapTextNodes;\n        const content = shouldUnescape ? i18nOptions.unescape(i18n.services.interpolator.interpolate(node.content, opts, i18n.language)) : i18n.services.interpolator.interpolate(node.content, opts, i18n.language);\n        if (wrapTextNodes) {\n          mem.push(createElement(wrapTextNodes, {\n            key: `${node.name}-${i}`\n          }, content));\n        } else {\n          mem.push(content);\n        }\n      }\n      return mem;\n    }, []);\n  };\n  const result = mapAST([{\n    dummy: true,\n    children: children || []\n  }], ast, getAsArray(children || []));\n  return getChildren(result[0]);\n};\nexport function Trans(_ref) {\n  let {\n    children,\n    count,\n    parent,\n    i18nKey,\n    context,\n    tOptions = {},\n    values,\n    defaults,\n    components,\n    ns,\n    i18n: i18nFromProps,\n    t: tFromProps,\n    shouldUnescape,\n    ...additionalProps\n  } = _ref;\n  const i18n = i18nFromProps || getI18n();\n  if (!i18n) {\n    warnOnce('You will need to pass in an i18next instance by using i18nextReactModule');\n    return children;\n  }\n  const t = tFromProps || i18n.t.bind(i18n) || (k => k);\n  const reactI18nextOptions = {\n    ...getDefaults(),\n    ...(i18n.options && i18n.options.react)\n  };\n  let namespaces = ns || t.ns || i18n.options && i18n.options.defaultNS;\n  namespaces = isString(namespaces) ? [namespaces] : namespaces || ['translation'];\n  const nodeAsString = nodesToString(children, reactI18nextOptions);\n  const defaultValue = defaults || nodeAsString || reactI18nextOptions.transEmptyNodeValue || i18nKey;\n  const {\n    hashTransKey\n  } = reactI18nextOptions;\n  const key = i18nKey || (hashTransKey ? hashTransKey(nodeAsString || defaultValue) : nodeAsString || defaultValue);\n  if (i18n.options && i18n.options.interpolation && i18n.options.interpolation.defaultVariables) {\n    values = values && Object.keys(values).length > 0 ? {\n      ...values,\n      ...i18n.options.interpolation.defaultVariables\n    } : {\n      ...i18n.options.interpolation.defaultVariables\n    };\n  }\n  const interpolationOverride = values || count !== undefined || !children ? tOptions.interpolation : {\n    interpolation: {\n      ...tOptions.interpolation,\n      prefix: '#$?',\n      suffix: '?$#'\n    }\n  };\n  const combinedTOpts = {\n    ...tOptions,\n    context: context || tOptions.context,\n    count,\n    ...values,\n    ...interpolationOverride,\n    defaultValue,\n    ns: namespaces\n  };\n  const translation = key ? t(key, combinedTOpts) : defaultValue;\n  if (components) {\n    Object.keys(components).forEach(c => {\n      const comp = components[c];\n      if (typeof comp.type === 'function' || !comp.props || !comp.props.children || translation.indexOf(`${c}/>`) < 0 && translation.indexOf(`${c} />`) < 0) return;\n      function Componentized() {\n        return createElement(Fragment, null, comp);\n      }\n      components[c] = createElement(Componentized);\n    });\n  }\n  const content = renderNodes(components || children, translation, i18n, reactI18nextOptions, combinedTOpts, shouldUnescape);\n  const useAsParent = parent !== undefined ? parent : reactI18nextOptions.defaultTransParent;\n  return useAsParent ? createElement(useAsParent, additionalProps, content) : content;\n}"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,cAAc,EAAEC,YAAY,EAAEC,aAAa,EAAEC,QAAQ,QAAQ,OAAO;AACvF,OAAOC,IAAI,MAAM,sBAAsB;AACvC,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,QAAQ,QAAQ,YAAY;AAC/D,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,OAAO,QAAQ,mBAAmB;AAC3C,MAAMC,WAAW,GAAGA,CAACC,IAAI,EAAEC,WAAW,KAAK;EACzC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EACvB,MAAME,IAAI,GAAGF,IAAI,CAACG,KAAK,GAAGH,IAAI,CAACG,KAAK,CAACC,QAAQ,GAAGJ,IAAI,CAACI,QAAQ;EAC7D,IAAIH,WAAW,EAAE,OAAOC,IAAI,CAACG,MAAM,GAAG,CAAC;EACvC,OAAO,CAAC,CAACH,IAAI;AACf,CAAC;AACD,MAAMI,WAAW,GAAGN,IAAI,IAAI;EAC1B,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;EACpB,MAAMI,QAAQ,GAAGJ,IAAI,CAACG,KAAK,GAAGH,IAAI,CAACG,KAAK,CAACC,QAAQ,GAAGJ,IAAI,CAACI,QAAQ;EACjE,OAAOJ,IAAI,CAACG,KAAK,IAAIH,IAAI,CAACG,KAAK,CAACI,iBAAiB,GAAGC,UAAU,CAACJ,QAAQ,CAAC,GAAGA,QAAQ;AACrF,CAAC;AACD,MAAMK,qBAAqB,GAAGL,QAAQ,IAAIM,KAAK,CAACC,OAAO,CAACP,QAAQ,CAAC,IAAIA,QAAQ,CAACQ,KAAK,CAACxB,cAAc,CAAC;AACnG,MAAMoB,UAAU,GAAGK,IAAI,IAAIH,KAAK,CAACC,OAAO,CAACE,IAAI,CAAC,GAAGA,IAAI,GAAG,CAACA,IAAI,CAAC;AAC9D,MAAMC,UAAU,GAAGA,CAACC,MAAM,EAAEC,MAAM,KAAK;EACrC,MAAMC,SAAS,GAAG;IAChB,GAAGD;EACL,CAAC;EACDC,SAAS,CAACd,KAAK,GAAGe,MAAM,CAACC,MAAM,CAACJ,MAAM,CAACZ,KAAK,EAAEa,MAAM,CAACb,KAAK,CAAC;EAC3D,OAAOc,SAAS;AAClB,CAAC;AACD,OAAO,MAAMG,aAAa,GAAGA,CAAChB,QAAQ,EAAEiB,WAAW,KAAK;EACtD,IAAI,CAACjB,QAAQ,EAAE,OAAO,EAAE;EACxB,IAAIkB,UAAU,GAAG,EAAE;EACnB,MAAMC,aAAa,GAAGf,UAAU,CAACJ,QAAQ,CAAC;EAC1C,MAAMoB,SAAS,GAAGH,WAAW,CAACI,0BAA0B,IAAIJ,WAAW,CAACK,0BAA0B,GAAGL,WAAW,CAACK,0BAA0B,GAAG,EAAE;EAChJH,aAAa,CAACI,OAAO,CAAC,CAACC,KAAK,EAAEC,UAAU,KAAK;IAC3C,IAAInC,QAAQ,CAACkC,KAAK,CAAC,EAAE;MACnBN,UAAU,IAAI,GAAGM,KAAK,EAAE;IAC1B,CAAC,MAAM,IAAIxC,cAAc,CAACwC,KAAK,CAAC,EAAE;MAChC,MAAM;QACJzB,KAAK;QACL2B;MACF,CAAC,GAAGF,KAAK;MACT,MAAMG,eAAe,GAAGb,MAAM,CAACc,IAAI,CAAC7B,KAAK,CAAC,CAACE,MAAM;MACjD,MAAM4B,eAAe,GAAGT,SAAS,CAACU,OAAO,CAACJ,IAAI,CAAC,GAAG,CAAC,CAAC;MACpD,MAAMK,aAAa,GAAGhC,KAAK,CAACC,QAAQ;MACpC,IAAI,CAAC+B,aAAa,IAAIF,eAAe,IAAI,CAACF,eAAe,EAAE;QACzDT,UAAU,IAAI,IAAIQ,IAAI,IAAI;MAC5B,CAAC,MAAM,IAAI,CAACK,aAAa,KAAK,CAACF,eAAe,IAAIF,eAAe,CAAC,IAAI5B,KAAK,CAACI,iBAAiB,EAAE;QAC7Fe,UAAU,IAAI,IAAIO,UAAU,MAAMA,UAAU,GAAG;MACjD,CAAC,MAAM,IAAII,eAAe,IAAIF,eAAe,KAAK,CAAC,IAAIrC,QAAQ,CAACyC,aAAa,CAAC,EAAE;QAC9Eb,UAAU,IAAI,IAAIQ,IAAI,IAAIK,aAAa,KAAKL,IAAI,GAAG;MACrD,CAAC,MAAM;QACL,MAAMM,OAAO,GAAGhB,aAAa,CAACe,aAAa,EAAEd,WAAW,CAAC;QACzDC,UAAU,IAAI,IAAIO,UAAU,IAAIO,OAAO,KAAKP,UAAU,GAAG;MAC3D;IACF,CAAC,MAAM,IAAID,KAAK,KAAK,IAAI,EAAE;MACzBjC,IAAI,CAAC,2EAA2E,CAAC;IACnF,CAAC,MAAM,IAAIF,QAAQ,CAACmC,KAAK,CAAC,EAAE;MAC1B,MAAM;QACJS,MAAM;QACN,GAAGC;MACL,CAAC,GAAGV,KAAK;MACT,MAAMI,IAAI,GAAGd,MAAM,CAACc,IAAI,CAACM,KAAK,CAAC;MAC/B,IAAIN,IAAI,CAAC3B,MAAM,KAAK,CAAC,EAAE;QACrB,MAAMkC,KAAK,GAAGF,MAAM,GAAG,GAAGL,IAAI,CAAC,CAAC,CAAC,KAAKK,MAAM,EAAE,GAAGL,IAAI,CAAC,CAAC,CAAC;QACxDV,UAAU,IAAI,KAAKiB,KAAK,IAAI;MAC9B,CAAC,MAAM;QACL5C,IAAI,CAAC,kJAAkJ,EAAEiC,KAAK,CAAC;MACjK;IACF,CAAC,MAAM;MACLjC,IAAI,CAAC,oKAAoK,EAAEiC,KAAK,CAAC;IACnL;EACF,CAAC,CAAC;EACF,OAAON,UAAU;AACnB,CAAC;AACD,MAAMkB,WAAW,GAAGA,CAACpC,QAAQ,EAAEqC,YAAY,EAAEC,IAAI,EAAErB,WAAW,EAAEsB,aAAa,EAAEC,cAAc,KAAK;EAChG,IAAIH,YAAY,KAAK,EAAE,EAAE,OAAO,EAAE;EAClC,MAAMjB,SAAS,GAAGH,WAAW,CAACK,0BAA0B,IAAI,EAAE;EAC9D,MAAMmB,6BAA6B,GAAGJ,YAAY,IAAI,IAAIK,MAAM,CAACtB,SAAS,CAACuB,GAAG,CAACC,IAAI,IAAI,IAAIA,IAAI,EAAE,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC,CAACC,IAAI,CAACT,YAAY,CAAC;EAChI,IAAI,CAACrC,QAAQ,IAAI,CAACyC,6BAA6B,IAAI,CAACD,cAAc,EAAE,OAAO,CAACH,YAAY,CAAC;EACzF,MAAM5B,IAAI,GAAG,CAAC,CAAC;EACf,MAAMsC,OAAO,GAAGC,MAAM,IAAI;IACxB,MAAM7B,aAAa,GAAGf,UAAU,CAAC4C,MAAM,CAAC;IACxC7B,aAAa,CAACI,OAAO,CAACC,KAAK,IAAI;MAC7B,IAAIlC,QAAQ,CAACkC,KAAK,CAAC,EAAE;MACrB,IAAI7B,WAAW,CAAC6B,KAAK,CAAC,EAAEuB,OAAO,CAAC7C,WAAW,CAACsB,KAAK,CAAC,CAAC,CAAC,KAAK,IAAInC,QAAQ,CAACmC,KAAK,CAAC,IAAI,CAACxC,cAAc,CAACwC,KAAK,CAAC,EAAEV,MAAM,CAACC,MAAM,CAACN,IAAI,EAAEe,KAAK,CAAC;IACpI,CAAC,CAAC;EACJ,CAAC;EACDuB,OAAO,CAAC/C,QAAQ,CAAC;EACjB,MAAMiD,GAAG,GAAG7D,IAAI,CAAC8D,KAAK,CAAC,MAAMb,YAAY,MAAM,CAAC;EAChD,MAAMc,IAAI,GAAG;IACX,GAAG1C,IAAI;IACP,GAAG8B;EACL,CAAC;EACD,MAAMa,WAAW,GAAGA,CAAC5B,KAAK,EAAE5B,IAAI,EAAEyD,aAAa,KAAK;IAClD,MAAML,MAAM,GAAG9C,WAAW,CAACsB,KAAK,CAAC;IACjC,MAAM8B,cAAc,GAAGC,MAAM,CAACP,MAAM,EAAEpD,IAAI,CAACI,QAAQ,EAAEqD,aAAa,CAAC;IACnE,OAAOhD,qBAAqB,CAAC2C,MAAM,CAAC,IAAIM,cAAc,CAACrD,MAAM,KAAK,CAAC,IAAIuB,KAAK,CAACzB,KAAK,IAAIyB,KAAK,CAACzB,KAAK,CAACI,iBAAiB,GAAG6C,MAAM,GAAGM,cAAc;EAC/I,CAAC;EACD,MAAME,iBAAiB,GAAGA,CAAChC,KAAK,EAAEiC,KAAK,EAAEC,GAAG,EAAEC,CAAC,EAAEC,MAAM,KAAK;IAC1D,IAAIpC,KAAK,CAACqC,KAAK,EAAE;MACfrC,KAAK,CAACxB,QAAQ,GAAGyD,KAAK;MACtBC,GAAG,CAACI,IAAI,CAAC7E,YAAY,CAACuC,KAAK,EAAE;QAC3BuC,GAAG,EAAEJ;MACP,CAAC,EAAEC,MAAM,GAAGI,SAAS,GAAGP,KAAK,CAAC,CAAC;IACjC,CAAC,MAAM;MACLC,GAAG,CAACI,IAAI,CAAC,GAAG3E,QAAQ,CAACwD,GAAG,CAAC,CAACnB,KAAK,CAAC,EAAEyC,CAAC,IAAI;QACrC,MAAMlE,KAAK,GAAG;UACZ,GAAGkE,CAAC,CAAClE;QACP,CAAC;QACD,OAAOA,KAAK,CAACI,iBAAiB;QAC9B,OAAOjB,aAAa,CAAC+E,CAAC,CAACvC,IAAI,EAAE;UAC3B,GAAG3B,KAAK;UACRgE,GAAG,EAAEJ,CAAC;UACNO,GAAG,EAAED,CAAC,CAACC;QACT,CAAC,EAAEN,MAAM,GAAG,IAAI,GAAGH,KAAK,CAAC;MAC3B,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EACD,MAAMF,MAAM,GAAGA,CAACY,SAAS,EAAEC,OAAO,EAAEf,aAAa,KAAK;IACpD,MAAMgB,UAAU,GAAGjE,UAAU,CAAC+D,SAAS,CAAC;IACxC,MAAMG,QAAQ,GAAGlE,UAAU,CAACgE,OAAO,CAAC;IACpC,OAAOE,QAAQ,CAACC,MAAM,CAAC,CAACb,GAAG,EAAE9D,IAAI,EAAE+D,CAAC,KAAK;MACvC,MAAMa,kBAAkB,GAAG5E,IAAI,CAACI,QAAQ,IAAIJ,IAAI,CAACI,QAAQ,CAAC,CAAC,CAAC,IAAIJ,IAAI,CAACI,QAAQ,CAAC,CAAC,CAAC,CAACgC,OAAO,IAAIM,IAAI,CAACmC,QAAQ,CAACC,YAAY,CAACC,WAAW,CAAC/E,IAAI,CAACI,QAAQ,CAAC,CAAC,CAAC,CAACgC,OAAO,EAAEmB,IAAI,EAAEb,IAAI,CAACsC,QAAQ,CAAC;MACjL,IAAIhF,IAAI,CAAC8B,IAAI,KAAK,KAAK,EAAE;QACvB,IAAImD,GAAG,GAAGR,UAAU,CAACS,QAAQ,CAAClF,IAAI,CAACmF,IAAI,EAAE,EAAE,CAAC,CAAC;QAC7C,IAAI1B,aAAa,CAACpD,MAAM,KAAK,CAAC,IAAI,CAAC4E,GAAG,EAAEA,GAAG,GAAGxB,aAAa,CAAC,CAAC,CAAC,CAACzD,IAAI,CAACmF,IAAI,CAAC;QACzE,IAAI,CAACF,GAAG,EAAEA,GAAG,GAAG,CAAC,CAAC;QAClB,MAAMrD,KAAK,GAAGV,MAAM,CAACc,IAAI,CAAChC,IAAI,CAACoF,KAAK,CAAC,CAAC/E,MAAM,KAAK,CAAC,GAAGS,UAAU,CAAC;UAC9DX,KAAK,EAAEH,IAAI,CAACoF;QACd,CAAC,EAAEH,GAAG,CAAC,GAAGA,GAAG;QACb,MAAMI,SAAS,GAAGjG,cAAc,CAACwC,KAAK,CAAC;QACvC,MAAM0D,8BAA8B,GAAGD,SAAS,IAAItF,WAAW,CAACC,IAAI,EAAE,IAAI,CAAC,IAAI,CAACA,IAAI,CAACuF,WAAW;QAChG,MAAMC,oBAAoB,GAAG3C,6BAA6B,IAAIpD,QAAQ,CAACmC,KAAK,CAAC,IAAIA,KAAK,CAACqC,KAAK,IAAI,CAACoB,SAAS;QAC1G,MAAMI,gBAAgB,GAAGhG,QAAQ,CAACW,QAAQ,CAAC,IAAIc,MAAM,CAACwE,cAAc,CAACC,IAAI,CAACvF,QAAQ,EAAEJ,IAAI,CAACmF,IAAI,CAAC;QAC9F,IAAIzF,QAAQ,CAACkC,KAAK,CAAC,EAAE;UACnB,MAAMW,KAAK,GAAGG,IAAI,CAACmC,QAAQ,CAACC,YAAY,CAACC,WAAW,CAACnD,KAAK,EAAE2B,IAAI,EAAEb,IAAI,CAACsC,QAAQ,CAAC;UAChFlB,GAAG,CAACI,IAAI,CAAC3B,KAAK,CAAC;QACjB,CAAC,MAAM,IAAIxC,WAAW,CAAC6B,KAAK,CAAC,IAAI0D,8BAA8B,EAAE;UAC/D,MAAMzB,KAAK,GAAGL,WAAW,CAAC5B,KAAK,EAAE5B,IAAI,EAAEyD,aAAa,CAAC;UACrDG,iBAAiB,CAAChC,KAAK,EAAEiC,KAAK,EAAEC,GAAG,EAAEC,CAAC,CAAC;QACzC,CAAC,MAAM,IAAIyB,oBAAoB,EAAE;UAC/B,MAAM3B,KAAK,GAAGF,MAAM,CAACc,UAAU,EAAEzE,IAAI,CAACI,QAAQ,EAAEqD,aAAa,CAAC;UAC9DG,iBAAiB,CAAChC,KAAK,EAAEiC,KAAK,EAAEC,GAAG,EAAEC,CAAC,CAAC;QACzC,CAAC,MAAM,IAAI6B,MAAM,CAACC,KAAK,CAACC,UAAU,CAAC9F,IAAI,CAACmF,IAAI,CAAC,CAAC,EAAE;UAC9C,IAAIM,gBAAgB,EAAE;YACpB,MAAM5B,KAAK,GAAGL,WAAW,CAAC5B,KAAK,EAAE5B,IAAI,EAAEyD,aAAa,CAAC;YACrDG,iBAAiB,CAAChC,KAAK,EAAEiC,KAAK,EAAEC,GAAG,EAAEC,CAAC,EAAE/D,IAAI,CAACuF,WAAW,CAAC;UAC3D,CAAC,MAAM,IAAIlE,WAAW,CAACI,0BAA0B,IAAID,SAAS,CAACU,OAAO,CAAClC,IAAI,CAACmF,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE;YACtF,IAAInF,IAAI,CAACuF,WAAW,EAAE;cACpBzB,GAAG,CAACI,IAAI,CAAC5E,aAAa,CAACU,IAAI,CAACmF,IAAI,EAAE;gBAChChB,GAAG,EAAE,GAAGnE,IAAI,CAACmF,IAAI,IAAIpB,CAAC;cACxB,CAAC,CAAC,CAAC;YACL,CAAC,MAAM;cACL,MAAMF,KAAK,GAAGF,MAAM,CAACc,UAAU,EAAEzE,IAAI,CAACI,QAAQ,EAAEqD,aAAa,CAAC;cAC9DK,GAAG,CAACI,IAAI,CAAC5E,aAAa,CAACU,IAAI,CAACmF,IAAI,EAAE;gBAChChB,GAAG,EAAE,GAAGnE,IAAI,CAACmF,IAAI,IAAIpB,CAAC;cACxB,CAAC,EAAEF,KAAK,CAAC,CAAC;YACZ;UACF,CAAC,MAAM,IAAI7D,IAAI,CAACuF,WAAW,EAAE;YAC3BzB,GAAG,CAACI,IAAI,CAAC,IAAIlE,IAAI,CAACmF,IAAI,KAAK,CAAC;UAC9B,CAAC,MAAM;YACL,MAAMtB,KAAK,GAAGF,MAAM,CAACc,UAAU,EAAEzE,IAAI,CAACI,QAAQ,EAAEqD,aAAa,CAAC;YAC9DK,GAAG,CAACI,IAAI,CAAC,IAAIlE,IAAI,CAACmF,IAAI,IAAItB,KAAK,KAAK7D,IAAI,CAACmF,IAAI,GAAG,CAAC;UACnD;QACF,CAAC,MAAM,IAAI1F,QAAQ,CAACmC,KAAK,CAAC,IAAI,CAACyD,SAAS,EAAE;UACxC,MAAMjD,OAAO,GAAGpC,IAAI,CAACI,QAAQ,CAAC,CAAC,CAAC,GAAGwE,kBAAkB,GAAG,IAAI;UAC5D,IAAIxC,OAAO,EAAE0B,GAAG,CAACI,IAAI,CAAC9B,OAAO,CAAC;QAChC,CAAC,MAAM;UACLwB,iBAAiB,CAAChC,KAAK,EAAEgD,kBAAkB,EAAEd,GAAG,EAAEC,CAAC,EAAE/D,IAAI,CAACI,QAAQ,CAACC,MAAM,KAAK,CAAC,IAAI,CAACuE,kBAAkB,CAAC;QACzG;MACF,CAAC,MAAM,IAAI5E,IAAI,CAAC8B,IAAI,KAAK,MAAM,EAAE;QAC/B,MAAMiE,aAAa,GAAG1E,WAAW,CAAC2E,kBAAkB;QACpD,MAAM5D,OAAO,GAAGQ,cAAc,GAAGvB,WAAW,CAAC4E,QAAQ,CAACvD,IAAI,CAACmC,QAAQ,CAACC,YAAY,CAACC,WAAW,CAAC/E,IAAI,CAACoC,OAAO,EAAEmB,IAAI,EAAEb,IAAI,CAACsC,QAAQ,CAAC,CAAC,GAAGtC,IAAI,CAACmC,QAAQ,CAACC,YAAY,CAACC,WAAW,CAAC/E,IAAI,CAACoC,OAAO,EAAEmB,IAAI,EAAEb,IAAI,CAACsC,QAAQ,CAAC;QAC5M,IAAIe,aAAa,EAAE;UACjBjC,GAAG,CAACI,IAAI,CAAC5E,aAAa,CAACyG,aAAa,EAAE;YACpC5B,GAAG,EAAE,GAAGnE,IAAI,CAACmF,IAAI,IAAIpB,CAAC;UACxB,CAAC,EAAE3B,OAAO,CAAC,CAAC;QACd,CAAC,MAAM;UACL0B,GAAG,CAACI,IAAI,CAAC9B,OAAO,CAAC;QACnB;MACF;MACA,OAAO0B,GAAG;IACZ,CAAC,EAAE,EAAE,CAAC;EACR,CAAC;EACD,MAAMoC,MAAM,GAAGvC,MAAM,CAAC,CAAC;IACrBM,KAAK,EAAE,IAAI;IACX7D,QAAQ,EAAEA,QAAQ,IAAI;EACxB,CAAC,CAAC,EAAEiD,GAAG,EAAE7C,UAAU,CAACJ,QAAQ,IAAI,EAAE,CAAC,CAAC;EACpC,OAAOE,WAAW,CAAC4F,MAAM,CAAC,CAAC,CAAC,CAAC;AAC/B,CAAC;AACD,OAAO,SAASC,KAAKA,CAACC,IAAI,EAAE;EAC1B,IAAI;IACFhG,QAAQ;IACRiG,KAAK;IACLC,MAAM;IACNC,OAAO;IACPC,OAAO;IACPC,QAAQ,GAAG,CAAC,CAAC;IACbC,MAAM;IACNC,QAAQ;IACRC,UAAU;IACVC,EAAE;IACFnE,IAAI,EAAEoE,aAAa;IACnBC,CAAC,EAAEC,UAAU;IACbpE,cAAc;IACd,GAAGqE;EACL,CAAC,GAAGb,IAAI;EACR,MAAM1D,IAAI,GAAGoE,aAAa,IAAIhH,OAAO,CAAC,CAAC;EACvC,IAAI,CAAC4C,IAAI,EAAE;IACT9C,QAAQ,CAAC,0EAA0E,CAAC;IACpF,OAAOQ,QAAQ;EACjB;EACA,MAAM2G,CAAC,GAAGC,UAAU,IAAItE,IAAI,CAACqE,CAAC,CAACG,IAAI,CAACxE,IAAI,CAAC,KAAKyE,CAAC,IAAIA,CAAC,CAAC;EACrD,MAAMC,mBAAmB,GAAG;IAC1B,GAAGvH,WAAW,CAAC,CAAC;IAChB,IAAI6C,IAAI,CAAC2E,OAAO,IAAI3E,IAAI,CAAC2E,OAAO,CAACC,KAAK;EACxC,CAAC;EACD,IAAIC,UAAU,GAAGV,EAAE,IAAIE,CAAC,CAACF,EAAE,IAAInE,IAAI,CAAC2E,OAAO,IAAI3E,IAAI,CAAC2E,OAAO,CAACG,SAAS;EACrED,UAAU,GAAG7H,QAAQ,CAAC6H,UAAU,CAAC,GAAG,CAACA,UAAU,CAAC,GAAGA,UAAU,IAAI,CAAC,aAAa,CAAC;EAChF,MAAME,YAAY,GAAGrG,aAAa,CAAChB,QAAQ,EAAEgH,mBAAmB,CAAC;EACjE,MAAMM,YAAY,GAAGf,QAAQ,IAAIc,YAAY,IAAIL,mBAAmB,CAACO,mBAAmB,IAAIpB,OAAO;EACnG,MAAM;IACJqB;EACF,CAAC,GAAGR,mBAAmB;EACvB,MAAMjD,GAAG,GAAGoC,OAAO,KAAKqB,YAAY,GAAGA,YAAY,CAACH,YAAY,IAAIC,YAAY,CAAC,GAAGD,YAAY,IAAIC,YAAY,CAAC;EACjH,IAAIhF,IAAI,CAAC2E,OAAO,IAAI3E,IAAI,CAAC2E,OAAO,CAACQ,aAAa,IAAInF,IAAI,CAAC2E,OAAO,CAACQ,aAAa,CAACC,gBAAgB,EAAE;IAC7FpB,MAAM,GAAGA,MAAM,IAAIxF,MAAM,CAACc,IAAI,CAAC0E,MAAM,CAAC,CAACrG,MAAM,GAAG,CAAC,GAAG;MAClD,GAAGqG,MAAM;MACT,GAAGhE,IAAI,CAAC2E,OAAO,CAACQ,aAAa,CAACC;IAChC,CAAC,GAAG;MACF,GAAGpF,IAAI,CAAC2E,OAAO,CAACQ,aAAa,CAACC;IAChC,CAAC;EACH;EACA,MAAMC,qBAAqB,GAAGrB,MAAM,IAAIL,KAAK,KAAKjC,SAAS,IAAI,CAAChE,QAAQ,GAAGqG,QAAQ,CAACoB,aAAa,GAAG;IAClGA,aAAa,EAAE;MACb,GAAGpB,QAAQ,CAACoB,aAAa;MACzBG,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE;IACV;EACF,CAAC;EACD,MAAMtF,aAAa,GAAG;IACpB,GAAG8D,QAAQ;IACXD,OAAO,EAAEA,OAAO,IAAIC,QAAQ,CAACD,OAAO;IACpCH,KAAK;IACL,GAAGK,MAAM;IACT,GAAGqB,qBAAqB;IACxBL,YAAY;IACZb,EAAE,EAAEU;EACN,CAAC;EACD,MAAMW,WAAW,GAAG/D,GAAG,GAAG4C,CAAC,CAAC5C,GAAG,EAAExB,aAAa,CAAC,GAAG+E,YAAY;EAC9D,IAAId,UAAU,EAAE;IACd1F,MAAM,CAACc,IAAI,CAAC4E,UAAU,CAAC,CAACjF,OAAO,CAAC0C,CAAC,IAAI;MACnC,MAAM8D,IAAI,GAAGvB,UAAU,CAACvC,CAAC,CAAC;MAC1B,IAAI,OAAO8D,IAAI,CAACrG,IAAI,KAAK,UAAU,IAAI,CAACqG,IAAI,CAAChI,KAAK,IAAI,CAACgI,IAAI,CAAChI,KAAK,CAACC,QAAQ,IAAI8H,WAAW,CAAChG,OAAO,CAAC,GAAGmC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI6D,WAAW,CAAChG,OAAO,CAAC,GAAGmC,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;MACvJ,SAAS+D,aAAaA,CAAA,EAAG;QACvB,OAAO9I,aAAa,CAACH,QAAQ,EAAE,IAAI,EAAEgJ,IAAI,CAAC;MAC5C;MACAvB,UAAU,CAACvC,CAAC,CAAC,GAAG/E,aAAa,CAAC8I,aAAa,CAAC;IAC9C,CAAC,CAAC;EACJ;EACA,MAAMhG,OAAO,GAAGI,WAAW,CAACoE,UAAU,IAAIxG,QAAQ,EAAE8H,WAAW,EAAExF,IAAI,EAAE0E,mBAAmB,EAAEzE,aAAa,EAAEC,cAAc,CAAC;EAC1H,MAAMyF,WAAW,GAAG/B,MAAM,KAAKlC,SAAS,GAAGkC,MAAM,GAAGc,mBAAmB,CAACkB,kBAAkB;EAC1F,OAAOD,WAAW,GAAG/I,aAAa,CAAC+I,WAAW,EAAEpB,eAAe,EAAE7E,OAAO,CAAC,GAAGA,OAAO;AACrF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}