{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { Subscription } from '../Subscription';\nvar Action = function (_super) {\n  __extends(Action, _super);\n  function Action(scheduler, work) {\n    return _super.call(this) || this;\n  }\n  Action.prototype.schedule = function (state, delay) {\n    if (delay === void 0) {\n      delay = 0;\n    }\n    return this;\n  };\n  return Action;\n}(Subscription);\nexport { Action };", "map": {"version": 3, "names": ["Subscription", "Action", "_super", "__extends", "scheduler", "work", "call", "prototype", "schedule", "state", "delay"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\scheduler\\Action.ts"], "sourcesContent": ["import { Scheduler } from '../Scheduler';\nimport { Subscription } from '../Subscription';\nimport { SchedulerAction } from '../types';\n\n/**\n * A unit of work to be executed in a `scheduler`. An action is typically\n * created from within a {@link SchedulerLike} and an RxJS user does not need to concern\n * themselves about creating and manipulating an Action.\n *\n * ```ts\n * class Action<T> extends Subscription {\n *   new (scheduler: Scheduler, work: (state?: T) => void);\n *   schedule(state?: T, delay: number = 0): Subscription;\n * }\n * ```\n */\nexport class Action<T> extends Subscription {\n  constructor(scheduler: Scheduler, work: (this: SchedulerAction<T>, state?: T) => void) {\n    super();\n  }\n  /**\n   * Schedules this action on its parent {@link SchedulerLike} for execution. May be passed\n   * some context object, `state`. May happen at some point in the future,\n   * according to the `delay` parameter, if specified.\n   * @param state Some contextual data that the `work` function uses when called by the\n   * Scheduler.\n   * @param delay Time to wait before executing the work, where the time unit is implicit\n   * and defined by the Scheduler.\n   * @return A subscription in order to be able to unsubscribe the scheduled work.\n   */\n  public schedule(state?: T, delay: number = 0): Subscription {\n    return this;\n  }\n}\n"], "mappings": ";AACA,SAASA,YAAY,QAAQ,iBAAiB;AAe9C,IAAAC,MAAA,aAAAC,MAAA;EAA+BC,SAAA,CAAAF,MAAA,EAAAC,MAAA;EAC7B,SAAAD,OAAYG,SAAoB,EAAEC,IAAmD;WACnFH,MAAA,CAAAI,IAAA,MAAO;EACT;EAWOL,MAAA,CAAAM,SAAA,CAAAC,QAAQ,GAAf,UAAgBC,KAAS,EAAEC,KAAiB;IAAjB,IAAAA,KAAA;MAAAA,KAAA,IAAiB;IAAA;IAC1C,OAAO,IAAI;EACb,CAAC;EACH,OAAAT,MAAC;AAAD,CAAC,CAjB8BD,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}