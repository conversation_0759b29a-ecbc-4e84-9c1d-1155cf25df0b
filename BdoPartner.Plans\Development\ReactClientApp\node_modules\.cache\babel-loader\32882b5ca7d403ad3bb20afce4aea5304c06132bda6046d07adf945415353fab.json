{"ast": null, "code": "import { Observable } from '../Observable';\nexport var EMPTY = new Observable(function (subscriber) {\n  return subscriber.complete();\n});\nexport function empty(scheduler) {\n  return scheduler ? emptyScheduled(scheduler) : EMPTY;\n}\nfunction emptyScheduled(scheduler) {\n  return new Observable(function (subscriber) {\n    return scheduler.schedule(function () {\n      return subscriber.complete();\n    });\n  });\n}", "map": {"version": 3, "names": ["Observable", "EMPTY", "subscriber", "complete", "empty", "scheduler", "emptyScheduled", "schedule"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\observable\\empty.ts"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { SchedulerLike } from '../types';\n\n/**\n * A simple Observable that emits no items to the Observer and immediately\n * emits a complete notification.\n *\n * <span class=\"informal\">Just emits 'complete', and nothing else.</span>\n *\n * ![](empty.png)\n *\n * A simple Observable that only emits the complete notification. It can be used\n * for composing with other Observables, such as in a {@link mergeMap}.\n *\n * ## Examples\n *\n * Log complete notification\n *\n * ```ts\n * import { EMPTY } from 'rxjs';\n *\n * EMPTY.subscribe({\n *   next: () => console.log('Next'),\n *   complete: () => console.log('Complete!')\n * });\n *\n * // Outputs\n * // Complete!\n * ```\n *\n * Emit the number 7, then complete\n *\n * ```ts\n * import { EMPTY, startWith } from 'rxjs';\n *\n * const result = EMPTY.pipe(startWith(7));\n * result.subscribe(x => console.log(x));\n *\n * // Outputs\n * // 7\n * ```\n *\n * Map and flatten only odd numbers to the sequence `'a'`, `'b'`, `'c'`\n *\n * ```ts\n * import { interval, mergeMap, of, EMPTY } from 'rxjs';\n *\n * const interval$ = interval(1000);\n * const result = interval$.pipe(\n *   mergeMap(x => x % 2 === 1 ? of('a', 'b', 'c') : EMPTY),\n * );\n * result.subscribe(x => console.log(x));\n *\n * // Results in the following to the console:\n * // x is equal to the count on the interval, e.g. (0, 1, 2, 3, ...)\n * // x will occur every 1000ms\n * // if x % 2 is equal to 1, print a, b, c (each on its own)\n * // if x % 2 is not equal to 1, nothing will be output\n * ```\n *\n * @see {@link Observable}\n * @see {@link NEVER}\n * @see {@link of}\n * @see {@link throwError}\n */\nexport const EMPTY = new Observable<never>((subscriber) => subscriber.complete());\n\n/**\n * @param scheduler A {@link SchedulerLike} to use for scheduling\n * the emission of the complete notification.\n * @deprecated Replaced with the {@link EMPTY} constant or {@link scheduled} (e.g. `scheduled([], scheduler)`). Will be removed in v8.\n */\nexport function empty(scheduler?: SchedulerLike) {\n  return scheduler ? emptyScheduled(scheduler) : EMPTY;\n}\n\nfunction emptyScheduled(scheduler: SchedulerLike) {\n  return new Observable<never>((subscriber) => scheduler.schedule(() => subscriber.complete()));\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAiE1C,OAAO,IAAMC,KAAK,GAAG,IAAID,UAAU,CAAQ,UAACE,UAAU;EAAK,OAAAA,UAAU,CAACC,QAAQ,EAAE;AAArB,CAAqB,CAAC;AAOjF,OAAM,SAAUC,KAAKA,CAACC,SAAyB;EAC7C,OAAOA,SAAS,GAAGC,cAAc,CAACD,SAAS,CAAC,GAAGJ,KAAK;AACtD;AAEA,SAASK,cAAcA,CAACD,SAAwB;EAC9C,OAAO,IAAIL,UAAU,CAAQ,UAACE,UAAU;IAAK,OAAAG,SAAS,CAACE,QAAQ,CAAC;MAAM,OAAAL,UAAU,CAACC,QAAQ,EAAE;IAArB,CAAqB,CAAC;EAA/C,CAA+C,CAAC;AAC/F", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}