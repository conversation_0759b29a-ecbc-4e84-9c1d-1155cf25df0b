{"ast": null, "code": "import { scanInternals } from './scanInternals';\nimport { operate } from '../util/lift';\nexport function reduce(accumulator, seed) {\n  return operate(scanInternals(accumulator, seed, arguments.length >= 2, false, true));\n}", "map": {"version": 3, "names": ["scanInternals", "operate", "reduce", "accumulator", "seed", "arguments", "length"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\reduce.ts"], "sourcesContent": ["import { scanInternals } from './scanInternals';\nimport { OperatorFunction } from '../types';\nimport { operate } from '../util/lift';\n\nexport function reduce<V, A = V>(accumulator: (acc: A | V, value: V, index: number) => A): OperatorFunction<V, V | A>;\nexport function reduce<V, A>(accumulator: (acc: A, value: V, index: number) => A, seed: A): OperatorFunction<V, A>;\nexport function reduce<V, A, S = A>(accumulator: (acc: A | S, value: V, index: number) => A, seed: S): OperatorFunction<V, A>;\n\n/**\n * Applies an accumulator function over the source Observable, and returns the\n * accumulated result when the source completes, given an optional seed value.\n *\n * <span class=\"informal\">Combines together all values emitted on the source,\n * using an accumulator function that knows how to join a new source value into\n * the accumulation from the past.</span>\n *\n * ![](reduce.png)\n *\n * Like\n * [Array.prototype.reduce()](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/reduce),\n * `reduce` applies an `accumulator` function against an accumulation and each\n * value of the source Observable (from the past) to reduce it to a single\n * value, emitted on the output Observable. Note that `reduce` will only emit\n * one value, only when the source Observable completes. It is equivalent to\n * applying operator {@link scan} followed by operator {@link last}.\n *\n * Returns an Observable that applies a specified `accumulator` function to each\n * item emitted by the source Observable. If a `seed` value is specified, then\n * that value will be used as the initial value for the accumulator. If no seed\n * value is specified, the first item of the source is used as the seed.\n *\n * ## Example\n *\n * Count the number of click events that happened in 5 seconds\n *\n * ```ts\n * import { fromEvent, takeUntil, interval, map, reduce } from 'rxjs';\n *\n * const clicksInFiveSeconds = fromEvent(document, 'click')\n *   .pipe(takeUntil(interval(5000)));\n *\n * const ones = clicksInFiveSeconds.pipe(map(() => 1));\n * const seed = 0;\n * const count = ones.pipe(reduce((acc, one) => acc + one, seed));\n *\n * count.subscribe(x => console.log(x));\n * ```\n *\n * @see {@link count}\n * @see {@link expand}\n * @see {@link mergeScan}\n * @see {@link scan}\n *\n * @param accumulator The accumulator function called on each source value.\n * @param seed The initial accumulation value.\n * @return A function that returns an Observable that emits a single value that\n * is the result of accumulating the values emitted by the source Observable.\n */\nexport function reduce<V, A>(accumulator: (acc: V | A, value: V, index: number) => A, seed?: any): OperatorFunction<V, V | A> {\n  return operate(scanInternals(accumulator, seed, arguments.length >= 2, false, true));\n}\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,iBAAiB;AAE/C,SAASC,OAAO,QAAQ,cAAc;AAwDtC,OAAM,SAAUC,MAAMA,CAAOC,WAAuD,EAAEC,IAAU;EAC9F,OAAOH,OAAO,CAACD,aAAa,CAACG,WAAW,EAAEC,IAAI,EAAEC,SAAS,CAACC,MAAM,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;AACtF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}