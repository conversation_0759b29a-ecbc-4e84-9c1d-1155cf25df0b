{"ast": null, "code": "import { isFunction } from '../util/isFunction';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { identity } from '../util/identity';\nexport function tap(observerOrNext, error, complete) {\n  var tapObserver = isFunction(observerOrNext) || error || complete ? {\n    next: observerOrNext,\n    error: error,\n    complete: complete\n  } : observerOrNext;\n  return tapObserver ? operate(function (source, subscriber) {\n    var _a;\n    (_a = tapObserver.subscribe) === null || _a === void 0 ? void 0 : _a.call(tapObserver);\n    var isUnsub = true;\n    source.subscribe(createOperatorSubscriber(subscriber, function (value) {\n      var _a;\n      (_a = tapObserver.next) === null || _a === void 0 ? void 0 : _a.call(tapObserver, value);\n      subscriber.next(value);\n    }, function () {\n      var _a;\n      isUnsub = false;\n      (_a = tapObserver.complete) === null || _a === void 0 ? void 0 : _a.call(tapObserver);\n      subscriber.complete();\n    }, function (err) {\n      var _a;\n      isUnsub = false;\n      (_a = tapObserver.error) === null || _a === void 0 ? void 0 : _a.call(tapObserver, err);\n      subscriber.error(err);\n    }, function () {\n      var _a, _b;\n      if (isUnsub) {\n        (_a = tapObserver.unsubscribe) === null || _a === void 0 ? void 0 : _a.call(tapObserver);\n      }\n      (_b = tapObserver.finalize) === null || _b === void 0 ? void 0 : _b.call(tapObserver);\n    }));\n  }) : identity;\n}", "map": {"version": 3, "names": ["isFunction", "operate", "createOperatorSubscriber", "identity", "tap", "observerOrNext", "error", "complete", "tapObserver", "next", "source", "subscriber", "_a", "subscribe", "call", "isUnsub", "value", "err", "unsubscribe", "_b", "finalize"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\tap.ts"], "sourcesContent": ["import { MonoTypeOperatorFunction, Observer } from '../types';\nimport { isFunction } from '../util/isFunction';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { identity } from '../util/identity';\n\n/**\n * An extension to the {@link Observer} interface used only by the {@link tap} operator.\n *\n * It provides a useful set of callbacks a user can register to do side-effects in\n * cases other than what the usual {@link Observer} callbacks are\n * ({@link guide/glossary-and-semantics#next next},\n * {@link guide/glossary-and-semantics#error error} and/or\n * {@link guide/glossary-and-semantics#complete complete}).\n *\n * ## Example\n *\n * ```ts\n * import { fromEvent, switchMap, tap, interval, take } from 'rxjs';\n *\n * const source$ = fromEvent(document, 'click');\n * const result$ = source$.pipe(\n *   switchMap((_, i) => i % 2 === 0\n *     ? fromEvent(document, 'mousemove').pipe(\n *         tap({\n *           subscribe: () => console.log('Subscribed to the mouse move events after click #' + i),\n *           unsubscribe: () => console.log('Mouse move events #' + i + ' unsubscribed'),\n *           finalize: () => console.log('Mouse move events #' + i + ' finalized')\n *         })\n *       )\n *     : interval(1_000).pipe(\n *         take(5),\n *         tap({\n *           subscribe: () => console.log('Subscribed to the 1-second interval events after click #' + i),\n *           unsubscribe: () => console.log('1-second interval events #' + i + ' unsubscribed'),\n *           finalize: () => console.log('1-second interval events #' + i + ' finalized')\n *         })\n *       )\n *   )\n * );\n *\n * const subscription = result$.subscribe({\n *   next: console.log\n * });\n *\n * setTimeout(() => {\n *   console.log('Unsubscribe after 60 seconds');\n *   subscription.unsubscribe();\n * }, 60_000);\n * ```\n */\nexport interface TapObserver<T> extends Observer<T> {\n  /**\n   * The callback that `tap` operator invokes at the moment when the source Observable\n   * gets subscribed to.\n   */\n  subscribe: () => void;\n  /**\n   * The callback that `tap` operator invokes when an explicit\n   * {@link guide/glossary-and-semantics#unsubscription unsubscribe} happens. It won't get invoked on\n   * `error` or `complete` events.\n   */\n  unsubscribe: () => void;\n  /**\n   * The callback that `tap` operator invokes when any kind of\n   * {@link guide/glossary-and-semantics#finalization finalization} happens - either when\n   * the source Observable `error`s or `complete`s or when it gets explicitly unsubscribed\n   * by the user. There is no difference in using this callback or the {@link finalize}\n   * operator, but if you're already using `tap` operator, you can use this callback\n   * instead. You'd get the same result in either case.\n   */\n  finalize: () => void;\n}\nexport function tap<T>(observerOrNext?: Partial<TapObserver<T>> | ((value: T) => void)): MonoTypeOperatorFunction<T>;\n/** @deprecated Instead of passing separate callback arguments, use an observer argument. Signatures taking separate callback arguments will be removed in v8. Details: https://rxjs.dev/deprecations/subscribe-arguments */\nexport function tap<T>(\n  next?: ((value: T) => void) | null,\n  error?: ((error: any) => void) | null,\n  complete?: (() => void) | null\n): MonoTypeOperatorFunction<T>;\n\n/**\n * Used to perform side-effects for notifications from the source observable\n *\n * <span class=\"informal\">Used when you want to affect outside state with a notification without altering the notification</span>\n *\n * ![](tap.png)\n *\n * Tap is designed to allow the developer a designated place to perform side effects. While you _could_ perform side-effects\n * inside of a `map` or a `mergeMap`, that would make their mapping functions impure, which isn't always a big deal, but will\n * make it so you can't do things like memoize those functions. The `tap` operator is designed solely for such side-effects to\n * help you remove side-effects from other operations.\n *\n * For any notification, next, error, or complete, `tap` will call the appropriate callback you have provided to it, via a function\n * reference, or a partial observer, then pass that notification down the stream.\n *\n * The observable returned by `tap` is an exact mirror of the source, with one exception: Any error that occurs -- synchronously -- in a handler\n * provided to `tap` will be emitted as an error from the returned observable.\n *\n * > Be careful! You can mutate objects as they pass through the `tap` operator's handlers.\n *\n * The most common use of `tap` is actually for debugging. You can place a `tap(console.log)` anywhere\n * in your observable `pipe`, log out the notifications as they are emitted by the source returned by the previous\n * operation.\n *\n * ## Examples\n *\n * Check a random number before it is handled. Below is an observable that will use a random number between 0 and 1,\n * and emit `'big'` or `'small'` depending on the size of that number. But we wanted to log what the original number\n * was, so we have added a `tap(console.log)`.\n *\n * ```ts\n * import { of, tap, map } from 'rxjs';\n *\n * of(Math.random()).pipe(\n *   tap(console.log),\n *   map(n => n > 0.5 ? 'big' : 'small')\n * ).subscribe(console.log);\n * ```\n *\n * Using `tap` to analyze a value and force an error. Below is an observable where in our system we only\n * want to emit numbers 3 or less we get from another source. We can force our observable to error\n * using `tap`.\n *\n * ```ts\n * import { of, tap } from 'rxjs';\n *\n * const source = of(1, 2, 3, 4, 5);\n *\n * source.pipe(\n *   tap(n => {\n *     if (n > 3) {\n *       throw new TypeError(`Value ${ n } is greater than 3`);\n *     }\n *   })\n * )\n * .subscribe({ next: console.log, error: err => console.log(err.message) });\n * ```\n *\n * We want to know when an observable completes before moving on to the next observable. The system\n * below will emit a random series of `'X'` characters from 3 different observables in sequence. The\n * only way we know when one observable completes and moves to the next one, in this case, is because\n * we have added a `tap` with the side effect of logging to console.\n *\n * ```ts\n * import { of, concatMap, interval, take, map, tap } from 'rxjs';\n *\n * of(1, 2, 3).pipe(\n *   concatMap(n => interval(1000).pipe(\n *     take(Math.round(Math.random() * 10)),\n *     map(() => 'X'),\n *     tap({ complete: () => console.log(`Done with ${ n }`) })\n *   ))\n * )\n * .subscribe(console.log);\n * ```\n *\n * @see {@link finalize}\n * @see {@link TapObserver}\n *\n * @param observerOrNext A next handler or partial observer\n * @param error An error handler\n * @param complete A completion handler\n * @return A function that returns an Observable identical to the source, but\n * runs the specified Observer or callback(s) for each item.\n */\nexport function tap<T>(\n  observerOrNext?: Partial<TapObserver<T>> | ((value: T) => void) | null,\n  error?: ((e: any) => void) | null,\n  complete?: (() => void) | null\n): MonoTypeOperatorFunction<T> {\n  // We have to check to see not only if next is a function,\n  // but if error or complete were passed. This is because someone\n  // could technically call tap like `tap(null, fn)` or `tap(null, null, fn)`.\n  const tapObserver =\n    isFunction(observerOrNext) || error || complete\n      ? // tslint:disable-next-line: no-object-literal-type-assertion\n        ({ next: observerOrNext as Exclude<typeof observerOrNext, Partial<TapObserver<T>>>, error, complete } as Partial<TapObserver<T>>)\n      : observerOrNext;\n\n  return tapObserver\n    ? operate((source, subscriber) => {\n        tapObserver.subscribe?.();\n        let isUnsub = true;\n        source.subscribe(\n          createOperatorSubscriber(\n            subscriber,\n            (value) => {\n              tapObserver.next?.(value);\n              subscriber.next(value);\n            },\n            () => {\n              isUnsub = false;\n              tapObserver.complete?.();\n              subscriber.complete();\n            },\n            (err) => {\n              isUnsub = false;\n              tapObserver.error?.(err);\n              subscriber.error(err);\n            },\n            () => {\n              if (isUnsub) {\n                tapObserver.unsubscribe?.();\n              }\n              tapObserver.finalize?.();\n            }\n          )\n        );\n      })\n    : // Tap was called with no valid tap observer or handler\n      // (e.g. `tap(null, null, null)` or `tap(null)` or `tap()`)\n      // so we're going to just mirror the source.\n      identity;\n}\n"], "mappings": "AACA,SAASA,UAAU,QAAQ,oBAAoB;AAC/C,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,SAASC,QAAQ,QAAQ,kBAAkB;AAkK3C,OAAM,SAAUC,GAAGA,CACjBC,cAAsE,EACtEC,KAAiC,EACjCC,QAA8B;EAK9B,IAAMC,WAAW,GACfR,UAAU,CAACK,cAAc,CAAC,IAAIC,KAAK,IAAIC,QAAQ,GAE1C;IAAEE,IAAI,EAAEJ,cAAyE;IAAEC,KAAK,EAAAA,KAAA;IAAEC,QAAQ,EAAAA;EAAA,CAA8B,GACjIF,cAAc;EAEpB,OAAOG,WAAW,GACdP,OAAO,CAAC,UAACS,MAAM,EAAEC,UAAU;;IACzB,CAAAC,EAAA,GAAAJ,WAAW,CAACK,SAAS,cAAAD,EAAA,uBAAAA,EAAA,CAAAE,IAAA,CAArBN,WAAW,CAAc;IACzB,IAAIO,OAAO,GAAG,IAAI;IAClBL,MAAM,CAACG,SAAS,CACdX,wBAAwB,CACtBS,UAAU,EACV,UAACK,KAAK;;MACJ,CAAAJ,EAAA,GAAAJ,WAAW,CAACC,IAAI,cAAAG,EAAA,uBAAAA,EAAA,CAAAE,IAAA,CAAhBN,WAAW,EAAQQ,KAAK,CAAC;MACzBL,UAAU,CAACF,IAAI,CAACO,KAAK,CAAC;IACxB,CAAC,EACD;;MACED,OAAO,GAAG,KAAK;MACf,CAAAH,EAAA,GAAAJ,WAAW,CAACD,QAAQ,cAAAK,EAAA,uBAAAA,EAAA,CAAAE,IAAA,CAApBN,WAAW,CAAa;MACxBG,UAAU,CAACJ,QAAQ,EAAE;IACvB,CAAC,EACD,UAACU,GAAG;;MACFF,OAAO,GAAG,KAAK;MACf,CAAAH,EAAA,GAAAJ,WAAW,CAACF,KAAK,cAAAM,EAAA,uBAAAA,EAAA,CAAAE,IAAA,CAAjBN,WAAW,EAASS,GAAG,CAAC;MACxBN,UAAU,CAACL,KAAK,CAACW,GAAG,CAAC;IACvB,CAAC,EACD;;MACE,IAAIF,OAAO,EAAE;QACX,CAAAH,EAAA,GAAAJ,WAAW,CAACU,WAAW,cAAAN,EAAA,uBAAAA,EAAA,CAAAE,IAAA,CAAvBN,WAAW,CAAgB;;MAE7B,CAAAW,EAAA,GAAAX,WAAW,CAACY,QAAQ,cAAAD,EAAA,uBAAAA,EAAA,CAAAL,IAAA,CAApBN,WAAW,CAAa;IAC1B,CAAC,CACF,CACF;EACH,CAAC,CAAC,GAIFL,QAAQ;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}