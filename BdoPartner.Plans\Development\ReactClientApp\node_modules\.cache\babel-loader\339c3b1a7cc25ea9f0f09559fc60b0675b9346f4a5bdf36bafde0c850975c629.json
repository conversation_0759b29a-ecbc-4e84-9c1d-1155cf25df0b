{"ast": null, "code": "import { __read, __spreadArray } from \"tslib\";\nimport { Immediate } from '../util/Immediate';\nvar setImmediate = Immediate.setImmediate,\n  clearImmediate = Immediate.clearImmediate;\nexport var immediateProvider = {\n  setImmediate: function () {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      args[_i] = arguments[_i];\n    }\n    var delegate = immediateProvider.delegate;\n    return ((delegate === null || delegate === void 0 ? void 0 : delegate.setImmediate) || setImmediate).apply(void 0, __spreadArray([], __read(args)));\n  },\n  clearImmediate: function (handle) {\n    var delegate = immediateProvider.delegate;\n    return ((delegate === null || delegate === void 0 ? void 0 : delegate.clearImmediate) || clearImmediate)(handle);\n  },\n  delegate: undefined\n};", "map": {"version": 3, "names": ["Immediate", "setImmediate", "clearImmediate", "immediate<PERSON>rovider", "args", "_i", "arguments", "length", "delegate", "apply", "__spread<PERSON><PERSON>y", "__read", "handle", "undefined"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\scheduler\\immediateProvider.ts"], "sourcesContent": ["import { Immediate } from '../util/Immediate';\nimport type { TimerHandle } from './timerHandle';\nconst { setImmediate, clearImmediate } = Immediate;\n\ntype SetImmediateFunction = (handler: () => void, ...args: any[]) => TimerHandle;\ntype ClearImmediateFunction = (handle: TimerHandle) => void;\n\ninterface ImmediateProvider {\n  setImmediate: SetImmediateFunction;\n  clearImmediate: ClearImmediateFunction;\n  delegate:\n    | {\n        setImmediate: SetImmediateFunction;\n        clearImmediate: ClearImmediateFunction;\n      }\n    | undefined;\n}\n\nexport const immediateProvider: ImmediateProvider = {\n  // When accessing the delegate, use the variable rather than `this` so that\n  // the functions can be called without being bound to the provider.\n  setImmediate(...args) {\n    const { delegate } = immediateProvider;\n    return (delegate?.setImmediate || setImmediate)(...args);\n  },\n  clearImmediate(handle) {\n    const { delegate } = immediateProvider;\n    return (delegate?.clearImmediate || clearImmediate)(handle as any);\n  },\n  delegate: undefined,\n};\n"], "mappings": ";AAAA,SAASA,SAAS,QAAQ,mBAAmB;AAErC,IAAAC,YAAY,GAAqBD,SAAS,CAAAC,YAA9B;EAAEC,cAAc,GAAKF,SAAS,CAAAE,cAAd;AAgBpC,OAAO,IAAMC,iBAAiB,GAAsB;EAGlDF,YAAY,WAAAA,CAAA;IAAC,IAAAG,IAAA;SAAA,IAAAC,EAAA,IAAO,EAAPA,EAAA,GAAAC,SAAA,CAAAC,MAAO,EAAPF,EAAA,EAAO;MAAPD,IAAA,CAAAC,EAAA,IAAAC,SAAA,CAAAD,EAAA;;IACH,IAAAG,QAAQ,GAAKL,iBAAiB,CAAAK,QAAtB;IAChB,OAAO,CAAC,CAAAA,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEP,YAAY,KAAIA,YAAY,EAACQ,KAAA,SAAAC,aAAA,KAAAC,MAAA,CAAIP,IAAI;EACzD,CAAC;EACDF,cAAc,EAAd,SAAAA,CAAeU,MAAM;IACX,IAAAJ,QAAQ,GAAKL,iBAAiB,CAAAK,QAAtB;IAChB,OAAO,CAAC,CAAAA,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEN,cAAc,KAAIA,cAAc,EAAEU,MAAa,CAAC;EACpE,CAAC;EACDJ,QAAQ,EAAEK;CACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}