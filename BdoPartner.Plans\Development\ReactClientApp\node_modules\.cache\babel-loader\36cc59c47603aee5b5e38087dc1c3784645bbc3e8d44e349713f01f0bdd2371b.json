{"ast": null, "code": "import { __values } from \"tslib\";\nimport { Subscription } from '../Subscription';\nimport { operate } from '../util/lift';\nimport { innerFrom } from '../observable/innerFrom';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { noop } from '../util/noop';\nimport { arrRemove } from '../util/arrRemove';\nexport function bufferToggle(openings, closingSelector) {\n  return operate(function (source, subscriber) {\n    var buffers = [];\n    innerFrom(openings).subscribe(createOperatorSubscriber(subscriber, function (openValue) {\n      var buffer = [];\n      buffers.push(buffer);\n      var closingSubscription = new Subscription();\n      var emitBuffer = function () {\n        arrRemove(buffers, buffer);\n        subscriber.next(buffer);\n        closingSubscription.unsubscribe();\n      };\n      closingSubscription.add(innerFrom(closingSelector(openValue)).subscribe(createOperatorSubscriber(subscriber, emitBuffer, noop)));\n    }, noop));\n    source.subscribe(createOperatorSubscriber(subscriber, function (value) {\n      var e_1, _a;\n      try {\n        for (var buffers_1 = __values(buffers), buffers_1_1 = buffers_1.next(); !buffers_1_1.done; buffers_1_1 = buffers_1.next()) {\n          var buffer = buffers_1_1.value;\n          buffer.push(value);\n        }\n      } catch (e_1_1) {\n        e_1 = {\n          error: e_1_1\n        };\n      } finally {\n        try {\n          if (buffers_1_1 && !buffers_1_1.done && (_a = buffers_1.return)) _a.call(buffers_1);\n        } finally {\n          if (e_1) throw e_1.error;\n        }\n      }\n    }, function () {\n      while (buffers.length > 0) {\n        subscriber.next(buffers.shift());\n      }\n      subscriber.complete();\n    }));\n  });\n}", "map": {"version": 3, "names": ["Subscription", "operate", "innerFrom", "createOperatorSubscriber", "noop", "arr<PERSON><PERSON><PERSON>", "bufferToggle", "openings", "closingSelector", "source", "subscriber", "buffers", "subscribe", "openValue", "buffer", "push", "closingSubscription", "emitB<PERSON>er", "next", "unsubscribe", "add", "value", "buffers_1", "__values", "buffers_1_1", "done", "length", "shift", "complete"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\bufferToggle.ts"], "sourcesContent": ["import { Subscription } from '../Subscription';\nimport { OperatorFunction, ObservableInput } from '../types';\nimport { operate } from '../util/lift';\nimport { innerFrom } from '../observable/innerFrom';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { noop } from '../util/noop';\nimport { arrRemove } from '../util/arrRemove';\n\n/**\n * Buffers the source Observable values starting from an emission from\n * `openings` and ending when the output of `closingSelector` emits.\n *\n * <span class=\"informal\">Collects values from the past as an array. Starts\n * collecting only when `opening` emits, and calls the `closingSelector`\n * function to get an Observable that tells when to close the buffer.</span>\n *\n * ![](bufferToggle.png)\n *\n * Buffers values from the source by opening the buffer via signals from an\n * Observable provided to `openings`, and closing and sending the buffers when\n * a Subscribable or Promise returned by the `closingSelector` function emits.\n *\n * ## Example\n *\n * Every other second, emit the click events from the next 500ms\n *\n * ```ts\n * import { fromEvent, interval, bufferToggle, EMPTY } from 'rxjs';\n *\n * const clicks = fromEvent(document, 'click');\n * const openings = interval(1000);\n * const buffered = clicks.pipe(bufferToggle(openings, i =>\n *   i % 2 ? interval(500) : EMPTY\n * ));\n * buffered.subscribe(x => console.log(x));\n * ```\n *\n * @see {@link buffer}\n * @see {@link bufferCount}\n * @see {@link bufferTime}\n * @see {@link bufferWhen}\n * @see {@link windowToggle}\n *\n * @param openings A Subscribable or Promise of notifications to start new\n * buffers.\n * @param closingSelector A function that takes\n * the value emitted by the `openings` observable and returns a Subscribable or Promise,\n * which, when it emits, signals that the associated buffer should be emitted\n * and cleared.\n * @return A function that returns an Observable of arrays of buffered values.\n */\nexport function bufferToggle<T, O>(\n  openings: ObservableInput<O>,\n  closingSelector: (value: O) => ObservableInput<any>\n): OperatorFunction<T, T[]> {\n  return operate((source, subscriber) => {\n    const buffers: T[][] = [];\n\n    // Subscribe to the openings notifier first\n    innerFrom(openings).subscribe(\n      createOperatorSubscriber(\n        subscriber,\n        (openValue) => {\n          const buffer: T[] = [];\n          buffers.push(buffer);\n          // We use this composite subscription, so that\n          // when the closing notifier emits, we can tear it down.\n          const closingSubscription = new Subscription();\n\n          const emitBuffer = () => {\n            arrRemove(buffers, buffer);\n            subscriber.next(buffer);\n            closingSubscription.unsubscribe();\n          };\n\n          // The line below will add the subscription to the parent subscriber *and* the closing subscription.\n          closingSubscription.add(innerFrom(closingSelector(openValue)).subscribe(createOperatorSubscriber(subscriber, emitBuffer, noop)));\n        },\n        noop\n      )\n    );\n\n    source.subscribe(\n      createOperatorSubscriber(\n        subscriber,\n        (value) => {\n          // Value from our source. Add it to all pending buffers.\n          for (const buffer of buffers) {\n            buffer.push(value);\n          }\n        },\n        () => {\n          // Source complete. Emit all pending buffers.\n          while (buffers.length > 0) {\n            subscriber.next(buffers.shift()!);\n          }\n          subscriber.complete();\n        }\n      )\n    );\n  });\n}\n"], "mappings": ";AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,SAAS,QAAQ,yBAAyB;AACnD,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,SAASC,IAAI,QAAQ,cAAc;AACnC,SAASC,SAAS,QAAQ,mBAAmB;AA6C7C,OAAM,SAAUC,YAAYA,CAC1BC,QAA4B,EAC5BC,eAAmD;EAEnD,OAAOP,OAAO,CAAC,UAACQ,MAAM,EAAEC,UAAU;IAChC,IAAMC,OAAO,GAAU,EAAE;IAGzBT,SAAS,CAACK,QAAQ,CAAC,CAACK,SAAS,CAC3BT,wBAAwB,CACtBO,UAAU,EACV,UAACG,SAAS;MACR,IAAMC,MAAM,GAAQ,EAAE;MACtBH,OAAO,CAACI,IAAI,CAACD,MAAM,CAAC;MAGpB,IAAME,mBAAmB,GAAG,IAAIhB,YAAY,EAAE;MAE9C,IAAMiB,UAAU,GAAG,SAAAA,CAAA;QACjBZ,SAAS,CAACM,OAAO,EAAEG,MAAM,CAAC;QAC1BJ,UAAU,CAACQ,IAAI,CAACJ,MAAM,CAAC;QACvBE,mBAAmB,CAACG,WAAW,EAAE;MACnC,CAAC;MAGDH,mBAAmB,CAACI,GAAG,CAAClB,SAAS,CAACM,eAAe,CAACK,SAAS,CAAC,CAAC,CAACD,SAAS,CAACT,wBAAwB,CAACO,UAAU,EAAEO,UAAU,EAAEb,IAAI,CAAC,CAAC,CAAC;IAClI,CAAC,EACDA,IAAI,CACL,CACF;IAEDK,MAAM,CAACG,SAAS,CACdT,wBAAwB,CACtBO,UAAU,EACV,UAACW,KAAK;;;QAEJ,KAAqB,IAAAC,SAAA,GAAAC,QAAA,CAAAZ,OAAO,GAAAa,WAAA,GAAAF,SAAA,CAAAJ,IAAA,KAAAM,WAAA,CAAAC,IAAA,EAAAD,WAAA,GAAAF,SAAA,CAAAJ,IAAA,IAAE;UAAzB,IAAMJ,MAAM,GAAAU,WAAA,CAAAH,KAAA;UACfP,MAAM,CAACC,IAAI,CAACM,KAAK,CAAC;;;;;;;;;;;;;IAEtB,CAAC,EACD;MAEE,OAAOV,OAAO,CAACe,MAAM,GAAG,CAAC,EAAE;QACzBhB,UAAU,CAACQ,IAAI,CAACP,OAAO,CAACgB,KAAK,EAAG,CAAC;;MAEnCjB,UAAU,CAACkB,QAAQ,EAAE;IACvB,CAAC,CACF,CACF;EACH,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}