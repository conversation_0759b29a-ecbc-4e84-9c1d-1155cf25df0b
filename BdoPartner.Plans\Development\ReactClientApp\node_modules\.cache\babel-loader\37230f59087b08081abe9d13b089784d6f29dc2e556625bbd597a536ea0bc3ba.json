{"ast": null, "code": "var _jsxFileName = \"C:\\\\git\\\\Partner-Site\\\\BdoPartner.Plans\\\\Development\\\\ReactClientApp\\\\src\\\\pages\\\\partnerPlan.jsx\",\n  _s = $RefreshSig$();\nimport React from \"react\";\nimport { useSearchParams, useNavigate } from \"react-router-dom\";\nimport { Button } from 'primereact/button';\nimport { PartnerPlanQuestionnaire } from \"../components/questionnaire/PartnerPlanQuestionnaire\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const PartnerPlan = () => {\n  _s();\n  const [searchParams] = useSearchParams();\n  const navigate = useNavigate();\n  const formId = searchParams.get('formId');\n\n  // Back handler for the shared component\n  const handleBack = () => {\n    navigate(-1); // Go back to previous page\n  };\n\n  // Validate formId is provided\n  if (!formId) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error-container\",\n      style: {\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        height: '50vh',\n        flexDirection: 'column'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"pi pi-exclamation-triangle\",\n        style: {\n          fontSize: '2rem',\n          color: '#e74c3c'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Error\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Form ID is required to view partner plan\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        icon: \"pi pi-arrow-left\",\n        label: \"Go Back\",\n        className: \"p-button-outlined\",\n        onClick: handleBack,\n        style: {\n          marginTop: '1rem'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Use the shared PartnerPlanQuestionnaire component with formId for review scenario\n  return /*#__PURE__*/_jsxDEV(PartnerPlanQuestionnaire, {\n    formId: formId,\n    backHandler: handleBack\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 41,\n    columnNumber: 10\n  }, this);\n};\n_s(PartnerPlan, \"4Ej3WJsIE3wmZONCXCgcikfvxmM=\", false, function () {\n  return [useSearchParams, useNavigate];\n});\n_c = PartnerPlan;\nexport default PartnerPlan;\nvar _c;\n$RefreshReg$(_c, \"PartnerPlan\");", "map": {"version": 3, "names": ["React", "useSearchParams", "useNavigate", "<PERSON><PERSON>", "PartnerPlanQuestionnaire", "jsxDEV", "_jsxDEV", "PartnerPlan", "_s", "searchParams", "navigate", "formId", "get", "handleBack", "className", "style", "display", "justifyContent", "alignItems", "height", "flexDirection", "children", "fontSize", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "icon", "label", "onClick", "marginTop", "<PERSON><PERSON><PERSON><PERSON>", "_c", "$RefreshReg$"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/pages/partnerPlan.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { useSearchParams, useNavigate } from \"react-router-dom\";\r\nimport { Button } from 'primereact/button';\r\nimport { PartnerPlanQuestionnaire } from \"../components/questionnaire/PartnerPlanQuestionnaire\";\r\n\r\nexport const PartnerPlan = () => {\r\n  const [searchParams] = useSearchParams();\r\n  const navigate = useNavigate();\r\n  const formId = searchParams.get('formId');\r\n\r\n  // Back handler for the shared component\r\n  const handleBack = () => {\r\n    navigate(-1); // Go back to previous page\r\n  };\r\n\r\n  // Validate formId is provided\r\n  if (!formId) {\r\n    return (\r\n      <div className=\"error-container\" style={{\r\n        display: 'flex',\r\n        justifyContent: 'center',\r\n        alignItems: 'center',\r\n        height: '50vh',\r\n        flexDirection: 'column'\r\n      }}>\r\n        <i className=\"pi pi-exclamation-triangle\" style={{ fontSize: '2rem', color: '#e74c3c' }}></i>\r\n        <h3>Error</h3>\r\n        <p>Form ID is required to view partner plan</p>\r\n        <Button\r\n          icon=\"pi pi-arrow-left\"\r\n          label=\"Go Back\"\r\n          className=\"p-button-outlined\"\r\n          onClick={handleBack}\r\n          style={{ marginTop: '1rem' }}\r\n        />\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Use the shared PartnerPlanQuestionnaire component with formId for review scenario\r\n  return <PartnerPlanQuestionnaire formId={formId} backHandler={handleBack} />;\r\n};\r\n\r\nexport default PartnerPlan;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,eAAe,EAAEC,WAAW,QAAQ,kBAAkB;AAC/D,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,wBAAwB,QAAQ,sDAAsD;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhG,OAAO,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM,CAACC,YAAY,CAAC,GAAGR,eAAe,CAAC,CAAC;EACxC,MAAMS,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAMS,MAAM,GAAGF,YAAY,CAACG,GAAG,CAAC,QAAQ,CAAC;;EAEzC;EACA,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvBH,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC;;EAED;EACA,IAAI,CAACC,MAAM,EAAE;IACX,oBACEL,OAAA;MAAKQ,SAAS,EAAC,iBAAiB;MAACC,KAAK,EAAE;QACtCC,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,QAAQ;QACxBC,UAAU,EAAE,QAAQ;QACpBC,MAAM,EAAE,MAAM;QACdC,aAAa,EAAE;MACjB,CAAE;MAAAC,QAAA,gBACAf,OAAA;QAAGQ,SAAS,EAAC,4BAA4B;QAACC,KAAK,EAAE;UAAEO,QAAQ,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAU;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC7FrB,OAAA;QAAAe,QAAA,EAAI;MAAK;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACdrB,OAAA;QAAAe,QAAA,EAAG;MAAwC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAC/CrB,OAAA,CAACH,MAAM;QACLyB,IAAI,EAAC,kBAAkB;QACvBC,KAAK,EAAC,SAAS;QACff,SAAS,EAAC,mBAAmB;QAC7BgB,OAAO,EAAEjB,UAAW;QACpBE,KAAK,EAAE;UAAEgB,SAAS,EAAE;QAAO;MAAE;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV;;EAEA;EACA,oBAAOrB,OAAA,CAACF,wBAAwB;IAACO,MAAM,EAAEA,MAAO;IAACqB,WAAW,EAAEnB;EAAW;IAAAW,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAC9E,CAAC;AAACnB,EAAA,CApCWD,WAAW;EAAA,QACCN,eAAe,EACrBC,WAAW;AAAA;AAAA+B,EAAA,GAFjB1B,WAAW;AAsCxB,eAAeA,WAAW;AAAC,IAAA0B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}