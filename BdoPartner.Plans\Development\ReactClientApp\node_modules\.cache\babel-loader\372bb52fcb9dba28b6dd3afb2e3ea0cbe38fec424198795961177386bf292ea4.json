{"ast": null, "code": "/**\r\n * Date utility functions for formatting and manipulating dates\r\n */\n\n/**\r\n * Format a date string or Date object to a readable date and time format\r\n * @param {string|Date} dateValue - The date value to format\r\n * @param {Object} options - Formatting options\r\n * @param {boolean} options.includeTime - Whether to include time (default: true)\r\n * @param {boolean} options.includeSeconds - Whether to include seconds (default: false)\r\n * @param {string} options.locale - Locale for formatting (default: 'en-US')\r\n * @returns {string} Formatted date string\r\n */\nexport const formatDateTime = (dateValue, options = {}) => {\n  if (!dateValue) return '';\n  const {\n    includeTime = true,\n    includeSeconds = false,\n    locale = 'en-US'\n  } = options;\n  try {\n    const date = typeof dateValue === 'string' ? new Date(dateValue) : dateValue;\n    if (isNaN(date.getTime())) {\n      return 'Invalid Date';\n    }\n    const dateOptions = {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    };\n    if (includeTime) {\n      dateOptions.hour = '2-digit';\n      dateOptions.minute = '2-digit';\n      if (includeSeconds) {\n        dateOptions.second = '2-digit';\n      }\n    }\n    return date.toLocaleDateString(locale, dateOptions);\n  } catch (error) {\n    console.error('Error formatting date:', error);\n    return 'Invalid Date';\n  }\n};\n\n/**\r\n * Format a date string or Date object to a readable date format (no time)\r\n * @param {string|Date} dateValue - The date value to format\r\n * @param {string} locale - Locale for formatting (default: 'en-US')\r\n * @returns {string} Formatted date string\r\n */\nexport const formatDate = (dateValue, locale = 'en-US') => {\n  return formatDateTime(dateValue, {\n    includeTime: false,\n    locale\n  });\n};\n\n/**\r\n * Format a date string or Date object to a readable time format (no date)\r\n * @param {string|Date} dateValue - The date value to format\r\n * @param {boolean} includeSeconds - Whether to include seconds (default: false)\r\n * @param {string} locale - Locale for formatting (default: 'en-US')\r\n * @returns {string} Formatted time string\r\n */\nexport const formatTime = (dateValue, includeSeconds = false, locale = 'en-US') => {\n  if (!dateValue) return '';\n  try {\n    const date = typeof dateValue === 'string' ? new Date(dateValue) : dateValue;\n    if (isNaN(date.getTime())) {\n      return 'Invalid Time';\n    }\n    const timeOptions = {\n      hour: '2-digit',\n      minute: '2-digit'\n    };\n    if (includeSeconds) {\n      timeOptions.second = '2-digit';\n    }\n    return date.toLocaleTimeString(locale, timeOptions);\n  } catch (error) {\n    console.error('Error formatting time:', error);\n    return 'Invalid Time';\n  }\n};\n\n/**\r\n * Get relative time string (e.g., \"2 hours ago\", \"3 days ago\")\r\n * @param {string|Date} dateValue - The date value to compare\r\n * @returns {string} Relative time string\r\n */\nexport const getRelativeTime = dateValue => {\n  if (!dateValue) return '';\n  try {\n    const date = typeof dateValue === 'string' ? new Date(dateValue) : dateValue;\n    const now = new Date();\n    const diffInSeconds = Math.floor((now - date) / 1000);\n    if (diffInSeconds < 60) {\n      return 'Just now';\n    }\n    const diffInMinutes = Math.floor(diffInSeconds / 60);\n    if (diffInMinutes < 60) {\n      return `${diffInMinutes} minute${diffInMinutes !== 1 ? 's' : ''} ago`;\n    }\n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) {\n      return `${diffInHours} hour${diffInHours !== 1 ? 's' : ''} ago`;\n    }\n    const diffInDays = Math.floor(diffInHours / 24);\n    if (diffInDays < 30) {\n      return `${diffInDays} day${diffInDays !== 1 ? 's' : ''} ago`;\n    }\n    const diffInMonths = Math.floor(diffInDays / 30);\n    if (diffInMonths < 12) {\n      return `${diffInMonths} month${diffInMonths !== 1 ? 's' : ''} ago`;\n    }\n    const diffInYears = Math.floor(diffInMonths / 12);\n    return `${diffInYears} year${diffInYears !== 1 ? 's' : ''} ago`;\n  } catch (error) {\n    console.error('Error calculating relative time:', error);\n    return '';\n  }\n};\n\n/**\r\n * Check if a date is today\r\n * @param {string|Date} dateValue - The date value to check\r\n * @returns {boolean} True if the date is today\r\n */\nexport const isToday = dateValue => {\n  if (!dateValue) return false;\n  try {\n    const date = typeof dateValue === 'string' ? new Date(dateValue) : dateValue;\n    const today = new Date();\n    return date.getDate() === today.getDate() && date.getMonth() === today.getMonth() && date.getFullYear() === today.getFullYear();\n  } catch (error) {\n    return false;\n  }\n};\n\n/**\r\n * Check if a date is yesterday\r\n * @param {string|Date} dateValue - The date value to check\r\n * @returns {boolean} True if the date is yesterday\r\n */\nexport const isYesterday = dateValue => {\n  if (!dateValue) return false;\n  try {\n    const date = typeof dateValue === 'string' ? new Date(dateValue) : dateValue;\n    const yesterday = new Date();\n    yesterday.setDate(yesterday.getDate() - 1);\n    return date.getDate() === yesterday.getDate() && date.getMonth() === yesterday.getMonth() && date.getFullYear() === yesterday.getFullYear();\n  } catch (error) {\n    return false;\n  }\n};\n\n/**\r\n * Format date for audit display with relative time if recent\r\n * @param {string|Date} dateValue - The date value to format\r\n * @returns {string} Formatted date string with relative time if applicable\r\n */\nexport const formatAuditDate = dateValue => {\n  if (!dateValue) return 'N/A';\n  try {\n    const date = typeof dateValue === 'string' ? new Date(dateValue) : dateValue;\n    const now = new Date();\n    const diffInHours = Math.floor((now - date) / (1000 * 60 * 60));\n\n    // Show relative time for recent dates (within 24 hours)\n    if (diffInHours < 24) {\n      const relativeTime = getRelativeTime(dateValue);\n      const formattedDate = formatDateTime(dateValue);\n      return `${relativeTime} (${formattedDate})`;\n    }\n\n    // Show formatted date for older dates\n    return formatDateTime(dateValue);\n  } catch (error) {\n    console.error('Error formatting audit date:', error);\n    return 'Invalid Date';\n  }\n};", "map": {"version": 3, "names": ["formatDateTime", "dateValue", "options", "includeTime", "includeSeconds", "locale", "date", "Date", "isNaN", "getTime", "dateOptions", "year", "month", "day", "hour", "minute", "second", "toLocaleDateString", "error", "console", "formatDate", "formatTime", "timeOptions", "toLocaleTimeString", "getRelativeTime", "now", "diffInSeconds", "Math", "floor", "diffInMinutes", "diffInHours", "diffInDays", "diffInMonths", "diffInYears", "isToday", "today", "getDate", "getMonth", "getFullYear", "isYesterday", "yesterday", "setDate", "formatAuditDate", "relativeTime", "formattedDate"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/core/utils/dateUtils.js"], "sourcesContent": ["/**\r\n * Date utility functions for formatting and manipulating dates\r\n */\r\n\r\n/**\r\n * Format a date string or Date object to a readable date and time format\r\n * @param {string|Date} dateValue - The date value to format\r\n * @param {Object} options - Formatting options\r\n * @param {boolean} options.includeTime - Whether to include time (default: true)\r\n * @param {boolean} options.includeSeconds - Whether to include seconds (default: false)\r\n * @param {string} options.locale - Locale for formatting (default: 'en-US')\r\n * @returns {string} Formatted date string\r\n */\r\nexport const formatDateTime = (dateValue, options = {}) => {\r\n  if (!dateValue) return '';\r\n\r\n  const {\r\n    includeTime = true,\r\n    includeSeconds = false,\r\n    locale = 'en-US'\r\n  } = options;\r\n\r\n  try {\r\n    const date = typeof dateValue === 'string' ? new Date(dateValue) : dateValue;\r\n    \r\n    if (isNaN(date.getTime())) {\r\n      return 'Invalid Date';\r\n    }\r\n\r\n    const dateOptions = {\r\n      year: 'numeric',\r\n      month: 'short',\r\n      day: 'numeric'\r\n    };\r\n\r\n    if (includeTime) {\r\n      dateOptions.hour = '2-digit';\r\n      dateOptions.minute = '2-digit';\r\n      \r\n      if (includeSeconds) {\r\n        dateOptions.second = '2-digit';\r\n      }\r\n    }\r\n\r\n    return date.toLocaleDateString(locale, dateOptions);\r\n  } catch (error) {\r\n    console.error('Error formatting date:', error);\r\n    return 'Invalid Date';\r\n  }\r\n};\r\n\r\n/**\r\n * Format a date string or Date object to a readable date format (no time)\r\n * @param {string|Date} dateValue - The date value to format\r\n * @param {string} locale - Locale for formatting (default: 'en-US')\r\n * @returns {string} Formatted date string\r\n */\r\nexport const formatDate = (dateValue, locale = 'en-US') => {\r\n  return formatDateTime(dateValue, { includeTime: false, locale });\r\n};\r\n\r\n/**\r\n * Format a date string or Date object to a readable time format (no date)\r\n * @param {string|Date} dateValue - The date value to format\r\n * @param {boolean} includeSeconds - Whether to include seconds (default: false)\r\n * @param {string} locale - Locale for formatting (default: 'en-US')\r\n * @returns {string} Formatted time string\r\n */\r\nexport const formatTime = (dateValue, includeSeconds = false, locale = 'en-US') => {\r\n  if (!dateValue) return '';\r\n\r\n  try {\r\n    const date = typeof dateValue === 'string' ? new Date(dateValue) : dateValue;\r\n    \r\n    if (isNaN(date.getTime())) {\r\n      return 'Invalid Time';\r\n    }\r\n\r\n    const timeOptions = {\r\n      hour: '2-digit',\r\n      minute: '2-digit'\r\n    };\r\n\r\n    if (includeSeconds) {\r\n      timeOptions.second = '2-digit';\r\n    }\r\n\r\n    return date.toLocaleTimeString(locale, timeOptions);\r\n  } catch (error) {\r\n    console.error('Error formatting time:', error);\r\n    return 'Invalid Time';\r\n  }\r\n};\r\n\r\n/**\r\n * Get relative time string (e.g., \"2 hours ago\", \"3 days ago\")\r\n * @param {string|Date} dateValue - The date value to compare\r\n * @returns {string} Relative time string\r\n */\r\nexport const getRelativeTime = (dateValue) => {\r\n  if (!dateValue) return '';\r\n\r\n  try {\r\n    const date = typeof dateValue === 'string' ? new Date(dateValue) : dateValue;\r\n    const now = new Date();\r\n    const diffInSeconds = Math.floor((now - date) / 1000);\r\n\r\n    if (diffInSeconds < 60) {\r\n      return 'Just now';\r\n    }\r\n\r\n    const diffInMinutes = Math.floor(diffInSeconds / 60);\r\n    if (diffInMinutes < 60) {\r\n      return `${diffInMinutes} minute${diffInMinutes !== 1 ? 's' : ''} ago`;\r\n    }\r\n\r\n    const diffInHours = Math.floor(diffInMinutes / 60);\r\n    if (diffInHours < 24) {\r\n      return `${diffInHours} hour${diffInHours !== 1 ? 's' : ''} ago`;\r\n    }\r\n\r\n    const diffInDays = Math.floor(diffInHours / 24);\r\n    if (diffInDays < 30) {\r\n      return `${diffInDays} day${diffInDays !== 1 ? 's' : ''} ago`;\r\n    }\r\n\r\n    const diffInMonths = Math.floor(diffInDays / 30);\r\n    if (diffInMonths < 12) {\r\n      return `${diffInMonths} month${diffInMonths !== 1 ? 's' : ''} ago`;\r\n    }\r\n\r\n    const diffInYears = Math.floor(diffInMonths / 12);\r\n    return `${diffInYears} year${diffInYears !== 1 ? 's' : ''} ago`;\r\n  } catch (error) {\r\n    console.error('Error calculating relative time:', error);\r\n    return '';\r\n  }\r\n};\r\n\r\n/**\r\n * Check if a date is today\r\n * @param {string|Date} dateValue - The date value to check\r\n * @returns {boolean} True if the date is today\r\n */\r\nexport const isToday = (dateValue) => {\r\n  if (!dateValue) return false;\r\n\r\n  try {\r\n    const date = typeof dateValue === 'string' ? new Date(dateValue) : dateValue;\r\n    const today = new Date();\r\n    \r\n    return date.getDate() === today.getDate() &&\r\n           date.getMonth() === today.getMonth() &&\r\n           date.getFullYear() === today.getFullYear();\r\n  } catch (error) {\r\n    return false;\r\n  }\r\n};\r\n\r\n/**\r\n * Check if a date is yesterday\r\n * @param {string|Date} dateValue - The date value to check\r\n * @returns {boolean} True if the date is yesterday\r\n */\r\nexport const isYesterday = (dateValue) => {\r\n  if (!dateValue) return false;\r\n\r\n  try {\r\n    const date = typeof dateValue === 'string' ? new Date(dateValue) : dateValue;\r\n    const yesterday = new Date();\r\n    yesterday.setDate(yesterday.getDate() - 1);\r\n    \r\n    return date.getDate() === yesterday.getDate() &&\r\n           date.getMonth() === yesterday.getMonth() &&\r\n           date.getFullYear() === yesterday.getFullYear();\r\n  } catch (error) {\r\n    return false;\r\n  }\r\n};\r\n\r\n/**\r\n * Format date for audit display with relative time if recent\r\n * @param {string|Date} dateValue - The date value to format\r\n * @returns {string} Formatted date string with relative time if applicable\r\n */\r\nexport const formatAuditDate = (dateValue) => {\r\n  if (!dateValue) return 'N/A';\r\n\r\n  try {\r\n    const date = typeof dateValue === 'string' ? new Date(dateValue) : dateValue;\r\n    const now = new Date();\r\n    const diffInHours = Math.floor((now - date) / (1000 * 60 * 60));\r\n\r\n    // Show relative time for recent dates (within 24 hours)\r\n    if (diffInHours < 24) {\r\n      const relativeTime = getRelativeTime(dateValue);\r\n      const formattedDate = formatDateTime(dateValue);\r\n      return `${relativeTime} (${formattedDate})`;\r\n    }\r\n\r\n    // Show formatted date for older dates\r\n    return formatDateTime(dateValue);\r\n  } catch (error) {\r\n    console.error('Error formatting audit date:', error);\r\n    return 'Invalid Date';\r\n  }\r\n};\r\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,cAAc,GAAGA,CAACC,SAAS,EAAEC,OAAO,GAAG,CAAC,CAAC,KAAK;EACzD,IAAI,CAACD,SAAS,EAAE,OAAO,EAAE;EAEzB,MAAM;IACJE,WAAW,GAAG,IAAI;IAClBC,cAAc,GAAG,KAAK;IACtBC,MAAM,GAAG;EACX,CAAC,GAAGH,OAAO;EAEX,IAAI;IACF,MAAMI,IAAI,GAAG,OAAOL,SAAS,KAAK,QAAQ,GAAG,IAAIM,IAAI,CAACN,SAAS,CAAC,GAAGA,SAAS;IAE5E,IAAIO,KAAK,CAACF,IAAI,CAACG,OAAO,CAAC,CAAC,CAAC,EAAE;MACzB,OAAO,cAAc;IACvB;IAEA,MAAMC,WAAW,GAAG;MAClBC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,OAAO;MACdC,GAAG,EAAE;IACP,CAAC;IAED,IAAIV,WAAW,EAAE;MACfO,WAAW,CAACI,IAAI,GAAG,SAAS;MAC5BJ,WAAW,CAACK,MAAM,GAAG,SAAS;MAE9B,IAAIX,cAAc,EAAE;QAClBM,WAAW,CAACM,MAAM,GAAG,SAAS;MAChC;IACF;IAEA,OAAOV,IAAI,CAACW,kBAAkB,CAACZ,MAAM,EAAEK,WAAW,CAAC;EACrD,CAAC,CAAC,OAAOQ,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAC9C,OAAO,cAAc;EACvB;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAME,UAAU,GAAGA,CAACnB,SAAS,EAAEI,MAAM,GAAG,OAAO,KAAK;EACzD,OAAOL,cAAc,CAACC,SAAS,EAAE;IAAEE,WAAW,EAAE,KAAK;IAAEE;EAAO,CAAC,CAAC;AAClE,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMgB,UAAU,GAAGA,CAACpB,SAAS,EAAEG,cAAc,GAAG,KAAK,EAAEC,MAAM,GAAG,OAAO,KAAK;EACjF,IAAI,CAACJ,SAAS,EAAE,OAAO,EAAE;EAEzB,IAAI;IACF,MAAMK,IAAI,GAAG,OAAOL,SAAS,KAAK,QAAQ,GAAG,IAAIM,IAAI,CAACN,SAAS,CAAC,GAAGA,SAAS;IAE5E,IAAIO,KAAK,CAACF,IAAI,CAACG,OAAO,CAAC,CAAC,CAAC,EAAE;MACzB,OAAO,cAAc;IACvB;IAEA,MAAMa,WAAW,GAAG;MAClBR,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC;IAED,IAAIX,cAAc,EAAE;MAClBkB,WAAW,CAACN,MAAM,GAAG,SAAS;IAChC;IAEA,OAAOV,IAAI,CAACiB,kBAAkB,CAAClB,MAAM,EAAEiB,WAAW,CAAC;EACrD,CAAC,CAAC,OAAOJ,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAC9C,OAAO,cAAc;EACvB;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMM,eAAe,GAAIvB,SAAS,IAAK;EAC5C,IAAI,CAACA,SAAS,EAAE,OAAO,EAAE;EAEzB,IAAI;IACF,MAAMK,IAAI,GAAG,OAAOL,SAAS,KAAK,QAAQ,GAAG,IAAIM,IAAI,CAACN,SAAS,CAAC,GAAGA,SAAS;IAC5E,MAAMwB,GAAG,GAAG,IAAIlB,IAAI,CAAC,CAAC;IACtB,MAAMmB,aAAa,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACH,GAAG,GAAGnB,IAAI,IAAI,IAAI,CAAC;IAErD,IAAIoB,aAAa,GAAG,EAAE,EAAE;MACtB,OAAO,UAAU;IACnB;IAEA,MAAMG,aAAa,GAAGF,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,EAAE,CAAC;IACpD,IAAIG,aAAa,GAAG,EAAE,EAAE;MACtB,OAAO,GAAGA,aAAa,UAAUA,aAAa,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,MAAM;IACvE;IAEA,MAAMC,WAAW,GAAGH,IAAI,CAACC,KAAK,CAACC,aAAa,GAAG,EAAE,CAAC;IAClD,IAAIC,WAAW,GAAG,EAAE,EAAE;MACpB,OAAO,GAAGA,WAAW,QAAQA,WAAW,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,MAAM;IACjE;IAEA,MAAMC,UAAU,GAAGJ,IAAI,CAACC,KAAK,CAACE,WAAW,GAAG,EAAE,CAAC;IAC/C,IAAIC,UAAU,GAAG,EAAE,EAAE;MACnB,OAAO,GAAGA,UAAU,OAAOA,UAAU,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,MAAM;IAC9D;IAEA,MAAMC,YAAY,GAAGL,IAAI,CAACC,KAAK,CAACG,UAAU,GAAG,EAAE,CAAC;IAChD,IAAIC,YAAY,GAAG,EAAE,EAAE;MACrB,OAAO,GAAGA,YAAY,SAASA,YAAY,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,MAAM;IACpE;IAEA,MAAMC,WAAW,GAAGN,IAAI,CAACC,KAAK,CAACI,YAAY,GAAG,EAAE,CAAC;IACjD,OAAO,GAAGC,WAAW,QAAQA,WAAW,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,MAAM;EACjE,CAAC,CAAC,OAAOf,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;IACxD,OAAO,EAAE;EACX;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMgB,OAAO,GAAIjC,SAAS,IAAK;EACpC,IAAI,CAACA,SAAS,EAAE,OAAO,KAAK;EAE5B,IAAI;IACF,MAAMK,IAAI,GAAG,OAAOL,SAAS,KAAK,QAAQ,GAAG,IAAIM,IAAI,CAACN,SAAS,CAAC,GAAGA,SAAS;IAC5E,MAAMkC,KAAK,GAAG,IAAI5B,IAAI,CAAC,CAAC;IAExB,OAAOD,IAAI,CAAC8B,OAAO,CAAC,CAAC,KAAKD,KAAK,CAACC,OAAO,CAAC,CAAC,IAClC9B,IAAI,CAAC+B,QAAQ,CAAC,CAAC,KAAKF,KAAK,CAACE,QAAQ,CAAC,CAAC,IACpC/B,IAAI,CAACgC,WAAW,CAAC,CAAC,KAAKH,KAAK,CAACG,WAAW,CAAC,CAAC;EACnD,CAAC,CAAC,OAAOpB,KAAK,EAAE;IACd,OAAO,KAAK;EACd;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMqB,WAAW,GAAItC,SAAS,IAAK;EACxC,IAAI,CAACA,SAAS,EAAE,OAAO,KAAK;EAE5B,IAAI;IACF,MAAMK,IAAI,GAAG,OAAOL,SAAS,KAAK,QAAQ,GAAG,IAAIM,IAAI,CAACN,SAAS,CAAC,GAAGA,SAAS;IAC5E,MAAMuC,SAAS,GAAG,IAAIjC,IAAI,CAAC,CAAC;IAC5BiC,SAAS,CAACC,OAAO,CAACD,SAAS,CAACJ,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;IAE1C,OAAO9B,IAAI,CAAC8B,OAAO,CAAC,CAAC,KAAKI,SAAS,CAACJ,OAAO,CAAC,CAAC,IACtC9B,IAAI,CAAC+B,QAAQ,CAAC,CAAC,KAAKG,SAAS,CAACH,QAAQ,CAAC,CAAC,IACxC/B,IAAI,CAACgC,WAAW,CAAC,CAAC,KAAKE,SAAS,CAACF,WAAW,CAAC,CAAC;EACvD,CAAC,CAAC,OAAOpB,KAAK,EAAE;IACd,OAAO,KAAK;EACd;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMwB,eAAe,GAAIzC,SAAS,IAAK;EAC5C,IAAI,CAACA,SAAS,EAAE,OAAO,KAAK;EAE5B,IAAI;IACF,MAAMK,IAAI,GAAG,OAAOL,SAAS,KAAK,QAAQ,GAAG,IAAIM,IAAI,CAACN,SAAS,CAAC,GAAGA,SAAS;IAC5E,MAAMwB,GAAG,GAAG,IAAIlB,IAAI,CAAC,CAAC;IACtB,MAAMuB,WAAW,GAAGH,IAAI,CAACC,KAAK,CAAC,CAACH,GAAG,GAAGnB,IAAI,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;;IAE/D;IACA,IAAIwB,WAAW,GAAG,EAAE,EAAE;MACpB,MAAMa,YAAY,GAAGnB,eAAe,CAACvB,SAAS,CAAC;MAC/C,MAAM2C,aAAa,GAAG5C,cAAc,CAACC,SAAS,CAAC;MAC/C,OAAO,GAAG0C,YAAY,KAAKC,aAAa,GAAG;IAC7C;;IAEA;IACA,OAAO5C,cAAc,CAACC,SAAS,CAAC;EAClC,CAAC,CAAC,OAAOiB,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACpD,OAAO,cAAc;EACvB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}