{"ast": null, "code": "import { __read } from \"tslib\";\nimport { innerFrom } from '../observable/innerFrom';\nimport { Observable } from '../Observable';\nimport { mergeMap } from '../operators/mergeMap';\nimport { isArrayLike } from '../util/isArrayLike';\nimport { isFunction } from '../util/isFunction';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nvar nodeEventEmitterMethods = ['addListener', 'removeListener'];\nvar eventTargetMethods = ['addEventListener', 'removeEventListener'];\nvar jqueryMethods = ['on', 'off'];\nexport function fromEvent(target, eventName, options, resultSelector) {\n  if (isFunction(options)) {\n    resultSelector = options;\n    options = undefined;\n  }\n  if (resultSelector) {\n    return fromEvent(target, eventName, options).pipe(mapOneOrManyArgs(resultSelector));\n  }\n  var _a = __read(isEventTarget(target) ? eventTargetMethods.map(function (methodName) {\n      return function (handler) {\n        return target[methodName](eventName, handler, options);\n      };\n    }) : isNodeStyleEventEmitter(target) ? nodeEventEmitterMethods.map(toCommonHandlerRegistry(target, eventName)) : isJQueryStyleEventEmitter(target) ? jqueryMethods.map(toCommonHandlerRegistry(target, eventName)) : [], 2),\n    add = _a[0],\n    remove = _a[1];\n  if (!add) {\n    if (isArrayLike(target)) {\n      return mergeMap(function (subTarget) {\n        return fromEvent(subTarget, eventName, options);\n      })(innerFrom(target));\n    }\n  }\n  if (!add) {\n    throw new TypeError('Invalid event target');\n  }\n  return new Observable(function (subscriber) {\n    var handler = function () {\n      var args = [];\n      for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n      }\n      return subscriber.next(1 < args.length ? args : args[0]);\n    };\n    add(handler);\n    return function () {\n      return remove(handler);\n    };\n  });\n}\nfunction toCommonHandlerRegistry(target, eventName) {\n  return function (methodName) {\n    return function (handler) {\n      return target[methodName](eventName, handler);\n    };\n  };\n}\nfunction isNodeStyleEventEmitter(target) {\n  return isFunction(target.addListener) && isFunction(target.removeListener);\n}\nfunction isJQueryStyleEventEmitter(target) {\n  return isFunction(target.on) && isFunction(target.off);\n}\nfunction isEventTarget(target) {\n  return isFunction(target.addEventListener) && isFunction(target.removeEventListener);\n}", "map": {"version": 3, "names": ["innerFrom", "Observable", "mergeMap", "isArrayLike", "isFunction", "mapOneOrManyArgs", "nodeEventEmitterMethods", "eventTargetMethods", "jqueryMethods", "fromEvent", "target", "eventName", "options", "resultSelector", "undefined", "pipe", "_a", "__read", "isEventTarget", "map", "methodName", "handler", "isNodeStyleEventEmitter", "toCommonHandlerRegistry", "isJQueryStyleEventEmitter", "add", "remove", "subTarget", "TypeError", "subscriber", "args", "_i", "arguments", "length", "next", "addListener", "removeListener", "on", "off", "addEventListener", "removeEventListener"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\observable\\fromEvent.ts"], "sourcesContent": ["import { innerFrom } from '../observable/innerFrom';\nimport { Observable } from '../Observable';\nimport { mergeMap } from '../operators/mergeMap';\nimport { isArrayLike } from '../util/isArrayLike';\nimport { isFunction } from '../util/isFunction';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\n\n// These constants are used to create handler registry functions using array mapping below.\nconst nodeEventEmitterMethods = ['addListener', 'removeListener'] as const;\nconst eventTargetMethods = ['addEventListener', 'removeEventListener'] as const;\nconst jqueryMethods = ['on', 'off'] as const;\n\nexport interface NodeStyleEventEmitter {\n  addListener(eventName: string | symbol, handler: NodeEventHandler): this;\n  removeListener(eventName: string | symbol, handler: NodeEventHandler): this;\n}\n\nexport type NodeEventHandler = (...args: any[]) => void;\n\n// For APIs that implement `addListener` and `removeListener` methods that may\n// not use the same arguments or return EventEmitter values\n// such as React Native\nexport interface NodeCompatibleEventEmitter {\n  addListener(eventName: string, handler: NodeEventHandler): void | {};\n  removeListener(eventName: string, handler: NodeEventHandler): void | {};\n}\n\n// Use handler types like those in @types/jquery. See:\n// https://github.com/DefinitelyTyped/DefinitelyTyped/blob/847731ba1d7fa6db6b911c0e43aa0afe596e7723/types/jquery/misc.d.ts#L6395\nexport interface JQueryStyleEventEmitter<TContext, T> {\n  on(eventName: string, handler: (this: TContext, t: T, ...args: any[]) => any): void;\n  off(eventName: string, handler: (this: TContext, t: T, ...args: any[]) => any): void;\n}\n\nexport interface EventListenerObject<E> {\n  handleEvent(evt: E): void;\n}\n\nexport interface HasEventTargetAddRemove<E> {\n  addEventListener(\n    type: string,\n    listener: ((evt: E) => void) | EventListenerObject<E> | null,\n    options?: boolean | AddEventListenerOptions\n  ): void;\n  removeEventListener(\n    type: string,\n    listener: ((evt: E) => void) | EventListenerObject<E> | null,\n    options?: EventListenerOptions | boolean\n  ): void;\n}\n\nexport interface EventListenerOptions {\n  capture?: boolean;\n  passive?: boolean;\n  once?: boolean;\n}\n\nexport interface AddEventListenerOptions extends EventListenerOptions {\n  once?: boolean;\n  passive?: boolean;\n}\n\nexport function fromEvent<T>(target: HasEventTargetAddRemove<T> | ArrayLike<HasEventTargetAddRemove<T>>, eventName: string): Observable<T>;\nexport function fromEvent<T, R>(\n  target: HasEventTargetAddRemove<T> | ArrayLike<HasEventTargetAddRemove<T>>,\n  eventName: string,\n  resultSelector: (event: T) => R\n): Observable<R>;\nexport function fromEvent<T>(\n  target: HasEventTargetAddRemove<T> | ArrayLike<HasEventTargetAddRemove<T>>,\n  eventName: string,\n  options: EventListenerOptions\n): Observable<T>;\nexport function fromEvent<T, R>(\n  target: HasEventTargetAddRemove<T> | ArrayLike<HasEventTargetAddRemove<T>>,\n  eventName: string,\n  options: EventListenerOptions,\n  resultSelector: (event: T) => R\n): Observable<R>;\n\nexport function fromEvent(target: NodeStyleEventEmitter | ArrayLike<NodeStyleEventEmitter>, eventName: string): Observable<unknown>;\n/** @deprecated Do not specify explicit type parameters. Signatures with type parameters that cannot be inferred will be removed in v8. */\nexport function fromEvent<T>(target: NodeStyleEventEmitter | ArrayLike<NodeStyleEventEmitter>, eventName: string): Observable<T>;\nexport function fromEvent<R>(\n  target: NodeStyleEventEmitter | ArrayLike<NodeStyleEventEmitter>,\n  eventName: string,\n  resultSelector: (...args: any[]) => R\n): Observable<R>;\n\nexport function fromEvent(\n  target: NodeCompatibleEventEmitter | ArrayLike<NodeCompatibleEventEmitter>,\n  eventName: string\n): Observable<unknown>;\n/** @deprecated Do not specify explicit type parameters. Signatures with type parameters that cannot be inferred will be removed in v8. */\nexport function fromEvent<T>(target: NodeCompatibleEventEmitter | ArrayLike<NodeCompatibleEventEmitter>, eventName: string): Observable<T>;\nexport function fromEvent<R>(\n  target: NodeCompatibleEventEmitter | ArrayLike<NodeCompatibleEventEmitter>,\n  eventName: string,\n  resultSelector: (...args: any[]) => R\n): Observable<R>;\n\nexport function fromEvent<T>(\n  target: JQueryStyleEventEmitter<any, T> | ArrayLike<JQueryStyleEventEmitter<any, T>>,\n  eventName: string\n): Observable<T>;\nexport function fromEvent<T, R>(\n  target: JQueryStyleEventEmitter<any, T> | ArrayLike<JQueryStyleEventEmitter<any, T>>,\n  eventName: string,\n  resultSelector: (value: T, ...args: any[]) => R\n): Observable<R>;\n\n/**\n * Creates an Observable that emits events of a specific type coming from the\n * given event target.\n *\n * <span class=\"informal\">Creates an Observable from DOM events, or Node.js\n * EventEmitter events or others.</span>\n *\n * ![](fromEvent.png)\n *\n * `fromEvent` accepts as a first argument event target, which is an object with methods\n * for registering event handler functions. As a second argument it takes string that indicates\n * type of event we want to listen for. `fromEvent` supports selected types of event targets,\n * which are described in detail below. If your event target does not match any of the ones listed,\n * you should use {@link fromEventPattern}, which can be used on arbitrary APIs.\n * When it comes to APIs supported by `fromEvent`, their methods for adding and removing event\n * handler functions have different names, but they all accept a string describing event type\n * and function itself, which will be called whenever said event happens.\n *\n * Every time resulting Observable is subscribed, event handler function will be registered\n * to event target on given event type. When that event fires, value\n * passed as a first argument to registered function will be emitted by output Observable.\n * When Observable is unsubscribed, function will be unregistered from event target.\n *\n * Note that if event target calls registered function with more than one argument, second\n * and following arguments will not appear in resulting stream. In order to get access to them,\n * you can pass to `fromEvent` optional project function, which will be called with all arguments\n * passed to event handler. Output Observable will then emit value returned by project function,\n * instead of the usual value.\n *\n * Remember that event targets listed below are checked via duck typing. It means that\n * no matter what kind of object you have and no matter what environment you work in,\n * you can safely use `fromEvent` on that object if it exposes described methods (provided\n * of course they behave as was described above). So for example if Node.js library exposes\n * event target which has the same method names as DOM EventTarget, `fromEvent` is still\n * a good choice.\n *\n * If the API you use is more callback then event handler oriented (subscribed\n * callback function fires only once and thus there is no need to manually\n * unregister it), you should use {@link bindCallback} or {@link bindNodeCallback}\n * instead.\n *\n * `fromEvent` supports following types of event targets:\n *\n * **DOM EventTarget**\n *\n * This is an object with `addEventListener` and `removeEventListener` methods.\n *\n * In the browser, `addEventListener` accepts - apart from event type string and event\n * handler function arguments - optional third parameter, which is either an object or boolean,\n * both used for additional configuration how and when passed function will be called. When\n * `fromEvent` is used with event target of that type, you can provide this values\n * as third parameter as well.\n *\n * **Node.js EventEmitter**\n *\n * An object with `addListener` and `removeListener` methods.\n *\n * **JQuery-style event target**\n *\n * An object with `on` and `off` methods\n *\n * **DOM NodeList**\n *\n * List of DOM Nodes, returned for example by `document.querySelectorAll` or `Node.childNodes`.\n *\n * Although this collection is not event target in itself, `fromEvent` will iterate over all Nodes\n * it contains and install event handler function in every of them. When returned Observable\n * is unsubscribed, function will be removed from all Nodes.\n *\n * **DOM HtmlCollection**\n *\n * Just as in case of NodeList it is a collection of DOM nodes. Here as well event handler function is\n * installed and removed in each of elements.\n *\n *\n * ## Examples\n *\n * Emit clicks happening on the DOM document\n *\n * ```ts\n * import { fromEvent } from 'rxjs';\n *\n * const clicks = fromEvent(document, 'click');\n * clicks.subscribe(x => console.log(x));\n *\n * // Results in:\n * // MouseEvent object logged to console every time a click\n * // occurs on the document.\n * ```\n *\n * Use `addEventListener` with capture option\n *\n * ```ts\n * import { fromEvent } from 'rxjs';\n *\n * const div = document.createElement('div');\n * div.style.cssText = 'width: 200px; height: 200px; background: #09c;';\n * document.body.appendChild(div);\n *\n * // note optional configuration parameter which will be passed to addEventListener\n * const clicksInDocument = fromEvent(document, 'click', { capture: true });\n * const clicksInDiv = fromEvent(div, 'click');\n *\n * clicksInDocument.subscribe(() => console.log('document'));\n * clicksInDiv.subscribe(() => console.log('div'));\n *\n * // By default events bubble UP in DOM tree, so normally\n * // when we would click on div in document\n * // \"div\" would be logged first and then \"document\".\n * // Since we specified optional `capture` option, document\n * // will catch event when it goes DOWN DOM tree, so console\n * // will log \"document\" and then \"div\".\n * ```\n *\n * @see {@link bindCallback}\n * @see {@link bindNodeCallback}\n * @see {@link fromEventPattern}\n *\n * @param target The DOM EventTarget, Node.js EventEmitter, JQuery-like event target,\n * NodeList or HTMLCollection to attach the event handler to.\n * @param eventName The event name of interest, being emitted by the `target`.\n * @param options Options to pass through to the underlying `addListener`,\n * `addEventListener` or `on` functions.\n * @param resultSelector A mapping function used to transform events. It takes the\n * arguments from the event handler and should return a single value.\n * @return An Observable emitting events registered through `target`'s\n * listener handlers.\n */\nexport function fromEvent<T>(\n  target: any,\n  eventName: string,\n  options?: EventListenerOptions | ((...args: any[]) => T),\n  resultSelector?: (...args: any[]) => T\n): Observable<T> {\n  if (isFunction(options)) {\n    resultSelector = options;\n    options = undefined;\n  }\n  if (resultSelector) {\n    return fromEvent<T>(target, eventName, options as EventListenerOptions).pipe(mapOneOrManyArgs(resultSelector));\n  }\n\n  // Figure out our add and remove methods. In order to do this,\n  // we are going to analyze the target in a preferred order, if\n  // the target matches a given signature, we take the two \"add\" and \"remove\"\n  // method names and apply them to a map to create opposite versions of the\n  // same function. This is because they all operate in duplicate pairs,\n  // `addListener(name, handler)`, `removeListener(name, handler)`, for example.\n  // The call only differs by method name, as to whether or not you're adding or removing.\n  const [add, remove] =\n    // If it is an EventTarget, we need to use a slightly different method than the other two patterns.\n    isEventTarget(target)\n      ? eventTargetMethods.map((methodName) => (handler: any) => target[methodName](eventName, handler, options as EventListenerOptions))\n      : // In all other cases, the call pattern is identical with the exception of the method names.\n      isNodeStyleEventEmitter(target)\n      ? nodeEventEmitterMethods.map(toCommonHandlerRegistry(target, eventName))\n      : isJQueryStyleEventEmitter(target)\n      ? jqueryMethods.map(toCommonHandlerRegistry(target, eventName))\n      : [];\n\n  // If add is falsy, it's because we didn't match a pattern above.\n  // Check to see if it is an ArrayLike, because if it is, we want to\n  // try to apply fromEvent to all of it's items. We do this check last,\n  // because there are may be some types that are both ArrayLike *and* implement\n  // event registry points, and we'd rather delegate to that when possible.\n  if (!add) {\n    if (isArrayLike(target)) {\n      return mergeMap((subTarget: any) => fromEvent(subTarget, eventName, options as EventListenerOptions))(\n        innerFrom(target)\n      ) as Observable<T>;\n    }\n  }\n\n  // If add is falsy and we made it here, it's because we didn't\n  // match any valid target objects above.\n  if (!add) {\n    throw new TypeError('Invalid event target');\n  }\n\n  return new Observable<T>((subscriber) => {\n    // The handler we are going to register. Forwards the event object, by itself, or\n    // an array of arguments to the event handler, if there is more than one argument,\n    // to the consumer.\n    const handler = (...args: any[]) => subscriber.next(1 < args.length ? args : args[0]);\n    // Do the work of adding the handler to the target.\n    add(handler);\n    // When we finalize, we want to remove the handler and free up memory.\n    return () => remove!(handler);\n  });\n}\n\n/**\n * Used to create `add` and `remove` functions to register and unregister event handlers\n * from a target in the most common handler pattern, where there are only two arguments.\n * (e.g.  `on(name, fn)`, `off(name, fn)`, `addListener(name, fn)`, or `removeListener(name, fn)`)\n * @param target The target we're calling methods on\n * @param eventName The event name for the event we're creating register or unregister functions for\n */\nfunction toCommonHandlerRegistry(target: any, eventName: string) {\n  return (methodName: string) => (handler: any) => target[methodName](eventName, handler);\n}\n\n/**\n * Checks to see if the target implements the required node-style EventEmitter methods\n * for adding and removing event handlers.\n * @param target the object to check\n */\nfunction isNodeStyleEventEmitter(target: any): target is NodeStyleEventEmitter {\n  return isFunction(target.addListener) && isFunction(target.removeListener);\n}\n\n/**\n * Checks to see if the target implements the required jQuery-style EventEmitter methods\n * for adding and removing event handlers.\n * @param target the object to check\n */\nfunction isJQueryStyleEventEmitter(target: any): target is JQueryStyleEventEmitter<any, any> {\n  return isFunction(target.on) && isFunction(target.off);\n}\n\n/**\n * Checks to see if the target implements the required EventTarget methods\n * for adding and removing event handlers.\n * @param target the object to check\n */\nfunction isEventTarget(target: any): target is HasEventTargetAddRemove<any> {\n  return isFunction(target.addEventListener) && isFunction(target.removeEventListener);\n}\n"], "mappings": ";AAAA,SAASA,SAAS,QAAQ,yBAAyB;AACnD,SAASC,UAAU,QAAQ,eAAe;AAC1C,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,WAAW,QAAQ,qBAAqB;AACjD,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,SAASC,gBAAgB,QAAQ,0BAA0B;AAG3D,IAAMC,uBAAuB,GAAG,CAAC,aAAa,EAAE,gBAAgB,CAAU;AAC1E,IAAMC,kBAAkB,GAAG,CAAC,kBAAkB,EAAE,qBAAqB,CAAU;AAC/E,IAAMC,aAAa,GAAG,CAAC,IAAI,EAAE,KAAK,CAAU;AAqO5C,OAAM,SAAUC,SAASA,CACvBC,MAAW,EACXC,SAAiB,EACjBC,OAAwD,EACxDC,cAAsC;EAEtC,IAAIT,UAAU,CAACQ,OAAO,CAAC,EAAE;IACvBC,cAAc,GAAGD,OAAO;IACxBA,OAAO,GAAGE,SAAS;;EAErB,IAAID,cAAc,EAAE;IAClB,OAAOJ,SAAS,CAAIC,MAAM,EAAEC,SAAS,EAAEC,OAA+B,CAAC,CAACG,IAAI,CAACV,gBAAgB,CAACQ,cAAc,CAAC,CAAC;;EAU1G,IAAAG,EAAA,GAAAC,MAAA,CAEJC,aAAa,CAACR,MAAM,CAAC,GACjBH,kBAAkB,CAACY,GAAG,CAAC,UAACC,UAAU;MAAK,iBAACC,OAAY;QAAK,OAAAX,MAAM,CAACU,UAAU,CAAC,CAACT,SAAS,EAAEU,OAAO,EAAET,OAA+B,CAAC;MAAvE,CAAuE;IAAzF,CAAyF,CAAC,GAEnIU,uBAAuB,CAACZ,MAAM,CAAC,GAC7BJ,uBAAuB,CAACa,GAAG,CAACI,uBAAuB,CAACb,MAAM,EAAEC,SAAS,CAAC,CAAC,GACvEa,yBAAyB,CAACd,MAAM,CAAC,GACjCF,aAAa,CAACW,GAAG,CAACI,uBAAuB,CAACb,MAAM,EAAEC,SAAS,CAAC,CAAC,GAC7D,EAAE;IATDc,GAAG,GAAAT,EAAA;IAAEU,MAAM,GAAAV,EAAA,GASV;EAOR,IAAI,CAACS,GAAG,EAAE;IACR,IAAItB,WAAW,CAACO,MAAM,CAAC,EAAE;MACvB,OAAOR,QAAQ,CAAC,UAACyB,SAAc;QAAK,OAAAlB,SAAS,CAACkB,SAAS,EAAEhB,SAAS,EAAEC,OAA+B,CAAC;MAAhE,CAAgE,CAAC,CACnGZ,SAAS,CAACU,MAAM,CAAC,CACD;;;EAMtB,IAAI,CAACe,GAAG,EAAE;IACR,MAAM,IAAIG,SAAS,CAAC,sBAAsB,CAAC;;EAG7C,OAAO,IAAI3B,UAAU,CAAI,UAAC4B,UAAU;IAIlC,IAAMR,OAAO,GAAG,SAAAA,CAAA;MAAC,IAAAS,IAAA;WAAA,IAAAC,EAAA,IAAc,EAAdA,EAAA,GAAAC,SAAA,CAAAC,MAAc,EAAdF,EAAA,EAAc;QAAdD,IAAA,CAAAC,EAAA,IAAAC,SAAA,CAAAD,EAAA;;MAAmB,OAAAF,UAAU,CAACK,IAAI,CAAC,CAAC,GAAGJ,IAAI,CAACG,MAAM,GAAGH,IAAI,GAAGA,IAAI,CAAC,CAAC,CAAC,CAAC;IAAjD,CAAiD;IAErFL,GAAG,CAACJ,OAAO,CAAC;IAEZ,OAAO;MAAM,OAAAK,MAAO,CAACL,OAAO,CAAC;IAAhB,CAAgB;EAC/B,CAAC,CAAC;AACJ;AASA,SAASE,uBAAuBA,CAACb,MAAW,EAAEC,SAAiB;EAC7D,OAAO,UAACS,UAAkB;IAAK,iBAACC,OAAY;MAAK,OAAAX,MAAM,CAACU,UAAU,CAAC,CAACT,SAAS,EAAEU,OAAO,CAAC;IAAtC,CAAsC;EAAxD,CAAwD;AACzF;AAOA,SAASC,uBAAuBA,CAACZ,MAAW;EAC1C,OAAON,UAAU,CAACM,MAAM,CAACyB,WAAW,CAAC,IAAI/B,UAAU,CAACM,MAAM,CAAC0B,cAAc,CAAC;AAC5E;AAOA,SAASZ,yBAAyBA,CAACd,MAAW;EAC5C,OAAON,UAAU,CAACM,MAAM,CAAC2B,EAAE,CAAC,IAAIjC,UAAU,CAACM,MAAM,CAAC4B,GAAG,CAAC;AACxD;AAOA,SAASpB,aAAaA,CAACR,MAAW;EAChC,OAAON,UAAU,CAACM,MAAM,CAAC6B,gBAAgB,CAAC,IAAInC,UAAU,CAACM,MAAM,CAAC8B,mBAAmB,CAAC;AACtF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}