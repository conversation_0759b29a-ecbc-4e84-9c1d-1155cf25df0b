{"ast": null, "code": "function _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _classCallCheck(a, n) {\n  if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\");\n}\nfunction _defineProperties(e, r) {\n  for (var t = 0; t < r.length; t++) {\n    var o = r[t];\n    o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o);\n  }\n}\nfunction _createClass(e, r, t) {\n  return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", {\n    writable: !1\n  }), e;\n}\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nimport { makePromise } from './utils.js';\nimport request from './request.js';\nvar getDefaults = function getDefaults() {\n  return {\n    loadPath: '/locales/{{lng}}/{{ns}}.json',\n    addPath: '/locales/add/{{lng}}/{{ns}}',\n    parse: function parse(data) {\n      return JSON.parse(data);\n    },\n    stringify: JSON.stringify,\n    parsePayload: function parsePayload(namespace, key, fallbackValue) {\n      return _defineProperty({}, key, fallbackValue || '');\n    },\n    parseLoadPayload: function parseLoadPayload(languages, namespaces) {\n      return undefined;\n    },\n    request: request,\n    reloadInterval: typeof window !== 'undefined' ? false : 60 * 60 * 1000,\n    customHeaders: {},\n    queryStringParams: {},\n    crossDomain: false,\n    withCredentials: false,\n    overrideMimeType: false,\n    requestOptions: {\n      mode: 'cors',\n      credentials: 'same-origin',\n      cache: 'default'\n    }\n  };\n};\nvar Backend = function () {\n  function Backend(services) {\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var allOptions = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    _classCallCheck(this, Backend);\n    this.services = services;\n    this.options = options;\n    this.allOptions = allOptions;\n    this.type = 'backend';\n    this.init(services, options, allOptions);\n  }\n  return _createClass(Backend, [{\n    key: \"init\",\n    value: function init(services) {\n      var _this = this;\n      var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      var allOptions = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n      this.services = services;\n      this.options = _objectSpread(_objectSpread(_objectSpread({}, getDefaults()), this.options || {}), options);\n      this.allOptions = allOptions;\n      if (this.services && this.options.reloadInterval) {\n        var timer = setInterval(function () {\n          return _this.reload();\n        }, this.options.reloadInterval);\n        if (_typeof(timer) === 'object' && typeof timer.unref === 'function') timer.unref();\n      }\n    }\n  }, {\n    key: \"readMulti\",\n    value: function readMulti(languages, namespaces, callback) {\n      this._readAny(languages, languages, namespaces, namespaces, callback);\n    }\n  }, {\n    key: \"read\",\n    value: function read(language, namespace, callback) {\n      this._readAny([language], language, [namespace], namespace, callback);\n    }\n  }, {\n    key: \"_readAny\",\n    value: function _readAny(languages, loadUrlLanguages, namespaces, loadUrlNamespaces, callback) {\n      var _this2 = this;\n      var loadPath = this.options.loadPath;\n      if (typeof this.options.loadPath === 'function') {\n        loadPath = this.options.loadPath(languages, namespaces);\n      }\n      loadPath = makePromise(loadPath);\n      loadPath.then(function (resolvedLoadPath) {\n        if (!resolvedLoadPath) return callback(null, {});\n        var url = _this2.services.interpolator.interpolate(resolvedLoadPath, {\n          lng: languages.join('+'),\n          ns: namespaces.join('+')\n        });\n        _this2.loadUrl(url, callback, loadUrlLanguages, loadUrlNamespaces);\n      });\n    }\n  }, {\n    key: \"loadUrl\",\n    value: function loadUrl(url, callback, languages, namespaces) {\n      var _this3 = this;\n      var lng = typeof languages === 'string' ? [languages] : languages;\n      var ns = typeof namespaces === 'string' ? [namespaces] : namespaces;\n      var payload = this.options.parseLoadPayload(lng, ns);\n      this.options.request(this.options, url, payload, function (err, res) {\n        if (res && (res.status >= 500 && res.status < 600 || !res.status)) return callback('failed loading ' + url + '; status code: ' + res.status, true);\n        if (res && res.status >= 400 && res.status < 500) return callback('failed loading ' + url + '; status code: ' + res.status, false);\n        if (!res && err && err.message) {\n          var errorMessage = err.message.toLowerCase();\n          var isNetworkError = ['failed', 'fetch', 'network', 'load'].find(function (term) {\n            return errorMessage.indexOf(term) > -1;\n          });\n          if (isNetworkError) {\n            return callback('failed loading ' + url + ': ' + err.message, true);\n          }\n        }\n        if (err) return callback(err, false);\n        var ret, parseErr;\n        try {\n          if (typeof res.data === 'string') {\n            ret = _this3.options.parse(res.data, languages, namespaces);\n          } else {\n            ret = res.data;\n          }\n        } catch (e) {\n          parseErr = 'failed parsing ' + url + ' to json';\n        }\n        if (parseErr) return callback(parseErr, false);\n        callback(null, ret);\n      });\n    }\n  }, {\n    key: \"create\",\n    value: function create(languages, namespace, key, fallbackValue, callback) {\n      var _this4 = this;\n      if (!this.options.addPath) return;\n      if (typeof languages === 'string') languages = [languages];\n      var payload = this.options.parsePayload(namespace, key, fallbackValue);\n      var finished = 0;\n      var dataArray = [];\n      var resArray = [];\n      languages.forEach(function (lng) {\n        var addPath = _this4.options.addPath;\n        if (typeof _this4.options.addPath === 'function') {\n          addPath = _this4.options.addPath(lng, namespace);\n        }\n        var url = _this4.services.interpolator.interpolate(addPath, {\n          lng: lng,\n          ns: namespace\n        });\n        _this4.options.request(_this4.options, url, payload, function (data, res) {\n          finished += 1;\n          dataArray.push(data);\n          resArray.push(res);\n          if (finished === languages.length) {\n            if (typeof callback === 'function') callback(dataArray, resArray);\n          }\n        });\n      });\n    }\n  }, {\n    key: \"reload\",\n    value: function reload() {\n      var _this5 = this;\n      var _this$services = this.services,\n        backendConnector = _this$services.backendConnector,\n        languageUtils = _this$services.languageUtils,\n        logger = _this$services.logger;\n      var currentLanguage = backendConnector.language;\n      if (currentLanguage && currentLanguage.toLowerCase() === 'cimode') return;\n      var toLoad = [];\n      var append = function append(lng) {\n        var lngs = languageUtils.toResolveHierarchy(lng);\n        lngs.forEach(function (l) {\n          if (toLoad.indexOf(l) < 0) toLoad.push(l);\n        });\n      };\n      append(currentLanguage);\n      if (this.allOptions.preload) this.allOptions.preload.forEach(function (l) {\n        return append(l);\n      });\n      toLoad.forEach(function (lng) {\n        _this5.allOptions.ns.forEach(function (ns) {\n          backendConnector.read(lng, ns, 'read', null, null, function (err, data) {\n            if (err) logger.warn(\"loading namespace \".concat(ns, \" for language \").concat(lng, \" failed\"), err);\n            if (!err && data) logger.log(\"loaded namespace \".concat(ns, \" for language \").concat(lng), data);\n            backendConnector.loaded(\"\".concat(lng, \"|\").concat(ns), err, data);\n          });\n        });\n      });\n    }\n  }]);\n}();\nBackend.type = 'backend';\nexport default Backend;", "map": {"version": 3, "names": ["_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "ownKeys", "e", "r", "t", "Object", "keys", "getOwnPropertySymbols", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "length", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_classCallCheck", "a", "n", "TypeError", "_defineProperties", "configurable", "writable", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "_createClass", "value", "i", "_toPrimitive", "toPrimitive", "call", "String", "Number", "makePromise", "request", "getDefaults", "loadPath", "addPath", "parse", "data", "JSON", "stringify", "parsePayload", "namespace", "fallback<PERSON><PERSON><PERSON>", "parseLoadPayload", "languages", "namespaces", "undefined", "reloadInterval", "window", "customHeaders", "queryStringParams", "crossDomain", "withCredentials", "overrideMimeType", "requestOptions", "mode", "credentials", "cache", "Backend", "services", "options", "allOptions", "type", "init", "_this", "timer", "setInterval", "reload", "unref", "readMulti", "callback", "_readAny", "read", "language", "loadUrlLanguages", "loadUrlNamespaces", "_this2", "then", "resolvedLoadPath", "url", "interpolator", "interpolate", "lng", "join", "ns", "loadUrl", "_this3", "payload", "err", "res", "status", "message", "errorMessage", "toLowerCase", "isNetworkError", "find", "term", "indexOf", "ret", "parseErr", "create", "_this4", "finished", "dataArray", "resArray", "_this5", "_this$services", "backendConnector", "languageUtils", "logger", "currentLanguage", "toLoad", "append", "lngs", "toResolveHierarchy", "l", "preload", "warn", "concat", "log", "loaded"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/node_modules/i18next-http-backend/esm/index.js"], "sourcesContent": ["function _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\"); }\nfunction _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }\nfunction _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", { writable: !1 }), e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport { makePromise } from './utils.js';\nimport request from './request.js';\nvar getDefaults = function getDefaults() {\n  return {\n    loadPath: '/locales/{{lng}}/{{ns}}.json',\n    addPath: '/locales/add/{{lng}}/{{ns}}',\n    parse: function parse(data) {\n      return JSON.parse(data);\n    },\n    stringify: JSON.stringify,\n    parsePayload: function parsePayload(namespace, key, fallbackValue) {\n      return _defineProperty({}, key, fallbackValue || '');\n    },\n    parseLoadPayload: function parseLoadPayload(languages, namespaces) {\n      return undefined;\n    },\n    request: request,\n    reloadInterval: typeof window !== 'undefined' ? false : 60 * 60 * 1000,\n    customHeaders: {},\n    queryStringParams: {},\n    crossDomain: false,\n    withCredentials: false,\n    overrideMimeType: false,\n    requestOptions: {\n      mode: 'cors',\n      credentials: 'same-origin',\n      cache: 'default'\n    }\n  };\n};\nvar Backend = function () {\n  function Backend(services) {\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var allOptions = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    _classCallCheck(this, Backend);\n    this.services = services;\n    this.options = options;\n    this.allOptions = allOptions;\n    this.type = 'backend';\n    this.init(services, options, allOptions);\n  }\n  return _createClass(Backend, [{\n    key: \"init\",\n    value: function init(services) {\n      var _this = this;\n      var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      var allOptions = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n      this.services = services;\n      this.options = _objectSpread(_objectSpread(_objectSpread({}, getDefaults()), this.options || {}), options);\n      this.allOptions = allOptions;\n      if (this.services && this.options.reloadInterval) {\n        var timer = setInterval(function () {\n          return _this.reload();\n        }, this.options.reloadInterval);\n        if (_typeof(timer) === 'object' && typeof timer.unref === 'function') timer.unref();\n      }\n    }\n  }, {\n    key: \"readMulti\",\n    value: function readMulti(languages, namespaces, callback) {\n      this._readAny(languages, languages, namespaces, namespaces, callback);\n    }\n  }, {\n    key: \"read\",\n    value: function read(language, namespace, callback) {\n      this._readAny([language], language, [namespace], namespace, callback);\n    }\n  }, {\n    key: \"_readAny\",\n    value: function _readAny(languages, loadUrlLanguages, namespaces, loadUrlNamespaces, callback) {\n      var _this2 = this;\n      var loadPath = this.options.loadPath;\n      if (typeof this.options.loadPath === 'function') {\n        loadPath = this.options.loadPath(languages, namespaces);\n      }\n      loadPath = makePromise(loadPath);\n      loadPath.then(function (resolvedLoadPath) {\n        if (!resolvedLoadPath) return callback(null, {});\n        var url = _this2.services.interpolator.interpolate(resolvedLoadPath, {\n          lng: languages.join('+'),\n          ns: namespaces.join('+')\n        });\n        _this2.loadUrl(url, callback, loadUrlLanguages, loadUrlNamespaces);\n      });\n    }\n  }, {\n    key: \"loadUrl\",\n    value: function loadUrl(url, callback, languages, namespaces) {\n      var _this3 = this;\n      var lng = typeof languages === 'string' ? [languages] : languages;\n      var ns = typeof namespaces === 'string' ? [namespaces] : namespaces;\n      var payload = this.options.parseLoadPayload(lng, ns);\n      this.options.request(this.options, url, payload, function (err, res) {\n        if (res && (res.status >= 500 && res.status < 600 || !res.status)) return callback('failed loading ' + url + '; status code: ' + res.status, true);\n        if (res && res.status >= 400 && res.status < 500) return callback('failed loading ' + url + '; status code: ' + res.status, false);\n        if (!res && err && err.message) {\n          var errorMessage = err.message.toLowerCase();\n          var isNetworkError = ['failed', 'fetch', 'network', 'load'].find(function (term) {\n            return errorMessage.indexOf(term) > -1;\n          });\n          if (isNetworkError) {\n            return callback('failed loading ' + url + ': ' + err.message, true);\n          }\n        }\n        if (err) return callback(err, false);\n        var ret, parseErr;\n        try {\n          if (typeof res.data === 'string') {\n            ret = _this3.options.parse(res.data, languages, namespaces);\n          } else {\n            ret = res.data;\n          }\n        } catch (e) {\n          parseErr = 'failed parsing ' + url + ' to json';\n        }\n        if (parseErr) return callback(parseErr, false);\n        callback(null, ret);\n      });\n    }\n  }, {\n    key: \"create\",\n    value: function create(languages, namespace, key, fallbackValue, callback) {\n      var _this4 = this;\n      if (!this.options.addPath) return;\n      if (typeof languages === 'string') languages = [languages];\n      var payload = this.options.parsePayload(namespace, key, fallbackValue);\n      var finished = 0;\n      var dataArray = [];\n      var resArray = [];\n      languages.forEach(function (lng) {\n        var addPath = _this4.options.addPath;\n        if (typeof _this4.options.addPath === 'function') {\n          addPath = _this4.options.addPath(lng, namespace);\n        }\n        var url = _this4.services.interpolator.interpolate(addPath, {\n          lng: lng,\n          ns: namespace\n        });\n        _this4.options.request(_this4.options, url, payload, function (data, res) {\n          finished += 1;\n          dataArray.push(data);\n          resArray.push(res);\n          if (finished === languages.length) {\n            if (typeof callback === 'function') callback(dataArray, resArray);\n          }\n        });\n      });\n    }\n  }, {\n    key: \"reload\",\n    value: function reload() {\n      var _this5 = this;\n      var _this$services = this.services,\n        backendConnector = _this$services.backendConnector,\n        languageUtils = _this$services.languageUtils,\n        logger = _this$services.logger;\n      var currentLanguage = backendConnector.language;\n      if (currentLanguage && currentLanguage.toLowerCase() === 'cimode') return;\n      var toLoad = [];\n      var append = function append(lng) {\n        var lngs = languageUtils.toResolveHierarchy(lng);\n        lngs.forEach(function (l) {\n          if (toLoad.indexOf(l) < 0) toLoad.push(l);\n        });\n      };\n      append(currentLanguage);\n      if (this.allOptions.preload) this.allOptions.preload.forEach(function (l) {\n        return append(l);\n      });\n      toLoad.forEach(function (lng) {\n        _this5.allOptions.ns.forEach(function (ns) {\n          backendConnector.read(lng, ns, 'read', null, null, function (err, data) {\n            if (err) logger.warn(\"loading namespace \".concat(ns, \" for language \").concat(lng, \" failed\"), err);\n            if (!err && data) logger.log(\"loaded namespace \".concat(ns, \" for language \").concat(lng), data);\n            backendConnector.loaded(\"\".concat(lng, \"|\").concat(ns), err, data);\n          });\n        });\n      });\n    }\n  }]);\n}();\nBackend.type = 'backend';\nexport default Backend;"], "mappings": "AAAA,SAASA,OAAOA,CAACC,CAAC,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,CAAC,EAAE;IAAE,OAAO,OAAOA,CAAC;EAAE,CAAC,GAAG,UAAUA,CAAC,EAAE;IAAE,OAAOA,CAAC,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,CAAC,CAACG,WAAW,KAAKF,MAAM,IAAID,CAAC,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,CAAC;EAAE,CAAC,EAAED,OAAO,CAACC,CAAC,CAAC;AAAE;AAC7T,SAASK,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAIC,CAAC,GAAGC,MAAM,CAACC,IAAI,CAACJ,CAAC,CAAC;EAAE,IAAIG,MAAM,CAACE,qBAAqB,EAAE;IAAE,IAAIX,CAAC,GAAGS,MAAM,CAACE,qBAAqB,CAACL,CAAC,CAAC;IAAEC,CAAC,KAAKP,CAAC,GAAGA,CAAC,CAACY,MAAM,CAAC,UAAUL,CAAC,EAAE;MAAE,OAAOE,MAAM,CAACI,wBAAwB,CAACP,CAAC,EAAEC,CAAC,CAAC,CAACO,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEN,CAAC,CAACO,IAAI,CAACC,KAAK,CAACR,CAAC,EAAER,CAAC,CAAC;EAAE;EAAE,OAAOQ,CAAC;AAAE;AAC9P,SAASS,aAAaA,CAACX,CAAC,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGW,SAAS,CAACC,MAAM,EAAEZ,CAAC,EAAE,EAAE;IAAE,IAAIC,CAAC,GAAG,IAAI,IAAIU,SAAS,CAACX,CAAC,CAAC,GAAGW,SAAS,CAACX,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGF,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACY,OAAO,CAAC,UAAUb,CAAC,EAAE;MAAEc,eAAe,CAACf,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAACD,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGE,MAAM,CAACa,yBAAyB,GAAGb,MAAM,CAACc,gBAAgB,CAACjB,CAAC,EAAEG,MAAM,CAACa,yBAAyB,CAACd,CAAC,CAAC,CAAC,GAAGH,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,CAAC,CAACY,OAAO,CAAC,UAAUb,CAAC,EAAE;MAAEE,MAAM,CAACe,cAAc,CAAClB,CAAC,EAAEC,CAAC,EAAEE,MAAM,CAACI,wBAAwB,CAACL,CAAC,EAAED,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AACtb,SAASmB,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAI,EAAED,CAAC,YAAYC,CAAC,CAAC,EAAE,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;AAAE;AAClH,SAASC,iBAAiBA,CAACvB,CAAC,EAAEC,CAAC,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,CAAC,CAACY,MAAM,EAAEX,CAAC,EAAE,EAAE;IAAE,IAAIR,CAAC,GAAGO,CAAC,CAACC,CAAC,CAAC;IAAER,CAAC,CAACc,UAAU,GAAGd,CAAC,CAACc,UAAU,IAAI,CAAC,CAAC,EAAEd,CAAC,CAAC8B,YAAY,GAAG,CAAC,CAAC,EAAE,OAAO,IAAI9B,CAAC,KAAKA,CAAC,CAAC+B,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAEtB,MAAM,CAACe,cAAc,CAAClB,CAAC,EAAE0B,cAAc,CAAChC,CAAC,CAACiC,GAAG,CAAC,EAAEjC,CAAC,CAAC;EAAE;AAAE;AACvO,SAASkC,YAAYA,CAAC5B,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAAE,OAAOD,CAAC,IAAIsB,iBAAiB,CAACvB,CAAC,CAACF,SAAS,EAAEG,CAAC,CAAC,EAAEC,CAAC,IAAIqB,iBAAiB,CAACvB,CAAC,EAAEE,CAAC,CAAC,EAAEC,MAAM,CAACe,cAAc,CAAClB,CAAC,EAAE,WAAW,EAAE;IAAEyB,QAAQ,EAAE,CAAC;EAAE,CAAC,CAAC,EAAEzB,CAAC;AAAE;AAC1K,SAASe,eAAeA,CAACf,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAAE,OAAO,CAACD,CAAC,GAAGyB,cAAc,CAACzB,CAAC,CAAC,KAAKD,CAAC,GAAGG,MAAM,CAACe,cAAc,CAAClB,CAAC,EAAEC,CAAC,EAAE;IAAE4B,KAAK,EAAE3B,CAAC;IAAEM,UAAU,EAAE,CAAC,CAAC;IAAEgB,YAAY,EAAE,CAAC,CAAC;IAAEC,QAAQ,EAAE,CAAC;EAAE,CAAC,CAAC,GAAGzB,CAAC,CAACC,CAAC,CAAC,GAAGC,CAAC,EAAEF,CAAC;AAAE;AACnL,SAAS0B,cAAcA,CAACxB,CAAC,EAAE;EAAE,IAAI4B,CAAC,GAAGC,YAAY,CAAC7B,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAIT,OAAO,CAACqC,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC5G,SAASC,YAAYA,CAAC7B,CAAC,EAAED,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAIR,OAAO,CAACS,CAAC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIF,CAAC,GAAGE,CAAC,CAACP,MAAM,CAACqC,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAKhC,CAAC,EAAE;IAAE,IAAI8B,CAAC,GAAG9B,CAAC,CAACiC,IAAI,CAAC/B,CAAC,EAAED,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAIR,OAAO,CAACqC,CAAC,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAIR,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAKrB,CAAC,GAAGiC,MAAM,GAAGC,MAAM,EAAEjC,CAAC,CAAC;AAAE;AAC3T,SAASkC,WAAW,QAAQ,YAAY;AACxC,OAAOC,OAAO,MAAM,cAAc;AAClC,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAAA,EAAG;EACvC,OAAO;IACLC,QAAQ,EAAE,8BAA8B;IACxCC,OAAO,EAAE,6BAA6B;IACtCC,KAAK,EAAE,SAASA,KAAKA,CAACC,IAAI,EAAE;MAC1B,OAAOC,IAAI,CAACF,KAAK,CAACC,IAAI,CAAC;IACzB,CAAC;IACDE,SAAS,EAAED,IAAI,CAACC,SAAS;IACzBC,YAAY,EAAE,SAASA,YAAYA,CAACC,SAAS,EAAEnB,GAAG,EAAEoB,aAAa,EAAE;MACjE,OAAOhC,eAAe,CAAC,CAAC,CAAC,EAAEY,GAAG,EAAEoB,aAAa,IAAI,EAAE,CAAC;IACtD,CAAC;IACDC,gBAAgB,EAAE,SAASA,gBAAgBA,CAACC,SAAS,EAAEC,UAAU,EAAE;MACjE,OAAOC,SAAS;IAClB,CAAC;IACDd,OAAO,EAAEA,OAAO;IAChBe,cAAc,EAAE,OAAOC,MAAM,KAAK,WAAW,GAAG,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;IACtEC,aAAa,EAAE,CAAC,CAAC;IACjBC,iBAAiB,EAAE,CAAC,CAAC;IACrBC,WAAW,EAAE,KAAK;IAClBC,eAAe,EAAE,KAAK;IACtBC,gBAAgB,EAAE,KAAK;IACvBC,cAAc,EAAE;MACdC,IAAI,EAAE,MAAM;MACZC,WAAW,EAAE,aAAa;MAC1BC,KAAK,EAAE;IACT;EACF,CAAC;AACH,CAAC;AACD,IAAIC,OAAO,GAAG,YAAY;EACxB,SAASA,OAAOA,CAACC,QAAQ,EAAE;IACzB,IAAIC,OAAO,GAAGrD,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKuC,SAAS,GAAGvC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACpF,IAAIsD,UAAU,GAAGtD,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKuC,SAAS,GAAGvC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACvFO,eAAe,CAAC,IAAI,EAAE4C,OAAO,CAAC;IAC9B,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,IAAI,GAAG,SAAS;IACrB,IAAI,CAACC,IAAI,CAACJ,QAAQ,EAAEC,OAAO,EAAEC,UAAU,CAAC;EAC1C;EACA,OAAOtC,YAAY,CAACmC,OAAO,EAAE,CAAC;IAC5BpC,GAAG,EAAE,MAAM;IACXE,KAAK,EAAE,SAASuC,IAAIA,CAACJ,QAAQ,EAAE;MAC7B,IAAIK,KAAK,GAAG,IAAI;MAChB,IAAIJ,OAAO,GAAGrD,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKuC,SAAS,GAAGvC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;MACpF,IAAIsD,UAAU,GAAGtD,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKuC,SAAS,GAAGvC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;MACvF,IAAI,CAACoD,QAAQ,GAAGA,QAAQ;MACxB,IAAI,CAACC,OAAO,GAAGtD,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE2B,WAAW,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC2B,OAAO,IAAI,CAAC,CAAC,CAAC,EAAEA,OAAO,CAAC;MAC1G,IAAI,CAACC,UAAU,GAAGA,UAAU;MAC5B,IAAI,IAAI,CAACF,QAAQ,IAAI,IAAI,CAACC,OAAO,CAACb,cAAc,EAAE;QAChD,IAAIkB,KAAK,GAAGC,WAAW,CAAC,YAAY;UAClC,OAAOF,KAAK,CAACG,MAAM,CAAC,CAAC;QACvB,CAAC,EAAE,IAAI,CAACP,OAAO,CAACb,cAAc,CAAC;QAC/B,IAAI3D,OAAO,CAAC6E,KAAK,CAAC,KAAK,QAAQ,IAAI,OAAOA,KAAK,CAACG,KAAK,KAAK,UAAU,EAAEH,KAAK,CAACG,KAAK,CAAC,CAAC;MACrF;IACF;EACF,CAAC,EAAE;IACD9C,GAAG,EAAE,WAAW;IAChBE,KAAK,EAAE,SAAS6C,SAASA,CAACzB,SAAS,EAAEC,UAAU,EAAEyB,QAAQ,EAAE;MACzD,IAAI,CAACC,QAAQ,CAAC3B,SAAS,EAAEA,SAAS,EAAEC,UAAU,EAAEA,UAAU,EAAEyB,QAAQ,CAAC;IACvE;EACF,CAAC,EAAE;IACDhD,GAAG,EAAE,MAAM;IACXE,KAAK,EAAE,SAASgD,IAAIA,CAACC,QAAQ,EAAEhC,SAAS,EAAE6B,QAAQ,EAAE;MAClD,IAAI,CAACC,QAAQ,CAAC,CAACE,QAAQ,CAAC,EAAEA,QAAQ,EAAE,CAAChC,SAAS,CAAC,EAAEA,SAAS,EAAE6B,QAAQ,CAAC;IACvE;EACF,CAAC,EAAE;IACDhD,GAAG,EAAE,UAAU;IACfE,KAAK,EAAE,SAAS+C,QAAQA,CAAC3B,SAAS,EAAE8B,gBAAgB,EAAE7B,UAAU,EAAE8B,iBAAiB,EAAEL,QAAQ,EAAE;MAC7F,IAAIM,MAAM,GAAG,IAAI;MACjB,IAAI1C,QAAQ,GAAG,IAAI,CAAC0B,OAAO,CAAC1B,QAAQ;MACpC,IAAI,OAAO,IAAI,CAAC0B,OAAO,CAAC1B,QAAQ,KAAK,UAAU,EAAE;QAC/CA,QAAQ,GAAG,IAAI,CAAC0B,OAAO,CAAC1B,QAAQ,CAACU,SAAS,EAAEC,UAAU,CAAC;MACzD;MACAX,QAAQ,GAAGH,WAAW,CAACG,QAAQ,CAAC;MAChCA,QAAQ,CAAC2C,IAAI,CAAC,UAAUC,gBAAgB,EAAE;QACxC,IAAI,CAACA,gBAAgB,EAAE,OAAOR,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QAChD,IAAIS,GAAG,GAAGH,MAAM,CAACjB,QAAQ,CAACqB,YAAY,CAACC,WAAW,CAACH,gBAAgB,EAAE;UACnEI,GAAG,EAAEtC,SAAS,CAACuC,IAAI,CAAC,GAAG,CAAC;UACxBC,EAAE,EAAEvC,UAAU,CAACsC,IAAI,CAAC,GAAG;QACzB,CAAC,CAAC;QACFP,MAAM,CAACS,OAAO,CAACN,GAAG,EAAET,QAAQ,EAAEI,gBAAgB,EAAEC,iBAAiB,CAAC;MACpE,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDrD,GAAG,EAAE,SAAS;IACdE,KAAK,EAAE,SAAS6D,OAAOA,CAACN,GAAG,EAAET,QAAQ,EAAE1B,SAAS,EAAEC,UAAU,EAAE;MAC5D,IAAIyC,MAAM,GAAG,IAAI;MACjB,IAAIJ,GAAG,GAAG,OAAOtC,SAAS,KAAK,QAAQ,GAAG,CAACA,SAAS,CAAC,GAAGA,SAAS;MACjE,IAAIwC,EAAE,GAAG,OAAOvC,UAAU,KAAK,QAAQ,GAAG,CAACA,UAAU,CAAC,GAAGA,UAAU;MACnE,IAAI0C,OAAO,GAAG,IAAI,CAAC3B,OAAO,CAACjB,gBAAgB,CAACuC,GAAG,EAAEE,EAAE,CAAC;MACpD,IAAI,CAACxB,OAAO,CAAC5B,OAAO,CAAC,IAAI,CAAC4B,OAAO,EAAEmB,GAAG,EAAEQ,OAAO,EAAE,UAAUC,GAAG,EAAEC,GAAG,EAAE;QACnE,IAAIA,GAAG,KAAKA,GAAG,CAACC,MAAM,IAAI,GAAG,IAAID,GAAG,CAACC,MAAM,GAAG,GAAG,IAAI,CAACD,GAAG,CAACC,MAAM,CAAC,EAAE,OAAOpB,QAAQ,CAAC,iBAAiB,GAAGS,GAAG,GAAG,iBAAiB,GAAGU,GAAG,CAACC,MAAM,EAAE,IAAI,CAAC;QAClJ,IAAID,GAAG,IAAIA,GAAG,CAACC,MAAM,IAAI,GAAG,IAAID,GAAG,CAACC,MAAM,GAAG,GAAG,EAAE,OAAOpB,QAAQ,CAAC,iBAAiB,GAAGS,GAAG,GAAG,iBAAiB,GAAGU,GAAG,CAACC,MAAM,EAAE,KAAK,CAAC;QAClI,IAAI,CAACD,GAAG,IAAID,GAAG,IAAIA,GAAG,CAACG,OAAO,EAAE;UAC9B,IAAIC,YAAY,GAAGJ,GAAG,CAACG,OAAO,CAACE,WAAW,CAAC,CAAC;UAC5C,IAAIC,cAAc,GAAG,CAAC,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,CAAC,CAACC,IAAI,CAAC,UAAUC,IAAI,EAAE;YAC/E,OAAOJ,YAAY,CAACK,OAAO,CAACD,IAAI,CAAC,GAAG,CAAC,CAAC;UACxC,CAAC,CAAC;UACF,IAAIF,cAAc,EAAE;YAClB,OAAOxB,QAAQ,CAAC,iBAAiB,GAAGS,GAAG,GAAG,IAAI,GAAGS,GAAG,CAACG,OAAO,EAAE,IAAI,CAAC;UACrE;QACF;QACA,IAAIH,GAAG,EAAE,OAAOlB,QAAQ,CAACkB,GAAG,EAAE,KAAK,CAAC;QACpC,IAAIU,GAAG,EAAEC,QAAQ;QACjB,IAAI;UACF,IAAI,OAAOV,GAAG,CAACpD,IAAI,KAAK,QAAQ,EAAE;YAChC6D,GAAG,GAAGZ,MAAM,CAAC1B,OAAO,CAACxB,KAAK,CAACqD,GAAG,CAACpD,IAAI,EAAEO,SAAS,EAAEC,UAAU,CAAC;UAC7D,CAAC,MAAM;YACLqD,GAAG,GAAGT,GAAG,CAACpD,IAAI;UAChB;QACF,CAAC,CAAC,OAAO1C,CAAC,EAAE;UACVwG,QAAQ,GAAG,iBAAiB,GAAGpB,GAAG,GAAG,UAAU;QACjD;QACA,IAAIoB,QAAQ,EAAE,OAAO7B,QAAQ,CAAC6B,QAAQ,EAAE,KAAK,CAAC;QAC9C7B,QAAQ,CAAC,IAAI,EAAE4B,GAAG,CAAC;MACrB,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACD5E,GAAG,EAAE,QAAQ;IACbE,KAAK,EAAE,SAAS4E,MAAMA,CAACxD,SAAS,EAAEH,SAAS,EAAEnB,GAAG,EAAEoB,aAAa,EAAE4B,QAAQ,EAAE;MACzE,IAAI+B,MAAM,GAAG,IAAI;MACjB,IAAI,CAAC,IAAI,CAACzC,OAAO,CAACzB,OAAO,EAAE;MAC3B,IAAI,OAAOS,SAAS,KAAK,QAAQ,EAAEA,SAAS,GAAG,CAACA,SAAS,CAAC;MAC1D,IAAI2C,OAAO,GAAG,IAAI,CAAC3B,OAAO,CAACpB,YAAY,CAACC,SAAS,EAAEnB,GAAG,EAAEoB,aAAa,CAAC;MACtE,IAAI4D,QAAQ,GAAG,CAAC;MAChB,IAAIC,SAAS,GAAG,EAAE;MAClB,IAAIC,QAAQ,GAAG,EAAE;MACjB5D,SAAS,CAACnC,OAAO,CAAC,UAAUyE,GAAG,EAAE;QAC/B,IAAI/C,OAAO,GAAGkE,MAAM,CAACzC,OAAO,CAACzB,OAAO;QACpC,IAAI,OAAOkE,MAAM,CAACzC,OAAO,CAACzB,OAAO,KAAK,UAAU,EAAE;UAChDA,OAAO,GAAGkE,MAAM,CAACzC,OAAO,CAACzB,OAAO,CAAC+C,GAAG,EAAEzC,SAAS,CAAC;QAClD;QACA,IAAIsC,GAAG,GAAGsB,MAAM,CAAC1C,QAAQ,CAACqB,YAAY,CAACC,WAAW,CAAC9C,OAAO,EAAE;UAC1D+C,GAAG,EAAEA,GAAG;UACRE,EAAE,EAAE3C;QACN,CAAC,CAAC;QACF4D,MAAM,CAACzC,OAAO,CAAC5B,OAAO,CAACqE,MAAM,CAACzC,OAAO,EAAEmB,GAAG,EAAEQ,OAAO,EAAE,UAAUlD,IAAI,EAAEoD,GAAG,EAAE;UACxEa,QAAQ,IAAI,CAAC;UACbC,SAAS,CAACnG,IAAI,CAACiC,IAAI,CAAC;UACpBmE,QAAQ,CAACpG,IAAI,CAACqF,GAAG,CAAC;UAClB,IAAIa,QAAQ,KAAK1D,SAAS,CAACpC,MAAM,EAAE;YACjC,IAAI,OAAO8D,QAAQ,KAAK,UAAU,EAAEA,QAAQ,CAACiC,SAAS,EAAEC,QAAQ,CAAC;UACnE;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDlF,GAAG,EAAE,QAAQ;IACbE,KAAK,EAAE,SAAS2C,MAAMA,CAAA,EAAG;MACvB,IAAIsC,MAAM,GAAG,IAAI;MACjB,IAAIC,cAAc,GAAG,IAAI,CAAC/C,QAAQ;QAChCgD,gBAAgB,GAAGD,cAAc,CAACC,gBAAgB;QAClDC,aAAa,GAAGF,cAAc,CAACE,aAAa;QAC5CC,MAAM,GAAGH,cAAc,CAACG,MAAM;MAChC,IAAIC,eAAe,GAAGH,gBAAgB,CAAClC,QAAQ;MAC/C,IAAIqC,eAAe,IAAIA,eAAe,CAACjB,WAAW,CAAC,CAAC,KAAK,QAAQ,EAAE;MACnE,IAAIkB,MAAM,GAAG,EAAE;MACf,IAAIC,MAAM,GAAG,SAASA,MAAMA,CAAC9B,GAAG,EAAE;QAChC,IAAI+B,IAAI,GAAGL,aAAa,CAACM,kBAAkB,CAAChC,GAAG,CAAC;QAChD+B,IAAI,CAACxG,OAAO,CAAC,UAAU0G,CAAC,EAAE;UACxB,IAAIJ,MAAM,CAACd,OAAO,CAACkB,CAAC,CAAC,GAAG,CAAC,EAAEJ,MAAM,CAAC3G,IAAI,CAAC+G,CAAC,CAAC;QAC3C,CAAC,CAAC;MACJ,CAAC;MACDH,MAAM,CAACF,eAAe,CAAC;MACvB,IAAI,IAAI,CAACjD,UAAU,CAACuD,OAAO,EAAE,IAAI,CAACvD,UAAU,CAACuD,OAAO,CAAC3G,OAAO,CAAC,UAAU0G,CAAC,EAAE;QACxE,OAAOH,MAAM,CAACG,CAAC,CAAC;MAClB,CAAC,CAAC;MACFJ,MAAM,CAACtG,OAAO,CAAC,UAAUyE,GAAG,EAAE;QAC5BuB,MAAM,CAAC5C,UAAU,CAACuB,EAAE,CAAC3E,OAAO,CAAC,UAAU2E,EAAE,EAAE;UACzCuB,gBAAgB,CAACnC,IAAI,CAACU,GAAG,EAAEE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,UAAUI,GAAG,EAAEnD,IAAI,EAAE;YACtE,IAAImD,GAAG,EAAEqB,MAAM,CAACQ,IAAI,CAAC,oBAAoB,CAACC,MAAM,CAAClC,EAAE,EAAE,gBAAgB,CAAC,CAACkC,MAAM,CAACpC,GAAG,EAAE,SAAS,CAAC,EAAEM,GAAG,CAAC;YACnG,IAAI,CAACA,GAAG,IAAInD,IAAI,EAAEwE,MAAM,CAACU,GAAG,CAAC,mBAAmB,CAACD,MAAM,CAAClC,EAAE,EAAE,gBAAgB,CAAC,CAACkC,MAAM,CAACpC,GAAG,CAAC,EAAE7C,IAAI,CAAC;YAChGsE,gBAAgB,CAACa,MAAM,CAAC,EAAE,CAACF,MAAM,CAACpC,GAAG,EAAE,GAAG,CAAC,CAACoC,MAAM,CAAClC,EAAE,CAAC,EAAEI,GAAG,EAAEnD,IAAI,CAAC;UACpE,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AACHqB,OAAO,CAACI,IAAI,GAAG,SAAS;AACxB,eAAeJ,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}