{"ast": null, "code": "import { combineReducers } from \"redux\";\nimport languageReducer from \"./languageReducer\";\n\n/** Combine all custom reducers as one.\r\n *  Note: We are able to have multiple reducers.\r\n */\nconst rootReducer = combineReducers({\n  /** Work for multiple language support. keep the selected language code crossing components and pages. */\n  language: languageReducer\n});\nexport default rootReducer;", "map": {"version": 3, "names": ["combineReducers", "languageReducer", "rootReducer", "language"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/redux/reducers/rootReducer.js"], "sourcesContent": ["import { combineReducers } from \"redux\";\r\nimport languageReducer from \"./languageReducer\";\r\n\r\n/** Combine all custom reducers as one.\r\n *  Note: We are able to have multiple reducers.\r\n */\r\nconst rootReducer = combineReducers({\r\n   /** Work for multiple language support. keep the selected language code crossing components and pages. */\r\n   language: languageReducer \r\n});\r\n\r\nexport default rootReducer;"], "mappings": "AAAA,SAASA,eAAe,QAAQ,OAAO;AACvC,OAAOC,eAAe,MAAM,mBAAmB;;AAE/C;AACA;AACA;AACA,MAAMC,WAAW,GAAGF,eAAe,CAAC;EACjC;EACAG,QAAQ,EAAEF;AACb,CAAC,CAAC;AAEF,eAAeC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}