{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function skipWhile(predicate) {\n  return operate(function (source, subscriber) {\n    var taking = false;\n    var index = 0;\n    source.subscribe(createOperatorSubscriber(subscriber, function (value) {\n      return (taking || (taking = !predicate(value, index++))) && subscriber.next(value);\n    }));\n  });\n}", "map": {"version": 3, "names": ["operate", "createOperatorSubscriber", "<PERSON><PERSON><PERSON><PERSON>", "predicate", "source", "subscriber", "taking", "index", "subscribe", "value", "next"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\skipWhile.ts"], "sourcesContent": ["import { Falsy, MonoTypeOperatorFunction, OperatorFunction } from '../types';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\n\nexport function skipWhile<T>(predicate: BooleanConstructor): OperatorFunction<T, Extract<T, Falsy> extends never ? never : T>;\nexport function skipWhile<T>(predicate: (value: T, index: number) => true): OperatorFunction<T, never>;\nexport function skipWhile<T>(predicate: (value: T, index: number) => boolean): MonoTypeOperatorFunction<T>;\n\n/**\n * Returns an Observable that skips all items emitted by the source Observable as long as a specified condition holds\n * true, but emits all further source items as soon as the condition becomes false.\n *\n * ![](skipWhile.png)\n *\n * Skips all the notifications with a truthy predicate. It will not skip the notifications when the predicate is falsy.\n * It can also be skipped using index. Once the predicate is true, it will not be called again.\n *\n * ## Example\n *\n * Skip some super heroes\n *\n * ```ts\n * import { from, skipWhile } from 'rxjs';\n *\n * const source = from(['Green Arrow', 'SuperMan', 'Flash', 'SuperGirl', 'Black Canary'])\n * // Skip the heroes until SuperGirl\n * const example = source.pipe(skipWhile(hero => hero !== 'SuperGirl'));\n * // output: SuperGirl, Black Canary\n * example.subscribe(femaleHero => console.log(femaleHero));\n * ```\n *\n * Skip values from the array until index 5\n *\n * ```ts\n * import { from, skipWhile } from 'rxjs';\n *\n * const source = from([1, 2, 3, 4, 5, 6, 7, 9, 10]);\n * const example = source.pipe(skipWhile((_, i) => i !== 5));\n * // output: 6, 7, 9, 10\n * example.subscribe(value => console.log(value));\n * ```\n *\n * @see {@link last}\n * @see {@link skip}\n * @see {@link skipUntil}\n * @see {@link skipLast}\n *\n * @param predicate A function to test each item emitted from the source Observable.\n * @return A function that returns an Observable that begins emitting items\n * emitted by the source Observable when the specified predicate becomes false.\n */\nexport function skipWhile<T>(predicate: (value: T, index: number) => boolean): MonoTypeOperatorFunction<T> {\n  return operate((source, subscriber) => {\n    let taking = false;\n    let index = 0;\n    source.subscribe(\n      createOperatorSubscriber(subscriber, (value) => (taking || (taking = !predicate(value, index++))) && subscriber.next(value))\n    );\n  });\n}\n"], "mappings": "AACA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAiD/D,OAAM,SAAUC,SAASA,CAAIC,SAA+C;EAC1E,OAAOH,OAAO,CAAC,UAACI,MAAM,EAAEC,UAAU;IAChC,IAAIC,MAAM,GAAG,KAAK;IAClB,IAAIC,KAAK,GAAG,CAAC;IACbH,MAAM,CAACI,SAAS,CACdP,wBAAwB,CAACI,UAAU,EAAE,UAACI,KAAK;MAAK,QAACH,MAAM,KAAKA,MAAM,GAAG,CAACH,SAAS,CAACM,KAAK,EAAEF,KAAK,EAAE,CAAC,CAAC,KAAKF,UAAU,CAACK,IAAI,CAACD,KAAK,CAAC;IAA3E,CAA2E,CAAC,CAC7H;EACH,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}