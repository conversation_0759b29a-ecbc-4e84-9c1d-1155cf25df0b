{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function pairwise() {\n  return operate(function (source, subscriber) {\n    var prev;\n    var hasPrev = false;\n    source.subscribe(createOperatorSubscriber(subscriber, function (value) {\n      var p = prev;\n      prev = value;\n      hasPrev && subscriber.next([p, value]);\n      hasPrev = true;\n    }));\n  });\n}", "map": {"version": 3, "names": ["operate", "createOperatorSubscriber", "pairwise", "source", "subscriber", "prev", "has<PERSON>rev", "subscribe", "value", "p", "next"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\pairwise.ts"], "sourcesContent": ["import { OperatorFunction } from '../types';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\n\n/**\n * Groups pairs of consecutive emissions together and emits them as an array of\n * two values.\n *\n * <span class=\"informal\">Puts the current value and previous value together as\n * an array, and emits that.</span>\n *\n * ![](pairwise.png)\n *\n * The Nth emission from the source Observable will cause the output Observable\n * to emit an array [(N-1)th, Nth] of the previous and the current value, as a\n * pair. For this reason, `pairwise` emits on the second and subsequent\n * emissions from the source Observable, but not on the first emission, because\n * there is no previous value in that case.\n *\n * ## Example\n *\n * On every click (starting from the second), emit the relative distance to the previous click\n *\n * ```ts\n * import { fromEvent, pairwise, map } from 'rxjs';\n *\n * const clicks = fromEvent<PointerEvent>(document, 'click');\n * const pairs = clicks.pipe(pairwise());\n * const distance = pairs.pipe(\n *   map(([first, second]) => {\n *     const x0 = first.clientX;\n *     const y0 = first.clientY;\n *     const x1 = second.clientX;\n *     const y1 = second.clientY;\n *     return Math.sqrt(Math.pow(x0 - x1, 2) + Math.pow(y0 - y1, 2));\n *   })\n * );\n *\n * distance.subscribe(x => console.log(x));\n * ```\n *\n * @see {@link buffer}\n * @see {@link bufferCount}\n *\n * @return A function that returns an Observable of pairs (as arrays) of\n * consecutive values from the source Observable.\n */\nexport function pairwise<T>(): OperatorFunction<T, [T, T]> {\n  return operate((source, subscriber) => {\n    let prev: T;\n    let hasPrev = false;\n    source.subscribe(\n      createOperatorSubscriber(subscriber, (value) => {\n        const p = prev;\n        prev = value;\n        hasPrev && subscriber.next([p, value]);\n        hasPrev = true;\n      })\n    );\n  });\n}\n"], "mappings": "AACA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AA6C/D,OAAM,SAAUC,QAAQA,CAAA;EACtB,OAAOF,OAAO,CAAC,UAACG,MAAM,EAAEC,UAAU;IAChC,IAAIC,IAAO;IACX,IAAIC,OAAO,GAAG,KAAK;IACnBH,MAAM,CAACI,SAAS,CACdN,wBAAwB,CAACG,UAAU,EAAE,UAACI,KAAK;MACzC,IAAMC,CAAC,GAAGJ,IAAI;MACdA,IAAI,GAAGG,KAAK;MACZF,OAAO,IAAIF,UAAU,CAACM,IAAI,CAAC,CAACD,CAAC,EAAED,KAAK,CAAC,CAAC;MACtCF,OAAO,GAAG,IAAI;IAChB,CAAC,CAAC,CACH;EACH,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}