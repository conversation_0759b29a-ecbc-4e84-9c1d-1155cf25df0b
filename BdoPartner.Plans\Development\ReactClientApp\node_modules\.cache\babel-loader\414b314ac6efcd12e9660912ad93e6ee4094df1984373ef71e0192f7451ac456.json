{"ast": null, "code": "var _jsxFileName = \"C:\\\\git\\\\Partner-Site\\\\BdoPartner.Plans\\\\Development\\\\ReactClientApp\\\\src\\\\pages\\\\myPartnerPlan.jsx\",\n  _s = $RefreshSig$();\nimport React from \"react\";\nimport { useSearchParams } from \"react-router-dom\";\nimport { PartnerPlanQuestionnaire } from \"../components/questionnaire/PartnerPlanQuestionnaire\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const MyPartnerPlan = () => {\n  _s();\n  const [searchParams] = useSearchParams();\n  const year = searchParams.get('year') || new Date().getFullYear().toString();\n  return /*#__PURE__*/_jsxDEV(PartnerPlanQuestionnaire, {\n    year: year\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 10\n  }, this);\n};\n_s(MyPartnerPlan, \"HWxNQEGJGSlsPJ3ubBB3081mtng=\", false, function () {\n  return [useSearchParams];\n});\n_c = MyPartnerPlan;\nexport default MyPartnerPlan;\nvar _c;\n$RefreshReg$(_c, \"MyPartnerPlan\");", "map": {"version": 3, "names": ["React", "useSearchParams", "PartnerPlanQuestionnaire", "jsxDEV", "_jsxDEV", "MyPartnerPlan", "_s", "searchParams", "year", "get", "Date", "getFullYear", "toString", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/pages/myPartnerPlan.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { useSearchParams } from \"react-router-dom\";\r\nimport { PartnerPlanQuestionnaire } from \"../components/questionnaire/PartnerPlanQuestionnaire\";\r\n\r\nexport const MyPartnerPlan = () => {\r\n  const [searchParams] = useSearchParams();\r\n  const year = searchParams.get('year') || new Date().getFullYear().toString();\r\n\r\n  return <PartnerPlanQuestionnaire year={year} />;\r\n};\r\n\r\nexport default MyPartnerPlan;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,eAAe,QAAQ,kBAAkB;AAClD,SAASC,wBAAwB,QAAQ,sDAAsD;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhG,OAAO,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAM,CAACC,YAAY,CAAC,GAAGN,eAAe,CAAC,CAAC;EACxC,MAAMO,IAAI,GAAGD,YAAY,CAACE,GAAG,CAAC,MAAM,CAAC,IAAI,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;EAE5E,oBAAOR,OAAA,CAACF,wBAAwB;IAACM,IAAI,EAAEA;EAAK;IAAAK,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AACjD,CAAC;AAACV,EAAA,CALWD,aAAa;EAAA,QACDJ,eAAe;AAAA;AAAAgB,EAAA,GAD3BZ,aAAa;AAO1B,eAAeA,aAAa;AAAC,IAAAY,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}