{"ast": null, "code": "var _jsxFileName = \"C:\\\\git\\\\Partner-Site\\\\BdoPartner.Plans\\\\Development\\\\ReactClientApp\\\\src\\\\pages\\\\admin\\\\partnerAnnualPlansReviewer.jsx\",\n  _s = $RefreshSig$();\nimport React from \"react\";\nimport { Card } from \"primereact/card\";\nimport { Button } from \"primereact/button\";\nimport { useSearchParams } from \"react-router-dom\";\nimport PartnerAnnualPlansTable from \"../../components/admin/PartnerAnnualPlansTable\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const PartnerAnnualPlansReviewer = () => {\n  _s();\n  const [searchParams] = useSearchParams();\n  const year = searchParams.get('year');\n  const handleBack = () => {\n    window.history.back();\n  };\n\n  // Set initial filters for reviewer mode\n  const initialFilters = {\n    year: year ? parseInt(year) : new Date().getFullYear()\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"banner\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"banner__site-title-area\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"page-title\",\n          children: \"My Reviews - Partner Annual Plans\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-content-wrapper\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"back-button-container\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          icon: \"pi pi-arrow-left\",\n          label: \"Back\",\n          className: \"p-button-outlined p-button-sm back-button\",\n          onClick: handleBack\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        children: /*#__PURE__*/_jsxDEV(PartnerAnnualPlansTable, {\n          onBack: handleBack,\n          initialPageSize: 10,\n          initialFilters: initialFilters,\n          reviewerMode: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 21,\n    columnNumber: 5\n  }, this);\n};\n_s(PartnerAnnualPlansReviewer, \"HWxNQEGJGSlsPJ3ubBB3081mtng=\", false, function () {\n  return [useSearchParams];\n});\n_c = PartnerAnnualPlansReviewer;\nexport default PartnerAnnualPlansReviewer;\nvar _c;\n$RefreshReg$(_c, \"PartnerAnnualPlansReviewer\");", "map": {"version": 3, "names": ["React", "Card", "<PERSON><PERSON>", "useSearchParams", "PartnerAnnualPlansTable", "jsxDEV", "_jsxDEV", "PartnerAnnualPlansReviewer", "_s", "searchParams", "year", "get", "handleBack", "window", "history", "back", "initialFilters", "parseInt", "Date", "getFullYear", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "icon", "label", "onClick", "onBack", "initialPageSize", "reviewerMode", "_c", "$RefreshReg$"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/pages/admin/partnerAnnualPlansReviewer.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Card } from \"primereact/card\";\r\nimport { Button } from \"primereact/button\";\r\nimport { useSearchParams } from \"react-router-dom\";\r\nimport PartnerAnnualPlansTable from \"../../components/admin/PartnerAnnualPlansTable\";\r\n\r\nexport const PartnerAnnualPlansReviewer = () => {\r\n  const [searchParams] = useSearchParams();\r\n  const year = searchParams.get('year');\r\n\r\n  const handleBack = () => {\r\n    window.history.back();\r\n  };\r\n\r\n  // Set initial filters for reviewer mode\r\n  const initialFilters = {\r\n    year: year ? parseInt(year) : new Date().getFullYear()\r\n  };\r\n\r\n  return (\r\n    <div>\r\n      <div className=\"banner\">\r\n        <div className=\"banner__site-title-area\">\r\n          <div className=\"page-title\">My Reviews - Partner Annual Plans</div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"page-content-wrapper\">\r\n        <div className=\"back-button-container\">\r\n          <Button\r\n            icon=\"pi pi-arrow-left\"\r\n            label=\"Back\"\r\n            className=\"p-button-outlined p-button-sm back-button\"\r\n            onClick={handleBack}\r\n          />\r\n        </div>\r\n\r\n        <Card>\r\n          <PartnerAnnualPlansTable\r\n            onBack={handleBack}\r\n            initialPageSize={10}\r\n            initialFilters={initialFilters}\r\n            reviewerMode={true}\r\n          />\r\n        </Card>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PartnerAnnualPlansReviewer;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,iBAAiB;AACtC,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,eAAe,QAAQ,kBAAkB;AAClD,OAAOC,uBAAuB,MAAM,gDAAgD;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErF,OAAO,MAAMC,0BAA0B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9C,MAAM,CAACC,YAAY,CAAC,GAAGN,eAAe,CAAC,CAAC;EACxC,MAAMO,IAAI,GAAGD,YAAY,CAACE,GAAG,CAAC,MAAM,CAAC;EAErC,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvBC,MAAM,CAACC,OAAO,CAACC,IAAI,CAAC,CAAC;EACvB,CAAC;;EAED;EACA,MAAMC,cAAc,GAAG;IACrBN,IAAI,EAAEA,IAAI,GAAGO,QAAQ,CAACP,IAAI,CAAC,GAAG,IAAIQ,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;EACvD,CAAC;EAED,oBACEb,OAAA;IAAAc,QAAA,gBACEd,OAAA;MAAKe,SAAS,EAAC,QAAQ;MAAAD,QAAA,eACrBd,OAAA;QAAKe,SAAS,EAAC,yBAAyB;QAAAD,QAAA,eACtCd,OAAA;UAAKe,SAAS,EAAC,YAAY;UAAAD,QAAA,EAAC;QAAiC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENnB,OAAA;MAAKe,SAAS,EAAC,sBAAsB;MAAAD,QAAA,gBACnCd,OAAA;QAAKe,SAAS,EAAC,uBAAuB;QAAAD,QAAA,eACpCd,OAAA,CAACJ,MAAM;UACLwB,IAAI,EAAC,kBAAkB;UACvBC,KAAK,EAAC,MAAM;UACZN,SAAS,EAAC,2CAA2C;UACrDO,OAAO,EAAEhB;QAAW;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENnB,OAAA,CAACL,IAAI;QAAAmB,QAAA,eACHd,OAAA,CAACF,uBAAuB;UACtByB,MAAM,EAAEjB,UAAW;UACnBkB,eAAe,EAAE,EAAG;UACpBd,cAAc,EAAEA,cAAe;UAC/Be,YAAY,EAAE;QAAK;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjB,EAAA,CA1CWD,0BAA0B;EAAA,QACdJ,eAAe;AAAA;AAAA6B,EAAA,GAD3BzB,0BAA0B;AA4CvC,eAAeA,0BAA0B;AAAC,IAAAyB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}