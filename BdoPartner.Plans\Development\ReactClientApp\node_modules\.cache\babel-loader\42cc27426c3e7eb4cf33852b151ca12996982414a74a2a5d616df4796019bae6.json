{"ast": null, "code": "import { createErrorClass } from './createErrorClass';\nexport var UnsubscriptionError = createErrorClass(function (_super) {\n  return function UnsubscriptionErrorImpl(errors) {\n    _super(this);\n    this.message = errors ? errors.length + \" errors occurred during unsubscription:\\n\" + errors.map(function (err, i) {\n      return i + 1 + \") \" + err.toString();\n    }).join('\\n  ') : '';\n    this.name = 'UnsubscriptionError';\n    this.errors = errors;\n  };\n});", "map": {"version": 3, "names": ["createErrorClass", "UnsubscriptionError", "_super", "UnsubscriptionErrorImpl", "errors", "message", "length", "map", "err", "i", "toString", "join", "name"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\util\\UnsubscriptionError.ts"], "sourcesContent": ["import { createErrorClass } from './createErrorClass';\n\nexport interface UnsubscriptionError extends Error {\n  readonly errors: any[];\n}\n\nexport interface UnsubscriptionErrorCtor {\n  /**\n   * @deprecated Internal implementation detail. Do not construct error instances.\n   * Cannot be tagged as internal: https://github.com/ReactiveX/rxjs/issues/6269\n   */\n  new (errors: any[]): UnsubscriptionError;\n}\n\n/**\n * An error thrown when one or more errors have occurred during the\n * `unsubscribe` of a {@link Subscription}.\n */\nexport const UnsubscriptionError: UnsubscriptionErrorCtor = createErrorClass(\n  (_super) =>\n    function UnsubscriptionErrorImpl(this: any, errors: (Error | string)[]) {\n      _super(this);\n      this.message = errors\n        ? `${errors.length} errors occurred during unsubscription:\n${errors.map((err, i) => `${i + 1}) ${err.toString()}`).join('\\n  ')}`\n        : '';\n      this.name = 'UnsubscriptionError';\n      this.errors = errors;\n    }\n);\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,oBAAoB;AAkBrD,OAAO,IAAMC,mBAAmB,GAA4BD,gBAAgB,CAC1E,UAACE,MAAM;EACL,gBAASC,uBAAuBA,CAAYC,MAA0B;IACpEF,MAAM,CAAC,IAAI,CAAC;IACZ,IAAI,CAACG,OAAO,GAAGD,MAAM,GACdA,MAAM,CAACE,MAAM,iDACxBF,MAAM,CAACG,GAAG,CAAC,UAACC,GAAG,EAAEC,CAAC;MAAK,OAAGA,CAAC,GAAG,CAAC,UAAKD,GAAG,CAACE,QAAQ,EAAI;IAA7B,CAA6B,CAAC,CAACC,IAAI,CAAC,MAAM,CAAG,GAC5D,EAAE;IACN,IAAI,CAACC,IAAI,GAAG,qBAAqB;IACjC,IAAI,CAACR,MAAM,GAAGA,MAAM;EACtB,CAAC;AARD,CAQC,CACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}