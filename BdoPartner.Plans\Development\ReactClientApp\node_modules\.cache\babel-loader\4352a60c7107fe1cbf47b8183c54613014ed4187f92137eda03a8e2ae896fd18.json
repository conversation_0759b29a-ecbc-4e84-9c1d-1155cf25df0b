{"ast": null, "code": "import { from } from './from';\nexport function pairs(obj, scheduler) {\n  return from(Object.entries(obj), scheduler);\n}", "map": {"version": 3, "names": ["from", "pairs", "obj", "scheduler", "Object", "entries"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\observable\\pairs.ts"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { SchedulerLike } from '../types';\nimport { from } from './from';\n\n/**\n * @deprecated Use `from(Object.entries(obj))` instead. Will be removed in v8.\n */\nexport function pairs<T>(arr: readonly T[], scheduler?: SchedulerLike): Observable<[string, T]>;\n/**\n * @deprecated Use `from(Object.entries(obj))` instead. Will be removed in v8.\n */\nexport function pairs<O extends Record<string, unknown>>(obj: O, scheduler?: SchedulerLike): Observable<[keyof O, O[keyof O]]>;\n/**\n * @deprecated Use `from(Object.entries(obj))` instead. Will be removed in v8.\n */\nexport function pairs<T>(iterable: Iterable<T>, scheduler?: SchedulerLike): Observable<[string, T]>;\n/**\n * @deprecated Use `from(Object.entries(obj))` instead. Will be removed in v8.\n */\nexport function pairs(\n  n: number | bigint | boolean | ((...args: any[]) => any) | symbol,\n  scheduler?: SchedulerLike\n): Observable<[never, never]>;\n\n/**\n * Convert an object into an Observable of `[key, value]` pairs.\n *\n * <span class=\"informal\">Turn entries of an object into a stream.</span>\n *\n * ![](pairs.png)\n *\n * `pairs` takes an arbitrary object and returns an Observable that emits arrays. Each\n * emitted array has exactly two elements - the first is a key from the object\n * and the second is a value corresponding to that key. Keys are extracted from\n * an object via `Object.keys` function, which means that they will be only\n * enumerable keys that are present on an object directly - not ones inherited\n * via prototype chain.\n *\n * By default, these arrays are emitted synchronously. To change that you can\n * pass a {@link SchedulerLike} as a second argument to `pairs`.\n *\n * ## Example\n *\n * Converts an object to an Observable\n *\n * ```ts\n * import { pairs } from 'rxjs';\n *\n * const obj = {\n *   foo: 42,\n *   bar: 56,\n *   baz: 78\n * };\n *\n * pairs(obj).subscribe({\n *   next: value => console.log(value),\n *   complete: () => console.log('Complete!')\n * });\n *\n * // Logs:\n * // ['foo', 42]\n * // ['bar', 56]\n * // ['baz', 78]\n * // 'Complete!'\n * ```\n *\n * ### Object.entries required\n *\n * In IE, you will need to polyfill `Object.entries` in order to use this.\n * [MDN has a polyfill here](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/entries)\n *\n * @param obj The object to inspect and turn into an Observable sequence.\n * @param scheduler An optional IScheduler to schedule when resulting\n * Observable will emit values.\n * @returns An observable sequence of [key, value] pairs from the object.\n * @deprecated Use `from(Object.entries(obj))` instead. Will be removed in v8.\n */\nexport function pairs(obj: any, scheduler?: SchedulerLike) {\n  return from(Object.entries(obj), scheduler as any);\n}\n"], "mappings": "AAEA,SAASA,IAAI,QAAQ,QAAQ;AA2E7B,OAAM,SAAUC,KAAKA,CAACC,GAAQ,EAAEC,SAAyB;EACvD,OAAOH,IAAI,CAACI,MAAM,CAACC,OAAO,CAACH,GAAG,CAAC,EAAEC,SAAgB,CAAC;AACpD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}