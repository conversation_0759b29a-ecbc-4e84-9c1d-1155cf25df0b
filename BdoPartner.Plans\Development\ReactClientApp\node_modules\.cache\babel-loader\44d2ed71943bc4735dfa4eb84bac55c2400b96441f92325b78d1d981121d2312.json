{"ast": null, "code": "var _jsxFileName = \"C:\\\\git\\\\Partner-Site\\\\BdoPartner.Plans\\\\Development\\\\ReactClientApp\\\\src\\\\components\\\\admin\\\\UploadPartnerReferenceData.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from \"react\";\nimport { Card } from \"primereact/card\";\nimport { Button } from \"primereact/button\";\nimport { FileUpload } from \"primereact/fileupload\";\nimport { DataTable } from \"primereact/datatable\";\nimport { Column } from \"primereact/column\";\nimport { Dialog } from \"primereact/dialog\";\nimport { Dropdown } from \"primereact/dropdown\";\nimport { InputText } from \"primereact/inputtext\";\nimport { Paginator } from \"primereact/paginator\";\nimport { Toast } from \"primereact/toast\";\nimport { ConfirmDialog, confirmDialog } from \"primereact/confirmdialog\";\nimport { Badge } from \"primereact/badge\";\nimport { Tooltip } from \"primereact/tooltip\";\nimport partnerReferenceDataUploadService from \"../../services/partnerReferenceDataUploadService\";\nimport { messageService } from \"../../core/message/messageService\";\nimport { PartnerReferenceDataUploadStatus } from \"../../core/enumertions/partnerReferenceDataUploadStatus\";\nimport { PartnerPlanCycle, getCycleOptions, getCycleDisplayName } from \"../../core/enumertions/partnerPlanCycle\";\nimport { useLoadingControl } from \"../../core/loading/hooks/useLoadingControl\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport const UploadPartnerReferenceData = () => {\n  _s();\n  const [uploads, setUploads] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [totalRecords, setTotalRecords] = useState(0);\n  const [first, setFirst] = useState(0);\n  const [rows, setRows] = useState(10);\n  const [globalFilter, setGlobalFilter] = useState(\"\");\n\n  // Upload dialog state\n  const [showUploadDialog, setShowUploadDialog] = useState(false);\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());\n  const [selectedCycle, setSelectedCycle] = useState(PartnerPlanCycle.Planning);\n  const [uploading, setUploading] = useState(false);\n\n  // Details dialog state\n  const [showDetailsDialog, setShowDetailsDialog] = useState(false);\n  const [selectedUpload, setSelectedUpload] = useState(null);\n  const [uploadDetails, setUploadDetails] = useState([]);\n  const [metaDetails, setMetaDetails] = useState([]);\n  const [detailsLoading, setDetailsLoading] = useState(false);\n  const [detailsFilter, setDetailsFilter] = useState('all'); // 'all', 'valid', 'invalid'\n\n  const toast = useRef(null);\n  const fileUploadRef = useRef(null);\n\n  // Year options for the dropdown\n  const currentYear = new Date().getFullYear();\n  const yearOptions = [];\n  for (let i = currentYear - 1; i <= currentYear + 5; i++) {\n    yearOptions.push({\n      label: i.toString(),\n      value: i\n    });\n  }\n\n  // Cycle options\n  const cycleOptions = getCycleOptions();\n\n  // Disable loading interceptor for survey component\n  useLoadingControl('survey', true);\n  useEffect(() => {\n    loadUploads();\n  }, [first, rows]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  const loadUploads = async () => {\n    setLoading(true);\n    try {\n      const pageIndex = Math.floor(first / rows);\n      const result = await partnerReferenceDataUploadService.searchPartnerReferenceDataUploads(null,\n      // year filter\n      null,\n      // status filter\n      pageIndex, rows);\n      setUploads(result.items || []);\n      setTotalRecords(result.totalCount || 0);\n    } catch (error) {\n      messageService.errorToast(\"Failed to load uploads\");\n    } finally {\n      setLoading(false);\n    }\n  };\n  const onPageChange = event => {\n    setFirst(event.first);\n    setRows(event.rows);\n  };\n  const handleFileSelect = event => {\n    const file = event.files[0];\n    if (file) {\n      // Validate file type\n      const allowedTypes = ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\n      // .xlsx\n      'application/vnd.ms-excel',\n      // .xls\n      'text/csv' // .csv\n      ];\n      if (!allowedTypes.includes(file.type)) {\n        messageService.errorToast(\"Only Excel (.xlsx, .xls) and CSV files are allowed\");\n        fileUploadRef.current.clear();\n        return;\n      }\n      setSelectedFile(file);\n      setShowUploadDialog(true);\n    }\n  };\n  const handleUpload = async () => {\n    if (!selectedFile) {\n      messageService.warnToast(\"Please select a file\");\n      return;\n    }\n    setUploading(true);\n    try {\n      await partnerReferenceDataUploadService.uploadFile(selectedFile, selectedYear, selectedCycle);\n      messageService.successToast(\"File uploaded successfully\");\n      setShowUploadDialog(false);\n      setSelectedFile(null);\n      setSelectedYear(new Date().getFullYear());\n      setSelectedCycle(PartnerPlanCycle.Planning);\n      fileUploadRef.current.clear();\n      loadUploads();\n    } catch (error) {\n      messageService.errorToast(error.message || \"Upload failed\");\n    } finally {\n      setUploading(false);\n    }\n  };\n  const handleDownloadTemplate = async () => {\n    try {\n      const blob = await partnerReferenceDataUploadService.getUploadTemplate();\n      const url = window.URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = 'PartnerReferenceDataUploadTemplate.xlsx';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      window.URL.revokeObjectURL(url);\n    } catch (error) {\n      messageService.errorToast(\"Failed to download template\");\n    }\n  };\n  const handleViewDetails = async upload => {\n    setSelectedUpload(upload);\n    setDetailsLoading(true);\n    setShowDetailsDialog(true);\n    setDetailsFilter('all'); // Reset filter to \"Show All\" when opening dialog\n\n    await loadUploadDetails(upload.id, 'all');\n  };\n  const loadUploadDetails = async (uploadId, filter) => {\n    setDetailsLoading(true);\n    try {\n      let includeValidOnly = false;\n      let includeInvalidOnly = false;\n      if (filter === 'valid') {\n        includeValidOnly = true;\n      } else if (filter === 'invalid') {\n        includeInvalidOnly = true;\n      }\n      const result = await partnerReferenceDataUploadService.getPartnerReferenceDataUploadDetails(uploadId, includeValidOnly, includeInvalidOnly);\n\n      // Handle the new structure with uploadDetails and metaDetails\n      if (result && result.uploadDetails && result.metaDetails) {\n        setUploadDetails(result.uploadDetails);\n        setMetaDetails(result.metaDetails);\n      } else {\n        // Fallback for backward compatibility\n        setUploadDetails(result || []);\n        setMetaDetails([]);\n      }\n    } catch (error) {\n      messageService.errorToast(\"Failed to load upload details\");\n    } finally {\n      setDetailsLoading(false);\n    }\n  };\n  const handleDetailsFilterChange = e => {\n    const newFilter = e.value;\n    setDetailsFilter(newFilter);\n    if (selectedUpload) {\n      loadUploadDetails(selectedUpload.id, newFilter);\n    }\n  };\n\n  // Filter options for the details dialog\n  const filterOptions = [{\n    label: 'Show All',\n    value: 'all'\n  }, {\n    label: 'Only Valid',\n    value: 'valid'\n  }, {\n    label: 'Only Invalid',\n    value: 'invalid'\n  }];\n  const handleSubmit = async uploadId => {\n    confirmDialog({\n      message: 'Are you sure you want to submit this upload? This will update the partner reference data.',\n      header: 'Confirm Submit',\n      icon: 'pi pi-exclamation-triangle',\n      accept: async () => {\n        try {\n          await partnerReferenceDataUploadService.submitUpload(uploadId);\n          messageService.successToast(\"Upload submitted successfully\");\n          loadUploads();\n        } catch (error) {\n          messageService.errorToast(error.message || \"Submit failed\");\n        }\n      }\n    });\n  };\n  const handleRetry = async uploadId => {\n    try {\n      await partnerReferenceDataUploadService.validateUpload(uploadId);\n      messageService.successToast(\"Validation retried successfully\");\n      loadUploads();\n    } catch (error) {\n      messageService.errorToast(error.message || \"Retry failed\");\n    }\n  };\n  const handleDelete = async (uploadId, fileName) => {\n    confirmDialog({\n      message: `Are you sure you want to delete the upload \"${fileName}\"? This action cannot be undone and will remove all associated data.`,\n      header: 'Confirm Delete',\n      icon: 'pi pi-exclamation-triangle',\n      acceptClassName: 'p-button-danger',\n      accept: async () => {\n        try {\n          await partnerReferenceDataUploadService.deleteUpload(uploadId);\n          messageService.successToast(\"Upload deleted successfully\");\n          loadUploads();\n        } catch (error) {\n          messageService.errorToast(error.message || \"Delete failed\");\n        }\n      }\n    });\n  };\n\n  // Column renderers\n  const statusBodyTemplate = rowData => {\n    const statusMap = {\n      [PartnerReferenceDataUploadStatus.Uploading]: {\n        label: 'Uploading',\n        severity: 'info'\n      },\n      [PartnerReferenceDataUploadStatus.Uploaded]: {\n        label: 'Uploaded',\n        severity: 'warning'\n      },\n      [PartnerReferenceDataUploadStatus.Validating]: {\n        label: 'Validating',\n        severity: 'info'\n      },\n      [PartnerReferenceDataUploadStatus.ValidationPassed]: {\n        label: 'Validation Passed',\n        severity: 'success'\n      },\n      [PartnerReferenceDataUploadStatus.ValidationFailed]: {\n        label: 'Validation Failed',\n        severity: 'danger'\n      },\n      [PartnerReferenceDataUploadStatus.Submitted]: {\n        label: 'Submitted',\n        severity: 'success'\n      }\n    };\n    const status = statusMap[rowData.status] || {\n      label: 'Unknown',\n      severity: 'secondary'\n    };\n    return /*#__PURE__*/_jsxDEV(Badge, {\n      value: status.label,\n      severity: status.severity\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 264,\n      columnNumber: 12\n    }, this);\n  };\n  const cycleBodyTemplate = rowData => {\n    return getCycleDisplayName(rowData.cycle);\n  };\n  const validationSummaryBodyTemplate = rowData => {\n    if (!rowData.validationSummary) return null;\n    const truncated = rowData.validationSummary.length > 50 ? rowData.validationSummary.substring(0, 50) + '...' : rowData.validationSummary;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        children: truncated\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 9\n      }, this), rowData.validationSummary.length > 50 && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          icon: \"pi pi-info-circle\",\n          className: \"p-button-text p-button-sm\",\n          tooltip: \"View full validation summary\",\n          onClick: () => {\n            messageService.infoDialog(rowData.validationSummary);\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 13\n        }, this)\n      }, void 0, false)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 279,\n      columnNumber: 7\n    }, this);\n  };\n  const actionBodyTemplate = rowData => {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-d-flex p-ai-center\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        icon: \"pi pi-eye\",\n        className: \"p-button-text p-button-sm p-mr-2\",\n        tooltip: \"View Details\",\n        onClick: () => handleViewDetails(rowData)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 300,\n        columnNumber: 9\n      }, this), rowData.status === PartnerReferenceDataUploadStatus.ValidationPassed && /*#__PURE__*/_jsxDEV(Button, {\n        icon: \"pi pi-check\",\n        className: \"p-button-text p-button-success p-button-sm p-mr-2\",\n        tooltip: \"Submit\",\n        onClick: () => handleSubmit(rowData.id)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 11\n      }, this), (rowData.status === PartnerReferenceDataUploadStatus.ValidationFailed || rowData.status === PartnerReferenceDataUploadStatus.Uploaded || rowData.status === PartnerReferenceDataUploadStatus.Uploading || rowData.status === PartnerReferenceDataUploadStatus.Validating) && /*#__PURE__*/_jsxDEV(Button, {\n        icon: \"pi pi-refresh\",\n        className: \"p-button-text p-button-warning p-button-sm p-mr-2\",\n        tooltip: \"Retry\",\n        onClick: () => handleRetry(rowData.id)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 320,\n        columnNumber: 11\n      }, this), rowData.status === PartnerReferenceDataUploadStatus.ValidationFailed && /*#__PURE__*/_jsxDEV(Button, {\n        icon: \"pi pi-trash\",\n        className: \"p-button-text p-button-danger p-button-sm\",\n        tooltip: \"Delete Upload\",\n        onClick: () => handleDelete(rowData.id, rowData.uploadFileName)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 329,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 299,\n      columnNumber: 7\n    }, this);\n  };\n  const dateBodyTemplate = (rowData, field) => {\n    if (!rowData[field.field]) return null;\n    return new Date(rowData[field.field]).toLocaleString();\n  };\n  const rowIdBodyTemplate = rowData => {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        fontWeight: 'bold',\n        color: '#6366f1'\n      },\n      children: rowData.rowId\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 347,\n      columnNumber: 7\n    }, this);\n  };\n  const rowIdHeaderTemplate = () => {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [\"Row ID\", /*#__PURE__*/_jsxDEV(Tooltip, {\n        target: \".row-id-header\",\n        content: \"The row number from the original uploaded file\",\n        position: \"top\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 357,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"pi pi-info-circle row-id-header p-ml-1\",\n        style: {\n          fontSize: '0.8rem',\n          color: '#6c757d'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 358,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 355,\n      columnNumber: 7\n    }, this);\n  };\n  const header = /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"upload-history-header\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"section-header\",\n      children: \"Upload History\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 365,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"upload-history-actions\",\n      children: [/*#__PURE__*/_jsxDEV(InputText, {\n        type: \"search\",\n        onInput: e => setGlobalFilter(e.target.value),\n        placeholder: \"Search uploads...\",\n        className: \"upload-search-input\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 367,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        icon: \"pi pi-download\",\n        label: \"Download Template\",\n        className: \"p-button-red\",\n        rounded: true,\n        onClick: handleDownloadTemplate\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 373,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 366,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 364,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"upload-partner-reviewer\",\n    children: [/*#__PURE__*/_jsxDEV(Toast, {\n      ref: toast\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 386,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ConfirmDialog, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 387,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"p-mb-4 upload-section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-card-content\",\n        children: [/*#__PURE__*/_jsxDEV(FileUpload, {\n          ref: fileUploadRef,\n          mode: \"basic\",\n          name: \"file\",\n          accept: \".xlsx,.xls,.csv\",\n          maxFileSize: 10000000 // 10MB\n          ,\n          onSelect: handleFileSelect,\n          chooseLabel: \"Select File\",\n          className: \"p-mr-2\",\n          auto: false\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 395,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n          className: \"p-d-block p-mt-2\",\n          children: \"Supported formats: Excel (.xlsx, .xls) and CSV files. Maximum size: 10MB\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 406,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 394,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 390,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"upload-history\",\n      children: [/*#__PURE__*/_jsxDEV(DataTable, {\n        value: uploads,\n        loading: loading,\n        header: header,\n        globalFilter: globalFilter,\n        emptyMessage: \"No uploads found\",\n        scrollable: true,\n        children: [/*#__PURE__*/_jsxDEV(Column, {\n          field: \"uploadFileName\",\n          header: \"File Name\",\n          sortable: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 422,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Column, {\n          field: \"year\",\n          header: \"Year\",\n          sortable: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 423,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Column, {\n          field: \"cycle\",\n          header: \"Cycle\",\n          sortable: true,\n          body: cycleBodyTemplate\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 424,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Column, {\n          field: \"modifiedByName\",\n          header: \"Updated By\",\n          sortable: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 425,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Column, {\n          field: \"modifiedOn\",\n          header: \"Updated On\",\n          sortable: true,\n          body: rowData => dateBodyTemplate(rowData, {\n            field: 'modifiedOn'\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 426,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Column, {\n          field: \"validationSummary\",\n          header: \"Validation Summary\",\n          body: validationSummaryBodyTemplate\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 432,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Column, {\n          field: \"status\",\n          header: \"Status\",\n          sortable: true,\n          body: statusBodyTemplate\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 437,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Column, {\n          header: \"Actions\",\n          body: actionBodyTemplate,\n          style: {\n            width: '150px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 443,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 414,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paginator, {\n        first: first,\n        rows: rows,\n        totalRecords: totalRecords,\n        rowsPerPageOptions: [10, 20, 50],\n        onPageChange: onPageChange,\n        className: \"p-mt-3\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 450,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 413,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      header: \"Select Year and Cycle for Upload\",\n      visible: showUploadDialog,\n      style: {\n        width: '450px'\n      },\n      modal: true,\n      onHide: () => {\n        var _fileUploadRef$curren;\n        setShowUploadDialog(false);\n        setSelectedFile(null);\n        setSelectedYear(new Date().getFullYear());\n        setSelectedCycle(PartnerPlanCycle.Planning);\n        (_fileUploadRef$curren = fileUploadRef.current) === null || _fileUploadRef$curren === void 0 ? void 0 : _fileUploadRef$curren.clear();\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-fluid\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-field p-mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"selectedFile\",\n            children: \"Selected File:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 476,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-mt-2\",\n            children: /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: selectedFile === null || selectedFile === void 0 ? void 0 : selectedFile.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 478,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 477,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 475,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-field p-mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"year\",\n            children: \"Year:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 483,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n            id: \"year\",\n            value: selectedYear,\n            options: yearOptions,\n            onChange: e => setSelectedYear(e.value),\n            placeholder: \"Select year\",\n            className: \"p-mt-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 484,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 482,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-field p-mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"cycle\",\n            children: \"Cycle:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 495,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n            id: \"cycle\",\n            value: selectedCycle,\n            options: cycleOptions,\n            onChange: e => setSelectedCycle(e.value),\n            placeholder: \"Select cycle\",\n            className: \"p-mt-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 496,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 494,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'flex-end',\n            alignItems: 'center',\n            gap: '12px',\n            marginTop: '20px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            label: \"Cancel\",\n            icon: \"pi pi-times\",\n            className: \"p-button-text\",\n            onClick: () => {\n              var _fileUploadRef$curren2;\n              setShowUploadDialog(false);\n              setSelectedFile(null);\n              setSelectedYear(new Date().getFullYear());\n              setSelectedCycle(PartnerPlanCycle.Planning);\n              (_fileUploadRef$curren2 = fileUploadRef.current) === null || _fileUploadRef$curren2 === void 0 ? void 0 : _fileUploadRef$curren2.clear();\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 513,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            label: \"Upload\",\n            icon: \"pi pi-upload\",\n            className: \"p-button-red\",\n            loading: uploading,\n            onClick: handleUpload\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 525,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 506,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 474,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 461,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      header: `Upload Details - ${(selectedUpload === null || selectedUpload === void 0 ? void 0 : selectedUpload.uploadFileName) || ''}`,\n      visible: showDetailsDialog,\n      style: {\n        width: '80vw',\n        height: '80vh'\n      },\n      modal: true,\n      onHide: () => {\n        setShowDetailsDialog(false);\n        setSelectedUpload(null);\n        setUploadDetails([]);\n        setDetailsFilter('all');\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '12px',\n          marginBottom: '16px',\n          padding: '12px',\n          backgroundColor: '#f8f9fa',\n          borderRadius: '6px',\n          border: '1px solid #e9ecef'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"details-filter\",\n          style: {\n            fontWeight: '600',\n            color: '#495057',\n            minWidth: '60px'\n          },\n          children: \"Filter:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 560,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n          id: \"details-filter\",\n          value: detailsFilter,\n          options: filterOptions,\n          onChange: handleDetailsFilterChange,\n          placeholder: \"Select filter\",\n          style: {\n            minWidth: '150px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 567,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            color: '#6c757d',\n            fontSize: '0.9rem',\n            marginLeft: '8px'\n          },\n          children: [\"Showing \", uploadDetails.length, \" record(s)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 575,\n          columnNumber: 11\n        }, this), (selectedUpload === null || selectedUpload === void 0 ? void 0 : selectedUpload.validationSummary) && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginLeft: '16px',\n            padding: '8px 12px',\n            backgroundColor: '#fff3cd',\n            border: '1px solid #ffeaa7',\n            borderRadius: '4px',\n            fontSize: '0.9rem',\n            color: '#856404',\n            maxWidth: '400px',\n            overflow: 'hidden',\n            textOverflow: 'ellipsis',\n            whiteSpace: 'nowrap'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Validation Summary:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 596,\n            columnNumber: 15\n          }, this), \" \", selectedUpload.validationSummary]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 583,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 550,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DataTable, {\n        value: uploadDetails,\n        loading: detailsLoading,\n        scrollable: true,\n        scrollHeight: \"500px\",\n        emptyMessage: \"No details found\",\n        children: [/*#__PURE__*/_jsxDEV(Column, {\n          field: \"rowId\",\n          header: rowIdHeaderTemplate,\n          sortable: true,\n          style: {\n            width: '80px'\n          },\n          body: rowIdBodyTemplate\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 608,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Column, {\n          field: \"isValid\",\n          header: \"Valid\",\n          sortable: true,\n          style: {\n            width: '80px'\n          },\n          body: rowData => /*#__PURE__*/_jsxDEV(Badge, {\n            value: rowData.isValid ? 'Valid' : 'Invalid',\n            severity: rowData.isValid ? 'success' : 'danger'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 622,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 616,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Column, {\n          field: \"validationError\",\n          header: \"Validation Error\",\n          style: {\n            minWidth: '200px'\n          },\n          body: rowData => /*#__PURE__*/_jsxDEV(\"span\", {\n            title: rowData.validationError,\n            style: {\n              display: 'block',\n              maxWidth: '300px',\n              overflow: 'hidden',\n              textOverflow: 'ellipsis',\n              whiteSpace: 'nowrap'\n            },\n            children: rowData.validationError\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 633,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 628,\n          columnNumber: 11\n        }, this), metaDetails.sort((a, b) => a.columnOrder - b.columnOrder).map(metaDetail => /*#__PURE__*/_jsxDEV(Column, {\n          field: `columnData.${metaDetail.normalizedColumnName}`,\n          header: metaDetail.columnName,\n          sortable: true,\n          body: rowData => {\n            var _rowData$columnData;\n            const value = ((_rowData$columnData = rowData.columnData) === null || _rowData$columnData === void 0 ? void 0 : _rowData$columnData[metaDetail.normalizedColumnName]) || '';\n            return /*#__PURE__*/_jsxDEV(\"span\", {\n              title: value,\n              style: {\n                display: 'block',\n                maxWidth: '200px',\n                overflow: 'hidden',\n                textOverflow: 'ellipsis',\n                whiteSpace: 'nowrap'\n              },\n              children: value\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 656,\n              columnNumber: 21\n            }, this);\n          }\n        }, metaDetail.normalizedColumnName, false, {\n          fileName: _jsxFileName,\n          lineNumber: 648,\n          columnNumber: 15\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 601,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 537,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 385,\n    columnNumber: 5\n  }, this);\n};\n_s(UploadPartnerReferenceData, \"YWur5xV1wRDblj61k7Pq8V0nels=\", false, function () {\n  return [useLoadingControl];\n});\n_c = UploadPartnerReferenceData;\nvar _c;\n$RefreshReg$(_c, \"UploadPartnerReferenceData\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Card", "<PERSON><PERSON>", "FileUpload", "DataTable", "Column", "Dialog", "Dropdown", "InputText", "Paginator", "Toast", "ConfirmDialog", "confirmDialog", "Badge", "<PERSON><PERSON><PERSON>", "partnerReferenceDataUploadService", "messageService", "PartnerReferenceDataUploadStatus", "PartnerPlanCycle", "getCycleOptions", "getCycleDisplayName", "useLoadingControl", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "UploadPartnerReferenceData", "_s", "uploads", "setUploads", "loading", "setLoading", "totalRecords", "setTotalRecords", "first", "<PERSON><PERSON><PERSON><PERSON>", "rows", "setRows", "globalFilter", "setGlobalFilter", "showUploadDialog", "setShowUploadDialog", "selectedFile", "setSelectedFile", "selected<PERSON>ear", "setSelectedYear", "Date", "getFullYear", "selectedCycle", "setSelectedCycle", "Planning", "uploading", "setUploading", "showDetailsDialog", "setShowDetailsDialog", "selectedUpload", "setSelectedUpload", "uploadDetails", "setUploadDetails", "metaDetails", "setMetaDetails", "detailsLoading", "setDetailsLoading", "detailsFilter", "setDetailsFilter", "toast", "fileUploadRef", "currentYear", "yearOptions", "i", "push", "label", "toString", "value", "cycleOptions", "loadUploads", "pageIndex", "Math", "floor", "result", "searchPartnerReferenceDataUploads", "items", "totalCount", "error", "errorToast", "onPageChange", "event", "handleFileSelect", "file", "files", "allowedTypes", "includes", "type", "current", "clear", "handleUpload", "warnToast", "uploadFile", "successToast", "message", "handleDownloadTemplate", "blob", "getUploadTemplate", "url", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "handleViewDetails", "upload", "loadUploadDetails", "id", "uploadId", "filter", "includeValidOnly", "includeInvalidOnly", "getPartnerReferenceDataUploadDetails", "handleDetailsFilterChange", "e", "newFilter", "filterOptions", "handleSubmit", "header", "icon", "accept", "submitUpload", "handleRetry", "validateUpload", "handleDelete", "fileName", "acceptClassName", "deleteUpload", "statusBodyTemplate", "rowData", "statusMap", "Uploading", "severity", "Uploaded", "Validating", "ValidationPassed", "ValidationFailed", "Submitted", "status", "_jsxFileName", "lineNumber", "columnNumber", "cycleBodyTemplate", "cycle", "validationSummaryBodyTemplate", "validationSummary", "truncated", "length", "substring", "children", "className", "tooltip", "onClick", "infoDialog", "actionBodyTemplate", "uploadFileName", "dateBodyTemplate", "field", "toLocaleString", "rowIdBodyTemplate", "style", "textAlign", "fontWeight", "color", "rowId", "rowIdHeaderTemplate", "target", "content", "position", "fontSize", "onInput", "placeholder", "rounded", "ref", "mode", "name", "maxFileSize", "onSelect", "<PERSON><PERSON><PERSON><PERSON>", "auto", "emptyMessage", "scrollable", "sortable", "width", "rowsPerPageOptions", "visible", "modal", "onHide", "_fileUploadRef$curren", "htmlFor", "options", "onChange", "display", "justifyContent", "alignItems", "gap", "marginTop", "_fileUploadRef$curren2", "height", "marginBottom", "padding", "backgroundColor", "borderRadius", "border", "min<PERSON><PERSON><PERSON>", "marginLeft", "max<PERSON><PERSON><PERSON>", "overflow", "textOverflow", "whiteSpace", "scrollHeight", "<PERSON><PERSON><PERSON><PERSON>", "title", "validationError", "sort", "a", "b", "columnOrder", "map", "metaDetail", "normalizedColumnName", "columnName", "_rowData$columnData", "columnData", "_c", "$RefreshReg$"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/components/admin/UploadPartnerReferenceData.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from \"react\";\r\nimport { Card } from \"primereact/card\";\r\nimport { <PERSON><PERSON> } from \"primereact/button\";\r\nimport { FileUpload } from \"primereact/fileupload\";\r\nimport { DataTable } from \"primereact/datatable\";\r\nimport { Column } from \"primereact/column\";\r\nimport { Dialog } from \"primereact/dialog\";\r\nimport { Dropdown } from \"primereact/dropdown\";\r\nimport { InputText } from \"primereact/inputtext\";\r\nimport { Paginator } from \"primereact/paginator\";\r\nimport { Toast } from \"primereact/toast\";\r\nimport { ConfirmDialog, confirmDialog } from \"primereact/confirmdialog\";\r\nimport { Badge } from \"primereact/badge\";\r\nimport { Tooltip } from \"primereact/tooltip\";\r\nimport partnerReferenceDataUploadService from \"../../services/partnerReferenceDataUploadService\";\r\nimport { messageService } from \"../../core/message/messageService\";\r\nimport { PartnerReferenceDataUploadStatus } from \"../../core/enumertions/partnerReferenceDataUploadStatus\";\r\nimport { PartnerPlanCycle, getCycleOptions, getCycleDisplayName } from \"../../core/enumertions/partnerPlanCycle\";\r\nimport { useLoadingControl } from \"../../core/loading/hooks/useLoadingControl\";\r\n\r\nexport const UploadPartnerReferenceData = () => {\r\n  const [uploads, setUploads] = useState([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [totalRecords, setTotalRecords] = useState(0);\r\n  const [first, setFirst] = useState(0);\r\n  const [rows, setRows] = useState(10);\r\n  const [globalFilter, setGlobalFilter] = useState(\"\");\r\n\r\n  // Upload dialog state\r\n  const [showUploadDialog, setShowUploadDialog] = useState(false);\r\n  const [selectedFile, setSelectedFile] = useState(null);\r\n  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());\r\n  const [selectedCycle, setSelectedCycle] = useState(PartnerPlanCycle.Planning);\r\n  const [uploading, setUploading] = useState(false);\r\n\r\n  // Details dialog state\r\n  const [showDetailsDialog, setShowDetailsDialog] = useState(false);\r\n  const [selectedUpload, setSelectedUpload] = useState(null);\r\n  const [uploadDetails, setUploadDetails] = useState([]);\r\n  const [metaDetails, setMetaDetails] = useState([]);\r\n  const [detailsLoading, setDetailsLoading] = useState(false);\r\n  const [detailsFilter, setDetailsFilter] = useState('all'); // 'all', 'valid', 'invalid'\r\n\r\n  const toast = useRef(null);\r\n  const fileUploadRef = useRef(null);\r\n\r\n  // Year options for the dropdown\r\n  const currentYear = new Date().getFullYear();\r\n  const yearOptions = [];\r\n  for (let i = currentYear - 1; i <= currentYear + 5; i++) {\r\n    yearOptions.push({ label: i.toString(), value: i });\r\n  }\r\n\r\n  // Cycle options\r\n  const cycleOptions = getCycleOptions();\r\n\r\n  // Disable loading interceptor for survey component\r\n  useLoadingControl('survey', true);\r\n\r\n  useEffect(() => {\r\n    loadUploads();\r\n  }, [first, rows]); // eslint-disable-line react-hooks/exhaustive-deps\r\n\r\n  const loadUploads = async () => {\r\n    setLoading(true);\r\n    try {\r\n      const pageIndex = Math.floor(first / rows);\r\n      const result = await partnerReferenceDataUploadService.searchPartnerReferenceDataUploads(\r\n        null, // year filter\r\n        null, // status filter\r\n        pageIndex,\r\n        rows\r\n      );\r\n      setUploads(result.items || []);\r\n      setTotalRecords(result.totalCount || 0);\r\n    } catch (error) {\r\n      messageService.errorToast(\"Failed to load uploads\");\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const onPageChange = (event) => {\r\n    setFirst(event.first);\r\n    setRows(event.rows);\r\n  };\r\n\r\n  const handleFileSelect = (event) => {\r\n    const file = event.files[0];\r\n    if (file) {\r\n      // Validate file type\r\n      const allowedTypes = [\r\n        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx\r\n        'application/vnd.ms-excel', // .xls\r\n        'text/csv' // .csv\r\n      ];\r\n\r\n      if (!allowedTypes.includes(file.type)) {\r\n        messageService.errorToast(\"Only Excel (.xlsx, .xls) and CSV files are allowed\");\r\n        fileUploadRef.current.clear();\r\n        return;\r\n      }\r\n\r\n      setSelectedFile(file);\r\n      setShowUploadDialog(true);\r\n    }\r\n  };\r\n\r\n  const handleUpload = async () => {\r\n    if (!selectedFile) {\r\n      messageService.warnToast(\"Please select a file\");\r\n      return;\r\n    }\r\n\r\n    setUploading(true);\r\n    try {\r\n      await partnerReferenceDataUploadService.uploadFile(selectedFile, selectedYear, selectedCycle);\r\n\r\n      messageService.successToast(\"File uploaded successfully\");\r\n      setShowUploadDialog(false);\r\n      setSelectedFile(null);\r\n      setSelectedYear(new Date().getFullYear());\r\n      setSelectedCycle(PartnerPlanCycle.Planning);\r\n      fileUploadRef.current.clear();\r\n      loadUploads();\r\n    } catch (error) {\r\n      messageService.errorToast(error.message || \"Upload failed\");\r\n    } finally {\r\n      setUploading(false);\r\n    }\r\n  };\r\n\r\n  const handleDownloadTemplate = async () => {\r\n    try {\r\n      const blob = await partnerReferenceDataUploadService.getUploadTemplate();\r\n      const url = window.URL.createObjectURL(blob);\r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      link.download = 'PartnerReferenceDataUploadTemplate.xlsx';\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n      window.URL.revokeObjectURL(url);\r\n    } catch (error) {\r\n      messageService.errorToast(\"Failed to download template\");\r\n    }\r\n  };\r\n\r\n  const handleViewDetails = async (upload) => {\r\n    setSelectedUpload(upload);\r\n    setDetailsLoading(true);\r\n    setShowDetailsDialog(true);\r\n    setDetailsFilter('all'); // Reset filter to \"Show All\" when opening dialog\r\n\r\n    await loadUploadDetails(upload.id, 'all');\r\n  };\r\n\r\n  const loadUploadDetails = async (uploadId, filter) => {\r\n    setDetailsLoading(true);\r\n    try {\r\n      let includeValidOnly = false;\r\n      let includeInvalidOnly = false;\r\n\r\n      if (filter === 'valid') {\r\n        includeValidOnly = true;\r\n      } else if (filter === 'invalid') {\r\n        includeInvalidOnly = true;\r\n      }\r\n\r\n      const result = await partnerReferenceDataUploadService.getPartnerReferenceDataUploadDetails(\r\n        uploadId,\r\n        includeValidOnly,\r\n        includeInvalidOnly\r\n      );\r\n\r\n      // Handle the new structure with uploadDetails and metaDetails\r\n      if (result && result.uploadDetails && result.metaDetails) {\r\n        setUploadDetails(result.uploadDetails);\r\n        setMetaDetails(result.metaDetails);\r\n      } else {\r\n        // Fallback for backward compatibility\r\n        setUploadDetails(result || []);\r\n        setMetaDetails([]);\r\n      }\r\n    } catch (error) {\r\n      messageService.errorToast(\"Failed to load upload details\");\r\n    } finally {\r\n      setDetailsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleDetailsFilterChange = (e) => {\r\n    const newFilter = e.value;\r\n    setDetailsFilter(newFilter);\r\n    if (selectedUpload) {\r\n      loadUploadDetails(selectedUpload.id, newFilter);\r\n    }\r\n  };\r\n\r\n  // Filter options for the details dialog\r\n  const filterOptions = [\r\n    { label: 'Show All', value: 'all' },\r\n    { label: 'Only Valid', value: 'valid' },\r\n    { label: 'Only Invalid', value: 'invalid' }\r\n  ];\r\n\r\n  const handleSubmit = async (uploadId) => {\r\n    confirmDialog({\r\n      message: 'Are you sure you want to submit this upload? This will update the partner reference data.',\r\n      header: 'Confirm Submit',\r\n      icon: 'pi pi-exclamation-triangle',\r\n      accept: async () => {\r\n        try {\r\n          await partnerReferenceDataUploadService.submitUpload(uploadId);\r\n          messageService.successToast(\"Upload submitted successfully\");\r\n          loadUploads();\r\n        } catch (error) {\r\n          messageService.errorToast(error.message || \"Submit failed\");\r\n        }\r\n      }\r\n    });\r\n  };\r\n\r\n  const handleRetry = async (uploadId) => {\r\n    try {\r\n      await partnerReferenceDataUploadService.validateUpload(uploadId);\r\n      messageService.successToast(\"Validation retried successfully\");\r\n      loadUploads();\r\n    } catch (error) {\r\n      messageService.errorToast(error.message || \"Retry failed\");\r\n    }\r\n  };\r\n\r\n  const handleDelete = async (uploadId, fileName) => {\r\n    confirmDialog({\r\n      message: `Are you sure you want to delete the upload \"${fileName}\"? This action cannot be undone and will remove all associated data.`,\r\n      header: 'Confirm Delete',\r\n      icon: 'pi pi-exclamation-triangle',\r\n      acceptClassName: 'p-button-danger',\r\n      accept: async () => {\r\n        try {\r\n          await partnerReferenceDataUploadService.deleteUpload(uploadId);\r\n          messageService.successToast(\"Upload deleted successfully\");\r\n          loadUploads();\r\n        } catch (error) {\r\n          messageService.errorToast(error.message || \"Delete failed\");\r\n        }\r\n      }\r\n    });\r\n  };\r\n\r\n  // Column renderers\r\n  const statusBodyTemplate = (rowData) => {\r\n    const statusMap = {\r\n      [PartnerReferenceDataUploadStatus.Uploading]: { label: 'Uploading', severity: 'info' },\r\n      [PartnerReferenceDataUploadStatus.Uploaded]: { label: 'Uploaded', severity: 'warning' },\r\n      [PartnerReferenceDataUploadStatus.Validating]: { label: 'Validating', severity: 'info' },\r\n      [PartnerReferenceDataUploadStatus.ValidationPassed]: { label: 'Validation Passed', severity: 'success' },\r\n      [PartnerReferenceDataUploadStatus.ValidationFailed]: { label: 'Validation Failed', severity: 'danger' },\r\n      [PartnerReferenceDataUploadStatus.Submitted]: { label: 'Submitted', severity: 'success' }\r\n    };\r\n\r\n    const status = statusMap[rowData.status] || { label: 'Unknown', severity: 'secondary' };\r\n    return <Badge value={status.label} severity={status.severity} />;\r\n  };\r\n\r\n  const cycleBodyTemplate = (rowData) => {\r\n    return getCycleDisplayName(rowData.cycle);\r\n  };\r\n\r\n  const validationSummaryBodyTemplate = (rowData) => {\r\n    if (!rowData.validationSummary) return null;\r\n\r\n    const truncated = rowData.validationSummary.length > 50\r\n      ? rowData.validationSummary.substring(0, 50) + '...'\r\n      : rowData.validationSummary;\r\n\r\n    return (\r\n      <div>\r\n        <span>{truncated}</span>\r\n        {rowData.validationSummary.length > 50 && (\r\n          <>\r\n            <Button\r\n              icon=\"pi pi-info-circle\"\r\n              className=\"p-button-text p-button-sm\"\r\n              tooltip=\"View full validation summary\"\r\n              onClick={() => {\r\n                messageService.infoDialog(rowData.validationSummary);\r\n              }}\r\n            />\r\n          </>\r\n        )}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  const actionBodyTemplate = (rowData) => {\r\n    return (\r\n      <div className=\"p-d-flex p-ai-center\">\r\n        <Button\r\n          icon=\"pi pi-eye\"\r\n          className=\"p-button-text p-button-sm p-mr-2\"\r\n          tooltip=\"View Details\"\r\n          onClick={() => handleViewDetails(rowData)}\r\n        />\r\n\r\n        {rowData.status === PartnerReferenceDataUploadStatus.ValidationPassed && (\r\n          <Button\r\n            icon=\"pi pi-check\"\r\n            className=\"p-button-text p-button-success p-button-sm p-mr-2\"\r\n            tooltip=\"Submit\"\r\n            onClick={() => handleSubmit(rowData.id)}\r\n          />\r\n        )}\r\n\r\n        {(rowData.status === PartnerReferenceDataUploadStatus.ValidationFailed\r\n          || rowData.status === PartnerReferenceDataUploadStatus.Uploaded\r\n          || rowData.status === PartnerReferenceDataUploadStatus.Uploading\r\n          || rowData.status === PartnerReferenceDataUploadStatus.Validating) && (\r\n          <Button\r\n            icon=\"pi pi-refresh\"\r\n            className=\"p-button-text p-button-warning p-button-sm p-mr-2\"\r\n            tooltip=\"Retry\"\r\n            onClick={() => handleRetry(rowData.id)}\r\n          />\r\n        )}\r\n\r\n        {rowData.status === PartnerReferenceDataUploadStatus.ValidationFailed && (\r\n          <Button\r\n            icon=\"pi pi-trash\"\r\n            className=\"p-button-text p-button-danger p-button-sm\"\r\n            tooltip=\"Delete Upload\"\r\n            onClick={() => handleDelete(rowData.id, rowData.uploadFileName)}\r\n          />\r\n        )}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  const dateBodyTemplate = (rowData, field) => {\r\n    if (!rowData[field.field]) return null;\r\n    return new Date(rowData[field.field]).toLocaleString();\r\n  };\r\n\r\n  const rowIdBodyTemplate = (rowData) => {\r\n    return (\r\n      <div style={{ textAlign: 'center', fontWeight: 'bold', color: '#6366f1' }}>\r\n        {rowData.rowId}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  const rowIdHeaderTemplate = () => {\r\n    return (\r\n      <div>\r\n        Row ID\r\n        <Tooltip target=\".row-id-header\" content=\"The row number from the original uploaded file\" position=\"top\" />\r\n        <i className=\"pi pi-info-circle row-id-header p-ml-1\" style={{ fontSize: '0.8rem', color: '#6c757d' }}></i>\r\n      </div>\r\n    );\r\n  };\r\n\r\n  const header = (\r\n    <div className=\"upload-history-header\">\r\n      <div className=\"section-header\">Upload History</div>\r\n      <div className=\"upload-history-actions\">\r\n        <InputText\r\n          type=\"search\"\r\n          onInput={(e) => setGlobalFilter(e.target.value)}\r\n          placeholder=\"Search uploads...\"\r\n          className=\"upload-search-input\"\r\n        />\r\n        <Button\r\n          icon=\"pi pi-download\"\r\n          label=\"Download Template\"\r\n          className=\"p-button-red\"\r\n          rounded\r\n          onClick={handleDownloadTemplate}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  return (\r\n    <div className=\"upload-partner-reviewer\">\r\n      <Toast ref={toast} />\r\n      <ConfirmDialog />\r\n\r\n      {/* Upload Section */}\r\n      <Card className=\"p-mb-4 upload-section\">\r\n        {/* <div className=\"p-card-title\">\r\n          <h5>Upload Partner Reference Data File</h5>\r\n        </div> */}\r\n        <div className=\"p-card-content\">\r\n          <FileUpload\r\n            ref={fileUploadRef}\r\n            mode=\"basic\"\r\n            name=\"file\"\r\n            accept=\".xlsx,.xls,.csv\"\r\n            maxFileSize={10000000} // 10MB\r\n            onSelect={handleFileSelect}\r\n            chooseLabel=\"Select File\"\r\n            className=\"p-mr-2\"\r\n            auto={false}\r\n          />\r\n          <small className=\"p-d-block p-mt-2\">\r\n            Supported formats: Excel (.xlsx, .xls) and CSV files. Maximum size: 10MB\r\n          </small>\r\n        </div>\r\n      </Card>\r\n\r\n      {/* Upload History Table */}\r\n      <Card className=\"upload-history\">\r\n        <DataTable\r\n          value={uploads}\r\n          loading={loading}\r\n          header={header}\r\n          globalFilter={globalFilter}\r\n          emptyMessage=\"No uploads found\"\r\n          scrollable\r\n        >\r\n          <Column field=\"uploadFileName\" header=\"File Name\" sortable />\r\n          <Column field=\"year\" header=\"Year\" sortable />\r\n          <Column field=\"cycle\" header=\"Cycle\" sortable body={cycleBodyTemplate} />\r\n          <Column field=\"modifiedByName\" header=\"Updated By\" sortable />\r\n          <Column\r\n            field=\"modifiedOn\"\r\n            header=\"Updated On\"\r\n            sortable\r\n            body={(rowData) => dateBodyTemplate(rowData, { field: 'modifiedOn' })}\r\n          />\r\n          <Column\r\n            field=\"validationSummary\"\r\n            header=\"Validation Summary\"\r\n            body={validationSummaryBodyTemplate}\r\n          />\r\n          <Column\r\n            field=\"status\"\r\n            header=\"Status\"\r\n            sortable\r\n            body={statusBodyTemplate}\r\n          />\r\n          <Column\r\n            header=\"Actions\"\r\n            body={actionBodyTemplate}\r\n            style={{ width: '150px' }}\r\n          />\r\n        </DataTable>\r\n\r\n        <Paginator\r\n          first={first}\r\n          rows={rows}\r\n          totalRecords={totalRecords}\r\n          rowsPerPageOptions={[10, 20, 50]}\r\n          onPageChange={onPageChange}\r\n          className=\"p-mt-3\"\r\n        />\r\n      </Card>\r\n\r\n      {/* Upload Dialog */}\r\n      <Dialog\r\n        header=\"Select Year and Cycle for Upload\"\r\n        visible={showUploadDialog}\r\n        style={{ width: '450px' }}\r\n        modal\r\n        onHide={() => {\r\n          setShowUploadDialog(false);\r\n          setSelectedFile(null);\r\n          setSelectedYear(new Date().getFullYear());\r\n          setSelectedCycle(PartnerPlanCycle.Planning);\r\n          fileUploadRef.current?.clear();\r\n        }}\r\n      >\r\n        <div className=\"p-fluid\">\r\n          <div className=\"p-field p-mb-3\">\r\n            <label htmlFor=\"selectedFile\">Selected File:</label>\r\n            <div className=\"p-mt-2\">\r\n              <strong>{selectedFile?.name}</strong>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"p-field p-mb-3\">\r\n            <label htmlFor=\"year\">Year:</label>\r\n            <Dropdown\r\n              id=\"year\"\r\n              value={selectedYear}\r\n              options={yearOptions}\r\n              onChange={(e) => setSelectedYear(e.value)}\r\n              placeholder=\"Select year\"\r\n              className=\"p-mt-2\"\r\n            />\r\n          </div>\r\n\r\n          <div className=\"p-field p-mb-4\">\r\n            <label htmlFor=\"cycle\">Cycle:</label>\r\n            <Dropdown\r\n              id=\"cycle\"\r\n              value={selectedCycle}\r\n              options={cycleOptions}\r\n              onChange={(e) => setSelectedCycle(e.value)}\r\n              placeholder=\"Select cycle\"\r\n              className=\"p-mt-2\"\r\n            />\r\n          </div>\r\n\r\n          <div style={{\r\n            display: 'flex',\r\n            justifyContent: 'flex-end',\r\n            alignItems: 'center',\r\n            gap: '12px',\r\n            marginTop: '20px'\r\n          }}>\r\n            <Button\r\n              label=\"Cancel\"\r\n              icon=\"pi pi-times\"\r\n              className=\"p-button-text\"\r\n              onClick={() => {\r\n                setShowUploadDialog(false);\r\n                setSelectedFile(null);\r\n                setSelectedYear(new Date().getFullYear());\r\n                setSelectedCycle(PartnerPlanCycle.Planning);\r\n                fileUploadRef.current?.clear();\r\n              }}\r\n            />\r\n            <Button\r\n              label=\"Upload\"\r\n              icon=\"pi pi-upload\"\r\n              className=\"p-button-red\"\r\n              loading={uploading}\r\n              onClick={handleUpload}\r\n            />\r\n          </div>\r\n        </div>\r\n      </Dialog>\r\n\r\n      {/* Details Dialog */}\r\n      <Dialog\r\n        header={`Upload Details - ${selectedUpload?.uploadFileName || ''}`}\r\n        visible={showDetailsDialog}\r\n        style={{ width: '80vw', height: '80vh' }}\r\n        modal\r\n        onHide={() => {\r\n          setShowDetailsDialog(false);\r\n          setSelectedUpload(null);\r\n          setUploadDetails([]);\r\n          setDetailsFilter('all');\r\n        }}\r\n      >\r\n        {/* Filter Controls */}\r\n        <div style={{\r\n          display: 'flex',\r\n          alignItems: 'center',\r\n          gap: '12px',\r\n          marginBottom: '16px',\r\n          padding: '12px',\r\n          backgroundColor: '#f8f9fa',\r\n          borderRadius: '6px',\r\n          border: '1px solid #e9ecef'\r\n        }}>\r\n          <label htmlFor=\"details-filter\" style={{\r\n            fontWeight: '600',\r\n            color: '#495057',\r\n            minWidth: '60px'\r\n          }}>\r\n            Filter:\r\n          </label>\r\n          <Dropdown\r\n            id=\"details-filter\"\r\n            value={detailsFilter}\r\n            options={filterOptions}\r\n            onChange={handleDetailsFilterChange}\r\n            placeholder=\"Select filter\"\r\n            style={{ minWidth: '150px' }}\r\n          />\r\n          <span style={{\r\n            color: '#6c757d',\r\n            fontSize: '0.9rem',\r\n            marginLeft: '8px'\r\n          }}>\r\n            Showing {uploadDetails.length} record(s)\r\n          </span>\r\n          {selectedUpload?.validationSummary && (\r\n            <div style={{\r\n              marginLeft: '16px',\r\n              padding: '8px 12px',\r\n              backgroundColor: '#fff3cd',\r\n              border: '1px solid #ffeaa7',\r\n              borderRadius: '4px',\r\n              fontSize: '0.9rem',\r\n              color: '#856404',\r\n              maxWidth: '400px',\r\n              overflow: 'hidden',\r\n              textOverflow: 'ellipsis',\r\n              whiteSpace: 'nowrap'\r\n            }}>\r\n              <strong>Validation Summary:</strong> {selectedUpload.validationSummary}\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        <DataTable\r\n          value={uploadDetails}\r\n          loading={detailsLoading}\r\n          scrollable\r\n          scrollHeight=\"500px\"\r\n          emptyMessage=\"No details found\"\r\n        >\r\n          <Column\r\n            field=\"rowId\"\r\n            header={rowIdHeaderTemplate}\r\n            sortable\r\n            style={{ width: '80px' }}\r\n            body={rowIdBodyTemplate}\r\n          />\r\n          {/* Move \"Valid\" and \"Validation Error\" columns to come first */}\r\n          <Column\r\n            field=\"isValid\"\r\n            header=\"Valid\"\r\n            sortable\r\n            style={{ width: '80px' }}\r\n            body={(rowData) => (\r\n              <Badge\r\n                value={rowData.isValid ? 'Valid' : 'Invalid'}\r\n                severity={rowData.isValid ? 'success' : 'danger'}\r\n              />\r\n            )}\r\n          />\r\n          <Column\r\n            field=\"validationError\"\r\n            header=\"Validation Error\"\r\n            style={{ minWidth: '200px' }}\r\n            body={(rowData) => (\r\n              <span title={rowData.validationError} style={{\r\n                display: 'block',\r\n                maxWidth: '300px',\r\n                overflow: 'hidden',\r\n                textOverflow: 'ellipsis',\r\n                whiteSpace: 'nowrap'\r\n              }}>\r\n                {rowData.validationError}\r\n              </span>\r\n            )}\r\n          />\r\n          {/* Dynamic columns based on metadata - keep the same sort order */}\r\n          {metaDetails\r\n            .sort((a, b) => a.columnOrder - b.columnOrder)\r\n            .map((metaDetail) => (\r\n              <Column\r\n                key={metaDetail.normalizedColumnName}\r\n                field={`columnData.${metaDetail.normalizedColumnName}`}\r\n                header={metaDetail.columnName}\r\n                sortable\r\n                body={(rowData) => {\r\n                  const value = rowData.columnData?.[metaDetail.normalizedColumnName] || '';\r\n                  return (\r\n                    <span title={value} style={{\r\n                      display: 'block',\r\n                      maxWidth: '200px',\r\n                      overflow: 'hidden',\r\n                      textOverflow: 'ellipsis',\r\n                      whiteSpace: 'nowrap'\r\n                    }}>\r\n                      {value}\r\n                    </span>\r\n                  );\r\n                }}\r\n              />\r\n            ))}\r\n        </DataTable>\r\n      </Dialog>\r\n    </div>\r\n  );\r\n};"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,IAAI,QAAQ,iBAAiB;AACtC,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,UAAU,QAAQ,uBAAuB;AAClD,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,aAAa,EAAEC,aAAa,QAAQ,0BAA0B;AACvE,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,OAAOC,iCAAiC,MAAM,kDAAkD;AAChG,SAASC,cAAc,QAAQ,mCAAmC;AAClE,SAASC,gCAAgC,QAAQ,yDAAyD;AAC1G,SAASC,gBAAgB,EAAEC,eAAe,EAAEC,mBAAmB,QAAQ,yCAAyC;AAChH,SAASC,iBAAiB,QAAQ,4CAA4C;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/E,OAAO,MAAMC,0BAA0B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9C,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACgC,OAAO,EAAEC,UAAU,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACkC,YAAY,EAAEC,eAAe,CAAC,GAAGnC,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM,CAACoC,KAAK,EAAEC,QAAQ,CAAC,GAAGrC,QAAQ,CAAC,CAAC,CAAC;EACrC,MAAM,CAACsC,IAAI,EAAEC,OAAO,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACwC,YAAY,EAAEC,eAAe,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;;EAEpD;EACA,MAAM,CAAC0C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC4C,YAAY,EAAEC,eAAe,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC8C,YAAY,EAAEC,eAAe,CAAC,GAAG/C,QAAQ,CAAC,IAAIgD,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;EAC1E,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGnD,QAAQ,CAACoB,gBAAgB,CAACgC,QAAQ,CAAC;EAC7E,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;;EAEjD;EACA,MAAM,CAACuD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACyD,cAAc,EAAEC,iBAAiB,CAAC,GAAG1D,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC2D,aAAa,EAAEC,gBAAgB,CAAC,GAAG5D,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC6D,WAAW,EAAEC,cAAc,CAAC,GAAG9D,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC+D,cAAc,EAAEC,iBAAiB,CAAC,GAAGhE,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACiE,aAAa,EAAEC,gBAAgB,CAAC,GAAGlE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;;EAE3D,MAAMmE,KAAK,GAAGjE,MAAM,CAAC,IAAI,CAAC;EAC1B,MAAMkE,aAAa,GAAGlE,MAAM,CAAC,IAAI,CAAC;;EAElC;EACA,MAAMmE,WAAW,GAAG,IAAIrB,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;EAC5C,MAAMqB,WAAW,GAAG,EAAE;EACtB,KAAK,IAAIC,CAAC,GAAGF,WAAW,GAAG,CAAC,EAAEE,CAAC,IAAIF,WAAW,GAAG,CAAC,EAAEE,CAAC,EAAE,EAAE;IACvDD,WAAW,CAACE,IAAI,CAAC;MAAEC,KAAK,EAAEF,CAAC,CAACG,QAAQ,CAAC,CAAC;MAAEC,KAAK,EAAEJ;IAAE,CAAC,CAAC;EACrD;;EAEA;EACA,MAAMK,YAAY,GAAGvD,eAAe,CAAC,CAAC;;EAEtC;EACAE,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAAC;EAEjCtB,SAAS,CAAC,MAAM;IACd4E,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAACzC,KAAK,EAAEE,IAAI,CAAC,CAAC,CAAC,CAAC;;EAEnB,MAAMuC,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B5C,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAM6C,SAAS,GAAGC,IAAI,CAACC,KAAK,CAAC5C,KAAK,GAAGE,IAAI,CAAC;MAC1C,MAAM2C,MAAM,GAAG,MAAMhE,iCAAiC,CAACiE,iCAAiC,CACtF,IAAI;MAAE;MACN,IAAI;MAAE;MACNJ,SAAS,EACTxC,IACF,CAAC;MACDP,UAAU,CAACkD,MAAM,CAACE,KAAK,IAAI,EAAE,CAAC;MAC9BhD,eAAe,CAAC8C,MAAM,CAACG,UAAU,IAAI,CAAC,CAAC;IACzC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdnE,cAAc,CAACoE,UAAU,CAAC,wBAAwB,CAAC;IACrD,CAAC,SAAS;MACRrD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMsD,YAAY,GAAIC,KAAK,IAAK;IAC9BnD,QAAQ,CAACmD,KAAK,CAACpD,KAAK,CAAC;IACrBG,OAAO,CAACiD,KAAK,CAAClD,IAAI,CAAC;EACrB,CAAC;EAED,MAAMmD,gBAAgB,GAAID,KAAK,IAAK;IAClC,MAAME,IAAI,GAAGF,KAAK,CAACG,KAAK,CAAC,CAAC,CAAC;IAC3B,IAAID,IAAI,EAAE;MACR;MACA,MAAME,YAAY,GAAG,CACnB,mEAAmE;MAAE;MACrE,0BAA0B;MAAE;MAC5B,UAAU,CAAC;MAAA,CACZ;MAED,IAAI,CAACA,YAAY,CAACC,QAAQ,CAACH,IAAI,CAACI,IAAI,CAAC,EAAE;QACrC5E,cAAc,CAACoE,UAAU,CAAC,oDAAoD,CAAC;QAC/ElB,aAAa,CAAC2B,OAAO,CAACC,KAAK,CAAC,CAAC;QAC7B;MACF;MAEAnD,eAAe,CAAC6C,IAAI,CAAC;MACrB/C,mBAAmB,CAAC,IAAI,CAAC;IAC3B;EACF,CAAC;EAED,MAAMsD,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI,CAACrD,YAAY,EAAE;MACjB1B,cAAc,CAACgF,SAAS,CAAC,sBAAsB,CAAC;MAChD;IACF;IAEA5C,YAAY,CAAC,IAAI,CAAC;IAClB,IAAI;MACF,MAAMrC,iCAAiC,CAACkF,UAAU,CAACvD,YAAY,EAAEE,YAAY,EAAEI,aAAa,CAAC;MAE7FhC,cAAc,CAACkF,YAAY,CAAC,4BAA4B,CAAC;MACzDzD,mBAAmB,CAAC,KAAK,CAAC;MAC1BE,eAAe,CAAC,IAAI,CAAC;MACrBE,eAAe,CAAC,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;MACzCE,gBAAgB,CAAC/B,gBAAgB,CAACgC,QAAQ,CAAC;MAC3CgB,aAAa,CAAC2B,OAAO,CAACC,KAAK,CAAC,CAAC;MAC7BnB,WAAW,CAAC,CAAC;IACf,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdnE,cAAc,CAACoE,UAAU,CAACD,KAAK,CAACgB,OAAO,IAAI,eAAe,CAAC;IAC7D,CAAC,SAAS;MACR/C,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMgD,sBAAsB,GAAG,MAAAA,CAAA,KAAY;IACzC,IAAI;MACF,MAAMC,IAAI,GAAG,MAAMtF,iCAAiC,CAACuF,iBAAiB,CAAC,CAAC;MACxE,MAAMC,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;MAC5C,MAAMM,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGP,GAAG;MACfI,IAAI,CAACI,QAAQ,GAAG,yCAAyC;MACzDH,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;MAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;MACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;MAC/BH,MAAM,CAACC,GAAG,CAACW,eAAe,CAACb,GAAG,CAAC;IACjC,CAAC,CAAC,OAAOpB,KAAK,EAAE;MACdnE,cAAc,CAACoE,UAAU,CAAC,6BAA6B,CAAC;IAC1D;EACF,CAAC;EAED,MAAMiC,iBAAiB,GAAG,MAAOC,MAAM,IAAK;IAC1C9D,iBAAiB,CAAC8D,MAAM,CAAC;IACzBxD,iBAAiB,CAAC,IAAI,CAAC;IACvBR,oBAAoB,CAAC,IAAI,CAAC;IAC1BU,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC;;IAEzB,MAAMuD,iBAAiB,CAACD,MAAM,CAACE,EAAE,EAAE,KAAK,CAAC;EAC3C,CAAC;EAED,MAAMD,iBAAiB,GAAG,MAAAA,CAAOE,QAAQ,EAAEC,MAAM,KAAK;IACpD5D,iBAAiB,CAAC,IAAI,CAAC;IACvB,IAAI;MACF,IAAI6D,gBAAgB,GAAG,KAAK;MAC5B,IAAIC,kBAAkB,GAAG,KAAK;MAE9B,IAAIF,MAAM,KAAK,OAAO,EAAE;QACtBC,gBAAgB,GAAG,IAAI;MACzB,CAAC,MAAM,IAAID,MAAM,KAAK,SAAS,EAAE;QAC/BE,kBAAkB,GAAG,IAAI;MAC3B;MAEA,MAAM7C,MAAM,GAAG,MAAMhE,iCAAiC,CAAC8G,oCAAoC,CACzFJ,QAAQ,EACRE,gBAAgB,EAChBC,kBACF,CAAC;;MAED;MACA,IAAI7C,MAAM,IAAIA,MAAM,CAACtB,aAAa,IAAIsB,MAAM,CAACpB,WAAW,EAAE;QACxDD,gBAAgB,CAACqB,MAAM,CAACtB,aAAa,CAAC;QACtCG,cAAc,CAACmB,MAAM,CAACpB,WAAW,CAAC;MACpC,CAAC,MAAM;QACL;QACAD,gBAAgB,CAACqB,MAAM,IAAI,EAAE,CAAC;QAC9BnB,cAAc,CAAC,EAAE,CAAC;MACpB;IACF,CAAC,CAAC,OAAOuB,KAAK,EAAE;MACdnE,cAAc,CAACoE,UAAU,CAAC,+BAA+B,CAAC;IAC5D,CAAC,SAAS;MACRtB,iBAAiB,CAAC,KAAK,CAAC;IAC1B;EACF,CAAC;EAED,MAAMgE,yBAAyB,GAAIC,CAAC,IAAK;IACvC,MAAMC,SAAS,GAAGD,CAAC,CAACtD,KAAK;IACzBT,gBAAgB,CAACgE,SAAS,CAAC;IAC3B,IAAIzE,cAAc,EAAE;MAClBgE,iBAAiB,CAAChE,cAAc,CAACiE,EAAE,EAAEQ,SAAS,CAAC;IACjD;EACF,CAAC;;EAED;EACA,MAAMC,aAAa,GAAG,CACpB;IAAE1D,KAAK,EAAE,UAAU;IAAEE,KAAK,EAAE;EAAM,CAAC,EACnC;IAAEF,KAAK,EAAE,YAAY;IAAEE,KAAK,EAAE;EAAQ,CAAC,EACvC;IAAEF,KAAK,EAAE,cAAc;IAAEE,KAAK,EAAE;EAAU,CAAC,CAC5C;EAED,MAAMyD,YAAY,GAAG,MAAOT,QAAQ,IAAK;IACvC7G,aAAa,CAAC;MACZuF,OAAO,EAAE,2FAA2F;MACpGgC,MAAM,EAAE,gBAAgB;MACxBC,IAAI,EAAE,4BAA4B;MAClCC,MAAM,EAAE,MAAAA,CAAA,KAAY;QAClB,IAAI;UACF,MAAMtH,iCAAiC,CAACuH,YAAY,CAACb,QAAQ,CAAC;UAC9DzG,cAAc,CAACkF,YAAY,CAAC,+BAA+B,CAAC;UAC5DvB,WAAW,CAAC,CAAC;QACf,CAAC,CAAC,OAAOQ,KAAK,EAAE;UACdnE,cAAc,CAACoE,UAAU,CAACD,KAAK,CAACgB,OAAO,IAAI,eAAe,CAAC;QAC7D;MACF;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMoC,WAAW,GAAG,MAAOd,QAAQ,IAAK;IACtC,IAAI;MACF,MAAM1G,iCAAiC,CAACyH,cAAc,CAACf,QAAQ,CAAC;MAChEzG,cAAc,CAACkF,YAAY,CAAC,iCAAiC,CAAC;MAC9DvB,WAAW,CAAC,CAAC;IACf,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdnE,cAAc,CAACoE,UAAU,CAACD,KAAK,CAACgB,OAAO,IAAI,cAAc,CAAC;IAC5D;EACF,CAAC;EAED,MAAMsC,YAAY,GAAG,MAAAA,CAAOhB,QAAQ,EAAEiB,QAAQ,KAAK;IACjD9H,aAAa,CAAC;MACZuF,OAAO,EAAE,+CAA+CuC,QAAQ,sEAAsE;MACtIP,MAAM,EAAE,gBAAgB;MACxBC,IAAI,EAAE,4BAA4B;MAClCO,eAAe,EAAE,iBAAiB;MAClCN,MAAM,EAAE,MAAAA,CAAA,KAAY;QAClB,IAAI;UACF,MAAMtH,iCAAiC,CAAC6H,YAAY,CAACnB,QAAQ,CAAC;UAC9DzG,cAAc,CAACkF,YAAY,CAAC,6BAA6B,CAAC;UAC1DvB,WAAW,CAAC,CAAC;QACf,CAAC,CAAC,OAAOQ,KAAK,EAAE;UACdnE,cAAc,CAACoE,UAAU,CAACD,KAAK,CAACgB,OAAO,IAAI,eAAe,CAAC;QAC7D;MACF;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAM0C,kBAAkB,GAAIC,OAAO,IAAK;IACtC,MAAMC,SAAS,GAAG;MAChB,CAAC9H,gCAAgC,CAAC+H,SAAS,GAAG;QAAEzE,KAAK,EAAE,WAAW;QAAE0E,QAAQ,EAAE;MAAO,CAAC;MACtF,CAAChI,gCAAgC,CAACiI,QAAQ,GAAG;QAAE3E,KAAK,EAAE,UAAU;QAAE0E,QAAQ,EAAE;MAAU,CAAC;MACvF,CAAChI,gCAAgC,CAACkI,UAAU,GAAG;QAAE5E,KAAK,EAAE,YAAY;QAAE0E,QAAQ,EAAE;MAAO,CAAC;MACxF,CAAChI,gCAAgC,CAACmI,gBAAgB,GAAG;QAAE7E,KAAK,EAAE,mBAAmB;QAAE0E,QAAQ,EAAE;MAAU,CAAC;MACxG,CAAChI,gCAAgC,CAACoI,gBAAgB,GAAG;QAAE9E,KAAK,EAAE,mBAAmB;QAAE0E,QAAQ,EAAE;MAAS,CAAC;MACvG,CAAChI,gCAAgC,CAACqI,SAAS,GAAG;QAAE/E,KAAK,EAAE,WAAW;QAAE0E,QAAQ,EAAE;MAAU;IAC1F,CAAC;IAED,MAAMM,MAAM,GAAGR,SAAS,CAACD,OAAO,CAACS,MAAM,CAAC,IAAI;MAAEhF,KAAK,EAAE,SAAS;MAAE0E,QAAQ,EAAE;IAAY,CAAC;IACvF,oBAAO1H,OAAA,CAACV,KAAK;MAAC4D,KAAK,EAAE8E,MAAM,CAAChF,KAAM;MAAC0E,QAAQ,EAAEM,MAAM,CAACN;IAAS;MAAAP,QAAA,EAAAc,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAClE,CAAC;EAED,MAAMC,iBAAiB,GAAIb,OAAO,IAAK;IACrC,OAAO1H,mBAAmB,CAAC0H,OAAO,CAACc,KAAK,CAAC;EAC3C,CAAC;EAED,MAAMC,6BAA6B,GAAIf,OAAO,IAAK;IACjD,IAAI,CAACA,OAAO,CAACgB,iBAAiB,EAAE,OAAO,IAAI;IAE3C,MAAMC,SAAS,GAAGjB,OAAO,CAACgB,iBAAiB,CAACE,MAAM,GAAG,EAAE,GACnDlB,OAAO,CAACgB,iBAAiB,CAACG,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAClDnB,OAAO,CAACgB,iBAAiB;IAE7B,oBACEvI,OAAA;MAAA2I,QAAA,gBACE3I,OAAA;QAAA2I,QAAA,EAAOH;MAAS;QAAArB,QAAA,EAAAc,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EACvBZ,OAAO,CAACgB,iBAAiB,CAACE,MAAM,GAAG,EAAE,iBACpCzI,OAAA,CAAAE,SAAA;QAAAyI,QAAA,eACE3I,OAAA,CAACrB,MAAM;UACLkI,IAAI,EAAC,mBAAmB;UACxB+B,SAAS,EAAC,2BAA2B;UACrCC,OAAO,EAAC,8BAA8B;UACtCC,OAAO,EAAEA,CAAA,KAAM;YACbrJ,cAAc,CAACsJ,UAAU,CAACxB,OAAO,CAACgB,iBAAiB,CAAC;UACtD;QAAE;UAAApB,QAAA,EAAAc,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,gBACF,CACH;IAAA;MAAAhB,QAAA,EAAAc,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEV,CAAC;EAED,MAAMa,kBAAkB,GAAIzB,OAAO,IAAK;IACtC,oBACEvH,OAAA;MAAK4I,SAAS,EAAC,sBAAsB;MAAAD,QAAA,gBACnC3I,OAAA,CAACrB,MAAM;QACLkI,IAAI,EAAC,WAAW;QAChB+B,SAAS,EAAC,kCAAkC;QAC5CC,OAAO,EAAC,cAAc;QACtBC,OAAO,EAAEA,CAAA,KAAMhD,iBAAiB,CAACyB,OAAO;MAAE;QAAAJ,QAAA,EAAAc,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC,EAEDZ,OAAO,CAACS,MAAM,KAAKtI,gCAAgC,CAACmI,gBAAgB,iBACnE7H,OAAA,CAACrB,MAAM;QACLkI,IAAI,EAAC,aAAa;QAClB+B,SAAS,EAAC,mDAAmD;QAC7DC,OAAO,EAAC,QAAQ;QAChBC,OAAO,EAAEA,CAAA,KAAMnC,YAAY,CAACY,OAAO,CAACtB,EAAE;MAAE;QAAAkB,QAAA,EAAAc,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC,CACF,EAEA,CAACZ,OAAO,CAACS,MAAM,KAAKtI,gCAAgC,CAACoI,gBAAgB,IACjEP,OAAO,CAACS,MAAM,KAAKtI,gCAAgC,CAACiI,QAAQ,IAC5DJ,OAAO,CAACS,MAAM,KAAKtI,gCAAgC,CAAC+H,SAAS,IAC7DF,OAAO,CAACS,MAAM,KAAKtI,gCAAgC,CAACkI,UAAU,kBACjE5H,OAAA,CAACrB,MAAM;QACLkI,IAAI,EAAC,eAAe;QACpB+B,SAAS,EAAC,mDAAmD;QAC7DC,OAAO,EAAC,OAAO;QACfC,OAAO,EAAEA,CAAA,KAAM9B,WAAW,CAACO,OAAO,CAACtB,EAAE;MAAE;QAAAkB,QAAA,EAAAc,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CACF,EAEAZ,OAAO,CAACS,MAAM,KAAKtI,gCAAgC,CAACoI,gBAAgB,iBACnE9H,OAAA,CAACrB,MAAM;QACLkI,IAAI,EAAC,aAAa;QAClB+B,SAAS,EAAC,2CAA2C;QACrDC,OAAO,EAAC,eAAe;QACvBC,OAAO,EAAEA,CAAA,KAAM5B,YAAY,CAACK,OAAO,CAACtB,EAAE,EAAEsB,OAAO,CAAC0B,cAAc;MAAE;QAAA9B,QAAA,EAAAc,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjE,CACF;IAAA;MAAAhB,QAAA,EAAAc,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEV,CAAC;EAED,MAAMe,gBAAgB,GAAGA,CAAC3B,OAAO,EAAE4B,KAAK,KAAK;IAC3C,IAAI,CAAC5B,OAAO,CAAC4B,KAAK,CAACA,KAAK,CAAC,EAAE,OAAO,IAAI;IACtC,OAAO,IAAI5H,IAAI,CAACgG,OAAO,CAAC4B,KAAK,CAACA,KAAK,CAAC,CAAC,CAACC,cAAc,CAAC,CAAC;EACxD,CAAC;EAED,MAAMC,iBAAiB,GAAI9B,OAAO,IAAK;IACrC,oBACEvH,OAAA;MAAKsJ,KAAK,EAAE;QAAEC,SAAS,EAAE,QAAQ;QAAEC,UAAU,EAAE,MAAM;QAAEC,KAAK,EAAE;MAAU,CAAE;MAAAd,QAAA,EACvEpB,OAAO,CAACmC;IAAK;MAAAvC,QAAA,EAAAc,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CAAC;EAEV,CAAC;EAED,MAAMwB,mBAAmB,GAAGA,CAAA,KAAM;IAChC,oBACE3J,OAAA;MAAA2I,QAAA,GAAK,QAEH,eAAA3I,OAAA,CAACT,OAAO;QAACqK,MAAM,EAAC,gBAAgB;QAACC,OAAO,EAAC,gDAAgD;QAACC,QAAQ,EAAC;MAAK;QAAA3C,QAAA,EAAAc,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3GnI,OAAA;QAAG4I,SAAS,EAAC,wCAAwC;QAACU,KAAK,EAAE;UAAES,QAAQ,EAAE,QAAQ;UAAEN,KAAK,EAAE;QAAU;MAAE;QAAAtC,QAAA,EAAAc,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAhB,QAAA,EAAAc,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxG,CAAC;EAEV,CAAC;EAED,MAAMvB,MAAM,gBACV5G,OAAA;IAAK4I,SAAS,EAAC,uBAAuB;IAAAD,QAAA,gBACpC3I,OAAA;MAAK4I,SAAS,EAAC,gBAAgB;MAAAD,QAAA,EAAC;IAAc;MAAAxB,QAAA,EAAAc,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACpDnI,OAAA;MAAK4I,SAAS,EAAC,wBAAwB;MAAAD,QAAA,gBACrC3I,OAAA,CAACf,SAAS;QACRoF,IAAI,EAAC,QAAQ;QACb2F,OAAO,EAAGxD,CAAC,IAAKxF,eAAe,CAACwF,CAAC,CAACoD,MAAM,CAAC1G,KAAK,CAAE;QAChD+G,WAAW,EAAC,mBAAmB;QAC/BrB,SAAS,EAAC;MAAqB;QAAAzB,QAAA,EAAAc,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CAAC,eACFnI,OAAA,CAACrB,MAAM;QACLkI,IAAI,EAAC,gBAAgB;QACrB7D,KAAK,EAAC,mBAAmB;QACzB4F,SAAS,EAAC,cAAc;QACxBsB,OAAO;QACPpB,OAAO,EAAEjE;MAAuB;QAAAsC,QAAA,EAAAc,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC;IAAA;MAAAhB,QAAA,EAAAc,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAhB,QAAA,EAAAc,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,oBACEnI,OAAA;IAAK4I,SAAS,EAAC,yBAAyB;IAAAD,QAAA,gBACtC3I,OAAA,CAACb,KAAK;MAACgL,GAAG,EAAEzH;IAAM;MAAAyE,QAAA,EAAAc,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACrBnI,OAAA,CAACZ,aAAa;MAAA+H,QAAA,EAAAc,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGjBnI,OAAA,CAACtB,IAAI;MAACkK,SAAS,EAAC,uBAAuB;MAAAD,QAAA,eAIrC3I,OAAA;QAAK4I,SAAS,EAAC,gBAAgB;QAAAD,QAAA,gBAC7B3I,OAAA,CAACpB,UAAU;UACTuL,GAAG,EAAExH,aAAc;UACnByH,IAAI,EAAC,OAAO;UACZC,IAAI,EAAC,MAAM;UACXvD,MAAM,EAAC,iBAAiB;UACxBwD,WAAW,EAAE,QAAS,CAAC;UAAA;UACvBC,QAAQ,EAAEvG,gBAAiB;UAC3BwG,WAAW,EAAC,aAAa;UACzB5B,SAAS,EAAC,QAAQ;UAClB6B,IAAI,EAAE;QAAM;UAAAtD,QAAA,EAAAc,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC,eACFnI,OAAA;UAAO4I,SAAS,EAAC,kBAAkB;UAAAD,QAAA,EAAC;QAEpC;UAAAxB,QAAA,EAAAc,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAhB,QAAA,EAAAc,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAhB,QAAA,EAAAc,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGPnI,OAAA,CAACtB,IAAI;MAACkK,SAAS,EAAC,gBAAgB;MAAAD,QAAA,gBAC9B3I,OAAA,CAACnB,SAAS;QACRqE,KAAK,EAAE7C,OAAQ;QACfE,OAAO,EAAEA,OAAQ;QACjBqG,MAAM,EAAEA,MAAO;QACf7F,YAAY,EAAEA,YAAa;QAC3B2J,YAAY,EAAC,kBAAkB;QAC/BC,UAAU;QAAAhC,QAAA,gBAEV3I,OAAA,CAAClB,MAAM;UAACqK,KAAK,EAAC,gBAAgB;UAACvC,MAAM,EAAC,WAAW;UAACgE,QAAQ;QAAA;UAAAzD,QAAA,EAAAc,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7DnI,OAAA,CAAClB,MAAM;UAACqK,KAAK,EAAC,MAAM;UAACvC,MAAM,EAAC,MAAM;UAACgE,QAAQ;QAAA;UAAAzD,QAAA,EAAAc,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9CnI,OAAA,CAAClB,MAAM;UAACqK,KAAK,EAAC,OAAO;UAACvC,MAAM,EAAC,OAAO;UAACgE,QAAQ;UAACnF,IAAI,EAAE2C;QAAkB;UAAAjB,QAAA,EAAAc,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzEnI,OAAA,CAAClB,MAAM;UAACqK,KAAK,EAAC,gBAAgB;UAACvC,MAAM,EAAC,YAAY;UAACgE,QAAQ;QAAA;UAAAzD,QAAA,EAAAc,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9DnI,OAAA,CAAClB,MAAM;UACLqK,KAAK,EAAC,YAAY;UAClBvC,MAAM,EAAC,YAAY;UACnBgE,QAAQ;UACRnF,IAAI,EAAG8B,OAAO,IAAK2B,gBAAgB,CAAC3B,OAAO,EAAE;YAAE4B,KAAK,EAAE;UAAa,CAAC;QAAE;UAAAhC,QAAA,EAAAc,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvE,CAAC,eACFnI,OAAA,CAAClB,MAAM;UACLqK,KAAK,EAAC,mBAAmB;UACzBvC,MAAM,EAAC,oBAAoB;UAC3BnB,IAAI,EAAE6C;QAA8B;UAAAnB,QAAA,EAAAc,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC,eACFnI,OAAA,CAAClB,MAAM;UACLqK,KAAK,EAAC,QAAQ;UACdvC,MAAM,EAAC,QAAQ;UACfgE,QAAQ;UACRnF,IAAI,EAAE6B;QAAmB;UAAAH,QAAA,EAAAc,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eACFnI,OAAA,CAAClB,MAAM;UACL8H,MAAM,EAAC,SAAS;UAChBnB,IAAI,EAAEuD,kBAAmB;UACzBM,KAAK,EAAE;YAAEuB,KAAK,EAAE;UAAQ;QAAE;UAAA1D,QAAA,EAAAc,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC;MAAA;QAAAhB,QAAA,EAAAc,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAEZnI,OAAA,CAACd,SAAS;QACRyB,KAAK,EAAEA,KAAM;QACbE,IAAI,EAAEA,IAAK;QACXJ,YAAY,EAAEA,YAAa;QAC3BqK,kBAAkB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAE;QACjChH,YAAY,EAAEA,YAAa;QAC3B8E,SAAS,EAAC;MAAQ;QAAAzB,QAAA,EAAAc,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC;IAAA;MAAAhB,QAAA,EAAAc,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGPnI,OAAA,CAACjB,MAAM;MACL6H,MAAM,EAAC,kCAAkC;MACzCmE,OAAO,EAAE9J,gBAAiB;MAC1BqI,KAAK,EAAE;QAAEuB,KAAK,EAAE;MAAQ,CAAE;MAC1BG,KAAK;MACLC,MAAM,EAAEA,CAAA,KAAM;QAAA,IAAAC,qBAAA;QACZhK,mBAAmB,CAAC,KAAK,CAAC;QAC1BE,eAAe,CAAC,IAAI,CAAC;QACrBE,eAAe,CAAC,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;QACzCE,gBAAgB,CAAC/B,gBAAgB,CAACgC,QAAQ,CAAC;QAC3C,CAAAuJ,qBAAA,GAAAvI,aAAa,CAAC2B,OAAO,cAAA4G,qBAAA,uBAArBA,qBAAA,CAAuB3G,KAAK,CAAC,CAAC;MAChC,CAAE;MAAAoE,QAAA,eAEF3I,OAAA;QAAK4I,SAAS,EAAC,SAAS;QAAAD,QAAA,gBACtB3I,OAAA;UAAK4I,SAAS,EAAC,gBAAgB;UAAAD,QAAA,gBAC7B3I,OAAA;YAAOmL,OAAO,EAAC,cAAc;YAAAxC,QAAA,EAAC;UAAc;YAAAxB,QAAA,EAAAc,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACpDnI,OAAA;YAAK4I,SAAS,EAAC,QAAQ;YAAAD,QAAA,eACrB3I,OAAA;cAAA2I,QAAA,EAASxH,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEkJ;YAAI;cAAAlD,QAAA,EAAAc,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS;UAAC;YAAAhB,QAAA,EAAAc,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC;QAAA;UAAAhB,QAAA,EAAAc,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnI,OAAA;UAAK4I,SAAS,EAAC,gBAAgB;UAAAD,QAAA,gBAC7B3I,OAAA;YAAOmL,OAAO,EAAC,MAAM;YAAAxC,QAAA,EAAC;UAAK;YAAAxB,QAAA,EAAAc,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACnCnI,OAAA,CAAChB,QAAQ;YACPiH,EAAE,EAAC,MAAM;YACT/C,KAAK,EAAE7B,YAAa;YACpB+J,OAAO,EAAEvI,WAAY;YACrBwI,QAAQ,EAAG7E,CAAC,IAAKlF,eAAe,CAACkF,CAAC,CAACtD,KAAK,CAAE;YAC1C+G,WAAW,EAAC,aAAa;YACzBrB,SAAS,EAAC;UAAQ;YAAAzB,QAAA,EAAAc,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAhB,QAAA,EAAAc,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENnI,OAAA;UAAK4I,SAAS,EAAC,gBAAgB;UAAAD,QAAA,gBAC7B3I,OAAA;YAAOmL,OAAO,EAAC,OAAO;YAAAxC,QAAA,EAAC;UAAM;YAAAxB,QAAA,EAAAc,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACrCnI,OAAA,CAAChB,QAAQ;YACPiH,EAAE,EAAC,OAAO;YACV/C,KAAK,EAAEzB,aAAc;YACrB2J,OAAO,EAAEjI,YAAa;YACtBkI,QAAQ,EAAG7E,CAAC,IAAK9E,gBAAgB,CAAC8E,CAAC,CAACtD,KAAK,CAAE;YAC3C+G,WAAW,EAAC,cAAc;YAC1BrB,SAAS,EAAC;UAAQ;YAAAzB,QAAA,EAAAc,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAhB,QAAA,EAAAc,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENnI,OAAA;UAAKsJ,KAAK,EAAE;YACVgC,OAAO,EAAE,MAAM;YACfC,cAAc,EAAE,UAAU;YAC1BC,UAAU,EAAE,QAAQ;YACpBC,GAAG,EAAE,MAAM;YACXC,SAAS,EAAE;UACb,CAAE;UAAA/C,QAAA,gBACA3I,OAAA,CAACrB,MAAM;YACLqE,KAAK,EAAC,QAAQ;YACd6D,IAAI,EAAC,aAAa;YAClB+B,SAAS,EAAC,eAAe;YACzBE,OAAO,EAAEA,CAAA,KAAM;cAAA,IAAA6C,sBAAA;cACbzK,mBAAmB,CAAC,KAAK,CAAC;cAC1BE,eAAe,CAAC,IAAI,CAAC;cACrBE,eAAe,CAAC,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;cACzCE,gBAAgB,CAAC/B,gBAAgB,CAACgC,QAAQ,CAAC;cAC3C,CAAAgK,sBAAA,GAAAhJ,aAAa,CAAC2B,OAAO,cAAAqH,sBAAA,uBAArBA,sBAAA,CAAuBpH,KAAK,CAAC,CAAC;YAChC;UAAE;YAAA4C,QAAA,EAAAc,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACFnI,OAAA,CAACrB,MAAM;YACLqE,KAAK,EAAC,QAAQ;YACd6D,IAAI,EAAC,cAAc;YACnB+B,SAAS,EAAC,cAAc;YACxBrI,OAAO,EAAEqB,SAAU;YACnBkH,OAAO,EAAEtE;UAAa;YAAA2C,QAAA,EAAAc,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC;QAAA;UAAAhB,QAAA,EAAAc,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAhB,QAAA,EAAAc,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAhB,QAAA,EAAAc,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGTnI,OAAA,CAACjB,MAAM;MACL6H,MAAM,EAAE,oBAAoB,CAAA5E,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEiH,cAAc,KAAI,EAAE,EAAG;MACnE8B,OAAO,EAAEjJ,iBAAkB;MAC3BwH,KAAK,EAAE;QAAEuB,KAAK,EAAE,MAAM;QAAEe,MAAM,EAAE;MAAO,CAAE;MACzCZ,KAAK;MACLC,MAAM,EAAEA,CAAA,KAAM;QACZlJ,oBAAoB,CAAC,KAAK,CAAC;QAC3BE,iBAAiB,CAAC,IAAI,CAAC;QACvBE,gBAAgB,CAAC,EAAE,CAAC;QACpBM,gBAAgB,CAAC,KAAK,CAAC;MACzB,CAAE;MAAAkG,QAAA,gBAGF3I,OAAA;QAAKsJ,KAAK,EAAE;UACVgC,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBC,GAAG,EAAE,MAAM;UACXI,YAAY,EAAE,MAAM;UACpBC,OAAO,EAAE,MAAM;UACfC,eAAe,EAAE,SAAS;UAC1BC,YAAY,EAAE,KAAK;UACnBC,MAAM,EAAE;QACV,CAAE;QAAAtD,QAAA,gBACA3I,OAAA;UAAOmL,OAAO,EAAC,gBAAgB;UAAC7B,KAAK,EAAE;YACrCE,UAAU,EAAE,KAAK;YACjBC,KAAK,EAAE,SAAS;YAChByC,QAAQ,EAAE;UACZ,CAAE;UAAAvD,QAAA,EAAC;QAEH;UAAAxB,QAAA,EAAAc,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRnI,OAAA,CAAChB,QAAQ;UACPiH,EAAE,EAAC,gBAAgB;UACnB/C,KAAK,EAAEV,aAAc;UACrB4I,OAAO,EAAE1E,aAAc;UACvB2E,QAAQ,EAAE9E,yBAA0B;UACpC0D,WAAW,EAAC,eAAe;UAC3BX,KAAK,EAAE;YAAE4C,QAAQ,EAAE;UAAQ;QAAE;UAAA/E,QAAA,EAAAc,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACFnI,OAAA;UAAMsJ,KAAK,EAAE;YACXG,KAAK,EAAE,SAAS;YAChBM,QAAQ,EAAE,QAAQ;YAClBoC,UAAU,EAAE;UACd,CAAE;UAAAxD,QAAA,GAAC,UACO,EAACzG,aAAa,CAACuG,MAAM,EAAC,YAChC;QAAA;UAAAtB,QAAA,EAAAc,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EACN,CAAAnG,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEuG,iBAAiB,kBAChCvI,OAAA;UAAKsJ,KAAK,EAAE;YACV6C,UAAU,EAAE,MAAM;YAClBL,OAAO,EAAE,UAAU;YACnBC,eAAe,EAAE,SAAS;YAC1BE,MAAM,EAAE,mBAAmB;YAC3BD,YAAY,EAAE,KAAK;YACnBjC,QAAQ,EAAE,QAAQ;YAClBN,KAAK,EAAE,SAAS;YAChB2C,QAAQ,EAAE,OAAO;YACjBC,QAAQ,EAAE,QAAQ;YAClBC,YAAY,EAAE,UAAU;YACxBC,UAAU,EAAE;UACd,CAAE;UAAA5D,QAAA,gBACA3I,OAAA;YAAA2I,QAAA,EAAQ;UAAmB;YAAAxB,QAAA,EAAAc,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACnG,cAAc,CAACuG,iBAAiB;QAAA;UAAApB,QAAA,EAAAc,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE,CACN;MAAA;QAAAhB,QAAA,EAAAc,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENnI,OAAA,CAACnB,SAAS;QACRqE,KAAK,EAAEhB,aAAc;QACrB3B,OAAO,EAAE+B,cAAe;QACxBqI,UAAU;QACV6B,YAAY,EAAC,OAAO;QACpB9B,YAAY,EAAC,kBAAkB;QAAA/B,QAAA,gBAE/B3I,OAAA,CAAClB,MAAM;UACLqK,KAAK,EAAC,OAAO;UACbvC,MAAM,EAAE+C,mBAAoB;UAC5BiB,QAAQ;UACRtB,KAAK,EAAE;YAAEuB,KAAK,EAAE;UAAO,CAAE;UACzBpF,IAAI,EAAE4D;QAAkB;UAAAlC,QAAA,EAAAc,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,eAEFnI,OAAA,CAAClB,MAAM;UACLqK,KAAK,EAAC,SAAS;UACfvC,MAAM,EAAC,OAAO;UACdgE,QAAQ;UACRtB,KAAK,EAAE;YAAEuB,KAAK,EAAE;UAAO,CAAE;UACzBpF,IAAI,EAAG8B,OAAO,iBACZvH,OAAA,CAACV,KAAK;YACJ4D,KAAK,EAAEqE,OAAO,CAACkF,OAAO,GAAG,OAAO,GAAG,SAAU;YAC7C/E,QAAQ,EAAEH,OAAO,CAACkF,OAAO,GAAG,SAAS,GAAG;UAAS;YAAAtF,QAAA,EAAAc,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD;QACD;UAAAhB,QAAA,EAAAc,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACFnI,OAAA,CAAClB,MAAM;UACLqK,KAAK,EAAC,iBAAiB;UACvBvC,MAAM,EAAC,kBAAkB;UACzB0C,KAAK,EAAE;YAAE4C,QAAQ,EAAE;UAAQ,CAAE;UAC7BzG,IAAI,EAAG8B,OAAO,iBACZvH,OAAA;YAAM0M,KAAK,EAAEnF,OAAO,CAACoF,eAAgB;YAACrD,KAAK,EAAE;cAC3CgC,OAAO,EAAE,OAAO;cAChBc,QAAQ,EAAE,OAAO;cACjBC,QAAQ,EAAE,QAAQ;cAClBC,YAAY,EAAE,UAAU;cACxBC,UAAU,EAAE;YACd,CAAE;YAAA5D,QAAA,EACCpB,OAAO,CAACoF;UAAe;YAAAxF,QAAA,EAAAc,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB;QACN;UAAAhB,QAAA,EAAAc,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAED/F,WAAW,CACTwK,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACE,WAAW,GAAGD,CAAC,CAACC,WAAW,CAAC,CAC7CC,GAAG,CAAEC,UAAU,iBACdjN,OAAA,CAAClB,MAAM;UAELqK,KAAK,EAAE,cAAc8D,UAAU,CAACC,oBAAoB,EAAG;UACvDtG,MAAM,EAAEqG,UAAU,CAACE,UAAW;UAC9BvC,QAAQ;UACRnF,IAAI,EAAG8B,OAAO,IAAK;YAAA,IAAA6F,mBAAA;YACjB,MAAMlK,KAAK,GAAG,EAAAkK,mBAAA,GAAA7F,OAAO,CAAC8F,UAAU,cAAAD,mBAAA,uBAAlBA,mBAAA,CAAqBH,UAAU,CAACC,oBAAoB,CAAC,KAAI,EAAE;YACzE,oBACElN,OAAA;cAAM0M,KAAK,EAAExJ,KAAM;cAACoG,KAAK,EAAE;gBACzBgC,OAAO,EAAE,OAAO;gBAChBc,QAAQ,EAAE,OAAO;gBACjBC,QAAQ,EAAE,QAAQ;gBAClBC,YAAY,EAAE,UAAU;gBACxBC,UAAU,EAAE;cACd,CAAE;cAAA5D,QAAA,EACCzF;YAAK;cAAAiE,QAAA,EAAAc,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAEX;QAAE,GAjBG8E,UAAU,CAACC,oBAAoB;UAAA/F,QAAA,EAAAc,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAkBrC,CACF,CAAC;MAAA;QAAAhB,QAAA,EAAAc,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAhB,QAAA,EAAAc,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAhB,QAAA,EAAAc,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAAC/H,EAAA,CA5oBWD,0BAA0B;EAAA,QAqCrCL,iBAAiB;AAAA;AAAAwN,EAAA,GArCNnN,0BAA0B;AAAA,IAAAmN,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}