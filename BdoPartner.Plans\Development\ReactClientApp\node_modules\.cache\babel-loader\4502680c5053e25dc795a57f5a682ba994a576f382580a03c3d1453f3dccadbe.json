{"ast": null, "code": "import APP_CONFIG from \"./appConfig\";\n\n/**\r\n * Identity Server 4 client associated config.\r\n *\r\n */\nexport const IDENTITY_CLIENT_CONFIG = {\n  authority: APP_CONFIG.iamDomain,\n  //(string): The URL of the OIDC provider.\n  client_id: APP_CONFIG.clientId,\n  //(string): Your client application's identifier as registered with the OIDC provider.\n  redirect_uri: APP_CONFIG.basePath.length > 0 ? APP_CONFIG.appDomain + APP_CONFIG.basePath + \"/auth-callback\" : APP_CONFIG.appDomain + \"/auth-callback\",\n  //The URI of your client application to receive a response from the OIDC provider.\n  automaticSilentRenew: true,\n  //(boolean, default: false): Flag to indicate if there should be an automatic attempt to renew the access token prior to its expiration.\n  loadUserInfo: true,\n  //(boolean, default: true): Flag to control if additional identity data is loaded from the user info endpoint in order to populate the user's profile.\n  silent_redirect_uri: APP_CONFIG.basePath.length > 0 ? APP_CONFIG.appDomain + APP_CONFIG.basePath + \"/silent-auth-callback\" : APP_CONFIG.appDomain + \"/silent-auth-callback\",\n  //(string): The URL for the page containing the code handling the silent renew.\n\n  post_logout_redirect_uri: APP_CONFIG.basePath.length > 0 ? APP_CONFIG.appDomain + APP_CONFIG.basePath + \"/\" : APP_CONFIG.appDomain + \"/\",\n  // (string): The OIDC post-logout redirect URI.\n  //audience: \"https://example.com\", //is there a way to specific the audience when making the jwt\n  response_type: \"code\",\n  // PECK approach. //\"id_token token\", //(string, default: 'id_token'): The type of response desired from the OIDC provider.\n  filterProtocolClaims: true,\n  // PECK way.\n  grant_type: \"authorization_code\",\n  //\"password\",\n  scope: APP_CONFIG.iamScope,\n  //(string, default: 'openid'): The scope being requested from the OIDC provider.\n  //\n  // revoke (reference) access tokens at logout time\n  //\n  revokeAccessTokenOnSignout: true,\n  // integer value of seconds. Default value = 300.\n  // Work for resolve login infinity looping if client machine datetime is in correct.\n  // Set clockSkew is 15 minutes now.\n  clockSkew: 900,\n  // Additional settings to help with state management\n  includeIdTokenInSilentRenew: true,\n  monitorSession: false,\n  // Disable session monitoring to avoid conflicts\n  checkSessionInterval: 10000 // Check session every 10 seconds\n};", "map": {"version": 3, "names": ["APP_CONFIG", "IDENTITY_CLIENT_CONFIG", "authority", "iamDomain", "client_id", "clientId", "redirect_uri", "basePath", "length", "appDomain", "automaticSilentRenew", "loadUserInfo", "silent_redirect_uri", "post_logout_redirect_uri", "response_type", "filterProtocolClaims", "grant_type", "scope", "iamScope", "revokeAccessTokenOnSignout", "clockSkew", "includeIdTokenInSilentRenew", "monitorSession", "checkSessionInterval"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/core/config/identityClientConfig.js"], "sourcesContent": ["import APP_CONFIG from \"./appConfig\";\r\n\r\n/**\r\n * Identity Server 4 client associated config.\r\n *\r\n */\r\nexport const IDENTITY_CLIENT_CONFIG = {\r\n  authority: APP_CONFIG.iamDomain, //(string): The URL of the OIDC provider.\r\n  client_id: APP_CONFIG.clientId, //(string): Your client application's identifier as registered with the OIDC provider.\r\n  redirect_uri:\r\n    APP_CONFIG.basePath.length > 0\r\n      ? APP_CONFIG.appDomain + APP_CONFIG.basePath + \"/auth-callback\"\r\n      : APP_CONFIG.appDomain + \"/auth-callback\", //The URI of your client application to receive a response from the OIDC provider.\r\n  automaticSilentRenew: true, //(boolean, default: false): Flag to indicate if there should be an automatic attempt to renew the access token prior to its expiration.\r\n  loadUserInfo: true, //(boolean, default: true): Flag to control if additional identity data is loaded from the user info endpoint in order to populate the user's profile.\r\n  silent_redirect_uri:\r\n    APP_CONFIG.basePath.length > 0\r\n      ? APP_CONFIG.appDomain + APP_CONFIG.basePath + \"/silent-auth-callback\"\r\n      : APP_CONFIG.appDomain + \"/silent-auth-callback\", //(string): The URL for the page containing the code handling the silent renew.\r\n\r\n  post_logout_redirect_uri:\r\n    APP_CONFIG.basePath.length > 0\r\n      ? APP_CONFIG.appDomain + APP_CONFIG.basePath + \"/\"\r\n      : APP_CONFIG.appDomain + \"/\", // (string): The OIDC post-logout redirect URI.\r\n  //audience: \"https://example.com\", //is there a way to specific the audience when making the jwt\r\n  response_type: \"code\", // PECK approach. //\"id_token token\", //(string, default: 'id_token'): The type of response desired from the OIDC provider.\r\n  filterProtocolClaims: true, // PECK way.\r\n  grant_type: \"authorization_code\", //\"password\",\r\n  scope: APP_CONFIG.iamScope, //(string, default: 'openid'): The scope being requested from the OIDC provider.\r\n  //\r\n  // revoke (reference) access tokens at logout time\r\n  //\r\n  revokeAccessTokenOnSignout: true,\r\n  // integer value of seconds. Default value = 300.\r\n  // Work for resolve login infinity looping if client machine datetime is in correct.\r\n  // Set clockSkew is 15 minutes now.\r\n  clockSkew: 900,\r\n  // Additional settings to help with state management\r\n  includeIdTokenInSilentRenew: true,\r\n  monitorSession: false, // Disable session monitoring to avoid conflicts\r\n  checkSessionInterval: 10000, // Check session every 10 seconds\r\n};\r\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,aAAa;;AAEpC;AACA;AACA;AACA;AACA,OAAO,MAAMC,sBAAsB,GAAG;EACpCC,SAAS,EAAEF,UAAU,CAACG,SAAS;EAAE;EACjCC,SAAS,EAAEJ,UAAU,CAACK,QAAQ;EAAE;EAChCC,YAAY,EACVN,UAAU,CAACO,QAAQ,CAACC,MAAM,GAAG,CAAC,GAC1BR,UAAU,CAACS,SAAS,GAAGT,UAAU,CAACO,QAAQ,GAAG,gBAAgB,GAC7DP,UAAU,CAACS,SAAS,GAAG,gBAAgB;EAAE;EAC/CC,oBAAoB,EAAE,IAAI;EAAE;EAC5BC,YAAY,EAAE,IAAI;EAAE;EACpBC,mBAAmB,EACjBZ,UAAU,CAACO,QAAQ,CAACC,MAAM,GAAG,CAAC,GAC1BR,UAAU,CAACS,SAAS,GAAGT,UAAU,CAACO,QAAQ,GAAG,uBAAuB,GACpEP,UAAU,CAACS,SAAS,GAAG,uBAAuB;EAAE;;EAEtDI,wBAAwB,EACtBb,UAAU,CAACO,QAAQ,CAACC,MAAM,GAAG,CAAC,GAC1BR,UAAU,CAACS,SAAS,GAAGT,UAAU,CAACO,QAAQ,GAAG,GAAG,GAChDP,UAAU,CAACS,SAAS,GAAG,GAAG;EAAE;EAClC;EACAK,aAAa,EAAE,MAAM;EAAE;EACvBC,oBAAoB,EAAE,IAAI;EAAE;EAC5BC,UAAU,EAAE,oBAAoB;EAAE;EAClCC,KAAK,EAAEjB,UAAU,CAACkB,QAAQ;EAAE;EAC5B;EACA;EACA;EACAC,0BAA0B,EAAE,IAAI;EAChC;EACA;EACA;EACAC,SAAS,EAAE,GAAG;EACd;EACAC,2BAA2B,EAAE,IAAI;EACjCC,cAAc,EAAE,KAAK;EAAE;EACvBC,oBAAoB,EAAE,KAAK,CAAE;AAC/B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}