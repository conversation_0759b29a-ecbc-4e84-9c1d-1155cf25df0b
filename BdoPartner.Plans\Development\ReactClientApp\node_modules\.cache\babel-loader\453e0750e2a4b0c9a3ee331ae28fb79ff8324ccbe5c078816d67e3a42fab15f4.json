{"ast": null, "code": "export default function createTransform(\n// @NOTE inbound: transform state coming from redux on its way to being serialized and stored\ninbound,\n// @NOTE outbound: transform state coming from storage, on its way to be rehydrated into redux\noutbound) {\n  var config = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  var whitelist = config.whitelist || null;\n  var blacklist = config.blacklist || null;\n  function whitelistBlacklistCheck(key) {\n    if (whitelist && whitelist.indexOf(key) === -1) return true;\n    if (blacklist && blacklist.indexOf(key) !== -1) return true;\n    return false;\n  }\n  return {\n    in: function _in(state, key, fullState) {\n      return !whitelistBlacklistCheck(key) && inbound ? inbound(state, key, fullState) : state;\n    },\n    out: function out(state, key, fullState) {\n      return !whitelistBlacklistCheck(key) && outbound ? outbound(state, key, fullState) : state;\n    }\n  };\n}", "map": {"version": 3, "names": ["createTransform", "inbound", "outbound", "config", "arguments", "length", "undefined", "whitelist", "blacklist", "whitelistBlacklist<PERSON>heck", "key", "indexOf", "in", "_in", "state", "fullState", "out"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/node_modules/redux-persist/es/createTransform.js"], "sourcesContent": ["export default function createTransform( // @NOTE inbound: transform state coming from redux on its way to being serialized and stored\ninbound, // @NOTE outbound: transform state coming from storage, on its way to be rehydrated into redux\noutbound) {\n  var config = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  var whitelist = config.whitelist || null;\n  var blacklist = config.blacklist || null;\n\n  function whitelistBlacklistCheck(key) {\n    if (whitelist && whitelist.indexOf(key) === -1) return true;\n    if (blacklist && blacklist.indexOf(key) !== -1) return true;\n    return false;\n  }\n\n  return {\n    in: function _in(state, key, fullState) {\n      return !whitelistBlacklistCheck(key) && inbound ? inbound(state, key, fullState) : state;\n    },\n    out: function out(state, key, fullState) {\n      return !whitelistBlacklistCheck(key) && outbound ? outbound(state, key, fullState) : state;\n    }\n  };\n}"], "mappings": "AAAA,eAAe,SAASA,eAAeA;AAAE;AACzCC,OAAO;AAAE;AACTC,QAAQ,EAAE;EACR,IAAIC,MAAM,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACnF,IAAIG,SAAS,GAAGJ,MAAM,CAACI,SAAS,IAAI,IAAI;EACxC,IAAIC,SAAS,GAAGL,MAAM,CAACK,SAAS,IAAI,IAAI;EAExC,SAASC,uBAAuBA,CAACC,GAAG,EAAE;IACpC,IAAIH,SAAS,IAAIA,SAAS,CAACI,OAAO,CAACD,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,IAAI;IAC3D,IAAIF,SAAS,IAAIA,SAAS,CAACG,OAAO,CAACD,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,IAAI;IAC3D,OAAO,KAAK;EACd;EAEA,OAAO;IACLE,EAAE,EAAE,SAASC,GAAGA,CAACC,KAAK,EAAEJ,GAAG,EAAEK,SAAS,EAAE;MACtC,OAAO,CAACN,uBAAuB,CAACC,GAAG,CAAC,IAAIT,OAAO,GAAGA,OAAO,CAACa,KAAK,EAAEJ,GAAG,EAAEK,SAAS,CAAC,GAAGD,KAAK;IAC1F,CAAC;IACDE,GAAG,EAAE,SAASA,GAAGA,CAACF,KAAK,EAAEJ,GAAG,EAAEK,SAAS,EAAE;MACvC,OAAO,CAACN,uBAAuB,CAACC,GAAG,CAAC,IAAIR,QAAQ,GAAGA,QAAQ,CAACY,KAAK,EAAEJ,GAAG,EAAEK,SAAS,CAAC,GAAGD,KAAK;IAC5F;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}