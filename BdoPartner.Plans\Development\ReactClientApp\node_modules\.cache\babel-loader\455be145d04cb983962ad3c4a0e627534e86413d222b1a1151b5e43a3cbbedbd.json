{"ast": null, "code": "const isString = obj => typeof obj === 'string';\nconst defer = () => {\n  let res;\n  let rej;\n  const promise = new Promise((resolve, reject) => {\n    res = resolve;\n    rej = reject;\n  });\n  promise.resolve = res;\n  promise.reject = rej;\n  return promise;\n};\nconst makeString = object => {\n  if (object == null) return '';\n  return '' + object;\n};\nconst copy = (a, s, t) => {\n  a.forEach(m => {\n    if (s[m]) t[m] = s[m];\n  });\n};\nconst lastOfPathSeparatorRegExp = /###/g;\nconst cleanKey = key => key && key.indexOf('###') > -1 ? key.replace(lastOfPathSeparatorRegExp, '.') : key;\nconst canNotTraverseDeeper = object => !object || isString(object);\nconst getLastOfPath = (object, path, Empty) => {\n  const stack = !isString(path) ? path : path.split('.');\n  let stackIndex = 0;\n  while (stackIndex < stack.length - 1) {\n    if (canNotTraverseDeeper(object)) return {};\n    const key = cleanKey(stack[stackIndex]);\n    if (!object[key] && Empty) object[key] = new Empty();\n    if (Object.prototype.hasOwnProperty.call(object, key)) {\n      object = object[key];\n    } else {\n      object = {};\n    }\n    ++stackIndex;\n  }\n  if (canNotTraverseDeeper(object)) return {};\n  return {\n    obj: object,\n    k: cleanKey(stack[stackIndex])\n  };\n};\nconst setPath = (object, path, newValue) => {\n  const {\n    obj,\n    k\n  } = getLastOfPath(object, path, Object);\n  if (obj !== undefined || path.length === 1) {\n    obj[k] = newValue;\n    return;\n  }\n  let e = path[path.length - 1];\n  let p = path.slice(0, path.length - 1);\n  let last = getLastOfPath(object, p, Object);\n  while (last.obj === undefined && p.length) {\n    e = `${p[p.length - 1]}.${e}`;\n    p = p.slice(0, p.length - 1);\n    last = getLastOfPath(object, p, Object);\n    if (last && last.obj && typeof last.obj[`${last.k}.${e}`] !== 'undefined') {\n      last.obj = undefined;\n    }\n  }\n  last.obj[`${last.k}.${e}`] = newValue;\n};\nconst pushPath = (object, path, newValue, concat) => {\n  const {\n    obj,\n    k\n  } = getLastOfPath(object, path, Object);\n  obj[k] = obj[k] || [];\n  obj[k].push(newValue);\n};\nconst getPath = (object, path) => {\n  const {\n    obj,\n    k\n  } = getLastOfPath(object, path);\n  if (!obj) return undefined;\n  return obj[k];\n};\nconst getPathWithDefaults = (data, defaultData, key) => {\n  const value = getPath(data, key);\n  if (value !== undefined) {\n    return value;\n  }\n  return getPath(defaultData, key);\n};\nconst deepExtend = (target, source, overwrite) => {\n  for (const prop in source) {\n    if (prop !== '__proto__' && prop !== 'constructor') {\n      if (prop in target) {\n        if (isString(target[prop]) || target[prop] instanceof String || isString(source[prop]) || source[prop] instanceof String) {\n          if (overwrite) target[prop] = source[prop];\n        } else {\n          deepExtend(target[prop], source[prop], overwrite);\n        }\n      } else {\n        target[prop] = source[prop];\n      }\n    }\n  }\n  return target;\n};\nconst regexEscape = str => str.replace(/[\\-\\[\\]\\/\\{\\}\\(\\)\\*\\+\\?\\.\\\\\\^\\$\\|]/g, '\\\\$&');\nvar _entityMap = {\n  '&': '&amp;',\n  '<': '&lt;',\n  '>': '&gt;',\n  '\"': '&quot;',\n  \"'\": '&#39;',\n  '/': '&#x2F;'\n};\nconst escape = data => {\n  if (isString(data)) {\n    return data.replace(/[&<>\"'\\/]/g, s => _entityMap[s]);\n  }\n  return data;\n};\nclass RegExpCache {\n  constructor(capacity) {\n    this.capacity = capacity;\n    this.regExpMap = new Map();\n    this.regExpQueue = [];\n  }\n  getRegExp(pattern) {\n    const regExpFromCache = this.regExpMap.get(pattern);\n    if (regExpFromCache !== undefined) {\n      return regExpFromCache;\n    }\n    const regExpNew = new RegExp(pattern);\n    if (this.regExpQueue.length === this.capacity) {\n      this.regExpMap.delete(this.regExpQueue.shift());\n    }\n    this.regExpMap.set(pattern, regExpNew);\n    this.regExpQueue.push(pattern);\n    return regExpNew;\n  }\n}\nconst chars = [' ', ',', '?', '!', ';'];\nconst looksLikeObjectPathRegExpCache = new RegExpCache(20);\nconst looksLikeObjectPath = (key, nsSeparator, keySeparator) => {\n  nsSeparator = nsSeparator || '';\n  keySeparator = keySeparator || '';\n  const possibleChars = chars.filter(c => nsSeparator.indexOf(c) < 0 && keySeparator.indexOf(c) < 0);\n  if (possibleChars.length === 0) return true;\n  const r = looksLikeObjectPathRegExpCache.getRegExp(`(${possibleChars.map(c => c === '?' ? '\\\\?' : c).join('|')})`);\n  let matched = !r.test(key);\n  if (!matched) {\n    const ki = key.indexOf(keySeparator);\n    if (ki > 0 && !r.test(key.substring(0, ki))) {\n      matched = true;\n    }\n  }\n  return matched;\n};\nconst deepFind = function (obj, path) {\n  let keySeparator = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : '.';\n  if (!obj) return undefined;\n  if (obj[path]) return obj[path];\n  const tokens = path.split(keySeparator);\n  let current = obj;\n  for (let i = 0; i < tokens.length;) {\n    if (!current || typeof current !== 'object') {\n      return undefined;\n    }\n    let next;\n    let nextPath = '';\n    for (let j = i; j < tokens.length; ++j) {\n      if (j !== i) {\n        nextPath += keySeparator;\n      }\n      nextPath += tokens[j];\n      next = current[nextPath];\n      if (next !== undefined) {\n        if (['string', 'number', 'boolean'].indexOf(typeof next) > -1 && j < tokens.length - 1) {\n          continue;\n        }\n        i += j - i + 1;\n        break;\n      }\n    }\n    current = next;\n  }\n  return current;\n};\nconst getCleanedCode = code => code && code.replace('_', '-');\nconst consoleLogger = {\n  type: 'logger',\n  log(args) {\n    this.output('log', args);\n  },\n  warn(args) {\n    this.output('warn', args);\n  },\n  error(args) {\n    this.output('error', args);\n  },\n  output(type, args) {\n    if (console && console[type]) console[type].apply(console, args);\n  }\n};\nclass Logger {\n  constructor(concreteLogger) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    this.init(concreteLogger, options);\n  }\n  init(concreteLogger) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    this.prefix = options.prefix || 'i18next:';\n    this.logger = concreteLogger || consoleLogger;\n    this.options = options;\n    this.debug = options.debug;\n  }\n  log() {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    return this.forward(args, 'log', '', true);\n  }\n  warn() {\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n    return this.forward(args, 'warn', '', true);\n  }\n  error() {\n    for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n      args[_key3] = arguments[_key3];\n    }\n    return this.forward(args, 'error', '');\n  }\n  deprecate() {\n    for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {\n      args[_key4] = arguments[_key4];\n    }\n    return this.forward(args, 'warn', 'WARNING DEPRECATED: ', true);\n  }\n  forward(args, lvl, prefix, debugOnly) {\n    if (debugOnly && !this.debug) return null;\n    if (isString(args[0])) args[0] = `${prefix}${this.prefix} ${args[0]}`;\n    return this.logger[lvl](args);\n  }\n  create(moduleName) {\n    return new Logger(this.logger, {\n      ...{\n        prefix: `${this.prefix}:${moduleName}:`\n      },\n      ...this.options\n    });\n  }\n  clone(options) {\n    options = options || this.options;\n    options.prefix = options.prefix || this.prefix;\n    return new Logger(this.logger, options);\n  }\n}\nvar baseLogger = new Logger();\nclass EventEmitter {\n  constructor() {\n    this.observers = {};\n  }\n  on(events, listener) {\n    events.split(' ').forEach(event => {\n      if (!this.observers[event]) this.observers[event] = new Map();\n      const numListeners = this.observers[event].get(listener) || 0;\n      this.observers[event].set(listener, numListeners + 1);\n    });\n    return this;\n  }\n  off(event, listener) {\n    if (!this.observers[event]) return;\n    if (!listener) {\n      delete this.observers[event];\n      return;\n    }\n    this.observers[event].delete(listener);\n  }\n  emit(event) {\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n    if (this.observers[event]) {\n      const cloned = Array.from(this.observers[event].entries());\n      cloned.forEach(_ref => {\n        let [observer, numTimesAdded] = _ref;\n        for (let i = 0; i < numTimesAdded; i++) {\n          observer(...args);\n        }\n      });\n    }\n    if (this.observers['*']) {\n      const cloned = Array.from(this.observers['*'].entries());\n      cloned.forEach(_ref2 => {\n        let [observer, numTimesAdded] = _ref2;\n        for (let i = 0; i < numTimesAdded; i++) {\n          observer.apply(observer, [event, ...args]);\n        }\n      });\n    }\n  }\n}\nclass ResourceStore extends EventEmitter {\n  constructor(data) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n      ns: ['translation'],\n      defaultNS: 'translation'\n    };\n    super();\n    this.data = data || {};\n    this.options = options;\n    if (this.options.keySeparator === undefined) {\n      this.options.keySeparator = '.';\n    }\n    if (this.options.ignoreJSONStructure === undefined) {\n      this.options.ignoreJSONStructure = true;\n    }\n  }\n  addNamespaces(ns) {\n    if (this.options.ns.indexOf(ns) < 0) {\n      this.options.ns.push(ns);\n    }\n  }\n  removeNamespaces(ns) {\n    const index = this.options.ns.indexOf(ns);\n    if (index > -1) {\n      this.options.ns.splice(index, 1);\n    }\n  }\n  getResource(lng, ns, key) {\n    let options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n    const keySeparator = options.keySeparator !== undefined ? options.keySeparator : this.options.keySeparator;\n    const ignoreJSONStructure = options.ignoreJSONStructure !== undefined ? options.ignoreJSONStructure : this.options.ignoreJSONStructure;\n    let path;\n    if (lng.indexOf('.') > -1) {\n      path = lng.split('.');\n    } else {\n      path = [lng, ns];\n      if (key) {\n        if (Array.isArray(key)) {\n          path.push(...key);\n        } else if (isString(key) && keySeparator) {\n          path.push(...key.split(keySeparator));\n        } else {\n          path.push(key);\n        }\n      }\n    }\n    const result = getPath(this.data, path);\n    if (!result && !ns && !key && lng.indexOf('.') > -1) {\n      lng = path[0];\n      ns = path[1];\n      key = path.slice(2).join('.');\n    }\n    if (result || !ignoreJSONStructure || !isString(key)) return result;\n    return deepFind(this.data && this.data[lng] && this.data[lng][ns], key, keySeparator);\n  }\n  addResource(lng, ns, key, value) {\n    let options = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : {\n      silent: false\n    };\n    const keySeparator = options.keySeparator !== undefined ? options.keySeparator : this.options.keySeparator;\n    let path = [lng, ns];\n    if (key) path = path.concat(keySeparator ? key.split(keySeparator) : key);\n    if (lng.indexOf('.') > -1) {\n      path = lng.split('.');\n      value = ns;\n      ns = path[1];\n    }\n    this.addNamespaces(ns);\n    setPath(this.data, path, value);\n    if (!options.silent) this.emit('added', lng, ns, key, value);\n  }\n  addResources(lng, ns, resources) {\n    let options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {\n      silent: false\n    };\n    for (const m in resources) {\n      if (isString(resources[m]) || Array.isArray(resources[m])) this.addResource(lng, ns, m, resources[m], {\n        silent: true\n      });\n    }\n    if (!options.silent) this.emit('added', lng, ns, resources);\n  }\n  addResourceBundle(lng, ns, resources, deep, overwrite) {\n    let options = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : {\n      silent: false,\n      skipCopy: false\n    };\n    let path = [lng, ns];\n    if (lng.indexOf('.') > -1) {\n      path = lng.split('.');\n      deep = resources;\n      resources = ns;\n      ns = path[1];\n    }\n    this.addNamespaces(ns);\n    let pack = getPath(this.data, path) || {};\n    if (!options.skipCopy) resources = JSON.parse(JSON.stringify(resources));\n    if (deep) {\n      deepExtend(pack, resources, overwrite);\n    } else {\n      pack = {\n        ...pack,\n        ...resources\n      };\n    }\n    setPath(this.data, path, pack);\n    if (!options.silent) this.emit('added', lng, ns, resources);\n  }\n  removeResourceBundle(lng, ns) {\n    if (this.hasResourceBundle(lng, ns)) {\n      delete this.data[lng][ns];\n    }\n    this.removeNamespaces(ns);\n    this.emit('removed', lng, ns);\n  }\n  hasResourceBundle(lng, ns) {\n    return this.getResource(lng, ns) !== undefined;\n  }\n  getResourceBundle(lng, ns) {\n    if (!ns) ns = this.options.defaultNS;\n    if (this.options.compatibilityAPI === 'v1') return {\n      ...{},\n      ...this.getResource(lng, ns)\n    };\n    return this.getResource(lng, ns);\n  }\n  getDataByLanguage(lng) {\n    return this.data[lng];\n  }\n  hasLanguageSomeTranslations(lng) {\n    const data = this.getDataByLanguage(lng);\n    const n = data && Object.keys(data) || [];\n    return !!n.find(v => data[v] && Object.keys(data[v]).length > 0);\n  }\n  toJSON() {\n    return this.data;\n  }\n}\nvar postProcessor = {\n  processors: {},\n  addPostProcessor(module) {\n    this.processors[module.name] = module;\n  },\n  handle(processors, value, key, options, translator) {\n    processors.forEach(processor => {\n      if (this.processors[processor]) value = this.processors[processor].process(value, key, options, translator);\n    });\n    return value;\n  }\n};\nconst checkedLoadedFor = {};\nclass Translator extends EventEmitter {\n  constructor(services) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    super();\n    copy(['resourceStore', 'languageUtils', 'pluralResolver', 'interpolator', 'backendConnector', 'i18nFormat', 'utils'], services, this);\n    this.options = options;\n    if (this.options.keySeparator === undefined) {\n      this.options.keySeparator = '.';\n    }\n    this.logger = baseLogger.create('translator');\n  }\n  changeLanguage(lng) {\n    if (lng) this.language = lng;\n  }\n  exists(key) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n      interpolation: {}\n    };\n    if (key === undefined || key === null) {\n      return false;\n    }\n    const resolved = this.resolve(key, options);\n    return resolved && resolved.res !== undefined;\n  }\n  extractFromKey(key, options) {\n    let nsSeparator = options.nsSeparator !== undefined ? options.nsSeparator : this.options.nsSeparator;\n    if (nsSeparator === undefined) nsSeparator = ':';\n    const keySeparator = options.keySeparator !== undefined ? options.keySeparator : this.options.keySeparator;\n    let namespaces = options.ns || this.options.defaultNS || [];\n    const wouldCheckForNsInKey = nsSeparator && key.indexOf(nsSeparator) > -1;\n    const seemsNaturalLanguage = !this.options.userDefinedKeySeparator && !options.keySeparator && !this.options.userDefinedNsSeparator && !options.nsSeparator && !looksLikeObjectPath(key, nsSeparator, keySeparator);\n    if (wouldCheckForNsInKey && !seemsNaturalLanguage) {\n      const m = key.match(this.interpolator.nestingRegexp);\n      if (m && m.length > 0) {\n        return {\n          key,\n          namespaces: isString(namespaces) ? [namespaces] : namespaces\n        };\n      }\n      const parts = key.split(nsSeparator);\n      if (nsSeparator !== keySeparator || nsSeparator === keySeparator && this.options.ns.indexOf(parts[0]) > -1) namespaces = parts.shift();\n      key = parts.join(keySeparator);\n    }\n    return {\n      key,\n      namespaces: isString(namespaces) ? [namespaces] : namespaces\n    };\n  }\n  translate(keys, options, lastKey) {\n    if (typeof options !== 'object' && this.options.overloadTranslationOptionHandler) {\n      options = this.options.overloadTranslationOptionHandler(arguments);\n    }\n    if (typeof options === 'object') options = {\n      ...options\n    };\n    if (!options) options = {};\n    if (keys === undefined || keys === null) return '';\n    if (!Array.isArray(keys)) keys = [String(keys)];\n    const returnDetails = options.returnDetails !== undefined ? options.returnDetails : this.options.returnDetails;\n    const keySeparator = options.keySeparator !== undefined ? options.keySeparator : this.options.keySeparator;\n    const {\n      key,\n      namespaces\n    } = this.extractFromKey(keys[keys.length - 1], options);\n    const namespace = namespaces[namespaces.length - 1];\n    const lng = options.lng || this.language;\n    const appendNamespaceToCIMode = options.appendNamespaceToCIMode || this.options.appendNamespaceToCIMode;\n    if (lng && lng.toLowerCase() === 'cimode') {\n      if (appendNamespaceToCIMode) {\n        const nsSeparator = options.nsSeparator || this.options.nsSeparator;\n        if (returnDetails) {\n          return {\n            res: `${namespace}${nsSeparator}${key}`,\n            usedKey: key,\n            exactUsedKey: key,\n            usedLng: lng,\n            usedNS: namespace,\n            usedParams: this.getUsedParamsDetails(options)\n          };\n        }\n        return `${namespace}${nsSeparator}${key}`;\n      }\n      if (returnDetails) {\n        return {\n          res: key,\n          usedKey: key,\n          exactUsedKey: key,\n          usedLng: lng,\n          usedNS: namespace,\n          usedParams: this.getUsedParamsDetails(options)\n        };\n      }\n      return key;\n    }\n    const resolved = this.resolve(keys, options);\n    let res = resolved && resolved.res;\n    const resUsedKey = resolved && resolved.usedKey || key;\n    const resExactUsedKey = resolved && resolved.exactUsedKey || key;\n    const resType = Object.prototype.toString.apply(res);\n    const noObject = ['[object Number]', '[object Function]', '[object RegExp]'];\n    const joinArrays = options.joinArrays !== undefined ? options.joinArrays : this.options.joinArrays;\n    const handleAsObjectInI18nFormat = !this.i18nFormat || this.i18nFormat.handleAsObject;\n    const handleAsObject = !isString(res) && typeof res !== 'boolean' && typeof res !== 'number';\n    if (handleAsObjectInI18nFormat && res && handleAsObject && noObject.indexOf(resType) < 0 && !(isString(joinArrays) && Array.isArray(res))) {\n      if (!options.returnObjects && !this.options.returnObjects) {\n        if (!this.options.returnedObjectHandler) {\n          this.logger.warn('accessing an object - but returnObjects options is not enabled!');\n        }\n        const r = this.options.returnedObjectHandler ? this.options.returnedObjectHandler(resUsedKey, res, {\n          ...options,\n          ns: namespaces\n        }) : `key '${key} (${this.language})' returned an object instead of string.`;\n        if (returnDetails) {\n          resolved.res = r;\n          resolved.usedParams = this.getUsedParamsDetails(options);\n          return resolved;\n        }\n        return r;\n      }\n      if (keySeparator) {\n        const resTypeIsArray = Array.isArray(res);\n        const copy = resTypeIsArray ? [] : {};\n        const newKeyToUse = resTypeIsArray ? resExactUsedKey : resUsedKey;\n        for (const m in res) {\n          if (Object.prototype.hasOwnProperty.call(res, m)) {\n            const deepKey = `${newKeyToUse}${keySeparator}${m}`;\n            copy[m] = this.translate(deepKey, {\n              ...options,\n              ...{\n                joinArrays: false,\n                ns: namespaces\n              }\n            });\n            if (copy[m] === deepKey) copy[m] = res[m];\n          }\n        }\n        res = copy;\n      }\n    } else if (handleAsObjectInI18nFormat && isString(joinArrays) && Array.isArray(res)) {\n      res = res.join(joinArrays);\n      if (res) res = this.extendTranslation(res, keys, options, lastKey);\n    } else {\n      let usedDefault = false;\n      let usedKey = false;\n      const needsPluralHandling = options.count !== undefined && !isString(options.count);\n      const hasDefaultValue = Translator.hasDefaultValue(options);\n      const defaultValueSuffix = needsPluralHandling ? this.pluralResolver.getSuffix(lng, options.count, options) : '';\n      const defaultValueSuffixOrdinalFallback = options.ordinal && needsPluralHandling ? this.pluralResolver.getSuffix(lng, options.count, {\n        ordinal: false\n      }) : '';\n      const needsZeroSuffixLookup = needsPluralHandling && !options.ordinal && options.count === 0 && this.pluralResolver.shouldUseIntlApi();\n      const defaultValue = needsZeroSuffixLookup && options[`defaultValue${this.options.pluralSeparator}zero`] || options[`defaultValue${defaultValueSuffix}`] || options[`defaultValue${defaultValueSuffixOrdinalFallback}`] || options.defaultValue;\n      if (!this.isValidLookup(res) && hasDefaultValue) {\n        usedDefault = true;\n        res = defaultValue;\n      }\n      if (!this.isValidLookup(res)) {\n        usedKey = true;\n        res = key;\n      }\n      const missingKeyNoValueFallbackToKey = options.missingKeyNoValueFallbackToKey || this.options.missingKeyNoValueFallbackToKey;\n      const resForMissing = missingKeyNoValueFallbackToKey && usedKey ? undefined : res;\n      const updateMissing = hasDefaultValue && defaultValue !== res && this.options.updateMissing;\n      if (usedKey || usedDefault || updateMissing) {\n        this.logger.log(updateMissing ? 'updateKey' : 'missingKey', lng, namespace, key, updateMissing ? defaultValue : res);\n        if (keySeparator) {\n          const fk = this.resolve(key, {\n            ...options,\n            keySeparator: false\n          });\n          if (fk && fk.res) this.logger.warn('Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.');\n        }\n        let lngs = [];\n        const fallbackLngs = this.languageUtils.getFallbackCodes(this.options.fallbackLng, options.lng || this.language);\n        if (this.options.saveMissingTo === 'fallback' && fallbackLngs && fallbackLngs[0]) {\n          for (let i = 0; i < fallbackLngs.length; i++) {\n            lngs.push(fallbackLngs[i]);\n          }\n        } else if (this.options.saveMissingTo === 'all') {\n          lngs = this.languageUtils.toResolveHierarchy(options.lng || this.language);\n        } else {\n          lngs.push(options.lng || this.language);\n        }\n        const send = (l, k, specificDefaultValue) => {\n          const defaultForMissing = hasDefaultValue && specificDefaultValue !== res ? specificDefaultValue : resForMissing;\n          if (this.options.missingKeyHandler) {\n            this.options.missingKeyHandler(l, namespace, k, defaultForMissing, updateMissing, options);\n          } else if (this.backendConnector && this.backendConnector.saveMissing) {\n            this.backendConnector.saveMissing(l, namespace, k, defaultForMissing, updateMissing, options);\n          }\n          this.emit('missingKey', l, namespace, k, res);\n        };\n        if (this.options.saveMissing) {\n          if (this.options.saveMissingPlurals && needsPluralHandling) {\n            lngs.forEach(language => {\n              const suffixes = this.pluralResolver.getSuffixes(language, options);\n              if (needsZeroSuffixLookup && options[`defaultValue${this.options.pluralSeparator}zero`] && suffixes.indexOf(`${this.options.pluralSeparator}zero`) < 0) {\n                suffixes.push(`${this.options.pluralSeparator}zero`);\n              }\n              suffixes.forEach(suffix => {\n                send([language], key + suffix, options[`defaultValue${suffix}`] || defaultValue);\n              });\n            });\n          } else {\n            send(lngs, key, defaultValue);\n          }\n        }\n      }\n      res = this.extendTranslation(res, keys, options, resolved, lastKey);\n      if (usedKey && res === key && this.options.appendNamespaceToMissingKey) res = `${namespace}:${key}`;\n      if ((usedKey || usedDefault) && this.options.parseMissingKeyHandler) {\n        if (this.options.compatibilityAPI !== 'v1') {\n          res = this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey ? `${namespace}:${key}` : key, usedDefault ? res : undefined);\n        } else {\n          res = this.options.parseMissingKeyHandler(res);\n        }\n      }\n    }\n    if (returnDetails) {\n      resolved.res = res;\n      resolved.usedParams = this.getUsedParamsDetails(options);\n      return resolved;\n    }\n    return res;\n  }\n  extendTranslation(res, key, options, resolved, lastKey) {\n    var _this = this;\n    if (this.i18nFormat && this.i18nFormat.parse) {\n      res = this.i18nFormat.parse(res, {\n        ...this.options.interpolation.defaultVariables,\n        ...options\n      }, options.lng || this.language || resolved.usedLng, resolved.usedNS, resolved.usedKey, {\n        resolved\n      });\n    } else if (!options.skipInterpolation) {\n      if (options.interpolation) this.interpolator.init({\n        ...options,\n        ...{\n          interpolation: {\n            ...this.options.interpolation,\n            ...options.interpolation\n          }\n        }\n      });\n      const skipOnVariables = isString(res) && (options && options.interpolation && options.interpolation.skipOnVariables !== undefined ? options.interpolation.skipOnVariables : this.options.interpolation.skipOnVariables);\n      let nestBef;\n      if (skipOnVariables) {\n        const nb = res.match(this.interpolator.nestingRegexp);\n        nestBef = nb && nb.length;\n      }\n      let data = options.replace && !isString(options.replace) ? options.replace : options;\n      if (this.options.interpolation.defaultVariables) data = {\n        ...this.options.interpolation.defaultVariables,\n        ...data\n      };\n      res = this.interpolator.interpolate(res, data, options.lng || this.language || resolved.usedLng, options);\n      if (skipOnVariables) {\n        const na = res.match(this.interpolator.nestingRegexp);\n        const nestAft = na && na.length;\n        if (nestBef < nestAft) options.nest = false;\n      }\n      if (!options.lng && this.options.compatibilityAPI !== 'v1' && resolved && resolved.res) options.lng = this.language || resolved.usedLng;\n      if (options.nest !== false) res = this.interpolator.nest(res, function () {\n        for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n          args[_key] = arguments[_key];\n        }\n        if (lastKey && lastKey[0] === args[0] && !options.context) {\n          _this.logger.warn(`It seems you are nesting recursively key: ${args[0]} in key: ${key[0]}`);\n          return null;\n        }\n        return _this.translate(...args, key);\n      }, options);\n      if (options.interpolation) this.interpolator.reset();\n    }\n    const postProcess = options.postProcess || this.options.postProcess;\n    const postProcessorNames = isString(postProcess) ? [postProcess] : postProcess;\n    if (res !== undefined && res !== null && postProcessorNames && postProcessorNames.length && options.applyPostProcessor !== false) {\n      res = postProcessor.handle(postProcessorNames, res, key, this.options && this.options.postProcessPassResolved ? {\n        i18nResolved: {\n          ...resolved,\n          usedParams: this.getUsedParamsDetails(options)\n        },\n        ...options\n      } : options, this);\n    }\n    return res;\n  }\n  resolve(keys) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    let found;\n    let usedKey;\n    let exactUsedKey;\n    let usedLng;\n    let usedNS;\n    if (isString(keys)) keys = [keys];\n    keys.forEach(k => {\n      if (this.isValidLookup(found)) return;\n      const extracted = this.extractFromKey(k, options);\n      const key = extracted.key;\n      usedKey = key;\n      let namespaces = extracted.namespaces;\n      if (this.options.fallbackNS) namespaces = namespaces.concat(this.options.fallbackNS);\n      const needsPluralHandling = options.count !== undefined && !isString(options.count);\n      const needsZeroSuffixLookup = needsPluralHandling && !options.ordinal && options.count === 0 && this.pluralResolver.shouldUseIntlApi();\n      const needsContextHandling = options.context !== undefined && (isString(options.context) || typeof options.context === 'number') && options.context !== '';\n      const codes = options.lngs ? options.lngs : this.languageUtils.toResolveHierarchy(options.lng || this.language, options.fallbackLng);\n      namespaces.forEach(ns => {\n        if (this.isValidLookup(found)) return;\n        usedNS = ns;\n        if (!checkedLoadedFor[`${codes[0]}-${ns}`] && this.utils && this.utils.hasLoadedNamespace && !this.utils.hasLoadedNamespace(usedNS)) {\n          checkedLoadedFor[`${codes[0]}-${ns}`] = true;\n          this.logger.warn(`key \"${usedKey}\" for languages \"${codes.join(', ')}\" won't get resolved as namespace \"${usedNS}\" was not yet loaded`, 'This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!');\n        }\n        codes.forEach(code => {\n          if (this.isValidLookup(found)) return;\n          usedLng = code;\n          const finalKeys = [key];\n          if (this.i18nFormat && this.i18nFormat.addLookupKeys) {\n            this.i18nFormat.addLookupKeys(finalKeys, key, code, ns, options);\n          } else {\n            let pluralSuffix;\n            if (needsPluralHandling) pluralSuffix = this.pluralResolver.getSuffix(code, options.count, options);\n            const zeroSuffix = `${this.options.pluralSeparator}zero`;\n            const ordinalPrefix = `${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;\n            if (needsPluralHandling) {\n              finalKeys.push(key + pluralSuffix);\n              if (options.ordinal && pluralSuffix.indexOf(ordinalPrefix) === 0) {\n                finalKeys.push(key + pluralSuffix.replace(ordinalPrefix, this.options.pluralSeparator));\n              }\n              if (needsZeroSuffixLookup) {\n                finalKeys.push(key + zeroSuffix);\n              }\n            }\n            if (needsContextHandling) {\n              const contextKey = `${key}${this.options.contextSeparator}${options.context}`;\n              finalKeys.push(contextKey);\n              if (needsPluralHandling) {\n                finalKeys.push(contextKey + pluralSuffix);\n                if (options.ordinal && pluralSuffix.indexOf(ordinalPrefix) === 0) {\n                  finalKeys.push(contextKey + pluralSuffix.replace(ordinalPrefix, this.options.pluralSeparator));\n                }\n                if (needsZeroSuffixLookup) {\n                  finalKeys.push(contextKey + zeroSuffix);\n                }\n              }\n            }\n          }\n          let possibleKey;\n          while (possibleKey = finalKeys.pop()) {\n            if (!this.isValidLookup(found)) {\n              exactUsedKey = possibleKey;\n              found = this.getResource(code, ns, possibleKey, options);\n            }\n          }\n        });\n      });\n    });\n    return {\n      res: found,\n      usedKey,\n      exactUsedKey,\n      usedLng,\n      usedNS\n    };\n  }\n  isValidLookup(res) {\n    return res !== undefined && !(!this.options.returnNull && res === null) && !(!this.options.returnEmptyString && res === '');\n  }\n  getResource(code, ns, key) {\n    let options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n    if (this.i18nFormat && this.i18nFormat.getResource) return this.i18nFormat.getResource(code, ns, key, options);\n    return this.resourceStore.getResource(code, ns, key, options);\n  }\n  getUsedParamsDetails() {\n    let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    const optionsKeys = ['defaultValue', 'ordinal', 'context', 'replace', 'lng', 'lngs', 'fallbackLng', 'ns', 'keySeparator', 'nsSeparator', 'returnObjects', 'returnDetails', 'joinArrays', 'postProcess', 'interpolation'];\n    const useOptionsReplaceForData = options.replace && !isString(options.replace);\n    let data = useOptionsReplaceForData ? options.replace : options;\n    if (useOptionsReplaceForData && typeof options.count !== 'undefined') {\n      data.count = options.count;\n    }\n    if (this.options.interpolation.defaultVariables) {\n      data = {\n        ...this.options.interpolation.defaultVariables,\n        ...data\n      };\n    }\n    if (!useOptionsReplaceForData) {\n      data = {\n        ...data\n      };\n      for (const key of optionsKeys) {\n        delete data[key];\n      }\n    }\n    return data;\n  }\n  static hasDefaultValue(options) {\n    const prefix = 'defaultValue';\n    for (const option in options) {\n      if (Object.prototype.hasOwnProperty.call(options, option) && prefix === option.substring(0, prefix.length) && undefined !== options[option]) {\n        return true;\n      }\n    }\n    return false;\n  }\n}\nconst capitalize = string => string.charAt(0).toUpperCase() + string.slice(1);\nclass LanguageUtil {\n  constructor(options) {\n    this.options = options;\n    this.supportedLngs = this.options.supportedLngs || false;\n    this.logger = baseLogger.create('languageUtils');\n  }\n  getScriptPartFromCode(code) {\n    code = getCleanedCode(code);\n    if (!code || code.indexOf('-') < 0) return null;\n    const p = code.split('-');\n    if (p.length === 2) return null;\n    p.pop();\n    if (p[p.length - 1].toLowerCase() === 'x') return null;\n    return this.formatLanguageCode(p.join('-'));\n  }\n  getLanguagePartFromCode(code) {\n    code = getCleanedCode(code);\n    if (!code || code.indexOf('-') < 0) return code;\n    const p = code.split('-');\n    return this.formatLanguageCode(p[0]);\n  }\n  formatLanguageCode(code) {\n    if (isString(code) && code.indexOf('-') > -1) {\n      if (typeof Intl !== 'undefined' && typeof Intl.getCanonicalLocales !== 'undefined') {\n        try {\n          let formattedCode = Intl.getCanonicalLocales(code)[0];\n          if (formattedCode && this.options.lowerCaseLng) {\n            formattedCode = formattedCode.toLowerCase();\n          }\n          if (formattedCode) return formattedCode;\n        } catch (e) {}\n      }\n      const specialCases = ['hans', 'hant', 'latn', 'cyrl', 'cans', 'mong', 'arab'];\n      let p = code.split('-');\n      if (this.options.lowerCaseLng) {\n        p = p.map(part => part.toLowerCase());\n      } else if (p.length === 2) {\n        p[0] = p[0].toLowerCase();\n        p[1] = p[1].toUpperCase();\n        if (specialCases.indexOf(p[1].toLowerCase()) > -1) p[1] = capitalize(p[1].toLowerCase());\n      } else if (p.length === 3) {\n        p[0] = p[0].toLowerCase();\n        if (p[1].length === 2) p[1] = p[1].toUpperCase();\n        if (p[0] !== 'sgn' && p[2].length === 2) p[2] = p[2].toUpperCase();\n        if (specialCases.indexOf(p[1].toLowerCase()) > -1) p[1] = capitalize(p[1].toLowerCase());\n        if (specialCases.indexOf(p[2].toLowerCase()) > -1) p[2] = capitalize(p[2].toLowerCase());\n      }\n      return p.join('-');\n    }\n    return this.options.cleanCode || this.options.lowerCaseLng ? code.toLowerCase() : code;\n  }\n  isSupportedCode(code) {\n    if (this.options.load === 'languageOnly' || this.options.nonExplicitSupportedLngs) {\n      code = this.getLanguagePartFromCode(code);\n    }\n    return !this.supportedLngs || !this.supportedLngs.length || this.supportedLngs.indexOf(code) > -1;\n  }\n  getBestMatchFromCodes(codes) {\n    if (!codes) return null;\n    let found;\n    codes.forEach(code => {\n      if (found) return;\n      const cleanedLng = this.formatLanguageCode(code);\n      if (!this.options.supportedLngs || this.isSupportedCode(cleanedLng)) found = cleanedLng;\n    });\n    if (!found && this.options.supportedLngs) {\n      codes.forEach(code => {\n        if (found) return;\n        const lngOnly = this.getLanguagePartFromCode(code);\n        if (this.isSupportedCode(lngOnly)) return found = lngOnly;\n        found = this.options.supportedLngs.find(supportedLng => {\n          if (supportedLng === lngOnly) return supportedLng;\n          if (supportedLng.indexOf('-') < 0 && lngOnly.indexOf('-') < 0) return;\n          if (supportedLng.indexOf('-') > 0 && lngOnly.indexOf('-') < 0 && supportedLng.substring(0, supportedLng.indexOf('-')) === lngOnly) return supportedLng;\n          if (supportedLng.indexOf(lngOnly) === 0 && lngOnly.length > 1) return supportedLng;\n        });\n      });\n    }\n    if (!found) found = this.getFallbackCodes(this.options.fallbackLng)[0];\n    return found;\n  }\n  getFallbackCodes(fallbacks, code) {\n    if (!fallbacks) return [];\n    if (typeof fallbacks === 'function') fallbacks = fallbacks(code);\n    if (isString(fallbacks)) fallbacks = [fallbacks];\n    if (Array.isArray(fallbacks)) return fallbacks;\n    if (!code) return fallbacks.default || [];\n    let found = fallbacks[code];\n    if (!found) found = fallbacks[this.getScriptPartFromCode(code)];\n    if (!found) found = fallbacks[this.formatLanguageCode(code)];\n    if (!found) found = fallbacks[this.getLanguagePartFromCode(code)];\n    if (!found) found = fallbacks.default;\n    return found || [];\n  }\n  toResolveHierarchy(code, fallbackCode) {\n    const fallbackCodes = this.getFallbackCodes(fallbackCode || this.options.fallbackLng || [], code);\n    const codes = [];\n    const addCode = c => {\n      if (!c) return;\n      if (this.isSupportedCode(c)) {\n        codes.push(c);\n      } else {\n        this.logger.warn(`rejecting language code not found in supportedLngs: ${c}`);\n      }\n    };\n    if (isString(code) && (code.indexOf('-') > -1 || code.indexOf('_') > -1)) {\n      if (this.options.load !== 'languageOnly') addCode(this.formatLanguageCode(code));\n      if (this.options.load !== 'languageOnly' && this.options.load !== 'currentOnly') addCode(this.getScriptPartFromCode(code));\n      if (this.options.load !== 'currentOnly') addCode(this.getLanguagePartFromCode(code));\n    } else if (isString(code)) {\n      addCode(this.formatLanguageCode(code));\n    }\n    fallbackCodes.forEach(fc => {\n      if (codes.indexOf(fc) < 0) addCode(this.formatLanguageCode(fc));\n    });\n    return codes;\n  }\n}\nlet sets = [{\n  lngs: ['ach', 'ak', 'am', 'arn', 'br', 'fil', 'gun', 'ln', 'mfe', 'mg', 'mi', 'oc', 'pt', 'pt-BR', 'tg', 'tl', 'ti', 'tr', 'uz', 'wa'],\n  nr: [1, 2],\n  fc: 1\n}, {\n  lngs: ['af', 'an', 'ast', 'az', 'bg', 'bn', 'ca', 'da', 'de', 'dev', 'el', 'en', 'eo', 'es', 'et', 'eu', 'fi', 'fo', 'fur', 'fy', 'gl', 'gu', 'ha', 'hi', 'hu', 'hy', 'ia', 'it', 'kk', 'kn', 'ku', 'lb', 'mai', 'ml', 'mn', 'mr', 'nah', 'nap', 'nb', 'ne', 'nl', 'nn', 'no', 'nso', 'pa', 'pap', 'pms', 'ps', 'pt-PT', 'rm', 'sco', 'se', 'si', 'so', 'son', 'sq', 'sv', 'sw', 'ta', 'te', 'tk', 'ur', 'yo'],\n  nr: [1, 2],\n  fc: 2\n}, {\n  lngs: ['ay', 'bo', 'cgg', 'fa', 'ht', 'id', 'ja', 'jbo', 'ka', 'km', 'ko', 'ky', 'lo', 'ms', 'sah', 'su', 'th', 'tt', 'ug', 'vi', 'wo', 'zh'],\n  nr: [1],\n  fc: 3\n}, {\n  lngs: ['be', 'bs', 'cnr', 'dz', 'hr', 'ru', 'sr', 'uk'],\n  nr: [1, 2, 5],\n  fc: 4\n}, {\n  lngs: ['ar'],\n  nr: [0, 1, 2, 3, 11, 100],\n  fc: 5\n}, {\n  lngs: ['cs', 'sk'],\n  nr: [1, 2, 5],\n  fc: 6\n}, {\n  lngs: ['csb', 'pl'],\n  nr: [1, 2, 5],\n  fc: 7\n}, {\n  lngs: ['cy'],\n  nr: [1, 2, 3, 8],\n  fc: 8\n}, {\n  lngs: ['fr'],\n  nr: [1, 2],\n  fc: 9\n}, {\n  lngs: ['ga'],\n  nr: [1, 2, 3, 7, 11],\n  fc: 10\n}, {\n  lngs: ['gd'],\n  nr: [1, 2, 3, 20],\n  fc: 11\n}, {\n  lngs: ['is'],\n  nr: [1, 2],\n  fc: 12\n}, {\n  lngs: ['jv'],\n  nr: [0, 1],\n  fc: 13\n}, {\n  lngs: ['kw'],\n  nr: [1, 2, 3, 4],\n  fc: 14\n}, {\n  lngs: ['lt'],\n  nr: [1, 2, 10],\n  fc: 15\n}, {\n  lngs: ['lv'],\n  nr: [1, 2, 0],\n  fc: 16\n}, {\n  lngs: ['mk'],\n  nr: [1, 2],\n  fc: 17\n}, {\n  lngs: ['mnk'],\n  nr: [0, 1, 2],\n  fc: 18\n}, {\n  lngs: ['mt'],\n  nr: [1, 2, 11, 20],\n  fc: 19\n}, {\n  lngs: ['or'],\n  nr: [2, 1],\n  fc: 2\n}, {\n  lngs: ['ro'],\n  nr: [1, 2, 20],\n  fc: 20\n}, {\n  lngs: ['sl'],\n  nr: [5, 1, 2, 3],\n  fc: 21\n}, {\n  lngs: ['he', 'iw'],\n  nr: [1, 2, 20, 21],\n  fc: 22\n}];\nlet _rulesPluralsTypes = {\n  1: n => Number(n > 1),\n  2: n => Number(n != 1),\n  3: n => 0,\n  4: n => Number(n % 10 == 1 && n % 100 != 11 ? 0 : n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 10 || n % 100 >= 20) ? 1 : 2),\n  5: n => Number(n == 0 ? 0 : n == 1 ? 1 : n == 2 ? 2 : n % 100 >= 3 && n % 100 <= 10 ? 3 : n % 100 >= 11 ? 4 : 5),\n  6: n => Number(n == 1 ? 0 : n >= 2 && n <= 4 ? 1 : 2),\n  7: n => Number(n == 1 ? 0 : n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 10 || n % 100 >= 20) ? 1 : 2),\n  8: n => Number(n == 1 ? 0 : n == 2 ? 1 : n != 8 && n != 11 ? 2 : 3),\n  9: n => Number(n >= 2),\n  10: n => Number(n == 1 ? 0 : n == 2 ? 1 : n < 7 ? 2 : n < 11 ? 3 : 4),\n  11: n => Number(n == 1 || n == 11 ? 0 : n == 2 || n == 12 ? 1 : n > 2 && n < 20 ? 2 : 3),\n  12: n => Number(n % 10 != 1 || n % 100 == 11),\n  13: n => Number(n !== 0),\n  14: n => Number(n == 1 ? 0 : n == 2 ? 1 : n == 3 ? 2 : 3),\n  15: n => Number(n % 10 == 1 && n % 100 != 11 ? 0 : n % 10 >= 2 && (n % 100 < 10 || n % 100 >= 20) ? 1 : 2),\n  16: n => Number(n % 10 == 1 && n % 100 != 11 ? 0 : n !== 0 ? 1 : 2),\n  17: n => Number(n == 1 || n % 10 == 1 && n % 100 != 11 ? 0 : 1),\n  18: n => Number(n == 0 ? 0 : n == 1 ? 1 : 2),\n  19: n => Number(n == 1 ? 0 : n == 0 || n % 100 > 1 && n % 100 < 11 ? 1 : n % 100 > 10 && n % 100 < 20 ? 2 : 3),\n  20: n => Number(n == 1 ? 0 : n == 0 || n % 100 > 0 && n % 100 < 20 ? 1 : 2),\n  21: n => Number(n % 100 == 1 ? 1 : n % 100 == 2 ? 2 : n % 100 == 3 || n % 100 == 4 ? 3 : 0),\n  22: n => Number(n == 1 ? 0 : n == 2 ? 1 : (n < 0 || n > 10) && n % 10 == 0 ? 2 : 3)\n};\nconst nonIntlVersions = ['v1', 'v2', 'v3'];\nconst intlVersions = ['v4'];\nconst suffixesOrder = {\n  zero: 0,\n  one: 1,\n  two: 2,\n  few: 3,\n  many: 4,\n  other: 5\n};\nconst createRules = () => {\n  const rules = {};\n  sets.forEach(set => {\n    set.lngs.forEach(l => {\n      rules[l] = {\n        numbers: set.nr,\n        plurals: _rulesPluralsTypes[set.fc]\n      };\n    });\n  });\n  return rules;\n};\nclass PluralResolver {\n  constructor(languageUtils) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    this.languageUtils = languageUtils;\n    this.options = options;\n    this.logger = baseLogger.create('pluralResolver');\n    if ((!this.options.compatibilityJSON || intlVersions.includes(this.options.compatibilityJSON)) && (typeof Intl === 'undefined' || !Intl.PluralRules)) {\n      this.options.compatibilityJSON = 'v3';\n      this.logger.error('Your environment seems not to be Intl API compatible, use an Intl.PluralRules polyfill. Will fallback to the compatibilityJSON v3 format handling.');\n    }\n    this.rules = createRules();\n    this.pluralRulesCache = {};\n  }\n  addRule(lng, obj) {\n    this.rules[lng] = obj;\n  }\n  clearCache() {\n    this.pluralRulesCache = {};\n  }\n  getRule(code) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    if (this.shouldUseIntlApi()) {\n      const cleanedCode = getCleanedCode(code === 'dev' ? 'en' : code);\n      const type = options.ordinal ? 'ordinal' : 'cardinal';\n      const cacheKey = JSON.stringify({\n        cleanedCode,\n        type\n      });\n      if (cacheKey in this.pluralRulesCache) {\n        return this.pluralRulesCache[cacheKey];\n      }\n      let rule;\n      try {\n        rule = new Intl.PluralRules(cleanedCode, {\n          type\n        });\n      } catch (err) {\n        if (!code.match(/-|_/)) return;\n        const lngPart = this.languageUtils.getLanguagePartFromCode(code);\n        rule = this.getRule(lngPart, options);\n      }\n      this.pluralRulesCache[cacheKey] = rule;\n      return rule;\n    }\n    return this.rules[code] || this.rules[this.languageUtils.getLanguagePartFromCode(code)];\n  }\n  needsPlural(code) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    const rule = this.getRule(code, options);\n    if (this.shouldUseIntlApi()) {\n      return rule && rule.resolvedOptions().pluralCategories.length > 1;\n    }\n    return rule && rule.numbers.length > 1;\n  }\n  getPluralFormsOfKey(code, key) {\n    let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    return this.getSuffixes(code, options).map(suffix => `${key}${suffix}`);\n  }\n  getSuffixes(code) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    const rule = this.getRule(code, options);\n    if (!rule) {\n      return [];\n    }\n    if (this.shouldUseIntlApi()) {\n      return rule.resolvedOptions().pluralCategories.sort((pluralCategory1, pluralCategory2) => suffixesOrder[pluralCategory1] - suffixesOrder[pluralCategory2]).map(pluralCategory => `${this.options.prepend}${options.ordinal ? `ordinal${this.options.prepend}` : ''}${pluralCategory}`);\n    }\n    return rule.numbers.map(number => this.getSuffix(code, number, options));\n  }\n  getSuffix(code, count) {\n    let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    const rule = this.getRule(code, options);\n    if (rule) {\n      if (this.shouldUseIntlApi()) {\n        return `${this.options.prepend}${options.ordinal ? `ordinal${this.options.prepend}` : ''}${rule.select(count)}`;\n      }\n      return this.getSuffixRetroCompatible(rule, count);\n    }\n    this.logger.warn(`no plural rule found for: ${code}`);\n    return '';\n  }\n  getSuffixRetroCompatible(rule, count) {\n    const idx = rule.noAbs ? rule.plurals(count) : rule.plurals(Math.abs(count));\n    let suffix = rule.numbers[idx];\n    if (this.options.simplifyPluralSuffix && rule.numbers.length === 2 && rule.numbers[0] === 1) {\n      if (suffix === 2) {\n        suffix = 'plural';\n      } else if (suffix === 1) {\n        suffix = '';\n      }\n    }\n    const returnSuffix = () => this.options.prepend && suffix.toString() ? this.options.prepend + suffix.toString() : suffix.toString();\n    if (this.options.compatibilityJSON === 'v1') {\n      if (suffix === 1) return '';\n      if (typeof suffix === 'number') return `_plural_${suffix.toString()}`;\n      return returnSuffix();\n    } else if (this.options.compatibilityJSON === 'v2') {\n      return returnSuffix();\n    } else if (this.options.simplifyPluralSuffix && rule.numbers.length === 2 && rule.numbers[0] === 1) {\n      return returnSuffix();\n    }\n    return this.options.prepend && idx.toString() ? this.options.prepend + idx.toString() : idx.toString();\n  }\n  shouldUseIntlApi() {\n    return !nonIntlVersions.includes(this.options.compatibilityJSON);\n  }\n}\nconst deepFindWithDefaults = function (data, defaultData, key) {\n  let keySeparator = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : '.';\n  let ignoreJSONStructure = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : true;\n  let path = getPathWithDefaults(data, defaultData, key);\n  if (!path && ignoreJSONStructure && isString(key)) {\n    path = deepFind(data, key, keySeparator);\n    if (path === undefined) path = deepFind(defaultData, key, keySeparator);\n  }\n  return path;\n};\nconst regexSafe = val => val.replace(/\\$/g, '$$$$');\nclass Interpolator {\n  constructor() {\n    let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    this.logger = baseLogger.create('interpolator');\n    this.options = options;\n    this.format = options.interpolation && options.interpolation.format || (value => value);\n    this.init(options);\n  }\n  init() {\n    let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    if (!options.interpolation) options.interpolation = {\n      escapeValue: true\n    };\n    const {\n      escape: escape$1,\n      escapeValue,\n      useRawValueToEscape,\n      prefix,\n      prefixEscaped,\n      suffix,\n      suffixEscaped,\n      formatSeparator,\n      unescapeSuffix,\n      unescapePrefix,\n      nestingPrefix,\n      nestingPrefixEscaped,\n      nestingSuffix,\n      nestingSuffixEscaped,\n      nestingOptionsSeparator,\n      maxReplaces,\n      alwaysFormat\n    } = options.interpolation;\n    this.escape = escape$1 !== undefined ? escape$1 : escape;\n    this.escapeValue = escapeValue !== undefined ? escapeValue : true;\n    this.useRawValueToEscape = useRawValueToEscape !== undefined ? useRawValueToEscape : false;\n    this.prefix = prefix ? regexEscape(prefix) : prefixEscaped || '{{';\n    this.suffix = suffix ? regexEscape(suffix) : suffixEscaped || '}}';\n    this.formatSeparator = formatSeparator || ',';\n    this.unescapePrefix = unescapeSuffix ? '' : unescapePrefix || '-';\n    this.unescapeSuffix = this.unescapePrefix ? '' : unescapeSuffix || '';\n    this.nestingPrefix = nestingPrefix ? regexEscape(nestingPrefix) : nestingPrefixEscaped || regexEscape('$t(');\n    this.nestingSuffix = nestingSuffix ? regexEscape(nestingSuffix) : nestingSuffixEscaped || regexEscape(')');\n    this.nestingOptionsSeparator = nestingOptionsSeparator || ',';\n    this.maxReplaces = maxReplaces || 1000;\n    this.alwaysFormat = alwaysFormat !== undefined ? alwaysFormat : false;\n    this.resetRegExp();\n  }\n  reset() {\n    if (this.options) this.init(this.options);\n  }\n  resetRegExp() {\n    const getOrResetRegExp = (existingRegExp, pattern) => {\n      if (existingRegExp && existingRegExp.source === pattern) {\n        existingRegExp.lastIndex = 0;\n        return existingRegExp;\n      }\n      return new RegExp(pattern, 'g');\n    };\n    this.regexp = getOrResetRegExp(this.regexp, `${this.prefix}(.+?)${this.suffix}`);\n    this.regexpUnescape = getOrResetRegExp(this.regexpUnescape, `${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`);\n    this.nestingRegexp = getOrResetRegExp(this.nestingRegexp, `${this.nestingPrefix}(.+?)${this.nestingSuffix}`);\n  }\n  interpolate(str, data, lng, options) {\n    let match;\n    let value;\n    let replaces;\n    const defaultData = this.options && this.options.interpolation && this.options.interpolation.defaultVariables || {};\n    const handleFormat = key => {\n      if (key.indexOf(this.formatSeparator) < 0) {\n        const path = deepFindWithDefaults(data, defaultData, key, this.options.keySeparator, this.options.ignoreJSONStructure);\n        return this.alwaysFormat ? this.format(path, undefined, lng, {\n          ...options,\n          ...data,\n          interpolationkey: key\n        }) : path;\n      }\n      const p = key.split(this.formatSeparator);\n      const k = p.shift().trim();\n      const f = p.join(this.formatSeparator).trim();\n      return this.format(deepFindWithDefaults(data, defaultData, k, this.options.keySeparator, this.options.ignoreJSONStructure), f, lng, {\n        ...options,\n        ...data,\n        interpolationkey: k\n      });\n    };\n    this.resetRegExp();\n    const missingInterpolationHandler = options && options.missingInterpolationHandler || this.options.missingInterpolationHandler;\n    const skipOnVariables = options && options.interpolation && options.interpolation.skipOnVariables !== undefined ? options.interpolation.skipOnVariables : this.options.interpolation.skipOnVariables;\n    const todos = [{\n      regex: this.regexpUnescape,\n      safeValue: val => regexSafe(val)\n    }, {\n      regex: this.regexp,\n      safeValue: val => this.escapeValue ? regexSafe(this.escape(val)) : regexSafe(val)\n    }];\n    todos.forEach(todo => {\n      replaces = 0;\n      while (match = todo.regex.exec(str)) {\n        const matchedVar = match[1].trim();\n        value = handleFormat(matchedVar);\n        if (value === undefined) {\n          if (typeof missingInterpolationHandler === 'function') {\n            const temp = missingInterpolationHandler(str, match, options);\n            value = isString(temp) ? temp : '';\n          } else if (options && Object.prototype.hasOwnProperty.call(options, matchedVar)) {\n            value = '';\n          } else if (skipOnVariables) {\n            value = match[0];\n            continue;\n          } else {\n            this.logger.warn(`missed to pass in variable ${matchedVar} for interpolating ${str}`);\n            value = '';\n          }\n        } else if (!isString(value) && !this.useRawValueToEscape) {\n          value = makeString(value);\n        }\n        const safeValue = todo.safeValue(value);\n        str = str.replace(match[0], safeValue);\n        if (skipOnVariables) {\n          todo.regex.lastIndex += value.length;\n          todo.regex.lastIndex -= match[0].length;\n        } else {\n          todo.regex.lastIndex = 0;\n        }\n        replaces++;\n        if (replaces >= this.maxReplaces) {\n          break;\n        }\n      }\n    });\n    return str;\n  }\n  nest(str, fc) {\n    let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    let match;\n    let value;\n    let clonedOptions;\n    const handleHasOptions = (key, inheritedOptions) => {\n      const sep = this.nestingOptionsSeparator;\n      if (key.indexOf(sep) < 0) return key;\n      const c = key.split(new RegExp(`${sep}[ ]*{`));\n      let optionsString = `{${c[1]}`;\n      key = c[0];\n      optionsString = this.interpolate(optionsString, clonedOptions);\n      const matchedSingleQuotes = optionsString.match(/'/g);\n      const matchedDoubleQuotes = optionsString.match(/\"/g);\n      if (matchedSingleQuotes && matchedSingleQuotes.length % 2 === 0 && !matchedDoubleQuotes || matchedDoubleQuotes.length % 2 !== 0) {\n        optionsString = optionsString.replace(/'/g, '\"');\n      }\n      try {\n        clonedOptions = JSON.parse(optionsString);\n        if (inheritedOptions) clonedOptions = {\n          ...inheritedOptions,\n          ...clonedOptions\n        };\n      } catch (e) {\n        this.logger.warn(`failed parsing options string in nesting for key ${key}`, e);\n        return `${key}${sep}${optionsString}`;\n      }\n      if (clonedOptions.defaultValue && clonedOptions.defaultValue.indexOf(this.prefix) > -1) delete clonedOptions.defaultValue;\n      return key;\n    };\n    while (match = this.nestingRegexp.exec(str)) {\n      let formatters = [];\n      clonedOptions = {\n        ...options\n      };\n      clonedOptions = clonedOptions.replace && !isString(clonedOptions.replace) ? clonedOptions.replace : clonedOptions;\n      clonedOptions.applyPostProcessor = false;\n      delete clonedOptions.defaultValue;\n      let doReduce = false;\n      if (match[0].indexOf(this.formatSeparator) !== -1 && !/{.*}/.test(match[1])) {\n        const r = match[1].split(this.formatSeparator).map(elem => elem.trim());\n        match[1] = r.shift();\n        formatters = r;\n        doReduce = true;\n      }\n      value = fc(handleHasOptions.call(this, match[1].trim(), clonedOptions), clonedOptions);\n      if (value && match[0] === str && !isString(value)) return value;\n      if (!isString(value)) value = makeString(value);\n      if (!value) {\n        this.logger.warn(`missed to resolve ${match[1]} for nesting ${str}`);\n        value = '';\n      }\n      if (doReduce) {\n        value = formatters.reduce((v, f) => this.format(v, f, options.lng, {\n          ...options,\n          interpolationkey: match[1].trim()\n        }), value.trim());\n      }\n      str = str.replace(match[0], value);\n      this.regexp.lastIndex = 0;\n    }\n    return str;\n  }\n}\nconst parseFormatStr = formatStr => {\n  let formatName = formatStr.toLowerCase().trim();\n  const formatOptions = {};\n  if (formatStr.indexOf('(') > -1) {\n    const p = formatStr.split('(');\n    formatName = p[0].toLowerCase().trim();\n    const optStr = p[1].substring(0, p[1].length - 1);\n    if (formatName === 'currency' && optStr.indexOf(':') < 0) {\n      if (!formatOptions.currency) formatOptions.currency = optStr.trim();\n    } else if (formatName === 'relativetime' && optStr.indexOf(':') < 0) {\n      if (!formatOptions.range) formatOptions.range = optStr.trim();\n    } else {\n      const opts = optStr.split(';');\n      opts.forEach(opt => {\n        if (opt) {\n          const [key, ...rest] = opt.split(':');\n          const val = rest.join(':').trim().replace(/^'+|'+$/g, '');\n          const trimmedKey = key.trim();\n          if (!formatOptions[trimmedKey]) formatOptions[trimmedKey] = val;\n          if (val === 'false') formatOptions[trimmedKey] = false;\n          if (val === 'true') formatOptions[trimmedKey] = true;\n          if (!isNaN(val)) formatOptions[trimmedKey] = parseInt(val, 10);\n        }\n      });\n    }\n  }\n  return {\n    formatName,\n    formatOptions\n  };\n};\nconst createCachedFormatter = fn => {\n  const cache = {};\n  return (val, lng, options) => {\n    let optForCache = options;\n    if (options && options.interpolationkey && options.formatParams && options.formatParams[options.interpolationkey] && options[options.interpolationkey]) {\n      optForCache = {\n        ...optForCache,\n        [options.interpolationkey]: undefined\n      };\n    }\n    const key = lng + JSON.stringify(optForCache);\n    let formatter = cache[key];\n    if (!formatter) {\n      formatter = fn(getCleanedCode(lng), options);\n      cache[key] = formatter;\n    }\n    return formatter(val);\n  };\n};\nclass Formatter {\n  constructor() {\n    let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    this.logger = baseLogger.create('formatter');\n    this.options = options;\n    this.formats = {\n      number: createCachedFormatter((lng, opt) => {\n        const formatter = new Intl.NumberFormat(lng, {\n          ...opt\n        });\n        return val => formatter.format(val);\n      }),\n      currency: createCachedFormatter((lng, opt) => {\n        const formatter = new Intl.NumberFormat(lng, {\n          ...opt,\n          style: 'currency'\n        });\n        return val => formatter.format(val);\n      }),\n      datetime: createCachedFormatter((lng, opt) => {\n        const formatter = new Intl.DateTimeFormat(lng, {\n          ...opt\n        });\n        return val => formatter.format(val);\n      }),\n      relativetime: createCachedFormatter((lng, opt) => {\n        const formatter = new Intl.RelativeTimeFormat(lng, {\n          ...opt\n        });\n        return val => formatter.format(val, opt.range || 'day');\n      }),\n      list: createCachedFormatter((lng, opt) => {\n        const formatter = new Intl.ListFormat(lng, {\n          ...opt\n        });\n        return val => formatter.format(val);\n      })\n    };\n    this.init(options);\n  }\n  init(services) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n      interpolation: {}\n    };\n    this.formatSeparator = options.interpolation.formatSeparator || ',';\n  }\n  add(name, fc) {\n    this.formats[name.toLowerCase().trim()] = fc;\n  }\n  addCached(name, fc) {\n    this.formats[name.toLowerCase().trim()] = createCachedFormatter(fc);\n  }\n  format(value, format, lng) {\n    let options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n    const formats = format.split(this.formatSeparator);\n    if (formats.length > 1 && formats[0].indexOf('(') > 1 && formats[0].indexOf(')') < 0 && formats.find(f => f.indexOf(')') > -1)) {\n      const lastIndex = formats.findIndex(f => f.indexOf(')') > -1);\n      formats[0] = [formats[0], ...formats.splice(1, lastIndex)].join(this.formatSeparator);\n    }\n    const result = formats.reduce((mem, f) => {\n      const {\n        formatName,\n        formatOptions\n      } = parseFormatStr(f);\n      if (this.formats[formatName]) {\n        let formatted = mem;\n        try {\n          const valOptions = options && options.formatParams && options.formatParams[options.interpolationkey] || {};\n          const l = valOptions.locale || valOptions.lng || options.locale || options.lng || lng;\n          formatted = this.formats[formatName](mem, l, {\n            ...formatOptions,\n            ...options,\n            ...valOptions\n          });\n        } catch (error) {\n          this.logger.warn(error);\n        }\n        return formatted;\n      } else {\n        this.logger.warn(`there was no format function for ${formatName}`);\n      }\n      return mem;\n    }, value);\n    return result;\n  }\n}\nconst removePending = (q, name) => {\n  if (q.pending[name] !== undefined) {\n    delete q.pending[name];\n    q.pendingCount--;\n  }\n};\nclass Connector extends EventEmitter {\n  constructor(backend, store, services) {\n    let options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n    super();\n    this.backend = backend;\n    this.store = store;\n    this.services = services;\n    this.languageUtils = services.languageUtils;\n    this.options = options;\n    this.logger = baseLogger.create('backendConnector');\n    this.waitingReads = [];\n    this.maxParallelReads = options.maxParallelReads || 10;\n    this.readingCalls = 0;\n    this.maxRetries = options.maxRetries >= 0 ? options.maxRetries : 5;\n    this.retryTimeout = options.retryTimeout >= 1 ? options.retryTimeout : 350;\n    this.state = {};\n    this.queue = [];\n    if (this.backend && this.backend.init) {\n      this.backend.init(services, options.backend, options);\n    }\n  }\n  queueLoad(languages, namespaces, options, callback) {\n    const toLoad = {};\n    const pending = {};\n    const toLoadLanguages = {};\n    const toLoadNamespaces = {};\n    languages.forEach(lng => {\n      let hasAllNamespaces = true;\n      namespaces.forEach(ns => {\n        const name = `${lng}|${ns}`;\n        if (!options.reload && this.store.hasResourceBundle(lng, ns)) {\n          this.state[name] = 2;\n        } else if (this.state[name] < 0) ;else if (this.state[name] === 1) {\n          if (pending[name] === undefined) pending[name] = true;\n        } else {\n          this.state[name] = 1;\n          hasAllNamespaces = false;\n          if (pending[name] === undefined) pending[name] = true;\n          if (toLoad[name] === undefined) toLoad[name] = true;\n          if (toLoadNamespaces[ns] === undefined) toLoadNamespaces[ns] = true;\n        }\n      });\n      if (!hasAllNamespaces) toLoadLanguages[lng] = true;\n    });\n    if (Object.keys(toLoad).length || Object.keys(pending).length) {\n      this.queue.push({\n        pending,\n        pendingCount: Object.keys(pending).length,\n        loaded: {},\n        errors: [],\n        callback\n      });\n    }\n    return {\n      toLoad: Object.keys(toLoad),\n      pending: Object.keys(pending),\n      toLoadLanguages: Object.keys(toLoadLanguages),\n      toLoadNamespaces: Object.keys(toLoadNamespaces)\n    };\n  }\n  loaded(name, err, data) {\n    const s = name.split('|');\n    const lng = s[0];\n    const ns = s[1];\n    if (err) this.emit('failedLoading', lng, ns, err);\n    if (!err && data) {\n      this.store.addResourceBundle(lng, ns, data, undefined, undefined, {\n        skipCopy: true\n      });\n    }\n    this.state[name] = err ? -1 : 2;\n    if (err && data) this.state[name] = 0;\n    const loaded = {};\n    this.queue.forEach(q => {\n      pushPath(q.loaded, [lng], ns);\n      removePending(q, name);\n      if (err) q.errors.push(err);\n      if (q.pendingCount === 0 && !q.done) {\n        Object.keys(q.loaded).forEach(l => {\n          if (!loaded[l]) loaded[l] = {};\n          const loadedKeys = q.loaded[l];\n          if (loadedKeys.length) {\n            loadedKeys.forEach(n => {\n              if (loaded[l][n] === undefined) loaded[l][n] = true;\n            });\n          }\n        });\n        q.done = true;\n        if (q.errors.length) {\n          q.callback(q.errors);\n        } else {\n          q.callback();\n        }\n      }\n    });\n    this.emit('loaded', loaded);\n    this.queue = this.queue.filter(q => !q.done);\n  }\n  read(lng, ns, fcName) {\n    let tried = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 0;\n    let wait = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : this.retryTimeout;\n    let callback = arguments.length > 5 ? arguments[5] : undefined;\n    if (!lng.length) return callback(null, {});\n    if (this.readingCalls >= this.maxParallelReads) {\n      this.waitingReads.push({\n        lng,\n        ns,\n        fcName,\n        tried,\n        wait,\n        callback\n      });\n      return;\n    }\n    this.readingCalls++;\n    const resolver = (err, data) => {\n      this.readingCalls--;\n      if (this.waitingReads.length > 0) {\n        const next = this.waitingReads.shift();\n        this.read(next.lng, next.ns, next.fcName, next.tried, next.wait, next.callback);\n      }\n      if (err && data && tried < this.maxRetries) {\n        setTimeout(() => {\n          this.read.call(this, lng, ns, fcName, tried + 1, wait * 2, callback);\n        }, wait);\n        return;\n      }\n      callback(err, data);\n    };\n    const fc = this.backend[fcName].bind(this.backend);\n    if (fc.length === 2) {\n      try {\n        const r = fc(lng, ns);\n        if (r && typeof r.then === 'function') {\n          r.then(data => resolver(null, data)).catch(resolver);\n        } else {\n          resolver(null, r);\n        }\n      } catch (err) {\n        resolver(err);\n      }\n      return;\n    }\n    return fc(lng, ns, resolver);\n  }\n  prepareLoading(languages, namespaces) {\n    let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    let callback = arguments.length > 3 ? arguments[3] : undefined;\n    if (!this.backend) {\n      this.logger.warn('No backend was added via i18next.use. Will not load resources.');\n      return callback && callback();\n    }\n    if (isString(languages)) languages = this.languageUtils.toResolveHierarchy(languages);\n    if (isString(namespaces)) namespaces = [namespaces];\n    const toLoad = this.queueLoad(languages, namespaces, options, callback);\n    if (!toLoad.toLoad.length) {\n      if (!toLoad.pending.length) callback();\n      return null;\n    }\n    toLoad.toLoad.forEach(name => {\n      this.loadOne(name);\n    });\n  }\n  load(languages, namespaces, callback) {\n    this.prepareLoading(languages, namespaces, {}, callback);\n  }\n  reload(languages, namespaces, callback) {\n    this.prepareLoading(languages, namespaces, {\n      reload: true\n    }, callback);\n  }\n  loadOne(name) {\n    let prefix = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';\n    const s = name.split('|');\n    const lng = s[0];\n    const ns = s[1];\n    this.read(lng, ns, 'read', undefined, undefined, (err, data) => {\n      if (err) this.logger.warn(`${prefix}loading namespace ${ns} for language ${lng} failed`, err);\n      if (!err && data) this.logger.log(`${prefix}loaded namespace ${ns} for language ${lng}`, data);\n      this.loaded(name, err, data);\n    });\n  }\n  saveMissing(languages, namespace, key, fallbackValue, isUpdate) {\n    let options = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : {};\n    let clb = arguments.length > 6 && arguments[6] !== undefined ? arguments[6] : () => {};\n    if (this.services.utils && this.services.utils.hasLoadedNamespace && !this.services.utils.hasLoadedNamespace(namespace)) {\n      this.logger.warn(`did not save key \"${key}\" as the namespace \"${namespace}\" was not yet loaded`, 'This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!');\n      return;\n    }\n    if (key === undefined || key === null || key === '') return;\n    if (this.backend && this.backend.create) {\n      const opts = {\n        ...options,\n        isUpdate\n      };\n      const fc = this.backend.create.bind(this.backend);\n      if (fc.length < 6) {\n        try {\n          let r;\n          if (fc.length === 5) {\n            r = fc(languages, namespace, key, fallbackValue, opts);\n          } else {\n            r = fc(languages, namespace, key, fallbackValue);\n          }\n          if (r && typeof r.then === 'function') {\n            r.then(data => clb(null, data)).catch(clb);\n          } else {\n            clb(null, r);\n          }\n        } catch (err) {\n          clb(err);\n        }\n      } else {\n        fc(languages, namespace, key, fallbackValue, clb, opts);\n      }\n    }\n    if (!languages || !languages[0]) return;\n    this.store.addResource(languages[0], namespace, key, fallbackValue);\n  }\n}\nconst get = () => ({\n  debug: false,\n  initImmediate: true,\n  ns: ['translation'],\n  defaultNS: ['translation'],\n  fallbackLng: ['dev'],\n  fallbackNS: false,\n  supportedLngs: false,\n  nonExplicitSupportedLngs: false,\n  load: 'all',\n  preload: false,\n  simplifyPluralSuffix: true,\n  keySeparator: '.',\n  nsSeparator: ':',\n  pluralSeparator: '_',\n  contextSeparator: '_',\n  partialBundledLanguages: false,\n  saveMissing: false,\n  updateMissing: false,\n  saveMissingTo: 'fallback',\n  saveMissingPlurals: true,\n  missingKeyHandler: false,\n  missingInterpolationHandler: false,\n  postProcess: false,\n  postProcessPassResolved: false,\n  returnNull: false,\n  returnEmptyString: true,\n  returnObjects: false,\n  joinArrays: false,\n  returnedObjectHandler: false,\n  parseMissingKeyHandler: false,\n  appendNamespaceToMissingKey: false,\n  appendNamespaceToCIMode: false,\n  overloadTranslationOptionHandler: args => {\n    let ret = {};\n    if (typeof args[1] === 'object') ret = args[1];\n    if (isString(args[1])) ret.defaultValue = args[1];\n    if (isString(args[2])) ret.tDescription = args[2];\n    if (typeof args[2] === 'object' || typeof args[3] === 'object') {\n      const options = args[3] || args[2];\n      Object.keys(options).forEach(key => {\n        ret[key] = options[key];\n      });\n    }\n    return ret;\n  },\n  interpolation: {\n    escapeValue: true,\n    format: value => value,\n    prefix: '{{',\n    suffix: '}}',\n    formatSeparator: ',',\n    unescapePrefix: '-',\n    nestingPrefix: '$t(',\n    nestingSuffix: ')',\n    nestingOptionsSeparator: ',',\n    maxReplaces: 1000,\n    skipOnVariables: true\n  }\n});\nconst transformOptions = options => {\n  if (isString(options.ns)) options.ns = [options.ns];\n  if (isString(options.fallbackLng)) options.fallbackLng = [options.fallbackLng];\n  if (isString(options.fallbackNS)) options.fallbackNS = [options.fallbackNS];\n  if (options.supportedLngs && options.supportedLngs.indexOf('cimode') < 0) {\n    options.supportedLngs = options.supportedLngs.concat(['cimode']);\n  }\n  return options;\n};\nconst noop = () => {};\nconst bindMemberFunctions = inst => {\n  const mems = Object.getOwnPropertyNames(Object.getPrototypeOf(inst));\n  mems.forEach(mem => {\n    if (typeof inst[mem] === 'function') {\n      inst[mem] = inst[mem].bind(inst);\n    }\n  });\n};\nclass I18n extends EventEmitter {\n  constructor() {\n    let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    let callback = arguments.length > 1 ? arguments[1] : undefined;\n    super();\n    this.options = transformOptions(options);\n    this.services = {};\n    this.logger = baseLogger;\n    this.modules = {\n      external: []\n    };\n    bindMemberFunctions(this);\n    if (callback && !this.isInitialized && !options.isClone) {\n      if (!this.options.initImmediate) {\n        this.init(options, callback);\n        return this;\n      }\n      setTimeout(() => {\n        this.init(options, callback);\n      }, 0);\n    }\n  }\n  init() {\n    var _this = this;\n    let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    let callback = arguments.length > 1 ? arguments[1] : undefined;\n    this.isInitializing = true;\n    if (typeof options === 'function') {\n      callback = options;\n      options = {};\n    }\n    if (!options.defaultNS && options.defaultNS !== false && options.ns) {\n      if (isString(options.ns)) {\n        options.defaultNS = options.ns;\n      } else if (options.ns.indexOf('translation') < 0) {\n        options.defaultNS = options.ns[0];\n      }\n    }\n    const defOpts = get();\n    this.options = {\n      ...defOpts,\n      ...this.options,\n      ...transformOptions(options)\n    };\n    if (this.options.compatibilityAPI !== 'v1') {\n      this.options.interpolation = {\n        ...defOpts.interpolation,\n        ...this.options.interpolation\n      };\n    }\n    if (options.keySeparator !== undefined) {\n      this.options.userDefinedKeySeparator = options.keySeparator;\n    }\n    if (options.nsSeparator !== undefined) {\n      this.options.userDefinedNsSeparator = options.nsSeparator;\n    }\n    const createClassOnDemand = ClassOrObject => {\n      if (!ClassOrObject) return null;\n      if (typeof ClassOrObject === 'function') return new ClassOrObject();\n      return ClassOrObject;\n    };\n    if (!this.options.isClone) {\n      if (this.modules.logger) {\n        baseLogger.init(createClassOnDemand(this.modules.logger), this.options);\n      } else {\n        baseLogger.init(null, this.options);\n      }\n      let formatter;\n      if (this.modules.formatter) {\n        formatter = this.modules.formatter;\n      } else if (typeof Intl !== 'undefined') {\n        formatter = Formatter;\n      }\n      const lu = new LanguageUtil(this.options);\n      this.store = new ResourceStore(this.options.resources, this.options);\n      const s = this.services;\n      s.logger = baseLogger;\n      s.resourceStore = this.store;\n      s.languageUtils = lu;\n      s.pluralResolver = new PluralResolver(lu, {\n        prepend: this.options.pluralSeparator,\n        compatibilityJSON: this.options.compatibilityJSON,\n        simplifyPluralSuffix: this.options.simplifyPluralSuffix\n      });\n      if (formatter && (!this.options.interpolation.format || this.options.interpolation.format === defOpts.interpolation.format)) {\n        s.formatter = createClassOnDemand(formatter);\n        s.formatter.init(s, this.options);\n        this.options.interpolation.format = s.formatter.format.bind(s.formatter);\n      }\n      s.interpolator = new Interpolator(this.options);\n      s.utils = {\n        hasLoadedNamespace: this.hasLoadedNamespace.bind(this)\n      };\n      s.backendConnector = new Connector(createClassOnDemand(this.modules.backend), s.resourceStore, s, this.options);\n      s.backendConnector.on('*', function (event) {\n        for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n          args[_key - 1] = arguments[_key];\n        }\n        _this.emit(event, ...args);\n      });\n      if (this.modules.languageDetector) {\n        s.languageDetector = createClassOnDemand(this.modules.languageDetector);\n        if (s.languageDetector.init) s.languageDetector.init(s, this.options.detection, this.options);\n      }\n      if (this.modules.i18nFormat) {\n        s.i18nFormat = createClassOnDemand(this.modules.i18nFormat);\n        if (s.i18nFormat.init) s.i18nFormat.init(this);\n      }\n      this.translator = new Translator(this.services, this.options);\n      this.translator.on('*', function (event) {\n        for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n          args[_key2 - 1] = arguments[_key2];\n        }\n        _this.emit(event, ...args);\n      });\n      this.modules.external.forEach(m => {\n        if (m.init) m.init(this);\n      });\n    }\n    this.format = this.options.interpolation.format;\n    if (!callback) callback = noop;\n    if (this.options.fallbackLng && !this.services.languageDetector && !this.options.lng) {\n      const codes = this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);\n      if (codes.length > 0 && codes[0] !== 'dev') this.options.lng = codes[0];\n    }\n    if (!this.services.languageDetector && !this.options.lng) {\n      this.logger.warn('init: no languageDetector is used and no lng is defined');\n    }\n    const storeApi = ['getResource', 'hasResourceBundle', 'getResourceBundle', 'getDataByLanguage'];\n    storeApi.forEach(fcName => {\n      this[fcName] = function () {\n        return _this.store[fcName](...arguments);\n      };\n    });\n    const storeApiChained = ['addResource', 'addResources', 'addResourceBundle', 'removeResourceBundle'];\n    storeApiChained.forEach(fcName => {\n      this[fcName] = function () {\n        _this.store[fcName](...arguments);\n        return _this;\n      };\n    });\n    const deferred = defer();\n    const load = () => {\n      const finish = (err, t) => {\n        this.isInitializing = false;\n        if (this.isInitialized && !this.initializedStoreOnce) this.logger.warn('init: i18next is already initialized. You should call init just once!');\n        this.isInitialized = true;\n        if (!this.options.isClone) this.logger.log('initialized', this.options);\n        this.emit('initialized', this.options);\n        deferred.resolve(t);\n        callback(err, t);\n      };\n      if (this.languages && this.options.compatibilityAPI !== 'v1' && !this.isInitialized) return finish(null, this.t.bind(this));\n      this.changeLanguage(this.options.lng, finish);\n    };\n    if (this.options.resources || !this.options.initImmediate) {\n      load();\n    } else {\n      setTimeout(load, 0);\n    }\n    return deferred;\n  }\n  loadResources(language) {\n    let callback = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : noop;\n    let usedCallback = callback;\n    const usedLng = isString(language) ? language : this.language;\n    if (typeof language === 'function') usedCallback = language;\n    if (!this.options.resources || this.options.partialBundledLanguages) {\n      if (usedLng && usedLng.toLowerCase() === 'cimode' && (!this.options.preload || this.options.preload.length === 0)) return usedCallback();\n      const toLoad = [];\n      const append = lng => {\n        if (!lng) return;\n        if (lng === 'cimode') return;\n        const lngs = this.services.languageUtils.toResolveHierarchy(lng);\n        lngs.forEach(l => {\n          if (l === 'cimode') return;\n          if (toLoad.indexOf(l) < 0) toLoad.push(l);\n        });\n      };\n      if (!usedLng) {\n        const fallbacks = this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);\n        fallbacks.forEach(l => append(l));\n      } else {\n        append(usedLng);\n      }\n      if (this.options.preload) {\n        this.options.preload.forEach(l => append(l));\n      }\n      this.services.backendConnector.load(toLoad, this.options.ns, e => {\n        if (!e && !this.resolvedLanguage && this.language) this.setResolvedLanguage(this.language);\n        usedCallback(e);\n      });\n    } else {\n      usedCallback(null);\n    }\n  }\n  reloadResources(lngs, ns, callback) {\n    const deferred = defer();\n    if (typeof lngs === 'function') {\n      callback = lngs;\n      lngs = undefined;\n    }\n    if (typeof ns === 'function') {\n      callback = ns;\n      ns = undefined;\n    }\n    if (!lngs) lngs = this.languages;\n    if (!ns) ns = this.options.ns;\n    if (!callback) callback = noop;\n    this.services.backendConnector.reload(lngs, ns, err => {\n      deferred.resolve();\n      callback(err);\n    });\n    return deferred;\n  }\n  use(module) {\n    if (!module) throw new Error('You are passing an undefined module! Please check the object you are passing to i18next.use()');\n    if (!module.type) throw new Error('You are passing a wrong module! Please check the object you are passing to i18next.use()');\n    if (module.type === 'backend') {\n      this.modules.backend = module;\n    }\n    if (module.type === 'logger' || module.log && module.warn && module.error) {\n      this.modules.logger = module;\n    }\n    if (module.type === 'languageDetector') {\n      this.modules.languageDetector = module;\n    }\n    if (module.type === 'i18nFormat') {\n      this.modules.i18nFormat = module;\n    }\n    if (module.type === 'postProcessor') {\n      postProcessor.addPostProcessor(module);\n    }\n    if (module.type === 'formatter') {\n      this.modules.formatter = module;\n    }\n    if (module.type === '3rdParty') {\n      this.modules.external.push(module);\n    }\n    return this;\n  }\n  setResolvedLanguage(l) {\n    if (!l || !this.languages) return;\n    if (['cimode', 'dev'].indexOf(l) > -1) return;\n    for (let li = 0; li < this.languages.length; li++) {\n      const lngInLngs = this.languages[li];\n      if (['cimode', 'dev'].indexOf(lngInLngs) > -1) continue;\n      if (this.store.hasLanguageSomeTranslations(lngInLngs)) {\n        this.resolvedLanguage = lngInLngs;\n        break;\n      }\n    }\n  }\n  changeLanguage(lng, callback) {\n    var _this2 = this;\n    this.isLanguageChangingTo = lng;\n    const deferred = defer();\n    this.emit('languageChanging', lng);\n    const setLngProps = l => {\n      this.language = l;\n      this.languages = this.services.languageUtils.toResolveHierarchy(l);\n      this.resolvedLanguage = undefined;\n      this.setResolvedLanguage(l);\n    };\n    const done = (err, l) => {\n      if (l) {\n        setLngProps(l);\n        this.translator.changeLanguage(l);\n        this.isLanguageChangingTo = undefined;\n        this.emit('languageChanged', l);\n        this.logger.log('languageChanged', l);\n      } else {\n        this.isLanguageChangingTo = undefined;\n      }\n      deferred.resolve(function () {\n        return _this2.t(...arguments);\n      });\n      if (callback) callback(err, function () {\n        return _this2.t(...arguments);\n      });\n    };\n    const setLng = lngs => {\n      if (!lng && !lngs && this.services.languageDetector) lngs = [];\n      const l = isString(lngs) ? lngs : this.services.languageUtils.getBestMatchFromCodes(lngs);\n      if (l) {\n        if (!this.language) {\n          setLngProps(l);\n        }\n        if (!this.translator.language) this.translator.changeLanguage(l);\n        if (this.services.languageDetector && this.services.languageDetector.cacheUserLanguage) this.services.languageDetector.cacheUserLanguage(l);\n      }\n      this.loadResources(l, err => {\n        done(err, l);\n      });\n    };\n    if (!lng && this.services.languageDetector && !this.services.languageDetector.async) {\n      setLng(this.services.languageDetector.detect());\n    } else if (!lng && this.services.languageDetector && this.services.languageDetector.async) {\n      if (this.services.languageDetector.detect.length === 0) {\n        this.services.languageDetector.detect().then(setLng);\n      } else {\n        this.services.languageDetector.detect(setLng);\n      }\n    } else {\n      setLng(lng);\n    }\n    return deferred;\n  }\n  getFixedT(lng, ns, keyPrefix) {\n    var _this3 = this;\n    const fixedT = function (key, opts) {\n      let options;\n      if (typeof opts !== 'object') {\n        for (var _len3 = arguments.length, rest = new Array(_len3 > 2 ? _len3 - 2 : 0), _key3 = 2; _key3 < _len3; _key3++) {\n          rest[_key3 - 2] = arguments[_key3];\n        }\n        options = _this3.options.overloadTranslationOptionHandler([key, opts].concat(rest));\n      } else {\n        options = {\n          ...opts\n        };\n      }\n      options.lng = options.lng || fixedT.lng;\n      options.lngs = options.lngs || fixedT.lngs;\n      options.ns = options.ns || fixedT.ns;\n      if (options.keyPrefix !== '') options.keyPrefix = options.keyPrefix || keyPrefix || fixedT.keyPrefix;\n      const keySeparator = _this3.options.keySeparator || '.';\n      let resultKey;\n      if (options.keyPrefix && Array.isArray(key)) {\n        resultKey = key.map(k => `${options.keyPrefix}${keySeparator}${k}`);\n      } else {\n        resultKey = options.keyPrefix ? `${options.keyPrefix}${keySeparator}${key}` : key;\n      }\n      return _this3.t(resultKey, options);\n    };\n    if (isString(lng)) {\n      fixedT.lng = lng;\n    } else {\n      fixedT.lngs = lng;\n    }\n    fixedT.ns = ns;\n    fixedT.keyPrefix = keyPrefix;\n    return fixedT;\n  }\n  t() {\n    return this.translator && this.translator.translate(...arguments);\n  }\n  exists() {\n    return this.translator && this.translator.exists(...arguments);\n  }\n  setDefaultNamespace(ns) {\n    this.options.defaultNS = ns;\n  }\n  hasLoadedNamespace(ns) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    if (!this.isInitialized) {\n      this.logger.warn('hasLoadedNamespace: i18next was not initialized', this.languages);\n      return false;\n    }\n    if (!this.languages || !this.languages.length) {\n      this.logger.warn('hasLoadedNamespace: i18n.languages were undefined or empty', this.languages);\n      return false;\n    }\n    const lng = options.lng || this.resolvedLanguage || this.languages[0];\n    const fallbackLng = this.options ? this.options.fallbackLng : false;\n    const lastLng = this.languages[this.languages.length - 1];\n    if (lng.toLowerCase() === 'cimode') return true;\n    const loadNotPending = (l, n) => {\n      const loadState = this.services.backendConnector.state[`${l}|${n}`];\n      return loadState === -1 || loadState === 0 || loadState === 2;\n    };\n    if (options.precheck) {\n      const preResult = options.precheck(this, loadNotPending);\n      if (preResult !== undefined) return preResult;\n    }\n    if (this.hasResourceBundle(lng, ns)) return true;\n    if (!this.services.backendConnector.backend || this.options.resources && !this.options.partialBundledLanguages) return true;\n    if (loadNotPending(lng, ns) && (!fallbackLng || loadNotPending(lastLng, ns))) return true;\n    return false;\n  }\n  loadNamespaces(ns, callback) {\n    const deferred = defer();\n    if (!this.options.ns) {\n      if (callback) callback();\n      return Promise.resolve();\n    }\n    if (isString(ns)) ns = [ns];\n    ns.forEach(n => {\n      if (this.options.ns.indexOf(n) < 0) this.options.ns.push(n);\n    });\n    this.loadResources(err => {\n      deferred.resolve();\n      if (callback) callback(err);\n    });\n    return deferred;\n  }\n  loadLanguages(lngs, callback) {\n    const deferred = defer();\n    if (isString(lngs)) lngs = [lngs];\n    const preloaded = this.options.preload || [];\n    const newLngs = lngs.filter(lng => preloaded.indexOf(lng) < 0 && this.services.languageUtils.isSupportedCode(lng));\n    if (!newLngs.length) {\n      if (callback) callback();\n      return Promise.resolve();\n    }\n    this.options.preload = preloaded.concat(newLngs);\n    this.loadResources(err => {\n      deferred.resolve();\n      if (callback) callback(err);\n    });\n    return deferred;\n  }\n  dir(lng) {\n    if (!lng) lng = this.resolvedLanguage || (this.languages && this.languages.length > 0 ? this.languages[0] : this.language);\n    if (!lng) return 'rtl';\n    const rtlLngs = ['ar', 'shu', 'sqr', 'ssh', 'xaa', 'yhd', 'yud', 'aao', 'abh', 'abv', 'acm', 'acq', 'acw', 'acx', 'acy', 'adf', 'ads', 'aeb', 'aec', 'afb', 'ajp', 'apc', 'apd', 'arb', 'arq', 'ars', 'ary', 'arz', 'auz', 'avl', 'ayh', 'ayl', 'ayn', 'ayp', 'bbz', 'pga', 'he', 'iw', 'ps', 'pbt', 'pbu', 'pst', 'prp', 'prd', 'ug', 'ur', 'ydd', 'yds', 'yih', 'ji', 'yi', 'hbo', 'men', 'xmn', 'fa', 'jpr', 'peo', 'pes', 'prs', 'dv', 'sam', 'ckb'];\n    const languageUtils = this.services && this.services.languageUtils || new LanguageUtil(get());\n    return rtlLngs.indexOf(languageUtils.getLanguagePartFromCode(lng)) > -1 || lng.toLowerCase().indexOf('-arab') > 1 ? 'rtl' : 'ltr';\n  }\n  static createInstance() {\n    let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    let callback = arguments.length > 1 ? arguments[1] : undefined;\n    return new I18n(options, callback);\n  }\n  cloneInstance() {\n    let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    let callback = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : noop;\n    const forkResourceStore = options.forkResourceStore;\n    if (forkResourceStore) delete options.forkResourceStore;\n    const mergedOptions = {\n      ...this.options,\n      ...options,\n      ...{\n        isClone: true\n      }\n    };\n    const clone = new I18n(mergedOptions);\n    if (options.debug !== undefined || options.prefix !== undefined) {\n      clone.logger = clone.logger.clone(options);\n    }\n    const membersToCopy = ['store', 'services', 'language'];\n    membersToCopy.forEach(m => {\n      clone[m] = this[m];\n    });\n    clone.services = {\n      ...this.services\n    };\n    clone.services.utils = {\n      hasLoadedNamespace: clone.hasLoadedNamespace.bind(clone)\n    };\n    if (forkResourceStore) {\n      clone.store = new ResourceStore(this.store.data, mergedOptions);\n      clone.services.resourceStore = clone.store;\n    }\n    clone.translator = new Translator(clone.services, mergedOptions);\n    clone.translator.on('*', function (event) {\n      for (var _len4 = arguments.length, args = new Array(_len4 > 1 ? _len4 - 1 : 0), _key4 = 1; _key4 < _len4; _key4++) {\n        args[_key4 - 1] = arguments[_key4];\n      }\n      clone.emit(event, ...args);\n    });\n    clone.init(mergedOptions, callback);\n    clone.translator.options = mergedOptions;\n    clone.translator.backendConnector.services.utils = {\n      hasLoadedNamespace: clone.hasLoadedNamespace.bind(clone)\n    };\n    return clone;\n  }\n  toJSON() {\n    return {\n      options: this.options,\n      store: this.store,\n      language: this.language,\n      languages: this.languages,\n      resolvedLanguage: this.resolvedLanguage\n    };\n  }\n}\nconst instance = I18n.createInstance();\ninstance.createInstance = I18n.createInstance;\nconst createInstance = instance.createInstance;\nconst dir = instance.dir;\nconst init = instance.init;\nconst loadResources = instance.loadResources;\nconst reloadResources = instance.reloadResources;\nconst use = instance.use;\nconst changeLanguage = instance.changeLanguage;\nconst getFixedT = instance.getFixedT;\nconst t = instance.t;\nconst exists = instance.exists;\nconst setDefaultNamespace = instance.setDefaultNamespace;\nconst hasLoadedNamespace = instance.hasLoadedNamespace;\nconst loadNamespaces = instance.loadNamespaces;\nconst loadLanguages = instance.loadLanguages;\nexport { changeLanguage, createInstance, instance as default, dir, exists, getFixedT, hasLoadedNamespace, init, loadLanguages, loadNamespaces, loadResources, reloadResources, setDefaultNamespace, t, use };", "map": {"version": 3, "names": ["isString", "obj", "defer", "res", "rej", "promise", "Promise", "resolve", "reject", "makeString", "object", "copy", "a", "s", "t", "for<PERSON>ach", "m", "lastOfPathSeparatorRegExp", "<PERSON><PERSON><PERSON>", "key", "indexOf", "replace", "canNotTraverseDeeper", "getLastOfPath", "path", "Empty", "stack", "split", "stackIndex", "length", "Object", "prototype", "hasOwnProperty", "call", "k", "set<PERSON>ath", "newValue", "undefined", "e", "p", "slice", "last", "push<PERSON><PERSON>", "concat", "push", "<PERSON><PERSON><PERSON>", "getPathWithDefaults", "data", "defaultData", "value", "deepExtend", "target", "source", "overwrite", "prop", "String", "regexEscape", "str", "_entityMap", "escape", "RegExpCache", "constructor", "capacity", "regExpMap", "Map", "regExpQueue", "getRegExp", "pattern", "regExpFromCache", "get", "regExpNew", "RegExp", "delete", "shift", "set", "chars", "looksLikeObjectPathRegExpCache", "looksLikeObjectPath", "nsSeparator", "keySeparator", "possibleChars", "filter", "c", "r", "map", "join", "matched", "test", "ki", "substring", "deepFind", "arguments", "tokens", "current", "i", "next", "nextPath", "j", "getCleanedCode", "code", "consoleLogger", "type", "log", "args", "output", "warn", "error", "console", "apply", "<PERSON><PERSON>", "concreteLogger", "options", "init", "prefix", "logger", "debug", "_len", "Array", "_key", "forward", "_len2", "_key2", "_len3", "_key3", "deprecate", "_len4", "_key4", "lvl", "debugOnly", "create", "moduleName", "clone", "baseLogger", "EventEmitter", "observers", "on", "events", "listener", "event", "numListeners", "off", "emit", "cloned", "from", "entries", "_ref", "observer", "numTimesAdded", "_ref2", "ResourceStore", "ns", "defaultNS", "ignoreJSONStructure", "addNamespaces", "removeNamespaces", "index", "splice", "getResource", "lng", "isArray", "result", "addResource", "silent", "addResources", "resources", "addResourceBundle", "deep", "skipCopy", "pack", "JSON", "parse", "stringify", "removeResourceBundle", "hasResourceBundle", "getResourceBundle", "compatibilityAPI", "getDataByLanguage", "hasLanguageSomeTranslations", "n", "keys", "find", "v", "toJSON", "postProcessor", "processors", "addPostProcessor", "module", "name", "handle", "translator", "processor", "process", "checkedLoadedFor", "Translator", "services", "changeLanguage", "language", "exists", "interpolation", "resolved", "extractFromKey", "namespaces", "wouldCheckForNsInKey", "seemsNaturalLanguage", "userDefinedKeySeparator", "userDefinedNsSeparator", "match", "interpolator", "nestingRegexp", "parts", "translate", "last<PERSON>ey", "overloadTranslationOptionHandler", "returnDetails", "namespace", "appendNamespaceToCIMode", "toLowerCase", "usedKey", "exactUsed<PERSON>ey", "usedLng", "usedNS", "usedParams", "getUsedParamsDetails", "resUsed<PERSON><PERSON>", "resExactUsedKey", "resType", "toString", "noObject", "joinArrays", "handleAsObjectInI18nFormat", "i18nFormat", "handleAsObject", "returnObjects", "returnedObjectHandler", "resTypeIsArray", "newKeyToUse", "<PERSON><PERSON><PERSON>", "extendTranslation", "usedDefault", "needsPluralHandling", "count", "hasDefaultValue", "defaultValueSuffix", "pluralResolver", "getSuffix", "defaultValueSuffixOrdinalFallback", "ordinal", "needsZeroSuffixLookup", "shouldUseIntlApi", "defaultValue", "pluralSeparator", "isValidLookup", "missingKeyNoValueFallbackToKey", "resForMissing", "updateMissing", "fk", "lngs", "fallbackLngs", "languageUtils", "getFallbackCodes", "fallbackLng", "saveMissingTo", "toResolveHierarchy", "send", "l", "specificDefaultValue", "defaultForMissing", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "backendConnector", "saveMissing", "saveMissingPlurals", "suffixes", "getSuffixes", "suffix", "appendNamespaceToMissingKey", "parseMissingKeyHandler", "_this", "defaultVariables", "skipInterpolation", "skipOnVariables", "nestBef", "nb", "interpolate", "na", "nestAft", "nest", "context", "reset", "postProcess", "postProcessorNames", "applyPostProcessor", "postProcessPassResolved", "i18nResolved", "found", "extracted", "fallbackNS", "needsContextHandling", "codes", "utils", "hasLoadedNamespace", "finalKeys", "addLookupKeys", "pluralSuffix", "zeroSuffix", "ordinalPrefix", "<PERSON><PERSON>ey", "contextSeparator", "<PERSON><PERSON><PERSON>", "pop", "returnNull", "returnEmptyString", "resourceStore", "optionsKeys", "useOptionsReplaceForData", "option", "capitalize", "string", "char<PERSON>t", "toUpperCase", "LanguageUtil", "supportedLngs", "getScriptPartFromCode", "formatLanguageCode", "getLanguagePartFromCode", "Intl", "getCanonicalLocales", "formattedCode", "lowerCaseLng", "specialCases", "part", "cleanCode", "isSupportedCode", "load", "nonExplicitSupportedLngs", "getBestMatchFromCodes", "cleanedLng", "lngOnly", "supportedLng", "fallbacks", "default", "fallbackCode", "fallbackCodes", "addCode", "fc", "sets", "nr", "_rulesPluralsTypes", "Number", "nonIntlVersions", "intlVersions", "suffixesOrder", "zero", "one", "two", "few", "many", "other", "createRules", "rules", "numbers", "plurals", "PluralResolver", "compatibilityJSON", "includes", "PluralRules", "pluralRulesCache", "addRule", "clearCache", "getRule", "cleanedCode", "cache<PERSON>ey", "rule", "err", "lngPart", "needsPlural", "resolvedOptions", "pluralCategories", "getPluralFormsOfKey", "sort", "pluralCategory1", "pluralCategory2", "pluralCategory", "prepend", "number", "select", "getSuffixRetroCompatible", "idx", "noAbs", "Math", "abs", "simplifyPluralSuffix", "returnSuffix", "deepFindWithDefaults", "regexSafe", "val", "Interpolator", "format", "escapeValue", "escape$1", "useRawValueToEscape", "prefixEscaped", "suffixEscaped", "formatSeparator", "unescapeSuffix", "unescapePrefix", "nestingPrefix", "nestingPrefixEscaped", "nestingSuffix", "nestingSuffixEscaped", "nestingOptionsSeparator", "maxReplaces", "alwaysFormat", "resetRegExp", "getOrResetRegExp", "existingRegExp", "lastIndex", "regexp", "regexpUnescape", "replaces", "handleFormat", "interpolationkey", "trim", "f", "missingInterpolationHandler", "todos", "regex", "safeValue", "todo", "exec", "matchedVar", "temp", "clonedOptions", "handleHasOptions", "inheritedOptions", "sep", "optionsString", "matchedSingleQuotes", "matchedDoubleQuotes", "formatters", "doReduce", "elem", "reduce", "parseFormatStr", "formatStr", "formatName", "formatOptions", "optStr", "currency", "range", "opts", "opt", "rest", "<PERSON><PERSON><PERSON>", "isNaN", "parseInt", "createCachedFormatter", "fn", "cache", "optForCache", "formatParams", "formatter", "<PERSON><PERSON><PERSON>", "formats", "NumberFormat", "style", "datetime", "DateTimeFormat", "relativetime", "RelativeTimeFormat", "list", "ListFormat", "add", "addCached", "findIndex", "mem", "formatted", "valOptions", "locale", "removePending", "q", "pending", "pendingCount", "Connector", "backend", "store", "waitingReads", "maxP<PERSON>llelReads", "readingCalls", "maxRetries", "retryTimeout", "state", "queue", "queueLoad", "languages", "callback", "toLoad", "toLoadLanguages", "toLoadNamespaces", "hasAllNamespaces", "reload", "loaded", "errors", "done", "loadedKeys", "read", "fcName", "tried", "wait", "resolver", "setTimeout", "bind", "then", "catch", "prepareLoading", "loadOne", "fallback<PERSON><PERSON><PERSON>", "isUpdate", "clb", "initImmediate", "preload", "partialBundledLanguages", "ret", "tDescription", "transformOptions", "noop", "bindMemberFunctions", "inst", "mems", "getOwnPropertyNames", "getPrototypeOf", "I18n", "modules", "external", "isInitialized", "isClone", "isInitializing", "defOpts", "createClassOnDemand", "ClassOrObject", "lu", "languageDetector", "detection", "storeApi", "storeApiChained", "deferred", "finish", "initializedStoreOnce", "loadResources", "usedCallback", "append", "resolvedLanguage", "setResolvedLanguage", "reloadResources", "use", "Error", "li", "lngInLngs", "_this2", "isLanguageChangingTo", "setLngProps", "setLng", "cacheUserLanguage", "async", "detect", "getFixedT", "keyPrefix", "_this3", "fixedT", "<PERSON><PERSON><PERSON>", "setDefaultNamespace", "lastLng", "loadNotPending", "loadState", "precheck", "preResult", "loadNamespaces", "loadLanguages", "preloaded", "newLngs", "dir", "rtlLngs", "createInstance", "cloneInstance", "forkResourceStore", "mergedOptions", "membersToCopy", "instance"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/node_modules/i18next/dist/esm/i18next.js"], "sourcesContent": ["const isString = obj => typeof obj === 'string';\nconst defer = () => {\n  let res;\n  let rej;\n  const promise = new Promise((resolve, reject) => {\n    res = resolve;\n    rej = reject;\n  });\n  promise.resolve = res;\n  promise.reject = rej;\n  return promise;\n};\nconst makeString = object => {\n  if (object == null) return '';\n  return '' + object;\n};\nconst copy = (a, s, t) => {\n  a.forEach(m => {\n    if (s[m]) t[m] = s[m];\n  });\n};\nconst lastOfPathSeparatorRegExp = /###/g;\nconst cleanKey = key => key && key.indexOf('###') > -1 ? key.replace(lastOfPathSeparatorRegExp, '.') : key;\nconst canNotTraverseDeeper = object => !object || isString(object);\nconst getLastOfPath = (object, path, Empty) => {\n  const stack = !isString(path) ? path : path.split('.');\n  let stackIndex = 0;\n  while (stackIndex < stack.length - 1) {\n    if (canNotTraverseDeeper(object)) return {};\n    const key = cleanKey(stack[stackIndex]);\n    if (!object[key] && Empty) object[key] = new Empty();\n    if (Object.prototype.hasOwnProperty.call(object, key)) {\n      object = object[key];\n    } else {\n      object = {};\n    }\n    ++stackIndex;\n  }\n  if (canNotTraverseDeeper(object)) return {};\n  return {\n    obj: object,\n    k: cleanKey(stack[stackIndex])\n  };\n};\nconst setPath = (object, path, newValue) => {\n  const {\n    obj,\n    k\n  } = getLastOfPath(object, path, Object);\n  if (obj !== undefined || path.length === 1) {\n    obj[k] = newValue;\n    return;\n  }\n  let e = path[path.length - 1];\n  let p = path.slice(0, path.length - 1);\n  let last = getLastOfPath(object, p, Object);\n  while (last.obj === undefined && p.length) {\n    e = `${p[p.length - 1]}.${e}`;\n    p = p.slice(0, p.length - 1);\n    last = getLastOfPath(object, p, Object);\n    if (last && last.obj && typeof last.obj[`${last.k}.${e}`] !== 'undefined') {\n      last.obj = undefined;\n    }\n  }\n  last.obj[`${last.k}.${e}`] = newValue;\n};\nconst pushPath = (object, path, newValue, concat) => {\n  const {\n    obj,\n    k\n  } = getLastOfPath(object, path, Object);\n  obj[k] = obj[k] || [];\n  obj[k].push(newValue);\n};\nconst getPath = (object, path) => {\n  const {\n    obj,\n    k\n  } = getLastOfPath(object, path);\n  if (!obj) return undefined;\n  return obj[k];\n};\nconst getPathWithDefaults = (data, defaultData, key) => {\n  const value = getPath(data, key);\n  if (value !== undefined) {\n    return value;\n  }\n  return getPath(defaultData, key);\n};\nconst deepExtend = (target, source, overwrite) => {\n  for (const prop in source) {\n    if (prop !== '__proto__' && prop !== 'constructor') {\n      if (prop in target) {\n        if (isString(target[prop]) || target[prop] instanceof String || isString(source[prop]) || source[prop] instanceof String) {\n          if (overwrite) target[prop] = source[prop];\n        } else {\n          deepExtend(target[prop], source[prop], overwrite);\n        }\n      } else {\n        target[prop] = source[prop];\n      }\n    }\n  }\n  return target;\n};\nconst regexEscape = str => str.replace(/[\\-\\[\\]\\/\\{\\}\\(\\)\\*\\+\\?\\.\\\\\\^\\$\\|]/g, '\\\\$&');\nvar _entityMap = {\n  '&': '&amp;',\n  '<': '&lt;',\n  '>': '&gt;',\n  '\"': '&quot;',\n  \"'\": '&#39;',\n  '/': '&#x2F;'\n};\nconst escape = data => {\n  if (isString(data)) {\n    return data.replace(/[&<>\"'\\/]/g, s => _entityMap[s]);\n  }\n  return data;\n};\nclass RegExpCache {\n  constructor(capacity) {\n    this.capacity = capacity;\n    this.regExpMap = new Map();\n    this.regExpQueue = [];\n  }\n  getRegExp(pattern) {\n    const regExpFromCache = this.regExpMap.get(pattern);\n    if (regExpFromCache !== undefined) {\n      return regExpFromCache;\n    }\n    const regExpNew = new RegExp(pattern);\n    if (this.regExpQueue.length === this.capacity) {\n      this.regExpMap.delete(this.regExpQueue.shift());\n    }\n    this.regExpMap.set(pattern, regExpNew);\n    this.regExpQueue.push(pattern);\n    return regExpNew;\n  }\n}\nconst chars = [' ', ',', '?', '!', ';'];\nconst looksLikeObjectPathRegExpCache = new RegExpCache(20);\nconst looksLikeObjectPath = (key, nsSeparator, keySeparator) => {\n  nsSeparator = nsSeparator || '';\n  keySeparator = keySeparator || '';\n  const possibleChars = chars.filter(c => nsSeparator.indexOf(c) < 0 && keySeparator.indexOf(c) < 0);\n  if (possibleChars.length === 0) return true;\n  const r = looksLikeObjectPathRegExpCache.getRegExp(`(${possibleChars.map(c => c === '?' ? '\\\\?' : c).join('|')})`);\n  let matched = !r.test(key);\n  if (!matched) {\n    const ki = key.indexOf(keySeparator);\n    if (ki > 0 && !r.test(key.substring(0, ki))) {\n      matched = true;\n    }\n  }\n  return matched;\n};\nconst deepFind = function (obj, path) {\n  let keySeparator = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : '.';\n  if (!obj) return undefined;\n  if (obj[path]) return obj[path];\n  const tokens = path.split(keySeparator);\n  let current = obj;\n  for (let i = 0; i < tokens.length;) {\n    if (!current || typeof current !== 'object') {\n      return undefined;\n    }\n    let next;\n    let nextPath = '';\n    for (let j = i; j < tokens.length; ++j) {\n      if (j !== i) {\n        nextPath += keySeparator;\n      }\n      nextPath += tokens[j];\n      next = current[nextPath];\n      if (next !== undefined) {\n        if (['string', 'number', 'boolean'].indexOf(typeof next) > -1 && j < tokens.length - 1) {\n          continue;\n        }\n        i += j - i + 1;\n        break;\n      }\n    }\n    current = next;\n  }\n  return current;\n};\nconst getCleanedCode = code => code && code.replace('_', '-');\n\nconst consoleLogger = {\n  type: 'logger',\n  log(args) {\n    this.output('log', args);\n  },\n  warn(args) {\n    this.output('warn', args);\n  },\n  error(args) {\n    this.output('error', args);\n  },\n  output(type, args) {\n    if (console && console[type]) console[type].apply(console, args);\n  }\n};\nclass Logger {\n  constructor(concreteLogger) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    this.init(concreteLogger, options);\n  }\n  init(concreteLogger) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    this.prefix = options.prefix || 'i18next:';\n    this.logger = concreteLogger || consoleLogger;\n    this.options = options;\n    this.debug = options.debug;\n  }\n  log() {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    return this.forward(args, 'log', '', true);\n  }\n  warn() {\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n    return this.forward(args, 'warn', '', true);\n  }\n  error() {\n    for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n      args[_key3] = arguments[_key3];\n    }\n    return this.forward(args, 'error', '');\n  }\n  deprecate() {\n    for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {\n      args[_key4] = arguments[_key4];\n    }\n    return this.forward(args, 'warn', 'WARNING DEPRECATED: ', true);\n  }\n  forward(args, lvl, prefix, debugOnly) {\n    if (debugOnly && !this.debug) return null;\n    if (isString(args[0])) args[0] = `${prefix}${this.prefix} ${args[0]}`;\n    return this.logger[lvl](args);\n  }\n  create(moduleName) {\n    return new Logger(this.logger, {\n      ...{\n        prefix: `${this.prefix}:${moduleName}:`\n      },\n      ...this.options\n    });\n  }\n  clone(options) {\n    options = options || this.options;\n    options.prefix = options.prefix || this.prefix;\n    return new Logger(this.logger, options);\n  }\n}\nvar baseLogger = new Logger();\n\nclass EventEmitter {\n  constructor() {\n    this.observers = {};\n  }\n  on(events, listener) {\n    events.split(' ').forEach(event => {\n      if (!this.observers[event]) this.observers[event] = new Map();\n      const numListeners = this.observers[event].get(listener) || 0;\n      this.observers[event].set(listener, numListeners + 1);\n    });\n    return this;\n  }\n  off(event, listener) {\n    if (!this.observers[event]) return;\n    if (!listener) {\n      delete this.observers[event];\n      return;\n    }\n    this.observers[event].delete(listener);\n  }\n  emit(event) {\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n    if (this.observers[event]) {\n      const cloned = Array.from(this.observers[event].entries());\n      cloned.forEach(_ref => {\n        let [observer, numTimesAdded] = _ref;\n        for (let i = 0; i < numTimesAdded; i++) {\n          observer(...args);\n        }\n      });\n    }\n    if (this.observers['*']) {\n      const cloned = Array.from(this.observers['*'].entries());\n      cloned.forEach(_ref2 => {\n        let [observer, numTimesAdded] = _ref2;\n        for (let i = 0; i < numTimesAdded; i++) {\n          observer.apply(observer, [event, ...args]);\n        }\n      });\n    }\n  }\n}\n\nclass ResourceStore extends EventEmitter {\n  constructor(data) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n      ns: ['translation'],\n      defaultNS: 'translation'\n    };\n    super();\n    this.data = data || {};\n    this.options = options;\n    if (this.options.keySeparator === undefined) {\n      this.options.keySeparator = '.';\n    }\n    if (this.options.ignoreJSONStructure === undefined) {\n      this.options.ignoreJSONStructure = true;\n    }\n  }\n  addNamespaces(ns) {\n    if (this.options.ns.indexOf(ns) < 0) {\n      this.options.ns.push(ns);\n    }\n  }\n  removeNamespaces(ns) {\n    const index = this.options.ns.indexOf(ns);\n    if (index > -1) {\n      this.options.ns.splice(index, 1);\n    }\n  }\n  getResource(lng, ns, key) {\n    let options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n    const keySeparator = options.keySeparator !== undefined ? options.keySeparator : this.options.keySeparator;\n    const ignoreJSONStructure = options.ignoreJSONStructure !== undefined ? options.ignoreJSONStructure : this.options.ignoreJSONStructure;\n    let path;\n    if (lng.indexOf('.') > -1) {\n      path = lng.split('.');\n    } else {\n      path = [lng, ns];\n      if (key) {\n        if (Array.isArray(key)) {\n          path.push(...key);\n        } else if (isString(key) && keySeparator) {\n          path.push(...key.split(keySeparator));\n        } else {\n          path.push(key);\n        }\n      }\n    }\n    const result = getPath(this.data, path);\n    if (!result && !ns && !key && lng.indexOf('.') > -1) {\n      lng = path[0];\n      ns = path[1];\n      key = path.slice(2).join('.');\n    }\n    if (result || !ignoreJSONStructure || !isString(key)) return result;\n    return deepFind(this.data && this.data[lng] && this.data[lng][ns], key, keySeparator);\n  }\n  addResource(lng, ns, key, value) {\n    let options = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : {\n      silent: false\n    };\n    const keySeparator = options.keySeparator !== undefined ? options.keySeparator : this.options.keySeparator;\n    let path = [lng, ns];\n    if (key) path = path.concat(keySeparator ? key.split(keySeparator) : key);\n    if (lng.indexOf('.') > -1) {\n      path = lng.split('.');\n      value = ns;\n      ns = path[1];\n    }\n    this.addNamespaces(ns);\n    setPath(this.data, path, value);\n    if (!options.silent) this.emit('added', lng, ns, key, value);\n  }\n  addResources(lng, ns, resources) {\n    let options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {\n      silent: false\n    };\n    for (const m in resources) {\n      if (isString(resources[m]) || Array.isArray(resources[m])) this.addResource(lng, ns, m, resources[m], {\n        silent: true\n      });\n    }\n    if (!options.silent) this.emit('added', lng, ns, resources);\n  }\n  addResourceBundle(lng, ns, resources, deep, overwrite) {\n    let options = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : {\n      silent: false,\n      skipCopy: false\n    };\n    let path = [lng, ns];\n    if (lng.indexOf('.') > -1) {\n      path = lng.split('.');\n      deep = resources;\n      resources = ns;\n      ns = path[1];\n    }\n    this.addNamespaces(ns);\n    let pack = getPath(this.data, path) || {};\n    if (!options.skipCopy) resources = JSON.parse(JSON.stringify(resources));\n    if (deep) {\n      deepExtend(pack, resources, overwrite);\n    } else {\n      pack = {\n        ...pack,\n        ...resources\n      };\n    }\n    setPath(this.data, path, pack);\n    if (!options.silent) this.emit('added', lng, ns, resources);\n  }\n  removeResourceBundle(lng, ns) {\n    if (this.hasResourceBundle(lng, ns)) {\n      delete this.data[lng][ns];\n    }\n    this.removeNamespaces(ns);\n    this.emit('removed', lng, ns);\n  }\n  hasResourceBundle(lng, ns) {\n    return this.getResource(lng, ns) !== undefined;\n  }\n  getResourceBundle(lng, ns) {\n    if (!ns) ns = this.options.defaultNS;\n    if (this.options.compatibilityAPI === 'v1') return {\n      ...{},\n      ...this.getResource(lng, ns)\n    };\n    return this.getResource(lng, ns);\n  }\n  getDataByLanguage(lng) {\n    return this.data[lng];\n  }\n  hasLanguageSomeTranslations(lng) {\n    const data = this.getDataByLanguage(lng);\n    const n = data && Object.keys(data) || [];\n    return !!n.find(v => data[v] && Object.keys(data[v]).length > 0);\n  }\n  toJSON() {\n    return this.data;\n  }\n}\n\nvar postProcessor = {\n  processors: {},\n  addPostProcessor(module) {\n    this.processors[module.name] = module;\n  },\n  handle(processors, value, key, options, translator) {\n    processors.forEach(processor => {\n      if (this.processors[processor]) value = this.processors[processor].process(value, key, options, translator);\n    });\n    return value;\n  }\n};\n\nconst checkedLoadedFor = {};\nclass Translator extends EventEmitter {\n  constructor(services) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    super();\n    copy(['resourceStore', 'languageUtils', 'pluralResolver', 'interpolator', 'backendConnector', 'i18nFormat', 'utils'], services, this);\n    this.options = options;\n    if (this.options.keySeparator === undefined) {\n      this.options.keySeparator = '.';\n    }\n    this.logger = baseLogger.create('translator');\n  }\n  changeLanguage(lng) {\n    if (lng) this.language = lng;\n  }\n  exists(key) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n      interpolation: {}\n    };\n    if (key === undefined || key === null) {\n      return false;\n    }\n    const resolved = this.resolve(key, options);\n    return resolved && resolved.res !== undefined;\n  }\n  extractFromKey(key, options) {\n    let nsSeparator = options.nsSeparator !== undefined ? options.nsSeparator : this.options.nsSeparator;\n    if (nsSeparator === undefined) nsSeparator = ':';\n    const keySeparator = options.keySeparator !== undefined ? options.keySeparator : this.options.keySeparator;\n    let namespaces = options.ns || this.options.defaultNS || [];\n    const wouldCheckForNsInKey = nsSeparator && key.indexOf(nsSeparator) > -1;\n    const seemsNaturalLanguage = !this.options.userDefinedKeySeparator && !options.keySeparator && !this.options.userDefinedNsSeparator && !options.nsSeparator && !looksLikeObjectPath(key, nsSeparator, keySeparator);\n    if (wouldCheckForNsInKey && !seemsNaturalLanguage) {\n      const m = key.match(this.interpolator.nestingRegexp);\n      if (m && m.length > 0) {\n        return {\n          key,\n          namespaces: isString(namespaces) ? [namespaces] : namespaces\n        };\n      }\n      const parts = key.split(nsSeparator);\n      if (nsSeparator !== keySeparator || nsSeparator === keySeparator && this.options.ns.indexOf(parts[0]) > -1) namespaces = parts.shift();\n      key = parts.join(keySeparator);\n    }\n    return {\n      key,\n      namespaces: isString(namespaces) ? [namespaces] : namespaces\n    };\n  }\n  translate(keys, options, lastKey) {\n    if (typeof options !== 'object' && this.options.overloadTranslationOptionHandler) {\n      options = this.options.overloadTranslationOptionHandler(arguments);\n    }\n    if (typeof options === 'object') options = {\n      ...options\n    };\n    if (!options) options = {};\n    if (keys === undefined || keys === null) return '';\n    if (!Array.isArray(keys)) keys = [String(keys)];\n    const returnDetails = options.returnDetails !== undefined ? options.returnDetails : this.options.returnDetails;\n    const keySeparator = options.keySeparator !== undefined ? options.keySeparator : this.options.keySeparator;\n    const {\n      key,\n      namespaces\n    } = this.extractFromKey(keys[keys.length - 1], options);\n    const namespace = namespaces[namespaces.length - 1];\n    const lng = options.lng || this.language;\n    const appendNamespaceToCIMode = options.appendNamespaceToCIMode || this.options.appendNamespaceToCIMode;\n    if (lng && lng.toLowerCase() === 'cimode') {\n      if (appendNamespaceToCIMode) {\n        const nsSeparator = options.nsSeparator || this.options.nsSeparator;\n        if (returnDetails) {\n          return {\n            res: `${namespace}${nsSeparator}${key}`,\n            usedKey: key,\n            exactUsedKey: key,\n            usedLng: lng,\n            usedNS: namespace,\n            usedParams: this.getUsedParamsDetails(options)\n          };\n        }\n        return `${namespace}${nsSeparator}${key}`;\n      }\n      if (returnDetails) {\n        return {\n          res: key,\n          usedKey: key,\n          exactUsedKey: key,\n          usedLng: lng,\n          usedNS: namespace,\n          usedParams: this.getUsedParamsDetails(options)\n        };\n      }\n      return key;\n    }\n    const resolved = this.resolve(keys, options);\n    let res = resolved && resolved.res;\n    const resUsedKey = resolved && resolved.usedKey || key;\n    const resExactUsedKey = resolved && resolved.exactUsedKey || key;\n    const resType = Object.prototype.toString.apply(res);\n    const noObject = ['[object Number]', '[object Function]', '[object RegExp]'];\n    const joinArrays = options.joinArrays !== undefined ? options.joinArrays : this.options.joinArrays;\n    const handleAsObjectInI18nFormat = !this.i18nFormat || this.i18nFormat.handleAsObject;\n    const handleAsObject = !isString(res) && typeof res !== 'boolean' && typeof res !== 'number';\n    if (handleAsObjectInI18nFormat && res && handleAsObject && noObject.indexOf(resType) < 0 && !(isString(joinArrays) && Array.isArray(res))) {\n      if (!options.returnObjects && !this.options.returnObjects) {\n        if (!this.options.returnedObjectHandler) {\n          this.logger.warn('accessing an object - but returnObjects options is not enabled!');\n        }\n        const r = this.options.returnedObjectHandler ? this.options.returnedObjectHandler(resUsedKey, res, {\n          ...options,\n          ns: namespaces\n        }) : `key '${key} (${this.language})' returned an object instead of string.`;\n        if (returnDetails) {\n          resolved.res = r;\n          resolved.usedParams = this.getUsedParamsDetails(options);\n          return resolved;\n        }\n        return r;\n      }\n      if (keySeparator) {\n        const resTypeIsArray = Array.isArray(res);\n        const copy = resTypeIsArray ? [] : {};\n        const newKeyToUse = resTypeIsArray ? resExactUsedKey : resUsedKey;\n        for (const m in res) {\n          if (Object.prototype.hasOwnProperty.call(res, m)) {\n            const deepKey = `${newKeyToUse}${keySeparator}${m}`;\n            copy[m] = this.translate(deepKey, {\n              ...options,\n              ...{\n                joinArrays: false,\n                ns: namespaces\n              }\n            });\n            if (copy[m] === deepKey) copy[m] = res[m];\n          }\n        }\n        res = copy;\n      }\n    } else if (handleAsObjectInI18nFormat && isString(joinArrays) && Array.isArray(res)) {\n      res = res.join(joinArrays);\n      if (res) res = this.extendTranslation(res, keys, options, lastKey);\n    } else {\n      let usedDefault = false;\n      let usedKey = false;\n      const needsPluralHandling = options.count !== undefined && !isString(options.count);\n      const hasDefaultValue = Translator.hasDefaultValue(options);\n      const defaultValueSuffix = needsPluralHandling ? this.pluralResolver.getSuffix(lng, options.count, options) : '';\n      const defaultValueSuffixOrdinalFallback = options.ordinal && needsPluralHandling ? this.pluralResolver.getSuffix(lng, options.count, {\n        ordinal: false\n      }) : '';\n      const needsZeroSuffixLookup = needsPluralHandling && !options.ordinal && options.count === 0 && this.pluralResolver.shouldUseIntlApi();\n      const defaultValue = needsZeroSuffixLookup && options[`defaultValue${this.options.pluralSeparator}zero`] || options[`defaultValue${defaultValueSuffix}`] || options[`defaultValue${defaultValueSuffixOrdinalFallback}`] || options.defaultValue;\n      if (!this.isValidLookup(res) && hasDefaultValue) {\n        usedDefault = true;\n        res = defaultValue;\n      }\n      if (!this.isValidLookup(res)) {\n        usedKey = true;\n        res = key;\n      }\n      const missingKeyNoValueFallbackToKey = options.missingKeyNoValueFallbackToKey || this.options.missingKeyNoValueFallbackToKey;\n      const resForMissing = missingKeyNoValueFallbackToKey && usedKey ? undefined : res;\n      const updateMissing = hasDefaultValue && defaultValue !== res && this.options.updateMissing;\n      if (usedKey || usedDefault || updateMissing) {\n        this.logger.log(updateMissing ? 'updateKey' : 'missingKey', lng, namespace, key, updateMissing ? defaultValue : res);\n        if (keySeparator) {\n          const fk = this.resolve(key, {\n            ...options,\n            keySeparator: false\n          });\n          if (fk && fk.res) this.logger.warn('Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.');\n        }\n        let lngs = [];\n        const fallbackLngs = this.languageUtils.getFallbackCodes(this.options.fallbackLng, options.lng || this.language);\n        if (this.options.saveMissingTo === 'fallback' && fallbackLngs && fallbackLngs[0]) {\n          for (let i = 0; i < fallbackLngs.length; i++) {\n            lngs.push(fallbackLngs[i]);\n          }\n        } else if (this.options.saveMissingTo === 'all') {\n          lngs = this.languageUtils.toResolveHierarchy(options.lng || this.language);\n        } else {\n          lngs.push(options.lng || this.language);\n        }\n        const send = (l, k, specificDefaultValue) => {\n          const defaultForMissing = hasDefaultValue && specificDefaultValue !== res ? specificDefaultValue : resForMissing;\n          if (this.options.missingKeyHandler) {\n            this.options.missingKeyHandler(l, namespace, k, defaultForMissing, updateMissing, options);\n          } else if (this.backendConnector && this.backendConnector.saveMissing) {\n            this.backendConnector.saveMissing(l, namespace, k, defaultForMissing, updateMissing, options);\n          }\n          this.emit('missingKey', l, namespace, k, res);\n        };\n        if (this.options.saveMissing) {\n          if (this.options.saveMissingPlurals && needsPluralHandling) {\n            lngs.forEach(language => {\n              const suffixes = this.pluralResolver.getSuffixes(language, options);\n              if (needsZeroSuffixLookup && options[`defaultValue${this.options.pluralSeparator}zero`] && suffixes.indexOf(`${this.options.pluralSeparator}zero`) < 0) {\n                suffixes.push(`${this.options.pluralSeparator}zero`);\n              }\n              suffixes.forEach(suffix => {\n                send([language], key + suffix, options[`defaultValue${suffix}`] || defaultValue);\n              });\n            });\n          } else {\n            send(lngs, key, defaultValue);\n          }\n        }\n      }\n      res = this.extendTranslation(res, keys, options, resolved, lastKey);\n      if (usedKey && res === key && this.options.appendNamespaceToMissingKey) res = `${namespace}:${key}`;\n      if ((usedKey || usedDefault) && this.options.parseMissingKeyHandler) {\n        if (this.options.compatibilityAPI !== 'v1') {\n          res = this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey ? `${namespace}:${key}` : key, usedDefault ? res : undefined);\n        } else {\n          res = this.options.parseMissingKeyHandler(res);\n        }\n      }\n    }\n    if (returnDetails) {\n      resolved.res = res;\n      resolved.usedParams = this.getUsedParamsDetails(options);\n      return resolved;\n    }\n    return res;\n  }\n  extendTranslation(res, key, options, resolved, lastKey) {\n    var _this = this;\n    if (this.i18nFormat && this.i18nFormat.parse) {\n      res = this.i18nFormat.parse(res, {\n        ...this.options.interpolation.defaultVariables,\n        ...options\n      }, options.lng || this.language || resolved.usedLng, resolved.usedNS, resolved.usedKey, {\n        resolved\n      });\n    } else if (!options.skipInterpolation) {\n      if (options.interpolation) this.interpolator.init({\n        ...options,\n        ...{\n          interpolation: {\n            ...this.options.interpolation,\n            ...options.interpolation\n          }\n        }\n      });\n      const skipOnVariables = isString(res) && (options && options.interpolation && options.interpolation.skipOnVariables !== undefined ? options.interpolation.skipOnVariables : this.options.interpolation.skipOnVariables);\n      let nestBef;\n      if (skipOnVariables) {\n        const nb = res.match(this.interpolator.nestingRegexp);\n        nestBef = nb && nb.length;\n      }\n      let data = options.replace && !isString(options.replace) ? options.replace : options;\n      if (this.options.interpolation.defaultVariables) data = {\n        ...this.options.interpolation.defaultVariables,\n        ...data\n      };\n      res = this.interpolator.interpolate(res, data, options.lng || this.language || resolved.usedLng, options);\n      if (skipOnVariables) {\n        const na = res.match(this.interpolator.nestingRegexp);\n        const nestAft = na && na.length;\n        if (nestBef < nestAft) options.nest = false;\n      }\n      if (!options.lng && this.options.compatibilityAPI !== 'v1' && resolved && resolved.res) options.lng = this.language || resolved.usedLng;\n      if (options.nest !== false) res = this.interpolator.nest(res, function () {\n        for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n          args[_key] = arguments[_key];\n        }\n        if (lastKey && lastKey[0] === args[0] && !options.context) {\n          _this.logger.warn(`It seems you are nesting recursively key: ${args[0]} in key: ${key[0]}`);\n          return null;\n        }\n        return _this.translate(...args, key);\n      }, options);\n      if (options.interpolation) this.interpolator.reset();\n    }\n    const postProcess = options.postProcess || this.options.postProcess;\n    const postProcessorNames = isString(postProcess) ? [postProcess] : postProcess;\n    if (res !== undefined && res !== null && postProcessorNames && postProcessorNames.length && options.applyPostProcessor !== false) {\n      res = postProcessor.handle(postProcessorNames, res, key, this.options && this.options.postProcessPassResolved ? {\n        i18nResolved: {\n          ...resolved,\n          usedParams: this.getUsedParamsDetails(options)\n        },\n        ...options\n      } : options, this);\n    }\n    return res;\n  }\n  resolve(keys) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    let found;\n    let usedKey;\n    let exactUsedKey;\n    let usedLng;\n    let usedNS;\n    if (isString(keys)) keys = [keys];\n    keys.forEach(k => {\n      if (this.isValidLookup(found)) return;\n      const extracted = this.extractFromKey(k, options);\n      const key = extracted.key;\n      usedKey = key;\n      let namespaces = extracted.namespaces;\n      if (this.options.fallbackNS) namespaces = namespaces.concat(this.options.fallbackNS);\n      const needsPluralHandling = options.count !== undefined && !isString(options.count);\n      const needsZeroSuffixLookup = needsPluralHandling && !options.ordinal && options.count === 0 && this.pluralResolver.shouldUseIntlApi();\n      const needsContextHandling = options.context !== undefined && (isString(options.context) || typeof options.context === 'number') && options.context !== '';\n      const codes = options.lngs ? options.lngs : this.languageUtils.toResolveHierarchy(options.lng || this.language, options.fallbackLng);\n      namespaces.forEach(ns => {\n        if (this.isValidLookup(found)) return;\n        usedNS = ns;\n        if (!checkedLoadedFor[`${codes[0]}-${ns}`] && this.utils && this.utils.hasLoadedNamespace && !this.utils.hasLoadedNamespace(usedNS)) {\n          checkedLoadedFor[`${codes[0]}-${ns}`] = true;\n          this.logger.warn(`key \"${usedKey}\" for languages \"${codes.join(', ')}\" won't get resolved as namespace \"${usedNS}\" was not yet loaded`, 'This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!');\n        }\n        codes.forEach(code => {\n          if (this.isValidLookup(found)) return;\n          usedLng = code;\n          const finalKeys = [key];\n          if (this.i18nFormat && this.i18nFormat.addLookupKeys) {\n            this.i18nFormat.addLookupKeys(finalKeys, key, code, ns, options);\n          } else {\n            let pluralSuffix;\n            if (needsPluralHandling) pluralSuffix = this.pluralResolver.getSuffix(code, options.count, options);\n            const zeroSuffix = `${this.options.pluralSeparator}zero`;\n            const ordinalPrefix = `${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;\n            if (needsPluralHandling) {\n              finalKeys.push(key + pluralSuffix);\n              if (options.ordinal && pluralSuffix.indexOf(ordinalPrefix) === 0) {\n                finalKeys.push(key + pluralSuffix.replace(ordinalPrefix, this.options.pluralSeparator));\n              }\n              if (needsZeroSuffixLookup) {\n                finalKeys.push(key + zeroSuffix);\n              }\n            }\n            if (needsContextHandling) {\n              const contextKey = `${key}${this.options.contextSeparator}${options.context}`;\n              finalKeys.push(contextKey);\n              if (needsPluralHandling) {\n                finalKeys.push(contextKey + pluralSuffix);\n                if (options.ordinal && pluralSuffix.indexOf(ordinalPrefix) === 0) {\n                  finalKeys.push(contextKey + pluralSuffix.replace(ordinalPrefix, this.options.pluralSeparator));\n                }\n                if (needsZeroSuffixLookup) {\n                  finalKeys.push(contextKey + zeroSuffix);\n                }\n              }\n            }\n          }\n          let possibleKey;\n          while (possibleKey = finalKeys.pop()) {\n            if (!this.isValidLookup(found)) {\n              exactUsedKey = possibleKey;\n              found = this.getResource(code, ns, possibleKey, options);\n            }\n          }\n        });\n      });\n    });\n    return {\n      res: found,\n      usedKey,\n      exactUsedKey,\n      usedLng,\n      usedNS\n    };\n  }\n  isValidLookup(res) {\n    return res !== undefined && !(!this.options.returnNull && res === null) && !(!this.options.returnEmptyString && res === '');\n  }\n  getResource(code, ns, key) {\n    let options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n    if (this.i18nFormat && this.i18nFormat.getResource) return this.i18nFormat.getResource(code, ns, key, options);\n    return this.resourceStore.getResource(code, ns, key, options);\n  }\n  getUsedParamsDetails() {\n    let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    const optionsKeys = ['defaultValue', 'ordinal', 'context', 'replace', 'lng', 'lngs', 'fallbackLng', 'ns', 'keySeparator', 'nsSeparator', 'returnObjects', 'returnDetails', 'joinArrays', 'postProcess', 'interpolation'];\n    const useOptionsReplaceForData = options.replace && !isString(options.replace);\n    let data = useOptionsReplaceForData ? options.replace : options;\n    if (useOptionsReplaceForData && typeof options.count !== 'undefined') {\n      data.count = options.count;\n    }\n    if (this.options.interpolation.defaultVariables) {\n      data = {\n        ...this.options.interpolation.defaultVariables,\n        ...data\n      };\n    }\n    if (!useOptionsReplaceForData) {\n      data = {\n        ...data\n      };\n      for (const key of optionsKeys) {\n        delete data[key];\n      }\n    }\n    return data;\n  }\n  static hasDefaultValue(options) {\n    const prefix = 'defaultValue';\n    for (const option in options) {\n      if (Object.prototype.hasOwnProperty.call(options, option) && prefix === option.substring(0, prefix.length) && undefined !== options[option]) {\n        return true;\n      }\n    }\n    return false;\n  }\n}\n\nconst capitalize = string => string.charAt(0).toUpperCase() + string.slice(1);\nclass LanguageUtil {\n  constructor(options) {\n    this.options = options;\n    this.supportedLngs = this.options.supportedLngs || false;\n    this.logger = baseLogger.create('languageUtils');\n  }\n  getScriptPartFromCode(code) {\n    code = getCleanedCode(code);\n    if (!code || code.indexOf('-') < 0) return null;\n    const p = code.split('-');\n    if (p.length === 2) return null;\n    p.pop();\n    if (p[p.length - 1].toLowerCase() === 'x') return null;\n    return this.formatLanguageCode(p.join('-'));\n  }\n  getLanguagePartFromCode(code) {\n    code = getCleanedCode(code);\n    if (!code || code.indexOf('-') < 0) return code;\n    const p = code.split('-');\n    return this.formatLanguageCode(p[0]);\n  }\n  formatLanguageCode(code) {\n    if (isString(code) && code.indexOf('-') > -1) {\n      if (typeof Intl !== 'undefined' && typeof Intl.getCanonicalLocales !== 'undefined') {\n        try {\n          let formattedCode = Intl.getCanonicalLocales(code)[0];\n          if (formattedCode && this.options.lowerCaseLng) {\n            formattedCode = formattedCode.toLowerCase();\n          }\n          if (formattedCode) return formattedCode;\n        } catch (e) {}\n      }\n      const specialCases = ['hans', 'hant', 'latn', 'cyrl', 'cans', 'mong', 'arab'];\n      let p = code.split('-');\n      if (this.options.lowerCaseLng) {\n        p = p.map(part => part.toLowerCase());\n      } else if (p.length === 2) {\n        p[0] = p[0].toLowerCase();\n        p[1] = p[1].toUpperCase();\n        if (specialCases.indexOf(p[1].toLowerCase()) > -1) p[1] = capitalize(p[1].toLowerCase());\n      } else if (p.length === 3) {\n        p[0] = p[0].toLowerCase();\n        if (p[1].length === 2) p[1] = p[1].toUpperCase();\n        if (p[0] !== 'sgn' && p[2].length === 2) p[2] = p[2].toUpperCase();\n        if (specialCases.indexOf(p[1].toLowerCase()) > -1) p[1] = capitalize(p[1].toLowerCase());\n        if (specialCases.indexOf(p[2].toLowerCase()) > -1) p[2] = capitalize(p[2].toLowerCase());\n      }\n      return p.join('-');\n    }\n    return this.options.cleanCode || this.options.lowerCaseLng ? code.toLowerCase() : code;\n  }\n  isSupportedCode(code) {\n    if (this.options.load === 'languageOnly' || this.options.nonExplicitSupportedLngs) {\n      code = this.getLanguagePartFromCode(code);\n    }\n    return !this.supportedLngs || !this.supportedLngs.length || this.supportedLngs.indexOf(code) > -1;\n  }\n  getBestMatchFromCodes(codes) {\n    if (!codes) return null;\n    let found;\n    codes.forEach(code => {\n      if (found) return;\n      const cleanedLng = this.formatLanguageCode(code);\n      if (!this.options.supportedLngs || this.isSupportedCode(cleanedLng)) found = cleanedLng;\n    });\n    if (!found && this.options.supportedLngs) {\n      codes.forEach(code => {\n        if (found) return;\n        const lngOnly = this.getLanguagePartFromCode(code);\n        if (this.isSupportedCode(lngOnly)) return found = lngOnly;\n        found = this.options.supportedLngs.find(supportedLng => {\n          if (supportedLng === lngOnly) return supportedLng;\n          if (supportedLng.indexOf('-') < 0 && lngOnly.indexOf('-') < 0) return;\n          if (supportedLng.indexOf('-') > 0 && lngOnly.indexOf('-') < 0 && supportedLng.substring(0, supportedLng.indexOf('-')) === lngOnly) return supportedLng;\n          if (supportedLng.indexOf(lngOnly) === 0 && lngOnly.length > 1) return supportedLng;\n        });\n      });\n    }\n    if (!found) found = this.getFallbackCodes(this.options.fallbackLng)[0];\n    return found;\n  }\n  getFallbackCodes(fallbacks, code) {\n    if (!fallbacks) return [];\n    if (typeof fallbacks === 'function') fallbacks = fallbacks(code);\n    if (isString(fallbacks)) fallbacks = [fallbacks];\n    if (Array.isArray(fallbacks)) return fallbacks;\n    if (!code) return fallbacks.default || [];\n    let found = fallbacks[code];\n    if (!found) found = fallbacks[this.getScriptPartFromCode(code)];\n    if (!found) found = fallbacks[this.formatLanguageCode(code)];\n    if (!found) found = fallbacks[this.getLanguagePartFromCode(code)];\n    if (!found) found = fallbacks.default;\n    return found || [];\n  }\n  toResolveHierarchy(code, fallbackCode) {\n    const fallbackCodes = this.getFallbackCodes(fallbackCode || this.options.fallbackLng || [], code);\n    const codes = [];\n    const addCode = c => {\n      if (!c) return;\n      if (this.isSupportedCode(c)) {\n        codes.push(c);\n      } else {\n        this.logger.warn(`rejecting language code not found in supportedLngs: ${c}`);\n      }\n    };\n    if (isString(code) && (code.indexOf('-') > -1 || code.indexOf('_') > -1)) {\n      if (this.options.load !== 'languageOnly') addCode(this.formatLanguageCode(code));\n      if (this.options.load !== 'languageOnly' && this.options.load !== 'currentOnly') addCode(this.getScriptPartFromCode(code));\n      if (this.options.load !== 'currentOnly') addCode(this.getLanguagePartFromCode(code));\n    } else if (isString(code)) {\n      addCode(this.formatLanguageCode(code));\n    }\n    fallbackCodes.forEach(fc => {\n      if (codes.indexOf(fc) < 0) addCode(this.formatLanguageCode(fc));\n    });\n    return codes;\n  }\n}\n\nlet sets = [{\n  lngs: ['ach', 'ak', 'am', 'arn', 'br', 'fil', 'gun', 'ln', 'mfe', 'mg', 'mi', 'oc', 'pt', 'pt-BR', 'tg', 'tl', 'ti', 'tr', 'uz', 'wa'],\n  nr: [1, 2],\n  fc: 1\n}, {\n  lngs: ['af', 'an', 'ast', 'az', 'bg', 'bn', 'ca', 'da', 'de', 'dev', 'el', 'en', 'eo', 'es', 'et', 'eu', 'fi', 'fo', 'fur', 'fy', 'gl', 'gu', 'ha', 'hi', 'hu', 'hy', 'ia', 'it', 'kk', 'kn', 'ku', 'lb', 'mai', 'ml', 'mn', 'mr', 'nah', 'nap', 'nb', 'ne', 'nl', 'nn', 'no', 'nso', 'pa', 'pap', 'pms', 'ps', 'pt-PT', 'rm', 'sco', 'se', 'si', 'so', 'son', 'sq', 'sv', 'sw', 'ta', 'te', 'tk', 'ur', 'yo'],\n  nr: [1, 2],\n  fc: 2\n}, {\n  lngs: ['ay', 'bo', 'cgg', 'fa', 'ht', 'id', 'ja', 'jbo', 'ka', 'km', 'ko', 'ky', 'lo', 'ms', 'sah', 'su', 'th', 'tt', 'ug', 'vi', 'wo', 'zh'],\n  nr: [1],\n  fc: 3\n}, {\n  lngs: ['be', 'bs', 'cnr', 'dz', 'hr', 'ru', 'sr', 'uk'],\n  nr: [1, 2, 5],\n  fc: 4\n}, {\n  lngs: ['ar'],\n  nr: [0, 1, 2, 3, 11, 100],\n  fc: 5\n}, {\n  lngs: ['cs', 'sk'],\n  nr: [1, 2, 5],\n  fc: 6\n}, {\n  lngs: ['csb', 'pl'],\n  nr: [1, 2, 5],\n  fc: 7\n}, {\n  lngs: ['cy'],\n  nr: [1, 2, 3, 8],\n  fc: 8\n}, {\n  lngs: ['fr'],\n  nr: [1, 2],\n  fc: 9\n}, {\n  lngs: ['ga'],\n  nr: [1, 2, 3, 7, 11],\n  fc: 10\n}, {\n  lngs: ['gd'],\n  nr: [1, 2, 3, 20],\n  fc: 11\n}, {\n  lngs: ['is'],\n  nr: [1, 2],\n  fc: 12\n}, {\n  lngs: ['jv'],\n  nr: [0, 1],\n  fc: 13\n}, {\n  lngs: ['kw'],\n  nr: [1, 2, 3, 4],\n  fc: 14\n}, {\n  lngs: ['lt'],\n  nr: [1, 2, 10],\n  fc: 15\n}, {\n  lngs: ['lv'],\n  nr: [1, 2, 0],\n  fc: 16\n}, {\n  lngs: ['mk'],\n  nr: [1, 2],\n  fc: 17\n}, {\n  lngs: ['mnk'],\n  nr: [0, 1, 2],\n  fc: 18\n}, {\n  lngs: ['mt'],\n  nr: [1, 2, 11, 20],\n  fc: 19\n}, {\n  lngs: ['or'],\n  nr: [2, 1],\n  fc: 2\n}, {\n  lngs: ['ro'],\n  nr: [1, 2, 20],\n  fc: 20\n}, {\n  lngs: ['sl'],\n  nr: [5, 1, 2, 3],\n  fc: 21\n}, {\n  lngs: ['he', 'iw'],\n  nr: [1, 2, 20, 21],\n  fc: 22\n}];\nlet _rulesPluralsTypes = {\n  1: n => Number(n > 1),\n  2: n => Number(n != 1),\n  3: n => 0,\n  4: n => Number(n % 10 == 1 && n % 100 != 11 ? 0 : n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 10 || n % 100 >= 20) ? 1 : 2),\n  5: n => Number(n == 0 ? 0 : n == 1 ? 1 : n == 2 ? 2 : n % 100 >= 3 && n % 100 <= 10 ? 3 : n % 100 >= 11 ? 4 : 5),\n  6: n => Number(n == 1 ? 0 : n >= 2 && n <= 4 ? 1 : 2),\n  7: n => Number(n == 1 ? 0 : n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 10 || n % 100 >= 20) ? 1 : 2),\n  8: n => Number(n == 1 ? 0 : n == 2 ? 1 : n != 8 && n != 11 ? 2 : 3),\n  9: n => Number(n >= 2),\n  10: n => Number(n == 1 ? 0 : n == 2 ? 1 : n < 7 ? 2 : n < 11 ? 3 : 4),\n  11: n => Number(n == 1 || n == 11 ? 0 : n == 2 || n == 12 ? 1 : n > 2 && n < 20 ? 2 : 3),\n  12: n => Number(n % 10 != 1 || n % 100 == 11),\n  13: n => Number(n !== 0),\n  14: n => Number(n == 1 ? 0 : n == 2 ? 1 : n == 3 ? 2 : 3),\n  15: n => Number(n % 10 == 1 && n % 100 != 11 ? 0 : n % 10 >= 2 && (n % 100 < 10 || n % 100 >= 20) ? 1 : 2),\n  16: n => Number(n % 10 == 1 && n % 100 != 11 ? 0 : n !== 0 ? 1 : 2),\n  17: n => Number(n == 1 || n % 10 == 1 && n % 100 != 11 ? 0 : 1),\n  18: n => Number(n == 0 ? 0 : n == 1 ? 1 : 2),\n  19: n => Number(n == 1 ? 0 : n == 0 || n % 100 > 1 && n % 100 < 11 ? 1 : n % 100 > 10 && n % 100 < 20 ? 2 : 3),\n  20: n => Number(n == 1 ? 0 : n == 0 || n % 100 > 0 && n % 100 < 20 ? 1 : 2),\n  21: n => Number(n % 100 == 1 ? 1 : n % 100 == 2 ? 2 : n % 100 == 3 || n % 100 == 4 ? 3 : 0),\n  22: n => Number(n == 1 ? 0 : n == 2 ? 1 : (n < 0 || n > 10) && n % 10 == 0 ? 2 : 3)\n};\nconst nonIntlVersions = ['v1', 'v2', 'v3'];\nconst intlVersions = ['v4'];\nconst suffixesOrder = {\n  zero: 0,\n  one: 1,\n  two: 2,\n  few: 3,\n  many: 4,\n  other: 5\n};\nconst createRules = () => {\n  const rules = {};\n  sets.forEach(set => {\n    set.lngs.forEach(l => {\n      rules[l] = {\n        numbers: set.nr,\n        plurals: _rulesPluralsTypes[set.fc]\n      };\n    });\n  });\n  return rules;\n};\nclass PluralResolver {\n  constructor(languageUtils) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    this.languageUtils = languageUtils;\n    this.options = options;\n    this.logger = baseLogger.create('pluralResolver');\n    if ((!this.options.compatibilityJSON || intlVersions.includes(this.options.compatibilityJSON)) && (typeof Intl === 'undefined' || !Intl.PluralRules)) {\n      this.options.compatibilityJSON = 'v3';\n      this.logger.error('Your environment seems not to be Intl API compatible, use an Intl.PluralRules polyfill. Will fallback to the compatibilityJSON v3 format handling.');\n    }\n    this.rules = createRules();\n    this.pluralRulesCache = {};\n  }\n  addRule(lng, obj) {\n    this.rules[lng] = obj;\n  }\n  clearCache() {\n    this.pluralRulesCache = {};\n  }\n  getRule(code) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    if (this.shouldUseIntlApi()) {\n      const cleanedCode = getCleanedCode(code === 'dev' ? 'en' : code);\n      const type = options.ordinal ? 'ordinal' : 'cardinal';\n      const cacheKey = JSON.stringify({\n        cleanedCode,\n        type\n      });\n      if (cacheKey in this.pluralRulesCache) {\n        return this.pluralRulesCache[cacheKey];\n      }\n      let rule;\n      try {\n        rule = new Intl.PluralRules(cleanedCode, {\n          type\n        });\n      } catch (err) {\n        if (!code.match(/-|_/)) return;\n        const lngPart = this.languageUtils.getLanguagePartFromCode(code);\n        rule = this.getRule(lngPart, options);\n      }\n      this.pluralRulesCache[cacheKey] = rule;\n      return rule;\n    }\n    return this.rules[code] || this.rules[this.languageUtils.getLanguagePartFromCode(code)];\n  }\n  needsPlural(code) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    const rule = this.getRule(code, options);\n    if (this.shouldUseIntlApi()) {\n      return rule && rule.resolvedOptions().pluralCategories.length > 1;\n    }\n    return rule && rule.numbers.length > 1;\n  }\n  getPluralFormsOfKey(code, key) {\n    let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    return this.getSuffixes(code, options).map(suffix => `${key}${suffix}`);\n  }\n  getSuffixes(code) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    const rule = this.getRule(code, options);\n    if (!rule) {\n      return [];\n    }\n    if (this.shouldUseIntlApi()) {\n      return rule.resolvedOptions().pluralCategories.sort((pluralCategory1, pluralCategory2) => suffixesOrder[pluralCategory1] - suffixesOrder[pluralCategory2]).map(pluralCategory => `${this.options.prepend}${options.ordinal ? `ordinal${this.options.prepend}` : ''}${pluralCategory}`);\n    }\n    return rule.numbers.map(number => this.getSuffix(code, number, options));\n  }\n  getSuffix(code, count) {\n    let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    const rule = this.getRule(code, options);\n    if (rule) {\n      if (this.shouldUseIntlApi()) {\n        return `${this.options.prepend}${options.ordinal ? `ordinal${this.options.prepend}` : ''}${rule.select(count)}`;\n      }\n      return this.getSuffixRetroCompatible(rule, count);\n    }\n    this.logger.warn(`no plural rule found for: ${code}`);\n    return '';\n  }\n  getSuffixRetroCompatible(rule, count) {\n    const idx = rule.noAbs ? rule.plurals(count) : rule.plurals(Math.abs(count));\n    let suffix = rule.numbers[idx];\n    if (this.options.simplifyPluralSuffix && rule.numbers.length === 2 && rule.numbers[0] === 1) {\n      if (suffix === 2) {\n        suffix = 'plural';\n      } else if (suffix === 1) {\n        suffix = '';\n      }\n    }\n    const returnSuffix = () => this.options.prepend && suffix.toString() ? this.options.prepend + suffix.toString() : suffix.toString();\n    if (this.options.compatibilityJSON === 'v1') {\n      if (suffix === 1) return '';\n      if (typeof suffix === 'number') return `_plural_${suffix.toString()}`;\n      return returnSuffix();\n    } else if (this.options.compatibilityJSON === 'v2') {\n      return returnSuffix();\n    } else if (this.options.simplifyPluralSuffix && rule.numbers.length === 2 && rule.numbers[0] === 1) {\n      return returnSuffix();\n    }\n    return this.options.prepend && idx.toString() ? this.options.prepend + idx.toString() : idx.toString();\n  }\n  shouldUseIntlApi() {\n    return !nonIntlVersions.includes(this.options.compatibilityJSON);\n  }\n}\n\nconst deepFindWithDefaults = function (data, defaultData, key) {\n  let keySeparator = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : '.';\n  let ignoreJSONStructure = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : true;\n  let path = getPathWithDefaults(data, defaultData, key);\n  if (!path && ignoreJSONStructure && isString(key)) {\n    path = deepFind(data, key, keySeparator);\n    if (path === undefined) path = deepFind(defaultData, key, keySeparator);\n  }\n  return path;\n};\nconst regexSafe = val => val.replace(/\\$/g, '$$$$');\nclass Interpolator {\n  constructor() {\n    let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    this.logger = baseLogger.create('interpolator');\n    this.options = options;\n    this.format = options.interpolation && options.interpolation.format || (value => value);\n    this.init(options);\n  }\n  init() {\n    let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    if (!options.interpolation) options.interpolation = {\n      escapeValue: true\n    };\n    const {\n      escape: escape$1,\n      escapeValue,\n      useRawValueToEscape,\n      prefix,\n      prefixEscaped,\n      suffix,\n      suffixEscaped,\n      formatSeparator,\n      unescapeSuffix,\n      unescapePrefix,\n      nestingPrefix,\n      nestingPrefixEscaped,\n      nestingSuffix,\n      nestingSuffixEscaped,\n      nestingOptionsSeparator,\n      maxReplaces,\n      alwaysFormat\n    } = options.interpolation;\n    this.escape = escape$1 !== undefined ? escape$1 : escape;\n    this.escapeValue = escapeValue !== undefined ? escapeValue : true;\n    this.useRawValueToEscape = useRawValueToEscape !== undefined ? useRawValueToEscape : false;\n    this.prefix = prefix ? regexEscape(prefix) : prefixEscaped || '{{';\n    this.suffix = suffix ? regexEscape(suffix) : suffixEscaped || '}}';\n    this.formatSeparator = formatSeparator || ',';\n    this.unescapePrefix = unescapeSuffix ? '' : unescapePrefix || '-';\n    this.unescapeSuffix = this.unescapePrefix ? '' : unescapeSuffix || '';\n    this.nestingPrefix = nestingPrefix ? regexEscape(nestingPrefix) : nestingPrefixEscaped || regexEscape('$t(');\n    this.nestingSuffix = nestingSuffix ? regexEscape(nestingSuffix) : nestingSuffixEscaped || regexEscape(')');\n    this.nestingOptionsSeparator = nestingOptionsSeparator || ',';\n    this.maxReplaces = maxReplaces || 1000;\n    this.alwaysFormat = alwaysFormat !== undefined ? alwaysFormat : false;\n    this.resetRegExp();\n  }\n  reset() {\n    if (this.options) this.init(this.options);\n  }\n  resetRegExp() {\n    const getOrResetRegExp = (existingRegExp, pattern) => {\n      if (existingRegExp && existingRegExp.source === pattern) {\n        existingRegExp.lastIndex = 0;\n        return existingRegExp;\n      }\n      return new RegExp(pattern, 'g');\n    };\n    this.regexp = getOrResetRegExp(this.regexp, `${this.prefix}(.+?)${this.suffix}`);\n    this.regexpUnescape = getOrResetRegExp(this.regexpUnescape, `${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`);\n    this.nestingRegexp = getOrResetRegExp(this.nestingRegexp, `${this.nestingPrefix}(.+?)${this.nestingSuffix}`);\n  }\n  interpolate(str, data, lng, options) {\n    let match;\n    let value;\n    let replaces;\n    const defaultData = this.options && this.options.interpolation && this.options.interpolation.defaultVariables || {};\n    const handleFormat = key => {\n      if (key.indexOf(this.formatSeparator) < 0) {\n        const path = deepFindWithDefaults(data, defaultData, key, this.options.keySeparator, this.options.ignoreJSONStructure);\n        return this.alwaysFormat ? this.format(path, undefined, lng, {\n          ...options,\n          ...data,\n          interpolationkey: key\n        }) : path;\n      }\n      const p = key.split(this.formatSeparator);\n      const k = p.shift().trim();\n      const f = p.join(this.formatSeparator).trim();\n      return this.format(deepFindWithDefaults(data, defaultData, k, this.options.keySeparator, this.options.ignoreJSONStructure), f, lng, {\n        ...options,\n        ...data,\n        interpolationkey: k\n      });\n    };\n    this.resetRegExp();\n    const missingInterpolationHandler = options && options.missingInterpolationHandler || this.options.missingInterpolationHandler;\n    const skipOnVariables = options && options.interpolation && options.interpolation.skipOnVariables !== undefined ? options.interpolation.skipOnVariables : this.options.interpolation.skipOnVariables;\n    const todos = [{\n      regex: this.regexpUnescape,\n      safeValue: val => regexSafe(val)\n    }, {\n      regex: this.regexp,\n      safeValue: val => this.escapeValue ? regexSafe(this.escape(val)) : regexSafe(val)\n    }];\n    todos.forEach(todo => {\n      replaces = 0;\n      while (match = todo.regex.exec(str)) {\n        const matchedVar = match[1].trim();\n        value = handleFormat(matchedVar);\n        if (value === undefined) {\n          if (typeof missingInterpolationHandler === 'function') {\n            const temp = missingInterpolationHandler(str, match, options);\n            value = isString(temp) ? temp : '';\n          } else if (options && Object.prototype.hasOwnProperty.call(options, matchedVar)) {\n            value = '';\n          } else if (skipOnVariables) {\n            value = match[0];\n            continue;\n          } else {\n            this.logger.warn(`missed to pass in variable ${matchedVar} for interpolating ${str}`);\n            value = '';\n          }\n        } else if (!isString(value) && !this.useRawValueToEscape) {\n          value = makeString(value);\n        }\n        const safeValue = todo.safeValue(value);\n        str = str.replace(match[0], safeValue);\n        if (skipOnVariables) {\n          todo.regex.lastIndex += value.length;\n          todo.regex.lastIndex -= match[0].length;\n        } else {\n          todo.regex.lastIndex = 0;\n        }\n        replaces++;\n        if (replaces >= this.maxReplaces) {\n          break;\n        }\n      }\n    });\n    return str;\n  }\n  nest(str, fc) {\n    let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    let match;\n    let value;\n    let clonedOptions;\n    const handleHasOptions = (key, inheritedOptions) => {\n      const sep = this.nestingOptionsSeparator;\n      if (key.indexOf(sep) < 0) return key;\n      const c = key.split(new RegExp(`${sep}[ ]*{`));\n      let optionsString = `{${c[1]}`;\n      key = c[0];\n      optionsString = this.interpolate(optionsString, clonedOptions);\n      const matchedSingleQuotes = optionsString.match(/'/g);\n      const matchedDoubleQuotes = optionsString.match(/\"/g);\n      if (matchedSingleQuotes && matchedSingleQuotes.length % 2 === 0 && !matchedDoubleQuotes || matchedDoubleQuotes.length % 2 !== 0) {\n        optionsString = optionsString.replace(/'/g, '\"');\n      }\n      try {\n        clonedOptions = JSON.parse(optionsString);\n        if (inheritedOptions) clonedOptions = {\n          ...inheritedOptions,\n          ...clonedOptions\n        };\n      } catch (e) {\n        this.logger.warn(`failed parsing options string in nesting for key ${key}`, e);\n        return `${key}${sep}${optionsString}`;\n      }\n      if (clonedOptions.defaultValue && clonedOptions.defaultValue.indexOf(this.prefix) > -1) delete clonedOptions.defaultValue;\n      return key;\n    };\n    while (match = this.nestingRegexp.exec(str)) {\n      let formatters = [];\n      clonedOptions = {\n        ...options\n      };\n      clonedOptions = clonedOptions.replace && !isString(clonedOptions.replace) ? clonedOptions.replace : clonedOptions;\n      clonedOptions.applyPostProcessor = false;\n      delete clonedOptions.defaultValue;\n      let doReduce = false;\n      if (match[0].indexOf(this.formatSeparator) !== -1 && !/{.*}/.test(match[1])) {\n        const r = match[1].split(this.formatSeparator).map(elem => elem.trim());\n        match[1] = r.shift();\n        formatters = r;\n        doReduce = true;\n      }\n      value = fc(handleHasOptions.call(this, match[1].trim(), clonedOptions), clonedOptions);\n      if (value && match[0] === str && !isString(value)) return value;\n      if (!isString(value)) value = makeString(value);\n      if (!value) {\n        this.logger.warn(`missed to resolve ${match[1]} for nesting ${str}`);\n        value = '';\n      }\n      if (doReduce) {\n        value = formatters.reduce((v, f) => this.format(v, f, options.lng, {\n          ...options,\n          interpolationkey: match[1].trim()\n        }), value.trim());\n      }\n      str = str.replace(match[0], value);\n      this.regexp.lastIndex = 0;\n    }\n    return str;\n  }\n}\n\nconst parseFormatStr = formatStr => {\n  let formatName = formatStr.toLowerCase().trim();\n  const formatOptions = {};\n  if (formatStr.indexOf('(') > -1) {\n    const p = formatStr.split('(');\n    formatName = p[0].toLowerCase().trim();\n    const optStr = p[1].substring(0, p[1].length - 1);\n    if (formatName === 'currency' && optStr.indexOf(':') < 0) {\n      if (!formatOptions.currency) formatOptions.currency = optStr.trim();\n    } else if (formatName === 'relativetime' && optStr.indexOf(':') < 0) {\n      if (!formatOptions.range) formatOptions.range = optStr.trim();\n    } else {\n      const opts = optStr.split(';');\n      opts.forEach(opt => {\n        if (opt) {\n          const [key, ...rest] = opt.split(':');\n          const val = rest.join(':').trim().replace(/^'+|'+$/g, '');\n          const trimmedKey = key.trim();\n          if (!formatOptions[trimmedKey]) formatOptions[trimmedKey] = val;\n          if (val === 'false') formatOptions[trimmedKey] = false;\n          if (val === 'true') formatOptions[trimmedKey] = true;\n          if (!isNaN(val)) formatOptions[trimmedKey] = parseInt(val, 10);\n        }\n      });\n    }\n  }\n  return {\n    formatName,\n    formatOptions\n  };\n};\nconst createCachedFormatter = fn => {\n  const cache = {};\n  return (val, lng, options) => {\n    let optForCache = options;\n    if (options && options.interpolationkey && options.formatParams && options.formatParams[options.interpolationkey] && options[options.interpolationkey]) {\n      optForCache = {\n        ...optForCache,\n        [options.interpolationkey]: undefined\n      };\n    }\n    const key = lng + JSON.stringify(optForCache);\n    let formatter = cache[key];\n    if (!formatter) {\n      formatter = fn(getCleanedCode(lng), options);\n      cache[key] = formatter;\n    }\n    return formatter(val);\n  };\n};\nclass Formatter {\n  constructor() {\n    let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    this.logger = baseLogger.create('formatter');\n    this.options = options;\n    this.formats = {\n      number: createCachedFormatter((lng, opt) => {\n        const formatter = new Intl.NumberFormat(lng, {\n          ...opt\n        });\n        return val => formatter.format(val);\n      }),\n      currency: createCachedFormatter((lng, opt) => {\n        const formatter = new Intl.NumberFormat(lng, {\n          ...opt,\n          style: 'currency'\n        });\n        return val => formatter.format(val);\n      }),\n      datetime: createCachedFormatter((lng, opt) => {\n        const formatter = new Intl.DateTimeFormat(lng, {\n          ...opt\n        });\n        return val => formatter.format(val);\n      }),\n      relativetime: createCachedFormatter((lng, opt) => {\n        const formatter = new Intl.RelativeTimeFormat(lng, {\n          ...opt\n        });\n        return val => formatter.format(val, opt.range || 'day');\n      }),\n      list: createCachedFormatter((lng, opt) => {\n        const formatter = new Intl.ListFormat(lng, {\n          ...opt\n        });\n        return val => formatter.format(val);\n      })\n    };\n    this.init(options);\n  }\n  init(services) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n      interpolation: {}\n    };\n    this.formatSeparator = options.interpolation.formatSeparator || ',';\n  }\n  add(name, fc) {\n    this.formats[name.toLowerCase().trim()] = fc;\n  }\n  addCached(name, fc) {\n    this.formats[name.toLowerCase().trim()] = createCachedFormatter(fc);\n  }\n  format(value, format, lng) {\n    let options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n    const formats = format.split(this.formatSeparator);\n    if (formats.length > 1 && formats[0].indexOf('(') > 1 && formats[0].indexOf(')') < 0 && formats.find(f => f.indexOf(')') > -1)) {\n      const lastIndex = formats.findIndex(f => f.indexOf(')') > -1);\n      formats[0] = [formats[0], ...formats.splice(1, lastIndex)].join(this.formatSeparator);\n    }\n    const result = formats.reduce((mem, f) => {\n      const {\n        formatName,\n        formatOptions\n      } = parseFormatStr(f);\n      if (this.formats[formatName]) {\n        let formatted = mem;\n        try {\n          const valOptions = options && options.formatParams && options.formatParams[options.interpolationkey] || {};\n          const l = valOptions.locale || valOptions.lng || options.locale || options.lng || lng;\n          formatted = this.formats[formatName](mem, l, {\n            ...formatOptions,\n            ...options,\n            ...valOptions\n          });\n        } catch (error) {\n          this.logger.warn(error);\n        }\n        return formatted;\n      } else {\n        this.logger.warn(`there was no format function for ${formatName}`);\n      }\n      return mem;\n    }, value);\n    return result;\n  }\n}\n\nconst removePending = (q, name) => {\n  if (q.pending[name] !== undefined) {\n    delete q.pending[name];\n    q.pendingCount--;\n  }\n};\nclass Connector extends EventEmitter {\n  constructor(backend, store, services) {\n    let options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n    super();\n    this.backend = backend;\n    this.store = store;\n    this.services = services;\n    this.languageUtils = services.languageUtils;\n    this.options = options;\n    this.logger = baseLogger.create('backendConnector');\n    this.waitingReads = [];\n    this.maxParallelReads = options.maxParallelReads || 10;\n    this.readingCalls = 0;\n    this.maxRetries = options.maxRetries >= 0 ? options.maxRetries : 5;\n    this.retryTimeout = options.retryTimeout >= 1 ? options.retryTimeout : 350;\n    this.state = {};\n    this.queue = [];\n    if (this.backend && this.backend.init) {\n      this.backend.init(services, options.backend, options);\n    }\n  }\n  queueLoad(languages, namespaces, options, callback) {\n    const toLoad = {};\n    const pending = {};\n    const toLoadLanguages = {};\n    const toLoadNamespaces = {};\n    languages.forEach(lng => {\n      let hasAllNamespaces = true;\n      namespaces.forEach(ns => {\n        const name = `${lng}|${ns}`;\n        if (!options.reload && this.store.hasResourceBundle(lng, ns)) {\n          this.state[name] = 2;\n        } else if (this.state[name] < 0) ; else if (this.state[name] === 1) {\n          if (pending[name] === undefined) pending[name] = true;\n        } else {\n          this.state[name] = 1;\n          hasAllNamespaces = false;\n          if (pending[name] === undefined) pending[name] = true;\n          if (toLoad[name] === undefined) toLoad[name] = true;\n          if (toLoadNamespaces[ns] === undefined) toLoadNamespaces[ns] = true;\n        }\n      });\n      if (!hasAllNamespaces) toLoadLanguages[lng] = true;\n    });\n    if (Object.keys(toLoad).length || Object.keys(pending).length) {\n      this.queue.push({\n        pending,\n        pendingCount: Object.keys(pending).length,\n        loaded: {},\n        errors: [],\n        callback\n      });\n    }\n    return {\n      toLoad: Object.keys(toLoad),\n      pending: Object.keys(pending),\n      toLoadLanguages: Object.keys(toLoadLanguages),\n      toLoadNamespaces: Object.keys(toLoadNamespaces)\n    };\n  }\n  loaded(name, err, data) {\n    const s = name.split('|');\n    const lng = s[0];\n    const ns = s[1];\n    if (err) this.emit('failedLoading', lng, ns, err);\n    if (!err && data) {\n      this.store.addResourceBundle(lng, ns, data, undefined, undefined, {\n        skipCopy: true\n      });\n    }\n    this.state[name] = err ? -1 : 2;\n    if (err && data) this.state[name] = 0;\n    const loaded = {};\n    this.queue.forEach(q => {\n      pushPath(q.loaded, [lng], ns);\n      removePending(q, name);\n      if (err) q.errors.push(err);\n      if (q.pendingCount === 0 && !q.done) {\n        Object.keys(q.loaded).forEach(l => {\n          if (!loaded[l]) loaded[l] = {};\n          const loadedKeys = q.loaded[l];\n          if (loadedKeys.length) {\n            loadedKeys.forEach(n => {\n              if (loaded[l][n] === undefined) loaded[l][n] = true;\n            });\n          }\n        });\n        q.done = true;\n        if (q.errors.length) {\n          q.callback(q.errors);\n        } else {\n          q.callback();\n        }\n      }\n    });\n    this.emit('loaded', loaded);\n    this.queue = this.queue.filter(q => !q.done);\n  }\n  read(lng, ns, fcName) {\n    let tried = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 0;\n    let wait = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : this.retryTimeout;\n    let callback = arguments.length > 5 ? arguments[5] : undefined;\n    if (!lng.length) return callback(null, {});\n    if (this.readingCalls >= this.maxParallelReads) {\n      this.waitingReads.push({\n        lng,\n        ns,\n        fcName,\n        tried,\n        wait,\n        callback\n      });\n      return;\n    }\n    this.readingCalls++;\n    const resolver = (err, data) => {\n      this.readingCalls--;\n      if (this.waitingReads.length > 0) {\n        const next = this.waitingReads.shift();\n        this.read(next.lng, next.ns, next.fcName, next.tried, next.wait, next.callback);\n      }\n      if (err && data && tried < this.maxRetries) {\n        setTimeout(() => {\n          this.read.call(this, lng, ns, fcName, tried + 1, wait * 2, callback);\n        }, wait);\n        return;\n      }\n      callback(err, data);\n    };\n    const fc = this.backend[fcName].bind(this.backend);\n    if (fc.length === 2) {\n      try {\n        const r = fc(lng, ns);\n        if (r && typeof r.then === 'function') {\n          r.then(data => resolver(null, data)).catch(resolver);\n        } else {\n          resolver(null, r);\n        }\n      } catch (err) {\n        resolver(err);\n      }\n      return;\n    }\n    return fc(lng, ns, resolver);\n  }\n  prepareLoading(languages, namespaces) {\n    let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    let callback = arguments.length > 3 ? arguments[3] : undefined;\n    if (!this.backend) {\n      this.logger.warn('No backend was added via i18next.use. Will not load resources.');\n      return callback && callback();\n    }\n    if (isString(languages)) languages = this.languageUtils.toResolveHierarchy(languages);\n    if (isString(namespaces)) namespaces = [namespaces];\n    const toLoad = this.queueLoad(languages, namespaces, options, callback);\n    if (!toLoad.toLoad.length) {\n      if (!toLoad.pending.length) callback();\n      return null;\n    }\n    toLoad.toLoad.forEach(name => {\n      this.loadOne(name);\n    });\n  }\n  load(languages, namespaces, callback) {\n    this.prepareLoading(languages, namespaces, {}, callback);\n  }\n  reload(languages, namespaces, callback) {\n    this.prepareLoading(languages, namespaces, {\n      reload: true\n    }, callback);\n  }\n  loadOne(name) {\n    let prefix = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';\n    const s = name.split('|');\n    const lng = s[0];\n    const ns = s[1];\n    this.read(lng, ns, 'read', undefined, undefined, (err, data) => {\n      if (err) this.logger.warn(`${prefix}loading namespace ${ns} for language ${lng} failed`, err);\n      if (!err && data) this.logger.log(`${prefix}loaded namespace ${ns} for language ${lng}`, data);\n      this.loaded(name, err, data);\n    });\n  }\n  saveMissing(languages, namespace, key, fallbackValue, isUpdate) {\n    let options = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : {};\n    let clb = arguments.length > 6 && arguments[6] !== undefined ? arguments[6] : () => {};\n    if (this.services.utils && this.services.utils.hasLoadedNamespace && !this.services.utils.hasLoadedNamespace(namespace)) {\n      this.logger.warn(`did not save key \"${key}\" as the namespace \"${namespace}\" was not yet loaded`, 'This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!');\n      return;\n    }\n    if (key === undefined || key === null || key === '') return;\n    if (this.backend && this.backend.create) {\n      const opts = {\n        ...options,\n        isUpdate\n      };\n      const fc = this.backend.create.bind(this.backend);\n      if (fc.length < 6) {\n        try {\n          let r;\n          if (fc.length === 5) {\n            r = fc(languages, namespace, key, fallbackValue, opts);\n          } else {\n            r = fc(languages, namespace, key, fallbackValue);\n          }\n          if (r && typeof r.then === 'function') {\n            r.then(data => clb(null, data)).catch(clb);\n          } else {\n            clb(null, r);\n          }\n        } catch (err) {\n          clb(err);\n        }\n      } else {\n        fc(languages, namespace, key, fallbackValue, clb, opts);\n      }\n    }\n    if (!languages || !languages[0]) return;\n    this.store.addResource(languages[0], namespace, key, fallbackValue);\n  }\n}\n\nconst get = () => ({\n  debug: false,\n  initImmediate: true,\n  ns: ['translation'],\n  defaultNS: ['translation'],\n  fallbackLng: ['dev'],\n  fallbackNS: false,\n  supportedLngs: false,\n  nonExplicitSupportedLngs: false,\n  load: 'all',\n  preload: false,\n  simplifyPluralSuffix: true,\n  keySeparator: '.',\n  nsSeparator: ':',\n  pluralSeparator: '_',\n  contextSeparator: '_',\n  partialBundledLanguages: false,\n  saveMissing: false,\n  updateMissing: false,\n  saveMissingTo: 'fallback',\n  saveMissingPlurals: true,\n  missingKeyHandler: false,\n  missingInterpolationHandler: false,\n  postProcess: false,\n  postProcessPassResolved: false,\n  returnNull: false,\n  returnEmptyString: true,\n  returnObjects: false,\n  joinArrays: false,\n  returnedObjectHandler: false,\n  parseMissingKeyHandler: false,\n  appendNamespaceToMissingKey: false,\n  appendNamespaceToCIMode: false,\n  overloadTranslationOptionHandler: args => {\n    let ret = {};\n    if (typeof args[1] === 'object') ret = args[1];\n    if (isString(args[1])) ret.defaultValue = args[1];\n    if (isString(args[2])) ret.tDescription = args[2];\n    if (typeof args[2] === 'object' || typeof args[3] === 'object') {\n      const options = args[3] || args[2];\n      Object.keys(options).forEach(key => {\n        ret[key] = options[key];\n      });\n    }\n    return ret;\n  },\n  interpolation: {\n    escapeValue: true,\n    format: value => value,\n    prefix: '{{',\n    suffix: '}}',\n    formatSeparator: ',',\n    unescapePrefix: '-',\n    nestingPrefix: '$t(',\n    nestingSuffix: ')',\n    nestingOptionsSeparator: ',',\n    maxReplaces: 1000,\n    skipOnVariables: true\n  }\n});\nconst transformOptions = options => {\n  if (isString(options.ns)) options.ns = [options.ns];\n  if (isString(options.fallbackLng)) options.fallbackLng = [options.fallbackLng];\n  if (isString(options.fallbackNS)) options.fallbackNS = [options.fallbackNS];\n  if (options.supportedLngs && options.supportedLngs.indexOf('cimode') < 0) {\n    options.supportedLngs = options.supportedLngs.concat(['cimode']);\n  }\n  return options;\n};\n\nconst noop = () => {};\nconst bindMemberFunctions = inst => {\n  const mems = Object.getOwnPropertyNames(Object.getPrototypeOf(inst));\n  mems.forEach(mem => {\n    if (typeof inst[mem] === 'function') {\n      inst[mem] = inst[mem].bind(inst);\n    }\n  });\n};\nclass I18n extends EventEmitter {\n  constructor() {\n    let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    let callback = arguments.length > 1 ? arguments[1] : undefined;\n    super();\n    this.options = transformOptions(options);\n    this.services = {};\n    this.logger = baseLogger;\n    this.modules = {\n      external: []\n    };\n    bindMemberFunctions(this);\n    if (callback && !this.isInitialized && !options.isClone) {\n      if (!this.options.initImmediate) {\n        this.init(options, callback);\n        return this;\n      }\n      setTimeout(() => {\n        this.init(options, callback);\n      }, 0);\n    }\n  }\n  init() {\n    var _this = this;\n    let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    let callback = arguments.length > 1 ? arguments[1] : undefined;\n    this.isInitializing = true;\n    if (typeof options === 'function') {\n      callback = options;\n      options = {};\n    }\n    if (!options.defaultNS && options.defaultNS !== false && options.ns) {\n      if (isString(options.ns)) {\n        options.defaultNS = options.ns;\n      } else if (options.ns.indexOf('translation') < 0) {\n        options.defaultNS = options.ns[0];\n      }\n    }\n    const defOpts = get();\n    this.options = {\n      ...defOpts,\n      ...this.options,\n      ...transformOptions(options)\n    };\n    if (this.options.compatibilityAPI !== 'v1') {\n      this.options.interpolation = {\n        ...defOpts.interpolation,\n        ...this.options.interpolation\n      };\n    }\n    if (options.keySeparator !== undefined) {\n      this.options.userDefinedKeySeparator = options.keySeparator;\n    }\n    if (options.nsSeparator !== undefined) {\n      this.options.userDefinedNsSeparator = options.nsSeparator;\n    }\n    const createClassOnDemand = ClassOrObject => {\n      if (!ClassOrObject) return null;\n      if (typeof ClassOrObject === 'function') return new ClassOrObject();\n      return ClassOrObject;\n    };\n    if (!this.options.isClone) {\n      if (this.modules.logger) {\n        baseLogger.init(createClassOnDemand(this.modules.logger), this.options);\n      } else {\n        baseLogger.init(null, this.options);\n      }\n      let formatter;\n      if (this.modules.formatter) {\n        formatter = this.modules.formatter;\n      } else if (typeof Intl !== 'undefined') {\n        formatter = Formatter;\n      }\n      const lu = new LanguageUtil(this.options);\n      this.store = new ResourceStore(this.options.resources, this.options);\n      const s = this.services;\n      s.logger = baseLogger;\n      s.resourceStore = this.store;\n      s.languageUtils = lu;\n      s.pluralResolver = new PluralResolver(lu, {\n        prepend: this.options.pluralSeparator,\n        compatibilityJSON: this.options.compatibilityJSON,\n        simplifyPluralSuffix: this.options.simplifyPluralSuffix\n      });\n      if (formatter && (!this.options.interpolation.format || this.options.interpolation.format === defOpts.interpolation.format)) {\n        s.formatter = createClassOnDemand(formatter);\n        s.formatter.init(s, this.options);\n        this.options.interpolation.format = s.formatter.format.bind(s.formatter);\n      }\n      s.interpolator = new Interpolator(this.options);\n      s.utils = {\n        hasLoadedNamespace: this.hasLoadedNamespace.bind(this)\n      };\n      s.backendConnector = new Connector(createClassOnDemand(this.modules.backend), s.resourceStore, s, this.options);\n      s.backendConnector.on('*', function (event) {\n        for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n          args[_key - 1] = arguments[_key];\n        }\n        _this.emit(event, ...args);\n      });\n      if (this.modules.languageDetector) {\n        s.languageDetector = createClassOnDemand(this.modules.languageDetector);\n        if (s.languageDetector.init) s.languageDetector.init(s, this.options.detection, this.options);\n      }\n      if (this.modules.i18nFormat) {\n        s.i18nFormat = createClassOnDemand(this.modules.i18nFormat);\n        if (s.i18nFormat.init) s.i18nFormat.init(this);\n      }\n      this.translator = new Translator(this.services, this.options);\n      this.translator.on('*', function (event) {\n        for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n          args[_key2 - 1] = arguments[_key2];\n        }\n        _this.emit(event, ...args);\n      });\n      this.modules.external.forEach(m => {\n        if (m.init) m.init(this);\n      });\n    }\n    this.format = this.options.interpolation.format;\n    if (!callback) callback = noop;\n    if (this.options.fallbackLng && !this.services.languageDetector && !this.options.lng) {\n      const codes = this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);\n      if (codes.length > 0 && codes[0] !== 'dev') this.options.lng = codes[0];\n    }\n    if (!this.services.languageDetector && !this.options.lng) {\n      this.logger.warn('init: no languageDetector is used and no lng is defined');\n    }\n    const storeApi = ['getResource', 'hasResourceBundle', 'getResourceBundle', 'getDataByLanguage'];\n    storeApi.forEach(fcName => {\n      this[fcName] = function () {\n        return _this.store[fcName](...arguments);\n      };\n    });\n    const storeApiChained = ['addResource', 'addResources', 'addResourceBundle', 'removeResourceBundle'];\n    storeApiChained.forEach(fcName => {\n      this[fcName] = function () {\n        _this.store[fcName](...arguments);\n        return _this;\n      };\n    });\n    const deferred = defer();\n    const load = () => {\n      const finish = (err, t) => {\n        this.isInitializing = false;\n        if (this.isInitialized && !this.initializedStoreOnce) this.logger.warn('init: i18next is already initialized. You should call init just once!');\n        this.isInitialized = true;\n        if (!this.options.isClone) this.logger.log('initialized', this.options);\n        this.emit('initialized', this.options);\n        deferred.resolve(t);\n        callback(err, t);\n      };\n      if (this.languages && this.options.compatibilityAPI !== 'v1' && !this.isInitialized) return finish(null, this.t.bind(this));\n      this.changeLanguage(this.options.lng, finish);\n    };\n    if (this.options.resources || !this.options.initImmediate) {\n      load();\n    } else {\n      setTimeout(load, 0);\n    }\n    return deferred;\n  }\n  loadResources(language) {\n    let callback = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : noop;\n    let usedCallback = callback;\n    const usedLng = isString(language) ? language : this.language;\n    if (typeof language === 'function') usedCallback = language;\n    if (!this.options.resources || this.options.partialBundledLanguages) {\n      if (usedLng && usedLng.toLowerCase() === 'cimode' && (!this.options.preload || this.options.preload.length === 0)) return usedCallback();\n      const toLoad = [];\n      const append = lng => {\n        if (!lng) return;\n        if (lng === 'cimode') return;\n        const lngs = this.services.languageUtils.toResolveHierarchy(lng);\n        lngs.forEach(l => {\n          if (l === 'cimode') return;\n          if (toLoad.indexOf(l) < 0) toLoad.push(l);\n        });\n      };\n      if (!usedLng) {\n        const fallbacks = this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);\n        fallbacks.forEach(l => append(l));\n      } else {\n        append(usedLng);\n      }\n      if (this.options.preload) {\n        this.options.preload.forEach(l => append(l));\n      }\n      this.services.backendConnector.load(toLoad, this.options.ns, e => {\n        if (!e && !this.resolvedLanguage && this.language) this.setResolvedLanguage(this.language);\n        usedCallback(e);\n      });\n    } else {\n      usedCallback(null);\n    }\n  }\n  reloadResources(lngs, ns, callback) {\n    const deferred = defer();\n    if (typeof lngs === 'function') {\n      callback = lngs;\n      lngs = undefined;\n    }\n    if (typeof ns === 'function') {\n      callback = ns;\n      ns = undefined;\n    }\n    if (!lngs) lngs = this.languages;\n    if (!ns) ns = this.options.ns;\n    if (!callback) callback = noop;\n    this.services.backendConnector.reload(lngs, ns, err => {\n      deferred.resolve();\n      callback(err);\n    });\n    return deferred;\n  }\n  use(module) {\n    if (!module) throw new Error('You are passing an undefined module! Please check the object you are passing to i18next.use()');\n    if (!module.type) throw new Error('You are passing a wrong module! Please check the object you are passing to i18next.use()');\n    if (module.type === 'backend') {\n      this.modules.backend = module;\n    }\n    if (module.type === 'logger' || module.log && module.warn && module.error) {\n      this.modules.logger = module;\n    }\n    if (module.type === 'languageDetector') {\n      this.modules.languageDetector = module;\n    }\n    if (module.type === 'i18nFormat') {\n      this.modules.i18nFormat = module;\n    }\n    if (module.type === 'postProcessor') {\n      postProcessor.addPostProcessor(module);\n    }\n    if (module.type === 'formatter') {\n      this.modules.formatter = module;\n    }\n    if (module.type === '3rdParty') {\n      this.modules.external.push(module);\n    }\n    return this;\n  }\n  setResolvedLanguage(l) {\n    if (!l || !this.languages) return;\n    if (['cimode', 'dev'].indexOf(l) > -1) return;\n    for (let li = 0; li < this.languages.length; li++) {\n      const lngInLngs = this.languages[li];\n      if (['cimode', 'dev'].indexOf(lngInLngs) > -1) continue;\n      if (this.store.hasLanguageSomeTranslations(lngInLngs)) {\n        this.resolvedLanguage = lngInLngs;\n        break;\n      }\n    }\n  }\n  changeLanguage(lng, callback) {\n    var _this2 = this;\n    this.isLanguageChangingTo = lng;\n    const deferred = defer();\n    this.emit('languageChanging', lng);\n    const setLngProps = l => {\n      this.language = l;\n      this.languages = this.services.languageUtils.toResolveHierarchy(l);\n      this.resolvedLanguage = undefined;\n      this.setResolvedLanguage(l);\n    };\n    const done = (err, l) => {\n      if (l) {\n        setLngProps(l);\n        this.translator.changeLanguage(l);\n        this.isLanguageChangingTo = undefined;\n        this.emit('languageChanged', l);\n        this.logger.log('languageChanged', l);\n      } else {\n        this.isLanguageChangingTo = undefined;\n      }\n      deferred.resolve(function () {\n        return _this2.t(...arguments);\n      });\n      if (callback) callback(err, function () {\n        return _this2.t(...arguments);\n      });\n    };\n    const setLng = lngs => {\n      if (!lng && !lngs && this.services.languageDetector) lngs = [];\n      const l = isString(lngs) ? lngs : this.services.languageUtils.getBestMatchFromCodes(lngs);\n      if (l) {\n        if (!this.language) {\n          setLngProps(l);\n        }\n        if (!this.translator.language) this.translator.changeLanguage(l);\n        if (this.services.languageDetector && this.services.languageDetector.cacheUserLanguage) this.services.languageDetector.cacheUserLanguage(l);\n      }\n      this.loadResources(l, err => {\n        done(err, l);\n      });\n    };\n    if (!lng && this.services.languageDetector && !this.services.languageDetector.async) {\n      setLng(this.services.languageDetector.detect());\n    } else if (!lng && this.services.languageDetector && this.services.languageDetector.async) {\n      if (this.services.languageDetector.detect.length === 0) {\n        this.services.languageDetector.detect().then(setLng);\n      } else {\n        this.services.languageDetector.detect(setLng);\n      }\n    } else {\n      setLng(lng);\n    }\n    return deferred;\n  }\n  getFixedT(lng, ns, keyPrefix) {\n    var _this3 = this;\n    const fixedT = function (key, opts) {\n      let options;\n      if (typeof opts !== 'object') {\n        for (var _len3 = arguments.length, rest = new Array(_len3 > 2 ? _len3 - 2 : 0), _key3 = 2; _key3 < _len3; _key3++) {\n          rest[_key3 - 2] = arguments[_key3];\n        }\n        options = _this3.options.overloadTranslationOptionHandler([key, opts].concat(rest));\n      } else {\n        options = {\n          ...opts\n        };\n      }\n      options.lng = options.lng || fixedT.lng;\n      options.lngs = options.lngs || fixedT.lngs;\n      options.ns = options.ns || fixedT.ns;\n      if (options.keyPrefix !== '') options.keyPrefix = options.keyPrefix || keyPrefix || fixedT.keyPrefix;\n      const keySeparator = _this3.options.keySeparator || '.';\n      let resultKey;\n      if (options.keyPrefix && Array.isArray(key)) {\n        resultKey = key.map(k => `${options.keyPrefix}${keySeparator}${k}`);\n      } else {\n        resultKey = options.keyPrefix ? `${options.keyPrefix}${keySeparator}${key}` : key;\n      }\n      return _this3.t(resultKey, options);\n    };\n    if (isString(lng)) {\n      fixedT.lng = lng;\n    } else {\n      fixedT.lngs = lng;\n    }\n    fixedT.ns = ns;\n    fixedT.keyPrefix = keyPrefix;\n    return fixedT;\n  }\n  t() {\n    return this.translator && this.translator.translate(...arguments);\n  }\n  exists() {\n    return this.translator && this.translator.exists(...arguments);\n  }\n  setDefaultNamespace(ns) {\n    this.options.defaultNS = ns;\n  }\n  hasLoadedNamespace(ns) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    if (!this.isInitialized) {\n      this.logger.warn('hasLoadedNamespace: i18next was not initialized', this.languages);\n      return false;\n    }\n    if (!this.languages || !this.languages.length) {\n      this.logger.warn('hasLoadedNamespace: i18n.languages were undefined or empty', this.languages);\n      return false;\n    }\n    const lng = options.lng || this.resolvedLanguage || this.languages[0];\n    const fallbackLng = this.options ? this.options.fallbackLng : false;\n    const lastLng = this.languages[this.languages.length - 1];\n    if (lng.toLowerCase() === 'cimode') return true;\n    const loadNotPending = (l, n) => {\n      const loadState = this.services.backendConnector.state[`${l}|${n}`];\n      return loadState === -1 || loadState === 0 || loadState === 2;\n    };\n    if (options.precheck) {\n      const preResult = options.precheck(this, loadNotPending);\n      if (preResult !== undefined) return preResult;\n    }\n    if (this.hasResourceBundle(lng, ns)) return true;\n    if (!this.services.backendConnector.backend || this.options.resources && !this.options.partialBundledLanguages) return true;\n    if (loadNotPending(lng, ns) && (!fallbackLng || loadNotPending(lastLng, ns))) return true;\n    return false;\n  }\n  loadNamespaces(ns, callback) {\n    const deferred = defer();\n    if (!this.options.ns) {\n      if (callback) callback();\n      return Promise.resolve();\n    }\n    if (isString(ns)) ns = [ns];\n    ns.forEach(n => {\n      if (this.options.ns.indexOf(n) < 0) this.options.ns.push(n);\n    });\n    this.loadResources(err => {\n      deferred.resolve();\n      if (callback) callback(err);\n    });\n    return deferred;\n  }\n  loadLanguages(lngs, callback) {\n    const deferred = defer();\n    if (isString(lngs)) lngs = [lngs];\n    const preloaded = this.options.preload || [];\n    const newLngs = lngs.filter(lng => preloaded.indexOf(lng) < 0 && this.services.languageUtils.isSupportedCode(lng));\n    if (!newLngs.length) {\n      if (callback) callback();\n      return Promise.resolve();\n    }\n    this.options.preload = preloaded.concat(newLngs);\n    this.loadResources(err => {\n      deferred.resolve();\n      if (callback) callback(err);\n    });\n    return deferred;\n  }\n  dir(lng) {\n    if (!lng) lng = this.resolvedLanguage || (this.languages && this.languages.length > 0 ? this.languages[0] : this.language);\n    if (!lng) return 'rtl';\n    const rtlLngs = ['ar', 'shu', 'sqr', 'ssh', 'xaa', 'yhd', 'yud', 'aao', 'abh', 'abv', 'acm', 'acq', 'acw', 'acx', 'acy', 'adf', 'ads', 'aeb', 'aec', 'afb', 'ajp', 'apc', 'apd', 'arb', 'arq', 'ars', 'ary', 'arz', 'auz', 'avl', 'ayh', 'ayl', 'ayn', 'ayp', 'bbz', 'pga', 'he', 'iw', 'ps', 'pbt', 'pbu', 'pst', 'prp', 'prd', 'ug', 'ur', 'ydd', 'yds', 'yih', 'ji', 'yi', 'hbo', 'men', 'xmn', 'fa', 'jpr', 'peo', 'pes', 'prs', 'dv', 'sam', 'ckb'];\n    const languageUtils = this.services && this.services.languageUtils || new LanguageUtil(get());\n    return rtlLngs.indexOf(languageUtils.getLanguagePartFromCode(lng)) > -1 || lng.toLowerCase().indexOf('-arab') > 1 ? 'rtl' : 'ltr';\n  }\n  static createInstance() {\n    let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    let callback = arguments.length > 1 ? arguments[1] : undefined;\n    return new I18n(options, callback);\n  }\n  cloneInstance() {\n    let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    let callback = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : noop;\n    const forkResourceStore = options.forkResourceStore;\n    if (forkResourceStore) delete options.forkResourceStore;\n    const mergedOptions = {\n      ...this.options,\n      ...options,\n      ...{\n        isClone: true\n      }\n    };\n    const clone = new I18n(mergedOptions);\n    if (options.debug !== undefined || options.prefix !== undefined) {\n      clone.logger = clone.logger.clone(options);\n    }\n    const membersToCopy = ['store', 'services', 'language'];\n    membersToCopy.forEach(m => {\n      clone[m] = this[m];\n    });\n    clone.services = {\n      ...this.services\n    };\n    clone.services.utils = {\n      hasLoadedNamespace: clone.hasLoadedNamespace.bind(clone)\n    };\n    if (forkResourceStore) {\n      clone.store = new ResourceStore(this.store.data, mergedOptions);\n      clone.services.resourceStore = clone.store;\n    }\n    clone.translator = new Translator(clone.services, mergedOptions);\n    clone.translator.on('*', function (event) {\n      for (var _len4 = arguments.length, args = new Array(_len4 > 1 ? _len4 - 1 : 0), _key4 = 1; _key4 < _len4; _key4++) {\n        args[_key4 - 1] = arguments[_key4];\n      }\n      clone.emit(event, ...args);\n    });\n    clone.init(mergedOptions, callback);\n    clone.translator.options = mergedOptions;\n    clone.translator.backendConnector.services.utils = {\n      hasLoadedNamespace: clone.hasLoadedNamespace.bind(clone)\n    };\n    return clone;\n  }\n  toJSON() {\n    return {\n      options: this.options,\n      store: this.store,\n      language: this.language,\n      languages: this.languages,\n      resolvedLanguage: this.resolvedLanguage\n    };\n  }\n}\nconst instance = I18n.createInstance();\ninstance.createInstance = I18n.createInstance;\n\nconst createInstance = instance.createInstance;\nconst dir = instance.dir;\nconst init = instance.init;\nconst loadResources = instance.loadResources;\nconst reloadResources = instance.reloadResources;\nconst use = instance.use;\nconst changeLanguage = instance.changeLanguage;\nconst getFixedT = instance.getFixedT;\nconst t = instance.t;\nconst exists = instance.exists;\nconst setDefaultNamespace = instance.setDefaultNamespace;\nconst hasLoadedNamespace = instance.hasLoadedNamespace;\nconst loadNamespaces = instance.loadNamespaces;\nconst loadLanguages = instance.loadLanguages;\n\nexport { changeLanguage, createInstance, instance as default, dir, exists, getFixedT, hasLoadedNamespace, init, loadLanguages, loadNamespaces, loadResources, reloadResources, setDefaultNamespace, t, use };\n"], "mappings": "AAAA,MAAMA,QAAQ,GAAGC,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ;AAC/C,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAClB,IAAIC,GAAG;EACP,IAAIC,GAAG;EACP,MAAMC,OAAO,GAAG,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IAC/CL,GAAG,GAAGI,OAAO;IACbH,GAAG,GAAGI,MAAM;EACd,CAAC,CAAC;EACFH,OAAO,CAACE,OAAO,GAAGJ,GAAG;EACrBE,OAAO,CAACG,MAAM,GAAGJ,GAAG;EACpB,OAAOC,OAAO;AAChB,CAAC;AACD,MAAMI,UAAU,GAAGC,MAAM,IAAI;EAC3B,IAAIA,MAAM,IAAI,IAAI,EAAE,OAAO,EAAE;EAC7B,OAAO,EAAE,GAAGA,MAAM;AACpB,CAAC;AACD,MAAMC,IAAI,GAAGA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,KAAK;EACxBF,CAAC,CAACG,OAAO,CAACC,CAAC,IAAI;IACb,IAAIH,CAAC,CAACG,CAAC,CAAC,EAAEF,CAAC,CAACE,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACvB,CAAC,CAAC;AACJ,CAAC;AACD,MAAMC,yBAAyB,GAAG,MAAM;AACxC,MAAMC,QAAQ,GAAGC,GAAG,IAAIA,GAAG,IAAIA,GAAG,CAACC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGD,GAAG,CAACE,OAAO,CAACJ,yBAAyB,EAAE,GAAG,CAAC,GAAGE,GAAG;AAC1G,MAAMG,oBAAoB,GAAGZ,MAAM,IAAI,CAACA,MAAM,IAAIV,QAAQ,CAACU,MAAM,CAAC;AAClE,MAAMa,aAAa,GAAGA,CAACb,MAAM,EAAEc,IAAI,EAAEC,KAAK,KAAK;EAC7C,MAAMC,KAAK,GAAG,CAAC1B,QAAQ,CAACwB,IAAI,CAAC,GAAGA,IAAI,GAAGA,IAAI,CAACG,KAAK,CAAC,GAAG,CAAC;EACtD,IAAIC,UAAU,GAAG,CAAC;EAClB,OAAOA,UAAU,GAAGF,KAAK,CAACG,MAAM,GAAG,CAAC,EAAE;IACpC,IAAIP,oBAAoB,CAACZ,MAAM,CAAC,EAAE,OAAO,CAAC,CAAC;IAC3C,MAAMS,GAAG,GAAGD,QAAQ,CAACQ,KAAK,CAACE,UAAU,CAAC,CAAC;IACvC,IAAI,CAAClB,MAAM,CAACS,GAAG,CAAC,IAAIM,KAAK,EAAEf,MAAM,CAACS,GAAG,CAAC,GAAG,IAAIM,KAAK,CAAC,CAAC;IACpD,IAAIK,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACvB,MAAM,EAAES,GAAG,CAAC,EAAE;MACrDT,MAAM,GAAGA,MAAM,CAACS,GAAG,CAAC;IACtB,CAAC,MAAM;MACLT,MAAM,GAAG,CAAC,CAAC;IACb;IACA,EAAEkB,UAAU;EACd;EACA,IAAIN,oBAAoB,CAACZ,MAAM,CAAC,EAAE,OAAO,CAAC,CAAC;EAC3C,OAAO;IACLT,GAAG,EAAES,MAAM;IACXwB,CAAC,EAAEhB,QAAQ,CAACQ,KAAK,CAACE,UAAU,CAAC;EAC/B,CAAC;AACH,CAAC;AACD,MAAMO,OAAO,GAAGA,CAACzB,MAAM,EAAEc,IAAI,EAAEY,QAAQ,KAAK;EAC1C,MAAM;IACJnC,GAAG;IACHiC;EACF,CAAC,GAAGX,aAAa,CAACb,MAAM,EAAEc,IAAI,EAAEM,MAAM,CAAC;EACvC,IAAI7B,GAAG,KAAKoC,SAAS,IAAIb,IAAI,CAACK,MAAM,KAAK,CAAC,EAAE;IAC1C5B,GAAG,CAACiC,CAAC,CAAC,GAAGE,QAAQ;IACjB;EACF;EACA,IAAIE,CAAC,GAAGd,IAAI,CAACA,IAAI,CAACK,MAAM,GAAG,CAAC,CAAC;EAC7B,IAAIU,CAAC,GAAGf,IAAI,CAACgB,KAAK,CAAC,CAAC,EAAEhB,IAAI,CAACK,MAAM,GAAG,CAAC,CAAC;EACtC,IAAIY,IAAI,GAAGlB,aAAa,CAACb,MAAM,EAAE6B,CAAC,EAAET,MAAM,CAAC;EAC3C,OAAOW,IAAI,CAACxC,GAAG,KAAKoC,SAAS,IAAIE,CAAC,CAACV,MAAM,EAAE;IACzCS,CAAC,GAAG,GAAGC,CAAC,CAACA,CAAC,CAACV,MAAM,GAAG,CAAC,CAAC,IAAIS,CAAC,EAAE;IAC7BC,CAAC,GAAGA,CAAC,CAACC,KAAK,CAAC,CAAC,EAAED,CAAC,CAACV,MAAM,GAAG,CAAC,CAAC;IAC5BY,IAAI,GAAGlB,aAAa,CAACb,MAAM,EAAE6B,CAAC,EAAET,MAAM,CAAC;IACvC,IAAIW,IAAI,IAAIA,IAAI,CAACxC,GAAG,IAAI,OAAOwC,IAAI,CAACxC,GAAG,CAAC,GAAGwC,IAAI,CAACP,CAAC,IAAII,CAAC,EAAE,CAAC,KAAK,WAAW,EAAE;MACzEG,IAAI,CAACxC,GAAG,GAAGoC,SAAS;IACtB;EACF;EACAI,IAAI,CAACxC,GAAG,CAAC,GAAGwC,IAAI,CAACP,CAAC,IAAII,CAAC,EAAE,CAAC,GAAGF,QAAQ;AACvC,CAAC;AACD,MAAMM,QAAQ,GAAGA,CAAChC,MAAM,EAAEc,IAAI,EAAEY,QAAQ,EAAEO,MAAM,KAAK;EACnD,MAAM;IACJ1C,GAAG;IACHiC;EACF,CAAC,GAAGX,aAAa,CAACb,MAAM,EAAEc,IAAI,EAAEM,MAAM,CAAC;EACvC7B,GAAG,CAACiC,CAAC,CAAC,GAAGjC,GAAG,CAACiC,CAAC,CAAC,IAAI,EAAE;EACrBjC,GAAG,CAACiC,CAAC,CAAC,CAACU,IAAI,CAACR,QAAQ,CAAC;AACvB,CAAC;AACD,MAAMS,OAAO,GAAGA,CAACnC,MAAM,EAAEc,IAAI,KAAK;EAChC,MAAM;IACJvB,GAAG;IACHiC;EACF,CAAC,GAAGX,aAAa,CAACb,MAAM,EAAEc,IAAI,CAAC;EAC/B,IAAI,CAACvB,GAAG,EAAE,OAAOoC,SAAS;EAC1B,OAAOpC,GAAG,CAACiC,CAAC,CAAC;AACf,CAAC;AACD,MAAMY,mBAAmB,GAAGA,CAACC,IAAI,EAAEC,WAAW,EAAE7B,GAAG,KAAK;EACtD,MAAM8B,KAAK,GAAGJ,OAAO,CAACE,IAAI,EAAE5B,GAAG,CAAC;EAChC,IAAI8B,KAAK,KAAKZ,SAAS,EAAE;IACvB,OAAOY,KAAK;EACd;EACA,OAAOJ,OAAO,CAACG,WAAW,EAAE7B,GAAG,CAAC;AAClC,CAAC;AACD,MAAM+B,UAAU,GAAGA,CAACC,MAAM,EAAEC,MAAM,EAAEC,SAAS,KAAK;EAChD,KAAK,MAAMC,IAAI,IAAIF,MAAM,EAAE;IACzB,IAAIE,IAAI,KAAK,WAAW,IAAIA,IAAI,KAAK,aAAa,EAAE;MAClD,IAAIA,IAAI,IAAIH,MAAM,EAAE;QAClB,IAAInD,QAAQ,CAACmD,MAAM,CAACG,IAAI,CAAC,CAAC,IAAIH,MAAM,CAACG,IAAI,CAAC,YAAYC,MAAM,IAAIvD,QAAQ,CAACoD,MAAM,CAACE,IAAI,CAAC,CAAC,IAAIF,MAAM,CAACE,IAAI,CAAC,YAAYC,MAAM,EAAE;UACxH,IAAIF,SAAS,EAAEF,MAAM,CAACG,IAAI,CAAC,GAAGF,MAAM,CAACE,IAAI,CAAC;QAC5C,CAAC,MAAM;UACLJ,UAAU,CAACC,MAAM,CAACG,IAAI,CAAC,EAAEF,MAAM,CAACE,IAAI,CAAC,EAAED,SAAS,CAAC;QACnD;MACF,CAAC,MAAM;QACLF,MAAM,CAACG,IAAI,CAAC,GAAGF,MAAM,CAACE,IAAI,CAAC;MAC7B;IACF;EACF;EACA,OAAOH,MAAM;AACf,CAAC;AACD,MAAMK,WAAW,GAAGC,GAAG,IAAIA,GAAG,CAACpC,OAAO,CAAC,qCAAqC,EAAE,MAAM,CAAC;AACrF,IAAIqC,UAAU,GAAG;EACf,GAAG,EAAE,OAAO;EACZ,GAAG,EAAE,MAAM;EACX,GAAG,EAAE,MAAM;EACX,GAAG,EAAE,QAAQ;EACb,GAAG,EAAE,OAAO;EACZ,GAAG,EAAE;AACP,CAAC;AACD,MAAMC,MAAM,GAAGZ,IAAI,IAAI;EACrB,IAAI/C,QAAQ,CAAC+C,IAAI,CAAC,EAAE;IAClB,OAAOA,IAAI,CAAC1B,OAAO,CAAC,YAAY,EAAER,CAAC,IAAI6C,UAAU,CAAC7C,CAAC,CAAC,CAAC;EACvD;EACA,OAAOkC,IAAI;AACb,CAAC;AACD,MAAMa,WAAW,CAAC;EAChBC,WAAWA,CAACC,QAAQ,EAAE;IACpB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC1B,IAAI,CAACC,WAAW,GAAG,EAAE;EACvB;EACAC,SAASA,CAACC,OAAO,EAAE;IACjB,MAAMC,eAAe,GAAG,IAAI,CAACL,SAAS,CAACM,GAAG,CAACF,OAAO,CAAC;IACnD,IAAIC,eAAe,KAAK/B,SAAS,EAAE;MACjC,OAAO+B,eAAe;IACxB;IACA,MAAME,SAAS,GAAG,IAAIC,MAAM,CAACJ,OAAO,CAAC;IACrC,IAAI,IAAI,CAACF,WAAW,CAACpC,MAAM,KAAK,IAAI,CAACiC,QAAQ,EAAE;MAC7C,IAAI,CAACC,SAAS,CAACS,MAAM,CAAC,IAAI,CAACP,WAAW,CAACQ,KAAK,CAAC,CAAC,CAAC;IACjD;IACA,IAAI,CAACV,SAAS,CAACW,GAAG,CAACP,OAAO,EAAEG,SAAS,CAAC;IACtC,IAAI,CAACL,WAAW,CAACrB,IAAI,CAACuB,OAAO,CAAC;IAC9B,OAAOG,SAAS;EAClB;AACF;AACA,MAAMK,KAAK,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AACvC,MAAMC,8BAA8B,GAAG,IAAIhB,WAAW,CAAC,EAAE,CAAC;AAC1D,MAAMiB,mBAAmB,GAAGA,CAAC1D,GAAG,EAAE2D,WAAW,EAAEC,YAAY,KAAK;EAC9DD,WAAW,GAAGA,WAAW,IAAI,EAAE;EAC/BC,YAAY,GAAGA,YAAY,IAAI,EAAE;EACjC,MAAMC,aAAa,GAAGL,KAAK,CAACM,MAAM,CAACC,CAAC,IAAIJ,WAAW,CAAC1D,OAAO,CAAC8D,CAAC,CAAC,GAAG,CAAC,IAAIH,YAAY,CAAC3D,OAAO,CAAC8D,CAAC,CAAC,GAAG,CAAC,CAAC;EAClG,IAAIF,aAAa,CAACnD,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;EAC3C,MAAMsD,CAAC,GAAGP,8BAA8B,CAACV,SAAS,CAAC,IAAIc,aAAa,CAACI,GAAG,CAACF,CAAC,IAAIA,CAAC,KAAK,GAAG,GAAG,KAAK,GAAGA,CAAC,CAAC,CAACG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;EAClH,IAAIC,OAAO,GAAG,CAACH,CAAC,CAACI,IAAI,CAACpE,GAAG,CAAC;EAC1B,IAAI,CAACmE,OAAO,EAAE;IACZ,MAAME,EAAE,GAAGrE,GAAG,CAACC,OAAO,CAAC2D,YAAY,CAAC;IACpC,IAAIS,EAAE,GAAG,CAAC,IAAI,CAACL,CAAC,CAACI,IAAI,CAACpE,GAAG,CAACsE,SAAS,CAAC,CAAC,EAAED,EAAE,CAAC,CAAC,EAAE;MAC3CF,OAAO,GAAG,IAAI;IAChB;EACF;EACA,OAAOA,OAAO;AAChB,CAAC;AACD,MAAMI,QAAQ,GAAG,SAAAA,CAAUzF,GAAG,EAAEuB,IAAI,EAAE;EACpC,IAAIuD,YAAY,GAAGY,SAAS,CAAC9D,MAAM,GAAG,CAAC,IAAI8D,SAAS,CAAC,CAAC,CAAC,KAAKtD,SAAS,GAAGsD,SAAS,CAAC,CAAC,CAAC,GAAG,GAAG;EAC1F,IAAI,CAAC1F,GAAG,EAAE,OAAOoC,SAAS;EAC1B,IAAIpC,GAAG,CAACuB,IAAI,CAAC,EAAE,OAAOvB,GAAG,CAACuB,IAAI,CAAC;EAC/B,MAAMoE,MAAM,GAAGpE,IAAI,CAACG,KAAK,CAACoD,YAAY,CAAC;EACvC,IAAIc,OAAO,GAAG5F,GAAG;EACjB,KAAK,IAAI6F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,MAAM,CAAC/D,MAAM,GAAG;IAClC,IAAI,CAACgE,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;MAC3C,OAAOxD,SAAS;IAClB;IACA,IAAI0D,IAAI;IACR,IAAIC,QAAQ,GAAG,EAAE;IACjB,KAAK,IAAIC,CAAC,GAAGH,CAAC,EAAEG,CAAC,GAAGL,MAAM,CAAC/D,MAAM,EAAE,EAAEoE,CAAC,EAAE;MACtC,IAAIA,CAAC,KAAKH,CAAC,EAAE;QACXE,QAAQ,IAAIjB,YAAY;MAC1B;MACAiB,QAAQ,IAAIJ,MAAM,CAACK,CAAC,CAAC;MACrBF,IAAI,GAAGF,OAAO,CAACG,QAAQ,CAAC;MACxB,IAAID,IAAI,KAAK1D,SAAS,EAAE;QACtB,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC,CAACjB,OAAO,CAAC,OAAO2E,IAAI,CAAC,GAAG,CAAC,CAAC,IAAIE,CAAC,GAAGL,MAAM,CAAC/D,MAAM,GAAG,CAAC,EAAE;UACtF;QACF;QACAiE,CAAC,IAAIG,CAAC,GAAGH,CAAC,GAAG,CAAC;QACd;MACF;IACF;IACAD,OAAO,GAAGE,IAAI;EAChB;EACA,OAAOF,OAAO;AAChB,CAAC;AACD,MAAMK,cAAc,GAAGC,IAAI,IAAIA,IAAI,IAAIA,IAAI,CAAC9E,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;AAE7D,MAAM+E,aAAa,GAAG;EACpBC,IAAI,EAAE,QAAQ;EACdC,GAAGA,CAACC,IAAI,EAAE;IACR,IAAI,CAACC,MAAM,CAAC,KAAK,EAAED,IAAI,CAAC;EAC1B,CAAC;EACDE,IAAIA,CAACF,IAAI,EAAE;IACT,IAAI,CAACC,MAAM,CAAC,MAAM,EAAED,IAAI,CAAC;EAC3B,CAAC;EACDG,KAAKA,CAACH,IAAI,EAAE;IACV,IAAI,CAACC,MAAM,CAAC,OAAO,EAAED,IAAI,CAAC;EAC5B,CAAC;EACDC,MAAMA,CAACH,IAAI,EAAEE,IAAI,EAAE;IACjB,IAAII,OAAO,IAAIA,OAAO,CAACN,IAAI,CAAC,EAAEM,OAAO,CAACN,IAAI,CAAC,CAACO,KAAK,CAACD,OAAO,EAAEJ,IAAI,CAAC;EAClE;AACF,CAAC;AACD,MAAMM,MAAM,CAAC;EACXhD,WAAWA,CAACiD,cAAc,EAAE;IAC1B,IAAIC,OAAO,GAAGpB,SAAS,CAAC9D,MAAM,GAAG,CAAC,IAAI8D,SAAS,CAAC,CAAC,CAAC,KAAKtD,SAAS,GAAGsD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACpF,IAAI,CAACqB,IAAI,CAACF,cAAc,EAAEC,OAAO,CAAC;EACpC;EACAC,IAAIA,CAACF,cAAc,EAAE;IACnB,IAAIC,OAAO,GAAGpB,SAAS,CAAC9D,MAAM,GAAG,CAAC,IAAI8D,SAAS,CAAC,CAAC,CAAC,KAAKtD,SAAS,GAAGsD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACpF,IAAI,CAACsB,MAAM,GAAGF,OAAO,CAACE,MAAM,IAAI,UAAU;IAC1C,IAAI,CAACC,MAAM,GAAGJ,cAAc,IAAIV,aAAa;IAC7C,IAAI,CAACW,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACI,KAAK,GAAGJ,OAAO,CAACI,KAAK;EAC5B;EACAb,GAAGA,CAAA,EAAG;IACJ,KAAK,IAAIc,IAAI,GAAGzB,SAAS,CAAC9D,MAAM,EAAE0E,IAAI,GAAG,IAAIc,KAAK,CAACD,IAAI,CAAC,EAAEE,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGF,IAAI,EAAEE,IAAI,EAAE,EAAE;MACvFf,IAAI,CAACe,IAAI,CAAC,GAAG3B,SAAS,CAAC2B,IAAI,CAAC;IAC9B;IACA,OAAO,IAAI,CAACC,OAAO,CAAChB,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE,IAAI,CAAC;EAC5C;EACAE,IAAIA,CAAA,EAAG;IACL,KAAK,IAAIe,KAAK,GAAG7B,SAAS,CAAC9D,MAAM,EAAE0E,IAAI,GAAG,IAAIc,KAAK,CAACG,KAAK,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;MAC7FlB,IAAI,CAACkB,KAAK,CAAC,GAAG9B,SAAS,CAAC8B,KAAK,CAAC;IAChC;IACA,OAAO,IAAI,CAACF,OAAO,CAAChB,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,IAAI,CAAC;EAC7C;EACAG,KAAKA,CAAA,EAAG;IACN,KAAK,IAAIgB,KAAK,GAAG/B,SAAS,CAAC9D,MAAM,EAAE0E,IAAI,GAAG,IAAIc,KAAK,CAACK,KAAK,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;MAC7FpB,IAAI,CAACoB,KAAK,CAAC,GAAGhC,SAAS,CAACgC,KAAK,CAAC;IAChC;IACA,OAAO,IAAI,CAACJ,OAAO,CAAChB,IAAI,EAAE,OAAO,EAAE,EAAE,CAAC;EACxC;EACAqB,SAASA,CAAA,EAAG;IACV,KAAK,IAAIC,KAAK,GAAGlC,SAAS,CAAC9D,MAAM,EAAE0E,IAAI,GAAG,IAAIc,KAAK,CAACQ,KAAK,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;MAC7FvB,IAAI,CAACuB,KAAK,CAAC,GAAGnC,SAAS,CAACmC,KAAK,CAAC;IAChC;IACA,OAAO,IAAI,CAACP,OAAO,CAAChB,IAAI,EAAE,MAAM,EAAE,sBAAsB,EAAE,IAAI,CAAC;EACjE;EACAgB,OAAOA,CAAChB,IAAI,EAAEwB,GAAG,EAAEd,MAAM,EAAEe,SAAS,EAAE;IACpC,IAAIA,SAAS,IAAI,CAAC,IAAI,CAACb,KAAK,EAAE,OAAO,IAAI;IACzC,IAAInH,QAAQ,CAACuG,IAAI,CAAC,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,GAAG,GAAGU,MAAM,GAAG,IAAI,CAACA,MAAM,IAAIV,IAAI,CAAC,CAAC,CAAC,EAAE;IACrE,OAAO,IAAI,CAACW,MAAM,CAACa,GAAG,CAAC,CAACxB,IAAI,CAAC;EAC/B;EACA0B,MAAMA,CAACC,UAAU,EAAE;IACjB,OAAO,IAAIrB,MAAM,CAAC,IAAI,CAACK,MAAM,EAAE;MAC7B,GAAG;QACDD,MAAM,EAAE,GAAG,IAAI,CAACA,MAAM,IAAIiB,UAAU;MACtC,CAAC;MACD,GAAG,IAAI,CAACnB;IACV,CAAC,CAAC;EACJ;EACAoB,KAAKA,CAACpB,OAAO,EAAE;IACbA,OAAO,GAAGA,OAAO,IAAI,IAAI,CAACA,OAAO;IACjCA,OAAO,CAACE,MAAM,GAAGF,OAAO,CAACE,MAAM,IAAI,IAAI,CAACA,MAAM;IAC9C,OAAO,IAAIJ,MAAM,CAAC,IAAI,CAACK,MAAM,EAAEH,OAAO,CAAC;EACzC;AACF;AACA,IAAIqB,UAAU,GAAG,IAAIvB,MAAM,CAAC,CAAC;AAE7B,MAAMwB,YAAY,CAAC;EACjBxE,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACyE,SAAS,GAAG,CAAC,CAAC;EACrB;EACAC,EAAEA,CAACC,MAAM,EAAEC,QAAQ,EAAE;IACnBD,MAAM,CAAC7G,KAAK,CAAC,GAAG,CAAC,CAACZ,OAAO,CAAC2H,KAAK,IAAI;MACjC,IAAI,CAAC,IAAI,CAACJ,SAAS,CAACI,KAAK,CAAC,EAAE,IAAI,CAACJ,SAAS,CAACI,KAAK,CAAC,GAAG,IAAI1E,GAAG,CAAC,CAAC;MAC7D,MAAM2E,YAAY,GAAG,IAAI,CAACL,SAAS,CAACI,KAAK,CAAC,CAACrE,GAAG,CAACoE,QAAQ,CAAC,IAAI,CAAC;MAC7D,IAAI,CAACH,SAAS,CAACI,KAAK,CAAC,CAAChE,GAAG,CAAC+D,QAAQ,EAAEE,YAAY,GAAG,CAAC,CAAC;IACvD,CAAC,CAAC;IACF,OAAO,IAAI;EACb;EACAC,GAAGA,CAACF,KAAK,EAAED,QAAQ,EAAE;IACnB,IAAI,CAAC,IAAI,CAACH,SAAS,CAACI,KAAK,CAAC,EAAE;IAC5B,IAAI,CAACD,QAAQ,EAAE;MACb,OAAO,IAAI,CAACH,SAAS,CAACI,KAAK,CAAC;MAC5B;IACF;IACA,IAAI,CAACJ,SAAS,CAACI,KAAK,CAAC,CAAClE,MAAM,CAACiE,QAAQ,CAAC;EACxC;EACAI,IAAIA,CAACH,KAAK,EAAE;IACV,KAAK,IAAItB,IAAI,GAAGzB,SAAS,CAAC9D,MAAM,EAAE0E,IAAI,GAAG,IAAIc,KAAK,CAACD,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEE,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGF,IAAI,EAAEE,IAAI,EAAE,EAAE;MAC1Gf,IAAI,CAACe,IAAI,GAAG,CAAC,CAAC,GAAG3B,SAAS,CAAC2B,IAAI,CAAC;IAClC;IACA,IAAI,IAAI,CAACgB,SAAS,CAACI,KAAK,CAAC,EAAE;MACzB,MAAMI,MAAM,GAAGzB,KAAK,CAAC0B,IAAI,CAAC,IAAI,CAACT,SAAS,CAACI,KAAK,CAAC,CAACM,OAAO,CAAC,CAAC,CAAC;MAC1DF,MAAM,CAAC/H,OAAO,CAACkI,IAAI,IAAI;QACrB,IAAI,CAACC,QAAQ,EAAEC,aAAa,CAAC,GAAGF,IAAI;QACpC,KAAK,IAAInD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqD,aAAa,EAAErD,CAAC,EAAE,EAAE;UACtCoD,QAAQ,CAAC,GAAG3C,IAAI,CAAC;QACnB;MACF,CAAC,CAAC;IACJ;IACA,IAAI,IAAI,CAAC+B,SAAS,CAAC,GAAG,CAAC,EAAE;MACvB,MAAMQ,MAAM,GAAGzB,KAAK,CAAC0B,IAAI,CAAC,IAAI,CAACT,SAAS,CAAC,GAAG,CAAC,CAACU,OAAO,CAAC,CAAC,CAAC;MACxDF,MAAM,CAAC/H,OAAO,CAACqI,KAAK,IAAI;QACtB,IAAI,CAACF,QAAQ,EAAEC,aAAa,CAAC,GAAGC,KAAK;QACrC,KAAK,IAAItD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqD,aAAa,EAAErD,CAAC,EAAE,EAAE;UACtCoD,QAAQ,CAACtC,KAAK,CAACsC,QAAQ,EAAE,CAACR,KAAK,EAAE,GAAGnC,IAAI,CAAC,CAAC;QAC5C;MACF,CAAC,CAAC;IACJ;EACF;AACF;AAEA,MAAM8C,aAAa,SAAShB,YAAY,CAAC;EACvCxE,WAAWA,CAACd,IAAI,EAAE;IAChB,IAAIgE,OAAO,GAAGpB,SAAS,CAAC9D,MAAM,GAAG,CAAC,IAAI8D,SAAS,CAAC,CAAC,CAAC,KAAKtD,SAAS,GAAGsD,SAAS,CAAC,CAAC,CAAC,GAAG;MAChF2D,EAAE,EAAE,CAAC,aAAa,CAAC;MACnBC,SAAS,EAAE;IACb,CAAC;IACD,KAAK,CAAC,CAAC;IACP,IAAI,CAACxG,IAAI,GAAGA,IAAI,IAAI,CAAC,CAAC;IACtB,IAAI,CAACgE,OAAO,GAAGA,OAAO;IACtB,IAAI,IAAI,CAACA,OAAO,CAAChC,YAAY,KAAK1C,SAAS,EAAE;MAC3C,IAAI,CAAC0E,OAAO,CAAChC,YAAY,GAAG,GAAG;IACjC;IACA,IAAI,IAAI,CAACgC,OAAO,CAACyC,mBAAmB,KAAKnH,SAAS,EAAE;MAClD,IAAI,CAAC0E,OAAO,CAACyC,mBAAmB,GAAG,IAAI;IACzC;EACF;EACAC,aAAaA,CAACH,EAAE,EAAE;IAChB,IAAI,IAAI,CAACvC,OAAO,CAACuC,EAAE,CAAClI,OAAO,CAACkI,EAAE,CAAC,GAAG,CAAC,EAAE;MACnC,IAAI,CAACvC,OAAO,CAACuC,EAAE,CAAC1G,IAAI,CAAC0G,EAAE,CAAC;IAC1B;EACF;EACAI,gBAAgBA,CAACJ,EAAE,EAAE;IACnB,MAAMK,KAAK,GAAG,IAAI,CAAC5C,OAAO,CAACuC,EAAE,CAAClI,OAAO,CAACkI,EAAE,CAAC;IACzC,IAAIK,KAAK,GAAG,CAAC,CAAC,EAAE;MACd,IAAI,CAAC5C,OAAO,CAACuC,EAAE,CAACM,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;IAClC;EACF;EACAE,WAAWA,CAACC,GAAG,EAAER,EAAE,EAAEnI,GAAG,EAAE;IACxB,IAAI4F,OAAO,GAAGpB,SAAS,CAAC9D,MAAM,GAAG,CAAC,IAAI8D,SAAS,CAAC,CAAC,CAAC,KAAKtD,SAAS,GAAGsD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACpF,MAAMZ,YAAY,GAAGgC,OAAO,CAAChC,YAAY,KAAK1C,SAAS,GAAG0E,OAAO,CAAChC,YAAY,GAAG,IAAI,CAACgC,OAAO,CAAChC,YAAY;IAC1G,MAAMyE,mBAAmB,GAAGzC,OAAO,CAACyC,mBAAmB,KAAKnH,SAAS,GAAG0E,OAAO,CAACyC,mBAAmB,GAAG,IAAI,CAACzC,OAAO,CAACyC,mBAAmB;IACtI,IAAIhI,IAAI;IACR,IAAIsI,GAAG,CAAC1I,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;MACzBI,IAAI,GAAGsI,GAAG,CAACnI,KAAK,CAAC,GAAG,CAAC;IACvB,CAAC,MAAM;MACLH,IAAI,GAAG,CAACsI,GAAG,EAAER,EAAE,CAAC;MAChB,IAAInI,GAAG,EAAE;QACP,IAAIkG,KAAK,CAAC0C,OAAO,CAAC5I,GAAG,CAAC,EAAE;UACtBK,IAAI,CAACoB,IAAI,CAAC,GAAGzB,GAAG,CAAC;QACnB,CAAC,MAAM,IAAInB,QAAQ,CAACmB,GAAG,CAAC,IAAI4D,YAAY,EAAE;UACxCvD,IAAI,CAACoB,IAAI,CAAC,GAAGzB,GAAG,CAACQ,KAAK,CAACoD,YAAY,CAAC,CAAC;QACvC,CAAC,MAAM;UACLvD,IAAI,CAACoB,IAAI,CAACzB,GAAG,CAAC;QAChB;MACF;IACF;IACA,MAAM6I,MAAM,GAAGnH,OAAO,CAAC,IAAI,CAACE,IAAI,EAAEvB,IAAI,CAAC;IACvC,IAAI,CAACwI,MAAM,IAAI,CAACV,EAAE,IAAI,CAACnI,GAAG,IAAI2I,GAAG,CAAC1I,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;MACnD0I,GAAG,GAAGtI,IAAI,CAAC,CAAC,CAAC;MACb8H,EAAE,GAAG9H,IAAI,CAAC,CAAC,CAAC;MACZL,GAAG,GAAGK,IAAI,CAACgB,KAAK,CAAC,CAAC,CAAC,CAAC6C,IAAI,CAAC,GAAG,CAAC;IAC/B;IACA,IAAI2E,MAAM,IAAI,CAACR,mBAAmB,IAAI,CAACxJ,QAAQ,CAACmB,GAAG,CAAC,EAAE,OAAO6I,MAAM;IACnE,OAAOtE,QAAQ,CAAC,IAAI,CAAC3C,IAAI,IAAI,IAAI,CAACA,IAAI,CAAC+G,GAAG,CAAC,IAAI,IAAI,CAAC/G,IAAI,CAAC+G,GAAG,CAAC,CAACR,EAAE,CAAC,EAAEnI,GAAG,EAAE4D,YAAY,CAAC;EACvF;EACAkF,WAAWA,CAACH,GAAG,EAAER,EAAE,EAAEnI,GAAG,EAAE8B,KAAK,EAAE;IAC/B,IAAI8D,OAAO,GAAGpB,SAAS,CAAC9D,MAAM,GAAG,CAAC,IAAI8D,SAAS,CAAC,CAAC,CAAC,KAAKtD,SAAS,GAAGsD,SAAS,CAAC,CAAC,CAAC,GAAG;MAChFuE,MAAM,EAAE;IACV,CAAC;IACD,MAAMnF,YAAY,GAAGgC,OAAO,CAAChC,YAAY,KAAK1C,SAAS,GAAG0E,OAAO,CAAChC,YAAY,GAAG,IAAI,CAACgC,OAAO,CAAChC,YAAY;IAC1G,IAAIvD,IAAI,GAAG,CAACsI,GAAG,EAAER,EAAE,CAAC;IACpB,IAAInI,GAAG,EAAEK,IAAI,GAAGA,IAAI,CAACmB,MAAM,CAACoC,YAAY,GAAG5D,GAAG,CAACQ,KAAK,CAACoD,YAAY,CAAC,GAAG5D,GAAG,CAAC;IACzE,IAAI2I,GAAG,CAAC1I,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;MACzBI,IAAI,GAAGsI,GAAG,CAACnI,KAAK,CAAC,GAAG,CAAC;MACrBsB,KAAK,GAAGqG,EAAE;MACVA,EAAE,GAAG9H,IAAI,CAAC,CAAC,CAAC;IACd;IACA,IAAI,CAACiI,aAAa,CAACH,EAAE,CAAC;IACtBnH,OAAO,CAAC,IAAI,CAACY,IAAI,EAAEvB,IAAI,EAAEyB,KAAK,CAAC;IAC/B,IAAI,CAAC8D,OAAO,CAACmD,MAAM,EAAE,IAAI,CAACrB,IAAI,CAAC,OAAO,EAAEiB,GAAG,EAAER,EAAE,EAAEnI,GAAG,EAAE8B,KAAK,CAAC;EAC9D;EACAkH,YAAYA,CAACL,GAAG,EAAER,EAAE,EAAEc,SAAS,EAAE;IAC/B,IAAIrD,OAAO,GAAGpB,SAAS,CAAC9D,MAAM,GAAG,CAAC,IAAI8D,SAAS,CAAC,CAAC,CAAC,KAAKtD,SAAS,GAAGsD,SAAS,CAAC,CAAC,CAAC,GAAG;MAChFuE,MAAM,EAAE;IACV,CAAC;IACD,KAAK,MAAMlJ,CAAC,IAAIoJ,SAAS,EAAE;MACzB,IAAIpK,QAAQ,CAACoK,SAAS,CAACpJ,CAAC,CAAC,CAAC,IAAIqG,KAAK,CAAC0C,OAAO,CAACK,SAAS,CAACpJ,CAAC,CAAC,CAAC,EAAE,IAAI,CAACiJ,WAAW,CAACH,GAAG,EAAER,EAAE,EAAEtI,CAAC,EAAEoJ,SAAS,CAACpJ,CAAC,CAAC,EAAE;QACpGkJ,MAAM,EAAE;MACV,CAAC,CAAC;IACJ;IACA,IAAI,CAACnD,OAAO,CAACmD,MAAM,EAAE,IAAI,CAACrB,IAAI,CAAC,OAAO,EAAEiB,GAAG,EAAER,EAAE,EAAEc,SAAS,CAAC;EAC7D;EACAC,iBAAiBA,CAACP,GAAG,EAAER,EAAE,EAAEc,SAAS,EAAEE,IAAI,EAAEjH,SAAS,EAAE;IACrD,IAAI0D,OAAO,GAAGpB,SAAS,CAAC9D,MAAM,GAAG,CAAC,IAAI8D,SAAS,CAAC,CAAC,CAAC,KAAKtD,SAAS,GAAGsD,SAAS,CAAC,CAAC,CAAC,GAAG;MAChFuE,MAAM,EAAE,KAAK;MACbK,QAAQ,EAAE;IACZ,CAAC;IACD,IAAI/I,IAAI,GAAG,CAACsI,GAAG,EAAER,EAAE,CAAC;IACpB,IAAIQ,GAAG,CAAC1I,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;MACzBI,IAAI,GAAGsI,GAAG,CAACnI,KAAK,CAAC,GAAG,CAAC;MACrB2I,IAAI,GAAGF,SAAS;MAChBA,SAAS,GAAGd,EAAE;MACdA,EAAE,GAAG9H,IAAI,CAAC,CAAC,CAAC;IACd;IACA,IAAI,CAACiI,aAAa,CAACH,EAAE,CAAC;IACtB,IAAIkB,IAAI,GAAG3H,OAAO,CAAC,IAAI,CAACE,IAAI,EAAEvB,IAAI,CAAC,IAAI,CAAC,CAAC;IACzC,IAAI,CAACuF,OAAO,CAACwD,QAAQ,EAAEH,SAAS,GAAGK,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACP,SAAS,CAAC,CAAC;IACxE,IAAIE,IAAI,EAAE;MACRpH,UAAU,CAACsH,IAAI,EAAEJ,SAAS,EAAE/G,SAAS,CAAC;IACxC,CAAC,MAAM;MACLmH,IAAI,GAAG;QACL,GAAGA,IAAI;QACP,GAAGJ;MACL,CAAC;IACH;IACAjI,OAAO,CAAC,IAAI,CAACY,IAAI,EAAEvB,IAAI,EAAEgJ,IAAI,CAAC;IAC9B,IAAI,CAACzD,OAAO,CAACmD,MAAM,EAAE,IAAI,CAACrB,IAAI,CAAC,OAAO,EAAEiB,GAAG,EAAER,EAAE,EAAEc,SAAS,CAAC;EAC7D;EACAQ,oBAAoBA,CAACd,GAAG,EAAER,EAAE,EAAE;IAC5B,IAAI,IAAI,CAACuB,iBAAiB,CAACf,GAAG,EAAER,EAAE,CAAC,EAAE;MACnC,OAAO,IAAI,CAACvG,IAAI,CAAC+G,GAAG,CAAC,CAACR,EAAE,CAAC;IAC3B;IACA,IAAI,CAACI,gBAAgB,CAACJ,EAAE,CAAC;IACzB,IAAI,CAACT,IAAI,CAAC,SAAS,EAAEiB,GAAG,EAAER,EAAE,CAAC;EAC/B;EACAuB,iBAAiBA,CAACf,GAAG,EAAER,EAAE,EAAE;IACzB,OAAO,IAAI,CAACO,WAAW,CAACC,GAAG,EAAER,EAAE,CAAC,KAAKjH,SAAS;EAChD;EACAyI,iBAAiBA,CAAChB,GAAG,EAAER,EAAE,EAAE;IACzB,IAAI,CAACA,EAAE,EAAEA,EAAE,GAAG,IAAI,CAACvC,OAAO,CAACwC,SAAS;IACpC,IAAI,IAAI,CAACxC,OAAO,CAACgE,gBAAgB,KAAK,IAAI,EAAE,OAAO;MACjD,GAAG,CAAC,CAAC;MACL,GAAG,IAAI,CAAClB,WAAW,CAACC,GAAG,EAAER,EAAE;IAC7B,CAAC;IACD,OAAO,IAAI,CAACO,WAAW,CAACC,GAAG,EAAER,EAAE,CAAC;EAClC;EACA0B,iBAAiBA,CAAClB,GAAG,EAAE;IACrB,OAAO,IAAI,CAAC/G,IAAI,CAAC+G,GAAG,CAAC;EACvB;EACAmB,2BAA2BA,CAACnB,GAAG,EAAE;IAC/B,MAAM/G,IAAI,GAAG,IAAI,CAACiI,iBAAiB,CAAClB,GAAG,CAAC;IACxC,MAAMoB,CAAC,GAAGnI,IAAI,IAAIjB,MAAM,CAACqJ,IAAI,CAACpI,IAAI,CAAC,IAAI,EAAE;IACzC,OAAO,CAAC,CAACmI,CAAC,CAACE,IAAI,CAACC,CAAC,IAAItI,IAAI,CAACsI,CAAC,CAAC,IAAIvJ,MAAM,CAACqJ,IAAI,CAACpI,IAAI,CAACsI,CAAC,CAAC,CAAC,CAACxJ,MAAM,GAAG,CAAC,CAAC;EAClE;EACAyJ,MAAMA,CAAA,EAAG;IACP,OAAO,IAAI,CAACvI,IAAI;EAClB;AACF;AAEA,IAAIwI,aAAa,GAAG;EAClBC,UAAU,EAAE,CAAC,CAAC;EACdC,gBAAgBA,CAACC,MAAM,EAAE;IACvB,IAAI,CAACF,UAAU,CAACE,MAAM,CAACC,IAAI,CAAC,GAAGD,MAAM;EACvC,CAAC;EACDE,MAAMA,CAACJ,UAAU,EAAEvI,KAAK,EAAE9B,GAAG,EAAE4F,OAAO,EAAE8E,UAAU,EAAE;IAClDL,UAAU,CAACzK,OAAO,CAAC+K,SAAS,IAAI;MAC9B,IAAI,IAAI,CAACN,UAAU,CAACM,SAAS,CAAC,EAAE7I,KAAK,GAAG,IAAI,CAACuI,UAAU,CAACM,SAAS,CAAC,CAACC,OAAO,CAAC9I,KAAK,EAAE9B,GAAG,EAAE4F,OAAO,EAAE8E,UAAU,CAAC;IAC7G,CAAC,CAAC;IACF,OAAO5I,KAAK;EACd;AACF,CAAC;AAED,MAAM+I,gBAAgB,GAAG,CAAC,CAAC;AAC3B,MAAMC,UAAU,SAAS5D,YAAY,CAAC;EACpCxE,WAAWA,CAACqI,QAAQ,EAAE;IACpB,IAAInF,OAAO,GAAGpB,SAAS,CAAC9D,MAAM,GAAG,CAAC,IAAI8D,SAAS,CAAC,CAAC,CAAC,KAAKtD,SAAS,GAAGsD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACpF,KAAK,CAAC,CAAC;IACPhF,IAAI,CAAC,CAAC,eAAe,EAAE,eAAe,EAAE,gBAAgB,EAAE,cAAc,EAAE,kBAAkB,EAAE,YAAY,EAAE,OAAO,CAAC,EAAEuL,QAAQ,EAAE,IAAI,CAAC;IACrI,IAAI,CAACnF,OAAO,GAAGA,OAAO;IACtB,IAAI,IAAI,CAACA,OAAO,CAAChC,YAAY,KAAK1C,SAAS,EAAE;MAC3C,IAAI,CAAC0E,OAAO,CAAChC,YAAY,GAAG,GAAG;IACjC;IACA,IAAI,CAACmC,MAAM,GAAGkB,UAAU,CAACH,MAAM,CAAC,YAAY,CAAC;EAC/C;EACAkE,cAAcA,CAACrC,GAAG,EAAE;IAClB,IAAIA,GAAG,EAAE,IAAI,CAACsC,QAAQ,GAAGtC,GAAG;EAC9B;EACAuC,MAAMA,CAAClL,GAAG,EAAE;IACV,IAAI4F,OAAO,GAAGpB,SAAS,CAAC9D,MAAM,GAAG,CAAC,IAAI8D,SAAS,CAAC,CAAC,CAAC,KAAKtD,SAAS,GAAGsD,SAAS,CAAC,CAAC,CAAC,GAAG;MAChF2G,aAAa,EAAE,CAAC;IAClB,CAAC;IACD,IAAInL,GAAG,KAAKkB,SAAS,IAAIlB,GAAG,KAAK,IAAI,EAAE;MACrC,OAAO,KAAK;IACd;IACA,MAAMoL,QAAQ,GAAG,IAAI,CAAChM,OAAO,CAACY,GAAG,EAAE4F,OAAO,CAAC;IAC3C,OAAOwF,QAAQ,IAAIA,QAAQ,CAACpM,GAAG,KAAKkC,SAAS;EAC/C;EACAmK,cAAcA,CAACrL,GAAG,EAAE4F,OAAO,EAAE;IAC3B,IAAIjC,WAAW,GAAGiC,OAAO,CAACjC,WAAW,KAAKzC,SAAS,GAAG0E,OAAO,CAACjC,WAAW,GAAG,IAAI,CAACiC,OAAO,CAACjC,WAAW;IACpG,IAAIA,WAAW,KAAKzC,SAAS,EAAEyC,WAAW,GAAG,GAAG;IAChD,MAAMC,YAAY,GAAGgC,OAAO,CAAChC,YAAY,KAAK1C,SAAS,GAAG0E,OAAO,CAAChC,YAAY,GAAG,IAAI,CAACgC,OAAO,CAAChC,YAAY;IAC1G,IAAI0H,UAAU,GAAG1F,OAAO,CAACuC,EAAE,IAAI,IAAI,CAACvC,OAAO,CAACwC,SAAS,IAAI,EAAE;IAC3D,MAAMmD,oBAAoB,GAAG5H,WAAW,IAAI3D,GAAG,CAACC,OAAO,CAAC0D,WAAW,CAAC,GAAG,CAAC,CAAC;IACzE,MAAM6H,oBAAoB,GAAG,CAAC,IAAI,CAAC5F,OAAO,CAAC6F,uBAAuB,IAAI,CAAC7F,OAAO,CAAChC,YAAY,IAAI,CAAC,IAAI,CAACgC,OAAO,CAAC8F,sBAAsB,IAAI,CAAC9F,OAAO,CAACjC,WAAW,IAAI,CAACD,mBAAmB,CAAC1D,GAAG,EAAE2D,WAAW,EAAEC,YAAY,CAAC;IACnN,IAAI2H,oBAAoB,IAAI,CAACC,oBAAoB,EAAE;MACjD,MAAM3L,CAAC,GAAGG,GAAG,CAAC2L,KAAK,CAAC,IAAI,CAACC,YAAY,CAACC,aAAa,CAAC;MACpD,IAAIhM,CAAC,IAAIA,CAAC,CAACa,MAAM,GAAG,CAAC,EAAE;QACrB,OAAO;UACLV,GAAG;UACHsL,UAAU,EAAEzM,QAAQ,CAACyM,UAAU,CAAC,GAAG,CAACA,UAAU,CAAC,GAAGA;QACpD,CAAC;MACH;MACA,MAAMQ,KAAK,GAAG9L,GAAG,CAACQ,KAAK,CAACmD,WAAW,CAAC;MACpC,IAAIA,WAAW,KAAKC,YAAY,IAAID,WAAW,KAAKC,YAAY,IAAI,IAAI,CAACgC,OAAO,CAACuC,EAAE,CAAClI,OAAO,CAAC6L,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAER,UAAU,GAAGQ,KAAK,CAACxI,KAAK,CAAC,CAAC;MACtItD,GAAG,GAAG8L,KAAK,CAAC5H,IAAI,CAACN,YAAY,CAAC;IAChC;IACA,OAAO;MACL5D,GAAG;MACHsL,UAAU,EAAEzM,QAAQ,CAACyM,UAAU,CAAC,GAAG,CAACA,UAAU,CAAC,GAAGA;IACpD,CAAC;EACH;EACAS,SAASA,CAAC/B,IAAI,EAAEpE,OAAO,EAAEoG,OAAO,EAAE;IAChC,IAAI,OAAOpG,OAAO,KAAK,QAAQ,IAAI,IAAI,CAACA,OAAO,CAACqG,gCAAgC,EAAE;MAChFrG,OAAO,GAAG,IAAI,CAACA,OAAO,CAACqG,gCAAgC,CAACzH,SAAS,CAAC;IACpE;IACA,IAAI,OAAOoB,OAAO,KAAK,QAAQ,EAAEA,OAAO,GAAG;MACzC,GAAGA;IACL,CAAC;IACD,IAAI,CAACA,OAAO,EAAEA,OAAO,GAAG,CAAC,CAAC;IAC1B,IAAIoE,IAAI,KAAK9I,SAAS,IAAI8I,IAAI,KAAK,IAAI,EAAE,OAAO,EAAE;IAClD,IAAI,CAAC9D,KAAK,CAAC0C,OAAO,CAACoB,IAAI,CAAC,EAAEA,IAAI,GAAG,CAAC5H,MAAM,CAAC4H,IAAI,CAAC,CAAC;IAC/C,MAAMkC,aAAa,GAAGtG,OAAO,CAACsG,aAAa,KAAKhL,SAAS,GAAG0E,OAAO,CAACsG,aAAa,GAAG,IAAI,CAACtG,OAAO,CAACsG,aAAa;IAC9G,MAAMtI,YAAY,GAAGgC,OAAO,CAAChC,YAAY,KAAK1C,SAAS,GAAG0E,OAAO,CAAChC,YAAY,GAAG,IAAI,CAACgC,OAAO,CAAChC,YAAY;IAC1G,MAAM;MACJ5D,GAAG;MACHsL;IACF,CAAC,GAAG,IAAI,CAACD,cAAc,CAACrB,IAAI,CAACA,IAAI,CAACtJ,MAAM,GAAG,CAAC,CAAC,EAAEkF,OAAO,CAAC;IACvD,MAAMuG,SAAS,GAAGb,UAAU,CAACA,UAAU,CAAC5K,MAAM,GAAG,CAAC,CAAC;IACnD,MAAMiI,GAAG,GAAG/C,OAAO,CAAC+C,GAAG,IAAI,IAAI,CAACsC,QAAQ;IACxC,MAAMmB,uBAAuB,GAAGxG,OAAO,CAACwG,uBAAuB,IAAI,IAAI,CAACxG,OAAO,CAACwG,uBAAuB;IACvG,IAAIzD,GAAG,IAAIA,GAAG,CAAC0D,WAAW,CAAC,CAAC,KAAK,QAAQ,EAAE;MACzC,IAAID,uBAAuB,EAAE;QAC3B,MAAMzI,WAAW,GAAGiC,OAAO,CAACjC,WAAW,IAAI,IAAI,CAACiC,OAAO,CAACjC,WAAW;QACnE,IAAIuI,aAAa,EAAE;UACjB,OAAO;YACLlN,GAAG,EAAE,GAAGmN,SAAS,GAAGxI,WAAW,GAAG3D,GAAG,EAAE;YACvCsM,OAAO,EAAEtM,GAAG;YACZuM,YAAY,EAAEvM,GAAG;YACjBwM,OAAO,EAAE7D,GAAG;YACZ8D,MAAM,EAAEN,SAAS;YACjBO,UAAU,EAAE,IAAI,CAACC,oBAAoB,CAAC/G,OAAO;UAC/C,CAAC;QACH;QACA,OAAO,GAAGuG,SAAS,GAAGxI,WAAW,GAAG3D,GAAG,EAAE;MAC3C;MACA,IAAIkM,aAAa,EAAE;QACjB,OAAO;UACLlN,GAAG,EAAEgB,GAAG;UACRsM,OAAO,EAAEtM,GAAG;UACZuM,YAAY,EAAEvM,GAAG;UACjBwM,OAAO,EAAE7D,GAAG;UACZ8D,MAAM,EAAEN,SAAS;UACjBO,UAAU,EAAE,IAAI,CAACC,oBAAoB,CAAC/G,OAAO;QAC/C,CAAC;MACH;MACA,OAAO5F,GAAG;IACZ;IACA,MAAMoL,QAAQ,GAAG,IAAI,CAAChM,OAAO,CAAC4K,IAAI,EAAEpE,OAAO,CAAC;IAC5C,IAAI5G,GAAG,GAAGoM,QAAQ,IAAIA,QAAQ,CAACpM,GAAG;IAClC,MAAM4N,UAAU,GAAGxB,QAAQ,IAAIA,QAAQ,CAACkB,OAAO,IAAItM,GAAG;IACtD,MAAM6M,eAAe,GAAGzB,QAAQ,IAAIA,QAAQ,CAACmB,YAAY,IAAIvM,GAAG;IAChE,MAAM8M,OAAO,GAAGnM,MAAM,CAACC,SAAS,CAACmM,QAAQ,CAACtH,KAAK,CAACzG,GAAG,CAAC;IACpD,MAAMgO,QAAQ,GAAG,CAAC,iBAAiB,EAAE,mBAAmB,EAAE,iBAAiB,CAAC;IAC5E,MAAMC,UAAU,GAAGrH,OAAO,CAACqH,UAAU,KAAK/L,SAAS,GAAG0E,OAAO,CAACqH,UAAU,GAAG,IAAI,CAACrH,OAAO,CAACqH,UAAU;IAClG,MAAMC,0BAA0B,GAAG,CAAC,IAAI,CAACC,UAAU,IAAI,IAAI,CAACA,UAAU,CAACC,cAAc;IACrF,MAAMA,cAAc,GAAG,CAACvO,QAAQ,CAACG,GAAG,CAAC,IAAI,OAAOA,GAAG,KAAK,SAAS,IAAI,OAAOA,GAAG,KAAK,QAAQ;IAC5F,IAAIkO,0BAA0B,IAAIlO,GAAG,IAAIoO,cAAc,IAAIJ,QAAQ,CAAC/M,OAAO,CAAC6M,OAAO,CAAC,GAAG,CAAC,IAAI,EAAEjO,QAAQ,CAACoO,UAAU,CAAC,IAAI/G,KAAK,CAAC0C,OAAO,CAAC5J,GAAG,CAAC,CAAC,EAAE;MACzI,IAAI,CAAC4G,OAAO,CAACyH,aAAa,IAAI,CAAC,IAAI,CAACzH,OAAO,CAACyH,aAAa,EAAE;QACzD,IAAI,CAAC,IAAI,CAACzH,OAAO,CAAC0H,qBAAqB,EAAE;UACvC,IAAI,CAACvH,MAAM,CAACT,IAAI,CAAC,iEAAiE,CAAC;QACrF;QACA,MAAMtB,CAAC,GAAG,IAAI,CAAC4B,OAAO,CAAC0H,qBAAqB,GAAG,IAAI,CAAC1H,OAAO,CAAC0H,qBAAqB,CAACV,UAAU,EAAE5N,GAAG,EAAE;UACjG,GAAG4G,OAAO;UACVuC,EAAE,EAAEmD;QACN,CAAC,CAAC,GAAG,QAAQtL,GAAG,KAAK,IAAI,CAACiL,QAAQ,0CAA0C;QAC5E,IAAIiB,aAAa,EAAE;UACjBd,QAAQ,CAACpM,GAAG,GAAGgF,CAAC;UAChBoH,QAAQ,CAACsB,UAAU,GAAG,IAAI,CAACC,oBAAoB,CAAC/G,OAAO,CAAC;UACxD,OAAOwF,QAAQ;QACjB;QACA,OAAOpH,CAAC;MACV;MACA,IAAIJ,YAAY,EAAE;QAChB,MAAM2J,cAAc,GAAGrH,KAAK,CAAC0C,OAAO,CAAC5J,GAAG,CAAC;QACzC,MAAMQ,IAAI,GAAG+N,cAAc,GAAG,EAAE,GAAG,CAAC,CAAC;QACrC,MAAMC,WAAW,GAAGD,cAAc,GAAGV,eAAe,GAAGD,UAAU;QACjE,KAAK,MAAM/M,CAAC,IAAIb,GAAG,EAAE;UACnB,IAAI2B,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAAC9B,GAAG,EAAEa,CAAC,CAAC,EAAE;YAChD,MAAM4N,OAAO,GAAG,GAAGD,WAAW,GAAG5J,YAAY,GAAG/D,CAAC,EAAE;YACnDL,IAAI,CAACK,CAAC,CAAC,GAAG,IAAI,CAACkM,SAAS,CAAC0B,OAAO,EAAE;cAChC,GAAG7H,OAAO;cACV,GAAG;gBACDqH,UAAU,EAAE,KAAK;gBACjB9E,EAAE,EAAEmD;cACN;YACF,CAAC,CAAC;YACF,IAAI9L,IAAI,CAACK,CAAC,CAAC,KAAK4N,OAAO,EAAEjO,IAAI,CAACK,CAAC,CAAC,GAAGb,GAAG,CAACa,CAAC,CAAC;UAC3C;QACF;QACAb,GAAG,GAAGQ,IAAI;MACZ;IACF,CAAC,MAAM,IAAI0N,0BAA0B,IAAIrO,QAAQ,CAACoO,UAAU,CAAC,IAAI/G,KAAK,CAAC0C,OAAO,CAAC5J,GAAG,CAAC,EAAE;MACnFA,GAAG,GAAGA,GAAG,CAACkF,IAAI,CAAC+I,UAAU,CAAC;MAC1B,IAAIjO,GAAG,EAAEA,GAAG,GAAG,IAAI,CAAC0O,iBAAiB,CAAC1O,GAAG,EAAEgL,IAAI,EAAEpE,OAAO,EAAEoG,OAAO,CAAC;IACpE,CAAC,MAAM;MACL,IAAI2B,WAAW,GAAG,KAAK;MACvB,IAAIrB,OAAO,GAAG,KAAK;MACnB,MAAMsB,mBAAmB,GAAGhI,OAAO,CAACiI,KAAK,KAAK3M,SAAS,IAAI,CAACrC,QAAQ,CAAC+G,OAAO,CAACiI,KAAK,CAAC;MACnF,MAAMC,eAAe,GAAGhD,UAAU,CAACgD,eAAe,CAAClI,OAAO,CAAC;MAC3D,MAAMmI,kBAAkB,GAAGH,mBAAmB,GAAG,IAAI,CAACI,cAAc,CAACC,SAAS,CAACtF,GAAG,EAAE/C,OAAO,CAACiI,KAAK,EAAEjI,OAAO,CAAC,GAAG,EAAE;MAChH,MAAMsI,iCAAiC,GAAGtI,OAAO,CAACuI,OAAO,IAAIP,mBAAmB,GAAG,IAAI,CAACI,cAAc,CAACC,SAAS,CAACtF,GAAG,EAAE/C,OAAO,CAACiI,KAAK,EAAE;QACnIM,OAAO,EAAE;MACX,CAAC,CAAC,GAAG,EAAE;MACP,MAAMC,qBAAqB,GAAGR,mBAAmB,IAAI,CAAChI,OAAO,CAACuI,OAAO,IAAIvI,OAAO,CAACiI,KAAK,KAAK,CAAC,IAAI,IAAI,CAACG,cAAc,CAACK,gBAAgB,CAAC,CAAC;MACtI,MAAMC,YAAY,GAAGF,qBAAqB,IAAIxI,OAAO,CAAC,eAAe,IAAI,CAACA,OAAO,CAAC2I,eAAe,MAAM,CAAC,IAAI3I,OAAO,CAAC,eAAemI,kBAAkB,EAAE,CAAC,IAAInI,OAAO,CAAC,eAAesI,iCAAiC,EAAE,CAAC,IAAItI,OAAO,CAAC0I,YAAY;MAC/O,IAAI,CAAC,IAAI,CAACE,aAAa,CAACxP,GAAG,CAAC,IAAI8O,eAAe,EAAE;QAC/CH,WAAW,GAAG,IAAI;QAClB3O,GAAG,GAAGsP,YAAY;MACpB;MACA,IAAI,CAAC,IAAI,CAACE,aAAa,CAACxP,GAAG,CAAC,EAAE;QAC5BsN,OAAO,GAAG,IAAI;QACdtN,GAAG,GAAGgB,GAAG;MACX;MACA,MAAMyO,8BAA8B,GAAG7I,OAAO,CAAC6I,8BAA8B,IAAI,IAAI,CAAC7I,OAAO,CAAC6I,8BAA8B;MAC5H,MAAMC,aAAa,GAAGD,8BAA8B,IAAInC,OAAO,GAAGpL,SAAS,GAAGlC,GAAG;MACjF,MAAM2P,aAAa,GAAGb,eAAe,IAAIQ,YAAY,KAAKtP,GAAG,IAAI,IAAI,CAAC4G,OAAO,CAAC+I,aAAa;MAC3F,IAAIrC,OAAO,IAAIqB,WAAW,IAAIgB,aAAa,EAAE;QAC3C,IAAI,CAAC5I,MAAM,CAACZ,GAAG,CAACwJ,aAAa,GAAG,WAAW,GAAG,YAAY,EAAEhG,GAAG,EAAEwD,SAAS,EAAEnM,GAAG,EAAE2O,aAAa,GAAGL,YAAY,GAAGtP,GAAG,CAAC;QACpH,IAAI4E,YAAY,EAAE;UAChB,MAAMgL,EAAE,GAAG,IAAI,CAACxP,OAAO,CAACY,GAAG,EAAE;YAC3B,GAAG4F,OAAO;YACVhC,YAAY,EAAE;UAChB,CAAC,CAAC;UACF,IAAIgL,EAAE,IAAIA,EAAE,CAAC5P,GAAG,EAAE,IAAI,CAAC+G,MAAM,CAACT,IAAI,CAAC,iLAAiL,CAAC;QACvN;QACA,IAAIuJ,IAAI,GAAG,EAAE;QACb,MAAMC,YAAY,GAAG,IAAI,CAACC,aAAa,CAACC,gBAAgB,CAAC,IAAI,CAACpJ,OAAO,CAACqJ,WAAW,EAAErJ,OAAO,CAAC+C,GAAG,IAAI,IAAI,CAACsC,QAAQ,CAAC;QAChH,IAAI,IAAI,CAACrF,OAAO,CAACsJ,aAAa,KAAK,UAAU,IAAIJ,YAAY,IAAIA,YAAY,CAAC,CAAC,CAAC,EAAE;UAChF,KAAK,IAAInK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmK,YAAY,CAACpO,MAAM,EAAEiE,CAAC,EAAE,EAAE;YAC5CkK,IAAI,CAACpN,IAAI,CAACqN,YAAY,CAACnK,CAAC,CAAC,CAAC;UAC5B;QACF,CAAC,MAAM,IAAI,IAAI,CAACiB,OAAO,CAACsJ,aAAa,KAAK,KAAK,EAAE;UAC/CL,IAAI,GAAG,IAAI,CAACE,aAAa,CAACI,kBAAkB,CAACvJ,OAAO,CAAC+C,GAAG,IAAI,IAAI,CAACsC,QAAQ,CAAC;QAC5E,CAAC,MAAM;UACL4D,IAAI,CAACpN,IAAI,CAACmE,OAAO,CAAC+C,GAAG,IAAI,IAAI,CAACsC,QAAQ,CAAC;QACzC;QACA,MAAMmE,IAAI,GAAGA,CAACC,CAAC,EAAEtO,CAAC,EAAEuO,oBAAoB,KAAK;UAC3C,MAAMC,iBAAiB,GAAGzB,eAAe,IAAIwB,oBAAoB,KAAKtQ,GAAG,GAAGsQ,oBAAoB,GAAGZ,aAAa;UAChH,IAAI,IAAI,CAAC9I,OAAO,CAAC4J,iBAAiB,EAAE;YAClC,IAAI,CAAC5J,OAAO,CAAC4J,iBAAiB,CAACH,CAAC,EAAElD,SAAS,EAAEpL,CAAC,EAAEwO,iBAAiB,EAAEZ,aAAa,EAAE/I,OAAO,CAAC;UAC5F,CAAC,MAAM,IAAI,IAAI,CAAC6J,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,CAACC,WAAW,EAAE;YACrE,IAAI,CAACD,gBAAgB,CAACC,WAAW,CAACL,CAAC,EAAElD,SAAS,EAAEpL,CAAC,EAAEwO,iBAAiB,EAAEZ,aAAa,EAAE/I,OAAO,CAAC;UAC/F;UACA,IAAI,CAAC8B,IAAI,CAAC,YAAY,EAAE2H,CAAC,EAAElD,SAAS,EAAEpL,CAAC,EAAE/B,GAAG,CAAC;QAC/C,CAAC;QACD,IAAI,IAAI,CAAC4G,OAAO,CAAC8J,WAAW,EAAE;UAC5B,IAAI,IAAI,CAAC9J,OAAO,CAAC+J,kBAAkB,IAAI/B,mBAAmB,EAAE;YAC1DiB,IAAI,CAACjP,OAAO,CAACqL,QAAQ,IAAI;cACvB,MAAM2E,QAAQ,GAAG,IAAI,CAAC5B,cAAc,CAAC6B,WAAW,CAAC5E,QAAQ,EAAErF,OAAO,CAAC;cACnE,IAAIwI,qBAAqB,IAAIxI,OAAO,CAAC,eAAe,IAAI,CAACA,OAAO,CAAC2I,eAAe,MAAM,CAAC,IAAIqB,QAAQ,CAAC3P,OAAO,CAAC,GAAG,IAAI,CAAC2F,OAAO,CAAC2I,eAAe,MAAM,CAAC,GAAG,CAAC,EAAE;gBACtJqB,QAAQ,CAACnO,IAAI,CAAC,GAAG,IAAI,CAACmE,OAAO,CAAC2I,eAAe,MAAM,CAAC;cACtD;cACAqB,QAAQ,CAAChQ,OAAO,CAACkQ,MAAM,IAAI;gBACzBV,IAAI,CAAC,CAACnE,QAAQ,CAAC,EAAEjL,GAAG,GAAG8P,MAAM,EAAElK,OAAO,CAAC,eAAekK,MAAM,EAAE,CAAC,IAAIxB,YAAY,CAAC;cAClF,CAAC,CAAC;YACJ,CAAC,CAAC;UACJ,CAAC,MAAM;YACLc,IAAI,CAACP,IAAI,EAAE7O,GAAG,EAAEsO,YAAY,CAAC;UAC/B;QACF;MACF;MACAtP,GAAG,GAAG,IAAI,CAAC0O,iBAAiB,CAAC1O,GAAG,EAAEgL,IAAI,EAAEpE,OAAO,EAAEwF,QAAQ,EAAEY,OAAO,CAAC;MACnE,IAAIM,OAAO,IAAItN,GAAG,KAAKgB,GAAG,IAAI,IAAI,CAAC4F,OAAO,CAACmK,2BAA2B,EAAE/Q,GAAG,GAAG,GAAGmN,SAAS,IAAInM,GAAG,EAAE;MACnG,IAAI,CAACsM,OAAO,IAAIqB,WAAW,KAAK,IAAI,CAAC/H,OAAO,CAACoK,sBAAsB,EAAE;QACnE,IAAI,IAAI,CAACpK,OAAO,CAACgE,gBAAgB,KAAK,IAAI,EAAE;UAC1C5K,GAAG,GAAG,IAAI,CAAC4G,OAAO,CAACoK,sBAAsB,CAAC,IAAI,CAACpK,OAAO,CAACmK,2BAA2B,GAAG,GAAG5D,SAAS,IAAInM,GAAG,EAAE,GAAGA,GAAG,EAAE2N,WAAW,GAAG3O,GAAG,GAAGkC,SAAS,CAAC;QAClJ,CAAC,MAAM;UACLlC,GAAG,GAAG,IAAI,CAAC4G,OAAO,CAACoK,sBAAsB,CAAChR,GAAG,CAAC;QAChD;MACF;IACF;IACA,IAAIkN,aAAa,EAAE;MACjBd,QAAQ,CAACpM,GAAG,GAAGA,GAAG;MAClBoM,QAAQ,CAACsB,UAAU,GAAG,IAAI,CAACC,oBAAoB,CAAC/G,OAAO,CAAC;MACxD,OAAOwF,QAAQ;IACjB;IACA,OAAOpM,GAAG;EACZ;EACA0O,iBAAiBA,CAAC1O,GAAG,EAAEgB,GAAG,EAAE4F,OAAO,EAAEwF,QAAQ,EAAEY,OAAO,EAAE;IACtD,IAAIiE,KAAK,GAAG,IAAI;IAChB,IAAI,IAAI,CAAC9C,UAAU,IAAI,IAAI,CAACA,UAAU,CAAC5D,KAAK,EAAE;MAC5CvK,GAAG,GAAG,IAAI,CAACmO,UAAU,CAAC5D,KAAK,CAACvK,GAAG,EAAE;QAC/B,GAAG,IAAI,CAAC4G,OAAO,CAACuF,aAAa,CAAC+E,gBAAgB;QAC9C,GAAGtK;MACL,CAAC,EAAEA,OAAO,CAAC+C,GAAG,IAAI,IAAI,CAACsC,QAAQ,IAAIG,QAAQ,CAACoB,OAAO,EAAEpB,QAAQ,CAACqB,MAAM,EAAErB,QAAQ,CAACkB,OAAO,EAAE;QACtFlB;MACF,CAAC,CAAC;IACJ,CAAC,MAAM,IAAI,CAACxF,OAAO,CAACuK,iBAAiB,EAAE;MACrC,IAAIvK,OAAO,CAACuF,aAAa,EAAE,IAAI,CAACS,YAAY,CAAC/F,IAAI,CAAC;QAChD,GAAGD,OAAO;QACV,GAAG;UACDuF,aAAa,EAAE;YACb,GAAG,IAAI,CAACvF,OAAO,CAACuF,aAAa;YAC7B,GAAGvF,OAAO,CAACuF;UACb;QACF;MACF,CAAC,CAAC;MACF,MAAMiF,eAAe,GAAGvR,QAAQ,CAACG,GAAG,CAAC,KAAK4G,OAAO,IAAIA,OAAO,CAACuF,aAAa,IAAIvF,OAAO,CAACuF,aAAa,CAACiF,eAAe,KAAKlP,SAAS,GAAG0E,OAAO,CAACuF,aAAa,CAACiF,eAAe,GAAG,IAAI,CAACxK,OAAO,CAACuF,aAAa,CAACiF,eAAe,CAAC;MACvN,IAAIC,OAAO;MACX,IAAID,eAAe,EAAE;QACnB,MAAME,EAAE,GAAGtR,GAAG,CAAC2M,KAAK,CAAC,IAAI,CAACC,YAAY,CAACC,aAAa,CAAC;QACrDwE,OAAO,GAAGC,EAAE,IAAIA,EAAE,CAAC5P,MAAM;MAC3B;MACA,IAAIkB,IAAI,GAAGgE,OAAO,CAAC1F,OAAO,IAAI,CAACrB,QAAQ,CAAC+G,OAAO,CAAC1F,OAAO,CAAC,GAAG0F,OAAO,CAAC1F,OAAO,GAAG0F,OAAO;MACpF,IAAI,IAAI,CAACA,OAAO,CAACuF,aAAa,CAAC+E,gBAAgB,EAAEtO,IAAI,GAAG;QACtD,GAAG,IAAI,CAACgE,OAAO,CAACuF,aAAa,CAAC+E,gBAAgB;QAC9C,GAAGtO;MACL,CAAC;MACD5C,GAAG,GAAG,IAAI,CAAC4M,YAAY,CAAC2E,WAAW,CAACvR,GAAG,EAAE4C,IAAI,EAAEgE,OAAO,CAAC+C,GAAG,IAAI,IAAI,CAACsC,QAAQ,IAAIG,QAAQ,CAACoB,OAAO,EAAE5G,OAAO,CAAC;MACzG,IAAIwK,eAAe,EAAE;QACnB,MAAMI,EAAE,GAAGxR,GAAG,CAAC2M,KAAK,CAAC,IAAI,CAACC,YAAY,CAACC,aAAa,CAAC;QACrD,MAAM4E,OAAO,GAAGD,EAAE,IAAIA,EAAE,CAAC9P,MAAM;QAC/B,IAAI2P,OAAO,GAAGI,OAAO,EAAE7K,OAAO,CAAC8K,IAAI,GAAG,KAAK;MAC7C;MACA,IAAI,CAAC9K,OAAO,CAAC+C,GAAG,IAAI,IAAI,CAAC/C,OAAO,CAACgE,gBAAgB,KAAK,IAAI,IAAIwB,QAAQ,IAAIA,QAAQ,CAACpM,GAAG,EAAE4G,OAAO,CAAC+C,GAAG,GAAG,IAAI,CAACsC,QAAQ,IAAIG,QAAQ,CAACoB,OAAO;MACvI,IAAI5G,OAAO,CAAC8K,IAAI,KAAK,KAAK,EAAE1R,GAAG,GAAG,IAAI,CAAC4M,YAAY,CAAC8E,IAAI,CAAC1R,GAAG,EAAE,YAAY;QACxE,KAAK,IAAIiH,IAAI,GAAGzB,SAAS,CAAC9D,MAAM,EAAE0E,IAAI,GAAG,IAAIc,KAAK,CAACD,IAAI,CAAC,EAAEE,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGF,IAAI,EAAEE,IAAI,EAAE,EAAE;UACvFf,IAAI,CAACe,IAAI,CAAC,GAAG3B,SAAS,CAAC2B,IAAI,CAAC;QAC9B;QACA,IAAI6F,OAAO,IAAIA,OAAO,CAAC,CAAC,CAAC,KAAK5G,IAAI,CAAC,CAAC,CAAC,IAAI,CAACQ,OAAO,CAAC+K,OAAO,EAAE;UACzDV,KAAK,CAAClK,MAAM,CAACT,IAAI,CAAC,6CAA6CF,IAAI,CAAC,CAAC,CAAC,YAAYpF,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;UAC3F,OAAO,IAAI;QACb;QACA,OAAOiQ,KAAK,CAAClE,SAAS,CAAC,GAAG3G,IAAI,EAAEpF,GAAG,CAAC;MACtC,CAAC,EAAE4F,OAAO,CAAC;MACX,IAAIA,OAAO,CAACuF,aAAa,EAAE,IAAI,CAACS,YAAY,CAACgF,KAAK,CAAC,CAAC;IACtD;IACA,MAAMC,WAAW,GAAGjL,OAAO,CAACiL,WAAW,IAAI,IAAI,CAACjL,OAAO,CAACiL,WAAW;IACnE,MAAMC,kBAAkB,GAAGjS,QAAQ,CAACgS,WAAW,CAAC,GAAG,CAACA,WAAW,CAAC,GAAGA,WAAW;IAC9E,IAAI7R,GAAG,KAAKkC,SAAS,IAAIlC,GAAG,KAAK,IAAI,IAAI8R,kBAAkB,IAAIA,kBAAkB,CAACpQ,MAAM,IAAIkF,OAAO,CAACmL,kBAAkB,KAAK,KAAK,EAAE;MAChI/R,GAAG,GAAGoL,aAAa,CAACK,MAAM,CAACqG,kBAAkB,EAAE9R,GAAG,EAAEgB,GAAG,EAAE,IAAI,CAAC4F,OAAO,IAAI,IAAI,CAACA,OAAO,CAACoL,uBAAuB,GAAG;QAC9GC,YAAY,EAAE;UACZ,GAAG7F,QAAQ;UACXsB,UAAU,EAAE,IAAI,CAACC,oBAAoB,CAAC/G,OAAO;QAC/C,CAAC;QACD,GAAGA;MACL,CAAC,GAAGA,OAAO,EAAE,IAAI,CAAC;IACpB;IACA,OAAO5G,GAAG;EACZ;EACAI,OAAOA,CAAC4K,IAAI,EAAE;IACZ,IAAIpE,OAAO,GAAGpB,SAAS,CAAC9D,MAAM,GAAG,CAAC,IAAI8D,SAAS,CAAC,CAAC,CAAC,KAAKtD,SAAS,GAAGsD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACpF,IAAI0M,KAAK;IACT,IAAI5E,OAAO;IACX,IAAIC,YAAY;IAChB,IAAIC,OAAO;IACX,IAAIC,MAAM;IACV,IAAI5N,QAAQ,CAACmL,IAAI,CAAC,EAAEA,IAAI,GAAG,CAACA,IAAI,CAAC;IACjCA,IAAI,CAACpK,OAAO,CAACmB,CAAC,IAAI;MAChB,IAAI,IAAI,CAACyN,aAAa,CAAC0C,KAAK,CAAC,EAAE;MAC/B,MAAMC,SAAS,GAAG,IAAI,CAAC9F,cAAc,CAACtK,CAAC,EAAE6E,OAAO,CAAC;MACjD,MAAM5F,GAAG,GAAGmR,SAAS,CAACnR,GAAG;MACzBsM,OAAO,GAAGtM,GAAG;MACb,IAAIsL,UAAU,GAAG6F,SAAS,CAAC7F,UAAU;MACrC,IAAI,IAAI,CAAC1F,OAAO,CAACwL,UAAU,EAAE9F,UAAU,GAAGA,UAAU,CAAC9J,MAAM,CAAC,IAAI,CAACoE,OAAO,CAACwL,UAAU,CAAC;MACpF,MAAMxD,mBAAmB,GAAGhI,OAAO,CAACiI,KAAK,KAAK3M,SAAS,IAAI,CAACrC,QAAQ,CAAC+G,OAAO,CAACiI,KAAK,CAAC;MACnF,MAAMO,qBAAqB,GAAGR,mBAAmB,IAAI,CAAChI,OAAO,CAACuI,OAAO,IAAIvI,OAAO,CAACiI,KAAK,KAAK,CAAC,IAAI,IAAI,CAACG,cAAc,CAACK,gBAAgB,CAAC,CAAC;MACtI,MAAMgD,oBAAoB,GAAGzL,OAAO,CAAC+K,OAAO,KAAKzP,SAAS,KAAKrC,QAAQ,CAAC+G,OAAO,CAAC+K,OAAO,CAAC,IAAI,OAAO/K,OAAO,CAAC+K,OAAO,KAAK,QAAQ,CAAC,IAAI/K,OAAO,CAAC+K,OAAO,KAAK,EAAE;MAC1J,MAAMW,KAAK,GAAG1L,OAAO,CAACiJ,IAAI,GAAGjJ,OAAO,CAACiJ,IAAI,GAAG,IAAI,CAACE,aAAa,CAACI,kBAAkB,CAACvJ,OAAO,CAAC+C,GAAG,IAAI,IAAI,CAACsC,QAAQ,EAAErF,OAAO,CAACqJ,WAAW,CAAC;MACpI3D,UAAU,CAAC1L,OAAO,CAACuI,EAAE,IAAI;QACvB,IAAI,IAAI,CAACqG,aAAa,CAAC0C,KAAK,CAAC,EAAE;QAC/BzE,MAAM,GAAGtE,EAAE;QACX,IAAI,CAAC0C,gBAAgB,CAAC,GAAGyG,KAAK,CAAC,CAAC,CAAC,IAAInJ,EAAE,EAAE,CAAC,IAAI,IAAI,CAACoJ,KAAK,IAAI,IAAI,CAACA,KAAK,CAACC,kBAAkB,IAAI,CAAC,IAAI,CAACD,KAAK,CAACC,kBAAkB,CAAC/E,MAAM,CAAC,EAAE;UACnI5B,gBAAgB,CAAC,GAAGyG,KAAK,CAAC,CAAC,CAAC,IAAInJ,EAAE,EAAE,CAAC,GAAG,IAAI;UAC5C,IAAI,CAACpC,MAAM,CAACT,IAAI,CAAC,QAAQgH,OAAO,oBAAoBgF,KAAK,CAACpN,IAAI,CAAC,IAAI,CAAC,sCAAsCuI,MAAM,sBAAsB,EAAE,0NAA0N,CAAC;QACrW;QACA6E,KAAK,CAAC1R,OAAO,CAACoF,IAAI,IAAI;UACpB,IAAI,IAAI,CAACwJ,aAAa,CAAC0C,KAAK,CAAC,EAAE;UAC/B1E,OAAO,GAAGxH,IAAI;UACd,MAAMyM,SAAS,GAAG,CAACzR,GAAG,CAAC;UACvB,IAAI,IAAI,CAACmN,UAAU,IAAI,IAAI,CAACA,UAAU,CAACuE,aAAa,EAAE;YACpD,IAAI,CAACvE,UAAU,CAACuE,aAAa,CAACD,SAAS,EAAEzR,GAAG,EAAEgF,IAAI,EAAEmD,EAAE,EAAEvC,OAAO,CAAC;UAClE,CAAC,MAAM;YACL,IAAI+L,YAAY;YAChB,IAAI/D,mBAAmB,EAAE+D,YAAY,GAAG,IAAI,CAAC3D,cAAc,CAACC,SAAS,CAACjJ,IAAI,EAAEY,OAAO,CAACiI,KAAK,EAAEjI,OAAO,CAAC;YACnG,MAAMgM,UAAU,GAAG,GAAG,IAAI,CAAChM,OAAO,CAAC2I,eAAe,MAAM;YACxD,MAAMsD,aAAa,GAAG,GAAG,IAAI,CAACjM,OAAO,CAAC2I,eAAe,UAAU,IAAI,CAAC3I,OAAO,CAAC2I,eAAe,EAAE;YAC7F,IAAIX,mBAAmB,EAAE;cACvB6D,SAAS,CAAChQ,IAAI,CAACzB,GAAG,GAAG2R,YAAY,CAAC;cAClC,IAAI/L,OAAO,CAACuI,OAAO,IAAIwD,YAAY,CAAC1R,OAAO,CAAC4R,aAAa,CAAC,KAAK,CAAC,EAAE;gBAChEJ,SAAS,CAAChQ,IAAI,CAACzB,GAAG,GAAG2R,YAAY,CAACzR,OAAO,CAAC2R,aAAa,EAAE,IAAI,CAACjM,OAAO,CAAC2I,eAAe,CAAC,CAAC;cACzF;cACA,IAAIH,qBAAqB,EAAE;gBACzBqD,SAAS,CAAChQ,IAAI,CAACzB,GAAG,GAAG4R,UAAU,CAAC;cAClC;YACF;YACA,IAAIP,oBAAoB,EAAE;cACxB,MAAMS,UAAU,GAAG,GAAG9R,GAAG,GAAG,IAAI,CAAC4F,OAAO,CAACmM,gBAAgB,GAAGnM,OAAO,CAAC+K,OAAO,EAAE;cAC7Ec,SAAS,CAAChQ,IAAI,CAACqQ,UAAU,CAAC;cAC1B,IAAIlE,mBAAmB,EAAE;gBACvB6D,SAAS,CAAChQ,IAAI,CAACqQ,UAAU,GAAGH,YAAY,CAAC;gBACzC,IAAI/L,OAAO,CAACuI,OAAO,IAAIwD,YAAY,CAAC1R,OAAO,CAAC4R,aAAa,CAAC,KAAK,CAAC,EAAE;kBAChEJ,SAAS,CAAChQ,IAAI,CAACqQ,UAAU,GAAGH,YAAY,CAACzR,OAAO,CAAC2R,aAAa,EAAE,IAAI,CAACjM,OAAO,CAAC2I,eAAe,CAAC,CAAC;gBAChG;gBACA,IAAIH,qBAAqB,EAAE;kBACzBqD,SAAS,CAAChQ,IAAI,CAACqQ,UAAU,GAAGF,UAAU,CAAC;gBACzC;cACF;YACF;UACF;UACA,IAAII,WAAW;UACf,OAAOA,WAAW,GAAGP,SAAS,CAACQ,GAAG,CAAC,CAAC,EAAE;YACpC,IAAI,CAAC,IAAI,CAACzD,aAAa,CAAC0C,KAAK,CAAC,EAAE;cAC9B3E,YAAY,GAAGyF,WAAW;cAC1Bd,KAAK,GAAG,IAAI,CAACxI,WAAW,CAAC1D,IAAI,EAAEmD,EAAE,EAAE6J,WAAW,EAAEpM,OAAO,CAAC;YAC1D;UACF;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,OAAO;MACL5G,GAAG,EAAEkS,KAAK;MACV5E,OAAO;MACPC,YAAY;MACZC,OAAO;MACPC;IACF,CAAC;EACH;EACA+B,aAAaA,CAACxP,GAAG,EAAE;IACjB,OAAOA,GAAG,KAAKkC,SAAS,IAAI,EAAE,CAAC,IAAI,CAAC0E,OAAO,CAACsM,UAAU,IAAIlT,GAAG,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC4G,OAAO,CAACuM,iBAAiB,IAAInT,GAAG,KAAK,EAAE,CAAC;EAC7H;EACA0J,WAAWA,CAAC1D,IAAI,EAAEmD,EAAE,EAAEnI,GAAG,EAAE;IACzB,IAAI4F,OAAO,GAAGpB,SAAS,CAAC9D,MAAM,GAAG,CAAC,IAAI8D,SAAS,CAAC,CAAC,CAAC,KAAKtD,SAAS,GAAGsD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACpF,IAAI,IAAI,CAAC2I,UAAU,IAAI,IAAI,CAACA,UAAU,CAACzE,WAAW,EAAE,OAAO,IAAI,CAACyE,UAAU,CAACzE,WAAW,CAAC1D,IAAI,EAAEmD,EAAE,EAAEnI,GAAG,EAAE4F,OAAO,CAAC;IAC9G,OAAO,IAAI,CAACwM,aAAa,CAAC1J,WAAW,CAAC1D,IAAI,EAAEmD,EAAE,EAAEnI,GAAG,EAAE4F,OAAO,CAAC;EAC/D;EACA+G,oBAAoBA,CAAA,EAAG;IACrB,IAAI/G,OAAO,GAAGpB,SAAS,CAAC9D,MAAM,GAAG,CAAC,IAAI8D,SAAS,CAAC,CAAC,CAAC,KAAKtD,SAAS,GAAGsD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACpF,MAAM6N,WAAW,GAAG,CAAC,cAAc,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,aAAa,EAAE,IAAI,EAAE,cAAc,EAAE,aAAa,EAAE,eAAe,EAAE,eAAe,EAAE,YAAY,EAAE,aAAa,EAAE,eAAe,CAAC;IACxN,MAAMC,wBAAwB,GAAG1M,OAAO,CAAC1F,OAAO,IAAI,CAACrB,QAAQ,CAAC+G,OAAO,CAAC1F,OAAO,CAAC;IAC9E,IAAI0B,IAAI,GAAG0Q,wBAAwB,GAAG1M,OAAO,CAAC1F,OAAO,GAAG0F,OAAO;IAC/D,IAAI0M,wBAAwB,IAAI,OAAO1M,OAAO,CAACiI,KAAK,KAAK,WAAW,EAAE;MACpEjM,IAAI,CAACiM,KAAK,GAAGjI,OAAO,CAACiI,KAAK;IAC5B;IACA,IAAI,IAAI,CAACjI,OAAO,CAACuF,aAAa,CAAC+E,gBAAgB,EAAE;MAC/CtO,IAAI,GAAG;QACL,GAAG,IAAI,CAACgE,OAAO,CAACuF,aAAa,CAAC+E,gBAAgB;QAC9C,GAAGtO;MACL,CAAC;IACH;IACA,IAAI,CAAC0Q,wBAAwB,EAAE;MAC7B1Q,IAAI,GAAG;QACL,GAAGA;MACL,CAAC;MACD,KAAK,MAAM5B,GAAG,IAAIqS,WAAW,EAAE;QAC7B,OAAOzQ,IAAI,CAAC5B,GAAG,CAAC;MAClB;IACF;IACA,OAAO4B,IAAI;EACb;EACA,OAAOkM,eAAeA,CAAClI,OAAO,EAAE;IAC9B,MAAME,MAAM,GAAG,cAAc;IAC7B,KAAK,MAAMyM,MAAM,IAAI3M,OAAO,EAAE;MAC5B,IAAIjF,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAAC8E,OAAO,EAAE2M,MAAM,CAAC,IAAIzM,MAAM,KAAKyM,MAAM,CAACjO,SAAS,CAAC,CAAC,EAAEwB,MAAM,CAACpF,MAAM,CAAC,IAAIQ,SAAS,KAAK0E,OAAO,CAAC2M,MAAM,CAAC,EAAE;QAC3I,OAAO,IAAI;MACb;IACF;IACA,OAAO,KAAK;EACd;AACF;AAEA,MAAMC,UAAU,GAAGC,MAAM,IAAIA,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGF,MAAM,CAACpR,KAAK,CAAC,CAAC,CAAC;AAC7E,MAAMuR,YAAY,CAAC;EACjBlQ,WAAWA,CAACkD,OAAO,EAAE;IACnB,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACiN,aAAa,GAAG,IAAI,CAACjN,OAAO,CAACiN,aAAa,IAAI,KAAK;IACxD,IAAI,CAAC9M,MAAM,GAAGkB,UAAU,CAACH,MAAM,CAAC,eAAe,CAAC;EAClD;EACAgM,qBAAqBA,CAAC9N,IAAI,EAAE;IAC1BA,IAAI,GAAGD,cAAc,CAACC,IAAI,CAAC;IAC3B,IAAI,CAACA,IAAI,IAAIA,IAAI,CAAC/E,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,OAAO,IAAI;IAC/C,MAAMmB,CAAC,GAAG4D,IAAI,CAACxE,KAAK,CAAC,GAAG,CAAC;IACzB,IAAIY,CAAC,CAACV,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;IAC/BU,CAAC,CAAC6Q,GAAG,CAAC,CAAC;IACP,IAAI7Q,CAAC,CAACA,CAAC,CAACV,MAAM,GAAG,CAAC,CAAC,CAAC2L,WAAW,CAAC,CAAC,KAAK,GAAG,EAAE,OAAO,IAAI;IACtD,OAAO,IAAI,CAAC0G,kBAAkB,CAAC3R,CAAC,CAAC8C,IAAI,CAAC,GAAG,CAAC,CAAC;EAC7C;EACA8O,uBAAuBA,CAAChO,IAAI,EAAE;IAC5BA,IAAI,GAAGD,cAAc,CAACC,IAAI,CAAC;IAC3B,IAAI,CAACA,IAAI,IAAIA,IAAI,CAAC/E,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,OAAO+E,IAAI;IAC/C,MAAM5D,CAAC,GAAG4D,IAAI,CAACxE,KAAK,CAAC,GAAG,CAAC;IACzB,OAAO,IAAI,CAACuS,kBAAkB,CAAC3R,CAAC,CAAC,CAAC,CAAC,CAAC;EACtC;EACA2R,kBAAkBA,CAAC/N,IAAI,EAAE;IACvB,IAAInG,QAAQ,CAACmG,IAAI,CAAC,IAAIA,IAAI,CAAC/E,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;MAC5C,IAAI,OAAOgT,IAAI,KAAK,WAAW,IAAI,OAAOA,IAAI,CAACC,mBAAmB,KAAK,WAAW,EAAE;QAClF,IAAI;UACF,IAAIC,aAAa,GAAGF,IAAI,CAACC,mBAAmB,CAAClO,IAAI,CAAC,CAAC,CAAC,CAAC;UACrD,IAAImO,aAAa,IAAI,IAAI,CAACvN,OAAO,CAACwN,YAAY,EAAE;YAC9CD,aAAa,GAAGA,aAAa,CAAC9G,WAAW,CAAC,CAAC;UAC7C;UACA,IAAI8G,aAAa,EAAE,OAAOA,aAAa;QACzC,CAAC,CAAC,OAAOhS,CAAC,EAAE,CAAC;MACf;MACA,MAAMkS,YAAY,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;MAC7E,IAAIjS,CAAC,GAAG4D,IAAI,CAACxE,KAAK,CAAC,GAAG,CAAC;MACvB,IAAI,IAAI,CAACoF,OAAO,CAACwN,YAAY,EAAE;QAC7BhS,CAAC,GAAGA,CAAC,CAAC6C,GAAG,CAACqP,IAAI,IAAIA,IAAI,CAACjH,WAAW,CAAC,CAAC,CAAC;MACvC,CAAC,MAAM,IAAIjL,CAAC,CAACV,MAAM,KAAK,CAAC,EAAE;QACzBU,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,CAACiL,WAAW,CAAC,CAAC;QACzBjL,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,CAACuR,WAAW,CAAC,CAAC;QACzB,IAAIU,YAAY,CAACpT,OAAO,CAACmB,CAAC,CAAC,CAAC,CAAC,CAACiL,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAEjL,CAAC,CAAC,CAAC,CAAC,GAAGoR,UAAU,CAACpR,CAAC,CAAC,CAAC,CAAC,CAACiL,WAAW,CAAC,CAAC,CAAC;MAC1F,CAAC,MAAM,IAAIjL,CAAC,CAACV,MAAM,KAAK,CAAC,EAAE;QACzBU,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,CAACiL,WAAW,CAAC,CAAC;QACzB,IAAIjL,CAAC,CAAC,CAAC,CAAC,CAACV,MAAM,KAAK,CAAC,EAAEU,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,CAACuR,WAAW,CAAC,CAAC;QAChD,IAAIvR,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,IAAIA,CAAC,CAAC,CAAC,CAAC,CAACV,MAAM,KAAK,CAAC,EAAEU,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,CAACuR,WAAW,CAAC,CAAC;QAClE,IAAIU,YAAY,CAACpT,OAAO,CAACmB,CAAC,CAAC,CAAC,CAAC,CAACiL,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAEjL,CAAC,CAAC,CAAC,CAAC,GAAGoR,UAAU,CAACpR,CAAC,CAAC,CAAC,CAAC,CAACiL,WAAW,CAAC,CAAC,CAAC;QACxF,IAAIgH,YAAY,CAACpT,OAAO,CAACmB,CAAC,CAAC,CAAC,CAAC,CAACiL,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAEjL,CAAC,CAAC,CAAC,CAAC,GAAGoR,UAAU,CAACpR,CAAC,CAAC,CAAC,CAAC,CAACiL,WAAW,CAAC,CAAC,CAAC;MAC1F;MACA,OAAOjL,CAAC,CAAC8C,IAAI,CAAC,GAAG,CAAC;IACpB;IACA,OAAO,IAAI,CAAC0B,OAAO,CAAC2N,SAAS,IAAI,IAAI,CAAC3N,OAAO,CAACwN,YAAY,GAAGpO,IAAI,CAACqH,WAAW,CAAC,CAAC,GAAGrH,IAAI;EACxF;EACAwO,eAAeA,CAACxO,IAAI,EAAE;IACpB,IAAI,IAAI,CAACY,OAAO,CAAC6N,IAAI,KAAK,cAAc,IAAI,IAAI,CAAC7N,OAAO,CAAC8N,wBAAwB,EAAE;MACjF1O,IAAI,GAAG,IAAI,CAACgO,uBAAuB,CAAChO,IAAI,CAAC;IAC3C;IACA,OAAO,CAAC,IAAI,CAAC6N,aAAa,IAAI,CAAC,IAAI,CAACA,aAAa,CAACnS,MAAM,IAAI,IAAI,CAACmS,aAAa,CAAC5S,OAAO,CAAC+E,IAAI,CAAC,GAAG,CAAC,CAAC;EACnG;EACA2O,qBAAqBA,CAACrC,KAAK,EAAE;IAC3B,IAAI,CAACA,KAAK,EAAE,OAAO,IAAI;IACvB,IAAIJ,KAAK;IACTI,KAAK,CAAC1R,OAAO,CAACoF,IAAI,IAAI;MACpB,IAAIkM,KAAK,EAAE;MACX,MAAM0C,UAAU,GAAG,IAAI,CAACb,kBAAkB,CAAC/N,IAAI,CAAC;MAChD,IAAI,CAAC,IAAI,CAACY,OAAO,CAACiN,aAAa,IAAI,IAAI,CAACW,eAAe,CAACI,UAAU,CAAC,EAAE1C,KAAK,GAAG0C,UAAU;IACzF,CAAC,CAAC;IACF,IAAI,CAAC1C,KAAK,IAAI,IAAI,CAACtL,OAAO,CAACiN,aAAa,EAAE;MACxCvB,KAAK,CAAC1R,OAAO,CAACoF,IAAI,IAAI;QACpB,IAAIkM,KAAK,EAAE;QACX,MAAM2C,OAAO,GAAG,IAAI,CAACb,uBAAuB,CAAChO,IAAI,CAAC;QAClD,IAAI,IAAI,CAACwO,eAAe,CAACK,OAAO,CAAC,EAAE,OAAO3C,KAAK,GAAG2C,OAAO;QACzD3C,KAAK,GAAG,IAAI,CAACtL,OAAO,CAACiN,aAAa,CAAC5I,IAAI,CAAC6J,YAAY,IAAI;UACtD,IAAIA,YAAY,KAAKD,OAAO,EAAE,OAAOC,YAAY;UACjD,IAAIA,YAAY,CAAC7T,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI4T,OAAO,CAAC5T,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;UAC/D,IAAI6T,YAAY,CAAC7T,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI4T,OAAO,CAAC5T,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI6T,YAAY,CAACxP,SAAS,CAAC,CAAC,EAAEwP,YAAY,CAAC7T,OAAO,CAAC,GAAG,CAAC,CAAC,KAAK4T,OAAO,EAAE,OAAOC,YAAY;UACtJ,IAAIA,YAAY,CAAC7T,OAAO,CAAC4T,OAAO,CAAC,KAAK,CAAC,IAAIA,OAAO,CAACnT,MAAM,GAAG,CAAC,EAAE,OAAOoT,YAAY;QACpF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;IACA,IAAI,CAAC5C,KAAK,EAAEA,KAAK,GAAG,IAAI,CAAClC,gBAAgB,CAAC,IAAI,CAACpJ,OAAO,CAACqJ,WAAW,CAAC,CAAC,CAAC,CAAC;IACtE,OAAOiC,KAAK;EACd;EACAlC,gBAAgBA,CAAC+E,SAAS,EAAE/O,IAAI,EAAE;IAChC,IAAI,CAAC+O,SAAS,EAAE,OAAO,EAAE;IACzB,IAAI,OAAOA,SAAS,KAAK,UAAU,EAAEA,SAAS,GAAGA,SAAS,CAAC/O,IAAI,CAAC;IAChE,IAAInG,QAAQ,CAACkV,SAAS,CAAC,EAAEA,SAAS,GAAG,CAACA,SAAS,CAAC;IAChD,IAAI7N,KAAK,CAAC0C,OAAO,CAACmL,SAAS,CAAC,EAAE,OAAOA,SAAS;IAC9C,IAAI,CAAC/O,IAAI,EAAE,OAAO+O,SAAS,CAACC,OAAO,IAAI,EAAE;IACzC,IAAI9C,KAAK,GAAG6C,SAAS,CAAC/O,IAAI,CAAC;IAC3B,IAAI,CAACkM,KAAK,EAAEA,KAAK,GAAG6C,SAAS,CAAC,IAAI,CAACjB,qBAAqB,CAAC9N,IAAI,CAAC,CAAC;IAC/D,IAAI,CAACkM,KAAK,EAAEA,KAAK,GAAG6C,SAAS,CAAC,IAAI,CAAChB,kBAAkB,CAAC/N,IAAI,CAAC,CAAC;IAC5D,IAAI,CAACkM,KAAK,EAAEA,KAAK,GAAG6C,SAAS,CAAC,IAAI,CAACf,uBAAuB,CAAChO,IAAI,CAAC,CAAC;IACjE,IAAI,CAACkM,KAAK,EAAEA,KAAK,GAAG6C,SAAS,CAACC,OAAO;IACrC,OAAO9C,KAAK,IAAI,EAAE;EACpB;EACA/B,kBAAkBA,CAACnK,IAAI,EAAEiP,YAAY,EAAE;IACrC,MAAMC,aAAa,GAAG,IAAI,CAAClF,gBAAgB,CAACiF,YAAY,IAAI,IAAI,CAACrO,OAAO,CAACqJ,WAAW,IAAI,EAAE,EAAEjK,IAAI,CAAC;IACjG,MAAMsM,KAAK,GAAG,EAAE;IAChB,MAAM6C,OAAO,GAAGpQ,CAAC,IAAI;MACnB,IAAI,CAACA,CAAC,EAAE;MACR,IAAI,IAAI,CAACyP,eAAe,CAACzP,CAAC,CAAC,EAAE;QAC3BuN,KAAK,CAAC7P,IAAI,CAACsC,CAAC,CAAC;MACf,CAAC,MAAM;QACL,IAAI,CAACgC,MAAM,CAACT,IAAI,CAAC,uDAAuDvB,CAAC,EAAE,CAAC;MAC9E;IACF,CAAC;IACD,IAAIlF,QAAQ,CAACmG,IAAI,CAAC,KAAKA,IAAI,CAAC/E,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI+E,IAAI,CAAC/E,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;MACxE,IAAI,IAAI,CAAC2F,OAAO,CAAC6N,IAAI,KAAK,cAAc,EAAEU,OAAO,CAAC,IAAI,CAACpB,kBAAkB,CAAC/N,IAAI,CAAC,CAAC;MAChF,IAAI,IAAI,CAACY,OAAO,CAAC6N,IAAI,KAAK,cAAc,IAAI,IAAI,CAAC7N,OAAO,CAAC6N,IAAI,KAAK,aAAa,EAAEU,OAAO,CAAC,IAAI,CAACrB,qBAAqB,CAAC9N,IAAI,CAAC,CAAC;MAC1H,IAAI,IAAI,CAACY,OAAO,CAAC6N,IAAI,KAAK,aAAa,EAAEU,OAAO,CAAC,IAAI,CAACnB,uBAAuB,CAAChO,IAAI,CAAC,CAAC;IACtF,CAAC,MAAM,IAAInG,QAAQ,CAACmG,IAAI,CAAC,EAAE;MACzBmP,OAAO,CAAC,IAAI,CAACpB,kBAAkB,CAAC/N,IAAI,CAAC,CAAC;IACxC;IACAkP,aAAa,CAACtU,OAAO,CAACwU,EAAE,IAAI;MAC1B,IAAI9C,KAAK,CAACrR,OAAO,CAACmU,EAAE,CAAC,GAAG,CAAC,EAAED,OAAO,CAAC,IAAI,CAACpB,kBAAkB,CAACqB,EAAE,CAAC,CAAC;IACjE,CAAC,CAAC;IACF,OAAO9C,KAAK;EACd;AACF;AAEA,IAAI+C,IAAI,GAAG,CAAC;EACVxF,IAAI,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACtIyF,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;EACVF,EAAE,EAAE;AACN,CAAC,EAAE;EACDvF,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC9YyF,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;EACVF,EAAE,EAAE;AACN,CAAC,EAAE;EACDvF,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC7IyF,EAAE,EAAE,CAAC,CAAC,CAAC;EACPF,EAAE,EAAE;AACN,CAAC,EAAE;EACDvF,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACvDyF,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACbF,EAAE,EAAE;AACN,CAAC,EAAE;EACDvF,IAAI,EAAE,CAAC,IAAI,CAAC;EACZyF,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC;EACzBF,EAAE,EAAE;AACN,CAAC,EAAE;EACDvF,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;EAClByF,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACbF,EAAE,EAAE;AACN,CAAC,EAAE;EACDvF,IAAI,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC;EACnByF,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACbF,EAAE,EAAE;AACN,CAAC,EAAE;EACDvF,IAAI,EAAE,CAAC,IAAI,CAAC;EACZyF,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAChBF,EAAE,EAAE;AACN,CAAC,EAAE;EACDvF,IAAI,EAAE,CAAC,IAAI,CAAC;EACZyF,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;EACVF,EAAE,EAAE;AACN,CAAC,EAAE;EACDvF,IAAI,EAAE,CAAC,IAAI,CAAC;EACZyF,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;EACpBF,EAAE,EAAE;AACN,CAAC,EAAE;EACDvF,IAAI,EAAE,CAAC,IAAI,CAAC;EACZyF,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;EACjBF,EAAE,EAAE;AACN,CAAC,EAAE;EACDvF,IAAI,EAAE,CAAC,IAAI,CAAC;EACZyF,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;EACVF,EAAE,EAAE;AACN,CAAC,EAAE;EACDvF,IAAI,EAAE,CAAC,IAAI,CAAC;EACZyF,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;EACVF,EAAE,EAAE;AACN,CAAC,EAAE;EACDvF,IAAI,EAAE,CAAC,IAAI,CAAC;EACZyF,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAChBF,EAAE,EAAE;AACN,CAAC,EAAE;EACDvF,IAAI,EAAE,CAAC,IAAI,CAAC;EACZyF,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;EACdF,EAAE,EAAE;AACN,CAAC,EAAE;EACDvF,IAAI,EAAE,CAAC,IAAI,CAAC;EACZyF,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACbF,EAAE,EAAE;AACN,CAAC,EAAE;EACDvF,IAAI,EAAE,CAAC,IAAI,CAAC;EACZyF,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;EACVF,EAAE,EAAE;AACN,CAAC,EAAE;EACDvF,IAAI,EAAE,CAAC,KAAK,CAAC;EACbyF,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACbF,EAAE,EAAE;AACN,CAAC,EAAE;EACDvF,IAAI,EAAE,CAAC,IAAI,CAAC;EACZyF,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;EAClBF,EAAE,EAAE;AACN,CAAC,EAAE;EACDvF,IAAI,EAAE,CAAC,IAAI,CAAC;EACZyF,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;EACVF,EAAE,EAAE;AACN,CAAC,EAAE;EACDvF,IAAI,EAAE,CAAC,IAAI,CAAC;EACZyF,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;EACdF,EAAE,EAAE;AACN,CAAC,EAAE;EACDvF,IAAI,EAAE,CAAC,IAAI,CAAC;EACZyF,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAChBF,EAAE,EAAE;AACN,CAAC,EAAE;EACDvF,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;EAClByF,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;EAClBF,EAAE,EAAE;AACN,CAAC,CAAC;AACF,IAAIG,kBAAkB,GAAG;EACvB,CAAC,EAAExK,CAAC,IAAIyK,MAAM,CAACzK,CAAC,GAAG,CAAC,CAAC;EACrB,CAAC,EAAEA,CAAC,IAAIyK,MAAM,CAACzK,CAAC,IAAI,CAAC,CAAC;EACtB,CAAC,EAAEA,CAAC,IAAI,CAAC;EACT,CAAC,EAAEA,CAAC,IAAIyK,MAAM,CAACzK,CAAC,GAAG,EAAE,IAAI,CAAC,IAAIA,CAAC,GAAG,GAAG,IAAI,EAAE,GAAG,CAAC,GAAGA,CAAC,GAAG,EAAE,IAAI,CAAC,IAAIA,CAAC,GAAG,EAAE,IAAI,CAAC,KAAKA,CAAC,GAAG,GAAG,GAAG,EAAE,IAAIA,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EACxH,CAAC,EAAEA,CAAC,IAAIyK,MAAM,CAACzK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAGA,CAAC,IAAI,CAAC,GAAG,CAAC,GAAGA,CAAC,IAAI,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAG,GAAG,IAAI,CAAC,IAAIA,CAAC,GAAG,GAAG,IAAI,EAAE,GAAG,CAAC,GAAGA,CAAC,GAAG,GAAG,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;EAChH,CAAC,EAAEA,CAAC,IAAIyK,MAAM,CAACzK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAGA,CAAC,IAAI,CAAC,IAAIA,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EACrD,CAAC,EAAEA,CAAC,IAAIyK,MAAM,CAACzK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAG,EAAE,IAAI,CAAC,IAAIA,CAAC,GAAG,EAAE,IAAI,CAAC,KAAKA,CAAC,GAAG,GAAG,GAAG,EAAE,IAAIA,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EAClG,CAAC,EAAEA,CAAC,IAAIyK,MAAM,CAACzK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAGA,CAAC,IAAI,CAAC,GAAG,CAAC,GAAGA,CAAC,IAAI,CAAC,IAAIA,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;EACnE,CAAC,EAAEA,CAAC,IAAIyK,MAAM,CAACzK,CAAC,IAAI,CAAC,CAAC;EACtB,EAAE,EAAEA,CAAC,IAAIyK,MAAM,CAACzK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAGA,CAAC,IAAI,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;EACrE,EAAE,EAAEA,CAAC,IAAIyK,MAAM,CAACzK,CAAC,IAAI,CAAC,IAAIA,CAAC,IAAI,EAAE,GAAG,CAAC,GAAGA,CAAC,IAAI,CAAC,IAAIA,CAAC,IAAI,EAAE,GAAG,CAAC,GAAGA,CAAC,GAAG,CAAC,IAAIA,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;EACxF,EAAE,EAAEA,CAAC,IAAIyK,MAAM,CAACzK,CAAC,GAAG,EAAE,IAAI,CAAC,IAAIA,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC;EAC7C,EAAE,EAAEA,CAAC,IAAIyK,MAAM,CAACzK,CAAC,KAAK,CAAC,CAAC;EACxB,EAAE,EAAEA,CAAC,IAAIyK,MAAM,CAACzK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAGA,CAAC,IAAI,CAAC,GAAG,CAAC,GAAGA,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EACzD,EAAE,EAAEA,CAAC,IAAIyK,MAAM,CAACzK,CAAC,GAAG,EAAE,IAAI,CAAC,IAAIA,CAAC,GAAG,GAAG,IAAI,EAAE,GAAG,CAAC,GAAGA,CAAC,GAAG,EAAE,IAAI,CAAC,KAAKA,CAAC,GAAG,GAAG,GAAG,EAAE,IAAIA,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EAC1G,EAAE,EAAEA,CAAC,IAAIyK,MAAM,CAACzK,CAAC,GAAG,EAAE,IAAI,CAAC,IAAIA,CAAC,GAAG,GAAG,IAAI,EAAE,GAAG,CAAC,GAAGA,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EACnE,EAAE,EAAEA,CAAC,IAAIyK,MAAM,CAACzK,CAAC,IAAI,CAAC,IAAIA,CAAC,GAAG,EAAE,IAAI,CAAC,IAAIA,CAAC,GAAG,GAAG,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;EAC/D,EAAE,EAAEA,CAAC,IAAIyK,MAAM,CAACzK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAGA,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EAC5C,EAAE,EAAEA,CAAC,IAAIyK,MAAM,CAACzK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAGA,CAAC,IAAI,CAAC,IAAIA,CAAC,GAAG,GAAG,GAAG,CAAC,IAAIA,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,GAAGA,CAAC,GAAG,GAAG,GAAG,EAAE,IAAIA,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;EAC9G,EAAE,EAAEA,CAAC,IAAIyK,MAAM,CAACzK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAGA,CAAC,IAAI,CAAC,IAAIA,CAAC,GAAG,GAAG,GAAG,CAAC,IAAIA,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;EAC3E,EAAE,EAAEA,CAAC,IAAIyK,MAAM,CAACzK,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAG,GAAG,IAAI,CAAC,IAAIA,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EAC3F,EAAE,EAAEA,CAAC,IAAIyK,MAAM,CAACzK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAGA,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAACA,CAAC,GAAG,CAAC,IAAIA,CAAC,GAAG,EAAE,KAAKA,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;AACpF,CAAC;AACD,MAAM0K,eAAe,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;AAC1C,MAAMC,YAAY,GAAG,CAAC,IAAI,CAAC;AAC3B,MAAMC,aAAa,GAAG;EACpBC,IAAI,EAAE,CAAC;EACPC,GAAG,EAAE,CAAC;EACNC,GAAG,EAAE,CAAC;EACNC,GAAG,EAAE,CAAC;EACNC,IAAI,EAAE,CAAC;EACPC,KAAK,EAAE;AACT,CAAC;AACD,MAAMC,WAAW,GAAGA,CAAA,KAAM;EACxB,MAAMC,KAAK,GAAG,CAAC,CAAC;EAChBd,IAAI,CAACzU,OAAO,CAAC2D,GAAG,IAAI;IAClBA,GAAG,CAACsL,IAAI,CAACjP,OAAO,CAACyP,CAAC,IAAI;MACpB8F,KAAK,CAAC9F,CAAC,CAAC,GAAG;QACT+F,OAAO,EAAE7R,GAAG,CAAC+Q,EAAE;QACfe,OAAO,EAAEd,kBAAkB,CAAChR,GAAG,CAAC6Q,EAAE;MACpC,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAOe,KAAK;AACd,CAAC;AACD,MAAMG,cAAc,CAAC;EACnB5S,WAAWA,CAACqM,aAAa,EAAE;IACzB,IAAInJ,OAAO,GAAGpB,SAAS,CAAC9D,MAAM,GAAG,CAAC,IAAI8D,SAAS,CAAC,CAAC,CAAC,KAAKtD,SAAS,GAAGsD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACpF,IAAI,CAACuK,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACnJ,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACG,MAAM,GAAGkB,UAAU,CAACH,MAAM,CAAC,gBAAgB,CAAC;IACjD,IAAI,CAAC,CAAC,IAAI,CAAClB,OAAO,CAAC2P,iBAAiB,IAAIb,YAAY,CAACc,QAAQ,CAAC,IAAI,CAAC5P,OAAO,CAAC2P,iBAAiB,CAAC,MAAM,OAAOtC,IAAI,KAAK,WAAW,IAAI,CAACA,IAAI,CAACwC,WAAW,CAAC,EAAE;MACpJ,IAAI,CAAC7P,OAAO,CAAC2P,iBAAiB,GAAG,IAAI;MACrC,IAAI,CAACxP,MAAM,CAACR,KAAK,CAAC,oJAAoJ,CAAC;IACzK;IACA,IAAI,CAAC4P,KAAK,GAAGD,WAAW,CAAC,CAAC;IAC1B,IAAI,CAACQ,gBAAgB,GAAG,CAAC,CAAC;EAC5B;EACAC,OAAOA,CAAChN,GAAG,EAAE7J,GAAG,EAAE;IAChB,IAAI,CAACqW,KAAK,CAACxM,GAAG,CAAC,GAAG7J,GAAG;EACvB;EACA8W,UAAUA,CAAA,EAAG;IACX,IAAI,CAACF,gBAAgB,GAAG,CAAC,CAAC;EAC5B;EACAG,OAAOA,CAAC7Q,IAAI,EAAE;IACZ,IAAIY,OAAO,GAAGpB,SAAS,CAAC9D,MAAM,GAAG,CAAC,IAAI8D,SAAS,CAAC,CAAC,CAAC,KAAKtD,SAAS,GAAGsD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACpF,IAAI,IAAI,CAAC6J,gBAAgB,CAAC,CAAC,EAAE;MAC3B,MAAMyH,WAAW,GAAG/Q,cAAc,CAACC,IAAI,KAAK,KAAK,GAAG,IAAI,GAAGA,IAAI,CAAC;MAChE,MAAME,IAAI,GAAGU,OAAO,CAACuI,OAAO,GAAG,SAAS,GAAG,UAAU;MACrD,MAAM4H,QAAQ,GAAGzM,IAAI,CAACE,SAAS,CAAC;QAC9BsM,WAAW;QACX5Q;MACF,CAAC,CAAC;MACF,IAAI6Q,QAAQ,IAAI,IAAI,CAACL,gBAAgB,EAAE;QACrC,OAAO,IAAI,CAACA,gBAAgB,CAACK,QAAQ,CAAC;MACxC;MACA,IAAIC,IAAI;MACR,IAAI;QACFA,IAAI,GAAG,IAAI/C,IAAI,CAACwC,WAAW,CAACK,WAAW,EAAE;UACvC5Q;QACF,CAAC,CAAC;MACJ,CAAC,CAAC,OAAO+Q,GAAG,EAAE;QACZ,IAAI,CAACjR,IAAI,CAAC2G,KAAK,CAAC,KAAK,CAAC,EAAE;QACxB,MAAMuK,OAAO,GAAG,IAAI,CAACnH,aAAa,CAACiE,uBAAuB,CAAChO,IAAI,CAAC;QAChEgR,IAAI,GAAG,IAAI,CAACH,OAAO,CAACK,OAAO,EAAEtQ,OAAO,CAAC;MACvC;MACA,IAAI,CAAC8P,gBAAgB,CAACK,QAAQ,CAAC,GAAGC,IAAI;MACtC,OAAOA,IAAI;IACb;IACA,OAAO,IAAI,CAACb,KAAK,CAACnQ,IAAI,CAAC,IAAI,IAAI,CAACmQ,KAAK,CAAC,IAAI,CAACpG,aAAa,CAACiE,uBAAuB,CAAChO,IAAI,CAAC,CAAC;EACzF;EACAmR,WAAWA,CAACnR,IAAI,EAAE;IAChB,IAAIY,OAAO,GAAGpB,SAAS,CAAC9D,MAAM,GAAG,CAAC,IAAI8D,SAAS,CAAC,CAAC,CAAC,KAAKtD,SAAS,GAAGsD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACpF,MAAMwR,IAAI,GAAG,IAAI,CAACH,OAAO,CAAC7Q,IAAI,EAAEY,OAAO,CAAC;IACxC,IAAI,IAAI,CAACyI,gBAAgB,CAAC,CAAC,EAAE;MAC3B,OAAO2H,IAAI,IAAIA,IAAI,CAACI,eAAe,CAAC,CAAC,CAACC,gBAAgB,CAAC3V,MAAM,GAAG,CAAC;IACnE;IACA,OAAOsV,IAAI,IAAIA,IAAI,CAACZ,OAAO,CAAC1U,MAAM,GAAG,CAAC;EACxC;EACA4V,mBAAmBA,CAACtR,IAAI,EAAEhF,GAAG,EAAE;IAC7B,IAAI4F,OAAO,GAAGpB,SAAS,CAAC9D,MAAM,GAAG,CAAC,IAAI8D,SAAS,CAAC,CAAC,CAAC,KAAKtD,SAAS,GAAGsD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACpF,OAAO,IAAI,CAACqL,WAAW,CAAC7K,IAAI,EAAEY,OAAO,CAAC,CAAC3B,GAAG,CAAC6L,MAAM,IAAI,GAAG9P,GAAG,GAAG8P,MAAM,EAAE,CAAC;EACzE;EACAD,WAAWA,CAAC7K,IAAI,EAAE;IAChB,IAAIY,OAAO,GAAGpB,SAAS,CAAC9D,MAAM,GAAG,CAAC,IAAI8D,SAAS,CAAC,CAAC,CAAC,KAAKtD,SAAS,GAAGsD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACpF,MAAMwR,IAAI,GAAG,IAAI,CAACH,OAAO,CAAC7Q,IAAI,EAAEY,OAAO,CAAC;IACxC,IAAI,CAACoQ,IAAI,EAAE;MACT,OAAO,EAAE;IACX;IACA,IAAI,IAAI,CAAC3H,gBAAgB,CAAC,CAAC,EAAE;MAC3B,OAAO2H,IAAI,CAACI,eAAe,CAAC,CAAC,CAACC,gBAAgB,CAACE,IAAI,CAAC,CAACC,eAAe,EAAEC,eAAe,KAAK9B,aAAa,CAAC6B,eAAe,CAAC,GAAG7B,aAAa,CAAC8B,eAAe,CAAC,CAAC,CAACxS,GAAG,CAACyS,cAAc,IAAI,GAAG,IAAI,CAAC9Q,OAAO,CAAC+Q,OAAO,GAAG/Q,OAAO,CAACuI,OAAO,GAAG,UAAU,IAAI,CAACvI,OAAO,CAAC+Q,OAAO,EAAE,GAAG,EAAE,GAAGD,cAAc,EAAE,CAAC;IACxR;IACA,OAAOV,IAAI,CAACZ,OAAO,CAACnR,GAAG,CAAC2S,MAAM,IAAI,IAAI,CAAC3I,SAAS,CAACjJ,IAAI,EAAE4R,MAAM,EAAEhR,OAAO,CAAC,CAAC;EAC1E;EACAqI,SAASA,CAACjJ,IAAI,EAAE6I,KAAK,EAAE;IACrB,IAAIjI,OAAO,GAAGpB,SAAS,CAAC9D,MAAM,GAAG,CAAC,IAAI8D,SAAS,CAAC,CAAC,CAAC,KAAKtD,SAAS,GAAGsD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACpF,MAAMwR,IAAI,GAAG,IAAI,CAACH,OAAO,CAAC7Q,IAAI,EAAEY,OAAO,CAAC;IACxC,IAAIoQ,IAAI,EAAE;MACR,IAAI,IAAI,CAAC3H,gBAAgB,CAAC,CAAC,EAAE;QAC3B,OAAO,GAAG,IAAI,CAACzI,OAAO,CAAC+Q,OAAO,GAAG/Q,OAAO,CAACuI,OAAO,GAAG,UAAU,IAAI,CAACvI,OAAO,CAAC+Q,OAAO,EAAE,GAAG,EAAE,GAAGX,IAAI,CAACa,MAAM,CAAChJ,KAAK,CAAC,EAAE;MACjH;MACA,OAAO,IAAI,CAACiJ,wBAAwB,CAACd,IAAI,EAAEnI,KAAK,CAAC;IACnD;IACA,IAAI,CAAC9H,MAAM,CAACT,IAAI,CAAC,6BAA6BN,IAAI,EAAE,CAAC;IACrD,OAAO,EAAE;EACX;EACA8R,wBAAwBA,CAACd,IAAI,EAAEnI,KAAK,EAAE;IACpC,MAAMkJ,GAAG,GAAGf,IAAI,CAACgB,KAAK,GAAGhB,IAAI,CAACX,OAAO,CAACxH,KAAK,CAAC,GAAGmI,IAAI,CAACX,OAAO,CAAC4B,IAAI,CAACC,GAAG,CAACrJ,KAAK,CAAC,CAAC;IAC5E,IAAIiC,MAAM,GAAGkG,IAAI,CAACZ,OAAO,CAAC2B,GAAG,CAAC;IAC9B,IAAI,IAAI,CAACnR,OAAO,CAACuR,oBAAoB,IAAInB,IAAI,CAACZ,OAAO,CAAC1U,MAAM,KAAK,CAAC,IAAIsV,IAAI,CAACZ,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;MAC3F,IAAItF,MAAM,KAAK,CAAC,EAAE;QAChBA,MAAM,GAAG,QAAQ;MACnB,CAAC,MAAM,IAAIA,MAAM,KAAK,CAAC,EAAE;QACvBA,MAAM,GAAG,EAAE;MACb;IACF;IACA,MAAMsH,YAAY,GAAGA,CAAA,KAAM,IAAI,CAACxR,OAAO,CAAC+Q,OAAO,IAAI7G,MAAM,CAAC/C,QAAQ,CAAC,CAAC,GAAG,IAAI,CAACnH,OAAO,CAAC+Q,OAAO,GAAG7G,MAAM,CAAC/C,QAAQ,CAAC,CAAC,GAAG+C,MAAM,CAAC/C,QAAQ,CAAC,CAAC;IACnI,IAAI,IAAI,CAACnH,OAAO,CAAC2P,iBAAiB,KAAK,IAAI,EAAE;MAC3C,IAAIzF,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;MAC3B,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE,OAAO,WAAWA,MAAM,CAAC/C,QAAQ,CAAC,CAAC,EAAE;MACrE,OAAOqK,YAAY,CAAC,CAAC;IACvB,CAAC,MAAM,IAAI,IAAI,CAACxR,OAAO,CAAC2P,iBAAiB,KAAK,IAAI,EAAE;MAClD,OAAO6B,YAAY,CAAC,CAAC;IACvB,CAAC,MAAM,IAAI,IAAI,CAACxR,OAAO,CAACuR,oBAAoB,IAAInB,IAAI,CAACZ,OAAO,CAAC1U,MAAM,KAAK,CAAC,IAAIsV,IAAI,CAACZ,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;MAClG,OAAOgC,YAAY,CAAC,CAAC;IACvB;IACA,OAAO,IAAI,CAACxR,OAAO,CAAC+Q,OAAO,IAAII,GAAG,CAAChK,QAAQ,CAAC,CAAC,GAAG,IAAI,CAACnH,OAAO,CAAC+Q,OAAO,GAAGI,GAAG,CAAChK,QAAQ,CAAC,CAAC,GAAGgK,GAAG,CAAChK,QAAQ,CAAC,CAAC;EACxG;EACAsB,gBAAgBA,CAAA,EAAG;IACjB,OAAO,CAACoG,eAAe,CAACe,QAAQ,CAAC,IAAI,CAAC5P,OAAO,CAAC2P,iBAAiB,CAAC;EAClE;AACF;AAEA,MAAM8B,oBAAoB,GAAG,SAAAA,CAAUzV,IAAI,EAAEC,WAAW,EAAE7B,GAAG,EAAE;EAC7D,IAAI4D,YAAY,GAAGY,SAAS,CAAC9D,MAAM,GAAG,CAAC,IAAI8D,SAAS,CAAC,CAAC,CAAC,KAAKtD,SAAS,GAAGsD,SAAS,CAAC,CAAC,CAAC,GAAG,GAAG;EAC1F,IAAI6D,mBAAmB,GAAG7D,SAAS,CAAC9D,MAAM,GAAG,CAAC,IAAI8D,SAAS,CAAC,CAAC,CAAC,KAAKtD,SAAS,GAAGsD,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;EAClG,IAAInE,IAAI,GAAGsB,mBAAmB,CAACC,IAAI,EAAEC,WAAW,EAAE7B,GAAG,CAAC;EACtD,IAAI,CAACK,IAAI,IAAIgI,mBAAmB,IAAIxJ,QAAQ,CAACmB,GAAG,CAAC,EAAE;IACjDK,IAAI,GAAGkE,QAAQ,CAAC3C,IAAI,EAAE5B,GAAG,EAAE4D,YAAY,CAAC;IACxC,IAAIvD,IAAI,KAAKa,SAAS,EAAEb,IAAI,GAAGkE,QAAQ,CAAC1C,WAAW,EAAE7B,GAAG,EAAE4D,YAAY,CAAC;EACzE;EACA,OAAOvD,IAAI;AACb,CAAC;AACD,MAAMiX,SAAS,GAAGC,GAAG,IAAIA,GAAG,CAACrX,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC;AACnD,MAAMsX,YAAY,CAAC;EACjB9U,WAAWA,CAAA,EAAG;IACZ,IAAIkD,OAAO,GAAGpB,SAAS,CAAC9D,MAAM,GAAG,CAAC,IAAI8D,SAAS,CAAC,CAAC,CAAC,KAAKtD,SAAS,GAAGsD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACpF,IAAI,CAACuB,MAAM,GAAGkB,UAAU,CAACH,MAAM,CAAC,cAAc,CAAC;IAC/C,IAAI,CAAClB,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC6R,MAAM,GAAG7R,OAAO,CAACuF,aAAa,IAAIvF,OAAO,CAACuF,aAAa,CAACsM,MAAM,KAAK3V,KAAK,IAAIA,KAAK,CAAC;IACvF,IAAI,CAAC+D,IAAI,CAACD,OAAO,CAAC;EACpB;EACAC,IAAIA,CAAA,EAAG;IACL,IAAID,OAAO,GAAGpB,SAAS,CAAC9D,MAAM,GAAG,CAAC,IAAI8D,SAAS,CAAC,CAAC,CAAC,KAAKtD,SAAS,GAAGsD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACpF,IAAI,CAACoB,OAAO,CAACuF,aAAa,EAAEvF,OAAO,CAACuF,aAAa,GAAG;MAClDuM,WAAW,EAAE;IACf,CAAC;IACD,MAAM;MACJlV,MAAM,EAAEmV,QAAQ;MAChBD,WAAW;MACXE,mBAAmB;MACnB9R,MAAM;MACN+R,aAAa;MACb/H,MAAM;MACNgI,aAAa;MACbC,eAAe;MACfC,cAAc;MACdC,cAAc;MACdC,aAAa;MACbC,oBAAoB;MACpBC,aAAa;MACbC,oBAAoB;MACpBC,uBAAuB;MACvBC,WAAW;MACXC;IACF,CAAC,GAAG5S,OAAO,CAACuF,aAAa;IACzB,IAAI,CAAC3I,MAAM,GAAGmV,QAAQ,KAAKzW,SAAS,GAAGyW,QAAQ,GAAGnV,MAAM;IACxD,IAAI,CAACkV,WAAW,GAAGA,WAAW,KAAKxW,SAAS,GAAGwW,WAAW,GAAG,IAAI;IACjE,IAAI,CAACE,mBAAmB,GAAGA,mBAAmB,KAAK1W,SAAS,GAAG0W,mBAAmB,GAAG,KAAK;IAC1F,IAAI,CAAC9R,MAAM,GAAGA,MAAM,GAAGzD,WAAW,CAACyD,MAAM,CAAC,GAAG+R,aAAa,IAAI,IAAI;IAClE,IAAI,CAAC/H,MAAM,GAAGA,MAAM,GAAGzN,WAAW,CAACyN,MAAM,CAAC,GAAGgI,aAAa,IAAI,IAAI;IAClE,IAAI,CAACC,eAAe,GAAGA,eAAe,IAAI,GAAG;IAC7C,IAAI,CAACE,cAAc,GAAGD,cAAc,GAAG,EAAE,GAAGC,cAAc,IAAI,GAAG;IACjE,IAAI,CAACD,cAAc,GAAG,IAAI,CAACC,cAAc,GAAG,EAAE,GAAGD,cAAc,IAAI,EAAE;IACrE,IAAI,CAACE,aAAa,GAAGA,aAAa,GAAG7V,WAAW,CAAC6V,aAAa,CAAC,GAAGC,oBAAoB,IAAI9V,WAAW,CAAC,KAAK,CAAC;IAC5G,IAAI,CAAC+V,aAAa,GAAGA,aAAa,GAAG/V,WAAW,CAAC+V,aAAa,CAAC,GAAGC,oBAAoB,IAAIhW,WAAW,CAAC,GAAG,CAAC;IAC1G,IAAI,CAACiW,uBAAuB,GAAGA,uBAAuB,IAAI,GAAG;IAC7D,IAAI,CAACC,WAAW,GAAGA,WAAW,IAAI,IAAI;IACtC,IAAI,CAACC,YAAY,GAAGA,YAAY,KAAKtX,SAAS,GAAGsX,YAAY,GAAG,KAAK;IACrE,IAAI,CAACC,WAAW,CAAC,CAAC;EACpB;EACA7H,KAAKA,CAAA,EAAG;IACN,IAAI,IAAI,CAAChL,OAAO,EAAE,IAAI,CAACC,IAAI,CAAC,IAAI,CAACD,OAAO,CAAC;EAC3C;EACA6S,WAAWA,CAAA,EAAG;IACZ,MAAMC,gBAAgB,GAAGA,CAACC,cAAc,EAAE3V,OAAO,KAAK;MACpD,IAAI2V,cAAc,IAAIA,cAAc,CAAC1W,MAAM,KAAKe,OAAO,EAAE;QACvD2V,cAAc,CAACC,SAAS,GAAG,CAAC;QAC5B,OAAOD,cAAc;MACvB;MACA,OAAO,IAAIvV,MAAM,CAACJ,OAAO,EAAE,GAAG,CAAC;IACjC,CAAC;IACD,IAAI,CAAC6V,MAAM,GAAGH,gBAAgB,CAAC,IAAI,CAACG,MAAM,EAAE,GAAG,IAAI,CAAC/S,MAAM,QAAQ,IAAI,CAACgK,MAAM,EAAE,CAAC;IAChF,IAAI,CAACgJ,cAAc,GAAGJ,gBAAgB,CAAC,IAAI,CAACI,cAAc,EAAE,GAAG,IAAI,CAAChT,MAAM,GAAG,IAAI,CAACmS,cAAc,QAAQ,IAAI,CAACD,cAAc,GAAG,IAAI,CAAClI,MAAM,EAAE,CAAC;IAC5I,IAAI,CAACjE,aAAa,GAAG6M,gBAAgB,CAAC,IAAI,CAAC7M,aAAa,EAAE,GAAG,IAAI,CAACqM,aAAa,QAAQ,IAAI,CAACE,aAAa,EAAE,CAAC;EAC9G;EACA7H,WAAWA,CAACjO,GAAG,EAAEV,IAAI,EAAE+G,GAAG,EAAE/C,OAAO,EAAE;IACnC,IAAI+F,KAAK;IACT,IAAI7J,KAAK;IACT,IAAIiX,QAAQ;IACZ,MAAMlX,WAAW,GAAG,IAAI,CAAC+D,OAAO,IAAI,IAAI,CAACA,OAAO,CAACuF,aAAa,IAAI,IAAI,CAACvF,OAAO,CAACuF,aAAa,CAAC+E,gBAAgB,IAAI,CAAC,CAAC;IACnH,MAAM8I,YAAY,GAAGhZ,GAAG,IAAI;MAC1B,IAAIA,GAAG,CAACC,OAAO,CAAC,IAAI,CAAC8X,eAAe,CAAC,GAAG,CAAC,EAAE;QACzC,MAAM1X,IAAI,GAAGgX,oBAAoB,CAACzV,IAAI,EAAEC,WAAW,EAAE7B,GAAG,EAAE,IAAI,CAAC4F,OAAO,CAAChC,YAAY,EAAE,IAAI,CAACgC,OAAO,CAACyC,mBAAmB,CAAC;QACtH,OAAO,IAAI,CAACmQ,YAAY,GAAG,IAAI,CAACf,MAAM,CAACpX,IAAI,EAAEa,SAAS,EAAEyH,GAAG,EAAE;UAC3D,GAAG/C,OAAO;UACV,GAAGhE,IAAI;UACPqX,gBAAgB,EAAEjZ;QACpB,CAAC,CAAC,GAAGK,IAAI;MACX;MACA,MAAMe,CAAC,GAAGpB,GAAG,CAACQ,KAAK,CAAC,IAAI,CAACuX,eAAe,CAAC;MACzC,MAAMhX,CAAC,GAAGK,CAAC,CAACkC,KAAK,CAAC,CAAC,CAAC4V,IAAI,CAAC,CAAC;MAC1B,MAAMC,CAAC,GAAG/X,CAAC,CAAC8C,IAAI,CAAC,IAAI,CAAC6T,eAAe,CAAC,CAACmB,IAAI,CAAC,CAAC;MAC7C,OAAO,IAAI,CAACzB,MAAM,CAACJ,oBAAoB,CAACzV,IAAI,EAAEC,WAAW,EAAEd,CAAC,EAAE,IAAI,CAAC6E,OAAO,CAAChC,YAAY,EAAE,IAAI,CAACgC,OAAO,CAACyC,mBAAmB,CAAC,EAAE8Q,CAAC,EAAExQ,GAAG,EAAE;QAClI,GAAG/C,OAAO;QACV,GAAGhE,IAAI;QACPqX,gBAAgB,EAAElY;MACpB,CAAC,CAAC;IACJ,CAAC;IACD,IAAI,CAAC0X,WAAW,CAAC,CAAC;IAClB,MAAMW,2BAA2B,GAAGxT,OAAO,IAAIA,OAAO,CAACwT,2BAA2B,IAAI,IAAI,CAACxT,OAAO,CAACwT,2BAA2B;IAC9H,MAAMhJ,eAAe,GAAGxK,OAAO,IAAIA,OAAO,CAACuF,aAAa,IAAIvF,OAAO,CAACuF,aAAa,CAACiF,eAAe,KAAKlP,SAAS,GAAG0E,OAAO,CAACuF,aAAa,CAACiF,eAAe,GAAG,IAAI,CAACxK,OAAO,CAACuF,aAAa,CAACiF,eAAe;IACpM,MAAMiJ,KAAK,GAAG,CAAC;MACbC,KAAK,EAAE,IAAI,CAACR,cAAc;MAC1BS,SAAS,EAAEhC,GAAG,IAAID,SAAS,CAACC,GAAG;IACjC,CAAC,EAAE;MACD+B,KAAK,EAAE,IAAI,CAACT,MAAM;MAClBU,SAAS,EAAEhC,GAAG,IAAI,IAAI,CAACG,WAAW,GAAGJ,SAAS,CAAC,IAAI,CAAC9U,MAAM,CAAC+U,GAAG,CAAC,CAAC,GAAGD,SAAS,CAACC,GAAG;IAClF,CAAC,CAAC;IACF8B,KAAK,CAACzZ,OAAO,CAAC4Z,IAAI,IAAI;MACpBT,QAAQ,GAAG,CAAC;MACZ,OAAOpN,KAAK,GAAG6N,IAAI,CAACF,KAAK,CAACG,IAAI,CAACnX,GAAG,CAAC,EAAE;QACnC,MAAMoX,UAAU,GAAG/N,KAAK,CAAC,CAAC,CAAC,CAACuN,IAAI,CAAC,CAAC;QAClCpX,KAAK,GAAGkX,YAAY,CAACU,UAAU,CAAC;QAChC,IAAI5X,KAAK,KAAKZ,SAAS,EAAE;UACvB,IAAI,OAAOkY,2BAA2B,KAAK,UAAU,EAAE;YACrD,MAAMO,IAAI,GAAGP,2BAA2B,CAAC9W,GAAG,EAAEqJ,KAAK,EAAE/F,OAAO,CAAC;YAC7D9D,KAAK,GAAGjD,QAAQ,CAAC8a,IAAI,CAAC,GAAGA,IAAI,GAAG,EAAE;UACpC,CAAC,MAAM,IAAI/T,OAAO,IAAIjF,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAAC8E,OAAO,EAAE8T,UAAU,CAAC,EAAE;YAC/E5X,KAAK,GAAG,EAAE;UACZ,CAAC,MAAM,IAAIsO,eAAe,EAAE;YAC1BtO,KAAK,GAAG6J,KAAK,CAAC,CAAC,CAAC;YAChB;UACF,CAAC,MAAM;YACL,IAAI,CAAC5F,MAAM,CAACT,IAAI,CAAC,8BAA8BoU,UAAU,sBAAsBpX,GAAG,EAAE,CAAC;YACrFR,KAAK,GAAG,EAAE;UACZ;QACF,CAAC,MAAM,IAAI,CAACjD,QAAQ,CAACiD,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC8V,mBAAmB,EAAE;UACxD9V,KAAK,GAAGxC,UAAU,CAACwC,KAAK,CAAC;QAC3B;QACA,MAAMyX,SAAS,GAAGC,IAAI,CAACD,SAAS,CAACzX,KAAK,CAAC;QACvCQ,GAAG,GAAGA,GAAG,CAACpC,OAAO,CAACyL,KAAK,CAAC,CAAC,CAAC,EAAE4N,SAAS,CAAC;QACtC,IAAInJ,eAAe,EAAE;UACnBoJ,IAAI,CAACF,KAAK,CAACV,SAAS,IAAI9W,KAAK,CAACpB,MAAM;UACpC8Y,IAAI,CAACF,KAAK,CAACV,SAAS,IAAIjN,KAAK,CAAC,CAAC,CAAC,CAACjL,MAAM;QACzC,CAAC,MAAM;UACL8Y,IAAI,CAACF,KAAK,CAACV,SAAS,GAAG,CAAC;QAC1B;QACAG,QAAQ,EAAE;QACV,IAAIA,QAAQ,IAAI,IAAI,CAACR,WAAW,EAAE;UAChC;QACF;MACF;IACF,CAAC,CAAC;IACF,OAAOjW,GAAG;EACZ;EACAoO,IAAIA,CAACpO,GAAG,EAAE8R,EAAE,EAAE;IACZ,IAAIxO,OAAO,GAAGpB,SAAS,CAAC9D,MAAM,GAAG,CAAC,IAAI8D,SAAS,CAAC,CAAC,CAAC,KAAKtD,SAAS,GAAGsD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACpF,IAAImH,KAAK;IACT,IAAI7J,KAAK;IACT,IAAI8X,aAAa;IACjB,MAAMC,gBAAgB,GAAGA,CAAC7Z,GAAG,EAAE8Z,gBAAgB,KAAK;MAClD,MAAMC,GAAG,GAAG,IAAI,CAACzB,uBAAuB;MACxC,IAAItY,GAAG,CAACC,OAAO,CAAC8Z,GAAG,CAAC,GAAG,CAAC,EAAE,OAAO/Z,GAAG;MACpC,MAAM+D,CAAC,GAAG/D,GAAG,CAACQ,KAAK,CAAC,IAAI4C,MAAM,CAAC,GAAG2W,GAAG,OAAO,CAAC,CAAC;MAC9C,IAAIC,aAAa,GAAG,IAAIjW,CAAC,CAAC,CAAC,CAAC,EAAE;MAC9B/D,GAAG,GAAG+D,CAAC,CAAC,CAAC,CAAC;MACViW,aAAa,GAAG,IAAI,CAACzJ,WAAW,CAACyJ,aAAa,EAAEJ,aAAa,CAAC;MAC9D,MAAMK,mBAAmB,GAAGD,aAAa,CAACrO,KAAK,CAAC,IAAI,CAAC;MACrD,MAAMuO,mBAAmB,GAAGF,aAAa,CAACrO,KAAK,CAAC,IAAI,CAAC;MACrD,IAAIsO,mBAAmB,IAAIA,mBAAmB,CAACvZ,MAAM,GAAG,CAAC,KAAK,CAAC,IAAI,CAACwZ,mBAAmB,IAAIA,mBAAmB,CAACxZ,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;QAC/HsZ,aAAa,GAAGA,aAAa,CAAC9Z,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;MAClD;MACA,IAAI;QACF0Z,aAAa,GAAGtQ,IAAI,CAACC,KAAK,CAACyQ,aAAa,CAAC;QACzC,IAAIF,gBAAgB,EAAEF,aAAa,GAAG;UACpC,GAAGE,gBAAgB;UACnB,GAAGF;QACL,CAAC;MACH,CAAC,CAAC,OAAOzY,CAAC,EAAE;QACV,IAAI,CAAC4E,MAAM,CAACT,IAAI,CAAC,oDAAoDtF,GAAG,EAAE,EAAEmB,CAAC,CAAC;QAC9E,OAAO,GAAGnB,GAAG,GAAG+Z,GAAG,GAAGC,aAAa,EAAE;MACvC;MACA,IAAIJ,aAAa,CAACtL,YAAY,IAAIsL,aAAa,CAACtL,YAAY,CAACrO,OAAO,CAAC,IAAI,CAAC6F,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO8T,aAAa,CAACtL,YAAY;MACzH,OAAOtO,GAAG;IACZ,CAAC;IACD,OAAO2L,KAAK,GAAG,IAAI,CAACE,aAAa,CAAC4N,IAAI,CAACnX,GAAG,CAAC,EAAE;MAC3C,IAAI6X,UAAU,GAAG,EAAE;MACnBP,aAAa,GAAG;QACd,GAAGhU;MACL,CAAC;MACDgU,aAAa,GAAGA,aAAa,CAAC1Z,OAAO,IAAI,CAACrB,QAAQ,CAAC+a,aAAa,CAAC1Z,OAAO,CAAC,GAAG0Z,aAAa,CAAC1Z,OAAO,GAAG0Z,aAAa;MACjHA,aAAa,CAAC7I,kBAAkB,GAAG,KAAK;MACxC,OAAO6I,aAAa,CAACtL,YAAY;MACjC,IAAI8L,QAAQ,GAAG,KAAK;MACpB,IAAIzO,KAAK,CAAC,CAAC,CAAC,CAAC1L,OAAO,CAAC,IAAI,CAAC8X,eAAe,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC3T,IAAI,CAACuH,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;QAC3E,MAAM3H,CAAC,GAAG2H,KAAK,CAAC,CAAC,CAAC,CAACnL,KAAK,CAAC,IAAI,CAACuX,eAAe,CAAC,CAAC9T,GAAG,CAACoW,IAAI,IAAIA,IAAI,CAACnB,IAAI,CAAC,CAAC,CAAC;QACvEvN,KAAK,CAAC,CAAC,CAAC,GAAG3H,CAAC,CAACV,KAAK,CAAC,CAAC;QACpB6W,UAAU,GAAGnW,CAAC;QACdoW,QAAQ,GAAG,IAAI;MACjB;MACAtY,KAAK,GAAGsS,EAAE,CAACyF,gBAAgB,CAAC/Y,IAAI,CAAC,IAAI,EAAE6K,KAAK,CAAC,CAAC,CAAC,CAACuN,IAAI,CAAC,CAAC,EAAEU,aAAa,CAAC,EAAEA,aAAa,CAAC;MACtF,IAAI9X,KAAK,IAAI6J,KAAK,CAAC,CAAC,CAAC,KAAKrJ,GAAG,IAAI,CAACzD,QAAQ,CAACiD,KAAK,CAAC,EAAE,OAAOA,KAAK;MAC/D,IAAI,CAACjD,QAAQ,CAACiD,KAAK,CAAC,EAAEA,KAAK,GAAGxC,UAAU,CAACwC,KAAK,CAAC;MAC/C,IAAI,CAACA,KAAK,EAAE;QACV,IAAI,CAACiE,MAAM,CAACT,IAAI,CAAC,qBAAqBqG,KAAK,CAAC,CAAC,CAAC,gBAAgBrJ,GAAG,EAAE,CAAC;QACpER,KAAK,GAAG,EAAE;MACZ;MACA,IAAIsY,QAAQ,EAAE;QACZtY,KAAK,GAAGqY,UAAU,CAACG,MAAM,CAAC,CAACpQ,CAAC,EAAEiP,CAAC,KAAK,IAAI,CAAC1B,MAAM,CAACvN,CAAC,EAAEiP,CAAC,EAAEvT,OAAO,CAAC+C,GAAG,EAAE;UACjE,GAAG/C,OAAO;UACVqT,gBAAgB,EAAEtN,KAAK,CAAC,CAAC,CAAC,CAACuN,IAAI,CAAC;QAClC,CAAC,CAAC,EAAEpX,KAAK,CAACoX,IAAI,CAAC,CAAC,CAAC;MACnB;MACA5W,GAAG,GAAGA,GAAG,CAACpC,OAAO,CAACyL,KAAK,CAAC,CAAC,CAAC,EAAE7J,KAAK,CAAC;MAClC,IAAI,CAAC+W,MAAM,CAACD,SAAS,GAAG,CAAC;IAC3B;IACA,OAAOtW,GAAG;EACZ;AACF;AAEA,MAAMiY,cAAc,GAAGC,SAAS,IAAI;EAClC,IAAIC,UAAU,GAAGD,SAAS,CAACnO,WAAW,CAAC,CAAC,CAAC6M,IAAI,CAAC,CAAC;EAC/C,MAAMwB,aAAa,GAAG,CAAC,CAAC;EACxB,IAAIF,SAAS,CAACva,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;IAC/B,MAAMmB,CAAC,GAAGoZ,SAAS,CAACha,KAAK,CAAC,GAAG,CAAC;IAC9Bia,UAAU,GAAGrZ,CAAC,CAAC,CAAC,CAAC,CAACiL,WAAW,CAAC,CAAC,CAAC6M,IAAI,CAAC,CAAC;IACtC,MAAMyB,MAAM,GAAGvZ,CAAC,CAAC,CAAC,CAAC,CAACkD,SAAS,CAAC,CAAC,EAAElD,CAAC,CAAC,CAAC,CAAC,CAACV,MAAM,GAAG,CAAC,CAAC;IACjD,IAAI+Z,UAAU,KAAK,UAAU,IAAIE,MAAM,CAAC1a,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;MACxD,IAAI,CAACya,aAAa,CAACE,QAAQ,EAAEF,aAAa,CAACE,QAAQ,GAAGD,MAAM,CAACzB,IAAI,CAAC,CAAC;IACrE,CAAC,MAAM,IAAIuB,UAAU,KAAK,cAAc,IAAIE,MAAM,CAAC1a,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;MACnE,IAAI,CAACya,aAAa,CAACG,KAAK,EAAEH,aAAa,CAACG,KAAK,GAAGF,MAAM,CAACzB,IAAI,CAAC,CAAC;IAC/D,CAAC,MAAM;MACL,MAAM4B,IAAI,GAAGH,MAAM,CAACna,KAAK,CAAC,GAAG,CAAC;MAC9Bsa,IAAI,CAAClb,OAAO,CAACmb,GAAG,IAAI;QAClB,IAAIA,GAAG,EAAE;UACP,MAAM,CAAC/a,GAAG,EAAE,GAAGgb,IAAI,CAAC,GAAGD,GAAG,CAACva,KAAK,CAAC,GAAG,CAAC;UACrC,MAAM+W,GAAG,GAAGyD,IAAI,CAAC9W,IAAI,CAAC,GAAG,CAAC,CAACgV,IAAI,CAAC,CAAC,CAAChZ,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;UACzD,MAAM+a,UAAU,GAAGjb,GAAG,CAACkZ,IAAI,CAAC,CAAC;UAC7B,IAAI,CAACwB,aAAa,CAACO,UAAU,CAAC,EAAEP,aAAa,CAACO,UAAU,CAAC,GAAG1D,GAAG;UAC/D,IAAIA,GAAG,KAAK,OAAO,EAAEmD,aAAa,CAACO,UAAU,CAAC,GAAG,KAAK;UACtD,IAAI1D,GAAG,KAAK,MAAM,EAAEmD,aAAa,CAACO,UAAU,CAAC,GAAG,IAAI;UACpD,IAAI,CAACC,KAAK,CAAC3D,GAAG,CAAC,EAAEmD,aAAa,CAACO,UAAU,CAAC,GAAGE,QAAQ,CAAC5D,GAAG,EAAE,EAAE,CAAC;QAChE;MACF,CAAC,CAAC;IACJ;EACF;EACA,OAAO;IACLkD,UAAU;IACVC;EACF,CAAC;AACH,CAAC;AACD,MAAMU,qBAAqB,GAAGC,EAAE,IAAI;EAClC,MAAMC,KAAK,GAAG,CAAC,CAAC;EAChB,OAAO,CAAC/D,GAAG,EAAE5O,GAAG,EAAE/C,OAAO,KAAK;IAC5B,IAAI2V,WAAW,GAAG3V,OAAO;IACzB,IAAIA,OAAO,IAAIA,OAAO,CAACqT,gBAAgB,IAAIrT,OAAO,CAAC4V,YAAY,IAAI5V,OAAO,CAAC4V,YAAY,CAAC5V,OAAO,CAACqT,gBAAgB,CAAC,IAAIrT,OAAO,CAACA,OAAO,CAACqT,gBAAgB,CAAC,EAAE;MACtJsC,WAAW,GAAG;QACZ,GAAGA,WAAW;QACd,CAAC3V,OAAO,CAACqT,gBAAgB,GAAG/X;MAC9B,CAAC;IACH;IACA,MAAMlB,GAAG,GAAG2I,GAAG,GAAGW,IAAI,CAACE,SAAS,CAAC+R,WAAW,CAAC;IAC7C,IAAIE,SAAS,GAAGH,KAAK,CAACtb,GAAG,CAAC;IAC1B,IAAI,CAACyb,SAAS,EAAE;MACdA,SAAS,GAAGJ,EAAE,CAACtW,cAAc,CAAC4D,GAAG,CAAC,EAAE/C,OAAO,CAAC;MAC5C0V,KAAK,CAACtb,GAAG,CAAC,GAAGyb,SAAS;IACxB;IACA,OAAOA,SAAS,CAAClE,GAAG,CAAC;EACvB,CAAC;AACH,CAAC;AACD,MAAMmE,SAAS,CAAC;EACdhZ,WAAWA,CAAA,EAAG;IACZ,IAAIkD,OAAO,GAAGpB,SAAS,CAAC9D,MAAM,GAAG,CAAC,IAAI8D,SAAS,CAAC,CAAC,CAAC,KAAKtD,SAAS,GAAGsD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACpF,IAAI,CAACuB,MAAM,GAAGkB,UAAU,CAACH,MAAM,CAAC,WAAW,CAAC;IAC5C,IAAI,CAAClB,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC+V,OAAO,GAAG;MACb/E,MAAM,EAAEwE,qBAAqB,CAAC,CAACzS,GAAG,EAAEoS,GAAG,KAAK;QAC1C,MAAMU,SAAS,GAAG,IAAIxI,IAAI,CAAC2I,YAAY,CAACjT,GAAG,EAAE;UAC3C,GAAGoS;QACL,CAAC,CAAC;QACF,OAAOxD,GAAG,IAAIkE,SAAS,CAAChE,MAAM,CAACF,GAAG,CAAC;MACrC,CAAC,CAAC;MACFqD,QAAQ,EAAEQ,qBAAqB,CAAC,CAACzS,GAAG,EAAEoS,GAAG,KAAK;QAC5C,MAAMU,SAAS,GAAG,IAAIxI,IAAI,CAAC2I,YAAY,CAACjT,GAAG,EAAE;UAC3C,GAAGoS,GAAG;UACNc,KAAK,EAAE;QACT,CAAC,CAAC;QACF,OAAOtE,GAAG,IAAIkE,SAAS,CAAChE,MAAM,CAACF,GAAG,CAAC;MACrC,CAAC,CAAC;MACFuE,QAAQ,EAAEV,qBAAqB,CAAC,CAACzS,GAAG,EAAEoS,GAAG,KAAK;QAC5C,MAAMU,SAAS,GAAG,IAAIxI,IAAI,CAAC8I,cAAc,CAACpT,GAAG,EAAE;UAC7C,GAAGoS;QACL,CAAC,CAAC;QACF,OAAOxD,GAAG,IAAIkE,SAAS,CAAChE,MAAM,CAACF,GAAG,CAAC;MACrC,CAAC,CAAC;MACFyE,YAAY,EAAEZ,qBAAqB,CAAC,CAACzS,GAAG,EAAEoS,GAAG,KAAK;QAChD,MAAMU,SAAS,GAAG,IAAIxI,IAAI,CAACgJ,kBAAkB,CAACtT,GAAG,EAAE;UACjD,GAAGoS;QACL,CAAC,CAAC;QACF,OAAOxD,GAAG,IAAIkE,SAAS,CAAChE,MAAM,CAACF,GAAG,EAAEwD,GAAG,CAACF,KAAK,IAAI,KAAK,CAAC;MACzD,CAAC,CAAC;MACFqB,IAAI,EAAEd,qBAAqB,CAAC,CAACzS,GAAG,EAAEoS,GAAG,KAAK;QACxC,MAAMU,SAAS,GAAG,IAAIxI,IAAI,CAACkJ,UAAU,CAACxT,GAAG,EAAE;UACzC,GAAGoS;QACL,CAAC,CAAC;QACF,OAAOxD,GAAG,IAAIkE,SAAS,CAAChE,MAAM,CAACF,GAAG,CAAC;MACrC,CAAC;IACH,CAAC;IACD,IAAI,CAAC1R,IAAI,CAACD,OAAO,CAAC;EACpB;EACAC,IAAIA,CAACkF,QAAQ,EAAE;IACb,IAAInF,OAAO,GAAGpB,SAAS,CAAC9D,MAAM,GAAG,CAAC,IAAI8D,SAAS,CAAC,CAAC,CAAC,KAAKtD,SAAS,GAAGsD,SAAS,CAAC,CAAC,CAAC,GAAG;MAChF2G,aAAa,EAAE,CAAC;IAClB,CAAC;IACD,IAAI,CAAC4M,eAAe,GAAGnS,OAAO,CAACuF,aAAa,CAAC4M,eAAe,IAAI,GAAG;EACrE;EACAqE,GAAGA,CAAC5R,IAAI,EAAE4J,EAAE,EAAE;IACZ,IAAI,CAACuH,OAAO,CAACnR,IAAI,CAAC6B,WAAW,CAAC,CAAC,CAAC6M,IAAI,CAAC,CAAC,CAAC,GAAG9E,EAAE;EAC9C;EACAiI,SAASA,CAAC7R,IAAI,EAAE4J,EAAE,EAAE;IAClB,IAAI,CAACuH,OAAO,CAACnR,IAAI,CAAC6B,WAAW,CAAC,CAAC,CAAC6M,IAAI,CAAC,CAAC,CAAC,GAAGkC,qBAAqB,CAAChH,EAAE,CAAC;EACrE;EACAqD,MAAMA,CAAC3V,KAAK,EAAE2V,MAAM,EAAE9O,GAAG,EAAE;IACzB,IAAI/C,OAAO,GAAGpB,SAAS,CAAC9D,MAAM,GAAG,CAAC,IAAI8D,SAAS,CAAC,CAAC,CAAC,KAAKtD,SAAS,GAAGsD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACpF,MAAMmX,OAAO,GAAGlE,MAAM,CAACjX,KAAK,CAAC,IAAI,CAACuX,eAAe,CAAC;IAClD,IAAI4D,OAAO,CAACjb,MAAM,GAAG,CAAC,IAAIib,OAAO,CAAC,CAAC,CAAC,CAAC1b,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI0b,OAAO,CAAC,CAAC,CAAC,CAAC1b,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI0b,OAAO,CAAC1R,IAAI,CAACkP,CAAC,IAAIA,CAAC,CAAClZ,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;MAC9H,MAAM2Y,SAAS,GAAG+C,OAAO,CAACW,SAAS,CAACnD,CAAC,IAAIA,CAAC,CAAClZ,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;MAC7D0b,OAAO,CAAC,CAAC,CAAC,GAAG,CAACA,OAAO,CAAC,CAAC,CAAC,EAAE,GAAGA,OAAO,CAAClT,MAAM,CAAC,CAAC,EAAEmQ,SAAS,CAAC,CAAC,CAAC1U,IAAI,CAAC,IAAI,CAAC6T,eAAe,CAAC;IACvF;IACA,MAAMlP,MAAM,GAAG8S,OAAO,CAACrB,MAAM,CAAC,CAACiC,GAAG,EAAEpD,CAAC,KAAK;MACxC,MAAM;QACJsB,UAAU;QACVC;MACF,CAAC,GAAGH,cAAc,CAACpB,CAAC,CAAC;MACrB,IAAI,IAAI,CAACwC,OAAO,CAAClB,UAAU,CAAC,EAAE;QAC5B,IAAI+B,SAAS,GAAGD,GAAG;QACnB,IAAI;UACF,MAAME,UAAU,GAAG7W,OAAO,IAAIA,OAAO,CAAC4V,YAAY,IAAI5V,OAAO,CAAC4V,YAAY,CAAC5V,OAAO,CAACqT,gBAAgB,CAAC,IAAI,CAAC,CAAC;UAC1G,MAAM5J,CAAC,GAAGoN,UAAU,CAACC,MAAM,IAAID,UAAU,CAAC9T,GAAG,IAAI/C,OAAO,CAAC8W,MAAM,IAAI9W,OAAO,CAAC+C,GAAG,IAAIA,GAAG;UACrF6T,SAAS,GAAG,IAAI,CAACb,OAAO,CAAClB,UAAU,CAAC,CAAC8B,GAAG,EAAElN,CAAC,EAAE;YAC3C,GAAGqL,aAAa;YAChB,GAAG9U,OAAO;YACV,GAAG6W;UACL,CAAC,CAAC;QACJ,CAAC,CAAC,OAAOlX,KAAK,EAAE;UACd,IAAI,CAACQ,MAAM,CAACT,IAAI,CAACC,KAAK,CAAC;QACzB;QACA,OAAOiX,SAAS;MAClB,CAAC,MAAM;QACL,IAAI,CAACzW,MAAM,CAACT,IAAI,CAAC,oCAAoCmV,UAAU,EAAE,CAAC;MACpE;MACA,OAAO8B,GAAG;IACZ,CAAC,EAAEza,KAAK,CAAC;IACT,OAAO+G,MAAM;EACf;AACF;AAEA,MAAM8T,aAAa,GAAGA,CAACC,CAAC,EAAEpS,IAAI,KAAK;EACjC,IAAIoS,CAAC,CAACC,OAAO,CAACrS,IAAI,CAAC,KAAKtJ,SAAS,EAAE;IACjC,OAAO0b,CAAC,CAACC,OAAO,CAACrS,IAAI,CAAC;IACtBoS,CAAC,CAACE,YAAY,EAAE;EAClB;AACF,CAAC;AACD,MAAMC,SAAS,SAAS7V,YAAY,CAAC;EACnCxE,WAAWA,CAACsa,OAAO,EAAEC,KAAK,EAAElS,QAAQ,EAAE;IACpC,IAAInF,OAAO,GAAGpB,SAAS,CAAC9D,MAAM,GAAG,CAAC,IAAI8D,SAAS,CAAC,CAAC,CAAC,KAAKtD,SAAS,GAAGsD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACpF,KAAK,CAAC,CAAC;IACP,IAAI,CAACwY,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAClS,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACgE,aAAa,GAAGhE,QAAQ,CAACgE,aAAa;IAC3C,IAAI,CAACnJ,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACG,MAAM,GAAGkB,UAAU,CAACH,MAAM,CAAC,kBAAkB,CAAC;IACnD,IAAI,CAACoW,YAAY,GAAG,EAAE;IACtB,IAAI,CAACC,gBAAgB,GAAGvX,OAAO,CAACuX,gBAAgB,IAAI,EAAE;IACtD,IAAI,CAACC,YAAY,GAAG,CAAC;IACrB,IAAI,CAACC,UAAU,GAAGzX,OAAO,CAACyX,UAAU,IAAI,CAAC,GAAGzX,OAAO,CAACyX,UAAU,GAAG,CAAC;IAClE,IAAI,CAACC,YAAY,GAAG1X,OAAO,CAAC0X,YAAY,IAAI,CAAC,GAAG1X,OAAO,CAAC0X,YAAY,GAAG,GAAG;IAC1E,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;IACf,IAAI,CAACC,KAAK,GAAG,EAAE;IACf,IAAI,IAAI,CAACR,OAAO,IAAI,IAAI,CAACA,OAAO,CAACnX,IAAI,EAAE;MACrC,IAAI,CAACmX,OAAO,CAACnX,IAAI,CAACkF,QAAQ,EAAEnF,OAAO,CAACoX,OAAO,EAAEpX,OAAO,CAAC;IACvD;EACF;EACA6X,SAASA,CAACC,SAAS,EAAEpS,UAAU,EAAE1F,OAAO,EAAE+X,QAAQ,EAAE;IAClD,MAAMC,MAAM,GAAG,CAAC,CAAC;IACjB,MAAMf,OAAO,GAAG,CAAC,CAAC;IAClB,MAAMgB,eAAe,GAAG,CAAC,CAAC;IAC1B,MAAMC,gBAAgB,GAAG,CAAC,CAAC;IAC3BJ,SAAS,CAAC9d,OAAO,CAAC+I,GAAG,IAAI;MACvB,IAAIoV,gBAAgB,GAAG,IAAI;MAC3BzS,UAAU,CAAC1L,OAAO,CAACuI,EAAE,IAAI;QACvB,MAAMqC,IAAI,GAAG,GAAG7B,GAAG,IAAIR,EAAE,EAAE;QAC3B,IAAI,CAACvC,OAAO,CAACoY,MAAM,IAAI,IAAI,CAACf,KAAK,CAACvT,iBAAiB,CAACf,GAAG,EAAER,EAAE,CAAC,EAAE;UAC5D,IAAI,CAACoV,KAAK,CAAC/S,IAAI,CAAC,GAAG,CAAC;QACtB,CAAC,MAAM,IAAI,IAAI,CAAC+S,KAAK,CAAC/S,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,KAAM,IAAI,IAAI,CAAC+S,KAAK,CAAC/S,IAAI,CAAC,KAAK,CAAC,EAAE;UAClE,IAAIqS,OAAO,CAACrS,IAAI,CAAC,KAAKtJ,SAAS,EAAE2b,OAAO,CAACrS,IAAI,CAAC,GAAG,IAAI;QACvD,CAAC,MAAM;UACL,IAAI,CAAC+S,KAAK,CAAC/S,IAAI,CAAC,GAAG,CAAC;UACpBuT,gBAAgB,GAAG,KAAK;UACxB,IAAIlB,OAAO,CAACrS,IAAI,CAAC,KAAKtJ,SAAS,EAAE2b,OAAO,CAACrS,IAAI,CAAC,GAAG,IAAI;UACrD,IAAIoT,MAAM,CAACpT,IAAI,CAAC,KAAKtJ,SAAS,EAAE0c,MAAM,CAACpT,IAAI,CAAC,GAAG,IAAI;UACnD,IAAIsT,gBAAgB,CAAC3V,EAAE,CAAC,KAAKjH,SAAS,EAAE4c,gBAAgB,CAAC3V,EAAE,CAAC,GAAG,IAAI;QACrE;MACF,CAAC,CAAC;MACF,IAAI,CAAC4V,gBAAgB,EAAEF,eAAe,CAAClV,GAAG,CAAC,GAAG,IAAI;IACpD,CAAC,CAAC;IACF,IAAIhI,MAAM,CAACqJ,IAAI,CAAC4T,MAAM,CAAC,CAACld,MAAM,IAAIC,MAAM,CAACqJ,IAAI,CAAC6S,OAAO,CAAC,CAACnc,MAAM,EAAE;MAC7D,IAAI,CAAC8c,KAAK,CAAC/b,IAAI,CAAC;QACdob,OAAO;QACPC,YAAY,EAAEnc,MAAM,CAACqJ,IAAI,CAAC6S,OAAO,CAAC,CAACnc,MAAM;QACzCud,MAAM,EAAE,CAAC,CAAC;QACVC,MAAM,EAAE,EAAE;QACVP;MACF,CAAC,CAAC;IACJ;IACA,OAAO;MACLC,MAAM,EAAEjd,MAAM,CAACqJ,IAAI,CAAC4T,MAAM,CAAC;MAC3Bf,OAAO,EAAElc,MAAM,CAACqJ,IAAI,CAAC6S,OAAO,CAAC;MAC7BgB,eAAe,EAAEld,MAAM,CAACqJ,IAAI,CAAC6T,eAAe,CAAC;MAC7CC,gBAAgB,EAAEnd,MAAM,CAACqJ,IAAI,CAAC8T,gBAAgB;IAChD,CAAC;EACH;EACAG,MAAMA,CAACzT,IAAI,EAAEyL,GAAG,EAAErU,IAAI,EAAE;IACtB,MAAMlC,CAAC,GAAG8K,IAAI,CAAChK,KAAK,CAAC,GAAG,CAAC;IACzB,MAAMmI,GAAG,GAAGjJ,CAAC,CAAC,CAAC,CAAC;IAChB,MAAMyI,EAAE,GAAGzI,CAAC,CAAC,CAAC,CAAC;IACf,IAAIuW,GAAG,EAAE,IAAI,CAACvO,IAAI,CAAC,eAAe,EAAEiB,GAAG,EAAER,EAAE,EAAE8N,GAAG,CAAC;IACjD,IAAI,CAACA,GAAG,IAAIrU,IAAI,EAAE;MAChB,IAAI,CAACqb,KAAK,CAAC/T,iBAAiB,CAACP,GAAG,EAAER,EAAE,EAAEvG,IAAI,EAAEV,SAAS,EAAEA,SAAS,EAAE;QAChEkI,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;IACA,IAAI,CAACmU,KAAK,CAAC/S,IAAI,CAAC,GAAGyL,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC;IAC/B,IAAIA,GAAG,IAAIrU,IAAI,EAAE,IAAI,CAAC2b,KAAK,CAAC/S,IAAI,CAAC,GAAG,CAAC;IACrC,MAAMyT,MAAM,GAAG,CAAC,CAAC;IACjB,IAAI,CAACT,KAAK,CAAC5d,OAAO,CAACgd,CAAC,IAAI;MACtBrb,QAAQ,CAACqb,CAAC,CAACqB,MAAM,EAAE,CAACtV,GAAG,CAAC,EAAER,EAAE,CAAC;MAC7BwU,aAAa,CAACC,CAAC,EAAEpS,IAAI,CAAC;MACtB,IAAIyL,GAAG,EAAE2G,CAAC,CAACsB,MAAM,CAACzc,IAAI,CAACwU,GAAG,CAAC;MAC3B,IAAI2G,CAAC,CAACE,YAAY,KAAK,CAAC,IAAI,CAACF,CAAC,CAACuB,IAAI,EAAE;QACnCxd,MAAM,CAACqJ,IAAI,CAAC4S,CAAC,CAACqB,MAAM,CAAC,CAACre,OAAO,CAACyP,CAAC,IAAI;UACjC,IAAI,CAAC4O,MAAM,CAAC5O,CAAC,CAAC,EAAE4O,MAAM,CAAC5O,CAAC,CAAC,GAAG,CAAC,CAAC;UAC9B,MAAM+O,UAAU,GAAGxB,CAAC,CAACqB,MAAM,CAAC5O,CAAC,CAAC;UAC9B,IAAI+O,UAAU,CAAC1d,MAAM,EAAE;YACrB0d,UAAU,CAACxe,OAAO,CAACmK,CAAC,IAAI;cACtB,IAAIkU,MAAM,CAAC5O,CAAC,CAAC,CAACtF,CAAC,CAAC,KAAK7I,SAAS,EAAE+c,MAAM,CAAC5O,CAAC,CAAC,CAACtF,CAAC,CAAC,GAAG,IAAI;YACrD,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;QACF6S,CAAC,CAACuB,IAAI,GAAG,IAAI;QACb,IAAIvB,CAAC,CAACsB,MAAM,CAACxd,MAAM,EAAE;UACnBkc,CAAC,CAACe,QAAQ,CAACf,CAAC,CAACsB,MAAM,CAAC;QACtB,CAAC,MAAM;UACLtB,CAAC,CAACe,QAAQ,CAAC,CAAC;QACd;MACF;IACF,CAAC,CAAC;IACF,IAAI,CAACjW,IAAI,CAAC,QAAQ,EAAEuW,MAAM,CAAC;IAC3B,IAAI,CAACT,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC1Z,MAAM,CAAC8Y,CAAC,IAAI,CAACA,CAAC,CAACuB,IAAI,CAAC;EAC9C;EACAE,IAAIA,CAAC1V,GAAG,EAAER,EAAE,EAAEmW,MAAM,EAAE;IACpB,IAAIC,KAAK,GAAG/Z,SAAS,CAAC9D,MAAM,GAAG,CAAC,IAAI8D,SAAS,CAAC,CAAC,CAAC,KAAKtD,SAAS,GAAGsD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IACjF,IAAIga,IAAI,GAAGha,SAAS,CAAC9D,MAAM,GAAG,CAAC,IAAI8D,SAAS,CAAC,CAAC,CAAC,KAAKtD,SAAS,GAAGsD,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC8Y,YAAY;IAChG,IAAIK,QAAQ,GAAGnZ,SAAS,CAAC9D,MAAM,GAAG,CAAC,GAAG8D,SAAS,CAAC,CAAC,CAAC,GAAGtD,SAAS;IAC9D,IAAI,CAACyH,GAAG,CAACjI,MAAM,EAAE,OAAOid,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;IAC1C,IAAI,IAAI,CAACP,YAAY,IAAI,IAAI,CAACD,gBAAgB,EAAE;MAC9C,IAAI,CAACD,YAAY,CAACzb,IAAI,CAAC;QACrBkH,GAAG;QACHR,EAAE;QACFmW,MAAM;QACNC,KAAK;QACLC,IAAI;QACJb;MACF,CAAC,CAAC;MACF;IACF;IACA,IAAI,CAACP,YAAY,EAAE;IACnB,MAAMqB,QAAQ,GAAGA,CAACxI,GAAG,EAAErU,IAAI,KAAK;MAC9B,IAAI,CAACwb,YAAY,EAAE;MACnB,IAAI,IAAI,CAACF,YAAY,CAACxc,MAAM,GAAG,CAAC,EAAE;QAChC,MAAMkE,IAAI,GAAG,IAAI,CAACsY,YAAY,CAAC5Z,KAAK,CAAC,CAAC;QACtC,IAAI,CAAC+a,IAAI,CAACzZ,IAAI,CAAC+D,GAAG,EAAE/D,IAAI,CAACuD,EAAE,EAAEvD,IAAI,CAAC0Z,MAAM,EAAE1Z,IAAI,CAAC2Z,KAAK,EAAE3Z,IAAI,CAAC4Z,IAAI,EAAE5Z,IAAI,CAAC+Y,QAAQ,CAAC;MACjF;MACA,IAAI1H,GAAG,IAAIrU,IAAI,IAAI2c,KAAK,GAAG,IAAI,CAAClB,UAAU,EAAE;QAC1CqB,UAAU,CAAC,MAAM;UACf,IAAI,CAACL,IAAI,CAACvd,IAAI,CAAC,IAAI,EAAE6H,GAAG,EAAER,EAAE,EAAEmW,MAAM,EAAEC,KAAK,GAAG,CAAC,EAAEC,IAAI,GAAG,CAAC,EAAEb,QAAQ,CAAC;QACtE,CAAC,EAAEa,IAAI,CAAC;QACR;MACF;MACAb,QAAQ,CAAC1H,GAAG,EAAErU,IAAI,CAAC;IACrB,CAAC;IACD,MAAMwS,EAAE,GAAG,IAAI,CAAC4I,OAAO,CAACsB,MAAM,CAAC,CAACK,IAAI,CAAC,IAAI,CAAC3B,OAAO,CAAC;IAClD,IAAI5I,EAAE,CAAC1T,MAAM,KAAK,CAAC,EAAE;MACnB,IAAI;QACF,MAAMsD,CAAC,GAAGoQ,EAAE,CAACzL,GAAG,EAAER,EAAE,CAAC;QACrB,IAAInE,CAAC,IAAI,OAAOA,CAAC,CAAC4a,IAAI,KAAK,UAAU,EAAE;UACrC5a,CAAC,CAAC4a,IAAI,CAAChd,IAAI,IAAI6c,QAAQ,CAAC,IAAI,EAAE7c,IAAI,CAAC,CAAC,CAACid,KAAK,CAACJ,QAAQ,CAAC;QACtD,CAAC,MAAM;UACLA,QAAQ,CAAC,IAAI,EAAEza,CAAC,CAAC;QACnB;MACF,CAAC,CAAC,OAAOiS,GAAG,EAAE;QACZwI,QAAQ,CAACxI,GAAG,CAAC;MACf;MACA;IACF;IACA,OAAO7B,EAAE,CAACzL,GAAG,EAAER,EAAE,EAAEsW,QAAQ,CAAC;EAC9B;EACAK,cAAcA,CAACpB,SAAS,EAAEpS,UAAU,EAAE;IACpC,IAAI1F,OAAO,GAAGpB,SAAS,CAAC9D,MAAM,GAAG,CAAC,IAAI8D,SAAS,CAAC,CAAC,CAAC,KAAKtD,SAAS,GAAGsD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACpF,IAAImZ,QAAQ,GAAGnZ,SAAS,CAAC9D,MAAM,GAAG,CAAC,GAAG8D,SAAS,CAAC,CAAC,CAAC,GAAGtD,SAAS;IAC9D,IAAI,CAAC,IAAI,CAAC8b,OAAO,EAAE;MACjB,IAAI,CAACjX,MAAM,CAACT,IAAI,CAAC,gEAAgE,CAAC;MAClF,OAAOqY,QAAQ,IAAIA,QAAQ,CAAC,CAAC;IAC/B;IACA,IAAI9e,QAAQ,CAAC6e,SAAS,CAAC,EAAEA,SAAS,GAAG,IAAI,CAAC3O,aAAa,CAACI,kBAAkB,CAACuO,SAAS,CAAC;IACrF,IAAI7e,QAAQ,CAACyM,UAAU,CAAC,EAAEA,UAAU,GAAG,CAACA,UAAU,CAAC;IACnD,MAAMsS,MAAM,GAAG,IAAI,CAACH,SAAS,CAACC,SAAS,EAAEpS,UAAU,EAAE1F,OAAO,EAAE+X,QAAQ,CAAC;IACvE,IAAI,CAACC,MAAM,CAACA,MAAM,CAACld,MAAM,EAAE;MACzB,IAAI,CAACkd,MAAM,CAACf,OAAO,CAACnc,MAAM,EAAEid,QAAQ,CAAC,CAAC;MACtC,OAAO,IAAI;IACb;IACAC,MAAM,CAACA,MAAM,CAAChe,OAAO,CAAC4K,IAAI,IAAI;MAC5B,IAAI,CAACuU,OAAO,CAACvU,IAAI,CAAC;IACpB,CAAC,CAAC;EACJ;EACAiJ,IAAIA,CAACiK,SAAS,EAAEpS,UAAU,EAAEqS,QAAQ,EAAE;IACpC,IAAI,CAACmB,cAAc,CAACpB,SAAS,EAAEpS,UAAU,EAAE,CAAC,CAAC,EAAEqS,QAAQ,CAAC;EAC1D;EACAK,MAAMA,CAACN,SAAS,EAAEpS,UAAU,EAAEqS,QAAQ,EAAE;IACtC,IAAI,CAACmB,cAAc,CAACpB,SAAS,EAAEpS,UAAU,EAAE;MACzC0S,MAAM,EAAE;IACV,CAAC,EAAEL,QAAQ,CAAC;EACd;EACAoB,OAAOA,CAACvU,IAAI,EAAE;IACZ,IAAI1E,MAAM,GAAGtB,SAAS,CAAC9D,MAAM,GAAG,CAAC,IAAI8D,SAAS,CAAC,CAAC,CAAC,KAAKtD,SAAS,GAAGsD,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;IACnF,MAAM9E,CAAC,GAAG8K,IAAI,CAAChK,KAAK,CAAC,GAAG,CAAC;IACzB,MAAMmI,GAAG,GAAGjJ,CAAC,CAAC,CAAC,CAAC;IAChB,MAAMyI,EAAE,GAAGzI,CAAC,CAAC,CAAC,CAAC;IACf,IAAI,CAAC2e,IAAI,CAAC1V,GAAG,EAAER,EAAE,EAAE,MAAM,EAAEjH,SAAS,EAAEA,SAAS,EAAE,CAAC+U,GAAG,EAAErU,IAAI,KAAK;MAC9D,IAAIqU,GAAG,EAAE,IAAI,CAAClQ,MAAM,CAACT,IAAI,CAAC,GAAGQ,MAAM,qBAAqBqC,EAAE,iBAAiBQ,GAAG,SAAS,EAAEsN,GAAG,CAAC;MAC7F,IAAI,CAACA,GAAG,IAAIrU,IAAI,EAAE,IAAI,CAACmE,MAAM,CAACZ,GAAG,CAAC,GAAGW,MAAM,oBAAoBqC,EAAE,iBAAiBQ,GAAG,EAAE,EAAE/G,IAAI,CAAC;MAC9F,IAAI,CAACqc,MAAM,CAACzT,IAAI,EAAEyL,GAAG,EAAErU,IAAI,CAAC;IAC9B,CAAC,CAAC;EACJ;EACA8N,WAAWA,CAACgO,SAAS,EAAEvR,SAAS,EAAEnM,GAAG,EAAEgf,aAAa,EAAEC,QAAQ,EAAE;IAC9D,IAAIrZ,OAAO,GAAGpB,SAAS,CAAC9D,MAAM,GAAG,CAAC,IAAI8D,SAAS,CAAC,CAAC,CAAC,KAAKtD,SAAS,GAAGsD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACpF,IAAI0a,GAAG,GAAG1a,SAAS,CAAC9D,MAAM,GAAG,CAAC,IAAI8D,SAAS,CAAC,CAAC,CAAC,KAAKtD,SAAS,GAAGsD,SAAS,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;IACtF,IAAI,IAAI,CAACuG,QAAQ,CAACwG,KAAK,IAAI,IAAI,CAACxG,QAAQ,CAACwG,KAAK,CAACC,kBAAkB,IAAI,CAAC,IAAI,CAACzG,QAAQ,CAACwG,KAAK,CAACC,kBAAkB,CAACrF,SAAS,CAAC,EAAE;MACvH,IAAI,CAACpG,MAAM,CAACT,IAAI,CAAC,qBAAqBtF,GAAG,uBAAuBmM,SAAS,sBAAsB,EAAE,0NAA0N,CAAC;MAC5T;IACF;IACA,IAAInM,GAAG,KAAKkB,SAAS,IAAIlB,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,EAAE,EAAE;IACrD,IAAI,IAAI,CAACgd,OAAO,IAAI,IAAI,CAACA,OAAO,CAAClW,MAAM,EAAE;MACvC,MAAMgU,IAAI,GAAG;QACX,GAAGlV,OAAO;QACVqZ;MACF,CAAC;MACD,MAAM7K,EAAE,GAAG,IAAI,CAAC4I,OAAO,CAAClW,MAAM,CAAC6X,IAAI,CAAC,IAAI,CAAC3B,OAAO,CAAC;MACjD,IAAI5I,EAAE,CAAC1T,MAAM,GAAG,CAAC,EAAE;QACjB,IAAI;UACF,IAAIsD,CAAC;UACL,IAAIoQ,EAAE,CAAC1T,MAAM,KAAK,CAAC,EAAE;YACnBsD,CAAC,GAAGoQ,EAAE,CAACsJ,SAAS,EAAEvR,SAAS,EAAEnM,GAAG,EAAEgf,aAAa,EAAElE,IAAI,CAAC;UACxD,CAAC,MAAM;YACL9W,CAAC,GAAGoQ,EAAE,CAACsJ,SAAS,EAAEvR,SAAS,EAAEnM,GAAG,EAAEgf,aAAa,CAAC;UAClD;UACA,IAAIhb,CAAC,IAAI,OAAOA,CAAC,CAAC4a,IAAI,KAAK,UAAU,EAAE;YACrC5a,CAAC,CAAC4a,IAAI,CAAChd,IAAI,IAAIsd,GAAG,CAAC,IAAI,EAAEtd,IAAI,CAAC,CAAC,CAACid,KAAK,CAACK,GAAG,CAAC;UAC5C,CAAC,MAAM;YACLA,GAAG,CAAC,IAAI,EAAElb,CAAC,CAAC;UACd;QACF,CAAC,CAAC,OAAOiS,GAAG,EAAE;UACZiJ,GAAG,CAACjJ,GAAG,CAAC;QACV;MACF,CAAC,MAAM;QACL7B,EAAE,CAACsJ,SAAS,EAAEvR,SAAS,EAAEnM,GAAG,EAAEgf,aAAa,EAAEE,GAAG,EAAEpE,IAAI,CAAC;MACzD;IACF;IACA,IAAI,CAAC4C,SAAS,IAAI,CAACA,SAAS,CAAC,CAAC,CAAC,EAAE;IACjC,IAAI,CAACT,KAAK,CAACnU,WAAW,CAAC4U,SAAS,CAAC,CAAC,CAAC,EAAEvR,SAAS,EAAEnM,GAAG,EAAEgf,aAAa,CAAC;EACrE;AACF;AAEA,MAAM9b,GAAG,GAAGA,CAAA,MAAO;EACjB8C,KAAK,EAAE,KAAK;EACZmZ,aAAa,EAAE,IAAI;EACnBhX,EAAE,EAAE,CAAC,aAAa,CAAC;EACnBC,SAAS,EAAE,CAAC,aAAa,CAAC;EAC1B6G,WAAW,EAAE,CAAC,KAAK,CAAC;EACpBmC,UAAU,EAAE,KAAK;EACjByB,aAAa,EAAE,KAAK;EACpBa,wBAAwB,EAAE,KAAK;EAC/BD,IAAI,EAAE,KAAK;EACX2L,OAAO,EAAE,KAAK;EACdjI,oBAAoB,EAAE,IAAI;EAC1BvT,YAAY,EAAE,GAAG;EACjBD,WAAW,EAAE,GAAG;EAChB4K,eAAe,EAAE,GAAG;EACpBwD,gBAAgB,EAAE,GAAG;EACrBsN,uBAAuB,EAAE,KAAK;EAC9B3P,WAAW,EAAE,KAAK;EAClBf,aAAa,EAAE,KAAK;EACpBO,aAAa,EAAE,UAAU;EACzBS,kBAAkB,EAAE,IAAI;EACxBH,iBAAiB,EAAE,KAAK;EACxB4J,2BAA2B,EAAE,KAAK;EAClCvI,WAAW,EAAE,KAAK;EAClBG,uBAAuB,EAAE,KAAK;EAC9BkB,UAAU,EAAE,KAAK;EACjBC,iBAAiB,EAAE,IAAI;EACvB9E,aAAa,EAAE,KAAK;EACpBJ,UAAU,EAAE,KAAK;EACjBK,qBAAqB,EAAE,KAAK;EAC5B0C,sBAAsB,EAAE,KAAK;EAC7BD,2BAA2B,EAAE,KAAK;EAClC3D,uBAAuB,EAAE,KAAK;EAC9BH,gCAAgC,EAAE7G,IAAI,IAAI;IACxC,IAAIka,GAAG,GAAG,CAAC,CAAC;IACZ,IAAI,OAAOla,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAEka,GAAG,GAAGla,IAAI,CAAC,CAAC,CAAC;IAC9C,IAAIvG,QAAQ,CAACuG,IAAI,CAAC,CAAC,CAAC,CAAC,EAAEka,GAAG,CAAChR,YAAY,GAAGlJ,IAAI,CAAC,CAAC,CAAC;IACjD,IAAIvG,QAAQ,CAACuG,IAAI,CAAC,CAAC,CAAC,CAAC,EAAEka,GAAG,CAACC,YAAY,GAAGna,IAAI,CAAC,CAAC,CAAC;IACjD,IAAI,OAAOA,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,IAAI,OAAOA,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;MAC9D,MAAMQ,OAAO,GAAGR,IAAI,CAAC,CAAC,CAAC,IAAIA,IAAI,CAAC,CAAC,CAAC;MAClCzE,MAAM,CAACqJ,IAAI,CAACpE,OAAO,CAAC,CAAChG,OAAO,CAACI,GAAG,IAAI;QAClCsf,GAAG,CAACtf,GAAG,CAAC,GAAG4F,OAAO,CAAC5F,GAAG,CAAC;MACzB,CAAC,CAAC;IACJ;IACA,OAAOsf,GAAG;EACZ,CAAC;EACDnU,aAAa,EAAE;IACbuM,WAAW,EAAE,IAAI;IACjBD,MAAM,EAAE3V,KAAK,IAAIA,KAAK;IACtBgE,MAAM,EAAE,IAAI;IACZgK,MAAM,EAAE,IAAI;IACZiI,eAAe,EAAE,GAAG;IACpBE,cAAc,EAAE,GAAG;IACnBC,aAAa,EAAE,KAAK;IACpBE,aAAa,EAAE,GAAG;IAClBE,uBAAuB,EAAE,GAAG;IAC5BC,WAAW,EAAE,IAAI;IACjBnI,eAAe,EAAE;EACnB;AACF,CAAC,CAAC;AACF,MAAMoP,gBAAgB,GAAG5Z,OAAO,IAAI;EAClC,IAAI/G,QAAQ,CAAC+G,OAAO,CAACuC,EAAE,CAAC,EAAEvC,OAAO,CAACuC,EAAE,GAAG,CAACvC,OAAO,CAACuC,EAAE,CAAC;EACnD,IAAItJ,QAAQ,CAAC+G,OAAO,CAACqJ,WAAW,CAAC,EAAErJ,OAAO,CAACqJ,WAAW,GAAG,CAACrJ,OAAO,CAACqJ,WAAW,CAAC;EAC9E,IAAIpQ,QAAQ,CAAC+G,OAAO,CAACwL,UAAU,CAAC,EAAExL,OAAO,CAACwL,UAAU,GAAG,CAACxL,OAAO,CAACwL,UAAU,CAAC;EAC3E,IAAIxL,OAAO,CAACiN,aAAa,IAAIjN,OAAO,CAACiN,aAAa,CAAC5S,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;IACxE2F,OAAO,CAACiN,aAAa,GAAGjN,OAAO,CAACiN,aAAa,CAACrR,MAAM,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClE;EACA,OAAOoE,OAAO;AAChB,CAAC;AAED,MAAM6Z,IAAI,GAAGA,CAAA,KAAM,CAAC,CAAC;AACrB,MAAMC,mBAAmB,GAAGC,IAAI,IAAI;EAClC,MAAMC,IAAI,GAAGjf,MAAM,CAACkf,mBAAmB,CAAClf,MAAM,CAACmf,cAAc,CAACH,IAAI,CAAC,CAAC;EACpEC,IAAI,CAAChgB,OAAO,CAAC2c,GAAG,IAAI;IAClB,IAAI,OAAOoD,IAAI,CAACpD,GAAG,CAAC,KAAK,UAAU,EAAE;MACnCoD,IAAI,CAACpD,GAAG,CAAC,GAAGoD,IAAI,CAACpD,GAAG,CAAC,CAACoC,IAAI,CAACgB,IAAI,CAAC;IAClC;EACF,CAAC,CAAC;AACJ,CAAC;AACD,MAAMI,IAAI,SAAS7Y,YAAY,CAAC;EAC9BxE,WAAWA,CAAA,EAAG;IACZ,IAAIkD,OAAO,GAAGpB,SAAS,CAAC9D,MAAM,GAAG,CAAC,IAAI8D,SAAS,CAAC,CAAC,CAAC,KAAKtD,SAAS,GAAGsD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACpF,IAAImZ,QAAQ,GAAGnZ,SAAS,CAAC9D,MAAM,GAAG,CAAC,GAAG8D,SAAS,CAAC,CAAC,CAAC,GAAGtD,SAAS;IAC9D,KAAK,CAAC,CAAC;IACP,IAAI,CAAC0E,OAAO,GAAG4Z,gBAAgB,CAAC5Z,OAAO,CAAC;IACxC,IAAI,CAACmF,QAAQ,GAAG,CAAC,CAAC;IAClB,IAAI,CAAChF,MAAM,GAAGkB,UAAU;IACxB,IAAI,CAAC+Y,OAAO,GAAG;MACbC,QAAQ,EAAE;IACZ,CAAC;IACDP,mBAAmB,CAAC,IAAI,CAAC;IACzB,IAAI/B,QAAQ,IAAI,CAAC,IAAI,CAACuC,aAAa,IAAI,CAACta,OAAO,CAACua,OAAO,EAAE;MACvD,IAAI,CAAC,IAAI,CAACva,OAAO,CAACuZ,aAAa,EAAE;QAC/B,IAAI,CAACtZ,IAAI,CAACD,OAAO,EAAE+X,QAAQ,CAAC;QAC5B,OAAO,IAAI;MACb;MACAe,UAAU,CAAC,MAAM;QACf,IAAI,CAAC7Y,IAAI,CAACD,OAAO,EAAE+X,QAAQ,CAAC;MAC9B,CAAC,EAAE,CAAC,CAAC;IACP;EACF;EACA9X,IAAIA,CAAA,EAAG;IACL,IAAIoK,KAAK,GAAG,IAAI;IAChB,IAAIrK,OAAO,GAAGpB,SAAS,CAAC9D,MAAM,GAAG,CAAC,IAAI8D,SAAS,CAAC,CAAC,CAAC,KAAKtD,SAAS,GAAGsD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACpF,IAAImZ,QAAQ,GAAGnZ,SAAS,CAAC9D,MAAM,GAAG,CAAC,GAAG8D,SAAS,CAAC,CAAC,CAAC,GAAGtD,SAAS;IAC9D,IAAI,CAACkf,cAAc,GAAG,IAAI;IAC1B,IAAI,OAAOxa,OAAO,KAAK,UAAU,EAAE;MACjC+X,QAAQ,GAAG/X,OAAO;MAClBA,OAAO,GAAG,CAAC,CAAC;IACd;IACA,IAAI,CAACA,OAAO,CAACwC,SAAS,IAAIxC,OAAO,CAACwC,SAAS,KAAK,KAAK,IAAIxC,OAAO,CAACuC,EAAE,EAAE;MACnE,IAAItJ,QAAQ,CAAC+G,OAAO,CAACuC,EAAE,CAAC,EAAE;QACxBvC,OAAO,CAACwC,SAAS,GAAGxC,OAAO,CAACuC,EAAE;MAChC,CAAC,MAAM,IAAIvC,OAAO,CAACuC,EAAE,CAAClI,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE;QAChD2F,OAAO,CAACwC,SAAS,GAAGxC,OAAO,CAACuC,EAAE,CAAC,CAAC,CAAC;MACnC;IACF;IACA,MAAMkY,OAAO,GAAGnd,GAAG,CAAC,CAAC;IACrB,IAAI,CAAC0C,OAAO,GAAG;MACb,GAAGya,OAAO;MACV,GAAG,IAAI,CAACza,OAAO;MACf,GAAG4Z,gBAAgB,CAAC5Z,OAAO;IAC7B,CAAC;IACD,IAAI,IAAI,CAACA,OAAO,CAACgE,gBAAgB,KAAK,IAAI,EAAE;MAC1C,IAAI,CAAChE,OAAO,CAACuF,aAAa,GAAG;QAC3B,GAAGkV,OAAO,CAAClV,aAAa;QACxB,GAAG,IAAI,CAACvF,OAAO,CAACuF;MAClB,CAAC;IACH;IACA,IAAIvF,OAAO,CAAChC,YAAY,KAAK1C,SAAS,EAAE;MACtC,IAAI,CAAC0E,OAAO,CAAC6F,uBAAuB,GAAG7F,OAAO,CAAChC,YAAY;IAC7D;IACA,IAAIgC,OAAO,CAACjC,WAAW,KAAKzC,SAAS,EAAE;MACrC,IAAI,CAAC0E,OAAO,CAAC8F,sBAAsB,GAAG9F,OAAO,CAACjC,WAAW;IAC3D;IACA,MAAM2c,mBAAmB,GAAGC,aAAa,IAAI;MAC3C,IAAI,CAACA,aAAa,EAAE,OAAO,IAAI;MAC/B,IAAI,OAAOA,aAAa,KAAK,UAAU,EAAE,OAAO,IAAIA,aAAa,CAAC,CAAC;MACnE,OAAOA,aAAa;IACtB,CAAC;IACD,IAAI,CAAC,IAAI,CAAC3a,OAAO,CAACua,OAAO,EAAE;MACzB,IAAI,IAAI,CAACH,OAAO,CAACja,MAAM,EAAE;QACvBkB,UAAU,CAACpB,IAAI,CAACya,mBAAmB,CAAC,IAAI,CAACN,OAAO,CAACja,MAAM,CAAC,EAAE,IAAI,CAACH,OAAO,CAAC;MACzE,CAAC,MAAM;QACLqB,UAAU,CAACpB,IAAI,CAAC,IAAI,EAAE,IAAI,CAACD,OAAO,CAAC;MACrC;MACA,IAAI6V,SAAS;MACb,IAAI,IAAI,CAACuE,OAAO,CAACvE,SAAS,EAAE;QAC1BA,SAAS,GAAG,IAAI,CAACuE,OAAO,CAACvE,SAAS;MACpC,CAAC,MAAM,IAAI,OAAOxI,IAAI,KAAK,WAAW,EAAE;QACtCwI,SAAS,GAAGC,SAAS;MACvB;MACA,MAAM8E,EAAE,GAAG,IAAI5N,YAAY,CAAC,IAAI,CAAChN,OAAO,CAAC;MACzC,IAAI,CAACqX,KAAK,GAAG,IAAI/U,aAAa,CAAC,IAAI,CAACtC,OAAO,CAACqD,SAAS,EAAE,IAAI,CAACrD,OAAO,CAAC;MACpE,MAAMlG,CAAC,GAAG,IAAI,CAACqL,QAAQ;MACvBrL,CAAC,CAACqG,MAAM,GAAGkB,UAAU;MACrBvH,CAAC,CAAC0S,aAAa,GAAG,IAAI,CAAC6K,KAAK;MAC5Bvd,CAAC,CAACqP,aAAa,GAAGyR,EAAE;MACpB9gB,CAAC,CAACsO,cAAc,GAAG,IAAIsH,cAAc,CAACkL,EAAE,EAAE;QACxC7J,OAAO,EAAE,IAAI,CAAC/Q,OAAO,CAAC2I,eAAe;QACrCgH,iBAAiB,EAAE,IAAI,CAAC3P,OAAO,CAAC2P,iBAAiB;QACjD4B,oBAAoB,EAAE,IAAI,CAACvR,OAAO,CAACuR;MACrC,CAAC,CAAC;MACF,IAAIsE,SAAS,KAAK,CAAC,IAAI,CAAC7V,OAAO,CAACuF,aAAa,CAACsM,MAAM,IAAI,IAAI,CAAC7R,OAAO,CAACuF,aAAa,CAACsM,MAAM,KAAK4I,OAAO,CAAClV,aAAa,CAACsM,MAAM,CAAC,EAAE;QAC3H/X,CAAC,CAAC+b,SAAS,GAAG6E,mBAAmB,CAAC7E,SAAS,CAAC;QAC5C/b,CAAC,CAAC+b,SAAS,CAAC5V,IAAI,CAACnG,CAAC,EAAE,IAAI,CAACkG,OAAO,CAAC;QACjC,IAAI,CAACA,OAAO,CAACuF,aAAa,CAACsM,MAAM,GAAG/X,CAAC,CAAC+b,SAAS,CAAChE,MAAM,CAACkH,IAAI,CAACjf,CAAC,CAAC+b,SAAS,CAAC;MAC1E;MACA/b,CAAC,CAACkM,YAAY,GAAG,IAAI4L,YAAY,CAAC,IAAI,CAAC5R,OAAO,CAAC;MAC/ClG,CAAC,CAAC6R,KAAK,GAAG;QACRC,kBAAkB,EAAE,IAAI,CAACA,kBAAkB,CAACmN,IAAI,CAAC,IAAI;MACvD,CAAC;MACDjf,CAAC,CAAC+P,gBAAgB,GAAG,IAAIsN,SAAS,CAACuD,mBAAmB,CAAC,IAAI,CAACN,OAAO,CAAChD,OAAO,CAAC,EAAEtd,CAAC,CAAC0S,aAAa,EAAE1S,CAAC,EAAE,IAAI,CAACkG,OAAO,CAAC;MAC/GlG,CAAC,CAAC+P,gBAAgB,CAACrI,EAAE,CAAC,GAAG,EAAE,UAAUG,KAAK,EAAE;QAC1C,KAAK,IAAItB,IAAI,GAAGzB,SAAS,CAAC9D,MAAM,EAAE0E,IAAI,GAAG,IAAIc,KAAK,CAACD,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEE,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGF,IAAI,EAAEE,IAAI,EAAE,EAAE;UAC1Gf,IAAI,CAACe,IAAI,GAAG,CAAC,CAAC,GAAG3B,SAAS,CAAC2B,IAAI,CAAC;QAClC;QACA8J,KAAK,CAACvI,IAAI,CAACH,KAAK,EAAE,GAAGnC,IAAI,CAAC;MAC5B,CAAC,CAAC;MACF,IAAI,IAAI,CAAC4a,OAAO,CAACS,gBAAgB,EAAE;QACjC/gB,CAAC,CAAC+gB,gBAAgB,GAAGH,mBAAmB,CAAC,IAAI,CAACN,OAAO,CAACS,gBAAgB,CAAC;QACvE,IAAI/gB,CAAC,CAAC+gB,gBAAgB,CAAC5a,IAAI,EAAEnG,CAAC,CAAC+gB,gBAAgB,CAAC5a,IAAI,CAACnG,CAAC,EAAE,IAAI,CAACkG,OAAO,CAAC8a,SAAS,EAAE,IAAI,CAAC9a,OAAO,CAAC;MAC/F;MACA,IAAI,IAAI,CAACoa,OAAO,CAAC7S,UAAU,EAAE;QAC3BzN,CAAC,CAACyN,UAAU,GAAGmT,mBAAmB,CAAC,IAAI,CAACN,OAAO,CAAC7S,UAAU,CAAC;QAC3D,IAAIzN,CAAC,CAACyN,UAAU,CAACtH,IAAI,EAAEnG,CAAC,CAACyN,UAAU,CAACtH,IAAI,CAAC,IAAI,CAAC;MAChD;MACA,IAAI,CAAC6E,UAAU,GAAG,IAAII,UAAU,CAAC,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACnF,OAAO,CAAC;MAC7D,IAAI,CAAC8E,UAAU,CAACtD,EAAE,CAAC,GAAG,EAAE,UAAUG,KAAK,EAAE;QACvC,KAAK,IAAIlB,KAAK,GAAG7B,SAAS,CAAC9D,MAAM,EAAE0E,IAAI,GAAG,IAAIc,KAAK,CAACG,KAAK,GAAG,CAAC,GAAGA,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;UACjHlB,IAAI,CAACkB,KAAK,GAAG,CAAC,CAAC,GAAG9B,SAAS,CAAC8B,KAAK,CAAC;QACpC;QACA2J,KAAK,CAACvI,IAAI,CAACH,KAAK,EAAE,GAAGnC,IAAI,CAAC;MAC5B,CAAC,CAAC;MACF,IAAI,CAAC4a,OAAO,CAACC,QAAQ,CAACrgB,OAAO,CAACC,CAAC,IAAI;QACjC,IAAIA,CAAC,CAACgG,IAAI,EAAEhG,CAAC,CAACgG,IAAI,CAAC,IAAI,CAAC;MAC1B,CAAC,CAAC;IACJ;IACA,IAAI,CAAC4R,MAAM,GAAG,IAAI,CAAC7R,OAAO,CAACuF,aAAa,CAACsM,MAAM;IAC/C,IAAI,CAACkG,QAAQ,EAAEA,QAAQ,GAAG8B,IAAI;IAC9B,IAAI,IAAI,CAAC7Z,OAAO,CAACqJ,WAAW,IAAI,CAAC,IAAI,CAAClE,QAAQ,CAAC0V,gBAAgB,IAAI,CAAC,IAAI,CAAC7a,OAAO,CAAC+C,GAAG,EAAE;MACpF,MAAM2I,KAAK,GAAG,IAAI,CAACvG,QAAQ,CAACgE,aAAa,CAACC,gBAAgB,CAAC,IAAI,CAACpJ,OAAO,CAACqJ,WAAW,CAAC;MACpF,IAAIqC,KAAK,CAAC5Q,MAAM,GAAG,CAAC,IAAI4Q,KAAK,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE,IAAI,CAAC1L,OAAO,CAAC+C,GAAG,GAAG2I,KAAK,CAAC,CAAC,CAAC;IACzE;IACA,IAAI,CAAC,IAAI,CAACvG,QAAQ,CAAC0V,gBAAgB,IAAI,CAAC,IAAI,CAAC7a,OAAO,CAAC+C,GAAG,EAAE;MACxD,IAAI,CAAC5C,MAAM,CAACT,IAAI,CAAC,yDAAyD,CAAC;IAC7E;IACA,MAAMqb,QAAQ,GAAG,CAAC,aAAa,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,mBAAmB,CAAC;IAC/FA,QAAQ,CAAC/gB,OAAO,CAAC0e,MAAM,IAAI;MACzB,IAAI,CAACA,MAAM,CAAC,GAAG,YAAY;QACzB,OAAOrO,KAAK,CAACgN,KAAK,CAACqB,MAAM,CAAC,CAAC,GAAG9Z,SAAS,CAAC;MAC1C,CAAC;IACH,CAAC,CAAC;IACF,MAAMoc,eAAe,GAAG,CAAC,aAAa,EAAE,cAAc,EAAE,mBAAmB,EAAE,sBAAsB,CAAC;IACpGA,eAAe,CAAChhB,OAAO,CAAC0e,MAAM,IAAI;MAChC,IAAI,CAACA,MAAM,CAAC,GAAG,YAAY;QACzBrO,KAAK,CAACgN,KAAK,CAACqB,MAAM,CAAC,CAAC,GAAG9Z,SAAS,CAAC;QACjC,OAAOyL,KAAK;MACd,CAAC;IACH,CAAC,CAAC;IACF,MAAM4Q,QAAQ,GAAG9hB,KAAK,CAAC,CAAC;IACxB,MAAM0U,IAAI,GAAGA,CAAA,KAAM;MACjB,MAAMqN,MAAM,GAAGA,CAAC7K,GAAG,EAAEtW,CAAC,KAAK;QACzB,IAAI,CAACygB,cAAc,GAAG,KAAK;QAC3B,IAAI,IAAI,CAACF,aAAa,IAAI,CAAC,IAAI,CAACa,oBAAoB,EAAE,IAAI,CAAChb,MAAM,CAACT,IAAI,CAAC,uEAAuE,CAAC;QAC/I,IAAI,CAAC4a,aAAa,GAAG,IAAI;QACzB,IAAI,CAAC,IAAI,CAACta,OAAO,CAACua,OAAO,EAAE,IAAI,CAACpa,MAAM,CAACZ,GAAG,CAAC,aAAa,EAAE,IAAI,CAACS,OAAO,CAAC;QACvE,IAAI,CAAC8B,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC9B,OAAO,CAAC;QACtCib,QAAQ,CAACzhB,OAAO,CAACO,CAAC,CAAC;QACnBge,QAAQ,CAAC1H,GAAG,EAAEtW,CAAC,CAAC;MAClB,CAAC;MACD,IAAI,IAAI,CAAC+d,SAAS,IAAI,IAAI,CAAC9X,OAAO,CAACgE,gBAAgB,KAAK,IAAI,IAAI,CAAC,IAAI,CAACsW,aAAa,EAAE,OAAOY,MAAM,CAAC,IAAI,EAAE,IAAI,CAACnhB,CAAC,CAACgf,IAAI,CAAC,IAAI,CAAC,CAAC;MAC3H,IAAI,CAAC3T,cAAc,CAAC,IAAI,CAACpF,OAAO,CAAC+C,GAAG,EAAEmY,MAAM,CAAC;IAC/C,CAAC;IACD,IAAI,IAAI,CAAClb,OAAO,CAACqD,SAAS,IAAI,CAAC,IAAI,CAACrD,OAAO,CAACuZ,aAAa,EAAE;MACzD1L,IAAI,CAAC,CAAC;IACR,CAAC,MAAM;MACLiL,UAAU,CAACjL,IAAI,EAAE,CAAC,CAAC;IACrB;IACA,OAAOoN,QAAQ;EACjB;EACAG,aAAaA,CAAC/V,QAAQ,EAAE;IACtB,IAAI0S,QAAQ,GAAGnZ,SAAS,CAAC9D,MAAM,GAAG,CAAC,IAAI8D,SAAS,CAAC,CAAC,CAAC,KAAKtD,SAAS,GAAGsD,SAAS,CAAC,CAAC,CAAC,GAAGib,IAAI;IACvF,IAAIwB,YAAY,GAAGtD,QAAQ;IAC3B,MAAMnR,OAAO,GAAG3N,QAAQ,CAACoM,QAAQ,CAAC,GAAGA,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC7D,IAAI,OAAOA,QAAQ,KAAK,UAAU,EAAEgW,YAAY,GAAGhW,QAAQ;IAC3D,IAAI,CAAC,IAAI,CAACrF,OAAO,CAACqD,SAAS,IAAI,IAAI,CAACrD,OAAO,CAACyZ,uBAAuB,EAAE;MACnE,IAAI7S,OAAO,IAAIA,OAAO,CAACH,WAAW,CAAC,CAAC,KAAK,QAAQ,KAAK,CAAC,IAAI,CAACzG,OAAO,CAACwZ,OAAO,IAAI,IAAI,CAACxZ,OAAO,CAACwZ,OAAO,CAAC1e,MAAM,KAAK,CAAC,CAAC,EAAE,OAAOugB,YAAY,CAAC,CAAC;MACxI,MAAMrD,MAAM,GAAG,EAAE;MACjB,MAAMsD,MAAM,GAAGvY,GAAG,IAAI;QACpB,IAAI,CAACA,GAAG,EAAE;QACV,IAAIA,GAAG,KAAK,QAAQ,EAAE;QACtB,MAAMkG,IAAI,GAAG,IAAI,CAAC9D,QAAQ,CAACgE,aAAa,CAACI,kBAAkB,CAACxG,GAAG,CAAC;QAChEkG,IAAI,CAACjP,OAAO,CAACyP,CAAC,IAAI;UAChB,IAAIA,CAAC,KAAK,QAAQ,EAAE;UACpB,IAAIuO,MAAM,CAAC3d,OAAO,CAACoP,CAAC,CAAC,GAAG,CAAC,EAAEuO,MAAM,CAACnc,IAAI,CAAC4N,CAAC,CAAC;QAC3C,CAAC,CAAC;MACJ,CAAC;MACD,IAAI,CAAC7C,OAAO,EAAE;QACZ,MAAMuH,SAAS,GAAG,IAAI,CAAChJ,QAAQ,CAACgE,aAAa,CAACC,gBAAgB,CAAC,IAAI,CAACpJ,OAAO,CAACqJ,WAAW,CAAC;QACxF8E,SAAS,CAACnU,OAAO,CAACyP,CAAC,IAAI6R,MAAM,CAAC7R,CAAC,CAAC,CAAC;MACnC,CAAC,MAAM;QACL6R,MAAM,CAAC1U,OAAO,CAAC;MACjB;MACA,IAAI,IAAI,CAAC5G,OAAO,CAACwZ,OAAO,EAAE;QACxB,IAAI,CAACxZ,OAAO,CAACwZ,OAAO,CAACxf,OAAO,CAACyP,CAAC,IAAI6R,MAAM,CAAC7R,CAAC,CAAC,CAAC;MAC9C;MACA,IAAI,CAACtE,QAAQ,CAAC0E,gBAAgB,CAACgE,IAAI,CAACmK,MAAM,EAAE,IAAI,CAAChY,OAAO,CAACuC,EAAE,EAAEhH,CAAC,IAAI;QAChE,IAAI,CAACA,CAAC,IAAI,CAAC,IAAI,CAACggB,gBAAgB,IAAI,IAAI,CAAClW,QAAQ,EAAE,IAAI,CAACmW,mBAAmB,CAAC,IAAI,CAACnW,QAAQ,CAAC;QAC1FgW,YAAY,CAAC9f,CAAC,CAAC;MACjB,CAAC,CAAC;IACJ,CAAC,MAAM;MACL8f,YAAY,CAAC,IAAI,CAAC;IACpB;EACF;EACAI,eAAeA,CAACxS,IAAI,EAAE1G,EAAE,EAAEwV,QAAQ,EAAE;IAClC,MAAMkD,QAAQ,GAAG9hB,KAAK,CAAC,CAAC;IACxB,IAAI,OAAO8P,IAAI,KAAK,UAAU,EAAE;MAC9B8O,QAAQ,GAAG9O,IAAI;MACfA,IAAI,GAAG3N,SAAS;IAClB;IACA,IAAI,OAAOiH,EAAE,KAAK,UAAU,EAAE;MAC5BwV,QAAQ,GAAGxV,EAAE;MACbA,EAAE,GAAGjH,SAAS;IAChB;IACA,IAAI,CAAC2N,IAAI,EAAEA,IAAI,GAAG,IAAI,CAAC6O,SAAS;IAChC,IAAI,CAACvV,EAAE,EAAEA,EAAE,GAAG,IAAI,CAACvC,OAAO,CAACuC,EAAE;IAC7B,IAAI,CAACwV,QAAQ,EAAEA,QAAQ,GAAG8B,IAAI;IAC9B,IAAI,CAAC1U,QAAQ,CAAC0E,gBAAgB,CAACuO,MAAM,CAACnP,IAAI,EAAE1G,EAAE,EAAE8N,GAAG,IAAI;MACrD4K,QAAQ,CAACzhB,OAAO,CAAC,CAAC;MAClBue,QAAQ,CAAC1H,GAAG,CAAC;IACf,CAAC,CAAC;IACF,OAAO4K,QAAQ;EACjB;EACAS,GAAGA,CAAC/W,MAAM,EAAE;IACV,IAAI,CAACA,MAAM,EAAE,MAAM,IAAIgX,KAAK,CAAC,+FAA+F,CAAC;IAC7H,IAAI,CAAChX,MAAM,CAACrF,IAAI,EAAE,MAAM,IAAIqc,KAAK,CAAC,0FAA0F,CAAC;IAC7H,IAAIhX,MAAM,CAACrF,IAAI,KAAK,SAAS,EAAE;MAC7B,IAAI,CAAC8a,OAAO,CAAChD,OAAO,GAAGzS,MAAM;IAC/B;IACA,IAAIA,MAAM,CAACrF,IAAI,KAAK,QAAQ,IAAIqF,MAAM,CAACpF,GAAG,IAAIoF,MAAM,CAACjF,IAAI,IAAIiF,MAAM,CAAChF,KAAK,EAAE;MACzE,IAAI,CAACya,OAAO,CAACja,MAAM,GAAGwE,MAAM;IAC9B;IACA,IAAIA,MAAM,CAACrF,IAAI,KAAK,kBAAkB,EAAE;MACtC,IAAI,CAAC8a,OAAO,CAACS,gBAAgB,GAAGlW,MAAM;IACxC;IACA,IAAIA,MAAM,CAACrF,IAAI,KAAK,YAAY,EAAE;MAChC,IAAI,CAAC8a,OAAO,CAAC7S,UAAU,GAAG5C,MAAM;IAClC;IACA,IAAIA,MAAM,CAACrF,IAAI,KAAK,eAAe,EAAE;MACnCkF,aAAa,CAACE,gBAAgB,CAACC,MAAM,CAAC;IACxC;IACA,IAAIA,MAAM,CAACrF,IAAI,KAAK,WAAW,EAAE;MAC/B,IAAI,CAAC8a,OAAO,CAACvE,SAAS,GAAGlR,MAAM;IACjC;IACA,IAAIA,MAAM,CAACrF,IAAI,KAAK,UAAU,EAAE;MAC9B,IAAI,CAAC8a,OAAO,CAACC,QAAQ,CAACxe,IAAI,CAAC8I,MAAM,CAAC;IACpC;IACA,OAAO,IAAI;EACb;EACA6W,mBAAmBA,CAAC/R,CAAC,EAAE;IACrB,IAAI,CAACA,CAAC,IAAI,CAAC,IAAI,CAACqO,SAAS,EAAE;IAC3B,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAACzd,OAAO,CAACoP,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE;IACvC,KAAK,IAAImS,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAG,IAAI,CAAC9D,SAAS,CAAChd,MAAM,EAAE8gB,EAAE,EAAE,EAAE;MACjD,MAAMC,SAAS,GAAG,IAAI,CAAC/D,SAAS,CAAC8D,EAAE,CAAC;MACpC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAACvhB,OAAO,CAACwhB,SAAS,CAAC,GAAG,CAAC,CAAC,EAAE;MAC/C,IAAI,IAAI,CAACxE,KAAK,CAACnT,2BAA2B,CAAC2X,SAAS,CAAC,EAAE;QACrD,IAAI,CAACN,gBAAgB,GAAGM,SAAS;QACjC;MACF;IACF;EACF;EACAzW,cAAcA,CAACrC,GAAG,EAAEgV,QAAQ,EAAE;IAC5B,IAAI+D,MAAM,GAAG,IAAI;IACjB,IAAI,CAACC,oBAAoB,GAAGhZ,GAAG;IAC/B,MAAMkY,QAAQ,GAAG9hB,KAAK,CAAC,CAAC;IACxB,IAAI,CAAC2I,IAAI,CAAC,kBAAkB,EAAEiB,GAAG,CAAC;IAClC,MAAMiZ,WAAW,GAAGvS,CAAC,IAAI;MACvB,IAAI,CAACpE,QAAQ,GAAGoE,CAAC;MACjB,IAAI,CAACqO,SAAS,GAAG,IAAI,CAAC3S,QAAQ,CAACgE,aAAa,CAACI,kBAAkB,CAACE,CAAC,CAAC;MAClE,IAAI,CAAC8R,gBAAgB,GAAGjgB,SAAS;MACjC,IAAI,CAACkgB,mBAAmB,CAAC/R,CAAC,CAAC;IAC7B,CAAC;IACD,MAAM8O,IAAI,GAAGA,CAAClI,GAAG,EAAE5G,CAAC,KAAK;MACvB,IAAIA,CAAC,EAAE;QACLuS,WAAW,CAACvS,CAAC,CAAC;QACd,IAAI,CAAC3E,UAAU,CAACM,cAAc,CAACqE,CAAC,CAAC;QACjC,IAAI,CAACsS,oBAAoB,GAAGzgB,SAAS;QACrC,IAAI,CAACwG,IAAI,CAAC,iBAAiB,EAAE2H,CAAC,CAAC;QAC/B,IAAI,CAACtJ,MAAM,CAACZ,GAAG,CAAC,iBAAiB,EAAEkK,CAAC,CAAC;MACvC,CAAC,MAAM;QACL,IAAI,CAACsS,oBAAoB,GAAGzgB,SAAS;MACvC;MACA2f,QAAQ,CAACzhB,OAAO,CAAC,YAAY;QAC3B,OAAOsiB,MAAM,CAAC/hB,CAAC,CAAC,GAAG6E,SAAS,CAAC;MAC/B,CAAC,CAAC;MACF,IAAImZ,QAAQ,EAAEA,QAAQ,CAAC1H,GAAG,EAAE,YAAY;QACtC,OAAOyL,MAAM,CAAC/hB,CAAC,CAAC,GAAG6E,SAAS,CAAC;MAC/B,CAAC,CAAC;IACJ,CAAC;IACD,MAAMqd,MAAM,GAAGhT,IAAI,IAAI;MACrB,IAAI,CAAClG,GAAG,IAAI,CAACkG,IAAI,IAAI,IAAI,CAAC9D,QAAQ,CAAC0V,gBAAgB,EAAE5R,IAAI,GAAG,EAAE;MAC9D,MAAMQ,CAAC,GAAGxQ,QAAQ,CAACgQ,IAAI,CAAC,GAAGA,IAAI,GAAG,IAAI,CAAC9D,QAAQ,CAACgE,aAAa,CAAC4E,qBAAqB,CAAC9E,IAAI,CAAC;MACzF,IAAIQ,CAAC,EAAE;QACL,IAAI,CAAC,IAAI,CAACpE,QAAQ,EAAE;UAClB2W,WAAW,CAACvS,CAAC,CAAC;QAChB;QACA,IAAI,CAAC,IAAI,CAAC3E,UAAU,CAACO,QAAQ,EAAE,IAAI,CAACP,UAAU,CAACM,cAAc,CAACqE,CAAC,CAAC;QAChE,IAAI,IAAI,CAACtE,QAAQ,CAAC0V,gBAAgB,IAAI,IAAI,CAAC1V,QAAQ,CAAC0V,gBAAgB,CAACqB,iBAAiB,EAAE,IAAI,CAAC/W,QAAQ,CAAC0V,gBAAgB,CAACqB,iBAAiB,CAACzS,CAAC,CAAC;MAC7I;MACA,IAAI,CAAC2R,aAAa,CAAC3R,CAAC,EAAE4G,GAAG,IAAI;QAC3BkI,IAAI,CAAClI,GAAG,EAAE5G,CAAC,CAAC;MACd,CAAC,CAAC;IACJ,CAAC;IACD,IAAI,CAAC1G,GAAG,IAAI,IAAI,CAACoC,QAAQ,CAAC0V,gBAAgB,IAAI,CAAC,IAAI,CAAC1V,QAAQ,CAAC0V,gBAAgB,CAACsB,KAAK,EAAE;MACnFF,MAAM,CAAC,IAAI,CAAC9W,QAAQ,CAAC0V,gBAAgB,CAACuB,MAAM,CAAC,CAAC,CAAC;IACjD,CAAC,MAAM,IAAI,CAACrZ,GAAG,IAAI,IAAI,CAACoC,QAAQ,CAAC0V,gBAAgB,IAAI,IAAI,CAAC1V,QAAQ,CAAC0V,gBAAgB,CAACsB,KAAK,EAAE;MACzF,IAAI,IAAI,CAAChX,QAAQ,CAAC0V,gBAAgB,CAACuB,MAAM,CAACthB,MAAM,KAAK,CAAC,EAAE;QACtD,IAAI,CAACqK,QAAQ,CAAC0V,gBAAgB,CAACuB,MAAM,CAAC,CAAC,CAACpD,IAAI,CAACiD,MAAM,CAAC;MACtD,CAAC,MAAM;QACL,IAAI,CAAC9W,QAAQ,CAAC0V,gBAAgB,CAACuB,MAAM,CAACH,MAAM,CAAC;MAC/C;IACF,CAAC,MAAM;MACLA,MAAM,CAAClZ,GAAG,CAAC;IACb;IACA,OAAOkY,QAAQ;EACjB;EACAoB,SAASA,CAACtZ,GAAG,EAAER,EAAE,EAAE+Z,SAAS,EAAE;IAC5B,IAAIC,MAAM,GAAG,IAAI;IACjB,MAAMC,MAAM,GAAG,SAAAA,CAAUpiB,GAAG,EAAE8a,IAAI,EAAE;MAClC,IAAIlV,OAAO;MACX,IAAI,OAAOkV,IAAI,KAAK,QAAQ,EAAE;QAC5B,KAAK,IAAIvU,KAAK,GAAG/B,SAAS,CAAC9D,MAAM,EAAEsa,IAAI,GAAG,IAAI9U,KAAK,CAACK,KAAK,GAAG,CAAC,GAAGA,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;UACjHwU,IAAI,CAACxU,KAAK,GAAG,CAAC,CAAC,GAAGhC,SAAS,CAACgC,KAAK,CAAC;QACpC;QACAZ,OAAO,GAAGuc,MAAM,CAACvc,OAAO,CAACqG,gCAAgC,CAAC,CAACjM,GAAG,EAAE8a,IAAI,CAAC,CAACtZ,MAAM,CAACwZ,IAAI,CAAC,CAAC;MACrF,CAAC,MAAM;QACLpV,OAAO,GAAG;UACR,GAAGkV;QACL,CAAC;MACH;MACAlV,OAAO,CAAC+C,GAAG,GAAG/C,OAAO,CAAC+C,GAAG,IAAIyZ,MAAM,CAACzZ,GAAG;MACvC/C,OAAO,CAACiJ,IAAI,GAAGjJ,OAAO,CAACiJ,IAAI,IAAIuT,MAAM,CAACvT,IAAI;MAC1CjJ,OAAO,CAACuC,EAAE,GAAGvC,OAAO,CAACuC,EAAE,IAAIia,MAAM,CAACja,EAAE;MACpC,IAAIvC,OAAO,CAACsc,SAAS,KAAK,EAAE,EAAEtc,OAAO,CAACsc,SAAS,GAAGtc,OAAO,CAACsc,SAAS,IAAIA,SAAS,IAAIE,MAAM,CAACF,SAAS;MACpG,MAAMte,YAAY,GAAGue,MAAM,CAACvc,OAAO,CAAChC,YAAY,IAAI,GAAG;MACvD,IAAIye,SAAS;MACb,IAAIzc,OAAO,CAACsc,SAAS,IAAIhc,KAAK,CAAC0C,OAAO,CAAC5I,GAAG,CAAC,EAAE;QAC3CqiB,SAAS,GAAGriB,GAAG,CAACiE,GAAG,CAAClD,CAAC,IAAI,GAAG6E,OAAO,CAACsc,SAAS,GAAGte,YAAY,GAAG7C,CAAC,EAAE,CAAC;MACrE,CAAC,MAAM;QACLshB,SAAS,GAAGzc,OAAO,CAACsc,SAAS,GAAG,GAAGtc,OAAO,CAACsc,SAAS,GAAGte,YAAY,GAAG5D,GAAG,EAAE,GAAGA,GAAG;MACnF;MACA,OAAOmiB,MAAM,CAACxiB,CAAC,CAAC0iB,SAAS,EAAEzc,OAAO,CAAC;IACrC,CAAC;IACD,IAAI/G,QAAQ,CAAC8J,GAAG,CAAC,EAAE;MACjByZ,MAAM,CAACzZ,GAAG,GAAGA,GAAG;IAClB,CAAC,MAAM;MACLyZ,MAAM,CAACvT,IAAI,GAAGlG,GAAG;IACnB;IACAyZ,MAAM,CAACja,EAAE,GAAGA,EAAE;IACdia,MAAM,CAACF,SAAS,GAAGA,SAAS;IAC5B,OAAOE,MAAM;EACf;EACAziB,CAACA,CAAA,EAAG;IACF,OAAO,IAAI,CAAC+K,UAAU,IAAI,IAAI,CAACA,UAAU,CAACqB,SAAS,CAAC,GAAGvH,SAAS,CAAC;EACnE;EACA0G,MAAMA,CAAA,EAAG;IACP,OAAO,IAAI,CAACR,UAAU,IAAI,IAAI,CAACA,UAAU,CAACQ,MAAM,CAAC,GAAG1G,SAAS,CAAC;EAChE;EACA8d,mBAAmBA,CAACna,EAAE,EAAE;IACtB,IAAI,CAACvC,OAAO,CAACwC,SAAS,GAAGD,EAAE;EAC7B;EACAqJ,kBAAkBA,CAACrJ,EAAE,EAAE;IACrB,IAAIvC,OAAO,GAAGpB,SAAS,CAAC9D,MAAM,GAAG,CAAC,IAAI8D,SAAS,CAAC,CAAC,CAAC,KAAKtD,SAAS,GAAGsD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACpF,IAAI,CAAC,IAAI,CAAC0b,aAAa,EAAE;MACvB,IAAI,CAACna,MAAM,CAACT,IAAI,CAAC,iDAAiD,EAAE,IAAI,CAACoY,SAAS,CAAC;MACnF,OAAO,KAAK;IACd;IACA,IAAI,CAAC,IAAI,CAACA,SAAS,IAAI,CAAC,IAAI,CAACA,SAAS,CAAChd,MAAM,EAAE;MAC7C,IAAI,CAACqF,MAAM,CAACT,IAAI,CAAC,4DAA4D,EAAE,IAAI,CAACoY,SAAS,CAAC;MAC9F,OAAO,KAAK;IACd;IACA,MAAM/U,GAAG,GAAG/C,OAAO,CAAC+C,GAAG,IAAI,IAAI,CAACwY,gBAAgB,IAAI,IAAI,CAACzD,SAAS,CAAC,CAAC,CAAC;IACrE,MAAMzO,WAAW,GAAG,IAAI,CAACrJ,OAAO,GAAG,IAAI,CAACA,OAAO,CAACqJ,WAAW,GAAG,KAAK;IACnE,MAAMsT,OAAO,GAAG,IAAI,CAAC7E,SAAS,CAAC,IAAI,CAACA,SAAS,CAAChd,MAAM,GAAG,CAAC,CAAC;IACzD,IAAIiI,GAAG,CAAC0D,WAAW,CAAC,CAAC,KAAK,QAAQ,EAAE,OAAO,IAAI;IAC/C,MAAMmW,cAAc,GAAGA,CAACnT,CAAC,EAAEtF,CAAC,KAAK;MAC/B,MAAM0Y,SAAS,GAAG,IAAI,CAAC1X,QAAQ,CAAC0E,gBAAgB,CAAC8N,KAAK,CAAC,GAAGlO,CAAC,IAAItF,CAAC,EAAE,CAAC;MACnE,OAAO0Y,SAAS,KAAK,CAAC,CAAC,IAAIA,SAAS,KAAK,CAAC,IAAIA,SAAS,KAAK,CAAC;IAC/D,CAAC;IACD,IAAI7c,OAAO,CAAC8c,QAAQ,EAAE;MACpB,MAAMC,SAAS,GAAG/c,OAAO,CAAC8c,QAAQ,CAAC,IAAI,EAAEF,cAAc,CAAC;MACxD,IAAIG,SAAS,KAAKzhB,SAAS,EAAE,OAAOyhB,SAAS;IAC/C;IACA,IAAI,IAAI,CAACjZ,iBAAiB,CAACf,GAAG,EAAER,EAAE,CAAC,EAAE,OAAO,IAAI;IAChD,IAAI,CAAC,IAAI,CAAC4C,QAAQ,CAAC0E,gBAAgB,CAACuN,OAAO,IAAI,IAAI,CAACpX,OAAO,CAACqD,SAAS,IAAI,CAAC,IAAI,CAACrD,OAAO,CAACyZ,uBAAuB,EAAE,OAAO,IAAI;IAC3H,IAAImD,cAAc,CAAC7Z,GAAG,EAAER,EAAE,CAAC,KAAK,CAAC8G,WAAW,IAAIuT,cAAc,CAACD,OAAO,EAAEpa,EAAE,CAAC,CAAC,EAAE,OAAO,IAAI;IACzF,OAAO,KAAK;EACd;EACAya,cAAcA,CAACza,EAAE,EAAEwV,QAAQ,EAAE;IAC3B,MAAMkD,QAAQ,GAAG9hB,KAAK,CAAC,CAAC;IACxB,IAAI,CAAC,IAAI,CAAC6G,OAAO,CAACuC,EAAE,EAAE;MACpB,IAAIwV,QAAQ,EAAEA,QAAQ,CAAC,CAAC;MACxB,OAAOxe,OAAO,CAACC,OAAO,CAAC,CAAC;IAC1B;IACA,IAAIP,QAAQ,CAACsJ,EAAE,CAAC,EAAEA,EAAE,GAAG,CAACA,EAAE,CAAC;IAC3BA,EAAE,CAACvI,OAAO,CAACmK,CAAC,IAAI;MACd,IAAI,IAAI,CAACnE,OAAO,CAACuC,EAAE,CAAClI,OAAO,CAAC8J,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAACnE,OAAO,CAACuC,EAAE,CAAC1G,IAAI,CAACsI,CAAC,CAAC;IAC7D,CAAC,CAAC;IACF,IAAI,CAACiX,aAAa,CAAC/K,GAAG,IAAI;MACxB4K,QAAQ,CAACzhB,OAAO,CAAC,CAAC;MAClB,IAAIue,QAAQ,EAAEA,QAAQ,CAAC1H,GAAG,CAAC;IAC7B,CAAC,CAAC;IACF,OAAO4K,QAAQ;EACjB;EACAgC,aAAaA,CAAChU,IAAI,EAAE8O,QAAQ,EAAE;IAC5B,MAAMkD,QAAQ,GAAG9hB,KAAK,CAAC,CAAC;IACxB,IAAIF,QAAQ,CAACgQ,IAAI,CAAC,EAAEA,IAAI,GAAG,CAACA,IAAI,CAAC;IACjC,MAAMiU,SAAS,GAAG,IAAI,CAACld,OAAO,CAACwZ,OAAO,IAAI,EAAE;IAC5C,MAAM2D,OAAO,GAAGlU,IAAI,CAAC/K,MAAM,CAAC6E,GAAG,IAAIma,SAAS,CAAC7iB,OAAO,CAAC0I,GAAG,CAAC,GAAG,CAAC,IAAI,IAAI,CAACoC,QAAQ,CAACgE,aAAa,CAACyE,eAAe,CAAC7K,GAAG,CAAC,CAAC;IAClH,IAAI,CAACoa,OAAO,CAACriB,MAAM,EAAE;MACnB,IAAIid,QAAQ,EAAEA,QAAQ,CAAC,CAAC;MACxB,OAAOxe,OAAO,CAACC,OAAO,CAAC,CAAC;IAC1B;IACA,IAAI,CAACwG,OAAO,CAACwZ,OAAO,GAAG0D,SAAS,CAACthB,MAAM,CAACuhB,OAAO,CAAC;IAChD,IAAI,CAAC/B,aAAa,CAAC/K,GAAG,IAAI;MACxB4K,QAAQ,CAACzhB,OAAO,CAAC,CAAC;MAClB,IAAIue,QAAQ,EAAEA,QAAQ,CAAC1H,GAAG,CAAC;IAC7B,CAAC,CAAC;IACF,OAAO4K,QAAQ;EACjB;EACAmC,GAAGA,CAACra,GAAG,EAAE;IACP,IAAI,CAACA,GAAG,EAAEA,GAAG,GAAG,IAAI,CAACwY,gBAAgB,KAAK,IAAI,CAACzD,SAAS,IAAI,IAAI,CAACA,SAAS,CAAChd,MAAM,GAAG,CAAC,GAAG,IAAI,CAACgd,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAACzS,QAAQ,CAAC;IAC1H,IAAI,CAACtC,GAAG,EAAE,OAAO,KAAK;IACtB,MAAMsa,OAAO,GAAG,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC;IACxb,MAAMlU,aAAa,GAAG,IAAI,CAAChE,QAAQ,IAAI,IAAI,CAACA,QAAQ,CAACgE,aAAa,IAAI,IAAI6D,YAAY,CAAC1P,GAAG,CAAC,CAAC,CAAC;IAC7F,OAAO+f,OAAO,CAAChjB,OAAO,CAAC8O,aAAa,CAACiE,uBAAuB,CAACrK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAIA,GAAG,CAAC0D,WAAW,CAAC,CAAC,CAACpM,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,KAAK,GAAG,KAAK;EACnI;EACA,OAAOijB,cAAcA,CAAA,EAAG;IACtB,IAAItd,OAAO,GAAGpB,SAAS,CAAC9D,MAAM,GAAG,CAAC,IAAI8D,SAAS,CAAC,CAAC,CAAC,KAAKtD,SAAS,GAAGsD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACpF,IAAImZ,QAAQ,GAAGnZ,SAAS,CAAC9D,MAAM,GAAG,CAAC,GAAG8D,SAAS,CAAC,CAAC,CAAC,GAAGtD,SAAS;IAC9D,OAAO,IAAI6e,IAAI,CAACna,OAAO,EAAE+X,QAAQ,CAAC;EACpC;EACAwF,aAAaA,CAAA,EAAG;IACd,IAAIvd,OAAO,GAAGpB,SAAS,CAAC9D,MAAM,GAAG,CAAC,IAAI8D,SAAS,CAAC,CAAC,CAAC,KAAKtD,SAAS,GAAGsD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACpF,IAAImZ,QAAQ,GAAGnZ,SAAS,CAAC9D,MAAM,GAAG,CAAC,IAAI8D,SAAS,CAAC,CAAC,CAAC,KAAKtD,SAAS,GAAGsD,SAAS,CAAC,CAAC,CAAC,GAAGib,IAAI;IACvF,MAAM2D,iBAAiB,GAAGxd,OAAO,CAACwd,iBAAiB;IACnD,IAAIA,iBAAiB,EAAE,OAAOxd,OAAO,CAACwd,iBAAiB;IACvD,MAAMC,aAAa,GAAG;MACpB,GAAG,IAAI,CAACzd,OAAO;MACf,GAAGA,OAAO;MACV,GAAG;QACDua,OAAO,EAAE;MACX;IACF,CAAC;IACD,MAAMnZ,KAAK,GAAG,IAAI+Y,IAAI,CAACsD,aAAa,CAAC;IACrC,IAAIzd,OAAO,CAACI,KAAK,KAAK9E,SAAS,IAAI0E,OAAO,CAACE,MAAM,KAAK5E,SAAS,EAAE;MAC/D8F,KAAK,CAACjB,MAAM,GAAGiB,KAAK,CAACjB,MAAM,CAACiB,KAAK,CAACpB,OAAO,CAAC;IAC5C;IACA,MAAM0d,aAAa,GAAG,CAAC,OAAO,EAAE,UAAU,EAAE,UAAU,CAAC;IACvDA,aAAa,CAAC1jB,OAAO,CAACC,CAAC,IAAI;MACzBmH,KAAK,CAACnH,CAAC,CAAC,GAAG,IAAI,CAACA,CAAC,CAAC;IACpB,CAAC,CAAC;IACFmH,KAAK,CAAC+D,QAAQ,GAAG;MACf,GAAG,IAAI,CAACA;IACV,CAAC;IACD/D,KAAK,CAAC+D,QAAQ,CAACwG,KAAK,GAAG;MACrBC,kBAAkB,EAAExK,KAAK,CAACwK,kBAAkB,CAACmN,IAAI,CAAC3X,KAAK;IACzD,CAAC;IACD,IAAIoc,iBAAiB,EAAE;MACrBpc,KAAK,CAACiW,KAAK,GAAG,IAAI/U,aAAa,CAAC,IAAI,CAAC+U,KAAK,CAACrb,IAAI,EAAEyhB,aAAa,CAAC;MAC/Drc,KAAK,CAAC+D,QAAQ,CAACqH,aAAa,GAAGpL,KAAK,CAACiW,KAAK;IAC5C;IACAjW,KAAK,CAAC0D,UAAU,GAAG,IAAII,UAAU,CAAC9D,KAAK,CAAC+D,QAAQ,EAAEsY,aAAa,CAAC;IAChErc,KAAK,CAAC0D,UAAU,CAACtD,EAAE,CAAC,GAAG,EAAE,UAAUG,KAAK,EAAE;MACxC,KAAK,IAAIb,KAAK,GAAGlC,SAAS,CAAC9D,MAAM,EAAE0E,IAAI,GAAG,IAAIc,KAAK,CAACQ,KAAK,GAAG,CAAC,GAAGA,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;QACjHvB,IAAI,CAACuB,KAAK,GAAG,CAAC,CAAC,GAAGnC,SAAS,CAACmC,KAAK,CAAC;MACpC;MACAK,KAAK,CAACU,IAAI,CAACH,KAAK,EAAE,GAAGnC,IAAI,CAAC;IAC5B,CAAC,CAAC;IACF4B,KAAK,CAACnB,IAAI,CAACwd,aAAa,EAAE1F,QAAQ,CAAC;IACnC3W,KAAK,CAAC0D,UAAU,CAAC9E,OAAO,GAAGyd,aAAa;IACxCrc,KAAK,CAAC0D,UAAU,CAAC+E,gBAAgB,CAAC1E,QAAQ,CAACwG,KAAK,GAAG;MACjDC,kBAAkB,EAAExK,KAAK,CAACwK,kBAAkB,CAACmN,IAAI,CAAC3X,KAAK;IACzD,CAAC;IACD,OAAOA,KAAK;EACd;EACAmD,MAAMA,CAAA,EAAG;IACP,OAAO;MACLvE,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBqX,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBhS,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvByS,SAAS,EAAE,IAAI,CAACA,SAAS;MACzByD,gBAAgB,EAAE,IAAI,CAACA;IACzB,CAAC;EACH;AACF;AACA,MAAMoC,QAAQ,GAAGxD,IAAI,CAACmD,cAAc,CAAC,CAAC;AACtCK,QAAQ,CAACL,cAAc,GAAGnD,IAAI,CAACmD,cAAc;AAE7C,MAAMA,cAAc,GAAGK,QAAQ,CAACL,cAAc;AAC9C,MAAMF,GAAG,GAAGO,QAAQ,CAACP,GAAG;AACxB,MAAMnd,IAAI,GAAG0d,QAAQ,CAAC1d,IAAI;AAC1B,MAAMmb,aAAa,GAAGuC,QAAQ,CAACvC,aAAa;AAC5C,MAAMK,eAAe,GAAGkC,QAAQ,CAAClC,eAAe;AAChD,MAAMC,GAAG,GAAGiC,QAAQ,CAACjC,GAAG;AACxB,MAAMtW,cAAc,GAAGuY,QAAQ,CAACvY,cAAc;AAC9C,MAAMiX,SAAS,GAAGsB,QAAQ,CAACtB,SAAS;AACpC,MAAMtiB,CAAC,GAAG4jB,QAAQ,CAAC5jB,CAAC;AACpB,MAAMuL,MAAM,GAAGqY,QAAQ,CAACrY,MAAM;AAC9B,MAAMoX,mBAAmB,GAAGiB,QAAQ,CAACjB,mBAAmB;AACxD,MAAM9Q,kBAAkB,GAAG+R,QAAQ,CAAC/R,kBAAkB;AACtD,MAAMoR,cAAc,GAAGW,QAAQ,CAACX,cAAc;AAC9C,MAAMC,aAAa,GAAGU,QAAQ,CAACV,aAAa;AAE5C,SAAS7X,cAAc,EAAEkY,cAAc,EAAEK,QAAQ,IAAIvP,OAAO,EAAEgP,GAAG,EAAE9X,MAAM,EAAE+W,SAAS,EAAEzQ,kBAAkB,EAAE3L,IAAI,EAAEgd,aAAa,EAAED,cAAc,EAAE5B,aAAa,EAAEK,eAAe,EAAEiB,mBAAmB,EAAE3iB,CAAC,EAAE2hB,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}