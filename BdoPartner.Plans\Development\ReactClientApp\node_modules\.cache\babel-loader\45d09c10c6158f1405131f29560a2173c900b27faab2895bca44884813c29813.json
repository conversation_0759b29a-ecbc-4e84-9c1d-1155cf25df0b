{"ast": null, "code": "var _jsxFileName = \"C:\\\\git\\\\Partner-Site\\\\BdoPartner.Plans\\\\Development\\\\ReactClientApp\\\\src\\\\components\\\\admin\\\\PartnerPlanDetailsDialog.jsx\";\nimport React from 'react';\nimport { Dialog } from 'primereact/dialog';\nimport { Tag } from 'primereact/tag';\nimport './PartnerPlanDetailsDialog.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PartnerPlanDetailsDialog = ({\n  visible,\n  onHide,\n  planDetails,\n  loading\n}) => {\n  if (!planDetails && !loading) {\n    return null;\n  }\n  const formatDateTime = dateString => {\n    if (!dateString) return '';\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: '2-digit',\n      day: '2-digit',\n      hour: '2-digit',\n      minute: '2-digit',\n      hour12: true\n    });\n  };\n  const getStatusSeverity = status => {\n    switch (status === null || status === void 0 ? void 0 : status.toLowerCase()) {\n      case 'completed':\n      case 'submitted':\n        return 'success';\n      case 'under review':\n      case 'in review':\n        return 'warning';\n      case 'not started':\n      case 'draft':\n        return 'danger';\n      default:\n        return 'secondary';\n    }\n  };\n  const getStatusIcon = status => {\n    switch (status === null || status === void 0 ? void 0 : status.toLowerCase()) {\n      case 'completed':\n      case 'submitted':\n        return 'pi pi-check-circle';\n      case 'under review':\n      case 'in review':\n        return 'pi pi-clock';\n      case 'not started':\n      case 'draft':\n        return 'pi pi-exclamation-triangle';\n      default:\n        return 'pi pi-circle';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    visible: visible,\n    onHide: onHide,\n    header: \"Partner Annual Plan Details\",\n    style: {\n      width: '800px'\n    },\n    modal: true,\n    className: \"partner-plan-details-dialog\",\n    draggable: false,\n    resizable: false,\n    closable: true,\n    children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"pi pi-spinner pi-spin\",\n        style: {\n          fontSize: '2rem'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Loading partner plan details...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 17\n    }, this) : planDetails ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"plan-details-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-row\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"header-label\",\n            children: \"Partner Name:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"header-value\",\n            children: planDetails.partnerName || ''\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"header-label\",\n            children: \"Service Line:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"header-value\",\n            children: planDetails.serviceLine || ''\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"header-label\",\n            children: \"Sub-service Line:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"header-value\",\n            children: planDetails.subServiceLine || ''\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"header-label\",\n            children: \"Year:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"header-value\",\n            children: planDetails.year\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"reviewers-row\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"reviewer-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"reviewer-label\",\n            children: \"Primary Reviewer:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"reviewer-value\",\n            children: planDetails.primaryReviewerName || ''\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"reviewer-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"reviewer-label\",\n            children: \"Secondary Reviewer:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"reviewer-value\",\n            children: planDetails.secondaryReviewerName || ''\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"status-row\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"status-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"status-label\",\n            children: \"Plan Status:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(Tag, {\n            value: planDetails.planStatus || 'Not Started',\n            severity: getStatusSeverity(planDetails.planStatus),\n            icon: getStatusIcon(planDetails.planStatus),\n            className: \"status-tag\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"status-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"status-label\",\n            children: \"Submission Date/Time:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"status-value\",\n            children: planDetails.planSubmittedOn ? formatDateTime(planDetails.planSubmittedOn) : ''\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"status-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"status-label\",\n            children: \"Submitted by:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"status-value\",\n            children: planDetails.planSubmittedByName || ''\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"status-row\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"status-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"status-label\",\n            children: \"Mid-Year Review Status:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(Tag, {\n            value: planDetails.midYearReviewStatus || 'Not Started',\n            severity: getStatusSeverity(planDetails.midYearReviewStatus),\n            icon: getStatusIcon(planDetails.midYearReviewStatus),\n            className: \"status-tag\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"status-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"status-label\",\n            children: \"Submission Date/Time:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"status-value\",\n            children: planDetails.midYearReviewSubmittedOn ? formatDateTime(planDetails.midYearReviewSubmittedOn) : ''\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"status-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"status-label\",\n            children: \"Submitted by:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"status-value\",\n            children: planDetails.midYearReviewSubmittedByName || ''\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"status-row\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"status-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"status-label\",\n            children: \"Final Review Status:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(Tag, {\n            value: planDetails.finalReviewStatus || 'Not Started',\n            severity: getStatusSeverity(planDetails.finalReviewStatus),\n            icon: getStatusIcon(planDetails.finalReviewStatus),\n            className: \"status-tag\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"status-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"status-label\",\n            children: \"Submission Date/Time:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"status-value\",\n            children: planDetails.finalReviewSubmittedOn ? formatDateTime(planDetails.finalReviewSubmittedOn) : ''\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"status-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"status-label\",\n            children: \"Submitted by:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"status-value\",\n            children: planDetails.finalReviewSubmittedByName || ''\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 17\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error-container\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Failed to load partner plan details.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 182,\n      columnNumber: 17\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 56,\n    columnNumber: 9\n  }, this);\n};\n_c = PartnerPlanDetailsDialog;\nexport default PartnerPlanDetailsDialog;\nvar _c;\n$RefreshReg$(_c, \"PartnerPlanDetailsDialog\");", "map": {"version": 3, "names": ["React", "Dialog", "Tag", "jsxDEV", "_jsxDEV", "PartnerPlanDetailsDialog", "visible", "onHide", "planDetails", "loading", "formatDateTime", "dateString", "Date", "toLocaleDateString", "year", "month", "day", "hour", "minute", "hour12", "getStatusSeverity", "status", "toLowerCase", "getStatusIcon", "header", "style", "width", "modal", "className", "draggable", "resizable", "closable", "children", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "partner<PERSON>ame", "serviceLine", "subServiceLine", "primaryReviewerName", "secondaryReviewerName", "value", "planStatus", "severity", "icon", "planSubmittedOn", "planSubmittedByName", "midYearReviewStatus", "midYearReviewSubmittedOn", "midYearReviewSubmittedByName", "finalReviewStatus", "finalReviewSubmittedOn", "finalReviewSubmittedByName", "_c", "$RefreshReg$"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/components/admin/PartnerPlanDetailsDialog.jsx"], "sourcesContent": ["import React from 'react';\r\nimport { Dialog } from 'primereact/dialog';\r\nimport { Tag } from 'primereact/tag';\r\nimport './PartnerPlanDetailsDialog.css';\r\n\r\nconst PartnerPlanDetailsDialog = ({ visible, onHide, planDetails, loading }) => {\r\n    if (!planDetails && !loading) {\r\n        return null;\r\n    }\r\n\r\n    const formatDateTime = (dateString) => {\r\n        if (!dateString) return '';\r\n        return new Date(dateString).toLocaleDateString('en-US', {\r\n            year: 'numeric',\r\n            month: '2-digit',\r\n            day: '2-digit',\r\n            hour: '2-digit',\r\n            minute: '2-digit',\r\n            hour12: true\r\n        });\r\n    };\r\n\r\n    const getStatusSeverity = (status) => {\r\n        switch (status?.toLowerCase()) {\r\n            case 'completed':\r\n            case 'submitted':\r\n                return 'success';\r\n            case 'under review':\r\n            case 'in review':\r\n                return 'warning';\r\n            case 'not started':\r\n            case 'draft':\r\n                return 'danger';\r\n            default:\r\n                return 'secondary';\r\n        }\r\n    };\r\n\r\n    const getStatusIcon = (status) => {\r\n        switch (status?.toLowerCase()) {\r\n            case 'completed':\r\n            case 'submitted':\r\n                return 'pi pi-check-circle';\r\n            case 'under review':\r\n            case 'in review':\r\n                return 'pi pi-clock';\r\n            case 'not started':\r\n            case 'draft':\r\n                return 'pi pi-exclamation-triangle';\r\n            default:\r\n                return 'pi pi-circle';\r\n        }\r\n    };\r\n\r\n    return (\r\n        <Dialog\r\n            visible={visible}\r\n            onHide={onHide}\r\n            header=\"Partner Annual Plan Details\"\r\n            style={{ width: '800px' }}\r\n            modal\r\n            className=\"partner-plan-details-dialog\"\r\n            draggable={false}\r\n            resizable={false}\r\n            closable={true}\r\n        >\r\n            {loading ? (\r\n                <div className=\"loading-container\">\r\n                    <i className=\"pi pi-spinner pi-spin\" style={{ fontSize: '2rem' }}></i>\r\n                    <p>Loading partner plan details...</p>\r\n                </div>\r\n            ) : planDetails ? (\r\n                <div className=\"plan-details-content\">\r\n                    {/* Header Row with Partner Name, Service Lines, and Year */}\r\n                    <div className=\"header-row\">\r\n                        <div className=\"header-item\">\r\n                            <span className=\"header-label\">Partner Name:</span>\r\n                            <span className=\"header-value\">{planDetails.partnerName || ''}</span>\r\n                        </div>\r\n                        <div className=\"header-item\">\r\n                            <span className=\"header-label\">Service Line:</span>\r\n                            <span className=\"header-value\">{planDetails.serviceLine || ''}</span>\r\n                        </div>\r\n                        <div className=\"header-item\">\r\n                            <span className=\"header-label\">Sub-service Line:</span>\r\n                            <span className=\"header-value\">{planDetails.subServiceLine || ''}</span>\r\n                        </div>\r\n                        <div className=\"header-item\">\r\n                            <span className=\"header-label\">Year:</span>\r\n                            <span className=\"header-value\">{planDetails.year}</span>\r\n                        </div>\r\n                    </div>\r\n\r\n                    {/* Reviewers Row */}\r\n                    <div className=\"reviewers-row\">\r\n                        <div className=\"reviewer-item\">\r\n                            <span className=\"reviewer-label\">Primary Reviewer:</span>\r\n                            <span className=\"reviewer-value\">{planDetails.primaryReviewerName || ''}</span>\r\n                        </div>\r\n                        <div className=\"reviewer-item\">\r\n                            <span className=\"reviewer-label\">Secondary Reviewer:</span>\r\n                            <span className=\"reviewer-value\">{planDetails.secondaryReviewerName || ''}</span>\r\n                        </div>\r\n                    </div>\r\n\r\n                    {/* Plan Status Row */}\r\n                    <div className=\"status-row\">\r\n                        <div className=\"status-item\">\r\n                            <span className=\"status-label\">Plan Status:</span>\r\n                            <Tag\r\n                                value={planDetails.planStatus || 'Not Started'}\r\n                                severity={getStatusSeverity(planDetails.planStatus)}\r\n                                icon={getStatusIcon(planDetails.planStatus)}\r\n                                className=\"status-tag\"\r\n                            />\r\n                        </div>\r\n                        <div className=\"status-item\">\r\n                            <span className=\"status-label\">Submission Date/Time:</span>\r\n                            <span className=\"status-value\">\r\n                                {planDetails.planSubmittedOn ? formatDateTime(planDetails.planSubmittedOn) : ''}\r\n                            </span>\r\n                        </div>\r\n                        <div className=\"status-item\">\r\n                            <span className=\"status-label\">Submitted by:</span>\r\n                            <span className=\"status-value\">\r\n                                {planDetails.planSubmittedByName || ''}\r\n                            </span>\r\n                        </div>\r\n                    </div>\r\n\r\n                    {/* Mid-Year Review Status Row */}\r\n                    <div className=\"status-row\">\r\n                        <div className=\"status-item\">\r\n                            <span className=\"status-label\">Mid-Year Review Status:</span>\r\n                            <Tag\r\n                                value={planDetails.midYearReviewStatus || 'Not Started'}\r\n                                severity={getStatusSeverity(planDetails.midYearReviewStatus)}\r\n                                icon={getStatusIcon(planDetails.midYearReviewStatus)}\r\n                                className=\"status-tag\"\r\n                            />\r\n                        </div>\r\n                        <div className=\"status-item\">\r\n                            <span className=\"status-label\">Submission Date/Time:</span>\r\n                            <span className=\"status-value\">\r\n                                {planDetails.midYearReviewSubmittedOn ? formatDateTime(planDetails.midYearReviewSubmittedOn) : ''}\r\n                            </span>\r\n                        </div>\r\n                        <div className=\"status-item\">\r\n                            <span className=\"status-label\">Submitted by:</span>\r\n                            <span className=\"status-value\">\r\n                                {planDetails.midYearReviewSubmittedByName || ''}\r\n                            </span>\r\n                        </div>\r\n                    </div>\r\n\r\n                    {/* Final Review Status Row */}\r\n                    <div className=\"status-row\">\r\n                        <div className=\"status-item\">\r\n                            <span className=\"status-label\">Final Review Status:</span>\r\n                            <Tag\r\n                                value={planDetails.finalReviewStatus || 'Not Started'}\r\n                                severity={getStatusSeverity(planDetails.finalReviewStatus)}\r\n                                icon={getStatusIcon(planDetails.finalReviewStatus)}\r\n                                className=\"status-tag\"\r\n                            />\r\n                        </div>\r\n                        <div className=\"status-item\">\r\n                            <span className=\"status-label\">Submission Date/Time:</span>\r\n                            <span className=\"status-value\">\r\n                                {planDetails.finalReviewSubmittedOn ? formatDateTime(planDetails.finalReviewSubmittedOn) : ''}\r\n                            </span>\r\n                        </div>\r\n                        <div className=\"status-item\">\r\n                            <span className=\"status-label\">Submitted by:</span>\r\n                            <span className=\"status-value\">\r\n                                {planDetails.finalReviewSubmittedByName || ''}\r\n                            </span>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            ) : (\r\n                <div className=\"error-container\">\r\n                    <p>Failed to load partner plan details.</p>\r\n                </div>\r\n            )}\r\n        </Dialog>\r\n    );\r\n};\r\n\r\nexport default PartnerPlanDetailsDialog;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,GAAG,QAAQ,gBAAgB;AACpC,OAAO,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,wBAAwB,GAAGA,CAAC;EAAEC,OAAO;EAAEC,MAAM;EAAEC,WAAW;EAAEC;AAAQ,CAAC,KAAK;EAC5E,IAAI,CAACD,WAAW,IAAI,CAACC,OAAO,EAAE;IAC1B,OAAO,IAAI;EACf;EAEA,MAAMC,cAAc,GAAIC,UAAU,IAAK;IACnC,IAAI,CAACA,UAAU,EAAE,OAAO,EAAE;IAC1B,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACpDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,SAAS;MAChBC,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE,SAAS;MACjBC,MAAM,EAAE;IACZ,CAAC,CAAC;EACN,CAAC;EAED,MAAMC,iBAAiB,GAAIC,MAAM,IAAK;IAClC,QAAQA,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEC,WAAW,CAAC,CAAC;MACzB,KAAK,WAAW;MAChB,KAAK,WAAW;QACZ,OAAO,SAAS;MACpB,KAAK,cAAc;MACnB,KAAK,WAAW;QACZ,OAAO,SAAS;MACpB,KAAK,aAAa;MAClB,KAAK,OAAO;QACR,OAAO,QAAQ;MACnB;QACI,OAAO,WAAW;IAC1B;EACJ,CAAC;EAED,MAAMC,aAAa,GAAIF,MAAM,IAAK;IAC9B,QAAQA,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEC,WAAW,CAAC,CAAC;MACzB,KAAK,WAAW;MAChB,KAAK,WAAW;QACZ,OAAO,oBAAoB;MAC/B,KAAK,cAAc;MACnB,KAAK,WAAW;QACZ,OAAO,aAAa;MACxB,KAAK,aAAa;MAClB,KAAK,OAAO;QACR,OAAO,4BAA4B;MACvC;QACI,OAAO,cAAc;IAC7B;EACJ,CAAC;EAED,oBACIlB,OAAA,CAACH,MAAM;IACHK,OAAO,EAAEA,OAAQ;IACjBC,MAAM,EAAEA,MAAO;IACfiB,MAAM,EAAC,6BAA6B;IACpCC,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAQ,CAAE;IAC1BC,KAAK;IACLC,SAAS,EAAC,6BAA6B;IACvCC,SAAS,EAAE,KAAM;IACjBC,SAAS,EAAE,KAAM;IACjBC,QAAQ,EAAE,IAAK;IAAAC,QAAA,EAEdvB,OAAO,gBACJL,OAAA;MAAKwB,SAAS,EAAC,mBAAmB;MAAAI,QAAA,gBAC9B5B,OAAA;QAAGwB,SAAS,EAAC,uBAAuB;QAACH,KAAK,EAAE;UAAEQ,QAAQ,EAAE;QAAO;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtEjC,OAAA;QAAA4B,QAAA,EAAG;MAA+B;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrC,CAAC,GACN7B,WAAW,gBACXJ,OAAA;MAAKwB,SAAS,EAAC,sBAAsB;MAAAI,QAAA,gBAEjC5B,OAAA;QAAKwB,SAAS,EAAC,YAAY;QAAAI,QAAA,gBACvB5B,OAAA;UAAKwB,SAAS,EAAC,aAAa;UAAAI,QAAA,gBACxB5B,OAAA;YAAMwB,SAAS,EAAC,cAAc;YAAAI,QAAA,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACnDjC,OAAA;YAAMwB,SAAS,EAAC,cAAc;YAAAI,QAAA,EAAExB,WAAW,CAAC8B,WAAW,IAAI;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpE,CAAC,eACNjC,OAAA;UAAKwB,SAAS,EAAC,aAAa;UAAAI,QAAA,gBACxB5B,OAAA;YAAMwB,SAAS,EAAC,cAAc;YAAAI,QAAA,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACnDjC,OAAA;YAAMwB,SAAS,EAAC,cAAc;YAAAI,QAAA,EAAExB,WAAW,CAAC+B,WAAW,IAAI;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpE,CAAC,eACNjC,OAAA;UAAKwB,SAAS,EAAC,aAAa;UAAAI,QAAA,gBACxB5B,OAAA;YAAMwB,SAAS,EAAC,cAAc;YAAAI,QAAA,EAAC;UAAiB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvDjC,OAAA;YAAMwB,SAAS,EAAC,cAAc;YAAAI,QAAA,EAAExB,WAAW,CAACgC,cAAc,IAAI;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvE,CAAC,eACNjC,OAAA;UAAKwB,SAAS,EAAC,aAAa;UAAAI,QAAA,gBACxB5B,OAAA;YAAMwB,SAAS,EAAC,cAAc;YAAAI,QAAA,EAAC;UAAK;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3CjC,OAAA;YAAMwB,SAAS,EAAC,cAAc;YAAAI,QAAA,EAAExB,WAAW,CAACM;UAAI;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNjC,OAAA;QAAKwB,SAAS,EAAC,eAAe;QAAAI,QAAA,gBAC1B5B,OAAA;UAAKwB,SAAS,EAAC,eAAe;UAAAI,QAAA,gBAC1B5B,OAAA;YAAMwB,SAAS,EAAC,gBAAgB;YAAAI,QAAA,EAAC;UAAiB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzDjC,OAAA;YAAMwB,SAAS,EAAC,gBAAgB;YAAAI,QAAA,EAAExB,WAAW,CAACiC,mBAAmB,IAAI;UAAE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9E,CAAC,eACNjC,OAAA;UAAKwB,SAAS,EAAC,eAAe;UAAAI,QAAA,gBAC1B5B,OAAA;YAAMwB,SAAS,EAAC,gBAAgB;YAAAI,QAAA,EAAC;UAAmB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3DjC,OAAA;YAAMwB,SAAS,EAAC,gBAAgB;YAAAI,QAAA,EAAExB,WAAW,CAACkC,qBAAqB,IAAI;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNjC,OAAA;QAAKwB,SAAS,EAAC,YAAY;QAAAI,QAAA,gBACvB5B,OAAA;UAAKwB,SAAS,EAAC,aAAa;UAAAI,QAAA,gBACxB5B,OAAA;YAAMwB,SAAS,EAAC,cAAc;YAAAI,QAAA,EAAC;UAAY;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAClDjC,OAAA,CAACF,GAAG;YACAyC,KAAK,EAAEnC,WAAW,CAACoC,UAAU,IAAI,aAAc;YAC/CC,QAAQ,EAAEzB,iBAAiB,CAACZ,WAAW,CAACoC,UAAU,CAAE;YACpDE,IAAI,EAAEvB,aAAa,CAACf,WAAW,CAACoC,UAAU,CAAE;YAC5ChB,SAAS,EAAC;UAAY;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNjC,OAAA;UAAKwB,SAAS,EAAC,aAAa;UAAAI,QAAA,gBACxB5B,OAAA;YAAMwB,SAAS,EAAC,cAAc;YAAAI,QAAA,EAAC;UAAqB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3DjC,OAAA;YAAMwB,SAAS,EAAC,cAAc;YAAAI,QAAA,EACzBxB,WAAW,CAACuC,eAAe,GAAGrC,cAAc,CAACF,WAAW,CAACuC,eAAe,CAAC,GAAG;UAAE;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNjC,OAAA;UAAKwB,SAAS,EAAC,aAAa;UAAAI,QAAA,gBACxB5B,OAAA;YAAMwB,SAAS,EAAC,cAAc;YAAAI,QAAA,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACnDjC,OAAA;YAAMwB,SAAS,EAAC,cAAc;YAAAI,QAAA,EACzBxB,WAAW,CAACwC,mBAAmB,IAAI;UAAE;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNjC,OAAA;QAAKwB,SAAS,EAAC,YAAY;QAAAI,QAAA,gBACvB5B,OAAA;UAAKwB,SAAS,EAAC,aAAa;UAAAI,QAAA,gBACxB5B,OAAA;YAAMwB,SAAS,EAAC,cAAc;YAAAI,QAAA,EAAC;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7DjC,OAAA,CAACF,GAAG;YACAyC,KAAK,EAAEnC,WAAW,CAACyC,mBAAmB,IAAI,aAAc;YACxDJ,QAAQ,EAAEzB,iBAAiB,CAACZ,WAAW,CAACyC,mBAAmB,CAAE;YAC7DH,IAAI,EAAEvB,aAAa,CAACf,WAAW,CAACyC,mBAAmB,CAAE;YACrDrB,SAAS,EAAC;UAAY;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNjC,OAAA;UAAKwB,SAAS,EAAC,aAAa;UAAAI,QAAA,gBACxB5B,OAAA;YAAMwB,SAAS,EAAC,cAAc;YAAAI,QAAA,EAAC;UAAqB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3DjC,OAAA;YAAMwB,SAAS,EAAC,cAAc;YAAAI,QAAA,EACzBxB,WAAW,CAAC0C,wBAAwB,GAAGxC,cAAc,CAACF,WAAW,CAAC0C,wBAAwB,CAAC,GAAG;UAAE;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/F,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNjC,OAAA;UAAKwB,SAAS,EAAC,aAAa;UAAAI,QAAA,gBACxB5B,OAAA;YAAMwB,SAAS,EAAC,cAAc;YAAAI,QAAA,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACnDjC,OAAA;YAAMwB,SAAS,EAAC,cAAc;YAAAI,QAAA,EACzBxB,WAAW,CAAC2C,4BAA4B,IAAI;UAAE;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNjC,OAAA;QAAKwB,SAAS,EAAC,YAAY;QAAAI,QAAA,gBACvB5B,OAAA;UAAKwB,SAAS,EAAC,aAAa;UAAAI,QAAA,gBACxB5B,OAAA;YAAMwB,SAAS,EAAC,cAAc;YAAAI,QAAA,EAAC;UAAoB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1DjC,OAAA,CAACF,GAAG;YACAyC,KAAK,EAAEnC,WAAW,CAAC4C,iBAAiB,IAAI,aAAc;YACtDP,QAAQ,EAAEzB,iBAAiB,CAACZ,WAAW,CAAC4C,iBAAiB,CAAE;YAC3DN,IAAI,EAAEvB,aAAa,CAACf,WAAW,CAAC4C,iBAAiB,CAAE;YACnDxB,SAAS,EAAC;UAAY;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNjC,OAAA;UAAKwB,SAAS,EAAC,aAAa;UAAAI,QAAA,gBACxB5B,OAAA;YAAMwB,SAAS,EAAC,cAAc;YAAAI,QAAA,EAAC;UAAqB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3DjC,OAAA;YAAMwB,SAAS,EAAC,cAAc;YAAAI,QAAA,EACzBxB,WAAW,CAAC6C,sBAAsB,GAAG3C,cAAc,CAACF,WAAW,CAAC6C,sBAAsB,CAAC,GAAG;UAAE;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3F,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNjC,OAAA;UAAKwB,SAAS,EAAC,aAAa;UAAAI,QAAA,gBACxB5B,OAAA;YAAMwB,SAAS,EAAC,cAAc;YAAAI,QAAA,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACnDjC,OAAA;YAAMwB,SAAS,EAAC,cAAc;YAAAI,QAAA,EACzBxB,WAAW,CAAC8C,0BAA0B,IAAI;UAAE;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,gBAENjC,OAAA;MAAKwB,SAAS,EAAC,iBAAiB;MAAAI,QAAA,eAC5B5B,OAAA;QAAA4B,QAAA,EAAG;MAAoC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C;EACR;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEjB,CAAC;AAACkB,EAAA,GAtLIlD,wBAAwB;AAwL9B,eAAeA,wBAAwB;AAAC,IAAAkD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}