{"ast": null, "code": "import { ArgumentOutOfRangeError } from '../util/ArgumentOutOfRangeError';\nimport { filter } from './filter';\nimport { throwIfEmpty } from './throwIfEmpty';\nimport { defaultIfEmpty } from './defaultIfEmpty';\nimport { take } from './take';\nexport function elementAt(index, defaultValue) {\n  if (index < 0) {\n    throw new ArgumentOutOfRangeError();\n  }\n  var hasDefaultValue = arguments.length >= 2;\n  return function (source) {\n    return source.pipe(filter(function (v, i) {\n      return i === index;\n    }), take(1), hasDefaultValue ? defaultIfEmpty(defaultValue) : throwIfEmpty(function () {\n      return new ArgumentOutOfRangeError();\n    }));\n  };\n}", "map": {"version": 3, "names": ["ArgumentOutOfRangeError", "filter", "throwIfEmpty", "defaultIfEmpty", "take", "elementAt", "index", "defaultValue", "hasDefaultValue", "arguments", "length", "source", "pipe", "v", "i"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\elementAt.ts"], "sourcesContent": ["import { ArgumentOutOfRangeError } from '../util/ArgumentOutOfRangeError';\nimport { Observable } from '../Observable';\nimport { OperatorFunction } from '../types';\nimport { filter } from './filter';\nimport { throwIfEmpty } from './throwIfEmpty';\nimport { defaultIfEmpty } from './defaultIfEmpty';\nimport { take } from './take';\n\n/**\n * Emits the single value at the specified `index` in a sequence of emissions\n * from the source Observable.\n *\n * <span class=\"informal\">Emits only the i-th value, then completes.</span>\n *\n * ![](elementAt.png)\n *\n * `elementAt` returns an Observable that emits the item at the specified\n * `index` in the source Observable, or a default value if that `index` is out\n * of range and the `default` argument is provided. If the `default` argument is\n * not given and the `index` is out of range, the output Observable will emit an\n * `ArgumentOutOfRangeError` error.\n *\n * ## Example\n *\n * Emit only the third click event\n *\n * ```ts\n * import { fromEvent, elementAt } from 'rxjs';\n *\n * const clicks = fromEvent(document, 'click');\n * const result = clicks.pipe(elementAt(2));\n * result.subscribe(x => console.log(x));\n *\n * // Results in:\n * // click 1 = nothing\n * // click 2 = nothing\n * // click 3 = MouseEvent object logged to console\n * ```\n *\n * @see {@link first}\n * @see {@link last}\n * @see {@link skip}\n * @see {@link single}\n * @see {@link take}\n *\n * @throws {ArgumentOutOfRangeError} When using `elementAt(i)`, it delivers an\n * `ArgumentOutOfRangeError` to the Observer's `error` callback if `i < 0` or the\n * Observable has completed before emitting the i-th `next` notification.\n *\n * @param index Is the number `i` for the i-th source emission that has happened\n * since the subscription, starting from the number `0`.\n * @param defaultValue The default value returned for missing indices.\n * @return A function that returns an Observable that emits a single item, if\n * it is found. Otherwise, it will emit the default value if given. If not, it\n * emits an error.\n */\nexport function elementAt<T, D = T>(index: number, defaultValue?: D): OperatorFunction<T, T | D> {\n  if (index < 0) {\n    throw new ArgumentOutOfRangeError();\n  }\n  const hasDefaultValue = arguments.length >= 2;\n  return (source: Observable<T>) =>\n    source.pipe(\n      filter((v, i) => i === index),\n      take(1),\n      hasDefaultValue ? defaultIfEmpty(defaultValue!) : throwIfEmpty(() => new ArgumentOutOfRangeError())\n    );\n}\n"], "mappings": "AAAA,SAASA,uBAAuB,QAAQ,iCAAiC;AAGzE,SAASC,MAAM,QAAQ,UAAU;AACjC,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,IAAI,QAAQ,QAAQ;AAkD7B,OAAM,SAAUC,SAASA,CAAWC,KAAa,EAAEC,YAAgB;EACjE,IAAID,KAAK,GAAG,CAAC,EAAE;IACb,MAAM,IAAIN,uBAAuB,EAAE;;EAErC,IAAMQ,eAAe,GAAGC,SAAS,CAACC,MAAM,IAAI,CAAC;EAC7C,OAAO,UAACC,MAAqB;IAC3B,OAAAA,MAAM,CAACC,IAAI,CACTX,MAAM,CAAC,UAACY,CAAC,EAAEC,CAAC;MAAK,OAAAA,CAAC,KAAKR,KAAK;IAAX,CAAW,CAAC,EAC7BF,IAAI,CAAC,CAAC,CAAC,EACPI,eAAe,GAAGL,cAAc,CAACI,YAAa,CAAC,GAAGL,YAAY,CAAC;MAAM,WAAIF,uBAAuB,EAAE;IAA7B,CAA6B,CAAC,CACpG;EAJD,CAIC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}