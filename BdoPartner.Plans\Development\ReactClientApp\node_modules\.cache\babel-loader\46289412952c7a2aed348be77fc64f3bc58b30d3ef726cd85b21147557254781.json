{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { Subject } from './Subject';\nvar AsyncSubject = function (_super) {\n  __extends(AsyncSubject, _super);\n  function AsyncSubject() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this._value = null;\n    _this._hasValue = false;\n    _this._isComplete = false;\n    return _this;\n  }\n  AsyncSubject.prototype._checkFinalizedStatuses = function (subscriber) {\n    var _a = this,\n      hasError = _a.hasError,\n      _hasValue = _a._hasValue,\n      _value = _a._value,\n      thrownError = _a.thrownError,\n      isStopped = _a.isStopped,\n      _isComplete = _a._isComplete;\n    if (hasError) {\n      subscriber.error(thrownError);\n    } else if (isStopped || _isComplete) {\n      _hasValue && subscriber.next(_value);\n      subscriber.complete();\n    }\n  };\n  AsyncSubject.prototype.next = function (value) {\n    if (!this.isStopped) {\n      this._value = value;\n      this._hasValue = true;\n    }\n  };\n  AsyncSubject.prototype.complete = function () {\n    var _a = this,\n      _hasValue = _a._hasValue,\n      _value = _a._value,\n      _isComplete = _a._isComplete;\n    if (!_isComplete) {\n      this._isComplete = true;\n      _hasValue && _super.prototype.next.call(this, _value);\n      _super.prototype.complete.call(this);\n    }\n  };\n  return AsyncSubject;\n}(Subject);\nexport { AsyncSubject };", "map": {"version": 3, "names": ["Subject", "AsyncSubject", "_super", "__extends", "_this", "apply", "arguments", "_value", "_hasValue", "_isComplete", "prototype", "_checkFinalizedStatuses", "subscriber", "_a", "<PERSON><PERSON><PERSON><PERSON>", "thrownError", "isStopped", "error", "next", "complete", "value", "call"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\AsyncSubject.ts"], "sourcesContent": ["import { Subject } from './Subject';\nimport { Subscriber } from './Subscriber';\n\n/**\n * A variant of Subject that only emits a value when it completes. It will emit\n * its latest value to all its observers on completion.\n */\nexport class AsyncSubject<T> extends Subject<T> {\n  private _value: T | null = null;\n  private _hasValue = false;\n  private _isComplete = false;\n\n  /** @internal */\n  protected _checkFinalizedStatuses(subscriber: Subscriber<T>) {\n    const { hasError, _hasValue, _value, thrownError, isStopped, _isComplete } = this;\n    if (hasError) {\n      subscriber.error(thrownError);\n    } else if (isStopped || _isComplete) {\n      _hasValue && subscriber.next(_value!);\n      subscriber.complete();\n    }\n  }\n\n  next(value: T): void {\n    if (!this.isStopped) {\n      this._value = value;\n      this._hasValue = true;\n    }\n  }\n\n  complete(): void {\n    const { _hasValue, _value, _isComplete } = this;\n    if (!_isComplete) {\n      this._isComplete = true;\n      _hasValue && super.next(_value!);\n      super.complete();\n    }\n  }\n}\n"], "mappings": ";AAAA,SAASA,OAAO,QAAQ,WAAW;AAOnC,IAAAC,YAAA,aAAAC,MAAA;EAAqCC,SAAA,CAAAF,YAAA,EAAAC,MAAA;EAArC,SAAAD,aAAA;IAAA,IAAAG,KAAA,GAAAF,MAAA,aAAAA,MAAA,CAAAG,KAAA,OAAAC,SAAA;IACUF,KAAA,CAAAG,MAAM,GAAa,IAAI;IACvBH,KAAA,CAAAI,SAAS,GAAG,KAAK;IACjBJ,KAAA,CAAAK,WAAW,GAAG,KAAK;;EA4B7B;EAzBYR,YAAA,CAAAS,SAAA,CAAAC,uBAAuB,GAAjC,UAAkCC,UAAyB;IACnD,IAAAC,EAAA,GAAuE,IAAI;MAAzEC,QAAQ,GAAAD,EAAA,CAAAC,QAAA;MAAEN,SAAS,GAAAK,EAAA,CAAAL,SAAA;MAAED,MAAM,GAAAM,EAAA,CAAAN,MAAA;MAAEQ,WAAW,GAAAF,EAAA,CAAAE,WAAA;MAAEC,SAAS,GAAAH,EAAA,CAAAG,SAAA;MAAEP,WAAW,GAAAI,EAAA,CAAAJ,WAAS;IACjF,IAAIK,QAAQ,EAAE;MACZF,UAAU,CAACK,KAAK,CAACF,WAAW,CAAC;KAC9B,MAAM,IAAIC,SAAS,IAAIP,WAAW,EAAE;MACnCD,SAAS,IAAII,UAAU,CAACM,IAAI,CAACX,MAAO,CAAC;MACrCK,UAAU,CAACO,QAAQ,EAAE;;EAEzB,CAAC;EAEDlB,YAAA,CAAAS,SAAA,CAAAQ,IAAI,GAAJ,UAAKE,KAAQ;IACX,IAAI,CAAC,IAAI,CAACJ,SAAS,EAAE;MACnB,IAAI,CAACT,MAAM,GAAGa,KAAK;MACnB,IAAI,CAACZ,SAAS,GAAG,IAAI;;EAEzB,CAAC;EAEDP,YAAA,CAAAS,SAAA,CAAAS,QAAQ,GAAR;IACQ,IAAAN,EAAA,GAAqC,IAAI;MAAvCL,SAAS,GAAAK,EAAA,CAAAL,SAAA;MAAED,MAAM,GAAAM,EAAA,CAAAN,MAAA;MAAEE,WAAW,GAAAI,EAAA,CAAAJ,WAAS;IAC/C,IAAI,CAACA,WAAW,EAAE;MAChB,IAAI,CAACA,WAAW,GAAG,IAAI;MACvBD,SAAS,IAAIN,MAAA,CAAAQ,SAAA,CAAMQ,IAAI,CAAAG,IAAA,OAACd,MAAO,CAAC;MAChCL,MAAA,CAAAQ,SAAA,CAAMS,QAAQ,CAAAE,IAAA,MAAE;;EAEpB,CAAC;EACH,OAAApB,YAAC;AAAD,CAAC,CA/BoCD,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}