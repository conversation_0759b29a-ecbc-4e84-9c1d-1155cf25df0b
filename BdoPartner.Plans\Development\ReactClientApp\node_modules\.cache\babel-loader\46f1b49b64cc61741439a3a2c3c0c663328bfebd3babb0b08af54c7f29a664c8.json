{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { Subject } from './Subject';\nvar BehaviorSubject = function (_super) {\n  __extends(BehaviorSubject, _super);\n  function BehaviorSubject(_value) {\n    var _this = _super.call(this) || this;\n    _this._value = _value;\n    return _this;\n  }\n  Object.defineProperty(BehaviorSubject.prototype, \"value\", {\n    get: function () {\n      return this.getValue();\n    },\n    enumerable: false,\n    configurable: true\n  });\n  BehaviorSubject.prototype._subscribe = function (subscriber) {\n    var subscription = _super.prototype._subscribe.call(this, subscriber);\n    !subscription.closed && subscriber.next(this._value);\n    return subscription;\n  };\n  BehaviorSubject.prototype.getValue = function () {\n    var _a = this,\n      hasError = _a.hasError,\n      thrownError = _a.thrownError,\n      _value = _a._value;\n    if (hasError) {\n      throw thrownError;\n    }\n    this._throwIfClosed();\n    return _value;\n  };\n  BehaviorSubject.prototype.next = function (value) {\n    _super.prototype.next.call(this, this._value = value);\n  };\n  return BehaviorSubject;\n}(Subject);\nexport { BehaviorSubject };", "map": {"version": 3, "names": ["Subject", "BehaviorSubject", "_super", "__extends", "_value", "_this", "call", "Object", "defineProperty", "prototype", "get", "getValue", "_subscribe", "subscriber", "subscription", "closed", "next", "_a", "<PERSON><PERSON><PERSON><PERSON>", "thrownError", "_throwIfClosed", "value"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\BehaviorSubject.ts"], "sourcesContent": ["import { Subject } from './Subject';\nimport { Subscriber } from './Subscriber';\nimport { Subscription } from './Subscription';\n\n/**\n * A variant of Subject that requires an initial value and emits its current\n * value whenever it is subscribed to.\n */\nexport class BehaviorSubject<T> extends Subject<T> {\n  constructor(private _value: T) {\n    super();\n  }\n\n  get value(): T {\n    return this.getValue();\n  }\n\n  /** @internal */\n  protected _subscribe(subscriber: Subscriber<T>): Subscription {\n    const subscription = super._subscribe(subscriber);\n    !subscription.closed && subscriber.next(this._value);\n    return subscription;\n  }\n\n  getValue(): T {\n    const { hasError, thrownError, _value } = this;\n    if (hasError) {\n      throw thrownError;\n    }\n    this._throwIfClosed();\n    return _value;\n  }\n\n  next(value: T): void {\n    super.next((this._value = value));\n  }\n}\n"], "mappings": ";AAAA,SAASA,OAAO,QAAQ,WAAW;AAQnC,IAAAC,eAAA,aAAAC,MAAA;EAAwCC,SAAA,CAAAF,eAAA,EAAAC,MAAA;EACtC,SAAAD,gBAAoBG,MAAS;IAA7B,IAAAC,KAAA,GACEH,MAAA,CAAAI,IAAA,MAAO;IADWD,KAAA,CAAAD,MAAM,GAANA,MAAM;;EAE1B;EAEAG,MAAA,CAAAC,cAAA,CAAIP,eAAA,CAAAQ,SAAA,SAAK;SAAT,SAAAC,CAAA;MACE,OAAO,IAAI,CAACC,QAAQ,EAAE;IACxB,CAAC;;;;EAGSV,eAAA,CAAAQ,SAAA,CAAAG,UAAU,GAApB,UAAqBC,UAAyB;IAC5C,IAAMC,YAAY,GAAGZ,MAAA,CAAAO,SAAA,CAAMG,UAAU,CAAAN,IAAA,OAACO,UAAU,CAAC;IACjD,CAACC,YAAY,CAACC,MAAM,IAAIF,UAAU,CAACG,IAAI,CAAC,IAAI,CAACZ,MAAM,CAAC;IACpD,OAAOU,YAAY;EACrB,CAAC;EAEDb,eAAA,CAAAQ,SAAA,CAAAE,QAAQ,GAAR;IACQ,IAAAM,EAAA,GAAoC,IAAI;MAAtCC,QAAQ,GAAAD,EAAA,CAAAC,QAAA;MAAEC,WAAW,GAAAF,EAAA,CAAAE,WAAA;MAAEf,MAAM,GAAAa,EAAA,CAAAb,MAAS;IAC9C,IAAIc,QAAQ,EAAE;MACZ,MAAMC,WAAW;;IAEnB,IAAI,CAACC,cAAc,EAAE;IACrB,OAAOhB,MAAM;EACf,CAAC;EAEDH,eAAA,CAAAQ,SAAA,CAAAO,IAAI,GAAJ,UAAKK,KAAQ;IACXnB,MAAA,CAAAO,SAAA,CAAMO,IAAI,CAAAV,IAAA,OAAE,IAAI,CAACF,MAAM,GAAGiB,KAAM,CAAC;EACnC,CAAC;EACH,OAAApB,eAAC;AAAD,CAAC,CA5BuCD,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}