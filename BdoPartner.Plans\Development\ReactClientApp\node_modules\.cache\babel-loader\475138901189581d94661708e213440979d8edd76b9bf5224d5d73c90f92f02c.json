{"ast": null, "code": "import { innerFrom } from '../observable/innerFrom';\nimport { executeSchedule } from '../util/executeSchedule';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function mergeInternals(source, subscriber, project, concurrent, onBeforeNext, expand, innerSubScheduler, additionalFinalizer) {\n  var buffer = [];\n  var active = 0;\n  var index = 0;\n  var isComplete = false;\n  var checkComplete = function () {\n    if (isComplete && !buffer.length && !active) {\n      subscriber.complete();\n    }\n  };\n  var outerNext = function (value) {\n    return active < concurrent ? doInnerSub(value) : buffer.push(value);\n  };\n  var doInnerSub = function (value) {\n    expand && subscriber.next(value);\n    active++;\n    var innerComplete = false;\n    innerFrom(project(value, index++)).subscribe(createOperatorSubscriber(subscriber, function (innerValue) {\n      onBeforeNext === null || onBeforeNext === void 0 ? void 0 : onBeforeNext(innerValue);\n      if (expand) {\n        outerNext(innerValue);\n      } else {\n        subscriber.next(innerValue);\n      }\n    }, function () {\n      innerComplete = true;\n    }, undefined, function () {\n      if (innerComplete) {\n        try {\n          active--;\n          var _loop_1 = function () {\n            var bufferedValue = buffer.shift();\n            if (innerSubScheduler) {\n              executeSchedule(subscriber, innerSubScheduler, function () {\n                return doInnerSub(bufferedValue);\n              });\n            } else {\n              doInnerSub(bufferedValue);\n            }\n          };\n          while (buffer.length && active < concurrent) {\n            _loop_1();\n          }\n          checkComplete();\n        } catch (err) {\n          subscriber.error(err);\n        }\n      }\n    }));\n  };\n  source.subscribe(createOperatorSubscriber(subscriber, outerNext, function () {\n    isComplete = true;\n    checkComplete();\n  }));\n  return function () {\n    additionalFinalizer === null || additionalFinalizer === void 0 ? void 0 : additionalFinalizer();\n  };\n}", "map": {"version": 3, "names": ["innerFrom", "executeSchedule", "createOperatorSubscriber", "mergeInternals", "source", "subscriber", "project", "concurrent", "onBeforeNext", "expand", "innerSubScheduler", "additionalFinalizer", "buffer", "active", "index", "isComplete", "checkComplete", "length", "complete", "outerNext", "value", "doInnerSub", "push", "next", "innerComplete", "subscribe", "innerValue", "undefined", "bufferedValue", "shift", "err", "error"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\mergeInternals.ts"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { innerFrom } from '../observable/innerFrom';\nimport { Subscriber } from '../Subscriber';\nimport { ObservableInput, SchedulerLike } from '../types';\nimport { executeSchedule } from '../util/executeSchedule';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\n\n/**\n * A process embodying the general \"merge\" strategy. This is used in\n * `mergeMap` and `mergeScan` because the logic is otherwise nearly identical.\n * @param source The original source observable\n * @param subscriber The consumer subscriber\n * @param project The projection function to get our inner sources\n * @param concurrent The number of concurrent inner subscriptions\n * @param onBeforeNext Additional logic to apply before nexting to our consumer\n * @param expand If `true` this will perform an \"expand\" strategy, which differs only\n * in that it recurses, and the inner subscription must be schedule-able.\n * @param innerSubScheduler A scheduler to use to schedule inner subscriptions,\n * this is to support the expand strategy, mostly, and should be deprecated\n */\nexport function mergeInternals<T, R>(\n  source: Observable<T>,\n  subscriber: Subscriber<R>,\n  project: (value: T, index: number) => ObservableInput<R>,\n  concurrent: number,\n  onBeforeNext?: (innerValue: R) => void,\n  expand?: boolean,\n  innerSubScheduler?: SchedulerLike,\n  additionalFinalizer?: () => void\n) {\n  // Buffered values, in the event of going over our concurrency limit\n  const buffer: T[] = [];\n  // The number of active inner subscriptions.\n  let active = 0;\n  // An index to pass to our accumulator function\n  let index = 0;\n  // Whether or not the outer source has completed.\n  let isComplete = false;\n\n  /**\n   * Checks to see if we can complete our result or not.\n   */\n  const checkComplete = () => {\n    // If the outer has completed, and nothing is left in the buffer,\n    // and we don't have any active inner subscriptions, then we can\n    // Emit the state and complete.\n    if (isComplete && !buffer.length && !active) {\n      subscriber.complete();\n    }\n  };\n\n  // If we're under our concurrency limit, just start the inner subscription, otherwise buffer and wait.\n  const outerNext = (value: T) => (active < concurrent ? doInnerSub(value) : buffer.push(value));\n\n  const doInnerSub = (value: T) => {\n    // If we're expanding, we need to emit the outer values and the inner values\n    // as the inners will \"become outers\" in a way as they are recursively fed\n    // back to the projection mechanism.\n    expand && subscriber.next(value as any);\n\n    // Increment the number of active subscriptions so we can track it\n    // against our concurrency limit later.\n    active++;\n\n    // A flag used to show that the inner observable completed.\n    // This is checked during finalization to see if we should\n    // move to the next item in the buffer, if there is on.\n    let innerComplete = false;\n\n    // Start our inner subscription.\n    innerFrom(project(value, index++)).subscribe(\n      createOperatorSubscriber(\n        subscriber,\n        (innerValue) => {\n          // `mergeScan` has additional handling here. For example\n          // taking the inner value and updating state.\n          onBeforeNext?.(innerValue);\n\n          if (expand) {\n            // If we're expanding, then just recurse back to our outer\n            // handler. It will emit the value first thing.\n            outerNext(innerValue as any);\n          } else {\n            // Otherwise, emit the inner value.\n            subscriber.next(innerValue);\n          }\n        },\n        () => {\n          // Flag that we have completed, so we know to check the buffer\n          // during finalization.\n          innerComplete = true;\n        },\n        // Errors are passed to the destination.\n        undefined,\n        () => {\n          // During finalization, if the inner completed (it wasn't errored or\n          // cancelled), then we want to try the next item in the buffer if\n          // there is one.\n          if (innerComplete) {\n            // We have to wrap this in a try/catch because it happens during\n            // finalization, possibly asynchronously, and we want to pass\n            // any errors that happen (like in a projection function) to\n            // the outer Subscriber.\n            try {\n              // INNER SOURCE COMPLETE\n              // Decrement the active count to ensure that the next time\n              // we try to call `doInnerSub`, the number is accurate.\n              active--;\n              // If we have more values in the buffer, try to process those\n              // Note that this call will increment `active` ahead of the\n              // next conditional, if there were any more inner subscriptions\n              // to start.\n              while (buffer.length && active < concurrent) {\n                const bufferedValue = buffer.shift()!;\n                // Particularly for `expand`, we need to check to see if a scheduler was provided\n                // for when we want to start our inner subscription. Otherwise, we just start\n                // are next inner subscription.\n                if (innerSubScheduler) {\n                  executeSchedule(subscriber, innerSubScheduler, () => doInnerSub(bufferedValue));\n                } else {\n                  doInnerSub(bufferedValue);\n                }\n              }\n              // Check to see if we can complete, and complete if so.\n              checkComplete();\n            } catch (err) {\n              subscriber.error(err);\n            }\n          }\n        }\n      )\n    );\n  };\n\n  // Subscribe to our source observable.\n  source.subscribe(\n    createOperatorSubscriber(subscriber, outerNext, () => {\n      // Outer completed, make a note of it, and check to see if we can complete everything.\n      isComplete = true;\n      checkComplete();\n    })\n  );\n\n  // Additional finalization (for when the destination is torn down).\n  // Other finalization is added implicitly via subscription above.\n  return () => {\n    additionalFinalizer?.();\n  };\n}\n"], "mappings": "AACA,SAASA,SAAS,QAAQ,yBAAyB;AAGnD,SAASC,eAAe,QAAQ,yBAAyB;AACzD,SAASC,wBAAwB,QAAQ,sBAAsB;AAe/D,OAAM,SAAUC,cAAcA,CAC5BC,MAAqB,EACrBC,UAAyB,EACzBC,OAAwD,EACxDC,UAAkB,EAClBC,YAAsC,EACtCC,MAAgB,EAChBC,iBAAiC,EACjCC,mBAAgC;EAGhC,IAAMC,MAAM,GAAQ,EAAE;EAEtB,IAAIC,MAAM,GAAG,CAAC;EAEd,IAAIC,KAAK,GAAG,CAAC;EAEb,IAAIC,UAAU,GAAG,KAAK;EAKtB,IAAMC,aAAa,GAAG,SAAAA,CAAA;IAIpB,IAAID,UAAU,IAAI,CAACH,MAAM,CAACK,MAAM,IAAI,CAACJ,MAAM,EAAE;MAC3CR,UAAU,CAACa,QAAQ,EAAE;;EAEzB,CAAC;EAGD,IAAMC,SAAS,GAAG,SAAAA,CAACC,KAAQ;IAAK,OAACP,MAAM,GAAGN,UAAU,GAAGc,UAAU,CAACD,KAAK,CAAC,GAAGR,MAAM,CAACU,IAAI,CAACF,KAAK,CAAC;EAA7D,CAA8D;EAE9F,IAAMC,UAAU,GAAG,SAAAA,CAACD,KAAQ;IAI1BX,MAAM,IAAIJ,UAAU,CAACkB,IAAI,CAACH,KAAY,CAAC;IAIvCP,MAAM,EAAE;IAKR,IAAIW,aAAa,GAAG,KAAK;IAGzBxB,SAAS,CAACM,OAAO,CAACc,KAAK,EAAEN,KAAK,EAAE,CAAC,CAAC,CAACW,SAAS,CAC1CvB,wBAAwB,CACtBG,UAAU,EACV,UAACqB,UAAU;MAGTlB,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAGkB,UAAU,CAAC;MAE1B,IAAIjB,MAAM,EAAE;QAGVU,SAAS,CAACO,UAAiB,CAAC;OAC7B,MAAM;QAELrB,UAAU,CAACkB,IAAI,CAACG,UAAU,CAAC;;IAE/B,CAAC,EACD;MAGEF,aAAa,GAAG,IAAI;IACtB,CAAC,EAEDG,SAAS,EACT;MAIE,IAAIH,aAAa,EAAE;QAKjB,IAAI;UAIFX,MAAM,EAAE;;YAMN,IAAMe,aAAa,GAAGhB,MAAM,CAACiB,KAAK,EAAG;YAIrC,IAAInB,iBAAiB,EAAE;cACrBT,eAAe,CAACI,UAAU,EAAEK,iBAAiB,EAAE;gBAAM,OAAAW,UAAU,CAACO,aAAa,CAAC;cAAzB,CAAyB,CAAC;aAChF,MAAM;cACLP,UAAU,CAACO,aAAa,CAAC;;;UAR7B,OAAOhB,MAAM,CAACK,MAAM,IAAIJ,MAAM,GAAGN,UAAU;;;UAY3CS,aAAa,EAAE;SAChB,CAAC,OAAOc,GAAG,EAAE;UACZzB,UAAU,CAAC0B,KAAK,CAACD,GAAG,CAAC;;;IAG3B,CAAC,CACF,CACF;EACH,CAAC;EAGD1B,MAAM,CAACqB,SAAS,CACdvB,wBAAwB,CAACG,UAAU,EAAEc,SAAS,EAAE;IAE9CJ,UAAU,GAAG,IAAI;IACjBC,aAAa,EAAE;EACjB,CAAC,CAAC,CACH;EAID,OAAO;IACLL,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,EAAI;EACzB,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}