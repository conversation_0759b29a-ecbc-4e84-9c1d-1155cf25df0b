{"ast": null, "code": "import { operate } from '../util/lift';\nimport { noop } from '../util/noop';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nexport function buffer(closingNotifier) {\n  return operate(function (source, subscriber) {\n    var currentBuffer = [];\n    source.subscribe(createOperatorSubscriber(subscriber, function (value) {\n      return currentBuffer.push(value);\n    }, function () {\n      subscriber.next(currentBuffer);\n      subscriber.complete();\n    }));\n    innerFrom(closingNotifier).subscribe(createOperatorSubscriber(subscriber, function () {\n      var b = currentBuffer;\n      currentBuffer = [];\n      subscriber.next(b);\n    }, noop));\n    return function () {\n      currentBuffer = null;\n    };\n  });\n}", "map": {"version": 3, "names": ["operate", "noop", "createOperatorSubscriber", "innerFrom", "buffer", "closingNotifier", "source", "subscriber", "current<PERSON><PERSON><PERSON>", "subscribe", "value", "push", "next", "complete", "b"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\buffer.ts"], "sourcesContent": ["import { OperatorFunction, ObservableInput } from '../types';\nimport { operate } from '../util/lift';\nimport { noop } from '../util/noop';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\n\n/**\n * Buffers the source Observable values until `closingNotifier` emits.\n *\n * <span class=\"informal\">Collects values from the past as an array, and emits\n * that array only when another Observable emits.</span>\n *\n * ![](buffer.png)\n *\n * Buffers the incoming Observable values until the given `closingNotifier`\n * `ObservableInput` (that internally gets converted to an Observable)\n * emits a value, at which point it emits the buffer on the output\n * Observable and starts a new buffer internally, awaiting the next time\n * `closingNotifier` emits.\n *\n * ## Example\n *\n * On every click, emit array of most recent interval events\n *\n * ```ts\n * import { fromEvent, interval, buffer } from 'rxjs';\n *\n * const clicks = fromEvent(document, 'click');\n * const intervalEvents = interval(1000);\n * const buffered = intervalEvents.pipe(buffer(clicks));\n * buffered.subscribe(x => console.log(x));\n * ```\n *\n * @see {@link bufferCount}\n * @see {@link bufferTime}\n * @see {@link bufferToggle}\n * @see {@link bufferWhen}\n * @see {@link window}\n *\n * @param closingNotifier An `ObservableInput` that signals the\n * buffer to be emitted on the output Observable.\n * @return A function that returns an Observable of buffers, which are arrays\n * of values.\n */\nexport function buffer<T>(closingNotifier: ObservableInput<any>): OperatorFunction<T, T[]> {\n  return operate((source, subscriber) => {\n    // The current buffered values.\n    let currentBuffer: T[] = [];\n\n    // Subscribe to our source.\n    source.subscribe(\n      createOperatorSubscriber(\n        subscriber,\n        (value) => currentBuffer.push(value),\n        () => {\n          subscriber.next(currentBuffer);\n          subscriber.complete();\n        }\n      )\n    );\n\n    // Subscribe to the closing notifier.\n    innerFrom(closingNotifier).subscribe(\n      createOperatorSubscriber(\n        subscriber,\n        () => {\n          // Start a new buffer and emit the previous one.\n          const b = currentBuffer;\n          currentBuffer = [];\n          subscriber.next(b);\n        },\n        noop\n      )\n    );\n\n    return () => {\n      // Ensure buffered values are released on finalization.\n      currentBuffer = null!;\n    };\n  });\n}\n"], "mappings": "AACA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,IAAI,QAAQ,cAAc;AACnC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,SAASC,SAAS,QAAQ,yBAAyB;AAwCnD,OAAM,SAAUC,MAAMA,CAAIC,eAAqC;EAC7D,OAAOL,OAAO,CAAC,UAACM,MAAM,EAAEC,UAAU;IAEhC,IAAIC,aAAa,GAAQ,EAAE;IAG3BF,MAAM,CAACG,SAAS,CACdP,wBAAwB,CACtBK,UAAU,EACV,UAACG,KAAK;MAAK,OAAAF,aAAa,CAACG,IAAI,CAACD,KAAK,CAAC;IAAzB,CAAyB,EACpC;MACEH,UAAU,CAACK,IAAI,CAACJ,aAAa,CAAC;MAC9BD,UAAU,CAACM,QAAQ,EAAE;IACvB,CAAC,CACF,CACF;IAGDV,SAAS,CAACE,eAAe,CAAC,CAACI,SAAS,CAClCP,wBAAwB,CACtBK,UAAU,EACV;MAEE,IAAMO,CAAC,GAAGN,aAAa;MACvBA,aAAa,GAAG,EAAE;MAClBD,UAAU,CAACK,IAAI,CAACE,CAAC,CAAC;IACpB,CAAC,EACDb,IAAI,CACL,CACF;IAED,OAAO;MAELO,aAAa,GAAG,IAAK;IACvB,CAAC;EACH,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}