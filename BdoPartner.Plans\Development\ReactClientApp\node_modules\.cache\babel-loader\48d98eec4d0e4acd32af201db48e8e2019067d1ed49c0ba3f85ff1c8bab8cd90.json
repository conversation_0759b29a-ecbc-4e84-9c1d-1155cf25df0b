{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function map(project, thisArg) {\n  return operate(function (source, subscriber) {\n    var index = 0;\n    source.subscribe(createOperatorSubscriber(subscriber, function (value) {\n      subscriber.next(project.call(thisArg, value, index++));\n    }));\n  });\n}", "map": {"version": 3, "names": ["operate", "createOperatorSubscriber", "map", "project", "thisArg", "source", "subscriber", "index", "subscribe", "value", "next", "call"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\map.ts"], "sourcesContent": ["import { OperatorFunction } from '../types';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\n\nexport function map<T, R>(project: (value: T, index: number) => R): OperatorFunction<T, R>;\n/** @deprecated Use a closure instead of a `thisArg`. Signatures accepting a `thisArg` will be removed in v8. */\nexport function map<T, R, A>(project: (this: A, value: T, index: number) => R, thisArg: A): OperatorFunction<T, R>;\n\n/**\n * Applies a given `project` function to each value emitted by the source\n * Observable, and emits the resulting values as an Observable.\n *\n * <span class=\"informal\">Like [Array.prototype.map()](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/map),\n * it passes each source value through a transformation function to get\n * corresponding output values.</span>\n *\n * ![](map.png)\n *\n * Similar to the well known `Array.prototype.map` function, this operator\n * applies a projection to each value and emits that projection in the output\n * Observable.\n *\n * ## Example\n *\n * Map every click to the `clientX` position of that click\n *\n * ```ts\n * import { fromEvent, map } from 'rxjs';\n *\n * const clicks = fromEvent<PointerEvent>(document, 'click');\n * const positions = clicks.pipe(map(ev => ev.clientX));\n *\n * positions.subscribe(x => console.log(x));\n * ```\n *\n * @see {@link mapTo}\n * @see {@link pluck}\n *\n * @param project The function to apply to each `value` emitted by the source\n * Observable. The `index` parameter is the number `i` for the i-th emission\n * that has happened since the subscription, starting from the number `0`.\n * @param thisArg An optional argument to define what `this` is in the\n * `project` function.\n * @return A function that returns an Observable that emits the values from the\n * source Observable transformed by the given `project` function.\n */\nexport function map<T, R>(project: (value: T, index: number) => R, thisArg?: any): OperatorFunction<T, R> {\n  return operate((source, subscriber) => {\n    // The index of the value from the source. Used with projection.\n    let index = 0;\n    // Subscribe to the source, all errors and completions are sent along\n    // to the consumer.\n    source.subscribe(\n      createOperatorSubscriber(subscriber, (value: T) => {\n        // Call the projection function with the appropriate this context,\n        // and send the resulting value to the consumer.\n        subscriber.next(project.call(thisArg, value, index++));\n      })\n    );\n  });\n}\n"], "mappings": "AACA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AA4C/D,OAAM,SAAUC,GAAGA,CAAOC,OAAuC,EAAEC,OAAa;EAC9E,OAAOJ,OAAO,CAAC,UAACK,MAAM,EAAEC,UAAU;IAEhC,IAAIC,KAAK,GAAG,CAAC;IAGbF,MAAM,CAACG,SAAS,CACdP,wBAAwB,CAACK,UAAU,EAAE,UAACG,KAAQ;MAG5CH,UAAU,CAACI,IAAI,CAACP,OAAO,CAACQ,IAAI,CAACP,OAAO,EAAEK,KAAK,EAAEF,KAAK,EAAE,CAAC,CAAC;IACxD,CAAC,CAAC,CACH;EACH,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}