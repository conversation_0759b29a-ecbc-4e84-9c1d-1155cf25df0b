{"ast": null, "code": "export function isFunction(value) {\n  return typeof value === 'function';\n}", "map": {"version": 3, "names": ["isFunction", "value"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\util\\isFunction.ts"], "sourcesContent": ["/**\n * Returns true if the object is a function.\n * @param value The value to check\n */\nexport function isFunction(value: any): value is (...args: any[]) => any {\n  return typeof value === 'function';\n}\n"], "mappings": "AAIA,OAAM,SAAUA,UAAUA,CAACC,KAAU;EACnC,OAAO,OAAOA,KAAK,KAAK,UAAU;AACpC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}