{"ast": null, "code": "var _jsxFileName = \"C:\\\\git\\\\Partner-Site\\\\BdoPartner.Plans\\\\Development\\\\ReactClientApp\\\\src\\\\components\\\\common\\\\ReviewerCommentsDialog.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Dialog } from 'primereact/dialog';\nimport { Button } from 'primereact/button';\nimport { InputTextarea } from 'primereact/inputtextarea';\nimport { Message } from 'primereact/message';\n\n/**\r\n * Shared confirmation dialog for capturing reviewer comments when sending forms back to partners\r\n * @param {boolean} visible - Whether the dialog is visible\r\n * @param {function} onHide - Callback when dialog is hidden\r\n * @param {function} onConfirm - Callback when user confirms with comments (receives comments as parameter)\r\n * @param {string} title - Dialog title (optional, defaults to \"Send Back to Partner\")\r\n * @param {string} message - Dialog message (optional, defaults to standard message)\r\n * @param {boolean} loading - Whether the confirm action is in progress\r\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const ReviewerCommentsDialog = ({\n  visible,\n  onHide,\n  onConfirm,\n  title = \"Send Back to Partner\",\n  message = \"Please provide comments explaining why this form needs to be revised:\",\n  loading = false\n}) => {\n  _s();\n  const [comments, setComments] = useState('');\n  const [error, setError] = useState('');\n\n  // Reset state when dialog becomes visible\n  useEffect(() => {\n    if (visible) {\n      setComments('');\n      setError('');\n    }\n  }, [visible]);\n  const handleConfirm = () => {\n    // Validate comments are required\n    if (!comments || comments.trim().length === 0) {\n      setError('Comments are required');\n      return;\n    }\n\n    // Clear error and call confirm callback\n    setError('');\n    onConfirm(comments.trim());\n  };\n  const handleCancel = () => {\n    setComments('');\n    setError('');\n    onHide();\n  };\n  const handleCommentsChange = e => {\n    setComments(e.target.value);\n    // Clear error when user starts typing\n    if (error) {\n      setError('');\n    }\n  };\n  const dialogFooter = /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Button, {\n      label: \"Cancel\",\n      icon: \"pi pi-times\",\n      className: \"p-button-text\",\n      onClick: handleCancel,\n      disabled: loading\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      label: \"Send Back\",\n      icon: \"pi pi-send\",\n      className: \"p-button-primary\",\n      onClick: handleConfirm,\n      loading: loading,\n      disabled: loading\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 62,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    header: title,\n    visible: visible,\n    style: {\n      width: '500px'\n    },\n    modal: true,\n    footer: dialogFooter,\n    onHide: handleCancel,\n    closable: !loading,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-fluid\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"field\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            marginBottom: '1rem',\n            color: '#495057'\n          },\n          children: message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(Message, {\n          severity: \"error\",\n          text: error,\n          style: {\n            marginBottom: '1rem'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"reviewer-comments\",\n          className: \"p-sr-only\",\n          children: \"Comments\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(InputTextarea, {\n          id: \"reviewer-comments\",\n          value: comments,\n          onChange: handleCommentsChange,\n          rows: 5,\n          cols: 30,\n          placeholder: \"Enter your comments here...\",\n          autoResize: true,\n          disabled: loading,\n          style: {\n            minHeight: '120px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n          className: \"p-text-secondary\",\n          style: {\n            marginTop: '0.5rem',\n            display: 'block'\n          },\n          children: \"These comments will be visible to the partner and will help them understand what needs to be revised.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 82,\n    columnNumber: 5\n  }, this);\n};\n_s(ReviewerCommentsDialog, \"g+rP5m4q3oQizBZopo5KsKUAxAw=\");\n_c = ReviewerCommentsDialog;\nexport default ReviewerCommentsDialog;\nvar _c;\n$RefreshReg$(_c, \"ReviewerCommentsDialog\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Dialog", "<PERSON><PERSON>", "InputTextarea", "Message", "jsxDEV", "_jsxDEV", "ReviewerCommentsDialog", "visible", "onHide", "onConfirm", "title", "message", "loading", "_s", "comments", "setComments", "error", "setError", "handleConfirm", "trim", "length", "handleCancel", "handleCommentsChange", "e", "target", "value", "dialogFooter", "children", "label", "icon", "className", "onClick", "disabled", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "header", "style", "width", "modal", "footer", "closable", "marginBottom", "color", "severity", "text", "htmlFor", "id", "onChange", "rows", "cols", "placeholder", "autoResize", "minHeight", "marginTop", "display", "_c", "$RefreshReg$"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/components/common/ReviewerCommentsDialog.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { Dialog } from 'primereact/dialog';\r\nimport { Button } from 'primereact/button';\r\nimport { InputTextarea } from 'primereact/inputtextarea';\r\nimport { Message } from 'primereact/message';\r\n\r\n/**\r\n * Shared confirmation dialog for capturing reviewer comments when sending forms back to partners\r\n * @param {boolean} visible - Whether the dialog is visible\r\n * @param {function} onHide - Callback when dialog is hidden\r\n * @param {function} onConfirm - Callback when user confirms with comments (receives comments as parameter)\r\n * @param {string} title - Dialog title (optional, defaults to \"Send Back to Partner\")\r\n * @param {string} message - Dialog message (optional, defaults to standard message)\r\n * @param {boolean} loading - Whether the confirm action is in progress\r\n */\r\nexport const ReviewerCommentsDialog = ({\r\n  visible,\r\n  onHide,\r\n  onConfirm,\r\n  title = \"Send Back to Partner\",\r\n  message = \"Please provide comments explaining why this form needs to be revised:\",\r\n  loading = false\r\n}) => {\r\n  const [comments, setComments] = useState('');\r\n  const [error, setError] = useState('');\r\n\r\n  // Reset state when dialog becomes visible\r\n  useEffect(() => {\r\n    if (visible) {\r\n      setComments('');\r\n      setError('');\r\n    }\r\n  }, [visible]);\r\n\r\n  const handleConfirm = () => {\r\n    // Validate comments are required\r\n    if (!comments || comments.trim().length === 0) {\r\n      setError('Comments are required');\r\n      return;\r\n    }\r\n\r\n    // Clear error and call confirm callback\r\n    setError('');\r\n    onConfirm(comments.trim());\r\n  };\r\n\r\n  const handleCancel = () => {\r\n    setComments('');\r\n    setError('');\r\n    onHide();\r\n  };\r\n\r\n  const handleCommentsChange = (e) => {\r\n    setComments(e.target.value);\r\n    // Clear error when user starts typing\r\n    if (error) {\r\n      setError('');\r\n    }\r\n  };\r\n\r\n  const dialogFooter = (\r\n    <div>\r\n      <Button\r\n        label=\"Cancel\"\r\n        icon=\"pi pi-times\"\r\n        className=\"p-button-text\"\r\n        onClick={handleCancel}\r\n        disabled={loading}\r\n      />\r\n      <Button\r\n        label=\"Send Back\"\r\n        icon=\"pi pi-send\"\r\n        className=\"p-button-primary\"\r\n        onClick={handleConfirm}\r\n        loading={loading}\r\n        disabled={loading}\r\n      />\r\n    </div>\r\n  );\r\n\r\n  return (\r\n    <Dialog\r\n      header={title}\r\n      visible={visible}\r\n      style={{ width: '500px' }}\r\n      modal\r\n      footer={dialogFooter}\r\n      onHide={handleCancel}\r\n      closable={!loading}\r\n    >\r\n      <div className=\"p-fluid\">\r\n        <div className=\"field\">\r\n          <p style={{ marginBottom: '1rem', color: '#495057' }}>\r\n            {message}\r\n          </p>\r\n          \r\n          {error && (\r\n            <Message \r\n              severity=\"error\" \r\n              text={error} \r\n              style={{ marginBottom: '1rem' }}\r\n            />\r\n          )}\r\n          \r\n          <label htmlFor=\"reviewer-comments\" className=\"p-sr-only\">\r\n            Comments\r\n          </label>\r\n          <InputTextarea\r\n            id=\"reviewer-comments\"\r\n            value={comments}\r\n            onChange={handleCommentsChange}\r\n            rows={5}\r\n            cols={30}\r\n            placeholder=\"Enter your comments here...\"\r\n            autoResize\r\n            disabled={loading}\r\n            style={{ minHeight: '120px' }}\r\n          />\r\n          \r\n          <small className=\"p-text-secondary\" style={{ marginTop: '0.5rem', display: 'block' }}>\r\n            These comments will be visible to the partner and will help them understand what needs to be revised.\r\n          </small>\r\n        </div>\r\n      </div>\r\n    </Dialog>\r\n  );\r\n};\r\n\r\nexport default ReviewerCommentsDialog;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,aAAa,QAAQ,0BAA0B;AACxD,SAASC,OAAO,QAAQ,oBAAoB;;AAE5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA,SAAAC,MAAA,IAAAC,OAAA;AASA,OAAO,MAAMC,sBAAsB,GAAGA,CAAC;EACrCC,OAAO;EACPC,MAAM;EACNC,SAAS;EACTC,KAAK,GAAG,sBAAsB;EAC9BC,OAAO,GAAG,uEAAuE;EACjFC,OAAO,GAAG;AACZ,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACkB,KAAK,EAAEC,QAAQ,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;;EAEtC;EACAC,SAAS,CAAC,MAAM;IACd,IAAIQ,OAAO,EAAE;MACXQ,WAAW,CAAC,EAAE,CAAC;MACfE,QAAQ,CAAC,EAAE,CAAC;IACd;EACF,CAAC,EAAE,CAACV,OAAO,CAAC,CAAC;EAEb,MAAMW,aAAa,GAAGA,CAAA,KAAM;IAC1B;IACA,IAAI,CAACJ,QAAQ,IAAIA,QAAQ,CAACK,IAAI,CAAC,CAAC,CAACC,MAAM,KAAK,CAAC,EAAE;MAC7CH,QAAQ,CAAC,uBAAuB,CAAC;MACjC;IACF;;IAEA;IACAA,QAAQ,CAAC,EAAE,CAAC;IACZR,SAAS,CAACK,QAAQ,CAACK,IAAI,CAAC,CAAC,CAAC;EAC5B,CAAC;EAED,MAAME,YAAY,GAAGA,CAAA,KAAM;IACzBN,WAAW,CAAC,EAAE,CAAC;IACfE,QAAQ,CAAC,EAAE,CAAC;IACZT,MAAM,CAAC,CAAC;EACV,CAAC;EAED,MAAMc,oBAAoB,GAAIC,CAAC,IAAK;IAClCR,WAAW,CAACQ,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;IAC3B;IACA,IAAIT,KAAK,EAAE;MACTC,QAAQ,CAAC,EAAE,CAAC;IACd;EACF,CAAC;EAED,MAAMS,YAAY,gBAChBrB,OAAA;IAAAsB,QAAA,gBACEtB,OAAA,CAACJ,MAAM;MACL2B,KAAK,EAAC,QAAQ;MACdC,IAAI,EAAC,aAAa;MAClBC,SAAS,EAAC,eAAe;MACzBC,OAAO,EAAEV,YAAa;MACtBW,QAAQ,EAAEpB;IAAQ;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CAAC,eACF/B,OAAA,CAACJ,MAAM;MACL2B,KAAK,EAAC,WAAW;MACjBC,IAAI,EAAC,YAAY;MACjBC,SAAS,EAAC,kBAAkB;MAC5BC,OAAO,EAAEb,aAAc;MACvBN,OAAO,EAAEA,OAAQ;MACjBoB,QAAQ,EAAEpB;IAAQ;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CACN;EAED,oBACE/B,OAAA,CAACL,MAAM;IACLqC,MAAM,EAAE3B,KAAM;IACdH,OAAO,EAAEA,OAAQ;IACjB+B,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAQ,CAAE;IAC1BC,KAAK;IACLC,MAAM,EAAEf,YAAa;IACrBlB,MAAM,EAAEa,YAAa;IACrBqB,QAAQ,EAAE,CAAC9B,OAAQ;IAAAe,QAAA,eAEnBtB,OAAA;MAAKyB,SAAS,EAAC,SAAS;MAAAH,QAAA,eACtBtB,OAAA;QAAKyB,SAAS,EAAC,OAAO;QAAAH,QAAA,gBACpBtB,OAAA;UAAGiC,KAAK,EAAE;YAAEK,YAAY,EAAE,MAAM;YAAEC,KAAK,EAAE;UAAU,CAAE;UAAAjB,QAAA,EAClDhB;QAAO;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,EAEHpB,KAAK,iBACJX,OAAA,CAACF,OAAO;UACN0C,QAAQ,EAAC,OAAO;UAChBC,IAAI,EAAE9B,KAAM;UACZsB,KAAK,EAAE;YAAEK,YAAY,EAAE;UAAO;QAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CACF,eAED/B,OAAA;UAAO0C,OAAO,EAAC,mBAAmB;UAACjB,SAAS,EAAC,WAAW;UAAAH,QAAA,EAAC;QAEzD;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACR/B,OAAA,CAACH,aAAa;UACZ8C,EAAE,EAAC,mBAAmB;UACtBvB,KAAK,EAAEX,QAAS;UAChBmC,QAAQ,EAAE3B,oBAAqB;UAC/B4B,IAAI,EAAE,CAAE;UACRC,IAAI,EAAE,EAAG;UACTC,WAAW,EAAC,6BAA6B;UACzCC,UAAU;UACVrB,QAAQ,EAAEpB,OAAQ;UAClB0B,KAAK,EAAE;YAAEgB,SAAS,EAAE;UAAQ;QAAE;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,eAEF/B,OAAA;UAAOyB,SAAS,EAAC,kBAAkB;UAACQ,KAAK,EAAE;YAAEiB,SAAS,EAAE,QAAQ;YAAEC,OAAO,EAAE;UAAQ,CAAE;UAAA7B,QAAA,EAAC;QAEtF;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACvB,EAAA,CA/GWP,sBAAsB;AAAAmD,EAAA,GAAtBnD,sBAAsB;AAiHnC,eAAeA,sBAAsB;AAAC,IAAAmD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}