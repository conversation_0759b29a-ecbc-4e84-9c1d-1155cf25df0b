{"ast": null, "code": "import { createElement, forwardRef as forwardRefReact } from 'react';\nimport { useTranslation } from './useTranslation.js';\nimport { getDisplayName } from './utils.js';\nexport const withTranslation = function (ns) {\n  let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  return function Extend(WrappedComponent) {\n    function I18nextWithTranslation(_ref) {\n      let {\n        forwardedRef,\n        ...rest\n      } = _ref;\n      const [t, i18n, ready] = useTranslation(ns, {\n        ...rest,\n        keyPrefix: options.keyPrefix\n      });\n      const passDownProps = {\n        ...rest,\n        t,\n        i18n,\n        tReady: ready\n      };\n      if (options.withRef && forwardedRef) {\n        passDownProps.ref = forwardedRef;\n      } else if (!options.withRef && forwardedRef) {\n        passDownProps.forwardedRef = forwardedRef;\n      }\n      return createElement(WrappedComponent, passDownProps);\n    }\n    I18nextWithTranslation.displayName = `withI18nextTranslation(${getDisplayName(WrappedComponent)})`;\n    I18nextWithTranslation.WrappedComponent = WrappedComponent;\n    const forwardRef = (props, ref) => createElement(I18nextWithTranslation, Object.assign({}, props, {\n      forwardedRef: ref\n    }));\n    return options.withRef ? forwardRefReact(forwardRef) : I18nextWithTranslation;\n  };\n};", "map": {"version": 3, "names": ["createElement", "forwardRef", "forwardRefReact", "useTranslation", "getDisplayName", "withTranslation", "ns", "options", "arguments", "length", "undefined", "Extend", "WrappedComponent", "I18nextWithTranslation", "_ref", "forwardedRef", "rest", "t", "i18n", "ready", "keyPrefix", "passDownProps", "tReady", "with<PERSON>ef", "ref", "displayName", "props", "Object", "assign"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/node_modules/react-i18next/dist/es/withTranslation.js"], "sourcesContent": ["import { createElement, forwardRef as forwardRefReact } from 'react';\nimport { useTranslation } from './useTranslation.js';\nimport { getDisplayName } from './utils.js';\nexport const withTranslation = function (ns) {\n  let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  return function Extend(WrappedComponent) {\n    function I18nextWithTranslation(_ref) {\n      let {\n        forwardedRef,\n        ...rest\n      } = _ref;\n      const [t, i18n, ready] = useTranslation(ns, {\n        ...rest,\n        keyPrefix: options.keyPrefix\n      });\n      const passDownProps = {\n        ...rest,\n        t,\n        i18n,\n        tReady: ready\n      };\n      if (options.withRef && forwardedRef) {\n        passDownProps.ref = forwardedRef;\n      } else if (!options.withRef && forwardedRef) {\n        passDownProps.forwardedRef = forwardedRef;\n      }\n      return createElement(WrappedComponent, passDownProps);\n    }\n    I18nextWithTranslation.displayName = `withI18nextTranslation(${getDisplayName(WrappedComponent)})`;\n    I18nextWithTranslation.WrappedComponent = WrappedComponent;\n    const forwardRef = (props, ref) => createElement(I18nextWithTranslation, Object.assign({}, props, {\n      forwardedRef: ref\n    }));\n    return options.withRef ? forwardRefReact(forwardRef) : I18nextWithTranslation;\n  };\n};"], "mappings": "AAAA,SAASA,aAAa,EAAEC,UAAU,IAAIC,eAAe,QAAQ,OAAO;AACpE,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,cAAc,QAAQ,YAAY;AAC3C,OAAO,MAAMC,eAAe,GAAG,SAAAA,CAAUC,EAAE,EAAE;EAC3C,IAAIC,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACpF,OAAO,SAASG,MAAMA,CAACC,gBAAgB,EAAE;IACvC,SAASC,sBAAsBA,CAACC,IAAI,EAAE;MACpC,IAAI;QACFC,YAAY;QACZ,GAAGC;MACL,CAAC,GAAGF,IAAI;MACR,MAAM,CAACG,CAAC,EAAEC,IAAI,EAAEC,KAAK,CAAC,GAAGhB,cAAc,CAACG,EAAE,EAAE;QAC1C,GAAGU,IAAI;QACPI,SAAS,EAAEb,OAAO,CAACa;MACrB,CAAC,CAAC;MACF,MAAMC,aAAa,GAAG;QACpB,GAAGL,IAAI;QACPC,CAAC;QACDC,IAAI;QACJI,MAAM,EAAEH;MACV,CAAC;MACD,IAAIZ,OAAO,CAACgB,OAAO,IAAIR,YAAY,EAAE;QACnCM,aAAa,CAACG,GAAG,GAAGT,YAAY;MAClC,CAAC,MAAM,IAAI,CAACR,OAAO,CAACgB,OAAO,IAAIR,YAAY,EAAE;QAC3CM,aAAa,CAACN,YAAY,GAAGA,YAAY;MAC3C;MACA,OAAOf,aAAa,CAACY,gBAAgB,EAAES,aAAa,CAAC;IACvD;IACAR,sBAAsB,CAACY,WAAW,GAAG,0BAA0BrB,cAAc,CAACQ,gBAAgB,CAAC,GAAG;IAClGC,sBAAsB,CAACD,gBAAgB,GAAGA,gBAAgB;IAC1D,MAAMX,UAAU,GAAGA,CAACyB,KAAK,EAAEF,GAAG,KAAKxB,aAAa,CAACa,sBAAsB,EAAEc,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEF,KAAK,EAAE;MAChGX,YAAY,EAAES;IAChB,CAAC,CAAC,CAAC;IACH,OAAOjB,OAAO,CAACgB,OAAO,GAAGrB,eAAe,CAACD,UAAU,CAAC,GAAGY,sBAAsB;EAC/E,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}