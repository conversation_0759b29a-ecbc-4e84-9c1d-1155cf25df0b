{"ast": null, "code": "export function executeSchedule(parentSubscription, scheduler, work, delay, repeat) {\n  if (delay === void 0) {\n    delay = 0;\n  }\n  if (repeat === void 0) {\n    repeat = false;\n  }\n  var scheduleSubscription = scheduler.schedule(function () {\n    work();\n    if (repeat) {\n      parentSubscription.add(this.schedule(null, delay));\n    } else {\n      this.unsubscribe();\n    }\n  }, delay);\n  parentSubscription.add(scheduleSubscription);\n  if (!repeat) {\n    return scheduleSubscription;\n  }\n}", "map": {"version": 3, "names": ["executeSchedule", "parentSubscription", "scheduler", "work", "delay", "repeat", "scheduleSubscription", "schedule", "add", "unsubscribe"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\util\\executeSchedule.ts"], "sourcesContent": ["import { Subscription } from '../Subscription';\nimport { SchedulerAction, SchedulerLike } from '../types';\n\nexport function executeSchedule(\n  parentSubscription: Subscription,\n  scheduler: SchedulerLike,\n  work: () => void,\n  delay: number,\n  repeat: true\n): void;\nexport function executeSchedule(\n  parentSubscription: Subscription,\n  scheduler: SchedulerLike,\n  work: () => void,\n  delay?: number,\n  repeat?: false\n): Subscription;\n\nexport function executeSchedule(\n  parentSubscription: Subscription,\n  scheduler: SchedulerLike,\n  work: () => void,\n  delay = 0,\n  repeat = false\n): Subscription | void {\n  const scheduleSubscription = scheduler.schedule(function (this: SchedulerAction<any>) {\n    work();\n    if (repeat) {\n      parentSubscription.add(this.schedule(null, delay));\n    } else {\n      this.unsubscribe();\n    }\n  }, delay);\n\n  parentSubscription.add(scheduleSubscription);\n\n  if (!repeat) {\n    // Because user-land scheduler implementations are unlikely to properly reuse\n    // Actions for repeat scheduling, we can't trust that the returned subscription\n    // will control repeat subscription scenarios. So we're trying to avoid using them\n    // incorrectly within this library.\n    return scheduleSubscription;\n  }\n}\n"], "mappings": "AAkBA,OAAM,SAAUA,eAAeA,CAC7BC,kBAAgC,EAChCC,SAAwB,EACxBC,IAAgB,EAChBC,KAAS,EACTC,MAAc;EADd,IAAAD,KAAA;IAAAA,KAAA,IAAS;EAAA;EACT,IAAAC,MAAA;IAAAA,MAAA,QAAc;EAAA;EAEd,IAAMC,oBAAoB,GAAGJ,SAAS,CAACK,QAAQ,CAAC;IAC9CJ,IAAI,EAAE;IACN,IAAIE,MAAM,EAAE;MACVJ,kBAAkB,CAACO,GAAG,CAAC,IAAI,CAACD,QAAQ,CAAC,IAAI,EAAEH,KAAK,CAAC,CAAC;KACnD,MAAM;MACL,IAAI,CAACK,WAAW,EAAE;;EAEtB,CAAC,EAAEL,KAAK,CAAC;EAETH,kBAAkB,CAACO,GAAG,CAACF,oBAAoB,CAAC;EAE5C,IAAI,CAACD,MAAM,EAAE;IAKX,OAAOC,oBAAoB;;AAE/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}