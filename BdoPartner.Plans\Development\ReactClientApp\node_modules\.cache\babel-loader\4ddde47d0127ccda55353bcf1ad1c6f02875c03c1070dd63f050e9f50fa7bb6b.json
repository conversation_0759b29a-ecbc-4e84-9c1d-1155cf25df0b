{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { Scheduler } from '../Scheduler';\nvar AsyncScheduler = function (_super) {\n  __extends(AsyncScheduler, _super);\n  function AsyncScheduler(SchedulerAction, now) {\n    if (now === void 0) {\n      now = Scheduler.now;\n    }\n    var _this = _super.call(this, SchedulerAction, now) || this;\n    _this.actions = [];\n    _this._active = false;\n    return _this;\n  }\n  AsyncScheduler.prototype.flush = function (action) {\n    var actions = this.actions;\n    if (this._active) {\n      actions.push(action);\n      return;\n    }\n    var error;\n    this._active = true;\n    do {\n      if (error = action.execute(action.state, action.delay)) {\n        break;\n      }\n    } while (action = actions.shift());\n    this._active = false;\n    if (error) {\n      while (action = actions.shift()) {\n        action.unsubscribe();\n      }\n      throw error;\n    }\n  };\n  return AsyncScheduler;\n}(Scheduler);\nexport { AsyncScheduler };", "map": {"version": 3, "names": ["Scheduler", "AsyncScheduler", "_super", "__extends", "SchedulerAction", "now", "_this", "call", "actions", "_active", "prototype", "flush", "action", "push", "error", "execute", "state", "delay", "shift", "unsubscribe"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\scheduler\\AsyncScheduler.ts"], "sourcesContent": ["import { Scheduler } from '../Scheduler';\nimport { Action } from './Action';\nimport { AsyncAction } from './AsyncAction';\nimport { TimerHandle } from './timerHandle';\n\nexport class AsyncScheduler extends Scheduler {\n  public actions: Array<AsyncAction<any>> = [];\n  /**\n   * A flag to indicate whether the Scheduler is currently executing a batch of\n   * queued actions.\n   * @internal\n   */\n  public _active: boolean = false;\n  /**\n   * An internal ID used to track the latest asynchronous task such as those\n   * coming from `setTimeout`, `setInterval`, `requestAnimationFrame`, and\n   * others.\n   * @internal\n   */\n  public _scheduled: TimerHandle | undefined;\n\n  constructor(SchedulerAction: typeof Action, now: () => number = Scheduler.now) {\n    super(SchedulerAction, now);\n  }\n\n  public flush(action: AsyncAction<any>): void {\n    const { actions } = this;\n\n    if (this._active) {\n      actions.push(action);\n      return;\n    }\n\n    let error: any;\n    this._active = true;\n\n    do {\n      if ((error = action.execute(action.state, action.delay))) {\n        break;\n      }\n    } while ((action = actions.shift()!)); // exhaust the scheduler queue\n\n    this._active = false;\n\n    if (error) {\n      while ((action = actions.shift()!)) {\n        action.unsubscribe();\n      }\n      throw error;\n    }\n  }\n}\n"], "mappings": ";AAAA,SAASA,SAAS,QAAQ,cAAc;AAKxC,IAAAC,cAAA,aAAAC,MAAA;EAAoCC,SAAA,CAAAF,cAAA,EAAAC,MAAA;EAgBlC,SAAAD,eAAYG,eAA8B,EAAEC,GAAiC;IAAjC,IAAAA,GAAA;MAAAA,GAAA,GAAoBL,SAAS,CAACK,GAAG;IAAA;IAA7E,IAAAC,KAAA,GACEJ,MAAA,CAAAK,IAAA,OAAMH,eAAe,EAAEC,GAAG,CAAC;IAhBtBC,KAAA,CAAAE,OAAO,GAA4B,EAAE;IAMrCF,KAAA,CAAAG,OAAO,GAAY,KAAK;;EAW/B;EAEOR,cAAA,CAAAS,SAAA,CAAAC,KAAK,GAAZ,UAAaC,MAAwB;IAC3B,IAAAJ,OAAO,GAAK,IAAI,CAAAA,OAAT;IAEf,IAAI,IAAI,CAACC,OAAO,EAAE;MAChBD,OAAO,CAACK,IAAI,CAACD,MAAM,CAAC;MACpB;;IAGF,IAAIE,KAAU;IACd,IAAI,CAACL,OAAO,GAAG,IAAI;IAEnB,GAAG;MACD,IAAKK,KAAK,GAAGF,MAAM,CAACG,OAAO,CAACH,MAAM,CAACI,KAAK,EAAEJ,MAAM,CAACK,KAAK,CAAC,EAAG;QACxD;;KAEH,QAASL,MAAM,GAAGJ,OAAO,CAACU,KAAK,EAAG;IAEnC,IAAI,CAACT,OAAO,GAAG,KAAK;IAEpB,IAAIK,KAAK,EAAE;MACT,OAAQF,MAAM,GAAGJ,OAAO,CAACU,KAAK,EAAG,EAAG;QAClCN,MAAM,CAACO,WAAW,EAAE;;MAEtB,MAAML,KAAK;;EAEf,CAAC;EACH,OAAAb,cAAC;AAAD,CAAC,CA9CmCD,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}