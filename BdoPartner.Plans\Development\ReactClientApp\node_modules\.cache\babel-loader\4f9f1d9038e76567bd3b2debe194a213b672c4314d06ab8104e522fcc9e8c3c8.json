{"ast": null, "code": "var nextHandle = 1;\nvar resolved;\nvar activeHandles = {};\nfunction findAndClearHandle(handle) {\n  if (handle in activeHandles) {\n    delete activeHandles[handle];\n    return true;\n  }\n  return false;\n}\nexport var Immediate = {\n  setImmediate: function (cb) {\n    var handle = nextHandle++;\n    activeHandles[handle] = true;\n    if (!resolved) {\n      resolved = Promise.resolve();\n    }\n    resolved.then(function () {\n      return findAndClearHandle(handle) && cb();\n    });\n    return handle;\n  },\n  clearImmediate: function (handle) {\n    findAndClearHandle(handle);\n  }\n};\nexport var TestTools = {\n  pending: function () {\n    return Object.keys(activeHandles).length;\n  }\n};", "map": {"version": 3, "names": ["nextH<PERSON>le", "resolved", "active<PERSON><PERSON><PERSON>", "findAndClearHandle", "handle", "Immediate", "setImmediate", "cb", "Promise", "resolve", "then", "clearImmediate", "TestTools", "pending", "Object", "keys", "length"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\util\\Immediate.ts"], "sourcesContent": ["let nextHandle = 1;\n// The promise needs to be created lazily otherwise it won't be patched by Zones\nlet resolved: Promise<any>;\nconst activeHandles: { [key: number]: any } = {};\n\n/**\n * Finds the handle in the list of active handles, and removes it.\n * Returns `true` if found, `false` otherwise. Used both to clear\n * Immediate scheduled tasks, and to identify if a task should be scheduled.\n */\nfunction findAndClearHandle(handle: number): boolean {\n  if (handle in activeHandles) {\n    delete activeHandles[handle];\n    return true;\n  }\n  return false;\n}\n\n/**\n * Helper functions to schedule and unschedule microtasks.\n */\nexport const Immediate = {\n  setImmediate(cb: () => void): number {\n    const handle = nextHandle++;\n    activeHandles[handle] = true;\n    if (!resolved) {\n      resolved = Promise.resolve();\n    }\n    resolved.then(() => findAndClearHandle(handle) && cb());\n    return handle;\n  },\n\n  clearImmediate(handle: number): void {\n    findAndClearHandle(handle);\n  },\n};\n\n/**\n * Used for internal testing purposes only. Do not export from library.\n */\nexport const TestTools = {\n  pending() {\n    return Object.keys(activeHandles).length;\n  }\n};\n"], "mappings": "AAAA,IAAIA,UAAU,GAAG,CAAC;AAElB,IAAIC,QAAsB;AAC1B,IAAMC,aAAa,GAA2B,EAAE;AAOhD,SAASC,kBAAkBA,CAACC,MAAc;EACxC,IAAIA,MAAM,IAAIF,aAAa,EAAE;IAC3B,OAAOA,aAAa,CAACE,MAAM,CAAC;IAC5B,OAAO,IAAI;;EAEb,OAAO,KAAK;AACd;AAKA,OAAO,IAAMC,SAAS,GAAG;EACvBC,YAAY,EAAZ,SAAAA,CAAaC,EAAc;IACzB,IAAMH,MAAM,GAAGJ,UAAU,EAAE;IAC3BE,aAAa,CAACE,MAAM,CAAC,GAAG,IAAI;IAC5B,IAAI,CAACH,QAAQ,EAAE;MACbA,QAAQ,GAAGO,OAAO,CAACC,OAAO,EAAE;;IAE9BR,QAAQ,CAACS,IAAI,CAAC;MAAM,OAAAP,kBAAkB,CAACC,MAAM,CAAC,IAAIG,EAAE,EAAE;IAAlC,CAAkC,CAAC;IACvD,OAAOH,MAAM;EACf,CAAC;EAEDO,cAAc,EAAd,SAAAA,CAAeP,MAAc;IAC3BD,kBAAkB,CAACC,MAAM,CAAC;EAC5B;CACD;AAKD,OAAO,IAAMQ,SAAS,GAAG;EACvBC,OAAO,WAAAA,CAAA;IACL,OAAOC,MAAM,CAACC,IAAI,CAACb,aAAa,CAAC,CAACc,MAAM;EAC1C;CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}