{"ast": null, "code": "\"use strict\";\n\nexports.__esModule = true;\nexports.default = getStorage;\nfunction _typeof(obj) {\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    _typeof = function _typeof(obj) {\n      return typeof obj;\n    };\n  } else {\n    _typeof = function _typeof(obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n  }\n  return _typeof(obj);\n}\nfunction noop() {}\nvar noopStorage = {\n  getItem: noop,\n  setItem: noop,\n  removeItem: noop\n};\nfunction hasStorage(storageType) {\n  if ((typeof self === \"undefined\" ? \"undefined\" : _typeof(self)) !== 'object' || !(storageType in self)) {\n    return false;\n  }\n  try {\n    var storage = self[storageType];\n    var testKey = \"redux-persist \".concat(storageType, \" test\");\n    storage.setItem(testKey, 'test');\n    storage.getItem(testKey);\n    storage.removeItem(testKey);\n  } catch (e) {\n    if (process.env.NODE_ENV !== 'production') console.warn(\"redux-persist \".concat(storageType, \" test failed, persistence will be disabled.\"));\n    return false;\n  }\n  return true;\n}\nfunction getStorage(type) {\n  var storageType = \"\".concat(type, \"Storage\");\n  if (hasStorage(storageType)) return self[storageType];else {\n    if (process.env.NODE_ENV !== 'production') {\n      console.error(\"redux-persist failed to create sync storage. falling back to noop storage.\");\n    }\n    return noopStorage;\n  }\n}", "map": {"version": 3, "names": ["exports", "__esModule", "default", "getStorage", "_typeof", "obj", "Symbol", "iterator", "constructor", "prototype", "noop", "noopStorage", "getItem", "setItem", "removeItem", "hasStorage", "storageType", "self", "storage", "<PERSON><PERSON><PERSON>", "concat", "e", "process", "env", "NODE_ENV", "console", "warn", "type", "error"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/node_modules/redux-persist/lib/storage/getStorage.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.default = getStorage;\n\nfunction _typeof(obj) { if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; } return _typeof(obj); }\n\nfunction noop() {}\n\nvar noopStorage = {\n  getItem: noop,\n  setItem: noop,\n  removeItem: noop\n};\n\nfunction hasStorage(storageType) {\n  if ((typeof self === \"undefined\" ? \"undefined\" : _typeof(self)) !== 'object' || !(storageType in self)) {\n    return false;\n  }\n\n  try {\n    var storage = self[storageType];\n    var testKey = \"redux-persist \".concat(storageType, \" test\");\n    storage.setItem(testKey, 'test');\n    storage.getItem(testKey);\n    storage.removeItem(testKey);\n  } catch (e) {\n    if (process.env.NODE_ENV !== 'production') console.warn(\"redux-persist \".concat(storageType, \" test failed, persistence will be disabled.\"));\n    return false;\n  }\n\n  return true;\n}\n\nfunction getStorage(type) {\n  var storageType = \"\".concat(type, \"Storage\");\n  if (hasStorage(storageType)) return self[storageType];else {\n    if (process.env.NODE_ENV !== 'production') {\n      console.error(\"redux-persist failed to create sync storage. falling back to noop storage.\");\n    }\n\n    return noopStorage;\n  }\n}"], "mappings": "AAAA,YAAY;;AAEZA,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACE,OAAO,GAAGC,UAAU;AAE5B,SAASC,OAAOA,CAACC,GAAG,EAAE;EAAE,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAI,OAAOA,MAAM,CAACC,QAAQ,KAAK,QAAQ,EAAE;IAAEH,OAAO,GAAG,SAASA,OAAOA,CAACC,GAAG,EAAE;MAAE,OAAO,OAAOA,GAAG;IAAE,CAAC;EAAE,CAAC,MAAM;IAAED,OAAO,GAAG,SAASA,OAAOA,CAACC,GAAG,EAAE;MAAE,OAAOA,GAAG,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAID,GAAG,CAACG,WAAW,KAAKF,MAAM,IAAID,GAAG,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,GAAG;IAAE,CAAC;EAAE;EAAE,OAAOD,OAAO,CAACC,GAAG,CAAC;AAAE;AAE9V,SAASK,IAAIA,CAAA,EAAG,CAAC;AAEjB,IAAIC,WAAW,GAAG;EAChBC,OAAO,EAAEF,IAAI;EACbG,OAAO,EAAEH,IAAI;EACbI,UAAU,EAAEJ;AACd,CAAC;AAED,SAASK,UAAUA,CAACC,WAAW,EAAE;EAC/B,IAAI,CAAC,OAAOC,IAAI,KAAK,WAAW,GAAG,WAAW,GAAGb,OAAO,CAACa,IAAI,CAAC,MAAM,QAAQ,IAAI,EAAED,WAAW,IAAIC,IAAI,CAAC,EAAE;IACtG,OAAO,KAAK;EACd;EAEA,IAAI;IACF,IAAIC,OAAO,GAAGD,IAAI,CAACD,WAAW,CAAC;IAC/B,IAAIG,OAAO,GAAG,gBAAgB,CAACC,MAAM,CAACJ,WAAW,EAAE,OAAO,CAAC;IAC3DE,OAAO,CAACL,OAAO,CAACM,OAAO,EAAE,MAAM,CAAC;IAChCD,OAAO,CAACN,OAAO,CAACO,OAAO,CAAC;IACxBD,OAAO,CAACJ,UAAU,CAACK,OAAO,CAAC;EAC7B,CAAC,CAAC,OAAOE,CAAC,EAAE;IACV,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAEC,OAAO,CAACC,IAAI,CAAC,gBAAgB,CAACN,MAAM,CAACJ,WAAW,EAAE,6CAA6C,CAAC,CAAC;IAC5I,OAAO,KAAK;EACd;EAEA,OAAO,IAAI;AACb;AAEA,SAASb,UAAUA,CAACwB,IAAI,EAAE;EACxB,IAAIX,WAAW,GAAG,EAAE,CAACI,MAAM,CAACO,IAAI,EAAE,SAAS,CAAC;EAC5C,IAAIZ,UAAU,CAACC,WAAW,CAAC,EAAE,OAAOC,IAAI,CAACD,WAAW,CAAC,CAAC,KAAK;IACzD,IAAIM,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzCC,OAAO,CAACG,KAAK,CAAC,4EAA4E,CAAC;IAC7F;IAEA,OAAOjB,WAAW;EACpB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}