{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport { IconBase } from 'primereact/iconbase';\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nvar ChevronRightIcon = /*#__PURE__*/React.memo(/*#__PURE__*/React.forwardRef(function (inProps, ref) {\n  var pti = IconBase.getPTI(inProps);\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({\n    ref: ref,\n    width: \"14\",\n    height: \"14\",\n    viewBox: \"0 0 14 14\",\n    fill: \"none\",\n    xmlns: \"http://www.w3.org/2000/svg\"\n  }, pti), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M4.38708 13C4.28408 13.0005 4.18203 12.9804 4.08691 12.9409C3.99178 12.9014 3.9055 12.8433 3.83313 12.7701C3.68634 12.6231 3.60388 12.4238 3.60388 12.2161C3.60388 12.0084 3.68634 11.8091 3.83313 11.6622L8.50507 6.99022L3.83313 2.31827C3.69467 2.16968 3.61928 1.97313 3.62287 1.77005C3.62645 1.56698 3.70872 1.37322 3.85234 1.22959C3.99596 1.08597 4.18972 1.00371 4.3928 1.00012C4.59588 0.996539 4.79242 1.07192 4.94102 1.21039L10.1669 6.43628C10.3137 6.58325 10.3962 6.78249 10.3962 6.99022C10.3962 7.19795 10.3137 7.39718 10.1669 7.54416L4.94102 12.7701C4.86865 12.8433 4.78237 12.9014 4.68724 12.9409C4.59212 12.9804 4.49007 13.0005 4.38708 13Z\",\n    fill: \"currentColor\"\n  }));\n}));\nChevronRightIcon.displayName = 'ChevronRightIcon';\nexport { ChevronRightIcon };", "map": {"version": 3, "names": ["React", "IconBase", "_extends", "Object", "assign", "bind", "n", "e", "arguments", "length", "t", "r", "hasOwnProperty", "call", "apply", "ChevronRightIcon", "memo", "forwardRef", "inProps", "ref", "pti", "getPTI", "createElement", "width", "height", "viewBox", "fill", "xmlns", "d", "displayName"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/node_modules/primereact/icons/chevronright/index.esm.js"], "sourcesContent": ["'use client';\nimport * as React from 'react';\nimport { IconBase } from 'primereact/iconbase';\n\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\n\nvar ChevronRightIcon = /*#__PURE__*/React.memo(/*#__PURE__*/React.forwardRef(function (inProps, ref) {\n  var pti = IconBase.getPTI(inProps);\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({\n    ref: ref,\n    width: \"14\",\n    height: \"14\",\n    viewBox: \"0 0 14 14\",\n    fill: \"none\",\n    xmlns: \"http://www.w3.org/2000/svg\"\n  }, pti), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M4.38708 13C4.28408 13.0005 4.18203 12.9804 4.08691 12.9409C3.99178 12.9014 3.9055 12.8433 3.83313 12.7701C3.68634 12.6231 3.60388 12.4238 3.60388 12.2161C3.60388 12.0084 3.68634 11.8091 3.83313 11.6622L8.50507 6.99022L3.83313 2.31827C3.69467 2.16968 3.61928 1.97313 3.62287 1.77005C3.62645 1.56698 3.70872 1.37322 3.85234 1.22959C3.99596 1.08597 4.18972 1.00371 4.3928 1.00012C4.59588 0.996539 4.79242 1.07192 4.94102 1.21039L10.1669 6.43628C10.3137 6.58325 10.3962 6.78249 10.3962 6.99022C10.3962 7.19795 10.3137 7.39718 10.1669 7.54416L4.94102 12.7701C4.86865 12.8433 4.78237 12.9014 4.68724 12.9409C4.59212 12.9804 4.49007 13.0005 4.38708 13Z\",\n    fill: \"currentColor\"\n  }));\n}));\nChevronRightIcon.displayName = 'ChevronRightIcon';\n\nexport { ChevronRightIcon };\n"], "mappings": "AAAA,YAAY;;AACZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,QAAQ,qBAAqB;AAE9C,SAASC,QAAQA,CAAA,EAAG;EAClB,OAAOA,QAAQ,GAAGC,MAAM,CAACC,MAAM,GAAGD,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUC,CAAC,EAAE;IACpE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MACzC,IAAIG,CAAC,GAAGF,SAAS,CAACD,CAAC,CAAC;MACpB,KAAK,IAAII,CAAC,IAAID,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEE,cAAc,CAACC,IAAI,CAACH,CAAC,EAAEC,CAAC,CAAC,KAAKL,CAAC,CAACK,CAAC,CAAC,GAAGD,CAAC,CAACC,CAAC,CAAC,CAAC;IAClE;IACA,OAAOL,CAAC;EACV,CAAC,EAAEJ,QAAQ,CAACY,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;AACpC;AAEA,IAAIO,gBAAgB,GAAG,aAAaf,KAAK,CAACgB,IAAI,CAAC,aAAahB,KAAK,CAACiB,UAAU,CAAC,UAAUC,OAAO,EAAEC,GAAG,EAAE;EACnG,IAAIC,GAAG,GAAGnB,QAAQ,CAACoB,MAAM,CAACH,OAAO,CAAC;EAClC,OAAO,aAAalB,KAAK,CAACsB,aAAa,CAAC,KAAK,EAAEpB,QAAQ,CAAC;IACtDiB,GAAG,EAAEA,GAAG;IACRI,KAAK,EAAE,IAAI;IACXC,MAAM,EAAE,IAAI;IACZC,OAAO,EAAE,WAAW;IACpBC,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE;EACT,CAAC,EAAEP,GAAG,CAAC,EAAE,aAAapB,KAAK,CAACsB,aAAa,CAAC,MAAM,EAAE;IAChDM,CAAC,EAAE,woBAAwoB;IAC3oBF,IAAI,EAAE;EACR,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AACHX,gBAAgB,CAACc,WAAW,GAAG,kBAAkB;AAEjD,SAASd,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}