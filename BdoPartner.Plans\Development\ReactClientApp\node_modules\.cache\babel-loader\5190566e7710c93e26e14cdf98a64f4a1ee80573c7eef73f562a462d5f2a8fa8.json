{"ast": null, "code": "import { __asyncValues, __awaiter, __generator, __values } from \"tslib\";\nimport { isArrayLike } from '../util/isArrayLike';\nimport { isPromise } from '../util/isPromise';\nimport { Observable } from '../Observable';\nimport { isInteropObservable } from '../util/isInteropObservable';\nimport { isAsyncIterable } from '../util/isAsyncIterable';\nimport { createInvalidObservableTypeError } from '../util/throwUnobservableError';\nimport { isIterable } from '../util/isIterable';\nimport { isReadableStreamLike, readableStreamLikeToAsyncGenerator } from '../util/isReadableStreamLike';\nimport { isFunction } from '../util/isFunction';\nimport { reportUnhandledError } from '../util/reportUnhandledError';\nimport { observable as Symbol_observable } from '../symbol/observable';\nexport function innerFrom(input) {\n  if (input instanceof Observable) {\n    return input;\n  }\n  if (input != null) {\n    if (isInteropObservable(input)) {\n      return fromInteropObservable(input);\n    }\n    if (isArrayLike(input)) {\n      return fromArrayLike(input);\n    }\n    if (isPromise(input)) {\n      return fromPromise(input);\n    }\n    if (isAsyncIterable(input)) {\n      return fromAsyncIterable(input);\n    }\n    if (isIterable(input)) {\n      return fromIterable(input);\n    }\n    if (isReadableStreamLike(input)) {\n      return fromReadableStreamLike(input);\n    }\n  }\n  throw createInvalidObservableTypeError(input);\n}\nexport function fromInteropObservable(obj) {\n  return new Observable(function (subscriber) {\n    var obs = obj[Symbol_observable]();\n    if (isFunction(obs.subscribe)) {\n      return obs.subscribe(subscriber);\n    }\n    throw new TypeError('Provided object does not correctly implement Symbol.observable');\n  });\n}\nexport function fromArrayLike(array) {\n  return new Observable(function (subscriber) {\n    for (var i = 0; i < array.length && !subscriber.closed; i++) {\n      subscriber.next(array[i]);\n    }\n    subscriber.complete();\n  });\n}\nexport function fromPromise(promise) {\n  return new Observable(function (subscriber) {\n    promise.then(function (value) {\n      if (!subscriber.closed) {\n        subscriber.next(value);\n        subscriber.complete();\n      }\n    }, function (err) {\n      return subscriber.error(err);\n    }).then(null, reportUnhandledError);\n  });\n}\nexport function fromIterable(iterable) {\n  return new Observable(function (subscriber) {\n    var e_1, _a;\n    try {\n      for (var iterable_1 = __values(iterable), iterable_1_1 = iterable_1.next(); !iterable_1_1.done; iterable_1_1 = iterable_1.next()) {\n        var value = iterable_1_1.value;\n        subscriber.next(value);\n        if (subscriber.closed) {\n          return;\n        }\n      }\n    } catch (e_1_1) {\n      e_1 = {\n        error: e_1_1\n      };\n    } finally {\n      try {\n        if (iterable_1_1 && !iterable_1_1.done && (_a = iterable_1.return)) _a.call(iterable_1);\n      } finally {\n        if (e_1) throw e_1.error;\n      }\n    }\n    subscriber.complete();\n  });\n}\nexport function fromAsyncIterable(asyncIterable) {\n  return new Observable(function (subscriber) {\n    process(asyncIterable, subscriber).catch(function (err) {\n      return subscriber.error(err);\n    });\n  });\n}\nexport function fromReadableStreamLike(readableStream) {\n  return fromAsyncIterable(readableStreamLikeToAsyncGenerator(readableStream));\n}\nfunction process(asyncIterable, subscriber) {\n  var asyncIterable_1, asyncIterable_1_1;\n  var e_2, _a;\n  return __awaiter(this, void 0, void 0, function () {\n    var value, e_2_1;\n    return __generator(this, function (_b) {\n      switch (_b.label) {\n        case 0:\n          _b.trys.push([0, 5, 6, 11]);\n          asyncIterable_1 = __asyncValues(asyncIterable);\n          _b.label = 1;\n        case 1:\n          return [4, asyncIterable_1.next()];\n        case 2:\n          if (!(asyncIterable_1_1 = _b.sent(), !asyncIterable_1_1.done)) return [3, 4];\n          value = asyncIterable_1_1.value;\n          subscriber.next(value);\n          if (subscriber.closed) {\n            return [2];\n          }\n          _b.label = 3;\n        case 3:\n          return [3, 1];\n        case 4:\n          return [3, 11];\n        case 5:\n          e_2_1 = _b.sent();\n          e_2 = {\n            error: e_2_1\n          };\n          return [3, 11];\n        case 6:\n          _b.trys.push([6,, 9, 10]);\n          if (!(asyncIterable_1_1 && !asyncIterable_1_1.done && (_a = asyncIterable_1.return))) return [3, 8];\n          return [4, _a.call(asyncIterable_1)];\n        case 7:\n          _b.sent();\n          _b.label = 8;\n        case 8:\n          return [3, 10];\n        case 9:\n          if (e_2) throw e_2.error;\n          return [7];\n        case 10:\n          return [7];\n        case 11:\n          subscriber.complete();\n          return [2];\n      }\n    });\n  });\n}", "map": {"version": 3, "names": ["isArrayLike", "isPromise", "Observable", "isInteropObservable", "isAsyncIterable", "createInvalidObservableTypeError", "isIterable", "isReadableStreamLike", "readableStreamLikeToAsyncGenerator", "isFunction", "reportUnhandledError", "observable", "Symbol_observable", "innerFrom", "input", "fromInteropObservable", "fromArrayLike", "fromPromise", "fromAsyncIterable", "fromIterable", "fromReadableStreamLike", "obj", "subscriber", "obs", "subscribe", "TypeError", "array", "i", "length", "closed", "next", "complete", "promise", "then", "value", "err", "error", "iterable", "iterable_1", "__values", "iterable_1_1", "done", "asyncIterable", "process", "catch", "readableStream", "asyncIterable_1", "__asyncValues", "asyncIterable_1_1"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\observable\\innerFrom.ts"], "sourcesContent": ["import { isArrayLike } from '../util/isArrayLike';\nimport { isPromise } from '../util/isPromise';\nimport { Observable } from '../Observable';\nimport { ObservableInput, ObservedValueOf, ReadableStreamLike } from '../types';\nimport { isInteropObservable } from '../util/isInteropObservable';\nimport { isAsyncIterable } from '../util/isAsyncIterable';\nimport { createInvalidObservableTypeError } from '../util/throwUnobservableError';\nimport { isIterable } from '../util/isIterable';\nimport { isReadableStreamLike, readableStreamLikeToAsyncGenerator } from '../util/isReadableStreamLike';\nimport { Subscriber } from '../Subscriber';\nimport { isFunction } from '../util/isFunction';\nimport { reportUnhandledError } from '../util/reportUnhandledError';\nimport { observable as Symbol_observable } from '../symbol/observable';\n\nexport function innerFrom<O extends ObservableInput<any>>(input: O): Observable<ObservedValueOf<O>>;\nexport function innerFrom<T>(input: ObservableInput<T>): Observable<T> {\n  if (input instanceof Observable) {\n    return input;\n  }\n  if (input != null) {\n    if (isInteropObservable(input)) {\n      return fromInteropObservable(input);\n    }\n    if (isArrayLike(input)) {\n      return fromArrayLike(input);\n    }\n    if (isPromise(input)) {\n      return fromPromise(input);\n    }\n    if (isAsyncIterable(input)) {\n      return fromAsyncIterable(input);\n    }\n    if (isIterable(input)) {\n      return fromIterable(input);\n    }\n    if (isReadableStreamLike(input)) {\n      return fromReadableStreamLike(input);\n    }\n  }\n\n  throw createInvalidObservableTypeError(input);\n}\n\n/**\n * Creates an RxJS Observable from an object that implements `Symbol.observable`.\n * @param obj An object that properly implements `Symbol.observable`.\n */\nexport function fromInteropObservable<T>(obj: any) {\n  return new Observable((subscriber: Subscriber<T>) => {\n    const obs = obj[Symbol_observable]();\n    if (isFunction(obs.subscribe)) {\n      return obs.subscribe(subscriber);\n    }\n    // Should be caught by observable subscribe function error handling.\n    throw new TypeError('Provided object does not correctly implement Symbol.observable');\n  });\n}\n\n/**\n * Synchronously emits the values of an array like and completes.\n * This is exported because there are creation functions and operators that need to\n * make direct use of the same logic, and there's no reason to make them run through\n * `from` conditionals because we *know* they're dealing with an array.\n * @param array The array to emit values from\n */\nexport function fromArrayLike<T>(array: ArrayLike<T>) {\n  return new Observable((subscriber: Subscriber<T>) => {\n    // Loop over the array and emit each value. Note two things here:\n    // 1. We're making sure that the subscriber is not closed on each loop.\n    //    This is so we don't continue looping over a very large array after\n    //    something like a `take`, `takeWhile`, or other synchronous unsubscription\n    //    has already unsubscribed.\n    // 2. In this form, reentrant code can alter that array we're looping over.\n    //    This is a known issue, but considered an edge case. The alternative would\n    //    be to copy the array before executing the loop, but this has\n    //    performance implications.\n    for (let i = 0; i < array.length && !subscriber.closed; i++) {\n      subscriber.next(array[i]);\n    }\n    subscriber.complete();\n  });\n}\n\nexport function fromPromise<T>(promise: PromiseLike<T>) {\n  return new Observable((subscriber: Subscriber<T>) => {\n    promise\n      .then(\n        (value) => {\n          if (!subscriber.closed) {\n            subscriber.next(value);\n            subscriber.complete();\n          }\n        },\n        (err: any) => subscriber.error(err)\n      )\n      .then(null, reportUnhandledError);\n  });\n}\n\nexport function fromIterable<T>(iterable: Iterable<T>) {\n  return new Observable((subscriber: Subscriber<T>) => {\n    for (const value of iterable) {\n      subscriber.next(value);\n      if (subscriber.closed) {\n        return;\n      }\n    }\n    subscriber.complete();\n  });\n}\n\nexport function fromAsyncIterable<T>(asyncIterable: AsyncIterable<T>) {\n  return new Observable((subscriber: Subscriber<T>) => {\n    process(asyncIterable, subscriber).catch((err) => subscriber.error(err));\n  });\n}\n\nexport function fromReadableStreamLike<T>(readableStream: ReadableStreamLike<T>) {\n  return fromAsyncIterable(readableStreamLikeToAsyncGenerator(readableStream));\n}\n\nasync function process<T>(asyncIterable: AsyncIterable<T>, subscriber: Subscriber<T>) {\n  for await (const value of asyncIterable) {\n    subscriber.next(value);\n    // A side-effect may have closed our subscriber,\n    // check before the next iteration.\n    if (subscriber.closed) {\n      return;\n    }\n  }\n  subscriber.complete();\n}\n"], "mappings": ";AAAA,SAASA,WAAW,QAAQ,qBAAqB;AACjD,SAASC,SAAS,QAAQ,mBAAmB;AAC7C,SAASC,UAAU,QAAQ,eAAe;AAE1C,SAASC,mBAAmB,QAAQ,6BAA6B;AACjE,SAASC,eAAe,QAAQ,yBAAyB;AACzD,SAASC,gCAAgC,QAAQ,gCAAgC;AACjF,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,SAASC,oBAAoB,EAAEC,kCAAkC,QAAQ,8BAA8B;AAEvG,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,SAASC,oBAAoB,QAAQ,8BAA8B;AACnE,SAASC,UAAU,IAAIC,iBAAiB,QAAQ,sBAAsB;AAGtE,OAAM,SAAUC,SAASA,CAAIC,KAAyB;EACpD,IAAIA,KAAK,YAAYZ,UAAU,EAAE;IAC/B,OAAOY,KAAK;;EAEd,IAAIA,KAAK,IAAI,IAAI,EAAE;IACjB,IAAIX,mBAAmB,CAACW,KAAK,CAAC,EAAE;MAC9B,OAAOC,qBAAqB,CAACD,KAAK,CAAC;;IAErC,IAAId,WAAW,CAACc,KAAK,CAAC,EAAE;MACtB,OAAOE,aAAa,CAACF,KAAK,CAAC;;IAE7B,IAAIb,SAAS,CAACa,KAAK,CAAC,EAAE;MACpB,OAAOG,WAAW,CAACH,KAAK,CAAC;;IAE3B,IAAIV,eAAe,CAACU,KAAK,CAAC,EAAE;MAC1B,OAAOI,iBAAiB,CAACJ,KAAK,CAAC;;IAEjC,IAAIR,UAAU,CAACQ,KAAK,CAAC,EAAE;MACrB,OAAOK,YAAY,CAACL,KAAK,CAAC;;IAE5B,IAAIP,oBAAoB,CAACO,KAAK,CAAC,EAAE;MAC/B,OAAOM,sBAAsB,CAACN,KAAK,CAAC;;;EAIxC,MAAMT,gCAAgC,CAACS,KAAK,CAAC;AAC/C;AAMA,OAAM,SAAUC,qBAAqBA,CAAIM,GAAQ;EAC/C,OAAO,IAAInB,UAAU,CAAC,UAACoB,UAAyB;IAC9C,IAAMC,GAAG,GAAGF,GAAG,CAACT,iBAAiB,CAAC,EAAE;IACpC,IAAIH,UAAU,CAACc,GAAG,CAACC,SAAS,CAAC,EAAE;MAC7B,OAAOD,GAAG,CAACC,SAAS,CAACF,UAAU,CAAC;;IAGlC,MAAM,IAAIG,SAAS,CAAC,gEAAgE,CAAC;EACvF,CAAC,CAAC;AACJ;AASA,OAAM,SAAUT,aAAaA,CAAIU,KAAmB;EAClD,OAAO,IAAIxB,UAAU,CAAC,UAACoB,UAAyB;IAU9C,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACE,MAAM,IAAI,CAACN,UAAU,CAACO,MAAM,EAAEF,CAAC,EAAE,EAAE;MAC3DL,UAAU,CAACQ,IAAI,CAACJ,KAAK,CAACC,CAAC,CAAC,CAAC;;IAE3BL,UAAU,CAACS,QAAQ,EAAE;EACvB,CAAC,CAAC;AACJ;AAEA,OAAM,SAAUd,WAAWA,CAAIe,OAAuB;EACpD,OAAO,IAAI9B,UAAU,CAAC,UAACoB,UAAyB;IAC9CU,OAAO,CACJC,IAAI,CACH,UAACC,KAAK;MACJ,IAAI,CAACZ,UAAU,CAACO,MAAM,EAAE;QACtBP,UAAU,CAACQ,IAAI,CAACI,KAAK,CAAC;QACtBZ,UAAU,CAACS,QAAQ,EAAE;;IAEzB,CAAC,EACD,UAACI,GAAQ;MAAK,OAAAb,UAAU,CAACc,KAAK,CAACD,GAAG,CAAC;IAArB,CAAqB,CACpC,CACAF,IAAI,CAAC,IAAI,EAAEvB,oBAAoB,CAAC;EACrC,CAAC,CAAC;AACJ;AAEA,OAAM,SAAUS,YAAYA,CAAIkB,QAAqB;EACnD,OAAO,IAAInC,UAAU,CAAC,UAACoB,UAAyB;;;MAC9C,KAAoB,IAAAgB,UAAA,GAAAC,QAAA,CAAAF,QAAQ,GAAAG,YAAA,GAAAF,UAAA,CAAAR,IAAA,KAAAU,YAAA,CAAAC,IAAA,EAAAD,YAAA,GAAAF,UAAA,CAAAR,IAAA,IAAE;QAAzB,IAAMI,KAAK,GAAAM,YAAA,CAAAN,KAAA;QACdZ,UAAU,CAACQ,IAAI,CAACI,KAAK,CAAC;QACtB,IAAIZ,UAAU,CAACO,MAAM,EAAE;UACrB;;;;;;;;;;;;;;IAGJP,UAAU,CAACS,QAAQ,EAAE;EACvB,CAAC,CAAC;AACJ;AAEA,OAAM,SAAUb,iBAAiBA,CAAIwB,aAA+B;EAClE,OAAO,IAAIxC,UAAU,CAAC,UAACoB,UAAyB;IAC9CqB,OAAO,CAACD,aAAa,EAAEpB,UAAU,CAAC,CAACsB,KAAK,CAAC,UAACT,GAAG;MAAK,OAAAb,UAAU,CAACc,KAAK,CAACD,GAAG,CAAC;IAArB,CAAqB,CAAC;EAC1E,CAAC,CAAC;AACJ;AAEA,OAAM,SAAUf,sBAAsBA,CAAIyB,cAAqC;EAC7E,OAAO3B,iBAAiB,CAACV,kCAAkC,CAACqC,cAAc,CAAC,CAAC;AAC9E;AAEA,SAAeF,OAAOA,CAAID,aAA+B,EAAEpB,UAAyB;;;;;;;;;UACxDwB,eAAA,GAAAC,aAAA,CAAAL,aAAa;;;;;;UAAtBR,KAAK,GAAAc,iBAAA,CAAAd,KAAA;UACpBZ,UAAU,CAACQ,IAAI,CAACI,KAAK,CAAC;UAGtB,IAAIZ,UAAU,CAACO,MAAM,EAAE;YACrB;;;;;;;;;;;;;;;;;;;;;;;;;;;;UAGJP,UAAU,CAACS,QAAQ,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}