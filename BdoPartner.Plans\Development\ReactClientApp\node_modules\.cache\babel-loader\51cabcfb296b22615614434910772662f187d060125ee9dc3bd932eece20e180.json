{"ast": null, "code": "export var dateTimestampProvider = {\n  now: function () {\n    return (dateTimestampProvider.delegate || Date).now();\n  },\n  delegate: undefined\n};", "map": {"version": 3, "names": ["dateTimestampProvider", "now", "delegate", "Date", "undefined"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\scheduler\\dateTimestampProvider.ts"], "sourcesContent": ["import { TimestampProvider } from '../types';\n\ninterface DateTimestampProvider extends TimestampProvider {\n  delegate: TimestampProvider | undefined;\n}\n\nexport const dateTimestampProvider: DateTimestampProvider = {\n  now() {\n    // Use the variable rather than `this` so that the function can be called\n    // without being bound to the provider.\n    return (dateTimestampProvider.delegate || Date).now();\n  },\n  delegate: undefined,\n};\n"], "mappings": "AAMA,OAAO,IAAMA,qBAAqB,GAA0B;EAC1DC,GAAG,WAAAA,CAAA;IAGD,OAAO,CAACD,qBAAqB,CAACE,QAAQ,IAAIC,IAAI,EAAEF,GAAG,EAAE;EACvD,CAAC;EACDC,QAAQ,EAAEE;CACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}