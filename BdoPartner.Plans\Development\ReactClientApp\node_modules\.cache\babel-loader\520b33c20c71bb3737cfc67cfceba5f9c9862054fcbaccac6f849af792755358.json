{"ast": null, "code": "import { EmptyError } from './util/EmptyError';\nexport function lastValueFrom(source, config) {\n  var hasConfig = typeof config === 'object';\n  return new Promise(function (resolve, reject) {\n    var _hasValue = false;\n    var _value;\n    source.subscribe({\n      next: function (value) {\n        _value = value;\n        _hasValue = true;\n      },\n      error: reject,\n      complete: function () {\n        if (_hasValue) {\n          resolve(_value);\n        } else if (hasConfig) {\n          resolve(config.defaultValue);\n        } else {\n          reject(new EmptyError());\n        }\n      }\n    });\n  });\n}", "map": {"version": 3, "names": ["EmptyError", "lastValueFrom", "source", "config", "hasConfig", "Promise", "resolve", "reject", "_hasValue", "_value", "subscribe", "next", "value", "error", "complete", "defaultValue"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\lastValueFrom.ts"], "sourcesContent": ["import { Observable } from './Observable';\nimport { EmptyError } from './util/EmptyError';\n\nexport interface LastValueFromConfig<T> {\n  defaultValue: T;\n}\n\nexport function lastValueFrom<T, D>(source: Observable<T>, config: LastValueFromConfig<D>): Promise<T | D>;\nexport function lastValueFrom<T>(source: Observable<T>): Promise<T>;\n\n/**\n * Converts an observable to a promise by subscribing to the observable,\n * waiting for it to complete, and resolving the returned promise with the\n * last value from the observed stream.\n *\n * If the observable stream completes before any values were emitted, the\n * returned promise will reject with {@link EmptyError} or will resolve\n * with the default value if a default was specified.\n *\n * If the observable stream emits an error, the returned promise will reject\n * with that error.\n *\n * **WARNING**: Only use this with observables you *know* will complete. If the source\n * observable does not complete, you will end up with a promise that is hung up, and\n * potentially all of the state of an async function hanging out in memory. To avoid\n * this situation, look into adding something like {@link timeout}, {@link take},\n * {@link takeWhile}, or {@link takeUntil} amongst others.\n *\n * ## Example\n *\n * Wait for the last value from a stream and emit it from a promise in\n * an async function\n *\n * ```ts\n * import { interval, take, lastValueFrom } from 'rxjs';\n *\n * async function execute() {\n *   const source$ = interval(2000).pipe(take(10));\n *   const finalNumber = await lastValueFrom(source$);\n *   console.log(`The final number is ${ finalNumber }`);\n * }\n *\n * execute();\n *\n * // Expected output:\n * // 'The final number is 9'\n * ```\n *\n * @see {@link firstValueFrom}\n *\n * @param source the observable to convert to a promise\n * @param config a configuration object to define the `defaultValue` to use if the source completes without emitting a value\n */\nexport function lastValueFrom<T, D>(source: Observable<T>, config?: LastValueFromConfig<D>): Promise<T | D> {\n  const hasConfig = typeof config === 'object';\n  return new Promise<T | D>((resolve, reject) => {\n    let _hasValue = false;\n    let _value: T;\n    source.subscribe({\n      next: (value) => {\n        _value = value;\n        _hasValue = true;\n      },\n      error: reject,\n      complete: () => {\n        if (_hasValue) {\n          resolve(_value);\n        } else if (hasConfig) {\n          resolve(config!.defaultValue);\n        } else {\n          reject(new EmptyError());\n        }\n      },\n    });\n  });\n}\n"], "mappings": "AACA,SAASA,UAAU,QAAQ,mBAAmB;AAoD9C,OAAM,SAAUC,aAAaA,CAAOC,MAAqB,EAAEC,MAA+B;EACxF,IAAMC,SAAS,GAAG,OAAOD,MAAM,KAAK,QAAQ;EAC5C,OAAO,IAAIE,OAAO,CAAQ,UAACC,OAAO,EAAEC,MAAM;IACxC,IAAIC,SAAS,GAAG,KAAK;IACrB,IAAIC,MAAS;IACbP,MAAM,CAACQ,SAAS,CAAC;MACfC,IAAI,EAAE,SAAAA,CAACC,KAAK;QACVH,MAAM,GAAGG,KAAK;QACdJ,SAAS,GAAG,IAAI;MAClB,CAAC;MACDK,KAAK,EAAEN,MAAM;MACbO,QAAQ,EAAE,SAAAA,CAAA;QACR,IAAIN,SAAS,EAAE;UACbF,OAAO,CAACG,MAAM,CAAC;SAChB,MAAM,IAAIL,SAAS,EAAE;UACpBE,OAAO,CAACH,MAAO,CAACY,YAAY,CAAC;SAC9B,MAAM;UACLR,MAAM,CAAC,IAAIP,UAAU,EAAE,CAAC;;MAE5B;KACD,CAAC;EACJ,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}