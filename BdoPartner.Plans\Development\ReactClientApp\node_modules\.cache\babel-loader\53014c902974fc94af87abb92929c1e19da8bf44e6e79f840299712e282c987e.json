{"ast": null, "code": "import { Subject } from '../Subject';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { noop } from '../util/noop';\nimport { innerFrom } from '../observable/innerFrom';\nexport function window(windowBoundaries) {\n  return operate(function (source, subscriber) {\n    var windowSubject = new Subject();\n    subscriber.next(windowSubject.asObservable());\n    var errorHandler = function (err) {\n      windowSubject.error(err);\n      subscriber.error(err);\n    };\n    source.subscribe(createOperatorSubscriber(subscriber, function (value) {\n      return windowSubject === null || windowSubject === void 0 ? void 0 : windowSubject.next(value);\n    }, function () {\n      windowSubject.complete();\n      subscriber.complete();\n    }, errorHandler));\n    innerFrom(windowBoundaries).subscribe(createOperatorSubscriber(subscriber, function () {\n      windowSubject.complete();\n      subscriber.next(windowSubject = new Subject());\n    }, noop, errorHandler));\n    return function () {\n      windowSubject === null || windowSubject === void 0 ? void 0 : windowSubject.unsubscribe();\n      windowSubject = null;\n    };\n  });\n}", "map": {"version": 3, "names": ["Subject", "operate", "createOperatorSubscriber", "noop", "innerFrom", "window", "windowBoundaries", "source", "subscriber", "windowSubject", "next", "asObservable", "<PERSON><PERSON><PERSON><PERSON>", "err", "error", "subscribe", "value", "complete", "unsubscribe"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\window.ts"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { OperatorFunction, ObservableInput } from '../types';\nimport { Subject } from '../Subject';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { noop } from '../util/noop';\nimport { innerFrom } from '../observable/innerFrom';\n\n/**\n * Branch out the source Observable values as a nested Observable whenever\n * `windowBoundaries` emits.\n *\n * <span class=\"informal\">It's like {@link buffer}, but emits a nested Observable\n * instead of an array.</span>\n *\n * ![](window.png)\n *\n * Returns an Observable that emits windows of items it collects from the source\n * Observable. The output Observable emits connected, non-overlapping\n * windows. It emits the current window and opens a new one whenever the\n * `windowBoundaries` emits an item. `windowBoundaries` can be any type that\n * `ObservableInput` accepts. It internally gets converted to an Observable.\n * Because each window is an Observable, the output is a higher-order Observable.\n *\n * ## Example\n *\n * In every window of 1 second each, emit at most 2 click events\n *\n * ```ts\n * import { fromEvent, interval, window, map, take, mergeAll } from 'rxjs';\n *\n * const clicks = fromEvent(document, 'click');\n * const sec = interval(1000);\n * const result = clicks.pipe(\n *   window(sec),\n *   map(win => win.pipe(take(2))), // take at most 2 emissions from each window\n *   mergeAll()                     // flatten the Observable-of-Observables\n * );\n * result.subscribe(x => console.log(x));\n * ```\n *\n * @see {@link windowCount}\n * @see {@link windowTime}\n * @see {@link windowToggle}\n * @see {@link windowWhen}\n * @see {@link buffer}\n *\n * @param windowBoundaries An `ObservableInput` that completes the\n * previous window and starts a new window.\n * @return A function that returns an Observable of windows, which are\n * Observables emitting values of the source Observable.\n */\nexport function window<T>(windowBoundaries: ObservableInput<any>): OperatorFunction<T, Observable<T>> {\n  return operate((source, subscriber) => {\n    let windowSubject: Subject<T> = new Subject<T>();\n\n    subscriber.next(windowSubject.asObservable());\n\n    const errorHandler = (err: any) => {\n      windowSubject.error(err);\n      subscriber.error(err);\n    };\n\n    // Subscribe to our source\n    source.subscribe(\n      createOperatorSubscriber(\n        subscriber,\n        (value) => windowSubject?.next(value),\n        () => {\n          windowSubject.complete();\n          subscriber.complete();\n        },\n        errorHandler\n      )\n    );\n\n    // Subscribe to the window boundaries.\n    innerFrom(windowBoundaries).subscribe(\n      createOperatorSubscriber(\n        subscriber,\n        () => {\n          windowSubject.complete();\n          subscriber.next((windowSubject = new Subject()));\n        },\n        noop,\n        errorHandler\n      )\n    );\n\n    return () => {\n      // Unsubscribing the subject ensures that anyone who has captured\n      // a reference to this window that tries to use it after it can\n      // no longer get values from the source will get an ObjectUnsubscribedError.\n      windowSubject?.unsubscribe();\n      windowSubject = null!;\n    };\n  });\n}\n"], "mappings": "AAEA,SAASA,OAAO,QAAQ,YAAY;AACpC,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,SAASC,IAAI,QAAQ,cAAc;AACnC,SAASC,SAAS,QAAQ,yBAAyB;AA8CnD,OAAM,SAAUC,MAAMA,CAAIC,gBAAsC;EAC9D,OAAOL,OAAO,CAAC,UAACM,MAAM,EAAEC,UAAU;IAChC,IAAIC,aAAa,GAAe,IAAIT,OAAO,EAAK;IAEhDQ,UAAU,CAACE,IAAI,CAACD,aAAa,CAACE,YAAY,EAAE,CAAC;IAE7C,IAAMC,YAAY,GAAG,SAAAA,CAACC,GAAQ;MAC5BJ,aAAa,CAACK,KAAK,CAACD,GAAG,CAAC;MACxBL,UAAU,CAACM,KAAK,CAACD,GAAG,CAAC;IACvB,CAAC;IAGDN,MAAM,CAACQ,SAAS,CACdb,wBAAwB,CACtBM,UAAU,EACV,UAACQ,KAAK;MAAK,OAAAP,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEC,IAAI,CAACM,KAAK,CAAC;IAA1B,CAA0B,EACrC;MACEP,aAAa,CAACQ,QAAQ,EAAE;MACxBT,UAAU,CAACS,QAAQ,EAAE;IACvB,CAAC,EACDL,YAAY,CACb,CACF;IAGDR,SAAS,CAACE,gBAAgB,CAAC,CAACS,SAAS,CACnCb,wBAAwB,CACtBM,UAAU,EACV;MACEC,aAAa,CAACQ,QAAQ,EAAE;MACxBT,UAAU,CAACE,IAAI,CAAED,aAAa,GAAG,IAAIT,OAAO,EAAG,CAAC;IAClD,CAAC,EACDG,IAAI,EACJS,YAAY,CACb,CACF;IAED,OAAO;MAILH,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAES,WAAW,EAAE;MAC5BT,aAAa,GAAG,IAAK;IACvB,CAAC;EACH,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}