{"ast": null, "code": "import { __values } from \"tslib\";\nimport { Subscription } from '../Subscription';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { arrRemove } from '../util/arrRemove';\nimport { asyncScheduler } from '../scheduler/async';\nimport { popScheduler } from '../util/args';\nimport { executeSchedule } from '../util/executeSchedule';\nexport function bufferTime(bufferTimeSpan) {\n  var _a, _b;\n  var otherArgs = [];\n  for (var _i = 1; _i < arguments.length; _i++) {\n    otherArgs[_i - 1] = arguments[_i];\n  }\n  var scheduler = (_a = popScheduler(otherArgs)) !== null && _a !== void 0 ? _a : asyncScheduler;\n  var bufferCreationInterval = (_b = otherArgs[0]) !== null && _b !== void 0 ? _b : null;\n  var maxBufferSize = otherArgs[1] || Infinity;\n  return operate(function (source, subscriber) {\n    var bufferRecords = [];\n    var restartOnEmit = false;\n    var emit = function (record) {\n      var buffer = record.buffer,\n        subs = record.subs;\n      subs.unsubscribe();\n      arrRemove(bufferRecords, record);\n      subscriber.next(buffer);\n      restartOnEmit && startBuffer();\n    };\n    var startBuffer = function () {\n      if (bufferRecords) {\n        var subs = new Subscription();\n        subscriber.add(subs);\n        var buffer = [];\n        var record_1 = {\n          buffer: buffer,\n          subs: subs\n        };\n        bufferRecords.push(record_1);\n        executeSchedule(subs, scheduler, function () {\n          return emit(record_1);\n        }, bufferTimeSpan);\n      }\n    };\n    if (bufferCreationInterval !== null && bufferCreationInterval >= 0) {\n      executeSchedule(subscriber, scheduler, startBuffer, bufferCreationInterval, true);\n    } else {\n      restartOnEmit = true;\n    }\n    startBuffer();\n    var bufferTimeSubscriber = createOperatorSubscriber(subscriber, function (value) {\n      var e_1, _a;\n      var recordsCopy = bufferRecords.slice();\n      try {\n        for (var recordsCopy_1 = __values(recordsCopy), recordsCopy_1_1 = recordsCopy_1.next(); !recordsCopy_1_1.done; recordsCopy_1_1 = recordsCopy_1.next()) {\n          var record = recordsCopy_1_1.value;\n          var buffer = record.buffer;\n          buffer.push(value);\n          maxBufferSize <= buffer.length && emit(record);\n        }\n      } catch (e_1_1) {\n        e_1 = {\n          error: e_1_1\n        };\n      } finally {\n        try {\n          if (recordsCopy_1_1 && !recordsCopy_1_1.done && (_a = recordsCopy_1.return)) _a.call(recordsCopy_1);\n        } finally {\n          if (e_1) throw e_1.error;\n        }\n      }\n    }, function () {\n      while (bufferRecords === null || bufferRecords === void 0 ? void 0 : bufferRecords.length) {\n        subscriber.next(bufferRecords.shift().buffer);\n      }\n      bufferTimeSubscriber === null || bufferTimeSubscriber === void 0 ? void 0 : bufferTimeSubscriber.unsubscribe();\n      subscriber.complete();\n      subscriber.unsubscribe();\n    }, undefined, function () {\n      return bufferRecords = null;\n    });\n    source.subscribe(bufferTimeSubscriber);\n  });\n}", "map": {"version": 3, "names": ["Subscription", "operate", "createOperatorSubscriber", "arr<PERSON><PERSON><PERSON>", "asyncScheduler", "popScheduler", "executeSchedule", "bufferTime", "bufferTimeSpan", "otherArgs", "_i", "arguments", "length", "scheduler", "_a", "bufferCreationInterval", "_b", "maxBufferSize", "Infinity", "source", "subscriber", "bufferRecords", "restartOnEmit", "emit", "record", "buffer", "subs", "unsubscribe", "next", "startBuffer", "add", "record_1", "push", "bufferTimeSubscriber", "value", "recordsCopy", "slice", "recordsCopy_1", "__values", "recordsCopy_1_1", "done", "shift", "complete", "undefined", "subscribe"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\bufferTime.ts"], "sourcesContent": ["import { Subscription } from '../Subscription';\nimport { OperatorFunction, SchedulerLike } from '../types';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { arrRemove } from '../util/arrRemove';\nimport { asyncScheduler } from '../scheduler/async';\nimport { popScheduler } from '../util/args';\nimport { executeSchedule } from '../util/executeSchedule';\n\nexport function bufferTime<T>(bufferTimeSpan: number, scheduler?: SchedulerLike): OperatorFunction<T, T[]>;\nexport function bufferTime<T>(\n  bufferTimeSpan: number,\n  bufferCreationInterval: number | null | undefined,\n  scheduler?: SchedulerLike\n): OperatorFunction<T, T[]>;\nexport function bufferTime<T>(\n  bufferTimeSpan: number,\n  bufferCreationInterval: number | null | undefined,\n  maxBufferSize: number,\n  scheduler?: SchedulerLike\n): OperatorFunction<T, T[]>;\n\n/**\n * Buffers the source Observable values for a specific time period.\n *\n * <span class=\"informal\">Collects values from the past as an array, and emits\n * those arrays periodically in time.</span>\n *\n * ![](bufferTime.png)\n *\n * Buffers values from the source for a specific time duration `bufferTimeSpan`.\n * Unless the optional argument `bufferCreationInterval` is given, it emits and\n * resets the buffer every `bufferTimeSpan` milliseconds. If\n * `bufferCreationInterval` is given, this operator opens the buffer every\n * `bufferCreationInterval` milliseconds and closes (emits and resets) the\n * buffer every `bufferTimeSpan` milliseconds. When the optional argument\n * `maxBufferSize` is specified, the buffer will be closed either after\n * `bufferTimeSpan` milliseconds or when it contains `maxBufferSize` elements.\n *\n * ## Examples\n *\n * Every second, emit an array of the recent click events\n *\n * ```ts\n * import { fromEvent, bufferTime } from 'rxjs';\n *\n * const clicks = fromEvent(document, 'click');\n * const buffered = clicks.pipe(bufferTime(1000));\n * buffered.subscribe(x => console.log(x));\n * ```\n *\n * Every 5 seconds, emit the click events from the next 2 seconds\n *\n * ```ts\n * import { fromEvent, bufferTime } from 'rxjs';\n *\n * const clicks = fromEvent(document, 'click');\n * const buffered = clicks.pipe(bufferTime(2000, 5000));\n * buffered.subscribe(x => console.log(x));\n * ```\n *\n * @see {@link buffer}\n * @see {@link bufferCount}\n * @see {@link bufferToggle}\n * @see {@link bufferWhen}\n * @see {@link windowTime}\n *\n * @param bufferTimeSpan The amount of time to fill each buffer array.\n * @param otherArgs Other configuration arguments such as:\n * - `bufferCreationInterval` - the interval at which to start new buffers;\n * - `maxBufferSize` - the maximum buffer size;\n * - `scheduler` - the scheduler on which to schedule the intervals that determine buffer boundaries.\n * @return A function that returns an Observable of arrays of buffered values.\n */\nexport function bufferTime<T>(bufferTimeSpan: number, ...otherArgs: any[]): OperatorFunction<T, T[]> {\n  const scheduler = popScheduler(otherArgs) ?? asyncScheduler;\n  const bufferCreationInterval = (otherArgs[0] as number) ?? null;\n  const maxBufferSize = (otherArgs[1] as number) || Infinity;\n\n  return operate((source, subscriber) => {\n    // The active buffers, their related subscriptions, and removal functions.\n    let bufferRecords: { buffer: T[]; subs: Subscription }[] | null = [];\n    // If true, it means that every time we emit a buffer, we want to start a new buffer\n    // this is only really used for when *just* the buffer time span is passed.\n    let restartOnEmit = false;\n\n    /**\n     * Does the work of emitting the buffer from the record, ensuring that the\n     * record is removed before the emission so reentrant code (from some custom scheduling, perhaps)\n     * does not alter the buffer. Also checks to see if a new buffer needs to be started\n     * after the emit.\n     */\n    const emit = (record: { buffer: T[]; subs: Subscription }) => {\n      const { buffer, subs } = record;\n      subs.unsubscribe();\n      arrRemove(bufferRecords, record);\n      subscriber.next(buffer);\n      restartOnEmit && startBuffer();\n    };\n\n    /**\n     * Called every time we start a new buffer. This does\n     * the work of scheduling a job at the requested bufferTimeSpan\n     * that will emit the buffer (if it's not unsubscribed before then).\n     */\n    const startBuffer = () => {\n      if (bufferRecords) {\n        const subs = new Subscription();\n        subscriber.add(subs);\n        const buffer: T[] = [];\n        const record = {\n          buffer,\n          subs,\n        };\n        bufferRecords.push(record);\n        executeSchedule(subs, scheduler, () => emit(record), bufferTimeSpan);\n      }\n    };\n\n    if (bufferCreationInterval !== null && bufferCreationInterval >= 0) {\n      // The user passed both a bufferTimeSpan (required), and a creation interval\n      // That means we need to start new buffers on the interval, and those buffers need\n      // to wait the required time span before emitting.\n      executeSchedule(subscriber, scheduler, startBuffer, bufferCreationInterval, true);\n    } else {\n      restartOnEmit = true;\n    }\n\n    startBuffer();\n\n    const bufferTimeSubscriber = createOperatorSubscriber(\n      subscriber,\n      (value: T) => {\n        // Copy the records, so if we need to remove one we\n        // don't mutate the array. It's hard, but not impossible to\n        // set up a buffer time that could mutate the array and\n        // cause issues here.\n        const recordsCopy = bufferRecords!.slice();\n        for (const record of recordsCopy) {\n          // Loop over all buffers and\n          const { buffer } = record;\n          buffer.push(value);\n          // If the buffer is over the max size, we need to emit it.\n          maxBufferSize <= buffer.length && emit(record);\n        }\n      },\n      () => {\n        // The source completed, emit all of the active\n        // buffers we have before we complete.\n        while (bufferRecords?.length) {\n          subscriber.next(bufferRecords.shift()!.buffer);\n        }\n        bufferTimeSubscriber?.unsubscribe();\n        subscriber.complete();\n        subscriber.unsubscribe();\n      },\n      // Pass all errors through to consumer.\n      undefined,\n      // Clean up\n      () => (bufferRecords = null)\n    );\n\n    source.subscribe(bufferTimeSubscriber);\n  });\n}\n"], "mappings": ";AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,SAASC,SAAS,QAAQ,mBAAmB;AAC7C,SAASC,cAAc,QAAQ,oBAAoB;AACnD,SAASC,YAAY,QAAQ,cAAc;AAC3C,SAASC,eAAe,QAAQ,yBAAyB;AAmEzD,OAAM,SAAUC,UAAUA,CAAIC,cAAsB;;EAAE,IAAAC,SAAA;OAAA,IAAAC,EAAA,IAAmB,EAAnBA,EAAA,GAAAC,SAAA,CAAAC,MAAmB,EAAnBF,EAAA,EAAmB;IAAnBD,SAAA,CAAAC,EAAA,QAAAC,SAAA,CAAAD,EAAA;;EACpD,IAAMG,SAAS,GAAG,CAAAC,EAAA,GAAAT,YAAY,CAACI,SAAS,CAAC,cAAAK,EAAA,cAAAA,EAAA,GAAIV,cAAc;EAC3D,IAAMW,sBAAsB,GAAG,CAAAC,EAAA,GAACP,SAAS,CAAC,CAAC,CAAY,cAAAO,EAAA,cAAAA,EAAA,GAAI,IAAI;EAC/D,IAAMC,aAAa,GAAIR,SAAS,CAAC,CAAC,CAAY,IAAIS,QAAQ;EAE1D,OAAOjB,OAAO,CAAC,UAACkB,MAAM,EAAEC,UAAU;IAEhC,IAAIC,aAAa,GAAiD,EAAE;IAGpE,IAAIC,aAAa,GAAG,KAAK;IAQzB,IAAMC,IAAI,GAAG,SAAAA,CAACC,MAA2C;MAC/C,IAAAC,MAAM,GAAWD,MAAM,CAAAC,MAAjB;QAAEC,IAAI,GAAKF,MAAM,CAAAE,IAAX;MACpBA,IAAI,CAACC,WAAW,EAAE;MAClBxB,SAAS,CAACkB,aAAa,EAAEG,MAAM,CAAC;MAChCJ,UAAU,CAACQ,IAAI,CAACH,MAAM,CAAC;MACvBH,aAAa,IAAIO,WAAW,EAAE;IAChC,CAAC;IAOD,IAAMA,WAAW,GAAG,SAAAA,CAAA;MAClB,IAAIR,aAAa,EAAE;QACjB,IAAMK,IAAI,GAAG,IAAI1B,YAAY,EAAE;QAC/BoB,UAAU,CAACU,GAAG,CAACJ,IAAI,CAAC;QACpB,IAAMD,MAAM,GAAQ,EAAE;QACtB,IAAMM,QAAM,GAAG;UACbN,MAAM,EAAAA,MAAA;UACNC,IAAI,EAAAA;SACL;QACDL,aAAa,CAACW,IAAI,CAACD,QAAM,CAAC;QAC1BzB,eAAe,CAACoB,IAAI,EAAEb,SAAS,EAAE;UAAM,OAAAU,IAAI,CAACQ,QAAM,CAAC;QAAZ,CAAY,EAAEvB,cAAc,CAAC;;IAExE,CAAC;IAED,IAAIO,sBAAsB,KAAK,IAAI,IAAIA,sBAAsB,IAAI,CAAC,EAAE;MAIlET,eAAe,CAACc,UAAU,EAAEP,SAAS,EAAEgB,WAAW,EAAEd,sBAAsB,EAAE,IAAI,CAAC;KAClF,MAAM;MACLO,aAAa,GAAG,IAAI;;IAGtBO,WAAW,EAAE;IAEb,IAAMI,oBAAoB,GAAG/B,wBAAwB,CACnDkB,UAAU,EACV,UAACc,KAAQ;;MAKP,IAAMC,WAAW,GAAGd,aAAc,CAACe,KAAK,EAAE;;QAC1C,KAAqB,IAAAC,aAAA,GAAAC,QAAA,CAAAH,WAAW,GAAAI,eAAA,GAAAF,aAAA,CAAAT,IAAA,KAAAW,eAAA,CAAAC,IAAA,EAAAD,eAAA,GAAAF,aAAA,CAAAT,IAAA,IAAE;UAA7B,IAAMJ,MAAM,GAAAe,eAAA,CAAAL,KAAA;UAEP,IAAAT,MAAM,GAAKD,MAAM,CAAAC,MAAX;UACdA,MAAM,CAACO,IAAI,CAACE,KAAK,CAAC;UAElBjB,aAAa,IAAIQ,MAAM,CAACb,MAAM,IAAIW,IAAI,CAACC,MAAM,CAAC;;;;;;;;;;;;;IAElD,CAAC,EACD;MAGE,OAAOH,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAET,MAAM,EAAE;QAC5BQ,UAAU,CAACQ,IAAI,CAACP,aAAa,CAACoB,KAAK,EAAG,CAAChB,MAAM,CAAC;;MAEhDQ,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAEN,WAAW,EAAE;MACnCP,UAAU,CAACsB,QAAQ,EAAE;MACrBtB,UAAU,CAACO,WAAW,EAAE;IAC1B,CAAC,EAEDgB,SAAS,EAET;MAAM,OAACtB,aAAa,GAAG,IAAI;IAArB,CAAsB,CAC7B;IAEDF,MAAM,CAACyB,SAAS,CAACX,oBAAoB,CAAC;EACxC,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}