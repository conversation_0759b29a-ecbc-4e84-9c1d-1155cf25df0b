{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { Observable } from '../Observable';\nimport { Subscription } from '../Subscription';\nimport { refCount as higherOrderRefCount } from '../operators/refCount';\nimport { createOperatorSubscriber } from '../operators/OperatorSubscriber';\nimport { hasLift } from '../util/lift';\nvar ConnectableObservable = function (_super) {\n  __extends(ConnectableObservable, _super);\n  function ConnectableObservable(source, subjectFactory) {\n    var _this = _super.call(this) || this;\n    _this.source = source;\n    _this.subjectFactory = subjectFactory;\n    _this._subject = null;\n    _this._refCount = 0;\n    _this._connection = null;\n    if (hasLift(source)) {\n      _this.lift = source.lift;\n    }\n    return _this;\n  }\n  ConnectableObservable.prototype._subscribe = function (subscriber) {\n    return this.getSubject().subscribe(subscriber);\n  };\n  ConnectableObservable.prototype.getSubject = function () {\n    var subject = this._subject;\n    if (!subject || subject.isStopped) {\n      this._subject = this.subjectFactory();\n    }\n    return this._subject;\n  };\n  ConnectableObservable.prototype._teardown = function () {\n    this._refCount = 0;\n    var _connection = this._connection;\n    this._subject = this._connection = null;\n    _connection === null || _connection === void 0 ? void 0 : _connection.unsubscribe();\n  };\n  ConnectableObservable.prototype.connect = function () {\n    var _this = this;\n    var connection = this._connection;\n    if (!connection) {\n      connection = this._connection = new Subscription();\n      var subject_1 = this.getSubject();\n      connection.add(this.source.subscribe(createOperatorSubscriber(subject_1, undefined, function () {\n        _this._teardown();\n        subject_1.complete();\n      }, function (err) {\n        _this._teardown();\n        subject_1.error(err);\n      }, function () {\n        return _this._teardown();\n      })));\n      if (connection.closed) {\n        this._connection = null;\n        connection = Subscription.EMPTY;\n      }\n    }\n    return connection;\n  };\n  ConnectableObservable.prototype.refCount = function () {\n    return higherOrderRefCount()(this);\n  };\n  return ConnectableObservable;\n}(Observable);\nexport { ConnectableObservable };", "map": {"version": 3, "names": ["Observable", "Subscription", "refCount", "higherOrderRefCount", "createOperatorSubscriber", "hasLift", "ConnectableObservable", "_super", "__extends", "source", "subjectFactory", "_this", "call", "_subject", "_refCount", "_connection", "lift", "prototype", "_subscribe", "subscriber", "getSubject", "subscribe", "subject", "isStopped", "_teardown", "unsubscribe", "connect", "connection", "subject_1", "add", "undefined", "complete", "err", "error", "closed", "EMPTY"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\observable\\ConnectableObservable.ts"], "sourcesContent": ["import { Subject } from '../Subject';\nimport { Observable } from '../Observable';\nimport { Subscriber } from '../Subscriber';\nimport { Subscription } from '../Subscription';\nimport { refCount as higherOrderRefCount } from '../operators/refCount';\nimport { createOperatorSubscriber } from '../operators/OperatorSubscriber';\nimport { hasLift } from '../util/lift';\n\n/**\n * @class ConnectableObservable<T>\n * @deprecated Will be removed in v8. Use {@link connectable} to create a connectable observable.\n * If you are using the `refCount` method of `ConnectableObservable`, use the {@link share} operator\n * instead.\n * Details: https://rxjs.dev/deprecations/multicasting\n */\nexport class ConnectableObservable<T> extends Observable<T> {\n  protected _subject: Subject<T> | null = null;\n  protected _refCount: number = 0;\n  protected _connection: Subscription | null = null;\n\n  /**\n   * @param source The source observable\n   * @param subjectFactory The factory that creates the subject used internally.\n   * @deprecated Will be removed in v8. Use {@link connectable} to create a connectable observable.\n   * `new ConnectableObservable(source, factory)` is equivalent to\n   * `connectable(source, { connector: factory })`.\n   * When the `refCount()` method is needed, the {@link share} operator should be used instead:\n   * `new ConnectableObservable(source, factory).refCount()` is equivalent to\n   * `source.pipe(share({ connector: factory }))`.\n   * Details: https://rxjs.dev/deprecations/multicasting\n   */\n  constructor(public source: Observable<T>, protected subjectFactory: () => Subject<T>) {\n    super();\n    // If we have lift, monkey patch that here. This is done so custom observable\n    // types will compose through multicast. Otherwise the resulting observable would\n    // simply be an instance of `ConnectableObservable`.\n    if (hasLift(source)) {\n      this.lift = source.lift;\n    }\n  }\n\n  /** @internal */\n  protected _subscribe(subscriber: Subscriber<T>) {\n    return this.getSubject().subscribe(subscriber);\n  }\n\n  protected getSubject(): Subject<T> {\n    const subject = this._subject;\n    if (!subject || subject.isStopped) {\n      this._subject = this.subjectFactory();\n    }\n    return this._subject!;\n  }\n\n  protected _teardown() {\n    this._refCount = 0;\n    const { _connection } = this;\n    this._subject = this._connection = null;\n    _connection?.unsubscribe();\n  }\n\n  /**\n   * @deprecated {@link ConnectableObservable} will be removed in v8. Use {@link connectable} instead.\n   * Details: https://rxjs.dev/deprecations/multicasting\n   */\n  connect(): Subscription {\n    let connection = this._connection;\n    if (!connection) {\n      connection = this._connection = new Subscription();\n      const subject = this.getSubject();\n      connection.add(\n        this.source.subscribe(\n          createOperatorSubscriber(\n            subject as any,\n            undefined,\n            () => {\n              this._teardown();\n              subject.complete();\n            },\n            (err) => {\n              this._teardown();\n              subject.error(err);\n            },\n            () => this._teardown()\n          )\n        )\n      );\n\n      if (connection.closed) {\n        this._connection = null;\n        connection = Subscription.EMPTY;\n      }\n    }\n    return connection;\n  }\n\n  /**\n   * @deprecated {@link ConnectableObservable} will be removed in v8. Use the {@link share} operator instead.\n   * Details: https://rxjs.dev/deprecations/multicasting\n   */\n  refCount(): Observable<T> {\n    return higherOrderRefCount()(this) as Observable<T>;\n  }\n}\n"], "mappings": ";AACA,SAASA,UAAU,QAAQ,eAAe;AAE1C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,QAAQ,IAAIC,mBAAmB,QAAQ,uBAAuB;AACvE,SAASC,wBAAwB,QAAQ,iCAAiC;AAC1E,SAASC,OAAO,QAAQ,cAAc;AAStC,IAAAC,qBAAA,aAAAC,MAAA;EAA8CC,SAAA,CAAAF,qBAAA,EAAAC,MAAA;EAgB5C,SAAAD,sBAAmBG,MAAqB,EAAYC,cAAgC;IAApF,IAAAC,KAAA,GACEJ,MAAA,CAAAK,IAAA,MAAO;IADUD,KAAA,CAAAF,MAAM,GAANA,MAAM;IAA2BE,KAAA,CAAAD,cAAc,GAAdA,cAAc;IAfxDC,KAAA,CAAAE,QAAQ,GAAsB,IAAI;IAClCF,KAAA,CAAAG,SAAS,GAAW,CAAC;IACrBH,KAAA,CAAAI,WAAW,GAAwB,IAAI;IAkB/C,IAAIV,OAAO,CAACI,MAAM,CAAC,EAAE;MACnBE,KAAI,CAACK,IAAI,GAAGP,MAAM,CAACO,IAAI;;;EAE3B;EAGUV,qBAAA,CAAAW,SAAA,CAAAC,UAAU,GAApB,UAAqBC,UAAyB;IAC5C,OAAO,IAAI,CAACC,UAAU,EAAE,CAACC,SAAS,CAACF,UAAU,CAAC;EAChD,CAAC;EAESb,qBAAA,CAAAW,SAAA,CAAAG,UAAU,GAApB;IACE,IAAME,OAAO,GAAG,IAAI,CAACT,QAAQ;IAC7B,IAAI,CAACS,OAAO,IAAIA,OAAO,CAACC,SAAS,EAAE;MACjC,IAAI,CAACV,QAAQ,GAAG,IAAI,CAACH,cAAc,EAAE;;IAEvC,OAAO,IAAI,CAACG,QAAS;EACvB,CAAC;EAESP,qBAAA,CAAAW,SAAA,CAAAO,SAAS,GAAnB;IACE,IAAI,CAACV,SAAS,GAAG,CAAC;IACV,IAAAC,WAAW,GAAK,IAAI,CAAAA,WAAT;IACnB,IAAI,CAACF,QAAQ,GAAG,IAAI,CAACE,WAAW,GAAG,IAAI;IACvCA,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEU,WAAW,EAAE;EAC5B,CAAC;EAMDnB,qBAAA,CAAAW,SAAA,CAAAS,OAAO,GAAP;IAAA,IAAAf,KAAA;IACE,IAAIgB,UAAU,GAAG,IAAI,CAACZ,WAAW;IACjC,IAAI,CAACY,UAAU,EAAE;MACfA,UAAU,GAAG,IAAI,CAACZ,WAAW,GAAG,IAAId,YAAY,EAAE;MAClD,IAAM2B,SAAO,GAAG,IAAI,CAACR,UAAU,EAAE;MACjCO,UAAU,CAACE,GAAG,CACZ,IAAI,CAACpB,MAAM,CAACY,SAAS,CACnBjB,wBAAwB,CACtBwB,SAAc,EACdE,SAAS,EACT;QACEnB,KAAI,CAACa,SAAS,EAAE;QAChBI,SAAO,CAACG,QAAQ,EAAE;MACpB,CAAC,EACD,UAACC,GAAG;QACFrB,KAAI,CAACa,SAAS,EAAE;QAChBI,SAAO,CAACK,KAAK,CAACD,GAAG,CAAC;MACpB,CAAC,EACD;QAAM,OAAArB,KAAI,CAACa,SAAS,EAAE;MAAhB,CAAgB,CACvB,CACF,CACF;MAED,IAAIG,UAAU,CAACO,MAAM,EAAE;QACrB,IAAI,CAACnB,WAAW,GAAG,IAAI;QACvBY,UAAU,GAAG1B,YAAY,CAACkC,KAAK;;;IAGnC,OAAOR,UAAU;EACnB,CAAC;EAMDrB,qBAAA,CAAAW,SAAA,CAAAf,QAAQ,GAAR;IACE,OAAOC,mBAAmB,EAAE,CAAC,IAAI,CAAkB;EACrD,CAAC;EACH,OAAAG,qBAAC;AAAD,CAAC,CAxF6CN,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}