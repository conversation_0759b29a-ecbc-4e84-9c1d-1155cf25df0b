{"ast": null, "code": "'use client';\n\nvar Column = function Column() {};\n//@todo Pass Parent MetaData\n\nColumn.displayName = 'Column';\nexport { Column };", "map": {"version": 3, "names": ["Column", "displayName"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/node_modules/primereact/column/column.esm.js"], "sourcesContent": ["'use client';\nvar Column = function Column() {};\n//@todo Pass Parent MetaData\n\nColumn.displayName = 'Column';\n\nexport { Column };\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG,CAAC,CAAC;AACjC;;AAEAA,MAAM,CAACC,WAAW,GAAG,QAAQ;AAE7B,SAASD,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}