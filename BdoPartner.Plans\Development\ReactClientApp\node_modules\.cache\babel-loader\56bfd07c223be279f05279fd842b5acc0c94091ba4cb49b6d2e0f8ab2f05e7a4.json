{"ast": null, "code": "import { combineLatestAll } from './combineLatestAll';\nexport var combineAll = combineLatestAll;", "map": {"version": 3, "names": ["combineLatestAll", "combineAll"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\combineAll.ts"], "sourcesContent": ["import { combineLatestAll } from './combineLatestAll';\n\n/**\n * @deprecated Renamed to {@link combineLatestAll}. Will be removed in v8.\n */\nexport const combineAll = combineLatestAll;\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,oBAAoB;AAKrD,OAAO,IAAMC,UAAU,GAAGD,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}