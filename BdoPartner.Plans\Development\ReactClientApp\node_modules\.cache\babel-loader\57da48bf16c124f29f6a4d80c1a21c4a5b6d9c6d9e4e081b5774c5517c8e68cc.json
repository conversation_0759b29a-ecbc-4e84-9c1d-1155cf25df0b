{"ast": null, "code": "import { dateTimestampProvider } from '../scheduler/dateTimestampProvider';\nimport { map } from './map';\nexport function timestamp(timestampProvider) {\n  if (timestampProvider === void 0) {\n    timestampProvider = dateTimestampProvider;\n  }\n  return map(function (value) {\n    return {\n      value: value,\n      timestamp: timestampProvider.now()\n    };\n  });\n}", "map": {"version": 3, "names": ["dateTimestampProvider", "map", "timestamp", "timestampProvider", "value", "now"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\timestamp.ts"], "sourcesContent": ["import { OperatorFunction, TimestampProvider, Timestamp } from '../types';\nimport { dateTimestampProvider } from '../scheduler/dateTimestampProvider';\nimport { map } from './map';\n\n/**\n * Attaches a timestamp to each item emitted by an observable indicating when it was emitted\n *\n * The `timestamp` operator maps the *source* observable stream to an object of type\n * `{value: T, timestamp: R}`. The properties are generically typed. The `value` property contains the value\n * and type of the *source* observable. The `timestamp` is generated by the schedulers `now` function. By\n * default, it uses the `asyncScheduler` which simply returns `Date.now()` (milliseconds since 1970/01/01\n * 00:00:00:000) and therefore is of type `number`.\n *\n * ![](timestamp.png)\n *\n * ## Example\n *\n * In this example there is a timestamp attached to the document's click events\n *\n * ```ts\n * import { fromEvent, timestamp } from 'rxjs';\n *\n * const clickWithTimestamp = fromEvent(document, 'click').pipe(\n *   timestamp()\n * );\n *\n * // Emits data of type { value: PointerEvent, timestamp: number }\n * clickWithTimestamp.subscribe(data => {\n *   console.log(data);\n * });\n * ```\n *\n * @param timestampProvider An object with a `now()` method used to get the current timestamp.\n * @return A function that returns an Observable that attaches a timestamp to\n * each item emitted by the source Observable indicating when it was emitted.\n */\nexport function timestamp<T>(timestampProvider: TimestampProvider = dateTimestampProvider): OperatorFunction<T, Timestamp<T>> {\n  return map((value: T) => ({ value, timestamp: timestampProvider.now() }));\n}\n"], "mappings": "AACA,SAASA,qBAAqB,QAAQ,oCAAoC;AAC1E,SAASC,GAAG,QAAQ,OAAO;AAkC3B,OAAM,SAAUC,SAASA,CAAIC,iBAA4D;EAA5D,IAAAA,iBAAA;IAAAA,iBAAA,GAAAH,qBAA4D;EAAA;EACvF,OAAOC,GAAG,CAAC,UAACG,KAAQ;IAAK,OAAC;MAAEA,KAAK,EAAAA,KAAA;MAAEF,SAAS,EAAEC,iBAAiB,CAACE,GAAG;IAAE,CAAE;EAA9C,CAA+C,CAAC;AAC3E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}