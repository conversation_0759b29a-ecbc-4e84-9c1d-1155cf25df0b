{"ast": null, "code": "var _jsxFileName = \"C:\\\\git\\\\Partner-Site\\\\BdoPartner.Plans\\\\Development\\\\ReactClientApp\\\\src\\\\index.js\";\nimport React from \"react\";\nimport { createRoot } from \"react-dom/client\";\nimport \"./index.css\";\nimport App from \"./App\";\nimport \"./core/config/i18nConfig\";\nimport { Suspense } from \"react\";\nimport { Provider } from \"react-redux\";\nimport { store } from \"./redux/store\";\n\n// import reportWebVitals from './reportWebVitals';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst container = document.getElementById(\"root\");\nconst root = createRoot(container);\nroot.render(\n/*#__PURE__*/\n//\n// Note: React.StrictMode only applies to Development mode. In production mode, it will not impact the production build.\n// Note: StrictMode affect axios to send two request (get,post) one time. It is about \"prelight request\"\n// TEMPORARILY DISABLED StrictMode to prevent duplicate API calls during development\n//\n// <React.StrictMode>\n_jsxDEV(Suspense, {\n  fallback: /*#__PURE__*/_jsxDEV(\"span\", {\n    children: \"Loading\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 25\n  }, this),\n  children: /*#__PURE__*/_jsxDEV(Provider, {\n    store: store,\n    children: /*#__PURE__*/_jsxDEV(App, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 27,\n    columnNumber: 7\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 22,\n  columnNumber: 5\n}, this)\n// </React.StrictMode>\n);\n\n// If you want to start measuring performance in your app, pass a function\n// to log results (for example: reportWebVitals(console.log))\n// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals\n//reportWebVitals();", "map": {"version": 3, "names": ["React", "createRoot", "App", "Suspense", "Provider", "store", "jsxDEV", "_jsxDEV", "container", "document", "getElementById", "root", "render", "fallback", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/index.js"], "sourcesContent": ["import React from \"react\";\r\nimport { createRoot } from \"react-dom/client\";\r\nimport \"./index.css\";\r\nimport App from \"./App\";\r\nimport \"./core/config/i18nConfig\";\r\nimport { Suspense } from \"react\";\r\nimport { Provider } from \"react-redux\";\r\nimport { store } from \"./redux/store\";\r\n\r\n// import reportWebVitals from './reportWebVitals';\r\n\r\nconst container = document.getElementById(\"root\");\r\nconst root = createRoot(container);\r\n\r\nroot.render(\r\n  //\r\n  // Note: React.StrictMode only applies to Development mode. In production mode, it will not impact the production build.\r\n  // Note: StrictMode affect axios to send two request (get,post) one time. It is about \"prelight request\"\r\n  // TEMPORARILY DISABLED StrictMode to prevent duplicate API calls during development\r\n  //\r\n  // <React.StrictMode>\r\n    <Suspense fallback={<span>Loading</span>}>\r\n      {/*\r\n       * Reference: https://stackoverflow.com/questions/36212860/subscribe-to-single-property-change-in-store-in-redux\r\n       * Means the child components are able to access the redux store.\r\n       */}\r\n      <Provider store={store}>\r\n        <App />\r\n      </Provider>\r\n    </Suspense>\r\n  // </React.StrictMode>\r\n);\r\n\r\n// If you want to start measuring performance in your app, pass a function\r\n// to log results (for example: reportWebVitals(console.log))\r\n// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals\r\n//reportWebVitals();\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,QAAQ,kBAAkB;AAC7C,OAAO,aAAa;AACpB,OAAOC,GAAG,MAAM,OAAO;AACvB,OAAO,0BAA0B;AACjC,SAASC,QAAQ,QAAQ,OAAO;AAChC,SAASC,QAAQ,QAAQ,aAAa;AACtC,SAASC,KAAK,QAAQ,eAAe;;AAErC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEA,MAAMC,SAAS,GAAGC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAAC;AACjD,MAAMC,IAAI,GAAGV,UAAU,CAACO,SAAS,CAAC;AAElCG,IAAI,CAACC,MAAM;AAAA;AACT;AACA;AACA;AACA;AACA;AACA;AACEL,OAAA,CAACJ,QAAQ;EAACU,QAAQ,eAAEN,OAAA;IAAAO,QAAA,EAAM;EAAO;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAE;EAAAJ,QAAA,eAKvCP,OAAA,CAACH,QAAQ;IAACC,KAAK,EAAEA,KAAM;IAAAS,QAAA,eACrBP,OAAA,CAACL,GAAG;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACH;AACZ;AACF,CAAC;;AAED;AACA;AACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}