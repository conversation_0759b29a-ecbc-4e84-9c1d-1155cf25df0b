{"ast": null, "code": "export function getSymbolIterator() {\n  if (typeof Symbol !== 'function' || !Symbol.iterator) {\n    return '@@iterator';\n  }\n  return Symbol.iterator;\n}\nexport var iterator = getSymbolIterator();", "map": {"version": 3, "names": ["getSymbolIterator", "Symbol", "iterator"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\symbol\\iterator.ts"], "sourcesContent": ["export function getSymbolIterator(): symbol {\n  if (typeof Symbol !== 'function' || !Symbol.iterator) {\n    return '@@iterator' as any;\n  }\n\n  return Symbol.iterator;\n}\n\nexport const iterator = getSymbolIterator();\n"], "mappings": "AAAA,OAAM,SAAUA,iBAAiBA,CAAA;EAC/B,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAI,CAACA,MAAM,CAACC,QAAQ,EAAE;IACpD,OAAO,YAAmB;;EAG5B,OAAOD,MAAM,CAACC,QAAQ;AACxB;AAEA,OAAO,IAAMA,QAAQ,GAAGF,iBAAiB,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}