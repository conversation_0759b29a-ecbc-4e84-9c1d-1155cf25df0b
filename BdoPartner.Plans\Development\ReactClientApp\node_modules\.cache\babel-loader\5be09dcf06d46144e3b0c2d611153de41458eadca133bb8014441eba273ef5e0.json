{"ast": null, "code": "var _jsxFileName = \"C:\\\\git\\\\Partner-Site\\\\BdoPartner.Plans\\\\Development\\\\ReactClientApp\\\\src\\\\components\\\\questionnaire\\\\PartnerPlanQuestionnaire.jsx\",\n  _s = $RefreshSig$();\nimport { useState, useEffect, useContext, useCallback, useRef } from \"react\";\nimport { Survey } from \"survey-react-ui\";\nimport { Model } from \"survey-core\";\nimport { configureSurveyJSLicense } from \"../../core/surveyjs/licenseConfig\";\nimport { useNavigate } from \"react-router-dom\";\nimport { AuthContext } from \"../../core/auth/components/authProvider\";\nimport { messageService } from \"../../core/message/messageService\";\nimport { useLoadingControl } from \"../../core/loading/hooks/useLoadingControl\";\nimport formService from \"../../services/formService\";\nimport partnerReferenceDataUploadService from \"../../services/partnerReferenceDataUploadService\";\nimport { FormStatus, getFormStatusName, getFormStatusClass, isFormEditableByOwner } from \"../../core/enumertions/formStatus\";\nimport { UserFormRole } from \"../../core/enumertions/userFormRole\";\nimport { PartnerPlanCycle } from \"../../core/enumertions/partnerPlanCycle\";\nimport http from \"../../core/http/httpClient\";\nimport APP_CONFIG from \"../../core/config/appConfig\";\nimport FormAnswerUtility from \"./formAnswerUtilities\";\nimport PDFExportUtilities from \"./pdfExportUtilities\";\nimport ReviewerCommentsDialog from \"../common/ReviewerCommentsDialog\";\nimport AdminModificationAuditHistory from \"../audit/AdminModificationAuditHistory\";\nimport { Panel } from \"primereact/panel\";\nimport { formatDateTime } from \"../../core/utils/dateUtils\";\nimport { registerCustomPropertiesForRuntime } from \"../../core/utils/surveyCustomPropertiesUtils\";\n\n// Import the latest Survey.js CSS themes\nimport \"survey-core/survey-core.css\";\nimport \"../../survey-v2-styles.css\"; // Import our custom Survey.js v2 styles\nimport \"./PartnerPlanQuestionnaire.css\"; // Import component-specific styles\n\n/**\r\n * Recursively traverses the survey JSON to build a map of element names to their tags.\r\n * @param {Array} elements - The array of elements (questions or panels) to process.\r\n * @param {Object} map - The map to populate.\r\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst buildNameTagMap = (elements, map) => {\n  if (!elements || !Array.isArray(elements)) {\n    return;\n  }\n  elements.forEach(element => {\n    if (element.name && element.tag) {\n      map[element.name] = element.tag;\n    }\n\n    // Recurse into nested elements (e.g., in panels)\n    if (element.elements) {\n      buildNameTagMap(element.elements, map);\n    }\n  });\n};\n\n/**\r\n * Recursively traverses the survey JSON to build a map of element names to their leadership roles.\r\n * @param {Array} elements - The array of elements (questions or panels) to process.\r\n * @param {Object} map - The map to populate.\r\n */\nconst buildNameLeadershipRolesMap = (elements, map) => {\n  if (!elements || !Array.isArray(elements)) {\n    return;\n  }\n  elements.forEach(element => {\n    if (element.name && element.leadershipRoles) {\n      map[element.name] = element.leadershipRoles;\n    }\n\n    // Recurse into nested elements (e.g., in panels)\n    if (element.elements) {\n      buildNameLeadershipRolesMap(element.elements, map);\n    }\n  });\n};\n\n/**\r\n * Recursively traverses the survey JSON to build a map of element names to their service lines.\r\n * @param {Array} elements - The array of elements (questions or panels) to process.\r\n * @param {Object} map - The map to populate.\r\n */\nconst buildNameServiceLinesMap = (elements, map) => {\n  if (!elements || !Array.isArray(elements)) {\n    return;\n  }\n  elements.forEach(element => {\n    if (element.name && element.serviceLines) {\n      map[element.name] = element.serviceLines;\n    }\n\n    // Recurse into nested elements (e.g., in panels)\n    if (element.elements) {\n      buildNameServiceLinesMap(element.elements, map);\n    }\n  });\n};\n\n/**\r\n * Recursively traverses the survey JSON to build a map of element names to their sub-service lines.\r\n * @param {Array} elements - The array of elements (questions or panels) to process.\r\n * @param {Object} map - The map to populate.\r\n */\nconst buildNameSubServiceLinesMap = (elements, map) => {\n  if (!elements || !Array.isArray(elements)) {\n    return;\n  }\n  elements.forEach(element => {\n    if (element.name && element.subServiceLines) {\n      map[element.name] = element.subServiceLines;\n    }\n\n    // Recurse into nested elements (e.g., in panels)\n    if (element.elements) {\n      buildNameSubServiceLinesMap(element.elements, map);\n    }\n  });\n};\n\n/**\r\n * Normalize leadership role for consistent comparison\r\n * @param {string} role - Original leadership role\r\n * @returns {string} Normalized leadership role\r\n */\nconst normalizeLeadershipRole = role => {\n  if (!role || typeof role !== \"string\") {\n    return \"\";\n  }\n  // Apply normalization: trim whitespace, convert to uppercase for consistent comparison\n  return role.trim().toUpperCase();\n};\n/**\r\n * Applies tag configuration from planData to survey elements for visibility and readonly rules\r\n * @param {Object} survey - The SurveyJS model instance\r\n * @param {Object} config - The form access configuration object from planData\r\n * @param {Object} nameTagMap - Map of element names to their tag values\r\n * @returns {Object|null} The form access configuration object or null if not found\r\n */\nconst applyTagConfiguration = (survey, config, nameTagMap) => {\n  try {\n    // Validate inputs\n    if (!survey) {\n      console.warn(\"🏷️ Survey model is null, skipping tag configuration\");\n      return null;\n    }\n    if (!config) {\n      console.log(\"🏷️ No Form Access configuration found for this form. Using default behavior.\");\n      return null;\n    }\n    console.log(\"📋 Form Access configuration received:\", config);\n\n    // Validate config structure\n    if (typeof config !== \"object\") {\n      console.warn(\"🏷️ Invalid tag configuration format received. Using default behavior.\");\n      return null;\n    }\n    let appliedRulesCount = 0;\n    const applyRulesToElements = (elements, elementType) => {\n      console.log(`🏷️ Processing ${elements.length} ${elementType} for tag rules`);\n      elements.forEach(element => {\n        const tagName = nameTagMap[element.name] || element.tag;\n        console.log(`🔍 ${elementType} ${element.name}: tag=\"${tagName}\"`);\n        if (tagName) {\n          const configKey = Object.keys(config).find(k => k.toLowerCase() === tagName.toLowerCase());\n          if (configKey && config[configKey]) {\n            const tagConfig = config[configKey];\n            if (typeof tagConfig.readOnly === \"boolean\") {\n              element.readOnly = tagConfig.readOnly;\n              console.log(`🔒 Applied readOnly=${tagConfig.readOnly} to ${elementType} ${element.name} with tag ${tagName}`);\n              appliedRulesCount++;\n            }\n            if (typeof tagConfig.visible === \"boolean\") {\n              element.visible = tagConfig.visible;\n              console.log(`👁️ Applied visible=${tagConfig.visible} to ${elementType} ${element.name} with tag ${tagName}`);\n              appliedRulesCount++;\n            }\n          }\n        }\n      });\n    };\n    applyRulesToElements(survey.getAllQuestions(), \"question\");\n    applyRulesToElements(survey.getAllPanels(), \"panel\");\n    console.log(`✅ Tag configuration applied successfully. ${appliedRulesCount} rules applied.`);\n\n    // Return the configuration so it can be used by the caller\n    return config;\n  } catch (error) {\n    console.error(\"❌ Error fetching or applying tag configuration:\", error);\n    console.log(\"📝 Form will load with default behavior (all visible, all editable)\");\n    // Don't throw the error - let the form load with default behavior\n    return null;\n  }\n};\n\n/**\r\n * Applies leadership roles configuration to survey panels for visibility rules\r\n * @param {Object} survey - The SurveyJS model instance\r\n * @param {Array} userLeadershipRoles - Current user's leadership roles\r\n * @param {Object} nameLeadershipRolesMap - Map of element names to their leadership roles\r\n * @returns {number} Number of panels processed\r\n */\nconst applyLeadershipRolesConfiguration = (survey, userLeadershipRoles, nameLeadershipRolesMap) => {\n  try {\n    // Validate inputs\n    if (!survey) {\n      console.warn(\"👤 Survey model is null, skipping leadership roles configuration\");\n      return 0;\n    }\n    if (!userLeadershipRoles) {\n      console.log(\"👤 No user leadership roles provided, treating as empty array\");\n      userLeadershipRoles = [];\n    }\n    console.log(\"👤 Applying leadership roles configuration...\");\n    console.log(\"👤 User leadership roles:\", userLeadershipRoles);\n    console.log(\"👤 Name-to-leadership-roles map:\", nameLeadershipRolesMap);\n    let processedPanelsCount = 0;\n\n    // Only apply to panels (not questions)\n    const panels = survey.getAllPanels();\n    console.log(`👤 Processing ${panels.length} panels for leadership roles rules`);\n    panels.forEach(panel => {\n      // Skip leadership roles check if panel is already hidden by tag configuration\n      if (!panel.visible) {\n        console.log(`⏭️ Skipping panel ${panel.name} - already hidden by tag configuration`);\n        processedPanelsCount++;\n        return;\n      }\n\n      // Get leadership roles from the map or directly from the panel\n      const panelLeadershipRoles = nameLeadershipRolesMap[panel.name] || panel.leadershipRoles;\n      console.log(`🔍 Panel ${panel.name}: leadershipRoles=\"${panelLeadershipRoles}\"`);\n      if (panelLeadershipRoles) {\n        // Parse leadership roles (could be array or comma-separated string)\n        let requiredRoles = [];\n        if (Array.isArray(panelLeadershipRoles)) {\n          requiredRoles = panelLeadershipRoles;\n        } else if (typeof panelLeadershipRoles === \"string\") {\n          requiredRoles = panelLeadershipRoles.split(\",\").map(role => role.trim()).filter(role => role);\n        }\n        console.log(`👤 Panel ${panel.name} requires roles:`, requiredRoles);\n        if (requiredRoles.length > 0) {\n          // Normalize required roles for consistent comparison\n          const normalizedRequiredRoles = requiredRoles.map(role => normalizeLeadershipRole(role));\n\n          // Check if user has any of the required leadership roles (both are normalized)\n          const hasRequiredRole = normalizedRequiredRoles.some(requiredRole => userLeadershipRoles.some(userRole => userRole === requiredRole));\n          if (!hasRequiredRole) {\n            // User doesn't have required leadership role, hide the panel\n            panel.visible = false;\n            console.log(`👁️ Hidden panel ${panel.name} - user lacks required leadership roles`);\n            console.log(`   Required (normalized): [${normalizedRequiredRoles.join(\", \")}]`);\n            console.log(`   User has (normalized): [${userLeadershipRoles.join(\", \")}]`);\n          } else {\n            console.log(`✅ Panel ${panel.name} visible - user has required leadership role`);\n          }\n        } else {\n          console.log(`📝 Panel ${panel.name} has empty leadership roles - visible to all`);\n        }\n      } else {\n        console.log(`📝 Panel ${panel.name} has no leadership roles restriction - visible to all`);\n      }\n      processedPanelsCount++;\n    });\n    console.log(`✅ Leadership roles configuration applied successfully. ${processedPanelsCount} panels processed.`);\n    return processedPanelsCount;\n  } catch (error) {\n    console.error(\"❌ Error applying leadership roles configuration:\", error);\n    console.log(\"📝 Form will load with default behavior (all panels visible)\");\n    // Don't throw the error - let the form load with default behavior\n    return 0;\n  }\n};\n\n/**\r\n * Applies service lines configuration to survey panels for visibility rules\r\n * @param {Object} survey - The SurveyJS model instance\r\n * @param {Array} formOwnerServiceLines - Form owner's service lines\r\n * @param {Array} formOwnerSubServiceLines - Form owner's sub-service lines\r\n * @param {Object} nameServiceLinesMap - Map of element names to their service lines\r\n * @param {Object} nameSubServiceLinesMap - Map of element names to their sub-service lines\r\n * @returns {number} Number of panels processed\r\n */\nconst applyServiceLinesConfiguration = (survey, formOwnerServiceLines, formOwnerSubServiceLines, nameServiceLinesMap, nameSubServiceLinesMap) => {\n  try {\n    // Validate inputs\n    if (!survey) {\n      console.warn(\"🏢 Survey model is null, skipping service lines configuration\");\n      return 0;\n    }\n    if (!formOwnerServiceLines) {\n      console.log(\"🏢 No form owner service lines provided, treating as empty array\");\n      formOwnerServiceLines = [];\n    }\n    if (!formOwnerSubServiceLines) {\n      console.log(\"🏢 No form owner sub-service lines provided, treating as empty array\");\n      formOwnerSubServiceLines = [];\n    }\n    console.log(\"🏢 Applying service lines configuration...\");\n    console.log(\"🏢 Form owner service lines:\", formOwnerServiceLines);\n    console.log(\"🏢 Form owner sub-service lines:\", formOwnerSubServiceLines);\n    console.log(\"🏢 Name-to-service-lines map:\", nameServiceLinesMap);\n    console.log(\"🏢 Name-to-sub-service-lines map:\", nameSubServiceLinesMap);\n    let processedPanelsCount = 0;\n\n    // Only apply to panels (not questions)\n    const panels = survey.getAllPanels();\n    console.log(`🏢 Found ${panels.length} panels to process`);\n    panels.forEach(panel => {\n      // Skip if panel is already hidden (e.g., by tag configuration)\n      if (!panel.visible) {\n        console.log(`🏢 Panel ${panel.name} already hidden, skipping service lines check`);\n        processedPanelsCount++;\n        return;\n      }\n\n      // Get service lines and sub-service lines for this panel\n      const panelServiceLines = nameServiceLinesMap[panel.name] || panel.serviceLines;\n      const panelSubServiceLines = nameSubServiceLinesMap[panel.name] || panel.subServiceLines;\n      console.log(`🏢 Panel ${panel.name}:`);\n      console.log(`   Service lines: ${panelServiceLines}`);\n      console.log(`   Sub-service lines: ${panelSubServiceLines}`);\n\n      // Check service lines first\n      let hiddenByServiceLines = false;\n      if (panelServiceLines) {\n        // Handle both array and string formats for panelServiceLines\n        let requiredServiceLines = [];\n        if (Array.isArray(panelServiceLines)) {\n          requiredServiceLines = panelServiceLines.filter(sl => sl && sl.trim() !== \"\");\n        } else if (typeof panelServiceLines === \"string\" && panelServiceLines.trim() !== \"\") {\n          requiredServiceLines = panelServiceLines.split(\",\").map(sl => sl.trim()).filter(sl => sl !== \"\");\n        }\n        if (requiredServiceLines.length > 0) {\n          // Check if form owner has any of the required service lines\n          const hasRequiredServiceLine = requiredServiceLines.some(requiredSL => formOwnerServiceLines.some(ownerSL => ownerSL.trim() === requiredSL));\n          if (!hasRequiredServiceLine) {\n            // Form owner doesn't have required service line, hide the panel\n            panel.visible = false;\n            hiddenByServiceLines = true;\n            console.log(`👁️ Hidden panel ${panel.name} - form owner lacks required service lines`);\n            console.log(`   Required: [${requiredServiceLines.join(\", \")}]`);\n            console.log(`   Form owner has: [${formOwnerServiceLines.join(\", \")}]`);\n          } else {\n            console.log(`✅ Panel ${panel.name} visible - form owner has required service line`);\n          }\n        }\n      }\n\n      // Check sub-service lines only if not already hidden by service lines\n      if (!hiddenByServiceLines && panelSubServiceLines) {\n        // Handle both array and string formats for panelSubServiceLines\n        let requiredSubServiceLines = [];\n        if (Array.isArray(panelSubServiceLines)) {\n          requiredSubServiceLines = panelSubServiceLines.filter(ssl => ssl && ssl.trim() !== \"\");\n        } else if (typeof panelSubServiceLines === \"string\" && panelSubServiceLines.trim() !== \"\") {\n          requiredSubServiceLines = panelSubServiceLines.split(\",\").map(ssl => ssl.trim()).filter(ssl => ssl !== \"\");\n        }\n        if (requiredSubServiceLines.length > 0) {\n          // Check if form owner has any of the required sub-service lines\n          const hasRequiredSubServiceLine = requiredSubServiceLines.some(requiredSSL => formOwnerSubServiceLines.some(ownerSSL => ownerSSL.trim() === requiredSSL));\n          if (!hasRequiredSubServiceLine) {\n            // Form owner doesn't have required sub-service line, hide the panel\n            panel.visible = false;\n            console.log(`👁️ Hidden panel ${panel.name} - form owner lacks required sub-service lines`);\n            console.log(`   Required: [${requiredSubServiceLines.join(\", \")}]`);\n            console.log(`   Form owner has: [${formOwnerSubServiceLines.join(\", \")}]`);\n          } else {\n            console.log(`✅ Panel ${panel.name} visible - form owner has required sub-service line`);\n          }\n        }\n      }\n\n      // If both service lines and sub-service lines are empty, panel is visible to all\n      const hasServiceLinesRestriction = panelServiceLines && (Array.isArray(panelServiceLines) && panelServiceLines.length > 0 || typeof panelServiceLines === \"string\" && panelServiceLines.trim() !== \"\");\n      const hasSubServiceLinesRestriction = panelSubServiceLines && (Array.isArray(panelSubServiceLines) && panelSubServiceLines.length > 0 || typeof panelSubServiceLines === \"string\" && panelSubServiceLines.trim() !== \"\");\n      if (!hasServiceLinesRestriction && !hasSubServiceLinesRestriction) {\n        console.log(`📝 Panel ${panel.name} has no service lines restrictions - visible to all`);\n      }\n      processedPanelsCount++;\n    });\n    console.log(`✅ Service lines configuration applied successfully. ${processedPanelsCount} panels processed.`);\n    return processedPanelsCount;\n  } catch (error) {\n    console.error(\"❌ Error applying service lines configuration:\", error);\n    console.log(\"📝 Form will load with default behavior (all panels visible)\");\n    // Don't throw the error - let the form load with default behavior\n    return 0;\n  }\n};\n\n/**\r\n *\r\n * @param {*} year Work for partner click \"View My Plan\" from landing page.\r\n * @param {*} formId Work for Admin/ELT user click \"View\" icon button in \"Partner Annual Plans\" grid.\r\n * @param {*} backHandler handle Go back to previous page\r\n * @returns\r\n */\nexport const PartnerPlanQuestionnaire = ({\n  year,\n  formId,\n  backHandler\n}) => {\n  _s();\n  const [surveyModel, setSurveyModel] = useState(null);\n  const [questionnaire, setQuestionnaire] = useState(null);\n  const [form, setForm] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [hasLoaded, setHasLoaded] = useState(false); // Track if data has been loaded\n  const [currentPageInfo, setCurrentPageInfo] = useState({\n    pageNo: 0,\n    pageCount: 0,\n    pageTitle: \"\"\n  });\n  // Keep a live ref of form to avoid stale closures in SurveyJS handlers\n  const formRef = useRef(null);\n  useEffect(() => {\n    formRef.current = form;\n  }, [form]);\n\n  // State for reviewer comments dialog\n  const [showCommentsDialog, setShowCommentsDialog] = useState(false);\n  const [sendingBack, setSendingBack] = useState(false);\n\n  // State for current user's form role (from form access configuration)\n  const [currentUserFormRole, setCurrentUserFormRole] = useState(null);\n  const [formAccessConfig, setFormAccessConfig] = useState(null);\n\n  // State for reviewer comments history\n  const [reviewerComments, setReviewerComments] = useState([]);\n\n  // State for submission allowed flag\n  const [isSubmissionAllowed, setIsSubmissionAllowed] = useState(true);\n\n  // State for admin editing flag\n  const [isAdminEditing, setIsAdminEditing] = useState(false);\n  const authService = useContext(AuthContext);\n  const navigate = useNavigate();\n\n  // Disable loading interceptor for survey component\n  useLoadingControl(\"survey\", true);\n\n  // Determine scenario: MyPartnerPlan (year provided) vs PartnerPlan (formId provided)\n  const isMyPartnerPlan = !!year && !formId;\n  const isPartnerPlan = !!formId && !year;\n\n  // Reset hasLoaded when year or formId changes to trigger data reload\n  useEffect(() => {\n    setHasLoaded(false);\n  }, [year, formId]);\n\n  // Use useEffect directly instead of useCallback to avoid dependency issues\n  useEffect(() => {\n    // Prevent multiple calls if already loaded or not authenticated\n    if (hasLoaded || !authService || !authService.isAuthenticated()) {\n      return;\n    }\n    const loadData = async () => {\n      try {\n        setLoading(true);\n        setError(null);\n        let planData = null;\n\n        // Determine which API endpoint to call based on scenario\n        if (formId) {\n          // Scenario 2: Admin/ELT/Reviewer accessing other partner's plan\n          console.log(\"🔄 Loading partner plan data for formId:\", formId);\n          planData = await formService.getPartnerPlan(formId);\n        } else {\n          // Scenario 1: MyPartnerPlan - form owner accessing their own plan\n          const targetYear = year ? parseInt(year) : new Date().getFullYear();\n          console.log(\"🔄 Loading my plan data for year:\", targetYear);\n          planData = await formService.getMyPlan(targetYear);\n        }\n        if (planData) {\n          var _planData$userAnswer;\n          // Set all the data from the consolidated response\n          setQuestionnaire(planData.questionnaire);\n\n          // Set current user's form role from planData\n          if (planData.currentUserFormRole !== undefined) {\n            setCurrentUserFormRole(planData.currentUserFormRole);\n            console.log(\"👤 Current user form role:\", planData.currentUserFormRole);\n          }\n\n          // Set submission allowed flag from planData\n          if (planData.isSubmissionAllowed !== undefined) {\n            setIsSubmissionAllowed(planData.isSubmissionAllowed);\n            console.log(\"🔒 Submission allowed:\", planData.isSubmissionAllowed);\n          }\n\n          // Set admin editing flag from planData\n          if (planData.isAdminEditing !== undefined) {\n            setIsAdminEditing(planData.isAdminEditing);\n            console.log(\"👨‍💼 Admin editing:\", planData.isAdminEditing);\n          }\n\n          // Ensure form includes userAnswer to prevent null reference errors\n          const formWithUserAnswer = {\n            ...planData.form,\n            userAnswer: planData.userAnswer\n          };\n          setForm(formWithUserAnswer);\n\n          // Fetch reviewer comments history for this form (only for MyPartnerPlan scenario)\n          if (formWithUserAnswer.id && !formId) {\n            try {\n              const comments = await formService.getReviewerCommentsHistory(formWithUserAnswer.id);\n              setReviewerComments(comments || []);\n            } catch (error) {\n              console.error(`Error fetching reviewer comments for form ${formWithUserAnswer.id}:`, error);\n              setReviewerComments([]);\n            }\n          }\n\n          // Get existing answers if available\n          const existingAnswers = (_planData$userAnswer = planData.userAnswer) === null || _planData$userAnswer === void 0 ? void 0 : _planData$userAnswer.answer;\n\n          // Parse the DefinitionJson to create the survey model\n          if (planData.questionnaire.definitionJson) {\n            try {\n              const surveyJson = JSON.parse(planData.questionnaire.definitionJson);\n\n              // Validate that the parsed JSON is a valid object\n              if (!surveyJson || typeof surveyJson !== \"object\") {\n                throw new Error(\"Invalid survey JSON: not a valid object\");\n              }\n\n              // Ensure the survey JSON has required properties\n              if (!surveyJson.pages && !surveyJson.elements) {\n                throw new Error(\"Invalid survey JSON: missing pages or elements\");\n              }\n\n              // Debug: Check if tag properties exist in the original JSON\n              console.log(\"🔍 Checking original survey JSON for tag properties...\");\n              if (surveyJson.pages) {\n                surveyJson.pages.forEach((page, pageIndex) => {\n                  if (page.elements) {\n                    page.elements.forEach((element, elementIndex) => {\n                      if (element.tag) {\n                        console.log(`🏷️ Found tag in JSON - ${element.type} \"${element.name}\": tag=\"${element.tag}\"`);\n                      }\n                      // Check nested elements (panels can contain questions)\n                      if (element.elements) {\n                        element.elements.forEach((nestedElement, nestedIndex) => {\n                          if (nestedElement.tag) {\n                            console.log(`🏷️ Found tag in nested JSON - ${nestedElement.type} \"${nestedElement.name}\": tag=\"${nestedElement.tag}\"`);\n                          }\n                        });\n                      }\n                    });\n                  }\n                });\n              }\n\n              // Configure Survey.js commercial license before creating survey model\n              configureSurveyJSLicense();\n\n              // Register custom properties BEFORE creating the Survey model to avoid race conditions\n              await registerCustomPropertiesForRuntime({\n                isDesigner: false\n              });\n              const survey = new Model(surveyJson);\n              console.log(\"Survey model created:\", surveyJson);\n\n              // Validate that the survey was created successfully\n              if (!survey) {\n                throw new Error(\"Failed to create survey model\");\n              }\n\n              // Debug: Check if custom properties are available\n              const questions = survey.getAllQuestions();\n              const panels = survey.getAllPanels();\n              if (questions.length > 0) {\n                const firstQuestion = questions[0];\n              }\n              if (panels.length > 0) {\n                const firstPanel = panels[0];\n              }\n\n              // Validate that the survey has pages\n              if (!survey.pages || survey.pages.length === 0) {\n                throw new Error(\"Survey has no pages\");\n              }\n\n              // Load existing answers if available\n              if (existingAnswers) {\n                try {\n                  let parsedAnswers = JSON.parse(existingAnswers);\n                  // Handle legacy double-encoded payloads (JSON string inside JSON string)\n                  if (typeof parsedAnswers === \"string\") {\n                    try {\n                      parsedAnswers = JSON.parse(parsedAnswers);\n                    } catch {}\n                  }\n                  if (parsedAnswers && typeof parsedAnswers === \"object\") {\n                    survey.data = parsedAnswers;\n                  }\n                } catch (parseError) {\n                  console.warn(\"Failed to parse existing answers:\", parseError);\n                }\n              }\n\n              // Configure survey settings for wizard mode\n              survey.clearInvisibleValues = false;\n              // survey.allowShowPreview = true;\n              survey.showPreviewBeforeComplete = \"showAllQuestions\";\n              survey.showProgressBar = true;\n              survey.progressBarLocation = \"top\";\n              // survey.showQuestionNumbers = \"onPage\";\n              survey.questionsOnPageMode = \"standard\"; // Enable multi-page wizard mode\n              survey.showPageNumbers = false;\n              survey.showPageTitles = true;\n              survey.pagePrevText = \"Previous\";\n              survey.pageNextText = \"Next\";\n              survey.goNextPageAutomatic = false; // Don't auto-advance pages\n\n              //\n              // Check if form is editable. Updated to check FormAccessConfig matrix,\n              // if any of three panels are visible and not readonly, the form is editable for current user.\n              //\n              const formEditable = planData.isEditable;\n              if (!formEditable) {\n                // Set survey to read-only mode for non-editable forms (based on workflow rules and user roles)\n                survey.mode = \"display\";\n                survey.showPreviewBeforeComplete = \"noPreview\";\n              } else {\n                // Customize button text for editable forms\n                survey.completeText = \"Submit Partner Plan\";\n                survey.previewText = \"Preview\";\n                survey.editText = \"Previous\"; // Change Edit button text to Previous for clarity\n                // Ensure navigation buttons are shown in preview mode\n                survey.showPrevButton = true;\n                // Ensure preview mode shows navigation buttons\n                survey.showPreviewBeforeComplete = \"showAllQuestions\";\n                // survey.allowShowPreview = true;\n\n                // Hide complete button if submission is not allowed\n                if (!planData.isSubmissionAllowed) {\n                  survey.showCompleteButton = false;\n                  console.log(\"🚫 Complete button hidden - submission not allowed\");\n                }\n              }\n              // Add custom CSS classes\n              survey.css = {\n                ...survey.css,\n                root: `sv-root partner-plan-survey ${!formEditable ? \"survey-readonly\" : \"\"}`,\n                header: \"sv-header\",\n                body: \"sv-body\",\n                footer: \"sv-footer\"\n              };\n\n              // Apply modern theme for better styling\n              // Apply BDO red theme using modern Survey.js v2.2.2 approach\n              survey.applyTheme({\n                themeName: \"default-light\",\n                colorPalette: \"light\",\n                isPanelless: false,\n                // Custom CSS variables for BDO branding (equivalent to old StylesManager)\n                cssVariables: {\n                  \"--sjs-primary-backcolor\": \"#ED1A3B\",\n                  // Main color\n                  \"--sjs-primary-forecolor\": \"#FFFFFF\",\n                  // Text on primary\n                  \"--sjs-primary-backcolor-light\": \"#F5E6EA\",\n                  // Light variant\n                  \"--sjs-primary-backcolor-dark\": \"#AF273C\",\n                  // Hover color\n                  \"--sjs-secondary-backcolor\": \"#ED1A3B\",\n                  // Secondary color\n                  \"--sjs-secondary-forecolor\": \"#FFFFFF\",\n                  // Text on secondary\n                  \"--sjs-general-backcolor-dim\": \"#F3F2F1\",\n                  // Background\n                  \"--sjs-border-default\": \"#959597\",\n                  // Border color\n                  \"--sjs-border-light\": \"#D4D4D4\",\n                  // Light border\n                  \"--ctr-font-family\": \"primeicons\",\n                  \"--lbr-font-family\": \"primeicons\",\n                  \"--sjs-font-family\": \"primeicons\",\n                  \"--font-family\": \"primeicons\",\n                  \"--sjs-font-pagetitle-family\": \"primeicons\",\n                  \"--sjs-default-font-family\": \"primeicons\",\n                  \"--sjs-article-font-default\": \"24\",\n                  \"--sd-base-padding\": \"20px\",\n                  \"--sd-base-vertical-padding\": \"calc(1.4 * var(--sjs-base-unit, var(--base-unit, 8px)))\",\n                  \"--sd-page-vertical-padding\": \"calc(1.2 * var(--sjs-base-unit, var(--base-unit, 8px)))\"\n                }\n              });\n              survey.onTextMarkdown.add(function (survey, options) {\n                //convert the markdown text to html\n                var str = FormAnswerUtility.convertToHtml(options.text);\n                //set html\n                options.html = str;\n              });\n              // Only add completion handler for editable forms\n              if (formEditable) {\n                // Create a completion handler for form submission\n                const completionHandler = async (sender, options) => {\n                  // Prevent default completion\n                  options.allowComplete = false;\n\n                  // Show confirmation dialog\n                  messageService.confirmDialog(\"Are you sure you want to submit your partner plan? Once submitted, you may not be able to make changes.\", async confirmed => {\n                    if (!confirmed) {\n                      return;\n                    }\n                    try {\n                      // Save the final answers\n                      await formService.saveUserAnswer(planData.form.id, JSON.stringify(sender.data));\n\n                      // Submit the form using the new workflow-aware API\n                      const updatedForm = await formService.submitForm(planData.form.id);\n                      console.log(\"Survey completed and submitted successfully\");\n\n                      // Show success message with workflow information\n                      if (updatedForm && updatedForm.message) {\n                        messageService.successToast(updatedForm.message);\n                      } else {\n                        messageService.successToast(\"Partner plan submitted successfully!\");\n                      }\n\n                      // Update form status with the actual returned status from the workflow\n                      if (updatedForm) {\n                        setForm(prev => ({\n                          ...prev,\n                          status: updatedForm.status,\n                          statusString: getFormStatusName(updatedForm.status)\n                        }));\n                      }\n\n                      // Complete the survey\n                      sender.doComplete();\n                    } catch (error) {\n                      console.error(\"Error handling survey completion:\", error);\n                      messageService.errorToast(error.message || \"Failed to submit partner plan. Please try again.\");\n                    }\n                  });\n                };\n\n                // Set up survey completion handler\n                survey.onComplete.add(completionHandler);\n              }\n\n              // Only add auto-save for editable forms\n              if (formEditable) {\n                // Set up immediate save on value change (following Retirement project pattern)\n                survey.onValueChanged.add(surveyValueChanged);\n              }\n\n              // Set up page change handler for wizard navigation\n              survey.onCurrentPageChanged.add((sender, options) => {\n                handlePageChanged(sender, options);\n\n                // Check if we're on the last page and submission is not allowed\n                if (!planData.isSubmissionAllowed && planData.currentUserFormRole === UserFormRole.FormOwner) {\n                  const isLastPage = sender.isLastPage;\n                  if (isLastPage) {\n                    // Add a message to the last page about reviewer not being assigned\n                    const lastPage = sender.currentPage;\n                    if (lastPage && !lastPage.hasCustomMessage) {\n                      // Create a custom HTML element to show the message\n                      const submissionMessage = planData.submissionNotAllowedMessage;\n\n                      // Only render the message if submissionMessage is not null or empty\n                      if (submissionMessage && submissionMessage.trim() !== \"\") {\n                        const messageHtml = `\n                          <div style=\"background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px; padding: 15px; margin: 20px 0; color: #856404;\">\n                            <div style=\"display: flex; flex-direction: column; align-items: center; text-align: center;\">\n                              <div style=\"display: flex; align-items: center; margin-bottom: 10px;\">\n                                <i class=\"pi pi-exclamation-triangle\" style=\"font-size: 1.2rem; margin-right: 10px; color: #f39c12;\"></i>\n                                <strong>Unable to Submit Partner Plan</strong>\n                              </div>\n                              <p style=\"margin: 0;\">${submissionMessage}</p>\n                            </div>\n                          </div>\n                        `;\n\n                        // Add the message as HTML content to the page\n                        if (!lastPage.description) {\n                          lastPage.description = messageHtml;\n                        } else {\n                          lastPage.description = lastPage.description + messageHtml;\n                        }\n                        lastPage.hasCustomMessage = true;\n                      }\n                    }\n                  }\n                }\n              });\n\n              // Add page validation handler\n              survey.onCurrentPageChanging.add((_, options) => {\n                var _options$oldCurrentPa, _options$newCurrentPa;\n                // You can add custom validation logic here if needed\n                console.log(`Navigating from page ${(_options$oldCurrentPa = options.oldCurrentPage) === null || _options$oldCurrentPa === void 0 ? void 0 : _options$oldCurrentPa.name} to ${(_options$newCurrentPa = options.newCurrentPage) === null || _options$newCurrentPa === void 0 ? void 0 : _options$newCurrentPa.name}`);\n              });\n\n              // Add preview mode handler to ensure navigation buttons are shown\n              // Note: onShowPreview might not be available in all SurveyJS versions\n              if (survey.onShowPreview) {\n                survey.onShowPreview.add(sender => {\n                  console.log(\"Survey entered preview mode\");\n                  // Ensure navigation buttons are visible in preview mode\n                  sender.showNavigationButtons = \"both\";\n                  sender.showPrevButton = true;\n                });\n              }\n\n              // Set completion message\n              survey.completedHtml = `<h3>Thank you for completing ${survey.title || planData.questionnaire.name}!</h3>`;\n\n              // Build the name-to-tag map from the raw survey JSON\n              const nameTagMap = {};\n              if (surveyJson.pages) {\n                surveyJson.pages.forEach(page => {\n                  buildNameTagMap(page.elements, nameTagMap);\n                });\n              }\n              console.log(\"🏷️ Name-to-tag map created:\", nameTagMap);\n\n              // Apply tag-based visibility and readonly rules using configuration from planData\n              const formAccessConfig = applyTagConfiguration(survey, planData.formTagConfiguration, nameTagMap);\n\n              // Set form access config for other uses\n              if (formAccessConfig) {\n                setFormAccessConfig(formAccessConfig);\n              }\n\n              // Build the name-to-leadership-roles map from the raw survey JSON\n              const nameLeadershipRolesMap = {};\n              if (surveyJson.pages) {\n                surveyJson.pages.forEach(page => {\n                  buildNameLeadershipRolesMap(page.elements, nameLeadershipRolesMap);\n                });\n              }\n              console.log(\"👤 Name-to-leadership-roles map created:\", nameLeadershipRolesMap);\n\n              // Apply leadership roles-based visibility rules using user's leadership roles from planData\n              // Only apply if tag configuration hasn't already hidden the panels\n              applyLeadershipRolesConfiguration(survey, planData.leadershipRoles, nameLeadershipRolesMap);\n\n              // Build the name-to-service-lines maps from the raw survey JSON\n              const nameServiceLinesMap = {};\n              const nameSubServiceLinesMap = {};\n              if (surveyJson.pages) {\n                surveyJson.pages.forEach(page => {\n                  buildNameServiceLinesMap(page.elements, nameServiceLinesMap);\n                  buildNameSubServiceLinesMap(page.elements, nameSubServiceLinesMap);\n                });\n              }\n              console.log(\"🏢 Name-to-service-lines map created:\", nameServiceLinesMap);\n              console.log(\"🏢 Name-to-sub-service-lines map created:\", nameSubServiceLinesMap);\n\n              // Apply service lines-based visibility rules using form owner's service lines from planData\n              // Only apply if tag configuration and leadership roles haven't already hidden the panels\n              applyServiceLinesConfiguration(survey, planData.serviceLines || [], planData.subServiceLines || [], nameServiceLinesMap, nameSubServiceLinesMap);\n\n              // Set up partner data auto-population for dropdowns with mapFromGroup\n              await setupPartnerReferenceDataAutoPopulation(survey, planData.partnerId, planData.questionnaire.year);\n              setSurveyModel(survey);\n\n              // Initialize page info for wizard\n              if (survey.pageCount > 1) {\n                var _survey$currentPage, _survey$currentPage2;\n                const visiblePageIndex = survey.visiblePages.indexOf(survey.currentPage);\n                const processedTitle = survey.getProcessedText(((_survey$currentPage = survey.currentPage) === null || _survey$currentPage === void 0 ? void 0 : _survey$currentPage.title) || ((_survey$currentPage2 = survey.currentPage) === null || _survey$currentPage2 === void 0 ? void 0 : _survey$currentPage2.name));\n                setCurrentPageInfo({\n                  pageNo: visiblePageIndex > -1 ? visiblePageIndex : 0,\n                  pageCount: survey.visiblePages.length,\n                  pageTitle: processedTitle\n                });\n              }\n              setHasLoaded(true); // Mark as loaded\n            } catch (parseError) {\n              console.error(\"Error parsing survey JSON:\", parseError);\n              setError(\"Failed to load survey form. Invalid survey definition.\");\n            }\n          } else {\n            setError(\"No survey definition found for the current year.\");\n          }\n        } else {\n          var _planData;\n          setError(((_planData = planData) === null || _planData === void 0 ? void 0 : _planData.message) || \"Failed to load partner plan data.\");\n        }\n      } catch (err) {\n        console.error(\"Error loading my plan:\", err);\n        setError(err.message || \"Failed to load partner plan. Please try again later.\");\n      } finally {\n        setLoading(false);\n      }\n    };\n    loadData();\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [hasLoaded, year, formId]); // Depend on hasLoaded, year, and formId to reload when parameters change\n\n  // Handle return to home navigation\n  const handleReturnToHome = useCallback(() => {\n    navigate(\"/home\");\n  }, [navigate]);\n\n  // Handle Send Back to Partner button click\n  const handleSendBackToPartner = () => {\n    setShowCommentsDialog(true);\n  };\n\n  // Handle reviewer comments dialog confirm\n  const handleCommentsConfirm = async comments => {\n    if (!(form !== null && form !== void 0 && form.id)) return;\n    setSendingBack(true);\n    try {\n      await formService.sendBackToPartner(form.id, comments);\n      messageService.successDialog(\"Form sent back to partner successfully\");\n\n      // Close dialog\n      setShowCommentsDialog(false);\n\n      // Navigate back or refresh\n      if (backHandler) {\n        backHandler();\n      } else {\n        navigate(\"/home\");\n      }\n    } catch (error) {\n      messageService.errorDialog(error.message || \"Failed to send form back to partner\");\n    } finally {\n      setSendingBack(false);\n    }\n  };\n\n  // Handle Submit Admin Update button click\n  const handleSubmitAdminUpdate = async () => {\n    if (!(form !== null && form !== void 0 && form.id)) return;\n\n    // Show confirmation dialog\n    const confirmed = window.confirm(\"Are you sure you want to submit this admin update? This action will update the form status.\");\n    if (!confirmed) {\n      return;\n    }\n    try {\n      // Save the current answers first\n      if (surveyModel !== null && surveyModel !== void 0 && surveyModel.data) {\n        await formService.saveUserAnswer(form.id, JSON.stringify(surveyModel.data));\n      }\n\n      // Submit the form using the same endpoint as the regular submit\n      const updatedForm = await formService.submitForm(form.id);\n      console.log(\"Admin update submitted successfully\");\n\n      // Show success message\n      if (updatedForm && updatedForm.message) {\n        messageService.successToast(updatedForm.message);\n      } else {\n        messageService.successToast(\"Admin update submitted successfully!\");\n      }\n\n      // Update form status with the actual returned status\n      if (updatedForm) {\n        setForm(prev => ({\n          ...prev,\n          status: updatedForm.status,\n          statusString: getFormStatusName(updatedForm.status)\n        }));\n      }\n\n      // Navigate back or refresh\n      if (backHandler) {\n        backHandler();\n      } else {\n        navigate(\"/home\");\n      }\n    } catch (error) {\n      console.error(\"Error submitting admin update:\", error);\n      messageService.errorToast(error.message || \"Failed to submit admin update. Please try again.\");\n    }\n  };\n\n  // Handle reviewer comments dialog hide\n  const handleCommentsDialogHide = () => {\n    if (!sendingBack) {\n      setShowCommentsDialog(false);\n    }\n  };\n\n  // Check if current user can send form back to partner\n  const canSendBackToPartner = () => {\n    if (!form) return false;\n\n    // Check if current user is a reviewer based on the form access configuration\n    if (currentUserFormRole !== UserFormRole.Reviewer) {\n      return false;\n    }\n\n    // Check if form is in \"Under Review\" status for MidYear or YearEnd cycles\n    return form.status === FormStatus.MidYearReviewUnderReview || form.status === FormStatus.YearEndReviewUnderReview;\n  };\n\n  //\n  // Handle PDF export with Survey.js build function.\n  //\n  const handleExportToPDF = useCallback(async () => {\n    await PDFExportUtilities.handleExportToPDF(surveyModel, form);\n  }, [surveyModel, form]);\n\n  // Alternative PDF export method using browser's print functionality\n  const handleAlternativePDFExport = useCallback(() => {\n    PDFExportUtilities.handleAlternativePDFExport(surveyModel, form);\n  }, [surveyModel, form]);\n  const handlePageChanged = (sender, options) => {\n    // Update page information for better UX\n    if (sender && sender.currentPage) {\n      var _sender$currentPage, _sender$currentPage2;\n      const visiblePageIndex = sender.visiblePages.indexOf(sender.currentPage);\n      const processedTitle = sender.currentPage.getProcessedText(((_sender$currentPage = sender.currentPage) === null || _sender$currentPage === void 0 ? void 0 : _sender$currentPage.title) || ((_sender$currentPage2 = sender.currentPage) === null || _sender$currentPage2 === void 0 ? void 0 : _sender$currentPage2.name));\n      const pageInfo = {\n        pageNo: visiblePageIndex > -1 ? visiblePageIndex : 0,\n        pageCount: sender.visiblePages.length,\n        pageTitle: processedTitle\n      };\n      setCurrentPageInfo(pageInfo);\n    }\n  };\n\n  // Save questionnaire data without submitting (use backend AutoSave API)\n  const saveQuestionnaire = async (callback, dataOverride = null) => {\n    try {\n      var _ref;\n      const currentForm = formRef.current;\n      if (!currentForm) {\n        // Form not ready yet; skip this autosave quietly\n        callback && callback();\n        return;\n      }\n      const answers = (_ref = dataOverride !== null && dataOverride !== void 0 ? dataOverride : surveyModel === null || surveyModel === void 0 ? void 0 : surveyModel.data) !== null && _ref !== void 0 ? _ref : {};\n\n      // Call auto-save endpoint which will create/update UserAnswer and set form status server-side\n      await formService.autoSaveUserAnswer(currentForm.id, answers);\n    } catch (e) {\n      console.warn(\"Auto-save failed:\", e);\n    } finally {\n      callback && callback();\n    }\n  };\n\n  // Survey value changed handler - immediate save (following Retirement project pattern)\n  function surveyValueChanged(sender, options) {\n    console.log(\"Survey value changed:\", options);\n\n    // Save immediately using the values from the sender (always up-to-date)\n    saveQuestionnaire(undefined, sender === null || sender === void 0 ? void 0 : sender.data);\n  }\n\n  // Admin reopen function following Retirement project pattern\n  const adminReopen = () => {\n    function returnForm(confirmed) {\n      if (!confirmed) {\n        return;\n      }\n      http.put(APP_CONFIG.apiDomain + \"/api/Form/AdminReopenForms\", [form.id], {\n        headers: {\n          \"Content-Type\": \"application/json\"\n        }\n      }).then(response => {\n        // popup success if OK\n        if (response.data.resultStatus === 1) {\n          messageService.successDialog(\"Questionnaire reopened.\");\n          backHandler && backHandler();\n        } else {\n          messageService.errorDialog(response.data.message);\n        }\n      }).catch(() => {\n        messageService.errorDialog(\"Failed to reopen questionnaire.\");\n      });\n    }\n    const surveyTitle = questionnaire ? questionnaire.name : \"Partner Plan\";\n    const partnerName = form ? form.partnerName : \"Partner\";\n    messageService.confirmDialog(`Are you sure to reopen the ${surveyTitle} to ${partnerName}`, returnForm);\n  };\n\n  // Render reviewer comments history section (only for form owners)\n  const renderReviewerCommentsHistory = () => {\n    // Only show for form owners and when there are comments to display\n    if (currentUserFormRole !== UserFormRole.FormOwner || !reviewerComments || reviewerComments.length === 0) {\n      return null;\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"reviewer-comments-section\",\n      children: /*#__PURE__*/_jsxDEV(Panel, {\n        header: \"Reviewer Comments History\",\n        toggleable: true,\n        collapsed: true,\n        className: \"reviewer-comments-panel\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"comments-list\",\n          children: reviewerComments.map((comment, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"comment-item\",\n            style: {\n              marginBottom: index < reviewerComments.length - 1 ? \"1rem\" : \"0\",\n              padding: \"1rem\",\n              border: \"1px solid #e0e0e0\",\n              borderRadius: \"4px\",\n              backgroundColor: \"#f9f9f9\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"comment-header\",\n              style: {\n                display: \"flex\",\n                justifyContent: \"space-between\",\n                alignItems: \"center\",\n                marginBottom: \"0.5rem\",\n                fontSize: \"0.9rem\",\n                color: \"#666\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"reviewer-name\",\n                children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: comment.submittedByName || \"Reviewer\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1176,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1175,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"comment-date\",\n                children: comment.submittedOn ? formatDateTime(comment.submittedOn) : \"Date not available\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1178,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1164,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"comment-cycle\",\n              style: {\n                fontSize: \"0.8rem\",\n                color: \"#888\",\n                marginBottom: \"0.5rem\"\n              },\n              children: /*#__PURE__*/_jsxDEV(\"em\", {\n                children: [comment.cycleDescription, \" Cycle\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1188,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1180,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"comment-text\",\n              style: {\n                lineHeight: \"1.5\",\n                whiteSpace: \"pre-wrap\"\n              },\n              children: comment.comments\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1190,\n              columnNumber: 17\n            }, this)]\n          }, comment.id || index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1153,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1151,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1150,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1149,\n      columnNumber: 7\n    }, this);\n  };\n  if (!authService || !authService.isAuthenticated()) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"survey-container\",\n      children: /*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Partner Plans\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1210,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1209,\n      columnNumber: 7\n    }, this);\n  }\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"survey-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Partner Plans\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1219,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Loading plan...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1220,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1218,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1217,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      id: \"surveyContainer\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-message p-message-error\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-message-wrapper\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"p-message-icon pi pi-times-circle\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1232,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-message-text\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"p-message-summary\",\n                children: \"Error\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1234,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-message-detail\",\n                children: error\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1235,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1233,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1231,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1230,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"p-button p-component p-button-outlined mt-3\",\n          onClick: handleReturnToHome,\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-button-icon pi pi-arrow-left\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1240,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"p-button-label\",\n            children: \"Back\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1241,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1239,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1229,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1228,\n      columnNumber: 7\n    }, this);\n  }\n  if (!surveyModel) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"survey-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Partner Plans\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1252,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"No plan available at this time.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1253,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1251,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1250,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"survey-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"partner-planning-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bdo-logo\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            alt: \"BDO logo\",\n            src: `${APP_CONFIG.basePath}/logo.png`,\n            onError: e => e.target.src = `/images/BDO.png`,\n            className: \"bdo-logo-image\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1265,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"partner-planning-text\",\n            children: formId ? `${form !== null && form !== void 0 && form.year || questionnaire !== null && questionnaire !== void 0 && questionnaire.year || year ? `${(form === null || form === void 0 ? void 0 : form.year) || (questionnaire === null || questionnaire === void 0 ? void 0 : questionnaire.year) || year}` : \"\"} Partner Planning Tool - Review Mode` : `${form !== null && form !== void 0 && form.year || questionnaire !== null && questionnaire !== void 0 && questionnaire.year || year ? `${(form === null || form === void 0 ? void 0 : form.year) || (questionnaire === null || questionnaire === void 0 ? void 0 : questionnaire.year) || year}` : \"\"} Partner Planning Tool`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1271,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1264,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-actions\",\n          children: [isAdminEditing && /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"submit-admin-update-btn\",\n            onClick: handleSubmitAdminUpdate,\n            style: {\n              backgroundColor: \"#28a745\",\n              color: \"white\",\n              border: \"none\",\n              padding: \"8px 16px\",\n              borderRadius: \"4px\",\n              cursor: \"pointer\",\n              marginRight: \"10px\",\n              fontSize: \"14px\",\n              fontWeight: \"600\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-check\",\n              style: {\n                marginRight: \"5px\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1297,\n              columnNumber: 17\n            }, this), \"Submit Admin Update\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1282,\n            columnNumber: 15\n          }, this), canSendBackToPartner() && /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"send-back-btn\",\n            onClick: handleSendBackToPartner,\n            style: {\n              backgroundColor: \"#f39c12\",\n              color: \"white\",\n              border: \"none\",\n              padding: \"8px 16px\",\n              borderRadius: \"4px\",\n              cursor: \"pointer\",\n              marginRight: \"10px\",\n              fontSize: \"14px\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-send\",\n              style: {\n                marginRight: \"5px\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1318,\n              columnNumber: 17\n            }, this), \"Send Back to Partner\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1304,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"export-pdf-btn\",\n            onClick: handleAlternativePDFExport,\n            style: {\n              marginLeft: \"10px\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"pi pi-print\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1328,\n              columnNumber: 15\n            }, this), \"Print/Export to PDF\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1327,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"return-home-btn\",\n            onClick: handleReturnToHome,\n            children: \"Return to Home\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1331,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1279,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1263,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-description\",\n        children: [\"This form guides partners in creating a personal plan to achieve the firm's \", year, \" strategy. Complete all sections to set qualitative and quantitative targets.\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1336,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1262,\n      columnNumber: 7\n    }, this), formId && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: \"1rem\"\n      },\n      children: /*#__PURE__*/_jsxDEV(AdminModificationAuditHistory, {\n        formId: formId,\n        visible: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1345,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1344,\n      columnNumber: 9\n    }, this), currentUserFormRole === UserFormRole.FormOwner && form && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"partner-details-section\",\n      style: {\n        marginBottom: \"1rem\"\n      },\n      children: /*#__PURE__*/_jsxDEV(Panel, {\n        header: \"Partner Details\",\n        toggleable: true,\n        collapsed: false,\n        className: \"partner-details-panel\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"partner-details-table\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"partner-details-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"detail-label\",\n                children: \"Partner Name:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1356,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"detail-value\",\n                children: (form === null || form === void 0 ? void 0 : form.partnerName) || \"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1357,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1355,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"detail-label\",\n                children: \"Service Line:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1360,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"detail-value\",\n                children: (form === null || form === void 0 ? void 0 : form.serviceLine) || \"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1361,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1359,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"detail-label\",\n                children: \"Sub-service Line:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1364,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"detail-value\",\n                children: (form === null || form === void 0 ? void 0 : form.subServiceLine) || \"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1365,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1363,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"detail-label\",\n                children: \"Location:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1368,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"detail-value\",\n                children: (form === null || form === void 0 ? void 0 : form.location) || \"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1369,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1367,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"detail-label\",\n                children: \"Primary Reviewer:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1372,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"detail-value\",\n                children: (form === null || form === void 0 ? void 0 : form.primaryReviewerName) || \"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1373,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1371,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"detail-label\",\n                children: \"Secondary Reviewer:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1376,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"detail-value\",\n                children: (form === null || form === void 0 ? void 0 : form.secondaryReviewerName) || \"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1377,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1375,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"status-container\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"status-badge\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"status-text\",\n                  children: \"STATUS: \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1381,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `status-value ${getFormStatusClass(form === null || form === void 0 ? void 0 : form.status)}`,\n                  children: (form === null || form === void 0 ? void 0 : form.statusString) || getFormStatusName(form === null || form === void 0 ? void 0 : form.status) || \"DRAFT\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1382,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1380,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"autosaved-container\",\n                children: form !== null && form !== void 0 && form.partnerSubmittionDate ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"autosaved-info\",\n                  children: [\"Autosaved: \", new Date(form.partnerSubmittionDate).toLocaleDateString(), \" at\", \" \", new Date(form.partnerSubmittionDate).toLocaleTimeString()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1388,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"autosaved-info\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1393,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1386,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1379,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1354,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1353,\n          columnNumber: 13\n        }, this), renderReviewerCommentsHistory()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1352,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1351,\n      columnNumber: 9\n    }, this), currentUserFormRole !== UserFormRole.FormOwner && form && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"partner-info-section\",\n      style: {\n        background: \"#f8f9fa\",\n        padding: \"1rem\",\n        marginBottom: \"1rem\",\n        borderRadius: \"4px\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Partner Information\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1426,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: \"grid\",\n          gridTemplateColumns: \"repeat(auto-fit, minmax(250px, 1fr))\",\n          gap: \"1rem\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Partner Name:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1429,\n            columnNumber: 15\n          }, this), \" \", form.partnerName || \"N/A\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1428,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Service Line:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1432,\n            columnNumber: 15\n          }, this), \" \", form.serviceLine || \"N/A\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1431,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Sub-Service Line:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1435,\n            columnNumber: 15\n          }, this), \" \", form.subServiceLine || \"N/A\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1434,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Location:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1438,\n            columnNumber: 15\n          }, this), \" \", form.location || \"N/A\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1437,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Primary Reviewer:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1441,\n            columnNumber: 15\n          }, this), \" \", form.primaryReviewerName || \"N/A\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1440,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Secondary Reviewer:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1444,\n            columnNumber: 15\n          }, this), \" \", form.secondaryReviewerName || \"N/A\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1443,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Status:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1447,\n            columnNumber: 15\n          }, this), \" \", form.statusString || getFormStatusName(form.status) || \"DRAFT\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1446,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Year:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1450,\n            columnNumber: 15\n          }, this), \" \", form.year || \"N/A\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1449,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1427,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1417,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"survey-wrapper mt-1\",\n      children: /*#__PURE__*/_jsxDEV(Survey, {\n        model: surveyModel\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1457,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1456,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ReviewerCommentsDialog, {\n      visible: showCommentsDialog,\n      onHide: handleCommentsDialogHide,\n      onConfirm: handleCommentsConfirm,\n      loading: sendingBack,\n      title: \"Send Back to Partner\",\n      message: `Please provide comments explaining why the form for ${(form === null || form === void 0 ? void 0 : form.partnerName) || \"this partner\"} needs to be revised:`\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1494,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1260,\n    columnNumber: 5\n  }, this);\n};\n\n/**\r\n * Set up partner data auto-population for dropdowns with mapFromGroup\r\n * @param {Object} survey - The SurveyJS model instance\r\n * @param {string} partnerId - Partner ID for data retrieval\r\n * @param {number} year - Year for data retrieval\r\n */\n_s(PartnerPlanQuestionnaire, \"ESMD1mq3qyykMIHHXW8CJSUYlTw=\", false, function () {\n  return [useNavigate, useLoadingControl];\n});\n_c = PartnerPlanQuestionnaire;\nasync function setupPartnerReferenceDataAutoPopulation(survey, partnerId, year) {\n  console.log(\"🔧 setupPartnerReferenceDataAutoPopulation called with:\", {\n    partnerId,\n    year\n  });\n\n  // Find all text questions with auto-population enabled (legacy and cycle-specific)\n  const autoPopulateFields = survey.getAllQuestions().filter(q => {\n    if (q.getType() !== \"text\") return false;\n\n    // Check for any linkedToGroup[Cycle] property (legacy or cycle-specific)\n    return q.linkedToGroupPlanning || q.linkedToGroupMidYear || q.linkedToGroupYearEnd;\n  });\n  console.log(\"📝 Found auto-populate text fields:\", autoPopulateFields.map(f => ({\n    name: f.name,\n    linkedToPlanning: f.linkedToGroupPlanning,\n    linkedToMidYear: f.linkedToGroupMidYear,\n    linkedToYearEnd: f.linkedToGroupYearEnd\n  })));\n\n  // Create mapping of dropdown -> text fields with cycle information\n  const dropdownToTextMapping = {};\n  autoPopulateFields.forEach(textField => {\n    // Determine which linkedToGroup[Cycle] property to use and the corresponding cycle\n    let dropdownName;\n    let cycle;\n    if (textField.linkedToGroupPlanning) {\n      dropdownName = textField.linkedToGroupPlanning;\n      cycle = PartnerPlanCycle.Planning;\n    } else if (textField.linkedToGroupMidYear) {\n      dropdownName = textField.linkedToGroupMidYear;\n      cycle = PartnerPlanCycle.MidYearReview;\n    } else if (textField.linkedToGroupYearEnd) {\n      dropdownName = textField.linkedToGroupYearEnd;\n      cycle = PartnerPlanCycle.YearEndReview;\n    }\n    if (dropdownName) {\n      if (!dropdownToTextMapping[dropdownName]) {\n        dropdownToTextMapping[dropdownName] = [];\n      }\n      // Store both the text field and its associated cycle\n      dropdownToTextMapping[dropdownName].push({\n        textField,\n        cycle\n      });\n    }\n  });\n\n  // Debug: Log all questions first\n  const allQuestions = survey.getAllQuestions();\n  console.log(\"🔍 All questions in survey:\", allQuestions.map(q => ({\n    name: q.name,\n    type: q.getType(),\n    mapFromGroup: q.mapFromGroup,\n    hasMapFromGroup: !!q.mapFromGroup\n  })));\n\n  // Set up dynamic choice loading for dropdowns with mapFromGroup\n  const mapFromGroupDropdowns = survey.getAllQuestions().filter(q => q.getType() === \"dropdown\" && q.mapFromGroup);\n  console.log(\"🎯 Found dropdowns with mapFromGroup:\", mapFromGroupDropdowns.map(d => ({\n    name: d.name,\n    group: d.mapFromGroup\n  })));\n\n  // Load choices for mapFromGroup dropdowns\n  for (const dropdown of mapFromGroupDropdowns) {\n    try {\n      console.log(`🌐 Calling API: getColumnsByGroup(${year}, \"${dropdown.mapFromGroup}\") for dropdown \"${dropdown.name}\"`);\n      const columns = await partnerReferenceDataUploadService.getColumnsByGroup(year, dropdown.mapFromGroup);\n      dropdown.choices = columns;\n      console.log(`✅ Loaded ${columns.length} choices for dropdown ${dropdown.name}:`, columns);\n    } catch (error) {\n      console.error(`❌ Error loading choices for dropdown ${dropdown.name}:`, error);\n    }\n  }\n\n  // Set up value change handlers\n  survey.onValueChanged.add(async (sender, options) => {\n    await handlePartnerReferenceDataAutoPopulation(sender, options, dropdownToTextMapping, partnerId, year);\n  });\n\n  // Set up initial population for existing values\n  Object.keys(dropdownToTextMapping).forEach(dropdownName => {\n    const dropdown = survey.getQuestionByName(dropdownName);\n    if (dropdown && dropdown.value) {\n      // Populate on initial load if dropdown already has a value\n      handlePartnerReferenceDataAutoPopulation(survey, {\n        name: dropdownName,\n        value: dropdown.value\n      }, dropdownToTextMapping, partnerId, year);\n    }\n  });\n}\n\n/**\r\n * Handle partner data auto-population when dropdown values change\r\n * @param {Object} sender - The survey instance\r\n * @param {Object} options - The value change options\r\n * @param {Object} dropdownToTextMapping - Mapping of dropdown names to text fields with cycle info\r\n * @param {string} partnerId - Partner ID for data retrieval\r\n * @param {number} year - Year for data retrieval\r\n */\nasync function handlePartnerReferenceDataAutoPopulation(sender, options, dropdownToTextMapping, partnerId, year) {\n  const dropdownName = options.name;\n  const selectedColumn = options.value;\n\n  // Check if this dropdown has linked text fields\n  const linkedTextFieldsWithCycles = dropdownToTextMapping[dropdownName];\n  if (!linkedTextFieldsWithCycles || linkedTextFieldsWithCycles.length === 0) {\n    return;\n  }\n  const dropdown = sender.getQuestionByName(dropdownName);\n  if (!dropdown || !dropdown.mapFromGroup) {\n    return;\n  }\n  try {\n    if (!selectedColumn) {\n      // Clear linked text fields when no selection\n      linkedTextFieldsWithCycles.forEach(({\n        textField\n      }) => {\n        sender.setValue(textField.name, \"\");\n      });\n      return;\n    }\n\n    // Show loading state in text fields\n    linkedTextFieldsWithCycles.forEach(({\n      textField\n    }) => {\n      sender.setValue(textField.name, \"Loading...\");\n    });\n\n    // Process each text field with its specific cycle\n    for (const {\n      textField,\n      cycle\n    } of linkedTextFieldsWithCycles) {\n      try {\n        // Fetch partner reference data value with the text field's specific cycle\n        const dataValue = await partnerReferenceDataUploadService.getPartnerReferenceDataValue(year, cycle, dropdown.mapFromGroup, selectedColumn, partnerId);\n\n        // Update the text field with the fetched value\n        const displayValue = dataValue !== null && dataValue !== undefined ? String(dataValue) : \"No data available\";\n        sender.setValue(textField.name, displayValue);\n        console.log(`Auto-populated ${textField.name} with value: ${displayValue} (cycle: ${cycle})`);\n      } catch (error) {\n        console.error(`Error fetching data for ${textField.name} (cycle: ${cycle}):`, error);\n        sender.setValue(textField.name, \"Error loading data\");\n      }\n    }\n  } catch (error) {\n    console.error(\"Error in partner data auto-population:\", error);\n\n    // Show error state in all text fields\n    linkedTextFieldsWithCycles.forEach(({\n      textField\n    }) => {\n      sender.setValue(textField.name, \"Error loading data\");\n    });\n  }\n}\nexport default PartnerPlanQuestionnaire;\nvar _c;\n$RefreshReg$(_c, \"PartnerPlanQuestionnaire\");", "map": {"version": 3, "names": ["useState", "useEffect", "useContext", "useCallback", "useRef", "Survey", "Model", "configureSurveyJSLicense", "useNavigate", "AuthContext", "messageService", "useLoadingControl", "formService", "partnerReferenceDataUploadService", "FormStatus", "getFormStatusName", "getFormStatusClass", "isFormEditableByOwner", "UserFormRole", "PartnerPlanCycle", "http", "APP_CONFIG", "FormAnswerUtility", "PDFExportUtilities", "ReviewerCommentsDialog", "AdminModificationAuditHistory", "Panel", "formatDateTime", "registerCustomPropertiesForRuntime", "jsxDEV", "_jsxDEV", "buildNameTagMap", "elements", "map", "Array", "isArray", "for<PERSON>ach", "element", "name", "tag", "buildNameLeadershipRolesMap", "leadershipRoles", "buildNameServiceLinesMap", "serviceLines", "buildNameSubServiceLinesMap", "subServiceLines", "normalizeLeadershipRole", "role", "trim", "toUpperCase", "applyTagConfiguration", "survey", "config", "nameTagMap", "console", "warn", "log", "appliedRulesCount", "applyRulesToElements", "elementType", "length", "tagName", "config<PERSON><PERSON>", "Object", "keys", "find", "k", "toLowerCase", "tagConfig", "readOnly", "visible", "getAllQuestions", "getAllPanels", "error", "applyLeadershipRolesConfiguration", "userLeadershipRoles", "nameLeadershipRolesMap", "processedPanelsCount", "panels", "panel", "panelLeadershipRoles", "requiredRoles", "split", "filter", "normalizedRequiredRoles", "hasRequiredRole", "some", "requiredRole", "userRole", "join", "applyServiceLinesConfiguration", "formOwnerServiceLines", "formOwnerSubServiceLines", "nameServiceLinesMap", "nameSubServiceLinesMap", "panelServiceLines", "panelSubServiceLines", "hiddenByServiceLines", "requiredServiceLines", "sl", "hasRequiredServiceLine", "requiredSL", "ownerSL", "requiredSubServiceLines", "ssl", "hasRequiredSubServiceLine", "requiredSSL", "ownerSSL", "hasServiceLinesRestriction", "hasSubServiceLinesRestriction", "PartnerPlanQuestionnaire", "year", "formId", "<PERSON><PERSON><PERSON><PERSON>", "_s", "surveyModel", "setSurveyModel", "questionnaire", "setQuestionnaire", "form", "setForm", "loading", "setLoading", "setError", "hasLoaded", "setHasLoaded", "currentPageInfo", "setCurrentPageInfo", "pageNo", "pageCount", "pageTitle", "formRef", "current", "showCommentsDialog", "setShowCommentsDialog", "sendingBack", "setSendingBack", "currentUserFormRole", "setCurrentUserFormRole", "formAccessConfig", "setFormAccessConfig", "reviewerComments", "setReviewerComments", "isSubmissionAllowed", "setIsSubmissionAllowed", "isAdminEditing", "setIsAdminEditing", "authService", "navigate", "isMyPartnerPlan", "isPartnerPlan", "isAuthenticated", "loadData", "planData", "getPartnerPlan", "targetYear", "parseInt", "Date", "getFullYear", "getMyPlan", "_planData$userAnswer", "undefined", "formWithUserAnswer", "userAnswer", "id", "comments", "getReviewerCommentsHistory", "existingAnswers", "answer", "definitionJson", "surveyJson", "JSON", "parse", "Error", "pages", "page", "pageIndex", "elementIndex", "type", "nestedElement", "nestedIndex", "isDesigner", "questions", "firstQuestion", "firstPanel", "parsedAnswers", "data", "parseError", "clearInvisibleValues", "showPreviewBeforeComplete", "showProgressBar", "progressBarLocation", "questionsOnPageMode", "showPageNumbers", "showPageTitles", "pagePrevText", "pageNextText", "goNextPageAutomatic", "formEditable", "isEditable", "mode", "completeText", "previewText", "editText", "showPrevButton", "showCompleteButton", "css", "root", "header", "body", "footer", "applyTheme", "themeName", "colorPalette", "isPanelless", "cssVariables", "onTextMarkdown", "add", "options", "str", "convertToHtml", "text", "html", "completionHandler", "sender", "allowComplete", "confirmDialog", "confirmed", "saveUserAnswer", "stringify", "updatedForm", "submitForm", "message", "successToast", "prev", "status", "statusString", "doComplete", "errorToast", "onComplete", "onValueChanged", "surveyValueChanged", "onCurrentPageChanged", "handlePageChanged", "FormOwner", "isLastPage", "lastPage", "currentPage", "hasCustomMessage", "submissionMessage", "submissionNotAllowedMessage", "messageHtml", "description", "onCurrentPageChanging", "_", "_options$oldCurrentPa", "_options$newCurrentPa", "oldCurrentPage", "newCurrentPage", "onShowPreview", "showNavigationButtons", "completedHtml", "title", "formTagConfiguration", "setupPartnerReferenceDataAutoPopulation", "partnerId", "_survey$currentPage", "_survey$currentPage2", "visiblePageIndex", "visiblePages", "indexOf", "processedTitle", "getProcessedText", "_planData", "err", "handleReturnToHome", "handleSendBackToPartner", "handleCommentsConfirm", "sendBackToPartner", "successDialog", "errorDialog", "handleSubmitAdminUpdate", "window", "confirm", "handleCommentsDialogHide", "canSendBackToPartner", "Reviewer", "MidYearReviewUnderReview", "YearEndReviewUnderReview", "handleExportToPDF", "handleAlternativePDFExport", "_sender$currentPage", "_sender$currentPage2", "pageInfo", "saveQuestionnaire", "callback", "dataOverride", "_ref", "currentForm", "answers", "autoSaveUserAnswer", "e", "adminReopen", "returnForm", "put", "apiDomain", "headers", "then", "response", "resultStatus", "catch", "surveyTitle", "partner<PERSON>ame", "renderReviewerCommentsHistory", "className", "children", "toggleable", "collapsed", "comment", "index", "style", "marginBottom", "padding", "border", "borderRadius", "backgroundColor", "display", "justifyContent", "alignItems", "fontSize", "color", "submittedByName", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "submittedOn", "cycleDescription", "lineHeight", "whiteSpace", "onClick", "alt", "src", "basePath", "onError", "target", "cursor", "marginRight", "fontWeight", "marginLeft", "serviceLine", "subServiceLine", "location", "primaryReviewerName", "secondaryReviewerName", "partnerSubmittionDate", "toLocaleDateString", "toLocaleTimeString", "background", "gridTemplateColumns", "gap", "model", "onHide", "onConfirm", "_c", "autoPopulateFields", "q", "getType", "linkedToGroupPlanning", "linkedToGroupMidYear", "linkedToGroupYearEnd", "f", "linkedToPlanning", "linkedToMidYear", "linkedToYearEnd", "dropdownToTextMapping", "textField", "dropdownName", "cycle", "Planning", "MidYearReview", "YearEndReview", "push", "allQuestions", "mapFromGroup", "hasMapFromGroup", "mapFromGroupDropdowns", "d", "group", "dropdown", "columns", "getColumnsByGroup", "choices", "handlePartnerReferenceDataAutoPopulation", "getQuestionByName", "value", "selectedColumn", "linkedTextFieldsWithCycles", "setValue", "dataValue", "getPartnerReferenceDataValue", "displayValue", "String", "$RefreshReg$"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/components/questionnaire/PartnerPlanQuestionnaire.jsx"], "sourcesContent": ["import { useState, useEffect, useContext, useCallback, useRef } from \"react\";\r\nimport { Survey } from \"survey-react-ui\";\r\nimport { Model } from \"survey-core\";\r\nimport { configureSurveyJSLicense } from \"../../core/surveyjs/licenseConfig\";\r\n\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { AuthContext } from \"../../core/auth/components/authProvider\";\r\nimport { messageService } from \"../../core/message/messageService\";\r\nimport { useLoadingControl } from \"../../core/loading/hooks/useLoadingControl\";\r\nimport formService from \"../../services/formService\";\r\nimport partnerReferenceDataUploadService from \"../../services/partnerReferenceDataUploadService\";\r\nimport { FormStatus, getFormStatusName, getFormStatusClass, isFormEditableByOwner } from \"../../core/enumertions/formStatus\";\r\nimport { UserFormRole } from \"../../core/enumertions/userFormRole\";\r\nimport { PartnerPlanCycle } from \"../../core/enumertions/partnerPlanCycle\";\r\nimport http from \"../../core/http/httpClient\";\r\nimport APP_CONFIG from \"../../core/config/appConfig\";\r\nimport FormAnswerUtility from \"./formAnswerUtilities\";\r\nimport PDFExportUtilities from \"./pdfExportUtilities\";\r\nimport ReviewerCommentsDialog from \"../common/ReviewerCommentsDialog\";\r\nimport AdminModificationAuditHistory from \"../audit/AdminModificationAuditHistory\";\r\nimport { Panel } from \"primereact/panel\";\r\nimport { formatDateTime } from \"../../core/utils/dateUtils\";\r\nimport { registerCustomPropertiesForRuntime } from \"../../core/utils/surveyCustomPropertiesUtils\";\r\n\r\n// Import the latest Survey.js CSS themes\r\nimport \"survey-core/survey-core.css\";\r\nimport \"../../survey-v2-styles.css\"; // Import our custom Survey.js v2 styles\r\nimport \"./PartnerPlanQuestionnaire.css\"; // Import component-specific styles\r\n\r\n/**\r\n * Recursively traverses the survey JSON to build a map of element names to their tags.\r\n * @param {Array} elements - The array of elements (questions or panels) to process.\r\n * @param {Object} map - The map to populate.\r\n */\r\nconst buildNameTagMap = (elements, map) => {\r\n  if (!elements || !Array.isArray(elements)) {\r\n    return;\r\n  }\r\n\r\n  elements.forEach((element) => {\r\n    if (element.name && element.tag) {\r\n      map[element.name] = element.tag;\r\n    }\r\n\r\n    // Recurse into nested elements (e.g., in panels)\r\n    if (element.elements) {\r\n      buildNameTagMap(element.elements, map);\r\n    }\r\n  });\r\n};\r\n\r\n/**\r\n * Recursively traverses the survey JSON to build a map of element names to their leadership roles.\r\n * @param {Array} elements - The array of elements (questions or panels) to process.\r\n * @param {Object} map - The map to populate.\r\n */\r\nconst buildNameLeadershipRolesMap = (elements, map) => {\r\n  if (!elements || !Array.isArray(elements)) {\r\n    return;\r\n  }\r\n\r\n  elements.forEach((element) => {\r\n    if (element.name && element.leadershipRoles) {\r\n      map[element.name] = element.leadershipRoles;\r\n    }\r\n\r\n    // Recurse into nested elements (e.g., in panels)\r\n    if (element.elements) {\r\n      buildNameLeadershipRolesMap(element.elements, map);\r\n    }\r\n  });\r\n};\r\n\r\n/**\r\n * Recursively traverses the survey JSON to build a map of element names to their service lines.\r\n * @param {Array} elements - The array of elements (questions or panels) to process.\r\n * @param {Object} map - The map to populate.\r\n */\r\nconst buildNameServiceLinesMap = (elements, map) => {\r\n  if (!elements || !Array.isArray(elements)) {\r\n    return;\r\n  }\r\n\r\n  elements.forEach((element) => {\r\n    if (element.name && element.serviceLines) {\r\n      map[element.name] = element.serviceLines;\r\n    }\r\n\r\n    // Recurse into nested elements (e.g., in panels)\r\n    if (element.elements) {\r\n      buildNameServiceLinesMap(element.elements, map);\r\n    }\r\n  });\r\n};\r\n\r\n/**\r\n * Recursively traverses the survey JSON to build a map of element names to their sub-service lines.\r\n * @param {Array} elements - The array of elements (questions or panels) to process.\r\n * @param {Object} map - The map to populate.\r\n */\r\nconst buildNameSubServiceLinesMap = (elements, map) => {\r\n  if (!elements || !Array.isArray(elements)) {\r\n    return;\r\n  }\r\n\r\n  elements.forEach((element) => {\r\n    if (element.name && element.subServiceLines) {\r\n      map[element.name] = element.subServiceLines;\r\n    }\r\n\r\n    // Recurse into nested elements (e.g., in panels)\r\n    if (element.elements) {\r\n      buildNameSubServiceLinesMap(element.elements, map);\r\n    }\r\n  });\r\n};\r\n\r\n/**\r\n * Normalize leadership role for consistent comparison\r\n * @param {string} role - Original leadership role\r\n * @returns {string} Normalized leadership role\r\n */\r\nconst normalizeLeadershipRole = (role) => {\r\n  if (!role || typeof role !== \"string\") {\r\n    return \"\";\r\n  }\r\n  // Apply normalization: trim whitespace, convert to uppercase for consistent comparison\r\n  return role.trim().toUpperCase();\r\n};\r\n/**\r\n * Applies tag configuration from planData to survey elements for visibility and readonly rules\r\n * @param {Object} survey - The SurveyJS model instance\r\n * @param {Object} config - The form access configuration object from planData\r\n * @param {Object} nameTagMap - Map of element names to their tag values\r\n * @returns {Object|null} The form access configuration object or null if not found\r\n */\r\nconst applyTagConfiguration = (survey, config, nameTagMap) => {\r\n  try {\r\n    // Validate inputs\r\n    if (!survey) {\r\n      console.warn(\"🏷️ Survey model is null, skipping tag configuration\");\r\n      return null;\r\n    }\r\n\r\n    if (!config) {\r\n      console.log(\"🏷️ No Form Access configuration found for this form. Using default behavior.\");\r\n      return null;\r\n    }\r\n\r\n    console.log(\"📋 Form Access configuration received:\", config);\r\n\r\n    // Validate config structure\r\n    if (typeof config !== \"object\") {\r\n      console.warn(\"🏷️ Invalid tag configuration format received. Using default behavior.\");\r\n      return null;\r\n    }\r\n\r\n    let appliedRulesCount = 0;\r\n\r\n    const applyRulesToElements = (elements, elementType) => {\r\n      console.log(`🏷️ Processing ${elements.length} ${elementType} for tag rules`);\r\n      elements.forEach((element) => {\r\n        const tagName = nameTagMap[element.name] || element.tag;\r\n        console.log(`🔍 ${elementType} ${element.name}: tag=\"${tagName}\"`);\r\n        if (tagName) {\r\n          const configKey = Object.keys(config).find((k) => k.toLowerCase() === tagName.toLowerCase());\r\n          if (configKey && config[configKey]) {\r\n            const tagConfig = config[configKey];\r\n\r\n            if (typeof tagConfig.readOnly === \"boolean\") {\r\n              element.readOnly = tagConfig.readOnly;\r\n              console.log(`🔒 Applied readOnly=${tagConfig.readOnly} to ${elementType} ${element.name} with tag ${tagName}`);\r\n              appliedRulesCount++;\r\n            }\r\n\r\n            if (typeof tagConfig.visible === \"boolean\") {\r\n              element.visible = tagConfig.visible;\r\n              console.log(`👁️ Applied visible=${tagConfig.visible} to ${elementType} ${element.name} with tag ${tagName}`);\r\n              appliedRulesCount++;\r\n            }\r\n          }\r\n        }\r\n      });\r\n    };\r\n\r\n    applyRulesToElements(survey.getAllQuestions(), \"question\");\r\n    applyRulesToElements(survey.getAllPanels(), \"panel\");\r\n\r\n    console.log(`✅ Tag configuration applied successfully. ${appliedRulesCount} rules applied.`);\r\n\r\n    // Return the configuration so it can be used by the caller\r\n    return config;\r\n  } catch (error) {\r\n    console.error(\"❌ Error fetching or applying tag configuration:\", error);\r\n    console.log(\"📝 Form will load with default behavior (all visible, all editable)\");\r\n    // Don't throw the error - let the form load with default behavior\r\n    return null;\r\n  }\r\n};\r\n\r\n/**\r\n * Applies leadership roles configuration to survey panels for visibility rules\r\n * @param {Object} survey - The SurveyJS model instance\r\n * @param {Array} userLeadershipRoles - Current user's leadership roles\r\n * @param {Object} nameLeadershipRolesMap - Map of element names to their leadership roles\r\n * @returns {number} Number of panels processed\r\n */\r\nconst applyLeadershipRolesConfiguration = (survey, userLeadershipRoles, nameLeadershipRolesMap) => {\r\n  try {\r\n    // Validate inputs\r\n    if (!survey) {\r\n      console.warn(\"👤 Survey model is null, skipping leadership roles configuration\");\r\n      return 0;\r\n    }\r\n\r\n    if (!userLeadershipRoles) {\r\n      console.log(\"👤 No user leadership roles provided, treating as empty array\");\r\n      userLeadershipRoles = [];\r\n    }\r\n\r\n    console.log(\"👤 Applying leadership roles configuration...\");\r\n    console.log(\"👤 User leadership roles:\", userLeadershipRoles);\r\n    console.log(\"👤 Name-to-leadership-roles map:\", nameLeadershipRolesMap);\r\n\r\n    let processedPanelsCount = 0;\r\n\r\n    // Only apply to panels (not questions)\r\n    const panels = survey.getAllPanels();\r\n    console.log(`👤 Processing ${panels.length} panels for leadership roles rules`);\r\n\r\n    panels.forEach((panel) => {\r\n      // Skip leadership roles check if panel is already hidden by tag configuration\r\n      if (!panel.visible) {\r\n        console.log(`⏭️ Skipping panel ${panel.name} - already hidden by tag configuration`);\r\n        processedPanelsCount++;\r\n        return;\r\n      }\r\n\r\n      // Get leadership roles from the map or directly from the panel\r\n      const panelLeadershipRoles = nameLeadershipRolesMap[panel.name] || panel.leadershipRoles;\r\n\r\n      console.log(`🔍 Panel ${panel.name}: leadershipRoles=\"${panelLeadershipRoles}\"`);\r\n\r\n      if (panelLeadershipRoles) {\r\n        // Parse leadership roles (could be array or comma-separated string)\r\n        let requiredRoles = [];\r\n        if (Array.isArray(panelLeadershipRoles)) {\r\n          requiredRoles = panelLeadershipRoles;\r\n        } else if (typeof panelLeadershipRoles === \"string\") {\r\n          requiredRoles = panelLeadershipRoles\r\n            .split(\",\")\r\n            .map((role) => role.trim())\r\n            .filter((role) => role);\r\n        }\r\n\r\n        console.log(`👤 Panel ${panel.name} requires roles:`, requiredRoles);\r\n\r\n        if (requiredRoles.length > 0) {\r\n          // Normalize required roles for consistent comparison\r\n          const normalizedRequiredRoles = requiredRoles.map((role) => normalizeLeadershipRole(role));\r\n\r\n          // Check if user has any of the required leadership roles (both are normalized)\r\n          const hasRequiredRole = normalizedRequiredRoles.some((requiredRole) => userLeadershipRoles.some((userRole) => userRole === requiredRole));\r\n\r\n          if (!hasRequiredRole) {\r\n            // User doesn't have required leadership role, hide the panel\r\n            panel.visible = false;\r\n            console.log(`👁️ Hidden panel ${panel.name} - user lacks required leadership roles`);\r\n            console.log(`   Required (normalized): [${normalizedRequiredRoles.join(\", \")}]`);\r\n            console.log(`   User has (normalized): [${userLeadershipRoles.join(\", \")}]`);\r\n          } else {\r\n            console.log(`✅ Panel ${panel.name} visible - user has required leadership role`);\r\n          }\r\n        } else {\r\n          console.log(`📝 Panel ${panel.name} has empty leadership roles - visible to all`);\r\n        }\r\n      } else {\r\n        console.log(`📝 Panel ${panel.name} has no leadership roles restriction - visible to all`);\r\n      }\r\n\r\n      processedPanelsCount++;\r\n    });\r\n\r\n    console.log(`✅ Leadership roles configuration applied successfully. ${processedPanelsCount} panels processed.`);\r\n    return processedPanelsCount;\r\n  } catch (error) {\r\n    console.error(\"❌ Error applying leadership roles configuration:\", error);\r\n    console.log(\"📝 Form will load with default behavior (all panels visible)\");\r\n    // Don't throw the error - let the form load with default behavior\r\n    return 0;\r\n  }\r\n};\r\n\r\n/**\r\n * Applies service lines configuration to survey panels for visibility rules\r\n * @param {Object} survey - The SurveyJS model instance\r\n * @param {Array} formOwnerServiceLines - Form owner's service lines\r\n * @param {Array} formOwnerSubServiceLines - Form owner's sub-service lines\r\n * @param {Object} nameServiceLinesMap - Map of element names to their service lines\r\n * @param {Object} nameSubServiceLinesMap - Map of element names to their sub-service lines\r\n * @returns {number} Number of panels processed\r\n */\r\nconst applyServiceLinesConfiguration = (survey, formOwnerServiceLines, formOwnerSubServiceLines, nameServiceLinesMap, nameSubServiceLinesMap) => {\r\n  try {\r\n    // Validate inputs\r\n    if (!survey) {\r\n      console.warn(\"🏢 Survey model is null, skipping service lines configuration\");\r\n      return 0;\r\n    }\r\n\r\n    if (!formOwnerServiceLines) {\r\n      console.log(\"🏢 No form owner service lines provided, treating as empty array\");\r\n      formOwnerServiceLines = [];\r\n    }\r\n\r\n    if (!formOwnerSubServiceLines) {\r\n      console.log(\"🏢 No form owner sub-service lines provided, treating as empty array\");\r\n      formOwnerSubServiceLines = [];\r\n    }\r\n\r\n    console.log(\"🏢 Applying service lines configuration...\");\r\n    console.log(\"🏢 Form owner service lines:\", formOwnerServiceLines);\r\n    console.log(\"🏢 Form owner sub-service lines:\", formOwnerSubServiceLines);\r\n    console.log(\"🏢 Name-to-service-lines map:\", nameServiceLinesMap);\r\n    console.log(\"🏢 Name-to-sub-service-lines map:\", nameSubServiceLinesMap);\r\n\r\n    let processedPanelsCount = 0;\r\n\r\n    // Only apply to panels (not questions)\r\n    const panels = survey.getAllPanels();\r\n    console.log(`🏢 Found ${panels.length} panels to process`);\r\n\r\n    panels.forEach((panel) => {\r\n      // Skip if panel is already hidden (e.g., by tag configuration)\r\n      if (!panel.visible) {\r\n        console.log(`🏢 Panel ${panel.name} already hidden, skipping service lines check`);\r\n        processedPanelsCount++;\r\n        return;\r\n      }\r\n\r\n      // Get service lines and sub-service lines for this panel\r\n      const panelServiceLines = nameServiceLinesMap[panel.name] || panel.serviceLines;\r\n      const panelSubServiceLines = nameSubServiceLinesMap[panel.name] || panel.subServiceLines;\r\n\r\n      console.log(`🏢 Panel ${panel.name}:`);\r\n      console.log(`   Service lines: ${panelServiceLines}`);\r\n      console.log(`   Sub-service lines: ${panelSubServiceLines}`);\r\n\r\n      // Check service lines first\r\n      let hiddenByServiceLines = false;\r\n      if (panelServiceLines) {\r\n        // Handle both array and string formats for panelServiceLines\r\n        let requiredServiceLines = [];\r\n        if (Array.isArray(panelServiceLines)) {\r\n          requiredServiceLines = panelServiceLines.filter((sl) => sl && sl.trim() !== \"\");\r\n        } else if (typeof panelServiceLines === \"string\" && panelServiceLines.trim() !== \"\") {\r\n          requiredServiceLines = panelServiceLines\r\n            .split(\",\")\r\n            .map((sl) => sl.trim())\r\n            .filter((sl) => sl !== \"\");\r\n        }\r\n\r\n        if (requiredServiceLines.length > 0) {\r\n          // Check if form owner has any of the required service lines\r\n          const hasRequiredServiceLine = requiredServiceLines.some((requiredSL) =>\r\n            formOwnerServiceLines.some((ownerSL) => ownerSL.trim() === requiredSL)\r\n          );\r\n\r\n          if (!hasRequiredServiceLine) {\r\n            // Form owner doesn't have required service line, hide the panel\r\n            panel.visible = false;\r\n            hiddenByServiceLines = true;\r\n            console.log(`👁️ Hidden panel ${panel.name} - form owner lacks required service lines`);\r\n            console.log(`   Required: [${requiredServiceLines.join(\", \")}]`);\r\n            console.log(`   Form owner has: [${formOwnerServiceLines.join(\", \")}]`);\r\n          } else {\r\n            console.log(`✅ Panel ${panel.name} visible - form owner has required service line`);\r\n          }\r\n        }\r\n      }\r\n\r\n      // Check sub-service lines only if not already hidden by service lines\r\n      if (!hiddenByServiceLines && panelSubServiceLines) {\r\n        // Handle both array and string formats for panelSubServiceLines\r\n        let requiredSubServiceLines = [];\r\n        if (Array.isArray(panelSubServiceLines)) {\r\n          requiredSubServiceLines = panelSubServiceLines.filter((ssl) => ssl && ssl.trim() !== \"\");\r\n        } else if (typeof panelSubServiceLines === \"string\" && panelSubServiceLines.trim() !== \"\") {\r\n          requiredSubServiceLines = panelSubServiceLines\r\n            .split(\",\")\r\n            .map((ssl) => ssl.trim())\r\n            .filter((ssl) => ssl !== \"\");\r\n        }\r\n\r\n        if (requiredSubServiceLines.length > 0) {\r\n          // Check if form owner has any of the required sub-service lines\r\n          const hasRequiredSubServiceLine = requiredSubServiceLines.some((requiredSSL) =>\r\n            formOwnerSubServiceLines.some((ownerSSL) => ownerSSL.trim() === requiredSSL)\r\n          );\r\n\r\n          if (!hasRequiredSubServiceLine) {\r\n            // Form owner doesn't have required sub-service line, hide the panel\r\n            panel.visible = false;\r\n            console.log(`👁️ Hidden panel ${panel.name} - form owner lacks required sub-service lines`);\r\n            console.log(`   Required: [${requiredSubServiceLines.join(\", \")}]`);\r\n            console.log(`   Form owner has: [${formOwnerSubServiceLines.join(\", \")}]`);\r\n          } else {\r\n            console.log(`✅ Panel ${panel.name} visible - form owner has required sub-service line`);\r\n          }\r\n        }\r\n      }\r\n\r\n      // If both service lines and sub-service lines are empty, panel is visible to all\r\n      const hasServiceLinesRestriction =\r\n        panelServiceLines &&\r\n        ((Array.isArray(panelServiceLines) && panelServiceLines.length > 0) ||\r\n          (typeof panelServiceLines === \"string\" && panelServiceLines.trim() !== \"\"));\r\n      const hasSubServiceLinesRestriction =\r\n        panelSubServiceLines &&\r\n        ((Array.isArray(panelSubServiceLines) && panelSubServiceLines.length > 0) ||\r\n          (typeof panelSubServiceLines === \"string\" && panelSubServiceLines.trim() !== \"\"));\r\n\r\n      if (!hasServiceLinesRestriction && !hasSubServiceLinesRestriction) {\r\n        console.log(`📝 Panel ${panel.name} has no service lines restrictions - visible to all`);\r\n      }\r\n\r\n      processedPanelsCount++;\r\n    });\r\n\r\n    console.log(`✅ Service lines configuration applied successfully. ${processedPanelsCount} panels processed.`);\r\n    return processedPanelsCount;\r\n  } catch (error) {\r\n    console.error(\"❌ Error applying service lines configuration:\", error);\r\n    console.log(\"📝 Form will load with default behavior (all panels visible)\");\r\n    // Don't throw the error - let the form load with default behavior\r\n    return 0;\r\n  }\r\n};\r\n\r\n/**\r\n *\r\n * @param {*} year Work for partner click \"View My Plan\" from landing page.\r\n * @param {*} formId Work for Admin/ELT user click \"View\" icon button in \"Partner Annual Plans\" grid.\r\n * @param {*} backHandler handle Go back to previous page\r\n * @returns\r\n */\r\nexport const PartnerPlanQuestionnaire = ({ year, formId, backHandler }) => {\r\n  const [surveyModel, setSurveyModel] = useState(null);\r\n  const [questionnaire, setQuestionnaire] = useState(null);\r\n  const [form, setForm] = useState(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n  const [hasLoaded, setHasLoaded] = useState(false); // Track if data has been loaded\r\n  const [currentPageInfo, setCurrentPageInfo] = useState({ pageNo: 0, pageCount: 0, pageTitle: \"\" });\r\n  // Keep a live ref of form to avoid stale closures in SurveyJS handlers\r\n  const formRef = useRef(null);\r\n  useEffect(() => {\r\n    formRef.current = form;\r\n  }, [form]);\r\n\r\n  // State for reviewer comments dialog\r\n  const [showCommentsDialog, setShowCommentsDialog] = useState(false);\r\n  const [sendingBack, setSendingBack] = useState(false);\r\n\r\n  // State for current user's form role (from form access configuration)\r\n  const [currentUserFormRole, setCurrentUserFormRole] = useState(null);\r\n  const [formAccessConfig, setFormAccessConfig] = useState(null);\r\n\r\n  // State for reviewer comments history\r\n  const [reviewerComments, setReviewerComments] = useState([]);\r\n\r\n  // State for submission allowed flag\r\n  const [isSubmissionAllowed, setIsSubmissionAllowed] = useState(true);\r\n\r\n  // State for admin editing flag\r\n  const [isAdminEditing, setIsAdminEditing] = useState(false);\r\n\r\n  const authService = useContext(AuthContext);\r\n  const navigate = useNavigate();\r\n\r\n  // Disable loading interceptor for survey component\r\n  useLoadingControl(\"survey\", true);\r\n\r\n  // Determine scenario: MyPartnerPlan (year provided) vs PartnerPlan (formId provided)\r\n  const isMyPartnerPlan = !!year && !formId;\r\n  const isPartnerPlan = !!formId && !year;\r\n\r\n  // Reset hasLoaded when year or formId changes to trigger data reload\r\n  useEffect(() => {\r\n    setHasLoaded(false);\r\n  }, [year, formId]);\r\n\r\n  // Use useEffect directly instead of useCallback to avoid dependency issues\r\n  useEffect(() => {\r\n    // Prevent multiple calls if already loaded or not authenticated\r\n    if (hasLoaded || !authService || !authService.isAuthenticated()) {\r\n      return;\r\n    }\r\n\r\n    const loadData = async () => {\r\n      try {\r\n        setLoading(true);\r\n        setError(null);\r\n\r\n        let planData = null;\r\n\r\n        // Determine which API endpoint to call based on scenario\r\n        if (formId) {\r\n          // Scenario 2: Admin/ELT/Reviewer accessing other partner's plan\r\n          console.log(\"🔄 Loading partner plan data for formId:\", formId);\r\n          planData = await formService.getPartnerPlan(formId);\r\n        } else {\r\n          // Scenario 1: MyPartnerPlan - form owner accessing their own plan\r\n          const targetYear = year ? parseInt(year) : new Date().getFullYear();\r\n          console.log(\"🔄 Loading my plan data for year:\", targetYear);\r\n          planData = await formService.getMyPlan(targetYear);\r\n        }\r\n\r\n        if (planData) {\r\n          // Set all the data from the consolidated response\r\n          setQuestionnaire(planData.questionnaire);\r\n\r\n          // Set current user's form role from planData\r\n          if (planData.currentUserFormRole !== undefined) {\r\n            setCurrentUserFormRole(planData.currentUserFormRole);\r\n            console.log(\"👤 Current user form role:\", planData.currentUserFormRole);\r\n          }\r\n\r\n          // Set submission allowed flag from planData\r\n          if (planData.isSubmissionAllowed !== undefined) {\r\n            setIsSubmissionAllowed(planData.isSubmissionAllowed);\r\n            console.log(\"🔒 Submission allowed:\", planData.isSubmissionAllowed);\r\n          }\r\n\r\n          // Set admin editing flag from planData\r\n          if (planData.isAdminEditing !== undefined) {\r\n            setIsAdminEditing(planData.isAdminEditing);\r\n            console.log(\"👨‍💼 Admin editing:\", planData.isAdminEditing);\r\n          }\r\n\r\n          // Ensure form includes userAnswer to prevent null reference errors\r\n          const formWithUserAnswer = {\r\n            ...planData.form,\r\n            userAnswer: planData.userAnswer,\r\n          };\r\n          setForm(formWithUserAnswer);\r\n\r\n          // Fetch reviewer comments history for this form (only for MyPartnerPlan scenario)\r\n          if (formWithUserAnswer.id && !formId) {\r\n            try {\r\n              const comments = await formService.getReviewerCommentsHistory(formWithUserAnswer.id);\r\n              setReviewerComments(comments || []);\r\n            } catch (error) {\r\n              console.error(`Error fetching reviewer comments for form ${formWithUserAnswer.id}:`, error);\r\n              setReviewerComments([]);\r\n            }\r\n          }\r\n\r\n          // Get existing answers if available\r\n          const existingAnswers = planData.userAnswer?.answer;\r\n\r\n          // Parse the DefinitionJson to create the survey model\r\n          if (planData.questionnaire.definitionJson) {\r\n            try {\r\n              const surveyJson = JSON.parse(planData.questionnaire.definitionJson);\r\n\r\n              // Validate that the parsed JSON is a valid object\r\n              if (!surveyJson || typeof surveyJson !== \"object\") {\r\n                throw new Error(\"Invalid survey JSON: not a valid object\");\r\n              }\r\n\r\n              // Ensure the survey JSON has required properties\r\n              if (!surveyJson.pages && !surveyJson.elements) {\r\n                throw new Error(\"Invalid survey JSON: missing pages or elements\");\r\n              }\r\n\r\n              // Debug: Check if tag properties exist in the original JSON\r\n              console.log(\"🔍 Checking original survey JSON for tag properties...\");\r\n              if (surveyJson.pages) {\r\n                surveyJson.pages.forEach((page, pageIndex) => {\r\n                  if (page.elements) {\r\n                    page.elements.forEach((element, elementIndex) => {\r\n                      if (element.tag) {\r\n                        console.log(`🏷️ Found tag in JSON - ${element.type} \"${element.name}\": tag=\"${element.tag}\"`);\r\n                      }\r\n                      // Check nested elements (panels can contain questions)\r\n                      if (element.elements) {\r\n                        element.elements.forEach((nestedElement, nestedIndex) => {\r\n                          if (nestedElement.tag) {\r\n                            console.log(`🏷️ Found tag in nested JSON - ${nestedElement.type} \"${nestedElement.name}\": tag=\"${nestedElement.tag}\"`);\r\n                          }\r\n                        });\r\n                      }\r\n                    });\r\n                  }\r\n                });\r\n              }\r\n\r\n              // Configure Survey.js commercial license before creating survey model\r\n              configureSurveyJSLicense();\r\n\r\n              // Register custom properties BEFORE creating the Survey model to avoid race conditions\r\n              await registerCustomPropertiesForRuntime({ isDesigner: false });\r\n\r\n              const survey = new Model(surveyJson);\r\n              console.log(\"Survey model created:\", surveyJson);\r\n\r\n              // Validate that the survey was created successfully\r\n              if (!survey) {\r\n                throw new Error(\"Failed to create survey model\");\r\n              }\r\n\r\n              // Debug: Check if custom properties are available\r\n              const questions = survey.getAllQuestions();\r\n              const panels = survey.getAllPanels();\r\n\r\n              if (questions.length > 0) {\r\n                const firstQuestion = questions[0];\r\n              }\r\n\r\n              if (panels.length > 0) {\r\n                const firstPanel = panels[0];\r\n              }\r\n\r\n              // Validate that the survey has pages\r\n              if (!survey.pages || survey.pages.length === 0) {\r\n                throw new Error(\"Survey has no pages\");\r\n              }\r\n\r\n              // Load existing answers if available\r\n              if (existingAnswers) {\r\n                try {\r\n                  let parsedAnswers = JSON.parse(existingAnswers);\r\n                  // Handle legacy double-encoded payloads (JSON string inside JSON string)\r\n                  if (typeof parsedAnswers === \"string\") {\r\n                    try {\r\n                      parsedAnswers = JSON.parse(parsedAnswers);\r\n                    } catch {}\r\n                  }\r\n                  if (parsedAnswers && typeof parsedAnswers === \"object\") {\r\n                    survey.data = parsedAnswers;\r\n                  }\r\n                } catch (parseError) {\r\n                  console.warn(\"Failed to parse existing answers:\", parseError);\r\n                }\r\n              }\r\n\r\n              // Configure survey settings for wizard mode\r\n              survey.clearInvisibleValues = false;\r\n              // survey.allowShowPreview = true;\r\n              survey.showPreviewBeforeComplete = \"showAllQuestions\";\r\n              survey.showProgressBar = true;\r\n              survey.progressBarLocation = \"top\";\r\n              // survey.showQuestionNumbers = \"onPage\";\r\n              survey.questionsOnPageMode = \"standard\"; // Enable multi-page wizard mode\r\n              survey.showPageNumbers = false;\r\n              survey.showPageTitles = true;\r\n              survey.pagePrevText = \"Previous\";\r\n              survey.pageNextText = \"Next\";\r\n              survey.goNextPageAutomatic = false; // Don't auto-advance pages\r\n\r\n              //\r\n              // Check if form is editable. Updated to check FormAccessConfig matrix,\r\n              // if any of three panels are visible and not readonly, the form is editable for current user.\r\n              //\r\n              const formEditable = planData.isEditable;\r\n\r\n              if (!formEditable) {\r\n                // Set survey to read-only mode for non-editable forms (based on workflow rules and user roles)\r\n                survey.mode = \"display\";\r\n                survey.showPreviewBeforeComplete = \"noPreview\";\r\n              } else {\r\n                // Customize button text for editable forms\r\n                survey.completeText = \"Submit Partner Plan\";\r\n                survey.previewText = \"Preview\";\r\n                survey.editText = \"Previous\"; // Change Edit button text to Previous for clarity\r\n                // Ensure navigation buttons are shown in preview mode\r\n                survey.showPrevButton = true;\r\n                // Ensure preview mode shows navigation buttons\r\n                survey.showPreviewBeforeComplete = \"showAllQuestions\";\r\n                // survey.allowShowPreview = true;\r\n\r\n                // Hide complete button if submission is not allowed\r\n                if (!planData.isSubmissionAllowed) {\r\n                  survey.showCompleteButton = false;\r\n                  console.log(\"🚫 Complete button hidden - submission not allowed\");\r\n                }\r\n              }\r\n              // Add custom CSS classes\r\n              survey.css = {\r\n                ...survey.css,\r\n                root: `sv-root partner-plan-survey ${!formEditable ? \"survey-readonly\" : \"\"}`,\r\n                header: \"sv-header\",\r\n                body: \"sv-body\",\r\n                footer: \"sv-footer\",\r\n              };\r\n\r\n              // Apply modern theme for better styling\r\n              // Apply BDO red theme using modern Survey.js v2.2.2 approach\r\n              survey.applyTheme({\r\n                themeName: \"default-light\",\r\n                colorPalette: \"light\",\r\n                isPanelless: false,\r\n                // Custom CSS variables for BDO branding (equivalent to old StylesManager)\r\n                cssVariables: {\r\n                  \"--sjs-primary-backcolor\": \"#ED1A3B\", // Main color\r\n                  \"--sjs-primary-forecolor\": \"#FFFFFF\", // Text on primary\r\n                  \"--sjs-primary-backcolor-light\": \"#F5E6EA\", // Light variant\r\n                  \"--sjs-primary-backcolor-dark\": \"#AF273C\", // Hover color\r\n                  \"--sjs-secondary-backcolor\": \"#ED1A3B\", // Secondary color\r\n                  \"--sjs-secondary-forecolor\": \"#FFFFFF\", // Text on secondary\r\n                  \"--sjs-general-backcolor-dim\": \"#F3F2F1\", // Background\r\n                  \"--sjs-border-default\": \"#959597\", // Border color\r\n                  \"--sjs-border-light\": \"#D4D4D4\", // Light border\r\n                  \"--ctr-font-family\": \"primeicons\",\r\n                  \"--lbr-font-family\": \"primeicons\",\r\n                  \"--sjs-font-family\": \"primeicons\",\r\n                  \"--font-family\": \"primeicons\",\r\n                  \"--sjs-font-pagetitle-family\": \"primeicons\",\r\n                  \"--sjs-default-font-family\": \"primeicons\",\r\n\r\n                  \"--sjs-article-font-default\": \"24\",\r\n\r\n                  \"--sd-base-padding\": \"20px\",\r\n                  \"--sd-base-vertical-padding\": \"calc(1.4 * var(--sjs-base-unit, var(--base-unit, 8px)))\",\r\n                  \"--sd-page-vertical-padding\": \"calc(1.2 * var(--sjs-base-unit, var(--base-unit, 8px)))\",\r\n                },\r\n              });\r\n              survey.onTextMarkdown.add(function (survey, options) {\r\n                //convert the markdown text to html\r\n                var str = FormAnswerUtility.convertToHtml(options.text);\r\n                //set html\r\n                options.html = str;\r\n              });\r\n              // Only add completion handler for editable forms\r\n              if (formEditable) {\r\n                // Create a completion handler for form submission\r\n                const completionHandler = async (sender, options) => {\r\n                  // Prevent default completion\r\n                  options.allowComplete = false;\r\n\r\n                  // Show confirmation dialog\r\n                  messageService.confirmDialog(\r\n                    \"Are you sure you want to submit your partner plan? Once submitted, you may not be able to make changes.\",\r\n                    async (confirmed) => {\r\n                      if (!confirmed) {\r\n                        return;\r\n                      }\r\n\r\n                      try {\r\n                        // Save the final answers\r\n                        await formService.saveUserAnswer(planData.form.id, JSON.stringify(sender.data));\r\n\r\n                        // Submit the form using the new workflow-aware API\r\n                        const updatedForm = await formService.submitForm(planData.form.id);\r\n\r\n                        console.log(\"Survey completed and submitted successfully\");\r\n\r\n                        // Show success message with workflow information\r\n                        if (updatedForm && updatedForm.message) {\r\n                          messageService.successToast(updatedForm.message);\r\n                        } else {\r\n                          messageService.successToast(\"Partner plan submitted successfully!\");\r\n                        }\r\n\r\n                        // Update form status with the actual returned status from the workflow\r\n                        if (updatedForm) {\r\n                          setForm((prev) => ({\r\n                            ...prev,\r\n                            status: updatedForm.status,\r\n                            statusString: getFormStatusName(updatedForm.status),\r\n                          }));\r\n                        }\r\n\r\n                        // Complete the survey\r\n                        sender.doComplete();\r\n                      } catch (error) {\r\n                        console.error(\"Error handling survey completion:\", error);\r\n                        messageService.errorToast(error.message || \"Failed to submit partner plan. Please try again.\");\r\n                      }\r\n                    }\r\n                  );\r\n                };\r\n\r\n                // Set up survey completion handler\r\n                survey.onComplete.add(completionHandler);\r\n              }\r\n\r\n              // Only add auto-save for editable forms\r\n              if (formEditable) {\r\n                // Set up immediate save on value change (following Retirement project pattern)\r\n                survey.onValueChanged.add(surveyValueChanged);\r\n              }\r\n\r\n              // Set up page change handler for wizard navigation\r\n              survey.onCurrentPageChanged.add((sender, options) => {\r\n                handlePageChanged(sender, options);\r\n\r\n                // Check if we're on the last page and submission is not allowed\r\n                if (!planData.isSubmissionAllowed && planData.currentUserFormRole === UserFormRole.FormOwner) {\r\n                  const isLastPage = sender.isLastPage;\r\n                  if (isLastPage) {\r\n                    // Add a message to the last page about reviewer not being assigned\r\n                    const lastPage = sender.currentPage;\r\n                    if (lastPage && !lastPage.hasCustomMessage) {\r\n                      // Create a custom HTML element to show the message\r\n                      const submissionMessage = planData.submissionNotAllowedMessage;\r\n\r\n                      // Only render the message if submissionMessage is not null or empty\r\n                      if (submissionMessage && submissionMessage.trim() !== \"\") {\r\n                        const messageHtml = `\r\n                          <div style=\"background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px; padding: 15px; margin: 20px 0; color: #856404;\">\r\n                            <div style=\"display: flex; flex-direction: column; align-items: center; text-align: center;\">\r\n                              <div style=\"display: flex; align-items: center; margin-bottom: 10px;\">\r\n                                <i class=\"pi pi-exclamation-triangle\" style=\"font-size: 1.2rem; margin-right: 10px; color: #f39c12;\"></i>\r\n                                <strong>Unable to Submit Partner Plan</strong>\r\n                              </div>\r\n                              <p style=\"margin: 0;\">${submissionMessage}</p>\r\n                            </div>\r\n                          </div>\r\n                        `;\r\n\r\n                        // Add the message as HTML content to the page\r\n                        if (!lastPage.description) {\r\n                          lastPage.description = messageHtml;\r\n                        } else {\r\n                          lastPage.description = lastPage.description + messageHtml;\r\n                        }\r\n                        lastPage.hasCustomMessage = true;\r\n                      }\r\n                    }\r\n                  }\r\n                }\r\n              });\r\n\r\n              // Add page validation handler\r\n              survey.onCurrentPageChanging.add((_, options) => {\r\n                // You can add custom validation logic here if needed\r\n                console.log(`Navigating from page ${options.oldCurrentPage?.name} to ${options.newCurrentPage?.name}`);\r\n              });\r\n\r\n              // Add preview mode handler to ensure navigation buttons are shown\r\n              // Note: onShowPreview might not be available in all SurveyJS versions\r\n              if (survey.onShowPreview) {\r\n                survey.onShowPreview.add((sender) => {\r\n                  console.log(\"Survey entered preview mode\");\r\n                  // Ensure navigation buttons are visible in preview mode\r\n                  sender.showNavigationButtons = \"both\";\r\n                  sender.showPrevButton = true;\r\n                });\r\n              }\r\n\r\n              // Set completion message\r\n              survey.completedHtml = `<h3>Thank you for completing ${survey.title || planData.questionnaire.name}!</h3>`;\r\n\r\n              // Build the name-to-tag map from the raw survey JSON\r\n              const nameTagMap = {};\r\n              if (surveyJson.pages) {\r\n                surveyJson.pages.forEach((page) => {\r\n                  buildNameTagMap(page.elements, nameTagMap);\r\n                });\r\n              }\r\n              console.log(\"🏷️ Name-to-tag map created:\", nameTagMap);\r\n\r\n              // Apply tag-based visibility and readonly rules using configuration from planData\r\n              const formAccessConfig = applyTagConfiguration(survey, planData.formTagConfiguration, nameTagMap);\r\n\r\n              // Set form access config for other uses\r\n              if (formAccessConfig) {\r\n                setFormAccessConfig(formAccessConfig);\r\n              }\r\n\r\n              // Build the name-to-leadership-roles map from the raw survey JSON\r\n              const nameLeadershipRolesMap = {};\r\n              if (surveyJson.pages) {\r\n                surveyJson.pages.forEach((page) => {\r\n                  buildNameLeadershipRolesMap(page.elements, nameLeadershipRolesMap);\r\n                });\r\n              }\r\n              console.log(\"👤 Name-to-leadership-roles map created:\", nameLeadershipRolesMap);\r\n\r\n              // Apply leadership roles-based visibility rules using user's leadership roles from planData\r\n              // Only apply if tag configuration hasn't already hidden the panels\r\n              applyLeadershipRolesConfiguration(survey, planData.leadershipRoles, nameLeadershipRolesMap);\r\n\r\n              // Build the name-to-service-lines maps from the raw survey JSON\r\n              const nameServiceLinesMap = {};\r\n              const nameSubServiceLinesMap = {};\r\n              if (surveyJson.pages) {\r\n                surveyJson.pages.forEach((page) => {\r\n                  buildNameServiceLinesMap(page.elements, nameServiceLinesMap);\r\n                  buildNameSubServiceLinesMap(page.elements, nameSubServiceLinesMap);\r\n                });\r\n              }\r\n              console.log(\"🏢 Name-to-service-lines map created:\", nameServiceLinesMap);\r\n              console.log(\"🏢 Name-to-sub-service-lines map created:\", nameSubServiceLinesMap);\r\n\r\n              // Apply service lines-based visibility rules using form owner's service lines from planData\r\n              // Only apply if tag configuration and leadership roles haven't already hidden the panels\r\n              applyServiceLinesConfiguration(\r\n                survey,\r\n                planData.serviceLines || [],\r\n                planData.subServiceLines || [],\r\n                nameServiceLinesMap,\r\n                nameSubServiceLinesMap\r\n              );\r\n\r\n              // Set up partner data auto-population for dropdowns with mapFromGroup\r\n              await setupPartnerReferenceDataAutoPopulation(survey, planData.partnerId, planData.questionnaire.year);\r\n\r\n              setSurveyModel(survey);\r\n\r\n              // Initialize page info for wizard\r\n              if (survey.pageCount > 1) {\r\n                const visiblePageIndex = survey.visiblePages.indexOf(survey.currentPage);\r\n                const processedTitle = survey.getProcessedText(survey.currentPage?.title || survey.currentPage?.name);\r\n\r\n                setCurrentPageInfo({\r\n                  pageNo: visiblePageIndex > -1 ? visiblePageIndex : 0,\r\n                  pageCount: survey.visiblePages.length,\r\n                  pageTitle: processedTitle,\r\n                });\r\n              }\r\n\r\n              setHasLoaded(true); // Mark as loaded\r\n            } catch (parseError) {\r\n              console.error(\"Error parsing survey JSON:\", parseError);\r\n              setError(\"Failed to load survey form. Invalid survey definition.\");\r\n            }\r\n          } else {\r\n            setError(\"No survey definition found for the current year.\");\r\n          }\r\n        } else {\r\n          setError(planData?.message || \"Failed to load partner plan data.\");\r\n        }\r\n      } catch (err) {\r\n        console.error(\"Error loading my plan:\", err);\r\n        setError(err.message || \"Failed to load partner plan. Please try again later.\");\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    loadData();\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [hasLoaded, year, formId]); // Depend on hasLoaded, year, and formId to reload when parameters change\r\n\r\n  // Handle return to home navigation\r\n  const handleReturnToHome = useCallback(() => {\r\n    navigate(\"/home\");\r\n  }, [navigate]);\r\n\r\n  // Handle Send Back to Partner button click\r\n  const handleSendBackToPartner = () => {\r\n    setShowCommentsDialog(true);\r\n  };\r\n\r\n  // Handle reviewer comments dialog confirm\r\n  const handleCommentsConfirm = async (comments) => {\r\n    if (!form?.id) return;\r\n\r\n    setSendingBack(true);\r\n    try {\r\n      await formService.sendBackToPartner(form.id, comments);\r\n      messageService.successDialog(\"Form sent back to partner successfully\");\r\n\r\n      // Close dialog\r\n      setShowCommentsDialog(false);\r\n\r\n      // Navigate back or refresh\r\n      if (backHandler) {\r\n        backHandler();\r\n      } else {\r\n        navigate(\"/home\");\r\n      }\r\n    } catch (error) {\r\n      messageService.errorDialog(error.message || \"Failed to send form back to partner\");\r\n    } finally {\r\n      setSendingBack(false);\r\n    }\r\n  };\r\n\r\n  // Handle Submit Admin Update button click\r\n  const handleSubmitAdminUpdate = async () => {\r\n    if (!form?.id) return;\r\n\r\n    // Show confirmation dialog\r\n    const confirmed = window.confirm(\"Are you sure you want to submit this admin update? This action will update the form status.\");\r\n\r\n    if (!confirmed) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // Save the current answers first\r\n      if (surveyModel?.data) {\r\n        await formService.saveUserAnswer(form.id, JSON.stringify(surveyModel.data));\r\n      }\r\n\r\n      // Submit the form using the same endpoint as the regular submit\r\n      const updatedForm = await formService.submitForm(form.id);\r\n\r\n      console.log(\"Admin update submitted successfully\");\r\n\r\n      // Show success message\r\n      if (updatedForm && updatedForm.message) {\r\n        messageService.successToast(updatedForm.message);\r\n      } else {\r\n        messageService.successToast(\"Admin update submitted successfully!\");\r\n      }\r\n\r\n      // Update form status with the actual returned status\r\n      if (updatedForm) {\r\n        setForm((prev) => ({\r\n          ...prev,\r\n          status: updatedForm.status,\r\n          statusString: getFormStatusName(updatedForm.status),\r\n        }));\r\n      }\r\n\r\n      // Navigate back or refresh\r\n      if (backHandler) {\r\n        backHandler();\r\n      } else {\r\n        navigate(\"/home\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error submitting admin update:\", error);\r\n      messageService.errorToast(error.message || \"Failed to submit admin update. Please try again.\");\r\n    }\r\n  };\r\n\r\n  // Handle reviewer comments dialog hide\r\n  const handleCommentsDialogHide = () => {\r\n    if (!sendingBack) {\r\n      setShowCommentsDialog(false);\r\n    }\r\n  };\r\n\r\n  // Check if current user can send form back to partner\r\n  const canSendBackToPartner = () => {\r\n    if (!form) return false;\r\n\r\n    // Check if current user is a reviewer based on the form access configuration\r\n    if (currentUserFormRole !== UserFormRole.Reviewer) {\r\n      return false;\r\n    }\r\n\r\n    // Check if form is in \"Under Review\" status for MidYear or YearEnd cycles\r\n    return form.status === FormStatus.MidYearReviewUnderReview || form.status === FormStatus.YearEndReviewUnderReview;\r\n  };\r\n\r\n  //\r\n  // Handle PDF export with Survey.js build function.\r\n  //\r\n  const handleExportToPDF = useCallback(async () => {\r\n    await PDFExportUtilities.handleExportToPDF(surveyModel, form);\r\n  }, [surveyModel, form]);\r\n\r\n  // Alternative PDF export method using browser's print functionality\r\n  const handleAlternativePDFExport = useCallback(() => {\r\n    PDFExportUtilities.handleAlternativePDFExport(surveyModel, form);\r\n  }, [surveyModel, form]);\r\n\r\n  const handlePageChanged = (sender, options) => {\r\n    // Update page information for better UX\r\n    if (sender && sender.currentPage) {\r\n      const visiblePageIndex = sender.visiblePages.indexOf(sender.currentPage);\r\n\r\n      const processedTitle = sender.currentPage.getProcessedText(sender.currentPage?.title || sender.currentPage?.name);\r\n\r\n      const pageInfo = {\r\n        pageNo: visiblePageIndex > -1 ? visiblePageIndex : 0,\r\n        pageCount: sender.visiblePages.length,\r\n        pageTitle: processedTitle,\r\n      };\r\n      setCurrentPageInfo(pageInfo);\r\n    }\r\n  };\r\n\r\n  // Save questionnaire data without submitting (use backend AutoSave API)\r\n  const saveQuestionnaire = async (callback, dataOverride = null) => {\r\n    try {\r\n      const currentForm = formRef.current;\r\n      if (!currentForm) {\r\n        // Form not ready yet; skip this autosave quietly\r\n        callback && callback();\r\n        return;\r\n      }\r\n\r\n      const answers = dataOverride ?? surveyModel?.data ?? {};\r\n\r\n      // Call auto-save endpoint which will create/update UserAnswer and set form status server-side\r\n      await formService.autoSaveUserAnswer(currentForm.id, answers);\r\n    } catch (e) {\r\n      console.warn(\"Auto-save failed:\", e);\r\n    } finally {\r\n      callback && callback();\r\n    }\r\n  };\r\n\r\n  // Survey value changed handler - immediate save (following Retirement project pattern)\r\n  function surveyValueChanged(sender, options) {\r\n    console.log(\"Survey value changed:\", options);\r\n\r\n    // Save immediately using the values from the sender (always up-to-date)\r\n    saveQuestionnaire(undefined, sender?.data);\r\n  }\r\n\r\n  // Admin reopen function following Retirement project pattern\r\n  const adminReopen = () => {\r\n    function returnForm(confirmed) {\r\n      if (!confirmed) {\r\n        return;\r\n      }\r\n      http\r\n        .put(APP_CONFIG.apiDomain + \"/api/Form/AdminReopenForms\", [form.id], {\r\n          headers: {\r\n            \"Content-Type\": \"application/json\",\r\n          },\r\n        })\r\n        .then((response) => {\r\n          // popup success if OK\r\n          if (response.data.resultStatus === 1) {\r\n            messageService.successDialog(\"Questionnaire reopened.\");\r\n            backHandler && backHandler();\r\n          } else {\r\n            messageService.errorDialog(response.data.message);\r\n          }\r\n        })\r\n        .catch(() => {\r\n          messageService.errorDialog(\"Failed to reopen questionnaire.\");\r\n        });\r\n    }\r\n\r\n    const surveyTitle = questionnaire ? questionnaire.name : \"Partner Plan\";\r\n    const partnerName = form ? form.partnerName : \"Partner\";\r\n    messageService.confirmDialog(`Are you sure to reopen the ${surveyTitle} to ${partnerName}`, returnForm);\r\n  };\r\n\r\n  // Render reviewer comments history section (only for form owners)\r\n  const renderReviewerCommentsHistory = () => {\r\n    // Only show for form owners and when there are comments to display\r\n    if (currentUserFormRole !== UserFormRole.FormOwner || !reviewerComments || reviewerComments.length === 0) {\r\n      return null;\r\n    }\r\n\r\n    return (\r\n      <div className=\"reviewer-comments-section\">\r\n        <Panel header=\"Reviewer Comments History\" toggleable collapsed={true} className=\"reviewer-comments-panel\">\r\n          <div className=\"comments-list\">\r\n            {reviewerComments.map((comment, index) => (\r\n              <div\r\n                key={comment.id || index}\r\n                className=\"comment-item\"\r\n                style={{\r\n                  marginBottom: index < reviewerComments.length - 1 ? \"1rem\" : \"0\",\r\n                  padding: \"1rem\",\r\n                  border: \"1px solid #e0e0e0\",\r\n                  borderRadius: \"4px\",\r\n                  backgroundColor: \"#f9f9f9\",\r\n                }}\r\n              >\r\n                <div\r\n                  className=\"comment-header\"\r\n                  style={{\r\n                    display: \"flex\",\r\n                    justifyContent: \"space-between\",\r\n                    alignItems: \"center\",\r\n                    marginBottom: \"0.5rem\",\r\n                    fontSize: \"0.9rem\",\r\n                    color: \"#666\",\r\n                  }}\r\n                >\r\n                  <span className=\"reviewer-name\">\r\n                    <strong>{comment.submittedByName || \"Reviewer\"}</strong>\r\n                  </span>\r\n                  <span className=\"comment-date\">{comment.submittedOn ? formatDateTime(comment.submittedOn) : \"Date not available\"}</span>\r\n                </div>\r\n                <div\r\n                  className=\"comment-cycle\"\r\n                  style={{\r\n                    fontSize: \"0.8rem\",\r\n                    color: \"#888\",\r\n                    marginBottom: \"0.5rem\",\r\n                  }}\r\n                >\r\n                  <em>{comment.cycleDescription} Cycle</em>\r\n                </div>\r\n                <div\r\n                  className=\"comment-text\"\r\n                  style={{\r\n                    lineHeight: \"1.5\",\r\n                    whiteSpace: \"pre-wrap\",\r\n                  }}\r\n                >\r\n                  {comment.comments}\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </Panel>\r\n      </div>\r\n    );\r\n  };\r\n\r\n  if (!authService || !authService.isAuthenticated()) {\r\n    return (\r\n      <div className=\"survey-container\">\r\n        <h2>Partner Plans</h2>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"survey-container\">\r\n        <div className=\"p-4\">\r\n          <h2>Partner Plans</h2>\r\n          <p>Loading plan...</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <div id=\"surveyContainer\">\r\n        <div>\r\n          <div className=\"p-message p-message-error\">\r\n            <div className=\"p-message-wrapper\">\r\n              <span className=\"p-message-icon pi pi-times-circle\"></span>\r\n              <div className=\"p-message-text\">\r\n                <span className=\"p-message-summary\">Error</span>\r\n                <div className=\"p-message-detail\">{error}</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <button className=\"p-button p-component p-button-outlined mt-3\" onClick={handleReturnToHome}>\r\n            <span className=\"p-button-icon pi pi-arrow-left\"></span>\r\n            <span className=\"p-button-label\">Back</span>\r\n          </button>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (!surveyModel) {\r\n    return (\r\n      <div className=\"survey-container\">\r\n        <div className=\"p-4\">\r\n          <h2>Partner Plans</h2>\r\n          <p>No plan available at this time.</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"survey-container\">\r\n      {/* Header Section */}\r\n      <div className=\"partner-planning-header\">\r\n        <div className=\"header-content\">\r\n          <div className=\"bdo-logo\">\r\n            <img\r\n              alt=\"BDO logo\"\r\n              src={`${APP_CONFIG.basePath}/logo.png`}\r\n              onError={(e) => (e.target.src = `/images/BDO.png`)}\r\n              className=\"bdo-logo-image\"\r\n            />\r\n            <span className=\"partner-planning-text\">\r\n              {formId\r\n                ? `${\r\n                    form?.year || questionnaire?.year || year ? `${form?.year || questionnaire?.year || year}` : \"\"\r\n                  } Partner Planning Tool - Review Mode`\r\n                : `${form?.year || questionnaire?.year || year ? `${form?.year || questionnaire?.year || year}` : \"\"} Partner Planning Tool`}\r\n            </span>\r\n          </div>\r\n          <div className=\"header-actions\">\r\n            {/* Submit Admin Update button - only visible when IsAdminEditing is true */}\r\n            {isAdminEditing && (\r\n              <button\r\n                className=\"submit-admin-update-btn\"\r\n                onClick={handleSubmitAdminUpdate}\r\n                style={{\r\n                  backgroundColor: \"#28a745\",\r\n                  color: \"white\",\r\n                  border: \"none\",\r\n                  padding: \"8px 16px\",\r\n                  borderRadius: \"4px\",\r\n                  cursor: \"pointer\",\r\n                  marginRight: \"10px\",\r\n                  fontSize: \"14px\",\r\n                  fontWeight: \"600\",\r\n                }}\r\n              >\r\n                <i className=\"pi pi-check\" style={{ marginRight: \"5px\" }}></i>\r\n                Submit Admin Update\r\n              </button>\r\n            )}\r\n\r\n            {/* Send Back to Partner button - only visible for reviewers when form is Under Review */}\r\n            {canSendBackToPartner() && (\r\n              <button\r\n                className=\"send-back-btn\"\r\n                onClick={handleSendBackToPartner}\r\n                style={{\r\n                  backgroundColor: \"#f39c12\",\r\n                  color: \"white\",\r\n                  border: \"none\",\r\n                  padding: \"8px 16px\",\r\n                  borderRadius: \"4px\",\r\n                  cursor: \"pointer\",\r\n                  marginRight: \"10px\",\r\n                  fontSize: \"14px\",\r\n                }}\r\n              >\r\n                <i className=\"pi pi-send\" style={{ marginRight: \"5px\" }}></i>\r\n                Send Back to Partner\r\n              </button>\r\n            )}\r\n\r\n            {/* <button className=\"export-pdf-btn\" onClick={handleExportToPDF}>\r\n                <i className=\"pi pi-file-pdf\"></i>\r\n                Export to PDF\r\n              </button>  */}\r\n            <button className=\"export-pdf-btn\" onClick={handleAlternativePDFExport} style={{ marginLeft: \"10px\" }}>\r\n              <i className=\"pi pi-print\"></i>\r\n              Print/Export to PDF\r\n            </button>\r\n            <button className=\"return-home-btn\" onClick={handleReturnToHome}>\r\n              Return to Home\r\n            </button>\r\n          </div>\r\n        </div>\r\n        <div className=\"form-description\">\r\n          This form guides partners in creating a personal plan to achieve the firm's {year} strategy. Complete all sections to set qualitative and\r\n          quantitative targets.\r\n        </div>\r\n      </div>\r\n\r\n      {/* Admin Modification Audit History - Collapsible section at the top */}\r\n      {formId && (\r\n        <div style={{ marginBottom: \"1rem\" }}>\r\n          <AdminModificationAuditHistory formId={formId} visible={true} />\r\n        </div>\r\n      )}\r\n\r\n      {/* Partner Details Section - Collapsible, only for Form Owners */}\r\n      {currentUserFormRole === UserFormRole.FormOwner && form && (\r\n        <div className=\"partner-details-section\" style={{ marginBottom: \"1rem\" }}>\r\n          <Panel header=\"Partner Details\" toggleable collapsed={false} className=\"partner-details-panel\">\r\n            <div className=\"partner-details-table\">\r\n              <div className=\"partner-details-row\">\r\n                <div className=\"detail-item\">\r\n                  <span className=\"detail-label\">Partner Name:</span>\r\n                  <span className=\"detail-value\">{form?.partnerName || \"\"}</span>\r\n                </div>\r\n                <div className=\"detail-item\">\r\n                  <span className=\"detail-label\">Service Line:</span>\r\n                  <span className=\"detail-value\">{form?.serviceLine || \"\"}</span>\r\n                </div>\r\n                <div className=\"detail-item\">\r\n                  <span className=\"detail-label\">Sub-service Line:</span>\r\n                  <span className=\"detail-value\">{form?.subServiceLine || \"\"}</span>\r\n                </div>\r\n                <div className=\"detail-item\">\r\n                  <span className=\"detail-label\">Location:</span>\r\n                  <span className=\"detail-value\">{form?.location || \"\"}</span>\r\n                </div>\r\n                <div className=\"detail-item\">\r\n                  <span className=\"detail-label\">Primary Reviewer:</span>\r\n                  <span className=\"detail-value\">{form?.primaryReviewerName || \"\"}</span>\r\n                </div>\r\n                <div className=\"detail-item\">\r\n                  <span className=\"detail-label\">Secondary Reviewer:</span>\r\n                  <span className=\"detail-value\">{form?.secondaryReviewerName || \"\"}</span>\r\n                </div>\r\n                <div className=\"status-container\">\r\n                  <div className=\"status-badge\">\r\n                    <span className=\"status-text\">STATUS: </span>\r\n                    <span className={`status-value ${getFormStatusClass(form?.status)}`}>\r\n                      {form?.statusString || getFormStatusName(form?.status) || \"DRAFT\"}\r\n                    </span>\r\n                  </div>\r\n                  <div className=\"autosaved-container\">\r\n                    {form?.partnerSubmittionDate ? (\r\n                      <div className=\"autosaved-info\">\r\n                        Autosaved: {new Date(form.partnerSubmittionDate).toLocaleDateString()} at{\" \"}\r\n                        {new Date(form.partnerSubmittionDate).toLocaleTimeString()}\r\n                      </div>\r\n                    ) : (\r\n                      <div className=\"autosaved-info\">{/* Show blank when no autosave date available */}</div>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            {/* Reviewer Comments History Section - only for Form Owners */}\r\n            {renderReviewerCommentsHistory()}\r\n          </Panel>\r\n        </div>\r\n      )}\r\n      {/* Wizard Progress Indicator. Temporarily hidden no need at this moment. */}\r\n      {/* {surveyModel && currentPageInfo.pageCount > 1 && (\r\n        <div className=\"wizard-progress-info\">\r\n          <div className=\"progress-text\">\r\n            <span className=\"current-step\">Step {currentPageInfo.pageNo + 1}</span>\r\n            <span className=\"total-steps\"> of {currentPageInfo.pageCount}</span>\r\n            {currentPageInfo.pageTitle && <span className=\"page-title\">: {currentPageInfo.pageTitle}</span>}\r\n          </div>\r\n        </div>\r\n      )} */}\r\n\r\n      {/* Partner Information Section - only show for non-Form Owners (Reviewers, Admins, ELT) */}\r\n      {currentUserFormRole !== UserFormRole.FormOwner && form && (\r\n        <div\r\n          className=\"partner-info-section\"\r\n          style={{\r\n            background: \"#f8f9fa\",\r\n            padding: \"1rem\",\r\n            marginBottom: \"1rem\",\r\n            borderRadius: \"4px\",\r\n          }}\r\n        >\r\n          <h3>Partner Information</h3>\r\n          <div style={{ display: \"grid\", gridTemplateColumns: \"repeat(auto-fit, minmax(250px, 1fr))\", gap: \"1rem\" }}>\r\n            <div>\r\n              <strong>Partner Name:</strong> {form.partnerName || \"N/A\"}\r\n            </div>\r\n            <div>\r\n              <strong>Service Line:</strong> {form.serviceLine || \"N/A\"}\r\n            </div>\r\n            <div>\r\n              <strong>Sub-Service Line:</strong> {form.subServiceLine || \"N/A\"}\r\n            </div>\r\n            <div>\r\n              <strong>Location:</strong> {form.location || \"N/A\"}\r\n            </div>\r\n            <div>\r\n              <strong>Primary Reviewer:</strong> {form.primaryReviewerName || \"N/A\"}\r\n            </div>\r\n            <div>\r\n              <strong>Secondary Reviewer:</strong> {form.secondaryReviewerName || \"N/A\"}\r\n            </div>\r\n            <div>\r\n              <strong>Status:</strong> {form.statusString || getFormStatusName(form.status) || \"DRAFT\"}\r\n            </div>\r\n            <div>\r\n              <strong>Year:</strong> {form.year || \"N/A\"}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      <div className=\"survey-wrapper mt-1\">\r\n        <Survey model={surveyModel} />\r\n      </div>\r\n\r\n      {/* Auto-save indicator - only show for editable forms */}\r\n      {/* {form && isFormEditableByOwner(form.status) && (\r\n          <div className=\"auto-save-indicator mt-2\">\r\n            <small className=\"text-muted\">\r\n              <i className=\"pi pi-info-circle mr-1\"></i>\r\n              Your progress is automatically saved as you work\r\n            </small>\r\n          </div>\r\n        )} */}\r\n\r\n      {/* <div className=\"flexbox flexbox--flex-end\" style={{marginTop: \"30px\", marginRight:\"17px\"}}>\r\n          {user_role === 'PPAdministrator' && (\r\n            <>\r\n              <Button className=\"action\"\r\n                disabled={!(form && form.status !== FormStatus.PlanningDraft && form.status !== FormStatus.PlanningReopened \r\n                  && form.status !== FormStatus.MidYearReviewDraft && form.status !== FormStatus.MidYearReviewReopened\r\n                  && form.status !== FormStatus.YearEndReviewDraft && form.status !== FormStatus.YearEndReviewReopened)}\r\n                label=\"Reopen\"\r\n                style={{ padding: '15px 20px' }}\r\n                onClick={adminReopen} />\r\n            </>\r\n          )}\r\n          {backHandler && (\r\n            <>\r\n              <Button className=\"action\"\r\n                label=\"Exit\"\r\n                style={{ padding: '15px 20px', marginLeft: '10px'}}\r\n                onClick={() => {backHandler();}} />\r\n            </>\r\n          )}\r\n        </div> */}\r\n\r\n      {/* Reviewer Comments Dialog, when Reviewer clicks \"Send Back to Partner\", \r\n      this popup modal displays and asks for comments */}\r\n      <ReviewerCommentsDialog\r\n        visible={showCommentsDialog}\r\n        onHide={handleCommentsDialogHide}\r\n        onConfirm={handleCommentsConfirm}\r\n        loading={sendingBack}\r\n        title=\"Send Back to Partner\"\r\n        message={`Please provide comments explaining why the form for ${form?.partnerName || \"this partner\"} needs to be revised:`}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\n/**\r\n * Set up partner data auto-population for dropdowns with mapFromGroup\r\n * @param {Object} survey - The SurveyJS model instance\r\n * @param {string} partnerId - Partner ID for data retrieval\r\n * @param {number} year - Year for data retrieval\r\n */\r\nasync function setupPartnerReferenceDataAutoPopulation(survey, partnerId, year) {\r\n  console.log(\"🔧 setupPartnerReferenceDataAutoPopulation called with:\", { partnerId, year });\r\n\r\n  // Find all text questions with auto-population enabled (legacy and cycle-specific)\r\n  const autoPopulateFields = survey.getAllQuestions().filter((q) => {\r\n    if (q.getType() !== \"text\") return false;\r\n\r\n    // Check for any linkedToGroup[Cycle] property (legacy or cycle-specific)\r\n    return q.linkedToGroupPlanning || q.linkedToGroupMidYear || q.linkedToGroupYearEnd;\r\n  });\r\n\r\n  console.log(\r\n    \"📝 Found auto-populate text fields:\",\r\n    autoPopulateFields.map((f) => ({\r\n      name: f.name,\r\n      linkedToPlanning: f.linkedToGroupPlanning,\r\n      linkedToMidYear: f.linkedToGroupMidYear,\r\n      linkedToYearEnd: f.linkedToGroupYearEnd,\r\n    }))\r\n  );\r\n\r\n  // Create mapping of dropdown -> text fields with cycle information\r\n  const dropdownToTextMapping = {};\r\n  autoPopulateFields.forEach((textField) => {\r\n    // Determine which linkedToGroup[Cycle] property to use and the corresponding cycle\r\n    let dropdownName;\r\n    let cycle;\r\n\r\n    if (textField.linkedToGroupPlanning) {\r\n      dropdownName = textField.linkedToGroupPlanning;\r\n      cycle = PartnerPlanCycle.Planning;\r\n    } else if (textField.linkedToGroupMidYear) {\r\n      dropdownName = textField.linkedToGroupMidYear;\r\n      cycle = PartnerPlanCycle.MidYearReview;\r\n    } else if (textField.linkedToGroupYearEnd) {\r\n      dropdownName = textField.linkedToGroupYearEnd;\r\n      cycle = PartnerPlanCycle.YearEndReview;\r\n    }\r\n\r\n    if (dropdownName) {\r\n      if (!dropdownToTextMapping[dropdownName]) {\r\n        dropdownToTextMapping[dropdownName] = [];\r\n      }\r\n      // Store both the text field and its associated cycle\r\n      dropdownToTextMapping[dropdownName].push({ textField, cycle });\r\n    }\r\n  });\r\n\r\n  // Debug: Log all questions first\r\n  const allQuestions = survey.getAllQuestions();\r\n  console.log(\r\n    \"🔍 All questions in survey:\",\r\n    allQuestions.map((q) => ({\r\n      name: q.name,\r\n      type: q.getType(),\r\n      mapFromGroup: q.mapFromGroup,\r\n      hasMapFromGroup: !!q.mapFromGroup,\r\n    }))\r\n  );\r\n\r\n  // Set up dynamic choice loading for dropdowns with mapFromGroup\r\n  const mapFromGroupDropdowns = survey.getAllQuestions().filter((q) => q.getType() === \"dropdown\" && q.mapFromGroup);\r\n  console.log(\r\n    \"🎯 Found dropdowns with mapFromGroup:\",\r\n    mapFromGroupDropdowns.map((d) => ({ name: d.name, group: d.mapFromGroup }))\r\n  );\r\n\r\n  // Load choices for mapFromGroup dropdowns\r\n  for (const dropdown of mapFromGroupDropdowns) {\r\n    try {\r\n      console.log(`🌐 Calling API: getColumnsByGroup(${year}, \"${dropdown.mapFromGroup}\") for dropdown \"${dropdown.name}\"`);\r\n      const columns = await partnerReferenceDataUploadService.getColumnsByGroup(year, dropdown.mapFromGroup);\r\n      dropdown.choices = columns;\r\n      console.log(`✅ Loaded ${columns.length} choices for dropdown ${dropdown.name}:`, columns);\r\n    } catch (error) {\r\n      console.error(`❌ Error loading choices for dropdown ${dropdown.name}:`, error);\r\n    }\r\n  }\r\n\r\n  // Set up value change handlers\r\n  survey.onValueChanged.add(async (sender, options) => {\r\n    await handlePartnerReferenceDataAutoPopulation(sender, options, dropdownToTextMapping, partnerId, year);\r\n  });\r\n\r\n  // Set up initial population for existing values\r\n  Object.keys(dropdownToTextMapping).forEach((dropdownName) => {\r\n    const dropdown = survey.getQuestionByName(dropdownName);\r\n    if (dropdown && dropdown.value) {\r\n      // Populate on initial load if dropdown already has a value\r\n      handlePartnerReferenceDataAutoPopulation(survey, { name: dropdownName, value: dropdown.value }, dropdownToTextMapping, partnerId, year);\r\n    }\r\n  });\r\n}\r\n\r\n/**\r\n * Handle partner data auto-population when dropdown values change\r\n * @param {Object} sender - The survey instance\r\n * @param {Object} options - The value change options\r\n * @param {Object} dropdownToTextMapping - Mapping of dropdown names to text fields with cycle info\r\n * @param {string} partnerId - Partner ID for data retrieval\r\n * @param {number} year - Year for data retrieval\r\n */\r\nasync function handlePartnerReferenceDataAutoPopulation(sender, options, dropdownToTextMapping, partnerId, year) {\r\n  const dropdownName = options.name;\r\n  const selectedColumn = options.value;\r\n\r\n  // Check if this dropdown has linked text fields\r\n  const linkedTextFieldsWithCycles = dropdownToTextMapping[dropdownName];\r\n  if (!linkedTextFieldsWithCycles || linkedTextFieldsWithCycles.length === 0) {\r\n    return;\r\n  }\r\n\r\n  const dropdown = sender.getQuestionByName(dropdownName);\r\n  if (!dropdown || !dropdown.mapFromGroup) {\r\n    return;\r\n  }\r\n\r\n  try {\r\n    if (!selectedColumn) {\r\n      // Clear linked text fields when no selection\r\n      linkedTextFieldsWithCycles.forEach(({ textField }) => {\r\n        sender.setValue(textField.name, \"\");\r\n      });\r\n      return;\r\n    }\r\n\r\n    // Show loading state in text fields\r\n    linkedTextFieldsWithCycles.forEach(({ textField }) => {\r\n      sender.setValue(textField.name, \"Loading...\");\r\n    });\r\n\r\n    // Process each text field with its specific cycle\r\n    for (const { textField, cycle } of linkedTextFieldsWithCycles) {\r\n      try {\r\n        // Fetch partner reference data value with the text field's specific cycle\r\n        const dataValue = await partnerReferenceDataUploadService.getPartnerReferenceDataValue(\r\n          year,\r\n          cycle,\r\n          dropdown.mapFromGroup,\r\n          selectedColumn,\r\n          partnerId\r\n        );\r\n\r\n        // Update the text field with the fetched value\r\n        const displayValue = dataValue !== null && dataValue !== undefined ? String(dataValue) : \"No data available\";\r\n        sender.setValue(textField.name, displayValue);\r\n\r\n        console.log(`Auto-populated ${textField.name} with value: ${displayValue} (cycle: ${cycle})`);\r\n      } catch (error) {\r\n        console.error(`Error fetching data for ${textField.name} (cycle: ${cycle}):`, error);\r\n        sender.setValue(textField.name, \"Error loading data\");\r\n      }\r\n    }\r\n  } catch (error) {\r\n    console.error(\"Error in partner data auto-population:\", error);\r\n\r\n    // Show error state in all text fields\r\n    linkedTextFieldsWithCycles.forEach(({ textField }) => {\r\n      sender.setValue(textField.name, \"Error loading data\");\r\n    });\r\n  }\r\n}\r\n\r\nexport default PartnerPlanQuestionnaire;\r\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,UAAU,EAAEC,WAAW,EAAEC,MAAM,QAAQ,OAAO;AAC5E,SAASC,MAAM,QAAQ,iBAAiB;AACxC,SAASC,KAAK,QAAQ,aAAa;AACnC,SAASC,wBAAwB,QAAQ,mCAAmC;AAE5E,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,QAAQ,yCAAyC;AACrE,SAASC,cAAc,QAAQ,mCAAmC;AAClE,SAASC,iBAAiB,QAAQ,4CAA4C;AAC9E,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,iCAAiC,MAAM,kDAAkD;AAChG,SAASC,UAAU,EAAEC,iBAAiB,EAAEC,kBAAkB,EAAEC,qBAAqB,QAAQ,mCAAmC;AAC5H,SAASC,YAAY,QAAQ,qCAAqC;AAClE,SAASC,gBAAgB,QAAQ,yCAAyC;AAC1E,OAAOC,IAAI,MAAM,4BAA4B;AAC7C,OAAOC,UAAU,MAAM,6BAA6B;AACpD,OAAOC,iBAAiB,MAAM,uBAAuB;AACrD,OAAOC,kBAAkB,MAAM,sBAAsB;AACrD,OAAOC,sBAAsB,MAAM,kCAAkC;AACrE,OAAOC,6BAA6B,MAAM,wCAAwC;AAClF,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,cAAc,QAAQ,4BAA4B;AAC3D,SAASC,kCAAkC,QAAQ,8CAA8C;;AAEjG;AACA,OAAO,6BAA6B;AACpC,OAAO,4BAA4B,CAAC,CAAC;AACrC,OAAO,gCAAgC,CAAC,CAAC;;AAEzC;AACA;AACA;AACA;AACA;AAJA,SAAAC,MAAA,IAAAC,OAAA;AAKA,MAAMC,eAAe,GAAGA,CAACC,QAAQ,EAAEC,GAAG,KAAK;EACzC,IAAI,CAACD,QAAQ,IAAI,CAACE,KAAK,CAACC,OAAO,CAACH,QAAQ,CAAC,EAAE;IACzC;EACF;EAEAA,QAAQ,CAACI,OAAO,CAAEC,OAAO,IAAK;IAC5B,IAAIA,OAAO,CAACC,IAAI,IAAID,OAAO,CAACE,GAAG,EAAE;MAC/BN,GAAG,CAACI,OAAO,CAACC,IAAI,CAAC,GAAGD,OAAO,CAACE,GAAG;IACjC;;IAEA;IACA,IAAIF,OAAO,CAACL,QAAQ,EAAE;MACpBD,eAAe,CAACM,OAAO,CAACL,QAAQ,EAAEC,GAAG,CAAC;IACxC;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,MAAMO,2BAA2B,GAAGA,CAACR,QAAQ,EAAEC,GAAG,KAAK;EACrD,IAAI,CAACD,QAAQ,IAAI,CAACE,KAAK,CAACC,OAAO,CAACH,QAAQ,CAAC,EAAE;IACzC;EACF;EAEAA,QAAQ,CAACI,OAAO,CAAEC,OAAO,IAAK;IAC5B,IAAIA,OAAO,CAACC,IAAI,IAAID,OAAO,CAACI,eAAe,EAAE;MAC3CR,GAAG,CAACI,OAAO,CAACC,IAAI,CAAC,GAAGD,OAAO,CAACI,eAAe;IAC7C;;IAEA;IACA,IAAIJ,OAAO,CAACL,QAAQ,EAAE;MACpBQ,2BAA2B,CAACH,OAAO,CAACL,QAAQ,EAAEC,GAAG,CAAC;IACpD;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,MAAMS,wBAAwB,GAAGA,CAACV,QAAQ,EAAEC,GAAG,KAAK;EAClD,IAAI,CAACD,QAAQ,IAAI,CAACE,KAAK,CAACC,OAAO,CAACH,QAAQ,CAAC,EAAE;IACzC;EACF;EAEAA,QAAQ,CAACI,OAAO,CAAEC,OAAO,IAAK;IAC5B,IAAIA,OAAO,CAACC,IAAI,IAAID,OAAO,CAACM,YAAY,EAAE;MACxCV,GAAG,CAACI,OAAO,CAACC,IAAI,CAAC,GAAGD,OAAO,CAACM,YAAY;IAC1C;;IAEA;IACA,IAAIN,OAAO,CAACL,QAAQ,EAAE;MACpBU,wBAAwB,CAACL,OAAO,CAACL,QAAQ,EAAEC,GAAG,CAAC;IACjD;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,MAAMW,2BAA2B,GAAGA,CAACZ,QAAQ,EAAEC,GAAG,KAAK;EACrD,IAAI,CAACD,QAAQ,IAAI,CAACE,KAAK,CAACC,OAAO,CAACH,QAAQ,CAAC,EAAE;IACzC;EACF;EAEAA,QAAQ,CAACI,OAAO,CAAEC,OAAO,IAAK;IAC5B,IAAIA,OAAO,CAACC,IAAI,IAAID,OAAO,CAACQ,eAAe,EAAE;MAC3CZ,GAAG,CAACI,OAAO,CAACC,IAAI,CAAC,GAAGD,OAAO,CAACQ,eAAe;IAC7C;;IAEA;IACA,IAAIR,OAAO,CAACL,QAAQ,EAAE;MACpBY,2BAA2B,CAACP,OAAO,CAACL,QAAQ,EAAEC,GAAG,CAAC;IACpD;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,MAAMa,uBAAuB,GAAIC,IAAI,IAAK;EACxC,IAAI,CAACA,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IACrC,OAAO,EAAE;EACX;EACA;EACA,OAAOA,IAAI,CAACC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;AAClC,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,qBAAqB,GAAGA,CAACC,MAAM,EAAEC,MAAM,EAAEC,UAAU,KAAK;EAC5D,IAAI;IACF;IACA,IAAI,CAACF,MAAM,EAAE;MACXG,OAAO,CAACC,IAAI,CAAC,sDAAsD,CAAC;MACpE,OAAO,IAAI;IACb;IAEA,IAAI,CAACH,MAAM,EAAE;MACXE,OAAO,CAACE,GAAG,CAAC,+EAA+E,CAAC;MAC5F,OAAO,IAAI;IACb;IAEAF,OAAO,CAACE,GAAG,CAAC,wCAAwC,EAAEJ,MAAM,CAAC;;IAE7D;IACA,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;MAC9BE,OAAO,CAACC,IAAI,CAAC,wEAAwE,CAAC;MACtF,OAAO,IAAI;IACb;IAEA,IAAIE,iBAAiB,GAAG,CAAC;IAEzB,MAAMC,oBAAoB,GAAGA,CAAC1B,QAAQ,EAAE2B,WAAW,KAAK;MACtDL,OAAO,CAACE,GAAG,CAAC,kBAAkBxB,QAAQ,CAAC4B,MAAM,IAAID,WAAW,gBAAgB,CAAC;MAC7E3B,QAAQ,CAACI,OAAO,CAAEC,OAAO,IAAK;QAC5B,MAAMwB,OAAO,GAAGR,UAAU,CAAChB,OAAO,CAACC,IAAI,CAAC,IAAID,OAAO,CAACE,GAAG;QACvDe,OAAO,CAACE,GAAG,CAAC,MAAMG,WAAW,IAAItB,OAAO,CAACC,IAAI,UAAUuB,OAAO,GAAG,CAAC;QAClE,IAAIA,OAAO,EAAE;UACX,MAAMC,SAAS,GAAGC,MAAM,CAACC,IAAI,CAACZ,MAAM,CAAC,CAACa,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACC,WAAW,CAAC,CAAC,KAAKN,OAAO,CAACM,WAAW,CAAC,CAAC,CAAC;UAC5F,IAAIL,SAAS,IAAIV,MAAM,CAACU,SAAS,CAAC,EAAE;YAClC,MAAMM,SAAS,GAAGhB,MAAM,CAACU,SAAS,CAAC;YAEnC,IAAI,OAAOM,SAAS,CAACC,QAAQ,KAAK,SAAS,EAAE;cAC3ChC,OAAO,CAACgC,QAAQ,GAAGD,SAAS,CAACC,QAAQ;cACrCf,OAAO,CAACE,GAAG,CAAC,uBAAuBY,SAAS,CAACC,QAAQ,OAAOV,WAAW,IAAItB,OAAO,CAACC,IAAI,aAAauB,OAAO,EAAE,CAAC;cAC9GJ,iBAAiB,EAAE;YACrB;YAEA,IAAI,OAAOW,SAAS,CAACE,OAAO,KAAK,SAAS,EAAE;cAC1CjC,OAAO,CAACiC,OAAO,GAAGF,SAAS,CAACE,OAAO;cACnChB,OAAO,CAACE,GAAG,CAAC,uBAAuBY,SAAS,CAACE,OAAO,OAAOX,WAAW,IAAItB,OAAO,CAACC,IAAI,aAAauB,OAAO,EAAE,CAAC;cAC7GJ,iBAAiB,EAAE;YACrB;UACF;QACF;MACF,CAAC,CAAC;IACJ,CAAC;IAEDC,oBAAoB,CAACP,MAAM,CAACoB,eAAe,CAAC,CAAC,EAAE,UAAU,CAAC;IAC1Db,oBAAoB,CAACP,MAAM,CAACqB,YAAY,CAAC,CAAC,EAAE,OAAO,CAAC;IAEpDlB,OAAO,CAACE,GAAG,CAAC,6CAA6CC,iBAAiB,iBAAiB,CAAC;;IAE5F;IACA,OAAOL,MAAM;EACf,CAAC,CAAC,OAAOqB,KAAK,EAAE;IACdnB,OAAO,CAACmB,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;IACvEnB,OAAO,CAACE,GAAG,CAAC,qEAAqE,CAAC;IAClF;IACA,OAAO,IAAI;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMkB,iCAAiC,GAAGA,CAACvB,MAAM,EAAEwB,mBAAmB,EAAEC,sBAAsB,KAAK;EACjG,IAAI;IACF;IACA,IAAI,CAACzB,MAAM,EAAE;MACXG,OAAO,CAACC,IAAI,CAAC,kEAAkE,CAAC;MAChF,OAAO,CAAC;IACV;IAEA,IAAI,CAACoB,mBAAmB,EAAE;MACxBrB,OAAO,CAACE,GAAG,CAAC,+DAA+D,CAAC;MAC5EmB,mBAAmB,GAAG,EAAE;IAC1B;IAEArB,OAAO,CAACE,GAAG,CAAC,+CAA+C,CAAC;IAC5DF,OAAO,CAACE,GAAG,CAAC,2BAA2B,EAAEmB,mBAAmB,CAAC;IAC7DrB,OAAO,CAACE,GAAG,CAAC,kCAAkC,EAAEoB,sBAAsB,CAAC;IAEvE,IAAIC,oBAAoB,GAAG,CAAC;;IAE5B;IACA,MAAMC,MAAM,GAAG3B,MAAM,CAACqB,YAAY,CAAC,CAAC;IACpClB,OAAO,CAACE,GAAG,CAAC,iBAAiBsB,MAAM,CAAClB,MAAM,oCAAoC,CAAC;IAE/EkB,MAAM,CAAC1C,OAAO,CAAE2C,KAAK,IAAK;MACxB;MACA,IAAI,CAACA,KAAK,CAACT,OAAO,EAAE;QAClBhB,OAAO,CAACE,GAAG,CAAC,qBAAqBuB,KAAK,CAACzC,IAAI,wCAAwC,CAAC;QACpFuC,oBAAoB,EAAE;QACtB;MACF;;MAEA;MACA,MAAMG,oBAAoB,GAAGJ,sBAAsB,CAACG,KAAK,CAACzC,IAAI,CAAC,IAAIyC,KAAK,CAACtC,eAAe;MAExFa,OAAO,CAACE,GAAG,CAAC,YAAYuB,KAAK,CAACzC,IAAI,sBAAsB0C,oBAAoB,GAAG,CAAC;MAEhF,IAAIA,oBAAoB,EAAE;QACxB;QACA,IAAIC,aAAa,GAAG,EAAE;QACtB,IAAI/C,KAAK,CAACC,OAAO,CAAC6C,oBAAoB,CAAC,EAAE;UACvCC,aAAa,GAAGD,oBAAoB;QACtC,CAAC,MAAM,IAAI,OAAOA,oBAAoB,KAAK,QAAQ,EAAE;UACnDC,aAAa,GAAGD,oBAAoB,CACjCE,KAAK,CAAC,GAAG,CAAC,CACVjD,GAAG,CAAEc,IAAI,IAAKA,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,CAC1BmC,MAAM,CAAEpC,IAAI,IAAKA,IAAI,CAAC;QAC3B;QAEAO,OAAO,CAACE,GAAG,CAAC,YAAYuB,KAAK,CAACzC,IAAI,kBAAkB,EAAE2C,aAAa,CAAC;QAEpE,IAAIA,aAAa,CAACrB,MAAM,GAAG,CAAC,EAAE;UAC5B;UACA,MAAMwB,uBAAuB,GAAGH,aAAa,CAAChD,GAAG,CAAEc,IAAI,IAAKD,uBAAuB,CAACC,IAAI,CAAC,CAAC;;UAE1F;UACA,MAAMsC,eAAe,GAAGD,uBAAuB,CAACE,IAAI,CAAEC,YAAY,IAAKZ,mBAAmB,CAACW,IAAI,CAAEE,QAAQ,IAAKA,QAAQ,KAAKD,YAAY,CAAC,CAAC;UAEzI,IAAI,CAACF,eAAe,EAAE;YACpB;YACAN,KAAK,CAACT,OAAO,GAAG,KAAK;YACrBhB,OAAO,CAACE,GAAG,CAAC,oBAAoBuB,KAAK,CAACzC,IAAI,yCAAyC,CAAC;YACpFgB,OAAO,CAACE,GAAG,CAAC,8BAA8B4B,uBAAuB,CAACK,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;YAChFnC,OAAO,CAACE,GAAG,CAAC,8BAA8BmB,mBAAmB,CAACc,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;UAC9E,CAAC,MAAM;YACLnC,OAAO,CAACE,GAAG,CAAC,WAAWuB,KAAK,CAACzC,IAAI,8CAA8C,CAAC;UAClF;QACF,CAAC,MAAM;UACLgB,OAAO,CAACE,GAAG,CAAC,YAAYuB,KAAK,CAACzC,IAAI,8CAA8C,CAAC;QACnF;MACF,CAAC,MAAM;QACLgB,OAAO,CAACE,GAAG,CAAC,YAAYuB,KAAK,CAACzC,IAAI,uDAAuD,CAAC;MAC5F;MAEAuC,oBAAoB,EAAE;IACxB,CAAC,CAAC;IAEFvB,OAAO,CAACE,GAAG,CAAC,0DAA0DqB,oBAAoB,oBAAoB,CAAC;IAC/G,OAAOA,oBAAoB;EAC7B,CAAC,CAAC,OAAOJ,KAAK,EAAE;IACdnB,OAAO,CAACmB,KAAK,CAAC,kDAAkD,EAAEA,KAAK,CAAC;IACxEnB,OAAO,CAACE,GAAG,CAAC,8DAA8D,CAAC;IAC3E;IACA,OAAO,CAAC;EACV;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMkC,8BAA8B,GAAGA,CAACvC,MAAM,EAAEwC,qBAAqB,EAAEC,wBAAwB,EAAEC,mBAAmB,EAAEC,sBAAsB,KAAK;EAC/I,IAAI;IACF;IACA,IAAI,CAAC3C,MAAM,EAAE;MACXG,OAAO,CAACC,IAAI,CAAC,+DAA+D,CAAC;MAC7E,OAAO,CAAC;IACV;IAEA,IAAI,CAACoC,qBAAqB,EAAE;MAC1BrC,OAAO,CAACE,GAAG,CAAC,kEAAkE,CAAC;MAC/EmC,qBAAqB,GAAG,EAAE;IAC5B;IAEA,IAAI,CAACC,wBAAwB,EAAE;MAC7BtC,OAAO,CAACE,GAAG,CAAC,sEAAsE,CAAC;MACnFoC,wBAAwB,GAAG,EAAE;IAC/B;IAEAtC,OAAO,CAACE,GAAG,CAAC,4CAA4C,CAAC;IACzDF,OAAO,CAACE,GAAG,CAAC,8BAA8B,EAAEmC,qBAAqB,CAAC;IAClErC,OAAO,CAACE,GAAG,CAAC,kCAAkC,EAAEoC,wBAAwB,CAAC;IACzEtC,OAAO,CAACE,GAAG,CAAC,+BAA+B,EAAEqC,mBAAmB,CAAC;IACjEvC,OAAO,CAACE,GAAG,CAAC,mCAAmC,EAAEsC,sBAAsB,CAAC;IAExE,IAAIjB,oBAAoB,GAAG,CAAC;;IAE5B;IACA,MAAMC,MAAM,GAAG3B,MAAM,CAACqB,YAAY,CAAC,CAAC;IACpClB,OAAO,CAACE,GAAG,CAAC,YAAYsB,MAAM,CAAClB,MAAM,oBAAoB,CAAC;IAE1DkB,MAAM,CAAC1C,OAAO,CAAE2C,KAAK,IAAK;MACxB;MACA,IAAI,CAACA,KAAK,CAACT,OAAO,EAAE;QAClBhB,OAAO,CAACE,GAAG,CAAC,YAAYuB,KAAK,CAACzC,IAAI,+CAA+C,CAAC;QAClFuC,oBAAoB,EAAE;QACtB;MACF;;MAEA;MACA,MAAMkB,iBAAiB,GAAGF,mBAAmB,CAACd,KAAK,CAACzC,IAAI,CAAC,IAAIyC,KAAK,CAACpC,YAAY;MAC/E,MAAMqD,oBAAoB,GAAGF,sBAAsB,CAACf,KAAK,CAACzC,IAAI,CAAC,IAAIyC,KAAK,CAAClC,eAAe;MAExFS,OAAO,CAACE,GAAG,CAAC,YAAYuB,KAAK,CAACzC,IAAI,GAAG,CAAC;MACtCgB,OAAO,CAACE,GAAG,CAAC,qBAAqBuC,iBAAiB,EAAE,CAAC;MACrDzC,OAAO,CAACE,GAAG,CAAC,yBAAyBwC,oBAAoB,EAAE,CAAC;;MAE5D;MACA,IAAIC,oBAAoB,GAAG,KAAK;MAChC,IAAIF,iBAAiB,EAAE;QACrB;QACA,IAAIG,oBAAoB,GAAG,EAAE;QAC7B,IAAIhE,KAAK,CAACC,OAAO,CAAC4D,iBAAiB,CAAC,EAAE;UACpCG,oBAAoB,GAAGH,iBAAiB,CAACZ,MAAM,CAAEgB,EAAE,IAAKA,EAAE,IAAIA,EAAE,CAACnD,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC;QACjF,CAAC,MAAM,IAAI,OAAO+C,iBAAiB,KAAK,QAAQ,IAAIA,iBAAiB,CAAC/C,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;UACnFkD,oBAAoB,GAAGH,iBAAiB,CACrCb,KAAK,CAAC,GAAG,CAAC,CACVjD,GAAG,CAAEkE,EAAE,IAAKA,EAAE,CAACnD,IAAI,CAAC,CAAC,CAAC,CACtBmC,MAAM,CAAEgB,EAAE,IAAKA,EAAE,KAAK,EAAE,CAAC;QAC9B;QAEA,IAAID,oBAAoB,CAACtC,MAAM,GAAG,CAAC,EAAE;UACnC;UACA,MAAMwC,sBAAsB,GAAGF,oBAAoB,CAACZ,IAAI,CAAEe,UAAU,IAClEV,qBAAqB,CAACL,IAAI,CAAEgB,OAAO,IAAKA,OAAO,CAACtD,IAAI,CAAC,CAAC,KAAKqD,UAAU,CACvE,CAAC;UAED,IAAI,CAACD,sBAAsB,EAAE;YAC3B;YACArB,KAAK,CAACT,OAAO,GAAG,KAAK;YACrB2B,oBAAoB,GAAG,IAAI;YAC3B3C,OAAO,CAACE,GAAG,CAAC,oBAAoBuB,KAAK,CAACzC,IAAI,4CAA4C,CAAC;YACvFgB,OAAO,CAACE,GAAG,CAAC,iBAAiB0C,oBAAoB,CAACT,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;YAChEnC,OAAO,CAACE,GAAG,CAAC,uBAAuBmC,qBAAqB,CAACF,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;UACzE,CAAC,MAAM;YACLnC,OAAO,CAACE,GAAG,CAAC,WAAWuB,KAAK,CAACzC,IAAI,iDAAiD,CAAC;UACrF;QACF;MACF;;MAEA;MACA,IAAI,CAAC2D,oBAAoB,IAAID,oBAAoB,EAAE;QACjD;QACA,IAAIO,uBAAuB,GAAG,EAAE;QAChC,IAAIrE,KAAK,CAACC,OAAO,CAAC6D,oBAAoB,CAAC,EAAE;UACvCO,uBAAuB,GAAGP,oBAAoB,CAACb,MAAM,CAAEqB,GAAG,IAAKA,GAAG,IAAIA,GAAG,CAACxD,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC;QAC1F,CAAC,MAAM,IAAI,OAAOgD,oBAAoB,KAAK,QAAQ,IAAIA,oBAAoB,CAAChD,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;UACzFuD,uBAAuB,GAAGP,oBAAoB,CAC3Cd,KAAK,CAAC,GAAG,CAAC,CACVjD,GAAG,CAAEuE,GAAG,IAAKA,GAAG,CAACxD,IAAI,CAAC,CAAC,CAAC,CACxBmC,MAAM,CAAEqB,GAAG,IAAKA,GAAG,KAAK,EAAE,CAAC;QAChC;QAEA,IAAID,uBAAuB,CAAC3C,MAAM,GAAG,CAAC,EAAE;UACtC;UACA,MAAM6C,yBAAyB,GAAGF,uBAAuB,CAACjB,IAAI,CAAEoB,WAAW,IACzEd,wBAAwB,CAACN,IAAI,CAAEqB,QAAQ,IAAKA,QAAQ,CAAC3D,IAAI,CAAC,CAAC,KAAK0D,WAAW,CAC7E,CAAC;UAED,IAAI,CAACD,yBAAyB,EAAE;YAC9B;YACA1B,KAAK,CAACT,OAAO,GAAG,KAAK;YACrBhB,OAAO,CAACE,GAAG,CAAC,oBAAoBuB,KAAK,CAACzC,IAAI,gDAAgD,CAAC;YAC3FgB,OAAO,CAACE,GAAG,CAAC,iBAAiB+C,uBAAuB,CAACd,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;YACnEnC,OAAO,CAACE,GAAG,CAAC,uBAAuBoC,wBAAwB,CAACH,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;UAC5E,CAAC,MAAM;YACLnC,OAAO,CAACE,GAAG,CAAC,WAAWuB,KAAK,CAACzC,IAAI,qDAAqD,CAAC;UACzF;QACF;MACF;;MAEA;MACA,MAAMsE,0BAA0B,GAC9Bb,iBAAiB,KACf7D,KAAK,CAACC,OAAO,CAAC4D,iBAAiB,CAAC,IAAIA,iBAAiB,CAACnC,MAAM,GAAG,CAAC,IAC/D,OAAOmC,iBAAiB,KAAK,QAAQ,IAAIA,iBAAiB,CAAC/C,IAAI,CAAC,CAAC,KAAK,EAAG,CAAC;MAC/E,MAAM6D,6BAA6B,GACjCb,oBAAoB,KAClB9D,KAAK,CAACC,OAAO,CAAC6D,oBAAoB,CAAC,IAAIA,oBAAoB,CAACpC,MAAM,GAAG,CAAC,IACrE,OAAOoC,oBAAoB,KAAK,QAAQ,IAAIA,oBAAoB,CAAChD,IAAI,CAAC,CAAC,KAAK,EAAG,CAAC;MAErF,IAAI,CAAC4D,0BAA0B,IAAI,CAACC,6BAA6B,EAAE;QACjEvD,OAAO,CAACE,GAAG,CAAC,YAAYuB,KAAK,CAACzC,IAAI,qDAAqD,CAAC;MAC1F;MAEAuC,oBAAoB,EAAE;IACxB,CAAC,CAAC;IAEFvB,OAAO,CAACE,GAAG,CAAC,uDAAuDqB,oBAAoB,oBAAoB,CAAC;IAC5G,OAAOA,oBAAoB;EAC7B,CAAC,CAAC,OAAOJ,KAAK,EAAE;IACdnB,OAAO,CAACmB,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;IACrEnB,OAAO,CAACE,GAAG,CAAC,8DAA8D,CAAC;IAC3E;IACA,OAAO,CAAC;EACV;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMsD,wBAAwB,GAAGA,CAAC;EAAEC,IAAI;EAAEC,MAAM;EAAEC;AAAY,CAAC,KAAK;EAAAC,EAAA;EACzE,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGpH,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACqH,aAAa,EAAEC,gBAAgB,CAAC,GAAGtH,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACuH,IAAI,EAAEC,OAAO,CAAC,GAAGxH,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACyH,OAAO,EAAEC,UAAU,CAAC,GAAG1H,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACyE,KAAK,EAAEkD,QAAQ,CAAC,GAAG3H,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC4H,SAAS,EAAEC,YAAY,CAAC,GAAG7H,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACnD,MAAM,CAAC8H,eAAe,EAAEC,kBAAkB,CAAC,GAAG/H,QAAQ,CAAC;IAAEgI,MAAM,EAAE,CAAC;IAAEC,SAAS,EAAE,CAAC;IAAEC,SAAS,EAAE;EAAG,CAAC,CAAC;EAClG;EACA,MAAMC,OAAO,GAAG/H,MAAM,CAAC,IAAI,CAAC;EAC5BH,SAAS,CAAC,MAAM;IACdkI,OAAO,CAACC,OAAO,GAAGb,IAAI;EACxB,CAAC,EAAE,CAACA,IAAI,CAAC,CAAC;;EAEV;EACA,MAAM,CAACc,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGtI,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACuI,WAAW,EAAEC,cAAc,CAAC,GAAGxI,QAAQ,CAAC,KAAK,CAAC;;EAErD;EACA,MAAM,CAACyI,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG1I,QAAQ,CAAC,IAAI,CAAC;EACpE,MAAM,CAAC2I,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5I,QAAQ,CAAC,IAAI,CAAC;;EAE9D;EACA,MAAM,CAAC6I,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG9I,QAAQ,CAAC,EAAE,CAAC;;EAE5D;EACA,MAAM,CAAC+I,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGhJ,QAAQ,CAAC,IAAI,CAAC;;EAEpE;EACA,MAAM,CAACiJ,cAAc,EAAEC,iBAAiB,CAAC,GAAGlJ,QAAQ,CAAC,KAAK,CAAC;EAE3D,MAAMmJ,WAAW,GAAGjJ,UAAU,CAACO,WAAW,CAAC;EAC3C,MAAM2I,QAAQ,GAAG5I,WAAW,CAAC,CAAC;;EAE9B;EACAG,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAAC;;EAEjC;EACA,MAAM0I,eAAe,GAAG,CAAC,CAACtC,IAAI,IAAI,CAACC,MAAM;EACzC,MAAMsC,aAAa,GAAG,CAAC,CAACtC,MAAM,IAAI,CAACD,IAAI;;EAEvC;EACA9G,SAAS,CAAC,MAAM;IACd4H,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC,EAAE,CAACd,IAAI,EAAEC,MAAM,CAAC,CAAC;;EAElB;EACA/G,SAAS,CAAC,MAAM;IACd;IACA,IAAI2H,SAAS,IAAI,CAACuB,WAAW,IAAI,CAACA,WAAW,CAACI,eAAe,CAAC,CAAC,EAAE;MAC/D;IACF;IAEA,MAAMC,QAAQ,GAAG,MAAAA,CAAA,KAAY;MAC3B,IAAI;QACF9B,UAAU,CAAC,IAAI,CAAC;QAChBC,QAAQ,CAAC,IAAI,CAAC;QAEd,IAAI8B,QAAQ,GAAG,IAAI;;QAEnB;QACA,IAAIzC,MAAM,EAAE;UACV;UACA1D,OAAO,CAACE,GAAG,CAAC,0CAA0C,EAAEwD,MAAM,CAAC;UAC/DyC,QAAQ,GAAG,MAAM7I,WAAW,CAAC8I,cAAc,CAAC1C,MAAM,CAAC;QACrD,CAAC,MAAM;UACL;UACA,MAAM2C,UAAU,GAAG5C,IAAI,GAAG6C,QAAQ,CAAC7C,IAAI,CAAC,GAAG,IAAI8C,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;UACnExG,OAAO,CAACE,GAAG,CAAC,mCAAmC,EAAEmG,UAAU,CAAC;UAC5DF,QAAQ,GAAG,MAAM7I,WAAW,CAACmJ,SAAS,CAACJ,UAAU,CAAC;QACpD;QAEA,IAAIF,QAAQ,EAAE;UAAA,IAAAO,oBAAA;UACZ;UACA1C,gBAAgB,CAACmC,QAAQ,CAACpC,aAAa,CAAC;;UAExC;UACA,IAAIoC,QAAQ,CAAChB,mBAAmB,KAAKwB,SAAS,EAAE;YAC9CvB,sBAAsB,CAACe,QAAQ,CAAChB,mBAAmB,CAAC;YACpDnF,OAAO,CAACE,GAAG,CAAC,4BAA4B,EAAEiG,QAAQ,CAAChB,mBAAmB,CAAC;UACzE;;UAEA;UACA,IAAIgB,QAAQ,CAACV,mBAAmB,KAAKkB,SAAS,EAAE;YAC9CjB,sBAAsB,CAACS,QAAQ,CAACV,mBAAmB,CAAC;YACpDzF,OAAO,CAACE,GAAG,CAAC,wBAAwB,EAAEiG,QAAQ,CAACV,mBAAmB,CAAC;UACrE;;UAEA;UACA,IAAIU,QAAQ,CAACR,cAAc,KAAKgB,SAAS,EAAE;YACzCf,iBAAiB,CAACO,QAAQ,CAACR,cAAc,CAAC;YAC1C3F,OAAO,CAACE,GAAG,CAAC,sBAAsB,EAAEiG,QAAQ,CAACR,cAAc,CAAC;UAC9D;;UAEA;UACA,MAAMiB,kBAAkB,GAAG;YACzB,GAAGT,QAAQ,CAAClC,IAAI;YAChB4C,UAAU,EAAEV,QAAQ,CAACU;UACvB,CAAC;UACD3C,OAAO,CAAC0C,kBAAkB,CAAC;;UAE3B;UACA,IAAIA,kBAAkB,CAACE,EAAE,IAAI,CAACpD,MAAM,EAAE;YACpC,IAAI;cACF,MAAMqD,QAAQ,GAAG,MAAMzJ,WAAW,CAAC0J,0BAA0B,CAACJ,kBAAkB,CAACE,EAAE,CAAC;cACpFtB,mBAAmB,CAACuB,QAAQ,IAAI,EAAE,CAAC;YACrC,CAAC,CAAC,OAAO5F,KAAK,EAAE;cACdnB,OAAO,CAACmB,KAAK,CAAC,6CAA6CyF,kBAAkB,CAACE,EAAE,GAAG,EAAE3F,KAAK,CAAC;cAC3FqE,mBAAmB,CAAC,EAAE,CAAC;YACzB;UACF;;UAEA;UACA,MAAMyB,eAAe,IAAAP,oBAAA,GAAGP,QAAQ,CAACU,UAAU,cAAAH,oBAAA,uBAAnBA,oBAAA,CAAqBQ,MAAM;;UAEnD;UACA,IAAIf,QAAQ,CAACpC,aAAa,CAACoD,cAAc,EAAE;YACzC,IAAI;cACF,MAAMC,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACnB,QAAQ,CAACpC,aAAa,CAACoD,cAAc,CAAC;;cAEpE;cACA,IAAI,CAACC,UAAU,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;gBACjD,MAAM,IAAIG,KAAK,CAAC,yCAAyC,CAAC;cAC5D;;cAEA;cACA,IAAI,CAACH,UAAU,CAACI,KAAK,IAAI,CAACJ,UAAU,CAAC1I,QAAQ,EAAE;gBAC7C,MAAM,IAAI6I,KAAK,CAAC,gDAAgD,CAAC;cACnE;;cAEA;cACAvH,OAAO,CAACE,GAAG,CAAC,wDAAwD,CAAC;cACrE,IAAIkH,UAAU,CAACI,KAAK,EAAE;gBACpBJ,UAAU,CAACI,KAAK,CAAC1I,OAAO,CAAC,CAAC2I,IAAI,EAAEC,SAAS,KAAK;kBAC5C,IAAID,IAAI,CAAC/I,QAAQ,EAAE;oBACjB+I,IAAI,CAAC/I,QAAQ,CAACI,OAAO,CAAC,CAACC,OAAO,EAAE4I,YAAY,KAAK;sBAC/C,IAAI5I,OAAO,CAACE,GAAG,EAAE;wBACfe,OAAO,CAACE,GAAG,CAAC,2BAA2BnB,OAAO,CAAC6I,IAAI,KAAK7I,OAAO,CAACC,IAAI,WAAWD,OAAO,CAACE,GAAG,GAAG,CAAC;sBAChG;sBACA;sBACA,IAAIF,OAAO,CAACL,QAAQ,EAAE;wBACpBK,OAAO,CAACL,QAAQ,CAACI,OAAO,CAAC,CAAC+I,aAAa,EAAEC,WAAW,KAAK;0BACvD,IAAID,aAAa,CAAC5I,GAAG,EAAE;4BACrBe,OAAO,CAACE,GAAG,CAAC,kCAAkC2H,aAAa,CAACD,IAAI,KAAKC,aAAa,CAAC7I,IAAI,WAAW6I,aAAa,CAAC5I,GAAG,GAAG,CAAC;0BACzH;wBACF,CAAC,CAAC;sBACJ;oBACF,CAAC,CAAC;kBACJ;gBACF,CAAC,CAAC;cACJ;;cAEA;cACAhC,wBAAwB,CAAC,CAAC;;cAE1B;cACA,MAAMqB,kCAAkC,CAAC;gBAAEyJ,UAAU,EAAE;cAAM,CAAC,CAAC;cAE/D,MAAMlI,MAAM,GAAG,IAAI7C,KAAK,CAACoK,UAAU,CAAC;cACpCpH,OAAO,CAACE,GAAG,CAAC,uBAAuB,EAAEkH,UAAU,CAAC;;cAEhD;cACA,IAAI,CAACvH,MAAM,EAAE;gBACX,MAAM,IAAI0H,KAAK,CAAC,+BAA+B,CAAC;cAClD;;cAEA;cACA,MAAMS,SAAS,GAAGnI,MAAM,CAACoB,eAAe,CAAC,CAAC;cAC1C,MAAMO,MAAM,GAAG3B,MAAM,CAACqB,YAAY,CAAC,CAAC;cAEpC,IAAI8G,SAAS,CAAC1H,MAAM,GAAG,CAAC,EAAE;gBACxB,MAAM2H,aAAa,GAAGD,SAAS,CAAC,CAAC,CAAC;cACpC;cAEA,IAAIxG,MAAM,CAAClB,MAAM,GAAG,CAAC,EAAE;gBACrB,MAAM4H,UAAU,GAAG1G,MAAM,CAAC,CAAC,CAAC;cAC9B;;cAEA;cACA,IAAI,CAAC3B,MAAM,CAAC2H,KAAK,IAAI3H,MAAM,CAAC2H,KAAK,CAAClH,MAAM,KAAK,CAAC,EAAE;gBAC9C,MAAM,IAAIiH,KAAK,CAAC,qBAAqB,CAAC;cACxC;;cAEA;cACA,IAAIN,eAAe,EAAE;gBACnB,IAAI;kBACF,IAAIkB,aAAa,GAAGd,IAAI,CAACC,KAAK,CAACL,eAAe,CAAC;kBAC/C;kBACA,IAAI,OAAOkB,aAAa,KAAK,QAAQ,EAAE;oBACrC,IAAI;sBACFA,aAAa,GAAGd,IAAI,CAACC,KAAK,CAACa,aAAa,CAAC;oBAC3C,CAAC,CAAC,MAAM,CAAC;kBACX;kBACA,IAAIA,aAAa,IAAI,OAAOA,aAAa,KAAK,QAAQ,EAAE;oBACtDtI,MAAM,CAACuI,IAAI,GAAGD,aAAa;kBAC7B;gBACF,CAAC,CAAC,OAAOE,UAAU,EAAE;kBACnBrI,OAAO,CAACC,IAAI,CAAC,mCAAmC,EAAEoI,UAAU,CAAC;gBAC/D;cACF;;cAEA;cACAxI,MAAM,CAACyI,oBAAoB,GAAG,KAAK;cACnC;cACAzI,MAAM,CAAC0I,yBAAyB,GAAG,kBAAkB;cACrD1I,MAAM,CAAC2I,eAAe,GAAG,IAAI;cAC7B3I,MAAM,CAAC4I,mBAAmB,GAAG,KAAK;cAClC;cACA5I,MAAM,CAAC6I,mBAAmB,GAAG,UAAU,CAAC,CAAC;cACzC7I,MAAM,CAAC8I,eAAe,GAAG,KAAK;cAC9B9I,MAAM,CAAC+I,cAAc,GAAG,IAAI;cAC5B/I,MAAM,CAACgJ,YAAY,GAAG,UAAU;cAChChJ,MAAM,CAACiJ,YAAY,GAAG,MAAM;cAC5BjJ,MAAM,CAACkJ,mBAAmB,GAAG,KAAK,CAAC,CAAC;;cAEpC;cACA;cACA;cACA;cACA,MAAMC,YAAY,GAAG7C,QAAQ,CAAC8C,UAAU;cAExC,IAAI,CAACD,YAAY,EAAE;gBACjB;gBACAnJ,MAAM,CAACqJ,IAAI,GAAG,SAAS;gBACvBrJ,MAAM,CAAC0I,yBAAyB,GAAG,WAAW;cAChD,CAAC,MAAM;gBACL;gBACA1I,MAAM,CAACsJ,YAAY,GAAG,qBAAqB;gBAC3CtJ,MAAM,CAACuJ,WAAW,GAAG,SAAS;gBAC9BvJ,MAAM,CAACwJ,QAAQ,GAAG,UAAU,CAAC,CAAC;gBAC9B;gBACAxJ,MAAM,CAACyJ,cAAc,GAAG,IAAI;gBAC5B;gBACAzJ,MAAM,CAAC0I,yBAAyB,GAAG,kBAAkB;gBACrD;;gBAEA;gBACA,IAAI,CAACpC,QAAQ,CAACV,mBAAmB,EAAE;kBACjC5F,MAAM,CAAC0J,kBAAkB,GAAG,KAAK;kBACjCvJ,OAAO,CAACE,GAAG,CAAC,oDAAoD,CAAC;gBACnE;cACF;cACA;cACAL,MAAM,CAAC2J,GAAG,GAAG;gBACX,GAAG3J,MAAM,CAAC2J,GAAG;gBACbC,IAAI,EAAE,+BAA+B,CAACT,YAAY,GAAG,iBAAiB,GAAG,EAAE,EAAE;gBAC7EU,MAAM,EAAE,WAAW;gBACnBC,IAAI,EAAE,SAAS;gBACfC,MAAM,EAAE;cACV,CAAC;;cAED;cACA;cACA/J,MAAM,CAACgK,UAAU,CAAC;gBAChBC,SAAS,EAAE,eAAe;gBAC1BC,YAAY,EAAE,OAAO;gBACrBC,WAAW,EAAE,KAAK;gBAClB;gBACAC,YAAY,EAAE;kBACZ,yBAAyB,EAAE,SAAS;kBAAE;kBACtC,yBAAyB,EAAE,SAAS;kBAAE;kBACtC,+BAA+B,EAAE,SAAS;kBAAE;kBAC5C,8BAA8B,EAAE,SAAS;kBAAE;kBAC3C,2BAA2B,EAAE,SAAS;kBAAE;kBACxC,2BAA2B,EAAE,SAAS;kBAAE;kBACxC,6BAA6B,EAAE,SAAS;kBAAE;kBAC1C,sBAAsB,EAAE,SAAS;kBAAE;kBACnC,oBAAoB,EAAE,SAAS;kBAAE;kBACjC,mBAAmB,EAAE,YAAY;kBACjC,mBAAmB,EAAE,YAAY;kBACjC,mBAAmB,EAAE,YAAY;kBACjC,eAAe,EAAE,YAAY;kBAC7B,6BAA6B,EAAE,YAAY;kBAC3C,2BAA2B,EAAE,YAAY;kBAEzC,4BAA4B,EAAE,IAAI;kBAElC,mBAAmB,EAAE,MAAM;kBAC3B,4BAA4B,EAAE,yDAAyD;kBACvF,4BAA4B,EAAE;gBAChC;cACF,CAAC,CAAC;cACFpK,MAAM,CAACqK,cAAc,CAACC,GAAG,CAAC,UAAUtK,MAAM,EAAEuK,OAAO,EAAE;gBACnD;gBACA,IAAIC,GAAG,GAAGrM,iBAAiB,CAACsM,aAAa,CAACF,OAAO,CAACG,IAAI,CAAC;gBACvD;gBACAH,OAAO,CAACI,IAAI,GAAGH,GAAG;cACpB,CAAC,CAAC;cACF;cACA,IAAIrB,YAAY,EAAE;gBAChB;gBACA,MAAMyB,iBAAiB,GAAG,MAAAA,CAAOC,MAAM,EAAEN,OAAO,KAAK;kBACnD;kBACAA,OAAO,CAACO,aAAa,GAAG,KAAK;;kBAE7B;kBACAvN,cAAc,CAACwN,aAAa,CAC1B,yGAAyG,EACzG,MAAOC,SAAS,IAAK;oBACnB,IAAI,CAACA,SAAS,EAAE;sBACd;oBACF;oBAEA,IAAI;sBACF;sBACA,MAAMvN,WAAW,CAACwN,cAAc,CAAC3E,QAAQ,CAAClC,IAAI,CAAC6C,EAAE,EAAEO,IAAI,CAAC0D,SAAS,CAACL,MAAM,CAACtC,IAAI,CAAC,CAAC;;sBAE/E;sBACA,MAAM4C,WAAW,GAAG,MAAM1N,WAAW,CAAC2N,UAAU,CAAC9E,QAAQ,CAAClC,IAAI,CAAC6C,EAAE,CAAC;sBAElE9G,OAAO,CAACE,GAAG,CAAC,6CAA6C,CAAC;;sBAE1D;sBACA,IAAI8K,WAAW,IAAIA,WAAW,CAACE,OAAO,EAAE;wBACtC9N,cAAc,CAAC+N,YAAY,CAACH,WAAW,CAACE,OAAO,CAAC;sBAClD,CAAC,MAAM;wBACL9N,cAAc,CAAC+N,YAAY,CAAC,sCAAsC,CAAC;sBACrE;;sBAEA;sBACA,IAAIH,WAAW,EAAE;wBACf9G,OAAO,CAAEkH,IAAI,KAAM;0BACjB,GAAGA,IAAI;0BACPC,MAAM,EAAEL,WAAW,CAACK,MAAM;0BAC1BC,YAAY,EAAE7N,iBAAiB,CAACuN,WAAW,CAACK,MAAM;wBACpD,CAAC,CAAC,CAAC;sBACL;;sBAEA;sBACAX,MAAM,CAACa,UAAU,CAAC,CAAC;oBACrB,CAAC,CAAC,OAAOpK,KAAK,EAAE;sBACdnB,OAAO,CAACmB,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;sBACzD/D,cAAc,CAACoO,UAAU,CAACrK,KAAK,CAAC+J,OAAO,IAAI,kDAAkD,CAAC;oBAChG;kBACF,CACF,CAAC;gBACH,CAAC;;gBAED;gBACArL,MAAM,CAAC4L,UAAU,CAACtB,GAAG,CAACM,iBAAiB,CAAC;cAC1C;;cAEA;cACA,IAAIzB,YAAY,EAAE;gBAChB;gBACAnJ,MAAM,CAAC6L,cAAc,CAACvB,GAAG,CAACwB,kBAAkB,CAAC;cAC/C;;cAEA;cACA9L,MAAM,CAAC+L,oBAAoB,CAACzB,GAAG,CAAC,CAACO,MAAM,EAAEN,OAAO,KAAK;gBACnDyB,iBAAiB,CAACnB,MAAM,EAAEN,OAAO,CAAC;;gBAElC;gBACA,IAAI,CAACjE,QAAQ,CAACV,mBAAmB,IAAIU,QAAQ,CAAChB,mBAAmB,KAAKvH,YAAY,CAACkO,SAAS,EAAE;kBAC5F,MAAMC,UAAU,GAAGrB,MAAM,CAACqB,UAAU;kBACpC,IAAIA,UAAU,EAAE;oBACd;oBACA,MAAMC,QAAQ,GAAGtB,MAAM,CAACuB,WAAW;oBACnC,IAAID,QAAQ,IAAI,CAACA,QAAQ,CAACE,gBAAgB,EAAE;sBAC1C;sBACA,MAAMC,iBAAiB,GAAGhG,QAAQ,CAACiG,2BAA2B;;sBAE9D;sBACA,IAAID,iBAAiB,IAAIA,iBAAiB,CAACzM,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;wBACxD,MAAM2M,WAAW,GAAG;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA,sDAAsDF,iBAAiB;AACvE;AACA;AACA,yBAAyB;;wBAED;wBACA,IAAI,CAACH,QAAQ,CAACM,WAAW,EAAE;0BACzBN,QAAQ,CAACM,WAAW,GAAGD,WAAW;wBACpC,CAAC,MAAM;0BACLL,QAAQ,CAACM,WAAW,GAAGN,QAAQ,CAACM,WAAW,GAAGD,WAAW;wBAC3D;wBACAL,QAAQ,CAACE,gBAAgB,GAAG,IAAI;sBAClC;oBACF;kBACF;gBACF;cACF,CAAC,CAAC;;cAEF;cACArM,MAAM,CAAC0M,qBAAqB,CAACpC,GAAG,CAAC,CAACqC,CAAC,EAAEpC,OAAO,KAAK;gBAAA,IAAAqC,qBAAA,EAAAC,qBAAA;gBAC/C;gBACA1M,OAAO,CAACE,GAAG,CAAC,yBAAAuM,qBAAA,GAAwBrC,OAAO,CAACuC,cAAc,cAAAF,qBAAA,uBAAtBA,qBAAA,CAAwBzN,IAAI,QAAA0N,qBAAA,GAAOtC,OAAO,CAACwC,cAAc,cAAAF,qBAAA,uBAAtBA,qBAAA,CAAwB1N,IAAI,EAAE,CAAC;cACxG,CAAC,CAAC;;cAEF;cACA;cACA,IAAIa,MAAM,CAACgN,aAAa,EAAE;gBACxBhN,MAAM,CAACgN,aAAa,CAAC1C,GAAG,CAAEO,MAAM,IAAK;kBACnC1K,OAAO,CAACE,GAAG,CAAC,6BAA6B,CAAC;kBAC1C;kBACAwK,MAAM,CAACoC,qBAAqB,GAAG,MAAM;kBACrCpC,MAAM,CAACpB,cAAc,GAAG,IAAI;gBAC9B,CAAC,CAAC;cACJ;;cAEA;cACAzJ,MAAM,CAACkN,aAAa,GAAG,gCAAgClN,MAAM,CAACmN,KAAK,IAAI7G,QAAQ,CAACpC,aAAa,CAAC/E,IAAI,QAAQ;;cAE1G;cACA,MAAMe,UAAU,GAAG,CAAC,CAAC;cACrB,IAAIqH,UAAU,CAACI,KAAK,EAAE;gBACpBJ,UAAU,CAACI,KAAK,CAAC1I,OAAO,CAAE2I,IAAI,IAAK;kBACjChJ,eAAe,CAACgJ,IAAI,CAAC/I,QAAQ,EAAEqB,UAAU,CAAC;gBAC5C,CAAC,CAAC;cACJ;cACAC,OAAO,CAACE,GAAG,CAAC,8BAA8B,EAAEH,UAAU,CAAC;;cAEvD;cACA,MAAMsF,gBAAgB,GAAGzF,qBAAqB,CAACC,MAAM,EAAEsG,QAAQ,CAAC8G,oBAAoB,EAAElN,UAAU,CAAC;;cAEjG;cACA,IAAIsF,gBAAgB,EAAE;gBACpBC,mBAAmB,CAACD,gBAAgB,CAAC;cACvC;;cAEA;cACA,MAAM/D,sBAAsB,GAAG,CAAC,CAAC;cACjC,IAAI8F,UAAU,CAACI,KAAK,EAAE;gBACpBJ,UAAU,CAACI,KAAK,CAAC1I,OAAO,CAAE2I,IAAI,IAAK;kBACjCvI,2BAA2B,CAACuI,IAAI,CAAC/I,QAAQ,EAAE4C,sBAAsB,CAAC;gBACpE,CAAC,CAAC;cACJ;cACAtB,OAAO,CAACE,GAAG,CAAC,0CAA0C,EAAEoB,sBAAsB,CAAC;;cAE/E;cACA;cACAF,iCAAiC,CAACvB,MAAM,EAAEsG,QAAQ,CAAChH,eAAe,EAAEmC,sBAAsB,CAAC;;cAE3F;cACA,MAAMiB,mBAAmB,GAAG,CAAC,CAAC;cAC9B,MAAMC,sBAAsB,GAAG,CAAC,CAAC;cACjC,IAAI4E,UAAU,CAACI,KAAK,EAAE;gBACpBJ,UAAU,CAACI,KAAK,CAAC1I,OAAO,CAAE2I,IAAI,IAAK;kBACjCrI,wBAAwB,CAACqI,IAAI,CAAC/I,QAAQ,EAAE6D,mBAAmB,CAAC;kBAC5DjD,2BAA2B,CAACmI,IAAI,CAAC/I,QAAQ,EAAE8D,sBAAsB,CAAC;gBACpE,CAAC,CAAC;cACJ;cACAxC,OAAO,CAACE,GAAG,CAAC,uCAAuC,EAAEqC,mBAAmB,CAAC;cACzEvC,OAAO,CAACE,GAAG,CAAC,2CAA2C,EAAEsC,sBAAsB,CAAC;;cAEhF;cACA;cACAJ,8BAA8B,CAC5BvC,MAAM,EACNsG,QAAQ,CAAC9G,YAAY,IAAI,EAAE,EAC3B8G,QAAQ,CAAC5G,eAAe,IAAI,EAAE,EAC9BgD,mBAAmB,EACnBC,sBACF,CAAC;;cAED;cACA,MAAM0K,uCAAuC,CAACrN,MAAM,EAAEsG,QAAQ,CAACgH,SAAS,EAAEhH,QAAQ,CAACpC,aAAa,CAACN,IAAI,CAAC;cAEtGK,cAAc,CAACjE,MAAM,CAAC;;cAEtB;cACA,IAAIA,MAAM,CAAC8E,SAAS,GAAG,CAAC,EAAE;gBAAA,IAAAyI,mBAAA,EAAAC,oBAAA;gBACxB,MAAMC,gBAAgB,GAAGzN,MAAM,CAAC0N,YAAY,CAACC,OAAO,CAAC3N,MAAM,CAACoM,WAAW,CAAC;gBACxE,MAAMwB,cAAc,GAAG5N,MAAM,CAAC6N,gBAAgB,CAAC,EAAAN,mBAAA,GAAAvN,MAAM,CAACoM,WAAW,cAAAmB,mBAAA,uBAAlBA,mBAAA,CAAoBJ,KAAK,OAAAK,oBAAA,GAAIxN,MAAM,CAACoM,WAAW,cAAAoB,oBAAA,uBAAlBA,oBAAA,CAAoBrO,IAAI,EAAC;gBAErGyF,kBAAkB,CAAC;kBACjBC,MAAM,EAAE4I,gBAAgB,GAAG,CAAC,CAAC,GAAGA,gBAAgB,GAAG,CAAC;kBACpD3I,SAAS,EAAE9E,MAAM,CAAC0N,YAAY,CAACjN,MAAM;kBACrCsE,SAAS,EAAE6I;gBACb,CAAC,CAAC;cACJ;cAEAlJ,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,OAAO8D,UAAU,EAAE;cACnBrI,OAAO,CAACmB,KAAK,CAAC,4BAA4B,EAAEkH,UAAU,CAAC;cACvDhE,QAAQ,CAAC,wDAAwD,CAAC;YACpE;UACF,CAAC,MAAM;YACLA,QAAQ,CAAC,kDAAkD,CAAC;UAC9D;QACF,CAAC,MAAM;UAAA,IAAAsJ,SAAA;UACLtJ,QAAQ,CAAC,EAAAsJ,SAAA,GAAAxH,QAAQ,cAAAwH,SAAA,uBAARA,SAAA,CAAUzC,OAAO,KAAI,mCAAmC,CAAC;QACpE;MACF,CAAC,CAAC,OAAO0C,GAAG,EAAE;QACZ5N,OAAO,CAACmB,KAAK,CAAC,wBAAwB,EAAEyM,GAAG,CAAC;QAC5CvJ,QAAQ,CAACuJ,GAAG,CAAC1C,OAAO,IAAI,sDAAsD,CAAC;MACjF,CAAC,SAAS;QACR9G,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED8B,QAAQ,CAAC,CAAC;IACV;EACF,CAAC,EAAE,CAAC5B,SAAS,EAAEb,IAAI,EAAEC,MAAM,CAAC,CAAC,CAAC,CAAC;;EAE/B;EACA,MAAMmK,kBAAkB,GAAGhR,WAAW,CAAC,MAAM;IAC3CiJ,QAAQ,CAAC,OAAO,CAAC;EACnB,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMgI,uBAAuB,GAAGA,CAAA,KAAM;IACpC9I,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;;EAED;EACA,MAAM+I,qBAAqB,GAAG,MAAOhH,QAAQ,IAAK;IAChD,IAAI,EAAC9C,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE6C,EAAE,GAAE;IAEf5B,cAAc,CAAC,IAAI,CAAC;IACpB,IAAI;MACF,MAAM5H,WAAW,CAAC0Q,iBAAiB,CAAC/J,IAAI,CAAC6C,EAAE,EAAEC,QAAQ,CAAC;MACtD3J,cAAc,CAAC6Q,aAAa,CAAC,wCAAwC,CAAC;;MAEtE;MACAjJ,qBAAqB,CAAC,KAAK,CAAC;;MAE5B;MACA,IAAIrB,WAAW,EAAE;QACfA,WAAW,CAAC,CAAC;MACf,CAAC,MAAM;QACLmC,QAAQ,CAAC,OAAO,CAAC;MACnB;IACF,CAAC,CAAC,OAAO3E,KAAK,EAAE;MACd/D,cAAc,CAAC8Q,WAAW,CAAC/M,KAAK,CAAC+J,OAAO,IAAI,qCAAqC,CAAC;IACpF,CAAC,SAAS;MACRhG,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMiJ,uBAAuB,GAAG,MAAAA,CAAA,KAAY;IAC1C,IAAI,EAAClK,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE6C,EAAE,GAAE;;IAEf;IACA,MAAM+D,SAAS,GAAGuD,MAAM,CAACC,OAAO,CAAC,6FAA6F,CAAC;IAE/H,IAAI,CAACxD,SAAS,EAAE;MACd;IACF;IAEA,IAAI;MACF;MACA,IAAIhH,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEuE,IAAI,EAAE;QACrB,MAAM9K,WAAW,CAACwN,cAAc,CAAC7G,IAAI,CAAC6C,EAAE,EAAEO,IAAI,CAAC0D,SAAS,CAAClH,WAAW,CAACuE,IAAI,CAAC,CAAC;MAC7E;;MAEA;MACA,MAAM4C,WAAW,GAAG,MAAM1N,WAAW,CAAC2N,UAAU,CAAChH,IAAI,CAAC6C,EAAE,CAAC;MAEzD9G,OAAO,CAACE,GAAG,CAAC,qCAAqC,CAAC;;MAElD;MACA,IAAI8K,WAAW,IAAIA,WAAW,CAACE,OAAO,EAAE;QACtC9N,cAAc,CAAC+N,YAAY,CAACH,WAAW,CAACE,OAAO,CAAC;MAClD,CAAC,MAAM;QACL9N,cAAc,CAAC+N,YAAY,CAAC,sCAAsC,CAAC;MACrE;;MAEA;MACA,IAAIH,WAAW,EAAE;QACf9G,OAAO,CAAEkH,IAAI,KAAM;UACjB,GAAGA,IAAI;UACPC,MAAM,EAAEL,WAAW,CAACK,MAAM;UAC1BC,YAAY,EAAE7N,iBAAiB,CAACuN,WAAW,CAACK,MAAM;QACpD,CAAC,CAAC,CAAC;MACL;;MAEA;MACA,IAAI1H,WAAW,EAAE;QACfA,WAAW,CAAC,CAAC;MACf,CAAC,MAAM;QACLmC,QAAQ,CAAC,OAAO,CAAC;MACnB;IACF,CAAC,CAAC,OAAO3E,KAAK,EAAE;MACdnB,OAAO,CAACmB,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD/D,cAAc,CAACoO,UAAU,CAACrK,KAAK,CAAC+J,OAAO,IAAI,kDAAkD,CAAC;IAChG;EACF,CAAC;;EAED;EACA,MAAMoD,wBAAwB,GAAGA,CAAA,KAAM;IACrC,IAAI,CAACrJ,WAAW,EAAE;MAChBD,qBAAqB,CAAC,KAAK,CAAC;IAC9B;EACF,CAAC;;EAED;EACA,MAAMuJ,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAI,CAACtK,IAAI,EAAE,OAAO,KAAK;;IAEvB;IACA,IAAIkB,mBAAmB,KAAKvH,YAAY,CAAC4Q,QAAQ,EAAE;MACjD,OAAO,KAAK;IACd;;IAEA;IACA,OAAOvK,IAAI,CAACoH,MAAM,KAAK7N,UAAU,CAACiR,wBAAwB,IAAIxK,IAAI,CAACoH,MAAM,KAAK7N,UAAU,CAACkR,wBAAwB;EACnH,CAAC;;EAED;EACA;EACA;EACA,MAAMC,iBAAiB,GAAG9R,WAAW,CAAC,YAAY;IAChD,MAAMoB,kBAAkB,CAAC0Q,iBAAiB,CAAC9K,WAAW,EAAEI,IAAI,CAAC;EAC/D,CAAC,EAAE,CAACJ,WAAW,EAAEI,IAAI,CAAC,CAAC;;EAEvB;EACA,MAAM2K,0BAA0B,GAAG/R,WAAW,CAAC,MAAM;IACnDoB,kBAAkB,CAAC2Q,0BAA0B,CAAC/K,WAAW,EAAEI,IAAI,CAAC;EAClE,CAAC,EAAE,CAACJ,WAAW,EAAEI,IAAI,CAAC,CAAC;EAEvB,MAAM4H,iBAAiB,GAAGA,CAACnB,MAAM,EAAEN,OAAO,KAAK;IAC7C;IACA,IAAIM,MAAM,IAAIA,MAAM,CAACuB,WAAW,EAAE;MAAA,IAAA4C,mBAAA,EAAAC,oBAAA;MAChC,MAAMxB,gBAAgB,GAAG5C,MAAM,CAAC6C,YAAY,CAACC,OAAO,CAAC9C,MAAM,CAACuB,WAAW,CAAC;MAExE,MAAMwB,cAAc,GAAG/C,MAAM,CAACuB,WAAW,CAACyB,gBAAgB,CAAC,EAAAmB,mBAAA,GAAAnE,MAAM,CAACuB,WAAW,cAAA4C,mBAAA,uBAAlBA,mBAAA,CAAoB7B,KAAK,OAAA8B,oBAAA,GAAIpE,MAAM,CAACuB,WAAW,cAAA6C,oBAAA,uBAAlBA,oBAAA,CAAoB9P,IAAI,EAAC;MAEjH,MAAM+P,QAAQ,GAAG;QACfrK,MAAM,EAAE4I,gBAAgB,GAAG,CAAC,CAAC,GAAGA,gBAAgB,GAAG,CAAC;QACpD3I,SAAS,EAAE+F,MAAM,CAAC6C,YAAY,CAACjN,MAAM;QACrCsE,SAAS,EAAE6I;MACb,CAAC;MACDhJ,kBAAkB,CAACsK,QAAQ,CAAC;IAC9B;EACF,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAG,MAAAA,CAAOC,QAAQ,EAAEC,YAAY,GAAG,IAAI,KAAK;IACjE,IAAI;MAAA,IAAAC,IAAA;MACF,MAAMC,WAAW,GAAGvK,OAAO,CAACC,OAAO;MACnC,IAAI,CAACsK,WAAW,EAAE;QAChB;QACAH,QAAQ,IAAIA,QAAQ,CAAC,CAAC;QACtB;MACF;MAEA,MAAMI,OAAO,IAAAF,IAAA,GAAGD,YAAY,aAAZA,YAAY,cAAZA,YAAY,GAAIrL,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEuE,IAAI,cAAA+G,IAAA,cAAAA,IAAA,GAAI,CAAC,CAAC;;MAEvD;MACA,MAAM7R,WAAW,CAACgS,kBAAkB,CAACF,WAAW,CAACtI,EAAE,EAAEuI,OAAO,CAAC;IAC/D,CAAC,CAAC,OAAOE,CAAC,EAAE;MACVvP,OAAO,CAACC,IAAI,CAAC,mBAAmB,EAAEsP,CAAC,CAAC;IACtC,CAAC,SAAS;MACRN,QAAQ,IAAIA,QAAQ,CAAC,CAAC;IACxB;EACF,CAAC;;EAED;EACA,SAAStD,kBAAkBA,CAACjB,MAAM,EAAEN,OAAO,EAAE;IAC3CpK,OAAO,CAACE,GAAG,CAAC,uBAAuB,EAAEkK,OAAO,CAAC;;IAE7C;IACA4E,iBAAiB,CAACrI,SAAS,EAAE+D,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEtC,IAAI,CAAC;EAC5C;;EAEA;EACA,MAAMoH,WAAW,GAAGA,CAAA,KAAM;IACxB,SAASC,UAAUA,CAAC5E,SAAS,EAAE;MAC7B,IAAI,CAACA,SAAS,EAAE;QACd;MACF;MACA/M,IAAI,CACD4R,GAAG,CAAC3R,UAAU,CAAC4R,SAAS,GAAG,4BAA4B,EAAE,CAAC1L,IAAI,CAAC6C,EAAE,CAAC,EAAE;QACnE8I,OAAO,EAAE;UACP,cAAc,EAAE;QAClB;MACF,CAAC,CAAC,CACDC,IAAI,CAAEC,QAAQ,IAAK;QAClB;QACA,IAAIA,QAAQ,CAAC1H,IAAI,CAAC2H,YAAY,KAAK,CAAC,EAAE;UACpC3S,cAAc,CAAC6Q,aAAa,CAAC,yBAAyB,CAAC;UACvDtK,WAAW,IAAIA,WAAW,CAAC,CAAC;QAC9B,CAAC,MAAM;UACLvG,cAAc,CAAC8Q,WAAW,CAAC4B,QAAQ,CAAC1H,IAAI,CAAC8C,OAAO,CAAC;QACnD;MACF,CAAC,CAAC,CACD8E,KAAK,CAAC,MAAM;QACX5S,cAAc,CAAC8Q,WAAW,CAAC,iCAAiC,CAAC;MAC/D,CAAC,CAAC;IACN;IAEA,MAAM+B,WAAW,GAAGlM,aAAa,GAAGA,aAAa,CAAC/E,IAAI,GAAG,cAAc;IACvE,MAAMkR,WAAW,GAAGjM,IAAI,GAAGA,IAAI,CAACiM,WAAW,GAAG,SAAS;IACvD9S,cAAc,CAACwN,aAAa,CAAC,8BAA8BqF,WAAW,OAAOC,WAAW,EAAE,EAAET,UAAU,CAAC;EACzG,CAAC;;EAED;EACA,MAAMU,6BAA6B,GAAGA,CAAA,KAAM;IAC1C;IACA,IAAIhL,mBAAmB,KAAKvH,YAAY,CAACkO,SAAS,IAAI,CAACvG,gBAAgB,IAAIA,gBAAgB,CAACjF,MAAM,KAAK,CAAC,EAAE;MACxG,OAAO,IAAI;IACb;IAEA,oBACE9B,OAAA;MAAK4R,SAAS,EAAC,2BAA2B;MAAAC,QAAA,eACxC7R,OAAA,CAACJ,KAAK;QAACsL,MAAM,EAAC,2BAA2B;QAAC4G,UAAU;QAACC,SAAS,EAAE,IAAK;QAACH,SAAS,EAAC,yBAAyB;QAAAC,QAAA,eACvG7R,OAAA;UAAK4R,SAAS,EAAC,eAAe;UAAAC,QAAA,EAC3B9K,gBAAgB,CAAC5G,GAAG,CAAC,CAAC6R,OAAO,EAAEC,KAAK,kBACnCjS,OAAA;YAEE4R,SAAS,EAAC,cAAc;YACxBM,KAAK,EAAE;cACLC,YAAY,EAAEF,KAAK,GAAGlL,gBAAgB,CAACjF,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,GAAG;cAChEsQ,OAAO,EAAE,MAAM;cACfC,MAAM,EAAE,mBAAmB;cAC3BC,YAAY,EAAE,KAAK;cACnBC,eAAe,EAAE;YACnB,CAAE;YAAAV,QAAA,gBAEF7R,OAAA;cACE4R,SAAS,EAAC,gBAAgB;cAC1BM,KAAK,EAAE;gBACLM,OAAO,EAAE,MAAM;gBACfC,cAAc,EAAE,eAAe;gBAC/BC,UAAU,EAAE,QAAQ;gBACpBP,YAAY,EAAE,QAAQ;gBACtBQ,QAAQ,EAAE,QAAQ;gBAClBC,KAAK,EAAE;cACT,CAAE;cAAAf,QAAA,gBAEF7R,OAAA;gBAAM4R,SAAS,EAAC,eAAe;gBAAAC,QAAA,eAC7B7R,OAAA;kBAAA6R,QAAA,EAASG,OAAO,CAACa,eAAe,IAAI;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC,eACPjT,OAAA;gBAAM4R,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAEG,OAAO,CAACkB,WAAW,GAAGrT,cAAc,CAACmS,OAAO,CAACkB,WAAW,CAAC,GAAG;cAAoB;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrH,CAAC,eACNjT,OAAA;cACE4R,SAAS,EAAC,eAAe;cACzBM,KAAK,EAAE;gBACLS,QAAQ,EAAE,QAAQ;gBAClBC,KAAK,EAAE,MAAM;gBACbT,YAAY,EAAE;cAChB,CAAE;cAAAN,QAAA,eAEF7R,OAAA;gBAAA6R,QAAA,GAAKG,OAAO,CAACmB,gBAAgB,EAAC,QAAM;cAAA;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eACNjT,OAAA;cACE4R,SAAS,EAAC,cAAc;cACxBM,KAAK,EAAE;gBACLkB,UAAU,EAAE,KAAK;gBACjBC,UAAU,EAAE;cACd,CAAE;cAAAxB,QAAA,EAEDG,OAAO,CAACzJ;YAAQ;cAAAuK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC;UAAA,GA5CDjB,OAAO,CAAC1J,EAAE,IAAI2J,KAAK;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA6CrB,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;EAED,IAAI,CAAC5L,WAAW,IAAI,CAACA,WAAW,CAACI,eAAe,CAAC,CAAC,EAAE;IAClD,oBACEzH,OAAA;MAAK4R,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/B7R,OAAA;QAAA6R,QAAA,EAAI;MAAa;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CAAC;EAEV;EAEA,IAAItN,OAAO,EAAE;IACX,oBACE3F,OAAA;MAAK4R,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/B7R,OAAA;QAAK4R,SAAS,EAAC,KAAK;QAAAC,QAAA,gBAClB7R,OAAA;UAAA6R,QAAA,EAAI;QAAa;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtBjT,OAAA;UAAA6R,QAAA,EAAG;QAAe;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAItQ,KAAK,EAAE;IACT,oBACE3C,OAAA;MAAKsI,EAAE,EAAC,iBAAiB;MAAAuJ,QAAA,eACvB7R,OAAA;QAAA6R,QAAA,gBACE7R,OAAA;UAAK4R,SAAS,EAAC,2BAA2B;UAAAC,QAAA,eACxC7R,OAAA;YAAK4R,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChC7R,OAAA;cAAM4R,SAAS,EAAC;YAAmC;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC3DjT,OAAA;cAAK4R,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7B7R,OAAA;gBAAM4R,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,EAAC;cAAK;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAChDjT,OAAA;gBAAK4R,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAElP;cAAK;gBAAAmQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNjT,OAAA;UAAQ4R,SAAS,EAAC,6CAA6C;UAAC0B,OAAO,EAAEjE,kBAAmB;UAAAwC,QAAA,gBAC1F7R,OAAA;YAAM4R,SAAS,EAAC;UAAgC;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACxDjT,OAAA;YAAM4R,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAC;UAAI;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI,CAAC5N,WAAW,EAAE;IAChB,oBACErF,OAAA;MAAK4R,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/B7R,OAAA;QAAK4R,SAAS,EAAC,KAAK;QAAAC,QAAA,gBAClB7R,OAAA;UAAA6R,QAAA,EAAI;QAAa;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtBjT,OAAA;UAAA6R,QAAA,EAAG;QAA+B;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEjT,OAAA;IAAK4R,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBAE/B7R,OAAA;MAAK4R,SAAS,EAAC,yBAAyB;MAAAC,QAAA,gBACtC7R,OAAA;QAAK4R,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B7R,OAAA;UAAK4R,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvB7R,OAAA;YACEuT,GAAG,EAAC,UAAU;YACdC,GAAG,EAAE,GAAGjU,UAAU,CAACkU,QAAQ,WAAY;YACvCC,OAAO,EAAG3C,CAAC,IAAMA,CAAC,CAAC4C,MAAM,CAACH,GAAG,GAAG,iBAAmB;YACnD5B,SAAS,EAAC;UAAgB;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,eACFjT,OAAA;YAAM4R,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EACpC3M,MAAM,GACH,GACEO,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAER,IAAI,IAAIM,aAAa,aAAbA,aAAa,eAAbA,aAAa,CAAEN,IAAI,IAAIA,IAAI,GAAG,GAAG,CAAAQ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAER,IAAI,MAAIM,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEN,IAAI,KAAIA,IAAI,EAAE,GAAG,EAAE,sCAC3D,GACtC,GAAGQ,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAER,IAAI,IAAIM,aAAa,aAAbA,aAAa,eAAbA,aAAa,CAAEN,IAAI,IAAIA,IAAI,GAAG,GAAG,CAAAQ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAER,IAAI,MAAIM,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEN,IAAI,KAAIA,IAAI,EAAE,GAAG,EAAE;UAAwB;YAAA6N,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1H,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNjT,OAAA;UAAK4R,SAAS,EAAC,gBAAgB;UAAAC,QAAA,GAE5B1K,cAAc,iBACbnH,OAAA;YACE4R,SAAS,EAAC,yBAAyB;YACnC0B,OAAO,EAAE3D,uBAAwB;YACjCuC,KAAK,EAAE;cACLK,eAAe,EAAE,SAAS;cAC1BK,KAAK,EAAE,OAAO;cACdP,MAAM,EAAE,MAAM;cACdD,OAAO,EAAE,UAAU;cACnBE,YAAY,EAAE,KAAK;cACnBsB,MAAM,EAAE,SAAS;cACjBC,WAAW,EAAE,MAAM;cACnBlB,QAAQ,EAAE,MAAM;cAChBmB,UAAU,EAAE;YACd,CAAE;YAAAjC,QAAA,gBAEF7R,OAAA;cAAG4R,SAAS,EAAC,aAAa;cAACM,KAAK,EAAE;gBAAE2B,WAAW,EAAE;cAAM;YAAE;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,uBAEhE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT,EAGAlD,oBAAoB,CAAC,CAAC,iBACrB/P,OAAA;YACE4R,SAAS,EAAC,eAAe;YACzB0B,OAAO,EAAEhE,uBAAwB;YACjC4C,KAAK,EAAE;cACLK,eAAe,EAAE,SAAS;cAC1BK,KAAK,EAAE,OAAO;cACdP,MAAM,EAAE,MAAM;cACdD,OAAO,EAAE,UAAU;cACnBE,YAAY,EAAE,KAAK;cACnBsB,MAAM,EAAE,SAAS;cACjBC,WAAW,EAAE,MAAM;cACnBlB,QAAQ,EAAE;YACZ,CAAE;YAAAd,QAAA,gBAEF7R,OAAA;cAAG4R,SAAS,EAAC,YAAY;cAACM,KAAK,EAAE;gBAAE2B,WAAW,EAAE;cAAM;YAAE;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,wBAE/D;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT,eAMDjT,OAAA;YAAQ4R,SAAS,EAAC,gBAAgB;YAAC0B,OAAO,EAAElD,0BAA2B;YAAC8B,KAAK,EAAE;cAAE6B,UAAU,EAAE;YAAO,CAAE;YAAAlC,QAAA,gBACpG7R,OAAA;cAAG4R,SAAS,EAAC;YAAa;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,uBAEjC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTjT,OAAA;YAAQ4R,SAAS,EAAC,iBAAiB;YAAC0B,OAAO,EAAEjE,kBAAmB;YAAAwC,QAAA,EAAC;UAEjE;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNjT,OAAA;QAAK4R,SAAS,EAAC,kBAAkB;QAAAC,QAAA,GAAC,8EAC4C,EAAC5M,IAAI,EAAC,+EAEpF;MAAA;QAAA6N,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL/N,MAAM,iBACLlF,OAAA;MAAKkS,KAAK,EAAE;QAAEC,YAAY,EAAE;MAAO,CAAE;MAAAN,QAAA,eACnC7R,OAAA,CAACL,6BAA6B;QAACuF,MAAM,EAAEA,MAAO;QAAC1C,OAAO,EAAE;MAAK;QAAAsQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7D,CACN,EAGAtM,mBAAmB,KAAKvH,YAAY,CAACkO,SAAS,IAAI7H,IAAI,iBACrDzF,OAAA;MAAK4R,SAAS,EAAC,yBAAyB;MAACM,KAAK,EAAE;QAAEC,YAAY,EAAE;MAAO,CAAE;MAAAN,QAAA,eACvE7R,OAAA,CAACJ,KAAK;QAACsL,MAAM,EAAC,iBAAiB;QAAC4G,UAAU;QAACC,SAAS,EAAE,KAAM;QAACH,SAAS,EAAC,uBAAuB;QAAAC,QAAA,gBAC5F7R,OAAA;UAAK4R,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eACpC7R,OAAA;YAAK4R,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClC7R,OAAA;cAAK4R,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B7R,OAAA;gBAAM4R,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAa;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACnDjT,OAAA;gBAAM4R,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAE,CAAApM,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiM,WAAW,KAAI;cAAE;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC,eACNjT,OAAA;cAAK4R,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B7R,OAAA;gBAAM4R,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAa;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACnDjT,OAAA;gBAAM4R,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAE,CAAApM,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuO,WAAW,KAAI;cAAE;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC,eACNjT,OAAA;cAAK4R,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B7R,OAAA;gBAAM4R,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAiB;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvDjT,OAAA;gBAAM4R,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAE,CAAApM,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwO,cAAc,KAAI;cAAE;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC,eACNjT,OAAA;cAAK4R,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B7R,OAAA;gBAAM4R,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAS;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/CjT,OAAA;gBAAM4R,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAE,CAAApM,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyO,QAAQ,KAAI;cAAE;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC,eACNjT,OAAA;cAAK4R,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B7R,OAAA;gBAAM4R,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAiB;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvDjT,OAAA;gBAAM4R,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAE,CAAApM,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0O,mBAAmB,KAAI;cAAE;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpE,CAAC,eACNjT,OAAA;cAAK4R,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B7R,OAAA;gBAAM4R,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAmB;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzDjT,OAAA;gBAAM4R,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAE,CAAApM,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2O,qBAAqB,KAAI;cAAE;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE,CAAC,eACNjT,OAAA;cAAK4R,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/B7R,OAAA;gBAAK4R,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3B7R,OAAA;kBAAM4R,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC7CjT,OAAA;kBAAM4R,SAAS,EAAE,gBAAgB1S,kBAAkB,CAACuG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoH,MAAM,CAAC,EAAG;kBAAAgF,QAAA,EACjE,CAAApM,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqH,YAAY,KAAI7N,iBAAiB,CAACwG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoH,MAAM,CAAC,IAAI;gBAAO;kBAAAiG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNjT,OAAA;gBAAK4R,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EACjCpM,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE4O,qBAAqB,gBAC1BrU,OAAA;kBAAK4R,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,GAAC,aACnB,EAAC,IAAI9J,IAAI,CAACtC,IAAI,CAAC4O,qBAAqB,CAAC,CAACC,kBAAkB,CAAC,CAAC,EAAC,KAAG,EAAC,GAAG,EAC5E,IAAIvM,IAAI,CAACtC,IAAI,CAAC4O,qBAAqB,CAAC,CAACE,kBAAkB,CAAC,CAAC;gBAAA;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD,CAAC,gBAENjT,OAAA;kBAAK4R,SAAS,EAAC;gBAAgB;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAwD;cACxF;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAELtB,6BAA6B,CAAC,CAAC;MAAA;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACN,EAaAtM,mBAAmB,KAAKvH,YAAY,CAACkO,SAAS,IAAI7H,IAAI,iBACrDzF,OAAA;MACE4R,SAAS,EAAC,sBAAsB;MAChCM,KAAK,EAAE;QACLsC,UAAU,EAAE,SAAS;QACrBpC,OAAO,EAAE,MAAM;QACfD,YAAY,EAAE,MAAM;QACpBG,YAAY,EAAE;MAChB,CAAE;MAAAT,QAAA,gBAEF7R,OAAA;QAAA6R,QAAA,EAAI;MAAmB;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5BjT,OAAA;QAAKkS,KAAK,EAAE;UAAEM,OAAO,EAAE,MAAM;UAAEiC,mBAAmB,EAAE,sCAAsC;UAAEC,GAAG,EAAE;QAAO,CAAE;QAAA7C,QAAA,gBACxG7R,OAAA;UAAA6R,QAAA,gBACE7R,OAAA;YAAA6R,QAAA,EAAQ;UAAa;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACxN,IAAI,CAACiM,WAAW,IAAI,KAAK;QAAA;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC,eACNjT,OAAA;UAAA6R,QAAA,gBACE7R,OAAA;YAAA6R,QAAA,EAAQ;UAAa;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACxN,IAAI,CAACuO,WAAW,IAAI,KAAK;QAAA;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC,eACNjT,OAAA;UAAA6R,QAAA,gBACE7R,OAAA;YAAA6R,QAAA,EAAQ;UAAiB;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACxN,IAAI,CAACwO,cAAc,IAAI,KAAK;QAAA;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC,eACNjT,OAAA;UAAA6R,QAAA,gBACE7R,OAAA;YAAA6R,QAAA,EAAQ;UAAS;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACxN,IAAI,CAACyO,QAAQ,IAAI,KAAK;QAAA;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC,eACNjT,OAAA;UAAA6R,QAAA,gBACE7R,OAAA;YAAA6R,QAAA,EAAQ;UAAiB;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACxN,IAAI,CAAC0O,mBAAmB,IAAI,KAAK;QAAA;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE,CAAC,eACNjT,OAAA;UAAA6R,QAAA,gBACE7R,OAAA;YAAA6R,QAAA,EAAQ;UAAmB;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACxN,IAAI,CAAC2O,qBAAqB,IAAI,KAAK;QAAA;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtE,CAAC,eACNjT,OAAA;UAAA6R,QAAA,gBACE7R,OAAA;YAAA6R,QAAA,EAAQ;UAAO;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACxN,IAAI,CAACqH,YAAY,IAAI7N,iBAAiB,CAACwG,IAAI,CAACoH,MAAM,CAAC,IAAI,OAAO;QAAA;UAAAiG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrF,CAAC,eACNjT,OAAA;UAAA6R,QAAA,gBACE7R,OAAA;YAAA6R,QAAA,EAAQ;UAAK;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACxN,IAAI,CAACR,IAAI,IAAI,KAAK;QAAA;UAAA6N,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAEDjT,OAAA;MAAK4R,SAAS,EAAC,qBAAqB;MAAAC,QAAA,eAClC7R,OAAA,CAACzB,MAAM;QAACoW,KAAK,EAAEtP;MAAY;QAAAyN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC,eAoCNjT,OAAA,CAACN,sBAAsB;MACrB8C,OAAO,EAAE+D,kBAAmB;MAC5BqO,MAAM,EAAE9E,wBAAyB;MACjC+E,SAAS,EAAEtF,qBAAsB;MACjC5J,OAAO,EAAEc,WAAY;MACrB+H,KAAK,EAAC,sBAAsB;MAC5B9B,OAAO,EAAE,uDAAuD,CAAAjH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiM,WAAW,KAAI,cAAc;IAAwB;MAAAoB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5H,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AALA7N,EAAA,CAniCaJ,wBAAwB;EAAA,QAgClBtG,WAAW,EAG5BG,iBAAiB;AAAA;AAAAiW,EAAA,GAnCN9P,wBAAwB;AAyiCrC,eAAe0J,uCAAuCA,CAACrN,MAAM,EAAEsN,SAAS,EAAE1J,IAAI,EAAE;EAC9EzD,OAAO,CAACE,GAAG,CAAC,yDAAyD,EAAE;IAAEiN,SAAS;IAAE1J;EAAK,CAAC,CAAC;;EAE3F;EACA,MAAM8P,kBAAkB,GAAG1T,MAAM,CAACoB,eAAe,CAAC,CAAC,CAACY,MAAM,CAAE2R,CAAC,IAAK;IAChE,IAAIA,CAAC,CAACC,OAAO,CAAC,CAAC,KAAK,MAAM,EAAE,OAAO,KAAK;;IAExC;IACA,OAAOD,CAAC,CAACE,qBAAqB,IAAIF,CAAC,CAACG,oBAAoB,IAAIH,CAAC,CAACI,oBAAoB;EACpF,CAAC,CAAC;EAEF5T,OAAO,CAACE,GAAG,CACT,qCAAqC,EACrCqT,kBAAkB,CAAC5U,GAAG,CAAEkV,CAAC,KAAM;IAC7B7U,IAAI,EAAE6U,CAAC,CAAC7U,IAAI;IACZ8U,gBAAgB,EAAED,CAAC,CAACH,qBAAqB;IACzCK,eAAe,EAAEF,CAAC,CAACF,oBAAoB;IACvCK,eAAe,EAAEH,CAAC,CAACD;EACrB,CAAC,CAAC,CACJ,CAAC;;EAED;EACA,MAAMK,qBAAqB,GAAG,CAAC,CAAC;EAChCV,kBAAkB,CAACzU,OAAO,CAAEoV,SAAS,IAAK;IACxC;IACA,IAAIC,YAAY;IAChB,IAAIC,KAAK;IAET,IAAIF,SAAS,CAACR,qBAAqB,EAAE;MACnCS,YAAY,GAAGD,SAAS,CAACR,qBAAqB;MAC9CU,KAAK,GAAGvW,gBAAgB,CAACwW,QAAQ;IACnC,CAAC,MAAM,IAAIH,SAAS,CAACP,oBAAoB,EAAE;MACzCQ,YAAY,GAAGD,SAAS,CAACP,oBAAoB;MAC7CS,KAAK,GAAGvW,gBAAgB,CAACyW,aAAa;IACxC,CAAC,MAAM,IAAIJ,SAAS,CAACN,oBAAoB,EAAE;MACzCO,YAAY,GAAGD,SAAS,CAACN,oBAAoB;MAC7CQ,KAAK,GAAGvW,gBAAgB,CAAC0W,aAAa;IACxC;IAEA,IAAIJ,YAAY,EAAE;MAChB,IAAI,CAACF,qBAAqB,CAACE,YAAY,CAAC,EAAE;QACxCF,qBAAqB,CAACE,YAAY,CAAC,GAAG,EAAE;MAC1C;MACA;MACAF,qBAAqB,CAACE,YAAY,CAAC,CAACK,IAAI,CAAC;QAAEN,SAAS;QAAEE;MAAM,CAAC,CAAC;IAChE;EACF,CAAC,CAAC;;EAEF;EACA,MAAMK,YAAY,GAAG5U,MAAM,CAACoB,eAAe,CAAC,CAAC;EAC7CjB,OAAO,CAACE,GAAG,CACT,6BAA6B,EAC7BuU,YAAY,CAAC9V,GAAG,CAAE6U,CAAC,KAAM;IACvBxU,IAAI,EAAEwU,CAAC,CAACxU,IAAI;IACZ4I,IAAI,EAAE4L,CAAC,CAACC,OAAO,CAAC,CAAC;IACjBiB,YAAY,EAAElB,CAAC,CAACkB,YAAY;IAC5BC,eAAe,EAAE,CAAC,CAACnB,CAAC,CAACkB;EACvB,CAAC,CAAC,CACJ,CAAC;;EAED;EACA,MAAME,qBAAqB,GAAG/U,MAAM,CAACoB,eAAe,CAAC,CAAC,CAACY,MAAM,CAAE2R,CAAC,IAAKA,CAAC,CAACC,OAAO,CAAC,CAAC,KAAK,UAAU,IAAID,CAAC,CAACkB,YAAY,CAAC;EAClH1U,OAAO,CAACE,GAAG,CACT,uCAAuC,EACvC0U,qBAAqB,CAACjW,GAAG,CAAEkW,CAAC,KAAM;IAAE7V,IAAI,EAAE6V,CAAC,CAAC7V,IAAI;IAAE8V,KAAK,EAAED,CAAC,CAACH;EAAa,CAAC,CAAC,CAC5E,CAAC;;EAED;EACA,KAAK,MAAMK,QAAQ,IAAIH,qBAAqB,EAAE;IAC5C,IAAI;MACF5U,OAAO,CAACE,GAAG,CAAC,qCAAqCuD,IAAI,MAAMsR,QAAQ,CAACL,YAAY,oBAAoBK,QAAQ,CAAC/V,IAAI,GAAG,CAAC;MACrH,MAAMgW,OAAO,GAAG,MAAMzX,iCAAiC,CAAC0X,iBAAiB,CAACxR,IAAI,EAAEsR,QAAQ,CAACL,YAAY,CAAC;MACtGK,QAAQ,CAACG,OAAO,GAAGF,OAAO;MAC1BhV,OAAO,CAACE,GAAG,CAAC,YAAY8U,OAAO,CAAC1U,MAAM,yBAAyByU,QAAQ,CAAC/V,IAAI,GAAG,EAAEgW,OAAO,CAAC;IAC3F,CAAC,CAAC,OAAO7T,KAAK,EAAE;MACdnB,OAAO,CAACmB,KAAK,CAAC,wCAAwC4T,QAAQ,CAAC/V,IAAI,GAAG,EAAEmC,KAAK,CAAC;IAChF;EACF;;EAEA;EACAtB,MAAM,CAAC6L,cAAc,CAACvB,GAAG,CAAC,OAAOO,MAAM,EAAEN,OAAO,KAAK;IACnD,MAAM+K,wCAAwC,CAACzK,MAAM,EAAEN,OAAO,EAAE6J,qBAAqB,EAAE9G,SAAS,EAAE1J,IAAI,CAAC;EACzG,CAAC,CAAC;;EAEF;EACAhD,MAAM,CAACC,IAAI,CAACuT,qBAAqB,CAAC,CAACnV,OAAO,CAAEqV,YAAY,IAAK;IAC3D,MAAMY,QAAQ,GAAGlV,MAAM,CAACuV,iBAAiB,CAACjB,YAAY,CAAC;IACvD,IAAIY,QAAQ,IAAIA,QAAQ,CAACM,KAAK,EAAE;MAC9B;MACAF,wCAAwC,CAACtV,MAAM,EAAE;QAAEb,IAAI,EAAEmV,YAAY;QAAEkB,KAAK,EAAEN,QAAQ,CAACM;MAAM,CAAC,EAAEpB,qBAAqB,EAAE9G,SAAS,EAAE1J,IAAI,CAAC;IACzI;EACF,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe0R,wCAAwCA,CAACzK,MAAM,EAAEN,OAAO,EAAE6J,qBAAqB,EAAE9G,SAAS,EAAE1J,IAAI,EAAE;EAC/G,MAAM0Q,YAAY,GAAG/J,OAAO,CAACpL,IAAI;EACjC,MAAMsW,cAAc,GAAGlL,OAAO,CAACiL,KAAK;;EAEpC;EACA,MAAME,0BAA0B,GAAGtB,qBAAqB,CAACE,YAAY,CAAC;EACtE,IAAI,CAACoB,0BAA0B,IAAIA,0BAA0B,CAACjV,MAAM,KAAK,CAAC,EAAE;IAC1E;EACF;EAEA,MAAMyU,QAAQ,GAAGrK,MAAM,CAAC0K,iBAAiB,CAACjB,YAAY,CAAC;EACvD,IAAI,CAACY,QAAQ,IAAI,CAACA,QAAQ,CAACL,YAAY,EAAE;IACvC;EACF;EAEA,IAAI;IACF,IAAI,CAACY,cAAc,EAAE;MACnB;MACAC,0BAA0B,CAACzW,OAAO,CAAC,CAAC;QAAEoV;MAAU,CAAC,KAAK;QACpDxJ,MAAM,CAAC8K,QAAQ,CAACtB,SAAS,CAAClV,IAAI,EAAE,EAAE,CAAC;MACrC,CAAC,CAAC;MACF;IACF;;IAEA;IACAuW,0BAA0B,CAACzW,OAAO,CAAC,CAAC;MAAEoV;IAAU,CAAC,KAAK;MACpDxJ,MAAM,CAAC8K,QAAQ,CAACtB,SAAS,CAAClV,IAAI,EAAE,YAAY,CAAC;IAC/C,CAAC,CAAC;;IAEF;IACA,KAAK,MAAM;MAAEkV,SAAS;MAAEE;IAAM,CAAC,IAAImB,0BAA0B,EAAE;MAC7D,IAAI;QACF;QACA,MAAME,SAAS,GAAG,MAAMlY,iCAAiC,CAACmY,4BAA4B,CACpFjS,IAAI,EACJ2Q,KAAK,EACLW,QAAQ,CAACL,YAAY,EACrBY,cAAc,EACdnI,SACF,CAAC;;QAED;QACA,MAAMwI,YAAY,GAAGF,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK9O,SAAS,GAAGiP,MAAM,CAACH,SAAS,CAAC,GAAG,mBAAmB;QAC5G/K,MAAM,CAAC8K,QAAQ,CAACtB,SAAS,CAAClV,IAAI,EAAE2W,YAAY,CAAC;QAE7C3V,OAAO,CAACE,GAAG,CAAC,kBAAkBgU,SAAS,CAAClV,IAAI,gBAAgB2W,YAAY,YAAYvB,KAAK,GAAG,CAAC;MAC/F,CAAC,CAAC,OAAOjT,KAAK,EAAE;QACdnB,OAAO,CAACmB,KAAK,CAAC,2BAA2B+S,SAAS,CAAClV,IAAI,YAAYoV,KAAK,IAAI,EAAEjT,KAAK,CAAC;QACpFuJ,MAAM,CAAC8K,QAAQ,CAACtB,SAAS,CAAClV,IAAI,EAAE,oBAAoB,CAAC;MACvD;IACF;EACF,CAAC,CAAC,OAAOmC,KAAK,EAAE;IACdnB,OAAO,CAACmB,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;;IAE9D;IACAoU,0BAA0B,CAACzW,OAAO,CAAC,CAAC;MAAEoV;IAAU,CAAC,KAAK;MACpDxJ,MAAM,CAAC8K,QAAQ,CAACtB,SAAS,CAAClV,IAAI,EAAE,oBAAoB,CAAC;IACvD,CAAC,CAAC;EACJ;AACF;AAEA,eAAewE,wBAAwB;AAAC,IAAA8P,EAAA;AAAAuC,YAAA,CAAAvC,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}