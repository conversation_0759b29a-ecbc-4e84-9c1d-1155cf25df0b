{"ast": null, "code": "var _jsxFileName = \"C:\\\\git\\\\Partner-Site\\\\BdoPartner.Plans\\\\Development\\\\ReactClientApp\\\\src\\\\pages\\\\userprofile.jsx\",\n  _s = $RefreshSig$();\nimport React, { useContext } from \"react\";\nimport { AuthContext } from \"../core/auth/components/authProvider\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport const UserProfile = () => {\n  _s();\n  const authService = useContext(AuthContext);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"It is user profile page\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this), \"Current logon user name:\", authService.getUser().userName]\n  }, void 0, true);\n};\n_s(UserProfile, \"TLh9duf52wwWZvm9lOaqSx6A0bw=\");\n_c = UserProfile;\nvar _c;\n$RefreshReg$(_c, \"UserProfile\");", "map": {"version": 3, "names": ["React", "useContext", "AuthContext", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "UserProfile", "_s", "authService", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getUser", "userName", "_c", "$RefreshReg$"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/pages/userprofile.jsx"], "sourcesContent": ["import React, { useContext } from \"react\";\r\nimport { AuthContext } from \"../core/auth/components/authProvider\";\r\n\r\nexport const UserProfile = () => {\r\n  const authService = useContext(AuthContext);\r\n  return (\r\n    <>\r\n      <h2>It is user profile page</h2>Current logon user name:\r\n      {authService.getUser().userName}\r\n    </>\r\n  );\r\n};\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,UAAU,QAAQ,OAAO;AACzC,SAASC,WAAW,QAAQ,sCAAsC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEnE,OAAO,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAMC,WAAW,GAAGR,UAAU,CAACC,WAAW,CAAC;EAC3C,oBACEE,OAAA,CAAAE,SAAA;IAAAI,QAAA,gBACEN,OAAA;MAAAM,QAAA,EAAI;IAAuB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,4BAChC,EAACL,WAAW,CAACM,OAAO,CAAC,CAAC,CAACC,QAAQ;EAAA,eAC/B,CAAC;AAEP,CAAC;AAACR,EAAA,CARWD,WAAW;AAAAU,EAAA,GAAXV,WAAW;AAAA,IAAAU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}