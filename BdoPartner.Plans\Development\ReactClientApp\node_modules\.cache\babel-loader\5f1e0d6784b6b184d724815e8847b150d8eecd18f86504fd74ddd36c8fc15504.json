{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport ReactDOM from 'react-dom';\nimport PrimeReact, { PrimeReactContext } from 'primereact/api';\nimport { useMountEffect, useUpdateEffect, useUnmountEffect } from 'primereact/hooks';\nimport { ObjectUtils, DomHandler } from 'primereact/utils';\nfunction _arrayWithHoles(r) {\n  if (Array.isArray(r)) return r;\n}\nfunction _iterableToArrayLimit(r, l) {\n  var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"];\n  if (null != t) {\n    var e,\n      n,\n      i,\n      u,\n      a = [],\n      f = !0,\n      o = !1;\n    try {\n      if (i = (t = t.call(r)).next, 0 === l) {\n        if (Object(t) !== t) return;\n        f = !1;\n      } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0);\n    } catch (r) {\n      o = !0, n = r;\n    } finally {\n      try {\n        if (!f && null != t[\"return\"] && (u = t[\"return\"](), Object(u) !== u)) return;\n      } finally {\n        if (o) throw n;\n      }\n    }\n    return a;\n  }\n}\nfunction _arrayLikeToArray(r, a) {\n  (null == a || a > r.length) && (a = r.length);\n  for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e];\n  return n;\n}\nfunction _unsupportedIterableToArray(r, a) {\n  if (r) {\n    if (\"string\" == typeof r) return _arrayLikeToArray(r, a);\n    var t = {}.toString.call(r).slice(8, -1);\n    return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0;\n  }\n}\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _slicedToArray(r, e) {\n  return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest();\n}\nvar PortalBase = {\n  defaultProps: {\n    __TYPE: 'Portal',\n    element: null,\n    appendTo: null,\n    visible: false,\n    onMounted: null,\n    onUnmounted: null,\n    children: undefined\n  },\n  getProps: function getProps(props) {\n    return ObjectUtils.getMergedProps(props, PortalBase.defaultProps);\n  },\n  getOtherProps: function getOtherProps(props) {\n    return ObjectUtils.getDiffProps(props, PortalBase.defaultProps);\n  }\n};\nvar Portal = /*#__PURE__*/React.memo(function (inProps) {\n  var props = PortalBase.getProps(inProps);\n  var context = React.useContext(PrimeReactContext);\n  var _React$useState = React.useState(props.visible && DomHandler.isClient()),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    mountedState = _React$useState2[0],\n    setMountedState = _React$useState2[1];\n  useMountEffect(function () {\n    if (DomHandler.isClient() && !mountedState) {\n      setMountedState(true);\n      props.onMounted && props.onMounted();\n    }\n  });\n  useUpdateEffect(function () {\n    props.onMounted && props.onMounted();\n  }, [mountedState]);\n  useUnmountEffect(function () {\n    props.onUnmounted && props.onUnmounted();\n  });\n  var element = props.element || props.children;\n  if (element && mountedState) {\n    var appendTo = props.appendTo || context && context.appendTo || PrimeReact.appendTo;\n    if (ObjectUtils.isFunction(appendTo)) {\n      appendTo = appendTo();\n    }\n    if (!appendTo) {\n      appendTo = document.body;\n    }\n    return appendTo === 'self' ? element : /*#__PURE__*/ReactDOM.createPortal(element, appendTo);\n  }\n  return null;\n});\nPortal.displayName = 'Portal';\nexport { Portal };", "map": {"version": 3, "names": ["React", "ReactDOM", "PrimeReact", "PrimeReactContext", "useMountEffect", "useUpdateEffect", "useUnmountEffect", "ObjectUtils", "<PERSON><PERSON><PERSON><PERSON>", "_arrayWithHoles", "r", "Array", "isArray", "_iterableToArrayLimit", "l", "t", "Symbol", "iterator", "e", "n", "i", "u", "a", "f", "o", "call", "next", "Object", "done", "push", "value", "length", "_arrayLikeToArray", "_unsupportedIterableToArray", "toString", "slice", "constructor", "name", "from", "test", "_nonIterableRest", "TypeError", "_slicedToArray", "PortalBase", "defaultProps", "__TYPE", "element", "appendTo", "visible", "onMounted", "onUnmounted", "children", "undefined", "getProps", "props", "getMergedProps", "getOtherProps", "getDiffProps", "Portal", "memo", "inProps", "context", "useContext", "_React$useState", "useState", "isClient", "_React$useState2", "mountedState", "setMountedState", "isFunction", "document", "body", "createPortal", "displayName"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/node_modules/primereact/portal/portal.esm.js"], "sourcesContent": ["'use client';\nimport * as React from 'react';\nimport ReactDOM from 'react-dom';\nimport PrimeReact, { PrimeReactContext } from 'primereact/api';\nimport { useMountEffect, useUpdateEffect, useUnmountEffect } from 'primereact/hooks';\nimport { ObjectUtils, DomHandler } from 'primereact/utils';\n\nfunction _arrayWithHoles(r) {\n  if (Array.isArray(r)) return r;\n}\n\nfunction _iterableToArrayLimit(r, l) {\n  var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"];\n  if (null != t) {\n    var e,\n      n,\n      i,\n      u,\n      a = [],\n      f = !0,\n      o = !1;\n    try {\n      if (i = (t = t.call(r)).next, 0 === l) {\n        if (Object(t) !== t) return;\n        f = !1;\n      } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0);\n    } catch (r) {\n      o = !0, n = r;\n    } finally {\n      try {\n        if (!f && null != t[\"return\"] && (u = t[\"return\"](), Object(u) !== u)) return;\n      } finally {\n        if (o) throw n;\n      }\n    }\n    return a;\n  }\n}\n\nfunction _arrayLikeToArray(r, a) {\n  (null == a || a > r.length) && (a = r.length);\n  for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e];\n  return n;\n}\n\nfunction _unsupportedIterableToArray(r, a) {\n  if (r) {\n    if (\"string\" == typeof r) return _arrayLikeToArray(r, a);\n    var t = {}.toString.call(r).slice(8, -1);\n    return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0;\n  }\n}\n\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nfunction _slicedToArray(r, e) {\n  return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest();\n}\n\nvar PortalBase = {\n  defaultProps: {\n    __TYPE: 'Portal',\n    element: null,\n    appendTo: null,\n    visible: false,\n    onMounted: null,\n    onUnmounted: null,\n    children: undefined\n  },\n  getProps: function getProps(props) {\n    return ObjectUtils.getMergedProps(props, PortalBase.defaultProps);\n  },\n  getOtherProps: function getOtherProps(props) {\n    return ObjectUtils.getDiffProps(props, PortalBase.defaultProps);\n  }\n};\n\nvar Portal = /*#__PURE__*/React.memo(function (inProps) {\n  var props = PortalBase.getProps(inProps);\n  var context = React.useContext(PrimeReactContext);\n  var _React$useState = React.useState(props.visible && DomHandler.isClient()),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    mountedState = _React$useState2[0],\n    setMountedState = _React$useState2[1];\n  useMountEffect(function () {\n    if (DomHandler.isClient() && !mountedState) {\n      setMountedState(true);\n      props.onMounted && props.onMounted();\n    }\n  });\n  useUpdateEffect(function () {\n    props.onMounted && props.onMounted();\n  }, [mountedState]);\n  useUnmountEffect(function () {\n    props.onUnmounted && props.onUnmounted();\n  });\n  var element = props.element || props.children;\n  if (element && mountedState) {\n    var appendTo = props.appendTo || context && context.appendTo || PrimeReact.appendTo;\n    if (ObjectUtils.isFunction(appendTo)) {\n      appendTo = appendTo();\n    }\n    if (!appendTo) {\n      appendTo = document.body;\n    }\n    return appendTo === 'self' ? element : /*#__PURE__*/ReactDOM.createPortal(element, appendTo);\n  }\n  return null;\n});\nPortal.displayName = 'Portal';\n\nexport { Portal };\n"], "mappings": "AAAA,YAAY;;AACZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,QAAQ,MAAM,WAAW;AAChC,OAAOC,UAAU,IAAIC,iBAAiB,QAAQ,gBAAgB;AAC9D,SAASC,cAAc,EAAEC,eAAe,EAAEC,gBAAgB,QAAQ,kBAAkB;AACpF,SAASC,WAAW,EAAEC,UAAU,QAAQ,kBAAkB;AAE1D,SAASC,eAAeA,CAACC,CAAC,EAAE;EAC1B,IAAIC,KAAK,CAACC,OAAO,CAACF,CAAC,CAAC,EAAE,OAAOA,CAAC;AAChC;AAEA,SAASG,qBAAqBA,CAACH,CAAC,EAAEI,CAAC,EAAE;EACnC,IAAIC,CAAC,GAAG,IAAI,IAAIL,CAAC,GAAG,IAAI,GAAG,WAAW,IAAI,OAAOM,MAAM,IAAIN,CAAC,CAACM,MAAM,CAACC,QAAQ,CAAC,IAAIP,CAAC,CAAC,YAAY,CAAC;EAChG,IAAI,IAAI,IAAIK,CAAC,EAAE;IACb,IAAIG,CAAC;MACHC,CAAC;MACDC,CAAC;MACDC,CAAC;MACDC,CAAC,GAAG,EAAE;MACNC,CAAC,GAAG,CAAC,CAAC;MACNC,CAAC,GAAG,CAAC,CAAC;IACR,IAAI;MACF,IAAIJ,CAAC,GAAG,CAACL,CAAC,GAAGA,CAAC,CAACU,IAAI,CAACf,CAAC,CAAC,EAAEgB,IAAI,EAAE,CAAC,KAAKZ,CAAC,EAAE;QACrC,IAAIa,MAAM,CAACZ,CAAC,CAAC,KAAKA,CAAC,EAAE;QACrBQ,CAAC,GAAG,CAAC,CAAC;MACR,CAAC,MAAM,OAAO,EAAEA,CAAC,GAAG,CAACL,CAAC,GAAGE,CAAC,CAACK,IAAI,CAACV,CAAC,CAAC,EAAEa,IAAI,CAAC,KAAKN,CAAC,CAACO,IAAI,CAACX,CAAC,CAACY,KAAK,CAAC,EAAER,CAAC,CAACS,MAAM,KAAKjB,CAAC,CAAC,EAAES,CAAC,GAAG,CAAC,CAAC,CAAC;IACzF,CAAC,CAAC,OAAOb,CAAC,EAAE;MACVc,CAAC,GAAG,CAAC,CAAC,EAAEL,CAAC,GAAGT,CAAC;IACf,CAAC,SAAS;MACR,IAAI;QACF,IAAI,CAACa,CAAC,IAAI,IAAI,IAAIR,CAAC,CAAC,QAAQ,CAAC,KAAKM,CAAC,GAAGN,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAEY,MAAM,CAACN,CAAC,CAAC,KAAKA,CAAC,CAAC,EAAE;MACzE,CAAC,SAAS;QACR,IAAIG,CAAC,EAAE,MAAML,CAAC;MAChB;IACF;IACA,OAAOG,CAAC;EACV;AACF;AAEA,SAASU,iBAAiBA,CAACtB,CAAC,EAAEY,CAAC,EAAE;EAC/B,CAAC,IAAI,IAAIA,CAAC,IAAIA,CAAC,GAAGZ,CAAC,CAACqB,MAAM,MAAMT,CAAC,GAAGZ,CAAC,CAACqB,MAAM,CAAC;EAC7C,KAAK,IAAIb,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGR,KAAK,CAACW,CAAC,CAAC,EAAEJ,CAAC,GAAGI,CAAC,EAAEJ,CAAC,EAAE,EAAEC,CAAC,CAACD,CAAC,CAAC,GAAGR,CAAC,CAACQ,CAAC,CAAC;EACrD,OAAOC,CAAC;AACV;AAEA,SAASc,2BAA2BA,CAACvB,CAAC,EAAEY,CAAC,EAAE;EACzC,IAAIZ,CAAC,EAAE;IACL,IAAI,QAAQ,IAAI,OAAOA,CAAC,EAAE,OAAOsB,iBAAiB,CAACtB,CAAC,EAAEY,CAAC,CAAC;IACxD,IAAIP,CAAC,GAAG,CAAC,CAAC,CAACmB,QAAQ,CAACT,IAAI,CAACf,CAAC,CAAC,CAACyB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACxC,OAAO,QAAQ,KAAKpB,CAAC,IAAIL,CAAC,CAAC0B,WAAW,KAAKrB,CAAC,GAAGL,CAAC,CAAC0B,WAAW,CAACC,IAAI,CAAC,EAAE,KAAK,KAAKtB,CAAC,IAAI,KAAK,KAAKA,CAAC,GAAGJ,KAAK,CAAC2B,IAAI,CAAC5B,CAAC,CAAC,GAAG,WAAW,KAAKK,CAAC,IAAI,0CAA0C,CAACwB,IAAI,CAACxB,CAAC,CAAC,GAAGiB,iBAAiB,CAACtB,CAAC,EAAEY,CAAC,CAAC,GAAG,KAAK,CAAC;EAC7N;AACF;AAEA,SAASkB,gBAAgBA,CAAA,EAAG;EAC1B,MAAM,IAAIC,SAAS,CAAC,2IAA2I,CAAC;AAClK;AAEA,SAASC,cAAcA,CAAChC,CAAC,EAAEQ,CAAC,EAAE;EAC5B,OAAOT,eAAe,CAACC,CAAC,CAAC,IAAIG,qBAAqB,CAACH,CAAC,EAAEQ,CAAC,CAAC,IAAIe,2BAA2B,CAACvB,CAAC,EAAEQ,CAAC,CAAC,IAAIsB,gBAAgB,CAAC,CAAC;AACrH;AAEA,IAAIG,UAAU,GAAG;EACfC,YAAY,EAAE;IACZC,MAAM,EAAE,QAAQ;IAChBC,OAAO,EAAE,IAAI;IACbC,QAAQ,EAAE,IAAI;IACdC,OAAO,EAAE,KAAK;IACdC,SAAS,EAAE,IAAI;IACfC,WAAW,EAAE,IAAI;IACjBC,QAAQ,EAAEC;EACZ,CAAC;EACDC,QAAQ,EAAE,SAASA,QAAQA,CAACC,KAAK,EAAE;IACjC,OAAO/C,WAAW,CAACgD,cAAc,CAACD,KAAK,EAAEX,UAAU,CAACC,YAAY,CAAC;EACnE,CAAC;EACDY,aAAa,EAAE,SAASA,aAAaA,CAACF,KAAK,EAAE;IAC3C,OAAO/C,WAAW,CAACkD,YAAY,CAACH,KAAK,EAAEX,UAAU,CAACC,YAAY,CAAC;EACjE;AACF,CAAC;AAED,IAAIc,MAAM,GAAG,aAAa1D,KAAK,CAAC2D,IAAI,CAAC,UAAUC,OAAO,EAAE;EACtD,IAAIN,KAAK,GAAGX,UAAU,CAACU,QAAQ,CAACO,OAAO,CAAC;EACxC,IAAIC,OAAO,GAAG7D,KAAK,CAAC8D,UAAU,CAAC3D,iBAAiB,CAAC;EACjD,IAAI4D,eAAe,GAAG/D,KAAK,CAACgE,QAAQ,CAACV,KAAK,CAACN,OAAO,IAAIxC,UAAU,CAACyD,QAAQ,CAAC,CAAC,CAAC;IAC1EC,gBAAgB,GAAGxB,cAAc,CAACqB,eAAe,EAAE,CAAC,CAAC;IACrDI,YAAY,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAClCE,eAAe,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACvC9D,cAAc,CAAC,YAAY;IACzB,IAAII,UAAU,CAACyD,QAAQ,CAAC,CAAC,IAAI,CAACE,YAAY,EAAE;MAC1CC,eAAe,CAAC,IAAI,CAAC;MACrBd,KAAK,CAACL,SAAS,IAAIK,KAAK,CAACL,SAAS,CAAC,CAAC;IACtC;EACF,CAAC,CAAC;EACF5C,eAAe,CAAC,YAAY;IAC1BiD,KAAK,CAACL,SAAS,IAAIK,KAAK,CAACL,SAAS,CAAC,CAAC;EACtC,CAAC,EAAE,CAACkB,YAAY,CAAC,CAAC;EAClB7D,gBAAgB,CAAC,YAAY;IAC3BgD,KAAK,CAACJ,WAAW,IAAII,KAAK,CAACJ,WAAW,CAAC,CAAC;EAC1C,CAAC,CAAC;EACF,IAAIJ,OAAO,GAAGQ,KAAK,CAACR,OAAO,IAAIQ,KAAK,CAACH,QAAQ;EAC7C,IAAIL,OAAO,IAAIqB,YAAY,EAAE;IAC3B,IAAIpB,QAAQ,GAAGO,KAAK,CAACP,QAAQ,IAAIc,OAAO,IAAIA,OAAO,CAACd,QAAQ,IAAI7C,UAAU,CAAC6C,QAAQ;IACnF,IAAIxC,WAAW,CAAC8D,UAAU,CAACtB,QAAQ,CAAC,EAAE;MACpCA,QAAQ,GAAGA,QAAQ,CAAC,CAAC;IACvB;IACA,IAAI,CAACA,QAAQ,EAAE;MACbA,QAAQ,GAAGuB,QAAQ,CAACC,IAAI;IAC1B;IACA,OAAOxB,QAAQ,KAAK,MAAM,GAAGD,OAAO,GAAG,aAAa7C,QAAQ,CAACuE,YAAY,CAAC1B,OAAO,EAAEC,QAAQ,CAAC;EAC9F;EACA,OAAO,IAAI;AACb,CAAC,CAAC;AACFW,MAAM,CAACe,WAAW,GAAG,QAAQ;AAE7B,SAASf,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}