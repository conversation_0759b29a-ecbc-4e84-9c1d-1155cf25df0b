{"ast": null, "code": "import { innerFrom } from '../observable/innerFrom';\nimport { Subject } from '../Subject';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function repeatWhen(notifier) {\n  return operate(function (source, subscriber) {\n    var innerSub;\n    var syncResub = false;\n    var completions$;\n    var isNotifierComplete = false;\n    var isMainComplete = false;\n    var checkComplete = function () {\n      return isMainComplete && isNotifierComplete && (subscriber.complete(), true);\n    };\n    var getCompletionSubject = function () {\n      if (!completions$) {\n        completions$ = new Subject();\n        innerFrom(notifier(completions$)).subscribe(createOperatorSubscriber(subscriber, function () {\n          if (innerSub) {\n            subscribeForRepeatWhen();\n          } else {\n            syncResub = true;\n          }\n        }, function () {\n          isNotifierComplete = true;\n          checkComplete();\n        }));\n      }\n      return completions$;\n    };\n    var subscribeForRepeatWhen = function () {\n      isMainComplete = false;\n      innerSub = source.subscribe(createOperatorSubscriber(subscriber, undefined, function () {\n        isMainComplete = true;\n        !checkComplete() && getCompletionSubject().next();\n      }));\n      if (syncResub) {\n        innerSub.unsubscribe();\n        innerSub = null;\n        syncResub = false;\n        subscribeForRepeatWhen();\n      }\n    };\n    subscribeForRepeatWhen();\n  });\n}", "map": {"version": 3, "names": ["innerFrom", "Subject", "operate", "createOperatorSubscriber", "repeatWhen", "notifier", "source", "subscriber", "innerSub", "syncResub", "completions$", "isNotifierComplete", "isMainComplete", "checkComplete", "complete", "getCompletionSubject", "subscribe", "subscribeForRepeatWhen", "undefined", "next", "unsubscribe"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\repeatWhen.ts"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { innerFrom } from '../observable/innerFrom';\nimport { Subject } from '../Subject';\nimport { Subscription } from '../Subscription';\n\nimport { MonoTypeOperatorFunction, ObservableInput } from '../types';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\n\n/**\n * Returns an Observable that mirrors the source Observable with the exception of a `complete`. If the source\n * Observable calls `complete`, this method will emit to the Observable returned from `notifier`. If that Observable\n * calls `complete` or `error`, then this method will call `complete` or `error` on the child subscription. Otherwise\n * this method will resubscribe to the source Observable.\n *\n * ![](repeatWhen.png)\n *\n * ## Example\n *\n * Repeat a message stream on click\n *\n * ```ts\n * import { of, fromEvent, repeatWhen } from 'rxjs';\n *\n * const source = of('Repeat message');\n * const documentClick$ = fromEvent(document, 'click');\n *\n * const result = source.pipe(repeatWhen(() => documentClick$));\n *\n * result.subscribe(data => console.log(data))\n * ```\n *\n * @see {@link repeat}\n * @see {@link retry}\n * @see {@link retryWhen}\n *\n * @param notifier Function that receives an Observable of notifications with\n * which a user can `complete` or `error`, aborting the repetition.\n * @return A function that returns an Observable that mirrors the source\n * Observable with the exception of a `complete`.\n * @deprecated Will be removed in v9 or v10. Use {@link repeat}'s {@link RepeatConfig#delay delay} option instead.\n * Instead of `repeatWhen(() => notify$)`, use: `repeat({ delay: () => notify$ })`.\n */\nexport function repeatWhen<T>(notifier: (notifications: Observable<void>) => ObservableInput<any>): MonoTypeOperatorFunction<T> {\n  return operate((source, subscriber) => {\n    let innerSub: Subscription | null;\n    let syncResub = false;\n    let completions$: Subject<void>;\n    let isNotifierComplete = false;\n    let isMainComplete = false;\n\n    /**\n     * Checks to see if we can complete the result, completes it, and returns `true` if it was completed.\n     */\n    const checkComplete = () => isMainComplete && isNotifierComplete && (subscriber.complete(), true);\n    /**\n     * Gets the subject to send errors through. If it doesn't exist,\n     * we know we need to setup the notifier.\n     */\n    const getCompletionSubject = () => {\n      if (!completions$) {\n        completions$ = new Subject();\n\n        // If the call to `notifier` throws, it will be caught by the OperatorSubscriber\n        // In the main subscription -- in `subscribeForRepeatWhen`.\n        innerFrom(notifier(completions$)).subscribe(\n          createOperatorSubscriber(\n            subscriber,\n            () => {\n              if (innerSub) {\n                subscribeForRepeatWhen();\n              } else {\n                // If we don't have an innerSub yet, that's because the inner subscription\n                // call hasn't even returned yet. We've arrived here synchronously.\n                // So we flag that we want to resub, such that we can ensure finalization\n                // happens before we resubscribe.\n                syncResub = true;\n              }\n            },\n            () => {\n              isNotifierComplete = true;\n              checkComplete();\n            }\n          )\n        );\n      }\n      return completions$;\n    };\n\n    const subscribeForRepeatWhen = () => {\n      isMainComplete = false;\n\n      innerSub = source.subscribe(\n        createOperatorSubscriber(subscriber, undefined, () => {\n          isMainComplete = true;\n          // Check to see if we are complete, and complete if so.\n          // If we are not complete. Get the subject. This calls the `notifier` function.\n          // If that function fails, it will throw and `.next()` will not be reached on this\n          // line. The thrown error is caught by the _complete handler in this\n          // `OperatorSubscriber` and handled appropriately.\n          !checkComplete() && getCompletionSubject().next();\n        })\n      );\n\n      if (syncResub) {\n        // Ensure that the inner subscription is torn down before\n        // moving on to the next subscription in the synchronous case.\n        // If we don't do this here, all inner subscriptions will not be\n        // torn down until the entire observable is done.\n        innerSub.unsubscribe();\n        // It is important to null this out. Not only to free up memory, but\n        // to make sure code above knows we are in a subscribing state to\n        // handle synchronous resubscription.\n        innerSub = null;\n        // We may need to do this multiple times, so reset the flags.\n        syncResub = false;\n        // Resubscribe\n        subscribeForRepeatWhen();\n      }\n    };\n\n    // Start the subscription\n    subscribeForRepeatWhen();\n  });\n}\n"], "mappings": "AACA,SAASA,SAAS,QAAQ,yBAAyB;AACnD,SAASC,OAAO,QAAQ,YAAY;AAIpC,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAoC/D,OAAM,SAAUC,UAAUA,CAAIC,QAAmE;EAC/F,OAAOH,OAAO,CAAC,UAACI,MAAM,EAAEC,UAAU;IAChC,IAAIC,QAA6B;IACjC,IAAIC,SAAS,GAAG,KAAK;IACrB,IAAIC,YAA2B;IAC/B,IAAIC,kBAAkB,GAAG,KAAK;IAC9B,IAAIC,cAAc,GAAG,KAAK;IAK1B,IAAMC,aAAa,GAAG,SAAAA,CAAA;MAAM,OAAAD,cAAc,IAAID,kBAAkB,KAAKJ,UAAU,CAACO,QAAQ,EAAE,EAAE,IAAI,CAAC;IAArE,CAAqE;IAKjG,IAAMC,oBAAoB,GAAG,SAAAA,CAAA;MAC3B,IAAI,CAACL,YAAY,EAAE;QACjBA,YAAY,GAAG,IAAIT,OAAO,EAAE;QAI5BD,SAAS,CAACK,QAAQ,CAACK,YAAY,CAAC,CAAC,CAACM,SAAS,CACzCb,wBAAwB,CACtBI,UAAU,EACV;UACE,IAAIC,QAAQ,EAAE;YACZS,sBAAsB,EAAE;WACzB,MAAM;YAKLR,SAAS,GAAG,IAAI;;QAEpB,CAAC,EACD;UACEE,kBAAkB,GAAG,IAAI;UACzBE,aAAa,EAAE;QACjB,CAAC,CACF,CACF;;MAEH,OAAOH,YAAY;IACrB,CAAC;IAED,IAAMO,sBAAsB,GAAG,SAAAA,CAAA;MAC7BL,cAAc,GAAG,KAAK;MAEtBJ,QAAQ,GAAGF,MAAM,CAACU,SAAS,CACzBb,wBAAwB,CAACI,UAAU,EAAEW,SAAS,EAAE;QAC9CN,cAAc,GAAG,IAAI;QAMrB,CAACC,aAAa,EAAE,IAAIE,oBAAoB,EAAE,CAACI,IAAI,EAAE;MACnD,CAAC,CAAC,CACH;MAED,IAAIV,SAAS,EAAE;QAKbD,QAAQ,CAACY,WAAW,EAAE;QAItBZ,QAAQ,GAAG,IAAI;QAEfC,SAAS,GAAG,KAAK;QAEjBQ,sBAAsB,EAAE;;IAE5B,CAAC;IAGDA,sBAAsB,EAAE;EAC1B,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}