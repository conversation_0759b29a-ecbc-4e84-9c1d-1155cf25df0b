{"ast": null, "code": "import { DEFAULT_VERSION } from './constants';\nexport default function createMigrate(migrations, config) {\n  var _ref = config || {},\n    debug = _ref.debug;\n  return function (state, currentVersion) {\n    if (!state) {\n      if (process.env.NODE_ENV !== 'production' && debug) console.log('redux-persist: no inbound state, skipping migration');\n      return Promise.resolve(undefined);\n    }\n    var inboundVersion = state._persist && state._persist.version !== undefined ? state._persist.version : DEFAULT_VERSION;\n    if (inboundVersion === currentVersion) {\n      if (process.env.NODE_ENV !== 'production' && debug) console.log('redux-persist: versions match, noop migration');\n      return Promise.resolve(state);\n    }\n    if (inboundVersion > currentVersion) {\n      if (process.env.NODE_ENV !== 'production') console.error('redux-persist: downgrading version is not supported');\n      return Promise.resolve(state);\n    }\n    var migrationKeys = Object.keys(migrations).map(function (ver) {\n      return parseInt(ver);\n    }).filter(function (key) {\n      return currentVersion >= key && key > inboundVersion;\n    }).sort(function (a, b) {\n      return a - b;\n    });\n    if (process.env.NODE_ENV !== 'production' && debug) console.log('redux-persist: migrationKeys', migrationKeys);\n    try {\n      var migratedState = migrationKeys.reduce(function (state, versionKey) {\n        if (process.env.NODE_ENV !== 'production' && debug) console.log('redux-persist: running migration for versionKey', versionKey);\n        return migrations[versionKey](state);\n      }, state);\n      return Promise.resolve(migratedState);\n    } catch (err) {\n      return Promise.reject(err);\n    }\n  };\n}", "map": {"version": 3, "names": ["DEFAULT_VERSION", "createMigrate", "migrations", "config", "_ref", "debug", "state", "currentVersion", "process", "env", "NODE_ENV", "console", "log", "Promise", "resolve", "undefined", "inboundVersion", "_persist", "version", "error", "migrationKeys", "Object", "keys", "map", "ver", "parseInt", "filter", "key", "sort", "a", "b", "migratedState", "reduce", "version<PERSON>ey", "err", "reject"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/node_modules/redux-persist/es/createMigrate.js"], "sourcesContent": ["import { DEFAULT_VERSION } from './constants';\nexport default function createMigrate(migrations, config) {\n  var _ref = config || {},\n      debug = _ref.debug;\n\n  return function (state, currentVersion) {\n    if (!state) {\n      if (process.env.NODE_ENV !== 'production' && debug) console.log('redux-persist: no inbound state, skipping migration');\n      return Promise.resolve(undefined);\n    }\n\n    var inboundVersion = state._persist && state._persist.version !== undefined ? state._persist.version : DEFAULT_VERSION;\n\n    if (inboundVersion === currentVersion) {\n      if (process.env.NODE_ENV !== 'production' && debug) console.log('redux-persist: versions match, noop migration');\n      return Promise.resolve(state);\n    }\n\n    if (inboundVersion > currentVersion) {\n      if (process.env.NODE_ENV !== 'production') console.error('redux-persist: downgrading version is not supported');\n      return Promise.resolve(state);\n    }\n\n    var migrationKeys = Object.keys(migrations).map(function (ver) {\n      return parseInt(ver);\n    }).filter(function (key) {\n      return currentVersion >= key && key > inboundVersion;\n    }).sort(function (a, b) {\n      return a - b;\n    });\n    if (process.env.NODE_ENV !== 'production' && debug) console.log('redux-persist: migrationKeys', migrationKeys);\n\n    try {\n      var migratedState = migrationKeys.reduce(function (state, versionKey) {\n        if (process.env.NODE_ENV !== 'production' && debug) console.log('redux-persist: running migration for versionKey', versionKey);\n        return migrations[versionKey](state);\n      }, state);\n      return Promise.resolve(migratedState);\n    } catch (err) {\n      return Promise.reject(err);\n    }\n  };\n}"], "mappings": "AAAA,SAASA,eAAe,QAAQ,aAAa;AAC7C,eAAe,SAASC,aAAaA,CAACC,UAAU,EAAEC,MAAM,EAAE;EACxD,IAAIC,IAAI,GAAGD,MAAM,IAAI,CAAC,CAAC;IACnBE,KAAK,GAAGD,IAAI,CAACC,KAAK;EAEtB,OAAO,UAAUC,KAAK,EAAEC,cAAc,EAAE;IACtC,IAAI,CAACD,KAAK,EAAE;MACV,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAIL,KAAK,EAAEM,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;MACtH,OAAOC,OAAO,CAACC,OAAO,CAACC,SAAS,CAAC;IACnC;IAEA,IAAIC,cAAc,GAAGV,KAAK,CAACW,QAAQ,IAAIX,KAAK,CAACW,QAAQ,CAACC,OAAO,KAAKH,SAAS,GAAGT,KAAK,CAACW,QAAQ,CAACC,OAAO,GAAGlB,eAAe;IAEtH,IAAIgB,cAAc,KAAKT,cAAc,EAAE;MACrC,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAIL,KAAK,EAAEM,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;MAChH,OAAOC,OAAO,CAACC,OAAO,CAACR,KAAK,CAAC;IAC/B;IAEA,IAAIU,cAAc,GAAGT,cAAc,EAAE;MACnC,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAEC,OAAO,CAACQ,KAAK,CAAC,qDAAqD,CAAC;MAC/G,OAAON,OAAO,CAACC,OAAO,CAACR,KAAK,CAAC;IAC/B;IAEA,IAAIc,aAAa,GAAGC,MAAM,CAACC,IAAI,CAACpB,UAAU,CAAC,CAACqB,GAAG,CAAC,UAAUC,GAAG,EAAE;MAC7D,OAAOC,QAAQ,CAACD,GAAG,CAAC;IACtB,CAAC,CAAC,CAACE,MAAM,CAAC,UAAUC,GAAG,EAAE;MACvB,OAAOpB,cAAc,IAAIoB,GAAG,IAAIA,GAAG,GAAGX,cAAc;IACtD,CAAC,CAAC,CAACY,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;MACtB,OAAOD,CAAC,GAAGC,CAAC;IACd,CAAC,CAAC;IACF,IAAItB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAIL,KAAK,EAAEM,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEQ,aAAa,CAAC;IAE9G,IAAI;MACF,IAAIW,aAAa,GAAGX,aAAa,CAACY,MAAM,CAAC,UAAU1B,KAAK,EAAE2B,UAAU,EAAE;QACpE,IAAIzB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAIL,KAAK,EAAEM,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAEqB,UAAU,CAAC;QAC9H,OAAO/B,UAAU,CAAC+B,UAAU,CAAC,CAAC3B,KAAK,CAAC;MACtC,CAAC,EAAEA,KAAK,CAAC;MACT,OAAOO,OAAO,CAACC,OAAO,CAACiB,aAAa,CAAC;IACvC,CAAC,CAAC,OAAOG,GAAG,EAAE;MACZ,OAAOrB,OAAO,CAACsB,MAAM,CAACD,GAAG,CAAC;IAC5B;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}