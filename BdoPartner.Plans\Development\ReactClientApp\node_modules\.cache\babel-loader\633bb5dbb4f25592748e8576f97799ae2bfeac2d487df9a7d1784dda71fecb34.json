{"ast": null, "code": "import { Subject } from '../Subject';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nexport function windowWhen(closingSelector) {\n  return operate(function (source, subscriber) {\n    var window;\n    var closingSubscriber;\n    var handleError = function (err) {\n      window.error(err);\n      subscriber.error(err);\n    };\n    var openWindow = function () {\n      closingSubscriber === null || closingSubscriber === void 0 ? void 0 : closingSubscriber.unsubscribe();\n      window === null || window === void 0 ? void 0 : window.complete();\n      window = new Subject();\n      subscriber.next(window.asObservable());\n      var closingNotifier;\n      try {\n        closingNotifier = innerFrom(closingSelector());\n      } catch (err) {\n        handleError(err);\n        return;\n      }\n      closingNotifier.subscribe(closingSubscriber = createOperatorSubscriber(subscriber, openWindow, openWindow, handleError));\n    };\n    openWindow();\n    source.subscribe(createOperatorSubscriber(subscriber, function (value) {\n      return window.next(value);\n    }, function () {\n      window.complete();\n      subscriber.complete();\n    }, handleError, function () {\n      closingSubscriber === null || closingSubscriber === void 0 ? void 0 : closingSubscriber.unsubscribe();\n      window = null;\n    }));\n  });\n}", "map": {"version": 3, "names": ["Subject", "operate", "createOperatorSubscriber", "innerFrom", "windowWhen", "closingSelector", "source", "subscriber", "window", "closingSubscriber", "handleError", "err", "error", "openWindow", "unsubscribe", "complete", "next", "asObservable", "closingNotifier", "subscribe", "value"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\windowWhen.ts"], "sourcesContent": ["import { Subscriber } from '../Subscriber';\nimport { Observable } from '../Observable';\nimport { Subject } from '../Subject';\nimport { ObservableInput, OperatorFunction } from '../types';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\n\n/**\n * Branch out the source Observable values as a nested Observable using a\n * factory function of closing Observables to determine when to start a new\n * window.\n *\n * <span class=\"informal\">It's like {@link bufferWhen}, but emits a nested\n * Observable instead of an array.</span>\n *\n * ![](windowWhen.png)\n *\n * Returns an Observable that emits windows of items it collects from the source\n * Observable. The output Observable emits connected, non-overlapping windows.\n * It emits the current window and opens a new one whenever the Observable\n * produced by the specified `closingSelector` function emits an item. The first\n * window is opened immediately when subscribing to the output Observable.\n *\n * ## Example\n *\n * Emit only the first two clicks events in every window of [1-5] random seconds\n *\n * ```ts\n * import { fromEvent, windowWhen, interval, map, take, mergeAll } from 'rxjs';\n *\n * const clicks = fromEvent(document, 'click');\n * const result = clicks.pipe(\n *   windowWhen(() => interval(1000 + Math.random() * 4000)),\n *   map(win => win.pipe(take(2))), // take at most 2 emissions from each window\n *   mergeAll()                     // flatten the Observable-of-Observables\n * );\n * result.subscribe(x => console.log(x));\n * ```\n *\n * @see {@link window}\n * @see {@link windowCount}\n * @see {@link windowTime}\n * @see {@link windowToggle}\n * @see {@link bufferWhen}\n *\n * @param closingSelector A function that takes no arguments and returns an\n * {@link ObservableInput} (that gets converted to Observable) that signals\n * (on either `next` or `complete`) when to close the previous window and\n * start a new one.\n * @return A function that returns an Observable of windows, which in turn are\n * Observables.\n */\nexport function windowWhen<T>(closingSelector: () => ObservableInput<any>): OperatorFunction<T, Observable<T>> {\n  return operate((source, subscriber) => {\n    let window: Subject<T> | null;\n    let closingSubscriber: Subscriber<any> | undefined;\n\n    /**\n     * When we get an error, we have to notify both the\n     * destination subscriber and the window.\n     */\n    const handleError = (err: any) => {\n      window!.error(err);\n      subscriber.error(err);\n    };\n\n    /**\n     * Called every time we need to open a window.\n     * Recursive, as it will start the closing notifier, which\n     * inevitably *should* call openWindow -- but may not if\n     * it is a \"never\" observable.\n     */\n    const openWindow = () => {\n      // We need to clean up our closing subscription,\n      // we only cared about the first next or complete notification.\n      closingSubscriber?.unsubscribe();\n\n      // Close our window before starting a new one.\n      window?.complete();\n\n      // Start the new window.\n      window = new Subject<T>();\n      subscriber.next(window.asObservable());\n\n      // Get our closing notifier.\n      let closingNotifier: Observable<any>;\n      try {\n        closingNotifier = innerFrom(closingSelector());\n      } catch (err) {\n        handleError(err);\n        return;\n      }\n\n      // Subscribe to the closing notifier, be sure\n      // to capture the subscriber (aka Subscription)\n      // so we can clean it up when we close the window\n      // and open a new one.\n      closingNotifier.subscribe((closingSubscriber = createOperatorSubscriber(subscriber, openWindow, openWindow, handleError)));\n    };\n\n    // Start the first window.\n    openWindow();\n\n    // Subscribe to the source\n    source.subscribe(\n      createOperatorSubscriber(\n        subscriber,\n        (value) => window!.next(value),\n        () => {\n          // The source completed, close the window and complete.\n          window!.complete();\n          subscriber.complete();\n        },\n        handleError,\n        () => {\n          // Be sure to clean up our closing subscription\n          // when this tears down.\n          closingSubscriber?.unsubscribe();\n          window = null!;\n        }\n      )\n    );\n  });\n}\n"], "mappings": "AAEA,SAASA,OAAO,QAAQ,YAAY;AAEpC,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,SAASC,SAAS,QAAQ,yBAAyB;AA+CnD,OAAM,SAAUC,UAAUA,CAAIC,eAA2C;EACvE,OAAOJ,OAAO,CAAC,UAACK,MAAM,EAAEC,UAAU;IAChC,IAAIC,MAAyB;IAC7B,IAAIC,iBAA8C;IAMlD,IAAMC,WAAW,GAAG,SAAAA,CAACC,GAAQ;MAC3BH,MAAO,CAACI,KAAK,CAACD,GAAG,CAAC;MAClBJ,UAAU,CAACK,KAAK,CAACD,GAAG,CAAC;IACvB,CAAC;IAQD,IAAME,UAAU,GAAG,SAAAA,CAAA;MAGjBJ,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEK,WAAW,EAAE;MAGhCN,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEO,QAAQ,EAAE;MAGlBP,MAAM,GAAG,IAAIR,OAAO,EAAK;MACzBO,UAAU,CAACS,IAAI,CAACR,MAAM,CAACS,YAAY,EAAE,CAAC;MAGtC,IAAIC,eAAgC;MACpC,IAAI;QACFA,eAAe,GAAGf,SAAS,CAACE,eAAe,EAAE,CAAC;OAC/C,CAAC,OAAOM,GAAG,EAAE;QACZD,WAAW,CAACC,GAAG,CAAC;QAChB;;MAOFO,eAAe,CAACC,SAAS,CAAEV,iBAAiB,GAAGP,wBAAwB,CAACK,UAAU,EAAEM,UAAU,EAAEA,UAAU,EAAEH,WAAW,CAAE,CAAC;IAC5H,CAAC;IAGDG,UAAU,EAAE;IAGZP,MAAM,CAACa,SAAS,CACdjB,wBAAwB,CACtBK,UAAU,EACV,UAACa,KAAK;MAAK,OAAAZ,MAAO,CAACQ,IAAI,CAACI,KAAK,CAAC;IAAnB,CAAmB,EAC9B;MAEEZ,MAAO,CAACO,QAAQ,EAAE;MAClBR,UAAU,CAACQ,QAAQ,EAAE;IACvB,CAAC,EACDL,WAAW,EACX;MAGED,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEK,WAAW,EAAE;MAChCN,MAAM,GAAG,IAAK;IAChB,CAAC,CACF,CACF;EACH,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}