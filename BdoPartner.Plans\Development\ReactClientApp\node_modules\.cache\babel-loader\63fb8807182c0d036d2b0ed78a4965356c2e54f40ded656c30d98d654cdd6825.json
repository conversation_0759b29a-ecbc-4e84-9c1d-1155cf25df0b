{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PrimeReact, { PrimeReactContext, ariaLabel } from 'primereact/api';\nimport { ComponentBase, useHandleStyle } from 'primereact/componentbase';\nimport { useMergeProps, useEventListener, useResizeListener, useMountEffect, useUpdateEffect, useUnmountEffect } from 'primereact/hooks';\nimport { BarsIcon } from 'primereact/icons/bars';\nimport { classNames, ObjectUtils, IconUtils, DomHandler, UniqueComponentId, ZIndexUtils } from 'primereact/utils';\nimport { AngleDownIcon } from 'primereact/icons/angledown';\nimport { AngleRightIcon } from 'primereact/icons/angleright';\nimport { Ripple } from 'primereact/ripple';\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nfunction toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction toPropertyKey(t) {\n  var i = toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nfunction _defineProperty(e, r, t) {\n  return (r = toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _arrayWithHoles(r) {\n  if (Array.isArray(r)) return r;\n}\nfunction _iterableToArrayLimit(r, l) {\n  var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"];\n  if (null != t) {\n    var e,\n      n,\n      i,\n      u,\n      a = [],\n      f = !0,\n      o = !1;\n    try {\n      if (i = (t = t.call(r)).next, 0 === l) {\n        if (Object(t) !== t) return;\n        f = !1;\n      } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0);\n    } catch (r) {\n      o = !0, n = r;\n    } finally {\n      try {\n        if (!f && null != t[\"return\"] && (u = t[\"return\"](), Object(u) !== u)) return;\n      } finally {\n        if (o) throw n;\n      }\n    }\n    return a;\n  }\n}\nfunction _arrayLikeToArray(r, a) {\n  (null == a || a > r.length) && (a = r.length);\n  for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e];\n  return n;\n}\nfunction _unsupportedIterableToArray(r, a) {\n  if (r) {\n    if (\"string\" == typeof r) return _arrayLikeToArray(r, a);\n    var t = {}.toString.call(r).slice(8, -1);\n    return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0;\n  }\n}\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _slicedToArray(r, e) {\n  return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest();\n}\nvar classes = {\n  start: 'p-menubar-start',\n  end: 'p-menubar-end',\n  button: 'p-menubar-button',\n  root: function root(_ref) {\n    var mobileActiveState = _ref.mobileActiveState;\n    return classNames('p-menubar p-component', {\n      'p-menubar-mobile-active': mobileActiveState\n    });\n  },\n  separator: 'p-menuitem-separator',\n  icon: 'p-menuitem-icon',\n  label: 'p-menuitem-text',\n  submenuIcon: 'p-submenu-icon',\n  menuitem: function menuitem(_ref2) {\n    var active = _ref2.active,\n      focused = _ref2.focused,\n      disabled = _ref2.disabled;\n    return classNames('p-menuitem', {\n      'p-menuitem-active p-highlight': active,\n      'p-focus': focused,\n      'p-disabled': disabled\n    });\n  },\n  menu: 'p-menubar-root-list',\n  content: 'p-menuitem-content',\n  submenu: 'p-submenu-list',\n  action: function action(_ref3) {\n    var disabled = _ref3.disabled;\n    return classNames('p-menuitem-link', {\n      'p-disabled': disabled\n    });\n  }\n};\nvar styles = \"\\n@layer primereact {\\n    .p-menubar {\\n        display: flex;\\n        align-items: center;\\n    }\\n\\n    .p-menubar ul {\\n        margin: 0;\\n        padding: 0;\\n        list-style: none;\\n    }\\n\\n    .p-menubar .p-menuitem-link {\\n        cursor: pointer;\\n        display: flex;\\n        align-items: center;\\n        text-decoration: none;\\n        overflow: hidden;\\n        position: relative;\\n    }\\n\\n    .p-menubar .p-menuitem-text {\\n        line-height: 1;\\n    }\\n\\n    .p-menubar .p-menuitem {\\n        position: relative;\\n    }\\n\\n    .p-menubar-root-list {\\n        display: flex;\\n        align-items: center;\\n        flex-wrap: wrap;\\n    }\\n\\n    .p-menubar-root-list > li ul {\\n        display: none;\\n        z-index: 1;\\n    }\\n\\n    .p-menubar-root-list > .p-menuitem-active > .p-submenu-list {\\n        display: block;\\n    }\\n\\n    .p-menubar .p-submenu-list {\\n        display: none;\\n        position: absolute;\\n        z-index: 5;\\n    }\\n\\n    .p-menubar .p-submenu-list > .p-menuitem-active > .p-submenu-list {\\n        display: block;\\n        left: 100%;\\n        top: 0;\\n    }\\n\\n    .p-menubar .p-submenu-list .p-menuitem .p-menuitem-content .p-menuitem-link .p-submenu-icon {\\n        margin-left: auto;\\n    }\\n\\n    .p-menubar .p-menubar-end {\\n        margin-left: auto;\\n        align-self: center;\\n    }\\n\\n    .p-menubar-button {\\n        display: none;\\n        cursor: pointer;\\n        align-items: center;\\n        justify-content: center;\\n        text-decoration: none;\\n    }\\n}\\n\";\nvar MenubarBase = ComponentBase.extend({\n  defaultProps: {\n    __TYPE: 'Menubar',\n    id: null,\n    model: null,\n    style: null,\n    className: null,\n    start: null,\n    ariaLabel: null,\n    ariaLabelledBy: null,\n    onFocus: null,\n    onBlur: null,\n    submenuIcon: null,\n    menuIcon: null,\n    end: null,\n    children: undefined\n  },\n  css: {\n    classes: classes,\n    styles: styles\n  }\n});\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nfunction ownKeys$1(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread$1(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys$1(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$1(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nvar MenubarSub = /*#__PURE__*/React.memo(/*#__PURE__*/React.forwardRef(function (props, ref) {\n  var mergeProps = useMergeProps();\n  var ptm = props.ptm,\n    cx = props.cx;\n  var getPTOptions = function getPTOptions(processedItem, key, index) {\n    return ptm(key, {\n      props: props,\n      hostName: props.hostName,\n      context: {\n        item: processedItem,\n        index: index,\n        active: isItemActive(processedItem),\n        focused: isItemFocused(processedItem),\n        disabled: isItemDisabled(processedItem),\n        level: props.level\n      }\n    });\n  };\n  var onItemMouseEnter = function onItemMouseEnter(event, item) {\n    if (isItemDisabled(item) || props.mobileActive) {\n      event.preventDefault();\n      return;\n    }\n    props.onItemMouseEnter && props.onItemMouseEnter({\n      originalEvent: event,\n      processedItem: item\n    });\n  };\n  var onItemClick = function onItemClick(event, processedItem) {\n    var item = processedItem.item;\n    if (isItemDisabled(processedItem)) {\n      event.preventDefault();\n      return;\n    }\n    if (item.command) {\n      item.command({\n        originalEvent: event,\n        item: item\n      });\n    }\n    onLeafClick({\n      originalEvent: event,\n      processedItem: processedItem,\n      isFocus: true\n    });\n    if (!item.url) {\n      event.preventDefault();\n      event.stopPropagation();\n    }\n  };\n  var onLeafClick = function onLeafClick(event) {\n    props.onLeafClick && props.onLeafClick(event);\n  };\n  var getItemId = function getItemId(processedItem) {\n    var _processedItem$item;\n    return (_processedItem$item = processedItem.item) === null || _processedItem$item === void 0 ? void 0 : _processedItem$item.id;\n  };\n  var getItemDataId = function getItemDataId(processedItem) {\n    return \"\".concat(props.id, \"_\").concat(processedItem.key);\n  };\n  var getItemProp = function getItemProp(processedItem, name, params) {\n    return processedItem && processedItem.item ? ObjectUtils.getItemValue(processedItem.item[name], params) : undefined;\n  };\n  var isItemActive = function isItemActive(processedItem) {\n    return props.activeItemPath.some(function (path) {\n      return path.key === processedItem.key;\n    });\n  };\n  var isItemVisible = function isItemVisible(processedItem) {\n    return getItemProp(processedItem, 'visible') !== false;\n  };\n  var isItemDisabled = function isItemDisabled(processedItem) {\n    return getItemProp(processedItem, 'disabled');\n  };\n  var isItemFocused = function isItemFocused(processedItem) {\n    return props.focusedItemId === getItemDataId(processedItem);\n  };\n  var isItemGroup = function isItemGroup(processedItem) {\n    return ObjectUtils.isNotEmpty(processedItem.items);\n  };\n  var getAriaSetSize = function getAriaSetSize() {\n    return props.model.filter(function (processedItem) {\n      return isItemVisible(processedItem) && !getItemProp(processedItem, 'separator');\n    }).length;\n  };\n  var getAriaPosInset = function getAriaPosInset(index) {\n    return index - props.model.slice(0, index).filter(function (processedItem) {\n      return isItemVisible(processedItem) && getItemProp(processedItem, 'separator');\n    }).length + 1;\n  };\n  var createSeparator = function createSeparator(processedItem, index) {\n    var key = props.id + '_separator_' + index + '_' + processedItem.key;\n    var separatorProps = mergeProps({\n      'data-id': key,\n      className: cx('separator'),\n      role: 'separator'\n    }, ptm('separator', {\n      hostName: props.hostName\n    }));\n    return /*#__PURE__*/React.createElement(\"li\", _extends({}, separatorProps, {\n      key: key\n    }));\n  };\n  var createSubmenu = function createSubmenu(processedItem) {\n    var items = processedItem && processedItem.items;\n    if (items) {\n      return /*#__PURE__*/React.createElement(MenubarSub, {\n        id: props.id,\n        hostName: props.hostName,\n        menuProps: props.menuProps,\n        level: props.level + 1,\n        model: items,\n        activeItemPath: props.activeItemPath,\n        focusedItemId: props.focusedItemId,\n        onLeafClick: onLeafClick,\n        onItemMouseEnter: props.onItemMouseEnter,\n        submenuIcon: props.submenuIcon,\n        ptm: ptm,\n        style: {\n          display: isItemActive(processedItem) ? 'block' : 'none'\n        },\n        cx: cx\n      });\n    }\n    return null;\n  };\n  var createMenuitem = function createMenuitem(processedItem, index) {\n    var item = processedItem.item;\n    if (!isItemVisible(processedItem)) {\n      return null;\n    }\n    var id = getItemId(processedItem);\n    var dataId = getItemDataId(processedItem);\n    var active = isItemActive(processedItem);\n    var focused = isItemFocused(processedItem);\n    var disabled = isItemDisabled(processedItem) || false;\n    var group = isItemGroup(processedItem);\n    var linkClassName = classNames('p-menuitem-link', {\n      'p-disabled': disabled\n    });\n    var iconClassName = classNames('p-menuitem-icon', getItemProp(processedItem, 'icon'));\n    var iconProps = mergeProps({\n      className: cx('icon')\n    }, getPTOptions(processedItem, 'icon', index));\n    var icon = IconUtils.getJSXIcon(item.icon, _objectSpread$1({}, iconProps), {\n      props: props.menuProps\n    });\n    var labelProps = mergeProps({\n      className: cx('label')\n    }, getPTOptions(processedItem, 'label', index));\n    var label = item.label && /*#__PURE__*/React.createElement(\"span\", labelProps, item.label);\n    var items = getItemProp(processedItem, 'items');\n    var submenuIconClassName = 'p-submenu-icon';\n    var submenuIconProps = mergeProps({\n      className: cx('submenuIcon')\n    }, getPTOptions(processedItem, 'submenuIcon', index));\n    var submenuIcon = items && IconUtils.getJSXIcon(!props.root ? props.submenuIcon || /*#__PURE__*/React.createElement(AngleRightIcon, submenuIconProps) : props.submenuIcon || /*#__PURE__*/React.createElement(AngleDownIcon, submenuIconProps), _objectSpread$1({}, submenuIconProps), {\n      props: _objectSpread$1({\n        menuProps: props.menuProps\n      }, props)\n    });\n    var submenu = createSubmenu(processedItem);\n    var actionProps = mergeProps({\n      href: item.url || '#',\n      tabIndex: '-1',\n      className: cx('action', {\n        disabled: disabled\n      }),\n      onFocus: function onFocus(event) {\n        return event.stopPropagation();\n      },\n      target: getItemProp(processedItem, 'target'),\n      'aria-haspopup': items != null\n    }, getPTOptions(processedItem, 'action', index));\n    var content = /*#__PURE__*/React.createElement(\"a\", actionProps, icon, label, submenuIcon, /*#__PURE__*/React.createElement(Ripple, null));\n    if (item.template) {\n      var defaultContentOptions = {\n        className: linkClassName,\n        labelClassName: 'p-menuitem-text',\n        iconClassName: iconClassName,\n        submenuIconClassName: submenuIconClassName,\n        element: content,\n        props: props\n      };\n      content = ObjectUtils.getJSXElement(item.template, item, defaultContentOptions);\n    }\n    var contentProps = mergeProps({\n      onClick: function onClick(event) {\n        return onItemClick(event, processedItem);\n      },\n      onMouseEnter: function onMouseEnter(event) {\n        return onItemMouseEnter(event, processedItem);\n      },\n      className: cx('content')\n    }, getPTOptions(processedItem, 'content', index));\n    var itemClassName = getItemProp(processedItem, 'className');\n    var menuitemProps = mergeProps(_defineProperty({\n      id: id,\n      'data-id': dataId,\n      role: 'menuitem',\n      'aria-label': item.label,\n      'aria-disabled': disabled,\n      'aria-expanded': group ? active : undefined,\n      'aria-haspopup': group && !item.url ? 'menu' : undefined,\n      'aria-setsize': getAriaSetSize(),\n      'aria-posinset': getAriaPosInset(index),\n      'data-p-highlight': active,\n      'data-p-focused': focused,\n      'data-p-disabled': disabled,\n      className: classNames(itemClassName, cx('menuitem', {\n        active: active,\n        focused: focused,\n        disabled: disabled\n      }))\n    }, \"data-p-disabled\", disabled || false), getPTOptions(processedItem, 'menuitem', index));\n    return /*#__PURE__*/React.createElement(\"li\", _extends({}, menuitemProps, {\n      key: \"\".concat(dataId)\n    }), /*#__PURE__*/React.createElement(\"div\", contentProps, content), submenu);\n  };\n  var createItem = function createItem(processedItem, index) {\n    if (processedItem.visible === false) {\n      return null;\n    }\n    return getItemProp(processedItem, 'separator') ? createSeparator(processedItem, index) : createMenuitem(processedItem, index);\n  };\n  var createMenu = function createMenu() {\n    return props.model ? props.model.map(createItem) : null;\n  };\n  var role = props.root ? 'menubar' : 'menu';\n  var ptKey = props.root ? 'menu' : 'submenu';\n  var tabIndex = props.root ? '0' : null;\n  var submenu = createMenu();\n  var menuProps = mergeProps({\n    ref: ref,\n    className: cx(ptKey),\n    level: props.level,\n    onFocus: props.onFocus,\n    onBlur: props.onBlur,\n    onKeyDown: props.onKeyDown,\n    'data-id': props.id,\n    tabIndex: tabIndex,\n    'aria-activedescendant': props.ariaActivedescendant,\n    style: props.style,\n    role: role\n  }, ptm(ptKey));\n  return /*#__PURE__*/React.createElement(\"ul\", menuProps, submenu);\n}));\nMenubarSub.displayName = 'MenubarSub';\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nvar Menubar = /*#__PURE__*/React.memo(/*#__PURE__*/React.forwardRef(function (inProps, ref) {\n  var mergeProps = useMergeProps();\n  var context = React.useContext(PrimeReactContext);\n  var props = MenubarBase.getProps(inProps, context);\n  var _React$useState = React.useState(props.id),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    idState = _React$useState2[0],\n    setIdState = _React$useState2[1];\n  var _React$useState3 = React.useState(false),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    mobileActiveState = _React$useState4[0],\n    setMobileActiveState = _React$useState4[1];\n  var _React$useState5 = React.useState(false),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    focused = _React$useState6[0],\n    setFocused = _React$useState6[1];\n  var _React$useState7 = React.useState({\n      index: -1,\n      level: 0,\n      parentKey: ''\n    }),\n    _React$useState8 = _slicedToArray(_React$useState7, 2),\n    focusedItemInfo = _React$useState8[0],\n    setFocusedItemInfo = _React$useState8[1];\n  var _React$useState9 = React.useState(null),\n    _React$useState10 = _slicedToArray(_React$useState9, 2),\n    focusedItemId = _React$useState10[0],\n    setFocusedItemId = _React$useState10[1];\n  var _React$useState11 = React.useState([]),\n    _React$useState12 = _slicedToArray(_React$useState11, 2),\n    activeItemPath = _React$useState12[0],\n    setActiveItemPath = _React$useState12[1];\n  var _React$useState13 = React.useState([]),\n    _React$useState14 = _slicedToArray(_React$useState13, 2),\n    visibleItems = _React$useState14[0],\n    setVisibleItems = _React$useState14[1];\n  var _React$useState15 = React.useState([]),\n    _React$useState16 = _slicedToArray(_React$useState15, 2),\n    processedItems = _React$useState16[0],\n    setProcessedItems = _React$useState16[1];\n  var _React$useState17 = React.useState(false),\n    _React$useState18 = _slicedToArray(_React$useState17, 2),\n    focusTrigger = _React$useState18[0],\n    setFocusTrigger = _React$useState18[1];\n  var _React$useState19 = React.useState(false),\n    _React$useState20 = _slicedToArray(_React$useState19, 2),\n    dirty = _React$useState20[0],\n    setDirty = _React$useState20[1];\n  var elementRef = React.useRef(null);\n  var rootMenuRef = React.useRef(null);\n  var menuButtonRef = React.useRef(null);\n  var searchValue = React.useRef('');\n  var searchTimeout = React.useRef(null);\n  var reverseTrigger = React.useRef(false);\n  var _MenubarBase$setMetaD = MenubarBase.setMetaData({\n      props: props,\n      state: {\n        id: idState,\n        mobileActive: mobileActiveState\n      }\n    }),\n    ptm = _MenubarBase$setMetaD.ptm,\n    cx = _MenubarBase$setMetaD.cx,\n    isUnstyled = _MenubarBase$setMetaD.isUnstyled;\n  useHandleStyle(MenubarBase.css.styles, isUnstyled, {\n    name: 'menubar'\n  });\n  var _useEventListener = useEventListener({\n      type: 'click',\n      listener: function listener(event) {\n        var isOutsideButton = menuButtonRef.current && !menuButtonRef.current.contains(event.target);\n        if (isOutsideButton) {\n          hide();\n        }\n      },\n      options: {\n        capture: true\n      }\n    }),\n    _useEventListener2 = _slicedToArray(_useEventListener, 2),\n    bindOutsideClickListener = _useEventListener2[0],\n    unbindOutsideClickListener = _useEventListener2[1];\n  var _useResizeListener = useResizeListener({\n      listener: function listener(event) {\n        if (!DomHandler.isTouchDevice()) {\n          hide(event);\n        }\n      }\n    }),\n    _useResizeListener2 = _slicedToArray(_useResizeListener, 2),\n    bindResizeListener = _useResizeListener2[0],\n    unbindResizeListener = _useResizeListener2[1];\n  var toggle = function toggle(event) {\n    if (mobileActiveState) {\n      setMobileActiveState(false);\n      hide();\n    } else {\n      setMobileActiveState(true);\n      setTimeout(function () {\n        show();\n      }, 1);\n    }\n    event.preventDefault();\n  };\n  var show = function show() {\n    setFocusedItemInfo({\n      index: findFirstFocusedItemIndex(),\n      level: 0,\n      parentKey: ''\n    });\n    DomHandler.focus(rootMenuRef.current);\n  };\n  var hide = function hide(isFocus) {\n    if (mobileActiveState) {\n      setMobileActiveState(false);\n      setTimeout(function () {\n        DomHandler.focus(menuButtonRef.current);\n      }, 0);\n    }\n    setActiveItemPath([]);\n    setFocusedItemInfo({\n      index: -1,\n      level: 0,\n      parentKey: ''\n    });\n    isFocus && DomHandler.focus(rootMenuRef.current);\n    setDirty(false);\n  };\n  var menuButtonKeydown = function menuButtonKeydown(event) {\n    (event.code === 'Enter' || event.code === 'NumpadEnter' || event.code === 'Space') && toggle(event);\n  };\n  var getItemProp = function getItemProp(item, name) {\n    return item ? ObjectUtils.getItemValue(item[name]) : undefined;\n  };\n  var getItemLabel = function getItemLabel(item) {\n    return getItemProp(item, 'label');\n  };\n  var isItemDisabled = function isItemDisabled(item) {\n    return getItemProp(item, 'disabled');\n  };\n  var isItemSeparator = function isItemSeparator(item) {\n    return getItemProp(item, 'separator');\n  };\n  var getProccessedItemLabel = function getProccessedItemLabel(processedItem) {\n    return processedItem ? getItemLabel(processedItem.item) : undefined;\n  };\n  var isProccessedItemGroup = function isProccessedItemGroup(processedItem) {\n    return processedItem && ObjectUtils.isNotEmpty(processedItem.items);\n  };\n  var onFocus = function onFocus(event) {\n    setFocused(true);\n    setFocusedItemInfo(focusedItemInfo.index !== -1 ? focusedItemInfo : {\n      index: findFirstFocusedItemIndex(),\n      level: 0,\n      parentKey: ''\n    });\n    props.onFocus && props.onFocus(event);\n  };\n  var onBlur = function onBlur(event) {\n    setFocused(false);\n    setFocusedItemInfo({\n      index: -1,\n      level: 0,\n      parentKey: ''\n    });\n    searchValue.current = '';\n    setDirty(false);\n    props.onBlur && props.onBlur(event);\n  };\n  var onKeyDown = function onKeyDown(event) {\n    var metaKey = event.metaKey || event.ctrlKey;\n    var code = event.code;\n    switch (code) {\n      case 'ArrowDown':\n        onArrowDownKey(event);\n        break;\n      case 'ArrowUp':\n        onArrowUpKey(event);\n        break;\n      case 'ArrowLeft':\n        onArrowLeftKey(event);\n        break;\n      case 'ArrowRight':\n        onArrowRightKey(event);\n        break;\n      case 'Home':\n        onHomeKey(event);\n        break;\n      case 'End':\n        onEndKey(event);\n        break;\n      case 'Space':\n        onSpaceKey(event);\n        break;\n      case 'Enter':\n      case 'NumpadEnter':\n        onEnterKey(event);\n        break;\n      case 'Escape':\n        onEscapeKey();\n        break;\n      case 'Tab':\n        onTabKey(event);\n        break;\n      case 'PageDown':\n      case 'PageUp':\n      case 'Backspace':\n      case 'ShiftLeft':\n      case 'ShiftRight':\n        break;\n      default:\n        if (!metaKey && ObjectUtils.isPrintableCharacter(event.key)) {\n          searchItems(event, event.key);\n        }\n        break;\n    }\n  };\n  var onItemChange = function onItemChange(event) {\n    var processedItem = event.processedItem,\n      isFocus = event.isFocus;\n    if (ObjectUtils.isEmpty(processedItem)) {\n      return;\n    }\n    var index = processedItem.index,\n      key = processedItem.key,\n      level = processedItem.level,\n      parentKey = processedItem.parentKey,\n      items = processedItem.items;\n    var grouped = ObjectUtils.isNotEmpty(items);\n    var _activeItemPath = activeItemPath.filter(function (p) {\n      return p.parentKey !== parentKey && p.parentKey !== key;\n    });\n    grouped && _activeItemPath.push(processedItem);\n    setFocusedItemInfo({\n      index: index,\n      level: level,\n      parentKey: parentKey\n    });\n    setActiveItemPath(_activeItemPath);\n    grouped && setDirty(true);\n    isFocus && DomHandler.focus(rootMenuRef.current);\n  };\n  var onItemClick = function onItemClick(event) {\n    var originalEvent = event.originalEvent,\n      processedItem = event.processedItem;\n    var grouped = isProccessedItemGroup(processedItem);\n    var root = ObjectUtils.isEmpty(processedItem.parent);\n    var selected = isSelected(processedItem);\n    if (selected) {\n      var index = processedItem.index,\n        key = processedItem.key,\n        level = processedItem.level,\n        parentKey = processedItem.parentKey;\n      setActiveItemPath(activeItemPath.filter(function (p) {\n        return key !== p.key && key.startsWith(p.key);\n      }));\n      setFocusedItemInfo({\n        index: index,\n        level: level,\n        parentKey: parentKey\n      });\n      if (!grouped) {\n        setDirty(!root);\n      }\n      setTimeout(function () {\n        DomHandler.focus(rootMenuRef.current);\n        if (grouped) {\n          setDirty(true);\n        }\n      }, 0);\n    } else if (grouped) {\n      DomHandler.focus(rootMenuRef.current);\n      onItemChange({\n        originalEvent: originalEvent,\n        processedItem: processedItem\n      });\n    } else {\n      var rootProcessedItem = root ? processedItem : activeItemPath.find(function (p) {\n        return p.parentKey === '';\n      });\n      var rootProcessedItemIndex = rootProcessedItem ? rootProcessedItem.index : -1;\n      hide(originalEvent);\n      setFocusedItemInfo({\n        index: rootProcessedItemIndex,\n        parentKey: rootProcessedItem ? rootProcessedItem.parentKey : ''\n      });\n      setMobileActiveState(false);\n    }\n  };\n  var onItemMouseEnter = function onItemMouseEnter(event) {\n    if (!mobileActiveState && dirty) {\n      onItemChange(event);\n    }\n  };\n  var onArrowDownKey = function onArrowDownKey(event) {\n    var processedItem = visibleItems[focusedItemInfo.index];\n    var root = processedItem ? ObjectUtils.isEmpty(processedItem.parent) : null;\n    if (root) {\n      var grouped = isProccessedItemGroup(processedItem);\n      if (grouped) {\n        onItemChange({\n          originalEvent: event,\n          processedItem: processedItem\n        });\n        setFocusedItemInfo({\n          index: -1,\n          parentKey: processedItem.key\n        });\n        setTimeout(function () {\n          return setFocusTrigger(true);\n        }, 0);\n      }\n    } else {\n      var itemIndex = focusedItemInfo.index !== -1 ? findNextItemIndex(focusedItemInfo.index) : findFirstFocusedItemIndex();\n      changeFocusedItemIndex(itemIndex);\n    }\n    event.preventDefault();\n  };\n  var onArrowUpKey = function onArrowUpKey(event) {\n    var processedItem = visibleItems[focusedItemInfo.index];\n    var root = ObjectUtils.isEmpty(processedItem.parent);\n    if (root) {\n      var grouped = isProccessedItemGroup(processedItem);\n      if (grouped) {\n        onItemChange({\n          originalEvent: event,\n          processedItem: processedItem\n        });\n        setFocusedItemInfo({\n          index: -1,\n          parentKey: processedItem.key\n        });\n        reverseTrigger.current = true;\n        setTimeout(function () {\n          return setFocusTrigger(true);\n        }, 0);\n      }\n    } else {\n      var parentItem = activeItemPath.find(function (p) {\n        return p.key === processedItem.parentKey;\n      });\n      if (focusedItemInfo.index === 0 && parentItem && parentItem.parentKey === '') {\n        setFocusedItemInfo({\n          index: -1,\n          parentKey: parentItem ? parentItem.parentKey : ''\n        });\n        searchValue.current = '';\n        onArrowLeftKey(event);\n      } else {\n        var itemIndex = focusedItemInfo.index !== -1 ? findPrevItemIndex(focusedItemInfo.index) : findLastFocusedItemIndex();\n        changeFocusedItemIndex(itemIndex);\n      }\n    }\n    event.preventDefault();\n  };\n  var onArrowLeftKey = function onArrowLeftKey(event) {\n    var processedItem = visibleItems[focusedItemInfo.index];\n    var parentItem = processedItem ? activeItemPath.find(function (p) {\n      return p.key === processedItem.parentKey;\n    }) : null;\n    if (parentItem) {\n      onItemChange({\n        originalEvent: event,\n        processedItem: parentItem\n      });\n      setActiveItemPath(activeItemPath.filter(function (p) {\n        return p.key !== parentItem.key;\n      }));\n    } else {\n      var itemIndex = focusedItemInfo.index !== -1 ? findPrevItemIndex(focusedItemInfo.index) : findLastFocusedItemIndex();\n      changeFocusedItemIndex(itemIndex);\n    }\n    event.preventDefault();\n  };\n  var onArrowRightKey = function onArrowRightKey(event) {\n    var processedItem = visibleItems[focusedItemInfo.index];\n    var parentItem = processedItem ? activeItemPath.find(function (p) {\n      return p.key === processedItem.parentKey;\n    }) : null;\n    if (parentItem) {\n      var grouped = isProccessedItemGroup(processedItem);\n      if (grouped) {\n        onItemChange({\n          originalEvent: event,\n          processedItem: processedItem\n        });\n        setFocusedItemInfo({\n          index: -1,\n          parentKey: processedItem.key\n        });\n        setTimeout(function () {\n          return setFocusTrigger(true);\n        }, 0);\n      }\n    } else {\n      var itemIndex = focusedItemInfo.index !== -1 ? findNextItemIndex(focusedItemInfo.index) : findFirstFocusedItemIndex();\n      changeFocusedItemIndex(itemIndex);\n    }\n    event.preventDefault();\n  };\n  var onHomeKey = function onHomeKey(event) {\n    changeFocusedItemIndex(findFirstItemIndex());\n    event.preventDefault();\n  };\n  var onEndKey = function onEndKey(event) {\n    changeFocusedItemIndex(findLastItemIndex());\n    event.preventDefault();\n  };\n  var onEnterKey = function onEnterKey(event) {\n    if (focusedItemInfo.index !== -1) {\n      var element = DomHandler.findSingle(rootMenuRef.current, \"li[data-id=\\\"\".concat(\"\".concat(focusedItemId), \"\\\"]\"));\n      var anchorElement = element && DomHandler.findSingle(element, 'a[data-pc-section=\"action\"]');\n      anchorElement ? anchorElement.click() : element && element.click();\n    }\n    event.preventDefault();\n  };\n  var onSpaceKey = function onSpaceKey(event) {\n    onEnterKey(event);\n  };\n  var onEscapeKey = function onEscapeKey(event) {\n    hide(true);\n    setFocusedItemInfo({\n      focusedItemInfo: focusedItemInfo,\n      index: findFirstFocusedItemIndex()\n    });\n  };\n  var onTabKey = function onTabKey(event) {\n    if (focusedItemInfo.index !== -1) {\n      var processedItem = visibleItems[focusedItemInfo.index];\n      var grouped = isProccessedItemGroup(processedItem);\n      !grouped && onItemChange({\n        originalEvent: event,\n        processedItem: processedItem\n      });\n    }\n    hide();\n  };\n  var isItemMatched = function isItemMatched(processedItem) {\n    return isValidItem(processedItem) && getProccessedItemLabel(processedItem).toLocaleLowerCase().startsWith(searchValue.current.toLocaleLowerCase());\n  };\n  var isValidItem = function isValidItem(processedItem) {\n    return !!processedItem && !isItemDisabled(processedItem.item) && !isItemSeparator(processedItem.item);\n  };\n  var isValidSelectedItem = function isValidSelectedItem(processedItem) {\n    return isValidItem(processedItem) && isSelected(processedItem);\n  };\n  var isSelected = function isSelected(processedItem) {\n    return activeItemPath.some(function (p) {\n      return p.key === processedItem.key;\n    });\n  };\n  var findFirstItemIndex = function findFirstItemIndex() {\n    return visibleItems.findIndex(function (processedItem) {\n      return isValidItem(processedItem);\n    });\n  };\n  var findLastItemIndex = function findLastItemIndex() {\n    return ObjectUtils.findLastIndex(visibleItems, function (processedItem) {\n      return isValidItem(processedItem);\n    });\n  };\n  var findNextItemIndex = function findNextItemIndex(index) {\n    var matchedItemIndex = index < visibleItems.length - 1 ? visibleItems.slice(index + 1).findIndex(function (processedItem) {\n      return isValidItem(processedItem);\n    }) : -1;\n    return matchedItemIndex > -1 ? matchedItemIndex + index + 1 : index;\n  };\n  var findPrevItemIndex = function findPrevItemIndex(index) {\n    var matchedItemIndex = index > 0 ? ObjectUtils.findLastIndex(visibleItems.slice(0, index), function (processedItem) {\n      return isValidItem(processedItem);\n    }) : -1;\n    return matchedItemIndex > -1 ? matchedItemIndex : index;\n  };\n  var findSelectedItemIndex = function findSelectedItemIndex() {\n    return visibleItems.findIndex(function (processedItem) {\n      return isValidSelectedItem(processedItem);\n    });\n  };\n  var findFirstFocusedItemIndex = function findFirstFocusedItemIndex() {\n    var selectedIndex = findSelectedItemIndex();\n    return selectedIndex;\n  };\n  var findLastFocusedItemIndex = function findLastFocusedItemIndex() {\n    var selectedIndex = findSelectedItemIndex();\n    return selectedIndex;\n  };\n  var searchItems = function searchItems(event, _char) {\n    searchValue.current = (searchValue.current || '') + _char;\n    var itemIndex = -1;\n    var matched = false;\n    if (focusedItemInfo.index !== -1) {\n      itemIndex = visibleItems.slice(focusedItemInfo.index).findIndex(function (processedItem) {\n        return isItemMatched(processedItem);\n      });\n      itemIndex = itemIndex === -1 ? visibleItems.slice(0, focusedItemInfo.index).findIndex(function (processedItem) {\n        return isItemMatched(processedItem);\n      }) : itemIndex + focusedItemInfo.index;\n    } else {\n      itemIndex = visibleItems.findIndex(function (processedItem) {\n        return isItemMatched(processedItem);\n      });\n    }\n    if (itemIndex !== -1) {\n      matched = true;\n    }\n    if (itemIndex === -1 && focusedItemInfo.index === -1) {\n      itemIndex = findFirstFocusedItemIndex();\n    }\n    if (itemIndex !== -1) {\n      changeFocusedItemIndex(itemIndex);\n    }\n    if (searchTimeout.current) {\n      clearTimeout(searchTimeout.current);\n    }\n    searchTimeout.current = setTimeout(function () {\n      searchValue.current = '';\n      searchTimeout.current = null;\n    }, 500);\n    return matched;\n  };\n  var changeFocusedItemIndex = function changeFocusedItemIndex(index) {\n    if (focusedItemInfo.index !== index) {\n      setFocusedItemInfo(_objectSpread(_objectSpread({}, focusedItemInfo), {}, {\n        index: index\n      }));\n      scrollInView();\n    }\n  };\n  var scrollInView = function scrollInView() {\n    var index = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : -1;\n    var id = index !== -1 ? \"\".concat(idState, \"_\").concat(index) : focusedItemId;\n    var element = DomHandler.findSingle(rootMenuRef.current, \"li[data-id=\\\"\".concat(id, \"\\\"]\"));\n    if (element) {\n      element.scrollIntoView && element.scrollIntoView({\n        block: 'nearest',\n        inline: 'start'\n      });\n    }\n  };\n  var _createProcessedItems = function createProcessedItems(items) {\n    var level = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n    var parent = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    var parentKey = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : '';\n    var _processedItems = [];\n    items && items.forEach(function (item, index) {\n      var key = (parentKey !== '' ? parentKey + '_' : '') + index;\n      var newItem = {\n        item: item,\n        index: index,\n        level: level,\n        key: key,\n        parent: parent,\n        parentKey: parentKey\n      };\n      newItem.items = _createProcessedItems(item.items, level + 1, newItem, key);\n      _processedItems.push(newItem);\n    });\n    return _processedItems;\n  };\n  useMountEffect(function () {\n    if (!idState) {\n      setIdState(UniqueComponentId());\n    }\n  });\n  useUpdateEffect(function () {\n    if (mobileActiveState) {\n      bindOutsideClickListener();\n      bindResizeListener();\n      ZIndexUtils.set('menu', rootMenuRef.current, context && context.autoZIndex || PrimeReact.autoZIndex, context && context.zIndex.menu || PrimeReact.zIndex.menu);\n    } else {\n      unbindResizeListener();\n      unbindOutsideClickListener();\n      ZIndexUtils.clear(rootMenuRef.current);\n    }\n  }, [mobileActiveState]);\n  React.useEffect(function () {\n    var itemsToProcess = props.model || [];\n    var processed = _createProcessedItems(itemsToProcess, 0, null, '');\n    setProcessedItems(processed);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [props.model]);\n  useUpdateEffect(function () {\n    var processedItem = activeItemPath.find(function (p) {\n      return p.key === focusedItemInfo.parentKey;\n    });\n    var _processedItems = processedItem ? processedItem.items : processedItems;\n    setVisibleItems(_processedItems);\n  }, [activeItemPath, focusedItemInfo, processedItems]);\n  useUpdateEffect(function () {\n    if (ObjectUtils.isNotEmpty(activeItemPath)) {\n      bindOutsideClickListener();\n      bindResizeListener();\n    } else {\n      unbindOutsideClickListener();\n      unbindResizeListener();\n    }\n  }, [activeItemPath]);\n  useUpdateEffect(function () {\n    if (focusTrigger) {\n      var itemIndex = focusedItemInfo.index !== -1 ? findNextItemIndex(focusedItemInfo.index) : reverseTrigger.current ? findLastItemIndex() : findFirstFocusedItemIndex();\n      changeFocusedItemIndex(itemIndex);\n      reverseTrigger.current = false;\n      setFocusTrigger(false);\n    }\n  }, [focusTrigger]);\n  useUpdateEffect(function () {\n    setFocusedItemId(focusedItemInfo.index !== -1 ? \"\".concat(idState).concat(ObjectUtils.isNotEmpty(focusedItemInfo.parentKey) ? '_' + focusedItemInfo.parentKey : '', \"_\").concat(focusedItemInfo.index) : null);\n  }, [focusedItemInfo]);\n  useUnmountEffect(function () {\n    ZIndexUtils.clear(rootMenuRef.current);\n  });\n  React.useImperativeHandle(ref, function () {\n    return {\n      props: props,\n      toggle: toggle,\n      getElement: function getElement() {\n        return elementRef.current;\n      },\n      getRootMenu: function getRootMenu() {\n        return rootMenuRef.current;\n      },\n      getMenuButton: function getMenuButton() {\n        return menuButtonRef.current;\n      }\n    };\n  });\n  var createStartContent = function createStartContent() {\n    if (props.start) {\n      var _start = ObjectUtils.getJSXElement(props.start, props);\n      var startProps = mergeProps({\n        className: cx('start')\n      }, ptm('start'));\n      return /*#__PURE__*/React.createElement(\"div\", startProps, _start);\n    }\n    return null;\n  };\n  var createEndContent = function createEndContent() {\n    if (props.end) {\n      var _end = ObjectUtils.getJSXElement(props.end, props);\n      var endProps = mergeProps({\n        className: cx('end')\n      }, ptm('end'));\n      return /*#__PURE__*/React.createElement(\"div\", endProps, _end);\n    }\n    return null;\n  };\n  var createMenuButton = function createMenuButton() {\n    if (props.model && props.model.length < 1) {\n      return null;\n    }\n    var buttonProps = mergeProps(_defineProperty(_defineProperty(_defineProperty(_defineProperty({\n      ref: menuButtonRef,\n      href: '#',\n      tabIndex: '0',\n      'aria-haspopup': mobileActiveState && props.model && props.model.length > 0 ? true : false,\n      'aria-expanded': mobileActiveState,\n      'aria-label': ariaLabel('navigation'),\n      'aria-controls': idState,\n      role: 'button'\n    }, \"tabIndex\", 0), \"className\", cx('button')), \"onKeyDown\", function onKeyDown(e) {\n      return menuButtonKeydown(e);\n    }), \"onClick\", function onClick(e) {\n      return toggle(e);\n    }), ptm('button'));\n    var popupIconProps = mergeProps(ptm('popupIcon'));\n    var icon = props.menuIcon || /*#__PURE__*/React.createElement(BarsIcon, popupIconProps);\n    var menuIcon = IconUtils.getJSXIcon(icon, _objectSpread({}, popupIconProps), {\n      props: props\n    });\n\n    /* eslint-disable */\n    var button = /*#__PURE__*/React.createElement(\"a\", buttonProps, menuIcon);\n    /* eslint-enable */\n\n    return button;\n  };\n  var start = createStartContent();\n  var end = createEndContent();\n  var menuButton = createMenuButton();\n  var submenu = /*#__PURE__*/React.createElement(MenubarSub, {\n    hostName: \"Menubar\",\n    ariaActivedescendant: focused ? focusedItemId : undefined,\n    level: 0,\n    id: idState,\n    ref: rootMenuRef,\n    menuProps: props,\n    model: processedItems,\n    onLeafClick: onItemClick,\n    onItemMouseEnter: onItemMouseEnter,\n    onFocus: onFocus,\n    onBlur: onBlur,\n    onKeyDown: onKeyDown,\n    root: true,\n    activeItemPath: activeItemPath,\n    focusedItemId: focused ? focusedItemId : undefined,\n    submenuIcon: props.submenuIcon,\n    ptm: ptm,\n    cx: cx\n  });\n  var rootProps = mergeProps({\n    id: props.id,\n    ref: elementRef,\n    className: classNames(props.className, cx('root', {\n      mobileActiveState: mobileActiveState\n    })),\n    style: props.style\n  }, MenubarBase.getOtherProps(props), ptm('root'));\n  return /*#__PURE__*/React.createElement(\"div\", rootProps, start, menuButton, submenu, end);\n}));\nMenubar.displayName = 'Menubar';\nexport { Menubar };", "map": {"version": 3, "names": ["React", "PrimeReact", "PrimeReactContext", "aria<PERSON><PERSON><PERSON>", "ComponentBase", "useHandleStyle", "useMergeProps", "useEventListener", "useResizeListener", "useMountEffect", "useUpdateEffect", "useUnmountEffect", "BarsIcon", "classNames", "ObjectUtils", "IconUtils", "<PERSON><PERSON><PERSON><PERSON>", "UniqueComponentId", "ZIndexUtils", "AngleDownIcon", "AngleRightIcon", "<PERSON><PERSON><PERSON>", "_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "toPrimitive", "t", "r", "e", "i", "call", "TypeError", "String", "Number", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_defineProperty", "Object", "defineProperty", "value", "enumerable", "configurable", "writable", "_arrayWithHoles", "Array", "isArray", "_iterableToArrayLimit", "l", "n", "u", "a", "f", "next", "done", "push", "length", "_arrayLikeToArray", "_unsupportedIterableToArray", "toString", "slice", "name", "from", "test", "_nonIterableRest", "_slicedToArray", "classes", "start", "end", "button", "root", "_ref", "mobileActiveState", "separator", "icon", "label", "submenuIcon", "menuitem", "_ref2", "active", "focused", "disabled", "menu", "content", "submenu", "action", "_ref3", "styles", "MenubarBase", "extend", "defaultProps", "__TYPE", "id", "model", "style", "className", "ariaLabelledBy", "onFocus", "onBlur", "menuIcon", "children", "undefined", "css", "_extends", "assign", "bind", "arguments", "hasOwnProperty", "apply", "ownKeys$1", "keys", "getOwnPropertySymbols", "filter", "getOwnPropertyDescriptor", "_objectSpread$1", "for<PERSON>ach", "getOwnPropertyDescriptors", "defineProperties", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "memo", "forwardRef", "props", "ref", "mergeProps", "ptm", "cx", "getPTOptions", "processedItem", "key", "index", "hostName", "context", "item", "isItemActive", "isItemFocused", "isItemDisabled", "level", "onItemMouseEnter", "event", "mobileActive", "preventDefault", "originalEvent", "onItemClick", "command", "onLeafClick", "isFocus", "url", "stopPropagation", "getItemId", "_processedItem$item", "getItemDataId", "concat", "getItemProp", "params", "getItemValue", "activeItemPath", "some", "path", "isItemVisible", "focusedItemId", "isItemGroup", "isNotEmpty", "items", "getAriaSetSize", "getAriaPosInset", "createSeparator", "separatorProps", "role", "createElement", "createSubmenu", "menuProps", "display", "createMenuitem", "dataId", "group", "linkClassName", "iconClassName", "iconProps", "getJSXIcon", "labelProps", "submenuIconClassName", "submenuIconProps", "actionProps", "href", "tabIndex", "target", "template", "defaultContentOptions", "labelClassName", "element", "getJSXElement", "contentProps", "onClick", "onMouseEnter", "itemClassName", "menuitemProps", "createItem", "visible", "createMenu", "map", "ptKey", "onKeyDown", "ariaActivedescendant", "displayName", "ownKeys", "_objectSpread", "Men<PERSON><PERSON>", "inProps", "useContext", "getProps", "_React$useState", "useState", "_React$useState2", "idState", "setIdState", "_React$useState3", "_React$useState4", "setMobileActiveState", "_React$useState5", "_React$useState6", "setFocused", "_React$useState7", "parent<PERSON><PERSON>", "_React$useState8", "focusedItemInfo", "setFocusedItemInfo", "_React$useState9", "_React$useState10", "setFocusedItemId", "_React$useState11", "_React$useState12", "setActiveItemPath", "_React$useState13", "_React$useState14", "visibleItems", "setVisibleItems", "_React$useState15", "_React$useState16", "processedItems", "setProcessedItems", "_React$useState17", "_React$useState18", "focusTrigger", "setFocusTrigger", "_React$useState19", "_React$useState20", "dirty", "set<PERSON>irty", "elementRef", "useRef", "rootMenuRef", "menuButtonRef", "searchValue", "searchTimeout", "reverseTrigger", "_MenubarBase$setMetaD", "setMetaData", "state", "isUnstyled", "_useEventListener", "type", "listener", "isOutsideButton", "current", "contains", "hide", "options", "capture", "_useEventListener2", "bindOutsideClickListener", "unbindOutsideClickListener", "_useResizeListener", "isTouchDevice", "_useResizeListener2", "bindResizeListener", "unbindResizeListener", "toggle", "setTimeout", "show", "findFirstFocusedItemIndex", "focus", "menuButtonKeydown", "code", "getItemLabel", "isItemSeparator", "getProccessedItemLabel", "isProccessedItemGroup", "metaKey", "ctrl<PERSON>ey", "onArrowDownKey", "onArrowUpKey", "onArrowLeftKey", "onArrowRightKey", "onHomeKey", "onEndKey", "onSpaceKey", "onEnterKey", "onEscapeKey", "onTabKey", "isPrintableCharacter", "searchItems", "onItemChange", "isEmpty", "grouped", "_activeItemPath", "p", "parent", "selected", "isSelected", "startsWith", "rootProcessedItem", "find", "rootProcessedItemIndex", "itemIndex", "findNextItemIndex", "changeFocusedItemIndex", "parentItem", "findPrevItemIndex", "findLastFocusedItemIndex", "findFirstItemIndex", "findLastItemIndex", "findSingle", "anchorElement", "click", "isItemMatched", "isValidItem", "toLocaleLowerCase", "isValidSelectedItem", "findIndex", "findLastIndex", "matchedItemIndex", "findSelectedItemIndex", "selectedIndex", "_char", "matched", "clearTimeout", "scrollInView", "scrollIntoView", "block", "inline", "_createProcessedItems", "createProcessedItems", "_processedItems", "newItem", "set", "autoZIndex", "zIndex", "clear", "useEffect", "itemsToProcess", "processed", "useImperativeHandle", "getElement", "getRootMenu", "getMenuButton", "createStartContent", "_start", "startProps", "createEndContent", "_end", "endProps", "createMenuButton", "buttonProps", "popupIconProps", "menuButton", "rootProps", "getOtherProps"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/node_modules/primereact/menubar/menubar.esm.js"], "sourcesContent": ["'use client';\nimport * as React from 'react';\nimport PrimeReact, { PrimeReactContext, ariaLabel } from 'primereact/api';\nimport { ComponentBase, useHandleStyle } from 'primereact/componentbase';\nimport { useMergeProps, useEventListener, useResizeListener, useMountEffect, useUpdateEffect, useUnmountEffect } from 'primereact/hooks';\nimport { BarsIcon } from 'primereact/icons/bars';\nimport { classNames, ObjectUtils, IconUtils, DomHandler, UniqueComponentId, ZIndexUtils } from 'primereact/utils';\nimport { AngleDownIcon } from 'primereact/icons/angledown';\nimport { AngleRightIcon } from 'primereact/icons/angleright';\nimport { Ripple } from 'primereact/ripple';\n\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\n\nfunction toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\n\nfunction toPropertyKey(t) {\n  var i = toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\n\nfunction _defineProperty(e, r, t) {\n  return (r = toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\n\nfunction _arrayWithHoles(r) {\n  if (Array.isArray(r)) return r;\n}\n\nfunction _iterableToArrayLimit(r, l) {\n  var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"];\n  if (null != t) {\n    var e,\n      n,\n      i,\n      u,\n      a = [],\n      f = !0,\n      o = !1;\n    try {\n      if (i = (t = t.call(r)).next, 0 === l) {\n        if (Object(t) !== t) return;\n        f = !1;\n      } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0);\n    } catch (r) {\n      o = !0, n = r;\n    } finally {\n      try {\n        if (!f && null != t[\"return\"] && (u = t[\"return\"](), Object(u) !== u)) return;\n      } finally {\n        if (o) throw n;\n      }\n    }\n    return a;\n  }\n}\n\nfunction _arrayLikeToArray(r, a) {\n  (null == a || a > r.length) && (a = r.length);\n  for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e];\n  return n;\n}\n\nfunction _unsupportedIterableToArray(r, a) {\n  if (r) {\n    if (\"string\" == typeof r) return _arrayLikeToArray(r, a);\n    var t = {}.toString.call(r).slice(8, -1);\n    return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0;\n  }\n}\n\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nfunction _slicedToArray(r, e) {\n  return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest();\n}\n\nvar classes = {\n  start: 'p-menubar-start',\n  end: 'p-menubar-end',\n  button: 'p-menubar-button',\n  root: function root(_ref) {\n    var mobileActiveState = _ref.mobileActiveState;\n    return classNames('p-menubar p-component', {\n      'p-menubar-mobile-active': mobileActiveState\n    });\n  },\n  separator: 'p-menuitem-separator',\n  icon: 'p-menuitem-icon',\n  label: 'p-menuitem-text',\n  submenuIcon: 'p-submenu-icon',\n  menuitem: function menuitem(_ref2) {\n    var active = _ref2.active,\n      focused = _ref2.focused,\n      disabled = _ref2.disabled;\n    return classNames('p-menuitem', {\n      'p-menuitem-active p-highlight': active,\n      'p-focus': focused,\n      'p-disabled': disabled\n    });\n  },\n  menu: 'p-menubar-root-list',\n  content: 'p-menuitem-content',\n  submenu: 'p-submenu-list',\n  action: function action(_ref3) {\n    var disabled = _ref3.disabled;\n    return classNames('p-menuitem-link', {\n      'p-disabled': disabled\n    });\n  }\n};\nvar styles = \"\\n@layer primereact {\\n    .p-menubar {\\n        display: flex;\\n        align-items: center;\\n    }\\n\\n    .p-menubar ul {\\n        margin: 0;\\n        padding: 0;\\n        list-style: none;\\n    }\\n\\n    .p-menubar .p-menuitem-link {\\n        cursor: pointer;\\n        display: flex;\\n        align-items: center;\\n        text-decoration: none;\\n        overflow: hidden;\\n        position: relative;\\n    }\\n\\n    .p-menubar .p-menuitem-text {\\n        line-height: 1;\\n    }\\n\\n    .p-menubar .p-menuitem {\\n        position: relative;\\n    }\\n\\n    .p-menubar-root-list {\\n        display: flex;\\n        align-items: center;\\n        flex-wrap: wrap;\\n    }\\n\\n    .p-menubar-root-list > li ul {\\n        display: none;\\n        z-index: 1;\\n    }\\n\\n    .p-menubar-root-list > .p-menuitem-active > .p-submenu-list {\\n        display: block;\\n    }\\n\\n    .p-menubar .p-submenu-list {\\n        display: none;\\n        position: absolute;\\n        z-index: 5;\\n    }\\n\\n    .p-menubar .p-submenu-list > .p-menuitem-active > .p-submenu-list {\\n        display: block;\\n        left: 100%;\\n        top: 0;\\n    }\\n\\n    .p-menubar .p-submenu-list .p-menuitem .p-menuitem-content .p-menuitem-link .p-submenu-icon {\\n        margin-left: auto;\\n    }\\n\\n    .p-menubar .p-menubar-end {\\n        margin-left: auto;\\n        align-self: center;\\n    }\\n\\n    .p-menubar-button {\\n        display: none;\\n        cursor: pointer;\\n        align-items: center;\\n        justify-content: center;\\n        text-decoration: none;\\n    }\\n}\\n\";\nvar MenubarBase = ComponentBase.extend({\n  defaultProps: {\n    __TYPE: 'Menubar',\n    id: null,\n    model: null,\n    style: null,\n    className: null,\n    start: null,\n    ariaLabel: null,\n    ariaLabelledBy: null,\n    onFocus: null,\n    onBlur: null,\n    submenuIcon: null,\n    menuIcon: null,\n    end: null,\n    children: undefined\n  },\n  css: {\n    classes: classes,\n    styles: styles\n  }\n});\n\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\n\nfunction ownKeys$1(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread$1(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys$1(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$1(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nvar MenubarSub = /*#__PURE__*/React.memo(/*#__PURE__*/React.forwardRef(function (props, ref) {\n  var mergeProps = useMergeProps();\n  var ptm = props.ptm,\n    cx = props.cx;\n  var getPTOptions = function getPTOptions(processedItem, key, index) {\n    return ptm(key, {\n      props: props,\n      hostName: props.hostName,\n      context: {\n        item: processedItem,\n        index: index,\n        active: isItemActive(processedItem),\n        focused: isItemFocused(processedItem),\n        disabled: isItemDisabled(processedItem),\n        level: props.level\n      }\n    });\n  };\n  var onItemMouseEnter = function onItemMouseEnter(event, item) {\n    if (isItemDisabled(item) || props.mobileActive) {\n      event.preventDefault();\n      return;\n    }\n    props.onItemMouseEnter && props.onItemMouseEnter({\n      originalEvent: event,\n      processedItem: item\n    });\n  };\n  var onItemClick = function onItemClick(event, processedItem) {\n    var item = processedItem.item;\n    if (isItemDisabled(processedItem)) {\n      event.preventDefault();\n      return;\n    }\n    if (item.command) {\n      item.command({\n        originalEvent: event,\n        item: item\n      });\n    }\n    onLeafClick({\n      originalEvent: event,\n      processedItem: processedItem,\n      isFocus: true\n    });\n    if (!item.url) {\n      event.preventDefault();\n      event.stopPropagation();\n    }\n  };\n  var onLeafClick = function onLeafClick(event) {\n    props.onLeafClick && props.onLeafClick(event);\n  };\n  var getItemId = function getItemId(processedItem) {\n    var _processedItem$item;\n    return (_processedItem$item = processedItem.item) === null || _processedItem$item === void 0 ? void 0 : _processedItem$item.id;\n  };\n  var getItemDataId = function getItemDataId(processedItem) {\n    return \"\".concat(props.id, \"_\").concat(processedItem.key);\n  };\n  var getItemProp = function getItemProp(processedItem, name, params) {\n    return processedItem && processedItem.item ? ObjectUtils.getItemValue(processedItem.item[name], params) : undefined;\n  };\n  var isItemActive = function isItemActive(processedItem) {\n    return props.activeItemPath.some(function (path) {\n      return path.key === processedItem.key;\n    });\n  };\n  var isItemVisible = function isItemVisible(processedItem) {\n    return getItemProp(processedItem, 'visible') !== false;\n  };\n  var isItemDisabled = function isItemDisabled(processedItem) {\n    return getItemProp(processedItem, 'disabled');\n  };\n  var isItemFocused = function isItemFocused(processedItem) {\n    return props.focusedItemId === getItemDataId(processedItem);\n  };\n  var isItemGroup = function isItemGroup(processedItem) {\n    return ObjectUtils.isNotEmpty(processedItem.items);\n  };\n  var getAriaSetSize = function getAriaSetSize() {\n    return props.model.filter(function (processedItem) {\n      return isItemVisible(processedItem) && !getItemProp(processedItem, 'separator');\n    }).length;\n  };\n  var getAriaPosInset = function getAriaPosInset(index) {\n    return index - props.model.slice(0, index).filter(function (processedItem) {\n      return isItemVisible(processedItem) && getItemProp(processedItem, 'separator');\n    }).length + 1;\n  };\n  var createSeparator = function createSeparator(processedItem, index) {\n    var key = props.id + '_separator_' + index + '_' + processedItem.key;\n    var separatorProps = mergeProps({\n      'data-id': key,\n      className: cx('separator'),\n      role: 'separator'\n    }, ptm('separator', {\n      hostName: props.hostName\n    }));\n    return /*#__PURE__*/React.createElement(\"li\", _extends({}, separatorProps, {\n      key: key\n    }));\n  };\n  var createSubmenu = function createSubmenu(processedItem) {\n    var items = processedItem && processedItem.items;\n    if (items) {\n      return /*#__PURE__*/React.createElement(MenubarSub, {\n        id: props.id,\n        hostName: props.hostName,\n        menuProps: props.menuProps,\n        level: props.level + 1,\n        model: items,\n        activeItemPath: props.activeItemPath,\n        focusedItemId: props.focusedItemId,\n        onLeafClick: onLeafClick,\n        onItemMouseEnter: props.onItemMouseEnter,\n        submenuIcon: props.submenuIcon,\n        ptm: ptm,\n        style: {\n          display: isItemActive(processedItem) ? 'block' : 'none'\n        },\n        cx: cx\n      });\n    }\n    return null;\n  };\n  var createMenuitem = function createMenuitem(processedItem, index) {\n    var item = processedItem.item;\n    if (!isItemVisible(processedItem)) {\n      return null;\n    }\n    var id = getItemId(processedItem);\n    var dataId = getItemDataId(processedItem);\n    var active = isItemActive(processedItem);\n    var focused = isItemFocused(processedItem);\n    var disabled = isItemDisabled(processedItem) || false;\n    var group = isItemGroup(processedItem);\n    var linkClassName = classNames('p-menuitem-link', {\n      'p-disabled': disabled\n    });\n    var iconClassName = classNames('p-menuitem-icon', getItemProp(processedItem, 'icon'));\n    var iconProps = mergeProps({\n      className: cx('icon')\n    }, getPTOptions(processedItem, 'icon', index));\n    var icon = IconUtils.getJSXIcon(item.icon, _objectSpread$1({}, iconProps), {\n      props: props.menuProps\n    });\n    var labelProps = mergeProps({\n      className: cx('label')\n    }, getPTOptions(processedItem, 'label', index));\n    var label = item.label && /*#__PURE__*/React.createElement(\"span\", labelProps, item.label);\n    var items = getItemProp(processedItem, 'items');\n    var submenuIconClassName = 'p-submenu-icon';\n    var submenuIconProps = mergeProps({\n      className: cx('submenuIcon')\n    }, getPTOptions(processedItem, 'submenuIcon', index));\n    var submenuIcon = items && IconUtils.getJSXIcon(!props.root ? props.submenuIcon || /*#__PURE__*/React.createElement(AngleRightIcon, submenuIconProps) : props.submenuIcon || /*#__PURE__*/React.createElement(AngleDownIcon, submenuIconProps), _objectSpread$1({}, submenuIconProps), {\n      props: _objectSpread$1({\n        menuProps: props.menuProps\n      }, props)\n    });\n    var submenu = createSubmenu(processedItem);\n    var actionProps = mergeProps({\n      href: item.url || '#',\n      tabIndex: '-1',\n      className: cx('action', {\n        disabled: disabled\n      }),\n      onFocus: function onFocus(event) {\n        return event.stopPropagation();\n      },\n      target: getItemProp(processedItem, 'target'),\n      'aria-haspopup': items != null\n    }, getPTOptions(processedItem, 'action', index));\n    var content = /*#__PURE__*/React.createElement(\"a\", actionProps, icon, label, submenuIcon, /*#__PURE__*/React.createElement(Ripple, null));\n    if (item.template) {\n      var defaultContentOptions = {\n        className: linkClassName,\n        labelClassName: 'p-menuitem-text',\n        iconClassName: iconClassName,\n        submenuIconClassName: submenuIconClassName,\n        element: content,\n        props: props\n      };\n      content = ObjectUtils.getJSXElement(item.template, item, defaultContentOptions);\n    }\n    var contentProps = mergeProps({\n      onClick: function onClick(event) {\n        return onItemClick(event, processedItem);\n      },\n      onMouseEnter: function onMouseEnter(event) {\n        return onItemMouseEnter(event, processedItem);\n      },\n      className: cx('content')\n    }, getPTOptions(processedItem, 'content', index));\n    var itemClassName = getItemProp(processedItem, 'className');\n    var menuitemProps = mergeProps(_defineProperty({\n      id: id,\n      'data-id': dataId,\n      role: 'menuitem',\n      'aria-label': item.label,\n      'aria-disabled': disabled,\n      'aria-expanded': group ? active : undefined,\n      'aria-haspopup': group && !item.url ? 'menu' : undefined,\n      'aria-setsize': getAriaSetSize(),\n      'aria-posinset': getAriaPosInset(index),\n      'data-p-highlight': active,\n      'data-p-focused': focused,\n      'data-p-disabled': disabled,\n      className: classNames(itemClassName, cx('menuitem', {\n        active: active,\n        focused: focused,\n        disabled: disabled\n      }))\n    }, \"data-p-disabled\", disabled || false), getPTOptions(processedItem, 'menuitem', index));\n    return /*#__PURE__*/React.createElement(\"li\", _extends({}, menuitemProps, {\n      key: \"\".concat(dataId)\n    }), /*#__PURE__*/React.createElement(\"div\", contentProps, content), submenu);\n  };\n  var createItem = function createItem(processedItem, index) {\n    if (processedItem.visible === false) {\n      return null;\n    }\n    return getItemProp(processedItem, 'separator') ? createSeparator(processedItem, index) : createMenuitem(processedItem, index);\n  };\n  var createMenu = function createMenu() {\n    return props.model ? props.model.map(createItem) : null;\n  };\n  var role = props.root ? 'menubar' : 'menu';\n  var ptKey = props.root ? 'menu' : 'submenu';\n  var tabIndex = props.root ? '0' : null;\n  var submenu = createMenu();\n  var menuProps = mergeProps({\n    ref: ref,\n    className: cx(ptKey),\n    level: props.level,\n    onFocus: props.onFocus,\n    onBlur: props.onBlur,\n    onKeyDown: props.onKeyDown,\n    'data-id': props.id,\n    tabIndex: tabIndex,\n    'aria-activedescendant': props.ariaActivedescendant,\n    style: props.style,\n    role: role\n  }, ptm(ptKey));\n  return /*#__PURE__*/React.createElement(\"ul\", menuProps, submenu);\n}));\nMenubarSub.displayName = 'MenubarSub';\n\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nvar Menubar = /*#__PURE__*/React.memo(/*#__PURE__*/React.forwardRef(function (inProps, ref) {\n  var mergeProps = useMergeProps();\n  var context = React.useContext(PrimeReactContext);\n  var props = MenubarBase.getProps(inProps, context);\n  var _React$useState = React.useState(props.id),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    idState = _React$useState2[0],\n    setIdState = _React$useState2[1];\n  var _React$useState3 = React.useState(false),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    mobileActiveState = _React$useState4[0],\n    setMobileActiveState = _React$useState4[1];\n  var _React$useState5 = React.useState(false),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    focused = _React$useState6[0],\n    setFocused = _React$useState6[1];\n  var _React$useState7 = React.useState({\n      index: -1,\n      level: 0,\n      parentKey: ''\n    }),\n    _React$useState8 = _slicedToArray(_React$useState7, 2),\n    focusedItemInfo = _React$useState8[0],\n    setFocusedItemInfo = _React$useState8[1];\n  var _React$useState9 = React.useState(null),\n    _React$useState10 = _slicedToArray(_React$useState9, 2),\n    focusedItemId = _React$useState10[0],\n    setFocusedItemId = _React$useState10[1];\n  var _React$useState11 = React.useState([]),\n    _React$useState12 = _slicedToArray(_React$useState11, 2),\n    activeItemPath = _React$useState12[0],\n    setActiveItemPath = _React$useState12[1];\n  var _React$useState13 = React.useState([]),\n    _React$useState14 = _slicedToArray(_React$useState13, 2),\n    visibleItems = _React$useState14[0],\n    setVisibleItems = _React$useState14[1];\n  var _React$useState15 = React.useState([]),\n    _React$useState16 = _slicedToArray(_React$useState15, 2),\n    processedItems = _React$useState16[0],\n    setProcessedItems = _React$useState16[1];\n  var _React$useState17 = React.useState(false),\n    _React$useState18 = _slicedToArray(_React$useState17, 2),\n    focusTrigger = _React$useState18[0],\n    setFocusTrigger = _React$useState18[1];\n  var _React$useState19 = React.useState(false),\n    _React$useState20 = _slicedToArray(_React$useState19, 2),\n    dirty = _React$useState20[0],\n    setDirty = _React$useState20[1];\n  var elementRef = React.useRef(null);\n  var rootMenuRef = React.useRef(null);\n  var menuButtonRef = React.useRef(null);\n  var searchValue = React.useRef('');\n  var searchTimeout = React.useRef(null);\n  var reverseTrigger = React.useRef(false);\n  var _MenubarBase$setMetaD = MenubarBase.setMetaData({\n      props: props,\n      state: {\n        id: idState,\n        mobileActive: mobileActiveState\n      }\n    }),\n    ptm = _MenubarBase$setMetaD.ptm,\n    cx = _MenubarBase$setMetaD.cx,\n    isUnstyled = _MenubarBase$setMetaD.isUnstyled;\n  useHandleStyle(MenubarBase.css.styles, isUnstyled, {\n    name: 'menubar'\n  });\n  var _useEventListener = useEventListener({\n      type: 'click',\n      listener: function listener(event) {\n        var isOutsideButton = menuButtonRef.current && !menuButtonRef.current.contains(event.target);\n        if (isOutsideButton) {\n          hide();\n        }\n      },\n      options: {\n        capture: true\n      }\n    }),\n    _useEventListener2 = _slicedToArray(_useEventListener, 2),\n    bindOutsideClickListener = _useEventListener2[0],\n    unbindOutsideClickListener = _useEventListener2[1];\n  var _useResizeListener = useResizeListener({\n      listener: function listener(event) {\n        if (!DomHandler.isTouchDevice()) {\n          hide(event);\n        }\n      }\n    }),\n    _useResizeListener2 = _slicedToArray(_useResizeListener, 2),\n    bindResizeListener = _useResizeListener2[0],\n    unbindResizeListener = _useResizeListener2[1];\n  var toggle = function toggle(event) {\n    if (mobileActiveState) {\n      setMobileActiveState(false);\n      hide();\n    } else {\n      setMobileActiveState(true);\n      setTimeout(function () {\n        show();\n      }, 1);\n    }\n    event.preventDefault();\n  };\n  var show = function show() {\n    setFocusedItemInfo({\n      index: findFirstFocusedItemIndex(),\n      level: 0,\n      parentKey: ''\n    });\n    DomHandler.focus(rootMenuRef.current);\n  };\n  var hide = function hide(isFocus) {\n    if (mobileActiveState) {\n      setMobileActiveState(false);\n      setTimeout(function () {\n        DomHandler.focus(menuButtonRef.current);\n      }, 0);\n    }\n    setActiveItemPath([]);\n    setFocusedItemInfo({\n      index: -1,\n      level: 0,\n      parentKey: ''\n    });\n    isFocus && DomHandler.focus(rootMenuRef.current);\n    setDirty(false);\n  };\n  var menuButtonKeydown = function menuButtonKeydown(event) {\n    (event.code === 'Enter' || event.code === 'NumpadEnter' || event.code === 'Space') && toggle(event);\n  };\n  var getItemProp = function getItemProp(item, name) {\n    return item ? ObjectUtils.getItemValue(item[name]) : undefined;\n  };\n  var getItemLabel = function getItemLabel(item) {\n    return getItemProp(item, 'label');\n  };\n  var isItemDisabled = function isItemDisabled(item) {\n    return getItemProp(item, 'disabled');\n  };\n  var isItemSeparator = function isItemSeparator(item) {\n    return getItemProp(item, 'separator');\n  };\n  var getProccessedItemLabel = function getProccessedItemLabel(processedItem) {\n    return processedItem ? getItemLabel(processedItem.item) : undefined;\n  };\n  var isProccessedItemGroup = function isProccessedItemGroup(processedItem) {\n    return processedItem && ObjectUtils.isNotEmpty(processedItem.items);\n  };\n  var onFocus = function onFocus(event) {\n    setFocused(true);\n    setFocusedItemInfo(focusedItemInfo.index !== -1 ? focusedItemInfo : {\n      index: findFirstFocusedItemIndex(),\n      level: 0,\n      parentKey: ''\n    });\n    props.onFocus && props.onFocus(event);\n  };\n  var onBlur = function onBlur(event) {\n    setFocused(false);\n    setFocusedItemInfo({\n      index: -1,\n      level: 0,\n      parentKey: ''\n    });\n    searchValue.current = '';\n    setDirty(false);\n    props.onBlur && props.onBlur(event);\n  };\n  var onKeyDown = function onKeyDown(event) {\n    var metaKey = event.metaKey || event.ctrlKey;\n    var code = event.code;\n    switch (code) {\n      case 'ArrowDown':\n        onArrowDownKey(event);\n        break;\n      case 'ArrowUp':\n        onArrowUpKey(event);\n        break;\n      case 'ArrowLeft':\n        onArrowLeftKey(event);\n        break;\n      case 'ArrowRight':\n        onArrowRightKey(event);\n        break;\n      case 'Home':\n        onHomeKey(event);\n        break;\n      case 'End':\n        onEndKey(event);\n        break;\n      case 'Space':\n        onSpaceKey(event);\n        break;\n      case 'Enter':\n      case 'NumpadEnter':\n        onEnterKey(event);\n        break;\n      case 'Escape':\n        onEscapeKey();\n        break;\n      case 'Tab':\n        onTabKey(event);\n        break;\n      case 'PageDown':\n      case 'PageUp':\n      case 'Backspace':\n      case 'ShiftLeft':\n      case 'ShiftRight':\n        break;\n      default:\n        if (!metaKey && ObjectUtils.isPrintableCharacter(event.key)) {\n          searchItems(event, event.key);\n        }\n        break;\n    }\n  };\n  var onItemChange = function onItemChange(event) {\n    var processedItem = event.processedItem,\n      isFocus = event.isFocus;\n    if (ObjectUtils.isEmpty(processedItem)) {\n      return;\n    }\n    var index = processedItem.index,\n      key = processedItem.key,\n      level = processedItem.level,\n      parentKey = processedItem.parentKey,\n      items = processedItem.items;\n    var grouped = ObjectUtils.isNotEmpty(items);\n    var _activeItemPath = activeItemPath.filter(function (p) {\n      return p.parentKey !== parentKey && p.parentKey !== key;\n    });\n    grouped && _activeItemPath.push(processedItem);\n    setFocusedItemInfo({\n      index: index,\n      level: level,\n      parentKey: parentKey\n    });\n    setActiveItemPath(_activeItemPath);\n    grouped && setDirty(true);\n    isFocus && DomHandler.focus(rootMenuRef.current);\n  };\n  var onItemClick = function onItemClick(event) {\n    var originalEvent = event.originalEvent,\n      processedItem = event.processedItem;\n    var grouped = isProccessedItemGroup(processedItem);\n    var root = ObjectUtils.isEmpty(processedItem.parent);\n    var selected = isSelected(processedItem);\n    if (selected) {\n      var index = processedItem.index,\n        key = processedItem.key,\n        level = processedItem.level,\n        parentKey = processedItem.parentKey;\n      setActiveItemPath(activeItemPath.filter(function (p) {\n        return key !== p.key && key.startsWith(p.key);\n      }));\n      setFocusedItemInfo({\n        index: index,\n        level: level,\n        parentKey: parentKey\n      });\n      if (!grouped) {\n        setDirty(!root);\n      }\n      setTimeout(function () {\n        DomHandler.focus(rootMenuRef.current);\n        if (grouped) {\n          setDirty(true);\n        }\n      }, 0);\n    } else if (grouped) {\n      DomHandler.focus(rootMenuRef.current);\n      onItemChange({\n        originalEvent: originalEvent,\n        processedItem: processedItem\n      });\n    } else {\n      var rootProcessedItem = root ? processedItem : activeItemPath.find(function (p) {\n        return p.parentKey === '';\n      });\n      var rootProcessedItemIndex = rootProcessedItem ? rootProcessedItem.index : -1;\n      hide(originalEvent);\n      setFocusedItemInfo({\n        index: rootProcessedItemIndex,\n        parentKey: rootProcessedItem ? rootProcessedItem.parentKey : ''\n      });\n      setMobileActiveState(false);\n    }\n  };\n  var onItemMouseEnter = function onItemMouseEnter(event) {\n    if (!mobileActiveState && dirty) {\n      onItemChange(event);\n    }\n  };\n  var onArrowDownKey = function onArrowDownKey(event) {\n    var processedItem = visibleItems[focusedItemInfo.index];\n    var root = processedItem ? ObjectUtils.isEmpty(processedItem.parent) : null;\n    if (root) {\n      var grouped = isProccessedItemGroup(processedItem);\n      if (grouped) {\n        onItemChange({\n          originalEvent: event,\n          processedItem: processedItem\n        });\n        setFocusedItemInfo({\n          index: -1,\n          parentKey: processedItem.key\n        });\n        setTimeout(function () {\n          return setFocusTrigger(true);\n        }, 0);\n      }\n    } else {\n      var itemIndex = focusedItemInfo.index !== -1 ? findNextItemIndex(focusedItemInfo.index) : findFirstFocusedItemIndex();\n      changeFocusedItemIndex(itemIndex);\n    }\n    event.preventDefault();\n  };\n  var onArrowUpKey = function onArrowUpKey(event) {\n    var processedItem = visibleItems[focusedItemInfo.index];\n    var root = ObjectUtils.isEmpty(processedItem.parent);\n    if (root) {\n      var grouped = isProccessedItemGroup(processedItem);\n      if (grouped) {\n        onItemChange({\n          originalEvent: event,\n          processedItem: processedItem\n        });\n        setFocusedItemInfo({\n          index: -1,\n          parentKey: processedItem.key\n        });\n        reverseTrigger.current = true;\n        setTimeout(function () {\n          return setFocusTrigger(true);\n        }, 0);\n      }\n    } else {\n      var parentItem = activeItemPath.find(function (p) {\n        return p.key === processedItem.parentKey;\n      });\n      if (focusedItemInfo.index === 0 && parentItem && parentItem.parentKey === '') {\n        setFocusedItemInfo({\n          index: -1,\n          parentKey: parentItem ? parentItem.parentKey : ''\n        });\n        searchValue.current = '';\n        onArrowLeftKey(event);\n      } else {\n        var itemIndex = focusedItemInfo.index !== -1 ? findPrevItemIndex(focusedItemInfo.index) : findLastFocusedItemIndex();\n        changeFocusedItemIndex(itemIndex);\n      }\n    }\n    event.preventDefault();\n  };\n  var onArrowLeftKey = function onArrowLeftKey(event) {\n    var processedItem = visibleItems[focusedItemInfo.index];\n    var parentItem = processedItem ? activeItemPath.find(function (p) {\n      return p.key === processedItem.parentKey;\n    }) : null;\n    if (parentItem) {\n      onItemChange({\n        originalEvent: event,\n        processedItem: parentItem\n      });\n      setActiveItemPath(activeItemPath.filter(function (p) {\n        return p.key !== parentItem.key;\n      }));\n    } else {\n      var itemIndex = focusedItemInfo.index !== -1 ? findPrevItemIndex(focusedItemInfo.index) : findLastFocusedItemIndex();\n      changeFocusedItemIndex(itemIndex);\n    }\n    event.preventDefault();\n  };\n  var onArrowRightKey = function onArrowRightKey(event) {\n    var processedItem = visibleItems[focusedItemInfo.index];\n    var parentItem = processedItem ? activeItemPath.find(function (p) {\n      return p.key === processedItem.parentKey;\n    }) : null;\n    if (parentItem) {\n      var grouped = isProccessedItemGroup(processedItem);\n      if (grouped) {\n        onItemChange({\n          originalEvent: event,\n          processedItem: processedItem\n        });\n        setFocusedItemInfo({\n          index: -1,\n          parentKey: processedItem.key\n        });\n        setTimeout(function () {\n          return setFocusTrigger(true);\n        }, 0);\n      }\n    } else {\n      var itemIndex = focusedItemInfo.index !== -1 ? findNextItemIndex(focusedItemInfo.index) : findFirstFocusedItemIndex();\n      changeFocusedItemIndex(itemIndex);\n    }\n    event.preventDefault();\n  };\n  var onHomeKey = function onHomeKey(event) {\n    changeFocusedItemIndex(findFirstItemIndex());\n    event.preventDefault();\n  };\n  var onEndKey = function onEndKey(event) {\n    changeFocusedItemIndex(findLastItemIndex());\n    event.preventDefault();\n  };\n  var onEnterKey = function onEnterKey(event) {\n    if (focusedItemInfo.index !== -1) {\n      var element = DomHandler.findSingle(rootMenuRef.current, \"li[data-id=\\\"\".concat(\"\".concat(focusedItemId), \"\\\"]\"));\n      var anchorElement = element && DomHandler.findSingle(element, 'a[data-pc-section=\"action\"]');\n      anchorElement ? anchorElement.click() : element && element.click();\n    }\n    event.preventDefault();\n  };\n  var onSpaceKey = function onSpaceKey(event) {\n    onEnterKey(event);\n  };\n  var onEscapeKey = function onEscapeKey(event) {\n    hide(true);\n    setFocusedItemInfo({\n      focusedItemInfo: focusedItemInfo,\n      index: findFirstFocusedItemIndex()\n    });\n  };\n  var onTabKey = function onTabKey(event) {\n    if (focusedItemInfo.index !== -1) {\n      var processedItem = visibleItems[focusedItemInfo.index];\n      var grouped = isProccessedItemGroup(processedItem);\n      !grouped && onItemChange({\n        originalEvent: event,\n        processedItem: processedItem\n      });\n    }\n    hide();\n  };\n  var isItemMatched = function isItemMatched(processedItem) {\n    return isValidItem(processedItem) && getProccessedItemLabel(processedItem).toLocaleLowerCase().startsWith(searchValue.current.toLocaleLowerCase());\n  };\n  var isValidItem = function isValidItem(processedItem) {\n    return !!processedItem && !isItemDisabled(processedItem.item) && !isItemSeparator(processedItem.item);\n  };\n  var isValidSelectedItem = function isValidSelectedItem(processedItem) {\n    return isValidItem(processedItem) && isSelected(processedItem);\n  };\n  var isSelected = function isSelected(processedItem) {\n    return activeItemPath.some(function (p) {\n      return p.key === processedItem.key;\n    });\n  };\n  var findFirstItemIndex = function findFirstItemIndex() {\n    return visibleItems.findIndex(function (processedItem) {\n      return isValidItem(processedItem);\n    });\n  };\n  var findLastItemIndex = function findLastItemIndex() {\n    return ObjectUtils.findLastIndex(visibleItems, function (processedItem) {\n      return isValidItem(processedItem);\n    });\n  };\n  var findNextItemIndex = function findNextItemIndex(index) {\n    var matchedItemIndex = index < visibleItems.length - 1 ? visibleItems.slice(index + 1).findIndex(function (processedItem) {\n      return isValidItem(processedItem);\n    }) : -1;\n    return matchedItemIndex > -1 ? matchedItemIndex + index + 1 : index;\n  };\n  var findPrevItemIndex = function findPrevItemIndex(index) {\n    var matchedItemIndex = index > 0 ? ObjectUtils.findLastIndex(visibleItems.slice(0, index), function (processedItem) {\n      return isValidItem(processedItem);\n    }) : -1;\n    return matchedItemIndex > -1 ? matchedItemIndex : index;\n  };\n  var findSelectedItemIndex = function findSelectedItemIndex() {\n    return visibleItems.findIndex(function (processedItem) {\n      return isValidSelectedItem(processedItem);\n    });\n  };\n  var findFirstFocusedItemIndex = function findFirstFocusedItemIndex() {\n    var selectedIndex = findSelectedItemIndex();\n    return selectedIndex;\n  };\n  var findLastFocusedItemIndex = function findLastFocusedItemIndex() {\n    var selectedIndex = findSelectedItemIndex();\n    return selectedIndex;\n  };\n  var searchItems = function searchItems(event, _char) {\n    searchValue.current = (searchValue.current || '') + _char;\n    var itemIndex = -1;\n    var matched = false;\n    if (focusedItemInfo.index !== -1) {\n      itemIndex = visibleItems.slice(focusedItemInfo.index).findIndex(function (processedItem) {\n        return isItemMatched(processedItem);\n      });\n      itemIndex = itemIndex === -1 ? visibleItems.slice(0, focusedItemInfo.index).findIndex(function (processedItem) {\n        return isItemMatched(processedItem);\n      }) : itemIndex + focusedItemInfo.index;\n    } else {\n      itemIndex = visibleItems.findIndex(function (processedItem) {\n        return isItemMatched(processedItem);\n      });\n    }\n    if (itemIndex !== -1) {\n      matched = true;\n    }\n    if (itemIndex === -1 && focusedItemInfo.index === -1) {\n      itemIndex = findFirstFocusedItemIndex();\n    }\n    if (itemIndex !== -1) {\n      changeFocusedItemIndex(itemIndex);\n    }\n    if (searchTimeout.current) {\n      clearTimeout(searchTimeout.current);\n    }\n    searchTimeout.current = setTimeout(function () {\n      searchValue.current = '';\n      searchTimeout.current = null;\n    }, 500);\n    return matched;\n  };\n  var changeFocusedItemIndex = function changeFocusedItemIndex(index) {\n    if (focusedItemInfo.index !== index) {\n      setFocusedItemInfo(_objectSpread(_objectSpread({}, focusedItemInfo), {}, {\n        index: index\n      }));\n      scrollInView();\n    }\n  };\n  var scrollInView = function scrollInView() {\n    var index = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : -1;\n    var id = index !== -1 ? \"\".concat(idState, \"_\").concat(index) : focusedItemId;\n    var element = DomHandler.findSingle(rootMenuRef.current, \"li[data-id=\\\"\".concat(id, \"\\\"]\"));\n    if (element) {\n      element.scrollIntoView && element.scrollIntoView({\n        block: 'nearest',\n        inline: 'start'\n      });\n    }\n  };\n  var _createProcessedItems = function createProcessedItems(items) {\n    var level = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n    var parent = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    var parentKey = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : '';\n    var _processedItems = [];\n    items && items.forEach(function (item, index) {\n      var key = (parentKey !== '' ? parentKey + '_' : '') + index;\n      var newItem = {\n        item: item,\n        index: index,\n        level: level,\n        key: key,\n        parent: parent,\n        parentKey: parentKey\n      };\n      newItem.items = _createProcessedItems(item.items, level + 1, newItem, key);\n      _processedItems.push(newItem);\n    });\n    return _processedItems;\n  };\n  useMountEffect(function () {\n    if (!idState) {\n      setIdState(UniqueComponentId());\n    }\n  });\n  useUpdateEffect(function () {\n    if (mobileActiveState) {\n      bindOutsideClickListener();\n      bindResizeListener();\n      ZIndexUtils.set('menu', rootMenuRef.current, context && context.autoZIndex || PrimeReact.autoZIndex, context && context.zIndex.menu || PrimeReact.zIndex.menu);\n    } else {\n      unbindResizeListener();\n      unbindOutsideClickListener();\n      ZIndexUtils.clear(rootMenuRef.current);\n    }\n  }, [mobileActiveState]);\n  React.useEffect(function () {\n    var itemsToProcess = props.model || [];\n    var processed = _createProcessedItems(itemsToProcess, 0, null, '');\n    setProcessedItems(processed);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [props.model]);\n  useUpdateEffect(function () {\n    var processedItem = activeItemPath.find(function (p) {\n      return p.key === focusedItemInfo.parentKey;\n    });\n    var _processedItems = processedItem ? processedItem.items : processedItems;\n    setVisibleItems(_processedItems);\n  }, [activeItemPath, focusedItemInfo, processedItems]);\n  useUpdateEffect(function () {\n    if (ObjectUtils.isNotEmpty(activeItemPath)) {\n      bindOutsideClickListener();\n      bindResizeListener();\n    } else {\n      unbindOutsideClickListener();\n      unbindResizeListener();\n    }\n  }, [activeItemPath]);\n  useUpdateEffect(function () {\n    if (focusTrigger) {\n      var itemIndex = focusedItemInfo.index !== -1 ? findNextItemIndex(focusedItemInfo.index) : reverseTrigger.current ? findLastItemIndex() : findFirstFocusedItemIndex();\n      changeFocusedItemIndex(itemIndex);\n      reverseTrigger.current = false;\n      setFocusTrigger(false);\n    }\n  }, [focusTrigger]);\n  useUpdateEffect(function () {\n    setFocusedItemId(focusedItemInfo.index !== -1 ? \"\".concat(idState).concat(ObjectUtils.isNotEmpty(focusedItemInfo.parentKey) ? '_' + focusedItemInfo.parentKey : '', \"_\").concat(focusedItemInfo.index) : null);\n  }, [focusedItemInfo]);\n  useUnmountEffect(function () {\n    ZIndexUtils.clear(rootMenuRef.current);\n  });\n  React.useImperativeHandle(ref, function () {\n    return {\n      props: props,\n      toggle: toggle,\n      getElement: function getElement() {\n        return elementRef.current;\n      },\n      getRootMenu: function getRootMenu() {\n        return rootMenuRef.current;\n      },\n      getMenuButton: function getMenuButton() {\n        return menuButtonRef.current;\n      }\n    };\n  });\n  var createStartContent = function createStartContent() {\n    if (props.start) {\n      var _start = ObjectUtils.getJSXElement(props.start, props);\n      var startProps = mergeProps({\n        className: cx('start')\n      }, ptm('start'));\n      return /*#__PURE__*/React.createElement(\"div\", startProps, _start);\n    }\n    return null;\n  };\n  var createEndContent = function createEndContent() {\n    if (props.end) {\n      var _end = ObjectUtils.getJSXElement(props.end, props);\n      var endProps = mergeProps({\n        className: cx('end')\n      }, ptm('end'));\n      return /*#__PURE__*/React.createElement(\"div\", endProps, _end);\n    }\n    return null;\n  };\n  var createMenuButton = function createMenuButton() {\n    if (props.model && props.model.length < 1) {\n      return null;\n    }\n    var buttonProps = mergeProps(_defineProperty(_defineProperty(_defineProperty(_defineProperty({\n      ref: menuButtonRef,\n      href: '#',\n      tabIndex: '0',\n      'aria-haspopup': mobileActiveState && props.model && props.model.length > 0 ? true : false,\n      'aria-expanded': mobileActiveState,\n      'aria-label': ariaLabel('navigation'),\n      'aria-controls': idState,\n      role: 'button'\n    }, \"tabIndex\", 0), \"className\", cx('button')), \"onKeyDown\", function onKeyDown(e) {\n      return menuButtonKeydown(e);\n    }), \"onClick\", function onClick(e) {\n      return toggle(e);\n    }), ptm('button'));\n    var popupIconProps = mergeProps(ptm('popupIcon'));\n    var icon = props.menuIcon || /*#__PURE__*/React.createElement(BarsIcon, popupIconProps);\n    var menuIcon = IconUtils.getJSXIcon(icon, _objectSpread({}, popupIconProps), {\n      props: props\n    });\n\n    /* eslint-disable */\n    var button = /*#__PURE__*/React.createElement(\"a\", buttonProps, menuIcon);\n    /* eslint-enable */\n\n    return button;\n  };\n  var start = createStartContent();\n  var end = createEndContent();\n  var menuButton = createMenuButton();\n  var submenu = /*#__PURE__*/React.createElement(MenubarSub, {\n    hostName: \"Menubar\",\n    ariaActivedescendant: focused ? focusedItemId : undefined,\n    level: 0,\n    id: idState,\n    ref: rootMenuRef,\n    menuProps: props,\n    model: processedItems,\n    onLeafClick: onItemClick,\n    onItemMouseEnter: onItemMouseEnter,\n    onFocus: onFocus,\n    onBlur: onBlur,\n    onKeyDown: onKeyDown,\n    root: true,\n    activeItemPath: activeItemPath,\n    focusedItemId: focused ? focusedItemId : undefined,\n    submenuIcon: props.submenuIcon,\n    ptm: ptm,\n    cx: cx\n  });\n  var rootProps = mergeProps({\n    id: props.id,\n    ref: elementRef,\n    className: classNames(props.className, cx('root', {\n      mobileActiveState: mobileActiveState\n    })),\n    style: props.style\n  }, MenubarBase.getOtherProps(props), ptm('root'));\n  return /*#__PURE__*/React.createElement(\"div\", rootProps, start, menuButton, submenu, end);\n}));\nMenubar.displayName = 'Menubar';\n\nexport { Menubar };\n"], "mappings": "AAAA,YAAY;;AACZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,IAAIC,iBAAiB,EAAEC,SAAS,QAAQ,gBAAgB;AACzE,SAASC,aAAa,EAAEC,cAAc,QAAQ,0BAA0B;AACxE,SAASC,aAAa,EAAEC,gBAAgB,EAAEC,iBAAiB,EAAEC,cAAc,EAAEC,eAAe,EAAEC,gBAAgB,QAAQ,kBAAkB;AACxI,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,UAAU,EAAEC,WAAW,EAAEC,SAAS,EAAEC,UAAU,EAAEC,iBAAiB,EAAEC,WAAW,QAAQ,kBAAkB;AACjH,SAASC,aAAa,QAAQ,4BAA4B;AAC1D,SAASC,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,MAAM,QAAQ,mBAAmB;AAE1C,SAASC,OAAOA,CAACC,CAAC,EAAE;EAClB,yBAAyB;;EAEzB,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,CAAC,EAAE;IAChG,OAAO,OAAOA,CAAC;EACjB,CAAC,GAAG,UAAUA,CAAC,EAAE;IACf,OAAOA,CAAC,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,CAAC,CAACG,WAAW,KAAKF,MAAM,IAAID,CAAC,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,CAAC;EACrH,CAAC,EAAED,OAAO,CAACC,CAAC,CAAC;AACf;AAEA,SAASK,WAAWA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACzB,IAAI,QAAQ,IAAIR,OAAO,CAACO,CAAC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAC1C,IAAIE,CAAC,GAAGF,CAAC,CAACL,MAAM,CAACI,WAAW,CAAC;EAC7B,IAAI,KAAK,CAAC,KAAKG,CAAC,EAAE;IAChB,IAAIC,CAAC,GAAGD,CAAC,CAACE,IAAI,CAACJ,CAAC,EAAEC,CAAC,IAAI,SAAS,CAAC;IACjC,IAAI,QAAQ,IAAIR,OAAO,CAACU,CAAC,CAAC,EAAE,OAAOA,CAAC;IACpC,MAAM,IAAIE,SAAS,CAAC,8CAA8C,CAAC;EACrE;EACA,OAAO,CAAC,QAAQ,KAAKJ,CAAC,GAAGK,MAAM,GAAGC,MAAM,EAAEP,CAAC,CAAC;AAC9C;AAEA,SAASQ,aAAaA,CAACR,CAAC,EAAE;EACxB,IAAIG,CAAC,GAAGJ,WAAW,CAACC,CAAC,EAAE,QAAQ,CAAC;EAChC,OAAO,QAAQ,IAAIP,OAAO,CAACU,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAC5C;AAEA,SAASM,eAAeA,CAACP,CAAC,EAAED,CAAC,EAAED,CAAC,EAAE;EAChC,OAAO,CAACC,CAAC,GAAGO,aAAa,CAACP,CAAC,CAAC,KAAKC,CAAC,GAAGQ,MAAM,CAACC,cAAc,CAACT,CAAC,EAAED,CAAC,EAAE;IAC/DW,KAAK,EAAEZ,CAAC;IACRa,UAAU,EAAE,CAAC,CAAC;IACdC,YAAY,EAAE,CAAC,CAAC;IAChBC,QAAQ,EAAE,CAAC;EACb,CAAC,CAAC,GAAGb,CAAC,CAACD,CAAC,CAAC,GAAGD,CAAC,EAAEE,CAAC;AAClB;AAEA,SAASc,eAAeA,CAACf,CAAC,EAAE;EAC1B,IAAIgB,KAAK,CAACC,OAAO,CAACjB,CAAC,CAAC,EAAE,OAAOA,CAAC;AAChC;AAEA,SAASkB,qBAAqBA,CAAClB,CAAC,EAAEmB,CAAC,EAAE;EACnC,IAAIpB,CAAC,GAAG,IAAI,IAAIC,CAAC,GAAG,IAAI,GAAG,WAAW,IAAI,OAAON,MAAM,IAAIM,CAAC,CAACN,MAAM,CAACC,QAAQ,CAAC,IAAIK,CAAC,CAAC,YAAY,CAAC;EAChG,IAAI,IAAI,IAAID,CAAC,EAAE;IACb,IAAIE,CAAC;MACHmB,CAAC;MACDlB,CAAC;MACDmB,CAAC;MACDC,CAAC,GAAG,EAAE;MACNC,CAAC,GAAG,CAAC,CAAC;MACN9B,CAAC,GAAG,CAAC,CAAC;IACR,IAAI;MACF,IAAIS,CAAC,GAAG,CAACH,CAAC,GAAGA,CAAC,CAACI,IAAI,CAACH,CAAC,CAAC,EAAEwB,IAAI,EAAE,CAAC,KAAKL,CAAC,EAAE;QACrC,IAAIV,MAAM,CAACV,CAAC,CAAC,KAAKA,CAAC,EAAE;QACrBwB,CAAC,GAAG,CAAC,CAAC;MACR,CAAC,MAAM,OAAO,EAAEA,CAAC,GAAG,CAACtB,CAAC,GAAGC,CAAC,CAACC,IAAI,CAACJ,CAAC,CAAC,EAAE0B,IAAI,CAAC,KAAKH,CAAC,CAACI,IAAI,CAACzB,CAAC,CAACU,KAAK,CAAC,EAAEW,CAAC,CAACK,MAAM,KAAKR,CAAC,CAAC,EAAEI,CAAC,GAAG,CAAC,CAAC,CAAC;IACzF,CAAC,CAAC,OAAOvB,CAAC,EAAE;MACVP,CAAC,GAAG,CAAC,CAAC,EAAE2B,CAAC,GAAGpB,CAAC;IACf,CAAC,SAAS;MACR,IAAI;QACF,IAAI,CAACuB,CAAC,IAAI,IAAI,IAAIxB,CAAC,CAAC,QAAQ,CAAC,KAAKsB,CAAC,GAAGtB,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAEU,MAAM,CAACY,CAAC,CAAC,KAAKA,CAAC,CAAC,EAAE;MACzE,CAAC,SAAS;QACR,IAAI5B,CAAC,EAAE,MAAM2B,CAAC;MAChB;IACF;IACA,OAAOE,CAAC;EACV;AACF;AAEA,SAASM,iBAAiBA,CAAC5B,CAAC,EAAEsB,CAAC,EAAE;EAC/B,CAAC,IAAI,IAAIA,CAAC,IAAIA,CAAC,GAAGtB,CAAC,CAAC2B,MAAM,MAAML,CAAC,GAAGtB,CAAC,CAAC2B,MAAM,CAAC;EAC7C,KAAK,IAAI1B,CAAC,GAAG,CAAC,EAAEmB,CAAC,GAAGJ,KAAK,CAACM,CAAC,CAAC,EAAErB,CAAC,GAAGqB,CAAC,EAAErB,CAAC,EAAE,EAAEmB,CAAC,CAACnB,CAAC,CAAC,GAAGD,CAAC,CAACC,CAAC,CAAC;EACrD,OAAOmB,CAAC;AACV;AAEA,SAASS,2BAA2BA,CAAC7B,CAAC,EAAEsB,CAAC,EAAE;EACzC,IAAItB,CAAC,EAAE;IACL,IAAI,QAAQ,IAAI,OAAOA,CAAC,EAAE,OAAO4B,iBAAiB,CAAC5B,CAAC,EAAEsB,CAAC,CAAC;IACxD,IAAIvB,CAAC,GAAG,CAAC,CAAC,CAAC+B,QAAQ,CAAC3B,IAAI,CAACH,CAAC,CAAC,CAAC+B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACxC,OAAO,QAAQ,KAAKhC,CAAC,IAAIC,CAAC,CAACJ,WAAW,KAAKG,CAAC,GAAGC,CAAC,CAACJ,WAAW,CAACoC,IAAI,CAAC,EAAE,KAAK,KAAKjC,CAAC,IAAI,KAAK,KAAKA,CAAC,GAAGiB,KAAK,CAACiB,IAAI,CAACjC,CAAC,CAAC,GAAG,WAAW,KAAKD,CAAC,IAAI,0CAA0C,CAACmC,IAAI,CAACnC,CAAC,CAAC,GAAG6B,iBAAiB,CAAC5B,CAAC,EAAEsB,CAAC,CAAC,GAAG,KAAK,CAAC;EAC7N;AACF;AAEA,SAASa,gBAAgBA,CAAA,EAAG;EAC1B,MAAM,IAAI/B,SAAS,CAAC,2IAA2I,CAAC;AAClK;AAEA,SAASgC,cAAcA,CAACpC,CAAC,EAAEC,CAAC,EAAE;EAC5B,OAAOc,eAAe,CAACf,CAAC,CAAC,IAAIkB,qBAAqB,CAAClB,CAAC,EAAEC,CAAC,CAAC,IAAI4B,2BAA2B,CAAC7B,CAAC,EAAEC,CAAC,CAAC,IAAIkC,gBAAgB,CAAC,CAAC;AACrH;AAEA,IAAIE,OAAO,GAAG;EACZC,KAAK,EAAE,iBAAiB;EACxBC,GAAG,EAAE,eAAe;EACpBC,MAAM,EAAE,kBAAkB;EAC1BC,IAAI,EAAE,SAASA,IAAIA,CAACC,IAAI,EAAE;IACxB,IAAIC,iBAAiB,GAAGD,IAAI,CAACC,iBAAiB;IAC9C,OAAO5D,UAAU,CAAC,uBAAuB,EAAE;MACzC,yBAAyB,EAAE4D;IAC7B,CAAC,CAAC;EACJ,CAAC;EACDC,SAAS,EAAE,sBAAsB;EACjCC,IAAI,EAAE,iBAAiB;EACvBC,KAAK,EAAE,iBAAiB;EACxBC,WAAW,EAAE,gBAAgB;EAC7BC,QAAQ,EAAE,SAASA,QAAQA,CAACC,KAAK,EAAE;IACjC,IAAIC,MAAM,GAAGD,KAAK,CAACC,MAAM;MACvBC,OAAO,GAAGF,KAAK,CAACE,OAAO;MACvBC,QAAQ,GAAGH,KAAK,CAACG,QAAQ;IAC3B,OAAOrE,UAAU,CAAC,YAAY,EAAE;MAC9B,+BAA+B,EAAEmE,MAAM;MACvC,SAAS,EAAEC,OAAO;MAClB,YAAY,EAAEC;IAChB,CAAC,CAAC;EACJ,CAAC;EACDC,IAAI,EAAE,qBAAqB;EAC3BC,OAAO,EAAE,oBAAoB;EAC7BC,OAAO,EAAE,gBAAgB;EACzBC,MAAM,EAAE,SAASA,MAAMA,CAACC,KAAK,EAAE;IAC7B,IAAIL,QAAQ,GAAGK,KAAK,CAACL,QAAQ;IAC7B,OAAOrE,UAAU,CAAC,iBAAiB,EAAE;MACnC,YAAY,EAAEqE;IAChB,CAAC,CAAC;EACJ;AACF,CAAC;AACD,IAAIM,MAAM,GAAG,mgDAAmgD;AAChhD,IAAIC,WAAW,GAAGrF,aAAa,CAACsF,MAAM,CAAC;EACrCC,YAAY,EAAE;IACZC,MAAM,EAAE,SAAS;IACjBC,EAAE,EAAE,IAAI;IACRC,KAAK,EAAE,IAAI;IACXC,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,IAAI;IACf5B,KAAK,EAAE,IAAI;IACXjE,SAAS,EAAE,IAAI;IACf8F,cAAc,EAAE,IAAI;IACpBC,OAAO,EAAE,IAAI;IACbC,MAAM,EAAE,IAAI;IACZtB,WAAW,EAAE,IAAI;IACjBuB,QAAQ,EAAE,IAAI;IACd/B,GAAG,EAAE,IAAI;IACTgC,QAAQ,EAAEC;EACZ,CAAC;EACDC,GAAG,EAAE;IACHpC,OAAO,EAAEA,OAAO;IAChBqB,MAAM,EAAEA;EACV;AACF,CAAC,CAAC;AAEF,SAASgB,QAAQA,CAAA,EAAG;EAClB,OAAOA,QAAQ,GAAGjE,MAAM,CAACkE,MAAM,GAAGlE,MAAM,CAACkE,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUxD,CAAC,EAAE;IACpE,KAAK,IAAInB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4E,SAAS,CAAClD,MAAM,EAAE1B,CAAC,EAAE,EAAE;MACzC,IAAIF,CAAC,GAAG8E,SAAS,CAAC5E,CAAC,CAAC;MACpB,KAAK,IAAID,CAAC,IAAID,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE+E,cAAc,CAAC3E,IAAI,CAACJ,CAAC,EAAEC,CAAC,CAAC,KAAKoB,CAAC,CAACpB,CAAC,CAAC,GAAGD,CAAC,CAACC,CAAC,CAAC,CAAC;IAClE;IACA,OAAOoB,CAAC;EACV,CAAC,EAAEsD,QAAQ,CAACK,KAAK,CAAC,IAAI,EAAEF,SAAS,CAAC;AACpC;AAEA,SAASG,SAASA,CAAC/E,CAAC,EAAED,CAAC,EAAE;EAAE,IAAID,CAAC,GAAGU,MAAM,CAACwE,IAAI,CAAChF,CAAC,CAAC;EAAE,IAAIQ,MAAM,CAACyE,qBAAqB,EAAE;IAAE,IAAIzF,CAAC,GAAGgB,MAAM,CAACyE,qBAAqB,CAACjF,CAAC,CAAC;IAAED,CAAC,KAAKP,CAAC,GAAGA,CAAC,CAAC0F,MAAM,CAAC,UAAUnF,CAAC,EAAE;MAAE,OAAOS,MAAM,CAAC2E,wBAAwB,CAACnF,CAAC,EAAED,CAAC,CAAC,CAACY,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEb,CAAC,CAAC2B,IAAI,CAACqD,KAAK,CAAChF,CAAC,EAAEN,CAAC,CAAC;EAAE;EAAE,OAAOM,CAAC;AAAE;AAChQ,SAASsF,eAAeA,CAACpF,CAAC,EAAE;EAAE,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6E,SAAS,CAAClD,MAAM,EAAE3B,CAAC,EAAE,EAAE;IAAE,IAAID,CAAC,GAAG,IAAI,IAAI8E,SAAS,CAAC7E,CAAC,CAAC,GAAG6E,SAAS,CAAC7E,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGgF,SAAS,CAACvE,MAAM,CAACV,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACuF,OAAO,CAAC,UAAUtF,CAAC,EAAE;MAAEQ,eAAe,CAACP,CAAC,EAAED,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGS,MAAM,CAAC8E,yBAAyB,GAAG9E,MAAM,CAAC+E,gBAAgB,CAACvF,CAAC,EAAEQ,MAAM,CAAC8E,yBAAyB,CAACxF,CAAC,CAAC,CAAC,GAAGiF,SAAS,CAACvE,MAAM,CAACV,CAAC,CAAC,CAAC,CAACuF,OAAO,CAAC,UAAUtF,CAAC,EAAE;MAAES,MAAM,CAACC,cAAc,CAACT,CAAC,EAAED,CAAC,EAAES,MAAM,CAAC2E,wBAAwB,CAACrF,CAAC,EAAEC,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOC,CAAC;AAAE;AAC5b,IAAIwF,UAAU,GAAG,aAAavH,KAAK,CAACwH,IAAI,CAAC,aAAaxH,KAAK,CAACyH,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EAC3F,IAAIC,UAAU,GAAGtH,aAAa,CAAC,CAAC;EAChC,IAAIuH,GAAG,GAAGH,KAAK,CAACG,GAAG;IACjBC,EAAE,GAAGJ,KAAK,CAACI,EAAE;EACf,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAACC,aAAa,EAAEC,GAAG,EAAEC,KAAK,EAAE;IAClE,OAAOL,GAAG,CAACI,GAAG,EAAE;MACdP,KAAK,EAAEA,KAAK;MACZS,QAAQ,EAAET,KAAK,CAACS,QAAQ;MACxBC,OAAO,EAAE;QACPC,IAAI,EAAEL,aAAa;QACnBE,KAAK,EAAEA,KAAK;QACZlD,MAAM,EAAEsD,YAAY,CAACN,aAAa,CAAC;QACnC/C,OAAO,EAAEsD,aAAa,CAACP,aAAa,CAAC;QACrC9C,QAAQ,EAAEsD,cAAc,CAACR,aAAa,CAAC;QACvCS,KAAK,EAAEf,KAAK,CAACe;MACf;IACF,CAAC,CAAC;EACJ,CAAC;EACD,IAAIC,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,KAAK,EAAEN,IAAI,EAAE;IAC5D,IAAIG,cAAc,CAACH,IAAI,CAAC,IAAIX,KAAK,CAACkB,YAAY,EAAE;MAC9CD,KAAK,CAACE,cAAc,CAAC,CAAC;MACtB;IACF;IACAnB,KAAK,CAACgB,gBAAgB,IAAIhB,KAAK,CAACgB,gBAAgB,CAAC;MAC/CI,aAAa,EAAEH,KAAK;MACpBX,aAAa,EAAEK;IACjB,CAAC,CAAC;EACJ,CAAC;EACD,IAAIU,WAAW,GAAG,SAASA,WAAWA,CAACJ,KAAK,EAAEX,aAAa,EAAE;IAC3D,IAAIK,IAAI,GAAGL,aAAa,CAACK,IAAI;IAC7B,IAAIG,cAAc,CAACR,aAAa,CAAC,EAAE;MACjCW,KAAK,CAACE,cAAc,CAAC,CAAC;MACtB;IACF;IACA,IAAIR,IAAI,CAACW,OAAO,EAAE;MAChBX,IAAI,CAACW,OAAO,CAAC;QACXF,aAAa,EAAEH,KAAK;QACpBN,IAAI,EAAEA;MACR,CAAC,CAAC;IACJ;IACAY,WAAW,CAAC;MACVH,aAAa,EAAEH,KAAK;MACpBX,aAAa,EAAEA,aAAa;MAC5BkB,OAAO,EAAE;IACX,CAAC,CAAC;IACF,IAAI,CAACb,IAAI,CAACc,GAAG,EAAE;MACbR,KAAK,CAACE,cAAc,CAAC,CAAC;MACtBF,KAAK,CAACS,eAAe,CAAC,CAAC;IACzB;EACF,CAAC;EACD,IAAIH,WAAW,GAAG,SAASA,WAAWA,CAACN,KAAK,EAAE;IAC5CjB,KAAK,CAACuB,WAAW,IAAIvB,KAAK,CAACuB,WAAW,CAACN,KAAK,CAAC;EAC/C,CAAC;EACD,IAAIU,SAAS,GAAG,SAASA,SAASA,CAACrB,aAAa,EAAE;IAChD,IAAIsB,mBAAmB;IACvB,OAAO,CAACA,mBAAmB,GAAGtB,aAAa,CAACK,IAAI,MAAM,IAAI,IAAIiB,mBAAmB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,mBAAmB,CAACzD,EAAE;EAChI,CAAC;EACD,IAAI0D,aAAa,GAAG,SAASA,aAAaA,CAACvB,aAAa,EAAE;IACxD,OAAO,EAAE,CAACwB,MAAM,CAAC9B,KAAK,CAAC7B,EAAE,EAAE,GAAG,CAAC,CAAC2D,MAAM,CAACxB,aAAa,CAACC,GAAG,CAAC;EAC3D,CAAC;EACD,IAAIwB,WAAW,GAAG,SAASA,WAAWA,CAACzB,aAAa,EAAElE,IAAI,EAAE4F,MAAM,EAAE;IAClE,OAAO1B,aAAa,IAAIA,aAAa,CAACK,IAAI,GAAGvH,WAAW,CAAC6I,YAAY,CAAC3B,aAAa,CAACK,IAAI,CAACvE,IAAI,CAAC,EAAE4F,MAAM,CAAC,GAAGpD,SAAS;EACrH,CAAC;EACD,IAAIgC,YAAY,GAAG,SAASA,YAAYA,CAACN,aAAa,EAAE;IACtD,OAAON,KAAK,CAACkC,cAAc,CAACC,IAAI,CAAC,UAAUC,IAAI,EAAE;MAC/C,OAAOA,IAAI,CAAC7B,GAAG,KAAKD,aAAa,CAACC,GAAG;IACvC,CAAC,CAAC;EACJ,CAAC;EACD,IAAI8B,aAAa,GAAG,SAASA,aAAaA,CAAC/B,aAAa,EAAE;IACxD,OAAOyB,WAAW,CAACzB,aAAa,EAAE,SAAS,CAAC,KAAK,KAAK;EACxD,CAAC;EACD,IAAIQ,cAAc,GAAG,SAASA,cAAcA,CAACR,aAAa,EAAE;IAC1D,OAAOyB,WAAW,CAACzB,aAAa,EAAE,UAAU,CAAC;EAC/C,CAAC;EACD,IAAIO,aAAa,GAAG,SAASA,aAAaA,CAACP,aAAa,EAAE;IACxD,OAAON,KAAK,CAACsC,aAAa,KAAKT,aAAa,CAACvB,aAAa,CAAC;EAC7D,CAAC;EACD,IAAIiC,WAAW,GAAG,SAASA,WAAWA,CAACjC,aAAa,EAAE;IACpD,OAAOlH,WAAW,CAACoJ,UAAU,CAAClC,aAAa,CAACmC,KAAK,CAAC;EACpD,CAAC;EACD,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAAA,EAAG;IAC7C,OAAO1C,KAAK,CAAC5B,KAAK,CAACmB,MAAM,CAAC,UAAUe,aAAa,EAAE;MACjD,OAAO+B,aAAa,CAAC/B,aAAa,CAAC,IAAI,CAACyB,WAAW,CAACzB,aAAa,EAAE,WAAW,CAAC;IACjF,CAAC,CAAC,CAACvE,MAAM;EACX,CAAC;EACD,IAAI4G,eAAe,GAAG,SAASA,eAAeA,CAACnC,KAAK,EAAE;IACpD,OAAOA,KAAK,GAAGR,KAAK,CAAC5B,KAAK,CAACjC,KAAK,CAAC,CAAC,EAAEqE,KAAK,CAAC,CAACjB,MAAM,CAAC,UAAUe,aAAa,EAAE;MACzE,OAAO+B,aAAa,CAAC/B,aAAa,CAAC,IAAIyB,WAAW,CAACzB,aAAa,EAAE,WAAW,CAAC;IAChF,CAAC,CAAC,CAACvE,MAAM,GAAG,CAAC;EACf,CAAC;EACD,IAAI6G,eAAe,GAAG,SAASA,eAAeA,CAACtC,aAAa,EAAEE,KAAK,EAAE;IACnE,IAAID,GAAG,GAAGP,KAAK,CAAC7B,EAAE,GAAG,aAAa,GAAGqC,KAAK,GAAG,GAAG,GAAGF,aAAa,CAACC,GAAG;IACpE,IAAIsC,cAAc,GAAG3C,UAAU,CAAC;MAC9B,SAAS,EAAEK,GAAG;MACdjC,SAAS,EAAE8B,EAAE,CAAC,WAAW,CAAC;MAC1B0C,IAAI,EAAE;IACR,CAAC,EAAE3C,GAAG,CAAC,WAAW,EAAE;MAClBM,QAAQ,EAAET,KAAK,CAACS;IAClB,CAAC,CAAC,CAAC;IACH,OAAO,aAAanI,KAAK,CAACyK,aAAa,CAAC,IAAI,EAAEjE,QAAQ,CAAC,CAAC,CAAC,EAAE+D,cAAc,EAAE;MACzEtC,GAAG,EAAEA;IACP,CAAC,CAAC,CAAC;EACL,CAAC;EACD,IAAIyC,aAAa,GAAG,SAASA,aAAaA,CAAC1C,aAAa,EAAE;IACxD,IAAImC,KAAK,GAAGnC,aAAa,IAAIA,aAAa,CAACmC,KAAK;IAChD,IAAIA,KAAK,EAAE;MACT,OAAO,aAAanK,KAAK,CAACyK,aAAa,CAAClD,UAAU,EAAE;QAClD1B,EAAE,EAAE6B,KAAK,CAAC7B,EAAE;QACZsC,QAAQ,EAAET,KAAK,CAACS,QAAQ;QACxBwC,SAAS,EAAEjD,KAAK,CAACiD,SAAS;QAC1BlC,KAAK,EAAEf,KAAK,CAACe,KAAK,GAAG,CAAC;QACtB3C,KAAK,EAAEqE,KAAK;QACZP,cAAc,EAAElC,KAAK,CAACkC,cAAc;QACpCI,aAAa,EAAEtC,KAAK,CAACsC,aAAa;QAClCf,WAAW,EAAEA,WAAW;QACxBP,gBAAgB,EAAEhB,KAAK,CAACgB,gBAAgB;QACxC7D,WAAW,EAAE6C,KAAK,CAAC7C,WAAW;QAC9BgD,GAAG,EAAEA,GAAG;QACR9B,KAAK,EAAE;UACL6E,OAAO,EAAEtC,YAAY,CAACN,aAAa,CAAC,GAAG,OAAO,GAAG;QACnD,CAAC;QACDF,EAAE,EAAEA;MACN,CAAC,CAAC;IACJ;IACA,OAAO,IAAI;EACb,CAAC;EACD,IAAI+C,cAAc,GAAG,SAASA,cAAcA,CAAC7C,aAAa,EAAEE,KAAK,EAAE;IACjE,IAAIG,IAAI,GAAGL,aAAa,CAACK,IAAI;IAC7B,IAAI,CAAC0B,aAAa,CAAC/B,aAAa,CAAC,EAAE;MACjC,OAAO,IAAI;IACb;IACA,IAAInC,EAAE,GAAGwD,SAAS,CAACrB,aAAa,CAAC;IACjC,IAAI8C,MAAM,GAAGvB,aAAa,CAACvB,aAAa,CAAC;IACzC,IAAIhD,MAAM,GAAGsD,YAAY,CAACN,aAAa,CAAC;IACxC,IAAI/C,OAAO,GAAGsD,aAAa,CAACP,aAAa,CAAC;IAC1C,IAAI9C,QAAQ,GAAGsD,cAAc,CAACR,aAAa,CAAC,IAAI,KAAK;IACrD,IAAI+C,KAAK,GAAGd,WAAW,CAACjC,aAAa,CAAC;IACtC,IAAIgD,aAAa,GAAGnK,UAAU,CAAC,iBAAiB,EAAE;MAChD,YAAY,EAAEqE;IAChB,CAAC,CAAC;IACF,IAAI+F,aAAa,GAAGpK,UAAU,CAAC,iBAAiB,EAAE4I,WAAW,CAACzB,aAAa,EAAE,MAAM,CAAC,CAAC;IACrF,IAAIkD,SAAS,GAAGtD,UAAU,CAAC;MACzB5B,SAAS,EAAE8B,EAAE,CAAC,MAAM;IACtB,CAAC,EAAEC,YAAY,CAACC,aAAa,EAAE,MAAM,EAAEE,KAAK,CAAC,CAAC;IAC9C,IAAIvD,IAAI,GAAG5D,SAAS,CAACoK,UAAU,CAAC9C,IAAI,CAAC1D,IAAI,EAAEwC,eAAe,CAAC,CAAC,CAAC,EAAE+D,SAAS,CAAC,EAAE;MACzExD,KAAK,EAAEA,KAAK,CAACiD;IACf,CAAC,CAAC;IACF,IAAIS,UAAU,GAAGxD,UAAU,CAAC;MAC1B5B,SAAS,EAAE8B,EAAE,CAAC,OAAO;IACvB,CAAC,EAAEC,YAAY,CAACC,aAAa,EAAE,OAAO,EAAEE,KAAK,CAAC,CAAC;IAC/C,IAAItD,KAAK,GAAGyD,IAAI,CAACzD,KAAK,IAAI,aAAa5E,KAAK,CAACyK,aAAa,CAAC,MAAM,EAAEW,UAAU,EAAE/C,IAAI,CAACzD,KAAK,CAAC;IAC1F,IAAIuF,KAAK,GAAGV,WAAW,CAACzB,aAAa,EAAE,OAAO,CAAC;IAC/C,IAAIqD,oBAAoB,GAAG,gBAAgB;IAC3C,IAAIC,gBAAgB,GAAG1D,UAAU,CAAC;MAChC5B,SAAS,EAAE8B,EAAE,CAAC,aAAa;IAC7B,CAAC,EAAEC,YAAY,CAACC,aAAa,EAAE,aAAa,EAAEE,KAAK,CAAC,CAAC;IACrD,IAAIrD,WAAW,GAAGsF,KAAK,IAAIpJ,SAAS,CAACoK,UAAU,CAAC,CAACzD,KAAK,CAACnD,IAAI,GAAGmD,KAAK,CAAC7C,WAAW,IAAI,aAAa7E,KAAK,CAACyK,aAAa,CAACrJ,cAAc,EAAEkK,gBAAgB,CAAC,GAAG5D,KAAK,CAAC7C,WAAW,IAAI,aAAa7E,KAAK,CAACyK,aAAa,CAACtJ,aAAa,EAAEmK,gBAAgB,CAAC,EAAEnE,eAAe,CAAC,CAAC,CAAC,EAAEmE,gBAAgB,CAAC,EAAE;MACrR5D,KAAK,EAAEP,eAAe,CAAC;QACrBwD,SAAS,EAAEjD,KAAK,CAACiD;MACnB,CAAC,EAAEjD,KAAK;IACV,CAAC,CAAC;IACF,IAAIrC,OAAO,GAAGqF,aAAa,CAAC1C,aAAa,CAAC;IAC1C,IAAIuD,WAAW,GAAG3D,UAAU,CAAC;MAC3B4D,IAAI,EAAEnD,IAAI,CAACc,GAAG,IAAI,GAAG;MACrBsC,QAAQ,EAAE,IAAI;MACdzF,SAAS,EAAE8B,EAAE,CAAC,QAAQ,EAAE;QACtB5C,QAAQ,EAAEA;MACZ,CAAC,CAAC;MACFgB,OAAO,EAAE,SAASA,OAAOA,CAACyC,KAAK,EAAE;QAC/B,OAAOA,KAAK,CAACS,eAAe,CAAC,CAAC;MAChC,CAAC;MACDsC,MAAM,EAAEjC,WAAW,CAACzB,aAAa,EAAE,QAAQ,CAAC;MAC5C,eAAe,EAAEmC,KAAK,IAAI;IAC5B,CAAC,EAAEpC,YAAY,CAACC,aAAa,EAAE,QAAQ,EAAEE,KAAK,CAAC,CAAC;IAChD,IAAI9C,OAAO,GAAG,aAAapF,KAAK,CAACyK,aAAa,CAAC,GAAG,EAAEc,WAAW,EAAE5G,IAAI,EAAEC,KAAK,EAAEC,WAAW,EAAE,aAAa7E,KAAK,CAACyK,aAAa,CAACpJ,MAAM,EAAE,IAAI,CAAC,CAAC;IAC1I,IAAIgH,IAAI,CAACsD,QAAQ,EAAE;MACjB,IAAIC,qBAAqB,GAAG;QAC1B5F,SAAS,EAAEgF,aAAa;QACxBa,cAAc,EAAE,iBAAiB;QACjCZ,aAAa,EAAEA,aAAa;QAC5BI,oBAAoB,EAAEA,oBAAoB;QAC1CS,OAAO,EAAE1G,OAAO;QAChBsC,KAAK,EAAEA;MACT,CAAC;MACDtC,OAAO,GAAGtE,WAAW,CAACiL,aAAa,CAAC1D,IAAI,CAACsD,QAAQ,EAAEtD,IAAI,EAAEuD,qBAAqB,CAAC;IACjF;IACA,IAAII,YAAY,GAAGpE,UAAU,CAAC;MAC5BqE,OAAO,EAAE,SAASA,OAAOA,CAACtD,KAAK,EAAE;QAC/B,OAAOI,WAAW,CAACJ,KAAK,EAAEX,aAAa,CAAC;MAC1C,CAAC;MACDkE,YAAY,EAAE,SAASA,YAAYA,CAACvD,KAAK,EAAE;QACzC,OAAOD,gBAAgB,CAACC,KAAK,EAAEX,aAAa,CAAC;MAC/C,CAAC;MACDhC,SAAS,EAAE8B,EAAE,CAAC,SAAS;IACzB,CAAC,EAAEC,YAAY,CAACC,aAAa,EAAE,SAAS,EAAEE,KAAK,CAAC,CAAC;IACjD,IAAIiE,aAAa,GAAG1C,WAAW,CAACzB,aAAa,EAAE,WAAW,CAAC;IAC3D,IAAIoE,aAAa,GAAGxE,UAAU,CAACtF,eAAe,CAAC;MAC7CuD,EAAE,EAAEA,EAAE;MACN,SAAS,EAAEiF,MAAM;MACjBN,IAAI,EAAE,UAAU;MAChB,YAAY,EAAEnC,IAAI,CAACzD,KAAK;MACxB,eAAe,EAAEM,QAAQ;MACzB,eAAe,EAAE6F,KAAK,GAAG/F,MAAM,GAAGsB,SAAS;MAC3C,eAAe,EAAEyE,KAAK,IAAI,CAAC1C,IAAI,CAACc,GAAG,GAAG,MAAM,GAAG7C,SAAS;MACxD,cAAc,EAAE8D,cAAc,CAAC,CAAC;MAChC,eAAe,EAAEC,eAAe,CAACnC,KAAK,CAAC;MACvC,kBAAkB,EAAElD,MAAM;MAC1B,gBAAgB,EAAEC,OAAO;MACzB,iBAAiB,EAAEC,QAAQ;MAC3Bc,SAAS,EAAEnF,UAAU,CAACsL,aAAa,EAAErE,EAAE,CAAC,UAAU,EAAE;QAClD9C,MAAM,EAAEA,MAAM;QACdC,OAAO,EAAEA,OAAO;QAChBC,QAAQ,EAAEA;MACZ,CAAC,CAAC;IACJ,CAAC,EAAE,iBAAiB,EAAEA,QAAQ,IAAI,KAAK,CAAC,EAAE6C,YAAY,CAACC,aAAa,EAAE,UAAU,EAAEE,KAAK,CAAC,CAAC;IACzF,OAAO,aAAalI,KAAK,CAACyK,aAAa,CAAC,IAAI,EAAEjE,QAAQ,CAAC,CAAC,CAAC,EAAE4F,aAAa,EAAE;MACxEnE,GAAG,EAAE,EAAE,CAACuB,MAAM,CAACsB,MAAM;IACvB,CAAC,CAAC,EAAE,aAAa9K,KAAK,CAACyK,aAAa,CAAC,KAAK,EAAEuB,YAAY,EAAE5G,OAAO,CAAC,EAAEC,OAAO,CAAC;EAC9E,CAAC;EACD,IAAIgH,UAAU,GAAG,SAASA,UAAUA,CAACrE,aAAa,EAAEE,KAAK,EAAE;IACzD,IAAIF,aAAa,CAACsE,OAAO,KAAK,KAAK,EAAE;MACnC,OAAO,IAAI;IACb;IACA,OAAO7C,WAAW,CAACzB,aAAa,EAAE,WAAW,CAAC,GAAGsC,eAAe,CAACtC,aAAa,EAAEE,KAAK,CAAC,GAAG2C,cAAc,CAAC7C,aAAa,EAAEE,KAAK,CAAC;EAC/H,CAAC;EACD,IAAIqE,UAAU,GAAG,SAASA,UAAUA,CAAA,EAAG;IACrC,OAAO7E,KAAK,CAAC5B,KAAK,GAAG4B,KAAK,CAAC5B,KAAK,CAAC0G,GAAG,CAACH,UAAU,CAAC,GAAG,IAAI;EACzD,CAAC;EACD,IAAI7B,IAAI,GAAG9C,KAAK,CAACnD,IAAI,GAAG,SAAS,GAAG,MAAM;EAC1C,IAAIkI,KAAK,GAAG/E,KAAK,CAACnD,IAAI,GAAG,MAAM,GAAG,SAAS;EAC3C,IAAIkH,QAAQ,GAAG/D,KAAK,CAACnD,IAAI,GAAG,GAAG,GAAG,IAAI;EACtC,IAAIc,OAAO,GAAGkH,UAAU,CAAC,CAAC;EAC1B,IAAI5B,SAAS,GAAG/C,UAAU,CAAC;IACzBD,GAAG,EAAEA,GAAG;IACR3B,SAAS,EAAE8B,EAAE,CAAC2E,KAAK,CAAC;IACpBhE,KAAK,EAAEf,KAAK,CAACe,KAAK;IAClBvC,OAAO,EAAEwB,KAAK,CAACxB,OAAO;IACtBC,MAAM,EAAEuB,KAAK,CAACvB,MAAM;IACpBuG,SAAS,EAAEhF,KAAK,CAACgF,SAAS;IAC1B,SAAS,EAAEhF,KAAK,CAAC7B,EAAE;IACnB4F,QAAQ,EAAEA,QAAQ;IAClB,uBAAuB,EAAE/D,KAAK,CAACiF,oBAAoB;IACnD5G,KAAK,EAAE2B,KAAK,CAAC3B,KAAK;IAClByE,IAAI,EAAEA;EACR,CAAC,EAAE3C,GAAG,CAAC4E,KAAK,CAAC,CAAC;EACd,OAAO,aAAazM,KAAK,CAACyK,aAAa,CAAC,IAAI,EAAEE,SAAS,EAAEtF,OAAO,CAAC;AACnE,CAAC,CAAC,CAAC;AACHkC,UAAU,CAACqF,WAAW,GAAG,YAAY;AAErC,SAASC,OAAOA,CAAC9K,CAAC,EAAED,CAAC,EAAE;EAAE,IAAID,CAAC,GAAGU,MAAM,CAACwE,IAAI,CAAChF,CAAC,CAAC;EAAE,IAAIQ,MAAM,CAACyE,qBAAqB,EAAE;IAAE,IAAIzF,CAAC,GAAGgB,MAAM,CAACyE,qBAAqB,CAACjF,CAAC,CAAC;IAAED,CAAC,KAAKP,CAAC,GAAGA,CAAC,CAAC0F,MAAM,CAAC,UAAUnF,CAAC,EAAE;MAAE,OAAOS,MAAM,CAAC2E,wBAAwB,CAACnF,CAAC,EAAED,CAAC,CAAC,CAACY,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEb,CAAC,CAAC2B,IAAI,CAACqD,KAAK,CAAChF,CAAC,EAAEN,CAAC,CAAC;EAAE;EAAE,OAAOM,CAAC;AAAE;AAC9P,SAASiL,aAAaA,CAAC/K,CAAC,EAAE;EAAE,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6E,SAAS,CAAClD,MAAM,EAAE3B,CAAC,EAAE,EAAE;IAAE,IAAID,CAAC,GAAG,IAAI,IAAI8E,SAAS,CAAC7E,CAAC,CAAC,GAAG6E,SAAS,CAAC7E,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAG+K,OAAO,CAACtK,MAAM,CAACV,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACuF,OAAO,CAAC,UAAUtF,CAAC,EAAE;MAAEQ,eAAe,CAACP,CAAC,EAAED,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGS,MAAM,CAAC8E,yBAAyB,GAAG9E,MAAM,CAAC+E,gBAAgB,CAACvF,CAAC,EAAEQ,MAAM,CAAC8E,yBAAyB,CAACxF,CAAC,CAAC,CAAC,GAAGgL,OAAO,CAACtK,MAAM,CAACV,CAAC,CAAC,CAAC,CAACuF,OAAO,CAAC,UAAUtF,CAAC,EAAE;MAAES,MAAM,CAACC,cAAc,CAACT,CAAC,EAAED,CAAC,EAAES,MAAM,CAAC2E,wBAAwB,CAACrF,CAAC,EAAEC,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOC,CAAC;AAAE;AACtb,IAAIgL,OAAO,GAAG,aAAa/M,KAAK,CAACwH,IAAI,CAAC,aAAaxH,KAAK,CAACyH,UAAU,CAAC,UAAUuF,OAAO,EAAErF,GAAG,EAAE;EAC1F,IAAIC,UAAU,GAAGtH,aAAa,CAAC,CAAC;EAChC,IAAI8H,OAAO,GAAGpI,KAAK,CAACiN,UAAU,CAAC/M,iBAAiB,CAAC;EACjD,IAAIwH,KAAK,GAAGjC,WAAW,CAACyH,QAAQ,CAACF,OAAO,EAAE5E,OAAO,CAAC;EAClD,IAAI+E,eAAe,GAAGnN,KAAK,CAACoN,QAAQ,CAAC1F,KAAK,CAAC7B,EAAE,CAAC;IAC5CwH,gBAAgB,GAAGnJ,cAAc,CAACiJ,eAAe,EAAE,CAAC,CAAC;IACrDG,OAAO,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC7BE,UAAU,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAClC,IAAIG,gBAAgB,GAAGxN,KAAK,CAACoN,QAAQ,CAAC,KAAK,CAAC;IAC1CK,gBAAgB,GAAGvJ,cAAc,CAACsJ,gBAAgB,EAAE,CAAC,CAAC;IACtD/I,iBAAiB,GAAGgJ,gBAAgB,CAAC,CAAC,CAAC;IACvCC,oBAAoB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;EAC5C,IAAIE,gBAAgB,GAAG3N,KAAK,CAACoN,QAAQ,CAAC,KAAK,CAAC;IAC1CQ,gBAAgB,GAAG1J,cAAc,CAACyJ,gBAAgB,EAAE,CAAC,CAAC;IACtD1I,OAAO,GAAG2I,gBAAgB,CAAC,CAAC,CAAC;IAC7BC,UAAU,GAAGD,gBAAgB,CAAC,CAAC,CAAC;EAClC,IAAIE,gBAAgB,GAAG9N,KAAK,CAACoN,QAAQ,CAAC;MAClClF,KAAK,EAAE,CAAC,CAAC;MACTO,KAAK,EAAE,CAAC;MACRsF,SAAS,EAAE;IACb,CAAC,CAAC;IACFC,gBAAgB,GAAG9J,cAAc,CAAC4J,gBAAgB,EAAE,CAAC,CAAC;IACtDG,eAAe,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACrCE,kBAAkB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAC1C,IAAIG,gBAAgB,GAAGnO,KAAK,CAACoN,QAAQ,CAAC,IAAI,CAAC;IACzCgB,iBAAiB,GAAGlK,cAAc,CAACiK,gBAAgB,EAAE,CAAC,CAAC;IACvDnE,aAAa,GAAGoE,iBAAiB,CAAC,CAAC,CAAC;IACpCC,gBAAgB,GAAGD,iBAAiB,CAAC,CAAC,CAAC;EACzC,IAAIE,iBAAiB,GAAGtO,KAAK,CAACoN,QAAQ,CAAC,EAAE,CAAC;IACxCmB,iBAAiB,GAAGrK,cAAc,CAACoK,iBAAiB,EAAE,CAAC,CAAC;IACxD1E,cAAc,GAAG2E,iBAAiB,CAAC,CAAC,CAAC;IACrCC,iBAAiB,GAAGD,iBAAiB,CAAC,CAAC,CAAC;EAC1C,IAAIE,iBAAiB,GAAGzO,KAAK,CAACoN,QAAQ,CAAC,EAAE,CAAC;IACxCsB,iBAAiB,GAAGxK,cAAc,CAACuK,iBAAiB,EAAE,CAAC,CAAC;IACxDE,YAAY,GAAGD,iBAAiB,CAAC,CAAC,CAAC;IACnCE,eAAe,GAAGF,iBAAiB,CAAC,CAAC,CAAC;EACxC,IAAIG,iBAAiB,GAAG7O,KAAK,CAACoN,QAAQ,CAAC,EAAE,CAAC;IACxC0B,iBAAiB,GAAG5K,cAAc,CAAC2K,iBAAiB,EAAE,CAAC,CAAC;IACxDE,cAAc,GAAGD,iBAAiB,CAAC,CAAC,CAAC;IACrCE,iBAAiB,GAAGF,iBAAiB,CAAC,CAAC,CAAC;EAC1C,IAAIG,iBAAiB,GAAGjP,KAAK,CAACoN,QAAQ,CAAC,KAAK,CAAC;IAC3C8B,iBAAiB,GAAGhL,cAAc,CAAC+K,iBAAiB,EAAE,CAAC,CAAC;IACxDE,YAAY,GAAGD,iBAAiB,CAAC,CAAC,CAAC;IACnCE,eAAe,GAAGF,iBAAiB,CAAC,CAAC,CAAC;EACxC,IAAIG,iBAAiB,GAAGrP,KAAK,CAACoN,QAAQ,CAAC,KAAK,CAAC;IAC3CkC,iBAAiB,GAAGpL,cAAc,CAACmL,iBAAiB,EAAE,CAAC,CAAC;IACxDE,KAAK,GAAGD,iBAAiB,CAAC,CAAC,CAAC;IAC5BE,QAAQ,GAAGF,iBAAiB,CAAC,CAAC,CAAC;EACjC,IAAIG,UAAU,GAAGzP,KAAK,CAAC0P,MAAM,CAAC,IAAI,CAAC;EACnC,IAAIC,WAAW,GAAG3P,KAAK,CAAC0P,MAAM,CAAC,IAAI,CAAC;EACpC,IAAIE,aAAa,GAAG5P,KAAK,CAAC0P,MAAM,CAAC,IAAI,CAAC;EACtC,IAAIG,WAAW,GAAG7P,KAAK,CAAC0P,MAAM,CAAC,EAAE,CAAC;EAClC,IAAII,aAAa,GAAG9P,KAAK,CAAC0P,MAAM,CAAC,IAAI,CAAC;EACtC,IAAIK,cAAc,GAAG/P,KAAK,CAAC0P,MAAM,CAAC,KAAK,CAAC;EACxC,IAAIM,qBAAqB,GAAGvK,WAAW,CAACwK,WAAW,CAAC;MAChDvI,KAAK,EAAEA,KAAK;MACZwI,KAAK,EAAE;QACLrK,EAAE,EAAEyH,OAAO;QACX1E,YAAY,EAAEnE;MAChB;IACF,CAAC,CAAC;IACFoD,GAAG,GAAGmI,qBAAqB,CAACnI,GAAG;IAC/BC,EAAE,GAAGkI,qBAAqB,CAAClI,EAAE;IAC7BqI,UAAU,GAAGH,qBAAqB,CAACG,UAAU;EAC/C9P,cAAc,CAACoF,WAAW,CAACc,GAAG,CAACf,MAAM,EAAE2K,UAAU,EAAE;IACjDrM,IAAI,EAAE;EACR,CAAC,CAAC;EACF,IAAIsM,iBAAiB,GAAG7P,gBAAgB,CAAC;MACrC8P,IAAI,EAAE,OAAO;MACbC,QAAQ,EAAE,SAASA,QAAQA,CAAC3H,KAAK,EAAE;QACjC,IAAI4H,eAAe,GAAGX,aAAa,CAACY,OAAO,IAAI,CAACZ,aAAa,CAACY,OAAO,CAACC,QAAQ,CAAC9H,KAAK,CAAC+C,MAAM,CAAC;QAC5F,IAAI6E,eAAe,EAAE;UACnBG,IAAI,CAAC,CAAC;QACR;MACF,CAAC;MACDC,OAAO,EAAE;QACPC,OAAO,EAAE;MACX;IACF,CAAC,CAAC;IACFC,kBAAkB,GAAG3M,cAAc,CAACkM,iBAAiB,EAAE,CAAC,CAAC;IACzDU,wBAAwB,GAAGD,kBAAkB,CAAC,CAAC,CAAC;IAChDE,0BAA0B,GAAGF,kBAAkB,CAAC,CAAC,CAAC;EACpD,IAAIG,kBAAkB,GAAGxQ,iBAAiB,CAAC;MACvC8P,QAAQ,EAAE,SAASA,QAAQA,CAAC3H,KAAK,EAAE;QACjC,IAAI,CAAC3H,UAAU,CAACiQ,aAAa,CAAC,CAAC,EAAE;UAC/BP,IAAI,CAAC/H,KAAK,CAAC;QACb;MACF;IACF,CAAC,CAAC;IACFuI,mBAAmB,GAAGhN,cAAc,CAAC8M,kBAAkB,EAAE,CAAC,CAAC;IAC3DG,kBAAkB,GAAGD,mBAAmB,CAAC,CAAC,CAAC;IAC3CE,oBAAoB,GAAGF,mBAAmB,CAAC,CAAC,CAAC;EAC/C,IAAIG,MAAM,GAAG,SAASA,MAAMA,CAAC1I,KAAK,EAAE;IAClC,IAAIlE,iBAAiB,EAAE;MACrBiJ,oBAAoB,CAAC,KAAK,CAAC;MAC3BgD,IAAI,CAAC,CAAC;IACR,CAAC,MAAM;MACLhD,oBAAoB,CAAC,IAAI,CAAC;MAC1B4D,UAAU,CAAC,YAAY;QACrBC,IAAI,CAAC,CAAC;MACR,CAAC,EAAE,CAAC,CAAC;IACP;IACA5I,KAAK,CAACE,cAAc,CAAC,CAAC;EACxB,CAAC;EACD,IAAI0I,IAAI,GAAG,SAASA,IAAIA,CAAA,EAAG;IACzBrD,kBAAkB,CAAC;MACjBhG,KAAK,EAAEsJ,yBAAyB,CAAC,CAAC;MAClC/I,KAAK,EAAE,CAAC;MACRsF,SAAS,EAAE;IACb,CAAC,CAAC;IACF/M,UAAU,CAACyQ,KAAK,CAAC9B,WAAW,CAACa,OAAO,CAAC;EACvC,CAAC;EACD,IAAIE,IAAI,GAAG,SAASA,IAAIA,CAACxH,OAAO,EAAE;IAChC,IAAIzE,iBAAiB,EAAE;MACrBiJ,oBAAoB,CAAC,KAAK,CAAC;MAC3B4D,UAAU,CAAC,YAAY;QACrBtQ,UAAU,CAACyQ,KAAK,CAAC7B,aAAa,CAACY,OAAO,CAAC;MACzC,CAAC,EAAE,CAAC,CAAC;IACP;IACAhC,iBAAiB,CAAC,EAAE,CAAC;IACrBN,kBAAkB,CAAC;MACjBhG,KAAK,EAAE,CAAC,CAAC;MACTO,KAAK,EAAE,CAAC;MACRsF,SAAS,EAAE;IACb,CAAC,CAAC;IACF7E,OAAO,IAAIlI,UAAU,CAACyQ,KAAK,CAAC9B,WAAW,CAACa,OAAO,CAAC;IAChDhB,QAAQ,CAAC,KAAK,CAAC;EACjB,CAAC;EACD,IAAIkC,iBAAiB,GAAG,SAASA,iBAAiBA,CAAC/I,KAAK,EAAE;IACxD,CAACA,KAAK,CAACgJ,IAAI,KAAK,OAAO,IAAIhJ,KAAK,CAACgJ,IAAI,KAAK,aAAa,IAAIhJ,KAAK,CAACgJ,IAAI,KAAK,OAAO,KAAKN,MAAM,CAAC1I,KAAK,CAAC;EACrG,CAAC;EACD,IAAIc,WAAW,GAAG,SAASA,WAAWA,CAACpB,IAAI,EAAEvE,IAAI,EAAE;IACjD,OAAOuE,IAAI,GAAGvH,WAAW,CAAC6I,YAAY,CAACtB,IAAI,CAACvE,IAAI,CAAC,CAAC,GAAGwC,SAAS;EAChE,CAAC;EACD,IAAIsL,YAAY,GAAG,SAASA,YAAYA,CAACvJ,IAAI,EAAE;IAC7C,OAAOoB,WAAW,CAACpB,IAAI,EAAE,OAAO,CAAC;EACnC,CAAC;EACD,IAAIG,cAAc,GAAG,SAASA,cAAcA,CAACH,IAAI,EAAE;IACjD,OAAOoB,WAAW,CAACpB,IAAI,EAAE,UAAU,CAAC;EACtC,CAAC;EACD,IAAIwJ,eAAe,GAAG,SAASA,eAAeA,CAACxJ,IAAI,EAAE;IACnD,OAAOoB,WAAW,CAACpB,IAAI,EAAE,WAAW,CAAC;EACvC,CAAC;EACD,IAAIyJ,sBAAsB,GAAG,SAASA,sBAAsBA,CAAC9J,aAAa,EAAE;IAC1E,OAAOA,aAAa,GAAG4J,YAAY,CAAC5J,aAAa,CAACK,IAAI,CAAC,GAAG/B,SAAS;EACrE,CAAC;EACD,IAAIyL,qBAAqB,GAAG,SAASA,qBAAqBA,CAAC/J,aAAa,EAAE;IACxE,OAAOA,aAAa,IAAIlH,WAAW,CAACoJ,UAAU,CAAClC,aAAa,CAACmC,KAAK,CAAC;EACrE,CAAC;EACD,IAAIjE,OAAO,GAAG,SAASA,OAAOA,CAACyC,KAAK,EAAE;IACpCkF,UAAU,CAAC,IAAI,CAAC;IAChBK,kBAAkB,CAACD,eAAe,CAAC/F,KAAK,KAAK,CAAC,CAAC,GAAG+F,eAAe,GAAG;MAClE/F,KAAK,EAAEsJ,yBAAyB,CAAC,CAAC;MAClC/I,KAAK,EAAE,CAAC;MACRsF,SAAS,EAAE;IACb,CAAC,CAAC;IACFrG,KAAK,CAACxB,OAAO,IAAIwB,KAAK,CAACxB,OAAO,CAACyC,KAAK,CAAC;EACvC,CAAC;EACD,IAAIxC,MAAM,GAAG,SAASA,MAAMA,CAACwC,KAAK,EAAE;IAClCkF,UAAU,CAAC,KAAK,CAAC;IACjBK,kBAAkB,CAAC;MACjBhG,KAAK,EAAE,CAAC,CAAC;MACTO,KAAK,EAAE,CAAC;MACRsF,SAAS,EAAE;IACb,CAAC,CAAC;IACF8B,WAAW,CAACW,OAAO,GAAG,EAAE;IACxBhB,QAAQ,CAAC,KAAK,CAAC;IACf9H,KAAK,CAACvB,MAAM,IAAIuB,KAAK,CAACvB,MAAM,CAACwC,KAAK,CAAC;EACrC,CAAC;EACD,IAAI+D,SAAS,GAAG,SAASA,SAASA,CAAC/D,KAAK,EAAE;IACxC,IAAIqJ,OAAO,GAAGrJ,KAAK,CAACqJ,OAAO,IAAIrJ,KAAK,CAACsJ,OAAO;IAC5C,IAAIN,IAAI,GAAGhJ,KAAK,CAACgJ,IAAI;IACrB,QAAQA,IAAI;MACV,KAAK,WAAW;QACdO,cAAc,CAACvJ,KAAK,CAAC;QACrB;MACF,KAAK,SAAS;QACZwJ,YAAY,CAACxJ,KAAK,CAAC;QACnB;MACF,KAAK,WAAW;QACdyJ,cAAc,CAACzJ,KAAK,CAAC;QACrB;MACF,KAAK,YAAY;QACf0J,eAAe,CAAC1J,KAAK,CAAC;QACtB;MACF,KAAK,MAAM;QACT2J,SAAS,CAAC3J,KAAK,CAAC;QAChB;MACF,KAAK,KAAK;QACR4J,QAAQ,CAAC5J,KAAK,CAAC;QACf;MACF,KAAK,OAAO;QACV6J,UAAU,CAAC7J,KAAK,CAAC;QACjB;MACF,KAAK,OAAO;MACZ,KAAK,aAAa;QAChB8J,UAAU,CAAC9J,KAAK,CAAC;QACjB;MACF,KAAK,QAAQ;QACX+J,WAAW,CAAC,CAAC;QACb;MACF,KAAK,KAAK;QACRC,QAAQ,CAAChK,KAAK,CAAC;QACf;MACF,KAAK,UAAU;MACf,KAAK,QAAQ;MACb,KAAK,WAAW;MAChB,KAAK,WAAW;MAChB,KAAK,YAAY;QACf;MACF;QACE,IAAI,CAACqJ,OAAO,IAAIlR,WAAW,CAAC8R,oBAAoB,CAACjK,KAAK,CAACV,GAAG,CAAC,EAAE;UAC3D4K,WAAW,CAAClK,KAAK,EAAEA,KAAK,CAACV,GAAG,CAAC;QAC/B;QACA;IACJ;EACF,CAAC;EACD,IAAI6K,YAAY,GAAG,SAASA,YAAYA,CAACnK,KAAK,EAAE;IAC9C,IAAIX,aAAa,GAAGW,KAAK,CAACX,aAAa;MACrCkB,OAAO,GAAGP,KAAK,CAACO,OAAO;IACzB,IAAIpI,WAAW,CAACiS,OAAO,CAAC/K,aAAa,CAAC,EAAE;MACtC;IACF;IACA,IAAIE,KAAK,GAAGF,aAAa,CAACE,KAAK;MAC7BD,GAAG,GAAGD,aAAa,CAACC,GAAG;MACvBQ,KAAK,GAAGT,aAAa,CAACS,KAAK;MAC3BsF,SAAS,GAAG/F,aAAa,CAAC+F,SAAS;MACnC5D,KAAK,GAAGnC,aAAa,CAACmC,KAAK;IAC7B,IAAI6I,OAAO,GAAGlS,WAAW,CAACoJ,UAAU,CAACC,KAAK,CAAC;IAC3C,IAAI8I,eAAe,GAAGrJ,cAAc,CAAC3C,MAAM,CAAC,UAAUiM,CAAC,EAAE;MACvD,OAAOA,CAAC,CAACnF,SAAS,KAAKA,SAAS,IAAImF,CAAC,CAACnF,SAAS,KAAK9F,GAAG;IACzD,CAAC,CAAC;IACF+K,OAAO,IAAIC,eAAe,CAACzP,IAAI,CAACwE,aAAa,CAAC;IAC9CkG,kBAAkB,CAAC;MACjBhG,KAAK,EAAEA,KAAK;MACZO,KAAK,EAAEA,KAAK;MACZsF,SAAS,EAAEA;IACb,CAAC,CAAC;IACFS,iBAAiB,CAACyE,eAAe,CAAC;IAClCD,OAAO,IAAIxD,QAAQ,CAAC,IAAI,CAAC;IACzBtG,OAAO,IAAIlI,UAAU,CAACyQ,KAAK,CAAC9B,WAAW,CAACa,OAAO,CAAC;EAClD,CAAC;EACD,IAAIzH,WAAW,GAAG,SAASA,WAAWA,CAACJ,KAAK,EAAE;IAC5C,IAAIG,aAAa,GAAGH,KAAK,CAACG,aAAa;MACrCd,aAAa,GAAGW,KAAK,CAACX,aAAa;IACrC,IAAIgL,OAAO,GAAGjB,qBAAqB,CAAC/J,aAAa,CAAC;IAClD,IAAIzD,IAAI,GAAGzD,WAAW,CAACiS,OAAO,CAAC/K,aAAa,CAACmL,MAAM,CAAC;IACpD,IAAIC,QAAQ,GAAGC,UAAU,CAACrL,aAAa,CAAC;IACxC,IAAIoL,QAAQ,EAAE;MACZ,IAAIlL,KAAK,GAAGF,aAAa,CAACE,KAAK;QAC7BD,GAAG,GAAGD,aAAa,CAACC,GAAG;QACvBQ,KAAK,GAAGT,aAAa,CAACS,KAAK;QAC3BsF,SAAS,GAAG/F,aAAa,CAAC+F,SAAS;MACrCS,iBAAiB,CAAC5E,cAAc,CAAC3C,MAAM,CAAC,UAAUiM,CAAC,EAAE;QACnD,OAAOjL,GAAG,KAAKiL,CAAC,CAACjL,GAAG,IAAIA,GAAG,CAACqL,UAAU,CAACJ,CAAC,CAACjL,GAAG,CAAC;MAC/C,CAAC,CAAC,CAAC;MACHiG,kBAAkB,CAAC;QACjBhG,KAAK,EAAEA,KAAK;QACZO,KAAK,EAAEA,KAAK;QACZsF,SAAS,EAAEA;MACb,CAAC,CAAC;MACF,IAAI,CAACiF,OAAO,EAAE;QACZxD,QAAQ,CAAC,CAACjL,IAAI,CAAC;MACjB;MACA+M,UAAU,CAAC,YAAY;QACrBtQ,UAAU,CAACyQ,KAAK,CAAC9B,WAAW,CAACa,OAAO,CAAC;QACrC,IAAIwC,OAAO,EAAE;UACXxD,QAAQ,CAAC,IAAI,CAAC;QAChB;MACF,CAAC,EAAE,CAAC,CAAC;IACP,CAAC,MAAM,IAAIwD,OAAO,EAAE;MAClBhS,UAAU,CAACyQ,KAAK,CAAC9B,WAAW,CAACa,OAAO,CAAC;MACrCsC,YAAY,CAAC;QACXhK,aAAa,EAAEA,aAAa;QAC5Bd,aAAa,EAAEA;MACjB,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAIuL,iBAAiB,GAAGhP,IAAI,GAAGyD,aAAa,GAAG4B,cAAc,CAAC4J,IAAI,CAAC,UAAUN,CAAC,EAAE;QAC9E,OAAOA,CAAC,CAACnF,SAAS,KAAK,EAAE;MAC3B,CAAC,CAAC;MACF,IAAI0F,sBAAsB,GAAGF,iBAAiB,GAAGA,iBAAiB,CAACrL,KAAK,GAAG,CAAC,CAAC;MAC7EwI,IAAI,CAAC5H,aAAa,CAAC;MACnBoF,kBAAkB,CAAC;QACjBhG,KAAK,EAAEuL,sBAAsB;QAC7B1F,SAAS,EAAEwF,iBAAiB,GAAGA,iBAAiB,CAACxF,SAAS,GAAG;MAC/D,CAAC,CAAC;MACFL,oBAAoB,CAAC,KAAK,CAAC;IAC7B;EACF,CAAC;EACD,IAAIhF,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,KAAK,EAAE;IACtD,IAAI,CAAClE,iBAAiB,IAAI8K,KAAK,EAAE;MAC/BuD,YAAY,CAACnK,KAAK,CAAC;IACrB;EACF,CAAC;EACD,IAAIuJ,cAAc,GAAG,SAASA,cAAcA,CAACvJ,KAAK,EAAE;IAClD,IAAIX,aAAa,GAAG2G,YAAY,CAACV,eAAe,CAAC/F,KAAK,CAAC;IACvD,IAAI3D,IAAI,GAAGyD,aAAa,GAAGlH,WAAW,CAACiS,OAAO,CAAC/K,aAAa,CAACmL,MAAM,CAAC,GAAG,IAAI;IAC3E,IAAI5O,IAAI,EAAE;MACR,IAAIyO,OAAO,GAAGjB,qBAAqB,CAAC/J,aAAa,CAAC;MAClD,IAAIgL,OAAO,EAAE;QACXF,YAAY,CAAC;UACXhK,aAAa,EAAEH,KAAK;UACpBX,aAAa,EAAEA;QACjB,CAAC,CAAC;QACFkG,kBAAkB,CAAC;UACjBhG,KAAK,EAAE,CAAC,CAAC;UACT6F,SAAS,EAAE/F,aAAa,CAACC;QAC3B,CAAC,CAAC;QACFqJ,UAAU,CAAC,YAAY;UACrB,OAAOlC,eAAe,CAAC,IAAI,CAAC;QAC9B,CAAC,EAAE,CAAC,CAAC;MACP;IACF,CAAC,MAAM;MACL,IAAIsE,SAAS,GAAGzF,eAAe,CAAC/F,KAAK,KAAK,CAAC,CAAC,GAAGyL,iBAAiB,CAAC1F,eAAe,CAAC/F,KAAK,CAAC,GAAGsJ,yBAAyB,CAAC,CAAC;MACrHoC,sBAAsB,CAACF,SAAS,CAAC;IACnC;IACA/K,KAAK,CAACE,cAAc,CAAC,CAAC;EACxB,CAAC;EACD,IAAIsJ,YAAY,GAAG,SAASA,YAAYA,CAACxJ,KAAK,EAAE;IAC9C,IAAIX,aAAa,GAAG2G,YAAY,CAACV,eAAe,CAAC/F,KAAK,CAAC;IACvD,IAAI3D,IAAI,GAAGzD,WAAW,CAACiS,OAAO,CAAC/K,aAAa,CAACmL,MAAM,CAAC;IACpD,IAAI5O,IAAI,EAAE;MACR,IAAIyO,OAAO,GAAGjB,qBAAqB,CAAC/J,aAAa,CAAC;MAClD,IAAIgL,OAAO,EAAE;QACXF,YAAY,CAAC;UACXhK,aAAa,EAAEH,KAAK;UACpBX,aAAa,EAAEA;QACjB,CAAC,CAAC;QACFkG,kBAAkB,CAAC;UACjBhG,KAAK,EAAE,CAAC,CAAC;UACT6F,SAAS,EAAE/F,aAAa,CAACC;QAC3B,CAAC,CAAC;QACF8H,cAAc,CAACS,OAAO,GAAG,IAAI;QAC7Bc,UAAU,CAAC,YAAY;UACrB,OAAOlC,eAAe,CAAC,IAAI,CAAC;QAC9B,CAAC,EAAE,CAAC,CAAC;MACP;IACF,CAAC,MAAM;MACL,IAAIyE,UAAU,GAAGjK,cAAc,CAAC4J,IAAI,CAAC,UAAUN,CAAC,EAAE;QAChD,OAAOA,CAAC,CAACjL,GAAG,KAAKD,aAAa,CAAC+F,SAAS;MAC1C,CAAC,CAAC;MACF,IAAIE,eAAe,CAAC/F,KAAK,KAAK,CAAC,IAAI2L,UAAU,IAAIA,UAAU,CAAC9F,SAAS,KAAK,EAAE,EAAE;QAC5EG,kBAAkB,CAAC;UACjBhG,KAAK,EAAE,CAAC,CAAC;UACT6F,SAAS,EAAE8F,UAAU,GAAGA,UAAU,CAAC9F,SAAS,GAAG;QACjD,CAAC,CAAC;QACF8B,WAAW,CAACW,OAAO,GAAG,EAAE;QACxB4B,cAAc,CAACzJ,KAAK,CAAC;MACvB,CAAC,MAAM;QACL,IAAI+K,SAAS,GAAGzF,eAAe,CAAC/F,KAAK,KAAK,CAAC,CAAC,GAAG4L,iBAAiB,CAAC7F,eAAe,CAAC/F,KAAK,CAAC,GAAG6L,wBAAwB,CAAC,CAAC;QACpHH,sBAAsB,CAACF,SAAS,CAAC;MACnC;IACF;IACA/K,KAAK,CAACE,cAAc,CAAC,CAAC;EACxB,CAAC;EACD,IAAIuJ,cAAc,GAAG,SAASA,cAAcA,CAACzJ,KAAK,EAAE;IAClD,IAAIX,aAAa,GAAG2G,YAAY,CAACV,eAAe,CAAC/F,KAAK,CAAC;IACvD,IAAI2L,UAAU,GAAG7L,aAAa,GAAG4B,cAAc,CAAC4J,IAAI,CAAC,UAAUN,CAAC,EAAE;MAChE,OAAOA,CAAC,CAACjL,GAAG,KAAKD,aAAa,CAAC+F,SAAS;IAC1C,CAAC,CAAC,GAAG,IAAI;IACT,IAAI8F,UAAU,EAAE;MACdf,YAAY,CAAC;QACXhK,aAAa,EAAEH,KAAK;QACpBX,aAAa,EAAE6L;MACjB,CAAC,CAAC;MACFrF,iBAAiB,CAAC5E,cAAc,CAAC3C,MAAM,CAAC,UAAUiM,CAAC,EAAE;QACnD,OAAOA,CAAC,CAACjL,GAAG,KAAK4L,UAAU,CAAC5L,GAAG;MACjC,CAAC,CAAC,CAAC;IACL,CAAC,MAAM;MACL,IAAIyL,SAAS,GAAGzF,eAAe,CAAC/F,KAAK,KAAK,CAAC,CAAC,GAAG4L,iBAAiB,CAAC7F,eAAe,CAAC/F,KAAK,CAAC,GAAG6L,wBAAwB,CAAC,CAAC;MACpHH,sBAAsB,CAACF,SAAS,CAAC;IACnC;IACA/K,KAAK,CAACE,cAAc,CAAC,CAAC;EACxB,CAAC;EACD,IAAIwJ,eAAe,GAAG,SAASA,eAAeA,CAAC1J,KAAK,EAAE;IACpD,IAAIX,aAAa,GAAG2G,YAAY,CAACV,eAAe,CAAC/F,KAAK,CAAC;IACvD,IAAI2L,UAAU,GAAG7L,aAAa,GAAG4B,cAAc,CAAC4J,IAAI,CAAC,UAAUN,CAAC,EAAE;MAChE,OAAOA,CAAC,CAACjL,GAAG,KAAKD,aAAa,CAAC+F,SAAS;IAC1C,CAAC,CAAC,GAAG,IAAI;IACT,IAAI8F,UAAU,EAAE;MACd,IAAIb,OAAO,GAAGjB,qBAAqB,CAAC/J,aAAa,CAAC;MAClD,IAAIgL,OAAO,EAAE;QACXF,YAAY,CAAC;UACXhK,aAAa,EAAEH,KAAK;UACpBX,aAAa,EAAEA;QACjB,CAAC,CAAC;QACFkG,kBAAkB,CAAC;UACjBhG,KAAK,EAAE,CAAC,CAAC;UACT6F,SAAS,EAAE/F,aAAa,CAACC;QAC3B,CAAC,CAAC;QACFqJ,UAAU,CAAC,YAAY;UACrB,OAAOlC,eAAe,CAAC,IAAI,CAAC;QAC9B,CAAC,EAAE,CAAC,CAAC;MACP;IACF,CAAC,MAAM;MACL,IAAIsE,SAAS,GAAGzF,eAAe,CAAC/F,KAAK,KAAK,CAAC,CAAC,GAAGyL,iBAAiB,CAAC1F,eAAe,CAAC/F,KAAK,CAAC,GAAGsJ,yBAAyB,CAAC,CAAC;MACrHoC,sBAAsB,CAACF,SAAS,CAAC;IACnC;IACA/K,KAAK,CAACE,cAAc,CAAC,CAAC;EACxB,CAAC;EACD,IAAIyJ,SAAS,GAAG,SAASA,SAASA,CAAC3J,KAAK,EAAE;IACxCiL,sBAAsB,CAACI,kBAAkB,CAAC,CAAC,CAAC;IAC5CrL,KAAK,CAACE,cAAc,CAAC,CAAC;EACxB,CAAC;EACD,IAAI0J,QAAQ,GAAG,SAASA,QAAQA,CAAC5J,KAAK,EAAE;IACtCiL,sBAAsB,CAACK,iBAAiB,CAAC,CAAC,CAAC;IAC3CtL,KAAK,CAACE,cAAc,CAAC,CAAC;EACxB,CAAC;EACD,IAAI4J,UAAU,GAAG,SAASA,UAAUA,CAAC9J,KAAK,EAAE;IAC1C,IAAIsF,eAAe,CAAC/F,KAAK,KAAK,CAAC,CAAC,EAAE;MAChC,IAAI4D,OAAO,GAAG9K,UAAU,CAACkT,UAAU,CAACvE,WAAW,CAACa,OAAO,EAAE,eAAe,CAAChH,MAAM,CAAC,EAAE,CAACA,MAAM,CAACQ,aAAa,CAAC,EAAE,KAAK,CAAC,CAAC;MACjH,IAAImK,aAAa,GAAGrI,OAAO,IAAI9K,UAAU,CAACkT,UAAU,CAACpI,OAAO,EAAE,6BAA6B,CAAC;MAC5FqI,aAAa,GAAGA,aAAa,CAACC,KAAK,CAAC,CAAC,GAAGtI,OAAO,IAAIA,OAAO,CAACsI,KAAK,CAAC,CAAC;IACpE;IACAzL,KAAK,CAACE,cAAc,CAAC,CAAC;EACxB,CAAC;EACD,IAAI2J,UAAU,GAAG,SAASA,UAAUA,CAAC7J,KAAK,EAAE;IAC1C8J,UAAU,CAAC9J,KAAK,CAAC;EACnB,CAAC;EACD,IAAI+J,WAAW,GAAG,SAASA,WAAWA,CAAC/J,KAAK,EAAE;IAC5C+H,IAAI,CAAC,IAAI,CAAC;IACVxC,kBAAkB,CAAC;MACjBD,eAAe,EAAEA,eAAe;MAChC/F,KAAK,EAAEsJ,yBAAyB,CAAC;IACnC,CAAC,CAAC;EACJ,CAAC;EACD,IAAImB,QAAQ,GAAG,SAASA,QAAQA,CAAChK,KAAK,EAAE;IACtC,IAAIsF,eAAe,CAAC/F,KAAK,KAAK,CAAC,CAAC,EAAE;MAChC,IAAIF,aAAa,GAAG2G,YAAY,CAACV,eAAe,CAAC/F,KAAK,CAAC;MACvD,IAAI8K,OAAO,GAAGjB,qBAAqB,CAAC/J,aAAa,CAAC;MAClD,CAACgL,OAAO,IAAIF,YAAY,CAAC;QACvBhK,aAAa,EAAEH,KAAK;QACpBX,aAAa,EAAEA;MACjB,CAAC,CAAC;IACJ;IACA0I,IAAI,CAAC,CAAC;EACR,CAAC;EACD,IAAI2D,aAAa,GAAG,SAASA,aAAaA,CAACrM,aAAa,EAAE;IACxD,OAAOsM,WAAW,CAACtM,aAAa,CAAC,IAAI8J,sBAAsB,CAAC9J,aAAa,CAAC,CAACuM,iBAAiB,CAAC,CAAC,CAACjB,UAAU,CAACzD,WAAW,CAACW,OAAO,CAAC+D,iBAAiB,CAAC,CAAC,CAAC;EACpJ,CAAC;EACD,IAAID,WAAW,GAAG,SAASA,WAAWA,CAACtM,aAAa,EAAE;IACpD,OAAO,CAAC,CAACA,aAAa,IAAI,CAACQ,cAAc,CAACR,aAAa,CAACK,IAAI,CAAC,IAAI,CAACwJ,eAAe,CAAC7J,aAAa,CAACK,IAAI,CAAC;EACvG,CAAC;EACD,IAAImM,mBAAmB,GAAG,SAASA,mBAAmBA,CAACxM,aAAa,EAAE;IACpE,OAAOsM,WAAW,CAACtM,aAAa,CAAC,IAAIqL,UAAU,CAACrL,aAAa,CAAC;EAChE,CAAC;EACD,IAAIqL,UAAU,GAAG,SAASA,UAAUA,CAACrL,aAAa,EAAE;IAClD,OAAO4B,cAAc,CAACC,IAAI,CAAC,UAAUqJ,CAAC,EAAE;MACtC,OAAOA,CAAC,CAACjL,GAAG,KAAKD,aAAa,CAACC,GAAG;IACpC,CAAC,CAAC;EACJ,CAAC;EACD,IAAI+L,kBAAkB,GAAG,SAASA,kBAAkBA,CAAA,EAAG;IACrD,OAAOrF,YAAY,CAAC8F,SAAS,CAAC,UAAUzM,aAAa,EAAE;MACrD,OAAOsM,WAAW,CAACtM,aAAa,CAAC;IACnC,CAAC,CAAC;EACJ,CAAC;EACD,IAAIiM,iBAAiB,GAAG,SAASA,iBAAiBA,CAAA,EAAG;IACnD,OAAOnT,WAAW,CAAC4T,aAAa,CAAC/F,YAAY,EAAE,UAAU3G,aAAa,EAAE;MACtE,OAAOsM,WAAW,CAACtM,aAAa,CAAC;IACnC,CAAC,CAAC;EACJ,CAAC;EACD,IAAI2L,iBAAiB,GAAG,SAASA,iBAAiBA,CAACzL,KAAK,EAAE;IACxD,IAAIyM,gBAAgB,GAAGzM,KAAK,GAAGyG,YAAY,CAAClL,MAAM,GAAG,CAAC,GAAGkL,YAAY,CAAC9K,KAAK,CAACqE,KAAK,GAAG,CAAC,CAAC,CAACuM,SAAS,CAAC,UAAUzM,aAAa,EAAE;MACxH,OAAOsM,WAAW,CAACtM,aAAa,CAAC;IACnC,CAAC,CAAC,GAAG,CAAC,CAAC;IACP,OAAO2M,gBAAgB,GAAG,CAAC,CAAC,GAAGA,gBAAgB,GAAGzM,KAAK,GAAG,CAAC,GAAGA,KAAK;EACrE,CAAC;EACD,IAAI4L,iBAAiB,GAAG,SAASA,iBAAiBA,CAAC5L,KAAK,EAAE;IACxD,IAAIyM,gBAAgB,GAAGzM,KAAK,GAAG,CAAC,GAAGpH,WAAW,CAAC4T,aAAa,CAAC/F,YAAY,CAAC9K,KAAK,CAAC,CAAC,EAAEqE,KAAK,CAAC,EAAE,UAAUF,aAAa,EAAE;MAClH,OAAOsM,WAAW,CAACtM,aAAa,CAAC;IACnC,CAAC,CAAC,GAAG,CAAC,CAAC;IACP,OAAO2M,gBAAgB,GAAG,CAAC,CAAC,GAAGA,gBAAgB,GAAGzM,KAAK;EACzD,CAAC;EACD,IAAI0M,qBAAqB,GAAG,SAASA,qBAAqBA,CAAA,EAAG;IAC3D,OAAOjG,YAAY,CAAC8F,SAAS,CAAC,UAAUzM,aAAa,EAAE;MACrD,OAAOwM,mBAAmB,CAACxM,aAAa,CAAC;IAC3C,CAAC,CAAC;EACJ,CAAC;EACD,IAAIwJ,yBAAyB,GAAG,SAASA,yBAAyBA,CAAA,EAAG;IACnE,IAAIqD,aAAa,GAAGD,qBAAqB,CAAC,CAAC;IAC3C,OAAOC,aAAa;EACtB,CAAC;EACD,IAAId,wBAAwB,GAAG,SAASA,wBAAwBA,CAAA,EAAG;IACjE,IAAIc,aAAa,GAAGD,qBAAqB,CAAC,CAAC;IAC3C,OAAOC,aAAa;EACtB,CAAC;EACD,IAAIhC,WAAW,GAAG,SAASA,WAAWA,CAAClK,KAAK,EAAEmM,KAAK,EAAE;IACnDjF,WAAW,CAACW,OAAO,GAAG,CAACX,WAAW,CAACW,OAAO,IAAI,EAAE,IAAIsE,KAAK;IACzD,IAAIpB,SAAS,GAAG,CAAC,CAAC;IAClB,IAAIqB,OAAO,GAAG,KAAK;IACnB,IAAI9G,eAAe,CAAC/F,KAAK,KAAK,CAAC,CAAC,EAAE;MAChCwL,SAAS,GAAG/E,YAAY,CAAC9K,KAAK,CAACoK,eAAe,CAAC/F,KAAK,CAAC,CAACuM,SAAS,CAAC,UAAUzM,aAAa,EAAE;QACvF,OAAOqM,aAAa,CAACrM,aAAa,CAAC;MACrC,CAAC,CAAC;MACF0L,SAAS,GAAGA,SAAS,KAAK,CAAC,CAAC,GAAG/E,YAAY,CAAC9K,KAAK,CAAC,CAAC,EAAEoK,eAAe,CAAC/F,KAAK,CAAC,CAACuM,SAAS,CAAC,UAAUzM,aAAa,EAAE;QAC7G,OAAOqM,aAAa,CAACrM,aAAa,CAAC;MACrC,CAAC,CAAC,GAAG0L,SAAS,GAAGzF,eAAe,CAAC/F,KAAK;IACxC,CAAC,MAAM;MACLwL,SAAS,GAAG/E,YAAY,CAAC8F,SAAS,CAAC,UAAUzM,aAAa,EAAE;QAC1D,OAAOqM,aAAa,CAACrM,aAAa,CAAC;MACrC,CAAC,CAAC;IACJ;IACA,IAAI0L,SAAS,KAAK,CAAC,CAAC,EAAE;MACpBqB,OAAO,GAAG,IAAI;IAChB;IACA,IAAIrB,SAAS,KAAK,CAAC,CAAC,IAAIzF,eAAe,CAAC/F,KAAK,KAAK,CAAC,CAAC,EAAE;MACpDwL,SAAS,GAAGlC,yBAAyB,CAAC,CAAC;IACzC;IACA,IAAIkC,SAAS,KAAK,CAAC,CAAC,EAAE;MACpBE,sBAAsB,CAACF,SAAS,CAAC;IACnC;IACA,IAAI5D,aAAa,CAACU,OAAO,EAAE;MACzBwE,YAAY,CAAClF,aAAa,CAACU,OAAO,CAAC;IACrC;IACAV,aAAa,CAACU,OAAO,GAAGc,UAAU,CAAC,YAAY;MAC7CzB,WAAW,CAACW,OAAO,GAAG,EAAE;MACxBV,aAAa,CAACU,OAAO,GAAG,IAAI;IAC9B,CAAC,EAAE,GAAG,CAAC;IACP,OAAOuE,OAAO;EAChB,CAAC;EACD,IAAInB,sBAAsB,GAAG,SAASA,sBAAsBA,CAAC1L,KAAK,EAAE;IAClE,IAAI+F,eAAe,CAAC/F,KAAK,KAAKA,KAAK,EAAE;MACnCgG,kBAAkB,CAACpB,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEmB,eAAe,CAAC,EAAE,CAAC,CAAC,EAAE;QACvE/F,KAAK,EAAEA;MACT,CAAC,CAAC,CAAC;MACH+M,YAAY,CAAC,CAAC;IAChB;EACF,CAAC;EACD,IAAIA,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAG;IACzC,IAAI/M,KAAK,GAAGvB,SAAS,CAAClD,MAAM,GAAG,CAAC,IAAIkD,SAAS,CAAC,CAAC,CAAC,KAAKL,SAAS,GAAGK,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAClF,IAAId,EAAE,GAAGqC,KAAK,KAAK,CAAC,CAAC,GAAG,EAAE,CAACsB,MAAM,CAAC8D,OAAO,EAAE,GAAG,CAAC,CAAC9D,MAAM,CAACtB,KAAK,CAAC,GAAG8B,aAAa;IAC7E,IAAI8B,OAAO,GAAG9K,UAAU,CAACkT,UAAU,CAACvE,WAAW,CAACa,OAAO,EAAE,eAAe,CAAChH,MAAM,CAAC3D,EAAE,EAAE,KAAK,CAAC,CAAC;IAC3F,IAAIiG,OAAO,EAAE;MACXA,OAAO,CAACoJ,cAAc,IAAIpJ,OAAO,CAACoJ,cAAc,CAAC;QAC/CC,KAAK,EAAE,SAAS;QAChBC,MAAM,EAAE;MACV,CAAC,CAAC;IACJ;EACF,CAAC;EACD,IAAIC,qBAAqB,GAAG,SAASC,oBAAoBA,CAACnL,KAAK,EAAE;IAC/D,IAAI1B,KAAK,GAAG9B,SAAS,CAAClD,MAAM,GAAG,CAAC,IAAIkD,SAAS,CAAC,CAAC,CAAC,KAAKL,SAAS,GAAGK,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IACjF,IAAIwM,MAAM,GAAGxM,SAAS,CAAClD,MAAM,GAAG,CAAC,IAAIkD,SAAS,CAAC,CAAC,CAAC,KAAKL,SAAS,GAAGK,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACnF,IAAIoH,SAAS,GAAGpH,SAAS,CAAClD,MAAM,GAAG,CAAC,IAAIkD,SAAS,CAAC,CAAC,CAAC,KAAKL,SAAS,GAAGK,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;IACtF,IAAI4O,eAAe,GAAG,EAAE;IACxBpL,KAAK,IAAIA,KAAK,CAAC/C,OAAO,CAAC,UAAUiB,IAAI,EAAEH,KAAK,EAAE;MAC5C,IAAID,GAAG,GAAG,CAAC8F,SAAS,KAAK,EAAE,GAAGA,SAAS,GAAG,GAAG,GAAG,EAAE,IAAI7F,KAAK;MAC3D,IAAIsN,OAAO,GAAG;QACZnN,IAAI,EAAEA,IAAI;QACVH,KAAK,EAAEA,KAAK;QACZO,KAAK,EAAEA,KAAK;QACZR,GAAG,EAAEA,GAAG;QACRkL,MAAM,EAAEA,MAAM;QACdpF,SAAS,EAAEA;MACb,CAAC;MACDyH,OAAO,CAACrL,KAAK,GAAGkL,qBAAqB,CAAChN,IAAI,CAAC8B,KAAK,EAAE1B,KAAK,GAAG,CAAC,EAAE+M,OAAO,EAAEvN,GAAG,CAAC;MAC1EsN,eAAe,CAAC/R,IAAI,CAACgS,OAAO,CAAC;IAC/B,CAAC,CAAC;IACF,OAAOD,eAAe;EACxB,CAAC;EACD9U,cAAc,CAAC,YAAY;IACzB,IAAI,CAAC6M,OAAO,EAAE;MACZC,UAAU,CAACtM,iBAAiB,CAAC,CAAC,CAAC;IACjC;EACF,CAAC,CAAC;EACFP,eAAe,CAAC,YAAY;IAC1B,IAAI+D,iBAAiB,EAAE;MACrBqM,wBAAwB,CAAC,CAAC;MAC1BK,kBAAkB,CAAC,CAAC;MACpBjQ,WAAW,CAACuU,GAAG,CAAC,MAAM,EAAE9F,WAAW,CAACa,OAAO,EAAEpI,OAAO,IAAIA,OAAO,CAACsN,UAAU,IAAIzV,UAAU,CAACyV,UAAU,EAAEtN,OAAO,IAAIA,OAAO,CAACuN,MAAM,CAACxQ,IAAI,IAAIlF,UAAU,CAAC0V,MAAM,CAACxQ,IAAI,CAAC;IAChK,CAAC,MAAM;MACLiM,oBAAoB,CAAC,CAAC;MACtBL,0BAA0B,CAAC,CAAC;MAC5B7P,WAAW,CAAC0U,KAAK,CAACjG,WAAW,CAACa,OAAO,CAAC;IACxC;EACF,CAAC,EAAE,CAAC/L,iBAAiB,CAAC,CAAC;EACvBzE,KAAK,CAAC6V,SAAS,CAAC,YAAY;IAC1B,IAAIC,cAAc,GAAGpO,KAAK,CAAC5B,KAAK,IAAI,EAAE;IACtC,IAAIiQ,SAAS,GAAGV,qBAAqB,CAACS,cAAc,EAAE,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC;IAClE9G,iBAAiB,CAAC+G,SAAS,CAAC;IAC5B;EACF,CAAC,EAAE,CAACrO,KAAK,CAAC5B,KAAK,CAAC,CAAC;EACjBpF,eAAe,CAAC,YAAY;IAC1B,IAAIsH,aAAa,GAAG4B,cAAc,CAAC4J,IAAI,CAAC,UAAUN,CAAC,EAAE;MACnD,OAAOA,CAAC,CAACjL,GAAG,KAAKgG,eAAe,CAACF,SAAS;IAC5C,CAAC,CAAC;IACF,IAAIwH,eAAe,GAAGvN,aAAa,GAAGA,aAAa,CAACmC,KAAK,GAAG4E,cAAc;IAC1EH,eAAe,CAAC2G,eAAe,CAAC;EAClC,CAAC,EAAE,CAAC3L,cAAc,EAAEqE,eAAe,EAAEc,cAAc,CAAC,CAAC;EACrDrO,eAAe,CAAC,YAAY;IAC1B,IAAII,WAAW,CAACoJ,UAAU,CAACN,cAAc,CAAC,EAAE;MAC1CkH,wBAAwB,CAAC,CAAC;MAC1BK,kBAAkB,CAAC,CAAC;IACtB,CAAC,MAAM;MACLJ,0BAA0B,CAAC,CAAC;MAC5BK,oBAAoB,CAAC,CAAC;IACxB;EACF,CAAC,EAAE,CAACxH,cAAc,CAAC,CAAC;EACpBlJ,eAAe,CAAC,YAAY;IAC1B,IAAIyO,YAAY,EAAE;MAChB,IAAIuE,SAAS,GAAGzF,eAAe,CAAC/F,KAAK,KAAK,CAAC,CAAC,GAAGyL,iBAAiB,CAAC1F,eAAe,CAAC/F,KAAK,CAAC,GAAG6H,cAAc,CAACS,OAAO,GAAGyD,iBAAiB,CAAC,CAAC,GAAGzC,yBAAyB,CAAC,CAAC;MACpKoC,sBAAsB,CAACF,SAAS,CAAC;MACjC3D,cAAc,CAACS,OAAO,GAAG,KAAK;MAC9BpB,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC,EAAE,CAACD,YAAY,CAAC,CAAC;EAClBzO,eAAe,CAAC,YAAY;IAC1B2N,gBAAgB,CAACJ,eAAe,CAAC/F,KAAK,KAAK,CAAC,CAAC,GAAG,EAAE,CAACsB,MAAM,CAAC8D,OAAO,CAAC,CAAC9D,MAAM,CAAC1I,WAAW,CAACoJ,UAAU,CAAC+D,eAAe,CAACF,SAAS,CAAC,GAAG,GAAG,GAAGE,eAAe,CAACF,SAAS,GAAG,EAAE,EAAE,GAAG,CAAC,CAACvE,MAAM,CAACyE,eAAe,CAAC/F,KAAK,CAAC,GAAG,IAAI,CAAC;EAChN,CAAC,EAAE,CAAC+F,eAAe,CAAC,CAAC;EACrBtN,gBAAgB,CAAC,YAAY;IAC3BO,WAAW,CAAC0U,KAAK,CAACjG,WAAW,CAACa,OAAO,CAAC;EACxC,CAAC,CAAC;EACFxQ,KAAK,CAACgW,mBAAmB,CAACrO,GAAG,EAAE,YAAY;IACzC,OAAO;MACLD,KAAK,EAAEA,KAAK;MACZ2J,MAAM,EAAEA,MAAM;MACd4E,UAAU,EAAE,SAASA,UAAUA,CAAA,EAAG;QAChC,OAAOxG,UAAU,CAACe,OAAO;MAC3B,CAAC;MACD0F,WAAW,EAAE,SAASA,WAAWA,CAAA,EAAG;QAClC,OAAOvG,WAAW,CAACa,OAAO;MAC5B,CAAC;MACD2F,aAAa,EAAE,SAASA,aAAaA,CAAA,EAAG;QACtC,OAAOvG,aAAa,CAACY,OAAO;MAC9B;IACF,CAAC;EACH,CAAC,CAAC;EACF,IAAI4F,kBAAkB,GAAG,SAASA,kBAAkBA,CAAA,EAAG;IACrD,IAAI1O,KAAK,CAACtD,KAAK,EAAE;MACf,IAAIiS,MAAM,GAAGvV,WAAW,CAACiL,aAAa,CAACrE,KAAK,CAACtD,KAAK,EAAEsD,KAAK,CAAC;MAC1D,IAAI4O,UAAU,GAAG1O,UAAU,CAAC;QAC1B5B,SAAS,EAAE8B,EAAE,CAAC,OAAO;MACvB,CAAC,EAAED,GAAG,CAAC,OAAO,CAAC,CAAC;MAChB,OAAO,aAAa7H,KAAK,CAACyK,aAAa,CAAC,KAAK,EAAE6L,UAAU,EAAED,MAAM,CAAC;IACpE;IACA,OAAO,IAAI;EACb,CAAC;EACD,IAAIE,gBAAgB,GAAG,SAASA,gBAAgBA,CAAA,EAAG;IACjD,IAAI7O,KAAK,CAACrD,GAAG,EAAE;MACb,IAAImS,IAAI,GAAG1V,WAAW,CAACiL,aAAa,CAACrE,KAAK,CAACrD,GAAG,EAAEqD,KAAK,CAAC;MACtD,IAAI+O,QAAQ,GAAG7O,UAAU,CAAC;QACxB5B,SAAS,EAAE8B,EAAE,CAAC,KAAK;MACrB,CAAC,EAAED,GAAG,CAAC,KAAK,CAAC,CAAC;MACd,OAAO,aAAa7H,KAAK,CAACyK,aAAa,CAAC,KAAK,EAAEgM,QAAQ,EAAED,IAAI,CAAC;IAChE;IACA,OAAO,IAAI;EACb,CAAC;EACD,IAAIE,gBAAgB,GAAG,SAASA,gBAAgBA,CAAA,EAAG;IACjD,IAAIhP,KAAK,CAAC5B,KAAK,IAAI4B,KAAK,CAAC5B,KAAK,CAACrC,MAAM,GAAG,CAAC,EAAE;MACzC,OAAO,IAAI;IACb;IACA,IAAIkT,WAAW,GAAG/O,UAAU,CAACtF,eAAe,CAACA,eAAe,CAACA,eAAe,CAACA,eAAe,CAAC;MAC3FqF,GAAG,EAAEiI,aAAa;MAClBpE,IAAI,EAAE,GAAG;MACTC,QAAQ,EAAE,GAAG;MACb,eAAe,EAAEhH,iBAAiB,IAAIiD,KAAK,CAAC5B,KAAK,IAAI4B,KAAK,CAAC5B,KAAK,CAACrC,MAAM,GAAG,CAAC,GAAG,IAAI,GAAG,KAAK;MAC1F,eAAe,EAAEgB,iBAAiB;MAClC,YAAY,EAAEtE,SAAS,CAAC,YAAY,CAAC;MACrC,eAAe,EAAEmN,OAAO;MACxB9C,IAAI,EAAE;IACR,CAAC,EAAE,UAAU,EAAE,CAAC,CAAC,EAAE,WAAW,EAAE1C,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,WAAW,EAAE,SAAS4E,SAASA,CAAC3K,CAAC,EAAE;MAChF,OAAO2P,iBAAiB,CAAC3P,CAAC,CAAC;IAC7B,CAAC,CAAC,EAAE,SAAS,EAAE,SAASkK,OAAOA,CAAClK,CAAC,EAAE;MACjC,OAAOsP,MAAM,CAACtP,CAAC,CAAC;IAClB,CAAC,CAAC,EAAE8F,GAAG,CAAC,QAAQ,CAAC,CAAC;IAClB,IAAI+O,cAAc,GAAGhP,UAAU,CAACC,GAAG,CAAC,WAAW,CAAC,CAAC;IACjD,IAAIlD,IAAI,GAAG+C,KAAK,CAACtB,QAAQ,IAAI,aAAapG,KAAK,CAACyK,aAAa,CAAC7J,QAAQ,EAAEgW,cAAc,CAAC;IACvF,IAAIxQ,QAAQ,GAAGrF,SAAS,CAACoK,UAAU,CAACxG,IAAI,EAAEmI,aAAa,CAAC,CAAC,CAAC,EAAE8J,cAAc,CAAC,EAAE;MAC3ElP,KAAK,EAAEA;IACT,CAAC,CAAC;;IAEF;IACA,IAAIpD,MAAM,GAAG,aAAatE,KAAK,CAACyK,aAAa,CAAC,GAAG,EAAEkM,WAAW,EAAEvQ,QAAQ,CAAC;IACzE;;IAEA,OAAO9B,MAAM;EACf,CAAC;EACD,IAAIF,KAAK,GAAGgS,kBAAkB,CAAC,CAAC;EAChC,IAAI/R,GAAG,GAAGkS,gBAAgB,CAAC,CAAC;EAC5B,IAAIM,UAAU,GAAGH,gBAAgB,CAAC,CAAC;EACnC,IAAIrR,OAAO,GAAG,aAAarF,KAAK,CAACyK,aAAa,CAAClD,UAAU,EAAE;IACzDY,QAAQ,EAAE,SAAS;IACnBwE,oBAAoB,EAAE1H,OAAO,GAAG+E,aAAa,GAAG1D,SAAS;IACzDmC,KAAK,EAAE,CAAC;IACR5C,EAAE,EAAEyH,OAAO;IACX3F,GAAG,EAAEgI,WAAW;IAChBhF,SAAS,EAAEjD,KAAK;IAChB5B,KAAK,EAAEiJ,cAAc;IACrB9F,WAAW,EAAEF,WAAW;IACxBL,gBAAgB,EAAEA,gBAAgB;IAClCxC,OAAO,EAAEA,OAAO;IAChBC,MAAM,EAAEA,MAAM;IACduG,SAAS,EAAEA,SAAS;IACpBnI,IAAI,EAAE,IAAI;IACVqF,cAAc,EAAEA,cAAc;IAC9BI,aAAa,EAAE/E,OAAO,GAAG+E,aAAa,GAAG1D,SAAS;IAClDzB,WAAW,EAAE6C,KAAK,CAAC7C,WAAW;IAC9BgD,GAAG,EAAEA,GAAG;IACRC,EAAE,EAAEA;EACN,CAAC,CAAC;EACF,IAAIgP,SAAS,GAAGlP,UAAU,CAAC;IACzB/B,EAAE,EAAE6B,KAAK,CAAC7B,EAAE;IACZ8B,GAAG,EAAE8H,UAAU;IACfzJ,SAAS,EAAEnF,UAAU,CAAC6G,KAAK,CAAC1B,SAAS,EAAE8B,EAAE,CAAC,MAAM,EAAE;MAChDrD,iBAAiB,EAAEA;IACrB,CAAC,CAAC,CAAC;IACHsB,KAAK,EAAE2B,KAAK,CAAC3B;EACf,CAAC,EAAEN,WAAW,CAACsR,aAAa,CAACrP,KAAK,CAAC,EAAEG,GAAG,CAAC,MAAM,CAAC,CAAC;EACjD,OAAO,aAAa7H,KAAK,CAACyK,aAAa,CAAC,KAAK,EAAEqM,SAAS,EAAE1S,KAAK,EAAEyS,UAAU,EAAExR,OAAO,EAAEhB,GAAG,CAAC;AAC5F,CAAC,CAAC,CAAC;AACH0I,OAAO,CAACH,WAAW,GAAG,SAAS;AAE/B,SAASG,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}