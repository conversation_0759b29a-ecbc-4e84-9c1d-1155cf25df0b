{"ast": null, "code": "var _jsxFileName = \"C:\\\\git\\\\Partner-Site\\\\BdoPartner.Plans\\\\Development\\\\ReactClientApp\\\\src\\\\core\\\\auth\\\\components\\\\logoutCallback.jsx\";\n/* /src/components/auth/logoutCallback.jsx */\n\nimport React from \"react\";\nimport { AuthConsumer } from \"./authProvider\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const LogoutCallback = () => /*#__PURE__*/_jsxDEV(AuthConsumer, {\n  children: ({\n    signoutRedirectCallback\n  }) => {\n    signoutRedirectCallback();\n    return /*#__PURE__*/_jsxDEV(\"span\", {\n      children: \"loading\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 20\n    }, this);\n  }\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 7,\n  columnNumber: 5\n}, this);\n_c = LogoutCallback;\nvar _c;\n$RefreshReg$(_c, \"LogoutCallback\");", "map": {"version": 3, "names": ["React", "AuthConsumer", "jsxDEV", "_jsxDEV", "LogoutCallback", "children", "signoutRedirectCallback", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/core/auth/components/logoutCallback.jsx"], "sourcesContent": ["/* /src/components/auth/logoutCallback.jsx */\r\n\r\nimport React from \"react\";\r\nimport { AuthConsumer } from \"./authProvider\";\r\n\r\nexport const LogoutCallback = () => (\r\n    <AuthConsumer>\r\n        {({ signoutRedirectCallback }) => {\r\n            signoutRedirectCallback();\r\n            return <span>loading</span>;\r\n        }}\r\n    </AuthConsumer>\r\n);"], "mappings": ";AAAA;;AAEA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,YAAY,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,OAAO,MAAMC,cAAc,GAAGA,CAAA,kBAC1BD,OAAA,CAACF,YAAY;EAAAI,QAAA,EACRA,CAAC;IAAEC;EAAwB,CAAC,KAAK;IAC9BA,uBAAuB,CAAC,CAAC;IACzB,oBAAOH,OAAA;MAAAE,QAAA,EAAM;IAAO;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAC/B;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACS,CACjB;AAACC,EAAA,GAPWP,cAAc;AAAA,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}