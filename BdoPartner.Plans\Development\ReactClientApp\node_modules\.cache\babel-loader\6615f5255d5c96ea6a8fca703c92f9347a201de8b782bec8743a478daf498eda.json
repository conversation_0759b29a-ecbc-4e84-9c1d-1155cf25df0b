{"ast": null, "code": "var _jsxFileName = \"C:\\\\git\\\\Partner-Site\\\\BdoPartner.Plans\\\\Development\\\\ReactClientApp\\\\src\\\\pages\\\\usermanagement.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport APP_CONFIG from \"../core/config/appConfig\";\nimport { ResultStatus } from \"../core/enumertions/resultStatus\";\nimport http from \"../core/http/httpClient\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport const UserManagement = () => {\n  _s();\n  const [users, setUsers] = useState([]);\n  useEffect(() => {\n    //console.log(\"UserManagement called : \" + APP_CONFIG.apiDoamin);\n    http.get(APP_CONFIG.apiDomain + \"/api/user/getusers\").then(response => {\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\n        setUsers(response.data.item);\n      }\n    });\n  }, []);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"It is user management\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n      children: users.map(user => {\n        return /*#__PURE__*/_jsxDEV(\"li\", {\n          children: user.userName\n        }, user.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 18\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(UserManagement, \"JadZszbqna06PpJs9hMo7Hl/LOY=\");\n_c = UserManagement;\nvar _c;\n$RefreshReg$(_c, \"UserManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "APP_CONFIG", "ResultStatus", "http", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "UserManagement", "_s", "users", "setUsers", "get", "apiDomain", "then", "response", "data", "resultStatus", "Success", "item", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "user", "userName", "id", "_c", "$RefreshReg$"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/pages/usermanagement.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport APP_CONFIG from \"../core/config/appConfig\";\r\nimport { ResultStatus } from \"../core/enumertions/resultStatus\";\r\nimport http from \"../core/http/httpClient\";\r\n\r\nexport const UserManagement = () => {\r\n  const [users, setUsers] = useState([]);\r\n  useEffect(() => {\r\n    //console.log(\"UserManagement called : \" + APP_CONFIG.apiDoamin);\r\n    http.get(APP_CONFIG.apiDomain + \"/api/user/getusers\").then((response) => {\r\n      if (\r\n        response.data &&\r\n        response.data.resultStatus === ResultStatus.Success\r\n      ) {\r\n        setUsers(response.data.item);\r\n      }\r\n    });\r\n  }, []);\r\n\r\n  return (\r\n    <>\r\n      <h2>It is user management</h2>\r\n      <ul>\r\n        {users.map((user) => {\r\n          return <li key={user.id}>{user.userName}</li>;\r\n        })}\r\n      </ul>\r\n    </>\r\n  );\r\n};\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,UAAU,MAAM,0BAA0B;AACjD,SAASC,YAAY,QAAQ,kCAAkC;AAC/D,OAAOC,IAAI,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE3C,OAAO,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EACtCC,SAAS,CAAC,MAAM;IACd;IACAG,IAAI,CAACS,GAAG,CAACX,UAAU,CAACY,SAAS,GAAG,oBAAoB,CAAC,CAACC,IAAI,CAAEC,QAAQ,IAAK;MACvE,IACEA,QAAQ,CAACC,IAAI,IACbD,QAAQ,CAACC,IAAI,CAACC,YAAY,KAAKf,YAAY,CAACgB,OAAO,EACnD;QACAP,QAAQ,CAACI,QAAQ,CAACC,IAAI,CAACG,IAAI,CAAC;MAC9B;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEd,OAAA,CAAAE,SAAA;IAAAa,QAAA,gBACEf,OAAA;MAAAe,QAAA,EAAI;IAAqB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC9BnB,OAAA;MAAAe,QAAA,EACGV,KAAK,CAACe,GAAG,CAAEC,IAAI,IAAK;QACnB,oBAAOrB,OAAA;UAAAe,QAAA,EAAmBM,IAAI,CAACC;QAAQ,GAAvBD,IAAI,CAACE,EAAE;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAqB,CAAC;MAC/C,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA,eACL,CAAC;AAEP,CAAC;AAACf,EAAA,CAxBWD,cAAc;AAAAqB,EAAA,GAAdrB,cAAc;AAAA,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}