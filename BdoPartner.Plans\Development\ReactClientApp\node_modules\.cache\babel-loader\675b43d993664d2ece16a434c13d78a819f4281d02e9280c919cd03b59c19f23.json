{"ast": null, "code": "import { isFunction } from './isFunction';\nimport { isScheduler } from './isScheduler';\nfunction last(arr) {\n  return arr[arr.length - 1];\n}\nexport function popResultSelector(args) {\n  return isFunction(last(args)) ? args.pop() : undefined;\n}\nexport function popScheduler(args) {\n  return isScheduler(last(args)) ? args.pop() : undefined;\n}\nexport function popNumber(args, defaultValue) {\n  return typeof last(args) === 'number' ? args.pop() : defaultValue;\n}", "map": {"version": 3, "names": ["isFunction", "isScheduler", "last", "arr", "length", "popResultSelector", "args", "pop", "undefined", "popScheduler", "popNumber", "defaultValue"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\util\\args.ts"], "sourcesContent": ["import { SchedulerLike } from '../types';\nimport { isFunction } from './isFunction';\nimport { isScheduler } from './isScheduler';\n\nfunction last<T>(arr: T[]): T | undefined {\n  return arr[arr.length - 1];\n}\n\nexport function popResultSelector(args: any[]): ((...args: unknown[]) => unknown) | undefined {\n  return isFunction(last(args)) ? args.pop() : undefined;\n}\n\nexport function popScheduler(args: any[]): SchedulerLike | undefined {\n  return isScheduler(last(args)) ? args.pop() : undefined;\n}\n\nexport function popNumber(args: any[], defaultValue: number): number {\n  return typeof last(args) === 'number' ? args.pop()! : defaultValue;\n}\n"], "mappings": "AACA,SAASA,UAAU,QAAQ,cAAc;AACzC,SAASC,WAAW,QAAQ,eAAe;AAE3C,SAASC,IAAIA,CAAIC,GAAQ;EACvB,OAAOA,GAAG,CAACA,GAAG,CAACC,MAAM,GAAG,CAAC,CAAC;AAC5B;AAEA,OAAM,SAAUC,iBAAiBA,CAACC,IAAW;EAC3C,OAAON,UAAU,CAACE,IAAI,CAACI,IAAI,CAAC,CAAC,GAAGA,IAAI,CAACC,GAAG,EAAE,GAAGC,SAAS;AACxD;AAEA,OAAM,SAAUC,YAAYA,CAACH,IAAW;EACtC,OAAOL,WAAW,CAACC,IAAI,CAACI,IAAI,CAAC,CAAC,GAAGA,IAAI,CAACC,GAAG,EAAE,GAAGC,SAAS;AACzD;AAEA,OAAM,SAAUE,SAASA,CAACJ,IAAW,EAAEK,YAAoB;EACzD,OAAO,OAAOT,IAAI,CAACI,IAAI,CAAC,KAAK,QAAQ,GAAGA,IAAI,CAACC,GAAG,EAAG,GAAGI,YAAY;AACpE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}