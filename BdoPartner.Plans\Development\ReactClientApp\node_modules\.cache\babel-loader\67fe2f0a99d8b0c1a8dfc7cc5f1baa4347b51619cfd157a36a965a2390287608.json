{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport { PrimeReactContext, ariaLabel } from 'primereact/api';\nimport { ComponentBase, useHandleStyle } from 'primereact/componentbase';\nimport { useMergeProps, useMountEffect, useUpdateEffect } from 'primereact/hooks';\nimport { ChevronLeftIcon } from 'primereact/icons/chevronleft';\nimport { ChevronRightIcon } from 'primereact/icons/chevronright';\nimport { TimesIcon } from 'primereact/icons/times';\nimport { Ripple } from 'primereact/ripple';\nimport { ObjectUtils, classNames, UniqueComponentId, DomHandler, IconUtils } from 'primereact/utils';\nfunction _arrayLikeToArray(r, a) {\n  (null == a || a > r.length) && (a = r.length);\n  for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e];\n  return n;\n}\nfunction _arrayWithoutHoles(r) {\n  if (Array.isArray(r)) return _arrayLikeToArray(r);\n}\nfunction _iterableToArray(r) {\n  if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r);\n}\nfunction _unsupportedIterableToArray(r, a) {\n  if (r) {\n    if (\"string\" == typeof r) return _arrayLikeToArray(r, a);\n    var t = {}.toString.call(r).slice(8, -1);\n    return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0;\n  }\n}\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _toConsumableArray(r) {\n  return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread();\n}\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nfunction toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction toPropertyKey(t) {\n  var i = toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nfunction _defineProperty(e, r, t) {\n  return (r = toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _arrayWithHoles(r) {\n  if (Array.isArray(r)) return r;\n}\nfunction _iterableToArrayLimit(r, l) {\n  var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"];\n  if (null != t) {\n    var e,\n      n,\n      i,\n      u,\n      a = [],\n      f = !0,\n      o = !1;\n    try {\n      if (i = (t = t.call(r)).next, 0 === l) {\n        if (Object(t) !== t) return;\n        f = !1;\n      } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0);\n    } catch (r) {\n      o = !0, n = r;\n    } finally {\n      try {\n        if (!f && null != t[\"return\"] && (u = t[\"return\"](), Object(u) !== u)) return;\n      } finally {\n        if (o) throw n;\n      }\n    }\n    return a;\n  }\n}\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _slicedToArray(r, e) {\n  return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest();\n}\nfunction ownKeys$1(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread$1(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys$1(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$1(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nvar classes = {\n  navcontent: 'p-tabview-nav-content',\n  nav: 'p-tabview-nav',\n  inkbar: 'p-tabview-ink-bar',\n  panelcontainer: function panelcontainer(_ref) {\n    var props = _ref.props;\n    return classNames('p-tabview-panels', props.panelContainerClassName);\n  },\n  prevbutton: 'p-tabview-nav-prev p-tabview-nav-btn p-link',\n  nextbutton: 'p-tabview-nav-next p-tabview-nav-btn p-link',\n  root: function root(_ref2) {\n    var props = _ref2.props;\n    return classNames('p-tabview p-component', {\n      'p-tabview-scrollable': props.scrollable\n    });\n  },\n  navcontainer: 'p-tabview-nav-container',\n  tab: {\n    header: function header(_ref3) {\n      var selected = _ref3.selected,\n        disabled = _ref3.disabled,\n        headerClassName = _ref3.headerClassName,\n        _className = _ref3._className;\n      return classNames('p-unselectable-text', {\n        'p-tabview-selected p-highlight': selected,\n        'p-disabled': disabled\n      }, headerClassName, _className);\n    },\n    headertitle: 'p-tabview-title',\n    headeraction: 'p-tabview-nav-link',\n    closeIcon: 'p-tabview-close',\n    content: function content(_ref4) {\n      var props = _ref4.props,\n        selected = _ref4.selected,\n        getTabProp = _ref4.getTabProp,\n        tab = _ref4.tab,\n        isSelected = _ref4.isSelected,\n        shouldUseTab = _ref4.shouldUseTab,\n        index = _ref4.index;\n      return shouldUseTab(tab, index) && (!props.renderActiveOnly || isSelected(index)) ? classNames(getTabProp(tab, 'contentClassName'), getTabProp(tab, 'className'), 'p-tabview-panel', {\n        'p-hidden': !selected\n      }) : undefined;\n    }\n  }\n};\nvar inlineStyles = {\n  tab: {\n    header: function header(_ref5) {\n      var headerStyle = _ref5.headerStyle,\n        _style = _ref5._style;\n      return _objectSpread$1(_objectSpread$1({}, headerStyle || {}), _style || {});\n    },\n    content: function content(_ref6) {\n      var props = _ref6.props,\n        getTabProp = _ref6.getTabProp,\n        tab = _ref6.tab,\n        isSelected = _ref6.isSelected,\n        shouldUseTab = _ref6.shouldUseTab,\n        index = _ref6.index;\n      return shouldUseTab(tab, index) && (!props.renderActiveOnly || isSelected(index)) ? _objectSpread$1(_objectSpread$1({}, getTabProp(tab, 'contentStyle') || {}), getTabProp(tab, 'style') || {}) : undefined;\n    }\n  }\n};\nvar TabViewBase = ComponentBase.extend({\n  defaultProps: {\n    __TYPE: 'TabView',\n    id: null,\n    activeIndex: 0,\n    className: null,\n    onBeforeTabChange: null,\n    onBeforeTabClose: null,\n    onTabChange: null,\n    onTabClose: null,\n    panelContainerClassName: null,\n    panelContainerStyle: null,\n    renderActiveOnly: true,\n    scrollable: false,\n    style: null,\n    children: undefined\n  },\n  css: {\n    classes: classes,\n    inlineStyles: inlineStyles\n  }\n});\nvar TabPanelBase = ComponentBase.extend({\n  defaultProps: {\n    __TYPE: 'TabPanel',\n    children: undefined,\n    className: null,\n    closable: false,\n    closeIcon: null,\n    contentClassName: null,\n    contentStyle: null,\n    disabled: false,\n    header: null,\n    headerClassName: null,\n    headerStyle: null,\n    headerTemplate: null,\n    leftIcon: null,\n    nextButton: null,\n    prevButton: null,\n    rightIcon: null,\n    style: null,\n    visible: true\n  },\n  getCProp: function getCProp(tab, name) {\n    return ObjectUtils.getComponentProp(tab, name, TabPanelBase.defaultProps);\n  },\n  getCProps: function getCProps(tab) {\n    return ObjectUtils.getComponentProps(tab, TabPanelBase.defaultProps);\n  },\n  getCOtherProps: function getCOtherProps(tab) {\n    return ObjectUtils.getComponentDiffProps(tab, TabPanelBase.defaultProps);\n  }\n});\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nvar TabPanel = function TabPanel() {};\nvar TabView = /*#__PURE__*/React.forwardRef(function (inProps, ref) {\n  var mergeProps = useMergeProps();\n  var context = React.useContext(PrimeReactContext);\n  var props = TabViewBase.getProps(inProps, context);\n  var _React$useState = React.useState(props.id),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    idState = _React$useState2[0],\n    setIdState = _React$useState2[1];\n  var _React$useState3 = React.useState(true),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    backwardIsDisabledState = _React$useState4[0],\n    setBackwardIsDisabledState = _React$useState4[1];\n  var _React$useState5 = React.useState(false),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    forwardIsDisabledState = _React$useState6[0],\n    setForwardIsDisabledState = _React$useState6[1];\n  var _React$useState7 = React.useState([]),\n    _React$useState8 = _slicedToArray(_React$useState7, 2),\n    hiddenTabsState = _React$useState8[0],\n    setHiddenTabsState = _React$useState8[1];\n  var _React$useState9 = React.useState(props.activeIndex),\n    _React$useState10 = _slicedToArray(_React$useState9, 2),\n    activeIndexState = _React$useState10[0],\n    setActiveIndexState = _React$useState10[1];\n  var elementRef = React.useRef(null);\n  var contentRef = React.useRef(null);\n  var navRef = React.useRef(null);\n  var inkbarRef = React.useRef(null);\n  var prevBtnRef = React.useRef(null);\n  var nextBtnRef = React.useRef(null);\n  var tabsRef = React.useRef({});\n  var activeIndex = props.onTabChange ? props.activeIndex : activeIndexState;\n  var count = React.Children.count(props.children);\n  var metaData = {\n    props: props,\n    state: {\n      id: idState,\n      isPrevButtonDisabled: backwardIsDisabledState,\n      isNextButtonDisabled: forwardIsDisabledState,\n      hiddenTabsState: hiddenTabsState,\n      activeIndex: activeIndexState\n    }\n  };\n  var _TabViewBase$setMetaD = TabViewBase.setMetaData(_objectSpread({}, metaData)),\n    ptm = _TabViewBase$setMetaD.ptm,\n    ptmo = _TabViewBase$setMetaD.ptmo,\n    cx = _TabViewBase$setMetaD.cx,\n    sx = _TabViewBase$setMetaD.sx,\n    isUnstyled = _TabViewBase$setMetaD.isUnstyled;\n  useHandleStyle(TabViewBase.css.styles, isUnstyled, {\n    name: 'tabview'\n  });\n  var getTabPT = function getTabPT(tab, key, index) {\n    var tabMetaData = {\n      props: tab.props,\n      parent: metaData,\n      context: {\n        index: index,\n        count: count,\n        first: index === 0,\n        last: index === count - 1,\n        active: index == activeIndexState,\n        disabled: getTabProp(tab, 'disabled')\n      }\n    };\n    return mergeProps(ptm(\"tab.\".concat(key), {\n      tab: tabMetaData\n    }), ptm(\"tabpanel.\".concat(key), {\n      tabpanel: tabMetaData\n    }), ptm(\"tabpanel.\".concat(key), tabMetaData), ptmo(getTabProp(tab, 'pt'), key, tabMetaData));\n  };\n  var isSelected = function isSelected(index) {\n    return index === activeIndex;\n  };\n  var getTabProp = function getTabProp(tab, name) {\n    return TabPanelBase.getCProp(tab, name);\n  };\n  var shouldUseTab = function shouldUseTab(tab) {\n    return tab && getTabProp(tab, 'visible') && ObjectUtils.isValidChild(tab, 'TabPanel') && hiddenTabsState.every(function (_i) {\n      return _i !== tab.key;\n    });\n  };\n  var findVisibleActiveTab = function findVisibleActiveTab(i) {\n    var tabsInfo = React.Children.map(props.children, function (tab, index) {\n      if (shouldUseTab(tab)) {\n        return {\n          tab: tab,\n          index: index\n        };\n      }\n    });\n    return tabsInfo.find(function (_ref) {\n      var tab = _ref.tab,\n        index = _ref.index;\n      return !getTabProp(tab, 'disabled') && index >= i;\n    }) || tabsInfo.reverse().find(function (_ref2) {\n      var tab = _ref2.tab,\n        index = _ref2.index;\n      return !getTabProp(tab, 'disabled') && i > index;\n    });\n  };\n  var onTabHeaderClose = function onTabHeaderClose(event, index) {\n    event.preventDefault();\n    var onBeforeTabClose = props.onBeforeTabClose,\n      onTabClose = props.onTabClose,\n      children = props.children;\n    var key = children[index].key;\n\n    // give caller a chance to stop the selection\n    if (onBeforeTabClose && onBeforeTabClose({\n      originalEvent: event,\n      index: index\n    }) === false) {\n      return;\n    }\n    setHiddenTabsState([].concat(_toConsumableArray(hiddenTabsState), [key]));\n    if (onTabClose) {\n      onTabClose({\n        originalEvent: event,\n        index: index\n      });\n    }\n  };\n  var onTabHeaderClick = function onTabHeaderClick(event, tab, index) {\n    changeActiveIndex(event, tab, index);\n  };\n  var changeActiveIndex = function changeActiveIndex(event, tab, index) {\n    if (event) {\n      event.preventDefault();\n    }\n    if (!getTabProp(tab, 'disabled')) {\n      // give caller a chance to stop the selection\n      if (props.onBeforeTabChange && props.onBeforeTabChange({\n        originalEvent: event,\n        index: index\n      }) === false) {\n        return;\n      }\n      if (props.onTabChange) {\n        props.onTabChange({\n          originalEvent: event,\n          index: index\n        });\n      } else {\n        setActiveIndexState(index);\n      }\n    }\n    updateScrollBar({\n      index: index\n    });\n  };\n  var _onKeyDown = function onKeyDown(event, tab, index) {\n    switch (event.code) {\n      case 'ArrowLeft':\n        onTabArrowLeftKey(event);\n        break;\n      case 'ArrowRight':\n        onTabArrowRightKey(event);\n        break;\n      case 'Home':\n        onTabHomeKey(event);\n        break;\n      case 'End':\n        onTabEndKey(event);\n        break;\n      case 'PageDown':\n        onPageDownKey(event);\n        break;\n      case 'PageUp':\n        onPageUpKey(event);\n        break;\n      case 'Enter':\n      case 'NumpadEnter':\n      case 'Space':\n        onTabEnterKey(event, tab, index);\n        break;\n    }\n  };\n  var onTabArrowRightKey = function onTabArrowRightKey(event) {\n    var nextHeaderAction = _findNextHeaderAction(event.target.parentElement);\n    nextHeaderAction ? changeFocusedTab(nextHeaderAction) : onTabHomeKey(event);\n    event.preventDefault();\n  };\n  var onTabArrowLeftKey = function onTabArrowLeftKey(event) {\n    var prevHeaderAction = _findPrevHeaderAction(event.target.parentElement);\n    prevHeaderAction ? changeFocusedTab(prevHeaderAction) : onTabEndKey(event);\n    event.preventDefault();\n  };\n  var onTabHomeKey = function onTabHomeKey(event) {\n    var firstHeaderAction = findFirstHeaderAction();\n    changeFocusedTab(firstHeaderAction);\n    event.preventDefault();\n  };\n  var onTabEndKey = function onTabEndKey(event) {\n    var lastHeaderAction = findLastHeaderAction();\n    changeFocusedTab(lastHeaderAction);\n    event.preventDefault();\n  };\n  var onPageDownKey = function onPageDownKey(event) {\n    updateScrollBar({\n      index: React.Children.count(props.children) - 1\n    });\n    event.preventDefault();\n  };\n  var onPageUpKey = function onPageUpKey(event) {\n    updateScrollBar({\n      index: 0\n    });\n    event.preventDefault();\n  };\n  var onTabEnterKey = function onTabEnterKey(event, tab, index) {\n    changeActiveIndex(event, tab, index);\n    event.preventDefault();\n  };\n  var _findNextHeaderAction = function findNextHeaderAction(tabElement) {\n    var selfCheck = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    var headerElement = selfCheck ? tabElement : tabElement.nextElementSibling;\n    return headerElement ? DomHandler.getAttribute(headerElement, 'data-p-disabled') || DomHandler.getAttribute(headerElement, 'data-pc-section') === 'inkbar' ? _findNextHeaderAction(headerElement) : DomHandler.findSingle(headerElement, '[data-pc-section=\"headeraction\"]') : null;\n  };\n  var _findPrevHeaderAction = function findPrevHeaderAction(tabElement) {\n    var selfCheck = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    var headerElement = selfCheck ? tabElement : tabElement.previousElementSibling;\n    return headerElement ? DomHandler.getAttribute(headerElement, 'data-p-disabled') || DomHandler.getAttribute(headerElement, 'data-pc-section') === 'inkbar' ? _findPrevHeaderAction(headerElement) : DomHandler.findSingle(headerElement, '[data-pc-section=\"headeraction\"]') : null;\n  };\n  var findFirstHeaderAction = function findFirstHeaderAction() {\n    return _findNextHeaderAction(navRef.current.firstElementChild, true);\n  };\n  var findLastHeaderAction = function findLastHeaderAction() {\n    return _findPrevHeaderAction(navRef.current.lastElementChild, true);\n  };\n  var changeFocusedTab = function changeFocusedTab(element) {\n    if (element) {\n      DomHandler.focus(element);\n      updateScrollBar({\n        element: element\n      });\n    }\n  };\n  var updateInkBar = function updateInkBar() {\n    var tabHeader = tabsRef.current[\"tab_\".concat(activeIndex)];\n    inkbarRef.current.style.width = DomHandler.getWidth(tabHeader) + 'px';\n    inkbarRef.current.style.left = DomHandler.getOffset(tabHeader).left - DomHandler.getOffset(navRef.current).left + 'px';\n  };\n  var updateScrollBar = function updateScrollBar(_ref3) {\n    var index = _ref3.index,\n      element = _ref3.element;\n    var tabHeader = element || tabsRef.current[\"tab_\".concat(index)];\n    if (tabHeader && tabHeader.scrollIntoView) {\n      tabHeader.scrollIntoView({\n        block: 'nearest'\n      });\n    }\n  };\n  var updateButtonState = function updateButtonState() {\n    var _contentRef$current = contentRef.current,\n      scrollLeft = _contentRef$current.scrollLeft,\n      scrollWidth = _contentRef$current.scrollWidth;\n    var width = DomHandler.getWidth(contentRef.current);\n    setBackwardIsDisabledState(scrollLeft === 0);\n    setForwardIsDisabledState(parseInt(scrollLeft) === scrollWidth - width);\n  };\n  var onScroll = function onScroll(event) {\n    props.scrollable && updateButtonState();\n    event.preventDefault();\n  };\n  var getVisibleButtonWidths = function getVisibleButtonWidths() {\n    return [prevBtnRef.current, nextBtnRef.current].reduce(function (acc, el) {\n      return el ? acc + DomHandler.getWidth(el) : acc;\n    }, 0);\n  };\n  var navBackward = function navBackward() {\n    var width = DomHandler.getWidth(contentRef.current) - getVisibleButtonWidths();\n    var pos = contentRef.current.scrollLeft - width;\n    contentRef.current.scrollLeft = pos <= 0 ? 0 : pos;\n  };\n  var navForward = function navForward() {\n    var width = DomHandler.getWidth(contentRef.current) - getVisibleButtonWidths();\n    var pos = contentRef.current.scrollLeft + width;\n    var lastPos = contentRef.current.scrollWidth - width;\n    contentRef.current.scrollLeft = pos >= lastPos ? lastPos : pos;\n  };\n  var reset = function reset() {\n    setBackwardIsDisabledState(true);\n    setForwardIsDisabledState(false);\n    setHiddenTabsState([]);\n    if (props.onTabChange) {\n      props.onTabChange({\n        index: activeIndex\n      });\n    } else {\n      setActiveIndexState(props.activeIndex);\n    }\n  };\n  React.useEffect(function () {\n    updateInkBar();\n    updateButtonState();\n  });\n  useMountEffect(function () {\n    if (!idState) {\n      setIdState(UniqueComponentId());\n    }\n  });\n  useUpdateEffect(function () {\n    if (ObjectUtils.isNotEmpty(hiddenTabsState)) {\n      var tabInfo = findVisibleActiveTab(hiddenTabsState[hiddenTabsState.length - 1]);\n      tabInfo && onTabHeaderClick(null, tabInfo.tab, tabInfo.index);\n    }\n  }, [hiddenTabsState]);\n  useUpdateEffect(function () {\n    if (props.activeIndex !== activeIndexState) {\n      updateScrollBar({\n        index: props.activeIndex\n      });\n    }\n  }, [props.activeIndex]);\n  React.useImperativeHandle(ref, function () {\n    return {\n      props: props,\n      reset: reset,\n      getElement: function getElement() {\n        return elementRef.current;\n      }\n    };\n  });\n  var onCloseIconKeyDown = function onCloseIconKeyDown(event, index) {\n    event.preventDefault();\n    event.stopPropagation();\n    switch (event.code) {\n      case 'Space':\n      case 'NumpadEnter':\n      case 'Enter':\n        onTabHeaderClose(event, index);\n        break;\n    }\n  };\n  var createTabHeader = function createTabHeader(tab, index) {\n    var selected = isSelected(index);\n    var _TabPanelBase$getCPro = TabPanelBase.getCProps(tab),\n      headerStyle = _TabPanelBase$getCPro.headerStyle,\n      headerClassName = _TabPanelBase$getCPro.headerClassName,\n      _style = _TabPanelBase$getCPro.style,\n      _className = _TabPanelBase$getCPro.className,\n      disabled = _TabPanelBase$getCPro.disabled,\n      leftIcon = _TabPanelBase$getCPro.leftIcon,\n      rightIcon = _TabPanelBase$getCPro.rightIcon,\n      header = _TabPanelBase$getCPro.header,\n      headerTemplate = _TabPanelBase$getCPro.headerTemplate,\n      closable = _TabPanelBase$getCPro.closable,\n      closeIcon = _TabPanelBase$getCPro.closeIcon;\n    var headerId = idState + '_header_' + index;\n    var ariaControls = idState + index + '_content';\n    var tabIndex = disabled || !selected ? -1 : 0;\n    var leftIconElement = leftIcon && IconUtils.getJSXIcon(leftIcon, undefined, {\n      props: props\n    });\n    var headerTitleProps = mergeProps({\n      className: cx('tab.headertitle')\n    }, getTabPT(tab, 'headertitle', index));\n    var titleElement = /*#__PURE__*/React.createElement(\"span\", headerTitleProps, header);\n    var rightIconElement = rightIcon && IconUtils.getJSXIcon(rightIcon, undefined, {\n      props: props\n    });\n    var closeIconProps = mergeProps({\n      className: cx('tab.closeIcon'),\n      onClick: function onClick(e) {\n        return onTabHeaderClose(e, index);\n      },\n      onKeyDown: function onKeyDown(e) {\n        return onCloseIconKeyDown(e, index);\n      },\n      tabIndex: 0,\n      'aria-label': ariaLabel('close') || 'Close'\n    }, getTabPT(tab, 'closeIcon', index));\n    var icon = closeIcon || /*#__PURE__*/React.createElement(TimesIcon, closeIconProps);\n    var closableIconElement = closable ? IconUtils.getJSXIcon(icon, _objectSpread({}, closeIconProps), {\n      props: props\n    }) : null;\n    var headerActionProps = mergeProps({\n      id: headerId,\n      role: 'tab',\n      className: cx('tab.headeraction'),\n      tabIndex: tabIndex,\n      'aria-controls': ariaControls,\n      'aria-selected': selected,\n      'aria-disabled': disabled,\n      onClick: function onClick(e) {\n        return onTabHeaderClick(e, tab, index);\n      },\n      onKeyDown: function onKeyDown(e) {\n        return _onKeyDown(e, tab, index);\n      }\n    }, getTabPT(tab, 'headeraction', index));\n    var content = /*#__PURE__*/\n    // eslint-disable /\n    React.createElement(\"a\", headerActionProps, leftIconElement, titleElement, rightIconElement, closableIconElement, /*#__PURE__*/React.createElement(Ripple, null))\n    // eslint-enable /\n    ;\n    if (headerTemplate) {\n      var defaultContentOptions = {\n        className: 'p-tabview-nav-link',\n        titleClassName: 'p-tabview-title',\n        onClick: function onClick(e) {\n          return onTabHeaderClick(e, tab, index);\n        },\n        onKeyDown: function onKeyDown(e) {\n          return _onKeyDown(e, tab, index);\n        },\n        leftIconElement: leftIconElement,\n        titleElement: titleElement,\n        rightIconElement: rightIconElement,\n        element: content,\n        props: props,\n        index: index,\n        selected: selected,\n        ariaControls: ariaControls\n      };\n      content = ObjectUtils.getJSXElement(headerTemplate, defaultContentOptions);\n    }\n    var headerProps = mergeProps({\n      ref: function ref(el) {\n        return tabsRef.current[\"tab_\".concat(index)] = el;\n      },\n      className: cx('tab.header', {\n        selected: selected,\n        disabled: disabled,\n        headerClassName: headerClassName,\n        _className: _className\n      }),\n      style: sx('tab.header', {\n        headerStyle: headerStyle,\n        _style: _style\n      }),\n      role: 'presentation'\n    }, getTabPT(tab, 'root', index), getTabPT(tab, 'header', index));\n    return /*#__PURE__*/React.createElement(\"li\", headerProps, content);\n  };\n  var createTabHeaders = function createTabHeaders() {\n    return React.Children.map(props.children, function (tab, index) {\n      if (shouldUseTab(tab)) {\n        return createTabHeader(tab, index);\n      }\n    });\n  };\n  var createNavigator = function createNavigator() {\n    var headers = createTabHeaders();\n    var navContentProps = mergeProps({\n      id: idState + '_navcontent',\n      ref: contentRef,\n      className: cx('navcontent'),\n      style: props.style,\n      onScroll: onScroll\n    }, ptm('navcontent'));\n    var navProps = mergeProps({\n      ref: navRef,\n      className: cx('nav'),\n      role: 'tablist'\n    }, ptm('nav'));\n    var inkbarProps = mergeProps({\n      ref: inkbarRef,\n      'aria-hidden': 'true',\n      role: 'presentation',\n      className: cx('inkbar')\n    }, ptm('inkbar'));\n    return /*#__PURE__*/React.createElement(\"div\", navContentProps, /*#__PURE__*/React.createElement(\"ul\", navProps, headers, /*#__PURE__*/React.createElement(\"li\", inkbarProps)));\n  };\n  var createContent = function createContent() {\n    var panelContainerProps = mergeProps({\n      className: cx('panelcontainer'),\n      style: props.panelContainerStyle\n    }, ptm('panelcontainer'));\n    var contents = React.Children.map(props.children, function (tab, index) {\n      if (shouldUseTab(tab) && (!props.renderActiveOnly || isSelected(index))) {\n        var selected = isSelected(index);\n        var contentId = idState + index + '_content';\n        var ariaLabelledBy = idState + '_header_' + index;\n        var contentProps = mergeProps({\n          id: contentId,\n          className: cx('tab.content', {\n            props: props,\n            selected: selected,\n            getTabProp: getTabProp,\n            tab: tab,\n            isSelected: isSelected,\n            shouldUseTab: shouldUseTab,\n            index: index\n          }),\n          style: sx('tab.content', {\n            props: props,\n            getTabProp: getTabProp,\n            tab: tab,\n            isSelected: isSelected,\n            shouldUseTab: shouldUseTab,\n            index: index\n          }),\n          role: 'tabpanel',\n          'aria-labelledby': ariaLabelledBy\n        }, TabPanelBase.getCOtherProps(tab), getTabPT(tab, 'root', index), getTabPT(tab, 'content', index));\n        return /*#__PURE__*/React.createElement(\"div\", contentProps, !props.renderActiveOnly ? getTabProp(tab, 'children') : selected && getTabProp(tab, 'children'));\n      }\n    });\n    return /*#__PURE__*/React.createElement(\"div\", panelContainerProps, contents);\n  };\n  var createPrevButton = function createPrevButton() {\n    var prevIconProps = mergeProps({\n      'aria-hidden': 'true'\n    }, ptm('previcon'));\n    var icon = props.prevButton || /*#__PURE__*/React.createElement(ChevronLeftIcon, prevIconProps);\n    var leftIcon = IconUtils.getJSXIcon(icon, _objectSpread({}, prevIconProps), {\n      props: props\n    });\n    var prevButtonProps = mergeProps({\n      ref: prevBtnRef,\n      type: 'button',\n      className: cx('prevbutton'),\n      'aria-label': ariaLabel('prevPageLabel'),\n      onClick: function onClick(e) {\n        return navBackward();\n      }\n    }, ptm('prevbutton'));\n    if (props.scrollable && !backwardIsDisabledState) {\n      return /*#__PURE__*/React.createElement(\"button\", prevButtonProps, leftIcon, /*#__PURE__*/React.createElement(Ripple, null));\n    }\n    return null;\n  };\n  var createNextButton = function createNextButton() {\n    var nextIconProps = mergeProps({\n      'aria-hidden': 'true'\n    }, ptm('nexticon'));\n    var icon = props.nextButton || /*#__PURE__*/React.createElement(ChevronRightIcon, nextIconProps);\n    var rightIcon = IconUtils.getJSXIcon(icon, _objectSpread({}, nextIconProps), {\n      props: props\n    });\n    var nextButtonProps = mergeProps({\n      ref: nextBtnRef,\n      type: 'button',\n      className: cx('nextbutton'),\n      'aria-label': ariaLabel('nextPageLabel'),\n      onClick: function onClick(e) {\n        return navForward();\n      }\n    }, ptm('nextbutton'));\n    if (props.scrollable && !forwardIsDisabledState) {\n      return /*#__PURE__*/React.createElement(\"button\", nextButtonProps, rightIcon, /*#__PURE__*/React.createElement(Ripple, null));\n    }\n  };\n  var rootProps = mergeProps({\n    id: idState,\n    ref: elementRef,\n    style: props.style,\n    className: classNames(props.className, cx('root'))\n  }, TabViewBase.getOtherProps(props), ptm('root'));\n  var navContainerProps = mergeProps({\n    className: cx('navcontainer')\n  }, ptm('navcontainer'));\n  var navigator = createNavigator();\n  var content = createContent();\n  var prevButton = createPrevButton();\n  var nextButton = createNextButton();\n  return /*#__PURE__*/React.createElement(\"div\", rootProps, /*#__PURE__*/React.createElement(\"div\", navContainerProps, prevButton, navigator, nextButton), content);\n});\nTabPanel.displayName = 'TabPanel';\nTabView.displayName = 'TabView';\nexport { TabPanel, TabView };", "map": {"version": 3, "names": ["React", "PrimeReactContext", "aria<PERSON><PERSON><PERSON>", "ComponentBase", "useHandleStyle", "useMergeProps", "useMountEffect", "useUpdateEffect", "ChevronLeftIcon", "ChevronRightIcon", "TimesIcon", "<PERSON><PERSON><PERSON>", "ObjectUtils", "classNames", "UniqueComponentId", "<PERSON><PERSON><PERSON><PERSON>", "IconUtils", "_arrayLikeToArray", "r", "a", "length", "e", "n", "Array", "_arrayWithoutHoles", "isArray", "_iterableToArray", "Symbol", "iterator", "from", "_unsupportedIterableToArray", "t", "toString", "call", "slice", "constructor", "name", "test", "_nonIterableSpread", "TypeError", "_toConsumableArray", "_typeof", "o", "prototype", "toPrimitive", "i", "String", "Number", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_defineProperty", "Object", "defineProperty", "value", "enumerable", "configurable", "writable", "_arrayWithHoles", "_iterableToArrayLimit", "l", "u", "f", "next", "done", "push", "_nonIterableRest", "_slicedToArray", "ownKeys$1", "keys", "getOwnPropertySymbols", "filter", "getOwnPropertyDescriptor", "apply", "_objectSpread$1", "arguments", "for<PERSON>ach", "getOwnPropertyDescriptors", "defineProperties", "classes", "navcontent", "nav", "inkbar", "panelcontainer", "_ref", "props", "panelContainerClassName", "prevbutton", "nextbutton", "root", "_ref2", "scrollable", "navcontainer", "tab", "header", "_ref3", "selected", "disabled", "headerClassName", "_className", "headertitle", "headeraction", "closeIcon", "content", "_ref4", "getTabProp", "isSelected", "shouldUseTab", "index", "renderActiveOnly", "undefined", "inlineStyles", "_ref5", "headerStyle", "_style", "_ref6", "TabViewBase", "extend", "defaultProps", "__TYPE", "id", "activeIndex", "className", "onBeforeTabChange", "onBeforeTabClose", "onTabChange", "onTabClose", "panelContainerStyle", "style", "children", "css", "TabPanelBase", "closable", "contentClassName", "contentStyle", "headerTemplate", "leftIcon", "nextButton", "prevButton", "rightIcon", "visible", "getCProp", "getComponentProp", "getCProps", "getComponentProps", "getCOtherProps", "getComponentDiffProps", "ownKeys", "_objectSpread", "TabPanel", "TabView", "forwardRef", "inProps", "ref", "mergeProps", "context", "useContext", "getProps", "_React$useState", "useState", "_React$useState2", "idState", "setIdState", "_React$useState3", "_React$useState4", "backwardIsDisabledState", "setBackwardIsDisabledState", "_React$useState5", "_React$useState6", "forwardIsDisabledState", "setForwardIsDisabledState", "_React$useState7", "_React$useState8", "hiddenTabsState", "setHiddenTabsState", "_React$useState9", "_React$useState10", "activeIndexState", "setActiveIndexState", "elementRef", "useRef", "contentRef", "navRef", "inkbarRef", "prevBtnRef", "nextBtnRef", "tabsRef", "count", "Children", "metaData", "state", "isPrevButtonDisabled", "isNextButtonDisabled", "_TabViewBase$setMetaD", "setMetaData", "ptm", "ptmo", "cx", "sx", "isUnstyled", "styles", "getTabPT", "key", "tabMetaData", "parent", "first", "last", "active", "concat", "tabpanel", "is<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "every", "_i", "findVisibleActiveTab", "tabsInfo", "map", "find", "reverse", "onTabHeaderClose", "event", "preventDefault", "originalEvent", "onTabHeaderClick", "changeActiveIndex", "updateScrollBar", "_onKeyDown", "onKeyDown", "code", "onTabArrowLeftKey", "onTabArrowRightKey", "onTabHomeKey", "onTabEndKey", "onPageDownKey", "onPageUpKey", "onTabEnterKey", "nextHeaderAction", "_findNextHeaderAction", "target", "parentElement", "changeFocusedTab", "prevHeaderAction", "_findPrevHeaderAction", "firstHeaderAction", "findFirstHeaderAction", "lastHeaderAction", "findLastHeaderAction", "findNextHeaderAction", "tabElement", "<PERSON><PERSON><PERSON><PERSON>", "headerElement", "nextElement<PERSON><PERSON>ling", "getAttribute", "findSingle", "findPrevHeaderAction", "previousElementSibling", "current", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "element", "focus", "updateInkBar", "tabHeader", "width", "getWidth", "left", "getOffset", "scrollIntoView", "block", "updateButtonState", "_contentRef$current", "scrollLeft", "scrollWidth", "parseInt", "onScroll", "getVisibleButtonWidths", "reduce", "acc", "el", "navBackward", "pos", "navForward", "lastPos", "reset", "useEffect", "isNotEmpty", "tabInfo", "useImperativeHandle", "getElement", "onCloseIconKeyDown", "stopPropagation", "createTabHeader", "_TabPanelBase$getCPro", "headerId", "ariaControls", "tabIndex", "leftIconElement", "getJSXIcon", "headerTitleProps", "titleElement", "createElement", "rightIconElement", "closeIconProps", "onClick", "icon", "closableIconElement", "headerActionProps", "role", "defaultContentOptions", "titleClassName", "getJSXElement", "headerProps", "createTabHeaders", "createNavigator", "headers", "navContentProps", "navProps", "inkbarProps", "createContent", "panelContainerProps", "contents", "contentId", "ariaLabelledBy", "contentProps", "createPrevButton", "prevIconProps", "prevButtonProps", "type", "createNextButton", "nextIconProps", "nextButtonProps", "rootProps", "getOtherProps", "navContainerProps", "navigator", "displayName"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/node_modules/primereact/tabview/tabview.esm.js"], "sourcesContent": ["'use client';\nimport * as React from 'react';\nimport { PrimeReactContext, ariaLabel } from 'primereact/api';\nimport { ComponentBase, useHandleStyle } from 'primereact/componentbase';\nimport { useMergeProps, useMountEffect, useUpdateEffect } from 'primereact/hooks';\nimport { ChevronLeftIcon } from 'primereact/icons/chevronleft';\nimport { ChevronRightIcon } from 'primereact/icons/chevronright';\nimport { TimesIcon } from 'primereact/icons/times';\nimport { Ripple } from 'primereact/ripple';\nimport { ObjectUtils, classNames, UniqueComponentId, DomHandler, IconUtils } from 'primereact/utils';\n\nfunction _arrayLikeToArray(r, a) {\n  (null == a || a > r.length) && (a = r.length);\n  for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e];\n  return n;\n}\n\nfunction _arrayWithoutHoles(r) {\n  if (Array.isArray(r)) return _arrayLikeToArray(r);\n}\n\nfunction _iterableToArray(r) {\n  if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r);\n}\n\nfunction _unsupportedIterableToArray(r, a) {\n  if (r) {\n    if (\"string\" == typeof r) return _arrayLikeToArray(r, a);\n    var t = {}.toString.call(r).slice(8, -1);\n    return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0;\n  }\n}\n\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nfunction _toConsumableArray(r) {\n  return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread();\n}\n\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\n\nfunction toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\n\nfunction toPropertyKey(t) {\n  var i = toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\n\nfunction _defineProperty(e, r, t) {\n  return (r = toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\n\nfunction _arrayWithHoles(r) {\n  if (Array.isArray(r)) return r;\n}\n\nfunction _iterableToArrayLimit(r, l) {\n  var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"];\n  if (null != t) {\n    var e,\n      n,\n      i,\n      u,\n      a = [],\n      f = !0,\n      o = !1;\n    try {\n      if (i = (t = t.call(r)).next, 0 === l) {\n        if (Object(t) !== t) return;\n        f = !1;\n      } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0);\n    } catch (r) {\n      o = !0, n = r;\n    } finally {\n      try {\n        if (!f && null != t[\"return\"] && (u = t[\"return\"](), Object(u) !== u)) return;\n      } finally {\n        if (o) throw n;\n      }\n    }\n    return a;\n  }\n}\n\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nfunction _slicedToArray(r, e) {\n  return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest();\n}\n\nfunction ownKeys$1(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread$1(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys$1(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$1(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nvar classes = {\n  navcontent: 'p-tabview-nav-content',\n  nav: 'p-tabview-nav',\n  inkbar: 'p-tabview-ink-bar',\n  panelcontainer: function panelcontainer(_ref) {\n    var props = _ref.props;\n    return classNames('p-tabview-panels', props.panelContainerClassName);\n  },\n  prevbutton: 'p-tabview-nav-prev p-tabview-nav-btn p-link',\n  nextbutton: 'p-tabview-nav-next p-tabview-nav-btn p-link',\n  root: function root(_ref2) {\n    var props = _ref2.props;\n    return classNames('p-tabview p-component', {\n      'p-tabview-scrollable': props.scrollable\n    });\n  },\n  navcontainer: 'p-tabview-nav-container',\n  tab: {\n    header: function header(_ref3) {\n      var selected = _ref3.selected,\n        disabled = _ref3.disabled,\n        headerClassName = _ref3.headerClassName,\n        _className = _ref3._className;\n      return classNames('p-unselectable-text', {\n        'p-tabview-selected p-highlight': selected,\n        'p-disabled': disabled\n      }, headerClassName, _className);\n    },\n    headertitle: 'p-tabview-title',\n    headeraction: 'p-tabview-nav-link',\n    closeIcon: 'p-tabview-close',\n    content: function content(_ref4) {\n      var props = _ref4.props,\n        selected = _ref4.selected,\n        getTabProp = _ref4.getTabProp,\n        tab = _ref4.tab,\n        isSelected = _ref4.isSelected,\n        shouldUseTab = _ref4.shouldUseTab,\n        index = _ref4.index;\n      return shouldUseTab(tab, index) && (!props.renderActiveOnly || isSelected(index)) ? classNames(getTabProp(tab, 'contentClassName'), getTabProp(tab, 'className'), 'p-tabview-panel', {\n        'p-hidden': !selected\n      }) : undefined;\n    }\n  }\n};\nvar inlineStyles = {\n  tab: {\n    header: function header(_ref5) {\n      var headerStyle = _ref5.headerStyle,\n        _style = _ref5._style;\n      return _objectSpread$1(_objectSpread$1({}, headerStyle || {}), _style || {});\n    },\n    content: function content(_ref6) {\n      var props = _ref6.props,\n        getTabProp = _ref6.getTabProp,\n        tab = _ref6.tab,\n        isSelected = _ref6.isSelected,\n        shouldUseTab = _ref6.shouldUseTab,\n        index = _ref6.index;\n      return shouldUseTab(tab, index) && (!props.renderActiveOnly || isSelected(index)) ? _objectSpread$1(_objectSpread$1({}, getTabProp(tab, 'contentStyle') || {}), getTabProp(tab, 'style') || {}) : undefined;\n    }\n  }\n};\nvar TabViewBase = ComponentBase.extend({\n  defaultProps: {\n    __TYPE: 'TabView',\n    id: null,\n    activeIndex: 0,\n    className: null,\n    onBeforeTabChange: null,\n    onBeforeTabClose: null,\n    onTabChange: null,\n    onTabClose: null,\n    panelContainerClassName: null,\n    panelContainerStyle: null,\n    renderActiveOnly: true,\n    scrollable: false,\n    style: null,\n    children: undefined\n  },\n  css: {\n    classes: classes,\n    inlineStyles: inlineStyles\n  }\n});\nvar TabPanelBase = ComponentBase.extend({\n  defaultProps: {\n    __TYPE: 'TabPanel',\n    children: undefined,\n    className: null,\n    closable: false,\n    closeIcon: null,\n    contentClassName: null,\n    contentStyle: null,\n    disabled: false,\n    header: null,\n    headerClassName: null,\n    headerStyle: null,\n    headerTemplate: null,\n    leftIcon: null,\n    nextButton: null,\n    prevButton: null,\n    rightIcon: null,\n    style: null,\n    visible: true\n  },\n  getCProp: function getCProp(tab, name) {\n    return ObjectUtils.getComponentProp(tab, name, TabPanelBase.defaultProps);\n  },\n  getCProps: function getCProps(tab) {\n    return ObjectUtils.getComponentProps(tab, TabPanelBase.defaultProps);\n  },\n  getCOtherProps: function getCOtherProps(tab) {\n    return ObjectUtils.getComponentDiffProps(tab, TabPanelBase.defaultProps);\n  }\n});\n\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nvar TabPanel = function TabPanel() {};\nvar TabView = /*#__PURE__*/React.forwardRef(function (inProps, ref) {\n  var mergeProps = useMergeProps();\n  var context = React.useContext(PrimeReactContext);\n  var props = TabViewBase.getProps(inProps, context);\n  var _React$useState = React.useState(props.id),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    idState = _React$useState2[0],\n    setIdState = _React$useState2[1];\n  var _React$useState3 = React.useState(true),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    backwardIsDisabledState = _React$useState4[0],\n    setBackwardIsDisabledState = _React$useState4[1];\n  var _React$useState5 = React.useState(false),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    forwardIsDisabledState = _React$useState6[0],\n    setForwardIsDisabledState = _React$useState6[1];\n  var _React$useState7 = React.useState([]),\n    _React$useState8 = _slicedToArray(_React$useState7, 2),\n    hiddenTabsState = _React$useState8[0],\n    setHiddenTabsState = _React$useState8[1];\n  var _React$useState9 = React.useState(props.activeIndex),\n    _React$useState10 = _slicedToArray(_React$useState9, 2),\n    activeIndexState = _React$useState10[0],\n    setActiveIndexState = _React$useState10[1];\n  var elementRef = React.useRef(null);\n  var contentRef = React.useRef(null);\n  var navRef = React.useRef(null);\n  var inkbarRef = React.useRef(null);\n  var prevBtnRef = React.useRef(null);\n  var nextBtnRef = React.useRef(null);\n  var tabsRef = React.useRef({});\n  var activeIndex = props.onTabChange ? props.activeIndex : activeIndexState;\n  var count = React.Children.count(props.children);\n  var metaData = {\n    props: props,\n    state: {\n      id: idState,\n      isPrevButtonDisabled: backwardIsDisabledState,\n      isNextButtonDisabled: forwardIsDisabledState,\n      hiddenTabsState: hiddenTabsState,\n      activeIndex: activeIndexState\n    }\n  };\n  var _TabViewBase$setMetaD = TabViewBase.setMetaData(_objectSpread({}, metaData)),\n    ptm = _TabViewBase$setMetaD.ptm,\n    ptmo = _TabViewBase$setMetaD.ptmo,\n    cx = _TabViewBase$setMetaD.cx,\n    sx = _TabViewBase$setMetaD.sx,\n    isUnstyled = _TabViewBase$setMetaD.isUnstyled;\n  useHandleStyle(TabViewBase.css.styles, isUnstyled, {\n    name: 'tabview'\n  });\n  var getTabPT = function getTabPT(tab, key, index) {\n    var tabMetaData = {\n      props: tab.props,\n      parent: metaData,\n      context: {\n        index: index,\n        count: count,\n        first: index === 0,\n        last: index === count - 1,\n        active: index == activeIndexState,\n        disabled: getTabProp(tab, 'disabled')\n      }\n    };\n    return mergeProps(ptm(\"tab.\".concat(key), {\n      tab: tabMetaData\n    }), ptm(\"tabpanel.\".concat(key), {\n      tabpanel: tabMetaData\n    }), ptm(\"tabpanel.\".concat(key), tabMetaData), ptmo(getTabProp(tab, 'pt'), key, tabMetaData));\n  };\n  var isSelected = function isSelected(index) {\n    return index === activeIndex;\n  };\n  var getTabProp = function getTabProp(tab, name) {\n    return TabPanelBase.getCProp(tab, name);\n  };\n  var shouldUseTab = function shouldUseTab(tab) {\n    return tab && getTabProp(tab, 'visible') && ObjectUtils.isValidChild(tab, 'TabPanel') && hiddenTabsState.every(function (_i) {\n      return _i !== tab.key;\n    });\n  };\n  var findVisibleActiveTab = function findVisibleActiveTab(i) {\n    var tabsInfo = React.Children.map(props.children, function (tab, index) {\n      if (shouldUseTab(tab)) {\n        return {\n          tab: tab,\n          index: index\n        };\n      }\n    });\n    return tabsInfo.find(function (_ref) {\n      var tab = _ref.tab,\n        index = _ref.index;\n      return !getTabProp(tab, 'disabled') && index >= i;\n    }) || tabsInfo.reverse().find(function (_ref2) {\n      var tab = _ref2.tab,\n        index = _ref2.index;\n      return !getTabProp(tab, 'disabled') && i > index;\n    });\n  };\n  var onTabHeaderClose = function onTabHeaderClose(event, index) {\n    event.preventDefault();\n    var onBeforeTabClose = props.onBeforeTabClose,\n      onTabClose = props.onTabClose,\n      children = props.children;\n    var key = children[index].key;\n\n    // give caller a chance to stop the selection\n    if (onBeforeTabClose && onBeforeTabClose({\n      originalEvent: event,\n      index: index\n    }) === false) {\n      return;\n    }\n    setHiddenTabsState([].concat(_toConsumableArray(hiddenTabsState), [key]));\n    if (onTabClose) {\n      onTabClose({\n        originalEvent: event,\n        index: index\n      });\n    }\n  };\n  var onTabHeaderClick = function onTabHeaderClick(event, tab, index) {\n    changeActiveIndex(event, tab, index);\n  };\n  var changeActiveIndex = function changeActiveIndex(event, tab, index) {\n    if (event) {\n      event.preventDefault();\n    }\n    if (!getTabProp(tab, 'disabled')) {\n      // give caller a chance to stop the selection\n      if (props.onBeforeTabChange && props.onBeforeTabChange({\n        originalEvent: event,\n        index: index\n      }) === false) {\n        return;\n      }\n      if (props.onTabChange) {\n        props.onTabChange({\n          originalEvent: event,\n          index: index\n        });\n      } else {\n        setActiveIndexState(index);\n      }\n    }\n    updateScrollBar({\n      index: index\n    });\n  };\n  var _onKeyDown = function onKeyDown(event, tab, index) {\n    switch (event.code) {\n      case 'ArrowLeft':\n        onTabArrowLeftKey(event);\n        break;\n      case 'ArrowRight':\n        onTabArrowRightKey(event);\n        break;\n      case 'Home':\n        onTabHomeKey(event);\n        break;\n      case 'End':\n        onTabEndKey(event);\n        break;\n      case 'PageDown':\n        onPageDownKey(event);\n        break;\n      case 'PageUp':\n        onPageUpKey(event);\n        break;\n      case 'Enter':\n      case 'NumpadEnter':\n      case 'Space':\n        onTabEnterKey(event, tab, index);\n        break;\n    }\n  };\n  var onTabArrowRightKey = function onTabArrowRightKey(event) {\n    var nextHeaderAction = _findNextHeaderAction(event.target.parentElement);\n    nextHeaderAction ? changeFocusedTab(nextHeaderAction) : onTabHomeKey(event);\n    event.preventDefault();\n  };\n  var onTabArrowLeftKey = function onTabArrowLeftKey(event) {\n    var prevHeaderAction = _findPrevHeaderAction(event.target.parentElement);\n    prevHeaderAction ? changeFocusedTab(prevHeaderAction) : onTabEndKey(event);\n    event.preventDefault();\n  };\n  var onTabHomeKey = function onTabHomeKey(event) {\n    var firstHeaderAction = findFirstHeaderAction();\n    changeFocusedTab(firstHeaderAction);\n    event.preventDefault();\n  };\n  var onTabEndKey = function onTabEndKey(event) {\n    var lastHeaderAction = findLastHeaderAction();\n    changeFocusedTab(lastHeaderAction);\n    event.preventDefault();\n  };\n  var onPageDownKey = function onPageDownKey(event) {\n    updateScrollBar({\n      index: React.Children.count(props.children) - 1\n    });\n    event.preventDefault();\n  };\n  var onPageUpKey = function onPageUpKey(event) {\n    updateScrollBar({\n      index: 0\n    });\n    event.preventDefault();\n  };\n  var onTabEnterKey = function onTabEnterKey(event, tab, index) {\n    changeActiveIndex(event, tab, index);\n    event.preventDefault();\n  };\n  var _findNextHeaderAction = function findNextHeaderAction(tabElement) {\n    var selfCheck = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    var headerElement = selfCheck ? tabElement : tabElement.nextElementSibling;\n    return headerElement ? DomHandler.getAttribute(headerElement, 'data-p-disabled') || DomHandler.getAttribute(headerElement, 'data-pc-section') === 'inkbar' ? _findNextHeaderAction(headerElement) : DomHandler.findSingle(headerElement, '[data-pc-section=\"headeraction\"]') : null;\n  };\n  var _findPrevHeaderAction = function findPrevHeaderAction(tabElement) {\n    var selfCheck = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    var headerElement = selfCheck ? tabElement : tabElement.previousElementSibling;\n    return headerElement ? DomHandler.getAttribute(headerElement, 'data-p-disabled') || DomHandler.getAttribute(headerElement, 'data-pc-section') === 'inkbar' ? _findPrevHeaderAction(headerElement) : DomHandler.findSingle(headerElement, '[data-pc-section=\"headeraction\"]') : null;\n  };\n  var findFirstHeaderAction = function findFirstHeaderAction() {\n    return _findNextHeaderAction(navRef.current.firstElementChild, true);\n  };\n  var findLastHeaderAction = function findLastHeaderAction() {\n    return _findPrevHeaderAction(navRef.current.lastElementChild, true);\n  };\n  var changeFocusedTab = function changeFocusedTab(element) {\n    if (element) {\n      DomHandler.focus(element);\n      updateScrollBar({\n        element: element\n      });\n    }\n  };\n  var updateInkBar = function updateInkBar() {\n    var tabHeader = tabsRef.current[\"tab_\".concat(activeIndex)];\n    inkbarRef.current.style.width = DomHandler.getWidth(tabHeader) + 'px';\n    inkbarRef.current.style.left = DomHandler.getOffset(tabHeader).left - DomHandler.getOffset(navRef.current).left + 'px';\n  };\n  var updateScrollBar = function updateScrollBar(_ref3) {\n    var index = _ref3.index,\n      element = _ref3.element;\n    var tabHeader = element || tabsRef.current[\"tab_\".concat(index)];\n    if (tabHeader && tabHeader.scrollIntoView) {\n      tabHeader.scrollIntoView({\n        block: 'nearest'\n      });\n    }\n  };\n  var updateButtonState = function updateButtonState() {\n    var _contentRef$current = contentRef.current,\n      scrollLeft = _contentRef$current.scrollLeft,\n      scrollWidth = _contentRef$current.scrollWidth;\n    var width = DomHandler.getWidth(contentRef.current);\n    setBackwardIsDisabledState(scrollLeft === 0);\n    setForwardIsDisabledState(parseInt(scrollLeft) === scrollWidth - width);\n  };\n  var onScroll = function onScroll(event) {\n    props.scrollable && updateButtonState();\n    event.preventDefault();\n  };\n  var getVisibleButtonWidths = function getVisibleButtonWidths() {\n    return [prevBtnRef.current, nextBtnRef.current].reduce(function (acc, el) {\n      return el ? acc + DomHandler.getWidth(el) : acc;\n    }, 0);\n  };\n  var navBackward = function navBackward() {\n    var width = DomHandler.getWidth(contentRef.current) - getVisibleButtonWidths();\n    var pos = contentRef.current.scrollLeft - width;\n    contentRef.current.scrollLeft = pos <= 0 ? 0 : pos;\n  };\n  var navForward = function navForward() {\n    var width = DomHandler.getWidth(contentRef.current) - getVisibleButtonWidths();\n    var pos = contentRef.current.scrollLeft + width;\n    var lastPos = contentRef.current.scrollWidth - width;\n    contentRef.current.scrollLeft = pos >= lastPos ? lastPos : pos;\n  };\n  var reset = function reset() {\n    setBackwardIsDisabledState(true);\n    setForwardIsDisabledState(false);\n    setHiddenTabsState([]);\n    if (props.onTabChange) {\n      props.onTabChange({\n        index: activeIndex\n      });\n    } else {\n      setActiveIndexState(props.activeIndex);\n    }\n  };\n  React.useEffect(function () {\n    updateInkBar();\n    updateButtonState();\n  });\n  useMountEffect(function () {\n    if (!idState) {\n      setIdState(UniqueComponentId());\n    }\n  });\n  useUpdateEffect(function () {\n    if (ObjectUtils.isNotEmpty(hiddenTabsState)) {\n      var tabInfo = findVisibleActiveTab(hiddenTabsState[hiddenTabsState.length - 1]);\n      tabInfo && onTabHeaderClick(null, tabInfo.tab, tabInfo.index);\n    }\n  }, [hiddenTabsState]);\n  useUpdateEffect(function () {\n    if (props.activeIndex !== activeIndexState) {\n      updateScrollBar({\n        index: props.activeIndex\n      });\n    }\n  }, [props.activeIndex]);\n  React.useImperativeHandle(ref, function () {\n    return {\n      props: props,\n      reset: reset,\n      getElement: function getElement() {\n        return elementRef.current;\n      }\n    };\n  });\n  var onCloseIconKeyDown = function onCloseIconKeyDown(event, index) {\n    event.preventDefault();\n    event.stopPropagation();\n    switch (event.code) {\n      case 'Space':\n      case 'NumpadEnter':\n      case 'Enter':\n        onTabHeaderClose(event, index);\n        break;\n    }\n  };\n  var createTabHeader = function createTabHeader(tab, index) {\n    var selected = isSelected(index);\n    var _TabPanelBase$getCPro = TabPanelBase.getCProps(tab),\n      headerStyle = _TabPanelBase$getCPro.headerStyle,\n      headerClassName = _TabPanelBase$getCPro.headerClassName,\n      _style = _TabPanelBase$getCPro.style,\n      _className = _TabPanelBase$getCPro.className,\n      disabled = _TabPanelBase$getCPro.disabled,\n      leftIcon = _TabPanelBase$getCPro.leftIcon,\n      rightIcon = _TabPanelBase$getCPro.rightIcon,\n      header = _TabPanelBase$getCPro.header,\n      headerTemplate = _TabPanelBase$getCPro.headerTemplate,\n      closable = _TabPanelBase$getCPro.closable,\n      closeIcon = _TabPanelBase$getCPro.closeIcon;\n    var headerId = idState + '_header_' + index;\n    var ariaControls = idState + index + '_content';\n    var tabIndex = disabled || !selected ? -1 : 0;\n    var leftIconElement = leftIcon && IconUtils.getJSXIcon(leftIcon, undefined, {\n      props: props\n    });\n    var headerTitleProps = mergeProps({\n      className: cx('tab.headertitle')\n    }, getTabPT(tab, 'headertitle', index));\n    var titleElement = /*#__PURE__*/React.createElement(\"span\", headerTitleProps, header);\n    var rightIconElement = rightIcon && IconUtils.getJSXIcon(rightIcon, undefined, {\n      props: props\n    });\n    var closeIconProps = mergeProps({\n      className: cx('tab.closeIcon'),\n      onClick: function onClick(e) {\n        return onTabHeaderClose(e, index);\n      },\n      onKeyDown: function onKeyDown(e) {\n        return onCloseIconKeyDown(e, index);\n      },\n      tabIndex: 0,\n      'aria-label': ariaLabel('close') || 'Close'\n    }, getTabPT(tab, 'closeIcon', index));\n    var icon = closeIcon || /*#__PURE__*/React.createElement(TimesIcon, closeIconProps);\n    var closableIconElement = closable ? IconUtils.getJSXIcon(icon, _objectSpread({}, closeIconProps), {\n      props: props\n    }) : null;\n    var headerActionProps = mergeProps({\n      id: headerId,\n      role: 'tab',\n      className: cx('tab.headeraction'),\n      tabIndex: tabIndex,\n      'aria-controls': ariaControls,\n      'aria-selected': selected,\n      'aria-disabled': disabled,\n      onClick: function onClick(e) {\n        return onTabHeaderClick(e, tab, index);\n      },\n      onKeyDown: function onKeyDown(e) {\n        return _onKeyDown(e, tab, index);\n      }\n    }, getTabPT(tab, 'headeraction', index));\n    var content =\n    /*#__PURE__*/\n    // eslint-disable /\n    React.createElement(\"a\", headerActionProps, leftIconElement, titleElement, rightIconElement, closableIconElement, /*#__PURE__*/React.createElement(Ripple, null))\n    // eslint-enable /\n    ;\n    if (headerTemplate) {\n      var defaultContentOptions = {\n        className: 'p-tabview-nav-link',\n        titleClassName: 'p-tabview-title',\n        onClick: function onClick(e) {\n          return onTabHeaderClick(e, tab, index);\n        },\n        onKeyDown: function onKeyDown(e) {\n          return _onKeyDown(e, tab, index);\n        },\n        leftIconElement: leftIconElement,\n        titleElement: titleElement,\n        rightIconElement: rightIconElement,\n        element: content,\n        props: props,\n        index: index,\n        selected: selected,\n        ariaControls: ariaControls\n      };\n      content = ObjectUtils.getJSXElement(headerTemplate, defaultContentOptions);\n    }\n    var headerProps = mergeProps({\n      ref: function ref(el) {\n        return tabsRef.current[\"tab_\".concat(index)] = el;\n      },\n      className: cx('tab.header', {\n        selected: selected,\n        disabled: disabled,\n        headerClassName: headerClassName,\n        _className: _className\n      }),\n      style: sx('tab.header', {\n        headerStyle: headerStyle,\n        _style: _style\n      }),\n      role: 'presentation'\n    }, getTabPT(tab, 'root', index), getTabPT(tab, 'header', index));\n    return /*#__PURE__*/React.createElement(\"li\", headerProps, content);\n  };\n  var createTabHeaders = function createTabHeaders() {\n    return React.Children.map(props.children, function (tab, index) {\n      if (shouldUseTab(tab)) {\n        return createTabHeader(tab, index);\n      }\n    });\n  };\n  var createNavigator = function createNavigator() {\n    var headers = createTabHeaders();\n    var navContentProps = mergeProps({\n      id: idState + '_navcontent',\n      ref: contentRef,\n      className: cx('navcontent'),\n      style: props.style,\n      onScroll: onScroll\n    }, ptm('navcontent'));\n    var navProps = mergeProps({\n      ref: navRef,\n      className: cx('nav'),\n      role: 'tablist'\n    }, ptm('nav'));\n    var inkbarProps = mergeProps({\n      ref: inkbarRef,\n      'aria-hidden': 'true',\n      role: 'presentation',\n      className: cx('inkbar')\n    }, ptm('inkbar'));\n    return /*#__PURE__*/React.createElement(\"div\", navContentProps, /*#__PURE__*/React.createElement(\"ul\", navProps, headers, /*#__PURE__*/React.createElement(\"li\", inkbarProps)));\n  };\n  var createContent = function createContent() {\n    var panelContainerProps = mergeProps({\n      className: cx('panelcontainer'),\n      style: props.panelContainerStyle\n    }, ptm('panelcontainer'));\n    var contents = React.Children.map(props.children, function (tab, index) {\n      if (shouldUseTab(tab) && (!props.renderActiveOnly || isSelected(index))) {\n        var selected = isSelected(index);\n        var contentId = idState + index + '_content';\n        var ariaLabelledBy = idState + '_header_' + index;\n        var contentProps = mergeProps({\n          id: contentId,\n          className: cx('tab.content', {\n            props: props,\n            selected: selected,\n            getTabProp: getTabProp,\n            tab: tab,\n            isSelected: isSelected,\n            shouldUseTab: shouldUseTab,\n            index: index\n          }),\n          style: sx('tab.content', {\n            props: props,\n            getTabProp: getTabProp,\n            tab: tab,\n            isSelected: isSelected,\n            shouldUseTab: shouldUseTab,\n            index: index\n          }),\n          role: 'tabpanel',\n          'aria-labelledby': ariaLabelledBy\n        }, TabPanelBase.getCOtherProps(tab), getTabPT(tab, 'root', index), getTabPT(tab, 'content', index));\n        return /*#__PURE__*/React.createElement(\"div\", contentProps, !props.renderActiveOnly ? getTabProp(tab, 'children') : selected && getTabProp(tab, 'children'));\n      }\n    });\n    return /*#__PURE__*/React.createElement(\"div\", panelContainerProps, contents);\n  };\n  var createPrevButton = function createPrevButton() {\n    var prevIconProps = mergeProps({\n      'aria-hidden': 'true'\n    }, ptm('previcon'));\n    var icon = props.prevButton || /*#__PURE__*/React.createElement(ChevronLeftIcon, prevIconProps);\n    var leftIcon = IconUtils.getJSXIcon(icon, _objectSpread({}, prevIconProps), {\n      props: props\n    });\n    var prevButtonProps = mergeProps({\n      ref: prevBtnRef,\n      type: 'button',\n      className: cx('prevbutton'),\n      'aria-label': ariaLabel('prevPageLabel'),\n      onClick: function onClick(e) {\n        return navBackward();\n      }\n    }, ptm('prevbutton'));\n    if (props.scrollable && !backwardIsDisabledState) {\n      return /*#__PURE__*/React.createElement(\"button\", prevButtonProps, leftIcon, /*#__PURE__*/React.createElement(Ripple, null));\n    }\n    return null;\n  };\n  var createNextButton = function createNextButton() {\n    var nextIconProps = mergeProps({\n      'aria-hidden': 'true'\n    }, ptm('nexticon'));\n    var icon = props.nextButton || /*#__PURE__*/React.createElement(ChevronRightIcon, nextIconProps);\n    var rightIcon = IconUtils.getJSXIcon(icon, _objectSpread({}, nextIconProps), {\n      props: props\n    });\n    var nextButtonProps = mergeProps({\n      ref: nextBtnRef,\n      type: 'button',\n      className: cx('nextbutton'),\n      'aria-label': ariaLabel('nextPageLabel'),\n      onClick: function onClick(e) {\n        return navForward();\n      }\n    }, ptm('nextbutton'));\n    if (props.scrollable && !forwardIsDisabledState) {\n      return /*#__PURE__*/React.createElement(\"button\", nextButtonProps, rightIcon, /*#__PURE__*/React.createElement(Ripple, null));\n    }\n  };\n  var rootProps = mergeProps({\n    id: idState,\n    ref: elementRef,\n    style: props.style,\n    className: classNames(props.className, cx('root'))\n  }, TabViewBase.getOtherProps(props), ptm('root'));\n  var navContainerProps = mergeProps({\n    className: cx('navcontainer')\n  }, ptm('navcontainer'));\n  var navigator = createNavigator();\n  var content = createContent();\n  var prevButton = createPrevButton();\n  var nextButton = createNextButton();\n  return /*#__PURE__*/React.createElement(\"div\", rootProps, /*#__PURE__*/React.createElement(\"div\", navContainerProps, prevButton, navigator, nextButton), content);\n});\nTabPanel.displayName = 'TabPanel';\nTabView.displayName = 'TabView';\n\nexport { TabPanel, TabView };\n"], "mappings": "AAAA,YAAY;;AACZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,iBAAiB,EAAEC,SAAS,QAAQ,gBAAgB;AAC7D,SAASC,aAAa,EAAEC,cAAc,QAAQ,0BAA0B;AACxE,SAASC,aAAa,EAAEC,cAAc,EAAEC,eAAe,QAAQ,kBAAkB;AACjF,SAASC,eAAe,QAAQ,8BAA8B;AAC9D,SAASC,gBAAgB,QAAQ,+BAA+B;AAChE,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,WAAW,EAAEC,UAAU,EAAEC,iBAAiB,EAAEC,UAAU,EAAEC,SAAS,QAAQ,kBAAkB;AAEpG,SAASC,iBAAiBA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAC/B,CAAC,IAAI,IAAIA,CAAC,IAAIA,CAAC,GAAGD,CAAC,CAACE,MAAM,MAAMD,CAAC,GAAGD,CAAC,CAACE,MAAM,CAAC;EAC7C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGC,KAAK,CAACJ,CAAC,CAAC,EAAEE,CAAC,GAAGF,CAAC,EAAEE,CAAC,EAAE,EAAEC,CAAC,CAACD,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACrD,OAAOC,CAAC;AACV;AAEA,SAASE,kBAAkBA,CAACN,CAAC,EAAE;EAC7B,IAAIK,KAAK,CAACE,OAAO,CAACP,CAAC,CAAC,EAAE,OAAOD,iBAAiB,CAACC,CAAC,CAAC;AACnD;AAEA,SAASQ,gBAAgBA,CAACR,CAAC,EAAE;EAC3B,IAAI,WAAW,IAAI,OAAOS,MAAM,IAAI,IAAI,IAAIT,CAAC,CAACS,MAAM,CAACC,QAAQ,CAAC,IAAI,IAAI,IAAIV,CAAC,CAAC,YAAY,CAAC,EAAE,OAAOK,KAAK,CAACM,IAAI,CAACX,CAAC,CAAC;AACjH;AAEA,SAASY,2BAA2BA,CAACZ,CAAC,EAAEC,CAAC,EAAE;EACzC,IAAID,CAAC,EAAE;IACL,IAAI,QAAQ,IAAI,OAAOA,CAAC,EAAE,OAAOD,iBAAiB,CAACC,CAAC,EAAEC,CAAC,CAAC;IACxD,IAAIY,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAACC,IAAI,CAACf,CAAC,CAAC,CAACgB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACxC,OAAO,QAAQ,KAAKH,CAAC,IAAIb,CAAC,CAACiB,WAAW,KAAKJ,CAAC,GAAGb,CAAC,CAACiB,WAAW,CAACC,IAAI,CAAC,EAAE,KAAK,KAAKL,CAAC,IAAI,KAAK,KAAKA,CAAC,GAAGR,KAAK,CAACM,IAAI,CAACX,CAAC,CAAC,GAAG,WAAW,KAAKa,CAAC,IAAI,0CAA0C,CAACM,IAAI,CAACN,CAAC,CAAC,GAAGd,iBAAiB,CAACC,CAAC,EAAEC,CAAC,CAAC,GAAG,KAAK,CAAC;EAC7N;AACF;AAEA,SAASmB,kBAAkBA,CAAA,EAAG;EAC5B,MAAM,IAAIC,SAAS,CAAC,sIAAsI,CAAC;AAC7J;AAEA,SAASC,kBAAkBA,CAACtB,CAAC,EAAE;EAC7B,OAAOM,kBAAkB,CAACN,CAAC,CAAC,IAAIQ,gBAAgB,CAACR,CAAC,CAAC,IAAIY,2BAA2B,CAACZ,CAAC,CAAC,IAAIoB,kBAAkB,CAAC,CAAC;AAC/G;AAEA,SAASG,OAAOA,CAACC,CAAC,EAAE;EAClB,yBAAyB;;EAEzB,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOd,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUc,CAAC,EAAE;IAChG,OAAO,OAAOA,CAAC;EACjB,CAAC,GAAG,UAAUA,CAAC,EAAE;IACf,OAAOA,CAAC,IAAI,UAAU,IAAI,OAAOf,MAAM,IAAIe,CAAC,CAACP,WAAW,KAAKR,MAAM,IAAIe,CAAC,KAAKf,MAAM,CAACgB,SAAS,GAAG,QAAQ,GAAG,OAAOD,CAAC;EACrH,CAAC,EAAED,OAAO,CAACC,CAAC,CAAC;AACf;AAEA,SAASE,WAAWA,CAACb,CAAC,EAAEb,CAAC,EAAE;EACzB,IAAI,QAAQ,IAAIuB,OAAO,CAACV,CAAC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAC1C,IAAIV,CAAC,GAAGU,CAAC,CAACJ,MAAM,CAACiB,WAAW,CAAC;EAC7B,IAAI,KAAK,CAAC,KAAKvB,CAAC,EAAE;IAChB,IAAIwB,CAAC,GAAGxB,CAAC,CAACY,IAAI,CAACF,CAAC,EAAEb,CAAC,IAAI,SAAS,CAAC;IACjC,IAAI,QAAQ,IAAIuB,OAAO,CAACI,CAAC,CAAC,EAAE,OAAOA,CAAC;IACpC,MAAM,IAAIN,SAAS,CAAC,8CAA8C,CAAC;EACrE;EACA,OAAO,CAAC,QAAQ,KAAKrB,CAAC,GAAG4B,MAAM,GAAGC,MAAM,EAAEhB,CAAC,CAAC;AAC9C;AAEA,SAASiB,aAAaA,CAACjB,CAAC,EAAE;EACxB,IAAIc,CAAC,GAAGD,WAAW,CAACb,CAAC,EAAE,QAAQ,CAAC;EAChC,OAAO,QAAQ,IAAIU,OAAO,CAACI,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAC5C;AAEA,SAASI,eAAeA,CAAC5B,CAAC,EAAEH,CAAC,EAAEa,CAAC,EAAE;EAChC,OAAO,CAACb,CAAC,GAAG8B,aAAa,CAAC9B,CAAC,CAAC,KAAKG,CAAC,GAAG6B,MAAM,CAACC,cAAc,CAAC9B,CAAC,EAAEH,CAAC,EAAE;IAC/DkC,KAAK,EAAErB,CAAC;IACRsB,UAAU,EAAE,CAAC,CAAC;IACdC,YAAY,EAAE,CAAC,CAAC;IAChBC,QAAQ,EAAE,CAAC;EACb,CAAC,CAAC,GAAGlC,CAAC,CAACH,CAAC,CAAC,GAAGa,CAAC,EAAEV,CAAC;AAClB;AAEA,SAASmC,eAAeA,CAACtC,CAAC,EAAE;EAC1B,IAAIK,KAAK,CAACE,OAAO,CAACP,CAAC,CAAC,EAAE,OAAOA,CAAC;AAChC;AAEA,SAASuC,qBAAqBA,CAACvC,CAAC,EAAEwC,CAAC,EAAE;EACnC,IAAI3B,CAAC,GAAG,IAAI,IAAIb,CAAC,GAAG,IAAI,GAAG,WAAW,IAAI,OAAOS,MAAM,IAAIT,CAAC,CAACS,MAAM,CAACC,QAAQ,CAAC,IAAIV,CAAC,CAAC,YAAY,CAAC;EAChG,IAAI,IAAI,IAAIa,CAAC,EAAE;IACb,IAAIV,CAAC;MACHC,CAAC;MACDuB,CAAC;MACDc,CAAC;MACDxC,CAAC,GAAG,EAAE;MACNyC,CAAC,GAAG,CAAC,CAAC;MACNlB,CAAC,GAAG,CAAC,CAAC;IACR,IAAI;MACF,IAAIG,CAAC,GAAG,CAACd,CAAC,GAAGA,CAAC,CAACE,IAAI,CAACf,CAAC,CAAC,EAAE2C,IAAI,EAAE,CAAC,KAAKH,CAAC,EAAE;QACrC,IAAIR,MAAM,CAACnB,CAAC,CAAC,KAAKA,CAAC,EAAE;QACrB6B,CAAC,GAAG,CAAC,CAAC;MACR,CAAC,MAAM,OAAO,EAAEA,CAAC,GAAG,CAACvC,CAAC,GAAGwB,CAAC,CAACZ,IAAI,CAACF,CAAC,CAAC,EAAE+B,IAAI,CAAC,KAAK3C,CAAC,CAAC4C,IAAI,CAAC1C,CAAC,CAAC+B,KAAK,CAAC,EAAEjC,CAAC,CAACC,MAAM,KAAKsC,CAAC,CAAC,EAAEE,CAAC,GAAG,CAAC,CAAC,CAAC;IACzF,CAAC,CAAC,OAAO1C,CAAC,EAAE;MACVwB,CAAC,GAAG,CAAC,CAAC,EAAEpB,CAAC,GAAGJ,CAAC;IACf,CAAC,SAAS;MACR,IAAI;QACF,IAAI,CAAC0C,CAAC,IAAI,IAAI,IAAI7B,CAAC,CAAC,QAAQ,CAAC,KAAK4B,CAAC,GAAG5B,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAEmB,MAAM,CAACS,CAAC,CAAC,KAAKA,CAAC,CAAC,EAAE;MACzE,CAAC,SAAS;QACR,IAAIjB,CAAC,EAAE,MAAMpB,CAAC;MAChB;IACF;IACA,OAAOH,CAAC;EACV;AACF;AAEA,SAAS6C,gBAAgBA,CAAA,EAAG;EAC1B,MAAM,IAAIzB,SAAS,CAAC,2IAA2I,CAAC;AAClK;AAEA,SAAS0B,cAAcA,CAAC/C,CAAC,EAAEG,CAAC,EAAE;EAC5B,OAAOmC,eAAe,CAACtC,CAAC,CAAC,IAAIuC,qBAAqB,CAACvC,CAAC,EAAEG,CAAC,CAAC,IAAIS,2BAA2B,CAACZ,CAAC,EAAEG,CAAC,CAAC,IAAI2C,gBAAgB,CAAC,CAAC;AACrH;AAEA,SAASE,SAASA,CAAC7C,CAAC,EAAEH,CAAC,EAAE;EAAE,IAAIa,CAAC,GAAGmB,MAAM,CAACiB,IAAI,CAAC9C,CAAC,CAAC;EAAE,IAAI6B,MAAM,CAACkB,qBAAqB,EAAE;IAAE,IAAI1B,CAAC,GAAGQ,MAAM,CAACkB,qBAAqB,CAAC/C,CAAC,CAAC;IAAEH,CAAC,KAAKwB,CAAC,GAAGA,CAAC,CAAC2B,MAAM,CAAC,UAAUnD,CAAC,EAAE;MAAE,OAAOgC,MAAM,CAACoB,wBAAwB,CAACjD,CAAC,EAAEH,CAAC,CAAC,CAACmC,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEtB,CAAC,CAACgC,IAAI,CAACQ,KAAK,CAACxC,CAAC,EAAEW,CAAC,CAAC;EAAE;EAAE,OAAOX,CAAC;AAAE;AAChQ,SAASyC,eAAeA,CAACnD,CAAC,EAAE;EAAE,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuD,SAAS,CAACrD,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAIa,CAAC,GAAG,IAAI,IAAI0C,SAAS,CAACvD,CAAC,CAAC,GAAGuD,SAAS,CAACvD,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGgD,SAAS,CAAChB,MAAM,CAACnB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC2C,OAAO,CAAC,UAAUxD,CAAC,EAAE;MAAE+B,eAAe,CAAC5B,CAAC,EAAEH,CAAC,EAAEa,CAAC,CAACb,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGgC,MAAM,CAACyB,yBAAyB,GAAGzB,MAAM,CAAC0B,gBAAgB,CAACvD,CAAC,EAAE6B,MAAM,CAACyB,yBAAyB,CAAC5C,CAAC,CAAC,CAAC,GAAGmC,SAAS,CAAChB,MAAM,CAACnB,CAAC,CAAC,CAAC,CAAC2C,OAAO,CAAC,UAAUxD,CAAC,EAAE;MAAEgC,MAAM,CAACC,cAAc,CAAC9B,CAAC,EAAEH,CAAC,EAAEgC,MAAM,CAACoB,wBAAwB,CAACvC,CAAC,EAAEb,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOG,CAAC;AAAE;AAC5b,IAAIwD,OAAO,GAAG;EACZC,UAAU,EAAE,uBAAuB;EACnCC,GAAG,EAAE,eAAe;EACpBC,MAAM,EAAE,mBAAmB;EAC3BC,cAAc,EAAE,SAASA,cAAcA,CAACC,IAAI,EAAE;IAC5C,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;IACtB,OAAOtE,UAAU,CAAC,kBAAkB,EAAEsE,KAAK,CAACC,uBAAuB,CAAC;EACtE,CAAC;EACDC,UAAU,EAAE,6CAA6C;EACzDC,UAAU,EAAE,6CAA6C;EACzDC,IAAI,EAAE,SAASA,IAAIA,CAACC,KAAK,EAAE;IACzB,IAAIL,KAAK,GAAGK,KAAK,CAACL,KAAK;IACvB,OAAOtE,UAAU,CAAC,uBAAuB,EAAE;MACzC,sBAAsB,EAAEsE,KAAK,CAACM;IAChC,CAAC,CAAC;EACJ,CAAC;EACDC,YAAY,EAAE,yBAAyB;EACvCC,GAAG,EAAE;IACHC,MAAM,EAAE,SAASA,MAAMA,CAACC,KAAK,EAAE;MAC7B,IAAIC,QAAQ,GAAGD,KAAK,CAACC,QAAQ;QAC3BC,QAAQ,GAAGF,KAAK,CAACE,QAAQ;QACzBC,eAAe,GAAGH,KAAK,CAACG,eAAe;QACvCC,UAAU,GAAGJ,KAAK,CAACI,UAAU;MAC/B,OAAOpF,UAAU,CAAC,qBAAqB,EAAE;QACvC,gCAAgC,EAAEiF,QAAQ;QAC1C,YAAY,EAAEC;MAChB,CAAC,EAAEC,eAAe,EAAEC,UAAU,CAAC;IACjC,CAAC;IACDC,WAAW,EAAE,iBAAiB;IAC9BC,YAAY,EAAE,oBAAoB;IAClCC,SAAS,EAAE,iBAAiB;IAC5BC,OAAO,EAAE,SAASA,OAAOA,CAACC,KAAK,EAAE;MAC/B,IAAInB,KAAK,GAAGmB,KAAK,CAACnB,KAAK;QACrBW,QAAQ,GAAGQ,KAAK,CAACR,QAAQ;QACzBS,UAAU,GAAGD,KAAK,CAACC,UAAU;QAC7BZ,GAAG,GAAGW,KAAK,CAACX,GAAG;QACfa,UAAU,GAAGF,KAAK,CAACE,UAAU;QAC7BC,YAAY,GAAGH,KAAK,CAACG,YAAY;QACjCC,KAAK,GAAGJ,KAAK,CAACI,KAAK;MACrB,OAAOD,YAAY,CAACd,GAAG,EAAEe,KAAK,CAAC,KAAK,CAACvB,KAAK,CAACwB,gBAAgB,IAAIH,UAAU,CAACE,KAAK,CAAC,CAAC,GAAG7F,UAAU,CAAC0F,UAAU,CAACZ,GAAG,EAAE,kBAAkB,CAAC,EAAEY,UAAU,CAACZ,GAAG,EAAE,WAAW,CAAC,EAAE,iBAAiB,EAAE;QACnL,UAAU,EAAE,CAACG;MACf,CAAC,CAAC,GAAGc,SAAS;IAChB;EACF;AACF,CAAC;AACD,IAAIC,YAAY,GAAG;EACjBlB,GAAG,EAAE;IACHC,MAAM,EAAE,SAASA,MAAMA,CAACkB,KAAK,EAAE;MAC7B,IAAIC,WAAW,GAAGD,KAAK,CAACC,WAAW;QACjCC,MAAM,GAAGF,KAAK,CAACE,MAAM;MACvB,OAAOxC,eAAe,CAACA,eAAe,CAAC,CAAC,CAAC,EAAEuC,WAAW,IAAI,CAAC,CAAC,CAAC,EAAEC,MAAM,IAAI,CAAC,CAAC,CAAC;IAC9E,CAAC;IACDX,OAAO,EAAE,SAASA,OAAOA,CAACY,KAAK,EAAE;MAC/B,IAAI9B,KAAK,GAAG8B,KAAK,CAAC9B,KAAK;QACrBoB,UAAU,GAAGU,KAAK,CAACV,UAAU;QAC7BZ,GAAG,GAAGsB,KAAK,CAACtB,GAAG;QACfa,UAAU,GAAGS,KAAK,CAACT,UAAU;QAC7BC,YAAY,GAAGQ,KAAK,CAACR,YAAY;QACjCC,KAAK,GAAGO,KAAK,CAACP,KAAK;MACrB,OAAOD,YAAY,CAACd,GAAG,EAAEe,KAAK,CAAC,KAAK,CAACvB,KAAK,CAACwB,gBAAgB,IAAIH,UAAU,CAACE,KAAK,CAAC,CAAC,GAAGlC,eAAe,CAACA,eAAe,CAAC,CAAC,CAAC,EAAE+B,UAAU,CAACZ,GAAG,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAEY,UAAU,CAACZ,GAAG,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,GAAGiB,SAAS;IAC7M;EACF;AACF,CAAC;AACD,IAAIM,WAAW,GAAG/G,aAAa,CAACgH,MAAM,CAAC;EACrCC,YAAY,EAAE;IACZC,MAAM,EAAE,SAAS;IACjBC,EAAE,EAAE,IAAI;IACRC,WAAW,EAAE,CAAC;IACdC,SAAS,EAAE,IAAI;IACfC,iBAAiB,EAAE,IAAI;IACvBC,gBAAgB,EAAE,IAAI;IACtBC,WAAW,EAAE,IAAI;IACjBC,UAAU,EAAE,IAAI;IAChBxC,uBAAuB,EAAE,IAAI;IAC7ByC,mBAAmB,EAAE,IAAI;IACzBlB,gBAAgB,EAAE,IAAI;IACtBlB,UAAU,EAAE,KAAK;IACjBqC,KAAK,EAAE,IAAI;IACXC,QAAQ,EAAEnB;EACZ,CAAC;EACDoB,GAAG,EAAE;IACHnD,OAAO,EAAEA,OAAO;IAChBgC,YAAY,EAAEA;EAChB;AACF,CAAC,CAAC;AACF,IAAIoB,YAAY,GAAG9H,aAAa,CAACgH,MAAM,CAAC;EACtCC,YAAY,EAAE;IACZC,MAAM,EAAE,UAAU;IAClBU,QAAQ,EAAEnB,SAAS;IACnBY,SAAS,EAAE,IAAI;IACfU,QAAQ,EAAE,KAAK;IACf9B,SAAS,EAAE,IAAI;IACf+B,gBAAgB,EAAE,IAAI;IACtBC,YAAY,EAAE,IAAI;IAClBrC,QAAQ,EAAE,KAAK;IACfH,MAAM,EAAE,IAAI;IACZI,eAAe,EAAE,IAAI;IACrBe,WAAW,EAAE,IAAI;IACjBsB,cAAc,EAAE,IAAI;IACpBC,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE,IAAI;IAChBC,UAAU,EAAE,IAAI;IAChBC,SAAS,EAAE,IAAI;IACfX,KAAK,EAAE,IAAI;IACXY,OAAO,EAAE;EACX,CAAC;EACDC,QAAQ,EAAE,SAASA,QAAQA,CAAChD,GAAG,EAAEvD,IAAI,EAAE;IACrC,OAAOxB,WAAW,CAACgI,gBAAgB,CAACjD,GAAG,EAAEvD,IAAI,EAAE6F,YAAY,CAACb,YAAY,CAAC;EAC3E,CAAC;EACDyB,SAAS,EAAE,SAASA,SAASA,CAAClD,GAAG,EAAE;IACjC,OAAO/E,WAAW,CAACkI,iBAAiB,CAACnD,GAAG,EAAEsC,YAAY,CAACb,YAAY,CAAC;EACtE,CAAC;EACD2B,cAAc,EAAE,SAASA,cAAcA,CAACpD,GAAG,EAAE;IAC3C,OAAO/E,WAAW,CAACoI,qBAAqB,CAACrD,GAAG,EAAEsC,YAAY,CAACb,YAAY,CAAC;EAC1E;AACF,CAAC,CAAC;AAEF,SAAS6B,OAAOA,CAAC5H,CAAC,EAAEH,CAAC,EAAE;EAAE,IAAIa,CAAC,GAAGmB,MAAM,CAACiB,IAAI,CAAC9C,CAAC,CAAC;EAAE,IAAI6B,MAAM,CAACkB,qBAAqB,EAAE;IAAE,IAAI1B,CAAC,GAAGQ,MAAM,CAACkB,qBAAqB,CAAC/C,CAAC,CAAC;IAAEH,CAAC,KAAKwB,CAAC,GAAGA,CAAC,CAAC2B,MAAM,CAAC,UAAUnD,CAAC,EAAE;MAAE,OAAOgC,MAAM,CAACoB,wBAAwB,CAACjD,CAAC,EAAEH,CAAC,CAAC,CAACmC,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEtB,CAAC,CAACgC,IAAI,CAACQ,KAAK,CAACxC,CAAC,EAAEW,CAAC,CAAC;EAAE;EAAE,OAAOX,CAAC;AAAE;AAC9P,SAASmH,aAAaA,CAAC7H,CAAC,EAAE;EAAE,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuD,SAAS,CAACrD,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAIa,CAAC,GAAG,IAAI,IAAI0C,SAAS,CAACvD,CAAC,CAAC,GAAGuD,SAAS,CAACvD,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAG+H,OAAO,CAAC/F,MAAM,CAACnB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC2C,OAAO,CAAC,UAAUxD,CAAC,EAAE;MAAE+B,eAAe,CAAC5B,CAAC,EAAEH,CAAC,EAAEa,CAAC,CAACb,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGgC,MAAM,CAACyB,yBAAyB,GAAGzB,MAAM,CAAC0B,gBAAgB,CAACvD,CAAC,EAAE6B,MAAM,CAACyB,yBAAyB,CAAC5C,CAAC,CAAC,CAAC,GAAGkH,OAAO,CAAC/F,MAAM,CAACnB,CAAC,CAAC,CAAC,CAAC2C,OAAO,CAAC,UAAUxD,CAAC,EAAE;MAAEgC,MAAM,CAACC,cAAc,CAAC9B,CAAC,EAAEH,CAAC,EAAEgC,MAAM,CAACoB,wBAAwB,CAACvC,CAAC,EAAEb,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOG,CAAC;AAAE;AACtb,IAAI8H,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAG,CAAC,CAAC;AACrC,IAAIC,OAAO,GAAG,aAAapJ,KAAK,CAACqJ,UAAU,CAAC,UAAUC,OAAO,EAAEC,GAAG,EAAE;EAClE,IAAIC,UAAU,GAAGnJ,aAAa,CAAC,CAAC;EAChC,IAAIoJ,OAAO,GAAGzJ,KAAK,CAAC0J,UAAU,CAACzJ,iBAAiB,CAAC;EACjD,IAAIkF,KAAK,GAAG+B,WAAW,CAACyC,QAAQ,CAACL,OAAO,EAAEG,OAAO,CAAC;EAClD,IAAIG,eAAe,GAAG5J,KAAK,CAAC6J,QAAQ,CAAC1E,KAAK,CAACmC,EAAE,CAAC;IAC5CwC,gBAAgB,GAAG7F,cAAc,CAAC2F,eAAe,EAAE,CAAC,CAAC;IACrDG,OAAO,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC7BE,UAAU,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAClC,IAAIG,gBAAgB,GAAGjK,KAAK,CAAC6J,QAAQ,CAAC,IAAI,CAAC;IACzCK,gBAAgB,GAAGjG,cAAc,CAACgG,gBAAgB,EAAE,CAAC,CAAC;IACtDE,uBAAuB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC7CE,0BAA0B,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAClD,IAAIG,gBAAgB,GAAGrK,KAAK,CAAC6J,QAAQ,CAAC,KAAK,CAAC;IAC1CS,gBAAgB,GAAGrG,cAAc,CAACoG,gBAAgB,EAAE,CAAC,CAAC;IACtDE,sBAAsB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC5CE,yBAAyB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACjD,IAAIG,gBAAgB,GAAGzK,KAAK,CAAC6J,QAAQ,CAAC,EAAE,CAAC;IACvCa,gBAAgB,GAAGzG,cAAc,CAACwG,gBAAgB,EAAE,CAAC,CAAC;IACtDE,eAAe,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACrCE,kBAAkB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAC1C,IAAIG,gBAAgB,GAAG7K,KAAK,CAAC6J,QAAQ,CAAC1E,KAAK,CAACoC,WAAW,CAAC;IACtDuD,iBAAiB,GAAG7G,cAAc,CAAC4G,gBAAgB,EAAE,CAAC,CAAC;IACvDE,gBAAgB,GAAGD,iBAAiB,CAAC,CAAC,CAAC;IACvCE,mBAAmB,GAAGF,iBAAiB,CAAC,CAAC,CAAC;EAC5C,IAAIG,UAAU,GAAGjL,KAAK,CAACkL,MAAM,CAAC,IAAI,CAAC;EACnC,IAAIC,UAAU,GAAGnL,KAAK,CAACkL,MAAM,CAAC,IAAI,CAAC;EACnC,IAAIE,MAAM,GAAGpL,KAAK,CAACkL,MAAM,CAAC,IAAI,CAAC;EAC/B,IAAIG,SAAS,GAAGrL,KAAK,CAACkL,MAAM,CAAC,IAAI,CAAC;EAClC,IAAII,UAAU,GAAGtL,KAAK,CAACkL,MAAM,CAAC,IAAI,CAAC;EACnC,IAAIK,UAAU,GAAGvL,KAAK,CAACkL,MAAM,CAAC,IAAI,CAAC;EACnC,IAAIM,OAAO,GAAGxL,KAAK,CAACkL,MAAM,CAAC,CAAC,CAAC,CAAC;EAC9B,IAAI3D,WAAW,GAAGpC,KAAK,CAACwC,WAAW,GAAGxC,KAAK,CAACoC,WAAW,GAAGwD,gBAAgB;EAC1E,IAAIU,KAAK,GAAGzL,KAAK,CAAC0L,QAAQ,CAACD,KAAK,CAACtG,KAAK,CAAC4C,QAAQ,CAAC;EAChD,IAAI4D,QAAQ,GAAG;IACbxG,KAAK,EAAEA,KAAK;IACZyG,KAAK,EAAE;MACLtE,EAAE,EAAEyC,OAAO;MACX8B,oBAAoB,EAAE1B,uBAAuB;MAC7C2B,oBAAoB,EAAEvB,sBAAsB;MAC5CI,eAAe,EAAEA,eAAe;MAChCpD,WAAW,EAAEwD;IACf;EACF,CAAC;EACD,IAAIgB,qBAAqB,GAAG7E,WAAW,CAAC8E,WAAW,CAAC9C,aAAa,CAAC,CAAC,CAAC,EAAEyC,QAAQ,CAAC,CAAC;IAC9EM,GAAG,GAAGF,qBAAqB,CAACE,GAAG;IAC/BC,IAAI,GAAGH,qBAAqB,CAACG,IAAI;IACjCC,EAAE,GAAGJ,qBAAqB,CAACI,EAAE;IAC7BC,EAAE,GAAGL,qBAAqB,CAACK,EAAE;IAC7BC,UAAU,GAAGN,qBAAqB,CAACM,UAAU;EAC/CjM,cAAc,CAAC8G,WAAW,CAACc,GAAG,CAACsE,MAAM,EAAED,UAAU,EAAE;IACjDjK,IAAI,EAAE;EACR,CAAC,CAAC;EACF,IAAImK,QAAQ,GAAG,SAASA,QAAQA,CAAC5G,GAAG,EAAE6G,GAAG,EAAE9F,KAAK,EAAE;IAChD,IAAI+F,WAAW,GAAG;MAChBtH,KAAK,EAAEQ,GAAG,CAACR,KAAK;MAChBuH,MAAM,EAAEf,QAAQ;MAChBlC,OAAO,EAAE;QACP/C,KAAK,EAAEA,KAAK;QACZ+E,KAAK,EAAEA,KAAK;QACZkB,KAAK,EAAEjG,KAAK,KAAK,CAAC;QAClBkG,IAAI,EAAElG,KAAK,KAAK+E,KAAK,GAAG,CAAC;QACzBoB,MAAM,EAAEnG,KAAK,IAAIqE,gBAAgB;QACjChF,QAAQ,EAAEQ,UAAU,CAACZ,GAAG,EAAE,UAAU;MACtC;IACF,CAAC;IACD,OAAO6D,UAAU,CAACyC,GAAG,CAAC,MAAM,CAACa,MAAM,CAACN,GAAG,CAAC,EAAE;MACxC7G,GAAG,EAAE8G;IACP,CAAC,CAAC,EAAER,GAAG,CAAC,WAAW,CAACa,MAAM,CAACN,GAAG,CAAC,EAAE;MAC/BO,QAAQ,EAAEN;IACZ,CAAC,CAAC,EAAER,GAAG,CAAC,WAAW,CAACa,MAAM,CAACN,GAAG,CAAC,EAAEC,WAAW,CAAC,EAAEP,IAAI,CAAC3F,UAAU,CAACZ,GAAG,EAAE,IAAI,CAAC,EAAE6G,GAAG,EAAEC,WAAW,CAAC,CAAC;EAC/F,CAAC;EACD,IAAIjG,UAAU,GAAG,SAASA,UAAUA,CAACE,KAAK,EAAE;IAC1C,OAAOA,KAAK,KAAKa,WAAW;EAC9B,CAAC;EACD,IAAIhB,UAAU,GAAG,SAASA,UAAUA,CAACZ,GAAG,EAAEvD,IAAI,EAAE;IAC9C,OAAO6F,YAAY,CAACU,QAAQ,CAAChD,GAAG,EAAEvD,IAAI,CAAC;EACzC,CAAC;EACD,IAAIqE,YAAY,GAAG,SAASA,YAAYA,CAACd,GAAG,EAAE;IAC5C,OAAOA,GAAG,IAAIY,UAAU,CAACZ,GAAG,EAAE,SAAS,CAAC,IAAI/E,WAAW,CAACoM,YAAY,CAACrH,GAAG,EAAE,UAAU,CAAC,IAAIgF,eAAe,CAACsC,KAAK,CAAC,UAAUC,EAAE,EAAE;MAC3H,OAAOA,EAAE,KAAKvH,GAAG,CAAC6G,GAAG;IACvB,CAAC,CAAC;EACJ,CAAC;EACD,IAAIW,oBAAoB,GAAG,SAASA,oBAAoBA,CAACtK,CAAC,EAAE;IAC1D,IAAIuK,QAAQ,GAAGpN,KAAK,CAAC0L,QAAQ,CAAC2B,GAAG,CAAClI,KAAK,CAAC4C,QAAQ,EAAE,UAAUpC,GAAG,EAAEe,KAAK,EAAE;MACtE,IAAID,YAAY,CAACd,GAAG,CAAC,EAAE;QACrB,OAAO;UACLA,GAAG,EAAEA,GAAG;UACRe,KAAK,EAAEA;QACT,CAAC;MACH;IACF,CAAC,CAAC;IACF,OAAO0G,QAAQ,CAACE,IAAI,CAAC,UAAUpI,IAAI,EAAE;MACnC,IAAIS,GAAG,GAAGT,IAAI,CAACS,GAAG;QAChBe,KAAK,GAAGxB,IAAI,CAACwB,KAAK;MACpB,OAAO,CAACH,UAAU,CAACZ,GAAG,EAAE,UAAU,CAAC,IAAIe,KAAK,IAAI7D,CAAC;IACnD,CAAC,CAAC,IAAIuK,QAAQ,CAACG,OAAO,CAAC,CAAC,CAACD,IAAI,CAAC,UAAU9H,KAAK,EAAE;MAC7C,IAAIG,GAAG,GAAGH,KAAK,CAACG,GAAG;QACjBe,KAAK,GAAGlB,KAAK,CAACkB,KAAK;MACrB,OAAO,CAACH,UAAU,CAACZ,GAAG,EAAE,UAAU,CAAC,IAAI9C,CAAC,GAAG6D,KAAK;IAClD,CAAC,CAAC;EACJ,CAAC;EACD,IAAI8G,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,KAAK,EAAE/G,KAAK,EAAE;IAC7D+G,KAAK,CAACC,cAAc,CAAC,CAAC;IACtB,IAAIhG,gBAAgB,GAAGvC,KAAK,CAACuC,gBAAgB;MAC3CE,UAAU,GAAGzC,KAAK,CAACyC,UAAU;MAC7BG,QAAQ,GAAG5C,KAAK,CAAC4C,QAAQ;IAC3B,IAAIyE,GAAG,GAAGzE,QAAQ,CAACrB,KAAK,CAAC,CAAC8F,GAAG;;IAE7B;IACA,IAAI9E,gBAAgB,IAAIA,gBAAgB,CAAC;MACvCiG,aAAa,EAAEF,KAAK;MACpB/G,KAAK,EAAEA;IACT,CAAC,CAAC,KAAK,KAAK,EAAE;MACZ;IACF;IACAkE,kBAAkB,CAAC,EAAE,CAACkC,MAAM,CAACtK,kBAAkB,CAACmI,eAAe,CAAC,EAAE,CAAC6B,GAAG,CAAC,CAAC,CAAC;IACzE,IAAI5E,UAAU,EAAE;MACdA,UAAU,CAAC;QACT+F,aAAa,EAAEF,KAAK;QACpB/G,KAAK,EAAEA;MACT,CAAC,CAAC;IACJ;EACF,CAAC;EACD,IAAIkH,gBAAgB,GAAG,SAASA,gBAAgBA,CAACH,KAAK,EAAE9H,GAAG,EAAEe,KAAK,EAAE;IAClEmH,iBAAiB,CAACJ,KAAK,EAAE9H,GAAG,EAAEe,KAAK,CAAC;EACtC,CAAC;EACD,IAAImH,iBAAiB,GAAG,SAASA,iBAAiBA,CAACJ,KAAK,EAAE9H,GAAG,EAAEe,KAAK,EAAE;IACpE,IAAI+G,KAAK,EAAE;MACTA,KAAK,CAACC,cAAc,CAAC,CAAC;IACxB;IACA,IAAI,CAACnH,UAAU,CAACZ,GAAG,EAAE,UAAU,CAAC,EAAE;MAChC;MACA,IAAIR,KAAK,CAACsC,iBAAiB,IAAItC,KAAK,CAACsC,iBAAiB,CAAC;QACrDkG,aAAa,EAAEF,KAAK;QACpB/G,KAAK,EAAEA;MACT,CAAC,CAAC,KAAK,KAAK,EAAE;QACZ;MACF;MACA,IAAIvB,KAAK,CAACwC,WAAW,EAAE;QACrBxC,KAAK,CAACwC,WAAW,CAAC;UAChBgG,aAAa,EAAEF,KAAK;UACpB/G,KAAK,EAAEA;QACT,CAAC,CAAC;MACJ,CAAC,MAAM;QACLsE,mBAAmB,CAACtE,KAAK,CAAC;MAC5B;IACF;IACAoH,eAAe,CAAC;MACdpH,KAAK,EAAEA;IACT,CAAC,CAAC;EACJ,CAAC;EACD,IAAIqH,UAAU,GAAG,SAASC,SAASA,CAACP,KAAK,EAAE9H,GAAG,EAAEe,KAAK,EAAE;IACrD,QAAQ+G,KAAK,CAACQ,IAAI;MAChB,KAAK,WAAW;QACdC,iBAAiB,CAACT,KAAK,CAAC;QACxB;MACF,KAAK,YAAY;QACfU,kBAAkB,CAACV,KAAK,CAAC;QACzB;MACF,KAAK,MAAM;QACTW,YAAY,CAACX,KAAK,CAAC;QACnB;MACF,KAAK,KAAK;QACRY,WAAW,CAACZ,KAAK,CAAC;QAClB;MACF,KAAK,UAAU;QACba,aAAa,CAACb,KAAK,CAAC;QACpB;MACF,KAAK,QAAQ;QACXc,WAAW,CAACd,KAAK,CAAC;QAClB;MACF,KAAK,OAAO;MACZ,KAAK,aAAa;MAClB,KAAK,OAAO;QACVe,aAAa,CAACf,KAAK,EAAE9H,GAAG,EAAEe,KAAK,CAAC;QAChC;IACJ;EACF,CAAC;EACD,IAAIyH,kBAAkB,GAAG,SAASA,kBAAkBA,CAACV,KAAK,EAAE;IAC1D,IAAIgB,gBAAgB,GAAGC,qBAAqB,CAACjB,KAAK,CAACkB,MAAM,CAACC,aAAa,CAAC;IACxEH,gBAAgB,GAAGI,gBAAgB,CAACJ,gBAAgB,CAAC,GAAGL,YAAY,CAACX,KAAK,CAAC;IAC3EA,KAAK,CAACC,cAAc,CAAC,CAAC;EACxB,CAAC;EACD,IAAIQ,iBAAiB,GAAG,SAASA,iBAAiBA,CAACT,KAAK,EAAE;IACxD,IAAIqB,gBAAgB,GAAGC,qBAAqB,CAACtB,KAAK,CAACkB,MAAM,CAACC,aAAa,CAAC;IACxEE,gBAAgB,GAAGD,gBAAgB,CAACC,gBAAgB,CAAC,GAAGT,WAAW,CAACZ,KAAK,CAAC;IAC1EA,KAAK,CAACC,cAAc,CAAC,CAAC;EACxB,CAAC;EACD,IAAIU,YAAY,GAAG,SAASA,YAAYA,CAACX,KAAK,EAAE;IAC9C,IAAIuB,iBAAiB,GAAGC,qBAAqB,CAAC,CAAC;IAC/CJ,gBAAgB,CAACG,iBAAiB,CAAC;IACnCvB,KAAK,CAACC,cAAc,CAAC,CAAC;EACxB,CAAC;EACD,IAAIW,WAAW,GAAG,SAASA,WAAWA,CAACZ,KAAK,EAAE;IAC5C,IAAIyB,gBAAgB,GAAGC,oBAAoB,CAAC,CAAC;IAC7CN,gBAAgB,CAACK,gBAAgB,CAAC;IAClCzB,KAAK,CAACC,cAAc,CAAC,CAAC;EACxB,CAAC;EACD,IAAIY,aAAa,GAAG,SAASA,aAAaA,CAACb,KAAK,EAAE;IAChDK,eAAe,CAAC;MACdpH,KAAK,EAAE1G,KAAK,CAAC0L,QAAQ,CAACD,KAAK,CAACtG,KAAK,CAAC4C,QAAQ,CAAC,GAAG;IAChD,CAAC,CAAC;IACF0F,KAAK,CAACC,cAAc,CAAC,CAAC;EACxB,CAAC;EACD,IAAIa,WAAW,GAAG,SAASA,WAAWA,CAACd,KAAK,EAAE;IAC5CK,eAAe,CAAC;MACdpH,KAAK,EAAE;IACT,CAAC,CAAC;IACF+G,KAAK,CAACC,cAAc,CAAC,CAAC;EACxB,CAAC;EACD,IAAIc,aAAa,GAAG,SAASA,aAAaA,CAACf,KAAK,EAAE9H,GAAG,EAAEe,KAAK,EAAE;IAC5DmH,iBAAiB,CAACJ,KAAK,EAAE9H,GAAG,EAAEe,KAAK,CAAC;IACpC+G,KAAK,CAACC,cAAc,CAAC,CAAC;EACxB,CAAC;EACD,IAAIgB,qBAAqB,GAAG,SAASU,oBAAoBA,CAACC,UAAU,EAAE;IACpE,IAAIC,SAAS,GAAG7K,SAAS,CAACrD,MAAM,GAAG,CAAC,IAAIqD,SAAS,CAAC,CAAC,CAAC,KAAKmC,SAAS,GAAGnC,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;IACzF,IAAI8K,aAAa,GAAGD,SAAS,GAAGD,UAAU,GAAGA,UAAU,CAACG,kBAAkB;IAC1E,OAAOD,aAAa,GAAGxO,UAAU,CAAC0O,YAAY,CAACF,aAAa,EAAE,iBAAiB,CAAC,IAAIxO,UAAU,CAAC0O,YAAY,CAACF,aAAa,EAAE,iBAAiB,CAAC,KAAK,QAAQ,GAAGb,qBAAqB,CAACa,aAAa,CAAC,GAAGxO,UAAU,CAAC2O,UAAU,CAACH,aAAa,EAAE,kCAAkC,CAAC,GAAG,IAAI;EACrR,CAAC;EACD,IAAIR,qBAAqB,GAAG,SAASY,oBAAoBA,CAACN,UAAU,EAAE;IACpE,IAAIC,SAAS,GAAG7K,SAAS,CAACrD,MAAM,GAAG,CAAC,IAAIqD,SAAS,CAAC,CAAC,CAAC,KAAKmC,SAAS,GAAGnC,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;IACzF,IAAI8K,aAAa,GAAGD,SAAS,GAAGD,UAAU,GAAGA,UAAU,CAACO,sBAAsB;IAC9E,OAAOL,aAAa,GAAGxO,UAAU,CAAC0O,YAAY,CAACF,aAAa,EAAE,iBAAiB,CAAC,IAAIxO,UAAU,CAAC0O,YAAY,CAACF,aAAa,EAAE,iBAAiB,CAAC,KAAK,QAAQ,GAAGR,qBAAqB,CAACQ,aAAa,CAAC,GAAGxO,UAAU,CAAC2O,UAAU,CAACH,aAAa,EAAE,kCAAkC,CAAC,GAAG,IAAI;EACrR,CAAC;EACD,IAAIN,qBAAqB,GAAG,SAASA,qBAAqBA,CAAA,EAAG;IAC3D,OAAOP,qBAAqB,CAACtD,MAAM,CAACyE,OAAO,CAACC,iBAAiB,EAAE,IAAI,CAAC;EACtE,CAAC;EACD,IAAIX,oBAAoB,GAAG,SAASA,oBAAoBA,CAAA,EAAG;IACzD,OAAOJ,qBAAqB,CAAC3D,MAAM,CAACyE,OAAO,CAACE,gBAAgB,EAAE,IAAI,CAAC;EACrE,CAAC;EACD,IAAIlB,gBAAgB,GAAG,SAASA,gBAAgBA,CAACmB,OAAO,EAAE;IACxD,IAAIA,OAAO,EAAE;MACXjP,UAAU,CAACkP,KAAK,CAACD,OAAO,CAAC;MACzBlC,eAAe,CAAC;QACdkC,OAAO,EAAEA;MACX,CAAC,CAAC;IACJ;EACF,CAAC;EACD,IAAIE,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAG;IACzC,IAAIC,SAAS,GAAG3E,OAAO,CAACqE,OAAO,CAAC,MAAM,CAAC/C,MAAM,CAACvF,WAAW,CAAC,CAAC;IAC3D8D,SAAS,CAACwE,OAAO,CAAC/H,KAAK,CAACsI,KAAK,GAAGrP,UAAU,CAACsP,QAAQ,CAACF,SAAS,CAAC,GAAG,IAAI;IACrE9E,SAAS,CAACwE,OAAO,CAAC/H,KAAK,CAACwI,IAAI,GAAGvP,UAAU,CAACwP,SAAS,CAACJ,SAAS,CAAC,CAACG,IAAI,GAAGvP,UAAU,CAACwP,SAAS,CAACnF,MAAM,CAACyE,OAAO,CAAC,CAACS,IAAI,GAAG,IAAI;EACxH,CAAC;EACD,IAAIxC,eAAe,GAAG,SAASA,eAAeA,CAACjI,KAAK,EAAE;IACpD,IAAIa,KAAK,GAAGb,KAAK,CAACa,KAAK;MACrBsJ,OAAO,GAAGnK,KAAK,CAACmK,OAAO;IACzB,IAAIG,SAAS,GAAGH,OAAO,IAAIxE,OAAO,CAACqE,OAAO,CAAC,MAAM,CAAC/C,MAAM,CAACpG,KAAK,CAAC,CAAC;IAChE,IAAIyJ,SAAS,IAAIA,SAAS,CAACK,cAAc,EAAE;MACzCL,SAAS,CAACK,cAAc,CAAC;QACvBC,KAAK,EAAE;MACT,CAAC,CAAC;IACJ;EACF,CAAC;EACD,IAAIC,iBAAiB,GAAG,SAASA,iBAAiBA,CAAA,EAAG;IACnD,IAAIC,mBAAmB,GAAGxF,UAAU,CAAC0E,OAAO;MAC1Ce,UAAU,GAAGD,mBAAmB,CAACC,UAAU;MAC3CC,WAAW,GAAGF,mBAAmB,CAACE,WAAW;IAC/C,IAAIT,KAAK,GAAGrP,UAAU,CAACsP,QAAQ,CAAClF,UAAU,CAAC0E,OAAO,CAAC;IACnDzF,0BAA0B,CAACwG,UAAU,KAAK,CAAC,CAAC;IAC5CpG,yBAAyB,CAACsG,QAAQ,CAACF,UAAU,CAAC,KAAKC,WAAW,GAAGT,KAAK,CAAC;EACzE,CAAC;EACD,IAAIW,QAAQ,GAAG,SAASA,QAAQA,CAACtD,KAAK,EAAE;IACtCtI,KAAK,CAACM,UAAU,IAAIiL,iBAAiB,CAAC,CAAC;IACvCjD,KAAK,CAACC,cAAc,CAAC,CAAC;EACxB,CAAC;EACD,IAAIsD,sBAAsB,GAAG,SAASA,sBAAsBA,CAAA,EAAG;IAC7D,OAAO,CAAC1F,UAAU,CAACuE,OAAO,EAAEtE,UAAU,CAACsE,OAAO,CAAC,CAACoB,MAAM,CAAC,UAAUC,GAAG,EAAEC,EAAE,EAAE;MACxE,OAAOA,EAAE,GAAGD,GAAG,GAAGnQ,UAAU,CAACsP,QAAQ,CAACc,EAAE,CAAC,GAAGD,GAAG;IACjD,CAAC,EAAE,CAAC,CAAC;EACP,CAAC;EACD,IAAIE,WAAW,GAAG,SAASA,WAAWA,CAAA,EAAG;IACvC,IAAIhB,KAAK,GAAGrP,UAAU,CAACsP,QAAQ,CAAClF,UAAU,CAAC0E,OAAO,CAAC,GAAGmB,sBAAsB,CAAC,CAAC;IAC9E,IAAIK,GAAG,GAAGlG,UAAU,CAAC0E,OAAO,CAACe,UAAU,GAAGR,KAAK;IAC/CjF,UAAU,CAAC0E,OAAO,CAACe,UAAU,GAAGS,GAAG,IAAI,CAAC,GAAG,CAAC,GAAGA,GAAG;EACpD,CAAC;EACD,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAAA,EAAG;IACrC,IAAIlB,KAAK,GAAGrP,UAAU,CAACsP,QAAQ,CAAClF,UAAU,CAAC0E,OAAO,CAAC,GAAGmB,sBAAsB,CAAC,CAAC;IAC9E,IAAIK,GAAG,GAAGlG,UAAU,CAAC0E,OAAO,CAACe,UAAU,GAAGR,KAAK;IAC/C,IAAImB,OAAO,GAAGpG,UAAU,CAAC0E,OAAO,CAACgB,WAAW,GAAGT,KAAK;IACpDjF,UAAU,CAAC0E,OAAO,CAACe,UAAU,GAAGS,GAAG,IAAIE,OAAO,GAAGA,OAAO,GAAGF,GAAG;EAChE,CAAC;EACD,IAAIG,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAG;IAC3BpH,0BAA0B,CAAC,IAAI,CAAC;IAChCI,yBAAyB,CAAC,KAAK,CAAC;IAChCI,kBAAkB,CAAC,EAAE,CAAC;IACtB,IAAIzF,KAAK,CAACwC,WAAW,EAAE;MACrBxC,KAAK,CAACwC,WAAW,CAAC;QAChBjB,KAAK,EAAEa;MACT,CAAC,CAAC;IACJ,CAAC,MAAM;MACLyD,mBAAmB,CAAC7F,KAAK,CAACoC,WAAW,CAAC;IACxC;EACF,CAAC;EACDvH,KAAK,CAACyR,SAAS,CAAC,YAAY;IAC1BvB,YAAY,CAAC,CAAC;IACdQ,iBAAiB,CAAC,CAAC;EACrB,CAAC,CAAC;EACFpQ,cAAc,CAAC,YAAY;IACzB,IAAI,CAACyJ,OAAO,EAAE;MACZC,UAAU,CAAClJ,iBAAiB,CAAC,CAAC,CAAC;IACjC;EACF,CAAC,CAAC;EACFP,eAAe,CAAC,YAAY;IAC1B,IAAIK,WAAW,CAAC8Q,UAAU,CAAC/G,eAAe,CAAC,EAAE;MAC3C,IAAIgH,OAAO,GAAGxE,oBAAoB,CAACxC,eAAe,CAACA,eAAe,CAACvJ,MAAM,GAAG,CAAC,CAAC,CAAC;MAC/EuQ,OAAO,IAAI/D,gBAAgB,CAAC,IAAI,EAAE+D,OAAO,CAAChM,GAAG,EAAEgM,OAAO,CAACjL,KAAK,CAAC;IAC/D;EACF,CAAC,EAAE,CAACiE,eAAe,CAAC,CAAC;EACrBpK,eAAe,CAAC,YAAY;IAC1B,IAAI4E,KAAK,CAACoC,WAAW,KAAKwD,gBAAgB,EAAE;MAC1C+C,eAAe,CAAC;QACdpH,KAAK,EAAEvB,KAAK,CAACoC;MACf,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACpC,KAAK,CAACoC,WAAW,CAAC,CAAC;EACvBvH,KAAK,CAAC4R,mBAAmB,CAACrI,GAAG,EAAE,YAAY;IACzC,OAAO;MACLpE,KAAK,EAAEA,KAAK;MACZqM,KAAK,EAAEA,KAAK;MACZK,UAAU,EAAE,SAASA,UAAUA,CAAA,EAAG;QAChC,OAAO5G,UAAU,CAAC4E,OAAO;MAC3B;IACF,CAAC;EACH,CAAC,CAAC;EACF,IAAIiC,kBAAkB,GAAG,SAASA,kBAAkBA,CAACrE,KAAK,EAAE/G,KAAK,EAAE;IACjE+G,KAAK,CAACC,cAAc,CAAC,CAAC;IACtBD,KAAK,CAACsE,eAAe,CAAC,CAAC;IACvB,QAAQtE,KAAK,CAACQ,IAAI;MAChB,KAAK,OAAO;MACZ,KAAK,aAAa;MAClB,KAAK,OAAO;QACVT,gBAAgB,CAACC,KAAK,EAAE/G,KAAK,CAAC;QAC9B;IACJ;EACF,CAAC;EACD,IAAIsL,eAAe,GAAG,SAASA,eAAeA,CAACrM,GAAG,EAAEe,KAAK,EAAE;IACzD,IAAIZ,QAAQ,GAAGU,UAAU,CAACE,KAAK,CAAC;IAChC,IAAIuL,qBAAqB,GAAGhK,YAAY,CAACY,SAAS,CAAClD,GAAG,CAAC;MACrDoB,WAAW,GAAGkL,qBAAqB,CAAClL,WAAW;MAC/Cf,eAAe,GAAGiM,qBAAqB,CAACjM,eAAe;MACvDgB,MAAM,GAAGiL,qBAAqB,CAACnK,KAAK;MACpC7B,UAAU,GAAGgM,qBAAqB,CAACzK,SAAS;MAC5CzB,QAAQ,GAAGkM,qBAAqB,CAAClM,QAAQ;MACzCuC,QAAQ,GAAG2J,qBAAqB,CAAC3J,QAAQ;MACzCG,SAAS,GAAGwJ,qBAAqB,CAACxJ,SAAS;MAC3C7C,MAAM,GAAGqM,qBAAqB,CAACrM,MAAM;MACrCyC,cAAc,GAAG4J,qBAAqB,CAAC5J,cAAc;MACrDH,QAAQ,GAAG+J,qBAAqB,CAAC/J,QAAQ;MACzC9B,SAAS,GAAG6L,qBAAqB,CAAC7L,SAAS;IAC7C,IAAI8L,QAAQ,GAAGnI,OAAO,GAAG,UAAU,GAAGrD,KAAK;IAC3C,IAAIyL,YAAY,GAAGpI,OAAO,GAAGrD,KAAK,GAAG,UAAU;IAC/C,IAAI0L,QAAQ,GAAGrM,QAAQ,IAAI,CAACD,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC;IAC7C,IAAIuM,eAAe,GAAG/J,QAAQ,IAAItH,SAAS,CAACsR,UAAU,CAAChK,QAAQ,EAAE1B,SAAS,EAAE;MAC1EzB,KAAK,EAAEA;IACT,CAAC,CAAC;IACF,IAAIoN,gBAAgB,GAAG/I,UAAU,CAAC;MAChChC,SAAS,EAAE2E,EAAE,CAAC,iBAAiB;IACjC,CAAC,EAAEI,QAAQ,CAAC5G,GAAG,EAAE,aAAa,EAAEe,KAAK,CAAC,CAAC;IACvC,IAAI8L,YAAY,GAAG,aAAaxS,KAAK,CAACyS,aAAa,CAAC,MAAM,EAAEF,gBAAgB,EAAE3M,MAAM,CAAC;IACrF,IAAI8M,gBAAgB,GAAGjK,SAAS,IAAIzH,SAAS,CAACsR,UAAU,CAAC7J,SAAS,EAAE7B,SAAS,EAAE;MAC7EzB,KAAK,EAAEA;IACT,CAAC,CAAC;IACF,IAAIwN,cAAc,GAAGnJ,UAAU,CAAC;MAC9BhC,SAAS,EAAE2E,EAAE,CAAC,eAAe,CAAC;MAC9ByG,OAAO,EAAE,SAASA,OAAOA,CAACvR,CAAC,EAAE;QAC3B,OAAOmM,gBAAgB,CAACnM,CAAC,EAAEqF,KAAK,CAAC;MACnC,CAAC;MACDsH,SAAS,EAAE,SAASA,SAASA,CAAC3M,CAAC,EAAE;QAC/B,OAAOyQ,kBAAkB,CAACzQ,CAAC,EAAEqF,KAAK,CAAC;MACrC,CAAC;MACD0L,QAAQ,EAAE,CAAC;MACX,YAAY,EAAElS,SAAS,CAAC,OAAO,CAAC,IAAI;IACtC,CAAC,EAAEqM,QAAQ,CAAC5G,GAAG,EAAE,WAAW,EAAEe,KAAK,CAAC,CAAC;IACrC,IAAImM,IAAI,GAAGzM,SAAS,IAAI,aAAapG,KAAK,CAACyS,aAAa,CAAC/R,SAAS,EAAEiS,cAAc,CAAC;IACnF,IAAIG,mBAAmB,GAAG5K,QAAQ,GAAGlH,SAAS,CAACsR,UAAU,CAACO,IAAI,EAAE3J,aAAa,CAAC,CAAC,CAAC,EAAEyJ,cAAc,CAAC,EAAE;MACjGxN,KAAK,EAAEA;IACT,CAAC,CAAC,GAAG,IAAI;IACT,IAAI4N,iBAAiB,GAAGvJ,UAAU,CAAC;MACjClC,EAAE,EAAE4K,QAAQ;MACZc,IAAI,EAAE,KAAK;MACXxL,SAAS,EAAE2E,EAAE,CAAC,kBAAkB,CAAC;MACjCiG,QAAQ,EAAEA,QAAQ;MAClB,eAAe,EAAED,YAAY;MAC7B,eAAe,EAAErM,QAAQ;MACzB,eAAe,EAAEC,QAAQ;MACzB6M,OAAO,EAAE,SAASA,OAAOA,CAACvR,CAAC,EAAE;QAC3B,OAAOuM,gBAAgB,CAACvM,CAAC,EAAEsE,GAAG,EAAEe,KAAK,CAAC;MACxC,CAAC;MACDsH,SAAS,EAAE,SAASA,SAASA,CAAC3M,CAAC,EAAE;QAC/B,OAAO0M,UAAU,CAAC1M,CAAC,EAAEsE,GAAG,EAAEe,KAAK,CAAC;MAClC;IACF,CAAC,EAAE6F,QAAQ,CAAC5G,GAAG,EAAE,cAAc,EAAEe,KAAK,CAAC,CAAC;IACxC,IAAIL,OAAO,GACX;IACA;IACArG,KAAK,CAACyS,aAAa,CAAC,GAAG,EAAEM,iBAAiB,EAAEV,eAAe,EAAEG,YAAY,EAAEE,gBAAgB,EAAEI,mBAAmB,EAAE,aAAa9S,KAAK,CAACyS,aAAa,CAAC9R,MAAM,EAAE,IAAI,CAAC;IAChK;IAAA;IAEA,IAAI0H,cAAc,EAAE;MAClB,IAAI4K,qBAAqB,GAAG;QAC1BzL,SAAS,EAAE,oBAAoB;QAC/B0L,cAAc,EAAE,iBAAiB;QACjCN,OAAO,EAAE,SAASA,OAAOA,CAACvR,CAAC,EAAE;UAC3B,OAAOuM,gBAAgB,CAACvM,CAAC,EAAEsE,GAAG,EAAEe,KAAK,CAAC;QACxC,CAAC;QACDsH,SAAS,EAAE,SAASA,SAASA,CAAC3M,CAAC,EAAE;UAC/B,OAAO0M,UAAU,CAAC1M,CAAC,EAAEsE,GAAG,EAAEe,KAAK,CAAC;QAClC,CAAC;QACD2L,eAAe,EAAEA,eAAe;QAChCG,YAAY,EAAEA,YAAY;QAC1BE,gBAAgB,EAAEA,gBAAgB;QAClC1C,OAAO,EAAE3J,OAAO;QAChBlB,KAAK,EAAEA,KAAK;QACZuB,KAAK,EAAEA,KAAK;QACZZ,QAAQ,EAAEA,QAAQ;QAClBqM,YAAY,EAAEA;MAChB,CAAC;MACD9L,OAAO,GAAGzF,WAAW,CAACuS,aAAa,CAAC9K,cAAc,EAAE4K,qBAAqB,CAAC;IAC5E;IACA,IAAIG,WAAW,GAAG5J,UAAU,CAAC;MAC3BD,GAAG,EAAE,SAASA,GAAGA,CAAC4H,EAAE,EAAE;QACpB,OAAO3F,OAAO,CAACqE,OAAO,CAAC,MAAM,CAAC/C,MAAM,CAACpG,KAAK,CAAC,CAAC,GAAGyK,EAAE;MACnD,CAAC;MACD3J,SAAS,EAAE2E,EAAE,CAAC,YAAY,EAAE;QAC1BrG,QAAQ,EAAEA,QAAQ;QAClBC,QAAQ,EAAEA,QAAQ;QAClBC,eAAe,EAAEA,eAAe;QAChCC,UAAU,EAAEA;MACd,CAAC,CAAC;MACF6B,KAAK,EAAEsE,EAAE,CAAC,YAAY,EAAE;QACtBrF,WAAW,EAAEA,WAAW;QACxBC,MAAM,EAAEA;MACV,CAAC,CAAC;MACFgM,IAAI,EAAE;IACR,CAAC,EAAEzG,QAAQ,CAAC5G,GAAG,EAAE,MAAM,EAAEe,KAAK,CAAC,EAAE6F,QAAQ,CAAC5G,GAAG,EAAE,QAAQ,EAAEe,KAAK,CAAC,CAAC;IAChE,OAAO,aAAa1G,KAAK,CAACyS,aAAa,CAAC,IAAI,EAAEW,WAAW,EAAE/M,OAAO,CAAC;EACrE,CAAC;EACD,IAAIgN,gBAAgB,GAAG,SAASA,gBAAgBA,CAAA,EAAG;IACjD,OAAOrT,KAAK,CAAC0L,QAAQ,CAAC2B,GAAG,CAAClI,KAAK,CAAC4C,QAAQ,EAAE,UAAUpC,GAAG,EAAEe,KAAK,EAAE;MAC9D,IAAID,YAAY,CAACd,GAAG,CAAC,EAAE;QACrB,OAAOqM,eAAe,CAACrM,GAAG,EAAEe,KAAK,CAAC;MACpC;IACF,CAAC,CAAC;EACJ,CAAC;EACD,IAAI4M,eAAe,GAAG,SAASA,eAAeA,CAAA,EAAG;IAC/C,IAAIC,OAAO,GAAGF,gBAAgB,CAAC,CAAC;IAChC,IAAIG,eAAe,GAAGhK,UAAU,CAAC;MAC/BlC,EAAE,EAAEyC,OAAO,GAAG,aAAa;MAC3BR,GAAG,EAAE4B,UAAU;MACf3D,SAAS,EAAE2E,EAAE,CAAC,YAAY,CAAC;MAC3BrE,KAAK,EAAE3C,KAAK,CAAC2C,KAAK;MAClBiJ,QAAQ,EAAEA;IACZ,CAAC,EAAE9E,GAAG,CAAC,YAAY,CAAC,CAAC;IACrB,IAAIwH,QAAQ,GAAGjK,UAAU,CAAC;MACxBD,GAAG,EAAE6B,MAAM;MACX5D,SAAS,EAAE2E,EAAE,CAAC,KAAK,CAAC;MACpB6G,IAAI,EAAE;IACR,CAAC,EAAE/G,GAAG,CAAC,KAAK,CAAC,CAAC;IACd,IAAIyH,WAAW,GAAGlK,UAAU,CAAC;MAC3BD,GAAG,EAAE8B,SAAS;MACd,aAAa,EAAE,MAAM;MACrB2H,IAAI,EAAE,cAAc;MACpBxL,SAAS,EAAE2E,EAAE,CAAC,QAAQ;IACxB,CAAC,EAAEF,GAAG,CAAC,QAAQ,CAAC,CAAC;IACjB,OAAO,aAAajM,KAAK,CAACyS,aAAa,CAAC,KAAK,EAAEe,eAAe,EAAE,aAAaxT,KAAK,CAACyS,aAAa,CAAC,IAAI,EAAEgB,QAAQ,EAAEF,OAAO,EAAE,aAAavT,KAAK,CAACyS,aAAa,CAAC,IAAI,EAAEiB,WAAW,CAAC,CAAC,CAAC;EACjL,CAAC;EACD,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAAA,EAAG;IAC3C,IAAIC,mBAAmB,GAAGpK,UAAU,CAAC;MACnChC,SAAS,EAAE2E,EAAE,CAAC,gBAAgB,CAAC;MAC/BrE,KAAK,EAAE3C,KAAK,CAAC0C;IACf,CAAC,EAAEoE,GAAG,CAAC,gBAAgB,CAAC,CAAC;IACzB,IAAI4H,QAAQ,GAAG7T,KAAK,CAAC0L,QAAQ,CAAC2B,GAAG,CAAClI,KAAK,CAAC4C,QAAQ,EAAE,UAAUpC,GAAG,EAAEe,KAAK,EAAE;MACtE,IAAID,YAAY,CAACd,GAAG,CAAC,KAAK,CAACR,KAAK,CAACwB,gBAAgB,IAAIH,UAAU,CAACE,KAAK,CAAC,CAAC,EAAE;QACvE,IAAIZ,QAAQ,GAAGU,UAAU,CAACE,KAAK,CAAC;QAChC,IAAIoN,SAAS,GAAG/J,OAAO,GAAGrD,KAAK,GAAG,UAAU;QAC5C,IAAIqN,cAAc,GAAGhK,OAAO,GAAG,UAAU,GAAGrD,KAAK;QACjD,IAAIsN,YAAY,GAAGxK,UAAU,CAAC;UAC5BlC,EAAE,EAAEwM,SAAS;UACbtM,SAAS,EAAE2E,EAAE,CAAC,aAAa,EAAE;YAC3BhH,KAAK,EAAEA,KAAK;YACZW,QAAQ,EAAEA,QAAQ;YAClBS,UAAU,EAAEA,UAAU;YACtBZ,GAAG,EAAEA,GAAG;YACRa,UAAU,EAAEA,UAAU;YACtBC,YAAY,EAAEA,YAAY;YAC1BC,KAAK,EAAEA;UACT,CAAC,CAAC;UACFoB,KAAK,EAAEsE,EAAE,CAAC,aAAa,EAAE;YACvBjH,KAAK,EAAEA,KAAK;YACZoB,UAAU,EAAEA,UAAU;YACtBZ,GAAG,EAAEA,GAAG;YACRa,UAAU,EAAEA,UAAU;YACtBC,YAAY,EAAEA,YAAY;YAC1BC,KAAK,EAAEA;UACT,CAAC,CAAC;UACFsM,IAAI,EAAE,UAAU;UAChB,iBAAiB,EAAEe;QACrB,CAAC,EAAE9L,YAAY,CAACc,cAAc,CAACpD,GAAG,CAAC,EAAE4G,QAAQ,CAAC5G,GAAG,EAAE,MAAM,EAAEe,KAAK,CAAC,EAAE6F,QAAQ,CAAC5G,GAAG,EAAE,SAAS,EAAEe,KAAK,CAAC,CAAC;QACnG,OAAO,aAAa1G,KAAK,CAACyS,aAAa,CAAC,KAAK,EAAEuB,YAAY,EAAE,CAAC7O,KAAK,CAACwB,gBAAgB,GAAGJ,UAAU,CAACZ,GAAG,EAAE,UAAU,CAAC,GAAGG,QAAQ,IAAIS,UAAU,CAACZ,GAAG,EAAE,UAAU,CAAC,CAAC;MAC/J;IACF,CAAC,CAAC;IACF,OAAO,aAAa3F,KAAK,CAACyS,aAAa,CAAC,KAAK,EAAEmB,mBAAmB,EAAEC,QAAQ,CAAC;EAC/E,CAAC;EACD,IAAII,gBAAgB,GAAG,SAASA,gBAAgBA,CAAA,EAAG;IACjD,IAAIC,aAAa,GAAG1K,UAAU,CAAC;MAC7B,aAAa,EAAE;IACjB,CAAC,EAAEyC,GAAG,CAAC,UAAU,CAAC,CAAC;IACnB,IAAI4G,IAAI,GAAG1N,KAAK,CAACqD,UAAU,IAAI,aAAaxI,KAAK,CAACyS,aAAa,CAACjS,eAAe,EAAE0T,aAAa,CAAC;IAC/F,IAAI5L,QAAQ,GAAGtH,SAAS,CAACsR,UAAU,CAACO,IAAI,EAAE3J,aAAa,CAAC,CAAC,CAAC,EAAEgL,aAAa,CAAC,EAAE;MAC1E/O,KAAK,EAAEA;IACT,CAAC,CAAC;IACF,IAAIgP,eAAe,GAAG3K,UAAU,CAAC;MAC/BD,GAAG,EAAE+B,UAAU;MACf8I,IAAI,EAAE,QAAQ;MACd5M,SAAS,EAAE2E,EAAE,CAAC,YAAY,CAAC;MAC3B,YAAY,EAAEjM,SAAS,CAAC,eAAe,CAAC;MACxC0S,OAAO,EAAE,SAASA,OAAOA,CAACvR,CAAC,EAAE;QAC3B,OAAO+P,WAAW,CAAC,CAAC;MACtB;IACF,CAAC,EAAEnF,GAAG,CAAC,YAAY,CAAC,CAAC;IACrB,IAAI9G,KAAK,CAACM,UAAU,IAAI,CAAC0E,uBAAuB,EAAE;MAChD,OAAO,aAAanK,KAAK,CAACyS,aAAa,CAAC,QAAQ,EAAE0B,eAAe,EAAE7L,QAAQ,EAAE,aAAatI,KAAK,CAACyS,aAAa,CAAC9R,MAAM,EAAE,IAAI,CAAC,CAAC;IAC9H;IACA,OAAO,IAAI;EACb,CAAC;EACD,IAAI0T,gBAAgB,GAAG,SAASA,gBAAgBA,CAAA,EAAG;IACjD,IAAIC,aAAa,GAAG9K,UAAU,CAAC;MAC7B,aAAa,EAAE;IACjB,CAAC,EAAEyC,GAAG,CAAC,UAAU,CAAC,CAAC;IACnB,IAAI4G,IAAI,GAAG1N,KAAK,CAACoD,UAAU,IAAI,aAAavI,KAAK,CAACyS,aAAa,CAAChS,gBAAgB,EAAE6T,aAAa,CAAC;IAChG,IAAI7L,SAAS,GAAGzH,SAAS,CAACsR,UAAU,CAACO,IAAI,EAAE3J,aAAa,CAAC,CAAC,CAAC,EAAEoL,aAAa,CAAC,EAAE;MAC3EnP,KAAK,EAAEA;IACT,CAAC,CAAC;IACF,IAAIoP,eAAe,GAAG/K,UAAU,CAAC;MAC/BD,GAAG,EAAEgC,UAAU;MACf6I,IAAI,EAAE,QAAQ;MACd5M,SAAS,EAAE2E,EAAE,CAAC,YAAY,CAAC;MAC3B,YAAY,EAAEjM,SAAS,CAAC,eAAe,CAAC;MACxC0S,OAAO,EAAE,SAASA,OAAOA,CAACvR,CAAC,EAAE;QAC3B,OAAOiQ,UAAU,CAAC,CAAC;MACrB;IACF,CAAC,EAAErF,GAAG,CAAC,YAAY,CAAC,CAAC;IACrB,IAAI9G,KAAK,CAACM,UAAU,IAAI,CAAC8E,sBAAsB,EAAE;MAC/C,OAAO,aAAavK,KAAK,CAACyS,aAAa,CAAC,QAAQ,EAAE8B,eAAe,EAAE9L,SAAS,EAAE,aAAazI,KAAK,CAACyS,aAAa,CAAC9R,MAAM,EAAE,IAAI,CAAC,CAAC;IAC/H;EACF,CAAC;EACD,IAAI6T,SAAS,GAAGhL,UAAU,CAAC;IACzBlC,EAAE,EAAEyC,OAAO;IACXR,GAAG,EAAE0B,UAAU;IACfnD,KAAK,EAAE3C,KAAK,CAAC2C,KAAK;IAClBN,SAAS,EAAE3G,UAAU,CAACsE,KAAK,CAACqC,SAAS,EAAE2E,EAAE,CAAC,MAAM,CAAC;EACnD,CAAC,EAAEjF,WAAW,CAACuN,aAAa,CAACtP,KAAK,CAAC,EAAE8G,GAAG,CAAC,MAAM,CAAC,CAAC;EACjD,IAAIyI,iBAAiB,GAAGlL,UAAU,CAAC;IACjChC,SAAS,EAAE2E,EAAE,CAAC,cAAc;EAC9B,CAAC,EAAEF,GAAG,CAAC,cAAc,CAAC,CAAC;EACvB,IAAI0I,SAAS,GAAGrB,eAAe,CAAC,CAAC;EACjC,IAAIjN,OAAO,GAAGsN,aAAa,CAAC,CAAC;EAC7B,IAAInL,UAAU,GAAGyL,gBAAgB,CAAC,CAAC;EACnC,IAAI1L,UAAU,GAAG8L,gBAAgB,CAAC,CAAC;EACnC,OAAO,aAAarU,KAAK,CAACyS,aAAa,CAAC,KAAK,EAAE+B,SAAS,EAAE,aAAaxU,KAAK,CAACyS,aAAa,CAAC,KAAK,EAAEiC,iBAAiB,EAAElM,UAAU,EAAEmM,SAAS,EAAEpM,UAAU,CAAC,EAAElC,OAAO,CAAC;AACnK,CAAC,CAAC;AACF8C,QAAQ,CAACyL,WAAW,GAAG,UAAU;AACjCxL,OAAO,CAACwL,WAAW,GAAG,SAAS;AAE/B,SAASzL,QAAQ,EAAEC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}