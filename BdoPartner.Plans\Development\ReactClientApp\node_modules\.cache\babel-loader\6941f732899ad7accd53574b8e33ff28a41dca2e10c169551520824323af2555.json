{"ast": null, "code": "export var observable = function () {\n  return typeof Symbol === 'function' && Symbol.observable || '@@observable';\n}();", "map": {"version": 3, "names": ["observable", "Symbol"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\symbol\\observable.ts"], "sourcesContent": ["/**\n * Symbol.observable or a string \"@@observable\". Used for interop\n *\n * @deprecated We will no longer be exporting this symbol in upcoming versions of RxJS.\n * Instead polyfill and use Symbol.observable directly *or* use https://www.npmjs.com/package/symbol-observable\n */\nexport const observable: string | symbol = (() => (typeof Symbol === 'function' && Symbol.observable) || '@@observable')();\n"], "mappings": "AAMA,OAAO,IAAMA,UAAU,GAAqB;EAAM,OAAC,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACD,UAAU,IAAK,cAAc;AAArE,CAAqE,CAAC,CAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}