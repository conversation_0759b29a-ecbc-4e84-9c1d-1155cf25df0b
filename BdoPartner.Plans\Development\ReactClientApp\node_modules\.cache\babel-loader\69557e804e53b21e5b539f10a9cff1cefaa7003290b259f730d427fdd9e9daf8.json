{"ast": null, "code": "/**\r\n * Questionnaire Status enumeration definitions.\r\n * Reference to Enumerations.QuestionnaireStatus in server side.\r\n * Reference to records in table dbo.[QuestionnaireStatus].\r\n * Work for questionnaire status management and display.\r\n */\nexport const QuestionnaireStatus = {\n  /**\r\n   * Draft status - Questionnaire is in draft state\r\n   */\n  Draft: 0,\n  /**\r\n   * Published status - Questionnaire has been published and is active\r\n   */\n  Published: 1,\n  /**\r\n   * Closed status - Questionnaire is closed after EndYear cycle published\r\n   */\n  Closed: 2\n};\n\n/**\r\n * Get questionnaire status display name\r\n * @param {number} statusId - The questionnaire status ID\r\n * @returns {string} Display name of the questionnaire status\r\n */\nexport const getQuestionnaireStatusName = statusId => {\n  switch (statusId) {\n    case QuestionnaireStatus.Draft:\n      return 'Draft';\n    case QuestionnaireStatus.Published:\n      return 'Published';\n    case QuestionnaireStatus.Closed:\n      return 'Closed';\n    default:\n      return 'Unknown';\n  }\n};\n\n/**\r\n * Get questionnaire status CSS class for styling\r\n * @param {number} statusId - The questionnaire status ID\r\n * @returns {string} CSS class name for the status\r\n */\nexport const getQuestionnaireStatusClass = statusId => {\n  switch (statusId) {\n    case QuestionnaireStatus.Draft:\n      return 'status-draft';\n    case QuestionnaireStatus.Published:\n      return 'status-published';\n    case QuestionnaireStatus.Closed:\n      return 'status-closed';\n    default:\n      return 'status-unknown';\n  }\n};\n\n/**\r\n * Check if questionnaire status allows editing in the designer. \r\n * Note: If the questionnaire is closed, it can still be viewed in the designer, but it is not editable.\r\n * @param {number} statusId - The questionnaire status ID\r\n * @returns {boolean} True if questionnaire can be edited\r\n */\nexport const isQuestionnaireEditable = statusId => {\n  return statusId !== QuestionnaireStatus.Closed;\n};\n\n/**\r\n * Check if questionnaire is active (can be used for forms)\r\n * @param {number} statusId - The questionnaire status ID\r\n * @returns {boolean} True if questionnaire is active\r\n */\nexport const isQuestionnaireActive = statusId => {\n  return statusId === QuestionnaireStatus.Published;\n};\n\n/**\r\n * Get next possible statuses for a questionnaire\r\n * @param {number} currentStatusId - The current questionnaire status ID\r\n * @returns {Array} Array of possible next status IDs\r\n */\nexport const getNextPossibleStatuses = currentStatusId => {\n  switch (currentStatusId) {\n    case QuestionnaireStatus.Draft:\n      return [QuestionnaireStatus.Published];\n    case QuestionnaireStatus.Published:\n      return [QuestionnaireStatus.Closed];\n    // Can close after EndYear cycle published\n    case QuestionnaireStatus.Closed:\n      return [];\n    // No transitions from closed\n    default:\n      return [];\n  }\n};", "map": {"version": 3, "names": ["QuestionnaireStatus", "Draft", "Published", "Closed", "getQuestionnaireStatusName", "statusId", "getQuestionnaireStatusClass", "isQuestionnaireEditable", "isQuestionnaireActive", "getNextPossibleStatuses", "currentStatusId"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/core/enumertions/questionnaireStatus.js"], "sourcesContent": ["/**\r\n * Questionnaire Status enumeration definitions.\r\n * Reference to Enumerations.QuestionnaireStatus in server side.\r\n * Reference to records in table dbo.[QuestionnaireStatus].\r\n * Work for questionnaire status management and display.\r\n */\r\nexport const QuestionnaireStatus = {\r\n  /**\r\n   * Draft status - Questionnaire is in draft state\r\n   */\r\n  Draft: 0,\r\n\r\n  /**\r\n   * Published status - Questionnaire has been published and is active\r\n   */\r\n  Published: 1,\r\n\r\n  /**\r\n   * Closed status - Questionnaire is closed after EndYear cycle published\r\n   */\r\n  Closed: 2\r\n};\r\n\r\n/**\r\n * Get questionnaire status display name\r\n * @param {number} statusId - The questionnaire status ID\r\n * @returns {string} Display name of the questionnaire status\r\n */\r\nexport const getQuestionnaireStatusName = (statusId) => {\r\n  switch (statusId) {\r\n    case QuestionnaireStatus.Draft:\r\n      return 'Draft';\r\n    case QuestionnaireStatus.Published:\r\n      return 'Published';\r\n    case QuestionnaireStatus.Closed:\r\n      return 'Closed';\r\n    default:\r\n      return 'Unknown';\r\n  }\r\n};\r\n\r\n/**\r\n * Get questionnaire status CSS class for styling\r\n * @param {number} statusId - The questionnaire status ID\r\n * @returns {string} CSS class name for the status\r\n */\r\nexport const getQuestionnaireStatusClass = (statusId) => {\r\n  switch (statusId) {\r\n    case QuestionnaireStatus.Draft:\r\n      return 'status-draft';\r\n    case QuestionnaireStatus.Published:\r\n      return 'status-published';\r\n    case QuestionnaireStatus.Closed:\r\n      return 'status-closed';\r\n    default:\r\n      return 'status-unknown';\r\n  }\r\n};\r\n\r\n/**\r\n * Check if questionnaire status allows editing in the designer. \r\n * Note: If the questionnaire is closed, it can still be viewed in the designer, but it is not editable.\r\n * @param {number} statusId - The questionnaire status ID\r\n * @returns {boolean} True if questionnaire can be edited\r\n */\r\nexport const isQuestionnaireEditable = (statusId) => {\r\n  return statusId !== QuestionnaireStatus.Closed;\r\n};\r\n\r\n/**\r\n * Check if questionnaire is active (can be used for forms)\r\n * @param {number} statusId - The questionnaire status ID\r\n * @returns {boolean} True if questionnaire is active\r\n */\r\nexport const isQuestionnaireActive = (statusId) => {\r\n  return statusId === QuestionnaireStatus.Published;\r\n};\r\n\r\n/**\r\n * Get next possible statuses for a questionnaire\r\n * @param {number} currentStatusId - The current questionnaire status ID\r\n * @returns {Array} Array of possible next status IDs\r\n */\r\nexport const getNextPossibleStatuses = (currentStatusId) => {\r\n  switch (currentStatusId) {\r\n    case QuestionnaireStatus.Draft:\r\n      return [QuestionnaireStatus.Published];\r\n    case QuestionnaireStatus.Published:\r\n      return [QuestionnaireStatus.Closed]; // Can close after EndYear cycle published\r\n    case QuestionnaireStatus.Closed:\r\n      return []; // No transitions from closed\r\n    default:\r\n      return [];\r\n  }\r\n};\r\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,mBAAmB,GAAG;EACjC;AACF;AACA;EACEC,KAAK,EAAE,CAAC;EAER;AACF;AACA;EACEC,SAAS,EAAE,CAAC;EAEZ;AACF;AACA;EACEC,MAAM,EAAE;AACV,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,0BAA0B,GAAIC,QAAQ,IAAK;EACtD,QAAQA,QAAQ;IACd,KAAKL,mBAAmB,CAACC,KAAK;MAC5B,OAAO,OAAO;IAChB,KAAKD,mBAAmB,CAACE,SAAS;MAChC,OAAO,WAAW;IACpB,KAAKF,mBAAmB,CAACG,MAAM;MAC7B,OAAO,QAAQ;IACjB;MACE,OAAO,SAAS;EACpB;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMG,2BAA2B,GAAID,QAAQ,IAAK;EACvD,QAAQA,QAAQ;IACd,KAAKL,mBAAmB,CAACC,KAAK;MAC5B,OAAO,cAAc;IACvB,KAAKD,mBAAmB,CAACE,SAAS;MAChC,OAAO,kBAAkB;IAC3B,KAAKF,mBAAmB,CAACG,MAAM;MAC7B,OAAO,eAAe;IACxB;MACE,OAAO,gBAAgB;EAC3B;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMI,uBAAuB,GAAIF,QAAQ,IAAK;EACnD,OAAOA,QAAQ,KAAKL,mBAAmB,CAACG,MAAM;AAChD,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMK,qBAAqB,GAAIH,QAAQ,IAAK;EACjD,OAAOA,QAAQ,KAAKL,mBAAmB,CAACE,SAAS;AACnD,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMO,uBAAuB,GAAIC,eAAe,IAAK;EAC1D,QAAQA,eAAe;IACrB,KAAKV,mBAAmB,CAACC,KAAK;MAC5B,OAAO,CAACD,mBAAmB,CAACE,SAAS,CAAC;IACxC,KAAKF,mBAAmB,CAACE,SAAS;MAChC,OAAO,CAACF,mBAAmB,CAACG,MAAM,CAAC;IAAE;IACvC,KAAKH,mBAAmB,CAACG,MAAM;MAC7B,OAAO,EAAE;IAAE;IACb;MACE,OAAO,EAAE;EACb;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}