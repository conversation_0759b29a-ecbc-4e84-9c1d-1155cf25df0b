{"ast": null, "code": "import { createErrorClass } from './createErrorClass';\nexport var SequenceError = createErrorClass(function (_super) {\n  return function SequenceErrorImpl(message) {\n    _super(this);\n    this.name = 'SequenceError';\n    this.message = message;\n  };\n});", "map": {"version": 3, "names": ["createErrorClass", "SequenceError", "_super", "SequenceErrorImpl", "message", "name"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\util\\SequenceError.ts"], "sourcesContent": ["import { createErrorClass } from './createErrorClass';\n\nexport interface SequenceError extends Error {}\n\nexport interface SequenceErrorCtor {\n  /**\n   * @deprecated Internal implementation detail. Do not construct error instances.\n   * Cannot be tagged as internal: https://github.com/ReactiveX/rxjs/issues/6269\n   */\n  new (message: string): SequenceError;\n}\n\n/**\n * An error thrown when something is wrong with the sequence of\n * values arriving on the observable.\n *\n * @see {@link operators/single}\n */\nexport const SequenceError: SequenceErrorCtor = createErrorClass(\n  (_super) =>\n    function SequenceErrorImpl(this: any, message: string) {\n      _super(this);\n      this.name = 'SequenceError';\n      this.message = message;\n    }\n);\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,oBAAoB;AAkBrD,OAAO,IAAMC,aAAa,GAAsBD,gBAAgB,CAC9D,UAACE,MAAM;EACL,gBAASC,iBAAiBA,CAAYC,OAAe;IACnDF,MAAM,CAAC,IAAI,CAAC;IACZ,IAAI,CAACG,IAAI,GAAG,eAAe;IAC3B,IAAI,CAACD,OAAO,GAAGA,OAAO;EACxB,CAAC;AAJD,CAIC,CACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}