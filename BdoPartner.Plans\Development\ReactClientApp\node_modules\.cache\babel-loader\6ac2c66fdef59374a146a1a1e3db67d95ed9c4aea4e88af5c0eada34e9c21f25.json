{"ast": null, "code": "\"use strict\";\n\nexports.__esModule = true;\nexports.default = void 0;\nvar _createWebStorage = _interopRequireDefault(require(\"./createWebStorage\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nvar _default = (0, _createWebStorage.default)('local');\nexports.default = _default;", "map": {"version": 3, "names": ["exports", "__esModule", "default", "_createWebStorage", "_interopRequireDefault", "require", "obj", "_default"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/node_modules/redux-persist/lib/storage/index.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.default = void 0;\n\nvar _createWebStorage = _interopRequireDefault(require(\"./createWebStorage\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar _default = (0, _createWebStorage.default)('local');\n\nexports.default = _default;"], "mappings": "AAAA,YAAY;;AAEZA,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACE,OAAO,GAAG,KAAK,CAAC;AAExB,IAAIC,iBAAiB,GAAGC,sBAAsB,CAACC,OAAO,CAAC,oBAAoB,CAAC,CAAC;AAE7E,SAASD,sBAAsBA,CAACE,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACL,UAAU,GAAGK,GAAG,GAAG;IAAEJ,OAAO,EAAEI;EAAI,CAAC;AAAE;AAE9F,IAAIC,QAAQ,GAAG,CAAC,CAAC,EAAEJ,iBAAiB,CAACD,OAAO,EAAE,OAAO,CAAC;AAEtDF,OAAO,CAACE,OAAO,GAAGK,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}