{"ast": null, "code": "export function arrRemove(arr, item) {\n  if (arr) {\n    var index = arr.indexOf(item);\n    0 <= index && arr.splice(index, 1);\n  }\n}", "map": {"version": 3, "names": ["arr<PERSON><PERSON><PERSON>", "arr", "item", "index", "indexOf", "splice"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\util\\arrRemove.ts"], "sourcesContent": ["/**\n * Removes an item from an array, mutating it.\n * @param arr The array to remove the item from\n * @param item The item to remove\n */\nexport function arrRemove<T>(arr: T[] | undefined | null, item: T) {\n  if (arr) {\n    const index = arr.indexOf(item);\n    0 <= index && arr.splice(index, 1);\n  }\n}\n"], "mappings": "AAKA,OAAM,SAAUA,SAASA,CAAIC,GAA2B,EAAEC,IAAO;EAC/D,IAAID,GAAG,EAAE;IACP,IAAME,KAAK,GAAGF,GAAG,CAACG,OAAO,CAACF,IAAI,CAAC;IAC/B,CAAC,IAAIC,KAAK,IAAIF,GAAG,CAACI,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;;AAEtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}