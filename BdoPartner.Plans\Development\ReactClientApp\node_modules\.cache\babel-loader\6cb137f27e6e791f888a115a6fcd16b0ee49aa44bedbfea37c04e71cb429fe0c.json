{"ast": null, "code": "var _jsxFileName = \"C:\\\\git\\\\Partner-Site\\\\BdoPartner.Plans\\\\Development\\\\ReactClientApp\\\\src\\\\core\\\\auth\\\\components\\\\callback.jsx\",\n  _s = $RefreshSig$();\nimport * as React from \"react\";\nimport { useEffect, useState, useContext } from \"react\";\nimport { AuthContext } from \"./authProvider\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport const Callback = () => {\n  _s();\n  const [error, setError] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const {\n    signinRedirectCallback,\n    clearAuthState,\n    navigateToHome,\n    debugAuthState\n  } = useContext(AuthContext);\n  useEffect(() => {\n    const handleCallback = async () => {\n      try {\n        setLoading(true);\n        // Debug current state\n        if (debugAuthState) {\n          debugAuthState();\n        }\n        await signinRedirectCallback();\n      } catch (err) {\n        console.error(\"Authentication callback error:\", err);\n        setError(err.message);\n        // Clear stale state and redirect to home after a delay\n        clearAuthState();\n        setTimeout(() => {\n          navigateToHome();\n        }, 3000);\n      } finally {\n        setLoading(false);\n      }\n    };\n    handleCallback();\n  }, [signinRedirectCallback, clearAuthState, navigateToHome, debugAuthState]);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: \"Processing authentication...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 16\n    }, this);\n  }\n\n  // if (error) {\n  //     return (\n  //         <div>\n  //             <p>Authentication error: {error}</p>\n  //             <p>Redirecting to home page...</p>\n  //         </div>\n  //     );\n  // }\n\n  return /*#__PURE__*/_jsxDEV(_Fragment, {}, void 0, false);\n};\n_s(Callback, \"HOJv7zL2N2SJ9hvMC5hbOFI54nI=\");\n_c = Callback;\nvar _c;\n$RefreshReg$(_c, \"Callback\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useContext", "AuthContext", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Callback", "_s", "error", "setError", "loading", "setLoading", "signinRedirectCallback", "clearAuthState", "navigateToHome", "debugAuthState", "handleCallback", "err", "console", "message", "setTimeout", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/core/auth/components/callback.jsx"], "sourcesContent": ["import * as React from \"react\";\r\nimport { useEffect, useState, useContext } from \"react\";\r\n\r\nimport { AuthContext } from \"./authProvider\";\r\n\r\nexport const Callback = () => {\r\n    const [error, setError] = useState(null);\r\n    const [loading, setLoading] = useState(true);\r\n    const { signinRedirectCallback, clearAuthState, navigateToHome, debugAuthState } = useContext(AuthContext);\r\n\r\n    useEffect(() => {\r\n        const handleCallback = async () => {\r\n            try {\r\n                setLoading(true);\r\n                // Debug current state\r\n                if (debugAuthState) {\r\n                    debugAuthState();\r\n                }\r\n                await signinRedirectCallback();\r\n            } catch (err) {\r\n                console.error(\"Authentication callback error:\", err);\r\n                setError(err.message);\r\n                // Clear stale state and redirect to home after a delay\r\n                clearAuthState();\r\n                setTimeout(() => {\r\n                    navigateToHome();\r\n                }, 3000);\r\n            } finally {\r\n                setLoading(false);\r\n            }\r\n        };\r\n\r\n        handleCallback();\r\n    }, [signinRedirectCallback, clearAuthState, navigateToHome, debugAuthState]);\r\n\r\n    if (loading) {\r\n        return <div>Processing authentication...</div>;\r\n    }\r\n\r\n    // if (error) {\r\n    //     return (\r\n    //         <div>\r\n    //             <p>Authentication error: {error}</p>\r\n    //             <p>Redirecting to home page...</p>\r\n    //         </div>\r\n    //     );\r\n    // }\r\n\r\n    return <></>;\r\n};"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,OAAO;AAEvD,SAASC,WAAW,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE7C,OAAO,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM;IAAEa,sBAAsB;IAAEC,cAAc;IAAEC,cAAc;IAAEC;EAAe,CAAC,GAAGf,UAAU,CAACC,WAAW,CAAC;EAE1GH,SAAS,CAAC,MAAM;IACZ,MAAMkB,cAAc,GAAG,MAAAA,CAAA,KAAY;MAC/B,IAAI;QACAL,UAAU,CAAC,IAAI,CAAC;QAChB;QACA,IAAII,cAAc,EAAE;UAChBA,cAAc,CAAC,CAAC;QACpB;QACA,MAAMH,sBAAsB,CAAC,CAAC;MAClC,CAAC,CAAC,OAAOK,GAAG,EAAE;QACVC,OAAO,CAACV,KAAK,CAAC,gCAAgC,EAAES,GAAG,CAAC;QACpDR,QAAQ,CAACQ,GAAG,CAACE,OAAO,CAAC;QACrB;QACAN,cAAc,CAAC,CAAC;QAChBO,UAAU,CAAC,MAAM;UACbN,cAAc,CAAC,CAAC;QACpB,CAAC,EAAE,IAAI,CAAC;MACZ,CAAC,SAAS;QACNH,UAAU,CAAC,KAAK,CAAC;MACrB;IACJ,CAAC;IAEDK,cAAc,CAAC,CAAC;EACpB,CAAC,EAAE,CAACJ,sBAAsB,EAAEC,cAAc,EAAEC,cAAc,EAAEC,cAAc,CAAC,CAAC;EAE5E,IAAIL,OAAO,EAAE;IACT,oBAAOP,OAAA;MAAAkB,QAAA,EAAK;IAA4B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAClD;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA,oBAAOtB,OAAA,CAAAE,SAAA,mBAAI,CAAC;AAChB,CAAC;AAACE,EAAA,CA5CWD,QAAQ;AAAAoB,EAAA,GAARpB,QAAQ;AAAA,IAAAoB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}