{"ast": null, "code": "import { bindCallbackInternals } from './bindCallbackInternals';\nexport function bindNodeCallback(callbackFunc, resultSelector, scheduler) {\n  return bindCallbackInternals(true, callbackFunc, resultSelector, scheduler);\n}", "map": {"version": 3, "names": ["bindCallbackInternals", "bindNodeCallback", "callback<PERSON><PERSON><PERSON>", "resultSelector", "scheduler"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\observable\\bindNodeCallback.ts"], "sourcesContent": ["/* @prettier */\nimport { Observable } from '../Observable';\nimport { SchedulerLike } from '../types';\nimport { bindCallbackInternals } from './bindCallbackInternals';\n\nexport function bindNodeCallback(\n  callbackFunc: (...args: any[]) => void,\n  resultSelector: (...args: any[]) => any,\n  scheduler?: SchedulerLike\n): (...args: any[]) => Observable<any>;\n\n// args is the arguments array and we push the callback on the rest tuple since the rest parameter must be last (only item) in a parameter list\nexport function bindNodeCallback<A extends readonly unknown[], R extends readonly unknown[]>(\n  callbackFunc: (...args: [...A, (err: any, ...res: R) => void]) => void,\n  schedulerLike?: SchedulerLike\n): (...arg: A) => Observable<R extends [] ? void : R extends [any] ? R[0] : R>;\n\n/**\n * Converts a Node.js-style callback API to a function that returns an\n * Observable.\n *\n * <span class=\"informal\">It's just like {@link bindCallback}, but the\n * callback is expected to be of type `callback(error, result)`.</span>\n *\n * `bindNodeCallback` is not an operator because its input and output are not\n * Observables. The input is a function `func` with some parameters, but the\n * last parameter must be a callback function that `func` calls when it is\n * done. The callback function is expected to follow Node.js conventions,\n * where the first argument to the callback is an error object, signaling\n * whether call was successful. If that object is passed to callback, it means\n * something went wrong.\n *\n * The output of `bindNodeCallback` is a function that takes the same\n * parameters as `func`, except the last one (the callback). When the output\n * function is called with arguments, it will return an Observable.\n * If `func` calls its callback with error parameter present, Observable will\n * error with that value as well. If error parameter is not passed, Observable will emit\n * second parameter. If there are more parameters (third and so on),\n * Observable will emit an array with all arguments, except first error argument.\n *\n * Note that `func` will not be called at the same time output function is,\n * but rather whenever resulting Observable is subscribed. By default call to\n * `func` will happen synchronously after subscription, but that can be changed\n * with proper `scheduler` provided as optional third parameter. {@link SchedulerLike}\n * can also control when values from callback will be emitted by Observable.\n * To find out more, check out documentation for {@link bindCallback}, where\n * {@link SchedulerLike} works exactly the same.\n *\n * As in {@link bindCallback}, context (`this` property) of input function will be set to context\n * of returned function, when it is called.\n *\n * After Observable emits value, it will complete immediately. This means\n * even if `func` calls callback again, values from second and consecutive\n * calls will never appear on the stream. If you need to handle functions\n * that call callbacks multiple times, check out {@link fromEvent} or\n * {@link fromEventPattern} instead.\n *\n * Note that `bindNodeCallback` can be used in non-Node.js environments as well.\n * \"Node.js-style\" callbacks are just a convention, so if you write for\n * browsers or any other environment and API you use implements that callback style,\n * `bindNodeCallback` can be safely used on that API functions as well.\n *\n * Remember that Error object passed to callback does not have to be an instance\n * of JavaScript built-in `Error` object. In fact, it does not even have to an object.\n * Error parameter of callback function is interpreted as \"present\", when value\n * of that parameter is truthy. It could be, for example, non-zero number, non-empty\n * string or boolean `true`. In all of these cases resulting Observable would error\n * with that value. This means usually regular style callbacks will fail very often when\n * `bindNodeCallback` is used. If your Observable errors much more often then you\n * would expect, check if callback really is called in Node.js-style and, if not,\n * switch to {@link bindCallback} instead.\n *\n * Note that even if error parameter is technically present in callback, but its value\n * is falsy, it still won't appear in array emitted by Observable.\n *\n * ## Examples\n *\n *  Read a file from the filesystem and get the data as an Observable\n *\n * ```ts\n * import * as fs from 'fs';\n * const readFileAsObservable = bindNodeCallback(fs.readFile);\n * const result = readFileAsObservable('./roadNames.txt', 'utf8');\n * result.subscribe(x => console.log(x), e => console.error(e));\n * ```\n *\n * Use on function calling callback with multiple arguments\n *\n * ```ts\n * someFunction((err, a, b) => {\n *   console.log(err); // null\n *   console.log(a); // 5\n *   console.log(b); // \"some string\"\n * });\n * const boundSomeFunction = bindNodeCallback(someFunction);\n * boundSomeFunction()\n * .subscribe(value => {\n *   console.log(value); // [5, \"some string\"]\n * });\n * ```\n *\n * Use on function calling callback in regular style\n *\n * ```ts\n * someFunction(a => {\n *   console.log(a); // 5\n * });\n * const boundSomeFunction = bindNodeCallback(someFunction);\n * boundSomeFunction()\n * .subscribe(\n *   value => {}             // never gets called\n *   err => console.log(err) // 5\n * );\n * ```\n *\n * @see {@link bindCallback}\n * @see {@link from}\n *\n * @param callbackFunc Function with a Node.js-style callback as the last parameter.\n * @param resultSelector A mapping function used to transform callback events.\n * @param scheduler The scheduler on which to schedule the callbacks.\n * @return A function which returns the Observable that delivers the same values the\n * Node.js callback would deliver.\n */\nexport function bindNodeCallback(\n  callbackFunc: (...args: [...any[], (err: any, ...res: any) => void]) => void,\n  resultSelector?: ((...args: any[]) => any) | SchedulerLike,\n  scheduler?: SchedulerLike\n): (...args: any[]) => Observable<any> {\n  return bindCallbackInternals(true, callbackFunc, resultSelector, scheduler);\n}\n"], "mappings": "AAGA,SAASA,qBAAqB,QAAQ,yBAAyB;AAyH/D,OAAM,SAAUC,gBAAgBA,CAC9BC,YAA4E,EAC5EC,cAA0D,EAC1DC,SAAyB;EAEzB,OAAOJ,qBAAqB,CAAC,IAAI,EAAEE,YAAY,EAAEC,cAAc,EAAEC,SAAS,CAAC;AAC7E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}