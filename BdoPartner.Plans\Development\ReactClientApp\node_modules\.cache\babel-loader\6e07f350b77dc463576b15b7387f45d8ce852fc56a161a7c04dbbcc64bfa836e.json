{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nimport { noop } from '../util/noop';\nexport function skipUntil(notifier) {\n  return operate(function (source, subscriber) {\n    var taking = false;\n    var skipSubscriber = createOperatorSubscriber(subscriber, function () {\n      skipSubscriber === null || skipSubscriber === void 0 ? void 0 : skipSubscriber.unsubscribe();\n      taking = true;\n    }, noop);\n    innerFrom(notifier).subscribe(skipSubscriber);\n    source.subscribe(createOperatorSubscriber(subscriber, function (value) {\n      return taking && subscriber.next(value);\n    }));\n  });\n}", "map": {"version": 3, "names": ["operate", "createOperatorSubscriber", "innerFrom", "noop", "<PERSON><PERSON><PERSON><PERSON>", "notifier", "source", "subscriber", "taking", "skipSubscriber", "unsubscribe", "subscribe", "value", "next"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\skipUntil.ts"], "sourcesContent": ["import { MonoTypeOperatorFunction, ObservableInput } from '../types';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nimport { noop } from '../util/noop';\n\n/**\n * Returns an Observable that skips items emitted by the source Observable until a second Observable emits an item.\n *\n * The `skipUntil` operator causes the observable stream to skip the emission of values until the passed in observable\n * emits the first value. This can be particularly useful in combination with user interactions, responses of HTTP\n * requests or waiting for specific times to pass by.\n *\n * ![](skipUntil.png)\n *\n * Internally, the `skipUntil` operator subscribes to the passed in `notifier` `ObservableInput` (which gets converted\n * to an Observable) in order to recognize the emission of its first value. When `notifier` emits next, the operator\n * unsubscribes from it and starts emitting the values of the *source* observable until it completes or errors. It\n * will never let the *source* observable emit any values if the `notifier` completes or throws an error without\n * emitting a value before.\n *\n * ## Example\n *\n * In the following example, all emitted values of the interval observable are skipped until the user clicks anywhere\n * within the page\n *\n * ```ts\n * import { interval, fromEvent, skipUntil } from 'rxjs';\n *\n * const intervalObservable = interval(1000);\n * const click = fromEvent(document, 'click');\n *\n * const emitAfterClick = intervalObservable.pipe(\n *   skipUntil(click)\n * );\n * // clicked at 4.6s. output: 5...6...7...8........ or\n * // clicked at 7.3s. output: 8...9...10..11.......\n * emitAfterClick.subscribe(value => console.log(value));\n * ```\n *\n * @see {@link last}\n * @see {@link skip}\n * @see {@link skipWhile}\n * @see {@link skipLast}\n *\n * @param notifier An `ObservableInput` that has to emit an item before the source Observable elements begin to\n * be mirrored by the resulting Observable.\n * @return A function that returns an Observable that skips items from the\n * source Observable until the `notifier` Observable emits an item, then emits the\n * remaining items.\n */\nexport function skipUntil<T>(notifier: ObservableInput<any>): MonoTypeOperatorFunction<T> {\n  return operate((source, subscriber) => {\n    let taking = false;\n\n    const skipSubscriber = createOperatorSubscriber(\n      subscriber,\n      () => {\n        skipSubscriber?.unsubscribe();\n        taking = true;\n      },\n      noop\n    );\n\n    innerFrom(notifier).subscribe(skipSubscriber);\n\n    source.subscribe(createOperatorSubscriber(subscriber, (value) => taking && subscriber.next(value)));\n  });\n}\n"], "mappings": "AACA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,SAASC,SAAS,QAAQ,yBAAyB;AACnD,SAASC,IAAI,QAAQ,cAAc;AA+CnC,OAAM,SAAUC,SAASA,CAAIC,QAA8B;EACzD,OAAOL,OAAO,CAAC,UAACM,MAAM,EAAEC,UAAU;IAChC,IAAIC,MAAM,GAAG,KAAK;IAElB,IAAMC,cAAc,GAAGR,wBAAwB,CAC7CM,UAAU,EACV;MACEE,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEC,WAAW,EAAE;MAC7BF,MAAM,GAAG,IAAI;IACf,CAAC,EACDL,IAAI,CACL;IAEDD,SAAS,CAACG,QAAQ,CAAC,CAACM,SAAS,CAACF,cAAc,CAAC;IAE7CH,MAAM,CAACK,SAAS,CAACV,wBAAwB,CAACM,UAAU,EAAE,UAACK,KAAK;MAAK,OAAAJ,MAAM,IAAID,UAAU,CAACM,IAAI,CAACD,KAAK,CAAC;IAAhC,CAAgC,CAAC,CAAC;EACrG,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}