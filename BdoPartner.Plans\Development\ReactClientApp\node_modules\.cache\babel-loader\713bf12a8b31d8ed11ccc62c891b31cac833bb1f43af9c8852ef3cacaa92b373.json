{"ast": null, "code": "import { asyncScheduler } from '../scheduler/async';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function debounceTime(dueTime, scheduler) {\n  if (scheduler === void 0) {\n    scheduler = asyncScheduler;\n  }\n  return operate(function (source, subscriber) {\n    var activeTask = null;\n    var lastValue = null;\n    var lastTime = null;\n    var emit = function () {\n      if (activeTask) {\n        activeTask.unsubscribe();\n        activeTask = null;\n        var value = lastValue;\n        lastValue = null;\n        subscriber.next(value);\n      }\n    };\n    function emitWhenIdle() {\n      var targetTime = lastTime + dueTime;\n      var now = scheduler.now();\n      if (now < targetTime) {\n        activeTask = this.schedule(undefined, targetTime - now);\n        subscriber.add(activeTask);\n        return;\n      }\n      emit();\n    }\n    source.subscribe(createOperatorSubscriber(subscriber, function (value) {\n      lastValue = value;\n      lastTime = scheduler.now();\n      if (!activeTask) {\n        activeTask = scheduler.schedule(emitWhenIdle, dueTime);\n        subscriber.add(activeTask);\n      }\n    }, function () {\n      emit();\n      subscriber.complete();\n    }, undefined, function () {\n      lastValue = activeTask = null;\n    }));\n  });\n}", "map": {"version": 3, "names": ["asyncScheduler", "operate", "createOperatorSubscriber", "debounceTime", "dueTime", "scheduler", "source", "subscriber", "activeTask", "lastValue", "lastTime", "emit", "unsubscribe", "value", "next", "emitWhenIdle", "targetTime", "now", "schedule", "undefined", "add", "subscribe", "complete"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\debounceTime.ts"], "sourcesContent": ["import { asyncScheduler } from '../scheduler/async';\nimport { Subscription } from '../Subscription';\nimport { MonoTypeOperatorFunction, SchedulerAction, SchedulerLike } from '../types';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\n\n/**\n * Emits a notification from the source Observable only after a particular time span\n * has passed without another source emission.\n *\n * <span class=\"informal\">It's like {@link delay}, but passes only the most\n * recent notification from each burst of emissions.</span>\n *\n * ![](debounceTime.png)\n *\n * `debounceTime` delays notifications emitted by the source Observable, but drops\n * previous pending delayed emissions if a new notification arrives on the source\n * Observable. This operator keeps track of the most recent notification from the\n * source Observable, and emits that only when `dueTime` has passed\n * without any other notification appearing on the source Observable. If a new value\n * appears before `dueTime` silence occurs, the previous notification will be dropped\n * and will not be emitted and a new `dueTime` is scheduled.\n * If the completing event happens during `dueTime` the last cached notification\n * is emitted before the completion event is forwarded to the output observable.\n * If the error event happens during `dueTime` or after it only the error event is\n * forwarded to the output observable. The cache notification is not emitted in this case.\n *\n * This is a rate-limiting operator, because it is impossible for more than one\n * notification to be emitted in any time window of duration `dueTime`, but it is also\n * a delay-like operator since output emissions do not occur at the same time as\n * they did on the source Observable. Optionally takes a {@link SchedulerLike} for\n * managing timers.\n *\n * ## Example\n *\n * Emit the most recent click after a burst of clicks\n *\n * ```ts\n * import { fromEvent, debounceTime } from 'rxjs';\n *\n * const clicks = fromEvent(document, 'click');\n * const result = clicks.pipe(debounceTime(1000));\n * result.subscribe(x => console.log(x));\n * ```\n *\n * @see {@link audit}\n * @see {@link auditTime}\n * @see {@link debounce}\n * @see {@link sample}\n * @see {@link sampleTime}\n * @see {@link throttle}\n * @see {@link throttleTime}\n *\n * @param dueTime The timeout duration in milliseconds (or the time unit determined\n * internally by the optional `scheduler`) for the window of time required to wait\n * for emission silence before emitting the most recent source value.\n * @param scheduler The {@link SchedulerLike} to use for managing the timers that\n * handle the timeout for each value.\n * @return A function that returns an Observable that delays the emissions of\n * the source Observable by the specified `dueTime`, and may drop some values\n * if they occur too frequently.\n */\nexport function debounceTime<T>(dueTime: number, scheduler: SchedulerLike = asyncScheduler): MonoTypeOperatorFunction<T> {\n  return operate((source, subscriber) => {\n    let activeTask: Subscription | null = null;\n    let lastValue: T | null = null;\n    let lastTime: number | null = null;\n\n    const emit = () => {\n      if (activeTask) {\n        // We have a value! Free up memory first, then emit the value.\n        activeTask.unsubscribe();\n        activeTask = null;\n        const value = lastValue!;\n        lastValue = null;\n        subscriber.next(value);\n      }\n    };\n    function emitWhenIdle(this: SchedulerAction<unknown>) {\n      // This is called `dueTime` after the first value\n      // but we might have received new values during this window!\n\n      const targetTime = lastTime! + dueTime;\n      const now = scheduler.now();\n      if (now < targetTime) {\n        // On that case, re-schedule to the new target\n        activeTask = this.schedule(undefined, targetTime - now);\n        subscriber.add(activeTask);\n        return;\n      }\n\n      emit();\n    }\n\n    source.subscribe(\n      createOperatorSubscriber(\n        subscriber,\n        (value: T) => {\n          lastValue = value;\n          lastTime = scheduler.now();\n\n          // Only set up a task if it's not already up\n          if (!activeTask) {\n            activeTask = scheduler.schedule(emitWhenIdle, dueTime);\n            subscriber.add(activeTask);\n          }\n        },\n        () => {\n          // Source completed.\n          // Emit any pending debounced values then complete\n          emit();\n          subscriber.complete();\n        },\n        // Pass all errors through to consumer.\n        undefined,\n        () => {\n          // Finalization.\n          lastValue = activeTask = null;\n        }\n      )\n    );\n  });\n}\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,oBAAoB;AAGnD,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AA0D/D,OAAM,SAAUC,YAAYA,CAAIC,OAAe,EAAEC,SAAyC;EAAzC,IAAAA,SAAA;IAAAA,SAAA,GAAAL,cAAyC;EAAA;EACxF,OAAOC,OAAO,CAAC,UAACK,MAAM,EAAEC,UAAU;IAChC,IAAIC,UAAU,GAAwB,IAAI;IAC1C,IAAIC,SAAS,GAAa,IAAI;IAC9B,IAAIC,QAAQ,GAAkB,IAAI;IAElC,IAAMC,IAAI,GAAG,SAAAA,CAAA;MACX,IAAIH,UAAU,EAAE;QAEdA,UAAU,CAACI,WAAW,EAAE;QACxBJ,UAAU,GAAG,IAAI;QACjB,IAAMK,KAAK,GAAGJ,SAAU;QACxBA,SAAS,GAAG,IAAI;QAChBF,UAAU,CAACO,IAAI,CAACD,KAAK,CAAC;;IAE1B,CAAC;IACD,SAASE,YAAYA,CAAA;MAInB,IAAMC,UAAU,GAAGN,QAAS,GAAGN,OAAO;MACtC,IAAMa,GAAG,GAAGZ,SAAS,CAACY,GAAG,EAAE;MAC3B,IAAIA,GAAG,GAAGD,UAAU,EAAE;QAEpBR,UAAU,GAAG,IAAI,CAACU,QAAQ,CAACC,SAAS,EAAEH,UAAU,GAAGC,GAAG,CAAC;QACvDV,UAAU,CAACa,GAAG,CAACZ,UAAU,CAAC;QAC1B;;MAGFG,IAAI,EAAE;IACR;IAEAL,MAAM,CAACe,SAAS,CACdnB,wBAAwB,CACtBK,UAAU,EACV,UAACM,KAAQ;MACPJ,SAAS,GAAGI,KAAK;MACjBH,QAAQ,GAAGL,SAAS,CAACY,GAAG,EAAE;MAG1B,IAAI,CAACT,UAAU,EAAE;QACfA,UAAU,GAAGH,SAAS,CAACa,QAAQ,CAACH,YAAY,EAAEX,OAAO,CAAC;QACtDG,UAAU,CAACa,GAAG,CAACZ,UAAU,CAAC;;IAE9B,CAAC,EACD;MAGEG,IAAI,EAAE;MACNJ,UAAU,CAACe,QAAQ,EAAE;IACvB,CAAC,EAEDH,SAAS,EACT;MAEEV,SAAS,GAAGD,UAAU,GAAG,IAAI;IAC/B,CAAC,CACF,CACF;EACH,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}