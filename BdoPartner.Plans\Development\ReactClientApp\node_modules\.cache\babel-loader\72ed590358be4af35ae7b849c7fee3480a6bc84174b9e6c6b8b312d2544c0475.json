{"ast": null, "code": "import { iterator as Symbol_iterator } from '../symbol/iterator';\nimport { isFunction } from './isFunction';\nexport function isIterable(input) {\n  return isFunction(input === null || input === void 0 ? void 0 : input[Symbol_iterator]);\n}", "map": {"version": 3, "names": ["iterator", "Symbol_iterator", "isFunction", "isIterable", "input"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\util\\isIterable.ts"], "sourcesContent": ["import { iterator as Symbol_iterator } from '../symbol/iterator';\nimport { isFunction } from './isFunction';\n\n/** Identifies an input as being an Iterable */\nexport function isIterable(input: any): input is Iterable<any> {\n  return isFunction(input?.[Symbol_iterator]);\n}\n"], "mappings": "AAAA,SAASA,QAAQ,IAAIC,eAAe,QAAQ,oBAAoB;AAChE,SAASC,UAAU,QAAQ,cAAc;AAGzC,OAAM,SAAUC,UAAUA,CAACC,KAAU;EACnC,OAAOF,UAAU,CAACE,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAGH,eAAe,CAAC,CAAC;AAC7C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}