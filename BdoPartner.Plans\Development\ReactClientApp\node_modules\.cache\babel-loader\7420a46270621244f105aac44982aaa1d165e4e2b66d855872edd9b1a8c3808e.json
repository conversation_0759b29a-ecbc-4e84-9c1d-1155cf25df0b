{"ast": null, "code": "import { combineLatest } from '../observable/combineLatest';\nimport { joinAllInternals } from './joinAllInternals';\nexport function combineLatestAll(project) {\n  return joinAllInternals(combineLatest, project);\n}", "map": {"version": 3, "names": ["combineLatest", "joinAllInternals", "combineLatestAll", "project"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\combineLatestAll.ts"], "sourcesContent": ["import { combineLatest } from '../observable/combineLatest';\nimport { OperatorFunction, ObservableInput } from '../types';\nimport { joinAllInternals } from './joinAllInternals';\n\nexport function combineLatestAll<T>(): OperatorFunction<ObservableInput<T>, T[]>;\nexport function combineLatestAll<T>(): OperatorFunction<any, T[]>;\nexport function combineLatestAll<T, R>(project: (...values: T[]) => R): OperatorFunction<ObservableInput<T>, R>;\nexport function combineLatestAll<R>(project: (...values: Array<any>) => R): OperatorFunction<any, R>;\n\n/**\n * Flattens an Observable-of-Observables by applying {@link combineLatest} when the Observable-of-Observables completes.\n *\n * `combineLatestAll` takes an Observable of Observables, and collects all Observables from it. Once the outer Observable completes,\n * it subscribes to all collected Observables and combines their values using the {@link combineLatest} strategy, such that:\n *\n * * Every time an inner Observable emits, the output Observable emits\n * * When the returned observable emits, it emits all of the latest values by:\n *    * If a `project` function is provided, it is called with each recent value from each inner Observable in whatever order they\n *      arrived, and the result of the `project` function is what is emitted by the output Observable.\n *    * If there is no `project` function, an array of all the most recent values is emitted by the output Observable.\n *\n * ## Example\n *\n * Map two click events to a finite interval Observable, then apply `combineLatestAll`\n *\n * ```ts\n * import { fromEvent, map, interval, take, combineLatestAll } from 'rxjs';\n *\n * const clicks = fromEvent(document, 'click');\n * const higherOrder = clicks.pipe(\n *   map(() => interval(Math.random() * 2000).pipe(take(3))),\n *   take(2)\n * );\n * const result = higherOrder.pipe(combineLatestAll());\n *\n * result.subscribe(x => console.log(x));\n * ```\n *\n * @see {@link combineLatest}\n * @see {@link combineLatestWith}\n * @see {@link mergeAll}\n *\n * @param project optional function to map the most recent values from each inner Observable into a new result.\n * Takes each of the most recent values from each collected inner Observable as arguments, in order.\n * @return A function that returns an Observable that flattens Observables\n * emitted by the source Observable.\n */\nexport function combineLatestAll<R>(project?: (...values: Array<any>) => R) {\n  return joinAllInternals(combineLatest, project);\n}\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,6BAA6B;AAE3D,SAASC,gBAAgB,QAAQ,oBAAoB;AA6CrD,OAAM,SAAUC,gBAAgBA,CAAIC,OAAsC;EACxE,OAAOF,gBAAgB,CAACD,aAAa,EAAEG,OAAO,CAAC;AACjD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}