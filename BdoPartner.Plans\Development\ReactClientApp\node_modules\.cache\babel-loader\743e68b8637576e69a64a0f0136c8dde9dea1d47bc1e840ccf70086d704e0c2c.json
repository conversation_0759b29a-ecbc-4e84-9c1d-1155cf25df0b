{"ast": null, "code": "import { combineReducers } from 'redux';\nimport persistReducer from './persistReducer';\nimport autoMergeLevel2 from './stateReconciler/autoMergeLevel2';\n// combineReducers + persistReducer with stateReconciler defaulted to autoMergeLevel2\nexport default function persistCombineReducers(config, reducers) {\n  config.stateReconciler = config.stateReconciler === undefined ? autoMergeLevel2 : config.stateReconciler;\n  return persistReducer(config, combineReducers(reducers));\n}", "map": {"version": 3, "names": ["combineReducers", "persistReducer", "autoMergeLevel2", "persistCombineReducers", "config", "reducers", "stateReconciler", "undefined"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/node_modules/redux-persist/es/persistCombineReducers.js"], "sourcesContent": ["import { combineReducers } from 'redux';\nimport persistReducer from './persistReducer';\nimport autoMergeLevel2 from './stateReconciler/autoMergeLevel2';\n// combineReducers + persistReducer with stateReconciler defaulted to autoMergeLevel2\nexport default function persistCombineReducers(config, reducers) {\n  config.stateReconciler = config.stateReconciler === undefined ? autoMergeLevel2 : config.stateReconciler;\n  return persistReducer(config, combineReducers(reducers));\n}"], "mappings": "AAAA,SAASA,eAAe,QAAQ,OAAO;AACvC,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,eAAe,MAAM,mCAAmC;AAC/D;AACA,eAAe,SAASC,sBAAsBA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EAC/DD,MAAM,CAACE,eAAe,GAAGF,MAAM,CAACE,eAAe,KAAKC,SAAS,GAAGL,eAAe,GAAGE,MAAM,CAACE,eAAe;EACxG,OAAOL,cAAc,CAACG,MAAM,EAAEJ,eAAe,CAACK,QAAQ,CAAC,CAAC;AAC1D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}