{"ast": null, "code": "import { mergeAll } from '../operators/mergeAll';\nimport { innerFrom } from './innerFrom';\nimport { EMPTY } from './empty';\nimport { popNumber, popScheduler } from '../util/args';\nimport { from } from './from';\nexport function merge() {\n  var args = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    args[_i] = arguments[_i];\n  }\n  var scheduler = popScheduler(args);\n  var concurrent = popNumber(args, Infinity);\n  var sources = args;\n  return !sources.length ? EMPTY : sources.length === 1 ? innerFrom(sources[0]) : mergeAll(concurrent)(from(sources, scheduler));\n}", "map": {"version": 3, "names": ["mergeAll", "innerFrom", "EMPTY", "popNumber", "popScheduler", "from", "merge", "args", "_i", "arguments", "length", "scheduler", "concurrent", "Infinity", "sources"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\observable\\merge.ts"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { ObservableInput, ObservableInputTuple, SchedulerLike } from '../types';\nimport { mergeAll } from '../operators/mergeAll';\nimport { innerFrom } from './innerFrom';\nimport { EMPTY } from './empty';\nimport { popNumber, popScheduler } from '../util/args';\nimport { from } from './from';\n\nexport function merge<A extends readonly unknown[]>(...sources: [...ObservableInputTuple<A>]): Observable<A[number]>;\nexport function merge<A extends readonly unknown[]>(...sourcesAndConcurrency: [...ObservableInputTuple<A>, number?]): Observable<A[number]>;\n/** @deprecated The `scheduler` parameter will be removed in v8. Use `scheduled` and `mergeAll`. Details: https://rxjs.dev/deprecations/scheduler-argument */\nexport function merge<A extends readonly unknown[]>(\n  ...sourcesAndScheduler: [...ObservableInputTuple<A>, SchedulerLike?]\n): Observable<A[number]>;\n/** @deprecated The `scheduler` parameter will be removed in v8. Use `scheduled` and `mergeAll`. Details: https://rxjs.dev/deprecations/scheduler-argument */\nexport function merge<A extends readonly unknown[]>(\n  ...sourcesAndConcurrencyAndScheduler: [...ObservableInputTuple<A>, number?, SchedulerLike?]\n): Observable<A[number]>;\n\n/**\n * Creates an output Observable which concurrently emits all values from every\n * given input Observable.\n *\n * <span class=\"informal\">Flattens multiple Observables together by blending\n * their values into one Observable.</span>\n *\n * ![](merge.png)\n *\n * `merge` subscribes to each given input Observable (as arguments), and simply\n * forwards (without doing any transformation) all the values from all the input\n * Observables to the output Observable. The output Observable only completes\n * once all input Observables have completed. Any error delivered by an input\n * Observable will be immediately emitted on the output Observable.\n *\n * ## Examples\n *\n * Merge together two Observables: 1s interval and clicks\n *\n * ```ts\n * import { merge, fromEvent, interval } from 'rxjs';\n *\n * const clicks = fromEvent(document, 'click');\n * const timer = interval(1000);\n * const clicksOrTimer = merge(clicks, timer);\n * clicksOrTimer.subscribe(x => console.log(x));\n *\n * // Results in the following:\n * // timer will emit ascending values, one every second(1000ms) to console\n * // clicks logs MouseEvents to console every time the \"document\" is clicked\n * // Since the two streams are merged you see these happening\n * // as they occur.\n * ```\n *\n * Merge together 3 Observables, but run only 2 concurrently\n *\n * ```ts\n * import { interval, take, merge } from 'rxjs';\n *\n * const timer1 = interval(1000).pipe(take(10));\n * const timer2 = interval(2000).pipe(take(6));\n * const timer3 = interval(500).pipe(take(10));\n *\n * const concurrent = 2; // the argument\n * const merged = merge(timer1, timer2, timer3, concurrent);\n * merged.subscribe(x => console.log(x));\n *\n * // Results in the following:\n * // - First timer1 and timer2 will run concurrently\n * // - timer1 will emit a value every 1000ms for 10 iterations\n * // - timer2 will emit a value every 2000ms for 6 iterations\n * // - after timer1 hits its max iteration, timer2 will\n * //   continue, and timer3 will start to run concurrently with timer2\n * // - when timer2 hits its max iteration it terminates, and\n * //   timer3 will continue to emit a value every 500ms until it is complete\n * ```\n *\n * @see {@link mergeAll}\n * @see {@link mergeMap}\n * @see {@link mergeMapTo}\n * @see {@link mergeScan}\n *\n * @param args `ObservableInput`s to merge together. If the last parameter\n * is of type number, `merge` will use it to limit number of concurrently\n * subscribed `ObservableInput`s. If the last parameter is {@link SchedulerLike},\n * it will be used for scheduling the emission of values.\n * @return An Observable that emits items that are the result of every input Observable.\n */\nexport function merge(...args: (ObservableInput<unknown> | number | SchedulerLike)[]): Observable<unknown> {\n  const scheduler = popScheduler(args);\n  const concurrent = popNumber(args, Infinity);\n  const sources = args as ObservableInput<unknown>[];\n  return !sources.length\n    ? // No source provided\n      EMPTY\n    : sources.length === 1\n    ? // One source? Just return it.\n      innerFrom(sources[0])\n    : // Merge all sources\n      mergeAll(concurrent)(from(sources, scheduler));\n}\n"], "mappings": "AAEA,SAASA,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,SAAS,QAAQ,aAAa;AACvC,SAASC,KAAK,QAAQ,SAAS;AAC/B,SAASC,SAAS,EAAEC,YAAY,QAAQ,cAAc;AACtD,SAASC,IAAI,QAAQ,QAAQ;AAiF7B,OAAM,SAAUC,KAAKA,CAAA;EAAC,IAAAC,IAAA;OAAA,IAAAC,EAAA,IAA8D,EAA9DA,EAAA,GAAAC,SAAA,CAAAC,MAA8D,EAA9DF,EAAA,EAA8D;IAA9DD,IAAA,CAAAC,EAAA,IAAAC,SAAA,CAAAD,EAAA;;EACpB,IAAMG,SAAS,GAAGP,YAAY,CAACG,IAAI,CAAC;EACpC,IAAMK,UAAU,GAAGT,SAAS,CAACI,IAAI,EAAEM,QAAQ,CAAC;EAC5C,IAAMC,OAAO,GAAGP,IAAkC;EAClD,OAAO,CAACO,OAAO,CAACJ,MAAM,GAElBR,KAAK,GACLY,OAAO,CAACJ,MAAM,KAAK,CAAC,GAEpBT,SAAS,CAACa,OAAO,CAAC,CAAC,CAAC,CAAC,GAErBd,QAAQ,CAACY,UAAU,CAAC,CAACP,IAAI,CAACS,OAAO,EAAEH,SAAS,CAAC,CAAC;AACpD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}