{"ast": null, "code": "/**\r\n * User Form Role enumeration definitions.\r\n * Reference to Enumerations.UserFormRole in server side.\r\n * Reference to records in table dbo.[FormAccessConfig] column \"UserRole\".\r\n * Note: It is different with UserRole in Identity database.\r\n * Work for user's role in relation to a specific form.\r\n */\nexport const UserFormRole = {\n  /**\r\n   * User has no access to the form\r\n   */\n  NoAccess: 0,\n  /**\r\n   * User is the form owner (partner)\r\n   */\n  FormOwner: 1,\n  /**\r\n   * User is the primary reviewer or Secondary reviewer for this form\r\n   */\n  Reviewer: 2,\n  /**\r\n   * User is an admin with full access\r\n   */\n  Admin: 3,\n  /**\r\n   * ExecutiveLeadership and he/she is not current process partner plan's reviewer\r\n   */\n  ELT: 4\n};\n\n/**\r\n * Get user form role display name\r\n * @param {number} roleId - The user form role ID\r\n * @returns {string} Display name of the user form role\r\n */\nexport const getUserFormRoleName = roleId => {\n  switch (roleId) {\n    case UserFormRole.NoAccess:\n      return 'No Access';\n    case UserFormRole.FormOwner:\n      return 'Form Owner';\n    case UserFormRole.Reviewer:\n      return 'Reviewer';\n    case UserFormRole.Admin:\n      return 'Admin';\n    case UserFormRole.ELT:\n      return 'Executive Leadership';\n    default:\n      return 'Unknown';\n  }\n};\n\n/**\r\n * Get user form role CSS class for styling\r\n * @param {number} roleId - The user form role ID\r\n * @returns {string} CSS class name for the role\r\n */\nexport const getUserFormRoleClass = roleId => {\n  switch (roleId) {\n    case UserFormRole.NoAccess:\n      return 'role-no-access';\n    case UserFormRole.FormOwner:\n      return 'role-form-owner';\n    case UserFormRole.Reviewer:\n      return 'role-reviewer';\n    case UserFormRole.Admin:\n      return 'role-admin';\n    case UserFormRole.ELT:\n      return 'role-elt';\n    default:\n      return 'role-unknown';\n  }\n};\n\n/**\r\n * Check if a user form role has edit permissions\r\n * @param {number} roleId - The user form role ID\r\n * @returns {boolean} True if the role has edit permissions\r\n */\nexport const canEditForm = roleId => {\n  return roleId === UserFormRole.FormOwner || roleId === UserFormRole.Admin;\n};\n\n/**\r\n * Check if a user form role has review permissions\r\n * @param {number} roleId - The user form role ID\r\n * @returns {boolean} True if the role has review permissions\r\n */\nexport const canReviewForm = roleId => {\n  return roleId === UserFormRole.Reviewer || roleId === UserFormRole.Admin;\n};\n\n/**\r\n * Check if a user form role has admin permissions\r\n * @param {number} roleId - The user form role ID\r\n * @returns {boolean} True if the role has admin permissions\r\n */\nexport const hasAdminAccess = roleId => {\n  return roleId === UserFormRole.Admin;\n};\n\n/**\r\n * Check if a user form role has any access to the form\r\n * @param {number} roleId - The user form role ID\r\n * @returns {boolean} True if the role has any access\r\n */\nexport const hasFormAccess = roleId => {\n  return roleId !== UserFormRole.NoAccess;\n};", "map": {"version": 3, "names": ["UserFormRole", "NoAccess", "FormOwner", "Reviewer", "Admin", "ELT", "getUserFormRoleName", "roleId", "getUserFormRoleClass", "canEditForm", "canReviewForm", "hasAdminAccess", "hasFormAccess"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/core/enumertions/userFormRole.js"], "sourcesContent": ["/**\r\n * User Form Role enumeration definitions.\r\n * Reference to Enumerations.UserFormRole in server side.\r\n * Reference to records in table dbo.[FormAccessConfig] column \"UserRole\".\r\n * Note: It is different with UserRole in Identity database.\r\n * Work for user's role in relation to a specific form.\r\n */\r\nexport const UserFormRole = {\r\n  /**\r\n   * User has no access to the form\r\n   */\r\n  NoAccess: 0,\r\n\r\n  /**\r\n   * User is the form owner (partner)\r\n   */\r\n  FormOwner: 1,\r\n\r\n  /**\r\n   * User is the primary reviewer or Secondary reviewer for this form\r\n   */\r\n  Reviewer: 2,\r\n\r\n  /**\r\n   * User is an admin with full access\r\n   */\r\n  Admin: 3,\r\n\r\n  /**\r\n   * ExecutiveLeadership and he/she is not current process partner plan's reviewer\r\n   */\r\n  ELT: 4\r\n};\r\n\r\n/**\r\n * Get user form role display name\r\n * @param {number} roleId - The user form role ID\r\n * @returns {string} Display name of the user form role\r\n */\r\nexport const getUserFormRoleName = (roleId) => {\r\n  switch (roleId) {\r\n    case UserFormRole.NoAccess:\r\n      return 'No Access';\r\n    case UserFormRole.FormOwner:\r\n      return 'Form Owner';\r\n    case UserFormRole.Reviewer:\r\n      return 'Reviewer';\r\n    case UserFormRole.Admin:\r\n      return 'Admin';\r\n    case UserFormRole.ELT:\r\n      return 'Executive Leadership';\r\n    default:\r\n      return 'Unknown';\r\n  }\r\n};\r\n\r\n/**\r\n * Get user form role CSS class for styling\r\n * @param {number} roleId - The user form role ID\r\n * @returns {string} CSS class name for the role\r\n */\r\nexport const getUserFormRoleClass = (roleId) => {\r\n  switch (roleId) {\r\n    case UserFormRole.NoAccess:\r\n      return 'role-no-access';\r\n    case UserFormRole.FormOwner:\r\n      return 'role-form-owner';\r\n    case UserFormRole.Reviewer:\r\n      return 'role-reviewer';\r\n    case UserFormRole.Admin:\r\n      return 'role-admin';\r\n    case UserFormRole.ELT:\r\n      return 'role-elt';\r\n    default:\r\n      return 'role-unknown';\r\n  }\r\n};\r\n\r\n/**\r\n * Check if a user form role has edit permissions\r\n * @param {number} roleId - The user form role ID\r\n * @returns {boolean} True if the role has edit permissions\r\n */\r\nexport const canEditForm = (roleId) => {\r\n  return roleId === UserFormRole.FormOwner ||\r\n         roleId === UserFormRole.Admin;\r\n};\r\n\r\n/**\r\n * Check if a user form role has review permissions\r\n * @param {number} roleId - The user form role ID\r\n * @returns {boolean} True if the role has review permissions\r\n */\r\nexport const canReviewForm = (roleId) => {\r\n  return roleId === UserFormRole.Reviewer ||\r\n         roleId === UserFormRole.Admin;\r\n};\r\n\r\n/**\r\n * Check if a user form role has admin permissions\r\n * @param {number} roleId - The user form role ID\r\n * @returns {boolean} True if the role has admin permissions\r\n */\r\nexport const hasAdminAccess = (roleId) => {\r\n  return roleId === UserFormRole.Admin;\r\n};\r\n\r\n/**\r\n * Check if a user form role has any access to the form\r\n * @param {number} roleId - The user form role ID\r\n * @returns {boolean} True if the role has any access\r\n */\r\nexport const hasFormAccess = (roleId) => {\r\n  return roleId !== UserFormRole.NoAccess;\r\n};\r\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,YAAY,GAAG;EAC1B;AACF;AACA;EACEC,QAAQ,EAAE,CAAC;EAEX;AACF;AACA;EACEC,SAAS,EAAE,CAAC;EAEZ;AACF;AACA;EACEC,QAAQ,EAAE,CAAC;EAEX;AACF;AACA;EACEC,KAAK,EAAE,CAAC;EAER;AACF;AACA;EACEC,GAAG,EAAE;AACP,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,mBAAmB,GAAIC,MAAM,IAAK;EAC7C,QAAQA,MAAM;IACZ,KAAKP,YAAY,CAACC,QAAQ;MACxB,OAAO,WAAW;IACpB,KAAKD,YAAY,CAACE,SAAS;MACzB,OAAO,YAAY;IACrB,KAAKF,YAAY,CAACG,QAAQ;MACxB,OAAO,UAAU;IACnB,KAAKH,YAAY,CAACI,KAAK;MACrB,OAAO,OAAO;IAChB,KAAKJ,YAAY,CAACK,GAAG;MACnB,OAAO,sBAAsB;IAC/B;MACE,OAAO,SAAS;EACpB;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMG,oBAAoB,GAAID,MAAM,IAAK;EAC9C,QAAQA,MAAM;IACZ,KAAKP,YAAY,CAACC,QAAQ;MACxB,OAAO,gBAAgB;IACzB,KAAKD,YAAY,CAACE,SAAS;MACzB,OAAO,iBAAiB;IAC1B,KAAKF,YAAY,CAACG,QAAQ;MACxB,OAAO,eAAe;IACxB,KAAKH,YAAY,CAACI,KAAK;MACrB,OAAO,YAAY;IACrB,KAAKJ,YAAY,CAACK,GAAG;MACnB,OAAO,UAAU;IACnB;MACE,OAAO,cAAc;EACzB;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMI,WAAW,GAAIF,MAAM,IAAK;EACrC,OAAOA,MAAM,KAAKP,YAAY,CAACE,SAAS,IACjCK,MAAM,KAAKP,YAAY,CAACI,KAAK;AACtC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMM,aAAa,GAAIH,MAAM,IAAK;EACvC,OAAOA,MAAM,KAAKP,YAAY,CAACG,QAAQ,IAChCI,MAAM,KAAKP,YAAY,CAACI,KAAK;AACtC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMO,cAAc,GAAIJ,MAAM,IAAK;EACxC,OAAOA,MAAM,KAAKP,YAAY,CAACI,KAAK;AACtC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMQ,aAAa,GAAIL,MAAM,IAAK;EACvC,OAAOA,MAAM,KAAKP,YAAY,CAACC,QAAQ;AACzC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}