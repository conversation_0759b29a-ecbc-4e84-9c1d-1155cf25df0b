{"ast": null, "code": "import { Subject } from \"rxjs\";\n\n/** Work for subscribe message object crossing components\r\n *  No need to export this const, since it only works with messageService.\r\n */\nconst messageSubject = new Subject();\n\n/** Temporary collection pool to keep all isEmit = false messages.\r\n *  Work for emit messages to the redirected page.\r\n *  Corporate with \"delayEmit\" method.\r\n * */\nconst messageSubjectPool = [];\n\n/***\r\n * Reference: https://jasonwatmore.com/post/2019/02/13/react-rxjs-communicating-between-components-with-observable-subject#:~:text=React%20%2B%20RxJS%20App%20Component%20that,divs%20in%20the%20render%20method.\r\n * Note: This is a single tone service approach.\r\n * https://www.digitalocean.com/community/tutorials/js-js-singletons\r\n */\nexport const messageService = {\n  /**\r\n   * Subscribe \"infor\" messages to messageBox component only.\r\n   * @info:  It is string content of the message.\r\n   * @isEmit: default value = false, keep info messages in temp colleciton first without show up in UI.\r\n   *          if isEmit = true, current message and other keeping messags will be show to UI immediately and temp collection in messageBox component will be clean up.\r\n   *\r\n   *\r\n   *  Message json format:\r\n   *\r\n   * {\r\n   *    content: '',\r\n   *    messageType: 'info' or 'success' or 'warn' or 'error' or 'confirmation' or 'deletionConfirmation'.\r\n   *    modalType: 'dialog' or 'snackbar' or 'toast',\r\n   *    isEmit: true/false, (Define if message show up immediately or keep it in temp collection first without show)\r\n   *    callback: (result) => {} // It is function reference passed from caller component through MessageService's subscribe.\r\n   * }\r\n   *\r\n   * */\n  info: (info = \"\", isEmit = true) => {\n    sendMessage(info, isEmit, \"snackbar\", \"info\");\n  },\n  warn: (warn = \"\", isEmit = true) => {\n    sendMessage(warn, isEmit, \"snackbar\", \"warn\");\n  },\n  error: (error = \"\", isEmit = true) => {\n    sendMessage(error, isEmit, \"snackbar\", \"error\");\n  },\n  success: (success = \"\", isEmit = true) => {\n    sendMessage(success, isEmit, \"snackbar\", \"success\");\n  },\n  infoToast: (info = \"\", isEmit = true) => {\n    sendMessage(info, isEmit, \"toast\", \"info\");\n  },\n  warnToast: (warn = \"\", isEmit = true) => {\n    sendMessage(warn, isEmit, \"toast\", \"warn\");\n  },\n  errorToast: (error = \"\", isEmit = true) => {\n    sendMessage(error, isEmit, \"toast\", \"error\");\n  },\n  successToast: (success = \"\", isEmit = true) => {\n    sendMessage(success, isEmit, \"toast\", \"success\");\n  },\n  /** Dialog to show information message only. */\n  infoDialog: (info = \"\") => {\n    sendMessage(info, true, \"dialog\", \"info\");\n  },\n  /** Dialog to show warn message only. */\n  warnDialog: (warn = \"\") => {\n    sendMessage(warn, true, \"dialog\", \"warn\");\n  },\n  /** Dialog to show error message only. */\n  errorDialog: (error = \"\") => {\n    sendMessage(error, true, \"dialog\", \"error\");\n  },\n  /** Dialog to show success message only. */\n  successDialog: (success = \"\") => {\n    sendMessage(success, true, \"dialog\", \"success\");\n  },\n  /** Confirm dialog. It has Yes and No two buttons */\n  confirmDialog: (content = \"\", callback) => {\n    sendMessageWithCallback(content, true, \"dialog\", \"confirmation\", callback);\n  },\n  /** Specialy for confirm deletion action process. It has Yes and No two buttons.  */\n  confirmDeletionDialog: (content = \"\", callback) => {\n    sendMessageWithCallback(content, true, \"dialog\", \"deletionConfirmation\", callback);\n  },\n  /**\r\n   *  Try to listen and receive pop messages from subscribe.\r\n   *  Message object json format:\r\n   *\r\n   *  {\r\n   *    content: '',\r\n   *    messageType: 'info' or 'success' or 'warn' or 'error' or 'confirmation' or 'deletionConfirmation'.\r\n   *    modalType: 'dialog' or 'snackbar' or 'toast',\r\n   *    isEmit: true/false,\r\n   *    // callback function work for confirmation dialog (yes and no choice)\r\n   *    callback?: (result) => {}\r\n   *  }\r\n   *\r\n   */\n  get: () => {\n    return messageSubject.asObservable();\n  },\n  /** Emit holding messages from collection pool, and display them in UI immediately. */\n  emit: () => {\n    setTimeout(() => {\n      if (messageSubjectPool && messageSubjectPool.length > 0) {\n        while (messageSubjectPool.length > 0) {\n          const m = messageSubjectPool.pop();\n          m.isEmit = true;\n          messageSubject.next(m);\n        }\n      }\n      const snackBarNotify = {\n        content: \"IsEmitNotify\",\n        modalType: \"snackbar\",\n        messageType: \"info\",\n        isEmit: true\n      };\n      const dialogNotify = {\n        content: \"IsEmitNotify\",\n        modalType: \"dialog\",\n        messageType: \"info\",\n        isEmit: true\n      };\n      const toastNotify = {\n        content: \"IsEmitNotify\",\n        modalType: \"toast\",\n        messageType: \"info\",\n        isEmit: true\n      };\n      messageSubject.next(snackBarNotify);\n      messageSubject.next(dialogNotify);\n      messageSubject.next(toastNotify);\n    }, 200);\n  }\n};\nfunction sendMessage(messageContent, isEmit, modelType, messageType) {\n  if (messageContent && messageContent.length > 0) {\n    const newMessage = {\n      content: messageContent,\n      modalType: modelType,\n      messageType: messageType,\n      isEmit: isEmit\n    };\n    if (isEmit) {\n      // Subscribe the message to components immediately.\n      messageSubject.next(newMessage);\n    } else {\n      //\n      // Keep the message in temporary collection pool.\n      //\n      messageSubjectPool.push(newMessage);\n    }\n  }\n}\n\n/**\r\n * Only work for confirmation dialog at this moment.\r\n * @param {*} messageContent\r\n * @param {*} isEmit\r\n * @param {*} modelType\r\n * @param {*} messageType\r\n * @param {*} callback callback function work for send back confirmation dialog response back to caller component.\r\n */\nfunction sendMessageWithCallback(messageContent, isEmit, modelType, messageType, callback) {\n  if (messageContent && messageContent.length > 0) {\n    const newMessage = {\n      content: messageContent,\n      modalType: modelType,\n      messageType: messageType,\n      isEmit: isEmit,\n      callback: callback\n    };\n    if (isEmit) {\n      //\n      // Subscribe the message to components immediately.\n      //\n      messageSubject.next(newMessage);\n    } else {\n      //\n      // Keep the message in temporary collection pool.\n      //\n      messageSubjectPool.push(newMessage);\n    }\n  }\n}", "map": {"version": 3, "names": ["Subject", "messageSubject", "messageSubjectPool", "messageService", "info", "isEmit", "sendMessage", "warn", "error", "success", "infoToast", "warnToast", "errorToast", "successToast", "infoDialog", "warnDialog", "errorDialog", "successDialog", "confirmDialog", "content", "callback", "sendMessageWithCallback", "confirmDeletionDialog", "get", "asObservable", "emit", "setTimeout", "length", "m", "pop", "next", "snackBarNotify", "modalType", "messageType", "dialogNotify", "toastNotify", "messageContent", "modelType", "newMessage", "push"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/core/message/messageService.js"], "sourcesContent": ["import { Subject } from \"rxjs\";\r\n\r\n/** Work for subscribe message object crossing components\r\n *  No need to export this const, since it only works with messageService.\r\n */\r\nconst messageSubject = new Subject();\r\n\r\n/** Temporary collection pool to keep all isEmit = false messages.\r\n *  Work for emit messages to the redirected page.\r\n *  Corporate with \"delayEmit\" method.\r\n * */\r\nconst messageSubjectPool = [];\r\n\r\n/***\r\n * Reference: https://jasonwatmore.com/post/2019/02/13/react-rxjs-communicating-between-components-with-observable-subject#:~:text=React%20%2B%20RxJS%20App%20Component%20that,divs%20in%20the%20render%20method.\r\n * Note: This is a single tone service approach.\r\n * https://www.digitalocean.com/community/tutorials/js-js-singletons\r\n */\r\nexport const messageService = {\r\n  /**\r\n   * Subscribe \"infor\" messages to messageBox component only.\r\n   * @info:  It is string content of the message.\r\n   * @isEmit: default value = false, keep info messages in temp colleciton first without show up in UI.\r\n   *          if isEmit = true, current message and other keeping messags will be show to UI immediately and temp collection in messageBox component will be clean up.\r\n   *\r\n   *\r\n   *  Message json format:\r\n   *\r\n   * {\r\n   *    content: '',\r\n   *    messageType: 'info' or 'success' or 'warn' or 'error' or 'confirmation' or 'deletionConfirmation'.\r\n   *    modalType: 'dialog' or 'snackbar' or 'toast',\r\n   *    isEmit: true/false, (Define if message show up immediately or keep it in temp collection first without show)\r\n   *    callback: (result) => {} // It is function reference passed from caller component through MessageService's subscribe.\r\n   * }\r\n   *\r\n   * */\r\n  info: (info = \"\", isEmit = true) => {\r\n    sendMessage(info, isEmit, \"snackbar\", \"info\");\r\n  },\r\n\r\n  warn: (warn = \"\", isEmit = true) => {\r\n    sendMessage(warn, isEmit, \"snackbar\", \"warn\");\r\n  },\r\n\r\n  error: (error = \"\", isEmit = true) => {\r\n    sendMessage(error, isEmit, \"snackbar\", \"error\");\r\n  },\r\n\r\n  success: (success = \"\", isEmit = true) => {\r\n    sendMessage(success, isEmit, \"snackbar\", \"success\");\r\n  },\r\n\r\n  infoToast: (info = \"\", isEmit = true) => {\r\n    sendMessage(info, isEmit, \"toast\", \"info\");\r\n  },\r\n\r\n  warnToast: (warn = \"\", isEmit = true) => {\r\n    sendMessage(warn, isEmit, \"toast\", \"warn\");\r\n  },\r\n\r\n  errorToast: (error = \"\", isEmit = true) => {\r\n    sendMessage(error, isEmit, \"toast\", \"error\");\r\n  },\r\n\r\n  successToast: (success = \"\", isEmit = true) => {\r\n    sendMessage(success, isEmit, \"toast\", \"success\");\r\n  },\r\n\r\n  /** Dialog to show information message only. */\r\n  infoDialog: (info = \"\") => {\r\n    sendMessage(info, true, \"dialog\", \"info\");\r\n  },\r\n\r\n  /** Dialog to show warn message only. */\r\n  warnDialog: (warn = \"\") => {\r\n    sendMessage(warn, true, \"dialog\", \"warn\");\r\n  },\r\n\r\n  /** Dialog to show error message only. */\r\n  errorDialog: (error = \"\") => {\r\n    sendMessage(error, true, \"dialog\", \"error\");\r\n  },\r\n\r\n  /** Dialog to show success message only. */\r\n  successDialog: (success = \"\") => {\r\n    sendMessage(success, true, \"dialog\", \"success\");\r\n  },\r\n\r\n  /** Confirm dialog. It has Yes and No two buttons */\r\n  confirmDialog: (content = \"\", callback) => {\r\n    sendMessageWithCallback(content, true, \"dialog\", \"confirmation\", callback);\r\n  },\r\n\r\n  /** Specialy for confirm deletion action process. It has Yes and No two buttons.  */\r\n  confirmDeletionDialog: (content = \"\", callback) => {\r\n    sendMessageWithCallback(\r\n      content,\r\n      true,\r\n      \"dialog\",\r\n      \"deletionConfirmation\",\r\n      callback\r\n    );\r\n  },\r\n  \r\n  /**\r\n   *  Try to listen and receive pop messages from subscribe.\r\n   *  Message object json format:\r\n   *\r\n   *  {\r\n   *    content: '',\r\n   *    messageType: 'info' or 'success' or 'warn' or 'error' or 'confirmation' or 'deletionConfirmation'.\r\n   *    modalType: 'dialog' or 'snackbar' or 'toast',\r\n   *    isEmit: true/false,\r\n   *    // callback function work for confirmation dialog (yes and no choice)\r\n   *    callback?: (result) => {}\r\n   *  }\r\n   *\r\n   */\r\n  get: () => {\r\n    return messageSubject.asObservable();\r\n  },\r\n\r\n  /** Emit holding messages from collection pool, and display them in UI immediately. */\r\n  emit: () => {\r\n    setTimeout(() => {\r\n      if (messageSubjectPool && messageSubjectPool.length > 0) {\r\n        while (messageSubjectPool.length > 0) {\r\n          const m = messageSubjectPool.pop();\r\n          m.isEmit = true;\r\n          messageSubject.next(m);\r\n        }\r\n      }\r\n\r\n      const snackBarNotify = {\r\n        content: \"IsEmitNotify\",\r\n        modalType: \"snackbar\",\r\n        messageType: \"info\",\r\n        isEmit: true,\r\n      };\r\n\r\n      const dialogNotify = {\r\n        content: \"IsEmitNotify\",\r\n        modalType: \"dialog\",\r\n        messageType: \"info\",\r\n        isEmit: true,\r\n      };\r\n\r\n      const toastNotify = {\r\n        content: \"IsEmitNotify\",\r\n        modalType: \"toast\",\r\n        messageType: \"info\",\r\n        isEmit: true,\r\n      };\r\n\r\n      messageSubject.next(snackBarNotify);\r\n      messageSubject.next(dialogNotify);\r\n      messageSubject.next(toastNotify);\r\n    }, 200);\r\n  },\r\n};\r\n\r\nfunction sendMessage(messageContent, isEmit, modelType, messageType) {\r\n  if (messageContent && messageContent.length > 0) {\r\n    const newMessage = {\r\n      content: messageContent,\r\n      modalType: modelType,\r\n      messageType: messageType,\r\n      isEmit: isEmit,\r\n    };\r\n    if (isEmit) {\r\n      // Subscribe the message to components immediately.\r\n      messageSubject.next(newMessage);\r\n    } else {\r\n      //\r\n      // Keep the message in temporary collection pool.\r\n      //\r\n      messageSubjectPool.push(newMessage);\r\n    }\r\n  }\r\n}\r\n\r\n/**\r\n * Only work for confirmation dialog at this moment.\r\n * @param {*} messageContent\r\n * @param {*} isEmit\r\n * @param {*} modelType\r\n * @param {*} messageType\r\n * @param {*} callback callback function work for send back confirmation dialog response back to caller component.\r\n */\r\nfunction sendMessageWithCallback(\r\n  messageContent,\r\n  isEmit,\r\n  modelType,\r\n  messageType,\r\n  callback\r\n) {\r\n  if (messageContent && messageContent.length > 0) {\r\n    const newMessage = {\r\n      content: messageContent,\r\n      modalType: modelType,\r\n      messageType: messageType,\r\n      isEmit: isEmit,\r\n      callback: callback,\r\n    };\r\n    if (isEmit) {\r\n      //\r\n      // Subscribe the message to components immediately.\r\n      //\r\n      messageSubject.next(newMessage);\r\n    } else {\r\n      //\r\n      // Keep the message in temporary collection pool.\r\n      //\r\n      messageSubjectPool.push(newMessage);\r\n    }\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,MAAM;;AAE9B;AACA;AACA;AACA,MAAMC,cAAc,GAAG,IAAID,OAAO,CAAC,CAAC;;AAEpC;AACA;AACA;AACA;AACA,MAAME,kBAAkB,GAAG,EAAE;;AAE7B;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,cAAc,GAAG;EAC5B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEC,IAAI,EAAEA,CAACA,IAAI,GAAG,EAAE,EAAEC,MAAM,GAAG,IAAI,KAAK;IAClCC,WAAW,CAACF,IAAI,EAAEC,MAAM,EAAE,UAAU,EAAE,MAAM,CAAC;EAC/C,CAAC;EAEDE,IAAI,EAAEA,CAACA,IAAI,GAAG,EAAE,EAAEF,MAAM,GAAG,IAAI,KAAK;IAClCC,WAAW,CAACC,IAAI,EAAEF,MAAM,EAAE,UAAU,EAAE,MAAM,CAAC;EAC/C,CAAC;EAEDG,KAAK,EAAEA,CAACA,KAAK,GAAG,EAAE,EAAEH,MAAM,GAAG,IAAI,KAAK;IACpCC,WAAW,CAACE,KAAK,EAAEH,MAAM,EAAE,UAAU,EAAE,OAAO,CAAC;EACjD,CAAC;EAEDI,OAAO,EAAEA,CAACA,OAAO,GAAG,EAAE,EAAEJ,MAAM,GAAG,IAAI,KAAK;IACxCC,WAAW,CAACG,OAAO,EAAEJ,MAAM,EAAE,UAAU,EAAE,SAAS,CAAC;EACrD,CAAC;EAEDK,SAAS,EAAEA,CAACN,IAAI,GAAG,EAAE,EAAEC,MAAM,GAAG,IAAI,KAAK;IACvCC,WAAW,CAACF,IAAI,EAAEC,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC;EAC5C,CAAC;EAEDM,SAAS,EAAEA,CAACJ,IAAI,GAAG,EAAE,EAAEF,MAAM,GAAG,IAAI,KAAK;IACvCC,WAAW,CAACC,IAAI,EAAEF,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC;EAC5C,CAAC;EAEDO,UAAU,EAAEA,CAACJ,KAAK,GAAG,EAAE,EAAEH,MAAM,GAAG,IAAI,KAAK;IACzCC,WAAW,CAACE,KAAK,EAAEH,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC;EAC9C,CAAC;EAEDQ,YAAY,EAAEA,CAACJ,OAAO,GAAG,EAAE,EAAEJ,MAAM,GAAG,IAAI,KAAK;IAC7CC,WAAW,CAACG,OAAO,EAAEJ,MAAM,EAAE,OAAO,EAAE,SAAS,CAAC;EAClD,CAAC;EAED;EACAS,UAAU,EAAEA,CAACV,IAAI,GAAG,EAAE,KAAK;IACzBE,WAAW,CAACF,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,CAAC;EAC3C,CAAC;EAED;EACAW,UAAU,EAAEA,CAACR,IAAI,GAAG,EAAE,KAAK;IACzBD,WAAW,CAACC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,CAAC;EAC3C,CAAC;EAED;EACAS,WAAW,EAAEA,CAACR,KAAK,GAAG,EAAE,KAAK;IAC3BF,WAAW,CAACE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC;EAC7C,CAAC;EAED;EACAS,aAAa,EAAEA,CAACR,OAAO,GAAG,EAAE,KAAK;IAC/BH,WAAW,CAACG,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,CAAC;EACjD,CAAC;EAED;EACAS,aAAa,EAAEA,CAACC,OAAO,GAAG,EAAE,EAAEC,QAAQ,KAAK;IACzCC,uBAAuB,CAACF,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,cAAc,EAAEC,QAAQ,CAAC;EAC5E,CAAC;EAED;EACAE,qBAAqB,EAAEA,CAACH,OAAO,GAAG,EAAE,EAAEC,QAAQ,KAAK;IACjDC,uBAAuB,CACrBF,OAAO,EACP,IAAI,EACJ,QAAQ,EACR,sBAAsB,EACtBC,QACF,CAAC;EACH,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEG,GAAG,EAAEA,CAAA,KAAM;IACT,OAAOtB,cAAc,CAACuB,YAAY,CAAC,CAAC;EACtC,CAAC;EAED;EACAC,IAAI,EAAEA,CAAA,KAAM;IACVC,UAAU,CAAC,MAAM;MACf,IAAIxB,kBAAkB,IAAIA,kBAAkB,CAACyB,MAAM,GAAG,CAAC,EAAE;QACvD,OAAOzB,kBAAkB,CAACyB,MAAM,GAAG,CAAC,EAAE;UACpC,MAAMC,CAAC,GAAG1B,kBAAkB,CAAC2B,GAAG,CAAC,CAAC;UAClCD,CAAC,CAACvB,MAAM,GAAG,IAAI;UACfJ,cAAc,CAAC6B,IAAI,CAACF,CAAC,CAAC;QACxB;MACF;MAEA,MAAMG,cAAc,GAAG;QACrBZ,OAAO,EAAE,cAAc;QACvBa,SAAS,EAAE,UAAU;QACrBC,WAAW,EAAE,MAAM;QACnB5B,MAAM,EAAE;MACV,CAAC;MAED,MAAM6B,YAAY,GAAG;QACnBf,OAAO,EAAE,cAAc;QACvBa,SAAS,EAAE,QAAQ;QACnBC,WAAW,EAAE,MAAM;QACnB5B,MAAM,EAAE;MACV,CAAC;MAED,MAAM8B,WAAW,GAAG;QAClBhB,OAAO,EAAE,cAAc;QACvBa,SAAS,EAAE,OAAO;QAClBC,WAAW,EAAE,MAAM;QACnB5B,MAAM,EAAE;MACV,CAAC;MAEDJ,cAAc,CAAC6B,IAAI,CAACC,cAAc,CAAC;MACnC9B,cAAc,CAAC6B,IAAI,CAACI,YAAY,CAAC;MACjCjC,cAAc,CAAC6B,IAAI,CAACK,WAAW,CAAC;IAClC,CAAC,EAAE,GAAG,CAAC;EACT;AACF,CAAC;AAED,SAAS7B,WAAWA,CAAC8B,cAAc,EAAE/B,MAAM,EAAEgC,SAAS,EAAEJ,WAAW,EAAE;EACnE,IAAIG,cAAc,IAAIA,cAAc,CAACT,MAAM,GAAG,CAAC,EAAE;IAC/C,MAAMW,UAAU,GAAG;MACjBnB,OAAO,EAAEiB,cAAc;MACvBJ,SAAS,EAAEK,SAAS;MACpBJ,WAAW,EAAEA,WAAW;MACxB5B,MAAM,EAAEA;IACV,CAAC;IACD,IAAIA,MAAM,EAAE;MACV;MACAJ,cAAc,CAAC6B,IAAI,CAACQ,UAAU,CAAC;IACjC,CAAC,MAAM;MACL;MACA;MACA;MACApC,kBAAkB,CAACqC,IAAI,CAACD,UAAU,CAAC;IACrC;EACF;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASjB,uBAAuBA,CAC9Be,cAAc,EACd/B,MAAM,EACNgC,SAAS,EACTJ,WAAW,EACXb,QAAQ,EACR;EACA,IAAIgB,cAAc,IAAIA,cAAc,CAACT,MAAM,GAAG,CAAC,EAAE;IAC/C,MAAMW,UAAU,GAAG;MACjBnB,OAAO,EAAEiB,cAAc;MACvBJ,SAAS,EAAEK,SAAS;MACpBJ,WAAW,EAAEA,WAAW;MACxB5B,MAAM,EAAEA,MAAM;MACde,QAAQ,EAAEA;IACZ,CAAC;IACD,IAAIf,MAAM,EAAE;MACV;MACA;MACA;MACAJ,cAAc,CAAC6B,IAAI,CAACQ,UAAU,CAAC;IACjC,CAAC,MAAM;MACL;MACA;MACA;MACApC,kBAAkB,CAACqC,IAAI,CAACD,UAAU,CAAC;IACrC;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}