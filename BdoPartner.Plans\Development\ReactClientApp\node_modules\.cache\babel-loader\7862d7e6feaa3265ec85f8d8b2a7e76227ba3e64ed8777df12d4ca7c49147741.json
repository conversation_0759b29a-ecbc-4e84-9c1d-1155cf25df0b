{"ast": null, "code": "import URLSearchParams from './classes/URLSearchParams.js';\nimport FormData from './classes/FormData.js';\nimport Blob from './classes/Blob.js';\nexport default {\n  isBrowser: true,\n  classes: {\n    URLSearchParams,\n    FormData,\n    Blob\n  },\n  protocols: ['http', 'https', 'file', 'blob', 'url', 'data']\n};", "map": {"version": 3, "names": ["URLSearchParams", "FormData", "Blob", "<PERSON><PERSON><PERSON><PERSON>", "classes", "protocols"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/node_modules/axios/lib/platform/browser/index.js"], "sourcesContent": ["import URLSearchParams from './classes/URLSearchParams.js'\nimport FormData from './classes/FormData.js'\nimport Blob from './classes/Blob.js'\n\nexport default {\n  isBrowser: true,\n  classes: {\n    URLSearchParams,\n    FormData,\n    Blob\n  },\n  protocols: ['http', 'https', 'file', 'blob', 'url', 'data']\n};\n"], "mappings": "AAAA,OAAOA,eAAe,MAAM,8BAA8B;AAC1D,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,IAAI,MAAM,mBAAmB;AAEpC,eAAe;EACbC,SAAS,EAAE,IAAI;EACfC,OAAO,EAAE;IACPJ,eAAe;IACfC,QAAQ;IACRC;EACF,CAAC;EACDG,SAAS,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM;AAC5D,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}