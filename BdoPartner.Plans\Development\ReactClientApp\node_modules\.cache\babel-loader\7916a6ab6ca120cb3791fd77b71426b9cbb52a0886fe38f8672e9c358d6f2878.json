{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PrimeReact, { PrimeReactContext } from 'primereact/api';\nimport { ComponentBase, useHandleStyle } from 'primereact/componentbase';\nimport { useMergeProps, useDisplayOrder, useGlobalOnEscapeKey, ESC_KEY_HANDLING_PRIORITIES, useResizeListener, useOverlayScrollListener, useMountEffect, useUpdateEffect, useUnmountEffect } from 'primereact/hooks';\nimport { Portal } from 'primereact/portal';\nimport { classNames, DomHandler, ZIndexUtils, ObjectUtils } from 'primereact/utils';\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nfunction toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction toPropertyKey(t) {\n  var i = toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nfunction _defineProperty(e, r, t) {\n  return (r = toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _arrayLikeToArray(r, a) {\n  (null == a || a > r.length) && (a = r.length);\n  for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e];\n  return n;\n}\nfunction _arrayWithoutHoles(r) {\n  if (Array.isArray(r)) return _arrayLikeToArray(r);\n}\nfunction _iterableToArray(r) {\n  if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r);\n}\nfunction _unsupportedIterableToArray(r, a) {\n  if (r) {\n    if (\"string\" == typeof r) return _arrayLikeToArray(r, a);\n    var t = {}.toString.call(r).slice(8, -1);\n    return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0;\n  }\n}\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _toConsumableArray(r) {\n  return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread();\n}\nfunction _arrayWithHoles(r) {\n  if (Array.isArray(r)) return r;\n}\nfunction _iterableToArrayLimit(r, l) {\n  var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"];\n  if (null != t) {\n    var e,\n      n,\n      i,\n      u,\n      a = [],\n      f = !0,\n      o = !1;\n    try {\n      if (i = (t = t.call(r)).next, 0 === l) {\n        if (Object(t) !== t) return;\n        f = !1;\n      } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0);\n    } catch (r) {\n      o = !0, n = r;\n    } finally {\n      try {\n        if (!f && null != t[\"return\"] && (u = t[\"return\"](), Object(u) !== u)) return;\n      } finally {\n        if (o) throw n;\n      }\n    }\n    return a;\n  }\n}\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _slicedToArray(r, e) {\n  return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest();\n}\nvar classes = {\n  root: function root(_ref) {\n    var positionState = _ref.positionState,\n      classNameState = _ref.classNameState;\n    return classNames('p-tooltip p-component', _defineProperty({}, \"p-tooltip-\".concat(positionState), true), classNameState);\n  },\n  arrow: 'p-tooltip-arrow',\n  text: 'p-tooltip-text'\n};\nvar inlineStyles = {\n  arrow: function arrow(_ref2) {\n    var context = _ref2.context;\n    return {\n      top: context.bottom ? '0' : context.right || context.left || !context.right && !context.left && !context.top && !context.bottom ? '50%' : null,\n      bottom: context.top ? '0' : null,\n      left: context.right || !context.right && !context.left && !context.top && !context.bottom ? '0' : context.top || context.bottom ? '50%' : null,\n      right: context.left ? '0' : null\n    };\n  }\n};\nvar styles = \"\\n@layer primereact {\\n    .p-tooltip {\\n        position: absolute;\\n        padding: .25em .5rem;\\n        /* #3687: Tooltip prevent scrollbar flickering */\\n        top: -9999px;\\n        left: -9999px;\\n    }\\n    \\n    .p-tooltip.p-tooltip-right,\\n    .p-tooltip.p-tooltip-left {\\n        padding: 0 .25rem;\\n    }\\n    \\n    .p-tooltip.p-tooltip-top,\\n    .p-tooltip.p-tooltip-bottom {\\n        padding:.25em 0;\\n    }\\n    \\n    .p-tooltip .p-tooltip-text {\\n       white-space: pre-line;\\n       word-break: break-word;\\n    }\\n    \\n    .p-tooltip-arrow {\\n        position: absolute;\\n        width: 0;\\n        height: 0;\\n        border-color: transparent;\\n        border-style: solid;\\n    }\\n    \\n    .p-tooltip-right .p-tooltip-arrow {\\n        top: 50%;\\n        left: 0;\\n        margin-top: -.25rem;\\n        border-width: .25em .25em .25em 0;\\n    }\\n    \\n    .p-tooltip-left .p-tooltip-arrow {\\n        top: 50%;\\n        right: 0;\\n        margin-top: -.25rem;\\n        border-width: .25em 0 .25em .25rem;\\n    }\\n    \\n    .p-tooltip.p-tooltip-top {\\n        padding: .25em 0;\\n    }\\n    \\n    .p-tooltip-top .p-tooltip-arrow {\\n        bottom: 0;\\n        left: 50%;\\n        margin-left: -.25rem;\\n        border-width: .25em .25em 0;\\n    }\\n    \\n    .p-tooltip-bottom .p-tooltip-arrow {\\n        top: 0;\\n        left: 50%;\\n        margin-left: -.25rem;\\n        border-width: 0 .25em .25rem;\\n    }\\n\\n    .p-tooltip-target-wrapper {\\n        display: inline-flex;\\n    }\\n}\\n\";\nvar TooltipBase = ComponentBase.extend({\n  defaultProps: {\n    __TYPE: 'Tooltip',\n    appendTo: null,\n    at: null,\n    autoHide: true,\n    autoZIndex: true,\n    baseZIndex: 0,\n    className: null,\n    closeOnEscape: false,\n    content: null,\n    disabled: false,\n    event: null,\n    hideDelay: 0,\n    hideEvent: 'mouseleave',\n    id: null,\n    mouseTrack: false,\n    mouseTrackLeft: 5,\n    mouseTrackTop: 5,\n    my: null,\n    onBeforeHide: null,\n    onBeforeShow: null,\n    onHide: null,\n    onShow: null,\n    position: 'right',\n    showDelay: 0,\n    showEvent: 'mouseenter',\n    showOnDisabled: false,\n    style: null,\n    target: null,\n    updateDelay: 0,\n    children: undefined\n  },\n  css: {\n    classes: classes,\n    styles: styles,\n    inlineStyles: inlineStyles\n  }\n});\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nvar Tooltip = /*#__PURE__*/React.memo(/*#__PURE__*/React.forwardRef(function (inProps, ref) {\n  var mergeProps = useMergeProps();\n  var context = React.useContext(PrimeReactContext);\n  var props = TooltipBase.getProps(inProps, context);\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    visibleState = _React$useState2[0],\n    setVisibleState = _React$useState2[1];\n  var _React$useState3 = React.useState(props.position || 'right'),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    positionState = _React$useState4[0],\n    setPositionState = _React$useState4[1];\n  var _React$useState5 = React.useState(''),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    classNameState = _React$useState6[0],\n    setClassNameState = _React$useState6[1];\n  var _React$useState7 = React.useState(false),\n    _React$useState8 = _slicedToArray(_React$useState7, 2),\n    multipleFocusEvents = _React$useState8[0],\n    setMultipleFocusEvents = _React$useState8[1];\n  var isCloseOnEscape = visibleState && props.closeOnEscape;\n  var overlayDisplayOrder = useDisplayOrder('tooltip', isCloseOnEscape);\n  var metaData = {\n    props: props,\n    state: {\n      visible: visibleState,\n      position: positionState,\n      className: classNameState\n    },\n    context: {\n      right: positionState === 'right',\n      left: positionState === 'left',\n      top: positionState === 'top',\n      bottom: positionState === 'bottom'\n    }\n  };\n  var _TooltipBase$setMetaD = TooltipBase.setMetaData(metaData),\n    ptm = _TooltipBase$setMetaD.ptm,\n    cx = _TooltipBase$setMetaD.cx,\n    sx = _TooltipBase$setMetaD.sx,\n    isUnstyled = _TooltipBase$setMetaD.isUnstyled;\n  useHandleStyle(TooltipBase.css.styles, isUnstyled, {\n    name: 'tooltip'\n  });\n  useGlobalOnEscapeKey({\n    callback: function callback() {\n      hide();\n    },\n    when: isCloseOnEscape,\n    priority: [ESC_KEY_HANDLING_PRIORITIES.TOOLTIP, overlayDisplayOrder]\n  });\n  var elementRef = React.useRef(null);\n  var textRef = React.useRef(null);\n  var currentTargetRef = React.useRef(null);\n  var containerSize = React.useRef(null);\n  var allowHide = React.useRef(true);\n  var timeouts = React.useRef({});\n  var currentMouseEvent = React.useRef(null);\n  var _useResizeListener = useResizeListener({\n      listener: function listener(event) {\n        !DomHandler.isTouchDevice() && hide(event);\n      }\n    }),\n    _useResizeListener2 = _slicedToArray(_useResizeListener, 2),\n    bindWindowResizeListener = _useResizeListener2[0],\n    unbindWindowResizeListener = _useResizeListener2[1];\n  var _useOverlayScrollList = useOverlayScrollListener({\n      target: currentTargetRef.current,\n      listener: function listener(event) {\n        hide(event);\n      },\n      when: visibleState\n    }),\n    _useOverlayScrollList2 = _slicedToArray(_useOverlayScrollList, 2),\n    bindOverlayScrollListener = _useOverlayScrollList2[0],\n    unbindOverlayScrollListener = _useOverlayScrollList2[1];\n  var isTargetContentEmpty = function isTargetContentEmpty(target) {\n    return !(props.content || getTargetOption(target, 'tooltip'));\n  };\n  var isContentEmpty = function isContentEmpty(target) {\n    return !(props.content || getTargetOption(target, 'tooltip') || props.children);\n  };\n  var isMouseTrack = function isMouseTrack(target) {\n    return getTargetOption(target, 'mousetrack') || props.mouseTrack;\n  };\n  var isDisabled = function isDisabled(target) {\n    return getTargetOption(target, 'disabled') === 'true' || hasTargetOption(target, 'disabled') || props.disabled;\n  };\n  var isShowOnDisabled = function isShowOnDisabled(target) {\n    return getTargetOption(target, 'showondisabled') || props.showOnDisabled;\n  };\n  var isAutoHide = function isAutoHide() {\n    return getTargetOption(currentTargetRef.current, 'autohide') || props.autoHide;\n  };\n  var getTargetOption = function getTargetOption(target, option) {\n    return hasTargetOption(target, \"data-pr-\".concat(option)) ? target.getAttribute(\"data-pr-\".concat(option)) : null;\n  };\n  var hasTargetOption = function hasTargetOption(target, option) {\n    return target && target.hasAttribute(option);\n  };\n  var getEvents = function getEvents(target) {\n    var showEvents = [getTargetOption(target, 'showevent') || props.showEvent];\n    var hideEvents = [getTargetOption(target, 'hideevent') || props.hideEvent];\n    if (isMouseTrack(target)) {\n      showEvents = ['mousemove'];\n      hideEvents = ['mouseleave'];\n    } else {\n      var event = getTargetOption(target, 'event') || props.event;\n      if (event === 'focus') {\n        showEvents = ['focus'];\n        hideEvents = ['blur'];\n      }\n      if (event === 'both') {\n        showEvents = ['focus', 'mouseenter'];\n        hideEvents = multipleFocusEvents ? ['blur'] : ['mouseleave', 'blur'];\n      }\n    }\n    return {\n      showEvents: showEvents,\n      hideEvents: hideEvents\n    };\n  };\n  var getPosition = function getPosition(target) {\n    return getTargetOption(target, 'position') || positionState;\n  };\n  var getMouseTrackPosition = function getMouseTrackPosition(target) {\n    var top = getTargetOption(target, 'mousetracktop') || props.mouseTrackTop;\n    var left = getTargetOption(target, 'mousetrackleft') || props.mouseTrackLeft;\n    return {\n      top: top,\n      left: left\n    };\n  };\n  var updateText = function updateText(target, callback) {\n    if (textRef.current) {\n      var content = getTargetOption(target, 'tooltip') || props.content;\n      if (content) {\n        textRef.current.innerHTML = ''; // remove children\n        textRef.current.appendChild(document.createTextNode(content));\n        callback();\n      } else if (props.children) {\n        callback();\n      }\n    }\n  };\n  var updateTooltipState = function updateTooltipState(position) {\n    updateText(currentTargetRef.current, function () {\n      var _currentMouseEvent$cu = currentMouseEvent.current,\n        x = _currentMouseEvent$cu.pageX,\n        y = _currentMouseEvent$cu.pageY;\n      if (props.autoZIndex && !ZIndexUtils.get(elementRef.current)) {\n        ZIndexUtils.set('tooltip', elementRef.current, context && context.autoZIndex || PrimeReact.autoZIndex, props.baseZIndex || context && context.zIndex.tooltip || PrimeReact.zIndex.tooltip);\n      }\n      elementRef.current.style.left = '';\n      elementRef.current.style.top = '';\n\n      // GitHub #2695 disable pointer events when autohiding\n      if (isAutoHide()) {\n        elementRef.current.style.pointerEvents = 'none';\n      }\n      var mouseTrackCheck = isMouseTrack(currentTargetRef.current) || position === 'mouse';\n      if (mouseTrackCheck && !containerSize.current || mouseTrackCheck) {\n        containerSize.current = {\n          width: DomHandler.getOuterWidth(elementRef.current),\n          height: DomHandler.getOuterHeight(elementRef.current)\n        };\n      }\n      align(currentTargetRef.current, {\n        x: x,\n        y: y\n      }, position);\n    });\n  };\n  var show = function show(e) {\n    if (e.type && e.type === 'focus') setMultipleFocusEvents(true);\n    currentTargetRef.current = e.currentTarget;\n    var disabled = isDisabled(currentTargetRef.current);\n    var empty = isContentEmpty(isShowOnDisabled(currentTargetRef.current) && disabled ? currentTargetRef.current.firstChild : currentTargetRef.current);\n    if (empty || disabled) {\n      return;\n    }\n    currentMouseEvent.current = e;\n    if (visibleState) {\n      applyDelay('updateDelay', updateTooltipState);\n    } else {\n      // #2653 give the callback a chance to return false and not continue with display\n      var success = sendCallback(props.onBeforeShow, {\n        originalEvent: e,\n        target: currentTargetRef.current\n      });\n      if (success) {\n        applyDelay('showDelay', function () {\n          setVisibleState(true);\n          sendCallback(props.onShow, {\n            originalEvent: e,\n            target: currentTargetRef.current\n          });\n        });\n      }\n    }\n  };\n  var hide = function hide(e) {\n    if (e && e.type === 'blur') setMultipleFocusEvents(false);\n    clearTimeouts();\n    if (visibleState) {\n      var success = sendCallback(props.onBeforeHide, {\n        originalEvent: e,\n        target: currentTargetRef.current\n      });\n      if (success) {\n        applyDelay('hideDelay', function () {\n          if (!isAutoHide() && allowHide.current === false) {\n            return;\n          }\n          ZIndexUtils.clear(elementRef.current);\n          DomHandler.removeClass(elementRef.current, 'p-tooltip-active');\n          setVisibleState(false);\n          sendCallback(props.onHide, {\n            originalEvent: e,\n            target: currentTargetRef.current\n          });\n        });\n      }\n    } else if (!props.onBeforeHide && !getDelay('hideDelay')) {\n      // handles the case when visibleState change from mouseenter was queued and mouseleave handler was called earlier than queued re-render\n      setVisibleState(false);\n    }\n  };\n  var align = function align(target, coordinate, position) {\n    var left = 0;\n    var top = 0;\n    var currentPosition = position || positionState;\n    if ((isMouseTrack(target) || currentPosition == 'mouse') && coordinate) {\n      var _containerSize = {\n        width: DomHandler.getOuterWidth(elementRef.current),\n        height: DomHandler.getOuterHeight(elementRef.current)\n      };\n      left = coordinate.x;\n      top = coordinate.y;\n      var _getMouseTrackPositio = getMouseTrackPosition(target),\n        mouseTrackTop = _getMouseTrackPositio.top,\n        mouseTrackLeft = _getMouseTrackPositio.left;\n      switch (currentPosition) {\n        case 'left':\n          left = left - (_containerSize.width + mouseTrackLeft);\n          top = top - (_containerSize.height / 2 - mouseTrackTop);\n          break;\n        case 'right':\n        case 'mouse':\n          left = left + mouseTrackLeft;\n          top = top - (_containerSize.height / 2 - mouseTrackTop);\n          break;\n        case 'top':\n          left = left - (_containerSize.width / 2 - mouseTrackLeft);\n          top = top - (_containerSize.height + mouseTrackTop);\n          break;\n        case 'bottom':\n          left = left - (_containerSize.width / 2 - mouseTrackLeft);\n          top = top + mouseTrackTop;\n          break;\n      }\n      if (left <= 0 || containerSize.current.width > _containerSize.width) {\n        elementRef.current.style.left = '0px';\n        elementRef.current.style.right = window.innerWidth - _containerSize.width - left + 'px';\n      } else {\n        elementRef.current.style.right = '';\n        elementRef.current.style.left = left + 'px';\n      }\n      elementRef.current.style.top = top + 'px';\n      DomHandler.addClass(elementRef.current, 'p-tooltip-active');\n    } else {\n      var pos = DomHandler.findCollisionPosition(currentPosition);\n      var my = getTargetOption(target, 'my') || props.my || pos.my;\n      var at = getTargetOption(target, 'at') || props.at || pos.at;\n      elementRef.current.style.padding = '0px';\n      DomHandler.flipfitCollision(elementRef.current, target, my, at, function (calculatedPosition) {\n        var _calculatedPosition$a = calculatedPosition.at,\n          atX = _calculatedPosition$a.x,\n          atY = _calculatedPosition$a.y;\n        var myX = calculatedPosition.my.x;\n        var newPosition = props.at ? atX !== 'center' && atX !== myX ? atX : atY : calculatedPosition.at[\"\".concat(pos.axis)];\n        elementRef.current.style.padding = '';\n        setPositionState(newPosition);\n        updateContainerPosition(newPosition);\n        DomHandler.addClass(elementRef.current, 'p-tooltip-active');\n      });\n    }\n  };\n  var updateContainerPosition = function updateContainerPosition(position) {\n    if (elementRef.current) {\n      var style = getComputedStyle(elementRef.current);\n      if (position === 'left') {\n        elementRef.current.style.left = parseFloat(style.left) - parseFloat(style.paddingLeft) * 2 + 'px';\n      } else if (position === 'top') {\n        elementRef.current.style.top = parseFloat(style.top) - parseFloat(style.paddingTop) * 2 + 'px';\n      }\n    }\n  };\n  var _onMouseEnter = function onMouseEnter() {\n    if (!isAutoHide()) {\n      allowHide.current = false;\n    }\n  };\n  var _onMouseLeave = function onMouseLeave(e) {\n    if (!isAutoHide()) {\n      allowHide.current = true;\n      hide(e);\n    }\n  };\n  var bindTargetEvent = function bindTargetEvent(target) {\n    if (target) {\n      var _getEvents = getEvents(target),\n        showEvents = _getEvents.showEvents,\n        hideEvents = _getEvents.hideEvents;\n      var currentTarget = getTarget(target);\n      showEvents.forEach(function (event) {\n        return currentTarget === null || currentTarget === void 0 ? void 0 : currentTarget.addEventListener(event, show);\n      });\n      hideEvents.forEach(function (event) {\n        return currentTarget === null || currentTarget === void 0 ? void 0 : currentTarget.addEventListener(event, hide);\n      });\n    }\n  };\n  var unbindTargetEvent = function unbindTargetEvent(target) {\n    if (target) {\n      var _getEvents2 = getEvents(target),\n        showEvents = _getEvents2.showEvents,\n        hideEvents = _getEvents2.hideEvents;\n      var currentTarget = getTarget(target);\n      showEvents.forEach(function (event) {\n        return currentTarget === null || currentTarget === void 0 ? void 0 : currentTarget.removeEventListener(event, show);\n      });\n      hideEvents.forEach(function (event) {\n        return currentTarget === null || currentTarget === void 0 ? void 0 : currentTarget.removeEventListener(event, hide);\n      });\n    }\n  };\n  var getDelay = function getDelay(delayProp) {\n    return getTargetOption(currentTargetRef.current, delayProp.toLowerCase()) || props[delayProp];\n  };\n  var applyDelay = function applyDelay(delayProp, callback) {\n    clearTimeouts();\n    var delay = getDelay(delayProp);\n    delay ? timeouts.current[\"\".concat(delayProp)] = setTimeout(function () {\n      return callback();\n    }, delay) : callback();\n  };\n  var sendCallback = function sendCallback(callback) {\n    if (callback) {\n      for (var _len = arguments.length, params = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        params[_key - 1] = arguments[_key];\n      }\n      var result = callback.apply(void 0, params);\n      if (result === undefined) {\n        result = true;\n      }\n      return result;\n    }\n    return true;\n  };\n  var clearTimeouts = function clearTimeouts() {\n    Object.values(timeouts.current).forEach(function (t) {\n      return clearTimeout(t);\n    });\n  };\n  var getTarget = function getTarget(target) {\n    if (target) {\n      if (isShowOnDisabled(target)) {\n        if (!target.hasWrapper) {\n          var wrapper = document.createElement('div');\n          var isInputElement = target.nodeName === 'INPUT';\n          if (isInputElement) {\n            DomHandler.addMultipleClasses(wrapper, 'p-tooltip-target-wrapper p-inputwrapper');\n          } else {\n            DomHandler.addClass(wrapper, 'p-tooltip-target-wrapper');\n          }\n          target.parentNode.insertBefore(wrapper, target);\n          wrapper.appendChild(target);\n          target.hasWrapper = true;\n          return wrapper;\n        }\n        return target.parentElement;\n      } else if (target.hasWrapper) {\n        var _target$parentElement;\n        (_target$parentElement = target.parentElement).replaceWith.apply(_target$parentElement, _toConsumableArray(target.parentElement.childNodes));\n        delete target.hasWrapper;\n      }\n      return target;\n    }\n    return null;\n  };\n  var updateTargetEvents = function updateTargetEvents(target) {\n    unloadTargetEvents(target);\n    loadTargetEvents(target);\n  };\n  var loadTargetEvents = function loadTargetEvents(target) {\n    setTargetEventOperations(target || props.target, bindTargetEvent);\n  };\n  var unloadTargetEvents = function unloadTargetEvents(target) {\n    setTargetEventOperations(target || props.target, unbindTargetEvent);\n  };\n  var setTargetEventOperations = function setTargetEventOperations(target, operation) {\n    target = ObjectUtils.getRefElement(target);\n    if (target) {\n      if (DomHandler.isElement(target)) {\n        operation(target);\n      } else {\n        var setEvent = function setEvent(target) {\n          var element = DomHandler.find(document, target);\n          element.forEach(function (el) {\n            operation(el);\n          });\n        };\n        if (target instanceof Array) {\n          target.forEach(function (t) {\n            setEvent(t);\n          });\n        } else {\n          setEvent(target);\n        }\n      }\n    }\n  };\n  useMountEffect(function () {\n    if (visibleState && currentTargetRef.current && isDisabled(currentTargetRef.current)) {\n      hide();\n    }\n  });\n  useUpdateEffect(function () {\n    loadTargetEvents();\n    return function () {\n      unloadTargetEvents();\n    };\n  }, [show, hide, props.target]);\n  useUpdateEffect(function () {\n    if (visibleState) {\n      var position = getPosition(currentTargetRef.current);\n      var classname = getTargetOption(currentTargetRef.current, 'classname');\n      setPositionState(position);\n      setClassNameState(classname);\n      updateTooltipState(position);\n      bindWindowResizeListener();\n      bindOverlayScrollListener();\n    } else {\n      setPositionState(props.position || 'right');\n      setClassNameState('');\n      currentTargetRef.current = null;\n      containerSize.current = null;\n      allowHide.current = true;\n    }\n    return function () {\n      unbindWindowResizeListener();\n      unbindOverlayScrollListener();\n    };\n  }, [visibleState]);\n  useUpdateEffect(function () {\n    var position = getPosition(currentTargetRef.current);\n    if (visibleState && position !== 'mouse') {\n      applyDelay('updateDelay', function () {\n        updateText(currentTargetRef.current, function () {\n          align(currentTargetRef.current);\n        });\n      });\n    }\n  }, [props.content]);\n  useUnmountEffect(function () {\n    hide();\n    ZIndexUtils.clear(elementRef.current);\n  });\n  React.useImperativeHandle(ref, function () {\n    return {\n      props: props,\n      updateTargetEvents: updateTargetEvents,\n      loadTargetEvents: loadTargetEvents,\n      unloadTargetEvents: unloadTargetEvents,\n      show: show,\n      hide: hide,\n      getElement: function getElement() {\n        return elementRef.current;\n      },\n      getTarget: function getTarget() {\n        return currentTargetRef.current;\n      }\n    };\n  });\n  var createElement = function createElement() {\n    var empty = isTargetContentEmpty(currentTargetRef.current);\n    var rootProps = mergeProps({\n      id: props.id,\n      className: classNames(props.className, cx('root', {\n        positionState: positionState,\n        classNameState: classNameState\n      })),\n      style: props.style,\n      role: 'tooltip',\n      'aria-hidden': visibleState,\n      onMouseEnter: function onMouseEnter(e) {\n        return _onMouseEnter();\n      },\n      onMouseLeave: function onMouseLeave(e) {\n        return _onMouseLeave(e);\n      }\n    }, TooltipBase.getOtherProps(props), ptm('root'));\n    var arrowProps = mergeProps({\n      className: cx('arrow'),\n      style: sx('arrow', _objectSpread({}, metaData))\n    }, ptm('arrow'));\n    var textProps = mergeProps({\n      className: cx('text')\n    }, ptm('text'));\n    return /*#__PURE__*/React.createElement(\"div\", _extends({\n      ref: elementRef\n    }, rootProps), /*#__PURE__*/React.createElement(\"div\", arrowProps), /*#__PURE__*/React.createElement(\"div\", _extends({\n      ref: textRef\n    }, textProps), empty && props.children));\n  };\n  if (visibleState) {\n    var element = createElement();\n    return /*#__PURE__*/React.createElement(Portal, {\n      element: element,\n      appendTo: props.appendTo,\n      visible: true\n    });\n  }\n  return null;\n}));\nTooltip.displayName = 'Tooltip';\nexport { Tooltip };", "map": {"version": 3, "names": ["React", "PrimeReact", "PrimeReactContext", "ComponentBase", "useHandleStyle", "useMergeProps", "useDisplayOrder", "useGlobalOnEscapeKey", "ESC_KEY_HANDLING_PRIORITIES", "useResizeListener", "useOverlayScrollListener", "useMountEffect", "useUpdateEffect", "useUnmountEffect", "Portal", "classNames", "<PERSON><PERSON><PERSON><PERSON>", "ZIndexUtils", "ObjectUtils", "_extends", "Object", "assign", "bind", "n", "e", "arguments", "length", "t", "r", "hasOwnProperty", "call", "apply", "_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "toPrimitive", "i", "TypeError", "String", "Number", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_defineProperty", "defineProperty", "value", "enumerable", "configurable", "writable", "_arrayLikeToArray", "a", "Array", "_arrayWithoutHoles", "isArray", "_iterableToArray", "from", "_unsupportedIterableToArray", "toString", "slice", "name", "test", "_nonIterableSpread", "_toConsumableArray", "_arrayWithHoles", "_iterableToArrayLimit", "l", "u", "f", "next", "done", "push", "_nonIterableRest", "_slicedToArray", "classes", "root", "_ref", "positionState", "classNameState", "concat", "arrow", "text", "inlineStyles", "_ref2", "context", "top", "bottom", "right", "left", "styles", "TooltipBase", "extend", "defaultProps", "__TYPE", "appendTo", "at", "autoHide", "autoZIndex", "baseZIndex", "className", "closeOnEscape", "content", "disabled", "event", "<PERSON><PERSON><PERSON><PERSON>", "hideEvent", "id", "mouseTrack", "mouseTrackLeft", "mouseTrackTop", "my", "onBeforeHide", "onBeforeShow", "onHide", "onShow", "position", "showDelay", "showEvent", "showOnDisabled", "style", "target", "updateDelay", "children", "undefined", "css", "ownKeys", "keys", "getOwnPropertySymbols", "filter", "getOwnPropertyDescriptor", "_objectSpread", "for<PERSON>ach", "getOwnPropertyDescriptors", "defineProperties", "<PERSON><PERSON><PERSON>", "memo", "forwardRef", "inProps", "ref", "mergeProps", "useContext", "props", "getProps", "_React$useState", "useState", "_React$useState2", "visibleState", "setVisibleState", "_React$useState3", "_React$useState4", "setPositionState", "_React$useState5", "_React$useState6", "setClassNameState", "_React$useState7", "_React$useState8", "multipleFocusEvents", "setMultipleFocusEvents", "isCloseOnEscape", "overlayDisplayOrder", "metaData", "state", "visible", "_TooltipBase$setMetaD", "setMetaData", "ptm", "cx", "sx", "isUnstyled", "callback", "hide", "when", "priority", "TOOLTIP", "elementRef", "useRef", "textRef", "currentTargetRef", "containerSize", "allowHide", "timeouts", "currentMouseEvent", "_useResizeListener", "listener", "isTouchDevice", "_useResizeListener2", "bindWindowResizeListener", "unbindWindowResizeListener", "_useOverlayScrollList", "current", "_useOverlayScrollList2", "bindOverlayScrollListener", "unbindOverlayScrollListener", "isTargetContentEmpty", "getTargetOption", "isContentEmpty", "isMouseTrack", "isDisabled", "hasTargetOption", "isShowOnDisabled", "isAutoHide", "option", "getAttribute", "hasAttribute", "getEvents", "showEvents", "hideEvents", "getPosition", "getMouseTrackPosition", "updateText", "innerHTML", "append<PERSON><PERSON><PERSON>", "document", "createTextNode", "updateTooltipState", "_currentMouseEvent$cu", "x", "pageX", "y", "pageY", "get", "set", "zIndex", "tooltip", "pointerEvents", "mouseTrackCheck", "width", "getOuterWidth", "height", "getOuterHeight", "align", "show", "type", "currentTarget", "empty", "<PERSON><PERSON><PERSON><PERSON>", "apply<PERSON>elay", "success", "send<PERSON><PERSON>back", "originalEvent", "clearTimeouts", "clear", "removeClass", "get<PERSON>elay", "coordinate", "currentPosition", "_containerSize", "_getMouseTrackPositio", "window", "innerWidth", "addClass", "pos", "findCollisionPosition", "padding", "flipfitCollision", "calculatedPosition", "_calculatedPosition$a", "atX", "atY", "myX", "newPosition", "axis", "updateContainerPosition", "getComputedStyle", "parseFloat", "paddingLeft", "paddingTop", "_onMouseEnter", "onMouseEnter", "_onMouseLeave", "onMouseLeave", "bindTargetEvent", "_getEvents", "get<PERSON><PERSON><PERSON>", "addEventListener", "unbindTargetEvent", "_getEvents2", "removeEventListener", "delayProp", "toLowerCase", "delay", "setTimeout", "_len", "params", "_key", "result", "values", "clearTimeout", "hasWrapper", "wrapper", "createElement", "isInputElement", "nodeName", "addMultipleClasses", "parentNode", "insertBefore", "parentElement", "_target$parentElement", "replaceWith", "childNodes", "updateTargetEvents", "unloadTargetEvents", "loadTargetEvents", "setTargetEventOperations", "operation", "getRefElement", "isElement", "setEvent", "element", "find", "el", "classname", "useImperativeHandle", "getElement", "rootProps", "role", "getOtherProps", "arrowProps", "textProps", "displayName"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/node_modules/primereact/tooltip/tooltip.esm.js"], "sourcesContent": ["'use client';\nimport * as React from 'react';\nimport PrimeReact, { PrimeReactContext } from 'primereact/api';\nimport { ComponentBase, useHandleStyle } from 'primereact/componentbase';\nimport { useMergeProps, useDisplayOrder, useGlobalOnEscapeKey, ESC_KEY_HANDLING_PRIORITIES, useResizeListener, useOverlayScrollListener, useMountEffect, useUpdateEffect, useUnmountEffect } from 'primereact/hooks';\nimport { Portal } from 'primereact/portal';\nimport { classNames, DomHandler, ZIndexUtils, ObjectUtils } from 'primereact/utils';\n\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\n\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\n\nfunction toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\n\nfunction toPropertyKey(t) {\n  var i = toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\n\nfunction _defineProperty(e, r, t) {\n  return (r = toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\n\nfunction _arrayLikeToArray(r, a) {\n  (null == a || a > r.length) && (a = r.length);\n  for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e];\n  return n;\n}\n\nfunction _arrayWithoutHoles(r) {\n  if (Array.isArray(r)) return _arrayLikeToArray(r);\n}\n\nfunction _iterableToArray(r) {\n  if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r);\n}\n\nfunction _unsupportedIterableToArray(r, a) {\n  if (r) {\n    if (\"string\" == typeof r) return _arrayLikeToArray(r, a);\n    var t = {}.toString.call(r).slice(8, -1);\n    return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0;\n  }\n}\n\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nfunction _toConsumableArray(r) {\n  return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread();\n}\n\nfunction _arrayWithHoles(r) {\n  if (Array.isArray(r)) return r;\n}\n\nfunction _iterableToArrayLimit(r, l) {\n  var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"];\n  if (null != t) {\n    var e,\n      n,\n      i,\n      u,\n      a = [],\n      f = !0,\n      o = !1;\n    try {\n      if (i = (t = t.call(r)).next, 0 === l) {\n        if (Object(t) !== t) return;\n        f = !1;\n      } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0);\n    } catch (r) {\n      o = !0, n = r;\n    } finally {\n      try {\n        if (!f && null != t[\"return\"] && (u = t[\"return\"](), Object(u) !== u)) return;\n      } finally {\n        if (o) throw n;\n      }\n    }\n    return a;\n  }\n}\n\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nfunction _slicedToArray(r, e) {\n  return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest();\n}\n\nvar classes = {\n  root: function root(_ref) {\n    var positionState = _ref.positionState,\n      classNameState = _ref.classNameState;\n    return classNames('p-tooltip p-component', _defineProperty({}, \"p-tooltip-\".concat(positionState), true), classNameState);\n  },\n  arrow: 'p-tooltip-arrow',\n  text: 'p-tooltip-text'\n};\nvar inlineStyles = {\n  arrow: function arrow(_ref2) {\n    var context = _ref2.context;\n    return {\n      top: context.bottom ? '0' : context.right || context.left || !context.right && !context.left && !context.top && !context.bottom ? '50%' : null,\n      bottom: context.top ? '0' : null,\n      left: context.right || !context.right && !context.left && !context.top && !context.bottom ? '0' : context.top || context.bottom ? '50%' : null,\n      right: context.left ? '0' : null\n    };\n  }\n};\nvar styles = \"\\n@layer primereact {\\n    .p-tooltip {\\n        position: absolute;\\n        padding: .25em .5rem;\\n        /* #3687: Tooltip prevent scrollbar flickering */\\n        top: -9999px;\\n        left: -9999px;\\n    }\\n    \\n    .p-tooltip.p-tooltip-right,\\n    .p-tooltip.p-tooltip-left {\\n        padding: 0 .25rem;\\n    }\\n    \\n    .p-tooltip.p-tooltip-top,\\n    .p-tooltip.p-tooltip-bottom {\\n        padding:.25em 0;\\n    }\\n    \\n    .p-tooltip .p-tooltip-text {\\n       white-space: pre-line;\\n       word-break: break-word;\\n    }\\n    \\n    .p-tooltip-arrow {\\n        position: absolute;\\n        width: 0;\\n        height: 0;\\n        border-color: transparent;\\n        border-style: solid;\\n    }\\n    \\n    .p-tooltip-right .p-tooltip-arrow {\\n        top: 50%;\\n        left: 0;\\n        margin-top: -.25rem;\\n        border-width: .25em .25em .25em 0;\\n    }\\n    \\n    .p-tooltip-left .p-tooltip-arrow {\\n        top: 50%;\\n        right: 0;\\n        margin-top: -.25rem;\\n        border-width: .25em 0 .25em .25rem;\\n    }\\n    \\n    .p-tooltip.p-tooltip-top {\\n        padding: .25em 0;\\n    }\\n    \\n    .p-tooltip-top .p-tooltip-arrow {\\n        bottom: 0;\\n        left: 50%;\\n        margin-left: -.25rem;\\n        border-width: .25em .25em 0;\\n    }\\n    \\n    .p-tooltip-bottom .p-tooltip-arrow {\\n        top: 0;\\n        left: 50%;\\n        margin-left: -.25rem;\\n        border-width: 0 .25em .25rem;\\n    }\\n\\n    .p-tooltip-target-wrapper {\\n        display: inline-flex;\\n    }\\n}\\n\";\nvar TooltipBase = ComponentBase.extend({\n  defaultProps: {\n    __TYPE: 'Tooltip',\n    appendTo: null,\n    at: null,\n    autoHide: true,\n    autoZIndex: true,\n    baseZIndex: 0,\n    className: null,\n    closeOnEscape: false,\n    content: null,\n    disabled: false,\n    event: null,\n    hideDelay: 0,\n    hideEvent: 'mouseleave',\n    id: null,\n    mouseTrack: false,\n    mouseTrackLeft: 5,\n    mouseTrackTop: 5,\n    my: null,\n    onBeforeHide: null,\n    onBeforeShow: null,\n    onHide: null,\n    onShow: null,\n    position: 'right',\n    showDelay: 0,\n    showEvent: 'mouseenter',\n    showOnDisabled: false,\n    style: null,\n    target: null,\n    updateDelay: 0,\n    children: undefined\n  },\n  css: {\n    classes: classes,\n    styles: styles,\n    inlineStyles: inlineStyles\n  }\n});\n\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nvar Tooltip = /*#__PURE__*/React.memo(/*#__PURE__*/React.forwardRef(function (inProps, ref) {\n  var mergeProps = useMergeProps();\n  var context = React.useContext(PrimeReactContext);\n  var props = TooltipBase.getProps(inProps, context);\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    visibleState = _React$useState2[0],\n    setVisibleState = _React$useState2[1];\n  var _React$useState3 = React.useState(props.position || 'right'),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    positionState = _React$useState4[0],\n    setPositionState = _React$useState4[1];\n  var _React$useState5 = React.useState(''),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    classNameState = _React$useState6[0],\n    setClassNameState = _React$useState6[1];\n  var _React$useState7 = React.useState(false),\n    _React$useState8 = _slicedToArray(_React$useState7, 2),\n    multipleFocusEvents = _React$useState8[0],\n    setMultipleFocusEvents = _React$useState8[1];\n  var isCloseOnEscape = visibleState && props.closeOnEscape;\n  var overlayDisplayOrder = useDisplayOrder('tooltip', isCloseOnEscape);\n  var metaData = {\n    props: props,\n    state: {\n      visible: visibleState,\n      position: positionState,\n      className: classNameState\n    },\n    context: {\n      right: positionState === 'right',\n      left: positionState === 'left',\n      top: positionState === 'top',\n      bottom: positionState === 'bottom'\n    }\n  };\n  var _TooltipBase$setMetaD = TooltipBase.setMetaData(metaData),\n    ptm = _TooltipBase$setMetaD.ptm,\n    cx = _TooltipBase$setMetaD.cx,\n    sx = _TooltipBase$setMetaD.sx,\n    isUnstyled = _TooltipBase$setMetaD.isUnstyled;\n  useHandleStyle(TooltipBase.css.styles, isUnstyled, {\n    name: 'tooltip'\n  });\n  useGlobalOnEscapeKey({\n    callback: function callback() {\n      hide();\n    },\n    when: isCloseOnEscape,\n    priority: [ESC_KEY_HANDLING_PRIORITIES.TOOLTIP, overlayDisplayOrder]\n  });\n  var elementRef = React.useRef(null);\n  var textRef = React.useRef(null);\n  var currentTargetRef = React.useRef(null);\n  var containerSize = React.useRef(null);\n  var allowHide = React.useRef(true);\n  var timeouts = React.useRef({});\n  var currentMouseEvent = React.useRef(null);\n  var _useResizeListener = useResizeListener({\n      listener: function listener(event) {\n        !DomHandler.isTouchDevice() && hide(event);\n      }\n    }),\n    _useResizeListener2 = _slicedToArray(_useResizeListener, 2),\n    bindWindowResizeListener = _useResizeListener2[0],\n    unbindWindowResizeListener = _useResizeListener2[1];\n  var _useOverlayScrollList = useOverlayScrollListener({\n      target: currentTargetRef.current,\n      listener: function listener(event) {\n        hide(event);\n      },\n      when: visibleState\n    }),\n    _useOverlayScrollList2 = _slicedToArray(_useOverlayScrollList, 2),\n    bindOverlayScrollListener = _useOverlayScrollList2[0],\n    unbindOverlayScrollListener = _useOverlayScrollList2[1];\n  var isTargetContentEmpty = function isTargetContentEmpty(target) {\n    return !(props.content || getTargetOption(target, 'tooltip'));\n  };\n  var isContentEmpty = function isContentEmpty(target) {\n    return !(props.content || getTargetOption(target, 'tooltip') || props.children);\n  };\n  var isMouseTrack = function isMouseTrack(target) {\n    return getTargetOption(target, 'mousetrack') || props.mouseTrack;\n  };\n  var isDisabled = function isDisabled(target) {\n    return getTargetOption(target, 'disabled') === 'true' || hasTargetOption(target, 'disabled') || props.disabled;\n  };\n  var isShowOnDisabled = function isShowOnDisabled(target) {\n    return getTargetOption(target, 'showondisabled') || props.showOnDisabled;\n  };\n  var isAutoHide = function isAutoHide() {\n    return getTargetOption(currentTargetRef.current, 'autohide') || props.autoHide;\n  };\n  var getTargetOption = function getTargetOption(target, option) {\n    return hasTargetOption(target, \"data-pr-\".concat(option)) ? target.getAttribute(\"data-pr-\".concat(option)) : null;\n  };\n  var hasTargetOption = function hasTargetOption(target, option) {\n    return target && target.hasAttribute(option);\n  };\n  var getEvents = function getEvents(target) {\n    var showEvents = [getTargetOption(target, 'showevent') || props.showEvent];\n    var hideEvents = [getTargetOption(target, 'hideevent') || props.hideEvent];\n    if (isMouseTrack(target)) {\n      showEvents = ['mousemove'];\n      hideEvents = ['mouseleave'];\n    } else {\n      var event = getTargetOption(target, 'event') || props.event;\n      if (event === 'focus') {\n        showEvents = ['focus'];\n        hideEvents = ['blur'];\n      }\n      if (event === 'both') {\n        showEvents = ['focus', 'mouseenter'];\n        hideEvents = multipleFocusEvents ? ['blur'] : ['mouseleave', 'blur'];\n      }\n    }\n    return {\n      showEvents: showEvents,\n      hideEvents: hideEvents\n    };\n  };\n  var getPosition = function getPosition(target) {\n    return getTargetOption(target, 'position') || positionState;\n  };\n  var getMouseTrackPosition = function getMouseTrackPosition(target) {\n    var top = getTargetOption(target, 'mousetracktop') || props.mouseTrackTop;\n    var left = getTargetOption(target, 'mousetrackleft') || props.mouseTrackLeft;\n    return {\n      top: top,\n      left: left\n    };\n  };\n  var updateText = function updateText(target, callback) {\n    if (textRef.current) {\n      var content = getTargetOption(target, 'tooltip') || props.content;\n      if (content) {\n        textRef.current.innerHTML = ''; // remove children\n        textRef.current.appendChild(document.createTextNode(content));\n        callback();\n      } else if (props.children) {\n        callback();\n      }\n    }\n  };\n  var updateTooltipState = function updateTooltipState(position) {\n    updateText(currentTargetRef.current, function () {\n      var _currentMouseEvent$cu = currentMouseEvent.current,\n        x = _currentMouseEvent$cu.pageX,\n        y = _currentMouseEvent$cu.pageY;\n      if (props.autoZIndex && !ZIndexUtils.get(elementRef.current)) {\n        ZIndexUtils.set('tooltip', elementRef.current, context && context.autoZIndex || PrimeReact.autoZIndex, props.baseZIndex || context && context.zIndex.tooltip || PrimeReact.zIndex.tooltip);\n      }\n      elementRef.current.style.left = '';\n      elementRef.current.style.top = '';\n\n      // GitHub #2695 disable pointer events when autohiding\n      if (isAutoHide()) {\n        elementRef.current.style.pointerEvents = 'none';\n      }\n      var mouseTrackCheck = isMouseTrack(currentTargetRef.current) || position === 'mouse';\n      if (mouseTrackCheck && !containerSize.current || mouseTrackCheck) {\n        containerSize.current = {\n          width: DomHandler.getOuterWidth(elementRef.current),\n          height: DomHandler.getOuterHeight(elementRef.current)\n        };\n      }\n      align(currentTargetRef.current, {\n        x: x,\n        y: y\n      }, position);\n    });\n  };\n  var show = function show(e) {\n    if (e.type && e.type === 'focus') setMultipleFocusEvents(true);\n    currentTargetRef.current = e.currentTarget;\n    var disabled = isDisabled(currentTargetRef.current);\n    var empty = isContentEmpty(isShowOnDisabled(currentTargetRef.current) && disabled ? currentTargetRef.current.firstChild : currentTargetRef.current);\n    if (empty || disabled) {\n      return;\n    }\n    currentMouseEvent.current = e;\n    if (visibleState) {\n      applyDelay('updateDelay', updateTooltipState);\n    } else {\n      // #2653 give the callback a chance to return false and not continue with display\n      var success = sendCallback(props.onBeforeShow, {\n        originalEvent: e,\n        target: currentTargetRef.current\n      });\n      if (success) {\n        applyDelay('showDelay', function () {\n          setVisibleState(true);\n          sendCallback(props.onShow, {\n            originalEvent: e,\n            target: currentTargetRef.current\n          });\n        });\n      }\n    }\n  };\n  var hide = function hide(e) {\n    if (e && e.type === 'blur') setMultipleFocusEvents(false);\n    clearTimeouts();\n    if (visibleState) {\n      var success = sendCallback(props.onBeforeHide, {\n        originalEvent: e,\n        target: currentTargetRef.current\n      });\n      if (success) {\n        applyDelay('hideDelay', function () {\n          if (!isAutoHide() && allowHide.current === false) {\n            return;\n          }\n          ZIndexUtils.clear(elementRef.current);\n          DomHandler.removeClass(elementRef.current, 'p-tooltip-active');\n          setVisibleState(false);\n          sendCallback(props.onHide, {\n            originalEvent: e,\n            target: currentTargetRef.current\n          });\n        });\n      }\n    } else if (!props.onBeforeHide && !getDelay('hideDelay')) {\n      // handles the case when visibleState change from mouseenter was queued and mouseleave handler was called earlier than queued re-render\n      setVisibleState(false);\n    }\n  };\n  var align = function align(target, coordinate, position) {\n    var left = 0;\n    var top = 0;\n    var currentPosition = position || positionState;\n    if ((isMouseTrack(target) || currentPosition == 'mouse') && coordinate) {\n      var _containerSize = {\n        width: DomHandler.getOuterWidth(elementRef.current),\n        height: DomHandler.getOuterHeight(elementRef.current)\n      };\n      left = coordinate.x;\n      top = coordinate.y;\n      var _getMouseTrackPositio = getMouseTrackPosition(target),\n        mouseTrackTop = _getMouseTrackPositio.top,\n        mouseTrackLeft = _getMouseTrackPositio.left;\n      switch (currentPosition) {\n        case 'left':\n          left = left - (_containerSize.width + mouseTrackLeft);\n          top = top - (_containerSize.height / 2 - mouseTrackTop);\n          break;\n        case 'right':\n        case 'mouse':\n          left = left + mouseTrackLeft;\n          top = top - (_containerSize.height / 2 - mouseTrackTop);\n          break;\n        case 'top':\n          left = left - (_containerSize.width / 2 - mouseTrackLeft);\n          top = top - (_containerSize.height + mouseTrackTop);\n          break;\n        case 'bottom':\n          left = left - (_containerSize.width / 2 - mouseTrackLeft);\n          top = top + mouseTrackTop;\n          break;\n      }\n      if (left <= 0 || containerSize.current.width > _containerSize.width) {\n        elementRef.current.style.left = '0px';\n        elementRef.current.style.right = window.innerWidth - _containerSize.width - left + 'px';\n      } else {\n        elementRef.current.style.right = '';\n        elementRef.current.style.left = left + 'px';\n      }\n      elementRef.current.style.top = top + 'px';\n      DomHandler.addClass(elementRef.current, 'p-tooltip-active');\n    } else {\n      var pos = DomHandler.findCollisionPosition(currentPosition);\n      var my = getTargetOption(target, 'my') || props.my || pos.my;\n      var at = getTargetOption(target, 'at') || props.at || pos.at;\n      elementRef.current.style.padding = '0px';\n      DomHandler.flipfitCollision(elementRef.current, target, my, at, function (calculatedPosition) {\n        var _calculatedPosition$a = calculatedPosition.at,\n          atX = _calculatedPosition$a.x,\n          atY = _calculatedPosition$a.y;\n        var myX = calculatedPosition.my.x;\n        var newPosition = props.at ? atX !== 'center' && atX !== myX ? atX : atY : calculatedPosition.at[\"\".concat(pos.axis)];\n        elementRef.current.style.padding = '';\n        setPositionState(newPosition);\n        updateContainerPosition(newPosition);\n        DomHandler.addClass(elementRef.current, 'p-tooltip-active');\n      });\n    }\n  };\n  var updateContainerPosition = function updateContainerPosition(position) {\n    if (elementRef.current) {\n      var style = getComputedStyle(elementRef.current);\n      if (position === 'left') {\n        elementRef.current.style.left = parseFloat(style.left) - parseFloat(style.paddingLeft) * 2 + 'px';\n      } else if (position === 'top') {\n        elementRef.current.style.top = parseFloat(style.top) - parseFloat(style.paddingTop) * 2 + 'px';\n      }\n    }\n  };\n  var _onMouseEnter = function onMouseEnter() {\n    if (!isAutoHide()) {\n      allowHide.current = false;\n    }\n  };\n  var _onMouseLeave = function onMouseLeave(e) {\n    if (!isAutoHide()) {\n      allowHide.current = true;\n      hide(e);\n    }\n  };\n  var bindTargetEvent = function bindTargetEvent(target) {\n    if (target) {\n      var _getEvents = getEvents(target),\n        showEvents = _getEvents.showEvents,\n        hideEvents = _getEvents.hideEvents;\n      var currentTarget = getTarget(target);\n      showEvents.forEach(function (event) {\n        return currentTarget === null || currentTarget === void 0 ? void 0 : currentTarget.addEventListener(event, show);\n      });\n      hideEvents.forEach(function (event) {\n        return currentTarget === null || currentTarget === void 0 ? void 0 : currentTarget.addEventListener(event, hide);\n      });\n    }\n  };\n  var unbindTargetEvent = function unbindTargetEvent(target) {\n    if (target) {\n      var _getEvents2 = getEvents(target),\n        showEvents = _getEvents2.showEvents,\n        hideEvents = _getEvents2.hideEvents;\n      var currentTarget = getTarget(target);\n      showEvents.forEach(function (event) {\n        return currentTarget === null || currentTarget === void 0 ? void 0 : currentTarget.removeEventListener(event, show);\n      });\n      hideEvents.forEach(function (event) {\n        return currentTarget === null || currentTarget === void 0 ? void 0 : currentTarget.removeEventListener(event, hide);\n      });\n    }\n  };\n  var getDelay = function getDelay(delayProp) {\n    return getTargetOption(currentTargetRef.current, delayProp.toLowerCase()) || props[delayProp];\n  };\n  var applyDelay = function applyDelay(delayProp, callback) {\n    clearTimeouts();\n    var delay = getDelay(delayProp);\n    delay ? timeouts.current[\"\".concat(delayProp)] = setTimeout(function () {\n      return callback();\n    }, delay) : callback();\n  };\n  var sendCallback = function sendCallback(callback) {\n    if (callback) {\n      for (var _len = arguments.length, params = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        params[_key - 1] = arguments[_key];\n      }\n      var result = callback.apply(void 0, params);\n      if (result === undefined) {\n        result = true;\n      }\n      return result;\n    }\n    return true;\n  };\n  var clearTimeouts = function clearTimeouts() {\n    Object.values(timeouts.current).forEach(function (t) {\n      return clearTimeout(t);\n    });\n  };\n  var getTarget = function getTarget(target) {\n    if (target) {\n      if (isShowOnDisabled(target)) {\n        if (!target.hasWrapper) {\n          var wrapper = document.createElement('div');\n          var isInputElement = target.nodeName === 'INPUT';\n          if (isInputElement) {\n            DomHandler.addMultipleClasses(wrapper, 'p-tooltip-target-wrapper p-inputwrapper');\n          } else {\n            DomHandler.addClass(wrapper, 'p-tooltip-target-wrapper');\n          }\n          target.parentNode.insertBefore(wrapper, target);\n          wrapper.appendChild(target);\n          target.hasWrapper = true;\n          return wrapper;\n        }\n        return target.parentElement;\n      } else if (target.hasWrapper) {\n        var _target$parentElement;\n        (_target$parentElement = target.parentElement).replaceWith.apply(_target$parentElement, _toConsumableArray(target.parentElement.childNodes));\n        delete target.hasWrapper;\n      }\n      return target;\n    }\n    return null;\n  };\n  var updateTargetEvents = function updateTargetEvents(target) {\n    unloadTargetEvents(target);\n    loadTargetEvents(target);\n  };\n  var loadTargetEvents = function loadTargetEvents(target) {\n    setTargetEventOperations(target || props.target, bindTargetEvent);\n  };\n  var unloadTargetEvents = function unloadTargetEvents(target) {\n    setTargetEventOperations(target || props.target, unbindTargetEvent);\n  };\n  var setTargetEventOperations = function setTargetEventOperations(target, operation) {\n    target = ObjectUtils.getRefElement(target);\n    if (target) {\n      if (DomHandler.isElement(target)) {\n        operation(target);\n      } else {\n        var setEvent = function setEvent(target) {\n          var element = DomHandler.find(document, target);\n          element.forEach(function (el) {\n            operation(el);\n          });\n        };\n        if (target instanceof Array) {\n          target.forEach(function (t) {\n            setEvent(t);\n          });\n        } else {\n          setEvent(target);\n        }\n      }\n    }\n  };\n  useMountEffect(function () {\n    if (visibleState && currentTargetRef.current && isDisabled(currentTargetRef.current)) {\n      hide();\n    }\n  });\n  useUpdateEffect(function () {\n    loadTargetEvents();\n    return function () {\n      unloadTargetEvents();\n    };\n  }, [show, hide, props.target]);\n  useUpdateEffect(function () {\n    if (visibleState) {\n      var position = getPosition(currentTargetRef.current);\n      var classname = getTargetOption(currentTargetRef.current, 'classname');\n      setPositionState(position);\n      setClassNameState(classname);\n      updateTooltipState(position);\n      bindWindowResizeListener();\n      bindOverlayScrollListener();\n    } else {\n      setPositionState(props.position || 'right');\n      setClassNameState('');\n      currentTargetRef.current = null;\n      containerSize.current = null;\n      allowHide.current = true;\n    }\n    return function () {\n      unbindWindowResizeListener();\n      unbindOverlayScrollListener();\n    };\n  }, [visibleState]);\n  useUpdateEffect(function () {\n    var position = getPosition(currentTargetRef.current);\n    if (visibleState && position !== 'mouse') {\n      applyDelay('updateDelay', function () {\n        updateText(currentTargetRef.current, function () {\n          align(currentTargetRef.current);\n        });\n      });\n    }\n  }, [props.content]);\n  useUnmountEffect(function () {\n    hide();\n    ZIndexUtils.clear(elementRef.current);\n  });\n  React.useImperativeHandle(ref, function () {\n    return {\n      props: props,\n      updateTargetEvents: updateTargetEvents,\n      loadTargetEvents: loadTargetEvents,\n      unloadTargetEvents: unloadTargetEvents,\n      show: show,\n      hide: hide,\n      getElement: function getElement() {\n        return elementRef.current;\n      },\n      getTarget: function getTarget() {\n        return currentTargetRef.current;\n      }\n    };\n  });\n  var createElement = function createElement() {\n    var empty = isTargetContentEmpty(currentTargetRef.current);\n    var rootProps = mergeProps({\n      id: props.id,\n      className: classNames(props.className, cx('root', {\n        positionState: positionState,\n        classNameState: classNameState\n      })),\n      style: props.style,\n      role: 'tooltip',\n      'aria-hidden': visibleState,\n      onMouseEnter: function onMouseEnter(e) {\n        return _onMouseEnter();\n      },\n      onMouseLeave: function onMouseLeave(e) {\n        return _onMouseLeave(e);\n      }\n    }, TooltipBase.getOtherProps(props), ptm('root'));\n    var arrowProps = mergeProps({\n      className: cx('arrow'),\n      style: sx('arrow', _objectSpread({}, metaData))\n    }, ptm('arrow'));\n    var textProps = mergeProps({\n      className: cx('text')\n    }, ptm('text'));\n    return /*#__PURE__*/React.createElement(\"div\", _extends({\n      ref: elementRef\n    }, rootProps), /*#__PURE__*/React.createElement(\"div\", arrowProps), /*#__PURE__*/React.createElement(\"div\", _extends({\n      ref: textRef\n    }, textProps), empty && props.children));\n  };\n  if (visibleState) {\n    var element = createElement();\n    return /*#__PURE__*/React.createElement(Portal, {\n      element: element,\n      appendTo: props.appendTo,\n      visible: true\n    });\n  }\n  return null;\n}));\nTooltip.displayName = 'Tooltip';\n\nexport { Tooltip };\n"], "mappings": "AAAA,YAAY;;AACZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,IAAIC,iBAAiB,QAAQ,gBAAgB;AAC9D,SAASC,aAAa,EAAEC,cAAc,QAAQ,0BAA0B;AACxE,SAASC,aAAa,EAAEC,eAAe,EAAEC,oBAAoB,EAAEC,2BAA2B,EAAEC,iBAAiB,EAAEC,wBAAwB,EAAEC,cAAc,EAAEC,eAAe,EAAEC,gBAAgB,QAAQ,kBAAkB;AACpN,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,UAAU,EAAEC,UAAU,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAEnF,SAASC,QAAQA,CAAA,EAAG;EAClB,OAAOA,QAAQ,GAAGC,MAAM,CAACC,MAAM,GAAGD,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUC,CAAC,EAAE;IACpE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MACzC,IAAIG,CAAC,GAAGF,SAAS,CAACD,CAAC,CAAC;MACpB,KAAK,IAAII,CAAC,IAAID,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEE,cAAc,CAACC,IAAI,CAACH,CAAC,EAAEC,CAAC,CAAC,KAAKL,CAAC,CAACK,CAAC,CAAC,GAAGD,CAAC,CAACC,CAAC,CAAC,CAAC;IAClE;IACA,OAAOL,CAAC;EACV,CAAC,EAAEJ,QAAQ,CAACY,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;AACpC;AAEA,SAASO,OAAOA,CAACC,CAAC,EAAE;EAClB,yBAAyB;;EAEzB,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,CAAC,EAAE;IAChG,OAAO,OAAOA,CAAC;EACjB,CAAC,GAAG,UAAUA,CAAC,EAAE;IACf,OAAOA,CAAC,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,CAAC,CAACG,WAAW,KAAKF,MAAM,IAAID,CAAC,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,CAAC;EACrH,CAAC,EAAED,OAAO,CAACC,CAAC,CAAC;AACf;AAEA,SAASK,WAAWA,CAACX,CAAC,EAAEC,CAAC,EAAE;EACzB,IAAI,QAAQ,IAAII,OAAO,CAACL,CAAC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAC1C,IAAIH,CAAC,GAAGG,CAAC,CAACO,MAAM,CAACI,WAAW,CAAC;EAC7B,IAAI,KAAK,CAAC,KAAKd,CAAC,EAAE;IAChB,IAAIe,CAAC,GAAGf,CAAC,CAACM,IAAI,CAACH,CAAC,EAAEC,CAAC,IAAI,SAAS,CAAC;IACjC,IAAI,QAAQ,IAAII,OAAO,CAACO,CAAC,CAAC,EAAE,OAAOA,CAAC;IACpC,MAAM,IAAIC,SAAS,CAAC,8CAA8C,CAAC;EACrE;EACA,OAAO,CAAC,QAAQ,KAAKZ,CAAC,GAAGa,MAAM,GAAGC,MAAM,EAAEf,CAAC,CAAC;AAC9C;AAEA,SAASgB,aAAaA,CAAChB,CAAC,EAAE;EACxB,IAAIY,CAAC,GAAGD,WAAW,CAACX,CAAC,EAAE,QAAQ,CAAC;EAChC,OAAO,QAAQ,IAAIK,OAAO,CAACO,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAC5C;AAEA,SAASK,eAAeA,CAACpB,CAAC,EAAEI,CAAC,EAAED,CAAC,EAAE;EAChC,OAAO,CAACC,CAAC,GAAGe,aAAa,CAACf,CAAC,CAAC,KAAKJ,CAAC,GAAGJ,MAAM,CAACyB,cAAc,CAACrB,CAAC,EAAEI,CAAC,EAAE;IAC/DkB,KAAK,EAAEnB,CAAC;IACRoB,UAAU,EAAE,CAAC,CAAC;IACdC,YAAY,EAAE,CAAC,CAAC;IAChBC,QAAQ,EAAE,CAAC;EACb,CAAC,CAAC,GAAGzB,CAAC,CAACI,CAAC,CAAC,GAAGD,CAAC,EAAEH,CAAC;AAClB;AAEA,SAAS0B,iBAAiBA,CAACtB,CAAC,EAAEuB,CAAC,EAAE;EAC/B,CAAC,IAAI,IAAIA,CAAC,IAAIA,CAAC,GAAGvB,CAAC,CAACF,MAAM,MAAMyB,CAAC,GAAGvB,CAAC,CAACF,MAAM,CAAC;EAC7C,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAED,CAAC,GAAG6B,KAAK,CAACD,CAAC,CAAC,EAAE3B,CAAC,GAAG2B,CAAC,EAAE3B,CAAC,EAAE,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGI,CAAC,CAACJ,CAAC,CAAC;EACrD,OAAOD,CAAC;AACV;AAEA,SAAS8B,kBAAkBA,CAACzB,CAAC,EAAE;EAC7B,IAAIwB,KAAK,CAACE,OAAO,CAAC1B,CAAC,CAAC,EAAE,OAAOsB,iBAAiB,CAACtB,CAAC,CAAC;AACnD;AAEA,SAAS2B,gBAAgBA,CAAC3B,CAAC,EAAE;EAC3B,IAAI,WAAW,IAAI,OAAOM,MAAM,IAAI,IAAI,IAAIN,CAAC,CAACM,MAAM,CAACC,QAAQ,CAAC,IAAI,IAAI,IAAIP,CAAC,CAAC,YAAY,CAAC,EAAE,OAAOwB,KAAK,CAACI,IAAI,CAAC5B,CAAC,CAAC;AACjH;AAEA,SAAS6B,2BAA2BA,CAAC7B,CAAC,EAAEuB,CAAC,EAAE;EACzC,IAAIvB,CAAC,EAAE;IACL,IAAI,QAAQ,IAAI,OAAOA,CAAC,EAAE,OAAOsB,iBAAiB,CAACtB,CAAC,EAAEuB,CAAC,CAAC;IACxD,IAAIxB,CAAC,GAAG,CAAC,CAAC,CAAC+B,QAAQ,CAAC5B,IAAI,CAACF,CAAC,CAAC,CAAC+B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACxC,OAAO,QAAQ,KAAKhC,CAAC,IAAIC,CAAC,CAACQ,WAAW,KAAKT,CAAC,GAAGC,CAAC,CAACQ,WAAW,CAACwB,IAAI,CAAC,EAAE,KAAK,KAAKjC,CAAC,IAAI,KAAK,KAAKA,CAAC,GAAGyB,KAAK,CAACI,IAAI,CAAC5B,CAAC,CAAC,GAAG,WAAW,KAAKD,CAAC,IAAI,0CAA0C,CAACkC,IAAI,CAAClC,CAAC,CAAC,GAAGuB,iBAAiB,CAACtB,CAAC,EAAEuB,CAAC,CAAC,GAAG,KAAK,CAAC;EAC7N;AACF;AAEA,SAASW,kBAAkBA,CAAA,EAAG;EAC5B,MAAM,IAAItB,SAAS,CAAC,sIAAsI,CAAC;AAC7J;AAEA,SAASuB,kBAAkBA,CAACnC,CAAC,EAAE;EAC7B,OAAOyB,kBAAkB,CAACzB,CAAC,CAAC,IAAI2B,gBAAgB,CAAC3B,CAAC,CAAC,IAAI6B,2BAA2B,CAAC7B,CAAC,CAAC,IAAIkC,kBAAkB,CAAC,CAAC;AAC/G;AAEA,SAASE,eAAeA,CAACpC,CAAC,EAAE;EAC1B,IAAIwB,KAAK,CAACE,OAAO,CAAC1B,CAAC,CAAC,EAAE,OAAOA,CAAC;AAChC;AAEA,SAASqC,qBAAqBA,CAACrC,CAAC,EAAEsC,CAAC,EAAE;EACnC,IAAIvC,CAAC,GAAG,IAAI,IAAIC,CAAC,GAAG,IAAI,GAAG,WAAW,IAAI,OAAOM,MAAM,IAAIN,CAAC,CAACM,MAAM,CAACC,QAAQ,CAAC,IAAIP,CAAC,CAAC,YAAY,CAAC;EAChG,IAAI,IAAI,IAAID,CAAC,EAAE;IACb,IAAIH,CAAC;MACHD,CAAC;MACDgB,CAAC;MACD4B,CAAC;MACDhB,CAAC,GAAG,EAAE;MACNiB,CAAC,GAAG,CAAC,CAAC;MACNnC,CAAC,GAAG,CAAC,CAAC;IACR,IAAI;MACF,IAAIM,CAAC,GAAG,CAACZ,CAAC,GAAGA,CAAC,CAACG,IAAI,CAACF,CAAC,CAAC,EAAEyC,IAAI,EAAE,CAAC,KAAKH,CAAC,EAAE;QACrC,IAAI9C,MAAM,CAACO,CAAC,CAAC,KAAKA,CAAC,EAAE;QACrByC,CAAC,GAAG,CAAC,CAAC;MACR,CAAC,MAAM,OAAO,EAAEA,CAAC,GAAG,CAAC5C,CAAC,GAAGe,CAAC,CAACT,IAAI,CAACH,CAAC,CAAC,EAAE2C,IAAI,CAAC,KAAKnB,CAAC,CAACoB,IAAI,CAAC/C,CAAC,CAACsB,KAAK,CAAC,EAAEK,CAAC,CAACzB,MAAM,KAAKwC,CAAC,CAAC,EAAEE,CAAC,GAAG,CAAC,CAAC,CAAC;IACzF,CAAC,CAAC,OAAOxC,CAAC,EAAE;MACVK,CAAC,GAAG,CAAC,CAAC,EAAEV,CAAC,GAAGK,CAAC;IACf,CAAC,SAAS;MACR,IAAI;QACF,IAAI,CAACwC,CAAC,IAAI,IAAI,IAAIzC,CAAC,CAAC,QAAQ,CAAC,KAAKwC,CAAC,GAAGxC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAEP,MAAM,CAAC+C,CAAC,CAAC,KAAKA,CAAC,CAAC,EAAE;MACzE,CAAC,SAAS;QACR,IAAIlC,CAAC,EAAE,MAAMV,CAAC;MAChB;IACF;IACA,OAAO4B,CAAC;EACV;AACF;AAEA,SAASqB,gBAAgBA,CAAA,EAAG;EAC1B,MAAM,IAAIhC,SAAS,CAAC,2IAA2I,CAAC;AAClK;AAEA,SAASiC,cAAcA,CAAC7C,CAAC,EAAEJ,CAAC,EAAE;EAC5B,OAAOwC,eAAe,CAACpC,CAAC,CAAC,IAAIqC,qBAAqB,CAACrC,CAAC,EAAEJ,CAAC,CAAC,IAAIiC,2BAA2B,CAAC7B,CAAC,EAAEJ,CAAC,CAAC,IAAIgD,gBAAgB,CAAC,CAAC;AACrH;AAEA,IAAIE,OAAO,GAAG;EACZC,IAAI,EAAE,SAASA,IAAIA,CAACC,IAAI,EAAE;IACxB,IAAIC,aAAa,GAAGD,IAAI,CAACC,aAAa;MACpCC,cAAc,GAAGF,IAAI,CAACE,cAAc;IACtC,OAAO/D,UAAU,CAAC,uBAAuB,EAAE6B,eAAe,CAAC,CAAC,CAAC,EAAE,YAAY,CAACmC,MAAM,CAACF,aAAa,CAAC,EAAE,IAAI,CAAC,EAAEC,cAAc,CAAC;EAC3H,CAAC;EACDE,KAAK,EAAE,iBAAiB;EACxBC,IAAI,EAAE;AACR,CAAC;AACD,IAAIC,YAAY,GAAG;EACjBF,KAAK,EAAE,SAASA,KAAKA,CAACG,KAAK,EAAE;IAC3B,IAAIC,OAAO,GAAGD,KAAK,CAACC,OAAO;IAC3B,OAAO;MACLC,GAAG,EAAED,OAAO,CAACE,MAAM,GAAG,GAAG,GAAGF,OAAO,CAACG,KAAK,IAAIH,OAAO,CAACI,IAAI,IAAI,CAACJ,OAAO,CAACG,KAAK,IAAI,CAACH,OAAO,CAACI,IAAI,IAAI,CAACJ,OAAO,CAACC,GAAG,IAAI,CAACD,OAAO,CAACE,MAAM,GAAG,KAAK,GAAG,IAAI;MAC9IA,MAAM,EAAEF,OAAO,CAACC,GAAG,GAAG,GAAG,GAAG,IAAI;MAChCG,IAAI,EAAEJ,OAAO,CAACG,KAAK,IAAI,CAACH,OAAO,CAACG,KAAK,IAAI,CAACH,OAAO,CAACI,IAAI,IAAI,CAACJ,OAAO,CAACC,GAAG,IAAI,CAACD,OAAO,CAACE,MAAM,GAAG,GAAG,GAAGF,OAAO,CAACC,GAAG,IAAID,OAAO,CAACE,MAAM,GAAG,KAAK,GAAG,IAAI;MAC9IC,KAAK,EAAEH,OAAO,CAACI,IAAI,GAAG,GAAG,GAAG;IAC9B,CAAC;EACH;AACF,CAAC;AACD,IAAIC,MAAM,GAAG,u+CAAu+C;AACp/C,IAAIC,WAAW,GAAGvF,aAAa,CAACwF,MAAM,CAAC;EACrCC,YAAY,EAAE;IACZC,MAAM,EAAE,SAAS;IACjBC,QAAQ,EAAE,IAAI;IACdC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE,IAAI;IAChBC,UAAU,EAAE,CAAC;IACbC,SAAS,EAAE,IAAI;IACfC,aAAa,EAAE,KAAK;IACpBC,OAAO,EAAE,IAAI;IACbC,QAAQ,EAAE,KAAK;IACfC,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,CAAC;IACZC,SAAS,EAAE,YAAY;IACvBC,EAAE,EAAE,IAAI;IACRC,UAAU,EAAE,KAAK;IACjBC,cAAc,EAAE,CAAC;IACjBC,aAAa,EAAE,CAAC;IAChBC,EAAE,EAAE,IAAI;IACRC,YAAY,EAAE,IAAI;IAClBC,YAAY,EAAE,IAAI;IAClBC,MAAM,EAAE,IAAI;IACZC,MAAM,EAAE,IAAI;IACZC,QAAQ,EAAE,OAAO;IACjBC,SAAS,EAAE,CAAC;IACZC,SAAS,EAAE,YAAY;IACvBC,cAAc,EAAE,KAAK;IACrBC,KAAK,EAAE,IAAI;IACXC,MAAM,EAAE,IAAI;IACZC,WAAW,EAAE,CAAC;IACdC,QAAQ,EAAEC;EACZ,CAAC;EACDC,GAAG,EAAE;IACHlD,OAAO,EAAEA,OAAO;IAChBe,MAAM,EAAEA,MAAM;IACdP,YAAY,EAAEA;EAChB;AACF,CAAC,CAAC;AAEF,SAAS2C,OAAOA,CAACrG,CAAC,EAAEI,CAAC,EAAE;EAAE,IAAID,CAAC,GAAGP,MAAM,CAAC0G,IAAI,CAACtG,CAAC,CAAC;EAAE,IAAIJ,MAAM,CAAC2G,qBAAqB,EAAE;IAAE,IAAI9F,CAAC,GAAGb,MAAM,CAAC2G,qBAAqB,CAACvG,CAAC,CAAC;IAAEI,CAAC,KAAKK,CAAC,GAAGA,CAAC,CAAC+F,MAAM,CAAC,UAAUpG,CAAC,EAAE;MAAE,OAAOR,MAAM,CAAC6G,wBAAwB,CAACzG,CAAC,EAAEI,CAAC,CAAC,CAACmB,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEpB,CAAC,CAAC4C,IAAI,CAACxC,KAAK,CAACJ,CAAC,EAAEM,CAAC,CAAC;EAAE;EAAE,OAAON,CAAC;AAAE;AAC9P,SAASuG,aAAaA,CAAC1G,CAAC,EAAE;EAAE,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,SAAS,CAACC,MAAM,EAAEE,CAAC,EAAE,EAAE;IAAE,IAAID,CAAC,GAAG,IAAI,IAAIF,SAAS,CAACG,CAAC,CAAC,GAAGH,SAAS,CAACG,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGiG,OAAO,CAACzG,MAAM,CAACO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACwG,OAAO,CAAC,UAAUvG,CAAC,EAAE;MAAEgB,eAAe,CAACpB,CAAC,EAAEI,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGR,MAAM,CAACgH,yBAAyB,GAAGhH,MAAM,CAACiH,gBAAgB,CAAC7G,CAAC,EAAEJ,MAAM,CAACgH,yBAAyB,CAACzG,CAAC,CAAC,CAAC,GAAGkG,OAAO,CAACzG,MAAM,CAACO,CAAC,CAAC,CAAC,CAACwG,OAAO,CAAC,UAAUvG,CAAC,EAAE;MAAER,MAAM,CAACyB,cAAc,CAACrB,CAAC,EAAEI,CAAC,EAAER,MAAM,CAAC6G,wBAAwB,CAACtG,CAAC,EAAEC,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOJ,CAAC;AAAE;AACtb,IAAI8G,OAAO,GAAG,aAAatI,KAAK,CAACuI,IAAI,CAAC,aAAavI,KAAK,CAACwI,UAAU,CAAC,UAAUC,OAAO,EAAEC,GAAG,EAAE;EAC1F,IAAIC,UAAU,GAAGtI,aAAa,CAAC,CAAC;EAChC,IAAI+E,OAAO,GAAGpF,KAAK,CAAC4I,UAAU,CAAC1I,iBAAiB,CAAC;EACjD,IAAI2I,KAAK,GAAGnD,WAAW,CAACoD,QAAQ,CAACL,OAAO,EAAErD,OAAO,CAAC;EAClD,IAAI2D,eAAe,GAAG/I,KAAK,CAACgJ,QAAQ,CAAC,KAAK,CAAC;IACzCC,gBAAgB,GAAGxE,cAAc,CAACsE,eAAe,EAAE,CAAC,CAAC;IACrDG,YAAY,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAClCE,eAAe,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACvC,IAAIG,gBAAgB,GAAGpJ,KAAK,CAACgJ,QAAQ,CAACH,KAAK,CAAC1B,QAAQ,IAAI,OAAO,CAAC;IAC9DkC,gBAAgB,GAAG5E,cAAc,CAAC2E,gBAAgB,EAAE,CAAC,CAAC;IACtDvE,aAAa,GAAGwE,gBAAgB,CAAC,CAAC,CAAC;IACnCC,gBAAgB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;EACxC,IAAIE,gBAAgB,GAAGvJ,KAAK,CAACgJ,QAAQ,CAAC,EAAE,CAAC;IACvCQ,gBAAgB,GAAG/E,cAAc,CAAC8E,gBAAgB,EAAE,CAAC,CAAC;IACtDzE,cAAc,GAAG0E,gBAAgB,CAAC,CAAC,CAAC;IACpCC,iBAAiB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;EACzC,IAAIE,gBAAgB,GAAG1J,KAAK,CAACgJ,QAAQ,CAAC,KAAK,CAAC;IAC1CW,gBAAgB,GAAGlF,cAAc,CAACiF,gBAAgB,EAAE,CAAC,CAAC;IACtDE,mBAAmB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACzCE,sBAAsB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAC9C,IAAIG,eAAe,GAAGZ,YAAY,IAAIL,KAAK,CAACzC,aAAa;EACzD,IAAI2D,mBAAmB,GAAGzJ,eAAe,CAAC,SAAS,EAAEwJ,eAAe,CAAC;EACrE,IAAIE,QAAQ,GAAG;IACbnB,KAAK,EAAEA,KAAK;IACZoB,KAAK,EAAE;MACLC,OAAO,EAAEhB,YAAY;MACrB/B,QAAQ,EAAEtC,aAAa;MACvBsB,SAAS,EAAErB;IACb,CAAC;IACDM,OAAO,EAAE;MACPG,KAAK,EAAEV,aAAa,KAAK,OAAO;MAChCW,IAAI,EAAEX,aAAa,KAAK,MAAM;MAC9BQ,GAAG,EAAER,aAAa,KAAK,KAAK;MAC5BS,MAAM,EAAET,aAAa,KAAK;IAC5B;EACF,CAAC;EACD,IAAIsF,qBAAqB,GAAGzE,WAAW,CAAC0E,WAAW,CAACJ,QAAQ,CAAC;IAC3DK,GAAG,GAAGF,qBAAqB,CAACE,GAAG;IAC/BC,EAAE,GAAGH,qBAAqB,CAACG,EAAE;IAC7BC,EAAE,GAAGJ,qBAAqB,CAACI,EAAE;IAC7BC,UAAU,GAAGL,qBAAqB,CAACK,UAAU;EAC/CpK,cAAc,CAACsF,WAAW,CAACkC,GAAG,CAACnC,MAAM,EAAE+E,UAAU,EAAE;IACjD5G,IAAI,EAAE;EACR,CAAC,CAAC;EACFrD,oBAAoB,CAAC;IACnBkK,QAAQ,EAAE,SAASA,QAAQA,CAAA,EAAG;MAC5BC,IAAI,CAAC,CAAC;IACR,CAAC;IACDC,IAAI,EAAEb,eAAe;IACrBc,QAAQ,EAAE,CAACpK,2BAA2B,CAACqK,OAAO,EAAEd,mBAAmB;EACrE,CAAC,CAAC;EACF,IAAIe,UAAU,GAAG9K,KAAK,CAAC+K,MAAM,CAAC,IAAI,CAAC;EACnC,IAAIC,OAAO,GAAGhL,KAAK,CAAC+K,MAAM,CAAC,IAAI,CAAC;EAChC,IAAIE,gBAAgB,GAAGjL,KAAK,CAAC+K,MAAM,CAAC,IAAI,CAAC;EACzC,IAAIG,aAAa,GAAGlL,KAAK,CAAC+K,MAAM,CAAC,IAAI,CAAC;EACtC,IAAII,SAAS,GAAGnL,KAAK,CAAC+K,MAAM,CAAC,IAAI,CAAC;EAClC,IAAIK,QAAQ,GAAGpL,KAAK,CAAC+K,MAAM,CAAC,CAAC,CAAC,CAAC;EAC/B,IAAIM,iBAAiB,GAAGrL,KAAK,CAAC+K,MAAM,CAAC,IAAI,CAAC;EAC1C,IAAIO,kBAAkB,GAAG7K,iBAAiB,CAAC;MACvC8K,QAAQ,EAAE,SAASA,QAAQA,CAAChF,KAAK,EAAE;QACjC,CAACvF,UAAU,CAACwK,aAAa,CAAC,CAAC,IAAId,IAAI,CAACnE,KAAK,CAAC;MAC5C;IACF,CAAC,CAAC;IACFkF,mBAAmB,GAAGhH,cAAc,CAAC6G,kBAAkB,EAAE,CAAC,CAAC;IAC3DI,wBAAwB,GAAGD,mBAAmB,CAAC,CAAC,CAAC;IACjDE,0BAA0B,GAAGF,mBAAmB,CAAC,CAAC,CAAC;EACrD,IAAIG,qBAAqB,GAAGlL,wBAAwB,CAAC;MACjD8G,MAAM,EAAEyD,gBAAgB,CAACY,OAAO;MAChCN,QAAQ,EAAE,SAASA,QAAQA,CAAChF,KAAK,EAAE;QACjCmE,IAAI,CAACnE,KAAK,CAAC;MACb,CAAC;MACDoE,IAAI,EAAEzB;IACR,CAAC,CAAC;IACF4C,sBAAsB,GAAGrH,cAAc,CAACmH,qBAAqB,EAAE,CAAC,CAAC;IACjEG,yBAAyB,GAAGD,sBAAsB,CAAC,CAAC,CAAC;IACrDE,2BAA2B,GAAGF,sBAAsB,CAAC,CAAC,CAAC;EACzD,IAAIG,oBAAoB,GAAG,SAASA,oBAAoBA,CAACzE,MAAM,EAAE;IAC/D,OAAO,EAAEqB,KAAK,CAACxC,OAAO,IAAI6F,eAAe,CAAC1E,MAAM,EAAE,SAAS,CAAC,CAAC;EAC/D,CAAC;EACD,IAAI2E,cAAc,GAAG,SAASA,cAAcA,CAAC3E,MAAM,EAAE;IACnD,OAAO,EAAEqB,KAAK,CAACxC,OAAO,IAAI6F,eAAe,CAAC1E,MAAM,EAAE,SAAS,CAAC,IAAIqB,KAAK,CAACnB,QAAQ,CAAC;EACjF,CAAC;EACD,IAAI0E,YAAY,GAAG,SAASA,YAAYA,CAAC5E,MAAM,EAAE;IAC/C,OAAO0E,eAAe,CAAC1E,MAAM,EAAE,YAAY,CAAC,IAAIqB,KAAK,CAAClC,UAAU;EAClE,CAAC;EACD,IAAI0F,UAAU,GAAG,SAASA,UAAUA,CAAC7E,MAAM,EAAE;IAC3C,OAAO0E,eAAe,CAAC1E,MAAM,EAAE,UAAU,CAAC,KAAK,MAAM,IAAI8E,eAAe,CAAC9E,MAAM,EAAE,UAAU,CAAC,IAAIqB,KAAK,CAACvC,QAAQ;EAChH,CAAC;EACD,IAAIiG,gBAAgB,GAAG,SAASA,gBAAgBA,CAAC/E,MAAM,EAAE;IACvD,OAAO0E,eAAe,CAAC1E,MAAM,EAAE,gBAAgB,CAAC,IAAIqB,KAAK,CAACvB,cAAc;EAC1E,CAAC;EACD,IAAIkF,UAAU,GAAG,SAASA,UAAUA,CAAA,EAAG;IACrC,OAAON,eAAe,CAACjB,gBAAgB,CAACY,OAAO,EAAE,UAAU,CAAC,IAAIhD,KAAK,CAAC7C,QAAQ;EAChF,CAAC;EACD,IAAIkG,eAAe,GAAG,SAASA,eAAeA,CAAC1E,MAAM,EAAEiF,MAAM,EAAE;IAC7D,OAAOH,eAAe,CAAC9E,MAAM,EAAE,UAAU,CAACzC,MAAM,CAAC0H,MAAM,CAAC,CAAC,GAAGjF,MAAM,CAACkF,YAAY,CAAC,UAAU,CAAC3H,MAAM,CAAC0H,MAAM,CAAC,CAAC,GAAG,IAAI;EACnH,CAAC;EACD,IAAIH,eAAe,GAAG,SAASA,eAAeA,CAAC9E,MAAM,EAAEiF,MAAM,EAAE;IAC7D,OAAOjF,MAAM,IAAIA,MAAM,CAACmF,YAAY,CAACF,MAAM,CAAC;EAC9C,CAAC;EACD,IAAIG,SAAS,GAAG,SAASA,SAASA,CAACpF,MAAM,EAAE;IACzC,IAAIqF,UAAU,GAAG,CAACX,eAAe,CAAC1E,MAAM,EAAE,WAAW,CAAC,IAAIqB,KAAK,CAACxB,SAAS,CAAC;IAC1E,IAAIyF,UAAU,GAAG,CAACZ,eAAe,CAAC1E,MAAM,EAAE,WAAW,CAAC,IAAIqB,KAAK,CAACpC,SAAS,CAAC;IAC1E,IAAI2F,YAAY,CAAC5E,MAAM,CAAC,EAAE;MACxBqF,UAAU,GAAG,CAAC,WAAW,CAAC;MAC1BC,UAAU,GAAG,CAAC,YAAY,CAAC;IAC7B,CAAC,MAAM;MACL,IAAIvG,KAAK,GAAG2F,eAAe,CAAC1E,MAAM,EAAE,OAAO,CAAC,IAAIqB,KAAK,CAACtC,KAAK;MAC3D,IAAIA,KAAK,KAAK,OAAO,EAAE;QACrBsG,UAAU,GAAG,CAAC,OAAO,CAAC;QACtBC,UAAU,GAAG,CAAC,MAAM,CAAC;MACvB;MACA,IAAIvG,KAAK,KAAK,MAAM,EAAE;QACpBsG,UAAU,GAAG,CAAC,OAAO,EAAE,YAAY,CAAC;QACpCC,UAAU,GAAGlD,mBAAmB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,YAAY,EAAE,MAAM,CAAC;MACtE;IACF;IACA,OAAO;MACLiD,UAAU,EAAEA,UAAU;MACtBC,UAAU,EAAEA;IACd,CAAC;EACH,CAAC;EACD,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACvF,MAAM,EAAE;IAC7C,OAAO0E,eAAe,CAAC1E,MAAM,EAAE,UAAU,CAAC,IAAI3C,aAAa;EAC7D,CAAC;EACD,IAAImI,qBAAqB,GAAG,SAASA,qBAAqBA,CAACxF,MAAM,EAAE;IACjE,IAAInC,GAAG,GAAG6G,eAAe,CAAC1E,MAAM,EAAE,eAAe,CAAC,IAAIqB,KAAK,CAAChC,aAAa;IACzE,IAAIrB,IAAI,GAAG0G,eAAe,CAAC1E,MAAM,EAAE,gBAAgB,CAAC,IAAIqB,KAAK,CAACjC,cAAc;IAC5E,OAAO;MACLvB,GAAG,EAAEA,GAAG;MACRG,IAAI,EAAEA;IACR,CAAC;EACH,CAAC;EACD,IAAIyH,UAAU,GAAG,SAASA,UAAUA,CAACzF,MAAM,EAAEiD,QAAQ,EAAE;IACrD,IAAIO,OAAO,CAACa,OAAO,EAAE;MACnB,IAAIxF,OAAO,GAAG6F,eAAe,CAAC1E,MAAM,EAAE,SAAS,CAAC,IAAIqB,KAAK,CAACxC,OAAO;MACjE,IAAIA,OAAO,EAAE;QACX2E,OAAO,CAACa,OAAO,CAACqB,SAAS,GAAG,EAAE,CAAC,CAAC;QAChClC,OAAO,CAACa,OAAO,CAACsB,WAAW,CAACC,QAAQ,CAACC,cAAc,CAAChH,OAAO,CAAC,CAAC;QAC7DoE,QAAQ,CAAC,CAAC;MACZ,CAAC,MAAM,IAAI5B,KAAK,CAACnB,QAAQ,EAAE;QACzB+C,QAAQ,CAAC,CAAC;MACZ;IACF;EACF,CAAC;EACD,IAAI6C,kBAAkB,GAAG,SAASA,kBAAkBA,CAACnG,QAAQ,EAAE;IAC7D8F,UAAU,CAAChC,gBAAgB,CAACY,OAAO,EAAE,YAAY;MAC/C,IAAI0B,qBAAqB,GAAGlC,iBAAiB,CAACQ,OAAO;QACnD2B,CAAC,GAAGD,qBAAqB,CAACE,KAAK;QAC/BC,CAAC,GAAGH,qBAAqB,CAACI,KAAK;MACjC,IAAI9E,KAAK,CAAC5C,UAAU,IAAI,CAAChF,WAAW,CAAC2M,GAAG,CAAC9C,UAAU,CAACe,OAAO,CAAC,EAAE;QAC5D5K,WAAW,CAAC4M,GAAG,CAAC,SAAS,EAAE/C,UAAU,CAACe,OAAO,EAAEzG,OAAO,IAAIA,OAAO,CAACa,UAAU,IAAIhG,UAAU,CAACgG,UAAU,EAAE4C,KAAK,CAAC3C,UAAU,IAAId,OAAO,IAAIA,OAAO,CAAC0I,MAAM,CAACC,OAAO,IAAI9N,UAAU,CAAC6N,MAAM,CAACC,OAAO,CAAC;MAC5L;MACAjD,UAAU,CAACe,OAAO,CAACtE,KAAK,CAAC/B,IAAI,GAAG,EAAE;MAClCsF,UAAU,CAACe,OAAO,CAACtE,KAAK,CAAClC,GAAG,GAAG,EAAE;;MAEjC;MACA,IAAImH,UAAU,CAAC,CAAC,EAAE;QAChB1B,UAAU,CAACe,OAAO,CAACtE,KAAK,CAACyG,aAAa,GAAG,MAAM;MACjD;MACA,IAAIC,eAAe,GAAG7B,YAAY,CAACnB,gBAAgB,CAACY,OAAO,CAAC,IAAI1E,QAAQ,KAAK,OAAO;MACpF,IAAI8G,eAAe,IAAI,CAAC/C,aAAa,CAACW,OAAO,IAAIoC,eAAe,EAAE;QAChE/C,aAAa,CAACW,OAAO,GAAG;UACtBqC,KAAK,EAAElN,UAAU,CAACmN,aAAa,CAACrD,UAAU,CAACe,OAAO,CAAC;UACnDuC,MAAM,EAAEpN,UAAU,CAACqN,cAAc,CAACvD,UAAU,CAACe,OAAO;QACtD,CAAC;MACH;MACAyC,KAAK,CAACrD,gBAAgB,CAACY,OAAO,EAAE;QAC9B2B,CAAC,EAAEA,CAAC;QACJE,CAAC,EAAEA;MACL,CAAC,EAAEvG,QAAQ,CAAC;IACd,CAAC,CAAC;EACJ,CAAC;EACD,IAAIoH,IAAI,GAAG,SAASA,IAAIA,CAAC/M,CAAC,EAAE;IAC1B,IAAIA,CAAC,CAACgN,IAAI,IAAIhN,CAAC,CAACgN,IAAI,KAAK,OAAO,EAAE3E,sBAAsB,CAAC,IAAI,CAAC;IAC9DoB,gBAAgB,CAACY,OAAO,GAAGrK,CAAC,CAACiN,aAAa;IAC1C,IAAInI,QAAQ,GAAG+F,UAAU,CAACpB,gBAAgB,CAACY,OAAO,CAAC;IACnD,IAAI6C,KAAK,GAAGvC,cAAc,CAACI,gBAAgB,CAACtB,gBAAgB,CAACY,OAAO,CAAC,IAAIvF,QAAQ,GAAG2E,gBAAgB,CAACY,OAAO,CAAC8C,UAAU,GAAG1D,gBAAgB,CAACY,OAAO,CAAC;IACnJ,IAAI6C,KAAK,IAAIpI,QAAQ,EAAE;MACrB;IACF;IACA+E,iBAAiB,CAACQ,OAAO,GAAGrK,CAAC;IAC7B,IAAI0H,YAAY,EAAE;MAChB0F,UAAU,CAAC,aAAa,EAAEtB,kBAAkB,CAAC;IAC/C,CAAC,MAAM;MACL;MACA,IAAIuB,OAAO,GAAGC,YAAY,CAACjG,KAAK,CAAC7B,YAAY,EAAE;QAC7C+H,aAAa,EAAEvN,CAAC;QAChBgG,MAAM,EAAEyD,gBAAgB,CAACY;MAC3B,CAAC,CAAC;MACF,IAAIgD,OAAO,EAAE;QACXD,UAAU,CAAC,WAAW,EAAE,YAAY;UAClCzF,eAAe,CAAC,IAAI,CAAC;UACrB2F,YAAY,CAACjG,KAAK,CAAC3B,MAAM,EAAE;YACzB6H,aAAa,EAAEvN,CAAC;YAChBgG,MAAM,EAAEyD,gBAAgB,CAACY;UAC3B,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;IACF;EACF,CAAC;EACD,IAAInB,IAAI,GAAG,SAASA,IAAIA,CAAClJ,CAAC,EAAE;IAC1B,IAAIA,CAAC,IAAIA,CAAC,CAACgN,IAAI,KAAK,MAAM,EAAE3E,sBAAsB,CAAC,KAAK,CAAC;IACzDmF,aAAa,CAAC,CAAC;IACf,IAAI9F,YAAY,EAAE;MAChB,IAAI2F,OAAO,GAAGC,YAAY,CAACjG,KAAK,CAAC9B,YAAY,EAAE;QAC7CgI,aAAa,EAAEvN,CAAC;QAChBgG,MAAM,EAAEyD,gBAAgB,CAACY;MAC3B,CAAC,CAAC;MACF,IAAIgD,OAAO,EAAE;QACXD,UAAU,CAAC,WAAW,EAAE,YAAY;UAClC,IAAI,CAACpC,UAAU,CAAC,CAAC,IAAIrB,SAAS,CAACU,OAAO,KAAK,KAAK,EAAE;YAChD;UACF;UACA5K,WAAW,CAACgO,KAAK,CAACnE,UAAU,CAACe,OAAO,CAAC;UACrC7K,UAAU,CAACkO,WAAW,CAACpE,UAAU,CAACe,OAAO,EAAE,kBAAkB,CAAC;UAC9D1C,eAAe,CAAC,KAAK,CAAC;UACtB2F,YAAY,CAACjG,KAAK,CAAC5B,MAAM,EAAE;YACzB8H,aAAa,EAAEvN,CAAC;YAChBgG,MAAM,EAAEyD,gBAAgB,CAACY;UAC3B,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;IACF,CAAC,MAAM,IAAI,CAAChD,KAAK,CAAC9B,YAAY,IAAI,CAACoI,QAAQ,CAAC,WAAW,CAAC,EAAE;MACxD;MACAhG,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EACD,IAAImF,KAAK,GAAG,SAASA,KAAKA,CAAC9G,MAAM,EAAE4H,UAAU,EAAEjI,QAAQ,EAAE;IACvD,IAAI3B,IAAI,GAAG,CAAC;IACZ,IAAIH,GAAG,GAAG,CAAC;IACX,IAAIgK,eAAe,GAAGlI,QAAQ,IAAItC,aAAa;IAC/C,IAAI,CAACuH,YAAY,CAAC5E,MAAM,CAAC,IAAI6H,eAAe,IAAI,OAAO,KAAKD,UAAU,EAAE;MACtE,IAAIE,cAAc,GAAG;QACnBpB,KAAK,EAAElN,UAAU,CAACmN,aAAa,CAACrD,UAAU,CAACe,OAAO,CAAC;QACnDuC,MAAM,EAAEpN,UAAU,CAACqN,cAAc,CAACvD,UAAU,CAACe,OAAO;MACtD,CAAC;MACDrG,IAAI,GAAG4J,UAAU,CAAC5B,CAAC;MACnBnI,GAAG,GAAG+J,UAAU,CAAC1B,CAAC;MAClB,IAAI6B,qBAAqB,GAAGvC,qBAAqB,CAACxF,MAAM,CAAC;QACvDX,aAAa,GAAG0I,qBAAqB,CAAClK,GAAG;QACzCuB,cAAc,GAAG2I,qBAAqB,CAAC/J,IAAI;MAC7C,QAAQ6J,eAAe;QACrB,KAAK,MAAM;UACT7J,IAAI,GAAGA,IAAI,IAAI8J,cAAc,CAACpB,KAAK,GAAGtH,cAAc,CAAC;UACrDvB,GAAG,GAAGA,GAAG,IAAIiK,cAAc,CAAClB,MAAM,GAAG,CAAC,GAAGvH,aAAa,CAAC;UACvD;QACF,KAAK,OAAO;QACZ,KAAK,OAAO;UACVrB,IAAI,GAAGA,IAAI,GAAGoB,cAAc;UAC5BvB,GAAG,GAAGA,GAAG,IAAIiK,cAAc,CAAClB,MAAM,GAAG,CAAC,GAAGvH,aAAa,CAAC;UACvD;QACF,KAAK,KAAK;UACRrB,IAAI,GAAGA,IAAI,IAAI8J,cAAc,CAACpB,KAAK,GAAG,CAAC,GAAGtH,cAAc,CAAC;UACzDvB,GAAG,GAAGA,GAAG,IAAIiK,cAAc,CAAClB,MAAM,GAAGvH,aAAa,CAAC;UACnD;QACF,KAAK,QAAQ;UACXrB,IAAI,GAAGA,IAAI,IAAI8J,cAAc,CAACpB,KAAK,GAAG,CAAC,GAAGtH,cAAc,CAAC;UACzDvB,GAAG,GAAGA,GAAG,GAAGwB,aAAa;UACzB;MACJ;MACA,IAAIrB,IAAI,IAAI,CAAC,IAAI0F,aAAa,CAACW,OAAO,CAACqC,KAAK,GAAGoB,cAAc,CAACpB,KAAK,EAAE;QACnEpD,UAAU,CAACe,OAAO,CAACtE,KAAK,CAAC/B,IAAI,GAAG,KAAK;QACrCsF,UAAU,CAACe,OAAO,CAACtE,KAAK,CAAChC,KAAK,GAAGiK,MAAM,CAACC,UAAU,GAAGH,cAAc,CAACpB,KAAK,GAAG1I,IAAI,GAAG,IAAI;MACzF,CAAC,MAAM;QACLsF,UAAU,CAACe,OAAO,CAACtE,KAAK,CAAChC,KAAK,GAAG,EAAE;QACnCuF,UAAU,CAACe,OAAO,CAACtE,KAAK,CAAC/B,IAAI,GAAGA,IAAI,GAAG,IAAI;MAC7C;MACAsF,UAAU,CAACe,OAAO,CAACtE,KAAK,CAAClC,GAAG,GAAGA,GAAG,GAAG,IAAI;MACzCrE,UAAU,CAAC0O,QAAQ,CAAC5E,UAAU,CAACe,OAAO,EAAE,kBAAkB,CAAC;IAC7D,CAAC,MAAM;MACL,IAAI8D,GAAG,GAAG3O,UAAU,CAAC4O,qBAAqB,CAACP,eAAe,CAAC;MAC3D,IAAIvI,EAAE,GAAGoF,eAAe,CAAC1E,MAAM,EAAE,IAAI,CAAC,IAAIqB,KAAK,CAAC/B,EAAE,IAAI6I,GAAG,CAAC7I,EAAE;MAC5D,IAAIf,EAAE,GAAGmG,eAAe,CAAC1E,MAAM,EAAE,IAAI,CAAC,IAAIqB,KAAK,CAAC9C,EAAE,IAAI4J,GAAG,CAAC5J,EAAE;MAC5D+E,UAAU,CAACe,OAAO,CAACtE,KAAK,CAACsI,OAAO,GAAG,KAAK;MACxC7O,UAAU,CAAC8O,gBAAgB,CAAChF,UAAU,CAACe,OAAO,EAAErE,MAAM,EAAEV,EAAE,EAAEf,EAAE,EAAE,UAAUgK,kBAAkB,EAAE;QAC5F,IAAIC,qBAAqB,GAAGD,kBAAkB,CAAChK,EAAE;UAC/CkK,GAAG,GAAGD,qBAAqB,CAACxC,CAAC;UAC7B0C,GAAG,GAAGF,qBAAqB,CAACtC,CAAC;QAC/B,IAAIyC,GAAG,GAAGJ,kBAAkB,CAACjJ,EAAE,CAAC0G,CAAC;QACjC,IAAI4C,WAAW,GAAGvH,KAAK,CAAC9C,EAAE,GAAGkK,GAAG,KAAK,QAAQ,IAAIA,GAAG,KAAKE,GAAG,GAAGF,GAAG,GAAGC,GAAG,GAAGH,kBAAkB,CAAChK,EAAE,CAAC,EAAE,CAAChB,MAAM,CAAC4K,GAAG,CAACU,IAAI,CAAC,CAAC;QACrHvF,UAAU,CAACe,OAAO,CAACtE,KAAK,CAACsI,OAAO,GAAG,EAAE;QACrCvG,gBAAgB,CAAC8G,WAAW,CAAC;QAC7BE,uBAAuB,CAACF,WAAW,CAAC;QACpCpP,UAAU,CAAC0O,QAAQ,CAAC5E,UAAU,CAACe,OAAO,EAAE,kBAAkB,CAAC;MAC7D,CAAC,CAAC;IACJ;EACF,CAAC;EACD,IAAIyE,uBAAuB,GAAG,SAASA,uBAAuBA,CAACnJ,QAAQ,EAAE;IACvE,IAAI2D,UAAU,CAACe,OAAO,EAAE;MACtB,IAAItE,KAAK,GAAGgJ,gBAAgB,CAACzF,UAAU,CAACe,OAAO,CAAC;MAChD,IAAI1E,QAAQ,KAAK,MAAM,EAAE;QACvB2D,UAAU,CAACe,OAAO,CAACtE,KAAK,CAAC/B,IAAI,GAAGgL,UAAU,CAACjJ,KAAK,CAAC/B,IAAI,CAAC,GAAGgL,UAAU,CAACjJ,KAAK,CAACkJ,WAAW,CAAC,GAAG,CAAC,GAAG,IAAI;MACnG,CAAC,MAAM,IAAItJ,QAAQ,KAAK,KAAK,EAAE;QAC7B2D,UAAU,CAACe,OAAO,CAACtE,KAAK,CAAClC,GAAG,GAAGmL,UAAU,CAACjJ,KAAK,CAAClC,GAAG,CAAC,GAAGmL,UAAU,CAACjJ,KAAK,CAACmJ,UAAU,CAAC,GAAG,CAAC,GAAG,IAAI;MAChG;IACF;EACF,CAAC;EACD,IAAIC,aAAa,GAAG,SAASC,YAAYA,CAAA,EAAG;IAC1C,IAAI,CAACpE,UAAU,CAAC,CAAC,EAAE;MACjBrB,SAAS,CAACU,OAAO,GAAG,KAAK;IAC3B;EACF,CAAC;EACD,IAAIgF,aAAa,GAAG,SAASC,YAAYA,CAACtP,CAAC,EAAE;IAC3C,IAAI,CAACgL,UAAU,CAAC,CAAC,EAAE;MACjBrB,SAAS,CAACU,OAAO,GAAG,IAAI;MACxBnB,IAAI,CAAClJ,CAAC,CAAC;IACT;EACF,CAAC;EACD,IAAIuP,eAAe,GAAG,SAASA,eAAeA,CAACvJ,MAAM,EAAE;IACrD,IAAIA,MAAM,EAAE;MACV,IAAIwJ,UAAU,GAAGpE,SAAS,CAACpF,MAAM,CAAC;QAChCqF,UAAU,GAAGmE,UAAU,CAACnE,UAAU;QAClCC,UAAU,GAAGkE,UAAU,CAAClE,UAAU;MACpC,IAAI2B,aAAa,GAAGwC,SAAS,CAACzJ,MAAM,CAAC;MACrCqF,UAAU,CAAC1E,OAAO,CAAC,UAAU5B,KAAK,EAAE;QAClC,OAAOkI,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACyC,gBAAgB,CAAC3K,KAAK,EAAEgI,IAAI,CAAC;MAClH,CAAC,CAAC;MACFzB,UAAU,CAAC3E,OAAO,CAAC,UAAU5B,KAAK,EAAE;QAClC,OAAOkI,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACyC,gBAAgB,CAAC3K,KAAK,EAAEmE,IAAI,CAAC;MAClH,CAAC,CAAC;IACJ;EACF,CAAC;EACD,IAAIyG,iBAAiB,GAAG,SAASA,iBAAiBA,CAAC3J,MAAM,EAAE;IACzD,IAAIA,MAAM,EAAE;MACV,IAAI4J,WAAW,GAAGxE,SAAS,CAACpF,MAAM,CAAC;QACjCqF,UAAU,GAAGuE,WAAW,CAACvE,UAAU;QACnCC,UAAU,GAAGsE,WAAW,CAACtE,UAAU;MACrC,IAAI2B,aAAa,GAAGwC,SAAS,CAACzJ,MAAM,CAAC;MACrCqF,UAAU,CAAC1E,OAAO,CAAC,UAAU5B,KAAK,EAAE;QAClC,OAAOkI,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC4C,mBAAmB,CAAC9K,KAAK,EAAEgI,IAAI,CAAC;MACrH,CAAC,CAAC;MACFzB,UAAU,CAAC3E,OAAO,CAAC,UAAU5B,KAAK,EAAE;QAClC,OAAOkI,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC4C,mBAAmB,CAAC9K,KAAK,EAAEmE,IAAI,CAAC;MACrH,CAAC,CAAC;IACJ;EACF,CAAC;EACD,IAAIyE,QAAQ,GAAG,SAASA,QAAQA,CAACmC,SAAS,EAAE;IAC1C,OAAOpF,eAAe,CAACjB,gBAAgB,CAACY,OAAO,EAAEyF,SAAS,CAACC,WAAW,CAAC,CAAC,CAAC,IAAI1I,KAAK,CAACyI,SAAS,CAAC;EAC/F,CAAC;EACD,IAAI1C,UAAU,GAAG,SAASA,UAAUA,CAAC0C,SAAS,EAAE7G,QAAQ,EAAE;IACxDuE,aAAa,CAAC,CAAC;IACf,IAAIwC,KAAK,GAAGrC,QAAQ,CAACmC,SAAS,CAAC;IAC/BE,KAAK,GAAGpG,QAAQ,CAACS,OAAO,CAAC,EAAE,CAAC9G,MAAM,CAACuM,SAAS,CAAC,CAAC,GAAGG,UAAU,CAAC,YAAY;MACtE,OAAOhH,QAAQ,CAAC,CAAC;IACnB,CAAC,EAAE+G,KAAK,CAAC,GAAG/G,QAAQ,CAAC,CAAC;EACxB,CAAC;EACD,IAAIqE,YAAY,GAAG,SAASA,YAAYA,CAACrE,QAAQ,EAAE;IACjD,IAAIA,QAAQ,EAAE;MACZ,KAAK,IAAIiH,IAAI,GAAGjQ,SAAS,CAACC,MAAM,EAAEiQ,MAAM,GAAG,IAAIvO,KAAK,CAACsO,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEE,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGF,IAAI,EAAEE,IAAI,EAAE,EAAE;QAC5GD,MAAM,CAACC,IAAI,GAAG,CAAC,CAAC,GAAGnQ,SAAS,CAACmQ,IAAI,CAAC;MACpC;MACA,IAAIC,MAAM,GAAGpH,QAAQ,CAAC1I,KAAK,CAAC,KAAK,CAAC,EAAE4P,MAAM,CAAC;MAC3C,IAAIE,MAAM,KAAKlK,SAAS,EAAE;QACxBkK,MAAM,GAAG,IAAI;MACf;MACA,OAAOA,MAAM;IACf;IACA,OAAO,IAAI;EACb,CAAC;EACD,IAAI7C,aAAa,GAAG,SAASA,aAAaA,CAAA,EAAG;IAC3C5N,MAAM,CAAC0Q,MAAM,CAAC1G,QAAQ,CAACS,OAAO,CAAC,CAAC1D,OAAO,CAAC,UAAUxG,CAAC,EAAE;MACnD,OAAOoQ,YAAY,CAACpQ,CAAC,CAAC;IACxB,CAAC,CAAC;EACJ,CAAC;EACD,IAAIsP,SAAS,GAAG,SAASA,SAASA,CAACzJ,MAAM,EAAE;IACzC,IAAIA,MAAM,EAAE;MACV,IAAI+E,gBAAgB,CAAC/E,MAAM,CAAC,EAAE;QAC5B,IAAI,CAACA,MAAM,CAACwK,UAAU,EAAE;UACtB,IAAIC,OAAO,GAAG7E,QAAQ,CAAC8E,aAAa,CAAC,KAAK,CAAC;UAC3C,IAAIC,cAAc,GAAG3K,MAAM,CAAC4K,QAAQ,KAAK,OAAO;UAChD,IAAID,cAAc,EAAE;YAClBnR,UAAU,CAACqR,kBAAkB,CAACJ,OAAO,EAAE,yCAAyC,CAAC;UACnF,CAAC,MAAM;YACLjR,UAAU,CAAC0O,QAAQ,CAACuC,OAAO,EAAE,0BAA0B,CAAC;UAC1D;UACAzK,MAAM,CAAC8K,UAAU,CAACC,YAAY,CAACN,OAAO,EAAEzK,MAAM,CAAC;UAC/CyK,OAAO,CAAC9E,WAAW,CAAC3F,MAAM,CAAC;UAC3BA,MAAM,CAACwK,UAAU,GAAG,IAAI;UACxB,OAAOC,OAAO;QAChB;QACA,OAAOzK,MAAM,CAACgL,aAAa;MAC7B,CAAC,MAAM,IAAIhL,MAAM,CAACwK,UAAU,EAAE;QAC5B,IAAIS,qBAAqB;QACzB,CAACA,qBAAqB,GAAGjL,MAAM,CAACgL,aAAa,EAAEE,WAAW,CAAC3Q,KAAK,CAAC0Q,qBAAqB,EAAE1O,kBAAkB,CAACyD,MAAM,CAACgL,aAAa,CAACG,UAAU,CAAC,CAAC;QAC5I,OAAOnL,MAAM,CAACwK,UAAU;MAC1B;MACA,OAAOxK,MAAM;IACf;IACA,OAAO,IAAI;EACb,CAAC;EACD,IAAIoL,kBAAkB,GAAG,SAASA,kBAAkBA,CAACpL,MAAM,EAAE;IAC3DqL,kBAAkB,CAACrL,MAAM,CAAC;IAC1BsL,gBAAgB,CAACtL,MAAM,CAAC;EAC1B,CAAC;EACD,IAAIsL,gBAAgB,GAAG,SAASA,gBAAgBA,CAACtL,MAAM,EAAE;IACvDuL,wBAAwB,CAACvL,MAAM,IAAIqB,KAAK,CAACrB,MAAM,EAAEuJ,eAAe,CAAC;EACnE,CAAC;EACD,IAAI8B,kBAAkB,GAAG,SAASA,kBAAkBA,CAACrL,MAAM,EAAE;IAC3DuL,wBAAwB,CAACvL,MAAM,IAAIqB,KAAK,CAACrB,MAAM,EAAE2J,iBAAiB,CAAC;EACrE,CAAC;EACD,IAAI4B,wBAAwB,GAAG,SAASA,wBAAwBA,CAACvL,MAAM,EAAEwL,SAAS,EAAE;IAClFxL,MAAM,GAAGtG,WAAW,CAAC+R,aAAa,CAACzL,MAAM,CAAC;IAC1C,IAAIA,MAAM,EAAE;MACV,IAAIxG,UAAU,CAACkS,SAAS,CAAC1L,MAAM,CAAC,EAAE;QAChCwL,SAAS,CAACxL,MAAM,CAAC;MACnB,CAAC,MAAM;QACL,IAAI2L,QAAQ,GAAG,SAASA,QAAQA,CAAC3L,MAAM,EAAE;UACvC,IAAI4L,OAAO,GAAGpS,UAAU,CAACqS,IAAI,CAACjG,QAAQ,EAAE5F,MAAM,CAAC;UAC/C4L,OAAO,CAACjL,OAAO,CAAC,UAAUmL,EAAE,EAAE;YAC5BN,SAAS,CAACM,EAAE,CAAC;UACf,CAAC,CAAC;QACJ,CAAC;QACD,IAAI9L,MAAM,YAAYpE,KAAK,EAAE;UAC3BoE,MAAM,CAACW,OAAO,CAAC,UAAUxG,CAAC,EAAE;YAC1BwR,QAAQ,CAACxR,CAAC,CAAC;UACb,CAAC,CAAC;QACJ,CAAC,MAAM;UACLwR,QAAQ,CAAC3L,MAAM,CAAC;QAClB;MACF;IACF;EACF,CAAC;EACD7G,cAAc,CAAC,YAAY;IACzB,IAAIuI,YAAY,IAAI+B,gBAAgB,CAACY,OAAO,IAAIQ,UAAU,CAACpB,gBAAgB,CAACY,OAAO,CAAC,EAAE;MACpFnB,IAAI,CAAC,CAAC;IACR;EACF,CAAC,CAAC;EACF9J,eAAe,CAAC,YAAY;IAC1BkS,gBAAgB,CAAC,CAAC;IAClB,OAAO,YAAY;MACjBD,kBAAkB,CAAC,CAAC;IACtB,CAAC;EACH,CAAC,EAAE,CAACtE,IAAI,EAAE7D,IAAI,EAAE7B,KAAK,CAACrB,MAAM,CAAC,CAAC;EAC9B5G,eAAe,CAAC,YAAY;IAC1B,IAAIsI,YAAY,EAAE;MAChB,IAAI/B,QAAQ,GAAG4F,WAAW,CAAC9B,gBAAgB,CAACY,OAAO,CAAC;MACpD,IAAI0H,SAAS,GAAGrH,eAAe,CAACjB,gBAAgB,CAACY,OAAO,EAAE,WAAW,CAAC;MACtEvC,gBAAgB,CAACnC,QAAQ,CAAC;MAC1BsC,iBAAiB,CAAC8J,SAAS,CAAC;MAC5BjG,kBAAkB,CAACnG,QAAQ,CAAC;MAC5BuE,wBAAwB,CAAC,CAAC;MAC1BK,yBAAyB,CAAC,CAAC;IAC7B,CAAC,MAAM;MACLzC,gBAAgB,CAACT,KAAK,CAAC1B,QAAQ,IAAI,OAAO,CAAC;MAC3CsC,iBAAiB,CAAC,EAAE,CAAC;MACrBwB,gBAAgB,CAACY,OAAO,GAAG,IAAI;MAC/BX,aAAa,CAACW,OAAO,GAAG,IAAI;MAC5BV,SAAS,CAACU,OAAO,GAAG,IAAI;IAC1B;IACA,OAAO,YAAY;MACjBF,0BAA0B,CAAC,CAAC;MAC5BK,2BAA2B,CAAC,CAAC;IAC/B,CAAC;EACH,CAAC,EAAE,CAAC9C,YAAY,CAAC,CAAC;EAClBtI,eAAe,CAAC,YAAY;IAC1B,IAAIuG,QAAQ,GAAG4F,WAAW,CAAC9B,gBAAgB,CAACY,OAAO,CAAC;IACpD,IAAI3C,YAAY,IAAI/B,QAAQ,KAAK,OAAO,EAAE;MACxCyH,UAAU,CAAC,aAAa,EAAE,YAAY;QACpC3B,UAAU,CAAChC,gBAAgB,CAACY,OAAO,EAAE,YAAY;UAC/CyC,KAAK,CAACrD,gBAAgB,CAACY,OAAO,CAAC;QACjC,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAAChD,KAAK,CAACxC,OAAO,CAAC,CAAC;EACnBxF,gBAAgB,CAAC,YAAY;IAC3B6J,IAAI,CAAC,CAAC;IACNzJ,WAAW,CAACgO,KAAK,CAACnE,UAAU,CAACe,OAAO,CAAC;EACvC,CAAC,CAAC;EACF7L,KAAK,CAACwT,mBAAmB,CAAC9K,GAAG,EAAE,YAAY;IACzC,OAAO;MACLG,KAAK,EAAEA,KAAK;MACZ+J,kBAAkB,EAAEA,kBAAkB;MACtCE,gBAAgB,EAAEA,gBAAgB;MAClCD,kBAAkB,EAAEA,kBAAkB;MACtCtE,IAAI,EAAEA,IAAI;MACV7D,IAAI,EAAEA,IAAI;MACV+I,UAAU,EAAE,SAASA,UAAUA,CAAA,EAAG;QAChC,OAAO3I,UAAU,CAACe,OAAO;MAC3B,CAAC;MACDoF,SAAS,EAAE,SAASA,SAASA,CAAA,EAAG;QAC9B,OAAOhG,gBAAgB,CAACY,OAAO;MACjC;IACF,CAAC;EACH,CAAC,CAAC;EACF,IAAIqG,aAAa,GAAG,SAASA,aAAaA,CAAA,EAAG;IAC3C,IAAIxD,KAAK,GAAGzC,oBAAoB,CAAChB,gBAAgB,CAACY,OAAO,CAAC;IAC1D,IAAI6H,SAAS,GAAG/K,UAAU,CAAC;MACzBjC,EAAE,EAAEmC,KAAK,CAACnC,EAAE;MACZP,SAAS,EAAEpF,UAAU,CAAC8H,KAAK,CAAC1C,SAAS,EAAEmE,EAAE,CAAC,MAAM,EAAE;QAChDzF,aAAa,EAAEA,aAAa;QAC5BC,cAAc,EAAEA;MAClB,CAAC,CAAC,CAAC;MACHyC,KAAK,EAAEsB,KAAK,CAACtB,KAAK;MAClBoM,IAAI,EAAE,SAAS;MACf,aAAa,EAAEzK,YAAY;MAC3B0H,YAAY,EAAE,SAASA,YAAYA,CAACpP,CAAC,EAAE;QACrC,OAAOmP,aAAa,CAAC,CAAC;MACxB,CAAC;MACDG,YAAY,EAAE,SAASA,YAAYA,CAACtP,CAAC,EAAE;QACrC,OAAOqP,aAAa,CAACrP,CAAC,CAAC;MACzB;IACF,CAAC,EAAEkE,WAAW,CAACkO,aAAa,CAAC/K,KAAK,CAAC,EAAEwB,GAAG,CAAC,MAAM,CAAC,CAAC;IACjD,IAAIwJ,UAAU,GAAGlL,UAAU,CAAC;MAC1BxC,SAAS,EAAEmE,EAAE,CAAC,OAAO,CAAC;MACtB/C,KAAK,EAAEgD,EAAE,CAAC,OAAO,EAAErC,aAAa,CAAC,CAAC,CAAC,EAAE8B,QAAQ,CAAC;IAChD,CAAC,EAAEK,GAAG,CAAC,OAAO,CAAC,CAAC;IAChB,IAAIyJ,SAAS,GAAGnL,UAAU,CAAC;MACzBxC,SAAS,EAAEmE,EAAE,CAAC,MAAM;IACtB,CAAC,EAAED,GAAG,CAAC,MAAM,CAAC,CAAC;IACf,OAAO,aAAarK,KAAK,CAACkS,aAAa,CAAC,KAAK,EAAE/Q,QAAQ,CAAC;MACtDuH,GAAG,EAAEoC;IACP,CAAC,EAAE4I,SAAS,CAAC,EAAE,aAAa1T,KAAK,CAACkS,aAAa,CAAC,KAAK,EAAE2B,UAAU,CAAC,EAAE,aAAa7T,KAAK,CAACkS,aAAa,CAAC,KAAK,EAAE/Q,QAAQ,CAAC;MACnHuH,GAAG,EAAEsC;IACP,CAAC,EAAE8I,SAAS,CAAC,EAAEpF,KAAK,IAAI7F,KAAK,CAACnB,QAAQ,CAAC,CAAC;EAC1C,CAAC;EACD,IAAIwB,YAAY,EAAE;IAChB,IAAIkK,OAAO,GAAGlB,aAAa,CAAC,CAAC;IAC7B,OAAO,aAAalS,KAAK,CAACkS,aAAa,CAACpR,MAAM,EAAE;MAC9CsS,OAAO,EAAEA,OAAO;MAChBtN,QAAQ,EAAE+C,KAAK,CAAC/C,QAAQ;MACxBoE,OAAO,EAAE;IACX,CAAC,CAAC;EACJ;EACA,OAAO,IAAI;AACb,CAAC,CAAC,CAAC;AACH5B,OAAO,CAACyL,WAAW,GAAG,SAAS;AAE/B,SAASzL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}