{"ast": null, "code": "import { __read, __spreadArray } from \"tslib\";\nexport var timeoutProvider = {\n  setTimeout: function (handler, timeout) {\n    var args = [];\n    for (var _i = 2; _i < arguments.length; _i++) {\n      args[_i - 2] = arguments[_i];\n    }\n    var delegate = timeoutProvider.delegate;\n    if (delegate === null || delegate === void 0 ? void 0 : delegate.setTimeout) {\n      return delegate.setTimeout.apply(delegate, __spreadArray([handler, timeout], __read(args)));\n    }\n    return setTimeout.apply(void 0, __spreadArray([handler, timeout], __read(args)));\n  },\n  clearTimeout: function (handle) {\n    var delegate = timeoutProvider.delegate;\n    return ((delegate === null || delegate === void 0 ? void 0 : delegate.clearTimeout) || clearTimeout)(handle);\n  },\n  delegate: undefined\n};", "map": {"version": 3, "names": ["timeout<PERSON>rovider", "setTimeout", "handler", "timeout", "args", "_i", "arguments", "length", "delegate", "apply", "__spread<PERSON><PERSON>y", "__read", "clearTimeout", "handle", "undefined"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\scheduler\\timeoutProvider.ts"], "sourcesContent": ["import type { TimerHandle } from './timerHandle';\ntype SetTimeoutFunction = (handler: () => void, timeout?: number, ...args: any[]) => TimerHandle;\ntype ClearTimeoutFunction = (handle: TimerHandle) => void;\n\ninterface TimeoutProvider {\n  setTimeout: SetTimeoutFunction;\n  clearTimeout: ClearTimeoutFunction;\n  delegate:\n    | {\n        setTimeout: SetTimeoutFunction;\n        clearTimeout: ClearTimeoutFunction;\n      }\n    | undefined;\n}\n\nexport const timeoutProvider: TimeoutProvider = {\n  // When accessing the delegate, use the variable rather than `this` so that\n  // the functions can be called without being bound to the provider.\n  setTimeout(handler: () => void, timeout?: number, ...args) {\n    const { delegate } = timeoutProvider;\n    if (delegate?.setTimeout) {\n      return delegate.setTimeout(handler, timeout, ...args);\n    }\n    return setTimeout(handler, timeout, ...args);\n  },\n  clearTimeout(handle) {\n    const { delegate } = timeoutProvider;\n    return (delegate?.clearTimeout || clearTimeout)(handle as any);\n  },\n  delegate: undefined,\n};\n"], "mappings": ";AAeA,OAAO,IAAMA,eAAe,GAAoB;EAG9CC,UAAU,EAAV,SAAAA,CAAWC,OAAmB,EAAEC,OAAgB;IAAE,IAAAC,IAAA;SAAA,IAAAC,EAAA,IAAO,EAAPA,EAAA,GAAAC,SAAA,CAAAC,MAAO,EAAPF,EAAA,EAAO;MAAPD,IAAA,CAAAC,EAAA,QAAAC,SAAA,CAAAD,EAAA;;IACxC,IAAAG,QAAQ,GAAKR,eAAe,CAAAQ,QAApB;IAChB,IAAIA,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEP,UAAU,EAAE;MACxB,OAAOO,QAAQ,CAACP,UAAU,CAAAQ,KAAA,CAAnBD,QAAQ,EAAAE,aAAA,EAAYR,OAAO,EAAEC,OAAO,GAAAQ,MAAA,CAAKP,IAAI;;IAEtD,OAAOH,UAAU,CAAAQ,KAAA,SAAAC,aAAA,EAACR,OAAO,EAAEC,OAAO,GAAAQ,MAAA,CAAKP,IAAI;EAC7C,CAAC;EACDQ,YAAY,EAAZ,SAAAA,CAAaC,MAAM;IACT,IAAAL,QAAQ,GAAKR,eAAe,CAAAQ,QAApB;IAChB,OAAO,CAAC,CAAAA,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEI,YAAY,KAAIA,YAAY,EAAEC,MAAa,CAAC;EAChE,CAAC;EACDL,QAAQ,EAAEM;CACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}