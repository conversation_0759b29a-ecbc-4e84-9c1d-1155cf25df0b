{"ast": null, "code": "import CORE_ACTIONS from \"../actions\";\n\n/**\r\n * Action creator for setup current language.\r\n *\r\n * https://www.youtube.com/watch?v=9jULHSe41ls\r\n *\r\n * @param {*} selectedLangauge It is selected langauge code. value = \"en\" or \"fr\".\r\n * @returns\r\n */\nexport const setLanguage = selectedLangauge => {\n  return dispatch => {\n    dispatch({\n      /** type is a generic name.\r\n       *  It is defined which action routine\r\n       *  will be applied to update the state. */\n      type: CORE_ACTIONS.SET_LANGUAGE,\n      /** payload is a generic name.\r\n       * It is container to transfer date which is required to be applied to the state.\r\n       * */\n      payload: selectedLangauge\n    });\n  };\n};", "map": {"version": 3, "names": ["CORE_ACTIONS", "setLanguage", "selectedLanga<PERSON>", "dispatch", "type", "SET_LANGUAGE", "payload"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/redux/actionCreators/languageCreator.js"], "sourcesContent": ["import CORE_ACTIONS from \"../actions\";\r\n\r\n/**\r\n * Action creator for setup current language.\r\n *\r\n * https://www.youtube.com/watch?v=9jULHSe41ls\r\n *\r\n * @param {*} selectedLangauge It is selected langauge code. value = \"en\" or \"fr\".\r\n * @returns\r\n */\r\nexport const setLanguage = (selectedLangauge) => {\r\n  return (dispatch) => {\r\n    dispatch({\r\n      /** type is a generic name.\r\n       *  It is defined which action routine\r\n       *  will be applied to update the state. */\r\n      type: CORE_ACTIONS.SET_LANGUAGE,\r\n      /** payload is a generic name.\r\n       * It is container to transfer date which is required to be applied to the state.\r\n       * */\r\n      payload: selectedLangauge,\r\n    });\r\n  };\r\n};\r\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,YAAY;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,WAAW,GAAIC,gBAAgB,IAAK;EAC/C,OAAQC,QAAQ,IAAK;IACnBA,QAAQ,CAAC;MACP;AACN;AACA;MACMC,IAAI,EAAEJ,YAAY,CAACK,YAAY;MAC/B;AACN;AACA;MACMC,OAAO,EAAEJ;IACX,CAAC,CAAC;EACJ,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}