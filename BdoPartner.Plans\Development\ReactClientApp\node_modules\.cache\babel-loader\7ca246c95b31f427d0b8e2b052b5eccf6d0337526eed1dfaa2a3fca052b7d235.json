{"ast": null, "code": "import { __values } from \"tslib\";\nimport { Subject } from '../Subject';\nimport { Subscription } from '../Subscription';\nimport { operate } from '../util/lift';\nimport { innerFrom } from '../observable/innerFrom';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { noop } from '../util/noop';\nimport { arrRemove } from '../util/arrRemove';\nexport function windowToggle(openings, closingSelector) {\n  return operate(function (source, subscriber) {\n    var windows = [];\n    var handleError = function (err) {\n      while (0 < windows.length) {\n        windows.shift().error(err);\n      }\n      subscriber.error(err);\n    };\n    innerFrom(openings).subscribe(createOperatorSubscriber(subscriber, function (openValue) {\n      var window = new Subject();\n      windows.push(window);\n      var closingSubscription = new Subscription();\n      var closeWindow = function () {\n        arrRemove(windows, window);\n        window.complete();\n        closingSubscription.unsubscribe();\n      };\n      var closingNotifier;\n      try {\n        closingNotifier = innerFrom(closingSelector(openValue));\n      } catch (err) {\n        handleError(err);\n        return;\n      }\n      subscriber.next(window.asObservable());\n      closingSubscription.add(closingNotifier.subscribe(createOperatorSubscriber(subscriber, closeWindow, noop, handleError)));\n    }, noop));\n    source.subscribe(createOperatorSubscriber(subscriber, function (value) {\n      var e_1, _a;\n      var windowsCopy = windows.slice();\n      try {\n        for (var windowsCopy_1 = __values(windowsCopy), windowsCopy_1_1 = windowsCopy_1.next(); !windowsCopy_1_1.done; windowsCopy_1_1 = windowsCopy_1.next()) {\n          var window_1 = windowsCopy_1_1.value;\n          window_1.next(value);\n        }\n      } catch (e_1_1) {\n        e_1 = {\n          error: e_1_1\n        };\n      } finally {\n        try {\n          if (windowsCopy_1_1 && !windowsCopy_1_1.done && (_a = windowsCopy_1.return)) _a.call(windowsCopy_1);\n        } finally {\n          if (e_1) throw e_1.error;\n        }\n      }\n    }, function () {\n      while (0 < windows.length) {\n        windows.shift().complete();\n      }\n      subscriber.complete();\n    }, handleError, function () {\n      while (0 < windows.length) {\n        windows.shift().unsubscribe();\n      }\n    }));\n  });\n}", "map": {"version": 3, "names": ["Subject", "Subscription", "operate", "innerFrom", "createOperatorSubscriber", "noop", "arr<PERSON><PERSON><PERSON>", "windowToggle", "openings", "closingSelector", "source", "subscriber", "windows", "handleError", "err", "length", "shift", "error", "subscribe", "openValue", "window", "push", "closingSubscription", "closeWindow", "complete", "unsubscribe", "closingNotifier", "next", "asObservable", "add", "value", "windowsCopy", "slice", "windowsCopy_1", "__values", "windowsCopy_1_1", "done", "window_1"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\windowToggle.ts"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { Subject } from '../Subject';\nimport { Subscription } from '../Subscription';\nimport { ObservableInput, OperatorFunction } from '../types';\nimport { operate } from '../util/lift';\nimport { innerFrom } from '../observable/innerFrom';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { noop } from '../util/noop';\nimport { arrRemove } from '../util/arrRemove';\n\n/**\n * Branch out the source Observable values as a nested Observable starting from\n * an emission from `openings` and ending when the output of `closingSelector`\n * emits.\n *\n * <span class=\"informal\">It's like {@link bufferToggle}, but emits a nested\n * Observable instead of an array.</span>\n *\n * ![](windowToggle.png)\n *\n * Returns an Observable that emits windows of items it collects from the source\n * Observable. The output Observable emits windows that contain those items\n * emitted by the source Observable between the time when the `openings`\n * Observable emits an item and when the Observable returned by\n * `closingSelector` emits an item.\n *\n * ## Example\n *\n * Every other second, emit the click events from the next 500ms\n *\n * ```ts\n * import { fromEvent, interval, windowToggle, EMPTY, mergeAll } from 'rxjs';\n *\n * const clicks = fromEvent(document, 'click');\n * const openings = interval(1000);\n * const result = clicks.pipe(\n *   windowToggle(openings, i => i % 2 ? interval(500) : EMPTY),\n *   mergeAll()\n * );\n * result.subscribe(x => console.log(x));\n * ```\n *\n * @see {@link window}\n * @see {@link windowCount}\n * @see {@link windowTime}\n * @see {@link windowWhen}\n * @see {@link bufferToggle}\n *\n * @param openings An observable of notifications to start new windows.\n * @param closingSelector A function that takes the value emitted by the\n * `openings` observable and returns an Observable, which, when it emits a next\n * notification, signals that the associated window should complete.\n * @return A function that returns an Observable of windows, which in turn are\n * Observables.\n */\nexport function windowToggle<T, O>(\n  openings: ObservableInput<O>,\n  closingSelector: (openValue: O) => ObservableInput<any>\n): OperatorFunction<T, Observable<T>> {\n  return operate((source, subscriber) => {\n    const windows: Subject<T>[] = [];\n\n    const handleError = (err: any) => {\n      while (0 < windows.length) {\n        windows.shift()!.error(err);\n      }\n      subscriber.error(err);\n    };\n\n    innerFrom(openings).subscribe(\n      createOperatorSubscriber(\n        subscriber,\n        (openValue) => {\n          const window = new Subject<T>();\n          windows.push(window);\n          const closingSubscription = new Subscription();\n          const closeWindow = () => {\n            arrRemove(windows, window);\n            window.complete();\n            closingSubscription.unsubscribe();\n          };\n\n          let closingNotifier: Observable<any>;\n          try {\n            closingNotifier = innerFrom(closingSelector(openValue));\n          } catch (err) {\n            handleError(err);\n            return;\n          }\n\n          subscriber.next(window.asObservable());\n\n          closingSubscription.add(closingNotifier.subscribe(createOperatorSubscriber(subscriber, closeWindow, noop, handleError)));\n        },\n        noop\n      )\n    );\n\n    // Subscribe to the source to get things started.\n    source.subscribe(\n      createOperatorSubscriber(\n        subscriber,\n        (value: T) => {\n          // Copy the windows array before we emit to\n          // make sure we don't have issues with reentrant code.\n          const windowsCopy = windows.slice();\n          for (const window of windowsCopy) {\n            window.next(value);\n          }\n        },\n        () => {\n          // Complete all of our windows before we complete.\n          while (0 < windows.length) {\n            windows.shift()!.complete();\n          }\n          subscriber.complete();\n        },\n        handleError,\n        () => {\n          // Add this finalization so that all window subjects are\n          // disposed of. This way, if a user tries to subscribe\n          // to a window *after* the outer subscription has been unsubscribed,\n          // they will get an error, instead of waiting forever to\n          // see if a value arrives.\n          while (0 < windows.length) {\n            windows.shift()!.unsubscribe();\n          }\n        }\n      )\n    );\n  });\n}\n"], "mappings": ";AACA,SAASA,OAAO,QAAQ,YAAY;AACpC,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,SAAS,QAAQ,yBAAyB;AACnD,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,SAASC,IAAI,QAAQ,cAAc;AACnC,SAASC,SAAS,QAAQ,mBAAmB;AA+C7C,OAAM,SAAUC,YAAYA,CAC1BC,QAA4B,EAC5BC,eAAuD;EAEvD,OAAOP,OAAO,CAAC,UAACQ,MAAM,EAAEC,UAAU;IAChC,IAAMC,OAAO,GAAiB,EAAE;IAEhC,IAAMC,WAAW,GAAG,SAAAA,CAACC,GAAQ;MAC3B,OAAO,CAAC,GAAGF,OAAO,CAACG,MAAM,EAAE;QACzBH,OAAO,CAACI,KAAK,EAAG,CAACC,KAAK,CAACH,GAAG,CAAC;;MAE7BH,UAAU,CAACM,KAAK,CAACH,GAAG,CAAC;IACvB,CAAC;IAEDX,SAAS,CAACK,QAAQ,CAAC,CAACU,SAAS,CAC3Bd,wBAAwB,CACtBO,UAAU,EACV,UAACQ,SAAS;MACR,IAAMC,MAAM,GAAG,IAAIpB,OAAO,EAAK;MAC/BY,OAAO,CAACS,IAAI,CAACD,MAAM,CAAC;MACpB,IAAME,mBAAmB,GAAG,IAAIrB,YAAY,EAAE;MAC9C,IAAMsB,WAAW,GAAG,SAAAA,CAAA;QAClBjB,SAAS,CAACM,OAAO,EAAEQ,MAAM,CAAC;QAC1BA,MAAM,CAACI,QAAQ,EAAE;QACjBF,mBAAmB,CAACG,WAAW,EAAE;MACnC,CAAC;MAED,IAAIC,eAAgC;MACpC,IAAI;QACFA,eAAe,GAAGvB,SAAS,CAACM,eAAe,CAACU,SAAS,CAAC,CAAC;OACxD,CAAC,OAAOL,GAAG,EAAE;QACZD,WAAW,CAACC,GAAG,CAAC;QAChB;;MAGFH,UAAU,CAACgB,IAAI,CAACP,MAAM,CAACQ,YAAY,EAAE,CAAC;MAEtCN,mBAAmB,CAACO,GAAG,CAACH,eAAe,CAACR,SAAS,CAACd,wBAAwB,CAACO,UAAU,EAAEY,WAAW,EAAElB,IAAI,EAAEQ,WAAW,CAAC,CAAC,CAAC;IAC1H,CAAC,EACDR,IAAI,CACL,CACF;IAGDK,MAAM,CAACQ,SAAS,CACdd,wBAAwB,CACtBO,UAAU,EACV,UAACmB,KAAQ;;MAGP,IAAMC,WAAW,GAAGnB,OAAO,CAACoB,KAAK,EAAE;;QACnC,KAAqB,IAAAC,aAAA,GAAAC,QAAA,CAAAH,WAAW,GAAAI,eAAA,GAAAF,aAAA,CAAAN,IAAA,KAAAQ,eAAA,CAAAC,IAAA,EAAAD,eAAA,GAAAF,aAAA,CAAAN,IAAA,IAAE;UAA7B,IAAMU,QAAM,GAAAF,eAAA,CAAAL,KAAA;UACfO,QAAM,CAACV,IAAI,CAACG,KAAK,CAAC;;;;;;;;;;;;;IAEtB,CAAC,EACD;MAEE,OAAO,CAAC,GAAGlB,OAAO,CAACG,MAAM,EAAE;QACzBH,OAAO,CAACI,KAAK,EAAG,CAACQ,QAAQ,EAAE;;MAE7Bb,UAAU,CAACa,QAAQ,EAAE;IACvB,CAAC,EACDX,WAAW,EACX;MAME,OAAO,CAAC,GAAGD,OAAO,CAACG,MAAM,EAAE;QACzBH,OAAO,CAACI,KAAK,EAAG,CAACS,WAAW,EAAE;;IAElC,CAAC,CACF,CACF;EACH,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}