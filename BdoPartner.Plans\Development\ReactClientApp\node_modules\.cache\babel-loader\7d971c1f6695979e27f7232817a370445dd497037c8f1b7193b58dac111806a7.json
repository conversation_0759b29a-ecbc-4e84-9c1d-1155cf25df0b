{"ast": null, "code": "import { map } from './map';\nimport { innerFrom } from '../observable/innerFrom';\nimport { operate } from '../util/lift';\nimport { mergeInternals } from './mergeInternals';\nimport { isFunction } from '../util/isFunction';\nexport function mergeMap(project, resultSelector, concurrent) {\n  if (concurrent === void 0) {\n    concurrent = Infinity;\n  }\n  if (isFunction(resultSelector)) {\n    return mergeMap(function (a, i) {\n      return map(function (b, ii) {\n        return resultSelector(a, b, i, ii);\n      })(innerFrom(project(a, i)));\n    }, concurrent);\n  } else if (typeof resultSelector === 'number') {\n    concurrent = resultSelector;\n  }\n  return operate(function (source, subscriber) {\n    return mergeInternals(source, subscriber, project, concurrent);\n  });\n}", "map": {"version": 3, "names": ["map", "innerFrom", "operate", "mergeInternals", "isFunction", "mergeMap", "project", "resultSelector", "concurrent", "Infinity", "a", "i", "b", "ii", "source", "subscriber"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\mergeMap.ts"], "sourcesContent": ["import { ObservableInput, OperatorFunction, ObservedValueOf } from '../types';\nimport { map } from './map';\nimport { innerFrom } from '../observable/innerFrom';\nimport { operate } from '../util/lift';\nimport { mergeInternals } from './mergeInternals';\nimport { isFunction } from '../util/isFunction';\n\n/* tslint:disable:max-line-length */\nexport function mergeMap<T, O extends ObservableInput<any>>(\n  project: (value: T, index: number) => O,\n  concurrent?: number\n): OperatorFunction<T, ObservedValueOf<O>>;\n/** @deprecated The `resultSelector` parameter will be removed in v8. Use an inner `map` instead. Details: https://rxjs.dev/deprecations/resultSelector */\nexport function mergeMap<T, O extends ObservableInput<any>>(\n  project: (value: T, index: number) => O,\n  resultSelector: undefined,\n  concurrent?: number\n): OperatorFunction<T, ObservedValueOf<O>>;\n/** @deprecated The `resultSelector` parameter will be removed in v8. Use an inner `map` instead. Details: https://rxjs.dev/deprecations/resultSelector */\nexport function mergeMap<T, R, O extends ObservableInput<any>>(\n  project: (value: T, index: number) => O,\n  resultSelector: (outerValue: T, innerValue: ObservedValueOf<O>, outerIndex: number, innerIndex: number) => R,\n  concurrent?: number\n): OperatorFunction<T, R>;\n/* tslint:enable:max-line-length */\n\n/**\n * Projects each source value to an Observable which is merged in the output\n * Observable.\n *\n * <span class=\"informal\">Maps each value to an Observable, then flattens all of\n * these inner Observables using {@link mergeAll}.</span>\n *\n * ![](mergeMap.png)\n *\n * Returns an Observable that emits items based on applying a function that you\n * supply to each item emitted by the source Observable, where that function\n * returns an Observable, and then merging those resulting Observables and\n * emitting the results of this merger.\n *\n * ## Example\n *\n * Map and flatten each letter to an Observable ticking every 1 second\n *\n * ```ts\n * import { of, mergeMap, interval, map } from 'rxjs';\n *\n * const letters = of('a', 'b', 'c');\n * const result = letters.pipe(\n *   mergeMap(x => interval(1000).pipe(map(i => x + i)))\n * );\n *\n * result.subscribe(x => console.log(x));\n *\n * // Results in the following:\n * // a0\n * // b0\n * // c0\n * // a1\n * // b1\n * // c1\n * // continues to list a, b, c every second with respective ascending integers\n * ```\n *\n * @see {@link concatMap}\n * @see {@link exhaustMap}\n * @see {@link merge}\n * @see {@link mergeAll}\n * @see {@link mergeMapTo}\n * @see {@link mergeScan}\n * @see {@link switchMap}\n *\n * @param project A function that, when applied to an item emitted by the source\n * Observable, returns an Observable.\n * @param concurrent Maximum number of `ObservableInput`s being subscribed to concurrently.\n * @return A function that returns an Observable that emits the result of\n * applying the projection function (and the optional deprecated\n * `resultSelector`) to each item emitted by the source Observable and merging\n * the results of the Observables obtained from this transformation.\n */\nexport function mergeMap<T, R, O extends ObservableInput<any>>(\n  project: (value: T, index: number) => O,\n  resultSelector?: ((outerValue: T, innerValue: ObservedValueOf<O>, outerIndex: number, innerIndex: number) => R) | number,\n  concurrent: number = Infinity\n): OperatorFunction<T, ObservedValueOf<O> | R> {\n  if (isFunction(resultSelector)) {\n    // DEPRECATED PATH\n    return mergeMap((a, i) => map((b: any, ii: number) => resultSelector(a, b, i, ii))(innerFrom(project(a, i))), concurrent);\n  } else if (typeof resultSelector === 'number') {\n    concurrent = resultSelector;\n  }\n\n  return operate((source, subscriber) => mergeInternals(source, subscriber, project, concurrent));\n}\n"], "mappings": "AACA,SAASA,GAAG,QAAQ,OAAO;AAC3B,SAASC,SAAS,QAAQ,yBAAyB;AACnD,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,UAAU,QAAQ,oBAAoB;AA2E/C,OAAM,SAAUC,QAAQA,CACtBC,OAAuC,EACvCC,cAAwH,EACxHC,UAA6B;EAA7B,IAAAA,UAAA;IAAAA,UAAA,GAAAC,QAA6B;EAAA;EAE7B,IAAIL,UAAU,CAACG,cAAc,CAAC,EAAE;IAE9B,OAAOF,QAAQ,CAAC,UAACK,CAAC,EAAEC,CAAC;MAAK,OAAAX,GAAG,CAAC,UAACY,CAAM,EAAEC,EAAU;QAAK,OAAAN,cAAc,CAACG,CAAC,EAAEE,CAAC,EAAED,CAAC,EAAEE,EAAE,CAAC;MAA3B,CAA2B,CAAC,CAACZ,SAAS,CAACK,OAAO,CAACI,CAAC,EAAEC,CAAC,CAAC,CAAC,CAAC;IAAlF,CAAkF,EAAEH,UAAU,CAAC;GAC1H,MAAM,IAAI,OAAOD,cAAc,KAAK,QAAQ,EAAE;IAC7CC,UAAU,GAAGD,cAAc;;EAG7B,OAAOL,OAAO,CAAC,UAACY,MAAM,EAAEC,UAAU;IAAK,OAAAZ,cAAc,CAACW,MAAM,EAAEC,UAAU,EAAET,OAAO,EAAEE,UAAU,CAAC;EAAvD,CAAuD,CAAC;AACjG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}