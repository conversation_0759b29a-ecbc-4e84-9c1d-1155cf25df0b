{"ast": null, "code": "import { createOperatorSubscriber } from './OperatorSubscriber';\nexport function scanInternals(accumulator, seed, hasSeed, emitOnNext, emitBeforeComplete) {\n  return function (source, subscriber) {\n    var hasState = hasSeed;\n    var state = seed;\n    var index = 0;\n    source.subscribe(createOperatorSubscriber(subscriber, function (value) {\n      var i = index++;\n      state = hasState ? accumulator(state, value, i) : (hasState = true, value);\n      emitOnNext && subscriber.next(state);\n    }, emitBeforeComplete && function () {\n      hasState && subscriber.next(state);\n      subscriber.complete();\n    }));\n  };\n}", "map": {"version": 3, "names": ["createOperatorSubscriber", "scanInternals", "accumulator", "seed", "hasSeed", "emitOnNext", "emitBeforeComplete", "source", "subscriber", "hasState", "state", "index", "subscribe", "value", "i", "next", "complete"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\scanInternals.ts"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { Subscriber } from '../Subscriber';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\n\n/**\n * A basic scan operation. This is used for `scan` and `reduce`.\n * @param accumulator The accumulator to use\n * @param seed The seed value for the state to accumulate\n * @param hasSeed Whether or not a seed was provided\n * @param emitOnNext Whether or not to emit the state on next\n * @param emitBeforeComplete Whether or not to emit the before completion\n */\n\nexport function scanInternals<V, A, S>(\n  accumulator: (acc: V | A | S, value: V, index: number) => A,\n  seed: S,\n  hasSeed: boolean,\n  emitOnNext: boolean,\n  emitBeforeComplete?: undefined | true\n) {\n  return (source: Observable<V>, subscriber: Subscriber<any>) => {\n    // Whether or not we have state yet. This will only be\n    // false before the first value arrives if we didn't get\n    // a seed value.\n    let hasState = hasSeed;\n    // The state that we're tracking, starting with the seed,\n    // if there is one, and then updated by the return value\n    // from the accumulator on each emission.\n    let state: any = seed;\n    // An index to pass to the accumulator function.\n    let index = 0;\n\n    // Subscribe to our source. All errors and completions are passed through.\n    source.subscribe(\n      createOperatorSubscriber(\n        subscriber,\n        (value) => {\n          // Always increment the index.\n          const i = index++;\n          // Set the state\n          state = hasState\n            ? // We already have state, so we can get the new state from the accumulator\n              accumulator(state, value, i)\n            : // We didn't have state yet, a seed value was not provided, so\n\n              // we set the state to the first value, and mark that we have state now\n              ((hasState = true), value);\n\n          // Maybe send it to the consumer.\n          emitOnNext && subscriber.next(state);\n        },\n        // If an onComplete was given, call it, otherwise\n        // just pass through the complete notification to the consumer.\n        emitBeforeComplete &&\n          (() => {\n            hasState && subscriber.next(state);\n            subscriber.complete();\n          })\n      )\n    );\n  };\n}\n"], "mappings": "AAEA,SAASA,wBAAwB,QAAQ,sBAAsB;AAW/D,OAAM,SAAUC,aAAaA,CAC3BC,WAA2D,EAC3DC,IAAO,EACPC,OAAgB,EAChBC,UAAmB,EACnBC,kBAAqC;EAErC,OAAO,UAACC,MAAqB,EAAEC,UAA2B;IAIxD,IAAIC,QAAQ,GAAGL,OAAO;IAItB,IAAIM,KAAK,GAAQP,IAAI;IAErB,IAAIQ,KAAK,GAAG,CAAC;IAGbJ,MAAM,CAACK,SAAS,CACdZ,wBAAwB,CACtBQ,UAAU,EACV,UAACK,KAAK;MAEJ,IAAMC,CAAC,GAAGH,KAAK,EAAE;MAEjBD,KAAK,GAAGD,QAAQ,GAEZP,WAAW,CAACQ,KAAK,EAAEG,KAAK,EAAEC,CAAC,CAAC,IAI1BL,QAAQ,GAAG,IAAI,EAAGI,KAAK,CAAC;MAG9BR,UAAU,IAAIG,UAAU,CAACO,IAAI,CAACL,KAAK,CAAC;IACtC,CAAC,EAGDJ,kBAAkB,IACf;MACCG,QAAQ,IAAID,UAAU,CAACO,IAAI,CAACL,KAAK,CAAC;MAClCF,UAAU,CAACQ,QAAQ,EAAE;IACvB,CAAE,CACL,CACF;EACH,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}