{"ast": null, "code": "import { __generator } from \"tslib\";\nimport { identity } from '../util/identity';\nimport { isScheduler } from '../util/isScheduler';\nimport { defer } from './defer';\nimport { scheduleIterable } from '../scheduled/scheduleIterable';\nexport function generate(initialStateOrOptions, condition, iterate, resultSelectorOrScheduler, scheduler) {\n  var _a, _b;\n  var resultSelector;\n  var initialState;\n  if (arguments.length === 1) {\n    _a = initialStateOrOptions, initialState = _a.initialState, condition = _a.condition, iterate = _a.iterate, _b = _a.resultSelector, resultSelector = _b === void 0 ? identity : _b, scheduler = _a.scheduler;\n  } else {\n    initialState = initialStateOrOptions;\n    if (!resultSelectorOrScheduler || isScheduler(resultSelectorOrScheduler)) {\n      resultSelector = identity;\n      scheduler = resultSelectorOrScheduler;\n    } else {\n      resultSelector = resultSelectorOrScheduler;\n    }\n  }\n  function gen() {\n    var state;\n    return __generator(this, function (_a) {\n      switch (_a.label) {\n        case 0:\n          state = initialState;\n          _a.label = 1;\n        case 1:\n          if (!(!condition || condition(state))) return [3, 4];\n          return [4, resultSelector(state)];\n        case 2:\n          _a.sent();\n          _a.label = 3;\n        case 3:\n          state = iterate(state);\n          return [3, 1];\n        case 4:\n          return [2];\n      }\n    });\n  }\n  return defer(scheduler ? function () {\n    return scheduleIterable(gen(), scheduler);\n  } : gen);\n}", "map": {"version": 3, "names": ["identity", "isScheduler", "defer", "scheduleIterable", "generate", "initialStateOrOptions", "condition", "iterate", "resultSelectorOrScheduler", "scheduler", "resultSelector", "initialState", "arguments", "length", "_a", "_b", "gen", "state", "sent"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\observable\\generate.ts"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { identity } from '../util/identity';\nimport { ObservableInput, SchedulerLike } from '../types';\nimport { isScheduler } from '../util/isScheduler';\nimport { defer } from './defer';\nimport { scheduleIterable } from '../scheduled/scheduleIterable';\n\ntype ConditionFunc<S> = (state: S) => boolean;\ntype IterateFunc<S> = (state: S) => S;\ntype ResultFunc<S, T> = (state: S) => T;\n\nexport interface GenerateBaseOptions<S> {\n  /**\n   * Initial state.\n   */\n  initialState: S;\n  /**\n   * Condition function that accepts state and returns boolean.\n   * When it returns false, the generator stops.\n   * If not specified, a generator never stops.\n   */\n  condition?: ConditionFunc<S>;\n  /**\n   * Iterate function that accepts state and returns new state.\n   */\n  iterate: IterateFunc<S>;\n  /**\n   * SchedulerLike to use for generation process.\n   * By default, a generator starts immediately.\n   */\n  scheduler?: SchedulerLike;\n}\n\nexport interface GenerateOptions<T, S> extends GenerateBaseOptions<S> {\n  /**\n   * Result selection function that accepts state and returns a value to emit.\n   */\n  resultSelector: ResultFunc<S, T>;\n}\n\n/**\n * Generates an observable sequence by running a state-driven loop\n * producing the sequence's elements, using the specified scheduler\n * to send out observer messages.\n *\n * ![](generate.png)\n *\n * ## Examples\n *\n * Produces sequence of numbers\n *\n * ```ts\n * import { generate } from 'rxjs';\n *\n * const result = generate(0, x => x < 3, x => x + 1, x => x);\n *\n * result.subscribe(x => console.log(x));\n *\n * // Logs:\n * // 0\n * // 1\n * // 2\n * ```\n *\n * Use `asapScheduler`\n *\n * ```ts\n * import { generate, asapScheduler } from 'rxjs';\n *\n * const result = generate(1, x => x < 5, x => x * 2, x => x + 1, asapScheduler);\n *\n * result.subscribe(x => console.log(x));\n *\n * // Logs:\n * // 2\n * // 3\n * // 5\n * ```\n *\n * @see {@link from}\n * @see {@link Observable}\n *\n * @param initialState Initial state.\n * @param condition Condition to terminate generation (upon returning false).\n * @param iterate Iteration step function.\n * @param resultSelector Selector function for results produced in the sequence.\n * @param scheduler A {@link SchedulerLike} on which to run the generator loop.\n * If not provided, defaults to emit immediately.\n * @returns The generated sequence.\n * @deprecated Instead of passing separate arguments, use the options argument.\n * Signatures taking separate arguments will be removed in v8.\n */\nexport function generate<T, S>(\n  initialState: S,\n  condition: ConditionFunc<S>,\n  iterate: IterateFunc<S>,\n  resultSelector: ResultFunc<S, T>,\n  scheduler?: SchedulerLike\n): Observable<T>;\n\n/**\n * Generates an Observable by running a state-driven loop\n * that emits an element on each iteration.\n *\n * <span class=\"informal\">Use it instead of nexting values in a for loop.</span>\n *\n * ![](generate.png)\n *\n * `generate` allows you to create a stream of values generated with a loop very similar to\n * a traditional for loop. The first argument of `generate` is a beginning value. The second argument\n * is a function that accepts this value and tests if some condition still holds. If it does,\n * then the loop continues, if not, it stops. The third value is a function which takes the\n * previously defined value and modifies it in some way on each iteration. Note how these three parameters\n * are direct equivalents of three expressions in a traditional for loop: the first expression\n * initializes some state (for example, a numeric index), the second tests if the loop can perform the next\n * iteration (for example, if the index is lower than 10) and the third states how the defined value\n * will be modified on every step (for example, the index will be incremented by one).\n *\n * Return value of a `generate` operator is an Observable that on each loop iteration\n * emits a value. First of all, the condition function is ran. If it returns true, then the Observable\n * emits the currently stored value (initial value at the first iteration) and finally updates\n * that value with iterate function. If at some point the condition returns false, then the Observable\n * completes at that moment.\n *\n * Optionally you can pass a fourth parameter to `generate` - a result selector function which allows you\n * to immediately map the value that would normally be emitted by an Observable.\n *\n * If you find three anonymous functions in `generate` call hard to read, you can provide\n * a single object to the operator instead where the object has the properties: `initialState`,\n * `condition`, `iterate` and `resultSelector`, which should have respective values that you\n * would normally pass to `generate`. `resultSelector` is still optional, but that form\n * of calling `generate` allows you to omit `condition` as well. If you omit it, that means\n * condition always holds, or in other words the resulting Observable will never complete.\n *\n * Both forms of `generate` can optionally accept a scheduler. In case of a multi-parameter call,\n * scheduler simply comes as a last argument (no matter if there is a `resultSelector`\n * function or not). In case of a single-parameter call, you can provide it as a\n * `scheduler` property on the object passed to the operator. In both cases, a scheduler decides when\n * the next iteration of the loop will happen and therefore when the next value will be emitted\n * by the Observable. For example, to ensure that each value is pushed to the Observer\n * on a separate task in the event loop, you could use the `async` scheduler. Note that\n * by default (when no scheduler is passed) values are simply emitted synchronously.\n *\n *\n * ## Examples\n *\n * Use with condition and iterate functions\n *\n * ```ts\n * import { generate } from 'rxjs';\n *\n * const result = generate(0, x => x < 3, x => x + 1);\n *\n * result.subscribe({\n *   next: value => console.log(value),\n *   complete: () => console.log('Complete!')\n * });\n *\n * // Logs:\n * // 0\n * // 1\n * // 2\n * // 'Complete!'\n * ```\n *\n * Use with condition, iterate and resultSelector functions\n *\n * ```ts\n * import { generate } from 'rxjs';\n *\n * const result = generate(0, x => x < 3, x => x + 1, x => x * 1000);\n *\n * result.subscribe({\n *   next: value => console.log(value),\n *   complete: () => console.log('Complete!')\n * });\n *\n * // Logs:\n * // 0\n * // 1000\n * // 2000\n * // 'Complete!'\n * ```\n *\n * Use with options object\n *\n * ```ts\n * import { generate } from 'rxjs';\n *\n * const result = generate({\n *   initialState: 0,\n *   condition(value) { return value < 3; },\n *   iterate(value) { return value + 1; },\n *   resultSelector(value) { return value * 1000; }\n * });\n *\n * result.subscribe({\n *   next: value => console.log(value),\n *   complete: () => console.log('Complete!')\n * });\n *\n * // Logs:\n * // 0\n * // 1000\n * // 2000\n * // 'Complete!'\n * ```\n *\n * Use options object without condition function\n *\n * ```ts\n * import { generate } from 'rxjs';\n *\n * const result = generate({\n *   initialState: 0,\n *   iterate(value) { return value + 1; },\n *   resultSelector(value) { return value * 1000; }\n * });\n *\n * result.subscribe({\n *   next: value => console.log(value),\n *   complete: () => console.log('Complete!') // This will never run\n * });\n *\n * // Logs:\n * // 0\n * // 1000\n * // 2000\n * // 3000\n * // ...and never stops.\n * ```\n *\n * @see {@link from}\n *\n * @param initialState Initial state.\n * @param condition Condition to terminate generation (upon returning false).\n * @param iterate Iteration step function.\n * @param scheduler A {@link Scheduler} on which to run the generator loop. If not\n * provided, defaults to emitting immediately.\n * @return The generated sequence.\n * @deprecated Instead of passing separate arguments, use the options argument.\n * Signatures taking separate arguments will be removed in v8.\n */\nexport function generate<S>(\n  initialState: S,\n  condition: ConditionFunc<S>,\n  iterate: IterateFunc<S>,\n  scheduler?: SchedulerLike\n): Observable<S>;\n\n/**\n * Generates an observable sequence by running a state-driven loop\n * producing the sequence's elements, using the specified scheduler\n * to send out observer messages.\n * The overload accepts options object that might contain initial state, iterate,\n * condition and scheduler.\n *\n * ![](generate.png)\n *\n * ## Examples\n *\n * Use options object with condition function\n *\n * ```ts\n * import { generate } from 'rxjs';\n *\n * const result = generate({\n *   initialState: 0,\n *   condition: x => x < 3,\n *   iterate: x => x + 1\n * });\n *\n * result.subscribe({\n *   next: value => console.log(value),\n *   complete: () => console.log('Complete!')\n * });\n *\n * // Logs:\n * // 0\n * // 1\n * // 2\n * // 'Complete!'\n * ```\n *\n * @see {@link from}\n * @see {@link Observable}\n *\n * @param options Object that must contain initialState, iterate and might contain condition and scheduler.\n * @returns The generated sequence.\n */\nexport function generate<S>(options: GenerateBaseOptions<S>): Observable<S>;\n\n/**\n * Generates an observable sequence by running a state-driven loop\n * producing the sequence's elements, using the specified scheduler\n * to send out observer messages.\n * The overload accepts options object that might contain initial state, iterate,\n * condition, result selector and scheduler.\n *\n * ![](generate.png)\n *\n * ## Examples\n *\n * Use options object with condition and iterate function\n *\n * ```ts\n * import { generate } from 'rxjs';\n *\n * const result = generate({\n *   initialState: 0,\n *   condition: x => x < 3,\n *   iterate: x => x + 1,\n *   resultSelector: x => x\n * });\n *\n * result.subscribe({\n *   next: value => console.log(value),\n *   complete: () => console.log('Complete!')\n * });\n *\n * // Logs:\n * // 0\n * // 1\n * // 2\n * // 'Complete!'\n * ```\n *\n * @see {@link from}\n * @see {@link Observable}\n *\n * @param options Object that must contain initialState, iterate, resultSelector and might contain condition and scheduler.\n * @returns The generated sequence.\n */\nexport function generate<T, S>(options: GenerateOptions<T, S>): Observable<T>;\n\nexport function generate<T, S>(\n  initialStateOrOptions: S | GenerateOptions<T, S>,\n  condition?: ConditionFunc<S>,\n  iterate?: IterateFunc<S>,\n  resultSelectorOrScheduler?: ResultFunc<S, T> | SchedulerLike,\n  scheduler?: SchedulerLike\n): Observable<T> {\n  let resultSelector: ResultFunc<S, T>;\n  let initialState: S;\n\n  // TODO: Remove this as we move away from deprecated signatures\n  // and move towards a configuration object argument.\n  if (arguments.length === 1) {\n    // If we only have one argument, we can assume it is a configuration object.\n    // Note that folks not using TypeScript may trip over this.\n    ({\n      initialState,\n      condition,\n      iterate,\n      resultSelector = identity as ResultFunc<S, T>,\n      scheduler,\n    } = initialStateOrOptions as GenerateOptions<T, S>);\n  } else {\n    // Deprecated arguments path. Figure out what the user\n    // passed and set it here.\n    initialState = initialStateOrOptions as S;\n    if (!resultSelectorOrScheduler || isScheduler(resultSelectorOrScheduler)) {\n      resultSelector = identity as ResultFunc<S, T>;\n      scheduler = resultSelectorOrScheduler as SchedulerLike;\n    } else {\n      resultSelector = resultSelectorOrScheduler as ResultFunc<S, T>;\n    }\n  }\n\n  // The actual generator used to \"generate\" values.\n  function* gen() {\n    for (let state = initialState; !condition || condition(state); state = iterate!(state)) {\n      yield resultSelector(state);\n    }\n  }\n\n  // We use `defer` because we want to defer the creation of the iterator from the iterable.\n  return defer(\n    (scheduler\n      ? // If a scheduler was provided, use `scheduleIterable` to ensure that iteration/generation\n        // happens on the scheduler.\n        () => scheduleIterable(gen(), scheduler!)\n      : // Otherwise, if there's no scheduler, we can just use the generator function directly in\n        // `defer` and executing it will return the generator (which is iterable).\n        gen) as () => ObservableInput<T>\n  );\n}\n"], "mappings": ";AACA,SAASA,QAAQ,QAAQ,kBAAkB;AAE3C,SAASC,WAAW,QAAQ,qBAAqB;AACjD,SAASC,KAAK,QAAQ,SAAS;AAC/B,SAASC,gBAAgB,QAAQ,+BAA+B;AA0UhE,OAAM,SAAUC,QAAQA,CACtBC,qBAAgD,EAChDC,SAA4B,EAC5BC,OAAwB,EACxBC,yBAA4D,EAC5DC,SAAyB;;EAEzB,IAAIC,cAAgC;EACpC,IAAIC,YAAe;EAInB,IAAIC,SAAS,CAACC,MAAM,KAAK,CAAC,EAAE;IAGzBC,EAAA,GAMGT,qBAA8C,EALhDM,YAAY,GAAAG,EAAA,CAAAH,YAAA,EACZL,SAAS,GAAAQ,EAAA,CAAAR,SAAA,EACTC,OAAO,GAAAO,EAAA,CAAAP,OAAA,EACPQ,EAAA,GAAAD,EAAA,CAAAJ,cAA6C,EAA7CA,cAAc,GAAAK,EAAA,cAAGf,QAA4B,GAAAe,EAAA,EAC7CN,SAAS,GAAAK,EAAA,CAAAL,SAAA;GAEZ,MAAM;IAGLE,YAAY,GAAGN,qBAA0B;IACzC,IAAI,CAACG,yBAAyB,IAAIP,WAAW,CAACO,yBAAyB,CAAC,EAAE;MACxEE,cAAc,GAAGV,QAA4B;MAC7CS,SAAS,GAAGD,yBAA0C;KACvD,MAAM;MACLE,cAAc,GAAGF,yBAA6C;;;EAKlE,SAAUQ,GAAGA,CAAA;;;;;UACFC,KAAK,GAAGN,YAAY;;;gBAAE,CAACL,SAAS,IAAIA,SAAS,CAACW,KAAK,CAAC;UAC3D,WAAMP,cAAc,CAACO,KAAK,CAAC;;UAA3BH,EAAA,CAAAI,IAAA,EAA2B;;;UADkCD,KAAK,GAAGV,OAAQ,CAACU,KAAK,CAAC;;;;;;;EAMxF,OAAOf,KAAK,CACTO,SAAS,GAGN;IAAM,OAAAN,gBAAgB,CAACa,GAAG,EAAE,EAAEP,SAAU,CAAC;EAAnC,CAAmC,GAGzCO,GAAgC,CACrC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}