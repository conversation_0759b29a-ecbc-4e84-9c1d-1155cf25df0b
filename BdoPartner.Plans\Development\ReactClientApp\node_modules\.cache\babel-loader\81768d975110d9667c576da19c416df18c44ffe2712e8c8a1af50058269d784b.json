{"ast": null, "code": "import { operate } from '../util/lift';\nimport { mergeInternals } from './mergeInternals';\nexport function mergeScan(accumulator, seed, concurrent) {\n  if (concurrent === void 0) {\n    concurrent = Infinity;\n  }\n  return operate(function (source, subscriber) {\n    var state = seed;\n    return mergeInternals(source, subscriber, function (value, index) {\n      return accumulator(state, value, index);\n    }, concurrent, function (value) {\n      state = value;\n    }, false, undefined, function () {\n      return state = null;\n    });\n  });\n}", "map": {"version": 3, "names": ["operate", "mergeInternals", "mergeScan", "accumulator", "seed", "concurrent", "Infinity", "source", "subscriber", "state", "value", "index", "undefined"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\mergeScan.ts"], "sourcesContent": ["import { ObservableInput, OperatorFunction } from '../types';\nimport { operate } from '../util/lift';\nimport { mergeInternals } from './mergeInternals';\n\n/**\n * Applies an accumulator function over the source Observable where the\n * accumulator function itself returns an Observable, then each intermediate\n * Observable returned is merged into the output Observable.\n *\n * <span class=\"informal\">It's like {@link scan}, but the Observables returned\n * by the accumulator are merged into the outer Observable.</span>\n *\n * The first parameter of the `mergeScan` is an `accumulator` function which is\n * being called every time the source Observable emits a value. `mergeScan` will\n * subscribe to the value returned by the `accumulator` function and will emit\n * values to the subscriber emitted by inner Observable.\n *\n * The `accumulator` function is being called with three parameters passed to it:\n * `acc`, `value` and `index`. The `acc` parameter is used as the state parameter\n * whose value is initially set to the `seed` parameter (the second parameter\n * passed to the `mergeScan` operator).\n *\n * `mergeScan` internally keeps the value of the `acc` parameter: as long as the\n * source Observable emits without inner Observable emitting, the `acc` will be\n * set to `seed`. The next time the inner Observable emits a value, `mergeScan`\n * will internally remember it and it will be passed to the `accumulator`\n * function as `acc` parameter the next time source emits.\n *\n * The `value` parameter of the `accumulator` function is the value emitted by the\n * source Observable, while the `index` is a number which represent the order of the\n * current emission by the source Observable. It starts with 0.\n *\n * The last parameter to the `mergeScan` is the `concurrent` value which defaults\n * to Infinity. It represents the maximum number of inner Observable subscriptions\n * at a time.\n *\n * ## Example\n *\n * Count the number of click events\n *\n * ```ts\n * import { fromEvent, map, mergeScan, of } from 'rxjs';\n *\n * const click$ = fromEvent(document, 'click');\n * const one$ = click$.pipe(map(() => 1));\n * const seed = 0;\n * const count$ = one$.pipe(\n *   mergeScan((acc, one) => of(acc + one), seed)\n * );\n *\n * count$.subscribe(x => console.log(x));\n *\n * // Results:\n * // 1\n * // 2\n * // 3\n * // 4\n * // ...and so on for each click\n * ```\n *\n * @see {@link scan}\n * @see {@link switchScan}\n *\n * @param accumulator The accumulator function called on each source value.\n * @param seed The initial accumulation value.\n * @param concurrent Maximum number of input Observables being subscribed to\n * concurrently.\n * @return A function that returns an Observable of the accumulated values.\n */\nexport function mergeScan<T, R>(\n  accumulator: (acc: R, value: T, index: number) => ObservableInput<R>,\n  seed: R,\n  concurrent = Infinity\n): OperatorFunction<T, R> {\n  return operate((source, subscriber) => {\n    // The accumulated state.\n    let state = seed;\n\n    return mergeInternals(\n      source,\n      subscriber,\n      (value, index) => accumulator(state, value, index),\n      concurrent,\n      (value) => {\n        state = value;\n      },\n      false,\n      undefined,\n      () => (state = null!)\n    );\n  });\n}\n"], "mappings": "AACA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,cAAc,QAAQ,kBAAkB;AAmEjD,OAAM,SAAUC,SAASA,CACvBC,WAAoE,EACpEC,IAAO,EACPC,UAAqB;EAArB,IAAAA,UAAA;IAAAA,UAAA,GAAAC,QAAqB;EAAA;EAErB,OAAON,OAAO,CAAC,UAACO,MAAM,EAAEC,UAAU;IAEhC,IAAIC,KAAK,GAAGL,IAAI;IAEhB,OAAOH,cAAc,CACnBM,MAAM,EACNC,UAAU,EACV,UAACE,KAAK,EAAEC,KAAK;MAAK,OAAAR,WAAW,CAACM,KAAK,EAAEC,KAAK,EAAEC,KAAK,CAAC;IAAhC,CAAgC,EAClDN,UAAU,EACV,UAACK,KAAK;MACJD,KAAK,GAAGC,KAAK;IACf,CAAC,EACD,KAAK,EACLE,SAAS,EACT;MAAM,OAACH,KAAK,GAAG,IAAK;IAAd,CAAe,CACtB;EACH,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}