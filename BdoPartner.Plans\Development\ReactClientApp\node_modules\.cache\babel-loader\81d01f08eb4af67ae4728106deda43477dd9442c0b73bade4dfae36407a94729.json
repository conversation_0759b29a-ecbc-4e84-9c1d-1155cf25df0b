{"ast": null, "code": "import { __read, __spreadArray } from \"tslib\";\nimport { Observable } from '../Observable';\nimport { innerFrom } from './innerFrom';\nimport { argsOrArgArray } from '../util/argsOrArgArray';\nimport { EMPTY } from './empty';\nimport { createOperatorSubscriber } from '../operators/OperatorSubscriber';\nimport { popResultSelector } from '../util/args';\nexport function zip() {\n  var args = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    args[_i] = arguments[_i];\n  }\n  var resultSelector = popResultSelector(args);\n  var sources = argsOrArgArray(args);\n  return sources.length ? new Observable(function (subscriber) {\n    var buffers = sources.map(function () {\n      return [];\n    });\n    var completed = sources.map(function () {\n      return false;\n    });\n    subscriber.add(function () {\n      buffers = completed = null;\n    });\n    var _loop_1 = function (sourceIndex) {\n      innerFrom(sources[sourceIndex]).subscribe(createOperatorSubscriber(subscriber, function (value) {\n        buffers[sourceIndex].push(value);\n        if (buffers.every(function (buffer) {\n          return buffer.length;\n        })) {\n          var result = buffers.map(function (buffer) {\n            return buffer.shift();\n          });\n          subscriber.next(resultSelector ? resultSelector.apply(void 0, __spreadArray([], __read(result))) : result);\n          if (buffers.some(function (buffer, i) {\n            return !buffer.length && completed[i];\n          })) {\n            subscriber.complete();\n          }\n        }\n      }, function () {\n        completed[sourceIndex] = true;\n        !buffers[sourceIndex].length && subscriber.complete();\n      }));\n    };\n    for (var sourceIndex = 0; !subscriber.closed && sourceIndex < sources.length; sourceIndex++) {\n      _loop_1(sourceIndex);\n    }\n    return function () {\n      buffers = completed = null;\n    };\n  }) : EMPTY;\n}", "map": {"version": 3, "names": ["Observable", "innerFrom", "argsOrArgArray", "EMPTY", "createOperatorSubscriber", "popResultSelector", "zip", "args", "_i", "arguments", "length", "resultSelector", "sources", "subscriber", "buffers", "map", "completed", "add", "sourceIndex", "subscribe", "value", "push", "every", "buffer", "result", "shift", "next", "apply", "__spread<PERSON><PERSON>y", "__read", "some", "i", "complete", "closed"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\observable\\zip.ts"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { ObservableInputTuple } from '../types';\nimport { innerFrom } from './innerFrom';\nimport { argsOrArgArray } from '../util/argsOrArgArray';\nimport { EMPTY } from './empty';\nimport { createOperatorSubscriber } from '../operators/OperatorSubscriber';\nimport { popResultSelector } from '../util/args';\n\nexport function zip<A extends readonly unknown[]>(sources: [...ObservableInputTuple<A>]): Observable<A>;\nexport function zip<A extends readonly unknown[], R>(\n  sources: [...ObservableInputTuple<A>],\n  resultSelector: (...values: A) => R\n): Observable<R>;\nexport function zip<A extends readonly unknown[]>(...sources: [...ObservableInputTuple<A>]): Observable<A>;\nexport function zip<A extends readonly unknown[], R>(\n  ...sourcesAndResultSelector: [...ObservableInputTuple<A>, (...values: A) => R]\n): Observable<R>;\n\n/**\n * Combines multiple Observables to create an Observable whose values are calculated from the values, in order, of each\n * of its input Observables.\n *\n * If the last parameter is a function, this function is used to compute the created value from the input values.\n * Otherwise, an array of the input values is returned.\n *\n * ## Example\n *\n * Combine age and name from different sources\n *\n * ```ts\n * import { of, zip, map } from 'rxjs';\n *\n * const age$ = of(27, 25, 29);\n * const name$ = of('Foo', 'Bar', 'Beer');\n * const isDev$ = of(true, true, false);\n *\n * zip(age$, name$, isDev$).pipe(\n *   map(([age, name, isDev]) => ({ age, name, isDev }))\n * )\n * .subscribe(x => console.log(x));\n *\n * // Outputs\n * // { age: 27, name: 'Foo', isDev: true }\n * // { age: 25, name: 'Bar', isDev: true }\n * // { age: 29, name: 'Beer', isDev: false }\n * ```\n *\n * @param args Any number of `ObservableInput`s provided either as an array or as an object\n * to combine with each other.\n * @return An Observable of array values of the values emitted at the same index from each\n * individual `ObservableInput`.\n */\nexport function zip(...args: unknown[]): Observable<unknown> {\n  const resultSelector = popResultSelector(args);\n\n  const sources = argsOrArgArray(args) as Observable<unknown>[];\n\n  return sources.length\n    ? new Observable<unknown[]>((subscriber) => {\n        // A collection of buffers of values from each source.\n        // Keyed by the same index with which the sources were passed in.\n        let buffers: unknown[][] = sources.map(() => []);\n\n        // An array of flags of whether or not the sources have completed.\n        // This is used to check to see if we should complete the result.\n        // Keyed by the same index with which the sources were passed in.\n        let completed = sources.map(() => false);\n\n        // When everything is done, release the arrays above.\n        subscriber.add(() => {\n          buffers = completed = null!;\n        });\n\n        // Loop over our sources and subscribe to each one. The index `i` is\n        // especially important here, because we use it in closures below to\n        // access the related buffers and completion properties\n        for (let sourceIndex = 0; !subscriber.closed && sourceIndex < sources.length; sourceIndex++) {\n          innerFrom(sources[sourceIndex]).subscribe(\n            createOperatorSubscriber(\n              subscriber,\n              (value) => {\n                buffers[sourceIndex].push(value);\n                // if every buffer has at least one value in it, then we\n                // can shift out the oldest value from each buffer and emit\n                // them as an array.\n                if (buffers.every((buffer) => buffer.length)) {\n                  const result: any = buffers.map((buffer) => buffer.shift()!);\n                  // Emit the array. If theres' a result selector, use that.\n                  subscriber.next(resultSelector ? resultSelector(...result) : result);\n                  // If any one of the sources is both complete and has an empty buffer\n                  // then we complete the result. This is because we cannot possibly have\n                  // any more values to zip together.\n                  if (buffers.some((buffer, i) => !buffer.length && completed[i])) {\n                    subscriber.complete();\n                  }\n                }\n              },\n              () => {\n                // This source completed. Mark it as complete so we can check it later\n                // if we have to.\n                completed[sourceIndex] = true;\n                // But, if this complete source has nothing in its buffer, then we\n                // can complete the result, because we can't possibly have any more\n                // values from this to zip together with the other values.\n                !buffers[sourceIndex].length && subscriber.complete();\n              }\n            )\n          );\n        }\n\n        // When everything is done, release the arrays above.\n        return () => {\n          buffers = completed = null!;\n        };\n      })\n    : EMPTY;\n}\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,eAAe;AAE1C,SAASC,SAAS,QAAQ,aAAa;AACvC,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,KAAK,QAAQ,SAAS;AAC/B,SAASC,wBAAwB,QAAQ,iCAAiC;AAC1E,SAASC,iBAAiB,QAAQ,cAAc;AA8ChD,OAAM,SAAUC,GAAGA,CAAA;EAAC,IAAAC,IAAA;OAAA,IAAAC,EAAA,IAAkB,EAAlBA,EAAA,GAAAC,SAAA,CAAAC,MAAkB,EAAlBF,EAAA,EAAkB;IAAlBD,IAAA,CAAAC,EAAA,IAAAC,SAAA,CAAAD,EAAA;;EAClB,IAAMG,cAAc,GAAGN,iBAAiB,CAACE,IAAI,CAAC;EAE9C,IAAMK,OAAO,GAAGV,cAAc,CAACK,IAAI,CAA0B;EAE7D,OAAOK,OAAO,CAACF,MAAM,GACjB,IAAIV,UAAU,CAAY,UAACa,UAAU;IAGnC,IAAIC,OAAO,GAAgBF,OAAO,CAACG,GAAG,CAAC;MAAM,SAAE;IAAF,CAAE,CAAC;IAKhD,IAAIC,SAAS,GAAGJ,OAAO,CAACG,GAAG,CAAC;MAAM,YAAK;IAAL,CAAK,CAAC;IAGxCF,UAAU,CAACI,GAAG,CAAC;MACbH,OAAO,GAAGE,SAAS,GAAG,IAAK;IAC7B,CAAC,CAAC;4BAKOE,WAAW;MAClBjB,SAAS,CAACW,OAAO,CAACM,WAAW,CAAC,CAAC,CAACC,SAAS,CACvCf,wBAAwB,CACtBS,UAAU,EACV,UAACO,KAAK;QACJN,OAAO,CAACI,WAAW,CAAC,CAACG,IAAI,CAACD,KAAK,CAAC;QAIhC,IAAIN,OAAO,CAACQ,KAAK,CAAC,UAACC,MAAM;UAAK,OAAAA,MAAM,CAACb,MAAM;QAAb,CAAa,CAAC,EAAE;UAC5C,IAAMc,MAAM,GAAQV,OAAO,CAACC,GAAG,CAAC,UAACQ,MAAM;YAAK,OAAAA,MAAM,CAACE,KAAK,EAAG;UAAf,CAAe,CAAC;UAE5DZ,UAAU,CAACa,IAAI,CAACf,cAAc,GAAGA,cAAc,CAAAgB,KAAA,SAAAC,aAAA,KAAAC,MAAA,CAAIL,MAAM,MAAIA,MAAM,CAAC;UAIpE,IAAIV,OAAO,CAACgB,IAAI,CAAC,UAACP,MAAM,EAAEQ,CAAC;YAAK,QAACR,MAAM,CAACb,MAAM,IAAIM,SAAS,CAACe,CAAC,CAAC;UAA9B,CAA8B,CAAC,EAAE;YAC/DlB,UAAU,CAACmB,QAAQ,EAAE;;;MAG3B,CAAC,EACD;QAGEhB,SAAS,CAACE,WAAW,CAAC,GAAG,IAAI;QAI7B,CAACJ,OAAO,CAACI,WAAW,CAAC,CAACR,MAAM,IAAIG,UAAU,CAACmB,QAAQ,EAAE;MACvD,CAAC,CACF,CACF;;IA/BH,KAAK,IAAId,WAAW,GAAG,CAAC,EAAE,CAACL,UAAU,CAACoB,MAAM,IAAIf,WAAW,GAAGN,OAAO,CAACF,MAAM,EAAEQ,WAAW,EAAE;cAAlFA,WAAW;;IAmCpB,OAAO;MACLJ,OAAO,GAAGE,SAAS,GAAG,IAAK;IAC7B,CAAC;EACH,CAAC,CAAC,GACFb,KAAK;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}