{"ast": null, "code": "import { ConnectableObservable } from '../observable/ConnectableObservable';\nimport { isFunction } from '../util/isFunction';\nimport { connect } from './connect';\nexport function multicast(subjectOrSubjectFactory, selector) {\n  var subjectFactory = isFunction(subjectOrSubjectFactory) ? subjectOrSubjectFactory : function () {\n    return subjectOrSubjectFactory;\n  };\n  if (isFunction(selector)) {\n    return connect(selector, {\n      connector: subjectFactory\n    });\n  }\n  return function (source) {\n    return new ConnectableObservable(source, subjectFactory);\n  };\n}", "map": {"version": 3, "names": ["ConnectableObservable", "isFunction", "connect", "multicast", "subjectOrSubjectFactory", "selector", "subjectFactory", "connector", "source"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\multicast.ts"], "sourcesContent": ["import { Subject } from '../Subject';\nimport { Observable } from '../Observable';\nimport { ConnectableObservable } from '../observable/ConnectableObservable';\nimport { OperatorFunction, UnaryFunction, ObservedValueOf, ObservableInput } from '../types';\nimport { isFunction } from '../util/isFunction';\nimport { connect } from './connect';\n\n/**\n * An operator that creates a {@link ConnectableObservable}, that when connected,\n * with the `connect` method, will use the provided subject to multicast the values\n * from the source to all consumers.\n *\n * @param subject The subject to multicast through.\n * @return A function that returns a {@link ConnectableObservable}\n * @deprecated Will be removed in v8. To create a connectable observable, use {@link connectable}.\n * If you're using {@link refCount} after `multicast`, use the {@link share} operator instead.\n * `multicast(subject), refCount()` is equivalent to\n * `share({ connector: () => subject, resetOnError: false, resetOnComplete: false, resetOnRefCountZero: false })`.\n * Details: https://rxjs.dev/deprecations/multicasting\n */\nexport function multicast<T>(subject: Subject<T>): UnaryFunction<Observable<T>, ConnectableObservable<T>>;\n\n/**\n * Because this is deprecated in favor of the {@link connect} operator, and was otherwise poorly documented,\n * rather than duplicate the effort of documenting the same behavior, please see documentation for the\n * {@link connect} operator.\n *\n * @param subject The subject used to multicast.\n * @param selector A setup function to setup the multicast\n * @return A function that returns an observable that mirrors the observable returned by the selector.\n * @deprecated Will be removed in v8. Use the {@link connect} operator instead.\n * `multicast(subject, selector)` is equivalent to\n * `connect(selector, { connector: () => subject })`.\n * Details: https://rxjs.dev/deprecations/multicasting\n */\nexport function multicast<T, O extends ObservableInput<any>>(\n  subject: Subject<T>,\n  selector: (shared: Observable<T>) => O\n): OperatorFunction<T, ObservedValueOf<O>>;\n\n/**\n * An operator that creates a {@link ConnectableObservable}, that when connected,\n * with the `connect` method, will use the provided subject to multicast the values\n * from the source to all consumers.\n *\n * @param subjectFactory A factory that will be called to create the subject. Passing a function here\n * will cause the underlying subject to be \"reset\" on error, completion, or refCounted unsubscription of\n * the source.\n * @return A function that returns a {@link ConnectableObservable}\n * @deprecated Will be removed in v8. To create a connectable observable, use {@link connectable}.\n * If you're using {@link refCount} after `multicast`, use the {@link share} operator instead.\n * `multicast(() => new BehaviorSubject('test')), refCount()` is equivalent to\n * `share({ connector: () => new BehaviorSubject('test') })`.\n * Details: https://rxjs.dev/deprecations/multicasting\n */\nexport function multicast<T>(subjectFactory: () => Subject<T>): UnaryFunction<Observable<T>, ConnectableObservable<T>>;\n\n/**\n * Because this is deprecated in favor of the {@link connect} operator, and was otherwise poorly documented,\n * rather than duplicate the effort of documenting the same behavior, please see documentation for the\n * {@link connect} operator.\n *\n * @param subjectFactory A factory that creates the subject used to multicast.\n * @param selector A function to setup the multicast and select the output.\n * @return A function that returns an observable that mirrors the observable returned by the selector.\n * @deprecated Will be removed in v8. Use the {@link connect} operator instead.\n * `multicast(subjectFactory, selector)` is equivalent to\n * `connect(selector, { connector: subjectFactory })`.\n * Details: https://rxjs.dev/deprecations/multicasting\n */\nexport function multicast<T, O extends ObservableInput<any>>(\n  subjectFactory: () => Subject<T>,\n  selector: (shared: Observable<T>) => O\n): OperatorFunction<T, ObservedValueOf<O>>;\n\n/**\n * @deprecated Will be removed in v8. Use the {@link connectable} observable, the {@link connect} operator or the\n * {@link share} operator instead. See the overloads below for equivalent replacement examples of this operator's\n * behaviors.\n * Details: https://rxjs.dev/deprecations/multicasting\n */\nexport function multicast<T, R>(\n  subjectOrSubjectFactory: Subject<T> | (() => Subject<T>),\n  selector?: (source: Observable<T>) => Observable<R>\n): OperatorFunction<T, R> {\n  const subjectFactory = isFunction(subjectOrSubjectFactory) ? subjectOrSubjectFactory : () => subjectOrSubjectFactory;\n\n  if (isFunction(selector)) {\n    // If a selector function is provided, then we're a \"normal\" operator that isn't\n    // going to return a ConnectableObservable. We can use `connect` to do what we\n    // need to do.\n    return connect(selector, {\n      connector: subjectFactory,\n    });\n  }\n\n  return (source: Observable<T>) => new ConnectableObservable<any>(source, subjectFactory);\n}\n"], "mappings": "AAEA,SAASA,qBAAqB,QAAQ,qCAAqC;AAE3E,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,SAASC,OAAO,QAAQ,WAAW;AA4EnC,OAAM,SAAUC,SAASA,CACvBC,uBAAwD,EACxDC,QAAmD;EAEnD,IAAMC,cAAc,GAAGL,UAAU,CAACG,uBAAuB,CAAC,GAAGA,uBAAuB,GAAG;IAAM,OAAAA,uBAAuB;EAAvB,CAAuB;EAEpH,IAAIH,UAAU,CAACI,QAAQ,CAAC,EAAE;IAIxB,OAAOH,OAAO,CAACG,QAAQ,EAAE;MACvBE,SAAS,EAAED;KACZ,CAAC;;EAGJ,OAAO,UAACE,MAAqB;IAAK,WAAIR,qBAAqB,CAAMQ,MAAM,EAAEF,cAAc,CAAC;EAAtD,CAAsD;AAC1F", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}