{"ast": null, "code": "import { __values } from \"tslib\";\nimport { Subject } from '../Subject';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function windowCount(windowSize, startWindowEvery) {\n  if (startWindowEvery === void 0) {\n    startWindowEvery = 0;\n  }\n  var startEvery = startWindowEvery > 0 ? startWindowEvery : windowSize;\n  return operate(function (source, subscriber) {\n    var windows = [new Subject()];\n    var starts = [];\n    var count = 0;\n    subscriber.next(windows[0].asObservable());\n    source.subscribe(createOperatorSubscriber(subscriber, function (value) {\n      var e_1, _a;\n      try {\n        for (var windows_1 = __values(windows), windows_1_1 = windows_1.next(); !windows_1_1.done; windows_1_1 = windows_1.next()) {\n          var window_1 = windows_1_1.value;\n          window_1.next(value);\n        }\n      } catch (e_1_1) {\n        e_1 = {\n          error: e_1_1\n        };\n      } finally {\n        try {\n          if (windows_1_1 && !windows_1_1.done && (_a = windows_1.return)) _a.call(windows_1);\n        } finally {\n          if (e_1) throw e_1.error;\n        }\n      }\n      var c = count - windowSize + 1;\n      if (c >= 0 && c % startEvery === 0) {\n        windows.shift().complete();\n      }\n      if (++count % startEvery === 0) {\n        var window_2 = new Subject();\n        windows.push(window_2);\n        subscriber.next(window_2.asObservable());\n      }\n    }, function () {\n      while (windows.length > 0) {\n        windows.shift().complete();\n      }\n      subscriber.complete();\n    }, function (err) {\n      while (windows.length > 0) {\n        windows.shift().error(err);\n      }\n      subscriber.error(err);\n    }, function () {\n      starts = null;\n      windows = null;\n    }));\n  });\n}", "map": {"version": 3, "names": ["Subject", "operate", "createOperatorSubscriber", "windowCount", "windowSize", "startWindowEvery", "startEvery", "source", "subscriber", "windows", "starts", "count", "next", "asObservable", "subscribe", "value", "windows_1", "__values", "windows_1_1", "done", "window_1", "c", "shift", "complete", "window_2", "push", "length", "err", "error"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\windowCount.ts"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { Subject } from '../Subject';\nimport { OperatorFunction } from '../types';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\n\n/**\n * Branch out the source Observable values as a nested Observable with each\n * nested Observable emitting at most `windowSize` values.\n *\n * <span class=\"informal\">It's like {@link bufferCount}, but emits a nested\n * Observable instead of an array.</span>\n *\n * ![](windowCount.png)\n *\n * Returns an Observable that emits windows of items it collects from the source\n * Observable. The output Observable emits windows every `startWindowEvery`\n * items, each containing no more than `windowSize` items. When the source\n * Observable completes or encounters an error, the output Observable emits\n * the current window and propagates the notification from the source\n * Observable. If `startWindowEvery` is not provided, then new windows are\n * started immediately at the start of the source and when each window completes\n * with size `windowSize`.\n *\n * ## Examples\n *\n * Ignore every 3rd click event, starting from the first one\n *\n * ```ts\n * import { fromEvent, windowCount, map, skip, mergeAll } from 'rxjs';\n *\n * const clicks = fromEvent(document, 'click');\n * const result = clicks.pipe(\n *   windowCount(3),\n *   map(win => win.pipe(skip(1))), // skip first of every 3 clicks\n *   mergeAll()                     // flatten the Observable-of-Observables\n * );\n * result.subscribe(x => console.log(x));\n * ```\n *\n * Ignore every 3rd click event, starting from the third one\n *\n * ```ts\n * import { fromEvent, windowCount, mergeAll } from 'rxjs';\n *\n * const clicks = fromEvent(document, 'click');\n * const result = clicks.pipe(\n *   windowCount(2, 3),\n *   mergeAll() // flatten the Observable-of-Observables\n * );\n * result.subscribe(x => console.log(x));\n * ```\n *\n * @see {@link window}\n * @see {@link windowTime}\n * @see {@link windowToggle}\n * @see {@link windowWhen}\n * @see {@link bufferCount}\n *\n * @param windowSize The maximum number of values emitted by each window.\n * @param startWindowEvery Interval at which to start a new window. For example\n * if `startWindowEvery` is `2`, then a new window will be started on every\n * other value from the source. A new window is started at the beginning of the\n * source by default.\n * @return A function that returns an Observable of windows, which in turn are\n * Observable of values.\n */\nexport function windowCount<T>(windowSize: number, startWindowEvery: number = 0): OperatorFunction<T, Observable<T>> {\n  const startEvery = startWindowEvery > 0 ? startWindowEvery : windowSize;\n\n  return operate((source, subscriber) => {\n    let windows = [new Subject<T>()];\n    let starts: number[] = [];\n    let count = 0;\n\n    // Open the first window.\n    subscriber.next(windows[0].asObservable());\n\n    source.subscribe(\n      createOperatorSubscriber(\n        subscriber,\n        (value: T) => {\n          // Emit the value through all current windows.\n          // We don't need to create a new window yet, we\n          // do that as soon as we close one.\n          for (const window of windows) {\n            window.next(value);\n          }\n          // Here we're using the size of the window array to figure\n          // out if the oldest window has emitted enough values. We can do this\n          // because the size of the window array is a function of the values\n          // seen by the subscription. If it's time to close it, we complete\n          // it and remove it.\n          const c = count - windowSize + 1;\n          if (c >= 0 && c % startEvery === 0) {\n            windows.shift()!.complete();\n          }\n\n          // Look to see if the next count tells us it's time to open a new window.\n          // TODO: We need to figure out if this really makes sense. We're technically\n          // emitting windows *before* we have a value to emit them for. It's probably\n          // more expected that we should be emitting the window when the start\n          // count is reached -- not before.\n          if (++count % startEvery === 0) {\n            const window = new Subject<T>();\n            windows.push(window);\n            subscriber.next(window.asObservable());\n          }\n        },\n        () => {\n          while (windows.length > 0) {\n            windows.shift()!.complete();\n          }\n          subscriber.complete();\n        },\n        (err) => {\n          while (windows.length > 0) {\n            windows.shift()!.error(err);\n          }\n          subscriber.error(err);\n        },\n        () => {\n          starts = null!;\n          windows = null!;\n        }\n      )\n    );\n  });\n}\n"], "mappings": ";AACA,SAASA,OAAO,QAAQ,YAAY;AAEpC,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AA+D/D,OAAM,SAAUC,WAAWA,CAAIC,UAAkB,EAAEC,gBAA4B;EAA5B,IAAAA,gBAAA;IAAAA,gBAAA,IAA4B;EAAA;EAC7E,IAAMC,UAAU,GAAGD,gBAAgB,GAAG,CAAC,GAAGA,gBAAgB,GAAGD,UAAU;EAEvE,OAAOH,OAAO,CAAC,UAACM,MAAM,EAAEC,UAAU;IAChC,IAAIC,OAAO,GAAG,CAAC,IAAIT,OAAO,EAAK,CAAC;IAChC,IAAIU,MAAM,GAAa,EAAE;IACzB,IAAIC,KAAK,GAAG,CAAC;IAGbH,UAAU,CAACI,IAAI,CAACH,OAAO,CAAC,CAAC,CAAC,CAACI,YAAY,EAAE,CAAC;IAE1CN,MAAM,CAACO,SAAS,CACdZ,wBAAwB,CACtBM,UAAU,EACV,UAACO,KAAQ;;;QAIP,KAAqB,IAAAC,SAAA,GAAAC,QAAA,CAAAR,OAAO,GAAAS,WAAA,GAAAF,SAAA,CAAAJ,IAAA,KAAAM,WAAA,CAAAC,IAAA,EAAAD,WAAA,GAAAF,SAAA,CAAAJ,IAAA,IAAE;UAAzB,IAAMQ,QAAM,GAAAF,WAAA,CAAAH,KAAA;UACfK,QAAM,CAACR,IAAI,CAACG,KAAK,CAAC;;;;;;;;;;;;;MAOpB,IAAMM,CAAC,GAAGV,KAAK,GAAGP,UAAU,GAAG,CAAC;MAChC,IAAIiB,CAAC,IAAI,CAAC,IAAIA,CAAC,GAAGf,UAAU,KAAK,CAAC,EAAE;QAClCG,OAAO,CAACa,KAAK,EAAG,CAACC,QAAQ,EAAE;;MAQ7B,IAAI,EAAEZ,KAAK,GAAGL,UAAU,KAAK,CAAC,EAAE;QAC9B,IAAMkB,QAAM,GAAG,IAAIxB,OAAO,EAAK;QAC/BS,OAAO,CAACgB,IAAI,CAACD,QAAM,CAAC;QACpBhB,UAAU,CAACI,IAAI,CAACY,QAAM,CAACX,YAAY,EAAE,CAAC;;IAE1C,CAAC,EACD;MACE,OAAOJ,OAAO,CAACiB,MAAM,GAAG,CAAC,EAAE;QACzBjB,OAAO,CAACa,KAAK,EAAG,CAACC,QAAQ,EAAE;;MAE7Bf,UAAU,CAACe,QAAQ,EAAE;IACvB,CAAC,EACD,UAACI,GAAG;MACF,OAAOlB,OAAO,CAACiB,MAAM,GAAG,CAAC,EAAE;QACzBjB,OAAO,CAACa,KAAK,EAAG,CAACM,KAAK,CAACD,GAAG,CAAC;;MAE7BnB,UAAU,CAACoB,KAAK,CAACD,GAAG,CAAC;IACvB,CAAC,EACD;MACEjB,MAAM,GAAG,IAAK;MACdD,OAAO,GAAG,IAAK;IACjB,CAAC,CACF,CACF;EACH,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}