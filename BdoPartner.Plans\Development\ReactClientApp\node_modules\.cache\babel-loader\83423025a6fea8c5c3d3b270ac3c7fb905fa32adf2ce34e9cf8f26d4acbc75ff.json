{"ast": null, "code": "/**\r\n * Form Status enumeration definitions.\r\n * Reference to Enumerations.FormStatus in server side.\r\n * Reference to records in table dbo.[FormStatus].\r\n * Work for form status management and display.\r\n * Updated to match the new workflow-aware status system.\r\n */\nexport const FormStatus = {\n  // Planning Cycle Statuses (0-4)\n  /**\r\n   * Planning - Not Started status - Form is not started yet for Planning cycle.\r\n   */\n  PlanningNotStarted: 0,\n  /**\r\n   * Planning - Draft status - Form is in draft state for Planning cycle.\r\n   */\n  PlanningDraft: 1,\n  /**\r\n   * Planning - Reopened status - Form is sent back by reviewer for further edits.\r\n   */\n  PlanningReopened: 2,\n  /**\r\n   * Planning - Under Review status - Form is submitted for review in Planning cycle.\r\n   */\n  PlanningUnderReview: 3,\n  /**\r\n   * Planning - Completed status - Form has been completed for Planning cycle.\r\n   */\n  PlanningCompleted: 4,\n  // Mid Year Review Cycle Statuses (5-9)\n  /**\r\n   * Mid Year Review - Not Started status - Form is not started yet for Mid Year Review cycle.\r\n   */\n  MidYearReviewNotStarted: 5,\n  /**\r\n   * Mid Year Review - Draft status - Form is in draft state for Mid Year Review cycle.\r\n   */\n  MidYearReviewDraft: 6,\n  /**\r\n   * Mid Year Review - Reopened status - Form is sent back by reviewer for further edits.\r\n   */\n  MidYearReviewReopened: 7,\n  /**\r\n   * Mid Year Review - Under Review status - Form is submitted for review in Mid Year Review cycle.\r\n   */\n  MidYearReviewUnderReview: 8,\n  /**\r\n   * Mid Year Review - Completed status - Form has been completed for Mid Year Review cycle.\r\n   */\n  MidYearReviewCompleted: 9,\n  // Year End Review Cycle Statuses (10-14)\n  /**\r\n   * Year End Review - Not Started status - Form is not started yet for Year End Review cycle.\r\n   */\n  YearEndReviewNotStarted: 10,\n  /**\r\n   * Year End Review - Draft status - Form is in draft state for Year End Review cycle.\r\n   */\n  YearEndReviewDraft: 11,\n  /**\r\n   * Year End Review - Reopened status - Form is sent back by reviewer for further edits.\r\n   */\n  YearEndReviewReopened: 12,\n  /**\r\n   * Year End Review - Under Review status - Form is submitted for review in Year End Review cycle.\r\n   */\n  YearEndReviewUnderReview: 13,\n  /**\r\n   * Year End Review - Completed status - Form has been completed for Year End Review cycle.\r\n   */\n  YearEndReviewCompleted: 14\n};\n\n/**\r\n * Get form status display name\r\n * @param {number} statusId - The form status ID\r\n * @returns {string} Display name of the form status\r\n */\nexport const getFormStatusName = statusId => {\n  switch (statusId) {\n    // Planning Cycle\n    case FormStatus.PlanningNotStarted:\n      return 'Not Started';\n    case FormStatus.PlanningDraft:\n      return 'Draft';\n    case FormStatus.PlanningReopened:\n      return 'Reopened';\n    case FormStatus.PlanningUnderReview:\n      return 'Under Review';\n    case FormStatus.PlanningCompleted:\n      return 'Completed';\n\n    // Mid Year Review Cycle\n    case FormStatus.MidYearReviewNotStarted:\n      return 'Not Started';\n    case FormStatus.MidYearReviewDraft:\n      return 'Draft';\n    case FormStatus.MidYearReviewReopened:\n      return 'Reopened';\n    case FormStatus.MidYearReviewUnderReview:\n      return 'Under Review';\n    case FormStatus.MidYearReviewCompleted:\n      return 'Completed';\n\n    // Year End Review Cycle\n    case FormStatus.YearEndReviewNotStarted:\n      return 'Not Started';\n    case FormStatus.YearEndReviewDraft:\n      return 'Draft';\n    case FormStatus.YearEndReviewReopened:\n      return 'Reopened';\n    case FormStatus.YearEndReviewUnderReview:\n      return 'Under Review';\n    case FormStatus.YearEndReviewCompleted:\n      return 'Completed';\n    default:\n      return 'Unknown';\n  }\n};\n\n/**\r\n * Get form status CSS class for styling\r\n * @param {number} statusId - The form status ID\r\n * @returns {string} CSS class name for the status\r\n */\nexport const getFormStatusClass = statusId => {\n  switch (statusId) {\n    // Planning Cycle\n    case FormStatus.PlanningNotStarted:\n      return 'status-not-started';\n    case FormStatus.PlanningDraft:\n      return 'status-draft';\n    case FormStatus.PlanningReopened:\n      return 'status-reopened';\n    case FormStatus.PlanningUnderReview:\n      return 'status-under-review';\n    case FormStatus.PlanningCompleted:\n      return 'status-completed';\n\n    // Mid Year Review Cycle\n    case FormStatus.MidYearReviewNotStarted:\n      return 'status-not-started';\n    case FormStatus.MidYearReviewDraft:\n      return 'status-draft';\n    case FormStatus.MidYearReviewReopened:\n      return 'status-reopened';\n    case FormStatus.MidYearReviewUnderReview:\n      return 'status-under-review';\n    case FormStatus.MidYearReviewCompleted:\n      return 'status-completed';\n\n    // Year End Review Cycle\n    case FormStatus.YearEndReviewNotStarted:\n      return 'status-not-started';\n    case FormStatus.YearEndReviewDraft:\n      return 'status-draft';\n    case FormStatus.YearEndReviewReopened:\n      return 'status-reopened';\n    case FormStatus.YearEndReviewUnderReview:\n      return 'status-under-review';\n    case FormStatus.YearEndReviewCompleted:\n      return 'status-completed';\n    default:\n      return 'status-unknown';\n  }\n};\n\n/**\r\n * Check if a form status represents an editable state for the form owner\r\n * @param {number} statusId - The form status ID\r\n * @returns {boolean} True if the form is editable by the owner\r\n */\nexport const isFormEditableByOwner = statusId => {\n  return statusId === FormStatus.PlanningNotStarted || statusId === FormStatus.PlanningDraft || statusId === FormStatus.PlanningReopened || statusId === FormStatus.MidYearReviewNotStarted || statusId === FormStatus.MidYearReviewDraft || statusId === FormStatus.MidYearReviewReopened || statusId === FormStatus.YearEndReviewNotStarted || statusId === FormStatus.YearEndReviewDraft || statusId === FormStatus.YearEndReviewReopened;\n};\n\n/**\r\n * Check if a form status represents a completed state\r\n * @param {number} statusId - The form status ID\r\n * @returns {boolean} True if the form is completed\r\n */\nexport const isFormCompleted = statusId => {\n  return statusId === FormStatus.PlanningCompleted || statusId === FormStatus.MidYearReviewCompleted || statusId === FormStatus.YearEndReviewCompleted;\n};", "map": {"version": 3, "names": ["FormStatus", "PlanningNotStarted", "PlanningDraft", "PlanningReopened", "PlanningUnderReview", "PlanningCompleted", "MidYearReviewNotStarted", "MidYearReviewDraft", "MidYearReviewReopened", "MidYearReviewUnderReview", "MidYearReviewCompleted", "YearEndReviewNotStarted", "YearEndReviewDraft", "YearEndReviewReopened", "YearEndReviewUnderReview", "YearEndReviewCompleted", "getFormStatusName", "statusId", "getFormStatusClass", "isFormEditableByOwner", "isFormCompleted"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/core/enumertions/formStatus.js"], "sourcesContent": ["/**\r\n * Form Status enumeration definitions.\r\n * Reference to Enumerations.FormStatus in server side.\r\n * Reference to records in table dbo.[FormStatus].\r\n * Work for form status management and display.\r\n * Updated to match the new workflow-aware status system.\r\n */\r\nexport const FormStatus = {\r\n  // Planning Cycle Statuses (0-4)\r\n  /**\r\n   * Planning - Not Started status - Form is not started yet for Planning cycle.\r\n   */\r\n  PlanningNotStarted: 0,\r\n\r\n  /**\r\n   * Planning - Draft status - Form is in draft state for Planning cycle.\r\n   */\r\n  PlanningDraft: 1,\r\n\r\n  /**\r\n   * Planning - Reopened status - Form is sent back by reviewer for further edits.\r\n   */\r\n  PlanningReopened: 2,\r\n\r\n  /**\r\n   * Planning - Under Review status - Form is submitted for review in Planning cycle.\r\n   */\r\n  PlanningUnderReview: 3,\r\n\r\n  /**\r\n   * Planning - Completed status - Form has been completed for Planning cycle.\r\n   */\r\n  PlanningCompleted: 4,\r\n\r\n  // Mid Year Review Cycle Statuses (5-9)\r\n  /**\r\n   * Mid Year Review - Not Started status - Form is not started yet for Mid Year Review cycle.\r\n   */\r\n  MidYearReviewNotStarted: 5,\r\n\r\n  /**\r\n   * Mid Year Review - Draft status - Form is in draft state for Mid Year Review cycle.\r\n   */\r\n  MidYearReviewDraft: 6,\r\n\r\n  /**\r\n   * Mid Year Review - Reopened status - Form is sent back by reviewer for further edits.\r\n   */\r\n  MidYearReviewReopened: 7,\r\n\r\n  /**\r\n   * Mid Year Review - Under Review status - Form is submitted for review in Mid Year Review cycle.\r\n   */\r\n  MidYearReviewUnderReview: 8,\r\n\r\n  /**\r\n   * Mid Year Review - Completed status - Form has been completed for Mid Year Review cycle.\r\n   */\r\n  MidYearReviewCompleted: 9,\r\n\r\n  // Year End Review Cycle Statuses (10-14)\r\n  /**\r\n   * Year End Review - Not Started status - Form is not started yet for Year End Review cycle.\r\n   */\r\n  YearEndReviewNotStarted: 10,\r\n\r\n  /**\r\n   * Year End Review - Draft status - Form is in draft state for Year End Review cycle.\r\n   */\r\n  YearEndReviewDraft: 11,\r\n\r\n  /**\r\n   * Year End Review - Reopened status - Form is sent back by reviewer for further edits.\r\n   */\r\n  YearEndReviewReopened: 12,\r\n\r\n  /**\r\n   * Year End Review - Under Review status - Form is submitted for review in Year End Review cycle.\r\n   */\r\n  YearEndReviewUnderReview: 13,\r\n\r\n  /**\r\n   * Year End Review - Completed status - Form has been completed for Year End Review cycle.\r\n   */\r\n  YearEndReviewCompleted: 14\r\n\r\n};\r\n\r\n/**\r\n * Get form status display name\r\n * @param {number} statusId - The form status ID\r\n * @returns {string} Display name of the form status\r\n */\r\nexport const getFormStatusName = (statusId) => {\r\n  switch (statusId) {\r\n    // Planning Cycle\r\n    case FormStatus.PlanningNotStarted:\r\n      return 'Not Started';\r\n    case FormStatus.PlanningDraft:\r\n      return 'Draft';\r\n    case FormStatus.PlanningReopened:\r\n      return 'Reopened';\r\n    case FormStatus.PlanningUnderReview:\r\n      return 'Under Review';\r\n    case FormStatus.PlanningCompleted:\r\n      return 'Completed';\r\n\r\n    // Mid Year Review Cycle\r\n    case FormStatus.MidYearReviewNotStarted:\r\n      return 'Not Started';\r\n    case FormStatus.MidYearReviewDraft:\r\n      return 'Draft';\r\n    case FormStatus.MidYearReviewReopened:\r\n      return 'Reopened';\r\n    case FormStatus.MidYearReviewUnderReview:\r\n      return 'Under Review';\r\n    case FormStatus.MidYearReviewCompleted:\r\n      return 'Completed';\r\n\r\n    // Year End Review Cycle\r\n    case FormStatus.YearEndReviewNotStarted:\r\n      return 'Not Started';\r\n    case FormStatus.YearEndReviewDraft:\r\n      return 'Draft';\r\n    case FormStatus.YearEndReviewReopened:\r\n      return 'Reopened';\r\n    case FormStatus.YearEndReviewUnderReview:\r\n      return 'Under Review';\r\n    case FormStatus.YearEndReviewCompleted:\r\n      return 'Completed';\r\n    default:\r\n      return 'Unknown';\r\n  }\r\n};\r\n\r\n/**\r\n * Get form status CSS class for styling\r\n * @param {number} statusId - The form status ID\r\n * @returns {string} CSS class name for the status\r\n */\r\nexport const getFormStatusClass = (statusId) => {\r\n  switch (statusId) {\r\n    // Planning Cycle\r\n    case FormStatus.PlanningNotStarted:\r\n      return 'status-not-started';\r\n    case FormStatus.PlanningDraft:\r\n      return 'status-draft';\r\n    case FormStatus.PlanningReopened:\r\n      return 'status-reopened';\r\n    case FormStatus.PlanningUnderReview:\r\n      return 'status-under-review';\r\n    case FormStatus.PlanningCompleted:\r\n      return 'status-completed';\r\n\r\n    // Mid Year Review Cycle\r\n    case FormStatus.MidYearReviewNotStarted:\r\n      return 'status-not-started';\r\n    case FormStatus.MidYearReviewDraft:\r\n      return 'status-draft';\r\n    case FormStatus.MidYearReviewReopened:\r\n      return 'status-reopened';\r\n    case FormStatus.MidYearReviewUnderReview:\r\n      return 'status-under-review';\r\n    case FormStatus.MidYearReviewCompleted:\r\n      return 'status-completed';\r\n\r\n    // Year End Review Cycle\r\n    case FormStatus.YearEndReviewNotStarted:\r\n      return 'status-not-started';\r\n    case FormStatus.YearEndReviewDraft:\r\n      return 'status-draft';\r\n    case FormStatus.YearEndReviewReopened:\r\n      return 'status-reopened';\r\n    case FormStatus.YearEndReviewUnderReview:\r\n      return 'status-under-review';\r\n    case FormStatus.YearEndReviewCompleted:\r\n      return 'status-completed';\r\n\r\n    default:\r\n      return 'status-unknown';\r\n  }\r\n};\r\n\r\n/**\r\n * Check if a form status represents an editable state for the form owner\r\n * @param {number} statusId - The form status ID\r\n * @returns {boolean} True if the form is editable by the owner\r\n */\r\nexport const isFormEditableByOwner = (statusId) => {\r\n  return statusId === FormStatus.PlanningNotStarted ||\r\n         statusId === FormStatus.PlanningDraft ||\r\n         statusId === FormStatus.PlanningReopened ||\r\n         statusId === FormStatus.MidYearReviewNotStarted ||\r\n         statusId === FormStatus.MidYearReviewDraft ||\r\n         statusId === FormStatus.MidYearReviewReopened ||\r\n         statusId === FormStatus.YearEndReviewNotStarted ||\r\n         statusId === FormStatus.YearEndReviewDraft ||\r\n         statusId === FormStatus.YearEndReviewReopened \r\n       \r\n};\r\n\r\n/**\r\n * Check if a form status represents a completed state\r\n * @param {number} statusId - The form status ID\r\n * @returns {boolean} True if the form is completed\r\n */\r\nexport const isFormCompleted = (statusId) => {\r\n  return statusId === FormStatus.PlanningCompleted ||\r\n         statusId === FormStatus.MidYearReviewCompleted ||\r\n         statusId === FormStatus.YearEndReviewCompleted \r\n};\r\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,UAAU,GAAG;EACxB;EACA;AACF;AACA;EACEC,kBAAkB,EAAE,CAAC;EAErB;AACF;AACA;EACEC,aAAa,EAAE,CAAC;EAEhB;AACF;AACA;EACEC,gBAAgB,EAAE,CAAC;EAEnB;AACF;AACA;EACEC,mBAAmB,EAAE,CAAC;EAEtB;AACF;AACA;EACEC,iBAAiB,EAAE,CAAC;EAEpB;EACA;AACF;AACA;EACEC,uBAAuB,EAAE,CAAC;EAE1B;AACF;AACA;EACEC,kBAAkB,EAAE,CAAC;EAErB;AACF;AACA;EACEC,qBAAqB,EAAE,CAAC;EAExB;AACF;AACA;EACEC,wBAAwB,EAAE,CAAC;EAE3B;AACF;AACA;EACEC,sBAAsB,EAAE,CAAC;EAEzB;EACA;AACF;AACA;EACEC,uBAAuB,EAAE,EAAE;EAE3B;AACF;AACA;EACEC,kBAAkB,EAAE,EAAE;EAEtB;AACF;AACA;EACEC,qBAAqB,EAAE,EAAE;EAEzB;AACF;AACA;EACEC,wBAAwB,EAAE,EAAE;EAE5B;AACF;AACA;EACEC,sBAAsB,EAAE;AAE1B,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,iBAAiB,GAAIC,QAAQ,IAAK;EAC7C,QAAQA,QAAQ;IACd;IACA,KAAKjB,UAAU,CAACC,kBAAkB;MAChC,OAAO,aAAa;IACtB,KAAKD,UAAU,CAACE,aAAa;MAC3B,OAAO,OAAO;IAChB,KAAKF,UAAU,CAACG,gBAAgB;MAC9B,OAAO,UAAU;IACnB,KAAKH,UAAU,CAACI,mBAAmB;MACjC,OAAO,cAAc;IACvB,KAAKJ,UAAU,CAACK,iBAAiB;MAC/B,OAAO,WAAW;;IAEpB;IACA,KAAKL,UAAU,CAACM,uBAAuB;MACrC,OAAO,aAAa;IACtB,KAAKN,UAAU,CAACO,kBAAkB;MAChC,OAAO,OAAO;IAChB,KAAKP,UAAU,CAACQ,qBAAqB;MACnC,OAAO,UAAU;IACnB,KAAKR,UAAU,CAACS,wBAAwB;MACtC,OAAO,cAAc;IACvB,KAAKT,UAAU,CAACU,sBAAsB;MACpC,OAAO,WAAW;;IAEpB;IACA,KAAKV,UAAU,CAACW,uBAAuB;MACrC,OAAO,aAAa;IACtB,KAAKX,UAAU,CAACY,kBAAkB;MAChC,OAAO,OAAO;IAChB,KAAKZ,UAAU,CAACa,qBAAqB;MACnC,OAAO,UAAU;IACnB,KAAKb,UAAU,CAACc,wBAAwB;MACtC,OAAO,cAAc;IACvB,KAAKd,UAAU,CAACe,sBAAsB;MACpC,OAAO,WAAW;IACpB;MACE,OAAO,SAAS;EACpB;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMG,kBAAkB,GAAID,QAAQ,IAAK;EAC9C,QAAQA,QAAQ;IACd;IACA,KAAKjB,UAAU,CAACC,kBAAkB;MAChC,OAAO,oBAAoB;IAC7B,KAAKD,UAAU,CAACE,aAAa;MAC3B,OAAO,cAAc;IACvB,KAAKF,UAAU,CAACG,gBAAgB;MAC9B,OAAO,iBAAiB;IAC1B,KAAKH,UAAU,CAACI,mBAAmB;MACjC,OAAO,qBAAqB;IAC9B,KAAKJ,UAAU,CAACK,iBAAiB;MAC/B,OAAO,kBAAkB;;IAE3B;IACA,KAAKL,UAAU,CAACM,uBAAuB;MACrC,OAAO,oBAAoB;IAC7B,KAAKN,UAAU,CAACO,kBAAkB;MAChC,OAAO,cAAc;IACvB,KAAKP,UAAU,CAACQ,qBAAqB;MACnC,OAAO,iBAAiB;IAC1B,KAAKR,UAAU,CAACS,wBAAwB;MACtC,OAAO,qBAAqB;IAC9B,KAAKT,UAAU,CAACU,sBAAsB;MACpC,OAAO,kBAAkB;;IAE3B;IACA,KAAKV,UAAU,CAACW,uBAAuB;MACrC,OAAO,oBAAoB;IAC7B,KAAKX,UAAU,CAACY,kBAAkB;MAChC,OAAO,cAAc;IACvB,KAAKZ,UAAU,CAACa,qBAAqB;MACnC,OAAO,iBAAiB;IAC1B,KAAKb,UAAU,CAACc,wBAAwB;MACtC,OAAO,qBAAqB;IAC9B,KAAKd,UAAU,CAACe,sBAAsB;MACpC,OAAO,kBAAkB;IAE3B;MACE,OAAO,gBAAgB;EAC3B;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMI,qBAAqB,GAAIF,QAAQ,IAAK;EACjD,OAAOA,QAAQ,KAAKjB,UAAU,CAACC,kBAAkB,IAC1CgB,QAAQ,KAAKjB,UAAU,CAACE,aAAa,IACrCe,QAAQ,KAAKjB,UAAU,CAACG,gBAAgB,IACxCc,QAAQ,KAAKjB,UAAU,CAACM,uBAAuB,IAC/CW,QAAQ,KAAKjB,UAAU,CAACO,kBAAkB,IAC1CU,QAAQ,KAAKjB,UAAU,CAACQ,qBAAqB,IAC7CS,QAAQ,KAAKjB,UAAU,CAACW,uBAAuB,IAC/CM,QAAQ,KAAKjB,UAAU,CAACY,kBAAkB,IAC1CK,QAAQ,KAAKjB,UAAU,CAACa,qBAAqB;AAEtD,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMO,eAAe,GAAIH,QAAQ,IAAK;EAC3C,OAAOA,QAAQ,KAAKjB,UAAU,CAACK,iBAAiB,IACzCY,QAAQ,KAAKjB,UAAU,CAACU,sBAAsB,IAC9CO,QAAQ,KAAKjB,UAAU,CAACe,sBAAsB;AACvD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}