{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { AsyncScheduler } from './AsyncScheduler';\nvar QueueScheduler = function (_super) {\n  __extends(QueueScheduler, _super);\n  function QueueScheduler() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  return QueueScheduler;\n}(AsyncScheduler);\nexport { QueueScheduler };", "map": {"version": 3, "names": ["AsyncScheduler", "QueueScheduler", "_super", "__extends"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\scheduler\\QueueScheduler.ts"], "sourcesContent": ["import { AsyncScheduler } from './AsyncScheduler';\n\nexport class QueueScheduler extends AsyncScheduler {\n}\n"], "mappings": ";AAAA,SAASA,cAAc,QAAQ,kBAAkB;AAEjD,IAAAC,cAAA,aAAAC,MAAA;EAAoCC,SAAA,CAAAF,cAAA,EAAAC,MAAA;EAApC,SAAAD,eAAA;;EACA;EAAA,OAAAA,cAAC;AAAD,CAAC,CADmCD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}