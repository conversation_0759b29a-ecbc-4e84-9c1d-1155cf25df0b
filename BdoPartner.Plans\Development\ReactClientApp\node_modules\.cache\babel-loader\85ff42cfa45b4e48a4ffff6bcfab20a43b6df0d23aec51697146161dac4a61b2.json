{"ast": null, "code": "import { switchMap } from './switchMap';\nimport { identity } from '../util/identity';\nexport function switchAll() {\n  return switchMap(identity);\n}", "map": {"version": 3, "names": ["switchMap", "identity", "switchAll"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\switchAll.ts"], "sourcesContent": ["import { OperatorFunction, ObservableInput, ObservedValueOf } from '../types';\nimport { switchMap } from './switchMap';\nimport { identity } from '../util/identity';\n\n/**\n * Converts a higher-order Observable into a first-order Observable\n * producing values only from the most recent observable sequence\n *\n * <span class=\"informal\">Flattens an Observable-of-Observables.</span>\n *\n * ![](switchAll.png)\n *\n * `switchAll` subscribes to a source that is an observable of observables, also known as a\n * \"higher-order observable\" (or `Observable<Observable<T>>`). It subscribes to the most recently\n * provided \"inner observable\" emitted by the source, unsubscribing from any previously subscribed\n * to inner observable, such that only the most recent inner observable may be subscribed to at\n * any point in time. The resulting observable returned by `switchAll` will only complete if the\n * source observable completes, *and* any currently subscribed to inner observable also has completed,\n * if there are any.\n *\n * ## Examples\n *\n * Spawn a new interval observable for each click event, but for every new\n * click, cancel the previous interval and subscribe to the new one\n *\n * ```ts\n * import { fromEvent, tap, map, interval, switchAll } from 'rxjs';\n *\n * const clicks = fromEvent(document, 'click').pipe(tap(() => console.log('click')));\n * const source = clicks.pipe(map(() => interval(1000)));\n *\n * source\n *   .pipe(switchAll())\n *   .subscribe(x => console.log(x));\n *\n * // Output\n * // click\n * // 0\n * // 1\n * // 2\n * // 3\n * // ...\n * // click\n * // 0\n * // 1\n * // 2\n * // ...\n * // click\n * // ...\n * ```\n *\n * @see {@link combineLatestAll}\n * @see {@link concatAll}\n * @see {@link exhaustAll}\n * @see {@link switchMap}\n * @see {@link switchMapTo}\n * @see {@link mergeAll}\n *\n * @return A function that returns an Observable that converts a higher-order\n * Observable into a first-order Observable producing values only from the most\n * recent Observable sequence.\n */\nexport function switchAll<O extends ObservableInput<any>>(): OperatorFunction<O, ObservedValueOf<O>> {\n  return switchMap(identity);\n}\n"], "mappings": "AACA,SAASA,SAAS,QAAQ,aAAa;AACvC,SAASC,QAAQ,QAAQ,kBAAkB;AA4D3C,OAAM,SAAUC,SAASA,CAAA;EACvB,OAAOF,SAAS,CAACC,QAAQ,CAAC;AAC5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}