{"ast": null, "code": "import { Observable } from '../Observable';\nimport { EMPTY } from './empty';\nexport function range(start, count, scheduler) {\n  if (count == null) {\n    count = start;\n    start = 0;\n  }\n  if (count <= 0) {\n    return EMPTY;\n  }\n  var end = count + start;\n  return new Observable(scheduler ? function (subscriber) {\n    var n = start;\n    return scheduler.schedule(function () {\n      if (n < end) {\n        subscriber.next(n++);\n        this.schedule();\n      } else {\n        subscriber.complete();\n      }\n    });\n  } : function (subscriber) {\n    var n = start;\n    while (n < end && !subscriber.closed) {\n      subscriber.next(n++);\n    }\n    subscriber.complete();\n  });\n}", "map": {"version": 3, "names": ["Observable", "EMPTY", "range", "start", "count", "scheduler", "end", "subscriber", "n", "schedule", "next", "complete", "closed"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\observable\\range.ts"], "sourcesContent": ["import { SchedulerLike } from '../types';\nimport { Observable } from '../Observable';\nimport { EMPTY } from './empty';\n\nexport function range(start: number, count?: number): Observable<number>;\n\n/**\n * @deprecated The `scheduler` parameter will be removed in v8. Use `range(start, count).pipe(observeOn(scheduler))` instead. Details: Details: https://rxjs.dev/deprecations/scheduler-argument\n */\nexport function range(start: number, count: number | undefined, scheduler: SchedulerLike): Observable<number>;\n\n/**\n * Creates an Observable that emits a sequence of numbers within a specified\n * range.\n *\n * <span class=\"informal\">Emits a sequence of numbers in a range.</span>\n *\n * ![](range.png)\n *\n * `range` operator emits a range of sequential integers, in order, where you\n * select the `start` of the range and its `length`. By default, uses no\n * {@link SchedulerLike} and just delivers the notifications synchronously, but may use\n * an optional {@link SchedulerLike} to regulate those deliveries.\n *\n * ## Example\n *\n * Produce a range of numbers\n *\n * ```ts\n * import { range } from 'rxjs';\n *\n * const numbers = range(1, 3);\n *\n * numbers.subscribe({\n *   next: value => console.log(value),\n *   complete: () => console.log('Complete!')\n * });\n *\n * // Logs:\n * // 1\n * // 2\n * // 3\n * // 'Complete!'\n * ```\n *\n * @see {@link timer}\n * @see {@link interval}\n *\n * @param start The value of the first integer in the sequence.\n * @param count The number of sequential integers to generate.\n * @param scheduler A {@link SchedulerLike} to use for scheduling the emissions\n * of the notifications.\n * @return An Observable of numbers that emits a finite range of sequential integers.\n */\nexport function range(start: number, count?: number, scheduler?: SchedulerLike): Observable<number> {\n  if (count == null) {\n    // If one argument was passed, it's the count, not the start.\n    count = start;\n    start = 0;\n  }\n\n  if (count <= 0) {\n    // No count? We're going nowhere. Return EMPTY.\n    return EMPTY;\n  }\n\n  // Where the range should stop.\n  const end = count + start;\n\n  return new Observable(\n    scheduler\n      ? // The deprecated scheduled path.\n        (subscriber) => {\n          let n = start;\n          return scheduler.schedule(function () {\n            if (n < end) {\n              subscriber.next(n++);\n              this.schedule();\n            } else {\n              subscriber.complete();\n            }\n          });\n        }\n      : // Standard synchronous range.\n        (subscriber) => {\n          let n = start;\n          while (n < end && !subscriber.closed) {\n            subscriber.next(n++);\n          }\n          subscriber.complete();\n        }\n  );\n}\n"], "mappings": "AACA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,KAAK,QAAQ,SAAS;AAoD/B,OAAM,SAAUC,KAAKA,CAACC,KAAa,EAAEC,KAAc,EAAEC,SAAyB;EAC5E,IAAID,KAAK,IAAI,IAAI,EAAE;IAEjBA,KAAK,GAAGD,KAAK;IACbA,KAAK,GAAG,CAAC;;EAGX,IAAIC,KAAK,IAAI,CAAC,EAAE;IAEd,OAAOH,KAAK;;EAId,IAAMK,GAAG,GAAGF,KAAK,GAAGD,KAAK;EAEzB,OAAO,IAAIH,UAAU,CACnBK,SAAS,GAEL,UAACE,UAAU;IACT,IAAIC,CAAC,GAAGL,KAAK;IACb,OAAOE,SAAS,CAACI,QAAQ,CAAC;MACxB,IAAID,CAAC,GAAGF,GAAG,EAAE;QACXC,UAAU,CAACG,IAAI,CAACF,CAAC,EAAE,CAAC;QACpB,IAAI,CAACC,QAAQ,EAAE;OAChB,MAAM;QACLF,UAAU,CAACI,QAAQ,EAAE;;IAEzB,CAAC,CAAC;EACJ,CAAC,GAED,UAACJ,UAAU;IACT,IAAIC,CAAC,GAAGL,KAAK;IACb,OAAOK,CAAC,GAAGF,GAAG,IAAI,CAACC,UAAU,CAACK,MAAM,EAAE;MACpCL,UAAU,CAACG,IAAI,CAACF,CAAC,EAAE,CAAC;;IAEtBD,UAAU,CAACI,QAAQ,EAAE;EACvB,CAAC,CACN;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}