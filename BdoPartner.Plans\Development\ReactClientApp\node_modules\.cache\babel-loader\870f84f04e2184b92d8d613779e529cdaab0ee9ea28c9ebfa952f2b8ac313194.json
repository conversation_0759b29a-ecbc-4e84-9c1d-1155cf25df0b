{"ast": null, "code": "import { __read, __spreadArray } from \"tslib\";\nexport var intervalProvider = {\n  setInterval: function (handler, timeout) {\n    var args = [];\n    for (var _i = 2; _i < arguments.length; _i++) {\n      args[_i - 2] = arguments[_i];\n    }\n    var delegate = intervalProvider.delegate;\n    if (delegate === null || delegate === void 0 ? void 0 : delegate.setInterval) {\n      return delegate.setInterval.apply(delegate, __spreadArray([handler, timeout], __read(args)));\n    }\n    return setInterval.apply(void 0, __spreadArray([handler, timeout], __read(args)));\n  },\n  clearInterval: function (handle) {\n    var delegate = intervalProvider.delegate;\n    return ((delegate === null || delegate === void 0 ? void 0 : delegate.clearInterval) || clearInterval)(handle);\n  },\n  delegate: undefined\n};", "map": {"version": 3, "names": ["intervalProvider", "setInterval", "handler", "timeout", "args", "_i", "arguments", "length", "delegate", "apply", "__spread<PERSON><PERSON>y", "__read", "clearInterval", "handle", "undefined"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\scheduler\\intervalProvider.ts"], "sourcesContent": ["import type { TimerHandle } from './timerHandle';\ntype SetIntervalFunction = (handler: () => void, timeout?: number, ...args: any[]) => TimerHandle;\ntype ClearIntervalFunction = (handle: TimerHandle) => void;\n\ninterface IntervalProvider {\n  setInterval: SetIntervalFunction;\n  clearInterval: ClearIntervalFunction;\n  delegate:\n    | {\n        setInterval: SetIntervalFunction;\n        clearInterval: ClearIntervalFunction;\n      }\n    | undefined;\n}\n\nexport const intervalProvider: IntervalProvider = {\n  // When accessing the delegate, use the variable rather than `this` so that\n  // the functions can be called without being bound to the provider.\n  setInterval(handler: () => void, timeout?: number, ...args) {\n    const { delegate } = intervalProvider;\n    if (delegate?.setInterval) {\n      return delegate.setInterval(handler, timeout, ...args);\n    }\n    return setInterval(handler, timeout, ...args);\n  },\n  clearInterval(handle) {\n    const { delegate } = intervalProvider;\n    return (delegate?.clearInterval || clearInterval)(handle as any);\n  },\n  delegate: undefined,\n};\n"], "mappings": ";AAeA,OAAO,IAAMA,gBAAgB,GAAqB;EAGhDC,WAAW,EAAX,SAAAA,CAAYC,OAAmB,EAAEC,OAAgB;IAAE,IAAAC,IAAA;SAAA,IAAAC,EAAA,IAAO,EAAPA,EAAA,GAAAC,SAAA,CAAAC,MAAO,EAAPF,EAAA,EAAO;MAAPD,IAAA,CAAAC,EAAA,QAAAC,SAAA,CAAAD,EAAA;;IACzC,IAAAG,QAAQ,GAAKR,gBAAgB,CAAAQ,QAArB;IAChB,IAAIA,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEP,WAAW,EAAE;MACzB,OAAOO,QAAQ,CAACP,WAAW,CAAAQ,KAAA,CAApBD,QAAQ,EAAAE,aAAA,EAAaR,OAAO,EAAEC,OAAO,GAAAQ,MAAA,CAAKP,IAAI;;IAEvD,OAAOH,WAAW,CAAAQ,KAAA,SAAAC,aAAA,EAACR,OAAO,EAAEC,OAAO,GAAAQ,MAAA,CAAKP,IAAI;EAC9C,CAAC;EACDQ,aAAa,EAAb,SAAAA,CAAcC,MAAM;IACV,IAAAL,QAAQ,GAAKR,gBAAgB,CAAAQ,QAArB;IAChB,OAAO,CAAC,CAAAA,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEI,aAAa,KAAIA,aAAa,EAAEC,MAAa,CAAC;EAClE,CAAC;EACDL,QAAQ,EAAEM;CACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}