{"ast": null, "code": "var _jsxFileName = \"C:\\\\git\\\\Partner-Site\\\\BdoPartner.Plans\\\\Development\\\\ReactClientApp\\\\src\\\\core\\\\auth\\\\components\\\\silentRenew.jsx\";\n/* /src/components/auth/silentRenew.jsx */\n\nimport React from \"react\";\nimport { AuthConsumer } from \"./authProvider\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const SilentRenew = () => /*#__PURE__*/_jsxDEV(AuthConsumer, {\n  children: ({\n    signinSilentCallback\n  }) => {\n    signinSilentCallback();\n    return /*#__PURE__*/_jsxDEV(\"span\", {\n      children: \"loading\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 14\n    }, this);\n  }\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 8,\n  columnNumber: 3\n}, this);\n_c = SilentRenew;\nvar _c;\n$RefreshReg$(_c, \"SilentRenew\");", "map": {"version": 3, "names": ["React", "AuthConsumer", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON>", "children", "signinSilentCallback", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/core/auth/components/silentRenew.jsx"], "sourcesContent": ["/* /src/components/auth/silentRenew.jsx */\r\n\r\nimport React from \"react\";\r\n\r\nimport { AuthConsumer } from \"./authProvider\";\r\n\r\nexport const SilentRenew = () => (\r\n  <AuthConsumer>\r\n    {({ signinSilentCallback }) => {\r\n      signinSilentCallback();\r\n      return <span>loading</span>;\r\n    }}\r\n  </AuthConsumer>\r\n);\r\n"], "mappings": ";AAAA;;AAEA,OAAOA,KAAK,MAAM,OAAO;AAEzB,SAASC,YAAY,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,OAAO,MAAMC,WAAW,GAAGA,CAAA,kBACzBD,OAAA,CAACF,YAAY;EAAAI,QAAA,EACVA,CAAC;IAAEC;EAAqB,CAAC,KAAK;IAC7BA,oBAAoB,CAAC,CAAC;IACtB,oBAAOH,OAAA;MAAAE,QAAA,EAAM;IAAO;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAC7B;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACW,CACf;AAACC,EAAA,GAPWP,WAAW;AAAA,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}