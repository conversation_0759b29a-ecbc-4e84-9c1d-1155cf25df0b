{"ast": null, "code": "import http from \"../core/http/httpClient\";\nimport APP_CONFIG from \"../core/config/appConfig\";\nimport { ResultStatus } from \"../core/enumertions/resultStatus\";\nimport requestDeduplication from \"../core/http/requestDeduplication\";\n\n/**\r\n * Questionnaire Service for handling questionnaire API calls\r\n * Provides methods to manage questionnaires, including CRUD operations and publishing\r\n */\nclass QuestionnaireService {\n  /**\r\n   * Search questionnaires with filtering and pagination\r\n   * @param {string} searchTerm - Search term for filtering\r\n   * @param {number} year - Filter by year\r\n   * @param {number} status - Filter by status\r\n   * @param {boolean} isActive - Filter by active status\r\n   * @param {number} pageIndex - Page index (0-based, default: 0)\r\n   * @param {number} pageSize - Page size (default: 20)\r\n   * @returns {Promise<Object>} Paginated list of questionnaires with metadata\r\n   */\n  async searchQuestionnaires(searchTerm = null, year = null, status = null, isActive = null, pageIndex = 0, pageSize = 20) {\n    const params = new URLSearchParams();\n    if (searchTerm && searchTerm.trim()) {\n      params.append(\"searchTerm\", searchTerm.trim());\n    }\n    if (year !== null && year !== undefined) params.append(\"year\", year);\n    if (status !== null && status !== undefined) params.append(\"status\", status);\n    if (isActive !== null && isActive !== undefined) params.append(\"isActive\", isActive);\n    params.append(\"pageIndex\", pageIndex.toString());\n    params.append(\"pageSize\", pageSize.toString());\n    const queryString = params.toString();\n    const url = `${APP_CONFIG.apiDomain}/api/Questionnaire/SearchQuestionnaires${queryString ? `?${queryString}` : \"\"}`;\n\n    // Create unique key for request deduplication\n    const requestKey = requestDeduplication.createRequestKey(\"GET\", url, {});\n    return requestDeduplication.execute(requestKey, async () => {\n      try {\n        const response = await http.get(url);\n        if (response.data && response.data.resultStatus === ResultStatus.Success) {\n          const data = response.data.item;\n          // Handle IPagedList response structure\n          if (data && data.items && Array.isArray(data.items)) {\n            return {\n              items: data.items,\n              totalCount: data.totalCount || 0,\n              pageIndex: data.pageIndex || 0,\n              pageSize: data.pageSize || pageSize,\n              totalPages: data.totalPages || 0,\n              hasPreviousPage: data.hasPreviousPage || false,\n              hasNextPage: data.hasNextPage || false\n            };\n          } else {\n            return {\n              items: [],\n              totalCount: 0\n            };\n          }\n        } else {\n          var _response$data;\n          console.error(\"Failed to search questionnaires:\", (_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.message);\n          return {\n            items: [],\n            totalCount: 0\n          };\n        }\n      } catch (error) {\n        console.error(\"Error searching questionnaires:\", error);\n        return {\n          items: [],\n          totalCount: 0\n        };\n      }\n    });\n  }\n\n  /**\r\n   * Get questionnaire by ID\r\n   * @param {string} id - Questionnaire ID\r\n   * @returns {Promise<Object|null>} Questionnaire object or null if not found\r\n   */\n  async getQuestionnaireById(id) {\n    const url = `${APP_CONFIG.apiDomain}/api/Questionnaire/GetQuestionnaireById?id=${id}`;\n\n    // Create unique key for request deduplication\n    const requestKey = requestDeduplication.createRequestKey(\"GET\", url, {});\n    return requestDeduplication.execute(requestKey, async () => {\n      try {\n        const response = await http.get(url);\n        if (response.data && response.data.resultStatus === ResultStatus.Success) {\n          return response.data.item || null;\n        } else {\n          var _response$data2;\n          console.error(\"Failed to get questionnaire:\", (_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : _response$data2.message);\n          return null;\n        }\n      } catch (error) {\n        console.error(\"Error getting questionnaire:\", error);\n        return null;\n      }\n    });\n  }\n\n  /**\r\n   * Create a new questionnaire\r\n   * @param {Object} questionnaireData - Questionnaire data\r\n   * @param {string} questionnaireData.name - Questionnaire name\r\n   * @param {number} questionnaireData.year - Questionnaire year\r\n   * @param {number} questionnaireData.status - Status (0=Draft, 1=Published, 2=Archived)\r\n   * @param {boolean} questionnaireData.isActive - Active status\r\n   * @param {string} questionnaireData.draftDefinitionJson - Draft definition JSON\r\n   * @param {boolean} questionnaireData.acknowledgement - Acknowledgement required\r\n   * @param {string} questionnaireData.acknowledgementText - Acknowledgement text\r\n   * @param {boolean} questionnaireData.generalComments - General comments enabled\r\n   * @param {string} questionnaireData.generalCommentsText - General comments text\r\n   * @param {number} questionnaireData.formSystemVersion - Form system version\r\n   * @returns {Promise<Object>} Created questionnaire object\r\n   */\n  async createQuestionnaire(questionnaireData) {\n    try {\n      const response = await http.post(`${APP_CONFIG.apiDomain}/api/Questionnaire/CreateQuestionnaire`, questionnaireData);\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\n        return response.data.item || {};\n      } else {\n        var _response$data3;\n        throw new Error(((_response$data3 = response.data) === null || _response$data3 === void 0 ? void 0 : _response$data3.message) || \"Failed to create questionnaire\");\n      }\n    } catch (error) {\n      console.error(\"Error creating questionnaire:\", error);\n      throw error;\n    }\n  }\n\n  /**\r\n   * Update an existing questionnaire\r\n   * @param {Object} questionnaireData - Updated questionnaire data\r\n   * @returns {Promise<Object>} Updated questionnaire object\r\n   */\n  async updateQuestionnaire(questionnaireData) {\n    try {\n      const response = await http.post(`${APP_CONFIG.apiDomain}/api/Questionnaire/UpdateQuestionnaire`, questionnaireData);\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\n        return response.data.item || {};\n      } else {\n        var _response$data4;\n        throw new Error(((_response$data4 = response.data) === null || _response$data4 === void 0 ? void 0 : _response$data4.message) || \"Failed to update questionnaire\");\n      }\n    } catch (error) {\n      console.error(\"Error updating questionnaire:\", error);\n      throw error;\n    }\n  }\n\n  /**\r\n   * Check if there's already a published questionnaire in the specified year\r\n   * @param {number} year - Year to check\r\n   * @param {string} excludeId - Optional questionnaire ID to exclude from the check\r\n   * @returns {Promise<boolean>} True if a published questionnaire exists in the year\r\n   */\n  async hasPublishedQuestionnaireInYear(year, excludeId = null) {\n    try {\n      const params = new URLSearchParams({\n        year: year.toString()\n      });\n      if (excludeId) {\n        params.append(\"excludeId\", excludeId);\n      }\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/Questionnaire/HasPublishedQuestionnaireInYear?${params}`);\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\n        return response.data.item || false;\n      } else {\n        var _response$data5;\n        throw new Error(((_response$data5 = response.data) === null || _response$data5 === void 0 ? void 0 : _response$data5.message) || \"Failed to check for published questionnaire\");\n      }\n    } catch (error) {\n      console.error(\"Error checking for published questionnaire:\", error);\n      throw error;\n    }\n  }\n\n  /**\r\n   * Check if there's already an active questionnaire in the specified year\r\n   * @param {number} year - Year to check\r\n   * @param {string} excludeId - Optional questionnaire ID to exclude from the check\r\n   * @returns {Promise<boolean>} True if an active questionnaire exists in the year\r\n   */\n  async hasActiveQuestionnaireInYear(year, excludeId = null) {\n    try {\n      const params = new URLSearchParams({\n        year: year.toString()\n      });\n      if (excludeId) {\n        params.append(\"excludeId\", excludeId);\n      }\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/Questionnaire/HasActiveQuestionnaireInYear?${params}`);\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\n        return response.data.item || false;\n      } else {\n        var _response$data6;\n        throw new Error(((_response$data6 = response.data) === null || _response$data6 === void 0 ? void 0 : _response$data6.message) || \"Failed to check for active questionnaire\");\n      }\n    } catch (error) {\n      console.error(\"Error checking for active questionnaire:\", error);\n      throw error;\n    }\n  }\n\n  /**\r\n   * Publish a questionnaire by ID\r\n   * @param {string} questionnaireId - Questionnaire ID to publish\r\n   * @returns {Promise<Object>} Published questionnaire object\r\n   */\n  async publishQuestionnaire(questionnaireId) {\n    try {\n      const response = await http.post(`${APP_CONFIG.apiDomain}/api/Questionnaire/PublishQuestionnaire/${questionnaireId}`);\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\n        return response.data.item || {};\n      } else {\n        var _response$data7;\n        throw new Error(((_response$data7 = response.data) === null || _response$data7 === void 0 ? void 0 : _response$data7.message) || \"Failed to publish questionnaire\");\n      }\n    } catch (error) {\n      console.error(\"Error publishing questionnaire:\", error);\n      throw error;\n    }\n  }\n\n  /**\r\n   * Save questionnaire as draft (convenience method for updating with draft status)\r\n   * @param {Object} questionnaire - Questionnaire object\r\n   * @param {string} surveyName - Survey name\r\n   * @param {number} selectedYear - Selected year\r\n   * @param {string} definitionJson - Survey definition JSON\r\n   * @param {string} enableCycles - Comma-separated cycle values\r\n   * @returns {Promise<Object>} Updated questionnaire object\r\n   */\n  async saveDraft(questionnaire, surveyName, selectedYear, definitionJson, enableCycles = null) {\n    try {\n      const updateData = {\n        ...questionnaire,\n        name: surveyName,\n        year: selectedYear,\n        draftDefinitionJson: definitionJson,\n        enableCycles: enableCycles\n      };\n      return await this.updateQuestionnaire(updateData);\n    } catch (error) {\n      console.error(\"Error saving draft:\", error);\n      throw error;\n    }\n  }\n\n  /**\r\n   * Publish questionnaire with updated definition (convenience method)\r\n   * @param {Object} questionnaire - Questionnaire object\r\n   * @param {string} surveyName - Survey name\r\n   * @param {number} selectedYear - Selected year\r\n   * @param {string} definitionJson - Survey definition JSON\r\n   * @param {string} enableCycles - Comma-separated cycle values\r\n   * @returns {Promise<Object>} Published questionnaire object\r\n   */\n  async publishWithDefinition(questionnaire, surveyName, selectedYear, definitionJson, enableCycles = null) {\n    try {\n      const updateData = {\n        ...questionnaire,\n        name: surveyName,\n        year: selectedYear,\n        draftDefinitionJson: definitionJson,\n        definitionJson: definitionJson,\n        // Also update the published definition\n        status: 1,\n        // Published status\n        enableCycles: enableCycles\n      };\n      return await this.updateQuestionnaire(updateData);\n    } catch (error) {\n      console.error(\"Error publishing questionnaire:\", error);\n      throw error;\n    }\n  }\n\n  /**\r\n   * Validate if a questionnaire can be deleted\r\n   * @param {string} questionnaireId - Questionnaire ID to validate\r\n   * @returns {Promise<Object>} Validation result\r\n   */\n  async validateQuestionnaireForDeletion(questionnaireId) {\n    try {\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/Questionnaire/ValidateQuestionnaireForDeletion?id=${questionnaireId}`);\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\n        return response.data.item || {};\n      } else {\n        var _response$data8;\n        throw new Error(((_response$data8 = response.data) === null || _response$data8 === void 0 ? void 0 : _response$data8.message) || \"Failed to validate questionnaire for deletion\");\n      }\n    } catch (error) {\n      console.error(\"Error validating questionnaire for deletion:\", error);\n      throw error;\n    }\n  }\n\n  /**\r\n   * Get count of uncompleted forms that will be updated when publishing a cycle\r\n   * @param {string} questionnaireId - Questionnaire ID\r\n   * @param {number} cycleValue - Cycle value to publish (0=Planning, 1=MidYearReview, 2=EndYearReview)\r\n   * @returns {Promise<Object>} Summary of uncompleted forms for each cycle\r\n   */\n  async getUncompletedFormsCount(questionnaireId, cycleValue) {\n    try {\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/Questionnaire/GetUncompletedFormsCount/${questionnaireId}/${cycleValue}`);\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\n        return response.data.item || {\n          planningInCompleted: 0,\n          midYearReviewInCompleted: 0,\n          yearEndReviewInCompleted: 0\n        };\n      } else {\n        var _response$data9;\n        throw new Error(((_response$data9 = response.data) === null || _response$data9 === void 0 ? void 0 : _response$data9.message) || \"Failed to get uncompleted forms count\");\n      }\n    } catch (error) {\n      console.error(\"Error getting uncompleted forms count:\", error);\n      throw error;\n    }\n  }\n\n  /**\r\n   * Publish a specific cycle for a questionnaire\r\n   * @param {string} questionnaireId - Questionnaire ID\r\n   * @param {number} cycleValue - Cycle value to publish (0=Planning, 1=MidYearReview, 2=EndYearReview)\r\n   * @returns {Promise<Object>} Publish cycle result with enabled cycles and updated forms count\r\n   */\n  async publishCycle(questionnaireId, cycleValue) {\n    try {\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/Questionnaire/PublishCycle/${questionnaireId}/${cycleValue}`);\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\n        return response.data.item || {};\n      } else {\n        var _response$data0;\n        throw new Error(((_response$data0 = response.data) === null || _response$data0 === void 0 ? void 0 : _response$data0.message) || \"Failed to publish cycle\");\n      }\n    } catch (error) {\n      console.error(\"Error publishing cycle:\", error);\n      throw error;\n    }\n  }\n\n  /**\r\n   * Close a questionnaire (change status to Closed)\r\n   * Can only be done after EndYear cycle is published\r\n   * @param {string} questionnaireId - Questionnaire ID\r\n   * @returns {Promise<void>} Promise that resolves when questionnaire is closed\r\n   */\n  async closeQuestionnaire(questionnaireId) {\n    try {\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/Questionnaire/CloseQuestionnaire/${questionnaireId}`);\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\n        return; // Success, no return value needed\n      } else {\n        var _response$data1;\n        throw new Error(((_response$data1 = response.data) === null || _response$data1 === void 0 ? void 0 : _response$data1.message) || \"Failed to close questionnaire\");\n      }\n    } catch (error) {\n      console.error(\"Error closing questionnaire:\", error);\n      throw error;\n    }\n  }\n\n  /**\r\n   * Delete a questionnaire by ID\r\n   * @param {string} questionnaireId - Questionnaire ID to delete\r\n   * @returns {Promise<Object>} Success result\r\n   */\n  async deleteQuestionnaire(questionnaireId) {\n    try {\n      const response = await http.post(`${APP_CONFIG.apiDomain}/api/Questionnaire/DeleteQuestionnaire?id=${questionnaireId}`);\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\n        return response.data.item || {};\n      } else {\n        var _response$data10;\n        throw new Error(((_response$data10 = response.data) === null || _response$data10 === void 0 ? void 0 : _response$data10.message) || \"Failed to delete questionnaire\");\n      }\n    } catch (error) {\n      console.error(\"Error deleting questionnaire:\", error);\n      throw error;\n    }\n  }\n\n  /**\r\n   * Get all questionnaires (without pagination)\r\n   * @returns {Promise<Array>} List of all questionnaires\r\n   */\n  async getAllQuestionnaires() {\n    try {\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/Questionnaire/GetAllQuestionnaires`);\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\n        return response.data.item || [];\n      } else {\n        var _response$data11;\n        console.error(\"Failed to get all questionnaires:\", (_response$data11 = response.data) === null || _response$data11 === void 0 ? void 0 : _response$data11.message);\n        return [];\n      }\n    } catch (error) {\n      console.error(\"Error getting all questionnaires:\", error);\n      return [];\n    }\n  }\n\n  /**\r\n   * Unpublish a questionnaire by ID (change status to Draft)\r\n   * @param {string} questionnaireId - Questionnaire ID to unpublish\r\n   * @returns {Promise<Object>} Unpublished questionnaire object\r\n   */\n  async unpublishQuestionnaire(questionnaireId) {\n    try {\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/Questionnaire/UnpublishQuestionnaire/${questionnaireId}`);\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\n        return response.data.item || {};\n      } else {\n        var _response$data12;\n        throw new Error(((_response$data12 = response.data) === null || _response$data12 === void 0 ? void 0 : _response$data12.message) || \"Failed to unpublish questionnaire\");\n      }\n    } catch (error) {\n      console.error(\"Error unpublishing questionnaire:\", error);\n      throw error;\n    }\n  }\n\n  /**\r\n   * Copy a questionnaire to a new year with Draft status\r\n   * @param {string} sourceId - Source questionnaire ID to copy from\r\n   * @param {number} targetYear - Target year for the new questionnaire\r\n   * @returns {Promise<Object>} Newly created questionnaire object\r\n   */\n  async copyQuestionnaire(sourceId, targetYear) {\n    try {\n      const response = await http.post(`${APP_CONFIG.apiDomain}/api/Questionnaire/CopyQuestionnaire`, null, {\n        params: {\n          sourceId: sourceId,\n          targetYear: targetYear\n        }\n      });\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\n        return response.data.item || {};\n      } else {\n        var _response$data13;\n        throw new Error(((_response$data13 = response.data) === null || _response$data13 === void 0 ? void 0 : _response$data13.message) || \"Failed to copy questionnaire\");\n      }\n    } catch (error) {\n      console.error(\"Error copying questionnaire:\", error);\n      throw error;\n    }\n  }\n\n  /**\r\n   * Get count of completed forms for a specific questionnaire and cycle\r\n   * @param {string} questionnaireId - Questionnaire ID\r\n   * @param {number} cycle - Cycle to check (0=Planning, 1=MidYear, 2=YearEnd)\r\n   * @returns {Promise<number>} Count of completed forms\r\n   */\n  async getCompletedFormsCount(questionnaireId, cycle) {\n    try {\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/Questionnaire/GetCompletedFormsCount/${questionnaireId}/${cycle}`);\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\n        return response.data.item || 0;\n      } else {\n        var _response$data14;\n        throw new Error(((_response$data14 = response.data) === null || _response$data14 === void 0 ? void 0 : _response$data14.message) || \"Failed to get completed forms count\");\n      }\n    } catch (error) {\n      console.error(\"Error getting completed forms count:\", error);\n      throw error;\n    }\n  }\n\n  /**\r\n   * Process batch reopen of completed forms and republish questionnaire\r\n   * @param {Object} request - Batch reopen and republish request\r\n   * @param {string} request.questionnaireId - Questionnaire ID\r\n   * @param {string} request.surveyName - Survey name\r\n   * @param {number} request.selectedYear - Selected year\r\n   * @param {string} request.definitionJson - Survey definition JSON\r\n   * @param {string} request.enableCycles - Comma-separated cycle values\r\n   * @param {number} request.latestEnabledCycle - Latest enabled cycle\r\n   * @param {string} request.action - Action to perform\r\n   * @returns {Promise<Object>} Result with processed count and success status\r\n   */\n  async processBatchReopenAndRepublish(request) {\n    try {\n      const response = await http.post(`${APP_CONFIG.apiDomain}/api/Questionnaire/ProcessBatchReopenAndRepublish`, request);\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\n        return response.data.item;\n      } else {\n        var _response$data15;\n        throw new Error(((_response$data15 = response.data) === null || _response$data15 === void 0 ? void 0 : _response$data15.message) || \"Failed to process batch reopen and republish\");\n      }\n    } catch (error) {\n      console.error(\"Error processing batch reopen and republish:\", error);\n      throw error;\n    }\n  }\n}\n\n// Export singleton instance\nconst questionnaireService = new QuestionnaireService();\nexport default questionnaireService;", "map": {"version": 3, "names": ["http", "APP_CONFIG", "ResultStatus", "requestDeduplication", "QuestionnaireService", "searchQuestionnaires", "searchTerm", "year", "status", "isActive", "pageIndex", "pageSize", "params", "URLSearchParams", "trim", "append", "undefined", "toString", "queryString", "url", "apiDomain", "request<PERSON>ey", "createRequestKey", "execute", "response", "get", "data", "resultStatus", "Success", "item", "items", "Array", "isArray", "totalCount", "totalPages", "hasPreviousPage", "hasNextPage", "_response$data", "console", "error", "message", "getQuestionnaireById", "id", "_response$data2", "createQuestionnaire", "questionnaireData", "post", "_response$data3", "Error", "updateQuestionnaire", "_response$data4", "hasPublishedQuestionnaireInYear", "excludeId", "_response$data5", "hasActiveQuestionnaireInYear", "_response$data6", "publishQuestionnaire", "questionnaireId", "_response$data7", "saveDraft", "questionnaire", "surveyName", "selected<PERSON>ear", "definitionJson", "enableCycles", "updateData", "name", "draftDefinitionJson", "publishWithDefinition", "validateQuestionnaireForDeletion", "_response$data8", "getUncompletedFormsCount", "cycleValue", "planningInCompleted", "midYearReviewInCompleted", "yearEndReviewInCompleted", "_response$data9", "publishCycle", "_response$data0", "closeQuestionnaire", "_response$data1", "deleteQuestionnaire", "_response$data10", "getAllQuestionnaires", "_response$data11", "unpublishQuestionnaire", "_response$data12", "copyQuestionnaire", "sourceId", "targetYear", "_response$data13", "getCompletedFormsCount", "cycle", "_response$data14", "processBatchReopenAndRepublish", "request", "_response$data15", "questionnaireService"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/services/questionnaireService.js"], "sourcesContent": ["import http from \"../core/http/httpClient\";\r\nimport APP_CONFIG from \"../core/config/appConfig\";\r\nimport { ResultStatus } from \"../core/enumertions/resultStatus\";\r\nimport requestDeduplication from \"../core/http/requestDeduplication\";\r\n\r\n/**\r\n * Questionnaire Service for handling questionnaire API calls\r\n * Provides methods to manage questionnaires, including CRUD operations and publishing\r\n */\r\nclass QuestionnaireService {\r\n  /**\r\n   * Search questionnaires with filtering and pagination\r\n   * @param {string} searchTerm - Search term for filtering\r\n   * @param {number} year - Filter by year\r\n   * @param {number} status - Filter by status\r\n   * @param {boolean} isActive - Filter by active status\r\n   * @param {number} pageIndex - Page index (0-based, default: 0)\r\n   * @param {number} pageSize - Page size (default: 20)\r\n   * @returns {Promise<Object>} Paginated list of questionnaires with metadata\r\n   */\r\n  async searchQuestionnaires(searchTerm = null, year = null, status = null, isActive = null, pageIndex = 0, pageSize = 20) {\r\n    const params = new URLSearchParams();\r\n    if (searchTerm && searchTerm.trim()) {\r\n      params.append(\"searchTerm\", searchTerm.trim());\r\n    }\r\n    if (year !== null && year !== undefined) params.append(\"year\", year);\r\n    if (status !== null && status !== undefined) params.append(\"status\", status);\r\n    if (isActive !== null && isActive !== undefined) params.append(\"isActive\", isActive);\r\n    params.append(\"pageIndex\", pageIndex.toString());\r\n    params.append(\"pageSize\", pageSize.toString());\r\n\r\n    const queryString = params.toString();\r\n    const url = `${APP_CONFIG.apiDomain}/api/Questionnaire/SearchQuestionnaires${queryString ? `?${queryString}` : \"\"}`;\r\n\r\n    // Create unique key for request deduplication\r\n    const requestKey = requestDeduplication.createRequestKey(\"GET\", url, {});\r\n\r\n    return requestDeduplication.execute(requestKey, async () => {\r\n      try {\r\n        const response = await http.get(url);\r\n\r\n        if (response.data && response.data.resultStatus === ResultStatus.Success) {\r\n          const data = response.data.item;\r\n          // Handle IPagedList response structure\r\n          if (data && data.items && Array.isArray(data.items)) {\r\n            return {\r\n              items: data.items,\r\n              totalCount: data.totalCount || 0,\r\n              pageIndex: data.pageIndex || 0,\r\n              pageSize: data.pageSize || pageSize,\r\n              totalPages: data.totalPages || 0,\r\n              hasPreviousPage: data.hasPreviousPage || false,\r\n              hasNextPage: data.hasNextPage || false,\r\n            };\r\n          } else {\r\n            return { items: [], totalCount: 0 };\r\n          }\r\n        } else {\r\n          console.error(\"Failed to search questionnaires:\", response.data?.message);\r\n          return { items: [], totalCount: 0 };\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error searching questionnaires:\", error);\r\n        return { items: [], totalCount: 0 };\r\n      }\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Get questionnaire by ID\r\n   * @param {string} id - Questionnaire ID\r\n   * @returns {Promise<Object|null>} Questionnaire object or null if not found\r\n   */\r\n  async getQuestionnaireById(id) {\r\n    const url = `${APP_CONFIG.apiDomain}/api/Questionnaire/GetQuestionnaireById?id=${id}`;\r\n\r\n    // Create unique key for request deduplication\r\n    const requestKey = requestDeduplication.createRequestKey(\"GET\", url, {});\r\n\r\n    return requestDeduplication.execute(requestKey, async () => {\r\n      try {\r\n        const response = await http.get(url);\r\n\r\n        if (response.data && response.data.resultStatus === ResultStatus.Success) {\r\n          return response.data.item || null;\r\n        } else {\r\n          console.error(\"Failed to get questionnaire:\", response.data?.message);\r\n          return null;\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error getting questionnaire:\", error);\r\n        return null;\r\n      }\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Create a new questionnaire\r\n   * @param {Object} questionnaireData - Questionnaire data\r\n   * @param {string} questionnaireData.name - Questionnaire name\r\n   * @param {number} questionnaireData.year - Questionnaire year\r\n   * @param {number} questionnaireData.status - Status (0=Draft, 1=Published, 2=Archived)\r\n   * @param {boolean} questionnaireData.isActive - Active status\r\n   * @param {string} questionnaireData.draftDefinitionJson - Draft definition JSON\r\n   * @param {boolean} questionnaireData.acknowledgement - Acknowledgement required\r\n   * @param {string} questionnaireData.acknowledgementText - Acknowledgement text\r\n   * @param {boolean} questionnaireData.generalComments - General comments enabled\r\n   * @param {string} questionnaireData.generalCommentsText - General comments text\r\n   * @param {number} questionnaireData.formSystemVersion - Form system version\r\n   * @returns {Promise<Object>} Created questionnaire object\r\n   */\r\n  async createQuestionnaire(questionnaireData) {\r\n    try {\r\n      const response = await http.post(`${APP_CONFIG.apiDomain}/api/Questionnaire/CreateQuestionnaire`, questionnaireData);\r\n\r\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\r\n        return response.data.item || {};\r\n      } else {\r\n        throw new Error(response.data?.message || \"Failed to create questionnaire\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error creating questionnaire:\", error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Update an existing questionnaire\r\n   * @param {Object} questionnaireData - Updated questionnaire data\r\n   * @returns {Promise<Object>} Updated questionnaire object\r\n   */\r\n  async updateQuestionnaire(questionnaireData) {\r\n    try {\r\n      const response = await http.post(`${APP_CONFIG.apiDomain}/api/Questionnaire/UpdateQuestionnaire`, questionnaireData);\r\n\r\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\r\n        return response.data.item || {};\r\n      } else {\r\n        throw new Error(response.data?.message || \"Failed to update questionnaire\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error updating questionnaire:\", error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Check if there's already a published questionnaire in the specified year\r\n   * @param {number} year - Year to check\r\n   * @param {string} excludeId - Optional questionnaire ID to exclude from the check\r\n   * @returns {Promise<boolean>} True if a published questionnaire exists in the year\r\n   */\r\n  async hasPublishedQuestionnaireInYear(year, excludeId = null) {\r\n    try {\r\n      const params = new URLSearchParams({ year: year.toString() });\r\n      if (excludeId) {\r\n        params.append(\"excludeId\", excludeId);\r\n      }\r\n\r\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/Questionnaire/HasPublishedQuestionnaireInYear?${params}`);\r\n\r\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\r\n        return response.data.item || false;\r\n      } else {\r\n        throw new Error(response.data?.message || \"Failed to check for published questionnaire\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error checking for published questionnaire:\", error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Check if there's already an active questionnaire in the specified year\r\n   * @param {number} year - Year to check\r\n   * @param {string} excludeId - Optional questionnaire ID to exclude from the check\r\n   * @returns {Promise<boolean>} True if an active questionnaire exists in the year\r\n   */\r\n  async hasActiveQuestionnaireInYear(year, excludeId = null) {\r\n    try {\r\n      const params = new URLSearchParams({ year: year.toString() });\r\n      if (excludeId) {\r\n        params.append(\"excludeId\", excludeId);\r\n      }\r\n\r\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/Questionnaire/HasActiveQuestionnaireInYear?${params}`);\r\n\r\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\r\n        return response.data.item || false;\r\n      } else {\r\n        throw new Error(response.data?.message || \"Failed to check for active questionnaire\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error checking for active questionnaire:\", error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Publish a questionnaire by ID\r\n   * @param {string} questionnaireId - Questionnaire ID to publish\r\n   * @returns {Promise<Object>} Published questionnaire object\r\n   */\r\n  async publishQuestionnaire(questionnaireId) {\r\n    try {\r\n      const response = await http.post(`${APP_CONFIG.apiDomain}/api/Questionnaire/PublishQuestionnaire/${questionnaireId}`);\r\n\r\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\r\n        return response.data.item || {};\r\n      } else {\r\n        throw new Error(response.data?.message || \"Failed to publish questionnaire\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error publishing questionnaire:\", error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Save questionnaire as draft (convenience method for updating with draft status)\r\n   * @param {Object} questionnaire - Questionnaire object\r\n   * @param {string} surveyName - Survey name\r\n   * @param {number} selectedYear - Selected year\r\n   * @param {string} definitionJson - Survey definition JSON\r\n   * @param {string} enableCycles - Comma-separated cycle values\r\n   * @returns {Promise<Object>} Updated questionnaire object\r\n   */\r\n  async saveDraft(questionnaire, surveyName, selectedYear, definitionJson, enableCycles = null) {\r\n    try {\r\n      const updateData = {\r\n        ...questionnaire,\r\n        name: surveyName,\r\n        year: selectedYear,\r\n        draftDefinitionJson: definitionJson,\r\n        enableCycles: enableCycles,\r\n      };\r\n\r\n      return await this.updateQuestionnaire(updateData);\r\n    } catch (error) {\r\n      console.error(\"Error saving draft:\", error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Publish questionnaire with updated definition (convenience method)\r\n   * @param {Object} questionnaire - Questionnaire object\r\n   * @param {string} surveyName - Survey name\r\n   * @param {number} selectedYear - Selected year\r\n   * @param {string} definitionJson - Survey definition JSON\r\n   * @param {string} enableCycles - Comma-separated cycle values\r\n   * @returns {Promise<Object>} Published questionnaire object\r\n   */\r\n  async publishWithDefinition(questionnaire, surveyName, selectedYear, definitionJson, enableCycles = null) {\r\n    try {\r\n      const updateData = {\r\n        ...questionnaire,\r\n        name: surveyName,\r\n        year: selectedYear,\r\n        draftDefinitionJson: definitionJson,\r\n        definitionJson: definitionJson, // Also update the published definition\r\n        status: 1, // Published status\r\n        enableCycles: enableCycles,\r\n      };\r\n\r\n      return await this.updateQuestionnaire(updateData);\r\n    } catch (error) {\r\n      console.error(\"Error publishing questionnaire:\", error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Validate if a questionnaire can be deleted\r\n   * @param {string} questionnaireId - Questionnaire ID to validate\r\n   * @returns {Promise<Object>} Validation result\r\n   */\r\n  async validateQuestionnaireForDeletion(questionnaireId) {\r\n    try {\r\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/Questionnaire/ValidateQuestionnaireForDeletion?id=${questionnaireId}`);\r\n\r\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\r\n        return response.data.item || {};\r\n      } else {\r\n        throw new Error(response.data?.message || \"Failed to validate questionnaire for deletion\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error validating questionnaire for deletion:\", error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get count of uncompleted forms that will be updated when publishing a cycle\r\n   * @param {string} questionnaireId - Questionnaire ID\r\n   * @param {number} cycleValue - Cycle value to publish (0=Planning, 1=MidYearReview, 2=EndYearReview)\r\n   * @returns {Promise<Object>} Summary of uncompleted forms for each cycle\r\n   */\r\n  async getUncompletedFormsCount(questionnaireId, cycleValue) {\r\n    try {\r\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/Questionnaire/GetUncompletedFormsCount/${questionnaireId}/${cycleValue}`);\r\n\r\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\r\n        return response.data.item || { planningInCompleted: 0, midYearReviewInCompleted: 0, yearEndReviewInCompleted: 0 };\r\n      } else {\r\n        throw new Error(response.data?.message || \"Failed to get uncompleted forms count\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error getting uncompleted forms count:\", error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Publish a specific cycle for a questionnaire\r\n   * @param {string} questionnaireId - Questionnaire ID\r\n   * @param {number} cycleValue - Cycle value to publish (0=Planning, 1=MidYearReview, 2=EndYearReview)\r\n   * @returns {Promise<Object>} Publish cycle result with enabled cycles and updated forms count\r\n   */\r\n  async publishCycle(questionnaireId, cycleValue) {\r\n    try {\r\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/Questionnaire/PublishCycle/${questionnaireId}/${cycleValue}`);\r\n\r\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\r\n        return response.data.item || {};\r\n      } else {\r\n        throw new Error(response.data?.message || \"Failed to publish cycle\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error publishing cycle:\", error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Close a questionnaire (change status to Closed)\r\n   * Can only be done after EndYear cycle is published\r\n   * @param {string} questionnaireId - Questionnaire ID\r\n   * @returns {Promise<void>} Promise that resolves when questionnaire is closed\r\n   */\r\n  async closeQuestionnaire(questionnaireId) {\r\n    try {\r\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/Questionnaire/CloseQuestionnaire/${questionnaireId}`);\r\n\r\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\r\n        return; // Success, no return value needed\r\n      } else {\r\n        throw new Error(response.data?.message || \"Failed to close questionnaire\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error closing questionnaire:\", error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Delete a questionnaire by ID\r\n   * @param {string} questionnaireId - Questionnaire ID to delete\r\n   * @returns {Promise<Object>} Success result\r\n   */\r\n  async deleteQuestionnaire(questionnaireId) {\r\n    try {\r\n      const response = await http.post(`${APP_CONFIG.apiDomain}/api/Questionnaire/DeleteQuestionnaire?id=${questionnaireId}`);\r\n\r\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\r\n        return response.data.item || {};\r\n      } else {\r\n        throw new Error(response.data?.message || \"Failed to delete questionnaire\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error deleting questionnaire:\", error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get all questionnaires (without pagination)\r\n   * @returns {Promise<Array>} List of all questionnaires\r\n   */\r\n  async getAllQuestionnaires() {\r\n    try {\r\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/Questionnaire/GetAllQuestionnaires`);\r\n\r\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\r\n        return response.data.item || [];\r\n      } else {\r\n        console.error(\"Failed to get all questionnaires:\", response.data?.message);\r\n        return [];\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error getting all questionnaires:\", error);\r\n      return [];\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Unpublish a questionnaire by ID (change status to Draft)\r\n   * @param {string} questionnaireId - Questionnaire ID to unpublish\r\n   * @returns {Promise<Object>} Unpublished questionnaire object\r\n   */\r\n  async unpublishQuestionnaire(questionnaireId) {\r\n    try {\r\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/Questionnaire/UnpublishQuestionnaire/${questionnaireId}`);\r\n\r\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\r\n        return response.data.item || {};\r\n      } else {\r\n        throw new Error(response.data?.message || \"Failed to unpublish questionnaire\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error unpublishing questionnaire:\", error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Copy a questionnaire to a new year with Draft status\r\n   * @param {string} sourceId - Source questionnaire ID to copy from\r\n   * @param {number} targetYear - Target year for the new questionnaire\r\n   * @returns {Promise<Object>} Newly created questionnaire object\r\n   */\r\n  async copyQuestionnaire(sourceId, targetYear) {\r\n    try {\r\n      const response = await http.post(`${APP_CONFIG.apiDomain}/api/Questionnaire/CopyQuestionnaire`, null, {\r\n        params: {\r\n          sourceId: sourceId,\r\n          targetYear: targetYear,\r\n        },\r\n      });\r\n\r\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\r\n        return response.data.item || {};\r\n      } else {\r\n        throw new Error(response.data?.message || \"Failed to copy questionnaire\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error copying questionnaire:\", error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get count of completed forms for a specific questionnaire and cycle\r\n   * @param {string} questionnaireId - Questionnaire ID\r\n   * @param {number} cycle - Cycle to check (0=Planning, 1=MidYear, 2=YearEnd)\r\n   * @returns {Promise<number>} Count of completed forms\r\n   */\r\n  async getCompletedFormsCount(questionnaireId, cycle) {\r\n    try {\r\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/Questionnaire/GetCompletedFormsCount/${questionnaireId}/${cycle}`);\r\n\r\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\r\n        return response.data.item || 0;\r\n      } else {\r\n        throw new Error(response.data?.message || \"Failed to get completed forms count\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error getting completed forms count:\", error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Process batch reopen of completed forms and republish questionnaire\r\n   * @param {Object} request - Batch reopen and republish request\r\n   * @param {string} request.questionnaireId - Questionnaire ID\r\n   * @param {string} request.surveyName - Survey name\r\n   * @param {number} request.selectedYear - Selected year\r\n   * @param {string} request.definitionJson - Survey definition JSON\r\n   * @param {string} request.enableCycles - Comma-separated cycle values\r\n   * @param {number} request.latestEnabledCycle - Latest enabled cycle\r\n   * @param {string} request.action - Action to perform\r\n   * @returns {Promise<Object>} Result with processed count and success status\r\n   */\r\n  async processBatchReopenAndRepublish(request) {\r\n    try {\r\n      const response = await http.post(`${APP_CONFIG.apiDomain}/api/Questionnaire/ProcessBatchReopenAndRepublish`, request);\r\n\r\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\r\n        return response.data.item;\r\n      } else {\r\n        throw new Error(response.data?.message || \"Failed to process batch reopen and republish\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error processing batch reopen and republish:\", error);\r\n      throw error;\r\n    }\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nconst questionnaireService = new QuestionnaireService();\r\nexport default questionnaireService;\r\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,yBAAyB;AAC1C,OAAOC,UAAU,MAAM,0BAA0B;AACjD,SAASC,YAAY,QAAQ,kCAAkC;AAC/D,OAAOC,oBAAoB,MAAM,mCAAmC;;AAEpE;AACA;AACA;AACA;AACA,MAAMC,oBAAoB,CAAC;EACzB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,MAAMC,oBAAoBA,CAACC,UAAU,GAAG,IAAI,EAAEC,IAAI,GAAG,IAAI,EAAEC,MAAM,GAAG,IAAI,EAAEC,QAAQ,GAAG,IAAI,EAAEC,SAAS,GAAG,CAAC,EAAEC,QAAQ,GAAG,EAAE,EAAE;IACvH,MAAMC,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;IACpC,IAAIP,UAAU,IAAIA,UAAU,CAACQ,IAAI,CAAC,CAAC,EAAE;MACnCF,MAAM,CAACG,MAAM,CAAC,YAAY,EAAET,UAAU,CAACQ,IAAI,CAAC,CAAC,CAAC;IAChD;IACA,IAAIP,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAKS,SAAS,EAAEJ,MAAM,CAACG,MAAM,CAAC,MAAM,EAAER,IAAI,CAAC;IACpE,IAAIC,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAKQ,SAAS,EAAEJ,MAAM,CAACG,MAAM,CAAC,QAAQ,EAAEP,MAAM,CAAC;IAC5E,IAAIC,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAKO,SAAS,EAAEJ,MAAM,CAACG,MAAM,CAAC,UAAU,EAAEN,QAAQ,CAAC;IACpFG,MAAM,CAACG,MAAM,CAAC,WAAW,EAAEL,SAAS,CAACO,QAAQ,CAAC,CAAC,CAAC;IAChDL,MAAM,CAACG,MAAM,CAAC,UAAU,EAAEJ,QAAQ,CAACM,QAAQ,CAAC,CAAC,CAAC;IAE9C,MAAMC,WAAW,GAAGN,MAAM,CAACK,QAAQ,CAAC,CAAC;IACrC,MAAME,GAAG,GAAG,GAAGlB,UAAU,CAACmB,SAAS,0CAA0CF,WAAW,GAAG,IAAIA,WAAW,EAAE,GAAG,EAAE,EAAE;;IAEnH;IACA,MAAMG,UAAU,GAAGlB,oBAAoB,CAACmB,gBAAgB,CAAC,KAAK,EAAEH,GAAG,EAAE,CAAC,CAAC,CAAC;IAExE,OAAOhB,oBAAoB,CAACoB,OAAO,CAACF,UAAU,EAAE,YAAY;MAC1D,IAAI;QACF,MAAMG,QAAQ,GAAG,MAAMxB,IAAI,CAACyB,GAAG,CAACN,GAAG,CAAC;QAEpC,IAAIK,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACC,YAAY,KAAKzB,YAAY,CAAC0B,OAAO,EAAE;UACxE,MAAMF,IAAI,GAAGF,QAAQ,CAACE,IAAI,CAACG,IAAI;UAC/B;UACA,IAAIH,IAAI,IAAIA,IAAI,CAACI,KAAK,IAAIC,KAAK,CAACC,OAAO,CAACN,IAAI,CAACI,KAAK,CAAC,EAAE;YACnD,OAAO;cACLA,KAAK,EAAEJ,IAAI,CAACI,KAAK;cACjBG,UAAU,EAAEP,IAAI,CAACO,UAAU,IAAI,CAAC;cAChCvB,SAAS,EAAEgB,IAAI,CAAChB,SAAS,IAAI,CAAC;cAC9BC,QAAQ,EAAEe,IAAI,CAACf,QAAQ,IAAIA,QAAQ;cACnCuB,UAAU,EAAER,IAAI,CAACQ,UAAU,IAAI,CAAC;cAChCC,eAAe,EAAET,IAAI,CAACS,eAAe,IAAI,KAAK;cAC9CC,WAAW,EAAEV,IAAI,CAACU,WAAW,IAAI;YACnC,CAAC;UACH,CAAC,MAAM;YACL,OAAO;cAAEN,KAAK,EAAE,EAAE;cAAEG,UAAU,EAAE;YAAE,CAAC;UACrC;QACF,CAAC,MAAM;UAAA,IAAAI,cAAA;UACLC,OAAO,CAACC,KAAK,CAAC,kCAAkC,GAAAF,cAAA,GAAEb,QAAQ,CAACE,IAAI,cAAAW,cAAA,uBAAbA,cAAA,CAAeG,OAAO,CAAC;UACzE,OAAO;YAAEV,KAAK,EAAE,EAAE;YAAEG,UAAU,EAAE;UAAE,CAAC;QACrC;MACF,CAAC,CAAC,OAAOM,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QACvD,OAAO;UAAET,KAAK,EAAE,EAAE;UAAEG,UAAU,EAAE;QAAE,CAAC;MACrC;IACF,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAMQ,oBAAoBA,CAACC,EAAE,EAAE;IAC7B,MAAMvB,GAAG,GAAG,GAAGlB,UAAU,CAACmB,SAAS,8CAA8CsB,EAAE,EAAE;;IAErF;IACA,MAAMrB,UAAU,GAAGlB,oBAAoB,CAACmB,gBAAgB,CAAC,KAAK,EAAEH,GAAG,EAAE,CAAC,CAAC,CAAC;IAExE,OAAOhB,oBAAoB,CAACoB,OAAO,CAACF,UAAU,EAAE,YAAY;MAC1D,IAAI;QACF,MAAMG,QAAQ,GAAG,MAAMxB,IAAI,CAACyB,GAAG,CAACN,GAAG,CAAC;QAEpC,IAAIK,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACC,YAAY,KAAKzB,YAAY,CAAC0B,OAAO,EAAE;UACxE,OAAOJ,QAAQ,CAACE,IAAI,CAACG,IAAI,IAAI,IAAI;QACnC,CAAC,MAAM;UAAA,IAAAc,eAAA;UACLL,OAAO,CAACC,KAAK,CAAC,8BAA8B,GAAAI,eAAA,GAAEnB,QAAQ,CAACE,IAAI,cAAAiB,eAAA,uBAAbA,eAAA,CAAeH,OAAO,CAAC;UACrE,OAAO,IAAI;QACb;MACF,CAAC,CAAC,OAAOD,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpD,OAAO,IAAI;MACb;IACF,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,MAAMK,mBAAmBA,CAACC,iBAAiB,EAAE;IAC3C,IAAI;MACF,MAAMrB,QAAQ,GAAG,MAAMxB,IAAI,CAAC8C,IAAI,CAAC,GAAG7C,UAAU,CAACmB,SAAS,wCAAwC,EAAEyB,iBAAiB,CAAC;MAEpH,IAAIrB,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACC,YAAY,KAAKzB,YAAY,CAAC0B,OAAO,EAAE;QACxE,OAAOJ,QAAQ,CAACE,IAAI,CAACG,IAAI,IAAI,CAAC,CAAC;MACjC,CAAC,MAAM;QAAA,IAAAkB,eAAA;QACL,MAAM,IAAIC,KAAK,CAAC,EAAAD,eAAA,GAAAvB,QAAQ,CAACE,IAAI,cAAAqB,eAAA,uBAAbA,eAAA,CAAeP,OAAO,KAAI,gCAAgC,CAAC;MAC7E;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAMU,mBAAmBA,CAACJ,iBAAiB,EAAE;IAC3C,IAAI;MACF,MAAMrB,QAAQ,GAAG,MAAMxB,IAAI,CAAC8C,IAAI,CAAC,GAAG7C,UAAU,CAACmB,SAAS,wCAAwC,EAAEyB,iBAAiB,CAAC;MAEpH,IAAIrB,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACC,YAAY,KAAKzB,YAAY,CAAC0B,OAAO,EAAE;QACxE,OAAOJ,QAAQ,CAACE,IAAI,CAACG,IAAI,IAAI,CAAC,CAAC;MACjC,CAAC,MAAM;QAAA,IAAAqB,eAAA;QACL,MAAM,IAAIF,KAAK,CAAC,EAAAE,eAAA,GAAA1B,QAAQ,CAACE,IAAI,cAAAwB,eAAA,uBAAbA,eAAA,CAAeV,OAAO,KAAI,gCAAgC,CAAC;MAC7E;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,MAAMY,+BAA+BA,CAAC5C,IAAI,EAAE6C,SAAS,GAAG,IAAI,EAAE;IAC5D,IAAI;MACF,MAAMxC,MAAM,GAAG,IAAIC,eAAe,CAAC;QAAEN,IAAI,EAAEA,IAAI,CAACU,QAAQ,CAAC;MAAE,CAAC,CAAC;MAC7D,IAAImC,SAAS,EAAE;QACbxC,MAAM,CAACG,MAAM,CAAC,WAAW,EAAEqC,SAAS,CAAC;MACvC;MAEA,MAAM5B,QAAQ,GAAG,MAAMxB,IAAI,CAACyB,GAAG,CAAC,GAAGxB,UAAU,CAACmB,SAAS,sDAAsDR,MAAM,EAAE,CAAC;MAEtH,IAAIY,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACC,YAAY,KAAKzB,YAAY,CAAC0B,OAAO,EAAE;QACxE,OAAOJ,QAAQ,CAACE,IAAI,CAACG,IAAI,IAAI,KAAK;MACpC,CAAC,MAAM;QAAA,IAAAwB,eAAA;QACL,MAAM,IAAIL,KAAK,CAAC,EAAAK,eAAA,GAAA7B,QAAQ,CAACE,IAAI,cAAA2B,eAAA,uBAAbA,eAAA,CAAeb,OAAO,KAAI,6CAA6C,CAAC;MAC1F;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;MACnE,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,MAAMe,4BAA4BA,CAAC/C,IAAI,EAAE6C,SAAS,GAAG,IAAI,EAAE;IACzD,IAAI;MACF,MAAMxC,MAAM,GAAG,IAAIC,eAAe,CAAC;QAAEN,IAAI,EAAEA,IAAI,CAACU,QAAQ,CAAC;MAAE,CAAC,CAAC;MAC7D,IAAImC,SAAS,EAAE;QACbxC,MAAM,CAACG,MAAM,CAAC,WAAW,EAAEqC,SAAS,CAAC;MACvC;MAEA,MAAM5B,QAAQ,GAAG,MAAMxB,IAAI,CAACyB,GAAG,CAAC,GAAGxB,UAAU,CAACmB,SAAS,mDAAmDR,MAAM,EAAE,CAAC;MAEnH,IAAIY,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACC,YAAY,KAAKzB,YAAY,CAAC0B,OAAO,EAAE;QACxE,OAAOJ,QAAQ,CAACE,IAAI,CAACG,IAAI,IAAI,KAAK;MACpC,CAAC,MAAM;QAAA,IAAA0B,eAAA;QACL,MAAM,IAAIP,KAAK,CAAC,EAAAO,eAAA,GAAA/B,QAAQ,CAACE,IAAI,cAAA6B,eAAA,uBAAbA,eAAA,CAAef,OAAO,KAAI,0CAA0C,CAAC;MACvF;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChE,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAMiB,oBAAoBA,CAACC,eAAe,EAAE;IAC1C,IAAI;MACF,MAAMjC,QAAQ,GAAG,MAAMxB,IAAI,CAAC8C,IAAI,CAAC,GAAG7C,UAAU,CAACmB,SAAS,2CAA2CqC,eAAe,EAAE,CAAC;MAErH,IAAIjC,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACC,YAAY,KAAKzB,YAAY,CAAC0B,OAAO,EAAE;QACxE,OAAOJ,QAAQ,CAACE,IAAI,CAACG,IAAI,IAAI,CAAC,CAAC;MACjC,CAAC,MAAM;QAAA,IAAA6B,eAAA;QACL,MAAM,IAAIV,KAAK,CAAC,EAAAU,eAAA,GAAAlC,QAAQ,CAACE,IAAI,cAAAgC,eAAA,uBAAbA,eAAA,CAAelB,OAAO,KAAI,iCAAiC,CAAC;MAC9E;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,MAAMoB,SAASA,CAACC,aAAa,EAAEC,UAAU,EAAEC,YAAY,EAAEC,cAAc,EAAEC,YAAY,GAAG,IAAI,EAAE;IAC5F,IAAI;MACF,MAAMC,UAAU,GAAG;QACjB,GAAGL,aAAa;QAChBM,IAAI,EAAEL,UAAU;QAChBtD,IAAI,EAAEuD,YAAY;QAClBK,mBAAmB,EAAEJ,cAAc;QACnCC,YAAY,EAAEA;MAChB,CAAC;MAED,OAAO,MAAM,IAAI,CAACf,mBAAmB,CAACgB,UAAU,CAAC;IACnD,CAAC,CAAC,OAAO1B,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,MAAM6B,qBAAqBA,CAACR,aAAa,EAAEC,UAAU,EAAEC,YAAY,EAAEC,cAAc,EAAEC,YAAY,GAAG,IAAI,EAAE;IACxG,IAAI;MACF,MAAMC,UAAU,GAAG;QACjB,GAAGL,aAAa;QAChBM,IAAI,EAAEL,UAAU;QAChBtD,IAAI,EAAEuD,YAAY;QAClBK,mBAAmB,EAAEJ,cAAc;QACnCA,cAAc,EAAEA,cAAc;QAAE;QAChCvD,MAAM,EAAE,CAAC;QAAE;QACXwD,YAAY,EAAEA;MAChB,CAAC;MAED,OAAO,MAAM,IAAI,CAACf,mBAAmB,CAACgB,UAAU,CAAC;IACnD,CAAC,CAAC,OAAO1B,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAM8B,gCAAgCA,CAACZ,eAAe,EAAE;IACtD,IAAI;MACF,MAAMjC,QAAQ,GAAG,MAAMxB,IAAI,CAACyB,GAAG,CAAC,GAAGxB,UAAU,CAACmB,SAAS,0DAA0DqC,eAAe,EAAE,CAAC;MAEnI,IAAIjC,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACC,YAAY,KAAKzB,YAAY,CAAC0B,OAAO,EAAE;QACxE,OAAOJ,QAAQ,CAACE,IAAI,CAACG,IAAI,IAAI,CAAC,CAAC;MACjC,CAAC,MAAM;QAAA,IAAAyC,eAAA;QACL,MAAM,IAAItB,KAAK,CAAC,EAAAsB,eAAA,GAAA9C,QAAQ,CAACE,IAAI,cAAA4C,eAAA,uBAAbA,eAAA,CAAe9B,OAAO,KAAI,+CAA+C,CAAC;MAC5F;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,8CAA8C,EAAEA,KAAK,CAAC;MACpE,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,MAAMgC,wBAAwBA,CAACd,eAAe,EAAEe,UAAU,EAAE;IAC1D,IAAI;MACF,MAAMhD,QAAQ,GAAG,MAAMxB,IAAI,CAACyB,GAAG,CAAC,GAAGxB,UAAU,CAACmB,SAAS,+CAA+CqC,eAAe,IAAIe,UAAU,EAAE,CAAC;MAEtI,IAAIhD,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACC,YAAY,KAAKzB,YAAY,CAAC0B,OAAO,EAAE;QACxE,OAAOJ,QAAQ,CAACE,IAAI,CAACG,IAAI,IAAI;UAAE4C,mBAAmB,EAAE,CAAC;UAAEC,wBAAwB,EAAE,CAAC;UAAEC,wBAAwB,EAAE;QAAE,CAAC;MACnH,CAAC,MAAM;QAAA,IAAAC,eAAA;QACL,MAAM,IAAI5B,KAAK,CAAC,EAAA4B,eAAA,GAAApD,QAAQ,CAACE,IAAI,cAAAkD,eAAA,uBAAbA,eAAA,CAAepC,OAAO,KAAI,uCAAuC,CAAC;MACpF;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAC9D,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,MAAMsC,YAAYA,CAACpB,eAAe,EAAEe,UAAU,EAAE;IAC9C,IAAI;MACF,MAAMhD,QAAQ,GAAG,MAAMxB,IAAI,CAACyB,GAAG,CAAC,GAAGxB,UAAU,CAACmB,SAAS,mCAAmCqC,eAAe,IAAIe,UAAU,EAAE,CAAC;MAE1H,IAAIhD,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACC,YAAY,KAAKzB,YAAY,CAAC0B,OAAO,EAAE;QACxE,OAAOJ,QAAQ,CAACE,IAAI,CAACG,IAAI,IAAI,CAAC,CAAC;MACjC,CAAC,MAAM;QAAA,IAAAiD,eAAA;QACL,MAAM,IAAI9B,KAAK,CAAC,EAAA8B,eAAA,GAAAtD,QAAQ,CAACE,IAAI,cAAAoD,eAAA,uBAAbA,eAAA,CAAetC,OAAO,KAAI,yBAAyB,CAAC;MACtE;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,MAAMwC,kBAAkBA,CAACtB,eAAe,EAAE;IACxC,IAAI;MACF,MAAMjC,QAAQ,GAAG,MAAMxB,IAAI,CAACyB,GAAG,CAAC,GAAGxB,UAAU,CAACmB,SAAS,yCAAyCqC,eAAe,EAAE,CAAC;MAElH,IAAIjC,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACC,YAAY,KAAKzB,YAAY,CAAC0B,OAAO,EAAE;QACxE,OAAO,CAAC;MACV,CAAC,MAAM;QAAA,IAAAoD,eAAA;QACL,MAAM,IAAIhC,KAAK,CAAC,EAAAgC,eAAA,GAAAxD,QAAQ,CAACE,IAAI,cAAAsD,eAAA,uBAAbA,eAAA,CAAexC,OAAO,KAAI,+BAA+B,CAAC;MAC5E;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAM0C,mBAAmBA,CAACxB,eAAe,EAAE;IACzC,IAAI;MACF,MAAMjC,QAAQ,GAAG,MAAMxB,IAAI,CAAC8C,IAAI,CAAC,GAAG7C,UAAU,CAACmB,SAAS,6CAA6CqC,eAAe,EAAE,CAAC;MAEvH,IAAIjC,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACC,YAAY,KAAKzB,YAAY,CAAC0B,OAAO,EAAE;QACxE,OAAOJ,QAAQ,CAACE,IAAI,CAACG,IAAI,IAAI,CAAC,CAAC;MACjC,CAAC,MAAM;QAAA,IAAAqD,gBAAA;QACL,MAAM,IAAIlC,KAAK,CAAC,EAAAkC,gBAAA,GAAA1D,QAAQ,CAACE,IAAI,cAAAwD,gBAAA,uBAAbA,gBAAA,CAAe1C,OAAO,KAAI,gCAAgC,CAAC;MAC7E;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;EACE,MAAM4C,oBAAoBA,CAAA,EAAG;IAC3B,IAAI;MACF,MAAM3D,QAAQ,GAAG,MAAMxB,IAAI,CAACyB,GAAG,CAAC,GAAGxB,UAAU,CAACmB,SAAS,yCAAyC,CAAC;MAEjG,IAAII,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACC,YAAY,KAAKzB,YAAY,CAAC0B,OAAO,EAAE;QACxE,OAAOJ,QAAQ,CAACE,IAAI,CAACG,IAAI,IAAI,EAAE;MACjC,CAAC,MAAM;QAAA,IAAAuD,gBAAA;QACL9C,OAAO,CAACC,KAAK,CAAC,mCAAmC,GAAA6C,gBAAA,GAAE5D,QAAQ,CAACE,IAAI,cAAA0D,gBAAA,uBAAbA,gBAAA,CAAe5C,OAAO,CAAC;QAC1E,OAAO,EAAE;MACX;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,OAAO,EAAE;IACX;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAM8C,sBAAsBA,CAAC5B,eAAe,EAAE;IAC5C,IAAI;MACF,MAAMjC,QAAQ,GAAG,MAAMxB,IAAI,CAACyB,GAAG,CAAC,GAAGxB,UAAU,CAACmB,SAAS,6CAA6CqC,eAAe,EAAE,CAAC;MAEtH,IAAIjC,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACC,YAAY,KAAKzB,YAAY,CAAC0B,OAAO,EAAE;QACxE,OAAOJ,QAAQ,CAACE,IAAI,CAACG,IAAI,IAAI,CAAC,CAAC;MACjC,CAAC,MAAM;QAAA,IAAAyD,gBAAA;QACL,MAAM,IAAItC,KAAK,CAAC,EAAAsC,gBAAA,GAAA9D,QAAQ,CAACE,IAAI,cAAA4D,gBAAA,uBAAbA,gBAAA,CAAe9C,OAAO,KAAI,mCAAmC,CAAC;MAChF;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,MAAMgD,iBAAiBA,CAACC,QAAQ,EAAEC,UAAU,EAAE;IAC5C,IAAI;MACF,MAAMjE,QAAQ,GAAG,MAAMxB,IAAI,CAAC8C,IAAI,CAAC,GAAG7C,UAAU,CAACmB,SAAS,sCAAsC,EAAE,IAAI,EAAE;QACpGR,MAAM,EAAE;UACN4E,QAAQ,EAAEA,QAAQ;UAClBC,UAAU,EAAEA;QACd;MACF,CAAC,CAAC;MAEF,IAAIjE,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACC,YAAY,KAAKzB,YAAY,CAAC0B,OAAO,EAAE;QACxE,OAAOJ,QAAQ,CAACE,IAAI,CAACG,IAAI,IAAI,CAAC,CAAC;MACjC,CAAC,MAAM;QAAA,IAAA6D,gBAAA;QACL,MAAM,IAAI1C,KAAK,CAAC,EAAA0C,gBAAA,GAAAlE,QAAQ,CAACE,IAAI,cAAAgE,gBAAA,uBAAbA,gBAAA,CAAelD,OAAO,KAAI,8BAA8B,CAAC;MAC3E;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,MAAMoD,sBAAsBA,CAAClC,eAAe,EAAEmC,KAAK,EAAE;IACnD,IAAI;MACF,MAAMpE,QAAQ,GAAG,MAAMxB,IAAI,CAACyB,GAAG,CAAC,GAAGxB,UAAU,CAACmB,SAAS,6CAA6CqC,eAAe,IAAImC,KAAK,EAAE,CAAC;MAE/H,IAAIpE,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACC,YAAY,KAAKzB,YAAY,CAAC0B,OAAO,EAAE;QACxE,OAAOJ,QAAQ,CAACE,IAAI,CAACG,IAAI,IAAI,CAAC;MAChC,CAAC,MAAM;QAAA,IAAAgE,gBAAA;QACL,MAAM,IAAI7C,KAAK,CAAC,EAAA6C,gBAAA,GAAArE,QAAQ,CAACE,IAAI,cAAAmE,gBAAA,uBAAbA,gBAAA,CAAerD,OAAO,KAAI,qCAAqC,CAAC;MAClF;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,MAAMuD,8BAA8BA,CAACC,OAAO,EAAE;IAC5C,IAAI;MACF,MAAMvE,QAAQ,GAAG,MAAMxB,IAAI,CAAC8C,IAAI,CAAC,GAAG7C,UAAU,CAACmB,SAAS,mDAAmD,EAAE2E,OAAO,CAAC;MAErH,IAAIvE,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACC,YAAY,KAAKzB,YAAY,CAAC0B,OAAO,EAAE;QACxE,OAAOJ,QAAQ,CAACE,IAAI,CAACG,IAAI;MAC3B,CAAC,MAAM;QAAA,IAAAmE,gBAAA;QACL,MAAM,IAAIhD,KAAK,CAAC,EAAAgD,gBAAA,GAAAxE,QAAQ,CAACE,IAAI,cAAAsE,gBAAA,uBAAbA,gBAAA,CAAexD,OAAO,KAAI,8CAA8C,CAAC;MAC3F;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,8CAA8C,EAAEA,KAAK,CAAC;MACpE,MAAMA,KAAK;IACb;EACF;AACF;;AAEA;AACA,MAAM0D,oBAAoB,GAAG,IAAI7F,oBAAoB,CAAC,CAAC;AACvD,eAAe6F,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}