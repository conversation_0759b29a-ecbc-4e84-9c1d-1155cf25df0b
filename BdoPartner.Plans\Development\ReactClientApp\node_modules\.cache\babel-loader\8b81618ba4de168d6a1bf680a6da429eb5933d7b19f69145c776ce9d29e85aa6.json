{"ast": null, "code": "var _jsxFileName = \"C:\\\\git\\\\Partner-Site\\\\BdoPartner.Plans\\\\Development\\\\ReactClientApp\\\\src\\\\core\\\\auth\\\\components\\\\authProvider.jsx\";\nimport React from \"react\";\nimport AuthService from \"../authService\";\n\n/**\r\n * Declare a Authentication context.\r\n * Note: Provider refered to authService.\r\n *\r\n * Creates a Context object. When React renders a component that subscribes\r\n * to this Context object it will read the current context value\r\n * from the closest matching Provider above it in the tree.\r\n *\r\n * Note: This looks like define an service interface.\r\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const AuthContext = /*#__PURE__*/React.createContext({});\n\n/**\r\n * Consume the interfaces/properties defined in Authentication context.\r\n * Try to expose the Authentication Context defined properties to the nested components.\r\n *\r\n * Note: This looks like define a reference of the service interface.\r\n * Note: AuthConsumer can be replaced by useContext(AuthContext).\r\n */\nexport const AuthConsumer = AuthContext.Consumer;\n\n/**\r\n * Note: This looks as register implementation service instance (with detail business logic codes) into\r\n * the service interface.\r\n *\r\n * Reference: https://stackoverflow.com/questions/58197800/set-the-data-in-react-context-from-asynchronous-api-call\r\n */\nexport const AuthProvider = ({\n  children\n}) => {\n  const authService = new AuthService();\n\n  // The Provider component accepts a value prop to be passed to consuming components\n  // that are descendants of this Provider.\n  // One Provider can be connected to many consumers.\n  // Providers can be nested to override values deeper within the tree.\n  // All consumers that are descendants of a Provider will re-render\n  // whenever the Provider’s value prop changes.\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: {\n      ...authService\n    },\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 41,\n    columnNumber: 5\n  }, this);\n};\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "AuthService", "jsxDEV", "_jsxDEV", "AuthContext", "createContext", "AuthConsumer", "Consumer", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "authService", "Provider", "value", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/core/auth/components/authProvider.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport AuthService from \"../authService\";\r\n\r\n/**\r\n * Declare a Authentication context.\r\n * Note: Provider refered to authService.\r\n *\r\n * Creates a Context object. When React renders a component that subscribes\r\n * to this Context object it will read the current context value\r\n * from the closest matching Provider above it in the tree.\r\n *\r\n * Note: This looks like define an service interface.\r\n */\r\nexport const AuthContext = React.createContext({});\r\n\r\n/**\r\n * Consume the interfaces/properties defined in Authentication context.\r\n * Try to expose the Authentication Context defined properties to the nested components.\r\n *\r\n * Note: This looks like define a reference of the service interface.\r\n * Note: AuthConsumer can be replaced by useContext(AuthContext).\r\n */\r\nexport const AuthConsumer = AuthContext.Consumer;\r\n\r\n/**\r\n * Note: This looks as register implementation service instance (with detail business logic codes) into\r\n * the service interface.\r\n *\r\n * Reference: https://stackoverflow.com/questions/58197800/set-the-data-in-react-context-from-asynchronous-api-call\r\n */\r\nexport const AuthProvider = ({ children }) => {\r\n  const authService = new AuthService();\r\n\r\n  // The Provider component accepts a value prop to be passed to consuming components\r\n  // that are descendants of this Provider.\r\n  // One Provider can be connected to many consumers.\r\n  // Providers can be nested to override values deeper within the tree.\r\n  // All consumers that are descendants of a Provider will re-render\r\n  // whenever the Provider’s value prop changes.\r\n  return (\r\n    <AuthContext.Provider\r\n      value={{\r\n        ...authService,\r\n      }}\r\n    >\r\n      {/* \"children\" represents all nested children components. */}\r\n      {children}\r\n    </AuthContext.Provider>\r\n  );\r\n};\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,WAAW,MAAM,gBAAgB;;AAExC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA,SAAAC,MAAA,IAAAC,OAAA;AAUA,OAAO,MAAMC,WAAW,gBAAGJ,KAAK,CAACK,aAAa,CAAC,CAAC,CAAC,CAAC;;AAElD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,YAAY,GAAGF,WAAW,CAACG,QAAQ;;AAEhD;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAC5C,MAAMC,WAAW,GAAG,IAAIT,WAAW,CAAC,CAAC;;EAErC;EACA;EACA;EACA;EACA;EACA;EACA,oBACEE,OAAA,CAACC,WAAW,CAACO,QAAQ;IACnBC,KAAK,EAAE;MACL,GAAGF;IACL,CAAE;IAAAD,QAAA,EAGDA;EAAQ;IAAAI,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAACC,EAAA,GAnBWT,YAAY;AAAA,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}