{"ast": null, "code": "import { Observable } from '../Observable';\nimport { isFunction } from '../util/isFunction';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nexport function fromEventPattern(add<PERSON><PERSON><PERSON>, remove<PERSON><PERSON><PERSON>, resultSelector) {\n  if (resultSelector) {\n    return fromEventPattern(addH<PERSON><PERSON>, removeHandler).pipe(mapOneOrManyArgs(resultSelector));\n  }\n  return new Observable(function (subscriber) {\n    var handler = function () {\n      var e = [];\n      for (var _i = 0; _i < arguments.length; _i++) {\n        e[_i] = arguments[_i];\n      }\n      return subscriber.next(e.length === 1 ? e[0] : e);\n    };\n    var retValue = addHandler(handler);\n    return isFunction(removeHandler) ? function () {\n      return removeHandler(handler, retValue);\n    } : undefined;\n  });\n}", "map": {"version": 3, "names": ["Observable", "isFunction", "mapOneOrManyArgs", "fromEventPattern", "add<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "resultSelector", "pipe", "subscriber", "handler", "e", "_i", "arguments", "length", "next", "retValue", "undefined"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\observable\\fromEventPattern.ts"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { isFunction } from '../util/isFunction';\nimport { NodeEventHandler } from './fromEvent';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\n\n/* tslint:disable:max-line-length */\nexport function fromEventPattern<T>(\n  addHandler: (handler: NodeEventHandler) => any,\n  removeHandler?: (handler: NodeEventHandler, signal?: any) => void\n): Observable<T>;\nexport function fromEventPattern<T>(\n  addHandler: (handler: NodeEventHandler) => any,\n  removeHandler?: (handler: NodeEventHandler, signal?: any) => void,\n  resultSelector?: (...args: any[]) => T\n): Observable<T>;\n/* tslint:enable:max-line-length */\n\n/**\n * Creates an Observable from an arbitrary API for registering event handlers.\n *\n * <span class=\"informal\">When that method for adding event handler was something {@link fromEvent}\n * was not prepared for.</span>\n *\n * ![](fromEventPattern.png)\n *\n * `fromEventPattern` allows you to convert into an Observable any API that supports registering handler functions\n * for events. It is similar to {@link fromEvent}, but far\n * more flexible. In fact, all use cases of {@link fromEvent} could be easily handled by\n * `fromEventPattern` (although in slightly more verbose way).\n *\n * This operator accepts as a first argument an `addHandler` function, which will be injected with\n * handler parameter. That handler is actually an event handler function that you now can pass\n * to API expecting it. `addHandler` will be called whenever Observable\n * returned by the operator is subscribed, so registering handler in API will not\n * necessarily happen when `fromEventPattern` is called.\n *\n * After registration, every time an event that we listen to happens,\n * Observable returned by `fromEventPattern` will emit value that event handler\n * function was called with. Note that if event handler was called with more\n * than one argument, second and following arguments will not appear in the Observable.\n *\n * If API you are using allows to unregister event handlers as well, you can pass to `fromEventPattern`\n * another function - `removeHandler` - as a second parameter. It will be injected\n * with the same handler function as before, which now you can use to unregister\n * it from the API. `removeHandler` will be called when consumer of resulting Observable\n * unsubscribes from it.\n *\n * In some APIs unregistering is actually handled differently. Method registering an event handler\n * returns some kind of token, which is later used to identify which function should\n * be unregistered or it itself has method that unregisters event handler.\n * If that is the case with your API, make sure token returned\n * by registering method is returned by `addHandler`. Then it will be passed\n * as a second argument to `removeHandler`, where you will be able to use it.\n *\n * If you need access to all event handler parameters (not only the first one),\n * or you need to transform them in any way, you can call `fromEventPattern` with optional\n * third parameter - project function which will accept all arguments passed to\n * event handler when it is called. Whatever is returned from project function will appear on\n * resulting stream instead of usual event handlers first argument. This means\n * that default project can be thought of as function that takes its first parameter\n * and ignores the rest.\n *\n * ## Examples\n *\n * Emits clicks happening on the DOM document\n *\n * ```ts\n * import { fromEventPattern } from 'rxjs';\n *\n * function addClickHandler(handler) {\n *   document.addEventListener('click', handler);\n * }\n *\n * function removeClickHandler(handler) {\n *   document.removeEventListener('click', handler);\n * }\n *\n * const clicks = fromEventPattern(\n *   addClickHandler,\n *   removeClickHandler\n * );\n * clicks.subscribe(x => console.log(x));\n *\n * // Whenever you click anywhere in the browser, DOM MouseEvent\n * // object will be logged.\n * ```\n *\n * Use with API that returns cancellation token\n *\n * ```ts\n * import { fromEventPattern } from 'rxjs';\n *\n * const token = someAPI.registerEventHandler(function() {});\n * someAPI.unregisterEventHandler(token); // this APIs cancellation method accepts\n *                                        // not handler itself, but special token.\n *\n * const someAPIObservable = fromEventPattern(\n *   function(handler) { return someAPI.registerEventHandler(handler); }, // Note that we return the token here...\n *   function(handler, token) { someAPI.unregisterEventHandler(token); }  // ...to then use it here.\n * );\n * ```\n *\n * Use with project function\n *\n * ```ts\n * import { fromEventPattern } from 'rxjs';\n *\n * someAPI.registerEventHandler((eventType, eventMessage) => {\n *   console.log(eventType, eventMessage); // Logs 'EVENT_TYPE' 'EVENT_MESSAGE' to console.\n * });\n *\n * const someAPIObservable = fromEventPattern(\n *   handler => someAPI.registerEventHandler(handler),\n *   handler => someAPI.unregisterEventHandler(handler)\n *   (eventType, eventMessage) => eventType + ' --- ' + eventMessage // without that function only 'EVENT_TYPE'\n * );                                                                // would be emitted by the Observable\n *\n * someAPIObservable.subscribe(value => console.log(value));\n *\n * // Logs:\n * // 'EVENT_TYPE --- EVENT_MESSAGE'\n * ```\n *\n * @see {@link fromEvent}\n * @see {@link bindCallback}\n * @see {@link bindNodeCallback}\n *\n * @param addHandler A function that takes a `handler` function as argument and attaches it\n * somehow to the actual source of events.\n * @param removeHandler A function that takes a `handler` function as an argument and removes\n * it from the event source. If `addHandler` returns some kind of token, `removeHandler` function\n * will have it as a second parameter.\n * @param resultSelector A function to transform results. It takes the arguments from the event\n * handler and should return a single value.\n * @return Observable which, when an event happens, emits first parameter passed to registered\n * event handler. Alternatively it emits whatever project function returns at that moment.\n */\nexport function fromEventPattern<T>(\n  addHandler: (handler: NodeEventHandler) => any,\n  removeHandler?: (handler: NodeEventHandler, signal?: any) => void,\n  resultSelector?: (...args: any[]) => T\n): Observable<T | T[]> {\n  if (resultSelector) {\n    return fromEventPattern<T>(addHandler, removeHandler).pipe(mapOneOrManyArgs(resultSelector));\n  }\n\n  return new Observable<T | T[]>((subscriber) => {\n    const handler = (...e: T[]) => subscriber.next(e.length === 1 ? e[0] : e);\n    const retValue = addHandler(handler);\n    return isFunction(removeHandler) ? () => removeHandler(handler, retValue) : undefined;\n  });\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,UAAU,QAAQ,oBAAoB;AAE/C,SAASC,gBAAgB,QAAQ,0BAA0B;AAsI3D,OAAM,SAAUC,gBAAgBA,CAC9BC,UAA8C,EAC9CC,aAAiE,EACjEC,cAAsC;EAEtC,IAAIA,cAAc,EAAE;IAClB,OAAOH,gBAAgB,CAAIC,UAAU,EAAEC,aAAa,CAAC,CAACE,IAAI,CAACL,gBAAgB,CAACI,cAAc,CAAC,CAAC;;EAG9F,OAAO,IAAIN,UAAU,CAAU,UAACQ,UAAU;IACxC,IAAMC,OAAO,GAAG,SAAAA,CAAA;MAAC,IAAAC,CAAA;WAAA,IAAAC,EAAA,IAAS,EAATA,EAAA,GAAAC,SAAA,CAAAC,MAAS,EAATF,EAAA,EAAS;QAATD,CAAA,CAAAC,EAAA,IAAAC,SAAA,CAAAD,EAAA;;MAAc,OAAAH,UAAU,CAACM,IAAI,CAACJ,CAAC,CAACG,MAAM,KAAK,CAAC,GAAGH,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC;IAA1C,CAA0C;IACzE,IAAMK,QAAQ,GAAGX,UAAU,CAACK,OAAO,CAAC;IACpC,OAAOR,UAAU,CAACI,aAAa,CAAC,GAAG;MAAM,OAAAA,aAAa,CAACI,OAAO,EAAEM,QAAQ,CAAC;IAAhC,CAAgC,GAAGC,SAAS;EACvF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}