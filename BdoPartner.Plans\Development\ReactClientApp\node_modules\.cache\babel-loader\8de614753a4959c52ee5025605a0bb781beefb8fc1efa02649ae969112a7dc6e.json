{"ast": null, "code": "import { __read, __spreadArray } from \"tslib\";\nimport { operate } from '../util/lift';\nimport { concatAll } from './concatAll';\nimport { popScheduler } from '../util/args';\nimport { from } from '../observable/from';\nexport function concat() {\n  var args = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    args[_i] = arguments[_i];\n  }\n  var scheduler = popScheduler(args);\n  return operate(function (source, subscriber) {\n    concatAll()(from(__spreadArray([source], __read(args)), scheduler)).subscribe(subscriber);\n  });\n}", "map": {"version": 3, "names": ["operate", "concatAll", "popScheduler", "from", "concat", "args", "_i", "arguments", "length", "scheduler", "source", "subscriber", "__spread<PERSON><PERSON>y", "__read", "subscribe"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\concat.ts"], "sourcesContent": ["import { ObservableInputTuple, OperatorFunction, SchedulerLike } from '../types';\nimport { operate } from '../util/lift';\nimport { concatAll } from './concatAll';\nimport { popScheduler } from '../util/args';\nimport { from } from '../observable/from';\n\n/** @deprecated Replaced with {@link concatWith}. Will be removed in v8. */\nexport function concat<T, A extends readonly unknown[]>(...sources: [...ObservableInputTuple<A>]): OperatorFunction<T, T | A[number]>;\n/** @deprecated Replaced with {@link concatWith}. Will be removed in v8. */\nexport function concat<T, A extends readonly unknown[]>(\n  ...sourcesAndScheduler: [...ObservableInputTuple<A>, SchedulerLike]\n): OperatorFunction<T, T | A[number]>;\n\n/**\n * @deprecated Replaced with {@link concatWith}. Will be removed in v8.\n */\nexport function concat<T, R>(...args: any[]): OperatorFunction<T, R> {\n  const scheduler = popScheduler(args);\n  return operate((source, subscriber) => {\n    concatAll()(from([source, ...args], scheduler)).subscribe(subscriber);\n  });\n}\n"], "mappings": ";AACA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,SAAS,QAAQ,aAAa;AACvC,SAASC,YAAY,QAAQ,cAAc;AAC3C,SAASC,IAAI,QAAQ,oBAAoB;AAYzC,OAAM,SAAUC,MAAMA,CAAA;EAAO,IAAAC,IAAA;OAAA,IAAAC,EAAA,IAAc,EAAdA,EAAA,GAAAC,SAAA,CAAAC,MAAc,EAAdF,EAAA,EAAc;IAAdD,IAAA,CAAAC,EAAA,IAAAC,SAAA,CAAAD,EAAA;;EAC3B,IAAMG,SAAS,GAAGP,YAAY,CAACG,IAAI,CAAC;EACpC,OAAOL,OAAO,CAAC,UAACU,MAAM,EAAEC,UAAU;IAChCV,SAAS,EAAE,CAACE,IAAI,CAAAS,aAAA,EAAEF,MAAM,GAAAG,MAAA,CAAKR,IAAI,IAAGI,SAAS,CAAC,CAAC,CAACK,SAAS,CAACH,UAAU,CAAC;EACvE,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}