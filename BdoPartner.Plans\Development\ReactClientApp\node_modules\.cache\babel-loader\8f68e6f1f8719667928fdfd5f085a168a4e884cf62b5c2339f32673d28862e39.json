{"ast": null, "code": "var _jsxFileName = \"C:\\\\git\\\\Partner-Site\\\\BdoPartner.Plans\\\\Development\\\\ReactClientApp\\\\src\\\\routes\\\\privateRoute.js\",\n  _s = $RefreshSig$();\nimport React, { useContext } from \"react\";\nimport { AuthContext } from \"../core/auth/components/authProvider\";\nimport { messageService } from \"../core/message/messageService\";\n\n/**\r\n * Route required authentication.\r\n *\r\n * Reference: https://github.com/Franpastoragusti/oidc-react-app\r\n *\r\n * TODO. Check roles and permissions here.\r\n * @param {*} param0\r\n * @returns\r\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport const PrivateRoute = ({\n  component: Component,\n  roles,\n  ...rest\n}) => {\n  _s();\n  const authService = useContext(AuthContext);\n  //\n  // Demo get roles for the specified Route.\n  // TODO. <PERSON><PERSON><PERSON> is able to udpate the authorization check logic here.\n  //\n  const requiredRoles = roles;\n  if (!!Component && authService.isAuthenticated()) {\n    //\n    // TODO. We can do further checking here with logon user's Roles, Permissions etc.\n    // TODO. Further Authorization check here...\n    //\n    const userRoles = authService.getUser().roles;\n\n    // console.log(\"PrivateRoute.UserRoles\", JSON.stringify(userRoles));\n\n    if (requiredRoles && requiredRoles.length > 0) {\n      if (userRoles && userRoles.length > 0) {\n        let result = requiredRoles.some(r1 => {\n          if (userRoles.some(r2 => {\n            return r2 === r1;\n          })) return true;\n          return false;\n        });\n        if (result) {\n          return /*#__PURE__*/_jsxDEV(Component, {\n            ...rest\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 18\n          }, this);\n        } else {\n          messageService.warnToast(\"You do not have permission to access.\", false);\n\n          // If not permission, by default go back to home page. Note: Developer can change this.\n          return /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Unauthorized access\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 55,\n              columnNumber: 15\n            }, this), messageService.emit()]\n          }, void 0, true);\n        }\n      } else {\n        messageService.warnToast(\"You do not have permission to access\", false);\n\n        // If no permission, by default go back to home page. Note: Developer can change this.\n        return /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Unauthorized access\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 13\n          }, this), messageService.emit()]\n        }, void 0, true);\n      }\n    } else {\n      return /*#__PURE__*/_jsxDEV(Component, {\n        ...rest\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 14\n      }, this);\n    }\n  } else {\n    //\n    // As for unauthorized access.\n    //\n    authService.signinRedirect();\n    return /*#__PURE__*/_jsxDEV(\"span\", {\n      children: \"Unauthorized access. Redirecting to login.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 12\n    }, this);\n  }\n};\n_s(PrivateRoute, \"TLh9duf52wwWZvm9lOaqSx6A0bw=\");\n_c = PrivateRoute;\nvar _c;\n$RefreshReg$(_c, \"PrivateRoute\");", "map": {"version": 3, "names": ["React", "useContext", "AuthContext", "messageService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "PrivateRoute", "component", "Component", "roles", "rest", "_s", "authService", "requiredRoles", "isAuthenticated", "userRoles", "getUser", "length", "result", "some", "r1", "r2", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "warnToast", "children", "emit", "signinRedirect", "_c", "$RefreshReg$"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/routes/privateRoute.js"], "sourcesContent": ["import React, { useContext } from \"react\";\r\nimport { AuthContext } from \"../core/auth/components/authProvider\";\r\nimport { messageService } from \"../core/message/messageService\";\r\n\r\n/**\r\n * Route required authentication.\r\n *\r\n * Reference: https://github.com/Franpastoragusti/oidc-react-app\r\n *\r\n * TODO. Check roles and permissions here.\r\n * @param {*} param0\r\n * @returns\r\n */\r\nexport const PrivateRoute = ({ component: Component, roles, ...rest }) => {\r\n  const authService = useContext(AuthContext);\r\n  //\r\n  // Demo get roles for the specified Route.\r\n  // TODO. Developer is able to udpate the authorization check logic here.\r\n  //\r\n  const requiredRoles = roles;\r\n\r\n  if (!!Component && authService.isAuthenticated()) {\r\n    //\r\n    // TODO. We can do further checking here with logon user's Roles, Permissions etc.\r\n    // TODO. Further Authorization check here...\r\n    //\r\n    const userRoles = authService.getUser().roles;\r\n\r\n    // console.log(\"PrivateRoute.UserRoles\", JSON.stringify(userRoles));\r\n\r\n    if (requiredRoles && requiredRoles.length > 0) {\r\n      if (userRoles && userRoles.length > 0) {\r\n        let result = requiredRoles.some((r1) => {\r\n          if (\r\n            userRoles.some((r2) => {\r\n              return r2 === r1;\r\n            })\r\n          )\r\n            return true;\r\n\r\n          return false;\r\n        });\r\n\r\n        if (result) {\r\n          return <Component {...rest} />;\r\n        } else {\r\n          messageService.warnToast(\r\n            \"You do not have permission to access.\",\r\n            false\r\n          );\r\n\r\n          // If not permission, by default go back to home page. Note: Developer can change this.\r\n          return (\r\n            <>\r\n              <span>Unauthorized access</span>\r\n              {messageService.emit()}\r\n            </>\r\n          );\r\n        }\r\n      } else {\r\n        messageService.warnToast(\r\n          \"You do not have permission to access\",\r\n          false\r\n        );\r\n\r\n        // If no permission, by default go back to home page. Note: Developer can change this.\r\n        return (\r\n          <>\r\n            <span>Unauthorized access</span>\r\n            {messageService.emit()}\r\n          </>\r\n        );\r\n      }\r\n    } else {\r\n      return <Component {...rest} />;\r\n    }\r\n  } else {\r\n    //\r\n    // As for unauthorized access.\r\n    //\r\n    authService.signinRedirect();\r\n    return <span>Unauthorized access. Redirecting to login.</span>;\r\n  }\r\n};\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,UAAU,QAAQ,OAAO;AACzC,SAASC,WAAW,QAAQ,sCAAsC;AAClE,SAASC,cAAc,QAAQ,gCAAgC;;AAE/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AASA,OAAO,MAAMC,YAAY,GAAGA,CAAC;EAAEC,SAAS,EAAEC,SAAS;EAAEC,KAAK;EAAE,GAAGC;AAAK,CAAC,KAAK;EAAAC,EAAA;EACxE,MAAMC,WAAW,GAAGb,UAAU,CAACC,WAAW,CAAC;EAC3C;EACA;EACA;EACA;EACA,MAAMa,aAAa,GAAGJ,KAAK;EAE3B,IAAI,CAAC,CAACD,SAAS,IAAII,WAAW,CAACE,eAAe,CAAC,CAAC,EAAE;IAChD;IACA;IACA;IACA;IACA,MAAMC,SAAS,GAAGH,WAAW,CAACI,OAAO,CAAC,CAAC,CAACP,KAAK;;IAE7C;;IAEA,IAAII,aAAa,IAAIA,aAAa,CAACI,MAAM,GAAG,CAAC,EAAE;MAC7C,IAAIF,SAAS,IAAIA,SAAS,CAACE,MAAM,GAAG,CAAC,EAAE;QACrC,IAAIC,MAAM,GAAGL,aAAa,CAACM,IAAI,CAAEC,EAAE,IAAK;UACtC,IACEL,SAAS,CAACI,IAAI,CAAEE,EAAE,IAAK;YACrB,OAAOA,EAAE,KAAKD,EAAE;UAClB,CAAC,CAAC,EAEF,OAAO,IAAI;UAEb,OAAO,KAAK;QACd,CAAC,CAAC;QAEF,IAAIF,MAAM,EAAE;UACV,oBAAOf,OAAA,CAACK,SAAS;YAAA,GAAKE;UAAI;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAChC,CAAC,MAAM;UACLxB,cAAc,CAACyB,SAAS,CACtB,uCAAuC,EACvC,KACF,CAAC;;UAED;UACA,oBACEvB,OAAA,CAAAE,SAAA;YAAAsB,QAAA,gBACExB,OAAA;cAAAwB,QAAA,EAAM;YAAmB;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EAC/BxB,cAAc,CAAC2B,IAAI,CAAC,CAAC;UAAA,eACtB,CAAC;QAEP;MACF,CAAC,MAAM;QACL3B,cAAc,CAACyB,SAAS,CACtB,sCAAsC,EACtC,KACF,CAAC;;QAED;QACA,oBACEvB,OAAA,CAAAE,SAAA;UAAAsB,QAAA,gBACExB,OAAA;YAAAwB,QAAA,EAAM;UAAmB;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EAC/BxB,cAAc,CAAC2B,IAAI,CAAC,CAAC;QAAA,eACtB,CAAC;MAEP;IACF,CAAC,MAAM;MACL,oBAAOzB,OAAA,CAACK,SAAS;QAAA,GAAKE;MAAI;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAChC;EACF,CAAC,MAAM;IACL;IACA;IACA;IACAb,WAAW,CAACiB,cAAc,CAAC,CAAC;IAC5B,oBAAO1B,OAAA;MAAAwB,QAAA,EAAM;IAA0C;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAChE;AACF,CAAC;AAACd,EAAA,CAtEWL,YAAY;AAAAwB,EAAA,GAAZxB,YAAY;AAAA,IAAAwB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}