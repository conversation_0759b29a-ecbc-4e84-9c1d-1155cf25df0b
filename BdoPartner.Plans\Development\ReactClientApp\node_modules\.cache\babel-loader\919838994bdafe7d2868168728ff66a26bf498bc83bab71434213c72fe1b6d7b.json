{"ast": null, "code": "import { Observable } from '../Observable';\nimport { argsArgArrayOrObject } from '../util/argsArgArrayOrObject';\nimport { innerFrom } from './innerFrom';\nimport { popResultSelector } from '../util/args';\nimport { createOperatorSubscriber } from '../operators/OperatorSubscriber';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nimport { createObject } from '../util/createObject';\nexport function forkJoin() {\n  var args = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    args[_i] = arguments[_i];\n  }\n  var resultSelector = popResultSelector(args);\n  var _a = argsArgArrayOrObject(args),\n    sources = _a.args,\n    keys = _a.keys;\n  var result = new Observable(function (subscriber) {\n    var length = sources.length;\n    if (!length) {\n      subscriber.complete();\n      return;\n    }\n    var values = new Array(length);\n    var remainingCompletions = length;\n    var remainingEmissions = length;\n    var _loop_1 = function (sourceIndex) {\n      var hasValue = false;\n      innerFrom(sources[sourceIndex]).subscribe(createOperatorSubscriber(subscriber, function (value) {\n        if (!hasValue) {\n          hasValue = true;\n          remainingEmissions--;\n        }\n        values[sourceIndex] = value;\n      }, function () {\n        return remainingCompletions--;\n      }, undefined, function () {\n        if (!remainingCompletions || !hasValue) {\n          if (!remainingEmissions) {\n            subscriber.next(keys ? createObject(keys, values) : values);\n          }\n          subscriber.complete();\n        }\n      }));\n    };\n    for (var sourceIndex = 0; sourceIndex < length; sourceIndex++) {\n      _loop_1(sourceIndex);\n    }\n  });\n  return resultSelector ? result.pipe(mapOneOrManyArgs(resultSelector)) : result;\n}", "map": {"version": 3, "names": ["Observable", "argsArgArrayOrObject", "innerFrom", "popResultSelector", "createOperatorSubscriber", "mapOneOrManyArgs", "createObject", "fork<PERSON><PERSON>n", "args", "_i", "arguments", "length", "resultSelector", "_a", "sources", "keys", "result", "subscriber", "complete", "values", "Array", "remainingCompletions", "remainingEmissions", "sourceIndex", "hasValue", "subscribe", "value", "undefined", "next", "pipe"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\observable\\forkJoin.ts"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { ObservedValueOf, ObservableInputTuple, ObservableInput } from '../types';\nimport { argsArgArrayOrObject } from '../util/argsArgArrayOrObject';\nimport { innerFrom } from './innerFrom';\nimport { popResultSelector } from '../util/args';\nimport { createOperatorSubscriber } from '../operators/OperatorSubscriber';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nimport { createObject } from '../util/createObject';\nimport { AnyCatcher } from '../AnyCatcher';\n\n// forkJoin(any)\n// We put this first because we need to catch cases where the user has supplied\n// _exactly `any`_ as the argument. Since `any` literally matches _anything_,\n// we don't want it to randomly hit one of the other type signatures below,\n// as we have no idea at build-time what type we should be returning when given an any.\n\n/**\n * You have passed `any` here, we can't figure out if it is\n * an array or an object, so you're getting `unknown`. Use better types.\n * @param arg Something typed as `any`\n */\nexport function forkJoin<T extends AnyCatcher>(arg: T): Observable<unknown>;\n\n// forkJoin(null | undefined)\nexport function forkJoin(scheduler: null | undefined): Observable<never>;\n\n// forkJoin([a, b, c])\nexport function forkJoin(sources: readonly []): Observable<never>;\nexport function forkJoin<A extends readonly unknown[]>(sources: readonly [...ObservableInputTuple<A>]): Observable<A>;\nexport function forkJoin<A extends readonly unknown[], R>(\n  sources: readonly [...ObservableInputTuple<A>],\n  resultSelector: (...values: A) => R\n): Observable<R>;\n\n// forkJoin(a, b, c)\n/** @deprecated Pass an array of sources instead. The rest-parameters signature will be removed in v8. Details: https://rxjs.dev/deprecations/array-argument */\nexport function forkJoin<A extends readonly unknown[]>(...sources: [...ObservableInputTuple<A>]): Observable<A>;\n/** @deprecated Pass an array of sources instead. The rest-parameters signature will be removed in v8. Details: https://rxjs.dev/deprecations/array-argument */\nexport function forkJoin<A extends readonly unknown[], R>(\n  ...sourcesAndResultSelector: [...ObservableInputTuple<A>, (...values: A) => R]\n): Observable<R>;\n\n// forkJoin({a, b, c})\nexport function forkJoin(sourcesObject: { [K in any]: never }): Observable<never>;\nexport function forkJoin<T extends Record<string, ObservableInput<any>>>(\n  sourcesObject: T\n): Observable<{ [K in keyof T]: ObservedValueOf<T[K]> }>;\n\n/**\n * Accepts an `Array` of {@link ObservableInput} or a dictionary `Object` of {@link ObservableInput} and returns\n * an {@link Observable} that emits either an array of values in the exact same order as the passed array,\n * or a dictionary of values in the same shape as the passed dictionary.\n *\n * <span class=\"informal\">Wait for Observables to complete and then combine last values they emitted;\n * complete immediately if an empty array is passed.</span>\n *\n * ![](forkJoin.png)\n *\n * `forkJoin` is an operator that takes any number of input observables which can be passed either as an array\n * or a dictionary of input observables. If no input observables are provided (e.g. an empty array is passed),\n * then the resulting stream will complete immediately.\n *\n * `forkJoin` will wait for all passed observables to emit and complete and then it will emit an array or an object with last\n * values from corresponding observables.\n *\n * If you pass an array of `n` observables to the operator, then the resulting\n * array will have `n` values, where the first value is the last one emitted by the first observable,\n * second value is the last one emitted by the second observable and so on.\n *\n * If you pass a dictionary of observables to the operator, then the resulting\n * objects will have the same keys as the dictionary passed, with their last values they have emitted\n * located at the corresponding key.\n *\n * That means `forkJoin` will not emit more than once and it will complete after that. If you need to emit combined\n * values not only at the end of the lifecycle of passed observables, but also throughout it, try out {@link combineLatest}\n * or {@link zip} instead.\n *\n * In order for the resulting array to have the same length as the number of input observables, whenever any of\n * the given observables completes without emitting any value, `forkJoin` will complete at that moment as well\n * and it will not emit anything either, even if it already has some last values from other observables.\n * Conversely, if there is an observable that never completes, `forkJoin` will never complete either,\n * unless at any point some other observable completes without emitting a value, which brings us back to\n * the previous case. Overall, in order for `forkJoin` to emit a value, all given observables\n * have to emit something at least once and complete.\n *\n * If any given observable errors at some point, `forkJoin` will error as well and immediately unsubscribe\n * from the other observables.\n *\n * Optionally `forkJoin` accepts a `resultSelector` function, that will be called with values which normally\n * would land in the emitted array. Whatever is returned by the `resultSelector`, will appear in the output\n * observable instead. This means that the default `resultSelector` can be thought of as a function that takes\n * all its arguments and puts them into an array. Note that the `resultSelector` will be called only\n * when `forkJoin` is supposed to emit a result.\n *\n * ## Examples\n *\n * Use `forkJoin` with a dictionary of observable inputs\n *\n * ```ts\n * import { forkJoin, of, timer } from 'rxjs';\n *\n * const observable = forkJoin({\n *   foo: of(1, 2, 3, 4),\n *   bar: Promise.resolve(8),\n *   baz: timer(4000)\n * });\n * observable.subscribe({\n *  next: value => console.log(value),\n *  complete: () => console.log('This is how it ends!'),\n * });\n *\n * // Logs:\n * // { foo: 4, bar: 8, baz: 0 } after 4 seconds\n * // 'This is how it ends!' immediately after\n * ```\n *\n * Use `forkJoin` with an array of observable inputs\n *\n * ```ts\n * import { forkJoin, of, timer } from 'rxjs';\n *\n * const observable = forkJoin([\n *   of(1, 2, 3, 4),\n *   Promise.resolve(8),\n *   timer(4000)\n * ]);\n * observable.subscribe({\n *  next: value => console.log(value),\n *  complete: () => console.log('This is how it ends!'),\n * });\n *\n * // Logs:\n * // [4, 8, 0] after 4 seconds\n * // 'This is how it ends!' immediately after\n * ```\n *\n * @see {@link combineLatest}\n * @see {@link zip}\n *\n * @param args Any number of `ObservableInput`s provided either as an array, as an object\n * or as arguments passed directly to the operator.\n * @return Observable emitting either an array of last values emitted by passed Observables\n * or value from project function.\n */\nexport function forkJoin(...args: any[]): Observable<any> {\n  const resultSelector = popResultSelector(args);\n  const { args: sources, keys } = argsArgArrayOrObject(args);\n  const result = new Observable((subscriber) => {\n    const { length } = sources;\n    if (!length) {\n      subscriber.complete();\n      return;\n    }\n    const values = new Array(length);\n    let remainingCompletions = length;\n    let remainingEmissions = length;\n    for (let sourceIndex = 0; sourceIndex < length; sourceIndex++) {\n      let hasValue = false;\n      innerFrom(sources[sourceIndex]).subscribe(\n        createOperatorSubscriber(\n          subscriber,\n          (value) => {\n            if (!hasValue) {\n              hasValue = true;\n              remainingEmissions--;\n            }\n            values[sourceIndex] = value;\n          },\n          () => remainingCompletions--,\n          undefined,\n          () => {\n            if (!remainingCompletions || !hasValue) {\n              if (!remainingEmissions) {\n                subscriber.next(keys ? createObject(keys, values) : values);\n              }\n              subscriber.complete();\n            }\n          }\n        )\n      );\n    }\n  });\n  return resultSelector ? result.pipe(mapOneOrManyArgs(resultSelector)) : result;\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAE1C,SAASC,oBAAoB,QAAQ,8BAA8B;AACnE,SAASC,SAAS,QAAQ,aAAa;AACvC,SAASC,iBAAiB,QAAQ,cAAc;AAChD,SAASC,wBAAwB,QAAQ,iCAAiC;AAC1E,SAASC,gBAAgB,QAAQ,0BAA0B;AAC3D,SAASC,YAAY,QAAQ,sBAAsB;AAyInD,OAAM,SAAUC,QAAQA,CAAA;EAAC,IAAAC,IAAA;OAAA,IAAAC,EAAA,IAAc,EAAdA,EAAA,GAAAC,SAAA,CAAAC,MAAc,EAAdF,EAAA,EAAc;IAAdD,IAAA,CAAAC,EAAA,IAAAC,SAAA,CAAAD,EAAA;;EACvB,IAAMG,cAAc,GAAGT,iBAAiB,CAACK,IAAI,CAAC;EACxC,IAAAK,EAAA,GAA0BZ,oBAAoB,CAACO,IAAI,CAAC;IAA5CM,OAAO,GAAAD,EAAA,CAAAL,IAAA;IAAEO,IAAI,GAAAF,EAAA,CAAAE,IAA+B;EAC1D,IAAMC,MAAM,GAAG,IAAIhB,UAAU,CAAC,UAACiB,UAAU;IAC/B,IAAAN,MAAM,GAAKG,OAAO,CAAAH,MAAZ;IACd,IAAI,CAACA,MAAM,EAAE;MACXM,UAAU,CAACC,QAAQ,EAAE;MACrB;;IAEF,IAAMC,MAAM,GAAG,IAAIC,KAAK,CAACT,MAAM,CAAC;IAChC,IAAIU,oBAAoB,GAAGV,MAAM;IACjC,IAAIW,kBAAkB,GAAGX,MAAM;4BACtBY,WAAW;MAClB,IAAIC,QAAQ,GAAG,KAAK;MACpBtB,SAAS,CAACY,OAAO,CAACS,WAAW,CAAC,CAAC,CAACE,SAAS,CACvCrB,wBAAwB,CACtBa,UAAU,EACV,UAACS,KAAK;QACJ,IAAI,CAACF,QAAQ,EAAE;UACbA,QAAQ,GAAG,IAAI;UACfF,kBAAkB,EAAE;;QAEtBH,MAAM,CAACI,WAAW,CAAC,GAAGG,KAAK;MAC7B,CAAC,EACD;QAAM,OAAAL,oBAAoB,EAAE;MAAtB,CAAsB,EAC5BM,SAAS,EACT;QACE,IAAI,CAACN,oBAAoB,IAAI,CAACG,QAAQ,EAAE;UACtC,IAAI,CAACF,kBAAkB,EAAE;YACvBL,UAAU,CAACW,IAAI,CAACb,IAAI,GAAGT,YAAY,CAACS,IAAI,EAAEI,MAAM,CAAC,GAAGA,MAAM,CAAC;;UAE7DF,UAAU,CAACC,QAAQ,EAAE;;MAEzB,CAAC,CACF,CACF;;IAvBH,KAAK,IAAIK,WAAW,GAAG,CAAC,EAAEA,WAAW,GAAGZ,MAAM,EAAEY,WAAW,EAAE;cAApDA,WAAW;;EAyBtB,CAAC,CAAC;EACF,OAAOX,cAAc,GAAGI,MAAM,CAACa,IAAI,CAACxB,gBAAgB,CAACO,cAAc,CAAC,CAAC,GAAGI,MAAM;AAChF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}