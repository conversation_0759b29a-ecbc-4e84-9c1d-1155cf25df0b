{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { Subject } from './Subject';\nimport { dateTimestampProvider } from './scheduler/dateTimestampProvider';\nvar ReplaySubject = function (_super) {\n  __extends(ReplaySubject, _super);\n  function ReplaySubject(_bufferSize, _windowTime, _timestampProvider) {\n    if (_bufferSize === void 0) {\n      _bufferSize = Infinity;\n    }\n    if (_windowTime === void 0) {\n      _windowTime = Infinity;\n    }\n    if (_timestampProvider === void 0) {\n      _timestampProvider = dateTimestampProvider;\n    }\n    var _this = _super.call(this) || this;\n    _this._bufferSize = _bufferSize;\n    _this._windowTime = _windowTime;\n    _this._timestampProvider = _timestampProvider;\n    _this._buffer = [];\n    _this._infiniteTimeWindow = true;\n    _this._infiniteTimeWindow = _windowTime === Infinity;\n    _this._bufferSize = Math.max(1, _bufferSize);\n    _this._windowTime = Math.max(1, _windowTime);\n    return _this;\n  }\n  ReplaySubject.prototype.next = function (value) {\n    var _a = this,\n      isStopped = _a.isStopped,\n      _buffer = _a._buffer,\n      _infiniteTimeWindow = _a._infiniteTimeWindow,\n      _timestampProvider = _a._timestampProvider,\n      _windowTime = _a._windowTime;\n    if (!isStopped) {\n      _buffer.push(value);\n      !_infiniteTimeWindow && _buffer.push(_timestampProvider.now() + _windowTime);\n    }\n    this._trimBuffer();\n    _super.prototype.next.call(this, value);\n  };\n  ReplaySubject.prototype._subscribe = function (subscriber) {\n    this._throwIfClosed();\n    this._trimBuffer();\n    var subscription = this._innerSubscribe(subscriber);\n    var _a = this,\n      _infiniteTimeWindow = _a._infiniteTimeWindow,\n      _buffer = _a._buffer;\n    var copy = _buffer.slice();\n    for (var i = 0; i < copy.length && !subscriber.closed; i += _infiniteTimeWindow ? 1 : 2) {\n      subscriber.next(copy[i]);\n    }\n    this._checkFinalizedStatuses(subscriber);\n    return subscription;\n  };\n  ReplaySubject.prototype._trimBuffer = function () {\n    var _a = this,\n      _bufferSize = _a._bufferSize,\n      _timestampProvider = _a._timestampProvider,\n      _buffer = _a._buffer,\n      _infiniteTimeWindow = _a._infiniteTimeWindow;\n    var adjustedBufferSize = (_infiniteTimeWindow ? 1 : 2) * _bufferSize;\n    _bufferSize < Infinity && adjustedBufferSize < _buffer.length && _buffer.splice(0, _buffer.length - adjustedBufferSize);\n    if (!_infiniteTimeWindow) {\n      var now = _timestampProvider.now();\n      var last = 0;\n      for (var i = 1; i < _buffer.length && _buffer[i] <= now; i += 2) {\n        last = i;\n      }\n      last && _buffer.splice(0, last + 1);\n    }\n  };\n  return ReplaySubject;\n}(Subject);\nexport { ReplaySubject };", "map": {"version": 3, "names": ["Subject", "dateTimestampProvider", "ReplaySubject", "_super", "__extends", "_bufferSize", "_windowTime", "_timestampProvider", "Infinity", "_this", "call", "_buffer", "_infiniteTimeWindow", "Math", "max", "prototype", "next", "value", "_a", "isStopped", "push", "now", "_trimBuffer", "_subscribe", "subscriber", "_throwIfClosed", "subscription", "_innerSubscribe", "copy", "slice", "i", "length", "closed", "_checkFinalizedStatuses", "adjustedBufferSize", "splice", "last"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\ReplaySubject.ts"], "sourcesContent": ["import { Subject } from './Subject';\nimport { TimestampProvider } from './types';\nimport { Subscriber } from './Subscriber';\nimport { Subscription } from './Subscription';\nimport { dateTimestampProvider } from './scheduler/dateTimestampProvider';\n\n/**\n * A variant of {@link Subject} that \"replays\" old values to new subscribers by emitting them when they first subscribe.\n *\n * `ReplaySubject` has an internal buffer that will store a specified number of values that it has observed. Like `Subject`,\n * `ReplaySubject` \"observes\" values by having them passed to its `next` method. When it observes a value, it will store that\n * value for a time determined by the configuration of the `ReplaySubject`, as passed to its constructor.\n *\n * When a new subscriber subscribes to the `ReplaySubject` instance, it will synchronously emit all values in its buffer in\n * a First-In-First-Out (FIFO) manner. The `ReplaySubject` will also complete, if it has observed completion; and it will\n * error if it has observed an error.\n *\n * There are two main configuration items to be concerned with:\n *\n * 1. `bufferSize` - This will determine how many items are stored in the buffer, defaults to infinite.\n * 2. `windowTime` - The amount of time to hold a value in the buffer before removing it from the buffer.\n *\n * Both configurations may exist simultaneously. So if you would like to buffer a maximum of 3 values, as long as the values\n * are less than 2 seconds old, you could do so with a `new ReplaySubject(3, 2000)`.\n *\n * ### Differences with BehaviorSubject\n *\n * `BehaviorSubject` is similar to `new ReplaySubject(1)`, with a couple of exceptions:\n *\n * 1. `BehaviorSubject` comes \"primed\" with a single value upon construction.\n * 2. `ReplaySubject` will replay values, even after observing an error, where `BehaviorSubject` will not.\n *\n * @see {@link Subject}\n * @see {@link BehaviorSubject}\n * @see {@link shareReplay}\n */\nexport class ReplaySubject<T> extends Subject<T> {\n  private _buffer: (T | number)[] = [];\n  private _infiniteTimeWindow = true;\n\n  /**\n   * @param _bufferSize The size of the buffer to replay on subscription\n   * @param _windowTime The amount of time the buffered items will stay buffered\n   * @param _timestampProvider An object with a `now()` method that provides the current timestamp. This is used to\n   * calculate the amount of time something has been buffered.\n   */\n  constructor(\n    private _bufferSize = Infinity,\n    private _windowTime = Infinity,\n    private _timestampProvider: TimestampProvider = dateTimestampProvider\n  ) {\n    super();\n    this._infiniteTimeWindow = _windowTime === Infinity;\n    this._bufferSize = Math.max(1, _bufferSize);\n    this._windowTime = Math.max(1, _windowTime);\n  }\n\n  next(value: T): void {\n    const { isStopped, _buffer, _infiniteTimeWindow, _timestampProvider, _windowTime } = this;\n    if (!isStopped) {\n      _buffer.push(value);\n      !_infiniteTimeWindow && _buffer.push(_timestampProvider.now() + _windowTime);\n    }\n    this._trimBuffer();\n    super.next(value);\n  }\n\n  /** @internal */\n  protected _subscribe(subscriber: Subscriber<T>): Subscription {\n    this._throwIfClosed();\n    this._trimBuffer();\n\n    const subscription = this._innerSubscribe(subscriber);\n\n    const { _infiniteTimeWindow, _buffer } = this;\n    // We use a copy here, so reentrant code does not mutate our array while we're\n    // emitting it to a new subscriber.\n    const copy = _buffer.slice();\n    for (let i = 0; i < copy.length && !subscriber.closed; i += _infiniteTimeWindow ? 1 : 2) {\n      subscriber.next(copy[i] as T);\n    }\n\n    this._checkFinalizedStatuses(subscriber);\n\n    return subscription;\n  }\n\n  private _trimBuffer() {\n    const { _bufferSize, _timestampProvider, _buffer, _infiniteTimeWindow } = this;\n    // If we don't have an infinite buffer size, and we're over the length,\n    // use splice to truncate the old buffer values off. Note that we have to\n    // double the size for instances where we're not using an infinite time window\n    // because we're storing the values and the timestamps in the same array.\n    const adjustedBufferSize = (_infiniteTimeWindow ? 1 : 2) * _bufferSize;\n    _bufferSize < Infinity && adjustedBufferSize < _buffer.length && _buffer.splice(0, _buffer.length - adjustedBufferSize);\n\n    // Now, if we're not in an infinite time window, remove all values where the time is\n    // older than what is allowed.\n    if (!_infiniteTimeWindow) {\n      const now = _timestampProvider.now();\n      let last = 0;\n      // Search the array for the first timestamp that isn't expired and\n      // truncate the buffer up to that point.\n      for (let i = 1; i < _buffer.length && (_buffer[i] as number) <= now; i += 2) {\n        last = i;\n      }\n      last && _buffer.splice(0, last + 1);\n    }\n  }\n}\n"], "mappings": ";AAAA,SAASA,OAAO,QAAQ,WAAW;AAInC,SAASC,qBAAqB,QAAQ,mCAAmC;AAgCzE,IAAAC,aAAA,aAAAC,MAAA;EAAsCC,SAAA,CAAAF,aAAA,EAAAC,MAAA;EAUpC,SAAAD,cACUG,WAAsB,EACtBC,WAAsB,EACtBC,kBAA6D;IAF7D,IAAAF,WAAA;MAAAA,WAAA,GAAAG,QAAsB;IAAA;IACtB,IAAAF,WAAA;MAAAA,WAAA,GAAAE,QAAsB;IAAA;IACtB,IAAAD,kBAAA;MAAAA,kBAAA,GAAAN,qBAA6D;IAAA;IAHvE,IAAAQ,KAAA,GAKEN,MAAA,CAAAO,IAAA,MAAO;IAJCD,KAAA,CAAAJ,WAAW,GAAXA,WAAW;IACXI,KAAA,CAAAH,WAAW,GAAXA,WAAW;IACXG,KAAA,CAAAF,kBAAkB,GAAlBA,kBAAkB;IAZpBE,KAAA,CAAAE,OAAO,GAAmB,EAAE;IAC5BF,KAAA,CAAAG,mBAAmB,GAAG,IAAI;IAchCH,KAAI,CAACG,mBAAmB,GAAGN,WAAW,KAAKE,QAAQ;IACnDC,KAAI,CAACJ,WAAW,GAAGQ,IAAI,CAACC,GAAG,CAAC,CAAC,EAAET,WAAW,CAAC;IAC3CI,KAAI,CAACH,WAAW,GAAGO,IAAI,CAACC,GAAG,CAAC,CAAC,EAAER,WAAW,CAAC;;EAC7C;EAEAJ,aAAA,CAAAa,SAAA,CAAAC,IAAI,GAAJ,UAAKC,KAAQ;IACL,IAAAC,EAAA,GAA+E,IAAI;MAAjFC,SAAS,GAAAD,EAAA,CAAAC,SAAA;MAAER,OAAO,GAAAO,EAAA,CAAAP,OAAA;MAAEC,mBAAmB,GAAAM,EAAA,CAAAN,mBAAA;MAAEL,kBAAkB,GAAAW,EAAA,CAAAX,kBAAA;MAAED,WAAW,GAAAY,EAAA,CAAAZ,WAAS;IACzF,IAAI,CAACa,SAAS,EAAE;MACdR,OAAO,CAACS,IAAI,CAACH,KAAK,CAAC;MACnB,CAACL,mBAAmB,IAAID,OAAO,CAACS,IAAI,CAACb,kBAAkB,CAACc,GAAG,EAAE,GAAGf,WAAW,CAAC;;IAE9E,IAAI,CAACgB,WAAW,EAAE;IAClBnB,MAAA,CAAAY,SAAA,CAAMC,IAAI,CAAAN,IAAA,OAACO,KAAK,CAAC;EACnB,CAAC;EAGSf,aAAA,CAAAa,SAAA,CAAAQ,UAAU,GAApB,UAAqBC,UAAyB;IAC5C,IAAI,CAACC,cAAc,EAAE;IACrB,IAAI,CAACH,WAAW,EAAE;IAElB,IAAMI,YAAY,GAAG,IAAI,CAACC,eAAe,CAACH,UAAU,CAAC;IAE/C,IAAAN,EAAA,GAAmC,IAAI;MAArCN,mBAAmB,GAAAM,EAAA,CAAAN,mBAAA;MAAED,OAAO,GAAAO,EAAA,CAAAP,OAAS;IAG7C,IAAMiB,IAAI,GAAGjB,OAAO,CAACkB,KAAK,EAAE;IAC5B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,IAAI,CAACG,MAAM,IAAI,CAACP,UAAU,CAACQ,MAAM,EAAEF,CAAC,IAAIlB,mBAAmB,GAAG,CAAC,GAAG,CAAC,EAAE;MACvFY,UAAU,CAACR,IAAI,CAACY,IAAI,CAACE,CAAC,CAAM,CAAC;;IAG/B,IAAI,CAACG,uBAAuB,CAACT,UAAU,CAAC;IAExC,OAAOE,YAAY;EACrB,CAAC;EAEOxB,aAAA,CAAAa,SAAA,CAAAO,WAAW,GAAnB;IACQ,IAAAJ,EAAA,GAAoE,IAAI;MAAtEb,WAAW,GAAAa,EAAA,CAAAb,WAAA;MAAEE,kBAAkB,GAAAW,EAAA,CAAAX,kBAAA;MAAEI,OAAO,GAAAO,EAAA,CAAAP,OAAA;MAAEC,mBAAmB,GAAAM,EAAA,CAAAN,mBAAS;IAK9E,IAAMsB,kBAAkB,GAAG,CAACtB,mBAAmB,GAAG,CAAC,GAAG,CAAC,IAAIP,WAAW;IACtEA,WAAW,GAAGG,QAAQ,IAAI0B,kBAAkB,GAAGvB,OAAO,CAACoB,MAAM,IAAIpB,OAAO,CAACwB,MAAM,CAAC,CAAC,EAAExB,OAAO,CAACoB,MAAM,GAAGG,kBAAkB,CAAC;IAIvH,IAAI,CAACtB,mBAAmB,EAAE;MACxB,IAAMS,GAAG,GAAGd,kBAAkB,CAACc,GAAG,EAAE;MACpC,IAAIe,IAAI,GAAG,CAAC;MAGZ,KAAK,IAAIN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnB,OAAO,CAACoB,MAAM,IAAKpB,OAAO,CAACmB,CAAC,CAAY,IAAIT,GAAG,EAAES,CAAC,IAAI,CAAC,EAAE;QAC3EM,IAAI,GAAGN,CAAC;;MAEVM,IAAI,IAAIzB,OAAO,CAACwB,MAAM,CAAC,CAAC,EAAEC,IAAI,GAAG,CAAC,CAAC;;EAEvC,CAAC;EACH,OAAAlC,aAAC;AAAD,CAAC,CAzEqCF,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}