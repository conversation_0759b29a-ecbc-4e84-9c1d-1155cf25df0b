{"ast": null, "code": "import { asyncScheduler } from '../scheduler/async';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function timeInterval(scheduler) {\n  if (scheduler === void 0) {\n    scheduler = asyncScheduler;\n  }\n  return operate(function (source, subscriber) {\n    var last = scheduler.now();\n    source.subscribe(createOperatorSubscriber(subscriber, function (value) {\n      var now = scheduler.now();\n      var interval = now - last;\n      last = now;\n      subscriber.next(new TimeInterval(value, interval));\n    }));\n  });\n}\nvar TimeInterval = function () {\n  function TimeInterval(value, interval) {\n    this.value = value;\n    this.interval = interval;\n  }\n  return TimeInterval;\n}();\nexport { TimeInterval };", "map": {"version": 3, "names": ["asyncScheduler", "operate", "createOperatorSubscriber", "timeInterval", "scheduler", "source", "subscriber", "last", "now", "subscribe", "value", "interval", "next", "TimeInterval"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\timeInterval.ts"], "sourcesContent": ["import { asyncScheduler } from '../scheduler/async';\nimport { SchedulerLike, OperatorFunction } from '../types';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\n\n/**\n * Emits an object containing the current value, and the time that has\n * passed between emitting the current value and the previous value, which is\n * calculated by using the provided `scheduler`'s `now()` method to retrieve\n * the current time at each emission, then calculating the difference. The `scheduler`\n * defaults to {@link asyncScheduler}, so by default, the `interval` will be in\n * milliseconds.\n *\n * <span class=\"informal\">Convert an Observable that emits items into one that\n * emits indications of the amount of time elapsed between those emissions.</span>\n *\n * ![](timeInterval.png)\n *\n * ## Example\n *\n * Emit interval between current value with the last value\n *\n * ```ts\n * import { interval, timeInterval } from 'rxjs';\n *\n * const seconds = interval(1000);\n *\n * seconds\n *   .pipe(timeInterval())\n *   .subscribe(value => console.log(value));\n *\n * // NOTE: The values will never be this precise,\n * // intervals created with `interval` or `setInterval`\n * // are non-deterministic.\n *\n * // { value: 0, interval: 1000 }\n * // { value: 1, interval: 1000 }\n * // { value: 2, interval: 1000 }\n * ```\n *\n * @param scheduler Scheduler used to get the current time.\n * @return A function that returns an Observable that emits information about\n * value and interval.\n */\nexport function timeInterval<T>(scheduler: SchedulerLike = asyncScheduler): OperatorFunction<T, TimeInterval<T>> {\n  return operate((source, subscriber) => {\n    let last = scheduler.now();\n    source.subscribe(\n      createOperatorSubscriber(subscriber, (value) => {\n        const now = scheduler.now();\n        const interval = now - last;\n        last = now;\n        subscriber.next(new TimeInterval(value, interval));\n      })\n    );\n  });\n}\n\n// TODO(benlesh): make this an interface, export the interface, but not the implemented class,\n// there's no reason users should be manually creating this type.\n\nexport class TimeInterval<T> {\n  /**\n   * @deprecated Internal implementation detail, do not construct directly. Will be made an interface in v8.\n   */\n  constructor(public value: T, public interval: number) {}\n}\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,oBAAoB;AAEnD,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAyC/D,OAAM,SAAUC,YAAYA,CAAIC,SAAyC;EAAzC,IAAAA,SAAA;IAAAA,SAAA,GAAAJ,cAAyC;EAAA;EACvE,OAAOC,OAAO,CAAC,UAACI,MAAM,EAAEC,UAAU;IAChC,IAAIC,IAAI,GAAGH,SAAS,CAACI,GAAG,EAAE;IAC1BH,MAAM,CAACI,SAAS,CACdP,wBAAwB,CAACI,UAAU,EAAE,UAACI,KAAK;MACzC,IAAMF,GAAG,GAAGJ,SAAS,CAACI,GAAG,EAAE;MAC3B,IAAMG,QAAQ,GAAGH,GAAG,GAAGD,IAAI;MAC3BA,IAAI,GAAGC,GAAG;MACVF,UAAU,CAACM,IAAI,CAAC,IAAIC,YAAY,CAACH,KAAK,EAAEC,QAAQ,CAAC,CAAC;IACpD,CAAC,CAAC,CACH;EACH,CAAC,CAAC;AACJ;AAKA,IAAAE,YAAA;EAIE,SAAAA,aAAmBH,KAAQ,EAASC,QAAgB;IAAjC,KAAAD,KAAK,GAALA,KAAK;IAAY,KAAAC,QAAQ,GAARA,QAAQ;EAAW;EACzD,OAAAE,YAAC;AAAD,CAAC,CALD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}