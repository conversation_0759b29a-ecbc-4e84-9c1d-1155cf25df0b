{"ast": null, "code": "function _typeof(obj) {\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    _typeof = function _typeof(obj) {\n      return typeof obj;\n    };\n  } else {\n    _typeof = function _typeof(obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n  }\n  return _typeof(obj);\n}\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    });\n    keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n    if (i % 2) {\n      ownKeys(source, true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(source).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n  return target;\n}\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\n\n/*\n  autoMergeLevel2: \n    - merges 2 level of substate\n    - skips substate if already modified\n    - this is essentially redux-perist v4 behavior\n*/\nexport default function autoMergeLevel2(inboundState, originalState, reducedState, _ref) {\n  var debug = _ref.debug;\n  var newState = _objectSpread({}, reducedState); // only rehydrate if inboundState exists and is an object\n\n  if (inboundState && _typeof(inboundState) === 'object') {\n    Object.keys(inboundState).forEach(function (key) {\n      // ignore _persist data\n      if (key === '_persist') return; // if reducer modifies substate, skip auto rehydration\n\n      if (originalState[key] !== reducedState[key]) {\n        if (process.env.NODE_ENV !== 'production' && debug) console.log('redux-persist/stateReconciler: sub state for key `%s` modified, skipping.', key);\n        return;\n      }\n      if (isPlainEnoughObject(reducedState[key])) {\n        // if object is plain enough shallow merge the new values (hence \"Level2\")\n        newState[key] = _objectSpread({}, newState[key], {}, inboundState[key]);\n        return;\n      } // otherwise hard set\n\n      newState[key] = inboundState[key];\n    });\n  }\n  if (process.env.NODE_ENV !== 'production' && debug && inboundState && _typeof(inboundState) === 'object') console.log(\"redux-persist/stateReconciler: rehydrated keys '\".concat(Object.keys(inboundState).join(', '), \"'\"));\n  return newState;\n}\nfunction isPlainEnoughObject(o) {\n  return o !== null && !Array.isArray(o) && _typeof(o) === 'object';\n}", "map": {"version": 3, "names": ["_typeof", "obj", "Symbol", "iterator", "constructor", "prototype", "ownKeys", "object", "enumerableOnly", "keys", "Object", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "target", "i", "arguments", "length", "source", "for<PERSON>ach", "key", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "value", "configurable", "writable", "autoMergeLevel2", "inboundState", "originalState", "reducedState", "_ref", "debug", "newState", "process", "env", "NODE_ENV", "console", "log", "isPlainEnoughObject", "concat", "join", "o", "Array", "isArray"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/node_modules/redux-persist/es/stateReconciler/autoMergeLevel2.js"], "sourcesContent": ["function _typeof(obj) { if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; } return _typeof(obj); }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n/*\n  autoMergeLevel2: \n    - merges 2 level of substate\n    - skips substate if already modified\n    - this is essentially redux-perist v4 behavior\n*/\nexport default function autoMergeLevel2(inboundState, originalState, reducedState, _ref) {\n  var debug = _ref.debug;\n\n  var newState = _objectSpread({}, reducedState); // only rehydrate if inboundState exists and is an object\n\n\n  if (inboundState && _typeof(inboundState) === 'object') {\n    Object.keys(inboundState).forEach(function (key) {\n      // ignore _persist data\n      if (key === '_persist') return; // if reducer modifies substate, skip auto rehydration\n\n      if (originalState[key] !== reducedState[key]) {\n        if (process.env.NODE_ENV !== 'production' && debug) console.log('redux-persist/stateReconciler: sub state for key `%s` modified, skipping.', key);\n        return;\n      }\n\n      if (isPlainEnoughObject(reducedState[key])) {\n        // if object is plain enough shallow merge the new values (hence \"Level2\")\n        newState[key] = _objectSpread({}, newState[key], {}, inboundState[key]);\n        return;\n      } // otherwise hard set\n\n\n      newState[key] = inboundState[key];\n    });\n  }\n\n  if (process.env.NODE_ENV !== 'production' && debug && inboundState && _typeof(inboundState) === 'object') console.log(\"redux-persist/stateReconciler: rehydrated keys '\".concat(Object.keys(inboundState).join(', '), \"'\"));\n  return newState;\n}\n\nfunction isPlainEnoughObject(o) {\n  return o !== null && !Array.isArray(o) && _typeof(o) === 'object';\n}"], "mappings": "AAAA,SAASA,OAAOA,CAACC,GAAG,EAAE;EAAE,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAI,OAAOA,MAAM,CAACC,QAAQ,KAAK,QAAQ,EAAE;IAAEH,OAAO,GAAG,SAASA,OAAOA,CAACC,GAAG,EAAE;MAAE,OAAO,OAAOA,GAAG;IAAE,CAAC;EAAE,CAAC,MAAM;IAAED,OAAO,GAAG,SAASA,OAAOA,CAACC,GAAG,EAAE;MAAE,OAAOA,GAAG,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAID,GAAG,CAACG,WAAW,KAAKF,MAAM,IAAID,GAAG,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,GAAG;IAAE,CAAC;EAAE;EAAE,OAAOD,OAAO,CAACC,GAAG,CAAC;AAAE;AAE9V,SAASK,OAAOA,CAACC,MAAM,EAAEC,cAAc,EAAE;EAAE,IAAIC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACF,MAAM,CAAC;EAAE,IAAIG,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIC,OAAO,GAAGF,MAAM,CAACC,qBAAqB,CAACJ,MAAM,CAAC;IAAE,IAAIC,cAAc,EAAEI,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;MAAE,OAAOJ,MAAM,CAACK,wBAAwB,CAACR,MAAM,EAAEO,GAAG,CAAC,CAACE,UAAU;IAAE,CAAC,CAAC;IAAEP,IAAI,CAACQ,IAAI,CAACC,KAAK,CAACT,IAAI,EAAEG,OAAO,CAAC;EAAE;EAAE,OAAOH,IAAI;AAAE;AAEpV,SAASU,aAAaA,CAACC,MAAM,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC,IAAI,IAAI,GAAGC,SAAS,CAACD,CAAC,CAAC,GAAG,CAAC,CAAC;IAAE,IAAIA,CAAC,GAAG,CAAC,EAAE;MAAEf,OAAO,CAACkB,MAAM,EAAE,IAAI,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;QAAEC,eAAe,CAACP,MAAM,EAAEM,GAAG,EAAEF,MAAM,CAACE,GAAG,CAAC,CAAC;MAAE,CAAC,CAAC;IAAE,CAAC,MAAM,IAAIhB,MAAM,CAACkB,yBAAyB,EAAE;MAAElB,MAAM,CAACmB,gBAAgB,CAACT,MAAM,EAAEV,MAAM,CAACkB,yBAAyB,CAACJ,MAAM,CAAC,CAAC;IAAE,CAAC,MAAM;MAAElB,OAAO,CAACkB,MAAM,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;QAAEhB,MAAM,CAACoB,cAAc,CAACV,MAAM,EAAEM,GAAG,EAAEhB,MAAM,CAACK,wBAAwB,CAACS,MAAM,EAAEE,GAAG,CAAC,CAAC;MAAE,CAAC,CAAC;IAAE;EAAE;EAAE,OAAON,MAAM;AAAE;AAErgB,SAASO,eAAeA,CAAC1B,GAAG,EAAEyB,GAAG,EAAEK,KAAK,EAAE;EAAE,IAAIL,GAAG,IAAIzB,GAAG,EAAE;IAAES,MAAM,CAACoB,cAAc,CAAC7B,GAAG,EAAEyB,GAAG,EAAE;MAAEK,KAAK,EAAEA,KAAK;MAAEf,UAAU,EAAE,IAAI;MAAEgB,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAEhC,GAAG,CAACyB,GAAG,CAAC,GAAGK,KAAK;EAAE;EAAE,OAAO9B,GAAG;AAAE;;AAEhN;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASiC,eAAeA,CAACC,YAAY,EAAEC,aAAa,EAAEC,YAAY,EAAEC,IAAI,EAAE;EACvF,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;EAEtB,IAAIC,QAAQ,GAAGrB,aAAa,CAAC,CAAC,CAAC,EAAEkB,YAAY,CAAC,CAAC,CAAC;;EAGhD,IAAIF,YAAY,IAAInC,OAAO,CAACmC,YAAY,CAAC,KAAK,QAAQ,EAAE;IACtDzB,MAAM,CAACD,IAAI,CAAC0B,YAAY,CAAC,CAACV,OAAO,CAAC,UAAUC,GAAG,EAAE;MAC/C;MACA,IAAIA,GAAG,KAAK,UAAU,EAAE,OAAO,CAAC;;MAEhC,IAAIU,aAAa,CAACV,GAAG,CAAC,KAAKW,YAAY,CAACX,GAAG,CAAC,EAAE;QAC5C,IAAIe,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAIJ,KAAK,EAAEK,OAAO,CAACC,GAAG,CAAC,2EAA2E,EAAEnB,GAAG,CAAC;QACjJ;MACF;MAEA,IAAIoB,mBAAmB,CAACT,YAAY,CAACX,GAAG,CAAC,CAAC,EAAE;QAC1C;QACAc,QAAQ,CAACd,GAAG,CAAC,GAAGP,aAAa,CAAC,CAAC,CAAC,EAAEqB,QAAQ,CAACd,GAAG,CAAC,EAAE,CAAC,CAAC,EAAES,YAAY,CAACT,GAAG,CAAC,CAAC;QACvE;MACF,CAAC,CAAC;;MAGFc,QAAQ,CAACd,GAAG,CAAC,GAAGS,YAAY,CAACT,GAAG,CAAC;IACnC,CAAC,CAAC;EACJ;EAEA,IAAIe,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAIJ,KAAK,IAAIJ,YAAY,IAAInC,OAAO,CAACmC,YAAY,CAAC,KAAK,QAAQ,EAAES,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAACE,MAAM,CAACrC,MAAM,CAACD,IAAI,CAAC0B,YAAY,CAAC,CAACa,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;EAC3N,OAAOR,QAAQ;AACjB;AAEA,SAASM,mBAAmBA,CAACG,CAAC,EAAE;EAC9B,OAAOA,CAAC,KAAK,IAAI,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,CAAC,CAAC,IAAIjD,OAAO,CAACiD,CAAC,CAAC,KAAK,QAAQ;AACnE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}