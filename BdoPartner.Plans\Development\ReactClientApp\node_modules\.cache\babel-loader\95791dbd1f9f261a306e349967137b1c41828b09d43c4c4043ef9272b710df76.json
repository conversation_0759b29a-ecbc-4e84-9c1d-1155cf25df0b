{"ast": null, "code": "import { Observable } from '../Observable';\nimport { innerFrom } from './innerFrom';\nimport { argsOrArgArray } from '../util/argsOrArgArray';\nimport { createOperatorSubscriber } from '../operators/OperatorSubscriber';\nexport function race() {\n  var sources = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    sources[_i] = arguments[_i];\n  }\n  sources = argsOrArgArray(sources);\n  return sources.length === 1 ? innerFrom(sources[0]) : new Observable(raceInit(sources));\n}\nexport function raceInit(sources) {\n  return function (subscriber) {\n    var subscriptions = [];\n    var _loop_1 = function (i) {\n      subscriptions.push(innerFrom(sources[i]).subscribe(createOperatorSubscriber(subscriber, function (value) {\n        if (subscriptions) {\n          for (var s = 0; s < subscriptions.length; s++) {\n            s !== i && subscriptions[s].unsubscribe();\n          }\n          subscriptions = null;\n        }\n        subscriber.next(value);\n      })));\n    };\n    for (var i = 0; subscriptions && !subscriber.closed && i < sources.length; i++) {\n      _loop_1(i);\n    }\n  };\n}", "map": {"version": 3, "names": ["Observable", "innerFrom", "argsOrArgArray", "createOperatorSubscriber", "race", "sources", "_i", "arguments", "length", "raceInit", "subscriber", "subscriptions", "i", "push", "subscribe", "value", "s", "unsubscribe", "next", "closed"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\observable\\race.ts"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { innerFrom } from './innerFrom';\nimport { Subscription } from '../Subscription';\nimport { ObservableInput, ObservableInputTuple } from '../types';\nimport { argsOrArgArray } from '../util/argsOrArgArray';\nimport { createOperatorSubscriber } from '../operators/OperatorSubscriber';\nimport { Subscriber } from '../Subscriber';\n\nexport function race<T extends readonly unknown[]>(inputs: [...ObservableInputTuple<T>]): Observable<T[number]>;\nexport function race<T extends readonly unknown[]>(...inputs: [...ObservableInputTuple<T>]): Observable<T[number]>;\n\n/**\n * Returns an observable that mirrors the first source observable to emit an item.\n *\n * ![](race.png)\n *\n * `race` returns an observable, that when subscribed to, subscribes to all source observables immediately.\n * As soon as one of the source observables emits a value, the result unsubscribes from the other sources.\n * The resulting observable will forward all notifications, including error and completion, from the \"winning\"\n * source observable.\n *\n * If one of the used source observable throws an errors before a first notification\n * the race operator will also throw an error, no matter if another source observable\n * could potentially win the race.\n *\n * `race` can be useful for selecting the response from the fastest network connection for\n * HTTP or WebSockets. `race` can also be useful for switching observable context based on user\n * input.\n *\n * ## Example\n *\n * Subscribes to the observable that was the first to start emitting.\n *\n * ```ts\n * import { interval, map, race } from 'rxjs';\n *\n * const obs1 = interval(7000).pipe(map(() => 'slow one'));\n * const obs2 = interval(3000).pipe(map(() => 'fast one'));\n * const obs3 = interval(5000).pipe(map(() => 'medium one'));\n *\n * race(obs1, obs2, obs3)\n *   .subscribe(winner => console.log(winner));\n *\n * // Outputs\n * // a series of 'fast one'\n * ```\n *\n * @param sources Used to race for which `ObservableInput` emits first.\n * @return An Observable that mirrors the output of the first Observable to emit an item.\n */\nexport function race<T>(...sources: (ObservableInput<T> | ObservableInput<T>[])[]): Observable<any> {\n  sources = argsOrArgArray(sources);\n  // If only one source was passed, just return it. Otherwise return the race.\n  return sources.length === 1 ? innerFrom(sources[0] as ObservableInput<T>) : new Observable<T>(raceInit(sources as ObservableInput<T>[]));\n}\n\n/**\n * An observable initializer function for both the static version and the\n * operator version of race.\n * @param sources The sources to race\n */\nexport function raceInit<T>(sources: ObservableInput<T>[]) {\n  return (subscriber: Subscriber<T>) => {\n    let subscriptions: Subscription[] = [];\n\n    // Subscribe to all of the sources. Note that we are checking `subscriptions` here\n    // Is is an array of all actively \"racing\" subscriptions, and it is `null` after the\n    // race has been won. So, if we have racer that synchronously \"wins\", this loop will\n    // stop before it subscribes to any more.\n    for (let i = 0; subscriptions && !subscriber.closed && i < sources.length; i++) {\n      subscriptions.push(\n        innerFrom(sources[i] as ObservableInput<T>).subscribe(\n          createOperatorSubscriber(subscriber, (value) => {\n            if (subscriptions) {\n              // We're still racing, but we won! So unsubscribe\n              // all other subscriptions that we have, except this one.\n              for (let s = 0; s < subscriptions.length; s++) {\n                s !== i && subscriptions[s].unsubscribe();\n              }\n              subscriptions = null!;\n            }\n            subscriber.next(value);\n          })\n        )\n      );\n    }\n  };\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,SAAS,QAAQ,aAAa;AAGvC,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,wBAAwB,QAAQ,iCAAiC;AA6C1E,OAAM,SAAUC,IAAIA,CAAA;EAAI,IAAAC,OAAA;OAAA,IAAAC,EAAA,IAAyD,EAAzDA,EAAA,GAAAC,SAAA,CAAAC,MAAyD,EAAzDF,EAAA,EAAyD;IAAzDD,OAAA,CAAAC,EAAA,IAAAC,SAAA,CAAAD,EAAA;;EACtBD,OAAO,GAAGH,cAAc,CAACG,OAAO,CAAC;EAEjC,OAAOA,OAAO,CAACG,MAAM,KAAK,CAAC,GAAGP,SAAS,CAACI,OAAO,CAAC,CAAC,CAAuB,CAAC,GAAG,IAAIL,UAAU,CAAIS,QAAQ,CAACJ,OAA+B,CAAC,CAAC;AAC1I;AAOA,OAAM,SAAUI,QAAQA,CAAIJ,OAA6B;EACvD,OAAO,UAACK,UAAyB;IAC/B,IAAIC,aAAa,GAAmB,EAAE;4BAM7BC,CAAC;MACRD,aAAa,CAACE,IAAI,CAChBZ,SAAS,CAACI,OAAO,CAACO,CAAC,CAAuB,CAAC,CAACE,SAAS,CACnDX,wBAAwB,CAACO,UAAU,EAAE,UAACK,KAAK;QACzC,IAAIJ,aAAa,EAAE;UAGjB,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,aAAa,CAACH,MAAM,EAAEQ,CAAC,EAAE,EAAE;YAC7CA,CAAC,KAAKJ,CAAC,IAAID,aAAa,CAACK,CAAC,CAAC,CAACC,WAAW,EAAE;;UAE3CN,aAAa,GAAG,IAAK;;QAEvBD,UAAU,CAACQ,IAAI,CAACH,KAAK,CAAC;MACxB,CAAC,CAAC,CACH,CACF;;IAfH,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAED,aAAa,IAAI,CAACD,UAAU,CAACS,MAAM,IAAIP,CAAC,GAAGP,OAAO,CAACG,MAAM,EAAEI,CAAC,EAAE;cAArEA,CAAC;;EAiBZ,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}