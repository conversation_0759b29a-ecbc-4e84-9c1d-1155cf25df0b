{"ast": null, "code": "import { operate } from '../util/lift';\nimport { noop } from '../util/noop';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nexport function bufferWhen(closingSelector) {\n  return operate(function (source, subscriber) {\n    var buffer = null;\n    var closingSubscriber = null;\n    var openBuffer = function () {\n      closingSubscriber === null || closingSubscriber === void 0 ? void 0 : closingSubscriber.unsubscribe();\n      var b = buffer;\n      buffer = [];\n      b && subscriber.next(b);\n      innerFrom(closingSelector()).subscribe(closingSubscriber = createOperatorSubscriber(subscriber, openBuffer, noop));\n    };\n    openBuffer();\n    source.subscribe(createOperatorSubscriber(subscriber, function (value) {\n      return buffer === null || buffer === void 0 ? void 0 : buffer.push(value);\n    }, function () {\n      buffer && subscriber.next(buffer);\n      subscriber.complete();\n    }, undefined, function () {\n      return buffer = closingSubscriber = null;\n    }));\n  });\n}", "map": {"version": 3, "names": ["operate", "noop", "createOperatorSubscriber", "innerFrom", "bufferWhen", "closingSelector", "source", "subscriber", "buffer", "closingSubscriber", "openBuffer", "unsubscribe", "b", "next", "subscribe", "value", "push", "complete", "undefined"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\bufferWhen.ts"], "sourcesContent": ["import { Subscriber } from '../Subscriber';\nimport { ObservableInput, OperatorFunction } from '../types';\nimport { operate } from '../util/lift';\nimport { noop } from '../util/noop';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\n\n/**\n * Buffers the source Observable values, using a factory function of closing\n * Observables to determine when to close, emit, and reset the buffer.\n *\n * <span class=\"informal\">Collects values from the past as an array. When it\n * starts collecting values, it calls a function that returns an Observable that\n * tells when to close the buffer and restart collecting.</span>\n *\n * ![](bufferWhen.svg)\n *\n * Opens a buffer immediately, then closes the buffer when the observable\n * returned by calling `closingSelector` function emits a value. When it closes\n * the buffer, it immediately opens a new buffer and repeats the process.\n *\n * ## Example\n *\n * Emit an array of the last clicks every [1-5] random seconds\n *\n * ```ts\n * import { fromEvent, bufferWhen, interval } from 'rxjs';\n *\n * const clicks = fromEvent(document, 'click');\n * const buffered = clicks.pipe(\n *   bufferWhen(() => interval(1000 + Math.random() * 4000))\n * );\n * buffered.subscribe(x => console.log(x));\n * ```\n *\n * @see {@link buffer}\n * @see {@link bufferCount}\n * @see {@link bufferTime}\n * @see {@link bufferToggle}\n * @see {@link windowWhen}\n *\n * @param closingSelector A function that takes no arguments and returns an\n * Observable that signals buffer closure.\n * @return A function that returns an Observable of arrays of buffered values.\n */\nexport function bufferWhen<T>(closingSelector: () => ObservableInput<any>): OperatorFunction<T, T[]> {\n  return operate((source, subscriber) => {\n    // The buffer we keep and emit.\n    let buffer: T[] | null = null;\n    // A reference to the subscriber used to subscribe to\n    // the closing notifier. We need to hold this so we can\n    // end the subscription after the first notification.\n    let closingSubscriber: Subscriber<T> | null = null;\n\n    // Ends the previous closing notifier subscription, so it\n    // terminates after the first emission, then emits\n    // the current buffer  if there is one, starts a new buffer, and starts a\n    // new closing notifier.\n    const openBuffer = () => {\n      // Make sure to finalize the closing subscription, we only cared\n      // about one notification.\n      closingSubscriber?.unsubscribe();\n      // emit the buffer if we have one, and start a new buffer.\n      const b = buffer;\n      buffer = [];\n      b && subscriber.next(b);\n\n      // Get a new closing notifier and subscribe to it.\n      innerFrom(closingSelector()).subscribe((closingSubscriber = createOperatorSubscriber(subscriber, openBuffer, noop)));\n    };\n\n    // Start the first buffer.\n    openBuffer();\n\n    // Subscribe to our source.\n    source.subscribe(\n      createOperatorSubscriber(\n        subscriber,\n        // Add every new value to the current buffer.\n        (value) => buffer?.push(value),\n        // When we complete, emit the buffer if we have one,\n        // then complete the result.\n        () => {\n          buffer && subscriber.next(buffer);\n          subscriber.complete();\n        },\n        // Pass all errors through to consumer.\n        undefined,\n        // Release memory on finalization\n        () => (buffer = closingSubscriber = null!)\n      )\n    );\n  });\n}\n"], "mappings": "AAEA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,IAAI,QAAQ,cAAc;AACnC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,SAASC,SAAS,QAAQ,yBAAyB;AAwCnD,OAAM,SAAUC,UAAUA,CAAIC,eAA2C;EACvE,OAAOL,OAAO,CAAC,UAACM,MAAM,EAAEC,UAAU;IAEhC,IAAIC,MAAM,GAAe,IAAI;IAI7B,IAAIC,iBAAiB,GAAyB,IAAI;IAMlD,IAAMC,UAAU,GAAG,SAAAA,CAAA;MAGjBD,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEE,WAAW,EAAE;MAEhC,IAAMC,CAAC,GAAGJ,MAAM;MAChBA,MAAM,GAAG,EAAE;MACXI,CAAC,IAAIL,UAAU,CAACM,IAAI,CAACD,CAAC,CAAC;MAGvBT,SAAS,CAACE,eAAe,EAAE,CAAC,CAACS,SAAS,CAAEL,iBAAiB,GAAGP,wBAAwB,CAACK,UAAU,EAAEG,UAAU,EAAET,IAAI,CAAE,CAAC;IACtH,CAAC;IAGDS,UAAU,EAAE;IAGZJ,MAAM,CAACQ,SAAS,CACdZ,wBAAwB,CACtBK,UAAU,EAEV,UAACQ,KAAK;MAAK,OAAAP,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEQ,IAAI,CAACD,KAAK,CAAC;IAAnB,CAAmB,EAG9B;MACEP,MAAM,IAAID,UAAU,CAACM,IAAI,CAACL,MAAM,CAAC;MACjCD,UAAU,CAACU,QAAQ,EAAE;IACvB,CAAC,EAEDC,SAAS,EAET;MAAM,OAACV,MAAM,GAAGC,iBAAiB,GAAG,IAAK;IAAnC,CAAoC,CAC3C,CACF;EACH,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}