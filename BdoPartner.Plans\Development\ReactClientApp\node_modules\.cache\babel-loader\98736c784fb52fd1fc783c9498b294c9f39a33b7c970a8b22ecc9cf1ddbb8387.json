{"ast": null, "code": "import { Notification } from '../Notification';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function materialize() {\n  return operate(function (source, subscriber) {\n    source.subscribe(createOperatorSubscriber(subscriber, function (value) {\n      subscriber.next(Notification.createNext(value));\n    }, function () {\n      subscriber.next(Notification.createComplete());\n      subscriber.complete();\n    }, function (err) {\n      subscriber.next(Notification.createError(err));\n      subscriber.complete();\n    }));\n  });\n}", "map": {"version": 3, "names": ["Notification", "operate", "createOperatorSubscriber", "materialize", "source", "subscriber", "subscribe", "value", "next", "createNext", "createComplete", "complete", "err", "createError"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\materialize.ts"], "sourcesContent": ["import { Notification } from '../Notification';\nimport { OperatorFunction, ObservableNotification } from '../types';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\n\n/**\n * Represents all of the notifications from the source Observable as `next`\n * emissions marked with their original types within {@link Notification}\n * objects.\n *\n * <span class=\"informal\">Wraps `next`, `error` and `complete` emissions in\n * {@link Notification} objects, emitted as `next` on the output Observable.\n * </span>\n *\n * ![](materialize.png)\n *\n * `materialize` returns an Observable that emits a `next` notification for each\n * `next`, `error`, or `complete` emission of the source Observable. When the\n * source Observable emits `complete`, the output Observable will emit `next` as\n * a Notification of type \"complete\", and then it will emit `complete` as well.\n * When the source Observable emits `error`, the output will emit `next` as a\n * Notification of type \"error\", and then `complete`.\n *\n * This operator is useful for producing metadata of the source Observable, to\n * be consumed as `next` emissions. Use it in conjunction with\n * {@link dematerialize}.\n *\n * ## Example\n *\n * Convert a faulty Observable to an Observable of Notifications\n *\n * ```ts\n * import { of, materialize, map } from 'rxjs';\n *\n * const letters = of('a', 'b', 13, 'd');\n * const upperCase = letters.pipe(map((x: any) => x.toUpperCase()));\n * const materialized = upperCase.pipe(materialize());\n *\n * materialized.subscribe(x => console.log(x));\n *\n * // Results in the following:\n * // - Notification { kind: 'N', value: 'A', error: undefined, hasValue: true }\n * // - Notification { kind: 'N', value: 'B', error: undefined, hasValue: true }\n * // - Notification { kind: 'E', value: undefined, error: TypeError { message: x.toUpperCase is not a function }, hasValue: false }\n * ```\n *\n * @see {@link Notification}\n * @see {@link dematerialize}\n *\n * @return A function that returns an Observable that emits\n * {@link Notification} objects that wrap the original emissions from the\n * source Observable with metadata.\n */\nexport function materialize<T>(): OperatorFunction<T, Notification<T> & ObservableNotification<T>> {\n  return operate((source, subscriber) => {\n    source.subscribe(\n      createOperatorSubscriber(\n        subscriber,\n        (value) => {\n          subscriber.next(Notification.createNext(value));\n        },\n        () => {\n          subscriber.next(Notification.createComplete());\n          subscriber.complete();\n        },\n        (err) => {\n          subscriber.next(Notification.createError(err));\n          subscriber.complete();\n        }\n      )\n    );\n  });\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAkD/D,OAAM,SAAUC,WAAWA,CAAA;EACzB,OAAOF,OAAO,CAAC,UAACG,MAAM,EAAEC,UAAU;IAChCD,MAAM,CAACE,SAAS,CACdJ,wBAAwB,CACtBG,UAAU,EACV,UAACE,KAAK;MACJF,UAAU,CAACG,IAAI,CAACR,YAAY,CAACS,UAAU,CAACF,KAAK,CAAC,CAAC;IACjD,CAAC,EACD;MACEF,UAAU,CAACG,IAAI,CAACR,YAAY,CAACU,cAAc,EAAE,CAAC;MAC9CL,UAAU,CAACM,QAAQ,EAAE;IACvB,CAAC,EACD,UAACC,GAAG;MACFP,UAAU,CAACG,IAAI,CAACR,YAAY,CAACa,WAAW,CAACD,GAAG,CAAC,CAAC;MAC9CP,UAAU,CAACM,QAAQ,EAAE;IACvB,CAAC,CACF,CACF;EACH,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}