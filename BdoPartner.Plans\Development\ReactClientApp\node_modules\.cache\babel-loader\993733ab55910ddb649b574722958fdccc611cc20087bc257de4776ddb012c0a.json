{"ast": null, "code": "import { Subject } from '../Subject';\nimport { Observable } from '../Observable';\nimport { defer } from './defer';\nvar DEFAULT_CONFIG = {\n  connector: function () {\n    return new Subject();\n  },\n  resetOnDisconnect: true\n};\nexport function connectable(source, config) {\n  if (config === void 0) {\n    config = DEFAULT_CONFIG;\n  }\n  var connection = null;\n  var connector = config.connector,\n    _a = config.resetOnDisconnect,\n    resetOnDisconnect = _a === void 0 ? true : _a;\n  var subject = connector();\n  var result = new Observable(function (subscriber) {\n    return subject.subscribe(subscriber);\n  });\n  result.connect = function () {\n    if (!connection || connection.closed) {\n      connection = defer(function () {\n        return source;\n      }).subscribe(subject);\n      if (resetOnDisconnect) {\n        connection.add(function () {\n          return subject = connector();\n        });\n      }\n    }\n    return connection;\n  };\n  return result;\n}", "map": {"version": 3, "names": ["Subject", "Observable", "defer", "DEFAULT_CONFIG", "connector", "resetOnDisconnect", "connectable", "source", "config", "connection", "_a", "subject", "result", "subscriber", "subscribe", "connect", "closed", "add"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\observable\\connectable.ts"], "sourcesContent": ["import { Connectable, ObservableInput, SubjectLike } from '../types';\nimport { Subject } from '../Subject';\nimport { Subscription } from '../Subscription';\nimport { Observable } from '../Observable';\nimport { defer } from './defer';\n\nexport interface ConnectableConfig<T> {\n  /**\n   * A factory function used to create the Subject through which the source\n   * is multicast. By default this creates a {@link Subject}.\n   */\n  connector: () => SubjectLike<T>;\n  /**\n   * If true, the resulting observable will reset internal state upon disconnection\n   * and return to a \"cold\" state. This allows the resulting observable to be\n   * reconnected.\n   * If false, upon disconnection, the connecting subject will remain the\n   * connecting subject, meaning the resulting observable will not go \"cold\" again,\n   * and subsequent repeats or resubscriptions will resubscribe to that same subject.\n   */\n  resetOnDisconnect?: boolean;\n}\n\n/**\n * The default configuration for `connectable`.\n */\nconst DEFAULT_CONFIG: ConnectableConfig<unknown> = {\n  connector: () => new Subject<unknown>(),\n  resetOnDisconnect: true,\n};\n\n/**\n * Creates an observable that multicasts once `connect()` is called on it.\n *\n * @param source The observable source to make connectable.\n * @param config The configuration object for `connectable`.\n * @returns A \"connectable\" observable, that has a `connect()` method, that you must call to\n * connect the source to all consumers through the subject provided as the connector.\n */\nexport function connectable<T>(source: ObservableInput<T>, config: ConnectableConfig<T> = DEFAULT_CONFIG): Connectable<T> {\n  // The subscription representing the connection.\n  let connection: Subscription | null = null;\n  const { connector, resetOnDisconnect = true } = config;\n  let subject = connector();\n\n  const result: any = new Observable<T>((subscriber) => {\n    return subject.subscribe(subscriber);\n  });\n\n  // Define the `connect` function. This is what users must call\n  // in order to \"connect\" the source to the subject that is\n  // multicasting it.\n  result.connect = () => {\n    if (!connection || connection.closed) {\n      connection = defer(() => source).subscribe(subject);\n      if (resetOnDisconnect) {\n        connection.add(() => (subject = connector()));\n      }\n    }\n    return connection;\n  };\n\n  return result;\n}\n"], "mappings": "AACA,SAASA,OAAO,QAAQ,YAAY;AAEpC,SAASC,UAAU,QAAQ,eAAe;AAC1C,SAASC,KAAK,QAAQ,SAAS;AAsB/B,IAAMC,cAAc,GAA+B;EACjDC,SAAS,EAAE,SAAAA,CAAA;IAAM,WAAIJ,OAAO,EAAW;EAAtB,CAAsB;EACvCK,iBAAiB,EAAE;CACpB;AAUD,OAAM,SAAUC,WAAWA,CAAIC,MAA0B,EAAEC,MAA6C;EAA7C,IAAAA,MAAA;IAAAA,MAAA,GAAAL,cAA6C;EAAA;EAEtG,IAAIM,UAAU,GAAwB,IAAI;EAClC,IAAAL,SAAS,GAA+BI,MAAM,CAAAJ,SAArC;IAAEM,EAAA,GAA6BF,MAAM,CAAAH,iBAAX;IAAxBA,iBAAiB,GAAAK,EAAA,cAAG,IAAI,GAAAA,EAAA;EAC3C,IAAIC,OAAO,GAAGP,SAAS,EAAE;EAEzB,IAAMQ,MAAM,GAAQ,IAAIX,UAAU,CAAI,UAACY,UAAU;IAC/C,OAAOF,OAAO,CAACG,SAAS,CAACD,UAAU,CAAC;EACtC,CAAC,CAAC;EAKFD,MAAM,CAACG,OAAO,GAAG;IACf,IAAI,CAACN,UAAU,IAAIA,UAAU,CAACO,MAAM,EAAE;MACpCP,UAAU,GAAGP,KAAK,CAAC;QAAM,OAAAK,MAAM;MAAN,CAAM,CAAC,CAACO,SAAS,CAACH,OAAO,CAAC;MACnD,IAAIN,iBAAiB,EAAE;QACrBI,UAAU,CAACQ,GAAG,CAAC;UAAM,OAACN,OAAO,GAAGP,SAAS,EAAE;QAAtB,CAAuB,CAAC;;;IAGjD,OAAOK,UAAU;EACnB,CAAC;EAED,OAAOG,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}