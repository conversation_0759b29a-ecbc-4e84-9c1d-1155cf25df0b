{"ast": null, "code": "import { SafeSubscriber, Subscriber } from './Subscriber';\nimport { isSubscription } from './Subscription';\nimport { observable as Symbol_observable } from './symbol/observable';\nimport { pipeFromArray } from './util/pipe';\nimport { config } from './config';\nimport { isFunction } from './util/isFunction';\nimport { errorContext } from './util/errorContext';\nvar Observable = function () {\n  function Observable(subscribe) {\n    if (subscribe) {\n      this._subscribe = subscribe;\n    }\n  }\n  Observable.prototype.lift = function (operator) {\n    var observable = new Observable();\n    observable.source = this;\n    observable.operator = operator;\n    return observable;\n  };\n  Observable.prototype.subscribe = function (observerOrNext, error, complete) {\n    var _this = this;\n    var subscriber = isSubscriber(observerOrNext) ? observerOrNext : new SafeSubscriber(observerOrNext, error, complete);\n    errorContext(function () {\n      var _a = _this,\n        operator = _a.operator,\n        source = _a.source;\n      subscriber.add(operator ? operator.call(subscriber, source) : source ? _this._subscribe(subscriber) : _this._trySubscribe(subscriber));\n    });\n    return subscriber;\n  };\n  Observable.prototype._trySubscribe = function (sink) {\n    try {\n      return this._subscribe(sink);\n    } catch (err) {\n      sink.error(err);\n    }\n  };\n  Observable.prototype.forEach = function (next, promiseCtor) {\n    var _this = this;\n    promiseCtor = getPromiseCtor(promiseCtor);\n    return new promiseCtor(function (resolve, reject) {\n      var subscriber = new SafeSubscriber({\n        next: function (value) {\n          try {\n            next(value);\n          } catch (err) {\n            reject(err);\n            subscriber.unsubscribe();\n          }\n        },\n        error: reject,\n        complete: resolve\n      });\n      _this.subscribe(subscriber);\n    });\n  };\n  Observable.prototype._subscribe = function (subscriber) {\n    var _a;\n    return (_a = this.source) === null || _a === void 0 ? void 0 : _a.subscribe(subscriber);\n  };\n  Observable.prototype[Symbol_observable] = function () {\n    return this;\n  };\n  Observable.prototype.pipe = function () {\n    var operations = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      operations[_i] = arguments[_i];\n    }\n    return pipeFromArray(operations)(this);\n  };\n  Observable.prototype.toPromise = function (promiseCtor) {\n    var _this = this;\n    promiseCtor = getPromiseCtor(promiseCtor);\n    return new promiseCtor(function (resolve, reject) {\n      var value;\n      _this.subscribe(function (x) {\n        return value = x;\n      }, function (err) {\n        return reject(err);\n      }, function () {\n        return resolve(value);\n      });\n    });\n  };\n  Observable.create = function (subscribe) {\n    return new Observable(subscribe);\n  };\n  return Observable;\n}();\nexport { Observable };\nfunction getPromiseCtor(promiseCtor) {\n  var _a;\n  return (_a = promiseCtor !== null && promiseCtor !== void 0 ? promiseCtor : config.Promise) !== null && _a !== void 0 ? _a : Promise;\n}\nfunction isObserver(value) {\n  return value && isFunction(value.next) && isFunction(value.error) && isFunction(value.complete);\n}\nfunction isSubscriber(value) {\n  return value && value instanceof Subscriber || isObserver(value) && isSubscription(value);\n}", "map": {"version": 3, "names": ["SafeSubscriber", "Subscriber", "isSubscription", "observable", "Symbol_observable", "pipeFromArray", "config", "isFunction", "errorContext", "Observable", "subscribe", "_subscribe", "prototype", "lift", "operator", "source", "observerOrNext", "error", "complete", "_this", "subscriber", "isSubscriber", "_a", "add", "call", "_trySubscribe", "sink", "err", "for<PERSON>ach", "next", "promiseCtor", "getPromiseCtor", "resolve", "reject", "value", "unsubscribe", "pipe", "operations", "_i", "arguments", "length", "to<PERSON>romise", "x", "create", "Promise", "isObserver"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\Observable.ts"], "sourcesContent": ["import { Operator } from './Operator';\nimport { SafeSubscriber, Subscriber } from './Subscriber';\nimport { isSubscription, Subscription } from './Subscription';\nimport { TeardownLogic, OperatorFunction, Subscribable, Observer } from './types';\nimport { observable as Symbol_observable } from './symbol/observable';\nimport { pipeFromArray } from './util/pipe';\nimport { config } from './config';\nimport { isFunction } from './util/isFunction';\nimport { errorContext } from './util/errorContext';\n\n/**\n * A representation of any set of values over any amount of time. This is the most basic building block\n * of RxJS.\n */\nexport class Observable<T> implements Subscribable<T> {\n  /**\n   * @deprecated Internal implementation detail, do not use directly. Will be made internal in v8.\n   */\n  source: Observable<any> | undefined;\n\n  /**\n   * @deprecated Internal implementation detail, do not use directly. Will be made internal in v8.\n   */\n  operator: Operator<any, T> | undefined;\n\n  /**\n   * @param subscribe The function that is called when the Observable is\n   * initially subscribed to. This function is given a Subscriber, to which new values\n   * can be `next`ed, or an `error` method can be called to raise an error, or\n   * `complete` can be called to notify of a successful completion.\n   */\n  constructor(subscribe?: (this: Observable<T>, subscriber: Subscriber<T>) => TeardownLogic) {\n    if (subscribe) {\n      this._subscribe = subscribe;\n    }\n  }\n\n  // HACK: Since TypeScript inherits static properties too, we have to\n  // fight against TypeScript here so Subject can have a different static create signature\n  /**\n   * Creates a new Observable by calling the Observable constructor\n   * @param subscribe the subscriber function to be passed to the Observable constructor\n   * @return A new observable.\n   * @deprecated Use `new Observable()` instead. Will be removed in v8.\n   */\n  static create: (...args: any[]) => any = <T>(subscribe?: (subscriber: Subscriber<T>) => TeardownLogic) => {\n    return new Observable<T>(subscribe);\n  };\n\n  /**\n   * Creates a new Observable, with this Observable instance as the source, and the passed\n   * operator defined as the new observable's operator.\n   * @param operator the operator defining the operation to take on the observable\n   * @return A new observable with the Operator applied.\n   * @deprecated Internal implementation detail, do not use directly. Will be made internal in v8.\n   * If you have implemented an operator using `lift`, it is recommended that you create an\n   * operator by simply returning `new Observable()` directly. See \"Creating new operators from\n   * scratch\" section here: https://rxjs.dev/guide/operators\n   */\n  lift<R>(operator?: Operator<T, R>): Observable<R> {\n    const observable = new Observable<R>();\n    observable.source = this;\n    observable.operator = operator;\n    return observable;\n  }\n\n  subscribe(observerOrNext?: Partial<Observer<T>> | ((value: T) => void)): Subscription;\n  /** @deprecated Instead of passing separate callback arguments, use an observer argument. Signatures taking separate callback arguments will be removed in v8. Details: https://rxjs.dev/deprecations/subscribe-arguments */\n  subscribe(next?: ((value: T) => void) | null, error?: ((error: any) => void) | null, complete?: (() => void) | null): Subscription;\n  /**\n   * Invokes an execution of an Observable and registers Observer handlers for notifications it will emit.\n   *\n   * <span class=\"informal\">Use it when you have all these Observables, but still nothing is happening.</span>\n   *\n   * `subscribe` is not a regular operator, but a method that calls Observable's internal `subscribe` function. It\n   * might be for example a function that you passed to Observable's constructor, but most of the time it is\n   * a library implementation, which defines what will be emitted by an Observable, and when it be will emitted. This means\n   * that calling `subscribe` is actually the moment when Observable starts its work, not when it is created, as it is often\n   * the thought.\n   *\n   * Apart from starting the execution of an Observable, this method allows you to listen for values\n   * that an Observable emits, as well as for when it completes or errors. You can achieve this in two\n   * of the following ways.\n   *\n   * The first way is creating an object that implements {@link Observer} interface. It should have methods\n   * defined by that interface, but note that it should be just a regular JavaScript object, which you can create\n   * yourself in any way you want (ES6 class, classic function constructor, object literal etc.). In particular, do\n   * not attempt to use any RxJS implementation details to create Observers - you don't need them. Remember also\n   * that your object does not have to implement all methods. If you find yourself creating a method that doesn't\n   * do anything, you can simply omit it. Note however, if the `error` method is not provided and an error happens,\n   * it will be thrown asynchronously. Errors thrown asynchronously cannot be caught using `try`/`catch`. Instead,\n   * use the {@link onUnhandledError} configuration option or use a runtime handler (like `window.onerror` or\n   * `process.on('error)`) to be notified of unhandled errors. Because of this, it's recommended that you provide\n   * an `error` method to avoid missing thrown errors.\n   *\n   * The second way is to give up on Observer object altogether and simply provide callback functions in place of its methods.\n   * This means you can provide three functions as arguments to `subscribe`, where the first function is equivalent\n   * of a `next` method, the second of an `error` method and the third of a `complete` method. Just as in case of an Observer,\n   * if you do not need to listen for something, you can omit a function by passing `undefined` or `null`,\n   * since `subscribe` recognizes these functions by where they were placed in function call. When it comes\n   * to the `error` function, as with an Observer, if not provided, errors emitted by an Observable will be thrown asynchronously.\n   *\n   * You can, however, subscribe with no parameters at all. This may be the case where you're not interested in terminal events\n   * and you also handled emissions internally by using operators (e.g. using `tap`).\n   *\n   * Whichever style of calling `subscribe` you use, in both cases it returns a Subscription object.\n   * This object allows you to call `unsubscribe` on it, which in turn will stop the work that an Observable does and will clean\n   * up all resources that an Observable used. Note that cancelling a subscription will not call `complete` callback\n   * provided to `subscribe` function, which is reserved for a regular completion signal that comes from an Observable.\n   *\n   * Remember that callbacks provided to `subscribe` are not guaranteed to be called asynchronously.\n   * It is an Observable itself that decides when these functions will be called. For example {@link of}\n   * by default emits all its values synchronously. Always check documentation for how given Observable\n   * will behave when subscribed and if its default behavior can be modified with a `scheduler`.\n   *\n   * #### Examples\n   *\n   * Subscribe with an {@link guide/observer Observer}\n   *\n   * ```ts\n   * import { of } from 'rxjs';\n   *\n   * const sumObserver = {\n   *   sum: 0,\n   *   next(value) {\n   *     console.log('Adding: ' + value);\n   *     this.sum = this.sum + value;\n   *   },\n   *   error() {\n   *     // We actually could just remove this method,\n   *     // since we do not really care about errors right now.\n   *   },\n   *   complete() {\n   *     console.log('Sum equals: ' + this.sum);\n   *   }\n   * };\n   *\n   * of(1, 2, 3) // Synchronously emits 1, 2, 3 and then completes.\n   *   .subscribe(sumObserver);\n   *\n   * // Logs:\n   * // 'Adding: 1'\n   * // 'Adding: 2'\n   * // 'Adding: 3'\n   * // 'Sum equals: 6'\n   * ```\n   *\n   * Subscribe with functions ({@link deprecations/subscribe-arguments deprecated})\n   *\n   * ```ts\n   * import { of } from 'rxjs'\n   *\n   * let sum = 0;\n   *\n   * of(1, 2, 3).subscribe(\n   *   value => {\n   *     console.log('Adding: ' + value);\n   *     sum = sum + value;\n   *   },\n   *   undefined,\n   *   () => console.log('Sum equals: ' + sum)\n   * );\n   *\n   * // Logs:\n   * // 'Adding: 1'\n   * // 'Adding: 2'\n   * // 'Adding: 3'\n   * // 'Sum equals: 6'\n   * ```\n   *\n   * Cancel a subscription\n   *\n   * ```ts\n   * import { interval } from 'rxjs';\n   *\n   * const subscription = interval(1000).subscribe({\n   *   next(num) {\n   *     console.log(num)\n   *   },\n   *   complete() {\n   *     // Will not be called, even when cancelling subscription.\n   *     console.log('completed!');\n   *   }\n   * });\n   *\n   * setTimeout(() => {\n   *   subscription.unsubscribe();\n   *   console.log('unsubscribed!');\n   * }, 2500);\n   *\n   * // Logs:\n   * // 0 after 1s\n   * // 1 after 2s\n   * // 'unsubscribed!' after 2.5s\n   * ```\n   *\n   * @param observerOrNext Either an {@link Observer} with some or all callback methods,\n   * or the `next` handler that is called for each value emitted from the subscribed Observable.\n   * @param error A handler for a terminal event resulting from an error. If no error handler is provided,\n   * the error will be thrown asynchronously as unhandled.\n   * @param complete A handler for a terminal event resulting from successful completion.\n   * @return A subscription reference to the registered handlers.\n   */\n  subscribe(\n    observerOrNext?: Partial<Observer<T>> | ((value: T) => void) | null,\n    error?: ((error: any) => void) | null,\n    complete?: (() => void) | null\n  ): Subscription {\n    const subscriber = isSubscriber(observerOrNext) ? observerOrNext : new SafeSubscriber(observerOrNext, error, complete);\n\n    errorContext(() => {\n      const { operator, source } = this;\n      subscriber.add(\n        operator\n          ? // We're dealing with a subscription in the\n            // operator chain to one of our lifted operators.\n            operator.call(subscriber, source)\n          : source\n          ? // If `source` has a value, but `operator` does not, something that\n            // had intimate knowledge of our API, like our `Subject`, must have\n            // set it. We're going to just call `_subscribe` directly.\n            this._subscribe(subscriber)\n          : // In all other cases, we're likely wrapping a user-provided initializer\n            // function, so we need to catch errors and handle them appropriately.\n            this._trySubscribe(subscriber)\n      );\n    });\n\n    return subscriber;\n  }\n\n  /** @internal */\n  protected _trySubscribe(sink: Subscriber<T>): TeardownLogic {\n    try {\n      return this._subscribe(sink);\n    } catch (err) {\n      // We don't need to return anything in this case,\n      // because it's just going to try to `add()` to a subscription\n      // above.\n      sink.error(err);\n    }\n  }\n\n  /**\n   * Used as a NON-CANCELLABLE means of subscribing to an observable, for use with\n   * APIs that expect promises, like `async/await`. You cannot unsubscribe from this.\n   *\n   * **WARNING**: Only use this with observables you *know* will complete. If the source\n   * observable does not complete, you will end up with a promise that is hung up, and\n   * potentially all of the state of an async function hanging out in memory. To avoid\n   * this situation, look into adding something like {@link timeout}, {@link take},\n   * {@link takeWhile}, or {@link takeUntil} amongst others.\n   *\n   * #### Example\n   *\n   * ```ts\n   * import { interval, take } from 'rxjs';\n   *\n   * const source$ = interval(1000).pipe(take(4));\n   *\n   * async function getTotal() {\n   *   let total = 0;\n   *\n   *   await source$.forEach(value => {\n   *     total += value;\n   *     console.log('observable -> ' + value);\n   *   });\n   *\n   *   return total;\n   * }\n   *\n   * getTotal().then(\n   *   total => console.log('Total: ' + total)\n   * );\n   *\n   * // Expected:\n   * // 'observable -> 0'\n   * // 'observable -> 1'\n   * // 'observable -> 2'\n   * // 'observable -> 3'\n   * // 'Total: 6'\n   * ```\n   *\n   * @param next A handler for each value emitted by the observable.\n   * @return A promise that either resolves on observable completion or\n   * rejects with the handled error.\n   */\n  forEach(next: (value: T) => void): Promise<void>;\n\n  /**\n   * @param next a handler for each value emitted by the observable\n   * @param promiseCtor a constructor function used to instantiate the Promise\n   * @return a promise that either resolves on observable completion or\n   *  rejects with the handled error\n   * @deprecated Passing a Promise constructor will no longer be available\n   * in upcoming versions of RxJS. This is because it adds weight to the library, for very\n   * little benefit. If you need this functionality, it is recommended that you either\n   * polyfill Promise, or you create an adapter to convert the returned native promise\n   * to whatever promise implementation you wanted. Will be removed in v8.\n   */\n  forEach(next: (value: T) => void, promiseCtor: PromiseConstructorLike): Promise<void>;\n\n  forEach(next: (value: T) => void, promiseCtor?: PromiseConstructorLike): Promise<void> {\n    promiseCtor = getPromiseCtor(promiseCtor);\n\n    return new promiseCtor<void>((resolve, reject) => {\n      const subscriber = new SafeSubscriber<T>({\n        next: (value) => {\n          try {\n            next(value);\n          } catch (err) {\n            reject(err);\n            subscriber.unsubscribe();\n          }\n        },\n        error: reject,\n        complete: resolve,\n      });\n      this.subscribe(subscriber);\n    }) as Promise<void>;\n  }\n\n  /** @internal */\n  protected _subscribe(subscriber: Subscriber<any>): TeardownLogic {\n    return this.source?.subscribe(subscriber);\n  }\n\n  /**\n   * An interop point defined by the es7-observable spec https://github.com/zenparsing/es-observable\n   * @return This instance of the observable.\n   */\n  [Symbol_observable]() {\n    return this;\n  }\n\n  /* tslint:disable:max-line-length */\n  pipe(): Observable<T>;\n  pipe<A>(op1: OperatorFunction<T, A>): Observable<A>;\n  pipe<A, B>(op1: OperatorFunction<T, A>, op2: OperatorFunction<A, B>): Observable<B>;\n  pipe<A, B, C>(op1: OperatorFunction<T, A>, op2: OperatorFunction<A, B>, op3: OperatorFunction<B, C>): Observable<C>;\n  pipe<A, B, C, D>(\n    op1: OperatorFunction<T, A>,\n    op2: OperatorFunction<A, B>,\n    op3: OperatorFunction<B, C>,\n    op4: OperatorFunction<C, D>\n  ): Observable<D>;\n  pipe<A, B, C, D, E>(\n    op1: OperatorFunction<T, A>,\n    op2: OperatorFunction<A, B>,\n    op3: OperatorFunction<B, C>,\n    op4: OperatorFunction<C, D>,\n    op5: OperatorFunction<D, E>\n  ): Observable<E>;\n  pipe<A, B, C, D, E, F>(\n    op1: OperatorFunction<T, A>,\n    op2: OperatorFunction<A, B>,\n    op3: OperatorFunction<B, C>,\n    op4: OperatorFunction<C, D>,\n    op5: OperatorFunction<D, E>,\n    op6: OperatorFunction<E, F>\n  ): Observable<F>;\n  pipe<A, B, C, D, E, F, G>(\n    op1: OperatorFunction<T, A>,\n    op2: OperatorFunction<A, B>,\n    op3: OperatorFunction<B, C>,\n    op4: OperatorFunction<C, D>,\n    op5: OperatorFunction<D, E>,\n    op6: OperatorFunction<E, F>,\n    op7: OperatorFunction<F, G>\n  ): Observable<G>;\n  pipe<A, B, C, D, E, F, G, H>(\n    op1: OperatorFunction<T, A>,\n    op2: OperatorFunction<A, B>,\n    op3: OperatorFunction<B, C>,\n    op4: OperatorFunction<C, D>,\n    op5: OperatorFunction<D, E>,\n    op6: OperatorFunction<E, F>,\n    op7: OperatorFunction<F, G>,\n    op8: OperatorFunction<G, H>\n  ): Observable<H>;\n  pipe<A, B, C, D, E, F, G, H, I>(\n    op1: OperatorFunction<T, A>,\n    op2: OperatorFunction<A, B>,\n    op3: OperatorFunction<B, C>,\n    op4: OperatorFunction<C, D>,\n    op5: OperatorFunction<D, E>,\n    op6: OperatorFunction<E, F>,\n    op7: OperatorFunction<F, G>,\n    op8: OperatorFunction<G, H>,\n    op9: OperatorFunction<H, I>\n  ): Observable<I>;\n  pipe<A, B, C, D, E, F, G, H, I>(\n    op1: OperatorFunction<T, A>,\n    op2: OperatorFunction<A, B>,\n    op3: OperatorFunction<B, C>,\n    op4: OperatorFunction<C, D>,\n    op5: OperatorFunction<D, E>,\n    op6: OperatorFunction<E, F>,\n    op7: OperatorFunction<F, G>,\n    op8: OperatorFunction<G, H>,\n    op9: OperatorFunction<H, I>,\n    ...operations: OperatorFunction<any, any>[]\n  ): Observable<unknown>;\n  /* tslint:enable:max-line-length */\n\n  /**\n   * Used to stitch together functional operators into a chain.\n   *\n   * ## Example\n   *\n   * ```ts\n   * import { interval, filter, map, scan } from 'rxjs';\n   *\n   * interval(1000)\n   *   .pipe(\n   *     filter(x => x % 2 === 0),\n   *     map(x => x + x),\n   *     scan((acc, x) => acc + x)\n   *   )\n   *   .subscribe(x => console.log(x));\n   * ```\n   *\n   * @return The Observable result of all the operators having been called\n   * in the order they were passed in.\n   */\n  pipe(...operations: OperatorFunction<any, any>[]): Observable<any> {\n    return pipeFromArray(operations)(this);\n  }\n\n  /* tslint:disable:max-line-length */\n  /** @deprecated Replaced with {@link firstValueFrom} and {@link lastValueFrom}. Will be removed in v8. Details: https://rxjs.dev/deprecations/to-promise */\n  toPromise(): Promise<T | undefined>;\n  /** @deprecated Replaced with {@link firstValueFrom} and {@link lastValueFrom}. Will be removed in v8. Details: https://rxjs.dev/deprecations/to-promise */\n  toPromise(PromiseCtor: typeof Promise): Promise<T | undefined>;\n  /** @deprecated Replaced with {@link firstValueFrom} and {@link lastValueFrom}. Will be removed in v8. Details: https://rxjs.dev/deprecations/to-promise */\n  toPromise(PromiseCtor: PromiseConstructorLike): Promise<T | undefined>;\n  /* tslint:enable:max-line-length */\n\n  /**\n   * Subscribe to this Observable and get a Promise resolving on\n   * `complete` with the last emission (if any).\n   *\n   * **WARNING**: Only use this with observables you *know* will complete. If the source\n   * observable does not complete, you will end up with a promise that is hung up, and\n   * potentially all of the state of an async function hanging out in memory. To avoid\n   * this situation, look into adding something like {@link timeout}, {@link take},\n   * {@link takeWhile}, or {@link takeUntil} amongst others.\n   *\n   * @param [promiseCtor] a constructor function used to instantiate\n   * the Promise\n   * @return A Promise that resolves with the last value emit, or\n   * rejects on an error. If there were no emissions, Promise\n   * resolves with undefined.\n   * @deprecated Replaced with {@link firstValueFrom} and {@link lastValueFrom}. Will be removed in v8. Details: https://rxjs.dev/deprecations/to-promise\n   */\n  toPromise(promiseCtor?: PromiseConstructorLike): Promise<T | undefined> {\n    promiseCtor = getPromiseCtor(promiseCtor);\n\n    return new promiseCtor((resolve, reject) => {\n      let value: T | undefined;\n      this.subscribe(\n        (x: T) => (value = x),\n        (err: any) => reject(err),\n        () => resolve(value)\n      );\n    }) as Promise<T | undefined>;\n  }\n}\n\n/**\n * Decides between a passed promise constructor from consuming code,\n * A default configured promise constructor, and the native promise\n * constructor and returns it. If nothing can be found, it will throw\n * an error.\n * @param promiseCtor The optional promise constructor to passed by consuming code\n */\nfunction getPromiseCtor(promiseCtor: PromiseConstructorLike | undefined) {\n  return promiseCtor ?? config.Promise ?? Promise;\n}\n\nfunction isObserver<T>(value: any): value is Observer<T> {\n  return value && isFunction(value.next) && isFunction(value.error) && isFunction(value.complete);\n}\n\nfunction isSubscriber<T>(value: any): value is Subscriber<T> {\n  return (value && value instanceof Subscriber) || (isObserver(value) && isSubscription(value));\n}\n"], "mappings": "AACA,SAASA,cAAc,EAAEC,UAAU,QAAQ,cAAc;AACzD,SAASC,cAAc,QAAsB,gBAAgB;AAE7D,SAASC,UAAU,IAAIC,iBAAiB,QAAQ,qBAAqB;AACrE,SAASC,aAAa,QAAQ,aAAa;AAC3C,SAASC,MAAM,QAAQ,UAAU;AACjC,SAASC,UAAU,QAAQ,mBAAmB;AAC9C,SAASC,YAAY,QAAQ,qBAAqB;AAMlD,IAAAC,UAAA;EAiBE,SAAAA,WAAYC,SAA6E;IACvF,IAAIA,SAAS,EAAE;MACb,IAAI,CAACC,UAAU,GAAGD,SAAS;;EAE/B;EAwBAD,UAAA,CAAAG,SAAA,CAAAC,IAAI,GAAJ,UAAQC,QAAyB;IAC/B,IAAMX,UAAU,GAAG,IAAIM,UAAU,EAAK;IACtCN,UAAU,CAACY,MAAM,GAAG,IAAI;IACxBZ,UAAU,CAACW,QAAQ,GAAGA,QAAQ;IAC9B,OAAOX,UAAU;EACnB,CAAC;EA2IDM,UAAA,CAAAG,SAAA,CAAAF,SAAS,GAAT,UACEM,cAAmE,EACnEC,KAAqC,EACrCC,QAA8B;IAHhC,IAAAC,KAAA;IAKE,IAAMC,UAAU,GAAGC,YAAY,CAACL,cAAc,CAAC,GAAGA,cAAc,GAAG,IAAIhB,cAAc,CAACgB,cAAc,EAAEC,KAAK,EAAEC,QAAQ,CAAC;IAEtHV,YAAY,CAAC;MACL,IAAAc,EAAA,GAAuBH,KAAI;QAAzBL,QAAQ,GAAAQ,EAAA,CAAAR,QAAA;QAAEC,MAAM,GAAAO,EAAA,CAAAP,MAAS;MACjCK,UAAU,CAACG,GAAG,CACZT,QAAQ,GAGJA,QAAQ,CAACU,IAAI,CAACJ,UAAU,EAAEL,MAAM,CAAC,GACjCA,MAAM,GAINI,KAAI,CAACR,UAAU,CAACS,UAAU,CAAC,GAG3BD,KAAI,CAACM,aAAa,CAACL,UAAU,CAAC,CACnC;IACH,CAAC,CAAC;IAEF,OAAOA,UAAU;EACnB,CAAC;EAGSX,UAAA,CAAAG,SAAA,CAAAa,aAAa,GAAvB,UAAwBC,IAAmB;IACzC,IAAI;MACF,OAAO,IAAI,CAACf,UAAU,CAACe,IAAI,CAAC;KAC7B,CAAC,OAAOC,GAAG,EAAE;MAIZD,IAAI,CAACT,KAAK,CAACU,GAAG,CAAC;;EAEnB,CAAC;EA6DDlB,UAAA,CAAAG,SAAA,CAAAgB,OAAO,GAAP,UAAQC,IAAwB,EAAEC,WAAoC;IAAtE,IAAAX,KAAA;IACEW,WAAW,GAAGC,cAAc,CAACD,WAAW,CAAC;IAEzC,OAAO,IAAIA,WAAW,CAAO,UAACE,OAAO,EAAEC,MAAM;MAC3C,IAAMb,UAAU,GAAG,IAAIpB,cAAc,CAAI;QACvC6B,IAAI,EAAE,SAAAA,CAACK,KAAK;UACV,IAAI;YACFL,IAAI,CAACK,KAAK,CAAC;WACZ,CAAC,OAAOP,GAAG,EAAE;YACZM,MAAM,CAACN,GAAG,CAAC;YACXP,UAAU,CAACe,WAAW,EAAE;;QAE5B,CAAC;QACDlB,KAAK,EAAEgB,MAAM;QACbf,QAAQ,EAAEc;OACX,CAAC;MACFb,KAAI,CAACT,SAAS,CAACU,UAAU,CAAC;IAC5B,CAAC,CAAkB;EACrB,CAAC;EAGSX,UAAA,CAAAG,SAAA,CAAAD,UAAU,GAApB,UAAqBS,UAA2B;;IAC9C,OAAO,CAAAE,EAAA,OAAI,CAACP,MAAM,cAAAO,EAAA,uBAAAA,EAAA,CAAEZ,SAAS,CAACU,UAAU,CAAC;EAC3C,CAAC;EAMDX,UAAA,CAAAG,SAAA,CAACR,iBAAiB,CAAC,GAAnB;IACE,OAAO,IAAI;EACb,CAAC;EA4FDK,UAAA,CAAAG,SAAA,CAAAwB,IAAI,GAAJ;IAAK,IAAAC,UAAA;SAAA,IAAAC,EAAA,IAA2C,EAA3CA,EAAA,GAAAC,SAAA,CAAAC,MAA2C,EAA3CF,EAAA,EAA2C;MAA3CD,UAAA,CAAAC,EAAA,IAAAC,SAAA,CAAAD,EAAA;;IACH,OAAOjC,aAAa,CAACgC,UAAU,CAAC,CAAC,IAAI,CAAC;EACxC,CAAC;EA4BD5B,UAAA,CAAAG,SAAA,CAAA6B,SAAS,GAAT,UAAUX,WAAoC;IAA9C,IAAAX,KAAA;IACEW,WAAW,GAAGC,cAAc,CAACD,WAAW,CAAC;IAEzC,OAAO,IAAIA,WAAW,CAAC,UAACE,OAAO,EAAEC,MAAM;MACrC,IAAIC,KAAoB;MACxBf,KAAI,CAACT,SAAS,CACZ,UAACgC,CAAI;QAAK,OAACR,KAAK,GAAGQ,CAAC;MAAV,CAAW,EACrB,UAACf,GAAQ;QAAK,OAAAM,MAAM,CAACN,GAAG,CAAC;MAAX,CAAW,EACzB;QAAM,OAAAK,OAAO,CAACE,KAAK,CAAC;MAAd,CAAc,CACrB;IACH,CAAC,CAA2B;EAC9B,CAAC;EAraMzB,UAAA,CAAAkC,MAAM,GAA4B,UAAIjC,SAAwD;IACnG,OAAO,IAAID,UAAU,CAAIC,SAAS,CAAC;EACrC,CAAC;EAoaH,OAAAD,UAAC;CAAA,CArcD;SAAaA,UAAU;AA8cvB,SAASsB,cAAcA,CAACD,WAA+C;;EACrE,OAAO,CAAAR,EAAA,GAAAQ,WAAW,aAAXA,WAAW,cAAXA,WAAW,GAAIxB,MAAM,CAACsC,OAAO,cAAAtB,EAAA,cAAAA,EAAA,GAAIsB,OAAO;AACjD;AAEA,SAASC,UAAUA,CAAIX,KAAU;EAC/B,OAAOA,KAAK,IAAI3B,UAAU,CAAC2B,KAAK,CAACL,IAAI,CAAC,IAAItB,UAAU,CAAC2B,KAAK,CAACjB,KAAK,CAAC,IAAIV,UAAU,CAAC2B,KAAK,CAAChB,QAAQ,CAAC;AACjG;AAEA,SAASG,YAAYA,CAAIa,KAAU;EACjC,OAAQA,KAAK,IAAIA,KAAK,YAAYjC,UAAU,IAAM4C,UAAU,CAACX,KAAK,CAAC,IAAIhC,cAAc,CAACgC,KAAK,CAAE;AAC/F", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}