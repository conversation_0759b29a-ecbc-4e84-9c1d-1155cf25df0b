{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function every(predicate, thisArg) {\n  return operate(function (source, subscriber) {\n    var index = 0;\n    source.subscribe(createOperatorSubscriber(subscriber, function (value) {\n      if (!predicate.call(thisArg, value, index++, source)) {\n        subscriber.next(false);\n        subscriber.complete();\n      }\n    }, function () {\n      subscriber.next(true);\n      subscriber.complete();\n    }));\n  });\n}", "map": {"version": 3, "names": ["operate", "createOperatorSubscriber", "every", "predicate", "thisArg", "source", "subscriber", "index", "subscribe", "value", "call", "next", "complete"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\every.ts"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { Falsy, OperatorFunction } from '../types';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\n\nexport function every<T>(predicate: BooleanConstructor): OperatorFunction<T, Exclude<T, Falsy> extends never ? false : boolean>;\n/** @deprecated Use a closure instead of a `thisArg`. Signatures accepting a `thisArg` will be removed in v8. */\nexport function every<T>(\n  predicate: BooleanConstructor,\n  thisArg: any\n): OperatorFunction<T, Exclude<T, Falsy> extends never ? false : boolean>;\n/** @deprecated Use a closure instead of a `thisArg`. Signatures accepting a `thisArg` will be removed in v8. */\nexport function every<T, A>(\n  predicate: (this: A, value: T, index: number, source: Observable<T>) => boolean,\n  thisArg: A\n): OperatorFunction<T, boolean>;\nexport function every<T>(predicate: (value: T, index: number, source: Observable<T>) => boolean): OperatorFunction<T, boolean>;\n\n/**\n * Returns an Observable that emits whether or not every item of the source satisfies the condition specified.\n *\n * <span class=\"informal\">If all values pass predicate before the source completes, emits true before completion,\n * otherwise emit false, then complete.</span>\n *\n * ![](every.png)\n *\n * ## Example\n *\n * A simple example emitting true if all elements are less than 5, false otherwise\n *\n * ```ts\n * import { of, every } from 'rxjs';\n *\n * of(1, 2, 3, 4, 5, 6)\n *   .pipe(every(x => x < 5))\n *   .subscribe(x => console.log(x)); // -> false\n * ```\n *\n * @param predicate A function for determining if an item meets a specified condition.\n * @param thisArg Optional object to use for `this` in the callback.\n * @return A function that returns an Observable of booleans that determines if\n * all items of the source Observable meet the condition specified.\n */\nexport function every<T>(\n  predicate: (value: T, index: number, source: Observable<T>) => boolean,\n  thisArg?: any\n): OperatorFunction<T, boolean> {\n  return operate((source, subscriber) => {\n    let index = 0;\n    source.subscribe(\n      createOperatorSubscriber(\n        subscriber,\n        (value) => {\n          if (!predicate.call(thisArg, value, index++, source)) {\n            subscriber.next(false);\n            subscriber.complete();\n          }\n        },\n        () => {\n          subscriber.next(true);\n          subscriber.complete();\n        }\n      )\n    );\n  });\n}\n"], "mappings": "AAEA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAwC/D,OAAM,SAAUC,KAAKA,CACnBC,SAAsE,EACtEC,OAAa;EAEb,OAAOJ,OAAO,CAAC,UAACK,MAAM,EAAEC,UAAU;IAChC,IAAIC,KAAK,GAAG,CAAC;IACbF,MAAM,CAACG,SAAS,CACdP,wBAAwB,CACtBK,UAAU,EACV,UAACG,KAAK;MACJ,IAAI,CAACN,SAAS,CAACO,IAAI,CAACN,OAAO,EAAEK,KAAK,EAAEF,KAAK,EAAE,EAAEF,MAAM,CAAC,EAAE;QACpDC,UAAU,CAACK,IAAI,CAAC,KAAK,CAAC;QACtBL,UAAU,CAACM,QAAQ,EAAE;;IAEzB,CAAC,EACD;MACEN,UAAU,CAACK,IAAI,CAAC,IAAI,CAAC;MACrBL,UAAU,CAACM,QAAQ,EAAE;IACvB,CAAC,CACF,CACF;EACH,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}