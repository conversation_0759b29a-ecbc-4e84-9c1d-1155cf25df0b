{"ast": null, "code": "import { operate } from '../util/lift';\nimport { innerFrom } from '../observable/innerFrom';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function audit(durationSelector) {\n  return operate(function (source, subscriber) {\n    var hasValue = false;\n    var lastValue = null;\n    var durationSubscriber = null;\n    var isComplete = false;\n    var endDuration = function () {\n      durationSubscriber === null || durationSubscriber === void 0 ? void 0 : durationSubscriber.unsubscribe();\n      durationSubscriber = null;\n      if (hasValue) {\n        hasValue = false;\n        var value = lastValue;\n        lastValue = null;\n        subscriber.next(value);\n      }\n      isComplete && subscriber.complete();\n    };\n    var cleanupDuration = function () {\n      durationSubscriber = null;\n      isComplete && subscriber.complete();\n    };\n    source.subscribe(createOperatorSubscriber(subscriber, function (value) {\n      hasValue = true;\n      lastValue = value;\n      if (!durationSubscriber) {\n        innerFrom(durationSelector(value)).subscribe(durationSubscriber = createOperatorSubscriber(subscriber, endDuration, cleanupDuration));\n      }\n    }, function () {\n      isComplete = true;\n      (!hasValue || !durationSubscriber || durationSubscriber.closed) && subscriber.complete();\n    }));\n  });\n}", "map": {"version": 3, "names": ["operate", "innerFrom", "createOperatorSubscriber", "audit", "durationSelector", "source", "subscriber", "hasValue", "lastValue", "durationSubscriber", "isComplete", "endDuration", "unsubscribe", "value", "next", "complete", "cleanupDuration", "subscribe", "closed"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\audit.ts"], "sourcesContent": ["import { Subscriber } from '../Subscriber';\nimport { MonoTypeOperatorFunction, ObservableInput } from '../types';\n\nimport { operate } from '../util/lift';\nimport { innerFrom } from '../observable/innerFrom';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\n\n/**\n * Ignores source values for a duration determined by another Observable, then\n * emits the most recent value from the source Observable, then repeats this\n * process.\n *\n * <span class=\"informal\">It's like {@link auditTime}, but the silencing\n * duration is determined by a second Observable.</span>\n *\n * ![](audit.svg)\n *\n * `audit` is similar to `throttle`, but emits the last value from the silenced\n * time window, instead of the first value. `audit` emits the most recent value\n * from the source Observable on the output Observable as soon as its internal\n * timer becomes disabled, and ignores source values while the timer is enabled.\n * Initially, the timer is disabled. As soon as the first source value arrives,\n * the timer is enabled by calling the `durationSelector` function with the\n * source value, which returns the \"duration\" Observable. When the duration\n * Observable emits a value, the timer is disabled, then the most\n * recent source value is emitted on the output Observable, and this process\n * repeats for the next source value.\n *\n * ## Example\n *\n * Emit clicks at a rate of at most one click per second\n *\n * ```ts\n * import { fromEvent, audit, interval } from 'rxjs';\n *\n * const clicks = fromEvent(document, 'click');\n * const result = clicks.pipe(audit(ev => interval(1000)));\n * result.subscribe(x => console.log(x));\n * ```\n *\n * @see {@link auditTime}\n * @see {@link debounce}\n * @see {@link delayWhen}\n * @see {@link sample}\n * @see {@link throttle}\n *\n * @param durationSelector A function\n * that receives a value from the source Observable, for computing the silencing\n * duration, returned as an Observable or a Promise.\n * @return A function that returns an Observable that performs rate-limiting of\n * emissions from the source Observable.\n */\nexport function audit<T>(durationSelector: (value: T) => ObservableInput<any>): MonoTypeOperatorFunction<T> {\n  return operate((source, subscriber) => {\n    let hasValue = false;\n    let lastValue: T | null = null;\n    let durationSubscriber: Subscriber<any> | null = null;\n    let isComplete = false;\n\n    const endDuration = () => {\n      durationSubscriber?.unsubscribe();\n      durationSubscriber = null;\n      if (hasValue) {\n        hasValue = false;\n        const value = lastValue!;\n        lastValue = null;\n        subscriber.next(value);\n      }\n      isComplete && subscriber.complete();\n    };\n\n    const cleanupDuration = () => {\n      durationSubscriber = null;\n      isComplete && subscriber.complete();\n    };\n\n    source.subscribe(\n      createOperatorSubscriber(\n        subscriber,\n        (value) => {\n          hasValue = true;\n          lastValue = value;\n          if (!durationSubscriber) {\n            innerFrom(durationSelector(value)).subscribe(\n              (durationSubscriber = createOperatorSubscriber(subscriber, endDuration, cleanupDuration))\n            );\n          }\n        },\n        () => {\n          isComplete = true;\n          (!hasValue || !durationSubscriber || durationSubscriber.closed) && subscriber.complete();\n        }\n      )\n    );\n  });\n}\n"], "mappings": "AAGA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,SAAS,QAAQ,yBAAyB;AACnD,SAASC,wBAAwB,QAAQ,sBAAsB;AA+C/D,OAAM,SAAUC,KAAKA,CAAIC,gBAAoD;EAC3E,OAAOJ,OAAO,CAAC,UAACK,MAAM,EAAEC,UAAU;IAChC,IAAIC,QAAQ,GAAG,KAAK;IACpB,IAAIC,SAAS,GAAa,IAAI;IAC9B,IAAIC,kBAAkB,GAA2B,IAAI;IACrD,IAAIC,UAAU,GAAG,KAAK;IAEtB,IAAMC,WAAW,GAAG,SAAAA,CAAA;MAClBF,kBAAkB,aAAlBA,kBAAkB,uBAAlBA,kBAAkB,CAAEG,WAAW,EAAE;MACjCH,kBAAkB,GAAG,IAAI;MACzB,IAAIF,QAAQ,EAAE;QACZA,QAAQ,GAAG,KAAK;QAChB,IAAMM,KAAK,GAAGL,SAAU;QACxBA,SAAS,GAAG,IAAI;QAChBF,UAAU,CAACQ,IAAI,CAACD,KAAK,CAAC;;MAExBH,UAAU,IAAIJ,UAAU,CAACS,QAAQ,EAAE;IACrC,CAAC;IAED,IAAMC,eAAe,GAAG,SAAAA,CAAA;MACtBP,kBAAkB,GAAG,IAAI;MACzBC,UAAU,IAAIJ,UAAU,CAACS,QAAQ,EAAE;IACrC,CAAC;IAEDV,MAAM,CAACY,SAAS,CACdf,wBAAwB,CACtBI,UAAU,EACV,UAACO,KAAK;MACJN,QAAQ,GAAG,IAAI;MACfC,SAAS,GAAGK,KAAK;MACjB,IAAI,CAACJ,kBAAkB,EAAE;QACvBR,SAAS,CAACG,gBAAgB,CAACS,KAAK,CAAC,CAAC,CAACI,SAAS,CACzCR,kBAAkB,GAAGP,wBAAwB,CAACI,UAAU,EAAEK,WAAW,EAAEK,eAAe,CAAE,CAC1F;;IAEL,CAAC,EACD;MACEN,UAAU,GAAG,IAAI;MACjB,CAAC,CAACH,QAAQ,IAAI,CAACE,kBAAkB,IAAIA,kBAAkB,CAACS,MAAM,KAAKZ,UAAU,CAACS,QAAQ,EAAE;IAC1F,CAAC,CACF,CACF;EACH,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}