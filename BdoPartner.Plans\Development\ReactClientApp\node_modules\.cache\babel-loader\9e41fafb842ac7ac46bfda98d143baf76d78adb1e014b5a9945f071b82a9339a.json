{"ast": null, "code": "import { innerFrom } from '../observable/innerFrom';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { operate } from '../util/lift';\nexport function catchError(selector) {\n  return operate(function (source, subscriber) {\n    var innerSub = null;\n    var syncUnsub = false;\n    var handledResult;\n    innerSub = source.subscribe(createOperatorSubscriber(subscriber, undefined, undefined, function (err) {\n      handledResult = innerFrom(selector(err, catchError(selector)(source)));\n      if (innerSub) {\n        innerSub.unsubscribe();\n        innerSub = null;\n        handledResult.subscribe(subscriber);\n      } else {\n        syncUnsub = true;\n      }\n    }));\n    if (syncUnsub) {\n      innerSub.unsubscribe();\n      innerSub = null;\n      handledResult.subscribe(subscriber);\n    }\n  });\n}", "map": {"version": 3, "names": ["innerFrom", "createOperatorSubscriber", "operate", "catchError", "selector", "source", "subscriber", "innerSub", "syncUnsub", "handledResult", "subscribe", "undefined", "err", "unsubscribe"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\catchError.ts"], "sourcesContent": ["import { Observable } from '../Observable';\n\nimport { ObservableInput, OperatorFunction, ObservedValueOf } from '../types';\nimport { Subscription } from '../Subscription';\nimport { innerFrom } from '../observable/innerFrom';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { operate } from '../util/lift';\n\nexport function catchError<T, O extends ObservableInput<any>>(\n  selector: (err: any, caught: Observable<T>) => O\n): OperatorFunction<T, T | ObservedValueOf<O>>;\n\n/**\n * Catches errors on the observable to be handled by returning a new observable or throwing an error.\n *\n * <span class=\"informal\">\n * It only listens to the error channel and ignores notifications.\n * Handles errors from the source observable, and maps them to a new observable.\n * The error may also be rethrown, or a new error can be thrown to emit an error from the result.\n * </span>\n *\n * ![](catch.png)\n *\n * This operator handles errors, but forwards along all other events to the resulting observable.\n * If the source observable terminates with an error, it will map that error to a new observable,\n * subscribe to it, and forward all of its events to the resulting observable.\n *\n * ## Examples\n *\n * Continue with a different Observable when there's an error\n *\n * ```ts\n * import { of, map, catchError } from 'rxjs';\n *\n * of(1, 2, 3, 4, 5)\n *   .pipe(\n *     map(n => {\n *       if (n === 4) {\n *         throw 'four!';\n *       }\n *       return n;\n *     }),\n *     catchError(err => of('I', 'II', 'III', 'IV', 'V'))\n *   )\n *   .subscribe(x => console.log(x));\n *   // 1, 2, 3, I, II, III, IV, V\n * ```\n *\n * Retry the caught source Observable again in case of error, similar to `retry()` operator\n *\n * ```ts\n * import { of, map, catchError, take } from 'rxjs';\n *\n * of(1, 2, 3, 4, 5)\n *   .pipe(\n *     map(n => {\n *       if (n === 4) {\n *         throw 'four!';\n *       }\n *       return n;\n *     }),\n *     catchError((err, caught) => caught),\n *     take(30)\n *   )\n *   .subscribe(x => console.log(x));\n *   // 1, 2, 3, 1, 2, 3, ...\n * ```\n *\n * Throw a new error when the source Observable throws an error\n *\n * ```ts\n * import { of, map, catchError } from 'rxjs';\n *\n * of(1, 2, 3, 4, 5)\n *   .pipe(\n *     map(n => {\n *       if (n === 4) {\n *         throw 'four!';\n *       }\n *       return n;\n *     }),\n *     catchError(err => {\n *       throw 'error in source. Details: ' + err;\n *     })\n *   )\n *   .subscribe({\n *     next: x => console.log(x),\n *     error: err => console.log(err)\n *   });\n *   // 1, 2, 3, error in source. Details: four!\n * ```\n *\n * @see {@link onErrorResumeNext}\n * @see {@link repeat}\n * @see {@link repeatWhen}\n * @see {@link retry }\n * @see {@link retryWhen}\n *\n * @param selector A function that takes as arguments `err`, which is the error, and `caught`, which\n * is the source observable, in case you'd like to \"retry\" that observable by returning it again.\n * Whatever observable is returned by the `selector` will be used to continue the observable chain.\n * @return A function that returns an Observable that originates from either\n * the source or the Observable returned by the `selector` function.\n */\nexport function catchError<T, O extends ObservableInput<any>>(\n  selector: (err: any, caught: Observable<T>) => O\n): OperatorFunction<T, T | ObservedValueOf<O>> {\n  return operate((source, subscriber) => {\n    let innerSub: Subscription | null = null;\n    let syncUnsub = false;\n    let handledResult: Observable<ObservedValueOf<O>>;\n\n    innerSub = source.subscribe(\n      createOperatorSubscriber(subscriber, undefined, undefined, (err) => {\n        handledResult = innerFrom(selector(err, catchError(selector)(source)));\n        if (innerSub) {\n          innerSub.unsubscribe();\n          innerSub = null;\n          handledResult.subscribe(subscriber);\n        } else {\n          // We don't have an innerSub yet, that means the error was synchronous\n          // because the subscribe call hasn't returned yet.\n          syncUnsub = true;\n        }\n      })\n    );\n\n    if (syncUnsub) {\n      // We have a synchronous error, we need to make sure to\n      // finalize right away. This ensures that callbacks in the `finalize` operator are called\n      // at the right time, and that finalization occurs at the expected\n      // time between the source error and the subscription to the\n      // next observable.\n      innerSub.unsubscribe();\n      innerSub = null;\n      handledResult!.subscribe(subscriber);\n    }\n  });\n}\n"], "mappings": "AAIA,SAASA,SAAS,QAAQ,yBAAyB;AACnD,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,SAASC,OAAO,QAAQ,cAAc;AAkGtC,OAAM,SAAUC,UAAUA,CACxBC,QAAgD;EAEhD,OAAOF,OAAO,CAAC,UAACG,MAAM,EAAEC,UAAU;IAChC,IAAIC,QAAQ,GAAwB,IAAI;IACxC,IAAIC,SAAS,GAAG,KAAK;IACrB,IAAIC,aAA6C;IAEjDF,QAAQ,GAAGF,MAAM,CAACK,SAAS,CACzBT,wBAAwB,CAACK,UAAU,EAAEK,SAAS,EAAEA,SAAS,EAAE,UAACC,GAAG;MAC7DH,aAAa,GAAGT,SAAS,CAACI,QAAQ,CAACQ,GAAG,EAAET,UAAU,CAACC,QAAQ,CAAC,CAACC,MAAM,CAAC,CAAC,CAAC;MACtE,IAAIE,QAAQ,EAAE;QACZA,QAAQ,CAACM,WAAW,EAAE;QACtBN,QAAQ,GAAG,IAAI;QACfE,aAAa,CAACC,SAAS,CAACJ,UAAU,CAAC;OACpC,MAAM;QAGLE,SAAS,GAAG,IAAI;;IAEpB,CAAC,CAAC,CACH;IAED,IAAIA,SAAS,EAAE;MAMbD,QAAQ,CAACM,WAAW,EAAE;MACtBN,QAAQ,GAAG,IAAI;MACfE,aAAc,CAACC,SAAS,CAACJ,UAAU,CAAC;;EAExC,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}