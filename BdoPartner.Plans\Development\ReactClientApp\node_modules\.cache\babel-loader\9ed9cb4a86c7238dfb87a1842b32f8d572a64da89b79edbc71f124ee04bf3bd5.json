{"ast": null, "code": "var _jsxFileName = \"C:\\\\git\\\\Partner-Site\\\\BdoPartner.Plans\\\\Development\\\\ReactClientApp\\\\src\\\\components\\\\admin\\\\PartnerAnnualPlansTable.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef, useCallback, useMemo, useContext } from \"react\";\nimport { Card } from \"primereact/card\";\nimport { DataTable } from \"primereact/datatable\";\nimport { Column } from \"primereact/column\";\nimport { Button } from \"primereact/button\";\nimport { InputText } from \"primereact/inputtext\";\nimport { Dropdown } from \"primereact/dropdown\";\nimport { Checkbox } from \"primereact/checkbox\";\nimport { Toast } from \"primereact/toast\";\nimport { Tag } from \"primereact/tag\";\nimport { Tooltip } from \"primereact/tooltip\";\nimport { ConfirmDialog } from \"primereact/confirmdialog\";\nimport partnerAnnualPlanService from \"../../services/partnerAnnualPlanService\";\nimport { messageService } from \"../../core/message/messageService\";\nimport { getFormStatusClass } from \"../../core/enumertions/formStatus\";\nimport { UserFormRole } from \"../../core/enumertions/userFormRole\";\nimport { formatDateTime } from \"../../core/utils/dateUtils\";\nimport PartnerPlanDetailsDialog from \"./PartnerPlanDetailsDialog\";\nimport { useNavigate } from \"react-router-dom\";\nimport { AuthContext } from \"../../core/auth/components/authProvider\";\nimport { Role } from \"../../core/enumertions/role\";\nimport ReviewerCommentsDialog from \"../common/ReviewerCommentsDialog\";\nimport formService from \"../../services/formService\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport const PartnerAnnualPlansTable = ({\n  onBack,\n  onPlanDetailsClick,\n  initialFilters = {},\n  initialPageSize = 10,\n  reviewerMode = false // true if in open from reviewer section in dashboard, false if open from admin section in dashboard\n}) => {\n  _s();\n  var _user$roles;\n  const navigate = useNavigate();\n  const authService = useContext(AuthContext);\n  const user = authService.getUser();\n  const [loading, setLoading] = useState(false);\n  const [partnerPlans, setPartnerPlans] = useState([]);\n  const [totalRecords, setTotalRecords] = useState(0);\n  const [first, setFirst] = useState(0);\n  const [rows, setRows] = useState(initialPageSize);\n  const [globalFilter, setGlobalFilter] = useState(\"\");\n\n  // Search debouncing\n  const searchTimeoutRef = useRef(null);\n\n  // Sorting states\n  const [sortField, setSortField] = useState(\"modifiedOn\");\n  const [sortOrder, setSortOrder] = useState(-1); // -1 for desc, 1 for asc\n\n  // Filter states - Start with current year selected by default\n  const currentYear = new Date().getFullYear();\n  const [selectedYear, setSelectedYear] = useState(initialFilters.year || currentYear);\n  const [selectedCycle, setSelectedCycle] = useState(initialFilters.cycle || null);\n  const [selectedPlanStatus, setSelectedPlanStatus] = useState(initialFilters.status || null);\n  const [selectedServiceLine, setSelectedServiceLine] = useState(initialFilters.serviceLine || null);\n  const [selectedSubServiceLine, setSelectedSubServiceLine] = useState(initialFilters.subServiceLine || null);\n  const [assignedReviewsOnly, setAssignedReviewsOnly] = useState(reviewerMode);\n\n  // Check if user is ELT (Executive Leadership)\n  const isELTUser = user === null || user === void 0 ? void 0 : (_user$roles = user.roles) === null || _user$roles === void 0 ? void 0 : _user$roles.includes(Role.ExecutiveLeadership);\n  const toast = useRef(null);\n  const loadingRef = useRef(false);\n\n  // State for reviewer comments dialog\n  const [showCommentsDialog, setShowCommentsDialog] = useState(false);\n  const [selectedFormForSendBack, setSelectedFormForSendBack] = useState(null);\n  const [sendingBack, setSendingBack] = useState(false);\n\n  // State for user form roles (formId -> userFormRole mapping)\n  const [userFormRoles, setUserFormRoles] = useState({});\n\n  // State for filter options\n  const [filterOptions, setFilterOptions] = useState({\n    years: [{\n      label: \"All\",\n      value: null\n    }],\n    cycles: [{\n      label: \"All\",\n      value: null\n    }],\n    statuses: [{\n      label: \"All\",\n      value: null\n    }],\n    serviceLines: [{\n      label: \"All\",\n      value: null\n    }],\n    subServiceLines: [{\n      label: \"All\",\n      value: null\n    }]\n  });\n\n  // Computed filter options - use transformed data from loadFilterOptions\n  const yearOptions = useMemo(() => {\n    return filterOptions.years || [{\n      label: \"All\",\n      value: null\n    }];\n  }, [filterOptions.years]);\n  const cycleOptions = useMemo(() => {\n    return filterOptions.cycles || [{\n      label: \"All\",\n      value: null\n    }];\n  }, [filterOptions.cycles]);\n  const planStatusOptions = useMemo(() => {\n    return filterOptions.statuses || [{\n      label: \"All\",\n      value: null\n    }];\n  }, [filterOptions.statuses]);\n  const serviceLineOptions = useMemo(() => {\n    return filterOptions.serviceLines || [{\n      label: \"All\",\n      value: null\n    }];\n  }, [filterOptions.serviceLines]);\n  const subServiceLineOptions = useMemo(() => {\n    return filterOptions.subServiceLines || [{\n      label: \"All\",\n      value: null\n    }];\n  }, [filterOptions.subServiceLines]);\n\n  // Dialog states\n  const [showDetailsDialog, setShowDetailsDialog] = useState(false);\n  const [selectedPlanDetails, setSelectedPlanDetails] = useState(null);\n  const [loadingPlanDetails, setLoadingPlanDetails] = useState(false);\n\n  // Load filter options from API\n  const loadFilterOptions = useCallback(async (cycleFilter = null) => {\n    try {\n      const response = await partnerAnnualPlanService.getFilterOptions(cycleFilter);\n      if ((response.resultStatus === 1 || response.resultStatus === \"Success\") && response.item) {\n        const apiData = response.item;\n\n        // Ensure current year is included in the years list\n        const apiYears = apiData.years || [];\n        const yearsSet = new Set(apiYears);\n        if (!yearsSet.has(currentYear)) {\n          apiYears.push(currentYear);\n          apiYears.sort((a, b) => b - a); // Sort descending\n        }\n\n        // Transform API data to dropdown format\n        const transformedOptions = {\n          years: [{\n            label: \"All\",\n            value: null\n          }, ...apiYears.map(year => ({\n            label: year.toString(),\n            value: year\n          }))],\n          cycles: [{\n            label: \"All\",\n            value: null\n          }, ...(apiData.cycles || []).map(cycle => ({\n            label: cycle.value,\n            value: cycle.key\n          }))],\n          statuses: [{\n            label: \"All\",\n            value: null\n          }, ...(apiData.statuses || []).map(status => ({\n            label: status.value,\n            value: status.key\n          }))],\n          serviceLines: [{\n            label: \"All\",\n            value: null\n          }, ...(apiData.serviceLines || []).map(sl => ({\n            label: sl,\n            value: sl\n          }))],\n          subServiceLines: [{\n            label: \"All\",\n            value: null\n          }, ...(apiData.subServiceLines || []).map(sl => ({\n            label: sl,\n            value: sl\n          }))]\n        };\n        setFilterOptions(transformedOptions);\n      }\n    } catch (error) {\n      console.error(\"Error loading filter options:\", error);\n    }\n  }, [currentYear]);\n  const loadPartnerPlans = useCallback(async (pageIndex = 0, pageSize = 10, searchTerm = \"\", sortBy = null, sortDirection = null) => {\n    if (loadingRef.current) {\n      return;\n    }\n    try {\n      setLoading(true);\n      loadingRef.current = true;\n\n      // Build search request for API call\n      const searchRequest = {\n        page: pageIndex + 1,\n        pageSize: pageSize,\n        partnerName: searchTerm,\n        // Fixed: backend expects 'partnerName' not 'searchTerm'\n        year: selectedYear,\n        cycle: selectedCycle,\n        status: selectedPlanStatus,\n        serviceLine: selectedServiceLine,\n        subServiceLine: selectedSubServiceLine,\n        assignedReviewsOnly: assignedReviewsOnly,\n        sortBy: sortBy || sortField,\n        sortDirection: sortDirection || (sortOrder === 1 ? \"asc\" : \"desc\")\n      };\n\n      // Call the API\n      const result = await partnerAnnualPlanService.searchPartnerAnnualPlans(searchRequest);\n\n      // Check for success - API returns resultStatus as number (1 = Success)\n      if (result.resultStatus === 1 || result.resultStatus === \"Success\") {\n        var _result$item;\n        const plans = ((_result$item = result.item) === null || _result$item === void 0 ? void 0 : _result$item.items) || [];\n\n        // Fetch user form roles for each form in parallel\n        if (plans.length > 0) {\n          var _result$item2;\n          console.log(`🔄 Fetching user roles for ${plans.length} forms...`);\n          const rolePromises = plans.map(async plan => {\n            if (plan.id) {\n              try {\n                const role = await formService.getCurrentUserFormRole(plan.id);\n                console.log(`✅ Got role ${role} for form ${plan.id}`);\n                return {\n                  formId: plan.id,\n                  role\n                };\n              } catch (error) {\n                console.error(`❌ Error fetching user role for form ${plan.id}:`, error);\n                return {\n                  formId: plan.id,\n                  role: null\n                };\n              }\n            }\n            return {\n              formId: null,\n              role: null\n            };\n          });\n          const roleResults = await Promise.all(rolePromises);\n          const rolesMap = {};\n          roleResults.forEach(result => {\n            if (result.formId) {\n              rolesMap[result.formId] = result.role;\n            }\n          });\n          console.log(`🎯 Setting user form roles:`, rolesMap);\n          setUserFormRoles(rolesMap);\n          setPartnerPlans(plans);\n          setTotalRecords(((_result$item2 = result.item) === null || _result$item2 === void 0 ? void 0 : _result$item2.totalCount) || 0);\n        } else {\n          setPartnerPlans([]);\n          setTotalRecords(0);\n          setUserFormRoles({});\n        }\n      } else {\n        console.error(\"Error loading partner plans:\", result.message);\n        messageService.errorToast(`Error loading partner plans: ${result.message || \"Unknown error\"}`);\n        setPartnerPlans([]);\n        setTotalRecords(0);\n        setUserFormRoles({});\n      }\n    } catch (error) {\n      console.error(\"Error loading partner plans:\", error);\n      messageService.errorToast(\"Error loading partner plans\");\n      setPartnerPlans([]);\n      setTotalRecords(0);\n      setUserFormRoles({});\n    } finally {\n      setLoading(false);\n      loadingRef.current = false;\n    }\n  }, [selectedYear, selectedCycle, selectedPlanStatus, selectedServiceLine, selectedSubServiceLine, assignedReviewsOnly, sortField, sortOrder]);\n\n  // Initial load on component mount\n  useEffect(() => {\n    loadFilterOptions(); // Load filter options first\n    loadPartnerPlans(0, rows, globalFilter);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []); // Only run on mount\n\n  // Manual search handler\n  const handleSearch = () => {\n    setFirst(0);\n    loadPartnerPlans(0, rows, globalFilter);\n  };\n\n  // Reload when filters change (auto-trigger)\n  useEffect(() => {\n    if (loadingRef.current) return; // Prevent multiple calls\n    loadPartnerPlans(0, rows, globalFilter);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [selectedYear, selectedCycle, selectedPlanStatus, selectedServiceLine, selectedSubServiceLine, assignedReviewsOnly]);\n\n  // Cleanup search timeout on component unmount\n  useEffect(() => {\n    return () => {\n      if (searchTimeoutRef.current) {\n        clearTimeout(searchTimeoutRef.current);\n      }\n    };\n  }, []);\n  const onPageChange = event => {\n    const newFirst = event.first;\n    const newRows = event.rows;\n    const newPageIndex = Math.floor(newFirst / newRows);\n    setFirst(newFirst);\n    setRows(newRows);\n    loadPartnerPlans(newPageIndex, newRows, globalFilter);\n  };\n  const onGlobalFilterChange = value => {\n    setGlobalFilter(value);\n    setFirst(0);\n\n    // Clear previous timeout\n    if (searchTimeoutRef.current) {\n      clearTimeout(searchTimeoutRef.current);\n    }\n\n    // Only search if value has at least 3 characters or is empty (for clearing search)\n    if (value.trim().length >= 3 || value.trim().length === 0) {\n      // Set new timeout for debounced search (500ms for better network performance)\n      searchTimeoutRef.current = setTimeout(() => {\n        loadPartnerPlans(0, rows, value);\n      }, 500);\n    }\n  };\n  const onCycleChange = async value => {\n    setSelectedCycle(value);\n    setSelectedPlanStatus(null); // Reset plan status when cycle changes\n    setFirst(0);\n\n    // Reload filter options to get cycle-aware status options\n    await loadFilterOptions(value);\n\n    // Reload data with new cycle filter (auto-trigger)\n    loadPartnerPlans(0, rows, globalFilter);\n  };\n  const onSort = event => {\n    const {\n      sortField: newSortField,\n      sortOrder: newSortOrder\n    } = event;\n    setSortField(newSortField);\n    setSortOrder(newSortOrder);\n    setFirst(0);\n\n    // Convert PrimeReact field names to API field names\n    const apiSortField = convertToApiSortField(newSortField);\n    const apiSortDirection = newSortOrder === 1 ? \"asc\" : \"desc\";\n    loadPartnerPlans(0, rows, globalFilter, apiSortField, apiSortDirection);\n  };\n  const convertToApiSortField = primeReactField => {\n    const fieldMapping = {\n      partnerName: \"partnername\",\n      year: \"year\",\n      planStatus: \"status\",\n      midYearReviewStatus: \"status\",\n      finalReviewStatus: \"status\",\n      modifiedOn: \"modifiedon\",\n      createdOn: \"createdon\"\n    };\n    return fieldMapping[primeReactField] || \"modifiedon\";\n  };\n  const clearFilters = async () => {\n    setSelectedYear(currentYear); // Reset to current year instead of null\n    setSelectedCycle(null);\n    setSelectedPlanStatus(null);\n    setSelectedServiceLine(null);\n    setSelectedSubServiceLine(null);\n    setAssignedReviewsOnly(reviewerMode); // Keep true for reviewer mode, false for others\n    setGlobalFilter(\"\");\n    setFirst(0);\n\n    // Reload filter options to reset status options to all cycles\n    await loadFilterOptions(null);\n\n    // Explicitly reload data with cleared filters\n    // Use setTimeout to ensure state updates are applied first\n    setTimeout(() => {\n      loadPartnerPlans(0, rows, \"\", \"modifiedon\", \"desc\");\n    }, 0);\n  };\n  const clearSorting = () => {\n    // Reset sorting to default\n    setSortField(\"modifiedOn\");\n    setSortOrder(-1);\n    setFirst(0);\n    loadPartnerPlans(0, rows, globalFilter, \"modifiedon\", \"desc\");\n  };\n  const handleExport = async () => {\n    try {\n      const searchRequest = {\n        partnerName: globalFilter,\n        // Fixed: backend expects 'partnerName' not 'searchTerm'\n        year: selectedYear,\n        status: selectedPlanStatus,\n        serviceLine: selectedServiceLine,\n        subServiceLine: selectedSubServiceLine,\n        assignedReviewsOnly: assignedReviewsOnly,\n        exportToExcel: true,\n        page: 1,\n        pageSize: 10000 // Export all records\n      };\n      const blob = await partnerAnnualPlanService.exportToExcel(searchRequest);\n      partnerAnnualPlanService.downloadFile(blob, `PartnerAnnualPlans_${new Date().toISOString().split(\"T\")[0]}.xlsx`);\n      messageService.successToast(\"Export completed successfully\");\n    } catch (error) {\n      console.error(\"Export error:\", error);\n      messageService.errorToast(\"Export failed\");\n    }\n  };\n\n  // Status body template with orange warning for \"Not Started\"\n  const statusBodyTemplate = rowData => {\n    const status = rowData.planStatus || \"Not Started\";\n    if (status === \"Not Started\") {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex align-items-center gap-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"pi pi-exclamation-triangle\",\n          style: {\n            color: \"#ff9800\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 394,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            color: \"#ff9800\"\n          },\n          children: \"Not Started\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 395,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 393,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(Tag, {\n      value: status\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 400,\n      columnNumber: 12\n    }, this);\n  };\n\n  // Mid Year Status body template\n  const midYearStatusBodyTemplate = rowData => {\n    const status = rowData.midYearReviewStatus || \"Not Started\";\n    if (status === \"Not Started\") {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex align-items-center gap-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"pi pi-exclamation-triangle\",\n          style: {\n            color: \"#ff9800\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 410,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            color: \"#ff9800\"\n          },\n          children: \"Not Started\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 411,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 409,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(Tag, {\n      value: status\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 416,\n      columnNumber: 12\n    }, this);\n  };\n\n  // Year End Status body template\n  const yearEndStatusBodyTemplate = rowData => {\n    const status = rowData.finalReviewStatus || \"Not Started\";\n    if (status === \"Not Started\") {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex align-items-center gap-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"pi pi-exclamation-triangle\",\n          style: {\n            color: \"#ff9800\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 426,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            color: \"#ff9800\"\n          },\n          children: \"Not Started\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 427,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 425,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(Tag, {\n      value: status\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 432,\n      columnNumber: 12\n    }, this);\n  };\n\n  // Audit information body templates\n  const createdByBodyTemplate = rowData => {\n    if (!rowData.createdBy && !rowData.createdOn) return \"-\";\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"audit-info\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"audit-user\",\n        children: rowData.createdBy || \"-\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 441,\n        columnNumber: 9\n      }, this), rowData.createdOn && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"audit-date\",\n        children: formatDateTime(rowData.createdOn)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 442,\n        columnNumber: 31\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 440,\n      columnNumber: 7\n    }, this);\n  };\n  const modifiedByBodyTemplate = rowData => {\n    if (!rowData.modifiedBy && !rowData.modifiedOn) return \"-\";\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"audit-info\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"audit-user\",\n        children: rowData.modifiedBy || \"N/A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 452,\n        columnNumber: 9\n      }, this), rowData.modifiedOn && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"audit-date\",\n        children: formatDateTime(rowData.modifiedOn)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 453,\n        columnNumber: 32\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 451,\n      columnNumber: 7\n    }, this);\n  };\n  const planSubmissionBodyTemplate = rowData => {\n    if (!rowData.planCycleSubmittedByName && !rowData.planCycleSubmittedOn) return \"-\";\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"audit-info\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"audit-user\",\n        children: rowData.planCycleSubmittedByName || \"N/A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 463,\n        columnNumber: 9\n      }, this), rowData.planCycleSubmittedOn && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"audit-date\",\n        children: formatDateTime(rowData.planCycleSubmittedOn)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 464,\n        columnNumber: 42\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 462,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Handle More button click to show plan details\n  const handleShowPlanDetails = async planId => {\n    try {\n      setLoadingPlanDetails(true);\n      setShowDetailsDialog(true);\n      setSelectedPlanDetails(null);\n      const response = await partnerAnnualPlanService.getPartnerAnnualPlanById(planId);\n      if ((response.resultStatus === 1 || response.resultStatus === \"Success\") && response.item) {\n        setSelectedPlanDetails(response.item);\n        if (onPlanDetailsClick) {\n          onPlanDetailsClick(response.item);\n        }\n      } else {\n        messageService.errorToast(\"Failed to load partner plan details\");\n        setShowDetailsDialog(false);\n      }\n    } catch (error) {\n      console.error(\"Error loading plan details:\", error);\n      messageService.errorToast(\"Failed to load partner plan details\");\n      setShowDetailsDialog(false);\n    } finally {\n      setLoadingPlanDetails(false);\n    }\n  };\n\n  // Handle dialog close\n  const handleCloseDetailsDialog = () => {\n    setShowDetailsDialog(false);\n    setSelectedPlanDetails(null);\n    setLoadingPlanDetails(false);\n  };\n\n  // Handle View Plan button click\n  const handleViewPlan = formId => {\n    navigate(`/partner-plan?formId=${formId}`);\n  };\n\n  // Handle Send Back to Partner button click\n  const handleSendBackToPartner = rowData => {\n    setSelectedFormForSendBack(rowData);\n    setShowCommentsDialog(true);\n  };\n\n  // Handle reviewer comments dialog confirm\n  const handleCommentsConfirm = async comments => {\n    if (!selectedFormForSendBack) return;\n    setSendingBack(true);\n    try {\n      await formService.sendBackToPartner(selectedFormForSendBack.id, comments);\n      messageService.successToast(\"Form sent back to partner successfully\");\n\n      // Refresh the data to show updated status\n      loadPartnerPlans();\n\n      // Close dialog\n      setShowCommentsDialog(false);\n      setSelectedFormForSendBack(null);\n    } catch (error) {\n      messageService.errorToast(error.message || \"Failed to send form back to partner\");\n    } finally {\n      setSendingBack(false);\n    }\n  };\n\n  // Handle reviewer comments dialog hide\n  const handleCommentsDialogHide = () => {\n    if (!sendingBack) {\n      setShowCommentsDialog(false);\n      setSelectedFormForSendBack(null);\n    }\n  };\n\n  // Check if form can be sent back to partner (MidYear or YearEnd Under Review status)\n  const canSendBackToPartner = useCallback(rowData => {\n    if (!reviewerMode) return false; // Only available in reviewer mode\n\n    // Check if current user is a reviewer for this form based on server-side role determination\n    const userRole = userFormRoles[rowData.id];\n\n    // Debug logging\n    console.log(`🔍 canSendBackToPartner for form ${rowData.id}:`, {\n      userRole,\n      expectedRole: UserFormRole.Reviewer,\n      userFormRoles,\n      midYearReviewStatus: rowData.midYearReviewStatus,\n      finalReviewStatus: rowData.finalReviewStatus\n    });\n\n    // Check if role is loaded and matches Reviewer (value 2)\n    if (userRole === undefined || userRole === null) {\n      console.log(`⚠️ User role not loaded yet for form ${rowData.id}`);\n      return false; // Role not loaded yet\n    }\n    if (userRole !== UserFormRole.Reviewer) {\n      console.log(`❌ User role ${userRole} is not Reviewer (${UserFormRole.Reviewer}) for form ${rowData.id}`);\n      return false;\n    }\n\n    // Check if form is in \"Under Review\" status for MidYear or YearEnd cycles\n    // Note: The DTO uses string fields, not numeric enum values\n    const canSendBack = rowData.midYearReviewStatus === \"Under Review\" || rowData.finalReviewStatus === \"Under Review\";\n    console.log(`✅ Status check for form ${rowData.id}:`, {\n      midYearReviewStatus: rowData.midYearReviewStatus,\n      finalReviewStatus: rowData.finalReviewStatus,\n      expectedStatus: \"Under Review\",\n      canSendBack\n    });\n    return canSendBack;\n  }, [reviewerMode, userFormRoles]); // Dependencies: reviewerMode and userFormRoles\n\n  // Actions body template - memoized to re-render when userFormRoles changes\n  const actionsBodyTemplate = useCallback(rowData => {\n    const showSendBackButton = canSendBackToPartner(rowData);\n\n    // Check if this is a PartnerReviewer record without a form\n    const isNoFormGenerated = rowData.isNoFormGenerated === true;\n    const viewPlanTooltip = isNoFormGenerated ? \"Partner has not started filling out their plan yet\" : \"View Plan\";\n    const buttonId = `view-plan-btn-${rowData.id}`;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex align-items-center gap-2\",\n      children: [isNoFormGenerated ? /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          id: buttonId,\n          className: \"inline-block\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            icon: \"pi pi-eye\",\n            className: \"p-button-text p-button-sm p-button-secondary\",\n            disabled: true,\n            onClick: () => {}\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 603,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 602,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          target: `#${buttonId}`,\n          content: viewPlanTooltip,\n          position: \"top\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 605,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true) : /*#__PURE__*/_jsxDEV(Button, {\n        icon: \"pi pi-eye\",\n        className: \"p-button-text p-button-sm\",\n        tooltip: viewPlanTooltip,\n        onClick: () => handleViewPlan(rowData.id)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 608,\n        columnNumber: 13\n      }, this), showSendBackButton && /*#__PURE__*/_jsxDEV(Button, {\n        icon: \"pi pi-send\",\n        className: \"p-button-text p-button-sm p-button-warning\",\n        tooltip: \"Send Back to Partner\",\n        onClick: () => handleSendBackToPartner(rowData)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 613,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        icon: \"pi pi-ellipsis-h\",\n        className: \"p-button-text p-button-sm\",\n        tooltip: \"More\",\n        onClick: () => handleShowPlanDetails(rowData.id)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 621,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 599,\n      columnNumber: 9\n    }, this);\n  }, [userFormRoles, reviewerMode]); // Re-render when userFormRoles or reviewerMode changes\n\n  const header = /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"partner-annual-plans-header\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"top-row\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"search-field\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"filter-label\",\n          children: \"\\xA0\\xA0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 633,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-input-icon-left search-input-wrapper\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"pi pi-search\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 635,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(InputText, {\n            type: \"search\",\n            value: globalFilter,\n            onChange: e => onGlobalFilterChange(e.target.value),\n            placeholder: \"Search Partner Name\",\n            className: \"search-input\",\n            tooltip: \"Type at least 3 characters to search\",\n            tooltipOptions: {\n              position: \"top\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 636,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 634,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 632,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"filter-dropdowns\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-field\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"filter-label\",\n            children: \"Year\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 649,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n            value: selectedYear,\n            options: yearOptions,\n            onChange: e => setSelectedYear(e.value),\n            optionLabel: \"label\",\n            optionValue: \"value\",\n            placeholder: \"All Years\",\n            className: \"filter-dropdown\",\n            tooltip: \"Year\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 650,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 648,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-field\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"filter-label\",\n            children: \"Cycles\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 662,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n            value: selectedCycle,\n            options: cycleOptions,\n            onChange: e => onCycleChange(e.value),\n            optionLabel: \"label\",\n            optionValue: \"value\",\n            placeholder: \"All Cycles\",\n            className: \"filter-dropdown\",\n            tooltip: \"Cycles\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 663,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 661,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-field\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"filter-label\",\n            children: \"Plan Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 675,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n            value: selectedPlanStatus,\n            options: planStatusOptions,\n            onChange: e => setSelectedPlanStatus(e.value),\n            optionLabel: \"label\",\n            optionValue: \"value\",\n            placeholder: \"Plan Status\",\n            className: \"filter-dropdown\",\n            tooltip: \"Plan Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 676,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 674,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-field\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"filter-label\",\n            children: \"Service Line\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 688,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n            value: selectedServiceLine,\n            options: serviceLineOptions,\n            onChange: e => setSelectedServiceLine(e.value),\n            optionLabel: \"label\",\n            optionValue: \"value\",\n            placeholder: \"Service Line\",\n            className: \"filter-dropdown\",\n            tooltip: \"Service Line\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 689,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 687,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-field\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"filter-label\",\n            children: \"Sub-service Line\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 701,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n            value: selectedSubServiceLine,\n            options: subServiceLineOptions,\n            onChange: e => setSelectedSubServiceLine(e.value),\n            optionLabel: \"label\",\n            optionValue: \"value\",\n            placeholder: \"Sub-service Line\",\n            className: \"filter-dropdown\",\n            tooltip: \"Sub-service Line\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 702,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 700,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 647,\n        columnNumber: 9\n      }, this), isELTUser && !reviewerMode && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"elt-filter-section\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-field checkbox-field\",\n          children: [/*#__PURE__*/_jsxDEV(Checkbox, {\n            inputId: \"assignedReviewsOnly\",\n            checked: assignedReviewsOnly,\n            onChange: e => setAssignedReviewsOnly(e.checked),\n            className: \"filter-checkbox\",\n            disabled: reviewerMode\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 728,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"assignedReviewsOnly\",\n            className: \"checkbox-label\",\n            children: reviewerMode ? \"My assigned reviews\" : \"Assigned reviews only\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 735,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 727,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 726,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"action-buttons\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          label: \"Clear Filters\",\n          className: \"p-button-outlined p-button-sm p-button-rounded \",\n          onClick: clearFilters\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 742,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          label: \"Clear Sorting\",\n          className: \"p-button-outlined p-button-sm p-button-rounded\",\n          onClick: clearSorting\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 743,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          icon: \"pi pi-download\",\n          label: \"Export to Excel\",\n          className: \"p-button-primary-rounded\",\n          onClick: handleExport\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 744,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 741,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 631,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 630,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-content-wrapper\",\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        children: [/*#__PURE__*/_jsxDEV(Toast, {\n          ref: toast\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 754,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ConfirmDialog, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 755,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DataTable, {\n          value: partnerPlans,\n          loading: loading,\n          header: header,\n          emptyMessage: \"No partner annual plans found\",\n          sortMode: \"single\",\n          sortField: sortField,\n          sortOrder: sortOrder,\n          onSort: onSort,\n          paginator: true,\n          lazy: true,\n          rows: rows,\n          first: first,\n          totalRecords: totalRecords,\n          onPage: onPageChange,\n          rowsPerPageOptions: [10, 25, 50, 100],\n          className: \"p-datatable-gridlines\",\n          children: [/*#__PURE__*/_jsxDEV(Column, {\n            field: \"partnerName\",\n            header: \"Partner Name\",\n            sortable: true,\n            style: {\n              minWidth: \"150px\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 774,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Column, {\n            field: \"serviceLine\",\n            header: \"Service Line\",\n            sortable: true,\n            style: {\n              minWidth: \"120px\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 775,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Column, {\n            field: \"subServiceLine\",\n            header: \"Sub-Service Line\",\n            sortable: true,\n            style: {\n              minWidth: \"150px\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 776,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Column, {\n            field: \"year\",\n            header: \"Year\",\n            sortable: true,\n            style: {\n              minWidth: \"80px\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 777,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Column, {\n            field: \"planStatusString\",\n            header: \"Plan Status\",\n            body: statusBodyTemplate,\n            sortable: true,\n            style: {\n              minWidth: \"120px\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 778,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Column, {\n            field: \"midYearStatusString\",\n            header: \"Mid Year Review Status\",\n            body: midYearStatusBodyTemplate,\n            sortable: true,\n            style: {\n              minWidth: \"180px\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 779,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Column, {\n            field: \"yearEndStatusString\",\n            header: \"Final Review Status\",\n            body: yearEndStatusBodyTemplate,\n            sortable: true,\n            style: {\n              minWidth: \"160px\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 786,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Column, {\n            header: \"Actions\",\n            body: actionsBodyTemplate,\n            style: {\n              minWidth: \"120px\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 793,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 756,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 753,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 752,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PartnerPlanDetailsDialog, {\n      visible: showDetailsDialog,\n      onHide: handleCloseDetailsDialog,\n      planDetails: selectedPlanDetails,\n      loading: loadingPlanDetails\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 799,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ReviewerCommentsDialog, {\n      visible: showCommentsDialog,\n      onHide: handleCommentsDialogHide,\n      onConfirm: handleCommentsConfirm,\n      loading: sendingBack,\n      title: \"Send Back to Partner\",\n      message: `Please provide comments explaining why the form for ${(selectedFormForSendBack === null || selectedFormForSendBack === void 0 ? void 0 : selectedFormForSendBack.partnerName) || \"this partner\"} needs to be revised:`\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 807,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 751,\n    columnNumber: 5\n  }, this);\n};\n_s(PartnerAnnualPlansTable, \"BfXGl5BFx1x6YvrNG8zgIeWdGA4=\", false, function () {\n  return [useNavigate];\n});\n_c = PartnerAnnualPlansTable;\nexport default PartnerAnnualPlansTable;\nvar _c;\n$RefreshReg$(_c, \"PartnerAnnualPlansTable\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useCallback", "useMemo", "useContext", "Card", "DataTable", "Column", "<PERSON><PERSON>", "InputText", "Dropdown", "Checkbox", "Toast", "Tag", "<PERSON><PERSON><PERSON>", "ConfirmDialog", "partnerAnnualPlanService", "messageService", "getFormStatusClass", "UserFormRole", "formatDateTime", "PartnerPlanDetailsDialog", "useNavigate", "AuthContext", "Role", "ReviewerCommentsDialog", "formService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "PartnerAnnualPlansTable", "onBack", "onPlanDetailsClick", "initialFilters", "initialPageSize", "reviewerMode", "_s", "_user$roles", "navigate", "authService", "user", "getUser", "loading", "setLoading", "partnerPlans", "setPartnerPlans", "totalRecords", "setTotalRecords", "first", "<PERSON><PERSON><PERSON><PERSON>", "rows", "setRows", "globalFilter", "setGlobalFilter", "searchTimeoutRef", "sortField", "setSortField", "sortOrder", "setSortOrder", "currentYear", "Date", "getFullYear", "selected<PERSON>ear", "setSelectedYear", "year", "selectedCycle", "setSelectedCycle", "cycle", "selectedPlanStatus", "setSelectedPlanStatus", "status", "selectedServiceLine", "setSelectedServiceLine", "serviceLine", "selectedSubServiceLine", "setSelectedSubServiceLine", "subServiceLine", "assignedReviewsOnly", "setAssignedReviewsOnly", "isELTUser", "roles", "includes", "ExecutiveLeadership", "toast", "loadingRef", "showCommentsDialog", "setShowCommentsDialog", "selectedFormForSendBack", "setSelectedFormForSendBack", "sendingBack", "setSendingBack", "userFormRoles", "setUserFormRoles", "filterOptions", "setFilterOptions", "years", "label", "value", "cycles", "statuses", "serviceLines", "subServiceLines", "yearOptions", "cycleOptions", "planStatusOptions", "serviceLineOptions", "subServiceLineOptions", "showDetailsDialog", "setShowDetailsDialog", "selectedPlanDetails", "setSelectedPlanDetails", "loadingPlanDetails", "setLoadingPlanDetails", "loadFilterOptions", "cycleFilter", "response", "getFilterOptions", "resultStatus", "item", "apiData", "apiYears", "yearsSet", "Set", "has", "push", "sort", "a", "b", "transformedOptions", "map", "toString", "key", "sl", "error", "console", "loadPartnerPlans", "pageIndex", "pageSize", "searchTerm", "sortBy", "sortDirection", "current", "searchRequest", "page", "partner<PERSON>ame", "result", "searchPartnerAnnualPlans", "_result$item", "plans", "items", "length", "_result$item2", "log", "rolePromises", "plan", "id", "role", "getCurrentUserFormRole", "formId", "roleResults", "Promise", "all", "rolesMap", "for<PERSON>ach", "totalCount", "message", "errorToast", "handleSearch", "clearTimeout", "onPageChange", "event", "newFirst", "newRows", "newPageIndex", "Math", "floor", "onGlobalFilterChange", "trim", "setTimeout", "onCycleChange", "onSort", "newSortField", "newSortOrder", "apiSortField", "convertToApiSortField", "apiSortDirection", "primeReactField", "fieldMapping", "planStatus", "midYearReviewStatus", "finalReviewStatus", "modifiedOn", "createdOn", "clearFilters", "clearSorting", "handleExport", "exportToExcel", "blob", "downloadFile", "toISOString", "split", "successToast", "statusBodyTemplate", "rowData", "className", "children", "style", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "midYearStatusBodyTemplate", "yearEndStatusBodyTemplate", "createdByBodyTemplate", "created<PERSON>y", "modifiedByBodyTemplate", "modifiedBy", "planSubmissionBodyTemplate", "planCycleSubmittedByName", "planCycleSubmittedOn", "handleShowPlanDetails", "planId", "getPartnerAnnualPlanById", "handleCloseDetailsDialog", "handleViewPlan", "handleSendBackToPartner", "handleCommentsConfirm", "comments", "sendBackToPartner", "handleCommentsDialogHide", "canSendBackToPartner", "userRole", "expectedRole", "Reviewer", "undefined", "canSendBack", "expectedStatus", "actionsBodyTemplate", "showSendBackButton", "isNoFormGenerated", "viewPlanTooltip", "buttonId", "icon", "disabled", "onClick", "target", "content", "position", "tooltip", "header", "type", "onChange", "e", "placeholder", "tooltipOptions", "options", "optionLabel", "optionValue", "inputId", "checked", "htmlFor", "ref", "emptyMessage", "sortMode", "paginator", "lazy", "onPage", "rowsPerPageOptions", "field", "sortable", "min<PERSON><PERSON><PERSON>", "body", "visible", "onHide", "planDetails", "onConfirm", "title", "_c", "$RefreshReg$"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/components/admin/PartnerAnnualPlansTable.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef, useCallback, useMemo, useContext } from \"react\";\r\nimport { Card } from \"primereact/card\";\r\nimport { DataTable } from \"primereact/datatable\";\r\nimport { Column } from \"primereact/column\";\r\nimport { <PERSON><PERSON> } from \"primereact/button\";\r\nimport { InputText } from \"primereact/inputtext\";\r\nimport { Dropdown } from \"primereact/dropdown\";\r\nimport { Checkbox } from \"primereact/checkbox\";\r\nimport { Toast } from \"primereact/toast\";\r\nimport { Tag } from \"primereact/tag\";\r\nimport { Tooltip } from \"primereact/tooltip\";\r\nimport { ConfirmDialog } from \"primereact/confirmdialog\";\r\nimport partnerAnnualPlanService from \"../../services/partnerAnnualPlanService\";\r\nimport { messageService } from \"../../core/message/messageService\";\r\nimport { getFormStatusClass } from \"../../core/enumertions/formStatus\";\r\nimport { UserFormRole } from \"../../core/enumertions/userFormRole\";\r\nimport { formatDateTime } from \"../../core/utils/dateUtils\";\r\nimport PartnerPlanDetailsDialog from \"./PartnerPlanDetailsDialog\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { AuthContext } from \"../../core/auth/components/authProvider\";\r\nimport { Role } from \"../../core/enumertions/role\";\r\nimport ReviewerCommentsDialog from \"../common/ReviewerCommentsDialog\";\r\nimport formService from \"../../services/formService\";\r\n\r\nexport const PartnerAnnualPlansTable = ({\r\n  onBack,\r\n  onPlanDetailsClick,\r\n  initialFilters = {},\r\n  initialPageSize = 10,\r\n  reviewerMode = false, // true if in open from reviewer section in dashboard, false if open from admin section in dashboard\r\n}) => {\r\n  const navigate = useNavigate();\r\n  const authService = useContext(AuthContext);\r\n  const user = authService.getUser();\r\n\r\n  const [loading, setLoading] = useState(false);\r\n  const [partnerPlans, setPartnerPlans] = useState([]);\r\n  const [totalRecords, setTotalRecords] = useState(0);\r\n  const [first, setFirst] = useState(0);\r\n  const [rows, setRows] = useState(initialPageSize);\r\n  const [globalFilter, setGlobalFilter] = useState(\"\");\r\n\r\n  // Search debouncing\r\n  const searchTimeoutRef = useRef(null);\r\n\r\n  // Sorting states\r\n  const [sortField, setSortField] = useState(\"modifiedOn\");\r\n  const [sortOrder, setSortOrder] = useState(-1); // -1 for desc, 1 for asc\r\n\r\n  // Filter states - Start with current year selected by default\r\n  const currentYear = new Date().getFullYear();\r\n  const [selectedYear, setSelectedYear] = useState(initialFilters.year || currentYear);\r\n  const [selectedCycle, setSelectedCycle] = useState(initialFilters.cycle || null);\r\n  const [selectedPlanStatus, setSelectedPlanStatus] = useState(initialFilters.status || null);\r\n  const [selectedServiceLine, setSelectedServiceLine] = useState(initialFilters.serviceLine || null);\r\n  const [selectedSubServiceLine, setSelectedSubServiceLine] = useState(initialFilters.subServiceLine || null);\r\n  const [assignedReviewsOnly, setAssignedReviewsOnly] = useState(reviewerMode);\r\n\r\n  // Check if user is ELT (Executive Leadership)\r\n  const isELTUser = user?.roles?.includes(Role.ExecutiveLeadership);\r\n\r\n  const toast = useRef(null);\r\n  const loadingRef = useRef(false);\r\n\r\n  // State for reviewer comments dialog\r\n  const [showCommentsDialog, setShowCommentsDialog] = useState(false);\r\n  const [selectedFormForSendBack, setSelectedFormForSendBack] = useState(null);\r\n  const [sendingBack, setSendingBack] = useState(false);\r\n\r\n  // State for user form roles (formId -> userFormRole mapping)\r\n  const [userFormRoles, setUserFormRoles] = useState({});\r\n\r\n  // State for filter options\r\n  const [filterOptions, setFilterOptions] = useState({\r\n    years: [{ label: \"All\", value: null }],\r\n    cycles: [{ label: \"All\", value: null }],\r\n    statuses: [{ label: \"All\", value: null }],\r\n    serviceLines: [{ label: \"All\", value: null }],\r\n    subServiceLines: [{ label: \"All\", value: null }],\r\n  });\r\n\r\n  // Computed filter options - use transformed data from loadFilterOptions\r\n  const yearOptions = useMemo(() => {\r\n    return filterOptions.years || [{ label: \"All\", value: null }];\r\n  }, [filterOptions.years]);\r\n\r\n  const cycleOptions = useMemo(() => {\r\n    return filterOptions.cycles || [{ label: \"All\", value: null }];\r\n  }, [filterOptions.cycles]);\r\n\r\n  const planStatusOptions = useMemo(() => {\r\n    return filterOptions.statuses || [{ label: \"All\", value: null }];\r\n  }, [filterOptions.statuses]);\r\n\r\n  const serviceLineOptions = useMemo(() => {\r\n    return filterOptions.serviceLines || [{ label: \"All\", value: null }];\r\n  }, [filterOptions.serviceLines]);\r\n\r\n  const subServiceLineOptions = useMemo(() => {\r\n    return filterOptions.subServiceLines || [{ label: \"All\", value: null }];\r\n  }, [filterOptions.subServiceLines]);\r\n\r\n  // Dialog states\r\n  const [showDetailsDialog, setShowDetailsDialog] = useState(false);\r\n  const [selectedPlanDetails, setSelectedPlanDetails] = useState(null);\r\n  const [loadingPlanDetails, setLoadingPlanDetails] = useState(false);\r\n\r\n  // Load filter options from API\r\n  const loadFilterOptions = useCallback(\r\n    async (cycleFilter = null) => {\r\n      try {\r\n        const response = await partnerAnnualPlanService.getFilterOptions(cycleFilter);\r\n\r\n        if ((response.resultStatus === 1 || response.resultStatus === \"Success\") && response.item) {\r\n          const apiData = response.item;\r\n\r\n          // Ensure current year is included in the years list\r\n          const apiYears = apiData.years || [];\r\n          const yearsSet = new Set(apiYears);\r\n          if (!yearsSet.has(currentYear)) {\r\n            apiYears.push(currentYear);\r\n            apiYears.sort((a, b) => b - a); // Sort descending\r\n          }\r\n\r\n          // Transform API data to dropdown format\r\n          const transformedOptions = {\r\n            years: [{ label: \"All\", value: null }, ...apiYears.map((year) => ({ label: year.toString(), value: year }))],\r\n            cycles: [\r\n              { label: \"All\", value: null },\r\n              ...(apiData.cycles || []).map((cycle) => ({\r\n                label: cycle.value,\r\n                value: cycle.key,\r\n              })),\r\n            ],\r\n            statuses: [\r\n              { label: \"All\", value: null },\r\n              ...(apiData.statuses || []).map((status) => ({\r\n                label: status.value,\r\n                value: status.key,\r\n              })),\r\n            ],\r\n            serviceLines: [{ label: \"All\", value: null }, ...(apiData.serviceLines || []).map((sl) => ({ label: sl, value: sl }))],\r\n            subServiceLines: [{ label: \"All\", value: null }, ...(apiData.subServiceLines || []).map((sl) => ({ label: sl, value: sl }))],\r\n          };\r\n\r\n          setFilterOptions(transformedOptions);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error loading filter options:\", error);\r\n      }\r\n    },\r\n    [currentYear]\r\n  );\r\n\r\n  const loadPartnerPlans = useCallback(\r\n    async (pageIndex = 0, pageSize = 10, searchTerm = \"\", sortBy = null, sortDirection = null) => {\r\n      if (loadingRef.current) {\r\n        return;\r\n      }\r\n\r\n      try {\r\n        setLoading(true);\r\n        loadingRef.current = true;\r\n\r\n        // Build search request for API call\r\n        const searchRequest = {\r\n          page: pageIndex + 1,\r\n          pageSize: pageSize,\r\n          partnerName: searchTerm, // Fixed: backend expects 'partnerName' not 'searchTerm'\r\n          year: selectedYear,\r\n          cycle: selectedCycle,\r\n          status: selectedPlanStatus,\r\n          serviceLine: selectedServiceLine,\r\n          subServiceLine: selectedSubServiceLine,\r\n          assignedReviewsOnly: assignedReviewsOnly,\r\n          sortBy: sortBy || sortField,\r\n          sortDirection: sortDirection || (sortOrder === 1 ? \"asc\" : \"desc\"),\r\n        };\r\n\r\n        // Call the API\r\n        const result = await partnerAnnualPlanService.searchPartnerAnnualPlans(searchRequest);\r\n\r\n        // Check for success - API returns resultStatus as number (1 = Success)\r\n        if (result.resultStatus === 1 || result.resultStatus === \"Success\") {\r\n          const plans = result.item?.items || [];\r\n\r\n          // Fetch user form roles for each form in parallel\r\n          if (plans.length > 0) {\r\n            console.log(`🔄 Fetching user roles for ${plans.length} forms...`);\r\n            const rolePromises = plans.map(async (plan) => {\r\n              if (plan.id) {\r\n                try {\r\n                  const role = await formService.getCurrentUserFormRole(plan.id);\r\n                  console.log(`✅ Got role ${role} for form ${plan.id}`);\r\n                  return { formId: plan.id, role };\r\n                } catch (error) {\r\n                  console.error(`❌ Error fetching user role for form ${plan.id}:`, error);\r\n                  return { formId: plan.id, role: null };\r\n                }\r\n              }\r\n              return { formId: null, role: null };\r\n            });\r\n\r\n            const roleResults = await Promise.all(rolePromises);\r\n            const rolesMap = {};\r\n            roleResults.forEach((result) => {\r\n              if (result.formId) {\r\n                rolesMap[result.formId] = result.role;\r\n              }\r\n            });\r\n            console.log(`🎯 Setting user form roles:`, rolesMap);\r\n            setUserFormRoles(rolesMap);\r\n            setPartnerPlans(plans);\r\n            setTotalRecords(result.item?.totalCount || 0);\r\n          } else {\r\n            setPartnerPlans([]);\r\n            setTotalRecords(0);\r\n            setUserFormRoles({});\r\n          }\r\n        } else {\r\n          console.error(\"Error loading partner plans:\", result.message);\r\n          messageService.errorToast(`Error loading partner plans: ${result.message || \"Unknown error\"}`);\r\n          setPartnerPlans([]);\r\n          setTotalRecords(0);\r\n          setUserFormRoles({});\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error loading partner plans:\", error);\r\n        messageService.errorToast(\"Error loading partner plans\");\r\n        setPartnerPlans([]);\r\n        setTotalRecords(0);\r\n        setUserFormRoles({});\r\n      } finally {\r\n        setLoading(false);\r\n        loadingRef.current = false;\r\n      }\r\n    },\r\n    [selectedYear, selectedCycle, selectedPlanStatus, selectedServiceLine, selectedSubServiceLine, assignedReviewsOnly, sortField, sortOrder]\r\n  );\r\n\r\n  // Initial load on component mount\r\n  useEffect(() => {\r\n    loadFilterOptions(); // Load filter options first\r\n    loadPartnerPlans(0, rows, globalFilter);\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, []); // Only run on mount\r\n\r\n  // Manual search handler\r\n  const handleSearch = () => {\r\n    setFirst(0);\r\n    loadPartnerPlans(0, rows, globalFilter);\r\n  };\r\n\r\n  // Reload when filters change (auto-trigger)\r\n  useEffect(() => {\r\n    if (loadingRef.current) return; // Prevent multiple calls\r\n    loadPartnerPlans(0, rows, globalFilter);\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [selectedYear, selectedCycle, selectedPlanStatus, selectedServiceLine, selectedSubServiceLine, assignedReviewsOnly]);\r\n\r\n  // Cleanup search timeout on component unmount\r\n  useEffect(() => {\r\n    return () => {\r\n      if (searchTimeoutRef.current) {\r\n        clearTimeout(searchTimeoutRef.current);\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  const onPageChange = (event) => {\r\n    const newFirst = event.first;\r\n    const newRows = event.rows;\r\n    const newPageIndex = Math.floor(newFirst / newRows);\r\n\r\n    setFirst(newFirst);\r\n    setRows(newRows);\r\n    loadPartnerPlans(newPageIndex, newRows, globalFilter);\r\n  };\r\n\r\n  const onGlobalFilterChange = (value) => {\r\n    setGlobalFilter(value);\r\n    setFirst(0);\r\n\r\n    // Clear previous timeout\r\n    if (searchTimeoutRef.current) {\r\n      clearTimeout(searchTimeoutRef.current);\r\n    }\r\n\r\n    // Only search if value has at least 3 characters or is empty (for clearing search)\r\n    if (value.trim().length >= 3 || value.trim().length === 0) {\r\n      // Set new timeout for debounced search (500ms for better network performance)\r\n      searchTimeoutRef.current = setTimeout(() => {\r\n        loadPartnerPlans(0, rows, value);\r\n      }, 500);\r\n    }\r\n  };\r\n\r\n  const onCycleChange = async (value) => {\r\n    setSelectedCycle(value);\r\n    setSelectedPlanStatus(null); // Reset plan status when cycle changes\r\n    setFirst(0);\r\n\r\n    // Reload filter options to get cycle-aware status options\r\n    await loadFilterOptions(value);\r\n\r\n    // Reload data with new cycle filter (auto-trigger)\r\n    loadPartnerPlans(0, rows, globalFilter);\r\n  };\r\n\r\n  const onSort = (event) => {\r\n    const { sortField: newSortField, sortOrder: newSortOrder } = event;\r\n    setSortField(newSortField);\r\n    setSortOrder(newSortOrder);\r\n    setFirst(0);\r\n\r\n    // Convert PrimeReact field names to API field names\r\n    const apiSortField = convertToApiSortField(newSortField);\r\n    const apiSortDirection = newSortOrder === 1 ? \"asc\" : \"desc\";\r\n\r\n    loadPartnerPlans(0, rows, globalFilter, apiSortField, apiSortDirection);\r\n  };\r\n\r\n  const convertToApiSortField = (primeReactField) => {\r\n    const fieldMapping = {\r\n      partnerName: \"partnername\",\r\n      year: \"year\",\r\n      planStatus: \"status\",\r\n      midYearReviewStatus: \"status\",\r\n      finalReviewStatus: \"status\",\r\n      modifiedOn: \"modifiedon\",\r\n      createdOn: \"createdon\",\r\n    };\r\n    return fieldMapping[primeReactField] || \"modifiedon\";\r\n  };\r\n\r\n  const clearFilters = async () => {\r\n    setSelectedYear(currentYear); // Reset to current year instead of null\r\n    setSelectedCycle(null);\r\n    setSelectedPlanStatus(null);\r\n    setSelectedServiceLine(null);\r\n    setSelectedSubServiceLine(null);\r\n    setAssignedReviewsOnly(reviewerMode); // Keep true for reviewer mode, false for others\r\n    setGlobalFilter(\"\");\r\n    setFirst(0);\r\n\r\n    // Reload filter options to reset status options to all cycles\r\n    await loadFilterOptions(null);\r\n\r\n    // Explicitly reload data with cleared filters\r\n    // Use setTimeout to ensure state updates are applied first\r\n    setTimeout(() => {\r\n      loadPartnerPlans(0, rows, \"\", \"modifiedon\", \"desc\");\r\n    }, 0);\r\n  };\r\n\r\n  const clearSorting = () => {\r\n    // Reset sorting to default\r\n    setSortField(\"modifiedOn\");\r\n    setSortOrder(-1);\r\n    setFirst(0);\r\n    loadPartnerPlans(0, rows, globalFilter, \"modifiedon\", \"desc\");\r\n  };\r\n\r\n  const handleExport = async () => {\r\n    try {\r\n      const searchRequest = {\r\n        partnerName: globalFilter, // Fixed: backend expects 'partnerName' not 'searchTerm'\r\n        year: selectedYear,\r\n        status: selectedPlanStatus,\r\n        serviceLine: selectedServiceLine,\r\n        subServiceLine: selectedSubServiceLine,\r\n        assignedReviewsOnly: assignedReviewsOnly,\r\n        exportToExcel: true,\r\n        page: 1,\r\n        pageSize: 10000, // Export all records\r\n      };\r\n\r\n      const blob = await partnerAnnualPlanService.exportToExcel(searchRequest);\r\n      partnerAnnualPlanService.downloadFile(blob, `PartnerAnnualPlans_${new Date().toISOString().split(\"T\")[0]}.xlsx`);\r\n      messageService.successToast(\"Export completed successfully\");\r\n    } catch (error) {\r\n      console.error(\"Export error:\", error);\r\n      messageService.errorToast(\"Export failed\");\r\n    }\r\n  };\r\n\r\n  // Status body template with orange warning for \"Not Started\"\r\n  const statusBodyTemplate = (rowData) => {\r\n    const status = rowData.planStatus || \"Not Started\";\r\n\r\n    if (status === \"Not Started\") {\r\n      return (\r\n        <div className=\"flex align-items-center gap-1\">\r\n          <i className=\"pi pi-exclamation-triangle\" style={{ color: \"#ff9800\" }}></i>\r\n          <span style={{ color: \"#ff9800\" }}>Not Started</span>\r\n        </div>\r\n      );\r\n    }\r\n\r\n    return <Tag value={status} />;\r\n  };\r\n\r\n  // Mid Year Status body template\r\n  const midYearStatusBodyTemplate = (rowData) => {\r\n    const status = rowData.midYearReviewStatus || \"Not Started\";\r\n\r\n    if (status === \"Not Started\") {\r\n      return (\r\n        <div className=\"flex align-items-center gap-1\">\r\n          <i className=\"pi pi-exclamation-triangle\" style={{ color: \"#ff9800\" }}></i>\r\n          <span style={{ color: \"#ff9800\" }}>Not Started</span>\r\n        </div>\r\n      );\r\n    }\r\n\r\n    return <Tag value={status} />;\r\n  };\r\n\r\n  // Year End Status body template\r\n  const yearEndStatusBodyTemplate = (rowData) => {\r\n    const status = rowData.finalReviewStatus || \"Not Started\";\r\n\r\n    if (status === \"Not Started\") {\r\n      return (\r\n        <div className=\"flex align-items-center gap-1\">\r\n          <i className=\"pi pi-exclamation-triangle\" style={{ color: \"#ff9800\" }}></i>\r\n          <span style={{ color: \"#ff9800\" }}>Not Started</span>\r\n        </div>\r\n      );\r\n    }\r\n\r\n    return <Tag value={status} />;\r\n  };\r\n\r\n  // Audit information body templates\r\n  const createdByBodyTemplate = (rowData) => {\r\n    if (!rowData.createdBy && !rowData.createdOn) return \"-\";\r\n\r\n    return (\r\n      <div className=\"audit-info\">\r\n        <div className=\"audit-user\">{rowData.createdBy || \"-\"}</div>\r\n        {rowData.createdOn && <div className=\"audit-date\">{formatDateTime(rowData.createdOn)}</div>}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  const modifiedByBodyTemplate = (rowData) => {\r\n    if (!rowData.modifiedBy && !rowData.modifiedOn) return \"-\";\r\n\r\n    return (\r\n      <div className=\"audit-info\">\r\n        <div className=\"audit-user\">{rowData.modifiedBy || \"N/A\"}</div>\r\n        {rowData.modifiedOn && <div className=\"audit-date\">{formatDateTime(rowData.modifiedOn)}</div>}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  const planSubmissionBodyTemplate = (rowData) => {\r\n    if (!rowData.planCycleSubmittedByName && !rowData.planCycleSubmittedOn) return \"-\";\r\n\r\n    return (\r\n      <div className=\"audit-info\">\r\n        <div className=\"audit-user\">{rowData.planCycleSubmittedByName || \"N/A\"}</div>\r\n        {rowData.planCycleSubmittedOn && <div className=\"audit-date\">{formatDateTime(rowData.planCycleSubmittedOn)}</div>}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  // Handle More button click to show plan details\r\n  const handleShowPlanDetails = async (planId) => {\r\n    try {\r\n      setLoadingPlanDetails(true);\r\n      setShowDetailsDialog(true);\r\n      setSelectedPlanDetails(null);\r\n\r\n      const response = await partnerAnnualPlanService.getPartnerAnnualPlanById(planId);\r\n\r\n      if ((response.resultStatus === 1 || response.resultStatus === \"Success\") && response.item) {\r\n        setSelectedPlanDetails(response.item);\r\n        if (onPlanDetailsClick) {\r\n          onPlanDetailsClick(response.item);\r\n        }\r\n      } else {\r\n        messageService.errorToast(\"Failed to load partner plan details\");\r\n        setShowDetailsDialog(false);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error loading plan details:\", error);\r\n      messageService.errorToast(\"Failed to load partner plan details\");\r\n      setShowDetailsDialog(false);\r\n    } finally {\r\n      setLoadingPlanDetails(false);\r\n    }\r\n  };\r\n\r\n  // Handle dialog close\r\n  const handleCloseDetailsDialog = () => {\r\n    setShowDetailsDialog(false);\r\n    setSelectedPlanDetails(null);\r\n    setLoadingPlanDetails(false);\r\n  };\r\n\r\n  // Handle View Plan button click\r\n  const handleViewPlan = (formId) => {\r\n    navigate(`/partner-plan?formId=${formId}`);\r\n  };\r\n\r\n  // Handle Send Back to Partner button click\r\n  const handleSendBackToPartner = (rowData) => {\r\n    setSelectedFormForSendBack(rowData);\r\n    setShowCommentsDialog(true);\r\n  };\r\n\r\n  // Handle reviewer comments dialog confirm\r\n  const handleCommentsConfirm = async (comments) => {\r\n    if (!selectedFormForSendBack) return;\r\n\r\n    setSendingBack(true);\r\n    try {\r\n      await formService.sendBackToPartner(selectedFormForSendBack.id, comments);\r\n      messageService.successToast(\"Form sent back to partner successfully\");\r\n\r\n      // Refresh the data to show updated status\r\n      loadPartnerPlans();\r\n\r\n      // Close dialog\r\n      setShowCommentsDialog(false);\r\n      setSelectedFormForSendBack(null);\r\n    } catch (error) {\r\n      messageService.errorToast(error.message || \"Failed to send form back to partner\");\r\n    } finally {\r\n      setSendingBack(false);\r\n    }\r\n  };\r\n\r\n  // Handle reviewer comments dialog hide\r\n  const handleCommentsDialogHide = () => {\r\n    if (!sendingBack) {\r\n      setShowCommentsDialog(false);\r\n      setSelectedFormForSendBack(null);\r\n    }\r\n  };\r\n\r\n  // Check if form can be sent back to partner (MidYear or YearEnd Under Review status)\r\n  const canSendBackToPartner = useCallback(\r\n    (rowData) => {\r\n      if (!reviewerMode) return false; // Only available in reviewer mode\r\n\r\n      // Check if current user is a reviewer for this form based on server-side role determination\r\n      const userRole = userFormRoles[rowData.id];\r\n\r\n      // Debug logging\r\n      console.log(`🔍 canSendBackToPartner for form ${rowData.id}:`, {\r\n        userRole,\r\n        expectedRole: UserFormRole.Reviewer,\r\n        userFormRoles,\r\n        midYearReviewStatus: rowData.midYearReviewStatus,\r\n        finalReviewStatus: rowData.finalReviewStatus,\r\n      });\r\n\r\n      // Check if role is loaded and matches Reviewer (value 2)\r\n      if (userRole === undefined || userRole === null) {\r\n        console.log(`⚠️ User role not loaded yet for form ${rowData.id}`);\r\n        return false; // Role not loaded yet\r\n      }\r\n\r\n      if (userRole !== UserFormRole.Reviewer) {\r\n        console.log(`❌ User role ${userRole} is not Reviewer (${UserFormRole.Reviewer}) for form ${rowData.id}`);\r\n        return false;\r\n      }\r\n\r\n      // Check if form is in \"Under Review\" status for MidYear or YearEnd cycles\r\n      // Note: The DTO uses string fields, not numeric enum values\r\n      const canSendBack = rowData.midYearReviewStatus === \"Under Review\" || rowData.finalReviewStatus === \"Under Review\";\r\n\r\n      console.log(`✅ Status check for form ${rowData.id}:`, {\r\n        midYearReviewStatus: rowData.midYearReviewStatus,\r\n        finalReviewStatus: rowData.finalReviewStatus,\r\n        expectedStatus: \"Under Review\",\r\n        canSendBack,\r\n      });\r\n\r\n      return canSendBack;\r\n    },\r\n    [reviewerMode, userFormRoles]\r\n  ); // Dependencies: reviewerMode and userFormRoles\r\n\r\n  // Actions body template - memoized to re-render when userFormRoles changes\r\n  const actionsBodyTemplate = useCallback(\r\n    (rowData) => {\r\n      const showSendBackButton = canSendBackToPartner(rowData);\r\n\r\n      // Check if this is a PartnerReviewer record without a form\r\n      const isNoFormGenerated = rowData.isNoFormGenerated === true;\r\n      const viewPlanTooltip = isNoFormGenerated ? \"Partner has not started filling out their plan yet\" : \"View Plan\";\r\n      const buttonId = `view-plan-btn-${rowData.id}`;\r\n\r\n      return (\r\n        <div className=\"flex align-items-center gap-2\">\r\n          {isNoFormGenerated ? (\r\n            <>\r\n              <div id={buttonId} className=\"inline-block\">\r\n                <Button icon=\"pi pi-eye\" className=\"p-button-text p-button-sm p-button-secondary\" disabled={true} onClick={() => {}} />\r\n              </div>\r\n              <Tooltip target={`#${buttonId}`} content={viewPlanTooltip} position=\"top\" />\r\n            </>\r\n          ) : (\r\n            <Button icon=\"pi pi-eye\" className=\"p-button-text p-button-sm\" tooltip={viewPlanTooltip} onClick={() => handleViewPlan(rowData.id)} />\r\n          )}\r\n          {/* <span className=\"action-text\">View Plan</span> */}\r\n\r\n          {showSendBackButton && (\r\n            <Button\r\n              icon=\"pi pi-send\"\r\n              className=\"p-button-text p-button-sm p-button-warning\"\r\n              tooltip=\"Send Back to Partner\"\r\n              onClick={() => handleSendBackToPartner(rowData)}\r\n            />\r\n          )}\r\n\r\n          <Button icon=\"pi pi-ellipsis-h\" className=\"p-button-text p-button-sm\" tooltip=\"More\" onClick={() => handleShowPlanDetails(rowData.id)} />\r\n          {/* <span className=\"action-text\">More</span> */}\r\n        </div>\r\n      );\r\n    },\r\n    [userFormRoles, reviewerMode]\r\n  ); // Re-render when userFormRoles or reviewerMode changes\r\n\r\n  const header = (\r\n    <div className=\"partner-annual-plans-header\">\r\n      <div className=\"top-row\">\r\n        <div className=\"search-field\">\r\n          <label className=\"filter-label\">&nbsp;&nbsp;</label>\r\n          <span className=\"p-input-icon-left search-input-wrapper\">\r\n            <i className=\"pi pi-search\" />\r\n            <InputText\r\n              type=\"search\"\r\n              value={globalFilter}\r\n              onChange={(e) => onGlobalFilterChange(e.target.value)}\r\n              placeholder=\"Search Partner Name\"\r\n              className=\"search-input\"\r\n              tooltip=\"Type at least 3 characters to search\"\r\n              tooltipOptions={{ position: \"top\" }}\r\n            />\r\n          </span>\r\n        </div>\r\n        <div className=\"filter-dropdowns\">\r\n          <div className=\"filter-field\">\r\n            <label className=\"filter-label\">Year</label>\r\n            <Dropdown\r\n              value={selectedYear}\r\n              options={yearOptions}\r\n              onChange={(e) => setSelectedYear(e.value)}\r\n              optionLabel=\"label\"\r\n              optionValue=\"value\"\r\n              placeholder=\"All Years\"\r\n              className=\"filter-dropdown\"\r\n              tooltip=\"Year\"\r\n            />\r\n          </div>\r\n          <div className=\"filter-field\">\r\n            <label className=\"filter-label\">Cycles</label>\r\n            <Dropdown\r\n              value={selectedCycle}\r\n              options={cycleOptions}\r\n              onChange={(e) => onCycleChange(e.value)}\r\n              optionLabel=\"label\"\r\n              optionValue=\"value\"\r\n              placeholder=\"All Cycles\"\r\n              className=\"filter-dropdown\"\r\n              tooltip=\"Cycles\"\r\n            />\r\n          </div>\r\n          <div className=\"filter-field\">\r\n            <label className=\"filter-label\">Plan Status</label>\r\n            <Dropdown\r\n              value={selectedPlanStatus}\r\n              options={planStatusOptions}\r\n              onChange={(e) => setSelectedPlanStatus(e.value)}\r\n              optionLabel=\"label\"\r\n              optionValue=\"value\"\r\n              placeholder=\"Plan Status\"\r\n              className=\"filter-dropdown\"\r\n              tooltip=\"Plan Status\"\r\n            />\r\n          </div>\r\n          <div className=\"filter-field\">\r\n            <label className=\"filter-label\">Service Line</label>\r\n            <Dropdown\r\n              value={selectedServiceLine}\r\n              options={serviceLineOptions}\r\n              onChange={(e) => setSelectedServiceLine(e.value)}\r\n              optionLabel=\"label\"\r\n              optionValue=\"value\"\r\n              placeholder=\"Service Line\"\r\n              className=\"filter-dropdown\"\r\n              tooltip=\"Service Line\"\r\n            />\r\n          </div>\r\n          <div className=\"filter-field\">\r\n            <label className=\"filter-label\">Sub-service Line</label>\r\n            <Dropdown\r\n              value={selectedSubServiceLine}\r\n              options={subServiceLineOptions}\r\n              onChange={(e) => setSelectedSubServiceLine(e.value)}\r\n              optionLabel=\"label\"\r\n              optionValue=\"value\"\r\n              placeholder=\"Sub-service Line\"\r\n              className=\"filter-dropdown\"\r\n              tooltip=\"Sub-service Line\"\r\n            />\r\n          </div>\r\n        </div>\r\n        {/* Search Button */}\r\n        {/* <div className=\"search-button-section\">\r\n          <Button\r\n            icon=\"pi pi-search\"\r\n            label=\"Search\"\r\n            className=\"p-button-primary p-button-sm search-button p-button-rounded\"\r\n            onClick={handleSearch}\r\n            loading={loading}\r\n          />\r\n        </div> */}\r\n        {/* Reviewer Filter - Show for ELT users only */}\r\n        {isELTUser && !reviewerMode && (\r\n          <div className=\"elt-filter-section\">\r\n            <div className=\"filter-field checkbox-field\">\r\n              <Checkbox\r\n                inputId=\"assignedReviewsOnly\"\r\n                checked={assignedReviewsOnly}\r\n                onChange={(e) => setAssignedReviewsOnly(e.checked)}\r\n                className=\"filter-checkbox\"\r\n                disabled={reviewerMode}\r\n              />\r\n              <label htmlFor=\"assignedReviewsOnly\" className=\"checkbox-label\">\r\n                {reviewerMode ? \"My assigned reviews\" : \"Assigned reviews only\"}\r\n              </label>\r\n            </div>\r\n          </div>\r\n        )}\r\n        <div className=\"action-buttons\">\r\n          <Button label=\"Clear Filters\" className=\"p-button-outlined p-button-sm p-button-rounded \" onClick={clearFilters} />\r\n          <Button label=\"Clear Sorting\" className=\"p-button-outlined p-button-sm p-button-rounded\" onClick={clearSorting} />\r\n          <Button icon=\"pi pi-download\" label=\"Export to Excel\" className=\"p-button-primary-rounded\" onClick={handleExport} />\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  return (\r\n    <div>\r\n      <div className=\"page-content-wrapper\">\r\n        <Card>\r\n          <Toast ref={toast} />\r\n          <ConfirmDialog />\r\n          <DataTable\r\n            value={partnerPlans}\r\n            loading={loading}\r\n            header={header}\r\n            emptyMessage=\"No partner annual plans found\"\r\n            sortMode=\"single\"\r\n            sortField={sortField}\r\n            sortOrder={sortOrder}\r\n            onSort={onSort}\r\n            paginator\r\n            lazy\r\n            rows={rows}\r\n            first={first}\r\n            totalRecords={totalRecords}\r\n            onPage={onPageChange}\r\n            rowsPerPageOptions={[10, 25, 50, 100]}\r\n            className=\"p-datatable-gridlines\"\r\n          >\r\n            <Column field=\"partnerName\" header=\"Partner Name\" sortable style={{ minWidth: \"150px\" }} />\r\n            <Column field=\"serviceLine\" header=\"Service Line\" sortable style={{ minWidth: \"120px\" }} />\r\n            <Column field=\"subServiceLine\" header=\"Sub-Service Line\" sortable style={{ minWidth: \"150px\" }} />\r\n            <Column field=\"year\" header=\"Year\" sortable style={{ minWidth: \"80px\" }} />\r\n            <Column field=\"planStatusString\" header=\"Plan Status\" body={statusBodyTemplate} sortable style={{ minWidth: \"120px\" }} />\r\n            <Column\r\n              field=\"midYearStatusString\"\r\n              header=\"Mid Year Review Status\"\r\n              body={midYearStatusBodyTemplate}\r\n              sortable\r\n              style={{ minWidth: \"180px\" }}\r\n            />\r\n            <Column\r\n              field=\"yearEndStatusString\"\r\n              header=\"Final Review Status\"\r\n              body={yearEndStatusBodyTemplate}\r\n              sortable\r\n              style={{ minWidth: \"160px\" }}\r\n            />\r\n            <Column header=\"Actions\" body={actionsBodyTemplate} style={{ minWidth: \"120px\" }} />\r\n          </DataTable>\r\n        </Card>\r\n      </div>\r\n\r\n      {/* Partner Plan Details Dialog */}\r\n      <PartnerPlanDetailsDialog\r\n        visible={showDetailsDialog}\r\n        onHide={handleCloseDetailsDialog}\r\n        planDetails={selectedPlanDetails}\r\n        loading={loadingPlanDetails}\r\n      />\r\n\r\n      {/* Reviewer Comments Dialog */}\r\n      <ReviewerCommentsDialog\r\n        visible={showCommentsDialog}\r\n        onHide={handleCommentsDialogHide}\r\n        onConfirm={handleCommentsConfirm}\r\n        loading={sendingBack}\r\n        title=\"Send Back to Partner\"\r\n        message={`Please provide comments explaining why the form for ${selectedFormForSendBack?.partnerName || \"this partner\"} needs to be revised:`}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PartnerAnnualPlansTable;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,WAAW,EAAEC,OAAO,EAAEC,UAAU,QAAQ,OAAO;AAC5F,SAASC,IAAI,QAAQ,iBAAiB;AACtC,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,GAAG,QAAQ,gBAAgB;AACpC,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,SAASC,aAAa,QAAQ,0BAA0B;AACxD,OAAOC,wBAAwB,MAAM,yCAAyC;AAC9E,SAASC,cAAc,QAAQ,mCAAmC;AAClE,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,YAAY,QAAQ,qCAAqC;AAClE,SAASC,cAAc,QAAQ,4BAA4B;AAC3D,OAAOC,wBAAwB,MAAM,4BAA4B;AACjE,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,QAAQ,yCAAyC;AACrE,SAASC,IAAI,QAAQ,6BAA6B;AAClD,OAAOC,sBAAsB,MAAM,kCAAkC;AACrE,OAAOC,WAAW,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErD,OAAO,MAAMC,uBAAuB,GAAGA,CAAC;EACtCC,MAAM;EACNC,kBAAkB;EAClBC,cAAc,GAAG,CAAC,CAAC;EACnBC,eAAe,GAAG,EAAE;EACpBC,YAAY,GAAG,KAAK,CAAE;AACxB,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,WAAA;EACJ,MAAMC,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAC9B,MAAMkB,WAAW,GAAGpC,UAAU,CAACmB,WAAW,CAAC;EAC3C,MAAMkB,IAAI,GAAGD,WAAW,CAACE,OAAO,CAAC,CAAC;EAElC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC8C,YAAY,EAAEC,eAAe,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACgD,YAAY,EAAEC,eAAe,CAAC,GAAGjD,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM,CAACkD,KAAK,EAAEC,QAAQ,CAAC,GAAGnD,QAAQ,CAAC,CAAC,CAAC;EACrC,MAAM,CAACoD,IAAI,EAAEC,OAAO,CAAC,GAAGrD,QAAQ,CAACoC,eAAe,CAAC;EACjD,MAAM,CAACkB,YAAY,EAAEC,eAAe,CAAC,GAAGvD,QAAQ,CAAC,EAAE,CAAC;;EAEpD;EACA,MAAMwD,gBAAgB,GAAGtD,MAAM,CAAC,IAAI,CAAC;;EAErC;EACA,MAAM,CAACuD,SAAS,EAAEC,YAAY,CAAC,GAAG1D,QAAQ,CAAC,YAAY,CAAC;EACxD,MAAM,CAAC2D,SAAS,EAAEC,YAAY,CAAC,GAAG5D,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEhD;EACA,MAAM6D,WAAW,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;EAC5C,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGjE,QAAQ,CAACmC,cAAc,CAAC+B,IAAI,IAAIL,WAAW,CAAC;EACpF,MAAM,CAACM,aAAa,EAAEC,gBAAgB,CAAC,GAAGpE,QAAQ,CAACmC,cAAc,CAACkC,KAAK,IAAI,IAAI,CAAC;EAChF,MAAM,CAACC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGvE,QAAQ,CAACmC,cAAc,CAACqC,MAAM,IAAI,IAAI,CAAC;EAC3F,MAAM,CAACC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG1E,QAAQ,CAACmC,cAAc,CAACwC,WAAW,IAAI,IAAI,CAAC;EAClG,MAAM,CAACC,sBAAsB,EAAEC,yBAAyB,CAAC,GAAG7E,QAAQ,CAACmC,cAAc,CAAC2C,cAAc,IAAI,IAAI,CAAC;EAC3G,MAAM,CAACC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGhF,QAAQ,CAACqC,YAAY,CAAC;;EAE5E;EACA,MAAM4C,SAAS,GAAGvC,IAAI,aAAJA,IAAI,wBAAAH,WAAA,GAAJG,IAAI,CAAEwC,KAAK,cAAA3C,WAAA,uBAAXA,WAAA,CAAa4C,QAAQ,CAAC1D,IAAI,CAAC2D,mBAAmB,CAAC;EAEjE,MAAMC,KAAK,GAAGnF,MAAM,CAAC,IAAI,CAAC;EAC1B,MAAMoF,UAAU,GAAGpF,MAAM,CAAC,KAAK,CAAC;;EAEhC;EACA,MAAM,CAACqF,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGxF,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACyF,uBAAuB,EAAEC,0BAA0B,CAAC,GAAG1F,QAAQ,CAAC,IAAI,CAAC;EAC5E,MAAM,CAAC2F,WAAW,EAAEC,cAAc,CAAC,GAAG5F,QAAQ,CAAC,KAAK,CAAC;;EAErD;EACA,MAAM,CAAC6F,aAAa,EAAEC,gBAAgB,CAAC,GAAG9F,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEtD;EACA,MAAM,CAAC+F,aAAa,EAAEC,gBAAgB,CAAC,GAAGhG,QAAQ,CAAC;IACjDiG,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAK,CAAC,CAAC;IACtCC,MAAM,EAAE,CAAC;MAAEF,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAK,CAAC,CAAC;IACvCE,QAAQ,EAAE,CAAC;MAAEH,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAK,CAAC,CAAC;IACzCG,YAAY,EAAE,CAAC;MAAEJ,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAK,CAAC,CAAC;IAC7CI,eAAe,EAAE,CAAC;MAAEL,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAK,CAAC;EACjD,CAAC,CAAC;;EAEF;EACA,MAAMK,WAAW,GAAGpG,OAAO,CAAC,MAAM;IAChC,OAAO2F,aAAa,CAACE,KAAK,IAAI,CAAC;MAAEC,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAK,CAAC,CAAC;EAC/D,CAAC,EAAE,CAACJ,aAAa,CAACE,KAAK,CAAC,CAAC;EAEzB,MAAMQ,YAAY,GAAGrG,OAAO,CAAC,MAAM;IACjC,OAAO2F,aAAa,CAACK,MAAM,IAAI,CAAC;MAAEF,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAK,CAAC,CAAC;EAChE,CAAC,EAAE,CAACJ,aAAa,CAACK,MAAM,CAAC,CAAC;EAE1B,MAAMM,iBAAiB,GAAGtG,OAAO,CAAC,MAAM;IACtC,OAAO2F,aAAa,CAACM,QAAQ,IAAI,CAAC;MAAEH,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAK,CAAC,CAAC;EAClE,CAAC,EAAE,CAACJ,aAAa,CAACM,QAAQ,CAAC,CAAC;EAE5B,MAAMM,kBAAkB,GAAGvG,OAAO,CAAC,MAAM;IACvC,OAAO2F,aAAa,CAACO,YAAY,IAAI,CAAC;MAAEJ,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAK,CAAC,CAAC;EACtE,CAAC,EAAE,CAACJ,aAAa,CAACO,YAAY,CAAC,CAAC;EAEhC,MAAMM,qBAAqB,GAAGxG,OAAO,CAAC,MAAM;IAC1C,OAAO2F,aAAa,CAACQ,eAAe,IAAI,CAAC;MAAEL,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAK,CAAC,CAAC;EACzE,CAAC,EAAE,CAACJ,aAAa,CAACQ,eAAe,CAAC,CAAC;;EAEnC;EACA,MAAM,CAACM,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9G,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC+G,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGhH,QAAQ,CAAC,IAAI,CAAC;EACpE,MAAM,CAACiH,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGlH,QAAQ,CAAC,KAAK,CAAC;;EAEnE;EACA,MAAMmH,iBAAiB,GAAGhH,WAAW,CACnC,OAAOiH,WAAW,GAAG,IAAI,KAAK;IAC5B,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMpG,wBAAwB,CAACqG,gBAAgB,CAACF,WAAW,CAAC;MAE7E,IAAI,CAACC,QAAQ,CAACE,YAAY,KAAK,CAAC,IAAIF,QAAQ,CAACE,YAAY,KAAK,SAAS,KAAKF,QAAQ,CAACG,IAAI,EAAE;QACzF,MAAMC,OAAO,GAAGJ,QAAQ,CAACG,IAAI;;QAE7B;QACA,MAAME,QAAQ,GAAGD,OAAO,CAACxB,KAAK,IAAI,EAAE;QACpC,MAAM0B,QAAQ,GAAG,IAAIC,GAAG,CAACF,QAAQ,CAAC;QAClC,IAAI,CAACC,QAAQ,CAACE,GAAG,CAAChE,WAAW,CAAC,EAAE;UAC9B6D,QAAQ,CAACI,IAAI,CAACjE,WAAW,CAAC;UAC1B6D,QAAQ,CAACK,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,GAAGD,CAAC,CAAC,CAAC,CAAC;QAClC;;QAEA;QACA,MAAME,kBAAkB,GAAG;UACzBjC,KAAK,EAAE,CAAC;YAAEC,KAAK,EAAE,KAAK;YAAEC,KAAK,EAAE;UAAK,CAAC,EAAE,GAAGuB,QAAQ,CAACS,GAAG,CAAEjE,IAAI,KAAM;YAAEgC,KAAK,EAAEhC,IAAI,CAACkE,QAAQ,CAAC,CAAC;YAAEjC,KAAK,EAAEjC;UAAK,CAAC,CAAC,CAAC,CAAC;UAC5GkC,MAAM,EAAE,CACN;YAAEF,KAAK,EAAE,KAAK;YAAEC,KAAK,EAAE;UAAK,CAAC,EAC7B,GAAG,CAACsB,OAAO,CAACrB,MAAM,IAAI,EAAE,EAAE+B,GAAG,CAAE9D,KAAK,KAAM;YACxC6B,KAAK,EAAE7B,KAAK,CAAC8B,KAAK;YAClBA,KAAK,EAAE9B,KAAK,CAACgE;UACf,CAAC,CAAC,CAAC,CACJ;UACDhC,QAAQ,EAAE,CACR;YAAEH,KAAK,EAAE,KAAK;YAAEC,KAAK,EAAE;UAAK,CAAC,EAC7B,GAAG,CAACsB,OAAO,CAACpB,QAAQ,IAAI,EAAE,EAAE8B,GAAG,CAAE3D,MAAM,KAAM;YAC3C0B,KAAK,EAAE1B,MAAM,CAAC2B,KAAK;YACnBA,KAAK,EAAE3B,MAAM,CAAC6D;UAChB,CAAC,CAAC,CAAC,CACJ;UACD/B,YAAY,EAAE,CAAC;YAAEJ,KAAK,EAAE,KAAK;YAAEC,KAAK,EAAE;UAAK,CAAC,EAAE,GAAG,CAACsB,OAAO,CAACnB,YAAY,IAAI,EAAE,EAAE6B,GAAG,CAAEG,EAAE,KAAM;YAAEpC,KAAK,EAAEoC,EAAE;YAAEnC,KAAK,EAAEmC;UAAG,CAAC,CAAC,CAAC,CAAC;UACtH/B,eAAe,EAAE,CAAC;YAAEL,KAAK,EAAE,KAAK;YAAEC,KAAK,EAAE;UAAK,CAAC,EAAE,GAAG,CAACsB,OAAO,CAAClB,eAAe,IAAI,EAAE,EAAE4B,GAAG,CAAEG,EAAE,KAAM;YAAEpC,KAAK,EAAEoC,EAAE;YAAEnC,KAAK,EAAEmC;UAAG,CAAC,CAAC,CAAC;QAC7H,CAAC;QAEDtC,gBAAgB,CAACkC,kBAAkB,CAAC;MACtC;IACF,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD;EACF,CAAC,EACD,CAAC1E,WAAW,CACd,CAAC;EAED,MAAM4E,gBAAgB,GAAGtI,WAAW,CAClC,OAAOuI,SAAS,GAAG,CAAC,EAAEC,QAAQ,GAAG,EAAE,EAAEC,UAAU,GAAG,EAAE,EAAEC,MAAM,GAAG,IAAI,EAAEC,aAAa,GAAG,IAAI,KAAK;IAC5F,IAAIxD,UAAU,CAACyD,OAAO,EAAE;MACtB;IACF;IAEA,IAAI;MACFlG,UAAU,CAAC,IAAI,CAAC;MAChByC,UAAU,CAACyD,OAAO,GAAG,IAAI;;MAEzB;MACA,MAAMC,aAAa,GAAG;QACpBC,IAAI,EAAEP,SAAS,GAAG,CAAC;QACnBC,QAAQ,EAAEA,QAAQ;QAClBO,WAAW,EAAEN,UAAU;QAAE;QACzB1E,IAAI,EAAEF,YAAY;QAClBK,KAAK,EAAEF,aAAa;QACpBK,MAAM,EAAEF,kBAAkB;QAC1BK,WAAW,EAAEF,mBAAmB;QAChCK,cAAc,EAAEF,sBAAsB;QACtCG,mBAAmB,EAAEA,mBAAmB;QACxC8D,MAAM,EAAEA,MAAM,IAAIpF,SAAS;QAC3BqF,aAAa,EAAEA,aAAa,KAAKnF,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM;MACnE,CAAC;;MAED;MACA,MAAMwF,MAAM,GAAG,MAAMlI,wBAAwB,CAACmI,wBAAwB,CAACJ,aAAa,CAAC;;MAErF;MACA,IAAIG,MAAM,CAAC5B,YAAY,KAAK,CAAC,IAAI4B,MAAM,CAAC5B,YAAY,KAAK,SAAS,EAAE;QAAA,IAAA8B,YAAA;QAClE,MAAMC,KAAK,GAAG,EAAAD,YAAA,GAAAF,MAAM,CAAC3B,IAAI,cAAA6B,YAAA,uBAAXA,YAAA,CAAaE,KAAK,KAAI,EAAE;;QAEtC;QACA,IAAID,KAAK,CAACE,MAAM,GAAG,CAAC,EAAE;UAAA,IAAAC,aAAA;UACpBjB,OAAO,CAACkB,GAAG,CAAC,8BAA8BJ,KAAK,CAACE,MAAM,WAAW,CAAC;UAClE,MAAMG,YAAY,GAAGL,KAAK,CAACnB,GAAG,CAAC,MAAOyB,IAAI,IAAK;YAC7C,IAAIA,IAAI,CAACC,EAAE,EAAE;cACX,IAAI;gBACF,MAAMC,IAAI,GAAG,MAAMnI,WAAW,CAACoI,sBAAsB,CAACH,IAAI,CAACC,EAAE,CAAC;gBAC9DrB,OAAO,CAACkB,GAAG,CAAC,cAAcI,IAAI,aAAaF,IAAI,CAACC,EAAE,EAAE,CAAC;gBACrD,OAAO;kBAAEG,MAAM,EAAEJ,IAAI,CAACC,EAAE;kBAAEC;gBAAK,CAAC;cAClC,CAAC,CAAC,OAAOvB,KAAK,EAAE;gBACdC,OAAO,CAACD,KAAK,CAAC,uCAAuCqB,IAAI,CAACC,EAAE,GAAG,EAAEtB,KAAK,CAAC;gBACvE,OAAO;kBAAEyB,MAAM,EAAEJ,IAAI,CAACC,EAAE;kBAAEC,IAAI,EAAE;gBAAK,CAAC;cACxC;YACF;YACA,OAAO;cAAEE,MAAM,EAAE,IAAI;cAAEF,IAAI,EAAE;YAAK,CAAC;UACrC,CAAC,CAAC;UAEF,MAAMG,WAAW,GAAG,MAAMC,OAAO,CAACC,GAAG,CAACR,YAAY,CAAC;UACnD,MAAMS,QAAQ,GAAG,CAAC,CAAC;UACnBH,WAAW,CAACI,OAAO,CAAElB,MAAM,IAAK;YAC9B,IAAIA,MAAM,CAACa,MAAM,EAAE;cACjBI,QAAQ,CAACjB,MAAM,CAACa,MAAM,CAAC,GAAGb,MAAM,CAACW,IAAI;YACvC;UACF,CAAC,CAAC;UACFtB,OAAO,CAACkB,GAAG,CAAC,6BAA6B,EAAEU,QAAQ,CAAC;UACpDtE,gBAAgB,CAACsE,QAAQ,CAAC;UAC1BrH,eAAe,CAACuG,KAAK,CAAC;UACtBrG,eAAe,CAAC,EAAAwG,aAAA,GAAAN,MAAM,CAAC3B,IAAI,cAAAiC,aAAA,uBAAXA,aAAA,CAAaa,UAAU,KAAI,CAAC,CAAC;QAC/C,CAAC,MAAM;UACLvH,eAAe,CAAC,EAAE,CAAC;UACnBE,eAAe,CAAC,CAAC,CAAC;UAClB6C,gBAAgB,CAAC,CAAC,CAAC,CAAC;QACtB;MACF,CAAC,MAAM;QACL0C,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEY,MAAM,CAACoB,OAAO,CAAC;QAC7DrJ,cAAc,CAACsJ,UAAU,CAAC,gCAAgCrB,MAAM,CAACoB,OAAO,IAAI,eAAe,EAAE,CAAC;QAC9FxH,eAAe,CAAC,EAAE,CAAC;QACnBE,eAAe,CAAC,CAAC,CAAC;QAClB6C,gBAAgB,CAAC,CAAC,CAAC,CAAC;MACtB;IACF,CAAC,CAAC,OAAOyC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDrH,cAAc,CAACsJ,UAAU,CAAC,6BAA6B,CAAC;MACxDzH,eAAe,CAAC,EAAE,CAAC;MACnBE,eAAe,CAAC,CAAC,CAAC;MAClB6C,gBAAgB,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,SAAS;MACRjD,UAAU,CAAC,KAAK,CAAC;MACjByC,UAAU,CAACyD,OAAO,GAAG,KAAK;IAC5B;EACF,CAAC,EACD,CAAC/E,YAAY,EAAEG,aAAa,EAAEG,kBAAkB,EAAEG,mBAAmB,EAAEG,sBAAsB,EAAEG,mBAAmB,EAAEtB,SAAS,EAAEE,SAAS,CAC1I,CAAC;;EAED;EACA1D,SAAS,CAAC,MAAM;IACdkH,iBAAiB,CAAC,CAAC,CAAC,CAAC;IACrBsB,gBAAgB,CAAC,CAAC,EAAErF,IAAI,EAAEE,YAAY,CAAC;IACvC;EACF,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER;EACA,MAAMmH,YAAY,GAAGA,CAAA,KAAM;IACzBtH,QAAQ,CAAC,CAAC,CAAC;IACXsF,gBAAgB,CAAC,CAAC,EAAErF,IAAI,EAAEE,YAAY,CAAC;EACzC,CAAC;;EAED;EACArD,SAAS,CAAC,MAAM;IACd,IAAIqF,UAAU,CAACyD,OAAO,EAAE,OAAO,CAAC;IAChCN,gBAAgB,CAAC,CAAC,EAAErF,IAAI,EAAEE,YAAY,CAAC;IACvC;EACF,CAAC,EAAE,CAACU,YAAY,EAAEG,aAAa,EAAEG,kBAAkB,EAAEG,mBAAmB,EAAEG,sBAAsB,EAAEG,mBAAmB,CAAC,CAAC;;EAEvH;EACA9E,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX,IAAIuD,gBAAgB,CAACuF,OAAO,EAAE;QAC5B2B,YAAY,CAAClH,gBAAgB,CAACuF,OAAO,CAAC;MACxC;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM4B,YAAY,GAAIC,KAAK,IAAK;IAC9B,MAAMC,QAAQ,GAAGD,KAAK,CAAC1H,KAAK;IAC5B,MAAM4H,OAAO,GAAGF,KAAK,CAACxH,IAAI;IAC1B,MAAM2H,YAAY,GAAGC,IAAI,CAACC,KAAK,CAACJ,QAAQ,GAAGC,OAAO,CAAC;IAEnD3H,QAAQ,CAAC0H,QAAQ,CAAC;IAClBxH,OAAO,CAACyH,OAAO,CAAC;IAChBrC,gBAAgB,CAACsC,YAAY,EAAED,OAAO,EAAExH,YAAY,CAAC;EACvD,CAAC;EAED,MAAM4H,oBAAoB,GAAI/E,KAAK,IAAK;IACtC5C,eAAe,CAAC4C,KAAK,CAAC;IACtBhD,QAAQ,CAAC,CAAC,CAAC;;IAEX;IACA,IAAIK,gBAAgB,CAACuF,OAAO,EAAE;MAC5B2B,YAAY,CAAClH,gBAAgB,CAACuF,OAAO,CAAC;IACxC;;IAEA;IACA,IAAI5C,KAAK,CAACgF,IAAI,CAAC,CAAC,CAAC3B,MAAM,IAAI,CAAC,IAAIrD,KAAK,CAACgF,IAAI,CAAC,CAAC,CAAC3B,MAAM,KAAK,CAAC,EAAE;MACzD;MACAhG,gBAAgB,CAACuF,OAAO,GAAGqC,UAAU,CAAC,MAAM;QAC1C3C,gBAAgB,CAAC,CAAC,EAAErF,IAAI,EAAE+C,KAAK,CAAC;MAClC,CAAC,EAAE,GAAG,CAAC;IACT;EACF,CAAC;EAED,MAAMkF,aAAa,GAAG,MAAOlF,KAAK,IAAK;IACrC/B,gBAAgB,CAAC+B,KAAK,CAAC;IACvB5B,qBAAqB,CAAC,IAAI,CAAC,CAAC,CAAC;IAC7BpB,QAAQ,CAAC,CAAC,CAAC;;IAEX;IACA,MAAMgE,iBAAiB,CAAChB,KAAK,CAAC;;IAE9B;IACAsC,gBAAgB,CAAC,CAAC,EAAErF,IAAI,EAAEE,YAAY,CAAC;EACzC,CAAC;EAED,MAAMgI,MAAM,GAAIV,KAAK,IAAK;IACxB,MAAM;MAAEnH,SAAS,EAAE8H,YAAY;MAAE5H,SAAS,EAAE6H;IAAa,CAAC,GAAGZ,KAAK;IAClElH,YAAY,CAAC6H,YAAY,CAAC;IAC1B3H,YAAY,CAAC4H,YAAY,CAAC;IAC1BrI,QAAQ,CAAC,CAAC,CAAC;;IAEX;IACA,MAAMsI,YAAY,GAAGC,qBAAqB,CAACH,YAAY,CAAC;IACxD,MAAMI,gBAAgB,GAAGH,YAAY,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM;IAE5D/C,gBAAgB,CAAC,CAAC,EAAErF,IAAI,EAAEE,YAAY,EAAEmI,YAAY,EAAEE,gBAAgB,CAAC;EACzE,CAAC;EAED,MAAMD,qBAAqB,GAAIE,eAAe,IAAK;IACjD,MAAMC,YAAY,GAAG;MACnB3C,WAAW,EAAE,aAAa;MAC1BhF,IAAI,EAAE,MAAM;MACZ4H,UAAU,EAAE,QAAQ;MACpBC,mBAAmB,EAAE,QAAQ;MAC7BC,iBAAiB,EAAE,QAAQ;MAC3BC,UAAU,EAAE,YAAY;MACxBC,SAAS,EAAE;IACb,CAAC;IACD,OAAOL,YAAY,CAACD,eAAe,CAAC,IAAI,YAAY;EACtD,CAAC;EAED,MAAMO,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/BlI,eAAe,CAACJ,WAAW,CAAC,CAAC,CAAC;IAC9BO,gBAAgB,CAAC,IAAI,CAAC;IACtBG,qBAAqB,CAAC,IAAI,CAAC;IAC3BG,sBAAsB,CAAC,IAAI,CAAC;IAC5BG,yBAAyB,CAAC,IAAI,CAAC;IAC/BG,sBAAsB,CAAC3C,YAAY,CAAC,CAAC,CAAC;IACtCkB,eAAe,CAAC,EAAE,CAAC;IACnBJ,QAAQ,CAAC,CAAC,CAAC;;IAEX;IACA,MAAMgE,iBAAiB,CAAC,IAAI,CAAC;;IAE7B;IACA;IACAiE,UAAU,CAAC,MAAM;MACf3C,gBAAgB,CAAC,CAAC,EAAErF,IAAI,EAAE,EAAE,EAAE,YAAY,EAAE,MAAM,CAAC;IACrD,CAAC,EAAE,CAAC,CAAC;EACP,CAAC;EAED,MAAMgJ,YAAY,GAAGA,CAAA,KAAM;IACzB;IACA1I,YAAY,CAAC,YAAY,CAAC;IAC1BE,YAAY,CAAC,CAAC,CAAC,CAAC;IAChBT,QAAQ,CAAC,CAAC,CAAC;IACXsF,gBAAgB,CAAC,CAAC,EAAErF,IAAI,EAAEE,YAAY,EAAE,YAAY,EAAE,MAAM,CAAC;EAC/D,CAAC;EAED,MAAM+I,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMrD,aAAa,GAAG;QACpBE,WAAW,EAAE5F,YAAY;QAAE;QAC3BY,IAAI,EAAEF,YAAY;QAClBQ,MAAM,EAAEF,kBAAkB;QAC1BK,WAAW,EAAEF,mBAAmB;QAChCK,cAAc,EAAEF,sBAAsB;QACtCG,mBAAmB,EAAEA,mBAAmB;QACxCuH,aAAa,EAAE,IAAI;QACnBrD,IAAI,EAAE,CAAC;QACPN,QAAQ,EAAE,KAAK,CAAE;MACnB,CAAC;MAED,MAAM4D,IAAI,GAAG,MAAMtL,wBAAwB,CAACqL,aAAa,CAACtD,aAAa,CAAC;MACxE/H,wBAAwB,CAACuL,YAAY,CAACD,IAAI,EAAE,sBAAsB,IAAIzI,IAAI,CAAC,CAAC,CAAC2I,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;MAChHxL,cAAc,CAACyL,YAAY,CAAC,+BAA+B,CAAC;IAC9D,CAAC,CAAC,OAAOpE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrCrH,cAAc,CAACsJ,UAAU,CAAC,eAAe,CAAC;IAC5C;EACF,CAAC;;EAED;EACA,MAAMoC,kBAAkB,GAAIC,OAAO,IAAK;IACtC,MAAMrI,MAAM,GAAGqI,OAAO,CAACf,UAAU,IAAI,aAAa;IAElD,IAAItH,MAAM,KAAK,aAAa,EAAE;MAC5B,oBACE3C,OAAA;QAAKiL,SAAS,EAAC,+BAA+B;QAAAC,QAAA,gBAC5ClL,OAAA;UAAGiL,SAAS,EAAC,4BAA4B;UAACE,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3ExL,OAAA;UAAMmL,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU,CAAE;UAAAF,QAAA,EAAC;QAAW;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CAAC;IAEV;IAEA,oBAAOxL,OAAA,CAACf,GAAG;MAACqF,KAAK,EAAE3B;IAAO;MAAA0I,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC/B,CAAC;;EAED;EACA,MAAMC,yBAAyB,GAAIT,OAAO,IAAK;IAC7C,MAAMrI,MAAM,GAAGqI,OAAO,CAACd,mBAAmB,IAAI,aAAa;IAE3D,IAAIvH,MAAM,KAAK,aAAa,EAAE;MAC5B,oBACE3C,OAAA;QAAKiL,SAAS,EAAC,+BAA+B;QAAAC,QAAA,gBAC5ClL,OAAA;UAAGiL,SAAS,EAAC,4BAA4B;UAACE,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3ExL,OAAA;UAAMmL,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU,CAAE;UAAAF,QAAA,EAAC;QAAW;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CAAC;IAEV;IAEA,oBAAOxL,OAAA,CAACf,GAAG;MAACqF,KAAK,EAAE3B;IAAO;MAAA0I,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC/B,CAAC;;EAED;EACA,MAAME,yBAAyB,GAAIV,OAAO,IAAK;IAC7C,MAAMrI,MAAM,GAAGqI,OAAO,CAACb,iBAAiB,IAAI,aAAa;IAEzD,IAAIxH,MAAM,KAAK,aAAa,EAAE;MAC5B,oBACE3C,OAAA;QAAKiL,SAAS,EAAC,+BAA+B;QAAAC,QAAA,gBAC5ClL,OAAA;UAAGiL,SAAS,EAAC,4BAA4B;UAACE,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3ExL,OAAA;UAAMmL,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU,CAAE;UAAAF,QAAA,EAAC;QAAW;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CAAC;IAEV;IAEA,oBAAOxL,OAAA,CAACf,GAAG;MAACqF,KAAK,EAAE3B;IAAO;MAAA0I,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC/B,CAAC;;EAED;EACA,MAAMG,qBAAqB,GAAIX,OAAO,IAAK;IACzC,IAAI,CAACA,OAAO,CAACY,SAAS,IAAI,CAACZ,OAAO,CAACX,SAAS,EAAE,OAAO,GAAG;IAExD,oBACErK,OAAA;MAAKiL,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzBlL,OAAA;QAAKiL,SAAS,EAAC,YAAY;QAAAC,QAAA,EAAEF,OAAO,CAACY,SAAS,IAAI;MAAG;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,EAC3DR,OAAO,CAACX,SAAS,iBAAIrK,OAAA;QAAKiL,SAAS,EAAC,YAAY;QAAAC,QAAA,EAAE1L,cAAc,CAACwL,OAAO,CAACX,SAAS;MAAC;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxF,CAAC;EAEV,CAAC;EAED,MAAMK,sBAAsB,GAAIb,OAAO,IAAK;IAC1C,IAAI,CAACA,OAAO,CAACc,UAAU,IAAI,CAACd,OAAO,CAACZ,UAAU,EAAE,OAAO,GAAG;IAE1D,oBACEpK,OAAA;MAAKiL,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzBlL,OAAA;QAAKiL,SAAS,EAAC,YAAY;QAAAC,QAAA,EAAEF,OAAO,CAACc,UAAU,IAAI;MAAK;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,EAC9DR,OAAO,CAACZ,UAAU,iBAAIpK,OAAA;QAAKiL,SAAS,EAAC,YAAY;QAAAC,QAAA,EAAE1L,cAAc,CAACwL,OAAO,CAACZ,UAAU;MAAC;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1F,CAAC;EAEV,CAAC;EAED,MAAMO,0BAA0B,GAAIf,OAAO,IAAK;IAC9C,IAAI,CAACA,OAAO,CAACgB,wBAAwB,IAAI,CAAChB,OAAO,CAACiB,oBAAoB,EAAE,OAAO,GAAG;IAElF,oBACEjM,OAAA;MAAKiL,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzBlL,OAAA;QAAKiL,SAAS,EAAC,YAAY;QAAAC,QAAA,EAAEF,OAAO,CAACgB,wBAAwB,IAAI;MAAK;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,EAC5ER,OAAO,CAACiB,oBAAoB,iBAAIjM,OAAA;QAAKiL,SAAS,EAAC,YAAY;QAAAC,QAAA,EAAE1L,cAAc,CAACwL,OAAO,CAACiB,oBAAoB;MAAC;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9G,CAAC;EAEV,CAAC;;EAED;EACA,MAAMU,qBAAqB,GAAG,MAAOC,MAAM,IAAK;IAC9C,IAAI;MACF9G,qBAAqB,CAAC,IAAI,CAAC;MAC3BJ,oBAAoB,CAAC,IAAI,CAAC;MAC1BE,sBAAsB,CAAC,IAAI,CAAC;MAE5B,MAAMK,QAAQ,GAAG,MAAMpG,wBAAwB,CAACgN,wBAAwB,CAACD,MAAM,CAAC;MAEhF,IAAI,CAAC3G,QAAQ,CAACE,YAAY,KAAK,CAAC,IAAIF,QAAQ,CAACE,YAAY,KAAK,SAAS,KAAKF,QAAQ,CAACG,IAAI,EAAE;QACzFR,sBAAsB,CAACK,QAAQ,CAACG,IAAI,CAAC;QACrC,IAAItF,kBAAkB,EAAE;UACtBA,kBAAkB,CAACmF,QAAQ,CAACG,IAAI,CAAC;QACnC;MACF,CAAC,MAAM;QACLtG,cAAc,CAACsJ,UAAU,CAAC,qCAAqC,CAAC;QAChE1D,oBAAoB,CAAC,KAAK,CAAC;MAC7B;IACF,CAAC,CAAC,OAAOyB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnDrH,cAAc,CAACsJ,UAAU,CAAC,qCAAqC,CAAC;MAChE1D,oBAAoB,CAAC,KAAK,CAAC;IAC7B,CAAC,SAAS;MACRI,qBAAqB,CAAC,KAAK,CAAC;IAC9B;EACF,CAAC;;EAED;EACA,MAAMgH,wBAAwB,GAAGA,CAAA,KAAM;IACrCpH,oBAAoB,CAAC,KAAK,CAAC;IAC3BE,sBAAsB,CAAC,IAAI,CAAC;IAC5BE,qBAAqB,CAAC,KAAK,CAAC;EAC9B,CAAC;;EAED;EACA,MAAMiH,cAAc,GAAInE,MAAM,IAAK;IACjCxH,QAAQ,CAAC,wBAAwBwH,MAAM,EAAE,CAAC;EAC5C,CAAC;;EAED;EACA,MAAMoE,uBAAuB,GAAIvB,OAAO,IAAK;IAC3CnH,0BAA0B,CAACmH,OAAO,CAAC;IACnCrH,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;;EAED;EACA,MAAM6I,qBAAqB,GAAG,MAAOC,QAAQ,IAAK;IAChD,IAAI,CAAC7I,uBAAuB,EAAE;IAE9BG,cAAc,CAAC,IAAI,CAAC;IACpB,IAAI;MACF,MAAMjE,WAAW,CAAC4M,iBAAiB,CAAC9I,uBAAuB,CAACoE,EAAE,EAAEyE,QAAQ,CAAC;MACzEpN,cAAc,CAACyL,YAAY,CAAC,wCAAwC,CAAC;;MAErE;MACAlE,gBAAgB,CAAC,CAAC;;MAElB;MACAjD,qBAAqB,CAAC,KAAK,CAAC;MAC5BE,0BAA0B,CAAC,IAAI,CAAC;IAClC,CAAC,CAAC,OAAO6C,KAAK,EAAE;MACdrH,cAAc,CAACsJ,UAAU,CAACjC,KAAK,CAACgC,OAAO,IAAI,qCAAqC,CAAC;IACnF,CAAC,SAAS;MACR3E,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAM4I,wBAAwB,GAAGA,CAAA,KAAM;IACrC,IAAI,CAAC7I,WAAW,EAAE;MAChBH,qBAAqB,CAAC,KAAK,CAAC;MAC5BE,0BAA0B,CAAC,IAAI,CAAC;IAClC;EACF,CAAC;;EAED;EACA,MAAM+I,oBAAoB,GAAGtO,WAAW,CACrC0M,OAAO,IAAK;IACX,IAAI,CAACxK,YAAY,EAAE,OAAO,KAAK,CAAC,CAAC;;IAEjC;IACA,MAAMqM,QAAQ,GAAG7I,aAAa,CAACgH,OAAO,CAAChD,EAAE,CAAC;;IAE1C;IACArB,OAAO,CAACkB,GAAG,CAAC,oCAAoCmD,OAAO,CAAChD,EAAE,GAAG,EAAE;MAC7D6E,QAAQ;MACRC,YAAY,EAAEvN,YAAY,CAACwN,QAAQ;MACnC/I,aAAa;MACbkG,mBAAmB,EAAEc,OAAO,CAACd,mBAAmB;MAChDC,iBAAiB,EAAEa,OAAO,CAACb;IAC7B,CAAC,CAAC;;IAEF;IACA,IAAI0C,QAAQ,KAAKG,SAAS,IAAIH,QAAQ,KAAK,IAAI,EAAE;MAC/ClG,OAAO,CAACkB,GAAG,CAAC,wCAAwCmD,OAAO,CAAChD,EAAE,EAAE,CAAC;MACjE,OAAO,KAAK,CAAC,CAAC;IAChB;IAEA,IAAI6E,QAAQ,KAAKtN,YAAY,CAACwN,QAAQ,EAAE;MACtCpG,OAAO,CAACkB,GAAG,CAAC,eAAegF,QAAQ,qBAAqBtN,YAAY,CAACwN,QAAQ,cAAc/B,OAAO,CAAChD,EAAE,EAAE,CAAC;MACxG,OAAO,KAAK;IACd;;IAEA;IACA;IACA,MAAMiF,WAAW,GAAGjC,OAAO,CAACd,mBAAmB,KAAK,cAAc,IAAIc,OAAO,CAACb,iBAAiB,KAAK,cAAc;IAElHxD,OAAO,CAACkB,GAAG,CAAC,2BAA2BmD,OAAO,CAAChD,EAAE,GAAG,EAAE;MACpDkC,mBAAmB,EAAEc,OAAO,CAACd,mBAAmB;MAChDC,iBAAiB,EAAEa,OAAO,CAACb,iBAAiB;MAC5C+C,cAAc,EAAE,cAAc;MAC9BD;IACF,CAAC,CAAC;IAEF,OAAOA,WAAW;EACpB,CAAC,EACD,CAACzM,YAAY,EAAEwD,aAAa,CAC9B,CAAC,CAAC,CAAC;;EAEH;EACA,MAAMmJ,mBAAmB,GAAG7O,WAAW,CACpC0M,OAAO,IAAK;IACX,MAAMoC,kBAAkB,GAAGR,oBAAoB,CAAC5B,OAAO,CAAC;;IAExD;IACA,MAAMqC,iBAAiB,GAAGrC,OAAO,CAACqC,iBAAiB,KAAK,IAAI;IAC5D,MAAMC,eAAe,GAAGD,iBAAiB,GAAG,oDAAoD,GAAG,WAAW;IAC9G,MAAME,QAAQ,GAAG,iBAAiBvC,OAAO,CAAChD,EAAE,EAAE;IAE9C,oBACEhI,OAAA;MAAKiL,SAAS,EAAC,+BAA+B;MAAAC,QAAA,GAC3CmC,iBAAiB,gBAChBrN,OAAA,CAAAE,SAAA;QAAAgL,QAAA,gBACElL,OAAA;UAAKgI,EAAE,EAAEuF,QAAS;UAACtC,SAAS,EAAC,cAAc;UAAAC,QAAA,eACzClL,OAAA,CAACpB,MAAM;YAAC4O,IAAI,EAAC,WAAW;YAACvC,SAAS,EAAC,8CAA8C;YAACwC,QAAQ,EAAE,IAAK;YAACC,OAAO,EAAEA,CAAA,KAAM,CAAC;UAAE;YAAArC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpH,CAAC,eACNxL,OAAA,CAACd,OAAO;UAACyO,MAAM,EAAE,IAAIJ,QAAQ,EAAG;UAACK,OAAO,EAAEN,eAAgB;UAACO,QAAQ,EAAC;QAAK;UAAAxC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA,eAC5E,CAAC,gBAEHxL,OAAA,CAACpB,MAAM;QAAC4O,IAAI,EAAC,WAAW;QAACvC,SAAS,EAAC,2BAA2B;QAAC6C,OAAO,EAAER,eAAgB;QAACI,OAAO,EAAEA,CAAA,KAAMpB,cAAc,CAACtB,OAAO,CAAChD,EAAE;MAAE;QAAAqD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CACtI,EAGA4B,kBAAkB,iBACjBpN,OAAA,CAACpB,MAAM;QACL4O,IAAI,EAAC,YAAY;QACjBvC,SAAS,EAAC,4CAA4C;QACtD6C,OAAO,EAAC,sBAAsB;QAC9BJ,OAAO,EAAEA,CAAA,KAAMnB,uBAAuB,CAACvB,OAAO;MAAE;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CACF,eAEDxL,OAAA,CAACpB,MAAM;QAAC4O,IAAI,EAAC,kBAAkB;QAACvC,SAAS,EAAC,2BAA2B;QAAC6C,OAAO,EAAC,MAAM;QAACJ,OAAO,EAAEA,CAAA,KAAMxB,qBAAqB,CAAClB,OAAO,CAAChD,EAAE;MAAE;QAAAqD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEtI,CAAC;EAEV,CAAC,EACD,CAACxH,aAAa,EAAExD,YAAY,CAC9B,CAAC,CAAC,CAAC;;EAEH,MAAMuN,MAAM,gBACV/N,OAAA;IAAKiL,SAAS,EAAC,6BAA6B;IAAAC,QAAA,eAC1ClL,OAAA;MAAKiL,SAAS,EAAC,SAAS;MAAAC,QAAA,gBACtBlL,OAAA;QAAKiL,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BlL,OAAA;UAAOiL,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAY;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACpDxL,OAAA;UAAMiL,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACtDlL,OAAA;YAAGiL,SAAS,EAAC;UAAc;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9BxL,OAAA,CAACnB,SAAS;YACRmP,IAAI,EAAC,QAAQ;YACb1J,KAAK,EAAE7C,YAAa;YACpBwM,QAAQ,EAAGC,CAAC,IAAK7E,oBAAoB,CAAC6E,CAAC,CAACP,MAAM,CAACrJ,KAAK,CAAE;YACtD6J,WAAW,EAAC,qBAAqB;YACjClD,SAAS,EAAC,cAAc;YACxB6C,OAAO,EAAC,sCAAsC;YAC9CM,cAAc,EAAE;cAAEP,QAAQ,EAAE;YAAM;UAAE;YAAAxC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNxL,OAAA;QAAKiL,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BlL,OAAA;UAAKiL,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BlL,OAAA;YAAOiL,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAI;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC5CxL,OAAA,CAAClB,QAAQ;YACPwF,KAAK,EAAEnC,YAAa;YACpBkM,OAAO,EAAE1J,WAAY;YACrBsJ,QAAQ,EAAGC,CAAC,IAAK9L,eAAe,CAAC8L,CAAC,CAAC5J,KAAK,CAAE;YAC1CgK,WAAW,EAAC,OAAO;YACnBC,WAAW,EAAC,OAAO;YACnBJ,WAAW,EAAC,WAAW;YACvBlD,SAAS,EAAC,iBAAiB;YAC3B6C,OAAO,EAAC;UAAM;YAAAzC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNxL,OAAA;UAAKiL,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BlL,OAAA;YAAOiL,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAM;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC9CxL,OAAA,CAAClB,QAAQ;YACPwF,KAAK,EAAEhC,aAAc;YACrB+L,OAAO,EAAEzJ,YAAa;YACtBqJ,QAAQ,EAAGC,CAAC,IAAK1E,aAAa,CAAC0E,CAAC,CAAC5J,KAAK,CAAE;YACxCgK,WAAW,EAAC,OAAO;YACnBC,WAAW,EAAC,OAAO;YACnBJ,WAAW,EAAC,YAAY;YACxBlD,SAAS,EAAC,iBAAiB;YAC3B6C,OAAO,EAAC;UAAQ;YAAAzC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNxL,OAAA;UAAKiL,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BlL,OAAA;YAAOiL,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAW;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACnDxL,OAAA,CAAClB,QAAQ;YACPwF,KAAK,EAAE7B,kBAAmB;YAC1B4L,OAAO,EAAExJ,iBAAkB;YAC3BoJ,QAAQ,EAAGC,CAAC,IAAKxL,qBAAqB,CAACwL,CAAC,CAAC5J,KAAK,CAAE;YAChDgK,WAAW,EAAC,OAAO;YACnBC,WAAW,EAAC,OAAO;YACnBJ,WAAW,EAAC,aAAa;YACzBlD,SAAS,EAAC,iBAAiB;YAC3B6C,OAAO,EAAC;UAAa;YAAAzC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNxL,OAAA;UAAKiL,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BlL,OAAA;YAAOiL,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAY;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACpDxL,OAAA,CAAClB,QAAQ;YACPwF,KAAK,EAAE1B,mBAAoB;YAC3ByL,OAAO,EAAEvJ,kBAAmB;YAC5BmJ,QAAQ,EAAGC,CAAC,IAAKrL,sBAAsB,CAACqL,CAAC,CAAC5J,KAAK,CAAE;YACjDgK,WAAW,EAAC,OAAO;YACnBC,WAAW,EAAC,OAAO;YACnBJ,WAAW,EAAC,cAAc;YAC1BlD,SAAS,EAAC,iBAAiB;YAC3B6C,OAAO,EAAC;UAAc;YAAAzC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNxL,OAAA;UAAKiL,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BlL,OAAA;YAAOiL,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAgB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACxDxL,OAAA,CAAClB,QAAQ;YACPwF,KAAK,EAAEvB,sBAAuB;YAC9BsL,OAAO,EAAEtJ,qBAAsB;YAC/BkJ,QAAQ,EAAGC,CAAC,IAAKlL,yBAAyB,CAACkL,CAAC,CAAC5J,KAAK,CAAE;YACpDgK,WAAW,EAAC,OAAO;YACnBC,WAAW,EAAC,OAAO;YACnBJ,WAAW,EAAC,kBAAkB;YAC9BlD,SAAS,EAAC,iBAAiB;YAC3B6C,OAAO,EAAC;UAAkB;YAAAzC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAYLpI,SAAS,IAAI,CAAC5C,YAAY,iBACzBR,OAAA;QAAKiL,SAAS,EAAC,oBAAoB;QAAAC,QAAA,eACjClL,OAAA;UAAKiL,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1ClL,OAAA,CAACjB,QAAQ;YACPyP,OAAO,EAAC,qBAAqB;YAC7BC,OAAO,EAAEvL,mBAAoB;YAC7B+K,QAAQ,EAAGC,CAAC,IAAK/K,sBAAsB,CAAC+K,CAAC,CAACO,OAAO,CAAE;YACnDxD,SAAS,EAAC,iBAAiB;YAC3BwC,QAAQ,EAAEjN;UAAa;YAAA6K,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC,eACFxL,OAAA;YAAO0O,OAAO,EAAC,qBAAqB;YAACzD,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAC5D1K,YAAY,GAAG,qBAAqB,GAAG;UAAuB;YAAA6K,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eACDxL,OAAA;QAAKiL,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BlL,OAAA,CAACpB,MAAM;UAACyF,KAAK,EAAC,eAAe;UAAC4G,SAAS,EAAC,iDAAiD;UAACyC,OAAO,EAAEpD;QAAa;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnHxL,OAAA,CAACpB,MAAM;UAACyF,KAAK,EAAC,eAAe;UAAC4G,SAAS,EAAC,gDAAgD;UAACyC,OAAO,EAAEnD;QAAa;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClHxL,OAAA,CAACpB,MAAM;UAAC4O,IAAI,EAAC,gBAAgB;UAACnJ,KAAK,EAAC,iBAAiB;UAAC4G,SAAS,EAAC,0BAA0B;UAACyC,OAAO,EAAElD;QAAa;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,oBACExL,OAAA;IAAAkL,QAAA,gBACElL,OAAA;MAAKiL,SAAS,EAAC,sBAAsB;MAAAC,QAAA,eACnClL,OAAA,CAACvB,IAAI;QAAAyM,QAAA,gBACHlL,OAAA,CAAChB,KAAK;UAAC2P,GAAG,EAAEnL;QAAM;UAAA6H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrBxL,OAAA,CAACb,aAAa;UAAAkM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjBxL,OAAA,CAACtB,SAAS;UACR4F,KAAK,EAAErD,YAAa;UACpBF,OAAO,EAAEA,OAAQ;UACjBgN,MAAM,EAAEA,MAAO;UACfa,YAAY,EAAC,+BAA+B;UAC5CC,QAAQ,EAAC,QAAQ;UACjBjN,SAAS,EAAEA,SAAU;UACrBE,SAAS,EAAEA,SAAU;UACrB2H,MAAM,EAAEA,MAAO;UACfqF,SAAS;UACTC,IAAI;UACJxN,IAAI,EAAEA,IAAK;UACXF,KAAK,EAAEA,KAAM;UACbF,YAAY,EAAEA,YAAa;UAC3B6N,MAAM,EAAElG,YAAa;UACrBmG,kBAAkB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAE;UACtChE,SAAS,EAAC,uBAAuB;UAAAC,QAAA,gBAEjClL,OAAA,CAACrB,MAAM;YAACuQ,KAAK,EAAC,aAAa;YAACnB,MAAM,EAAC,cAAc;YAACoB,QAAQ;YAAChE,KAAK,EAAE;cAAEiE,QAAQ,EAAE;YAAQ;UAAE;YAAA/D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3FxL,OAAA,CAACrB,MAAM;YAACuQ,KAAK,EAAC,aAAa;YAACnB,MAAM,EAAC,cAAc;YAACoB,QAAQ;YAAChE,KAAK,EAAE;cAAEiE,QAAQ,EAAE;YAAQ;UAAE;YAAA/D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3FxL,OAAA,CAACrB,MAAM;YAACuQ,KAAK,EAAC,gBAAgB;YAACnB,MAAM,EAAC,kBAAkB;YAACoB,QAAQ;YAAChE,KAAK,EAAE;cAAEiE,QAAQ,EAAE;YAAQ;UAAE;YAAA/D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClGxL,OAAA,CAACrB,MAAM;YAACuQ,KAAK,EAAC,MAAM;YAACnB,MAAM,EAAC,MAAM;YAACoB,QAAQ;YAAChE,KAAK,EAAE;cAAEiE,QAAQ,EAAE;YAAO;UAAE;YAAA/D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3ExL,OAAA,CAACrB,MAAM;YAACuQ,KAAK,EAAC,kBAAkB;YAACnB,MAAM,EAAC,aAAa;YAACsB,IAAI,EAAEtE,kBAAmB;YAACoE,QAAQ;YAAChE,KAAK,EAAE;cAAEiE,QAAQ,EAAE;YAAQ;UAAE;YAAA/D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzHxL,OAAA,CAACrB,MAAM;YACLuQ,KAAK,EAAC,qBAAqB;YAC3BnB,MAAM,EAAC,wBAAwB;YAC/BsB,IAAI,EAAE5D,yBAA0B;YAChC0D,QAAQ;YACRhE,KAAK,EAAE;cAAEiE,QAAQ,EAAE;YAAQ;UAAE;YAAA/D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,eACFxL,OAAA,CAACrB,MAAM;YACLuQ,KAAK,EAAC,qBAAqB;YAC3BnB,MAAM,EAAC,qBAAqB;YAC5BsB,IAAI,EAAE3D,yBAA0B;YAChCyD,QAAQ;YACRhE,KAAK,EAAE;cAAEiE,QAAQ,EAAE;YAAQ;UAAE;YAAA/D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,eACFxL,OAAA,CAACrB,MAAM;YAACoP,MAAM,EAAC,SAAS;YAACsB,IAAI,EAAElC,mBAAoB;YAAChC,KAAK,EAAE;cAAEiE,QAAQ,EAAE;YAAQ;UAAE;YAAA/D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGNxL,OAAA,CAACP,wBAAwB;MACvB6P,OAAO,EAAEtK,iBAAkB;MAC3BuK,MAAM,EAAElD,wBAAyB;MACjCmD,WAAW,EAAEtK,mBAAoB;MACjCnE,OAAO,EAAEqE;IAAmB;MAAAiG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CAAC,eAGFxL,OAAA,CAACH,sBAAsB;MACrByP,OAAO,EAAE5L,kBAAmB;MAC5B6L,MAAM,EAAE5C,wBAAyB;MACjC8C,SAAS,EAAEjD,qBAAsB;MACjCzL,OAAO,EAAE+C,WAAY;MACrB4L,KAAK,EAAC,sBAAsB;MAC5BhH,OAAO,EAAE,uDAAuD,CAAA9E,uBAAuB,aAAvBA,uBAAuB,uBAAvBA,uBAAuB,CAAEyD,WAAW,KAAI,cAAc;IAAwB;MAAAgE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/I,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC/K,EAAA,CAxxBWN,uBAAuB;EAAA,QAOjBT,WAAW;AAAA;AAAAiQ,EAAA,GAPjBxP,uBAAuB;AA0xBpC,eAAeA,uBAAuB;AAAC,IAAAwP,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}