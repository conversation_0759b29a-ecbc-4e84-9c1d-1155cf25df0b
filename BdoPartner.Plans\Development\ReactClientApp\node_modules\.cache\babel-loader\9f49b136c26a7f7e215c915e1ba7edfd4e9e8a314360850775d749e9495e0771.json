{"ast": null, "code": "import { Serializer } from \"survey-core\";\nimport partnerReferenceDataUploadService from \"../../services/partnerReferenceDataUploadService\";\nimport formService from \"../../services/formService\";\nimport lookupService from \"../../services/lookupService\";\n\n/**\r\n * Register custom properties for SurveyJS runtime and designer\r\n * This utility consolidates the custom property registration logic used by both\r\n * PartnerPlanQuestionnaire.jsx and QuestionnaireDesignerCore.jsx\r\n */\n\n/**\r\n * Register custom properties for runtime (must be called before creating Survey model)\r\n * Work for survey form serialization process to recognize added custom properties in SurveyJS Form Designer.\r\n * Note: If not calling this method, even the form template json file contains the custom properties,\r\n * the survey model will not contain those properties.\r\n *\r\n * @param {Object} options - Configuration options\r\n * @param {boolean} options.isDesigner - Whether this is for the designer (includes additional properties)\r\n * @param {number} options.questionnaireYear - The year of the questionnaire (for designer mode)\r\n * @param {boolean} options.skipDuplicateCheck - Skip checking for existing properties (for designer mode)\r\n */\nexport const registerCustomPropertiesForRuntime = async (options = {}) => {\n  const {\n    isDesigner = false,\n    questionnaireYear = null,\n    skipDuplicateCheck = false\n  } = options;\n  try {\n    let columnChoices = [];\n    let groupChoices = [];\n    let leadershipRoleChoices = [];\n    let serviceLinesChoices = [];\n    let subServiceLinesChoices = [];\n\n    // For designer mode, fetch dynamic data\n    if (isDesigner) {\n      columnChoices = await partnerReferenceDataUploadService.getAvailableColumnNamesForMapping(questionnaireYear, true);\n      groupChoices = await partnerReferenceDataUploadService.getUniqueGroupNames(questionnaireYear);\n\n      // Fetch leadership roles for the questionnaire year\n      try {\n        const leadershipRoles = await formService.getUniqueLeadershipRoles(questionnaireYear);\n        // Convert Lookup objects to choice format: Key (normalized) as value, Value (original) as text\n        leadershipRoleChoices = leadershipRoles.map(lookup => ({\n          value: lookup.key,\n          text: lookup.value\n        }));\n      } catch (error) {\n        console.warn(\"Failed to fetch leadership roles:\", error);\n        leadershipRoleChoices = [];\n      }\n\n      // Fetch service lines for dropdown choices\n      try {\n        const serviceLines = await lookupService.getServiceLines(false);\n        // Convert Lookup objects to choice format: Key as value, Value as text\n        serviceLinesChoices = serviceLines.map(lookup => ({\n          value: lookup.key,\n          text: lookup.value\n        }));\n      } catch (error) {\n        console.warn(\"Failed to fetch service lines:\", error);\n        serviceLinesChoices = [];\n      }\n\n      // Fetch sub-service lines for dropdown choices\n      try {\n        const subServiceLines = await lookupService.getSubServiceLines(false);\n        // Convert Lookup objects to choice format: Key as value, Value as text\n        subServiceLinesChoices = subServiceLines.map(lookup => ({\n          value: lookup.key,\n          text: lookup.value\n        }));\n      } catch (error) {\n        console.warn(\"Failed to fetch sub-service lines:\", error);\n        subServiceLinesChoices = [];\n      }\n    }\n\n    // Register tag property for panels\n    if (skipDuplicateCheck || !Serializer.findProperty(\"panel\", \"tag\")) {\n      Serializer.addProperty(\"panel\", {\n        name: \"tag\",\n        displayName: \"Tag\",\n        category: \"customSettings\",\n        categoryDisplayName: \"Custom Settings\",\n        type: \"dropdown\",\n        choices: [{\n          value: \"\",\n          text: \"-- None --\"\n        }, {\n          value: \"PlanPanel\",\n          text: \"Planning Panel\"\n        }, {\n          value: \"MidYearPanel\",\n          text: isDesigner ? \"Mid-year Panel\" : \"Mid-Year Panel\"\n        }, {\n          value: \"YearEndPanel\",\n          text: isDesigner ? \"Year-end Panel\" : \"Year-End Panel\"\n        }],\n        ...(isDesigner && {\n          visibleIndex: 1\n        }),\n        description: \"Select a tag for the panel that contains questions for the Planning, Mid-Year, or Year-End period.\"\n      });\n    }\n\n    // Register LeadershipRoles property for panels\n    if (skipDuplicateCheck || !Serializer.findProperty(\"panel\", \"leadershipRoles\")) {\n      Serializer.addProperty(\"panel\", {\n        name: \"leadershipRoles\",\n        displayName: \"Leadership Roles\",\n        category: \"customSettings\",\n        categoryDisplayName: \"Custom Settings\",\n        type: isDesigner ? \"multiplevalues\" : \"string\",\n        ...(isDesigner && {\n          choices: leadershipRoleChoices.length ? [{\n            value: \"\",\n            text: \"-- None --\"\n          }, ...leadershipRoleChoices] : [{\n            value: \"\",\n            text: \"-- None --\"\n          }, {\n            value: \"\",\n            text: \"No leadership roles available\"\n          }],\n          visibleIndex: 2\n        }),\n        description: \"Select leadership roles that can view this panel. If empty, panel is visible to all users.\"\n      });\n    }\n\n    // Register ServiceLines property for panels\n    if (skipDuplicateCheck || !Serializer.findProperty(\"panel\", \"serviceLines\")) {\n      Serializer.addProperty(\"panel\", {\n        name: \"serviceLines\",\n        displayName: \"Service Lines\",\n        category: \"customSettings\",\n        categoryDisplayName: \"Custom Settings\",\n        type: isDesigner ? \"multiplevalues\" : \"string\",\n        ...(isDesigner && {\n          choices: serviceLinesChoices.length ? [{\n            value: \"\",\n            text: \"-- None --\"\n          }, ...serviceLinesChoices] : [{\n            value: \"\",\n            text: \"-- None --\"\n          }, {\n            value: \"\",\n            text: \"No service lines available\"\n          }],\n          visibleIndex: 3\n        }),\n        description: \"Select service lines that can view this panel. If empty, panel is visible to all users.\"\n      });\n    }\n\n    // Register SubServiceLines property for panels\n    if (skipDuplicateCheck || !Serializer.findProperty(\"panel\", \"subServiceLines\")) {\n      Serializer.addProperty(\"panel\", {\n        name: \"subServiceLines\",\n        displayName: \"Sub-Service Lines\",\n        category: \"customSettings\",\n        categoryDisplayName: \"Custom Settings\",\n        type: isDesigner ? \"multiplevalues\" : \"string\",\n        ...(isDesigner && {\n          choices: subServiceLinesChoices.length ? [{\n            value: \"\",\n            text: \"-- None --\"\n          }, ...subServiceLinesChoices] : [{\n            value: \"\",\n            text: \"-- None --\"\n          }, {\n            value: \"\",\n            text: \"No sub-service lines available\"\n          }],\n          visibleIndex: 4\n        }),\n        description: \"Select sub-service lines that can view this panel. If empty, panel is visible to all users.\"\n      });\n    }\n\n    // Register tag property for questions\n    if (skipDuplicateCheck || !Serializer.findProperty(\"question\", \"tag\")) {\n      const questionTagChoices = isDesigner ? [{\n        value: \"\",\n        text: \"-- None --\"\n      }, {\n        value: \"PlanningPartnerQuestion\",\n        text: \"Planning Partner Question\"\n      }, {\n        value: \"PlanningReviewerQuestion\",\n        text: \"Planning Reviewer Question\"\n      }, {\n        value: \"MidyearPartnerQuestion\",\n        text: \"Mid-year Partner Question\"\n      }, {\n        value: \"MidyearReviewerQuestion\",\n        text: \"Mid-Year Reviewer Question\"\n      }, {\n        value: \"YearendPartnerQuestion\",\n        text: \"Year-end Partner Question\"\n      }, {\n        value: \"YearendReviewerQuestion\",\n        text: \"Year-end Reviewer Question\"\n      }] : [{\n        value: \"\",\n        text: \"-- None --\"\n      }, {\n        value: \"PlanningPartnerQuestion\",\n        text: \"Planning Partner Question\"\n      }, {\n        value: \"MidYearPartnerQuestion\",\n        text: \"Mid-Year Partner Question\"\n      }, {\n        value: \"YearEndPartnerQuestion\",\n        text: \"Year-End Partner Question\"\n      }, {\n        value: \"PlanningReviewerQuestion\",\n        text: \"Planning Reviewer Question\"\n      }, {\n        value: \"MidYearReviewerQuestion\",\n        text: \"Mid-Year Reviewer Question\"\n      }, {\n        value: \"YearEndReviewerQuestion\",\n        text: \"Year-End Reviewer Question\"\n      }];\n      Serializer.addProperty(\"question\", {\n        name: \"tag\",\n        displayName: \"Tag\",\n        category: \"customSettings\",\n        categoryDisplayName: \"Custom Settings\",\n        type: \"dropdown\",\n        choices: questionTagChoices,\n        ...(isDesigner && {\n          visibleIndex: 0\n        }),\n        description: isDesigner ? \"Select a partner reference data column to map this question to\" : \"Select a tag for the question that indicates the period and role.\"\n      });\n    }\n\n    // Register mapFrom property for questions\n    if (skipDuplicateCheck || !Serializer.findProperty(\"question\", \"mapFrom\")) {\n      Serializer.addProperty(\"question\", {\n        name: \"mapFrom\",\n        displayName: \"Map From\",\n        category: \"customSettings\",\n        categoryDisplayName: \"Custom Settings\",\n        type: isDesigner ? \"dropdown\" : \"string\",\n        ...(isDesigner && {\n          choices: columnChoices.length ? [{\n            value: \"\",\n            text: \"-- None --\"\n          }, ...columnChoices] : [{\n            value: \"\",\n            text: \"-- None --\"\n          }]\n        }),\n        ...(isDesigner && {\n          visibleIndex: 1\n        }),\n        description: \"Select a partner reference data column to map this question to\"\n      });\n    }\n\n    // Register exportColumnName property for questions\n    if (skipDuplicateCheck || !Serializer.findProperty(\"question\", \"exportColumnName\")) {\n      Serializer.addProperty(\"question\", {\n        name: \"exportColumnName\",\n        displayName: \"Export Column Name\",\n        category: \"customSettings\",\n        categoryDisplayName: \"Custom Settings\",\n        type: \"text\",\n        ...(isDesigner && {\n          visibleIndex: 2\n        }),\n        description: \"Specify a custom column name for data export\"\n      });\n    }\n\n    // Register mapFromGroup property for questions\n    if (skipDuplicateCheck || !Serializer.findProperty(\"question\", \"mapFromGroup\")) {\n      Serializer.addProperty(\"question\", {\n        name: \"mapFromGroup\",\n        displayName: \"Partner Reference Group\",\n        category: \"customSettings\",\n        categoryDisplayName: \"Custom Settings\",\n        type: isDesigner ? \"dropdown\" : \"string\",\n        ...(isDesigner && {\n          choices: groupChoices.length ? [{\n            value: \"\",\n            text: \"-- None --\"\n          }, ...groupChoices] : [{\n            value: \"\",\n            text: \"-- None --\"\n          }, {\n            value: \"\",\n            text: \"No groups available\"\n          }],\n          visibleIndex: 3,\n          visibleIf: obj => obj && obj.getType && obj.getType() === \"dropdown\"\n        }),\n        description: \"Select a partner reference data group to map this question to\"\n      });\n    }\n\n    // Register linkedToGroupPlanning property for questions. \n    // Work for rendering planning cycle partner reference data's specified column value.\n    if (skipDuplicateCheck || !Serializer.findProperty(\"question\", \"linkedToGroupPlanning\")) {\n      const linkedToGroupPlanningConfig = {\n        name: \"linkedToGroupPlanning\",\n        displayName: \"Linked Group (Planning)\",\n        category: \"customSettings\",\n        categoryDisplayName: \"Custom Settings\",\n        type: isDesigner ? \"dropdown\" : \"string\",\n        description: isDesigner ? \"Link this text field to a dropdown with mapFromGroup property for auto-population during Planning cycle\" : \"Link this text field to a dropdown for auto-population during Planning cycle\"\n      };\n      if (isDesigner) {\n        linkedToGroupPlanningConfig.choices = function (obj) {\n          const survey = obj && obj.survey;\n          if (!survey) return [{\n            value: \"\",\n            text: \"-- None --\"\n          }];\n          const dropdownQuestions = survey.getAllQuestions().filter(q => q.getType() === \"dropdown\" && q.mapFromGroup).map(q => ({\n            value: q.name,\n            text: `${q.title || q.name} (Group: ${q.mapFromGroup})`\n          }));\n          return [{\n            value: \"\",\n            text: \"-- None --\"\n          }, ...dropdownQuestions];\n        };\n        linkedToGroupPlanningConfig.visibleIndex = 2;\n        linkedToGroupPlanningConfig.visibleIf = obj => obj && obj.getType && obj.getType() === \"text\";\n      }\n      Serializer.addProperty(\"question\", linkedToGroupPlanningConfig);\n    }\n\n    // Register linkedToGroupMidYear property for questions.\n    // Work for rendering Mid year cycle partner reference data's specified column value.    \n    if (skipDuplicateCheck || !Serializer.findProperty(\"question\", \"linkedToGroupMidYear\")) {\n      const linkedToGroupMidYearConfig = {\n        name: \"linkedToGroupMidYear\",\n        displayName: \"Linked Group (Mid Year)\",\n        category: \"customSettings\",\n        categoryDisplayName: \"Custom Settings\",\n        type: isDesigner ? \"dropdown\" : \"string\",\n        description: isDesigner ? \"Link this text field to a dropdown with mapFromGroup property for auto-population during Mid Year cycle\" : \"Link this text field to a dropdown for auto-population during Mid Year cycle\"\n      };\n      if (isDesigner) {\n        linkedToGroupMidYearConfig.choices = function (obj) {\n          const survey = obj && obj.survey;\n          if (!survey) return [{\n            value: \"\",\n            text: \"-- None --\"\n          }];\n          const dropdownQuestions = survey.getAllQuestions().filter(q => q.getType() === \"dropdown\" && q.mapFromGroup).map(q => ({\n            value: q.name,\n            text: `${q.title || q.name} (Group: ${q.mapFromGroup})`\n          }));\n          return [{\n            value: \"\",\n            text: \"-- None --\"\n          }, ...dropdownQuestions];\n        };\n        linkedToGroupMidYearConfig.visibleIndex = 3;\n        linkedToGroupMidYearConfig.visibleIf = obj => obj && obj.getType && obj.getType() === \"text\";\n      }\n      Serializer.addProperty(\"question\", linkedToGroupMidYearConfig);\n    }\n\n    // Register linkedToGroupYearEnd property for questions\n    if (skipDuplicateCheck || !Serializer.findProperty(\"question\", \"linkedToGroupYearEnd\")) {\n      const linkedToGroupYearEndConfig = {\n        name: \"linkedToGroupYearEnd\",\n        displayName: \"Linked Group (Year End)\",\n        category: \"customSettings\",\n        categoryDisplayName: \"Custom Settings\",\n        type: isDesigner ? \"dropdown\" : \"string\",\n        description: isDesigner ? \"Link this text field to a dropdown with mapFromGroup property for auto-population during Year End cycle\" : \"Link this text field to a dropdown for auto-population during Year End cycle\"\n      };\n      if (isDesigner) {\n        linkedToGroupYearEndConfig.choices = function (obj) {\n          const survey = obj && obj.survey;\n          if (!survey) return [{\n            value: \"\",\n            text: \"-- None --\"\n          }];\n          const dropdownQuestions = survey.getAllQuestions().filter(q => q.getType() === \"dropdown\" && q.mapFromGroup).map(q => ({\n            value: q.name,\n            text: `${q.title || q.name} (Group: ${q.mapFromGroup})`\n          }));\n          return [{\n            value: \"\",\n            text: \"-- None --\"\n          }, ...dropdownQuestions];\n        };\n        linkedToGroupYearEndConfig.visibleIndex = 4;\n        linkedToGroupYearEndConfig.visibleIf = obj => obj && obj.getType && obj.getType() === \"text\";\n      }\n      Serializer.addProperty(\"question\", linkedToGroupYearEndConfig);\n    }\n  } catch (error) {\n    console.error(\"Error registering custom properties:\", error);\n    throw error;\n  }\n};\n\n/**\r\n * Legacy function name for backward compatibility\r\n * @deprecated Use registerCustomPropertiesForRuntime instead\r\n */\nexport const setupCustomProperties = registerCustomPropertiesForRuntime;", "map": {"version": 3, "names": ["Serializer", "partnerReferenceDataUploadService", "formService", "lookupService", "registerCustomPropertiesForRuntime", "options", "isDesigner", "questionnaireYear", "skipDuplicate<PERSON><PERSON>ck", "columnChoices", "groupChoices", "leadershipRoleChoices", "serviceLinesChoices", "subServiceLinesChoices", "getAvailableColumnNamesForMapping", "getUniqueGroupNames", "leadershipRoles", "getUniqueLeadershipRoles", "map", "lookup", "value", "key", "text", "error", "console", "warn", "serviceLines", "getServiceLines", "subServiceLines", "getSubServiceLines", "findProperty", "addProperty", "name", "displayName", "category", "categoryDisplayName", "type", "choices", "visibleIndex", "description", "length", "questionTagChoices", "visibleIf", "obj", "getType", "linkedToGroupPlanningConfig", "survey", "dropdownQuestions", "getAllQuestions", "filter", "q", "mapFromGroup", "title", "linkedToGroupMidYearConfig", "linkedToGroupYearEndConfig", "setupCustomProperties"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/core/utils/surveyCustomPropertiesUtils.js"], "sourcesContent": ["import { Serializer } from \"survey-core\";\r\nimport partnerReferenceDataUploadService from \"../../services/partnerReferenceDataUploadService\";\r\nimport formService from \"../../services/formService\";\r\nimport lookupService from \"../../services/lookupService\";\r\n\r\n/**\r\n * Register custom properties for SurveyJS runtime and designer\r\n * This utility consolidates the custom property registration logic used by both\r\n * PartnerPlanQuestionnaire.jsx and QuestionnaireDesignerCore.jsx\r\n */\r\n\r\n/**\r\n * Register custom properties for runtime (must be called before creating Survey model)\r\n * Work for survey form serialization process to recognize added custom properties in SurveyJS Form Designer.\r\n * Note: If not calling this method, even the form template json file contains the custom properties,\r\n * the survey model will not contain those properties.\r\n *\r\n * @param {Object} options - Configuration options\r\n * @param {boolean} options.isDesigner - Whether this is for the designer (includes additional properties)\r\n * @param {number} options.questionnaireYear - The year of the questionnaire (for designer mode)\r\n * @param {boolean} options.skipDuplicateCheck - Skip checking for existing properties (for designer mode)\r\n */\r\nexport const registerCustomPropertiesForRuntime = async (options = {}) => {\r\n  const { isDesigner = false, questionnaireYear = null, skipDuplicateCheck = false } = options;\r\n\r\n  try {\r\n    let columnChoices = [];\r\n    let groupChoices = [];\r\n    let leadershipRoleChoices = [];\r\n    let serviceLinesChoices = [];\r\n    let subServiceLinesChoices = [];\r\n\r\n    // For designer mode, fetch dynamic data\r\n    if (isDesigner) {\r\n      columnChoices = await partnerReferenceDataUploadService.getAvailableColumnNamesForMapping(questionnaireYear, true);\r\n      groupChoices = await partnerReferenceDataUploadService.getUniqueGroupNames(questionnaireYear);\r\n\r\n      // Fetch leadership roles for the questionnaire year\r\n      try {\r\n        const leadershipRoles = await formService.getUniqueLeadershipRoles(questionnaireYear);\r\n        // Convert Lookup objects to choice format: Key (normalized) as value, Value (original) as text\r\n        leadershipRoleChoices = leadershipRoles.map((lookup) => ({ value: lookup.key, text: lookup.value }));\r\n      } catch (error) {\r\n        console.warn(\"Failed to fetch leadership roles:\", error);\r\n        leadershipRoleChoices = [];\r\n      }\r\n\r\n      // Fetch service lines for dropdown choices\r\n      try {\r\n        const serviceLines = await lookupService.getServiceLines(false);\r\n        // Convert Lookup objects to choice format: Key as value, Value as text\r\n        serviceLinesChoices = serviceLines.map((lookup) => ({ value: lookup.key, text: lookup.value }));\r\n      } catch (error) {\r\n        console.warn(\"Failed to fetch service lines:\", error);\r\n        serviceLinesChoices = [];\r\n      }\r\n\r\n      // Fetch sub-service lines for dropdown choices\r\n      try {\r\n        const subServiceLines = await lookupService.getSubServiceLines(false);\r\n        // Convert Lookup objects to choice format: Key as value, Value as text\r\n        subServiceLinesChoices = subServiceLines.map((lookup) => ({ value: lookup.key, text: lookup.value }));\r\n      } catch (error) {\r\n        console.warn(\"Failed to fetch sub-service lines:\", error);\r\n        subServiceLinesChoices = [];\r\n      }\r\n    }\r\n\r\n    // Register tag property for panels\r\n    if (skipDuplicateCheck || !Serializer.findProperty(\"panel\", \"tag\")) {\r\n      Serializer.addProperty(\"panel\", {\r\n        name: \"tag\",\r\n        displayName: \"Tag\",\r\n        category: \"customSettings\",\r\n        categoryDisplayName: \"Custom Settings\",\r\n        type: \"dropdown\",\r\n        choices: [\r\n          { value: \"\", text: \"-- None --\" },\r\n          { value: \"PlanPanel\", text: \"Planning Panel\" },\r\n          { value: \"MidYearPanel\", text: isDesigner ? \"Mid-year Panel\" : \"Mid-Year Panel\" },\r\n          { value: \"YearEndPanel\", text: isDesigner ? \"Year-end Panel\" : \"Year-End Panel\" },\r\n        ],\r\n        ...(isDesigner && { visibleIndex: 1 }),\r\n        description: \"Select a tag for the panel that contains questions for the Planning, Mid-Year, or Year-End period.\",\r\n      });\r\n    }\r\n\r\n    // Register LeadershipRoles property for panels\r\n    if (skipDuplicateCheck || !Serializer.findProperty(\"panel\", \"leadershipRoles\")) {\r\n      Serializer.addProperty(\"panel\", {\r\n        name: \"leadershipRoles\",\r\n        displayName: \"Leadership Roles\",\r\n        category: \"customSettings\",\r\n        categoryDisplayName: \"Custom Settings\",\r\n        type: isDesigner ? \"multiplevalues\" : \"string\",\r\n        ...(isDesigner && {\r\n          choices: leadershipRoleChoices.length\r\n            ? [{ value: \"\", text: \"-- None --\" }, ...leadershipRoleChoices]\r\n            : [\r\n                { value: \"\", text: \"-- None --\" },\r\n                { value: \"\", text: \"No leadership roles available\" },\r\n              ],\r\n          visibleIndex: 2,\r\n        }),\r\n        description: \"Select leadership roles that can view this panel. If empty, panel is visible to all users.\",\r\n      });\r\n    }\r\n\r\n    // Register ServiceLines property for panels\r\n    if (skipDuplicateCheck || !Serializer.findProperty(\"panel\", \"serviceLines\")) {\r\n      Serializer.addProperty(\"panel\", {\r\n        name: \"serviceLines\",\r\n        displayName: \"Service Lines\",\r\n        category: \"customSettings\",\r\n        categoryDisplayName: \"Custom Settings\",\r\n        type: isDesigner ? \"multiplevalues\" : \"string\",\r\n        ...(isDesigner && {\r\n          choices: serviceLinesChoices.length\r\n            ? [{ value: \"\", text: \"-- None --\" }, ...serviceLinesChoices]\r\n            : [\r\n                { value: \"\", text: \"-- None --\" },\r\n                { value: \"\", text: \"No service lines available\" },\r\n              ],\r\n          visibleIndex: 3,\r\n        }),\r\n        description: \"Select service lines that can view this panel. If empty, panel is visible to all users.\",\r\n      });\r\n    }\r\n\r\n    // Register SubServiceLines property for panels\r\n    if (skipDuplicateCheck || !Serializer.findProperty(\"panel\", \"subServiceLines\")) {\r\n      Serializer.addProperty(\"panel\", {\r\n        name: \"subServiceLines\",\r\n        displayName: \"Sub-Service Lines\",\r\n        category: \"customSettings\",\r\n        categoryDisplayName: \"Custom Settings\",\r\n        type: isDesigner ? \"multiplevalues\" : \"string\",\r\n        ...(isDesigner && {\r\n          choices: subServiceLinesChoices.length\r\n            ? [{ value: \"\", text: \"-- None --\" }, ...subServiceLinesChoices]\r\n            : [\r\n                { value: \"\", text: \"-- None --\" },\r\n                { value: \"\", text: \"No sub-service lines available\" },\r\n              ],\r\n          visibleIndex: 4,\r\n        }),\r\n        description: \"Select sub-service lines that can view this panel. If empty, panel is visible to all users.\",\r\n      });\r\n    }\r\n\r\n    // Register tag property for questions\r\n    if (skipDuplicateCheck || !Serializer.findProperty(\"question\", \"tag\")) {\r\n      const questionTagChoices = isDesigner\r\n        ? [\r\n            { value: \"\", text: \"-- None --\" },\r\n            { value: \"PlanningPartnerQuestion\", text: \"Planning Partner Question\" },\r\n            { value: \"PlanningReviewerQuestion\", text: \"Planning Reviewer Question\" },\r\n            { value: \"MidyearPartnerQuestion\", text: \"Mid-year Partner Question\" },\r\n            { value: \"MidyearReviewerQuestion\", text: \"Mid-Year Reviewer Question\" },\r\n            { value: \"YearendPartnerQuestion\", text: \"Year-end Partner Question\" },\r\n            { value: \"YearendReviewerQuestion\", text: \"Year-end Reviewer Question\" },\r\n          ]\r\n        : [\r\n            { value: \"\", text: \"-- None --\" },\r\n            { value: \"PlanningPartnerQuestion\", text: \"Planning Partner Question\" },\r\n            { value: \"MidYearPartnerQuestion\", text: \"Mid-Year Partner Question\" },\r\n            { value: \"YearEndPartnerQuestion\", text: \"Year-End Partner Question\" },\r\n            { value: \"PlanningReviewerQuestion\", text: \"Planning Reviewer Question\" },\r\n            { value: \"MidYearReviewerQuestion\", text: \"Mid-Year Reviewer Question\" },\r\n            { value: \"YearEndReviewerQuestion\", text: \"Year-End Reviewer Question\" },\r\n          ];\r\n\r\n      Serializer.addProperty(\"question\", {\r\n        name: \"tag\",\r\n        displayName: \"Tag\",\r\n        category: \"customSettings\",\r\n        categoryDisplayName: \"Custom Settings\",\r\n        type: \"dropdown\",\r\n        choices: questionTagChoices,\r\n        ...(isDesigner && { visibleIndex: 0 }),\r\n        description: isDesigner\r\n          ? \"Select a partner reference data column to map this question to\"\r\n          : \"Select a tag for the question that indicates the period and role.\",\r\n      });\r\n    }\r\n\r\n    // Register mapFrom property for questions\r\n    if (skipDuplicateCheck || !Serializer.findProperty(\"question\", \"mapFrom\")) {\r\n      Serializer.addProperty(\"question\", {\r\n        name: \"mapFrom\",\r\n        displayName: \"Map From\",\r\n        category: \"customSettings\",\r\n        categoryDisplayName: \"Custom Settings\",\r\n        type: isDesigner ? \"dropdown\" : \"string\",\r\n        ...(isDesigner && {\r\n          choices: columnChoices.length ? [{ value: \"\", text: \"-- None --\" }, ...columnChoices] : [{ value: \"\", text: \"-- None --\" }],\r\n        }),\r\n        ...(isDesigner && { visibleIndex: 1 }),\r\n        description: \"Select a partner reference data column to map this question to\",\r\n      });\r\n    }\r\n\r\n    // Register exportColumnName property for questions\r\n    if (skipDuplicateCheck || !Serializer.findProperty(\"question\", \"exportColumnName\")) {\r\n      Serializer.addProperty(\"question\", {\r\n        name: \"exportColumnName\",\r\n        displayName: \"Export Column Name\",\r\n        category: \"customSettings\",\r\n        categoryDisplayName: \"Custom Settings\",\r\n        type: \"text\",\r\n        ...(isDesigner && { visibleIndex: 2 }),\r\n        description: \"Specify a custom column name for data export\",\r\n      });\r\n    }\r\n\r\n    // Register mapFromGroup property for questions\r\n    if (skipDuplicateCheck || !Serializer.findProperty(\"question\", \"mapFromGroup\")) {\r\n      Serializer.addProperty(\"question\", {\r\n        name: \"mapFromGroup\",\r\n        displayName: \"Partner Reference Group\",\r\n        category: \"customSettings\",\r\n        categoryDisplayName: \"Custom Settings\",\r\n        type: isDesigner ? \"dropdown\" : \"string\",\r\n        ...(isDesigner && {\r\n          choices: groupChoices.length\r\n            ? [{ value: \"\", text: \"-- None --\" }, ...groupChoices]\r\n            : [\r\n                { value: \"\", text: \"-- None --\" },\r\n                { value: \"\", text: \"No groups available\" },\r\n              ],\r\n          visibleIndex: 3,\r\n          visibleIf: (obj) => obj && obj.getType && obj.getType() === \"dropdown\",\r\n        }),\r\n        description: \"Select a partner reference data group to map this question to\",\r\n      });\r\n    }\r\n\r\n    // Register linkedToGroupPlanning property for questions. \r\n    // Work for rendering planning cycle partner reference data's specified column value.\r\n    if (skipDuplicateCheck || !Serializer.findProperty(\"question\", \"linkedToGroupPlanning\")) {\r\n      const linkedToGroupPlanningConfig = {\r\n        name: \"linkedToGroupPlanning\",\r\n        displayName: \"Linked Group (Planning)\",\r\n        category: \"customSettings\",\r\n        categoryDisplayName: \"Custom Settings\",\r\n        type: isDesigner ? \"dropdown\" : \"string\",\r\n        description: isDesigner\r\n          ? \"Link this text field to a dropdown with mapFromGroup property for auto-population during Planning cycle\"\r\n          : \"Link this text field to a dropdown for auto-population during Planning cycle\",\r\n      };\r\n\r\n      if (isDesigner) {\r\n        linkedToGroupPlanningConfig.choices = function (obj) {\r\n          const survey = obj && obj.survey;\r\n          if (!survey) return [{ value: \"\", text: \"-- None --\" }];\r\n\r\n          const dropdownQuestions = survey\r\n            .getAllQuestions()\r\n            .filter((q) => q.getType() === \"dropdown\" && q.mapFromGroup)\r\n            .map((q) => ({\r\n              value: q.name,\r\n              text: `${q.title || q.name} (Group: ${q.mapFromGroup})`,\r\n            }));\r\n\r\n          return [{ value: \"\", text: \"-- None --\" }, ...dropdownQuestions];\r\n        };\r\n        linkedToGroupPlanningConfig.visibleIndex = 2;\r\n        linkedToGroupPlanningConfig.visibleIf = (obj) => obj && obj.getType && obj.getType() === \"text\";\r\n      }\r\n\r\n      Serializer.addProperty(\"question\", linkedToGroupPlanningConfig);\r\n    }\r\n\r\n    // Register linkedToGroupMidYear property for questions.\r\n    // Work for rendering Mid year cycle partner reference data's specified column value.    \r\n    if (skipDuplicateCheck || !Serializer.findProperty(\"question\", \"linkedToGroupMidYear\")) {\r\n      const linkedToGroupMidYearConfig = {\r\n        name: \"linkedToGroupMidYear\",\r\n        displayName: \"Linked Group (Mid Year)\",\r\n        category: \"customSettings\",\r\n        categoryDisplayName: \"Custom Settings\",\r\n        type: isDesigner ? \"dropdown\" : \"string\",\r\n        description: isDesigner\r\n          ? \"Link this text field to a dropdown with mapFromGroup property for auto-population during Mid Year cycle\"\r\n          : \"Link this text field to a dropdown for auto-population during Mid Year cycle\",\r\n      };\r\n\r\n      if (isDesigner) {\r\n        linkedToGroupMidYearConfig.choices = function (obj) {\r\n          const survey = obj && obj.survey;\r\n          if (!survey) return [{ value: \"\", text: \"-- None --\" }];\r\n\r\n          const dropdownQuestions = survey\r\n            .getAllQuestions()\r\n            .filter((q) => q.getType() === \"dropdown\" && q.mapFromGroup)\r\n            .map((q) => ({\r\n              value: q.name,\r\n              text: `${q.title || q.name} (Group: ${q.mapFromGroup})`,\r\n            }));\r\n\r\n          return [{ value: \"\", text: \"-- None --\" }, ...dropdownQuestions];\r\n        };\r\n        linkedToGroupMidYearConfig.visibleIndex = 3;\r\n        linkedToGroupMidYearConfig.visibleIf = (obj) => obj && obj.getType && obj.getType() === \"text\";\r\n      }\r\n\r\n      Serializer.addProperty(\"question\", linkedToGroupMidYearConfig);\r\n    }\r\n\r\n    // Register linkedToGroupYearEnd property for questions\r\n    if (skipDuplicateCheck || !Serializer.findProperty(\"question\", \"linkedToGroupYearEnd\")) {\r\n      const linkedToGroupYearEndConfig = {\r\n        name: \"linkedToGroupYearEnd\",\r\n        displayName: \"Linked Group (Year End)\",\r\n        category: \"customSettings\",\r\n        categoryDisplayName: \"Custom Settings\",\r\n        type: isDesigner ? \"dropdown\" : \"string\",\r\n        description: isDesigner\r\n          ? \"Link this text field to a dropdown with mapFromGroup property for auto-population during Year End cycle\"\r\n          : \"Link this text field to a dropdown for auto-population during Year End cycle\",\r\n      };\r\n\r\n      if (isDesigner) {\r\n        linkedToGroupYearEndConfig.choices = function (obj) {\r\n          const survey = obj && obj.survey;\r\n          if (!survey) return [{ value: \"\", text: \"-- None --\" }];\r\n\r\n          const dropdownQuestions = survey\r\n            .getAllQuestions()\r\n            .filter((q) => q.getType() === \"dropdown\" && q.mapFromGroup)\r\n            .map((q) => ({\r\n              value: q.name,\r\n              text: `${q.title || q.name} (Group: ${q.mapFromGroup})`,\r\n            }));\r\n\r\n          return [{ value: \"\", text: \"-- None --\" }, ...dropdownQuestions];\r\n        };\r\n        linkedToGroupYearEndConfig.visibleIndex = 4;\r\n        linkedToGroupYearEndConfig.visibleIf = (obj) => obj && obj.getType && obj.getType() === \"text\";\r\n      }\r\n\r\n      Serializer.addProperty(\"question\", linkedToGroupYearEndConfig);\r\n    }\r\n   \r\n  } catch (error) {\r\n    console.error(\"Error registering custom properties:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n/**\r\n * Legacy function name for backward compatibility\r\n * @deprecated Use registerCustomPropertiesForRuntime instead\r\n */\r\nexport const setupCustomProperties = registerCustomPropertiesForRuntime;\r\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,aAAa;AACxC,OAAOC,iCAAiC,MAAM,kDAAkD;AAChG,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,aAAa,MAAM,8BAA8B;;AAExD;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,kCAAkC,GAAG,MAAAA,CAAOC,OAAO,GAAG,CAAC,CAAC,KAAK;EACxE,MAAM;IAAEC,UAAU,GAAG,KAAK;IAAEC,iBAAiB,GAAG,IAAI;IAAEC,kBAAkB,GAAG;EAAM,CAAC,GAAGH,OAAO;EAE5F,IAAI;IACF,IAAII,aAAa,GAAG,EAAE;IACtB,IAAIC,YAAY,GAAG,EAAE;IACrB,IAAIC,qBAAqB,GAAG,EAAE;IAC9B,IAAIC,mBAAmB,GAAG,EAAE;IAC5B,IAAIC,sBAAsB,GAAG,EAAE;;IAE/B;IACA,IAAIP,UAAU,EAAE;MACdG,aAAa,GAAG,MAAMR,iCAAiC,CAACa,iCAAiC,CAACP,iBAAiB,EAAE,IAAI,CAAC;MAClHG,YAAY,GAAG,MAAMT,iCAAiC,CAACc,mBAAmB,CAACR,iBAAiB,CAAC;;MAE7F;MACA,IAAI;QACF,MAAMS,eAAe,GAAG,MAAMd,WAAW,CAACe,wBAAwB,CAACV,iBAAiB,CAAC;QACrF;QACAI,qBAAqB,GAAGK,eAAe,CAACE,GAAG,CAAEC,MAAM,KAAM;UAAEC,KAAK,EAAED,MAAM,CAACE,GAAG;UAAEC,IAAI,EAAEH,MAAM,CAACC;QAAM,CAAC,CAAC,CAAC;MACtG,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdC,OAAO,CAACC,IAAI,CAAC,mCAAmC,EAAEF,KAAK,CAAC;QACxDZ,qBAAqB,GAAG,EAAE;MAC5B;;MAEA;MACA,IAAI;QACF,MAAMe,YAAY,GAAG,MAAMvB,aAAa,CAACwB,eAAe,CAAC,KAAK,CAAC;QAC/D;QACAf,mBAAmB,GAAGc,YAAY,CAACR,GAAG,CAAEC,MAAM,KAAM;UAAEC,KAAK,EAAED,MAAM,CAACE,GAAG;UAAEC,IAAI,EAAEH,MAAM,CAACC;QAAM,CAAC,CAAC,CAAC;MACjG,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdC,OAAO,CAACC,IAAI,CAAC,gCAAgC,EAAEF,KAAK,CAAC;QACrDX,mBAAmB,GAAG,EAAE;MAC1B;;MAEA;MACA,IAAI;QACF,MAAMgB,eAAe,GAAG,MAAMzB,aAAa,CAAC0B,kBAAkB,CAAC,KAAK,CAAC;QACrE;QACAhB,sBAAsB,GAAGe,eAAe,CAACV,GAAG,CAAEC,MAAM,KAAM;UAAEC,KAAK,EAAED,MAAM,CAACE,GAAG;UAAEC,IAAI,EAAEH,MAAM,CAACC;QAAM,CAAC,CAAC,CAAC;MACvG,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdC,OAAO,CAACC,IAAI,CAAC,oCAAoC,EAAEF,KAAK,CAAC;QACzDV,sBAAsB,GAAG,EAAE;MAC7B;IACF;;IAEA;IACA,IAAIL,kBAAkB,IAAI,CAACR,UAAU,CAAC8B,YAAY,CAAC,OAAO,EAAE,KAAK,CAAC,EAAE;MAClE9B,UAAU,CAAC+B,WAAW,CAAC,OAAO,EAAE;QAC9BC,IAAI,EAAE,KAAK;QACXC,WAAW,EAAE,KAAK;QAClBC,QAAQ,EAAE,gBAAgB;QAC1BC,mBAAmB,EAAE,iBAAiB;QACtCC,IAAI,EAAE,UAAU;QAChBC,OAAO,EAAE,CACP;UAAEjB,KAAK,EAAE,EAAE;UAAEE,IAAI,EAAE;QAAa,CAAC,EACjC;UAAEF,KAAK,EAAE,WAAW;UAAEE,IAAI,EAAE;QAAiB,CAAC,EAC9C;UAAEF,KAAK,EAAE,cAAc;UAAEE,IAAI,EAAEhB,UAAU,GAAG,gBAAgB,GAAG;QAAiB,CAAC,EACjF;UAAEc,KAAK,EAAE,cAAc;UAAEE,IAAI,EAAEhB,UAAU,GAAG,gBAAgB,GAAG;QAAiB,CAAC,CAClF;QACD,IAAIA,UAAU,IAAI;UAAEgC,YAAY,EAAE;QAAE,CAAC,CAAC;QACtCC,WAAW,EAAE;MACf,CAAC,CAAC;IACJ;;IAEA;IACA,IAAI/B,kBAAkB,IAAI,CAACR,UAAU,CAAC8B,YAAY,CAAC,OAAO,EAAE,iBAAiB,CAAC,EAAE;MAC9E9B,UAAU,CAAC+B,WAAW,CAAC,OAAO,EAAE;QAC9BC,IAAI,EAAE,iBAAiB;QACvBC,WAAW,EAAE,kBAAkB;QAC/BC,QAAQ,EAAE,gBAAgB;QAC1BC,mBAAmB,EAAE,iBAAiB;QACtCC,IAAI,EAAE9B,UAAU,GAAG,gBAAgB,GAAG,QAAQ;QAC9C,IAAIA,UAAU,IAAI;UAChB+B,OAAO,EAAE1B,qBAAqB,CAAC6B,MAAM,GACjC,CAAC;YAAEpB,KAAK,EAAE,EAAE;YAAEE,IAAI,EAAE;UAAa,CAAC,EAAE,GAAGX,qBAAqB,CAAC,GAC7D,CACE;YAAES,KAAK,EAAE,EAAE;YAAEE,IAAI,EAAE;UAAa,CAAC,EACjC;YAAEF,KAAK,EAAE,EAAE;YAAEE,IAAI,EAAE;UAAgC,CAAC,CACrD;UACLgB,YAAY,EAAE;QAChB,CAAC,CAAC;QACFC,WAAW,EAAE;MACf,CAAC,CAAC;IACJ;;IAEA;IACA,IAAI/B,kBAAkB,IAAI,CAACR,UAAU,CAAC8B,YAAY,CAAC,OAAO,EAAE,cAAc,CAAC,EAAE;MAC3E9B,UAAU,CAAC+B,WAAW,CAAC,OAAO,EAAE;QAC9BC,IAAI,EAAE,cAAc;QACpBC,WAAW,EAAE,eAAe;QAC5BC,QAAQ,EAAE,gBAAgB;QAC1BC,mBAAmB,EAAE,iBAAiB;QACtCC,IAAI,EAAE9B,UAAU,GAAG,gBAAgB,GAAG,QAAQ;QAC9C,IAAIA,UAAU,IAAI;UAChB+B,OAAO,EAAEzB,mBAAmB,CAAC4B,MAAM,GAC/B,CAAC;YAAEpB,KAAK,EAAE,EAAE;YAAEE,IAAI,EAAE;UAAa,CAAC,EAAE,GAAGV,mBAAmB,CAAC,GAC3D,CACE;YAAEQ,KAAK,EAAE,EAAE;YAAEE,IAAI,EAAE;UAAa,CAAC,EACjC;YAAEF,KAAK,EAAE,EAAE;YAAEE,IAAI,EAAE;UAA6B,CAAC,CAClD;UACLgB,YAAY,EAAE;QAChB,CAAC,CAAC;QACFC,WAAW,EAAE;MACf,CAAC,CAAC;IACJ;;IAEA;IACA,IAAI/B,kBAAkB,IAAI,CAACR,UAAU,CAAC8B,YAAY,CAAC,OAAO,EAAE,iBAAiB,CAAC,EAAE;MAC9E9B,UAAU,CAAC+B,WAAW,CAAC,OAAO,EAAE;QAC9BC,IAAI,EAAE,iBAAiB;QACvBC,WAAW,EAAE,mBAAmB;QAChCC,QAAQ,EAAE,gBAAgB;QAC1BC,mBAAmB,EAAE,iBAAiB;QACtCC,IAAI,EAAE9B,UAAU,GAAG,gBAAgB,GAAG,QAAQ;QAC9C,IAAIA,UAAU,IAAI;UAChB+B,OAAO,EAAExB,sBAAsB,CAAC2B,MAAM,GAClC,CAAC;YAAEpB,KAAK,EAAE,EAAE;YAAEE,IAAI,EAAE;UAAa,CAAC,EAAE,GAAGT,sBAAsB,CAAC,GAC9D,CACE;YAAEO,KAAK,EAAE,EAAE;YAAEE,IAAI,EAAE;UAAa,CAAC,EACjC;YAAEF,KAAK,EAAE,EAAE;YAAEE,IAAI,EAAE;UAAiC,CAAC,CACtD;UACLgB,YAAY,EAAE;QAChB,CAAC,CAAC;QACFC,WAAW,EAAE;MACf,CAAC,CAAC;IACJ;;IAEA;IACA,IAAI/B,kBAAkB,IAAI,CAACR,UAAU,CAAC8B,YAAY,CAAC,UAAU,EAAE,KAAK,CAAC,EAAE;MACrE,MAAMW,kBAAkB,GAAGnC,UAAU,GACjC,CACE;QAAEc,KAAK,EAAE,EAAE;QAAEE,IAAI,EAAE;MAAa,CAAC,EACjC;QAAEF,KAAK,EAAE,yBAAyB;QAAEE,IAAI,EAAE;MAA4B,CAAC,EACvE;QAAEF,KAAK,EAAE,0BAA0B;QAAEE,IAAI,EAAE;MAA6B,CAAC,EACzE;QAAEF,KAAK,EAAE,wBAAwB;QAAEE,IAAI,EAAE;MAA4B,CAAC,EACtE;QAAEF,KAAK,EAAE,yBAAyB;QAAEE,IAAI,EAAE;MAA6B,CAAC,EACxE;QAAEF,KAAK,EAAE,wBAAwB;QAAEE,IAAI,EAAE;MAA4B,CAAC,EACtE;QAAEF,KAAK,EAAE,yBAAyB;QAAEE,IAAI,EAAE;MAA6B,CAAC,CACzE,GACD,CACE;QAAEF,KAAK,EAAE,EAAE;QAAEE,IAAI,EAAE;MAAa,CAAC,EACjC;QAAEF,KAAK,EAAE,yBAAyB;QAAEE,IAAI,EAAE;MAA4B,CAAC,EACvE;QAAEF,KAAK,EAAE,wBAAwB;QAAEE,IAAI,EAAE;MAA4B,CAAC,EACtE;QAAEF,KAAK,EAAE,wBAAwB;QAAEE,IAAI,EAAE;MAA4B,CAAC,EACtE;QAAEF,KAAK,EAAE,0BAA0B;QAAEE,IAAI,EAAE;MAA6B,CAAC,EACzE;QAAEF,KAAK,EAAE,yBAAyB;QAAEE,IAAI,EAAE;MAA6B,CAAC,EACxE;QAAEF,KAAK,EAAE,yBAAyB;QAAEE,IAAI,EAAE;MAA6B,CAAC,CACzE;MAELtB,UAAU,CAAC+B,WAAW,CAAC,UAAU,EAAE;QACjCC,IAAI,EAAE,KAAK;QACXC,WAAW,EAAE,KAAK;QAClBC,QAAQ,EAAE,gBAAgB;QAC1BC,mBAAmB,EAAE,iBAAiB;QACtCC,IAAI,EAAE,UAAU;QAChBC,OAAO,EAAEI,kBAAkB;QAC3B,IAAInC,UAAU,IAAI;UAAEgC,YAAY,EAAE;QAAE,CAAC,CAAC;QACtCC,WAAW,EAAEjC,UAAU,GACnB,gEAAgE,GAChE;MACN,CAAC,CAAC;IACJ;;IAEA;IACA,IAAIE,kBAAkB,IAAI,CAACR,UAAU,CAAC8B,YAAY,CAAC,UAAU,EAAE,SAAS,CAAC,EAAE;MACzE9B,UAAU,CAAC+B,WAAW,CAAC,UAAU,EAAE;QACjCC,IAAI,EAAE,SAAS;QACfC,WAAW,EAAE,UAAU;QACvBC,QAAQ,EAAE,gBAAgB;QAC1BC,mBAAmB,EAAE,iBAAiB;QACtCC,IAAI,EAAE9B,UAAU,GAAG,UAAU,GAAG,QAAQ;QACxC,IAAIA,UAAU,IAAI;UAChB+B,OAAO,EAAE5B,aAAa,CAAC+B,MAAM,GAAG,CAAC;YAAEpB,KAAK,EAAE,EAAE;YAAEE,IAAI,EAAE;UAAa,CAAC,EAAE,GAAGb,aAAa,CAAC,GAAG,CAAC;YAAEW,KAAK,EAAE,EAAE;YAAEE,IAAI,EAAE;UAAa,CAAC;QAC5H,CAAC,CAAC;QACF,IAAIhB,UAAU,IAAI;UAAEgC,YAAY,EAAE;QAAE,CAAC,CAAC;QACtCC,WAAW,EAAE;MACf,CAAC,CAAC;IACJ;;IAEA;IACA,IAAI/B,kBAAkB,IAAI,CAACR,UAAU,CAAC8B,YAAY,CAAC,UAAU,EAAE,kBAAkB,CAAC,EAAE;MAClF9B,UAAU,CAAC+B,WAAW,CAAC,UAAU,EAAE;QACjCC,IAAI,EAAE,kBAAkB;QACxBC,WAAW,EAAE,oBAAoB;QACjCC,QAAQ,EAAE,gBAAgB;QAC1BC,mBAAmB,EAAE,iBAAiB;QACtCC,IAAI,EAAE,MAAM;QACZ,IAAI9B,UAAU,IAAI;UAAEgC,YAAY,EAAE;QAAE,CAAC,CAAC;QACtCC,WAAW,EAAE;MACf,CAAC,CAAC;IACJ;;IAEA;IACA,IAAI/B,kBAAkB,IAAI,CAACR,UAAU,CAAC8B,YAAY,CAAC,UAAU,EAAE,cAAc,CAAC,EAAE;MAC9E9B,UAAU,CAAC+B,WAAW,CAAC,UAAU,EAAE;QACjCC,IAAI,EAAE,cAAc;QACpBC,WAAW,EAAE,yBAAyB;QACtCC,QAAQ,EAAE,gBAAgB;QAC1BC,mBAAmB,EAAE,iBAAiB;QACtCC,IAAI,EAAE9B,UAAU,GAAG,UAAU,GAAG,QAAQ;QACxC,IAAIA,UAAU,IAAI;UAChB+B,OAAO,EAAE3B,YAAY,CAAC8B,MAAM,GACxB,CAAC;YAAEpB,KAAK,EAAE,EAAE;YAAEE,IAAI,EAAE;UAAa,CAAC,EAAE,GAAGZ,YAAY,CAAC,GACpD,CACE;YAAEU,KAAK,EAAE,EAAE;YAAEE,IAAI,EAAE;UAAa,CAAC,EACjC;YAAEF,KAAK,EAAE,EAAE;YAAEE,IAAI,EAAE;UAAsB,CAAC,CAC3C;UACLgB,YAAY,EAAE,CAAC;UACfI,SAAS,EAAGC,GAAG,IAAKA,GAAG,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACC,OAAO,CAAC,CAAC,KAAK;QAC9D,CAAC,CAAC;QACFL,WAAW,EAAE;MACf,CAAC,CAAC;IACJ;;IAEA;IACA;IACA,IAAI/B,kBAAkB,IAAI,CAACR,UAAU,CAAC8B,YAAY,CAAC,UAAU,EAAE,uBAAuB,CAAC,EAAE;MACvF,MAAMe,2BAA2B,GAAG;QAClCb,IAAI,EAAE,uBAAuB;QAC7BC,WAAW,EAAE,yBAAyB;QACtCC,QAAQ,EAAE,gBAAgB;QAC1BC,mBAAmB,EAAE,iBAAiB;QACtCC,IAAI,EAAE9B,UAAU,GAAG,UAAU,GAAG,QAAQ;QACxCiC,WAAW,EAAEjC,UAAU,GACnB,yGAAyG,GACzG;MACN,CAAC;MAED,IAAIA,UAAU,EAAE;QACduC,2BAA2B,CAACR,OAAO,GAAG,UAAUM,GAAG,EAAE;UACnD,MAAMG,MAAM,GAAGH,GAAG,IAAIA,GAAG,CAACG,MAAM;UAChC,IAAI,CAACA,MAAM,EAAE,OAAO,CAAC;YAAE1B,KAAK,EAAE,EAAE;YAAEE,IAAI,EAAE;UAAa,CAAC,CAAC;UAEvD,MAAMyB,iBAAiB,GAAGD,MAAM,CAC7BE,eAAe,CAAC,CAAC,CACjBC,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAACN,OAAO,CAAC,CAAC,KAAK,UAAU,IAAIM,CAAC,CAACC,YAAY,CAAC,CAC3DjC,GAAG,CAAEgC,CAAC,KAAM;YACX9B,KAAK,EAAE8B,CAAC,CAAClB,IAAI;YACbV,IAAI,EAAE,GAAG4B,CAAC,CAACE,KAAK,IAAIF,CAAC,CAAClB,IAAI,YAAYkB,CAAC,CAACC,YAAY;UACtD,CAAC,CAAC,CAAC;UAEL,OAAO,CAAC;YAAE/B,KAAK,EAAE,EAAE;YAAEE,IAAI,EAAE;UAAa,CAAC,EAAE,GAAGyB,iBAAiB,CAAC;QAClE,CAAC;QACDF,2BAA2B,CAACP,YAAY,GAAG,CAAC;QAC5CO,2BAA2B,CAACH,SAAS,GAAIC,GAAG,IAAKA,GAAG,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACC,OAAO,CAAC,CAAC,KAAK,MAAM;MACjG;MAEA5C,UAAU,CAAC+B,WAAW,CAAC,UAAU,EAAEc,2BAA2B,CAAC;IACjE;;IAEA;IACA;IACA,IAAIrC,kBAAkB,IAAI,CAACR,UAAU,CAAC8B,YAAY,CAAC,UAAU,EAAE,sBAAsB,CAAC,EAAE;MACtF,MAAMuB,0BAA0B,GAAG;QACjCrB,IAAI,EAAE,sBAAsB;QAC5BC,WAAW,EAAE,yBAAyB;QACtCC,QAAQ,EAAE,gBAAgB;QAC1BC,mBAAmB,EAAE,iBAAiB;QACtCC,IAAI,EAAE9B,UAAU,GAAG,UAAU,GAAG,QAAQ;QACxCiC,WAAW,EAAEjC,UAAU,GACnB,yGAAyG,GACzG;MACN,CAAC;MAED,IAAIA,UAAU,EAAE;QACd+C,0BAA0B,CAAChB,OAAO,GAAG,UAAUM,GAAG,EAAE;UAClD,MAAMG,MAAM,GAAGH,GAAG,IAAIA,GAAG,CAACG,MAAM;UAChC,IAAI,CAACA,MAAM,EAAE,OAAO,CAAC;YAAE1B,KAAK,EAAE,EAAE;YAAEE,IAAI,EAAE;UAAa,CAAC,CAAC;UAEvD,MAAMyB,iBAAiB,GAAGD,MAAM,CAC7BE,eAAe,CAAC,CAAC,CACjBC,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAACN,OAAO,CAAC,CAAC,KAAK,UAAU,IAAIM,CAAC,CAACC,YAAY,CAAC,CAC3DjC,GAAG,CAAEgC,CAAC,KAAM;YACX9B,KAAK,EAAE8B,CAAC,CAAClB,IAAI;YACbV,IAAI,EAAE,GAAG4B,CAAC,CAACE,KAAK,IAAIF,CAAC,CAAClB,IAAI,YAAYkB,CAAC,CAACC,YAAY;UACtD,CAAC,CAAC,CAAC;UAEL,OAAO,CAAC;YAAE/B,KAAK,EAAE,EAAE;YAAEE,IAAI,EAAE;UAAa,CAAC,EAAE,GAAGyB,iBAAiB,CAAC;QAClE,CAAC;QACDM,0BAA0B,CAACf,YAAY,GAAG,CAAC;QAC3Ce,0BAA0B,CAACX,SAAS,GAAIC,GAAG,IAAKA,GAAG,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACC,OAAO,CAAC,CAAC,KAAK,MAAM;MAChG;MAEA5C,UAAU,CAAC+B,WAAW,CAAC,UAAU,EAAEsB,0BAA0B,CAAC;IAChE;;IAEA;IACA,IAAI7C,kBAAkB,IAAI,CAACR,UAAU,CAAC8B,YAAY,CAAC,UAAU,EAAE,sBAAsB,CAAC,EAAE;MACtF,MAAMwB,0BAA0B,GAAG;QACjCtB,IAAI,EAAE,sBAAsB;QAC5BC,WAAW,EAAE,yBAAyB;QACtCC,QAAQ,EAAE,gBAAgB;QAC1BC,mBAAmB,EAAE,iBAAiB;QACtCC,IAAI,EAAE9B,UAAU,GAAG,UAAU,GAAG,QAAQ;QACxCiC,WAAW,EAAEjC,UAAU,GACnB,yGAAyG,GACzG;MACN,CAAC;MAED,IAAIA,UAAU,EAAE;QACdgD,0BAA0B,CAACjB,OAAO,GAAG,UAAUM,GAAG,EAAE;UAClD,MAAMG,MAAM,GAAGH,GAAG,IAAIA,GAAG,CAACG,MAAM;UAChC,IAAI,CAACA,MAAM,EAAE,OAAO,CAAC;YAAE1B,KAAK,EAAE,EAAE;YAAEE,IAAI,EAAE;UAAa,CAAC,CAAC;UAEvD,MAAMyB,iBAAiB,GAAGD,MAAM,CAC7BE,eAAe,CAAC,CAAC,CACjBC,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAACN,OAAO,CAAC,CAAC,KAAK,UAAU,IAAIM,CAAC,CAACC,YAAY,CAAC,CAC3DjC,GAAG,CAAEgC,CAAC,KAAM;YACX9B,KAAK,EAAE8B,CAAC,CAAClB,IAAI;YACbV,IAAI,EAAE,GAAG4B,CAAC,CAACE,KAAK,IAAIF,CAAC,CAAClB,IAAI,YAAYkB,CAAC,CAACC,YAAY;UACtD,CAAC,CAAC,CAAC;UAEL,OAAO,CAAC;YAAE/B,KAAK,EAAE,EAAE;YAAEE,IAAI,EAAE;UAAa,CAAC,EAAE,GAAGyB,iBAAiB,CAAC;QAClE,CAAC;QACDO,0BAA0B,CAAChB,YAAY,GAAG,CAAC;QAC3CgB,0BAA0B,CAACZ,SAAS,GAAIC,GAAG,IAAKA,GAAG,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACC,OAAO,CAAC,CAAC,KAAK,MAAM;MAChG;MAEA5C,UAAU,CAAC+B,WAAW,CAAC,UAAU,EAAEuB,0BAA0B,CAAC;IAChE;EAEF,CAAC,CAAC,OAAO/B,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;IAC5D,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMgC,qBAAqB,GAAGnD,kCAAkC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}