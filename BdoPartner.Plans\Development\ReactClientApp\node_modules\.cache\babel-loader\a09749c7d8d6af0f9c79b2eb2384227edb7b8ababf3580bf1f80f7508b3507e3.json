{"ast": null, "code": "var _jsxFileName = \"C:\\\\git\\\\Partner-Site\\\\BdoPartner.Plans\\\\Development\\\\ReactClientApp\\\\src\\\\core\\\\message\\\\components\\\\messageDialog.jsx\";\nimport React, { Component } from \"react\";\nimport { messageService } from \"../messageService\";\nimport { <PERSON><PERSON> } from \"primereact/button\";\nimport { Dialog } from \"primereact/dialog\";\nimport { confirmDialog } from \"primereact/confirmdialog\";\nimport \"./messageDialog.scss\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nclass MessageDialog extends Component {\n  constructor(props) {\n    super(props);\n    /** It is reference of method passed from message service's caller,\r\n     * which is component calling messageService to show confirm dialog.\r\n     * */\n    this.callback = result => {};\n    this.state = {\n      visible: false,\n      content: \"\",\n      header: \"\",\n      messageType: \"info\"\n    };\n    this.accept = this.accept.bind(this);\n    this.reject = this.reject.bind(this);\n  }\n  /** It is \"ok\" button click in confirmation dialog. */\n  accept() {\n    this.callback(true);\n  }\n\n  /** It is \"no\" button click in confirmation dialog. */\n  reject() {\n    this.callback(false);\n  }\n  componentDidMount() {\n    this.subscription = messageService.get().subscribe(message => {\n      if (message && message.modalType === \"dialog\") {\n        this.callback = message.callback;\n        // Note: Dialog message must be shown up immediately.\n        this.showMessage(message);\n      }\n    });\n  }\n  componentWillUnmount() {\n    // unsubscribe to ensure no memory leaks\n    this.subscription.unsubscribe();\n  }\n  showMessage(message) {\n    if (message.content !== \"IsEmitNotify\") {\n      switch (message.messageType) {\n        case \"confirmation\":\n          // Display generic confirmation dialog with \"Yes\" and \"No\" buttons\n          confirmDialog({\n            className: \"appDialog-info\",\n            message: /*#__PURE__*/_jsxDEV(\"div\", {\n              id: \"appDialogContent\",\n              children: this.renderContent(\"info\", message.content)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 15\n            }, this),\n            header: \"Confirmation\",\n            accept: this.accept,\n            reject: this.reject\n          });\n          break;\n        case \"deletionConfirmation\":\n          //\n          // Display deletion confirmation dialog with \"Yes\" and \"No\" buttons. Style sheet a little bit difference with \"confirmation\" dialog.\n          //\n          confirmDialog({\n            className: \"appDialog-error\",\n            message: /*#__PURE__*/_jsxDEV(\"div\", {\n              id: \"appDialogContent\",\n              children: this.renderContent(\"info\", message.content)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 15\n            }, this),\n            header: \"Delete Confirmation\",\n            acceptClassName: \"p-button-danger\",\n            accept: this.accept,\n            reject: this.reject\n          });\n          break;\n        default:\n          //\n          // Display generic message dialog with \"Ok\" button only.\n          //\n          this.setState({\n            visible: true,\n            content: message.content,\n            header: message.messageType,\n            messageType: message.messageType\n          });\n          break;\n      }\n    }\n  }\n  onHide(name) {\n    this.setState({\n      [`${name}`]: false\n    });\n  }\n\n  /** Work for generic message dialog footer rendering. Only show \"Ok\" button. */\n  renderFooter(name) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        label: \"Ok\",\n        icon: \"pi pi-check\",\n        onClick: () => this.onHide(name),\n        autoFocus: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 110,\n      columnNumber: 7\n    }, this);\n  }\n  renderHeader(title) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"appDialogHeader\",\n      children: title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 12\n    }, this);\n  }\n  renderContent(messageType, content) {\n    switch (messageType) {\n      case \"success\":\n        return /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            class: \"pi pi-check-circle\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 13\n          }, this), \"\\xA0\\xA0\", content]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this);\n      case \"error\":\n        return /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            class: \"pi pi-times-circle\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this), \"\\xA0\\xA0\", content]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this);\n      case \"warn\":\n        return /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            class: \"pi pi-exclamation-circle\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this), \"\\xA0\\xA0\", content]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this);\n      case \"info\":\n        return /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            class: \"pi pi-info-circle\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this), \"\\xA0\\xA0\", content]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 11\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            class: \"pi pi-info-circle\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 13\n          }, this), \"\\xA0\\xA0\", content]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this);\n    }\n  }\n  render() {\n    return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: /*#__PURE__*/_jsxDEV(Dialog, {\n        className: `appDialog-${this.state.messageType}`,\n        header: this.renderHeader(this.state.header),\n        visible: this.state.visible,\n        style: {\n          width: \"50vw\"\n        },\n        footer: this.renderFooter(\"visible\"),\n        onHide: () => this.onHide(\"visible\"),\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          id: \"appDialogContent\",\n          children: this.renderContent(this.state.messageType, this.state.content)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 9\n      }, this)\n    }, void 0, false);\n  }\n}\nexport default MessageDialog;", "map": {"version": 3, "names": ["React", "Component", "messageService", "<PERSON><PERSON>", "Dialog", "confirmDialog", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "MessageDialog", "constructor", "props", "callback", "result", "state", "visible", "content", "header", "messageType", "accept", "bind", "reject", "componentDidMount", "subscription", "get", "subscribe", "message", "modalType", "showMessage", "componentWillUnmount", "unsubscribe", "className", "id", "children", "renderContent", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "acceptClassName", "setState", "onHide", "name", "renderFooter", "label", "icon", "onClick", "autoFocus", "renderHeader", "title", "class", "render", "style", "width", "footer"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/core/message/components/messageDialog.jsx"], "sourcesContent": ["import React, { Component } from \"react\";\r\nimport { messageService } from \"../messageService\";\r\nimport { <PERSON><PERSON> } from \"primereact/button\";\r\nimport { Dialog } from \"primereact/dialog\";\r\nimport { confirmDialog } from \"primereact/confirmdialog\";\r\nimport \"./messageDialog.scss\";\r\n\r\nclass MessageDialog extends Component {\r\n  constructor(props) {\r\n    super(props);\r\n    this.state = {\r\n      visible: false,\r\n      content: \"\",\r\n      header: \"\",\r\n      messageType: \"info\",\r\n    };\r\n\r\n    this.accept = this.accept.bind(this);\r\n    this.reject = this.reject.bind(this);\r\n  }\r\n\r\n  /** It is reference of method passed from message service's caller,\r\n   * which is component calling messageService to show confirm dialog.\r\n   * */\r\n  callback = (result) => {};\r\n\r\n  /** It is \"ok\" button click in confirmation dialog. */\r\n  accept() {\r\n    this.callback(true);\r\n  }\r\n\r\n  /** It is \"no\" button click in confirmation dialog. */\r\n  reject() {\r\n    this.callback(false);\r\n  }\r\n\r\n  componentDidMount() {\r\n    this.subscription = messageService.get().subscribe((message) => {\r\n      if (message && message.modalType === \"dialog\") {\r\n        this.callback = message.callback;\r\n        // Note: Dialog message must be shown up immediately.\r\n        this.showMessage(message);\r\n      }\r\n    });\r\n  }\r\n\r\n  componentWillUnmount() {\r\n    // unsubscribe to ensure no memory leaks\r\n    this.subscription.unsubscribe();\r\n  }\r\n\r\n  showMessage(message) {\r\n    if (message.content !== \"IsEmitNotify\") {\r\n      switch (message.messageType) {\r\n        case \"confirmation\":\r\n          // Display generic confirmation dialog with \"Yes\" and \"No\" buttons\r\n          confirmDialog({\r\n            className: \"appDialog-info\",\r\n            message: (\r\n              <div id=\"appDialogContent\">\r\n                {this.renderContent(\"info\", message.content)}\r\n              </div>\r\n            ),\r\n            header: \"Confirmation\",\r\n            accept: this.accept,\r\n            reject: this.reject,\r\n          });\r\n          break;\r\n        case \"deletionConfirmation\":\r\n          //\r\n          // Display deletion confirmation dialog with \"Yes\" and \"No\" buttons. Style sheet a little bit difference with \"confirmation\" dialog.\r\n          //\r\n          confirmDialog({\r\n            className: \"appDialog-error\",\r\n            message: (\r\n              <div id=\"appDialogContent\">\r\n                {this.renderContent(\"info\", message.content)}\r\n              </div>\r\n            ),\r\n            header: \"Delete Confirmation\",\r\n            acceptClassName: \"p-button-danger\",\r\n            accept: this.accept,\r\n            reject: this.reject,\r\n          });\r\n          break;\r\n        default:\r\n          //\r\n          // Display generic message dialog with \"Ok\" button only.\r\n          //\r\n          this.setState({\r\n            visible: true,\r\n            content: message.content,\r\n            header: message.messageType,\r\n            messageType: message.messageType,\r\n          });\r\n          break;\r\n      }\r\n    }\r\n  }\r\n\r\n  onHide(name) {\r\n    this.setState({\r\n      [`${name}`]: false,\r\n    });\r\n  }\r\n\r\n  /** Work for generic message dialog footer rendering. Only show \"Ok\" button. */\r\n  renderFooter(name) {\r\n    return (\r\n      <div>\r\n        {/* <Button\r\n          label=\"No\"\r\n          icon=\"pi pi-times\"\r\n          onClick={() => this.onHide(name)}\r\n          className=\"p-button-text\"\r\n        /> */}\r\n        <Button\r\n          label=\"Ok\"\r\n          icon=\"pi pi-check\"\r\n          onClick={() => this.onHide(name)}\r\n          autoFocus\r\n        />\r\n      </div>\r\n    );\r\n  }\r\n\r\n  renderHeader(title) {\r\n    return <div className=\"appDialogHeader\">{title}</div>;\r\n  }\r\n\r\n  renderContent(messageType, content) {\r\n    switch (messageType) {\r\n      case \"success\":\r\n        return (\r\n          <p>\r\n            <i class=\"pi pi-check-circle\" />\r\n            &nbsp;&nbsp;{content}\r\n          </p>\r\n        );\r\n      case \"error\":\r\n        return (\r\n          <p>\r\n            <i class=\"pi pi-times-circle\" />\r\n            &nbsp;&nbsp;{content}\r\n          </p>\r\n        );\r\n      case \"warn\":\r\n        return (\r\n          <p>\r\n            <i class=\"pi pi-exclamation-circle\" />\r\n            &nbsp;&nbsp;{content}\r\n          </p>\r\n        );\r\n      case \"info\":\r\n        return (\r\n          <p>\r\n            <i class=\"pi pi-info-circle\" />\r\n            &nbsp;&nbsp;{content}\r\n          </p>\r\n        );\r\n      default:\r\n        return (\r\n          <p>\r\n            <i class=\"pi pi-info-circle\" />\r\n            &nbsp;&nbsp;{content}\r\n          </p>\r\n        );\r\n    }\r\n  }\r\n\r\n  render() {\r\n    return (\r\n      <>\r\n        <Dialog\r\n          className={`appDialog-${this.state.messageType}`}\r\n          header={this.renderHeader(this.state.header)}\r\n          visible={this.state.visible}\r\n          style={{ width: \"50vw\" }}\r\n          footer={this.renderFooter(\"visible\")}\r\n          onHide={() => this.onHide(\"visible\")}\r\n        >\r\n          <div id=\"appDialogContent\">\r\n            {this.renderContent(this.state.messageType, this.state.content)}\r\n          </div>\r\n        </Dialog>\r\n      </>\r\n    );\r\n  }\r\n}\r\n\r\nexport default MessageDialog;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,aAAa,QAAQ,0BAA0B;AACxD,OAAO,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE9B,MAAMC,aAAa,SAAST,SAAS,CAAC;EACpCU,WAAWA,CAACC,KAAK,EAAE;IACjB,KAAK,CAACA,KAAK,CAAC;IAYd;AACF;AACA;IAFE,KAGAC,QAAQ,GAAIC,MAAM,IAAK,CAAC,CAAC;IAdvB,IAAI,CAACC,KAAK,GAAG;MACXC,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE,EAAE;MACXC,MAAM,EAAE,EAAE;MACVC,WAAW,EAAE;IACf,CAAC;IAED,IAAI,CAACC,MAAM,GAAG,IAAI,CAACA,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC;IACpC,IAAI,CAACC,MAAM,GAAG,IAAI,CAACA,MAAM,CAACD,IAAI,CAAC,IAAI,CAAC;EACtC;EAOA;EACAD,MAAMA,CAAA,EAAG;IACP,IAAI,CAACP,QAAQ,CAAC,IAAI,CAAC;EACrB;;EAEA;EACAS,MAAMA,CAAA,EAAG;IACP,IAAI,CAACT,QAAQ,CAAC,KAAK,CAAC;EACtB;EAEAU,iBAAiBA,CAAA,EAAG;IAClB,IAAI,CAACC,YAAY,GAAGtB,cAAc,CAACuB,GAAG,CAAC,CAAC,CAACC,SAAS,CAAEC,OAAO,IAAK;MAC9D,IAAIA,OAAO,IAAIA,OAAO,CAACC,SAAS,KAAK,QAAQ,EAAE;QAC7C,IAAI,CAACf,QAAQ,GAAGc,OAAO,CAACd,QAAQ;QAChC;QACA,IAAI,CAACgB,WAAW,CAACF,OAAO,CAAC;MAC3B;IACF,CAAC,CAAC;EACJ;EAEAG,oBAAoBA,CAAA,EAAG;IACrB;IACA,IAAI,CAACN,YAAY,CAACO,WAAW,CAAC,CAAC;EACjC;EAEAF,WAAWA,CAACF,OAAO,EAAE;IACnB,IAAIA,OAAO,CAACV,OAAO,KAAK,cAAc,EAAE;MACtC,QAAQU,OAAO,CAACR,WAAW;QACzB,KAAK,cAAc;UACjB;UACAd,aAAa,CAAC;YACZ2B,SAAS,EAAE,gBAAgB;YAC3BL,OAAO,eACLpB,OAAA;cAAK0B,EAAE,EAAC,kBAAkB;cAAAC,QAAA,EACvB,IAAI,CAACC,aAAa,CAAC,MAAM,EAAER,OAAO,CAACV,OAAO;YAAC;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CACN;YACDrB,MAAM,EAAE,cAAc;YACtBE,MAAM,EAAE,IAAI,CAACA,MAAM;YACnBE,MAAM,EAAE,IAAI,CAACA;UACf,CAAC,CAAC;UACF;QACF,KAAK,sBAAsB;UACzB;UACA;UACA;UACAjB,aAAa,CAAC;YACZ2B,SAAS,EAAE,iBAAiB;YAC5BL,OAAO,eACLpB,OAAA;cAAK0B,EAAE,EAAC,kBAAkB;cAAAC,QAAA,EACvB,IAAI,CAACC,aAAa,CAAC,MAAM,EAAER,OAAO,CAACV,OAAO;YAAC;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CACN;YACDrB,MAAM,EAAE,qBAAqB;YAC7BsB,eAAe,EAAE,iBAAiB;YAClCpB,MAAM,EAAE,IAAI,CAACA,MAAM;YACnBE,MAAM,EAAE,IAAI,CAACA;UACf,CAAC,CAAC;UACF;QACF;UACE;UACA;UACA;UACA,IAAI,CAACmB,QAAQ,CAAC;YACZzB,OAAO,EAAE,IAAI;YACbC,OAAO,EAAEU,OAAO,CAACV,OAAO;YACxBC,MAAM,EAAES,OAAO,CAACR,WAAW;YAC3BA,WAAW,EAAEQ,OAAO,CAACR;UACvB,CAAC,CAAC;UACF;MACJ;IACF;EACF;EAEAuB,MAAMA,CAACC,IAAI,EAAE;IACX,IAAI,CAACF,QAAQ,CAAC;MACZ,CAAC,GAAGE,IAAI,EAAE,GAAG;IACf,CAAC,CAAC;EACJ;;EAEA;EACAC,YAAYA,CAACD,IAAI,EAAE;IACjB,oBACEpC,OAAA;MAAA2B,QAAA,eAOE3B,OAAA,CAACJ,MAAM;QACL0C,KAAK,EAAC,IAAI;QACVC,IAAI,EAAC,aAAa;QAClBC,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACL,MAAM,CAACC,IAAI,CAAE;QACjCK,SAAS;MAAA;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV;EAEAU,YAAYA,CAACC,KAAK,EAAE;IAClB,oBAAO3C,OAAA;MAAKyB,SAAS,EAAC,iBAAiB;MAAAE,QAAA,EAAEgB;IAAK;MAAAd,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EACvD;EAEAJ,aAAaA,CAAChB,WAAW,EAAEF,OAAO,EAAE;IAClC,QAAQE,WAAW;MACjB,KAAK,SAAS;QACZ,oBACEZ,OAAA;UAAA2B,QAAA,gBACE3B,OAAA;YAAG4C,KAAK,EAAC;UAAoB;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,YACpB,EAACtB,OAAO;QAAA;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAER,KAAK,OAAO;QACV,oBACEhC,OAAA;UAAA2B,QAAA,gBACE3B,OAAA;YAAG4C,KAAK,EAAC;UAAoB;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,YACpB,EAACtB,OAAO;QAAA;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAER,KAAK,MAAM;QACT,oBACEhC,OAAA;UAAA2B,QAAA,gBACE3B,OAAA;YAAG4C,KAAK,EAAC;UAA0B;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,YAC1B,EAACtB,OAAO;QAAA;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAER,KAAK,MAAM;QACT,oBACEhC,OAAA;UAAA2B,QAAA,gBACE3B,OAAA;YAAG4C,KAAK,EAAC;UAAmB;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,YACnB,EAACtB,OAAO;QAAA;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAER;QACE,oBACEhC,OAAA;UAAA2B,QAAA,gBACE3B,OAAA;YAAG4C,KAAK,EAAC;UAAmB;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,YACnB,EAACtB,OAAO;QAAA;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;IAEV;EACF;EAEAa,MAAMA,CAAA,EAAG;IACP,oBACE7C,OAAA,CAAAE,SAAA;MAAAyB,QAAA,eACE3B,OAAA,CAACH,MAAM;QACL4B,SAAS,EAAE,aAAa,IAAI,CAACjB,KAAK,CAACI,WAAW,EAAG;QACjDD,MAAM,EAAE,IAAI,CAAC+B,YAAY,CAAC,IAAI,CAAClC,KAAK,CAACG,MAAM,CAAE;QAC7CF,OAAO,EAAE,IAAI,CAACD,KAAK,CAACC,OAAQ;QAC5BqC,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAO,CAAE;QACzBC,MAAM,EAAE,IAAI,CAACX,YAAY,CAAC,SAAS,CAAE;QACrCF,MAAM,EAAEA,CAAA,KAAM,IAAI,CAACA,MAAM,CAAC,SAAS,CAAE;QAAAR,QAAA,eAErC3B,OAAA;UAAK0B,EAAE,EAAC,kBAAkB;UAAAC,QAAA,EACvB,IAAI,CAACC,aAAa,CAAC,IAAI,CAACpB,KAAK,CAACI,WAAW,EAAE,IAAI,CAACJ,KAAK,CAACE,OAAO;QAAC;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC,gBACT,CAAC;EAEP;AACF;AAEA,eAAe7B,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}