{"ast": null, "code": "import { EmptyError } from '../util/EmptyError';\nimport { filter } from './filter';\nimport { takeLast } from './takeLast';\nimport { throwIfEmpty } from './throwIfEmpty';\nimport { defaultIfEmpty } from './defaultIfEmpty';\nimport { identity } from '../util/identity';\nexport function last(predicate, defaultValue) {\n  var hasDefaultValue = arguments.length >= 2;\n  return function (source) {\n    return source.pipe(predicate ? filter(function (v, i) {\n      return predicate(v, i, source);\n    }) : identity, takeLast(1), hasDefaultValue ? defaultIfEmpty(defaultValue) : throwIfEmpty(function () {\n      return new EmptyError();\n    }));\n  };\n}", "map": {"version": 3, "names": ["EmptyError", "filter", "takeLast", "throwIfEmpty", "defaultIfEmpty", "identity", "last", "predicate", "defaultValue", "hasDefaultValue", "arguments", "length", "source", "pipe", "v", "i"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\last.ts"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { EmptyError } from '../util/EmptyError';\nimport { OperatorFunction, TruthyTypesOf } from '../types';\nimport { filter } from './filter';\nimport { takeLast } from './takeLast';\nimport { throwIfEmpty } from './throwIfEmpty';\nimport { defaultIfEmpty } from './defaultIfEmpty';\nimport { identity } from '../util/identity';\n\nexport function last<T>(predicate: BooleanConstructor): OperatorFunction<T, TruthyTypesOf<T>>;\nexport function last<T, D>(predicate: BooleanConstructor, defaultValue: D): OperatorFunction<T, TruthyTypesOf<T> | D>;\nexport function last<T, D = T>(predicate?: null, defaultValue?: D): OperatorFunction<T, T | D>;\nexport function last<T, S extends T>(\n  predicate: (value: T, index: number, source: Observable<T>) => value is S,\n  defaultValue?: S\n): OperatorFunction<T, S>;\nexport function last<T, D = T>(\n  predicate: (value: T, index: number, source: Observable<T>) => boolean,\n  defaultValue?: D\n): OperatorFunction<T, T | D>;\n\n/**\n * Returns an Observable that emits only the last item emitted by the source Observable.\n * It optionally takes a predicate function as a parameter, in which case, rather than emitting\n * the last item from the source Observable, the resulting Observable will emit the last item\n * from the source Observable that satisfies the predicate.\n *\n * ![](last.png)\n *\n * It will emit an error notification if the source completes without notification or one that matches\n * the predicate. It returns the last value or if a predicate is provided last value that matches the\n * predicate. It returns the given default value if no notification is emitted or matches the predicate.\n *\n * ## Examples\n *\n * Last alphabet from the sequence\n *\n * ```ts\n * import { from, last } from 'rxjs';\n *\n * const source = from(['x', 'y', 'z']);\n * const result = source.pipe(last());\n *\n * result.subscribe(value => console.log(`Last alphabet: ${ value }`));\n *\n * // Outputs\n * // Last alphabet: z\n * ```\n *\n * Default value when the value in the predicate is not matched\n *\n * ```ts\n * import { from, last } from 'rxjs';\n *\n * const source = from(['x', 'y', 'z']);\n * const result = source.pipe(last(char => char === 'a', 'not found'));\n *\n * result.subscribe(value => console.log(`'a' is ${ value }.`));\n *\n * // Outputs\n * // 'a' is not found.\n * ```\n *\n * @see {@link skip}\n * @see {@link skipUntil}\n * @see {@link skipLast}\n * @see {@link skipWhile}\n * @see {@link first}\n *\n * @throws {EmptyError} Delivers an `EmptyError` to the Observer's `error`\n * callback if the Observable completes before any `next` notification was sent.\n *\n * @param predicate The condition any source emitted item has to satisfy.\n * @param defaultValue An optional default value to provide if last `predicate`\n * isn't met or no values were emitted.\n * @return A function that returns an Observable that emits only the last item\n * satisfying the given condition from the source, or an error notification\n * with an `EmptyError` object if no such items are emitted.\n */\nexport function last<T, D>(\n  predicate?: ((value: T, index: number, source: Observable<T>) => boolean) | null,\n  defaultValue?: D\n): OperatorFunction<T, T | D> {\n  const hasDefaultValue = arguments.length >= 2;\n  return (source: Observable<T>) =>\n    source.pipe(\n      predicate ? filter((v, i) => predicate(v, i, source)) : identity,\n      takeLast(1),\n      hasDefaultValue ? defaultIfEmpty(defaultValue!) : throwIfEmpty(() => new EmptyError())\n    );\n}\n"], "mappings": "AACA,SAASA,UAAU,QAAQ,oBAAoB;AAE/C,SAASC,MAAM,QAAQ,UAAU;AACjC,SAASC,QAAQ,QAAQ,YAAY;AACrC,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,QAAQ,QAAQ,kBAAkB;AAwE3C,OAAM,SAAUC,IAAIA,CAClBC,SAAgF,EAChFC,YAAgB;EAEhB,IAAMC,eAAe,GAAGC,SAAS,CAACC,MAAM,IAAI,CAAC;EAC7C,OAAO,UAACC,MAAqB;IAC3B,OAAAA,MAAM,CAACC,IAAI,CACTN,SAAS,GAAGN,MAAM,CAAC,UAACa,CAAC,EAAEC,CAAC;MAAK,OAAAR,SAAS,CAACO,CAAC,EAAEC,CAAC,EAAEH,MAAM,CAAC;IAAvB,CAAuB,CAAC,GAAGP,QAAQ,EAChEH,QAAQ,CAAC,CAAC,CAAC,EACXO,eAAe,GAAGL,cAAc,CAACI,YAAa,CAAC,GAAGL,YAAY,CAAC;MAAM,WAAIH,UAAU,EAAE;IAAhB,CAAgB,CAAC,CACvF;EAJD,CAIC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}