{"ast": null, "code": "import { Observable } from '../Observable';\nimport { argsOrArgArray } from '../util/argsOrArgArray';\nimport { OperatorSubscriber } from '../operators/OperatorSubscriber';\nimport { noop } from '../util/noop';\nimport { innerFrom } from './innerFrom';\nexport function onErrorResumeNext() {\n  var sources = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    sources[_i] = arguments[_i];\n  }\n  var nextSources = argsOrArgArray(sources);\n  return new Observable(function (subscriber) {\n    var sourceIndex = 0;\n    var subscribeNext = function () {\n      if (sourceIndex < nextSources.length) {\n        var nextSource = void 0;\n        try {\n          nextSource = innerFrom(nextSources[sourceIndex++]);\n        } catch (err) {\n          subscribeNext();\n          return;\n        }\n        var innerSubscriber = new OperatorSubscriber(subscriber, undefined, noop, noop);\n        nextSource.subscribe(innerSubscriber);\n        innerSubscriber.add(subscribeNext);\n      } else {\n        subscriber.complete();\n      }\n    };\n    subscribeNext();\n  });\n}", "map": {"version": 3, "names": ["Observable", "argsOrArgArray", "OperatorSubscriber", "noop", "innerFrom", "onErrorResumeNext", "sources", "_i", "arguments", "length", "nextSources", "subscriber", "sourceIndex", "subscribeNext", "nextSource", "err", "innerSubscriber", "undefined", "subscribe", "add", "complete"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\observable\\onErrorResumeNext.ts"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { ObservableInputTuple } from '../types';\nimport { argsOrArgArray } from '../util/argsOrArgArray';\nimport { OperatorSubscriber } from '../operators/OperatorSubscriber';\nimport { noop } from '../util/noop';\nimport { innerFrom } from './innerFrom';\n\nexport function onErrorResumeNext<A extends readonly unknown[]>(sources: [...ObservableInputTuple<A>]): Observable<A[number]>;\nexport function onErrorResumeNext<A extends readonly unknown[]>(...sources: [...ObservableInputTuple<A>]): Observable<A[number]>;\n\n/**\n * When any of the provided Observable emits a complete or an error notification, it immediately subscribes to the next one\n * that was passed.\n *\n * <span class=\"informal\">Execute series of Observables no matter what, even if it means swallowing errors.</span>\n *\n * ![](onErrorResumeNext.png)\n *\n * `onErrorResumeNext` will subscribe to each observable source it is provided, in order.\n * If the source it's subscribed to emits an error or completes, it will move to the next source\n * without error.\n *\n * If `onErrorResumeNext` is provided no arguments, or a single, empty array, it will return {@link EMPTY}.\n *\n * `onErrorResumeNext` is basically {@link concat}, only it will continue, even if one of its\n * sources emits an error.\n *\n * Note that there is no way to handle any errors thrown by sources via the result of\n * `onErrorResumeNext`. If you want to handle errors thrown in any given source, you can\n * always use the {@link catchError} operator on them before passing them into `onErrorResumeNext`.\n *\n * ## Example\n *\n * Subscribe to the next Observable after map fails\n *\n * ```ts\n * import { onErrorResumeNext, of, map } from 'rxjs';\n *\n * onErrorResumeNext(\n *   of(1, 2, 3, 0).pipe(\n *     map(x => {\n *       if (x === 0) {\n *         throw Error();\n *       }\n *       return 10 / x;\n *     })\n *   ),\n *   of(1, 2, 3)\n * )\n * .subscribe({\n *   next: value => console.log(value),\n *   error: err => console.log(err),     // Will never be called.\n *   complete: () => console.log('done')\n * });\n *\n * // Logs:\n * // 10\n * // 5\n * // 3.3333333333333335\n * // 1\n * // 2\n * // 3\n * // 'done'\n * ```\n *\n * @see {@link concat}\n * @see {@link catchError}\n *\n * @param sources `ObservableInput`s passed either directly or as an array.\n * @return An Observable that concatenates all sources, one after the other,\n * ignoring all errors, such that any error causes it to move on to the next source.\n */\nexport function onErrorResumeNext<A extends readonly unknown[]>(\n  ...sources: [[...ObservableInputTuple<A>]] | [...ObservableInputTuple<A>]\n): Observable<A[number]> {\n  const nextSources: ObservableInputTuple<A> = argsOrArgArray(sources) as any;\n\n  return new Observable((subscriber) => {\n    let sourceIndex = 0;\n    const subscribeNext = () => {\n      if (sourceIndex < nextSources.length) {\n        let nextSource: Observable<A[number]>;\n        try {\n          nextSource = innerFrom(nextSources[sourceIndex++]);\n        } catch (err) {\n          subscribeNext();\n          return;\n        }\n        const innerSubscriber = new OperatorSubscriber(subscriber, undefined, noop, noop);\n        nextSource.subscribe(innerSubscriber);\n        innerSubscriber.add(subscribeNext);\n      } else {\n        subscriber.complete();\n      }\n    };\n    subscribeNext();\n  });\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAE1C,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,kBAAkB,QAAQ,iCAAiC;AACpE,SAASC,IAAI,QAAQ,cAAc;AACnC,SAASC,SAAS,QAAQ,aAAa;AAmEvC,OAAM,SAAUC,iBAAiBA,CAAA;EAC/B,IAAAC,OAAA;OAAA,IAAAC,EAAA,IAAyE,EAAzEA,EAAA,GAAAC,SAAA,CAAAC,MAAyE,EAAzEF,EAAA,EAAyE;IAAzED,OAAA,CAAAC,EAAA,IAAAC,SAAA,CAAAD,EAAA;;EAEA,IAAMG,WAAW,GAA4BT,cAAc,CAACK,OAAO,CAAQ;EAE3E,OAAO,IAAIN,UAAU,CAAC,UAACW,UAAU;IAC/B,IAAIC,WAAW,GAAG,CAAC;IACnB,IAAMC,aAAa,GAAG,SAAAA,CAAA;MACpB,IAAID,WAAW,GAAGF,WAAW,CAACD,MAAM,EAAE;QACpC,IAAIK,UAAU,SAAuB;QACrC,IAAI;UACFA,UAAU,GAAGV,SAAS,CAACM,WAAW,CAACE,WAAW,EAAE,CAAC,CAAC;SACnD,CAAC,OAAOG,GAAG,EAAE;UACZF,aAAa,EAAE;UACf;;QAEF,IAAMG,eAAe,GAAG,IAAId,kBAAkB,CAACS,UAAU,EAAEM,SAAS,EAAEd,IAAI,EAAEA,IAAI,CAAC;QACjFW,UAAU,CAACI,SAAS,CAACF,eAAe,CAAC;QACrCA,eAAe,CAACG,GAAG,CAACN,aAAa,CAAC;OACnC,MAAM;QACLF,UAAU,CAACS,QAAQ,EAAE;;IAEzB,CAAC;IACDP,aAAa,EAAE;EACjB,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}