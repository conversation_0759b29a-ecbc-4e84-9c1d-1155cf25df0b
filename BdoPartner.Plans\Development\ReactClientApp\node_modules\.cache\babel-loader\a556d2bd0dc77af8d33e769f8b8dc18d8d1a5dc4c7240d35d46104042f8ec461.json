{"ast": null, "code": "import { createErrorClass } from './createErrorClass';\nexport var ObjectUnsubscribedError = createErrorClass(function (_super) {\n  return function ObjectUnsubscribedErrorImpl() {\n    _super(this);\n    this.name = 'ObjectUnsubscribedError';\n    this.message = 'object unsubscribed';\n  };\n});", "map": {"version": 3, "names": ["createErrorClass", "ObjectUnsubscribedError", "_super", "ObjectUnsubscribedErrorImpl", "name", "message"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\util\\ObjectUnsubscribedError.ts"], "sourcesContent": ["import { createErrorClass } from './createErrorClass';\n\nexport interface ObjectUnsubscribedError extends Error {}\n\nexport interface ObjectUnsubscribedErrorCtor {\n  /**\n   * @deprecated Internal implementation detail. Do not construct error instances.\n   * Cannot be tagged as internal: https://github.com/ReactiveX/rxjs/issues/6269\n   */\n  new (): ObjectUnsubscribedError;\n}\n\n/**\n * An error thrown when an action is invalid because the object has been\n * unsubscribed.\n *\n * @see {@link Subject}\n * @see {@link BehaviorSubject}\n *\n * @class ObjectUnsubscribedError\n */\nexport const ObjectUnsubscribedError: ObjectUnsubscribedErrorCtor = createErrorClass(\n  (_super) =>\n    function ObjectUnsubscribedErrorImpl(this: any) {\n      _super(this);\n      this.name = 'ObjectUnsubscribedError';\n      this.message = 'object unsubscribed';\n    }\n);\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,oBAAoB;AAqBrD,OAAO,IAAMC,uBAAuB,GAAgCD,gBAAgB,CAClF,UAACE,MAAM;EACL,gBAASC,2BAA2BA,CAAA;IAClCD,MAAM,CAAC,IAAI,CAAC;IACZ,IAAI,CAACE,IAAI,GAAG,yBAAyB;IACrC,IAAI,CAACC,OAAO,GAAG,qBAAqB;EACtC,CAAC;AAJD,CAIC,CACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}