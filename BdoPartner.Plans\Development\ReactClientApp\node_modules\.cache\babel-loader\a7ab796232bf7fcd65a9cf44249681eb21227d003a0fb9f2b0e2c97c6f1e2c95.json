{"ast": null, "code": "import { EMPTY } from './observable/empty';\nimport { of } from './observable/of';\nimport { throwError } from './observable/throwError';\nimport { isFunction } from './util/isFunction';\nexport var NotificationKind;\n(function (NotificationKind) {\n  NotificationKind[\"NEXT\"] = \"N\";\n  NotificationKind[\"ERROR\"] = \"E\";\n  NotificationKind[\"COMPLETE\"] = \"C\";\n})(NotificationKind || (NotificationKind = {}));\nvar Notification = function () {\n  function Notification(kind, value, error) {\n    this.kind = kind;\n    this.value = value;\n    this.error = error;\n    this.hasValue = kind === 'N';\n  }\n  Notification.prototype.observe = function (observer) {\n    return observeNotification(this, observer);\n  };\n  Notification.prototype.do = function (nextHandler, errorHandler, completeHandler) {\n    var _a = this,\n      kind = _a.kind,\n      value = _a.value,\n      error = _a.error;\n    return kind === 'N' ? nextHandler === null || nextHandler === void 0 ? void 0 : nextHandler(value) : kind === 'E' ? errorHandler === null || errorHandler === void 0 ? void 0 : errorHandler(error) : completeHandler === null || completeHandler === void 0 ? void 0 : completeHandler();\n  };\n  Notification.prototype.accept = function (nextOrObserver, error, complete) {\n    var _a;\n    return isFunction((_a = nextOrObserver) === null || _a === void 0 ? void 0 : _a.next) ? this.observe(nextOrObserver) : this.do(nextOrObserver, error, complete);\n  };\n  Notification.prototype.toObservable = function () {\n    var _a = this,\n      kind = _a.kind,\n      value = _a.value,\n      error = _a.error;\n    var result = kind === 'N' ? of(value) : kind === 'E' ? throwError(function () {\n      return error;\n    }) : kind === 'C' ? EMPTY : 0;\n    if (!result) {\n      throw new TypeError(\"Unexpected notification kind \" + kind);\n    }\n    return result;\n  };\n  Notification.createNext = function (value) {\n    return new Notification('N', value);\n  };\n  Notification.createError = function (err) {\n    return new Notification('E', undefined, err);\n  };\n  Notification.createComplete = function () {\n    return Notification.completeNotification;\n  };\n  Notification.completeNotification = new Notification('C');\n  return Notification;\n}();\nexport { Notification };\nexport function observeNotification(notification, observer) {\n  var _a, _b, _c;\n  var _d = notification,\n    kind = _d.kind,\n    value = _d.value,\n    error = _d.error;\n  if (typeof kind !== 'string') {\n    throw new TypeError('Invalid notification, missing \"kind\"');\n  }\n  kind === 'N' ? (_a = observer.next) === null || _a === void 0 ? void 0 : _a.call(observer, value) : kind === 'E' ? (_b = observer.error) === null || _b === void 0 ? void 0 : _b.call(observer, error) : (_c = observer.complete) === null || _c === void 0 ? void 0 : _c.call(observer);\n}", "map": {"version": 3, "names": ["EMPTY", "of", "throwError", "isFunction", "NotificationKind", "Notification", "kind", "value", "error", "hasValue", "prototype", "observe", "observer", "observeNotification", "do", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "completeHandler", "_a", "accept", "nextOrObserver", "complete", "next", "toObservable", "result", "TypeError", "createNext", "createError", "err", "undefined", "createComplete", "completeNotification", "notification", "_d", "call", "_b", "_c"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\Notification.ts"], "sourcesContent": ["import { PartialObserver, ObservableNotification, CompleteNotification, NextNotification, ErrorNotification } from './types';\nimport { Observable } from './Observable';\nimport { EMPTY } from './observable/empty';\nimport { of } from './observable/of';\nimport { throwError } from './observable/throwError';\nimport { isFunction } from './util/isFunction';\n\n// TODO: When this enum is removed, replace it with a type alias. See #4556.\n/**\n * @deprecated Use a string literal instead. `NotificationKind` will be replaced with a type alias in v8.\n * It will not be replaced with a const enum as those are not compatible with isolated modules.\n */\nexport enum NotificationKind {\n  NEXT = 'N',\n  ERROR = 'E',\n  COMPLETE = 'C',\n}\n\n/**\n * Represents a push-based event or value that an {@link Observable} can emit.\n * This class is particularly useful for operators that manage notifications,\n * like {@link materialize}, {@link dematerialize}, {@link observeOn}, and\n * others. Besides wrapping the actual delivered value, it also annotates it\n * with metadata of, for instance, what type of push message it is (`next`,\n * `error`, or `complete`).\n *\n * @see {@link materialize}\n * @see {@link dematerialize}\n * @see {@link observeOn}\n * @deprecated It is NOT recommended to create instances of `Notification` directly.\n * Rather, try to create POJOs matching the signature outlined in {@link ObservableNotification}.\n * For example: `{ kind: 'N', value: 1 }`, `{ kind: 'E', error: new Error('bad') }`, or `{ kind: 'C' }`.\n * Will be removed in v8.\n */\nexport class Notification<T> {\n  /**\n   * A value signifying that the notification will \"next\" if observed. In truth,\n   * This is really synonymous with just checking `kind === \"N\"`.\n   * @deprecated Will be removed in v8. Instead, just check to see if the value of `kind` is `\"N\"`.\n   */\n  readonly hasValue: boolean;\n\n  /**\n   * Creates a \"Next\" notification object.\n   * @param kind Always `'N'`\n   * @param value The value to notify with if observed.\n   * @deprecated Internal implementation detail. Use {@link Notification#createNext createNext} instead.\n   */\n  constructor(kind: 'N', value?: T);\n  /**\n   * Creates an \"Error\" notification object.\n   * @param kind Always `'E'`\n   * @param value Always `undefined`\n   * @param error The error to notify with if observed.\n   * @deprecated Internal implementation detail. Use {@link Notification#createError createError} instead.\n   */\n  constructor(kind: 'E', value: undefined, error: any);\n  /**\n   * Creates a \"completion\" notification object.\n   * @param kind Always `'C'`\n   * @deprecated Internal implementation detail. Use {@link Notification#createComplete createComplete} instead.\n   */\n  constructor(kind: 'C');\n  constructor(public readonly kind: 'N' | 'E' | 'C', public readonly value?: T, public readonly error?: any) {\n    this.hasValue = kind === 'N';\n  }\n\n  /**\n   * Executes the appropriate handler on a passed `observer` given the `kind` of notification.\n   * If the handler is missing it will do nothing. Even if the notification is an error, if\n   * there is no error handler on the observer, an error will not be thrown, it will noop.\n   * @param observer The observer to notify.\n   */\n  observe(observer: PartialObserver<T>): void {\n    return observeNotification(this as ObservableNotification<T>, observer);\n  }\n\n  /**\n   * Executes a notification on the appropriate handler from a list provided.\n   * If a handler is missing for the kind of notification, nothing is called\n   * and no error is thrown, it will be a noop.\n   * @param next A next handler\n   * @param error An error handler\n   * @param complete A complete handler\n   * @deprecated Replaced with {@link Notification#observe observe}. Will be removed in v8.\n   */\n  do(next: (value: T) => void, error: (err: any) => void, complete: () => void): void;\n  /**\n   * Executes a notification on the appropriate handler from a list provided.\n   * If a handler is missing for the kind of notification, nothing is called\n   * and no error is thrown, it will be a noop.\n   * @param next A next handler\n   * @param error An error handler\n   * @deprecated Replaced with {@link Notification#observe observe}. Will be removed in v8.\n   */\n  do(next: (value: T) => void, error: (err: any) => void): void;\n  /**\n   * Executes the next handler if the Notification is of `kind` `\"N\"`. Otherwise\n   * this will not error, and it will be a noop.\n   * @param next The next handler\n   * @deprecated Replaced with {@link Notification#observe observe}. Will be removed in v8.\n   */\n  do(next: (value: T) => void): void;\n  do(nextHandler: (value: T) => void, errorHandler?: (err: any) => void, completeHandler?: () => void): void {\n    const { kind, value, error } = this;\n    return kind === 'N' ? nextHandler?.(value!) : kind === 'E' ? errorHandler?.(error) : completeHandler?.();\n  }\n\n  /**\n   * Executes a notification on the appropriate handler from a list provided.\n   * If a handler is missing for the kind of notification, nothing is called\n   * and no error is thrown, it will be a noop.\n   * @param next A next handler\n   * @param error An error handler\n   * @param complete A complete handler\n   * @deprecated Replaced with {@link Notification#observe observe}. Will be removed in v8.\n   */\n  accept(next: (value: T) => void, error: (err: any) => void, complete: () => void): void;\n  /**\n   * Executes a notification on the appropriate handler from a list provided.\n   * If a handler is missing for the kind of notification, nothing is called\n   * and no error is thrown, it will be a noop.\n   * @param next A next handler\n   * @param error An error handler\n   * @deprecated Replaced with {@link Notification#observe observe}. Will be removed in v8.\n   */\n  accept(next: (value: T) => void, error: (err: any) => void): void;\n  /**\n   * Executes the next handler if the Notification is of `kind` `\"N\"`. Otherwise\n   * this will not error, and it will be a noop.\n   * @param next The next handler\n   * @deprecated Replaced with {@link Notification#observe observe}. Will be removed in v8.\n   */\n  accept(next: (value: T) => void): void;\n\n  /**\n   * Executes the appropriate handler on a passed `observer` given the `kind` of notification.\n   * If the handler is missing it will do nothing. Even if the notification is an error, if\n   * there is no error handler on the observer, an error will not be thrown, it will noop.\n   * @param observer The observer to notify.\n   * @deprecated Replaced with {@link Notification#observe observe}. Will be removed in v8.\n   */\n  accept(observer: PartialObserver<T>): void;\n  accept(nextOrObserver: PartialObserver<T> | ((value: T) => void), error?: (err: any) => void, complete?: () => void) {\n    return isFunction((nextOrObserver as any)?.next)\n      ? this.observe(nextOrObserver as PartialObserver<T>)\n      : this.do(nextOrObserver as (value: T) => void, error as any, complete as any);\n  }\n\n  /**\n   * Returns a simple Observable that just delivers the notification represented\n   * by this Notification instance.\n   *\n   * @deprecated Will be removed in v8. To convert a `Notification` to an {@link Observable},\n   * use {@link of} and {@link dematerialize}: `of(notification).pipe(dematerialize())`.\n   */\n  toObservable(): Observable<T> {\n    const { kind, value, error } = this;\n    // Select the observable to return by `kind`\n    const result =\n      kind === 'N'\n        ? // Next kind. Return an observable of that value.\n          of(value!)\n        : //\n        kind === 'E'\n        ? // Error kind. Return an observable that emits the error.\n          throwError(() => error)\n        : //\n        kind === 'C'\n        ? // Completion kind. Kind is \"C\", return an observable that just completes.\n          EMPTY\n        : // Unknown kind, return falsy, so we error below.\n          0;\n    if (!result) {\n      // TODO: consider removing this check. The only way to cause this would be to\n      // use the Notification constructor directly in a way that is not type-safe.\n      // and direct use of the Notification constructor is deprecated.\n      throw new TypeError(`Unexpected notification kind ${kind}`);\n    }\n    return result;\n  }\n\n  private static completeNotification = new Notification('C') as Notification<never> & CompleteNotification;\n  /**\n   * A shortcut to create a Notification instance of the type `next` from a\n   * given value.\n   * @param value The `next` value.\n   * @return The \"next\" Notification representing the argument.\n   * @deprecated It is NOT recommended to create instances of `Notification` directly.\n   * Rather, try to create POJOs matching the signature outlined in {@link ObservableNotification}.\n   * For example: `{ kind: 'N', value: 1 }`, `{ kind: 'E', error: new Error('bad') }`, or `{ kind: 'C' }`.\n   * Will be removed in v8.\n   */\n  static createNext<T>(value: T) {\n    return new Notification('N', value) as Notification<T> & NextNotification<T>;\n  }\n\n  /**\n   * A shortcut to create a Notification instance of the type `error` from a\n   * given error.\n   * @param err The `error` error.\n   * @return The \"error\" Notification representing the argument.\n   * @deprecated It is NOT recommended to create instances of `Notification` directly.\n   * Rather, try to create POJOs matching the signature outlined in {@link ObservableNotification}.\n   * For example: `{ kind: 'N', value: 1 }`, `{ kind: 'E', error: new Error('bad') }`, or `{ kind: 'C' }`.\n   * Will be removed in v8.\n   */\n  static createError(err?: any) {\n    return new Notification('E', undefined, err) as Notification<never> & ErrorNotification;\n  }\n\n  /**\n   * A shortcut to create a Notification instance of the type `complete`.\n   * @return The valueless \"complete\" Notification.\n   * @deprecated It is NOT recommended to create instances of `Notification` directly.\n   * Rather, try to create POJOs matching the signature outlined in {@link ObservableNotification}.\n   * For example: `{ kind: 'N', value: 1 }`, `{ kind: 'E', error: new Error('bad') }`, or `{ kind: 'C' }`.\n   * Will be removed in v8.\n   */\n  static createComplete(): Notification<never> & CompleteNotification {\n    return Notification.completeNotification;\n  }\n}\n\n/**\n * Executes the appropriate handler on a passed `observer` given the `kind` of notification.\n * If the handler is missing it will do nothing. Even if the notification is an error, if\n * there is no error handler on the observer, an error will not be thrown, it will noop.\n * @param notification The notification object to observe.\n * @param observer The observer to notify.\n */\nexport function observeNotification<T>(notification: ObservableNotification<T>, observer: PartialObserver<T>) {\n  const { kind, value, error } = notification as any;\n  if (typeof kind !== 'string') {\n    throw new TypeError('Invalid notification, missing \"kind\"');\n  }\n  kind === 'N' ? observer.next?.(value!) : kind === 'E' ? observer.error?.(error) : observer.complete?.();\n}\n"], "mappings": "AAEA,SAASA,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,EAAE,QAAQ,iBAAiB;AACpC,SAASC,UAAU,QAAQ,yBAAyB;AACpD,SAASC,UAAU,QAAQ,mBAAmB;AAO9C,WAAYC,gBAIX;AAJD,WAAYA,gBAAgB;EAC1BA,gBAAA,cAAU;EACVA,gBAAA,eAAW;EACXA,gBAAA,kBAAc;AAChB,CAAC,EAJWA,gBAAgB,KAAhBA,gBAAgB;AAsB5B,IAAAC,YAAA;EA6BE,SAAAA,aAA4BC,IAAqB,EAAkBC,KAAS,EAAkBC,KAAW;IAA7E,KAAAF,IAAI,GAAJA,IAAI;IAAmC,KAAAC,KAAK,GAALA,KAAK;IAAsB,KAAAC,KAAK,GAALA,KAAK;IACjG,IAAI,CAACC,QAAQ,GAAGH,IAAI,KAAK,GAAG;EAC9B;EAQAD,YAAA,CAAAK,SAAA,CAAAC,OAAO,GAAP,UAAQC,QAA4B;IAClC,OAAOC,mBAAmB,CAAC,IAAiC,EAAED,QAAQ,CAAC;EACzE,CAAC;EA4BDP,YAAA,CAAAK,SAAA,CAAAI,EAAE,GAAF,UAAGC,WAA+B,EAAEC,YAAiC,EAAEC,eAA4B;IAC3F,IAAAC,EAAA,GAAyB,IAAI;MAA3BZ,IAAI,GAAAY,EAAA,CAAAZ,IAAA;MAAEC,KAAK,GAAAW,EAAA,CAAAX,KAAA;MAAEC,KAAK,GAAAU,EAAA,CAAAV,KAAS;IACnC,OAAOF,IAAI,KAAK,GAAG,GAAGS,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAGR,KAAM,CAAC,GAAGD,IAAI,KAAK,GAAG,GAAGU,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAGR,KAAK,CAAC,GAAGS,eAAe,aAAfA,eAAe,uBAAfA,eAAe,EAAI;EAC1G,CAAC;EAqCDZ,YAAA,CAAAK,SAAA,CAAAS,MAAM,GAAN,UAAOC,cAAyD,EAAEZ,KAA0B,EAAEa,QAAqB;;IACjH,OAAOlB,UAAU,CAAC,CAAAe,EAAA,GAACE,cAAsB,cAAAF,EAAA,uBAAAA,EAAA,CAAEI,IAAI,CAAC,GAC5C,IAAI,CAACX,OAAO,CAACS,cAAoC,CAAC,GAClD,IAAI,CAACN,EAAE,CAACM,cAAoC,EAAEZ,KAAY,EAAEa,QAAe,CAAC;EAClF,CAAC;EASDhB,YAAA,CAAAK,SAAA,CAAAa,YAAY,GAAZ;IACQ,IAAAL,EAAA,GAAyB,IAAI;MAA3BZ,IAAI,GAAAY,EAAA,CAAAZ,IAAA;MAAEC,KAAK,GAAAW,EAAA,CAAAX,KAAA;MAAEC,KAAK,GAAAU,EAAA,CAAAV,KAAS;IAEnC,IAAMgB,MAAM,GACVlB,IAAI,KAAK,GAAG,GAERL,EAAE,CAACM,KAAM,CAAC,GAEZD,IAAI,KAAK,GAAG,GAEVJ,UAAU,CAAC;MAAM,OAAAM,KAAK;IAAL,CAAK,CAAC,GAEzBF,IAAI,KAAK,GAAG,GAEVN,KAAK,GAEL,CAAC;IACP,IAAI,CAACwB,MAAM,EAAE;MAIX,MAAM,IAAIC,SAAS,CAAC,kCAAgCnB,IAAM,CAAC;;IAE7D,OAAOkB,MAAM;EACf,CAAC;EAaMnB,YAAA,CAAAqB,UAAU,GAAjB,UAAqBnB,KAAQ;IAC3B,OAAO,IAAIF,YAAY,CAAC,GAAG,EAAEE,KAAK,CAA0C;EAC9E,CAAC;EAYMF,YAAA,CAAAsB,WAAW,GAAlB,UAAmBC,GAAS;IAC1B,OAAO,IAAIvB,YAAY,CAAC,GAAG,EAAEwB,SAAS,EAAED,GAAG,CAA4C;EACzF,CAAC;EAUMvB,YAAA,CAAAyB,cAAc,GAArB;IACE,OAAOzB,YAAY,CAAC0B,oBAAoB;EAC1C,CAAC;EAvCc1B,YAAA,CAAA0B,oBAAoB,GAAG,IAAI1B,YAAY,CAAC,GAAG,CAA+C;EAwC3G,OAAAA,YAAC;CAAA,CA5LD;SAAaA,YAAY;AAqMzB,OAAM,SAAUQ,mBAAmBA,CAAImB,YAAuC,EAAEpB,QAA4B;;EACpG,IAAAqB,EAAA,GAAyBD,YAAmB;IAA1C1B,IAAI,GAAA2B,EAAA,CAAA3B,IAAA;IAAEC,KAAK,GAAA0B,EAAA,CAAA1B,KAAA;IAAEC,KAAK,GAAAyB,EAAA,CAAAzB,KAAwB;EAClD,IAAI,OAAOF,IAAI,KAAK,QAAQ,EAAE;IAC5B,MAAM,IAAImB,SAAS,CAAC,sCAAsC,CAAC;;EAE7DnB,IAAI,KAAK,GAAG,GAAG,CAAAY,EAAA,GAAAN,QAAQ,CAACU,IAAI,cAAAJ,EAAA,uBAAAA,EAAA,CAAAgB,IAAA,CAAbtB,QAAQ,EAAQL,KAAM,CAAC,GAAGD,IAAI,KAAK,GAAG,GAAG,CAAA6B,EAAA,GAAAvB,QAAQ,CAACJ,KAAK,cAAA2B,EAAA,uBAAAA,EAAA,CAAAD,IAAA,CAAdtB,QAAQ,EAASJ,KAAK,CAAC,GAAG,CAAA4B,EAAA,GAAAxB,QAAQ,CAACS,QAAQ,cAAAe,EAAA,uBAAAA,EAAA,CAAAF,IAAA,CAAjBtB,QAAQ,CAAa;AACzG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}