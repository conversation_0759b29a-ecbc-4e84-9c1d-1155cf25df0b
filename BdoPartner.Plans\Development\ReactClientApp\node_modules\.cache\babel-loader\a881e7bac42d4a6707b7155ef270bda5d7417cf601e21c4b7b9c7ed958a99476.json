{"ast": null, "code": "import { not } from '../util/not';\nimport { filter } from '../operators/filter';\nimport { innerFrom } from './innerFrom';\nexport function partition(source, predicate, thisArg) {\n  return [filter(predicate, thisArg)(innerFrom(source)), filter(not(predicate, thisArg))(innerFrom(source))];\n}", "map": {"version": 3, "names": ["not", "filter", "innerFrom", "partition", "source", "predicate", "thisArg"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\observable\\partition.ts"], "sourcesContent": ["import { not } from '../util/not';\nimport { filter } from '../operators/filter';\nimport { ObservableInput } from '../types';\nimport { Observable } from '../Observable';\nimport { innerFrom } from './innerFrom';\n\n/** @deprecated Use a closure instead of a `thisArg`. Signatures accepting a `thisArg` will be removed in v8. */\nexport function partition<T, U extends T, A>(\n  source: ObservableInput<T>,\n  predicate: (this: A, value: T, index: number) => value is U,\n  thisArg: A\n): [Observable<U>, Observable<Exclude<T, U>>];\nexport function partition<T, U extends T>(\n  source: ObservableInput<T>,\n  predicate: (value: T, index: number) => value is U\n): [Observable<U>, Observable<Exclude<T, U>>];\n\n/** @deprecated Use a closure instead of a `thisArg`. Signatures accepting a `thisArg` will be removed in v8. */\nexport function partition<T, A>(\n  source: ObservableInput<T>,\n  predicate: (this: A, value: T, index: number) => boolean,\n  thisArg: A\n): [Observable<T>, Observable<T>];\nexport function partition<T>(source: ObservableInput<T>, predicate: (value: T, index: number) => boolean): [Observable<T>, Observable<T>];\n\n/**\n * Splits the source Observable into two, one with values that satisfy a\n * predicate, and another with values that don't satisfy the predicate.\n *\n * <span class=\"informal\">It's like {@link filter}, but returns two Observables:\n * one like the output of {@link filter}, and the other with values that did not\n * pass the condition.</span>\n *\n * ![](partition.png)\n *\n * `partition` outputs an array with two Observables that partition the values\n * from the source Observable through the given `predicate` function. The first\n * Observable in that array emits source values for which the predicate argument\n * returns true. The second Observable emits source values for which the\n * predicate returns false. The first behaves like {@link filter} and the second\n * behaves like {@link filter} with the predicate negated.\n *\n * ## Example\n *\n * Partition a set of numbers into odds and evens observables\n *\n * ```ts\n * import { of, partition } from 'rxjs';\n *\n * const observableValues = of(1, 2, 3, 4, 5, 6);\n * const [evens$, odds$] = partition(observableValues, value => value % 2 === 0);\n *\n * odds$.subscribe(x => console.log('odds', x));\n * evens$.subscribe(x => console.log('evens', x));\n *\n * // Logs:\n * // odds 1\n * // odds 3\n * // odds 5\n * // evens 2\n * // evens 4\n * // evens 6\n * ```\n *\n * @see {@link filter}\n *\n * @param source The source `ObservableInput` that will be split into a tuple of\n * two Observable elements.\n * @param predicate A function that evaluates each value emitted by the source\n * Observable. If it returns `true`, the value is emitted on the first Observable\n * in the returned array, if `false` the value is emitted on the second Observable\n * in the array. The `index` parameter is the number `i` for the i-th source\n * emission that has happened since the subscription, starting from the number `0`.\n * @param thisArg An optional argument to determine the value of `this` in the\n * `predicate` function.\n * @return An array with two Observables: one with values that passed the\n * predicate, and another with values that did not pass the predicate.\n */\nexport function partition<T>(\n  source: ObservableInput<T>,\n  predicate: (this: any, value: T, index: number) => boolean,\n  thisArg?: any\n): [Observable<T>, Observable<T>] {\n  return [filter(predicate, thisArg)(innerFrom(source)), filter(not(predicate, thisArg))(innerFrom(source))] as [\n    Observable<T>,\n    Observable<T>\n  ];\n}\n"], "mappings": "AAAA,SAASA,GAAG,QAAQ,aAAa;AACjC,SAASC,MAAM,QAAQ,qBAAqB;AAG5C,SAASC,SAAS,QAAQ,aAAa;AA0EvC,OAAM,SAAUC,SAASA,CACvBC,MAA0B,EAC1BC,SAA0D,EAC1DC,OAAa;EAEb,OAAO,CAACL,MAAM,CAACI,SAAS,EAAEC,OAAO,CAAC,CAACJ,SAAS,CAACE,MAAM,CAAC,CAAC,EAAEH,MAAM,CAACD,GAAG,CAACK,SAAS,EAAEC,OAAO,CAAC,CAAC,CAACJ,SAAS,CAACE,MAAM,CAAC,CAAC,CAGxG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}