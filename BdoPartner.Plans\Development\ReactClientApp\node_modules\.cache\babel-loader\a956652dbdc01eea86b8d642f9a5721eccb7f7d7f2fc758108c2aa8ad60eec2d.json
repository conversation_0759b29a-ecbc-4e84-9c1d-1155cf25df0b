{"ast": null, "code": "import { mergeMap } from './mergeMap';\nexport var flatMap = mergeMap;", "map": {"version": 3, "names": ["mergeMap", "flatMap"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\flatMap.ts"], "sourcesContent": ["import { mergeMap } from './mergeMap';\n\n/**\n * @deprecated Renamed to {@link mergeMap}. Will be removed in v8.\n */\nexport const flatMap = mergeMap;\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,YAAY;AAKrC,OAAO,IAAMC,OAAO,GAAGD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}