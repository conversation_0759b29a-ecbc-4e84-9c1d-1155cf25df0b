{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createFind } from './find';\nexport function findIndex(predicate, thisArg) {\n  return operate(createFind(predicate, thisArg, 'index'));\n}", "map": {"version": 3, "names": ["operate", "createFind", "findIndex", "predicate", "thisArg"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\findIndex.ts"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { Falsy, OperatorFunction } from '../types';\nimport { operate } from '../util/lift';\nimport { createFind } from './find';\n\nexport function findIndex<T>(predicate: BooleanConstructor): OperatorFunction<T, T extends Falsy ? -1 : number>;\n/** @deprecated Use a closure instead of a `thisArg`. Signatures accepting a `thisArg` will be removed in v8. */\nexport function findIndex<T>(predicate: BooleanConstructor, thisArg: any): OperatorFunction<T, T extends Falsy ? -1 : number>;\n/** @deprecated Use a closure instead of a `thisArg`. Signatures accepting a `thisArg` will be removed in v8. */\nexport function findIndex<T, A>(\n  predicate: (this: A, value: T, index: number, source: Observable<T>) => boolean,\n  thisArg: A\n): OperatorFunction<T, number>;\nexport function findIndex<T>(predicate: (value: T, index: number, source: Observable<T>) => boolean): OperatorFunction<T, number>;\n\n/**\n * Emits only the index of the first value emitted by the source Observable that\n * meets some condition.\n *\n * <span class=\"informal\">It's like {@link find}, but emits the index of the\n * found value, not the value itself.</span>\n *\n * ![](findIndex.png)\n *\n * `findIndex` searches for the first item in the source Observable that matches\n * the specified condition embodied by the `predicate`, and returns the\n * (zero-based) index of the first occurrence in the source. Unlike\n * {@link first}, the `predicate` is required in `findIndex`, and does not emit\n * an error if a valid value is not found.\n *\n * ## Example\n *\n * Emit the index of first click that happens on a DIV element\n *\n * ```ts\n * import { fromEvent, findIndex } from 'rxjs';\n *\n * const div = document.createElement('div');\n * div.style.cssText = 'width: 200px; height: 200px; background: #09c;';\n * document.body.appendChild(div);\n *\n * const clicks = fromEvent(document, 'click');\n * const result = clicks.pipe(findIndex(ev => (<HTMLElement>ev.target).tagName === 'DIV'));\n * result.subscribe(x => console.log(x));\n * ```\n *\n * @see {@link filter}\n * @see {@link find}\n * @see {@link first}\n * @see {@link take}\n *\n * @param predicate A function called with each item to test for condition matching.\n * @param thisArg An optional argument to determine the value of `this` in the\n * `predicate` function.\n * @return A function that returns an Observable that emits the index of the\n * first item that matches the condition.\n */\nexport function findIndex<T>(\n  predicate: (value: T, index: number, source: Observable<T>) => boolean,\n  thisArg?: any\n): OperatorFunction<T, number> {\n  return operate(createFind(predicate, thisArg, 'index'));\n}\n"], "mappings": "AAEA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,UAAU,QAAQ,QAAQ;AAsDnC,OAAM,SAAUC,SAASA,CACvBC,SAAsE,EACtEC,OAAa;EAEb,OAAOJ,OAAO,CAACC,UAAU,CAACE,SAAS,EAAEC,OAAO,EAAE,OAAO,CAAC,CAAC;AACzD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}