{"ast": null, "code": "import http from \"../core/http/httpClient\";\nimport APP_CONFIG from \"../core/config/appConfig\";\nimport { ResultStatus } from \"../core/enumertions/resultStatus\";\n\n/**\r\n * Partner Reference Data Upload Service for handling partner reference data upload API calls\r\n * Provides methods to manage partner reference data uploads, validation, and processing\r\n */\nclass PartnerReferenceDataUploadService {\n  /**\r\n   * Search partner reference data uploads with filtering and pagination\r\n   * @param {number} year - Filter by year\r\n   * @param {number} status - Filter by status\r\n   * @param {number} pageIndex - Page index (0-based, default: 0)\r\n   * @param {number} pageSize - Page size (default: 20)\r\n   * @returns {Promise<Object>} Paginated list of uploads with metadata\r\n   */\n  async searchPartnerReferenceDataUploads(year = null, status = null, pageIndex = 0, pageSize = 20) {\n    try {\n      const params = new URLSearchParams();\n      if (year) params.append(\"year\", year);\n      if (status !== null && status !== undefined) params.append(\"status\", status);\n      params.append(\"pageIndex\", pageIndex);\n      params.append(\"pageSize\", pageSize);\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/partnerreferencedataupload/searchpartnerreferencedatauploads?${params.toString()}`);\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\n        return response.data.item || {\n          items: [],\n          totalCount: 0\n        };\n      } else {\n        var _response$data;\n        console.error(\"Failed to search uploads:\", (_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.message);\n        return {\n          items: [],\n          totalCount: 0\n        };\n      }\n    } catch (error) {\n      console.error(\"Error searching uploads:\", error);\n      return {\n        items: [],\n        totalCount: 0\n      };\n    }\n  }\n\n  /**\r\n   * Get all partner reference data metadata\r\n   * @returns {Promise<Array>} List of all metadata objects\r\n   */\n  async getPartnerReferenceDataMetas() {\n    try {\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/partnerreferencedataupload/getpartnerreferencedatametas`);\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\n        return response.data.item || [];\n      } else {\n        var _response$data2;\n        console.error(\"Failed to get metadata list:\", (_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : _response$data2.message);\n        return [];\n      }\n    } catch (error) {\n      console.error(\"Error getting metadata list:\", error);\n      return [];\n    }\n  }\n\n  /**\r\n   * Get partner reference data metadata by ID\r\n   * @param {string} metaId - Metadata ID (GUID)\r\n   * @returns {Promise<Object>} Metadata object with details\r\n   */\n  async getPartnerReferenceDataMetaById(metaId) {\n    try {\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/partnerreferencedataupload/getpartnerreferencedatametabyid?id=${metaId}`);\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\n        return response.data.item || null;\n      } else {\n        var _response$data3;\n        console.error(\"Failed to get metadata:\", (_response$data3 = response.data) === null || _response$data3 === void 0 ? void 0 : _response$data3.message);\n        return null;\n      }\n    } catch (error) {\n      console.error(\"Error getting metadata:\", error);\n      return null;\n    }\n  }\n\n  /**\r\n   * Get partner reference data upload details with optional filtering\r\n   * @param {string} uploadId - Upload ID (GUID)\r\n   * @param {boolean} includeValidOnly - Include only valid records\r\n   * @param {boolean} includeInvalidOnly - Include only invalid records\r\n   * @returns {Promise<Array>} List of upload details\r\n   */\n  async getPartnerReferenceDataUploadDetails(uploadId, includeValidOnly = false, includeInvalidOnly = false) {\n    try {\n      const params = new URLSearchParams();\n      params.append(\"uploadId\", uploadId);\n      if (includeValidOnly) params.append(\"includeValidOnly\", \"true\");\n      if (includeInvalidOnly) params.append(\"includeInvalidOnly\", \"true\");\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/partnerreferencedataupload/getpartnerreferencedatauploaddetails?${params.toString()}`);\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\n        return response.data.item || [];\n      } else {\n        var _response$data4;\n        console.error(\"Failed to get upload details:\", (_response$data4 = response.data) === null || _response$data4 === void 0 ? void 0 : _response$data4.message);\n        return [];\n      }\n    } catch (error) {\n      console.error(\"Error getting upload details:\", error);\n      return [];\n    }\n  }\n\n  /**\r\n   * Upload Excel or CSV file for partner reference data\r\n   * @param {File} file - Excel or CSV file\r\n   * @param {number} year - Year for the upload\r\n   * @param {number} cycle - Cycle for the upload (0=Planning, 1=Mid Year Review, 2=End Year Review)\r\n   * @returns {Promise<Object>} Upload result\r\n   */\n  async uploadFile(file, year, cycle) {\n    try {\n      const formData = new FormData();\n      formData.append(\"file\", file);\n      formData.append(\"year\", year);\n      formData.append(\"cycle\", cycle);\n      const response = await http.post(`${APP_CONFIG.apiDomain}/api/partnerreferencedataupload/uploadfile`, formData\n      // Note: Don't set Content-Type header manually for FormData\n      // The browser will set it automatically with the correct boundary\n      );\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\n        return response.data.item || {};\n      } else {\n        var _response$data5;\n        throw new Error(((_response$data5 = response.data) === null || _response$data5 === void 0 ? void 0 : _response$data5.message) || \"Upload failed\");\n      }\n    } catch (error) {\n      console.error(\"Error uploading file:\", error);\n      throw error;\n    }\n  }\n\n  /**\r\n   * Validate uploaded data\r\n   * @param {string} uploadId - Upload ID (GUID) to validate\r\n   * @returns {Promise<Object>} Validation result\r\n   */\n  async validateUpload(uploadId) {\n    try {\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/partnerreferencedataupload/validateupload?uploadId=${uploadId}`);\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\n        return response.data.item || {};\n      } else {\n        var _response$data6;\n        throw new Error(((_response$data6 = response.data) === null || _response$data6 === void 0 ? void 0 : _response$data6.message) || \"Validation failed\");\n      }\n    } catch (error) {\n      console.error(\"Error validating upload:\", error);\n      throw error;\n    }\n  }\n\n  /**\r\n   * Submit validated data to final PartnerReferenceData table\r\n   * @param {string} uploadId - Upload ID (GUID) to submit\r\n   * @param {boolean} overwriteExisting - Default true. When true, replaces existing data for the same partner/year/cycle\r\n   * @returns {Promise<Object>} Submit result\r\n   */\n  async submitUpload(uploadId, overwriteExisting = true) {\n    try {\n      const params = new URLSearchParams();\n      params.append(\"uploadId\", uploadId);\n      params.append(\"overwriteExisting\", overwriteExisting);\n      const response = await http.post(`${APP_CONFIG.apiDomain}/api/partnerreferencedataupload/submitupload?${params.toString()}`);\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\n        return response.data.item || {};\n      } else {\n        var _response$data7;\n        throw new Error(((_response$data7 = response.data) === null || _response$data7 === void 0 ? void 0 : _response$data7.message) || \"Submit failed\");\n      }\n    } catch (error) {\n      console.error(\"Error submitting upload:\", error);\n      throw error;\n    }\n  }\n\n  /**\r\n   * Delete an upload record\r\n   * @param {string} uploadId - Upload ID (GUID) to delete\r\n   * @returns {Promise<Object>} Delete result\r\n   */\n  async deleteUpload(uploadId) {\n    try {\n      const params = new URLSearchParams();\n      params.append(\"uploadId\", uploadId);\n      const response = await http.delete(`${APP_CONFIG.apiDomain}/api/partnerreferencedataupload/deleteupload?${params.toString()}`);\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\n        return response.data.item || {};\n      } else {\n        var _response$data8;\n        throw new Error(((_response$data8 = response.data) === null || _response$data8 === void 0 ? void 0 : _response$data8.message) || \"Delete failed\");\n      }\n    } catch (error) {\n      console.error(\"Error deleting upload:\", error);\n      throw error;\n    }\n  }\n\n  /**\r\n   * Get upload template file\r\n   * @returns {Promise<Blob>} Template file blob\r\n   */\n  async getUploadTemplate() {\n    try {\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/partnerreferencedataupload/getuploadtemplate`, {\n        responseType: \"blob\"\n      });\n      return response.data;\n    } catch (error) {\n      console.error(\"Error getting upload template:\", error);\n      throw error;\n    }\n  }\n\n  /**\r\n   * Search partner reference data with filtering and pagination\r\n   * @param {number} year - Filter by year\r\n   * @param {number} cycle - Filter by cycle\r\n   * @param {string} partnerId - Filter by partner ID (GUID)\r\n   * @param {number} pageIndex - Page index (0-based, default: 0)\r\n   * @param {number} pageSize - Page size (default: 20)\r\n   * @returns {Promise<Object>} Paginated list of partner reference data\r\n   */\n  async searchPartnerReferenceData(year = null, cycle = null, partnerId = null, pageIndex = 0, pageSize = 20) {\n    try {\n      const params = new URLSearchParams();\n      if (year) params.append(\"year\", year);\n      if (cycle !== null && cycle !== undefined) params.append(\"cycle\", cycle);\n      if (partnerId) params.append(\"partnerId\", partnerId);\n      params.append(\"pageIndex\", pageIndex);\n      params.append(\"pageSize\", pageSize);\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/partnerreferencedataupload/searchpartnerreferencedata?${params.toString()}`);\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\n        return response.data || {\n          item: {\n            items: [],\n            totalCount: 0\n          }\n        };\n      } else {\n        return {\n          item: {\n            items: [],\n            totalCount: 0\n          }\n        };\n      }\n    } catch (error) {\n      console.error(\"Error searching partner reference data:\", error);\n      return {\n        item: {\n          items: [],\n          totalCount: 0\n        }\n      };\n    }\n  }\n\n  /**\r\n   * Export partner reference data to Excel\r\n   * @param {number} year - Year to export\r\n   * @param {number} cycle - Cycle to export\r\n   * @returns {Promise<Blob>} Excel file blob\r\n   */\n  async exportPartnerReferenceDataToExcel(year, cycle) {\n    try {\n      const params = new URLSearchParams();\n      if (year) params.append(\"year\", year);\n      if (cycle !== null && cycle !== undefined) params.append(\"cycle\", cycle);\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/partnerreferencedataupload/exportpartnerreferencedatatoexcel?${params.toString()}`, {\n        responseType: \"blob\"\n      });\n      return response.data;\n    } catch (error) {\n      console.error(\"Error exporting partner reference data:\", error);\n      throw error;\n    }\n  }\n\n  /**\r\n   * Get available column names for Form Creator mapping based on questionnaire year\r\n   * @param {number} year - Questionnaire year to get relevant column names for\r\n   * @param {boolean} includeCyclePrefixes - Whether to include cycle prefixes for disambiguation (default: true)\r\n   * @returns {Promise<Array>} Array of column name objects with value and text properties\r\n   */\n  async getAvailableColumnNamesForMapping(year = null, includeCyclePrefixes = true) {\n    try {\n      // If year is provided, use the new backend API\n      if (year) {\n        const response = await http.get(`${APP_CONFIG.apiDomain}/api/partnerreferencedataupload/getavailablecolumnnamesforformcreator?year=${year}&includeCyclePrefixes=${includeCyclePrefixes}`);\n        if (response.data && response.data.resultStatus === ResultStatus.Success) {\n          const columnChoices = response.data.item || [];\n          console.log(`Found ${columnChoices.length} column names for mapping (Year: ${year})`);\n          return columnChoices;\n        } else {\n          console.warn(\"Failed to get column names from backend, using fallback\");\n          return [];\n        }\n      }\n      const metadataList = await this.getPartnerReferenceDataMetas();\n\n      // Extract column names from all active metadata\n      const columnNamesSet = new Set();\n      for (const meta of metadataList) {\n        if (meta.isActive && meta.partnerReferenceDataMetaDetails) {\n          for (const detail of meta.partnerReferenceDataMetaDetails) {\n            if (detail.columnName && detail.columnName.trim()) {\n              columnNamesSet.add(detail.columnName.trim());\n            }\n          }\n        }\n      }\n\n      // Convert to array and sort alphabetically\n      const columnNames = Array.from(columnNamesSet).sort((a, b) => a.localeCompare(b)).map(name => ({\n        value: name,\n        text: name\n      }));\n      console.log(`Found ${columnNames.length} column names for mapping`);\n      return columnNames;\n    } catch (error) {\n      console.error(\"Error getting column names for mapping:\", error);\n      return [];\n    }\n  }\n\n  /**\r\n   * Get partner reference data metadata by year with all cycles\r\n   * @param {number} year - Year to filter metadata\r\n   * @returns {Promise<Array>} Array of metadata objects for the specified year\r\n   */\n  async getPartnerReferenceDataMetasByYear(year) {\n    try {\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/partnerreferencedataupload/getpartnerreferencedatametasbyyear?year=${year}`);\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\n        return response.data.item || [];\n      } else {\n        var _response$data9;\n        console.error(\"Failed to get metadata by year:\", (_response$data9 = response.data) === null || _response$data9 === void 0 ? void 0 : _response$data9.message);\n        return [];\n      }\n    } catch (error) {\n      console.error(\"Error getting metadata by year:\", error);\n      return [];\n    }\n  }\n\n  /**\r\n   * Get column names by group from partner reference data metadata\r\n   * @param {number} year - Year to filter by\r\n   * @param {string} groupName - Group name to filter by\r\n   * @returns {Promise<Array>} Array of column name objects\r\n   */\n  async getColumnsByGroup(year, groupName) {\n    try {\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/form/GetColumnsByGroup?year=${year}&groupName=${encodeURIComponent(groupName)}`);\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\n        const columns = response.data.item || [];\n        console.log(`Found ${columns.length} columns for group \"${groupName}\" in year ${year}`);\n        return columns.map(column => ({\n          value: column.key,\n          // NormalizedColumnName\n          text: column.value // ColumnName\n        }));\n      } else {\n        console.warn(`Failed to get columns for group \"${groupName}\", using fallback`);\n        return [];\n      }\n    } catch (error) {\n      console.error(`Error getting columns for group \"${groupName}\":`, error);\n      return [];\n    }\n  }\n\n  /**\r\n   * Get partner reference data value for a specific column and partner\r\n   * @param {number} year - Year to filter by\r\n   * @param {number} cycle - Cycle to filter by (0=Planning, 1=Mid Year Review, 2=Year End Review)\r\n   * @param {string} groupName - Group name to filter by\r\n   * @param {string} columnName - Column name to get value for\r\n   * @param {string} partnerId - Partner ID to get data for\r\n   * @returns {Promise<string>} Partner reference data value\r\n   */\n  async getPartnerReferenceDataValue(year, cycle, groupName, columnName, partnerId) {\n    try {\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/form/GetPartnerReferenceDataValue`, {\n        params: {\n          year: year,\n          cycle: cycle,\n          groupName: groupName,\n          columnName: columnName,\n          partnerId: partnerId\n        }\n      });\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\n        return response.data.item;\n      } else {\n        console.warn(`No partner reference data found for ${columnName} in group ${groupName} for cycle ${cycle}`);\n        return null;\n      }\n    } catch (error) {\n      console.error(`Error getting partner reference data value for ${columnName} in cycle ${cycle}:`, error);\n      throw error;\n    }\n  }\n\n  /**\r\n   * Get unique group names from partner reference data metadata by year\r\n   * @param {number} year - Year to filter by\r\n   * @returns {Promise<Array>} Array of group name objects with value and text properties\r\n   */\n  async getUniqueGroupNames(year) {\n    try {\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/form/GetUniqueGroupNames?year=${year || new Date().getFullYear()}`);\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\n        const groupNames = response.data.item || [];\n        console.log(`Found ${groupNames.length} unique group names for year ${year}`);\n        console.log(\"groupNames:\", groupNames);\n        return groupNames.map(groupName => ({\n          value: groupName,\n          text: groupName\n        }));\n      } else {\n        console.warn(\"Failed to get unique group names, using fallback\");\n        // return this.getFallbackGroupNames();\n      }\n    } catch (error) {\n      console.error(\"Error getting unique group names:\", error);\n      //return this.getFallbackGroupNames();\n    }\n  }\n}\n\n// Export singleton instance\nconst partnerReferenceDataUploadService = new PartnerReferenceDataUploadService();\nexport default partnerReferenceDataUploadService;", "map": {"version": 3, "names": ["http", "APP_CONFIG", "ResultStatus", "PartnerReferenceDataUploadService", "searchPartnerReferenceDataUploads", "year", "status", "pageIndex", "pageSize", "params", "URLSearchParams", "append", "undefined", "response", "get", "apiDomain", "toString", "data", "resultStatus", "Success", "item", "items", "totalCount", "_response$data", "console", "error", "message", "getPartnerReferenceDataMetas", "_response$data2", "getPartnerReferenceDataMetaById", "metaId", "_response$data3", "getPartnerReferenceDataUploadDetails", "uploadId", "includeValidOnly", "includeInvalidOnly", "_response$data4", "uploadFile", "file", "cycle", "formData", "FormData", "post", "_response$data5", "Error", "validateUpload", "_response$data6", "submitUpload", "overwriteExisting", "_response$data7", "deleteUpload", "delete", "_response$data8", "getUploadTemplate", "responseType", "searchPartnerReferenceData", "partnerId", "exportPartnerReferenceDataToExcel", "getAvailableColumnNamesForMapping", "includeCyclePrefixes", "columnChoices", "log", "length", "warn", "metadataList", "columnNamesSet", "Set", "meta", "isActive", "partnerReferenceDataMetaDetails", "detail", "columnName", "trim", "add", "columnNames", "Array", "from", "sort", "a", "b", "localeCompare", "map", "name", "value", "text", "getPartnerReferenceDataMetasByYear", "_response$data9", "getColumnsByGroup", "groupName", "encodeURIComponent", "columns", "column", "key", "getPartnerReferenceDataValue", "getUniqueGroupNames", "Date", "getFullYear", "groupNames", "partnerReferenceDataUploadService"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/services/partnerReferenceDataUploadService.js"], "sourcesContent": ["import http from \"../core/http/httpClient\";\r\nimport APP_CONFIG from \"../core/config/appConfig\";\r\nimport { ResultStatus } from \"../core/enumertions/resultStatus\";\r\n\r\n/**\r\n * Partner Reference Data Upload Service for handling partner reference data upload API calls\r\n * Provides methods to manage partner reference data uploads, validation, and processing\r\n */\r\nclass PartnerReferenceDataUploadService {\r\n  /**\r\n   * Search partner reference data uploads with filtering and pagination\r\n   * @param {number} year - Filter by year\r\n   * @param {number} status - Filter by status\r\n   * @param {number} pageIndex - Page index (0-based, default: 0)\r\n   * @param {number} pageSize - Page size (default: 20)\r\n   * @returns {Promise<Object>} Paginated list of uploads with metadata\r\n   */\r\n  async searchPartnerReferenceDataUploads(year = null, status = null, pageIndex = 0, pageSize = 20) {\r\n    try {\r\n      const params = new URLSearchParams();\r\n      if (year) params.append(\"year\", year);\r\n      if (status !== null && status !== undefined) params.append(\"status\", status);\r\n      params.append(\"pageIndex\", pageIndex);\r\n      params.append(\"pageSize\", pageSize);\r\n\r\n      const response = await http.get(\r\n        `${APP_CONFIG.apiDomain}/api/partnerreferencedataupload/searchpartnerreferencedatauploads?${params.toString()}`\r\n      );\r\n\r\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\r\n        return response.data.item || { items: [], totalCount: 0 };\r\n      } else {\r\n        console.error(\"Failed to search uploads:\", response.data?.message);\r\n        return { items: [], totalCount: 0 };\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error searching uploads:\", error);\r\n      return { items: [], totalCount: 0 };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get all partner reference data metadata\r\n   * @returns {Promise<Array>} List of all metadata objects\r\n   */\r\n  async getPartnerReferenceDataMetas() {\r\n    try {\r\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/partnerreferencedataupload/getpartnerreferencedatametas`);\r\n\r\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\r\n        return response.data.item || [];\r\n      } else {\r\n        console.error(\"Failed to get metadata list:\", response.data?.message);\r\n        return [];\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error getting metadata list:\", error);\r\n      return [];\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get partner reference data metadata by ID\r\n   * @param {string} metaId - Metadata ID (GUID)\r\n   * @returns {Promise<Object>} Metadata object with details\r\n   */\r\n  async getPartnerReferenceDataMetaById(metaId) {\r\n    try {\r\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/partnerreferencedataupload/getpartnerreferencedatametabyid?id=${metaId}`);\r\n\r\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\r\n        return response.data.item || null;\r\n      } else {\r\n        console.error(\"Failed to get metadata:\", response.data?.message);\r\n        return null;\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error getting metadata:\", error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get partner reference data upload details with optional filtering\r\n   * @param {string} uploadId - Upload ID (GUID)\r\n   * @param {boolean} includeValidOnly - Include only valid records\r\n   * @param {boolean} includeInvalidOnly - Include only invalid records\r\n   * @returns {Promise<Array>} List of upload details\r\n   */\r\n  async getPartnerReferenceDataUploadDetails(uploadId, includeValidOnly = false, includeInvalidOnly = false) {\r\n    try {\r\n      const params = new URLSearchParams();\r\n      params.append(\"uploadId\", uploadId);\r\n      if (includeValidOnly) params.append(\"includeValidOnly\", \"true\");\r\n      if (includeInvalidOnly) params.append(\"includeInvalidOnly\", \"true\");\r\n\r\n      const response = await http.get(\r\n        `${APP_CONFIG.apiDomain}/api/partnerreferencedataupload/getpartnerreferencedatauploaddetails?${params.toString()}`\r\n      );\r\n\r\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\r\n        return response.data.item || [];\r\n      } else {\r\n        console.error(\"Failed to get upload details:\", response.data?.message);\r\n        return [];\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error getting upload details:\", error);\r\n      return [];\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Upload Excel or CSV file for partner reference data\r\n   * @param {File} file - Excel or CSV file\r\n   * @param {number} year - Year for the upload\r\n   * @param {number} cycle - Cycle for the upload (0=Planning, 1=Mid Year Review, 2=End Year Review)\r\n   * @returns {Promise<Object>} Upload result\r\n   */\r\n  async uploadFile(file, year, cycle) {\r\n    try {\r\n      const formData = new FormData();\r\n      formData.append(\"file\", file);\r\n      formData.append(\"year\", year);\r\n      formData.append(\"cycle\", cycle);\r\n\r\n      const response = await http.post(\r\n        `${APP_CONFIG.apiDomain}/api/partnerreferencedataupload/uploadfile`,\r\n        formData\r\n        // Note: Don't set Content-Type header manually for FormData\r\n        // The browser will set it automatically with the correct boundary\r\n      );\r\n\r\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\r\n        return response.data.item || {};\r\n      } else {\r\n        throw new Error(response.data?.message || \"Upload failed\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error uploading file:\", error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Validate uploaded data\r\n   * @param {string} uploadId - Upload ID (GUID) to validate\r\n   * @returns {Promise<Object>} Validation result\r\n   */\r\n  async validateUpload(uploadId) {\r\n    try {\r\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/partnerreferencedataupload/validateupload?uploadId=${uploadId}`);\r\n\r\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\r\n        return response.data.item || {};\r\n      } else {\r\n        throw new Error(response.data?.message || \"Validation failed\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error validating upload:\", error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Submit validated data to final PartnerReferenceData table\r\n   * @param {string} uploadId - Upload ID (GUID) to submit\r\n   * @param {boolean} overwriteExisting - Default true. When true, replaces existing data for the same partner/year/cycle\r\n   * @returns {Promise<Object>} Submit result\r\n   */\r\n  async submitUpload(uploadId, overwriteExisting = true) {\r\n    try {\r\n      const params = new URLSearchParams();\r\n      params.append(\"uploadId\", uploadId);\r\n      params.append(\"overwriteExisting\", overwriteExisting);\r\n\r\n      const response = await http.post(`${APP_CONFIG.apiDomain}/api/partnerreferencedataupload/submitupload?${params.toString()}`);\r\n\r\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\r\n        return response.data.item || {};\r\n      } else {\r\n        throw new Error(response.data?.message || \"Submit failed\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error submitting upload:\", error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Delete an upload record\r\n   * @param {string} uploadId - Upload ID (GUID) to delete\r\n   * @returns {Promise<Object>} Delete result\r\n   */\r\n  async deleteUpload(uploadId) {\r\n    try {\r\n      const params = new URLSearchParams();\r\n      params.append(\"uploadId\", uploadId);\r\n\r\n      const response = await http.delete(`${APP_CONFIG.apiDomain}/api/partnerreferencedataupload/deleteupload?${params.toString()}`);\r\n\r\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\r\n        return response.data.item || {};\r\n      } else {\r\n        throw new Error(response.data?.message || \"Delete failed\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error deleting upload:\", error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get upload template file\r\n   * @returns {Promise<Blob>} Template file blob\r\n   */\r\n  async getUploadTemplate() {\r\n    try {\r\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/partnerreferencedataupload/getuploadtemplate`, { responseType: \"blob\" });\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error getting upload template:\", error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Search partner reference data with filtering and pagination\r\n   * @param {number} year - Filter by year\r\n   * @param {number} cycle - Filter by cycle\r\n   * @param {string} partnerId - Filter by partner ID (GUID)\r\n   * @param {number} pageIndex - Page index (0-based, default: 0)\r\n   * @param {number} pageSize - Page size (default: 20)\r\n   * @returns {Promise<Object>} Paginated list of partner reference data\r\n   */\r\n  async searchPartnerReferenceData(year = null, cycle = null, partnerId = null, pageIndex = 0, pageSize = 20) {\r\n    try {\r\n      const params = new URLSearchParams();\r\n      if (year) params.append(\"year\", year);\r\n      if (cycle !== null && cycle !== undefined) params.append(\"cycle\", cycle);\r\n      if (partnerId) params.append(\"partnerId\", partnerId);\r\n      params.append(\"pageIndex\", pageIndex);\r\n      params.append(\"pageSize\", pageSize);\r\n\r\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/partnerreferencedataupload/searchpartnerreferencedata?${params.toString()}`);\r\n\r\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\r\n        return response.data || { item: { items: [], totalCount: 0 } };\r\n      } else {\r\n        return { item: { items: [], totalCount: 0 } };\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error searching partner reference data:\", error);\r\n      return { item: { items: [], totalCount: 0 } };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Export partner reference data to Excel\r\n   * @param {number} year - Year to export\r\n   * @param {number} cycle - Cycle to export\r\n   * @returns {Promise<Blob>} Excel file blob\r\n   */\r\n  async exportPartnerReferenceDataToExcel(year, cycle) {\r\n    try {\r\n      const params = new URLSearchParams();\r\n      if (year) params.append(\"year\", year);\r\n      if (cycle !== null && cycle !== undefined) params.append(\"cycle\", cycle);\r\n\r\n      const response = await http.get(\r\n        `${APP_CONFIG.apiDomain}/api/partnerreferencedataupload/exportpartnerreferencedatatoexcel?${params.toString()}`,\r\n        { responseType: \"blob\" }\r\n      );\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error exporting partner reference data:\", error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get available column names for Form Creator mapping based on questionnaire year\r\n   * @param {number} year - Questionnaire year to get relevant column names for\r\n   * @param {boolean} includeCyclePrefixes - Whether to include cycle prefixes for disambiguation (default: true)\r\n   * @returns {Promise<Array>} Array of column name objects with value and text properties\r\n   */\r\n  async getAvailableColumnNamesForMapping(year = null, includeCyclePrefixes = true) {\r\n    try {\r\n      // If year is provided, use the new backend API\r\n      if (year) {\r\n        const response = await http.get(\r\n          `${APP_CONFIG.apiDomain}/api/partnerreferencedataupload/getavailablecolumnnamesforformcreator?year=${year}&includeCyclePrefixes=${includeCyclePrefixes}`\r\n        );\r\n\r\n        if (response.data && response.data.resultStatus === ResultStatus.Success) {\r\n          const columnChoices = response.data.item || [];\r\n          console.log(`Found ${columnChoices.length} column names for mapping (Year: ${year})`);\r\n          return columnChoices;\r\n        } else {\r\n          console.warn(\"Failed to get column names from backend, using fallback\");\r\n          return [];\r\n        }\r\n      }\r\n\r\n      const metadataList = await this.getPartnerReferenceDataMetas();\r\n\r\n      // Extract column names from all active metadata\r\n      const columnNamesSet = new Set();\r\n\r\n      for (const meta of metadataList) {\r\n        if (meta.isActive && meta.partnerReferenceDataMetaDetails) {\r\n          for (const detail of meta.partnerReferenceDataMetaDetails) {\r\n            if (detail.columnName && detail.columnName.trim()) {\r\n              columnNamesSet.add(detail.columnName.trim());\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      // Convert to array and sort alphabetically\r\n      const columnNames = Array.from(columnNamesSet)\r\n        .sort((a, b) => a.localeCompare(b))\r\n        .map((name) => ({\r\n          value: name,\r\n          text: name,\r\n        }));\r\n\r\n      console.log(`Found ${columnNames.length} column names for mapping`);\r\n      return columnNames;\r\n    } catch (error) {\r\n      console.error(\"Error getting column names for mapping:\", error);\r\n      return [];\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get partner reference data metadata by year with all cycles\r\n   * @param {number} year - Year to filter metadata\r\n   * @returns {Promise<Array>} Array of metadata objects for the specified year\r\n   */\r\n  async getPartnerReferenceDataMetasByYear(year) {\r\n    try {\r\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/partnerreferencedataupload/getpartnerreferencedatametasbyyear?year=${year}`);\r\n\r\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\r\n        return response.data.item || [];\r\n      } else {\r\n        console.error(\"Failed to get metadata by year:\", response.data?.message);\r\n        return [];\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error getting metadata by year:\", error);\r\n      return [];\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get column names by group from partner reference data metadata\r\n   * @param {number} year - Year to filter by\r\n   * @param {string} groupName - Group name to filter by\r\n   * @returns {Promise<Array>} Array of column name objects\r\n   */\r\n  async getColumnsByGroup(year, groupName) {\r\n    try {\r\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/form/GetColumnsByGroup?year=${year}&groupName=${encodeURIComponent(groupName)}`);\r\n\r\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\r\n        const columns = response.data.item || [];\r\n        console.log(`Found ${columns.length} columns for group \"${groupName}\" in year ${year}`);\r\n        return columns.map((column) => ({\r\n          value: column.key, // NormalizedColumnName\r\n          text: column.value, // ColumnName\r\n        }));\r\n      } else {\r\n        console.warn(`Failed to get columns for group \"${groupName}\", using fallback`);\r\n        return [];\r\n      }\r\n    } catch (error) {\r\n      console.error(`Error getting columns for group \"${groupName}\":`, error);\r\n      return [];\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get partner reference data value for a specific column and partner\r\n   * @param {number} year - Year to filter by\r\n   * @param {number} cycle - Cycle to filter by (0=Planning, 1=Mid Year Review, 2=Year End Review)\r\n   * @param {string} groupName - Group name to filter by\r\n   * @param {string} columnName - Column name to get value for\r\n   * @param {string} partnerId - Partner ID to get data for\r\n   * @returns {Promise<string>} Partner reference data value\r\n   */\r\n  async getPartnerReferenceDataValue(year, cycle, groupName, columnName, partnerId) {\r\n    try {\r\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/form/GetPartnerReferenceDataValue`, {\r\n        params: {\r\n          year: year,\r\n          cycle: cycle,\r\n          groupName: groupName,\r\n          columnName: columnName,\r\n          partnerId: partnerId,\r\n        },\r\n      });\r\n\r\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\r\n        return response.data.item;\r\n      } else {\r\n        console.warn(`No partner reference data found for ${columnName} in group ${groupName} for cycle ${cycle}`);\r\n        return null;\r\n      }\r\n    } catch (error) {\r\n      console.error(`Error getting partner reference data value for ${columnName} in cycle ${cycle}:`, error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get unique group names from partner reference data metadata by year\r\n   * @param {number} year - Year to filter by\r\n   * @returns {Promise<Array>} Array of group name objects with value and text properties\r\n   */\r\n  async getUniqueGroupNames(year) {\r\n    try {\r\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/form/GetUniqueGroupNames?year=${year || new Date().getFullYear()}`);\r\n\r\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\r\n        const groupNames = response.data.item || [];\r\n        console.log(`Found ${groupNames.length} unique group names for year ${year}`);\r\n        console.log(\"groupNames:\", groupNames);\r\n        return groupNames.map((groupName) => ({\r\n          value: groupName,\r\n          text: groupName,\r\n        }));\r\n      } else {\r\n        console.warn(\"Failed to get unique group names, using fallback\");\r\n        // return this.getFallbackGroupNames();\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error getting unique group names:\", error);\r\n      //return this.getFallbackGroupNames();\r\n    }\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nconst partnerReferenceDataUploadService = new PartnerReferenceDataUploadService();\r\nexport default partnerReferenceDataUploadService;\r\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,yBAAyB;AAC1C,OAAOC,UAAU,MAAM,0BAA0B;AACjD,SAASC,YAAY,QAAQ,kCAAkC;;AAE/D;AACA;AACA;AACA;AACA,MAAMC,iCAAiC,CAAC;EACtC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,MAAMC,iCAAiCA,CAACC,IAAI,GAAG,IAAI,EAAEC,MAAM,GAAG,IAAI,EAAEC,SAAS,GAAG,CAAC,EAAEC,QAAQ,GAAG,EAAE,EAAE;IAChG,IAAI;MACF,MAAMC,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;MACpC,IAAIL,IAAI,EAAEI,MAAM,CAACE,MAAM,CAAC,MAAM,EAAEN,IAAI,CAAC;MACrC,IAAIC,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAKM,SAAS,EAAEH,MAAM,CAACE,MAAM,CAAC,QAAQ,EAAEL,MAAM,CAAC;MAC5EG,MAAM,CAACE,MAAM,CAAC,WAAW,EAAEJ,SAAS,CAAC;MACrCE,MAAM,CAACE,MAAM,CAAC,UAAU,EAAEH,QAAQ,CAAC;MAEnC,MAAMK,QAAQ,GAAG,MAAMb,IAAI,CAACc,GAAG,CAC7B,GAAGb,UAAU,CAACc,SAAS,qEAAqEN,MAAM,CAACO,QAAQ,CAAC,CAAC,EAC/G,CAAC;MAED,IAAIH,QAAQ,CAACI,IAAI,IAAIJ,QAAQ,CAACI,IAAI,CAACC,YAAY,KAAKhB,YAAY,CAACiB,OAAO,EAAE;QACxE,OAAON,QAAQ,CAACI,IAAI,CAACG,IAAI,IAAI;UAAEC,KAAK,EAAE,EAAE;UAAEC,UAAU,EAAE;QAAE,CAAC;MAC3D,CAAC,MAAM;QAAA,IAAAC,cAAA;QACLC,OAAO,CAACC,KAAK,CAAC,2BAA2B,GAAAF,cAAA,GAAEV,QAAQ,CAACI,IAAI,cAAAM,cAAA,uBAAbA,cAAA,CAAeG,OAAO,CAAC;QAClE,OAAO;UAAEL,KAAK,EAAE,EAAE;UAAEC,UAAU,EAAE;QAAE,CAAC;MACrC;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,OAAO;QAAEJ,KAAK,EAAE,EAAE;QAAEC,UAAU,EAAE;MAAE,CAAC;IACrC;EACF;;EAEA;AACF;AACA;AACA;EACE,MAAMK,4BAA4BA,CAAA,EAAG;IACnC,IAAI;MACF,MAAMd,QAAQ,GAAG,MAAMb,IAAI,CAACc,GAAG,CAAC,GAAGb,UAAU,CAACc,SAAS,8DAA8D,CAAC;MAEtH,IAAIF,QAAQ,CAACI,IAAI,IAAIJ,QAAQ,CAACI,IAAI,CAACC,YAAY,KAAKhB,YAAY,CAACiB,OAAO,EAAE;QACxE,OAAON,QAAQ,CAACI,IAAI,CAACG,IAAI,IAAI,EAAE;MACjC,CAAC,MAAM;QAAA,IAAAQ,eAAA;QACLJ,OAAO,CAACC,KAAK,CAAC,8BAA8B,GAAAG,eAAA,GAAEf,QAAQ,CAACI,IAAI,cAAAW,eAAA,uBAAbA,eAAA,CAAeF,OAAO,CAAC;QACrE,OAAO,EAAE;MACX;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,OAAO,EAAE;IACX;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAMI,+BAA+BA,CAACC,MAAM,EAAE;IAC5C,IAAI;MACF,MAAMjB,QAAQ,GAAG,MAAMb,IAAI,CAACc,GAAG,CAAC,GAAGb,UAAU,CAACc,SAAS,sEAAsEe,MAAM,EAAE,CAAC;MAEtI,IAAIjB,QAAQ,CAACI,IAAI,IAAIJ,QAAQ,CAACI,IAAI,CAACC,YAAY,KAAKhB,YAAY,CAACiB,OAAO,EAAE;QACxE,OAAON,QAAQ,CAACI,IAAI,CAACG,IAAI,IAAI,IAAI;MACnC,CAAC,MAAM;QAAA,IAAAW,eAAA;QACLP,OAAO,CAACC,KAAK,CAAC,yBAAyB,GAAAM,eAAA,GAAElB,QAAQ,CAACI,IAAI,cAAAc,eAAA,uBAAbA,eAAA,CAAeL,OAAO,CAAC;QAChE,OAAO,IAAI;MACb;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,OAAO,IAAI;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,MAAMO,oCAAoCA,CAACC,QAAQ,EAAEC,gBAAgB,GAAG,KAAK,EAAEC,kBAAkB,GAAG,KAAK,EAAE;IACzG,IAAI;MACF,MAAM1B,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;MACpCD,MAAM,CAACE,MAAM,CAAC,UAAU,EAAEsB,QAAQ,CAAC;MACnC,IAAIC,gBAAgB,EAAEzB,MAAM,CAACE,MAAM,CAAC,kBAAkB,EAAE,MAAM,CAAC;MAC/D,IAAIwB,kBAAkB,EAAE1B,MAAM,CAACE,MAAM,CAAC,oBAAoB,EAAE,MAAM,CAAC;MAEnE,MAAME,QAAQ,GAAG,MAAMb,IAAI,CAACc,GAAG,CAC7B,GAAGb,UAAU,CAACc,SAAS,wEAAwEN,MAAM,CAACO,QAAQ,CAAC,CAAC,EAClH,CAAC;MAED,IAAIH,QAAQ,CAACI,IAAI,IAAIJ,QAAQ,CAACI,IAAI,CAACC,YAAY,KAAKhB,YAAY,CAACiB,OAAO,EAAE;QACxE,OAAON,QAAQ,CAACI,IAAI,CAACG,IAAI,IAAI,EAAE;MACjC,CAAC,MAAM;QAAA,IAAAgB,eAAA;QACLZ,OAAO,CAACC,KAAK,CAAC,+BAA+B,GAAAW,eAAA,GAAEvB,QAAQ,CAACI,IAAI,cAAAmB,eAAA,uBAAbA,eAAA,CAAeV,OAAO,CAAC;QACtE,OAAO,EAAE;MACX;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,OAAO,EAAE;IACX;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,MAAMY,UAAUA,CAACC,IAAI,EAAEjC,IAAI,EAAEkC,KAAK,EAAE;IAClC,IAAI;MACF,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAAC7B,MAAM,CAAC,MAAM,EAAE2B,IAAI,CAAC;MAC7BE,QAAQ,CAAC7B,MAAM,CAAC,MAAM,EAAEN,IAAI,CAAC;MAC7BmC,QAAQ,CAAC7B,MAAM,CAAC,OAAO,EAAE4B,KAAK,CAAC;MAE/B,MAAM1B,QAAQ,GAAG,MAAMb,IAAI,CAAC0C,IAAI,CAC9B,GAAGzC,UAAU,CAACc,SAAS,4CAA4C,EACnEyB;MACA;MACA;MACF,CAAC;MAED,IAAI3B,QAAQ,CAACI,IAAI,IAAIJ,QAAQ,CAACI,IAAI,CAACC,YAAY,KAAKhB,YAAY,CAACiB,OAAO,EAAE;QACxE,OAAON,QAAQ,CAACI,IAAI,CAACG,IAAI,IAAI,CAAC,CAAC;MACjC,CAAC,MAAM;QAAA,IAAAuB,eAAA;QACL,MAAM,IAAIC,KAAK,CAAC,EAAAD,eAAA,GAAA9B,QAAQ,CAACI,IAAI,cAAA0B,eAAA,uBAAbA,eAAA,CAAejB,OAAO,KAAI,eAAe,CAAC;MAC5D;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAMoB,cAAcA,CAACZ,QAAQ,EAAE;IAC7B,IAAI;MACF,MAAMpB,QAAQ,GAAG,MAAMb,IAAI,CAACc,GAAG,CAAC,GAAGb,UAAU,CAACc,SAAS,2DAA2DkB,QAAQ,EAAE,CAAC;MAE7H,IAAIpB,QAAQ,CAACI,IAAI,IAAIJ,QAAQ,CAACI,IAAI,CAACC,YAAY,KAAKhB,YAAY,CAACiB,OAAO,EAAE;QACxE,OAAON,QAAQ,CAACI,IAAI,CAACG,IAAI,IAAI,CAAC,CAAC;MACjC,CAAC,MAAM;QAAA,IAAA0B,eAAA;QACL,MAAM,IAAIF,KAAK,CAAC,EAAAE,eAAA,GAAAjC,QAAQ,CAACI,IAAI,cAAA6B,eAAA,uBAAbA,eAAA,CAAepB,OAAO,KAAI,mBAAmB,CAAC;MAChE;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,MAAMsB,YAAYA,CAACd,QAAQ,EAAEe,iBAAiB,GAAG,IAAI,EAAE;IACrD,IAAI;MACF,MAAMvC,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;MACpCD,MAAM,CAACE,MAAM,CAAC,UAAU,EAAEsB,QAAQ,CAAC;MACnCxB,MAAM,CAACE,MAAM,CAAC,mBAAmB,EAAEqC,iBAAiB,CAAC;MAErD,MAAMnC,QAAQ,GAAG,MAAMb,IAAI,CAAC0C,IAAI,CAAC,GAAGzC,UAAU,CAACc,SAAS,gDAAgDN,MAAM,CAACO,QAAQ,CAAC,CAAC,EAAE,CAAC;MAE5H,IAAIH,QAAQ,CAACI,IAAI,IAAIJ,QAAQ,CAACI,IAAI,CAACC,YAAY,KAAKhB,YAAY,CAACiB,OAAO,EAAE;QACxE,OAAON,QAAQ,CAACI,IAAI,CAACG,IAAI,IAAI,CAAC,CAAC;MACjC,CAAC,MAAM;QAAA,IAAA6B,eAAA;QACL,MAAM,IAAIL,KAAK,CAAC,EAAAK,eAAA,GAAApC,QAAQ,CAACI,IAAI,cAAAgC,eAAA,uBAAbA,eAAA,CAAevB,OAAO,KAAI,eAAe,CAAC;MAC5D;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAMyB,YAAYA,CAACjB,QAAQ,EAAE;IAC3B,IAAI;MACF,MAAMxB,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;MACpCD,MAAM,CAACE,MAAM,CAAC,UAAU,EAAEsB,QAAQ,CAAC;MAEnC,MAAMpB,QAAQ,GAAG,MAAMb,IAAI,CAACmD,MAAM,CAAC,GAAGlD,UAAU,CAACc,SAAS,gDAAgDN,MAAM,CAACO,QAAQ,CAAC,CAAC,EAAE,CAAC;MAE9H,IAAIH,QAAQ,CAACI,IAAI,IAAIJ,QAAQ,CAACI,IAAI,CAACC,YAAY,KAAKhB,YAAY,CAACiB,OAAO,EAAE;QACxE,OAAON,QAAQ,CAACI,IAAI,CAACG,IAAI,IAAI,CAAC,CAAC;MACjC,CAAC,MAAM;QAAA,IAAAgC,eAAA;QACL,MAAM,IAAIR,KAAK,CAAC,EAAAQ,eAAA,GAAAvC,QAAQ,CAACI,IAAI,cAAAmC,eAAA,uBAAbA,eAAA,CAAe1B,OAAO,KAAI,eAAe,CAAC;MAC5D;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;EACE,MAAM4B,iBAAiBA,CAAA,EAAG;IACxB,IAAI;MACF,MAAMxC,QAAQ,GAAG,MAAMb,IAAI,CAACc,GAAG,CAAC,GAAGb,UAAU,CAACc,SAAS,mDAAmD,EAAE;QAAEuC,YAAY,EAAE;MAAO,CAAC,CAAC;MACrI,OAAOzC,QAAQ,CAACI,IAAI;IACtB,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,MAAM8B,0BAA0BA,CAAClD,IAAI,GAAG,IAAI,EAAEkC,KAAK,GAAG,IAAI,EAAEiB,SAAS,GAAG,IAAI,EAAEjD,SAAS,GAAG,CAAC,EAAEC,QAAQ,GAAG,EAAE,EAAE;IAC1G,IAAI;MACF,MAAMC,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;MACpC,IAAIL,IAAI,EAAEI,MAAM,CAACE,MAAM,CAAC,MAAM,EAAEN,IAAI,CAAC;MACrC,IAAIkC,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK3B,SAAS,EAAEH,MAAM,CAACE,MAAM,CAAC,OAAO,EAAE4B,KAAK,CAAC;MACxE,IAAIiB,SAAS,EAAE/C,MAAM,CAACE,MAAM,CAAC,WAAW,EAAE6C,SAAS,CAAC;MACpD/C,MAAM,CAACE,MAAM,CAAC,WAAW,EAAEJ,SAAS,CAAC;MACrCE,MAAM,CAACE,MAAM,CAAC,UAAU,EAAEH,QAAQ,CAAC;MAEnC,MAAMK,QAAQ,GAAG,MAAMb,IAAI,CAACc,GAAG,CAAC,GAAGb,UAAU,CAACc,SAAS,8DAA8DN,MAAM,CAACO,QAAQ,CAAC,CAAC,EAAE,CAAC;MAEzI,IAAIH,QAAQ,CAACI,IAAI,IAAIJ,QAAQ,CAACI,IAAI,CAACC,YAAY,KAAKhB,YAAY,CAACiB,OAAO,EAAE;QACxE,OAAON,QAAQ,CAACI,IAAI,IAAI;UAAEG,IAAI,EAAE;YAAEC,KAAK,EAAE,EAAE;YAAEC,UAAU,EAAE;UAAE;QAAE,CAAC;MAChE,CAAC,MAAM;QACL,OAAO;UAAEF,IAAI,EAAE;YAAEC,KAAK,EAAE,EAAE;YAAEC,UAAU,EAAE;UAAE;QAAE,CAAC;MAC/C;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAC/D,OAAO;QAAEL,IAAI,EAAE;UAAEC,KAAK,EAAE,EAAE;UAAEC,UAAU,EAAE;QAAE;MAAE,CAAC;IAC/C;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,MAAMmC,iCAAiCA,CAACpD,IAAI,EAAEkC,KAAK,EAAE;IACnD,IAAI;MACF,MAAM9B,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;MACpC,IAAIL,IAAI,EAAEI,MAAM,CAACE,MAAM,CAAC,MAAM,EAAEN,IAAI,CAAC;MACrC,IAAIkC,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK3B,SAAS,EAAEH,MAAM,CAACE,MAAM,CAAC,OAAO,EAAE4B,KAAK,CAAC;MAExE,MAAM1B,QAAQ,GAAG,MAAMb,IAAI,CAACc,GAAG,CAC7B,GAAGb,UAAU,CAACc,SAAS,qEAAqEN,MAAM,CAACO,QAAQ,CAAC,CAAC,EAAE,EAC/G;QAAEsC,YAAY,EAAE;MAAO,CACzB,CAAC;MACD,OAAOzC,QAAQ,CAACI,IAAI;IACtB,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAC/D,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,MAAMiC,iCAAiCA,CAACrD,IAAI,GAAG,IAAI,EAAEsD,oBAAoB,GAAG,IAAI,EAAE;IAChF,IAAI;MACF;MACA,IAAItD,IAAI,EAAE;QACR,MAAMQ,QAAQ,GAAG,MAAMb,IAAI,CAACc,GAAG,CAC7B,GAAGb,UAAU,CAACc,SAAS,8EAA8EV,IAAI,yBAAyBsD,oBAAoB,EACxJ,CAAC;QAED,IAAI9C,QAAQ,CAACI,IAAI,IAAIJ,QAAQ,CAACI,IAAI,CAACC,YAAY,KAAKhB,YAAY,CAACiB,OAAO,EAAE;UACxE,MAAMyC,aAAa,GAAG/C,QAAQ,CAACI,IAAI,CAACG,IAAI,IAAI,EAAE;UAC9CI,OAAO,CAACqC,GAAG,CAAC,SAASD,aAAa,CAACE,MAAM,oCAAoCzD,IAAI,GAAG,CAAC;UACrF,OAAOuD,aAAa;QACtB,CAAC,MAAM;UACLpC,OAAO,CAACuC,IAAI,CAAC,yDAAyD,CAAC;UACvE,OAAO,EAAE;QACX;MACF;MAEA,MAAMC,YAAY,GAAG,MAAM,IAAI,CAACrC,4BAA4B,CAAC,CAAC;;MAE9D;MACA,MAAMsC,cAAc,GAAG,IAAIC,GAAG,CAAC,CAAC;MAEhC,KAAK,MAAMC,IAAI,IAAIH,YAAY,EAAE;QAC/B,IAAIG,IAAI,CAACC,QAAQ,IAAID,IAAI,CAACE,+BAA+B,EAAE;UACzD,KAAK,MAAMC,MAAM,IAAIH,IAAI,CAACE,+BAA+B,EAAE;YACzD,IAAIC,MAAM,CAACC,UAAU,IAAID,MAAM,CAACC,UAAU,CAACC,IAAI,CAAC,CAAC,EAAE;cACjDP,cAAc,CAACQ,GAAG,CAACH,MAAM,CAACC,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC;YAC9C;UACF;QACF;MACF;;MAEA;MACA,MAAME,WAAW,GAAGC,KAAK,CAACC,IAAI,CAACX,cAAc,CAAC,CAC3CY,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACE,aAAa,CAACD,CAAC,CAAC,CAAC,CAClCE,GAAG,CAAEC,IAAI,KAAM;QACdC,KAAK,EAAED,IAAI;QACXE,IAAI,EAAEF;MACR,CAAC,CAAC,CAAC;MAEL1D,OAAO,CAACqC,GAAG,CAAC,SAASa,WAAW,CAACZ,MAAM,2BAA2B,CAAC;MACnE,OAAOY,WAAW;IACpB,CAAC,CAAC,OAAOjD,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAC/D,OAAO,EAAE;IACX;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAM4D,kCAAkCA,CAAChF,IAAI,EAAE;IAC7C,IAAI;MACF,MAAMQ,QAAQ,GAAG,MAAMb,IAAI,CAACc,GAAG,CAAC,GAAGb,UAAU,CAACc,SAAS,2EAA2EV,IAAI,EAAE,CAAC;MAEzI,IAAIQ,QAAQ,CAACI,IAAI,IAAIJ,QAAQ,CAACI,IAAI,CAACC,YAAY,KAAKhB,YAAY,CAACiB,OAAO,EAAE;QACxE,OAAON,QAAQ,CAACI,IAAI,CAACG,IAAI,IAAI,EAAE;MACjC,CAAC,MAAM;QAAA,IAAAkE,eAAA;QACL9D,OAAO,CAACC,KAAK,CAAC,iCAAiC,GAAA6D,eAAA,GAAEzE,QAAQ,CAACI,IAAI,cAAAqE,eAAA,uBAAbA,eAAA,CAAe5D,OAAO,CAAC;QACxE,OAAO,EAAE;MACX;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,OAAO,EAAE;IACX;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,MAAM8D,iBAAiBA,CAAClF,IAAI,EAAEmF,SAAS,EAAE;IACvC,IAAI;MACF,MAAM3E,QAAQ,GAAG,MAAMb,IAAI,CAACc,GAAG,CAAC,GAAGb,UAAU,CAACc,SAAS,oCAAoCV,IAAI,cAAcoF,kBAAkB,CAACD,SAAS,CAAC,EAAE,CAAC;MAE7I,IAAI3E,QAAQ,CAACI,IAAI,IAAIJ,QAAQ,CAACI,IAAI,CAACC,YAAY,KAAKhB,YAAY,CAACiB,OAAO,EAAE;QACxE,MAAMuE,OAAO,GAAG7E,QAAQ,CAACI,IAAI,CAACG,IAAI,IAAI,EAAE;QACxCI,OAAO,CAACqC,GAAG,CAAC,SAAS6B,OAAO,CAAC5B,MAAM,uBAAuB0B,SAAS,aAAanF,IAAI,EAAE,CAAC;QACvF,OAAOqF,OAAO,CAACT,GAAG,CAAEU,MAAM,KAAM;UAC9BR,KAAK,EAAEQ,MAAM,CAACC,GAAG;UAAE;UACnBR,IAAI,EAAEO,MAAM,CAACR,KAAK,CAAE;QACtB,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACL3D,OAAO,CAACuC,IAAI,CAAC,oCAAoCyB,SAAS,mBAAmB,CAAC;QAC9E,OAAO,EAAE;MACX;IACF,CAAC,CAAC,OAAO/D,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,oCAAoC+D,SAAS,IAAI,EAAE/D,KAAK,CAAC;MACvE,OAAO,EAAE;IACX;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,MAAMoE,4BAA4BA,CAACxF,IAAI,EAAEkC,KAAK,EAAEiD,SAAS,EAAEjB,UAAU,EAAEf,SAAS,EAAE;IAChF,IAAI;MACF,MAAM3C,QAAQ,GAAG,MAAMb,IAAI,CAACc,GAAG,CAAC,GAAGb,UAAU,CAACc,SAAS,wCAAwC,EAAE;QAC/FN,MAAM,EAAE;UACNJ,IAAI,EAAEA,IAAI;UACVkC,KAAK,EAAEA,KAAK;UACZiD,SAAS,EAAEA,SAAS;UACpBjB,UAAU,EAAEA,UAAU;UACtBf,SAAS,EAAEA;QACb;MACF,CAAC,CAAC;MAEF,IAAI3C,QAAQ,CAACI,IAAI,IAAIJ,QAAQ,CAACI,IAAI,CAACC,YAAY,KAAKhB,YAAY,CAACiB,OAAO,EAAE;QACxE,OAAON,QAAQ,CAACI,IAAI,CAACG,IAAI;MAC3B,CAAC,MAAM;QACLI,OAAO,CAACuC,IAAI,CAAC,uCAAuCQ,UAAU,aAAaiB,SAAS,cAAcjD,KAAK,EAAE,CAAC;QAC1G,OAAO,IAAI;MACb;IACF,CAAC,CAAC,OAAOd,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,kDAAkD8C,UAAU,aAAahC,KAAK,GAAG,EAAEd,KAAK,CAAC;MACvG,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAMqE,mBAAmBA,CAACzF,IAAI,EAAE;IAC9B,IAAI;MACF,MAAMQ,QAAQ,GAAG,MAAMb,IAAI,CAACc,GAAG,CAAC,GAAGb,UAAU,CAACc,SAAS,sCAAsCV,IAAI,IAAI,IAAI0F,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,EAAE,CAAC;MAEhI,IAAInF,QAAQ,CAACI,IAAI,IAAIJ,QAAQ,CAACI,IAAI,CAACC,YAAY,KAAKhB,YAAY,CAACiB,OAAO,EAAE;QACxE,MAAM8E,UAAU,GAAGpF,QAAQ,CAACI,IAAI,CAACG,IAAI,IAAI,EAAE;QAC3CI,OAAO,CAACqC,GAAG,CAAC,SAASoC,UAAU,CAACnC,MAAM,gCAAgCzD,IAAI,EAAE,CAAC;QAC7EmB,OAAO,CAACqC,GAAG,CAAC,aAAa,EAAEoC,UAAU,CAAC;QACtC,OAAOA,UAAU,CAAChB,GAAG,CAAEO,SAAS,KAAM;UACpCL,KAAK,EAAEK,SAAS;UAChBJ,IAAI,EAAEI;QACR,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACLhE,OAAO,CAACuC,IAAI,CAAC,kDAAkD,CAAC;QAChE;MACF;IACF,CAAC,CAAC,OAAOtC,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD;IACF;EACF;AACF;;AAEA;AACA,MAAMyE,iCAAiC,GAAG,IAAI/F,iCAAiC,CAAC,CAAC;AACjF,eAAe+F,iCAAiC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}