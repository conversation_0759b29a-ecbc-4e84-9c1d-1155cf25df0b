{"ast": null, "code": "'use client';\n\nimport { ObjectUtils, classNames } from 'primereact/utils';\nvar IconBase = {\n  defaultProps: {\n    __TYPE: 'IconBase',\n    className: null,\n    label: null,\n    spin: false\n  },\n  getProps: function getProps(props) {\n    return ObjectUtils.getMergedProps(props, IconBase.defaultProps);\n  },\n  getOtherProps: function getOtherProps(props) {\n    return ObjectUtils.getDiffProps(props, IconBase.defaultProps);\n  },\n  getPTI: function getPTI(props) {\n    var isLabelEmpty = ObjectUtils.isEmpty(props.label);\n    var otherProps = IconBase.getOtherProps(props);\n    var ptiProps = {\n      className: classNames('p-icon', {\n        'p-icon-spin': props.spin\n      }, props.className),\n      role: !isLabelEmpty ? 'img' : undefined,\n      'aria-label': !isLabelEmpty ? props.label : undefined,\n      'aria-hidden': props.label ? isLabelEmpty : undefined\n    };\n    return ObjectUtils.getMergedProps(otherProps, ptiProps);\n  }\n};\nexport { IconBase };", "map": {"version": 3, "names": ["ObjectUtils", "classNames", "IconBase", "defaultProps", "__TYPE", "className", "label", "spin", "getProps", "props", "getMergedProps", "getOtherProps", "getDiffProps", "getPTI", "isLabelEmpty", "isEmpty", "otherProps", "ptiProps", "role", "undefined"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/node_modules/primereact/iconbase/iconbase.esm.js"], "sourcesContent": ["'use client';\nimport { ObjectUtils, classNames } from 'primereact/utils';\n\nvar IconBase = {\n  defaultProps: {\n    __TYPE: 'IconBase',\n    className: null,\n    label: null,\n    spin: false\n  },\n  getProps: function getProps(props) {\n    return ObjectUtils.getMergedProps(props, IconBase.defaultProps);\n  },\n  getOtherProps: function getOtherProps(props) {\n    return ObjectUtils.getDiffProps(props, IconBase.defaultProps);\n  },\n  getPTI: function getPTI(props) {\n    var isLabelEmpty = ObjectUtils.isEmpty(props.label);\n    var otherProps = IconBase.getOtherProps(props);\n    var ptiProps = {\n      className: classNames('p-icon', {\n        'p-icon-spin': props.spin\n      }, props.className),\n      role: !isLabelEmpty ? 'img' : undefined,\n      'aria-label': !isLabelEmpty ? props.label : undefined,\n      'aria-hidden': props.label ? isLabelEmpty : undefined\n    };\n    return ObjectUtils.getMergedProps(otherProps, ptiProps);\n  }\n};\n\nexport { IconBase };\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,WAAW,EAAEC,UAAU,QAAQ,kBAAkB;AAE1D,IAAIC,QAAQ,GAAG;EACbC,YAAY,EAAE;IACZC,MAAM,EAAE,UAAU;IAClBC,SAAS,EAAE,IAAI;IACfC,KAAK,EAAE,IAAI;IACXC,IAAI,EAAE;EACR,CAAC;EACDC,QAAQ,EAAE,SAASA,QAAQA,CAACC,KAAK,EAAE;IACjC,OAAOT,WAAW,CAACU,cAAc,CAACD,KAAK,EAAEP,QAAQ,CAACC,YAAY,CAAC;EACjE,CAAC;EACDQ,aAAa,EAAE,SAASA,aAAaA,CAACF,KAAK,EAAE;IAC3C,OAAOT,WAAW,CAACY,YAAY,CAACH,KAAK,EAAEP,QAAQ,CAACC,YAAY,CAAC;EAC/D,CAAC;EACDU,MAAM,EAAE,SAASA,MAAMA,CAACJ,KAAK,EAAE;IAC7B,IAAIK,YAAY,GAAGd,WAAW,CAACe,OAAO,CAACN,KAAK,CAACH,KAAK,CAAC;IACnD,IAAIU,UAAU,GAAGd,QAAQ,CAACS,aAAa,CAACF,KAAK,CAAC;IAC9C,IAAIQ,QAAQ,GAAG;MACbZ,SAAS,EAAEJ,UAAU,CAAC,QAAQ,EAAE;QAC9B,aAAa,EAAEQ,KAAK,CAACF;MACvB,CAAC,EAAEE,KAAK,CAACJ,SAAS,CAAC;MACnBa,IAAI,EAAE,CAACJ,YAAY,GAAG,KAAK,GAAGK,SAAS;MACvC,YAAY,EAAE,CAACL,YAAY,GAAGL,KAAK,CAACH,KAAK,GAAGa,SAAS;MACrD,aAAa,EAAEV,KAAK,CAACH,KAAK,GAAGQ,YAAY,GAAGK;IAC9C,CAAC;IACD,OAAOnB,WAAW,CAACU,cAAc,CAACM,UAAU,EAAEC,QAAQ,CAAC;EACzD;AACF,CAAC;AAED,SAASf,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}