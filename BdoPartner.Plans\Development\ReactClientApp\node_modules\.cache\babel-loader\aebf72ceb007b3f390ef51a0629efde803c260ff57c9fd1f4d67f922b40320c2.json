{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PrimeReact, { PrimeReactContext } from 'primereact/api';\nimport { useMergeProps, useStyle, useMountEffect, useUpdateEffect, useUnmountEffect } from 'primereact/hooks';\nimport { ObjectUtils, class<PERSON><PERSON>s, DomHandler } from 'primereact/utils';\nimport { ComponentBase } from 'primereact/componentbase';\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nfunction toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction toPropertyKey(t) {\n  var i = toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nfunction _defineProperty(e, r, t) {\n  return (r = toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _arrayWithHoles(r) {\n  if (Array.isArray(r)) return r;\n}\nfunction _iterableToArrayLimit(r, l) {\n  var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"];\n  if (null != t) {\n    var e,\n      n,\n      i,\n      u,\n      a = [],\n      f = !0,\n      o = !1;\n    try {\n      if (i = (t = t.call(r)).next, 0 === l) {\n        if (Object(t) !== t) return;\n        f = !1;\n      } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0);\n    } catch (r) {\n      o = !0, n = r;\n    } finally {\n      try {\n        if (!f && null != t[\"return\"] && (u = t[\"return\"](), Object(u) !== u)) return;\n      } finally {\n        if (o) throw n;\n      }\n    }\n    return a;\n  }\n}\nfunction _arrayLikeToArray(r, a) {\n  (null == a || a > r.length) && (a = r.length);\n  for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e];\n  return n;\n}\nfunction _unsupportedIterableToArray(r, a) {\n  if (r) {\n    if (\"string\" == typeof r) return _arrayLikeToArray(r, a);\n    var t = {}.toString.call(r).slice(8, -1);\n    return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0;\n  }\n}\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _slicedToArray(r, e) {\n  return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest();\n}\nvar styles = \"\\n@layer primereact {\\n    .p-ripple {\\n        overflow: hidden;\\n        position: relative;\\n    }\\n    \\n    .p-ink {\\n        display: block;\\n        position: absolute;\\n        background: rgba(255, 255, 255, 0.5);\\n        border-radius: 100%;\\n        transform: scale(0);\\n    }\\n    \\n    .p-ink-active {\\n        animation: ripple 0.4s linear;\\n    }\\n    \\n    .p-ripple-disabled .p-ink {\\n        display: none;\\n    }\\n}\\n\\n@keyframes ripple {\\n    100% {\\n        opacity: 0;\\n        transform: scale(2.5);\\n    }\\n}\\n\\n\";\nvar classes = {\n  root: 'p-ink'\n};\nvar RippleBase = ComponentBase.extend({\n  defaultProps: {\n    __TYPE: 'Ripple',\n    children: undefined\n  },\n  css: {\n    styles: styles,\n    classes: classes\n  },\n  getProps: function getProps(props) {\n    return ObjectUtils.getMergedProps(props, RippleBase.defaultProps);\n  },\n  getOtherProps: function getOtherProps(props) {\n    return ObjectUtils.getDiffProps(props, RippleBase.defaultProps);\n  }\n});\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nvar Ripple = /*#__PURE__*/React.memo(/*#__PURE__*/React.forwardRef(function (inProps, ref) {\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    isMounted = _React$useState2[0],\n    setMounted = _React$useState2[1];\n  var inkRef = React.useRef(null);\n  var targetRef = React.useRef(null);\n  var mergeProps = useMergeProps();\n  var context = React.useContext(PrimeReactContext);\n  var props = RippleBase.getProps(inProps, context);\n  var isRippleActive = context && context.ripple || PrimeReact.ripple;\n  var metaData = {\n    props: props\n  };\n  useStyle(RippleBase.css.styles, {\n    name: 'ripple',\n    manual: !isRippleActive\n  });\n  var _RippleBase$setMetaDa = RippleBase.setMetaData(_objectSpread({}, metaData)),\n    ptm = _RippleBase$setMetaDa.ptm,\n    cx = _RippleBase$setMetaDa.cx;\n  var getTarget = function getTarget() {\n    return inkRef.current && inkRef.current.parentElement;\n  };\n  var bindEvents = function bindEvents() {\n    if (targetRef.current) {\n      targetRef.current.addEventListener('pointerdown', onPointerDown);\n    }\n  };\n  var unbindEvents = function unbindEvents() {\n    if (targetRef.current) {\n      targetRef.current.removeEventListener('pointerdown', onPointerDown);\n    }\n  };\n  var onPointerDown = function onPointerDown(event) {\n    var offset = DomHandler.getOffset(targetRef.current);\n    var offsetX = event.pageX - offset.left + document.body.scrollTop - DomHandler.getWidth(inkRef.current) / 2;\n    var offsetY = event.pageY - offset.top + document.body.scrollLeft - DomHandler.getHeight(inkRef.current) / 2;\n    activateRipple(offsetX, offsetY);\n  };\n  var activateRipple = function activateRipple(offsetX, offsetY) {\n    if (!inkRef.current || getComputedStyle(inkRef.current, null).display === 'none') {\n      return;\n    }\n    DomHandler.removeClass(inkRef.current, 'p-ink-active');\n    setDimensions();\n    inkRef.current.style.top = offsetY + 'px';\n    inkRef.current.style.left = offsetX + 'px';\n    DomHandler.addClass(inkRef.current, 'p-ink-active');\n  };\n  var onAnimationEnd = function onAnimationEnd(event) {\n    DomHandler.removeClass(event.currentTarget, 'p-ink-active');\n  };\n  var setDimensions = function setDimensions() {\n    if (inkRef.current && !DomHandler.getHeight(inkRef.current) && !DomHandler.getWidth(inkRef.current)) {\n      var d = Math.max(DomHandler.getOuterWidth(targetRef.current), DomHandler.getOuterHeight(targetRef.current));\n      inkRef.current.style.height = d + 'px';\n      inkRef.current.style.width = d + 'px';\n    }\n  };\n  React.useImperativeHandle(ref, function () {\n    return {\n      props: props,\n      getInk: function getInk() {\n        return inkRef.current;\n      },\n      getTarget: function getTarget() {\n        return targetRef.current;\n      }\n    };\n  });\n  useMountEffect(function () {\n    // for App Router in Next.js ^14\n    setMounted(true);\n  });\n  useUpdateEffect(function () {\n    if (isMounted && inkRef.current) {\n      targetRef.current = getTarget();\n      setDimensions();\n      bindEvents();\n    }\n  }, [isMounted]);\n  useUpdateEffect(function () {\n    if (inkRef.current && !targetRef.current) {\n      targetRef.current = getTarget();\n      setDimensions();\n      bindEvents();\n    }\n  });\n  useUnmountEffect(function () {\n    if (inkRef.current) {\n      targetRef.current = null;\n      unbindEvents();\n    }\n  });\n  if (!isRippleActive) {\n    return null;\n  }\n  var rootProps = mergeProps({\n    'aria-hidden': true,\n    className: classNames(cx('root'))\n  }, RippleBase.getOtherProps(props), ptm('root'));\n  return /*#__PURE__*/React.createElement(\"span\", _extends({\n    role: \"presentation\",\n    ref: inkRef\n  }, rootProps, {\n    onAnimationEnd: onAnimationEnd\n  }));\n}));\nRipple.displayName = 'Ripple';\nexport { Ripple };", "map": {"version": 3, "names": ["React", "PrimeReact", "PrimeReactContext", "useMergeProps", "useStyle", "useMountEffect", "useUpdateEffect", "useUnmountEffect", "ObjectUtils", "classNames", "<PERSON><PERSON><PERSON><PERSON>", "ComponentBase", "_extends", "Object", "assign", "bind", "n", "e", "arguments", "length", "t", "r", "hasOwnProperty", "call", "apply", "_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "toPrimitive", "i", "TypeError", "String", "Number", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_defineProperty", "defineProperty", "value", "enumerable", "configurable", "writable", "_arrayWithHoles", "Array", "isArray", "_iterableToArrayLimit", "l", "u", "a", "f", "next", "done", "push", "_arrayLikeToArray", "_unsupportedIterableToArray", "toString", "slice", "name", "from", "test", "_nonIterableRest", "_slicedToArray", "styles", "classes", "root", "RippleBase", "extend", "defaultProps", "__TYPE", "children", "undefined", "css", "getProps", "props", "getMergedProps", "getOtherProps", "getDiffProps", "ownKeys", "keys", "getOwnPropertySymbols", "filter", "getOwnPropertyDescriptor", "_objectSpread", "for<PERSON>ach", "getOwnPropertyDescriptors", "defineProperties", "<PERSON><PERSON><PERSON>", "memo", "forwardRef", "inProps", "ref", "_React$useState", "useState", "_React$useState2", "isMounted", "setMounted", "inkRef", "useRef", "targetRef", "mergeProps", "context", "useContext", "isRippleActive", "ripple", "metaData", "manual", "_RippleBase$setMetaDa", "setMetaData", "ptm", "cx", "get<PERSON><PERSON><PERSON>", "current", "parentElement", "bindEvents", "addEventListener", "onPointerDown", "unbindEvents", "removeEventListener", "event", "offset", "getOffset", "offsetX", "pageX", "left", "document", "body", "scrollTop", "getWidth", "offsetY", "pageY", "top", "scrollLeft", "getHeight", "activateRipple", "getComputedStyle", "display", "removeClass", "setDimensions", "style", "addClass", "onAnimationEnd", "currentTarget", "d", "Math", "max", "getOuterWidth", "getOuterHeight", "height", "width", "useImperativeHandle", "getInk", "rootProps", "className", "createElement", "role", "displayName"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/node_modules/primereact/ripple/ripple.esm.js"], "sourcesContent": ["'use client';\nimport * as React from 'react';\nimport PrimeReact, { PrimeReactContext } from 'primereact/api';\nimport { useMergeProps, useStyle, useMountEffect, useUpdateEffect, useUnmountEffect } from 'primereact/hooks';\nimport { ObjectUtils, class<PERSON><PERSON>s, DomHandler } from 'primereact/utils';\nimport { ComponentBase } from 'primereact/componentbase';\n\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\n\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\n\nfunction toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\n\nfunction toPropertyKey(t) {\n  var i = toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\n\nfunction _defineProperty(e, r, t) {\n  return (r = toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\n\nfunction _arrayWithHoles(r) {\n  if (Array.isArray(r)) return r;\n}\n\nfunction _iterableToArrayLimit(r, l) {\n  var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"];\n  if (null != t) {\n    var e,\n      n,\n      i,\n      u,\n      a = [],\n      f = !0,\n      o = !1;\n    try {\n      if (i = (t = t.call(r)).next, 0 === l) {\n        if (Object(t) !== t) return;\n        f = !1;\n      } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0);\n    } catch (r) {\n      o = !0, n = r;\n    } finally {\n      try {\n        if (!f && null != t[\"return\"] && (u = t[\"return\"](), Object(u) !== u)) return;\n      } finally {\n        if (o) throw n;\n      }\n    }\n    return a;\n  }\n}\n\nfunction _arrayLikeToArray(r, a) {\n  (null == a || a > r.length) && (a = r.length);\n  for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e];\n  return n;\n}\n\nfunction _unsupportedIterableToArray(r, a) {\n  if (r) {\n    if (\"string\" == typeof r) return _arrayLikeToArray(r, a);\n    var t = {}.toString.call(r).slice(8, -1);\n    return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0;\n  }\n}\n\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nfunction _slicedToArray(r, e) {\n  return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest();\n}\n\nvar styles = \"\\n@layer primereact {\\n    .p-ripple {\\n        overflow: hidden;\\n        position: relative;\\n    }\\n    \\n    .p-ink {\\n        display: block;\\n        position: absolute;\\n        background: rgba(255, 255, 255, 0.5);\\n        border-radius: 100%;\\n        transform: scale(0);\\n    }\\n    \\n    .p-ink-active {\\n        animation: ripple 0.4s linear;\\n    }\\n    \\n    .p-ripple-disabled .p-ink {\\n        display: none;\\n    }\\n}\\n\\n@keyframes ripple {\\n    100% {\\n        opacity: 0;\\n        transform: scale(2.5);\\n    }\\n}\\n\\n\";\nvar classes = {\n  root: 'p-ink'\n};\nvar RippleBase = ComponentBase.extend({\n  defaultProps: {\n    __TYPE: 'Ripple',\n    children: undefined\n  },\n  css: {\n    styles: styles,\n    classes: classes\n  },\n  getProps: function getProps(props) {\n    return ObjectUtils.getMergedProps(props, RippleBase.defaultProps);\n  },\n  getOtherProps: function getOtherProps(props) {\n    return ObjectUtils.getDiffProps(props, RippleBase.defaultProps);\n  }\n});\n\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nvar Ripple = /*#__PURE__*/React.memo(/*#__PURE__*/React.forwardRef(function (inProps, ref) {\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    isMounted = _React$useState2[0],\n    setMounted = _React$useState2[1];\n  var inkRef = React.useRef(null);\n  var targetRef = React.useRef(null);\n  var mergeProps = useMergeProps();\n  var context = React.useContext(PrimeReactContext);\n  var props = RippleBase.getProps(inProps, context);\n  var isRippleActive = context && context.ripple || PrimeReact.ripple;\n  var metaData = {\n    props: props\n  };\n  useStyle(RippleBase.css.styles, {\n    name: 'ripple',\n    manual: !isRippleActive\n  });\n  var _RippleBase$setMetaDa = RippleBase.setMetaData(_objectSpread({}, metaData)),\n    ptm = _RippleBase$setMetaDa.ptm,\n    cx = _RippleBase$setMetaDa.cx;\n  var getTarget = function getTarget() {\n    return inkRef.current && inkRef.current.parentElement;\n  };\n  var bindEvents = function bindEvents() {\n    if (targetRef.current) {\n      targetRef.current.addEventListener('pointerdown', onPointerDown);\n    }\n  };\n  var unbindEvents = function unbindEvents() {\n    if (targetRef.current) {\n      targetRef.current.removeEventListener('pointerdown', onPointerDown);\n    }\n  };\n  var onPointerDown = function onPointerDown(event) {\n    var offset = DomHandler.getOffset(targetRef.current);\n    var offsetX = event.pageX - offset.left + document.body.scrollTop - DomHandler.getWidth(inkRef.current) / 2;\n    var offsetY = event.pageY - offset.top + document.body.scrollLeft - DomHandler.getHeight(inkRef.current) / 2;\n    activateRipple(offsetX, offsetY);\n  };\n  var activateRipple = function activateRipple(offsetX, offsetY) {\n    if (!inkRef.current || getComputedStyle(inkRef.current, null).display === 'none') {\n      return;\n    }\n    DomHandler.removeClass(inkRef.current, 'p-ink-active');\n    setDimensions();\n    inkRef.current.style.top = offsetY + 'px';\n    inkRef.current.style.left = offsetX + 'px';\n    DomHandler.addClass(inkRef.current, 'p-ink-active');\n  };\n  var onAnimationEnd = function onAnimationEnd(event) {\n    DomHandler.removeClass(event.currentTarget, 'p-ink-active');\n  };\n  var setDimensions = function setDimensions() {\n    if (inkRef.current && !DomHandler.getHeight(inkRef.current) && !DomHandler.getWidth(inkRef.current)) {\n      var d = Math.max(DomHandler.getOuterWidth(targetRef.current), DomHandler.getOuterHeight(targetRef.current));\n      inkRef.current.style.height = d + 'px';\n      inkRef.current.style.width = d + 'px';\n    }\n  };\n  React.useImperativeHandle(ref, function () {\n    return {\n      props: props,\n      getInk: function getInk() {\n        return inkRef.current;\n      },\n      getTarget: function getTarget() {\n        return targetRef.current;\n      }\n    };\n  });\n  useMountEffect(function () {\n    // for App Router in Next.js ^14\n    setMounted(true);\n  });\n  useUpdateEffect(function () {\n    if (isMounted && inkRef.current) {\n      targetRef.current = getTarget();\n      setDimensions();\n      bindEvents();\n    }\n  }, [isMounted]);\n  useUpdateEffect(function () {\n    if (inkRef.current && !targetRef.current) {\n      targetRef.current = getTarget();\n      setDimensions();\n      bindEvents();\n    }\n  });\n  useUnmountEffect(function () {\n    if (inkRef.current) {\n      targetRef.current = null;\n      unbindEvents();\n    }\n  });\n  if (!isRippleActive) {\n    return null;\n  }\n  var rootProps = mergeProps({\n    'aria-hidden': true,\n    className: classNames(cx('root'))\n  }, RippleBase.getOtherProps(props), ptm('root'));\n  return /*#__PURE__*/React.createElement(\"span\", _extends({\n    role: \"presentation\",\n    ref: inkRef\n  }, rootProps, {\n    onAnimationEnd: onAnimationEnd\n  }));\n}));\nRipple.displayName = 'Ripple';\n\nexport { Ripple };\n"], "mappings": "AAAA,YAAY;;AACZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,IAAIC,iBAAiB,QAAQ,gBAAgB;AAC9D,SAASC,aAAa,EAAEC,QAAQ,EAAEC,cAAc,EAAEC,eAAe,EAAEC,gBAAgB,QAAQ,kBAAkB;AAC7G,SAASC,WAAW,EAAEC,UAAU,EAAEC,UAAU,QAAQ,kBAAkB;AACtE,SAASC,aAAa,QAAQ,0BAA0B;AAExD,SAASC,QAAQA,CAAA,EAAG;EAClB,OAAOA,QAAQ,GAAGC,MAAM,CAACC,MAAM,GAAGD,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUC,CAAC,EAAE;IACpE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MACzC,IAAIG,CAAC,GAAGF,SAAS,CAACD,CAAC,CAAC;MACpB,KAAK,IAAII,CAAC,IAAID,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEE,cAAc,CAACC,IAAI,CAACH,CAAC,EAAEC,CAAC,CAAC,KAAKL,CAAC,CAACK,CAAC,CAAC,GAAGD,CAAC,CAACC,CAAC,CAAC,CAAC;IAClE;IACA,OAAOL,CAAC;EACV,CAAC,EAAEJ,QAAQ,CAACY,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;AACpC;AAEA,SAASO,OAAOA,CAACC,CAAC,EAAE;EAClB,yBAAyB;;EAEzB,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,CAAC,EAAE;IAChG,OAAO,OAAOA,CAAC;EACjB,CAAC,GAAG,UAAUA,CAAC,EAAE;IACf,OAAOA,CAAC,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,CAAC,CAACG,WAAW,KAAKF,MAAM,IAAID,CAAC,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,CAAC;EACrH,CAAC,EAAED,OAAO,CAACC,CAAC,CAAC;AACf;AAEA,SAASK,WAAWA,CAACX,CAAC,EAAEC,CAAC,EAAE;EACzB,IAAI,QAAQ,IAAII,OAAO,CAACL,CAAC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAC1C,IAAIH,CAAC,GAAGG,CAAC,CAACO,MAAM,CAACI,WAAW,CAAC;EAC7B,IAAI,KAAK,CAAC,KAAKd,CAAC,EAAE;IAChB,IAAIe,CAAC,GAAGf,CAAC,CAACM,IAAI,CAACH,CAAC,EAAEC,CAAC,IAAI,SAAS,CAAC;IACjC,IAAI,QAAQ,IAAII,OAAO,CAACO,CAAC,CAAC,EAAE,OAAOA,CAAC;IACpC,MAAM,IAAIC,SAAS,CAAC,8CAA8C,CAAC;EACrE;EACA,OAAO,CAAC,QAAQ,KAAKZ,CAAC,GAAGa,MAAM,GAAGC,MAAM,EAAEf,CAAC,CAAC;AAC9C;AAEA,SAASgB,aAAaA,CAAChB,CAAC,EAAE;EACxB,IAAIY,CAAC,GAAGD,WAAW,CAACX,CAAC,EAAE,QAAQ,CAAC;EAChC,OAAO,QAAQ,IAAIK,OAAO,CAACO,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAC5C;AAEA,SAASK,eAAeA,CAACpB,CAAC,EAAEI,CAAC,EAAED,CAAC,EAAE;EAChC,OAAO,CAACC,CAAC,GAAGe,aAAa,CAACf,CAAC,CAAC,KAAKJ,CAAC,GAAGJ,MAAM,CAACyB,cAAc,CAACrB,CAAC,EAAEI,CAAC,EAAE;IAC/DkB,KAAK,EAAEnB,CAAC;IACRoB,UAAU,EAAE,CAAC,CAAC;IACdC,YAAY,EAAE,CAAC,CAAC;IAChBC,QAAQ,EAAE,CAAC;EACb,CAAC,CAAC,GAAGzB,CAAC,CAACI,CAAC,CAAC,GAAGD,CAAC,EAAEH,CAAC;AAClB;AAEA,SAAS0B,eAAeA,CAACtB,CAAC,EAAE;EAC1B,IAAIuB,KAAK,CAACC,OAAO,CAACxB,CAAC,CAAC,EAAE,OAAOA,CAAC;AAChC;AAEA,SAASyB,qBAAqBA,CAACzB,CAAC,EAAE0B,CAAC,EAAE;EACnC,IAAI3B,CAAC,GAAG,IAAI,IAAIC,CAAC,GAAG,IAAI,GAAG,WAAW,IAAI,OAAOM,MAAM,IAAIN,CAAC,CAACM,MAAM,CAACC,QAAQ,CAAC,IAAIP,CAAC,CAAC,YAAY,CAAC;EAChG,IAAI,IAAI,IAAID,CAAC,EAAE;IACb,IAAIH,CAAC;MACHD,CAAC;MACDgB,CAAC;MACDgB,CAAC;MACDC,CAAC,GAAG,EAAE;MACNC,CAAC,GAAG,CAAC,CAAC;MACNxB,CAAC,GAAG,CAAC,CAAC;IACR,IAAI;MACF,IAAIM,CAAC,GAAG,CAACZ,CAAC,GAAGA,CAAC,CAACG,IAAI,CAACF,CAAC,CAAC,EAAE8B,IAAI,EAAE,CAAC,KAAKJ,CAAC,EAAE;QACrC,IAAIlC,MAAM,CAACO,CAAC,CAAC,KAAKA,CAAC,EAAE;QACrB8B,CAAC,GAAG,CAAC,CAAC;MACR,CAAC,MAAM,OAAO,EAAEA,CAAC,GAAG,CAACjC,CAAC,GAAGe,CAAC,CAACT,IAAI,CAACH,CAAC,CAAC,EAAEgC,IAAI,CAAC,KAAKH,CAAC,CAACI,IAAI,CAACpC,CAAC,CAACsB,KAAK,CAAC,EAAEU,CAAC,CAAC9B,MAAM,KAAK4B,CAAC,CAAC,EAAEG,CAAC,GAAG,CAAC,CAAC,CAAC;IACzF,CAAC,CAAC,OAAO7B,CAAC,EAAE;MACVK,CAAC,GAAG,CAAC,CAAC,EAAEV,CAAC,GAAGK,CAAC;IACf,CAAC,SAAS;MACR,IAAI;QACF,IAAI,CAAC6B,CAAC,IAAI,IAAI,IAAI9B,CAAC,CAAC,QAAQ,CAAC,KAAK4B,CAAC,GAAG5B,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAEP,MAAM,CAACmC,CAAC,CAAC,KAAKA,CAAC,CAAC,EAAE;MACzE,CAAC,SAAS;QACR,IAAItB,CAAC,EAAE,MAAMV,CAAC;MAChB;IACF;IACA,OAAOiC,CAAC;EACV;AACF;AAEA,SAASK,iBAAiBA,CAACjC,CAAC,EAAE4B,CAAC,EAAE;EAC/B,CAAC,IAAI,IAAIA,CAAC,IAAIA,CAAC,GAAG5B,CAAC,CAACF,MAAM,MAAM8B,CAAC,GAAG5B,CAAC,CAACF,MAAM,CAAC;EAC7C,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAED,CAAC,GAAG4B,KAAK,CAACK,CAAC,CAAC,EAAEhC,CAAC,GAAGgC,CAAC,EAAEhC,CAAC,EAAE,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGI,CAAC,CAACJ,CAAC,CAAC;EACrD,OAAOD,CAAC;AACV;AAEA,SAASuC,2BAA2BA,CAAClC,CAAC,EAAE4B,CAAC,EAAE;EACzC,IAAI5B,CAAC,EAAE;IACL,IAAI,QAAQ,IAAI,OAAOA,CAAC,EAAE,OAAOiC,iBAAiB,CAACjC,CAAC,EAAE4B,CAAC,CAAC;IACxD,IAAI7B,CAAC,GAAG,CAAC,CAAC,CAACoC,QAAQ,CAACjC,IAAI,CAACF,CAAC,CAAC,CAACoC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACxC,OAAO,QAAQ,KAAKrC,CAAC,IAAIC,CAAC,CAACQ,WAAW,KAAKT,CAAC,GAAGC,CAAC,CAACQ,WAAW,CAAC6B,IAAI,CAAC,EAAE,KAAK,KAAKtC,CAAC,IAAI,KAAK,KAAKA,CAAC,GAAGwB,KAAK,CAACe,IAAI,CAACtC,CAAC,CAAC,GAAG,WAAW,KAAKD,CAAC,IAAI,0CAA0C,CAACwC,IAAI,CAACxC,CAAC,CAAC,GAAGkC,iBAAiB,CAACjC,CAAC,EAAE4B,CAAC,CAAC,GAAG,KAAK,CAAC;EAC7N;AACF;AAEA,SAASY,gBAAgBA,CAAA,EAAG;EAC1B,MAAM,IAAI5B,SAAS,CAAC,2IAA2I,CAAC;AAClK;AAEA,SAAS6B,cAAcA,CAACzC,CAAC,EAAEJ,CAAC,EAAE;EAC5B,OAAO0B,eAAe,CAACtB,CAAC,CAAC,IAAIyB,qBAAqB,CAACzB,CAAC,EAAEJ,CAAC,CAAC,IAAIsC,2BAA2B,CAAClC,CAAC,EAAEJ,CAAC,CAAC,IAAI4C,gBAAgB,CAAC,CAAC;AACrH;AAEA,IAAIE,MAAM,GAAG,4hBAA4hB;AACziB,IAAIC,OAAO,GAAG;EACZC,IAAI,EAAE;AACR,CAAC;AACD,IAAIC,UAAU,GAAGvD,aAAa,CAACwD,MAAM,CAAC;EACpCC,YAAY,EAAE;IACZC,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAEC;EACZ,CAAC;EACDC,GAAG,EAAE;IACHT,MAAM,EAAEA,MAAM;IACdC,OAAO,EAAEA;EACX,CAAC;EACDS,QAAQ,EAAE,SAASA,QAAQA,CAACC,KAAK,EAAE;IACjC,OAAOlE,WAAW,CAACmE,cAAc,CAACD,KAAK,EAAER,UAAU,CAACE,YAAY,CAAC;EACnE,CAAC;EACDQ,aAAa,EAAE,SAASA,aAAaA,CAACF,KAAK,EAAE;IAC3C,OAAOlE,WAAW,CAACqE,YAAY,CAACH,KAAK,EAAER,UAAU,CAACE,YAAY,CAAC;EACjE;AACF,CAAC,CAAC;AAEF,SAASU,OAAOA,CAAC7D,CAAC,EAAEI,CAAC,EAAE;EAAE,IAAID,CAAC,GAAGP,MAAM,CAACkE,IAAI,CAAC9D,CAAC,CAAC;EAAE,IAAIJ,MAAM,CAACmE,qBAAqB,EAAE;IAAE,IAAItD,CAAC,GAAGb,MAAM,CAACmE,qBAAqB,CAAC/D,CAAC,CAAC;IAAEI,CAAC,KAAKK,CAAC,GAAGA,CAAC,CAACuD,MAAM,CAAC,UAAU5D,CAAC,EAAE;MAAE,OAAOR,MAAM,CAACqE,wBAAwB,CAACjE,CAAC,EAAEI,CAAC,CAAC,CAACmB,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEpB,CAAC,CAACiC,IAAI,CAAC7B,KAAK,CAACJ,CAAC,EAAEM,CAAC,CAAC;EAAE;EAAE,OAAON,CAAC;AAAE;AAC9P,SAAS+D,aAAaA,CAAClE,CAAC,EAAE;EAAE,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,SAAS,CAACC,MAAM,EAAEE,CAAC,EAAE,EAAE;IAAE,IAAID,CAAC,GAAG,IAAI,IAAIF,SAAS,CAACG,CAAC,CAAC,GAAGH,SAAS,CAACG,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGyD,OAAO,CAACjE,MAAM,CAACO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACgE,OAAO,CAAC,UAAU/D,CAAC,EAAE;MAAEgB,eAAe,CAACpB,CAAC,EAAEI,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGR,MAAM,CAACwE,yBAAyB,GAAGxE,MAAM,CAACyE,gBAAgB,CAACrE,CAAC,EAAEJ,MAAM,CAACwE,yBAAyB,CAACjE,CAAC,CAAC,CAAC,GAAG0D,OAAO,CAACjE,MAAM,CAACO,CAAC,CAAC,CAAC,CAACgE,OAAO,CAAC,UAAU/D,CAAC,EAAE;MAAER,MAAM,CAACyB,cAAc,CAACrB,CAAC,EAAEI,CAAC,EAAER,MAAM,CAACqE,wBAAwB,CAAC9D,CAAC,EAAEC,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOJ,CAAC;AAAE;AACtb,IAAIsE,MAAM,GAAG,aAAavF,KAAK,CAACwF,IAAI,CAAC,aAAaxF,KAAK,CAACyF,UAAU,CAAC,UAAUC,OAAO,EAAEC,GAAG,EAAE;EACzF,IAAIC,eAAe,GAAG5F,KAAK,CAAC6F,QAAQ,CAAC,KAAK,CAAC;IACzCC,gBAAgB,GAAGhC,cAAc,CAAC8B,eAAe,EAAE,CAAC,CAAC;IACrDG,SAAS,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC/BE,UAAU,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAClC,IAAIG,MAAM,GAAGjG,KAAK,CAACkG,MAAM,CAAC,IAAI,CAAC;EAC/B,IAAIC,SAAS,GAAGnG,KAAK,CAACkG,MAAM,CAAC,IAAI,CAAC;EAClC,IAAIE,UAAU,GAAGjG,aAAa,CAAC,CAAC;EAChC,IAAIkG,OAAO,GAAGrG,KAAK,CAACsG,UAAU,CAACpG,iBAAiB,CAAC;EACjD,IAAIwE,KAAK,GAAGR,UAAU,CAACO,QAAQ,CAACiB,OAAO,EAAEW,OAAO,CAAC;EACjD,IAAIE,cAAc,GAAGF,OAAO,IAAIA,OAAO,CAACG,MAAM,IAAIvG,UAAU,CAACuG,MAAM;EACnE,IAAIC,QAAQ,GAAG;IACb/B,KAAK,EAAEA;EACT,CAAC;EACDtE,QAAQ,CAAC8D,UAAU,CAACM,GAAG,CAACT,MAAM,EAAE;IAC9BL,IAAI,EAAE,QAAQ;IACdgD,MAAM,EAAE,CAACH;EACX,CAAC,CAAC;EACF,IAAII,qBAAqB,GAAGzC,UAAU,CAAC0C,WAAW,CAACzB,aAAa,CAAC,CAAC,CAAC,EAAEsB,QAAQ,CAAC,CAAC;IAC7EI,GAAG,GAAGF,qBAAqB,CAACE,GAAG;IAC/BC,EAAE,GAAGH,qBAAqB,CAACG,EAAE;EAC/B,IAAIC,SAAS,GAAG,SAASA,SAASA,CAAA,EAAG;IACnC,OAAOd,MAAM,CAACe,OAAO,IAAIf,MAAM,CAACe,OAAO,CAACC,aAAa;EACvD,CAAC;EACD,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAAA,EAAG;IACrC,IAAIf,SAAS,CAACa,OAAO,EAAE;MACrBb,SAAS,CAACa,OAAO,CAACG,gBAAgB,CAAC,aAAa,EAAEC,aAAa,CAAC;IAClE;EACF,CAAC;EACD,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAG;IACzC,IAAIlB,SAAS,CAACa,OAAO,EAAE;MACrBb,SAAS,CAACa,OAAO,CAACM,mBAAmB,CAAC,aAAa,EAAEF,aAAa,CAAC;IACrE;EACF,CAAC;EACD,IAAIA,aAAa,GAAG,SAASA,aAAaA,CAACG,KAAK,EAAE;IAChD,IAAIC,MAAM,GAAG9G,UAAU,CAAC+G,SAAS,CAACtB,SAAS,CAACa,OAAO,CAAC;IACpD,IAAIU,OAAO,GAAGH,KAAK,CAACI,KAAK,GAAGH,MAAM,CAACI,IAAI,GAAGC,QAAQ,CAACC,IAAI,CAACC,SAAS,GAAGrH,UAAU,CAACsH,QAAQ,CAAC/B,MAAM,CAACe,OAAO,CAAC,GAAG,CAAC;IAC3G,IAAIiB,OAAO,GAAGV,KAAK,CAACW,KAAK,GAAGV,MAAM,CAACW,GAAG,GAAGN,QAAQ,CAACC,IAAI,CAACM,UAAU,GAAG1H,UAAU,CAAC2H,SAAS,CAACpC,MAAM,CAACe,OAAO,CAAC,GAAG,CAAC;IAC5GsB,cAAc,CAACZ,OAAO,EAAEO,OAAO,CAAC;EAClC,CAAC;EACD,IAAIK,cAAc,GAAG,SAASA,cAAcA,CAACZ,OAAO,EAAEO,OAAO,EAAE;IAC7D,IAAI,CAAChC,MAAM,CAACe,OAAO,IAAIuB,gBAAgB,CAACtC,MAAM,CAACe,OAAO,EAAE,IAAI,CAAC,CAACwB,OAAO,KAAK,MAAM,EAAE;MAChF;IACF;IACA9H,UAAU,CAAC+H,WAAW,CAACxC,MAAM,CAACe,OAAO,EAAE,cAAc,CAAC;IACtD0B,aAAa,CAAC,CAAC;IACfzC,MAAM,CAACe,OAAO,CAAC2B,KAAK,CAACR,GAAG,GAAGF,OAAO,GAAG,IAAI;IACzChC,MAAM,CAACe,OAAO,CAAC2B,KAAK,CAACf,IAAI,GAAGF,OAAO,GAAG,IAAI;IAC1ChH,UAAU,CAACkI,QAAQ,CAAC3C,MAAM,CAACe,OAAO,EAAE,cAAc,CAAC;EACrD,CAAC;EACD,IAAI6B,cAAc,GAAG,SAASA,cAAcA,CAACtB,KAAK,EAAE;IAClD7G,UAAU,CAAC+H,WAAW,CAAClB,KAAK,CAACuB,aAAa,EAAE,cAAc,CAAC;EAC7D,CAAC;EACD,IAAIJ,aAAa,GAAG,SAASA,aAAaA,CAAA,EAAG;IAC3C,IAAIzC,MAAM,CAACe,OAAO,IAAI,CAACtG,UAAU,CAAC2H,SAAS,CAACpC,MAAM,CAACe,OAAO,CAAC,IAAI,CAACtG,UAAU,CAACsH,QAAQ,CAAC/B,MAAM,CAACe,OAAO,CAAC,EAAE;MACnG,IAAI+B,CAAC,GAAGC,IAAI,CAACC,GAAG,CAACvI,UAAU,CAACwI,aAAa,CAAC/C,SAAS,CAACa,OAAO,CAAC,EAAEtG,UAAU,CAACyI,cAAc,CAAChD,SAAS,CAACa,OAAO,CAAC,CAAC;MAC3Gf,MAAM,CAACe,OAAO,CAAC2B,KAAK,CAACS,MAAM,GAAGL,CAAC,GAAG,IAAI;MACtC9C,MAAM,CAACe,OAAO,CAAC2B,KAAK,CAACU,KAAK,GAAGN,CAAC,GAAG,IAAI;IACvC;EACF,CAAC;EACD/I,KAAK,CAACsJ,mBAAmB,CAAC3D,GAAG,EAAE,YAAY;IACzC,OAAO;MACLjB,KAAK,EAAEA,KAAK;MACZ6E,MAAM,EAAE,SAASA,MAAMA,CAAA,EAAG;QACxB,OAAOtD,MAAM,CAACe,OAAO;MACvB,CAAC;MACDD,SAAS,EAAE,SAASA,SAASA,CAAA,EAAG;QAC9B,OAAOZ,SAAS,CAACa,OAAO;MAC1B;IACF,CAAC;EACH,CAAC,CAAC;EACF3G,cAAc,CAAC,YAAY;IACzB;IACA2F,UAAU,CAAC,IAAI,CAAC;EAClB,CAAC,CAAC;EACF1F,eAAe,CAAC,YAAY;IAC1B,IAAIyF,SAAS,IAAIE,MAAM,CAACe,OAAO,EAAE;MAC/Bb,SAAS,CAACa,OAAO,GAAGD,SAAS,CAAC,CAAC;MAC/B2B,aAAa,CAAC,CAAC;MACfxB,UAAU,CAAC,CAAC;IACd;EACF,CAAC,EAAE,CAACnB,SAAS,CAAC,CAAC;EACfzF,eAAe,CAAC,YAAY;IAC1B,IAAI2F,MAAM,CAACe,OAAO,IAAI,CAACb,SAAS,CAACa,OAAO,EAAE;MACxCb,SAAS,CAACa,OAAO,GAAGD,SAAS,CAAC,CAAC;MAC/B2B,aAAa,CAAC,CAAC;MACfxB,UAAU,CAAC,CAAC;IACd;EACF,CAAC,CAAC;EACF3G,gBAAgB,CAAC,YAAY;IAC3B,IAAI0F,MAAM,CAACe,OAAO,EAAE;MAClBb,SAAS,CAACa,OAAO,GAAG,IAAI;MACxBK,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,CAAC;EACF,IAAI,CAACd,cAAc,EAAE;IACnB,OAAO,IAAI;EACb;EACA,IAAIiD,SAAS,GAAGpD,UAAU,CAAC;IACzB,aAAa,EAAE,IAAI;IACnBqD,SAAS,EAAEhJ,UAAU,CAACqG,EAAE,CAAC,MAAM,CAAC;EAClC,CAAC,EAAE5C,UAAU,CAACU,aAAa,CAACF,KAAK,CAAC,EAAEmC,GAAG,CAAC,MAAM,CAAC,CAAC;EAChD,OAAO,aAAa7G,KAAK,CAAC0J,aAAa,CAAC,MAAM,EAAE9I,QAAQ,CAAC;IACvD+I,IAAI,EAAE,cAAc;IACpBhE,GAAG,EAAEM;EACP,CAAC,EAAEuD,SAAS,EAAE;IACZX,cAAc,EAAEA;EAClB,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AACHtD,MAAM,CAACqE,WAAW,GAAG,QAAQ;AAE7B,SAASrE,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}