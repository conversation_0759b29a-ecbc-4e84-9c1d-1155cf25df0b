{"ast": null, "code": "import { Observable } from '../../Observable';\nimport { performanceTimestampProvider } from '../../scheduler/performanceTimestampProvider';\nimport { animationFrameProvider } from '../../scheduler/animationFrameProvider';\nexport function animationFrames(timestampProvider) {\n  return timestampProvider ? animationFramesFactory(timestampProvider) : DEFAULT_ANIMATION_FRAMES;\n}\nfunction animationFramesFactory(timestampProvider) {\n  return new Observable(function (subscriber) {\n    var provider = timestampProvider || performanceTimestampProvider;\n    var start = provider.now();\n    var id = 0;\n    var run = function () {\n      if (!subscriber.closed) {\n        id = animationFrameProvider.requestAnimationFrame(function (timestamp) {\n          id = 0;\n          var now = provider.now();\n          subscriber.next({\n            timestamp: timestampProvider ? now : timestamp,\n            elapsed: now - start\n          });\n          run();\n        });\n      }\n    };\n    run();\n    return function () {\n      if (id) {\n        animationFrameProvider.cancelAnimationFrame(id);\n      }\n    };\n  });\n}\nvar DEFAULT_ANIMATION_FRAMES = animationFramesFactory();", "map": {"version": 3, "names": ["Observable", "performanceTimestampProvider", "animationFrameProvider", "animationFrames", "timestampProvider", "animationFramesFactory", "DEFAULT_ANIMATION_FRAMES", "subscriber", "provider", "start", "now", "id", "run", "closed", "requestAnimationFrame", "timestamp", "next", "elapsed", "cancelAnimationFrame"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\observable\\dom\\animationFrames.ts"], "sourcesContent": ["import { Observable } from '../../Observable';\nimport { TimestampProvider } from '../../types';\nimport { performanceTimestampProvider } from '../../scheduler/performanceTimestampProvider';\nimport { animationFrameProvider } from '../../scheduler/animationFrameProvider';\n\n/**\n * An observable of animation frames\n *\n * Emits the amount of time elapsed since subscription and the timestamp on each animation frame.\n * Defaults to milliseconds provided to the requestAnimationFrame's callback. Does not end on its own.\n *\n * Every subscription will start a separate animation loop. Since animation frames are always scheduled\n * by the browser to occur directly before a repaint, scheduling more than one animation frame synchronously\n * should not be much different or have more overhead than looping over an array of events during\n * a single animation frame. However, if for some reason the developer would like to ensure the\n * execution of animation-related handlers are all executed during the same task by the engine,\n * the `share` operator can be used.\n *\n * This is useful for setting up animations with RxJS.\n *\n * ## Examples\n *\n * Tweening a div to move it on the screen\n *\n * ```ts\n * import { animationFrames, map, takeWhile, endWith } from 'rxjs';\n *\n * function tween(start: number, end: number, duration: number) {\n *   const diff = end - start;\n *   return animationFrames().pipe(\n *     // Figure out what percentage of time has passed\n *     map(({ elapsed }) => elapsed / duration),\n *     // Take the vector while less than 100%\n *     takeWhile(v => v < 1),\n *     // Finish with 100%\n *     endWith(1),\n *     // Calculate the distance traveled between start and end\n *     map(v => v * diff + start)\n *   );\n * }\n *\n * // Setup a div for us to move around\n * const div = document.createElement('div');\n * document.body.appendChild(div);\n * div.style.position = 'absolute';\n * div.style.width = '40px';\n * div.style.height = '40px';\n * div.style.backgroundColor = 'lime';\n * div.style.transform = 'translate3d(10px, 0, 0)';\n *\n * tween(10, 200, 4000).subscribe(x => {\n *   div.style.transform = `translate3d(${ x }px, 0, 0)`;\n * });\n * ```\n *\n * Providing a custom timestamp provider\n *\n * ```ts\n * import { animationFrames, TimestampProvider } from 'rxjs';\n *\n * // A custom timestamp provider\n * let now = 0;\n * const customTSProvider: TimestampProvider = {\n *   now() { return now++; }\n * };\n *\n * const source$ = animationFrames(customTSProvider);\n *\n * // Log increasing numbers 0...1...2... on every animation frame.\n * source$.subscribe(({ elapsed }) => console.log(elapsed));\n * ```\n *\n * @param timestampProvider An object with a `now` method that provides a numeric timestamp\n */\nexport function animationFrames(timestampProvider?: TimestampProvider) {\n  return timestampProvider ? animationFramesFactory(timestampProvider) : DEFAULT_ANIMATION_FRAMES;\n}\n\n/**\n * Does the work of creating the observable for `animationFrames`.\n * @param timestampProvider The timestamp provider to use to create the observable\n */\nfunction animationFramesFactory(timestampProvider?: TimestampProvider) {\n  return new Observable<{ timestamp: number; elapsed: number }>((subscriber) => {\n    // If no timestamp provider is specified, use performance.now() - as it\n    // will return timestamps 'compatible' with those passed to the run\n    // callback and won't be affected by NTP adjustments, etc.\n    const provider = timestampProvider || performanceTimestampProvider;\n\n    // Capture the start time upon subscription, as the run callback can remain\n    // queued for a considerable period of time and the elapsed time should\n    // represent the time elapsed since subscription - not the time since the\n    // first rendered animation frame.\n    const start = provider.now();\n\n    let id = 0;\n    const run = () => {\n      if (!subscriber.closed) {\n        id = animationFrameProvider.requestAnimationFrame((timestamp: DOMHighResTimeStamp | number) => {\n          id = 0;\n          // Use the provider's timestamp to calculate the elapsed time. Note that\n          // this means - if the caller hasn't passed a provider - that\n          // performance.now() will be used instead of the timestamp that was\n          // passed to the run callback. The reason for this is that the timestamp\n          // passed to the callback can be earlier than the start time, as it\n          // represents the time at which the browser decided it would render any\n          // queued frames - and that time can be earlier the captured start time.\n          const now = provider.now();\n          subscriber.next({\n            timestamp: timestampProvider ? now : timestamp,\n            elapsed: now - start,\n          });\n          run();\n        });\n      }\n    };\n\n    run();\n\n    return () => {\n      if (id) {\n        animationFrameProvider.cancelAnimationFrame(id);\n      }\n    };\n  });\n}\n\n/**\n * In the common case, where the timestamp provided by the rAF API is used,\n * we use this shared observable to reduce overhead.\n */\nconst DEFAULT_ANIMATION_FRAMES = animationFramesFactory();\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,kBAAkB;AAE7C,SAASC,4BAA4B,QAAQ,8CAA8C;AAC3F,SAASC,sBAAsB,QAAQ,wCAAwC;AAuE/E,OAAM,SAAUC,eAAeA,CAACC,iBAAqC;EACnE,OAAOA,iBAAiB,GAAGC,sBAAsB,CAACD,iBAAiB,CAAC,GAAGE,wBAAwB;AACjG;AAMA,SAASD,sBAAsBA,CAACD,iBAAqC;EACnE,OAAO,IAAIJ,UAAU,CAAyC,UAACO,UAAU;IAIvE,IAAMC,QAAQ,GAAGJ,iBAAiB,IAAIH,4BAA4B;IAMlE,IAAMQ,KAAK,GAAGD,QAAQ,CAACE,GAAG,EAAE;IAE5B,IAAIC,EAAE,GAAG,CAAC;IACV,IAAMC,GAAG,GAAG,SAAAA,CAAA;MACV,IAAI,CAACL,UAAU,CAACM,MAAM,EAAE;QACtBF,EAAE,GAAGT,sBAAsB,CAACY,qBAAqB,CAAC,UAACC,SAAuC;UACxFJ,EAAE,GAAG,CAAC;UAQN,IAAMD,GAAG,GAAGF,QAAQ,CAACE,GAAG,EAAE;UAC1BH,UAAU,CAACS,IAAI,CAAC;YACdD,SAAS,EAAEX,iBAAiB,GAAGM,GAAG,GAAGK,SAAS;YAC9CE,OAAO,EAAEP,GAAG,GAAGD;WAChB,CAAC;UACFG,GAAG,EAAE;QACP,CAAC,CAAC;;IAEN,CAAC;IAEDA,GAAG,EAAE;IAEL,OAAO;MACL,IAAID,EAAE,EAAE;QACNT,sBAAsB,CAACgB,oBAAoB,CAACP,EAAE,CAAC;;IAEnD,CAAC;EACH,CAAC,CAAC;AACJ;AAMA,IAAML,wBAAwB,GAAGD,sBAAsB,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}