{"ast": null, "code": "import { Subject } from \"rxjs\";\n\n/** Work for subscribe http request/response processing status crossing components\r\n *  Work for global progress spinner.\r\n */\nconst loadingSubject = new Subject();\n/**\r\n * Global variable to keep the couting of http request / http respone.\r\n */\nvar loadingCount = 0;\n\n/**\r\n * Global flag to control whether loading interceptor should be active\r\n * When disabled, HTTP requests won't trigger the loading spinner\r\n */\nvar isLoadingInterceptorEnabled = true;\n\n/**\r\n * Set of disabled contexts - when any of these contexts are active,\r\n * the loading interceptor will be disabled\r\n */\nconst disabledContexts = new Set();\nfunction timmerBeforeSend(inputCount) {\n  if (inputCount === 0) {\n    loadingCount = 0;\n  }\n  loadingCount = loadingCount + inputCount;\n  if (loadingCount <= 0) {\n    //\n    // Immediately notify system to hide the progress spinner.\n    //\n    loadingCount = 0;\n    return new Promise(resolve => {\n      resolve(\"hide\");\n    });\n  } else {\n    //\n    // Reduced delay to prevent BlockUI issues while still preventing spinner flash\n    // for very fast HTTP requests\n    //\n    return new Promise(resolve => {\n      setTimeout(() => {\n        resolve(\"show\");\n      }, 1000);\n    });\n  }\n}\n\n/**\r\n * Check if loading interceptor should be active\r\n * @returns {boolean} true if interceptor should be active, false otherwise\r\n */\nfunction shouldApplyLoadingInterceptor() {\n  // If globally disabled, return false\n  if (!isLoadingInterceptorEnabled) {\n    return false;\n  }\n\n  // If any disabled context is active, return false\n  if (disabledContexts.size > 0) {\n    return false;\n  }\n  return true;\n}\n\n/**\r\n * Called by LoadingService. Work for show/hide porgress spinner when http request sent and http response received.\r\n * @param {*} inputCount for http request sent, set inputCount = 1; for http response received, set inputCount = -1; any http error happened, set inputCount = 0;\r\n */\nasync function notifyLoading(inputCount) {\n  try {\n    // Check if loading interceptor should be applied\n    if (!shouldApplyLoadingInterceptor()) {\n      return; // Skip loading notification if interceptor is disabled\n    }\n    const result = await timmerBeforeSend(inputCount);\n    if (result === \"hide\") {\n      if (loadingCount <= 0) {\n        loadingSubject.next(false); // notify to hide spinner.\n      }\n    } else if (result === \"show\") {\n      if (loadingCount > 0) {\n        loadingSubject.next(true); // notify to show spinner.\n      }\n    }\n  } catch (error) {\n    console.error(\"Error in notifyLoading:\", error);\n    // On error, ensure loading is hidden\n    loadingSubject.next(false);\n  }\n}\n\n/***\r\n * Note: It is single tone service.\r\n * Reference: https://jasonwatmore.com/post/2019/02/13/react-rxjs-communicating-between-components-with-observable-subject#:~:text=React%20%2B%20RxJS%20App%20Component%20that,divs%20in%20the%20render%20method.\r\n */\nexport const loadingService = {\n  /**\r\n   * when http request sent, notify observer.\r\n   */\n  httpRequestSent: () => {\n    try {\n      notifyLoading(1);\n    } catch (error) {\n      console.error(\"Error in httpRequestSent:\", error);\n    }\n  },\n  /**\r\n   * when http response received, notify observer.\r\n   */\n  httpResponseReceived: () => {\n    try {\n      notifyLoading(-1);\n    } catch (error) {\n      console.error(\"Error in httpResponseReceived:\", error);\n    }\n  },\n  /** Any error happened in any http request or http response, notify observer */\n  error: () => {\n    try {\n      notifyLoading(0);\n    } catch (error) {\n      console.error(\"Error in error handler:\", error);\n      // Force hide loading on error\n      loadingSubject.next(false);\n    }\n  },\n  /**\r\n   * Return observaber subject to receiver.\r\n   * @returns observer.\r\n   */\n  get: () => {\n    return loadingSubject.asObservable();\n  },\n  /**\r\n   * Enable or disable the loading interceptor globally\r\n   * @param {boolean} enabled - true to enable, false to disable\r\n   */\n  setInterceptorEnabled: enabled => {\n    isLoadingInterceptorEnabled = enabled;\n    console.log(`Loading interceptor ${enabled ? 'enabled' : 'disabled'}`);\n  },\n  /**\r\n   * Check if the loading interceptor is currently enabled\r\n   * @returns {boolean} true if enabled, false if disabled\r\n   */\n  isInterceptorEnabled: () => {\n    return isLoadingInterceptorEnabled;\n  },\n  /**\r\n   * Add a context to the disabled contexts set\r\n   * When any disabled context is active, the loading interceptor will be disabled\r\n   * @param {string} context - context identifier (e.g., 'survey', 'form', etc.)\r\n   */\n  disableForContext: context => {\n    disabledContexts.add(context);\n    console.log(`Loading interceptor disabled for context: ${context}`);\n  },\n  /**\r\n   * Remove a context from the disabled contexts set\r\n   * @param {string} context - context identifier to re-enable\r\n   */\n  enableForContext: context => {\n    disabledContexts.delete(context);\n    console.log(`Loading interceptor enabled for context: ${context}`);\n  },\n  /**\r\n   * Clear all disabled contexts\r\n   */\n  clearDisabledContexts: () => {\n    disabledContexts.clear();\n    console.log('All disabled contexts cleared');\n  },\n  /**\r\n   * Get the current set of disabled contexts\r\n   * @returns {Set<string>} set of disabled context identifiers\r\n   */\n  getDisabledContexts: () => {\n    return new Set(disabledContexts);\n  },\n  /**\r\n   * Check if a specific context is disabled\r\n   * @param {string} context - context identifier to check\r\n   * @returns {boolean} true if context is disabled, false otherwise\r\n   */\n  isContextDisabled: context => {\n    return disabledContexts.has(context);\n  }\n};", "map": {"version": 3, "names": ["Subject", "loadingSubject", "loadingCount", "isLoadingInterceptorEnabled", "disabledContexts", "Set", "timmerBeforeSend", "inputCount", "Promise", "resolve", "setTimeout", "shouldApplyLoadingInterceptor", "size", "notify<PERSON>oa<PERSON>", "result", "next", "error", "console", "loadingService", "httpRequestSent", "httpResponseReceived", "get", "asObservable", "setInterceptorEnabled", "enabled", "log", "isInterceptorEnabled", "disableForContext", "context", "add", "enableForContext", "delete", "clearDisabledContexts", "clear", "getDisabledContexts", "isContextDisabled", "has"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/core/loading/loadingService.js"], "sourcesContent": ["import { Subject } from \"rxjs\";\r\n\r\n/** Work for subscribe http request/response processing status crossing components\r\n *  Work for global progress spinner.\r\n */\r\nconst loadingSubject = new Subject();\r\n/**\r\n * Global variable to keep the couting of http request / http respone.\r\n */\r\nvar loadingCount = 0;\r\n\r\n/**\r\n * Global flag to control whether loading interceptor should be active\r\n * When disabled, HTTP requests won't trigger the loading spinner\r\n */\r\nvar isLoadingInterceptorEnabled = true;\r\n\r\n/**\r\n * Set of disabled contexts - when any of these contexts are active,\r\n * the loading interceptor will be disabled\r\n */\r\nconst disabledContexts = new Set();\r\n\r\nfunction timmerBeforeSend(inputCount) {\r\n  if (inputCount === 0) {\r\n    loadingCount = 0;\r\n  }\r\n\r\n  loadingCount = loadingCount + inputCount;\r\n\r\n  if (loadingCount <= 0) {\r\n    //\r\n    // Immediately notify system to hide the progress spinner.\r\n    //\r\n    loadingCount = 0;\r\n    return new Promise((resolve) => {\r\n      resolve(\"hide\");\r\n    });\r\n  } else {\r\n    //\r\n    // Reduced delay to prevent BlockUI issues while still preventing spinner flash\r\n    // for very fast HTTP requests\r\n    //\r\n    return new Promise((resolve) => {\r\n      setTimeout(() => {\r\n        resolve(\"show\");\r\n      }, 1000); \r\n    });\r\n  }\r\n}\r\n\r\n/**\r\n * Check if loading interceptor should be active\r\n * @returns {boolean} true if interceptor should be active, false otherwise\r\n */\r\nfunction shouldApplyLoadingInterceptor() {\r\n  // If globally disabled, return false\r\n  if (!isLoadingInterceptorEnabled) {\r\n    return false;\r\n  }\r\n\r\n  // If any disabled context is active, return false\r\n  if (disabledContexts.size > 0) {\r\n    return false;\r\n  }\r\n\r\n  return true;\r\n}\r\n\r\n/**\r\n * Called by LoadingService. Work for show/hide porgress spinner when http request sent and http response received.\r\n * @param {*} inputCount for http request sent, set inputCount = 1; for http response received, set inputCount = -1; any http error happened, set inputCount = 0;\r\n */\r\nasync function notifyLoading(inputCount) {\r\n  try {\r\n    // Check if loading interceptor should be applied\r\n    if (!shouldApplyLoadingInterceptor()) {\r\n      return; // Skip loading notification if interceptor is disabled\r\n    }\r\n\r\n    const result = await timmerBeforeSend(inputCount);\r\n    if (result === \"hide\") {\r\n      if (loadingCount <= 0) {\r\n        loadingSubject.next(false); // notify to hide spinner.\r\n      }\r\n    } else if (result === \"show\") {\r\n      if (loadingCount > 0) {\r\n        loadingSubject.next(true); // notify to show spinner.\r\n      }\r\n    }\r\n  } catch (error) {\r\n    console.error(\"Error in notifyLoading:\", error);\r\n    // On error, ensure loading is hidden\r\n    loadingSubject.next(false);\r\n  }\r\n}\r\n\r\n/***\r\n * Note: It is single tone service.\r\n * Reference: https://jasonwatmore.com/post/2019/02/13/react-rxjs-communicating-between-components-with-observable-subject#:~:text=React%20%2B%20RxJS%20App%20Component%20that,divs%20in%20the%20render%20method.\r\n */\r\nexport const loadingService = {\r\n  /**\r\n   * when http request sent, notify observer.\r\n   */\r\n  httpRequestSent: () => {\r\n    try {\r\n      notifyLoading(1);\r\n    } catch (error) {\r\n      console.error(\"Error in httpRequestSent:\", error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * when http response received, notify observer.\r\n   */\r\n  httpResponseReceived: () => {\r\n    try {\r\n      notifyLoading(-1);\r\n    } catch (error) {\r\n      console.error(\"Error in httpResponseReceived:\", error);\r\n    }\r\n  },\r\n\r\n  /** Any error happened in any http request or http response, notify observer */\r\n  error: () => {\r\n    try {\r\n      notifyLoading(0);\r\n    } catch (error) {\r\n      console.error(\"Error in error handler:\", error);\r\n      // Force hide loading on error\r\n      loadingSubject.next(false);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Return observaber subject to receiver.\r\n   * @returns observer.\r\n   */\r\n  get: () => {\r\n    return loadingSubject.asObservable();\r\n  },\r\n\r\n  /**\r\n   * Enable or disable the loading interceptor globally\r\n   * @param {boolean} enabled - true to enable, false to disable\r\n   */\r\n  setInterceptorEnabled: (enabled) => {\r\n    isLoadingInterceptorEnabled = enabled;\r\n    console.log(`Loading interceptor ${enabled ? 'enabled' : 'disabled'}`);\r\n  },\r\n\r\n  /**\r\n   * Check if the loading interceptor is currently enabled\r\n   * @returns {boolean} true if enabled, false if disabled\r\n   */\r\n  isInterceptorEnabled: () => {\r\n    return isLoadingInterceptorEnabled;\r\n  },\r\n\r\n  /**\r\n   * Add a context to the disabled contexts set\r\n   * When any disabled context is active, the loading interceptor will be disabled\r\n   * @param {string} context - context identifier (e.g., 'survey', 'form', etc.)\r\n   */\r\n  disableForContext: (context) => {\r\n    disabledContexts.add(context);\r\n    console.log(`Loading interceptor disabled for context: ${context}`);\r\n  },\r\n\r\n  /**\r\n   * Remove a context from the disabled contexts set\r\n   * @param {string} context - context identifier to re-enable\r\n   */\r\n  enableForContext: (context) => {\r\n    disabledContexts.delete(context);\r\n    console.log(`Loading interceptor enabled for context: ${context}`);\r\n  },\r\n\r\n  /**\r\n   * Clear all disabled contexts\r\n   */\r\n  clearDisabledContexts: () => {\r\n    disabledContexts.clear();\r\n    console.log('All disabled contexts cleared');\r\n  },\r\n\r\n  /**\r\n   * Get the current set of disabled contexts\r\n   * @returns {Set<string>} set of disabled context identifiers\r\n   */\r\n  getDisabledContexts: () => {\r\n    return new Set(disabledContexts);\r\n  },\r\n\r\n  /**\r\n   * Check if a specific context is disabled\r\n   * @param {string} context - context identifier to check\r\n   * @returns {boolean} true if context is disabled, false otherwise\r\n   */\r\n  isContextDisabled: (context) => {\r\n    return disabledContexts.has(context);\r\n  }\r\n};\r\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,MAAM;;AAE9B;AACA;AACA;AACA,MAAMC,cAAc,GAAG,IAAID,OAAO,CAAC,CAAC;AACpC;AACA;AACA;AACA,IAAIE,YAAY,GAAG,CAAC;;AAEpB;AACA;AACA;AACA;AACA,IAAIC,2BAA2B,GAAG,IAAI;;AAEtC;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,GAAG,IAAIC,GAAG,CAAC,CAAC;AAElC,SAASC,gBAAgBA,CAACC,UAAU,EAAE;EACpC,IAAIA,UAAU,KAAK,CAAC,EAAE;IACpBL,YAAY,GAAG,CAAC;EAClB;EAEAA,YAAY,GAAGA,YAAY,GAAGK,UAAU;EAExC,IAAIL,YAAY,IAAI,CAAC,EAAE;IACrB;IACA;IACA;IACAA,YAAY,GAAG,CAAC;IAChB,OAAO,IAAIM,OAAO,CAAEC,OAAO,IAAK;MAC9BA,OAAO,CAAC,MAAM,CAAC;IACjB,CAAC,CAAC;EACJ,CAAC,MAAM;IACL;IACA;IACA;IACA;IACA,OAAO,IAAID,OAAO,CAAEC,OAAO,IAAK;MAC9BC,UAAU,CAAC,MAAM;QACfD,OAAO,CAAC,MAAM,CAAC;MACjB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC;EACJ;AACF;;AAEA;AACA;AACA;AACA;AACA,SAASE,6BAA6BA,CAAA,EAAG;EACvC;EACA,IAAI,CAACR,2BAA2B,EAAE;IAChC,OAAO,KAAK;EACd;;EAEA;EACA,IAAIC,gBAAgB,CAACQ,IAAI,GAAG,CAAC,EAAE;IAC7B,OAAO,KAAK;EACd;EAEA,OAAO,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA,eAAeC,aAAaA,CAACN,UAAU,EAAE;EACvC,IAAI;IACF;IACA,IAAI,CAACI,6BAA6B,CAAC,CAAC,EAAE;MACpC,OAAO,CAAC;IACV;IAEA,MAAMG,MAAM,GAAG,MAAMR,gBAAgB,CAACC,UAAU,CAAC;IACjD,IAAIO,MAAM,KAAK,MAAM,EAAE;MACrB,IAAIZ,YAAY,IAAI,CAAC,EAAE;QACrBD,cAAc,CAACc,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;MAC9B;IACF,CAAC,MAAM,IAAID,MAAM,KAAK,MAAM,EAAE;MAC5B,IAAIZ,YAAY,GAAG,CAAC,EAAE;QACpBD,cAAc,CAACc,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;MAC7B;IACF;EACF,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IAC/C;IACAf,cAAc,CAACc,IAAI,CAAC,KAAK,CAAC;EAC5B;AACF;;AAEA;AACA;AACA;AACA;AACA,OAAO,MAAMG,cAAc,GAAG;EAC5B;AACF;AACA;EACEC,eAAe,EAAEA,CAAA,KAAM;IACrB,IAAI;MACFN,aAAa,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACnD;EACF,CAAC;EAED;AACF;AACA;EACEI,oBAAoB,EAAEA,CAAA,KAAM;IAC1B,IAAI;MACFP,aAAa,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACxD;EACF,CAAC;EAED;EACAA,KAAK,EAAEA,CAAA,KAAM;IACX,IAAI;MACFH,aAAa,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C;MACAf,cAAc,CAACc,IAAI,CAAC,KAAK,CAAC;IAC5B;EACF,CAAC;EAED;AACF;AACA;AACA;EACEM,GAAG,EAAEA,CAAA,KAAM;IACT,OAAOpB,cAAc,CAACqB,YAAY,CAAC,CAAC;EACtC,CAAC;EAED;AACF;AACA;AACA;EACEC,qBAAqB,EAAGC,OAAO,IAAK;IAClCrB,2BAA2B,GAAGqB,OAAO;IACrCP,OAAO,CAACQ,GAAG,CAAC,uBAAuBD,OAAO,GAAG,SAAS,GAAG,UAAU,EAAE,CAAC;EACxE,CAAC;EAED;AACF;AACA;AACA;EACEE,oBAAoB,EAAEA,CAAA,KAAM;IAC1B,OAAOvB,2BAA2B;EACpC,CAAC;EAED;AACF;AACA;AACA;AACA;EACEwB,iBAAiB,EAAGC,OAAO,IAAK;IAC9BxB,gBAAgB,CAACyB,GAAG,CAACD,OAAO,CAAC;IAC7BX,OAAO,CAACQ,GAAG,CAAC,6CAA6CG,OAAO,EAAE,CAAC;EACrE,CAAC;EAED;AACF;AACA;AACA;EACEE,gBAAgB,EAAGF,OAAO,IAAK;IAC7BxB,gBAAgB,CAAC2B,MAAM,CAACH,OAAO,CAAC;IAChCX,OAAO,CAACQ,GAAG,CAAC,4CAA4CG,OAAO,EAAE,CAAC;EACpE,CAAC;EAED;AACF;AACA;EACEI,qBAAqB,EAAEA,CAAA,KAAM;IAC3B5B,gBAAgB,CAAC6B,KAAK,CAAC,CAAC;IACxBhB,OAAO,CAACQ,GAAG,CAAC,+BAA+B,CAAC;EAC9C,CAAC;EAED;AACF;AACA;AACA;EACES,mBAAmB,EAAEA,CAAA,KAAM;IACzB,OAAO,IAAI7B,GAAG,CAACD,gBAAgB,CAAC;EAClC,CAAC;EAED;AACF;AACA;AACA;AACA;EACE+B,iBAAiB,EAAGP,OAAO,IAAK;IAC9B,OAAOxB,gBAAgB,CAACgC,GAAG,CAACR,OAAO,CAAC;EACtC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}