{"ast": null, "code": "import { createStore, applyMiddleware } from \"redux\";\nimport { thunk } from \"redux-thunk\";\nimport logger from \"redux-logger\";\nimport { persistStore, persistReducer } from \"redux-persist\";\nimport storage from \"redux-persist/lib/storage\";\nimport rootReducer from \"./reducers/rootReducer\";\n\n/**\r\n *  Reference: https://edisondevadoss.medium.com/how-to-use-redux-persist-in-react-application-35943c1d8292\r\n * https://www.youtube.com/watch?v=9jULHSe41ls\r\n */\nconst persistConfig = {\n  key: \"language\",\n  storage: storage,\n  //\n  // It (whitelist) ensures which reducer want to save in persistence storage,\n  // and rest of the reducers are not save in persistence storage.\n  //\n  whitelist: [\"language\"]\n};\nconst pReducer = persistReducer(persistConfig, rootReducer);\n//const middleware = applyMiddleware(thunk, logger);\nconst middleware = applyMiddleware(thunk, logger);\n\n/**\r\n * Keeping the selected language in local storage through Redux.\r\n * Reference: https://edisondevadoss.medium.com/how-to-use-redux-persist-in-react-application-35943c1d8292\r\n */\nconst store = createStore(pReducer, middleware);\n\n// /** Every time redux state store got updated, \n//  * notify system to save the current langauge code into localstorage of browser.\n//  * */\n// store.subscribe(()=>{\n//   var state = store.getState();\n\n//   //\n//   // Keep the selected language in the local storage. \n//   // Help httpClient to get current selected langauge and inject langauge into the http request header.\n//   // corpoate with httpClient.js\n//   //\n//   localStorage.setItem(`${APP_CONFIG.clientId}_CurrentLanguage`, state.language);\n\n// });\n\n/**\r\n * Persistor keeps the root store for Redux.\r\n */\nconst persistor = persistStore(store);\nexport { persistor, store };", "map": {"version": 3, "names": ["createStore", "applyMiddleware", "thunk", "logger", "persistStore", "persistReducer", "storage", "rootReducer", "persistConfig", "key", "whitelist", "pReducer", "middleware", "store", "persistor"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/redux/store.js"], "sourcesContent": ["import { createStore, applyMiddleware } from \"redux\";\r\nimport { thunk } from \"redux-thunk\";\r\nimport logger from \"redux-logger\";\r\nimport { persistStore, persistReducer} from \"redux-persist\";\r\nimport storage from \"redux-persist/lib/storage\";\r\nimport rootReducer from \"./reducers/rootReducer\";\r\n\r\n/**\r\n *  Reference: https://edisondevadoss.medium.com/how-to-use-redux-persist-in-react-application-35943c1d8292\r\n * https://www.youtube.com/watch?v=9jULHSe41ls\r\n */\r\nconst persistConfig = {\r\n  key: \"language\",\r\n  storage: storage,\r\n  //\r\n  // It (whitelist) ensures which reducer want to save in persistence storage,\r\n  // and rest of the reducers are not save in persistence storage.\r\n  //\r\n  whitelist: [\"language\"],\r\n};\r\nconst pReducer = persistReducer(persistConfig, rootReducer);\r\n//const middleware = applyMiddleware(thunk, logger);\r\nconst middleware = applyMiddleware(thunk, logger);\r\n\r\n/**\r\n * Keeping the selected language in local storage through Redux.\r\n * Reference: https://edisondevadoss.medium.com/how-to-use-redux-persist-in-react-application-35943c1d8292\r\n */\r\nconst store = createStore(pReducer, middleware);\r\n\r\n// /** Every time redux state store got updated, \r\n//  * notify system to save the current langauge code into localstorage of browser.\r\n//  * */\r\n// store.subscribe(()=>{\r\n//   var state = store.getState();\r\n\r\n//   //\r\n//   // Keep the selected language in the local storage. \r\n//   // Help httpClient to get current selected langauge and inject langauge into the http request header.\r\n//   // corpoate with httpClient.js\r\n//   //\r\n//   localStorage.setItem(`${APP_CONFIG.clientId}_CurrentLanguage`, state.language);\r\n\r\n// });\r\n\r\n/**\r\n * Persistor keeps the root store for Redux.\r\n */\r\nconst persistor = persistStore(store);\r\n\r\nexport { persistor, store };\r\n"], "mappings": "AAAA,SAASA,WAAW,EAAEC,eAAe,QAAQ,OAAO;AACpD,SAASC,KAAK,QAAQ,aAAa;AACnC,OAAOC,MAAM,MAAM,cAAc;AACjC,SAASC,YAAY,EAAEC,cAAc,QAAO,eAAe;AAC3D,OAAOC,OAAO,MAAM,2BAA2B;AAC/C,OAAOC,WAAW,MAAM,wBAAwB;;AAEhD;AACA;AACA;AACA;AACA,MAAMC,aAAa,GAAG;EACpBC,GAAG,EAAE,UAAU;EACfH,OAAO,EAAEA,OAAO;EAChB;EACA;EACA;EACA;EACAI,SAAS,EAAE,CAAC,UAAU;AACxB,CAAC;AACD,MAAMC,QAAQ,GAAGN,cAAc,CAACG,aAAa,EAAED,WAAW,CAAC;AAC3D;AACA,MAAMK,UAAU,GAAGX,eAAe,CAACC,KAAK,EAAEC,MAAM,CAAC;;AAEjD;AACA;AACA;AACA;AACA,MAAMU,KAAK,GAAGb,WAAW,CAACW,QAAQ,EAAEC,UAAU,CAAC;;AAE/C;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,MAAME,SAAS,GAAGV,YAAY,CAACS,KAAK,CAAC;AAErC,SAASC,SAAS,EAAED,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}