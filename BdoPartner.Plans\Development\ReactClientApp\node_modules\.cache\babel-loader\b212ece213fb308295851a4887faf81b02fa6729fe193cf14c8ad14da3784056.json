{"ast": null, "code": "import { asyncScheduler } from '../scheduler/async';\nimport { isValidDate } from '../util/isDate';\nimport { operate } from '../util/lift';\nimport { innerFrom } from '../observable/innerFrom';\nimport { createErrorClass } from '../util/createErrorClass';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { executeSchedule } from '../util/executeSchedule';\nexport var TimeoutError = createErrorClass(function (_super) {\n  return function TimeoutErrorImpl(info) {\n    if (info === void 0) {\n      info = null;\n    }\n    _super(this);\n    this.message = 'Timeout has occurred';\n    this.name = 'TimeoutError';\n    this.info = info;\n  };\n});\nexport function timeout(config, schedulerArg) {\n  var _a = isValidDate(config) ? {\n      first: config\n    } : typeof config === 'number' ? {\n      each: config\n    } : config,\n    first = _a.first,\n    each = _a.each,\n    _b = _a.with,\n    _with = _b === void 0 ? timeoutErrorFactory : _b,\n    _c = _a.scheduler,\n    scheduler = _c === void 0 ? schedulerArg !== null && schedulerArg !== void 0 ? schedulerArg : asyncScheduler : _c,\n    _d = _a.meta,\n    meta = _d === void 0 ? null : _d;\n  if (first == null && each == null) {\n    throw new TypeError('No timeout provided.');\n  }\n  return operate(function (source, subscriber) {\n    var originalSourceSubscription;\n    var timerSubscription;\n    var lastValue = null;\n    var seen = 0;\n    var startTimer = function (delay) {\n      timerSubscription = executeSchedule(subscriber, scheduler, function () {\n        try {\n          originalSourceSubscription.unsubscribe();\n          innerFrom(_with({\n            meta: meta,\n            lastValue: lastValue,\n            seen: seen\n          })).subscribe(subscriber);\n        } catch (err) {\n          subscriber.error(err);\n        }\n      }, delay);\n    };\n    originalSourceSubscription = source.subscribe(createOperatorSubscriber(subscriber, function (value) {\n      timerSubscription === null || timerSubscription === void 0 ? void 0 : timerSubscription.unsubscribe();\n      seen++;\n      subscriber.next(lastValue = value);\n      each > 0 && startTimer(each);\n    }, undefined, undefined, function () {\n      if (!(timerSubscription === null || timerSubscription === void 0 ? void 0 : timerSubscription.closed)) {\n        timerSubscription === null || timerSubscription === void 0 ? void 0 : timerSubscription.unsubscribe();\n      }\n      lastValue = null;\n    }));\n    !seen && startTimer(first != null ? typeof first === 'number' ? first : +first - scheduler.now() : each);\n  });\n}\nfunction timeoutErrorFactory(info) {\n  throw new TimeoutError(info);\n}", "map": {"version": 3, "names": ["asyncScheduler", "isValidDate", "operate", "innerFrom", "createErrorClass", "createOperatorSubscriber", "executeSchedule", "TimeoutError", "_super", "TimeoutErrorImpl", "info", "message", "name", "timeout", "config", "schedulerArg", "_a", "first", "each", "_b", "with", "_with", "timeoutErrorFactory", "_c", "scheduler", "_d", "meta", "TypeError", "source", "subscriber", "originalSourceSubscription", "timerSubscription", "lastValue", "seen", "startTimer", "delay", "unsubscribe", "subscribe", "err", "error", "value", "next", "undefined", "closed", "now"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\timeout.ts"], "sourcesContent": ["import { asyncScheduler } from '../scheduler/async';\nimport { MonoTypeOperatorFunction, SchedulerLike, OperatorFunction, ObservableInput, ObservedValueOf } from '../types';\nimport { isValidDate } from '../util/isDate';\nimport { Subscription } from '../Subscription';\nimport { operate } from '../util/lift';\nimport { Observable } from '../Observable';\nimport { innerFrom } from '../observable/innerFrom';\nimport { createErrorClass } from '../util/createErrorClass';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { executeSchedule } from '../util/executeSchedule';\n\nexport interface TimeoutConfig<T, O extends ObservableInput<unknown> = ObservableInput<T>, M = unknown> {\n  /**\n   * The time allowed between values from the source before timeout is triggered.\n   */\n  each?: number;\n\n  /**\n   * The relative time as a `number` in milliseconds, or a specific time as a `Date` object,\n   * by which the first value must arrive from the source before timeout is triggered.\n   */\n  first?: number | Date;\n\n  /**\n   * The scheduler to use with time-related operations within this operator. Defaults to {@link asyncScheduler}\n   */\n  scheduler?: SchedulerLike;\n\n  /**\n   * A factory used to create observable to switch to when timeout occurs. Provides\n   * a {@link TimeoutInfo} about the source observable's emissions and what delay or\n   * exact time triggered the timeout.\n   */\n  with?: (info: TimeoutInfo<T, M>) => O;\n\n  /**\n   * Optional additional metadata you can provide to code that handles\n   * the timeout, will be provided through the {@link TimeoutError}.\n   * This can be used to help identify the source of a timeout or pass along\n   * other information related to the timeout.\n   */\n  meta?: M;\n}\n\nexport interface TimeoutInfo<T, M = unknown> {\n  /** Optional metadata that was provided to the timeout configuration. */\n  readonly meta: M;\n  /** The number of messages seen before the timeout */\n  readonly seen: number;\n  /** The last message seen */\n  readonly lastValue: T | null;\n}\n\n/**\n * An error emitted when a timeout occurs.\n */\nexport interface TimeoutError<T = unknown, M = unknown> extends Error {\n  /**\n   * The information provided to the error by the timeout\n   * operation that created the error. Will be `null` if\n   * used directly in non-RxJS code with an empty constructor.\n   * (Note that using this constructor directly is not recommended,\n   * you should create your own errors)\n   */\n  info: TimeoutInfo<T, M> | null;\n}\n\nexport interface TimeoutErrorCtor {\n  /**\n   * @deprecated Internal implementation detail. Do not construct error instances.\n   * Cannot be tagged as internal: https://github.com/ReactiveX/rxjs/issues/6269\n   */\n  new <T = unknown, M = unknown>(info?: TimeoutInfo<T, M>): TimeoutError<T, M>;\n}\n\n/**\n * An error thrown by the {@link timeout} operator.\n *\n * Provided so users can use as a type and do quality comparisons.\n * We recommend you do not subclass this or create instances of this class directly.\n * If you have need of a error representing a timeout, you should\n * create your own error class and use that.\n *\n * @see {@link timeout}\n */\nexport const TimeoutError: TimeoutErrorCtor = createErrorClass(\n  (_super) =>\n    function TimeoutErrorImpl(this: any, info: TimeoutInfo<any> | null = null) {\n      _super(this);\n      this.message = 'Timeout has occurred';\n      this.name = 'TimeoutError';\n      this.info = info;\n    }\n);\n\n/**\n * If `with` is provided, this will return an observable that will switch to a different observable if the source\n * does not push values within the specified time parameters.\n *\n * <span class=\"informal\">The most flexible option for creating a timeout behavior.</span>\n *\n * The first thing to know about the configuration is if you do not provide a `with` property to the configuration,\n * when timeout conditions are met, this operator will emit a {@link TimeoutError}. Otherwise, it will use the factory\n * function provided by `with`, and switch your subscription to the result of that. Timeout conditions are provided by\n * the settings in `first` and `each`.\n *\n * The `first` property can be either a `Date` for a specific time, a `number` for a time period relative to the\n * point of subscription, or it can be skipped. This property is to check timeout conditions for the arrival of\n * the first value from the source _only_. The timings of all subsequent values  from the source will be checked\n * against the time period provided by `each`, if it was provided.\n *\n * The `each` property can be either a `number` or skipped. If a value for `each` is provided, it represents the amount of\n * time the resulting observable will wait between the arrival of values from the source before timing out. Note that if\n * `first` is _not_ provided, the value from `each` will be used to check timeout conditions for the arrival of the first\n * value and all subsequent values. If `first` _is_ provided, `each` will only be use to check all values after the first.\n *\n * ## Examples\n *\n * Emit a custom error if there is too much time between values\n *\n * ```ts\n * import { interval, timeout, throwError } from 'rxjs';\n *\n * class CustomTimeoutError extends Error {\n *   constructor() {\n *     super('It was too slow');\n *     this.name = 'CustomTimeoutError';\n *   }\n * }\n *\n * const slow$ = interval(900);\n *\n * slow$.pipe(\n *   timeout({\n *     each: 1000,\n *     with: () => throwError(() => new CustomTimeoutError())\n *   })\n * )\n * .subscribe({\n *   error: console.error\n * });\n * ```\n *\n * Switch to a faster observable if your source is slow.\n *\n * ```ts\n * import { interval, timeout } from 'rxjs';\n *\n * const slow$ = interval(900);\n * const fast$ = interval(500);\n *\n * slow$.pipe(\n *   timeout({\n *     each: 1000,\n *     with: () => fast$,\n *   })\n * )\n * .subscribe(console.log);\n * ```\n * @param config The configuration for the timeout.\n */\nexport function timeout<T, O extends ObservableInput<unknown>, M = unknown>(\n  config: TimeoutConfig<T, O, M> & { with: (info: TimeoutInfo<T, M>) => O }\n): OperatorFunction<T, T | ObservedValueOf<O>>;\n\n/**\n * Returns an observable that will error or switch to a different observable if the source does not push values\n * within the specified time parameters.\n *\n * <span class=\"informal\">The most flexible option for creating a timeout behavior.</span>\n *\n * The first thing to know about the configuration is if you do not provide a `with` property to the configuration,\n * when timeout conditions are met, this operator will emit a {@link TimeoutError}. Otherwise, it will use the factory\n * function provided by `with`, and switch your subscription to the result of that. Timeout conditions are provided by\n * the settings in `first` and `each`.\n *\n * The `first` property can be either a `Date` for a specific time, a `number` for a time period relative to the\n * point of subscription, or it can be skipped. This property is to check timeout conditions for the arrival of\n * the first value from the source _only_. The timings of all subsequent values  from the source will be checked\n * against the time period provided by `each`, if it was provided.\n *\n * The `each` property can be either a `number` or skipped. If a value for `each` is provided, it represents the amount of\n * time the resulting observable will wait between the arrival of values from the source before timing out. Note that if\n * `first` is _not_ provided, the value from `each` will be used to check timeout conditions for the arrival of the first\n * value and all subsequent values. If `first` _is_ provided, `each` will only be use to check all values after the first.\n *\n * ### Handling TimeoutErrors\n *\n * If no `with` property was provided, subscriptions to the resulting observable may emit an error of {@link TimeoutError}.\n * The timeout error provides useful information you can examine when you're handling the error. The most common way to handle\n * the error would be with {@link catchError}, although you could use {@link tap} or just the error handler in your `subscribe` call\n * directly, if your error handling is only a side effect (such as notifying the user, or logging).\n *\n * In this case, you would check the error for `instanceof TimeoutError` to validate that the error was indeed from `timeout`, and\n * not from some other source. If it's not from `timeout`, you should probably rethrow it if you're in a `catchError`.\n *\n * ## Examples\n *\n * Emit a {@link TimeoutError} if the first value, and _only_ the first value, does not arrive within 5 seconds\n *\n * ```ts\n * import { interval, timeout } from 'rxjs';\n *\n * // A random interval that lasts between 0 and 10 seconds per tick\n * const source$ = interval(Math.round(Math.random() * 10_000));\n *\n * source$.pipe(\n *   timeout({ first: 5_000 })\n * )\n * .subscribe({\n *   next: console.log,\n *   error: console.error\n * });\n * ```\n *\n * Emit a {@link TimeoutError} if the source waits longer than 5 seconds between any two values or the first value\n * and subscription.\n *\n * ```ts\n * import { timer, timeout, expand } from 'rxjs';\n *\n * const getRandomTime = () => Math.round(Math.random() * 10_000);\n *\n * // An observable that waits a random amount of time between each delivered value\n * const source$ = timer(getRandomTime())\n *   .pipe(expand(() => timer(getRandomTime())));\n *\n * source$\n *   .pipe(timeout({ each: 5_000 }))\n *   .subscribe({\n *     next: console.log,\n *     error: console.error\n *   });\n * ```\n *\n * Emit a {@link TimeoutError} if the source does not emit before 7 seconds, _or_ if the source waits longer than\n * 5 seconds between any two values after the first.\n *\n * ```ts\n * import { timer, timeout, expand } from 'rxjs';\n *\n * const getRandomTime = () => Math.round(Math.random() * 10_000);\n *\n * // An observable that waits a random amount of time between each delivered value\n * const source$ = timer(getRandomTime())\n *   .pipe(expand(() => timer(getRandomTime())));\n *\n * source$\n *   .pipe(timeout({ first: 7_000, each: 5_000 }))\n *   .subscribe({\n *     next: console.log,\n *     error: console.error\n *   });\n * ```\n */\nexport function timeout<T, M = unknown>(config: Omit<TimeoutConfig<T, any, M>, 'with'>): OperatorFunction<T, T>;\n\n/**\n * Returns an observable that will error if the source does not push its first value before the specified time passed as a `Date`.\n * This is functionally the same as `timeout({ first: someDate })`.\n *\n * <span class=\"informal\">Errors if the first value doesn't show up before the given date and time</span>\n *\n * ![](timeout.png)\n *\n * @param first The date to at which the resulting observable will timeout if the source observable\n * does not emit at least one value.\n * @param scheduler The scheduler to use. Defaults to {@link asyncScheduler}.\n */\nexport function timeout<T>(first: Date, scheduler?: SchedulerLike): MonoTypeOperatorFunction<T>;\n\n/**\n * Returns an observable that will error if the source does not push a value within the specified time in milliseconds.\n * This is functionally the same as `timeout({ each: milliseconds })`.\n *\n * <span class=\"informal\">Errors if it waits too long between any value</span>\n *\n * ![](timeout.png)\n *\n * @param each The time allowed between each pushed value from the source before the resulting observable\n * will timeout.\n * @param scheduler The scheduler to use. Defaults to {@link asyncScheduler}.\n */\nexport function timeout<T>(each: number, scheduler?: SchedulerLike): MonoTypeOperatorFunction<T>;\n\n/**\n *\n * Errors if Observable does not emit a value in given time span.\n *\n * <span class=\"informal\">Timeouts on Observable that doesn't emit values fast enough.</span>\n *\n * ![](timeout.png)\n *\n * @see {@link timeoutWith}\n *\n * @return A function that returns an Observable that mirrors behaviour of the\n * source Observable, unless timeout happens when it throws an error.\n */\nexport function timeout<T, O extends ObservableInput<any>, M>(\n  config: number | Date | TimeoutConfig<T, O, M>,\n  schedulerArg?: SchedulerLike\n): OperatorFunction<T, T | ObservedValueOf<O>> {\n  // Intentionally terse code.\n  // If the first argument is a valid `Date`, then we use it as the `first` config.\n  // Otherwise, if the first argument is a `number`, then we use it as the `each` config.\n  // Otherwise, it can be assumed the first argument is the configuration object itself, and\n  // we destructure that into what we're going to use, setting important defaults as we do.\n  // NOTE: The default for `scheduler` will be the `scheduler` argument if it exists, or\n  // it will default to the `asyncScheduler`.\n  const {\n    first,\n    each,\n    with: _with = timeoutErrorFactory,\n    scheduler = schedulerArg ?? asyncScheduler,\n    meta = null!,\n  } = (isValidDate(config) ? { first: config } : typeof config === 'number' ? { each: config } : config) as TimeoutConfig<T, O, M>;\n\n  if (first == null && each == null) {\n    // Ensure timeout was provided at runtime.\n    throw new TypeError('No timeout provided.');\n  }\n\n  return operate((source, subscriber) => {\n    // This subscription encapsulates our subscription to the\n    // source for this operator. We're capturing it separately,\n    // because if there is a `with` observable to fail over to,\n    // we want to unsubscribe from our original subscription, and\n    // hand of the subscription to that one.\n    let originalSourceSubscription: Subscription;\n    // The subscription for our timeout timer. This changes\n    // every time we get a new value.\n    let timerSubscription: Subscription;\n    // A bit of state we pass to our with and error factories to\n    // tell what the last value we saw was.\n    let lastValue: T | null = null;\n    // A bit of state we pass to the with and error factories to\n    // tell how many values we have seen so far.\n    let seen = 0;\n    const startTimer = (delay: number) => {\n      timerSubscription = executeSchedule(\n        subscriber,\n        scheduler,\n        () => {\n          try {\n            originalSourceSubscription.unsubscribe();\n            innerFrom(\n              _with!({\n                meta,\n                lastValue,\n                seen,\n              })\n            ).subscribe(subscriber);\n          } catch (err) {\n            subscriber.error(err);\n          }\n        },\n        delay\n      );\n    };\n\n    originalSourceSubscription = source.subscribe(\n      createOperatorSubscriber(\n        subscriber,\n        (value: T) => {\n          // clear the timer so we can emit and start another one.\n          timerSubscription?.unsubscribe();\n          seen++;\n          // Emit\n          subscriber.next((lastValue = value));\n          // null | undefined are both < 0. Thanks, JavaScript.\n          each! > 0 && startTimer(each!);\n        },\n        undefined,\n        undefined,\n        () => {\n          if (!timerSubscription?.closed) {\n            timerSubscription?.unsubscribe();\n          }\n          // Be sure not to hold the last value in memory after unsubscription\n          // it could be quite large.\n          lastValue = null;\n        }\n      )\n    );\n\n    // Intentionally terse code.\n    // If we've `seen` a value, that means the \"first\" clause was met already, if it existed.\n    //   it also means that a timer was already started for \"each\" (in the next handler above).\n    // If `first` was provided, and it's a number, then use it.\n    // If `first` was provided and it's not a number, it's a Date, and we get the difference between it and \"now\".\n    // If `first` was not provided at all, then our first timer will be the value from `each`.\n    !seen && startTimer(first != null ? (typeof first === 'number' ? first : +first - scheduler!.now()) : each!);\n  });\n}\n\n/**\n * The default function to use to emit an error when timeout occurs and a `with` function\n * is not specified.\n * @param info The information about the timeout to pass along to the error\n */\nfunction timeoutErrorFactory(info: TimeoutInfo<any>): Observable<never> {\n  throw new TimeoutError(info);\n}\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,oBAAoB;AAEnD,SAASC,WAAW,QAAQ,gBAAgB;AAE5C,SAASC,OAAO,QAAQ,cAAc;AAEtC,SAASC,SAAS,QAAQ,yBAAyB;AACnD,SAASC,gBAAgB,QAAQ,0BAA0B;AAC3D,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,SAASC,eAAe,QAAQ,yBAAyB;AA4EzD,OAAO,IAAMC,YAAY,GAAqBH,gBAAgB,CAC5D,UAACI,MAAM;EACL,gBAASC,gBAAgBA,CAAYC,IAAoC;IAApC,IAAAA,IAAA;MAAAA,IAAA,OAAoC;IAAA;IACvEF,MAAM,CAAC,IAAI,CAAC;IACZ,IAAI,CAACG,OAAO,GAAG,sBAAsB;IACrC,IAAI,CAACC,IAAI,GAAG,cAAc;IAC1B,IAAI,CAACF,IAAI,GAAGA,IAAI;EAClB,CAAC;AALD,CAKC,CACJ;AA6MD,OAAM,SAAUG,OAAOA,CACrBC,MAA8C,EAC9CC,YAA4B;EAStB,IAAAC,EAAA,GAMDf,WAAW,CAACa,MAAM,CAAC,GAAG;MAAEG,KAAK,EAAEH;IAAM,CAAE,GAAG,OAAOA,MAAM,KAAK,QAAQ,GAAG;MAAEI,IAAI,EAAEJ;IAAM,CAAE,GAAGA,MAAiC;IAL9HG,KAAK,GAAAD,EAAA,CAAAC,KAAA;IACLC,IAAI,GAAAF,EAAA,CAAAE,IAAA;IACJC,EAAA,GAAAH,EAAA,CAAAI,IAAiC;IAA3BC,KAAK,GAAAF,EAAA,cAAGG,mBAAmB,GAAAH,EAAA;IACjCI,EAAA,GAAAP,EAAA,CAAAQ,SAA0C;IAA1CA,SAAS,GAAAD,EAAA,cAAGR,YAAY,aAAZA,YAAY,cAAZA,YAAY,GAAIf,cAAc,GAAAuB,EAAA;IAC1CE,EAAA,GAAAT,EAAA,CAAAU,IAAY;IAAZA,IAAI,GAAAD,EAAA,cAAG,IAAK,GAAAA,EACkH;EAEhI,IAAIR,KAAK,IAAI,IAAI,IAAIC,IAAI,IAAI,IAAI,EAAE;IAEjC,MAAM,IAAIS,SAAS,CAAC,sBAAsB,CAAC;;EAG7C,OAAOzB,OAAO,CAAC,UAAC0B,MAAM,EAAEC,UAAU;IAMhC,IAAIC,0BAAwC;IAG5C,IAAIC,iBAA+B;IAGnC,IAAIC,SAAS,GAAa,IAAI;IAG9B,IAAIC,IAAI,GAAG,CAAC;IACZ,IAAMC,UAAU,GAAG,SAAAA,CAACC,KAAa;MAC/BJ,iBAAiB,GAAGzB,eAAe,CACjCuB,UAAU,EACVL,SAAS,EACT;QACE,IAAI;UACFM,0BAA0B,CAACM,WAAW,EAAE;UACxCjC,SAAS,CACPkB,KAAM,CAAC;YACLK,IAAI,EAAAA,IAAA;YACJM,SAAS,EAAAA,SAAA;YACTC,IAAI,EAAAA;WACL,CAAC,CACH,CAACI,SAAS,CAACR,UAAU,CAAC;SACxB,CAAC,OAAOS,GAAG,EAAE;UACZT,UAAU,CAACU,KAAK,CAACD,GAAG,CAAC;;MAEzB,CAAC,EACDH,KAAK,CACN;IACH,CAAC;IAEDL,0BAA0B,GAAGF,MAAM,CAACS,SAAS,CAC3ChC,wBAAwB,CACtBwB,UAAU,EACV,UAACW,KAAQ;MAEPT,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEK,WAAW,EAAE;MAChCH,IAAI,EAAE;MAENJ,UAAU,CAACY,IAAI,CAAET,SAAS,GAAGQ,KAAM,CAAC;MAEpCtB,IAAK,GAAG,CAAC,IAAIgB,UAAU,CAAChB,IAAK,CAAC;IAChC,CAAC,EACDwB,SAAS,EACTA,SAAS,EACT;MACE,IAAI,EAACX,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEY,MAAM,GAAE;QAC9BZ,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEK,WAAW,EAAE;;MAIlCJ,SAAS,GAAG,IAAI;IAClB,CAAC,CACF,CACF;IAQD,CAACC,IAAI,IAAIC,UAAU,CAACjB,KAAK,IAAI,IAAI,GAAI,OAAOA,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAG,CAACA,KAAK,GAAGO,SAAU,CAACoB,GAAG,EAAE,GAAI1B,IAAK,CAAC;EAC9G,CAAC,CAAC;AACJ;AAOA,SAASI,mBAAmBA,CAACZ,IAAsB;EACjD,MAAM,IAAIH,YAAY,CAACG,IAAI,CAAC;AAC9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}