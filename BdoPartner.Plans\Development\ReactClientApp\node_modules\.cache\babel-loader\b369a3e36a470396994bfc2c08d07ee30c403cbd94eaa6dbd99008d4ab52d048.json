{"ast": null, "code": "import http from \"../core/http/httpClient\";\nimport APP_CONFIG from \"../core/config/appConfig\";\nimport { ResultStatus } from \"../core/enumertions/resultStatus\";\n\n/**\r\n * Audit Service for handling audit-related API calls\r\n * Provides methods to retrieve audit log data\r\n */\nclass AuditService {\n  /**\r\n   * Get aggregated audit log data with optional filtering\r\n   * @param {string} formId - Optional form ID to filter audit records\r\n   * @param {string} logicCode - Optional logic code to filter audit records\r\n   * @param {string} targetTable - Optional target table name to filter audit records\r\n   * @param {string} targetColumn - Optional target column name to filter audit records\r\n   * @returns {Promise<Object|null>} Audit log result with summary and details or null if failed\r\n   */\n  async getAggregatedAuditLog(formId = null, logicCode = null, targetTable = null, targetColumn = null) {\n    try {\n      const params = new URLSearchParams();\n      if (formId) params.append('formId', formId);\n      if (logicCode) params.append('logicCode', logicCode);\n      if (targetTable) params.append('targetTable', targetTable);\n      if (targetColumn) params.append('targetColumn', targetColumn);\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/audit/GetAggregatedAuditLog?${params.toString()}`);\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\n        return response.data.item;\n      } else {\n        var _response$data, _response$data2;\n        console.error(\"Failed to get aggregated audit log:\", (_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.message);\n        throw new Error(((_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : _response$data2.message) || \"Failed to load audit log data\");\n      }\n    } catch (error) {\n      console.error(\"Error getting aggregated audit log:\", error);\n      throw error;\n    }\n  }\n\n  /**\r\n   * Get audit log data for a specific form\r\n   * @param {string} formId - Form ID to filter audit records\r\n   * @returns {Promise<Object|null>} Audit log result for the specified form or null if failed\r\n   */\n  async getAuditLogByForm(formId) {\n    try {\n      if (!formId) {\n        throw new Error(\"Form ID is required\");\n      }\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/audit/GetByForm/${formId}`);\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\n        return response.data.item;\n      } else {\n        var _response$data3, _response$data4;\n        console.error(\"Failed to get audit log by form:\", (_response$data3 = response.data) === null || _response$data3 === void 0 ? void 0 : _response$data3.message);\n        throw new Error(((_response$data4 = response.data) === null || _response$data4 === void 0 ? void 0 : _response$data4.message) || \"Failed to load audit log data for form\");\n      }\n    } catch (error) {\n      console.error(\"Error getting audit log by form:\", error);\n      throw error;\n    }\n  }\n\n  /**\r\n   * Get audit log data for specific table and column changes\r\n   * @param {string} targetTable - Target table name to filter audit records\r\n   * @param {string} targetColumn - Optional target column name to filter audit records\r\n   * @returns {Promise<Object|null>} Audit log result for the specified table/column or null if failed\r\n   */\n  async getAuditLogByTableColumn(targetTable, targetColumn = null) {\n    try {\n      if (!targetTable) {\n        throw new Error(\"Target table is required\");\n      }\n      const params = new URLSearchParams();\n      params.append('targetTable', targetTable);\n      if (targetColumn) params.append('targetColumn', targetColumn);\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/audit/GetByTableColumn?${params.toString()}`);\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\n        return response.data.item;\n      } else {\n        var _response$data5, _response$data6;\n        console.error(\"Failed to get audit log by table/column:\", (_response$data5 = response.data) === null || _response$data5 === void 0 ? void 0 : _response$data5.message);\n        throw new Error(((_response$data6 = response.data) === null || _response$data6 === void 0 ? void 0 : _response$data6.message) || \"Failed to load audit log data for table/column\");\n      }\n    } catch (error) {\n      console.error(\"Error getting audit log by table/column:\", error);\n      throw error;\n    }\n  }\n\n  /**\r\n   * Get admin modification audit history for a specific form\r\n   * Convenience method that filters by AdminModifyCompletedPlan logic code and UserAnswer table\r\n   * @param {string} formId - Form ID to filter audit records\r\n   * @returns {Promise<Object|null>} Admin modification audit history or null if failed\r\n   */\n  async getAdminModificationHistory(formId) {\n    try {\n      return await this.getAggregatedAuditLog(formId, \"AdminModifyCompletedPlan\", \"UserAnswer\", \"Answer\");\n    } catch (error) {\n      console.error(\"Error getting admin modification history:\", error);\n      throw error;\n    }\n  }\n}\n\n// Export a singleton instance\nconst auditService = new AuditService();\nexport default auditService;", "map": {"version": 3, "names": ["http", "APP_CONFIG", "ResultStatus", "AuditService", "getAggregatedAuditLog", "formId", "logicCode", "targetTable", "targetColumn", "params", "URLSearchParams", "append", "response", "get", "apiDomain", "toString", "data", "resultStatus", "Success", "item", "_response$data", "_response$data2", "console", "error", "message", "Error", "getAuditLogByForm", "_response$data3", "_response$data4", "getAuditLogByTableColumn", "_response$data5", "_response$data6", "getAdminModificationHistory", "auditService"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/services/auditService.js"], "sourcesContent": ["import http from \"../core/http/httpClient\";\r\nimport APP_CONFIG from \"../core/config/appConfig\";\r\nimport { ResultStatus } from \"../core/enumertions/resultStatus\";\r\n\r\n/**\r\n * Audit Service for handling audit-related API calls\r\n * Provides methods to retrieve audit log data\r\n */\r\nclass AuditService {\r\n  /**\r\n   * Get aggregated audit log data with optional filtering\r\n   * @param {string} formId - Optional form ID to filter audit records\r\n   * @param {string} logicCode - Optional logic code to filter audit records\r\n   * @param {string} targetTable - Optional target table name to filter audit records\r\n   * @param {string} targetColumn - Optional target column name to filter audit records\r\n   * @returns {Promise<Object|null>} Audit log result with summary and details or null if failed\r\n   */\r\n  async getAggregatedAuditLog(formId = null, logicCode = null, targetTable = null, targetColumn = null) {\r\n    try {\r\n      const params = new URLSearchParams();\r\n      if (formId) params.append('formId', formId);\r\n      if (logicCode) params.append('logicCode', logicCode);\r\n      if (targetTable) params.append('targetTable', targetTable);\r\n      if (targetColumn) params.append('targetColumn', targetColumn);\r\n\r\n      const response = await http.get(\r\n        `${APP_CONFIG.apiDomain}/api/audit/GetAggregatedAuditLog?${params.toString()}`\r\n      );\r\n\r\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\r\n        return response.data.item;\r\n      } else {\r\n        console.error(\"Failed to get aggregated audit log:\", response.data?.message);\r\n        throw new Error(response.data?.message || \"Failed to load audit log data\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error getting aggregated audit log:\", error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get audit log data for a specific form\r\n   * @param {string} formId - Form ID to filter audit records\r\n   * @returns {Promise<Object|null>} Audit log result for the specified form or null if failed\r\n   */\r\n  async getAuditLogByForm(formId) {\r\n    try {\r\n      if (!formId) {\r\n        throw new Error(\"Form ID is required\");\r\n      }\r\n\r\n      const response = await http.get(\r\n        `${APP_CONFIG.apiDomain}/api/audit/GetByForm/${formId}`\r\n      );\r\n\r\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\r\n        return response.data.item;\r\n      } else {\r\n        console.error(\"Failed to get audit log by form:\", response.data?.message);\r\n        throw new Error(response.data?.message || \"Failed to load audit log data for form\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error getting audit log by form:\", error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get audit log data for specific table and column changes\r\n   * @param {string} targetTable - Target table name to filter audit records\r\n   * @param {string} targetColumn - Optional target column name to filter audit records\r\n   * @returns {Promise<Object|null>} Audit log result for the specified table/column or null if failed\r\n   */\r\n  async getAuditLogByTableColumn(targetTable, targetColumn = null) {\r\n    try {\r\n      if (!targetTable) {\r\n        throw new Error(\"Target table is required\");\r\n      }\r\n\r\n      const params = new URLSearchParams();\r\n      params.append('targetTable', targetTable);\r\n      if (targetColumn) params.append('targetColumn', targetColumn);\r\n\r\n      const response = await http.get(\r\n        `${APP_CONFIG.apiDomain}/api/audit/GetByTableColumn?${params.toString()}`\r\n      );\r\n\r\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\r\n        return response.data.item;\r\n      } else {\r\n        console.error(\"Failed to get audit log by table/column:\", response.data?.message);\r\n        throw new Error(response.data?.message || \"Failed to load audit log data for table/column\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error getting audit log by table/column:\", error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get admin modification audit history for a specific form\r\n   * Convenience method that filters by AdminModifyCompletedPlan logic code and UserAnswer table\r\n   * @param {string} formId - Form ID to filter audit records\r\n   * @returns {Promise<Object|null>} Admin modification audit history or null if failed\r\n   */\r\n  async getAdminModificationHistory(formId) {\r\n    try {\r\n      return await this.getAggregatedAuditLog(\r\n        formId,\r\n        \"AdminModifyCompletedPlan\",\r\n        \"UserAnswer\",\r\n        \"Answer\"\r\n      );\r\n    } catch (error) {\r\n      console.error(\"Error getting admin modification history:\", error);\r\n      throw error;\r\n    }\r\n  }\r\n}\r\n\r\n// Export a singleton instance\r\nconst auditService = new AuditService();\r\nexport default auditService;\r\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,yBAAyB;AAC1C,OAAOC,UAAU,MAAM,0BAA0B;AACjD,SAASC,YAAY,QAAQ,kCAAkC;;AAE/D;AACA;AACA;AACA;AACA,MAAMC,YAAY,CAAC;EACjB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,MAAMC,qBAAqBA,CAACC,MAAM,GAAG,IAAI,EAAEC,SAAS,GAAG,IAAI,EAAEC,WAAW,GAAG,IAAI,EAAEC,YAAY,GAAG,IAAI,EAAE;IACpG,IAAI;MACF,MAAMC,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;MACpC,IAAIL,MAAM,EAAEI,MAAM,CAACE,MAAM,CAAC,QAAQ,EAAEN,MAAM,CAAC;MAC3C,IAAIC,SAAS,EAAEG,MAAM,CAACE,MAAM,CAAC,WAAW,EAAEL,SAAS,CAAC;MACpD,IAAIC,WAAW,EAAEE,MAAM,CAACE,MAAM,CAAC,aAAa,EAAEJ,WAAW,CAAC;MAC1D,IAAIC,YAAY,EAAEC,MAAM,CAACE,MAAM,CAAC,cAAc,EAAEH,YAAY,CAAC;MAE7D,MAAMI,QAAQ,GAAG,MAAMZ,IAAI,CAACa,GAAG,CAC7B,GAAGZ,UAAU,CAACa,SAAS,oCAAoCL,MAAM,CAACM,QAAQ,CAAC,CAAC,EAC9E,CAAC;MAED,IAAIH,QAAQ,CAACI,IAAI,IAAIJ,QAAQ,CAACI,IAAI,CAACC,YAAY,KAAKf,YAAY,CAACgB,OAAO,EAAE;QACxE,OAAON,QAAQ,CAACI,IAAI,CAACG,IAAI;MAC3B,CAAC,MAAM;QAAA,IAAAC,cAAA,EAAAC,eAAA;QACLC,OAAO,CAACC,KAAK,CAAC,qCAAqC,GAAAH,cAAA,GAAER,QAAQ,CAACI,IAAI,cAAAI,cAAA,uBAAbA,cAAA,CAAeI,OAAO,CAAC;QAC5E,MAAM,IAAIC,KAAK,CAAC,EAAAJ,eAAA,GAAAT,QAAQ,CAACI,IAAI,cAAAK,eAAA,uBAAbA,eAAA,CAAeG,OAAO,KAAI,+BAA+B,CAAC;MAC5E;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAMG,iBAAiBA,CAACrB,MAAM,EAAE;IAC9B,IAAI;MACF,IAAI,CAACA,MAAM,EAAE;QACX,MAAM,IAAIoB,KAAK,CAAC,qBAAqB,CAAC;MACxC;MAEA,MAAMb,QAAQ,GAAG,MAAMZ,IAAI,CAACa,GAAG,CAC7B,GAAGZ,UAAU,CAACa,SAAS,wBAAwBT,MAAM,EACvD,CAAC;MAED,IAAIO,QAAQ,CAACI,IAAI,IAAIJ,QAAQ,CAACI,IAAI,CAACC,YAAY,KAAKf,YAAY,CAACgB,OAAO,EAAE;QACxE,OAAON,QAAQ,CAACI,IAAI,CAACG,IAAI;MAC3B,CAAC,MAAM;QAAA,IAAAQ,eAAA,EAAAC,eAAA;QACLN,OAAO,CAACC,KAAK,CAAC,kCAAkC,GAAAI,eAAA,GAAEf,QAAQ,CAACI,IAAI,cAAAW,eAAA,uBAAbA,eAAA,CAAeH,OAAO,CAAC;QACzE,MAAM,IAAIC,KAAK,CAAC,EAAAG,eAAA,GAAAhB,QAAQ,CAACI,IAAI,cAAAY,eAAA,uBAAbA,eAAA,CAAeJ,OAAO,KAAI,wCAAwC,CAAC;MACrF;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,MAAMM,wBAAwBA,CAACtB,WAAW,EAAEC,YAAY,GAAG,IAAI,EAAE;IAC/D,IAAI;MACF,IAAI,CAACD,WAAW,EAAE;QAChB,MAAM,IAAIkB,KAAK,CAAC,0BAA0B,CAAC;MAC7C;MAEA,MAAMhB,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;MACpCD,MAAM,CAACE,MAAM,CAAC,aAAa,EAAEJ,WAAW,CAAC;MACzC,IAAIC,YAAY,EAAEC,MAAM,CAACE,MAAM,CAAC,cAAc,EAAEH,YAAY,CAAC;MAE7D,MAAMI,QAAQ,GAAG,MAAMZ,IAAI,CAACa,GAAG,CAC7B,GAAGZ,UAAU,CAACa,SAAS,+BAA+BL,MAAM,CAACM,QAAQ,CAAC,CAAC,EACzE,CAAC;MAED,IAAIH,QAAQ,CAACI,IAAI,IAAIJ,QAAQ,CAACI,IAAI,CAACC,YAAY,KAAKf,YAAY,CAACgB,OAAO,EAAE;QACxE,OAAON,QAAQ,CAACI,IAAI,CAACG,IAAI;MAC3B,CAAC,MAAM;QAAA,IAAAW,eAAA,EAAAC,eAAA;QACLT,OAAO,CAACC,KAAK,CAAC,0CAA0C,GAAAO,eAAA,GAAElB,QAAQ,CAACI,IAAI,cAAAc,eAAA,uBAAbA,eAAA,CAAeN,OAAO,CAAC;QACjF,MAAM,IAAIC,KAAK,CAAC,EAAAM,eAAA,GAAAnB,QAAQ,CAACI,IAAI,cAAAe,eAAA,uBAAbA,eAAA,CAAeP,OAAO,KAAI,gDAAgD,CAAC;MAC7F;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChE,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,MAAMS,2BAA2BA,CAAC3B,MAAM,EAAE;IACxC,IAAI;MACF,OAAO,MAAM,IAAI,CAACD,qBAAqB,CACrCC,MAAM,EACN,0BAA0B,EAC1B,YAAY,EACZ,QACF,CAAC;IACH,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACjE,MAAMA,KAAK;IACb;EACF;AACF;;AAEA;AACA,MAAMU,YAAY,GAAG,IAAI9B,YAAY,CAAC,CAAC;AACvC,eAAe8B,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}