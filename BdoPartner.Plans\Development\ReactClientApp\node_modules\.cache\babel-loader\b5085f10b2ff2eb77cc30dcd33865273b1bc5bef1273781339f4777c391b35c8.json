{"ast": null, "code": "import APP_CONFIG from \"../../core/config/appConfig\";\nimport { Language } from \"../../core/enumertions/language\";\nimport CORE_ACTIONS from \"../actions\";\n\n/**\r\n * language setup reducer. Keep the selected language code in local storage.\r\n */\nconst languageReducer = (state = Language.English, action) => {\n  switch (action.type) {\n    //\n    // When selected langauge from the langauge drop down on the top menu, following action got triggered.\n    //\n    case CORE_ACTIONS.SET_LANGUAGE:\n      //\n      // Keep the selected language in the local storage.\n      // Help httpClient to get current selected langauge and inject langauge into the http request header.\n      // corpoate with httpClient.js\n      //\n      localStorage.setItem(`${APP_CONFIG.clientId}_CurrentLanguage`, action.payload);\n      return action.payload;\n    // it is new selected language code.\n    default:\n      return state;\n  }\n};\nexport default languageReducer;", "map": {"version": 3, "names": ["APP_CONFIG", "Language", "CORE_ACTIONS", "languageReducer", "state", "English", "action", "type", "SET_LANGUAGE", "localStorage", "setItem", "clientId", "payload"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/redux/reducers/languageReducer.js"], "sourcesContent": ["import APP_CONFIG from \"../../core/config/appConfig\";\r\nimport { Language } from \"../../core/enumertions/language\";\r\nimport CORE_ACTIONS from \"../actions\";\r\n\r\n/**\r\n * language setup reducer. Keep the selected language code in local storage.\r\n */\r\nconst languageReducer = (state = Language.English, action) => {\r\n  switch (action.type) {\r\n    //\r\n    // When selected langauge from the langauge drop down on the top menu, following action got triggered.\r\n    //\r\n    case CORE_ACTIONS.SET_LANGUAGE:\r\n      //\r\n      // Keep the selected language in the local storage.\r\n      // Help httpClient to get current selected langauge and inject langauge into the http request header.\r\n      // corpoate with httpClient.js\r\n      //\r\n      localStorage.setItem(\r\n        `${APP_CONFIG.clientId}_CurrentLanguage`,\r\n        action.payload\r\n      );\r\n\r\n      return action.payload; // it is new selected language code.\r\n    default:\r\n      return state;\r\n  }\r\n};\r\n\r\nexport default languageReducer;\r\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,6BAA6B;AACpD,SAASC,QAAQ,QAAQ,iCAAiC;AAC1D,OAAOC,YAAY,MAAM,YAAY;;AAErC;AACA;AACA;AACA,MAAMC,eAAe,GAAGA,CAACC,KAAK,GAAGH,QAAQ,CAACI,OAAO,EAAEC,MAAM,KAAK;EAC5D,QAAQA,MAAM,CAACC,IAAI;IACjB;IACA;IACA;IACA,KAAKL,YAAY,CAACM,YAAY;MAC5B;MACA;MACA;MACA;MACA;MACAC,YAAY,CAACC,OAAO,CAClB,GAAGV,UAAU,CAACW,QAAQ,kBAAkB,EACxCL,MAAM,CAACM,OACT,CAAC;MAED,OAAON,MAAM,CAACM,OAAO;IAAE;IACzB;MACE,OAAOR,KAAK;EAChB;AACF,CAAC;AAED,eAAeD,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}