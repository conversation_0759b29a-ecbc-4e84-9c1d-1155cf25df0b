{"ast": null, "code": "import i18n from \"i18next\";\nimport { initReactI18next } from \"react-i18next\";\nimport Backend from \"i18next-http-backend\";\nimport LanguageDetector from \"i18next-browser-languagedetector\";\nimport APP_CONFIG from \"./appConfig\";\n\n/**\r\n * Work for multiple languages support.\r\n *\r\n * Reference: https://www.cluemediator.com/implement-multi-languages-in-react\r\n */\ni18n\n// load translation using http -> see /public/locales (i.e. https://github.com/i18next/react-i18next/tree/master/example/react/public/locales)\n// learn more: https://github.com/i18next/i18next-http-backend\n.use(Backend)\n// detect user language\n// learn more: https://github.com/i18next/i18next-browser-languageDetector\n.use(LanguageDetector)\n// pass the i18n instance to react-i18next.\n.use(initReactI18next)\n// init i18next\n// for all options read: https://www.i18next.com/overview/configuration-options\n.init({\n  lng: \"en\",\n  backend: {\n    /* translation file path. Note: basePath default value = \"/\"  */\n    loadPath: `${APP_CONFIG.basePath}/assets/i18n/{{ns}}/{{lng}}.json`\n  },\n  fallbackLng: \"en\",\n  debug: false,\n  /**\r\n   * can have multiple namespace,\r\n   * in case you want to divide a huge translation\r\n   * into smaller pieces and load them on demand\r\n   **/\n  ns: [\"translations\"],\n  defaultNS: \"translations\",\n  // keySeparator: false, //Note: Diabled following line to support nested key in json files. (check json format in en.json or fr.json)\n  interpolation: {\n    escapeValue: false,\n    formatSeparator: \",\"\n  },\n  react: {\n    wait: true\n  }\n});\nexport default i18n;", "map": {"version": 3, "names": ["i18n", "initReactI18next", "Backend", "LanguageDetector", "APP_CONFIG", "use", "init", "lng", "backend", "loadPath", "basePath", "fallbackLng", "debug", "ns", "defaultNS", "interpolation", "escapeValue", "formatSeparator", "react", "wait"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/core/config/i18nConfig.js"], "sourcesContent": ["import i18n from \"i18next\";\r\nimport { initReactI18next } from \"react-i18next\";\r\n\r\nimport Backend from \"i18next-http-backend\";\r\nimport LanguageDetector from \"i18next-browser-languagedetector\";\r\nimport APP_CONFIG from \"./appConfig\";\r\n\r\n/**\r\n * Work for multiple languages support.\r\n *\r\n * Reference: https://www.cluemediator.com/implement-multi-languages-in-react\r\n */\r\ni18n\r\n  // load translation using http -> see /public/locales (i.e. https://github.com/i18next/react-i18next/tree/master/example/react/public/locales)\r\n  // learn more: https://github.com/i18next/i18next-http-backend\r\n  .use(Backend)\r\n  // detect user language\r\n  // learn more: https://github.com/i18next/i18next-browser-languageDetector\r\n  .use(LanguageDetector)\r\n  // pass the i18n instance to react-i18next.\r\n  .use(initReactI18next)\r\n  // init i18next\r\n  // for all options read: https://www.i18next.com/overview/configuration-options\r\n  .init({\r\n    lng: \"en\",\r\n    backend: {\r\n      /* translation file path. Note: basePath default value = \"/\"  */\r\n      loadPath: `${APP_CONFIG.basePath}/assets/i18n/{{ns}}/{{lng}}.json`,\r\n    },\r\n    fallbackLng: \"en\",\r\n    debug: false,\r\n    /**\r\n     * can have multiple namespace,\r\n     * in case you want to divide a huge translation\r\n     * into smaller pieces and load them on demand\r\n     **/\r\n    ns: [\"translations\"],\r\n    defaultNS: \"translations\",\r\n    // keySeparator: false, //Note: Diabled following line to support nested key in json files. (check json format in en.json or fr.json)\r\n    interpolation: {\r\n      escapeValue: false,\r\n      formatSeparator: \",\",\r\n    },\r\n    react: {\r\n      wait: true,\r\n    },\r\n  });\r\n\r\nexport default i18n;\r\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,SAAS;AAC1B,SAASC,gBAAgB,QAAQ,eAAe;AAEhD,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,OAAOC,UAAU,MAAM,aAAa;;AAEpC;AACA;AACA;AACA;AACA;AACAJ;AACE;AACA;AAAA,CACCK,GAAG,CAACH,OAAO;AACZ;AACA;AAAA,CACCG,GAAG,CAACF,gBAAgB;AACrB;AAAA,CACCE,GAAG,CAACJ,gBAAgB;AACrB;AACA;AAAA,CACCK,IAAI,CAAC;EACJC,GAAG,EAAE,IAAI;EACTC,OAAO,EAAE;IACP;IACAC,QAAQ,EAAE,GAAGL,UAAU,CAACM,QAAQ;EAClC,CAAC;EACDC,WAAW,EAAE,IAAI;EACjBC,KAAK,EAAE,KAAK;EACZ;AACJ;AACA;AACA;AACA;EACIC,EAAE,EAAE,CAAC,cAAc,CAAC;EACpBC,SAAS,EAAE,cAAc;EACzB;EACAC,aAAa,EAAE;IACbC,WAAW,EAAE,KAAK;IAClBC,eAAe,EAAE;EACnB,CAAC;EACDC,KAAK,EAAE;IACLC,IAAI,EAAE;EACR;AACF,CAAC,CAAC;AAEJ,eAAenB,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}