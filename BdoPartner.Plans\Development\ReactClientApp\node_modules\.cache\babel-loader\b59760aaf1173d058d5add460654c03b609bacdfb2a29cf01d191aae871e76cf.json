{"ast": null, "code": "import { observable as Symbol_observable } from '../symbol/observable';\nimport { isFunction } from './isFunction';\nexport function isInteropObservable(input) {\n  return isFunction(input[Symbol_observable]);\n}", "map": {"version": 3, "names": ["observable", "Symbol_observable", "isFunction", "isInteropObservable", "input"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\util\\isInteropObservable.ts"], "sourcesContent": ["import { InteropObservable } from '../types';\nimport { observable as Symbol_observable } from '../symbol/observable';\nimport { isFunction } from './isFunction';\n\n/** Identifies an input as being Observable (but not necessary an Rx Observable) */\nexport function isInteropObservable(input: any): input is InteropObservable<any> {\n  return isFunction(input[Symbol_observable]);\n}\n"], "mappings": "AACA,SAASA,UAAU,IAAIC,iBAAiB,QAAQ,sBAAsB;AACtE,SAASC,UAAU,QAAQ,cAAc;AAGzC,OAAM,SAAUC,mBAAmBA,CAACC,KAAU;EAC5C,OAAOF,UAAU,CAACE,KAAK,CAACH,iBAAiB,CAAC,CAAC;AAC7C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}