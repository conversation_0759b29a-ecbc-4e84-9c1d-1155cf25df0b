{"ast": null, "code": "var _jsxFileName = \"C:\\\\git\\\\Partner-Site\\\\BdoPartner.Plans\\\\Development\\\\ReactClientApp\\\\src\\\\pages\\\\admin\\\\questionnaireDesigner.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from \"react\";\nimport { useParams, useNavigate } from \"react-router-dom\";\nimport { Toast } from \"primereact/toast\";\nimport questionnaireService from \"../../services/questionnaireService\";\nimport { loadingService } from \"../../core/loading/loadingService\";\nimport { messageService } from \"../../core/message/messageService\";\nimport { QuestionnaireDesignerCore } from \"../../components/admin/QuestionnaireDesignerCore\";\nimport { QuestionnaireDesignerHeader } from \"../../components/admin/QuestionnaireDesignerHeader\";\nimport { isQuestionnaireEditable } from \"../../core/enumertions/questionnaireStatus\";\n// Add the import for the Year End Review popup\nimport BatchReopenPopup from \"../../components/admin/batchReopenPopup\";\n// Add the import for the Cycle Publish Confirmation popup\nimport CyclePublishConfirmationPopup from \"../../components/admin/CyclePublishConfirmationPopup\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const QuestionnaireDesigner = () => {\n  _s();\n  var _coreRef$current;\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const [questionnaire, setQuestionnaire] = useState(null);\n  const [surveyName, setSurveyName] = useState(\"\");\n  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());\n  const [enabledCycles, setEnabledCycles] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [isQuestionnaireLoaded, setIsQuestionnaireLoaded] = useState(false);\n  // Add state for the Year End Review popup\n  const [showBatchReopenPopup, setShowBatchReopenPopup] = useState(false);\n  // Add state for the Cycle Publish Confirmation popup\n  const [showCyclePublishPopup, setShowCyclePublishPopup] = useState(false);\n  const [pendingCyclePublish, setPendingCyclePublish] = useState(null);\n  const [uncompletedFormsCount, setUncompletedFormsCount] = useState(0);\n  const [cyclePublishLoading, setCyclePublishLoading] = useState(false);\n  const [cyclePublishError, setCyclePublishError] = useState(null);\n  const toast = useRef(null);\n  const coreRef = useRef(null);\n\n  // Load questionnaire data after component is initialized\n  // This ensures we have a valid questionnaire ID before attempting to load\n  useEffect(() => {\n    if (id && id !== \"undefined\" && id !== \"null\") {\n      loadQuestionnaire();\n    } else {\n      console.error(\"Invalid questionnaire ID provided:\", id);\n      messageService.errorToast(\"Invalid questionnaire ID\");\n      navigate(\"/admin/questionnaire-management\");\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [id]);\n  const loadQuestionnaire = async () => {\n    try {\n      setLoading(true);\n      setIsQuestionnaireLoaded(false);\n      loadingService.httpRequestSent();\n      const data = await questionnaireService.getQuestionnaireById(id);\n      if (data) {\n        // Validate that we have all required questionnaire data\n        const isValidQuestionnaire = data && data.id && data.name && typeof data.year === \"number\" && data.year > 0;\n        if (!isValidQuestionnaire) {\n          console.error(\"Invalid questionnaire data received:\", data);\n          messageService.errorToast(\"Invalid questionnaire data received\");\n          navigate(\"/admin/questionnaire-management\");\n          return;\n        }\n\n        // Set all questionnaire-related state synchronously\n        setQuestionnaire(data);\n        setSurveyName(data.name);\n        setSelectedYear(data.year);\n\n        // Populate enabled cycles from the questionnaire record\n        if (data.enableCycles) {\n          const cycles = data.enableCycles.split(\",\").map(Number);\n          setEnabledCycles(cycles);\n        } else {\n          setEnabledCycles([]);\n        }\n\n        // Mark questionnaire as loaded only after all state is set\n        // Use requestAnimationFrame to ensure state updates are processed\n        requestAnimationFrame(() => {\n          // TODO. Add some deplay time to make sure all state is updated before marking questionnaire as loaded\n          setTimeout(() => {\n            setIsQuestionnaireLoaded(true);\n            console.log(\"Questionnaire fully loaded and marked as ready:\", {\n              id: data.id,\n              name: data.name,\n              year: data.year,\n              hasDefinition: !!data.draftDefinitionJson\n            });\n          }, 1000);\n        });\n      } else {\n        messageService.errorToast(\"Failed to load questionnaire\");\n        navigate(\"/admin/questionnaire-management\");\n      }\n    } catch (error) {\n      console.error(\"Error loading questionnaire:\", error);\n      messageService.errorToast(\"Error loading questionnaire\");\n      navigate(\"/admin/questionnaire-management\");\n    } finally {\n      setLoading(false);\n      loadingService.httpResponseReceived();\n    }\n  };\n\n  // Handler for save action from shared component\n  const handleSave = async (surveyJson, showMessage = true) => {\n    console.log(\"Save attempt - Survey name:\", surveyName, \"Year:\", selectedYear);\n\n    // Check if survey name and year are available\n    if (!surveyName || !selectedYear) {\n      console.error(\"Survey name or year not available - cannot save survey\", {\n        surveyName,\n        selectedYear\n      });\n      if (showMessage) {\n        messageService.errorToast(\"Survey name or year not available. Please check the form fields.\");\n      }\n      return;\n    }\n\n    // Ensure we have valid survey data\n    if (!surveyJson || Object.keys(surveyJson).length === 0) {\n      console.error(\"Survey JSON is empty or invalid\");\n      if (showMessage) {\n        messageService.errorToast(\"No form data to save. Please add some questions first.\");\n      }\n      return;\n    }\n    try {\n      const definitionJson = JSON.stringify(surveyJson);\n\n      // Convert enabled cycles array to comma-separated string for storage\n      const enableCyclesString = enabledCycles.join(\",\");\n      const updatedQuestionnaire = await questionnaireService.saveDraft(questionnaire, surveyName, selectedYear, definitionJson, enableCyclesString);\n      if (showMessage) {\n        messageService.successToast(\"Questionnaire saved successfully\");\n      }\n      setQuestionnaire(updatedQuestionnaire);\n    } catch (error) {\n      console.error(\"Error saving questionnaire:\", error);\n      if (showMessage) {\n        messageService.errorToast(error.message || \"Failed to save questionnaire\");\n      }\n    }\n  };\n\n  // Handler for publish action from shared component\n  const handlePublish = async surveyJson => {\n    try {\n      const isRepublish = (questionnaire === null || questionnaire === void 0 ? void 0 : questionnaire.status) === 1;\n\n      // Check if there's already another active questionnaire in the same year\n      // This applies to both first-time publishing and republishing\n      const hasActiveInYear = await questionnaireService.hasActiveQuestionnaireInYear(selectedYear, questionnaire === null || questionnaire === void 0 ? void 0 : questionnaire.id);\n      if (hasActiveInYear) {\n        messageService.errorToast(`${selectedYear} year already has an active questionnaire. Only one active questionnaire is allowed per year.`);\n        return;\n      }\n      const definitionJson = JSON.stringify(surveyJson);\n\n      // Convert enabled cycles array to comma-separated string for storage\n      const enableCyclesString = enabledCycles.join(\",\");\n      const publishedQuestionnaire = await questionnaireService.publishWithDefinition(questionnaire, surveyName, selectedYear, definitionJson, enableCyclesString);\n      const successMessage = isRepublish ? \"Questionnaire republished successfully\" : \"Questionnaire published successfully\";\n      messageService.successToast(successMessage);\n      setQuestionnaire(publishedQuestionnaire);\n    } catch (error) {\n      console.error(\"Error publishing questionnaire:\", error);\n      messageService.errorToast(error.message || \"Failed to publish questionnaire\");\n    }\n  };\n\n  // Handler for back to list action\n  const handleBackToList = () => {\n    navigate(\"/admin/questionnaire-management\");\n  };\n\n  // Handler for survey name changes\n  const handleSurveyNameChange = newName => {\n    setSurveyName(newName);\n  };\n\n  // Handler for year changes\n  const handleYearChange = newYear => {\n    setSelectedYear(newYear);\n  };\n\n  // Handler for enabled cycles changes\n  const handleEnabledCyclesChange = newEnabledCycles => {\n    setEnabledCycles(newEnabledCycles);\n  };\n\n  // Handler for publishing individual cycles\n  const handlePublishCycle = async cycleValue => {\n    try {\n      // First, get the summary of uncompleted forms\n      const summary = await questionnaireService.getUncompletedFormsCount(questionnaire.id, cycleValue);\n\n      // Store the cycle info and show the confirmation popup\n      const cycleName = getCycleName(cycleValue);\n      setPendingCyclePublish({\n        cycleValue,\n        cycleName\n      });\n      setUncompletedFormsCount(summary);\n      setShowCyclePublishPopup(true);\n    } catch (error) {\n      console.error(\"Error getting uncompleted forms count:\", error);\n      messageService.errorToast(error.message || \"Error getting uncompleted forms count\");\n    }\n  };\n\n  // Helper function to get cycle name\n  const getCycleName = cycleValue => {\n    switch (cycleValue) {\n      case 0:\n        return \"Planning\";\n      case 1:\n        return \"Mid Year Review\";\n      case 2:\n        return \"Year End Review\";\n      default:\n        return \"Unknown\";\n    }\n  };\n\n  // Handler for confirming cycle publish from popup\n  const handleConfirmCyclePublish = async () => {\n    if (!pendingCyclePublish) return;\n    try {\n      setCyclePublishLoading(true);\n      setCyclePublishError(null);\n      const result = await questionnaireService.publishCycle(questionnaire.id, pendingCyclePublish.cycleValue);\n\n      // Update the enabled cycles from the returned result\n      if (result && result.enabledCycles !== undefined) {\n        // Convert byte back to array of cycle numbers\n        const cycles = [];\n        for (let i = 0; i < 3; i++) {\n          if (result.enabledCycles & 1 << i) {\n            cycles.push(i);\n          }\n        }\n        setEnabledCycles(cycles);\n      }\n\n      // Show success message with update count\n      const updateMessage = result.updatedFormsCount > 0 ? `Cycle published successfully. ${result.updatedFormsCount} form${result.updatedFormsCount === 1 ? \"\" : \"s\"} updated to Not Started status.` : \"Cycle published successfully.\";\n      messageService.successToast(updateMessage);\n\n      // Close popup and reset state\n      setShowCyclePublishPopup(false);\n      setPendingCyclePublish(null);\n      setUncompletedFormsCount(0);\n      setCyclePublishError(null);\n    } catch (error) {\n      console.error(\"Error publishing cycle:\", error);\n      setCyclePublishError(error.message || \"Error publishing cycle\");\n    } finally {\n      setCyclePublishLoading(false);\n    }\n  };\n\n  // Handler for canceling cycle publish\n  const handleCancelCyclePublish = () => {\n    setShowCyclePublishPopup(false);\n    setPendingCyclePublish(null);\n    setUncompletedFormsCount(0);\n    setCyclePublishLoading(false);\n    setCyclePublishError(null);\n  };\n\n  // Wrapper functions for header buttons that get survey JSON from core component\n  const handleSaveFromHeader = () => {\n    if (coreRef.current && coreRef.current.getSurveyJson) {\n      const surveyJson = coreRef.current.getSurveyJson();\n      handleSave(surveyJson, true);\n    }\n  };\n  const handlePublishFromHeader = () => {\n    if (coreRef.current && coreRef.current.getSurveyJson) {\n      const surveyJson = coreRef.current.getSurveyJson();\n      const isRepublish = (questionnaire === null || questionnaire === void 0 ? void 0 : questionnaire.status) === 1;\n      if (isRepublish) {\n        // Check if there are enabled cycles and get the latest one\n        if (enabledCycles.length > 0) {\n          setShowBatchReopenPopup(true);\n        } else {\n          // No enabled cycles, proceed with direct republish\n          handlePublish(surveyJson);\n        }\n      } else {\n        // For first-time publish, proceed directly\n        handlePublish(surveyJson);\n      }\n    }\n  };\n\n  //\n  // When click \"Apply Changes\" in the BatchReopenPopup model in Republish process.\n  //\n  const handleRepublishConfirmed = async result => {\n    const surveyJson = coreRef.current.getSurveyJson();\n    if (result.success && surveyJson) {\n      // Show success message for year end review decision\n      messageService.successToast(result.message);\n      try {\n        // Now proceed with the actual publish/republish\n        await handlePublish(surveyJson);\n      } catch (error) {\n        console.error(\"Error during republish after year end review:\", error);\n        messageService.errorToast(\"Error occurred during republish process\");\n      }\n    }\n  };\n\n  // Handler for Year End Review popup cancellation\n  const handleBatchReopenCancel = () => {\n    setShowBatchReopenPopup(false);\n  };\n\n  // Handler for close action from header\n  const handleCloseFromHeader = async () => {\n    try {\n      await questionnaireService.closeQuestionnaire(questionnaire.id);\n      messageService.successToast(\"Questionnaire closed successfully\");\n\n      // Update only the questionnaire status to Closed (2)\n      setQuestionnaire(prevQuestionnaire => ({\n        ...prevQuestionnaire,\n        status: 2 // QuestionnaireStatus.Closed\n      }));\n    } catch (error) {\n      console.error(\"Error closing questionnaire:\", error);\n      messageService.errorToast(error.message || \"Error closing questionnaire\");\n    }\n  };\n\n  // Show loading spinner while questionnaire is being loaded\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-content-center align-items-center\",\n      style: {\n        height: \"400px\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"pi pi-spinner pi-spin\",\n        style: {\n          fontSize: \"2rem\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 368,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"ml-2\",\n        children: \"Loading questionnaire...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 369,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 367,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Show loading state if questionnaire data is not yet ready for child components\n  if (!isQuestionnaireLoaded || !questionnaire || !questionnaire.id) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-content-center align-items-center\",\n      style: {\n        height: \"400px\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"pi pi-spinner pi-spin\",\n        style: {\n          fontSize: \"2rem\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 378,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"ml-2\",\n        children: \"Preparing questionnaire designer...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 379,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 377,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Toast, {\n      ref: toast\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 386,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(BatchReopenPopup, {\n      visible: showBatchReopenPopup,\n      onHide: handleBatchReopenCancel,\n      onComplete: handleRepublishConfirmed,\n      questionnaireId: questionnaire === null || questionnaire === void 0 ? void 0 : questionnaire.id,\n      surveyJson: (_coreRef$current = coreRef.current) === null || _coreRef$current === void 0 ? void 0 : _coreRef$current.getSurveyJson(),\n      latestEnabledCycle: enabledCycles.length > 0 ? Math.max(...enabledCycles) : 0,\n      surveyName: surveyName,\n      selectedYear: selectedYear,\n      enableCycles: enabledCycles.join(\",\")\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 388,\n      columnNumber: 7\n    }, this), questionnaire && questionnaire.id && isQuestionnaireLoaded && /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(QuestionnaireDesignerHeader, {\n        surveyName: surveyName,\n        selectedYear: selectedYear,\n        enabledCycles: enabledCycles,\n        isQuestionnaireLoaded: isQuestionnaireLoaded,\n        questionnaire: questionnaire,\n        isReadonly: !isQuestionnaireEditable(questionnaire === null || questionnaire === void 0 ? void 0 : questionnaire.status),\n        onSurveyNameChange: handleSurveyNameChange,\n        onYearChange: handleYearChange,\n        onEnabledCyclesChange: handleEnabledCyclesChange,\n        onPublishCycle: handlePublishCycle,\n        onSave: handleSaveFromHeader,\n        onPublish: handlePublishFromHeader,\n        onClose: handleCloseFromHeader,\n        onBackToList: handleBackToList\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 404,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(QuestionnaireDesignerCore, {\n        ref: coreRef,\n        questionnaire: questionnaire,\n        surveyName: surveyName,\n        selectedYear: selectedYear,\n        isQuestionnaireLoaded: isQuestionnaireLoaded,\n        isReadonly: !isQuestionnaireEditable(questionnaire === null || questionnaire === void 0 ? void 0 : questionnaire.status),\n        onSave: handleSave,\n        onPublish: handlePublish,\n        onBackToList: handleBackToList\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 422,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 402,\n      columnNumber: 9\n    }, this), showCyclePublishPopup && pendingCyclePublish && /*#__PURE__*/_jsxDEV(CyclePublishConfirmationPopup, {\n      visible: showCyclePublishPopup,\n      onHide: handleCancelCyclePublish,\n      onConfirm: handleConfirmCyclePublish,\n      cycleName: pendingCyclePublish.cycleName,\n      cycleValue: pendingCyclePublish.cycleValue,\n      uncompletedFormsCount: uncompletedFormsCount,\n      loading: cyclePublishLoading,\n      error: cyclePublishError\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 438,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 385,\n    columnNumber: 5\n  }, this);\n};\n_s(QuestionnaireDesigner, \"4lCSfmNSGa1C3U+AseY3S4wO69w=\", false, function () {\n  return [useParams, useNavigate];\n});\n_c = QuestionnaireDesigner;\nvar _c;\n$RefreshReg$(_c, \"QuestionnaireDesigner\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useParams", "useNavigate", "Toast", "questionnaireService", "loadingService", "messageService", "QuestionnaireDesignerCore", "QuestionnaireDesignerHeader", "isQuestionnaireEditable", "BatchReopenPopup", "CyclePublishConfirmationPopup", "jsxDEV", "_jsxDEV", "QuestionnaireDesigner", "_s", "_coreRef$current", "id", "navigate", "questionnaire", "setQuestionnaire", "surveyName", "setSurveyName", "selected<PERSON>ear", "setSelectedYear", "Date", "getFullYear", "enabledCycles", "setEnabledCycles", "loading", "setLoading", "isQuestionnaireLoaded", "setIsQuestionnaireLoaded", "showBatchReopenPopup", "setShowBatchReopenPopup", "showCyclePublishPopup", "setShowCyclePublishPopup", "pendingCyclePublish", "setPendingCyclePublish", "uncompletedFormsCount", "setUncompletedFormsCount", "cyclePublishLoading", "setCyclePublishLoading", "cyclePublishError", "setCyclePublishError", "toast", "coreRef", "loadQuestionnaire", "console", "error", "errorToast", "httpRequestSent", "data", "getQuestionnaireById", "isValidQuestionnaire", "name", "year", "enableCycles", "cycles", "split", "map", "Number", "requestAnimationFrame", "setTimeout", "log", "hasDefinition", "draftDefinitionJson", "httpResponseReceived", "handleSave", "surveyJson", "showMessage", "Object", "keys", "length", "definitionJson", "JSON", "stringify", "enableCyclesString", "join", "updatedQuestionnaire", "saveDraft", "successToast", "message", "handlePublish", "isRepublish", "status", "hasActiveInYear", "hasActiveQuestionnaireInYear", "publishedQuestionnaire", "publishWithDefinition", "successMessage", "handleBackToList", "handleSurveyNameChange", "newName", "handleYearChange", "newYear", "handleEnabledCyclesChange", "newEnabledCycles", "handlePublishCycle", "cycleValue", "summary", "getUncompletedFormsCount", "cycleName", "getCycleName", "handleConfirmCyclePublish", "result", "publishCycle", "undefined", "i", "push", "updateMessage", "updatedFormsCount", "handleCancelCyclePublish", "handleSaveFromHeader", "current", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "handlePublishFromHeader", "handleRepublishConfirmed", "success", "handleBatchReopenCancel", "handleCloseFromHeader", "closeQuestionnaire", "prevQuestionnaire", "className", "style", "height", "children", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ref", "visible", "onHide", "onComplete", "questionnaireId", "latestEnabledCycle", "Math", "max", "is<PERSON><PERSON><PERSON>ly", "onSurveyNameChange", "onYearChange", "onEnabledCyclesChange", "onPublishCycle", "onSave", "onPublish", "onClose", "onBackToList", "onConfirm", "_c", "$RefreshReg$"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/pages/admin/questionnaireDesigner.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from \"react\";\r\nimport { useParams, useNavigate } from \"react-router-dom\";\r\nimport { Toast } from \"primereact/toast\";\r\nimport questionnaireService from \"../../services/questionnaireService\";\r\nimport { loadingService } from \"../../core/loading/loadingService\";\r\nimport { messageService } from \"../../core/message/messageService\";\r\nimport { QuestionnaireDesignerCore } from \"../../components/admin/QuestionnaireDesignerCore\";\r\nimport { QuestionnaireDesignerHeader } from \"../../components/admin/QuestionnaireDesignerHeader\";\r\nimport { isQuestionnaireEditable } from \"../../core/enumertions/questionnaireStatus\";\r\n// Add the import for the Year End Review popup\r\nimport BatchReopenPopup from \"../../components/admin/batchReopenPopup\";\r\n// Add the import for the Cycle Publish Confirmation popup\r\nimport CyclePublishConfirmationPopup from \"../../components/admin/CyclePublishConfirmationPopup\";\r\n\r\nexport const QuestionnaireDesigner = () => {\r\n  const { id } = useParams();\r\n  const navigate = useNavigate();\r\n  const [questionnaire, setQuestionnaire] = useState(null);\r\n  const [surveyName, setSurveyName] = useState(\"\");\r\n  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());\r\n  const [enabledCycles, setEnabledCycles] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [isQuestionnaireLoaded, setIsQuestionnaireLoaded] = useState(false);\r\n  // Add state for the Year End Review popup\r\n  const [showBatchReopenPopup, setShowBatchReopenPopup] = useState(false);\r\n  // Add state for the Cycle Publish Confirmation popup\r\n  const [showCyclePublishPopup, setShowCyclePublishPopup] = useState(false);\r\n  const [pendingCyclePublish, setPendingCyclePublish] = useState(null);\r\n  const [uncompletedFormsCount, setUncompletedFormsCount] = useState(0);\r\n  const [cyclePublishLoading, setCyclePublishLoading] = useState(false);\r\n  const [cyclePublishError, setCyclePublishError] = useState(null);\r\n  const toast = useRef(null);\r\n  const coreRef = useRef(null);\r\n\r\n  // Load questionnaire data after component is initialized\r\n  // This ensures we have a valid questionnaire ID before attempting to load\r\n  useEffect(() => {\r\n    if (id && id !== \"undefined\" && id !== \"null\") {\r\n      loadQuestionnaire();\r\n    } else {\r\n      console.error(\"Invalid questionnaire ID provided:\", id);\r\n      messageService.errorToast(\"Invalid questionnaire ID\");\r\n      navigate(\"/admin/questionnaire-management\");\r\n    }\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [id]);\r\n\r\n  const loadQuestionnaire = async () => {\r\n    try {\r\n      setLoading(true);\r\n      setIsQuestionnaireLoaded(false);\r\n      loadingService.httpRequestSent();\r\n\r\n      const data = await questionnaireService.getQuestionnaireById(id);\r\n\r\n      if (data) {\r\n        // Validate that we have all required questionnaire data\r\n        const isValidQuestionnaire = data && data.id && data.name && typeof data.year === \"number\" && data.year > 0;\r\n\r\n        if (!isValidQuestionnaire) {\r\n          console.error(\"Invalid questionnaire data received:\", data);\r\n          messageService.errorToast(\"Invalid questionnaire data received\");\r\n          navigate(\"/admin/questionnaire-management\");\r\n          return;\r\n        }\r\n\r\n        // Set all questionnaire-related state synchronously\r\n        setQuestionnaire(data);\r\n        setSurveyName(data.name);\r\n        setSelectedYear(data.year);\r\n\r\n        // Populate enabled cycles from the questionnaire record\r\n        if (data.enableCycles) {\r\n          const cycles = data.enableCycles.split(\",\").map(Number);\r\n          setEnabledCycles(cycles);\r\n        } else {\r\n          setEnabledCycles([]);\r\n        }\r\n\r\n        // Mark questionnaire as loaded only after all state is set\r\n        // Use requestAnimationFrame to ensure state updates are processed\r\n        requestAnimationFrame(() => {\r\n          // TODO. Add some deplay time to make sure all state is updated before marking questionnaire as loaded\r\n          setTimeout(() => {\r\n            setIsQuestionnaireLoaded(true);\r\n            console.log(\"Questionnaire fully loaded and marked as ready:\", {\r\n              id: data.id,\r\n              name: data.name,\r\n              year: data.year,\r\n              hasDefinition: !!data.draftDefinitionJson,\r\n            });\r\n          }, 1000);\r\n        });\r\n      } else {\r\n        messageService.errorToast(\"Failed to load questionnaire\");\r\n        navigate(\"/admin/questionnaire-management\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error loading questionnaire:\", error);\r\n      messageService.errorToast(\"Error loading questionnaire\");\r\n      navigate(\"/admin/questionnaire-management\");\r\n    } finally {\r\n      setLoading(false);\r\n      loadingService.httpResponseReceived();\r\n    }\r\n  };\r\n\r\n  // Handler for save action from shared component\r\n  const handleSave = async (surveyJson, showMessage = true) => {\r\n    console.log(\"Save attempt - Survey name:\", surveyName, \"Year:\", selectedYear);\r\n\r\n    // Check if survey name and year are available\r\n    if (!surveyName || !selectedYear) {\r\n      console.error(\"Survey name or year not available - cannot save survey\", {\r\n        surveyName,\r\n        selectedYear,\r\n      });\r\n      if (showMessage) {\r\n        messageService.errorToast(\"Survey name or year not available. Please check the form fields.\");\r\n      }\r\n      return;\r\n    }\r\n\r\n    // Ensure we have valid survey data\r\n    if (!surveyJson || Object.keys(surveyJson).length === 0) {\r\n      console.error(\"Survey JSON is empty or invalid\");\r\n      if (showMessage) {\r\n        messageService.errorToast(\"No form data to save. Please add some questions first.\");\r\n      }\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const definitionJson = JSON.stringify(surveyJson);\r\n\r\n      // Convert enabled cycles array to comma-separated string for storage\r\n      const enableCyclesString = enabledCycles.join(\",\");\r\n\r\n      const updatedQuestionnaire = await questionnaireService.saveDraft(questionnaire, surveyName, selectedYear, definitionJson, enableCyclesString);\r\n\r\n      if (showMessage) {\r\n        messageService.successToast(\"Questionnaire saved successfully\");\r\n      }\r\n      setQuestionnaire(updatedQuestionnaire);\r\n    } catch (error) {\r\n      console.error(\"Error saving questionnaire:\", error);\r\n      if (showMessage) {\r\n        messageService.errorToast(error.message || \"Failed to save questionnaire\");\r\n      }\r\n    }\r\n  };\r\n\r\n  // Handler for publish action from shared component\r\n  const handlePublish = async (surveyJson) => {\r\n    try {\r\n      const isRepublish = questionnaire?.status === 1;\r\n\r\n      // Check if there's already another active questionnaire in the same year\r\n      // This applies to both first-time publishing and republishing\r\n      const hasActiveInYear = await questionnaireService.hasActiveQuestionnaireInYear(selectedYear, questionnaire?.id);\r\n\r\n      if (hasActiveInYear) {\r\n        messageService.errorToast(`${selectedYear} year already has an active questionnaire. Only one active questionnaire is allowed per year.`);\r\n        return;\r\n      }\r\n\r\n      const definitionJson = JSON.stringify(surveyJson);\r\n\r\n      // Convert enabled cycles array to comma-separated string for storage\r\n      const enableCyclesString = enabledCycles.join(\",\");\r\n\r\n      const publishedQuestionnaire = await questionnaireService.publishWithDefinition(\r\n        questionnaire,\r\n        surveyName,\r\n        selectedYear,\r\n        definitionJson,\r\n        enableCyclesString\r\n      );\r\n\r\n      const successMessage = isRepublish ? \"Questionnaire republished successfully\" : \"Questionnaire published successfully\";\r\n\r\n      messageService.successToast(successMessage);\r\n      setQuestionnaire(publishedQuestionnaire);\r\n    } catch (error) {\r\n      console.error(\"Error publishing questionnaire:\", error);\r\n      messageService.errorToast(error.message || \"Failed to publish questionnaire\");\r\n    }\r\n  };\r\n\r\n  // Handler for back to list action\r\n  const handleBackToList = () => {\r\n    navigate(\"/admin/questionnaire-management\");\r\n  };\r\n\r\n  // Handler for survey name changes\r\n  const handleSurveyNameChange = (newName) => {\r\n    setSurveyName(newName);\r\n  };\r\n\r\n  // Handler for year changes\r\n  const handleYearChange = (newYear) => {\r\n    setSelectedYear(newYear);\r\n  };\r\n\r\n  // Handler for enabled cycles changes\r\n  const handleEnabledCyclesChange = (newEnabledCycles) => {\r\n    setEnabledCycles(newEnabledCycles);\r\n  };\r\n\r\n  // Handler for publishing individual cycles\r\n  const handlePublishCycle = async (cycleValue) => {\r\n    try {\r\n      // First, get the summary of uncompleted forms\r\n      const summary = await questionnaireService.getUncompletedFormsCount(questionnaire.id, cycleValue);\r\n\r\n      // Store the cycle info and show the confirmation popup\r\n      const cycleName = getCycleName(cycleValue);\r\n      setPendingCyclePublish({ cycleValue, cycleName });\r\n      setUncompletedFormsCount(summary);\r\n      setShowCyclePublishPopup(true);\r\n    } catch (error) {\r\n      console.error(\"Error getting uncompleted forms count:\", error);\r\n      messageService.errorToast(error.message || \"Error getting uncompleted forms count\");\r\n    }\r\n  };\r\n\r\n  // Helper function to get cycle name\r\n  const getCycleName = (cycleValue) => {\r\n    switch (cycleValue) {\r\n      case 0:\r\n        return \"Planning\";\r\n      case 1:\r\n        return \"Mid Year Review\";\r\n      case 2:\r\n        return \"Year End Review\";\r\n      default:\r\n        return \"Unknown\";\r\n    }\r\n  };\r\n\r\n  // Handler for confirming cycle publish from popup\r\n  const handleConfirmCyclePublish = async () => {\r\n    if (!pendingCyclePublish) return;\r\n\r\n    try {\r\n      setCyclePublishLoading(true);\r\n      setCyclePublishError(null);\r\n\r\n      const result = await questionnaireService.publishCycle(questionnaire.id, pendingCyclePublish.cycleValue);\r\n\r\n      // Update the enabled cycles from the returned result\r\n      if (result && result.enabledCycles !== undefined) {\r\n        // Convert byte back to array of cycle numbers\r\n        const cycles = [];\r\n        for (let i = 0; i < 3; i++) {\r\n          if (result.enabledCycles & (1 << i)) {\r\n            cycles.push(i);\r\n          }\r\n        }\r\n        setEnabledCycles(cycles);\r\n      }\r\n\r\n      // Show success message with update count\r\n      const updateMessage =\r\n        result.updatedFormsCount > 0\r\n          ? `Cycle published successfully. ${result.updatedFormsCount} form${\r\n              result.updatedFormsCount === 1 ? \"\" : \"s\"\r\n            } updated to Not Started status.`\r\n          : \"Cycle published successfully.\";\r\n\r\n      messageService.successToast(updateMessage);\r\n\r\n      // Close popup and reset state\r\n      setShowCyclePublishPopup(false);\r\n      setPendingCyclePublish(null);\r\n      setUncompletedFormsCount(0);\r\n      setCyclePublishError(null);\r\n    } catch (error) {\r\n      console.error(\"Error publishing cycle:\", error);\r\n      setCyclePublishError(error.message || \"Error publishing cycle\");\r\n    } finally {\r\n      setCyclePublishLoading(false);\r\n    }\r\n  };\r\n\r\n  // Handler for canceling cycle publish\r\n  const handleCancelCyclePublish = () => {\r\n    setShowCyclePublishPopup(false);\r\n    setPendingCyclePublish(null);\r\n    setUncompletedFormsCount(0);\r\n    setCyclePublishLoading(false);\r\n    setCyclePublishError(null);\r\n  };\r\n\r\n  // Wrapper functions for header buttons that get survey JSON from core component\r\n  const handleSaveFromHeader = () => {\r\n    if (coreRef.current && coreRef.current.getSurveyJson) {\r\n      const surveyJson = coreRef.current.getSurveyJson();\r\n      handleSave(surveyJson, true);\r\n    }\r\n  };\r\n\r\n  const handlePublishFromHeader = () => {\r\n    if (coreRef.current && coreRef.current.getSurveyJson) {\r\n      const surveyJson = coreRef.current.getSurveyJson();\r\n      const isRepublish = questionnaire?.status === 1;\r\n\r\n      if (isRepublish) {\r\n        // Check if there are enabled cycles and get the latest one\r\n        if (enabledCycles.length > 0) {\r\n          setShowBatchReopenPopup(true);\r\n        } else {\r\n          // No enabled cycles, proceed with direct republish\r\n          handlePublish(surveyJson);\r\n        }\r\n      } else {\r\n        // For first-time publish, proceed directly\r\n        handlePublish(surveyJson);\r\n      }\r\n    }\r\n  };\r\n\r\n  //\r\n  // When click \"Apply Changes\" in the BatchReopenPopup model in Republish process.\r\n  //\r\n  const handleRepublishConfirmed = async (result) => {\r\n    const surveyJson = coreRef.current.getSurveyJson();\r\n\r\n    if (result.success && surveyJson) {\r\n      // Show success message for year end review decision\r\n      messageService.successToast(result.message);\r\n      try {\r\n        // Now proceed with the actual publish/republish\r\n        await handlePublish(surveyJson);\r\n      } catch (error) {\r\n        console.error(\"Error during republish after year end review:\", error);\r\n        messageService.errorToast(\"Error occurred during republish process\");\r\n      }\r\n    }\r\n  };\r\n\r\n  // Handler for Year End Review popup cancellation\r\n  const handleBatchReopenCancel = () => {\r\n    setShowBatchReopenPopup(false);\r\n  };\r\n\r\n  // Handler for close action from header\r\n  const handleCloseFromHeader = async () => {\r\n    try {\r\n      await questionnaireService.closeQuestionnaire(questionnaire.id);\r\n      messageService.successToast(\"Questionnaire closed successfully\");\r\n\r\n      // Update only the questionnaire status to Closed (2)\r\n      setQuestionnaire((prevQuestionnaire) => ({\r\n        ...prevQuestionnaire,\r\n        status: 2, // QuestionnaireStatus.Closed\r\n      }));\r\n    } catch (error) {\r\n      console.error(\"Error closing questionnaire:\", error);\r\n      messageService.errorToast(error.message || \"Error closing questionnaire\");\r\n    }\r\n  };\r\n\r\n  // Show loading spinner while questionnaire is being loaded\r\n  if (loading) {\r\n    return (\r\n      <div className=\"flex justify-content-center align-items-center\" style={{ height: \"400px\" }}>\r\n        <i className=\"pi pi-spinner pi-spin\" style={{ fontSize: \"2rem\" }}></i>\r\n        <span className=\"ml-2\">Loading questionnaire...</span>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Show loading state if questionnaire data is not yet ready for child components\r\n  if (!isQuestionnaireLoaded || !questionnaire || !questionnaire.id) {\r\n    return (\r\n      <div className=\"flex justify-content-center align-items-center\" style={{ height: \"400px\" }}>\r\n        <i className=\"pi pi-spinner pi-spin\" style={{ fontSize: \"2rem\" }}></i>\r\n        <span className=\"ml-2\">Preparing questionnaire designer...</span>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div>\r\n      <Toast ref={toast} />\r\n      {/* Batch Reopen Popup before republish */}\r\n      <BatchReopenPopup\r\n        visible={showBatchReopenPopup}\r\n        onHide={handleBatchReopenCancel}\r\n        onComplete={handleRepublishConfirmed}\r\n        questionnaireId={questionnaire?.id}\r\n        surveyJson={coreRef.current?.getSurveyJson()}\r\n        latestEnabledCycle={enabledCycles.length > 0 ? Math.max(...enabledCycles) : 0}\r\n        surveyName={surveyName}\r\n        selectedYear={selectedYear}\r\n        enableCycles={enabledCycles.join(\",\")}\r\n      />\r\n\r\n      {/* Make sure we have questionnaire data before rendering child components */}\r\n      {questionnaire && questionnaire.id && isQuestionnaireLoaded && (\r\n        <div>\r\n          {/* Header with questionnaire details - only render when questionnaire is fully loaded */}\r\n          <QuestionnaireDesignerHeader\r\n            surveyName={surveyName}\r\n            selectedYear={selectedYear}\r\n            enabledCycles={enabledCycles}\r\n            isQuestionnaireLoaded={isQuestionnaireLoaded}\r\n            questionnaire={questionnaire}\r\n            isReadonly={!isQuestionnaireEditable(questionnaire?.status)}\r\n            onSurveyNameChange={handleSurveyNameChange}\r\n            onYearChange={handleYearChange}\r\n            onEnabledCyclesChange={handleEnabledCyclesChange}\r\n            onPublishCycle={handlePublishCycle}\r\n            onSave={handleSaveFromHeader}\r\n            onPublish={handlePublishFromHeader}\r\n            onClose={handleCloseFromHeader}\r\n            onBackToList={handleBackToList}\r\n          />\r\n\r\n          {/* Survey Creator Core - only render when questionnaire is fully loaded */}\r\n          <QuestionnaireDesignerCore\r\n            ref={coreRef}\r\n            questionnaire={questionnaire}\r\n            surveyName={surveyName}\r\n            selectedYear={selectedYear}\r\n            isQuestionnaireLoaded={isQuestionnaireLoaded}\r\n            isReadonly={!isQuestionnaireEditable(questionnaire?.status)}\r\n            onSave={handleSave}\r\n            onPublish={handlePublish}\r\n            onBackToList={handleBackToList}\r\n          />\r\n        </div>\r\n      )}\r\n\r\n      {/* Cycle Publish Confirmation Popup */}\r\n      {showCyclePublishPopup && pendingCyclePublish && (\r\n        <CyclePublishConfirmationPopup\r\n          visible={showCyclePublishPopup}\r\n          onHide={handleCancelCyclePublish}\r\n          onConfirm={handleConfirmCyclePublish}\r\n          cycleName={pendingCyclePublish.cycleName}\r\n          cycleValue={pendingCyclePublish.cycleValue}\r\n          uncompletedFormsCount={uncompletedFormsCount}\r\n          loading={cyclePublishLoading}\r\n          error={cyclePublishError}\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,KAAK,QAAQ,kBAAkB;AACxC,OAAOC,oBAAoB,MAAM,qCAAqC;AACtE,SAASC,cAAc,QAAQ,mCAAmC;AAClE,SAASC,cAAc,QAAQ,mCAAmC;AAClE,SAASC,yBAAyB,QAAQ,kDAAkD;AAC5F,SAASC,2BAA2B,QAAQ,oDAAoD;AAChG,SAASC,uBAAuB,QAAQ,4CAA4C;AACpF;AACA,OAAOC,gBAAgB,MAAM,yCAAyC;AACtE;AACA,OAAOC,6BAA6B,MAAM,sDAAsD;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjG,OAAO,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,gBAAA;EACzC,MAAM;IAAEC;EAAG,CAAC,GAAGhB,SAAS,CAAC,CAAC;EAC1B,MAAMiB,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACiB,aAAa,EAAEC,gBAAgB,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACuB,UAAU,EAAEC,aAAa,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACyB,YAAY,EAAEC,eAAe,CAAC,GAAG1B,QAAQ,CAAC,IAAI2B,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;EAC1E,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC+B,OAAO,EAAEC,UAAU,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACiC,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EACzE;EACA,MAAM,CAACmC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EACvE;EACA,MAAM,CAACqC,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAACuC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;EACpE,MAAM,CAACyC,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG1C,QAAQ,CAAC,CAAC,CAAC;EACrE,MAAM,CAAC2C,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAAC6C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9C,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM+C,KAAK,GAAG7C,MAAM,CAAC,IAAI,CAAC;EAC1B,MAAM8C,OAAO,GAAG9C,MAAM,CAAC,IAAI,CAAC;;EAE5B;EACA;EACAD,SAAS,CAAC,MAAM;IACd,IAAIkB,EAAE,IAAIA,EAAE,KAAK,WAAW,IAAIA,EAAE,KAAK,MAAM,EAAE;MAC7C8B,iBAAiB,CAAC,CAAC;IACrB,CAAC,MAAM;MACLC,OAAO,CAACC,KAAK,CAAC,oCAAoC,EAAEhC,EAAE,CAAC;MACvDX,cAAc,CAAC4C,UAAU,CAAC,0BAA0B,CAAC;MACrDhC,QAAQ,CAAC,iCAAiC,CAAC;IAC7C;IACA;EACF,CAAC,EAAE,CAACD,EAAE,CAAC,CAAC;EAER,MAAM8B,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACFjB,UAAU,CAAC,IAAI,CAAC;MAChBE,wBAAwB,CAAC,KAAK,CAAC;MAC/B3B,cAAc,CAAC8C,eAAe,CAAC,CAAC;MAEhC,MAAMC,IAAI,GAAG,MAAMhD,oBAAoB,CAACiD,oBAAoB,CAACpC,EAAE,CAAC;MAEhE,IAAImC,IAAI,EAAE;QACR;QACA,MAAME,oBAAoB,GAAGF,IAAI,IAAIA,IAAI,CAACnC,EAAE,IAAImC,IAAI,CAACG,IAAI,IAAI,OAAOH,IAAI,CAACI,IAAI,KAAK,QAAQ,IAAIJ,IAAI,CAACI,IAAI,GAAG,CAAC;QAE3G,IAAI,CAACF,oBAAoB,EAAE;UACzBN,OAAO,CAACC,KAAK,CAAC,sCAAsC,EAAEG,IAAI,CAAC;UAC3D9C,cAAc,CAAC4C,UAAU,CAAC,qCAAqC,CAAC;UAChEhC,QAAQ,CAAC,iCAAiC,CAAC;UAC3C;QACF;;QAEA;QACAE,gBAAgB,CAACgC,IAAI,CAAC;QACtB9B,aAAa,CAAC8B,IAAI,CAACG,IAAI,CAAC;QACxB/B,eAAe,CAAC4B,IAAI,CAACI,IAAI,CAAC;;QAE1B;QACA,IAAIJ,IAAI,CAACK,YAAY,EAAE;UACrB,MAAMC,MAAM,GAAGN,IAAI,CAACK,YAAY,CAACE,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,MAAM,CAAC;UACvDjC,gBAAgB,CAAC8B,MAAM,CAAC;QAC1B,CAAC,MAAM;UACL9B,gBAAgB,CAAC,EAAE,CAAC;QACtB;;QAEA;QACA;QACAkC,qBAAqB,CAAC,MAAM;UAC1B;UACAC,UAAU,CAAC,MAAM;YACf/B,wBAAwB,CAAC,IAAI,CAAC;YAC9BgB,OAAO,CAACgB,GAAG,CAAC,iDAAiD,EAAE;cAC7D/C,EAAE,EAAEmC,IAAI,CAACnC,EAAE;cACXsC,IAAI,EAAEH,IAAI,CAACG,IAAI;cACfC,IAAI,EAAEJ,IAAI,CAACI,IAAI;cACfS,aAAa,EAAE,CAAC,CAACb,IAAI,CAACc;YACxB,CAAC,CAAC;UACJ,CAAC,EAAE,IAAI,CAAC;QACV,CAAC,CAAC;MACJ,CAAC,MAAM;QACL5D,cAAc,CAAC4C,UAAU,CAAC,8BAA8B,CAAC;QACzDhC,QAAQ,CAAC,iCAAiC,CAAC;MAC7C;IACF,CAAC,CAAC,OAAO+B,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD3C,cAAc,CAAC4C,UAAU,CAAC,6BAA6B,CAAC;MACxDhC,QAAQ,CAAC,iCAAiC,CAAC;IAC7C,CAAC,SAAS;MACRY,UAAU,CAAC,KAAK,CAAC;MACjBzB,cAAc,CAAC8D,oBAAoB,CAAC,CAAC;IACvC;EACF,CAAC;;EAED;EACA,MAAMC,UAAU,GAAG,MAAAA,CAAOC,UAAU,EAAEC,WAAW,GAAG,IAAI,KAAK;IAC3DtB,OAAO,CAACgB,GAAG,CAAC,6BAA6B,EAAE3C,UAAU,EAAE,OAAO,EAAEE,YAAY,CAAC;;IAE7E;IACA,IAAI,CAACF,UAAU,IAAI,CAACE,YAAY,EAAE;MAChCyB,OAAO,CAACC,KAAK,CAAC,wDAAwD,EAAE;QACtE5B,UAAU;QACVE;MACF,CAAC,CAAC;MACF,IAAI+C,WAAW,EAAE;QACfhE,cAAc,CAAC4C,UAAU,CAAC,kEAAkE,CAAC;MAC/F;MACA;IACF;;IAEA;IACA,IAAI,CAACmB,UAAU,IAAIE,MAAM,CAACC,IAAI,CAACH,UAAU,CAAC,CAACI,MAAM,KAAK,CAAC,EAAE;MACvDzB,OAAO,CAACC,KAAK,CAAC,iCAAiC,CAAC;MAChD,IAAIqB,WAAW,EAAE;QACfhE,cAAc,CAAC4C,UAAU,CAAC,wDAAwD,CAAC;MACrF;MACA;IACF;IAEA,IAAI;MACF,MAAMwB,cAAc,GAAGC,IAAI,CAACC,SAAS,CAACP,UAAU,CAAC;;MAEjD;MACA,MAAMQ,kBAAkB,GAAGlD,aAAa,CAACmD,IAAI,CAAC,GAAG,CAAC;MAElD,MAAMC,oBAAoB,GAAG,MAAM3E,oBAAoB,CAAC4E,SAAS,CAAC7D,aAAa,EAAEE,UAAU,EAAEE,YAAY,EAAEmD,cAAc,EAAEG,kBAAkB,CAAC;MAE9I,IAAIP,WAAW,EAAE;QACfhE,cAAc,CAAC2E,YAAY,CAAC,kCAAkC,CAAC;MACjE;MACA7D,gBAAgB,CAAC2D,oBAAoB,CAAC;IACxC,CAAC,CAAC,OAAO9B,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,IAAIqB,WAAW,EAAE;QACfhE,cAAc,CAAC4C,UAAU,CAACD,KAAK,CAACiC,OAAO,IAAI,8BAA8B,CAAC;MAC5E;IACF;EACF,CAAC;;EAED;EACA,MAAMC,aAAa,GAAG,MAAOd,UAAU,IAAK;IAC1C,IAAI;MACF,MAAMe,WAAW,GAAG,CAAAjE,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEkE,MAAM,MAAK,CAAC;;MAE/C;MACA;MACA,MAAMC,eAAe,GAAG,MAAMlF,oBAAoB,CAACmF,4BAA4B,CAAChE,YAAY,EAAEJ,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEF,EAAE,CAAC;MAEhH,IAAIqE,eAAe,EAAE;QACnBhF,cAAc,CAAC4C,UAAU,CAAC,GAAG3B,YAAY,+FAA+F,CAAC;QACzI;MACF;MAEA,MAAMmD,cAAc,GAAGC,IAAI,CAACC,SAAS,CAACP,UAAU,CAAC;;MAEjD;MACA,MAAMQ,kBAAkB,GAAGlD,aAAa,CAACmD,IAAI,CAAC,GAAG,CAAC;MAElD,MAAMU,sBAAsB,GAAG,MAAMpF,oBAAoB,CAACqF,qBAAqB,CAC7EtE,aAAa,EACbE,UAAU,EACVE,YAAY,EACZmD,cAAc,EACdG,kBACF,CAAC;MAED,MAAMa,cAAc,GAAGN,WAAW,GAAG,wCAAwC,GAAG,sCAAsC;MAEtH9E,cAAc,CAAC2E,YAAY,CAACS,cAAc,CAAC;MAC3CtE,gBAAgB,CAACoE,sBAAsB,CAAC;IAC1C,CAAC,CAAC,OAAOvC,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD3C,cAAc,CAAC4C,UAAU,CAACD,KAAK,CAACiC,OAAO,IAAI,iCAAiC,CAAC;IAC/E;EACF,CAAC;;EAED;EACA,MAAMS,gBAAgB,GAAGA,CAAA,KAAM;IAC7BzE,QAAQ,CAAC,iCAAiC,CAAC;EAC7C,CAAC;;EAED;EACA,MAAM0E,sBAAsB,GAAIC,OAAO,IAAK;IAC1CvE,aAAa,CAACuE,OAAO,CAAC;EACxB,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAIC,OAAO,IAAK;IACpCvE,eAAe,CAACuE,OAAO,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMC,yBAAyB,GAAIC,gBAAgB,IAAK;IACtDrE,gBAAgB,CAACqE,gBAAgB,CAAC;EACpC,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAG,MAAOC,UAAU,IAAK;IAC/C,IAAI;MACF;MACA,MAAMC,OAAO,GAAG,MAAMhG,oBAAoB,CAACiG,wBAAwB,CAAClF,aAAa,CAACF,EAAE,EAAEkF,UAAU,CAAC;;MAEjG;MACA,MAAMG,SAAS,GAAGC,YAAY,CAACJ,UAAU,CAAC;MAC1C7D,sBAAsB,CAAC;QAAE6D,UAAU;QAAEG;MAAU,CAAC,CAAC;MACjD9D,wBAAwB,CAAC4D,OAAO,CAAC;MACjChE,wBAAwB,CAAC,IAAI,CAAC;IAChC,CAAC,CAAC,OAAOa,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAC9D3C,cAAc,CAAC4C,UAAU,CAACD,KAAK,CAACiC,OAAO,IAAI,uCAAuC,CAAC;IACrF;EACF,CAAC;;EAED;EACA,MAAMqB,YAAY,GAAIJ,UAAU,IAAK;IACnC,QAAQA,UAAU;MAChB,KAAK,CAAC;QACJ,OAAO,UAAU;MACnB,KAAK,CAAC;QACJ,OAAO,iBAAiB;MAC1B,KAAK,CAAC;QACJ,OAAO,iBAAiB;MAC1B;QACE,OAAO,SAAS;IACpB;EACF,CAAC;;EAED;EACA,MAAMK,yBAAyB,GAAG,MAAAA,CAAA,KAAY;IAC5C,IAAI,CAACnE,mBAAmB,EAAE;IAE1B,IAAI;MACFK,sBAAsB,CAAC,IAAI,CAAC;MAC5BE,oBAAoB,CAAC,IAAI,CAAC;MAE1B,MAAM6D,MAAM,GAAG,MAAMrG,oBAAoB,CAACsG,YAAY,CAACvF,aAAa,CAACF,EAAE,EAAEoB,mBAAmB,CAAC8D,UAAU,CAAC;;MAExG;MACA,IAAIM,MAAM,IAAIA,MAAM,CAAC9E,aAAa,KAAKgF,SAAS,EAAE;QAChD;QACA,MAAMjD,MAAM,GAAG,EAAE;QACjB,KAAK,IAAIkD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;UAC1B,IAAIH,MAAM,CAAC9E,aAAa,GAAI,CAAC,IAAIiF,CAAE,EAAE;YACnClD,MAAM,CAACmD,IAAI,CAACD,CAAC,CAAC;UAChB;QACF;QACAhF,gBAAgB,CAAC8B,MAAM,CAAC;MAC1B;;MAEA;MACA,MAAMoD,aAAa,GACjBL,MAAM,CAACM,iBAAiB,GAAG,CAAC,GACxB,iCAAiCN,MAAM,CAACM,iBAAiB,QACvDN,MAAM,CAACM,iBAAiB,KAAK,CAAC,GAAG,EAAE,GAAG,GAAG,iCACV,GACjC,+BAA+B;MAErCzG,cAAc,CAAC2E,YAAY,CAAC6B,aAAa,CAAC;;MAE1C;MACA1E,wBAAwB,CAAC,KAAK,CAAC;MAC/BE,sBAAsB,CAAC,IAAI,CAAC;MAC5BE,wBAAwB,CAAC,CAAC,CAAC;MAC3BI,oBAAoB,CAAC,IAAI,CAAC;IAC5B,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CL,oBAAoB,CAACK,KAAK,CAACiC,OAAO,IAAI,wBAAwB,CAAC;IACjE,CAAC,SAAS;MACRxC,sBAAsB,CAAC,KAAK,CAAC;IAC/B;EACF,CAAC;;EAED;EACA,MAAMsE,wBAAwB,GAAGA,CAAA,KAAM;IACrC5E,wBAAwB,CAAC,KAAK,CAAC;IAC/BE,sBAAsB,CAAC,IAAI,CAAC;IAC5BE,wBAAwB,CAAC,CAAC,CAAC;IAC3BE,sBAAsB,CAAC,KAAK,CAAC;IAC7BE,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMqE,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAInE,OAAO,CAACoE,OAAO,IAAIpE,OAAO,CAACoE,OAAO,CAACC,aAAa,EAAE;MACpD,MAAM9C,UAAU,GAAGvB,OAAO,CAACoE,OAAO,CAACC,aAAa,CAAC,CAAC;MAClD/C,UAAU,CAACC,UAAU,EAAE,IAAI,CAAC;IAC9B;EACF,CAAC;EAED,MAAM+C,uBAAuB,GAAGA,CAAA,KAAM;IACpC,IAAItE,OAAO,CAACoE,OAAO,IAAIpE,OAAO,CAACoE,OAAO,CAACC,aAAa,EAAE;MACpD,MAAM9C,UAAU,GAAGvB,OAAO,CAACoE,OAAO,CAACC,aAAa,CAAC,CAAC;MAClD,MAAM/B,WAAW,GAAG,CAAAjE,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEkE,MAAM,MAAK,CAAC;MAE/C,IAAID,WAAW,EAAE;QACf;QACA,IAAIzD,aAAa,CAAC8C,MAAM,GAAG,CAAC,EAAE;UAC5BvC,uBAAuB,CAAC,IAAI,CAAC;QAC/B,CAAC,MAAM;UACL;UACAiD,aAAa,CAACd,UAAU,CAAC;QAC3B;MACF,CAAC,MAAM;QACL;QACAc,aAAa,CAACd,UAAU,CAAC;MAC3B;IACF;EACF,CAAC;;EAED;EACA;EACA;EACA,MAAMgD,wBAAwB,GAAG,MAAOZ,MAAM,IAAK;IACjD,MAAMpC,UAAU,GAAGvB,OAAO,CAACoE,OAAO,CAACC,aAAa,CAAC,CAAC;IAElD,IAAIV,MAAM,CAACa,OAAO,IAAIjD,UAAU,EAAE;MAChC;MACA/D,cAAc,CAAC2E,YAAY,CAACwB,MAAM,CAACvB,OAAO,CAAC;MAC3C,IAAI;QACF;QACA,MAAMC,aAAa,CAACd,UAAU,CAAC;MACjC,CAAC,CAAC,OAAOpB,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;QACrE3C,cAAc,CAAC4C,UAAU,CAAC,yCAAyC,CAAC;MACtE;IACF;EACF,CAAC;;EAED;EACA,MAAMqE,uBAAuB,GAAGA,CAAA,KAAM;IACpCrF,uBAAuB,CAAC,KAAK,CAAC;EAChC,CAAC;;EAED;EACA,MAAMsF,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI;MACF,MAAMpH,oBAAoB,CAACqH,kBAAkB,CAACtG,aAAa,CAACF,EAAE,CAAC;MAC/DX,cAAc,CAAC2E,YAAY,CAAC,mCAAmC,CAAC;;MAEhE;MACA7D,gBAAgB,CAAEsG,iBAAiB,KAAM;QACvC,GAAGA,iBAAiB;QACpBrC,MAAM,EAAE,CAAC,CAAE;MACb,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,OAAOpC,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD3C,cAAc,CAAC4C,UAAU,CAACD,KAAK,CAACiC,OAAO,IAAI,6BAA6B,CAAC;IAC3E;EACF,CAAC;;EAED;EACA,IAAIrD,OAAO,EAAE;IACX,oBACEhB,OAAA;MAAK8G,SAAS,EAAC,gDAAgD;MAACC,KAAK,EAAE;QAAEC,MAAM,EAAE;MAAQ,CAAE;MAAAC,QAAA,gBACzFjH,OAAA;QAAG8G,SAAS,EAAC,uBAAuB;QAACC,KAAK,EAAE;UAAEG,QAAQ,EAAE;QAAO;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtEtH,OAAA;QAAM8G,SAAS,EAAC,MAAM;QAAAG,QAAA,EAAC;MAAwB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnD,CAAC;EAEV;;EAEA;EACA,IAAI,CAACpG,qBAAqB,IAAI,CAACZ,aAAa,IAAI,CAACA,aAAa,CAACF,EAAE,EAAE;IACjE,oBACEJ,OAAA;MAAK8G,SAAS,EAAC,gDAAgD;MAACC,KAAK,EAAE;QAAEC,MAAM,EAAE;MAAQ,CAAE;MAAAC,QAAA,gBACzFjH,OAAA;QAAG8G,SAAS,EAAC,uBAAuB;QAACC,KAAK,EAAE;UAAEG,QAAQ,EAAE;QAAO;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtEtH,OAAA;QAAM8G,SAAS,EAAC,MAAM;QAAAG,QAAA,EAAC;MAAmC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9D,CAAC;EAEV;EAEA,oBACEtH,OAAA;IAAAiH,QAAA,gBACEjH,OAAA,CAACV,KAAK;MAACiI,GAAG,EAAEvF;IAAM;MAAAmF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAErBtH,OAAA,CAACH,gBAAgB;MACf2H,OAAO,EAAEpG,oBAAqB;MAC9BqG,MAAM,EAAEf,uBAAwB;MAChCgB,UAAU,EAAElB,wBAAyB;MACrCmB,eAAe,EAAErH,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEF,EAAG;MACnCoD,UAAU,GAAArD,gBAAA,GAAE8B,OAAO,CAACoE,OAAO,cAAAlG,gBAAA,uBAAfA,gBAAA,CAAiBmG,aAAa,CAAC,CAAE;MAC7CsB,kBAAkB,EAAE9G,aAAa,CAAC8C,MAAM,GAAG,CAAC,GAAGiE,IAAI,CAACC,GAAG,CAAC,GAAGhH,aAAa,CAAC,GAAG,CAAE;MAC9EN,UAAU,EAAEA,UAAW;MACvBE,YAAY,EAAEA,YAAa;MAC3BkC,YAAY,EAAE9B,aAAa,CAACmD,IAAI,CAAC,GAAG;IAAE;MAAAkD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvC,CAAC,EAGDhH,aAAa,IAAIA,aAAa,CAACF,EAAE,IAAIc,qBAAqB,iBACzDlB,OAAA;MAAAiH,QAAA,gBAEEjH,OAAA,CAACL,2BAA2B;QAC1Ba,UAAU,EAAEA,UAAW;QACvBE,YAAY,EAAEA,YAAa;QAC3BI,aAAa,EAAEA,aAAc;QAC7BI,qBAAqB,EAAEA,qBAAsB;QAC7CZ,aAAa,EAAEA,aAAc;QAC7ByH,UAAU,EAAE,CAACnI,uBAAuB,CAACU,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEkE,MAAM,CAAE;QAC5DwD,kBAAkB,EAAEjD,sBAAuB;QAC3CkD,YAAY,EAAEhD,gBAAiB;QAC/BiD,qBAAqB,EAAE/C,yBAA0B;QACjDgD,cAAc,EAAE9C,kBAAmB;QACnC+C,MAAM,EAAEhC,oBAAqB;QAC7BiC,SAAS,EAAE9B,uBAAwB;QACnC+B,OAAO,EAAE3B,qBAAsB;QAC/B4B,YAAY,EAAEzD;MAAiB;QAAAqC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CAAC,eAGFtH,OAAA,CAACN,yBAAyB;QACxB6H,GAAG,EAAEtF,OAAQ;QACb3B,aAAa,EAAEA,aAAc;QAC7BE,UAAU,EAAEA,UAAW;QACvBE,YAAY,EAAEA,YAAa;QAC3BQ,qBAAqB,EAAEA,qBAAsB;QAC7C6G,UAAU,EAAE,CAACnI,uBAAuB,CAACU,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEkE,MAAM,CAAE;QAC5D4D,MAAM,EAAE7E,UAAW;QACnB8E,SAAS,EAAE/D,aAAc;QACzBiE,YAAY,EAAEzD;MAAiB;QAAAqC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN,EAGAhG,qBAAqB,IAAIE,mBAAmB,iBAC3CxB,OAAA,CAACF,6BAA6B;MAC5B0H,OAAO,EAAElG,qBAAsB;MAC/BmG,MAAM,EAAEtB,wBAAyB;MACjCqC,SAAS,EAAE7C,yBAA0B;MACrCF,SAAS,EAAEjE,mBAAmB,CAACiE,SAAU;MACzCH,UAAU,EAAE9D,mBAAmB,CAAC8D,UAAW;MAC3C5D,qBAAqB,EAAEA,qBAAsB;MAC7CV,OAAO,EAAEY,mBAAoB;MAC7BQ,KAAK,EAAEN;IAAkB;MAAAqF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACpH,EAAA,CApbWD,qBAAqB;EAAA,QACjBb,SAAS,EACPC,WAAW;AAAA;AAAAoJ,EAAA,GAFjBxI,qBAAqB;AAAA,IAAAwI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}