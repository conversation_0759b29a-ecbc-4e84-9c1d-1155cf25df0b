{"ast": null, "code": "/** Corporate with server side project, Enumerations.PartnerReviewerUploadStatus definitions. */\nexport const PartnerReviewerUploadStatus = {\n  /** Upload is in progress. 0 */\n  Uploading: 0,\n  /** File has been uploaded successfully. 1 */\n  Uploaded: 1,\n  /** Data validation is in progress. 2*/\n  Validating: 2,\n  /** Data validation passed successfully. 3*/\n  ValidationPassed: 3,\n  /** Data validation failed with errors. 4 */\n  ValidationFailed: 4,\n  /** Data has been submitted and processed. 5 */\n  Submitted: 5\n};", "map": {"version": 3, "names": ["PartnerReviewerUploadStatus", "Uploading", "Uploaded", "Validating", "ValidationPassed", "ValidationFailed", "Submitted"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/core/enumertions/partnerReviewerUploadStatus.js"], "sourcesContent": ["/** Corporate with server side project, Enumerations.PartnerReviewerUploadStatus definitions. */\r\nexport const PartnerReviewerUploadStatus = {\r\n  /** Upload is in progress. 0 */\r\n  Uploading: 0,\r\n  /** File has been uploaded successfully. 1 */\r\n  Uploaded: 1,\r\n  /** Data validation is in progress. 2*/\r\n  Validating: 2,\r\n  /** Data validation passed successfully. 3*/\r\n  ValidationPassed: 3,\r\n  /** Data validation failed with errors. 4 */\r\n  ValidationFailed: 4,\r\n  /** Data has been submitted and processed. 5 */\r\n  Submitted: 5,\r\n};\r\n"], "mappings": "AAAA;AACA,OAAO,MAAMA,2BAA2B,GAAG;EACzC;EACAC,SAAS,EAAE,CAAC;EACZ;EACAC,QAAQ,EAAE,CAAC;EACX;EACAC,UAAU,EAAE,CAAC;EACb;EACAC,gBAAgB,EAAE,CAAC;EACnB;EACAC,gBAAgB,EAAE,CAAC;EACnB;EACAC,SAAS,EAAE;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}