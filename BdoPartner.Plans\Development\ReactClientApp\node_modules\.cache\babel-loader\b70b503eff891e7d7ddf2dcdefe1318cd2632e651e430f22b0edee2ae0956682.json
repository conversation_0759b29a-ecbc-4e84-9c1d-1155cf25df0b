{"ast": null, "code": "var _jsxFileName = \"C:\\\\git\\\\Partner-Site\\\\BdoPartner.Plans\\\\Development\\\\ReactClientApp\\\\src\\\\components\\\\common\\\\PartnerAutocomplete.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from \"react\";\nimport { AutoComplete } from \"primereact/autocomplete\";\nimport partnerService from \"../../services/partnerService\";\n\n/**\r\n * PartnerAutocomplete Component\r\n * \r\n * A reusable autocomplete component for selecting partners.\r\n * Provides search functionality by first name, last name, display name, or email.\r\n * \r\n * @param {Object} props - Component props\r\n * @param {Object} props.value - Selected partner object with id and displayName\r\n * @param {Function} props.onChange - Callback function when selection changes\r\n * @param {string} props.placeholder - Placeholder text for the input\r\n * @param {boolean} props.disabled - Whether the component is disabled\r\n * @param {string} props.className - Additional CSS classes\r\n * @param {string} props.id - HTML id attribute\r\n * @param {boolean} props.required - Whether the field is required\r\n * @param {Function} props.onBlur - Callback function when input loses focus\r\n * @param {Function} props.onFocus - Callback function when input gains focus\r\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const PartnerAutocomplete = ({\n  value,\n  onChange,\n  placeholder = \"Search partners...\",\n  disabled = false,\n  className = \"\",\n  id,\n  required = false,\n  onBlur,\n  onFocus\n}) => {\n  _s();\n  const [suggestions, setSuggestions] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const searchTimeoutRef = useRef(null);\n\n  // Initialize display value from the selected partner\n  useEffect(() => {\n    if (value && value.displayName) {\n      setSearchTerm(value.displayName);\n    } else if (value && typeof value === 'string') {\n      // Handle case where value is just a string (for backward compatibility)\n      setSearchTerm(value);\n    } else {\n      setSearchTerm(\"\");\n    }\n  }, [value]);\n\n  /**\r\n   * Search for partners based on the input term\r\n   * @param {string} query - Search query\r\n   */\n  const searchPartners = async query => {\n    if (!query || query.trim().length < 2) {\n      setSuggestions([]);\n      return;\n    }\n    setLoading(true);\n    try {\n      const partners = await partnerService.searchPartnersForAutocomplete(query);\n      setSuggestions(partners);\n    } catch (error) {\n      console.error(\"Error searching partners:\", error);\n      setSuggestions([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  /**\r\n   * Handle input change with debouncing\r\n   * @param {Object} event - Input change event\r\n   */\n  const handleInputChange = event => {\n    const query = event.query || event.target.value || \"\";\n    setSearchTerm(query);\n\n    // Clear previous timeout\n    if (searchTimeoutRef.current) {\n      clearTimeout(searchTimeoutRef.current);\n    }\n\n    // Set new timeout for debounced search\n    searchTimeoutRef.current = setTimeout(() => {\n      searchPartners(query);\n    }, 300); // 300ms debounce\n  };\n\n  /**\r\n   * Handle partner selection\r\n   * @param {Object} event - Selection event\r\n   */\n  const handleSelect = event => {\n    const selectedPartner = event.value;\n    if (selectedPartner && selectedPartner.id) {\n      // Update search term to show selected partner's name\n      setSearchTerm(selectedPartner.displayName);\n\n      // Call onChange with the selected partner object\n      if (onChange) {\n        onChange({\n          target: {\n            value: selectedPartner\n          }\n        });\n      }\n    }\n  };\n\n  /**\r\n   * Handle manual input (when user types without selecting from dropdown)\r\n   * @param {Object} event - Blur event\r\n   */\n  const handleBlur = event => {\n    const inputValue = event.target.value;\n\n    // If the input value doesn't match the current selection, clear the selection\n    if (value && value.displayName !== inputValue) {\n      if (onChange) {\n        onChange({\n          target: {\n            value: null\n          }\n        });\n      }\n    }\n    if (onBlur) {\n      onBlur(event);\n    }\n  };\n\n  /**\r\n   * Handle focus event\r\n   * @param {Object} event - Focus event\r\n   */\n  const handleFocus = event => {\n    if (onFocus) {\n      onFocus(event);\n    }\n  };\n\n  /**\r\n   * Template for displaying partner suggestions\r\n   * @param {Object} partner - Partner object\r\n   * @returns {JSX.Element} Rendered suggestion item\r\n   */\n  const itemTemplate = partner => {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"partner-suggestion-item\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"partner-name\",\n        children: partner.displayName\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"partner-details\",\n        children: [partner.employeeId && /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"ID: \", partner.employeeId]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 34\n        }, this), partner.department && /*#__PURE__*/_jsxDEV(\"span\", {\n          children: partner.department\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 34\n        }, this), partner.location && /*#__PURE__*/_jsxDEV(\"span\", {\n          children: partner.location\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 32\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 9\n      }, this), partner.mail && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"partner-email\",\n        children: partner.mail\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Cleanup timeout on unmount\n  useEffect(() => {\n    return () => {\n      if (searchTimeoutRef.current) {\n        clearTimeout(searchTimeoutRef.current);\n      }\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `partner-autocomplete ${className}`,\n    children: /*#__PURE__*/_jsxDEV(AutoComplete, {\n      id: id,\n      value: searchTerm,\n      suggestions: suggestions,\n      completeMethod: handleInputChange,\n      onSelect: handleSelect,\n      onBlur: handleBlur,\n      onFocus: handleFocus,\n      onChange: e => setSearchTerm(e.target.value),\n      placeholder: placeholder,\n      disabled: disabled,\n      itemTemplate: itemTemplate,\n      minLength: 2,\n      delay: 300,\n      loading: loading,\n      dropdown: false,\n      forceSelection: false,\n      className: `w-full ${loading ? 'p-autocomplete-loading' : ''}`,\n      inputClassName: \"w-full\",\n      panelClassName: \"partner-autocomplete-panel\",\n      emptyMessage: \"No partners found. Try a different search term.\",\n      required: required,\n      scrollHeight: \"320px\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 182,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 181,\n    columnNumber: 5\n  }, this);\n};\n_s(PartnerAutocomplete, \"Un/whCrwWFZxRbeA3dyY1HfA0N8=\");\n_c = PartnerAutocomplete;\nvar _c;\n$RefreshReg$(_c, \"PartnerAutocomplete\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "AutoComplete", "partnerService", "jsxDEV", "_jsxDEV", "PartnerAutocomplete", "value", "onChange", "placeholder", "disabled", "className", "id", "required", "onBlur", "onFocus", "_s", "suggestions", "setSuggestions", "loading", "setLoading", "searchTerm", "setSearchTerm", "searchTimeoutRef", "displayName", "searchPartners", "query", "trim", "length", "partners", "searchPartnersForAutocomplete", "error", "console", "handleInputChange", "event", "target", "current", "clearTimeout", "setTimeout", "handleSelect", "<PERSON><PERSON><PERSON><PERSON>", "handleBlur", "inputValue", "handleFocus", "itemTemplate", "partner", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "employeeId", "department", "location", "mail", "completeMethod", "onSelect", "e", "<PERSON><PERSON><PERSON><PERSON>", "delay", "dropdown", "forceSelection", "inputClassName", "panelClassName", "emptyMessage", "scrollHeight", "_c", "$RefreshReg$"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/components/common/PartnerAutocomplete.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from \"react\";\r\nimport { AutoComplete } from \"primereact/autocomplete\";\r\nimport partnerService from \"../../services/partnerService\";\r\n\r\n/**\r\n * PartnerAutocomplete Component\r\n * \r\n * A reusable autocomplete component for selecting partners.\r\n * Provides search functionality by first name, last name, display name, or email.\r\n * \r\n * @param {Object} props - Component props\r\n * @param {Object} props.value - Selected partner object with id and displayName\r\n * @param {Function} props.onChange - Callback function when selection changes\r\n * @param {string} props.placeholder - Placeholder text for the input\r\n * @param {boolean} props.disabled - Whether the component is disabled\r\n * @param {string} props.className - Additional CSS classes\r\n * @param {string} props.id - HTML id attribute\r\n * @param {boolean} props.required - Whether the field is required\r\n * @param {Function} props.onBlur - Callback function when input loses focus\r\n * @param {Function} props.onFocus - Callback function when input gains focus\r\n */\r\nexport const PartnerAutocomplete = ({\r\n  value,\r\n  onChange,\r\n  placeholder = \"Search partners...\",\r\n  disabled = false,\r\n  className = \"\",\r\n  id,\r\n  required = false,\r\n  onBlur,\r\n  onFocus\r\n}) => {\r\n  const [suggestions, setSuggestions] = useState([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [searchTerm, setSearchTerm] = useState(\"\");\r\n  const searchTimeoutRef = useRef(null);\r\n\r\n  // Initialize display value from the selected partner\r\n  useEffect(() => {\r\n    if (value && value.displayName) {\r\n      setSearchTerm(value.displayName);\r\n    } else if (value && typeof value === 'string') {\r\n      // Handle case where value is just a string (for backward compatibility)\r\n      setSearchTerm(value);\r\n    } else {\r\n      setSearchTerm(\"\");\r\n    }\r\n  }, [value]);\r\n\r\n  /**\r\n   * Search for partners based on the input term\r\n   * @param {string} query - Search query\r\n   */\r\n  const searchPartners = async (query) => {\r\n    if (!query || query.trim().length < 2) {\r\n      setSuggestions([]);\r\n      return;\r\n    }\r\n\r\n    setLoading(true);\r\n    try {\r\n      const partners = await partnerService.searchPartnersForAutocomplete(query);\r\n      setSuggestions(partners);\r\n    } catch (error) {\r\n      console.error(\"Error searching partners:\", error);\r\n      setSuggestions([]);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  /**\r\n   * Handle input change with debouncing\r\n   * @param {Object} event - Input change event\r\n   */\r\n  const handleInputChange = (event) => {\r\n    const query = event.query || event.target.value || \"\";\r\n    setSearchTerm(query);\r\n\r\n    // Clear previous timeout\r\n    if (searchTimeoutRef.current) {\r\n      clearTimeout(searchTimeoutRef.current);\r\n    }\r\n\r\n    // Set new timeout for debounced search\r\n    searchTimeoutRef.current = setTimeout(() => {\r\n      searchPartners(query);\r\n    }, 300); // 300ms debounce\r\n  };\r\n\r\n  /**\r\n   * Handle partner selection\r\n   * @param {Object} event - Selection event\r\n   */\r\n  const handleSelect = (event) => {\r\n    const selectedPartner = event.value;\r\n    \r\n    if (selectedPartner && selectedPartner.id) {\r\n      // Update search term to show selected partner's name\r\n      setSearchTerm(selectedPartner.displayName);\r\n      \r\n      // Call onChange with the selected partner object\r\n      if (onChange) {\r\n        onChange({\r\n          target: {\r\n            value: selectedPartner\r\n          }\r\n        });\r\n      }\r\n    }\r\n  };\r\n\r\n  /**\r\n   * Handle manual input (when user types without selecting from dropdown)\r\n   * @param {Object} event - Blur event\r\n   */\r\n  const handleBlur = (event) => {\r\n    const inputValue = event.target.value;\r\n    \r\n    // If the input value doesn't match the current selection, clear the selection\r\n    if (value && value.displayName !== inputValue) {\r\n      if (onChange) {\r\n        onChange({\r\n          target: {\r\n            value: null\r\n          }\r\n        });\r\n      }\r\n    }\r\n\r\n    if (onBlur) {\r\n      onBlur(event);\r\n    }\r\n  };\r\n\r\n  /**\r\n   * Handle focus event\r\n   * @param {Object} event - Focus event\r\n   */\r\n  const handleFocus = (event) => {\r\n    if (onFocus) {\r\n      onFocus(event);\r\n    }\r\n  };\r\n\r\n  /**\r\n   * Template for displaying partner suggestions\r\n   * @param {Object} partner - Partner object\r\n   * @returns {JSX.Element} Rendered suggestion item\r\n   */\r\n  const itemTemplate = (partner) => {\r\n    return (\r\n      <div className=\"partner-suggestion-item\">\r\n        <div className=\"partner-name\">\r\n          {partner.displayName}\r\n        </div>\r\n        <div className=\"partner-details\">\r\n          {partner.employeeId && <span>ID: {partner.employeeId}</span>}\r\n          {partner.department && <span>{partner.department}</span>}\r\n          {partner.location && <span>{partner.location}</span>}\r\n        </div>\r\n        {partner.mail && (\r\n          <div className=\"partner-email\">\r\n            {partner.mail}\r\n          </div>\r\n        )}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  // Cleanup timeout on unmount\r\n  useEffect(() => {\r\n    return () => {\r\n      if (searchTimeoutRef.current) {\r\n        clearTimeout(searchTimeoutRef.current);\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  return (\r\n    <div className={`partner-autocomplete ${className}`}>\r\n      <AutoComplete\r\n        id={id}\r\n        value={searchTerm}\r\n        suggestions={suggestions}\r\n        completeMethod={handleInputChange}\r\n        onSelect={handleSelect}\r\n        onBlur={handleBlur}\r\n        onFocus={handleFocus}\r\n        onChange={(e) => setSearchTerm(e.target.value)}\r\n        placeholder={placeholder}\r\n        disabled={disabled}\r\n        itemTemplate={itemTemplate}\r\n        minLength={2}\r\n        delay={300}\r\n        loading={loading}\r\n        dropdown={false}\r\n        forceSelection={false}\r\n        className={`w-full ${loading ? 'p-autocomplete-loading' : ''}`}\r\n        inputClassName=\"w-full\"\r\n        panelClassName=\"partner-autocomplete-panel\"\r\n        emptyMessage=\"No partners found. Try a different search term.\"\r\n        required={required}\r\n        scrollHeight=\"320px\"\r\n      />\r\n      \r\n\r\n    </div>\r\n  );\r\n};\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,YAAY,QAAQ,yBAAyB;AACtD,OAAOC,cAAc,MAAM,+BAA+B;;AAE1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAhBA,SAAAC,MAAA,IAAAC,OAAA;AAiBA,OAAO,MAAMC,mBAAmB,GAAGA,CAAC;EAClCC,KAAK;EACLC,QAAQ;EACRC,WAAW,GAAG,oBAAoB;EAClCC,QAAQ,GAAG,KAAK;EAChBC,SAAS,GAAG,EAAE;EACdC,EAAE;EACFC,QAAQ,GAAG,KAAK;EAChBC,MAAM;EACNC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACsB,UAAU,EAAEC,aAAa,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAMwB,gBAAgB,GAAGtB,MAAM,CAAC,IAAI,CAAC;;EAErC;EACAD,SAAS,CAAC,MAAM;IACd,IAAIO,KAAK,IAAIA,KAAK,CAACiB,WAAW,EAAE;MAC9BF,aAAa,CAACf,KAAK,CAACiB,WAAW,CAAC;IAClC,CAAC,MAAM,IAAIjB,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC7C;MACAe,aAAa,CAACf,KAAK,CAAC;IACtB,CAAC,MAAM;MACLe,aAAa,CAAC,EAAE,CAAC;IACnB;EACF,CAAC,EAAE,CAACf,KAAK,CAAC,CAAC;;EAEX;AACF;AACA;AACA;EACE,MAAMkB,cAAc,GAAG,MAAOC,KAAK,IAAK;IACtC,IAAI,CAACA,KAAK,IAAIA,KAAK,CAACC,IAAI,CAAC,CAAC,CAACC,MAAM,GAAG,CAAC,EAAE;MACrCV,cAAc,CAAC,EAAE,CAAC;MAClB;IACF;IAEAE,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMS,QAAQ,GAAG,MAAM1B,cAAc,CAAC2B,6BAA6B,CAACJ,KAAK,CAAC;MAC1ER,cAAc,CAACW,QAAQ,CAAC;IAC1B,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDb,cAAc,CAAC,EAAE,CAAC;IACpB,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAMa,iBAAiB,GAAIC,KAAK,IAAK;IACnC,MAAMR,KAAK,GAAGQ,KAAK,CAACR,KAAK,IAAIQ,KAAK,CAACC,MAAM,CAAC5B,KAAK,IAAI,EAAE;IACrDe,aAAa,CAACI,KAAK,CAAC;;IAEpB;IACA,IAAIH,gBAAgB,CAACa,OAAO,EAAE;MAC5BC,YAAY,CAACd,gBAAgB,CAACa,OAAO,CAAC;IACxC;;IAEA;IACAb,gBAAgB,CAACa,OAAO,GAAGE,UAAU,CAAC,MAAM;MAC1Cb,cAAc,CAACC,KAAK,CAAC;IACvB,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;EACX,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAMa,YAAY,GAAIL,KAAK,IAAK;IAC9B,MAAMM,eAAe,GAAGN,KAAK,CAAC3B,KAAK;IAEnC,IAAIiC,eAAe,IAAIA,eAAe,CAAC5B,EAAE,EAAE;MACzC;MACAU,aAAa,CAACkB,eAAe,CAAChB,WAAW,CAAC;;MAE1C;MACA,IAAIhB,QAAQ,EAAE;QACZA,QAAQ,CAAC;UACP2B,MAAM,EAAE;YACN5B,KAAK,EAAEiC;UACT;QACF,CAAC,CAAC;MACJ;IACF;EACF,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAMC,UAAU,GAAIP,KAAK,IAAK;IAC5B,MAAMQ,UAAU,GAAGR,KAAK,CAACC,MAAM,CAAC5B,KAAK;;IAErC;IACA,IAAIA,KAAK,IAAIA,KAAK,CAACiB,WAAW,KAAKkB,UAAU,EAAE;MAC7C,IAAIlC,QAAQ,EAAE;QACZA,QAAQ,CAAC;UACP2B,MAAM,EAAE;YACN5B,KAAK,EAAE;UACT;QACF,CAAC,CAAC;MACJ;IACF;IAEA,IAAIO,MAAM,EAAE;MACVA,MAAM,CAACoB,KAAK,CAAC;IACf;EACF,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAMS,WAAW,GAAIT,KAAK,IAAK;IAC7B,IAAInB,OAAO,EAAE;MACXA,OAAO,CAACmB,KAAK,CAAC;IAChB;EACF,CAAC;;EAED;AACF;AACA;AACA;AACA;EACE,MAAMU,YAAY,GAAIC,OAAO,IAAK;IAChC,oBACExC,OAAA;MAAKM,SAAS,EAAC,yBAAyB;MAAAmC,QAAA,gBACtCzC,OAAA;QAAKM,SAAS,EAAC,cAAc;QAAAmC,QAAA,EAC1BD,OAAO,CAACrB;MAAW;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,eACN7C,OAAA;QAAKM,SAAS,EAAC,iBAAiB;QAAAmC,QAAA,GAC7BD,OAAO,CAACM,UAAU,iBAAI9C,OAAA;UAAAyC,QAAA,GAAM,MAAI,EAACD,OAAO,CAACM,UAAU;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EAC3DL,OAAO,CAACO,UAAU,iBAAI/C,OAAA;UAAAyC,QAAA,EAAOD,OAAO,CAACO;QAAU;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACvDL,OAAO,CAACQ,QAAQ,iBAAIhD,OAAA;UAAAyC,QAAA,EAAOD,OAAO,CAACQ;QAAQ;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CAAC,EACLL,OAAO,CAACS,IAAI,iBACXjD,OAAA;QAAKM,SAAS,EAAC,eAAe;QAAAmC,QAAA,EAC3BD,OAAO,CAACS;MAAI;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEV,CAAC;;EAED;EACAlD,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX,IAAIuB,gBAAgB,CAACa,OAAO,EAAE;QAC5BC,YAAY,CAACd,gBAAgB,CAACa,OAAO,CAAC;MACxC;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACE/B,OAAA;IAAKM,SAAS,EAAE,wBAAwBA,SAAS,EAAG;IAAAmC,QAAA,eAClDzC,OAAA,CAACH,YAAY;MACXU,EAAE,EAAEA,EAAG;MACPL,KAAK,EAAEc,UAAW;MAClBJ,WAAW,EAAEA,WAAY;MACzBsC,cAAc,EAAEtB,iBAAkB;MAClCuB,QAAQ,EAAEjB,YAAa;MACvBzB,MAAM,EAAE2B,UAAW;MACnB1B,OAAO,EAAE4B,WAAY;MACrBnC,QAAQ,EAAGiD,CAAC,IAAKnC,aAAa,CAACmC,CAAC,CAACtB,MAAM,CAAC5B,KAAK,CAAE;MAC/CE,WAAW,EAAEA,WAAY;MACzBC,QAAQ,EAAEA,QAAS;MACnBkC,YAAY,EAAEA,YAAa;MAC3Bc,SAAS,EAAE,CAAE;MACbC,KAAK,EAAE,GAAI;MACXxC,OAAO,EAAEA,OAAQ;MACjByC,QAAQ,EAAE,KAAM;MAChBC,cAAc,EAAE,KAAM;MACtBlD,SAAS,EAAE,UAAUQ,OAAO,GAAG,wBAAwB,GAAG,EAAE,EAAG;MAC/D2C,cAAc,EAAC,QAAQ;MACvBC,cAAc,EAAC,4BAA4B;MAC3CC,YAAY,EAAC,iDAAiD;MAC9DnD,QAAQ,EAAEA,QAAS;MACnBoD,YAAY,EAAC;IAAO;MAAAlB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAGC,CAAC;AAEV,CAAC;AAAClC,EAAA,CA5LWV,mBAAmB;AAAA4D,EAAA,GAAnB5D,mBAAmB;AAAA,IAAA4D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}