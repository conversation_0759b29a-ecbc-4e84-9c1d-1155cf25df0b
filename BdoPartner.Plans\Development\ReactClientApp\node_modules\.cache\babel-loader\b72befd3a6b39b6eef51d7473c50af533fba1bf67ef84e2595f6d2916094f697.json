{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PrimeReact, { PrimeReactContext, ariaLabel, localeOption, FilterService } from 'primereact/api';\nimport { ComponentBase, useHandleStyle } from 'primereact/componentbase';\nimport { useMergeProps, useDebounce, useOverlayListener, useMountEffect, useUpdateEffect, useUnmountEffect } from 'primereact/hooks';\nimport { ChevronDownIcon } from 'primereact/icons/chevrondown';\nimport { ChevronUpIcon } from 'primereact/icons/chevronup';\nimport { SpinnerIcon } from 'primereact/icons/spinner';\nimport { TimesIcon } from 'primereact/icons/times';\nimport { OverlayService } from 'primereact/overlayservice';\nimport { Tooltip } from 'primereact/tooltip';\nimport { classNames, ObjectUtils, DomHandler, IconUtils, ZIndexUtils } from 'primereact/utils';\nimport { CSSTransition } from 'primereact/csstransition';\nimport { SearchIcon } from 'primereact/icons/search';\nimport { Portal } from 'primereact/portal';\nimport { VirtualScroller } from 'primereact/virtualscroller';\nimport { Ripple } from 'primereact/ripple';\nimport { CheckIcon } from 'primereact/icons/check';\nimport { IconBase } from 'primereact/iconbase';\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nfunction toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction toPropertyKey(t) {\n  var i = toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nfunction _defineProperty(e, r, t) {\n  return (r = toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _arrayWithHoles(r) {\n  if (Array.isArray(r)) return r;\n}\nfunction _iterableToArrayLimit(r, l) {\n  var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"];\n  if (null != t) {\n    var e,\n      n,\n      i,\n      u,\n      a = [],\n      f = !0,\n      o = !1;\n    try {\n      if (i = (t = t.call(r)).next, 0 === l) {\n        if (Object(t) !== t) return;\n        f = !1;\n      } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0);\n    } catch (r) {\n      o = !0, n = r;\n    } finally {\n      try {\n        if (!f && null != t[\"return\"] && (u = t[\"return\"](), Object(u) !== u)) return;\n      } finally {\n        if (o) throw n;\n      }\n    }\n    return a;\n  }\n}\nfunction _arrayLikeToArray$1(r, a) {\n  (null == a || a > r.length) && (a = r.length);\n  for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e];\n  return n;\n}\nfunction _unsupportedIterableToArray$1(r, a) {\n  if (r) {\n    if (\"string\" == typeof r) return _arrayLikeToArray$1(r, a);\n    var t = {}.toString.call(r).slice(8, -1);\n    return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray$1(r, a) : void 0;\n  }\n}\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _slicedToArray(r, e) {\n  return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray$1(r, e) || _nonIterableRest();\n}\nfunction ownKeys$2(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread$2(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys$2(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$2(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nvar classes = {\n  root: function root(_ref) {\n    var props = _ref.props,\n      focusedState = _ref.focusedState,\n      overlayVisibleState = _ref.overlayVisibleState,\n      context = _ref.context;\n    return classNames('p-dropdown p-component p-inputwrapper', {\n      'p-disabled': props.disabled,\n      'p-invalid': props.invalid,\n      'p-focus': focusedState,\n      'p-variant-filled': props.variant ? props.variant === 'filled' : context && context.inputStyle === 'filled',\n      'p-dropdown-clearable': props.showClear && !props.disabled,\n      'p-inputwrapper-filled': ObjectUtils.isNotEmpty(props.value),\n      'p-inputwrapper-focus': focusedState || overlayVisibleState\n    });\n  },\n  input: function input(_ref2) {\n    var props = _ref2.props,\n      label = _ref2.label;\n    return props.editable ? 'p-dropdown-label p-inputtext' : classNames('p-dropdown-label p-inputtext', {\n      'p-placeholder': label === null && props.placeholder,\n      'p-dropdown-label-empty': label === null && !props.placeholder\n    });\n  },\n  trigger: 'p-dropdown-trigger',\n  emptyMessage: 'p-dropdown-empty-message',\n  itemGroup: function itemGroup(_ref3) {\n    var optionGroupLabel = _ref3.optionGroupLabel;\n    return classNames('p-dropdown-item-group', {\n      'p-dropdown-item-empty': !optionGroupLabel || optionGroupLabel.length === 0\n    });\n  },\n  itemGroupLabel: 'p-dropdown-item-group-label',\n  dropdownIcon: 'p-dropdown-trigger-icon p-clickable',\n  loadingIcon: 'p-dropdown-trigger-icon p-clickable',\n  clearIcon: 'p-dropdown-clear-icon p-clickable',\n  filterIcon: 'p-dropdown-filter-icon',\n  filterClearIcon: 'p-dropdown-filter-clear-icon',\n  filterContainer: function filterContainer(_ref4) {\n    var clearIcon = _ref4.clearIcon;\n    return classNames('p-dropdown-filter-container', {\n      'p-dropdown-clearable-filter': !!clearIcon\n    });\n  },\n  filterInput: function filterInput(_ref5) {\n    var props = _ref5.props,\n      context = _ref5.context;\n    return classNames('p-dropdown-filter p-inputtext p-component', {\n      'p-variant-filled': props.variant ? props.variant === 'filled' : context && context.inputStyle === 'filled'\n    });\n  },\n  list: function list(_ref6) {\n    var virtualScrollerOptions = _ref6.virtualScrollerOptions;\n    return virtualScrollerOptions ? 'p-dropdown-items' : 'p-dropdown-items';\n  },\n  panel: function panel(_ref7) {\n    var context = _ref7.context;\n    return classNames('p-dropdown-panel p-component', {\n      'p-input-filled': context && context.inputStyle === 'filled' || PrimeReact.inputStyle === 'filled',\n      'p-ripple-disabled': context && context.ripple === false || PrimeReact.ripple === false\n    });\n  },\n  item: function item(_ref8) {\n    var selected = _ref8.selected,\n      disabled = _ref8.disabled,\n      label = _ref8.label,\n      index = _ref8.index,\n      focusedOptionIndex = _ref8.focusedOptionIndex,\n      highlightOnSelect = _ref8.highlightOnSelect;\n    return classNames('p-dropdown-item', {\n      'p-highlight': selected && highlightOnSelect,\n      'p-disabled': disabled,\n      'p-focus': index === focusedOptionIndex,\n      'p-dropdown-item-empty': !label || label.length === 0\n    });\n  },\n  itemLabel: 'p-dropdown-item-label',\n  checkIcon: 'p-dropdown-check-icon',\n  blankIcon: 'p-dropdown-blank-icon',\n  wrapper: 'p-dropdown-items-wrapper',\n  header: 'p-dropdown-header',\n  footer: 'p-dropdown-footer',\n  transition: 'p-connected-overlay'\n};\nvar styles = \"\\n@layer primereact {\\n    .p-dropdown {\\n        display: inline-flex;\\n        cursor: pointer;\\n        position: relative;\\n        user-select: none;\\n    }\\n    \\n    .p-dropdown-trigger {\\n        display: flex;\\n        align-items: center;\\n        justify-content: center;\\n        flex-shrink: 0;\\n    }\\n    \\n    .p-dropdown-label {\\n        display: block;\\n        white-space: nowrap;\\n        overflow: hidden;\\n        flex: 1 1 auto;\\n        width: 1%;\\n        text-overflow: ellipsis;\\n        cursor: pointer;\\n    }\\n    \\n    .p-dropdown-label-empty {\\n        overflow: hidden;\\n        visibility: hidden;\\n    }\\n    \\n    input.p-dropdown-label  {\\n        cursor: default;\\n    }\\n    \\n    .p-dropdown .p-dropdown-panel {\\n        min-width: 100%;\\n    }\\n    \\n    .p-dropdown-panel {\\n        position: absolute;\\n        top: 0;\\n        left: 0;\\n    }\\n    \\n    .p-dropdown-items-wrapper {\\n        overflow: auto;\\n    }\\n    \\n    .p-dropdown-item {\\n        cursor: pointer;\\n        font-weight: normal;\\n        white-space: nowrap;\\n        position: relative;\\n        overflow: hidden;\\n    }\\n    \\n    .p-dropdown-items {\\n        margin: 0;\\n        padding: 0;\\n        list-style-type: none;\\n    }\\n    \\n    .p-dropdown-filter {\\n        width: 100%;\\n    }\\n    \\n    .p-dropdown-filter-container {\\n        position: relative;\\n    }\\n    \\n    .p-dropdown-clear-icon,\\n    .p-dropdown-filter-icon,\\n    .p-dropdown-filter-clear-icon {\\n        position: absolute;\\n        top: 50%;\\n        margin-top: -.5rem;\\n        right: 2rem;\\n    }\\n    \\n    .p-fluid .p-dropdown {\\n        display: flex;\\n    }\\n    \\n    .p-fluid .p-dropdown .p-dropdown-label {\\n        width: 1%;\\n    }\\n}\\n\";\nvar inlineStyles = {\n  wrapper: function wrapper(_ref9) {\n    var props = _ref9.props;\n    return {\n      maxHeight: props.scrollHeight || 'auto'\n    };\n  },\n  panel: function panel(_ref10) {\n    var props = _ref10.props;\n    return _objectSpread$2({}, props.panelStyle);\n  }\n};\nvar DropdownBase = ComponentBase.extend({\n  defaultProps: {\n    __TYPE: 'Dropdown',\n    __parentMetadata: null,\n    appendTo: null,\n    ariaLabel: null,\n    ariaLabelledBy: null,\n    autoFocus: false,\n    autoOptionFocus: false,\n    checkmark: false,\n    children: undefined,\n    className: null,\n    clearIcon: null,\n    collapseIcon: null,\n    dataKey: null,\n    disabled: false,\n    dropdownIcon: null,\n    editable: false,\n    emptyFilterMessage: null,\n    emptyMessage: null,\n    filter: false,\n    filterBy: null,\n    filterClearIcon: null,\n    filterDelay: 300,\n    filterIcon: null,\n    filterInputAutoFocus: false,\n    filterLocale: undefined,\n    filterMatchMode: 'contains',\n    filterPlaceholder: null,\n    filterTemplate: null,\n    focusInputRef: null,\n    focusOnHover: true,\n    highlightOnSelect: true,\n    id: null,\n    inputId: null,\n    inputRef: null,\n    invalid: false,\n    itemTemplate: null,\n    loading: false,\n    loadingIcon: null,\n    maxLength: null,\n    name: null,\n    onBlur: null,\n    onChange: null,\n    onClick: null,\n    onContextMenu: null,\n    onFilter: null,\n    onFocus: null,\n    onHide: null,\n    onMouseDown: null,\n    onShow: null,\n    optionDisabled: null,\n    optionGroupChildren: 'items',\n    optionGroupLabel: null,\n    optionGroupTemplate: null,\n    optionLabel: null,\n    options: null,\n    optionValue: null,\n    panelClassName: null,\n    panelFooterTemplate: null,\n    panelStyle: null,\n    placeholder: null,\n    required: false,\n    resetFilterOnHide: false,\n    scrollHeight: '200px',\n    selectOnFocus: false,\n    showClear: false,\n    showFilterClear: false,\n    showOnFocus: false,\n    style: null,\n    tabIndex: null,\n    tooltip: null,\n    tooltipOptions: null,\n    transitionOptions: null,\n    useOptionAsValue: false,\n    value: null,\n    valueTemplate: null,\n    variant: null,\n    virtualScrollerOptions: null\n  },\n  css: {\n    classes: classes,\n    styles: styles,\n    inlineStyles: inlineStyles\n  }\n});\nvar BlankIcon = /*#__PURE__*/React.memo(/*#__PURE__*/React.forwardRef(function (inProps, ref) {\n  var pti = IconBase.getPTI(inProps);\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({\n    ref: ref,\n    width: \"14\",\n    height: \"14\",\n    viewBox: \"0 0 14 14\",\n    fill: \"none\",\n    xmlns: \"http://www.w3.org/2000/svg\"\n  }, pti), /*#__PURE__*/React.createElement(\"rect\", {\n    width: \"1\",\n    height: \"1\",\n    fill: \"currentColor\",\n    fillOpacity: \"0\"\n  }));\n}));\nBlankIcon.displayName = 'BlankIcon';\nvar DropdownItem = /*#__PURE__*/React.memo(function (props) {\n  var mergeProps = useMergeProps();\n  var ptm = props.ptm,\n    cx = props.cx,\n    selected = props.selected,\n    disabled = props.disabled,\n    option = props.option,\n    label = props.label,\n    index = props.index,\n    focusedOptionIndex = props.focusedOptionIndex,\n    ariaSetSize = props.ariaSetSize,\n    checkmark = props.checkmark,\n    highlightOnSelect = props.highlightOnSelect,\n    onInputKeyDown = props.onInputKeyDown;\n  var getPTOptions = function getPTOptions(key) {\n    return ptm(key, {\n      context: {\n        selected: selected,\n        disabled: disabled,\n        focused: index === focusedOptionIndex\n      }\n    });\n  };\n  var _onClick = function onClick(event, i) {\n    if (props.onClick) {\n      props.onClick({\n        originalEvent: event,\n        option: option\n      });\n    }\n  };\n  var content = props.template ? ObjectUtils.getJSXElement(props.template, props.option) : props.label;\n  var itemProps = mergeProps({\n    id: \"dropdownItem_\".concat(index),\n    role: 'option',\n    className: classNames(option.className, cx('item', {\n      selected: selected,\n      disabled: disabled,\n      label: label,\n      index: index,\n      focusedOptionIndex: focusedOptionIndex,\n      highlightOnSelect: highlightOnSelect\n    })),\n    style: props.style,\n    tabIndex: 0,\n    onClick: function onClick(e) {\n      return _onClick(e);\n    },\n    onKeyDown: function onKeyDown(e) {\n      return onInputKeyDown(e);\n    },\n    onMouseMove: function onMouseMove(e) {\n      return props === null || props === void 0 ? void 0 : props.onMouseMove(e, index);\n    },\n    'aria-setsize': ariaSetSize,\n    'aria-posinset': index + 1,\n    'aria-label': label,\n    'aria-selected': selected,\n    'data-p-highlight': selected,\n    'data-p-focused': focusedOptionIndex === index,\n    'data-p-disabled': disabled\n  }, getPTOptions('item'));\n  var itemGroupLabelProps = mergeProps({\n    className: cx('itemLabel')\n  }, getPTOptions('itemLabel'));\n  var iconRenderer = function iconRenderer() {\n    if (selected) {\n      var checkIconProps = mergeProps({\n        className: cx('checkIcon')\n      }, getPTOptions('checkIcon'));\n      return /*#__PURE__*/React.createElement(CheckIcon, checkIconProps);\n    }\n    var blankIconProps = mergeProps({\n      className: cx('blankIcon')\n    }, getPTOptions('blankIcon'));\n    return /*#__PURE__*/React.createElement(BlankIcon, blankIconProps);\n  };\n  return /*#__PURE__*/React.createElement(\"li\", _extends({\n    key: props.label\n  }, itemProps), checkmark && iconRenderer(), /*#__PURE__*/React.createElement(\"span\", itemGroupLabelProps, content), /*#__PURE__*/React.createElement(Ripple, null));\n});\nDropdownItem.displayName = 'DropdownItem';\nfunction ownKeys$1(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread$1(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys$1(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$1(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nvar DropdownPanel = /*#__PURE__*/React.memo(/*#__PURE__*/React.forwardRef(function (props, ref) {\n  var mergeProps = useMergeProps();\n  var ptm = props.ptm,\n    cx = props.cx,\n    sx = props.sx;\n  var context = React.useContext(PrimeReactContext);\n  var filterInputRef = React.useRef(null);\n  var isEmptyFilter = !(props.visibleOptions && props.visibleOptions.length) && props.hasFilter;\n  var ariaSetSize = props.visibleOptions ? props.visibleOptions.length : 0;\n  var filterOptions = {\n    filter: function filter(e) {\n      return onFilterInputChange(e);\n    },\n    reset: function reset() {\n      return props.resetFilter();\n    }\n  };\n  var getPTOptions = function getPTOptions(key, options) {\n    return ptm(key, _objectSpread$1({\n      hostName: props.hostName\n    }, options));\n  };\n  var onEnter = function onEnter() {\n    props.onEnter(function () {\n      if (props.virtualScrollerRef.current) {\n        var selectedIndex = props.getSelectedOptionIndex();\n        if (selectedIndex !== -1) {\n          setTimeout(function () {\n            return props.virtualScrollerRef.current.scrollToIndex(selectedIndex);\n          }, 0);\n        }\n      }\n    });\n  };\n  var onEntered = function onEntered() {\n    props.onEntered(function () {\n      if (props.filter && props.filterInputAutoFocus) {\n        DomHandler.focus(filterInputRef.current, false);\n      }\n    });\n  };\n  var onFilterInputChange = function onFilterInputChange(event) {\n    props.onFilterInputChange && props.onFilterInputChange(event);\n  };\n  var createFooter = function createFooter() {\n    if (props.panelFooterTemplate) {\n      var content = ObjectUtils.getJSXElement(props.panelFooterTemplate, props, props.onOverlayHide);\n      var footerProps = mergeProps({\n        className: cx('footer')\n      }, getPTOptions('footer'));\n      return /*#__PURE__*/React.createElement(\"div\", footerProps, content);\n    }\n    return null;\n  };\n  var changeFocusedItemOnHover = function changeFocusedItemOnHover(event, index) {\n    if (props.focusOnHover) {\n      var _props$changeFocusedO;\n      props === null || props === void 0 || (_props$changeFocusedO = props.changeFocusedOptionIndex) === null || _props$changeFocusedO === void 0 || _props$changeFocusedO.call(props, event, index);\n    }\n  };\n  var createEmptyMessage = function createEmptyMessage(emptyMessage, isFilter) {\n    var message = ObjectUtils.getJSXElement(emptyMessage, props) || localeOption(isFilter ? 'emptyFilterMessage' : 'emptyMessage');\n    var emptyMessageProps = mergeProps({\n      className: cx('emptyMessage')\n    }, getPTOptions('emptyMessage'));\n    return /*#__PURE__*/React.createElement(\"li\", emptyMessageProps, message);\n  };\n  var createItem = function createItem(option, index) {\n    var scrollerOptions = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    var style = {\n      height: scrollerOptions.props ? scrollerOptions.props.itemSize : undefined\n    };\n    style = _objectSpread$1(_objectSpread$1({}, style), option.style);\n    if (option.group && props.optionGroupLabel) {\n      var optionGroupLabel = props.optionGroupLabel;\n      var groupContent = props.optionGroupTemplate ? ObjectUtils.getJSXElement(props.optionGroupTemplate, option, index) : props.getOptionGroupLabel(option);\n      var key = index + '_' + props.getOptionGroupRenderKey(option);\n      var itemGroupProps = mergeProps({\n        className: cx('itemGroup', {\n          optionGroupLabel: optionGroupLabel\n        }),\n        style: style,\n        'data-p-highlight': props.selected\n      }, getPTOptions('itemGroup'));\n      var itemGroupLabelProps = mergeProps({\n        className: cx('itemGroupLabel')\n      }, getPTOptions('itemGroupLabel'));\n      return /*#__PURE__*/React.createElement(\"li\", _extends({\n        key: key\n      }, itemGroupProps), /*#__PURE__*/React.createElement(\"span\", itemGroupLabelProps, groupContent));\n    }\n    var optionKey = props.getOptionRenderKey(option) + '_' + index;\n    var optionLabel = props.getOptionLabel(option);\n    var disabled = props.isOptionDisabled(option);\n    return /*#__PURE__*/React.createElement(DropdownItem, {\n      key: optionKey,\n      label: optionLabel,\n      index: index,\n      focusedOptionIndex: props.focusedOptionIndex,\n      option: option,\n      ariaSetSize: ariaSetSize,\n      onInputKeyDown: props.onInputKeyDown,\n      style: style,\n      template: props.itemTemplate,\n      selected: props.isSelected(option),\n      highlightOnSelect: props.highlightOnSelect,\n      disabled: disabled,\n      onClick: props.onOptionClick,\n      onMouseMove: changeFocusedItemOnHover,\n      ptm: ptm,\n      cx: cx,\n      checkmark: props.checkmark\n    });\n  };\n  var createItems = function createItems() {\n    if (ObjectUtils.isNotEmpty(props.visibleOptions)) {\n      return props.visibleOptions.map(createItem);\n    } else if (props.hasFilter) {\n      return createEmptyMessage(props.emptyFilterMessage, true);\n    }\n    return createEmptyMessage(props.emptyMessage);\n  };\n  var createFilterClearIcon = function createFilterClearIcon() {\n    if (props.showFilterClear && props.filterValue) {\n      var ariaLabelFilterClear = localeOption('clear');\n      var clearIconProps = mergeProps({\n        className: cx('filterClearIcon'),\n        'aria-label': ariaLabelFilterClear,\n        onClick: function onClick() {\n          return props.onFilterClearIconClick(function () {\n            return DomHandler.focus(filterInputRef.current);\n          });\n        }\n      }, getPTOptions('filterClearIcon'));\n      var icon = props.filterClearIcon || /*#__PURE__*/React.createElement(TimesIcon, clearIconProps);\n      var filterClearIcon = IconUtils.getJSXIcon(icon, _objectSpread$1({}, clearIconProps), {\n        props: props\n      });\n      return filterClearIcon;\n    }\n    return null;\n  };\n  var createFilter = function createFilter() {\n    if (props.filter) {\n      var clearIcon = createFilterClearIcon();\n      var filterIconProps = mergeProps({\n        className: cx('filterIcon')\n      }, getPTOptions('filterIcon'));\n      var icon = props.filterIcon || /*#__PURE__*/React.createElement(SearchIcon, filterIconProps);\n      var filterIcon = IconUtils.getJSXIcon(icon, _objectSpread$1({}, filterIconProps), {\n        props: props\n      });\n      var filterContainerProps = mergeProps({\n        className: cx('filterContainer', {\n          clearIcon: clearIcon\n        })\n      }, getPTOptions('filterContainer'));\n      var filterInputProps = mergeProps({\n        ref: filterInputRef,\n        type: 'text',\n        autoComplete: 'off',\n        className: cx('filterInput', {\n          context: context\n        }),\n        placeholder: props.filterPlaceholder,\n        onKeyDown: props.onFilterInputKeyDown,\n        onChange: function onChange(e) {\n          return onFilterInputChange(e);\n        },\n        value: props.filterValue\n      }, getPTOptions('filterInput'));\n      var content = /*#__PURE__*/React.createElement(\"div\", filterContainerProps, /*#__PURE__*/React.createElement(\"input\", filterInputProps), clearIcon, filterIcon);\n      if (props.filterTemplate) {\n        var defaultContentOptions = {\n          className: classNames('p-dropdown-filter-container', {\n            'p-dropdown-clearable-filter': !!clearIcon\n          }),\n          element: content,\n          filterOptions: filterOptions,\n          filterInputKeyDown: props.onFilterInputKeyDown,\n          filterInputChange: onFilterInputChange,\n          filterIconClassName: 'p-dropdown-filter-icon',\n          clearIcon: clearIcon,\n          props: props\n        };\n        content = ObjectUtils.getJSXElement(props.filterTemplate, defaultContentOptions);\n      }\n      var headerProps = mergeProps({\n        className: cx('header')\n      }, getPTOptions('header'));\n      return /*#__PURE__*/React.createElement(\"div\", headerProps, content);\n    }\n    return null;\n  };\n  var createContent = function createContent() {\n    if (props.virtualScrollerOptions) {\n      var virtualScrollerProps = _objectSpread$1(_objectSpread$1({}, props.virtualScrollerOptions), {\n        style: _objectSpread$1(_objectSpread$1({}, props.virtualScrollerOptions.style), {\n          height: props.scrollHeight\n        }),\n        className: classNames('p-dropdown-items-wrapper', props.virtualScrollerOptions.className),\n        items: props.visibleOptions,\n        autoSize: true,\n        onLazyLoad: function onLazyLoad(event) {\n          return props.virtualScrollerOptions.onLazyLoad(_objectSpread$1(_objectSpread$1({}, event), {\n            filter: props.filterValue\n          }));\n        },\n        itemTemplate: function itemTemplate(item, options) {\n          return item && createItem(item, options.index, options);\n        },\n        contentTemplate: function contentTemplate(options) {\n          var emptyMessage = props.hasFilter ? props.emptyFilterMessage : props.emptyMessage;\n          var content = isEmptyFilter ? createEmptyMessage(emptyMessage) : options.children;\n          var listProps = mergeProps({\n            ref: options.contentRef,\n            style: options.style,\n            className: classNames(options.className, cx('list', {\n              virtualScrollerProps: props.virtualScrollerOptions\n            })),\n            role: 'listbox',\n            'aria-label': ariaLabel('listLabel')\n          }, getPTOptions('list'));\n          return /*#__PURE__*/React.createElement(\"ul\", listProps, content);\n        }\n      });\n      return /*#__PURE__*/React.createElement(VirtualScroller, _extends({\n        ref: props.virtualScrollerRef\n      }, virtualScrollerProps, {\n        pt: ptm('virtualScroller')\n      }));\n    }\n    var items = createItems();\n    var wrapperProps = mergeProps({\n      className: cx('wrapper'),\n      style: sx('wrapper')\n    }, getPTOptions('wrapper'));\n    var listProps = mergeProps({\n      className: cx('list'),\n      role: 'listbox',\n      'aria-label': ariaLabel('listLabel')\n    }, getPTOptions('list'));\n    return /*#__PURE__*/React.createElement(\"div\", wrapperProps, /*#__PURE__*/React.createElement(\"ul\", listProps, items));\n  };\n  var createElement = function createElement() {\n    var filter = createFilter();\n    var content = createContent();\n    var footer = createFooter();\n    var panelProps = mergeProps({\n      className: classNames(props.panelClassName, cx('panel', {\n        context: context\n      })),\n      style: sx('panel'),\n      onClick: props.onClick\n    }, getPTOptions('panel'));\n    var transitionProps = mergeProps({\n      classNames: cx('transition'),\n      \"in\": props[\"in\"],\n      timeout: {\n        enter: 120,\n        exit: 100\n      },\n      options: props.transitionOptions,\n      unmountOnExit: true,\n      onEnter: onEnter,\n      onEntered: onEntered,\n      onExit: props.onExit,\n      onExited: props.onExited\n    }, getPTOptions('transition'));\n    return /*#__PURE__*/React.createElement(CSSTransition, _extends({\n      nodeRef: ref\n    }, transitionProps), /*#__PURE__*/React.createElement(\"div\", _extends({\n      ref: ref\n    }, panelProps), props.firstFocusableElement, filter, content, footer, props.lastFocusableElement));\n  };\n  var element = createElement();\n  return /*#__PURE__*/React.createElement(Portal, {\n    element: element,\n    appendTo: props.appendTo\n  });\n}));\nDropdownPanel.displayName = 'DropdownPanel';\nfunction _createForOfIteratorHelper(r, e) {\n  var t = \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"];\n  if (!t) {\n    if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && \"number\" == typeof r.length) {\n      t && (r = t);\n      var _n = 0,\n        F = function F() {};\n      return {\n        s: F,\n        n: function n() {\n          return _n >= r.length ? {\n            done: !0\n          } : {\n            done: !1,\n            value: r[_n++]\n          };\n        },\n        e: function e(r) {\n          throw r;\n        },\n        f: F\n      };\n    }\n    throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n  }\n  var o,\n    a = !0,\n    u = !1;\n  return {\n    s: function s() {\n      t = t.call(r);\n    },\n    n: function n() {\n      var r = t.next();\n      return a = r.done, r;\n    },\n    e: function e(r) {\n      u = !0, o = r;\n    },\n    f: function f() {\n      try {\n        a || null == t[\"return\"] || t[\"return\"]();\n      } finally {\n        if (u) throw o;\n      }\n    }\n  };\n}\nfunction _unsupportedIterableToArray(r, a) {\n  if (r) {\n    if (\"string\" == typeof r) return _arrayLikeToArray(r, a);\n    var t = {}.toString.call(r).slice(8, -1);\n    return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0;\n  }\n}\nfunction _arrayLikeToArray(r, a) {\n  (null == a || a > r.length) && (a = r.length);\n  for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e];\n  return n;\n}\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nvar Dropdown = /*#__PURE__*/React.memo(/*#__PURE__*/React.forwardRef(function (inProps, ref) {\n  var mergeProps = useMergeProps();\n  var context = React.useContext(PrimeReactContext);\n  var props = DropdownBase.getProps(inProps, context);\n  var _useDebounce = useDebounce('', props.filterDelay || 0),\n    _useDebounce2 = _slicedToArray(_useDebounce, 3),\n    filterValue = _useDebounce2[0],\n    filterState = _useDebounce2[1],\n    setFilterState = _useDebounce2[2];\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    focusedState = _React$useState2[0],\n    setFocusedState = _React$useState2[1];\n  var _React$useState3 = React.useState(-1),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    focusedOptionIndex = _React$useState4[0],\n    setFocusedOptionIndex = _React$useState4[1];\n  var _React$useState5 = React.useState(false),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    overlayVisibleState = _React$useState6[0],\n    setOverlayVisibleState = _React$useState6[1];\n  var clickedRef = React.useRef(false);\n  var elementRef = React.useRef(null);\n  var overlayRef = React.useRef(null);\n  var firstHiddenFocusableElementOnOverlay = React.useRef(null);\n  var lastHiddenFocusableElementOnOverlay = React.useRef(null);\n  var inputRef = React.useRef(props.inputRef);\n  var focusInputRef = React.useRef(props.focusInputRef);\n  var virtualScrollerRef = React.useRef(null);\n  var searchTimeout = React.useRef(null);\n  var searchValue = React.useRef(null);\n  var isLazy = props.virtualScrollerOptions && props.virtualScrollerOptions.lazy;\n  var hasFilter = ObjectUtils.isNotEmpty(filterState);\n  var appendTo = props.appendTo || context && context.appendTo || PrimeReact.appendTo;\n  var _DropdownBase$setMeta = DropdownBase.setMetaData(_objectSpread(_objectSpread({\n      props: props\n    }, props.__parentMetadata), {}, {\n      state: {\n        filter: filterState,\n        focused: focusedState,\n        overlayVisible: overlayVisibleState\n      }\n    })),\n    ptm = _DropdownBase$setMeta.ptm,\n    cx = _DropdownBase$setMeta.cx,\n    sx = _DropdownBase$setMeta.sx,\n    isUnstyled = _DropdownBase$setMeta.isUnstyled;\n  useHandleStyle(DropdownBase.css.styles, isUnstyled, {\n    name: 'dropdown'\n  });\n  var _useOverlayListener = useOverlayListener({\n      target: elementRef,\n      overlay: overlayRef,\n      listener: function listener(event, _ref) {\n        var type = _ref.type,\n          valid = _ref.valid;\n        if (valid) {\n          if (type === 'outside') {\n            if (!isClearClicked(event)) {\n              hide();\n            }\n          } else if (context.hideOverlaysOnDocumentScrolling) {\n            hide();\n          } else if (!DomHandler.isDocument(event.target)) {\n            alignOverlay();\n          }\n        }\n      },\n      when: overlayVisibleState\n    }),\n    _useOverlayListener2 = _slicedToArray(_useOverlayListener, 2),\n    bindOverlayListener = _useOverlayListener2[0],\n    unbindOverlayListener = _useOverlayListener2[1];\n  var flatOptions = function flatOptions(options) {\n    return (options || []).reduce(function (result, option, index) {\n      result.push(_objectSpread(_objectSpread({}, option), {}, {\n        group: true,\n        index: index\n      }));\n      var optionGroupChildren = getOptionGroupChildren(option);\n      optionGroupChildren && optionGroupChildren.forEach(function (o) {\n        return result.push(o);\n      });\n      return result;\n    }, []);\n  };\n  var getVisibleOptions = function getVisibleOptions() {\n    var options = props.optionGroupLabel ? flatOptions(props.options) : props.options;\n    if (hasFilter && !isLazy) {\n      var _filterValue = filterState.trim().toLocaleLowerCase(props.filterLocale);\n      var searchFields = props.filterBy ? props.filterBy.split(',') : [props.optionLabel || 'label'];\n      if (props.optionGroupLabel) {\n        var filteredGroups = [];\n        var _iterator = _createForOfIteratorHelper(props.options),\n          _step;\n        try {\n          for (_iterator.s(); !(_step = _iterator.n()).done;) {\n            var optgroup = _step.value;\n            var filteredSubOptions = FilterService.filter(getOptionGroupChildren(optgroup), searchFields, _filterValue, props.filterMatchMode, props.filterLocale);\n            if (filteredSubOptions && filteredSubOptions.length) {\n              filteredGroups.push(_objectSpread(_objectSpread({}, optgroup), _defineProperty({}, \"\".concat(props.optionGroupChildren), filteredSubOptions)));\n            }\n          }\n        } catch (err) {\n          _iterator.e(err);\n        } finally {\n          _iterator.f();\n        }\n        return flatOptions(filteredGroups);\n      }\n      return FilterService.filter(options, searchFields, _filterValue, props.filterMatchMode, props.filterLocale);\n    }\n    return options;\n  };\n  var onFirstHiddenFocus = function onFirstHiddenFocus(event) {\n    var focusableEl = event.relatedTarget === focusInputRef.current ? DomHandler.getFirstFocusableElement(overlayRef.current, ':not([data-p-hidden-focusable=\"true\"])') : focusInputRef.current;\n    DomHandler.focus(focusableEl);\n  };\n  var onLastHiddenFocus = function onLastHiddenFocus(event) {\n    var focusableEl = event.relatedTarget === focusInputRef.current ? DomHandler.getLastFocusableElement(overlayRef.current, ':not([data-p-hidden-focusable=\"true\"])') : focusInputRef.current;\n    DomHandler.focus(focusableEl);\n  };\n  var isClearClicked = function isClearClicked(event) {\n    return DomHandler.isAttributeEquals(event.target, 'data-pc-section', 'clearicon') || DomHandler.isAttributeEquals(event.target.parentElement || event.target, 'data-pc-section', 'filterclearicon');\n  };\n  var _onClick = function onClick(event) {\n    if (props.disabled || props.loading) {\n      return;\n    }\n    props.onClick && props.onClick(event);\n\n    // do not continue if the user defined click wants to prevent it\n    if (event.defaultPrevented) {\n      return;\n    }\n    if (isClearClicked(event) || event.target.tagName === 'INPUT') {\n      return;\n    } else if (!overlayRef.current || !(overlayRef.current && overlayRef.current.contains(event.target))) {\n      DomHandler.focus(focusInputRef.current);\n      overlayVisibleState ? hide() : show();\n    }\n    event.preventDefault();\n    clickedRef.current = true;\n  };\n  var onInputFocus = function onInputFocus(event) {\n    if (props.showOnFocus && !overlayVisibleState) {\n      show();\n    }\n    setFocusedState(true);\n    props.onFocus && props.onFocus(event);\n  };\n  var onInputBlur = function onInputBlur(event) {\n    setFocusedState(false);\n    if (props.onBlur) {\n      setTimeout(function () {\n        var currentValue = inputRef.current ? inputRef.current.value : undefined;\n        props.onBlur({\n          originalEvent: event.originalEvent,\n          value: currentValue,\n          stopPropagation: function stopPropagation() {\n            event.originalEvent.stopPropagation();\n          },\n          preventDefault: function preventDefault() {\n            event.originalEvent.preventDefault();\n          },\n          target: {\n            name: props.name,\n            id: props.id,\n            value: currentValue\n          }\n        });\n      }, 200);\n    }\n  };\n  var onOptionSelect = function onOptionSelect(event, option) {\n    var isHide = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;\n    selectItem({\n      originalEvent: event,\n      option: option\n    });\n    if (isHide) {\n      hide();\n      DomHandler.focus(focusInputRef.current);\n    }\n  };\n  var onPanelClick = function onPanelClick(event) {\n    OverlayService.emit('overlay-click', {\n      originalEvent: event,\n      target: elementRef.current\n    });\n  };\n  var onInputKeyDown = function onInputKeyDown(event) {\n    if (props.disabled) {\n      event.preventDefault();\n      return;\n    }\n    var code = DomHandler.isAndroid() ? event.key : event.code;\n    switch (code) {\n      case 'ArrowDown':\n        onArrowDownKey(event);\n        break;\n      case 'ArrowUp':\n        onArrowUpKey(event);\n        break;\n      case 'ArrowLeft':\n      case 'ArrowRight':\n        onArrowLeftKey(event, props.editable);\n        break;\n      case 'Home':\n        onHomeKey(event);\n        break;\n      case 'End':\n        onEndKey(event);\n        break;\n      case 'PageDown':\n        onPageDownKey(event);\n        break;\n      case 'PageUp':\n        onPageUpKey(event);\n        break;\n      case 'Space':\n        onSpaceKey(event, props.editable);\n        break;\n      case 'NumpadEnter':\n      case 'Enter':\n        onEnterKey(event);\n        break;\n      case 'Escape':\n        onEscapeKey(event);\n        break;\n      case 'Tab':\n        onTabKey(event);\n        break;\n      case 'Backspace':\n        onBackspaceKey(event, props.editable);\n        break;\n      case 'ShiftLeft':\n      case 'ShiftRight':\n        //NOOP\n        break;\n      default:\n        var metaKey = event.metaKey || event.ctrlKey || event.altKey;\n\n        // Only handle printable characters when no meta keys are pressed\n        if (!metaKey && ObjectUtils.isPrintableCharacter(event.key)) {\n          !overlayVisibleState && !props.editable && show();\n          !props.editable && searchOptions(event, event.key);\n        }\n        break;\n    }\n    clickedRef.current = false;\n  };\n  var onFilterInputKeyDown = function onFilterInputKeyDown(event) {\n    switch (event.code) {\n      case 'ArrowDown':\n        onArrowDownKey(event);\n        break;\n      case 'ArrowUp':\n        onArrowUpKey(event);\n        break;\n      case 'ArrowLeft':\n      case 'ArrowRight':\n        onArrowLeftKey(event, true);\n        break;\n      case 'Enter':\n      case 'NumpadEnter':\n        onEnterKey(event);\n        event.preventDefault();\n        break;\n      case 'Escape':\n        onEscapeKey(event);\n        break;\n    }\n  };\n  var hasFocusableElements = function hasFocusableElements() {\n    return DomHandler.getFocusableElements(overlayRef.current, ':not([data-p-hidden-focusable=\"true\"])').length > 0;\n  };\n  var isOptionMatched = function isOptionMatched(option) {\n    var _getOptionLabel;\n    return isValidOption(option) && ((_getOptionLabel = getOptionLabel(option)) === null || _getOptionLabel === void 0 ? void 0 : _getOptionLabel.toLocaleLowerCase(props.filterLocale).startsWith(searchValue.current.toLocaleLowerCase(props.filterLocale)));\n  };\n  var isValidOption = function isValidOption(option) {\n    return ObjectUtils.isNotEmpty(option) && !(isOptionDisabled(option) || isOptionGroup(option));\n  };\n  var hasSelectedOption = function hasSelectedOption() {\n    return ObjectUtils.isNotEmpty(props.value);\n  };\n  var isValidSelectedOption = function isValidSelectedOption(option) {\n    return isValidOption(option) && isSelected(option);\n  };\n  var findSelectedOptionIndex = function findSelectedOptionIndex() {\n    return hasSelectedOption ? visibleOptions.findIndex(function (option) {\n      return isValidSelectedOption(option);\n    }) : -1;\n  };\n  var findFirstFocusedOptionIndex = function findFirstFocusedOptionIndex() {\n    var selectedIndex = findSelectedOptionIndex();\n    return selectedIndex < 0 ? findFirstOptionIndex() : selectedIndex;\n  };\n  var searchOptions = function searchOptions(event, _char) {\n    searchValue.current = (searchValue.current || '') + _char;\n    var optionIndex = -1;\n    var matched = false;\n    if (ObjectUtils.isNotEmpty(searchValue.current)) {\n      if (focusedOptionIndex !== -1) {\n        optionIndex = visibleOptions.slice(focusedOptionIndex).findIndex(function (option) {\n          return isOptionMatched(option);\n        });\n        optionIndex = optionIndex === -1 ? visibleOptions.slice(0, focusedOptionIndex).findIndex(function (option) {\n          return isOptionMatched(option);\n        }) : optionIndex + focusedOptionIndex;\n      } else {\n        optionIndex = visibleOptions.findIndex(function (option) {\n          return isOptionMatched(option);\n        });\n      }\n      if (optionIndex !== -1) {\n        matched = true;\n      }\n      if (optionIndex === -1 && focusedOptionIndex === -1) {\n        optionIndex = findFirstFocusedOptionIndex();\n      }\n      if (optionIndex !== -1) {\n        changeFocusedOptionIndex(event, optionIndex);\n      }\n    }\n    if (searchTimeout.current) {\n      clearTimeout(searchTimeout.current);\n    }\n    searchTimeout.current = setTimeout(function () {\n      searchValue.current = '';\n      searchTimeout.current = null;\n    }, 500);\n    return matched;\n  };\n  var findLastFocusedOptionIndex = function findLastFocusedOptionIndex() {\n    var selectedIndex = findSelectedOptionIndex();\n    return selectedIndex < 0 ? findLastOptionIndex() : selectedIndex;\n  };\n  var findFirstOptionIndex = function findFirstOptionIndex() {\n    return visibleOptions.findIndex(function (option) {\n      return isValidOption(option);\n    });\n  };\n  var findLastOptionIndex = function findLastOptionIndex() {\n    return ObjectUtils.findLastIndex(visibleOptions, function (option) {\n      return isValidOption(option);\n    });\n  };\n  var findNextOptionIndex = function findNextOptionIndex(index) {\n    var matchedOptionIndex = index < visibleOptions.length - 1 ? visibleOptions.slice(index + 1).findIndex(function (option) {\n      return isValidOption(option);\n    }) : -1;\n    return matchedOptionIndex > -1 ? matchedOptionIndex + index + 1 : index;\n  };\n  var findPrevOptionIndex = function findPrevOptionIndex(index) {\n    var matchedOptionIndex = index > 0 ? ObjectUtils.findLastIndex(visibleOptions.slice(0, index), function (option) {\n      return isValidOption(option);\n    }) : -1;\n    return matchedOptionIndex > -1 ? matchedOptionIndex : index;\n  };\n  var changeFocusedOptionIndex = function changeFocusedOptionIndex(event, index) {\n    if (focusedOptionIndex !== index) {\n      setFocusedOptionIndex(index);\n      focusOnItem(index);\n      if (props.selectOnFocus) {\n        onOptionSelect(event, visibleOptions[index], false);\n      }\n    }\n  };\n  var focusOnItem = function focusOnItem(index) {\n    var focusedItem = DomHandler.findSingle(overlayRef.current, \"li[id=\\\"dropdownItem_\".concat(index, \"\\\"]\"));\n    focusedItem && focusedItem.focus();\n  };\n  var onArrowDownKey = function onArrowDownKey(event) {\n    if (!overlayVisibleState) {\n      show();\n      props.editable && changeFocusedOptionIndex(event, findSelectedOptionIndex());\n    } else {\n      var optionIndex = focusedOptionIndex !== -1 ? findNextOptionIndex(focusedOptionIndex) : clickedRef.current ? findFirstOptionIndex() : findFirstFocusedOptionIndex();\n      changeFocusedOptionIndex(event, optionIndex);\n    }\n    event.preventDefault();\n  };\n  var onArrowUpKey = function onArrowUpKey(event) {\n    var pressedInInputText = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    if (event.altKey && !pressedInInputText) {\n      if (focusedOptionIndex !== -1) {\n        onOptionSelect(event, visibleOptions[focusedOptionIndex]);\n      }\n      state.overlayVisible && hide();\n      event.preventDefault();\n    } else {\n      var optionIndex = focusedOptionIndex !== -1 ? findPrevOptionIndex(focusedOptionIndex) : clickedRef.current ? findLastOptionIndex() : findLastFocusedOptionIndex();\n      changeFocusedOptionIndex(event, optionIndex);\n      !overlayVisibleState && show();\n      event.preventDefault();\n    }\n  };\n  var onArrowLeftKey = function onArrowLeftKey(event) {\n    var pressedInInputText = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    pressedInInputText && setFocusedOptionIndex(-1);\n  };\n  var onHomeKey = function onHomeKey(event) {\n    var pressedInInputText = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    if (pressedInInputText) {\n      event.currentTarget.setSelectionRange(0, 0);\n      setFocusedOptionIndex(-1);\n    } else {\n      changeFocusedOptionIndex(event, findFirstOptionIndex());\n      !overlayVisibleState && show();\n    }\n    event.preventDefault();\n  };\n  var onEndKey = function onEndKey(event) {\n    var pressedInInputText = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    if (pressedInInputText) {\n      var target = event.currentTarget;\n      var len = target.value.length;\n      target.setSelectionRange(len, len);\n      setFocusedOptionIndex(-1);\n    } else {\n      changeFocusedOptionIndex(event, findLastOptionIndex());\n      !overlayVisibleState && show();\n    }\n    event.preventDefault();\n  };\n  var onPageUpKey = function onPageUpKey(event) {\n    event.preventDefault();\n  };\n  var onPageDownKey = function onPageDownKey(event) {\n    event.preventDefault();\n  };\n  var onSpaceKey = function onSpaceKey(event) {\n    var pressedInInputText = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    !pressedInInputText && onEnterKey(event);\n  };\n  var onEnterKey = function onEnterKey(event) {\n    event.preventDefault();\n    if (!overlayVisibleState) {\n      setFocusedOptionIndex(-1);\n      onArrowDownKey(event);\n    } else {\n      if (focusedOptionIndex === -1) {\n        return;\n      }\n      var focusedOption = visibleOptions[focusedOptionIndex];\n      var optionValue = getOptionValue(focusedOption);\n      if (optionValue == null || optionValue == undefined) {\n        hide();\n        resetFilter();\n        updateEditableLabel(selectedOption);\n        return;\n      }\n      onOptionSelect(event, focusedOption);\n    }\n  };\n  var onEscapeKey = function onEscapeKey(event) {\n    overlayVisibleState && hide();\n    event.preventDefault();\n  };\n  var onTabKey = function onTabKey(event) {\n    var pressedInInputText = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    if (!pressedInInputText) {\n      if (overlayVisibleState && !hasFocusableElements()) {\n        DomHandler.focus(firstHiddenFocusableElementOnOverlay.current);\n        event.preventDefault();\n      } else {\n        if (focusedOptionIndex !== -1) {\n          onOptionSelect(event, visibleOptions[focusedOptionIndex]);\n        }\n        overlayVisibleState && hide();\n      }\n    }\n  };\n  var onBackspaceKey = function onBackspaceKey(event) {\n    var pressedInInputText = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    if (event && pressedInInputText) {\n      !overlayVisibleState && show();\n    }\n  };\n  var findInArray = function findInArray(visibleOptions, searchText) {\n    if (!searchText || !(visibleOptions !== null && visibleOptions !== void 0 && visibleOptions.length)) return -1;\n    var normalizedSearch = searchText.toLocaleLowerCase();\n    var exactMatch = visibleOptions.findIndex(function (item) {\n      return getOptionLabel(item).toLocaleLowerCase() === normalizedSearch;\n    });\n    if (exactMatch !== -1) return exactMatch;\n    return visibleOptions.findIndex(function (item) {\n      return getOptionLabel(item).toLocaleLowerCase().startsWith(normalizedSearch);\n    });\n  };\n  var onEditableInputChange = function onEditableInputChange(event) {\n    !overlayVisibleState && show();\n    var searchIndex = null;\n    if (event.target.value && visibleOptions) {\n      searchIndex = findInArray(visibleOptions, event.target.value);\n    }\n    setFocusedOptionIndex(searchIndex);\n    if (props.onChange) {\n      props.onChange({\n        originalEvent: event.originalEvent,\n        value: event.target.value,\n        stopPropagation: function stopPropagation() {\n          event.originalEvent.stopPropagation();\n        },\n        preventDefault: function preventDefault() {\n          event.originalEvent.preventDefault();\n        },\n        target: {\n          name: props.name,\n          id: props.id,\n          value: event.target.value\n        }\n      });\n    }\n  };\n  var onEditableInputFocus = function onEditableInputFocus(event) {\n    setFocusedState(true);\n    hide();\n    props.onFocus && props.onFocus(event);\n  };\n  var onOptionClick = function onOptionClick(event) {\n    var option = event.option;\n    if (!option.disabled) {\n      selectItem(event);\n      DomHandler.focus(focusInputRef.current);\n    }\n    hide();\n  };\n  var onFilterInputChange = function onFilterInputChange(event) {\n    var filter = event.target.value;\n    setFilterState(filter);\n    if (props.onFilter) {\n      props.onFilter({\n        originalEvent: event,\n        filter: filter\n      });\n    }\n  };\n  var onFilterClearIconClick = function onFilterClearIconClick(callback) {\n    resetFilter(callback);\n  };\n  var resetFilter = function resetFilter(callback) {\n    setFilterState('');\n    props.onFilter && props.onFilter({\n      filter: ''\n    });\n    callback && callback();\n  };\n  var clear = function clear(event) {\n    if (props.onChange) {\n      props.onChange({\n        originalEvent: event,\n        value: undefined,\n        stopPropagation: function stopPropagation() {\n          event === null || event === void 0 || event.stopPropagation();\n        },\n        preventDefault: function preventDefault() {\n          event === null || event === void 0 || event.preventDefault();\n        },\n        target: {\n          name: props.name,\n          id: props.id,\n          value: undefined\n        }\n      });\n    }\n    if (props.filter) {\n      resetFilter();\n    }\n    updateEditableLabel();\n    setFocusedOptionIndex(-1);\n  };\n  var selectItem = function selectItem(event) {\n    if (selectedOption !== event.option) {\n      updateEditableLabel(event.option);\n      setFocusedOptionIndex(-1);\n      var optionValue = getOptionValue(event.option);\n      var selectedOptionIndex = findOptionIndexInList(event.option, visibleOptions);\n      if (props.onChange) {\n        props.onChange({\n          originalEvent: event.originalEvent,\n          value: optionValue,\n          stopPropagation: function stopPropagation() {\n            event.originalEvent.stopPropagation();\n          },\n          preventDefault: function preventDefault() {\n            event.originalEvent.preventDefault();\n          },\n          target: {\n            name: props.name,\n            id: props.id,\n            value: optionValue\n          }\n        });\n      }\n      changeFocusedOptionIndex(event.originalEvent, selectedOptionIndex);\n    }\n  };\n  var getSelectedOptionIndex = function getSelectedOptionIndex(options) {\n    options = options || visibleOptions;\n    if (options) {\n      if (props.optionGroupLabel) {\n        for (var i = 0; i < options.length; i++) {\n          var selectedOptionIndex = findOptionIndexInList(props.value, getOptionGroupChildren(options[i]));\n          if (selectedOptionIndex !== -1) {\n            return {\n              group: i,\n              option: selectedOptionIndex\n            };\n          }\n        }\n      } else {\n        return findOptionIndexInList(props.value, options);\n      }\n    }\n    return -1;\n  };\n  var equalityKey = function equalityKey() {\n    return props.optionValue ? null : props.dataKey;\n  };\n  var findOptionIndexInList = function findOptionIndexInList(value, list) {\n    var key = equalityKey();\n    return list.findIndex(function (item) {\n      return ObjectUtils.equals(value, getOptionValue(item), key);\n    });\n  };\n  var isSelected = function isSelected(option) {\n    return ObjectUtils.equals(props.value, getOptionValue(option), equalityKey());\n  };\n  var show = function show() {\n    setFocusedOptionIndex(focusedOptionIndex !== -1 ? focusedOptionIndex : props.autoOptionFocus ? findFirstFocusedOptionIndex() : props.editable ? -1 : findSelectedOptionIndex());\n    setOverlayVisibleState(true);\n  };\n  var hide = function hide() {\n    setOverlayVisibleState(false);\n    clickedRef.current = false;\n  };\n  var onFocus = function onFocus() {\n    if (props.editable && !overlayVisibleState && clickedRef.current === false) {\n      DomHandler.focus(inputRef.current);\n    }\n  };\n  var onOverlayEnter = function onOverlayEnter(callback) {\n    ZIndexUtils.set('overlay', overlayRef.current, context && context.autoZIndex || PrimeReact.autoZIndex, context && context.zIndex.overlay || PrimeReact.zIndex.overlay);\n    DomHandler.addStyles(overlayRef.current, {\n      position: 'absolute',\n      top: '0',\n      left: '0'\n    });\n    alignOverlay();\n    callback && callback();\n  };\n  var onOverlayEntered = function onOverlayEntered(callback) {\n    callback && callback();\n    bindOverlayListener();\n    props.onShow && props.onShow();\n  };\n  var onOverlayExit = function onOverlayExit() {\n    unbindOverlayListener();\n  };\n  var onOverlayExited = function onOverlayExited() {\n    if (props.filter && props.resetFilterOnHide) {\n      resetFilter();\n    }\n    ZIndexUtils.clear(overlayRef.current);\n    props.onHide && props.onHide();\n  };\n  var alignOverlay = function alignOverlay() {\n    DomHandler.alignOverlay(overlayRef.current, inputRef.current.parentElement, props.appendTo || context && context.appendTo || PrimeReact.appendTo);\n  };\n  var scrollInView = function scrollInView() {\n    var focusedItem = DomHandler.findSingle(overlayRef.current, 'li[data-p-focused=\"true\"]');\n    if (focusedItem && focusedItem.scrollIntoView) {\n      focusedItem.scrollIntoView({\n        block: 'nearest',\n        inline: 'nearest'\n      });\n    } else {\n      var highlightItem = DomHandler.findSingle(overlayRef.current, 'li[data-p-highlight=\"true\"]');\n      if (highlightItem && highlightItem.scrollIntoView) {\n        highlightItem.scrollIntoView({\n          block: 'nearest',\n          inline: 'nearest'\n        });\n      }\n    }\n  };\n  var updateEditableLabel = function updateEditableLabel(option) {\n    if (inputRef.current) {\n      inputRef.current.value = option ? getOptionLabel(option) : props.value || '';\n\n      // #1413 NVDA screenreader\n      if (focusInputRef.current) {\n        focusInputRef.current.value = inputRef.current.value;\n      }\n    }\n  };\n  var getOptionLabel = function getOptionLabel(option) {\n    if (ObjectUtils.isScalar(option)) {\n      return \"\".concat(option);\n    }\n    var optionLabel = props.optionLabel ? ObjectUtils.resolveFieldData(option, props.optionLabel) : option['label'];\n    return \"\".concat(optionLabel);\n  };\n  var getOptionValue = function getOptionValue(option) {\n    if (props.useOptionAsValue) {\n      return option;\n    }\n    var optionValue = props.optionValue ? ObjectUtils.resolveFieldData(option, props.optionValue) : option ? option['value'] : ObjectUtils.resolveFieldData(option, 'value');\n    return props.optionValue || ObjectUtils.isNotEmpty(optionValue) ? optionValue : option;\n  };\n  var getOptionRenderKey = function getOptionRenderKey(option) {\n    return props.dataKey ? ObjectUtils.resolveFieldData(option, props.dataKey) : getOptionLabel(option);\n  };\n  var isOptionGroup = function isOptionGroup(option) {\n    return props.optionGroupLabel && option.group;\n  };\n  var isOptionDisabled = function isOptionDisabled(option) {\n    if (props.optionDisabled) {\n      return ObjectUtils.isFunction(props.optionDisabled) ? props.optionDisabled(option) : ObjectUtils.resolveFieldData(option, props.optionDisabled);\n    }\n    return option && option.disabled !== undefined ? option.disabled : false;\n  };\n  var getOptionGroupRenderKey = function getOptionGroupRenderKey(optionGroup) {\n    return ObjectUtils.resolveFieldData(optionGroup, props.optionGroupLabel);\n  };\n  var getOptionGroupLabel = function getOptionGroupLabel(optionGroup) {\n    return ObjectUtils.resolveFieldData(optionGroup, props.optionGroupLabel);\n  };\n  var getOptionGroupChildren = function getOptionGroupChildren(optionGroup) {\n    return ObjectUtils.resolveFieldData(optionGroup, props.optionGroupChildren);\n  };\n  var updateInputField = function updateInputField() {\n    if (props.editable && inputRef.current) {\n      var label = selectedOption ? getOptionLabel(selectedOption) : null;\n      var value = label || props.value || '';\n      inputRef.current.value = value;\n\n      // #1413 NVDA screenreader\n      if (focusInputRef.current) {\n        focusInputRef.current.value = value;\n      }\n    }\n  };\n  var getSelectedOption = function getSelectedOption() {\n    var index = getSelectedOptionIndex(props.options);\n    return index !== -1 ? props.optionGroupLabel ? getOptionGroupChildren(props.options[index.group])[index.option] : props.options[index] : null;\n  };\n  React.useImperativeHandle(ref, function () {\n    return {\n      props: props,\n      show: show,\n      hide: hide,\n      clear: clear,\n      focus: function focus() {\n        return DomHandler.focus(focusInputRef.current);\n      },\n      getElement: function getElement() {\n        return elementRef.current;\n      },\n      getOverlay: function getOverlay() {\n        return overlayRef.current;\n      },\n      getInput: function getInput() {\n        return inputRef.current;\n      },\n      getFocusInput: function getFocusInput() {\n        return focusInputRef.current;\n      },\n      getVirtualScroller: function getVirtualScroller() {\n        return virtualScrollerRef.current;\n      }\n    };\n  });\n  React.useEffect(function () {\n    ObjectUtils.combinedRefs(inputRef, props.inputRef);\n    ObjectUtils.combinedRefs(focusInputRef, props.focusInputRef);\n  }, [inputRef, props.inputRef, focusInputRef, props.focusInputRef]);\n  useMountEffect(function () {\n    if (props.autoFocus) {\n      DomHandler.focus(focusInputRef.current, props.autoFocus);\n    }\n    alignOverlay();\n  });\n  useUpdateEffect(function () {\n    if (overlayVisibleState && (props.value || focusedOptionIndex >= 0)) {\n      scrollInView();\n    }\n  }, [overlayVisibleState, props.value, focusedOptionIndex]);\n  useUpdateEffect(function () {\n    if (overlayVisibleState && filterState && props.filter) {\n      alignOverlay();\n    }\n  }, [overlayVisibleState, filterState, props.filter]);\n  useUpdateEffect(function () {\n    virtualScrollerRef.current && virtualScrollerRef.current.scrollInView(0);\n  }, [filterState]);\n  useUpdateEffect(function () {\n    updateInputField();\n    if (inputRef.current) {\n      inputRef.current.selectedIndex = 1;\n    }\n  });\n  useUnmountEffect(function () {\n    ZIndexUtils.clear(overlayRef.current);\n  });\n  var createHiddenSelect = function createHiddenSelect() {\n    var option = {\n      value: '',\n      label: props.placeholder\n    };\n    if (selectedOption) {\n      var optionValue = getOptionValue(selectedOption);\n      option = {\n        value: _typeof(optionValue) === 'object' ? props.options.findIndex(function (o) {\n          return o === optionValue;\n        }) : optionValue,\n        label: getOptionLabel(selectedOption)\n      };\n    }\n    var hiddenSelectedMessageProps = mergeProps({\n      className: 'p-hidden-accessible p-dropdown-hidden-select'\n    }, ptm('hiddenSelectedMessage'));\n    var selectProps = mergeProps({\n      ref: inputRef,\n      required: props.required,\n      defaultValue: option.value,\n      name: props.name,\n      tabIndex: -1\n    }, ptm('select'));\n    var optionProps = mergeProps({\n      value: option.value\n    }, ptm('option'));\n    return /*#__PURE__*/React.createElement(\"div\", hiddenSelectedMessageProps, /*#__PURE__*/React.createElement(\"select\", selectProps, /*#__PURE__*/React.createElement(\"option\", optionProps, option.label)));\n  };\n  var createKeyboardHelper = function createKeyboardHelper() {\n    var value = ObjectUtils.isNotEmpty(selectedOption) ? getOptionLabel(selectedOption) : null;\n    if (props.editable) {\n      value = value || props.value || '';\n    }\n    var hiddenSelectedMessageProps = mergeProps({\n      className: 'p-hidden-accessible'\n    }, ptm('hiddenSelectedMessage'));\n    var inputProps = mergeProps(_objectSpread({\n      ref: focusInputRef,\n      id: props.inputId,\n      defaultValue: value,\n      type: 'text',\n      readOnly: true,\n      'aria-haspopup': 'listbox',\n      onFocus: onInputFocus,\n      onBlur: onInputBlur,\n      onKeyDown: onInputKeyDown,\n      disabled: props.disabled,\n      tabIndex: !props.disabled ? props.tabIndex || 0 : -1\n    }, ariaProps), ptm('input'));\n    return /*#__PURE__*/React.createElement(\"div\", hiddenSelectedMessageProps, /*#__PURE__*/React.createElement(\"input\", inputProps));\n  };\n  var createLabel = function createLabel() {\n    var label = ObjectUtils.isNotEmpty(selectedOption) ? getOptionLabel(selectedOption) : null;\n    if (props.editable) {\n      var value = label || props.value || '';\n      var _inputProps = mergeProps(_objectSpread({\n        ref: inputRef,\n        type: 'text',\n        defaultValue: value,\n        className: cx('input', {\n          label: label\n        }),\n        disabled: props.disabled,\n        placeholder: props.placeholder,\n        maxLength: props.maxLength,\n        onInput: onEditableInputChange,\n        onFocus: onEditableInputFocus,\n        onKeyDown: onInputKeyDown,\n        onBlur: onInputBlur,\n        tabIndex: !props.disabled ? props.tabIndex || 0 : -1,\n        'aria-haspopup': 'listbox'\n      }, ariaProps), ptm('input'));\n      return /*#__PURE__*/React.createElement(\"input\", _inputProps);\n    }\n    var content = props.valueTemplate ? ObjectUtils.getJSXElement(props.valueTemplate, selectedOption, props) : label || props.placeholder || props.emptyMessage || /*#__PURE__*/React.createElement(React.Fragment, null, \"\\xA0\");\n    var inputProps = mergeProps({\n      ref: inputRef,\n      className: cx('input', {\n        label: label\n      }),\n      tabIndex: '-1'\n    }, ptm('input'));\n    return /*#__PURE__*/React.createElement(\"span\", inputProps, content);\n  };\n  var onClearIconKeyDown = function onClearIconKeyDown(event) {\n    if (event.key === 'Enter' || event.code === 'Space') {\n      clear(event);\n      event.preventDefault();\n    }\n  };\n  var createClearIcon = function createClearIcon() {\n    if (props.value != null && props.showClear && !props.disabled && !ObjectUtils.isEmpty(props.options)) {\n      var clearIconProps = mergeProps({\n        className: cx('clearIcon'),\n        onPointerUp: clear,\n        tabIndex: props.editable ? -1 : props.tabIndex || '0',\n        onKeyDown: onClearIconKeyDown,\n        'aria-label': localeOption('clear')\n      }, ptm('clearIcon'));\n      var icon = props.clearIcon || /*#__PURE__*/React.createElement(TimesIcon, clearIconProps);\n      return IconUtils.getJSXIcon(icon, _objectSpread({}, clearIconProps), {\n        props: props\n      });\n    }\n    return null;\n  };\n  var createLoadingIcon = function createLoadingIcon() {\n    var loadingIconProps = mergeProps({\n      className: cx('loadingIcon'),\n      'data-pr-overlay-visible': overlayVisibleState\n    }, ptm('loadingIcon'));\n    var icon = props.loadingIcon || /*#__PURE__*/React.createElement(SpinnerIcon, {\n      spin: true\n    });\n    var loadingIcon = IconUtils.getJSXIcon(icon, _objectSpread({}, loadingIconProps), {\n      props: props\n    });\n    var ariaLabel = props.placeholder || props.ariaLabel;\n    var loadingButtonProps = mergeProps({\n      className: cx('trigger'),\n      role: 'button',\n      'aria-haspopup': 'listbox',\n      'aria-expanded': overlayVisibleState,\n      'aria-label': ariaLabel\n    }, ptm('trigger'));\n    return /*#__PURE__*/React.createElement(\"div\", loadingButtonProps, loadingIcon);\n  };\n  var createDropdownIcon = function createDropdownIcon() {\n    var dropdownIconProps = mergeProps({\n      className: cx('dropdownIcon'),\n      'data-pr-overlay-visible': overlayVisibleState\n    }, ptm('dropdownIcon'));\n    var icon = !overlayVisibleState ? props.dropdownIcon || /*#__PURE__*/React.createElement(ChevronDownIcon, dropdownIconProps) : props.collapseIcon || /*#__PURE__*/React.createElement(ChevronUpIcon, dropdownIconProps);\n    var dropdownIcon = IconUtils.getJSXIcon(icon, _objectSpread({}, dropdownIconProps), {\n      props: props\n    });\n    var ariaLabel = props.placeholder || props.ariaLabel;\n    var triggerProps = mergeProps({\n      className: cx('trigger'),\n      role: 'button',\n      'aria-haspopup': 'listbox',\n      'aria-expanded': overlayVisibleState,\n      'aria-label': ariaLabel\n    }, ptm('trigger'));\n    return /*#__PURE__*/React.createElement(\"div\", triggerProps, dropdownIcon);\n  };\n  var visibleOptions = getVisibleOptions();\n  var selectedOption = getSelectedOption();\n  var hasTooltip = ObjectUtils.isNotEmpty(props.tooltip);\n  var otherProps = DropdownBase.getOtherProps(props);\n  var ariaProps = ObjectUtils.reduceKeys(otherProps, DomHandler.ARIA_PROPS);\n  var hiddenSelect = createHiddenSelect();\n  var keyboardHelper = createKeyboardHelper();\n  var labelElement = createLabel();\n  var dropdownIcon = props.loading ? createLoadingIcon() : createDropdownIcon();\n  var clearIcon = createClearIcon();\n  var rootProps = mergeProps({\n    id: props.id,\n    ref: elementRef,\n    className: classNames(props.className, cx('root', {\n      context: context,\n      focusedState: focusedState,\n      overlayVisibleState: overlayVisibleState\n    })),\n    style: props.style,\n    onClick: function onClick(e) {\n      return _onClick(e);\n    },\n    onMouseDown: props.onMouseDown,\n    onContextMenu: props.onContextMenu,\n    onFocus: onFocus,\n    'data-p-disabled': props.disabled,\n    'data-p-focus': focusedState,\n    'aria-activedescendant': focusedState ? \"dropdownItem_\".concat(focusedOptionIndex) : undefined\n  }, otherProps, ptm('root'));\n  var firstHiddenFocusableElementProps = mergeProps({\n    ref: firstHiddenFocusableElementOnOverlay,\n    role: 'presentation',\n    className: 'p-hidden-accessible p-hidden-focusable',\n    tabIndex: '0',\n    onFocus: onFirstHiddenFocus,\n    'data-p-hidden-accessible': true,\n    'data-p-hidden-focusable': true\n  }, ptm('hiddenFirstFocusableEl'));\n  var lastHiddenFocusableElementProps = mergeProps({\n    ref: lastHiddenFocusableElementOnOverlay,\n    role: 'presentation',\n    className: 'p-hidden-accessible p-hidden-focusable',\n    tabIndex: '0',\n    onFocus: onLastHiddenFocus,\n    'data-p-hidden-accessible': true,\n    'data-p-hidden-focusable': true\n  }, ptm('hiddenLastFocusableEl'));\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"div\", rootProps, keyboardHelper, hiddenSelect, labelElement, clearIcon, dropdownIcon, /*#__PURE__*/React.createElement(DropdownPanel, _extends({\n    hostName: \"Dropdown\",\n    ref: overlayRef,\n    visibleOptions: visibleOptions,\n    virtualScrollerRef: virtualScrollerRef\n  }, props, {\n    appendTo: appendTo,\n    cx: cx,\n    filterValue: filterValue,\n    focusedOptionIndex: focusedOptionIndex,\n    getOptionGroupChildren: getOptionGroupChildren,\n    getOptionGroupLabel: getOptionGroupLabel,\n    getOptionGroupRenderKey: getOptionGroupRenderKey,\n    getOptionLabel: getOptionLabel,\n    getOptionRenderKey: getOptionRenderKey,\n    getSelectedOptionIndex: getSelectedOptionIndex,\n    hasFilter: hasFilter,\n    \"in\": overlayVisibleState,\n    isOptionDisabled: isOptionDisabled,\n    isSelected: isSelected,\n    onOverlayHide: hide,\n    onClick: onPanelClick,\n    onEnter: onOverlayEnter,\n    onEntered: onOverlayEntered,\n    onExit: onOverlayExit,\n    onExited: onOverlayExited,\n    onFilterClearIconClick: onFilterClearIconClick,\n    onFilterInputChange: onFilterInputChange,\n    onFilterInputKeyDown: onFilterInputKeyDown,\n    onOptionClick: onOptionClick,\n    onInputKeyDown: onInputKeyDown,\n    ptm: ptm,\n    resetFilter: resetFilter,\n    changeFocusedOptionIndex: changeFocusedOptionIndex,\n    firstFocusableElement: /*#__PURE__*/React.createElement(\"span\", firstHiddenFocusableElementProps),\n    lastFocusableElement: /*#__PURE__*/React.createElement(\"span\", lastHiddenFocusableElementProps),\n    sx: sx\n  }))), hasTooltip && /*#__PURE__*/React.createElement(Tooltip, _extends({\n    target: elementRef,\n    content: props.tooltip,\n    pt: ptm('tooltip')\n  }, props.tooltipOptions)));\n}));\nDropdown.displayName = 'Dropdown';\nexport { Dropdown };", "map": {"version": 3, "names": ["React", "PrimeReact", "PrimeReactContext", "aria<PERSON><PERSON><PERSON>", "localeOption", "FilterService", "ComponentBase", "useHandleStyle", "useMergeProps", "useDebounce", "useOverlayListener", "useMountEffect", "useUpdateEffect", "useUnmountEffect", "ChevronDownIcon", "ChevronUpIcon", "SpinnerIcon", "TimesIcon", "OverlayService", "<PERSON><PERSON><PERSON>", "classNames", "ObjectUtils", "<PERSON><PERSON><PERSON><PERSON>", "IconUtils", "ZIndexUtils", "CSSTransition", "SearchIcon", "Portal", "VirtualScroller", "<PERSON><PERSON><PERSON>", "CheckIcon", "IconBase", "_extends", "Object", "assign", "bind", "n", "e", "arguments", "length", "t", "r", "hasOwnProperty", "call", "apply", "_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "toPrimitive", "i", "TypeError", "String", "Number", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_defineProperty", "defineProperty", "value", "enumerable", "configurable", "writable", "_arrayWithHoles", "Array", "isArray", "_iterableToArrayLimit", "l", "u", "a", "f", "next", "done", "push", "_arrayLikeToArray$1", "_unsupportedIterableToArray$1", "toString", "slice", "name", "from", "test", "_nonIterableRest", "_slicedToArray", "ownKeys$2", "keys", "getOwnPropertySymbols", "filter", "getOwnPropertyDescriptor", "_objectSpread$2", "for<PERSON>ach", "getOwnPropertyDescriptors", "defineProperties", "classes", "root", "_ref", "props", "focusedState", "overlayVisibleState", "context", "disabled", "invalid", "variant", "inputStyle", "showClear", "isNotEmpty", "input", "_ref2", "label", "editable", "placeholder", "trigger", "emptyMessage", "itemGroup", "_ref3", "optionGroupLabel", "itemGroupLabel", "dropdownIcon", "loadingIcon", "clearIcon", "filterIcon", "filterClearIcon", "filterContainer", "_ref4", "filterInput", "_ref5", "list", "_ref6", "virtualScrollerOptions", "panel", "_ref7", "ripple", "item", "_ref8", "selected", "index", "focusedOptionIndex", "highlightOnSelect", "itemLabel", "checkIcon", "blankIcon", "wrapper", "header", "footer", "transition", "styles", "inlineStyles", "_ref9", "maxHeight", "scrollHeight", "_ref10", "panelStyle", "DropdownBase", "extend", "defaultProps", "__TYPE", "__parentMetadata", "appendTo", "ariaLabelledBy", "autoFocus", "autoOptionFocus", "checkmark", "children", "undefined", "className", "collapseIcon", "dataKey", "emptyFilterMessage", "filterBy", "filterDelay", "filterInputAutoFocus", "filterLocale", "filterMatchMode", "filterPlaceholder", "filterTemplate", "focusInputRef", "focusOnHover", "id", "inputId", "inputRef", "itemTemplate", "loading", "max<PERSON><PERSON><PERSON>", "onBlur", "onChange", "onClick", "onContextMenu", "onFilter", "onFocus", "onHide", "onMouseDown", "onShow", "optionDisabled", "optionGroupChildren", "optionGroupTemplate", "optionLabel", "options", "optionValue", "panelClassName", "panelFooterTemplate", "required", "resetFilterOnHide", "selectOnFocus", "showFilterClear", "showOnFocus", "style", "tabIndex", "tooltip", "tooltipOptions", "transitionOptions", "useOptionAsValue", "valueTemplate", "css", "BlankIcon", "memo", "forwardRef", "inProps", "ref", "pti", "getPTI", "createElement", "width", "height", "viewBox", "fill", "xmlns", "fillOpacity", "displayName", "DropdownItem", "mergeProps", "ptm", "cx", "option", "ariaSetSize", "onInputKeyDown", "getPTOptions", "key", "focused", "_onClick", "event", "originalEvent", "content", "template", "getJSXElement", "itemProps", "concat", "role", "onKeyDown", "onMouseMove", "itemGroupLabelProps", "<PERSON><PERSON><PERSON><PERSON>", "checkIconProps", "blankIconProps", "ownKeys$1", "_objectSpread$1", "DropdownPanel", "sx", "useContext", "filterInputRef", "useRef", "isEmptyFilter", "visibleOptions", "<PERSON><PERSON><PERSON>er", "filterOptions", "onFilterInputChange", "reset", "resetFilter", "hostName", "onEnter", "virtualScrollerRef", "current", "selectedIndex", "getSelectedOptionIndex", "setTimeout", "scrollToIndex", "onEntered", "focus", "createFooter", "onOverlayHide", "footerProps", "changeFocusedItemOnHover", "_props$changeFocusedO", "changeFocusedOptionIndex", "createEmptyMessage", "isFilter", "message", "emptyMessageProps", "createItem", "scrollerOptions", "itemSize", "group", "groupContent", "getOptionGroupLabel", "getOptionGroupRenderKey", "itemGroupProps", "optionKey", "getOptionRenderKey", "getOptionLabel", "isOptionDisabled", "isSelected", "onOptionClick", "createItems", "map", "createFilterClearIcon", "filterValue", "ariaLabelFilterClear", "clearIconProps", "onFilterClearIconClick", "icon", "getJSXIcon", "createFilter", "filterIconProps", "filterContainerProps", "filterInputProps", "type", "autoComplete", "onFilterInputKeyDown", "defaultContentOptions", "element", "filterInputKeyDown", "filterInputChange", "filterIconClassName", "headerProps", "createContent", "virtualScrollerProps", "items", "autoSize", "onLazyLoad", "contentTemplate", "listProps", "contentRef", "pt", "wrapperProps", "panelProps", "transitionProps", "timeout", "enter", "exit", "unmountOnExit", "onExit", "onExited", "nodeRef", "firstFocusableElement", "lastFocusableElement", "_createForOfIteratorHelper", "_unsupportedIterableToArray", "_n", "F", "s", "_arrayLikeToArray", "ownKeys", "_objectSpread", "Dropdown", "getProps", "_useDebounce", "_useDebounce2", "filterState", "setFilterState", "_React$useState", "useState", "_React$useState2", "setFocusedState", "_React$useState3", "_React$useState4", "setFocusedOptionIndex", "_React$useState5", "_React$useState6", "setOverlayVisibleState", "clickedRef", "elementRef", "overlayRef", "firstHiddenFocusableElementOnOverlay", "lastHiddenFocusableElementOnOverlay", "searchTimeout", "searchValue", "isLazy", "lazy", "_DropdownBase$setMeta", "setMetaData", "state", "overlayVisible", "isUnstyled", "_useOverlayListener", "target", "overlay", "listener", "valid", "isClearClicked", "hide", "hideOverlaysOnDocumentScrolling", "isDocument", "alignOverlay", "when", "_useOverlayListener2", "bindOverlayListener", "unbindOverlayListener", "flatOptions", "reduce", "result", "getOptionGroupChildren", "getVisibleOptions", "_filterValue", "trim", "toLocaleLowerCase", "searchFields", "split", "filteredGroups", "_iterator", "_step", "optgroup", "filteredSubOptions", "err", "onFirstHiddenFocus", "focusableEl", "relatedTarget", "getFirstFocusableElement", "onLastHiddenFocus", "getLastFocusableElement", "isAttributeEquals", "parentElement", "defaultPrevented", "tagName", "contains", "show", "preventDefault", "onInputFocus", "onInputBlur", "currentValue", "stopPropagation", "onOptionSelect", "isHide", "selectItem", "onPanelClick", "emit", "code", "isAndroid", "onArrowDownKey", "onArrowUpKey", "onArrowLeftKey", "onHomeKey", "onEndKey", "onPageDownKey", "onPageUpKey", "onSpaceKey", "onEnterKey", "onEscapeKey", "onTabKey", "onBackspaceKey", "metaKey", "ctrl<PERSON>ey", "altKey", "isPrintableCharacter", "searchOptions", "hasFocusableElements", "getFocusableElements", "isOptionMatched", "_getOptionLabel", "isValidOption", "startsWith", "isOptionGroup", "hasSelectedOption", "isValidSelectedOption", "findSelectedOptionIndex", "findIndex", "findFirstFocusedOptionIndex", "findFirstOptionIndex", "_char", "optionIndex", "matched", "clearTimeout", "findLastFocusedOptionIndex", "findLastOptionIndex", "findLastIndex", "findNextOptionIndex", "matchedOptionIndex", "findPrevOptionIndex", "focusOnItem", "focusedItem", "findSingle", "pressedInInputText", "currentTarget", "setSelectionRange", "len", "focusedOption", "getOptionValue", "updateEditableLabel", "selectedOption", "findInArray", "searchText", "normalizedSearch", "exactMatch", "onEditableInputChange", "searchIndex", "onEditableInputFocus", "callback", "clear", "selectedOptionIndex", "findOptionIndexInList", "equalityKey", "equals", "onOverlayEnter", "set", "autoZIndex", "zIndex", "addStyles", "position", "top", "left", "onOverlayEntered", "onOverlayExit", "onOverlayExited", "scrollInView", "scrollIntoView", "block", "inline", "highlightItem", "isScalar", "resolveFieldData", "isFunction", "optionGroup", "updateInputField", "getSelectedOption", "useImperativeHandle", "getElement", "getOverlay", "getInput", "getFocusInput", "getVirtualScroller", "useEffect", "combinedRefs", "createHiddenSelect", "hiddenSelectedMessageProps", "selectProps", "defaultValue", "optionProps", "createKeyboardHelper", "inputProps", "readOnly", "ariaProps", "createLabel", "_inputProps", "onInput", "Fragment", "onClearIconKeyDown", "createClearIcon", "isEmpty", "onPointerUp", "createLoadingIcon", "loadingIconProps", "spin", "loadingButtonProps", "createDropdownIcon", "dropdownIconProps", "triggerProps", "hasTooltip", "otherProps", "getOtherProps", "reduceKeys", "ARIA_PROPS", "hiddenSelect", "keyboard<PERSON>elper", "labelElement", "rootProps", "firstHiddenFocusableElementProps", "lastHiddenFocusableElementProps"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/node_modules/primereact/dropdown/dropdown.esm.js"], "sourcesContent": ["'use client';\nimport * as React from 'react';\nimport PrimeReact, { PrimeReactContext, ariaLabel, localeOption, FilterService } from 'primereact/api';\nimport { ComponentBase, useHandleStyle } from 'primereact/componentbase';\nimport { useMergeProps, useDebounce, useOverlayListener, useMountEffect, useUpdateEffect, useUnmountEffect } from 'primereact/hooks';\nimport { ChevronDownIcon } from 'primereact/icons/chevrondown';\nimport { ChevronUpIcon } from 'primereact/icons/chevronup';\nimport { SpinnerIcon } from 'primereact/icons/spinner';\nimport { TimesIcon } from 'primereact/icons/times';\nimport { OverlayService } from 'primereact/overlayservice';\nimport { Tooltip } from 'primereact/tooltip';\nimport { classNames, ObjectUtils, DomHandler, IconUtils, ZIndexUtils } from 'primereact/utils';\nimport { CSSTransition } from 'primereact/csstransition';\nimport { SearchIcon } from 'primereact/icons/search';\nimport { Portal } from 'primereact/portal';\nimport { VirtualScroller } from 'primereact/virtualscroller';\nimport { Ripple } from 'primereact/ripple';\nimport { CheckIcon } from 'primereact/icons/check';\nimport { IconBase } from 'primereact/iconbase';\n\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\n\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\n\nfunction toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\n\nfunction toPropertyKey(t) {\n  var i = toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\n\nfunction _defineProperty(e, r, t) {\n  return (r = toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\n\nfunction _arrayWithHoles(r) {\n  if (Array.isArray(r)) return r;\n}\n\nfunction _iterableToArrayLimit(r, l) {\n  var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"];\n  if (null != t) {\n    var e,\n      n,\n      i,\n      u,\n      a = [],\n      f = !0,\n      o = !1;\n    try {\n      if (i = (t = t.call(r)).next, 0 === l) {\n        if (Object(t) !== t) return;\n        f = !1;\n      } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0);\n    } catch (r) {\n      o = !0, n = r;\n    } finally {\n      try {\n        if (!f && null != t[\"return\"] && (u = t[\"return\"](), Object(u) !== u)) return;\n      } finally {\n        if (o) throw n;\n      }\n    }\n    return a;\n  }\n}\n\nfunction _arrayLikeToArray$1(r, a) {\n  (null == a || a > r.length) && (a = r.length);\n  for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e];\n  return n;\n}\n\nfunction _unsupportedIterableToArray$1(r, a) {\n  if (r) {\n    if (\"string\" == typeof r) return _arrayLikeToArray$1(r, a);\n    var t = {}.toString.call(r).slice(8, -1);\n    return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray$1(r, a) : void 0;\n  }\n}\n\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nfunction _slicedToArray(r, e) {\n  return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray$1(r, e) || _nonIterableRest();\n}\n\nfunction ownKeys$2(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread$2(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys$2(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$2(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nvar classes = {\n  root: function root(_ref) {\n    var props = _ref.props,\n      focusedState = _ref.focusedState,\n      overlayVisibleState = _ref.overlayVisibleState,\n      context = _ref.context;\n    return classNames('p-dropdown p-component p-inputwrapper', {\n      'p-disabled': props.disabled,\n      'p-invalid': props.invalid,\n      'p-focus': focusedState,\n      'p-variant-filled': props.variant ? props.variant === 'filled' : context && context.inputStyle === 'filled',\n      'p-dropdown-clearable': props.showClear && !props.disabled,\n      'p-inputwrapper-filled': ObjectUtils.isNotEmpty(props.value),\n      'p-inputwrapper-focus': focusedState || overlayVisibleState\n    });\n  },\n  input: function input(_ref2) {\n    var props = _ref2.props,\n      label = _ref2.label;\n    return props.editable ? 'p-dropdown-label p-inputtext' : classNames('p-dropdown-label p-inputtext', {\n      'p-placeholder': label === null && props.placeholder,\n      'p-dropdown-label-empty': label === null && !props.placeholder\n    });\n  },\n  trigger: 'p-dropdown-trigger',\n  emptyMessage: 'p-dropdown-empty-message',\n  itemGroup: function itemGroup(_ref3) {\n    var optionGroupLabel = _ref3.optionGroupLabel;\n    return classNames('p-dropdown-item-group', {\n      'p-dropdown-item-empty': !optionGroupLabel || optionGroupLabel.length === 0\n    });\n  },\n  itemGroupLabel: 'p-dropdown-item-group-label',\n  dropdownIcon: 'p-dropdown-trigger-icon p-clickable',\n  loadingIcon: 'p-dropdown-trigger-icon p-clickable',\n  clearIcon: 'p-dropdown-clear-icon p-clickable',\n  filterIcon: 'p-dropdown-filter-icon',\n  filterClearIcon: 'p-dropdown-filter-clear-icon',\n  filterContainer: function filterContainer(_ref4) {\n    var clearIcon = _ref4.clearIcon;\n    return classNames('p-dropdown-filter-container', {\n      'p-dropdown-clearable-filter': !!clearIcon\n    });\n  },\n  filterInput: function filterInput(_ref5) {\n    var props = _ref5.props,\n      context = _ref5.context;\n    return classNames('p-dropdown-filter p-inputtext p-component', {\n      'p-variant-filled': props.variant ? props.variant === 'filled' : context && context.inputStyle === 'filled'\n    });\n  },\n  list: function list(_ref6) {\n    var virtualScrollerOptions = _ref6.virtualScrollerOptions;\n    return virtualScrollerOptions ? 'p-dropdown-items' : 'p-dropdown-items';\n  },\n  panel: function panel(_ref7) {\n    var context = _ref7.context;\n    return classNames('p-dropdown-panel p-component', {\n      'p-input-filled': context && context.inputStyle === 'filled' || PrimeReact.inputStyle === 'filled',\n      'p-ripple-disabled': context && context.ripple === false || PrimeReact.ripple === false\n    });\n  },\n  item: function item(_ref8) {\n    var selected = _ref8.selected,\n      disabled = _ref8.disabled,\n      label = _ref8.label,\n      index = _ref8.index,\n      focusedOptionIndex = _ref8.focusedOptionIndex,\n      highlightOnSelect = _ref8.highlightOnSelect;\n    return classNames('p-dropdown-item', {\n      'p-highlight': selected && highlightOnSelect,\n      'p-disabled': disabled,\n      'p-focus': index === focusedOptionIndex,\n      'p-dropdown-item-empty': !label || label.length === 0\n    });\n  },\n  itemLabel: 'p-dropdown-item-label',\n  checkIcon: 'p-dropdown-check-icon',\n  blankIcon: 'p-dropdown-blank-icon',\n  wrapper: 'p-dropdown-items-wrapper',\n  header: 'p-dropdown-header',\n  footer: 'p-dropdown-footer',\n  transition: 'p-connected-overlay'\n};\nvar styles = \"\\n@layer primereact {\\n    .p-dropdown {\\n        display: inline-flex;\\n        cursor: pointer;\\n        position: relative;\\n        user-select: none;\\n    }\\n    \\n    .p-dropdown-trigger {\\n        display: flex;\\n        align-items: center;\\n        justify-content: center;\\n        flex-shrink: 0;\\n    }\\n    \\n    .p-dropdown-label {\\n        display: block;\\n        white-space: nowrap;\\n        overflow: hidden;\\n        flex: 1 1 auto;\\n        width: 1%;\\n        text-overflow: ellipsis;\\n        cursor: pointer;\\n    }\\n    \\n    .p-dropdown-label-empty {\\n        overflow: hidden;\\n        visibility: hidden;\\n    }\\n    \\n    input.p-dropdown-label  {\\n        cursor: default;\\n    }\\n    \\n    .p-dropdown .p-dropdown-panel {\\n        min-width: 100%;\\n    }\\n    \\n    .p-dropdown-panel {\\n        position: absolute;\\n        top: 0;\\n        left: 0;\\n    }\\n    \\n    .p-dropdown-items-wrapper {\\n        overflow: auto;\\n    }\\n    \\n    .p-dropdown-item {\\n        cursor: pointer;\\n        font-weight: normal;\\n        white-space: nowrap;\\n        position: relative;\\n        overflow: hidden;\\n    }\\n    \\n    .p-dropdown-items {\\n        margin: 0;\\n        padding: 0;\\n        list-style-type: none;\\n    }\\n    \\n    .p-dropdown-filter {\\n        width: 100%;\\n    }\\n    \\n    .p-dropdown-filter-container {\\n        position: relative;\\n    }\\n    \\n    .p-dropdown-clear-icon,\\n    .p-dropdown-filter-icon,\\n    .p-dropdown-filter-clear-icon {\\n        position: absolute;\\n        top: 50%;\\n        margin-top: -.5rem;\\n        right: 2rem;\\n    }\\n    \\n    .p-fluid .p-dropdown {\\n        display: flex;\\n    }\\n    \\n    .p-fluid .p-dropdown .p-dropdown-label {\\n        width: 1%;\\n    }\\n}\\n\";\nvar inlineStyles = {\n  wrapper: function wrapper(_ref9) {\n    var props = _ref9.props;\n    return {\n      maxHeight: props.scrollHeight || 'auto'\n    };\n  },\n  panel: function panel(_ref10) {\n    var props = _ref10.props;\n    return _objectSpread$2({}, props.panelStyle);\n  }\n};\nvar DropdownBase = ComponentBase.extend({\n  defaultProps: {\n    __TYPE: 'Dropdown',\n    __parentMetadata: null,\n    appendTo: null,\n    ariaLabel: null,\n    ariaLabelledBy: null,\n    autoFocus: false,\n    autoOptionFocus: false,\n    checkmark: false,\n    children: undefined,\n    className: null,\n    clearIcon: null,\n    collapseIcon: null,\n    dataKey: null,\n    disabled: false,\n    dropdownIcon: null,\n    editable: false,\n    emptyFilterMessage: null,\n    emptyMessage: null,\n    filter: false,\n    filterBy: null,\n    filterClearIcon: null,\n    filterDelay: 300,\n    filterIcon: null,\n    filterInputAutoFocus: false,\n    filterLocale: undefined,\n    filterMatchMode: 'contains',\n    filterPlaceholder: null,\n    filterTemplate: null,\n    focusInputRef: null,\n    focusOnHover: true,\n    highlightOnSelect: true,\n    id: null,\n    inputId: null,\n    inputRef: null,\n    invalid: false,\n    itemTemplate: null,\n    loading: false,\n    loadingIcon: null,\n    maxLength: null,\n    name: null,\n    onBlur: null,\n    onChange: null,\n    onClick: null,\n    onContextMenu: null,\n    onFilter: null,\n    onFocus: null,\n    onHide: null,\n    onMouseDown: null,\n    onShow: null,\n    optionDisabled: null,\n    optionGroupChildren: 'items',\n    optionGroupLabel: null,\n    optionGroupTemplate: null,\n    optionLabel: null,\n    options: null,\n    optionValue: null,\n    panelClassName: null,\n    panelFooterTemplate: null,\n    panelStyle: null,\n    placeholder: null,\n    required: false,\n    resetFilterOnHide: false,\n    scrollHeight: '200px',\n    selectOnFocus: false,\n    showClear: false,\n    showFilterClear: false,\n    showOnFocus: false,\n    style: null,\n    tabIndex: null,\n    tooltip: null,\n    tooltipOptions: null,\n    transitionOptions: null,\n    useOptionAsValue: false,\n    value: null,\n    valueTemplate: null,\n    variant: null,\n    virtualScrollerOptions: null\n  },\n  css: {\n    classes: classes,\n    styles: styles,\n    inlineStyles: inlineStyles\n  }\n});\n\nvar BlankIcon = /*#__PURE__*/React.memo(/*#__PURE__*/React.forwardRef(function (inProps, ref) {\n  var pti = IconBase.getPTI(inProps);\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({\n    ref: ref,\n    width: \"14\",\n    height: \"14\",\n    viewBox: \"0 0 14 14\",\n    fill: \"none\",\n    xmlns: \"http://www.w3.org/2000/svg\"\n  }, pti), /*#__PURE__*/React.createElement(\"rect\", {\n    width: \"1\",\n    height: \"1\",\n    fill: \"currentColor\",\n    fillOpacity: \"0\"\n  }));\n}));\nBlankIcon.displayName = 'BlankIcon';\n\nvar DropdownItem = /*#__PURE__*/React.memo(function (props) {\n  var mergeProps = useMergeProps();\n  var ptm = props.ptm,\n    cx = props.cx,\n    selected = props.selected,\n    disabled = props.disabled,\n    option = props.option,\n    label = props.label,\n    index = props.index,\n    focusedOptionIndex = props.focusedOptionIndex,\n    ariaSetSize = props.ariaSetSize,\n    checkmark = props.checkmark,\n    highlightOnSelect = props.highlightOnSelect,\n    onInputKeyDown = props.onInputKeyDown;\n  var getPTOptions = function getPTOptions(key) {\n    return ptm(key, {\n      context: {\n        selected: selected,\n        disabled: disabled,\n        focused: index === focusedOptionIndex\n      }\n    });\n  };\n  var _onClick = function onClick(event, i) {\n    if (props.onClick) {\n      props.onClick({\n        originalEvent: event,\n        option: option\n      });\n    }\n  };\n  var content = props.template ? ObjectUtils.getJSXElement(props.template, props.option) : props.label;\n  var itemProps = mergeProps({\n    id: \"dropdownItem_\".concat(index),\n    role: 'option',\n    className: classNames(option.className, cx('item', {\n      selected: selected,\n      disabled: disabled,\n      label: label,\n      index: index,\n      focusedOptionIndex: focusedOptionIndex,\n      highlightOnSelect: highlightOnSelect\n    })),\n    style: props.style,\n    tabIndex: 0,\n    onClick: function onClick(e) {\n      return _onClick(e);\n    },\n    onKeyDown: function onKeyDown(e) {\n      return onInputKeyDown(e);\n    },\n    onMouseMove: function onMouseMove(e) {\n      return props === null || props === void 0 ? void 0 : props.onMouseMove(e, index);\n    },\n    'aria-setsize': ariaSetSize,\n    'aria-posinset': index + 1,\n    'aria-label': label,\n    'aria-selected': selected,\n    'data-p-highlight': selected,\n    'data-p-focused': focusedOptionIndex === index,\n    'data-p-disabled': disabled\n  }, getPTOptions('item'));\n  var itemGroupLabelProps = mergeProps({\n    className: cx('itemLabel')\n  }, getPTOptions('itemLabel'));\n  var iconRenderer = function iconRenderer() {\n    if (selected) {\n      var checkIconProps = mergeProps({\n        className: cx('checkIcon')\n      }, getPTOptions('checkIcon'));\n      return /*#__PURE__*/React.createElement(CheckIcon, checkIconProps);\n    }\n    var blankIconProps = mergeProps({\n      className: cx('blankIcon')\n    }, getPTOptions('blankIcon'));\n    return /*#__PURE__*/React.createElement(BlankIcon, blankIconProps);\n  };\n  return /*#__PURE__*/React.createElement(\"li\", _extends({\n    key: props.label\n  }, itemProps), checkmark && iconRenderer(), /*#__PURE__*/React.createElement(\"span\", itemGroupLabelProps, content), /*#__PURE__*/React.createElement(Ripple, null));\n});\nDropdownItem.displayName = 'DropdownItem';\n\nfunction ownKeys$1(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread$1(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys$1(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$1(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nvar DropdownPanel = /*#__PURE__*/React.memo(/*#__PURE__*/React.forwardRef(function (props, ref) {\n  var mergeProps = useMergeProps();\n  var ptm = props.ptm,\n    cx = props.cx,\n    sx = props.sx;\n  var context = React.useContext(PrimeReactContext);\n  var filterInputRef = React.useRef(null);\n  var isEmptyFilter = !(props.visibleOptions && props.visibleOptions.length) && props.hasFilter;\n  var ariaSetSize = props.visibleOptions ? props.visibleOptions.length : 0;\n  var filterOptions = {\n    filter: function filter(e) {\n      return onFilterInputChange(e);\n    },\n    reset: function reset() {\n      return props.resetFilter();\n    }\n  };\n  var getPTOptions = function getPTOptions(key, options) {\n    return ptm(key, _objectSpread$1({\n      hostName: props.hostName\n    }, options));\n  };\n  var onEnter = function onEnter() {\n    props.onEnter(function () {\n      if (props.virtualScrollerRef.current) {\n        var selectedIndex = props.getSelectedOptionIndex();\n        if (selectedIndex !== -1) {\n          setTimeout(function () {\n            return props.virtualScrollerRef.current.scrollToIndex(selectedIndex);\n          }, 0);\n        }\n      }\n    });\n  };\n  var onEntered = function onEntered() {\n    props.onEntered(function () {\n      if (props.filter && props.filterInputAutoFocus) {\n        DomHandler.focus(filterInputRef.current, false);\n      }\n    });\n  };\n  var onFilterInputChange = function onFilterInputChange(event) {\n    props.onFilterInputChange && props.onFilterInputChange(event);\n  };\n  var createFooter = function createFooter() {\n    if (props.panelFooterTemplate) {\n      var content = ObjectUtils.getJSXElement(props.panelFooterTemplate, props, props.onOverlayHide);\n      var footerProps = mergeProps({\n        className: cx('footer')\n      }, getPTOptions('footer'));\n      return /*#__PURE__*/React.createElement(\"div\", footerProps, content);\n    }\n    return null;\n  };\n  var changeFocusedItemOnHover = function changeFocusedItemOnHover(event, index) {\n    if (props.focusOnHover) {\n      var _props$changeFocusedO;\n      props === null || props === void 0 || (_props$changeFocusedO = props.changeFocusedOptionIndex) === null || _props$changeFocusedO === void 0 || _props$changeFocusedO.call(props, event, index);\n    }\n  };\n  var createEmptyMessage = function createEmptyMessage(emptyMessage, isFilter) {\n    var message = ObjectUtils.getJSXElement(emptyMessage, props) || localeOption(isFilter ? 'emptyFilterMessage' : 'emptyMessage');\n    var emptyMessageProps = mergeProps({\n      className: cx('emptyMessage')\n    }, getPTOptions('emptyMessage'));\n    return /*#__PURE__*/React.createElement(\"li\", emptyMessageProps, message);\n  };\n  var createItem = function createItem(option, index) {\n    var scrollerOptions = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    var style = {\n      height: scrollerOptions.props ? scrollerOptions.props.itemSize : undefined\n    };\n    style = _objectSpread$1(_objectSpread$1({}, style), option.style);\n    if (option.group && props.optionGroupLabel) {\n      var optionGroupLabel = props.optionGroupLabel;\n      var groupContent = props.optionGroupTemplate ? ObjectUtils.getJSXElement(props.optionGroupTemplate, option, index) : props.getOptionGroupLabel(option);\n      var key = index + '_' + props.getOptionGroupRenderKey(option);\n      var itemGroupProps = mergeProps({\n        className: cx('itemGroup', {\n          optionGroupLabel: optionGroupLabel\n        }),\n        style: style,\n        'data-p-highlight': props.selected\n      }, getPTOptions('itemGroup'));\n      var itemGroupLabelProps = mergeProps({\n        className: cx('itemGroupLabel')\n      }, getPTOptions('itemGroupLabel'));\n      return /*#__PURE__*/React.createElement(\"li\", _extends({\n        key: key\n      }, itemGroupProps), /*#__PURE__*/React.createElement(\"span\", itemGroupLabelProps, groupContent));\n    }\n    var optionKey = props.getOptionRenderKey(option) + '_' + index;\n    var optionLabel = props.getOptionLabel(option);\n    var disabled = props.isOptionDisabled(option);\n    return /*#__PURE__*/React.createElement(DropdownItem, {\n      key: optionKey,\n      label: optionLabel,\n      index: index,\n      focusedOptionIndex: props.focusedOptionIndex,\n      option: option,\n      ariaSetSize: ariaSetSize,\n      onInputKeyDown: props.onInputKeyDown,\n      style: style,\n      template: props.itemTemplate,\n      selected: props.isSelected(option),\n      highlightOnSelect: props.highlightOnSelect,\n      disabled: disabled,\n      onClick: props.onOptionClick,\n      onMouseMove: changeFocusedItemOnHover,\n      ptm: ptm,\n      cx: cx,\n      checkmark: props.checkmark\n    });\n  };\n  var createItems = function createItems() {\n    if (ObjectUtils.isNotEmpty(props.visibleOptions)) {\n      return props.visibleOptions.map(createItem);\n    } else if (props.hasFilter) {\n      return createEmptyMessage(props.emptyFilterMessage, true);\n    }\n    return createEmptyMessage(props.emptyMessage);\n  };\n  var createFilterClearIcon = function createFilterClearIcon() {\n    if (props.showFilterClear && props.filterValue) {\n      var ariaLabelFilterClear = localeOption('clear');\n      var clearIconProps = mergeProps({\n        className: cx('filterClearIcon'),\n        'aria-label': ariaLabelFilterClear,\n        onClick: function onClick() {\n          return props.onFilterClearIconClick(function () {\n            return DomHandler.focus(filterInputRef.current);\n          });\n        }\n      }, getPTOptions('filterClearIcon'));\n      var icon = props.filterClearIcon || /*#__PURE__*/React.createElement(TimesIcon, clearIconProps);\n      var filterClearIcon = IconUtils.getJSXIcon(icon, _objectSpread$1({}, clearIconProps), {\n        props: props\n      });\n      return filterClearIcon;\n    }\n    return null;\n  };\n  var createFilter = function createFilter() {\n    if (props.filter) {\n      var clearIcon = createFilterClearIcon();\n      var filterIconProps = mergeProps({\n        className: cx('filterIcon')\n      }, getPTOptions('filterIcon'));\n      var icon = props.filterIcon || /*#__PURE__*/React.createElement(SearchIcon, filterIconProps);\n      var filterIcon = IconUtils.getJSXIcon(icon, _objectSpread$1({}, filterIconProps), {\n        props: props\n      });\n      var filterContainerProps = mergeProps({\n        className: cx('filterContainer', {\n          clearIcon: clearIcon\n        })\n      }, getPTOptions('filterContainer'));\n      var filterInputProps = mergeProps({\n        ref: filterInputRef,\n        type: 'text',\n        autoComplete: 'off',\n        className: cx('filterInput', {\n          context: context\n        }),\n        placeholder: props.filterPlaceholder,\n        onKeyDown: props.onFilterInputKeyDown,\n        onChange: function onChange(e) {\n          return onFilterInputChange(e);\n        },\n        value: props.filterValue\n      }, getPTOptions('filterInput'));\n      var content = /*#__PURE__*/React.createElement(\"div\", filterContainerProps, /*#__PURE__*/React.createElement(\"input\", filterInputProps), clearIcon, filterIcon);\n      if (props.filterTemplate) {\n        var defaultContentOptions = {\n          className: classNames('p-dropdown-filter-container', {\n            'p-dropdown-clearable-filter': !!clearIcon\n          }),\n          element: content,\n          filterOptions: filterOptions,\n          filterInputKeyDown: props.onFilterInputKeyDown,\n          filterInputChange: onFilterInputChange,\n          filterIconClassName: 'p-dropdown-filter-icon',\n          clearIcon: clearIcon,\n          props: props\n        };\n        content = ObjectUtils.getJSXElement(props.filterTemplate, defaultContentOptions);\n      }\n      var headerProps = mergeProps({\n        className: cx('header')\n      }, getPTOptions('header'));\n      return /*#__PURE__*/React.createElement(\"div\", headerProps, content);\n    }\n    return null;\n  };\n  var createContent = function createContent() {\n    if (props.virtualScrollerOptions) {\n      var virtualScrollerProps = _objectSpread$1(_objectSpread$1({}, props.virtualScrollerOptions), {\n        style: _objectSpread$1(_objectSpread$1({}, props.virtualScrollerOptions.style), {\n          height: props.scrollHeight\n        }),\n        className: classNames('p-dropdown-items-wrapper', props.virtualScrollerOptions.className),\n        items: props.visibleOptions,\n        autoSize: true,\n        onLazyLoad: function onLazyLoad(event) {\n          return props.virtualScrollerOptions.onLazyLoad(_objectSpread$1(_objectSpread$1({}, event), {\n            filter: props.filterValue\n          }));\n        },\n        itemTemplate: function itemTemplate(item, options) {\n          return item && createItem(item, options.index, options);\n        },\n        contentTemplate: function contentTemplate(options) {\n          var emptyMessage = props.hasFilter ? props.emptyFilterMessage : props.emptyMessage;\n          var content = isEmptyFilter ? createEmptyMessage(emptyMessage) : options.children;\n          var listProps = mergeProps({\n            ref: options.contentRef,\n            style: options.style,\n            className: classNames(options.className, cx('list', {\n              virtualScrollerProps: props.virtualScrollerOptions\n            })),\n            role: 'listbox',\n            'aria-label': ariaLabel('listLabel')\n          }, getPTOptions('list'));\n          return /*#__PURE__*/React.createElement(\"ul\", listProps, content);\n        }\n      });\n      return /*#__PURE__*/React.createElement(VirtualScroller, _extends({\n        ref: props.virtualScrollerRef\n      }, virtualScrollerProps, {\n        pt: ptm('virtualScroller')\n      }));\n    }\n    var items = createItems();\n    var wrapperProps = mergeProps({\n      className: cx('wrapper'),\n      style: sx('wrapper')\n    }, getPTOptions('wrapper'));\n    var listProps = mergeProps({\n      className: cx('list'),\n      role: 'listbox',\n      'aria-label': ariaLabel('listLabel')\n    }, getPTOptions('list'));\n    return /*#__PURE__*/React.createElement(\"div\", wrapperProps, /*#__PURE__*/React.createElement(\"ul\", listProps, items));\n  };\n  var createElement = function createElement() {\n    var filter = createFilter();\n    var content = createContent();\n    var footer = createFooter();\n    var panelProps = mergeProps({\n      className: classNames(props.panelClassName, cx('panel', {\n        context: context\n      })),\n      style: sx('panel'),\n      onClick: props.onClick\n    }, getPTOptions('panel'));\n    var transitionProps = mergeProps({\n      classNames: cx('transition'),\n      \"in\": props[\"in\"],\n      timeout: {\n        enter: 120,\n        exit: 100\n      },\n      options: props.transitionOptions,\n      unmountOnExit: true,\n      onEnter: onEnter,\n      onEntered: onEntered,\n      onExit: props.onExit,\n      onExited: props.onExited\n    }, getPTOptions('transition'));\n    return /*#__PURE__*/React.createElement(CSSTransition, _extends({\n      nodeRef: ref\n    }, transitionProps), /*#__PURE__*/React.createElement(\"div\", _extends({\n      ref: ref\n    }, panelProps), props.firstFocusableElement, filter, content, footer, props.lastFocusableElement));\n  };\n  var element = createElement();\n  return /*#__PURE__*/React.createElement(Portal, {\n    element: element,\n    appendTo: props.appendTo\n  });\n}));\nDropdownPanel.displayName = 'DropdownPanel';\n\nfunction _createForOfIteratorHelper(r, e) { var t = \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && \"number\" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t[\"return\"] || t[\"return\"](); } finally { if (u) throw o; } } }; }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nvar Dropdown = /*#__PURE__*/React.memo(/*#__PURE__*/React.forwardRef(function (inProps, ref) {\n  var mergeProps = useMergeProps();\n  var context = React.useContext(PrimeReactContext);\n  var props = DropdownBase.getProps(inProps, context);\n  var _useDebounce = useDebounce('', props.filterDelay || 0),\n    _useDebounce2 = _slicedToArray(_useDebounce, 3),\n    filterValue = _useDebounce2[0],\n    filterState = _useDebounce2[1],\n    setFilterState = _useDebounce2[2];\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    focusedState = _React$useState2[0],\n    setFocusedState = _React$useState2[1];\n  var _React$useState3 = React.useState(-1),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    focusedOptionIndex = _React$useState4[0],\n    setFocusedOptionIndex = _React$useState4[1];\n  var _React$useState5 = React.useState(false),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    overlayVisibleState = _React$useState6[0],\n    setOverlayVisibleState = _React$useState6[1];\n  var clickedRef = React.useRef(false);\n  var elementRef = React.useRef(null);\n  var overlayRef = React.useRef(null);\n  var firstHiddenFocusableElementOnOverlay = React.useRef(null);\n  var lastHiddenFocusableElementOnOverlay = React.useRef(null);\n  var inputRef = React.useRef(props.inputRef);\n  var focusInputRef = React.useRef(props.focusInputRef);\n  var virtualScrollerRef = React.useRef(null);\n  var searchTimeout = React.useRef(null);\n  var searchValue = React.useRef(null);\n  var isLazy = props.virtualScrollerOptions && props.virtualScrollerOptions.lazy;\n  var hasFilter = ObjectUtils.isNotEmpty(filterState);\n  var appendTo = props.appendTo || context && context.appendTo || PrimeReact.appendTo;\n  var _DropdownBase$setMeta = DropdownBase.setMetaData(_objectSpread(_objectSpread({\n      props: props\n    }, props.__parentMetadata), {}, {\n      state: {\n        filter: filterState,\n        focused: focusedState,\n        overlayVisible: overlayVisibleState\n      }\n    })),\n    ptm = _DropdownBase$setMeta.ptm,\n    cx = _DropdownBase$setMeta.cx,\n    sx = _DropdownBase$setMeta.sx,\n    isUnstyled = _DropdownBase$setMeta.isUnstyled;\n  useHandleStyle(DropdownBase.css.styles, isUnstyled, {\n    name: 'dropdown'\n  });\n  var _useOverlayListener = useOverlayListener({\n      target: elementRef,\n      overlay: overlayRef,\n      listener: function listener(event, _ref) {\n        var type = _ref.type,\n          valid = _ref.valid;\n        if (valid) {\n          if (type === 'outside') {\n            if (!isClearClicked(event)) {\n              hide();\n            }\n          } else if (context.hideOverlaysOnDocumentScrolling) {\n            hide();\n          } else if (!DomHandler.isDocument(event.target)) {\n            alignOverlay();\n          }\n        }\n      },\n      when: overlayVisibleState\n    }),\n    _useOverlayListener2 = _slicedToArray(_useOverlayListener, 2),\n    bindOverlayListener = _useOverlayListener2[0],\n    unbindOverlayListener = _useOverlayListener2[1];\n  var flatOptions = function flatOptions(options) {\n    return (options || []).reduce(function (result, option, index) {\n      result.push(_objectSpread(_objectSpread({}, option), {}, {\n        group: true,\n        index: index\n      }));\n      var optionGroupChildren = getOptionGroupChildren(option);\n      optionGroupChildren && optionGroupChildren.forEach(function (o) {\n        return result.push(o);\n      });\n      return result;\n    }, []);\n  };\n  var getVisibleOptions = function getVisibleOptions() {\n    var options = props.optionGroupLabel ? flatOptions(props.options) : props.options;\n    if (hasFilter && !isLazy) {\n      var _filterValue = filterState.trim().toLocaleLowerCase(props.filterLocale);\n      var searchFields = props.filterBy ? props.filterBy.split(',') : [props.optionLabel || 'label'];\n      if (props.optionGroupLabel) {\n        var filteredGroups = [];\n        var _iterator = _createForOfIteratorHelper(props.options),\n          _step;\n        try {\n          for (_iterator.s(); !(_step = _iterator.n()).done;) {\n            var optgroup = _step.value;\n            var filteredSubOptions = FilterService.filter(getOptionGroupChildren(optgroup), searchFields, _filterValue, props.filterMatchMode, props.filterLocale);\n            if (filteredSubOptions && filteredSubOptions.length) {\n              filteredGroups.push(_objectSpread(_objectSpread({}, optgroup), _defineProperty({}, \"\".concat(props.optionGroupChildren), filteredSubOptions)));\n            }\n          }\n        } catch (err) {\n          _iterator.e(err);\n        } finally {\n          _iterator.f();\n        }\n        return flatOptions(filteredGroups);\n      }\n      return FilterService.filter(options, searchFields, _filterValue, props.filterMatchMode, props.filterLocale);\n    }\n    return options;\n  };\n  var onFirstHiddenFocus = function onFirstHiddenFocus(event) {\n    var focusableEl = event.relatedTarget === focusInputRef.current ? DomHandler.getFirstFocusableElement(overlayRef.current, ':not([data-p-hidden-focusable=\"true\"])') : focusInputRef.current;\n    DomHandler.focus(focusableEl);\n  };\n  var onLastHiddenFocus = function onLastHiddenFocus(event) {\n    var focusableEl = event.relatedTarget === focusInputRef.current ? DomHandler.getLastFocusableElement(overlayRef.current, ':not([data-p-hidden-focusable=\"true\"])') : focusInputRef.current;\n    DomHandler.focus(focusableEl);\n  };\n  var isClearClicked = function isClearClicked(event) {\n    return DomHandler.isAttributeEquals(event.target, 'data-pc-section', 'clearicon') || DomHandler.isAttributeEquals(event.target.parentElement || event.target, 'data-pc-section', 'filterclearicon');\n  };\n  var _onClick = function onClick(event) {\n    if (props.disabled || props.loading) {\n      return;\n    }\n    props.onClick && props.onClick(event);\n\n    // do not continue if the user defined click wants to prevent it\n    if (event.defaultPrevented) {\n      return;\n    }\n    if (isClearClicked(event) || event.target.tagName === 'INPUT') {\n      return;\n    } else if (!overlayRef.current || !(overlayRef.current && overlayRef.current.contains(event.target))) {\n      DomHandler.focus(focusInputRef.current);\n      overlayVisibleState ? hide() : show();\n    }\n    event.preventDefault();\n    clickedRef.current = true;\n  };\n  var onInputFocus = function onInputFocus(event) {\n    if (props.showOnFocus && !overlayVisibleState) {\n      show();\n    }\n    setFocusedState(true);\n    props.onFocus && props.onFocus(event);\n  };\n  var onInputBlur = function onInputBlur(event) {\n    setFocusedState(false);\n    if (props.onBlur) {\n      setTimeout(function () {\n        var currentValue = inputRef.current ? inputRef.current.value : undefined;\n        props.onBlur({\n          originalEvent: event.originalEvent,\n          value: currentValue,\n          stopPropagation: function stopPropagation() {\n            event.originalEvent.stopPropagation();\n          },\n          preventDefault: function preventDefault() {\n            event.originalEvent.preventDefault();\n          },\n          target: {\n            name: props.name,\n            id: props.id,\n            value: currentValue\n          }\n        });\n      }, 200);\n    }\n  };\n  var onOptionSelect = function onOptionSelect(event, option) {\n    var isHide = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;\n    selectItem({\n      originalEvent: event,\n      option: option\n    });\n    if (isHide) {\n      hide();\n      DomHandler.focus(focusInputRef.current);\n    }\n  };\n  var onPanelClick = function onPanelClick(event) {\n    OverlayService.emit('overlay-click', {\n      originalEvent: event,\n      target: elementRef.current\n    });\n  };\n  var onInputKeyDown = function onInputKeyDown(event) {\n    if (props.disabled) {\n      event.preventDefault();\n      return;\n    }\n    var code = DomHandler.isAndroid() ? event.key : event.code;\n    switch (code) {\n      case 'ArrowDown':\n        onArrowDownKey(event);\n        break;\n      case 'ArrowUp':\n        onArrowUpKey(event);\n        break;\n      case 'ArrowLeft':\n      case 'ArrowRight':\n        onArrowLeftKey(event, props.editable);\n        break;\n      case 'Home':\n        onHomeKey(event);\n        break;\n      case 'End':\n        onEndKey(event);\n        break;\n      case 'PageDown':\n        onPageDownKey(event);\n        break;\n      case 'PageUp':\n        onPageUpKey(event);\n        break;\n      case 'Space':\n        onSpaceKey(event, props.editable);\n        break;\n      case 'NumpadEnter':\n      case 'Enter':\n        onEnterKey(event);\n        break;\n      case 'Escape':\n        onEscapeKey(event);\n        break;\n      case 'Tab':\n        onTabKey(event);\n        break;\n      case 'Backspace':\n        onBackspaceKey(event, props.editable);\n        break;\n      case 'ShiftLeft':\n      case 'ShiftRight':\n        //NOOP\n        break;\n      default:\n        var metaKey = event.metaKey || event.ctrlKey || event.altKey;\n\n        // Only handle printable characters when no meta keys are pressed\n        if (!metaKey && ObjectUtils.isPrintableCharacter(event.key)) {\n          !overlayVisibleState && !props.editable && show();\n          !props.editable && searchOptions(event, event.key);\n        }\n        break;\n    }\n    clickedRef.current = false;\n  };\n  var onFilterInputKeyDown = function onFilterInputKeyDown(event) {\n    switch (event.code) {\n      case 'ArrowDown':\n        onArrowDownKey(event);\n        break;\n      case 'ArrowUp':\n        onArrowUpKey(event);\n        break;\n      case 'ArrowLeft':\n      case 'ArrowRight':\n        onArrowLeftKey(event, true);\n        break;\n      case 'Enter':\n      case 'NumpadEnter':\n        onEnterKey(event);\n        event.preventDefault();\n        break;\n      case 'Escape':\n        onEscapeKey(event);\n        break;\n    }\n  };\n  var hasFocusableElements = function hasFocusableElements() {\n    return DomHandler.getFocusableElements(overlayRef.current, ':not([data-p-hidden-focusable=\"true\"])').length > 0;\n  };\n  var isOptionMatched = function isOptionMatched(option) {\n    var _getOptionLabel;\n    return isValidOption(option) && ((_getOptionLabel = getOptionLabel(option)) === null || _getOptionLabel === void 0 ? void 0 : _getOptionLabel.toLocaleLowerCase(props.filterLocale).startsWith(searchValue.current.toLocaleLowerCase(props.filterLocale)));\n  };\n  var isValidOption = function isValidOption(option) {\n    return ObjectUtils.isNotEmpty(option) && !(isOptionDisabled(option) || isOptionGroup(option));\n  };\n  var hasSelectedOption = function hasSelectedOption() {\n    return ObjectUtils.isNotEmpty(props.value);\n  };\n  var isValidSelectedOption = function isValidSelectedOption(option) {\n    return isValidOption(option) && isSelected(option);\n  };\n  var findSelectedOptionIndex = function findSelectedOptionIndex() {\n    return hasSelectedOption ? visibleOptions.findIndex(function (option) {\n      return isValidSelectedOption(option);\n    }) : -1;\n  };\n  var findFirstFocusedOptionIndex = function findFirstFocusedOptionIndex() {\n    var selectedIndex = findSelectedOptionIndex();\n    return selectedIndex < 0 ? findFirstOptionIndex() : selectedIndex;\n  };\n  var searchOptions = function searchOptions(event, _char) {\n    searchValue.current = (searchValue.current || '') + _char;\n    var optionIndex = -1;\n    var matched = false;\n    if (ObjectUtils.isNotEmpty(searchValue.current)) {\n      if (focusedOptionIndex !== -1) {\n        optionIndex = visibleOptions.slice(focusedOptionIndex).findIndex(function (option) {\n          return isOptionMatched(option);\n        });\n        optionIndex = optionIndex === -1 ? visibleOptions.slice(0, focusedOptionIndex).findIndex(function (option) {\n          return isOptionMatched(option);\n        }) : optionIndex + focusedOptionIndex;\n      } else {\n        optionIndex = visibleOptions.findIndex(function (option) {\n          return isOptionMatched(option);\n        });\n      }\n      if (optionIndex !== -1) {\n        matched = true;\n      }\n      if (optionIndex === -1 && focusedOptionIndex === -1) {\n        optionIndex = findFirstFocusedOptionIndex();\n      }\n      if (optionIndex !== -1) {\n        changeFocusedOptionIndex(event, optionIndex);\n      }\n    }\n    if (searchTimeout.current) {\n      clearTimeout(searchTimeout.current);\n    }\n    searchTimeout.current = setTimeout(function () {\n      searchValue.current = '';\n      searchTimeout.current = null;\n    }, 500);\n    return matched;\n  };\n  var findLastFocusedOptionIndex = function findLastFocusedOptionIndex() {\n    var selectedIndex = findSelectedOptionIndex();\n    return selectedIndex < 0 ? findLastOptionIndex() : selectedIndex;\n  };\n  var findFirstOptionIndex = function findFirstOptionIndex() {\n    return visibleOptions.findIndex(function (option) {\n      return isValidOption(option);\n    });\n  };\n  var findLastOptionIndex = function findLastOptionIndex() {\n    return ObjectUtils.findLastIndex(visibleOptions, function (option) {\n      return isValidOption(option);\n    });\n  };\n  var findNextOptionIndex = function findNextOptionIndex(index) {\n    var matchedOptionIndex = index < visibleOptions.length - 1 ? visibleOptions.slice(index + 1).findIndex(function (option) {\n      return isValidOption(option);\n    }) : -1;\n    return matchedOptionIndex > -1 ? matchedOptionIndex + index + 1 : index;\n  };\n  var findPrevOptionIndex = function findPrevOptionIndex(index) {\n    var matchedOptionIndex = index > 0 ? ObjectUtils.findLastIndex(visibleOptions.slice(0, index), function (option) {\n      return isValidOption(option);\n    }) : -1;\n    return matchedOptionIndex > -1 ? matchedOptionIndex : index;\n  };\n  var changeFocusedOptionIndex = function changeFocusedOptionIndex(event, index) {\n    if (focusedOptionIndex !== index) {\n      setFocusedOptionIndex(index);\n      focusOnItem(index);\n      if (props.selectOnFocus) {\n        onOptionSelect(event, visibleOptions[index], false);\n      }\n    }\n  };\n  var focusOnItem = function focusOnItem(index) {\n    var focusedItem = DomHandler.findSingle(overlayRef.current, \"li[id=\\\"dropdownItem_\".concat(index, \"\\\"]\"));\n    focusedItem && focusedItem.focus();\n  };\n  var onArrowDownKey = function onArrowDownKey(event) {\n    if (!overlayVisibleState) {\n      show();\n      props.editable && changeFocusedOptionIndex(event, findSelectedOptionIndex());\n    } else {\n      var optionIndex = focusedOptionIndex !== -1 ? findNextOptionIndex(focusedOptionIndex) : clickedRef.current ? findFirstOptionIndex() : findFirstFocusedOptionIndex();\n      changeFocusedOptionIndex(event, optionIndex);\n    }\n    event.preventDefault();\n  };\n  var onArrowUpKey = function onArrowUpKey(event) {\n    var pressedInInputText = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    if (event.altKey && !pressedInInputText) {\n      if (focusedOptionIndex !== -1) {\n        onOptionSelect(event, visibleOptions[focusedOptionIndex]);\n      }\n      state.overlayVisible && hide();\n      event.preventDefault();\n    } else {\n      var optionIndex = focusedOptionIndex !== -1 ? findPrevOptionIndex(focusedOptionIndex) : clickedRef.current ? findLastOptionIndex() : findLastFocusedOptionIndex();\n      changeFocusedOptionIndex(event, optionIndex);\n      !overlayVisibleState && show();\n      event.preventDefault();\n    }\n  };\n  var onArrowLeftKey = function onArrowLeftKey(event) {\n    var pressedInInputText = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    pressedInInputText && setFocusedOptionIndex(-1);\n  };\n  var onHomeKey = function onHomeKey(event) {\n    var pressedInInputText = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    if (pressedInInputText) {\n      event.currentTarget.setSelectionRange(0, 0);\n      setFocusedOptionIndex(-1);\n    } else {\n      changeFocusedOptionIndex(event, findFirstOptionIndex());\n      !overlayVisibleState && show();\n    }\n    event.preventDefault();\n  };\n  var onEndKey = function onEndKey(event) {\n    var pressedInInputText = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    if (pressedInInputText) {\n      var target = event.currentTarget;\n      var len = target.value.length;\n      target.setSelectionRange(len, len);\n      setFocusedOptionIndex(-1);\n    } else {\n      changeFocusedOptionIndex(event, findLastOptionIndex());\n      !overlayVisibleState && show();\n    }\n    event.preventDefault();\n  };\n  var onPageUpKey = function onPageUpKey(event) {\n    event.preventDefault();\n  };\n  var onPageDownKey = function onPageDownKey(event) {\n    event.preventDefault();\n  };\n  var onSpaceKey = function onSpaceKey(event) {\n    var pressedInInputText = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    !pressedInInputText && onEnterKey(event);\n  };\n  var onEnterKey = function onEnterKey(event) {\n    event.preventDefault();\n    if (!overlayVisibleState) {\n      setFocusedOptionIndex(-1);\n      onArrowDownKey(event);\n    } else {\n      if (focusedOptionIndex === -1) {\n        return;\n      }\n      var focusedOption = visibleOptions[focusedOptionIndex];\n      var optionValue = getOptionValue(focusedOption);\n      if (optionValue == null || optionValue == undefined) {\n        hide();\n        resetFilter();\n        updateEditableLabel(selectedOption);\n        return;\n      }\n      onOptionSelect(event, focusedOption);\n    }\n  };\n  var onEscapeKey = function onEscapeKey(event) {\n    overlayVisibleState && hide();\n    event.preventDefault();\n  };\n  var onTabKey = function onTabKey(event) {\n    var pressedInInputText = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    if (!pressedInInputText) {\n      if (overlayVisibleState && !hasFocusableElements()) {\n        DomHandler.focus(firstHiddenFocusableElementOnOverlay.current);\n        event.preventDefault();\n      } else {\n        if (focusedOptionIndex !== -1) {\n          onOptionSelect(event, visibleOptions[focusedOptionIndex]);\n        }\n        overlayVisibleState && hide();\n      }\n    }\n  };\n  var onBackspaceKey = function onBackspaceKey(event) {\n    var pressedInInputText = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    if (event && pressedInInputText) {\n      !overlayVisibleState && show();\n    }\n  };\n  var findInArray = function findInArray(visibleOptions, searchText) {\n    if (!searchText || !(visibleOptions !== null && visibleOptions !== void 0 && visibleOptions.length)) return -1;\n    var normalizedSearch = searchText.toLocaleLowerCase();\n    var exactMatch = visibleOptions.findIndex(function (item) {\n      return getOptionLabel(item).toLocaleLowerCase() === normalizedSearch;\n    });\n    if (exactMatch !== -1) return exactMatch;\n    return visibleOptions.findIndex(function (item) {\n      return getOptionLabel(item).toLocaleLowerCase().startsWith(normalizedSearch);\n    });\n  };\n  var onEditableInputChange = function onEditableInputChange(event) {\n    !overlayVisibleState && show();\n    var searchIndex = null;\n    if (event.target.value && visibleOptions) {\n      searchIndex = findInArray(visibleOptions, event.target.value);\n    }\n    setFocusedOptionIndex(searchIndex);\n    if (props.onChange) {\n      props.onChange({\n        originalEvent: event.originalEvent,\n        value: event.target.value,\n        stopPropagation: function stopPropagation() {\n          event.originalEvent.stopPropagation();\n        },\n        preventDefault: function preventDefault() {\n          event.originalEvent.preventDefault();\n        },\n        target: {\n          name: props.name,\n          id: props.id,\n          value: event.target.value\n        }\n      });\n    }\n  };\n  var onEditableInputFocus = function onEditableInputFocus(event) {\n    setFocusedState(true);\n    hide();\n    props.onFocus && props.onFocus(event);\n  };\n  var onOptionClick = function onOptionClick(event) {\n    var option = event.option;\n    if (!option.disabled) {\n      selectItem(event);\n      DomHandler.focus(focusInputRef.current);\n    }\n    hide();\n  };\n  var onFilterInputChange = function onFilterInputChange(event) {\n    var filter = event.target.value;\n    setFilterState(filter);\n    if (props.onFilter) {\n      props.onFilter({\n        originalEvent: event,\n        filter: filter\n      });\n    }\n  };\n  var onFilterClearIconClick = function onFilterClearIconClick(callback) {\n    resetFilter(callback);\n  };\n  var resetFilter = function resetFilter(callback) {\n    setFilterState('');\n    props.onFilter && props.onFilter({\n      filter: ''\n    });\n    callback && callback();\n  };\n  var clear = function clear(event) {\n    if (props.onChange) {\n      props.onChange({\n        originalEvent: event,\n        value: undefined,\n        stopPropagation: function stopPropagation() {\n          event === null || event === void 0 || event.stopPropagation();\n        },\n        preventDefault: function preventDefault() {\n          event === null || event === void 0 || event.preventDefault();\n        },\n        target: {\n          name: props.name,\n          id: props.id,\n          value: undefined\n        }\n      });\n    }\n    if (props.filter) {\n      resetFilter();\n    }\n    updateEditableLabel();\n    setFocusedOptionIndex(-1);\n  };\n  var selectItem = function selectItem(event) {\n    if (selectedOption !== event.option) {\n      updateEditableLabel(event.option);\n      setFocusedOptionIndex(-1);\n      var optionValue = getOptionValue(event.option);\n      var selectedOptionIndex = findOptionIndexInList(event.option, visibleOptions);\n      if (props.onChange) {\n        props.onChange({\n          originalEvent: event.originalEvent,\n          value: optionValue,\n          stopPropagation: function stopPropagation() {\n            event.originalEvent.stopPropagation();\n          },\n          preventDefault: function preventDefault() {\n            event.originalEvent.preventDefault();\n          },\n          target: {\n            name: props.name,\n            id: props.id,\n            value: optionValue\n          }\n        });\n      }\n      changeFocusedOptionIndex(event.originalEvent, selectedOptionIndex);\n    }\n  };\n  var getSelectedOptionIndex = function getSelectedOptionIndex(options) {\n    options = options || visibleOptions;\n    if (options) {\n      if (props.optionGroupLabel) {\n        for (var i = 0; i < options.length; i++) {\n          var selectedOptionIndex = findOptionIndexInList(props.value, getOptionGroupChildren(options[i]));\n          if (selectedOptionIndex !== -1) {\n            return {\n              group: i,\n              option: selectedOptionIndex\n            };\n          }\n        }\n      } else {\n        return findOptionIndexInList(props.value, options);\n      }\n    }\n    return -1;\n  };\n  var equalityKey = function equalityKey() {\n    return props.optionValue ? null : props.dataKey;\n  };\n  var findOptionIndexInList = function findOptionIndexInList(value, list) {\n    var key = equalityKey();\n    return list.findIndex(function (item) {\n      return ObjectUtils.equals(value, getOptionValue(item), key);\n    });\n  };\n  var isSelected = function isSelected(option) {\n    return ObjectUtils.equals(props.value, getOptionValue(option), equalityKey());\n  };\n  var show = function show() {\n    setFocusedOptionIndex(focusedOptionIndex !== -1 ? focusedOptionIndex : props.autoOptionFocus ? findFirstFocusedOptionIndex() : props.editable ? -1 : findSelectedOptionIndex());\n    setOverlayVisibleState(true);\n  };\n  var hide = function hide() {\n    setOverlayVisibleState(false);\n    clickedRef.current = false;\n  };\n  var onFocus = function onFocus() {\n    if (props.editable && !overlayVisibleState && clickedRef.current === false) {\n      DomHandler.focus(inputRef.current);\n    }\n  };\n  var onOverlayEnter = function onOverlayEnter(callback) {\n    ZIndexUtils.set('overlay', overlayRef.current, context && context.autoZIndex || PrimeReact.autoZIndex, context && context.zIndex.overlay || PrimeReact.zIndex.overlay);\n    DomHandler.addStyles(overlayRef.current, {\n      position: 'absolute',\n      top: '0',\n      left: '0'\n    });\n    alignOverlay();\n    callback && callback();\n  };\n  var onOverlayEntered = function onOverlayEntered(callback) {\n    callback && callback();\n    bindOverlayListener();\n    props.onShow && props.onShow();\n  };\n  var onOverlayExit = function onOverlayExit() {\n    unbindOverlayListener();\n  };\n  var onOverlayExited = function onOverlayExited() {\n    if (props.filter && props.resetFilterOnHide) {\n      resetFilter();\n    }\n    ZIndexUtils.clear(overlayRef.current);\n    props.onHide && props.onHide();\n  };\n  var alignOverlay = function alignOverlay() {\n    DomHandler.alignOverlay(overlayRef.current, inputRef.current.parentElement, props.appendTo || context && context.appendTo || PrimeReact.appendTo);\n  };\n  var scrollInView = function scrollInView() {\n    var focusedItem = DomHandler.findSingle(overlayRef.current, 'li[data-p-focused=\"true\"]');\n    if (focusedItem && focusedItem.scrollIntoView) {\n      focusedItem.scrollIntoView({\n        block: 'nearest',\n        inline: 'nearest'\n      });\n    } else {\n      var highlightItem = DomHandler.findSingle(overlayRef.current, 'li[data-p-highlight=\"true\"]');\n      if (highlightItem && highlightItem.scrollIntoView) {\n        highlightItem.scrollIntoView({\n          block: 'nearest',\n          inline: 'nearest'\n        });\n      }\n    }\n  };\n  var updateEditableLabel = function updateEditableLabel(option) {\n    if (inputRef.current) {\n      inputRef.current.value = option ? getOptionLabel(option) : props.value || '';\n\n      // #1413 NVDA screenreader\n      if (focusInputRef.current) {\n        focusInputRef.current.value = inputRef.current.value;\n      }\n    }\n  };\n  var getOptionLabel = function getOptionLabel(option) {\n    if (ObjectUtils.isScalar(option)) {\n      return \"\".concat(option);\n    }\n    var optionLabel = props.optionLabel ? ObjectUtils.resolveFieldData(option, props.optionLabel) : option['label'];\n    return \"\".concat(optionLabel);\n  };\n  var getOptionValue = function getOptionValue(option) {\n    if (props.useOptionAsValue) {\n      return option;\n    }\n    var optionValue = props.optionValue ? ObjectUtils.resolveFieldData(option, props.optionValue) : option ? option['value'] : ObjectUtils.resolveFieldData(option, 'value');\n    return props.optionValue || ObjectUtils.isNotEmpty(optionValue) ? optionValue : option;\n  };\n  var getOptionRenderKey = function getOptionRenderKey(option) {\n    return props.dataKey ? ObjectUtils.resolveFieldData(option, props.dataKey) : getOptionLabel(option);\n  };\n  var isOptionGroup = function isOptionGroup(option) {\n    return props.optionGroupLabel && option.group;\n  };\n  var isOptionDisabled = function isOptionDisabled(option) {\n    if (props.optionDisabled) {\n      return ObjectUtils.isFunction(props.optionDisabled) ? props.optionDisabled(option) : ObjectUtils.resolveFieldData(option, props.optionDisabled);\n    }\n    return option && option.disabled !== undefined ? option.disabled : false;\n  };\n  var getOptionGroupRenderKey = function getOptionGroupRenderKey(optionGroup) {\n    return ObjectUtils.resolveFieldData(optionGroup, props.optionGroupLabel);\n  };\n  var getOptionGroupLabel = function getOptionGroupLabel(optionGroup) {\n    return ObjectUtils.resolveFieldData(optionGroup, props.optionGroupLabel);\n  };\n  var getOptionGroupChildren = function getOptionGroupChildren(optionGroup) {\n    return ObjectUtils.resolveFieldData(optionGroup, props.optionGroupChildren);\n  };\n  var updateInputField = function updateInputField() {\n    if (props.editable && inputRef.current) {\n      var label = selectedOption ? getOptionLabel(selectedOption) : null;\n      var value = label || props.value || '';\n      inputRef.current.value = value;\n\n      // #1413 NVDA screenreader\n      if (focusInputRef.current) {\n        focusInputRef.current.value = value;\n      }\n    }\n  };\n  var getSelectedOption = function getSelectedOption() {\n    var index = getSelectedOptionIndex(props.options);\n    return index !== -1 ? props.optionGroupLabel ? getOptionGroupChildren(props.options[index.group])[index.option] : props.options[index] : null;\n  };\n  React.useImperativeHandle(ref, function () {\n    return {\n      props: props,\n      show: show,\n      hide: hide,\n      clear: clear,\n      focus: function focus() {\n        return DomHandler.focus(focusInputRef.current);\n      },\n      getElement: function getElement() {\n        return elementRef.current;\n      },\n      getOverlay: function getOverlay() {\n        return overlayRef.current;\n      },\n      getInput: function getInput() {\n        return inputRef.current;\n      },\n      getFocusInput: function getFocusInput() {\n        return focusInputRef.current;\n      },\n      getVirtualScroller: function getVirtualScroller() {\n        return virtualScrollerRef.current;\n      }\n    };\n  });\n  React.useEffect(function () {\n    ObjectUtils.combinedRefs(inputRef, props.inputRef);\n    ObjectUtils.combinedRefs(focusInputRef, props.focusInputRef);\n  }, [inputRef, props.inputRef, focusInputRef, props.focusInputRef]);\n  useMountEffect(function () {\n    if (props.autoFocus) {\n      DomHandler.focus(focusInputRef.current, props.autoFocus);\n    }\n    alignOverlay();\n  });\n  useUpdateEffect(function () {\n    if (overlayVisibleState && (props.value || focusedOptionIndex >= 0)) {\n      scrollInView();\n    }\n  }, [overlayVisibleState, props.value, focusedOptionIndex]);\n  useUpdateEffect(function () {\n    if (overlayVisibleState && filterState && props.filter) {\n      alignOverlay();\n    }\n  }, [overlayVisibleState, filterState, props.filter]);\n  useUpdateEffect(function () {\n    virtualScrollerRef.current && virtualScrollerRef.current.scrollInView(0);\n  }, [filterState]);\n  useUpdateEffect(function () {\n    updateInputField();\n    if (inputRef.current) {\n      inputRef.current.selectedIndex = 1;\n    }\n  });\n  useUnmountEffect(function () {\n    ZIndexUtils.clear(overlayRef.current);\n  });\n  var createHiddenSelect = function createHiddenSelect() {\n    var option = {\n      value: '',\n      label: props.placeholder\n    };\n    if (selectedOption) {\n      var optionValue = getOptionValue(selectedOption);\n      option = {\n        value: _typeof(optionValue) === 'object' ? props.options.findIndex(function (o) {\n          return o === optionValue;\n        }) : optionValue,\n        label: getOptionLabel(selectedOption)\n      };\n    }\n    var hiddenSelectedMessageProps = mergeProps({\n      className: 'p-hidden-accessible p-dropdown-hidden-select'\n    }, ptm('hiddenSelectedMessage'));\n    var selectProps = mergeProps({\n      ref: inputRef,\n      required: props.required,\n      defaultValue: option.value,\n      name: props.name,\n      tabIndex: -1\n    }, ptm('select'));\n    var optionProps = mergeProps({\n      value: option.value\n    }, ptm('option'));\n    return /*#__PURE__*/React.createElement(\"div\", hiddenSelectedMessageProps, /*#__PURE__*/React.createElement(\"select\", selectProps, /*#__PURE__*/React.createElement(\"option\", optionProps, option.label)));\n  };\n  var createKeyboardHelper = function createKeyboardHelper() {\n    var value = ObjectUtils.isNotEmpty(selectedOption) ? getOptionLabel(selectedOption) : null;\n    if (props.editable) {\n      value = value || props.value || '';\n    }\n    var hiddenSelectedMessageProps = mergeProps({\n      className: 'p-hidden-accessible'\n    }, ptm('hiddenSelectedMessage'));\n    var inputProps = mergeProps(_objectSpread({\n      ref: focusInputRef,\n      id: props.inputId,\n      defaultValue: value,\n      type: 'text',\n      readOnly: true,\n      'aria-haspopup': 'listbox',\n      onFocus: onInputFocus,\n      onBlur: onInputBlur,\n      onKeyDown: onInputKeyDown,\n      disabled: props.disabled,\n      tabIndex: !props.disabled ? props.tabIndex || 0 : -1\n    }, ariaProps), ptm('input'));\n    return /*#__PURE__*/React.createElement(\"div\", hiddenSelectedMessageProps, /*#__PURE__*/React.createElement(\"input\", inputProps));\n  };\n  var createLabel = function createLabel() {\n    var label = ObjectUtils.isNotEmpty(selectedOption) ? getOptionLabel(selectedOption) : null;\n    if (props.editable) {\n      var value = label || props.value || '';\n      var _inputProps = mergeProps(_objectSpread({\n        ref: inputRef,\n        type: 'text',\n        defaultValue: value,\n        className: cx('input', {\n          label: label\n        }),\n        disabled: props.disabled,\n        placeholder: props.placeholder,\n        maxLength: props.maxLength,\n        onInput: onEditableInputChange,\n        onFocus: onEditableInputFocus,\n        onKeyDown: onInputKeyDown,\n        onBlur: onInputBlur,\n        tabIndex: !props.disabled ? props.tabIndex || 0 : -1,\n        'aria-haspopup': 'listbox'\n      }, ariaProps), ptm('input'));\n      return /*#__PURE__*/React.createElement(\"input\", _inputProps);\n    }\n    var content = props.valueTemplate ? ObjectUtils.getJSXElement(props.valueTemplate, selectedOption, props) : label || props.placeholder || props.emptyMessage || /*#__PURE__*/React.createElement(React.Fragment, null, \"\\xA0\");\n    var inputProps = mergeProps({\n      ref: inputRef,\n      className: cx('input', {\n        label: label\n      }),\n      tabIndex: '-1'\n    }, ptm('input'));\n    return /*#__PURE__*/React.createElement(\"span\", inputProps, content);\n  };\n  var onClearIconKeyDown = function onClearIconKeyDown(event) {\n    if (event.key === 'Enter' || event.code === 'Space') {\n      clear(event);\n      event.preventDefault();\n    }\n  };\n  var createClearIcon = function createClearIcon() {\n    if (props.value != null && props.showClear && !props.disabled && !ObjectUtils.isEmpty(props.options)) {\n      var clearIconProps = mergeProps({\n        className: cx('clearIcon'),\n        onPointerUp: clear,\n        tabIndex: props.editable ? -1 : props.tabIndex || '0',\n        onKeyDown: onClearIconKeyDown,\n        'aria-label': localeOption('clear')\n      }, ptm('clearIcon'));\n      var icon = props.clearIcon || /*#__PURE__*/React.createElement(TimesIcon, clearIconProps);\n      return IconUtils.getJSXIcon(icon, _objectSpread({}, clearIconProps), {\n        props: props\n      });\n    }\n    return null;\n  };\n  var createLoadingIcon = function createLoadingIcon() {\n    var loadingIconProps = mergeProps({\n      className: cx('loadingIcon'),\n      'data-pr-overlay-visible': overlayVisibleState\n    }, ptm('loadingIcon'));\n    var icon = props.loadingIcon || /*#__PURE__*/React.createElement(SpinnerIcon, {\n      spin: true\n    });\n    var loadingIcon = IconUtils.getJSXIcon(icon, _objectSpread({}, loadingIconProps), {\n      props: props\n    });\n    var ariaLabel = props.placeholder || props.ariaLabel;\n    var loadingButtonProps = mergeProps({\n      className: cx('trigger'),\n      role: 'button',\n      'aria-haspopup': 'listbox',\n      'aria-expanded': overlayVisibleState,\n      'aria-label': ariaLabel\n    }, ptm('trigger'));\n    return /*#__PURE__*/React.createElement(\"div\", loadingButtonProps, loadingIcon);\n  };\n  var createDropdownIcon = function createDropdownIcon() {\n    var dropdownIconProps = mergeProps({\n      className: cx('dropdownIcon'),\n      'data-pr-overlay-visible': overlayVisibleState\n    }, ptm('dropdownIcon'));\n    var icon = !overlayVisibleState ? props.dropdownIcon || /*#__PURE__*/React.createElement(ChevronDownIcon, dropdownIconProps) : props.collapseIcon || /*#__PURE__*/React.createElement(ChevronUpIcon, dropdownIconProps);\n    var dropdownIcon = IconUtils.getJSXIcon(icon, _objectSpread({}, dropdownIconProps), {\n      props: props\n    });\n    var ariaLabel = props.placeholder || props.ariaLabel;\n    var triggerProps = mergeProps({\n      className: cx('trigger'),\n      role: 'button',\n      'aria-haspopup': 'listbox',\n      'aria-expanded': overlayVisibleState,\n      'aria-label': ariaLabel\n    }, ptm('trigger'));\n    return /*#__PURE__*/React.createElement(\"div\", triggerProps, dropdownIcon);\n  };\n  var visibleOptions = getVisibleOptions();\n  var selectedOption = getSelectedOption();\n  var hasTooltip = ObjectUtils.isNotEmpty(props.tooltip);\n  var otherProps = DropdownBase.getOtherProps(props);\n  var ariaProps = ObjectUtils.reduceKeys(otherProps, DomHandler.ARIA_PROPS);\n  var hiddenSelect = createHiddenSelect();\n  var keyboardHelper = createKeyboardHelper();\n  var labelElement = createLabel();\n  var dropdownIcon = props.loading ? createLoadingIcon() : createDropdownIcon();\n  var clearIcon = createClearIcon();\n  var rootProps = mergeProps({\n    id: props.id,\n    ref: elementRef,\n    className: classNames(props.className, cx('root', {\n      context: context,\n      focusedState: focusedState,\n      overlayVisibleState: overlayVisibleState\n    })),\n    style: props.style,\n    onClick: function onClick(e) {\n      return _onClick(e);\n    },\n    onMouseDown: props.onMouseDown,\n    onContextMenu: props.onContextMenu,\n    onFocus: onFocus,\n    'data-p-disabled': props.disabled,\n    'data-p-focus': focusedState,\n    'aria-activedescendant': focusedState ? \"dropdownItem_\".concat(focusedOptionIndex) : undefined\n  }, otherProps, ptm('root'));\n  var firstHiddenFocusableElementProps = mergeProps({\n    ref: firstHiddenFocusableElementOnOverlay,\n    role: 'presentation',\n    className: 'p-hidden-accessible p-hidden-focusable',\n    tabIndex: '0',\n    onFocus: onFirstHiddenFocus,\n    'data-p-hidden-accessible': true,\n    'data-p-hidden-focusable': true\n  }, ptm('hiddenFirstFocusableEl'));\n  var lastHiddenFocusableElementProps = mergeProps({\n    ref: lastHiddenFocusableElementOnOverlay,\n    role: 'presentation',\n    className: 'p-hidden-accessible p-hidden-focusable',\n    tabIndex: '0',\n    onFocus: onLastHiddenFocus,\n    'data-p-hidden-accessible': true,\n    'data-p-hidden-focusable': true\n  }, ptm('hiddenLastFocusableEl'));\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"div\", rootProps, keyboardHelper, hiddenSelect, labelElement, clearIcon, dropdownIcon, /*#__PURE__*/React.createElement(DropdownPanel, _extends({\n    hostName: \"Dropdown\",\n    ref: overlayRef,\n    visibleOptions: visibleOptions,\n    virtualScrollerRef: virtualScrollerRef\n  }, props, {\n    appendTo: appendTo,\n    cx: cx,\n    filterValue: filterValue,\n    focusedOptionIndex: focusedOptionIndex,\n    getOptionGroupChildren: getOptionGroupChildren,\n    getOptionGroupLabel: getOptionGroupLabel,\n    getOptionGroupRenderKey: getOptionGroupRenderKey,\n    getOptionLabel: getOptionLabel,\n    getOptionRenderKey: getOptionRenderKey,\n    getSelectedOptionIndex: getSelectedOptionIndex,\n    hasFilter: hasFilter,\n    \"in\": overlayVisibleState,\n    isOptionDisabled: isOptionDisabled,\n    isSelected: isSelected,\n    onOverlayHide: hide,\n    onClick: onPanelClick,\n    onEnter: onOverlayEnter,\n    onEntered: onOverlayEntered,\n    onExit: onOverlayExit,\n    onExited: onOverlayExited,\n    onFilterClearIconClick: onFilterClearIconClick,\n    onFilterInputChange: onFilterInputChange,\n    onFilterInputKeyDown: onFilterInputKeyDown,\n    onOptionClick: onOptionClick,\n    onInputKeyDown: onInputKeyDown,\n    ptm: ptm,\n    resetFilter: resetFilter,\n    changeFocusedOptionIndex: changeFocusedOptionIndex,\n    firstFocusableElement: /*#__PURE__*/React.createElement(\"span\", firstHiddenFocusableElementProps),\n    lastFocusableElement: /*#__PURE__*/React.createElement(\"span\", lastHiddenFocusableElementProps),\n    sx: sx\n  }))), hasTooltip && /*#__PURE__*/React.createElement(Tooltip, _extends({\n    target: elementRef,\n    content: props.tooltip,\n    pt: ptm('tooltip')\n  }, props.tooltipOptions)));\n}));\nDropdown.displayName = 'Dropdown';\n\nexport { Dropdown };\n"], "mappings": "AAAA,YAAY;;AACZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,IAAIC,iBAAiB,EAAEC,SAAS,EAAEC,YAAY,EAAEC,aAAa,QAAQ,gBAAgB;AACtG,SAASC,aAAa,EAAEC,cAAc,QAAQ,0BAA0B;AACxE,SAASC,aAAa,EAAEC,WAAW,EAAEC,kBAAkB,EAAEC,cAAc,EAAEC,eAAe,EAAEC,gBAAgB,QAAQ,kBAAkB;AACpI,SAASC,eAAe,QAAQ,8BAA8B;AAC9D,SAASC,aAAa,QAAQ,4BAA4B;AAC1D,SAASC,WAAW,QAAQ,0BAA0B;AACtD,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,cAAc,QAAQ,2BAA2B;AAC1D,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,SAASC,UAAU,EAAEC,WAAW,EAAEC,UAAU,EAAEC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AAC9F,SAASC,aAAa,QAAQ,0BAA0B;AACxD,SAASC,UAAU,QAAQ,yBAAyB;AACpD,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,eAAe,QAAQ,4BAA4B;AAC5D,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,QAAQ,QAAQ,qBAAqB;AAE9C,SAASC,QAAQA,CAAA,EAAG;EAClB,OAAOA,QAAQ,GAAGC,MAAM,CAACC,MAAM,GAAGD,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUC,CAAC,EAAE;IACpE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MACzC,IAAIG,CAAC,GAAGF,SAAS,CAACD,CAAC,CAAC;MACpB,KAAK,IAAII,CAAC,IAAID,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEE,cAAc,CAACC,IAAI,CAACH,CAAC,EAAEC,CAAC,CAAC,KAAKL,CAAC,CAACK,CAAC,CAAC,GAAGD,CAAC,CAACC,CAAC,CAAC,CAAC;IAClE;IACA,OAAOL,CAAC;EACV,CAAC,EAAEJ,QAAQ,CAACY,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;AACpC;AAEA,SAASO,OAAOA,CAACC,CAAC,EAAE;EAClB,yBAAyB;;EAEzB,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,CAAC,EAAE;IAChG,OAAO,OAAOA,CAAC;EACjB,CAAC,GAAG,UAAUA,CAAC,EAAE;IACf,OAAOA,CAAC,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,CAAC,CAACG,WAAW,KAAKF,MAAM,IAAID,CAAC,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,CAAC;EACrH,CAAC,EAAED,OAAO,CAACC,CAAC,CAAC;AACf;AAEA,SAASK,WAAWA,CAACX,CAAC,EAAEC,CAAC,EAAE;EACzB,IAAI,QAAQ,IAAII,OAAO,CAACL,CAAC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAC1C,IAAIH,CAAC,GAAGG,CAAC,CAACO,MAAM,CAACI,WAAW,CAAC;EAC7B,IAAI,KAAK,CAAC,KAAKd,CAAC,EAAE;IAChB,IAAIe,CAAC,GAAGf,CAAC,CAACM,IAAI,CAACH,CAAC,EAAEC,CAAC,IAAI,SAAS,CAAC;IACjC,IAAI,QAAQ,IAAII,OAAO,CAACO,CAAC,CAAC,EAAE,OAAOA,CAAC;IACpC,MAAM,IAAIC,SAAS,CAAC,8CAA8C,CAAC;EACrE;EACA,OAAO,CAAC,QAAQ,KAAKZ,CAAC,GAAGa,MAAM,GAAGC,MAAM,EAAEf,CAAC,CAAC;AAC9C;AAEA,SAASgB,aAAaA,CAAChB,CAAC,EAAE;EACxB,IAAIY,CAAC,GAAGD,WAAW,CAACX,CAAC,EAAE,QAAQ,CAAC;EAChC,OAAO,QAAQ,IAAIK,OAAO,CAACO,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAC5C;AAEA,SAASK,eAAeA,CAACpB,CAAC,EAAEI,CAAC,EAAED,CAAC,EAAE;EAChC,OAAO,CAACC,CAAC,GAAGe,aAAa,CAACf,CAAC,CAAC,KAAKJ,CAAC,GAAGJ,MAAM,CAACyB,cAAc,CAACrB,CAAC,EAAEI,CAAC,EAAE;IAC/DkB,KAAK,EAAEnB,CAAC;IACRoB,UAAU,EAAE,CAAC,CAAC;IACdC,YAAY,EAAE,CAAC,CAAC;IAChBC,QAAQ,EAAE,CAAC;EACb,CAAC,CAAC,GAAGzB,CAAC,CAACI,CAAC,CAAC,GAAGD,CAAC,EAAEH,CAAC;AAClB;AAEA,SAAS0B,eAAeA,CAACtB,CAAC,EAAE;EAC1B,IAAIuB,KAAK,CAACC,OAAO,CAACxB,CAAC,CAAC,EAAE,OAAOA,CAAC;AAChC;AAEA,SAASyB,qBAAqBA,CAACzB,CAAC,EAAE0B,CAAC,EAAE;EACnC,IAAI3B,CAAC,GAAG,IAAI,IAAIC,CAAC,GAAG,IAAI,GAAG,WAAW,IAAI,OAAOM,MAAM,IAAIN,CAAC,CAACM,MAAM,CAACC,QAAQ,CAAC,IAAIP,CAAC,CAAC,YAAY,CAAC;EAChG,IAAI,IAAI,IAAID,CAAC,EAAE;IACb,IAAIH,CAAC;MACHD,CAAC;MACDgB,CAAC;MACDgB,CAAC;MACDC,CAAC,GAAG,EAAE;MACNC,CAAC,GAAG,CAAC,CAAC;MACNxB,CAAC,GAAG,CAAC,CAAC;IACR,IAAI;MACF,IAAIM,CAAC,GAAG,CAACZ,CAAC,GAAGA,CAAC,CAACG,IAAI,CAACF,CAAC,CAAC,EAAE8B,IAAI,EAAE,CAAC,KAAKJ,CAAC,EAAE;QACrC,IAAIlC,MAAM,CAACO,CAAC,CAAC,KAAKA,CAAC,EAAE;QACrB8B,CAAC,GAAG,CAAC,CAAC;MACR,CAAC,MAAM,OAAO,EAAEA,CAAC,GAAG,CAACjC,CAAC,GAAGe,CAAC,CAACT,IAAI,CAACH,CAAC,CAAC,EAAEgC,IAAI,CAAC,KAAKH,CAAC,CAACI,IAAI,CAACpC,CAAC,CAACsB,KAAK,CAAC,EAAEU,CAAC,CAAC9B,MAAM,KAAK4B,CAAC,CAAC,EAAEG,CAAC,GAAG,CAAC,CAAC,CAAC;IACzF,CAAC,CAAC,OAAO7B,CAAC,EAAE;MACVK,CAAC,GAAG,CAAC,CAAC,EAAEV,CAAC,GAAGK,CAAC;IACf,CAAC,SAAS;MACR,IAAI;QACF,IAAI,CAAC6B,CAAC,IAAI,IAAI,IAAI9B,CAAC,CAAC,QAAQ,CAAC,KAAK4B,CAAC,GAAG5B,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAEP,MAAM,CAACmC,CAAC,CAAC,KAAKA,CAAC,CAAC,EAAE;MACzE,CAAC,SAAS;QACR,IAAItB,CAAC,EAAE,MAAMV,CAAC;MAChB;IACF;IACA,OAAOiC,CAAC;EACV;AACF;AAEA,SAASK,mBAAmBA,CAACjC,CAAC,EAAE4B,CAAC,EAAE;EACjC,CAAC,IAAI,IAAIA,CAAC,IAAIA,CAAC,GAAG5B,CAAC,CAACF,MAAM,MAAM8B,CAAC,GAAG5B,CAAC,CAACF,MAAM,CAAC;EAC7C,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAED,CAAC,GAAG4B,KAAK,CAACK,CAAC,CAAC,EAAEhC,CAAC,GAAGgC,CAAC,EAAEhC,CAAC,EAAE,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGI,CAAC,CAACJ,CAAC,CAAC;EACrD,OAAOD,CAAC;AACV;AAEA,SAASuC,6BAA6BA,CAAClC,CAAC,EAAE4B,CAAC,EAAE;EAC3C,IAAI5B,CAAC,EAAE;IACL,IAAI,QAAQ,IAAI,OAAOA,CAAC,EAAE,OAAOiC,mBAAmB,CAACjC,CAAC,EAAE4B,CAAC,CAAC;IAC1D,IAAI7B,CAAC,GAAG,CAAC,CAAC,CAACoC,QAAQ,CAACjC,IAAI,CAACF,CAAC,CAAC,CAACoC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACxC,OAAO,QAAQ,KAAKrC,CAAC,IAAIC,CAAC,CAACQ,WAAW,KAAKT,CAAC,GAAGC,CAAC,CAACQ,WAAW,CAAC6B,IAAI,CAAC,EAAE,KAAK,KAAKtC,CAAC,IAAI,KAAK,KAAKA,CAAC,GAAGwB,KAAK,CAACe,IAAI,CAACtC,CAAC,CAAC,GAAG,WAAW,KAAKD,CAAC,IAAI,0CAA0C,CAACwC,IAAI,CAACxC,CAAC,CAAC,GAAGkC,mBAAmB,CAACjC,CAAC,EAAE4B,CAAC,CAAC,GAAG,KAAK,CAAC;EAC/N;AACF;AAEA,SAASY,gBAAgBA,CAAA,EAAG;EAC1B,MAAM,IAAI5B,SAAS,CAAC,2IAA2I,CAAC;AAClK;AAEA,SAAS6B,cAAcA,CAACzC,CAAC,EAAEJ,CAAC,EAAE;EAC5B,OAAO0B,eAAe,CAACtB,CAAC,CAAC,IAAIyB,qBAAqB,CAACzB,CAAC,EAAEJ,CAAC,CAAC,IAAIsC,6BAA6B,CAAClC,CAAC,EAAEJ,CAAC,CAAC,IAAI4C,gBAAgB,CAAC,CAAC;AACvH;AAEA,SAASE,SAASA,CAAC9C,CAAC,EAAEI,CAAC,EAAE;EAAE,IAAID,CAAC,GAAGP,MAAM,CAACmD,IAAI,CAAC/C,CAAC,CAAC;EAAE,IAAIJ,MAAM,CAACoD,qBAAqB,EAAE;IAAE,IAAIvC,CAAC,GAAGb,MAAM,CAACoD,qBAAqB,CAAChD,CAAC,CAAC;IAAEI,CAAC,KAAKK,CAAC,GAAGA,CAAC,CAACwC,MAAM,CAAC,UAAU7C,CAAC,EAAE;MAAE,OAAOR,MAAM,CAACsD,wBAAwB,CAAClD,CAAC,EAAEI,CAAC,CAAC,CAACmB,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEpB,CAAC,CAACiC,IAAI,CAAC7B,KAAK,CAACJ,CAAC,EAAEM,CAAC,CAAC;EAAE;EAAE,OAAON,CAAC;AAAE;AAChQ,SAASgD,eAAeA,CAACnD,CAAC,EAAE;EAAE,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,SAAS,CAACC,MAAM,EAAEE,CAAC,EAAE,EAAE;IAAE,IAAID,CAAC,GAAG,IAAI,IAAIF,SAAS,CAACG,CAAC,CAAC,GAAGH,SAAS,CAACG,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAG0C,SAAS,CAAClD,MAAM,CAACO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACiD,OAAO,CAAC,UAAUhD,CAAC,EAAE;MAAEgB,eAAe,CAACpB,CAAC,EAAEI,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGR,MAAM,CAACyD,yBAAyB,GAAGzD,MAAM,CAAC0D,gBAAgB,CAACtD,CAAC,EAAEJ,MAAM,CAACyD,yBAAyB,CAAClD,CAAC,CAAC,CAAC,GAAG2C,SAAS,CAAClD,MAAM,CAACO,CAAC,CAAC,CAAC,CAACiD,OAAO,CAAC,UAAUhD,CAAC,EAAE;MAAER,MAAM,CAACyB,cAAc,CAACrB,CAAC,EAAEI,CAAC,EAAER,MAAM,CAACsD,wBAAwB,CAAC/C,CAAC,EAAEC,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOJ,CAAC;AAAE;AAC5b,IAAIuD,OAAO,GAAG;EACZC,IAAI,EAAE,SAASA,IAAIA,CAACC,IAAI,EAAE;IACxB,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;MACpBC,YAAY,GAAGF,IAAI,CAACE,YAAY;MAChCC,mBAAmB,GAAGH,IAAI,CAACG,mBAAmB;MAC9CC,OAAO,GAAGJ,IAAI,CAACI,OAAO;IACxB,OAAO9E,UAAU,CAAC,uCAAuC,EAAE;MACzD,YAAY,EAAE2E,KAAK,CAACI,QAAQ;MAC5B,WAAW,EAAEJ,KAAK,CAACK,OAAO;MAC1B,SAAS,EAAEJ,YAAY;MACvB,kBAAkB,EAAED,KAAK,CAACM,OAAO,GAAGN,KAAK,CAACM,OAAO,KAAK,QAAQ,GAAGH,OAAO,IAAIA,OAAO,CAACI,UAAU,KAAK,QAAQ;MAC3G,sBAAsB,EAAEP,KAAK,CAACQ,SAAS,IAAI,CAACR,KAAK,CAACI,QAAQ;MAC1D,uBAAuB,EAAE9E,WAAW,CAACmF,UAAU,CAACT,KAAK,CAACpC,KAAK,CAAC;MAC5D,sBAAsB,EAAEqC,YAAY,IAAIC;IAC1C,CAAC,CAAC;EACJ,CAAC;EACDQ,KAAK,EAAE,SAASA,KAAKA,CAACC,KAAK,EAAE;IAC3B,IAAIX,KAAK,GAAGW,KAAK,CAACX,KAAK;MACrBY,KAAK,GAAGD,KAAK,CAACC,KAAK;IACrB,OAAOZ,KAAK,CAACa,QAAQ,GAAG,8BAA8B,GAAGxF,UAAU,CAAC,8BAA8B,EAAE;MAClG,eAAe,EAAEuF,KAAK,KAAK,IAAI,IAAIZ,KAAK,CAACc,WAAW;MACpD,wBAAwB,EAAEF,KAAK,KAAK,IAAI,IAAI,CAACZ,KAAK,CAACc;IACrD,CAAC,CAAC;EACJ,CAAC;EACDC,OAAO,EAAE,oBAAoB;EAC7BC,YAAY,EAAE,0BAA0B;EACxCC,SAAS,EAAE,SAASA,SAASA,CAACC,KAAK,EAAE;IACnC,IAAIC,gBAAgB,GAAGD,KAAK,CAACC,gBAAgB;IAC7C,OAAO9F,UAAU,CAAC,uBAAuB,EAAE;MACzC,uBAAuB,EAAE,CAAC8F,gBAAgB,IAAIA,gBAAgB,CAAC3E,MAAM,KAAK;IAC5E,CAAC,CAAC;EACJ,CAAC;EACD4E,cAAc,EAAE,6BAA6B;EAC7CC,YAAY,EAAE,qCAAqC;EACnDC,WAAW,EAAE,qCAAqC;EAClDC,SAAS,EAAE,mCAAmC;EAC9CC,UAAU,EAAE,wBAAwB;EACpCC,eAAe,EAAE,8BAA8B;EAC/CC,eAAe,EAAE,SAASA,eAAeA,CAACC,KAAK,EAAE;IAC/C,IAAIJ,SAAS,GAAGI,KAAK,CAACJ,SAAS;IAC/B,OAAOlG,UAAU,CAAC,6BAA6B,EAAE;MAC/C,6BAA6B,EAAE,CAAC,CAACkG;IACnC,CAAC,CAAC;EACJ,CAAC;EACDK,WAAW,EAAE,SAASA,WAAWA,CAACC,KAAK,EAAE;IACvC,IAAI7B,KAAK,GAAG6B,KAAK,CAAC7B,KAAK;MACrBG,OAAO,GAAG0B,KAAK,CAAC1B,OAAO;IACzB,OAAO9E,UAAU,CAAC,2CAA2C,EAAE;MAC7D,kBAAkB,EAAE2E,KAAK,CAACM,OAAO,GAAGN,KAAK,CAACM,OAAO,KAAK,QAAQ,GAAGH,OAAO,IAAIA,OAAO,CAACI,UAAU,KAAK;IACrG,CAAC,CAAC;EACJ,CAAC;EACDuB,IAAI,EAAE,SAASA,IAAIA,CAACC,KAAK,EAAE;IACzB,IAAIC,sBAAsB,GAAGD,KAAK,CAACC,sBAAsB;IACzD,OAAOA,sBAAsB,GAAG,kBAAkB,GAAG,kBAAkB;EACzE,CAAC;EACDC,KAAK,EAAE,SAASA,KAAKA,CAACC,KAAK,EAAE;IAC3B,IAAI/B,OAAO,GAAG+B,KAAK,CAAC/B,OAAO;IAC3B,OAAO9E,UAAU,CAAC,8BAA8B,EAAE;MAChD,gBAAgB,EAAE8E,OAAO,IAAIA,OAAO,CAACI,UAAU,KAAK,QAAQ,IAAIrG,UAAU,CAACqG,UAAU,KAAK,QAAQ;MAClG,mBAAmB,EAAEJ,OAAO,IAAIA,OAAO,CAACgC,MAAM,KAAK,KAAK,IAAIjI,UAAU,CAACiI,MAAM,KAAK;IACpF,CAAC,CAAC;EACJ,CAAC;EACDC,IAAI,EAAE,SAASA,IAAIA,CAACC,KAAK,EAAE;IACzB,IAAIC,QAAQ,GAAGD,KAAK,CAACC,QAAQ;MAC3BlC,QAAQ,GAAGiC,KAAK,CAACjC,QAAQ;MACzBQ,KAAK,GAAGyB,KAAK,CAACzB,KAAK;MACnB2B,KAAK,GAAGF,KAAK,CAACE,KAAK;MACnBC,kBAAkB,GAAGH,KAAK,CAACG,kBAAkB;MAC7CC,iBAAiB,GAAGJ,KAAK,CAACI,iBAAiB;IAC7C,OAAOpH,UAAU,CAAC,iBAAiB,EAAE;MACnC,aAAa,EAAEiH,QAAQ,IAAIG,iBAAiB;MAC5C,YAAY,EAAErC,QAAQ;MACtB,SAAS,EAAEmC,KAAK,KAAKC,kBAAkB;MACvC,uBAAuB,EAAE,CAAC5B,KAAK,IAAIA,KAAK,CAACpE,MAAM,KAAK;IACtD,CAAC,CAAC;EACJ,CAAC;EACDkG,SAAS,EAAE,uBAAuB;EAClCC,SAAS,EAAE,uBAAuB;EAClCC,SAAS,EAAE,uBAAuB;EAClCC,OAAO,EAAE,0BAA0B;EACnCC,MAAM,EAAE,mBAAmB;EAC3BC,MAAM,EAAE,mBAAmB;EAC3BC,UAAU,EAAE;AACd,CAAC;AACD,IAAIC,MAAM,GAAG,ktDAAktD;AAC/tD,IAAIC,YAAY,GAAG;EACjBL,OAAO,EAAE,SAASA,OAAOA,CAACM,KAAK,EAAE;IAC/B,IAAInD,KAAK,GAAGmD,KAAK,CAACnD,KAAK;IACvB,OAAO;MACLoD,SAAS,EAAEpD,KAAK,CAACqD,YAAY,IAAI;IACnC,CAAC;EACH,CAAC;EACDpB,KAAK,EAAE,SAASA,KAAKA,CAACqB,MAAM,EAAE;IAC5B,IAAItD,KAAK,GAAGsD,MAAM,CAACtD,KAAK;IACxB,OAAOP,eAAe,CAAC,CAAC,CAAC,EAAEO,KAAK,CAACuD,UAAU,CAAC;EAC9C;AACF,CAAC;AACD,IAAIC,YAAY,GAAGjJ,aAAa,CAACkJ,MAAM,CAAC;EACtCC,YAAY,EAAE;IACZC,MAAM,EAAE,UAAU;IAClBC,gBAAgB,EAAE,IAAI;IACtBC,QAAQ,EAAE,IAAI;IACdzJ,SAAS,EAAE,IAAI;IACf0J,cAAc,EAAE,IAAI;IACpBC,SAAS,EAAE,KAAK;IAChBC,eAAe,EAAE,KAAK;IACtBC,SAAS,EAAE,KAAK;IAChBC,QAAQ,EAAEC,SAAS;IACnBC,SAAS,EAAE,IAAI;IACf7C,SAAS,EAAE,IAAI;IACf8C,YAAY,EAAE,IAAI;IAClBC,OAAO,EAAE,IAAI;IACblE,QAAQ,EAAE,KAAK;IACfiB,YAAY,EAAE,IAAI;IAClBR,QAAQ,EAAE,KAAK;IACf0D,kBAAkB,EAAE,IAAI;IACxBvD,YAAY,EAAE,IAAI;IAClBzB,MAAM,EAAE,KAAK;IACbiF,QAAQ,EAAE,IAAI;IACd/C,eAAe,EAAE,IAAI;IACrBgD,WAAW,EAAE,GAAG;IAChBjD,UAAU,EAAE,IAAI;IAChBkD,oBAAoB,EAAE,KAAK;IAC3BC,YAAY,EAAER,SAAS;IACvBS,eAAe,EAAE,UAAU;IAC3BC,iBAAiB,EAAE,IAAI;IACvBC,cAAc,EAAE,IAAI;IACpBC,aAAa,EAAE,IAAI;IACnBC,YAAY,EAAE,IAAI;IAClBvC,iBAAiB,EAAE,IAAI;IACvBwC,EAAE,EAAE,IAAI;IACRC,OAAO,EAAE,IAAI;IACbC,QAAQ,EAAE,IAAI;IACd9E,OAAO,EAAE,KAAK;IACd+E,YAAY,EAAE,IAAI;IAClBC,OAAO,EAAE,KAAK;IACd/D,WAAW,EAAE,IAAI;IACjBgE,SAAS,EAAE,IAAI;IACfvG,IAAI,EAAE,IAAI;IACVwG,MAAM,EAAE,IAAI;IACZC,QAAQ,EAAE,IAAI;IACdC,OAAO,EAAE,IAAI;IACbC,aAAa,EAAE,IAAI;IACnBC,QAAQ,EAAE,IAAI;IACdC,OAAO,EAAE,IAAI;IACbC,MAAM,EAAE,IAAI;IACZC,WAAW,EAAE,IAAI;IACjBC,MAAM,EAAE,IAAI;IACZC,cAAc,EAAE,IAAI;IACpBC,mBAAmB,EAAE,OAAO;IAC5B9E,gBAAgB,EAAE,IAAI;IACtB+E,mBAAmB,EAAE,IAAI;IACzBC,WAAW,EAAE,IAAI;IACjBC,OAAO,EAAE,IAAI;IACbC,WAAW,EAAE,IAAI;IACjBC,cAAc,EAAE,IAAI;IACpBC,mBAAmB,EAAE,IAAI;IACzBhD,UAAU,EAAE,IAAI;IAChBzC,WAAW,EAAE,IAAI;IACjB0F,QAAQ,EAAE,KAAK;IACfC,iBAAiB,EAAE,KAAK;IACxBpD,YAAY,EAAE,OAAO;IACrBqD,aAAa,EAAE,KAAK;IACpBlG,SAAS,EAAE,KAAK;IAChBmG,eAAe,EAAE,KAAK;IACtBC,WAAW,EAAE,KAAK;IAClBC,KAAK,EAAE,IAAI;IACXC,QAAQ,EAAE,IAAI;IACdC,OAAO,EAAE,IAAI;IACbC,cAAc,EAAE,IAAI;IACpBC,iBAAiB,EAAE,IAAI;IACvBC,gBAAgB,EAAE,KAAK;IACvBtJ,KAAK,EAAE,IAAI;IACXuJ,aAAa,EAAE,IAAI;IACnB7G,OAAO,EAAE,IAAI;IACb0B,sBAAsB,EAAE;EAC1B,CAAC;EACDoF,GAAG,EAAE;IACHvH,OAAO,EAAEA,OAAO;IAChBoD,MAAM,EAAEA,MAAM;IACdC,YAAY,EAAEA;EAChB;AACF,CAAC,CAAC;AAEF,IAAImE,SAAS,GAAG,aAAapN,KAAK,CAACqN,IAAI,CAAC,aAAarN,KAAK,CAACsN,UAAU,CAAC,UAAUC,OAAO,EAAEC,GAAG,EAAE;EAC5F,IAAIC,GAAG,GAAG1L,QAAQ,CAAC2L,MAAM,CAACH,OAAO,CAAC;EAClC,OAAO,aAAavN,KAAK,CAAC2N,aAAa,CAAC,KAAK,EAAE3L,QAAQ,CAAC;IACtDwL,GAAG,EAAEA,GAAG;IACRI,KAAK,EAAE,IAAI;IACXC,MAAM,EAAE,IAAI;IACZC,OAAO,EAAE,WAAW;IACpBC,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE;EACT,CAAC,EAAEP,GAAG,CAAC,EAAE,aAAazN,KAAK,CAAC2N,aAAa,CAAC,MAAM,EAAE;IAChDC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAE,GAAG;IACXE,IAAI,EAAE,cAAc;IACpBE,WAAW,EAAE;EACf,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AACHb,SAAS,CAACc,WAAW,GAAG,WAAW;AAEnC,IAAIC,YAAY,GAAG,aAAanO,KAAK,CAACqN,IAAI,CAAC,UAAUtH,KAAK,EAAE;EAC1D,IAAIqI,UAAU,GAAG5N,aAAa,CAAC,CAAC;EAChC,IAAI6N,GAAG,GAAGtI,KAAK,CAACsI,GAAG;IACjBC,EAAE,GAAGvI,KAAK,CAACuI,EAAE;IACbjG,QAAQ,GAAGtC,KAAK,CAACsC,QAAQ;IACzBlC,QAAQ,GAAGJ,KAAK,CAACI,QAAQ;IACzBoI,MAAM,GAAGxI,KAAK,CAACwI,MAAM;IACrB5H,KAAK,GAAGZ,KAAK,CAACY,KAAK;IACnB2B,KAAK,GAAGvC,KAAK,CAACuC,KAAK;IACnBC,kBAAkB,GAAGxC,KAAK,CAACwC,kBAAkB;IAC7CiG,WAAW,GAAGzI,KAAK,CAACyI,WAAW;IAC/BxE,SAAS,GAAGjE,KAAK,CAACiE,SAAS;IAC3BxB,iBAAiB,GAAGzC,KAAK,CAACyC,iBAAiB;IAC3CiG,cAAc,GAAG1I,KAAK,CAAC0I,cAAc;EACvC,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAACC,GAAG,EAAE;IAC5C,OAAON,GAAG,CAACM,GAAG,EAAE;MACdzI,OAAO,EAAE;QACPmC,QAAQ,EAAEA,QAAQ;QAClBlC,QAAQ,EAAEA,QAAQ;QAClByI,OAAO,EAAEtG,KAAK,KAAKC;MACrB;IACF,CAAC,CAAC;EACJ,CAAC;EACD,IAAIsG,QAAQ,GAAG,SAASrD,OAAOA,CAACsD,KAAK,EAAE1L,CAAC,EAAE;IACxC,IAAI2C,KAAK,CAACyF,OAAO,EAAE;MACjBzF,KAAK,CAACyF,OAAO,CAAC;QACZuD,aAAa,EAAED,KAAK;QACpBP,MAAM,EAAEA;MACV,CAAC,CAAC;IACJ;EACF,CAAC;EACD,IAAIS,OAAO,GAAGjJ,KAAK,CAACkJ,QAAQ,GAAG5N,WAAW,CAAC6N,aAAa,CAACnJ,KAAK,CAACkJ,QAAQ,EAAElJ,KAAK,CAACwI,MAAM,CAAC,GAAGxI,KAAK,CAACY,KAAK;EACpG,IAAIwI,SAAS,GAAGf,UAAU,CAAC;IACzBpD,EAAE,EAAE,eAAe,CAACoE,MAAM,CAAC9G,KAAK,CAAC;IACjC+G,IAAI,EAAE,QAAQ;IACdlF,SAAS,EAAE/I,UAAU,CAACmN,MAAM,CAACpE,SAAS,EAAEmE,EAAE,CAAC,MAAM,EAAE;MACjDjG,QAAQ,EAAEA,QAAQ;MAClBlC,QAAQ,EAAEA,QAAQ;MAClBQ,KAAK,EAAEA,KAAK;MACZ2B,KAAK,EAAEA,KAAK;MACZC,kBAAkB,EAAEA,kBAAkB;MACtCC,iBAAiB,EAAEA;IACrB,CAAC,CAAC,CAAC;IACHoE,KAAK,EAAE7G,KAAK,CAAC6G,KAAK;IAClBC,QAAQ,EAAE,CAAC;IACXrB,OAAO,EAAE,SAASA,OAAOA,CAACnJ,CAAC,EAAE;MAC3B,OAAOwM,QAAQ,CAACxM,CAAC,CAAC;IACpB,CAAC;IACDiN,SAAS,EAAE,SAASA,SAASA,CAACjN,CAAC,EAAE;MAC/B,OAAOoM,cAAc,CAACpM,CAAC,CAAC;IAC1B,CAAC;IACDkN,WAAW,EAAE,SAASA,WAAWA,CAAClN,CAAC,EAAE;MACnC,OAAO0D,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACwJ,WAAW,CAAClN,CAAC,EAAEiG,KAAK,CAAC;IAClF,CAAC;IACD,cAAc,EAAEkG,WAAW;IAC3B,eAAe,EAAElG,KAAK,GAAG,CAAC;IAC1B,YAAY,EAAE3B,KAAK;IACnB,eAAe,EAAE0B,QAAQ;IACzB,kBAAkB,EAAEA,QAAQ;IAC5B,gBAAgB,EAAEE,kBAAkB,KAAKD,KAAK;IAC9C,iBAAiB,EAAEnC;EACrB,CAAC,EAAEuI,YAAY,CAAC,MAAM,CAAC,CAAC;EACxB,IAAIc,mBAAmB,GAAGpB,UAAU,CAAC;IACnCjE,SAAS,EAAEmE,EAAE,CAAC,WAAW;EAC3B,CAAC,EAAEI,YAAY,CAAC,WAAW,CAAC,CAAC;EAC7B,IAAIe,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAG;IACzC,IAAIpH,QAAQ,EAAE;MACZ,IAAIqH,cAAc,GAAGtB,UAAU,CAAC;QAC9BjE,SAAS,EAAEmE,EAAE,CAAC,WAAW;MAC3B,CAAC,EAAEI,YAAY,CAAC,WAAW,CAAC,CAAC;MAC7B,OAAO,aAAa1O,KAAK,CAAC2N,aAAa,CAAC7L,SAAS,EAAE4N,cAAc,CAAC;IACpE;IACA,IAAIC,cAAc,GAAGvB,UAAU,CAAC;MAC9BjE,SAAS,EAAEmE,EAAE,CAAC,WAAW;IAC3B,CAAC,EAAEI,YAAY,CAAC,WAAW,CAAC,CAAC;IAC7B,OAAO,aAAa1O,KAAK,CAAC2N,aAAa,CAACP,SAAS,EAAEuC,cAAc,CAAC;EACpE,CAAC;EACD,OAAO,aAAa3P,KAAK,CAAC2N,aAAa,CAAC,IAAI,EAAE3L,QAAQ,CAAC;IACrD2M,GAAG,EAAE5I,KAAK,CAACY;EACb,CAAC,EAAEwI,SAAS,CAAC,EAAEnF,SAAS,IAAIyF,YAAY,CAAC,CAAC,EAAE,aAAazP,KAAK,CAAC2N,aAAa,CAAC,MAAM,EAAE6B,mBAAmB,EAAER,OAAO,CAAC,EAAE,aAAahP,KAAK,CAAC2N,aAAa,CAAC9L,MAAM,EAAE,IAAI,CAAC,CAAC;AACrK,CAAC,CAAC;AACFsM,YAAY,CAACD,WAAW,GAAG,cAAc;AAEzC,SAAS0B,SAASA,CAACvN,CAAC,EAAEI,CAAC,EAAE;EAAE,IAAID,CAAC,GAAGP,MAAM,CAACmD,IAAI,CAAC/C,CAAC,CAAC;EAAE,IAAIJ,MAAM,CAACoD,qBAAqB,EAAE;IAAE,IAAIvC,CAAC,GAAGb,MAAM,CAACoD,qBAAqB,CAAChD,CAAC,CAAC;IAAEI,CAAC,KAAKK,CAAC,GAAGA,CAAC,CAACwC,MAAM,CAAC,UAAU7C,CAAC,EAAE;MAAE,OAAOR,MAAM,CAACsD,wBAAwB,CAAClD,CAAC,EAAEI,CAAC,CAAC,CAACmB,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEpB,CAAC,CAACiC,IAAI,CAAC7B,KAAK,CAACJ,CAAC,EAAEM,CAAC,CAAC;EAAE;EAAE,OAAON,CAAC;AAAE;AAChQ,SAASqN,eAAeA,CAACxN,CAAC,EAAE;EAAE,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,SAAS,CAACC,MAAM,EAAEE,CAAC,EAAE,EAAE;IAAE,IAAID,CAAC,GAAG,IAAI,IAAIF,SAAS,CAACG,CAAC,CAAC,GAAGH,SAAS,CAACG,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGmN,SAAS,CAAC3N,MAAM,CAACO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACiD,OAAO,CAAC,UAAUhD,CAAC,EAAE;MAAEgB,eAAe,CAACpB,CAAC,EAAEI,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGR,MAAM,CAACyD,yBAAyB,GAAGzD,MAAM,CAAC0D,gBAAgB,CAACtD,CAAC,EAAEJ,MAAM,CAACyD,yBAAyB,CAAClD,CAAC,CAAC,CAAC,GAAGoN,SAAS,CAAC3N,MAAM,CAACO,CAAC,CAAC,CAAC,CAACiD,OAAO,CAAC,UAAUhD,CAAC,EAAE;MAAER,MAAM,CAACyB,cAAc,CAACrB,CAAC,EAAEI,CAAC,EAAER,MAAM,CAACsD,wBAAwB,CAAC/C,CAAC,EAAEC,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOJ,CAAC;AAAE;AAC5b,IAAIyN,aAAa,GAAG,aAAa9P,KAAK,CAACqN,IAAI,CAAC,aAAarN,KAAK,CAACsN,UAAU,CAAC,UAAUvH,KAAK,EAAEyH,GAAG,EAAE;EAC9F,IAAIY,UAAU,GAAG5N,aAAa,CAAC,CAAC;EAChC,IAAI6N,GAAG,GAAGtI,KAAK,CAACsI,GAAG;IACjBC,EAAE,GAAGvI,KAAK,CAACuI,EAAE;IACbyB,EAAE,GAAGhK,KAAK,CAACgK,EAAE;EACf,IAAI7J,OAAO,GAAGlG,KAAK,CAACgQ,UAAU,CAAC9P,iBAAiB,CAAC;EACjD,IAAI+P,cAAc,GAAGjQ,KAAK,CAACkQ,MAAM,CAAC,IAAI,CAAC;EACvC,IAAIC,aAAa,GAAG,EAAEpK,KAAK,CAACqK,cAAc,IAAIrK,KAAK,CAACqK,cAAc,CAAC7N,MAAM,CAAC,IAAIwD,KAAK,CAACsK,SAAS;EAC7F,IAAI7B,WAAW,GAAGzI,KAAK,CAACqK,cAAc,GAAGrK,KAAK,CAACqK,cAAc,CAAC7N,MAAM,GAAG,CAAC;EACxE,IAAI+N,aAAa,GAAG;IAClBhL,MAAM,EAAE,SAASA,MAAMA,CAACjD,CAAC,EAAE;MACzB,OAAOkO,mBAAmB,CAAClO,CAAC,CAAC;IAC/B,CAAC;IACDmO,KAAK,EAAE,SAASA,KAAKA,CAAA,EAAG;MACtB,OAAOzK,KAAK,CAAC0K,WAAW,CAAC,CAAC;IAC5B;EACF,CAAC;EACD,IAAI/B,YAAY,GAAG,SAASA,YAAYA,CAACC,GAAG,EAAExC,OAAO,EAAE;IACrD,OAAOkC,GAAG,CAACM,GAAG,EAAEkB,eAAe,CAAC;MAC9Ba,QAAQ,EAAE3K,KAAK,CAAC2K;IAClB,CAAC,EAAEvE,OAAO,CAAC,CAAC;EACd,CAAC;EACD,IAAIwE,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAG;IAC/B5K,KAAK,CAAC4K,OAAO,CAAC,YAAY;MACxB,IAAI5K,KAAK,CAAC6K,kBAAkB,CAACC,OAAO,EAAE;QACpC,IAAIC,aAAa,GAAG/K,KAAK,CAACgL,sBAAsB,CAAC,CAAC;QAClD,IAAID,aAAa,KAAK,CAAC,CAAC,EAAE;UACxBE,UAAU,CAAC,YAAY;YACrB,OAAOjL,KAAK,CAAC6K,kBAAkB,CAACC,OAAO,CAACI,aAAa,CAACH,aAAa,CAAC;UACtE,CAAC,EAAE,CAAC,CAAC;QACP;MACF;IACF,CAAC,CAAC;EACJ,CAAC;EACD,IAAII,SAAS,GAAG,SAASA,SAASA,CAAA,EAAG;IACnCnL,KAAK,CAACmL,SAAS,CAAC,YAAY;MAC1B,IAAInL,KAAK,CAACT,MAAM,IAAIS,KAAK,CAAC0E,oBAAoB,EAAE;QAC9CnJ,UAAU,CAAC6P,KAAK,CAAClB,cAAc,CAACY,OAAO,EAAE,KAAK,CAAC;MACjD;IACF,CAAC,CAAC;EACJ,CAAC;EACD,IAAIN,mBAAmB,GAAG,SAASA,mBAAmBA,CAACzB,KAAK,EAAE;IAC5D/I,KAAK,CAACwK,mBAAmB,IAAIxK,KAAK,CAACwK,mBAAmB,CAACzB,KAAK,CAAC;EAC/D,CAAC;EACD,IAAIsC,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAG;IACzC,IAAIrL,KAAK,CAACuG,mBAAmB,EAAE;MAC7B,IAAI0C,OAAO,GAAG3N,WAAW,CAAC6N,aAAa,CAACnJ,KAAK,CAACuG,mBAAmB,EAAEvG,KAAK,EAAEA,KAAK,CAACsL,aAAa,CAAC;MAC9F,IAAIC,WAAW,GAAGlD,UAAU,CAAC;QAC3BjE,SAAS,EAAEmE,EAAE,CAAC,QAAQ;MACxB,CAAC,EAAEI,YAAY,CAAC,QAAQ,CAAC,CAAC;MAC1B,OAAO,aAAa1O,KAAK,CAAC2N,aAAa,CAAC,KAAK,EAAE2D,WAAW,EAAEtC,OAAO,CAAC;IACtE;IACA,OAAO,IAAI;EACb,CAAC;EACD,IAAIuC,wBAAwB,GAAG,SAASA,wBAAwBA,CAACzC,KAAK,EAAExG,KAAK,EAAE;IAC7E,IAAIvC,KAAK,CAACgF,YAAY,EAAE;MACtB,IAAIyG,qBAAqB;MACzBzL,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,IAAI,CAACyL,qBAAqB,GAAGzL,KAAK,CAAC0L,wBAAwB,MAAM,IAAI,IAAID,qBAAqB,KAAK,KAAK,CAAC,IAAIA,qBAAqB,CAAC7O,IAAI,CAACoD,KAAK,EAAE+I,KAAK,EAAExG,KAAK,CAAC;IAChM;EACF,CAAC;EACD,IAAIoJ,kBAAkB,GAAG,SAASA,kBAAkBA,CAAC3K,YAAY,EAAE4K,QAAQ,EAAE;IAC3E,IAAIC,OAAO,GAAGvQ,WAAW,CAAC6N,aAAa,CAACnI,YAAY,EAAEhB,KAAK,CAAC,IAAI3F,YAAY,CAACuR,QAAQ,GAAG,oBAAoB,GAAG,cAAc,CAAC;IAC9H,IAAIE,iBAAiB,GAAGzD,UAAU,CAAC;MACjCjE,SAAS,EAAEmE,EAAE,CAAC,cAAc;IAC9B,CAAC,EAAEI,YAAY,CAAC,cAAc,CAAC,CAAC;IAChC,OAAO,aAAa1O,KAAK,CAAC2N,aAAa,CAAC,IAAI,EAAEkE,iBAAiB,EAAED,OAAO,CAAC;EAC3E,CAAC;EACD,IAAIE,UAAU,GAAG,SAASA,UAAUA,CAACvD,MAAM,EAAEjG,KAAK,EAAE;IAClD,IAAIyJ,eAAe,GAAGzP,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAK4H,SAAS,GAAG5H,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAC5F,IAAIsK,KAAK,GAAG;MACViB,MAAM,EAAEkE,eAAe,CAAChM,KAAK,GAAGgM,eAAe,CAAChM,KAAK,CAACiM,QAAQ,GAAG9H;IACnE,CAAC;IACD0C,KAAK,GAAGiD,eAAe,CAACA,eAAe,CAAC,CAAC,CAAC,EAAEjD,KAAK,CAAC,EAAE2B,MAAM,CAAC3B,KAAK,CAAC;IACjE,IAAI2B,MAAM,CAAC0D,KAAK,IAAIlM,KAAK,CAACmB,gBAAgB,EAAE;MAC1C,IAAIA,gBAAgB,GAAGnB,KAAK,CAACmB,gBAAgB;MAC7C,IAAIgL,YAAY,GAAGnM,KAAK,CAACkG,mBAAmB,GAAG5K,WAAW,CAAC6N,aAAa,CAACnJ,KAAK,CAACkG,mBAAmB,EAAEsC,MAAM,EAAEjG,KAAK,CAAC,GAAGvC,KAAK,CAACoM,mBAAmB,CAAC5D,MAAM,CAAC;MACtJ,IAAII,GAAG,GAAGrG,KAAK,GAAG,GAAG,GAAGvC,KAAK,CAACqM,uBAAuB,CAAC7D,MAAM,CAAC;MAC7D,IAAI8D,cAAc,GAAGjE,UAAU,CAAC;QAC9BjE,SAAS,EAAEmE,EAAE,CAAC,WAAW,EAAE;UACzBpH,gBAAgB,EAAEA;QACpB,CAAC,CAAC;QACF0F,KAAK,EAAEA,KAAK;QACZ,kBAAkB,EAAE7G,KAAK,CAACsC;MAC5B,CAAC,EAAEqG,YAAY,CAAC,WAAW,CAAC,CAAC;MAC7B,IAAIc,mBAAmB,GAAGpB,UAAU,CAAC;QACnCjE,SAAS,EAAEmE,EAAE,CAAC,gBAAgB;MAChC,CAAC,EAAEI,YAAY,CAAC,gBAAgB,CAAC,CAAC;MAClC,OAAO,aAAa1O,KAAK,CAAC2N,aAAa,CAAC,IAAI,EAAE3L,QAAQ,CAAC;QACrD2M,GAAG,EAAEA;MACP,CAAC,EAAE0D,cAAc,CAAC,EAAE,aAAarS,KAAK,CAAC2N,aAAa,CAAC,MAAM,EAAE6B,mBAAmB,EAAE0C,YAAY,CAAC,CAAC;IAClG;IACA,IAAII,SAAS,GAAGvM,KAAK,CAACwM,kBAAkB,CAAChE,MAAM,CAAC,GAAG,GAAG,GAAGjG,KAAK;IAC9D,IAAI4D,WAAW,GAAGnG,KAAK,CAACyM,cAAc,CAACjE,MAAM,CAAC;IAC9C,IAAIpI,QAAQ,GAAGJ,KAAK,CAAC0M,gBAAgB,CAAClE,MAAM,CAAC;IAC7C,OAAO,aAAavO,KAAK,CAAC2N,aAAa,CAACQ,YAAY,EAAE;MACpDQ,GAAG,EAAE2D,SAAS;MACd3L,KAAK,EAAEuF,WAAW;MAClB5D,KAAK,EAAEA,KAAK;MACZC,kBAAkB,EAAExC,KAAK,CAACwC,kBAAkB;MAC5CgG,MAAM,EAAEA,MAAM;MACdC,WAAW,EAAEA,WAAW;MACxBC,cAAc,EAAE1I,KAAK,CAAC0I,cAAc;MACpC7B,KAAK,EAAEA,KAAK;MACZqC,QAAQ,EAAElJ,KAAK,CAACoF,YAAY;MAC5B9C,QAAQ,EAAEtC,KAAK,CAAC2M,UAAU,CAACnE,MAAM,CAAC;MAClC/F,iBAAiB,EAAEzC,KAAK,CAACyC,iBAAiB;MAC1CrC,QAAQ,EAAEA,QAAQ;MAClBqF,OAAO,EAAEzF,KAAK,CAAC4M,aAAa;MAC5BpD,WAAW,EAAEgC,wBAAwB;MACrClD,GAAG,EAAEA,GAAG;MACRC,EAAE,EAAEA,EAAE;MACNtE,SAAS,EAAEjE,KAAK,CAACiE;IACnB,CAAC,CAAC;EACJ,CAAC;EACD,IAAI4I,WAAW,GAAG,SAASA,WAAWA,CAAA,EAAG;IACvC,IAAIvR,WAAW,CAACmF,UAAU,CAACT,KAAK,CAACqK,cAAc,CAAC,EAAE;MAChD,OAAOrK,KAAK,CAACqK,cAAc,CAACyC,GAAG,CAACf,UAAU,CAAC;IAC7C,CAAC,MAAM,IAAI/L,KAAK,CAACsK,SAAS,EAAE;MAC1B,OAAOqB,kBAAkB,CAAC3L,KAAK,CAACuE,kBAAkB,EAAE,IAAI,CAAC;IAC3D;IACA,OAAOoH,kBAAkB,CAAC3L,KAAK,CAACgB,YAAY,CAAC;EAC/C,CAAC;EACD,IAAI+L,qBAAqB,GAAG,SAASA,qBAAqBA,CAAA,EAAG;IAC3D,IAAI/M,KAAK,CAAC2G,eAAe,IAAI3G,KAAK,CAACgN,WAAW,EAAE;MAC9C,IAAIC,oBAAoB,GAAG5S,YAAY,CAAC,OAAO,CAAC;MAChD,IAAI6S,cAAc,GAAG7E,UAAU,CAAC;QAC9BjE,SAAS,EAAEmE,EAAE,CAAC,iBAAiB,CAAC;QAChC,YAAY,EAAE0E,oBAAoB;QAClCxH,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;UAC1B,OAAOzF,KAAK,CAACmN,sBAAsB,CAAC,YAAY;YAC9C,OAAO5R,UAAU,CAAC6P,KAAK,CAAClB,cAAc,CAACY,OAAO,CAAC;UACjD,CAAC,CAAC;QACJ;MACF,CAAC,EAAEnC,YAAY,CAAC,iBAAiB,CAAC,CAAC;MACnC,IAAIyE,IAAI,GAAGpN,KAAK,CAACyB,eAAe,IAAI,aAAaxH,KAAK,CAAC2N,aAAa,CAAC1M,SAAS,EAAEgS,cAAc,CAAC;MAC/F,IAAIzL,eAAe,GAAGjG,SAAS,CAAC6R,UAAU,CAACD,IAAI,EAAEtD,eAAe,CAAC,CAAC,CAAC,EAAEoD,cAAc,CAAC,EAAE;QACpFlN,KAAK,EAAEA;MACT,CAAC,CAAC;MACF,OAAOyB,eAAe;IACxB;IACA,OAAO,IAAI;EACb,CAAC;EACD,IAAI6L,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAG;IACzC,IAAItN,KAAK,CAACT,MAAM,EAAE;MAChB,IAAIgC,SAAS,GAAGwL,qBAAqB,CAAC,CAAC;MACvC,IAAIQ,eAAe,GAAGlF,UAAU,CAAC;QAC/BjE,SAAS,EAAEmE,EAAE,CAAC,YAAY;MAC5B,CAAC,EAAEI,YAAY,CAAC,YAAY,CAAC,CAAC;MAC9B,IAAIyE,IAAI,GAAGpN,KAAK,CAACwB,UAAU,IAAI,aAAavH,KAAK,CAAC2N,aAAa,CAACjM,UAAU,EAAE4R,eAAe,CAAC;MAC5F,IAAI/L,UAAU,GAAGhG,SAAS,CAAC6R,UAAU,CAACD,IAAI,EAAEtD,eAAe,CAAC,CAAC,CAAC,EAAEyD,eAAe,CAAC,EAAE;QAChFvN,KAAK,EAAEA;MACT,CAAC,CAAC;MACF,IAAIwN,oBAAoB,GAAGnF,UAAU,CAAC;QACpCjE,SAAS,EAAEmE,EAAE,CAAC,iBAAiB,EAAE;UAC/BhH,SAAS,EAAEA;QACb,CAAC;MACH,CAAC,EAAEoH,YAAY,CAAC,iBAAiB,CAAC,CAAC;MACnC,IAAI8E,gBAAgB,GAAGpF,UAAU,CAAC;QAChCZ,GAAG,EAAEyC,cAAc;QACnBwD,IAAI,EAAE,MAAM;QACZC,YAAY,EAAE,KAAK;QACnBvJ,SAAS,EAAEmE,EAAE,CAAC,aAAa,EAAE;UAC3BpI,OAAO,EAAEA;QACX,CAAC,CAAC;QACFW,WAAW,EAAEd,KAAK,CAAC6E,iBAAiB;QACpC0E,SAAS,EAAEvJ,KAAK,CAAC4N,oBAAoB;QACrCpI,QAAQ,EAAE,SAASA,QAAQA,CAAClJ,CAAC,EAAE;UAC7B,OAAOkO,mBAAmB,CAAClO,CAAC,CAAC;QAC/B,CAAC;QACDsB,KAAK,EAAEoC,KAAK,CAACgN;MACf,CAAC,EAAErE,YAAY,CAAC,aAAa,CAAC,CAAC;MAC/B,IAAIM,OAAO,GAAG,aAAahP,KAAK,CAAC2N,aAAa,CAAC,KAAK,EAAE4F,oBAAoB,EAAE,aAAavT,KAAK,CAAC2N,aAAa,CAAC,OAAO,EAAE6F,gBAAgB,CAAC,EAAElM,SAAS,EAAEC,UAAU,CAAC;MAC/J,IAAIxB,KAAK,CAAC8E,cAAc,EAAE;QACxB,IAAI+I,qBAAqB,GAAG;UAC1BzJ,SAAS,EAAE/I,UAAU,CAAC,6BAA6B,EAAE;YACnD,6BAA6B,EAAE,CAAC,CAACkG;UACnC,CAAC,CAAC;UACFuM,OAAO,EAAE7E,OAAO;UAChBsB,aAAa,EAAEA,aAAa;UAC5BwD,kBAAkB,EAAE/N,KAAK,CAAC4N,oBAAoB;UAC9CI,iBAAiB,EAAExD,mBAAmB;UACtCyD,mBAAmB,EAAE,wBAAwB;UAC7C1M,SAAS,EAAEA,SAAS;UACpBvB,KAAK,EAAEA;QACT,CAAC;QACDiJ,OAAO,GAAG3N,WAAW,CAAC6N,aAAa,CAACnJ,KAAK,CAAC8E,cAAc,EAAE+I,qBAAqB,CAAC;MAClF;MACA,IAAIK,WAAW,GAAG7F,UAAU,CAAC;QAC3BjE,SAAS,EAAEmE,EAAE,CAAC,QAAQ;MACxB,CAAC,EAAEI,YAAY,CAAC,QAAQ,CAAC,CAAC;MAC1B,OAAO,aAAa1O,KAAK,CAAC2N,aAAa,CAAC,KAAK,EAAEsG,WAAW,EAAEjF,OAAO,CAAC;IACtE;IACA,OAAO,IAAI;EACb,CAAC;EACD,IAAIkF,aAAa,GAAG,SAASA,aAAaA,CAAA,EAAG;IAC3C,IAAInO,KAAK,CAACgC,sBAAsB,EAAE;MAChC,IAAIoM,oBAAoB,GAAGtE,eAAe,CAACA,eAAe,CAAC,CAAC,CAAC,EAAE9J,KAAK,CAACgC,sBAAsB,CAAC,EAAE;QAC5F6E,KAAK,EAAEiD,eAAe,CAACA,eAAe,CAAC,CAAC,CAAC,EAAE9J,KAAK,CAACgC,sBAAsB,CAAC6E,KAAK,CAAC,EAAE;UAC9EiB,MAAM,EAAE9H,KAAK,CAACqD;QAChB,CAAC,CAAC;QACFe,SAAS,EAAE/I,UAAU,CAAC,0BAA0B,EAAE2E,KAAK,CAACgC,sBAAsB,CAACoC,SAAS,CAAC;QACzFiK,KAAK,EAAErO,KAAK,CAACqK,cAAc;QAC3BiE,QAAQ,EAAE,IAAI;QACdC,UAAU,EAAE,SAASA,UAAUA,CAACxF,KAAK,EAAE;UACrC,OAAO/I,KAAK,CAACgC,sBAAsB,CAACuM,UAAU,CAACzE,eAAe,CAACA,eAAe,CAAC,CAAC,CAAC,EAAEf,KAAK,CAAC,EAAE;YACzFxJ,MAAM,EAAES,KAAK,CAACgN;UAChB,CAAC,CAAC,CAAC;QACL,CAAC;QACD5H,YAAY,EAAE,SAASA,YAAYA,CAAChD,IAAI,EAAEgE,OAAO,EAAE;UACjD,OAAOhE,IAAI,IAAI2J,UAAU,CAAC3J,IAAI,EAAEgE,OAAO,CAAC7D,KAAK,EAAE6D,OAAO,CAAC;QACzD,CAAC;QACDoI,eAAe,EAAE,SAASA,eAAeA,CAACpI,OAAO,EAAE;UACjD,IAAIpF,YAAY,GAAGhB,KAAK,CAACsK,SAAS,GAAGtK,KAAK,CAACuE,kBAAkB,GAAGvE,KAAK,CAACgB,YAAY;UAClF,IAAIiI,OAAO,GAAGmB,aAAa,GAAGuB,kBAAkB,CAAC3K,YAAY,CAAC,GAAGoF,OAAO,CAAClC,QAAQ;UACjF,IAAIuK,SAAS,GAAGpG,UAAU,CAAC;YACzBZ,GAAG,EAAErB,OAAO,CAACsI,UAAU;YACvB7H,KAAK,EAAET,OAAO,CAACS,KAAK;YACpBzC,SAAS,EAAE/I,UAAU,CAAC+K,OAAO,CAAChC,SAAS,EAAEmE,EAAE,CAAC,MAAM,EAAE;cAClD6F,oBAAoB,EAAEpO,KAAK,CAACgC;YAC9B,CAAC,CAAC,CAAC;YACHsH,IAAI,EAAE,SAAS;YACf,YAAY,EAAElP,SAAS,CAAC,WAAW;UACrC,CAAC,EAAEuO,YAAY,CAAC,MAAM,CAAC,CAAC;UACxB,OAAO,aAAa1O,KAAK,CAAC2N,aAAa,CAAC,IAAI,EAAE6G,SAAS,EAAExF,OAAO,CAAC;QACnE;MACF,CAAC,CAAC;MACF,OAAO,aAAahP,KAAK,CAAC2N,aAAa,CAAC/L,eAAe,EAAEI,QAAQ,CAAC;QAChEwL,GAAG,EAAEzH,KAAK,CAAC6K;MACb,CAAC,EAAEuD,oBAAoB,EAAE;QACvBO,EAAE,EAAErG,GAAG,CAAC,iBAAiB;MAC3B,CAAC,CAAC,CAAC;IACL;IACA,IAAI+F,KAAK,GAAGxB,WAAW,CAAC,CAAC;IACzB,IAAI+B,YAAY,GAAGvG,UAAU,CAAC;MAC5BjE,SAAS,EAAEmE,EAAE,CAAC,SAAS,CAAC;MACxB1B,KAAK,EAAEmD,EAAE,CAAC,SAAS;IACrB,CAAC,EAAErB,YAAY,CAAC,SAAS,CAAC,CAAC;IAC3B,IAAI8F,SAAS,GAAGpG,UAAU,CAAC;MACzBjE,SAAS,EAAEmE,EAAE,CAAC,MAAM,CAAC;MACrBe,IAAI,EAAE,SAAS;MACf,YAAY,EAAElP,SAAS,CAAC,WAAW;IACrC,CAAC,EAAEuO,YAAY,CAAC,MAAM,CAAC,CAAC;IACxB,OAAO,aAAa1O,KAAK,CAAC2N,aAAa,CAAC,KAAK,EAAEgH,YAAY,EAAE,aAAa3U,KAAK,CAAC2N,aAAa,CAAC,IAAI,EAAE6G,SAAS,EAAEJ,KAAK,CAAC,CAAC;EACxH,CAAC;EACD,IAAIzG,aAAa,GAAG,SAASA,aAAaA,CAAA,EAAG;IAC3C,IAAIrI,MAAM,GAAG+N,YAAY,CAAC,CAAC;IAC3B,IAAIrE,OAAO,GAAGkF,aAAa,CAAC,CAAC;IAC7B,IAAIpL,MAAM,GAAGsI,YAAY,CAAC,CAAC;IAC3B,IAAIwD,UAAU,GAAGxG,UAAU,CAAC;MAC1BjE,SAAS,EAAE/I,UAAU,CAAC2E,KAAK,CAACsG,cAAc,EAAEiC,EAAE,CAAC,OAAO,EAAE;QACtDpI,OAAO,EAAEA;MACX,CAAC,CAAC,CAAC;MACH0G,KAAK,EAAEmD,EAAE,CAAC,OAAO,CAAC;MAClBvE,OAAO,EAAEzF,KAAK,CAACyF;IACjB,CAAC,EAAEkD,YAAY,CAAC,OAAO,CAAC,CAAC;IACzB,IAAImG,eAAe,GAAGzG,UAAU,CAAC;MAC/BhN,UAAU,EAAEkN,EAAE,CAAC,YAAY,CAAC;MAC5B,IAAI,EAAEvI,KAAK,CAAC,IAAI,CAAC;MACjB+O,OAAO,EAAE;QACPC,KAAK,EAAE,GAAG;QACVC,IAAI,EAAE;MACR,CAAC;MACD7I,OAAO,EAAEpG,KAAK,CAACiH,iBAAiB;MAChCiI,aAAa,EAAE,IAAI;MACnBtE,OAAO,EAAEA,OAAO;MAChBO,SAAS,EAAEA,SAAS;MACpBgE,MAAM,EAAEnP,KAAK,CAACmP,MAAM;MACpBC,QAAQ,EAAEpP,KAAK,CAACoP;IAClB,CAAC,EAAEzG,YAAY,CAAC,YAAY,CAAC,CAAC;IAC9B,OAAO,aAAa1O,KAAK,CAAC2N,aAAa,CAAClM,aAAa,EAAEO,QAAQ,CAAC;MAC9DoT,OAAO,EAAE5H;IACX,CAAC,EAAEqH,eAAe,CAAC,EAAE,aAAa7U,KAAK,CAAC2N,aAAa,CAAC,KAAK,EAAE3L,QAAQ,CAAC;MACpEwL,GAAG,EAAEA;IACP,CAAC,EAAEoH,UAAU,CAAC,EAAE7O,KAAK,CAACsP,qBAAqB,EAAE/P,MAAM,EAAE0J,OAAO,EAAElG,MAAM,EAAE/C,KAAK,CAACuP,oBAAoB,CAAC,CAAC;EACpG,CAAC;EACD,IAAIzB,OAAO,GAAGlG,aAAa,CAAC,CAAC;EAC7B,OAAO,aAAa3N,KAAK,CAAC2N,aAAa,CAAChM,MAAM,EAAE;IAC9CkS,OAAO,EAAEA,OAAO;IAChBjK,QAAQ,EAAE7D,KAAK,CAAC6D;EAClB,CAAC,CAAC;AACJ,CAAC,CAAC,CAAC;AACHkG,aAAa,CAAC5B,WAAW,GAAG,eAAe;AAE3C,SAASqH,0BAA0BA,CAAC9S,CAAC,EAAEJ,CAAC,EAAE;EAAE,IAAIG,CAAC,GAAG,WAAW,IAAI,OAAOO,MAAM,IAAIN,CAAC,CAACM,MAAM,CAACC,QAAQ,CAAC,IAAIP,CAAC,CAAC,YAAY,CAAC;EAAE,IAAI,CAACD,CAAC,EAAE;IAAE,IAAIwB,KAAK,CAACC,OAAO,CAACxB,CAAC,CAAC,KAAKD,CAAC,GAAGgT,2BAA2B,CAAC/S,CAAC,CAAC,CAAC,IAAIJ,CAAC,IAAII,CAAC,IAAI,QAAQ,IAAI,OAAOA,CAAC,CAACF,MAAM,EAAE;MAAEC,CAAC,KAAKC,CAAC,GAAGD,CAAC,CAAC;MAAE,IAAIiT,EAAE,GAAG,CAAC;QAAEC,CAAC,GAAG,SAASA,CAACA,CAAA,EAAG,CAAC,CAAC;MAAE,OAAO;QAAEC,CAAC,EAAED,CAAC;QAAEtT,CAAC,EAAE,SAASA,CAACA,CAAA,EAAG;UAAE,OAAOqT,EAAE,IAAIhT,CAAC,CAACF,MAAM,GAAG;YAAEiC,IAAI,EAAE,CAAC;UAAE,CAAC,GAAG;YAAEA,IAAI,EAAE,CAAC,CAAC;YAAEb,KAAK,EAAElB,CAAC,CAACgT,EAAE,EAAE;UAAE,CAAC;QAAE,CAAC;QAAEpT,CAAC,EAAE,SAASA,CAACA,CAACI,CAAC,EAAE;UAAE,MAAMA,CAAC;QAAE,CAAC;QAAE6B,CAAC,EAAEoR;MAAE,CAAC;IAAE;IAAE,MAAM,IAAIrS,SAAS,CAAC,uIAAuI,CAAC;EAAE;EAAE,IAAIP,CAAC;IAAEuB,CAAC,GAAG,CAAC,CAAC;IAAED,CAAC,GAAG,CAAC,CAAC;EAAE,OAAO;IAAEuR,CAAC,EAAE,SAASA,CAACA,CAAA,EAAG;MAAEnT,CAAC,GAAGA,CAAC,CAACG,IAAI,CAACF,CAAC,CAAC;IAAE,CAAC;IAAEL,CAAC,EAAE,SAASA,CAACA,CAAA,EAAG;MAAE,IAAIK,CAAC,GAAGD,CAAC,CAAC+B,IAAI,CAAC,CAAC;MAAE,OAAOF,CAAC,GAAG5B,CAAC,CAAC+B,IAAI,EAAE/B,CAAC;IAAE,CAAC;IAAEJ,CAAC,EAAE,SAASA,CAACA,CAACI,CAAC,EAAE;MAAE2B,CAAC,GAAG,CAAC,CAAC,EAAEtB,CAAC,GAAGL,CAAC;IAAE,CAAC;IAAE6B,CAAC,EAAE,SAASA,CAACA,CAAA,EAAG;MAAE,IAAI;QAAED,CAAC,IAAI,IAAI,IAAI7B,CAAC,CAAC,QAAQ,CAAC,IAAIA,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;MAAE,CAAC,SAAS;QAAE,IAAI4B,CAAC,EAAE,MAAMtB,CAAC;MAAE;IAAE;EAAE,CAAC;AAAE;AAC31B,SAAS0S,2BAA2BA,CAAC/S,CAAC,EAAE4B,CAAC,EAAE;EAAE,IAAI5B,CAAC,EAAE;IAAE,IAAI,QAAQ,IAAI,OAAOA,CAAC,EAAE,OAAOmT,iBAAiB,CAACnT,CAAC,EAAE4B,CAAC,CAAC;IAAE,IAAI7B,CAAC,GAAG,CAAC,CAAC,CAACoC,QAAQ,CAACjC,IAAI,CAACF,CAAC,CAAC,CAACoC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAAE,OAAO,QAAQ,KAAKrC,CAAC,IAAIC,CAAC,CAACQ,WAAW,KAAKT,CAAC,GAAGC,CAAC,CAACQ,WAAW,CAAC6B,IAAI,CAAC,EAAE,KAAK,KAAKtC,CAAC,IAAI,KAAK,KAAKA,CAAC,GAAGwB,KAAK,CAACe,IAAI,CAACtC,CAAC,CAAC,GAAG,WAAW,KAAKD,CAAC,IAAI,0CAA0C,CAACwC,IAAI,CAACxC,CAAC,CAAC,GAAGoT,iBAAiB,CAACnT,CAAC,EAAE4B,CAAC,CAAC,GAAG,KAAK,CAAC;EAAE;AAAE;AACzX,SAASuR,iBAAiBA,CAACnT,CAAC,EAAE4B,CAAC,EAAE;EAAE,CAAC,IAAI,IAAIA,CAAC,IAAIA,CAAC,GAAG5B,CAAC,CAACF,MAAM,MAAM8B,CAAC,GAAG5B,CAAC,CAACF,MAAM,CAAC;EAAE,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAED,CAAC,GAAG4B,KAAK,CAACK,CAAC,CAAC,EAAEhC,CAAC,GAAGgC,CAAC,EAAEhC,CAAC,EAAE,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGI,CAAC,CAACJ,CAAC,CAAC;EAAE,OAAOD,CAAC;AAAE;AACnJ,SAASyT,OAAOA,CAACxT,CAAC,EAAEI,CAAC,EAAE;EAAE,IAAID,CAAC,GAAGP,MAAM,CAACmD,IAAI,CAAC/C,CAAC,CAAC;EAAE,IAAIJ,MAAM,CAACoD,qBAAqB,EAAE;IAAE,IAAIvC,CAAC,GAAGb,MAAM,CAACoD,qBAAqB,CAAChD,CAAC,CAAC;IAAEI,CAAC,KAAKK,CAAC,GAAGA,CAAC,CAACwC,MAAM,CAAC,UAAU7C,CAAC,EAAE;MAAE,OAAOR,MAAM,CAACsD,wBAAwB,CAAClD,CAAC,EAAEI,CAAC,CAAC,CAACmB,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEpB,CAAC,CAACiC,IAAI,CAAC7B,KAAK,CAACJ,CAAC,EAAEM,CAAC,CAAC;EAAE;EAAE,OAAON,CAAC;AAAE;AAC9P,SAASsT,aAAaA,CAACzT,CAAC,EAAE;EAAE,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,SAAS,CAACC,MAAM,EAAEE,CAAC,EAAE,EAAE;IAAE,IAAID,CAAC,GAAG,IAAI,IAAIF,SAAS,CAACG,CAAC,CAAC,GAAGH,SAAS,CAACG,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGoT,OAAO,CAAC5T,MAAM,CAACO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACiD,OAAO,CAAC,UAAUhD,CAAC,EAAE;MAAEgB,eAAe,CAACpB,CAAC,EAAEI,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGR,MAAM,CAACyD,yBAAyB,GAAGzD,MAAM,CAAC0D,gBAAgB,CAACtD,CAAC,EAAEJ,MAAM,CAACyD,yBAAyB,CAAClD,CAAC,CAAC,CAAC,GAAGqT,OAAO,CAAC5T,MAAM,CAACO,CAAC,CAAC,CAAC,CAACiD,OAAO,CAAC,UAAUhD,CAAC,EAAE;MAAER,MAAM,CAACyB,cAAc,CAACrB,CAAC,EAAEI,CAAC,EAAER,MAAM,CAACsD,wBAAwB,CAAC/C,CAAC,EAAEC,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOJ,CAAC;AAAE;AACtb,IAAI0T,QAAQ,GAAG,aAAa/V,KAAK,CAACqN,IAAI,CAAC,aAAarN,KAAK,CAACsN,UAAU,CAAC,UAAUC,OAAO,EAAEC,GAAG,EAAE;EAC3F,IAAIY,UAAU,GAAG5N,aAAa,CAAC,CAAC;EAChC,IAAI0F,OAAO,GAAGlG,KAAK,CAACgQ,UAAU,CAAC9P,iBAAiB,CAAC;EACjD,IAAI6F,KAAK,GAAGwD,YAAY,CAACyM,QAAQ,CAACzI,OAAO,EAAErH,OAAO,CAAC;EACnD,IAAI+P,YAAY,GAAGxV,WAAW,CAAC,EAAE,EAAEsF,KAAK,CAACyE,WAAW,IAAI,CAAC,CAAC;IACxD0L,aAAa,GAAGhR,cAAc,CAAC+Q,YAAY,EAAE,CAAC,CAAC;IAC/ClD,WAAW,GAAGmD,aAAa,CAAC,CAAC,CAAC;IAC9BC,WAAW,GAAGD,aAAa,CAAC,CAAC,CAAC;IAC9BE,cAAc,GAAGF,aAAa,CAAC,CAAC,CAAC;EACnC,IAAIG,eAAe,GAAGrW,KAAK,CAACsW,QAAQ,CAAC,KAAK,CAAC;IACzCC,gBAAgB,GAAGrR,cAAc,CAACmR,eAAe,EAAE,CAAC,CAAC;IACrDrQ,YAAY,GAAGuQ,gBAAgB,CAAC,CAAC,CAAC;IAClCC,eAAe,GAAGD,gBAAgB,CAAC,CAAC,CAAC;EACvC,IAAIE,gBAAgB,GAAGzW,KAAK,CAACsW,QAAQ,CAAC,CAAC,CAAC,CAAC;IACvCI,gBAAgB,GAAGxR,cAAc,CAACuR,gBAAgB,EAAE,CAAC,CAAC;IACtDlO,kBAAkB,GAAGmO,gBAAgB,CAAC,CAAC,CAAC;IACxCC,qBAAqB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;EAC7C,IAAIE,gBAAgB,GAAG5W,KAAK,CAACsW,QAAQ,CAAC,KAAK,CAAC;IAC1CO,gBAAgB,GAAG3R,cAAc,CAAC0R,gBAAgB,EAAE,CAAC,CAAC;IACtD3Q,mBAAmB,GAAG4Q,gBAAgB,CAAC,CAAC,CAAC;IACzCC,sBAAsB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;EAC9C,IAAIE,UAAU,GAAG/W,KAAK,CAACkQ,MAAM,CAAC,KAAK,CAAC;EACpC,IAAI8G,UAAU,GAAGhX,KAAK,CAACkQ,MAAM,CAAC,IAAI,CAAC;EACnC,IAAI+G,UAAU,GAAGjX,KAAK,CAACkQ,MAAM,CAAC,IAAI,CAAC;EACnC,IAAIgH,oCAAoC,GAAGlX,KAAK,CAACkQ,MAAM,CAAC,IAAI,CAAC;EAC7D,IAAIiH,mCAAmC,GAAGnX,KAAK,CAACkQ,MAAM,CAAC,IAAI,CAAC;EAC5D,IAAIhF,QAAQ,GAAGlL,KAAK,CAACkQ,MAAM,CAACnK,KAAK,CAACmF,QAAQ,CAAC;EAC3C,IAAIJ,aAAa,GAAG9K,KAAK,CAACkQ,MAAM,CAACnK,KAAK,CAAC+E,aAAa,CAAC;EACrD,IAAI8F,kBAAkB,GAAG5Q,KAAK,CAACkQ,MAAM,CAAC,IAAI,CAAC;EAC3C,IAAIkH,aAAa,GAAGpX,KAAK,CAACkQ,MAAM,CAAC,IAAI,CAAC;EACtC,IAAImH,WAAW,GAAGrX,KAAK,CAACkQ,MAAM,CAAC,IAAI,CAAC;EACpC,IAAIoH,MAAM,GAAGvR,KAAK,CAACgC,sBAAsB,IAAIhC,KAAK,CAACgC,sBAAsB,CAACwP,IAAI;EAC9E,IAAIlH,SAAS,GAAGhP,WAAW,CAACmF,UAAU,CAAC2P,WAAW,CAAC;EACnD,IAAIvM,QAAQ,GAAG7D,KAAK,CAAC6D,QAAQ,IAAI1D,OAAO,IAAIA,OAAO,CAAC0D,QAAQ,IAAI3J,UAAU,CAAC2J,QAAQ;EACnF,IAAI4N,qBAAqB,GAAGjO,YAAY,CAACkO,WAAW,CAAC3B,aAAa,CAACA,aAAa,CAAC;MAC7E/P,KAAK,EAAEA;IACT,CAAC,EAAEA,KAAK,CAAC4D,gBAAgB,CAAC,EAAE,CAAC,CAAC,EAAE;MAC9B+N,KAAK,EAAE;QACLpS,MAAM,EAAE6Q,WAAW;QACnBvH,OAAO,EAAE5I,YAAY;QACrB2R,cAAc,EAAE1R;MAClB;IACF,CAAC,CAAC,CAAC;IACHoI,GAAG,GAAGmJ,qBAAqB,CAACnJ,GAAG;IAC/BC,EAAE,GAAGkJ,qBAAqB,CAAClJ,EAAE;IAC7ByB,EAAE,GAAGyH,qBAAqB,CAACzH,EAAE;IAC7B6H,UAAU,GAAGJ,qBAAqB,CAACI,UAAU;EAC/CrX,cAAc,CAACgJ,YAAY,CAAC4D,GAAG,CAACnE,MAAM,EAAE4O,UAAU,EAAE;IAClD9S,IAAI,EAAE;EACR,CAAC,CAAC;EACF,IAAI+S,mBAAmB,GAAGnX,kBAAkB,CAAC;MACzCoX,MAAM,EAAEd,UAAU;MAClBe,OAAO,EAAEd,UAAU;MACnBe,QAAQ,EAAE,SAASA,QAAQA,CAAClJ,KAAK,EAAEhJ,IAAI,EAAE;QACvC,IAAI2N,IAAI,GAAG3N,IAAI,CAAC2N,IAAI;UAClBwE,KAAK,GAAGnS,IAAI,CAACmS,KAAK;QACpB,IAAIA,KAAK,EAAE;UACT,IAAIxE,IAAI,KAAK,SAAS,EAAE;YACtB,IAAI,CAACyE,cAAc,CAACpJ,KAAK,CAAC,EAAE;cAC1BqJ,IAAI,CAAC,CAAC;YACR;UACF,CAAC,MAAM,IAAIjS,OAAO,CAACkS,+BAA+B,EAAE;YAClDD,IAAI,CAAC,CAAC;UACR,CAAC,MAAM,IAAI,CAAC7W,UAAU,CAAC+W,UAAU,CAACvJ,KAAK,CAACgJ,MAAM,CAAC,EAAE;YAC/CQ,YAAY,CAAC,CAAC;UAChB;QACF;MACF,CAAC;MACDC,IAAI,EAAEtS;IACR,CAAC,CAAC;IACFuS,oBAAoB,GAAGtT,cAAc,CAAC2S,mBAAmB,EAAE,CAAC,CAAC;IAC7DY,mBAAmB,GAAGD,oBAAoB,CAAC,CAAC,CAAC;IAC7CE,qBAAqB,GAAGF,oBAAoB,CAAC,CAAC,CAAC;EACjD,IAAIG,WAAW,GAAG,SAASA,WAAWA,CAACxM,OAAO,EAAE;IAC9C,OAAO,CAACA,OAAO,IAAI,EAAE,EAAEyM,MAAM,CAAC,UAAUC,MAAM,EAAEtK,MAAM,EAAEjG,KAAK,EAAE;MAC7DuQ,MAAM,CAACpU,IAAI,CAACqR,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEvH,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE;QACvD0D,KAAK,EAAE,IAAI;QACX3J,KAAK,EAAEA;MACT,CAAC,CAAC,CAAC;MACH,IAAI0D,mBAAmB,GAAG8M,sBAAsB,CAACvK,MAAM,CAAC;MACxDvC,mBAAmB,IAAIA,mBAAmB,CAACvG,OAAO,CAAC,UAAU3C,CAAC,EAAE;QAC9D,OAAO+V,MAAM,CAACpU,IAAI,CAAC3B,CAAC,CAAC;MACvB,CAAC,CAAC;MACF,OAAO+V,MAAM;IACf,CAAC,EAAE,EAAE,CAAC;EACR,CAAC;EACD,IAAIE,iBAAiB,GAAG,SAASA,iBAAiBA,CAAA,EAAG;IACnD,IAAI5M,OAAO,GAAGpG,KAAK,CAACmB,gBAAgB,GAAGyR,WAAW,CAAC5S,KAAK,CAACoG,OAAO,CAAC,GAAGpG,KAAK,CAACoG,OAAO;IACjF,IAAIkE,SAAS,IAAI,CAACiH,MAAM,EAAE;MACxB,IAAI0B,YAAY,GAAG7C,WAAW,CAAC8C,IAAI,CAAC,CAAC,CAACC,iBAAiB,CAACnT,KAAK,CAAC2E,YAAY,CAAC;MAC3E,IAAIyO,YAAY,GAAGpT,KAAK,CAACwE,QAAQ,GAAGxE,KAAK,CAACwE,QAAQ,CAAC6O,KAAK,CAAC,GAAG,CAAC,GAAG,CAACrT,KAAK,CAACmG,WAAW,IAAI,OAAO,CAAC;MAC9F,IAAInG,KAAK,CAACmB,gBAAgB,EAAE;QAC1B,IAAImS,cAAc,GAAG,EAAE;QACvB,IAAIC,SAAS,GAAG/D,0BAA0B,CAACxP,KAAK,CAACoG,OAAO,CAAC;UACvDoN,KAAK;QACP,IAAI;UACF,KAAKD,SAAS,CAAC3D,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC4D,KAAK,GAAGD,SAAS,CAAClX,CAAC,CAAC,CAAC,EAAEoC,IAAI,GAAG;YAClD,IAAIgV,QAAQ,GAAGD,KAAK,CAAC5V,KAAK;YAC1B,IAAI8V,kBAAkB,GAAGpZ,aAAa,CAACiF,MAAM,CAACwT,sBAAsB,CAACU,QAAQ,CAAC,EAAEL,YAAY,EAAEH,YAAY,EAAEjT,KAAK,CAAC4E,eAAe,EAAE5E,KAAK,CAAC2E,YAAY,CAAC;YACtJ,IAAI+O,kBAAkB,IAAIA,kBAAkB,CAAClX,MAAM,EAAE;cACnD8W,cAAc,CAAC5U,IAAI,CAACqR,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE0D,QAAQ,CAAC,EAAE/V,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC2L,MAAM,CAACrJ,KAAK,CAACiG,mBAAmB,CAAC,EAAEyN,kBAAkB,CAAC,CAAC,CAAC;YAChJ;UACF;QACF,CAAC,CAAC,OAAOC,GAAG,EAAE;UACZJ,SAAS,CAACjX,CAAC,CAACqX,GAAG,CAAC;QAClB,CAAC,SAAS;UACRJ,SAAS,CAAChV,CAAC,CAAC,CAAC;QACf;QACA,OAAOqU,WAAW,CAACU,cAAc,CAAC;MACpC;MACA,OAAOhZ,aAAa,CAACiF,MAAM,CAAC6G,OAAO,EAAEgN,YAAY,EAAEH,YAAY,EAAEjT,KAAK,CAAC4E,eAAe,EAAE5E,KAAK,CAAC2E,YAAY,CAAC;IAC7G;IACA,OAAOyB,OAAO;EAChB,CAAC;EACD,IAAIwN,kBAAkB,GAAG,SAASA,kBAAkBA,CAAC7K,KAAK,EAAE;IAC1D,IAAI8K,WAAW,GAAG9K,KAAK,CAAC+K,aAAa,KAAK/O,aAAa,CAAC+F,OAAO,GAAGvP,UAAU,CAACwY,wBAAwB,CAAC7C,UAAU,CAACpG,OAAO,EAAE,wCAAwC,CAAC,GAAG/F,aAAa,CAAC+F,OAAO;IAC3LvP,UAAU,CAAC6P,KAAK,CAACyI,WAAW,CAAC;EAC/B,CAAC;EACD,IAAIG,iBAAiB,GAAG,SAASA,iBAAiBA,CAACjL,KAAK,EAAE;IACxD,IAAI8K,WAAW,GAAG9K,KAAK,CAAC+K,aAAa,KAAK/O,aAAa,CAAC+F,OAAO,GAAGvP,UAAU,CAAC0Y,uBAAuB,CAAC/C,UAAU,CAACpG,OAAO,EAAE,wCAAwC,CAAC,GAAG/F,aAAa,CAAC+F,OAAO;IAC1LvP,UAAU,CAAC6P,KAAK,CAACyI,WAAW,CAAC;EAC/B,CAAC;EACD,IAAI1B,cAAc,GAAG,SAASA,cAAcA,CAACpJ,KAAK,EAAE;IAClD,OAAOxN,UAAU,CAAC2Y,iBAAiB,CAACnL,KAAK,CAACgJ,MAAM,EAAE,iBAAiB,EAAE,WAAW,CAAC,IAAIxW,UAAU,CAAC2Y,iBAAiB,CAACnL,KAAK,CAACgJ,MAAM,CAACoC,aAAa,IAAIpL,KAAK,CAACgJ,MAAM,EAAE,iBAAiB,EAAE,iBAAiB,CAAC;EACrM,CAAC;EACD,IAAIjJ,QAAQ,GAAG,SAASrD,OAAOA,CAACsD,KAAK,EAAE;IACrC,IAAI/I,KAAK,CAACI,QAAQ,IAAIJ,KAAK,CAACqF,OAAO,EAAE;MACnC;IACF;IACArF,KAAK,CAACyF,OAAO,IAAIzF,KAAK,CAACyF,OAAO,CAACsD,KAAK,CAAC;;IAErC;IACA,IAAIA,KAAK,CAACqL,gBAAgB,EAAE;MAC1B;IACF;IACA,IAAIjC,cAAc,CAACpJ,KAAK,CAAC,IAAIA,KAAK,CAACgJ,MAAM,CAACsC,OAAO,KAAK,OAAO,EAAE;MAC7D;IACF,CAAC,MAAM,IAAI,CAACnD,UAAU,CAACpG,OAAO,IAAI,EAAEoG,UAAU,CAACpG,OAAO,IAAIoG,UAAU,CAACpG,OAAO,CAACwJ,QAAQ,CAACvL,KAAK,CAACgJ,MAAM,CAAC,CAAC,EAAE;MACpGxW,UAAU,CAAC6P,KAAK,CAACrG,aAAa,CAAC+F,OAAO,CAAC;MACvC5K,mBAAmB,GAAGkS,IAAI,CAAC,CAAC,GAAGmC,IAAI,CAAC,CAAC;IACvC;IACAxL,KAAK,CAACyL,cAAc,CAAC,CAAC;IACtBxD,UAAU,CAAClG,OAAO,GAAG,IAAI;EAC3B,CAAC;EACD,IAAI2J,YAAY,GAAG,SAASA,YAAYA,CAAC1L,KAAK,EAAE;IAC9C,IAAI/I,KAAK,CAAC4G,WAAW,IAAI,CAAC1G,mBAAmB,EAAE;MAC7CqU,IAAI,CAAC,CAAC;IACR;IACA9D,eAAe,CAAC,IAAI,CAAC;IACrBzQ,KAAK,CAAC4F,OAAO,IAAI5F,KAAK,CAAC4F,OAAO,CAACmD,KAAK,CAAC;EACvC,CAAC;EACD,IAAI2L,WAAW,GAAG,SAASA,WAAWA,CAAC3L,KAAK,EAAE;IAC5C0H,eAAe,CAAC,KAAK,CAAC;IACtB,IAAIzQ,KAAK,CAACuF,MAAM,EAAE;MAChB0F,UAAU,CAAC,YAAY;QACrB,IAAI0J,YAAY,GAAGxP,QAAQ,CAAC2F,OAAO,GAAG3F,QAAQ,CAAC2F,OAAO,CAAClN,KAAK,GAAGuG,SAAS;QACxEnE,KAAK,CAACuF,MAAM,CAAC;UACXyD,aAAa,EAAED,KAAK,CAACC,aAAa;UAClCpL,KAAK,EAAE+W,YAAY;UACnBC,eAAe,EAAE,SAASA,eAAeA,CAAA,EAAG;YAC1C7L,KAAK,CAACC,aAAa,CAAC4L,eAAe,CAAC,CAAC;UACvC,CAAC;UACDJ,cAAc,EAAE,SAASA,cAAcA,CAAA,EAAG;YACxCzL,KAAK,CAACC,aAAa,CAACwL,cAAc,CAAC,CAAC;UACtC,CAAC;UACDzC,MAAM,EAAE;YACNhT,IAAI,EAAEiB,KAAK,CAACjB,IAAI;YAChBkG,EAAE,EAAEjF,KAAK,CAACiF,EAAE;YACZrH,KAAK,EAAE+W;UACT;QACF,CAAC,CAAC;MACJ,CAAC,EAAE,GAAG,CAAC;IACT;EACF,CAAC;EACD,IAAIE,cAAc,GAAG,SAASA,cAAcA,CAAC9L,KAAK,EAAEP,MAAM,EAAE;IAC1D,IAAIsM,MAAM,GAAGvY,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAK4H,SAAS,GAAG5H,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;IACrFwY,UAAU,CAAC;MACT/L,aAAa,EAAED,KAAK;MACpBP,MAAM,EAAEA;IACV,CAAC,CAAC;IACF,IAAIsM,MAAM,EAAE;MACV1C,IAAI,CAAC,CAAC;MACN7W,UAAU,CAAC6P,KAAK,CAACrG,aAAa,CAAC+F,OAAO,CAAC;IACzC;EACF,CAAC;EACD,IAAIkK,YAAY,GAAG,SAASA,YAAYA,CAACjM,KAAK,EAAE;IAC9C5N,cAAc,CAAC8Z,IAAI,CAAC,eAAe,EAAE;MACnCjM,aAAa,EAAED,KAAK;MACpBgJ,MAAM,EAAEd,UAAU,CAACnG;IACrB,CAAC,CAAC;EACJ,CAAC;EACD,IAAIpC,cAAc,GAAG,SAASA,cAAcA,CAACK,KAAK,EAAE;IAClD,IAAI/I,KAAK,CAACI,QAAQ,EAAE;MAClB2I,KAAK,CAACyL,cAAc,CAAC,CAAC;MACtB;IACF;IACA,IAAIU,IAAI,GAAG3Z,UAAU,CAAC4Z,SAAS,CAAC,CAAC,GAAGpM,KAAK,CAACH,GAAG,GAAGG,KAAK,CAACmM,IAAI;IAC1D,QAAQA,IAAI;MACV,KAAK,WAAW;QACdE,cAAc,CAACrM,KAAK,CAAC;QACrB;MACF,KAAK,SAAS;QACZsM,YAAY,CAACtM,KAAK,CAAC;QACnB;MACF,KAAK,WAAW;MAChB,KAAK,YAAY;QACfuM,cAAc,CAACvM,KAAK,EAAE/I,KAAK,CAACa,QAAQ,CAAC;QACrC;MACF,KAAK,MAAM;QACT0U,SAAS,CAACxM,KAAK,CAAC;QAChB;MACF,KAAK,KAAK;QACRyM,QAAQ,CAACzM,KAAK,CAAC;QACf;MACF,KAAK,UAAU;QACb0M,aAAa,CAAC1M,KAAK,CAAC;QACpB;MACF,KAAK,QAAQ;QACX2M,WAAW,CAAC3M,KAAK,CAAC;QAClB;MACF,KAAK,OAAO;QACV4M,UAAU,CAAC5M,KAAK,EAAE/I,KAAK,CAACa,QAAQ,CAAC;QACjC;MACF,KAAK,aAAa;MAClB,KAAK,OAAO;QACV+U,UAAU,CAAC7M,KAAK,CAAC;QACjB;MACF,KAAK,QAAQ;QACX8M,WAAW,CAAC9M,KAAK,CAAC;QAClB;MACF,KAAK,KAAK;QACR+M,QAAQ,CAAC/M,KAAK,CAAC;QACf;MACF,KAAK,WAAW;QACdgN,cAAc,CAAChN,KAAK,EAAE/I,KAAK,CAACa,QAAQ,CAAC;QACrC;MACF,KAAK,WAAW;MAChB,KAAK,YAAY;QACf;QACA;MACF;QACE,IAAImV,OAAO,GAAGjN,KAAK,CAACiN,OAAO,IAAIjN,KAAK,CAACkN,OAAO,IAAIlN,KAAK,CAACmN,MAAM;;QAE5D;QACA,IAAI,CAACF,OAAO,IAAI1a,WAAW,CAAC6a,oBAAoB,CAACpN,KAAK,CAACH,GAAG,CAAC,EAAE;UAC3D,CAAC1I,mBAAmB,IAAI,CAACF,KAAK,CAACa,QAAQ,IAAI0T,IAAI,CAAC,CAAC;UACjD,CAACvU,KAAK,CAACa,QAAQ,IAAIuV,aAAa,CAACrN,KAAK,EAAEA,KAAK,CAACH,GAAG,CAAC;QACpD;QACA;IACJ;IACAoI,UAAU,CAAClG,OAAO,GAAG,KAAK;EAC5B,CAAC;EACD,IAAI8C,oBAAoB,GAAG,SAASA,oBAAoBA,CAAC7E,KAAK,EAAE;IAC9D,QAAQA,KAAK,CAACmM,IAAI;MAChB,KAAK,WAAW;QACdE,cAAc,CAACrM,KAAK,CAAC;QACrB;MACF,KAAK,SAAS;QACZsM,YAAY,CAACtM,KAAK,CAAC;QACnB;MACF,KAAK,WAAW;MAChB,KAAK,YAAY;QACfuM,cAAc,CAACvM,KAAK,EAAE,IAAI,CAAC;QAC3B;MACF,KAAK,OAAO;MACZ,KAAK,aAAa;QAChB6M,UAAU,CAAC7M,KAAK,CAAC;QACjBA,KAAK,CAACyL,cAAc,CAAC,CAAC;QACtB;MACF,KAAK,QAAQ;QACXqB,WAAW,CAAC9M,KAAK,CAAC;QAClB;IACJ;EACF,CAAC;EACD,IAAIsN,oBAAoB,GAAG,SAASA,oBAAoBA,CAAA,EAAG;IACzD,OAAO9a,UAAU,CAAC+a,oBAAoB,CAACpF,UAAU,CAACpG,OAAO,EAAE,wCAAwC,CAAC,CAACtO,MAAM,GAAG,CAAC;EACjH,CAAC;EACD,IAAI+Z,eAAe,GAAG,SAASA,eAAeA,CAAC/N,MAAM,EAAE;IACrD,IAAIgO,eAAe;IACnB,OAAOC,aAAa,CAACjO,MAAM,CAAC,KAAK,CAACgO,eAAe,GAAG/J,cAAc,CAACjE,MAAM,CAAC,MAAM,IAAI,IAAIgO,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACrD,iBAAiB,CAACnT,KAAK,CAAC2E,YAAY,CAAC,CAAC+R,UAAU,CAACpF,WAAW,CAACxG,OAAO,CAACqI,iBAAiB,CAACnT,KAAK,CAAC2E,YAAY,CAAC,CAAC,CAAC;EAC5P,CAAC;EACD,IAAI8R,aAAa,GAAG,SAASA,aAAaA,CAACjO,MAAM,EAAE;IACjD,OAAOlN,WAAW,CAACmF,UAAU,CAAC+H,MAAM,CAAC,IAAI,EAAEkE,gBAAgB,CAAClE,MAAM,CAAC,IAAImO,aAAa,CAACnO,MAAM,CAAC,CAAC;EAC/F,CAAC;EACD,IAAIoO,iBAAiB,GAAG,SAASA,iBAAiBA,CAAA,EAAG;IACnD,OAAOtb,WAAW,CAACmF,UAAU,CAACT,KAAK,CAACpC,KAAK,CAAC;EAC5C,CAAC;EACD,IAAIiZ,qBAAqB,GAAG,SAASA,qBAAqBA,CAACrO,MAAM,EAAE;IACjE,OAAOiO,aAAa,CAACjO,MAAM,CAAC,IAAImE,UAAU,CAACnE,MAAM,CAAC;EACpD,CAAC;EACD,IAAIsO,uBAAuB,GAAG,SAASA,uBAAuBA,CAAA,EAAG;IAC/D,OAAOF,iBAAiB,GAAGvM,cAAc,CAAC0M,SAAS,CAAC,UAAUvO,MAAM,EAAE;MACpE,OAAOqO,qBAAqB,CAACrO,MAAM,CAAC;IACtC,CAAC,CAAC,GAAG,CAAC,CAAC;EACT,CAAC;EACD,IAAIwO,2BAA2B,GAAG,SAASA,2BAA2BA,CAAA,EAAG;IACvE,IAAIjM,aAAa,GAAG+L,uBAAuB,CAAC,CAAC;IAC7C,OAAO/L,aAAa,GAAG,CAAC,GAAGkM,oBAAoB,CAAC,CAAC,GAAGlM,aAAa;EACnE,CAAC;EACD,IAAIqL,aAAa,GAAG,SAASA,aAAaA,CAACrN,KAAK,EAAEmO,KAAK,EAAE;IACvD5F,WAAW,CAACxG,OAAO,GAAG,CAACwG,WAAW,CAACxG,OAAO,IAAI,EAAE,IAAIoM,KAAK;IACzD,IAAIC,WAAW,GAAG,CAAC,CAAC;IACpB,IAAIC,OAAO,GAAG,KAAK;IACnB,IAAI9b,WAAW,CAACmF,UAAU,CAAC6Q,WAAW,CAACxG,OAAO,CAAC,EAAE;MAC/C,IAAItI,kBAAkB,KAAK,CAAC,CAAC,EAAE;QAC7B2U,WAAW,GAAG9M,cAAc,CAACvL,KAAK,CAAC0D,kBAAkB,CAAC,CAACuU,SAAS,CAAC,UAAUvO,MAAM,EAAE;UACjF,OAAO+N,eAAe,CAAC/N,MAAM,CAAC;QAChC,CAAC,CAAC;QACF2O,WAAW,GAAGA,WAAW,KAAK,CAAC,CAAC,GAAG9M,cAAc,CAACvL,KAAK,CAAC,CAAC,EAAE0D,kBAAkB,CAAC,CAACuU,SAAS,CAAC,UAAUvO,MAAM,EAAE;UACzG,OAAO+N,eAAe,CAAC/N,MAAM,CAAC;QAChC,CAAC,CAAC,GAAG2O,WAAW,GAAG3U,kBAAkB;MACvC,CAAC,MAAM;QACL2U,WAAW,GAAG9M,cAAc,CAAC0M,SAAS,CAAC,UAAUvO,MAAM,EAAE;UACvD,OAAO+N,eAAe,CAAC/N,MAAM,CAAC;QAChC,CAAC,CAAC;MACJ;MACA,IAAI2O,WAAW,KAAK,CAAC,CAAC,EAAE;QACtBC,OAAO,GAAG,IAAI;MAChB;MACA,IAAID,WAAW,KAAK,CAAC,CAAC,IAAI3U,kBAAkB,KAAK,CAAC,CAAC,EAAE;QACnD2U,WAAW,GAAGH,2BAA2B,CAAC,CAAC;MAC7C;MACA,IAAIG,WAAW,KAAK,CAAC,CAAC,EAAE;QACtBzL,wBAAwB,CAAC3C,KAAK,EAAEoO,WAAW,CAAC;MAC9C;IACF;IACA,IAAI9F,aAAa,CAACvG,OAAO,EAAE;MACzBuM,YAAY,CAAChG,aAAa,CAACvG,OAAO,CAAC;IACrC;IACAuG,aAAa,CAACvG,OAAO,GAAGG,UAAU,CAAC,YAAY;MAC7CqG,WAAW,CAACxG,OAAO,GAAG,EAAE;MACxBuG,aAAa,CAACvG,OAAO,GAAG,IAAI;IAC9B,CAAC,EAAE,GAAG,CAAC;IACP,OAAOsM,OAAO;EAChB,CAAC;EACD,IAAIE,0BAA0B,GAAG,SAASA,0BAA0BA,CAAA,EAAG;IACrE,IAAIvM,aAAa,GAAG+L,uBAAuB,CAAC,CAAC;IAC7C,OAAO/L,aAAa,GAAG,CAAC,GAAGwM,mBAAmB,CAAC,CAAC,GAAGxM,aAAa;EAClE,CAAC;EACD,IAAIkM,oBAAoB,GAAG,SAASA,oBAAoBA,CAAA,EAAG;IACzD,OAAO5M,cAAc,CAAC0M,SAAS,CAAC,UAAUvO,MAAM,EAAE;MAChD,OAAOiO,aAAa,CAACjO,MAAM,CAAC;IAC9B,CAAC,CAAC;EACJ,CAAC;EACD,IAAI+O,mBAAmB,GAAG,SAASA,mBAAmBA,CAAA,EAAG;IACvD,OAAOjc,WAAW,CAACkc,aAAa,CAACnN,cAAc,EAAE,UAAU7B,MAAM,EAAE;MACjE,OAAOiO,aAAa,CAACjO,MAAM,CAAC;IAC9B,CAAC,CAAC;EACJ,CAAC;EACD,IAAIiP,mBAAmB,GAAG,SAASA,mBAAmBA,CAAClV,KAAK,EAAE;IAC5D,IAAImV,kBAAkB,GAAGnV,KAAK,GAAG8H,cAAc,CAAC7N,MAAM,GAAG,CAAC,GAAG6N,cAAc,CAACvL,KAAK,CAACyD,KAAK,GAAG,CAAC,CAAC,CAACwU,SAAS,CAAC,UAAUvO,MAAM,EAAE;MACvH,OAAOiO,aAAa,CAACjO,MAAM,CAAC;IAC9B,CAAC,CAAC,GAAG,CAAC,CAAC;IACP,OAAOkP,kBAAkB,GAAG,CAAC,CAAC,GAAGA,kBAAkB,GAAGnV,KAAK,GAAG,CAAC,GAAGA,KAAK;EACzE,CAAC;EACD,IAAIoV,mBAAmB,GAAG,SAASA,mBAAmBA,CAACpV,KAAK,EAAE;IAC5D,IAAImV,kBAAkB,GAAGnV,KAAK,GAAG,CAAC,GAAGjH,WAAW,CAACkc,aAAa,CAACnN,cAAc,CAACvL,KAAK,CAAC,CAAC,EAAEyD,KAAK,CAAC,EAAE,UAAUiG,MAAM,EAAE;MAC/G,OAAOiO,aAAa,CAACjO,MAAM,CAAC;IAC9B,CAAC,CAAC,GAAG,CAAC,CAAC;IACP,OAAOkP,kBAAkB,GAAG,CAAC,CAAC,GAAGA,kBAAkB,GAAGnV,KAAK;EAC7D,CAAC;EACD,IAAImJ,wBAAwB,GAAG,SAASA,wBAAwBA,CAAC3C,KAAK,EAAExG,KAAK,EAAE;IAC7E,IAAIC,kBAAkB,KAAKD,KAAK,EAAE;MAChCqO,qBAAqB,CAACrO,KAAK,CAAC;MAC5BqV,WAAW,CAACrV,KAAK,CAAC;MAClB,IAAIvC,KAAK,CAAC0G,aAAa,EAAE;QACvBmO,cAAc,CAAC9L,KAAK,EAAEsB,cAAc,CAAC9H,KAAK,CAAC,EAAE,KAAK,CAAC;MACrD;IACF;EACF,CAAC;EACD,IAAIqV,WAAW,GAAG,SAASA,WAAWA,CAACrV,KAAK,EAAE;IAC5C,IAAIsV,WAAW,GAAGtc,UAAU,CAACuc,UAAU,CAAC5G,UAAU,CAACpG,OAAO,EAAE,uBAAuB,CAACzB,MAAM,CAAC9G,KAAK,EAAE,KAAK,CAAC,CAAC;IACzGsV,WAAW,IAAIA,WAAW,CAACzM,KAAK,CAAC,CAAC;EACpC,CAAC;EACD,IAAIgK,cAAc,GAAG,SAASA,cAAcA,CAACrM,KAAK,EAAE;IAClD,IAAI,CAAC7I,mBAAmB,EAAE;MACxBqU,IAAI,CAAC,CAAC;MACNvU,KAAK,CAACa,QAAQ,IAAI6K,wBAAwB,CAAC3C,KAAK,EAAE+N,uBAAuB,CAAC,CAAC,CAAC;IAC9E,CAAC,MAAM;MACL,IAAIK,WAAW,GAAG3U,kBAAkB,KAAK,CAAC,CAAC,GAAGiV,mBAAmB,CAACjV,kBAAkB,CAAC,GAAGwO,UAAU,CAAClG,OAAO,GAAGmM,oBAAoB,CAAC,CAAC,GAAGD,2BAA2B,CAAC,CAAC;MACnKtL,wBAAwB,CAAC3C,KAAK,EAAEoO,WAAW,CAAC;IAC9C;IACApO,KAAK,CAACyL,cAAc,CAAC,CAAC;EACxB,CAAC;EACD,IAAIa,YAAY,GAAG,SAASA,YAAYA,CAACtM,KAAK,EAAE;IAC9C,IAAIgP,kBAAkB,GAAGxb,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAK4H,SAAS,GAAG5H,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;IAClG,IAAIwM,KAAK,CAACmN,MAAM,IAAI,CAAC6B,kBAAkB,EAAE;MACvC,IAAIvV,kBAAkB,KAAK,CAAC,CAAC,EAAE;QAC7BqS,cAAc,CAAC9L,KAAK,EAAEsB,cAAc,CAAC7H,kBAAkB,CAAC,CAAC;MAC3D;MACAmP,KAAK,CAACC,cAAc,IAAIQ,IAAI,CAAC,CAAC;MAC9BrJ,KAAK,CAACyL,cAAc,CAAC,CAAC;IACxB,CAAC,MAAM;MACL,IAAI2C,WAAW,GAAG3U,kBAAkB,KAAK,CAAC,CAAC,GAAGmV,mBAAmB,CAACnV,kBAAkB,CAAC,GAAGwO,UAAU,CAAClG,OAAO,GAAGyM,mBAAmB,CAAC,CAAC,GAAGD,0BAA0B,CAAC,CAAC;MACjK5L,wBAAwB,CAAC3C,KAAK,EAAEoO,WAAW,CAAC;MAC5C,CAACjX,mBAAmB,IAAIqU,IAAI,CAAC,CAAC;MAC9BxL,KAAK,CAACyL,cAAc,CAAC,CAAC;IACxB;EACF,CAAC;EACD,IAAIc,cAAc,GAAG,SAASA,cAAcA,CAACvM,KAAK,EAAE;IAClD,IAAIgP,kBAAkB,GAAGxb,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAK4H,SAAS,GAAG5H,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;IAClGwb,kBAAkB,IAAInH,qBAAqB,CAAC,CAAC,CAAC,CAAC;EACjD,CAAC;EACD,IAAI2E,SAAS,GAAG,SAASA,SAASA,CAACxM,KAAK,EAAE;IACxC,IAAIgP,kBAAkB,GAAGxb,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAK4H,SAAS,GAAG5H,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;IAClG,IAAIwb,kBAAkB,EAAE;MACtBhP,KAAK,CAACiP,aAAa,CAACC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC;MAC3CrH,qBAAqB,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC,MAAM;MACLlF,wBAAwB,CAAC3C,KAAK,EAAEkO,oBAAoB,CAAC,CAAC,CAAC;MACvD,CAAC/W,mBAAmB,IAAIqU,IAAI,CAAC,CAAC;IAChC;IACAxL,KAAK,CAACyL,cAAc,CAAC,CAAC;EACxB,CAAC;EACD,IAAIgB,QAAQ,GAAG,SAASA,QAAQA,CAACzM,KAAK,EAAE;IACtC,IAAIgP,kBAAkB,GAAGxb,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAK4H,SAAS,GAAG5H,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;IAClG,IAAIwb,kBAAkB,EAAE;MACtB,IAAIhG,MAAM,GAAGhJ,KAAK,CAACiP,aAAa;MAChC,IAAIE,GAAG,GAAGnG,MAAM,CAACnU,KAAK,CAACpB,MAAM;MAC7BuV,MAAM,CAACkG,iBAAiB,CAACC,GAAG,EAAEA,GAAG,CAAC;MAClCtH,qBAAqB,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC,MAAM;MACLlF,wBAAwB,CAAC3C,KAAK,EAAEwO,mBAAmB,CAAC,CAAC,CAAC;MACtD,CAACrX,mBAAmB,IAAIqU,IAAI,CAAC,CAAC;IAChC;IACAxL,KAAK,CAACyL,cAAc,CAAC,CAAC;EACxB,CAAC;EACD,IAAIkB,WAAW,GAAG,SAASA,WAAWA,CAAC3M,KAAK,EAAE;IAC5CA,KAAK,CAACyL,cAAc,CAAC,CAAC;EACxB,CAAC;EACD,IAAIiB,aAAa,GAAG,SAASA,aAAaA,CAAC1M,KAAK,EAAE;IAChDA,KAAK,CAACyL,cAAc,CAAC,CAAC;EACxB,CAAC;EACD,IAAImB,UAAU,GAAG,SAASA,UAAUA,CAAC5M,KAAK,EAAE;IAC1C,IAAIgP,kBAAkB,GAAGxb,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAK4H,SAAS,GAAG5H,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;IAClG,CAACwb,kBAAkB,IAAInC,UAAU,CAAC7M,KAAK,CAAC;EAC1C,CAAC;EACD,IAAI6M,UAAU,GAAG,SAASA,UAAUA,CAAC7M,KAAK,EAAE;IAC1CA,KAAK,CAACyL,cAAc,CAAC,CAAC;IACtB,IAAI,CAACtU,mBAAmB,EAAE;MACxB0Q,qBAAqB,CAAC,CAAC,CAAC,CAAC;MACzBwE,cAAc,CAACrM,KAAK,CAAC;IACvB,CAAC,MAAM;MACL,IAAIvG,kBAAkB,KAAK,CAAC,CAAC,EAAE;QAC7B;MACF;MACA,IAAI2V,aAAa,GAAG9N,cAAc,CAAC7H,kBAAkB,CAAC;MACtD,IAAI6D,WAAW,GAAG+R,cAAc,CAACD,aAAa,CAAC;MAC/C,IAAI9R,WAAW,IAAI,IAAI,IAAIA,WAAW,IAAIlC,SAAS,EAAE;QACnDiO,IAAI,CAAC,CAAC;QACN1H,WAAW,CAAC,CAAC;QACb2N,mBAAmB,CAACC,cAAc,CAAC;QACnC;MACF;MACAzD,cAAc,CAAC9L,KAAK,EAAEoP,aAAa,CAAC;IACtC;EACF,CAAC;EACD,IAAItC,WAAW,GAAG,SAASA,WAAWA,CAAC9M,KAAK,EAAE;IAC5C7I,mBAAmB,IAAIkS,IAAI,CAAC,CAAC;IAC7BrJ,KAAK,CAACyL,cAAc,CAAC,CAAC;EACxB,CAAC;EACD,IAAIsB,QAAQ,GAAG,SAASA,QAAQA,CAAC/M,KAAK,EAAE;IACtC,IAAIgP,kBAAkB,GAAGxb,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAK4H,SAAS,GAAG5H,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;IAClG,IAAI,CAACwb,kBAAkB,EAAE;MACvB,IAAI7X,mBAAmB,IAAI,CAACmW,oBAAoB,CAAC,CAAC,EAAE;QAClD9a,UAAU,CAAC6P,KAAK,CAAC+F,oCAAoC,CAACrG,OAAO,CAAC;QAC9D/B,KAAK,CAACyL,cAAc,CAAC,CAAC;MACxB,CAAC,MAAM;QACL,IAAIhS,kBAAkB,KAAK,CAAC,CAAC,EAAE;UAC7BqS,cAAc,CAAC9L,KAAK,EAAEsB,cAAc,CAAC7H,kBAAkB,CAAC,CAAC;QAC3D;QACAtC,mBAAmB,IAAIkS,IAAI,CAAC,CAAC;MAC/B;IACF;EACF,CAAC;EACD,IAAI2D,cAAc,GAAG,SAASA,cAAcA,CAAChN,KAAK,EAAE;IAClD,IAAIgP,kBAAkB,GAAGxb,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAK4H,SAAS,GAAG5H,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;IAClG,IAAIwM,KAAK,IAAIgP,kBAAkB,EAAE;MAC/B,CAAC7X,mBAAmB,IAAIqU,IAAI,CAAC,CAAC;IAChC;EACF,CAAC;EACD,IAAIgE,WAAW,GAAG,SAASA,WAAWA,CAAClO,cAAc,EAAEmO,UAAU,EAAE;IACjE,IAAI,CAACA,UAAU,IAAI,EAAEnO,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,IAAIA,cAAc,CAAC7N,MAAM,CAAC,EAAE,OAAO,CAAC,CAAC;IAC9G,IAAIic,gBAAgB,GAAGD,UAAU,CAACrF,iBAAiB,CAAC,CAAC;IACrD,IAAIuF,UAAU,GAAGrO,cAAc,CAAC0M,SAAS,CAAC,UAAU3U,IAAI,EAAE;MACxD,OAAOqK,cAAc,CAACrK,IAAI,CAAC,CAAC+Q,iBAAiB,CAAC,CAAC,KAAKsF,gBAAgB;IACtE,CAAC,CAAC;IACF,IAAIC,UAAU,KAAK,CAAC,CAAC,EAAE,OAAOA,UAAU;IACxC,OAAOrO,cAAc,CAAC0M,SAAS,CAAC,UAAU3U,IAAI,EAAE;MAC9C,OAAOqK,cAAc,CAACrK,IAAI,CAAC,CAAC+Q,iBAAiB,CAAC,CAAC,CAACuD,UAAU,CAAC+B,gBAAgB,CAAC;IAC9E,CAAC,CAAC;EACJ,CAAC;EACD,IAAIE,qBAAqB,GAAG,SAASA,qBAAqBA,CAAC5P,KAAK,EAAE;IAChE,CAAC7I,mBAAmB,IAAIqU,IAAI,CAAC,CAAC;IAC9B,IAAIqE,WAAW,GAAG,IAAI;IACtB,IAAI7P,KAAK,CAACgJ,MAAM,CAACnU,KAAK,IAAIyM,cAAc,EAAE;MACxCuO,WAAW,GAAGL,WAAW,CAAClO,cAAc,EAAEtB,KAAK,CAACgJ,MAAM,CAACnU,KAAK,CAAC;IAC/D;IACAgT,qBAAqB,CAACgI,WAAW,CAAC;IAClC,IAAI5Y,KAAK,CAACwF,QAAQ,EAAE;MAClBxF,KAAK,CAACwF,QAAQ,CAAC;QACbwD,aAAa,EAAED,KAAK,CAACC,aAAa;QAClCpL,KAAK,EAAEmL,KAAK,CAACgJ,MAAM,CAACnU,KAAK;QACzBgX,eAAe,EAAE,SAASA,eAAeA,CAAA,EAAG;UAC1C7L,KAAK,CAACC,aAAa,CAAC4L,eAAe,CAAC,CAAC;QACvC,CAAC;QACDJ,cAAc,EAAE,SAASA,cAAcA,CAAA,EAAG;UACxCzL,KAAK,CAACC,aAAa,CAACwL,cAAc,CAAC,CAAC;QACtC,CAAC;QACDzC,MAAM,EAAE;UACNhT,IAAI,EAAEiB,KAAK,CAACjB,IAAI;UAChBkG,EAAE,EAAEjF,KAAK,CAACiF,EAAE;UACZrH,KAAK,EAAEmL,KAAK,CAACgJ,MAAM,CAACnU;QACtB;MACF,CAAC,CAAC;IACJ;EACF,CAAC;EACD,IAAIib,oBAAoB,GAAG,SAASA,oBAAoBA,CAAC9P,KAAK,EAAE;IAC9D0H,eAAe,CAAC,IAAI,CAAC;IACrB2B,IAAI,CAAC,CAAC;IACNpS,KAAK,CAAC4F,OAAO,IAAI5F,KAAK,CAAC4F,OAAO,CAACmD,KAAK,CAAC;EACvC,CAAC;EACD,IAAI6D,aAAa,GAAG,SAASA,aAAaA,CAAC7D,KAAK,EAAE;IAChD,IAAIP,MAAM,GAAGO,KAAK,CAACP,MAAM;IACzB,IAAI,CAACA,MAAM,CAACpI,QAAQ,EAAE;MACpB2U,UAAU,CAAChM,KAAK,CAAC;MACjBxN,UAAU,CAAC6P,KAAK,CAACrG,aAAa,CAAC+F,OAAO,CAAC;IACzC;IACAsH,IAAI,CAAC,CAAC;EACR,CAAC;EACD,IAAI5H,mBAAmB,GAAG,SAASA,mBAAmBA,CAACzB,KAAK,EAAE;IAC5D,IAAIxJ,MAAM,GAAGwJ,KAAK,CAACgJ,MAAM,CAACnU,KAAK;IAC/ByS,cAAc,CAAC9Q,MAAM,CAAC;IACtB,IAAIS,KAAK,CAAC2F,QAAQ,EAAE;MAClB3F,KAAK,CAAC2F,QAAQ,CAAC;QACbqD,aAAa,EAAED,KAAK;QACpBxJ,MAAM,EAAEA;MACV,CAAC,CAAC;IACJ;EACF,CAAC;EACD,IAAI4N,sBAAsB,GAAG,SAASA,sBAAsBA,CAAC2L,QAAQ,EAAE;IACrEpO,WAAW,CAACoO,QAAQ,CAAC;EACvB,CAAC;EACD,IAAIpO,WAAW,GAAG,SAASA,WAAWA,CAACoO,QAAQ,EAAE;IAC/CzI,cAAc,CAAC,EAAE,CAAC;IAClBrQ,KAAK,CAAC2F,QAAQ,IAAI3F,KAAK,CAAC2F,QAAQ,CAAC;MAC/BpG,MAAM,EAAE;IACV,CAAC,CAAC;IACFuZ,QAAQ,IAAIA,QAAQ,CAAC,CAAC;EACxB,CAAC;EACD,IAAIC,KAAK,GAAG,SAASA,KAAKA,CAAChQ,KAAK,EAAE;IAChC,IAAI/I,KAAK,CAACwF,QAAQ,EAAE;MAClBxF,KAAK,CAACwF,QAAQ,CAAC;QACbwD,aAAa,EAAED,KAAK;QACpBnL,KAAK,EAAEuG,SAAS;QAChByQ,eAAe,EAAE,SAASA,eAAeA,CAAA,EAAG;UAC1C7L,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,IAAIA,KAAK,CAAC6L,eAAe,CAAC,CAAC;QAC/D,CAAC;QACDJ,cAAc,EAAE,SAASA,cAAcA,CAAA,EAAG;UACxCzL,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,IAAIA,KAAK,CAACyL,cAAc,CAAC,CAAC;QAC9D,CAAC;QACDzC,MAAM,EAAE;UACNhT,IAAI,EAAEiB,KAAK,CAACjB,IAAI;UAChBkG,EAAE,EAAEjF,KAAK,CAACiF,EAAE;UACZrH,KAAK,EAAEuG;QACT;MACF,CAAC,CAAC;IACJ;IACA,IAAInE,KAAK,CAACT,MAAM,EAAE;MAChBmL,WAAW,CAAC,CAAC;IACf;IACA2N,mBAAmB,CAAC,CAAC;IACrBzH,qBAAqB,CAAC,CAAC,CAAC,CAAC;EAC3B,CAAC;EACD,IAAImE,UAAU,GAAG,SAASA,UAAUA,CAAChM,KAAK,EAAE;IAC1C,IAAIuP,cAAc,KAAKvP,KAAK,CAACP,MAAM,EAAE;MACnC6P,mBAAmB,CAACtP,KAAK,CAACP,MAAM,CAAC;MACjCoI,qBAAqB,CAAC,CAAC,CAAC,CAAC;MACzB,IAAIvK,WAAW,GAAG+R,cAAc,CAACrP,KAAK,CAACP,MAAM,CAAC;MAC9C,IAAIwQ,mBAAmB,GAAGC,qBAAqB,CAAClQ,KAAK,CAACP,MAAM,EAAE6B,cAAc,CAAC;MAC7E,IAAIrK,KAAK,CAACwF,QAAQ,EAAE;QAClBxF,KAAK,CAACwF,QAAQ,CAAC;UACbwD,aAAa,EAAED,KAAK,CAACC,aAAa;UAClCpL,KAAK,EAAEyI,WAAW;UAClBuO,eAAe,EAAE,SAASA,eAAeA,CAAA,EAAG;YAC1C7L,KAAK,CAACC,aAAa,CAAC4L,eAAe,CAAC,CAAC;UACvC,CAAC;UACDJ,cAAc,EAAE,SAASA,cAAcA,CAAA,EAAG;YACxCzL,KAAK,CAACC,aAAa,CAACwL,cAAc,CAAC,CAAC;UACtC,CAAC;UACDzC,MAAM,EAAE;YACNhT,IAAI,EAAEiB,KAAK,CAACjB,IAAI;YAChBkG,EAAE,EAAEjF,KAAK,CAACiF,EAAE;YACZrH,KAAK,EAAEyI;UACT;QACF,CAAC,CAAC;MACJ;MACAqF,wBAAwB,CAAC3C,KAAK,CAACC,aAAa,EAAEgQ,mBAAmB,CAAC;IACpE;EACF,CAAC;EACD,IAAIhO,sBAAsB,GAAG,SAASA,sBAAsBA,CAAC5E,OAAO,EAAE;IACpEA,OAAO,GAAGA,OAAO,IAAIiE,cAAc;IACnC,IAAIjE,OAAO,EAAE;MACX,IAAIpG,KAAK,CAACmB,gBAAgB,EAAE;QAC1B,KAAK,IAAI9D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+I,OAAO,CAAC5J,MAAM,EAAEa,CAAC,EAAE,EAAE;UACvC,IAAI2b,mBAAmB,GAAGC,qBAAqB,CAACjZ,KAAK,CAACpC,KAAK,EAAEmV,sBAAsB,CAAC3M,OAAO,CAAC/I,CAAC,CAAC,CAAC,CAAC;UAChG,IAAI2b,mBAAmB,KAAK,CAAC,CAAC,EAAE;YAC9B,OAAO;cACL9M,KAAK,EAAE7O,CAAC;cACRmL,MAAM,EAAEwQ;YACV,CAAC;UACH;QACF;MACF,CAAC,MAAM;QACL,OAAOC,qBAAqB,CAACjZ,KAAK,CAACpC,KAAK,EAAEwI,OAAO,CAAC;MACpD;IACF;IACA,OAAO,CAAC,CAAC;EACX,CAAC;EACD,IAAI8S,WAAW,GAAG,SAASA,WAAWA,CAAA,EAAG;IACvC,OAAOlZ,KAAK,CAACqG,WAAW,GAAG,IAAI,GAAGrG,KAAK,CAACsE,OAAO;EACjD,CAAC;EACD,IAAI2U,qBAAqB,GAAG,SAASA,qBAAqBA,CAACrb,KAAK,EAAEkE,IAAI,EAAE;IACtE,IAAI8G,GAAG,GAAGsQ,WAAW,CAAC,CAAC;IACvB,OAAOpX,IAAI,CAACiV,SAAS,CAAC,UAAU3U,IAAI,EAAE;MACpC,OAAO9G,WAAW,CAAC6d,MAAM,CAACvb,KAAK,EAAEwa,cAAc,CAAChW,IAAI,CAAC,EAAEwG,GAAG,CAAC;IAC7D,CAAC,CAAC;EACJ,CAAC;EACD,IAAI+D,UAAU,GAAG,SAASA,UAAUA,CAACnE,MAAM,EAAE;IAC3C,OAAOlN,WAAW,CAAC6d,MAAM,CAACnZ,KAAK,CAACpC,KAAK,EAAEwa,cAAc,CAAC5P,MAAM,CAAC,EAAE0Q,WAAW,CAAC,CAAC,CAAC;EAC/E,CAAC;EACD,IAAI3E,IAAI,GAAG,SAASA,IAAIA,CAAA,EAAG;IACzB3D,qBAAqB,CAACpO,kBAAkB,KAAK,CAAC,CAAC,GAAGA,kBAAkB,GAAGxC,KAAK,CAACgE,eAAe,GAAGgT,2BAA2B,CAAC,CAAC,GAAGhX,KAAK,CAACa,QAAQ,GAAG,CAAC,CAAC,GAAGiW,uBAAuB,CAAC,CAAC,CAAC;IAC/K/F,sBAAsB,CAAC,IAAI,CAAC;EAC9B,CAAC;EACD,IAAIqB,IAAI,GAAG,SAASA,IAAIA,CAAA,EAAG;IACzBrB,sBAAsB,CAAC,KAAK,CAAC;IAC7BC,UAAU,CAAClG,OAAO,GAAG,KAAK;EAC5B,CAAC;EACD,IAAIlF,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAG;IAC/B,IAAI5F,KAAK,CAACa,QAAQ,IAAI,CAACX,mBAAmB,IAAI8Q,UAAU,CAAClG,OAAO,KAAK,KAAK,EAAE;MAC1EvP,UAAU,CAAC6P,KAAK,CAACjG,QAAQ,CAAC2F,OAAO,CAAC;IACpC;EACF,CAAC;EACD,IAAIsO,cAAc,GAAG,SAASA,cAAcA,CAACN,QAAQ,EAAE;IACrDrd,WAAW,CAAC4d,GAAG,CAAC,SAAS,EAAEnI,UAAU,CAACpG,OAAO,EAAE3K,OAAO,IAAIA,OAAO,CAACmZ,UAAU,IAAIpf,UAAU,CAACof,UAAU,EAAEnZ,OAAO,IAAIA,OAAO,CAACoZ,MAAM,CAACvH,OAAO,IAAI9X,UAAU,CAACqf,MAAM,CAACvH,OAAO,CAAC;IACtKzW,UAAU,CAACie,SAAS,CAACtI,UAAU,CAACpG,OAAO,EAAE;MACvC2O,QAAQ,EAAE,UAAU;MACpBC,GAAG,EAAE,GAAG;MACRC,IAAI,EAAE;IACR,CAAC,CAAC;IACFpH,YAAY,CAAC,CAAC;IACduG,QAAQ,IAAIA,QAAQ,CAAC,CAAC;EACxB,CAAC;EACD,IAAIc,gBAAgB,GAAG,SAASA,gBAAgBA,CAACd,QAAQ,EAAE;IACzDA,QAAQ,IAAIA,QAAQ,CAAC,CAAC;IACtBpG,mBAAmB,CAAC,CAAC;IACrB1S,KAAK,CAAC+F,MAAM,IAAI/F,KAAK,CAAC+F,MAAM,CAAC,CAAC;EAChC,CAAC;EACD,IAAI8T,aAAa,GAAG,SAASA,aAAaA,CAAA,EAAG;IAC3ClH,qBAAqB,CAAC,CAAC;EACzB,CAAC;EACD,IAAImH,eAAe,GAAG,SAASA,eAAeA,CAAA,EAAG;IAC/C,IAAI9Z,KAAK,CAACT,MAAM,IAAIS,KAAK,CAACyG,iBAAiB,EAAE;MAC3CiE,WAAW,CAAC,CAAC;IACf;IACAjP,WAAW,CAACsd,KAAK,CAAC7H,UAAU,CAACpG,OAAO,CAAC;IACrC9K,KAAK,CAAC6F,MAAM,IAAI7F,KAAK,CAAC6F,MAAM,CAAC,CAAC;EAChC,CAAC;EACD,IAAI0M,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAG;IACzChX,UAAU,CAACgX,YAAY,CAACrB,UAAU,CAACpG,OAAO,EAAE3F,QAAQ,CAAC2F,OAAO,CAACqJ,aAAa,EAAEnU,KAAK,CAAC6D,QAAQ,IAAI1D,OAAO,IAAIA,OAAO,CAAC0D,QAAQ,IAAI3J,UAAU,CAAC2J,QAAQ,CAAC;EACnJ,CAAC;EACD,IAAIkW,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAG;IACzC,IAAIlC,WAAW,GAAGtc,UAAU,CAACuc,UAAU,CAAC5G,UAAU,CAACpG,OAAO,EAAE,2BAA2B,CAAC;IACxF,IAAI+M,WAAW,IAAIA,WAAW,CAACmC,cAAc,EAAE;MAC7CnC,WAAW,CAACmC,cAAc,CAAC;QACzBC,KAAK,EAAE,SAAS;QAChBC,MAAM,EAAE;MACV,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAIC,aAAa,GAAG5e,UAAU,CAACuc,UAAU,CAAC5G,UAAU,CAACpG,OAAO,EAAE,6BAA6B,CAAC;MAC5F,IAAIqP,aAAa,IAAIA,aAAa,CAACH,cAAc,EAAE;QACjDG,aAAa,CAACH,cAAc,CAAC;UAC3BC,KAAK,EAAE,SAAS;UAChBC,MAAM,EAAE;QACV,CAAC,CAAC;MACJ;IACF;EACF,CAAC;EACD,IAAI7B,mBAAmB,GAAG,SAASA,mBAAmBA,CAAC7P,MAAM,EAAE;IAC7D,IAAIrD,QAAQ,CAAC2F,OAAO,EAAE;MACpB3F,QAAQ,CAAC2F,OAAO,CAAClN,KAAK,GAAG4K,MAAM,GAAGiE,cAAc,CAACjE,MAAM,CAAC,GAAGxI,KAAK,CAACpC,KAAK,IAAI,EAAE;;MAE5E;MACA,IAAImH,aAAa,CAAC+F,OAAO,EAAE;QACzB/F,aAAa,CAAC+F,OAAO,CAAClN,KAAK,GAAGuH,QAAQ,CAAC2F,OAAO,CAAClN,KAAK;MACtD;IACF;EACF,CAAC;EACD,IAAI6O,cAAc,GAAG,SAASA,cAAcA,CAACjE,MAAM,EAAE;IACnD,IAAIlN,WAAW,CAAC8e,QAAQ,CAAC5R,MAAM,CAAC,EAAE;MAChC,OAAO,EAAE,CAACa,MAAM,CAACb,MAAM,CAAC;IAC1B;IACA,IAAIrC,WAAW,GAAGnG,KAAK,CAACmG,WAAW,GAAG7K,WAAW,CAAC+e,gBAAgB,CAAC7R,MAAM,EAAExI,KAAK,CAACmG,WAAW,CAAC,GAAGqC,MAAM,CAAC,OAAO,CAAC;IAC/G,OAAO,EAAE,CAACa,MAAM,CAAClD,WAAW,CAAC;EAC/B,CAAC;EACD,IAAIiS,cAAc,GAAG,SAASA,cAAcA,CAAC5P,MAAM,EAAE;IACnD,IAAIxI,KAAK,CAACkH,gBAAgB,EAAE;MAC1B,OAAOsB,MAAM;IACf;IACA,IAAInC,WAAW,GAAGrG,KAAK,CAACqG,WAAW,GAAG/K,WAAW,CAAC+e,gBAAgB,CAAC7R,MAAM,EAAExI,KAAK,CAACqG,WAAW,CAAC,GAAGmC,MAAM,GAAGA,MAAM,CAAC,OAAO,CAAC,GAAGlN,WAAW,CAAC+e,gBAAgB,CAAC7R,MAAM,EAAE,OAAO,CAAC;IACxK,OAAOxI,KAAK,CAACqG,WAAW,IAAI/K,WAAW,CAACmF,UAAU,CAAC4F,WAAW,CAAC,GAAGA,WAAW,GAAGmC,MAAM;EACxF,CAAC;EACD,IAAIgE,kBAAkB,GAAG,SAASA,kBAAkBA,CAAChE,MAAM,EAAE;IAC3D,OAAOxI,KAAK,CAACsE,OAAO,GAAGhJ,WAAW,CAAC+e,gBAAgB,CAAC7R,MAAM,EAAExI,KAAK,CAACsE,OAAO,CAAC,GAAGmI,cAAc,CAACjE,MAAM,CAAC;EACrG,CAAC;EACD,IAAImO,aAAa,GAAG,SAASA,aAAaA,CAACnO,MAAM,EAAE;IACjD,OAAOxI,KAAK,CAACmB,gBAAgB,IAAIqH,MAAM,CAAC0D,KAAK;EAC/C,CAAC;EACD,IAAIQ,gBAAgB,GAAG,SAASA,gBAAgBA,CAAClE,MAAM,EAAE;IACvD,IAAIxI,KAAK,CAACgG,cAAc,EAAE;MACxB,OAAO1K,WAAW,CAACgf,UAAU,CAACta,KAAK,CAACgG,cAAc,CAAC,GAAGhG,KAAK,CAACgG,cAAc,CAACwC,MAAM,CAAC,GAAGlN,WAAW,CAAC+e,gBAAgB,CAAC7R,MAAM,EAAExI,KAAK,CAACgG,cAAc,CAAC;IACjJ;IACA,OAAOwC,MAAM,IAAIA,MAAM,CAACpI,QAAQ,KAAK+D,SAAS,GAAGqE,MAAM,CAACpI,QAAQ,GAAG,KAAK;EAC1E,CAAC;EACD,IAAIiM,uBAAuB,GAAG,SAASA,uBAAuBA,CAACkO,WAAW,EAAE;IAC1E,OAAOjf,WAAW,CAAC+e,gBAAgB,CAACE,WAAW,EAAEva,KAAK,CAACmB,gBAAgB,CAAC;EAC1E,CAAC;EACD,IAAIiL,mBAAmB,GAAG,SAASA,mBAAmBA,CAACmO,WAAW,EAAE;IAClE,OAAOjf,WAAW,CAAC+e,gBAAgB,CAACE,WAAW,EAAEva,KAAK,CAACmB,gBAAgB,CAAC;EAC1E,CAAC;EACD,IAAI4R,sBAAsB,GAAG,SAASA,sBAAsBA,CAACwH,WAAW,EAAE;IACxE,OAAOjf,WAAW,CAAC+e,gBAAgB,CAACE,WAAW,EAAEva,KAAK,CAACiG,mBAAmB,CAAC;EAC7E,CAAC;EACD,IAAIuU,gBAAgB,GAAG,SAASA,gBAAgBA,CAAA,EAAG;IACjD,IAAIxa,KAAK,CAACa,QAAQ,IAAIsE,QAAQ,CAAC2F,OAAO,EAAE;MACtC,IAAIlK,KAAK,GAAG0X,cAAc,GAAG7L,cAAc,CAAC6L,cAAc,CAAC,GAAG,IAAI;MAClE,IAAI1a,KAAK,GAAGgD,KAAK,IAAIZ,KAAK,CAACpC,KAAK,IAAI,EAAE;MACtCuH,QAAQ,CAAC2F,OAAO,CAAClN,KAAK,GAAGA,KAAK;;MAE9B;MACA,IAAImH,aAAa,CAAC+F,OAAO,EAAE;QACzB/F,aAAa,CAAC+F,OAAO,CAAClN,KAAK,GAAGA,KAAK;MACrC;IACF;EACF,CAAC;EACD,IAAI6c,iBAAiB,GAAG,SAASA,iBAAiBA,CAAA,EAAG;IACnD,IAAIlY,KAAK,GAAGyI,sBAAsB,CAAChL,KAAK,CAACoG,OAAO,CAAC;IACjD,OAAO7D,KAAK,KAAK,CAAC,CAAC,GAAGvC,KAAK,CAACmB,gBAAgB,GAAG4R,sBAAsB,CAAC/S,KAAK,CAACoG,OAAO,CAAC7D,KAAK,CAAC2J,KAAK,CAAC,CAAC,CAAC3J,KAAK,CAACiG,MAAM,CAAC,GAAGxI,KAAK,CAACoG,OAAO,CAAC7D,KAAK,CAAC,GAAG,IAAI;EAC/I,CAAC;EACDtI,KAAK,CAACygB,mBAAmB,CAACjT,GAAG,EAAE,YAAY;IACzC,OAAO;MACLzH,KAAK,EAAEA,KAAK;MACZuU,IAAI,EAAEA,IAAI;MACVnC,IAAI,EAAEA,IAAI;MACV2G,KAAK,EAAEA,KAAK;MACZ3N,KAAK,EAAE,SAASA,KAAKA,CAAA,EAAG;QACtB,OAAO7P,UAAU,CAAC6P,KAAK,CAACrG,aAAa,CAAC+F,OAAO,CAAC;MAChD,CAAC;MACD6P,UAAU,EAAE,SAASA,UAAUA,CAAA,EAAG;QAChC,OAAO1J,UAAU,CAACnG,OAAO;MAC3B,CAAC;MACD8P,UAAU,EAAE,SAASA,UAAUA,CAAA,EAAG;QAChC,OAAO1J,UAAU,CAACpG,OAAO;MAC3B,CAAC;MACD+P,QAAQ,EAAE,SAASA,QAAQA,CAAA,EAAG;QAC5B,OAAO1V,QAAQ,CAAC2F,OAAO;MACzB,CAAC;MACDgQ,aAAa,EAAE,SAASA,aAAaA,CAAA,EAAG;QACtC,OAAO/V,aAAa,CAAC+F,OAAO;MAC9B,CAAC;MACDiQ,kBAAkB,EAAE,SAASA,kBAAkBA,CAAA,EAAG;QAChD,OAAOlQ,kBAAkB,CAACC,OAAO;MACnC;IACF,CAAC;EACH,CAAC,CAAC;EACF7Q,KAAK,CAAC+gB,SAAS,CAAC,YAAY;IAC1B1f,WAAW,CAAC2f,YAAY,CAAC9V,QAAQ,EAAEnF,KAAK,CAACmF,QAAQ,CAAC;IAClD7J,WAAW,CAAC2f,YAAY,CAAClW,aAAa,EAAE/E,KAAK,CAAC+E,aAAa,CAAC;EAC9D,CAAC,EAAE,CAACI,QAAQ,EAAEnF,KAAK,CAACmF,QAAQ,EAAEJ,aAAa,EAAE/E,KAAK,CAAC+E,aAAa,CAAC,CAAC;EAClEnK,cAAc,CAAC,YAAY;IACzB,IAAIoF,KAAK,CAAC+D,SAAS,EAAE;MACnBxI,UAAU,CAAC6P,KAAK,CAACrG,aAAa,CAAC+F,OAAO,EAAE9K,KAAK,CAAC+D,SAAS,CAAC;IAC1D;IACAwO,YAAY,CAAC,CAAC;EAChB,CAAC,CAAC;EACF1X,eAAe,CAAC,YAAY;IAC1B,IAAIqF,mBAAmB,KAAKF,KAAK,CAACpC,KAAK,IAAI4E,kBAAkB,IAAI,CAAC,CAAC,EAAE;MACnEuX,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAAC7Z,mBAAmB,EAAEF,KAAK,CAACpC,KAAK,EAAE4E,kBAAkB,CAAC,CAAC;EAC1D3H,eAAe,CAAC,YAAY;IAC1B,IAAIqF,mBAAmB,IAAIkQ,WAAW,IAAIpQ,KAAK,CAACT,MAAM,EAAE;MACtDgT,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAACrS,mBAAmB,EAAEkQ,WAAW,EAAEpQ,KAAK,CAACT,MAAM,CAAC,CAAC;EACpD1E,eAAe,CAAC,YAAY;IAC1BgQ,kBAAkB,CAACC,OAAO,IAAID,kBAAkB,CAACC,OAAO,CAACiP,YAAY,CAAC,CAAC,CAAC;EAC1E,CAAC,EAAE,CAAC3J,WAAW,CAAC,CAAC;EACjBvV,eAAe,CAAC,YAAY;IAC1B2f,gBAAgB,CAAC,CAAC;IAClB,IAAIrV,QAAQ,CAAC2F,OAAO,EAAE;MACpB3F,QAAQ,CAAC2F,OAAO,CAACC,aAAa,GAAG,CAAC;IACpC;EACF,CAAC,CAAC;EACFjQ,gBAAgB,CAAC,YAAY;IAC3BW,WAAW,CAACsd,KAAK,CAAC7H,UAAU,CAACpG,OAAO,CAAC;EACvC,CAAC,CAAC;EACF,IAAIoQ,kBAAkB,GAAG,SAASA,kBAAkBA,CAAA,EAAG;IACrD,IAAI1S,MAAM,GAAG;MACX5K,KAAK,EAAE,EAAE;MACTgD,KAAK,EAAEZ,KAAK,CAACc;IACf,CAAC;IACD,IAAIwX,cAAc,EAAE;MAClB,IAAIjS,WAAW,GAAG+R,cAAc,CAACE,cAAc,CAAC;MAChD9P,MAAM,GAAG;QACP5K,KAAK,EAAEd,OAAO,CAACuJ,WAAW,CAAC,KAAK,QAAQ,GAAGrG,KAAK,CAACoG,OAAO,CAAC2Q,SAAS,CAAC,UAAUha,CAAC,EAAE;UAC9E,OAAOA,CAAC,KAAKsJ,WAAW;QAC1B,CAAC,CAAC,GAAGA,WAAW;QAChBzF,KAAK,EAAE6L,cAAc,CAAC6L,cAAc;MACtC,CAAC;IACH;IACA,IAAI6C,0BAA0B,GAAG9S,UAAU,CAAC;MAC1CjE,SAAS,EAAE;IACb,CAAC,EAAEkE,GAAG,CAAC,uBAAuB,CAAC,CAAC;IAChC,IAAI8S,WAAW,GAAG/S,UAAU,CAAC;MAC3BZ,GAAG,EAAEtC,QAAQ;MACbqB,QAAQ,EAAExG,KAAK,CAACwG,QAAQ;MACxB6U,YAAY,EAAE7S,MAAM,CAAC5K,KAAK;MAC1BmB,IAAI,EAAEiB,KAAK,CAACjB,IAAI;MAChB+H,QAAQ,EAAE,CAAC;IACb,CAAC,EAAEwB,GAAG,CAAC,QAAQ,CAAC,CAAC;IACjB,IAAIgT,WAAW,GAAGjT,UAAU,CAAC;MAC3BzK,KAAK,EAAE4K,MAAM,CAAC5K;IAChB,CAAC,EAAE0K,GAAG,CAAC,QAAQ,CAAC,CAAC;IACjB,OAAO,aAAarO,KAAK,CAAC2N,aAAa,CAAC,KAAK,EAAEuT,0BAA0B,EAAE,aAAalhB,KAAK,CAAC2N,aAAa,CAAC,QAAQ,EAAEwT,WAAW,EAAE,aAAanhB,KAAK,CAAC2N,aAAa,CAAC,QAAQ,EAAE0T,WAAW,EAAE9S,MAAM,CAAC5H,KAAK,CAAC,CAAC,CAAC;EAC5M,CAAC;EACD,IAAI2a,oBAAoB,GAAG,SAASA,oBAAoBA,CAAA,EAAG;IACzD,IAAI3d,KAAK,GAAGtC,WAAW,CAACmF,UAAU,CAAC6X,cAAc,CAAC,GAAG7L,cAAc,CAAC6L,cAAc,CAAC,GAAG,IAAI;IAC1F,IAAItY,KAAK,CAACa,QAAQ,EAAE;MAClBjD,KAAK,GAAGA,KAAK,IAAIoC,KAAK,CAACpC,KAAK,IAAI,EAAE;IACpC;IACA,IAAIud,0BAA0B,GAAG9S,UAAU,CAAC;MAC1CjE,SAAS,EAAE;IACb,CAAC,EAAEkE,GAAG,CAAC,uBAAuB,CAAC,CAAC;IAChC,IAAIkT,UAAU,GAAGnT,UAAU,CAAC0H,aAAa,CAAC;MACxCtI,GAAG,EAAE1C,aAAa;MAClBE,EAAE,EAAEjF,KAAK,CAACkF,OAAO;MACjBmW,YAAY,EAAEzd,KAAK;MACnB8P,IAAI,EAAE,MAAM;MACZ+N,QAAQ,EAAE,IAAI;MACd,eAAe,EAAE,SAAS;MAC1B7V,OAAO,EAAE6O,YAAY;MACrBlP,MAAM,EAAEmP,WAAW;MACnBnL,SAAS,EAAEb,cAAc;MACzBtI,QAAQ,EAAEJ,KAAK,CAACI,QAAQ;MACxB0G,QAAQ,EAAE,CAAC9G,KAAK,CAACI,QAAQ,GAAGJ,KAAK,CAAC8G,QAAQ,IAAI,CAAC,GAAG,CAAC;IACrD,CAAC,EAAE4U,SAAS,CAAC,EAAEpT,GAAG,CAAC,OAAO,CAAC,CAAC;IAC5B,OAAO,aAAarO,KAAK,CAAC2N,aAAa,CAAC,KAAK,EAAEuT,0BAA0B,EAAE,aAAalhB,KAAK,CAAC2N,aAAa,CAAC,OAAO,EAAE4T,UAAU,CAAC,CAAC;EACnI,CAAC;EACD,IAAIG,WAAW,GAAG,SAASA,WAAWA,CAAA,EAAG;IACvC,IAAI/a,KAAK,GAAGtF,WAAW,CAACmF,UAAU,CAAC6X,cAAc,CAAC,GAAG7L,cAAc,CAAC6L,cAAc,CAAC,GAAG,IAAI;IAC1F,IAAItY,KAAK,CAACa,QAAQ,EAAE;MAClB,IAAIjD,KAAK,GAAGgD,KAAK,IAAIZ,KAAK,CAACpC,KAAK,IAAI,EAAE;MACtC,IAAIge,WAAW,GAAGvT,UAAU,CAAC0H,aAAa,CAAC;QACzCtI,GAAG,EAAEtC,QAAQ;QACbuI,IAAI,EAAE,MAAM;QACZ2N,YAAY,EAAEzd,KAAK;QACnBwG,SAAS,EAAEmE,EAAE,CAAC,OAAO,EAAE;UACrB3H,KAAK,EAAEA;QACT,CAAC,CAAC;QACFR,QAAQ,EAAEJ,KAAK,CAACI,QAAQ;QACxBU,WAAW,EAAEd,KAAK,CAACc,WAAW;QAC9BwE,SAAS,EAAEtF,KAAK,CAACsF,SAAS;QAC1BuW,OAAO,EAAElD,qBAAqB;QAC9B/S,OAAO,EAAEiT,oBAAoB;QAC7BtP,SAAS,EAAEb,cAAc;QACzBnD,MAAM,EAAEmP,WAAW;QACnB5N,QAAQ,EAAE,CAAC9G,KAAK,CAACI,QAAQ,GAAGJ,KAAK,CAAC8G,QAAQ,IAAI,CAAC,GAAG,CAAC,CAAC;QACpD,eAAe,EAAE;MACnB,CAAC,EAAE4U,SAAS,CAAC,EAAEpT,GAAG,CAAC,OAAO,CAAC,CAAC;MAC5B,OAAO,aAAarO,KAAK,CAAC2N,aAAa,CAAC,OAAO,EAAEgU,WAAW,CAAC;IAC/D;IACA,IAAI3S,OAAO,GAAGjJ,KAAK,CAACmH,aAAa,GAAG7L,WAAW,CAAC6N,aAAa,CAACnJ,KAAK,CAACmH,aAAa,EAAEmR,cAAc,EAAEtY,KAAK,CAAC,GAAGY,KAAK,IAAIZ,KAAK,CAACc,WAAW,IAAId,KAAK,CAACgB,YAAY,IAAI,aAAa/G,KAAK,CAAC2N,aAAa,CAAC3N,KAAK,CAAC6hB,QAAQ,EAAE,IAAI,EAAE,MAAM,CAAC;IAC9N,IAAIN,UAAU,GAAGnT,UAAU,CAAC;MAC1BZ,GAAG,EAAEtC,QAAQ;MACbf,SAAS,EAAEmE,EAAE,CAAC,OAAO,EAAE;QACrB3H,KAAK,EAAEA;MACT,CAAC,CAAC;MACFkG,QAAQ,EAAE;IACZ,CAAC,EAAEwB,GAAG,CAAC,OAAO,CAAC,CAAC;IAChB,OAAO,aAAarO,KAAK,CAAC2N,aAAa,CAAC,MAAM,EAAE4T,UAAU,EAAEvS,OAAO,CAAC;EACtE,CAAC;EACD,IAAI8S,kBAAkB,GAAG,SAASA,kBAAkBA,CAAChT,KAAK,EAAE;IAC1D,IAAIA,KAAK,CAACH,GAAG,KAAK,OAAO,IAAIG,KAAK,CAACmM,IAAI,KAAK,OAAO,EAAE;MACnD6D,KAAK,CAAChQ,KAAK,CAAC;MACZA,KAAK,CAACyL,cAAc,CAAC,CAAC;IACxB;EACF,CAAC;EACD,IAAIwH,eAAe,GAAG,SAASA,eAAeA,CAAA,EAAG;IAC/C,IAAIhc,KAAK,CAACpC,KAAK,IAAI,IAAI,IAAIoC,KAAK,CAACQ,SAAS,IAAI,CAACR,KAAK,CAACI,QAAQ,IAAI,CAAC9E,WAAW,CAAC2gB,OAAO,CAACjc,KAAK,CAACoG,OAAO,CAAC,EAAE;MACpG,IAAI8G,cAAc,GAAG7E,UAAU,CAAC;QAC9BjE,SAAS,EAAEmE,EAAE,CAAC,WAAW,CAAC;QAC1B2T,WAAW,EAAEnD,KAAK;QAClBjS,QAAQ,EAAE9G,KAAK,CAACa,QAAQ,GAAG,CAAC,CAAC,GAAGb,KAAK,CAAC8G,QAAQ,IAAI,GAAG;QACrDyC,SAAS,EAAEwS,kBAAkB;QAC7B,YAAY,EAAE1hB,YAAY,CAAC,OAAO;MACpC,CAAC,EAAEiO,GAAG,CAAC,WAAW,CAAC,CAAC;MACpB,IAAI8E,IAAI,GAAGpN,KAAK,CAACuB,SAAS,IAAI,aAAatH,KAAK,CAAC2N,aAAa,CAAC1M,SAAS,EAAEgS,cAAc,CAAC;MACzF,OAAO1R,SAAS,CAAC6R,UAAU,CAACD,IAAI,EAAE2C,aAAa,CAAC,CAAC,CAAC,EAAE7C,cAAc,CAAC,EAAE;QACnElN,KAAK,EAAEA;MACT,CAAC,CAAC;IACJ;IACA,OAAO,IAAI;EACb,CAAC;EACD,IAAImc,iBAAiB,GAAG,SAASA,iBAAiBA,CAAA,EAAG;IACnD,IAAIC,gBAAgB,GAAG/T,UAAU,CAAC;MAChCjE,SAAS,EAAEmE,EAAE,CAAC,aAAa,CAAC;MAC5B,yBAAyB,EAAErI;IAC7B,CAAC,EAAEoI,GAAG,CAAC,aAAa,CAAC,CAAC;IACtB,IAAI8E,IAAI,GAAGpN,KAAK,CAACsB,WAAW,IAAI,aAAarH,KAAK,CAAC2N,aAAa,CAAC3M,WAAW,EAAE;MAC5EohB,IAAI,EAAE;IACR,CAAC,CAAC;IACF,IAAI/a,WAAW,GAAG9F,SAAS,CAAC6R,UAAU,CAACD,IAAI,EAAE2C,aAAa,CAAC,CAAC,CAAC,EAAEqM,gBAAgB,CAAC,EAAE;MAChFpc,KAAK,EAAEA;IACT,CAAC,CAAC;IACF,IAAI5F,SAAS,GAAG4F,KAAK,CAACc,WAAW,IAAId,KAAK,CAAC5F,SAAS;IACpD,IAAIkiB,kBAAkB,GAAGjU,UAAU,CAAC;MAClCjE,SAAS,EAAEmE,EAAE,CAAC,SAAS,CAAC;MACxBe,IAAI,EAAE,QAAQ;MACd,eAAe,EAAE,SAAS;MAC1B,eAAe,EAAEpJ,mBAAmB;MACpC,YAAY,EAAE9F;IAChB,CAAC,EAAEkO,GAAG,CAAC,SAAS,CAAC,CAAC;IAClB,OAAO,aAAarO,KAAK,CAAC2N,aAAa,CAAC,KAAK,EAAE0U,kBAAkB,EAAEhb,WAAW,CAAC;EACjF,CAAC;EACD,IAAIib,kBAAkB,GAAG,SAASA,kBAAkBA,CAAA,EAAG;IACrD,IAAIC,iBAAiB,GAAGnU,UAAU,CAAC;MACjCjE,SAAS,EAAEmE,EAAE,CAAC,cAAc,CAAC;MAC7B,yBAAyB,EAAErI;IAC7B,CAAC,EAAEoI,GAAG,CAAC,cAAc,CAAC,CAAC;IACvB,IAAI8E,IAAI,GAAG,CAAClN,mBAAmB,GAAGF,KAAK,CAACqB,YAAY,IAAI,aAAapH,KAAK,CAAC2N,aAAa,CAAC7M,eAAe,EAAEyhB,iBAAiB,CAAC,GAAGxc,KAAK,CAACqE,YAAY,IAAI,aAAapK,KAAK,CAAC2N,aAAa,CAAC5M,aAAa,EAAEwhB,iBAAiB,CAAC;IACvN,IAAInb,YAAY,GAAG7F,SAAS,CAAC6R,UAAU,CAACD,IAAI,EAAE2C,aAAa,CAAC,CAAC,CAAC,EAAEyM,iBAAiB,CAAC,EAAE;MAClFxc,KAAK,EAAEA;IACT,CAAC,CAAC;IACF,IAAI5F,SAAS,GAAG4F,KAAK,CAACc,WAAW,IAAId,KAAK,CAAC5F,SAAS;IACpD,IAAIqiB,YAAY,GAAGpU,UAAU,CAAC;MAC5BjE,SAAS,EAAEmE,EAAE,CAAC,SAAS,CAAC;MACxBe,IAAI,EAAE,QAAQ;MACd,eAAe,EAAE,SAAS;MAC1B,eAAe,EAAEpJ,mBAAmB;MACpC,YAAY,EAAE9F;IAChB,CAAC,EAAEkO,GAAG,CAAC,SAAS,CAAC,CAAC;IAClB,OAAO,aAAarO,KAAK,CAAC2N,aAAa,CAAC,KAAK,EAAE6U,YAAY,EAAEpb,YAAY,CAAC;EAC5E,CAAC;EACD,IAAIgJ,cAAc,GAAG2I,iBAAiB,CAAC,CAAC;EACxC,IAAIsF,cAAc,GAAGmC,iBAAiB,CAAC,CAAC;EACxC,IAAIiC,UAAU,GAAGphB,WAAW,CAACmF,UAAU,CAACT,KAAK,CAAC+G,OAAO,CAAC;EACtD,IAAI4V,UAAU,GAAGnZ,YAAY,CAACoZ,aAAa,CAAC5c,KAAK,CAAC;EAClD,IAAI0b,SAAS,GAAGpgB,WAAW,CAACuhB,UAAU,CAACF,UAAU,EAAEphB,UAAU,CAACuhB,UAAU,CAAC;EACzE,IAAIC,YAAY,GAAG7B,kBAAkB,CAAC,CAAC;EACvC,IAAI8B,cAAc,GAAGzB,oBAAoB,CAAC,CAAC;EAC3C,IAAI0B,YAAY,GAAGtB,WAAW,CAAC,CAAC;EAChC,IAAIta,YAAY,GAAGrB,KAAK,CAACqF,OAAO,GAAG8W,iBAAiB,CAAC,CAAC,GAAGI,kBAAkB,CAAC,CAAC;EAC7E,IAAIhb,SAAS,GAAGya,eAAe,CAAC,CAAC;EACjC,IAAIkB,SAAS,GAAG7U,UAAU,CAAC;IACzBpD,EAAE,EAAEjF,KAAK,CAACiF,EAAE;IACZwC,GAAG,EAAEwJ,UAAU;IACf7M,SAAS,EAAE/I,UAAU,CAAC2E,KAAK,CAACoE,SAAS,EAAEmE,EAAE,CAAC,MAAM,EAAE;MAChDpI,OAAO,EAAEA,OAAO;MAChBF,YAAY,EAAEA,YAAY;MAC1BC,mBAAmB,EAAEA;IACvB,CAAC,CAAC,CAAC;IACH2G,KAAK,EAAE7G,KAAK,CAAC6G,KAAK;IAClBpB,OAAO,EAAE,SAASA,OAAOA,CAACnJ,CAAC,EAAE;MAC3B,OAAOwM,QAAQ,CAACxM,CAAC,CAAC;IACpB,CAAC;IACDwJ,WAAW,EAAE9F,KAAK,CAAC8F,WAAW;IAC9BJ,aAAa,EAAE1F,KAAK,CAAC0F,aAAa;IAClCE,OAAO,EAAEA,OAAO;IAChB,iBAAiB,EAAE5F,KAAK,CAACI,QAAQ;IACjC,cAAc,EAAEH,YAAY;IAC5B,uBAAuB,EAAEA,YAAY,GAAG,eAAe,CAACoJ,MAAM,CAAC7G,kBAAkB,CAAC,GAAG2B;EACvF,CAAC,EAAEwY,UAAU,EAAErU,GAAG,CAAC,MAAM,CAAC,CAAC;EAC3B,IAAI6U,gCAAgC,GAAG9U,UAAU,CAAC;IAChDZ,GAAG,EAAE0J,oCAAoC;IACzC7H,IAAI,EAAE,cAAc;IACpBlF,SAAS,EAAE,wCAAwC;IACnD0C,QAAQ,EAAE,GAAG;IACblB,OAAO,EAAEgO,kBAAkB;IAC3B,0BAA0B,EAAE,IAAI;IAChC,yBAAyB,EAAE;EAC7B,CAAC,EAAEtL,GAAG,CAAC,wBAAwB,CAAC,CAAC;EACjC,IAAI8U,+BAA+B,GAAG/U,UAAU,CAAC;IAC/CZ,GAAG,EAAE2J,mCAAmC;IACxC9H,IAAI,EAAE,cAAc;IACpBlF,SAAS,EAAE,wCAAwC;IACnD0C,QAAQ,EAAE,GAAG;IACblB,OAAO,EAAEoO,iBAAiB;IAC1B,0BAA0B,EAAE,IAAI;IAChC,yBAAyB,EAAE;EAC7B,CAAC,EAAE1L,GAAG,CAAC,uBAAuB,CAAC,CAAC;EAChC,OAAO,aAAarO,KAAK,CAAC2N,aAAa,CAAC3N,KAAK,CAAC6hB,QAAQ,EAAE,IAAI,EAAE,aAAa7hB,KAAK,CAAC2N,aAAa,CAAC,KAAK,EAAEsV,SAAS,EAAEF,cAAc,EAAED,YAAY,EAAEE,YAAY,EAAE1b,SAAS,EAAEF,YAAY,EAAE,aAAapH,KAAK,CAAC2N,aAAa,CAACmC,aAAa,EAAE9N,QAAQ,CAAC;IAC7O0O,QAAQ,EAAE,UAAU;IACpBlD,GAAG,EAAEyJ,UAAU;IACf7G,cAAc,EAAEA,cAAc;IAC9BQ,kBAAkB,EAAEA;EACtB,CAAC,EAAE7K,KAAK,EAAE;IACR6D,QAAQ,EAAEA,QAAQ;IAClB0E,EAAE,EAAEA,EAAE;IACNyE,WAAW,EAAEA,WAAW;IACxBxK,kBAAkB,EAAEA,kBAAkB;IACtCuQ,sBAAsB,EAAEA,sBAAsB;IAC9C3G,mBAAmB,EAAEA,mBAAmB;IACxCC,uBAAuB,EAAEA,uBAAuB;IAChDI,cAAc,EAAEA,cAAc;IAC9BD,kBAAkB,EAAEA,kBAAkB;IACtCxB,sBAAsB,EAAEA,sBAAsB;IAC9CV,SAAS,EAAEA,SAAS;IACpB,IAAI,EAAEpK,mBAAmB;IACzBwM,gBAAgB,EAAEA,gBAAgB;IAClCC,UAAU,EAAEA,UAAU;IACtBrB,aAAa,EAAE8G,IAAI;IACnB3M,OAAO,EAAEuP,YAAY;IACrBpK,OAAO,EAAEwO,cAAc;IACvBjO,SAAS,EAAEyO,gBAAgB;IAC3BzK,MAAM,EAAE0K,aAAa;IACrBzK,QAAQ,EAAE0K,eAAe;IACzB3M,sBAAsB,EAAEA,sBAAsB;IAC9C3C,mBAAmB,EAAEA,mBAAmB;IACxCoD,oBAAoB,EAAEA,oBAAoB;IAC1ChB,aAAa,EAAEA,aAAa;IAC5BlE,cAAc,EAAEA,cAAc;IAC9BJ,GAAG,EAAEA,GAAG;IACRoC,WAAW,EAAEA,WAAW;IACxBgB,wBAAwB,EAAEA,wBAAwB;IAClD4D,qBAAqB,EAAE,aAAarV,KAAK,CAAC2N,aAAa,CAAC,MAAM,EAAEuV,gCAAgC,CAAC;IACjG5N,oBAAoB,EAAE,aAAatV,KAAK,CAAC2N,aAAa,CAAC,MAAM,EAAEwV,+BAA+B,CAAC;IAC/FpT,EAAE,EAAEA;EACN,CAAC,CAAC,CAAC,CAAC,EAAE0S,UAAU,IAAI,aAAaziB,KAAK,CAAC2N,aAAa,CAACxM,OAAO,EAAEa,QAAQ,CAAC;IACrE8V,MAAM,EAAEd,UAAU;IAClBhI,OAAO,EAAEjJ,KAAK,CAAC+G,OAAO;IACtB4H,EAAE,EAAErG,GAAG,CAAC,SAAS;EACnB,CAAC,EAAEtI,KAAK,CAACgH,cAAc,CAAC,CAAC,CAAC;AAC5B,CAAC,CAAC,CAAC;AACHgJ,QAAQ,CAAC7H,WAAW,GAAG,UAAU;AAEjC,SAAS6H,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}