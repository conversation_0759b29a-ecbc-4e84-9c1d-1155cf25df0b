{"ast": null, "code": "var _jsxFileName = \"C:\\\\git\\\\Partner-Site\\\\BdoPartner.Plans\\\\Development\\\\ReactClientApp\\\\src\\\\components\\\\dashboard\\\\MyPastPlan.jsx\",\n  _s = $RefreshSig$();\nimport { Card } from \"primereact/card\";\nimport { Button } from \"primereact/button\";\nimport { Dropdown } from \"primereact/dropdown\";\nimport { useState, useMemo } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport APP_CONFIG from \"../../core/config/appConfig\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MyPastPlans = ({\n  myPastPlanYears = []\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const [selectedYear, setSelectedYear] = useState(null);\n\n  // Create year options from the provided years\n  const yearOptions = useMemo(() => {\n    const options = [{\n      label: \"Select\",\n      value: null\n    }];\n\n    // Add years from myPastPlanYears (already sorted in descending order from backend)\n    myPastPlanYears.forEach(year => {\n      options.push({\n        label: year.toString(),\n        value: year.toString()\n      });\n    });\n    return options;\n  }, [myPastPlanYears]);\n  const handleViewPlan = () => {\n    if (selectedYear) {\n      navigate(`/my-partner-plan?year=${selectedYear}`);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"my-past-plans\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"section-title\",\n      children: \"My Past Plans\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"past-plans-card\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"past-plans-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"section-description\",\n          children: \"Access a historical archive of your previously submitted annual plans. Review past submissions to analyze trends, reference prior strategies, and ensure consistency in planning across years.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 11\n        }, this), myPastPlanYears.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"past-plans-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"year-select\",\n              children: \"Select Year:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 43,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n              id: \"year-select\",\n              value: selectedYear,\n              options: yearOptions,\n              onChange: e => setSelectedYear(e.value),\n              placeholder: \"Select\",\n              className: \"year-dropdown\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 44,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"action-section\",\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              label: \"View Plan\",\n              className: \"p-button-primary-rounded\",\n              icon: \"pi pi-eye\",\n              rounded: true,\n              onClick: handleViewPlan,\n              disabled: !selectedYear\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 55,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"no-past-plans\",\n          style: {\n            textAlign: \"center\",\n            padding: \"2rem\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"pi pi-info-circle\",\n            style: {\n              fontSize: \"2rem\",\n              color: \"#6c757d\",\n              marginBottom: \"1rem\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: \"#6c757d\",\n              margin: 0\n            },\n            children: \"No past plans available. Past plans will appear here once questionnaires are closed.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 31,\n    columnNumber: 5\n  }, this);\n};\n_s(MyPastPlans, \"2iTVhL9b1Qh4gM6xh1hxY5zNajk=\", false, function () {\n  return [useNavigate];\n});\n_c = MyPastPlans;\nexport default MyPastPlans;\nvar _c;\n$RefreshReg$(_c, \"MyPastPlans\");", "map": {"version": 3, "names": ["Card", "<PERSON><PERSON>", "Dropdown", "useState", "useMemo", "useNavigate", "APP_CONFIG", "jsxDEV", "_jsxDEV", "MyPastPlans", "myPastPlanYears", "_s", "navigate", "selected<PERSON>ear", "setSelectedYear", "yearOptions", "options", "label", "value", "for<PERSON>ach", "year", "push", "toString", "handleViewPlan", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "htmlFor", "id", "onChange", "e", "placeholder", "icon", "rounded", "onClick", "disabled", "style", "textAlign", "padding", "fontSize", "color", "marginBottom", "margin", "_c", "$RefreshReg$"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/components/dashboard/MyPastPlan.jsx"], "sourcesContent": ["import { Card } from \"primereact/card\";\r\nimport { But<PERSON> } from \"primereact/button\";\r\nimport { Dropdown } from \"primereact/dropdown\";\r\nimport { useState, useMemo } from \"react\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport APP_CONFIG from \"../../core/config/appConfig\";\r\n\r\nconst MyPastPlans = ({ myPastPlanYears = [] }) => {\r\n  const navigate = useNavigate();\r\n  const [selectedYear, setSelectedYear] = useState(null);\r\n\r\n  // Create year options from the provided years\r\n  const yearOptions = useMemo(() => {\r\n    const options = [{ label: \"Select\", value: null }];\r\n\r\n    // Add years from myPastPlanYears (already sorted in descending order from backend)\r\n    myPastPlanYears.forEach((year) => {\r\n      options.push({ label: year.toString(), value: year.toString() });\r\n    });\r\n\r\n    return options;\r\n  }, [myPastPlanYears]);\r\n\r\n  const handleViewPlan = () => {\r\n    if (selectedYear) {\r\n      navigate(`/my-partner-plan?year=${selectedYear}`);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"my-past-plans\">\r\n      <h2 className=\"section-title\">My Past Plans</h2>\r\n      <Card className=\"past-plans-card\">\r\n        <div className=\"past-plans-content\">\r\n          <p className=\"section-description\">\r\n            Access a historical archive of your previously submitted annual plans. Review past submissions to analyze trends, reference prior\r\n            strategies, and ensure consistency in planning across years.\r\n          </p>\r\n\r\n          {myPastPlanYears.length > 0 ? (\r\n            <div className=\"past-plans-header\">\r\n              <div className=\"filter-section\">\r\n                <label htmlFor=\"year-select\">Select Year:</label>\r\n                <Dropdown\r\n                  id=\"year-select\"\r\n                  value={selectedYear}\r\n                  options={yearOptions}\r\n                  onChange={(e) => setSelectedYear(e.value)}\r\n                  placeholder=\"Select\"\r\n                  className=\"year-dropdown\"\r\n                />\r\n              </div>\r\n\r\n              <div className=\"action-section\">\r\n                <Button\r\n                  label=\"View Plan\"\r\n                  className=\"p-button-primary-rounded\"\r\n                  icon=\"pi pi-eye\"\r\n                  rounded\r\n                  onClick={handleViewPlan}\r\n                  disabled={!selectedYear}\r\n                />\r\n              </div>\r\n            </div>\r\n          ) : (\r\n            <div className=\"no-past-plans\" style={{ textAlign: \"center\", padding: \"2rem\" }}>\r\n              <i className=\"pi pi-info-circle\" style={{ fontSize: \"2rem\", color: \"#6c757d\", marginBottom: \"1rem\" }}></i>\r\n              <p style={{ color: \"#6c757d\", margin: 0 }}>No past plans available. Past plans will appear here once questionnaires are closed.</p>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </Card>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default MyPastPlans;\r\n"], "mappings": ";;AAAA,SAASA,IAAI,QAAQ,iBAAiB;AACtC,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,QAAQ,EAAEC,OAAO,QAAQ,OAAO;AACzC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,UAAU,MAAM,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,WAAW,GAAGA,CAAC;EAAEC,eAAe,GAAG;AAAG,CAAC,KAAK;EAAAC,EAAA;EAChD,MAAMC,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACQ,YAAY,EAAEC,eAAe,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;;EAEtD;EACA,MAAMY,WAAW,GAAGX,OAAO,CAAC,MAAM;IAChC,MAAMY,OAAO,GAAG,CAAC;MAAEC,KAAK,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAK,CAAC,CAAC;;IAElD;IACAR,eAAe,CAACS,OAAO,CAAEC,IAAI,IAAK;MAChCJ,OAAO,CAACK,IAAI,CAAC;QAAEJ,KAAK,EAAEG,IAAI,CAACE,QAAQ,CAAC,CAAC;QAAEJ,KAAK,EAAEE,IAAI,CAACE,QAAQ,CAAC;MAAE,CAAC,CAAC;IAClE,CAAC,CAAC;IAEF,OAAON,OAAO;EAChB,CAAC,EAAE,CAACN,eAAe,CAAC,CAAC;EAErB,MAAMa,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIV,YAAY,EAAE;MAChBD,QAAQ,CAAC,yBAAyBC,YAAY,EAAE,CAAC;IACnD;EACF,CAAC;EAED,oBACEL,OAAA;IAAKgB,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAC5BjB,OAAA;MAAIgB,SAAS,EAAC,eAAe;MAAAC,QAAA,EAAC;IAAa;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAChDrB,OAAA,CAACR,IAAI;MAACwB,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eAC/BjB,OAAA;QAAKgB,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjCjB,OAAA;UAAGgB,SAAS,EAAC,qBAAqB;UAAAC,QAAA,EAAC;QAGnC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,EAEHnB,eAAe,CAACoB,MAAM,GAAG,CAAC,gBACzBtB,OAAA;UAAKgB,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCjB,OAAA;YAAKgB,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BjB,OAAA;cAAOuB,OAAO,EAAC,aAAa;cAAAN,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACjDrB,OAAA,CAACN,QAAQ;cACP8B,EAAE,EAAC,aAAa;cAChBd,KAAK,EAAEL,YAAa;cACpBG,OAAO,EAAED,WAAY;cACrBkB,QAAQ,EAAGC,CAAC,IAAKpB,eAAe,CAACoB,CAAC,CAAChB,KAAK,CAAE;cAC1CiB,WAAW,EAAC,QAAQ;cACpBX,SAAS,EAAC;YAAe;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENrB,OAAA;YAAKgB,SAAS,EAAC,gBAAgB;YAAAC,QAAA,eAC7BjB,OAAA,CAACP,MAAM;cACLgB,KAAK,EAAC,WAAW;cACjBO,SAAS,EAAC,0BAA0B;cACpCY,IAAI,EAAC,WAAW;cAChBC,OAAO;cACPC,OAAO,EAAEf,cAAe;cACxBgB,QAAQ,EAAE,CAAC1B;YAAa;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAENrB,OAAA;UAAKgB,SAAS,EAAC,eAAe;UAACgB,KAAK,EAAE;YAAEC,SAAS,EAAE,QAAQ;YAAEC,OAAO,EAAE;UAAO,CAAE;UAAAjB,QAAA,gBAC7EjB,OAAA;YAAGgB,SAAS,EAAC,mBAAmB;YAACgB,KAAK,EAAE;cAAEG,QAAQ,EAAE,MAAM;cAAEC,KAAK,EAAE,SAAS;cAAEC,YAAY,EAAE;YAAO;UAAE;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1GrB,OAAA;YAAGgC,KAAK,EAAE;cAAEI,KAAK,EAAE,SAAS;cAAEE,MAAM,EAAE;YAAE,CAAE;YAAArB,QAAA,EAAC;UAAoF;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChI,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAClB,EAAA,CAnEIF,WAAW;EAAA,QACEJ,WAAW;AAAA;AAAA0C,EAAA,GADxBtC,WAAW;AAqEjB,eAAeA,WAAW;AAAC,IAAAsC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}