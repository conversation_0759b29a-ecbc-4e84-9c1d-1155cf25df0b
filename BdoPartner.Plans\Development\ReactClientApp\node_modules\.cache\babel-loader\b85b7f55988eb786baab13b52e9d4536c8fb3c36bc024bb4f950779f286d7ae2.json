{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { AsyncAction } from './AsyncAction';\nimport { Subscription } from '../Subscription';\nimport { AsyncScheduler } from './AsyncScheduler';\nvar VirtualTimeScheduler = function (_super) {\n  __extends(VirtualTimeScheduler, _super);\n  function VirtualTimeScheduler(schedulerActionCtor, maxFrames) {\n    if (schedulerActionCtor === void 0) {\n      schedulerActionCtor = VirtualAction;\n    }\n    if (maxFrames === void 0) {\n      maxFrames = Infinity;\n    }\n    var _this = _super.call(this, schedulerActionCtor, function () {\n      return _this.frame;\n    }) || this;\n    _this.maxFrames = maxFrames;\n    _this.frame = 0;\n    _this.index = -1;\n    return _this;\n  }\n  VirtualTimeScheduler.prototype.flush = function () {\n    var _a = this,\n      actions = _a.actions,\n      maxFrames = _a.maxFrames;\n    var error;\n    var action;\n    while ((action = actions[0]) && action.delay <= maxFrames) {\n      actions.shift();\n      this.frame = action.delay;\n      if (error = action.execute(action.state, action.delay)) {\n        break;\n      }\n    }\n    if (error) {\n      while (action = actions.shift()) {\n        action.unsubscribe();\n      }\n      throw error;\n    }\n  };\n  VirtualTimeScheduler.frameTimeFactor = 10;\n  return VirtualTimeScheduler;\n}(AsyncScheduler);\nexport { VirtualTimeScheduler };\nvar VirtualAction = function (_super) {\n  __extends(VirtualAction, _super);\n  function VirtualAction(scheduler, work, index) {\n    if (index === void 0) {\n      index = scheduler.index += 1;\n    }\n    var _this = _super.call(this, scheduler, work) || this;\n    _this.scheduler = scheduler;\n    _this.work = work;\n    _this.index = index;\n    _this.active = true;\n    _this.index = scheduler.index = index;\n    return _this;\n  }\n  VirtualAction.prototype.schedule = function (state, delay) {\n    if (delay === void 0) {\n      delay = 0;\n    }\n    if (Number.isFinite(delay)) {\n      if (!this.id) {\n        return _super.prototype.schedule.call(this, state, delay);\n      }\n      this.active = false;\n      var action = new VirtualAction(this.scheduler, this.work);\n      this.add(action);\n      return action.schedule(state, delay);\n    } else {\n      return Subscription.EMPTY;\n    }\n  };\n  VirtualAction.prototype.requestAsyncId = function (scheduler, id, delay) {\n    if (delay === void 0) {\n      delay = 0;\n    }\n    this.delay = scheduler.frame + delay;\n    var actions = scheduler.actions;\n    actions.push(this);\n    actions.sort(VirtualAction.sortActions);\n    return 1;\n  };\n  VirtualAction.prototype.recycleAsyncId = function (scheduler, id, delay) {\n    if (delay === void 0) {\n      delay = 0;\n    }\n    return undefined;\n  };\n  VirtualAction.prototype._execute = function (state, delay) {\n    if (this.active === true) {\n      return _super.prototype._execute.call(this, state, delay);\n    }\n  };\n  VirtualAction.sortActions = function (a, b) {\n    if (a.delay === b.delay) {\n      if (a.index === b.index) {\n        return 0;\n      } else if (a.index > b.index) {\n        return 1;\n      } else {\n        return -1;\n      }\n    } else if (a.delay > b.delay) {\n      return 1;\n    } else {\n      return -1;\n    }\n  };\n  return VirtualAction;\n}(AsyncAction);\nexport { VirtualAction };", "map": {"version": 3, "names": ["AsyncAction", "Subscription", "AsyncScheduler", "VirtualTimeScheduler", "_super", "__extends", "schedulerActionCtor", "maxFrames", "VirtualAction", "Infinity", "_this", "call", "frame", "index", "prototype", "flush", "_a", "actions", "error", "action", "delay", "shift", "execute", "state", "unsubscribe", "frameTimeFactor", "scheduler", "work", "active", "schedule", "Number", "isFinite", "id", "add", "EMPTY", "requestAsyncId", "push", "sort", "sortActions", "recycleAsyncId", "undefined", "_execute", "a", "b"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\scheduler\\VirtualTimeScheduler.ts"], "sourcesContent": ["import { AsyncAction } from './AsyncAction';\nimport { Subscription } from '../Subscription';\nimport { AsyncScheduler } from './AsyncScheduler';\nimport { SchedulerAction } from '../types';\nimport { TimerHandle } from './timerHandle';\n\nexport class VirtualTimeScheduler extends AsyncScheduler {\n  /** @deprecated Not used in VirtualTimeScheduler directly. Will be removed in v8. */\n  static frameTimeFactor = 10;\n\n  /**\n   * The current frame for the state of the virtual scheduler instance. The difference\n   * between two \"frames\" is synonymous with the passage of \"virtual time units\". So if\n   * you record `scheduler.frame` to be `1`, then later, observe `scheduler.frame` to be at `11`,\n   * that means `10` virtual time units have passed.\n   */\n  public frame: number = 0;\n\n  /**\n   * Used internally to examine the current virtual action index being processed.\n   * @deprecated Internal implementation detail, do not use directly. Will be made internal in v8.\n   */\n  public index: number = -1;\n\n  /**\n   * This creates an instance of a `VirtualTimeScheduler`. Experts only. The signature of\n   * this constructor is likely to change in the long run.\n   *\n   * @param schedulerActionCtor The type of Action to initialize when initializing actions during scheduling.\n   * @param maxFrames The maximum number of frames to process before stopping. Used to prevent endless flush cycles.\n   */\n  constructor(schedulerActionCtor: typeof AsyncAction = VirtualAction as any, public maxFrames: number = Infinity) {\n    super(schedulerActionCtor, () => this.frame);\n  }\n\n  /**\n   * Prompt the Scheduler to execute all of its queued actions, therefore\n   * clearing its queue.\n   */\n  public flush(): void {\n    const { actions, maxFrames } = this;\n    let error: any;\n    let action: AsyncAction<any> | undefined;\n\n    while ((action = actions[0]) && action.delay <= maxFrames) {\n      actions.shift();\n      this.frame = action.delay;\n\n      if ((error = action.execute(action.state, action.delay))) {\n        break;\n      }\n    }\n\n    if (error) {\n      while ((action = actions.shift())) {\n        action.unsubscribe();\n      }\n      throw error;\n    }\n  }\n}\n\nexport class VirtualAction<T> extends AsyncAction<T> {\n  protected active: boolean = true;\n\n  constructor(\n    protected scheduler: VirtualTimeScheduler,\n    protected work: (this: SchedulerAction<T>, state?: T) => void,\n    protected index: number = (scheduler.index += 1)\n  ) {\n    super(scheduler, work);\n    this.index = scheduler.index = index;\n  }\n\n  public schedule(state?: T, delay: number = 0): Subscription {\n    if (Number.isFinite(delay)) {\n      if (!this.id) {\n        return super.schedule(state, delay);\n      }\n      this.active = false;\n      // If an action is rescheduled, we save allocations by mutating its state,\n      // pushing it to the end of the scheduler queue, and recycling the action.\n      // But since the VirtualTimeScheduler is used for testing, VirtualActions\n      // must be immutable so they can be inspected later.\n      const action = new VirtualAction(this.scheduler, this.work);\n      this.add(action);\n      return action.schedule(state, delay);\n    } else {\n      // If someone schedules something with Infinity, it'll never happen. So we\n      // don't even schedule it.\n      return Subscription.EMPTY;\n    }\n  }\n\n  protected requestAsyncId(scheduler: VirtualTimeScheduler, id?: any, delay: number = 0): TimerHandle {\n    this.delay = scheduler.frame + delay;\n    const { actions } = scheduler;\n    actions.push(this);\n    (actions as Array<VirtualAction<T>>).sort(VirtualAction.sortActions);\n    return 1;\n  }\n\n  protected recycleAsyncId(scheduler: VirtualTimeScheduler, id?: any, delay: number = 0): TimerHandle | undefined {\n    return undefined;\n  }\n\n  protected _execute(state: T, delay: number): any {\n    if (this.active === true) {\n      return super._execute(state, delay);\n    }\n  }\n\n  private static sortActions<T>(a: VirtualAction<T>, b: VirtualAction<T>) {\n    if (a.delay === b.delay) {\n      if (a.index === b.index) {\n        return 0;\n      } else if (a.index > b.index) {\n        return 1;\n      } else {\n        return -1;\n      }\n    } else if (a.delay > b.delay) {\n      return 1;\n    } else {\n      return -1;\n    }\n  }\n}\n"], "mappings": ";AAAA,SAASA,WAAW,QAAQ,eAAe;AAC3C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,cAAc,QAAQ,kBAAkB;AAIjD,IAAAC,oBAAA,aAAAC,MAAA;EAA0CC,SAAA,CAAAF,oBAAA,EAAAC,MAAA;EAyBxC,SAAAD,qBAAYG,mBAA8D,EAASC,SAA4B;IAAnG,IAAAD,mBAAA;MAAAA,mBAAA,GAA0CE,aAAoB;IAAA;IAAS,IAAAD,SAAA;MAAAA,SAAA,GAAAE,QAA4B;IAAA;IAA/G,IAAAC,KAAA,GACEN,MAAA,CAAAO,IAAA,OAAML,mBAAmB,EAAE;MAAM,OAAAI,KAAI,CAACE,KAAK;IAAV,CAAU,CAAC;IADqCF,KAAA,CAAAH,SAAS,GAATA,SAAS;IAfrFG,KAAA,CAAAE,KAAK,GAAW,CAAC;IAMjBF,KAAA,CAAAG,KAAK,GAAW,CAAC,CAAC;;EAWzB;EAMOV,oBAAA,CAAAW,SAAA,CAAAC,KAAK,GAAZ;IACQ,IAAAC,EAAA,GAAyB,IAAI;MAA3BC,OAAO,GAAAD,EAAA,CAAAC,OAAA;MAAEV,SAAS,GAAAS,EAAA,CAAAT,SAAS;IACnC,IAAIW,KAAU;IACd,IAAIC,MAAoC;IAExC,OAAO,CAACA,MAAM,GAAGF,OAAO,CAAC,CAAC,CAAC,KAAKE,MAAM,CAACC,KAAK,IAAIb,SAAS,EAAE;MACzDU,OAAO,CAACI,KAAK,EAAE;MACf,IAAI,CAACT,KAAK,GAAGO,MAAM,CAACC,KAAK;MAEzB,IAAKF,KAAK,GAAGC,MAAM,CAACG,OAAO,CAACH,MAAM,CAACI,KAAK,EAAEJ,MAAM,CAACC,KAAK,CAAC,EAAG;QACxD;;;IAIJ,IAAIF,KAAK,EAAE;MACT,OAAQC,MAAM,GAAGF,OAAO,CAACI,KAAK,EAAE,EAAG;QACjCF,MAAM,CAACK,WAAW,EAAE;;MAEtB,MAAMN,KAAK;;EAEf,CAAC;EAnDMf,oBAAA,CAAAsB,eAAe,GAAG,EAAE;EAoD7B,OAAAtB,oBAAC;CAAA,CAtDyCD,cAAc;SAA3CC,oBAAoB;AAwDjC,IAAAK,aAAA,aAAAJ,MAAA;EAAsCC,SAAA,CAAAG,aAAA,EAAAJ,MAAA;EAGpC,SAAAI,cACYkB,SAA+B,EAC/BC,IAAmD,EACnDd,KAAsC;IAAtC,IAAAA,KAAA;MAAAA,KAAA,GAAiBa,SAAS,CAACb,KAAK,IAAI,CAAE;IAAA;IAHlD,IAAAH,KAAA,GAKEN,MAAA,CAAAO,IAAA,OAAMe,SAAS,EAAEC,IAAI,CAAC;IAJZjB,KAAA,CAAAgB,SAAS,GAATA,SAAS;IACThB,KAAA,CAAAiB,IAAI,GAAJA,IAAI;IACJjB,KAAA,CAAAG,KAAK,GAALA,KAAK;IALPH,KAAA,CAAAkB,MAAM,GAAY,IAAI;IAQ9BlB,KAAI,CAACG,KAAK,GAAGa,SAAS,CAACb,KAAK,GAAGA,KAAK;;EACtC;EAEOL,aAAA,CAAAM,SAAA,CAAAe,QAAQ,GAAf,UAAgBN,KAAS,EAAEH,KAAiB;IAAjB,IAAAA,KAAA;MAAAA,KAAA,IAAiB;IAAA;IAC1C,IAAIU,MAAM,CAACC,QAAQ,CAACX,KAAK,CAAC,EAAE;MAC1B,IAAI,CAAC,IAAI,CAACY,EAAE,EAAE;QACZ,OAAO5B,MAAA,CAAAU,SAAA,CAAMe,QAAQ,CAAAlB,IAAA,OAACY,KAAK,EAAEH,KAAK,CAAC;;MAErC,IAAI,CAACQ,MAAM,GAAG,KAAK;MAKnB,IAAMT,MAAM,GAAG,IAAIX,aAAa,CAAC,IAAI,CAACkB,SAAS,EAAE,IAAI,CAACC,IAAI,CAAC;MAC3D,IAAI,CAACM,GAAG,CAACd,MAAM,CAAC;MAChB,OAAOA,MAAM,CAACU,QAAQ,CAACN,KAAK,EAAEH,KAAK,CAAC;KACrC,MAAM;MAGL,OAAOnB,YAAY,CAACiC,KAAK;;EAE7B,CAAC;EAES1B,aAAA,CAAAM,SAAA,CAAAqB,cAAc,GAAxB,UAAyBT,SAA+B,EAAEM,EAAQ,EAAEZ,KAAiB;IAAjB,IAAAA,KAAA;MAAAA,KAAA,IAAiB;IAAA;IACnF,IAAI,CAACA,KAAK,GAAGM,SAAS,CAACd,KAAK,GAAGQ,KAAK;IAC5B,IAAAH,OAAO,GAAKS,SAAS,CAAAT,OAAd;IACfA,OAAO,CAACmB,IAAI,CAAC,IAAI,CAAC;IACjBnB,OAAmC,CAACoB,IAAI,CAAC7B,aAAa,CAAC8B,WAAW,CAAC;IACpE,OAAO,CAAC;EACV,CAAC;EAES9B,aAAA,CAAAM,SAAA,CAAAyB,cAAc,GAAxB,UAAyBb,SAA+B,EAAEM,EAAQ,EAAEZ,KAAiB;IAAjB,IAAAA,KAAA;MAAAA,KAAA,IAAiB;IAAA;IACnF,OAAOoB,SAAS;EAClB,CAAC;EAEShC,aAAA,CAAAM,SAAA,CAAA2B,QAAQ,GAAlB,UAAmBlB,KAAQ,EAAEH,KAAa;IACxC,IAAI,IAAI,CAACQ,MAAM,KAAK,IAAI,EAAE;MACxB,OAAOxB,MAAA,CAAAU,SAAA,CAAM2B,QAAQ,CAAA9B,IAAA,OAACY,KAAK,EAAEH,KAAK,CAAC;;EAEvC,CAAC;EAEcZ,aAAA,CAAA8B,WAAW,GAA1B,UAA8BI,CAAmB,EAAEC,CAAmB;IACpE,IAAID,CAAC,CAACtB,KAAK,KAAKuB,CAAC,CAACvB,KAAK,EAAE;MACvB,IAAIsB,CAAC,CAAC7B,KAAK,KAAK8B,CAAC,CAAC9B,KAAK,EAAE;QACvB,OAAO,CAAC;OACT,MAAM,IAAI6B,CAAC,CAAC7B,KAAK,GAAG8B,CAAC,CAAC9B,KAAK,EAAE;QAC5B,OAAO,CAAC;OACT,MAAM;QACL,OAAO,CAAC,CAAC;;KAEZ,MAAM,IAAI6B,CAAC,CAACtB,KAAK,GAAGuB,CAAC,CAACvB,KAAK,EAAE;MAC5B,OAAO,CAAC;KACT,MAAM;MACL,OAAO,CAAC,CAAC;;EAEb,CAAC;EACH,OAAAZ,aAAC;AAAD,CAAC,CAjEqCR,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}