{"ast": null, "code": "var _jsxFileName = \"C:\\\\git\\\\Partner-Site\\\\BdoPartner.Plans\\\\Development\\\\ReactClientApp\\\\src\\\\components\\\\dashboard\\\\AdminPartnerPlans.jsx\",\n  _s = $RefreshSig$();\nimport { Card } from 'primereact/card';\nimport { Button } from 'primereact/button';\nimport { useContext, useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { AuthContext } from '../../core/auth/components/authProvider';\nimport { Role } from '../../core/enumertions/role';\nimport partnerAnnualPlanService from '../../services/partnerAnnualPlanService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminPartnerPlans = () => {\n  _s();\n  var _user$roles, _user$roles2;\n  const navigate = useNavigate();\n  const authService = useContext(AuthContext);\n  const user = authService.getUser();\n\n  // State for dashboard summary data - now an array of summaries by year\n  const [yearSummaries, setYearSummaries] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  // Check if user has PPAdministrator or ExecutiveLeadership role\n  const isSystemAdminAndELT = (user === null || user === void 0 ? void 0 : (_user$roles = user.roles) === null || _user$roles === void 0 ? void 0 : _user$roles.includes(Role.PPAdministrator)) || (user === null || user === void 0 ? void 0 : (_user$roles2 = user.roles) === null || _user$roles2 === void 0 ? void 0 : _user$roles2.includes(Role.ExecutiveLeadership));\n\n  // Load admin dashboard summary data\n  useEffect(() => {\n    const loadAdminDashboardSummary = async () => {\n      try {\n        setLoading(true);\n        const response = await partnerAnnualPlanService.getAdminDashboardSummary();\n        if (response.resultStatus === 1 || response.resultStatus === \"Success\") {\n          // Response.item is now a collection of summaries grouped by year\n          setYearSummaries(response.item || []);\n        } else {\n          console.error('Error loading admin dashboard summary:', response.message);\n          setYearSummaries([]);\n        }\n      } catch (error) {\n        console.error('Error loading admin dashboard summary:', error);\n        setYearSummaries([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    // Only load data if user has appropriate roles and is authenticated\n    if (authService !== null && authService !== void 0 && authService.isAuthenticated() && user !== null && user !== void 0 && user.roles && (user.roles.includes(Role.PPAdministrator) || user.roles.includes(Role.ExecutiveLeadership))) {\n      loadAdminDashboardSummary();\n    } else {\n      setLoading(false);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []); // Empty dependency array to run only once on mount\n\n  // Handle View Plans button click for All Partner Plans section\n  const handleAllPartnerPlansViewClick = year => {\n    if (isSystemAdminAndELT) {\n      // Navigate to admin dashboard with specified year\n      navigate(`/admin/partner-annual-plans?year=${year}`);\n    } else {\n      // For non-admin users, you might want to show a message or redirect elsewhere\n      console.log('Access denied: Only system administrators can view all partner plans');\n    }\n  };\n\n  // Helper function to get status color class\n  const getStatusColorClass = statusType => {\n    switch (statusType) {\n      case 'pending':\n        return 'status-pending';\n      // red\n      case 'ready-planning':\n        return 'status-ready-planning';\n      // orange\n      case 'ready-midyear':\n        return 'status-ready-midyear';\n      // yellow\n      case 'ready-yearend':\n        return 'status-ready-yearend';\n      // yellow\n      case 'completed':\n        return 'status-completed';\n      // green\n      case 'exempt':\n        return 'status-exempt';\n      // black\n      default:\n        return '';\n    }\n  };\n\n  // Don't render if user doesn't have admin/ELT roles\n  if (!isSystemAdminAndELT) {\n    return null;\n  }\n\n  // If no admin data found, hide the section\n  if (!loading && (!yearSummaries || yearSummaries.length === 0)) {\n    return null; // Section should be hidden\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"admin-partner-plans\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"section-title\",\n      children: \"All Partner Plans\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"section-description\",\n      children: \"Explore a comprehensive overview of all partner plans giving you full visibility into the planning progress across all service lines.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 7\n    }, this), loading ? /*#__PURE__*/_jsxDEV(Card, {\n      className: \"all-plans-card\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"pi pi-spinner pi-spin\",\n          style: {\n            fontSize: '2rem'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Loading partner plans data...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 9\n    }, this) : yearSummaries.map((summary, index) => /*#__PURE__*/_jsxDEV(Card, {\n      className: \"all-plans-card\",\n      style: {\n        marginBottom: index < yearSummaries.length - 1 ? '2rem' : '0'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"all-plans-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"plan-info-grid\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"plan-info-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"plan-label\",\n              children: \"Year\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"plan-value\",\n              children: summary.year\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"plan-info-item cycles-column\",\n            children: [summary.isPlanningCycleEnabled && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"cycle-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"cycle-label\",\n                children: \"PLANNING:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 116,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"status-text\",\n                children: \"Pending Submission :\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `status-count ${getStatusColorClass('pending')}`,\n                children: summary.planningNotStarted + summary.planningDraft + summary.planningReopened || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 118,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"status-text\",\n                children: \"Ready for Review :\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 121,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `status-count ${getStatusColorClass('ready-planning')}`,\n                children: summary.planningUnderReview || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 122,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"status-text\",\n                children: \"Completed:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 125,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `status-count ${getStatusColorClass('completed')}`,\n                children: summary.planningCompleted || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"status-text\",\n                children: \"Exempt: \"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `status-count ${getStatusColorClass('exempt')}`,\n                children: summary.exemptPartners || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 21\n            }, this), summary.isMidYearCycleEnabled && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"cycle-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"cycle-label\",\n                children: \"MID YEAR:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"status-text\",\n                children: \"Pending Submission :\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `status-count ${getStatusColorClass('pending')}`,\n                children: summary.midYearReviewNotStarted + summary.midYearReviewDraft + summary.midYearReviewReopened || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 141,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"status-text\",\n                children: \"Ready for Review :\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `status-count ${getStatusColorClass('ready-midyear')}`,\n                children: summary.midYearReviewUnderReview || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"status-text\",\n                children: \"Completed:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 148,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `status-count ${getStatusColorClass('completed')}`,\n                children: summary.midYearReviewCompleted || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 21\n            }, this), summary.isYearEndCycleEnabled && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"cycle-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"cycle-label\",\n                children: \"YEAR END:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"status-text\",\n                children: \"Pending Submission :\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `status-count ${getStatusColorClass('pending')}`,\n                children: summary.yearEndReviewNotStarted + summary.yearEndReviewDraft + summary.yearEndReviewReopened || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"status-text\",\n                children: \"Ready for Review :\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `status-count ${getStatusColorClass('ready-yearend')}`,\n                children: summary.yearEndReviewUnderReview || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"status-text\",\n                children: \"Completed:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `status-count ${getStatusColorClass('completed')}`,\n                children: summary.yearEndReviewCompleted || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"plan-actions\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            label: \"View Plans\",\n            className: \"p-button-primary-rounded\",\n            icon: \"pi pi-eye\",\n            rounded: true,\n            onClick: () => handleAllPartnerPlansViewClick(summary.year)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 13\n      }, this)\n    }, `${summary.year}-${summary.questionnaireName}`, false, {\n      fileName: _jsxFileName,\n      lineNumber: 102,\n      columnNumber: 11\n    }, this))]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 87,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminPartnerPlans, \"1UfBgEiFzQriFh5aYA/0Zz+01OE=\", false, function () {\n  return [useNavigate];\n});\n_c = AdminPartnerPlans;\nexport default AdminPartnerPlans;\nvar _c;\n$RefreshReg$(_c, \"AdminPartnerPlans\");", "map": {"version": 3, "names": ["Card", "<PERSON><PERSON>", "useContext", "useState", "useEffect", "useNavigate", "AuthContext", "Role", "partnerAnnualPlanService", "jsxDEV", "_jsxDEV", "AdminPartnerPlans", "_s", "_user$roles", "_user$roles2", "navigate", "authService", "user", "getUser", "yearSummaries", "setYearSummaries", "loading", "setLoading", "isSystemAdminAndELT", "roles", "includes", "PPAdministrator", "ExecutiveLeadership", "loadAdminDashboardSummary", "response", "getAdminDashboardSummary", "resultStatus", "item", "console", "error", "message", "isAuthenticated", "handleAllPartnerPlansViewClick", "year", "log", "getStatusColorClass", "statusType", "length", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "fontSize", "map", "summary", "index", "marginBottom", "isPlanningCycleEnabled", "planningNotStarted", "planningDraft", "planningReopened", "planningUnderReview", "planningCompleted", "exemptPartners", "isMidYearCycleEnabled", "midYearReviewNotStarted", "midYearReviewDraft", "midYearReviewReopened", "midYearReviewUnderReview", "midYearReviewCompleted", "isYearEndCycleEnabled", "yearEndReviewNotStarted", "yearEndReviewDraft", "yearEndReviewReopened", "yearEndReviewUnderReview", "yearEndReviewCompleted", "label", "icon", "rounded", "onClick", "questionnaire<PERSON>ame", "_c", "$RefreshReg$"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/components/dashboard/AdminPartnerPlans.jsx"], "sourcesContent": ["import { Card } from 'primereact/card';\r\nimport { Button } from 'primereact/button';\r\nimport { useContext, useState, useEffect } from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport { AuthContext } from '../../core/auth/components/authProvider';\r\nimport { Role } from '../../core/enumertions/role';\r\nimport partnerAnnualPlanService from '../../services/partnerAnnualPlanService';\r\n\r\nconst AdminPartnerPlans = () => {\r\n  const navigate = useNavigate();\r\n  const authService = useContext(AuthContext);\r\n  const user = authService.getUser();\r\n\r\n  // State for dashboard summary data - now an array of summaries by year\r\n  const [yearSummaries, setYearSummaries] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n\r\n  // Check if user has PPAdministrator or ExecutiveLeadership role\r\n  const isSystemAdminAndELT = user?.roles?.includes(Role.PPAdministrator) || user?.roles?.includes(Role.ExecutiveLeadership);\r\n\r\n  // Load admin dashboard summary data\r\n  useEffect(() => {\r\n    const loadAdminDashboardSummary = async () => {\r\n      try {\r\n        setLoading(true);\r\n        const response = await partnerAnnualPlanService.getAdminDashboardSummary();\r\n\r\n        if (response.resultStatus === 1 || response.resultStatus === \"Success\") {\r\n          // Response.item is now a collection of summaries grouped by year\r\n          setYearSummaries(response.item || []);\r\n        } else {\r\n          console.error('Error loading admin dashboard summary:', response.message);\r\n          setYearSummaries([]);\r\n        }\r\n      } catch (error) {\r\n        console.error('Error loading admin dashboard summary:', error);\r\n        setYearSummaries([]);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    // Only load data if user has appropriate roles and is authenticated\r\n    if (authService?.isAuthenticated() && user?.roles && (user.roles.includes(Role.PPAdministrator) || user.roles.includes(Role.ExecutiveLeadership))) {\r\n      loadAdminDashboardSummary();\r\n    } else {\r\n      setLoading(false);\r\n    }\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, []); // Empty dependency array to run only once on mount\r\n\r\n  // Handle View Plans button click for All Partner Plans section\r\n  const handleAllPartnerPlansViewClick = (year) => {\r\n    if (isSystemAdminAndELT) {\r\n      // Navigate to admin dashboard with specified year\r\n      navigate(`/admin/partner-annual-plans?year=${year}`);\r\n    } else {\r\n      // For non-admin users, you might want to show a message or redirect elsewhere\r\n      console.log('Access denied: Only system administrators can view all partner plans');\r\n    }\r\n  };\r\n\r\n  // Helper function to get status color class\r\n  const getStatusColorClass = (statusType) => {\r\n    switch (statusType) {\r\n      case 'pending': return 'status-pending'; // red\r\n      case 'ready-planning': return 'status-ready-planning'; // orange\r\n      case 'ready-midyear': return 'status-ready-midyear'; // yellow\r\n      case 'ready-yearend': return 'status-ready-yearend'; // yellow\r\n      case 'completed': return 'status-completed'; // green\r\n      case 'exempt': return 'status-exempt'; // black\r\n      default: return '';\r\n    }\r\n  };\r\n\r\n  // Don't render if user doesn't have admin/ELT roles\r\n  if (!isSystemAdminAndELT) {\r\n    return null;\r\n  }\r\n\r\n  // If no admin data found, hide the section\r\n  if (!loading && (!yearSummaries || yearSummaries.length === 0)) {\r\n    return null; // Section should be hidden\r\n  }\r\n\r\n  return (\r\n    <div className=\"admin-partner-plans\">\r\n      <h2 className=\"section-title\">All Partner Plans</h2>\r\n      <p className=\"section-description\">\r\n        Explore a comprehensive overview of all partner plans giving you full visibility into the planning progress across all service lines.\r\n      </p>\r\n\r\n      {loading ? (\r\n        <Card className=\"all-plans-card\">\r\n          <div className=\"loading-content\">\r\n            <i className=\"pi pi-spinner pi-spin\" style={{ fontSize: '2rem' }}></i>\r\n            <p>Loading partner plans data...</p>\r\n          </div>\r\n        </Card>\r\n      ) : (\r\n        yearSummaries.map((summary, index) => (\r\n          <Card key={`${summary.year}-${summary.questionnaireName}`} className=\"all-plans-card\" style={{ marginBottom: index < yearSummaries.length - 1 ? '2rem' : '0' }}>\r\n            <div className=\"all-plans-content\">\r\n              <div className=\"plan-info-grid\">\r\n                {/* Year Column */}\r\n                <div className=\"plan-info-item\">\r\n                  <label className=\"plan-label\">Year</label>\r\n                  <span className=\"plan-value\">{summary.year}</span>\r\n                </div>\r\n\r\n                {/* Cycles Column - All cycles stacked vertically */}\r\n                <div className=\"plan-info-item cycles-column\">\r\n                  {/* Planning Cycle Row */}\r\n                  {summary.isPlanningCycleEnabled && (\r\n                    <div className=\"cycle-row\">\r\n                      <span className=\"cycle-label\">PLANNING:</span>\r\n                      <span className=\"status-text\">Pending Submission :</span>\r\n                      <span className={`status-count ${getStatusColorClass('pending')}`}>\r\n                        {(summary.planningNotStarted + summary.planningDraft + summary.planningReopened) || 0}\r\n                      </span>\r\n                      <span className=\"status-text\">Ready for Review :</span>\r\n                      <span className={`status-count ${getStatusColorClass('ready-planning')}`}>\r\n                        {summary.planningUnderReview || 0}\r\n                      </span>\r\n                      <span className=\"status-text\">Completed:</span>\r\n                      <span className={`status-count ${getStatusColorClass('completed')}`}>\r\n                        {summary.planningCompleted || 0}\r\n                      </span>\r\n                      <span className=\"status-text\">Exempt: </span>\r\n                      <span className={`status-count ${getStatusColorClass('exempt')}`}>\r\n                        {summary.exemptPartners || 0}\r\n                      </span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {/* Mid Year Cycle Row */}\r\n                  {summary.isMidYearCycleEnabled && (\r\n                    <div className=\"cycle-row\">\r\n                      <span className=\"cycle-label\">MID YEAR:</span>\r\n                      <span className=\"status-text\">Pending Submission :</span>\r\n                      <span className={`status-count ${getStatusColorClass('pending')}`}>\r\n                        {(summary.midYearReviewNotStarted + summary.midYearReviewDraft + summary.midYearReviewReopened) || 0}\r\n                      </span>\r\n                      <span className=\"status-text\">Ready for Review :</span>\r\n                      <span className={`status-count ${getStatusColorClass('ready-midyear')}`}>\r\n                        {summary.midYearReviewUnderReview || 0}\r\n                      </span>\r\n                      <span className=\"status-text\">Completed:</span>\r\n                      <span className={`status-count ${getStatusColorClass('completed')}`}>\r\n                        {summary.midYearReviewCompleted || 0}\r\n                      </span>                     \r\n                    </div>\r\n                  )}\r\n\r\n                  {/* Year End Cycle Row */}\r\n                  {summary.isYearEndCycleEnabled && (\r\n                    <div className=\"cycle-row\">\r\n                      <span className=\"cycle-label\">YEAR END:</span>\r\n                      <span className=\"status-text\">Pending Submission :</span>\r\n                      <span className={`status-count ${getStatusColorClass('pending')}`}>\r\n                        {(summary.yearEndReviewNotStarted + summary.yearEndReviewDraft + summary.yearEndReviewReopened) || 0}\r\n                      </span>\r\n                      <span className=\"status-text\">Ready for Review :</span>\r\n                      <span className={`status-count ${getStatusColorClass('ready-yearend')}`}>\r\n                        {summary.yearEndReviewUnderReview || 0}\r\n                      </span>\r\n                      <span className=\"status-text\">Completed:</span>\r\n                      <span className={`status-count ${getStatusColorClass('completed')}`}>\r\n                        {summary.yearEndReviewCompleted || 0}\r\n                      </span>                      \r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"plan-actions\">\r\n                <Button\r\n                  label=\"View Plans\"\r\n                  className=\"p-button-primary-rounded\"\r\n                  icon=\"pi pi-eye\"\r\n                  rounded\r\n                  onClick={() => handleAllPartnerPlansViewClick(summary.year)}\r\n                />\r\n              </div>\r\n            </div>\r\n          </Card>\r\n        ))\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default AdminPartnerPlans;\r\n"], "mappings": ";;AAAA,SAASA,IAAI,QAAQ,iBAAiB;AACtC,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AACvD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,QAAQ,yCAAyC;AACrE,SAASC,IAAI,QAAQ,6BAA6B;AAClD,OAAOC,wBAAwB,MAAM,yCAAyC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/E,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,WAAA,EAAAC,YAAA;EAC9B,MAAMC,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAMW,WAAW,GAAGd,UAAU,CAACI,WAAW,CAAC;EAC3C,MAAMW,IAAI,GAAGD,WAAW,CAACE,OAAO,CAAC,CAAC;;EAElC;EACA,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACA,MAAMoB,mBAAmB,GAAG,CAAAN,IAAI,aAAJA,IAAI,wBAAAJ,WAAA,GAAJI,IAAI,CAAEO,KAAK,cAAAX,WAAA,uBAAXA,WAAA,CAAaY,QAAQ,CAAClB,IAAI,CAACmB,eAAe,CAAC,MAAIT,IAAI,aAAJA,IAAI,wBAAAH,YAAA,GAAJG,IAAI,CAAEO,KAAK,cAAAV,YAAA,uBAAXA,YAAA,CAAaW,QAAQ,CAAClB,IAAI,CAACoB,mBAAmB,CAAC;;EAE1H;EACAvB,SAAS,CAAC,MAAM;IACd,MAAMwB,yBAAyB,GAAG,MAAAA,CAAA,KAAY;MAC5C,IAAI;QACFN,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMO,QAAQ,GAAG,MAAMrB,wBAAwB,CAACsB,wBAAwB,CAAC,CAAC;QAE1E,IAAID,QAAQ,CAACE,YAAY,KAAK,CAAC,IAAIF,QAAQ,CAACE,YAAY,KAAK,SAAS,EAAE;UACtE;UACAX,gBAAgB,CAACS,QAAQ,CAACG,IAAI,IAAI,EAAE,CAAC;QACvC,CAAC,MAAM;UACLC,OAAO,CAACC,KAAK,CAAC,wCAAwC,EAAEL,QAAQ,CAACM,OAAO,CAAC;UACzEf,gBAAgB,CAAC,EAAE,CAAC;QACtB;MACF,CAAC,CAAC,OAAOc,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;QAC9Dd,gBAAgB,CAAC,EAAE,CAAC;MACtB,CAAC,SAAS;QACRE,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;;IAED;IACA,IAAIN,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEoB,eAAe,CAAC,CAAC,IAAInB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEO,KAAK,KAAKP,IAAI,CAACO,KAAK,CAACC,QAAQ,CAAClB,IAAI,CAACmB,eAAe,CAAC,IAAIT,IAAI,CAACO,KAAK,CAACC,QAAQ,CAAClB,IAAI,CAACoB,mBAAmB,CAAC,CAAC,EAAE;MACjJC,yBAAyB,CAAC,CAAC;IAC7B,CAAC,MAAM;MACLN,UAAU,CAAC,KAAK,CAAC;IACnB;IACA;EACF,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER;EACA,MAAMe,8BAA8B,GAAIC,IAAI,IAAK;IAC/C,IAAIf,mBAAmB,EAAE;MACvB;MACAR,QAAQ,CAAC,oCAAoCuB,IAAI,EAAE,CAAC;IACtD,CAAC,MAAM;MACL;MACAL,OAAO,CAACM,GAAG,CAAC,sEAAsE,CAAC;IACrF;EACF,CAAC;;EAED;EACA,MAAMC,mBAAmB,GAAIC,UAAU,IAAK;IAC1C,QAAQA,UAAU;MAChB,KAAK,SAAS;QAAE,OAAO,gBAAgB;MAAE;MACzC,KAAK,gBAAgB;QAAE,OAAO,uBAAuB;MAAE;MACvD,KAAK,eAAe;QAAE,OAAO,sBAAsB;MAAE;MACrD,KAAK,eAAe;QAAE,OAAO,sBAAsB;MAAE;MACrD,KAAK,WAAW;QAAE,OAAO,kBAAkB;MAAE;MAC7C,KAAK,QAAQ;QAAE,OAAO,eAAe;MAAE;MACvC;QAAS,OAAO,EAAE;IACpB;EACF,CAAC;;EAED;EACA,IAAI,CAAClB,mBAAmB,EAAE;IACxB,OAAO,IAAI;EACb;;EAEA;EACA,IAAI,CAACF,OAAO,KAAK,CAACF,aAAa,IAAIA,aAAa,CAACuB,MAAM,KAAK,CAAC,CAAC,EAAE;IAC9D,OAAO,IAAI,CAAC,CAAC;EACf;EAEA,oBACEhC,OAAA;IAAKiC,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBAClClC,OAAA;MAAIiC,SAAS,EAAC,eAAe;MAAAC,QAAA,EAAC;IAAiB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACpDtC,OAAA;MAAGiC,SAAS,EAAC,qBAAqB;MAAAC,QAAA,EAAC;IAEnC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,EAEH3B,OAAO,gBACNX,OAAA,CAACV,IAAI;MAAC2C,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC9BlC,OAAA;QAAKiC,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BlC,OAAA;UAAGiC,SAAS,EAAC,uBAAuB;UAACM,KAAK,EAAE;YAAEC,QAAQ,EAAE;UAAO;QAAE;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtEtC,OAAA;UAAAkC,QAAA,EAAG;QAA6B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,GAEP7B,aAAa,CAACgC,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBAC/B3C,OAAA,CAACV,IAAI;MAAsD2C,SAAS,EAAC,gBAAgB;MAACM,KAAK,EAAE;QAAEK,YAAY,EAAED,KAAK,GAAGlC,aAAa,CAACuB,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG;MAAI,CAAE;MAAAE,QAAA,eAC7JlC,OAAA;QAAKiC,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChClC,OAAA;UAAKiC,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAE7BlC,OAAA;YAAKiC,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BlC,OAAA;cAAOiC,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1CtC,OAAA;cAAMiC,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAEQ,OAAO,CAACd;YAAI;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,eAGNtC,OAAA;YAAKiC,SAAS,EAAC,8BAA8B;YAAAC,QAAA,GAE1CQ,OAAO,CAACG,sBAAsB,iBAC7B7C,OAAA;cAAKiC,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBlC,OAAA;gBAAMiC,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC9CtC,OAAA;gBAAMiC,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzDtC,OAAA;gBAAMiC,SAAS,EAAE,gBAAgBH,mBAAmB,CAAC,SAAS,CAAC,EAAG;gBAAAI,QAAA,EAC9DQ,OAAO,CAACI,kBAAkB,GAAGJ,OAAO,CAACK,aAAa,GAAGL,OAAO,CAACM,gBAAgB,IAAK;cAAC;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjF,CAAC,eACPtC,OAAA;gBAAMiC,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvDtC,OAAA;gBAAMiC,SAAS,EAAE,gBAAgBH,mBAAmB,CAAC,gBAAgB,CAAC,EAAG;gBAAAI,QAAA,EACtEQ,OAAO,CAACO,mBAAmB,IAAI;cAAC;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC,eACPtC,OAAA;gBAAMiC,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/CtC,OAAA;gBAAMiC,SAAS,EAAE,gBAAgBH,mBAAmB,CAAC,WAAW,CAAC,EAAG;gBAAAI,QAAA,EACjEQ,OAAO,CAACQ,iBAAiB,IAAI;cAAC;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC,eACPtC,OAAA;gBAAMiC,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC7CtC,OAAA;gBAAMiC,SAAS,EAAE,gBAAgBH,mBAAmB,CAAC,QAAQ,CAAC,EAAG;gBAAAI,QAAA,EAC9DQ,OAAO,CAACS,cAAc,IAAI;cAAC;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACN,EAGAI,OAAO,CAACU,qBAAqB,iBAC5BpD,OAAA;cAAKiC,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBlC,OAAA;gBAAMiC,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC9CtC,OAAA;gBAAMiC,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzDtC,OAAA;gBAAMiC,SAAS,EAAE,gBAAgBH,mBAAmB,CAAC,SAAS,CAAC,EAAG;gBAAAI,QAAA,EAC9DQ,OAAO,CAACW,uBAAuB,GAAGX,OAAO,CAACY,kBAAkB,GAAGZ,OAAO,CAACa,qBAAqB,IAAK;cAAC;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChG,CAAC,eACPtC,OAAA;gBAAMiC,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvDtC,OAAA;gBAAMiC,SAAS,EAAE,gBAAgBH,mBAAmB,CAAC,eAAe,CAAC,EAAG;gBAAAI,QAAA,EACrEQ,OAAO,CAACc,wBAAwB,IAAI;cAAC;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC,eACPtC,OAAA;gBAAMiC,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/CtC,OAAA;gBAAMiC,SAAS,EAAE,gBAAgBH,mBAAmB,CAAC,WAAW,CAAC,EAAG;gBAAAI,QAAA,EACjEQ,OAAO,CAACe,sBAAsB,IAAI;cAAC;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACN,EAGAI,OAAO,CAACgB,qBAAqB,iBAC5B1D,OAAA;cAAKiC,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBlC,OAAA;gBAAMiC,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC9CtC,OAAA;gBAAMiC,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzDtC,OAAA;gBAAMiC,SAAS,EAAE,gBAAgBH,mBAAmB,CAAC,SAAS,CAAC,EAAG;gBAAAI,QAAA,EAC9DQ,OAAO,CAACiB,uBAAuB,GAAGjB,OAAO,CAACkB,kBAAkB,GAAGlB,OAAO,CAACmB,qBAAqB,IAAK;cAAC;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChG,CAAC,eACPtC,OAAA;gBAAMiC,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvDtC,OAAA;gBAAMiC,SAAS,EAAE,gBAAgBH,mBAAmB,CAAC,eAAe,CAAC,EAAG;gBAAAI,QAAA,EACrEQ,OAAO,CAACoB,wBAAwB,IAAI;cAAC;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC,eACPtC,OAAA;gBAAMiC,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/CtC,OAAA;gBAAMiC,SAAS,EAAE,gBAAgBH,mBAAmB,CAAC,WAAW,CAAC,EAAG;gBAAAI,QAAA,EACjEQ,OAAO,CAACqB,sBAAsB,IAAI;cAAC;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENtC,OAAA;UAAKiC,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3BlC,OAAA,CAACT,MAAM;YACLyE,KAAK,EAAC,YAAY;YAClB/B,SAAS,EAAC,0BAA0B;YACpCgC,IAAI,EAAC,WAAW;YAChBC,OAAO;YACPC,OAAO,EAAEA,CAAA,KAAMxC,8BAA8B,CAACe,OAAO,CAACd,IAAI;UAAE;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC,GAnFG,GAAGI,OAAO,CAACd,IAAI,IAAIc,OAAO,CAAC0B,iBAAiB,EAAE;MAAAjC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAoFnD,CACP,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACpC,EAAA,CAtLID,iBAAiB;EAAA,QACJN,WAAW;AAAA;AAAA0E,EAAA,GADxBpE,iBAAiB;AAwLvB,eAAeA,iBAAiB;AAAC,IAAAoE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}