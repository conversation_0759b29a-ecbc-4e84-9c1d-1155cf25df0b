{"ast": null, "code": "import { concat } from '../observable/concat';\nimport { popScheduler } from '../util/args';\nimport { operate } from '../util/lift';\nexport function startWith() {\n  var values = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    values[_i] = arguments[_i];\n  }\n  var scheduler = popScheduler(values);\n  return operate(function (source, subscriber) {\n    (scheduler ? concat(values, source, scheduler) : concat(values, source)).subscribe(subscriber);\n  });\n}", "map": {"version": 3, "names": ["concat", "popScheduler", "operate", "startWith", "values", "_i", "arguments", "length", "scheduler", "source", "subscriber", "subscribe"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\startWith.ts"], "sourcesContent": ["import { concat } from '../observable/concat';\nimport { OperatorFunction, SchedulerLike, ValueFromArray } from '../types';\nimport { popScheduler } from '../util/args';\nimport { operate } from '../util/lift';\n\n// Devs are more likely to pass null or undefined than they are a scheduler\n// without accompanying values. To make things easier for (naughty) devs who\n// use the `strictNullChecks: false` TypeScript compiler option, these\n// overloads with explicit null and undefined values are included.\n\nexport function startWith<T>(value: null): OperatorFunction<T, T | null>;\nexport function startWith<T>(value: undefined): OperatorFunction<T, T | undefined>;\n\n/** @deprecated The `scheduler` parameter will be removed in v8. Use `scheduled` and `concatAll`. Details: https://rxjs.dev/deprecations/scheduler-argument */\nexport function startWith<T, A extends readonly unknown[] = T[]>(\n  ...valuesAndScheduler: [...A, SchedulerLike]\n): OperatorFunction<T, T | ValueFromArray<A>>;\nexport function startWith<T, A extends readonly unknown[] = T[]>(...values: A): OperatorFunction<T, T | ValueFromArray<A>>;\n\n/**\n * Returns an observable that, at the moment of subscription, will synchronously emit all\n * values provided to this operator, then subscribe to the source and mirror all of its emissions\n * to subscribers.\n *\n * This is a useful way to know when subscription has occurred on an existing observable.\n *\n * <span class=\"informal\">First emits its arguments in order, and then any\n * emissions from the source.</span>\n *\n * ![](startWith.png)\n *\n * ## Examples\n *\n * Emit a value when a timer starts.\n *\n * ```ts\n * import { timer, map, startWith } from 'rxjs';\n *\n * timer(1000)\n *   .pipe(\n *     map(() => 'timer emit'),\n *     startWith('timer start')\n *   )\n *   .subscribe(x => console.log(x));\n *\n * // results:\n * // 'timer start'\n * // 'timer emit'\n * ```\n *\n * @param values Items you want the modified Observable to emit first.\n * @return A function that returns an Observable that synchronously emits\n * provided values before subscribing to the source Observable.\n *\n * @see {@link endWith}\n * @see {@link finalize}\n * @see {@link concat}\n */\nexport function startWith<T, D>(...values: D[]): OperatorFunction<T, T | D> {\n  const scheduler = popScheduler(values);\n  return operate((source, subscriber) => {\n    // Here we can't pass `undefined` as a scheduler, because if we did, the\n    // code inside of `concat` would be confused by the `undefined`, and treat it\n    // like an invalid observable. So we have to split it two different ways.\n    (scheduler ? concat(values, source, scheduler) : concat(values, source)).subscribe(subscriber);\n  });\n}\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,sBAAsB;AAE7C,SAASC,YAAY,QAAQ,cAAc;AAC3C,SAASC,OAAO,QAAQ,cAAc;AAuDtC,OAAM,SAAUC,SAASA,CAAA;EAAO,IAAAC,MAAA;OAAA,IAAAC,EAAA,IAAc,EAAdA,EAAA,GAAAC,SAAA,CAAAC,MAAc,EAAdF,EAAA,EAAc;IAAdD,MAAA,CAAAC,EAAA,IAAAC,SAAA,CAAAD,EAAA;;EAC9B,IAAMG,SAAS,GAAGP,YAAY,CAACG,MAAM,CAAC;EACtC,OAAOF,OAAO,CAAC,UAACO,MAAM,EAAEC,UAAU;IAIhC,CAACF,SAAS,GAAGR,MAAM,CAACI,MAAM,EAAEK,MAAM,EAAED,SAAS,CAAC,GAAGR,MAAM,CAACI,MAAM,EAAEK,MAAM,CAAC,EAAEE,SAAS,CAACD,UAAU,CAAC;EAChG,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}