{"ast": null, "code": "var _jsxFileName = \"C:\\\\git\\\\Partner-Site\\\\BdoPartner.Plans\\\\Development\\\\ReactClientApp\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React from \"react\";\nimport \"primereact/resources/themes/saga-blue/theme.css\";\nimport \"primereact/resources/primereact.min.css\";\nimport \"primeicons/primeicons.css\";\nimport \"./styling/primeicons.css\";\nimport \"primeflex/primeflex.css\";\nimport \"./styling/App.scss\";\nimport './styling/colors.css';\nimport \"./App.css\";\nimport MessageBox from \"./core/message/components/messageBox\";\nimport MessageToast from \"./core/message/components/messageToast\";\nimport AppProgress from \"./core/loading/components/appProgress\";\nimport { AuthProvider } from \"./core/auth/components/authProvider\";\nimport { BrowserRouter, useLocation } from \"react-router-dom\";\nimport { AppRoutes } from \"./routes/routes\";\nimport { NavBar } from \"./components/base/navbar\";\nimport APP_CONFIG from \"./core/config/appConfig\";\nimport MessageDialog from \"./core/message/components/messageDialog\";\n\n// Component to conditionally render navbar based on current route\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ConditionalNavBar = () => {\n  _s();\n  const location = useLocation();\n\n  // Hide navbar on partner questionnaire page\n  const hideNavbarRoutes = ['/my-partner-plan'];\n  const shouldHideNavbar = hideNavbarRoutes.includes(location.pathname);\n\n  // Add/remove body class for fullscreen layout\n  React.useEffect(() => {\n    if (shouldHideNavbar) {\n      document.body.classList.add('partner-questionnaire-fullscreen');\n    } else {\n      document.body.classList.remove('partner-questionnaire-fullscreen');\n    }\n\n    // Cleanup on unmount\n    return () => {\n      document.body.classList.remove('partner-questionnaire-fullscreen');\n    };\n  }, [shouldHideNavbar]);\n  return shouldHideNavbar ? null : /*#__PURE__*/_jsxDEV(NavBar, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 44,\n    columnNumber: 36\n  }, this);\n};\n_s(ConditionalNavBar, \"BXcZrDMM76mmm4zA8/QV5UbMNXE=\", false, function () {\n  return [useLocation];\n});\n_c = ConditionalNavBar;\nfunction App() {\n  // POC purpose.\n  // Try to access specified reducer's stored state.\n  //\n  //const loading = useSelector((state) => state.loading);\n  //console.log(\"LoadingReducer state\", loading);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"app\",\n    children: [/*#__PURE__*/_jsxDEV(MessageBox, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(MessageToast, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(MessageDialog, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AppProgress, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AuthProvider, {\n      children: /*#__PURE__*/_jsxDEV(BrowserRouter, {\n        basename: `${APP_CONFIG.basePath.length > 0 ? APP_CONFIG.basePath : \"/\"}`,\n        children: [/*#__PURE__*/_jsxDEV(ConditionalNavBar, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"body-area\",\n          children: /*#__PURE__*/_jsxDEV(AppRoutes, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 54,\n    columnNumber: 5\n  }, this);\n}\n_c2 = App;\nexport default App;\nvar _c, _c2;\n$RefreshReg$(_c, \"ConditionalNavBar\");\n$RefreshReg$(_c2, \"App\");", "map": {"version": 3, "names": ["React", "MessageBox", "MessageToast", "AppProgress", "<PERSON>th<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useLocation", "AppRoutes", "NavBar", "APP_CONFIG", "MessageDialog", "jsxDEV", "_jsxDEV", "ConditionalNavBar", "_s", "location", "hideNavbarRoutes", "shouldHideNavbar", "includes", "pathname", "useEffect", "document", "body", "classList", "add", "remove", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "App", "className", "children", "basename", "basePath", "length", "_c2", "$RefreshReg$"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/App.js"], "sourcesContent": ["import React from \"react\";\r\nimport \"primereact/resources/themes/saga-blue/theme.css\";\r\nimport \"primereact/resources/primereact.min.css\";\r\nimport \"primeicons/primeicons.css\";\r\nimport \"./styling/primeicons.css\";\r\nimport \"primeflex/primeflex.css\";\r\nimport \"./styling/App.scss\";\r\nimport './styling/colors.css';\r\nimport \"./App.css\";\r\n\r\nimport MessageBox from \"./core/message/components/messageBox\";\r\nimport MessageToast from \"./core/message/components/messageToast\";\r\nimport AppProgress from \"./core/loading/components/appProgress\";\r\n\r\nimport { AuthProvider } from \"./core/auth/components/authProvider\";\r\nimport { BrowserRouter, useLocation } from \"react-router-dom\";\r\nimport { AppRoutes } from \"./routes/routes\";\r\nimport { NavBar } from \"./components/base/navbar\";\r\nimport APP_CONFIG from \"./core/config/appConfig\";\r\nimport MessageDialog from \"./core/message/components/messageDialog\";\r\n\r\n// Component to conditionally render navbar based on current route\r\nconst ConditionalNavBar = () => {\r\n  const location = useLocation();\r\n\r\n  // Hide navbar on partner questionnaire page\r\n  const hideNavbarRoutes = ['/my-partner-plan'];\r\n  const shouldHideNavbar = hideNavbarRoutes.includes(location.pathname);\r\n\r\n  // Add/remove body class for fullscreen layout\r\n  React.useEffect(() => {\r\n    if (shouldHideNavbar) {\r\n      document.body.classList.add('partner-questionnaire-fullscreen');\r\n    } else {\r\n      document.body.classList.remove('partner-questionnaire-fullscreen');\r\n    }\r\n\r\n    // Cleanup on unmount\r\n    return () => {\r\n      document.body.classList.remove('partner-questionnaire-fullscreen');\r\n    };\r\n  }, [shouldHideNavbar]);\r\n\r\n  return shouldHideNavbar ? null : <NavBar />;\r\n};\r\n\r\nfunction App() {\r\n  // POC purpose.\r\n  // Try to access specified reducer's stored state.\r\n  //\r\n  //const loading = useSelector((state) => state.loading);\r\n  //console.log(\"LoadingReducer state\", loading);\r\n  return (\r\n    <div className=\"app\">\r\n      <MessageBox />\r\n      <MessageToast />\r\n      <MessageDialog />\r\n      <AppProgress />\r\n      <AuthProvider>\r\n        {/*  When authentication process updated, nexsted route\r\n                and route's refered component will got re-render automatically.\r\n                Note: Any nested components inside \"AuthProvider\",\r\n                it can call useContext(AuthContext) to get reference of authService.\r\n            */}\r\n        {/* As for \"basename\", it is the base URL for all locations.\r\n              If your app is served from a sub-directory on your server,\r\n              you’ll want to set this to the sub-directory.\r\n              A properly formatted basename should have a leading slash,\r\n              but no trailing slash.\r\n            */}\r\n        <BrowserRouter\r\n          basename={`${\r\n            APP_CONFIG.basePath.length > 0 ? APP_CONFIG.basePath : \"/\"\r\n          }`}\r\n        >\r\n          <ConditionalNavBar />\r\n          <div className=\"body-area\">\r\n            <AppRoutes />\r\n          </div>\r\n        </BrowserRouter>\r\n      </AuthProvider>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default App;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,iDAAiD;AACxD,OAAO,yCAAyC;AAChD,OAAO,2BAA2B;AAClC,OAAO,0BAA0B;AACjC,OAAO,yBAAyB;AAChC,OAAO,oBAAoB;AAC3B,OAAO,sBAAsB;AAC7B,OAAO,WAAW;AAElB,OAAOC,UAAU,MAAM,sCAAsC;AAC7D,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,WAAW,MAAM,uCAAuC;AAE/D,SAASC,YAAY,QAAQ,qCAAqC;AAClE,SAASC,aAAa,EAAEC,WAAW,QAAQ,kBAAkB;AAC7D,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,SAASC,MAAM,QAAQ,0BAA0B;AACjD,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,aAAa,MAAM,yCAAyC;;AAEnE;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAMC,QAAQ,GAAGT,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMU,gBAAgB,GAAG,CAAC,kBAAkB,CAAC;EAC7C,MAAMC,gBAAgB,GAAGD,gBAAgB,CAACE,QAAQ,CAACH,QAAQ,CAACI,QAAQ,CAAC;;EAErE;EACAnB,KAAK,CAACoB,SAAS,CAAC,MAAM;IACpB,IAAIH,gBAAgB,EAAE;MACpBI,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,kCAAkC,CAAC;IACjE,CAAC,MAAM;MACLH,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACE,MAAM,CAAC,kCAAkC,CAAC;IACpE;;IAEA;IACA,OAAO,MAAM;MACXJ,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACE,MAAM,CAAC,kCAAkC,CAAC;IACpE,CAAC;EACH,CAAC,EAAE,CAACR,gBAAgB,CAAC,CAAC;EAEtB,OAAOA,gBAAgB,GAAG,IAAI,gBAAGL,OAAA,CAACJ,MAAM;IAAAkB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAC7C,CAAC;AAACf,EAAA,CAtBID,iBAAiB;EAAA,QACJP,WAAW;AAAA;AAAAwB,EAAA,GADxBjB,iBAAiB;AAwBvB,SAASkB,GAAGA,CAAA,EAAG;EACb;EACA;EACA;EACA;EACA;EACA,oBACEnB,OAAA;IAAKoB,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAClBrB,OAAA,CAACX,UAAU;MAAAyB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACdjB,OAAA,CAACV,YAAY;MAAAwB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAChBjB,OAAA,CAACF,aAAa;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACjBjB,OAAA,CAACT,WAAW;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACfjB,OAAA,CAACR,YAAY;MAAA6B,QAAA,eAYXrB,OAAA,CAACP,aAAa;QACZ6B,QAAQ,EAAE,GACRzB,UAAU,CAAC0B,QAAQ,CAACC,MAAM,GAAG,CAAC,GAAG3B,UAAU,CAAC0B,QAAQ,GAAG,GAAG,EACzD;QAAAF,QAAA,gBAEHrB,OAAA,CAACC,iBAAiB;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrBjB,OAAA;UAAKoB,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxBrB,OAAA,CAACL,SAAS;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEV;AAACQ,GAAA,GArCQN,GAAG;AAuCZ,eAAeA,GAAG;AAAC,IAAAD,EAAA,EAAAO,GAAA;AAAAC,YAAA,CAAAR,EAAA;AAAAQ,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}