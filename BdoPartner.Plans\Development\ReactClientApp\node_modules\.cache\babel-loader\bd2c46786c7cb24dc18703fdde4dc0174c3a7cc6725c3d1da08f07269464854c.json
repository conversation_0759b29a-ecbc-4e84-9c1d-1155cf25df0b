{"ast": null, "code": "import http from \"../core/http/httpClient\";\nimport APP_CONFIG from \"../core/config/appConfig\";\nimport { ResultStatus } from \"../core/enumertions/resultStatus\";\n\n/**\r\n * Partner Service for handling partner-related API calls\r\n * Provides methods to search and retrieve partner data from the backend API\r\n */\nclass PartnerService {\n  /**\r\n   * Search partners with filtering and pagination\r\n   * @param {string} searchTerm - Search term for partner name or email\r\n   * @param {string} partnerType - Filter by partner type\r\n   * @param {string} department - Filter by department\r\n   * @param {string} location - Filter by location\r\n   * @param {boolean} isActive - Filter by active status\r\n   * @param {number} pageNumber - Page number (default: 1)\r\n   * @param {number} pageSize - Page size (default: 50)\r\n   * @returns {Promise<Array>} Array of partners matching the search criteria\r\n   */\n  async searchPartners(searchTerm = null, partnerType = null, department = null, location = null, isActive = null, pageNumber = 1, pageSize = 50) {\n    try {\n      const params = new URLSearchParams();\n      if (searchTerm) params.append('searchTerm', searchTerm);\n      if (partnerType) params.append('partnerType', partnerType);\n      if (department) params.append('department', department);\n      if (location) params.append('location', location);\n      if (isActive !== null && isActive !== undefined) params.append('isActive', isActive);\n      params.append('pageNumber', pageNumber);\n      params.append('pageSize', pageSize);\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/partner/searchpartners?${params.toString()}`);\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\n        return response.data.item || [];\n      } else {\n        var _response$data;\n        console.error(\"Failed to search partners:\", (_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.message);\n        return [];\n      }\n    } catch (error) {\n      console.error(\"Error searching partners:\", error);\n      return [];\n    }\n  }\n\n  /**\r\n   * Search partners for autocomplete (simplified search with smaller page size)\r\n   * @param {string} searchTerm - Search term for partner name\r\n   * @param {number} pageSize - Page size (default: 20 for autocomplete)\r\n   * @returns {Promise<Array>} Array of partners for autocomplete suggestions\r\n   */\n  async searchPartnersForAutocomplete(searchTerm, pageSize = 20) {\n    try {\n      if (!searchTerm || searchTerm.trim().length < 2) {\n        return [];\n      }\n      const partners = await this.searchPartners(searchTerm.trim(), null,\n      // partnerType\n      null,\n      // department\n      null,\n      // location\n      true,\n      // isActive - only show active partners\n      1,\n      // pageNumber\n      pageSize);\n\n      // Transform to autocomplete format with display name and ID\n      return partners.map(partner => ({\n        id: partner.id,\n        employeeId: partner.employeeId,\n        displayName: partner.displayName || `${partner.firstName} ${partner.lastName}`,\n        firstName: partner.firstName,\n        lastName: partner.lastName,\n        mail: partner.mail,\n        department: partner.department,\n        location: partner.location\n      }));\n    } catch (error) {\n      console.error(\"Error searching partners for autocomplete:\", error);\n      return [];\n    }\n  }\n\n  /**\r\n   * Get partner by ID\r\n   * @param {string} id - Partner ID\r\n   * @returns {Promise<Object|null>} Partner details or null if not found\r\n   */\n  async getPartnerById(id) {\n    try {\n      if (!id) return null;\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/partner/getpartnerbyid?id=${id}`);\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\n        return response.data.item || null;\n      } else {\n        var _response$data2;\n        console.error(\"Failed to get partner by ID:\", (_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : _response$data2.message);\n        return null;\n      }\n    } catch (error) {\n      console.error(\"Error getting partner by ID:\", error);\n      return null;\n    }\n  }\n\n  /**\r\n   * Get partner by employee ID\r\n   * @param {number} employeeId - Employee ID\r\n   * @returns {Promise<Object|null>} Partner details or null if not found\r\n   */\n  async getPartnerByEmployeeId(employeeId) {\n    try {\n      if (!employeeId) return null;\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/partner/getpartnerbyemployeeid?employeeId=${employeeId}`);\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\n        return response.data.item || null;\n      } else {\n        var _response$data3;\n        console.error(\"Failed to get partner by employee ID:\", (_response$data3 = response.data) === null || _response$data3 === void 0 ? void 0 : _response$data3.message);\n        return null;\n      }\n    } catch (error) {\n      console.error(\"Error getting partner by employee ID:\", error);\n      return null;\n    }\n  }\n\n  /**\r\n   * Get partners for lookup/dropdown purposes\r\n   * @param {boolean} includeInactive - Include inactive partners\r\n   * @returns {Promise<Array>} Array of lookup items with key-value pairs\r\n   */\n  async getPartnersLookup(includeInactive = false) {\n    try {\n      const response = await http.get(`${APP_CONFIG.apiDomain}/api/partner/getpartnerslookup?includeInactive=${includeInactive}`);\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\n        return response.data.item || [];\n      } else {\n        var _response$data4;\n        console.error(\"Failed to get partners lookup:\", (_response$data4 = response.data) === null || _response$data4 === void 0 ? void 0 : _response$data4.message);\n        return [];\n      }\n    } catch (error) {\n      console.error(\"Error getting partners lookup:\", error);\n      return [];\n    }\n  }\n}\n\n// Export singleton instance\nconst partnerService = new PartnerService();\nexport default partnerService;", "map": {"version": 3, "names": ["http", "APP_CONFIG", "ResultStatus", "PartnerService", "searchPartners", "searchTerm", "partnerType", "department", "location", "isActive", "pageNumber", "pageSize", "params", "URLSearchParams", "append", "undefined", "response", "get", "apiDomain", "toString", "data", "resultStatus", "Success", "item", "_response$data", "console", "error", "message", "searchPartnersForAutocomplete", "trim", "length", "partners", "map", "partner", "id", "employeeId", "displayName", "firstName", "lastName", "mail", "getPartnerById", "_response$data2", "getPartnerByEmployeeId", "_response$data3", "getPartnersLookup", "includeInactive", "_response$data4", "partnerService"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/services/partnerService.js"], "sourcesContent": ["import http from \"../core/http/httpClient\";\r\nimport APP_CONFIG from \"../core/config/appConfig\";\r\nimport { ResultStatus } from \"../core/enumertions/resultStatus\";\r\n\r\n/**\r\n * Partner Service for handling partner-related API calls\r\n * Provides methods to search and retrieve partner data from the backend API\r\n */\r\nclass PartnerService {\r\n  /**\r\n   * Search partners with filtering and pagination\r\n   * @param {string} searchTerm - Search term for partner name or email\r\n   * @param {string} partnerType - Filter by partner type\r\n   * @param {string} department - Filter by department\r\n   * @param {string} location - Filter by location\r\n   * @param {boolean} isActive - Filter by active status\r\n   * @param {number} pageNumber - Page number (default: 1)\r\n   * @param {number} pageSize - Page size (default: 50)\r\n   * @returns {Promise<Array>} Array of partners matching the search criteria\r\n   */\r\n  async searchPartners(searchTerm = null, partnerType = null, department = null, \r\n                      location = null, isActive = null, pageNumber = 1, pageSize = 50) {\r\n    try {\r\n      const params = new URLSearchParams();\r\n      \r\n      if (searchTerm) params.append('searchTerm', searchTerm);\r\n      if (partnerType) params.append('partnerType', partnerType);\r\n      if (department) params.append('department', department);\r\n      if (location) params.append('location', location);\r\n      if (isActive !== null && isActive !== undefined) params.append('isActive', isActive);\r\n      params.append('pageNumber', pageNumber);\r\n      params.append('pageSize', pageSize);\r\n\r\n      const response = await http.get(\r\n        `${APP_CONFIG.apiDomain}/api/partner/searchpartners?${params.toString()}`\r\n      );\r\n      \r\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\r\n        return response.data.item || [];\r\n      } else {\r\n        console.error(\"Failed to search partners:\", response.data?.message);\r\n        return [];\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error searching partners:\", error);\r\n      return [];\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Search partners for autocomplete (simplified search with smaller page size)\r\n   * @param {string} searchTerm - Search term for partner name\r\n   * @param {number} pageSize - Page size (default: 20 for autocomplete)\r\n   * @returns {Promise<Array>} Array of partners for autocomplete suggestions\r\n   */\r\n  async searchPartnersForAutocomplete(searchTerm, pageSize = 20) {\r\n    try {\r\n      if (!searchTerm || searchTerm.trim().length < 2) {\r\n        return [];\r\n      }\r\n\r\n      const partners = await this.searchPartners(\r\n        searchTerm.trim(), \r\n        null, // partnerType\r\n        null, // department\r\n        null, // location\r\n        true, // isActive - only show active partners\r\n        1,    // pageNumber\r\n        pageSize\r\n      );\r\n\r\n      // Transform to autocomplete format with display name and ID\r\n      return partners.map(partner => ({\r\n        id: partner.id,\r\n        employeeId: partner.employeeId,\r\n        displayName: partner.displayName || `${partner.firstName} ${partner.lastName}`,\r\n        firstName: partner.firstName,\r\n        lastName: partner.lastName,\r\n        mail: partner.mail,\r\n        department: partner.department,\r\n        location: partner.location\r\n      }));\r\n    } catch (error) {\r\n      console.error(\"Error searching partners for autocomplete:\", error);\r\n      return [];\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get partner by ID\r\n   * @param {string} id - Partner ID\r\n   * @returns {Promise<Object|null>} Partner details or null if not found\r\n   */\r\n  async getPartnerById(id) {\r\n    try {\r\n      if (!id) return null;\r\n\r\n      const response = await http.get(\r\n        `${APP_CONFIG.apiDomain}/api/partner/getpartnerbyid?id=${id}`\r\n      );\r\n      \r\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\r\n        return response.data.item || null;\r\n      } else {\r\n        console.error(\"Failed to get partner by ID:\", response.data?.message);\r\n        return null;\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error getting partner by ID:\", error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get partner by employee ID\r\n   * @param {number} employeeId - Employee ID\r\n   * @returns {Promise<Object|null>} Partner details or null if not found\r\n   */\r\n  async getPartnerByEmployeeId(employeeId) {\r\n    try {\r\n      if (!employeeId) return null;\r\n\r\n      const response = await http.get(\r\n        `${APP_CONFIG.apiDomain}/api/partner/getpartnerbyemployeeid?employeeId=${employeeId}`\r\n      );\r\n      \r\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\r\n        return response.data.item || null;\r\n      } else {\r\n        console.error(\"Failed to get partner by employee ID:\", response.data?.message);\r\n        return null;\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error getting partner by employee ID:\", error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get partners for lookup/dropdown purposes\r\n   * @param {boolean} includeInactive - Include inactive partners\r\n   * @returns {Promise<Array>} Array of lookup items with key-value pairs\r\n   */\r\n  async getPartnersLookup(includeInactive = false) {\r\n    try {\r\n      const response = await http.get(\r\n        `${APP_CONFIG.apiDomain}/api/partner/getpartnerslookup?includeInactive=${includeInactive}`\r\n      );\r\n      \r\n      if (response.data && response.data.resultStatus === ResultStatus.Success) {\r\n        return response.data.item || [];\r\n      } else {\r\n        console.error(\"Failed to get partners lookup:\", response.data?.message);\r\n        return [];\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error getting partners lookup:\", error);\r\n      return [];\r\n    }\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nconst partnerService = new PartnerService();\r\nexport default partnerService;\r\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,yBAAyB;AAC1C,OAAOC,UAAU,MAAM,0BAA0B;AACjD,SAASC,YAAY,QAAQ,kCAAkC;;AAE/D;AACA;AACA;AACA;AACA,MAAMC,cAAc,CAAC;EACnB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,MAAMC,cAAcA,CAACC,UAAU,GAAG,IAAI,EAAEC,WAAW,GAAG,IAAI,EAAEC,UAAU,GAAG,IAAI,EACzDC,QAAQ,GAAG,IAAI,EAAEC,QAAQ,GAAG,IAAI,EAAEC,UAAU,GAAG,CAAC,EAAEC,QAAQ,GAAG,EAAE,EAAE;IACnF,IAAI;MACF,MAAMC,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;MAEpC,IAAIR,UAAU,EAAEO,MAAM,CAACE,MAAM,CAAC,YAAY,EAAET,UAAU,CAAC;MACvD,IAAIC,WAAW,EAAEM,MAAM,CAACE,MAAM,CAAC,aAAa,EAAER,WAAW,CAAC;MAC1D,IAAIC,UAAU,EAAEK,MAAM,CAACE,MAAM,CAAC,YAAY,EAAEP,UAAU,CAAC;MACvD,IAAIC,QAAQ,EAAEI,MAAM,CAACE,MAAM,CAAC,UAAU,EAAEN,QAAQ,CAAC;MACjD,IAAIC,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAKM,SAAS,EAAEH,MAAM,CAACE,MAAM,CAAC,UAAU,EAAEL,QAAQ,CAAC;MACpFG,MAAM,CAACE,MAAM,CAAC,YAAY,EAAEJ,UAAU,CAAC;MACvCE,MAAM,CAACE,MAAM,CAAC,UAAU,EAAEH,QAAQ,CAAC;MAEnC,MAAMK,QAAQ,GAAG,MAAMhB,IAAI,CAACiB,GAAG,CAC7B,GAAGhB,UAAU,CAACiB,SAAS,+BAA+BN,MAAM,CAACO,QAAQ,CAAC,CAAC,EACzE,CAAC;MAED,IAAIH,QAAQ,CAACI,IAAI,IAAIJ,QAAQ,CAACI,IAAI,CAACC,YAAY,KAAKnB,YAAY,CAACoB,OAAO,EAAE;QACxE,OAAON,QAAQ,CAACI,IAAI,CAACG,IAAI,IAAI,EAAE;MACjC,CAAC,MAAM;QAAA,IAAAC,cAAA;QACLC,OAAO,CAACC,KAAK,CAAC,4BAA4B,GAAAF,cAAA,GAAER,QAAQ,CAACI,IAAI,cAAAI,cAAA,uBAAbA,cAAA,CAAeG,OAAO,CAAC;QACnE,OAAO,EAAE;MACX;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,OAAO,EAAE;IACX;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,MAAME,6BAA6BA,CAACvB,UAAU,EAAEM,QAAQ,GAAG,EAAE,EAAE;IAC7D,IAAI;MACF,IAAI,CAACN,UAAU,IAAIA,UAAU,CAACwB,IAAI,CAAC,CAAC,CAACC,MAAM,GAAG,CAAC,EAAE;QAC/C,OAAO,EAAE;MACX;MAEA,MAAMC,QAAQ,GAAG,MAAM,IAAI,CAAC3B,cAAc,CACxCC,UAAU,CAACwB,IAAI,CAAC,CAAC,EACjB,IAAI;MAAE;MACN,IAAI;MAAE;MACN,IAAI;MAAE;MACN,IAAI;MAAE;MACN,CAAC;MAAK;MACNlB,QACF,CAAC;;MAED;MACA,OAAOoB,QAAQ,CAACC,GAAG,CAACC,OAAO,KAAK;QAC9BC,EAAE,EAAED,OAAO,CAACC,EAAE;QACdC,UAAU,EAAEF,OAAO,CAACE,UAAU;QAC9BC,WAAW,EAAEH,OAAO,CAACG,WAAW,IAAI,GAAGH,OAAO,CAACI,SAAS,IAAIJ,OAAO,CAACK,QAAQ,EAAE;QAC9ED,SAAS,EAAEJ,OAAO,CAACI,SAAS;QAC5BC,QAAQ,EAAEL,OAAO,CAACK,QAAQ;QAC1BC,IAAI,EAAEN,OAAO,CAACM,IAAI;QAClBhC,UAAU,EAAE0B,OAAO,CAAC1B,UAAU;QAC9BC,QAAQ,EAAEyB,OAAO,CAACzB;MACpB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;MAClE,OAAO,EAAE;IACX;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAMc,cAAcA,CAACN,EAAE,EAAE;IACvB,IAAI;MACF,IAAI,CAACA,EAAE,EAAE,OAAO,IAAI;MAEpB,MAAMlB,QAAQ,GAAG,MAAMhB,IAAI,CAACiB,GAAG,CAC7B,GAAGhB,UAAU,CAACiB,SAAS,kCAAkCgB,EAAE,EAC7D,CAAC;MAED,IAAIlB,QAAQ,CAACI,IAAI,IAAIJ,QAAQ,CAACI,IAAI,CAACC,YAAY,KAAKnB,YAAY,CAACoB,OAAO,EAAE;QACxE,OAAON,QAAQ,CAACI,IAAI,CAACG,IAAI,IAAI,IAAI;MACnC,CAAC,MAAM;QAAA,IAAAkB,eAAA;QACLhB,OAAO,CAACC,KAAK,CAAC,8BAA8B,GAAAe,eAAA,GAAEzB,QAAQ,CAACI,IAAI,cAAAqB,eAAA,uBAAbA,eAAA,CAAed,OAAO,CAAC;QACrE,OAAO,IAAI;MACb;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,OAAO,IAAI;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAMgB,sBAAsBA,CAACP,UAAU,EAAE;IACvC,IAAI;MACF,IAAI,CAACA,UAAU,EAAE,OAAO,IAAI;MAE5B,MAAMnB,QAAQ,GAAG,MAAMhB,IAAI,CAACiB,GAAG,CAC7B,GAAGhB,UAAU,CAACiB,SAAS,kDAAkDiB,UAAU,EACrF,CAAC;MAED,IAAInB,QAAQ,CAACI,IAAI,IAAIJ,QAAQ,CAACI,IAAI,CAACC,YAAY,KAAKnB,YAAY,CAACoB,OAAO,EAAE;QACxE,OAAON,QAAQ,CAACI,IAAI,CAACG,IAAI,IAAI,IAAI;MACnC,CAAC,MAAM;QAAA,IAAAoB,eAAA;QACLlB,OAAO,CAACC,KAAK,CAAC,uCAAuC,GAAAiB,eAAA,GAAE3B,QAAQ,CAACI,IAAI,cAAAuB,eAAA,uBAAbA,eAAA,CAAehB,OAAO,CAAC;QAC9E,OAAO,IAAI;MACb;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7D,OAAO,IAAI;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAMkB,iBAAiBA,CAACC,eAAe,GAAG,KAAK,EAAE;IAC/C,IAAI;MACF,MAAM7B,QAAQ,GAAG,MAAMhB,IAAI,CAACiB,GAAG,CAC7B,GAAGhB,UAAU,CAACiB,SAAS,kDAAkD2B,eAAe,EAC1F,CAAC;MAED,IAAI7B,QAAQ,CAACI,IAAI,IAAIJ,QAAQ,CAACI,IAAI,CAACC,YAAY,KAAKnB,YAAY,CAACoB,OAAO,EAAE;QACxE,OAAON,QAAQ,CAACI,IAAI,CAACG,IAAI,IAAI,EAAE;MACjC,CAAC,MAAM;QAAA,IAAAuB,eAAA;QACLrB,OAAO,CAACC,KAAK,CAAC,gCAAgC,GAAAoB,eAAA,GAAE9B,QAAQ,CAACI,IAAI,cAAA0B,eAAA,uBAAbA,eAAA,CAAenB,OAAO,CAAC;QACvE,OAAO,EAAE;MACX;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,OAAO,EAAE;IACX;EACF;AACF;;AAEA;AACA,MAAMqB,cAAc,GAAG,IAAI5C,cAAc,CAAC,CAAC;AAC3C,eAAe4C,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}