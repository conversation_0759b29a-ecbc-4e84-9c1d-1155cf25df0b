{"ast": null, "code": "var _jsxFileName = \"C:\\\\git\\\\Partner-Site\\\\BdoPartner.Plans\\\\Development\\\\ReactClientApp\\\\src\\\\pages\\\\dashboard.jsx\",\n  _s = $RefreshSig$();\nimport { useContext, useState, useEffect } from \"react\";\nimport MyCurrentPlan from \"../components/dashboard/MyCurrentPlan\";\nimport MyPastPlans from \"../components/dashboard/MyPastPlan\";\nimport AdminPartnerPlans from \"../components/dashboard/AdminPartnerPlans\";\nimport ReviewerPartnerPlans from \"../components/dashboard/ReviewerPartnerPlans\";\nimport { AuthContext } from \"../core/auth/components/authProvider\";\nimport formService from \"../services/formService\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  const authService = useContext(AuthContext);\n  const user = authService.getUser();\n\n  // State for landing page access\n  const [landingPageAccess, setLandingPageAccess] = useState({\n    showMyPlanSection: false,\n    showMyPastPlansSection: false,\n    showReviewerSection: false,\n    showAdminSection: false,\n    myPastPlanYears: []\n  });\n\n  // Determine welcome message based on role\n  const getWelcomeMessage = () => {\n    if (landingPageAccess.showMyPlanSection) {\n      return \"This landing page is your centralized hub for managing your annual partner plans.\";\n    }\n    return \"This landing page is your centralized hub for managing and monitoring partner-submitted plans. Whether you're reviewing submissions, tracking progress, or analyzing trends, this page provides the tools and insights you need to drive effective planning.\";\n  };\n  const [loading, setLoading] = useState(true);\n\n  // Load landing page access data\n  useEffect(() => {\n    const loadLandingPageAccess = async () => {\n      try {\n        setLoading(true);\n        const response = await formService.getLandingPageAccess();\n        setLandingPageAccess(response);\n      } catch (error) {\n        console.error(\"Error loading landing page access:\", error);\n        // Set default values on error\n        setLandingPageAccess({\n          showMyPlanSection: false,\n          showMyPastPlansSection: false,\n          showReviewerSection: false,\n          showAdminSection: false,\n          myPastPlanYears: []\n        });\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    // Only load data if user is authenticated\n    if (authService !== null && authService !== void 0 && authService.isAuthenticated()) {\n      loadLandingPageAccess();\n    } else {\n      setLoading(false);\n    }\n  }, [authService]);\n\n  // Show loading state\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"welcome-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"welcome-title\",\n          children: [\"Welcome \", (user === null || user === void 0 ? void 0 : user.displayName) || \"User\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"welcome-description\",\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"dashboard-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"welcome-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"welcome-title\",\n        children: [\"Welcome \", (user === null || user === void 0 ? void 0 : user.displayName) || \"User\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"welcome-description\",\n        children: getWelcomeMessage()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-content\",\n      children: [landingPageAccess.showMyPlanSection && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-section\",\n        children: /*#__PURE__*/_jsxDEV(MyCurrentPlan, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 11\n      }, this), landingPageAccess.showMyPastPlansSection && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-section\",\n        children: /*#__PURE__*/_jsxDEV(MyPastPlans, {\n          myPastPlanYears: landingPageAccess.myPastPlanYears\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 11\n      }, this), landingPageAccess.showReviewerSection && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-section\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"partner-plans\",\n          children: /*#__PURE__*/_jsxDEV(ReviewerPartnerPlans, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 11\n      }, this), landingPageAccess.showAdminSection && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-section\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"partner-plans\",\n          children: /*#__PURE__*/_jsxDEV(AdminPartnerPlans, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 75,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"4r+w9xMEn8S/yEaqneJeBVZrDE0=\");\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["useContext", "useState", "useEffect", "MyCurrentPlan", "MyPastPlans", "AdminPartnerPlans", "ReviewerPartnerPlans", "AuthContext", "formService", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "authService", "user", "getUser", "landingPageAccess", "setLandingPageAccess", "showMyPlanSection", "showMyPastPlansSection", "showReviewerSection", "showAdminSection", "myPastPlanYears", "getWelcomeMessage", "loading", "setLoading", "loadLandingPageAccess", "response", "getLandingPageAccess", "error", "console", "isAuthenticated", "className", "children", "displayName", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/pages/dashboard.jsx"], "sourcesContent": ["import { useContext, useState, useEffect } from \"react\";\r\nimport MyCurrentPlan from \"../components/dashboard/MyCurrentPlan\";\r\nimport MyPastPlans from \"../components/dashboard/MyPastPlan\";\r\nimport AdminPartnerPlans from \"../components/dashboard/AdminPartnerPlans\";\r\nimport ReviewerPartnerPlans from \"../components/dashboard/ReviewerPartnerPlans\";\r\nimport { AuthContext } from \"../core/auth/components/authProvider\";\r\nimport formService from \"../services/formService\";\r\n\r\nconst Dashboard = () => {\r\n  const authService = useContext(AuthContext);\r\n  const user = authService.getUser();\r\n\r\n  // State for landing page access\r\n  const [landingPageAccess, setLandingPageAccess] = useState({\r\n    showMyPlanSection: false,\r\n    showMyPastPlansSection: false,\r\n    showReviewerSection: false,\r\n    showAdminSection: false,\r\n    myPastPlanYears: [],\r\n  });\r\n\r\n  // Determine welcome message based on role\r\n  const getWelcomeMessage = () => {\r\n    if (landingPageAccess.showMyPlanSection) {\r\n      return \"This landing page is your centralized hub for managing your annual partner plans.\";\r\n    }\r\n    return \"This landing page is your centralized hub for managing and monitoring partner-submitted plans. Whether you're reviewing submissions, tracking progress, or analyzing trends, this page provides the tools and insights you need to drive effective planning.\";\r\n  };\r\n\r\n  const [loading, setLoading] = useState(true);\r\n\r\n  // Load landing page access data\r\n  useEffect(() => {\r\n    const loadLandingPageAccess = async () => {\r\n      try {\r\n        setLoading(true);\r\n        const response = await formService.getLandingPageAccess();\r\n        setLandingPageAccess(response);\r\n      } catch (error) {\r\n        console.error(\"Error loading landing page access:\", error);\r\n        // Set default values on error\r\n        setLandingPageAccess({\r\n          showMyPlanSection: false,\r\n          showMyPastPlansSection: false,\r\n          showReviewerSection: false,\r\n          showAdminSection: false,\r\n          myPastPlanYears: [],\r\n        });\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    // Only load data if user is authenticated\r\n    if (authService?.isAuthenticated()) {\r\n      loadLandingPageAccess();\r\n    } else {\r\n      setLoading(false);\r\n    }\r\n  }, [authService]);\r\n\r\n  // Show loading state\r\n  if (loading) {\r\n    return (\r\n      <div className=\"dashboard-container\">\r\n        <div className=\"welcome-section\">\r\n          <h1 className=\"welcome-title\">Welcome {user?.displayName || \"User\"}</h1>\r\n          <p className=\"welcome-description\">Loading...</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"dashboard-container\">\r\n      {/* Welcome Section */}\r\n      <div className=\"welcome-section\">\r\n        <h1 className=\"welcome-title\">Welcome {user?.displayName || \"User\"}</h1>\r\n        <p className=\"welcome-description\">{getWelcomeMessage()}</p>\r\n      </div>\r\n\r\n      {/* Dashboard Content */}\r\n      <div className=\"dashboard-content\">\r\n        {/* My Current Plan Section - Show based on LandingPageAccess */}\r\n        {landingPageAccess.showMyPlanSection && (\r\n          <div className=\"dashboard-section\">\r\n            <MyCurrentPlan />\r\n          </div>\r\n        )}\r\n\r\n        {/* My Past Plans Section - Show based on LandingPageAccess */}\r\n        {landingPageAccess.showMyPastPlansSection && (\r\n          <div className=\"dashboard-section\">\r\n            <MyPastPlans myPastPlanYears={landingPageAccess.myPastPlanYears} />\r\n          </div>\r\n        )}\r\n\r\n        {/* Reviewer Partner Plans Section - Show based on LandingPageAccess */}\r\n        {landingPageAccess.showReviewerSection && (\r\n          <div className=\"dashboard-section\">\r\n            <div className=\"partner-plans\">\r\n              <ReviewerPartnerPlans />\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Admin Partner Plans Section - Show based on LandingPageAccess */}\r\n        {landingPageAccess.showAdminSection && (\r\n          <div className=\"dashboard-section\">\r\n            <div className=\"partner-plans\">\r\n              <AdminPartnerPlans />\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Dashboard;\r\n"], "mappings": ";;AAAA,SAASA,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AACvD,OAAOC,aAAa,MAAM,uCAAuC;AACjE,OAAOC,WAAW,MAAM,oCAAoC;AAC5D,OAAOC,iBAAiB,MAAM,2CAA2C;AACzE,OAAOC,oBAAoB,MAAM,8CAA8C;AAC/E,SAASC,WAAW,QAAQ,sCAAsC;AAClE,OAAOC,WAAW,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAMC,WAAW,GAAGb,UAAU,CAACO,WAAW,CAAC;EAC3C,MAAMO,IAAI,GAAGD,WAAW,CAACE,OAAO,CAAC,CAAC;;EAElC;EACA,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGhB,QAAQ,CAAC;IACzDiB,iBAAiB,EAAE,KAAK;IACxBC,sBAAsB,EAAE,KAAK;IAC7BC,mBAAmB,EAAE,KAAK;IAC1BC,gBAAgB,EAAE,KAAK;IACvBC,eAAe,EAAE;EACnB,CAAC,CAAC;;EAEF;EACA,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAIP,iBAAiB,CAACE,iBAAiB,EAAE;MACvC,OAAO,mFAAmF;IAC5F;IACA,OAAO,8PAA8P;EACvQ,CAAC;EAED,MAAM,CAACM,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACAC,SAAS,CAAC,MAAM;IACd,MAAMwB,qBAAqB,GAAG,MAAAA,CAAA,KAAY;MACxC,IAAI;QACFD,UAAU,CAAC,IAAI,CAAC;QAChB,MAAME,QAAQ,GAAG,MAAMnB,WAAW,CAACoB,oBAAoB,CAAC,CAAC;QACzDX,oBAAoB,CAACU,QAAQ,CAAC;MAChC,CAAC,CAAC,OAAOE,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;QAC1D;QACAZ,oBAAoB,CAAC;UACnBC,iBAAiB,EAAE,KAAK;UACxBC,sBAAsB,EAAE,KAAK;UAC7BC,mBAAmB,EAAE,KAAK;UAC1BC,gBAAgB,EAAE,KAAK;UACvBC,eAAe,EAAE;QACnB,CAAC,CAAC;MACJ,CAAC,SAAS;QACRG,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;;IAED;IACA,IAAIZ,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEkB,eAAe,CAAC,CAAC,EAAE;MAClCL,qBAAqB,CAAC,CAAC;IACzB,CAAC,MAAM;MACLD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACZ,WAAW,CAAC,CAAC;;EAEjB;EACA,IAAIW,OAAO,EAAE;IACX,oBACEd,OAAA;MAAKsB,SAAS,EAAC,qBAAqB;MAAAC,QAAA,eAClCvB,OAAA;QAAKsB,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BvB,OAAA;UAAIsB,SAAS,EAAC,eAAe;UAAAC,QAAA,GAAC,UAAQ,EAAC,CAAAnB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoB,WAAW,KAAI,MAAM;QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACxE5B,OAAA;UAAGsB,SAAS,EAAC,qBAAqB;UAAAC,QAAA,EAAC;QAAU;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE5B,OAAA;IAAKsB,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBAElCvB,OAAA;MAAKsB,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BvB,OAAA;QAAIsB,SAAS,EAAC,eAAe;QAAAC,QAAA,GAAC,UAAQ,EAAC,CAAAnB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoB,WAAW,KAAI,MAAM;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACxE5B,OAAA;QAAGsB,SAAS,EAAC,qBAAqB;QAAAC,QAAA,EAAEV,iBAAiB,CAAC;MAAC;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzD,CAAC,eAGN5B,OAAA;MAAKsB,SAAS,EAAC,mBAAmB;MAAAC,QAAA,GAE/BjB,iBAAiB,CAACE,iBAAiB,iBAClCR,OAAA;QAAKsB,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAChCvB,OAAA,CAACP,aAAa;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CACN,EAGAtB,iBAAiB,CAACG,sBAAsB,iBACvCT,OAAA;QAAKsB,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAChCvB,OAAA,CAACN,WAAW;UAACkB,eAAe,EAAEN,iBAAiB,CAACM;QAAgB;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChE,CACN,EAGAtB,iBAAiB,CAACI,mBAAmB,iBACpCV,OAAA;QAAKsB,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAChCvB,OAAA;UAAKsB,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5BvB,OAAA,CAACJ,oBAAoB;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAtB,iBAAiB,CAACK,gBAAgB,iBACjCX,OAAA;QAAKsB,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAChCvB,OAAA;UAAKsB,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5BvB,OAAA,CAACL,iBAAiB;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC1B,EAAA,CA7GID,SAAS;AAAA4B,EAAA,GAAT5B,SAAS;AA+Gf,eAAeA,SAAS;AAAC,IAAA4B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}