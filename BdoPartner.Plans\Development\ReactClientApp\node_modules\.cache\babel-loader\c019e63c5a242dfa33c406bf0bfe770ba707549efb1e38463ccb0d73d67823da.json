{"ast": null, "code": "import { reduce } from './reduce';\nimport { operate } from '../util/lift';\nvar arrReducer = function (arr, value) {\n  return arr.push(value), arr;\n};\nexport function toArray() {\n  return operate(function (source, subscriber) {\n    reduce(arrReducer, [])(source).subscribe(subscriber);\n  });\n}", "map": {"version": 3, "names": ["reduce", "operate", "arrReducer", "arr", "value", "push", "toArray", "source", "subscriber", "subscribe"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\toArray.ts"], "sourcesContent": ["import { reduce } from './reduce';\nimport { OperatorFunction } from '../types';\nimport { operate } from '../util/lift';\n\nconst arrReducer = (arr: any[], value: any) => (arr.push(value), arr);\n\n/**\n * Collects all source emissions and emits them as an array when the source completes.\n *\n * <span class=\"informal\">Get all values inside an array when the source completes</span>\n *\n * ![](toArray.png)\n *\n * `toArray` will wait until the source Observable completes before emitting\n * the array containing all emissions. When the source Observable errors no\n * array will be emitted.\n *\n * ## Example\n *\n * ```ts\n * import { interval, take, toArray } from 'rxjs';\n *\n * const source = interval(1000);\n * const example = source.pipe(\n *   take(10),\n *   toArray()\n * );\n *\n * example.subscribe(value => console.log(value));\n *\n * // output: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]\n * ```\n *\n * @return A function that returns an Observable that emits an array of items\n * emitted by the source Observable when source completes.\n */\nexport function toArray<T>(): OperatorFunction<T, T[]> {\n  // Because arrays are mutable, and we're mutating the array in this\n  // reducer process, we have to encapsulate the creation of the initial\n  // array within this `operate` function.\n  return operate((source, subscriber) => {\n    reduce(arrReducer, [] as T[])(source).subscribe(subscriber);\n  });\n}\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,UAAU;AAEjC,SAASC,OAAO,QAAQ,cAAc;AAEtC,IAAMC,UAAU,GAAG,SAAAA,CAACC,GAAU,EAAEC,KAAU;EAAK,OAACD,GAAG,CAACE,IAAI,CAACD,KAAK,CAAC,EAAED,GAAG;AAArB,CAAsB;AAgCrE,OAAM,SAAUG,OAAOA,CAAA;EAIrB,OAAOL,OAAO,CAAC,UAACM,MAAM,EAAEC,UAAU;IAChCR,MAAM,CAACE,UAAU,EAAE,EAAS,CAAC,CAACK,MAAM,CAAC,CAACE,SAAS,CAACD,UAAU,CAAC;EAC7D,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}