{"ast": null, "code": "import { bindCallbackInternals } from './bindCallbackInternals';\nexport function bindCallback(callbackFunc, resultSelector, scheduler) {\n  return bindCallbackInternals(false, callbackFunc, resultSelector, scheduler);\n}", "map": {"version": 3, "names": ["bindCallbackInternals", "bind<PERSON>allback", "callback<PERSON><PERSON><PERSON>", "resultSelector", "scheduler"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\observable\\bindCallback.ts"], "sourcesContent": ["/* @prettier */\nimport { SchedulerLike } from '../types';\nimport { Observable } from '../Observable';\nimport { bindCallbackInternals } from './bindCallbackInternals';\n\nexport function bindCallback(\n  callbackFunc: (...args: any[]) => void,\n  resultSelector: (...args: any[]) => any,\n  scheduler?: SchedulerLike\n): (...args: any[]) => Observable<any>;\n\n// args is the arguments array and we push the callback on the rest tuple since the rest parameter must be last (only item) in a parameter list\nexport function bindCallback<A extends readonly unknown[], R extends readonly unknown[]>(\n  callbackFunc: (...args: [...A, (...res: R) => void]) => void,\n  schedulerLike?: SchedulerLike\n): (...arg: A) => Observable<R extends [] ? void : R extends [any] ? R[0] : R>;\n\n/**\n * Converts a callback API to a function that returns an Observable.\n *\n * <span class=\"informal\">Give it a function `f` of type `f(x, callback)` and\n * it will return a function `g` that when called as `g(x)` will output an\n * Observable.</span>\n *\n * `bindCallback` is not an operator because its input and output are not\n * Observables. The input is a function `func` with some parameters. The\n * last parameter must be a callback function that `func` calls when it is\n * done.\n *\n * The output of `bindCallback` is a function that takes the same parameters\n * as `func`, except the last one (the callback). When the output function\n * is called with arguments it will return an Observable. If function `func`\n * calls its callback with one argument, the Observable will emit that value.\n * If on the other hand the callback is called with multiple values the resulting\n * Observable will emit an array with said values as arguments.\n *\n * It is **very important** to remember that input function `func` is not called\n * when the output function is, but rather when the Observable returned by the output\n * function is subscribed. This means if `func` makes an AJAX request, that request\n * will be made every time someone subscribes to the resulting Observable, but not before.\n *\n * The last optional parameter - `scheduler` - can be used to control when the call\n * to `func` happens after someone subscribes to Observable, as well as when results\n * passed to callback will be emitted. By default, the subscription to an Observable calls `func`\n * synchronously, but using {@link asyncScheduler} as the last parameter will defer the call to `func`,\n * just like wrapping the call in `setTimeout` with a timeout of `0` would. If you were to use the async Scheduler\n * and call `subscribe` on the output Observable, all function calls that are currently executing\n * will end before `func` is invoked.\n *\n * By default, results passed to the callback are emitted immediately after `func` invokes the callback.\n * In particular, if the callback is called synchronously, then the subscription of the resulting Observable\n * will call the `next` function synchronously as well.  If you want to defer that call,\n * you may use {@link asyncScheduler} just as before.  This means that by using `Scheduler.async` you can\n * ensure that `func` always calls its callback asynchronously, thus avoiding terrifying Zalgo.\n *\n * Note that the Observable created by the output function will always emit a single value\n * and then complete immediately. If `func` calls the callback multiple times, values from subsequent\n * calls will not appear in the stream. If you need to listen for multiple calls,\n *  you probably want to use {@link fromEvent} or {@link fromEventPattern} instead.\n *\n * If `func` depends on some context (`this` property) and is not already bound, the context of `func`\n * will be the context that the output function has at call time. In particular, if `func`\n * is called as a method of some object and if `func` is not already bound, in order to preserve the context\n * it is recommended that the context of the output function is set to that object as well.\n *\n * If the input function calls its callback in the \"node style\" (i.e. first argument to callback is\n * optional error parameter signaling whether the call failed or not), {@link bindNodeCallback}\n * provides convenient error handling and probably is a better choice.\n * `bindCallback` will treat such functions the same as any other and error parameters\n * (whether passed or not) will always be interpreted as regular callback argument.\n *\n * ## Examples\n *\n * Convert jQuery's getJSON to an Observable API\n *\n * ```ts\n * import { bindCallback } from 'rxjs';\n * import * as jQuery from 'jquery';\n *\n * // Suppose we have jQuery.getJSON('/my/url', callback)\n * const getJSONAsObservable = bindCallback(jQuery.getJSON);\n * const result = getJSONAsObservable('/my/url');\n * result.subscribe(x => console.log(x), e => console.error(e));\n * ```\n *\n * Receive an array of arguments passed to a callback\n *\n * ```ts\n * import { bindCallback } from 'rxjs';\n *\n * const someFunction = (n, s, cb) => {\n *   cb(n, s, { someProperty: 'someValue' });\n * };\n *\n * const boundSomeFunction = bindCallback(someFunction);\n * boundSomeFunction(5, 'some string').subscribe((values) => {\n *   console.log(values); // [5, 'some string', {someProperty: 'someValue'}]\n * });\n * ```\n *\n * Compare behaviour with and without `asyncScheduler`\n *\n * ```ts\n * import { bindCallback, asyncScheduler } from 'rxjs';\n *\n * function iCallMyCallbackSynchronously(cb) {\n *   cb();\n * }\n *\n * const boundSyncFn = bindCallback(iCallMyCallbackSynchronously);\n * const boundAsyncFn = bindCallback(iCallMyCallbackSynchronously, null, asyncScheduler);\n *\n * boundSyncFn().subscribe(() => console.log('I was sync!'));\n * boundAsyncFn().subscribe(() => console.log('I was async!'));\n * console.log('This happened...');\n *\n * // Logs:\n * // I was sync!\n * // This happened...\n * // I was async!\n * ```\n *\n * Use `bindCallback` on an object method\n *\n * ```ts\n * import { bindCallback } from 'rxjs';\n *\n * const boundMethod = bindCallback(someObject.methodWithCallback);\n * boundMethod\n *   .call(someObject) // make sure methodWithCallback has access to someObject\n *   .subscribe(subscriber);\n * ```\n *\n * @see {@link bindNodeCallback}\n * @see {@link from}\n *\n * @param callbackFunc A function with a callback as the last parameter.\n * @param resultSelector A mapping function used to transform callback events.\n * @param scheduler The scheduler on which to schedule the callbacks.\n * @return A function which returns the Observable that delivers the same\n * values the callback would deliver.\n */\nexport function bindCallback(\n  callbackFunc: (...args: [...any[], (...res: any) => void]) => void,\n  resultSelector?: ((...args: any[]) => any) | SchedulerLike,\n  scheduler?: SchedulerLike\n): (...args: any[]) => Observable<unknown> {\n  return bindCallbackInternals(false, callbackFunc, resultSelector, scheduler);\n}\n"], "mappings": "AAGA,SAASA,qBAAqB,QAAQ,yBAAyB;AA2I/D,OAAM,SAAUC,YAAYA,CAC1BC,YAAkE,EAClEC,cAA0D,EAC1DC,SAAyB;EAEzB,OAAOJ,qBAAqB,CAAC,KAAK,EAAEE,YAAY,EAAEC,cAAc,EAAEC,SAAS,CAAC;AAC9E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}