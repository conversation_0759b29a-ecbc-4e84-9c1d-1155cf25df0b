{"ast": null, "code": "/*!\n * SurveyJS Creator React v2.2.2\n * (c) 2015-2025 Devsoft Baltic OÜ - http://surveyjs.io/\n * Github: https://github.com/surveyjs/survey-creator\n * License: https://surveyjs.io/Licenses#SurveyCreator\n */\n\nimport * as React from 'react';\nimport { createElement, Fragment } from 'react';\nimport { ReactElementFactory, SurveyElementBase, attachKey2click, SvgIcon, Survey, ReactQuestionFactory, SurveyLocStringViewer, SvgBundleComponent, PopupModal, SurveyActionBar, NotifierComponent, TitleElement, ReactSurveyElementsWrapper, LoadingIndicatorComponent, SurveyPage, Popup, LogoImage, SurveyQuestionElementBase, SurveyQuestion, Scroll, CharacterCounterComponent, SurveyQuestionDropdown, SurveyHeader, List, SurveyQuestionText } from 'survey-react-ui';\nimport { SurveyCreatorModel, assign, RowViewModel, QuestionAdornerViewModel, QuestionDropdownAdornerViewModel, QuestionImageAdornerViewModel, QuestionRatingA<PERSON>ner<PERSON>iewModel, PageAdorner, LogoImageViewModel, editorLocalization, ItemValueWrapperViewModel, ImageItemValueWrapperViewModel, MatrixCellWrapperViewModel, SurveyResultsModel, ToolboxToolViewModel, editableStringRendererName, StringEditorViewModelBase, initLogicOperator, PageNavigatorViewModel } from 'survey-creator-core';\nexport { PropertyGridEditorCollection, SurveyLogic, SurveyLogicUI, SurveyQuestionEditorDefinition, ToolboxToolViewModel, editorLocalization, localization, settings, svgBundle } from 'survey-creator-core';\nimport * as ReactDOM from 'react-dom';\nimport { CssClassBuilder, settings, RendererFactory, unwrap, checkLibraryVersion } from 'survey-core';\nclass TabbedMenuComponent extends SurveyElementBase {\n  get model() {\n    return this.props.model;\n  }\n  getStateElement() {\n    return this.model;\n  }\n  constructor(props) {\n    super(props);\n    this.rootRef = React.createRef();\n  }\n  renderElement() {\n    const items = this.model.renderedActions.map(item => React.createElement(TabbedMenuItemWrapper, {\n      item: item,\n      key: item.renderedId\n    }));\n    return React.createElement(\"div\", {\n      ref: this.rootRef,\n      className: \"svc-tabbed-menu\",\n      role: \"tablist\",\n      style: this.model.getRootStyle()\n    }, items);\n  }\n  componentDidUpdate(prevProps, prevState) {\n    super.componentDidUpdate(prevProps, prevState);\n    const container = this.rootRef.current;\n    if (!container) return;\n    this.model.initResponsivityManager(container);\n  }\n  componentDidMount() {\n    super.componentDidMount();\n    const container = this.rootRef.current;\n    if (!container) return;\n    this.model.initResponsivityManager(container);\n  }\n  componentWillUnmount() {\n    this.model.resetResponsivityManager();\n    super.componentWillUnmount();\n  }\n}\nclass TabbedMenuItemWrapper extends SurveyElementBase {\n  constructor(props) {\n    super(props);\n    this.ref = React.createRef();\n  }\n  get item() {\n    return this.props.item;\n  }\n  getStateElement() {\n    return this.item;\n  }\n  renderElement() {\n    let css = \"svc-tabbed-menu-item-container\";\n    if (this.item.css) {\n      css += \" \" + this.item.css;\n    }\n    css += !this.item.isVisible ? \" sv-action--hidden\" : \"\";\n    const component = ReactElementFactory.Instance.createElement(this.item.component || \"svc-tabbed-menu-item\", {\n      item: this.item\n    });\n    return React.createElement(\"span\", {\n      key: this.item.id,\n      className: css,\n      ref: this.ref\n    }, React.createElement(\"div\", {\n      className: \"sv-action__content\"\n    }, component));\n  }\n  componentDidMount() {\n    super.componentDidMount();\n    this.item.updateModeCallback = (mode, callback) => {\n      queueMicrotask(() => {\n        if (ReactDOM[\"flushSync\"]) {\n          ReactDOM[\"flushSync\"](() => {\n            this.item.mode = mode;\n          });\n        } else {\n          this.item.mode = mode;\n        }\n        queueMicrotask(() => {\n          callback(mode, this.ref.current);\n        });\n      });\n    };\n    this.item.afterRender();\n  }\n  componentWillUnmount() {\n    super.componentWillUnmount();\n    this.item.updateModeCallback = undefined;\n  }\n}\nclass TabbedMenuItemComponent extends SurveyElementBase {\n  get item() {\n    return this.props.item;\n  }\n  getStateElement() {\n    return this.item;\n  }\n  render() {\n    const item = this.item;\n    return attachKey2click(React.createElement(\"div\", {\n      role: \"tab\",\n      id: \"tab-\" + item.id,\n      \"aria-selected\": item.active,\n      \"aria-controls\": \"scrollableDiv-\" + item.id,\n      className: item.getRootCss(),\n      onClick: () => item.action(item)\n    }, item.hasTitle ? React.createElement(\"span\", {\n      className: item.getTitleCss()\n    }, item.title) : null, item.hasIcon ? React.createElement(SvgIcon, {\n      iconName: item.iconName,\n      className: item.getIconCss(),\n      size: \"auto\",\n      title: item.tooltip || item.title\n    }) : null));\n  }\n}\nReactElementFactory.Instance.registerElement(\"svc-tabbed-menu-item\", props => {\n  return React.createElement(TabbedMenuItemComponent, props);\n});\nclass SurveyCreatorComponent extends SurveyElementBase {\n  constructor(props) {\n    super(props);\n    this.rootNode = React.createRef();\n  }\n  get creator() {\n    return this.props.creator;\n  }\n  getStateElement() {\n    return this.creator;\n  }\n  get style() {\n    return this.props.style;\n  }\n  componentDidUpdate(prevProps, prevState) {\n    super.componentDidUpdate(prevProps, prevState);\n    if (this.creator !== prevProps.creator) {\n      if (prevProps.creator) {\n        prevProps.creator.unsubscribeRootElement();\n      }\n      if (this.creator && this.rootNode.current) {\n        this.creator.setRootElement(this.rootNode.current);\n      }\n    }\n  }\n  componentDidMount() {\n    super.componentDidMount();\n    this.creator.setRootElement(this.rootNode.current);\n  }\n  componentWillUnmount() {\n    super.componentWillUnmount();\n    this.creator.unsubscribeRootElement();\n  }\n  renderElement() {\n    const creator = this.props.creator;\n    if (creator.isCreatorDisposed) return null;\n    const areaClassName = \"svc-full-container svc-creator__area svc-flex-column\" + (this.props.creator.haveCommercialLicense ? \"\" : \" svc-creator__area--with-banner\");\n    const contentWrapperClassName = \"svc-creator__content-wrapper svc-flex-row\" + (this.props.creator.isMobileView ? \" svc-creator__content-wrapper--footer-toolbar\" : \"\");\n    const fullContainerClassName = \"svc-flex-row svc-full-container\" + (\" svc-creator__side-bar--\" + this.creator.sidebarLocation);\n    const creatorStyles = {};\n    assign(creatorStyles, this.style, this.props.creator.themeVariables);\n    let licenseBanner = null;\n    if (!this.props.creator.haveCommercialLicense) {\n      const htmlValue = {\n        __html: this.props.creator.licenseText\n      };\n      licenseBanner = React.createElement(\"div\", {\n        className: \"svc-creator__banner\"\n      }, React.createElement(\"span\", {\n        className: \"svc-creator__non-commercial-text\",\n        dangerouslySetInnerHTML: htmlValue\n      }));\n    }\n    //AM: width unrecognized by react\n    return React.createElement(\"div\", {\n      className: this.creator.getRootCss(),\n      ref: this.rootNode,\n      style: creatorStyles\n    }, React.createElement(SvgBundleComponent, null), React.createElement(PopupModal, null), React.createElement(\"div\", {\n      className: areaClassName\n    }, React.createElement(\"div\", {\n      className: fullContainerClassName\n    }, React.createElement(\"div\", {\n      className: \"svc-flex-column svc-flex-row__element svc-flex-row__element--growing\"\n    }, React.createElement(\"div\", {\n      className: \"svc-top-bar\"\n    }, creator.showTabs ? React.createElement(\"div\", {\n      className: \"svc-tabbed-menu-wrapper\"\n    }, React.createElement(TabbedMenuComponent, {\n      model: creator.tabbedMenu\n    })) : null, creator.showToolbar ? React.createElement(\"div\", {\n      className: \"svc-toolbar-wrapper\"\n    }, React.createElement(SurveyActionBar, {\n      model: creator.toolbar\n    })) : null), React.createElement(\"div\", {\n      className: contentWrapperClassName\n    }, React.createElement(\"div\", {\n      className: \"svc-creator__content-holder svc-flex-column\"\n    }, this.renderActiveTab())), React.createElement(\"div\", {\n      className: \"svc-footer-bar\"\n    }, creator.isMobileView ? React.createElement(\"div\", {\n      className: \"svc-toolbar-wrapper\"\n    }, React.createElement(SurveyActionBar, {\n      model: creator.footerToolbar\n    })) : null)), this.renderSidebar()), licenseBanner, React.createElement(NotifierComponent, {\n      notifier: creator.notifier\n    })));\n  }\n  renderActiveTab() {\n    const creator = this.props.creator;\n    for (var i = 0; i < creator.tabs.length; i++) {\n      if (creator.tabs[i].id === creator.activeTab) {\n        return this.renderCreatorTab(creator.tabs[i]);\n      }\n    }\n    return null;\n  }\n  renderCreatorTab(tab) {\n    if (tab.visible === false) {\n      return null;\n    }\n    const creator = this.props.creator;\n    const component = !!tab.renderTab ? tab.renderTab() : ReactElementFactory.Instance.createElement(tab.componentContent, {\n      creator: creator,\n      survey: creator.survey,\n      data: tab.data.model\n    });\n    const className = \"svc-creator-tab\" + (creator.toolboxLocation == \"right\" ? \" svc-creator__toolbox--right\" : \"\");\n    return React.createElement(\"div\", {\n      role: \"tabpanel\",\n      key: tab.id,\n      id: \"scrollableDiv-\" + tab.id,\n      \"aria-labelledby\": \"tab-\" + tab.id,\n      className: className\n    }, component);\n  }\n  renderSidebar() {\n    if (!!this.creator.sidebar) {\n      return ReactElementFactory.Instance.createElement(\"svc-side-bar\", {\n        model: this.creator.sidebar\n      });\n    } else {\n      return null;\n    }\n  }\n}\nclass SurveyCreator extends SurveyCreatorModel {\n  constructor(options = {}, options2) {\n    super(options, options2);\n  }\n  render(target) {\n    // eslint-disable-next-line no-console\n    console.error(\"The render method is deprecated. Use SurveyCreatorComponent instead.\");\n  }\n  //ISurveyCreator\n  createQuestionElement(question) {\n    return ReactQuestionFactory.Instance.createQuestion(question.isDefaultRendering() ? question.getTemplate() : question.getComponentName(), {\n      question: question,\n      isDisplayMode: question.isReadOnly,\n      creator: this\n    });\n  }\n  renderError(key, error, cssClasses) {\n    return React.createElement(\"div\", {\n      key: key\n    }, React.createElement(\"span\", {\n      className: cssClasses.error.icon,\n      \"aria-hidden\": \"true\"\n    }), React.createElement(\"span\", {\n      className: cssClasses.error.item\n    }, React.createElement(SurveyLocStringViewer, {\n      locStr: error.locText\n    })));\n  }\n  questionTitleLocation() {\n    return this.survey.questionTitleLocation;\n  }\n  questionErrorLocation() {\n    return this.survey.questionErrorLocation;\n  }\n}\nReactElementFactory.Instance.registerElement(\"survey-widget\", props => {\n  return React.createElement(Survey, props);\n});\nclass CreatorModelElement extends SurveyElementBase {\n  constructor(props) {\n    super(props);\n    this.createModel(props);\n  }\n  shouldComponentUpdate(nextProps, nextState) {\n    const result = super.shouldComponentUpdate(nextProps, nextState);\n    if (result) {\n      if (this.needUpdateModel(nextProps)) {\n        this.createModel(nextProps);\n      }\n    }\n    return result;\n  }\n  createModel(props) {}\n  needUpdateModel(nextProps) {\n    const names = this.getUpdatedModelProps();\n    if (!Array.isArray(names)) return true;\n    for (var i = 0; i < names.length; i++) {\n      const key = names[i];\n      if (this.props[key] !== nextProps[key]) return true;\n    }\n    return false;\n  }\n  getUpdatedModelProps() {\n    return undefined;\n  }\n}\nclass RowWrapper extends CreatorModelElement {\n  constructor(props) {\n    super(props);\n  }\n  createModel(props) {\n    if (!!this.model) {\n      this.model.dispose();\n    }\n    this.model = new RowViewModel(props.componentData.creator, props.row, null);\n  }\n  getUpdatedModelProps() {\n    return [\"row\", \"componentData\"];\n  }\n  getStateElement() {\n    return this.model;\n  }\n  componentDidMount() {\n    super.componentDidMount();\n    this.model.subscribeElementChanges();\n  }\n  componentWillUnmount() {\n    this.model.unsubscribeElementChanges();\n    super.componentWillUnmount();\n  }\n  render() {\n    return React.createElement(\"div\", {\n      key: \"svc-row-\" + this.props.row.id,\n      className: this.model.cssClasses\n    }, React.createElement(\"div\", {\n      className: \"svc-row__drop-indicator svc-row__drop-indicator--top\"\n    }), React.createElement(\"div\", {\n      className: \"svc-row__drop-indicator svc-row__drop-indicator--bottom\"\n    }), this.props.element);\n  }\n}\nReactElementFactory.Instance.registerElement(\"svc-row\", props => {\n  return React.createElement(RowWrapper, props);\n});\nclass ReactMouseEvent {\n  constructor(event) {\n    this.event = event;\n  }\n  stopPropagation() {\n    this.event.stopPropagation();\n    //this.event.nativeEvent.stopPropagation();\n    //this.event.nativeEvent.stopImmediatePropagation();\n  }\n  preventDefault() {\n    this.event.preventDefault();\n    //this.event.nativeEvent.preventDefault();\n  }\n  get cancelBubble() {\n    //return this.event.cancelBubble;\n    return false;\n  }\n  set cancelBubble(value) {\n    //this.event.cancelBubble = value;\n  }\n  get target() {\n    return this.event.target;\n  }\n  get currentTarget() {\n    return this.event.currentTarget;\n  }\n  get clientX() {\n    return this.event.clientX;\n  }\n  get clientY() {\n    return this.event.clientY;\n  }\n  get offsetX() {\n    return this.event.nativeEvent.offsetX;\n  }\n  get offsetY() {\n    return this.event.nativeEvent.offsetY;\n  }\n}\nclass ReactDragEvent extends ReactMouseEvent {\n  constructor(event) {\n    super(event);\n    this.event = event;\n  }\n  get dataTransfer() {\n    return this.event.dataTransfer;\n  }\n}\nfunction QuestionElementContentFunc(props) {\n  return props.element;\n}\nconst QuestionElementContent = React.memo(QuestionElementContentFunc);\nQuestionElementContent.displayName = \"QuestionElementContent\";\nclass QuestionAdornerComponent extends CreatorModelElement {\n  constructor(props) {\n    super(props);\n    this.rootRef = React.createRef();\n  }\n  createModel(props) {\n    if (this.model) {\n      this.model.attachToUI(props.question, this.rootRef.current);\n    } else {\n      this.modelValue = this.createQuestionViewModel(props);\n    }\n  }\n  createQuestionViewModel(props) {\n    return new QuestionAdornerViewModel(props.componentData, props.question, null);\n  }\n  getUpdatedModelProps() {\n    return [\"question\", \"componentData\"];\n  }\n  get model() {\n    return this.modelValue;\n  }\n  getStateElement() {\n    return this.model;\n  }\n  renderElement() {\n    const allowInteractions = this.model.element.isInteractiveDesignElement;\n    const titleForCollapsedState = this.renderQuestionTitle();\n    const content = this.renderContent(allowInteractions);\n    return React.createElement(\"div\", {\n      ref: this.rootRef,\n      \"data-sv-drop-target-survey-element\": this.model.element.name || null,\n      className: this.model.rootCss(),\n      onDoubleClick: e => {\n        allowInteractions && this.model.dblclick(e.nativeEvent);\n        e.stopPropagation();\n      },\n      onMouseLeave: e => allowInteractions && this.model.hover(e.nativeEvent, e.currentTarget),\n      onMouseOver: e => allowInteractions && this.model.hover(e.nativeEvent, e.currentTarget)\n    }, titleForCollapsedState, content);\n  }\n  disableTabStop() {\n    return true;\n  }\n  renderContent(allowInteractions) {\n    var content = this.model.needToRenderContent ? this.renderElementContent() : null;\n    //if (!allowInteractions) return <>{content}{this.renderFooter()}</>;\n    return attachKey2click(React.createElement(\"div\", {\n      className: this.model.css(),\n      onClick: e => this.model.select(this.model, new ReactMouseEvent(e))\n    }, React.createElement(\"div\", {\n      className: \"svc-question__drop-indicator svc-question__drop-indicator--left\"\n    }), React.createElement(\"div\", {\n      className: \"svc-question__drop-indicator svc-question__drop-indicator--right\"\n    }), React.createElement(\"div\", {\n      className: \"svc-question__drop-indicator svc-question__drop-indicator--top\"\n    }), React.createElement(\"div\", {\n      className: \"svc-question__drop-indicator svc-question__drop-indicator--bottom\"\n    }), allowInteractions ? this.renderHeader() : null, content, this.model.needToRenderContent ? this.renderFooter() : null), undefined, {\n      disableTabStop: this.disableTabStop()\n    });\n  }\n  renderHeader() {\n    return ReactElementFactory.Instance.createElement(\"svc-question-header\", {\n      model: this.model\n    });\n  }\n  renderFooter() {\n    const allowInteractions = this.model.element.isInteractiveDesignElement;\n    return allowInteractions ? ReactElementFactory.Instance.createElement(\"svc-question-footer\", {\n      className: \"svc-question__content-actions\",\n      model: this.model\n    }) : null;\n  }\n  renderCarryForwardBanner() {\n    if (!this.model.isBannerShowing) return null;\n    return ReactElementFactory.Instance.createElement(\"svc-question-banner\", this.model.createBannerParams());\n  }\n  renderQuestionTitle() {\n    if (!this.model.showHiddenTitle) return null;\n    const element = this.model.element;\n    return React.createElement(\"div\", {\n      ref: node => node && (!this.model.renderedCollapsed ? node.setAttribute(\"inert\", \"\") : node.removeAttribute(\"inert\")),\n      className: this.model.cssCollapsedHiddenHeader\n    }, element.hasTitle ? React.createElement(TitleElement, {\n      element: element\n    }) : React.createElement(\"div\", {\n      className: this.model.cssCollapsedHiddenTitle\n    }, React.createElement(\"span\", {\n      className: \"svc-fake-title\"\n    }, element.name)));\n  }\n  renderElementContent() {\n    return React.createElement(React.Fragment, null, React.createElement(QuestionElementContent, {\n      element: this.props.element\n    }), this.renderElementPlaceholder(), this.renderCarryForwardBanner());\n  }\n  componentDidMount() {\n    super.componentDidMount();\n    this.model.attachToUI(this.props.question, this.rootRef.current);\n  }\n  renderElementPlaceholder() {\n    if (!this.model.isEmptyElement) {\n      return null;\n    }\n    return React.createElement(\"div\", {\n      className: \"svc-panel__placeholder_frame-wrapper\"\n    }, React.createElement(\"div\", {\n      className: \"svc-panel__placeholder_frame\"\n    }, React.createElement(\"div\", {\n      className: \"svc-panel__placeholder\"\n    }, this.model.placeholderText)));\n  }\n  componentWillUnmount() {\n    super.componentWillUnmount();\n    this.model.detachFromUI();\n  }\n}\nReactElementFactory.Instance.registerElement(\"svc-question\", props => {\n  return React.createElement(QuestionAdornerComponent, props);\n});\nclass QuestionWrapperHeader extends React.Component {\n  render() {\n    if (!this.props.model.allowDragging) return null;\n    return React.createElement(\"div\", {\n      className: \"svc-question__drag-area\",\n      onPointerDown: event => this.props.model.onPointerDown(event)\n    }, React.createElement(SvgIcon, {\n      className: \"svc-question__drag-element\",\n      size: \"auto\",\n      iconName: \"icon-drag-area-indicator_24x16\"\n    }), React.createElement(\"div\", {\n      className: \"svc-question__top-actions\"\n    }, React.createElement(SurveyActionBar, {\n      model: this.props.model.topActionContainer,\n      handleClick: false\n    })));\n  }\n}\nReactElementFactory.Instance.registerElement(\"svc-question-header\", props => {\n  return React.createElement(QuestionWrapperHeader, props);\n});\nclass QuestionWrapperFooter extends React.Component {\n  render() {\n    return React.createElement(\"div\", {\n      className: this.props.className,\n      onFocus: e => this.props.model.select(this.props.model, new ReactMouseEvent(e))\n    }, React.createElement(SurveyActionBar, {\n      model: this.props.model.actionContainer,\n      handleClick: false\n    }));\n  }\n}\nReactElementFactory.Instance.registerElement(\"svc-question-footer\", props => {\n  return React.createElement(QuestionWrapperFooter, props);\n});\nclass ActionButton extends SurveyElementBase {\n  renderElement() {\n    const classes = new CssClassBuilder().append(this.props.classes).append(\"svc-action-button\").append(\"svc-action-button--selected\", !!this.props.selected).append(\"svc-action-button--disabled\", !!this.props.disabled).toString();\n    if (this.props.iconName) {\n      return this.renderIcon(classes);\n    }\n    return this.renderButtonText(classes);\n  }\n  renderButtonText(classes) {\n    if (this.props.disabled) {\n      return React.createElement(\"span\", {\n        className: classes\n      }, this.props.text);\n    }\n    return React.createElement(React.Fragment, null, attachKey2click(React.createElement(\"span\", {\n      role: \"button\",\n      className: classes,\n      onClick: e => {\n        if (!this.props.allowBubble) {\n          e.stopPropagation();\n        }\n        this.props.click();\n      },\n      title: this.props.title\n    }, this.props.text)));\n  }\n  renderIcon(classes) {\n    classes += \" svc-action-button--icon\";\n    if (this.props.disabled) {\n      return React.createElement(\"span\", {\n        className: classes\n      }, React.createElement(SvgIcon, {\n        size: \"auto\",\n        iconName: this.props.iconName\n      }));\n    }\n    return React.createElement(React.Fragment, null, attachKey2click(React.createElement(\"span\", {\n      className: classes,\n      onClick: e => {\n        if (!this.props.allowBubble) {\n          e.stopPropagation();\n        }\n        this.props.click();\n      },\n      title: this.props.title\n    }, React.createElement(SvgIcon, {\n      size: \"auto\",\n      iconName: this.props.iconName\n    }))));\n  }\n}\nReactElementFactory.Instance.registerElement(\"svc-action-button\", props => {\n  return React.createElement(ActionButton, props);\n});\nclass QuestionBanner extends React.Component {\n  render() {\n    return React.createElement(\"div\", {\n      className: \"svc-carry-forward-panel-wrapper\"\n    }, React.createElement(\"div\", {\n      className: \"svc-carry-forward-panel\"\n    }, React.createElement(\"span\", null, this.props.text, \" \"), React.createElement(\"span\", {\n      className: \"svc-carry-forward-panel__link\"\n    }, React.createElement(ActionButton, {\n      click: () => this.props.onClick(),\n      text: this.props.actionText\n    }))));\n  }\n}\nReactElementFactory.Instance.registerElement(\"svc-question-banner\", props => {\n  return React.createElement(QuestionBanner, props);\n});\nclass QuestionDropdownAdornerComponent extends QuestionAdornerComponent {\n  constructor(props) {\n    super(props);\n  }\n  createQuestionViewModel(props) {\n    return new QuestionDropdownAdornerViewModel(props.componentData, props.question, null);\n  }\n  get dropdownModel() {\n    return this.model;\n  }\n  get question() {\n    return this.dropdownModel.question;\n  }\n  renderElementPlaceholder() {\n    const textStyle = this.question.textStyle;\n    return React.createElement(\"div\", {\n      className: \"svc-question__dropdown-choices--wrapper\"\n    }, React.createElement(\"div\", null, React.createElement(\"div\", {\n      className: \"svc-question__dropdown-choices\"\n    }, (this.dropdownModel.getRenderedItems() || []).map((item, index) => React.createElement(\"div\", {\n      className: this.dropdownModel.getChoiceCss(),\n      key: `editable_choice_${index}`\n    }, ReactSurveyElementsWrapper.wrapItemValue(this.question.survey, ReactElementFactory.Instance.createElement(this.dropdownModel.itemComponent, {\n      key: item.value,\n      question: this.question,\n      cssClasses: this.question.cssClasses,\n      isDisplayMode: true,\n      item: item,\n      textStyle: textStyle,\n      index: index,\n      isChecked: this.question.value === item.value\n    }), this.question, item)))), this.dropdownModel.needToCollapse ? React.createElement(ActionButton, {\n      click: this.dropdownModel.switchCollapse,\n      text: this.dropdownModel.getButtonText(),\n      allowBubble: true\n    }) : null));\n  }\n}\nReactElementFactory.Instance.registerElement(\"svc-dropdown-question\", props => {\n  return React.createElement(QuestionDropdownAdornerComponent, props);\n});\nclass QuestionImageAdornerComponent extends QuestionAdornerComponent {\n  createQuestionViewModel(props) {\n    return new QuestionImageAdornerViewModel(props.componentData, props.question, null);\n  }\n  get imageModel() {\n    return this.model;\n  }\n  renderHeader() {\n    return React.createElement(React.Fragment, null, React.createElement(\"input\", {\n      type: \"file\",\n      \"aria-hidden\": \"true\",\n      tabIndex: -1,\n      accept: this.imageModel.acceptedTypes,\n      className: \"svc-choose-file-input\",\n      style: {\n        position: \"absolute\",\n        opacity: 0,\n        width: \"1px\",\n        height: \"1px\",\n        overflow: \"hidden\"\n      }\n    }), super.renderHeader());\n  }\n  renderLoadingPlaceholder() {\n    return React.createElement(\"div\", {\n      className: \"svc-image-question__loading-placeholder\"\n    }, React.createElement(\"div\", {\n      className: \"svc-image-question__loading\"\n    }, React.createElement(LoadingIndicatorComponent, null)));\n  }\n  renderChooseButton() {\n    return React.createElement(\"div\", {\n      className: \"svc-image-question-controls\"\n    }, this.model.allowEdit ? attachKey2click(React.createElement(\"span\", {\n      className: \"svc-context-button\",\n      onClick: () => this.imageModel.chooseFile(this.imageModel)\n    }, React.createElement(SvgIcon, {\n      size: \"auto\",\n      iconName: \"icon-choosefile\"\n    }))) : null);\n  }\n  renderElementPlaceholder() {\n    return this.imageModel.isUploading ? this.renderLoadingPlaceholder() : this.renderChooseButton();\n  }\n  getStateElements() {\n    return [this.model, this.imageModel.filePresentationModel];\n  }\n  renderElementContent() {\n    if (this.imageModel.isEmptyImageLink) {\n      const fileQuestion = ReactQuestionFactory.Instance.createQuestion(\"file\", {\n        creator: this.imageModel.question.survey,\n        isDisplayMode: false,\n        question: this.imageModel.filePresentationModel\n      });\n      return React.createElement(React.Fragment, null, fileQuestion);\n    } else {\n      return React.createElement(React.Fragment, null, this.props.element, this.renderElementPlaceholder());\n    }\n  }\n}\nReactElementFactory.Instance.registerElement(\"svc-image-question\", props => {\n  return React.createElement(QuestionImageAdornerComponent, props);\n});\nclass QuestionRatingAdornerComponent extends CreatorModelElement {\n  createModel(props) {\n    this.modelValue = this.createQuestionViewModel(props);\n  }\n  createQuestionViewModel(props) {\n    return new QuestionRatingAdornerViewModel(props.componentData, props.question, null);\n  }\n  getUpdatedModelProps() {\n    return [\"question\", \"componentData\"];\n  }\n  get ratingModel() {\n    return this.model;\n  }\n  get model() {\n    return this.modelValue;\n  }\n  getStateElement() {\n    return this.model;\n  }\n  renderElement() {\n    const model = this.ratingModel;\n    return React.createElement(React.Fragment, null, React.createElement(\"div\", {\n      className: \"svc-rating-question-content\"\n    }, React.createElement(\"div\", {\n      className: model.controlsClassNames\n    }, model.allowRemove ? attachKey2click(React.createElement(\"span\", {\n      role: \"button\",\n      className: model.removeClassNames,\n      \"aria-label\": model.removeTooltip,\n      onClick: () => model.removeItem(model)\n    }, React.createElement(SvgIcon, {\n      size: \"auto\",\n      iconName: \"icon-remove_16x16\",\n      title: model.removeTooltip\n    }))) : null, model.allowAdd ? attachKey2click(React.createElement(\"span\", {\n      role: \"button\",\n      className: model.addClassNames,\n      \"aria-label\": model.addTooltip,\n      onClick: () => model.addItem(model)\n    }, React.createElement(SvgIcon, {\n      size: \"auto\",\n      iconName: \"icon-add_16x16\",\n      title: model.addTooltip\n    }))) : null), this.props.element));\n  }\n}\nReactElementFactory.Instance.registerElement(\"svc-rating-question\", props => {\n  return React.createElement(QuestionRatingAdornerComponent, props);\n});\nReactElementFactory.Instance.registerElement(\"svc-rating-question-content\", props => {\n  return React.createElement(QuestionRatingAdornerComponent, props);\n});\nclass QuestionWidgetAdornerComponent extends QuestionAdornerComponent {\n  createQuestionViewModel(props) {\n    return new QuestionAdornerViewModel(props.componentData, props.question, null);\n  }\n  get widgetModel() {\n    return this.model;\n  }\n  renderElementContent() {\n    return React.createElement(\"div\", {\n      className: \"svc-widget__content\"\n    }, this.props.element);\n  }\n}\nReactElementFactory.Instance.registerElement(\"svc-widget-question\", props => {\n  return React.createElement(QuestionWidgetAdornerComponent, props);\n});\nclass CellQuestionAdornerComponent extends CreatorModelElement {\n  createModel(props) {\n    this.model = new QuestionAdornerViewModel(props.componentData, props.question, null);\n  }\n  getStateElement() {\n    return this.model;\n  }\n  getUpdatedModelProps() {\n    return [\"question\", \"componentData\"];\n  }\n  render() {\n    return React.createElement(React.Fragment, null, React.createElement(\"div\", {\n      \"data-sv-drop-target-survey-element\": this.model.element.name,\n      className: \"svc-question__adorner\"\n    }, React.createElement(\"div\", {\n      className: \" svc-question__content--in-popup svc-question__content\"\n    }, this.props.element)));\n  }\n}\nReactElementFactory.Instance.registerElement(\"svc-cell-question\", props => {\n  return React.createElement(CellQuestionAdornerComponent, props);\n});\nclass CellQuestionDropdownAdornerComponent extends CreatorModelElement {\n  createModel(props) {\n    this.model = new QuestionAdornerViewModel(props.componentData, props.question, null);\n  }\n  getUpdatedModelProps() {\n    return [\"question\", \"componentData\"];\n  }\n  getStateElement() {\n    return this.model;\n  }\n  render() {\n    const question = this.props.question;\n    const textStyle = this.props.question.textStyle;\n    return React.createElement(React.Fragment, null, React.createElement(\"div\", {\n      \"data-sv-drop-target-survey-element\": this.model.element.name,\n      className: \"svc-question__adorner\"\n    }, React.createElement(\"div\", {\n      className: \" svc-question__content--in-popup svc-question__content\"\n    }, this.props.element, React.createElement(\"div\", {\n      className: \"svc-question__dropdown-choices\"\n    }, question.visibleChoices.map((item, index) => React.createElement(\"div\", {\n      className: \"svc-question__dropdown-choice\",\n      key: `editable_choice_${index}`\n    }, ReactSurveyElementsWrapper.wrapItemValue(question.survey, ReactElementFactory.Instance.createElement(\"survey-radiogroup-item\", {\n      question: question,\n      cssClasses: question.cssClasses,\n      isDisplayMode: true,\n      item: item,\n      textStyle: textStyle,\n      index: index,\n      isChecked: question.value === item.value\n    }), question, item)))))));\n  }\n}\nReactElementFactory.Instance.registerElement(\"svc-cell-dropdown-question\", props => {\n  return React.createElement(CellQuestionDropdownAdornerComponent, props);\n});\nconst PageElementContent = React.memo(({\n  page,\n  survey,\n  creator\n}) => {\n  return React.createElement(SurveyPage, {\n    page: page,\n    survey: survey,\n    creator: creator\n  });\n});\nPageElementContent.displayName = \"PageElementContent\";\nclass CreatorSurveyPageComponent extends CreatorModelElement {\n  constructor(props) {\n    super(props);\n    this.rootRef = React.createRef();\n  }\n  createModel(props) {\n    if (this.model) {\n      this.model.attachToUI(props.page, this.rootRef.current);\n    }\n    this.model = this.createPageAdorner(props.creator, props.page);\n    this.model.isGhost = this.props.isGhost;\n  }\n  createPageAdorner(creator, page) {\n    return new PageAdorner(creator, page);\n  }\n  shouldComponentUpdate(nextProps, nextState) {\n    const res = super.shouldComponentUpdate(nextProps, nextState);\n    if (this.model) {\n      this.model.isGhost = this.props.isGhost;\n    }\n    return res;\n  }\n  componentDidUpdate(prevProps, prevState) {\n    super.componentDidUpdate(prevProps, prevState);\n  }\n  getUpdatedModelProps() {\n    return [\"creator\", \"page\"];\n  }\n  getStateElement() {\n    return this.model;\n  }\n  componentDidMount() {\n    super.componentDidMount();\n    this.model.attachToUI(this.props.page, this.rootRef.current);\n    this.model.isGhost = this.props.isGhost;\n  }\n  componentWillUnmount() {\n    super.componentWillUnmount();\n    this.model.detachFromUI();\n  }\n  canRender() {\n    return super.canRender();\n  }\n  renderElement() {\n    if (!this.props.page) return null;\n    return attachKey2click(React.createElement(\"div\", {\n      ref: this.rootRef,\n      id: this.props.page.id,\n      \"data-sv-drop-target-survey-page\": this.model.dropTargetName,\n      className: \"svc-page__content \" + this.model.css,\n      onClick: e => {\n        return this.model.select(this.model, new ReactMouseEvent(e));\n      },\n      onDoubleClick: e => this.model.dblclick(e.nativeEvent),\n      onMouseLeave: e => this.model.hover(e.nativeEvent, e.currentTarget),\n      onMouseOver: e => this.model.hover(e.nativeEvent, e.currentTarget)\n    }, React.createElement(\"div\", {\n      className: \"svc-question__drop-indicator svc-question__drop-indicator--top\"\n    }), React.createElement(\"div\", {\n      className: \"svc-question__drop-indicator svc-question__drop-indicator--bottom\"\n    }), this.renderContent(), this.renderPlaceholder(), this.renderHeader(), this.renderFooter()));\n  }\n  renderPlaceholder() {\n    if (!this.model.showPlaceholder) return null;\n    return React.createElement(\"div\", {\n      className: \"svc-page__placeholder_frame\"\n    }, React.createElement(\"div\", {\n      className: \"svc-panel__placeholder_frame\"\n    }, React.createElement(\"div\", {\n      className: \"svc-panel__placeholder\"\n    }, this.model.placeholderText)));\n  }\n  renderContent() {\n    if (!this.model.needRenderContent) {\n      return React.createElement(\"div\", {\n        className: \"svc-page__loading-content\"\n      }, React.createElement(LoadingIndicatorComponent, null));\n    }\n    return React.createElement(PageElementContent, {\n      page: this.props.page,\n      survey: this.props.survey,\n      creator: this.props.creator\n    });\n  }\n  renderHeader() {\n    const actions = React.createElement(\"div\", {\n      className: \"svc-page__content-actions\"\n    }, React.createElement(SurveyActionBar, {\n      model: this.model.actionContainer\n    }), this.model.topActionContainer.hasActions ? React.createElement(SurveyActionBar, {\n      model: this.model.topActionContainer\n    }) : null);\n    if (this.model.isGhost || !this.model.allowDragging) {\n      return actions;\n    }\n    return React.createElement(\"div\", {\n      className: \"svc-question__drag-area\",\n      onPointerDown: event => this.model.onPointerDown(event)\n    }, React.createElement(SvgIcon, {\n      className: \"svc-question__drag-element\",\n      size: \"auto\",\n      iconName: \"icon-drag-area-indicator_24x16\"\n    }), actions);\n  }\n  renderFooter() {\n    return React.createElement(SurveyActionBar, {\n      model: this.model.footerActionsBar\n    });\n  }\n}\nReactElementFactory.Instance.registerElement(\"svc-page\", props => {\n  return React.createElement(CreatorSurveyPageComponent, props);\n});\nclass AddQuestionButtonComponent extends SurveyElementBase {\n  get model() {\n    return this.props.item.data;\n  }\n  renderTypeSelector() {\n    const questionTypeSelectorModel = this.model.questionTypeSelectorModel;\n    return attachKey2click(React.createElement(\"button\", {\n      type: \"button\",\n      onClick: e => {\n        e.stopPropagation();\n        questionTypeSelectorModel.action();\n      },\n      className: \"svc-element__question-type-selector\",\n      title: this.model.addNewQuestionText,\n      role: \"button\"\n    }, React.createElement(\"span\", {\n      className: \"svc-element__question-type-selector-icon\"\n    }, React.createElement(SvgIcon, {\n      iconName: questionTypeSelectorModel.iconName,\n      size: \"auto\",\n      title: this.model.addNewQuestionText\n    })), this.props.renderPopup === undefined || this.props.renderPopup ? React.createElement(Popup, {\n      model: questionTypeSelectorModel.popupModel\n    }) : null));\n  }\n  renderElement() {\n    const addButtonClass = this.props.buttonClass || \"svc-btn\";\n    return React.createElement(React.Fragment, null, attachKey2click(React.createElement(\"div\", {\n      className: \"svc-element__add-new-question \" + addButtonClass,\n      onClick: e => {\n        e.stopPropagation();\n        this.model.addNewQuestion(this.model, new ReactMouseEvent(e));\n      },\n      onMouseOver: e => this.model.hoverStopper && this.model.hoverStopper(e.nativeEvent, e.currentTarget)\n    }, React.createElement(SvgIcon, {\n      className: \"svc-panel__add-new-question-icon\",\n      iconName: \"icon-add_24x24\",\n      size: \"auto\"\n    }), React.createElement(\"span\", {\n      className: \"svc-add-new-item-button__text\"\n    }, this.model.addNewQuestionText), this.props.renderPopup !== false ? this.renderTypeSelector() : null)), this.props.renderPopup === false ? this.renderTypeSelector() : null);\n  }\n}\nReactElementFactory.Instance.registerElement(\"svc-add-new-question-btn\", props => {\n  return React.createElement(AddQuestionButtonComponent, props);\n});\nclass PanelAdornerComponent extends QuestionAdornerComponent {\n  renderElementPlaceholder() {\n    if (!this.model.isEmptyElement) {\n      return null;\n    }\n    return React.createElement(\"div\", {\n      className: \"svc-panel__placeholder_frame-wrapper\"\n    }, React.createElement(\"div\", {\n      className: \"svc-panel__placeholder_frame\"\n    }, React.createElement(\"div\", {\n      className: \"svc-panel__placeholder\"\n    }, this.model.placeholderText), this.model.showAddQuestionButton ? attachKey2click(React.createElement(\"div\", {\n      className: \"svc-panel__add-new-question svc-action-button\",\n      onClick: e => {\n        e.stopPropagation();\n        this.model.addNewQuestion();\n      }\n    }, React.createElement(SvgIcon, {\n      className: \"svc-panel__add-new-question-icon\",\n      iconName: \"icon-add_24x24\",\n      size: \"auto\"\n    }), React.createElement(\"span\", {\n      className: \"svc-add-new-item-button__text\"\n    }, this.model.addNewQuestionText))) : null));\n  }\n  disableTabStop() {\n    return true;\n  }\n  renderFooter() {\n    return React.createElement(React.Fragment, null, !this.model.isEmptyElement && this.model.element.isPanel && this.model.showAddQuestionButton ? React.createElement(\"div\", {\n      className: \"svc-panel__add-new-question-container\"\n    }, React.createElement(\"div\", {\n      className: \"svc-panel__question-type-selector-popup\"\n    }, React.createElement(Popup, {\n      model: this.model.questionTypeSelectorModel.popupModel\n    })), React.createElement(\"div\", {\n      className: \"svc-panel__add-new-question-wrapper\"\n    }, React.createElement(AddQuestionButtonComponent, {\n      item: {\n        data: this.model\n      },\n      buttonClass: \"svc-action-button\",\n      renderPopup: false\n    }))) : null, super.renderFooter());\n  }\n}\nReactElementFactory.Instance.registerElement(\"svc-panel\", props => {\n  return React.createElement(PanelAdornerComponent, props);\n});\nclass LogoImageComponent extends CreatorModelElement {\n  constructor(props) {\n    super(props);\n    this.rootRef = React.createRef();\n  }\n  createModel(props) {\n    let prevRoot = null;\n    if (!!this.model) {\n      prevRoot = this.model.root;\n    }\n    this.model = new LogoImageViewModel(props.data, prevRoot);\n  }\n  getUpdatedModelProps() {\n    return [\"data\"];\n  }\n  getStateElement() {\n    return this.model;\n  }\n  componentDidMount() {\n    super.componentDidMount();\n    this.model.root = this.rootRef.current;\n  }\n  renderChooseButton() {\n    return attachKey2click(React.createElement(\"span\", {\n      className: \"svc-context-button\",\n      onClick: () => this.model.chooseFile(this.model)\n    }, React.createElement(SvgIcon, {\n      size: \"auto\",\n      iconName: \"icon-choosefile\"\n    })));\n  }\n  renderClearButton() {\n    return attachKey2click(React.createElement(\"span\", {\n      className: \"svc-context-button svc-context-button--danger\",\n      onClick: () => this.model.remove(this.model)\n    }, React.createElement(SvgIcon, {\n      size: \"auto\",\n      iconName: \"icon-clear\"\n    })));\n  }\n  renderButtons() {\n    return React.createElement(\"div\", {\n      className: \"svc-context-container svc-logo-image-controls\"\n    }, this.renderChooseButton(), this.renderClearButton());\n  }\n  renderImage() {\n    return React.createElement(\"div\", {\n      className: this.model.containerCss\n    }, this.renderButtons(), React.createElement(LogoImage, {\n      data: this.props.data.survey\n    }));\n  }\n  renderPlaceHolder() {\n    return this.model.allowEdit && !this.model.isUploading ? attachKey2click(React.createElement(\"div\", {\n      className: \"svc-logo-image-placeholder\",\n      onClick: () => this.model.chooseFile(this.model)\n    }, React.createElement(\"svg\", null, React.createElement(\"use\", {\n      xlinkHref: \"#icon-image-48x48\"\n    })))) : null;\n  }\n  renderInput() {\n    return React.createElement(\"input\", {\n      \"aria-hidden\": \"true\",\n      type: \"file\",\n      tabIndex: -1,\n      accept: this.model.acceptedTypes,\n      className: \"svc-choose-file-input\"\n    });\n  }\n  renderLoadingIndicator() {\n    return React.createElement(\"div\", {\n      className: \"svc-logo-image__loading\"\n    }, React.createElement(LoadingIndicatorComponent, null));\n  }\n  render() {\n    let content = null;\n    if (this.model.survey.locLogo.renderedHtml && !this.model.isUploading) {\n      content = this.renderImage();\n    } else if (this.model.isUploading) {\n      content = this.renderLoadingIndicator();\n    } else {\n      content = this.renderPlaceHolder();\n    }\n    return React.createElement(\"div\", {\n      ref: this.rootRef,\n      className: \"svc-logo-image\"\n    }, this.renderInput(), content);\n  }\n}\nReactElementFactory.Instance.registerElement(\"svc-logo-image\", props => {\n  return React.createElement(LogoImageComponent, props);\n});\nclass SurveyQuestionLinkValue extends SurveyQuestionElementBase {\n  get question() {\n    return this.questionBase;\n  }\n  renderClear() {\n    const showClear = this.questionBase.showClear;\n    if (!this.questionBase.isReadOnly && showClear) {\n      return React.createElement(ActionButton, {\n        classes: this.question.linkClearButtonCssClasses,\n        click: () => this.question.doClearClick(),\n        text: editorLocalization.getString(\"pe.clear\")\n      });\n    } else {\n      return null;\n    }\n  }\n  renderElement() {\n    return React.createElement(React.Fragment, null, React.createElement(ActionButton, {\n      classes: this.question.linkSetButtonCssClasses,\n      click: () => this.question.doLinkClick(),\n      selected: this.question.isSelected,\n      disabled: !this.question.isClickable,\n      text: this.question.linkValueText,\n      title: this.question.tooltip,\n      iconName: this.question.iconName\n    }), this.renderClear());\n  }\n}\nReactQuestionFactory.Instance.registerQuestion(\"linkvalue\", props => {\n  return React.createElement(SurveyQuestionLinkValue, props);\n});\nclass SurveyElementEmbeddedSurvey extends SurveyQuestionElementBase {\n  get embeddedSurvey() {\n    return this.props.element || this.props.question;\n  }\n  get creator() {\n    return this.props.creator;\n  }\n  render() {\n    if (!this.embeddedSurvey) return null;\n    const survey = this.embeddedSurvey.embeddedSurvey;\n    if (!survey || !survey.currentPage) return null;\n    return React.createElement(SurveyPage, {\n      survey: survey,\n      page: survey.currentPage,\n      css: survey.css,\n      creator: this.creator\n    });\n  }\n}\nReactQuestionFactory.Instance.registerQuestion(\"embeddedsurvey\", props => {\n  return React.createElement(SurveyElementEmbeddedSurvey, props);\n});\nclass QuestionEditorContentComponent extends React.Component {\n  get survey() {\n    return this.props.survey;\n  }\n  createQuestionElement(question) {\n    return ReactQuestionFactory.Instance.createQuestion(!question.isDefaultRendering || question.isDefaultRendering() ? question.getTemplate() : question.getComponentName(), {\n      question: question,\n      isDisplayMode: question.isInputReadOnly,\n      creator: this\n    });\n  }\n  questionTitleLocation() {\n    return this.survey.questionTitleLocation;\n  }\n  questionErrorLocation() {\n    return this.survey.questionErrorLocation;\n  }\n  renderError(key, error, cssClasses) {\n    return null;\n  }\n  render() {\n    const question = this.survey.getAllQuestions()[0];\n    return React.createElement(\"div\", {\n      style: this.props.style\n    }, React.createElement(SurveyQuestion, {\n      creator: this,\n      element: question\n    }));\n  }\n}\nReactElementFactory.Instance.registerElement(\"svc-question-editor-content\", props => {\n  return React.createElement(QuestionEditorContentComponent, props);\n});\nclass ItemValueAdornerComponent extends CreatorModelElement {\n  constructor(props) {\n    super(props);\n    this.onBlur = event => {\n      this.model.onFocusOut(event.nativeEvent);\n    };\n    this.rootRef = React.createRef();\n  }\n  createModel(props) {\n    this.model = new ItemValueWrapperViewModel(props.componentData.creator, props.question, props.item);\n  }\n  getUpdatedModelProps() {\n    return [\"question\", \"item\"];\n  }\n  getStateElement() {\n    return this.model;\n  }\n  componentDidUpdate(prevProps, prevState) {\n    super.componentDidUpdate(prevProps, prevState);\n    this.props.item.setRootElement(this.rootRef.current);\n    if (prevProps.item !== this.props.item && prevProps.item) {\n      prevProps.item.setRootElement(undefined);\n    }\n  }\n  componentDidMount() {\n    super.componentDidMount();\n    this.props.item.setRootElement(this.rootRef.current);\n  }\n  componentWillUnmount() {\n    super.componentWillUnmount();\n    this.props.item.setRootElement(undefined);\n  }\n  render() {\n    this.model.item = this.props.item;\n    const button = this.model.allowAdd ? attachKey2click(React.createElement(\"span\", {\n      role: \"button\",\n      className: \"svc-item-value-controls__button svc-item-value-controls__add\",\n      \"aria-label\": this.model.tooltip,\n      onClick: () => {\n        this.model.add(this.model);\n        this.model.isNew = false;\n      }\n    }, React.createElement(SvgIcon, {\n      size: \"auto\",\n      iconName: \"icon-add_16x16\",\n      title: this.model.tooltip\n    }))) : React.createElement(React.Fragment, null, \" \", this.model.isDraggable ? React.createElement(\"span\", {\n      className: \"svc-item-value-controls__button svc-item-value-controls__drag\"\n    }, React.createElement(SvgIcon, {\n      className: \"svc-item-value-controls__drag-icon\",\n      size: \"auto\",\n      iconName: \"icon-drag-24x24\",\n      title: this.model.dragTooltip\n    })) : null, this.model.allowRemove ? attachKey2click(React.createElement(\"span\", {\n      role: \"button\",\n      className: \"svc-item-value-controls__button svc-item-value-controls__remove\",\n      \"aria-label\": this.model.tooltip,\n      onClick: () => this.model.remove(this.model)\n    }, React.createElement(SvgIcon, {\n      size: \"auto\",\n      iconName: \"icon-remove_16x16\",\n      title: this.model.tooltip\n    }))) : null);\n    const itemkey = this.props.element.key + (this.model.allowAdd ? \"_new\" : \"\");\n    return React.createElement(\"div\", {\n      ref: this.rootRef,\n      className: \"svc-item-value-wrapper\" + (this.model.allowAdd ? \" svc-item-value--new\" : \"\") + (this.model.isDragging ? \" svc-item-value--dragging\" : \"\") + (this.model.isDragDropGhost ? \" svc-item-value--ghost\" : \"\") + (this.model.isDragDropMoveDown ? \" svc-item-value--movedown\" : \"\") + (this.model.isDragDropMoveUp ? \" svc-item-value--moveup\" : \"\"),\n      key: itemkey,\n      \"data-sv-drop-target-item-value\": this.model.isDraggable ? this.model.item.value : undefined,\n      onPointerDown: event => this.model.onPointerDown(event)\n    }, React.createElement(\"div\", {\n      className: \"svc-item-value__ghost\"\n    }), React.createElement(\"div\", {\n      className: \"svc-item-value-controls\",\n      onBlur: this.onBlur\n    }, button), React.createElement(\"div\", {\n      className: \"svc-item-value__item\",\n      onClick: event => this.model.select(this.model, event.nativeEvent)\n    }, this.props.element));\n  }\n}\nReactElementFactory.Instance.registerElement(\"svc-item-value\", props => {\n  return React.createElement(ItemValueAdornerComponent, props);\n});\nclass ImageItemValueAdornerComponent extends CreatorModelElement {\n  constructor(props) {\n    super(props);\n    this.preventDragHandler = e => {\n      e.preventDefault();\n    };\n    this.rootRef = React.createRef();\n  }\n  createModel(props) {\n    this.model = new ImageItemValueWrapperViewModel(props.componentData.creator, props.question, props.item, null, null);\n  }\n  getUpdatedModelProps() {\n    return [\"question\", \"item\"];\n  }\n  getStateElement() {\n    return this.model;\n  }\n  get question() {\n    return this.props.question;\n  }\n  componentDidMount() {\n    super.componentDidMount();\n    this.model.itemsRoot = this.rootRef.current;\n  }\n  componentDidUpdate(prevProps, prevState) {\n    super.componentDidUpdate(prevProps, prevState);\n    this.model.itemsRoot = this.rootRef.current;\n  }\n  renderLoadingIndicator() {\n    return React.createElement(\"div\", {\n      className: \"svc-image-item-value__loading\"\n    }, React.createElement(LoadingIndicatorComponent, null));\n  }\n  renderNewItemControls() {\n    const addButton = attachKey2click(React.createElement(\"span\", {\n      className: this.model.addButtonCss,\n      onClick: () => this.model.chooseNewFile(this.model)\n    }, this.model.showChooseButtonAsIcon ? React.createElement(SvgIcon, {\n      size: \"auto\",\n      iconName: \"icon-add-lg\",\n      title: this.model.addFileTitle\n    }) : React.createElement(\"span\", null, this.model.chooseImageText)));\n    const placeholder = this.model.showPlaceholder ? React.createElement(\"span\", {\n      className: \"svc-image-item-value__placeholder\"\n    }, this.model.placeholderText) : null;\n    return React.createElement(React.Fragment, null, placeholder, addButton);\n  }\n  render() {\n    this.model.item = this.props.item;\n    const isNew = !this.props.question.isItemInList(this.props.item);\n    this.model.isNew = isNew;\n    const imageStyle = !this.model.getIsNewItemSingle() ? {\n      width: this.question.renderedImageWidth,\n      height: this.question.renderedImageHeight\n    } : null;\n    let content = null;\n    if (isNew || this.model.isUploading) {\n      content = React.createElement(React.Fragment, null, React.createElement(\"div\", {\n        className: \"svc-image-item-value__item\"\n      }, React.createElement(\"div\", {\n        className: \"sd-imagepicker__item sd-imagepicker__item--inline\"\n      }, React.createElement(\"label\", {\n        className: \"sd-imagepicker__label\"\n      }, React.createElement(\"div\", {\n        style: imageStyle,\n        className: \"sd-imagepicker__image\"\n      }, this.model.isUploading ? this.renderLoadingIndicator() : null)))), this.model.allowAdd && !this.model.isUploading ? React.createElement(\"div\", {\n        className: \"svc-image-item-value-controls\"\n      }, this.renderNewItemControls()) : null);\n    } else {\n      content = React.createElement(React.Fragment, null, React.createElement(\"div\", {\n        className: \"svc-image-item-value__item\"\n      }, this.props.element), this.model.isDraggable && this.model.canRenderControls ? React.createElement(\"span\", {\n        className: \"svc-context-button svc-image-item-value-controls__drag-area-indicator\",\n        onPointerDown: event => this.model.onPointerDown(event)\n      }, React.createElement(SvgIcon, {\n        size: \"auto\",\n        iconName: \"icon-drag-24x24\"\n      })) : null, this.model.canRenderControls ? React.createElement(\"div\", {\n        className: \"svc-context-container svc-image-item-value-controls\"\n      }, this.model.allowRemove && !this.model.isUploading ? attachKey2click(React.createElement(\"span\", {\n        className: \"svc-context-button\",\n        onClick: () => this.model.chooseFile(this.model)\n      }, React.createElement(SvgIcon, {\n        role: \"button\",\n        size: \"auto\",\n        iconName: \"icon-choosefile\",\n        title: this.model.selectFileTitle\n      }))) : null, this.model.allowRemove && !this.model.isUploading ? attachKey2click(React.createElement(\"span\", {\n        className: \"svc-context-button svc-context-button--danger\",\n        onClick: () => this.model.remove(this.model)\n      }, React.createElement(SvgIcon, {\n        role: \"button\",\n        size: \"auto\",\n        iconName: \"icon-delete\",\n        title: this.model.removeFileTitle\n      }))) : null) : null);\n    }\n    return React.createElement(\"div\", {\n      ref: this.rootRef,\n      className: this.model.getRootCss(),\n      key: this.props.element.key,\n      \"data-sv-drop-target-item-value\": this.model.isDraggable ? this.model.item.value : undefined,\n      onPointerDown: event => this.model.onPointerDown(event),\n      onDragStart: this.preventDragHandler,\n      onDrop: this.model.onDrop,\n      onDragEnter: this.model.onDragEnter,\n      onDragOver: this.model.onDragOver,\n      onDragLeave: this.model.onDragLeave\n    }, React.createElement(\"div\", {\n      className: \"svc-image-item-value-wrapper__ghost\",\n      style: imageStyle\n    }), React.createElement(\"div\", {\n      className: \"svc-image-item-value-wrapper__content\"\n    }, React.createElement(\"input\", {\n      type: \"file\",\n      \"aria-hidden\": \"true\",\n      tabIndex: -1,\n      accept: this.model.acceptedTypes,\n      className: \"svc-choose-file-input\",\n      style: {\n        position: \"absolute\",\n        opacity: 0,\n        width: \"1px\",\n        height: \"1px\",\n        overflow: \"hidden\"\n      }\n    }), content));\n  }\n}\nReactElementFactory.Instance.registerElement(\"svc-image-item-value\", props => {\n  return React.createElement(ImageItemValueAdornerComponent, props);\n});\nclass MatrixCellAdornerComponent extends CreatorModelElement {\n  createModel(props) {\n    var _a;\n    const data = props.componentData;\n    let prevIsSelected = false;\n    if (!!this.model) {\n      prevIsSelected = this.model.isSelected;\n    }\n    this.model = new MatrixCellWrapperViewModel(data.creator, data.element, data.question, data.row, data.column || ((_a = data.element.cell) === null || _a === void 0 ? void 0 : _a.column));\n    this.model.isSelected = prevIsSelected;\n  }\n  getUpdatedModelProps() {\n    return [\"componentData\"];\n  }\n  componentDidUpdate(prevProps, prevState) {\n    var _a, _b;\n    super.componentDidUpdate(prevProps, prevState);\n    const data = this.props.componentData;\n    this.model.templateData = data.element;\n    this.model.row = data.row;\n    this.model.column = data.column || ((_b = (_a = data.element) === null || _a === void 0 ? void 0 : _a.cell) === null || _b === void 0 ? void 0 : _b.column);\n    this.model.question = data.question;\n  }\n  getStateElement() {\n    return this.model;\n  }\n  render() {\n    let controls = null;\n    if (!!this.model.isSupportCellEditor) {\n      controls = React.createElement(\"div\", {\n        className: \"svc-matrix-cell__question-controls\"\n      }, attachKey2click(React.createElement(\"span\", {\n        className: \"svc-matrix-cell__question-controls-button svc-context-button\",\n        onClick: event => this.model.editQuestion(this.model, event)\n      }, React.createElement(SvgIcon, {\n        size: \"auto\",\n        iconName: \"icon-edit\"\n      }))));\n    }\n    return React.createElement(\"div\", {\n      className: \"svc-matrix-cell\",\n      tabIndex: -1,\n      key: this.props.element.key,\n      onClick: e => !this.props.question && this.model.selectContext(this.model, e),\n      onMouseOut: e => this.model.hover(e.nativeEvent, e.currentTarget),\n      onMouseOver: e => this.model.hover(e.nativeEvent, e.currentTarget)\n    }, React.createElement(\"div\", {\n      className: \"svc-matrix-cell--selected\" + (this.model.isSelected ? \" svc-visible\" : \"\")\n    }), this.props.element, controls);\n  }\n}\nReactElementFactory.Instance.registerElement(\"svc-matrix-cell\", props => {\n  return React.createElement(MatrixCellAdornerComponent, props);\n});\nclass SurveyResults extends CreatorModelElement {\n  createModel(props) {\n    if (this.props.survey) {\n      this.model = new SurveyResultsModel(props.survey);\n    }\n  }\n  getUpdatedModelProps() {\n    return [\"survey\"];\n  }\n  getStateElement() {\n    return this.model;\n  }\n  render() {\n    if (!this.model) {\n      return null;\n    }\n    return React.createElement(\"div\", {\n      className: \"svd-test-results\"\n    }, React.createElement(\"div\", {\n      className: \"svd-test-results__content\"\n    }, React.createElement(\"div\", {\n      className: \"svd-test-results__header\"\n    }, React.createElement(\"div\", {\n      className: \"svd-test-results__header-text\"\n    }, this.model.surveyResultsText), React.createElement(\"div\", {\n      className: \"svd-test-results__header-types\"\n    }, React.createElement(ActionButton, {\n      click: () => this.model.selectTableClick(),\n      text: this.model.surveyResultsTableText,\n      selected: this.model.isTableSelected,\n      disabled: false\n    }), React.createElement(ActionButton, {\n      click: () => this.model.selectJsonClick(),\n      text: this.model.surveyResultsJsonText,\n      selected: this.model.isJsonSelected,\n      disabled: false\n    }))), this.renderResultAsText(), this.renderResultAsTable()));\n  }\n  renderResultAsText() {\n    if (this.model.resultViewType !== \"text\") {\n      return null;\n    }\n    return React.createElement(\"div\", {\n      className: \"svd-test-results__text svd-light-bg-color\"\n    }, React.createElement(\"div\", null, this.model.resultText));\n  }\n  renderResultAsTable() {\n    if (this.model.resultViewType !== \"table\") {\n      return null;\n    }\n    return React.createElement(\"div\", {\n      className: \"svd-test-results__table svd-light-bg-color\"\n    }, React.createElement(\"table\", null, React.createElement(\"thead\", null, React.createElement(\"tr\", {\n      className: \"svd-light-background-color\"\n    }, React.createElement(\"th\", {\n      key: 1,\n      className: \"svd-dark-border-color\"\n    }, this.model.resultsTitle), React.createElement(\"th\", {\n      key: 2,\n      className: \"svd-dark-border-color\"\n    }, this.model.resultsDisplayValue))), React.createElement(\"tbody\", null, SurveyResults.renderRows(this.model.resultData))));\n  }\n  static renderRows(data) {\n    const rows = [];\n    for (var i = 0; i < data.length; i++) {\n      rows.push(React.createElement(SurveyResultsByRow, {\n        key: i + 1,\n        row: data[i]\n      }));\n    }\n    return rows;\n  }\n}\nclass SurveyResultsByRow extends CreatorModelElement {\n  get row() {\n    return this.props.row;\n  }\n  getStateElement() {\n    return this.row;\n  }\n  render() {\n    return React.createElement(React.Fragment, null, attachKey2click(React.createElement(\"tr\", {\n      onClick: () => this.row.toggle()\n    }, React.createElement(\"td\", {\n      key: 1,\n      style: {\n        paddingLeft: this.row.textMargin\n      },\n      className: \"svd-dark-border-color\"\n    }, this.row.isNode ? React.createElement(\"span\", {\n      style: {\n        left: this.row.markerMargin\n      },\n      className: \"svd-test-results__marker \" + (this.row.collapsed ? \"\" : \"svd-test-results__marker--expanded\")\n    }, React.createElement(SvgIcon, {\n      iconName: \"icon-expand_16x16\",\n      size: 16\n    })) : null, this.row.question ? React.createElement(SurveyLocStringViewer, {\n      locStr: this.row.question.locTitle\n    }) : React.createElement(\"span\", null, this.row.title)), React.createElement(\"td\", {\n      key: 2,\n      className: this.row.isNode ? \"svd-test-results__node-value\" : \"svd-dark-border-color\"\n    }, this.row.getString(this.row.displayValue)))), this.row.isNode && !this.row.collapsed ? SurveyResults.renderRows(this.row.data) : null);\n  }\n}\nclass SurveyCreatorToolboxTool extends CreatorModelElement {\n  constructor(props) {\n    super(props);\n    this.rootRef = React.createRef();\n  }\n  createModel(props) {\n    this.model = new ToolboxToolViewModel(props.item, props.creator, props.parentModel);\n  }\n  getUpdatedModelProps() {\n    return [\"creator\", \"item\"];\n  }\n  get item() {\n    return this.props.item;\n  }\n  get creator() {\n    return this.props.creator;\n  }\n  get isCompact() {\n    return this.props.isCompact;\n  }\n  getStateElement() {\n    return this.item;\n  }\n  render() {\n    const item = this.item;\n    const itemComponent = ReactElementFactory.Instance.createElement(this.model.itemComponent, {\n      item: item,\n      creator: this.creator,\n      parentModel: this.creator.toolbox,\n      model: this.model,\n      isCompact: this.isCompact\n    });\n    return React.createElement(\"div\", {\n      className: item.css,\n      key: item.id,\n      ref: this.rootRef\n    }, item.needSeparator && !this.creator.toolbox.showCategoryTitles ? React.createElement(\"div\", {\n      className: \"svc-toolbox__category-separator\"\n    }) : null, React.createElement(\"div\", {\n      className: \"svc-toolbox__tool-content sv-action__content\",\n      onPointerDown: event => {\n        event.persist();\n        this.model.onPointerDown(event);\n      }\n    }, itemComponent));\n  }\n  componentWillUnmount() {\n    super.componentWillUnmount();\n    this.item.updateModeCallback = undefined;\n  }\n  componentDidMount() {\n    super.componentDidMount();\n    this.item.updateModeCallback = (mode, callback) => {\n      queueMicrotask(() => {\n        if (ReactDOM[\"flushSync\"]) {\n          ReactDOM[\"flushSync\"](() => {\n            this.item.mode = mode;\n          });\n        } else {\n          this.item.mode = mode;\n        }\n        queueMicrotask(() => {\n          callback(mode, this.rootRef.current);\n        });\n      });\n    };\n    this.item.afterRender();\n  }\n}\nclass SurveyCreatorToolboxItem extends CreatorModelElement {\n  constructor(props) {\n    super(props);\n  }\n  getUpdatedModelProps() {\n    return [\"creator\", \"item\"];\n  }\n  get item() {\n    return this.props.item;\n  }\n  get creator() {\n    return this.props.creator;\n  }\n  get model() {\n    return this.props.model;\n  }\n  getStateElement() {\n    return this.model;\n  }\n  render() {\n    const banner = this.props.isCompact ? React.createElement(\"span\", {\n      className: \"svc-toolbox__item-banner\",\n      onClick: event => {\n        event.persist();\n        this.model.click(event);\n      }\n    }, React.createElement(SvgIcon, {\n      size: \"auto\",\n      iconName: this.item.iconName,\n      className: \"svc-toolbox__item-icon\",\n      title: this.item.tooltip\n    }), React.createElement(\"span\", null, this.item.title)) : null;\n    const item = attachKey2click(React.createElement(\"div\", {\n      className: this.item.renderedCss,\n      tabIndex: 0,\n      role: \"button\",\n      \"aria-label\": this.item.tooltip,\n      onClick: event => {\n        event.persist();\n        this.model.click(event);\n      }\n    }, React.createElement(\"span\", {\n      className: \"svc-toolbox__item-container\"\n    }, !!this.item.iconName ? React.createElement(SvgIcon, {\n      size: \"auto\",\n      iconName: this.item.iconName,\n      className: \"svc-toolbox__item-icon\"\n    }) : null), this.props.isCompact ? null : React.createElement(\"span\", {\n      className: \"svc-toolbox__item-title\"\n    }, this.item.title)));\n    return React.createElement(React.Fragment, null, item, banner);\n  }\n}\nReactElementFactory.Instance.registerElement(\"svc-toolbox-item\", props => {\n  return createElement(SurveyCreatorToolboxItem, props);\n});\nclass SurveyCreatorToolboxItemGroup extends CreatorModelElement {\n  constructor(props) {\n    super(props);\n  }\n  getUpdatedModelProps() {\n    return [\"creator\", \"item\"];\n  }\n  get item() {\n    return this.props.item;\n  }\n  get model() {\n    return this.props.model;\n  }\n  get creator() {\n    return this.props.creator;\n  }\n  get isCompact() {\n    return this.props.isCompact;\n  }\n  get parentModel() {\n    return this.props.parentModel;\n  }\n  getStateElement() {\n    return this.item;\n  }\n  render() {\n    return React.createElement(React.Fragment, null, React.createElement(SurveyCreatorToolboxItem, {\n      item: this.item,\n      creator: this.creator,\n      model: this.model,\n      parentModel: this.parentModel,\n      isCompact: this.isCompact\n    }), React.createElement(\"div\", {\n      className: \"svc-toolbox__item-submenu-button\",\n      onMouseOver: event => this.model.onMouseOver(this.item, event),\n      onMouseLeave: event => this.model.onMouseLeave(this.item, event)\n    }, React.createElement(SvgIcon, {\n      size: \"auto\",\n      iconName: this.item.subitemsButtonIcon\n    }), React.createElement(Popup, {\n      model: this.item.popupModel\n    })));\n  }\n}\nReactElementFactory.Instance.registerElement(\"svc-toolbox-item-group\", props => {\n  return React.createElement(SurveyCreatorToolboxItemGroup, props);\n});\nclass SurveyCreatorToolboxCategory extends SurveyElementBase {\n  get category() {\n    return this.props.category;\n  }\n  get toolbox() {\n    return this.props.toolbox;\n  }\n  get class() {\n    return \"svc-toolbox__category\" + (this.category.collapsed ? \" svc-toolbox__category--collapsed\" : \"\") + (this.category.empty ? \" svc-toolbox__category--empty\" : \"\");\n  }\n  getStateElement() {\n    return this.category;\n  }\n  render() {\n    const header = this.renderCategoryHeader();\n    const items = this.renderCategoryContent();\n    return React.createElement(\"div\", {\n      className: this.class,\n      key: this.category.name\n    }, React.createElement(\"div\", {\n      className: \"svc-toolbox__category-header-wrapper\"\n    }, header), items);\n  }\n  renderCategoryHeader() {\n    let className = \"svc-toolbox__category-header\";\n    if (this.toolbox.canCollapseCategories) {\n      className += \" svc-toolbox__category-header--collapsed\";\n    }\n    return attachKey2click(React.createElement(\"div\", {\n      className: className,\n      onClick: e => this.category.toggleState()\n    }, React.createElement(\"span\", {\n      className: \"svc-toolbox__category-title\"\n    }, this.category.title), this.renderButton()));\n  }\n  renderButton() {\n    if (!this.toolbox.canCollapseCategories) return null;\n    const iconName = this.category.iconName;\n    return React.createElement(\"div\", {\n      className: \"svc-toolbox__category-header__controls\"\n    }, React.createElement(SvgIcon, {\n      className: this.category.iconClassName,\n      iconName: iconName,\n      size: \"auto\"\n    }));\n  }\n  renderCategoryContent() {\n    return this.renderItems(this.category.items);\n  }\n  renderItems(items, isCompact = false) {\n    return items.map((item, itemIndex) => React.createElement(SurveyCreatorToolboxTool, {\n      item: item,\n      creator: this.toolbox.creator,\n      parentModel: this.toolbox,\n      isCompact: isCompact,\n      key: \"item\" + itemIndex\n    }));\n  }\n}\nReactElementFactory.Instance.registerElement(\"svc-toolbox-category\", props => {\n  return React.createElement(SurveyCreatorToolboxCategory, props);\n});\nclass ToolboxList extends SurveyElementBase {\n  constructor(props) {\n    super(props);\n  }\n  get model() {\n    return this.props.model;\n  }\n  get creator() {\n    return this.props.creator;\n  }\n  getStateElement() {\n    return this.model;\n  }\n  render() {\n    if (!this.model || !this.model.renderElements) return null;\n    const items = this.renderItems();\n    return React.createElement(\"div\", {\n      className: this.model.cssClasses.root\n    }, items);\n  }\n  renderItems() {\n    const items = this.model.renderedActions;\n    return items.map((item, itemIndex) => React.createElement(SurveyCreatorToolboxTool, {\n      item: item,\n      creator: this.creator,\n      parentModel: this.model,\n      isCompact: false,\n      key: \"item\" + itemIndex\n    }));\n  }\n}\nReactElementFactory.Instance.registerElement(\"svc-toolbox-list\", props => {\n  return React.createElement(ToolboxList, props);\n});\nclass SearchComponent extends SurveyElementBase {\n  get model() {\n    return this.props.model;\n  }\n  getStateElement() {\n    return this.model;\n  }\n  constructor(props) {\n    super(props);\n    this.state = {\n      filterString: this.model.filterString || \"\"\n    };\n  }\n  renderElement() {\n    if (!this.model.isVisible) return null;\n    const onChange = e => {\n      const {\n        root\n      } = settings.environment;\n      if (e.target === root.activeElement) {\n        this.model.filterString = e.target.value;\n      }\n    };\n    return React.createElement(\"div\", {\n      className: \"svc-search\"\n    }, React.createElement(\"div\", {\n      className: \"svc-search__search-icon\"\n    }, React.createElement(SvgIcon, {\n      iconName: \"icon-search\",\n      size: \"auto\"\n    })), React.createElement(\"input\", {\n      type: \"text\",\n      className: \"svc-search__input\",\n      \"aria-label\": this.model.filterStringPlaceholder,\n      placeholder: this.model.filterStringPlaceholder,\n      value: this.state.filterString,\n      onChange: onChange\n    }), React.createElement(\"div\", {\n      className: \"svc-search__toolbar\"\n    }, React.createElement(\"div\", {\n      className: \"svc-search__toolbar-counter\"\n    }, this.model.matchCounterText), React.createElement(SurveyActionBar, {\n      model: this.model.searchActionBar\n    })));\n  }\n}\nReactElementFactory.Instance.registerElement(\"svc-search\", props => {\n  return React.createElement(SearchComponent, props);\n});\nclass AdaptiveToolbox extends SurveyElementBase {\n  constructor(props) {\n    super(props);\n    this.rootRef = React.createRef();\n  }\n  componentDidUpdate(prevProps, prevState) {\n    super.componentDidUpdate(prevProps, prevState);\n    const container = this.rootRef.current;\n    if (container) {\n      this.toolbox.afterRender(container);\n    }\n  }\n  componentDidMount() {\n    super.componentDidMount();\n    const container = this.rootRef.current;\n    if (container) {\n      this.toolbox.afterRender(container);\n    }\n  }\n  componentWillUnmount() {\n    this.toolbox.beforeDestroy();\n    super.componentWillUnmount();\n  }\n  get creator() {\n    return this.props.model;\n  }\n  get toolbox() {\n    return this.creator.toolbox;\n  }\n  getStateElement() {\n    return this.toolbox;\n  }\n  renderItems(items, isCompact = false) {\n    return items.map((item, itemIndex) => {\n      return React.createElement(SurveyCreatorToolboxTool, {\n        item: item,\n        creator: this.creator,\n        parentModel: this.toolbox,\n        isCompact: isCompact,\n        key: item.renderedId\n      });\n    });\n  }\n  renderCategories() {\n    return this.toolbox.categories.map((category, index) => {\n      return React.createElement(SurveyCreatorToolboxCategory, {\n        category: category,\n        toolbox: this.toolbox,\n        key: \"category\" + index\n      });\n    });\n  }\n  renderSearch() {\n    const searchButton = this.toolbox.isCompactRendered ? React.createElement(React.Fragment, null, React.createElement(SurveyCreatorToolboxTool, {\n      item: this.toolbox.searchItem,\n      creator: this.creator,\n      parentModel: this.toolbox,\n      isCompact: this.toolbox.isCompactRendered,\n      key: \"searchitem\"\n    })) : null;\n    return React.createElement(\"div\", {\n      className: \"svc-toolbox__search-container\"\n    }, searchButton, React.createElement(SearchComponent, {\n      model: this.toolbox.searchManager\n    }), React.createElement(\"div\", {\n      className: \"svc-toolbox__category-separator svc-toolbox__category-separator--search\"\n    }));\n  }\n  render() {\n    const search = this.toolbox.showSearch ? this.renderSearch() : null;\n    const placeholder = this.toolbox.showPlaceholder ? React.createElement(\"div\", {\n      className: \"svc-toolbox__placeholder\"\n    }, this.toolbox.toolboxNoResultsFound) : null;\n    return React.createElement(\"div\", {\n      ref: this.rootRef,\n      className: this.toolbox.classNames,\n      style: this.toolbox.getRootStyle()\n    }, React.createElement(\"div\", {\n      onBlur: e => this.toolbox.focusOut(e),\n      className: \"svc-toolbox__panel\"\n    }, search, placeholder, React.createElement(Scroll, null, this.toolbox.showInSingleCategory ? React.createElement(\"div\", {\n      className: \"svc-toolbox__category\"\n    }, this.renderItems(this.toolbox.renderedActions, this.toolbox.isCompactRendered)) : this.renderCategories())));\n  }\n}\nReactElementFactory.Instance.registerElement(\"svc-toolbox\", props => {\n  return React.createElement(AdaptiveToolbox, props);\n});\nclass SurveyNavigation extends SurveyElementBase {\n  constructor() {\n    super(...arguments);\n    this.onPropChangedHandler = (sender, options) => {\n      if (this.isRendering) return;\n      const reactiveProps = [\"showProgressBar\", \"progressBarType\", \"currentPageValue\"];\n      if (reactiveProps.indexOf(options.name) < 0) return;\n      var val = {};\n      for (var i = 0; i < reactiveProps.length; i++) {\n        var propName = reactiveProps[i];\n        val[propName] = this.survey[propName];\n      }\n      this.setState(val);\n    };\n  }\n  componentDidMount() {\n    super.componentDidMount();\n    this.setHandler();\n  }\n  componentDidUpdate(prevProps, prevState) {\n    super.componentDidUpdate(prevProps, prevState);\n    this.setHandler();\n  }\n  setHandler() {\n    if (!this.survey || this.survey.onPropertyChanged.hasFunc(this.onPropChangedHandler)) return;\n    this.survey.onPropertyChanged.add(this.onPropChangedHandler);\n  }\n  componentWillUnmount() {\n    super.componentWillUnmount();\n    if (this.survey) {\n      this.survey.onPropertyChanged.remove(this.onPropChangedHandler);\n    }\n  }\n  get survey() {\n    return this.props.survey;\n  }\n  get location() {\n    return this.props.location;\n  }\n  get isTop() {\n    return this.location == \"top\";\n  }\n  canRender() {\n    return this.isTop ? this.survey.isShowProgressBarOnTop : this.survey.isShowProgressBarOnBottom;\n  }\n  renderElement() {\n    return ReactElementFactory.Instance.createElement(this.survey.getProgressTypeComponent(), {\n      survey: this.survey,\n      css: this.survey.css,\n      isTop: this.isTop\n    });\n  }\n}\nclass TabButtonComponent extends SurveyElementBase {\n  constructor(props) {\n    super(props);\n  }\n  getStateElement() {\n    return this.props.model;\n  }\n  renderElement() {\n    const model = this.props.model;\n    if (!model.visible) return null;\n    const button = attachKey2click(React.createElement(\"div\", {\n      className: model.buttonClassName,\n      title: model.tooltip,\n      onClick: () => {\n        model.action();\n      }\n    }, React.createElement(\"div\", {\n      className: \"svc-menu-action__icon\"\n    }, React.createElement(\"div\", {\n      className: \"svc-menu-action__icon-container\"\n    }, React.createElement(SvgIcon, {\n      iconName: model.iconName,\n      size: \"auto\"\n    })))), model);\n    return React.createElement(\"div\", {\n      className: \"svc-menu-action\"\n    }, button);\n  }\n}\nclass TabControl extends SurveyElementBase {\n  constructor(props) {\n    super(props);\n  }\n  getStateElement() {\n    return this.props.model;\n  }\n  canRender() {\n    if (!this.props.model) return false;\n    return super.canRender();\n  }\n  renderElement() {\n    return React.createElement(\"div\", {\n      className: this.props.model.sideBarClassName\n    }, React.createElement(\"div\", {\n      className: \"svc-sidebar-tabs__top-container\"\n    }, React.createElement(\"div\", {\n      className: \"svc-sidebar-tabs__collapse-button\"\n    }, React.createElement(TabButtonComponent, {\n      model: this.props.model.expandCollapseAction\n    })), React.createElement(\"div\", {\n      className: \"svc-sidebar-tabs__separator\"\n    }, React.createElement(\"div\", null)), React.createElement(Scroll, null, React.createElement(\"div\", {\n      className: \"svc-sidebar-tabs__items\"\n    }, React.createElement(TabsComponent, {\n      model: this.props.model.topToolbar\n    })))), React.createElement(\"div\", {\n      className: \"svc-sidebar-tabs__bottom-container\"\n    }, React.createElement(\"div\", {\n      className: \"svc-sidebar-tabs__items\"\n    }, React.createElement(TabsComponent, {\n      model: this.props.model.bottomToolbar\n    }))));\n  }\n}\nReactElementFactory.Instance.registerElement(\"svc-tab-control\", props => {\n  return React.createElement(TabControl, props);\n});\nclass TabsComponent extends SurveyElementBase {\n  constructor(props) {\n    super(props);\n  }\n  getStateElement() {\n    return this.props.model;\n  }\n  renderElement() {\n    return React.createElement(React.Fragment, null, this.props.model.actions.map((item, itemIndex) => React.createElement(TabButtonComponent, {\n      model: item,\n      key: \"item\" + itemIndex\n    })));\n  }\n}\nclass SideBarDefaultHeader extends SurveyElementBase {\n  get model() {\n    return this.props.model;\n  }\n  getStateElement() {\n    return this.model;\n  }\n  renderElement() {\n    const title = !!this.model.title ? React.createElement(\"div\", {\n      className: \"svc-side-bar__container-title\"\n    }, this.model.title) : null;\n    return React.createElement(\"div\", {\n      className: \"svc-side-bar__container-header\"\n    }, React.createElement(\"div\", {\n      className: \"svc-side-bar__container-actions\"\n    }, React.createElement(SurveyActionBar, {\n      model: this.model.toolbar\n    })), title);\n  }\n}\nReactElementFactory.Instance.registerElement(\"svc-side-bar-default-header\", props => {\n  return React.createElement(SideBarDefaultHeader, props);\n});\nclass SideBarPropertyGridHeader extends SurveyElementBase {\n  get objectSelectionAction() {\n    return this.props.model;\n  }\n  getStateElement() {\n    return this.objectSelectionAction;\n  }\n  renderElement() {\n    const button = attachKey2click(React.createElement(\"div\", {\n      className: this.objectSelectionAction.buttonClassName,\n      onClick: () => this.objectSelectionAction.action()\n    }, React.createElement(\"div\", {\n      className: \"svc-sidebar__header-caption\"\n    }, React.createElement(\"span\", {\n      className: \"svc-sidebar__header-title\"\n    }, this.objectSelectionAction.title), React.createElement(\"span\", {\n      className: \"svc-sidebar__header-subtitle\"\n    }, this.objectSelectionAction.tooltip))), this.props.model);\n    return React.createElement(\"div\", {\n      className: \"svc-sidebar__header svc-sidebar__header--tabbed\"\n    }, React.createElement(\"div\", {\n      className: \"svc-sidebar__header-container svc-sidebar__header-container--with-subtitle\"\n    }, React.createElement(\"div\", {\n      className: \"svc-sidebar__header-content\"\n    }, button, React.createElement(Popup, {\n      model: this.objectSelectionAction.popupModel\n    }))));\n  }\n}\nReactElementFactory.Instance.registerElement(\"svc-side-bar-property-grid-header\", props => {\n  return React.createElement(SideBarPropertyGridHeader, props);\n});\nclass SideBarHeader extends SurveyElementBase {\n  get model() {\n    return this.props.model;\n  }\n  getStateElement() {\n    return this.model;\n  }\n  renderElement() {\n    return React.createElement(\"div\", {\n      className: \"svc-side-bar__container-header svc-sidebar__header-container\"\n    }, this.model.subTitle ? React.createElement(\"div\", {\n      className: \"svc-sidebar__header-caption\"\n    }, React.createElement(\"span\", {\n      className: \"svc-sidebar__header-title\"\n    }, this.model.title), React.createElement(\"span\", {\n      className: \"svc-sidebar__header-subtitle\"\n    }, this.model.subTitle)) : React.createElement(\"div\", {\n      className: \"svc-side-bar__container-title\"\n    }, this.model.title));\n  }\n}\nReactElementFactory.Instance.registerElement(\"svc-side-bar-header\", props => {\n  return React.createElement(SideBarHeader, props);\n});\nclass SidebarComponent extends SurveyElementBase {\n  get model() {\n    return this.props.model;\n  }\n  constructor(props) {\n    super(props);\n    this.containerRef = React.createRef();\n  }\n  getStateElement() {\n    return this.model;\n  }\n  componentDidMount() {\n    super.componentDidMount();\n    this.model.initResizeManager(this.containerRef.current);\n  }\n  componentWillUnmount() {\n    super.componentWillUnmount();\n    this.model.resetResizeManager();\n  }\n  canRender() {\n    if (!this.model) return false;\n    return super.canRender();\n  }\n  renderElement() {\n    const style = {\n      display: this.model.renderRoot ? \"\" : \"none\"\n    };\n    const containerStyle = {\n      display: this.model.renderContainer ? \"\" : \"none\"\n    };\n    const items = this.model.pages.map(page => React.createElement(SidebarPage, {\n      page: page,\n      key: page.id\n    }));\n    const headerArea = ReactElementFactory.Instance.createElement(this.model.header.component, {\n      model: this.model.header.componentModel\n    });\n    let sideArea = null;\n    if (this.model.sideAreaComponentName) {\n      sideArea = ReactElementFactory.Instance.createElement(this.model.sideAreaComponentName, {\n        model: this.model.sideAreaComponentData\n      });\n    }\n    return React.createElement(\"div\", {\n      className: this.model.rootCss,\n      style: style\n    }, React.createElement(\"div\", {\n      className: \"svc-side-bar__shadow\",\n      onClick: () => this.model.collapseSidebar(),\n      style: containerStyle\n    }), React.createElement(\"div\", {\n      className: \"svc-flex-row svc-side-bar__wrapper\"\n    }, React.createElement(\"div\", {\n      className: \"svc-side-bar__container-wrapper\",\n      style: containerStyle\n    }, React.createElement(\"div\", {\n      ref: this.containerRef,\n      className: \"svc-side-bar__container\"\n    }, headerArea, React.createElement(\"div\", {\n      className: \"svc-side-bar__container-content\"\n    }, items))), sideArea));\n  }\n}\nclass SidebarPage extends SurveyElementBase {\n  get page() {\n    return this.props.page;\n  }\n  getStateElement() {\n    return this.page;\n  }\n  renderElement() {\n    if (!this.page.visible) return null;\n    const component = ReactElementFactory.Instance.createElement(this.page.componentName, {\n      model: this.page.componentData\n    });\n    return component;\n  }\n}\nReactQuestionFactory.Instance.registerQuestion(\"svc-side-bar-page\", props => {\n  return React.createElement(SidebarPage, props);\n});\nReactElementFactory.Instance.registerElement(\"svc-side-bar\", props => {\n  return React.createElement(SidebarComponent, props);\n});\nclass TranslationLineSkeleton extends React.Component {\n  render() {\n    return React.createElement(\"div\", {\n      className: \"sd-translation-line-skeleton\"\n    });\n  }\n}\nReactElementFactory.Instance.registerElement(\"sd-translation-line-skeleton\", props => {\n  return React.createElement(TranslationLineSkeleton, props);\n});\nclass TranslateFromAction extends SurveyElementBase {\n  get item() {\n    return this.props.item;\n  }\n  getStateElement() {\n    return this.item;\n  }\n  renderElement() {\n    const item = this.item;\n    return React.createElement(\"div\", {\n      className: item.data.containerCss\n    }, React.createElement(\"span\", {\n      className: item.data.additionalTitleCss\n    }, item.data.additionalTitle), ReactElementFactory.Instance.createElement(\"sv-action-bar-item-dropdown\", {\n      item: this.item\n    }));\n  }\n}\nReactElementFactory.Instance.registerElement(\"svc-translate-from-action\", props => {\n  return React.createElement(TranslateFromAction, props);\n});\nclass SurveyLocStringEditor extends CreatorModelElement {\n  constructor(props) {\n    var _a;\n    super(props);\n    this.onChangedHandler = (sender, options) => {\n      this.setState({\n        changed: !!this.state && this.state.changed ? this.state.changed + 1 : 1\n      });\n    };\n    this.onBlur = event => {\n      if (this.svStringEditorRef.current) {\n        this.svStringEditorRef.current.spellcheck = false;\n      }\n      this.locString[\"__isEditing\"] = false;\n      this.justFocused = false;\n      this.baseModel.onBlur(event.nativeEvent);\n      return this.baseModel.errorText;\n    };\n    this.onCompositionStart = event => {\n      this.baseModel.onCompositionStart(event.nativeEvent);\n    };\n    this.onCompositionEnd = event => {\n      this.baseModel.onCompositionEnd(event.nativeEvent);\n    };\n    this.onInput = event => {\n      this.baseModel.onInput(event.nativeEvent);\n    };\n    this.onPaste = event => {\n      this.baseModel.onPaste(event.nativeEvent);\n    };\n    this.justFocused = false;\n    this.onFocus = event => {\n      this.baseModel.onFocus(event.nativeEvent);\n      this.justFocused = true;\n    };\n    this.onKeyDown = event => {\n      return this.baseModel.onKeyDown(event.nativeEvent);\n    };\n    this.onKeyUp = event => {\n      return this.baseModel.onKeyUp(event.nativeEvent);\n    };\n    this.onMouseUp = event => {\n      return this.baseModel.onMouseUp(event.nativeEvent);\n    };\n    this.done = event => {\n      this.baseModel.done(event);\n      this.locString[\"__isEditing\"] = false;\n    };\n    this.edit = event => {\n      this.svStringEditorRef.current.focus();\n      // document.execCommand('selectAll', false, null);\n      this.locString[\"__isEditing\"] = true;\n      this.baseModel.onClick(event);\n    };\n    this.htmlValue = {\n      __html: (_a = this.locString) === null || _a === void 0 ? void 0 : _a.renderedHtml\n    };\n    this.state = {\n      changed: 0\n    };\n    this.svStringEditorRef = React.createRef();\n  }\n  createModel(props) {\n    if (this.baseModel) {\n      this.baseModel.dispose();\n    }\n    this.baseModel = new StringEditorViewModelBase(this.locString, this.creator);\n  }\n  getUpdatedModelProps() {\n    return [\"creator\", \"locString\"];\n  }\n  get locString() {\n    return this.props.locStr.locStr;\n  }\n  get creator() {\n    return this.props.locStr.creator;\n  }\n  get style() {\n    return this.props.style;\n  }\n  getStateElement() {\n    return this.baseModel;\n  }\n  get errorText() {\n    return this.baseModel.errorText;\n  }\n  componentDidMount() {\n    super.componentDidMount();\n    if (!this.locString) return;\n    this.baseModel.setLocString(this.locString);\n    this.baseModel.getEditorElement = () => this.svStringEditorRef.current;\n    this.baseModel.blurEditor = () => {\n      this.svStringEditorRef.current.blur();\n      this.svStringEditorRef.current.spellcheck = false;\n    };\n    this.baseModel.afterRender();\n    this.locString.onStringChanged.add(this.onChangedHandler);\n    if (this.locString[\"__isEditing\"]) {\n      this.svStringEditorRef.current.focus();\n      // document.execCommand('selectAll', false, null);\n    }\n  }\n  componentDidUpdate(prevProps, prevState) {\n    super.componentDidUpdate(prevProps, prevState);\n    this.baseModel.setLocString(this.locString);\n    this.baseModel.afterRender();\n    this.locString.onStringChanged.add(this.onChangedHandler);\n  }\n  componentWillUnmount() {\n    super.componentWillUnmount();\n    this.baseModel.detachFromUI();\n    if (!this.locString) return;\n    this.locString.onStringChanged.remove(this.onChangedHandler);\n  }\n  get placeholder() {\n    return this.baseModel.placeholder;\n  }\n  get contentEditable() {\n    return this.baseModel.contentEditable;\n  }\n  get className() {\n    return this.baseModel.className(this.locString.renderedHtml);\n  }\n  render() {\n    if (!this.locString) {\n      return null;\n    }\n    let control = null;\n    if (this.locString.hasHtml) {\n      if (this.htmlValue.__html !== this.locString.renderedHtml) {\n        this.htmlValue = {\n          __html: this.locString.renderedHtml\n        };\n      }\n      control = React.createElement(\"span\", {\n        role: \"textbox\",\n        ref: this.svStringEditorRef,\n        className: \"sv-string-editor sv-string-editor--html\",\n        contentEditable: this.contentEditable,\n        spellCheck: false,\n        \"aria-placeholder\": this.placeholder,\n        \"aria-label\": this.placeholder || \"content editable\",\n        suppressContentEditableWarning: true,\n        tabIndex: this.baseModel.tabIndex,\n        // style={this.style}\n        dangerouslySetInnerHTML: this.htmlValue,\n        onBlur: this.onBlur,\n        onFocus: this.onFocus,\n        onKeyDown: this.onKeyDown,\n        onMouseUp: this.onMouseUp,\n        onClick: this.edit\n      });\n    } else {\n      control = React.createElement(\"span\", {\n        role: \"textbox\",\n        ref: this.svStringEditorRef,\n        className: \"sv-string-editor\",\n        contentEditable: this.contentEditable,\n        tabIndex: this.baseModel.tabIndex,\n        spellCheck: false,\n        \"aria-placeholder\": this.placeholder,\n        \"aria-label\": this.placeholder || \"content editable\",\n        suppressContentEditableWarning: true,\n        // style={this.style}\n        key: this.locString.renderedHtml,\n        onBlur: this.onBlur,\n        onInput: this.onInput,\n        onPaste: this.onPaste,\n        onCompositionStart: this.onCompositionStart,\n        onCompositionEnd: this.onCompositionEnd,\n        onFocus: this.onFocus,\n        onKeyDown: this.onKeyDown,\n        onKeyUp: this.onKeyUp,\n        onMouseUp: this.onMouseUp,\n        onClick: this.edit\n      }, this.locString.renderedHtml);\n    }\n    const counter = this.baseModel.showCharacterCounter ? React.createElement(CharacterCounterComponent, {\n      counter: this.baseModel.characterCounter,\n      remainingCharacterCounter: this.baseModel.getCharacterCounterClass\n    }) : null;\n    return React.createElement(\"span\", {\n      className: this.className\n    }, React.createElement(\"span\", {\n      className: \"svc-string-editor__content\"\n    }, React.createElement(\"div\", {\n      className: \"svc-string-editor__border svc-string-editor__border--hover\",\n      onClick: this.edit\n    }), React.createElement(\"div\", {\n      className: \"svc-string-editor__border svc-string-editor__border--focus\",\n      onClick: this.edit\n    }), React.createElement(\"span\", {\n      className: \"svc-string-editor__input\"\n    }, control, React.createElement(\"div\", {\n      className: \"svc-string-editor__controls\",\n      onClick: this.edit\n    }), counter)), this.errorText ? React.createElement(\"span\", {\n      className: \"svc-string-editor__error\"\n    }, this.errorText) : \"\");\n  }\n}\nReactElementFactory.Instance.registerElement(editableStringRendererName, props => {\n  return React.createElement(SurveyLocStringEditor, props);\n});\nclass QuestionErrorComponent extends React.Component {\n  render() {\n    return React.createElement(\"div\", null, React.createElement(SvgIcon, {\n      \"aria-hidden\": \"true\",\n      iconName: \"icon-alert_24x24\",\n      size: \"24\",\n      className: this.props.cssClasses.error.icon\n    }), React.createElement(\"span\", {\n      className: this.props.cssClasses.error.item || undefined\n    }, React.createElement(SurveyLocStringViewer, {\n      locStr: this.props.error.locText\n    })));\n  }\n}\nReactElementFactory.Instance.registerElement(\"svc-question-error\", props => {\n  return React.createElement(QuestionErrorComponent, props);\n});\nclass SurveyLogicOpertor extends SurveyQuestionDropdown {\n  constructor(props) {\n    super(props);\n  }\n  renderInput() {\n    const q = this.question;\n    initLogicOperator(q);\n    const text = q.locReadOnlyText ? this.renderLocString(q.locReadOnlyText) : \"\";\n    const dropdownListModel = this.question.dropdownListModel;\n    return React.createElement(\"div\", {\n      id: this.question.inputId,\n      className: q.getControlClass(),\n      tabIndex: this.question.isInputReadOnly ? undefined : 0,\n      // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n      // @ts-ignore\n      disabled: this.question.isInputReadOnly,\n      required: this.question.isRequired,\n      onChange: this.updateValueOnEvent,\n      onInput: this.updateValueOnEvent,\n      onKeyUp: this.keyhandler,\n      role: dropdownListModel.ariaQuestionRole,\n      \"aria-required\": dropdownListModel.ariaQuestionRequired,\n      \"aria-invalid\": dropdownListModel.ariaQuestionInvalid,\n      \"aria-errormessage\": dropdownListModel.ariaQuestionErrorMessage,\n      \"aria-expanded\": dropdownListModel.ariaQuestionExpanded,\n      \"aria-label\": dropdownListModel.ariaQuestionLabel,\n      \"aria-labelledby\": dropdownListModel.ariaQuestionLabelledby,\n      \"aria-controls\": dropdownListModel.ariaQuestionControls\n    }, React.createElement(\"div\", {\n      className: this.question.cssClasses.controlValue\n    }, text));\n  }\n  renderEditorButtons() {\n    return null;\n  }\n}\nReactQuestionFactory.Instance.registerQuestion(\"sv-logic-operator\", props => {\n  return React.createElement(SurveyLogicOpertor, props);\n});\nRendererFactory.Instance.registerRenderer(\"dropdown\", \"logicoperator\", \"sv-logic-operator\");\nclass SurveyPageNavigator extends CreatorModelElement {\n  constructor(props) {\n    super(props);\n    this.containerRef = React.createRef();\n  }\n  createModel(props) {\n    if (this.model) {\n      this.model.dispose();\n    }\n    this.model = new PageNavigatorViewModel(props.pagesController, props.pageEditMode);\n  }\n  getUpdatedModelProps() {\n    return [\"pagesController\", \"pageEditMode\"];\n  }\n  getStateElement() {\n    return this.model;\n  }\n  componentDidMount() {\n    super.componentDidMount();\n    if (this.props.pageEditMode !== \"bypage\") {\n      const el = this.containerRef.current;\n      this.model.attachToUI(el);\n    }\n  }\n  componentWillUnmount() {\n    super.componentWillUnmount();\n    this.model.stopItemsContainerHeightObserver();\n    this.model.setScrollableContainer(undefined);\n  }\n  renderElement() {\n    let className = \"svc-page-navigator__selector svc-page-navigator__button\";\n    if (this.model.isPopupOpened) className += \" svc-page-navigator__button--pressed\";\n    return React.createElement(\"div\", {\n      className: \"svc-page-navigator\",\n      ref: this.containerRef,\n      style: {\n        display: this.model.visible ? \"flex\" : \"none\"\n      }\n    }, React.createElement(\"div\", null, attachKey2click(React.createElement(\"div\", {\n      role: \"button\",\n      className: className,\n      onClick: () => this.model.togglePageSelector(),\n      title: this.model.pageSelectorCaption\n    }, React.createElement(SvgIcon, {\n      className: \"svc-page-navigator__button-icon\",\n      iconName: this.model.icon,\n      size: \"auto\",\n      title: this.model.pageSelectorCaption\n    }))), React.createElement(Popup, {\n      model: this.model.popupModel\n    })), React.createElement(\"div\", null, this.model.visibleItems.map(item => React.createElement(SurveyPageNavigatorItem, {\n      key: item.id,\n      item: item\n    }))));\n  }\n}\nclass SurveyPageNavigatorItem extends CreatorModelElement {\n  getStateElement() {\n    return this.props.item;\n  }\n  renderElement() {\n    const item = this.props.item;\n    let className = \"svc-page-navigator-item-content\";\n    if (unwrap(item.active)) {\n      className += \" svc-page-navigator-item--selected\";\n    }\n    if (unwrap(item.disabled)) {\n      className += \" svc-page-navigator-item--disabled\";\n    }\n    return React.createElement(\"div\", {\n      className: \"svc-page-navigator-item\"\n    }, attachKey2click(React.createElement(\"div\", {\n      role: \"button\",\n      className: className,\n      onClick: e => {\n        item.action(item);\n        e.stopPropagation();\n      }\n    }, React.createElement(\"div\", {\n      className: \"svc-page-navigator-item__dot\",\n      title: item.title\n    }, React.createElement(\"div\", {\n      className: \"svc-page-navigator-item__dot-content\"\n    })), React.createElement(\"div\", {\n      className: \"svc-page-navigator-item__banner\"\n    }, React.createElement(\"span\", {\n      className: \"svc-page-navigator-item__text\"\n    }, item.title), React.createElement(\"span\", {\n      className: \"svc-page-navigator-item__dot\"\n    }, React.createElement(\"span\", {\n      className: \"svc-page-navigator-item__dot-content\"\n    }))))));\n  }\n}\nclass SurfacePlaceholder extends React.Component {\n  constructor(props) {\n    super(props);\n  }\n  render() {\n    return React.createElement(\"div\", {\n      className: \"svc-surface-placeholder\"\n    }, React.createElement(\"div\", {\n      className: \"svc-surface-placeholder__image svc-surface-placeholder__image--\" + this.props.name\n    }), React.createElement(\"div\", {\n      className: \"svc-surface-placeholder__text\"\n    }, React.createElement(\"div\", {\n      className: \"svc-surface-placeholder__title\"\n    }, this.props.placeholderTitleText), React.createElement(\"div\", {\n      className: \"svc-surface-placeholder__description\"\n    }, this.props.placeholderDescriptionText)));\n  }\n}\nReactElementFactory.Instance.registerElement(\"svc-surface-placeholder\", props => {\n  return React.createElement(SurfacePlaceholder, props);\n});\nclass TabDesignerComponent extends SurveyElementBase {\n  constructor() {\n    super(...arguments);\n    this.denyUpdate = () => {\n      this.denyComponentUpdate();\n    };\n    this.allowUpdate = () => {\n      this.allowComponentUpdate();\n    };\n    this.addDragDropEvents = () => {\n      this.creator.onDragStart.add(this.denyUpdate);\n      this.creator.onDragClear.add(this.allowUpdate);\n    };\n    this.clearDragDropEvents = () => {\n      this.creator.onDragStart.remove(this.denyUpdate);\n      this.creator.onDragClear.remove(this.allowUpdate);\n    };\n  }\n  get model() {\n    return this.props.data;\n  }\n  get creator() {\n    return this.model.creator;\n  }\n  componentDidMount() {\n    super.componentDidMount();\n    this.addDragDropEvents();\n  }\n  componentWillUnmount() {\n    super.componentWillUnmount();\n    this.clearDragDropEvents();\n    super.componentWillUnmount();\n  }\n  getStateElements() {\n    return [this.model, this.model.survey, this.model.pagesController];\n  }\n  getRenderedPages() {\n    const renderedPages = [];\n    if (this.creator.pageEditMode !== \"bypage\") {\n      const pages = this.model.pages;\n      pages.forEach(page => {\n        renderedPages.push(this.createRenderedPage(page, page == this.model.newPage));\n      });\n    } else {\n      const page2Display = this.model.pagesController.page2Display;\n      if (!!page2Display) {\n        renderedPages.push(this.createRenderedPage(page2Display, this.model.newPage === page2Display));\n      }\n    }\n    return renderedPages;\n  }\n  createRenderedPage(page, isGhostPage) {\n    return React.createElement(\"div\", {\n      className: \"svc-page\",\n      \"data-sv-drop-target-page\": page.name,\n      \"data-sv-drop-target-survey-element\": isGhostPage ? \"newGhostPage\" : page.name,\n      key: page.id\n    }, this.renderPage(page, isGhostPage));\n  }\n  renderNewPage(className, key = \"\") {\n    return React.createElement(React.Fragment, {\n      key: key\n    }, React.createElement(\"div\", {\n      className: className,\n      \"data-sv-drop-target-survey-element\": \"newGhostPage\"\n    }, !!this.model.newPage ? this.renderPage(this.model.newPage, true) : null));\n  }\n  renderPage(pageV, isGhost) {\n    return ReactElementFactory.Instance.createElement(\"svc-page\", {\n      survey: this.creator.survey,\n      page: pageV,\n      creator: this.creator,\n      isGhost\n    });\n  }\n  renderElement() {\n    const designerTabClassName = \"svc-tab-designer \" + this.model.getRootCss();\n    return React.createElement(React.Fragment, null, React.createElement(\"div\", {\n      className: \"svc-flex-column\"\n    }, this.model.isToolboxVisible ? ReactElementFactory.Instance.createElement(\"svc-toolbox\", {\n      model: this.creator\n    }) : null), React.createElement(\"div\", {\n      className: designerTabClassName,\n      onClick: () => this.model.clickDesigner()\n    }, React.createElement(Scroll, null, React.createElement(\"div\", {\n      className: \"svc-tab-designer_content\"\n    }, this.model.showPlaceholder ? this.renderPlaceHolder() : this.renderTabContent()))));\n  }\n  renderHeader(condition) {\n    if (!condition) return null;\n    const survey = this.creator.survey;\n    return React.createElement(React.Fragment, null, React.createElement(\"div\", {\n      className: \"svc-designer-header\"\n    }, React.createElement(SurveyHeader, {\n      survey: survey\n    })));\n  }\n  renderPlaceHolder() {\n    const surveyHeader = this.renderHeader(this.creator.allowEditSurveyTitle && this.creator.showHeaderInEmptySurvey);\n    return React.createElement(React.Fragment, null, surveyHeader, React.createElement(\"div\", {\n      className: \"svc-designer__placeholder-container\",\n      \"data-sv-drop-target-survey-element\": \"newGhostPage\"\n    }, this.renderPlaceHolderContent(), this.renderNewPage(\"svc-designer-placeholder-page\")));\n  }\n  renderPlaceHolderContent() {\n    return React.createElement(SurfacePlaceholder, {\n      name: \"designer\",\n      placeholderTitleText: this.model.placeholderTitleText,\n      placeholderDescriptionText: this.model.placeholderDescriptionText\n    });\n  }\n  renderTabContent() {\n    const survey = this.creator.survey;\n    const surveyHeader = this.renderHeader(this.creator.allowEditSurveyTitle);\n    const style = Object.assign({}, this.model.surfaceCssVariables);\n    style.maxWidth = survey.renderedWidth;\n    const tabTools = this.renderTabTools();\n    return React.createElement(React.Fragment, null, React.createElement(\"div\", {\n      className: this.model.designerCss,\n      style: style\n    }, surveyHeader, this.getRenderedPages()), tabTools);\n  }\n  renderTabTools() {\n    if (!this.model.showSurfaceTools) return null;\n    const pageNavigator = this.creator.showPageNavigator ? React.createElement(\"div\", {\n      className: \"svc-tab-designer__page-navigator\"\n    }, React.createElement(SurveyPageNavigator, {\n      pagesController: this.model.pagesController,\n      pageEditMode: this.model.creator.pageEditMode\n    })) : null;\n    const surfaceToolbar = this.model.showSurfaceToolbar ? React.createElement(SurveyActionBar, {\n      model: this.model.surfaceToolbar,\n      handleClick: false\n    }) : null;\n    return React.createElement(\"div\", {\n      className: \"svc-tab-designer__tools\"\n    }, pageNavigator, surfaceToolbar);\n  }\n}\nReactElementFactory.Instance.registerElement(\"svc-tab-designer\", props => {\n  return React.createElement(TabDesignerComponent, props);\n});\nclass TabJsonEditorErrorsComponent extends SurveyElementBase {\n  getStateElement() {\n    return this.model;\n  }\n  get model() {\n    return this.props.data;\n  }\n  renderElement() {\n    return React.createElement(\"div\", {\n      className: \"svc-json-editor-tab__errros_list\",\n      style: {\n        display: this.model.hasErrors ? \"block\" : \"none\"\n      }\n    }, React.createElement(List, {\n      model: this.model.errorList\n    }));\n  }\n}\nclass TabJsonEditorTextareaComponent extends SurveyElementBase {\n  constructor(props) {\n    super(props);\n  }\n  getStateElement() {\n    return this.model;\n  }\n  get model() {\n    return this.props.data;\n  }\n  renderElement() {\n    const setControl = el => {\n      this.model.textElement = el;\n    };\n    const errors = React.createElement(TabJsonEditorErrorsComponent, {\n      data: this.model\n    });\n    return React.createElement(\"div\", {\n      className: \"svc-creator-tab__content\"\n    }, React.createElement(\"div\", {\n      className: \"svc-json-editor-tab__content\"\n    }, React.createElement(\"textarea\", {\n      ref: input => setControl(input),\n      className: \"svc-json-editor-tab__content-area\",\n      value: this.model.text,\n      onChange: e => this.model.text = e.target.value,\n      onKeyDown: e => this.model.checkKey(e, e),\n      disabled: this.model.readOnly,\n      \"aria-label\": this.model.ariaLabel\n    }), errors));\n  }\n}\nReactElementFactory.Instance.registerElement(\"svc-tab-json-editor-textarea\", props => {\n  return React.createElement(TabJsonEditorTextareaComponent, props);\n});\nclass TabJsonEditorAceComponent extends SurveyElementBase {\n  constructor(props) {\n    super(props);\n    this.aceEditorrRef = React.createRef();\n  }\n  getStateElement() {\n    return this.model;\n  }\n  get model() {\n    return this.props.data;\n  }\n  componentDidMount() {\n    this.model.init(ace.edit(this.aceEditorrRef.current));\n  }\n  renderElement() {\n    const errors = React.createElement(TabJsonEditorErrorsComponent, {\n      data: this.model\n    });\n    return React.createElement(\"div\", {\n      className: \"svc-creator-tab__content\"\n    }, React.createElement(\"div\", {\n      className: \"svc-json-editor-tab__content\"\n    }, React.createElement(\"div\", {\n      className: \"svc-json-editor-tab__ace-editor\",\n      ref: this.aceEditorrRef\n    }), errors));\n  }\n}\nReactElementFactory.Instance.registerElement(\"svc-tab-json-editor-ace\", props => {\n  return React.createElement(TabJsonEditorAceComponent, props);\n});\nclass TabLogicAddButtonComponent extends SurveyElementBase {\n  get model() {\n    return this.props.button;\n  }\n  getStateElement() {\n    return this.model;\n  }\n  renderElement() {\n    const buttonClassName = \"svc-logic-tab__content-action svc-btn\" + (this.model.enabled !== undefined && !this.model.enabled ? \" svc-logic-tab__content-action--disabled\" : \"\");\n    return attachKey2click(React.createElement(\"div\", {\n      role: \"button\",\n      onClick: e => {\n        e.stopPropagation();\n        this.model.action();\n      },\n      className: buttonClassName,\n      title: this.model.title\n    }, React.createElement(\"span\", {\n      className: \"svc-btn__text\"\n    }, this.model.title)));\n  }\n}\nReactElementFactory.Instance.registerElement(\"svc-tab-logic-add-button\", props => {\n  return React.createElement(TabLogicAddButtonComponent, props);\n});\nclass TabLogicComponent extends SurveyElementBase {\n  get model() {\n    return this.props.data;\n  }\n  getStateElement() {\n    return this.model;\n  }\n  renderElement() {\n    this.model;\n    var rootClass = \"svc-creator-tab__content svc-logic-tab\";\n    var content = this.renderViewContent();\n    return React.createElement(\"div\", {\n      className: rootClass\n    }, content);\n  }\n  renderViewContent() {\n    const logicTabClassName = \"svc-plugin-tab__content svc-logic-tab svc-logic-tab__content \" + (this.model.hasItems ? \"\" : \"svc-logic-tab--empty\");\n    const addLogic = !this.model.readOnly ? React.createElement(TabLogicAddButtonComponent, {\n      button: this.model.addNewButton\n    }) : undefined;\n    return React.createElement(Fragment, null, React.createElement(\"div\", {\n      className: logicTabClassName\n    }, this.model.hasItems ? React.createElement(React.Fragment, null, React.createElement(Survey, {\n      model: this.model.itemsSurvey\n    }), addLogic) : React.createElement(\"div\", {\n      className: \"svc-logic-tab__content-empty\"\n    }, React.createElement(SurfacePlaceholder, {\n      name: \"logic\",\n      placeholderTitleText: this.model.placeholderTitleText,\n      placeholderDescriptionText: this.model.placeholderDescriptionText\n    }), addLogic)));\n  }\n}\nReactElementFactory.Instance.registerElement(\"svc-tab-logic\", props => {\n  return React.createElement(TabLogicComponent, props);\n});\nclass SurveySimulator extends SurveyElementBase {\n  get model() {\n    return this.props.model;\n  }\n  getStateElement() {\n    return this.model;\n  }\n  renderElement() {\n    const mainSimulatorClass = this.model.getRootCss();\n    if (!this.model.survey) {\n      return React.createElement(\"div\", {\n        className: mainSimulatorClass\n      });\n    }\n    if (this.model.hasFrame) {\n      return React.createElement(\"div\", {\n        className: mainSimulatorClass,\n        onKeyDown: e => this.model.tryToZoom(e, e),\n        onMouseEnter: this.model.device === \"desktop\" ? null : this.model.activateZoom,\n        onMouseLeave: this.model.device === \"desktop\" ? null : this.model.deactivateZoom\n      }, React.createElement(\"div\", {\n        className: \"svd-simulator-wrapper\",\n        id: \"svd-simulator-wrapper\",\n        style: {\n          width: this.model.simulatorFrame.frameWidth + \"px\",\n          height: this.model.simulatorFrame.frameHeight + \"px\"\n        }\n      }, React.createElement(\"div\", {\n        className: \"svd-simulator\",\n        style: {\n          width: this.model.simulatorFrame.deviceWidth + \"px\",\n          height: this.model.simulatorFrame.deviceHeight + \"px\",\n          transform: \"scale(\" + this.model.simulatorFrame.scale + \") translate(-50%, -50%)\"\n        }\n      }, React.createElement(\"div\", {\n        className: \"svd-simulator-content\"\n      }, React.createElement(Survey, {\n        model: this.model.survey\n      })))));\n    } else {\n      return React.createElement(\"div\", {\n        className: mainSimulatorClass\n      }, React.createElement(\"div\", {\n        className: \"svd-simulator-content\"\n      }, React.createElement(Survey, {\n        model: this.model.survey\n      })));\n    }\n  }\n}\nclass TabPreviewTestSurveyAgainComponent extends SurveyElementBase {\n  get model() {\n    return this.props.model.testAgainAction;\n  }\n  getStateElement() {\n    return this.model;\n  }\n  renderElement() {\n    const buttonClassName = \"svc-preview__test-again svc-btn\";\n    return attachKey2click(React.createElement(\"div\", {\n      role: \"button\",\n      onClick: e => {\n        e.stopPropagation();\n        this.model.action();\n      },\n      className: buttonClassName,\n      title: this.model.title\n    }, React.createElement(\"span\", {\n      className: \"svc-btn__text\"\n    }, this.model.title)));\n  }\n}\nReactElementFactory.Instance.registerElement(\"svc-complete-page\", props => {\n  return React.createElement(TabPreviewTestSurveyAgainComponent, props);\n});\nclass TabPreviewSurveyComponent extends SurveyElementBase {\n  constructor(props) {\n    super(props);\n  }\n  get model() {\n    return this.props.data;\n  }\n  getStateElement() {\n    return this.model;\n  }\n  renderPlaceholder() {\n    return React.createElement(SurfacePlaceholder, {\n      name: \"preview\",\n      placeholderTitleText: this.model.placeholderTitleText,\n      placeholderDescriptionText: this.model.placeholderDescriptionText\n    });\n  }\n  renderSimulator() {\n    return React.createElement(\"div\", {\n      className: \"svc-plugin-tab__content\"\n    }, React.createElement(SurveySimulator, {\n      model: this.model.simulator\n    }), this.model.showResults ? React.createElement(SurveyResults, {\n      survey: this.model.simulator.survey\n    }) : null);\n  }\n  renderElement() {\n    const tabContentClassName = \"svc-creator-tab__content svc-test-tab__content\" + (this.model.isPageToolbarVisible ? \" svc-creator-tab__content--with-toolbar\" : \"\");\n    return React.createElement(\"div\", {\n      className: tabContentClassName\n    }, this.model.simulator.survey.isEmpty ? this.renderPlaceholder() : this.renderSimulator(), this.getBottomToolbar());\n  }\n  getBottomToolbar() {\n    if (this.model.isPageToolbarVisible) {\n      return React.createElement(\"div\", {\n        className: \"svc-test-tab__content-actions\"\n      }, React.createElement(SurveyActionBar, {\n        model: this.model.pages\n      }));\n    } else {\n      return null;\n    }\n  }\n}\nReactElementFactory.Instance.registerElement(\"svc-tab-preview\", props => {\n  return React.createElement(TabPreviewSurveyComponent, props);\n});\nclass PropertyGridPlaceholderComponent extends React.Component {\n  render() {\n    return React.createElement(\"div\", {\n      className: \"svc-property-grid-placeholder\"\n    }, React.createElement(\"div\", {\n      className: \"svc-property-grid-placeholder__header\"\n    }, React.createElement(\"span\", {\n      className: \"svc-property-grid-placeholder__title\"\n    }, editorLocalization.getString(\"ed.propertyGridPlaceholderTitle\")), React.createElement(\"span\", {\n      className: \"svc-property-grid-placeholder__description\"\n    }, editorLocalization.getString(\"ed.propertyGridPlaceholderDescription\"))), React.createElement(\"div\", {\n      className: \"svc-property-grid-placeholder__content\"\n    }, React.createElement(\"div\", {\n      className: \"svc-property-grid-placeholder__gap\"\n    }), React.createElement(\"div\", {\n      className: \"svc-property-grid-placeholder__image\"\n    })));\n  }\n}\nReactElementFactory.Instance.registerElement(\"svc-property-grid-placeholder\", props => {\n  return React.createElement(PropertyGridPlaceholderComponent, props);\n});\nclass TabThemeSurveyComponent extends SurveyElementBase {\n  get model() {\n    return this.props.data;\n  }\n  getStateElement() {\n    return this.model;\n  }\n  renderPlaceholder() {\n    return React.createElement(SurfacePlaceholder, {\n      name: \"theme\",\n      placeholderTitleText: this.model.placeholderTitleText,\n      placeholderDescriptionText: this.model.placeholderDescriptionText\n    });\n  }\n  renderSimulator() {\n    return React.createElement(\"div\", {\n      className: \"svc-plugin-tab__content\"\n    }, React.createElement(SurveySimulator, {\n      model: this.model.simulator\n    }), this.model.showResults ? React.createElement(SurveyResults, {\n      survey: this.model.simulator.survey\n    }) : null);\n  }\n  renderElement() {\n    const tabContentClassName = \"svc-creator-tab__content svc-test-tab__content\" + (this.model.isPageToolbarVisible ? \" svc-creator-tab__content--with-toolbar\" : \"\");\n    return React.createElement(\"div\", {\n      className: tabContentClassName\n    }, this.model.simulator.survey.isEmpty ? this.renderPlaceholder() : this.renderSimulator(), this.getBottomToolbar());\n  }\n  getBottomToolbar() {\n    if (this.model.isPageToolbarVisible) {\n      return React.createElement(\"div\", {\n        className: \"svc-test-tab__content-actions\"\n      }, React.createElement(SurveyActionBar, {\n        model: this.model.pages\n      }));\n    } else {\n      return null;\n    }\n  }\n}\nReactElementFactory.Instance.registerElement(\"svc-tab-theme\", props => {\n  return React.createElement(TabThemeSurveyComponent, props);\n});\nclass TabTranslationComponent extends SurveyElementBase {\n  get model() {\n    return this.props.data || this.props.model;\n  }\n  getStateElement() {\n    return this.model;\n  }\n  renderElement() {\n    if (!this.model) return null;\n    return React.createElement(\"div\", {\n      className: \"svc-creator-tab__content svc-translation-tab\" + (this.model.isEmpty ? \" svc-translation-tab--empty\" : \"\")\n    }, this.renderElementContent());\n  }\n  renderElementContent() {\n    if (this.model.isEmpty) {\n      return React.createElement(SurfacePlaceholder, {\n        name: \"translation\",\n        placeholderTitleText: this.model.placeholderTitleText,\n        placeholderDescriptionText: this.model.placeholderDescriptionText\n      });\n    } else {\n      return React.createElement(\"div\", {\n        className: \"st-content\"\n      }, React.createElement(\"div\", {\n        className: \"svc-flex-column st-strings-wrapper\"\n      }, React.createElement(\"div\", {\n        className: \"svc-flex-row st-strings-header\"\n      }, React.createElement(Survey, {\n        model: this.model.stringsHeaderSurvey\n      })), React.createElement(\"div\", {\n        className: \"svc-flex-row svc-plugin-tab__content st-strings\"\n      }, React.createElement(Survey, {\n        model: this.model.stringsSurvey\n      }))));\n    }\n  }\n}\nReactElementFactory.Instance.registerElement(\"svc-tab-translation\", props => {\n  return React.createElement(TabTranslationComponent, props);\n});\nclass ObjectSelectorComponent extends SurveyElementBase {\n  get model() {\n    return this.props.model;\n  }\n  getStateElement() {\n    return this.model;\n  }\n  renderElement() {\n    if (!this.model.isVisible) return null;\n    return React.createElement(List, {\n      model: this.model.list\n    });\n  }\n}\nReactElementFactory.Instance.registerElement(\"svc-object-selector\", props => {\n  return React.createElement(ObjectSelectorComponent, props);\n});\nclass PropertyGridComponent extends SurveyElementBase {\n  get model() {\n    return this.props.model;\n  }\n  getStateElement() {\n    return this.model;\n  }\n  canRender() {\n    if (!this.model) return false;\n    return super.canRender();\n  }\n  renderElement() {\n    return React.createElement(\"div\", {\n      className: this.model.rootCss\n    }, React.createElement(SearchComponent, {\n      model: this.model.searchManager\n    }), React.createElement(Survey, {\n      model: this.model.survey\n    }));\n  }\n}\nReactElementFactory.Instance.registerElement(\"svc-property-grid\", props => {\n  return React.createElement(PropertyGridComponent, props);\n});\nclass SwitcherComponent extends SurveyElementBase {\n  get item() {\n    return this.props.item;\n  }\n  getStateElement() {\n    return this.item;\n  }\n  renderElement() {\n    const tooltip = this.item.tooltip || this.item.title;\n    const title = this.item.hasTitle ? React.createElement(\"span\", {\n      className: \"svc-switcher__title\"\n    }, this.item.title) : null;\n    const button = attachKey2click(React.createElement(\"button\", {\n      className: this.item.getActionBarItemCss(),\n      type: \"button\",\n      disabled: this.item.disabled,\n      onClick: args => this.item.action(this.item, this.item.getIsTrusted(args)),\n      title: tooltip,\n      \"aria-checked\": this.item.ariaChecked,\n      \"aria-expanded\": this.item.ariaExpanded,\n      role: this.item.ariaRole\n    }, React.createElement(\"div\", {\n      className: this.item.getSwitcherIconCss()\n    }, React.createElement(\"div\", {\n      className: \"svc-switcher__icon-thumb\"\n    })), title), this.item, {\n      processEsc: false\n    });\n    return button;\n  }\n}\nReactElementFactory.Instance.registerElement(\"svc-switcher\", props => {\n  return React.createElement(SwitcherComponent, props);\n});\nlet ItemTemplateComponent$1 = class ItemTemplateComponent extends SurveyElementBase {\n  render() {\n    const item = this.props.item;\n    return React.createElement(React.Fragment, null, React.createElement(SvgIcon, {\n      iconName: item.iconName,\n      size: item.iconSize,\n      className: \"svc-json-error__icon\"\n    }), React.createElement(\"div\", {\n      className: \"svc-json-error__container\"\n    }, React.createElement(\"div\", {\n      className: \"svc-json-error__title\"\n    }, React.createElement(\"span\", {\n      key: 2\n    }, this.renderLocString(item.locTitle, undefined, \"locString\"))), this.renderFixButton()));\n  }\n  renderFixButton() {\n    if (!this.props.item.data.showFixButton) return null;\n    const item = this.props.item;\n    return attachKey2click(React.createElement(\"button\", {\n      type: \"button\",\n      onClick: event => {\n        event.stopPropagation();\n        item.data.fixError();\n      },\n      title: item.data.fixButtonTitle,\n      className: \"svc-json-error__fix-button\"\n    }, React.createElement(SvgIcon, {\n      iconName: item.data.fixButtonIcon,\n      size: \"auto\"\n    })));\n  }\n};\nReactElementFactory.Instance.registerElement(\"json-error-item\", props => {\n  return React.createElement(ItemTemplateComponent$1, props);\n});\nclass SurveyQuestionSpinEditor extends SurveyQuestionText {\n  constructor(props) {\n    super(props);\n  }\n  get question() {\n    return this.questionBase;\n  }\n  renderInput() {\n    return React.createElement(React.Fragment, null, React.createElement(\"input\", {\n      role: \"spinbutton\",\n      id: this.question.inputId,\n      disabled: this.isDisplayMode,\n      className: this.question.cssClasses.control,\n      ref: input => this.setControl(input),\n      placeholder: this.question.renderedPlaceholder,\n      autoComplete: \"off\",\n      onBlur: event => this.question.onBlur(event.nativeEvent),\n      onFocus: event => this.question.onFocus(event.nativeEvent),\n      onChange: this.question.onChange,\n      onBeforeInput: event => this.question.onBeforeInput(event.nativeEvent),\n      onKeyUp: event => this.question.onKeyUp(event.nativeEvent),\n      onKeyDown: event => this.question.onInputKeyDown(event.nativeEvent),\n      \"aria-required\": this.question.a11y_input_ariaRequired,\n      \"aria-label\": this.question.a11y_input_ariaLabel,\n      \"aria-labelledby\": this.question.a11y_input_ariaLabelledBy,\n      \"aria-describedby\": this.question.a11y_input_ariaDescribedBy,\n      \"aria-invalid\": this.question.a11y_input_ariaInvalid,\n      \"aria-errormessage\": this.question.a11y_input_ariaErrormessage\n    }));\n  }\n  renderElement() {\n    return React.createElement(\"div\", {\n      className: this.question.cssClasses.root,\n      onKeyDown: event => this.question.onKeyDown(event.nativeEvent)\n    }, this.renderInput(), this.renderButtons());\n  }\n  getValueCore() {\n    return this.question.renderedValue;\n  }\n  renderButtons() {\n    return React.createElement(\"span\", {\n      className: this.question.cssClasses.buttonsContainer\n    }, React.createElement(\"button\", {\n      tabIndex: -1,\n      \"aria-hidden\": \"true\",\n      className: this.question.cssClasses.arrowButton,\n      disabled: this.isDisplayMode,\n      onClick: this.question.onDownButtonClick,\n      onMouseDown: this.question.onDownButtonMouseDown,\n      onMouseUp: this.question.onButtonMouseUp,\n      onMouseLeave: this.question.onButtonMouseLeave,\n      onBlur: event => this.question.onBlur(event.nativeEvent),\n      onFocus: event => this.question.onFocus(event.nativeEvent)\n    }, React.createElement(SvgIcon, {\n      iconName: this.question.cssClasses.decreaseButtonIcon,\n      size: \"auto\"\n    })), React.createElement(\"button\", {\n      tabIndex: -1,\n      \"aria-hidden\": \"true\",\n      className: this.question.cssClasses.arrowButton,\n      disabled: this.isDisplayMode,\n      onClick: this.question.onUpButtonClick,\n      onMouseDown: this.question.onUpButtonMouseDown,\n      onMouseUp: this.question.onButtonMouseUp,\n      onMouseLeave: this.question.onButtonMouseLeave,\n      onBlur: event => this.question.onBlur(event.nativeEvent),\n      onFocus: event => this.question.onFocus(event.nativeEvent)\n    }, React.createElement(SvgIcon, {\n      iconName: this.question.cssClasses.increaseButtonIcon,\n      size: \"auto\"\n    })));\n  }\n}\nReactQuestionFactory.Instance.registerQuestion(\"spinedit\", props => {\n  return React.createElement(SurveyQuestionSpinEditor, props);\n});\nclass ItemTemplateComponent extends SurveyElementBase {\n  render() {\n    const item = this.props.item;\n    return React.createElement(React.Fragment, null, React.createElement(\"span\", {\n      className: \"spg-color-editor__color-swatch\",\n      style: {\n        backgroundColor: item.value\n      }\n    }), React.createElement(\"span\", {\n      key: 2\n    }, this.renderLocString(item.locTitle, undefined, \"locString\")));\n  }\n}\nReactElementFactory.Instance.registerElement(\"color-item\", props => {\n  return React.createElement(ItemTemplateComponent, props);\n});\nclass SurveyQuestionColor extends SurveyQuestionText {\n  constructor(props) {\n    super(props);\n  }\n  get question() {\n    return this.questionBase;\n  }\n  renderInput() {\n    return React.createElement(React.Fragment, null, React.createElement(\"input\", {\n      id: this.question.inputId,\n      disabled: this.isDisplayMode,\n      className: this.question.cssClasses.control,\n      ref: input => this.setControl(input),\n      placeholder: this.question.renderedPlaceholder,\n      autoComplete: \"off\",\n      onKeyUp: event => this.question.onKeyUp(event.nativeEvent),\n      onBlur: event => this.question.onBlur(event.nativeEvent),\n      onChange: this.question.onChange,\n      onBeforeInput: event => this.question.onBeforeInput(event.nativeEvent),\n      \"aria-required\": this.question.a11y_input_ariaRequired,\n      \"aria-labelledby\": this.question.a11y_input_ariaLabelledBy,\n      \"aria-label\": this.question.a11y_input_ariaLabel,\n      \"aria-invalid\": this.question.a11y_input_ariaInvalid,\n      \"aria-describedby\": this.question.a11y_input_ariaDescribedBy\n    }));\n  }\n  renderElement() {\n    return React.createElement(\"div\", {\n      className: this.question.cssClasses.root,\n      onKeyDown: event => this.question.onKeyDown(event.nativeEvent)\n    }, this.renderColorSwatch(), this.renderInput(), this.question.showDropdownAction ? this.renderDropdownAction() : null);\n  }\n  getValueCore() {\n    return this.question.renderedValue;\n  }\n  renderColorSwatch() {\n    return React.createElement(\"label\", {\n      className: this.question.getSwatchCss(),\n      style: this.question.getSwatchStyle()\n    }, React.createElement(SvgIcon, {\n      iconName: this.question.cssClasses.swatchIcon,\n      size: \"auto\"\n    }), React.createElement(\"input\", {\n      type: \"color\",\n      disabled: this.isDisplayMode,\n      value: this.question.renderedColorValue,\n      className: this.question.cssClasses.colorInput,\n      onChange: event => this.question.onColorInputChange(event.nativeEvent),\n      tabIndex: -1,\n      \"aria-required\": this.question.a11y_input_ariaRequired,\n      \"aria-labelledby\": this.question.a11y_input_ariaLabelledBy,\n      \"aria-label\": this.question.a11y_input_ariaLabel,\n      \"aria-invalid\": this.question.a11y_input_ariaInvalid,\n      \"aria-describedby\": this.question.a11y_input_ariaDescribedBy\n    }));\n  }\n  renderDropdownAction() {\n    return React.createElement(React.Fragment, null, React.createElement(\"div\", {\n      \"aria-hidden\": \"true\",\n      className: this.question.cssClasses.choicesButtonWrapper\n    }, ReactElementFactory.Instance.createElement(\"sv-action-bar-item\", {\n      item: this.question.dropdownAction\n    })), this.renderPopup());\n  }\n  renderPopup() {\n    return React.createElement(Popup, {\n      model: this.question.dropdownAction.popupModel\n    });\n  }\n}\nReactQuestionFactory.Instance.registerQuestion(\"color\", props => {\n  return React.createElement(SurveyQuestionColor, props);\n});\nclass SurveyQuestionFileEditor extends SurveyQuestionText {\n  constructor(props) {\n    super(props);\n  }\n  get questionFile() {\n    return this.questionBase;\n  }\n  getValueCore() {\n    return this.question.renderedValue;\n  }\n  renderInput() {\n    return React.createElement(React.Fragment, null, React.createElement(\"input\", {\n      disabled: this.question.isTextInputReadOnly,\n      className: this.questionFile.cssClasses.control,\n      placeholder: this.questionFile.renderedPlaceholder,\n      ref: input => this.setControl(input),\n      autoComplete: \"off\",\n      type: \"text\",\n      onBlur: event => this.questionFile.onInputBlur(event.nativeEvent),\n      onChange: event => this.questionFile.onInputChange(event.nativeEvent)\n    }));\n  }\n  renderFileInput() {\n    return React.createElement(\"input\", {\n      type: \"file\",\n      disabled: this.isDisplayMode,\n      className: this.questionFile.cssClasses.fileInput,\n      id: this.questionFile.inputId,\n      \"aria-required\": this.questionFile.ariaRequired,\n      \"aria-label\": this.questionFile.ariaLabel,\n      \"aria-invalid\": this.questionFile.ariaInvalid,\n      \"aria-describedby\": this.questionFile.ariaDescribedBy,\n      multiple: false,\n      title: this.questionFile.inputTitle,\n      accept: this.questionFile.acceptedTypes,\n      tabIndex: -1,\n      onChange: event => this.questionFile.onFileInputChange(event.nativeEvent)\n    });\n  }\n  renderButtons() {\n    return React.createElement(\"div\", {\n      className: this.questionFile.cssClasses.buttonsContainer\n    }, this.renderClearButton(), this.renderChooseButton());\n  }\n  renderClearButton() {\n    return attachKey2click(React.createElement(\"button\", {\n      className: this.questionFile.cssClasses.clearButton,\n      title: this.questionFile.clearButtonCaption,\n      disabled: this.questionFile.getIsClearButtonDisabled(),\n      onClick: this.questionFile.doClean\n    }, React.createElement(SvgIcon, {\n      iconName: this.questionFile.cssClasses.clearButtonIcon,\n      size: \"auto\"\n    })));\n  }\n  renderChooseButton() {\n    return attachKey2click(React.createElement(\"label\", {\n      onClick: event => this.questionFile.chooseFiles(event.nativeEvent),\n      className: this.questionFile.getChooseButtonCss(),\n      htmlFor: this.questionFile.inputId,\n      \"aria-label\": this.questionFile.chooseButtonCaption\n    }, React.createElement(SvgIcon, {\n      iconName: this.questionFile.cssClasses.chooseButtonIcon,\n      size: \"auto\",\n      title: this.questionFile.chooseButtonCaption\n    })));\n  }\n  renderElement() {\n    return React.createElement(\"div\", {\n      className: this.questionFile.cssClasses.root,\n      ref: el => this.setContent(el),\n      onDragEnter: this.questionFile.onDragEnter,\n      onDragOver: this.questionFile.onDragOver,\n      onDrop: this.questionFile.onDrop,\n      onDragLeave: this.questionFile.onDragLeave,\n      onKeyDown: event => this.question.onKeyDown(event.nativeEvent)\n    }, this.renderInput(), this.renderFileInput(), this.renderButtons());\n  }\n}\nReactQuestionFactory.Instance.registerQuestion(\"fileedit\", props => {\n  return React.createElement(SurveyQuestionFileEditor, props);\n});\nclass SurveyQuestionTextWithReset extends SurveyQuestionElementBase {\n  get question() {\n    return this.questionBase;\n  }\n  renderElement() {\n    const textElement = this.renderInput();\n    const resetButton = this.renderResetButton();\n    return React.createElement(\"div\", {\n      className: this.question.getRootClass()\n    }, textElement, resetButton);\n  }\n  renderInput() {\n    return ReactQuestionFactory.Instance.createQuestion(this.question.wrappedQuestionTemplate, {\n      question: this.question,\n      isDisplayMode: this.question.isInputReadOnly,\n      creator: this\n    });\n  }\n  renderResetButton() {\n    return React.createElement(\"button\", {\n      className: this.question.cssClasses.resetButton,\n      disabled: this.question.resetValueAdorner.isDisabled,\n      title: this.question.resetValueAdorner.caption,\n      onClick: () => this.question.resetValueAdorner.resetValue()\n    }, React.createElement(SvgIcon, {\n      iconName: this.question.cssClasses.resetButtonIcon,\n      size: \"auto\"\n    }));\n  }\n}\nReactQuestionFactory.Instance.registerQuestion(\"textwithreset\", props => {\n  return React.createElement(SurveyQuestionTextWithReset, props);\n});\nReactQuestionFactory.Instance.registerQuestion(\"commentwithreset\", props => {\n  return React.createElement(SurveyQuestionTextWithReset, props);\n});\nclass SurveyQuestionBooleanSwitch extends SurveyQuestionElementBase {\n  renderElement() {\n    const button = attachKey2click(React.createElement(\"div\", {\n      className: \"spg-boolean-switch__button\" + (this.questionBase.value ? \" spg-boolean-switch__button--checked\" : \"\"),\n      tabIndex: 0,\n      role: \"checkbox\",\n      \"aria-checked\": this.questionBase.booleanValue || false,\n      \"aria-required\": this.questionBase.a11y_input_ariaRequired,\n      \"aria-label\": this.questionBase.a11y_input_ariaLabel,\n      \"aria-labelledby\": this.questionBase.a11y_input_ariaLabelledBy,\n      \"aria-invalid\": this.questionBase.a11y_input_ariaInvalid,\n      \"aria-errormessage\": this.questionBase.a11y_input_ariaErrormessage\n    }, React.createElement(\"div\", {\n      className: \"spg-boolean-switch__thumb\"\n    }, React.createElement(\"div\", {\n      className: \"spg-boolean-switch__thumb-circle spg-boolean-switch__thumb--left\"\n    })), React.createElement(\"div\", {\n      className: \"spg-boolean-switch__thumb\"\n    }, React.createElement(\"div\", {\n      className: \"spg-boolean-switch__thumb-circle spg-boolean-switch__thumb--right\"\n    }))), this.questionBase, {\n      processEsc: false\n    });\n    return React.createElement(\"div\", {\n      className: \"spg-boolean-switch\",\n      onClick: () => this.questionBase.value = !this.questionBase.value\n    }, button, React.createElement(\"div\", {\n      className: \"spg-boolean-switch__caption\"\n    }, React.createElement(\"div\", {\n      className: \"spg-boolean-switch__title\",\n      id: this.questionBase.labelRenderedAriaID\n    }, SurveyElementBase.renderLocString(this.questionBase.locTitle))));\n  }\n}\nReactQuestionFactory.Instance.registerQuestion(\"sv-boolean-switch\", props => {\n  return React.createElement(SurveyQuestionBooleanSwitch, props);\n});\nRendererFactory.Instance.registerRenderer(\"boolean\", \"switch\", \"sv-boolean-switch\");\nlet Version;\nVersion = `${\"2.2.2\"}`;\ncheckLibraryVersion(`${\"2.2.2\"}`, \"survey-creator-react\");\nexport { ActionButton, AdaptiveToolbox, CellQuestionAdornerComponent, CellQuestionDropdownAdornerComponent, CreatorSurveyPageComponent, ImageItemValueAdornerComponent, ItemValueAdornerComponent, LogoImageComponent, MatrixCellAdornerComponent, PanelAdornerComponent, PropertyGridComponent, PropertyGridPlaceholderComponent, QuestionAdornerComponent, QuestionBanner, QuestionDropdownAdornerComponent, QuestionEditorContentComponent, QuestionErrorComponent, QuestionImageAdornerComponent, QuestionRatingAdornerComponent, QuestionWidgetAdornerComponent, QuestionWrapperFooter, QuestionWrapperHeader, ReactDragEvent, ReactMouseEvent, RowWrapper, SearchComponent, SideBarDefaultHeader, SidebarComponent, SurveyCreator, SurveyCreatorComponent, SurveyCreatorToolboxCategory, SurveyCreatorToolboxItem, SurveyCreatorToolboxItemGroup, SurveyCreatorToolboxTool, SurveyElementEmbeddedSurvey, SurveyLocStringEditor, SurveyLogicOpertor, SurveyNavigation, SurveyQuestionBooleanSwitch, SurveyQuestionColor, SurveyQuestionFileEditor, SurveyQuestionLinkValue, SurveyQuestionSpinEditor, SurveyQuestionTextWithReset, SurveyResults, SurveyResultsByRow, SurveySimulator, SwitcherComponent, TabButtonComponent, TabDesignerComponent, TabJsonEditorAceComponent, TabJsonEditorErrorsComponent, TabJsonEditorTextareaComponent, TabLogicAddButtonComponent, TabLogicComponent, TabPreviewSurveyComponent, TabPreviewTestSurveyAgainComponent, TabThemeSurveyComponent, TabTranslationComponent, TabbedMenuComponent, TabbedMenuItemComponent, ToolboxList, TranslateFromAction, TranslationLineSkeleton, Version };", "map": {"version": 3, "names": ["TabbedMenuComponent", "SurveyElementBase", "model", "props", "getStateElement", "constructor", "rootRef", "React", "createRef", "renderElement", "items", "renderedActions", "map", "item", "createElement", "TabbedMenuItemWrapper", "key", "renderedId", "ref", "className", "role", "style", "getRootStyle", "componentDidUpdate", "prevProps", "prevState", "container", "current", "initResponsivityManager", "componentDidMount", "componentWillUnmount", "resetResponsivityManager", "css", "isVisible", "component", "ReactElementFactory", "Instance", "id", "updateModeCallback", "mode", "callback", "queueMicrotask", "ReactDOM", "afterRender", "undefined", "TabbedMenuItemComponent", "render", "attachKey2click", "active", "getRootCss", "onClick", "action", "hasTitle", "getTitleCss", "title", "hasIcon", "SvgIcon", "iconName", "getIconCss", "size", "tooltip", "registerElement", "SurveyCreatorComponent", "rootNode", "creator", "unsubscribeRootElement", "setRootElement", "isCreatorDisposed", "areaClassName", "haveCommercialLicense", "contentWrapperClassName", "isMobile<PERSON>iew", "fullContainerClassName", "sidebarLocation", "creator<PERSON><PERSON><PERSON>", "assign", "themeVariables", "licenseBanner", "htmlValue", "__html", "licenseText", "dangerouslySetInnerHTML", "SvgBundleComponent", "PopupModal", "showTabs", "tabbedMenu", "showToolbar", "SurveyActionBar", "toolbar", "renderActiveTab", "footerT<PERSON>bar", "renderSidebar", "NotifierComponent", "notifier", "i", "tabs", "length", "activeTab", "renderCreatorTab", "tab", "visible", "renderTab", "componentContent", "survey", "data", "toolboxLocation", "sidebar", "SurveyCreator", "SurveyCreatorModel", "options", "options2", "target", "console", "error", "createQuestionElement", "question", "ReactQuestionFactory", "createQuestion", "isDefaultRendering", "getTemplate", "getComponentName", "isDisplayMode", "isReadOnly", "renderError", "cssClasses", "icon", "SurveyLocStringViewer", "locStr", "locText", "questionTitleLocation", "questionErrorLocation", "Survey", "CreatorModelElement", "createModel", "shouldComponentUpdate", "nextProps", "nextState", "result", "needUpdateModel", "names", "getUpdatedModelProps", "Array", "isArray", "RowWrapper", "dispose", "RowViewModel", "componentData", "row", "subscribeElementChanges", "unsubscribeElementChanges", "element", "ReactMouseEvent", "event", "stopPropagation", "preventDefault", "cancelBubble", "value", "currentTarget", "clientX", "clientY", "offsetX", "nativeEvent", "offsetY", "ReactDragEvent", "dataTransfer", "QuestionElementContentFunc", "Question<PERSON><PERSON><PERSON><PERSON><PERSON>", "memo", "displayName", "QuestionAdornerComponent", "attachToUI", "modelValue", "createQuestionViewModel", "QuestionAdornerViewModel", "allowInteractions", "isInteractiveDesignElement", "titleForCollapsedState", "renderQuestionTitle", "content", "renderContent", "name", "rootCss", "onDoubleClick", "e", "dblclick", "onMouseLeave", "hover", "onMouseOver", "disableTabStop", "needToRender<PERSON><PERSON>nt", "renderElementContent", "select", "renderHeader", "renderFooter", "renderCarryForwardBanner", "isBannerShowing", "createBannerParams", "showHiddenTitle", "node", "renderedCollapsed", "setAttribute", "removeAttribute", "cssCollapsedHiddenHeader", "TitleElement", "cssCollapsedHiddenTitle", "Fragment", "renderElementPlaceholder", "isEmptyElement", "placeholderText", "detachFromUI", "QuestionWrapperHeader", "Component", "allowDragging", "onPointerDown", "topActionContainer", "handleClick", "Question<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onFocus", "actionContainer", "ActionButton", "classes", "CssClassBuilder", "append", "selected", "disabled", "toString", "renderIcon", "renderButtonText", "text", "allowBubble", "click", "QuestionBanner", "actionText", "QuestionDropdownAdornerComponent", "QuestionDropdownAdornerViewModel", "dropdownModel", "textStyle", "getRenderedItems", "index", "getChoiceCss", "ReactSurveyElementsWrapper", "wrapItemValue", "itemComponent", "isChecked", "needToCollapse", "switchCollapse", "getButtonText", "QuestionImageAdornerComponent", "QuestionImageAdornerViewModel", "imageModel", "type", "tabIndex", "accept", "acceptedTypes", "position", "opacity", "width", "height", "overflow", "renderLoadingPlaceholder", "LoadingIndicatorComponent", "renderChooseButton", "allowEdit", "chooseFile", "isUploading", "getStateElements", "filePresentationModel", "isEmptyImageLink", "fileQuestion", "QuestionRatingAdornerComponent", "QuestionRatingAdornerViewModel", "ratingModel", "controlsClassNames", "allowRemove", "removeClassNames", "removeTooltip", "removeItem", "allowAdd", "addClassNames", "addTooltip", "addItem", "QuestionWidgetAdornerComponent", "widgetModel", "CellQuestionAdornerComponent", "CellQuestionDropdownAdornerComponent", "visibleChoices", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "page", "SurveyPage", "CreatorSurveyPageComponent", "createPageAdorner", "isGhost", "Page<PERSON><PERSON><PERSON>", "res", "canRender", "dropTargetName", "renderPlaceholder", "showPlaceholder", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "actions", "hasActions", "footerActionsBar", "AddQuestionButtonComponent", "renderTypeSelector", "questionTypeSelectorModel", "addNewQuestionText", "renderPopup", "Popup", "popupModel", "addButtonClass", "buttonClass", "addNewQuestion", "hoverStopper", "PanelAdornerComponent", "showAddQuestionButton", "isPanel", "LogoImageComponent", "prevRoot", "root", "LogoImageViewModel", "renderClearButton", "remove", "renderButtons", "renderImage", "containerCss", "LogoImage", "renderPlaceHolder", "xlinkHref", "renderInput", "renderLoadingIndicator", "locLogo", "renderedHtml", "SurveyQuestionLinkValue", "SurveyQuestionElementBase", "questionBase", "renderClear", "showClear", "linkClearButtonCssClasses", "doClearClick", "editorLocalization", "getString", "linkSetButtonCssClasses", "doLinkClick", "isSelected", "isClickable", "linkValueText", "registerQuestion", "SurveyElementEmbedded<PERSON><PERSON>vey", "embeddedSurvey", "currentPage", "QuestionEditorContentComponent", "isInputReadOnly", "getAllQuestions", "SurveyQuestion", "ItemValueAdornerComponent", "onBlur", "onFocusOut", "ItemValueWrapperViewModel", "button", "add", "isNew", "isDraggable", "dragTooltip", "itemkey", "isDragging", "isDragDropGhost", "isDragDropMoveDown", "isDragDropMoveUp", "ImageItemValueAdornerComponent", "preventDragHandler", "ImageItemValueWrapperViewModel", "itemsRoot", "renderNewItemControls", "addButton", "addButtonCss", "chooseNewFile", "showChooseButtonAsIcon", "addFileTitle", "chooseImageText", "placeholder", "isItemInList", "imageStyle", "getIsNewItemSingle", "renderedImageWidth", "renderedImageHeight", "canRenderControls", "selectFileTitle", "removeFileTitle", "onDragStart", "onDrop", "onDragEnter", "onDragOver", "onDragLeave", "MatrixCellAdornerComponent", "prevIsSelected", "MatrixCellWrapperViewModel", "column", "_a", "cell", "templateData", "_b", "controls", "isSupportCellEditor", "editQuestion", "selectContext", "onMouseOut", "SurveyResults", "SurveyResultsModel", "surveyResultsText", "selectTableClick", "surveyResultsTableText", "isTableSelected", "selectJsonClick", "surveyResultsJsonText", "isJsonSelected", "renderResultAsText", "renderResultAsTable", "resultViewType", "resultText", "resultsTitle", "resultsDisplayValue", "renderRows", "resultData", "rows", "push", "SurveyResultsByRow", "toggle", "paddingLeft", "textMargin", "isNode", "left", "<PERSON><PERSON><PERSON><PERSON>", "collapsed", "locTitle", "displayValue", "SurveyCreatorToolboxTool", "ToolboxToolViewModel", "parentModel", "isCompact", "toolbox", "needSeparator", "showCategoryTitles", "persist", "SurveyCreatorToolboxItem", "banner", "renderedCss", "SurveyCreatorToolboxItemGroup", "subitemsButtonIcon", "SurveyCreatorToolboxCategory", "category", "class", "empty", "header", "renderCategoryHeader", "renderCategoryContent", "canCollapseCategories", "toggleState", "renderButton", "iconClassName", "renderItems", "itemIndex", "ToolboxList", "renderElements", "SearchComponent", "state", "filterString", "onChange", "settings", "environment", "activeElement", "filterStringPlaceholder", "matchCounterText", "searchActionBar", "AdaptiveToolbox", "<PERSON><PERSON><PERSON><PERSON>", "renderCategories", "categories", "renderSearch", "searchButton", "isCompactRendered", "searchItem", "searchManager", "search", "showSearch", "toolboxNoResultsFound", "classNames", "focusOut", "<PERSON><PERSON>", "showInSingleCategory", "SurveyNavigation", "onPropChangedHandler", "sender", "isRendering", "reactiveProps", "indexOf", "val", "propName", "setState", "<PERSON><PERSON><PERSON><PERSON>", "onPropertyChanged", "hasFunc", "location", "isTop", "isShowProgressBarOnTop", "isShowProgressBarOnBottom", "getProgressTypeComponent", "TabButtonComponent", "buttonClassName", "TabControl", "sideBarClassName", "expandCollapseAction", "TabsComponent", "topToolbar", "bottomToolbar", "SideBarDefaultHeader", "SideBarPropertyGridHeader", "objectSelectionAction", "SideBarHeader", "subTitle", "SidebarComponent", "containerRef", "initResizeManager", "resetResizeManager", "display", "renderRoot", "containerStyle", "renderContainer", "pages", "SidebarPage", "headerArea", "componentModel", "sideArea", "sideAreaComponentName", "sideAreaComponentData", "collapseSidebar", "componentName", "TranslationLineSkeleton", "TranslateFromAction", "additionalTitleCss", "additionalTitle", "SurveyLocStringEditor", "onChangedHandler", "changed", "svStringEditorRef", "spellcheck", "locString", "justFocused", "baseModel", "errorText", "onCompositionStart", "onCompositionEnd", "onInput", "onPaste", "onKeyDown", "onKeyUp", "onMouseUp", "done", "edit", "focus", "StringEditorViewModelBase", "setLocString", "getEditorElement", "blurEditor", "blur", "onStringChanged", "contentEditable", "control", "hasHtml", "spell<PERSON>heck", "suppressContentEditableWarning", "counter", "showCharacterCounter", "CharacterCounterComponent", "character<PERSON>ounter", "remainingCharacterCounter", "getCharacterCounterClass", "editableStringRendererName", "QuestionErrorComponent", "SurveyLogicOpertor", "SurveyQuestionDropdown", "q", "initLogicOperator", "locReadOnlyText", "renderLocString", "dropdownListModel", "inputId", "getControlClass", "required", "isRequired", "updateValueOnEvent", "keyhandler", "ariaQuestionRole", "ariaQuestionRequired", "ariaQuestionInvalid", "ariaQuestionErrorMessage", "ariaQuestionExpanded", "ariaQuestionLabel", "ariaQuestionLabelledby", "ariaQuestionControls", "controlValue", "renderEditorButtons", "RendererFactory", "register<PERSON><PERSON>er", "SurveyPageNavigator", "PageNavigatorViewModel", "pagesController", "pageEditMode", "el", "stopItemsContainerHeightObserver", "setScrollableContainer", "isPopupOpened", "togglePageSelector", "pageSelectorCaption", "visibleItems", "SurveyPageNavigatorItem", "unwrap", "SurfacePlaceholder", "placeholderTitleText", "placeholderDescriptionText", "TabDesignerComponent", "denyUpdate", "denyComponentUpdate", "allowUpdate", "allowComponentUpdate", "addDragDropEvents", "onDragClear", "clearDragDropEvents", "getRenderedPages", "renderedPages", "for<PERSON>ach", "createRenderedPage", "newPage", "page2Display", "isGhostPage", "renderPage", "renderNewPage", "pageV", "designerTabClassName", "isToolboxVisible", "clickDesigner", "renderTabContent", "condition", "SurveyHeader", "survey<PERSON><PERSON>er", "allowEditSurveyTitle", "showHeaderInEmptySurvey", "renderPlaceHolderContent", "Object", "surfaceCssVariables", "max<PERSON><PERSON><PERSON>", "rendered<PERSON><PERSON><PERSON>", "tabTools", "renderTabTools", "designerCss", "showSurfaceTools", "pageNavigator", "showPageNavigator", "surfaceToolbar", "showSurfaceToolbar", "TabJsonEditorErrorsComponent", "hasErrors", "List", "errorList", "TabJsonEditorTextareaComponent", "setControl", "textElement", "errors", "input", "<PERSON><PERSON><PERSON>", "readOnly", "aria<PERSON><PERSON><PERSON>", "TabJsonEditorAceComponent", "aceEditorrRef", "init", "ace", "TabLogicAddButtonComponent", "enabled", "TabLogicComponent", "rootClass", "<PERSON><PERSON>iew<PERSON><PERSON>nt", "logicTabClassName", "hasItems", "addLogic", "addNewButton", "itemsSurvey", "SurveySimulator", "mainSimulatorClass", "<PERSON><PERSON><PERSON><PERSON>", "tryToZoom", "onMouseEnter", "device", "activateZoom", "deactivateZoom", "simulatorFrame", "frameWidth", "frameHeight", "deviceWidth", "deviceHeight", "transform", "scale", "TabPreviewTestSurveyAgainComponent", "testAgainAction", "TabPreviewSurveyComponent", "renderSimulator", "simulator", "showResults", "tabContentClassName", "isPageToolbarVisible", "isEmpty", "getBottomToolbar", "PropertyGridPlaceholderComponent", "TabThemeSurveyComponent", "TabTranslationComponent", "stringsHeaderSurvey", "strings<PERSON><PERSON>vey", "ObjectSelectorComponent", "list", "PropertyGridComponent", "SwitcherComponent", "getActionBarItemCss", "args", "getIsTrusted", "ariaChe<PERSON>", "ariaExpanded", "ariaRole", "getSwitcherIconCss", "processEsc", "ItemTemplateComponent", "iconSize", "renderFixButton", "showFixButton", "fixError", "fixButtonTitle", "fixButtonIcon", "ItemTemplateComponent$1", "SurveyQuestionSpinEditor", "SurveyQuestionText", "renderedPlaceholder", "autoComplete", "onBeforeInput", "onInputKeyDown", "a11y_input_ariaRequired", "a11y_input_ariaLabel", "a11y_input_ariaLabelledBy", "a11y_input_ariaDescribedBy", "a11y_input_ariaInvalid", "a11y_input_ariaErrormessage", "getValueCore", "renderedValue", "buttonsContainer", "arrowButton", "onDownButtonClick", "onMouseDown", "onDownButtonMouseDown", "onButtonMouseUp", "onButtonMouseLeave", "decreaseButtonIcon", "onUpButtonClick", "onUpButtonMouseDown", "increaseButtonIcon", "backgroundColor", "SurveyQuestionColor", "renderColorSwatch", "showDropdownAction", "renderDropdownAction", "getSwatchCss", "getSwatchStyle", "swatchIcon", "renderedColorValue", "colorInput", "onColorInputChange", "choicesButtonWrapper", "dropdownAction", "SurveyQuestionFileEditor", "questionFile", "isTextInputReadOnly", "onInputBlur", "onInputChange", "renderFileInput", "fileInput", "ariaRequired", "ariaInvalid", "ariaDescribedBy", "multiple", "inputTitle", "onFileInputChange", "clearButton", "clearButtonCaption", "getIsClearButtonDisabled", "doClean", "clearButtonIcon", "chooseFiles", "getChooseButtonCss", "htmlFor", "chooseButtonCaption", "chooseButtonIcon", "<PERSON><PERSON><PERSON><PERSON>", "SurveyQuestionTextWithReset", "resetButton", "renderResetButton", "getRootClass", "wrappedQuestionTemplate", "resetValueAdorner", "isDisabled", "caption", "resetValue", "resetButtonIcon", "SurveyQuestionBooleanSwitch", "booleanValue", "labelRenderedAriaID", "Version", "checkLibraryVersion"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\TabbedMenu.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\SurveyCreator.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\ModelElement.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\adorners\\Row.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\events.ts", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\adorners\\Question.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\adorners\\QuestionHeader.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\adorners\\QuestionFooter.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\ActionButton.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\adorners\\QuestionBanner.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\adorners\\QuestionDropdown.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\adorners\\QuestionImage.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\adorners\\QuestionRating.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\adorners\\QuestionWidget.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\adorners\\CellQuestion.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\adorners\\CellQuestionDropdown.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\adorners\\Page.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\AddQuestionButton.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\adorners\\Panel.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\LogoImage.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\QuestionLinkValue.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\QuestionEmbeddedSurvey.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\QuestionEditorContent.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\ItemValueWrapper.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\ImageItemValueWrapper.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\MatrixCellWrapper.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\Results.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\toolbox\\ToolboxItem.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\toolbox\\ToolboxItemGroup.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\toolbox\\ToolboxCategory.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\toolbox\\ToolboxList.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\components\\Search.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\toolbox\\AdaptiveToolbox.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\Navigation.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\side-bar\\TabButton.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\side-bar\\TabControl.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\side-bar\\SideBarDefaultHeader.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\side-bar\\SideBarPropertyGridHeader.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\side-bar\\SideBarHeader.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\side-bar\\SideBar.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\tabs\\translation\\TranslationLineSkeleton.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\tabs\\translation\\TranslateFromAction.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\StringEditor.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\QuestionError.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\tabs\\logic-operator.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\PageNavigator.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\components\\SurfacePlaceholder.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\tabs\\Designer.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\tabs\\JsonEditorTextarea.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\tabs\\JsonEditorAce.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\tabs\\Logic.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\tabs\\SurveySimulator.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\tabs\\Preview.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\side-bar\\PropertyGridPlaceholder.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\tabs\\Theme.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\tabs\\translation\\Translation.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\side-bar\\ObjectSelector.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\side-bar\\PropertyGrid.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\Switcher.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\tabs\\JsonErrorItem.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\custom-questions\\SpinEditor.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\custom-questions\\ColorItem.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\custom-questions\\ColorQuestion.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\custom-questions\\FileEditQuestion.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\custom-questions\\TextWithResetQuestion.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\custom-questions\\BooleanSwitch.tsx", "C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\src\\entries\\index-wc.ts"], "sourcesContent": ["import * as React from \"react\";\nimport * as ReactDOM from \"react-dom\";\nimport { TabbedMenuItem, TabbedMenuContainer } from \"survey-creator-core\";\nimport { Base, ResponsivityManager } from \"survey-core\";\nimport { attachKey2click, ReactElementFactory, SurveyElementBase, SvgIcon } from \"survey-react-ui\";\n\nexport interface ITabbedMenuComponentProps {\n  model: TabbedMenuContainer;\n}\n\nexport class TabbedMenuComponent extends SurveyElementBase<\n  ITabbedMenuComponentProps,\n  any\n> {\n  private rootRef: React.RefObject<HTMLDivElement>;\n\n  private get model() {\n    return this.props.model;\n  }\n\n  protected getStateElement(): Base {\n    return this.model;\n  }\n\n  constructor(props) {\n    super(props);\n    this.rootRef = React.createRef();\n  }\n\n  renderElement(): React.JSX.Element {\n    const items = this.model.renderedActions.map((item) => <TabbedMenuItemWrapper item={item} key={item.renderedId} />);\n    return (\n      <div ref={this.rootRef} className=\"svc-tabbed-menu\" role=\"tablist\" style={this.model.getRootStyle()}>\n        {items}\n      </div>\n    );\n  }\n  componentDidUpdate(prevProps: any, prevState: any): void {\n    super.componentDidUpdate(prevProps, prevState);\n    const container: HTMLDivElement = this.rootRef.current;\n    if (!container) return;\n    this.model.initResponsivityManager(container);\n  }\n  componentDidMount() {\n    super.componentDidMount();\n    const container: HTMLDivElement = this.rootRef.current;\n    if (!container) return;\n    this.model.initResponsivityManager(container);\n  }\n  componentWillUnmount() {\n    this.model.resetResponsivityManager();\n    super.componentWillUnmount();\n  }\n}\n\nclass TabbedMenuItemWrapper extends SurveyElementBase<\n  any,\n  any\n> {\n  private ref: React.RefObject<HTMLDivElement>;\n  constructor(props) {\n    super(props);\n    this.ref = React.createRef();\n  }\n\n  private get item(): TabbedMenuItem {\n    return this.props.item;\n  }\n\n  protected getStateElement(): Base {\n    return this.item;\n  }\n\n  renderElement(): React.JSX.Element {\n    let css: string = \"svc-tabbed-menu-item-container\";\n    if (this.item.css) {\n      css += \" \" + this.item.css;\n    }\n    css += (!this.item.isVisible ? \" sv-action--hidden\" : \"\");\n\n    const component = ReactElementFactory.Instance.createElement(\n      this.item.component || \"svc-tabbed-menu-item\",\n      { item: this.item }\n    );\n\n    return (\n      <span key={this.item.id} className={css} ref={this.ref}>\n        <div className=\"sv-action__content\">\n          {component}\n        </div>\n      </span>\n    );\n  }\n  componentDidMount(): void {\n    super.componentDidMount();\n    this.item.updateModeCallback = (mode, callback) => {\n      queueMicrotask(() => {\n        if ((ReactDOM as any)[\"flushSync\"]) {\n          (ReactDOM as any)[\"flushSync\"](() => {\n            this.item.mode = mode;\n          });\n        } else {\n          this.item.mode = mode;\n        }\n        queueMicrotask(() => {\n          callback(mode, this.ref.current);\n        });\n      });\n    };\n    this.item.afterRender();\n  }\n  componentWillUnmount(): void {\n    super.componentWillUnmount();\n    this.item.updateModeCallback = undefined;\n  }\n}\n\nexport interface ITabbedMenuItemComponentProps {\n  item: TabbedMenuItem;\n}\nexport class TabbedMenuItemComponent extends SurveyElementBase<\n  ITabbedMenuItemComponentProps,\n  any\n> {\n  get item(): TabbedMenuItem {\n    return this.props.item;\n  }\n  protected getStateElement(): Base {\n    return this.item;\n  }\n\n  render(): React.JSX.Element {\n    const item = this.item;\n    return (attachKey2click(\n      <div\n        role=\"tab\"\n        id={\"tab-\" + item.id}\n        aria-selected={item.active}\n        aria-controls={\"scrollableDiv-\" + item.id}\n        className={item.getRootCss()}\n        onClick={() => item.action(item)}\n      >\n        {item.hasTitle ? <span className={item.getTitleCss()}>{item.title}</span> : null}\n        {item.hasIcon ? <SvgIcon iconName={item.iconName} className={item.getIconCss()} size={\"auto\"} title={item.tooltip || item.title}></SvgIcon> : null}\n      </div>\n    )\n    );\n  }\n}\n\nReactElementFactory.Instance.registerElement(\n  \"svc-tabbed-menu-item\",\n  (props) => {\n    return React.createElement(TabbedMenuItemComponent, props);\n  }\n);\nexport default TabbedMenuComponent;\n", "import * as React from \"react\";\nimport * as ReactDOM from \"react-dom\";\n\nimport {\n  Base,\n  Question,\n  SurveyError,\n  SurveyModel\n} from \"survey-core\";\nimport {\n  NotifierComponent,\n  SurveyActionBar,\n  ReactElementFactory,\n  ReactQuestionFactory,\n  SurveyElementBase,\n  SurveyLocStringViewer,\n  Survey,\n  SvgBundleComponent,\n  PopupModal\n} from \"survey-react-ui\";\nimport {\n  ICreatorOptions,\n  SurveyCreatorModel,\n  ITabbedMenuItem,\n  assign\n} from \"survey-creator-core\";\nimport { TabbedMenuComponent } from \"./TabbedMenu\";\n\ninterface ISurveyCreatorComponentProps {\n  creator: SurveyCreator;\n  style?: any;\n}\n\nexport class SurveyCreatorComponent extends SurveyElementBase<\n  ISurveyCreatorComponentProps,\n  any\n> {\n  constructor(props: ISurveyCreatorComponentProps) {\n    super(props);\n    this.rootNode = React.createRef();\n  }\n  get creator(): SurveyCreatorModel {\n    return this.props.creator;\n  }\n  protected getStateElement(): Base {\n    return this.creator;\n  }\n  get style(): any {\n    return this.props.style;\n  }\n\n  componentDidUpdate(prevProps: any, prevState: any): void {\n    super.componentDidUpdate(prevProps, prevState);\n    if (this.creator !== prevProps.creator) {\n      if (prevProps.creator) {\n        prevProps.creator.unsubscribeRootElement();\n      }\n      if (this.creator && this.rootNode.current) {\n        this.creator.setRootElement(this.rootNode.current);\n      }\n    }\n  }\n\n  componentDidMount() {\n    super.componentDidMount();\n    this.creator.setRootElement(this.rootNode.current);\n  }\n  componentWillUnmount() {\n    super.componentWillUnmount();\n    this.creator.unsubscribeRootElement();\n  }\n  private rootNode: React.RefObject<HTMLDivElement>;\n\n  renderElement() {\n    const creator: SurveyCreatorModel = this.props.creator;\n    if (creator.isCreatorDisposed) return null;\n    const areaClassName = \"svc-full-container svc-creator__area svc-flex-column\" + (this.props.creator.haveCommercialLicense ? \"\" : \" svc-creator__area--with-banner\");\n    const contentWrapperClassName = \"svc-creator__content-wrapper svc-flex-row\" + (this.props.creator.isMobileView ? \" svc-creator__content-wrapper--footer-toolbar\" : \"\");\n    const fullContainerClassName = \"svc-flex-row svc-full-container\" + (\" svc-creator__side-bar--\" + this.creator.sidebarLocation);\n    const creatorStyles = {};\n    assign(creatorStyles, this.style, this.props.creator.themeVariables);\n    let licenseBanner = null;\n    if (!this.props.creator.haveCommercialLicense) {\n      const htmlValue = { __html: this.props.creator.licenseText };\n      licenseBanner = (\n        <div className=\"svc-creator__banner\">\n          <span className=\"svc-creator__non-commercial-text\" dangerouslySetInnerHTML={htmlValue}></span>\n        </div>\n      );\n    }\n    //AM: width unrecognized by react\n    return (\n      <div className={this.creator.getRootCss()} ref={this.rootNode} style={creatorStyles}>\n        <SvgBundleComponent></SvgBundleComponent>\n        <PopupModal></PopupModal>\n        <div className={areaClassName}>\n          <div className={fullContainerClassName}>\n            <div className=\"svc-flex-column svc-flex-row__element svc-flex-row__element--growing\">\n              <div className=\"svc-top-bar\">\n                {(creator.showTabs ?\n                  <div className=\"svc-tabbed-menu-wrapper\">\n                    <TabbedMenuComponent model={creator.tabbedMenu}></TabbedMenuComponent>\n                  </div> : null)}\n                {(creator.showToolbar ?\n                  <div className=\"svc-toolbar-wrapper\">\n                    <SurveyActionBar model={creator.toolbar}></SurveyActionBar>\n                  </div>\n                  : null)}\n              </div>\n              <div className={contentWrapperClassName}>\n                <div className=\"svc-creator__content-holder svc-flex-column\">\n                  {this.renderActiveTab()}\n                </div>\n              </div>\n              <div className=\"svc-footer-bar\">\n                {(creator.isMobileView ?\n                  <div className=\"svc-toolbar-wrapper\">\n                    <SurveyActionBar model={creator.footerToolbar}></SurveyActionBar>\n                  </div>\n                  : null)}\n              </div>\n            </div>\n            {this.renderSidebar()}\n          </div>\n          {licenseBanner}\n          <NotifierComponent notifier={creator.notifier}></NotifierComponent>\n        </div>\n      </div>\n    );\n  }\n  renderActiveTab() {\n    const creator: SurveyCreatorModel = this.props.creator;\n    for (var i = 0; i < creator.tabs.length; i++) {\n      if (creator.tabs[i].id === creator.activeTab) {\n        return this.renderCreatorTab(creator.tabs[i]);\n      }\n    }\n    return null;\n  }\n  renderCreatorTab(tab: ITabbedMenuItem) {\n    if (tab.visible === false) {\n      return null;\n    }\n    const creator: SurveyCreatorModel = this.props.creator;\n    const component = !!tab.renderTab\n      ? tab.renderTab()\n      : ReactElementFactory.Instance.createElement(tab.componentContent, {\n        creator: creator,\n        survey: creator.survey,\n        data: tab.data.model\n      });\n    const className = \"svc-creator-tab\" + (creator.toolboxLocation == \"right\" ? \" svc-creator__toolbox--right\" : \"\");\n    return (\n      <div\n        role=\"tabpanel\"\n        key={tab.id}\n        id={\"scrollableDiv-\" + tab.id}\n        aria-labelledby={\"tab-\" + tab.id}\n        className={className}\n      >\n        {component}\n      </div>\n    );\n  }\n  renderSidebar() {\n    if (!!this.creator.sidebar) {\n      return ReactElementFactory.Instance.createElement(\"svc-side-bar\", { model: this.creator.sidebar });\n    } else {\n      return null;\n    }\n  }\n}\n\nexport class SurveyCreator extends SurveyCreatorModel {\n  constructor(options: ICreatorOptions = {}, options2?: ICreatorOptions) {\n    super(options, options2);\n  }\n  public render(target: string | HTMLElement) {\n    // eslint-disable-next-line no-console\n    console.error(\"The render method is deprecated. Use SurveyCreatorComponent instead.\");\n  }\n\n  //ISurveyCreator\n  public createQuestionElement(question: Question): React.JSX.Element {\n    return ReactQuestionFactory.Instance.createQuestion(\n      question.isDefaultRendering()\n        ? question.getTemplate()\n        : question.getComponentName(),\n      {\n        question: question,\n        isDisplayMode: question.isReadOnly,\n        creator: this\n      }\n    );\n  }\n  public renderError(\n    key: string,\n    error: SurveyError,\n    cssClasses: any\n  ): React.JSX.Element {\n    return (\n      <div key={key}>\n        <span className={cssClasses.error.icon} aria-hidden=\"true\" />\n        <span className={cssClasses.error.item}>\n          <SurveyLocStringViewer locStr={error.locText} />\n        </span>\n      </div>\n    );\n  }\n  public questionTitleLocation(): string {\n    return this.survey.questionTitleLocation;\n  }\n  public questionErrorLocation(): string {\n    return this.survey.questionErrorLocation;\n  }\n}\n\nReactElementFactory.Instance.registerElement(\"survey-widget\", (props) => {\n  return React.createElement(Survey, props);\n});\n", "import { SurveyElementBase } from \"survey-react-ui\";\n\nexport class CreatorModelElement<P, S> extends SurveyElementBase<P, S> {\n  constructor(props: P) {\n    super(props);\n    this.createModel(props);\n  }\n  shouldComponentUpdate(nextProps: any, nextState: any): boolean {\n    const result = super.shouldComponentUpdate(nextProps, nextState);\n    if (result) {\n      if (this.needUpdateModel(nextProps)) {\n        this.createModel(nextProps);\n      }\n    }\n    return result;\n  }\n  protected createModel(props: any): void { }\n  protected needUpdateModel(nextProps: any): boolean {\n    const names = this.getUpdatedModelProps();\n    if (!Array.isArray(names)) return true;\n    for (var i = 0; i < names.length; i++) {\n      const key = names[i];\n      if (this.props[key] !== nextProps[key]) return true;\n    }\n    return false;\n  }\n  protected getUpdatedModelProps(): string[] {\n    return undefined;\n  }\n}", "import { RowViewModel } from \"survey-creator-core\";\nimport * as React from \"react\";\nimport { Base, QuestionRowModel } from \"survey-core\";\nimport { ReactElementFactory } from \"survey-react-ui\";\nimport { CreatorModelElement } from \"../ModelElement\";\n\ninterface RowWrapperComponentProps {\n  element: React.JSX.Element;\n  componentData: any;\n  row: QuestionRowModel;\n}\n\nexport class RowWrapper extends CreatorModelElement<\n  RowWrapperComponentProps,\n  any\n> {\n  model: RowViewModel;\n  constructor(props: RowWrapperComponentProps) {\n    super(props);\n  }\n  protected createModel(props: any): void {\n    if (!!this.model) {\n      this.model.dispose();\n    }\n    this.model = new RowViewModel(\n      props.componentData.creator,\n      props.row,\n      null\n    );\n  }\n  protected getUpdatedModelProps(): string[] {\n    return [\"row\", \"componentData\"];\n  }\n  protected getStateElement(): Base {\n    return this.model;\n  }\n\n  componentDidMount(): void {\n    super.componentDidMount();\n    this.model.subscribeElementChanges();\n  }\n  componentWillUnmount(): void {\n    this.model.unsubscribeElementChanges();\n    super.componentWillUnmount();\n  }\n\n  render(): React.JSX.Element {\n    return (\n      <div\n        key={\"svc-row-\" + this.props.row.id}\n        className={this.model.cssClasses}\n      >\n        <div className=\"svc-row__drop-indicator svc-row__drop-indicator--top\"></div>\n        <div className=\"svc-row__drop-indicator svc-row__drop-indicator--bottom\"></div>\n        {this.props.element}\n      </div>\n    );\n  }\n}\n\nReactElementFactory.Instance.registerElement(\n  \"svc-row\",\n  (props: RowWrapperComponentProps) => {\n    return React.createElement(RowWrapper, props);\n  }\n);\n", "import { IPortableDragEvent, IPortableMouseEvent } from \"survey-creator-core\";\n\nexport class ReactMouseEvent implements IPortableMouseEvent {\n  constructor(public event: React.MouseEvent<HTMLDivElement, MouseEvent>) {}\n  stopPropagation() {\n    this.event.stopPropagation();\n    //this.event.nativeEvent.stopPropagation();\n    //this.event.nativeEvent.stopImmediatePropagation();\n  }\n  preventDefault() {\n    this.event.preventDefault();\n    //this.event.nativeEvent.preventDefault();\n  }\n  get cancelBubble(): boolean {\n    //return this.event.cancelBubble;\n    return false;\n  }\n  set cancelBubble(value: boolean) {\n    //this.event.cancelBubble = value;\n  }\n  get target(): EventTarget | null {\n    return this.event.target;\n  }\n  get currentTarget(): EventTarget | null {\n    return this.event.currentTarget;\n  }\n  get clientX(): number {\n    return this.event.clientX;\n  }\n  get clientY(): number {\n    return this.event.clientY;\n  }\n  get offsetX(): number {\n    return this.event.nativeEvent.offsetX;\n  }\n  get offsetY(): number {\n    return this.event.nativeEvent.offsetY;\n  }\n}\n\nexport class ReactDragEvent extends ReactMouseEvent\n  implements IPortableDragEvent {\n  constructor(public event: React.DragEvent<HTMLDivElement>) {\n    super(event);\n  }\n\n  get dataTransfer(): DataTransfer {\n    return this.event.dataTransfer;\n  }\n}\n", "import { QuestionAdornerViewModel, toggleHovered } from \"survey-creator-core\";\nimport * as React from \"react\";\nimport { ReactDragEvent, ReactMouseEvent } from \"../events\";\nimport { Base, PanelModel, Question, SurveyElementCore } from \"survey-core\";\nimport {\n  SurveyActionBar,\n  ReactElementFactory,\n  SurveyQuestion,\n  attachKey2click,\n  SvgIcon,\n  Popup,\n  SurveyElementBase,\n  TitleElement\n} from \"survey-react-ui\";\nimport { CreatorModelElement } from \"../ModelElement\";\n\nexport interface QuestionAdornerComponentProps {\n  element: React.JSX.Element;\n  question: Question;\n  componentData: any;\n}\n\nfunction QuestionElementContentFunc(props: { element: React.JSX.Element }): React.ReactElement {\n  return props.element;\n}\n\nconst QuestionElementContent = React.memo(QuestionElementContentFunc);\nQuestionElementContent.displayName = \"QuestionElementContent\";\n\nexport class QuestionAdornerComponent extends CreatorModelElement<\n  QuestionAdornerComponentProps,\n  any\n> {\n  private modelValue: QuestionAdornerViewModel;\n  protected rootRef: React.RefObject<HTMLDivElement>;\n  constructor(props: QuestionAdornerComponentProps) {\n    super(props);\n    this.rootRef = React.createRef();\n  }\n  protected createModel(props: QuestionAdornerComponentProps): void {\n    if (this.model) {\n      this.model.attachToUI(props.question, this.rootRef.current);\n    } else {\n      this.modelValue = this.createQuestionViewModel(props);\n    }\n  }\n  protected createQuestionViewModel(props: any): QuestionAdornerViewModel {\n    return new QuestionAdornerViewModel(\n      props.componentData,\n      props.question,\n      null\n    );\n  }\n  protected getUpdatedModelProps(): string[] {\n    return [\"question\", \"componentData\"];\n  }\n  public get model(): QuestionAdornerViewModel {\n    return this.modelValue;\n  }\n  protected getStateElement(): Base {\n    return this.model;\n  }\n\n  renderElement(): React.JSX.Element {\n    const allowInteractions = this.model.element\n      .isInteractiveDesignElement;\n    const titleForCollapsedState = this.renderQuestionTitle();\n    const content = this.renderContent(allowInteractions);\n    return (\n      <div\n        ref={this.rootRef}\n        data-sv-drop-target-survey-element={this.model.element.name || null}\n        className={this.model.rootCss()}\n        onDoubleClick={e => { allowInteractions && this.model.dblclick(e.nativeEvent); e.stopPropagation(); }}\n        onMouseLeave={e => allowInteractions && this.model.hover(e.nativeEvent, e.currentTarget)}\n        onMouseOver={e => allowInteractions && this.model.hover(e.nativeEvent, e.currentTarget)}\n      >\n        {titleForCollapsedState}\n        {content}\n      </div>\n    );\n  }\n  protected disableTabStop() {\n    return true;\n  }\n  protected renderContent(allowInteractions: boolean): React.JSX.Element {\n    var content = this.model.needToRenderContent ? this.renderElementContent() : null;\n    //if (!allowInteractions) return <>{content}{this.renderFooter()}</>;\n    return attachKey2click(\n      <div\n        className={this.model.css()}\n        onClick={(e) => this.model.select(this.model, new ReactMouseEvent(e))}\n      >\n        <div className=\"svc-question__drop-indicator svc-question__drop-indicator--left\"></div>\n        <div className=\"svc-question__drop-indicator svc-question__drop-indicator--right\"></div>\n        <div className=\"svc-question__drop-indicator svc-question__drop-indicator--top\"></div>\n        <div className=\"svc-question__drop-indicator svc-question__drop-indicator--bottom\"></div>\n        {allowInteractions ? this.renderHeader() : null}\n        {content}\n        {this.model.needToRenderContent ? this.renderFooter() : null}\n      </div>,\n      undefined, { disableTabStop: this.disableTabStop() });\n  }\n  protected renderHeader(): React.JSX.Element {\n    return ReactElementFactory.Instance.createElement(\"svc-question-header\", { model: this.model });\n  }\n  protected renderFooter(): React.JSX.Element {\n    const allowInteractions = this.model.element\n      .isInteractiveDesignElement;\n    return allowInteractions ? ReactElementFactory.Instance.createElement(\"svc-question-footer\", { className: \"svc-question__content-actions\", model: this.model }) : null;\n  }\n  protected renderCarryForwardBanner(): React.JSX.Element {\n    if (!this.model.isBannerShowing) return null;\n    return ReactElementFactory.Instance.createElement(\"svc-question-banner\", this.model.createBannerParams());\n  }\n\n  protected renderQuestionTitle(): React.JSX.Element {\n    if (!this.model.showHiddenTitle) return null;\n    const element = this.model.element as Question | PanelModel;\n    return (\n      <div\n        ref={node => node && (!this.model.renderedCollapsed ?\n          node.setAttribute(\"inert\", \"\") : node.removeAttribute(\"inert\")\n        )} className={this.model.cssCollapsedHiddenHeader} >\n        {(\n          element.hasTitle ?\n            <TitleElement element={element}></TitleElement> :\n            <div\n              className={this.model.cssCollapsedHiddenTitle} >\n              <span className=\"svc-fake-title\">{element.name}</span>\n            </div>\n        )}\n      </div>\n    );\n  }\n\n  protected renderElementContent(): React.JSX.Element {\n    return (\n      <>\n        <QuestionElementContent element={this.props.element} />\n        {this.renderElementPlaceholder()}\n        {this.renderCarryForwardBanner()}\n      </>\n    );\n  }\n  componentDidMount() {\n    super.componentDidMount();\n    this.model.attachToUI(this.props.question, this.rootRef.current);\n  }\n  renderElementPlaceholder(): React.JSX.Element {\n    if (!this.model.isEmptyElement) {\n      return null;\n    }\n    return (\n      <div className=\"svc-panel__placeholder_frame-wrapper\">\n        <div className=\"svc-panel__placeholder_frame\">\n          <div className=\"svc-panel__placeholder\">\n            {this.model.placeholderText}\n          </div>\n        </div>\n      </div>\n    );\n  }\n  componentWillUnmount(): void {\n    super.componentWillUnmount();\n    this.model.detachFromUI();\n  }\n}\n\nReactElementFactory.Instance.registerElement(\n  \"svc-question\",\n  (props: QuestionAdornerComponentProps) => {\n    return React.createElement(QuestionAdornerComponent, props);\n  }\n);\n", "import { QuestionAdornerViewModel, toggleHovered } from \"survey-creator-core\";\nimport * as React from \"react\";\nimport { ReactDragEvent, ReactMouseEvent } from \"../events\";\nimport { Base, Question } from \"survey-core\";\nimport {\n  SurveyActionBar,\n  ReactElementFactory,\n  SurveyElementBase,\n  SurveyQuestion,\n  attachKey2click,\n  SvgIcon,\n  Popup\n} from \"survey-react-ui\";\n\nexport interface QuestionWrapperHeaderProps {\n  className?: string;\n  model: QuestionAdornerViewModel;\n}\n\nexport class QuestionWrapperHeader extends React.Component<QuestionWrapperHeaderProps, any> {\n  render(): React.JSX.Element {\n    if (!this.props.model.allowDragging) return null;\n    return (\n      <div className={\"svc-question__drag-area\"}\n        onPointerDown={(event: any) =>\n          this.props.model.onPointerDown(event)\n        }\n      >\n        <SvgIcon className=\"svc-question__drag-element\" size={\"auto\"} iconName={\"icon-drag-area-indicator_24x16\"}></SvgIcon>\n        <div className=\"svc-question__top-actions\">\n          <SurveyActionBar model={this.props.model.topActionContainer} handleClick={false}></SurveyActionBar>\n        </div>\n      </div>\n    );\n  }\n}\n\nReactElementFactory.Instance.registerElement(\n  \"svc-question-header\",\n  (props: QuestionWrapperHeaderProps) => {\n    return React.createElement(QuestionWrapperHeader, props);\n  }\n);\n", "import { Question<PERSON>dornerViewModel, toggleHovered } from \"survey-creator-core\";\nimport * as React from \"react\";\nimport { ReactDragEvent, ReactMouseEvent } from \"../events\";\nimport {\n  SurveyActionBar,\n  ReactElementFactory,\n} from \"survey-react-ui\";\n\nexport interface QuestionWrapperFooterProps {\n  className?: string;\n  model: QuestionAdornerViewModel;\n}\nexport class Question<PERSON>rapperFooter extends React.Component<QuestionWrapperFooterProps, any> {\n  render(): React.JSX.Element {\n    return (<div className={this.props.className} onFocus={(e) => this.props.model.select(this.props.model, new ReactMouseEvent(e as any))}>\n      <SurveyActionBar model={this.props.model.actionContainer} handleClick={false}></SurveyActionBar>\n    </div>);\n  }\n}\n\nReactElementFactory.Instance.registerElement(\n  \"svc-question-footer\",\n  (props: Question<PERSON>rapperFooterProps) => {\n    return React.createElement(Question<PERSON><PERSON><PERSON>Footer, props);\n  }\n);\n\n", "import * as React from \"react\";\nimport { CssClassBuilder } from \"survey-core\";\nimport { attachKey2click, ReactElementFactory, SurveyElementBase, SvgIcon, } from \"survey-react-ui\";\n\ninterface IActionButtonProps {\n  classes?;\n  click: () => void;\n  selected?: boolean;\n  disabled?: boolean;\n  text?: string;\n  title?: string;\n  iconName?: string;\n  allowBubble?: boolean;\n}\nexport class ActionButton extends SurveyElementBase<IActionButtonProps, any> {\n  renderElement(): React.JSX.Element {\n    const classes = new CssClassBuilder()\n      .append(this.props.classes)\n      .append(\"svc-action-button\")\n      .append(\"svc-action-button--selected\", !!this.props.selected)\n      .append(\"svc-action-button--disabled\", !!this.props.disabled)\n      .toString();\n    if (this.props.iconName) {\n      return this.renderIcon(classes);\n    }\n    return this.renderButtonText(classes);\n  }\n\n  renderButtonText(classes): React.JSX.Element {\n    if (this.props.disabled) {\n      return <span className={classes}>{this.props.text}</span>;\n    }\n    return (\n      <>\n        {attachKey2click(\n          <span\n            role=\"button\"\n            className={classes}\n            onClick={(e) => {\n              if (!this.props.allowBubble) {\n                e.stopPropagation();\n              }\n              this.props.click();\n            }}\n            title={this.props.title}\n          >\n            {this.props.text}\n          </span>\n        )}\n      </>\n    );\n  }\n  renderIcon(classes): React.JSX.Element {\n    classes += \" svc-action-button--icon\";\n    if (this.props.disabled) {\n      return <span className={classes}><SvgIcon size={\"auto\"} iconName={this.props.iconName}></SvgIcon></span>;\n    }\n    return (\n      <>\n        {attachKey2click(\n          <span className={classes}\n            onClick={(e) => {\n              if (!this.props.allowBubble) {\n                e.stopPropagation();\n              }\n              this.props.click();\n            }}\n            title={this.props.title}\n          >\n            <SvgIcon size={\"auto\"} iconName={this.props.iconName}></SvgIcon>\n          </span>\n        )}\n      </>\n    );\n  }\n}\nReactElementFactory.Instance.registerElement(\"svc-action-button\", (props: any) => { return React.createElement(ActionButton, props); });", "import { QuestionBannerParams } from \"survey-creator-core\";\nimport * as React from \"react\";\nimport { ReactElementFactory } from \"survey-react-ui\";\nimport { ActionButton } from \"../ActionButton\";\n\nexport class QuestionBanner extends React.Component<QuestionBannerParams, any> {\n  render(): React.JSX.Element {\n    return (\n      <div className=\"svc-carry-forward-panel-wrapper\"><div className=\"svc-carry-forward-panel\">\n        <span>{this.props.text}{\" \"}</span>\n        <span className=\"svc-carry-forward-panel__link\">\n          <ActionButton click={() => this.props.onClick()} text={this.props.actionText}></ActionButton>\n        </span>\n      </div></div>);\n  }\n}\n\nReactElementFactory.Instance.registerElement(\n  \"svc-question-banner\",\n  (props: QuestionBannerParams) => {\n    return React.createElement(QuestionBanner, props);\n  }\n);\n\n", "import * as React from \"react\";\nimport { QuestionDropdownAdornerViewModel } from \"survey-creator-core\";\nimport { ItemValue, QuestionDropdownModel, QuestionSelectBase, SurveyModel } from \"survey-core\";\nimport { ReactElementFactory, ReactSurveyElementsWrapper } from \"survey-react-ui\";\nimport {\n  QuestionAdornerComponent,\n  QuestionAdornerComponentProps\n} from \"./Question\";\nimport { ActionButton } from \"../ActionButton\";\n\nexport class QuestionDropdownAdornerComponent extends QuestionAdornerComponent {\n  constructor(props: QuestionAdornerComponentProps) {\n    super(props);\n  }\n  protected createQuestionViewModel(props: any): QuestionDropdownAdornerViewModel {\n    return new QuestionDropdownAdornerViewModel(\n      props.componentData,\n      props.question as QuestionDropdownModel,\n      null\n    );\n  }\n  public get dropdownModel(): QuestionDropdownAdornerViewModel {\n    return this.model as QuestionDropdownAdornerViewModel;\n  }\n  public get question(): QuestionSelectBase {\n    return this.dropdownModel.question as QuestionSelectBase;\n  }\n\n  renderElementPlaceholder(): React.JSX.Element {\n    const textStyle = (this.question as any).textStyle;\n    return (\n      <div\n        className=\"svc-question__dropdown-choices--wrapper\">\n        <div>\n          <div className=\"svc-question__dropdown-choices\">\n            {(this.dropdownModel.getRenderedItems() || []).map(\n              (item: ItemValue, index: number) => (\n                <div\n                  className={this.dropdownModel.getChoiceCss()}\n                  key={`editable_choice_${index}`}\n                >\n                  {ReactSurveyElementsWrapper.wrapItemValue(this.question.survey as SurveyModel,\n                    ReactElementFactory.Instance.createElement(\n                      this.dropdownModel.itemComponent,\n                      {\n                        key: item.value,\n                        question: this.question,\n                        cssClasses: this.question.cssClasses,\n                        isDisplayMode: true,\n                        item: item,\n                        textStyle: textStyle,\n                        index: index,\n                        isChecked: this.question.value === item.value\n                      }\n                    ),\n                    this.question,\n                    item\n                  )}\n                </div>\n              )\n            )}\n          </div>\n          {this.dropdownModel.needToCollapse ?\n            <ActionButton\n              click={this.dropdownModel.switchCollapse}\n              text={this.dropdownModel.getButtonText()}\n              allowBubble={true}\n            ></ActionButton> :\n            null\n          }\n        </div>\n      </div>\n    );\n  }\n}\n\nReactElementFactory.Instance.registerElement(\n  \"svc-dropdown-question\",\n  (props: QuestionAdornerComponentProps) => {\n    return React.createElement(QuestionDropdownAdornerComponent, props);\n  }\n);\n", "import {\n  QuestionImageAdornerViewModel,\n  QuestionAdornerViewModel\n} from \"survey-creator-core\";\nimport * as React from \"react\";\nimport { attachKey2click, LoadingIndicatorComponent, ReactElementFactory, ReactQuestionFactory, SvgIcon } from \"survey-react-ui\";\nimport {\n  QuestionAdornerComponent,\n  QuestionAdornerComponentProps\n} from \"./Question\";\nimport { Base } from \"survey-core\";\n\nexport class QuestionImageAdornerComponent extends QuestionAdornerComponent {\n  protected createQuestionViewModel(props: any): QuestionAdornerViewModel {\n    return new QuestionImageAdornerViewModel(\n      props.componentData,\n      props.question as any,\n      null);\n  }\n  public get imageModel(): QuestionImageAdornerViewModel {\n    return this.model as QuestionImageAdornerViewModel;\n  }\n  protected renderHeader(): React.JSX.Element {\n    return (<React.Fragment>\n      <input\n        type=\"file\"\n        aria-hidden=\"true\"\n        tabIndex={-1}\n        accept={this.imageModel.acceptedTypes}\n        className=\"svc-choose-file-input\"\n        style={{\n          position: \"absolute\",\n          opacity: 0,\n          width: \"1px\",\n          height: \"1px\",\n          overflow: \"hidden\"\n        }}\n      />\n\n      {super.renderHeader()}\n    </React.Fragment>);\n  }\n  renderLoadingPlaceholder(): React.JSX.Element {\n    return (<div className=\"svc-image-question__loading-placeholder\">\n      <div className=\"svc-image-question__loading\">\n        <LoadingIndicatorComponent></LoadingIndicatorComponent>\n      </div>\n    </div>);\n  }\n  renderChooseButton(): React.JSX.Element {\n    return (<div className=\"svc-image-question-controls\">\n      {this.model.allowEdit ? attachKey2click(<span\n        className=\"svc-context-button\"\n        onClick={() => this.imageModel.chooseFile(this.imageModel)}\n      >\n        <SvgIcon size={\"auto\"} iconName={\"icon-choosefile\"}></SvgIcon>\n      </span>) : null}\n    </div>);\n  }\n  renderElementPlaceholder(): React.JSX.Element {\n    return this.imageModel.isUploading ? this.renderLoadingPlaceholder() : this.renderChooseButton();\n  }\n  protected getStateElements(): Array<Base> {\n    return [this.model, this.imageModel.filePresentationModel];\n  }\n\n  protected renderElementContent(): React.JSX.Element {\n    if (this.imageModel.isEmptyImageLink) {\n      const fileQuestion = ReactQuestionFactory.Instance.createQuestion(\"file\", {\n        creator: this.imageModel.question.survey,\n        isDisplayMode: false,\n        question: this.imageModel.filePresentationModel\n      });\n      return (<>\n        {fileQuestion}\n      </>);\n    } else {\n      return (\n        <>\n          {this.props.element}\n          {this.renderElementPlaceholder()}\n        </>\n      );\n    }\n  }\n}\n\nReactElementFactory.Instance.registerElement(\n  \"svc-image-question\",\n  (props: QuestionAdornerComponentProps) => {\n    return React.createElement(QuestionImageAdornerComponent, props);\n  }\n);\n", "import * as React from \"react\";\nimport { Base } from \"survey-core\";\nimport { QuestionRatingAdornerViewModel } from \"survey-creator-core\";\nimport { attachKey2click, ReactElementFactory, SvgIcon } from \"survey-react-ui\";\nimport { QuestionAdornerComponentProps } from \"./Question\";\nimport { CreatorModelElement } from \"../ModelElement\";\n\nexport class QuestionRatingAdornerComponent extends CreatorModelElement<QuestionAdornerComponentProps, any> {\n  private modelValue: QuestionRatingAdornerViewModel;\n\n  protected createModel(props: any): void {\n    this.modelValue = this.createQuestionViewModel(props);\n  }\n  protected createQuestionViewModel(props: any): QuestionRatingAdornerViewModel {\n    return new QuestionRatingAdornerViewModel(\n      props.componentData,\n      props.question as any,\n      null\n    );\n  }\n  protected getUpdatedModelProps(): string[] {\n    return [\"question\", \"componentData\"];\n  }\n  public get ratingModel(): QuestionRatingAdornerViewModel {\n    return this.model as QuestionRatingAdornerViewModel;\n  }\n  public get model(): QuestionRatingAdornerViewModel {\n    return this.modelValue;\n  }\n  protected getStateElement(): Base {\n    return this.model;\n  }\n  protected renderElement(): React.JSX.Element {\n    const model = this.ratingModel;\n    return (<>\n      <div className=\"svc-rating-question-content\">\n        <div className={model.controlsClassNames}>\n          {model.allowRemove ? attachKey2click(<span\n            role=\"button\"\n            className={model.removeClassNames}\n            aria-label={model.removeTooltip}\n            onClick={() => model.removeItem(model)}\n          >\n            <SvgIcon size={\"auto\"} iconName={\"icon-remove_16x16\"} title={model.removeTooltip}></SvgIcon>\n          </span>) : null}\n          {model.allowAdd ? attachKey2click(<span\n            role=\"button\"\n            className={model.addClassNames}\n            aria-label={model.addTooltip}\n            onClick={() => model.addItem(model)}\n          >\n            <SvgIcon size={\"auto\"} iconName={\"icon-add_16x16\"} title={model.addTooltip}></SvgIcon>\n          </span>) : null}\n        </div>\n        {this.props.element}\n      </div>\n    </>);\n  }\n}\n\nReactElementFactory.Instance.registerElement(\n  \"svc-rating-question\",\n  (props: QuestionAdornerComponentProps) => {\n    return React.createElement(QuestionRatingAdornerComponent, props);\n  }\n);\nReactElementFactory.Instance.registerElement(\n  \"svc-rating-question-content\",\n  (props: QuestionAdornerComponentProps) => {\n    return React.createElement(QuestionRatingAdornerComponent, props);\n  }\n);\n", "import {\n  QuestionAdornerViewModel\n} from \"survey-creator-core\";\nimport * as React from \"react\";\nimport {\n  QuestionAdornerComponent,\n  QuestionAdornerComponentProps\n} from \"./Question\";\nimport { attachKey2click, ReactElementFactory, SvgIcon } from \"survey-react-ui\";\n\nexport class QuestionWidgetAdornerComponent extends QuestionAdornerComponent {\n  protected createQuestionViewModel(props: any): QuestionAdornerViewModel {\n    return new QuestionAdornerViewModel(\n      props.componentData,\n      props.question as any,\n      null\n    );\n  }\n  public get widgetModel(): QuestionAdornerViewModel {\n    return this.model as QuestionAdornerViewModel;\n  }\n  protected renderElementContent(): React.JSX.Element {\n    return (\n      <div\n        className={\"svc-widget__content\"}\n      >\n        {this.props.element}\n      </div>\n    );\n  }\n}\n\nReactElementFactory.Instance.registerElement(\n  \"svc-widget-question\",\n  (props: QuestionAdornerComponentProps) => {\n    return React.createElement(QuestionWidgetAdornerComponent, props);\n  }\n);\n", "import * as React from \"react\";\nimport { Base, Question } from \"survey-core\";\nimport {\n  attach<PERSON><PERSON>2<PERSON>lick,\n  ReactElementFactory\n} from \"survey-react-ui\";\nimport { CreatorModelElement } from \"../ModelElement\";\nimport { QuestionAdornerViewModel, toggleHovered } from \"survey-creator-core\";\nimport { ReactDragEvent, ReactMouseEvent } from \"../events\";\nimport { QuestionAdornerComponentProps } from \"./Question\";\n\nexport class CellQuestionAdornerComponent extends CreatorModelElement<\n  QuestionAdornerComponentProps,\n  any\n> {\n  model: QuestionAdornerViewModel;\n  protected createModel(props: any): void {\n    this.model = new QuestionAdornerViewModel(\n      props.componentData,\n      props.question,\n      null\n    );\n  }\n  protected getStateElement(): Base {\n    return this.model;\n  }\n  protected getUpdatedModelProps(): string[] {\n    return [\"question\", \"componentData\"];\n  }\n  render(): React.JSX.Element {\n    return (\n      <React.Fragment>\n        <div\n          data-sv-drop-target-survey-element={this.model.element.name}\n          className={\"svc-question__adorner\"}\n        >\n          <div className={\" svc-question__content--in-popup svc-question__content\"}>\n            {this.props.element}\n          </div>\n        </div>\n      </React.Fragment>\n    );\n  }\n}\n\nReactElementFactory.Instance.registerElement(\n  \"svc-cell-question\",\n  (props: QuestionAdornerComponentProps) => {\n    return React.createElement(CellQuestionAdornerComponent, props);\n  }\n);\n", "import { QuestionAdornerViewModel, toggleHovered } from \"survey-creator-core\";\nimport * as React from \"react\";\nimport { ReactDragEvent, ReactMouseEvent } from \"../events\";\nimport { Base, ItemValue, QuestionSelectBase, SurveyModel } from \"survey-core\";\nimport { ReactElementFactory, ReactSurveyElementsWrapper } from \"survey-react-ui\";\nimport { QuestionAdornerComponentProps } from \"./Question\";\nimport { CreatorModelElement } from \"../ModelElement\";\nimport { attachKey2click } from \"survey-react-ui\";\n\nexport class CellQuestionDropdownAdornerComponent extends CreatorModelElement<\n  QuestionAdornerComponentProps,\n  any\n> {\n  model: QuestionAdornerViewModel;\n  protected createModel(props: any): void {\n    this.model = new QuestionAdornerViewModel(\n      props.componentData,\n      props.question,\n      null\n    );\n  }\n  protected getUpdatedModelProps(): string[] {\n    return [\"question\", \"componentData\"];\n  }\n  protected getStateElement(): Base {\n    return this.model;\n  }\n  render(): React.JSX.Element {\n    const question = this.props.question as QuestionSelectBase;\n    const textStyle = (this.props.question as any).textStyle;\n    return (\n      <React.Fragment>\n        <div\n          data-sv-drop-target-survey-element={this.model.element.name}\n          className={\"svc-question__adorner\"}\n        >\n          <div className={\" svc-question__content--in-popup svc-question__content\"}>\n            {this.props.element}\n\n            <div className=\"svc-question__dropdown-choices\">\n              {question.visibleChoices.map(\n                (item: ItemValue, index: number) => (\n                  <div\n                    className=\"svc-question__dropdown-choice\"\n                    key={`editable_choice_${index}`}\n                  >\n                    {ReactSurveyElementsWrapper.wrapItemValue(question.survey as SurveyModel,\n                      ReactElementFactory.Instance.createElement(\n                        \"survey-radiogroup-item\",\n                        {\n                          question: question,\n                          cssClasses: question.cssClasses,\n                          isDisplayMode: true,\n                          item: item,\n                          textStyle: textStyle,\n                          index: index,\n                          isChecked: question.value === item.value\n                        }\n                      ),\n                      question,\n                      item\n                    )}\n                  </div>\n                )\n              )}\n            </div>\n          </div>\n        </div>\n      </React.Fragment>\n    );\n  }\n}\n\nReactElementFactory.Instance.registerElement(\n  \"svc-cell-dropdown-question\",\n  (props: QuestionAdornerComponentProps) => {\n    return React.createElement(CellQuestionDropdownAdornerComponent, props);\n  }\n);\n", "import * as React from \"react\";\nimport { Action, Base, IAction, PageModel, SurveyModel } from \"survey-core\";\nimport {\n  attach<PERSON>ey2<PERSON>lick,\n  Popup,\n  SurveyActionBar,\n  ReactElementFactory,\n  SurveyPage,\n  SvgIcon,\n  LoadingIndicatorComponent\n} from \"survey-react-ui\";\nimport { CreatorModelElement } from \"../ModelElement\";\nimport {\n  SurveyCreatorModel,\n  Page<PERSON><PERSON>ner,\n  SurveyHelper,\n  toggleHovered\n} from \"survey-creator-core\";\nimport { ReactMouseEvent } from \"../events\";\n\ninterface ICreatorSurveyPageComponentProps {\n  creator: SurveyCreatorModel;\n  survey: SurveyModel;\n  page: PageModel;\n  isGhost: boolean;\n}\nconst PageElementContent = React.memo(({ page, survey, creator }: {\n  page: PageModel,\n  survey: SurveyModel,\n  creator: SurveyCreatorModel,\n}) => {\n  return <SurveyPage page={page} survey={survey} creator={creator} />;\n});\nPageElementContent.displayName = \"PageElementContent\";\n\nexport class CreatorSurveyPageComponent extends CreatorModelElement<\n  ICreatorSurveyPageComponentProps,\n  any\n> {\n  private model: PageAdorner;\n  private rootRef: React.RefObject<HTMLDivElement>;\n  constructor(props: ICreatorSurveyPageComponentProps) {\n    super(props);\n    this.rootRef = React.createRef();\n  }\n  protected createModel(props: ICreatorSurveyPageComponentProps): void {\n    if (this.model) {\n      this.model.attachToUI(props.page, this.rootRef.current);\n    }\n    this.model = this.createPageAdorner(props.creator, props.page);\n    this.model.isGhost = this.props.isGhost;\n  }\n  protected createPageAdorner(creator: SurveyCreatorModel, page: PageModel): PageAdorner {\n    return new PageAdorner(creator, page);\n  }\n  shouldComponentUpdate(nextProps: any, nextState: any): boolean {\n    const res = super.shouldComponentUpdate(nextProps, nextState);\n    if (this.model) {\n      this.model.isGhost = this.props.isGhost;\n    }\n    return res;\n  }\n  public componentDidUpdate(prevProps: any, prevState: any): void {\n    super.componentDidUpdate(prevProps, prevState);\n  }\n  protected getUpdatedModelProps(): string[] {\n    return [\"creator\", \"page\"];\n  }\n  protected getStateElement(): Base {\n    return this.model;\n  }\n  componentDidMount() {\n    super.componentDidMount();\n    this.model.attachToUI(this.props.page, this.rootRef.current);\n    this.model.isGhost = this.props.isGhost;\n  }\n  componentWillUnmount() {\n    super.componentWillUnmount();\n    this.model.detachFromUI();\n  }\n  protected canRender(): boolean {\n    return super.canRender();\n  }\n  renderElement(): React.JSX.Element {\n    if (!this.props.page) return null;\n    return (\n      attachKey2click(<div\n        ref={this.rootRef}\n        id={this.props.page.id}\n        data-sv-drop-target-survey-page={this.model.dropTargetName}\n        className={\"svc-page__content \" + this.model.css}\n        onClick={(e) => {\n          return this.model.select(this.model, new ReactMouseEvent(e));\n        }}\n        onDoubleClick={e => this.model.dblclick(e.nativeEvent)}\n        onMouseLeave={(e) => this.model.hover(e.nativeEvent, e.currentTarget)}\n        onMouseOver={(e) => this.model.hover(e.nativeEvent, e.currentTarget)}\n      >\n        <div className=\"svc-question__drop-indicator svc-question__drop-indicator--top\"></div>\n        <div className=\"svc-question__drop-indicator svc-question__drop-indicator--bottom\"></div>\n        {this.renderContent()}\n        {this.renderPlaceholder()}\n        {this.renderHeader()}\n        {this.renderFooter()}\n      </div>)\n    );\n  }\n  protected renderPlaceholder(): React.JSX.Element {\n    if (!this.model.showPlaceholder) return null;\n    return (\n      <div className=\"svc-page__placeholder_frame\">\n        <div className=\"svc-panel__placeholder_frame\">\n          <div className=\"svc-panel__placeholder\">{this.model.placeholderText}</div>\n        </div>\n      </div>\n    );\n  }\n  protected renderContent(): React.JSX.Element {\n    if (!this.model.needRenderContent) {\n      return <div className={\"svc-page__loading-content\"}><LoadingIndicatorComponent></LoadingIndicatorComponent></div>;\n    }\n    return (\n      <PageElementContent\n        page={this.props.page}\n        survey={this.props.survey}\n        creator={this.props.creator}\n      />\n    );\n  }\n  protected renderHeader(): React.JSX.Element {\n    const actions = (<div className=\"svc-page__content-actions\">\n      <SurveyActionBar model={this.model.actionContainer}></SurveyActionBar>\n      {(this.model.topActionContainer.hasActions ? <SurveyActionBar model={this.model.topActionContainer}></SurveyActionBar> : null)}\n    </div>);\n    if (this.model.isGhost || !this.model.allowDragging) {\n      return actions;\n    }\n    return (\n      <div className={\"svc-question__drag-area\"}\n        onPointerDown={(event: any) => this.model.onPointerDown(event)}\n      >\n        <SvgIcon className=\"svc-question__drag-element\" size={\"auto\"} iconName={\"icon-drag-area-indicator_24x16\"}></SvgIcon>\n        {actions}\n      </div>\n    );\n  }\n  protected renderFooter(): React.JSX.Element {\n    return <SurveyActionBar model={this.model.footerActionsBar}></SurveyActionBar>;\n  }\n}\n\nReactElementFactory.Instance.registerElement(\"svc-page\", (props) => {\n  return React.createElement(CreatorSurveyPageComponent, props);\n});\n", "import * as React from \"react\";\nimport { Action, Base, IAction, PageModel, SurveyModel } from \"survey-core\";\nimport {\n  attachKey2click,\n  Popup,\n  SurveyActionBar,\n  ReactElementFactory,\n  SurveyPage,\n  SvgIcon,\n  SurveyElementBase\n} from \"survey-react-ui\";\nimport { ReactMouseEvent } from \"./events\";\n\nexport class AddQuestionButtonComponent extends SurveyElementBase<{ item: Action, buttonClass?: string, renderPopup?: boolean }, any> {\n  public get model() {\n    return this.props.item.data;\n  }\n  protected renderTypeSelector(): React.JSX.Element {\n    const questionTypeSelectorModel = this.model.questionTypeSelectorModel;\n    return attachKey2click(<button\n      type=\"button\"\n      onClick={(e) => {\n        e.stopPropagation();\n        questionTypeSelectorModel.action();\n      }}\n      className=\"svc-element__question-type-selector\"\n      title={this.model.addNewQuestionText}\n      role=\"button\"\n    >\n      <span className=\"svc-element__question-type-selector-icon\">\n        <SvgIcon\n          iconName={questionTypeSelectorModel.iconName}\n          size={\"auto\"}\n          title={this.model.addNewQuestionText}\n        ></SvgIcon>\n      </span>\n      {this.props.renderPopup === undefined || this.props.renderPopup ?\n        <Popup model={questionTypeSelectorModel.popupModel}></Popup>\n        : null}\n    </button>);\n  }\n  protected renderElement(): React.JSX.Element {\n    const addButtonClass = this.props.buttonClass || \"svc-btn\";\n    return <>\n      {attachKey2click(<div\n        className={\"svc-element__add-new-question \" + addButtonClass}\n        onClick={(e) => {\n          e.stopPropagation();\n          this.model.addNewQuestion(this.model, new ReactMouseEvent(e));\n        }}\n        onMouseOver={(e) => this.model.hoverStopper && this.model.hoverStopper(e.nativeEvent, e.currentTarget)}\n      >\n        <SvgIcon\n          className={\"svc-panel__add-new-question-icon\"}\n          iconName={\"icon-add_24x24\"}\n          size={\"auto\"}\n        ></SvgIcon>\n        <span className=\"svc-add-new-item-button__text\">\n          {this.model.addNewQuestionText}\n        </span>\n        {this.props.renderPopup !== false ? this.renderTypeSelector() : null}\n      </div>)}\n      {this.props.renderPopup === false ? this.renderTypeSelector() : null}\n    </>;\n  }\n}\n\nReactElementFactory.Instance.registerElement(\"svc-add-new-question-btn\", (props) => {\n  return React.createElement(AddQuestionButtonComponent, props);\n});", "import { Question<PERSON>dornerViewModel, toggleHovered } from \"survey-creator-core\";\nimport * as React from \"react\";\nimport { ReactDragEvent, ReactMouseEvent } from \"../events\";\nimport { Base, Question } from \"survey-core\";\nimport {\n  SurveyActionBar,\n  ReactElementFactory,\n  SurveyElementBase,\n  SurveyQuestion,\n  attachKey2click,\n  SvgIcon,\n  Popup\n} from \"survey-react-ui\";\nimport { QuestionAdornerComponent, QuestionAdornerComponentProps } from \"./Question\";\nimport { AddQuestionButtonComponent } from \"../AddQuestionButton\";\n\nexport class PanelAdornerComponent extends QuestionAdornerComponent {\n  renderElementPlaceholder(): React.JSX.Element {\n    if (!this.model.isEmptyElement) {\n      return null;\n    }\n    return (\n      <div className=\"svc-panel__placeholder_frame-wrapper\">\n        <div className=\"svc-panel__placeholder_frame\">\n          <div className=\"svc-panel__placeholder\">\n            {this.model.placeholderText}\n          </div>\n          {this.model.showAddQuestionButton ? attachKey2click(<div\n            className=\"svc-panel__add-new-question svc-action-button\"\n            onClick={(e) => {\n              e.stopPropagation();\n              this.model.addNewQuestion();\n            }}\n          >\n            <SvgIcon\n              className={\"svc-panel__add-new-question-icon\"}\n              iconName={\"icon-add_24x24\"}\n              size={\"auto\"}\n            ></SvgIcon>\n            <span className=\"svc-add-new-item-button__text\">\n              {this.model.addNewQuestionText}\n            </span>\n          </div>) : null}\n        </div>\n      </div>\n    );\n  }\n  protected disableTabStop() {\n    return true;\n  }\n  protected renderFooter(): React.JSX.Element {\n    return (<React.Fragment>\n      {!this.model.isEmptyElement && this.model.element.isPanel && this.model.showAddQuestionButton ? (\n        <div className=\"svc-panel__add-new-question-container\">\n          <div className=\"svc-panel__question-type-selector-popup\">\n            <Popup model={this.model.questionTypeSelectorModel.popupModel}></Popup>\n          </div>\n          <div className=\"svc-panel__add-new-question-wrapper\">\n            <AddQuestionButtonComponent item={{ data: this.model } as any} buttonClass={\"svc-action-button\"} renderPopup={false} />\n          </div>\n        </div>) : null}\n\n      {super.renderFooter()}\n    </React.Fragment>);\n  }\n}\n\nReactElementFactory.Instance.registerElement(\n  \"svc-panel\",\n  (props: QuestionAdornerComponentProps) => {\n    return React.createElement(PanelAdornerComponent, props);\n  }\n);\n", "import * as React from \"react\";\nimport { Base } from \"survey-core\";\nimport { ReactElementFactory, LogoImage, SvgIcon, attachKey2click, LoadingIndicatorComponent } from \"survey-react-ui\";\nimport { SurveyCreatorModel, LogoImageViewModel } from \"survey-creator-core\";\nimport { CreatorModelElement } from \"./ModelElement\";\n\ninterface ILogoImageComponentProps {\n  data: SurveyCreatorModel;\n}\n\nexport class LogoImageComponent extends CreatorModelElement<ILogoImageComponentProps, any> {\n  private model: LogoImageViewModel;\n  private rootRef: React.RefObject<HTMLDivElement>;\n  constructor(props: ILogoImageComponentProps) {\n    super(props);\n    this.rootRef = React.createRef();\n  }\n  protected createModel(props: any): void {\n    let prevRoot: HTMLDivElement = null;\n    if (!!this.model) {\n      prevRoot = this.model.root;\n    }\n    this.model = new LogoImageViewModel(props.data, prevRoot);\n  }\n  protected getUpdatedModelProps(): string[] {\n    return [\"data\"];\n  }\n  protected getStateElement(): Base {\n    return this.model;\n  }\n  componentDidMount() {\n    super.componentDidMount();\n    this.model.root = this.rootRef.current;\n  }\n  renderChooseButton() {\n    return attachKey2click(<span\n      className=\"svc-context-button\"\n      onClick={() => this.model.chooseFile(this.model)}\n    >\n      <SvgIcon size={\"auto\"} iconName={\"icon-choosefile\"}></SvgIcon>\n    </span>);\n  }\n  renderClearButton() {\n    return attachKey2click(<span\n      className=\"svc-context-button svc-context-button--danger\"\n      onClick={() => this.model.remove(this.model)}\n    >\n      <SvgIcon size={\"auto\"} iconName={\"icon-clear\"}></SvgIcon>\n    </span>);\n  }\n  renderButtons() {\n    return (<div className=\"svc-context-container svc-logo-image-controls\">\n      {this.renderChooseButton()}\n      {this.renderClearButton()}\n    </div>);\n  }\n  renderImage() {\n    return <div className={this.model.containerCss}>\n      {this.renderButtons()}\n      <LogoImage data={this.props.data.survey}></LogoImage>\n    </div>;\n  }\n  renderPlaceHolder() {\n    return this.model.allowEdit && !this.model.isUploading ? attachKey2click(<div className=\"svc-logo-image-placeholder\" onClick={() => this.model.chooseFile(this.model)}><svg><use xlinkHref=\"#icon-image-48x48\"></use></svg></div>) : null;\n  }\n  renderInput() {\n    return <input aria-hidden=\"true\" type=\"file\" tabIndex={-1} accept={this.model.acceptedTypes} className=\"svc-choose-file-input\" />;\n  }\n  renderLoadingIndicator() {\n    return <div className=\"svc-logo-image__loading\"><LoadingIndicatorComponent></LoadingIndicatorComponent></div>;\n  }\n  render(): React.JSX.Element {\n    let content: React.JSX.Element = null;\n    if (this.model.survey.locLogo.renderedHtml && !this.model.isUploading) {\n      content = this.renderImage();\n    } else if (this.model.isUploading) {\n      content = this.renderLoadingIndicator();\n    } else {\n      content = this.renderPlaceHolder();\n    }\n    return (\n      <div ref={this.rootRef} className=\"svc-logo-image\">\n        {this.renderInput()}\n        {content}\n      </div>\n    );\n  }\n}\n\nReactElementFactory.Instance.registerElement(\"svc-logo-image\",\n  (props: ILogoImageComponentProps) => {\n    return React.createElement(LogoImageComponent, props);\n  }\n);", "import * as React from \"react\";\nimport {\n  ReactQuestionFactory,\n  SurveyQuestionElementBase\n} from \"survey-react-ui\";\nimport { editorLocalization, QuestionLinkValueModel } from \"survey-creator-core\";\nimport { ActionButton } from \"./ActionButton\";\n\nexport class SurveyQuestionLinkValue extends SurveyQuestionElementBase {\n  protected get question(): QuestionLinkValueModel {\n    return this.questionBase as QuestionLinkValueModel;\n  }\n  protected renderClear(): React.JSX.Element {\n    const showClear = (this.questionBase as any).showClear;\n    if (!this.questionBase.isReadOnly && showClear) {\n      return (\n        <ActionButton\n          classes={this.question.linkClearButtonCssClasses}\n          click={() => this.question.doClearClick()}\n          text={editorLocalization.getString(\"pe.clear\")}\n        ></ActionButton>\n      );\n    } else {\n      return null;\n    }\n  }\n  protected renderElement(): React.JSX.Element {\n    return (\n      <>\n        <ActionButton\n          classes={this.question.linkSetButtonCssClasses}\n          click={() => this.question.doLinkClick()}\n          selected={this.question.isSelected}\n          disabled={!this.question.isClickable}\n          text={this.question.linkValueText}\n          title={this.question.tooltip}\n          iconName={this.question.iconName}\n        ></ActionButton>\n        {this.renderClear()}\n      </>\n    );\n  }\n}\n\nReactQuestionFactory.Instance.registerQuestion(\"linkvalue\", (props) => {\n  return React.createElement(SurveyQuestionLinkValue, props);\n});\n", "import * as React from \"react\";\nimport { ReactQuestionFactory, SurveyPage, ISurveyCreator, SurveyQuestionElementBase } from \"survey-react-ui\";\nimport { QuestionEmbeddedSurveyModel } from \"survey-creator-core\";\n\nexport class SurveyElementEmbeddedSurvey extends SurveyQuestionElementBase {\n  protected get embeddedSurvey(): QuestionEmbeddedSurveyModel {\n    return (this.props.element || this.props.question) as QuestionEmbeddedSurveyModel;\n  }\n  protected get creator(): ISurveyCreator {\n    return this.props.creator;\n  }\n  public render(): React.JSX.Element {\n    if (!this.embeddedSurvey) return null;\n    const survey = this.embeddedSurvey.embeddedSurvey;\n    if (!survey || !survey.currentPage) return null;\n    return <SurveyPage\n      survey={survey}\n      page={survey.currentPage}\n      css={survey.css}\n      creator={this.creator}\n    />;\n\n  }\n}\n\nReactQuestionFactory.Instance.registerQuestion(\"embeddedsurvey\", (props) => {\n  return React.createElement(SurveyElementEmbeddedSurvey, props);\n});\n", "import { Base, Question, SurveyError, SurveyModel } from \"survey-core\";\nimport { ISurveyCreator, ReactElementFactory, ReactQuestionFactory, SurveyQuestion } from \"survey-react-ui\";\nimport { SurveyCreatorModel } from \"survey-creator-core\";\nimport * as React from \"react\";\n\ninterface IQuestionEditorContentComponentProps {\n  creator: SurveyCreatorModel;\n  survey: SurveyModel;\n  style: any;\n}\n\nexport class QuestionEditorContentComponent extends React.Component<\n  IQuestionEditorContentComponentProps,\n  any\n> implements ISurveyCreator {\n  get survey() {\n    return this.props.survey;\n  }\n  public createQuestionElement(question: Question): React.JSX.Element {\n    return ReactQuestionFactory.Instance.createQuestion(\n      !question.isDefaultRendering || question.isDefaultRendering()\n        ? question.getTemplate()\n        : question.getComponentName(),\n      {\n        question: question,\n        isDisplayMode: question.isInputReadOnly,\n        creator: this,\n      }\n    );\n  }\n  public questionTitleLocation(): string {\n    return this.survey.questionTitleLocation;\n  }\n  public questionErrorLocation(): string {\n    return this.survey.questionErrorLocation;\n  }\n  renderError(key: string, error: SurveyError, cssClasses: any): React.JSX.Element {\n    return null;\n  }\n\n  render(): React.JSX.Element {\n    const question = this.survey.getAllQuestions()[0];\n    return (\n      <div style={this.props.style} >\n        <SurveyQuestion creator={this} element={question}></SurveyQuestion>\n      </div>\n    );\n  }\n}\n\nReactElementFactory.Instance.registerElement(\n  \"svc-question-editor-content\",\n  (props: IQuestionEditorContentComponentProps) => {\n    return React.createElement(QuestionEditorContentComponent, props);\n  }\n);\n", "import { getLocString, ItemValueWrapperViewModel } from \"survey-creator-core\";\nimport * as React from \"react\";\nimport { QuestionSelectBase, Base, ItemValue } from \"survey-core\";\nimport {\n  attachKey2click,\n  ReactElementFactory,\n  SvgIcon\n} from \"survey-react-ui\";\nimport { CreatorModelElement } from \"./ModelElement\";\n\ninterface ItemValueAdornerComponentProps {\n  element: React.JSX.Element;\n  componentData: any;\n  question: QuestionSelectBase;\n  item: ItemValue;\n}\n\nexport class ItemValueAdornerComponent extends CreatorModelElement<\n  ItemValueAdornerComponentProps,\n  any\n> {\n  model: ItemValueWrapperViewModel;\n  private rootRef: React.RefObject<HTMLDivElement>;\n  constructor(props) {\n    super(props);\n    this.rootRef = React.createRef();\n  }\n  protected createModel(props: any): void {\n    this.model = new ItemValueWrapperViewModel(\n      props.componentData.creator,\n      props.question,\n      props.item\n    );\n  }\n  protected getUpdatedModelProps(): string[] {\n    return [\"question\", \"item\"];\n  }\n  protected getStateElement(): Base {\n    return this.model;\n  }\n  private onBlur = (event: any) => {\n    this.model.onFocusOut(event.nativeEvent);\n  };\n\n  componentDidUpdate(prevProps: any, prevState: any): void {\n    super.componentDidUpdate(prevProps, prevState);\n    this.props.item.setRootElement(this.rootRef.current);\n    if (prevProps.item !== this.props.item && prevProps.item) {\n      prevProps.item.setRootElement(undefined);\n    }\n  }\n\n  componentDidMount(): void {\n    super.componentDidMount();\n    this.props.item.setRootElement(this.rootRef.current);\n  }\n  componentWillUnmount(): void {\n    super.componentWillUnmount();\n    this.props.item.setRootElement(undefined);\n  }\n\n  render(): React.JSX.Element {\n    this.model.item = this.props.item;\n    const button = this.model.allowAdd ? (\n      attachKey2click(<span\n        role=\"button\"\n        className=\"svc-item-value-controls__button svc-item-value-controls__add\"\n        aria-label={this.model.tooltip}\n        onClick={() => {\n          this.model.add(this.model);\n          this.model.isNew = false;\n        }}\n      >\n        <SvgIcon size={\"auto\"} iconName={\"icon-add_16x16\"} title={this.model.tooltip}></SvgIcon>\n      </span>)\n    ) : (\n      <>\n        {\" \"}\n        {this.model.isDraggable ? (\n          <span\n            className=\"svc-item-value-controls__button svc-item-value-controls__drag\"\n          >\n            <SvgIcon className=\"svc-item-value-controls__drag-icon\" size={\"auto\"} iconName={\"icon-drag-24x24\"} title={this.model.dragTooltip}></SvgIcon>\n          </span>\n        ) : null}\n        {this.model.allowRemove ? attachKey2click(<span\n          role=\"button\"\n          className=\"svc-item-value-controls__button svc-item-value-controls__remove\"\n          aria-label={this.model.tooltip}\n          onClick={() => this.model.remove(this.model)}\n        >\n          <SvgIcon size={\"auto\"} iconName={\"icon-remove_16x16\"} title={this.model.tooltip}></SvgIcon>\n        </span>) : null}\n      </>\n    );\n\n    const itemkey = this.props.element.key + (this.model.allowAdd ? \"_new\" : \"\");\n\n    return (\n      <div\n        ref={this.rootRef}\n        className={\n          \"svc-item-value-wrapper\" +\n          (this.model.allowAdd ? \" svc-item-value--new\" : \"\") +\n          (this.model.isDragging ? \" svc-item-value--dragging\" : \"\") +\n          (this.model.isDragDropGhost ? \" svc-item-value--ghost\" : \"\") +\n          (this.model.isDragDropMoveDown ? \" svc-item-value--movedown\" : \"\") +\n          (this.model.isDragDropMoveUp ? \" svc-item-value--moveup\" : \"\")\n        }\n        key={itemkey}\n        data-sv-drop-target-item-value={\n          this.model.isDraggable ? this.model.item.value : undefined\n        }\n        onPointerDown={(event: any) => this.model.onPointerDown(event)}\n      >\n        <div className=\"svc-item-value__ghost\"></div>\n\n        <div className=\"svc-item-value-controls\" onBlur={this.onBlur}>{button}</div>\n\n        <div className={\"svc-item-value__item\"} onClick={(event) => this.model.select(this.model, event.nativeEvent)}>{this.props.element}</div>\n      </div>\n    );\n  }\n}\n\nReactElementFactory.Instance.registerElement(\n  \"svc-item-value\",\n  (props: ItemValueAdornerComponentProps) => {\n    return React.createElement(ItemValueAdornerComponent, props);\n  }\n);\n", "import { ImageItemValueWrapperViewModel } from \"survey-creator-core\";\nimport * as React from \"react\";\nimport { QuestionSelectBase, Base, ImageItemValue, QuestionImagePickerModel } from \"survey-core\";\nimport { LoadingIndicatorComponent, ReactElementFactory, SvgIcon } from \"survey-react-ui\";\nimport {\n  attachKey2click,\n} from \"survey-react-ui\";\nimport { CreatorModelElement } from \"./ModelElement\";\n\ninterface ImageItemValueAdornerComponentProps {\n  element: React.JSX.Element;\n  componentData: any;\n  question: QuestionSelectBase;\n  item: ImageItemValue;\n}\n\nexport class ImageItemValueAdornerComponent extends CreatorModelElement<\n  ImageItemValueAdornerComponentProps,\n  any\n> {\n  model: ImageItemValueWrapperViewModel;\n  private rootRef: React.RefObject<HTMLDivElement>;\n\n  constructor(props: ImageItemValueAdornerComponentProps) {\n    super(props);\n    this.rootRef = React.createRef();\n  }\n  protected createModel(props: any): void {\n    this.model = new ImageItemValueWrapperViewModel(\n      props.componentData.creator,\n      props.question,\n      props.item,\n      null,\n      null\n    );\n  }\n  protected getUpdatedModelProps(): string[] {\n    return [\"question\", \"item\"];\n  }\n  protected getStateElement(): Base {\n    return this.model;\n  }\n\n  protected get question(): QuestionImagePickerModel {\n    return this.props.question as QuestionImagePickerModel;\n  }\n\n  componentDidMount() {\n    super.componentDidMount();\n    this.model.itemsRoot = this.rootRef.current;\n  }\n\n  componentDidUpdate(prevProps, prevState) {\n    super.componentDidUpdate(prevProps, prevState);\n    this.model.itemsRoot = this.rootRef.current;\n  }\n\n  preventDragHandler = (e) => {\n    e.preventDefault();\n  };\n  renderLoadingIndicator() {\n    return <div className=\"svc-image-item-value__loading\"><LoadingIndicatorComponent></LoadingIndicatorComponent></div>;\n  }\n\n  renderNewItemControls() {\n    const addButton = attachKey2click(\n      <span className={this.model.addButtonCss}\n        onClick={() => this.model.chooseNewFile(this.model)}>\n        {this.model.showChooseButtonAsIcon ?\n          <SvgIcon size={\"auto\"} iconName={\"icon-add-lg\"}\n            title={this.model.addFileTitle}></SvgIcon> :\n          <span>{this.model.chooseImageText}</span>\n        }\n      </span>);\n    const placeholder = this.model.showPlaceholder ? <span className=\"svc-image-item-value__placeholder\">{this.model.placeholderText}</span> : null;\n    return <>\n      {placeholder}\n      {addButton}\n    </>;\n  }\n\n  render(): React.JSX.Element {\n    this.model.item = this.props.item;\n    const isNew = !this.props.question.isItemInList(this.props.item);\n    this.model.isNew = isNew;\n    const imageStyle = !this.model.getIsNewItemSingle() ? { width: this.question.renderedImageWidth, height: this.question.renderedImageHeight } : null;\n\n    let content = null;\n    if (isNew || this.model.isUploading) {\n      content = (<>\n        <div className=\"svc-image-item-value__item\">\n          <div className=\"sd-imagepicker__item sd-imagepicker__item--inline\">\n            <label className=\"sd-imagepicker__label\">\n              <div style={imageStyle} className=\"sd-imagepicker__image\">\n                {this.model.isUploading ? this.renderLoadingIndicator() : null}\n              </div>\n            </label>\n          </div>\n        </div>\n\n        {this.model.allowAdd && !this.model.isUploading ?\n          <div className=\"svc-image-item-value-controls\">\n            {this.renderNewItemControls()}\n          </div>\n          : null}\n      </>);\n    } else {\n      content = (\n        <>\n          <div className={\"svc-image-item-value__item\"}>\n            {this.props.element}\n          </div>\n\n          {\n            this.model.isDraggable && this.model.canRenderControls ?\n              <span className=\"svc-context-button svc-image-item-value-controls__drag-area-indicator\"\n                onPointerDown={(event: any) => this.model.onPointerDown(event)}\n              >\n                <SvgIcon size={\"auto\"} iconName={\"icon-drag-24x24\"}></SvgIcon>\n              </span>\n              : null\n          }\n\n          {\n            this.model.canRenderControls ?\n              <div className=\"svc-context-container svc-image-item-value-controls\">\n                {this.model.allowRemove && !this.model.isUploading ? attachKey2click(<span\n                  className=\"svc-context-button\"\n                  onClick={() => this.model.chooseFile(this.model)}\n                >\n                  <SvgIcon role=\"button\" size={\"auto\"} iconName={\"icon-choosefile\"} title={this.model.selectFileTitle}></SvgIcon>\n                </span>) : null}\n                {this.model.allowRemove && !this.model.isUploading ? attachKey2click(<span\n                  className=\"svc-context-button svc-context-button--danger\"\n                  onClick={() => this.model.remove(this.model)}\n                >\n                  <SvgIcon role=\"button\" size={\"auto\"} iconName={\"icon-delete\"} title={this.model.removeFileTitle}></SvgIcon>\n                </span>) : null}\n              </div>\n              : null\n          }\n        </>\n      );\n    }\n\n    return (\n      <div\n        ref={this.rootRef}\n        className={this.model.getRootCss()}\n        key={this.props.element.key}\n        data-sv-drop-target-item-value={\n          this.model.isDraggable ? this.model.item.value : undefined\n        }\n        onPointerDown={(event: any) => this.model.onPointerDown(event)}\n        onDragStart={this.preventDragHandler}\n        onDrop={this.model.onDrop}\n        onDragEnter={this.model.onDragEnter}\n        onDragOver={this.model.onDragOver}\n        onDragLeave={this.model.onDragLeave}\n      >\n        <div className={\"svc-image-item-value-wrapper__ghost\"} style={imageStyle}></div>\n        <div className={\"svc-image-item-value-wrapper__content\"}>\n          <input\n            type=\"file\"\n            aria-hidden=\"true\"\n            tabIndex={-1}\n            accept={this.model.acceptedTypes}\n            className=\"svc-choose-file-input\"\n            style={{\n              position: \"absolute\",\n              opacity: 0,\n              width: \"1px\",\n              height: \"1px\",\n              overflow: \"hidden\"\n            }}\n          />\n          {content}\n        </div>\n      </div>\n    );\n  }\n}\n\nReactElementFactory.Instance.registerElement(\n  \"svc-image-item-value\",\n  (props: ImageItemValueAdornerComponentProps) => {\n    return React.createElement(ImageItemValueAdornerComponent, props);\n  }\n);\n", "import * as React from \"react\";\nimport { QuestionSelectBase, Base, ItemValue, SurveyModel } from \"survey-core\";\nimport { MatrixCellWrapperViewModel, toggleHovered } from \"survey-creator-core\";\nimport {\n  attach<PERSON>ey2<PERSON>lick,\n  ReactElementFactory,\n  SvgIcon\n} from \"survey-react-ui\";\nimport { CreatorModelElement } from \"./ModelElement\";\n\ninterface MatrixCellAdornerComponentProps {\n  element: React.JSX.Element;\n  componentData: any;\n  question: QuestionSelectBase;\n  cell: any;\n}\n\nexport class MatrixCellAdornerComponent extends CreatorModelElement<\n  MatrixCellAdornerComponentProps,\n  any\n> {\n  model: MatrixCellWrapperViewModel;\n  protected createModel(props: any): void {\n    const data = props.componentData;\n    let prevIsSelected = false;\n    if (!!this.model) {\n      prevIsSelected = this.model.isSelected;\n    }\n    this.model = new MatrixCellWrapperViewModel(\n      data.creator,\n      data.element,\n      data.question,\n      data.row,\n      data.column || data.element.cell?.column,\n    );\n    this.model.isSelected = prevIsSelected;\n  }\n  protected getUpdatedModelProps(): string[] {\n    return [\"componentData\"];\n  }\n  componentDidUpdate(prevProps: any, prevState: any) {\n    super.componentDidUpdate(prevProps, prevState);\n    const data = this.props.componentData;\n    this.model.templateData = data.element;\n    this.model.row = data.row;\n    this.model.column = data.column || data.element?.cell?.column;\n    this.model.question = data.question;\n  }\n  protected getStateElement(): Base {\n    return this.model;\n  }\n\n  render(): React.JSX.Element {\n    let controls = null;\n    if (!!this.model.isSupportCellEditor) {\n      controls = <div className=\"svc-matrix-cell__question-controls\">\n        {attachKey2click(<span className=\"svc-matrix-cell__question-controls-button svc-context-button\" onClick={(event: any) => this.model.editQuestion(this.model, event)}>\n          <SvgIcon size={\"auto\"} iconName={\"icon-edit\"}></SvgIcon>\n        </span>)}\n      </div>;\n    }\n\n    return (\n      <div\n        className={\"svc-matrix-cell\"}\n        tabIndex={-1}\n        key={this.props.element.key}\n        onClick={(e: any) => !this.props.question && this.model.selectContext(this.model, e)}\n        onMouseOut={e => this.model.hover(e.nativeEvent, e.currentTarget)}\n        onMouseOver={e => this.model.hover(e.nativeEvent, e.currentTarget)}\n      >\n        <div className={\"svc-matrix-cell--selected\" + (this.model.isSelected ? \" svc-visible\" : \"\")}></div>\n\n        {this.props.element}\n\n        {controls}\n      </div>\n    );\n  }\n}\n\nReactElementFactory.Instance.registerElement(\n  \"svc-matrix-cell\",\n  (props: MatrixCellAdornerComponentProps) => {\n    return React.createElement(MatrixCellAdornerComponent, props);\n  }\n);\n", "import * as React from \"react\";\nimport { Base, SurveyModel } from \"survey-core\";\nimport { SurveyResultsItemModel, SurveyResultsModel } from \"survey-creator-core\";\nimport { attach<PERSON>ey2click, SurveyLocStringViewer, SvgIcon } from \"survey-react-ui\";\nimport { ActionButton } from \"./ActionButton\";\nimport { CreatorModelElement } from \"./ModelElement\";\n\ninterface ISurveyResultsProps {\n  survey: SurveyModel;\n}\nexport class SurveyResults extends CreatorModelElement<\n  ISurveyResultsProps,\n  any\n> {\n  model: SurveyResultsModel;\n  protected createModel(props: any): void {\n    if (this.props.survey) {\n      this.model = new SurveyResultsModel(props.survey);\n    }\n  }\n  protected getUpdatedModelProps(): string[] {\n    return [\"survey\"];\n  }\n  protected getStateElement(): Base {\n    return this.model;\n  }\n  render(): React.JSX.Element {\n    if (!this.model) {\n      return null;\n    }\n    return (\n      <div className=\"svd-test-results\">\n        <div className=\"svd-test-results__content\">\n          <div className=\"svd-test-results__header\">\n            <div className=\"svd-test-results__header-text\">{this.model.surveyResultsText}</div>\n            <div className=\"svd-test-results__header-types\">\n              <ActionButton\n                click={() => this.model.selectTableClick()}\n                text={this.model.surveyResultsTableText}\n                selected={this.model.isTableSelected}\n                disabled={false}\n              ></ActionButton>\n              <ActionButton\n                click={() => this.model.selectJsonClick()}\n                text={this.model.surveyResultsJsonText}\n                selected={this.model.isJsonSelected}\n                disabled={false}\n              ></ActionButton>\n            </div>\n          </div>\n          {this.renderResultAsText()}\n          {this.renderResultAsTable()}\n        </div>\n      </div>\n    );\n  }\n  renderResultAsText(): React.JSX.Element {\n    if (this.model.resultViewType !== \"text\") {\n      return null;\n    }\n    return (\n      <div className=\"svd-test-results__text svd-light-bg-color\">\n        <div>{this.model.resultText}</div>\n      </div>\n    );\n  }\n  renderResultAsTable(): React.JSX.Element {\n    if (this.model.resultViewType !== \"table\") {\n      return null;\n    }\n    return (\n      <div className=\"svd-test-results__table svd-light-bg-color\">\n        <table>\n          <thead>\n            <tr className=\"svd-light-background-color\">\n              <th key={1} className=\"svd-dark-border-color\">\n                {this.model.resultsTitle}\n              </th>\n              <th key={2} className=\"svd-dark-border-color\">\n                {this.model.resultsDisplayValue}\n              </th>\n            </tr>\n          </thead>\n          <tbody>{SurveyResults.renderRows(this.model.resultData)}</tbody>\n        </table>\n      </div>\n    );\n  }\n  static renderRows(data: Array<any>): Array<React.JSX.Element> {\n    const rows = [];\n    for (var i = 0; i < data.length; i++) {\n      rows.push(<SurveyResultsByRow key={i + 1} row={data[i]} />);\n    }\n    return rows;\n  }\n}\n\nexport class SurveyResultsByRow extends CreatorModelElement<any, any> {\n  private get row(): SurveyResultsItemModel {\n    return this.props.row;\n  }\n\n  protected getStateElement(): Base {\n    return this.row;\n  }\n\n  render(): React.JSX.Element {\n    return (\n      <>\n        {attachKey2click(<tr onClick={() => this.row.toggle()}>\n          <td key={1}\n            style={{ paddingLeft: this.row.textMargin }}\n            className=\"svd-dark-border-color\">\n\n            {this.row.isNode ? (\n              <span\n                style={{ left: this.row.markerMargin }}\n                className={\"svd-test-results__marker \" + (this.row.collapsed ? \"\" : \"svd-test-results__marker--expanded\")}>\n                <SvgIcon\n                  iconName={\"icon-expand_16x16\"}\n                  size={16}\n                ></SvgIcon>\n              </span>\n            ) : null}\n\n            {this.row.question ? <SurveyLocStringViewer locStr={this.row.question.locTitle} /> : <span>{this.row.title}</span>}\n          </td>\n          <td key={2} className={this.row.isNode ? \"svd-test-results__node-value\" : \"svd-dark-border-color\"}>\n            {this.row.getString(this.row.displayValue)}\n          </td>\n        </tr>)}\n        {this.row.isNode && !this.row.collapsed ? SurveyResults.renderRows(this.row.data) : null}\n      </>\n    );\n  }\n}\n", "import {\n  SurveyCreatorModel,\n  editorLocalization,\n  IQuestionToolboxItem,\n  QuestionToolboxItem\n} from \"survey-creator-core\";\nimport { CSSProperties, createElement } from \"react\";\nimport * as React from \"react\";\nimport * as ReactDOM from \"react-dom\";\nimport { ToolboxToolViewModel } from \"survey-creator-core\";\nimport {\n  Action,\n  ActionContainer,\n  Base,\n  SurveyModel\n} from \"survey-core\";\nimport {\n  attachKey2click,\n  ReactElementFactory,\n  SvgIcon\n} from \"survey-react-ui\";\nimport { CreatorModelElement } from \"../ModelElement\";\n\nexport interface ISurveyCreatorToolboxItemProps {\n  creator: SurveyCreatorModel;\n  item: QuestionToolboxItem;\n  model: ToolboxToolViewModel;\n  parentModel: ActionContainer;\n  isCompact: boolean;\n}\nexport interface ISurveyCreatorToolboxToolProps {\n  creator: SurveyCreatorModel;\n  item: QuestionToolboxItem;\n  parentModel: ActionContainer;\n  isCompact: boolean;\n}\n\nexport class SurveyCreatorToolboxTool extends CreatorModelElement<\n  ISurveyCreatorToolboxToolProps,\n  any\n> {\n  model: ToolboxToolViewModel;\n  rootRef: React.RefObject<HTMLDivElement>;\n  constructor(props) {\n    super(props);\n    this.rootRef = React.createRef();\n  }\n  protected createModel(props: any): void {\n    this.model = new ToolboxToolViewModel(props.item, props.creator, props.parentModel);\n  }\n  protected getUpdatedModelProps(): string[] {\n    return [\"creator\", \"item\"];\n  }\n  public get item() {\n    return this.props.item;\n  }\n  public get creator() {\n    return this.props.creator;\n  }\n  public get isCompact() {\n    return this.props.isCompact;\n  }\n\n  protected getStateElement(): Base {\n    return (this.item as any);\n  }\n\n  render(): React.JSX.Element {\n    const item = this.item;\n    const itemComponent = ReactElementFactory.Instance.createElement(\n      this.model.itemComponent,\n      {\n        item: item,\n        creator: this.creator,\n        parentModel: this.creator.toolbox,\n        model: this.model,\n        isCompact: this.isCompact\n      }\n    );\n    return (\n      <div className={item.css} key={item.id} ref={this.rootRef}>\n        {(item.needSeparator && !this.creator.toolbox.showCategoryTitles) ? (\n          <div className=\"svc-toolbox__category-separator\"></div>\n        ) : null}\n        <div className=\"svc-toolbox__tool-content sv-action__content\"\n          onPointerDown={(event: any) => {\n            event.persist();\n            this.model.onPointerDown(event);\n          }}\n        >\n          {itemComponent}\n        </div>\n      </div>\n    );\n  }\n  componentWillUnmount(): void {\n    super.componentWillUnmount();\n    this.item.updateModeCallback = undefined;\n  }\n  componentDidMount(): void {\n    super.componentDidMount();\n    this.item.updateModeCallback = (mode, callback) => {\n      queueMicrotask(() => {\n        if ((ReactDOM as any)[\"flushSync\"]) {\n          (ReactDOM as any)[\"flushSync\"](() => {\n            this.item.mode = mode;\n          });\n        } else {\n          this.item.mode = mode;\n        }\n        queueMicrotask(() => {\n          callback(mode, this.rootRef.current);\n        });\n      });\n    };\n    this.item.afterRender();\n  }\n}\n\nexport class SurveyCreatorToolboxItem extends CreatorModelElement<\n  ISurveyCreatorToolboxItemProps,\n  any\n> {\n  constructor(props) {\n    super(props);\n  }\n  protected getUpdatedModelProps(): string[] {\n    return [\"creator\", \"item\"];\n  }\n  public get item() {\n    return this.props.item;\n  }\n  public get creator() {\n    return this.props.creator;\n  }\n  public get model() {\n    return this.props.model;\n  }\n  protected getStateElement(): Base {\n    return this.model;\n  }\n  render(): React.JSX.Element {\n    const banner = (this.props.isCompact ?\n      <span className=\"svc-toolbox__item-banner\"\n        onClick={(event: any) => {\n          event.persist();\n          this.model.click(event);\n        }}>\n        <SvgIcon size={\"auto\"} iconName={this.item.iconName} className=\"svc-toolbox__item-icon\" title={this.item.tooltip}></SvgIcon>\n        <span>{this.item.title}</span>\n      </span>\n      :\n      null\n    );\n    const item = attachKey2click(\n      <div\n        className={this.item.renderedCss}\n        tabIndex={0}\n        role=\"button\"\n        aria-label={this.item.tooltip}\n        onClick={(event: any) => {\n          event.persist();\n          this.model.click(event);\n        }}\n      >\n        <span className=\"svc-toolbox__item-container\">\n          {!!this.item.iconName ? <SvgIcon size={\"auto\"} iconName={this.item.iconName} className=\"svc-toolbox__item-icon\"></SvgIcon> : null}\n        </span>\n        {(this.props.isCompact ?\n          null\n          :\n          <span className=\"svc-toolbox__item-title\">{this.item.title}</span>\n        )}\n      </div>);\n    return (\n      <>\n        {item}\n        {banner}\n      </>\n    );\n  }\n}\n\nReactElementFactory.Instance.registerElement(\"svc-toolbox-item\", (props) => {\n  return createElement(SurveyCreatorToolboxItem, props);\n});\n", "import * as React from \"react\";\nimport { Base } from \"survey-core\";\nimport { Popup, ReactElementFactory, SvgIcon } from \"survey-react-ui\";\nimport { CreatorModelElement } from \"../ModelElement\";\nimport { ISurveyCreatorToolboxItemProps, SurveyCreatorToolboxItem } from \"./ToolboxItem\";\n\nexport class SurveyCreatorToolboxItemGroup extends CreatorModelElement<ISurveyCreatorToolboxItemProps, any> {\n  constructor(props) {\n    super(props);\n  }\n  protected getUpdatedModelProps(): string[] {\n    return [\"creator\", \"item\"];\n  }\n  public get item() {\n    return this.props.item;\n  }\n  public get model() {\n    return this.props.model;\n  }\n  public get creator() {\n    return this.props.creator;\n  }\n  public get isCompact() {\n    return this.props.isCompact;\n  }\n  public get parentModel() {\n    return this.props.parentModel;\n  }\n\n  protected getStateElement(): Base {\n    return this.item;\n  }\n  render(): React.JSX.Element {\n    return <>\n      <SurveyCreatorToolboxItem item={this.item} creator={this.creator} model={this.model} parentModel={this.parentModel} isCompact={this.isCompact} ></SurveyCreatorToolboxItem >\n      <div className=\"svc-toolbox__item-submenu-button\"\n        onMouseOver={(event: any) => this.model.onMouseOver(this.item, event)}\n        onMouseLeave={(event: any) => this.model.onMouseLeave(this.item, event)}\n      >\n        <SvgIcon size={\"auto\"} iconName={this.item.subitemsButtonIcon} ></SvgIcon>\n        <Popup model={this.item.popupModel} />\n      </div>\n    </>;\n  }\n}\n\nReactElementFactory.Instance.registerElement(\"svc-toolbox-item-group\", (props) => {\n  return React.createElement(SurveyCreatorToolboxItemGroup, props);\n});\n", "import * as React from \"react\";\nimport { Base } from \"survey-core\";\nimport { attachKey2click, ReactElementFactory, SurveyElementBase, SvgIcon } from \"survey-react-ui\";\nimport { QuestionToolbox, QuestionToolboxCategory } from \"survey-creator-core\";\nimport { SurveyCreatorToolboxTool } from \"./ToolboxItem\";\n\nexport interface ISurveyCreatorToolboxCategoryProps {\n  category: QuestionToolboxCategory;\n  toolbox: QuestionToolbox;\n}\n\nexport class SurveyCreatorToolboxCategory extends SurveyElementBase<ISurveyCreatorToolboxCategoryProps, any> {\n\n  public get category() {\n    return this.props.category;\n  }\n  public get toolbox() {\n    return this.props.toolbox;\n  }\n  public get class() {\n    return \"svc-toolbox__category\" +\n      (this.category.collapsed ? \" svc-toolbox__category--collapsed\" : \"\") +\n      (this.category.empty ? \" svc-toolbox__category--empty\" : \"\");\n  }\n\n  protected getStateElement(): Base {\n    return (this.category as any);\n  }\n\n  render(): React.JSX.Element {\n    const header = this.renderCategoryHeader();\n    const items = this.renderCategoryContent();\n    return (\n      <div className={this.class} key={this.category.name}>\n        <div className=\"svc-toolbox__category-header-wrapper\">\n          {header}\n        </div>\n        {items}\n      </div>\n    );\n  }\n\n  renderCategoryHeader(): React.JSX.Element {\n    let className = \"svc-toolbox__category-header\";\n    if (this.toolbox.canCollapseCategories) {\n      className += \" svc-toolbox__category-header--collapsed\";\n    }\n    return attachKey2click(\n      <div className={className} onClick={e => this.category.toggleState()}>\n        <span className=\"svc-toolbox__category-title\">{this.category.title}</span>\n        {this.renderButton()}\n      </div>\n    );\n  }\n\n  renderButton(): React.JSX.Element {\n    if (!this.toolbox.canCollapseCategories) return null;\n\n    const iconName = this.category.iconName;\n    return (<div className=\"svc-toolbox__category-header__controls\">\n      <SvgIcon className={this.category.iconClassName} iconName={iconName} size={\"auto\"}></SvgIcon>\n    </div>);\n  }\n\n  protected renderCategoryContent(): Array<any> {\n    return this.renderItems(this.category.items);\n  }\n\n  renderItems(items: Array<any>, isCompact = false) {\n    return items.map((item, itemIndex) =>\n      <SurveyCreatorToolboxTool item={(item as any)} creator={this.toolbox.creator} parentModel={this.toolbox} isCompact={isCompact} key={\"item\" + itemIndex} ></SurveyCreatorToolboxTool>\n    );\n  }\n}\n\nReactElementFactory.Instance.registerElement(\"svc-toolbox-category\", (props) => {\n  return React.createElement(SurveyCreatorToolboxCategory, props);\n});", "import * as React from \"react\";\nimport { ListModel } from \"survey-core\";\nimport { ReactElementFactory, SurveyElementBase } from \"survey-react-ui\";\nimport { SurveyCreatorToolboxTool } from \"./ToolboxItem\";\nimport { CreatorBase } from \"survey-creator-core\";\n\ninterface IListProps {\n  model: ListModel;\n  creator: CreatorBase;\n}\n\nexport class ToolboxList extends SurveyElementBase<IListProps, any> {\n  constructor(props: any) {\n    super(props);\n  }\n\n  get model(): ListModel {\n    return this.props.model;\n  }\n  get creator(): CreatorBase {\n    return this.props.creator;\n  }\n  getStateElement() {\n    return this.model;\n  }\n\n  render() {\n    if (!this.model || !this.model.renderElements) return null;\n\n    const items = this.renderItems();\n    return (\n      <div className={this.model.cssClasses.root}>\n        {items}\n      </div>\n    );\n  }\n  renderItems() {\n    const items = this.model.renderedActions;\n    return items.map((item, itemIndex) =>\n      <SurveyCreatorToolboxTool item={(item as any)} creator={this.creator} parentModel={this.model} isCompact={false} key={\"item\" + itemIndex} ></SurveyCreatorToolboxTool>\n    );\n  }\n}\n\nReactElementFactory.Instance.registerElement(\"svc-toolbox-list\", (props) => {\n  return React.createElement(ToolboxList, props);\n});\n", "import * as React from \"react\";\nimport { Base, settings } from \"survey-core\";\nimport { SearchManager } from \"survey-creator-core\";\nimport { ReactElementFactory, SurveyActionBar, SurveyElementBase, SvgIcon } from \"survey-react-ui\";\n\ninterface ISearchComponentProps {\n  model: SearchManager;\n}\nexport class SearchComponent extends SurveyElementBase<ISearchComponentProps, any> {\n  protected get model(): SearchManager {\n    return this.props.model;\n  }\n  protected getStateElement(): Base {\n    return this.model;\n  }\n\n  constructor(props: any) {\n    super(props);\n    this.state = {\n      filterString: this.model.filterString || \"\"\n    };\n  }\n\n  renderElement(): React.JSX.Element {\n    if (!this.model.isVisible) return null;\n\n    const onChange = (e: any) => {\n      const { root } = settings.environment;\n      if (e.target === root.activeElement) {\n        this.model.filterString = e.target.value;\n      }\n    };\n    return (\n      <div className=\"svc-search\">\n        <div className=\"svc-search__search-icon\">\n          <SvgIcon iconName=\"icon-search\" size={\"auto\"}></SvgIcon>\n        </div>\n        <input type=\"text\" className=\"svc-search__input\"\n          aria-label={this.model.filterStringPlaceholder}\n          placeholder={this.model.filterStringPlaceholder}\n          value={this.state.filterString}\n          onChange={onChange}></input>\n        <div className=\"svc-search__toolbar\">\n          <div className=\"svc-search__toolbar-counter\">{(this.model as any).matchCounterText}</div>\n          <SurveyActionBar model={this.model.searchActionBar}></SurveyActionBar>\n        </div>\n      </div>);\n  }\n}\n\nReactElementFactory.Instance.registerElement(\"svc-search\", (props) => {\n  return React.createElement(\n    SearchComponent,\n    props as ISearchComponentProps\n  );\n});\n", "import * as React from \"react\";\nimport { Base, VerticalResponsivityManager } from \"survey-core\";\nimport { ReactElementFactory, SurveyElementBase, Scroll } from \"survey-react-ui\";\nimport { SurveyCreatorToolboxTool } from \"./ToolboxItem\";\nimport { SearchComponent } from \"../components/Search\";\nimport { SurveyCreatorToolboxCategory } from \"./ToolboxCategory\";\nimport { SurveyCreatorModel } from \"survey-creator-core\";\nexport interface ISurveyCreatorToolboxProps {\n  model: SurveyCreatorModel;\n}\nexport class AdaptiveToolbox extends SurveyElementBase<ISurveyCreatorToolboxProps, any> {\n  private rootRef: React.RefObject<HTMLDivElement>;\n\n  constructor(props: ISurveyCreatorToolboxProps) {\n    super(props);\n    this.rootRef = React.createRef();\n  }\n\n  componentDidUpdate(prevProps: any, prevState: any): void {\n    super.componentDidUpdate(prevProps, prevState);\n    const container = this.rootRef.current;\n    if (container) {\n      this.toolbox.afterRender(container);\n    }\n  }\n\n  componentDidMount() {\n    super.componentDidMount();\n    const container = this.rootRef.current;\n    if (container) {\n      this.toolbox.afterRender(container);\n    }\n  }\n  componentWillUnmount() {\n    this.toolbox.beforeDestroy();\n    super.componentWillUnmount();\n  }\n  public get creator() {\n    return this.props.model;\n  }\n  public get toolbox() {\n    return this.creator.toolbox;\n  }\n  protected getStateElement(): Base {\n    return this.toolbox;\n  }\n\n  renderItems(items: Array<any>, isCompact = false): Array<React.JSX.Element> {\n    return items.map((item, itemIndex) => {\n      return <SurveyCreatorToolboxTool item={(item as any)} creator={this.creator} parentModel={this.toolbox} isCompact={isCompact} key={item.renderedId} ></SurveyCreatorToolboxTool>;\n    });\n  }\n\n  renderCategories() {\n    return this.toolbox.categories.map((category, index) => {\n      return <SurveyCreatorToolboxCategory category={category} toolbox={this.toolbox} key={\"category\" + index} ></SurveyCreatorToolboxCategory>;\n    });\n  }\n  renderSearch() {\n    const searchButton = this.toolbox.isCompactRendered ?\n      <>\n        <SurveyCreatorToolboxTool item={this.toolbox.searchItem as any} creator={this.creator} parentModel={this.toolbox} isCompact={this.toolbox.isCompactRendered} key={\"searchitem\"} ></SurveyCreatorToolboxTool>\n      </> :\n      null;\n    return (<div className=\"svc-toolbox__search-container\">\n      {searchButton}\n      <SearchComponent model={this.toolbox.searchManager}></SearchComponent>\n      <div className=\"svc-toolbox__category-separator svc-toolbox__category-separator--search\"></div>\n    </div>);\n  }\n\n  render(): React.JSX.Element {\n    const search = this.toolbox.showSearch ? this.renderSearch() : null;\n    const placeholder = this.toolbox.showPlaceholder ? <div className=\"svc-toolbox__placeholder\">{this.toolbox.toolboxNoResultsFound}</div> : null;\n    return (\n      <div ref={this.rootRef} className={this.toolbox.classNames} style={this.toolbox.getRootStyle()}>\n        <div onBlur={(e) => this.toolbox.focusOut(e)} className=\"svc-toolbox__panel\">\n          {search}\n          {placeholder}\n          <Scroll>\n            {(this.toolbox.showInSingleCategory) ?\n              (<div className=\"svc-toolbox__category\">\n                {this.renderItems(this.toolbox.renderedActions, this.toolbox.isCompactRendered)}\n              </div>)\n              : this.renderCategories()\n            }\n          </Scroll>\n        </div>\n      </div>\n    );\n  }\n}\n\nReactElementFactory.Instance.registerElement(\"svc-toolbox\", (props) => {\n  return React.createElement(AdaptiveToolbox, props);\n});\n", "import { SurveyModel } from \"survey-core\";\nimport { ReactElementFactory, SurveyElementBase } from \"survey-react-ui\";\n\nexport class SurveyNavigation extends SurveyElementBase<any, any> {\n  componentDidMount() {\n    super.componentDidMount();\n    this.setHandler();\n  }\n  componentDidUpdate(prevProps: any, prevState: any) {\n    super.componentDidUpdate(prevProps, prevState);\n    this.setHandler();\n  }\n  private setHandler() {\n    if (\n      !this.survey ||\n      this.survey.onPropertyChanged.hasFunc(this.onPropChangedHandler)\n    )\n      return;\n    this.survey.onPropertyChanged.add(this.onPropChangedHandler);\n  }\n  private onPropChangedHandler = (sender: any, options: any): any => {\n    if (this.isRendering) return;\n    const reactiveProps = [\n      \"showProgressBar\",\n      \"progressBarType\",\n      \"currentPageValue\"\n    ];\n    if (reactiveProps.indexOf(options.name) < 0) return;\n    var val: any = {};\n    for (var i = 0; i < reactiveProps.length; i++) {\n      var propName = reactiveProps[i];\n      val[propName] = this.survey[propName];\n    }\n    this.setState(val);\n  };\n  componentWillUnmount() {\n    super.componentWillUnmount();\n    if (this.survey) {\n      this.survey.onPropertyChanged.remove(this.onPropChangedHandler);\n    }\n  }\n\n  protected get survey(): SurveyModel {\n    return this.props.survey;\n  }\n  protected get location(): string {\n    return this.props.location;\n  }\n  protected get isTop(): boolean {\n    return this.location == \"top\";\n  }\n  protected canRender(): boolean {\n    return this.isTop\n      ? this.survey.isShowProgressBarOnTop\n      : this.survey.isShowProgressBarOnBottom;\n  }\n  renderElement(): React.JSX.Element {\n    return ReactElementFactory.Instance.createElement(\n      this.survey.getProgressTypeComponent(),\n      { survey: this.survey, css: this.survey.css, isTop: this.isTop }\n    );\n  }\n}\n", "\nimport * as React from \"react\";\nimport { Base } from \"survey-core\";\nimport { attachKey2click, SurveyElementBase, SvgIcon } from \"survey-react-ui\";\nimport { MenuButton } from \"survey-creator-core\";\n\nexport class Tab<PERSON>uttonComponent extends SurveyElementBase<{ model: MenuButton }, any> {\n  constructor(props: { model: MenuButton }) {\n    super(props);\n  }\n\n  protected getStateElement(): Base {\n    return this.props.model;\n  }\n\n  protected renderElement(): React.JSX.Element | null {\n    const model = this.props.model;\n    if (!model.visible) return null;\n    const button = attachKey2click(\n      <div\n        className={model.buttonClassName}\n        title={model.tooltip}\n        onClick={() => { model.action(); }}\n      >\n        <div className=\"svc-menu-action__icon\">\n          <div className=\"svc-menu-action__icon-container\">\n            <SvgIcon iconName={model.iconName} size={\"auto\"}></SvgIcon>\n          </div>\n        </div>\n      </div>, model\n    );\n    return (\n      <div className=\"svc-menu-action\">\n        {button}\n      </div>\n    );\n  }\n}\n", "import * as React from \"react\";\nimport { Action, ActionContainer, Base } from \"survey-core\";\nimport { SurveyElementBase, ReactElementFactory, Scroll } from \"survey-react-ui\";\nimport { MenuButton, TabControlModel } from \"survey-creator-core\";\nimport { TabButtonComponent } from \"./TabButton\";\n\ninterface ITabControlProps {\n  model?: TabControlModel;\n}\n\nclass TabControl extends SurveyElementBase<any, any> {\n  constructor(props: ITabControlProps) {\n    super(props);\n  }\n\n  protected getStateElement(): Base {\n    return this.props.model;\n  }\n\n  public canRender(): boolean {\n    if (!this.props.model) return false;\n    return super.canRender();\n  }\n  renderElement() {\n    return (\n      <div className={this.props.model.sideBarClassName}>\n        <div className=\"svc-sidebar-tabs__top-container\">\n          <div className=\"svc-sidebar-tabs__collapse-button\">\n            <TabButtonComponent model={this.props.model.expandCollapseAction} ></TabButtonComponent>\n          </div>\n          <div className=\"svc-sidebar-tabs__separator\">\n            <div></div>\n          </div>\n          <Scroll>\n            <div className=\"svc-sidebar-tabs__items\">\n              <TabsComponent model={this.props.model.topToolbar}></TabsComponent>\n            </div>\n          </Scroll>\n        </div>\n        <div className=\"svc-sidebar-tabs__bottom-container\">\n          <div className=\"svc-sidebar-tabs__items\">\n            <TabsComponent model={this.props.model.bottomToolbar}></TabsComponent>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n}\n\nReactElementFactory.Instance.registerElement(\"svc-tab-control\", (props) => {\n  return React.createElement(TabControl, props);\n});\n\nclass TabsComponent extends SurveyElementBase<{ model: ActionContainer }, any> {\n  constructor(props: { model: ActionContainer }) {\n    super(props);\n  }\n\n  protected getStateElement(): Base {\n    return this.props.model;\n  }\n\n  protected renderElement(): React.JSX.Element | null {\n    return <>{this.props.model.actions.map(\n      (item: Action, itemIndex: number) => <TabButtonComponent model={item as MenuButton} key={\"item\" + itemIndex}></TabButtonComponent>\n    )}</>;\n  }\n}\n", "import * as React from \"react\";\nimport { Base } from \"survey-core\";\nimport { SurveyElementBase, ReactElementFactory, SurveyActionBar } from \"survey-react-ui\";\nimport { SidebarHeaderModel } from \"survey-creator-core\";\n\ninterface ISideBarHeaderProps {\n  model: SidebarHeaderModel;\n}\n\nexport class SideBarDefaultHeader extends SurveyElementBase<ISideBarHeaderProps, any> {\n  get model(): SidebarHeaderModel {\n    return this.props.model;\n  }\n\n  protected getStateElement(): Base | null {\n    return this.model;\n  }\n\n  renderElement(): React.JSX.Element {\n    const title = !!this.model.title ? (\n      <div className=\"svc-side-bar__container-title\">{this.model.title}</div>\n    ) : null;\n    return (\n      <div className=\"svc-side-bar__container-header\">\n        <div className=\"svc-side-bar__container-actions\">\n          <SurveyActionBar model={this.model.toolbar}></SurveyActionBar>\n        </div>\n        {title}\n      </div>\n    );\n  }\n}\n\nReactElementFactory.Instance.registerElement(\"svc-side-bar-default-header\", (props) => {\n  return React.createElement(SideBarDefaultHeader, props);\n});\n", "import * as React from \"react\";\nimport { Base, getActionDropdownButtonTarget } from \"survey-core\";\nimport { SurveyElementBase, ReactElementFactory, Popup, attachKey2click } from \"survey-react-ui\";\nimport { MenuButton } from \"survey-creator-core\";\n\ninterface ISideBarPropertyGridHeaderProps {\n  model: MenuButton;\n}\n\nclass SideBarPropertyGridHeader extends SurveyElementBase<ISideBarPropertyGridHeaderProps, any> {\n  get objectSelectionAction(): MenuButton {\n    return this.props.model;\n  }\n\n  protected getStateElement(): Base | null {\n    return this.objectSelectionAction;\n  }\n\n  renderElement(): React.JSX.Element {\n    const button = attachKey2click(\n      <div className={this.objectSelectionAction.buttonClassName} onClick={() => this.objectSelectionAction.action()}>\n        <div className=\"svc-sidebar__header-caption\">\n          <span className=\"svc-sidebar__header-title\">{this.objectSelectionAction.title}</span>\n          <span className=\"svc-sidebar__header-subtitle\">{this.objectSelectionAction.tooltip}</span>\n        </div>\n      </div>, this.props.model\n    );\n\n    return (\n      <div className=\"svc-sidebar__header svc-sidebar__header--tabbed\">\n        <div className=\"svc-sidebar__header-container svc-sidebar__header-container--with-subtitle\">\n          <div className=\"svc-sidebar__header-content\">\n            {button}\n            <Popup model={this.objectSelectionAction.popupModel}></Popup>\n          </div>\n        </div>\n      </div>\n    );\n  }\n}\n\nReactElementFactory.Instance.registerElement(\"svc-side-bar-property-grid-header\", (props) => {\n  return React.createElement(SideBarPropertyGridHeader, props);\n});", "import * as React from \"react\";\nimport { Base } from \"survey-core\";\nimport { SidebarHeaderModel } from \"survey-creator-core\";\nimport { SurveyElementBase, ReactElementFactory } from \"survey-react-ui\";\n\ninterface ISideBarHeaderProps {\n  model: SidebarHeaderModel;\n}\n\nclass SideBarHeader extends SurveyElementBase<ISideBarHeaderProps, any> {\n  get model(): SidebarHeaderModel {\n    return this.props.model;\n  }\n\n  protected getStateElement(): Base | null {\n    return this.model;\n  }\n\n  renderElement(): React.JSX.Element {\n    return (\n      <div className=\"svc-side-bar__container-header svc-sidebar__header-container\">\n        {(this.model.subTitle) ?\n          <div className=\"svc-sidebar__header-caption\">\n            <span className=\"svc-sidebar__header-title\">{this.model.title}</span>\n            <span className=\"svc-sidebar__header-subtitle\">{this.model.subTitle}</span>\n          </div>\n          : <div className=\"svc-side-bar__container-title\">{this.model.title}</div>\n        }\n      </div>);\n  }\n}\n\nReactElementFactory.Instance.registerElement(\"svc-side-bar-header\", (props) => {\n  return React.createElement(SideBarHeader, props);\n});", "import * as React from \"react\";\nimport { SidebarPageModel, SidebarModel } from \"survey-creator-core\";\nimport { Base } from \"survey-core\";\nimport { SurveyElementBase, ReactQuestionFactory, ReactElementFactory } from \"survey-react-ui\";\n\ninterface ISidebarComponentProps {\n  model: SidebarModel;\n}\n\nexport class SidebarComponent extends SurveyElementBase<ISidebarComponentProps, any> {\n  private containerRef: React.RefObject<HTMLDivElement>;\n\n  get model(): SidebarModel {\n    return this.props.model;\n  }\n\n  constructor(props: ISidebarComponentProps) {\n    super(props);\n    this.containerRef = React.createRef();\n  }\n\n  protected getStateElement(): Base {\n    return this.model;\n  }\n\n  componentDidMount() {\n    super.componentDidMount();\n    this.model.initResizeManager(this.containerRef.current);\n  }\n  componentWillUnmount() {\n    super.componentWillUnmount();\n    this.model.resetResizeManager();\n  }\n  public canRender(): boolean {\n    if (!this.model) return false;\n    return super.canRender();\n  }\n\n  renderElement(): React.JSX.Element {\n    const style = { display: this.model.renderRoot ? \"\" : \"none\" };\n    const containerStyle = { display: this.model.renderContainer ? \"\" : \"none\" };\n    const items = this.model.pages.map((page) => <SidebarPage page={page} key={page.id} />);\n    const headerArea = ReactElementFactory.Instance.createElement(this.model.header.component, { model: this.model.header.componentModel });\n    let sideArea = null;\n    if (this.model.sideAreaComponentName) {\n      sideArea = ReactElementFactory.Instance.createElement(this.model.sideAreaComponentName, { model: this.model.sideAreaComponentData });\n    }\n\n    return (\n      <div className={this.model.rootCss} style={style}>\n        <div className=\"svc-side-bar__shadow\" onClick={() => this.model.collapseSidebar()} style={containerStyle}></div>\n        <div className=\"svc-flex-row svc-side-bar__wrapper\">\n          <div className=\"svc-side-bar__container-wrapper\" style={containerStyle}>\n            <div ref={this.containerRef} className=\"svc-side-bar__container\">\n              {headerArea}\n              <div className=\"svc-side-bar__container-content\">{items}</div>\n            </div>\n          </div>\n          {sideArea}\n        </div>\n      </div>\n    );\n  }\n}\n\nclass SidebarPage extends SurveyElementBase<any, any> {\n  private get page(): SidebarPageModel {\n    return this.props.page;\n  }\n\n  protected getStateElement(): Base {\n    return this.page;\n  }\n\n  renderElement(): React.JSX.Element {\n    if (!this.page.visible) return null;\n\n    const component = ReactElementFactory.Instance.createElement(this.page.componentName, { model: this.page.componentData });\n\n    return component;\n  }\n}\n\nReactQuestionFactory.Instance.registerQuestion(\"svc-side-bar-page\", (props) => {\n  return React.createElement(SidebarPage, props);\n});\n\nexport default SidebarComponent;\n\nReactElementFactory.Instance.registerElement(\"svc-side-bar\", (props) => {\n  return React.createElement(SidebarComponent, props);\n});", "import * as React from \"react\";\nimport { ReactElementFactory } from \"survey-react-ui\";\n\nexport class TranslationLineSkeleton extends React.Component<any, any> {\n  render() {\n    return (\n      <div className=\"sd-translation-line-skeleton\">\n      </div>\n    );\n  }\n}\n\nReactElementFactory.Instance.registerElement(\"sd-translation-line-skeleton\", (props: any) => {\n  return React.createElement(TranslationLineSkeleton, props);\n});\n", "import * as React from \"react\";\nimport { Action, Base } from \"survey-core\";\nimport { SurveyElementBase, SvgIcon, attachKey2click, ReactElementFactory } from \"survey-react-ui\";\n\nexport class TranslateFromAction extends SurveyElementBase<{ item: Action }, any> {\n  get item(): Action {\n    return this.props.item;\n  }\n  protected getStateElement(): Base {\n    return this.item;\n  }\n\n  renderElement() {\n    const item = this.item;\n    return (\n      <div className={item.data.containerCss}>\n        <span className={item.data.additionalTitleCss}>{item.data.additionalTitle}</span>\n        {ReactElementFactory.Instance.createElement(\"sv-action-bar-item-dropdown\", { item: this.item })}\n      </div>\n    );\n  }\n}\n\nReactElementFactory.Instance.registerElement(\"svc-translate-from-action\", (props) => {\n  return React.createElement(TranslateFromAction, props);\n});\n", "import * as React from \"react\";\nimport { LocalizableString, Serializer, JsonObjectProperty, Base } from \"survey-core\";\nimport { CharacterCounterComponent, ReactElementFactory, SurveyElementBase, SvgIcon } from \"survey-react-ui\";\nimport { SurveyCreatorModel, StringEditorViewModelBase, editableStringRendererName } from \"survey-creator-core\";\nimport { CreatorModelElement } from \"./ModelElement\";\n\nexport class SurveyLocStringEditor extends CreatorModelElement<any, any> {\n  private baseModel: StringEditorViewModelBase;\n  private svStringEditorRef: React.RefObject<HTMLDivElement>;\n  constructor(props: any) {\n    super(props);\n    this.state = { changed: 0 };\n    this.svStringEditorRef = React.createRef();\n  }\n  protected createModel(props: any): void {\n    if (this.baseModel) {\n      this.baseModel.dispose();\n    }\n    this.baseModel = new StringEditorViewModelBase(this.locString, this.creator);\n  }\n  protected getUpdatedModelProps(): string[] {\n    return [\"creator\", \"locString\"];\n  }\n  protected get locString(): LocalizableString {\n    return this.props.locStr.locStr;\n  }\n  protected get creator(): SurveyCreatorModel {\n    return this.props.locStr.creator;\n  }\n  private get style(): any {\n    return this.props.style;\n  }\n\n  protected getStateElement(): Base {\n    return this.baseModel;\n  }\n\n  public get errorText(): string {\n    return this.baseModel.errorText;\n  }\n  private onChangedHandler = (sender: any, options: any) => {\n    this.setState({ changed: !!this.state && this.state.changed ? this.state.changed + 1 : 1 });\n  };\n  public componentDidMount() {\n    super.componentDidMount();\n    if (!this.locString) return;\n    this.baseModel.setLocString(this.locString);\n    this.baseModel.getEditorElement = () => this.svStringEditorRef.current;\n    this.baseModel.blurEditor = () => {\n      this.svStringEditorRef.current.blur();\n      this.svStringEditorRef.current.spellcheck = false;\n    };\n    this.baseModel.afterRender();\n    this.locString.onStringChanged.add(this.onChangedHandler);\n    if (this.locString[\"__isEditing\"]) {\n      this.svStringEditorRef.current.focus();\n      // document.execCommand('selectAll', false, null);\n    }\n  }\n  public componentDidUpdate(prevProps: any, prevState: any): void {\n    super.componentDidUpdate(prevProps, prevState);\n    this.baseModel.setLocString(this.locString);\n    this.baseModel.afterRender();\n    this.locString.onStringChanged.add(this.onChangedHandler);\n  }\n  public componentWillUnmount() {\n    super.componentWillUnmount();\n    this.baseModel.detachFromUI();\n    if (!this.locString) return;\n    this.locString.onStringChanged.remove(this.onChangedHandler);\n  }\n  private get placeholder(): string {\n    return this.baseModel.placeholder;\n  }\n  private get contentEditable(): boolean {\n    return this.baseModel.contentEditable;\n  }\n  private onBlur = (event: any) => {\n    if (this.svStringEditorRef.current) {\n      this.svStringEditorRef.current.spellcheck = false;\n    }\n    this.locString[\"__isEditing\"] = false;\n    this.justFocused = false;\n    this.baseModel.onBlur(event.nativeEvent);\n    return this.baseModel.errorText;\n  };\n  private onCompositionStart = (event: any) => {\n    this.baseModel.onCompositionStart(event.nativeEvent);\n  };\n  private onCompositionEnd = (event: any) => {\n    this.baseModel.onCompositionEnd(event.nativeEvent);\n  };\n  private onInput = (event: any) => {\n    this.baseModel.onInput(event.nativeEvent);\n  };\n  private onPaste = (event: any) => {\n    this.baseModel.onPaste(event.nativeEvent);\n  };\n  private justFocused = false;\n  private onFocus = (event: any) => {\n    this.baseModel.onFocus(event.nativeEvent);\n    this.justFocused = true;\n  };\n  private onKeyDown = (event: React.KeyboardEvent<HTMLSpanElement>) => {\n    return this.baseModel.onKeyDown(event.nativeEvent);\n  };\n  private onKeyUp = (event: React.KeyboardEvent<HTMLSpanElement>) => {\n    return this.baseModel.onKeyUp(event.nativeEvent);\n  };\n  private onMouseUp = (event: React.MouseEvent<HTMLSpanElement>): boolean => {\n    return this.baseModel.onMouseUp(event.nativeEvent);\n  };\n  private done = (event: any) => {\n    this.baseModel.done(event);\n    this.locString[\"__isEditing\"] = false;\n  };\n  private edit = (event: any) => {\n    this.svStringEditorRef.current.focus();\n    // document.execCommand('selectAll', false, null);\n    this.locString[\"__isEditing\"] = true;\n    this.baseModel.onClick(event);\n  };\n  private get className() {\n    return this.baseModel.className(this.locString.renderedHtml);\n  }\n  private htmlValue = {\n    __html: this.locString?.renderedHtml\n  };\n  public render(): React.JSX.Element {\n    if (!this.locString) {\n      return null;\n    }\n    let control = null;\n    if (this.locString.hasHtml) {\n      if (this.htmlValue.__html !== this.locString.renderedHtml) {\n        this.htmlValue = { __html: this.locString.renderedHtml };\n      }\n      control = (\n        <span\n          role=\"textbox\"\n          ref={this.svStringEditorRef}\n          className=\"sv-string-editor sv-string-editor--html\"\n          contentEditable={this.contentEditable}\n          spellCheck={false}\n          aria-placeholder={this.placeholder}\n          aria-label={this.placeholder || \"content editable\"}\n          suppressContentEditableWarning={true}\n          tabIndex={this.baseModel.tabIndex}\n          // style={this.style}\n          dangerouslySetInnerHTML={this.htmlValue}\n          onBlur={this.onBlur}\n          onFocus={this.onFocus}\n          onKeyDown={this.onKeyDown}\n          onMouseUp={this.onMouseUp}\n          onClick={this.edit}\n        ></span>\n      );\n    } else {\n      control = (\n        <span\n          role=\"textbox\"\n          ref={this.svStringEditorRef}\n          className=\"sv-string-editor\"\n          contentEditable={this.contentEditable}\n          tabIndex={this.baseModel.tabIndex}\n          spellCheck={false}\n          aria-placeholder={this.placeholder}\n          aria-label={this.placeholder || \"content editable\"}\n          suppressContentEditableWarning={true}\n          // style={this.style}\n          key={this.locString.renderedHtml}\n          onBlur={this.onBlur}\n          onInput={this.onInput}\n          onPaste={this.onPaste}\n          onCompositionStart={this.onCompositionStart}\n          onCompositionEnd={this.onCompositionEnd}\n          onFocus={this.onFocus}\n          onKeyDown={this.onKeyDown}\n          onKeyUp={this.onKeyUp}\n          onMouseUp={this.onMouseUp}\n          onClick={this.edit}\n        >{this.locString.renderedHtml}</span>\n      );\n    }\n    const counter = this.baseModel.showCharacterCounter ? (<CharacterCounterComponent counter={this.baseModel.characterCounter} remainingCharacterCounter={this.baseModel.getCharacterCounterClass}></CharacterCounterComponent>) : null;\n    return (\n      <span className={this.className}>\n        <span className=\"svc-string-editor__content\">\n          <div className=\"svc-string-editor__border svc-string-editor__border--hover\"\n            onClick={this.edit}\n          >\n          </div>\n\n          <div className=\"svc-string-editor__border svc-string-editor__border--focus\"\n            onClick={this.edit}\n          >\n          </div>\n\n          <span className=\"svc-string-editor__input\">\n            {control}\n            <div className=\"svc-string-editor__controls\"\n              onClick={this.edit}>\n            </div>\n            {counter}\n          </span>\n        </span>\n        {this.errorText ? <span className=\"svc-string-editor__error\">{this.errorText}</span> : \"\"}\n      </span>\n    );\n  }\n}\n\nReactElementFactory.Instance.registerElement(\n  editableStringRendererName,\n  (props) => {\n    return React.createElement(SurveyLocStringEditor, props);\n  }\n);\n", "import * as React from \"react\";\nimport { SurveyError } from \"survey-core\";\nimport { ReactElementFactory, SvgIcon } from \"survey-react-ui\";\nimport { SurveyLocStringViewer } from \"survey-react-ui\";\n\nexport interface IQuestionErrorComponentProps {\n  error: SurveyError;\n  cssClasses: any;\n  element: any;\n}\n\nexport class QuestionErrorComponent extends React.Component<IQuestionErrorComponentProps, any> {\n  render(): React.JSX.Element | null {\n    return (\n      <div>\n        <SvgIcon aria-hidden=\"true\" iconName=\"icon-alert_24x24\" size=\"24\" className={this.props.cssClasses.error.icon}></SvgIcon>\n        <span className={this.props.cssClasses.error.item || undefined}>\n          <SurveyLocStringViewer locStr={this.props.error.locText} />\n        </span>\n      </div>\n    );\n  }\n}\n\nReactElementFactory.Instance.registerElement(\"svc-question-error\", (props) => {\n  return React.createElement(QuestionErrorComponent, props);\n});", "import * as React from \"react\";\nimport { QuestionDropdownModel, RendererFactory } from \"survey-core\";\nimport { initLogicOperator } from \"survey-creator-core\";\nimport { SurveyQuestionDropdown, ReactQuestionFactory } from \"survey-react-ui\";\n\nexport class SurveyLogicOpertor extends SurveyQuestionDropdown {\n  constructor(props: any) {\n    super(props);\n  }\n\n  protected renderInput(): React.JSX.Element {\n    const q = this.question as QuestionDropdownModel;\n    initLogicOperator(q);\n    const text = (q.locReadOnlyText) ? this.renderLocString(q.locReadOnlyText) : \"\";\n    const dropdownListModel = this.question.dropdownListModel;\n\n    return (<div\n      id={this.question.inputId}\n      className={q.getControlClass()}\n      tabIndex={this.question.isInputReadOnly ? undefined : 0}\n      // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n      // @ts-ignore\n      disabled={this.question.isInputReadOnly}\n      required={this.question.isRequired}\n      onChange={this.updateValueOnEvent}\n      onInput={this.updateValueOnEvent}\n      onKeyUp={this.keyhandler}\n      role={dropdownListModel.ariaQuestionRole}\n      aria-required={dropdownListModel.ariaQuestionRequired}\n      aria-invalid={dropdownListModel.ariaQuestionInvalid}\n      aria-errormessage={dropdownListModel.ariaQuestionErrorMessage}\n      aria-expanded={dropdownListModel.ariaQuestionExpanded}\n      aria-label={dropdownListModel.ariaQuestionLabel}\n      aria-labelledby={dropdownListModel.ariaQuestionLabelledby}\n      aria-controls={dropdownListModel.ariaQuestionControls}\n    >\n      <div className={this.question.cssClasses.controlValue}>\n        {text}\n      </div>\n    </div>);\n  }\n\n  protected renderEditorButtons(): React.JSX.Element | null {\n    return null;\n  }\n}\n\nReactQuestionFactory.Instance.registerQuestion(\"sv-logic-operator\", (props) => {\n  return React.createElement(SurveyLogicOpertor, props);\n});\n\nRendererFactory.Instance.registerRenderer(\"dropdown\", \"logicoperator\", \"sv-logic-operator\");", "import {\n  Base,\n  IAction,\n  unwrap\n} from \"survey-core\";\nimport {\n  SvgIcon,\n  Popup,\n  attachKey2click\n} from \"survey-react-ui\";\nimport { PageNavigatorViewModel, PagesController } from \"survey-creator-core\";\nimport * as React from \"react\";\nimport { CreatorModelElement } from \"./ModelElement\";\n\ninterface ISurveyPageNavigatorProps {\n  pagesController: PagesController;\n  pageEditMode: string;\n}\n\nexport class SurveyPageNavigator extends CreatorModelElement<\n  ISurveyPageNavigatorProps,\n  any\n> {\n  private model: PageNavigatorViewModel;\n  private containerRef: React.RefObject<HTMLDivElement>;\n\n  constructor(props: ISurveyPageNavigatorProps) {\n    super(props);\n    this.containerRef = React.createRef();\n  }\n  protected createModel(props: any): void {\n    if (this.model) {\n      this.model.dispose();\n    }\n    this.model = new PageNavigatorViewModel(\n      props.pagesController,\n      props.pageEditMode\n    );\n  }\n  protected getUpdatedModelProps(): string[] {\n    return [\"pagesController\", \"pageEditMode\"];\n  }\n  protected getStateElement(): Base {\n    return this.model;\n  }\n  componentDidMount() {\n    super.componentDidMount();\n    if (this.props.pageEditMode !== \"bypage\") {\n      const el = this.containerRef.current as HTMLDivElement;\n      this.model.attachToUI(el);\n    }\n  }\n  componentWillUnmount() {\n    super.componentWillUnmount();\n    this.model.stopItemsContainerHeightObserver();\n    this.model.setScrollableContainer(undefined);\n  }\n  renderElement(): React.JSX.Element {\n    let className = \"svc-page-navigator__selector svc-page-navigator__button\";\n    if (this.model.isPopupOpened)\n      className += \" svc-page-navigator__button--pressed\";\n    return (\n      <div className=\"svc-page-navigator\" ref={this.containerRef} style={{ display: this.model.visible ? \"flex\" : \"none\" }}>\n        <div>\n          {attachKey2click(<div\n            role=\"button\"\n            className={className}\n            onClick={() => this.model.togglePageSelector()}\n            title={this.model.pageSelectorCaption}\n          >\n            <SvgIcon\n              className=\"svc-page-navigator__button-icon\"\n              iconName={this.model.icon}\n              size={\"auto\"}\n              title={this.model.pageSelectorCaption}\n            ></SvgIcon>\n          </div>)}\n          <Popup\n            model={this.model.popupModel}\n          //className=\"svc-page-navigator__popup\"\n          ></Popup>\n        </div>\n        <div>\n          {this.model.visibleItems.map((item) => (\n            <SurveyPageNavigatorItem\n              key={item.id}\n              item={item}\n            ></SurveyPageNavigatorItem>\n          ))}\n        </div>\n      </div>\n    );\n  }\n}\nexport class SurveyPageNavigatorItem extends CreatorModelElement<any, any> {\n  protected getStateElement(): Base {\n    return this.props.item as Base;\n  }\n  renderElement(): React.JSX.Element {\n    const item = this.props.item;\n    let className: string = \"svc-page-navigator-item-content\";\n    if (unwrap(item.active)) {\n      className += \" svc-page-navigator-item--selected\";\n    }\n    if (unwrap(item.disabled)) {\n      className += \" svc-page-navigator-item--disabled\";\n    }\n    return (\n      <div className=\"svc-page-navigator-item\">\n        {attachKey2click(<div\n          role=\"button\"\n          className={className}\n          onClick={(e) => {\n            item.action(item);\n            e.stopPropagation();\n          }}\n        >\n          <div className=\"svc-page-navigator-item__dot\" title={item.title}><div className=\"svc-page-navigator-item__dot-content\"></div></div>\n          <div className=\"svc-page-navigator-item__banner\">\n            <span className=\"svc-page-navigator-item__text\">{item.title}</span>\n            <span className=\"svc-page-navigator-item__dot\"><span className=\"svc-page-navigator-item__dot-content\"></span></span>\n          </div>\n        </div>)}\n      </div>\n    );\n  }\n}\n", "import * as React from \"react\";\nimport { ReactElementFactory } from \"survey-react-ui\";\n\ninterface ISurfacePlaceholderProps {\n  name: string;\n  placeholderTitleText: string;\n  placeholderDescriptionText: string;\n}\n\nexport class SurfacePlaceholder extends React.Component<ISurfacePlaceholderProps, any> {\n  constructor(props: ISurfacePlaceholderProps) {\n    super(props);\n  }\n  render(): React.JSX.Element {\n    return (\n      <div className=\"svc-surface-placeholder\" >\n        <div className={\"svc-surface-placeholder__image svc-surface-placeholder__image--\" + this.props.name}></div >\n        <div className=\"svc-surface-placeholder__text\">\n          <div className=\"svc-surface-placeholder__title\">{this.props.placeholderTitleText}</div>\n          <div className=\"svc-surface-placeholder__description\">{this.props.placeholderDescriptionText}</div>\n        </div>\n      </div>\n    );\n  }\n}\n\nReactElementFactory.Instance.registerElement(\"svc-surface-placeholder\",\n  (props: ISurfacePlaceholderProps) => {\n    return React.createElement(SurfacePlaceholder, props);\n  }\n);", "import * as React from \"react\";\nimport { Base, SurveyModel, PageModel } from \"survey-core\";\nimport {\n  ReactElementFactory,\n  Scroll,\n  SurveyActionBar,\n  SurveyElementBase,\n  SurveyHeader\n} from \"survey-react-ui\";\nimport { SurveyCreatorModel, TabDesignerViewModel } from \"survey-creator-core\";\nimport { SurveyPageNavigator } from \"../PageNavigator\";\nimport { SurfacePlaceholder } from \"../components/SurfacePlaceholder\";\ninterface ITabDesignerComponentProps {\n  data: TabDesignerViewModel;\n}\n\nexport class TabDesignerComponent extends SurveyElementBase<ITabDesignerComponentProps, any> {\n  private get model(): TabDesignerViewModel {\n    return this.props.data;\n  }\n  protected get creator(): SurveyCreatorModel {\n    return this.model.creator;\n  }\n\n  private denyUpdate = () => {\n    this.denyComponentUpdate();\n  };\n\n  private allowUpdate = () => {\n    this.allowComponentUpdate();\n  };\n\n  private addDragDropEvents = () => {\n    this.creator.onDragStart.add(this.denyUpdate);\n    this.creator.onDragClear.add(this.allowUpdate);\n  };\n\n  private clearDragDropEvents = () => {\n    this.creator.onDragStart.remove(this.denyUpdate);\n    this.creator.onDragClear.remove(this.allowUpdate);\n  };\n\n  componentDidMount(): void {\n    super.componentDidMount();\n    this.addDragDropEvents();\n  }\n\n  componentWillUnmount(): void {\n    super.componentWillUnmount();\n    this.clearDragDropEvents();\n    super.componentWillUnmount();\n  }\n\n  protected getStateElements(): Array<Base> {\n    return [this.model, this.model.survey, this.model.pagesController];\n  }\n\n  protected getRenderedPages(): React.JSX.Element[] {\n    const renderedPages = [];\n\n    if (this.creator.pageEditMode !== \"bypage\") {\n      const pages = this.model.pages;\n      pages.forEach((page) => {\n        renderedPages.push(this.createRenderedPage(page, page == this.model.newPage));\n      });\n    } else {\n      const page2Display = this.model.pagesController.page2Display;\n      if (!!page2Display) {\n        renderedPages.push(this.createRenderedPage(page2Display, this.model.newPage === page2Display));\n      }\n    }\n\n    return renderedPages;\n  }\n  protected createRenderedPage(page: PageModel, isGhostPage?: boolean): any {\n    return (\n      <div\n        className={\"svc-page\"}\n        data-sv-drop-target-page={page.name}\n        data-sv-drop-target-survey-element={isGhostPage ? \"newGhostPage\" : page.name}\n        key={page.id}\n      >\n        {this.renderPage(page, isGhostPage)}\n      </div>\n    );\n  }\n  private renderNewPage(className: string, key: string = \"\") {\n    return (\n      <React.Fragment key={key}>\n        <div\n          className={className}\n          data-sv-drop-target-survey-element={\"newGhostPage\"}\n        >\n          {!!this.model.newPage ? this.renderPage(this.model.newPage, true) : null}\n        </div>\n      </React.Fragment>);\n  }\n  protected renderPage(pageV: PageModel, isGhost: boolean): React.JSX.Element {\n    return ReactElementFactory.Instance.createElement(\"svc-page\", { survey: this.creator.survey, page: pageV, creator: this.creator, isGhost });\n  }\n  renderElement(): React.JSX.Element {\n    const designerTabClassName = \"svc-tab-designer \" + this.model.getRootCss();\n\n    return (\n      <React.Fragment>\n        <div className=\"svc-flex-column\">\n          {this.model.isToolboxVisible ? ReactElementFactory.Instance.createElement(\"svc-toolbox\", { model: this.creator }) : null}\n        </div>\n        <div className={designerTabClassName} onClick={() => this.model.clickDesigner()}>\n          <Scroll>\n            <div className=\"svc-tab-designer_content\">\n              {this.model.showPlaceholder ? this.renderPlaceHolder() : this.renderTabContent()}\n            </div>\n          </Scroll>\n        </div>\n      </React.Fragment>\n    );\n  }\n\n  renderHeader(condition: boolean): React.JSX.Element {\n    if (!condition) return null;\n\n    const survey: SurveyModel = this.creator.survey;\n    return (<React.Fragment>\n      <div className=\"svc-designer-header\">\n        <SurveyHeader survey={survey}></SurveyHeader>\n      </div>\n    </React.Fragment>);\n  }\n  renderPlaceHolder(): React.JSX.Element {\n    const surveyHeader = this.renderHeader(this.creator.allowEditSurveyTitle && this.creator.showHeaderInEmptySurvey);\n\n    return (<React.Fragment>\n      {surveyHeader}\n      <div className=\"svc-designer__placeholder-container\" data-sv-drop-target-survey-element={\"newGhostPage\"}>\n        {this.renderPlaceHolderContent()}\n        {this.renderNewPage(\"svc-designer-placeholder-page\")}\n      </div>\n    </React.Fragment>);\n  }\n  renderPlaceHolderContent(): React.JSX.Element {\n    return <SurfacePlaceholder name={\"designer\"} placeholderTitleText={this.model.placeholderTitleText} placeholderDescriptionText={this.model.placeholderDescriptionText} />;\n  }\n  renderTabContent(): React.JSX.Element {\n    const survey: SurveyModel = this.creator.survey;\n    const surveyHeader = this.renderHeader(this.creator.allowEditSurveyTitle);\n    const style: any = { ...this.model.surfaceCssVariables };\n    style.maxWidth = survey.renderedWidth;\n\n    const tabTools = this.renderTabTools();\n\n    return (<React.Fragment>\n      <div className={this.model.designerCss} style={style} >\n        {surveyHeader}\n        {/* <SurveyNavigation survey={survey} location=\"top\" /> */}\n        {this.getRenderedPages()}\n        {/* <SurveyNavigation\n          survey={survey}\n          location=\"bottom\"\n          css={survey.css}\n        /> */}\n      </div>\n      {tabTools}\n    </React.Fragment>);\n  }\n\n  renderTabTools(): React.JSX.Element {\n    if (!this.model.showSurfaceTools) return null;\n\n    const pageNavigator = this.creator.showPageNavigator ?\n      <div className=\"svc-tab-designer__page-navigator\"><SurveyPageNavigator\n        pagesController={this.model.pagesController} pageEditMode={this.model.creator.pageEditMode}\n      ></SurveyPageNavigator></div>\n      : null;\n\n    const surfaceToolbar = this.model.showSurfaceToolbar ?\n      <SurveyActionBar model={this.model.surfaceToolbar} handleClick={false}></SurveyActionBar>\n      : null;\n\n    return <div className=\"svc-tab-designer__tools\">\n      {pageNavigator}\n      {surfaceToolbar}\n    </div>;\n  }\n}\n\nReactElementFactory.Instance.registerElement(\"svc-tab-designer\", (props) => {\n  return React.createElement(\n    TabDesignerComponent,\n    props as ITabDesignerComponentProps\n  );\n});\n", "import { ChangeEvent } from \"react\";\nimport * as React from \"react\";\nimport { Base } from \"survey-core\";\nimport { ReactElementFactory, SurveyElementBase, List } from \"survey-react-ui\";\nimport { TextareaJsonEditorModel, JsonEditorBaseModel } from \"survey-creator-core\";\n\ninterface ITabJsonEditorTextareaComponentProps {\n  data: TextareaJsonEditorModel;\n}\n\ninterface ITabJsonEditorErrorsProps {\n  data: JsonEditorBaseModel;\n}\n\nexport class TabJsonEditorErrorsComponent extends SurveyElementBase<ITabJsonEditorErrorsProps, any> {\n  protected getStateElement(): Base {\n    return this.model;\n  }\n  private get model(): JsonEditorBaseModel {\n    return this.props.data;\n  }\n  renderElement(): React.JSX.Element {\n    return <div className=\"svc-json-editor-tab__errros_list\" style={{ display: this.model.hasErrors ? \"block\" : \"none\" }}>\n      <List model={this.model.errorList} />\n    </div>;\n  }\n}\n\nexport class TabJsonEditorTextareaComponent extends SurveyElementBase<\n  ITabJsonEditorTextareaComponentProps,\n  any\n> {\n  constructor(props) {\n    super(props);\n  }\n  protected getStateElement(): Base {\n    return this.model;\n  }\n  private get model(): TextareaJsonEditorModel {\n    return this.props.data;\n  }\n  renderElement(): React.JSX.Element {\n    const setControl = (el: HTMLTextAreaElement) => {\n      this.model.textElement = el;\n    };\n    const errors = <TabJsonEditorErrorsComponent data={this.model} />;\n    return (\n      <div className=\"svc-creator-tab__content\">\n        <div className=\"svc-json-editor-tab__content\">\n          <textarea\n            ref={input => (setControl(input))}\n            className=\"svc-json-editor-tab__content-area\"\n            value={this.model.text}\n            onChange={(e: ChangeEvent<HTMLTextAreaElement>) =>\n              (this.model.text = e.target.value)\n            }\n            onKeyDown={(e) => this.model.checkKey(e, e)}\n            disabled={this.model.readOnly}\n            aria-label={this.model.ariaLabel}\n          ></textarea>\n          {errors}\n        </div>\n      </div>\n    );\n  }\n}\n\nReactElementFactory.Instance.registerElement(\n  \"svc-tab-json-editor-textarea\",\n  (props: ITabJsonEditorTextareaComponentProps) => {\n    return React.createElement(TabJsonEditorTextareaComponent, props);\n  }\n);\n", "import * as React from \"react\";\nimport { Base } from \"survey-core\";\nimport { ReactElementFactory, SurveyElementBase } from \"survey-react-ui\";\nimport { AceJsonEditorModel } from \"survey-creator-core\";\nimport { TabJsonEditorErrorsComponent } from \"./JsonEditorTextarea\";\n\ninterface ITabJsonEditorAceComponentProps {\n  data: AceJsonEditorModel;\n}\n\nexport class TabJsonEditorAceComponent extends SurveyElementBase<\n  ITabJsonEditorAceComponentProps,\n  any\n> {\n  private aceEditorrRef: React.RefObject<HTMLDivElement>;\n  constructor(props: ITabJsonEditorAceComponentProps) {\n    super(props);\n    this.aceEditorrRef = React.createRef();\n  }\n  protected getStateElement(): Base {\n    return this.model;\n  }\n  private get model(): AceJsonEditorModel {\n    return this.props.data;\n  }\n  componentDidMount() {\n    this.model.init(ace.edit(this.aceEditorrRef.current as HTMLElement));\n  }\n  renderElement(): React.JSX.Element {\n    const errors = <TabJsonEditorErrorsComponent data={this.model} />;\n    return (\n      <div className=\"svc-creator-tab__content\">\n        <div className=\"svc-json-editor-tab__content\">\n          <div\n            className=\"svc-json-editor-tab__ace-editor\"\n            ref={this.aceEditorrRef}\n          ></div>\n          {errors}\n        </div>\n      </div>\n    );\n  }\n}\n\nReactElementFactory.Instance.registerElement(\n  \"svc-tab-json-editor-ace\",\n  (props: ITabJsonEditorAceComponentProps) => {\n    return React.createElement(TabJsonEditorAceComponent, props);\n  }\n);\n", "import { Fragment } from \"react\";\nimport * as React from \"react\";\nimport { Action, Base } from \"survey-core\";\nimport { ReactElementFactory, SurveyElementBase, Survey, attachKey2click } from \"survey-react-ui\";\nimport { SurveyLogicUI } from \"survey-creator-core\";\nimport { SurfacePlaceholder } from \"../components/SurfacePlaceholder\";\n\nexport class TabLogicAddButtonComponent extends SurveyElementBase<any, any> {\n  private get model(): Action {\n    return this.props.button;\n  }\n  protected getStateElement(): Base {\n    return this.model;\n  }\n\n  renderElement(): React.JSX.Element {\n    const buttonClassName = \"svc-logic-tab__content-action svc-btn\" + ((this.model.enabled !== undefined && !this.model.enabled) ? \" svc-logic-tab__content-action--disabled\" : \"\");\n    return attachKey2click(<div\n      role=\"button\"\n      onClick={(e) => {\n        e.stopPropagation();\n        this.model.action();\n      }}\n      className={buttonClassName}\n      title={this.model.title}\n    >\n      <span className=\"svc-btn__text\">\n        {this.model.title}\n      </span>\n    </div>);\n  }\n}\nReactElementFactory.Instance.registerElement(\"svc-tab-logic-add-button\", (props) => {\n  return React.createElement(TabLogicAddButtonComponent, props);\n});\n\nexport class TabLogicComponent extends SurveyElementBase<any, any> {\n  private get model(): SurveyLogicUI {\n    return this.props.data;\n  }\n  protected getStateElement(): Base {\n    return this.model;\n  }\n\n  renderElement(): React.JSX.Element {\n    var logic = this.model;\n    var rootClass = \"svc-creator-tab__content svc-logic-tab\";\n    var content = this.renderViewContent();\n    return <div className={rootClass}>{content}</div>;\n  }\n  private renderViewContent(): React.JSX.Element {\n    const logicTabClassName = \"svc-plugin-tab__content svc-logic-tab svc-logic-tab__content \" + (this.model.hasItems ? \"\" : \"svc-logic-tab--empty\");\n    const addLogic = !this.model.readOnly ? <TabLogicAddButtonComponent button={this.model.addNewButton} /> : undefined;\n    return (\n      <Fragment>\n        <div className={logicTabClassName}>\n          {this.model.hasItems ?\n            (<>\n              <Survey model={this.model.itemsSurvey}></Survey>\n              {addLogic}\n            </>)\n            : (<div className=\"svc-logic-tab__content-empty\" >\n              <SurfacePlaceholder name={\"logic\"} placeholderTitleText={this.model.placeholderTitleText} placeholderDescriptionText={this.model.placeholderDescriptionText} />\n              {addLogic}\n            </div>)\n          }\n        </div>\n      </Fragment >\n    );\n  }\n}\nReactElementFactory.Instance.registerElement(\"svc-tab-logic\", (props) => {\n  return React.createElement(TabLogicComponent, props);\n});\n", "import { Base } from \"survey-core\";\nimport { Survey, SurveyElementBase } from \"survey-react-ui\";\nimport { SurveySimulatorModel } from \"survey-creator-core\";\nimport * as React from \"react\";\n\nexport class SurveySimulator extends SurveyElementBase<any, any> {\n  private get model(): SurveySimulatorModel {\n    return this.props.model;\n  }\n\n  protected getStateElement(): Base {\n    return this.model;\n  }\n\n  renderElement(): React.JSX.Element {\n    const mainSimulatorClass = this.model.getRootCss();\n    if (!this.model.survey) {\n      return <div className={mainSimulatorClass}></div>;\n    }\n    if (this.model.hasFrame) {\n      return (\n        <div\n          className={mainSimulatorClass}\n          onKeyDown={e => this.model.tryToZoom(e, e)}\n          onMouseEnter={this.model.device === \"desktop\" ? null : this.model.activateZoom}\n          onMouseLeave={this.model.device === \"desktop\" ? null : this.model.deactivateZoom}\n        >\n          <div\n            className=\"svd-simulator-wrapper\"\n            id=\"svd-simulator-wrapper\"\n            style={{\n              width: this.model.simulatorFrame.frameWidth + \"px\",\n              height: this.model.simulatorFrame.frameHeight + \"px\"\n            }}\n          >\n            <div\n              className=\"svd-simulator\"\n              style={{\n                width:\n                  this.model.simulatorFrame.deviceWidth + \"px\",\n                height:\n                  this.model.simulatorFrame.deviceHeight + \"px\",\n                transform:\n                  \"scale(\" +\n                  this.model.simulatorFrame.scale +\n                  \") translate(-50%, -50%)\"\n              }}\n            >\n              <div className=\"svd-simulator-content\">\n                <Survey model={this.model.survey}></Survey>\n              </div>\n            </div>\n          </div>\n        </div>\n      );\n    } else {\n      return (\n        <div\n          className={mainSimulatorClass}>\n          <div className=\"svd-simulator-content\">\n            <Survey model={this.model.survey}></Survey>\n          </div>\n        </div>\n      );\n    }\n  }\n}\n", "import * as React from \"react\";\nimport { Action, Base } from \"survey-core\";\nimport {\n  SurveyActionBar,\n  ReactElementFactory,\n  SurveyElementBase,\n  attachKey2click\n} from \"survey-react-ui\";\nimport { TestSurveyTabViewModel } from \"survey-creator-core\";\nimport { SurveySimulator } from \"./SurveySimulator\";\nimport { SurveyResults } from \"../Results\";\nimport { SurfacePlaceholder } from \"../components/SurfacePlaceholder\";\n\nexport class TabPreviewTestSurveyAgainComponent extends SurveyElementBase<any, any> {\n  private get model(): Action {\n    return this.props.model.testAgainAction;\n  }\n  protected getStateElement(): Base {\n    return this.model;\n  }\n\n  renderElement(): React.JSX.Element {\n    const buttonClassName = \"svc-preview__test-again svc-btn\";\n    return attachKey2click(<div\n      role=\"button\"\n      onClick={(e) => {\n        e.stopPropagation();\n        this.model.action();\n      }}\n      className={buttonClassName}\n      title={this.model.title}\n    >\n      <span className=\"svc-btn__text\">\n        {this.model.title}\n      </span>\n    </div>);\n  }\n}\nReactElementFactory.Instance.registerElement(\"svc-complete-page\", (props) => {\n  return React.createElement(TabPreviewTestSurveyAgainComponent, props);\n});\n\nexport class TabPreviewSurveyComponent extends SurveyElementBase<any, any> {\n  constructor(props) {\n    super(props);\n  }\n  private get model(): TestSurveyTabViewModel {\n    return this.props.data;\n  }\n  protected getStateElement(): Base {\n    return this.model;\n  }\n  renderPlaceholder(): React.JSX.Element {\n    return <SurfacePlaceholder name={\"preview\"} placeholderTitleText={this.model.placeholderTitleText} placeholderDescriptionText={this.model.placeholderDescriptionText} />;\n  }\n  renderSimulator(): React.JSX.Element {\n    return (<div className=\"svc-plugin-tab__content\">\n      <SurveySimulator model={this.model.simulator}></SurveySimulator>\n      {this.model.showResults ? <SurveyResults survey={this.model.simulator.survey} /> : null}\n    </div>);\n  }\n\n  renderElement(): React.JSX.Element {\n    const tabContentClassName = \"svc-creator-tab__content svc-test-tab__content\" + (this.model.isPageToolbarVisible ? \" svc-creator-tab__content--with-toolbar\" : \"\");\n    return (\n      <div className={tabContentClassName}>\n        {this.model.simulator.survey.isEmpty ? this.renderPlaceholder() : this.renderSimulator()}\n        {this.getBottomToolbar()}\n      </div>\n    );\n  }\n  getBottomToolbar(): React.JSX.Element {\n    if (this.model.isPageToolbarVisible) {\n      return (\n        <div className=\"svc-test-tab__content-actions\">\n          <SurveyActionBar model={this.model.pages}></SurveyActionBar>\n        </div>\n      );\n    } else {\n      return null;\n    }\n  }\n}\n\nReactElementFactory.Instance.registerElement(\"svc-tab-preview\", (props) => {\n  return React.createElement(TabPreviewSurveyComponent, props);\n});\n", "import * as React from \"react\";\nimport { editorLocalization } from \"survey-creator-core\";\nimport { ReactElementFactory } from \"survey-react-ui\";\n\nexport class PropertyGridPlaceholderComponent extends React.Component<any, any> {\n\n  render(): React.JSX.Element | null {\n    return (\n      <div className=\"svc-property-grid-placeholder\">\n        <div className=\"svc-property-grid-placeholder__header\">\n          <span className=\"svc-property-grid-placeholder__title\">{editorLocalization.getString(\"ed.propertyGridPlaceholderTitle\")}</span>\n          <span className=\"svc-property-grid-placeholder__description\">{editorLocalization.getString(\"ed.propertyGridPlaceholderDescription\")}</span>\n        </div>\n        <div className=\"svc-property-grid-placeholder__content\">\n          <div className=\"svc-property-grid-placeholder__gap\"></div>\n          <div className=\"svc-property-grid-placeholder__image\"></div>\n        </div>\n      </div>\n    );\n  }\n}\n\nReactElementFactory.Instance.registerElement(\"svc-property-grid-placeholder\", (props) => {\n  return React.createElement(PropertyGridPlaceholderComponent, props);\n});", "import * as React from \"react\";\nimport { Action, Base } from \"survey-core\";\nimport {\n  SurveyActionBar,\n  ReactElementFactory,\n  SurveyElementBase,\n  attachKey2click\n} from \"survey-react-ui\";\nimport { ThemeTabViewModel } from \"survey-creator-core\";\nimport { SurveySimulator } from \"./SurveySimulator\";\nimport { SurveyResults } from \"../Results\";\nimport { SurfacePlaceholder } from \"../components/SurfacePlaceholder\";\n\nexport class TabThemeSurveyComponent extends SurveyElementBase<any, any> {\n  private get model(): ThemeTabViewModel {\n    return this.props.data;\n  }\n  protected getStateElement(): Base {\n    return this.model;\n  }\n  renderPlaceholder(): React.JSX.Element {\n    return <SurfacePlaceholder name={\"theme\"} placeholderTitleText={this.model.placeholderTitleText} placeholderDescriptionText={this.model.placeholderDescriptionText} />;\n  }\n  renderSimulator(): React.JSX.Element {\n    return (<div className=\"svc-plugin-tab__content\">\n      <SurveySimulator model={this.model.simulator}></SurveySimulator>\n      {this.model.showResults ? <SurveyResults survey={this.model.simulator.survey} /> : null}\n    </div>);\n  }\n  renderElement(): React.JSX.Element {\n    const tabContentClassName = \"svc-creator-tab__content svc-test-tab__content\" + (this.model.isPageToolbarVisible ? \" svc-creator-tab__content--with-toolbar\" : \"\");\n    return (\n      <div className={tabContentClassName}>\n        {this.model.simulator.survey.isEmpty ? this.renderPlaceholder() : this.renderSimulator()}\n        {this.getBottomToolbar()}\n      </div>\n    );\n  }\n  getBottomToolbar(): React.JSX.Element {\n    if (this.model.isPageToolbarVisible) {\n      return (\n        <div className=\"svc-test-tab__content-actions\">\n          <SurveyActionBar model={this.model.pages}></SurveyActionBar>\n        </div>\n      );\n    } else {\n      return null;\n    }\n  }\n}\n\nReactElementFactory.Instance.registerElement(\"svc-tab-theme\", (props) => {\n  return React.createElement(TabThemeSurveyComponent, props);\n});\n", "import * as React from \"react\";\nimport { Base } from \"survey-core\";\nimport {\n  ReactElementFactory,\n  SurveyElementBase,\n  Survey\n} from \"survey-react-ui\";\nimport { Translation } from \"survey-creator-core\";\nimport { SurfacePlaceholder } from \"../../components/SurfacePlaceholder\";\n\nexport class TabTranslationComponent extends SurveyElementBase<any, any> {\n  private get model(): Translation {\n    return this.props.data || this.props.model;\n  }\n  protected getStateElement(): Base {\n    return this.model;\n  }\n  renderElement(): React.JSX.Element {\n    if (!this.model) return null;\n    return (\n      <div className={\"svc-creator-tab__content svc-translation-tab\" + (this.model.isEmpty ? \" svc-translation-tab--empty\" : \"\")}>\n        {this.renderElementContent()}\n      </div>\n    );\n  }\n  renderElementContent(): React.JSX.Element {\n    if (this.model.isEmpty) {\n      return <SurfacePlaceholder name={\"translation\"} placeholderTitleText={this.model.placeholderTitleText} placeholderDescriptionText={this.model.placeholderDescriptionText} />;\n    } else {\n      return (\n        <div className=\"st-content\">\n          <div className=\"svc-flex-column st-strings-wrapper\">\n            <div className=\"svc-flex-row st-strings-header\">\n              <Survey model={this.model.stringsHeaderSurvey}></Survey>\n            </div>\n            <div className=\"svc-flex-row svc-plugin-tab__content st-strings\">\n              <Survey model={this.model.stringsSurvey}></Survey>\n            </div>\n          </div>\n        </div>\n      );\n    }\n  }\n}\n\nReactElementFactory.Instance.registerElement(\"svc-tab-translation\", (props) => {\n  return React.createElement(TabTranslationComponent, props);\n});\n", "import * as React from \"react\";\nimport { ObjectSelectorModel } from \"survey-creator-core\";\nimport { Base } from \"survey-core\";\nimport { ReactElementFactory, SurveyElementBase, List } from \"survey-react-ui\";\n\ninterface IObjectSelectorComponentProps {\n  model: ObjectSelectorModel;\n}\nclass ObjectSelectorComponent extends SurveyElementBase<IObjectSelectorComponentProps, any> {\n  protected get model(): ObjectSelectorModel {\n    return this.props.model;\n  }\n  protected getStateElement(): Base {\n    return this.model;\n  }\n  renderElement(): React.JSX.Element {\n    if (!this.model.isVisible) return null;\n\n    return (<List model={this.model.list}></List>);\n  }\n}\n\nReactElementFactory.Instance.registerElement(\"svc-object-selector\", (props) => {\n  return React.createElement(\n    ObjectSelectorComponent,\n    props as IObjectSelectorComponentProps\n  );\n});\n", "import * as React from \"react\";\nimport { PropertyGridViewModel, SearchManager } from \"survey-creator-core\";\nimport { Base, SurveyModel, settings } from \"survey-core\";\nimport { Survey, SurveyElementBase, ReactQuestionFactory, SurveyQuestionButtonGroup, ReactElementFactory, SvgIcon, SurveyActionBar, Scroll } from \"survey-react-ui\";\nimport { SearchComponent } from \"../components/Search\";\n\ninterface IPropertyGridComponentProps {\n  model: PropertyGridViewModel;\n}\n\nexport class PropertyGridComponent extends SurveyElementBase<IPropertyGridComponentProps, any> {\n\n  get model(): PropertyGridViewModel {\n    return this.props.model;\n  }\n\n  protected getStateElement(): Base {\n    return this.model;\n  }\n\n  public canRender(): boolean {\n    if (!this.model) return false;\n    return super.canRender();\n  }\n\n  renderElement() {\n    return (\n      <div className={this.model.rootCss}>\n        <SearchComponent model={this.model.searchManager}></SearchComponent>\n        <Survey model={this.model.survey}></Survey>\n      </div>\n    );\n  }\n}\n\nexport default PropertyGridComponent;\n\nReactElementFactory.Instance.registerElement(\"svc-property-grid\", (props) => {\n  return React.createElement(PropertyGridComponent, props);\n});\n", "import * as React from \"react\";\nimport { Switcher } from \"survey-creator-core\";\nimport { Base } from \"survey-core\";\nimport { ReactElementFactory, attachKey2click, SurveyElementBase } from \"survey-react-ui\";\n\ninterface ISwitcherComponentProps {\n  item: Switcher;\n}\n\nexport class SwitcherComponent extends SurveyElementBase<ISwitcherComponentProps, any> {\n  get item(): Switcher {\n    return this.props.item;\n  }\n\n  protected getStateElement(): Base {\n    return this.item;\n  }\n\n  renderElement() {\n    const tooltip = this.item.tooltip || this.item.title;\n    const title = this.item.hasTitle ? <span className=\"svc-switcher__title\">{this.item.title}</span> : null;\n    const button = attachKey2click(\n      <button\n        className={this.item.getActionBarItemCss()}\n        type=\"button\"\n        disabled={this.item.disabled}\n        onClick={(args) => this.item.action(this.item, this.item.getIsTrusted(args))}\n        title={tooltip}\n        aria-checked={this.item.ariaChecked}\n        aria-expanded={this.item.ariaExpanded}\n        role={this.item.ariaRole}\n      >\n        <div className={this.item.getSwitcherIconCss()}>\n          <div className=\"svc-switcher__icon-thumb\"></div>\n        </div>\n        {title}\n      </button>, this.item, { processEsc: false });\n\n    return button;\n  }\n}\n\nexport default SwitcherComponent;\n\nReactElementFactory.Instance.registerElement(\"svc-switcher\", (props) => {\n  return React.createElement(SwitcherComponent, props);\n});", "import * as React from \"react\";\nimport { ItemValue } from \"survey-core\";\nimport { ReactElementFactory, SurveyElementBase, SvgIcon, attachKey2click } from \"survey-react-ui\";\n\nclass ItemTemplateComponent extends SurveyElementBase<{ item: ItemValue }, any> {\n  render() {\n    const item = this.props.item;\n    return (\n      <>\n        <SvgIcon iconName={item.iconName} size={item.iconSize} className={\"svc-json-error__icon\"} ></SvgIcon>\n        <div className=\"svc-json-error__container\">\n          <div className=\"svc-json-error__title\">\n            <span key={2}>{this.renderLocString(item.locTitle, undefined, \"locString\")}</span>\n          </div>\n          {this.renderFixButton()}\n        </div>\n      </>\n    );\n  }\n  renderFixButton() {\n    if (!this.props.item.data.showFixButton) return null;\n    const item = this.props.item;\n    return (attachKey2click(<button type=\"button\" onClick={(event) => { event.stopPropagation(); item.data.fixError(); }} title={item.data.fixButtonTitle} className={\"svc-json-error__fix-button\"}>\n      <SvgIcon iconName={item.data.fixButtonIcon} size={\"auto\"}></SvgIcon>\n    </button>));\n  }\n}\n\nReactElementFactory.Instance.registerElement(\n  \"json-error-item\",\n  (props) => {\n    return React.createElement(ItemTemplateComponent, props);\n  }\n);", "import * as React from \"react\";\nimport { QuestionSpinEditorModel } from \"survey-creator-core\";\nimport { ReactQuestionFactory, SurveyQuestionText, SvgIcon } from \"survey-react-ui\";\n\nexport class SurveyQuestionSpinEditor extends SurveyQuestionText {\n  constructor(props: any) {\n    super(props);\n  }\n  protected get question(): QuestionSpinEditorModel {\n    return this.questionBase as QuestionSpinEditorModel;\n  }\n  protected renderInput() {\n    return (\n      <>\n        <input\n          role=\"spinbutton\"\n          id={this.question.inputId}\n          disabled={this.isDisplayMode}\n          className={this.question.cssClasses.control}\n          ref={(input) => (this.setControl(input))}\n          placeholder={this.question.renderedPlaceholder}\n          autoComplete=\"off\"\n          onBlur={(event) => this.question.onBlur(event.nativeEvent)}\n          onFocus={(event) => this.question.onFocus(event.nativeEvent)}\n          onChange={this.question.onChange}\n          onBeforeInput={event => this.question.onBeforeInput(event.nativeEvent as InputEvent)}\n          onKeyUp={(event) => this.question.onKeyUp(event.nativeEvent)}\n          onKeyDown={(event) => this.question.onInputKeyDown(event.nativeEvent)}\n          aria-required={this.question.a11y_input_ariaRequired}\n          aria-label={this.question.a11y_input_ariaLabel}\n          aria-labelledby={this.question.a11y_input_ariaLabelledBy}\n          aria-describedby={this.question.a11y_input_ariaDescribedBy}\n          aria-invalid={this.question.a11y_input_ariaInvalid}\n          aria-errormessage={this.question.a11y_input_ariaErrormessage}\n        />\n      </>\n    );\n  }\n  protected renderElement(): React.JSX.Element {\n    return (\n      <div className={this.question.cssClasses.root} onKeyDown={event => this.question.onKeyDown(event.nativeEvent)}>\n        {this.renderInput()}\n        {this.renderButtons()}\n      </div>\n    );\n  }\n  protected getValueCore() {\n    return this.question.renderedValue;\n  }\n  protected renderButtons(): React.JSX.Element {\n    return (\n      <span className={this.question.cssClasses.buttonsContainer}>\n        <button\n          tabIndex={-1}\n          aria-hidden={\"true\"}\n          className={this.question.cssClasses.arrowButton}\n          disabled={this.isDisplayMode}\n          onClick={this.question.onDownButtonClick}\n          onMouseDown={this.question.onDownButtonMouseDown}\n          onMouseUp={this.question.onButtonMouseUp}\n          onMouseLeave={this.question.onButtonMouseLeave}\n          onBlur={event => this.question.onBlur(event.nativeEvent)}\n          onFocus={event => this.question.onFocus(event.nativeEvent)}>\n          <SvgIcon iconName={this.question.cssClasses.decreaseButtonIcon} size={\"auto\"}></SvgIcon>\n        </button>\n        <button\n          tabIndex={-1}\n          aria-hidden={\"true\"}\n          className={this.question.cssClasses.arrowButton}\n          disabled={this.isDisplayMode}\n          onClick={this.question.onUpButtonClick}\n          onMouseDown={this.question.onUpButtonMouseDown}\n          onMouseUp={this.question.onButtonMouseUp}\n          onMouseLeave={this.question.onButtonMouseLeave}\n          onBlur={event => this.question.onBlur(event.nativeEvent)}\n          onFocus={event => this.question.onFocus(event.nativeEvent)}>\n          <SvgIcon iconName={this.question.cssClasses.increaseButtonIcon} size={\"auto\"}></SvgIcon>\n        </button>\n      </span>\n    );\n  }\n\n}\n\nReactQuestionFactory.Instance.registerQuestion(\"spinedit\", (props) => {\n  return React.createElement(SurveyQuestionSpinEditor, props);\n});\n", "import * as React from \"react\";\nimport { ItemValue } from \"survey-core\";\nimport { ReactElementFactory, SurveyElementBase } from \"survey-react-ui\";\n\nclass ItemTemplateComponent extends SurveyElementBase<{ item: ItemValue }, any> {\n  render() {\n    const item = this.props.item;\n    return (\n      <>\n        <span className=\"spg-color-editor__color-swatch\" style={{ backgroundColor: item.value }}></span>\n        <span key={2}>{this.renderLocString(item.locTitle, undefined, \"locString\")}</span>\n      </>\n    );\n  }\n}\n\nReactElementFactory.Instance.registerElement(\n  \"color-item\",\n  (props) => {\n    return React.createElement(ItemTemplateComponent, props);\n  }\n);", "import * as React from \"react\";\nimport { QuestionColorModel } from \"survey-creator-core\";\nimport { ReactElementFactory, ReactQuestionFactory, SurveyQuestionText, SvgIcon, Popup } from \"survey-react-ui\";\n\nexport class SurveyQuestionColor extends SurveyQuestionText {\n  constructor(props: any) {\n    super(props);\n  }\n  protected get question(): QuestionColorModel {\n    return this.questionBase as QuestionColorModel;\n  }\n  protected renderInput() {\n    return (\n      <>\n        <input\n          id={this.question.inputId}\n          disabled={this.isDisplayMode}\n          className={this.question.cssClasses.control}\n          ref={(input) => (this.setControl(input))}\n          placeholder={this.question.renderedPlaceholder}\n          autoComplete=\"off\"\n          onKeyUp={(event) => this.question.onKeyUp(event.nativeEvent)}\n          onBlur={(event) => this.question.onBlur(event.nativeEvent)}\n          onChange={this.question.onChange}\n          onBeforeInput={event => this.question.onBeforeInput(event.nativeEvent as InputEvent)}\n          aria-required={this.question.a11y_input_ariaRequired}\n          aria-labelledby={this.question.a11y_input_ariaLabelledBy}\n          aria-label={this.question.a11y_input_ariaLabel}\n          aria-invalid={this.question.a11y_input_ariaInvalid}\n          aria-describedby={this.question.a11y_input_ariaDescribedBy}\n        />\n      </>\n    );\n  }\n  protected renderElement(): React.JSX.Element {\n    return (\n      <div className={this.question.cssClasses.root} onKeyDown={event => this.question.onKeyDown(event.nativeEvent)}>\n        {this.renderColorSwatch()}\n        {this.renderInput()}\n        {this.question.showDropdownAction ? this.renderDropdownAction() : null}\n      </div>\n    );\n  }\n  protected getValueCore() {\n    return this.question.renderedValue;\n  }\n  protected renderColorSwatch(): React.JSX.Element {\n    return <label className={this.question.getSwatchCss()} style={this.question.getSwatchStyle()}>\n      <SvgIcon iconName={this.question.cssClasses.swatchIcon} size={\"auto\"}></SvgIcon>\n      <input type=\"color\"\n        disabled={this.isDisplayMode}\n        value={this.question.renderedColorValue}\n        className={this.question.cssClasses.colorInput}\n        onChange={(event) => this.question.onColorInputChange(event.nativeEvent)}\n        tabIndex={-1}\n        aria-required={this.question.a11y_input_ariaRequired}\n        aria-labelledby={this.question.a11y_input_ariaLabelledBy}\n        aria-label={this.question.a11y_input_ariaLabel}\n        aria-invalid={this.question.a11y_input_ariaInvalid}\n        aria-describedby={this.question.a11y_input_ariaDescribedBy} />\n    </label>;\n  }\n  protected renderDropdownAction(): React.JSX.Element {\n    return (\n      <>\n        <div aria-hidden=\"true\" className={this.question.cssClasses.choicesButtonWrapper}>\n          {ReactElementFactory.Instance.createElement(\"sv-action-bar-item\", { item: this.question.dropdownAction })}\n        </div>\n        {this.renderPopup()}\n      </>\n    );\n  }\n  protected renderPopup(): React.JSX.Element {\n    return <Popup model={this.question.dropdownAction.popupModel}></Popup>;\n  }\n}\n\nReactQuestionFactory.Instance.registerQuestion(\"color\", (props) => {\n  return React.createElement(SurveyQuestionColor, props);\n});", "import * as React from \"react\";\nimport { QuestionFileEditorModel } from \"survey-creator-core\";\nimport { ReactQuestionFactory, SurveyQuestionText, SvgIcon, attachKey2click } from \"survey-react-ui\";\n\nexport class SurveyQuestionFileEditor extends SurveyQuestionText {\n  constructor(props: any) {\n    super(props);\n  }\n  protected get questionFile(): QuestionFileEditorModel {\n    return this.questionBase as QuestionFileEditorModel;\n  }\n  protected getValueCore() {\n    return this.question.renderedValue;\n  }\n  protected renderInput() {\n    return (\n      <>\n        <input\n          disabled={this.question.isTextInputReadOnly}\n          className={this.questionFile.cssClasses.control}\n          placeholder={this.questionFile.renderedPlaceholder}\n          ref={(input) => (this.setControl(input))}\n          autoComplete=\"off\"\n          type=\"text\"\n          onBlur={(event) => this.questionFile.onInputBlur(event.nativeEvent)}\n          onChange={(event) => this.questionFile.onInputChange(event.nativeEvent)}\n        />\n      </>\n    );\n  }\n  protected renderFileInput(): React.JSX.Element {\n    return (\n      <input\n        type=\"file\"\n        disabled={this.isDisplayMode}\n        className={this.questionFile.cssClasses.fileInput}\n        id={this.questionFile.inputId}\n        aria-required={this.questionFile.ariaRequired}\n        aria-label={this.questionFile.ariaLabel}\n        aria-invalid={this.questionFile.ariaInvalid}\n        aria-describedby={this.questionFile.ariaDescribedBy}\n        multiple={false} title={this.questionFile.inputTitle}\n        accept={this.questionFile.acceptedTypes}\n        tabIndex={-1}\n        onChange={(event) => this.questionFile.onFileInputChange(event.nativeEvent)} />\n    );\n  }\n  protected renderButtons(): React.JSX.Element {\n    return (\n      <div className={this.questionFile.cssClasses.buttonsContainer}>\n        {this.renderClearButton()}\n        {this.renderChooseButton()}\n      </div>\n    );\n  }\n  protected renderClearButton(): React.JSX.Element {\n    return attachKey2click((\n      <button\n        className={this.questionFile.cssClasses.clearButton}\n        title={this.questionFile.clearButtonCaption}\n        disabled={this.questionFile.getIsClearButtonDisabled()}\n        onClick={this.questionFile.doClean}>\n        <SvgIcon iconName={this.questionFile.cssClasses.clearButtonIcon} size={\"auto\"}></SvgIcon>\n      </button>\n    ));\n  }\n  protected renderChooseButton(): React.JSX.Element {\n    return (\n      attachKey2click(\n        <label\n          onClick={event => this.questionFile.chooseFiles(event.nativeEvent)}\n          className={this.questionFile.getChooseButtonCss()}\n          htmlFor={this.questionFile.inputId}\n          aria-label={this.questionFile.chooseButtonCaption}>\n          <SvgIcon iconName={this.questionFile.cssClasses.chooseButtonIcon} size={\"auto\"} title={this.questionFile.chooseButtonCaption}></SvgIcon>\n        </label>\n      )\n    );\n  }\n  protected renderElement(): React.JSX.Element {\n    return (\n      <div\n        className={this.questionFile.cssClasses.root}\n        ref={el => this.setContent(el)}\n        onDragEnter={this.questionFile.onDragEnter}\n        onDragOver={this.questionFile.onDragOver}\n        onDrop={this.questionFile.onDrop}\n        onDragLeave={this.questionFile.onDragLeave}\n        onKeyDown={event => this.question.onKeyDown(event.nativeEvent)}>\n        {this.renderInput()}\n        {this.renderFileInput()}\n        {this.renderButtons()}\n      </div>\n    );\n  }\n}\n\nReactQuestionFactory.Instance.registerQuestion(\"fileedit\", (props) => {\n  return React.createElement(SurveyQuestionFileEditor, props);\n});", "import * as React from \"react\";\nimport { QuestionCommentWithResetModel, QuestionTextWithResetModel } from \"survey-creator-core\";\nimport { ReactQuestionFactory, SurveyQuestionElementBase, SurveyQuestionText, SvgIcon } from \"survey-react-ui\";\n\nexport class SurveyQuestionTextWithReset extends SurveyQuestionElementBase {\n  protected get question(): QuestionTextWithResetModel | QuestionCommentWithResetModel {\n    return this.questionBase as unknown as (QuestionTextWithResetModel | QuestionCommentWithResetModel);\n  }\n  protected renderElement(): React.JSX.Element {\n    const textElement = this.renderInput();\n    const resetButton = this.renderResetButton();\n    return (\n      <div className={this.question.getRootClass()}>\n        {textElement}\n        {resetButton}\n      </div>\n    );\n  }\n\n  protected renderInput() {\n    return ReactQuestionFactory.Instance.createQuestion(this.question.wrappedQuestionTemplate,\n      {\n        question: this.question,\n        isDisplayMode: this.question.isInputReadOnly,\n        creator: this,\n      });\n  }\n  protected renderResetButton() {\n    return (<button\n      className={this.question.cssClasses.resetButton}\n      disabled={this.question.resetValueAdorner.isDisabled}\n      title={this.question.resetValueAdorner.caption}\n      onClick={() => this.question.resetValueAdorner.resetValue()}>\n      <SvgIcon iconName={this.question.cssClasses.resetButtonIcon} size={\"auto\"}></SvgIcon>\n    </button>);\n  }\n}\n\nReactQuestionFactory.Instance.registerQuestion(\"textwithreset\", (props) => {\n  return React.createElement(SurveyQuestionTextWithReset, props);\n});\n\nReactQuestionFactory.Instance.registerQuestion(\"commentwithreset\", (props) => {\n  return React.createElement(SurveyQuestionTextWithReset, props);\n});", "import * as React from \"react\";\nimport { type QuestionBooleanModel, RendererFactory } from \"survey-core\";\nimport { attachKey2click, ReactQuestionFactory, SurveyElementBase, SurveyQuestionElementBase } from \"survey-react-ui\";\nexport class SurveyQuestionBooleanSwitch extends SurveyQuestionElementBase {\n  protected renderElement(): React.JSX.Element {\n    const button = attachKey2click(<div className={\"spg-boolean-switch__button\" + (this.questionBase.value ? \" spg-boolean-switch__button--checked\" : \"\")} tabIndex={0}\n      role=\"checkbox\"\n      aria-checked={this.questionBase.booleanValue || false}\n      aria-required={this.questionBase.a11y_input_ariaRequired}\n      aria-label={this.questionBase.a11y_input_ariaLabel}\n      aria-labelledby={this.questionBase.a11y_input_ariaLabelledBy}\n      aria-invalid={this.questionBase.a11y_input_ariaInvalid}\n      aria-errormessage={this.questionBase.a11y_input_ariaErrormessage}\n    >\n      <div className=\"spg-boolean-switch__thumb\">\n        <div className=\"spg-boolean-switch__thumb-circle spg-boolean-switch__thumb--left\"></div>\n      </div>\n      <div className=\"spg-boolean-switch__thumb\">\n        <div className=\"spg-boolean-switch__thumb-circle spg-boolean-switch__thumb--right\"></div>\n      </div>\n    </div>, this.questionBase, { processEsc: false });\n\n    return (\n      <div className=\"spg-boolean-switch\" onClick={() => this.questionBase.value = !this.questionBase.value}>\n        {button}\n        <div className=\"spg-boolean-switch__caption\">\n          <div className=\"spg-boolean-switch__title\" id={this.questionBase.labelRenderedAriaID}>\n            {SurveyElementBase.renderLocString(this.questionBase.locTitle)}\n          </div>\n        </div>\n      </div>\n    );\n  }\n}\n\nReactQuestionFactory.Instance.registerQuestion(\n  \"sv-boolean-switch\",\n  (props) => {\n    return React.createElement(SurveyQuestionBooleanSwitch, props);\n  }\n);\n\nRendererFactory.Instance.registerRenderer(\n  \"boolean\",\n  \"switch\",\n  \"sv-boolean-switch\"\n);\n", "export let Version: string;\nVersion = `${process.env.VERSION}`;\n\n// import \"@survey/creator/survey-creator-core.css\";\n\nexport { SurveyCreatorComponent } from \"../SurveyCreator\";\n\nexport * from \"../adorners/Row\";\nexport * from \"../adorners/Question\";\nexport * from \"../adorners/QuestionHeader\";\nexport * from \"../adorners/QuestionFooter\";\nexport * from \"../adorners/QuestionBanner\";\nexport * from \"../adorners/QuestionDropdown\";\nexport * from \"../adorners/QuestionImage\";\nexport * from \"../adorners/QuestionRating\";\nexport * from \"../adorners/QuestionWidget\";\nexport * from \"../adorners/CellQuestion\";\nexport * from \"../adorners/CellQuestionDropdown\";\nexport * from \"../adorners/Page\";\nexport * from \"../adorners/Panel\";\n\nexport * from \"../LogoImage\";\nexport * from \"../QuestionLinkValue\";\nexport * from \"../QuestionEmbeddedSurvey\";\nexport * from \"../QuestionEditorContent\";\nexport * from \"../ItemValueWrapper\";\nexport * from \"../ImageItemValueWrapper\";\nexport * from \"../MatrixCellWrapper\";\nexport * from \"../Results\";\nexport * from \"../toolbox/ToolboxItem\";\nexport * from \"../toolbox/ToolboxItemGroup\";\nexport * from \"../toolbox/ToolboxCategory\";\nexport * from \"../toolbox/ToolboxList\";\nexport * from \"../toolbox/AdaptiveToolbox\";\nexport * from \"../TabbedMenu\";\nexport * from \"../Navigation\";\nexport * from \"../side-bar/TabControl\";\nexport * from \"../side-bar/TabButton\";\nexport * from \"../side-bar/SideBarDefaultHeader\";\nexport * from \"../side-bar/SideBarPropertyGridHeader\";\nexport * from \"../side-bar/SideBarHeader\";\nexport * from \"../side-bar/SideBar\";\nexport * from \"../tabs/translation/TranslationLineSkeleton\";\nexport * from \"../tabs/translation/TranslateFromAction\";\nexport * from \"../ActionButton\";\nexport * from \"../StringEditor\";\nexport * from \"../QuestionError\";\n\nexport * from \"../tabs/logic-operator\";\nexport * from \"../tabs/Designer\";\nexport * from \"../tabs/JsonEditorAce\";\nexport * from \"../tabs/JsonEditorTextarea\";\nexport * from \"../tabs/Logic\";\nexport * from \"../tabs/Preview\";\nexport * from \"../side-bar/PropertyGridPlaceholder\";\nexport * from \"../tabs/Theme\";\nexport * from \"../tabs/translation/Translation\";\nexport * from \"../tabs/SurveySimulator\";\n\nexport * from \"../events\";\nexport * from \"../side-bar/ObjectSelector\";\nexport * from \"../side-bar/PropertyGrid\";\nexport * from \"../components/Search\";\nexport * from \"../Switcher\";\n\nexport * from \"../tabs/JsonErrorItem\";\n\n//custom questions\nexport * from \"../custom-questions/SpinEditor\";\nexport * from \"../custom-questions/ColorItem\";\nexport * from \"../custom-questions/ColorQuestion\";\nexport * from \"../custom-questions/FileEditQuestion\";\nexport * from \"../custom-questions/TextWithResetQuestion\";\nexport * from \"../custom-questions/BooleanSwitch\";\n\nexport { editorLocalization, localization } from \"survey-creator-core\";\nexport { settings } from \"survey-creator-core\";\nexport { svgBundle } from \"survey-creator-core\";\nexport { SurveyLogic, SurveyLogicUI } from \"survey-creator-core\";\nexport { SurveyQuestionEditorDefinition } from \"survey-creator-core\";\nexport { ISurveyCreatorOptions, IPropertyGridEditor } from \"survey-creator-core\";\nexport { ToolboxToolViewModel, PropertyGridEditorCollection } from \"survey-creator-core\";\n\nimport { checkLibraryVersion } from \"survey-core\";\ncheckLibraryVersion(`${process.env.VERSION}`, \"survey-creator-react\");"], "mappings": ";;;;;;;;;;;;;;AAUM,MAAOA,mBAAoB,SAAQC,iBAGxC;EAGC,IAAYC,KAAKA,CAAA;IACf,OAAO,IAAI,CAACC,KAAK,CAACD,KAAK;;EAGfE,eAAeA,CAAA;IACvB,OAAO,IAAI,CAACF,KAAK;;EAGnBG,YAAYF,KAAK;IACf,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACG,OAAO,GAAGC,KAAK,CAACC,SAAS,EAAE;;EAGlCC,aAAaA,CAAA;IACX,MAAMC,KAAK,GAAG,IAAI,CAACR,KAAK,CAACS,eAAe,CAACC,GAAG,CAAEC,IAAI,IAAKN,KAAA,CAAAO,aAAA,CAACC,qBAAqB;MAACF,IAAI,EAAEA,IAAI;MAAEG,GAAG,EAAEH,IAAI,CAACI;IAAU,EAAI,CAAC;IACnH,OACEV,KAAA,CAAAO,aAAA;MAAKI,GAAG,EAAE,IAAI,CAACZ,OAAO;MAAEa,SAAS,EAAC,iBAAiB;MAACC,IAAI,EAAC,SAAS;MAACC,KAAK,EAAE,IAAI,CAACnB,KAAK,CAACoB,YAAY;IAAE,GAChGZ,KAAK,CACF;;EAGVa,kBAAkBA,CAACC,SAAc,EAAEC,SAAc;IAC/C,KAAK,CAACF,kBAAkB,CAACC,SAAS,EAAEC,SAAS,CAAC;IAC9C,MAAMC,SAAS,GAAmB,IAAI,CAACpB,OAAO,CAACqB,OAAO;IACtD,IAAI,CAACD,SAAS,EAAE;IAChB,IAAI,CAACxB,KAAK,CAAC0B,uBAAuB,CAACF,SAAS,CAAC;;EAE/CG,iBAAiBA,CAAA;IACf,KAAK,CAACA,iBAAiB,EAAE;IACzB,MAAMH,SAAS,GAAmB,IAAI,CAACpB,OAAO,CAACqB,OAAO;IACtD,IAAI,CAACD,SAAS,EAAE;IAChB,IAAI,CAACxB,KAAK,CAAC0B,uBAAuB,CAACF,SAAS,CAAC;;EAE/CI,oBAAoBA,CAAA;IAClB,IAAI,CAAC5B,KAAK,CAAC6B,wBAAwB,EAAE;IACrC,KAAK,CAACD,oBAAoB,EAAE;;AAE/B;AAED,MAAMf,qBAAsB,SAAQd,iBAGnC;EAECI,YAAYF,KAAK;IACf,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACe,GAAG,GAAGX,KAAK,CAACC,SAAS,EAAE;;EAG9B,IAAYK,IAAIA,CAAA;IACd,OAAO,IAAI,CAACV,KAAK,CAACU,IAAI;;EAGdT,eAAeA,CAAA;IACvB,OAAO,IAAI,CAACS,IAAI;;EAGlBJ,aAAaA,CAAA;IACX,IAAIuB,GAAG,GAAW,gCAAgC;IAClD,IAAI,IAAI,CAACnB,IAAI,CAACmB,GAAG,EAAE;MACjBA,GAAG,IAAI,GAAG,GAAG,IAAI,CAACnB,IAAI,CAACmB,GAAG;;IAE5BA,GAAG,IAAK,CAAC,IAAI,CAACnB,IAAI,CAACoB,SAAS,GAAG,oBAAoB,GAAG,EAAG;IAEzD,MAAMC,SAAS,GAAGC,mBAAmB,CAACC,QAAQ,CAACtB,aAAa,CAC1D,IAAI,CAACD,IAAI,CAACqB,SAAS,IAAI,sBAAsB,EAC7C;MAAErB,IAAI,EAAE,IAAI,CAACA;IAAI,CAAE,CACpB;IAED,OACEN,KAAA,CAAAO,aAAA;MAAME,GAAG,EAAE,IAAI,CAACH,IAAI,CAACwB,EAAE;MAAElB,SAAS,EAAEa,GAAG;MAAEd,GAAG,EAAE,IAAI,CAACA;IAAG,GACpDX,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAoB,GAChCe,SAAS,CACN,CACD;;EAGXL,iBAAiBA,CAAA;IACf,KAAK,CAACA,iBAAiB,EAAE;IACzB,IAAI,CAAChB,IAAI,CAACyB,kBAAkB,GAAG,CAACC,IAAI,EAAEC,QAAQ,KAAI;MAChDC,cAAc,CAAC,MAAK;QAClB,IAAKC,QAAgB,CAAC,WAAW,CAAC,EAAE;UACjCA,QAAgB,CAAC,WAAW,CAAC,CAAC,MAAK;YAClC,IAAI,CAAC7B,IAAI,CAAC0B,IAAI,GAAGA,IAAI;UACvB,CAAC,CAAC;eACG;UACL,IAAI,CAAC1B,IAAI,CAAC0B,IAAI,GAAGA,IAAI;;QAEvBE,cAAc,CAAC,MAAK;UAClBD,QAAQ,CAACD,IAAI,EAAE,IAAI,CAACrB,GAAG,CAACS,OAAO,CAAC;QAClC,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IACD,IAAI,CAACd,IAAI,CAAC8B,WAAW,EAAE;;EAEzBb,oBAAoBA,CAAA;IAClB,KAAK,CAACA,oBAAoB,EAAE;IAC5B,IAAI,CAACjB,IAAI,CAACyB,kBAAkB,GAAGM,SAAS;;AAE3C;AAKK,MAAOC,uBAAwB,SAAQ5C,iBAG5C;EACC,IAAIY,IAAIA,CAAA;IACN,OAAO,IAAI,CAACV,KAAK,CAACU,IAAI;;EAEdT,eAAeA,CAAA;IACvB,OAAO,IAAI,CAACS,IAAI;;EAGlBiC,MAAMA,CAAA;IACJ,MAAMjC,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,OAAQkC,eAAe,CACrBxC,KAAA,CAAAO,aAAA;MACEM,IAAI,EAAC,KAAK;MACViB,EAAE,EAAE,MAAM,GAAGxB,IAAI,CAACwB,EAAE;MAAA,iBACLxB,IAAI,CAACmC,MAAM;MAAA,iBACX,gBAAgB,GAAGnC,IAAI,CAACwB,EAAE;MACzClB,SAAS,EAAEN,IAAI,CAACoC,UAAU,EAAE;MAC5BC,OAAO,EAAEA,CAAA,KAAMrC,IAAI,CAACsC,MAAM,CAACtC,IAAI;IAAC,GAE/BA,IAAI,CAACuC,QAAQ,GAAG7C,KAAA,CAAAO,aAAA;MAAMK,SAAS,EAAEN,IAAI,CAACwC,WAAW;IAAE,GAAGxC,IAAI,CAACyC,KAAK,CAAQ,GAAG,IAAI,EAC/EzC,IAAI,CAAC0C,OAAO,GAAGhD,KAAA,CAAAO,aAAA,CAAC0C,OAAO;MAACC,QAAQ,EAAE5C,IAAI,CAAC4C,QAAQ;MAAEtC,SAAS,EAAEN,IAAI,CAAC6C,UAAU,EAAE;MAAEC,IAAI,EAAE,MAAM;MAAEL,KAAK,EAAEzC,IAAI,CAAC+C,OAAO,IAAI/C,IAAI,CAACyC;IAAK,EAAY,GAAG,IAAI,CAC9I,CACP;;AAGJ;AAEDnB,mBAAmB,CAACC,QAAQ,CAACyB,eAAe,CAC1C,sBAAsB,EACrB1D,KAAK,IAAI;EACR,OAAOI,KAAK,CAACO,aAAa,CAAC+B,uBAAuB,EAAE1C,KAAK,CAAC;AAC5D,CAAC,CACF;AC1HK,MAAO2D,sBAAuB,SAAQ7D,iBAG3C;EACCI,YAAYF,KAAmC;IAC7C,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAAC4D,QAAQ,GAAGxD,KAAK,CAACC,SAAS,EAAE;;EAEnC,IAAIwD,OAAOA,CAAA;IACT,OAAO,IAAI,CAAC7D,KAAK,CAAC6D,OAAO;;EAEjB5D,eAAeA,CAAA;IACvB,OAAO,IAAI,CAAC4D,OAAO;;EAErB,IAAI3C,KAAKA,CAAA;IACP,OAAO,IAAI,CAAClB,KAAK,CAACkB,KAAK;;EAGzBE,kBAAkBA,CAACC,SAAc,EAAEC,SAAc;IAC/C,KAAK,CAACF,kBAAkB,CAACC,SAAS,EAAEC,SAAS,CAAC;IAC9C,IAAI,IAAI,CAACuC,OAAO,KAAKxC,SAAS,CAACwC,OAAO,EAAE;MACtC,IAAIxC,SAAS,CAACwC,OAAO,EAAE;QACrBxC,SAAS,CAACwC,OAAO,CAACC,sBAAsB,EAAE;;MAE5C,IAAI,IAAI,CAACD,OAAO,IAAI,IAAI,CAACD,QAAQ,CAACpC,OAAO,EAAE;QACzC,IAAI,CAACqC,OAAO,CAACE,cAAc,CAAC,IAAI,CAACH,QAAQ,CAACpC,OAAO,CAAC;;;;EAKxDE,iBAAiBA,CAAA;IACf,KAAK,CAACA,iBAAiB,EAAE;IACzB,IAAI,CAACmC,OAAO,CAACE,cAAc,CAAC,IAAI,CAACH,QAAQ,CAACpC,OAAO,CAAC;;EAEpDG,oBAAoBA,CAAA;IAClB,KAAK,CAACA,oBAAoB,EAAE;IAC5B,IAAI,CAACkC,OAAO,CAACC,sBAAsB,EAAE;;EAIvCxD,aAAaA,CAAA;IACX,MAAMuD,OAAO,GAAuB,IAAI,CAAC7D,KAAK,CAAC6D,OAAO;IACtD,IAAIA,OAAO,CAACG,iBAAiB,EAAE,OAAO,IAAI;IAC1C,MAAMC,aAAa,GAAG,sDAAsD,IAAI,IAAI,CAACjE,KAAK,CAAC6D,OAAO,CAACK,qBAAqB,GAAG,EAAE,GAAG,iCAAiC,CAAC;IAClK,MAAMC,uBAAuB,GAAG,2CAA2C,IAAI,IAAI,CAACnE,KAAK,CAAC6D,OAAO,CAACO,YAAY,GAAG,+CAA+C,GAAG,EAAE,CAAC;IACtK,MAAMC,sBAAsB,GAAG,iCAAiC,IAAI,0BAA0B,GAAG,IAAI,CAACR,OAAO,CAACS,eAAe,CAAC;IAC9H,MAAMC,aAAa,GAAG,EAAE;IACxBC,MAAM,CAACD,aAAa,EAAE,IAAI,CAACrD,KAAK,EAAE,IAAI,CAAClB,KAAK,CAAC6D,OAAO,CAACY,cAAc,CAAC;IACpE,IAAIC,aAAa,GAAG,IAAI;IACxB,IAAI,CAAC,IAAI,CAAC1E,KAAK,CAAC6D,OAAO,CAACK,qBAAqB,EAAE;MAC7C,MAAMS,SAAS,GAAG;QAAEC,MAAM,EAAE,IAAI,CAAC5E,KAAK,CAAC6D,OAAO,CAACgB;MAAW,CAAE;MAC5DH,aAAa,GACXtE,KAAA,CAAAO,aAAA;QAAKK,SAAS,EAAC;MAAqB,GAClCZ,KAAA,CAAAO,aAAA;QAAMK,SAAS,EAAC,kCAAkC;QAAC8D,uBAAuB,EAAEH;MAAS,EAAS,CAEjG;;;IAGH,OACEvE,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAE,IAAI,CAAC6C,OAAO,CAACf,UAAU,EAAE;MAAE/B,GAAG,EAAE,IAAI,CAAC6C,QAAQ;MAAE1C,KAAK,EAAEqD;IAAa,GACjFnE,KAAA,CAAAO,aAAA,CAACoE,kBAAkB,OAAsB,EACzC3E,KAAA,CAAAO,aAAA,CAACqE,UAAU,OAAc,EACzB5E,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAEiD;IAAa,GAC3B7D,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAEqD;IAAsB,GACpCjE,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAsE,GACnFZ,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAa,GACxB6C,OAAO,CAACoB,QAAQ,GAChB7E,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAyB,GACtCZ,KAAA,CAAAO,aAAA,CAACd,mBAAmB;MAACE,KAAK,EAAE8D,OAAO,CAACqB;IAAU,EAAwB,CAClE,GAAG,IAAI,EACbrB,OAAO,CAACsB,WAAW,GACnB/E,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAqB,GAClCZ,KAAA,CAAAO,aAAA,CAACyE,eAAe;MAACrF,KAAK,EAAE8D,OAAO,CAACwB;IAAO,EAAoB,IAE3D,IAAI,CACJ,EACNjF,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAEmD;IAAuB,GACrC/D,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAA6C,GACzD,IAAI,CAACsE,eAAe,EAAE,CACnB,CACF,EACNlF,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAgB,GAC3B6C,OAAO,CAACO,YAAY,GACpBhE,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAqB,GAClCZ,KAAA,CAAAO,aAAA,CAACyE,eAAe;MAACrF,KAAK,EAAE8D,OAAO,CAAC0B;IAAa,EAAoB,IAEjE,IAAI,CACJ,CACF,EACL,IAAI,CAACC,aAAa,EAAE,CACjB,EACLd,aAAa,EACdtE,KAAA,CAAAO,aAAA,CAAC8E,iBAAiB;MAACC,QAAQ,EAAE7B,OAAO,CAAC6B;IAAQ,EAAsB,CAC/D,CACF;;EAGVJ,eAAeA,CAAA;IACb,MAAMzB,OAAO,GAAuB,IAAI,CAAC7D,KAAK,CAAC6D,OAAO;IACtD,KAAK,IAAI8B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG9B,OAAO,CAAC+B,IAAI,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MAC5C,IAAI9B,OAAO,CAAC+B,IAAI,CAACD,CAAC,CAAC,CAACzD,EAAE,KAAK2B,OAAO,CAACiC,SAAS,EAAE;QAC5C,OAAO,IAAI,CAACC,gBAAgB,CAAClC,OAAO,CAAC+B,IAAI,CAACD,CAAC,CAAC,CAAC;;;IAGjD,OAAO,IAAI;;EAEbI,gBAAgBA,CAACC,GAAoB;IACnC,IAAIA,GAAG,CAACC,OAAO,KAAK,KAAK,EAAE;MACzB,OAAO,IAAI;;IAEb,MAAMpC,OAAO,GAAuB,IAAI,CAAC7D,KAAK,CAAC6D,OAAO;IACtD,MAAM9B,SAAS,GAAG,CAAC,CAACiE,GAAG,CAACE,SAAA,GACpBF,GAAG,CAACE,SAAS,KACblE,mBAAmB,CAACC,QAAQ,CAACtB,aAAa,CAACqF,GAAG,CAACG,gBAAgB,EAAE;MACjEtC,OAAO,EAAEA,OAAO;MAChBuC,MAAM,EAAEvC,OAAO,CAACuC,MAAM;MACtBC,IAAI,EAAEL,GAAG,CAACK,IAAI,CAACtG;IAChB,EAAC;IACJ,MAAMiB,SAAS,GAAG,iBAAiB,IAAI6C,OAAO,CAACyC,eAAe,IAAI,OAAO,GAAG,8BAA8B,GAAG,EAAE,CAAC;IAChH,OACElG,KAAA,CAAAO,aAAA;MACEM,IAAI,EAAC,UAAU;MACfJ,GAAG,EAAEmF,GAAG,CAAC9D,EAAE;MACXA,EAAE,EAAE,gBAAgB,GAAG8D,GAAG,CAAC9D,EAAE;MAAA,mBACZ,MAAM,GAAG8D,GAAG,CAAC9D,EAAE;MAChClB,SAAS,EAAEA;IAAS,GAEnBe,SAAS,CACN;;EAGVyD,aAAaA,CAAA;IACX,IAAI,CAAC,CAAC,IAAI,CAAC3B,OAAO,CAAC0C,OAAO,EAAE;MAC1B,OAAOvE,mBAAmB,CAACC,QAAQ,CAACtB,aAAa,CAAC,cAAc,EAAE;QAAEZ,KAAK,EAAE,IAAI,CAAC8D,OAAO,CAAC0C;MAAO,CAAE,CAAC;WAC7F;MACL,OAAO,IAAI;;;AAGhB;AAEK,MAAOC,aAAc,SAAQC,kBAAkB;EACnDvG,YAAYwG,OAAA,GAA2B,EAAE,EAAEC,QAA0B;IACnE,KAAK,CAACD,OAAO,EAAEC,QAAQ,CAAC;;EAEnBhE,MAAMA,CAACiE,MAA4B;;IAExCC,OAAO,CAACC,KAAK,CAAC,sEAAsE,CAAC;;;EAIhFC,qBAAqBA,CAACC,QAAkB;IAC7C,OAAOC,oBAAoB,CAAChF,QAAQ,CAACiF,cAAc,CACjDF,QAAQ,CAACG,kBAAkB,KACvBH,QAAQ,CAACI,WAAW,KACpBJ,QAAQ,CAACK,gBAAgB,EAAE,EAC/B;MACEL,QAAQ,EAAEA,QAAQ;MAClBM,aAAa,EAAEN,QAAQ,CAACO,UAAU;MAClC1D,OAAO,EAAE;IACV,EACF;;EAEI2D,WAAWA,CAChB3G,GAAW,EACXiG,KAAkB,EAClBW,UAAe;IAEf,OACErH,KAAA,CAAAO,aAAA;MAAKE,GAAG,EAAEA;IAAG,GACXT,KAAA,CAAAO,aAAA;MAAMK,SAAS,EAAEyG,UAAU,CAACX,KAAK,CAACY,IAAI;MAAA,eAAc;IAAM,EAAG,EAC7DtH,KAAA,CAAAO,aAAA;MAAMK,SAAS,EAAEyG,UAAU,CAACX,KAAK,CAACpG;IAAI,GACpCN,KAAA,CAAAO,aAAA,CAACgH,qBAAqB;MAACC,MAAM,EAAEd,KAAK,CAACe;IAAO,EAAI,CAC3C,CACH;;EAGHC,qBAAqBA,CAAA;IAC1B,OAAO,IAAI,CAAC1B,MAAM,CAAC0B,qBAAqB;;EAEnCC,qBAAqBA,CAAA;IAC1B,OAAO,IAAI,CAAC3B,MAAM,CAAC2B,qBAAqB;;AAE3C;AAED/F,mBAAmB,CAACC,QAAQ,CAACyB,eAAe,CAAC,eAAe,EAAG1D,KAAK,IAAI;EACtE,OAAOI,KAAK,CAACO,aAAa,CAACqH,MAAM,EAAEhI,KAAK,CAAC;AAC3C,CAAC,CAAC;ACzNI,MAAOiI,mBAA0B,SAAQnI,iBAAuB;EACpEI,YAAYF,KAAQ;IAClB,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACkI,WAAW,CAAClI,KAAK,CAAC;;EAEzBmI,qBAAqBA,CAACC,SAAc,EAAEC,SAAc;IAClD,MAAMC,MAAM,GAAG,KAAK,CAACH,qBAAqB,CAACC,SAAS,EAAEC,SAAS,CAAC;IAChE,IAAIC,MAAM,EAAE;MACV,IAAI,IAAI,CAACC,eAAe,CAACH,SAAS,CAAC,EAAE;QACnC,IAAI,CAACF,WAAW,CAACE,SAAS,CAAC;;;IAG/B,OAAOE,MAAM;;EAELJ,WAAWA,CAAClI,KAAU;EACtBuI,eAAeA,CAACH,SAAc;IACtC,MAAMI,KAAK,GAAG,IAAI,CAACC,oBAAoB,EAAE;IACzC,IAAI,CAACC,KAAK,CAACC,OAAO,CAACH,KAAK,CAAC,EAAE,OAAO,IAAI;IACtC,KAAK,IAAI7C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6C,KAAK,CAAC3C,MAAM,EAAEF,CAAC,EAAE,EAAE;MACrC,MAAM9E,GAAG,GAAG2H,KAAK,CAAC7C,CAAC,CAAC;MACpB,IAAI,IAAI,CAAC3F,KAAK,CAACa,GAAG,CAAC,KAAKuH,SAAS,CAACvH,GAAG,CAAC,EAAE,OAAO,IAAI;;IAErD,OAAO,KAAK;;EAEJ4H,oBAAoBA,CAAA;IAC5B,OAAOhG,SAAS;;AAEnB;ACjBK,MAAOmG,UAAW,SAAQX,mBAG/B;EAEC/H,YAAYF,KAA+B;IACzC,KAAK,CAACA,KAAK,CAAC;;EAEJkI,WAAWA,CAAClI,KAAU;IAC9B,IAAI,CAAC,CAAC,IAAI,CAACD,KAAK,EAAE;MAChB,IAAI,CAACA,KAAK,CAAC8I,OAAO,EAAE;;IAEtB,IAAI,CAAC9I,KAAK,GAAG,IAAI+I,YAAY,CAC3B9I,KAAK,CAAC+I,aAAa,CAAClF,OAAO,EAC3B7D,KAAK,CAACgJ,GAAG,EACT,IAAI,CACL;;EAEOP,oBAAoBA,CAAA;IAC5B,OAAO,CAAC,KAAK,EAAE,eAAe,CAAC;;EAEvBxI,eAAeA,CAAA;IACvB,OAAO,IAAI,CAACF,KAAK;;EAGnB2B,iBAAiBA,CAAA;IACf,KAAK,CAACA,iBAAiB,EAAE;IACzB,IAAI,CAAC3B,KAAK,CAACkJ,uBAAuB,EAAE;;EAEtCtH,oBAAoBA,CAAA;IAClB,IAAI,CAAC5B,KAAK,CAACmJ,yBAAyB,EAAE;IACtC,KAAK,CAACvH,oBAAoB,EAAE;;EAG9BgB,MAAMA,CAAA;IACJ,OACEvC,KAAA,CAAAO,aAAA;MACEE,GAAG,EAAE,UAAU,GAAG,IAAI,CAACb,KAAK,CAACgJ,GAAG,CAAC9G,EAAE;MACnClB,SAAS,EAAE,IAAI,CAACjB,KAAK,CAAC0H;IAAU,GAEhCrH,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAsD,EAAO,EAC5EZ,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAyD,EAAO,EAC9E,IAAI,CAAChB,KAAK,CAACmJ,OAAO,CACf;;AAGX;AAEDnH,mBAAmB,CAACC,QAAQ,CAACyB,eAAe,CAC1C,SAAS,EACR1D,KAA+B,IAAI;EAClC,OAAOI,KAAK,CAACO,aAAa,CAACiI,UAAU,EAAE5I,KAAK,CAAC;AAC/C,CAAC,CACF;MC/DYoJ,eAAe;EAC1BlJ,YAAmBmJ,KAAmD;IAAnD,KAAAA,KAAK,GAALA,KAAK;;EACxBC,eAAeA,CAAA;IACb,IAAI,CAACD,KAAK,CAACC,eAAe,EAAE;;;;EAI9BC,cAAcA,CAAA;IACZ,IAAI,CAACF,KAAK,CAACE,cAAc,EAAE;;;EAG7B,IAAIC,YAAYA,CAAA;;IAEd,OAAO,KAAK;;EAEd,IAAIA,YAAYA,CAACC,KAAc;;;EAG/B,IAAI7C,MAAMA,CAAA;IACR,OAAO,IAAI,CAACyC,KAAK,CAACzC,MAAM;;EAE1B,IAAI8C,aAAaA,CAAA;IACf,OAAO,IAAI,CAACL,KAAK,CAACK,aAAa;;EAEjC,IAAIC,OAAOA,CAAA;IACT,OAAO,IAAI,CAACN,KAAK,CAACM,OAAO;;EAE3B,IAAIC,OAAOA,CAAA;IACT,OAAO,IAAI,CAACP,KAAK,CAACO,OAAO;;EAE3B,IAAIC,OAAOA,CAAA;IACT,OAAO,IAAI,CAACR,KAAK,CAACS,WAAW,CAACD,OAAO;;EAEvC,IAAIE,OAAOA,CAAA;IACT,OAAO,IAAI,CAACV,KAAK,CAACS,WAAW,CAACC,OAAO;;AAExC;AAEK,MAAOC,cAAe,SAAQZ,eAAe;EAEjDlJ,YAAmBmJ,KAAsC;IACvD,KAAK,CAACA,KAAK,CAAC;IADK,KAAAA,KAAK,GAALA,KAAK;;EAIxB,IAAIY,YAAYA,CAAA;IACd,OAAO,IAAI,CAACZ,KAAK,CAACY,YAAY;;AAEjC;AC3BD,SAASC,0BAA0BA,CAAClK,KAAqC;EACvE,OAAOA,KAAK,CAACmJ,OAAO;AACtB;AAEA,MAAMgB,sBAAsB,GAAG/J,KAAK,CAACgK,IAAI,CAACF,0BAA0B,CAAC;AACrEC,sBAAsB,CAACE,WAAW,GAAG,wBAAwB;AAEvD,MAAOC,wBAAyB,SAAQrC,mBAG7C;EAGC/H,YAAYF,KAAoC;IAC9C,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACG,OAAO,GAAGC,KAAK,CAACC,SAAS,EAAE;;EAExB6H,WAAWA,CAAClI,KAAoC;IACxD,IAAI,IAAI,CAACD,KAAK,EAAE;MACd,IAAI,CAACA,KAAK,CAACwK,UAAU,CAACvK,KAAK,CAACgH,QAAQ,EAAE,IAAI,CAAC7G,OAAO,CAACqB,OAAO,CAAC;WACtD;MACL,IAAI,CAACgJ,UAAU,GAAG,IAAI,CAACC,uBAAuB,CAACzK,KAAK,CAAC;;;EAG/CyK,uBAAuBA,CAACzK,KAAU;IAC1C,OAAO,IAAI0K,wBAAwB,CACjC1K,KAAK,CAAC+I,aAAa,EACnB/I,KAAK,CAACgH,QAAQ,EACd,IAAI,CACL;;EAEOyB,oBAAoBA,CAAA;IAC5B,OAAO,CAAC,UAAU,EAAE,eAAe,CAAC;;EAEtC,IAAW1I,KAAKA,CAAA;IACd,OAAO,IAAI,CAACyK,UAAU;;EAEdvK,eAAeA,CAAA;IACvB,OAAO,IAAI,CAACF,KAAK;;EAGnBO,aAAaA,CAAA;IACX,MAAMqK,iBAAiB,GAAG,IAAI,CAAC5K,KAAK,CAACoJ,OAAA,CAClCyB,0BAA0B;IAC7B,MAAMC,sBAAsB,GAAG,IAAI,CAACC,mBAAmB,EAAE;IACzD,MAAMC,OAAO,GAAG,IAAI,CAACC,aAAa,CAACL,iBAAiB,CAAC;IACrD,OACEvK,KAAA,CAAAO,aAAA;MACEI,GAAG,EAAE,IAAI,CAACZ,OAAO;MAAA,sCACmB,IAAI,CAACJ,KAAK,CAACoJ,OAAO,CAAC8B,IAAI,IAAI,IAAI;MACnEjK,SAAS,EAAE,IAAI,CAACjB,KAAK,CAACmL,OAAO,EAAE;MAC/BC,aAAa,EAAEC,CAAC,IAAG;QAAGT,iBAAiB,IAAI,IAAI,CAAC5K,KAAK,CAACsL,QAAQ,CAACD,CAAC,CAACtB,WAAW,CAAC;QAAEsB,CAAC,CAAC9B,eAAe,EAAE;MAAC,CAAE;MACrGgC,YAAY,EAAEF,CAAC,IAAIT,iBAAiB,IAAI,IAAI,CAAC5K,KAAK,CAACwL,KAAK,CAACH,CAAC,CAACtB,WAAW,EAAEsB,CAAC,CAAC1B,aAAa,CAAC;MACxF8B,WAAW,EAAEJ,CAAC,IAAIT,iBAAiB,IAAI,IAAI,CAAC5K,KAAK,CAACwL,KAAK,CAACH,CAAC,CAACtB,WAAW,EAAEsB,CAAC,CAAC1B,aAAa;IAAC,GAEtFmB,sBAAsB,EACtBE,OAAO,CACJ;;EAGAU,cAAcA,CAAA;IACtB,OAAO,IAAI;;EAEHT,aAAaA,CAACL,iBAA0B;IAChD,IAAII,OAAO,GAAG,IAAI,CAAChL,KAAK,CAAC2L,mBAAmB,GAAG,IAAI,CAACC,oBAAoB,EAAE,GAAG,IAAI;;IAEjF,OAAO/I,eAAe,CACpBxC,KAAA,CAAAO,aAAA;MACEK,SAAS,EAAE,IAAI,CAACjB,KAAK,CAAC8B,GAAG,EAAE;MAC3BkB,OAAO,EAAGqI,CAAC,IAAK,IAAI,CAACrL,KAAK,CAAC6L,MAAM,CAAC,IAAI,CAAC7L,KAAK,EAAE,IAAIqJ,eAAe,CAACgC,CAAC,CAAC;IAAC,GAErEhL,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAiE,EAAO,EACvFZ,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAkE,EAAO,EACxFZ,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAgE,EAAO,EACtFZ,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAmE,EAAO,EACxF2J,iBAAiB,GAAG,IAAI,CAACkB,YAAY,EAAE,GAAG,IAAI,EAC9Cd,OAAO,EACP,IAAI,CAAChL,KAAK,CAAC2L,mBAAmB,GAAG,IAAI,CAACI,YAAY,EAAE,GAAG,IAAI,CACxD,EACNrJ,SAAS,EAAE;MAAEgJ,cAAc,EAAE,IAAI,CAACA,cAAc;IAAE,CAAE,CAAC;;EAE/CI,YAAYA,CAAA;IACpB,OAAO7J,mBAAmB,CAACC,QAAQ,CAACtB,aAAa,CAAC,qBAAqB,EAAE;MAAEZ,KAAK,EAAE,IAAI,CAACA;IAAK,CAAE,CAAC;;EAEvF+L,YAAYA,CAAA;IACpB,MAAMnB,iBAAiB,GAAG,IAAI,CAAC5K,KAAK,CAACoJ,OAAA,CAClCyB,0BAA0B;IAC7B,OAAOD,iBAAiB,GAAG3I,mBAAmB,CAACC,QAAQ,CAACtB,aAAa,CAAC,qBAAqB,EAAE;MAAEK,SAAS,EAAE,+BAA+B;MAAEjB,KAAK,EAAE,IAAI,CAACA;IAAK,CAAE,CAAC,GAAG,IAAI;;EAE9JgM,wBAAwBA,CAAA;IAChC,IAAI,CAAC,IAAI,CAAChM,KAAK,CAACiM,eAAe,EAAE,OAAO,IAAI;IAC5C,OAAOhK,mBAAmB,CAACC,QAAQ,CAACtB,aAAa,CAAC,qBAAqB,EAAE,IAAI,CAACZ,KAAK,CAACkM,kBAAkB,EAAE,CAAC;;EAGjGnB,mBAAmBA,CAAA;IAC3B,IAAI,CAAC,IAAI,CAAC/K,KAAK,CAACmM,eAAe,EAAE,OAAO,IAAI;IAC5C,MAAM/C,OAAO,GAAG,IAAI,CAACpJ,KAAK,CAACoJ,OAAgC;IAC3D,OACE/I,KAAA,CAAAO,aAAA;MACEI,GAAG,EAAEoL,IAAI,IAAIA,IAAI,KAAK,CAAC,IAAI,CAACpM,KAAK,CAACqM,iBAAiB,GACjDD,IAAI,CAACE,YAAY,CAAC,OAAO,EAAE,EAAE,CAAC,GAAGF,IAAI,CAACG,eAAe,CAAC,OAAO,CAAC,CAC/D;MAAEtL,SAAS,EAAE,IAAI,CAACjB,KAAK,CAACwM;IAAwB,GAE/CpD,OAAO,CAAClG,QAAQ,GACd7C,KAAA,CAAAO,aAAA,CAAC6L,YAAY;MAACrD,OAAO,EAAEA;IAAO,EAAiB,GAC/C/I,KAAA,CAAAO,aAAA;MACEK,SAAS,EAAE,IAAI,CAACjB,KAAK,CAAC0M;IAAuB,GAC7CrM,KAAA,CAAAO,aAAA;MAAMK,SAAS,EAAC;IAAgB,GAAEmI,OAAO,CAAC8B,IAAI,CAAQ,CAClD,CAEN;;EAIAU,oBAAoBA,CAAA;IAC5B,OACEvL,KAAA,CAAAO,aAAA,CAAAP,KAAA,CAAAsM,QAAA,QACEtM,KAAA,CAAAO,aAAA,CAACwJ,sBAAsB;MAAChB,OAAO,EAAE,IAAI,CAACnJ,KAAK,CAACmJ;IAAO,EAAI,EACtD,IAAI,CAACwD,wBAAwB,EAAE,EAC/B,IAAI,CAACZ,wBAAwB,EAAE,CAC/B;;EAGPrK,iBAAiBA,CAAA;IACf,KAAK,CAACA,iBAAiB,EAAE;IACzB,IAAI,CAAC3B,KAAK,CAACwK,UAAU,CAAC,IAAI,CAACvK,KAAK,CAACgH,QAAQ,EAAE,IAAI,CAAC7G,OAAO,CAACqB,OAAO,CAAC;;EAElEmL,wBAAwBA,CAAA;IACtB,IAAI,CAAC,IAAI,CAAC5M,KAAK,CAAC6M,cAAc,EAAE;MAC9B,OAAO,IAAI;;IAEb,OACExM,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAsC,GACnDZ,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAA8B,GAC3CZ,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAwB,GACpC,IAAI,CAACjB,KAAK,CAAC8M,eAAe,CACvB,CACF,CACF;;EAGVlL,oBAAoBA,CAAA;IAClB,KAAK,CAACA,oBAAoB,EAAE;IAC5B,IAAI,CAAC5B,KAAK,CAAC+M,YAAY,EAAE;;AAE5B;AAED9K,mBAAmB,CAACC,QAAQ,CAACyB,eAAe,CAC1C,cAAc,EACb1D,KAAoC,IAAI;EACvC,OAAOI,KAAK,CAACO,aAAa,CAAC2J,wBAAwB,EAAEtK,KAAK,CAAC;AAC7D,CAAC,CACF;AC3JK,MAAO+M,qBAAsB,SAAQ3M,KAAK,CAAC4M,SAA0C;EACzFrK,MAAMA,CAAA;IACJ,IAAI,CAAC,IAAI,CAAC3C,KAAK,CAACD,KAAK,CAACkN,aAAa,EAAE,OAAO,IAAI;IAChD,OACE7M,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAE,yBAAyB;MACvCkM,aAAa,EAAG7D,KAAU,IACxB,IAAI,CAACrJ,KAAK,CAACD,KAAK,CAACmN,aAAa,CAAC7D,KAAK;IAAC,GAGvCjJ,KAAA,CAAAO,aAAA,CAAC0C,OAAO;MAACrC,SAAS,EAAC,4BAA4B;MAACwC,IAAI,EAAE,MAAM;MAAEF,QAAQ,EAAE;IAAgC,EAAY,EACpHlD,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAA2B,GACxCZ,KAAA,CAAAO,aAAA,CAACyE,eAAe;MAACrF,KAAK,EAAE,IAAI,CAACC,KAAK,CAACD,KAAK,CAACoN,kBAAkB;MAAEC,WAAW,EAAE;IAAK,EAAoB,CAC/F,CACF;;AAGX;AAEDpL,mBAAmB,CAACC,QAAQ,CAACyB,eAAe,CAC1C,qBAAqB,EACpB1D,KAAiC,IAAI;EACpC,OAAOI,KAAK,CAACO,aAAa,CAACoM,qBAAqB,EAAE/M,KAAK,CAAC;AAC1D,CAAC,CACF;AC9BK,MAAOqN,qBAAsB,SAAQjN,KAAK,CAAC4M,SAA0C;EACzFrK,MAAMA,CAAA;IACJ,OAAQvC,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAE,IAAI,CAAChB,KAAK,CAACgB,SAAS;MAAEsM,OAAO,EAAGlC,CAAC,IAAK,IAAI,CAACpL,KAAK,CAACD,KAAK,CAAC6L,MAAM,CAAC,IAAI,CAAC5L,KAAK,CAACD,KAAK,EAAE,IAAIqJ,eAAe,CAACgC,CAAQ,CAAC;IAAC,GACpIhL,KAAA,CAAAO,aAAA,CAACyE,eAAe;MAACrF,KAAK,EAAE,IAAI,CAACC,KAAK,CAACD,KAAK,CAACwN,eAAe;MAAEH,WAAW,EAAE;IAAK,EAAoB,CAC5F;;AAET;AAEDpL,mBAAmB,CAACC,QAAQ,CAACyB,eAAe,CAC1C,qBAAqB,EACpB1D,KAAiC,IAAI;EACpC,OAAOI,KAAK,CAACO,aAAa,CAAC0M,qBAAqB,EAAErN,KAAK,CAAC;AAC1D,CAAC,CACF;ACXK,MAAOwN,YAAa,SAAQ1N,iBAA0C;EAC1EQ,aAAaA,CAAA;IACX,MAAMmN,OAAO,GAAG,IAAIC,eAAe,GAChCC,MAAM,CAAC,IAAI,CAAC3N,KAAK,CAACyN,OAAO,EACzBE,MAAM,CAAC,mBAAmB,EAC1BA,MAAM,CAAC,6BAA6B,EAAE,CAAC,CAAC,IAAI,CAAC3N,KAAK,CAAC4N,QAAQ,EAC3DD,MAAM,CAAC,6BAA6B,EAAE,CAAC,CAAC,IAAI,CAAC3N,KAAK,CAAC6N,QAAQ,EAC3DC,QAAQ,EAAE;IACb,IAAI,IAAI,CAAC9N,KAAK,CAACsD,QAAQ,EAAE;MACvB,OAAO,IAAI,CAACyK,UAAU,CAACN,OAAO,CAAC;;IAEjC,OAAO,IAAI,CAACO,gBAAgB,CAACP,OAAO,CAAC;;EAGvCO,gBAAgBA,CAACP,OAAO;IACtB,IAAI,IAAI,CAACzN,KAAK,CAAC6N,QAAQ,EAAE;MACvB,OAAOzN,KAAA,CAAAO,aAAA;QAAMK,SAAS,EAAEyM;MAAO,GAAG,IAAI,CAACzN,KAAK,CAACiO,IAAI,CAAQ;;IAE3D,OACE7N,KAAA,CAAAO,aAAA,CAAAP,KAAA,CAAAsM,QAAA,QACG9J,eAAe,CACdxC,KAAA,CAAAO,aAAA;MACEM,IAAI,EAAC,QAAQ;MACbD,SAAS,EAAEyM,OAAO;MAClB1K,OAAO,EAAGqI,CAAC,IAAI;QACb,IAAI,CAAC,IAAI,CAACpL,KAAK,CAACkO,WAAW,EAAE;UAC3B9C,CAAC,CAAC9B,eAAe,EAAE;;QAErB,IAAI,CAACtJ,KAAK,CAACmO,KAAK,EAAE;MACpB,CAAC;MACDhL,KAAK,EAAE,IAAI,CAACnD,KAAK,CAACmD;IAAK,GAEtB,IAAI,CAACnD,KAAK,CAACiO,IAAI,CACX,CACR,CACA;;EAGPF,UAAUA,CAACN,OAAO;IAChBA,OAAO,IAAI,0BAA0B;IACrC,IAAI,IAAI,CAACzN,KAAK,CAAC6N,QAAQ,EAAE;MACvB,OAAOzN,KAAA,CAAAO,aAAA;QAAMK,SAAS,EAAEyM;MAAO,GAAErN,KAAA,CAAAO,aAAA,CAAC0C,OAAO;QAACG,IAAI,EAAE,MAAM;QAAEF,QAAQ,EAAE,IAAI,CAACtD,KAAK,CAACsD;MAAQ,EAAY,CAAO;;IAE1G,OACElD,KAAA,CAAAO,aAAA,CAAAP,KAAA,CAAAsM,QAAA,QACG9J,eAAe,CACdxC,KAAA,CAAAO,aAAA;MAAMK,SAAS,EAAEyM,OAAO;MACtB1K,OAAO,EAAGqI,CAAC,IAAI;QACb,IAAI,CAAC,IAAI,CAACpL,KAAK,CAACkO,WAAW,EAAE;UAC3B9C,CAAC,CAAC9B,eAAe,EAAE;;QAErB,IAAI,CAACtJ,KAAK,CAACmO,KAAK,EAAE;MACpB,CAAC;MACDhL,KAAK,EAAE,IAAI,CAACnD,KAAK,CAACmD;IAAK,GAEvB/C,KAAA,CAAAO,aAAA,CAAC0C,OAAO;MAACG,IAAI,EAAE,MAAM;MAAEF,QAAQ,EAAE,IAAI,CAACtD,KAAK,CAACsD;IAAQ,EAAY,CAC3D,CACR,CACA;;AAGR;AACDtB,mBAAmB,CAACC,QAAQ,CAACyB,eAAe,CAAC,mBAAmB,EAAG1D,KAAU,IAAI;EAAG,OAAOI,KAAK,CAACO,aAAa,CAAC6M,YAAY,EAAExN,KAAK,CAAC;AAAC,CAAE,CAAC;ACvEjI,MAAOoO,cAAe,SAAQhO,KAAK,CAAC4M,SAAoC;EAC5ErK,MAAMA,CAAA;IACJ,OACEvC,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAiC,GAACZ,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAyB,GACvFZ,KAAA,CAAAO,aAAA,eAAO,IAAI,CAACX,KAAK,CAACiO,IAAI,EAAE,GAAG,CAAQ,EACnC7N,KAAA,CAAAO,aAAA;MAAMK,SAAS,EAAC;IAA+B,GAC7CZ,KAAA,CAAAO,aAAA,CAAC6M,YAAY;MAACW,KAAK,EAAEA,CAAA,KAAM,IAAI,CAACnO,KAAK,CAAC+C,OAAO,EAAE;MAAEkL,IAAI,EAAE,IAAI,CAACjO,KAAK,CAACqO;IAAU,EAAiB,CACxF,CACH,CAAM;;AAEjB;AAEDrM,mBAAmB,CAACC,QAAQ,CAACyB,eAAe,CAC1C,qBAAqB,EACpB1D,KAA2B,IAAI;EAC9B,OAAOI,KAAK,CAACO,aAAa,CAACyN,cAAc,EAAEpO,KAAK,CAAC;AACnD,CAAC,CACF;ACZK,MAAOsO,gCAAiC,SAAQhE,wBAAwB;EAC5EpK,YAAYF,KAAoC;IAC9C,KAAK,CAACA,KAAK,CAAC;;EAEJyK,uBAAuBA,CAACzK,KAAU;IAC1C,OAAO,IAAIuO,gCAAgC,CACzCvO,KAAK,CAAC+I,aAAa,EACnB/I,KAAK,CAACgH,QAAiC,EACvC,IAAI,CACL;;EAEH,IAAWwH,aAAaA,CAAA;IACtB,OAAO,IAAI,CAACzO,KAAyC;;EAEvD,IAAWiH,QAAQA,CAAA;IACjB,OAAO,IAAI,CAACwH,aAAa,CAACxH,QAA8B;;EAG1D2F,wBAAwBA,CAAA;IACtB,MAAM8B,SAAS,GAAI,IAAI,CAACzH,QAAgB,CAACyH,SAAS;IAClD,OACErO,KAAA,CAAAO,aAAA;MACEK,SAAS,EAAC;IAAyC,GACnDZ,KAAA,CAAAO,aAAA,cACEP,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAgC,GAC5C,CAAC,IAAI,CAACwN,aAAa,CAACE,gBAAgB,EAAE,IAAI,EAAE,EAAEjO,GAAG,CAChD,CAACC,IAAe,EAAEiO,KAAa,KAC7BvO,KAAA,CAAAO,aAAA;MACEK,SAAS,EAAE,IAAI,CAACwN,aAAa,CAACI,YAAY,EAAE;MAC5C/N,GAAG,EAAE,mBAAmB8N,KAAK;IAAE,GAE9BE,0BAA0B,CAACC,aAAa,CAAC,IAAI,CAAC9H,QAAQ,CAACZ,MAAqB,EAC3EpE,mBAAmB,CAACC,QAAQ,CAACtB,aAAa,CACxC,IAAI,CAAC6N,aAAa,CAACO,aAAa,EAChC;MACElO,GAAG,EAAEH,IAAI,CAAC+I,KAAK;MACfzC,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBS,UAAU,EAAE,IAAI,CAACT,QAAQ,CAACS,UAAU;MACpCH,aAAa,EAAE,IAAI;MACnB5G,IAAI,EAAEA,IAAI;MACV+N,SAAS,EAAEA,SAAS;MACpBE,KAAK,EAAEA,KAAK;MACZK,SAAS,EAAE,IAAI,CAAChI,QAAQ,CAACyC,KAAK,KAAK/I,IAAI,CAAC+I;KACzC,CACF,EACD,IAAI,CAACzC,QAAQ,EACbtG,IAAI,CACL,CAEJ,CACF,CACG,EACL,IAAI,CAAC8N,aAAa,CAACS,cAAc,GAChC7O,KAAA,CAAAO,aAAA,CAAC6M,YAAY;MACXW,KAAK,EAAE,IAAI,CAACK,aAAa,CAACU,cAAc;MACxCjB,IAAI,EAAE,IAAI,CAACO,aAAa,CAACW,aAAa,EAAE;MACxCjB,WAAW,EAAE;IAAI,EACH,GAChB,IAAI,CAEF,CACF;;AAGX;AAEDlM,mBAAmB,CAACC,QAAQ,CAACyB,eAAe,CAC1C,uBAAuB,EACtB1D,KAAoC,IAAI;EACvC,OAAOI,KAAK,CAACO,aAAa,CAAC2N,gCAAgC,EAAEtO,KAAK,CAAC;AACrE,CAAC,CACF;ACrEK,MAAOoP,6BAA8B,SAAQ9E,wBAAwB;EAC/DG,uBAAuBA,CAACzK,KAAU;IAC1C,OAAO,IAAIqP,6BAA6B,CACtCrP,KAAK,CAAC+I,aAAa,EACnB/I,KAAK,CAACgH,QAAe,EACrB,IAAI,CAAC;;EAET,IAAWsI,UAAUA,CAAA;IACnB,OAAO,IAAI,CAACvP,KAAsC;;EAE1C8L,YAAYA,CAAA;IACpB,OAAQzL,KAAA,CAAAO,aAAA,CAACP,KAAK,CAACsM,QAAQ,QACrBtM,KAAA,CAAAO,aAAA;MACE4O,IAAI,EAAC,MAAM;MAAA,eACC,MAAM;MAClBC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE,IAAI,CAACH,UAAU,CAACI,aAAa;MACrC1O,SAAS,EAAC,uBAAuB;MACjCE,KAAK,EAAE;QACLyO,QAAQ,EAAE,UAAU;QACpBC,OAAO,EAAE,CAAC;QACVC,KAAK,EAAE,KAAK;QACZC,MAAM,EAAE,KAAK;QACbC,QAAQ,EAAE;;IACX,EACD,EAED,KAAK,CAAClE,YAAY,EAAE,CACN;;EAEnBmE,wBAAwBA,CAAA;IACtB,OAAQ5P,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAyC,GAC9DZ,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAA6B,GAC1CZ,KAAA,CAAAO,aAAA,CAACsP,yBAAyB,OAA6B,CACnD,CACF;;EAERC,kBAAkBA,CAAA;IAChB,OAAQ9P,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAA6B,GACjD,IAAI,CAACjB,KAAK,CAACoQ,SAAS,GAAGvN,eAAe,CAACxC,KAAA,CAAAO,aAAA;MACtCK,SAAS,EAAC,oBAAoB;MAC9B+B,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACuM,UAAU,CAACc,UAAU,CAAC,IAAI,CAACd,UAAU;IAAC,GAE1DlP,KAAA,CAAAO,aAAA,CAAC0C,OAAO;MAACG,IAAI,EAAE,MAAM;MAAEF,QAAQ,EAAE;IAAiB,EAAY,CACzD,CAAC,GAAG,IAAI,CACX;;EAERqJ,wBAAwBA,CAAA;IACtB,OAAO,IAAI,CAAC2C,UAAU,CAACe,WAAW,GAAG,IAAI,CAACL,wBAAwB,EAAE,GAAG,IAAI,CAACE,kBAAkB,EAAE;;EAExFI,gBAAgBA,CAAA;IACxB,OAAO,CAAC,IAAI,CAACvQ,KAAK,EAAE,IAAI,CAACuP,UAAU,CAACiB,qBAAqB,CAAC;;EAGlD5E,oBAAoBA,CAAA;IAC5B,IAAI,IAAI,CAAC2D,UAAU,CAACkB,gBAAgB,EAAE;MACpC,MAAMC,YAAY,GAAGxJ,oBAAoB,CAAChF,QAAQ,CAACiF,cAAc,CAAC,MAAM,EAAE;QACxErD,OAAO,EAAE,IAAI,CAACyL,UAAU,CAACtI,QAAQ,CAACZ,MAAM;QACxCkB,aAAa,EAAE,KAAK;QACpBN,QAAQ,EAAE,IAAI,CAACsI,UAAU,CAACiB;MAC3B,EAAC;MACF,OAAQnQ,KAAA,CAAAO,aAAA,CAAAP,KAAA,CAAAsM,QAAA,QACL+D,YAAY,CACZ;WACE;MACL,OACErQ,KAAA,CAAAO,aAAA,CAAAP,KAAA,CAAAsM,QAAA,QACG,IAAI,CAAC1M,KAAK,CAACmJ,OAAO,EAClB,IAAI,CAACwD,wBAAwB,EAAE,CAC/B;;;AAIV;AAED3K,mBAAmB,CAACC,QAAQ,CAACyB,eAAe,CAC1C,oBAAoB,EACnB1D,KAAoC,IAAI;EACvC,OAAOI,KAAK,CAACO,aAAa,CAACyO,6BAA6B,EAAEpP,KAAK,CAAC;AAClE,CAAC,CACF;ACrFK,MAAO0Q,8BAA+B,SAAQzI,mBAAuD;EAG/FC,WAAWA,CAAClI,KAAU;IAC9B,IAAI,CAACwK,UAAU,GAAG,IAAI,CAACC,uBAAuB,CAACzK,KAAK,CAAC;;EAE7CyK,uBAAuBA,CAACzK,KAAU;IAC1C,OAAO,IAAI2Q,8BAA8B,CACvC3Q,KAAK,CAAC+I,aAAa,EACnB/I,KAAK,CAACgH,QAAe,EACrB,IAAI,CACL;;EAEOyB,oBAAoBA,CAAA;IAC5B,OAAO,CAAC,UAAU,EAAE,eAAe,CAAC;;EAEtC,IAAWmI,WAAWA,CAAA;IACpB,OAAO,IAAI,CAAC7Q,KAAuC;;EAErD,IAAWA,KAAKA,CAAA;IACd,OAAO,IAAI,CAACyK,UAAU;;EAEdvK,eAAeA,CAAA;IACvB,OAAO,IAAI,CAACF,KAAK;;EAETO,aAAaA,CAAA;IACrB,MAAMP,KAAK,GAAG,IAAI,CAAC6Q,WAAW;IAC9B,OAAQxQ,KAAA,CAAAO,aAAA,CAAAP,KAAA,CAAAsM,QAAA,QACNtM,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAA6B,GAC1CZ,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAEjB,KAAK,CAAC8Q;IAAkB,GACrC9Q,KAAK,CAAC+Q,WAAW,GAAGlO,eAAe,CAACxC,KAAA,CAAAO,aAAA;MACnCM,IAAI,EAAC,QAAQ;MACbD,SAAS,EAAEjB,KAAK,CAACgR,gBAAgB;MAAA,cACrBhR,KAAK,CAACiR,aAAa;MAC/BjO,OAAO,EAAEA,CAAA,KAAMhD,KAAK,CAACkR,UAAU,CAAClR,KAAK;IAAC,GAEtCK,KAAA,CAAAO,aAAA,CAAC0C,OAAO;MAACG,IAAI,EAAE,MAAM;MAAEF,QAAQ,EAAE,mBAAmB;MAAEH,KAAK,EAAEpD,KAAK,CAACiR;IAAa,EAAY,CACvF,CAAC,GAAG,IAAI,EACdjR,KAAK,CAACmR,QAAQ,GAAGtO,eAAe,CAACxC,KAAA,CAAAO,aAAA;MAChCM,IAAI,EAAC,QAAQ;MACbD,SAAS,EAAEjB,KAAK,CAACoR,aAAa;MAAA,cAClBpR,KAAK,CAACqR,UAAU;MAC5BrO,OAAO,EAAEA,CAAA,KAAMhD,KAAK,CAACsR,OAAO,CAACtR,KAAK;IAAC,GAEnCK,KAAA,CAAAO,aAAA,CAAC0C,OAAO;MAACG,IAAI,EAAE,MAAM;MAAEF,QAAQ,EAAE,gBAAgB;MAAEH,KAAK,EAAEpD,KAAK,CAACqR;IAAU,EAAY,CACjF,CAAC,GAAG,IAAI,CACX,EACL,IAAI,CAACpR,KAAK,CAACmJ,OAAO,CACf,CACL;;AAEN;AAEDnH,mBAAmB,CAACC,QAAQ,CAACyB,eAAe,CAC1C,qBAAqB,EACpB1D,KAAoC,IAAI;EACvC,OAAOI,KAAK,CAACO,aAAa,CAAC+P,8BAA8B,EAAE1Q,KAAK,CAAC;AACnE,CAAC,CACF;AACDgC,mBAAmB,CAACC,QAAQ,CAACyB,eAAe,CAC1C,6BAA6B,EAC5B1D,KAAoC,IAAI;EACvC,OAAOI,KAAK,CAACO,aAAa,CAAC+P,8BAA8B,EAAE1Q,KAAK,CAAC;AACnE,CAAC,CACF;AC7DK,MAAOsR,8BAA+B,SAAQhH,wBAAwB;EAChEG,uBAAuBA,CAACzK,KAAU;IAC1C,OAAO,IAAI0K,wBAAwB,CACjC1K,KAAK,CAAC+I,aAAa,EACnB/I,KAAK,CAACgH,QAAe,EACrB,IAAI,CACL;;EAEH,IAAWuK,WAAWA,CAAA;IACpB,OAAO,IAAI,CAACxR,KAAiC;;EAErC4L,oBAAoBA,CAAA;IAC5B,OACEvL,KAAA,CAAAO,aAAA;MACEK,SAAS,EAAE;IAAqB,GAE/B,IAAI,CAAChB,KAAK,CAACmJ,OAAO,CACf;;AAGX;AAEDnH,mBAAmB,CAACC,QAAQ,CAACyB,eAAe,CAC1C,qBAAqB,EACpB1D,KAAoC,IAAI;EACvC,OAAOI,KAAK,CAACO,aAAa,CAAC2Q,8BAA8B,EAAEtR,KAAK,CAAC;AACnE,CAAC,CACF;AC1BK,MAAOwR,4BAA6B,SAAQvJ,mBAGjD;EAEWC,WAAWA,CAAClI,KAAU;IAC9B,IAAI,CAACD,KAAK,GAAG,IAAI2K,wBAAwB,CACvC1K,KAAK,CAAC+I,aAAa,EACnB/I,KAAK,CAACgH,QAAQ,EACd,IAAI,CACL;;EAEO/G,eAAeA,CAAA;IACvB,OAAO,IAAI,CAACF,KAAK;;EAET0I,oBAAoBA,CAAA;IAC5B,OAAO,CAAC,UAAU,EAAE,eAAe,CAAC;;EAEtC9F,MAAMA,CAAA;IACJ,OACEvC,KAAA,CAAAO,aAAA,CAACP,KAAK,CAACsM,QAAQ,QACbtM,KAAA,CAAAO,aAAA;MAAA,sCACsC,IAAI,CAACZ,KAAK,CAACoJ,OAAO,CAAC8B,IAAI;MAC3DjK,SAAS,EAAE;IAAuB,GAElCZ,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAE;IAAwD,GACrE,IAAI,CAAChB,KAAK,CAACmJ,OAAO,CACf,CACF,CACS;;AAGtB;AAEDnH,mBAAmB,CAACC,QAAQ,CAACyB,eAAe,CAC1C,mBAAmB,EAClB1D,KAAoC,IAAI;EACvC,OAAOI,KAAK,CAACO,aAAa,CAAC6Q,4BAA4B,EAAExR,KAAK,CAAC;AACjE,CAAC,CACF;ACzCK,MAAOyR,oCAAqC,SAAQxJ,mBAGzD;EAEWC,WAAWA,CAAClI,KAAU;IAC9B,IAAI,CAACD,KAAK,GAAG,IAAI2K,wBAAwB,CACvC1K,KAAK,CAAC+I,aAAa,EACnB/I,KAAK,CAACgH,QAAQ,EACd,IAAI,CACL;;EAEOyB,oBAAoBA,CAAA;IAC5B,OAAO,CAAC,UAAU,EAAE,eAAe,CAAC;;EAE5BxI,eAAeA,CAAA;IACvB,OAAO,IAAI,CAACF,KAAK;;EAEnB4C,MAAMA,CAAA;IACJ,MAAMqE,QAAQ,GAAG,IAAI,CAAChH,KAAK,CAACgH,QAA8B;IAC1D,MAAMyH,SAAS,GAAI,IAAI,CAACzO,KAAK,CAACgH,QAAgB,CAACyH,SAAS;IACxD,OACErO,KAAA,CAAAO,aAAA,CAACP,KAAK,CAACsM,QAAQ,QACbtM,KAAA,CAAAO,aAAA;MAAA,sCACsC,IAAI,CAACZ,KAAK,CAACoJ,OAAO,CAAC8B,IAAI;MAC3DjK,SAAS,EAAE;IAAuB,GAElCZ,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAE;IAAwD,GACrE,IAAI,CAAChB,KAAK,CAACmJ,OAAO,EAEnB/I,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAgC,GAC5CgG,QAAQ,CAAC0K,cAAc,CAACjR,GAAG,CAC1B,CAACC,IAAe,EAAEiO,KAAa,KAC7BvO,KAAA,CAAAO,aAAA;MACEK,SAAS,EAAC,+BAA+B;MACzCH,GAAG,EAAE,mBAAmB8N,KAAK;IAAE,GAE9BE,0BAA0B,CAACC,aAAa,CAAC9H,QAAQ,CAACZ,MAAqB,EACtEpE,mBAAmB,CAACC,QAAQ,CAACtB,aAAa,CACxC,wBAAwB,EACxB;MACEqG,QAAQ,EAAEA,QAAQ;MAClBS,UAAU,EAAET,QAAQ,CAACS,UAAU;MAC/BH,aAAa,EAAE,IAAI;MACnB5G,IAAI,EAAEA,IAAI;MACV+N,SAAS,EAAEA,SAAS;MACpBE,KAAK,EAAEA,KAAK;MACZK,SAAS,EAAEhI,QAAQ,CAACyC,KAAK,KAAK/I,IAAI,CAAC+I;IACpC,EACF,EACDzC,QAAQ,EACRtG,IAAI,CACL,CAEJ,CACF,CACG,CACF,CACF,CACS;;AAGtB;AAEDsB,mBAAmB,CAACC,QAAQ,CAACyB,eAAe,CAC1C,4BAA4B,EAC3B1D,KAAoC,IAAI;EACvC,OAAOI,KAAK,CAACO,aAAa,CAAC8Q,oCAAoC,EAAEzR,KAAK,CAAC;AACzE,CAAC,CACF;ACpDD,MAAM2R,kBAAkB,GAAGvR,KAAK,CAACgK,IAAI,CAAC,CAAC;EAAEwH,IAAI;EAAExL,MAAM;EAAEvC;AAAO,CAI7D,KAAI;EACH,OAAOzD,KAAA,CAAAO,aAAA,CAACkR,UAAU;IAACD,IAAI,EAAEA,IAAI;IAAExL,MAAM,EAAEA,MAAM;IAAEvC,OAAO,EAAEA;EAAO,EAAI;AACrE,CAAC,CAAC;AACF8N,kBAAkB,CAACtH,WAAW,GAAG,oBAAoB;AAE/C,MAAOyH,0BAA2B,SAAQ7J,mBAG/C;EAGC/H,YAAYF,KAAuC;IACjD,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACG,OAAO,GAAGC,KAAK,CAACC,SAAS,EAAE;;EAExB6H,WAAWA,CAAClI,KAAuC;IAC3D,IAAI,IAAI,CAACD,KAAK,EAAE;MACd,IAAI,CAACA,KAAK,CAACwK,UAAU,CAACvK,KAAK,CAAC4R,IAAI,EAAE,IAAI,CAACzR,OAAO,CAACqB,OAAO,CAAC;;IAEzD,IAAI,CAACzB,KAAK,GAAG,IAAI,CAACgS,iBAAiB,CAAC/R,KAAK,CAAC6D,OAAO,EAAE7D,KAAK,CAAC4R,IAAI,CAAC;IAC9D,IAAI,CAAC7R,KAAK,CAACiS,OAAO,GAAG,IAAI,CAAChS,KAAK,CAACgS,OAAO;;EAE/BD,iBAAiBA,CAAClO,OAA2B,EAAE+N,IAAe;IACtE,OAAO,IAAIK,WAAW,CAACpO,OAAO,EAAE+N,IAAI,CAAC;;EAEvCzJ,qBAAqBA,CAACC,SAAc,EAAEC,SAAc;IAClD,MAAM6J,GAAG,GAAG,KAAK,CAAC/J,qBAAqB,CAACC,SAAS,EAAEC,SAAS,CAAC;IAC7D,IAAI,IAAI,CAACtI,KAAK,EAAE;MACd,IAAI,CAACA,KAAK,CAACiS,OAAO,GAAG,IAAI,CAAChS,KAAK,CAACgS,OAAO;;IAEzC,OAAOE,GAAG;;EAEL9Q,kBAAkBA,CAACC,SAAc,EAAEC,SAAc;IACtD,KAAK,CAACF,kBAAkB,CAACC,SAAS,EAAEC,SAAS,CAAC;;EAEtCmH,oBAAoBA,CAAA;IAC5B,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC;;EAElBxI,eAAeA,CAAA;IACvB,OAAO,IAAI,CAACF,KAAK;;EAEnB2B,iBAAiBA,CAAA;IACf,KAAK,CAACA,iBAAiB,EAAE;IACzB,IAAI,CAAC3B,KAAK,CAACwK,UAAU,CAAC,IAAI,CAACvK,KAAK,CAAC4R,IAAI,EAAE,IAAI,CAACzR,OAAO,CAACqB,OAAO,CAAC;IAC5D,IAAI,CAACzB,KAAK,CAACiS,OAAO,GAAG,IAAI,CAAChS,KAAK,CAACgS,OAAO;;EAEzCrQ,oBAAoBA,CAAA;IAClB,KAAK,CAACA,oBAAoB,EAAE;IAC5B,IAAI,CAAC5B,KAAK,CAAC+M,YAAY,EAAE;;EAEjBqF,SAASA,CAAA;IACjB,OAAO,KAAK,CAACA,SAAS,EAAE;;EAE1B7R,aAAaA,CAAA;IACX,IAAI,CAAC,IAAI,CAACN,KAAK,CAAC4R,IAAI,EAAE,OAAO,IAAI;IACjC,OACEhP,eAAe,CAACxC,KAAA,CAAAO,aAAA;MACdI,GAAG,EAAE,IAAI,CAACZ,OAAO;MACjB+B,EAAE,EAAE,IAAI,CAAClC,KAAK,CAAC4R,IAAI,CAAC1P,EAAE;MAAA,mCACW,IAAI,CAACnC,KAAK,CAACqS,cAAc;MAC1DpR,SAAS,EAAE,oBAAoB,GAAG,IAAI,CAACjB,KAAK,CAAC8B,GAAG;MAChDkB,OAAO,EAAGqI,CAAC,IAAI;QACb,OAAO,IAAI,CAACrL,KAAK,CAAC6L,MAAM,CAAC,IAAI,CAAC7L,KAAK,EAAE,IAAIqJ,eAAe,CAACgC,CAAC,CAAC,CAAC;MAC9D,CAAC;MACDD,aAAa,EAAEC,CAAC,IAAI,IAAI,CAACrL,KAAK,CAACsL,QAAQ,CAACD,CAAC,CAACtB,WAAW,CAAC;MACtDwB,YAAY,EAAGF,CAAC,IAAK,IAAI,CAACrL,KAAK,CAACwL,KAAK,CAACH,CAAC,CAACtB,WAAW,EAAEsB,CAAC,CAAC1B,aAAa,CAAC;MACrE8B,WAAW,EAAGJ,CAAC,IAAK,IAAI,CAACrL,KAAK,CAACwL,KAAK,CAACH,CAAC,CAACtB,WAAW,EAAEsB,CAAC,CAAC1B,aAAa;IAAC,GAEpEtJ,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAgE,EAAO,EACtFZ,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAmE,EAAO,EACxF,IAAI,CAACgK,aAAa,EAAE,EACpB,IAAI,CAACqH,iBAAiB,EAAE,EACxB,IAAI,CAACxG,YAAY,EAAE,EACnB,IAAI,CAACC,YAAY,EAAE,CAChB,CAAC;;EAGDuG,iBAAiBA,CAAA;IACzB,IAAI,CAAC,IAAI,CAACtS,KAAK,CAACuS,eAAe,EAAE,OAAO,IAAI;IAC5C,OACElS,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAA6B,GAC1CZ,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAA8B,GAC3CZ,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAwB,GAAE,IAAI,CAACjB,KAAK,CAAC8M,eAAe,CAAO,CACtE,CACF;;EAGA7B,aAAaA,CAAA;IACrB,IAAI,CAAC,IAAI,CAACjL,KAAK,CAACwS,iBAAiB,EAAE;MACjC,OAAOnS,KAAA,CAAAO,aAAA;QAAKK,SAAS,EAAE;MAA2B,GAAEZ,KAAA,CAAAO,aAAA,CAACsP,yBAAyB,OAA6B,CAAM;;IAEnH,OACE7P,KAAA,CAAAO,aAAA,CAACgR,kBAAkB;MACjBC,IAAI,EAAE,IAAI,CAAC5R,KAAK,CAAC4R,IAAI;MACrBxL,MAAM,EAAE,IAAI,CAACpG,KAAK,CAACoG,MAAM;MACzBvC,OAAO,EAAE,IAAI,CAAC7D,KAAK,CAAC6D;IAAO,EAC3B;;EAGIgI,YAAYA,CAAA;IACpB,MAAM2G,OAAO,GAAIpS,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAA2B,GACzDZ,KAAA,CAAAO,aAAA,CAACyE,eAAe;MAACrF,KAAK,EAAE,IAAI,CAACA,KAAK,CAACwN;IAAe,EAAoB,EACpE,IAAI,CAACxN,KAAK,CAACoN,kBAAkB,CAACsF,UAAU,GAAGrS,KAAA,CAAAO,aAAA,CAACyE,eAAe;MAACrF,KAAK,EAAE,IAAI,CAACA,KAAK,CAACoN;IAAkB,EAAoB,GAAG,IAAI,CACxH;IACP,IAAI,IAAI,CAACpN,KAAK,CAACiS,OAAO,IAAI,CAAC,IAAI,CAACjS,KAAK,CAACkN,aAAa,EAAE;MACnD,OAAOuF,OAAO;;IAEhB,OACEpS,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAE,yBAAyB;MACvCkM,aAAa,EAAG7D,KAAU,IAAK,IAAI,CAACtJ,KAAK,CAACmN,aAAa,CAAC7D,KAAK;IAAC,GAE9DjJ,KAAA,CAAAO,aAAA,CAAC0C,OAAO;MAACrC,SAAS,EAAC,4BAA4B;MAACwC,IAAI,EAAE,MAAM;MAAEF,QAAQ,EAAE;IAAgC,EAAY,EACnHkP,OAAO,CACJ;;EAGA1G,YAAYA,CAAA;IACpB,OAAO1L,KAAA,CAAAO,aAAA,CAACyE,eAAe;MAACrF,KAAK,EAAE,IAAI,CAACA,KAAK,CAAC2S;IAAgB,EAAoB;;AAEjF;AAED1Q,mBAAmB,CAACC,QAAQ,CAACyB,eAAe,CAAC,UAAU,EAAG1D,KAAK,IAAI;EACjE,OAAOI,KAAK,CAACO,aAAa,CAACmR,0BAA0B,EAAE9R,KAAK,CAAC;AAC/D,CAAC,CAAC;AC5II,MAAO2S,0BAA2B,SAAQ7S,iBAAqF;EACnI,IAAWC,KAAKA,CAAA;IACd,OAAO,IAAI,CAACC,KAAK,CAACU,IAAI,CAAC2F,IAAI;;EAEnBuM,kBAAkBA,CAAA;IAC1B,MAAMC,yBAAyB,GAAG,IAAI,CAAC9S,KAAK,CAAC8S,yBAAyB;IACtE,OAAOjQ,eAAe,CAACxC,KAAA,CAAAO,aAAA;MACrB4O,IAAI,EAAC,QAAQ;MACbxM,OAAO,EAAGqI,CAAC,IAAI;QACbA,CAAC,CAAC9B,eAAe,EAAE;QACnBuJ,yBAAyB,CAAC7P,MAAM,EAAE;MACpC,CAAC;MACDhC,SAAS,EAAC,qCAAqC;MAC/CmC,KAAK,EAAE,IAAI,CAACpD,KAAK,CAAC+S,kBAAkB;MACpC7R,IAAI,EAAC;IAAQ,GAEbb,KAAA,CAAAO,aAAA;MAAMK,SAAS,EAAC;IAA0C,GACxDZ,KAAA,CAAAO,aAAA,CAAC0C,OAAO;MACNC,QAAQ,EAAEuP,yBAAyB,CAACvP,QAAQ;MAC5CE,IAAI,EAAE,MAAM;MACZL,KAAK,EAAE,IAAI,CAACpD,KAAK,CAAC+S;IAAkB,EAC3B,CACN,EACN,IAAI,CAAC9S,KAAK,CAAC+S,WAAW,KAAKtQ,SAAS,IAAI,IAAI,CAACzC,KAAK,CAAC+S,WAAW,GAC7D3S,KAAA,CAAAO,aAAA,CAACqS,KAAK;MAACjT,KAAK,EAAE8S,yBAAyB,CAACI;IAAU,KAChD,IAAI,CACD,CAAC;;EAEF3S,aAAaA,CAAA;IACrB,MAAM4S,cAAc,GAAG,IAAI,CAAClT,KAAK,CAACmT,WAAW,IAAI,SAAS;IAC1D,OAAO/S,KAAA,CAAAO,aAAA,CAAAP,KAAA,CAAAsM,QAAA,QACJ9J,eAAe,CAACxC,KAAA,CAAAO,aAAA;MACfK,SAAS,EAAE,gCAAgC,GAAGkS,cAAc;MAC5DnQ,OAAO,EAAGqI,CAAC,IAAI;QACbA,CAAC,CAAC9B,eAAe,EAAE;QACnB,IAAI,CAACvJ,KAAK,CAACqT,cAAc,CAAC,IAAI,CAACrT,KAAK,EAAE,IAAIqJ,eAAe,CAACgC,CAAC,CAAC,CAAC;OAC9D;MACDI,WAAW,EAAGJ,CAAC,IAAK,IAAI,CAACrL,KAAK,CAACsT,YAAY,IAAI,IAAI,CAACtT,KAAK,CAACsT,YAAY,CAACjI,CAAC,CAACtB,WAAW,EAAEsB,CAAC,CAAC1B,aAAa;IAAC,GAEtGtJ,KAAA,CAAAO,aAAA,CAAC0C,OAAO;MACNrC,SAAS,EAAE,kCAAkC;MAC7CsC,QAAQ,EAAE,gBAAgB;MAC1BE,IAAI,EAAE;IAAM,EACH,EACXpD,KAAA,CAAAO,aAAA;MAAMK,SAAS,EAAC;IAA+B,GAC5C,IAAI,CAACjB,KAAK,CAAC+S,kBAAkB,CACzB,EACN,IAAI,CAAC9S,KAAK,CAAC+S,WAAW,KAAK,KAAK,GAAG,IAAI,CAACH,kBAAkB,EAAE,GAAG,IAAI,CAChE,CAAC,EACN,IAAI,CAAC5S,KAAK,CAAC+S,WAAW,KAAK,KAAK,GAAG,IAAI,CAACH,kBAAkB,EAAE,GAAG,IAAI,CACnE;;AAEN;AAED5Q,mBAAmB,CAACC,QAAQ,CAACyB,eAAe,CAAC,0BAA0B,EAAG1D,KAAK,IAAI;EACjF,OAAOI,KAAK,CAACO,aAAa,CAACgS,0BAA0B,EAAE3S,KAAK,CAAC;AAC/D,CAAC,CAAC;ACrDI,MAAOsT,qBAAsB,SAAQhJ,wBAAwB;EACjEqC,wBAAwBA,CAAA;IACtB,IAAI,CAAC,IAAI,CAAC5M,KAAK,CAAC6M,cAAc,EAAE;MAC9B,OAAO,IAAI;;IAEb,OACExM,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAsC,GACnDZ,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAA8B,GAC3CZ,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAwB,GACpC,IAAI,CAACjB,KAAK,CAAC8M,eAAe,CACvB,EACL,IAAI,CAAC9M,KAAK,CAACwT,qBAAqB,GAAG3Q,eAAe,CAACxC,KAAA,CAAAO,aAAA;MAClDK,SAAS,EAAC,+CAA+C;MACzD+B,OAAO,EAAGqI,CAAC,IAAI;QACbA,CAAC,CAAC9B,eAAe,EAAE;QACnB,IAAI,CAACvJ,KAAK,CAACqT,cAAc,EAAE;;IAC5B,GAEDhT,KAAA,CAAAO,aAAA,CAAC0C,OAAO;MACNrC,SAAS,EAAE,kCAAkC;MAC7CsC,QAAQ,EAAE,gBAAgB;MAC1BE,IAAI,EAAE;IAAM,EACH,EACXpD,KAAA,CAAAO,aAAA;MAAMK,SAAS,EAAC;IAA+B,GAC5C,IAAI,CAACjB,KAAK,CAAC+S,kBAAkB,CACzB,CACH,CAAC,GAAG,IAAI,CACV,CACF;;EAGArH,cAAcA,CAAA;IACtB,OAAO,IAAI;;EAEHK,YAAYA,CAAA;IACpB,OAAQ1L,KAAA,CAAAO,aAAA,CAACP,KAAK,CAACsM,QAAQ,QACpB,CAAC,IAAI,CAAC3M,KAAK,CAAC6M,cAAc,IAAI,IAAI,CAAC7M,KAAK,CAACoJ,OAAO,CAACqK,OAAO,IAAI,IAAI,CAACzT,KAAK,CAACwT,qBAAqB,GAC3FnT,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAuC,GACpDZ,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAyC,GACtDZ,KAAA,CAAAO,aAAA,CAACqS,KAAK;MAACjT,KAAK,EAAE,IAAI,CAACA,KAAK,CAAC8S,yBAAyB,CAACI;IAAU,EAAU,CACnE,EACN7S,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAqC,GAClDZ,KAAA,CAAAO,aAAA,CAACgS,0BAA0B;MAACjS,IAAI,EAAE;QAAE2F,IAAI,EAAE,IAAI,CAACtG;MAAK,CAAS;MAAEoT,WAAW,EAAE,mBAAmB;MAAEJ,WAAW,EAAE;IAAK,EAAI,CACnH,CACF,GAAI,IAAI,EAEf,KAAK,CAACjH,YAAY,EAAE,CACN;;AAEpB;AAED9J,mBAAmB,CAACC,QAAQ,CAACyB,eAAe,CAC1C,WAAW,EACV1D,KAAoC,IAAI;EACvC,OAAOI,KAAK,CAACO,aAAa,CAAC2S,qBAAqB,EAAEtT,KAAK,CAAC;AAC1D,CAAC,CACF;AC9DK,MAAOyT,kBAAmB,SAAQxL,mBAAkD;EAGxF/H,YAAYF,KAA+B;IACzC,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACG,OAAO,GAAGC,KAAK,CAACC,SAAS,EAAE;;EAExB6H,WAAWA,CAAClI,KAAU;IAC9B,IAAI0T,QAAQ,GAAmB,IAAI;IACnC,IAAI,CAAC,CAAC,IAAI,CAAC3T,KAAK,EAAE;MAChB2T,QAAQ,GAAG,IAAI,CAAC3T,KAAK,CAAC4T,IAAI;;IAE5B,IAAI,CAAC5T,KAAK,GAAG,IAAI6T,kBAAkB,CAAC5T,KAAK,CAACqG,IAAI,EAAEqN,QAAQ,CAAC;;EAEjDjL,oBAAoBA,CAAA;IAC5B,OAAO,CAAC,MAAM,CAAC;;EAEPxI,eAAeA,CAAA;IACvB,OAAO,IAAI,CAACF,KAAK;;EAEnB2B,iBAAiBA,CAAA;IACf,KAAK,CAACA,iBAAiB,EAAE;IACzB,IAAI,CAAC3B,KAAK,CAAC4T,IAAI,GAAG,IAAI,CAACxT,OAAO,CAACqB,OAAO;;EAExC0O,kBAAkBA,CAAA;IAChB,OAAOtN,eAAe,CAACxC,KAAA,CAAAO,aAAA;MACrBK,SAAS,EAAC,oBAAoB;MAC9B+B,OAAO,EAAEA,CAAA,KAAM,IAAI,CAAChD,KAAK,CAACqQ,UAAU,CAAC,IAAI,CAACrQ,KAAK;IAAC,GAEhDK,KAAA,CAAAO,aAAA,CAAC0C,OAAO;MAACG,IAAI,EAAE,MAAM;MAAEF,QAAQ,EAAE;IAAiB,EAAY,CACzD,CAAC;;EAEVuQ,iBAAiBA,CAAA;IACf,OAAOjR,eAAe,CAACxC,KAAA,CAAAO,aAAA;MACrBK,SAAS,EAAC,+CAA+C;MACzD+B,OAAO,EAAEA,CAAA,KAAM,IAAI,CAAChD,KAAK,CAAC+T,MAAM,CAAC,IAAI,CAAC/T,KAAK;IAAC,GAE5CK,KAAA,CAAAO,aAAA,CAAC0C,OAAO;MAACG,IAAI,EAAE,MAAM;MAAEF,QAAQ,EAAE;IAAY,EAAY,CACpD,CAAC;;EAEVyQ,aAAaA,CAAA;IACX,OAAQ3T,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAA+C,GACnE,IAAI,CAACkP,kBAAkB,EAAE,EACzB,IAAI,CAAC2D,iBAAiB,EAAE,CACrB;;EAERG,WAAWA,CAAA;IACT,OAAO5T,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAE,IAAI,CAACjB,KAAK,CAACkU;IAAY,GAC3C,IAAI,CAACF,aAAa,EAAE,EACrB3T,KAAA,CAAAO,aAAA,CAACuT,SAAS;MAAC7N,IAAI,EAAE,IAAI,CAACrG,KAAK,CAACqG,IAAI,CAACD;IAAM,EAAc,CACjD;;EAER+N,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAACpU,KAAK,CAACoQ,SAAS,IAAI,CAAC,IAAI,CAACpQ,KAAK,CAACsQ,WAAW,GAAGzN,eAAe,CAACxC,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC,4BAA4B;MAAC+B,OAAO,EAAEA,CAAA,KAAM,IAAI,CAAChD,KAAK,CAACqQ,UAAU,CAAC,IAAI,CAACrQ,KAAK;IAAC,GAAEK,KAAA,CAAAO,aAAA,cAAKP,KAAA,CAAAO,aAAA;MAAKyT,SAAS,EAAC;IAAmB,EAAO,CAAM,CAAM,CAAC,GAAG,IAAI;;EAE3OC,WAAWA,CAAA;IACT,OAAOjU,KAAA,CAAAO,aAAA;MAAA,eAAmB,MAAM;MAAC4O,IAAI,EAAC,MAAM;MAACC,QAAQ,EAAE,EAAE;MAAEC,MAAM,EAAE,IAAI,CAAC1P,KAAK,CAAC2P,aAAa;MAAE1O,SAAS,EAAC;IAAuB,EAAG;;EAEnIsT,sBAAsBA,CAAA;IACpB,OAAOlU,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAyB,GAACZ,KAAA,CAAAO,aAAA,CAACsP,yBAAyB,OAA6B,CAAM;;EAE/GtN,MAAMA,CAAA;IACJ,IAAIoI,OAAO,GAAsB,IAAI;IACrC,IAAI,IAAI,CAAChL,KAAK,CAACqG,MAAM,CAACmO,OAAO,CAACC,YAAY,IAAI,CAAC,IAAI,CAACzU,KAAK,CAACsQ,WAAW,EAAE;MACrEtF,OAAO,GAAG,IAAI,CAACiJ,WAAW,EAAE;WACvB,IAAI,IAAI,CAACjU,KAAK,CAACsQ,WAAW,EAAE;MACjCtF,OAAO,GAAG,IAAI,CAACuJ,sBAAsB,EAAE;WAClC;MACLvJ,OAAO,GAAG,IAAI,CAACoJ,iBAAiB,EAAE;;IAEpC,OACE/T,KAAA,CAAAO,aAAA;MAAKI,GAAG,EAAE,IAAI,CAACZ,OAAO;MAAEa,SAAS,EAAC;IAAgB,GAC/C,IAAI,CAACqT,WAAW,EAAE,EAClBtJ,OAAO,CACJ;;AAGX;AAED/I,mBAAmB,CAACC,QAAQ,CAACyB,eAAe,CAAC,gBAAgB,EAC1D1D,KAA+B,IAAI;EAClC,OAAOI,KAAK,CAACO,aAAa,CAAC8S,kBAAkB,EAAEzT,KAAK,CAAC;AACvD,CAAC,CACF;ACrFK,MAAOyU,uBAAwB,SAAQC,yBAAyB;EACpE,IAAc1N,QAAQA,CAAA;IACpB,OAAO,IAAI,CAAC2N,YAAsC;;EAE1CC,WAAWA,CAAA;IACnB,MAAMC,SAAS,GAAI,IAAI,CAACF,YAAoB,CAACE,SAAS;IACtD,IAAI,CAAC,IAAI,CAACF,YAAY,CAACpN,UAAU,IAAIsN,SAAS,EAAE;MAC9C,OACEzU,KAAA,CAAAO,aAAA,CAAC6M,YAAY;QACXC,OAAO,EAAE,IAAI,CAACzG,QAAQ,CAAC8N,yBAAyB;QAChD3G,KAAK,EAAEA,CAAA,KAAM,IAAI,CAACnH,QAAQ,CAAC+N,YAAY,EAAE;QACzC9G,IAAI,EAAE+G,kBAAkB,CAACC,SAAS,CAAC,UAAU;MAAC,EAChC;WAEb;MACL,OAAO,IAAI;;;EAGL3U,aAAaA,CAAA;IACrB,OACEF,KAAA,CAAAO,aAAA,CAAAP,KAAA,CAAAsM,QAAA,QACEtM,KAAA,CAAAO,aAAA,CAAC6M,YAAY;MACXC,OAAO,EAAE,IAAI,CAACzG,QAAQ,CAACkO,uBAAuB;MAC9C/G,KAAK,EAAEA,CAAA,KAAM,IAAI,CAACnH,QAAQ,CAACmO,WAAW,EAAE;MACxCvH,QAAQ,EAAE,IAAI,CAAC5G,QAAQ,CAACoO,UAAU;MAClCvH,QAAQ,EAAE,CAAC,IAAI,CAAC7G,QAAQ,CAACqO,WAAW;MACpCpH,IAAI,EAAE,IAAI,CAACjH,QAAQ,CAACsO,aAAa;MACjCnS,KAAK,EAAE,IAAI,CAAC6D,QAAQ,CAACvD,OAAO;MAC5BH,QAAQ,EAAE,IAAI,CAAC0D,QAAQ,CAAC1D;IAAQ,EAClB,EACf,IAAI,CAACsR,WAAW,EAAE,CAClB;;AAGR;AAED3N,oBAAoB,CAAChF,QAAQ,CAACsT,gBAAgB,CAAC,WAAW,EAAGvV,KAAK,IAAI;EACpE,OAAOI,KAAK,CAACO,aAAa,CAAC8T,uBAAuB,EAAEzU,KAAK,CAAC;AAC5D,CAAC,CAAC;AC1CI,MAAOwV,2BAA4B,SAAQd,yBAAyB;EACxE,IAAce,cAAcA,CAAA;IAC1B,OAAQ,IAAI,CAACzV,KAAK,CAACmJ,OAAO,IAAI,IAAI,CAACnJ,KAAK,CAACgH,QAAQ;;EAEnD,IAAcnD,OAAOA,CAAA;IACnB,OAAO,IAAI,CAAC7D,KAAK,CAAC6D,OAAO;;EAEpBlB,MAAMA,CAAA;IACX,IAAI,CAAC,IAAI,CAAC8S,cAAc,EAAE,OAAO,IAAI;IACrC,MAAMrP,MAAM,GAAG,IAAI,CAACqP,cAAc,CAACA,cAAc;IACjD,IAAI,CAACrP,MAAM,IAAI,CAACA,MAAM,CAACsP,WAAW,EAAE,OAAO,IAAI;IAC/C,OAAOtV,KAAA,CAAAO,aAAA,CAACkR,UAAU;MAChBzL,MAAM,EAAEA,MAAM;MACdwL,IAAI,EAAExL,MAAM,CAACsP,WAAW;MACxB7T,GAAG,EAAEuE,MAAM,CAACvE,GAAG;MACfgC,OAAO,EAAE,IAAI,CAACA;IAAO,EACrB;;AAGL;AAEDoD,oBAAoB,CAAChF,QAAQ,CAACsT,gBAAgB,CAAC,gBAAgB,EAAGvV,KAAK,IAAI;EACzE,OAAOI,KAAK,CAACO,aAAa,CAAC6U,2BAA2B,EAAExV,KAAK,CAAC;AAChE,CAAC,CAAC;AChBI,MAAO2V,8BAA+B,SAAQvV,KAAK,CAAC4M,SAGzD;EACC,IAAI5G,MAAMA,CAAA;IACR,OAAO,IAAI,CAACpG,KAAK,CAACoG,MAAM;;EAEnBW,qBAAqBA,CAACC,QAAkB;IAC7C,OAAOC,oBAAoB,CAAChF,QAAQ,CAACiF,cAAc,CACjD,CAACF,QAAQ,CAACG,kBAAkB,IAAIH,QAAQ,CAACG,kBAAkB,KACvDH,QAAQ,CAACI,WAAW,KACpBJ,QAAQ,CAACK,gBAAgB,EAAE,EAC/B;MACEL,QAAQ,EAAEA,QAAQ;MAClBM,aAAa,EAAEN,QAAQ,CAAC4O,eAAe;MACvC/R,OAAO,EAAE;IACV,EACF;;EAEIiE,qBAAqBA,CAAA;IAC1B,OAAO,IAAI,CAAC1B,MAAM,CAAC0B,qBAAqB;;EAEnCC,qBAAqBA,CAAA;IAC1B,OAAO,IAAI,CAAC3B,MAAM,CAAC2B,qBAAqB;;EAE1CP,WAAWA,CAAC3G,GAAW,EAAEiG,KAAkB,EAAEW,UAAe;IAC1D,OAAO,IAAI;;EAGb9E,MAAMA,CAAA;IACJ,MAAMqE,QAAQ,GAAG,IAAI,CAACZ,MAAM,CAACyP,eAAe,EAAE,CAAC,CAAC,CAAC;IACjD,OACEzV,KAAA,CAAAO,aAAA;MAAKO,KAAK,EAAE,IAAI,CAAClB,KAAK,CAACkB;IAAK,GAC1Bd,KAAA,CAAAO,aAAA,CAACmV,cAAc;MAACjS,OAAO,EAAE,IAAI;MAAEsF,OAAO,EAAEnC;IAAQ,EAAmB,CAC/D;;AAGX;AAEDhF,mBAAmB,CAACC,QAAQ,CAACyB,eAAe,CAC1C,6BAA6B,EAC5B1D,KAA2C,IAAI;EAC9C,OAAOI,KAAK,CAACO,aAAa,CAACgV,8BAA8B,EAAE3V,KAAK,CAAC;AACnE,CAAC,CACF;ACtCK,MAAO+V,yBAA0B,SAAQ9N,mBAG9C;EAGC/H,YAAYF,KAAK;IACf,KAAK,CAACA,KAAK,CAAC;IAgBN,KAAAgW,MAAM,GAAI3M,KAAU,IAAI;MAC9B,IAAI,CAACtJ,KAAK,CAACkW,UAAU,CAAC5M,KAAK,CAACS,WAAW,CAAC;IAC1C,CAAC;IAjBC,IAAI,CAAC3J,OAAO,GAAGC,KAAK,CAACC,SAAS,EAAE;;EAExB6H,WAAWA,CAAClI,KAAU;IAC9B,IAAI,CAACD,KAAK,GAAG,IAAImW,yBAAyB,CACxClW,KAAK,CAAC+I,aAAa,CAAClF,OAAO,EAC3B7D,KAAK,CAACgH,QAAQ,EACdhH,KAAK,CAACU,IAAI,CACX;;EAEO+H,oBAAoBA,CAAA;IAC5B,OAAO,CAAC,UAAU,EAAE,MAAM,CAAC;;EAEnBxI,eAAeA,CAAA;IACvB,OAAO,IAAI,CAACF,KAAK;;EAMnBqB,kBAAkBA,CAACC,SAAc,EAAEC,SAAc;IAC/C,KAAK,CAACF,kBAAkB,CAACC,SAAS,EAAEC,SAAS,CAAC;IAC9C,IAAI,CAACtB,KAAK,CAACU,IAAI,CAACqD,cAAc,CAAC,IAAI,CAAC5D,OAAO,CAACqB,OAAO,CAAC;IACpD,IAAIH,SAAS,CAACX,IAAI,KAAK,IAAI,CAACV,KAAK,CAACU,IAAI,IAAIW,SAAS,CAACX,IAAI,EAAE;MACxDW,SAAS,CAACX,IAAI,CAACqD,cAAc,CAACtB,SAAS,CAAC;;;EAI5Cf,iBAAiBA,CAAA;IACf,KAAK,CAACA,iBAAiB,EAAE;IACzB,IAAI,CAAC1B,KAAK,CAACU,IAAI,CAACqD,cAAc,CAAC,IAAI,CAAC5D,OAAO,CAACqB,OAAO,CAAC;;EAEtDG,oBAAoBA,CAAA;IAClB,KAAK,CAACA,oBAAoB,EAAE;IAC5B,IAAI,CAAC3B,KAAK,CAACU,IAAI,CAACqD,cAAc,CAACtB,SAAS,CAAC;;EAG3CE,MAAMA,CAAA;IACJ,IAAI,CAAC5C,KAAK,CAACW,IAAI,GAAG,IAAI,CAACV,KAAK,CAACU,IAAI;IACjC,MAAMyV,MAAM,GAAG,IAAI,CAACpW,KAAK,CAACmR,QAAQ,GAChCtO,eAAe,CAACxC,KAAA,CAAAO,aAAA;MACdM,IAAI,EAAC,QAAQ;MACbD,SAAS,EAAC,8DAA8D;MAAA,cAC5D,IAAI,CAACjB,KAAK,CAAC0D,OAAO;MAC9BV,OAAO,EAAEA,CAAA,KAAK;QACZ,IAAI,CAAChD,KAAK,CAACqW,GAAG,CAAC,IAAI,CAACrW,KAAK,CAAC;QAC1B,IAAI,CAACA,KAAK,CAACsW,KAAK,GAAG,KAAK;;IACzB,GAEDjW,KAAA,CAAAO,aAAA,CAAC0C,OAAO;MAACG,IAAI,EAAE,MAAM;MAAEF,QAAQ,EAAE,gBAAgB;MAAEH,KAAK,EAAE,IAAI,CAACpD,KAAK,CAAC0D;IAAO,EAAY,CACnF,CAAC,GAERrD,KAAA,CAAAO,aAAA,CAAAP,KAAA,CAAAsM,QAAA,QACG,GAAG,EACH,IAAI,CAAC3M,KAAK,CAACuW,WAAW,GACrBlW,KAAA,CAAAO,aAAA;MACEK,SAAS,EAAC;IAA+D,GAEzEZ,KAAA,CAAAO,aAAA,CAAC0C,OAAO;MAACrC,SAAS,EAAC,oCAAoC;MAACwC,IAAI,EAAE,MAAM;MAAEF,QAAQ,EAAE,iBAAiB;MAAEH,KAAK,EAAE,IAAI,CAACpD,KAAK,CAACwW;IAAW,EAAY,CACvI,GACL,IAAI,EACP,IAAI,CAACxW,KAAK,CAAC+Q,WAAW,GAAGlO,eAAe,CAACxC,KAAA,CAAAO,aAAA;MACxCM,IAAI,EAAC,QAAQ;MACbD,SAAS,EAAC,iEAAiE;MAAA,cAC/D,IAAI,CAACjB,KAAK,CAAC0D,OAAO;MAC9BV,OAAO,EAAEA,CAAA,KAAM,IAAI,CAAChD,KAAK,CAAC+T,MAAM,CAAC,IAAI,CAAC/T,KAAK;IAAC,GAE5CK,KAAA,CAAAO,aAAA,CAAC0C,OAAO;MAACG,IAAI,EAAE,MAAM;MAAEF,QAAQ,EAAE,mBAAmB;MAAEH,KAAK,EAAE,IAAI,CAACpD,KAAK,CAAC0D;IAAO,EAAY,CACtF,CAAC,GAAG,IAAI,CAElB;IAED,MAAM+S,OAAO,GAAG,IAAI,CAACxW,KAAK,CAACmJ,OAAO,CAACtI,GAAG,IAAI,IAAI,CAACd,KAAK,CAACmR,QAAQ,GAAG,MAAM,GAAG,EAAE,CAAC;IAE5E,OACE9Q,KAAA,CAAAO,aAAA;MACEI,GAAG,EAAE,IAAI,CAACZ,OAAO;MACjBa,SAAS,EACP,wBAAwB,IACvB,IAAI,CAACjB,KAAK,CAACmR,QAAQ,GAAG,sBAAsB,GAAG,EAAE,CAAC,IAClD,IAAI,CAACnR,KAAK,CAAC0W,UAAU,GAAG,2BAA2B,GAAG,EAAE,CAAC,IACzD,IAAI,CAAC1W,KAAK,CAAC2W,eAAe,GAAG,wBAAwB,GAAG,EAAE,CAAC,IAC3D,IAAI,CAAC3W,KAAK,CAAC4W,kBAAkB,GAAG,2BAA2B,GAAG,EAAE,CAAC,IACjE,IAAI,CAAC5W,KAAK,CAAC6W,gBAAgB,GAAG,yBAAyB,GAAG,EAAE,CAAC;MAEhE/V,GAAG,EAAE2V,OAAO;MAAA,kCAEV,IAAI,CAACzW,KAAK,CAACuW,WAAW,GAAG,IAAI,CAACvW,KAAK,CAACW,IAAI,CAAC+I,KAAK,GAAGhH,SAAS;MAE5DyK,aAAa,EAAG7D,KAAU,IAAK,IAAI,CAACtJ,KAAK,CAACmN,aAAa,CAAC7D,KAAK;IAAC,GAE9DjJ,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAuB,EAAO,EAE7CZ,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC,yBAAyB;MAACgV,MAAM,EAAE,IAAI,CAACA;IAAM,GAAGG,MAAM,CAAO,EAE5E/V,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAE,sBAAsB;MAAE+B,OAAO,EAAGsG,KAAK,IAAK,IAAI,CAACtJ,KAAK,CAAC6L,MAAM,CAAC,IAAI,CAAC7L,KAAK,EAAEsJ,KAAK,CAACS,WAAW;IAAC,GAAG,IAAI,CAAC9J,KAAK,CAACmJ,OAAO,CAAO,CACpI;;AAGX;AAEDnH,mBAAmB,CAACC,QAAQ,CAACyB,eAAe,CAC1C,gBAAgB,EACf1D,KAAqC,IAAI;EACxC,OAAOI,KAAK,CAACO,aAAa,CAACoV,yBAAyB,EAAE/V,KAAK,CAAC;AAC9D,CAAC,CACF;AClHK,MAAO6W,8BAA+B,SAAQ5O,mBAGnD;EAIC/H,YAAYF,KAA0C;IACpD,KAAK,CAACA,KAAK,CAAC;IAiCd,KAAA8W,kBAAkB,GAAI1L,CAAC,IAAI;MACzBA,CAAC,CAAC7B,cAAc,EAAE;IACpB,CAAC;IAlCC,IAAI,CAACpJ,OAAO,GAAGC,KAAK,CAACC,SAAS,EAAE;;EAExB6H,WAAWA,CAAClI,KAAU;IAC9B,IAAI,CAACD,KAAK,GAAG,IAAIgX,8BAA8B,CAC7C/W,KAAK,CAAC+I,aAAa,CAAClF,OAAO,EAC3B7D,KAAK,CAACgH,QAAQ,EACdhH,KAAK,CAACU,IAAI,EACV,IAAI,EACJ,IAAI,CACL;;EAEO+H,oBAAoBA,CAAA;IAC5B,OAAO,CAAC,UAAU,EAAE,MAAM,CAAC;;EAEnBxI,eAAeA,CAAA;IACvB,OAAO,IAAI,CAACF,KAAK;;EAGnB,IAAciH,QAAQA,CAAA;IACpB,OAAO,IAAI,CAAChH,KAAK,CAACgH,QAAoC;;EAGxDtF,iBAAiBA,CAAA;IACf,KAAK,CAACA,iBAAiB,EAAE;IACzB,IAAI,CAAC3B,KAAK,CAACiX,SAAS,GAAG,IAAI,CAAC7W,OAAO,CAACqB,OAAO;;EAG7CJ,kBAAkBA,CAACC,SAAS,EAAEC,SAAS;IACrC,KAAK,CAACF,kBAAkB,CAACC,SAAS,EAAEC,SAAS,CAAC;IAC9C,IAAI,CAACvB,KAAK,CAACiX,SAAS,GAAG,IAAI,CAAC7W,OAAO,CAACqB,OAAO;;EAM7C8S,sBAAsBA,CAAA;IACpB,OAAOlU,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAA+B,GAACZ,KAAA,CAAAO,aAAA,CAACsP,yBAAyB,OAA6B,CAAM;;EAGrHgH,qBAAqBA,CAAA;IACnB,MAAMC,SAAS,GAAGtU,eAAe,CAC/BxC,KAAA,CAAAO,aAAA;MAAMK,SAAS,EAAE,IAAI,CAACjB,KAAK,CAACoX,YAAY;MACtCpU,OAAO,EAAEA,CAAA,KAAM,IAAI,CAAChD,KAAK,CAACqX,aAAa,CAAC,IAAI,CAACrX,KAAK;IAAC,GAClD,IAAI,CAACA,KAAK,CAACsX,sBAAsB,GAChCjX,KAAA,CAAAO,aAAA,CAAC0C,OAAO;MAACG,IAAI,EAAE,MAAM;MAAEF,QAAQ,EAAE,aAAa;MAC5CH,KAAK,EAAE,IAAI,CAACpD,KAAK,CAACuX;IAAY,EAAY,GAC5ClX,KAAA,CAAAO,aAAA,eAAO,IAAI,CAACZ,KAAK,CAACwX,eAAe,CAAQ,CAEtC,CAAC;IACV,MAAMC,WAAW,GAAG,IAAI,CAACzX,KAAK,CAACuS,eAAe,GAAGlS,KAAA,CAAAO,aAAA;MAAMK,SAAS,EAAC;IAAmC,GAAE,IAAI,CAACjB,KAAK,CAAC8M,eAAe,CAAQ,GAAG,IAAI;IAC/I,OAAOzM,KAAA,CAAAO,aAAA,CAAAP,KAAA,CAAAsM,QAAA,QACJ8K,WAAW,EACXN,SAAS,CACT;;EAGLvU,MAAMA,CAAA;IACJ,IAAI,CAAC5C,KAAK,CAACW,IAAI,GAAG,IAAI,CAACV,KAAK,CAACU,IAAI;IACjC,MAAM2V,KAAK,GAAG,CAAC,IAAI,CAACrW,KAAK,CAACgH,QAAQ,CAACyQ,YAAY,CAAC,IAAI,CAACzX,KAAK,CAACU,IAAI,CAAC;IAChE,IAAI,CAACX,KAAK,CAACsW,KAAK,GAAGA,KAAK;IACxB,MAAMqB,UAAU,GAAG,CAAC,IAAI,CAAC3X,KAAK,CAAC4X,kBAAkB,EAAE,GAAG;MAAE9H,KAAK,EAAE,IAAI,CAAC7I,QAAQ,CAAC4Q,kBAAkB;MAAE9H,MAAM,EAAE,IAAI,CAAC9I,QAAQ,CAAC6Q;IAAmB,CAAE,GAAG,IAAI;IAEnJ,IAAI9M,OAAO,GAAG,IAAI;IAClB,IAAIsL,KAAK,IAAI,IAAI,CAACtW,KAAK,CAACsQ,WAAW,EAAE;MACnCtF,OAAO,GAAI3K,KAAA,CAAAO,aAAA,CAAAP,KAAA,CAAAsM,QAAA,QACTtM,KAAA,CAAAO,aAAA;QAAKK,SAAS,EAAC;MAA4B,GACzCZ,KAAA,CAAAO,aAAA;QAAKK,SAAS,EAAC;MAAmD,GAChEZ,KAAA,CAAAO,aAAA;QAAOK,SAAS,EAAC;MAAuB,GACtCZ,KAAA,CAAAO,aAAA;QAAKO,KAAK,EAAEwW,UAAU;QAAE1W,SAAS,EAAC;MAAuB,GACtD,IAAI,CAACjB,KAAK,CAACsQ,WAAW,GAAG,IAAI,CAACiE,sBAAsB,EAAE,GAAG,IAAI,CAC1D,CACA,CACJ,CACF,EAEL,IAAI,CAACvU,KAAK,CAACmR,QAAQ,IAAI,CAAC,IAAI,CAACnR,KAAK,CAACsQ,WAAW,GAC7CjQ,KAAA,CAAAO,aAAA;QAAKK,SAAS,EAAC;MAA+B,GAC3C,IAAI,CAACiW,qBAAqB,EAAE,IAE7B,IAAI,CACN;WACC;MACLlM,OAAO,GACL3K,KAAA,CAAAO,aAAA,CAAAP,KAAA,CAAAsM,QAAA,QACEtM,KAAA,CAAAO,aAAA;QAAKK,SAAS,EAAE;MAA4B,GACzC,IAAI,CAAChB,KAAK,CAACmJ,OAAO,CACf,EAGJ,IAAI,CAACpJ,KAAK,CAACuW,WAAW,IAAI,IAAI,CAACvW,KAAK,CAAC+X,iBAAiB,GACpD1X,KAAA,CAAAO,aAAA;QAAMK,SAAS,EAAC,uEAAuE;QACrFkM,aAAa,EAAG7D,KAAU,IAAK,IAAI,CAACtJ,KAAK,CAACmN,aAAa,CAAC7D,KAAK;MAAC,GAE9DjJ,KAAA,CAAAO,aAAA,CAAC0C,OAAO;QAACG,IAAI,EAAE,MAAM;QAAEF,QAAQ,EAAE;MAAiB,EAAY,IAE9D,IAAI,EAIR,IAAI,CAACvD,KAAK,CAAC+X,iBAAiB,GAC1B1X,KAAA,CAAAO,aAAA;QAAKK,SAAS,EAAC;MAAqD,GACjE,IAAI,CAACjB,KAAK,CAAC+Q,WAAW,IAAI,CAAC,IAAI,CAAC/Q,KAAK,CAACsQ,WAAW,GAAGzN,eAAe,CAACxC,KAAA,CAAAO,aAAA;QACnEK,SAAS,EAAC,oBAAoB;QAC9B+B,OAAO,EAAEA,CAAA,KAAM,IAAI,CAAChD,KAAK,CAACqQ,UAAU,CAAC,IAAI,CAACrQ,KAAK;MAAC,GAEhDK,KAAA,CAAAO,aAAA,CAAC0C,OAAO;QAACpC,IAAI,EAAC,QAAQ;QAACuC,IAAI,EAAE,MAAM;QAAEF,QAAQ,EAAE,iBAAiB;QAAEH,KAAK,EAAE,IAAI,CAACpD,KAAK,CAACgY;MAAe,EAAY,CAC1G,CAAC,GAAG,IAAI,EACd,IAAI,CAAChY,KAAK,CAAC+Q,WAAW,IAAI,CAAC,IAAI,CAAC/Q,KAAK,CAACsQ,WAAW,GAAGzN,eAAe,CAACxC,KAAA,CAAAO,aAAA;QACnEK,SAAS,EAAC,+CAA+C;QACzD+B,OAAO,EAAEA,CAAA,KAAM,IAAI,CAAChD,KAAK,CAAC+T,MAAM,CAAC,IAAI,CAAC/T,KAAK;MAAC,GAE5CK,KAAA,CAAAO,aAAA,CAAC0C,OAAO;QAACpC,IAAI,EAAC,QAAQ;QAACuC,IAAI,EAAE,MAAM;QAAEF,QAAQ,EAAE,aAAa;QAAEH,KAAK,EAAE,IAAI,CAACpD,KAAK,CAACiY;MAAe,EAAY,CACtG,CAAC,GAAG,IAAI,IAEf,IAAI,CAGb;;IAGH,OACE5X,KAAA,CAAAO,aAAA;MACEI,GAAG,EAAE,IAAI,CAACZ,OAAO;MACjBa,SAAS,EAAE,IAAI,CAACjB,KAAK,CAAC+C,UAAU,EAAE;MAClCjC,GAAG,EAAE,IAAI,CAACb,KAAK,CAACmJ,OAAO,CAACtI,GAAG;MAAA,kCAEzB,IAAI,CAACd,KAAK,CAACuW,WAAW,GAAG,IAAI,CAACvW,KAAK,CAACW,IAAI,CAAC+I,KAAK,GAAGhH,SAAS;MAE5DyK,aAAa,EAAG7D,KAAU,IAAK,IAAI,CAACtJ,KAAK,CAACmN,aAAa,CAAC7D,KAAK,CAAC;MAC9D4O,WAAW,EAAE,IAAI,CAACnB,kBAAkB;MACpCoB,MAAM,EAAE,IAAI,CAACnY,KAAK,CAACmY,MAAM;MACzBC,WAAW,EAAE,IAAI,CAACpY,KAAK,CAACoY,WAAW;MACnCC,UAAU,EAAE,IAAI,CAACrY,KAAK,CAACqY,UAAU;MACjCC,WAAW,EAAE,IAAI,CAACtY,KAAK,CAACsY;IAAW,GAEnCjY,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAE,qCAAqC;MAAEE,KAAK,EAAEwW;IAAU,EAAQ,EAChFtX,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAE;IAAuC,GACrDZ,KAAA,CAAAO,aAAA;MACE4O,IAAI,EAAC,MAAM;MAAA,eACC,MAAM;MAClBC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE,IAAI,CAAC1P,KAAK,CAAC2P,aAAa;MAChC1O,SAAS,EAAC,uBAAuB;MACjCE,KAAK,EAAE;QACLyO,QAAQ,EAAE,UAAU;QACpBC,OAAO,EAAE,CAAC;QACVC,KAAK,EAAE,KAAK;QACZC,MAAM,EAAE,KAAK;QACbC,QAAQ,EAAE;;IACX,EACD,EACDhF,OAAO,CACJ,CACF;;AAGX;AAED/I,mBAAmB,CAACC,QAAQ,CAACyB,eAAe,CAC1C,sBAAsB,EACrB1D,KAA0C,IAAI;EAC7C,OAAOI,KAAK,CAACO,aAAa,CAACkW,8BAA8B,EAAE7W,KAAK,CAAC;AACnE,CAAC,CACF;AC3KK,MAAOsY,0BAA2B,SAAQrQ,mBAG/C;EAEWC,WAAWA,CAAClI,KAAU;;IAC9B,MAAMqG,IAAI,GAAGrG,KAAK,CAAC+I,aAAa;IAChC,IAAIwP,cAAc,GAAG,KAAK;IAC1B,IAAI,CAAC,CAAC,IAAI,CAACxY,KAAK,EAAE;MAChBwY,cAAc,GAAG,IAAI,CAACxY,KAAK,CAACqV,UAAU;;IAExC,IAAI,CAACrV,KAAK,GAAG,IAAIyY,0BAA0B,CACzCnS,IAAI,CAACxC,OAAO,EACZwC,IAAI,CAAC8C,OAAO,EACZ9C,IAAI,CAACW,QAAQ,EACbX,IAAI,CAAC2C,GAAG,EACR3C,IAAI,CAACoS,MAAM,KAAI,CAAAC,EAAA,GAAArS,IAAI,CAAC8C,OAAO,CAACwP,IAAI,cAAAD,EAAA,uBAAAA,EAAA,CAAED,MAAM,EACzC;IACD,IAAI,CAAC1Y,KAAK,CAACqV,UAAU,GAAGmD,cAAc;;EAE9B9P,oBAAoBA,CAAA;IAC5B,OAAO,CAAC,eAAe,CAAC;;EAE1BrH,kBAAkBA,CAACC,SAAc,EAAEC,SAAc;;IAC/C,KAAK,CAACF,kBAAkB,CAACC,SAAS,EAAEC,SAAS,CAAC;IAC9C,MAAM+E,IAAI,GAAG,IAAI,CAACrG,KAAK,CAAC+I,aAAa;IACrC,IAAI,CAAChJ,KAAK,CAAC6Y,YAAY,GAAGvS,IAAI,CAAC8C,OAAO;IACtC,IAAI,CAACpJ,KAAK,CAACiJ,GAAG,GAAG3C,IAAI,CAAC2C,GAAG;IACzB,IAAI,CAACjJ,KAAK,CAAC0Y,MAAM,GAAGpS,IAAI,CAACoS,MAAM,KAAI,CAAAI,EAAA,IAAAH,EAAA,GAAArS,IAAI,CAAC8C,OAAO,cAAAuP,EAAA,uBAAAA,EAAA,CAAEC,IAAI,cAAAE,EAAA,uBAAAA,EAAA,CAAEJ,MAAM;IAC7D,IAAI,CAAC1Y,KAAK,CAACiH,QAAQ,GAAGX,IAAI,CAACW,QAAQ;;EAE3B/G,eAAeA,CAAA;IACvB,OAAO,IAAI,CAACF,KAAK;;EAGnB4C,MAAMA,CAAA;IACJ,IAAImW,QAAQ,GAAG,IAAI;IACnB,IAAI,CAAC,CAAC,IAAI,CAAC/Y,KAAK,CAACgZ,mBAAmB,EAAE;MACpCD,QAAQ,GAAG1Y,KAAA,CAAAO,aAAA;QAAKK,SAAS,EAAC;MAAoC,GAC3D4B,eAAe,CAACxC,KAAA,CAAAO,aAAA;QAAMK,SAAS,EAAC,8DAA8D;QAAC+B,OAAO,EAAGsG,KAAU,IAAK,IAAI,CAACtJ,KAAK,CAACiZ,YAAY,CAAC,IAAI,CAACjZ,KAAK,EAAEsJ,KAAK;MAAC,GACjKjJ,KAAA,CAAAO,aAAA,CAAC0C,OAAO;QAACG,IAAI,EAAE,MAAM;QAAEF,QAAQ,EAAE;MAAW,EAAY,CACnD,CAAC,CACJ;;IAGR,OACElD,KAAA,CAAAO,aAAA;MACEK,SAAS,EAAE,iBAAiB;MAC5BwO,QAAQ,EAAE,EAAE;MACZ3O,GAAG,EAAE,IAAI,CAACb,KAAK,CAACmJ,OAAO,CAACtI,GAAG;MAC3BkC,OAAO,EAAGqI,CAAM,IAAK,CAAC,IAAI,CAACpL,KAAK,CAACgH,QAAQ,IAAI,IAAI,CAACjH,KAAK,CAACkZ,aAAa,CAAC,IAAI,CAAClZ,KAAK,EAAEqL,CAAC,CAAC;MACpF8N,UAAU,EAAE9N,CAAC,IAAI,IAAI,CAACrL,KAAK,CAACwL,KAAK,CAACH,CAAC,CAACtB,WAAW,EAAEsB,CAAC,CAAC1B,aAAa,CAAC;MACjE8B,WAAW,EAAEJ,CAAC,IAAI,IAAI,CAACrL,KAAK,CAACwL,KAAK,CAACH,CAAC,CAACtB,WAAW,EAAEsB,CAAC,CAAC1B,aAAa;IAAC,GAElEtJ,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAE,2BAA2B,IAAI,IAAI,CAACjB,KAAK,CAACqV,UAAU,GAAG,cAAc,GAAG,EAAE;IAAC,EAAQ,EAElG,IAAI,CAACpV,KAAK,CAACmJ,OAAO,EAElB2P,QAAQ,CACL;;AAGX;AAED9W,mBAAmB,CAACC,QAAQ,CAACyB,eAAe,CAC1C,iBAAiB,EAChB1D,KAAsC,IAAI;EACzC,OAAOI,KAAK,CAACO,aAAa,CAAC2X,0BAA0B,EAAEtY,KAAK,CAAC;AAC/D,CAAC,CACF;AC5EK,MAAOmZ,aAAc,SAAQlR,mBAGlC;EAEWC,WAAWA,CAAClI,KAAU;IAC9B,IAAI,IAAI,CAACA,KAAK,CAACoG,MAAM,EAAE;MACrB,IAAI,CAACrG,KAAK,GAAG,IAAIqZ,kBAAkB,CAACpZ,KAAK,CAACoG,MAAM,CAAC;;;EAG3CqC,oBAAoBA,CAAA;IAC5B,OAAO,CAAC,QAAQ,CAAC;;EAETxI,eAAeA,CAAA;IACvB,OAAO,IAAI,CAACF,KAAK;;EAEnB4C,MAAMA,CAAA;IACJ,IAAI,CAAC,IAAI,CAAC5C,KAAK,EAAE;MACf,OAAO,IAAI;;IAEb,OACEK,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAkB,GAC/BZ,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAA2B,GACxCZ,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAA0B,GACvCZ,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAA+B,GAAE,IAAI,CAACjB,KAAK,CAACsZ,iBAAiB,CAAO,EACnFjZ,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAgC,GAC7CZ,KAAA,CAAAO,aAAA,CAAC6M,YAAY;MACXW,KAAK,EAAEA,CAAA,KAAM,IAAI,CAACpO,KAAK,CAACuZ,gBAAgB,EAAE;MAC1CrL,IAAI,EAAE,IAAI,CAAClO,KAAK,CAACwZ,sBAAsB;MACvC3L,QAAQ,EAAE,IAAI,CAAC7N,KAAK,CAACyZ,eAAe;MACpC3L,QAAQ,EAAE;IAAK,EACD,EAChBzN,KAAA,CAAAO,aAAA,CAAC6M,YAAY;MACXW,KAAK,EAAEA,CAAA,KAAM,IAAI,CAACpO,KAAK,CAAC0Z,eAAe,EAAE;MACzCxL,IAAI,EAAE,IAAI,CAAClO,KAAK,CAAC2Z,qBAAqB;MACtC9L,QAAQ,EAAE,IAAI,CAAC7N,KAAK,CAAC4Z,cAAc;MACnC9L,QAAQ,EAAE;IAAK,EACD,CACZ,CACF,EACL,IAAI,CAAC+L,kBAAkB,EAAE,EACzB,IAAI,CAACC,mBAAmB,EAAE,CACvB,CACF;;EAGVD,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAAC7Z,KAAK,CAAC+Z,cAAc,KAAK,MAAM,EAAE;MACxC,OAAO,IAAI;;IAEb,OACE1Z,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAA2C,GACxDZ,KAAA,CAAAO,aAAA,cAAM,IAAI,CAACZ,KAAK,CAACga,UAAU,CAAO,CAC9B;;EAGVF,mBAAmBA,CAAA;IACjB,IAAI,IAAI,CAAC9Z,KAAK,CAAC+Z,cAAc,KAAK,OAAO,EAAE;MACzC,OAAO,IAAI;;IAEb,OACE1Z,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAA4C,GACzDZ,KAAA,CAAAO,aAAA,gBACEP,KAAA,CAAAO,aAAA,gBACEP,KAAA,CAAAO,aAAA;MAAIK,SAAS,EAAC;IAA4B,GACxCZ,KAAA,CAAAO,aAAA;MAAIE,GAAG,EAAE,CAAC;MAAEG,SAAS,EAAC;IAAuB,GAC1C,IAAI,CAACjB,KAAK,CAACia,YAAY,CACrB,EACL5Z,KAAA,CAAAO,aAAA;MAAIE,GAAG,EAAE,CAAC;MAAEG,SAAS,EAAC;IAAuB,GAC1C,IAAI,CAACjB,KAAK,CAACka,mBAAmB,CAC5B,CACF,CACC,EACR7Z,KAAA,CAAAO,aAAA,gBAAQwY,aAAa,CAACe,UAAU,CAAC,IAAI,CAACna,KAAK,CAACoa,UAAU,CAAC,CAAS,CAC1D,CACJ;;EAGV,OAAOD,UAAUA,CAAC7T,IAAgB;IAChC,MAAM+T,IAAI,GAAG,EAAE;IACf,KAAK,IAAIzU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGU,IAAI,CAACR,MAAM,EAAEF,CAAC,EAAE,EAAE;MACpCyU,IAAI,CAACC,IAAI,CAACja,KAAA,CAAAO,aAAA,CAAC2Z,kBAAkB;QAACzZ,GAAG,EAAE8E,CAAC,GAAG,CAAC;QAAEqD,GAAG,EAAE3C,IAAI,CAACV,CAAC;MAAC,EAAI,CAAC;;IAE7D,OAAOyU,IAAI;;AAEd;AAEK,MAAOE,kBAAmB,SAAQrS,mBAA6B;EACnE,IAAYe,GAAGA,CAAA;IACb,OAAO,IAAI,CAAChJ,KAAK,CAACgJ,GAAG;;EAGb/I,eAAeA,CAAA;IACvB,OAAO,IAAI,CAAC+I,GAAG;;EAGjBrG,MAAMA,CAAA;IACJ,OACEvC,KAAA,CAAAO,aAAA,CAAAP,KAAA,CAAAsM,QAAA,QACG9J,eAAe,CAACxC,KAAA,CAAAO,aAAA;MAAIoC,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACiG,GAAG,CAACuR,MAAM;IAAE,GACnDna,KAAA,CAAAO,aAAA;MAAIE,GAAG,EAAE,CAAC;MACRK,KAAK,EAAE;QAAEsZ,WAAW,EAAE,IAAI,CAACxR,GAAG,CAACyR;MAAU,CAAE;MAC3CzZ,SAAS,EAAC;IAAuB,GAEhC,IAAI,CAACgI,GAAG,CAAC0R,MAAM,GACdta,KAAA,CAAAO,aAAA;MACEO,KAAK,EAAE;QAAEyZ,IAAI,EAAE,IAAI,CAAC3R,GAAG,CAAC4R;MAAY,CAAE;MACtC5Z,SAAS,EAAE,2BAA2B,IAAI,IAAI,CAACgI,GAAG,CAAC6R,SAAS,GAAG,EAAE,GAAG,oCAAoC;IAAC,GACzGza,KAAA,CAAAO,aAAA,CAAC0C,OAAO;MACNC,QAAQ,EAAE,mBAAmB;MAC7BE,IAAI,EAAE;IAAE,EACC,CACN,GACL,IAAI,EAEP,IAAI,CAACwF,GAAG,CAAChC,QAAQ,GAAG5G,KAAA,CAAAO,aAAA,CAACgH,qBAAqB;MAACC,MAAM,EAAE,IAAI,CAACoB,GAAG,CAAChC,QAAQ,CAAC8T;IAAQ,EAAI,GAAG1a,KAAA,CAAAO,aAAA,eAAO,IAAI,CAACqI,GAAG,CAAC7F,KAAK,CAAQ,CAC/G,EACL/C,KAAA,CAAAO,aAAA;MAAIE,GAAG,EAAE,CAAC;MAAEG,SAAS,EAAE,IAAI,CAACgI,GAAG,CAAC0R,MAAM,GAAG,8BAA8B,GAAG;IAAuB,GAC9F,IAAI,CAAC1R,GAAG,CAACiM,SAAS,CAAC,IAAI,CAACjM,GAAG,CAAC+R,YAAY,CAAC,CACvC,CACF,CAAC,EACL,IAAI,CAAC/R,GAAG,CAAC0R,MAAM,IAAI,CAAC,IAAI,CAAC1R,GAAG,CAAC6R,SAAS,GAAG1B,aAAa,CAACe,UAAU,CAAC,IAAI,CAAClR,GAAG,CAAC3C,IAAI,CAAC,GAAG,IAAI,CACvF;;AAGR;AClGK,MAAO2U,wBAAyB,SAAQ/S,mBAG7C;EAGC/H,YAAYF,KAAK;IACf,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACG,OAAO,GAAGC,KAAK,CAACC,SAAS,EAAE;;EAExB6H,WAAWA,CAAClI,KAAU;IAC9B,IAAI,CAACD,KAAK,GAAG,IAAIkb,oBAAoB,CAACjb,KAAK,CAACU,IAAI,EAAEV,KAAK,CAAC6D,OAAO,EAAE7D,KAAK,CAACkb,WAAW,CAAC;;EAE3EzS,oBAAoBA,CAAA;IAC5B,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC;;EAE5B,IAAW/H,IAAIA,CAAA;IACb,OAAO,IAAI,CAACV,KAAK,CAACU,IAAI;;EAExB,IAAWmD,OAAOA,CAAA;IAChB,OAAO,IAAI,CAAC7D,KAAK,CAAC6D,OAAO;;EAE3B,IAAWsX,SAASA,CAAA;IAClB,OAAO,IAAI,CAACnb,KAAK,CAACmb,SAAS;;EAGnBlb,eAAeA,CAAA;IACvB,OAAQ,IAAI,CAACS,IAAY;;EAG3BiC,MAAMA,CAAA;IACJ,MAAMjC,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,MAAMqO,aAAa,GAAG/M,mBAAmB,CAACC,QAAQ,CAACtB,aAAa,CAC9D,IAAI,CAACZ,KAAK,CAACgP,aAAa,EACxB;MACErO,IAAI,EAAEA,IAAI;MACVmD,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBqX,WAAW,EAAE,IAAI,CAACrX,OAAO,CAACuX,OAAO;MACjCrb,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBob,SAAS,EAAE,IAAI,CAACA;IACjB,EACF;IACD,OACE/a,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAEN,IAAI,CAACmB,GAAG;MAAEhB,GAAG,EAAEH,IAAI,CAACwB,EAAE;MAAEnB,GAAG,EAAE,IAAI,CAACZ;IAAO,GACrDO,IAAI,CAAC2a,aAAa,IAAI,CAAC,IAAI,CAACxX,OAAO,CAACuX,OAAO,CAACE,kBAAkB,GAC9Dlb,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAiC,EAAO,GACrD,IAAI,EACRZ,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC,8CAA8C;MAC3DkM,aAAa,EAAG7D,KAAU,IAAI;QAC5BA,KAAK,CAACkS,OAAO,EAAE;QACf,IAAI,CAACxb,KAAK,CAACmN,aAAa,CAAC7D,KAAK,CAAC;MACjC;IAAC,GAEA0F,aAAa,CACV,CACF;;EAGVpN,oBAAoBA,CAAA;IAClB,KAAK,CAACA,oBAAoB,EAAE;IAC5B,IAAI,CAACjB,IAAI,CAACyB,kBAAkB,GAAGM,SAAS;;EAE1Cf,iBAAiBA,CAAA;IACf,KAAK,CAACA,iBAAiB,EAAE;IACzB,IAAI,CAAChB,IAAI,CAACyB,kBAAkB,GAAG,CAACC,IAAI,EAAEC,QAAQ,KAAI;MAChDC,cAAc,CAAC,MAAK;QAClB,IAAKC,QAAgB,CAAC,WAAW,CAAC,EAAE;UACjCA,QAAgB,CAAC,WAAW,CAAC,CAAC,MAAK;YAClC,IAAI,CAAC7B,IAAI,CAAC0B,IAAI,GAAGA,IAAI;UACvB,CAAC,CAAC;eACG;UACL,IAAI,CAAC1B,IAAI,CAAC0B,IAAI,GAAGA,IAAI;;QAEvBE,cAAc,CAAC,MAAK;UAClBD,QAAQ,CAACD,IAAI,EAAE,IAAI,CAACjC,OAAO,CAACqB,OAAO,CAAC;QACtC,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IACD,IAAI,CAACd,IAAI,CAAC8B,WAAW,EAAE;;AAE1B;AAEK,MAAOgZ,wBAAyB,SAAQvT,mBAG7C;EACC/H,YAAYF,KAAK;IACf,KAAK,CAACA,KAAK,CAAC;;EAEJyI,oBAAoBA,CAAA;IAC5B,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC;;EAE5B,IAAW/H,IAAIA,CAAA;IACb,OAAO,IAAI,CAACV,KAAK,CAACU,IAAI;;EAExB,IAAWmD,OAAOA,CAAA;IAChB,OAAO,IAAI,CAAC7D,KAAK,CAAC6D,OAAO;;EAE3B,IAAW9D,KAAKA,CAAA;IACd,OAAO,IAAI,CAACC,KAAK,CAACD,KAAK;;EAEfE,eAAeA,CAAA;IACvB,OAAO,IAAI,CAACF,KAAK;;EAEnB4C,MAAMA,CAAA;IACJ,MAAM8Y,MAAM,GAAI,IAAI,CAACzb,KAAK,CAACmb,SAAS,GAClC/a,KAAA,CAAAO,aAAA;MAAMK,SAAS,EAAC,0BAA0B;MACxC+B,OAAO,EAAGsG,KAAU,IAAI;QACtBA,KAAK,CAACkS,OAAO,EAAE;QACf,IAAI,CAACxb,KAAK,CAACoO,KAAK,CAAC9E,KAAK,CAAC;;IACxB,GACDjJ,KAAA,CAAAO,aAAA,CAAC0C,OAAO;MAACG,IAAI,EAAE,MAAM;MAAEF,QAAQ,EAAE,IAAI,CAAC5C,IAAI,CAAC4C,QAAQ;MAAEtC,SAAS,EAAC,wBAAwB;MAACmC,KAAK,EAAE,IAAI,CAACzC,IAAI,CAAC+C;IAAO,EAAY,EAC5HrD,KAAA,CAAAO,aAAA,eAAO,IAAI,CAACD,IAAI,CAACyC,KAAK,CAAQ,IAGhC,IACD;IACD,MAAMzC,IAAI,GAAGkC,eAAe,CAC1BxC,KAAA,CAAAO,aAAA;MACEK,SAAS,EAAE,IAAI,CAACN,IAAI,CAACgb,WAAW;MAChClM,QAAQ,EAAE,CAAC;MACXvO,IAAI,EAAC,QAAQ;MAAA,cACD,IAAI,CAACP,IAAI,CAAC+C,OAAO;MAC7BV,OAAO,EAAGsG,KAAU,IAAI;QACtBA,KAAK,CAACkS,OAAO,EAAE;QACf,IAAI,CAACxb,KAAK,CAACoO,KAAK,CAAC9E,KAAK,CAAC;;IACxB,GAEDjJ,KAAA,CAAAO,aAAA;MAAMK,SAAS,EAAC;IAA6B,GAC1C,CAAC,CAAC,IAAI,CAACN,IAAI,CAAC4C,QAAQ,GAAGlD,KAAA,CAAAO,aAAA,CAAC0C,OAAO;MAACG,IAAI,EAAE,MAAM;MAAEF,QAAQ,EAAE,IAAI,CAAC5C,IAAI,CAAC4C,QAAQ;MAAEtC,SAAS,EAAC;IAAwB,EAAW,GAAG,IAAI,CAC5H,EACL,IAAI,CAAChB,KAAK,CAACmb,SAAS,GACpB,OAEA/a,KAAA,CAAAO,aAAA;MAAMK,SAAS,EAAC;IAAyB,GAAE,IAAI,CAACN,IAAI,CAACyC,KAAK,CAAQ,CAEhE,CAAC;IACT,OACE/C,KAAA,CAAAO,aAAA,CAAAP,KAAA,CAAAsM,QAAA,QACGhM,IAAI,EACJ+a,MAAM,CACN;;AAGR;AAEDzZ,mBAAmB,CAACC,QAAQ,CAACyB,eAAe,CAAC,kBAAkB,EAAG1D,KAAK,IAAI;EACzE,OAAOW,aAAa,CAAC6a,wBAAwB,EAAExb,KAAK,CAAC;AACvD,CAAC,CAAC;ACnLI,MAAO2b,6BAA8B,SAAQ1T,mBAAwD;EACzG/H,YAAYF,KAAK;IACf,KAAK,CAACA,KAAK,CAAC;;EAEJyI,oBAAoBA,CAAA;IAC5B,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC;;EAE5B,IAAW/H,IAAIA,CAAA;IACb,OAAO,IAAI,CAACV,KAAK,CAACU,IAAI;;EAExB,IAAWX,KAAKA,CAAA;IACd,OAAO,IAAI,CAACC,KAAK,CAACD,KAAK;;EAEzB,IAAW8D,OAAOA,CAAA;IAChB,OAAO,IAAI,CAAC7D,KAAK,CAAC6D,OAAO;;EAE3B,IAAWsX,SAASA,CAAA;IAClB,OAAO,IAAI,CAACnb,KAAK,CAACmb,SAAS;;EAE7B,IAAWD,WAAWA,CAAA;IACpB,OAAO,IAAI,CAAClb,KAAK,CAACkb,WAAW;;EAGrBjb,eAAeA,CAAA;IACvB,OAAO,IAAI,CAACS,IAAI;;EAElBiC,MAAMA,CAAA;IACJ,OAAOvC,KAAA,CAAAO,aAAA,CAAAP,KAAA,CAAAsM,QAAA,QACLtM,KAAA,CAAAO,aAAA,CAAC6a,wBAAwB;MAAC9a,IAAI,EAAE,IAAI,CAACA,IAAI;MAAEmD,OAAO,EAAE,IAAI,CAACA,OAAO;MAAE9D,KAAK,EAAE,IAAI,CAACA,KAAK;MAAEmb,WAAW,EAAE,IAAI,CAACA,WAAW;MAAEC,SAAS,EAAE,IAAI,CAACA;IAAS,EAA+B,EAC5K/a,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC,kCAAkC;MAC/CwK,WAAW,EAAGnC,KAAU,IAAK,IAAI,CAACtJ,KAAK,CAACyL,WAAW,CAAC,IAAI,CAAC9K,IAAI,EAAE2I,KAAK,CAAC;MACrEiC,YAAY,EAAGjC,KAAU,IAAK,IAAI,CAACtJ,KAAK,CAACuL,YAAY,CAAC,IAAI,CAAC5K,IAAI,EAAE2I,KAAK;IAAC,GAEvEjJ,KAAA,CAAAO,aAAA,CAAC0C,OAAO;MAACG,IAAI,EAAE,MAAM;MAAEF,QAAQ,EAAE,IAAI,CAAC5C,IAAI,CAACkb;IAAkB,EAAa,EAC1Exb,KAAA,CAAAO,aAAA,CAACqS,KAAK;MAACjT,KAAK,EAAE,IAAI,CAACW,IAAI,CAACuS;IAAU,EAAI,CAClC,CACL;;AAEN;AAEDjR,mBAAmB,CAACC,QAAQ,CAACyB,eAAe,CAAC,wBAAwB,EAAG1D,KAAK,IAAI;EAC/E,OAAOI,KAAK,CAACO,aAAa,CAACgb,6BAA6B,EAAE3b,KAAK,CAAC;AAClE,CAAC,CAAC;ACrCI,MAAO6b,4BAA6B,SAAQ/b,iBAA0D;EAE1G,IAAWgc,QAAQA,CAAA;IACjB,OAAO,IAAI,CAAC9b,KAAK,CAAC8b,QAAQ;;EAE5B,IAAWV,OAAOA,CAAA;IAChB,OAAO,IAAI,CAACpb,KAAK,CAACob,OAAO;;EAE3B,IAAWW,KAAKA,CAAA;IACd,OAAO,uBAAuB,IAC3B,IAAI,CAACD,QAAQ,CAACjB,SAAS,GAAG,mCAAmC,GAAG,EAAE,CAAC,IACnE,IAAI,CAACiB,QAAQ,CAACE,KAAK,GAAG,+BAA+B,GAAG,EAAE,CAAC;;EAGtD/b,eAAeA,CAAA;IACvB,OAAQ,IAAI,CAAC6b,QAAgB;;EAG/BnZ,MAAMA,CAAA;IACJ,MAAMsZ,MAAM,GAAG,IAAI,CAACC,oBAAoB,EAAE;IAC1C,MAAM3b,KAAK,GAAG,IAAI,CAAC4b,qBAAqB,EAAE;IAC1C,OACE/b,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAE,IAAI,CAAC+a,KAAK;MAAElb,GAAG,EAAE,IAAI,CAACib,QAAQ,CAAC7Q;IAAI,GACjD7K,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAsC,GAClDib,MAAM,CACH,EACL1b,KAAK,CACF;;EAIV2b,oBAAoBA,CAAA;IAClB,IAAIlb,SAAS,GAAG,8BAA8B;IAC9C,IAAI,IAAI,CAACoa,OAAO,CAACgB,qBAAqB,EAAE;MACtCpb,SAAS,IAAI,0CAA0C;;IAEzD,OAAO4B,eAAe,CACpBxC,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAEA,SAAS;MAAE+B,OAAO,EAAEqI,CAAC,IAAI,IAAI,CAAC0Q,QAAQ,CAACO,WAAW;IAAE,GAClEjc,KAAA,CAAAO,aAAA;MAAMK,SAAS,EAAC;IAA6B,GAAE,IAAI,CAAC8a,QAAQ,CAAC3Y,KAAK,CAAQ,EACzE,IAAI,CAACmZ,YAAY,EAAE,CAChB,CACP;;EAGHA,YAAYA,CAAA;IACV,IAAI,CAAC,IAAI,CAAClB,OAAO,CAACgB,qBAAqB,EAAE,OAAO,IAAI;IAEpD,MAAM9Y,QAAQ,GAAG,IAAI,CAACwY,QAAQ,CAACxY,QAAQ;IACvC,OAAQlD,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAwC,GAC7DZ,KAAA,CAAAO,aAAA,CAAC0C,OAAO;MAACrC,SAAS,EAAE,IAAI,CAAC8a,QAAQ,CAACS,aAAa;MAAEjZ,QAAQ,EAAEA,QAAQ;MAAEE,IAAI,EAAE;IAAM,EAAY,CACzF;;EAGE2Y,qBAAqBA,CAAA;IAC7B,OAAO,IAAI,CAACK,WAAW,CAAC,IAAI,CAACV,QAAQ,CAACvb,KAAK,CAAC;;EAG9Cic,WAAWA,CAACjc,KAAiB,EAAE4a,SAAS,GAAG,KAAK;IAC9C,OAAO5a,KAAK,CAACE,GAAG,CAAC,CAACC,IAAI,EAAE+b,SAAS,KAC/Brc,KAAA,CAAAO,aAAA,CAACqa,wBAAwB;MAACta,IAAI,EAAGA,IAAY;MAAEmD,OAAO,EAAE,IAAI,CAACuX,OAAO,CAACvX,OAAO;MAAEqX,WAAW,EAAE,IAAI,CAACE,OAAO;MAAED,SAAS,EAAEA,SAAS;MAAEta,GAAG,EAAE,MAAM,GAAG4b;IAAS,EAA8B,CACrL;;AAEJ;AAEDza,mBAAmB,CAACC,QAAQ,CAACyB,eAAe,CAAC,sBAAsB,EAAG1D,KAAK,IAAI;EAC7E,OAAOI,KAAK,CAACO,aAAa,CAACkb,4BAA4B,EAAE7b,KAAK,CAAC;AACjE,CAAC,CAAC;AClEI,MAAO0c,WAAY,SAAQ5c,iBAAkC;EACjEI,YAAYF,KAAU;IACpB,KAAK,CAACA,KAAK,CAAC;;EAGd,IAAID,KAAKA,CAAA;IACP,OAAO,IAAI,CAACC,KAAK,CAACD,KAAK;;EAEzB,IAAI8D,OAAOA,CAAA;IACT,OAAO,IAAI,CAAC7D,KAAK,CAAC6D,OAAO;;EAE3B5D,eAAeA,CAAA;IACb,OAAO,IAAI,CAACF,KAAK;;EAGnB4C,MAAMA,CAAA;IACJ,IAAI,CAAC,IAAI,CAAC5C,KAAK,IAAI,CAAC,IAAI,CAACA,KAAK,CAAC4c,cAAc,EAAE,OAAO,IAAI;IAE1D,MAAMpc,KAAK,GAAG,IAAI,CAACic,WAAW,EAAE;IAChC,OACEpc,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAE,IAAI,CAACjB,KAAK,CAAC0H,UAAU,CAACkM;IAAI,GACvCpT,KAAK,CACF;;EAGVic,WAAWA,CAAA;IACT,MAAMjc,KAAK,GAAG,IAAI,CAACR,KAAK,CAACS,eAAe;IACxC,OAAOD,KAAK,CAACE,GAAG,CAAC,CAACC,IAAI,EAAE+b,SAAS,KAC/Brc,KAAA,CAAAO,aAAA,CAACqa,wBAAwB;MAACta,IAAI,EAAGA,IAAY;MAAEmD,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEqX,WAAW,EAAE,IAAI,CAACnb,KAAK;MAAEob,SAAS,EAAE,KAAK;MAAEta,GAAG,EAAE,MAAM,GAAG4b;IAAS,EAA8B,CACvK;;AAEJ;AAEDza,mBAAmB,CAACC,QAAQ,CAACyB,eAAe,CAAC,kBAAkB,EAAG1D,KAAK,IAAI;EACzE,OAAOI,KAAK,CAACO,aAAa,CAAC+b,WAAW,EAAE1c,KAAK,CAAC;AAChD,CAAC,CAAC;ACtCI,MAAO4c,eAAgB,SAAQ9c,iBAA6C;EAChF,IAAcC,KAAKA,CAAA;IACjB,OAAO,IAAI,CAACC,KAAK,CAACD,KAAK;;EAEfE,eAAeA,CAAA;IACvB,OAAO,IAAI,CAACF,KAAK;;EAGnBG,YAAYF,KAAU;IACpB,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAAC6c,KAAK,GAAG;MACXC,YAAY,EAAE,IAAI,CAAC/c,KAAK,CAAC+c,YAAY,IAAI;KAC1C;;EAGHxc,aAAaA,CAAA;IACX,IAAI,CAAC,IAAI,CAACP,KAAK,CAAC+B,SAAS,EAAE,OAAO,IAAI;IAEtC,MAAMib,QAAQ,GAAI3R,CAAM,IAAI;MAC1B,MAAM;QAAEuI;MAAI,CAAE,GAAGqJ,QAAQ,CAACC,WAAW;MACrC,IAAI7R,CAAC,CAACxE,MAAM,KAAK+M,IAAI,CAACuJ,aAAa,EAAE;QACnC,IAAI,CAACnd,KAAK,CAAC+c,YAAY,GAAG1R,CAAC,CAACxE,MAAM,CAAC6C,KAAK;;IAE5C,CAAC;IACD,OACErJ,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAY,GACzBZ,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAyB,GACtCZ,KAAA,CAAAO,aAAA,CAAC0C,OAAO;MAACC,QAAQ,EAAC,aAAa;MAACE,IAAI,EAAE;IAAM,EAAY,CACpD,EACNpD,KAAA,CAAAO,aAAA;MAAO4O,IAAI,EAAC,MAAM;MAACvO,SAAS,EAAC,mBAAmB;MAAA,cAClC,IAAI,CAACjB,KAAK,CAACod,uBAAuB;MAC9C3F,WAAW,EAAE,IAAI,CAACzX,KAAK,CAACod,uBAAuB;MAC/C1T,KAAK,EAAE,IAAI,CAACoT,KAAK,CAACC,YAAY;MAC9BC,QAAQ,EAAEA;IAAQ,EAAU,EAC9B3c,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAqB,GAClCZ,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAA6B,GAAG,IAAI,CAACjB,KAAa,CAACqd,gBAAgB,CAAO,EACzFhd,KAAA,CAAAO,aAAA,CAACyE,eAAe;MAACrF,KAAK,EAAE,IAAI,CAACA,KAAK,CAACsd;IAAe,EAAoB,CAClE,CACF;;AAEX;AAEDrb,mBAAmB,CAACC,QAAQ,CAACyB,eAAe,CAAC,YAAY,EAAG1D,KAAK,IAAI;EACnE,OAAOI,KAAK,CAACO,aAAa,CACxBic,eAAe,EACf5c,KAA8B,CAC/B;AACH,CAAC,CAAC;AC7CI,MAAOsd,eAAgB,SAAQxd,iBAAkD;EAGrFI,YAAYF,KAAiC;IAC3C,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACG,OAAO,GAAGC,KAAK,CAACC,SAAS,EAAE;;EAGlCe,kBAAkBA,CAACC,SAAc,EAAEC,SAAc;IAC/C,KAAK,CAACF,kBAAkB,CAACC,SAAS,EAAEC,SAAS,CAAC;IAC9C,MAAMC,SAAS,GAAG,IAAI,CAACpB,OAAO,CAACqB,OAAO;IACtC,IAAID,SAAS,EAAE;MACb,IAAI,CAAC6Z,OAAO,CAAC5Y,WAAW,CAACjB,SAAS,CAAC;;;EAIvCG,iBAAiBA,CAAA;IACf,KAAK,CAACA,iBAAiB,EAAE;IACzB,MAAMH,SAAS,GAAG,IAAI,CAACpB,OAAO,CAACqB,OAAO;IACtC,IAAID,SAAS,EAAE;MACb,IAAI,CAAC6Z,OAAO,CAAC5Y,WAAW,CAACjB,SAAS,CAAC;;;EAGvCI,oBAAoBA,CAAA;IAClB,IAAI,CAACyZ,OAAO,CAACmC,aAAa,EAAE;IAC5B,KAAK,CAAC5b,oBAAoB,EAAE;;EAE9B,IAAWkC,OAAOA,CAAA;IAChB,OAAO,IAAI,CAAC7D,KAAK,CAACD,KAAK;;EAEzB,IAAWqb,OAAOA,CAAA;IAChB,OAAO,IAAI,CAACvX,OAAO,CAACuX,OAAO;;EAEnBnb,eAAeA,CAAA;IACvB,OAAO,IAAI,CAACmb,OAAO;;EAGrBoB,WAAWA,CAACjc,KAAiB,EAAE4a,SAAS,GAAG,KAAK;IAC9C,OAAO5a,KAAK,CAACE,GAAG,CAAC,CAACC,IAAI,EAAE+b,SAAS,KAAI;MACnC,OAAOrc,KAAA,CAAAO,aAAA,CAACqa,wBAAwB;QAACta,IAAI,EAAGA,IAAY;QAAEmD,OAAO,EAAE,IAAI,CAACA,OAAO;QAAEqX,WAAW,EAAE,IAAI,CAACE,OAAO;QAAED,SAAS,EAAEA,SAAS;QAAEta,GAAG,EAAEH,IAAI,CAACI;MAAU,EAA8B;IAClL,CAAC,CAAC;;EAGJ0c,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACpC,OAAO,CAACqC,UAAU,CAAChd,GAAG,CAAC,CAACqb,QAAQ,EAAEnN,KAAK,KAAI;MACrD,OAAOvO,KAAA,CAAAO,aAAA,CAACkb,4BAA4B;QAACC,QAAQ,EAAEA,QAAQ;QAAEV,OAAO,EAAE,IAAI,CAACA,OAAO;QAAEva,GAAG,EAAE,UAAU,GAAG8N;MAAK,EAAkC;IAC3I,CAAC,CAAC;;EAEJ+O,YAAYA,CAAA;IACV,MAAMC,YAAY,GAAG,IAAI,CAACvC,OAAO,CAACwC,iBAAiB,GACjDxd,KAAA,CAAAO,aAAA,CAAAP,KAAA,CAAAsM,QAAA,QACEtM,KAAA,CAAAO,aAAA,CAACqa,wBAAwB;MAACta,IAAI,EAAE,IAAI,CAAC0a,OAAO,CAACyC,UAAiB;MAAEha,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEqX,WAAW,EAAE,IAAI,CAACE,OAAO;MAAED,SAAS,EAAE,IAAI,CAACC,OAAO,CAACwC,iBAAiB;MAAE/c,GAAG,EAAE;IAAY,EAA8B,CAC3M,GACH,IAAI;IACN,OAAQT,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAA+B,GACnD2c,YAAY,EACbvd,KAAA,CAAAO,aAAA,CAACic,eAAe;MAAC7c,KAAK,EAAE,IAAI,CAACqb,OAAO,CAAC0C;IAAa,EAAoB,EACtE1d,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAyE,EAAO,CAC3F;;EAGR2B,MAAMA,CAAA;IACJ,MAAMob,MAAM,GAAG,IAAI,CAAC3C,OAAO,CAAC4C,UAAU,GAAG,IAAI,CAACN,YAAY,EAAE,GAAG,IAAI;IACnE,MAAMlG,WAAW,GAAG,IAAI,CAAC4D,OAAO,CAAC9I,eAAe,GAAGlS,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAA0B,GAAE,IAAI,CAACoa,OAAO,CAAC6C,qBAAqB,CAAO,GAAG,IAAI;IAC9I,OACE7d,KAAA,CAAAO,aAAA;MAAKI,GAAG,EAAE,IAAI,CAACZ,OAAO;MAAEa,SAAS,EAAE,IAAI,CAACoa,OAAO,CAAC8C,UAAU;MAAEhd,KAAK,EAAE,IAAI,CAACka,OAAO,CAACja,YAAY;IAAE,GAC5Ff,KAAA,CAAAO,aAAA;MAAKqV,MAAM,EAAG5K,CAAC,IAAK,IAAI,CAACgQ,OAAO,CAAC+C,QAAQ,CAAC/S,CAAC,CAAC;MAAEpK,SAAS,EAAC;IAAoB,GACzE+c,MAAM,EACNvG,WAAW,EACZpX,KAAA,CAAAO,aAAA,CAACyd,MAAM,QACH,IAAI,CAAChD,OAAO,CAACiD,oBAAoB,GAChCje,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAuB,GACpC,IAAI,CAACwb,WAAW,CAAC,IAAI,CAACpB,OAAO,CAAC5a,eAAe,EAAE,IAAI,CAAC4a,OAAO,CAACwC,iBAAiB,CAAC,CAC3E,GACJ,IAAI,CAACJ,gBAAgB,EAAE,CAEpB,CACL,CACF;;AAGX;AAEDxb,mBAAmB,CAACC,QAAQ,CAACyB,eAAe,CAAC,aAAa,EAAG1D,KAAK,IAAI;EACpE,OAAOI,KAAK,CAACO,aAAa,CAAC2c,eAAe,EAAEtd,KAAK,CAAC;AACpD,CAAC,CAAC;AC5FI,MAAOse,gBAAiB,SAAQxe,iBAA2B;EAAjEI,YAAA;;IAiBU,KAAAqe,oBAAoB,GAAG,CAACC,MAAW,EAAE9X,OAAY,KAAS;MAChE,IAAI,IAAI,CAAC+X,WAAW,EAAE;MACtB,MAAMC,aAAa,GAAG,CACpB,iBAAiB,EACjB,iBAAiB,EACjB,mBACD;MACD,IAAIA,aAAa,CAACC,OAAO,CAACjY,OAAO,CAACuE,IAAI,CAAC,GAAG,CAAC,EAAE;MAC7C,IAAI2T,GAAG,GAAQ,EAAE;MACjB,KAAK,IAAIjZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+Y,aAAa,CAAC7Y,MAAM,EAAEF,CAAC,EAAE,EAAE;QAC7C,IAAIkZ,QAAQ,GAAGH,aAAa,CAAC/Y,CAAC,CAAC;QAC/BiZ,GAAG,CAACC,QAAQ,CAAC,GAAG,IAAI,CAACzY,MAAM,CAACyY,QAAQ,CAAC;;MAEvC,IAAI,CAACC,QAAQ,CAACF,GAAG,CAAC;IACpB,CAAC;;EA9BDld,iBAAiBA,CAAA;IACf,KAAK,CAACA,iBAAiB,EAAE;IACzB,IAAI,CAACqd,UAAU,EAAE;;EAEnB3d,kBAAkBA,CAACC,SAAc,EAAEC,SAAc;IAC/C,KAAK,CAACF,kBAAkB,CAACC,SAAS,EAAEC,SAAS,CAAC;IAC9C,IAAI,CAACyd,UAAU,EAAE;;EAEXA,UAAUA,CAAA;IAChB,IACE,CAAC,IAAI,CAAC3Y,MAAM,IACZ,IAAI,CAACA,MAAM,CAAC4Y,iBAAiB,CAACC,OAAO,CAAC,IAAI,CAACV,oBAAoB,CAAC,EAEhE;IACF,IAAI,CAACnY,MAAM,CAAC4Y,iBAAiB,CAAC5I,GAAG,CAAC,IAAI,CAACmI,oBAAoB,CAAC;;EAiB9D5c,oBAAoBA,CAAA;IAClB,KAAK,CAACA,oBAAoB,EAAE;IAC5B,IAAI,IAAI,CAACyE,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAAC4Y,iBAAiB,CAAClL,MAAM,CAAC,IAAI,CAACyK,oBAAoB,CAAC;;;EAInE,IAAcnY,MAAMA,CAAA;IAClB,OAAO,IAAI,CAACpG,KAAK,CAACoG,MAAM;;EAE1B,IAAc8Y,QAAQA,CAAA;IACpB,OAAO,IAAI,CAAClf,KAAK,CAACkf,QAAQ;;EAE5B,IAAcC,KAAKA,CAAA;IACjB,OAAO,IAAI,CAACD,QAAQ,IAAI,KAAK;;EAErB/M,SAASA,CAAA;IACjB,OAAO,IAAI,CAACgN,KAAA,GACR,IAAI,CAAC/Y,MAAM,CAACgZ,sBAAA,GACZ,IAAI,CAAChZ,MAAM,CAACiZ,yBAAyB;;EAE3C/e,aAAaA,CAAA;IACX,OAAO0B,mBAAmB,CAACC,QAAQ,CAACtB,aAAa,CAC/C,IAAI,CAACyF,MAAM,CAACkZ,wBAAwB,EAAE,EACtC;MAAElZ,MAAM,EAAE,IAAI,CAACA,MAAM;MAAEvE,GAAG,EAAE,IAAI,CAACuE,MAAM,CAACvE,GAAG;MAAEsd,KAAK,EAAE,IAAI,CAACA;IAAK,CAAE,CACjE;;AAEJ;ACxDK,MAAOI,kBAAmB,SAAQzf,iBAA6C;EACnFI,YAAYF,KAA4B;IACtC,KAAK,CAACA,KAAK,CAAC;;EAGJC,eAAeA,CAAA;IACvB,OAAO,IAAI,CAACD,KAAK,CAACD,KAAK;;EAGfO,aAAaA,CAAA;IACrB,MAAMP,KAAK,GAAG,IAAI,CAACC,KAAK,CAACD,KAAK;IAC9B,IAAI,CAACA,KAAK,CAACkG,OAAO,EAAE,OAAO,IAAI;IAC/B,MAAMkQ,MAAM,GAAGvT,eAAe,CAC5BxC,KAAA,CAAAO,aAAA;MACEK,SAAS,EAAEjB,KAAK,CAACyf,eAAe;MAChCrc,KAAK,EAAEpD,KAAK,CAAC0D,OAAO;MACpBV,OAAO,EAAEA,CAAA,KAAK;QAAGhD,KAAK,CAACiD,MAAM,EAAE;MAAC;IAAE,GAElC5C,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAuB,GACpCZ,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAiC,GAC9CZ,KAAA,CAAAO,aAAA,CAAC0C,OAAO;MAACC,QAAQ,EAAEvD,KAAK,CAACuD,QAAQ;MAAEE,IAAI,EAAE;IAAM,EAAY,CACvD,CACF,CACF,EAAEzD,KAAK,CACd;IACD,OACEK,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAiB,GAC7BmV,MAAM,CACH;;AAGX;AC3BD,MAAMsJ,UAAW,SAAQ3f,iBAA2B;EAClDI,YAAYF,KAAuB;IACjC,KAAK,CAACA,KAAK,CAAC;;EAGJC,eAAeA,CAAA;IACvB,OAAO,IAAI,CAACD,KAAK,CAACD,KAAK;;EAGlBoS,SAASA,CAAA;IACd,IAAI,CAAC,IAAI,CAACnS,KAAK,CAACD,KAAK,EAAE,OAAO,KAAK;IACnC,OAAO,KAAK,CAACoS,SAAS,EAAE;;EAE1B7R,aAAaA,CAAA;IACX,OACEF,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAE,IAAI,CAAChB,KAAK,CAACD,KAAK,CAAC2f;IAAgB,GAC/Ctf,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAiC,GAC9CZ,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAmC,GAChDZ,KAAA,CAAAO,aAAA,CAAC4e,kBAAkB;MAACxf,KAAK,EAAE,IAAI,CAACC,KAAK,CAACD,KAAK,CAAC4f;IAAoB,EAAwB,CACpF,EACNvf,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAA6B,GAC1CZ,KAAA,CAAAO,aAAA,aAAW,CACP,EACNP,KAAA,CAAAO,aAAA,CAACyd,MAAM,QACLhe,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAyB,GACtCZ,KAAA,CAAAO,aAAA,CAACif,aAAa;MAAC7f,KAAK,EAAE,IAAI,CAACC,KAAK,CAACD,KAAK,CAAC8f;IAAU,EAAkB,CAC/D,CACC,CACL,EACNzf,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAoC,GACjDZ,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAyB,GACtCZ,KAAA,CAAAO,aAAA,CAACif,aAAa;MAAC7f,KAAK,EAAE,IAAI,CAACC,KAAK,CAACD,KAAK,CAAC+f;IAAa,EAAkB,CAClE,CACF,CACF;;AAIX;AAED9d,mBAAmB,CAACC,QAAQ,CAACyB,eAAe,CAAC,iBAAiB,EAAG1D,KAAK,IAAI;EACxE,OAAOI,KAAK,CAACO,aAAa,CAAC8e,UAAU,EAAEzf,KAAK,CAAC;AAC/C,CAAC,CAAC;AAEF,MAAM4f,aAAc,SAAQ9f,iBAAkD;EAC5EI,YAAYF,KAAiC;IAC3C,KAAK,CAACA,KAAK,CAAC;;EAGJC,eAAeA,CAAA;IACvB,OAAO,IAAI,CAACD,KAAK,CAACD,KAAK;;EAGfO,aAAaA,CAAA;IACrB,OAAOF,KAAA,CAAAO,aAAA,CAAAP,KAAA,CAAAsM,QAAA,QAAG,IAAI,CAAC1M,KAAK,CAACD,KAAK,CAACyS,OAAO,CAAC/R,GAAG,CACpC,CAACC,IAAY,EAAE+b,SAAiB,KAAKrc,KAAA,CAAAO,aAAA,CAAC4e,kBAAkB;MAACxf,KAAK,EAAEW,IAAkB;MAAEG,GAAG,EAAE,MAAM,GAAG4b;IAAS,EAAuB,CACnI,CAAI;;AAER;AC3DK,MAAOsD,oBAAqB,SAAQjgB,iBAA2C;EACnF,IAAIC,KAAKA,CAAA;IACP,OAAO,IAAI,CAACC,KAAK,CAACD,KAAK;;EAGfE,eAAeA,CAAA;IACvB,OAAO,IAAI,CAACF,KAAK;;EAGnBO,aAAaA,CAAA;IACX,MAAM6C,KAAK,GAAG,CAAC,CAAC,IAAI,CAACpD,KAAK,CAACoD,KAAK,GAC9B/C,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAA+B,GAAE,IAAI,CAACjB,KAAK,CAACoD,KAAK,CAAO,GACrE,IAAI;IACR,OACE/C,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAgC,GAC7CZ,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAiC,GAC9CZ,KAAA,CAAAO,aAAA,CAACyE,eAAe;MAACrF,KAAK,EAAE,IAAI,CAACA,KAAK,CAACsF;IAAO,EAAoB,CAC1D,EACLlC,KAAK,CACF;;AAGX;AAEDnB,mBAAmB,CAACC,QAAQ,CAACyB,eAAe,CAAC,6BAA6B,EAAG1D,KAAK,IAAI;EACpF,OAAOI,KAAK,CAACO,aAAa,CAACof,oBAAoB,EAAE/f,KAAK,CAAC;AACzD,CAAC,CAAC;AC1BF,MAAMggB,yBAA0B,SAAQlgB,iBAAuD;EAC7F,IAAImgB,qBAAqBA,CAAA;IACvB,OAAO,IAAI,CAACjgB,KAAK,CAACD,KAAK;;EAGfE,eAAeA,CAAA;IACvB,OAAO,IAAI,CAACggB,qBAAqB;;EAGnC3f,aAAaA,CAAA;IACX,MAAM6V,MAAM,GAAGvT,eAAe,CAC5BxC,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAE,IAAI,CAACif,qBAAqB,CAACT,eAAe;MAAEzc,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACkd,qBAAqB,CAACjd,MAAM;IAAE,GAC5G5C,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAA6B,GAC1CZ,KAAA,CAAAO,aAAA;MAAMK,SAAS,EAAC;IAA2B,GAAE,IAAI,CAACif,qBAAqB,CAAC9c,KAAK,CAAQ,EACrF/C,KAAA,CAAAO,aAAA;MAAMK,SAAS,EAAC;IAA8B,GAAE,IAAI,CAACif,qBAAqB,CAACxc,OAAO,CAAQ,CACtF,CACF,EAAE,IAAI,CAACzD,KAAK,CAACD,KAAK,CACzB;IAED,OACEK,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAiD,GAC9DZ,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAA4E,GACzFZ,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAA6B,GACzCmV,MAAM,EACP/V,KAAA,CAAAO,aAAA,CAACqS,KAAK;MAACjT,KAAK,EAAE,IAAI,CAACkgB,qBAAqB,CAAChN;IAAU,EAAU,CACzD,CACF,CACF;;AAGX;AAEDjR,mBAAmB,CAACC,QAAQ,CAACyB,eAAe,CAAC,mCAAmC,EAAG1D,KAAK,IAAI;EAC1F,OAAOI,KAAK,CAACO,aAAa,CAACqf,yBAAyB,EAAEhgB,KAAK,CAAC;AAC9D,CAAC,CAAC;AClCF,MAAMkgB,aAAc,SAAQpgB,iBAA2C;EACrE,IAAIC,KAAKA,CAAA;IACP,OAAO,IAAI,CAACC,KAAK,CAACD,KAAK;;EAGfE,eAAeA,CAAA;IACvB,OAAO,IAAI,CAACF,KAAK;;EAGnBO,aAAaA,CAAA;IACX,OACEF,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAA8D,GACzE,IAAI,CAACjB,KAAK,CAACogB,QAAQ,GACnB/f,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAA6B,GAC1CZ,KAAA,CAAAO,aAAA;MAAMK,SAAS,EAAC;IAA2B,GAAE,IAAI,CAACjB,KAAK,CAACoD,KAAK,CAAQ,EACrE/C,KAAA,CAAAO,aAAA;MAAMK,SAAS,EAAC;IAA8B,GAAE,IAAI,CAACjB,KAAK,CAACogB,QAAQ,CAAQ,IAE3E/f,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAA+B,GAAE,IAAI,CAACjB,KAAK,CAACoD,KAAK,CAAO,CAEvE;;AAEX;AAEDnB,mBAAmB,CAACC,QAAQ,CAACyB,eAAe,CAAC,qBAAqB,EAAG1D,KAAK,IAAI;EAC5E,OAAOI,KAAK,CAACO,aAAa,CAACuf,aAAa,EAAElgB,KAAK,CAAC;AAClD,CAAC,CAAC;ACzBI,MAAOogB,gBAAiB,SAAQtgB,iBAA8C;EAGlF,IAAIC,KAAKA,CAAA;IACP,OAAO,IAAI,CAACC,KAAK,CAACD,KAAK;;EAGzBG,YAAYF,KAA6B;IACvC,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACqgB,YAAY,GAAGjgB,KAAK,CAACC,SAAS,EAAE;;EAG7BJ,eAAeA,CAAA;IACvB,OAAO,IAAI,CAACF,KAAK;;EAGnB2B,iBAAiBA,CAAA;IACf,KAAK,CAACA,iBAAiB,EAAE;IACzB,IAAI,CAAC3B,KAAK,CAACugB,iBAAiB,CAAC,IAAI,CAACD,YAAY,CAAC7e,OAAO,CAAC;;EAEzDG,oBAAoBA,CAAA;IAClB,KAAK,CAACA,oBAAoB,EAAE;IAC5B,IAAI,CAAC5B,KAAK,CAACwgB,kBAAkB,EAAE;;EAE1BpO,SAASA,CAAA;IACd,IAAI,CAAC,IAAI,CAACpS,KAAK,EAAE,OAAO,KAAK;IAC7B,OAAO,KAAK,CAACoS,SAAS,EAAE;;EAG1B7R,aAAaA,CAAA;IACX,MAAMY,KAAK,GAAG;MAAEsf,OAAO,EAAE,IAAI,CAACzgB,KAAK,CAAC0gB,UAAU,GAAG,EAAE,GAAG;IAAM,CAAE;IAC9D,MAAMC,cAAc,GAAG;MAAEF,OAAO,EAAE,IAAI,CAACzgB,KAAK,CAAC4gB,eAAe,GAAG,EAAE,GAAG;IAAM,CAAE;IAC5E,MAAMpgB,KAAK,GAAG,IAAI,CAACR,KAAK,CAAC6gB,KAAK,CAACngB,GAAG,CAAEmR,IAAI,IAAKxR,KAAA,CAAAO,aAAA,CAACkgB,WAAW;MAACjP,IAAI,EAAEA,IAAI;MAAE/Q,GAAG,EAAE+Q,IAAI,CAAC1P;IAAE,EAAI,CAAC;IACvF,MAAM4e,UAAU,GAAG9e,mBAAmB,CAACC,QAAQ,CAACtB,aAAa,CAAC,IAAI,CAACZ,KAAK,CAACkc,MAAM,CAACla,SAAS,EAAE;MAAEhC,KAAK,EAAE,IAAI,CAACA,KAAK,CAACkc,MAAM,CAAC8E;IAAc,CAAE,CAAC;IACvI,IAAIC,QAAQ,GAAG,IAAI;IACnB,IAAI,IAAI,CAACjhB,KAAK,CAACkhB,qBAAqB,EAAE;MACpCD,QAAQ,GAAGhf,mBAAmB,CAACC,QAAQ,CAACtB,aAAa,CAAC,IAAI,CAACZ,KAAK,CAACkhB,qBAAqB,EAAE;QAAElhB,KAAK,EAAE,IAAI,CAACA,KAAK,CAACmhB;MAAqB,CAAE,CAAC;;IAGtI,OACE9gB,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAE,IAAI,CAACjB,KAAK,CAACmL,OAAO;MAAEhK,KAAK,EAAEA;IAAK,GAC9Cd,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC,sBAAsB;MAAC+B,OAAO,EAAEA,CAAA,KAAM,IAAI,CAAChD,KAAK,CAACohB,eAAe,EAAE;MAAEjgB,KAAK,EAAEwf;IAAc,EAAQ,EAChHtgB,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAoC,GACjDZ,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC,iCAAiC;MAACE,KAAK,EAAEwf;IAAc,GACpEtgB,KAAA,CAAAO,aAAA;MAAKI,GAAG,EAAE,IAAI,CAACsf,YAAY;MAAErf,SAAS,EAAC;IAAyB,GAC7D8f,UAAU,EACX1gB,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAiC,GAAET,KAAK,CAAO,CAC1D,CACF,EACLygB,QAAQ,CACL,CACF;;AAGX;AAED,MAAMH,WAAY,SAAQ/gB,iBAA2B;EACnD,IAAY8R,IAAIA,CAAA;IACd,OAAO,IAAI,CAAC5R,KAAK,CAAC4R,IAAI;;EAGd3R,eAAeA,CAAA;IACvB,OAAO,IAAI,CAAC2R,IAAI;;EAGlBtR,aAAaA,CAAA;IACX,IAAI,CAAC,IAAI,CAACsR,IAAI,CAAC3L,OAAO,EAAE,OAAO,IAAI;IAEnC,MAAMlE,SAAS,GAAGC,mBAAmB,CAACC,QAAQ,CAACtB,aAAa,CAAC,IAAI,CAACiR,IAAI,CAACwP,aAAa,EAAE;MAAErhB,KAAK,EAAE,IAAI,CAAC6R,IAAI,CAAC7I;IAAa,CAAE,CAAC;IAEzH,OAAOhH,SAAS;;AAEnB;AAEDkF,oBAAoB,CAAChF,QAAQ,CAACsT,gBAAgB,CAAC,mBAAmB,EAAGvV,KAAK,IAAI;EAC5E,OAAOI,KAAK,CAACO,aAAa,CAACkgB,WAAW,EAAE7gB,KAAK,CAAC;AAChD,CAAC,CAAC;AAIFgC,mBAAmB,CAACC,QAAQ,CAACyB,eAAe,CAAC,cAAc,EAAG1D,KAAK,IAAI;EACrE,OAAOI,KAAK,CAACO,aAAa,CAACyf,gBAAgB,EAAEpgB,KAAK,CAAC;AACrD,CAAC,CAAC;ACxFI,MAAOqhB,uBAAwB,SAAQjhB,KAAK,CAAC4M,SAAmB;EACpErK,MAAMA,CAAA;IACJ,OACEvC,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAA8B,EACvC;;AAGX;AAEDgB,mBAAmB,CAACC,QAAQ,CAACyB,eAAe,CAAC,8BAA8B,EAAG1D,KAAU,IAAI;EAC1F,OAAOI,KAAK,CAACO,aAAa,CAAC0gB,uBAAuB,EAAErhB,KAAK,CAAC;AAC5D,CAAC,CAAC;ACVI,MAAOshB,mBAAoB,SAAQxhB,iBAAwC;EAC/E,IAAIY,IAAIA,CAAA;IACN,OAAO,IAAI,CAACV,KAAK,CAACU,IAAI;;EAEdT,eAAeA,CAAA;IACvB,OAAO,IAAI,CAACS,IAAI;;EAGlBJ,aAAaA,CAAA;IACX,MAAMI,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,OACEN,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAEN,IAAI,CAAC2F,IAAI,CAAC4N;IAAY,GACpC7T,KAAA,CAAAO,aAAA;MAAMK,SAAS,EAAEN,IAAI,CAAC2F,IAAI,CAACkb;IAAkB,GAAG7gB,IAAI,CAAC2F,IAAI,CAACmb,eAAe,CAAQ,EAChFxf,mBAAmB,CAACC,QAAQ,CAACtB,aAAa,CAAC,6BAA6B,EAAE;MAAED,IAAI,EAAE,IAAI,CAACA;IAAI,CAAE,CAAC,CAC3F;;AAGX;AAEDsB,mBAAmB,CAACC,QAAQ,CAACyB,eAAe,CAAC,2BAA2B,EAAG1D,KAAK,IAAI;EAClF,OAAOI,KAAK,CAACO,aAAa,CAAC2gB,mBAAmB,EAAEthB,KAAK,CAAC;AACxD,CAAC,CAAC;ACnBI,MAAOyhB,qBAAsB,SAAQxZ,mBAA6B;EAGtE/H,YAAYF,KAAU;;IACpB,KAAK,CAACA,KAAK,CAAC;IA8BN,KAAA0hB,gBAAgB,GAAG,CAAClD,MAAW,EAAE9X,OAAY,KAAI;MACvD,IAAI,CAACoY,QAAQ,CAAC;QAAE6C,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC9E,KAAK,IAAI,IAAI,CAACA,KAAK,CAAC8E,OAAO,GAAG,IAAI,CAAC9E,KAAK,CAAC8E,OAAO,GAAG,CAAC,GAAG;MAAC,CAAE,CAAC;IAC7F,CAAC;IAmCO,KAAA3L,MAAM,GAAI3M,KAAU,IAAI;MAC9B,IAAI,IAAI,CAACuY,iBAAiB,CAACpgB,OAAO,EAAE;QAClC,IAAI,CAACogB,iBAAiB,CAACpgB,OAAO,CAACqgB,UAAU,GAAG,KAAK;;MAEnD,IAAI,CAACC,SAAS,CAAC,aAAa,CAAC,GAAG,KAAK;MACrC,IAAI,CAACC,WAAW,GAAG,KAAK;MACxB,IAAI,CAACC,SAAS,CAAChM,MAAM,CAAC3M,KAAK,CAACS,WAAW,CAAC;MACxC,OAAO,IAAI,CAACkY,SAAS,CAACC,SAAS;IACjC,CAAC;IACO,KAAAC,kBAAkB,GAAI7Y,KAAU,IAAI;MAC1C,IAAI,CAAC2Y,SAAS,CAACE,kBAAkB,CAAC7Y,KAAK,CAACS,WAAW,CAAC;IACtD,CAAC;IACO,KAAAqY,gBAAgB,GAAI9Y,KAAU,IAAI;MACxC,IAAI,CAAC2Y,SAAS,CAACG,gBAAgB,CAAC9Y,KAAK,CAACS,WAAW,CAAC;IACpD,CAAC;IACO,KAAAsY,OAAO,GAAI/Y,KAAU,IAAI;MAC/B,IAAI,CAAC2Y,SAAS,CAACI,OAAO,CAAC/Y,KAAK,CAACS,WAAW,CAAC;IAC3C,CAAC;IACO,KAAAuY,OAAO,GAAIhZ,KAAU,IAAI;MAC/B,IAAI,CAAC2Y,SAAS,CAACK,OAAO,CAAChZ,KAAK,CAACS,WAAW,CAAC;IAC3C,CAAC;IACO,KAAAiY,WAAW,GAAG,KAAK;IACnB,KAAAzU,OAAO,GAAIjE,KAAU,IAAI;MAC/B,IAAI,CAAC2Y,SAAS,CAAC1U,OAAO,CAACjE,KAAK,CAACS,WAAW,CAAC;MACzC,IAAI,CAACiY,WAAW,GAAG,IAAI;IACzB,CAAC;IACO,KAAAO,SAAS,GAAIjZ,KAA2C,IAAI;MAClE,OAAO,IAAI,CAAC2Y,SAAS,CAACM,SAAS,CAACjZ,KAAK,CAACS,WAAW,CAAC;IACpD,CAAC;IACO,KAAAyY,OAAO,GAAIlZ,KAA2C,IAAI;MAChE,OAAO,IAAI,CAAC2Y,SAAS,CAACO,OAAO,CAAClZ,KAAK,CAACS,WAAW,CAAC;IAClD,CAAC;IACO,KAAA0Y,SAAS,GAAInZ,KAAwC,IAAa;MACxE,OAAO,IAAI,CAAC2Y,SAAS,CAACQ,SAAS,CAACnZ,KAAK,CAACS,WAAW,CAAC;IACpD,CAAC;IACO,KAAA2Y,IAAI,GAAIpZ,KAAU,IAAI;MAC5B,IAAI,CAAC2Y,SAAS,CAACS,IAAI,CAACpZ,KAAK,CAAC;MAC1B,IAAI,CAACyY,SAAS,CAAC,aAAa,CAAC,GAAG,KAAK;IACvC,CAAC;IACO,KAAAY,IAAI,GAAIrZ,KAAU,IAAI;MAC5B,IAAI,CAACuY,iBAAiB,CAACpgB,OAAO,CAACmhB,KAAK,EAAE;;MAEtC,IAAI,CAACb,SAAS,CAAC,aAAa,CAAC,GAAG,IAAI;MACpC,IAAI,CAACE,SAAS,CAACjf,OAAO,CAACsG,KAAK,CAAC;IAC/B,CAAC;IAIO,KAAA1E,SAAS,GAAG;MAClBC,MAAM,EAAE,CAAA8T,EAAA,OAAI,CAACoJ,SAAS,cAAApJ,EAAA,uBAAAA,EAAA,CAAElE;KACzB;IApHC,IAAI,CAACqI,KAAK,GAAG;MAAE8E,OAAO,EAAE;IAAC,CAAE;IAC3B,IAAI,CAACC,iBAAiB,GAAGxhB,KAAK,CAACC,SAAS,EAAE;;EAElC6H,WAAWA,CAAClI,KAAU;IAC9B,IAAI,IAAI,CAACgiB,SAAS,EAAE;MAClB,IAAI,CAACA,SAAS,CAACnZ,OAAO,EAAE;;IAE1B,IAAI,CAACmZ,SAAS,GAAG,IAAIY,yBAAyB,CAAC,IAAI,CAACd,SAAS,EAAE,IAAI,CAACje,OAAO,CAAC;;EAEpE4E,oBAAoBA,CAAA;IAC5B,OAAO,CAAC,SAAS,EAAE,WAAW,CAAC;;EAEjC,IAAcqZ,SAASA,CAAA;IACrB,OAAO,IAAI,CAAC9hB,KAAK,CAAC4H,MAAM,CAACA,MAAM;;EAEjC,IAAc/D,OAAOA,CAAA;IACnB,OAAO,IAAI,CAAC7D,KAAK,CAAC4H,MAAM,CAAC/D,OAAO;;EAElC,IAAY3C,KAAKA,CAAA;IACf,OAAO,IAAI,CAAClB,KAAK,CAACkB,KAAK;;EAGfjB,eAAeA,CAAA;IACvB,OAAO,IAAI,CAAC+hB,SAAS;;EAGvB,IAAWC,SAASA,CAAA;IAClB,OAAO,IAAI,CAACD,SAAS,CAACC,SAAS;;EAK1BvgB,iBAAiBA,CAAA;IACtB,KAAK,CAACA,iBAAiB,EAAE;IACzB,IAAI,CAAC,IAAI,CAACogB,SAAS,EAAE;IACrB,IAAI,CAACE,SAAS,CAACa,YAAY,CAAC,IAAI,CAACf,SAAS,CAAC;IAC3C,IAAI,CAACE,SAAS,CAACc,gBAAgB,GAAG,MAAM,IAAI,CAAClB,iBAAiB,CAACpgB,OAAO;IACtE,IAAI,CAACwgB,SAAS,CAACe,UAAU,GAAG,MAAK;MAC/B,IAAI,CAACnB,iBAAiB,CAACpgB,OAAO,CAACwhB,IAAI,EAAE;MACrC,IAAI,CAACpB,iBAAiB,CAACpgB,OAAO,CAACqgB,UAAU,GAAG,KAAK;IACnD,CAAC;IACD,IAAI,CAACG,SAAS,CAACxf,WAAW,EAAE;IAC5B,IAAI,CAACsf,SAAS,CAACmB,eAAe,CAAC7M,GAAG,CAAC,IAAI,CAACsL,gBAAgB,CAAC;IACzD,IAAI,IAAI,CAACI,SAAS,CAAC,aAAa,CAAC,EAAE;MACjC,IAAI,CAACF,iBAAiB,CAACpgB,OAAO,CAACmhB,KAAK,EAAE;;;;EAInCvhB,kBAAkBA,CAACC,SAAc,EAAEC,SAAc;IACtD,KAAK,CAACF,kBAAkB,CAACC,SAAS,EAAEC,SAAS,CAAC;IAC9C,IAAI,CAAC0gB,SAAS,CAACa,YAAY,CAAC,IAAI,CAACf,SAAS,CAAC;IAC3C,IAAI,CAACE,SAAS,CAACxf,WAAW,EAAE;IAC5B,IAAI,CAACsf,SAAS,CAACmB,eAAe,CAAC7M,GAAG,CAAC,IAAI,CAACsL,gBAAgB,CAAC;;EAEpD/f,oBAAoBA,CAAA;IACzB,KAAK,CAACA,oBAAoB,EAAE;IAC5B,IAAI,CAACqgB,SAAS,CAAClV,YAAY,EAAE;IAC7B,IAAI,CAAC,IAAI,CAACgV,SAAS,EAAE;IACrB,IAAI,CAACA,SAAS,CAACmB,eAAe,CAACnP,MAAM,CAAC,IAAI,CAAC4N,gBAAgB,CAAC;;EAE9D,IAAYlK,WAAWA,CAAA;IACrB,OAAO,IAAI,CAACwK,SAAS,CAACxK,WAAW;;EAEnC,IAAY0L,eAAeA,CAAA;IACzB,OAAO,IAAI,CAAClB,SAAS,CAACkB,eAAe;;EA+CvC,IAAYliB,SAASA,CAAA;IACnB,OAAO,IAAI,CAACghB,SAAS,CAAChhB,SAAS,CAAC,IAAI,CAAC8gB,SAAS,CAACtN,YAAY,CAAC;;EAKvD7R,MAAMA,CAAA;IACX,IAAI,CAAC,IAAI,CAACmf,SAAS,EAAE;MACnB,OAAO,IAAI;;IAEb,IAAIqB,OAAO,GAAG,IAAI;IAClB,IAAI,IAAI,CAACrB,SAAS,CAACsB,OAAO,EAAE;MAC1B,IAAI,IAAI,CAACze,SAAS,CAACC,MAAM,KAAK,IAAI,CAACkd,SAAS,CAACtN,YAAY,EAAE;QACzD,IAAI,CAAC7P,SAAS,GAAG;UAAEC,MAAM,EAAE,IAAI,CAACkd,SAAS,CAACtN;QAAY,CAAE;;MAE1D2O,OAAO,GACL/iB,KAAA,CAAAO,aAAA;QACEM,IAAI,EAAC,SAAS;QACdF,GAAG,EAAE,IAAI,CAAC6gB,iBAAiB;QAC3B5gB,SAAS,EAAC,yCAAyC;QACnDkiB,eAAe,EAAE,IAAI,CAACA,eAAe;QACrCG,UAAU,EAAE,KAAK;QAAA,oBACC,IAAI,CAAC7L,WAAW;QAAA,cACtB,IAAI,CAACA,WAAW,IAAI,kBAAkB;QAClD8L,8BAA8B,EAAE,IAAI;QACpC9T,QAAQ,EAAE,IAAI,CAACwS,SAAS,CAACxS,QAAQ;;QAEjC1K,uBAAuB,EAAE,IAAI,CAACH,SAAS;QACvCqR,MAAM,EAAE,IAAI,CAACA,MAAM;QACnB1I,OAAO,EAAE,IAAI,CAACA,OAAO;QACrBgV,SAAS,EAAE,IAAI,CAACA,SAAS;QACzBE,SAAS,EAAE,IAAI,CAACA,SAAS;QACzBzf,OAAO,EAAE,IAAI,CAAC2f;MAAI,EAErB;WACI;MACLS,OAAO,GACL/iB,KAAA,CAAAO,aAAA;QACEM,IAAI,EAAC,SAAS;QACdF,GAAG,EAAE,IAAI,CAAC6gB,iBAAiB;QAC3B5gB,SAAS,EAAC,kBAAkB;QAC5BkiB,eAAe,EAAE,IAAI,CAACA,eAAe;QACrC1T,QAAQ,EAAE,IAAI,CAACwS,SAAS,CAACxS,QAAQ;QACjC6T,UAAU,EAAE,KAAK;QAAA,oBACC,IAAI,CAAC7L,WAAW;QAAA,cACtB,IAAI,CAACA,WAAW,IAAI,kBAAkB;QAClD8L,8BAA8B,EAAE,IAAI;;QAEpCziB,GAAG,EAAE,IAAI,CAACihB,SAAS,CAACtN,YAAY;QAChCwB,MAAM,EAAE,IAAI,CAACA,MAAM;QACnBoM,OAAO,EAAE,IAAI,CAACA,OAAO;QACrBC,OAAO,EAAE,IAAI,CAACA,OAAO;QACrBH,kBAAkB,EAAE,IAAI,CAACA,kBAAkB;QAC3CC,gBAAgB,EAAE,IAAI,CAACA,gBAAgB;QACvC7U,OAAO,EAAE,IAAI,CAACA,OAAO;QACrBgV,SAAS,EAAE,IAAI,CAACA,SAAS;QACzBC,OAAO,EAAE,IAAI,CAACA,OAAO;QACrBC,SAAS,EAAE,IAAI,CAACA,SAAS;QACzBzf,OAAO,EAAE,IAAI,CAAC2f;MAAI,GAClB,IAAI,CAACZ,SAAS,CAACtN,YAAY,CAC9B;;IAEH,MAAM+O,OAAO,GAAG,IAAI,CAACvB,SAAS,CAACwB,oBAAoB,GAAIpjB,KAAA,CAAAO,aAAA,CAAC8iB,yBAAyB;MAACF,OAAO,EAAE,IAAI,CAACvB,SAAS,CAAC0B,gBAAgB;MAAEC,yBAAyB,EAAE,IAAI,CAAC3B,SAAS,CAAC4B;IAAwB,EAA8B,GAAI,IAAI;IACpO,OACExjB,KAAA,CAAAO,aAAA;MAAMK,SAAS,EAAE,IAAI,CAACA;IAAS,GAC7BZ,KAAA,CAAAO,aAAA;MAAMK,SAAS,EAAC;IAA4B,GAC1CZ,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC,4DAA4D;MACzE+B,OAAO,EAAE,IAAI,CAAC2f;IAAI,EAEd,EAENtiB,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC,4DAA4D;MACzE+B,OAAO,EAAE,IAAI,CAAC2f;IAAI,EAEd,EAENtiB,KAAA,CAAAO,aAAA;MAAMK,SAAS,EAAC;IAA0B,GACvCmiB,OAAO,EACR/iB,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC,6BAA6B;MAC1C+B,OAAO,EAAE,IAAI,CAAC2f;IAAI,EACd,EACLa,OAAO,CACH,CACF,EACN,IAAI,CAACtB,SAAS,GAAG7hB,KAAA,CAAAO,aAAA;MAAMK,SAAS,EAAC;IAA0B,GAAE,IAAI,CAACihB,SAAS,CAAQ,GAAG,EAAE,CACpF;;AAGZ;AAEDjgB,mBAAmB,CAACC,QAAQ,CAACyB,eAAe,CAC1CmgB,0BAA0B,EACzB7jB,KAAK,IAAI;EACR,OAAOI,KAAK,CAACO,aAAa,CAAC8gB,qBAAqB,EAAEzhB,KAAK,CAAC;AAC1D,CAAC,CACF;AC9MK,MAAO8jB,sBAAuB,SAAQ1jB,KAAK,CAAC4M,SAA4C;EAC5FrK,MAAMA,CAAA;IACJ,OACEvC,KAAA,CAAAO,aAAA,cACEP,KAAA,CAAAO,aAAA,CAAC0C,OAAO;MAAA,eAAa,MAAM;MAACC,QAAQ,EAAC,kBAAkB;MAACE,IAAI,EAAC,IAAI;MAACxC,SAAS,EAAE,IAAI,CAAChB,KAAK,CAACyH,UAAU,CAACX,KAAK,CAACY;IAAI,EAAY,EACzHtH,KAAA,CAAAO,aAAA;MAAMK,SAAS,EAAE,IAAI,CAAChB,KAAK,CAACyH,UAAU,CAACX,KAAK,CAACpG,IAAI,IAAI+B;IAAS,GAC5DrC,KAAA,CAAAO,aAAA,CAACgH,qBAAqB;MAACC,MAAM,EAAE,IAAI,CAAC5H,KAAK,CAAC8G,KAAK,CAACe;IAAO,EAAI,CACtD,CACH;;AAGX;AAED7F,mBAAmB,CAACC,QAAQ,CAACyB,eAAe,CAAC,oBAAoB,EAAG1D,KAAK,IAAI;EAC3E,OAAOI,KAAK,CAACO,aAAa,CAACmjB,sBAAsB,EAAE9jB,KAAK,CAAC;AAC3D,CAAC,CAAC;ACrBI,MAAO+jB,kBAAmB,SAAQC,sBAAsB;EAC5D9jB,YAAYF,KAAU;IACpB,KAAK,CAACA,KAAK,CAAC;;EAGJqU,WAAWA,CAAA;IACnB,MAAM4P,CAAC,GAAG,IAAI,CAACjd,QAAiC;IAChDkd,iBAAiB,CAACD,CAAC,CAAC;IACpB,MAAMhW,IAAI,GAAIgW,CAAC,CAACE,eAAe,GAAI,IAAI,CAACC,eAAe,CAACH,CAAC,CAACE,eAAe,CAAC,GAAG,EAAE;IAC/E,MAAME,iBAAiB,GAAG,IAAI,CAACrd,QAAQ,CAACqd,iBAAiB;IAEzD,OAAQjkB,KAAA,CAAAO,aAAA;MACNuB,EAAE,EAAE,IAAI,CAAC8E,QAAQ,CAACsd,OAAO;MACzBtjB,SAAS,EAAEijB,CAAC,CAACM,eAAe,EAAE;MAC9B/U,QAAQ,EAAE,IAAI,CAACxI,QAAQ,CAAC4O,eAAe,GAAGnT,SAAS,GAAG,CAAC;;;MAGvDoL,QAAQ,EAAE,IAAI,CAAC7G,QAAQ,CAAC4O,eAAe;MACvC4O,QAAQ,EAAE,IAAI,CAACxd,QAAQ,CAACyd,UAAU;MAClC1H,QAAQ,EAAE,IAAI,CAAC2H,kBAAkB;MACjCtC,OAAO,EAAE,IAAI,CAACsC,kBAAkB;MAChCnC,OAAO,EAAE,IAAI,CAACoC,UAAU;MACxB1jB,IAAI,EAAEojB,iBAAiB,CAACO,gBAAgB;MAAA,iBACzBP,iBAAiB,CAACQ,oBAAoB;MAAA,gBACvCR,iBAAiB,CAACS,mBAAmB;MAAA,qBAChCT,iBAAiB,CAACU,wBAAwB;MAAA,iBAC9CV,iBAAiB,CAACW,oBAAoB;MAAA,cACzCX,iBAAiB,CAACY,iBAAiB;MAAA,mBAC9BZ,iBAAiB,CAACa,sBAAsB;MAAA,iBAC1Cb,iBAAiB,CAACc;IAAoB,GAErD/kB,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAE,IAAI,CAACgG,QAAQ,CAACS,UAAU,CAAC2d;IAAY,GAClDnX,IAAI,CACD,CACF;;EAGEoX,mBAAmBA,CAAA;IAC3B,OAAO,IAAI;;AAEd;AAEDpe,oBAAoB,CAAChF,QAAQ,CAACsT,gBAAgB,CAAC,mBAAmB,EAAGvV,KAAK,IAAI;EAC5E,OAAOI,KAAK,CAACO,aAAa,CAACojB,kBAAkB,EAAE/jB,KAAK,CAAC;AACvD,CAAC,CAAC;AAEFslB,eAAe,CAACrjB,QAAQ,CAACsjB,gBAAgB,CAAC,UAAU,EAAE,eAAe,EAAE,mBAAmB,CAAC;AChCrF,MAAOC,mBAAoB,SAAQvd,mBAGxC;EAIC/H,YAAYF,KAAgC;IAC1C,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACqgB,YAAY,GAAGjgB,KAAK,CAACC,SAAS,EAAE;;EAE7B6H,WAAWA,CAAClI,KAAU;IAC9B,IAAI,IAAI,CAACD,KAAK,EAAE;MACd,IAAI,CAACA,KAAK,CAAC8I,OAAO,EAAE;;IAEtB,IAAI,CAAC9I,KAAK,GAAG,IAAI0lB,sBAAsB,CACrCzlB,KAAK,CAAC0lB,eAAe,EACrB1lB,KAAK,CAAC2lB,YAAY,CACnB;;EAEOld,oBAAoBA,CAAA;IAC5B,OAAO,CAAC,iBAAiB,EAAE,cAAc,CAAC;;EAElCxI,eAAeA,CAAA;IACvB,OAAO,IAAI,CAACF,KAAK;;EAEnB2B,iBAAiBA,CAAA;IACf,KAAK,CAACA,iBAAiB,EAAE;IACzB,IAAI,IAAI,CAAC1B,KAAK,CAAC2lB,YAAY,KAAK,QAAQ,EAAE;MACxC,MAAMC,EAAE,GAAG,IAAI,CAACvF,YAAY,CAAC7e,OAAyB;MACtD,IAAI,CAACzB,KAAK,CAACwK,UAAU,CAACqb,EAAE,CAAC;;;EAG7BjkB,oBAAoBA,CAAA;IAClB,KAAK,CAACA,oBAAoB,EAAE;IAC5B,IAAI,CAAC5B,KAAK,CAAC8lB,gCAAgC,EAAE;IAC7C,IAAI,CAAC9lB,KAAK,CAAC+lB,sBAAsB,CAACrjB,SAAS,CAAC;;EAE9CnC,aAAaA,CAAA;IACX,IAAIU,SAAS,GAAG,yDAAyD;IACzE,IAAI,IAAI,CAACjB,KAAK,CAACgmB,aAAa,EAC1B/kB,SAAS,IAAI,sCAAsC;IACrD,OACEZ,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC,oBAAoB;MAACD,GAAG,EAAE,IAAI,CAACsf,YAAY;MAAEnf,KAAK,EAAE;QAAEsf,OAAO,EAAE,IAAI,CAACzgB,KAAK,CAACkG,OAAO,GAAG,MAAM,GAAG;MAAM;IAAE,GAClH7F,KAAA,CAAAO,aAAA,cACGiC,eAAe,CAACxC,KAAA,CAAAO,aAAA;MACfM,IAAI,EAAC,QAAQ;MACbD,SAAS,EAAEA,SAAS;MACpB+B,OAAO,EAAEA,CAAA,KAAM,IAAI,CAAChD,KAAK,CAACimB,kBAAkB,EAAE;MAC9C7iB,KAAK,EAAE,IAAI,CAACpD,KAAK,CAACkmB;IAAmB,GAErC7lB,KAAA,CAAAO,aAAA,CAAC0C,OAAO;MACNrC,SAAS,EAAC,iCAAiC;MAC3CsC,QAAQ,EAAE,IAAI,CAACvD,KAAK,CAAC2H,IAAI;MACzBlE,IAAI,EAAE,MAAM;MACZL,KAAK,EAAE,IAAI,CAACpD,KAAK,CAACkmB;IAAmB,EAC5B,CACP,CAAC,EACP7lB,KAAA,CAAAO,aAAA,CAACqS,KAAK;MACJjT,KAAK,EAAE,IAAI,CAACA,KAAK,CAACkT;IAAU,EAErB,CACL,EACN7S,KAAA,CAAAO,aAAA,cACG,IAAI,CAACZ,KAAK,CAACmmB,YAAY,CAACzlB,GAAG,CAAEC,IAAI,IAChCN,KAAA,CAAAO,aAAA,CAACwlB,uBAAuB;MACtBtlB,GAAG,EAAEH,IAAI,CAACwB,EAAE;MACZxB,IAAI,EAAEA;IAAI,EAEb,CAAC,CACE,CACF;;AAGX;AACK,MAAOylB,uBAAwB,SAAQle,mBAA6B;EAC9DhI,eAAeA,CAAA;IACvB,OAAO,IAAI,CAACD,KAAK,CAACU,IAAY;;EAEhCJ,aAAaA,CAAA;IACX,MAAMI,IAAI,GAAG,IAAI,CAACV,KAAK,CAACU,IAAI;IAC5B,IAAIM,SAAS,GAAW,iCAAiC;IACzD,IAAIolB,MAAM,CAAC1lB,IAAI,CAACmC,MAAM,CAAC,EAAE;MACvB7B,SAAS,IAAI,oCAAoC;;IAEnD,IAAIolB,MAAM,CAAC1lB,IAAI,CAACmN,QAAQ,CAAC,EAAE;MACzB7M,SAAS,IAAI,oCAAoC;;IAEnD,OACEZ,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAyB,GACrC4B,eAAe,CAACxC,KAAA,CAAAO,aAAA;MACfM,IAAI,EAAC,QAAQ;MACbD,SAAS,EAAEA,SAAS;MACpB+B,OAAO,EAAGqI,CAAC,IAAI;QACb1K,IAAI,CAACsC,MAAM,CAACtC,IAAI,CAAC;QACjB0K,CAAC,CAAC9B,eAAe,EAAE;;IACpB,GAEDlJ,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC,8BAA8B;MAACmC,KAAK,EAAEzC,IAAI,CAACyC;IAAK,GAAE/C,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAsC,EAAO,CAAM,EACnIZ,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAiC,GAC9CZ,KAAA,CAAAO,aAAA;MAAMK,SAAS,EAAC;IAA+B,GAAEN,IAAI,CAACyC,KAAK,CAAQ,EACnE/C,KAAA,CAAAO,aAAA;MAAMK,SAAS,EAAC;IAA8B,GAACZ,KAAA,CAAAO,aAAA;MAAMK,SAAS,EAAC;IAAsC,EAAQ,CAAO,CAChH,CACF,CAAC,CACH;;AAGX;ACrHK,MAAOqlB,kBAAmB,SAAQjmB,KAAK,CAAC4M,SAAwC;EACpF9M,YAAYF,KAA+B;IACzC,KAAK,CAACA,KAAK,CAAC;;EAEd2C,MAAMA,CAAA;IACJ,OACEvC,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAyB,GACtCZ,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAE,iEAAiE,GAAG,IAAI,CAAChB,KAAK,CAACiL;IAAI,EAAS,EAC5G7K,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAA+B,GAC5CZ,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAgC,GAAE,IAAI,CAAChB,KAAK,CAACsmB,oBAAoB,CAAO,EACvFlmB,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAsC,GAAE,IAAI,CAAChB,KAAK,CAACumB,0BAA0B,CAAO,CAC/F,CACF;;AAGX;AAEDvkB,mBAAmB,CAACC,QAAQ,CAACyB,eAAe,CAAC,yBAAyB,EACnE1D,KAA+B,IAAI;EAClC,OAAOI,KAAK,CAACO,aAAa,CAAC0lB,kBAAkB,EAAErmB,KAAK,CAAC;AACvD,CAAC,CACF;ACdK,MAAOwmB,oBAAqB,SAAQ1mB,iBAAkD;EAA5FI,YAAA;;IAQU,KAAAumB,UAAU,GAAG,MAAK;MACxB,IAAI,CAACC,mBAAmB,EAAE;IAC5B,CAAC;IAEO,KAAAC,WAAW,GAAG,MAAK;MACzB,IAAI,CAACC,oBAAoB,EAAE;IAC7B,CAAC;IAEO,KAAAC,iBAAiB,GAAG,MAAK;MAC/B,IAAI,CAAChjB,OAAO,CAACoU,WAAW,CAAC7B,GAAG,CAAC,IAAI,CAACqQ,UAAU,CAAC;MAC7C,IAAI,CAAC5iB,OAAO,CAACijB,WAAW,CAAC1Q,GAAG,CAAC,IAAI,CAACuQ,WAAW,CAAC;IAChD,CAAC;IAEO,KAAAI,mBAAmB,GAAG,MAAK;MACjC,IAAI,CAACljB,OAAO,CAACoU,WAAW,CAACnE,MAAM,CAAC,IAAI,CAAC2S,UAAU,CAAC;MAChD,IAAI,CAAC5iB,OAAO,CAACijB,WAAW,CAAChT,MAAM,CAAC,IAAI,CAAC6S,WAAW,CAAC;IACnD,CAAC;;EAvBD,IAAY5mB,KAAKA,CAAA;IACf,OAAO,IAAI,CAACC,KAAK,CAACqG,IAAI;;EAExB,IAAcxC,OAAOA,CAAA;IACnB,OAAO,IAAI,CAAC9D,KAAK,CAAC8D,OAAO;;EAqB3BnC,iBAAiBA,CAAA;IACf,KAAK,CAACA,iBAAiB,EAAE;IACzB,IAAI,CAACmlB,iBAAiB,EAAE;;EAG1BllB,oBAAoBA,CAAA;IAClB,KAAK,CAACA,oBAAoB,EAAE;IAC5B,IAAI,CAAColB,mBAAmB,EAAE;IAC1B,KAAK,CAACplB,oBAAoB,EAAE;;EAGpB2O,gBAAgBA,CAAA;IACxB,OAAO,CAAC,IAAI,CAACvQ,KAAK,EAAE,IAAI,CAACA,KAAK,CAACqG,MAAM,EAAE,IAAI,CAACrG,KAAK,CAAC2lB,eAAe,CAAC;;EAG1DsB,gBAAgBA,CAAA;IACxB,MAAMC,aAAa,GAAG,EAAE;IAExB,IAAI,IAAI,CAACpjB,OAAO,CAAC8hB,YAAY,KAAK,QAAQ,EAAE;MAC1C,MAAM/E,KAAK,GAAG,IAAI,CAAC7gB,KAAK,CAAC6gB,KAAK;MAC9BA,KAAK,CAACsG,OAAO,CAAEtV,IAAI,IAAI;QACrBqV,aAAa,CAAC5M,IAAI,CAAC,IAAI,CAAC8M,kBAAkB,CAACvV,IAAI,EAAEA,IAAI,IAAI,IAAI,CAAC7R,KAAK,CAACqnB,OAAO,CAAC,CAAC;MAC/E,CAAC,CAAC;WACG;MACL,MAAMC,YAAY,GAAG,IAAI,CAACtnB,KAAK,CAAC2lB,eAAe,CAAC2B,YAAY;MAC5D,IAAI,CAAC,CAACA,YAAY,EAAE;QAClBJ,aAAa,CAAC5M,IAAI,CAAC,IAAI,CAAC8M,kBAAkB,CAACE,YAAY,EAAE,IAAI,CAACtnB,KAAK,CAACqnB,OAAO,KAAKC,YAAY,CAAC,CAAC;;;IAIlG,OAAOJ,aAAa;;EAEZE,kBAAkBA,CAACvV,IAAe,EAAE0V,WAAqB;IACjE,OACElnB,KAAA,CAAAO,aAAA;MACEK,SAAS,EAAE,UAAU;MAAA,4BACK4Q,IAAI,CAAC3G,IAAI;MAAA,sCACCqc,WAAW,GAAG,cAAc,GAAG1V,IAAI,CAAC3G,IAAI;MAC5EpK,GAAG,EAAE+Q,IAAI,CAAC1P;IAAE,GAEX,IAAI,CAACqlB,UAAU,CAAC3V,IAAI,EAAE0V,WAAW,CAAC,CAC/B;;EAGFE,aAAaA,CAACxmB,SAAiB,EAAEH,GAAA,GAAc,EAAE;IACvD,OACET,KAAA,CAAAO,aAAA,CAACP,KAAK,CAACsM,QAAQ;MAAC7L,GAAG,EAAEA;IAAG,GACtBT,KAAA,CAAAO,aAAA;MACEK,SAAS,EAAEA,SAAS;MAAA,sCACgB;IAAc,GAEjD,CAAC,CAAC,IAAI,CAACjB,KAAK,CAACqnB,OAAO,GAAG,IAAI,CAACG,UAAU,CAAC,IAAI,CAACxnB,KAAK,CAACqnB,OAAO,EAAE,IAAI,CAAC,GAAG,IAAI,CACpE,CACS;;EAEXG,UAAUA,CAACE,KAAgB,EAAEzV,OAAgB;IACrD,OAAOhQ,mBAAmB,CAACC,QAAQ,CAACtB,aAAa,CAAC,UAAU,EAAE;MAAEyF,MAAM,EAAE,IAAI,CAACvC,OAAO,CAACuC,MAAM;MAAEwL,IAAI,EAAE6V,KAAK;MAAE5jB,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEmO;IAAO,CAAE,CAAC;;EAE7I1R,aAAaA,CAAA;IACX,MAAMonB,oBAAoB,GAAG,mBAAmB,GAAG,IAAI,CAAC3nB,KAAK,CAAC+C,UAAU,EAAE;IAE1E,OACE1C,KAAA,CAAAO,aAAA,CAACP,KAAK,CAACsM,QAAQ,QACbtM,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAiB,GAC7B,IAAI,CAACjB,KAAK,CAAC4nB,gBAAgB,GAAG3lB,mBAAmB,CAACC,QAAQ,CAACtB,aAAa,CAAC,aAAa,EAAE;MAAEZ,KAAK,EAAE,IAAI,CAAC8D;IAAO,CAAE,CAAC,GAAG,IAAI,CACpH,EACNzD,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAE0mB,oBAAoB;MAAE3kB,OAAO,EAAEA,CAAA,KAAM,IAAI,CAAChD,KAAK,CAAC6nB,aAAa;IAAE,GAC7ExnB,KAAA,CAAAO,aAAA,CAACyd,MAAM,QACLhe,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAA0B,GACtC,IAAI,CAACjB,KAAK,CAACuS,eAAe,GAAG,IAAI,CAAC6B,iBAAiB,EAAE,GAAG,IAAI,CAAC0T,gBAAgB,EAAE,CAC5E,CACC,CACL,CACS;;EAIrBhc,YAAYA,CAACic,SAAkB;IAC7B,IAAI,CAACA,SAAS,EAAE,OAAO,IAAI;IAE3B,MAAM1hB,MAAM,GAAgB,IAAI,CAACvC,OAAO,CAACuC,MAAM;IAC/C,OAAQhG,KAAA,CAAAO,aAAA,CAACP,KAAK,CAACsM,QAAQ,QACrBtM,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAqB,GAClCZ,KAAA,CAAAO,aAAA,CAAConB,YAAY;MAAC3hB,MAAM,EAAEA;IAAM,EAAiB,CACzC,CACS;;EAEnB+N,iBAAiBA,CAAA;IACf,MAAM6T,YAAY,GAAG,IAAI,CAACnc,YAAY,CAAC,IAAI,CAAChI,OAAO,CAACokB,oBAAoB,IAAI,IAAI,CAACpkB,OAAO,CAACqkB,uBAAuB,CAAC;IAEjH,OAAQ9nB,KAAA,CAAAO,aAAA,CAACP,KAAK,CAACsM,QAAQ,QACpBsb,YAAY,EACb5nB,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC,qCAAqC;MAAA,sCAAqC;IAAc,GACpG,IAAI,CAACmnB,wBAAwB,EAAE,EAC/B,IAAI,CAACX,aAAa,CAAC,+BAA+B,CAAC,CAChD,CACS;;EAEnBW,wBAAwBA,CAAA;IACtB,OAAO/nB,KAAA,CAAAO,aAAA,CAAC0lB,kBAAkB;MAACpb,IAAI,EAAE,UAAU;MAAEqb,oBAAoB,EAAE,IAAI,CAACvmB,KAAK,CAACumB,oBAAoB;MAAEC,0BAA0B,EAAE,IAAI,CAACxmB,KAAK,CAACwmB;IAA0B,EAAI;;EAE3KsB,gBAAgBA,CAAA;IACd,MAAMzhB,MAAM,GAAgB,IAAI,CAACvC,OAAO,CAACuC,MAAM;IAC/C,MAAM4hB,YAAY,GAAG,IAAI,CAACnc,YAAY,CAAC,IAAI,CAAChI,OAAO,CAACokB,oBAAoB,CAAC;IACzE,MAAM/mB,KAAK,GAAAknB,MAAA,CAAA5jB,MAAA,KAAa,IAAI,CAACzE,KAAK,CAACsoB,mBAAmB,CAAE;IACxDnnB,KAAK,CAAConB,QAAQ,GAAGliB,MAAM,CAACmiB,aAAa;IAErC,MAAMC,QAAQ,GAAG,IAAI,CAACC,cAAc,EAAE;IAEtC,OAAQroB,KAAA,CAAAO,aAAA,CAACP,KAAK,CAACsM,QAAQ,QACrBtM,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAE,IAAI,CAACjB,KAAK,CAAC2oB,WAAW;MAAExnB,KAAK,EAAEA;IAAK,GACjD8mB,YAAY,EAEZ,IAAI,CAAChB,gBAAgB,EAAE,CAMpB,EACLwB,QAAQ,CACM;;EAGnBC,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAAC1oB,KAAK,CAAC4oB,gBAAgB,EAAE,OAAO,IAAI;IAE7C,MAAMC,aAAa,GAAG,IAAI,CAAC/kB,OAAO,CAACglB,iBAAiB,GAClDzoB,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAkC,GAACZ,KAAA,CAAAO,aAAA,CAAC6kB,mBAAmB;MACpEE,eAAe,EAAE,IAAI,CAAC3lB,KAAK,CAAC2lB,eAAe;MAAEC,YAAY,EAAE,IAAI,CAAC5lB,KAAK,CAAC8D,OAAO,CAAC8hB;IAAY,EACrE,IACrB,IAAI;IAER,MAAMmD,cAAc,GAAG,IAAI,CAAC/oB,KAAK,CAACgpB,kBAAkB,GAClD3oB,KAAA,CAAAO,aAAA,CAACyE,eAAe;MAACrF,KAAK,EAAE,IAAI,CAACA,KAAK,CAAC+oB,cAAc;MAAE1b,WAAW,EAAE;IAAK,KACnE,IAAI;IAER,OAAOhN,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAyB,GAC5C4nB,aAAa,EACbE,cAAc,CACX;;AAET;AAED9mB,mBAAmB,CAACC,QAAQ,CAACyB,eAAe,CAAC,kBAAkB,EAAG1D,KAAK,IAAI;EACzE,OAAOI,KAAK,CAACO,aAAa,CACxB6lB,oBAAoB,EACpBxmB,KAAmC,CACpC;AACH,CAAC,CAAC;ACjLI,MAAOgpB,4BAA6B,SAAQlpB,iBAAiD;EACvFG,eAAeA,CAAA;IACvB,OAAO,IAAI,CAACF,KAAK;;EAEnB,IAAYA,KAAKA,CAAA;IACf,OAAO,IAAI,CAACC,KAAK,CAACqG,IAAI;;EAExB/F,aAAaA,CAAA;IACX,OAAOF,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC,kCAAkC;MAACE,KAAK,EAAE;QAAEsf,OAAO,EAAE,IAAI,CAACzgB,KAAK,CAACkpB,SAAS,GAAG,OAAO,GAAG;MAAM;IAAE,GAClH7oB,KAAA,CAAAO,aAAA,CAACuoB,IAAI;MAACnpB,KAAK,EAAE,IAAI,CAACA,KAAK,CAACopB;IAAS,EAAI,CACjC;;AAET;AAEK,MAAOC,8BAA+B,SAAQtpB,iBAGnD;EACCI,YAAYF,KAAK;IACf,KAAK,CAACA,KAAK,CAAC;;EAEJC,eAAeA,CAAA;IACvB,OAAO,IAAI,CAACF,KAAK;;EAEnB,IAAYA,KAAKA,CAAA;IACf,OAAO,IAAI,CAACC,KAAK,CAACqG,IAAI;;EAExB/F,aAAaA,CAAA;IACX,MAAM+oB,UAAU,GAAIzD,EAAuB,IAAI;MAC7C,IAAI,CAAC7lB,KAAK,CAACupB,WAAW,GAAG1D,EAAE;IAC7B,CAAC;IACD,MAAM2D,MAAM,GAAGnpB,KAAA,CAAAO,aAAA,CAACqoB,4BAA4B;MAAC3iB,IAAI,EAAE,IAAI,CAACtG;IAAK,EAAI;IACjE,OACEK,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAA0B,GACvCZ,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAA8B,GAC3CZ,KAAA,CAAAO,aAAA;MACEI,GAAG,EAAEyoB,KAAK,IAAKH,UAAU,CAACG,KAAK,CAAE;MACjCxoB,SAAS,EAAC,mCAAmC;MAC7CyI,KAAK,EAAE,IAAI,CAAC1J,KAAK,CAACkO,IAAI;MACtB8O,QAAQ,EAAG3R,CAAmC,IAC3C,IAAI,CAACrL,KAAK,CAACkO,IAAI,GAAG7C,CAAC,CAACxE,MAAM,CAAC6C,KAAM;MAEpC6Y,SAAS,EAAGlX,CAAC,IAAK,IAAI,CAACrL,KAAK,CAAC0pB,QAAQ,CAACre,CAAC,EAAEA,CAAC,CAAC;MAC3CyC,QAAQ,EAAE,IAAI,CAAC9N,KAAK,CAAC2pB,QAAQ;MAAA,cACjB,IAAI,CAAC3pB,KAAK,CAAC4pB;IAAS,EACtB,EACXJ,MAAM,CACH,CACF;;AAGX;AAEDvnB,mBAAmB,CAACC,QAAQ,CAACyB,eAAe,CAC1C,8BAA8B,EAC7B1D,KAA2C,IAAI;EAC9C,OAAOI,KAAK,CAACO,aAAa,CAACyoB,8BAA8B,EAAEppB,KAAK,CAAC;AACnE,CAAC,CACF;AC9DK,MAAO4pB,yBAA0B,SAAQ9pB,iBAG9C;EAECI,YAAYF,KAAsC;IAChD,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAAC6pB,aAAa,GAAGzpB,KAAK,CAACC,SAAS,EAAE;;EAE9BJ,eAAeA,CAAA;IACvB,OAAO,IAAI,CAACF,KAAK;;EAEnB,IAAYA,KAAKA,CAAA;IACf,OAAO,IAAI,CAACC,KAAK,CAACqG,IAAI;;EAExB3E,iBAAiBA,CAAA;IACf,IAAI,CAAC3B,KAAK,CAAC+pB,IAAI,CAACC,GAAG,CAACrH,IAAI,CAAC,IAAI,CAACmH,aAAa,CAACroB,OAAsB,CAAC,CAAC;;EAEtElB,aAAaA,CAAA;IACX,MAAMipB,MAAM,GAAGnpB,KAAA,CAAAO,aAAA,CAACqoB,4BAA4B;MAAC3iB,IAAI,EAAE,IAAI,CAACtG;IAAK,EAAI;IACjE,OACEK,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAA0B,GACvCZ,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAA8B,GAC3CZ,KAAA,CAAAO,aAAA;MACEK,SAAS,EAAC,iCAAiC;MAC3CD,GAAG,EAAE,IAAI,CAAC8oB;IAAa,EAClB,EACNN,MAAM,CACH,CACF;;AAGX;AAEDvnB,mBAAmB,CAACC,QAAQ,CAACyB,eAAe,CAC1C,yBAAyB,EACxB1D,KAAsC,IAAI;EACzC,OAAOI,KAAK,CAACO,aAAa,CAACipB,yBAAyB,EAAE5pB,KAAK,CAAC;AAC9D,CAAC,CACF;AC1CK,MAAOgqB,0BAA2B,SAAQlqB,iBAA2B;EACzE,IAAYC,KAAKA,CAAA;IACf,OAAO,IAAI,CAACC,KAAK,CAACmW,MAAM;;EAEhBlW,eAAeA,CAAA;IACvB,OAAO,IAAI,CAACF,KAAK;;EAGnBO,aAAaA,CAAA;IACX,MAAMkf,eAAe,GAAG,uCAAuC,IAAK,IAAI,CAACzf,KAAK,CAACkqB,OAAO,KAAKxnB,SAAS,IAAI,CAAC,IAAI,CAAC1C,KAAK,CAACkqB,OAAO,GAAI,0CAA0C,GAAG,EAAE,CAAC;IAC/K,OAAOrnB,eAAe,CAACxC,KAAA,CAAAO,aAAA;MACrBM,IAAI,EAAC,QAAQ;MACb8B,OAAO,EAAGqI,CAAC,IAAI;QACbA,CAAC,CAAC9B,eAAe,EAAE;QACnB,IAAI,CAACvJ,KAAK,CAACiD,MAAM,EAAE;OACpB;MACDhC,SAAS,EAAEwe,eAAe;MAC1Brc,KAAK,EAAE,IAAI,CAACpD,KAAK,CAACoD;IAAK,GAEvB/C,KAAA,CAAAO,aAAA;MAAMK,SAAS,EAAC;IAAe,GAC5B,IAAI,CAACjB,KAAK,CAACoD,KAAK,CACZ,CACH,CAAC;;AAEV;AACDnB,mBAAmB,CAACC,QAAQ,CAACyB,eAAe,CAAC,0BAA0B,EAAG1D,KAAK,IAAI;EACjF,OAAOI,KAAK,CAACO,aAAa,CAACqpB,0BAA0B,EAAEhqB,KAAK,CAAC;AAC/D,CAAC,CAAC;AAEI,MAAOkqB,iBAAkB,SAAQpqB,iBAA2B;EAChE,IAAYC,KAAKA,CAAA;IACf,OAAO,IAAI,CAACC,KAAK,CAACqG,IAAI;;EAEdpG,eAAeA,CAAA;IACvB,OAAO,IAAI,CAACF,KAAK;;EAGnBO,aAAaA,CAAA;IACC,IAAI,CAACP,KAAA;IACjB,IAAIoqB,SAAS,GAAG,wCAAwC;IACxD,IAAIpf,OAAO,GAAG,IAAI,CAACqf,iBAAiB,EAAE;IACtC,OAAOhqB,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAEmpB;IAAS,GAAGpf,OAAO,CAAO;;EAE3Cqf,iBAAiBA,CAAA;IACvB,MAAMC,iBAAiB,GAAG,+DAA+D,IAAI,IAAI,CAACtqB,KAAK,CAACuqB,QAAQ,GAAG,EAAE,GAAG,sBAAsB,CAAC;IAC/I,MAAMC,QAAQ,GAAG,CAAC,IAAI,CAACxqB,KAAK,CAAC2pB,QAAQ,GAAGtpB,KAAA,CAAAO,aAAA,CAACqpB,0BAA0B;MAAC7T,MAAM,EAAE,IAAI,CAACpW,KAAK,CAACyqB;IAAY,EAAI,GAAG/nB,SAAS;IACnH,OACErC,KAAA,CAAAO,aAAA,CAAC+L,QAAQ,QACPtM,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAEqpB;IAAiB,GAC9B,IAAI,CAACtqB,KAAK,CAACuqB,QAAQ,GACjBlqB,KAAA,CAAAO,aAAA,CAAAP,KAAA,CAAAsM,QAAA,QACCtM,KAAA,CAAAO,aAAA,CAACqH,MAAM;MAACjI,KAAK,EAAE,IAAI,CAACA,KAAK,CAAC0qB;IAAW,EAAW,EAC/CF,QAAQ,CACR,GACAnqB,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAA8B,GAC9CZ,KAAA,CAAAO,aAAA,CAAC0lB,kBAAkB;MAACpb,IAAI,EAAE,OAAO;MAAEqb,oBAAoB,EAAE,IAAI,CAACvmB,KAAK,CAACumB,oBAAoB;MAAEC,0BAA0B,EAAE,IAAI,CAACxmB,KAAK,CAACwmB;IAA0B,EAAI,EAC9JgE,QAAQ,CACJ,CAEL,CACI;;AAGjB;AACDvoB,mBAAmB,CAACC,QAAQ,CAACyB,eAAe,CAAC,eAAe,EAAG1D,KAAK,IAAI;EACtE,OAAOI,KAAK,CAACO,aAAa,CAACupB,iBAAiB,EAAElqB,KAAK,CAAC;AACtD,CAAC,CAAC;ACpEI,MAAO0qB,eAAgB,SAAQ5qB,iBAA2B;EAC9D,IAAYC,KAAKA,CAAA;IACf,OAAO,IAAI,CAACC,KAAK,CAACD,KAAK;;EAGfE,eAAeA,CAAA;IACvB,OAAO,IAAI,CAACF,KAAK;;EAGnBO,aAAaA,CAAA;IACX,MAAMqqB,kBAAkB,GAAG,IAAI,CAAC5qB,KAAK,CAAC+C,UAAU,EAAE;IAClD,IAAI,CAAC,IAAI,CAAC/C,KAAK,CAACqG,MAAM,EAAE;MACtB,OAAOhG,KAAA,CAAAO,aAAA;QAAKK,SAAS,EAAE2pB;MAAkB,EAAQ;;IAEnD,IAAI,IAAI,CAAC5qB,KAAK,CAAC6qB,QAAQ,EAAE;MACvB,OACExqB,KAAA,CAAAO,aAAA;QACEK,SAAS,EAAE2pB,kBAAkB;QAC7BrI,SAAS,EAAElX,CAAC,IAAI,IAAI,CAACrL,KAAK,CAAC8qB,SAAS,CAACzf,CAAC,EAAEA,CAAC,CAAC;QAC1C0f,YAAY,EAAE,IAAI,CAAC/qB,KAAK,CAACgrB,MAAM,KAAK,SAAS,GAAG,IAAI,GAAG,IAAI,CAAChrB,KAAK,CAACirB,YAAY;QAC9E1f,YAAY,EAAE,IAAI,CAACvL,KAAK,CAACgrB,MAAM,KAAK,SAAS,GAAG,IAAI,GAAG,IAAI,CAAChrB,KAAK,CAACkrB;MAAc,GAEhF7qB,KAAA,CAAAO,aAAA;QACEK,SAAS,EAAC,uBAAuB;QACjCkB,EAAE,EAAC,uBAAuB;QAC1BhB,KAAK,EAAE;UACL2O,KAAK,EAAE,IAAI,CAAC9P,KAAK,CAACmrB,cAAc,CAACC,UAAU,GAAG,IAAI;UAClDrb,MAAM,EAAE,IAAI,CAAC/P,KAAK,CAACmrB,cAAc,CAACE,WAAW,GAAG;QACjD;MAAA,GAEDhrB,KAAA,CAAAO,aAAA;QACEK,SAAS,EAAC,eAAe;QACzBE,KAAK,EAAE;UACL2O,KAAK,EACH,IAAI,CAAC9P,KAAK,CAACmrB,cAAc,CAACG,WAAW,GAAG,IAAI;UAC9Cvb,MAAM,EACJ,IAAI,CAAC/P,KAAK,CAACmrB,cAAc,CAACI,YAAY,GAAG,IAAI;UAC/CC,SAAS,EACP,QAAQ,GACR,IAAI,CAACxrB,KAAK,CAACmrB,cAAc,CAACM,KAAK,GAC/B;QACH;MAAA,GAEDprB,KAAA,CAAAO,aAAA;QAAKK,SAAS,EAAC;MAAuB,GACpCZ,KAAA,CAAAO,aAAA,CAACqH,MAAM;QAACjI,KAAK,EAAE,IAAI,CAACA,KAAK,CAACqG;MAAM,EAAW,CACvC,CACF,CACF,CACF;WAEH;MACL,OACEhG,KAAA,CAAAO,aAAA;QACEK,SAAS,EAAE2pB;MAAkB,GAC7BvqB,KAAA,CAAAO,aAAA;QAAKK,SAAS,EAAC;MAAuB,GACpCZ,KAAA,CAAAO,aAAA,CAACqH,MAAM;QAACjI,KAAK,EAAE,IAAI,CAACA,KAAK,CAACqG;MAAM,EAAW,CACvC,CACF;;;AAIb;ACrDK,MAAOqlB,kCAAmC,SAAQ3rB,iBAA2B;EACjF,IAAYC,KAAKA,CAAA;IACf,OAAO,IAAI,CAACC,KAAK,CAACD,KAAK,CAAC2rB,eAAe;;EAE/BzrB,eAAeA,CAAA;IACvB,OAAO,IAAI,CAACF,KAAK;;EAGnBO,aAAaA,CAAA;IACX,MAAMkf,eAAe,GAAG,iCAAiC;IACzD,OAAO5c,eAAe,CAACxC,KAAA,CAAAO,aAAA;MACrBM,IAAI,EAAC,QAAQ;MACb8B,OAAO,EAAGqI,CAAC,IAAI;QACbA,CAAC,CAAC9B,eAAe,EAAE;QACnB,IAAI,CAACvJ,KAAK,CAACiD,MAAM,EAAE;OACpB;MACDhC,SAAS,EAAEwe,eAAe;MAC1Brc,KAAK,EAAE,IAAI,CAACpD,KAAK,CAACoD;IAAK,GAEvB/C,KAAA,CAAAO,aAAA;MAAMK,SAAS,EAAC;IAAe,GAC5B,IAAI,CAACjB,KAAK,CAACoD,KAAK,CACZ,CACH,CAAC;;AAEV;AACDnB,mBAAmB,CAACC,QAAQ,CAACyB,eAAe,CAAC,mBAAmB,EAAG1D,KAAK,IAAI;EAC1E,OAAOI,KAAK,CAACO,aAAa,CAAC8qB,kCAAkC,EAAEzrB,KAAK,CAAC;AACvE,CAAC,CAAC;AAEI,MAAO2rB,yBAA0B,SAAQ7rB,iBAA2B;EACxEI,YAAYF,KAAK;IACf,KAAK,CAACA,KAAK,CAAC;;EAEd,IAAYD,KAAKA,CAAA;IACf,OAAO,IAAI,CAACC,KAAK,CAACqG,IAAI;;EAEdpG,eAAeA,CAAA;IACvB,OAAO,IAAI,CAACF,KAAK;;EAEnBsS,iBAAiBA,CAAA;IACf,OAAOjS,KAAA,CAAAO,aAAA,CAAC0lB,kBAAkB;MAACpb,IAAI,EAAE,SAAS;MAAEqb,oBAAoB,EAAE,IAAI,CAACvmB,KAAK,CAACumB,oBAAoB;MAAEC,0BAA0B,EAAE,IAAI,CAACxmB,KAAK,CAACwmB;IAA0B,EAAI;;EAE1KqF,eAAeA,CAAA;IACb,OAAQxrB,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAyB,GAC9CZ,KAAA,CAAAO,aAAA,CAAC+pB,eAAe;MAAC3qB,KAAK,EAAE,IAAI,CAACA,KAAK,CAAC8rB;IAAS,EAAoB,EAC/D,IAAI,CAAC9rB,KAAK,CAAC+rB,WAAW,GAAG1rB,KAAA,CAAAO,aAAA,CAACwY,aAAa;MAAC/S,MAAM,EAAE,IAAI,CAACrG,KAAK,CAAC8rB,SAAS,CAACzlB;IAAM,EAAI,GAAG,IAAI,CACnF;;EAGR9F,aAAaA,CAAA;IACX,MAAMyrB,mBAAmB,GAAG,gDAAgD,IAAI,IAAI,CAAChsB,KAAK,CAACisB,oBAAoB,GAAG,yCAAyC,GAAG,EAAE,CAAC;IACjK,OACE5rB,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAE+qB;IAAmB,GAChC,IAAI,CAAChsB,KAAK,CAAC8rB,SAAS,CAACzlB,MAAM,CAAC6lB,OAAO,GAAG,IAAI,CAAC5Z,iBAAiB,EAAE,GAAG,IAAI,CAACuZ,eAAe,EAAE,EACvF,IAAI,CAACM,gBAAgB,EAAE,CACpB;;EAGVA,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACnsB,KAAK,CAACisB,oBAAoB,EAAE;MACnC,OACE5rB,KAAA,CAAAO,aAAA;QAAKK,SAAS,EAAC;MAA+B,GAC5CZ,KAAA,CAAAO,aAAA,CAACyE,eAAe;QAACrF,KAAK,EAAE,IAAI,CAACA,KAAK,CAAC6gB;MAAK,EAAoB,CACxD;WAEH;MACL,OAAO,IAAI;;;AAGhB;AAED5e,mBAAmB,CAACC,QAAQ,CAACyB,eAAe,CAAC,iBAAiB,EAAG1D,KAAK,IAAI;EACxE,OAAOI,KAAK,CAACO,aAAa,CAACgrB,yBAAyB,EAAE3rB,KAAK,CAAC;AAC9D,CAAC,CAAC;AClFI,MAAOmsB,gCAAiC,SAAQ/rB,KAAK,CAAC4M,SAAmB;EAE7ErK,MAAMA,CAAA;IACJ,OACEvC,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAA+B,GAC5CZ,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAuC,GACpDZ,KAAA,CAAAO,aAAA;MAAMK,SAAS,EAAC;IAAsC,GAAEgU,kBAAkB,CAACC,SAAS,CAAC,iCAAiC,CAAC,CAAQ,EAC/H7U,KAAA,CAAAO,aAAA;MAAMK,SAAS,EAAC;IAA4C,GAAEgU,kBAAkB,CAACC,SAAS,CAAC,uCAAuC,CAAC,CAAQ,CACvI,EACN7U,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAwC,GACrDZ,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAoC,EAAO,EAC1DZ,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAsC,EAAO,CACxD,CACF;;AAGX;AAEDgB,mBAAmB,CAACC,QAAQ,CAACyB,eAAe,CAAC,+BAA+B,EAAG1D,KAAK,IAAI;EACtF,OAAOI,KAAK,CAACO,aAAa,CAACwrB,gCAAgC,EAAEnsB,KAAK,CAAC;AACrE,CAAC,CAAC;ACXI,MAAOosB,uBAAwB,SAAQtsB,iBAA2B;EACtE,IAAYC,KAAKA,CAAA;IACf,OAAO,IAAI,CAACC,KAAK,CAACqG,IAAI;;EAEdpG,eAAeA,CAAA;IACvB,OAAO,IAAI,CAACF,KAAK;;EAEnBsS,iBAAiBA,CAAA;IACf,OAAOjS,KAAA,CAAAO,aAAA,CAAC0lB,kBAAkB;MAACpb,IAAI,EAAE,OAAO;MAAEqb,oBAAoB,EAAE,IAAI,CAACvmB,KAAK,CAACumB,oBAAoB;MAAEC,0BAA0B,EAAE,IAAI,CAACxmB,KAAK,CAACwmB;IAA0B,EAAI;;EAExKqF,eAAeA,CAAA;IACb,OAAQxrB,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAyB,GAC9CZ,KAAA,CAAAO,aAAA,CAAC+pB,eAAe;MAAC3qB,KAAK,EAAE,IAAI,CAACA,KAAK,CAAC8rB;IAAS,EAAoB,EAC/D,IAAI,CAAC9rB,KAAK,CAAC+rB,WAAW,GAAG1rB,KAAA,CAAAO,aAAA,CAACwY,aAAa;MAAC/S,MAAM,EAAE,IAAI,CAACrG,KAAK,CAAC8rB,SAAS,CAACzlB;IAAM,EAAI,GAAG,IAAI,CACnF;;EAER9F,aAAaA,CAAA;IACX,MAAMyrB,mBAAmB,GAAG,gDAAgD,IAAI,IAAI,CAAChsB,KAAK,CAACisB,oBAAoB,GAAG,yCAAyC,GAAG,EAAE,CAAC;IACjK,OACE5rB,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAE+qB;IAAmB,GAChC,IAAI,CAAChsB,KAAK,CAAC8rB,SAAS,CAACzlB,MAAM,CAAC6lB,OAAO,GAAG,IAAI,CAAC5Z,iBAAiB,EAAE,GAAG,IAAI,CAACuZ,eAAe,EAAE,EACvF,IAAI,CAACM,gBAAgB,EAAE,CACpB;;EAGVA,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACnsB,KAAK,CAACisB,oBAAoB,EAAE;MACnC,OACE5rB,KAAA,CAAAO,aAAA;QAAKK,SAAS,EAAC;MAA+B,GAC5CZ,KAAA,CAAAO,aAAA,CAACyE,eAAe;QAACrF,KAAK,EAAE,IAAI,CAACA,KAAK,CAAC6gB;MAAK,EAAoB,CACxD;WAEH;MACL,OAAO,IAAI;;;AAGhB;AAED5e,mBAAmB,CAACC,QAAQ,CAACyB,eAAe,CAAC,eAAe,EAAG1D,KAAK,IAAI;EACtE,OAAOI,KAAK,CAACO,aAAa,CAACyrB,uBAAuB,EAAEpsB,KAAK,CAAC;AAC5D,CAAC,CAAC;AC3CI,MAAOqsB,uBAAwB,SAAQvsB,iBAA2B;EACtE,IAAYC,KAAKA,CAAA;IACf,OAAO,IAAI,CAACC,KAAK,CAACqG,IAAI,IAAI,IAAI,CAACrG,KAAK,CAACD,KAAK;;EAElCE,eAAeA,CAAA;IACvB,OAAO,IAAI,CAACF,KAAK;;EAEnBO,aAAaA,CAAA;IACX,IAAI,CAAC,IAAI,CAACP,KAAK,EAAE,OAAO,IAAI;IAC5B,OACEK,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAE,8CAA8C,IAAI,IAAI,CAACjB,KAAK,CAACksB,OAAO,GAAG,6BAA6B,GAAG,EAAE;IAAC,GACvH,IAAI,CAACtgB,oBAAoB,EAAE,CACxB;;EAGVA,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAAC5L,KAAK,CAACksB,OAAO,EAAE;MACtB,OAAO7rB,KAAA,CAAAO,aAAA,CAAC0lB,kBAAkB;QAACpb,IAAI,EAAE,aAAa;QAAEqb,oBAAoB,EAAE,IAAI,CAACvmB,KAAK,CAACumB,oBAAoB;QAAEC,0BAA0B,EAAE,IAAI,CAACxmB,KAAK,CAACwmB;MAA0B,EAAI;WACvK;MACL,OACEnmB,KAAA,CAAAO,aAAA;QAAKK,SAAS,EAAC;MAAY,GACzBZ,KAAA,CAAAO,aAAA;QAAKK,SAAS,EAAC;MAAoC,GACjDZ,KAAA,CAAAO,aAAA;QAAKK,SAAS,EAAC;MAAgC,GAC7CZ,KAAA,CAAAO,aAAA,CAACqH,MAAM;QAACjI,KAAK,EAAE,IAAI,CAACA,KAAK,CAACusB;MAAmB,EAAW,CACpD,EACNlsB,KAAA,CAAAO,aAAA;QAAKK,SAAS,EAAC;MAAiD,GAC9DZ,KAAA,CAAAO,aAAA,CAACqH,MAAM;QAACjI,KAAK,EAAE,IAAI,CAACA,KAAK,CAACwsB;MAAa,EAAW,CAC9C,CACF,CACF;;;AAIb;AAEDvqB,mBAAmB,CAACC,QAAQ,CAACyB,eAAe,CAAC,qBAAqB,EAAG1D,KAAK,IAAI;EAC5E,OAAOI,KAAK,CAACO,aAAa,CAAC0rB,uBAAuB,EAAErsB,KAAK,CAAC;AAC5D,CAAC,CAAC;ACvCF,MAAMwsB,uBAAwB,SAAQ1sB,iBAAqD;EACzF,IAAcC,KAAKA,CAAA;IACjB,OAAO,IAAI,CAACC,KAAK,CAACD,KAAK;;EAEfE,eAAeA,CAAA;IACvB,OAAO,IAAI,CAACF,KAAK;;EAEnBO,aAAaA,CAAA;IACX,IAAI,CAAC,IAAI,CAACP,KAAK,CAAC+B,SAAS,EAAE,OAAO,IAAI;IAEtC,OAAQ1B,KAAA,CAAAO,aAAA,CAACuoB,IAAI;MAACnpB,KAAK,EAAE,IAAI,CAACA,KAAK,CAAC0sB;IAAI,EAAS;;AAEhD;AAEDzqB,mBAAmB,CAACC,QAAQ,CAACyB,eAAe,CAAC,qBAAqB,EAAG1D,KAAK,IAAI;EAC5E,OAAOI,KAAK,CAACO,aAAa,CACxB6rB,uBAAuB,EACvBxsB,KAAsC,CACvC;AACH,CAAC,CAAC;ACjBI,MAAO0sB,qBAAsB,SAAQ5sB,iBAAmD;EAE5F,IAAIC,KAAKA,CAAA;IACP,OAAO,IAAI,CAACC,KAAK,CAACD,KAAK;;EAGfE,eAAeA,CAAA;IACvB,OAAO,IAAI,CAACF,KAAK;;EAGZoS,SAASA,CAAA;IACd,IAAI,CAAC,IAAI,CAACpS,KAAK,EAAE,OAAO,KAAK;IAC7B,OAAO,KAAK,CAACoS,SAAS,EAAE;;EAG1B7R,aAAaA,CAAA;IACX,OACEF,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAE,IAAI,CAACjB,KAAK,CAACmL;IAAO,GAChC9K,KAAA,CAAAO,aAAA,CAACic,eAAe;MAAC7c,KAAK,EAAE,IAAI,CAACA,KAAK,CAAC+d;IAAa,EAAoB,EACpE1d,KAAA,CAAAO,aAAA,CAACqH,MAAM;MAACjI,KAAK,EAAE,IAAI,CAACA,KAAK,CAACqG;IAAM,EAAW,CACvC;;AAGX;AAIDpE,mBAAmB,CAACC,QAAQ,CAACyB,eAAe,CAAC,mBAAmB,EAAG1D,KAAK,IAAI;EAC1E,OAAOI,KAAK,CAACO,aAAa,CAAC+rB,qBAAqB,EAAE1sB,KAAK,CAAC;AAC1D,CAAC,CAAC;AC9BI,MAAO2sB,iBAAkB,SAAQ7sB,iBAA+C;EACpF,IAAIY,IAAIA,CAAA;IACN,OAAO,IAAI,CAACV,KAAK,CAACU,IAAI;;EAGdT,eAAeA,CAAA;IACvB,OAAO,IAAI,CAACS,IAAI;;EAGlBJ,aAAaA,CAAA;IACX,MAAMmD,OAAO,GAAG,IAAI,CAAC/C,IAAI,CAAC+C,OAAO,IAAI,IAAI,CAAC/C,IAAI,CAACyC,KAAK;IACpD,MAAMA,KAAK,GAAG,IAAI,CAACzC,IAAI,CAACuC,QAAQ,GAAG7C,KAAA,CAAAO,aAAA;MAAMK,SAAS,EAAC;IAAqB,GAAE,IAAI,CAACN,IAAI,CAACyC,KAAK,CAAQ,GAAG,IAAI;IACxG,MAAMgT,MAAM,GAAGvT,eAAe,CAC5BxC,KAAA,CAAAO,aAAA;MACEK,SAAS,EAAE,IAAI,CAACN,IAAI,CAACksB,mBAAmB,EAAE;MAC1Crd,IAAI,EAAC,QAAQ;MACb1B,QAAQ,EAAE,IAAI,CAACnN,IAAI,CAACmN,QAAQ;MAC5B9K,OAAO,EAAG8pB,IAAI,IAAK,IAAI,CAACnsB,IAAI,CAACsC,MAAM,CAAC,IAAI,CAACtC,IAAI,EAAE,IAAI,CAACA,IAAI,CAACosB,YAAY,CAACD,IAAI,CAAC,CAAC;MAC5E1pB,KAAK,EAAEM,OAAO;MAAA,gBACA,IAAI,CAAC/C,IAAI,CAACqsB,WAAW;MAAA,iBACpB,IAAI,CAACrsB,IAAI,CAACssB,YAAY;MACrC/rB,IAAI,EAAE,IAAI,CAACP,IAAI,CAACusB;IAAQ,GAExB7sB,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAE,IAAI,CAACN,IAAI,CAACwsB,kBAAkB;IAAE,GAC5C9sB,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAA0B,EAAO,CAC5C,EACLmC,KAAK,CACC,EAAE,IAAI,CAACzC,IAAI,EAAE;MAAEysB,UAAU,EAAE;IAAK,CAAE,CAAC;IAE9C,OAAOhX,MAAM;;AAEhB;AAIDnU,mBAAmB,CAACC,QAAQ,CAACyB,eAAe,CAAC,cAAc,EAAG1D,KAAK,IAAI;EACrE,OAAOI,KAAK,CAACO,aAAa,CAACgsB,iBAAiB,EAAE3sB,KAAK,CAAC;AACtD,CAAC,CAAC;8BC1CF,MAAMotB,qBAAsB,SAAQttB,iBAA2C;EAC7E6C,MAAMA,CAAA;IACJ,MAAMjC,IAAI,GAAG,IAAI,CAACV,KAAK,CAACU,IAAI;IAC5B,OACEN,KAAA,CAAAO,aAAA,CAAAP,KAAA,CAAAsM,QAAA,QACEtM,KAAA,CAAAO,aAAA,CAAC0C,OAAO;MAACC,QAAQ,EAAE5C,IAAI,CAAC4C,QAAQ;MAAEE,IAAI,EAAE9C,IAAI,CAAC2sB,QAAQ;MAAErsB,SAAS,EAAE;IAAsB,EAAa,EACrGZ,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAA2B,GACxCZ,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAuB,GACpCZ,KAAA,CAAAO,aAAA;MAAME,GAAG,EAAE;IAAC,GAAG,IAAI,CAACujB,eAAe,CAAC1jB,IAAI,CAACoa,QAAQ,EAAErY,SAAS,EAAE,WAAW,CAAC,CAAQ,CAC9E,EACL,IAAI,CAAC6qB,eAAe,EAAE,CACnB,CACL;;EAGPA,eAAeA,CAAA;IACb,IAAI,CAAC,IAAI,CAACttB,KAAK,CAACU,IAAI,CAAC2F,IAAI,CAACknB,aAAa,EAAE,OAAO,IAAI;IACpD,MAAM7sB,IAAI,GAAG,IAAI,CAACV,KAAK,CAACU,IAAI;IAC5B,OAAQkC,eAAe,CAACxC,KAAA,CAAAO,aAAA;MAAQ4O,IAAI,EAAC,QAAQ;MAACxM,OAAO,EAAGsG,KAAK;QAAOA,KAAK,CAACC,eAAe,EAAE;QAAE5I,IAAI,CAAC2F,IAAI,CAACmnB,QAAQ,EAAE;MAAC,CAAE;MAAErqB,KAAK,EAAEzC,IAAI,CAAC2F,IAAI,CAAConB,cAAc;MAAEzsB,SAAS,EAAE;IAA4B,GAC5LZ,KAAA,CAAAO,aAAA,CAAC0C,OAAO;MAACC,QAAQ,EAAE5C,IAAI,CAAC2F,IAAI,CAACqnB,aAAa;MAAElqB,IAAI,EAAE;IAAM,EAAY,CAC7D,CAAC;;AAEb;AAEDxB,mBAAmB,CAACC,QAAQ,CAACyB,eAAe,CAC1C,iBAAiB,EAChB1D,KAAK,IAAI;EACR,OAAOI,KAAK,CAACO,aAAa,CAACgtB,uBAAqB,EAAE3tB,KAAK,CAAC;AAC1D,CAAC,CACF;AC7BK,MAAO4tB,wBAAyB,SAAQC,kBAAkB;EAC9D3tB,YAAYF,KAAU;IACpB,KAAK,CAACA,KAAK,CAAC;;EAEd,IAAcgH,QAAQA,CAAA;IACpB,OAAO,IAAI,CAAC2N,YAAuC;;EAE3CN,WAAWA,CAAA;IACnB,OACEjU,KAAA,CAAAO,aAAA,CAAAP,KAAA,CAAAsM,QAAA,QACEtM,KAAA,CAAAO,aAAA;MACEM,IAAI,EAAC,YAAY;MACjBiB,EAAE,EAAE,IAAI,CAAC8E,QAAQ,CAACsd,OAAO;MACzBzW,QAAQ,EAAE,IAAI,CAACvG,aAAa;MAC5BtG,SAAS,EAAE,IAAI,CAACgG,QAAQ,CAACS,UAAU,CAAC0b,OAAO;MAC3CpiB,GAAG,EAAGyoB,KAAK,IAAM,IAAI,CAACH,UAAU,CAACG,KAAK,CAAE;MACxChS,WAAW,EAAE,IAAI,CAACxQ,QAAQ,CAAC8mB,mBAAmB;MAC9CC,YAAY,EAAC,KAAK;MAClB/X,MAAM,EAAG3M,KAAK,IAAK,IAAI,CAACrC,QAAQ,CAACgP,MAAM,CAAC3M,KAAK,CAACS,WAAW,CAAC;MAC1DwD,OAAO,EAAGjE,KAAK,IAAK,IAAI,CAACrC,QAAQ,CAACsG,OAAO,CAACjE,KAAK,CAACS,WAAW,CAAC;MAC5DiT,QAAQ,EAAE,IAAI,CAAC/V,QAAQ,CAAC+V,QAAQ;MAChCiR,aAAa,EAAE3kB,KAAK,IAAI,IAAI,CAACrC,QAAQ,CAACgnB,aAAa,CAAC3kB,KAAK,CAACS,WAAyB,CAAC;MACpFyY,OAAO,EAAGlZ,KAAK,IAAK,IAAI,CAACrC,QAAQ,CAACub,OAAO,CAAClZ,KAAK,CAACS,WAAW,CAAC;MAC5DwY,SAAS,EAAGjZ,KAAK,IAAK,IAAI,CAACrC,QAAQ,CAACinB,cAAc,CAAC5kB,KAAK,CAACS,WAAW,CAAC;MAAA,iBACtD,IAAI,CAAC9C,QAAQ,CAACknB,uBAAuB;MAAA,cACxC,IAAI,CAAClnB,QAAQ,CAACmnB,oBAAoB;MAAA,mBAC7B,IAAI,CAACnnB,QAAQ,CAAConB,yBAAyB;MAAA,oBACtC,IAAI,CAACpnB,QAAQ,CAACqnB,0BAA0B;MAAA,gBAC5C,IAAI,CAACrnB,QAAQ,CAACsnB,sBAAsB;MAAA,qBAC/B,IAAI,CAACtnB,QAAQ,CAACunB;IAA2B,EAC5D,CACD;;EAGGjuB,aAAaA,CAAA;IACrB,OACEF,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAE,IAAI,CAACgG,QAAQ,CAACS,UAAU,CAACkM,IAAI;MAAE2O,SAAS,EAAEjZ,KAAK,IAAI,IAAI,CAACrC,QAAQ,CAACsb,SAAS,CAACjZ,KAAK,CAACS,WAAW;IAAC,GAC1G,IAAI,CAACuK,WAAW,EAAE,EAClB,IAAI,CAACN,aAAa,EAAE,CACjB;;EAGAya,YAAYA,CAAA;IACpB,OAAO,IAAI,CAACxnB,QAAQ,CAACynB,aAAa;;EAE1B1a,aAAaA,CAAA;IACrB,OACE3T,KAAA,CAAAO,aAAA;MAAMK,SAAS,EAAE,IAAI,CAACgG,QAAQ,CAACS,UAAU,CAACinB;IAAgB,GACxDtuB,KAAA,CAAAO,aAAA;MACE6O,QAAQ,EAAE,EAAE;MAAA,eACC,MAAM;MACnBxO,SAAS,EAAE,IAAI,CAACgG,QAAQ,CAACS,UAAU,CAACknB,WAAW;MAC/C9gB,QAAQ,EAAE,IAAI,CAACvG,aAAa;MAC5BvE,OAAO,EAAE,IAAI,CAACiE,QAAQ,CAAC4nB,iBAAiB;MACxCC,WAAW,EAAE,IAAI,CAAC7nB,QAAQ,CAAC8nB,qBAAqB;MAChDtM,SAAS,EAAE,IAAI,CAACxb,QAAQ,CAAC+nB,eAAe;MACxCzjB,YAAY,EAAE,IAAI,CAACtE,QAAQ,CAACgoB,kBAAkB;MAC9ChZ,MAAM,EAAE3M,KAAK,IAAI,IAAI,CAACrC,QAAQ,CAACgP,MAAM,CAAC3M,KAAK,CAACS,WAAW,CAAC;MACxDwD,OAAO,EAAEjE,KAAK,IAAI,IAAI,CAACrC,QAAQ,CAACsG,OAAO,CAACjE,KAAK,CAACS,WAAW;IAAC,GAC1D1J,KAAA,CAAAO,aAAA,CAAC0C,OAAO;MAACC,QAAQ,EAAE,IAAI,CAAC0D,QAAQ,CAACS,UAAU,CAACwnB,kBAAkB;MAAEzrB,IAAI,EAAE;IAAM,EAAY,CACjF,EACTpD,KAAA,CAAAO,aAAA;MACE6O,QAAQ,EAAE,EAAE;MAAA,eACC,MAAM;MACnBxO,SAAS,EAAE,IAAI,CAACgG,QAAQ,CAACS,UAAU,CAACknB,WAAW;MAC/C9gB,QAAQ,EAAE,IAAI,CAACvG,aAAa;MAC5BvE,OAAO,EAAE,IAAI,CAACiE,QAAQ,CAACkoB,eAAe;MACtCL,WAAW,EAAE,IAAI,CAAC7nB,QAAQ,CAACmoB,mBAAmB;MAC9C3M,SAAS,EAAE,IAAI,CAACxb,QAAQ,CAAC+nB,eAAe;MACxCzjB,YAAY,EAAE,IAAI,CAACtE,QAAQ,CAACgoB,kBAAkB;MAC9ChZ,MAAM,EAAE3M,KAAK,IAAI,IAAI,CAACrC,QAAQ,CAACgP,MAAM,CAAC3M,KAAK,CAACS,WAAW,CAAC;MACxDwD,OAAO,EAAEjE,KAAK,IAAI,IAAI,CAACrC,QAAQ,CAACsG,OAAO,CAACjE,KAAK,CAACS,WAAW;IAAC,GAC1D1J,KAAA,CAAAO,aAAA,CAAC0C,OAAO;MAACC,QAAQ,EAAE,IAAI,CAAC0D,QAAQ,CAACS,UAAU,CAAC2nB,kBAAkB;MAAE5rB,IAAI,EAAE;IAAM,EAAY,CACjF,CACJ;;AAIZ;AAEDyD,oBAAoB,CAAChF,QAAQ,CAACsT,gBAAgB,CAAC,UAAU,EAAGvV,KAAK,IAAI;EACnE,OAAOI,KAAK,CAACO,aAAa,CAACitB,wBAAwB,EAAE5tB,KAAK,CAAC;AAC7D,CAAC,CAAC;AClFF,MAAMotB,qBAAsB,SAAQttB,iBAA2C;EAC7E6C,MAAMA,CAAA;IACJ,MAAMjC,IAAI,GAAG,IAAI,CAACV,KAAK,CAACU,IAAI;IAC5B,OACEN,KAAA,CAAAO,aAAA,CAAAP,KAAA,CAAAsM,QAAA,QACEtM,KAAA,CAAAO,aAAA;MAAMK,SAAS,EAAC,gCAAgC;MAACE,KAAK,EAAE;QAAEmuB,eAAe,EAAE3uB,IAAI,CAAC+I;MAAK;IAAE,EAAS,EAChGrJ,KAAA,CAAAO,aAAA;MAAME,GAAG,EAAE;IAAC,GAAG,IAAI,CAACujB,eAAe,CAAC1jB,IAAI,CAACoa,QAAQ,EAAErY,SAAS,EAAE,WAAW,CAAC,CAAQ,CACjF;;AAGR;AAEDT,mBAAmB,CAACC,QAAQ,CAACyB,eAAe,CAC1C,YAAY,EACX1D,KAAK,IAAI;EACR,OAAOI,KAAK,CAACO,aAAa,CAACysB,qBAAqB,EAAEptB,KAAK,CAAC;AAC1D,CAAC,CACF;ACjBK,MAAOsvB,mBAAoB,SAAQzB,kBAAkB;EACzD3tB,YAAYF,KAAU;IACpB,KAAK,CAACA,KAAK,CAAC;;EAEd,IAAcgH,QAAQA,CAAA;IACpB,OAAO,IAAI,CAAC2N,YAAkC;;EAEtCN,WAAWA,CAAA;IACnB,OACEjU,KAAA,CAAAO,aAAA,CAAAP,KAAA,CAAAsM,QAAA,QACEtM,KAAA,CAAAO,aAAA;MACEuB,EAAE,EAAE,IAAI,CAAC8E,QAAQ,CAACsd,OAAO;MACzBzW,QAAQ,EAAE,IAAI,CAACvG,aAAa;MAC5BtG,SAAS,EAAE,IAAI,CAACgG,QAAQ,CAACS,UAAU,CAAC0b,OAAO;MAC3CpiB,GAAG,EAAGyoB,KAAK,IAAM,IAAI,CAACH,UAAU,CAACG,KAAK,CAAE;MACxChS,WAAW,EAAE,IAAI,CAACxQ,QAAQ,CAAC8mB,mBAAmB;MAC9CC,YAAY,EAAC,KAAK;MAClBxL,OAAO,EAAGlZ,KAAK,IAAK,IAAI,CAACrC,QAAQ,CAACub,OAAO,CAAClZ,KAAK,CAACS,WAAW,CAAC;MAC5DkM,MAAM,EAAG3M,KAAK,IAAK,IAAI,CAACrC,QAAQ,CAACgP,MAAM,CAAC3M,KAAK,CAACS,WAAW,CAAC;MAC1DiT,QAAQ,EAAE,IAAI,CAAC/V,QAAQ,CAAC+V,QAAQ;MAChCiR,aAAa,EAAE3kB,KAAK,IAAI,IAAI,CAACrC,QAAQ,CAACgnB,aAAa,CAAC3kB,KAAK,CAACS,WAAyB,CAAC;MAAA,iBACrE,IAAI,CAAC9C,QAAQ,CAACknB,uBAAuB;MAAA,mBACnC,IAAI,CAAClnB,QAAQ,CAAConB,yBAAyB;MAAA,cAC5C,IAAI,CAACpnB,QAAQ,CAACmnB,oBAAoB;MAAA,gBAChC,IAAI,CAACnnB,QAAQ,CAACsnB,sBAAsB;MAAA,oBAChC,IAAI,CAACtnB,QAAQ,CAACqnB;IAA0B,EAC1D,CACD;;EAGG/tB,aAAaA,CAAA;IACrB,OACEF,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAE,IAAI,CAACgG,QAAQ,CAACS,UAAU,CAACkM,IAAI;MAAE2O,SAAS,EAAEjZ,KAAK,IAAI,IAAI,CAACrC,QAAQ,CAACsb,SAAS,CAACjZ,KAAK,CAACS,WAAW;IAAC,GAC1G,IAAI,CAACylB,iBAAiB,EAAE,EACxB,IAAI,CAAClb,WAAW,EAAE,EAClB,IAAI,CAACrN,QAAQ,CAACwoB,kBAAkB,GAAG,IAAI,CAACC,oBAAoB,EAAE,GAAG,IAAI,CAClE;;EAGAjB,YAAYA,CAAA;IACpB,OAAO,IAAI,CAACxnB,QAAQ,CAACynB,aAAa;;EAE1Bc,iBAAiBA,CAAA;IACzB,OAAOnvB,KAAA,CAAAO,aAAA;MAAOK,SAAS,EAAE,IAAI,CAACgG,QAAQ,CAAC0oB,YAAY,EAAE;MAAExuB,KAAK,EAAE,IAAI,CAAC8F,QAAQ,CAAC2oB,cAAc;IAAE,GAC1FvvB,KAAA,CAAAO,aAAA,CAAC0C,OAAO;MAACC,QAAQ,EAAE,IAAI,CAAC0D,QAAQ,CAACS,UAAU,CAACmoB,UAAU;MAAEpsB,IAAI,EAAE;IAAM,EAAY,EAChFpD,KAAA,CAAAO,aAAA;MAAO4O,IAAI,EAAC,OAAO;MACjB1B,QAAQ,EAAE,IAAI,CAACvG,aAAa;MAC5BmC,KAAK,EAAE,IAAI,CAACzC,QAAQ,CAAC6oB,kBAAkB;MACvC7uB,SAAS,EAAE,IAAI,CAACgG,QAAQ,CAACS,UAAU,CAACqoB,UAAU;MAC9C/S,QAAQ,EAAG1T,KAAK,IAAK,IAAI,CAACrC,QAAQ,CAAC+oB,kBAAkB,CAAC1mB,KAAK,CAACS,WAAW,CAAC;MACxE0F,QAAQ,EAAE,EAAE;MAAA,iBACG,IAAI,CAACxI,QAAQ,CAACknB,uBAAuB;MAAA,mBACnC,IAAI,CAAClnB,QAAQ,CAAConB,yBAAyB;MAAA,cAC5C,IAAI,CAACpnB,QAAQ,CAACmnB,oBAAoB;MAAA,gBAChC,IAAI,CAACnnB,QAAQ,CAACsnB,sBAAsB;MAAA,oBAChC,IAAI,CAACtnB,QAAQ,CAACqnB;IAA0B,EAAI,CAC1D;;EAEAoB,oBAAoBA,CAAA;IAC5B,OACErvB,KAAA,CAAAO,aAAA,CAAAP,KAAA,CAAAsM,QAAA,QACEtM,KAAA,CAAAO,aAAA;MAAA,eAAiB,MAAM;MAACK,SAAS,EAAE,IAAI,CAACgG,QAAQ,CAACS,UAAU,CAACuoB;IAAoB,GAC7EhuB,mBAAmB,CAACC,QAAQ,CAACtB,aAAa,CAAC,oBAAoB,EAAE;MAAED,IAAI,EAAE,IAAI,CAACsG,QAAQ,CAACipB;IAAc,CAAE,CAAC,CACrG,EACL,IAAI,CAACld,WAAW,EAAE,CAClB;;EAGGA,WAAWA,CAAA;IACnB,OAAO3S,KAAA,CAAAO,aAAA,CAACqS,KAAK;MAACjT,KAAK,EAAE,IAAI,CAACiH,QAAQ,CAACipB,cAAc,CAAChd;IAAU,EAAU;;AAEzE;AAEDhM,oBAAoB,CAAChF,QAAQ,CAACsT,gBAAgB,CAAC,OAAO,EAAGvV,KAAK,IAAI;EAChE,OAAOI,KAAK,CAACO,aAAa,CAAC2uB,mBAAmB,EAAEtvB,KAAK,CAAC;AACxD,CAAC,CAAC;AC3EI,MAAOkwB,wBAAyB,SAAQrC,kBAAkB;EAC9D3tB,YAAYF,KAAU;IACpB,KAAK,CAACA,KAAK,CAAC;;EAEd,IAAcmwB,YAAYA,CAAA;IACxB,OAAO,IAAI,CAACxb,YAAuC;;EAE3C6Z,YAAYA,CAAA;IACpB,OAAO,IAAI,CAACxnB,QAAQ,CAACynB,aAAa;;EAE1Bpa,WAAWA,CAAA;IACnB,OACEjU,KAAA,CAAAO,aAAA,CAAAP,KAAA,CAAAsM,QAAA,QACEtM,KAAA,CAAAO,aAAA;MACEkN,QAAQ,EAAE,IAAI,CAAC7G,QAAQ,CAACopB,mBAAmB;MAC3CpvB,SAAS,EAAE,IAAI,CAACmvB,YAAY,CAAC1oB,UAAU,CAAC0b,OAAO;MAC/C3L,WAAW,EAAE,IAAI,CAAC2Y,YAAY,CAACrC,mBAAmB;MAClD/sB,GAAG,EAAGyoB,KAAK,IAAM,IAAI,CAACH,UAAU,CAACG,KAAK,CAAE;MACxCuE,YAAY,EAAC,KAAK;MAClBxe,IAAI,EAAC,MAAM;MACXyG,MAAM,EAAG3M,KAAK,IAAK,IAAI,CAAC8mB,YAAY,CAACE,WAAW,CAAChnB,KAAK,CAACS,WAAW,CAAC;MACnEiT,QAAQ,EAAG1T,KAAK,IAAK,IAAI,CAAC8mB,YAAY,CAACG,aAAa,CAACjnB,KAAK,CAACS,WAAW;IAAC,EACvE,CACD;;EAGGymB,eAAeA,CAAA;IACvB,OACEnwB,KAAA,CAAAO,aAAA;MACE4O,IAAI,EAAC,MAAM;MACX1B,QAAQ,EAAE,IAAI,CAACvG,aAAa;MAC5BtG,SAAS,EAAE,IAAI,CAACmvB,YAAY,CAAC1oB,UAAU,CAAC+oB,SAAS;MACjDtuB,EAAE,EAAE,IAAI,CAACiuB,YAAY,CAAC7L,OAAO;MAAA,iBACd,IAAI,CAAC6L,YAAY,CAACM,YAAY;MAAA,cACjC,IAAI,CAACN,YAAY,CAACxG,SAAS;MAAA,gBACzB,IAAI,CAACwG,YAAY,CAACO,WAAW;MAAA,oBACzB,IAAI,CAACP,YAAY,CAACQ,eAAe;MACnDC,QAAQ,EAAE,KAAK;MAAEztB,KAAK,EAAE,IAAI,CAACgtB,YAAY,CAACU,UAAU;MACpDphB,MAAM,EAAE,IAAI,CAAC0gB,YAAY,CAACzgB,aAAa;MACvCF,QAAQ,EAAE,EAAE;MACZuN,QAAQ,EAAG1T,KAAK,IAAK,IAAI,CAAC8mB,YAAY,CAACW,iBAAiB,CAACznB,KAAK,CAACS,WAAW;IAAC,EAAI;;EAG3EiK,aAAaA,CAAA;IACrB,OACE3T,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAE,IAAI,CAACmvB,YAAY,CAAC1oB,UAAU,CAACinB;IAAgB,GAC1D,IAAI,CAAC7a,iBAAiB,EAAE,EACxB,IAAI,CAAC3D,kBAAkB,EAAE,CACtB;;EAGA2D,iBAAiBA,CAAA;IACzB,OAAOjR,eAAe,CACpBxC,KAAA,CAAAO,aAAA;MACEK,SAAS,EAAE,IAAI,CAACmvB,YAAY,CAAC1oB,UAAU,CAACspB,WAAW;MACnD5tB,KAAK,EAAE,IAAI,CAACgtB,YAAY,CAACa,kBAAkB;MAC3CnjB,QAAQ,EAAE,IAAI,CAACsiB,YAAY,CAACc,wBAAwB,EAAE;MACtDluB,OAAO,EAAE,IAAI,CAACotB,YAAY,CAACe;IAAO,GAClC9wB,KAAA,CAAAO,aAAA,CAAC0C,OAAO;MAACC,QAAQ,EAAE,IAAI,CAAC6sB,YAAY,CAAC1oB,UAAU,CAAC0pB,eAAe;MAAE3tB,IAAI,EAAE;IAAM,EAAY,CAClF,CACT;;EAEM0M,kBAAkBA,CAAA;IAC1B,OACEtN,eAAe,CACbxC,KAAA,CAAAO,aAAA;MACEoC,OAAO,EAAEsG,KAAK,IAAI,IAAI,CAAC8mB,YAAY,CAACiB,WAAW,CAAC/nB,KAAK,CAACS,WAAW,CAAC;MAClE9I,SAAS,EAAE,IAAI,CAACmvB,YAAY,CAACkB,kBAAkB,EAAE;MACjDC,OAAO,EAAE,IAAI,CAACnB,YAAY,CAAC7L,OAAO;MAAA,cACtB,IAAI,CAAC6L,YAAY,CAACoB;IAAmB,GACjDnxB,KAAA,CAAAO,aAAA,CAAC0C,OAAO;MAACC,QAAQ,EAAE,IAAI,CAAC6sB,YAAY,CAAC1oB,UAAU,CAAC+pB,gBAAgB;MAAEhuB,IAAI,EAAE,MAAM;MAAEL,KAAK,EAAE,IAAI,CAACgtB,YAAY,CAACoB;IAAmB,EAAY,CAClI,CACT;;EAGKjxB,aAAaA,CAAA;IACrB,OACEF,KAAA,CAAAO,aAAA;MACEK,SAAS,EAAE,IAAI,CAACmvB,YAAY,CAAC1oB,UAAU,CAACkM,IAAI;MAC5C5S,GAAG,EAAE6kB,EAAE,IAAI,IAAI,CAAC6L,UAAU,CAAC7L,EAAE,CAAC;MAC9BzN,WAAW,EAAE,IAAI,CAACgY,YAAY,CAAChY,WAAW;MAC1CC,UAAU,EAAE,IAAI,CAAC+X,YAAY,CAAC/X,UAAU;MACxCF,MAAM,EAAE,IAAI,CAACiY,YAAY,CAACjY,MAAM;MAChCG,WAAW,EAAE,IAAI,CAAC8X,YAAY,CAAC9X,WAAW;MAC1CiK,SAAS,EAAEjZ,KAAK,IAAI,IAAI,CAACrC,QAAQ,CAACsb,SAAS,CAACjZ,KAAK,CAACS,WAAW;IAAC,GAC7D,IAAI,CAACuK,WAAW,EAAE,EAClB,IAAI,CAACkc,eAAe,EAAE,EACtB,IAAI,CAACxc,aAAa,EAAE,CACjB;;AAGX;AAED9M,oBAAoB,CAAChF,QAAQ,CAACsT,gBAAgB,CAAC,UAAU,EAAGvV,KAAK,IAAI;EACnE,OAAOI,KAAK,CAACO,aAAa,CAACuvB,wBAAwB,EAAElwB,KAAK,CAAC;AAC7D,CAAC,CAAC;AC/FI,MAAO0xB,2BAA4B,SAAQhd,yBAAyB;EACxE,IAAc1N,QAAQA,CAAA;IACpB,OAAO,IAAI,CAAC2N,YAAuF;;EAE3FrU,aAAaA,CAAA;IACrB,MAAMgpB,WAAW,GAAG,IAAI,CAACjV,WAAW,EAAE;IACtC,MAAMsd,WAAW,GAAG,IAAI,CAACC,iBAAiB,EAAE;IAC5C,OACExxB,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAE,IAAI,CAACgG,QAAQ,CAAC6qB,YAAY;IAAE,GACzCvI,WAAW,EACXqI,WAAW,CACR;;EAIAtd,WAAWA,CAAA;IACnB,OAAOpN,oBAAoB,CAAChF,QAAQ,CAACiF,cAAc,CAAC,IAAI,CAACF,QAAQ,CAAC8qB,uBAAuB,EACvF;MACE9qB,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBM,aAAa,EAAE,IAAI,CAACN,QAAQ,CAAC4O,eAAe;MAC5C/R,OAAO,EAAE;IACV,EAAC;;EAEI+tB,iBAAiBA,CAAA;IACzB,OAAQxxB,KAAA,CAAAO,aAAA;MACNK,SAAS,EAAE,IAAI,CAACgG,QAAQ,CAACS,UAAU,CAACkqB,WAAW;MAC/C9jB,QAAQ,EAAE,IAAI,CAAC7G,QAAQ,CAAC+qB,iBAAiB,CAACC,UAAU;MACpD7uB,KAAK,EAAE,IAAI,CAAC6D,QAAQ,CAAC+qB,iBAAiB,CAACE,OAAO;MAC9ClvB,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACiE,QAAQ,CAAC+qB,iBAAiB,CAACG,UAAU;IAAE,GAC3D9xB,KAAA,CAAAO,aAAA,CAAC0C,OAAO;MAACC,QAAQ,EAAE,IAAI,CAAC0D,QAAQ,CAACS,UAAU,CAAC0qB,eAAe;MAAE3uB,IAAI,EAAE;IAAM,EAAY,CAC9E;;AAEZ;AAEDyD,oBAAoB,CAAChF,QAAQ,CAACsT,gBAAgB,CAAC,eAAe,EAAGvV,KAAK,IAAI;EACxE,OAAOI,KAAK,CAACO,aAAa,CAAC+wB,2BAA2B,EAAE1xB,KAAK,CAAC;AAChE,CAAC,CAAC;AAEFiH,oBAAoB,CAAChF,QAAQ,CAACsT,gBAAgB,CAAC,kBAAkB,EAAGvV,KAAK,IAAI;EAC3E,OAAOI,KAAK,CAACO,aAAa,CAAC+wB,2BAA2B,EAAE1xB,KAAK,CAAC;AAChE,CAAC,CAAC;ACzCI,MAAOoyB,2BAA4B,SAAQ1d,yBAAyB;EAC9DpU,aAAaA,CAAA;IACrB,MAAM6V,MAAM,GAAGvT,eAAe,CAACxC,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAE,4BAA4B,IAAI,IAAI,CAAC2T,YAAY,CAAClL,KAAK,GAAG,sCAAsC,GAAG,EAAE,CAAC;MAAE+F,QAAQ,EAAE,CAAC;MAChKvO,IAAI,EAAC,UAAU;MAAA,gBACD,IAAI,CAAC0T,YAAY,CAAC0d,YAAY,IAAI,KAAK;MAAA,iBACtC,IAAI,CAAC1d,YAAY,CAACuZ,uBAAuB;MAAA,cAC5C,IAAI,CAACvZ,YAAY,CAACwZ,oBAAoB;MAAA,mBACjC,IAAI,CAACxZ,YAAY,CAACyZ,yBAAyB;MAAA,gBAC9C,IAAI,CAACzZ,YAAY,CAAC2Z,sBAAsB;MAAA,qBACnC,IAAI,CAAC3Z,YAAY,CAAC4Z;IAA2B,GAEhEnuB,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAA2B,GACxCZ,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAkE,EAAO,CACpF,EACNZ,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAA2B,GACxCZ,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAAmE,EAAO,CACrF,CACF,EAAE,IAAI,CAAC2T,YAAY,EAAE;MAAEwY,UAAU,EAAE;IAAK,CAAE,CAAC;IAEjD,OACE/sB,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC,oBAAoB;MAAC+B,OAAO,EAAEA,CAAA,KAAM,IAAI,CAAC4R,YAAY,CAAClL,KAAK,GAAG,CAAC,IAAI,CAACkL,YAAY,CAAClL;IAAK,GAClG0M,MAAM,EACP/V,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC;IAA6B,GAC1CZ,KAAA,CAAAO,aAAA;MAAKK,SAAS,EAAC,2BAA2B;MAACkB,EAAE,EAAE,IAAI,CAACyS,YAAY,CAAC2d;IAAmB,GACjFxyB,iBAAiB,CAACskB,eAAe,CAAC,IAAI,CAACzP,YAAY,CAACmG,QAAQ,CAAC,CAC1D,CACF,CACF;;AAGX;AAED7T,oBAAoB,CAAChF,QAAQ,CAACsT,gBAAgB,CAC5C,mBAAmB,EAClBvV,KAAK,IAAI;EACR,OAAOI,KAAK,CAACO,aAAa,CAACyxB,2BAA2B,EAAEpyB,KAAK,CAAC;AAChE,CAAC,CACF;AAEDslB,eAAe,CAACrjB,QAAQ,CAACsjB,gBAAgB,CACvC,SAAS,EACT,QAAQ,EACR,mBAAmB,CACpB;AC9CM,IAAIgN,OAAA;AACXA,OAAO,GAAG,GAAG,OAAmB,EAAE;AAmFlCC,mBAAmB,CAAC,GAAG,OAAmB,EAAE,EAAE,sBAAsB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}