{"ast": null, "code": "var _jsxFileName = \"C:\\\\git\\\\Partner-Site\\\\BdoPartner.Plans\\\\Development\\\\ReactClientApp\\\\src\\\\pages\\\\admin\\\\questionnaireManagement.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef, useCallback } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport { DataTable } from \"primereact/datatable\";\nimport { Column } from \"primereact/column\";\nimport { Button } from \"primereact/button\";\nimport { Dialog } from \"primereact/dialog\";\nimport { InputText } from \"primereact/inputtext\";\nimport { Dropdown } from \"primereact/dropdown\";\nimport { Toast } from \"primereact/toast\";\nimport { ConfirmDialog, confirmDialog } from \"primereact/confirmdialog\";\nimport { Tag } from \"primereact/tag\";\nimport questionnaireService from \"../../services/questionnaireService\";\nimport { messageService } from \"../../core/message/messageService\";\nimport { getCycleDisplayName } from \"../../core/enumertions/partnerPlanCycle\";\nimport CopyQuestionnaireModal from \"../../components/admin/CopyQuestionnaireModal\";\nimport BatchReopenPopup from \"../../components/admin/batchReopenPopup\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const QuestionnaireManagement = () => {\n  _s();\n  const navigate = useNavigate();\n  const [questionnaires, setQuestionnaires] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [showCreateDialog, setShowCreateDialog] = useState(false);\n  const [showCopyDialog, setShowCopyDialog] = useState(false);\n  const [questionnaireToCopy, setQuestionnaireToCopy] = useState(null);\n  const [copyLoading, setCopyLoading] = useState(false);\n  const [newQuestionnaire, setNewQuestionnaire] = useState({\n    name: \"\",\n    year: new Date().getFullYear()\n  });\n  const [globalFilter, setGlobalFilter] = useState(\"\");\n  const [totalRecords, setTotalRecords] = useState(0);\n  const [first, setFirst] = useState(0);\n  const [rows, setRows] = useState(10);\n  const [showBatchReopenPopup, setShowBatchReopenPopup] = useState(false);\n  const [currentRepublishQuestionnaire, setCurrentRepublishQuestionnaire] = useState(null);\n  const toast = useRef(null);\n  const loadingRef = useRef(false);\n\n  // Year options for dropdown\n  const currentYear = new Date().getFullYear();\n  const yearOptions = [];\n  for (let i = currentYear - 5; i <= currentYear + 5; i++) {\n    yearOptions.push({\n      label: i.toString(),\n      value: i\n    });\n  }\n  const loadQuestionnaires = useCallback(async (pageIndex = 0, pageSize = 10, searchTerm = \"\") => {\n    // Prevent duplicate calls if already loading\n    if (loading || loadingRef.current) {\n      return;\n    }\n    try {\n      setLoading(true);\n      loadingRef.current = true;\n      const data = await questionnaireService.searchQuestionnaires(searchTerm, null,\n      // year filter\n      null,\n      // status filter\n      null,\n      // isActive filter\n      pageIndex, pageSize);\n      setQuestionnaires(data.items);\n      setTotalRecords(data.totalCount);\n    } catch (error) {\n      console.error(\"Error loading questionnaires:\", error);\n      messageService.errorToast(\"Error loading questionnaires\");\n      setQuestionnaires([]);\n      setTotalRecords(0);\n    } finally {\n      setLoading(false);\n      loadingRef.current = false;\n    }\n  }, [loading]);\n  useEffect(() => {\n    loadQuestionnaires(0, rows, globalFilter);\n  }, []); // Only run on mount to prevent duplicate calls\n\n  const onPageChange = event => {\n    const newFirst = event.first;\n    const newRows = event.rows;\n    const newPageIndex = Math.floor(newFirst / newRows); // 0-based page index\n\n    setFirst(newFirst);\n    setRows(newRows);\n    loadQuestionnaires(newPageIndex, newRows, globalFilter);\n  };\n  const onGlobalFilterChange = value => {\n    setGlobalFilter(value);\n    setFirst(0); // Reset to first page when filtering\n    loadQuestionnaires(0, rows, value); // Start from page index 0\n  };\n  const handleCreateQuestionnaire = async () => {\n    if (!newQuestionnaire.name.trim()) {\n      messageService.warnToast(\"Please enter a questionnaire name\");\n      return;\n    }\n    try {\n      // Check if there's already an active questionnaire in the selected year\n      const hasActiveQuestionnaire = await questionnaireService.hasActiveQuestionnaireInYear(newQuestionnaire.year);\n      if (hasActiveQuestionnaire) {\n        messageService.errorToast(`${newQuestionnaire.year} year already has an active questionnaire. Only one active questionnaire is allowed per year.`);\n        return;\n      }\n      const questionnaireData = {\n        name: newQuestionnaire.name.trim(),\n        year: newQuestionnaire.year,\n        status: 0,\n        // Draft status\n        isActive: true,\n        draftDefinitionJson: JSON.stringify({}),\n        acknowledgement: false,\n        acknowledgementText: \"\",\n        generalComments: false,\n        generalCommentsText: \"\",\n        formSystemVersion: 1\n      };\n      const createdQuestionnaire = await questionnaireService.createQuestionnaire(questionnaireData);\n      messageService.successToast(\"Questionnaire created successfully\");\n      setShowCreateDialog(false);\n      setNewQuestionnaire({\n        name: \"\",\n        year: currentYear\n      });\n      loadQuestionnaires(Math.floor(first / rows), rows, globalFilter);\n\n      // Navigate to edit the new questionnaire\n      navigate(`/admin/questionnaire-designer/${createdQuestionnaire.id}`);\n    } catch (error) {\n      console.error(\"Error creating questionnaire:\", error);\n      messageService.errorToast(error.message || \"Error creating questionnaire\");\n    }\n  };\n  const handleEditQuestionnaire = questionnaire => {\n    navigate(`/admin/questionnaire-designer/${questionnaire.id}`);\n  };\n  const handleViewQuestionnaire = questionnaire => {\n    navigate(`/admin/questionnaire-viewer/${questionnaire.id}`);\n  };\n  const handleCopyQuestionnaire = questionnaire => {\n    setQuestionnaireToCopy(questionnaire);\n    setShowCopyDialog(true);\n  };\n  const handleCopyConfirm = async (sourceId, targetYear) => {\n    setCopyLoading(true);\n    try {\n      await questionnaireService.copyQuestionnaire(sourceId, targetYear);\n      messageService.successToast(`Questionnaire successfully copied to year ${targetYear}`);\n      setShowCopyDialog(false);\n      setQuestionnaireToCopy(null);\n      // Refresh the questionnaire list\n      loadQuestionnaires(Math.floor(first / rows), rows, globalFilter);\n    } catch (error) {\n      console.error(\"Error copying questionnaire:\", error);\n      messageService.errorToast(error.message || \"Error copying questionnaire\");\n    } finally {\n      setCopyLoading(false);\n    }\n  };\n  const handleCopyCancel = () => {\n    setShowCopyDialog(false);\n    setQuestionnaireToCopy(null);\n  };\n  const handlePublishQuestionnaire = async questionnaire => {\n    try {\n      const isRepublish = questionnaire.status === 1;\n\n      // Check if there's already another active questionnaire in the same year\n      // This applies to both first-time publishing and republishing\n      const hasActiveInYear = await questionnaireService.hasActiveQuestionnaireInYear(questionnaire.year, questionnaire.id);\n      if (hasActiveInYear) {\n        messageService.errorToast(`${questionnaire.year} year already has an active questionnaire. Only one active questionnaire is allowed per year.`);\n        return;\n      }\n      if (isRepublish) {\n        // Check if there are enabled cycles\n        const enabledCycles = getEnabledCyclesFromString(questionnaire.enableCycles);\n        if (enabledCycles.length > 0) {\n          // Store the questionnaire for the batch reopen popup\n          setCurrentRepublishQuestionnaire(questionnaire);\n          setShowBatchReopenPopup(true);\n          return;\n        } else {\n          // No enabled cycles, proceed with direct republish\n          await performDirectPublish(questionnaire);\n        }\n      } else {\n        // For first-time publish, proceed directly\n        await performDirectPublish(questionnaire);\n      }\n    } catch (error) {\n      console.error(\"Error checking for active questionnaire:\", error);\n      messageService.errorToast(\"Error checking for existing active questionnaire\");\n    }\n  };\n\n  // Helper function to parse enabled cycles from string\n  const getEnabledCyclesFromString = enableCyclesString => {\n    if (!enableCyclesString) return [];\n    return enableCyclesString.split(\",\").map(c => c.trim()).filter(c => c !== \"\").map(c => parseInt(c)).filter(c => !isNaN(c));\n  };\n\n  // Helper function to perform direct publish/republish\n  const performDirectPublish = async questionnaire => {\n    const isRepublish = questionnaire.status === 1;\n\n    // Show appropriate confirmation message\n    const confirmMessage = isRepublish ? `Are you sure you want to republish \"${questionnaire.name}\"? This will update the published version with the current draft changes.` : `Are you sure you want to publish \"${questionnaire.name}\"? This will make it available for use.`;\n    const confirmHeader = isRepublish ? \"Confirm Republish\" : \"Confirm Publish\";\n    confirmDialog({\n      message: confirmMessage,\n      header: confirmHeader,\n      icon: \"pi pi-exclamation-triangle\",\n      accept: async () => {\n        try {\n          await questionnaireService.publishQuestionnaire(questionnaire.id);\n          const successMessage = isRepublish ? \"Questionnaire republished successfully\" : \"Questionnaire published successfully\";\n          messageService.successToast(successMessage);\n          loadQuestionnaires(Math.floor(first / rows), rows, globalFilter);\n        } catch (error) {\n          console.error(\"Error publishing questionnaire:\", error);\n          messageService.errorToast(error.message || \"Error publishing questionnaire\");\n        }\n      }\n    });\n  };\n\n  // Handler for when batch reopen popup completes\n  const handleRepublishConfirmed = async result => {\n    if (result.success && currentRepublishQuestionnaire) {\n      // Show success message for batch reopen operation\n      messageService.successToast(result.message);\n      try {\n        // Now proceed with the actual republish\n        await questionnaireService.publishQuestionnaire(currentRepublishQuestionnaire.id);\n        messageService.successToast(\"Questionnaire republished successfully\");\n        loadQuestionnaires(Math.floor(first / rows), rows, globalFilter);\n      } catch (error) {\n        console.error(\"Error during republish after batch reopen:\", error);\n        messageService.errorToast(\"Error occurred during republish process\");\n      }\n    }\n\n    // Clean up state\n    setCurrentRepublishQuestionnaire(null);\n  };\n\n  // Handler for batch reopen popup cancellation\n  const handleBatchReopenCancel = () => {\n    setShowBatchReopenPopup(false);\n    setCurrentRepublishQuestionnaire(null);\n  };\n  const handleUnpublishQuestionnaire = async questionnaire => {\n    try {\n      confirmDialog({\n        message: `Are you sure you want to unpublish \"${questionnaire.name}\"? This will change the status back to Draft.`,\n        header: \"Confirm Unpublish\",\n        icon: \"pi pi-exclamation-triangle\",\n        accept: async () => {\n          try {\n            await questionnaireService.unpublishQuestionnaire(questionnaire.id);\n            messageService.successToast(\"Questionnaire unpublished successfully\");\n            loadQuestionnaires(Math.floor(first / rows), rows, globalFilter);\n          } catch (error) {\n            console.error(\"Error unpublishing questionnaire:\", error);\n            messageService.errorToast(error.message || \"Error unpublishing questionnaire\");\n          }\n        }\n      });\n    } catch (error) {\n      console.error(\"Error confirming unpublish:\", error);\n      messageService.errorToast(\"Error confirming unpublish\");\n    }\n  };\n  const handleDeleteQuestionnaire = async questionnaire => {\n    try {\n      // First validate if the questionnaire can be deleted\n      const validation = await questionnaireService.validateQuestionnaireForDeletion(questionnaire.id);\n      if (!validation.canDelete) {\n        let warningMessage = \"\";\n        if (validation.isPublished) {\n          warningMessage = \"Cannot delete a published questionnaire. Published questionnaires cannot be deleted to maintain data integrity. Please archive it first if you want to disable it.\";\n        } else if (validation.hasFormReferences) {\n          warningMessage = `Cannot delete this questionnaire because it is referenced by ${validation.formReferenceCount} form(s). Please remove or reassign these forms first before deleting the questionnaire.`;\n        }\n        messageService.warnToast(warningMessage);\n        return;\n      }\n\n      // If validation passes, show confirmation dialog\n      confirmDialog({\n        message: `Are you sure you want to delete \"${questionnaire.name}\"? This action cannot be undone.`,\n        header: \"Confirm Delete\",\n        icon: \"pi pi-exclamation-triangle\",\n        acceptClassName: \"p-button-danger\",\n        accept: async () => {\n          try {\n            await questionnaireService.deleteQuestionnaire(questionnaire.id);\n            messageService.successToast(\"Questionnaire deleted successfully\");\n            loadQuestionnaires(Math.floor(first / rows), rows, globalFilter);\n          } catch (error) {\n            console.error(\"Error deleting questionnaire:\", error);\n            messageService.errorToast(error.message || \"Error deleting questionnaire\");\n          }\n        }\n      });\n    } catch (error) {\n      console.error(\"Error validating questionnaire for deletion:\", error);\n      messageService.errorToast(\"Error validating questionnaire for deletion\");\n    }\n  };\n  const statusBodyTemplate = rowData => {\n    const getStatusInfo = status => {\n      switch (status) {\n        case 0:\n          return {\n            label: \"Draft\",\n            severity: \"warning\"\n          };\n        case 1:\n          return {\n            label: \"Published\",\n            severity: \"success\"\n          };\n        case 2:\n          return {\n            label: \"Closed\",\n            severity: \"danger\"\n          };\n        default:\n          return {\n            label: \"Unknown\",\n            severity: \"secondary\"\n          };\n      }\n    };\n    const statusInfo = getStatusInfo(rowData.status);\n    return /*#__PURE__*/_jsxDEV(Tag, {\n      value: statusInfo.label,\n      severity: statusInfo.severity\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 369,\n      columnNumber: 12\n    }, this);\n  };\n  const dateBodyTemplate = rowData => {\n    if (!rowData.modifiedOn) return \"-\";\n    return new Date(rowData.modifiedOn).toLocaleDateString();\n  };\n  const enabledCyclesBodyTemplate = rowData => {\n    if (!rowData.enableCycles) {\n      return \"-\";\n    }\n\n    // Parse the comma-separated cycle values and display their names\n    const cycleValues = rowData.enableCycles.split(\",\").map(val => parseInt(val.trim()));\n    const cycleNames = cycleValues.map(cycle => getCycleDisplayName(cycle)).join(\", \");\n    return cycleNames;\n  };\n  const validationMessageBodyTemplate = rowData => {\n    if (!rowData.validationMessage) {\n      return null;\n    }\n\n    // Use Tag component similar to statusBodyTemplate for consistent styling\n    return /*#__PURE__*/_jsxDEV(Tag, {\n      value: rowData.validationMessage,\n      severity: \"warning\",\n      icon: \"pi pi-exclamation-triangle\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 394,\n      columnNumber: 12\n    }, this);\n  };\n  const actionsBodyTemplate = rowData => {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex gap-2\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        icon: \"pi pi-eye\",\n        className: \"p-button-rounded p-button-text\",\n        onClick: () => handleViewQuestionnaire(rowData),\n        tooltip: \"View\",\n        tooltipOptions: {\n          position: \"top\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 400,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        icon: \"pi pi-pencil\",\n        className: \"p-button-rounded p-button-text\",\n        onClick: () => handleEditQuestionnaire(rowData),\n        tooltip: \"Edit\",\n        tooltipOptions: {\n          position: \"top\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 407,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        icon: \"pi pi-copy\",\n        className: \"p-button-rounded p-button-text\",\n        onClick: () => handleCopyQuestionnaire(rowData),\n        tooltip: \"Copy\",\n        tooltipOptions: {\n          position: \"top\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 414,\n        columnNumber: 9\n      }, this), rowData.status === 0 && /*#__PURE__*/_jsxDEV(Button, {\n        icon: \"pi pi-trash\",\n        className: \"p-button-rounded p-button-text p-button-danger\",\n        onClick: () => handleDeleteQuestionnaire(rowData),\n        tooltip: \"Delete\",\n        tooltipOptions: {\n          position: \"top\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 440,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 399,\n      columnNumber: 7\n    }, this);\n  };\n  const header = /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"questionnaire-management-header\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"top-row\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"search-field\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"p-input-icon-left search-input-wrapper\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"pi pi-search\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 457,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(InputText, {\n            type: \"search\",\n            value: globalFilter,\n            onChange: e => onGlobalFilterChange(e.target.value),\n            placeholder: \"Search questionnaires...\",\n            className: \"search-input\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 458,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 456,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 455,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"filter-dropdowns\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 467,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"action-buttons\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          label: \"Create New Questionnaire\",\n          icon: \"pi pi-plus\",\n          className: \"p-button-red p-button-sm p-button-rounded\",\n          onClick: () => setShowCreateDialog(true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 469,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 468,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 454,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 453,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"questionnaire-management\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"banner\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"banner__site-title-area\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"page-title\",\n          children: \"Questionnaire Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 484,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 483,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 482,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-content-wrapper\",\n      children: [/*#__PURE__*/_jsxDEV(Toast, {\n        ref: toast\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 489,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ConfirmDialog, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 490,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DataTable, {\n        value: questionnaires,\n        loading: loading,\n        header: header,\n        emptyMessage: \"No questionnaires found\",\n        sortMode: \"multiple\",\n        paginator: true,\n        lazy: true,\n        rows: rows,\n        first: first,\n        totalRecords: totalRecords,\n        onPage: onPageChange,\n        rowsPerPageOptions: [5, 10, 25, 50],\n        className: \"p-datatable-gridlines\",\n        children: [/*#__PURE__*/_jsxDEV(Column, {\n          field: \"name\",\n          header: \"Name\",\n          sortable: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 507,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Column, {\n          field: \"year\",\n          header: \"Year\",\n          sortable: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 508,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Column, {\n          field: \"status\",\n          header: \"Status\",\n          body: statusBodyTemplate,\n          sortable: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 509,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Column, {\n          field: \"enableCycles\",\n          header: \"Enabled Cycles\",\n          body: enabledCyclesBodyTemplate,\n          sortable: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 510,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Column, {\n          field: \"validationMessage\",\n          header: \"Validation\",\n          body: validationMessageBodyTemplate,\n          style: {\n            width: \"400px\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 511,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Column, {\n          field: \"modifiedOn\",\n          header: \"Updated On\",\n          body: dateBodyTemplate,\n          sortable: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 512,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Column, {\n          field: \"modifiedByName\",\n          header: \"Updated By\",\n          sortable: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 513,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Column, {\n          header: \"Actions\",\n          body: actionsBodyTemplate,\n          style: {\n            width: \"220px\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 514,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 492,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        header: \"Create New Questionnaire\",\n        visible: showCreateDialog,\n        style: {\n          width: \"450px\"\n        },\n        onHide: () => {\n          setShowCreateDialog(false);\n          setNewQuestionnaire({\n            name: \"\",\n            year: currentYear\n          });\n        },\n        footer: /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            label: \"Cancel\",\n            icon: \"pi pi-times\",\n            onClick: () => {\n              setShowCreateDialog(false);\n              setNewQuestionnaire({\n                name: \"\",\n                year: currentYear\n              });\n            },\n            className: \"p-button-text\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 528,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            label: \"Create\",\n            icon: \"pi pi-check\",\n            onClick: handleCreateQuestionnaire,\n            className: \"action\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 537,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 527,\n          columnNumber: 13\n        }, this),\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"field\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"questionnaire-name\",\n            className: \"block\",\n            children: [\"Name \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-red-500\",\n              children: \"*\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 543,\n              columnNumber: 20\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 542,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(InputText, {\n            id: \"questionnaire-name\",\n            value: newQuestionnaire.name,\n            onChange: e => setNewQuestionnaire({\n              ...newQuestionnaire,\n              name: e.target.value\n            }),\n            placeholder: \"Enter questionnaire name\",\n            className: \"w-full\",\n            autoFocus: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 545,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 541,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"field\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"questionnaire-year\",\n            className: \"block\",\n            children: [\"Year \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-red-500\",\n              children: \"*\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 556,\n              columnNumber: 20\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 555,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n            id: \"questionnaire-year\",\n            value: newQuestionnaire.year,\n            options: yearOptions,\n            onChange: e => setNewQuestionnaire({\n              ...newQuestionnaire,\n              year: e.value\n            }),\n            placeholder: \"Select year\",\n            className: \"w-full\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 558,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 554,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 518,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CopyQuestionnaireModal, {\n        visible: showCopyDialog,\n        onHide: handleCopyCancel,\n        onCopy: handleCopyConfirm,\n        questionnaire: questionnaireToCopy,\n        loading: copyLoading\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 570,\n        columnNumber: 9\n      }, this), currentRepublishQuestionnaire && /*#__PURE__*/_jsxDEV(BatchReopenPopup, {\n        visible: showBatchReopenPopup,\n        onHide: handleBatchReopenCancel,\n        onComplete: handleRepublishConfirmed,\n        questionnaireId: currentRepublishQuestionnaire.id,\n        surveyJson: currentRepublishQuestionnaire.draftDefinitionJson ? JSON.parse(currentRepublishQuestionnaire.draftDefinitionJson) : {\n          title: currentRepublishQuestionnaire.name,\n          pages: []\n        },\n        latestEnabledCycle: getEnabledCyclesFromString(currentRepublishQuestionnaire.enableCycles).length > 0 ? Math.max(...getEnabledCyclesFromString(currentRepublishQuestionnaire.enableCycles)) : 0,\n        surveyName: currentRepublishQuestionnaire.name,\n        selectedYear: currentRepublishQuestionnaire.year,\n        enableCycles: currentRepublishQuestionnaire.enableCycles || \"\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 580,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 488,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 481,\n    columnNumber: 5\n  }, this);\n};\n_s(QuestionnaireManagement, \"FrmLuAPgDtNzUcqzDIDafJg2XkI=\", false, function () {\n  return [useNavigate];\n});\n_c = QuestionnaireManagement;\nvar _c;\n$RefreshReg$(_c, \"QuestionnaireManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useCallback", "useNavigate", "DataTable", "Column", "<PERSON><PERSON>", "Dialog", "InputText", "Dropdown", "Toast", "ConfirmDialog", "confirmDialog", "Tag", "questionnaireService", "messageService", "getCycleDisplayName", "CopyQuestionnaireModal", "BatchReopenPopup", "jsxDEV", "_jsxDEV", "QuestionnaireManagement", "_s", "navigate", "questionnaires", "setQuestionnaires", "loading", "setLoading", "showCreateDialog", "setShowCreateDialog", "showCopyDialog", "setShowCopyDialog", "questionnaireToCopy", "setQuestionnaireToCopy", "copyLoading", "setCopyLoading", "newQuestionnaire", "setNewQuestionnaire", "name", "year", "Date", "getFullYear", "globalFilter", "setGlobalFilter", "totalRecords", "setTotalRecords", "first", "<PERSON><PERSON><PERSON><PERSON>", "rows", "setRows", "showBatchReopenPopup", "setShowBatchReopenPopup", "currentRepublishQuestionnaire", "setCurrentRepublishQuestionnaire", "toast", "loadingRef", "currentYear", "yearOptions", "i", "push", "label", "toString", "value", "loadQuestionnaires", "pageIndex", "pageSize", "searchTerm", "current", "data", "searchQuestionnaires", "items", "totalCount", "error", "console", "errorToast", "onPageChange", "event", "newFirst", "newRows", "newPageIndex", "Math", "floor", "onGlobalFilterChange", "handleCreateQuestionnaire", "trim", "warnToast", "hasActiveQuestionnaire", "hasActiveQuestionnaireInYear", "questionnaireData", "status", "isActive", "draftDefinitionJson", "JSON", "stringify", "acknowledgement", "acknowledgementText", "generalComments", "generalCommentsText", "formSystemVersion", "createdQuestionnaire", "createQuestionnaire", "successToast", "id", "message", "handleEditQuestionnaire", "questionnaire", "handleViewQuestionnaire", "handleCopyQuestionnaire", "handleCopyConfirm", "sourceId", "targetYear", "copyQuestionnaire", "handleCopyCancel", "handlePublishQuestionnaire", "isRepublish", "hasActiveInYear", "enabledCycles", "getEnabledCyclesFromString", "enableCycles", "length", "performDirectPublish", "enableCyclesString", "split", "map", "c", "filter", "parseInt", "isNaN", "confirmMessage", "<PERSON><PERSON><PERSON><PERSON>", "header", "icon", "accept", "publishQuestionnaire", "successMessage", "handleRepublishConfirmed", "result", "success", "handleBatchReopenCancel", "handleUnpublishQuestionnaire", "unpublishQuestionnaire", "handleDeleteQuestionnaire", "validation", "validateQuestionnaireForDeletion", "canDelete", "warningMessage", "isPublished", "hasFormReferences", "formReferenceCount", "acceptClassName", "deleteQuestionnaire", "statusBodyTemplate", "rowData", "getStatusInfo", "severity", "statusInfo", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "dateBodyTemplate", "modifiedOn", "toLocaleDateString", "enabledCyclesBodyTemplate", "cycleValues", "val", "cycleNames", "cycle", "join", "validationMessageBodyTemplate", "validationMessage", "actionsBodyTemplate", "className", "children", "onClick", "tooltip", "tooltipOptions", "position", "type", "onChange", "e", "target", "placeholder", "ref", "emptyMessage", "sortMode", "paginator", "lazy", "onPage", "rowsPerPageOptions", "field", "sortable", "body", "style", "width", "visible", "onHide", "footer", "htmlFor", "autoFocus", "options", "onCopy", "onComplete", "questionnaireId", "surveyJson", "parse", "title", "pages", "latestEnabledCycle", "max", "surveyName", "selected<PERSON>ear", "_c", "$RefreshReg$"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/pages/admin/questionnaireManagement.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef, useCallback } from \"react\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { DataTable } from \"primereact/datatable\";\r\nimport { Column } from \"primereact/column\";\r\nimport { Button } from \"primereact/button\";\r\nimport { Dialog } from \"primereact/dialog\";\r\nimport { InputText } from \"primereact/inputtext\";\r\nimport { Dropdown } from \"primereact/dropdown\";\r\nimport { Toast } from \"primereact/toast\";\r\nimport { ConfirmDialog, confirmDialog } from \"primereact/confirmdialog\";\r\nimport { Tag } from \"primereact/tag\";\r\nimport questionnaireService from \"../../services/questionnaireService\";\r\nimport { messageService } from \"../../core/message/messageService\";\r\nimport { getCycleDisplayName } from \"../../core/enumertions/partnerPlanCycle\";\r\nimport CopyQuestionnaireModal from \"../../components/admin/CopyQuestionnaireModal\";\r\nimport BatchReopenPopup from \"../../components/admin/batchReopenPopup\";\r\n\r\nexport const QuestionnaireManagement = () => {\r\n  const navigate = useNavigate();\r\n  const [questionnaires, setQuestionnaires] = useState([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [showCreateDialog, setShowCreateDialog] = useState(false);\r\n  const [showCopyDialog, setShowCopyDialog] = useState(false);\r\n  const [questionnaireToCopy, setQuestionnaireToCopy] = useState(null);\r\n  const [copyLoading, setCopyLoading] = useState(false);\r\n  const [newQuestionnaire, setNewQuestionnaire] = useState({\r\n    name: \"\",\r\n    year: new Date().getFullYear(),\r\n  });\r\n  const [globalFilter, setGlobalFilter] = useState(\"\");\r\n  const [totalRecords, setTotalRecords] = useState(0);\r\n  const [first, setFirst] = useState(0);\r\n  const [rows, setRows] = useState(10);\r\n  const [showBatchReopenPopup, setShowBatchReopenPopup] = useState(false);\r\n  const [currentRepublishQuestionnaire, setCurrentRepublishQuestionnaire] = useState(null);\r\n  const toast = useRef(null);\r\n  const loadingRef = useRef(false);\r\n\r\n  // Year options for dropdown\r\n  const currentYear = new Date().getFullYear();\r\n  const yearOptions = [];\r\n  for (let i = currentYear - 5; i <= currentYear + 5; i++) {\r\n    yearOptions.push({ label: i.toString(), value: i });\r\n  }\r\n\r\n  const loadQuestionnaires = useCallback(\r\n    async (pageIndex = 0, pageSize = 10, searchTerm = \"\") => {\r\n      // Prevent duplicate calls if already loading\r\n      if (loading || loadingRef.current) {\r\n        return;\r\n      }\r\n\r\n      try {\r\n        setLoading(true);\r\n        loadingRef.current = true;\r\n\r\n        const data = await questionnaireService.searchQuestionnaires(\r\n          searchTerm,\r\n          null, // year filter\r\n          null, // status filter\r\n          null, // isActive filter\r\n          pageIndex,\r\n          pageSize\r\n        );\r\n\r\n        setQuestionnaires(data.items);\r\n        setTotalRecords(data.totalCount);\r\n      } catch (error) {\r\n        console.error(\"Error loading questionnaires:\", error);\r\n        messageService.errorToast(\"Error loading questionnaires\");\r\n        setQuestionnaires([]);\r\n        setTotalRecords(0);\r\n      } finally {\r\n        setLoading(false);\r\n        loadingRef.current = false;\r\n      }\r\n    },\r\n    [loading]\r\n  );\r\n\r\n  useEffect(() => {\r\n    loadQuestionnaires(0, rows, globalFilter);\r\n  }, []); // Only run on mount to prevent duplicate calls\r\n\r\n  const onPageChange = (event) => {\r\n    const newFirst = event.first;\r\n    const newRows = event.rows;\r\n    const newPageIndex = Math.floor(newFirst / newRows); // 0-based page index\r\n\r\n    setFirst(newFirst);\r\n    setRows(newRows);\r\n    loadQuestionnaires(newPageIndex, newRows, globalFilter);\r\n  };\r\n\r\n  const onGlobalFilterChange = (value) => {\r\n    setGlobalFilter(value);\r\n    setFirst(0); // Reset to first page when filtering\r\n    loadQuestionnaires(0, rows, value); // Start from page index 0\r\n  };\r\n\r\n  const handleCreateQuestionnaire = async () => {\r\n    if (!newQuestionnaire.name.trim()) {\r\n      messageService.warnToast(\"Please enter a questionnaire name\");\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // Check if there's already an active questionnaire in the selected year\r\n      const hasActiveQuestionnaire = await questionnaireService.hasActiveQuestionnaireInYear(newQuestionnaire.year);\r\n\r\n      if (hasActiveQuestionnaire) {\r\n        messageService.errorToast(\r\n          `${newQuestionnaire.year} year already has an active questionnaire. Only one active questionnaire is allowed per year.`\r\n        );\r\n        return;\r\n      }\r\n\r\n      const questionnaireData = {\r\n        name: newQuestionnaire.name.trim(),\r\n        year: newQuestionnaire.year,\r\n        status: 0, // Draft status\r\n        isActive: true,\r\n        draftDefinitionJson: JSON.stringify({}),\r\n        acknowledgement: false,\r\n        acknowledgementText: \"\",\r\n        generalComments: false,\r\n        generalCommentsText: \"\",\r\n        formSystemVersion: 1,\r\n      };\r\n\r\n      const createdQuestionnaire = await questionnaireService.createQuestionnaire(questionnaireData);\r\n\r\n      messageService.successToast(\"Questionnaire created successfully\");\r\n      setShowCreateDialog(false);\r\n      setNewQuestionnaire({ name: \"\", year: currentYear });\r\n      loadQuestionnaires(Math.floor(first / rows), rows, globalFilter);\r\n\r\n      // Navigate to edit the new questionnaire\r\n      navigate(`/admin/questionnaire-designer/${createdQuestionnaire.id}`);\r\n    } catch (error) {\r\n      console.error(\"Error creating questionnaire:\", error);\r\n      messageService.errorToast(error.message || \"Error creating questionnaire\");\r\n    }\r\n  };\r\n\r\n  const handleEditQuestionnaire = (questionnaire) => {\r\n    navigate(`/admin/questionnaire-designer/${questionnaire.id}`);\r\n  };\r\n\r\n  const handleViewQuestionnaire = (questionnaire) => {\r\n    navigate(`/admin/questionnaire-viewer/${questionnaire.id}`);\r\n  };\r\n\r\n  const handleCopyQuestionnaire = (questionnaire) => {\r\n    setQuestionnaireToCopy(questionnaire);\r\n    setShowCopyDialog(true);\r\n  };\r\n\r\n  const handleCopyConfirm = async (sourceId, targetYear) => {\r\n    setCopyLoading(true);\r\n    try {\r\n      await questionnaireService.copyQuestionnaire(sourceId, targetYear);\r\n      messageService.successToast(`Questionnaire successfully copied to year ${targetYear}`);\r\n      setShowCopyDialog(false);\r\n      setQuestionnaireToCopy(null);\r\n      // Refresh the questionnaire list\r\n      loadQuestionnaires(Math.floor(first / rows), rows, globalFilter);\r\n    } catch (error) {\r\n      console.error(\"Error copying questionnaire:\", error);\r\n      messageService.errorToast(error.message || \"Error copying questionnaire\");\r\n    } finally {\r\n      setCopyLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleCopyCancel = () => {\r\n    setShowCopyDialog(false);\r\n    setQuestionnaireToCopy(null);\r\n  };\r\n\r\n  const handlePublishQuestionnaire = async (questionnaire) => {\r\n    try {\r\n      const isRepublish = questionnaire.status === 1;\r\n\r\n      // Check if there's already another active questionnaire in the same year\r\n      // This applies to both first-time publishing and republishing\r\n      const hasActiveInYear = await questionnaireService.hasActiveQuestionnaireInYear(questionnaire.year, questionnaire.id);\r\n\r\n      if (hasActiveInYear) {\r\n        messageService.errorToast(\r\n          `${questionnaire.year} year already has an active questionnaire. Only one active questionnaire is allowed per year.`\r\n        );\r\n        return;\r\n      }\r\n\r\n      if (isRepublish) {\r\n        // Check if there are enabled cycles\r\n        const enabledCycles = getEnabledCyclesFromString(questionnaire.enableCycles);\r\n        if (enabledCycles.length > 0) {\r\n          // Store the questionnaire for the batch reopen popup\r\n          setCurrentRepublishQuestionnaire(questionnaire);\r\n          setShowBatchReopenPopup(true);\r\n          return;\r\n        } else {\r\n          // No enabled cycles, proceed with direct republish\r\n          await performDirectPublish(questionnaire);\r\n        }\r\n      } else {\r\n        // For first-time publish, proceed directly\r\n        await performDirectPublish(questionnaire);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error checking for active questionnaire:\", error);\r\n      messageService.errorToast(\"Error checking for existing active questionnaire\");\r\n    }\r\n  };\r\n\r\n  // Helper function to parse enabled cycles from string\r\n  const getEnabledCyclesFromString = (enableCyclesString) => {\r\n    if (!enableCyclesString) return [];\r\n    return enableCyclesString\r\n      .split(\",\")\r\n      .map((c) => c.trim())\r\n      .filter((c) => c !== \"\")\r\n      .map((c) => parseInt(c))\r\n      .filter((c) => !isNaN(c));\r\n  };\r\n\r\n  // Helper function to perform direct publish/republish\r\n  const performDirectPublish = async (questionnaire) => {\r\n    const isRepublish = questionnaire.status === 1;\r\n\r\n    // Show appropriate confirmation message\r\n    const confirmMessage = isRepublish\r\n      ? `Are you sure you want to republish \"${questionnaire.name}\"? This will update the published version with the current draft changes.`\r\n      : `Are you sure you want to publish \"${questionnaire.name}\"? This will make it available for use.`;\r\n\r\n    const confirmHeader = isRepublish ? \"Confirm Republish\" : \"Confirm Publish\";\r\n\r\n    confirmDialog({\r\n      message: confirmMessage,\r\n      header: confirmHeader,\r\n      icon: \"pi pi-exclamation-triangle\",\r\n      accept: async () => {\r\n        try {\r\n          await questionnaireService.publishQuestionnaire(questionnaire.id);\r\n\r\n          const successMessage = isRepublish ? \"Questionnaire republished successfully\" : \"Questionnaire published successfully\";\r\n\r\n          messageService.successToast(successMessage);\r\n          loadQuestionnaires(Math.floor(first / rows), rows, globalFilter);\r\n        } catch (error) {\r\n          console.error(\"Error publishing questionnaire:\", error);\r\n          messageService.errorToast(error.message || \"Error publishing questionnaire\");\r\n        }\r\n      },\r\n    });\r\n  };\r\n\r\n  // Handler for when batch reopen popup completes\r\n  const handleRepublishConfirmed = async (result) => {\r\n    if (result.success && currentRepublishQuestionnaire) {\r\n      // Show success message for batch reopen operation\r\n      messageService.successToast(result.message);\r\n\r\n      try {\r\n        // Now proceed with the actual republish\r\n        await questionnaireService.publishQuestionnaire(currentRepublishQuestionnaire.id);\r\n\r\n        messageService.successToast(\"Questionnaire republished successfully\");\r\n        loadQuestionnaires(Math.floor(first / rows), rows, globalFilter);\r\n      } catch (error) {\r\n        console.error(\"Error during republish after batch reopen:\", error);\r\n        messageService.errorToast(\"Error occurred during republish process\");\r\n      }\r\n    }\r\n\r\n    // Clean up state\r\n    setCurrentRepublishQuestionnaire(null);\r\n  };\r\n\r\n  // Handler for batch reopen popup cancellation\r\n  const handleBatchReopenCancel = () => {\r\n    setShowBatchReopenPopup(false);\r\n    setCurrentRepublishQuestionnaire(null);\r\n  };\r\n\r\n  const handleUnpublishQuestionnaire = async (questionnaire) => {\r\n    try {\r\n      confirmDialog({\r\n        message: `Are you sure you want to unpublish \"${questionnaire.name}\"? This will change the status back to Draft.`,\r\n        header: \"Confirm Unpublish\",\r\n        icon: \"pi pi-exclamation-triangle\",\r\n        accept: async () => {\r\n          try {\r\n            await questionnaireService.unpublishQuestionnaire(questionnaire.id);\r\n\r\n            messageService.successToast(\"Questionnaire unpublished successfully\");\r\n            loadQuestionnaires(Math.floor(first / rows), rows, globalFilter);\r\n          } catch (error) {\r\n            console.error(\"Error unpublishing questionnaire:\", error);\r\n            messageService.errorToast(error.message || \"Error unpublishing questionnaire\");\r\n          }\r\n        },\r\n      });\r\n    } catch (error) {\r\n      console.error(\"Error confirming unpublish:\", error);\r\n      messageService.errorToast(\"Error confirming unpublish\");\r\n    }\r\n  };\r\n\r\n  const handleDeleteQuestionnaire = async (questionnaire) => {\r\n    try {\r\n      // First validate if the questionnaire can be deleted\r\n      const validation = await questionnaireService.validateQuestionnaireForDeletion(questionnaire.id);\r\n\r\n      if (!validation.canDelete) {\r\n        let warningMessage = \"\";\r\n\r\n        if (validation.isPublished) {\r\n          warningMessage =\r\n            \"Cannot delete a published questionnaire. Published questionnaires cannot be deleted to maintain data integrity. Please archive it first if you want to disable it.\";\r\n        } else if (validation.hasFormReferences) {\r\n          warningMessage = `Cannot delete this questionnaire because it is referenced by ${validation.formReferenceCount} form(s). Please remove or reassign these forms first before deleting the questionnaire.`;\r\n        }\r\n\r\n        messageService.warnToast(warningMessage);\r\n        return;\r\n      }\r\n\r\n      // If validation passes, show confirmation dialog\r\n      confirmDialog({\r\n        message: `Are you sure you want to delete \"${questionnaire.name}\"? This action cannot be undone.`,\r\n        header: \"Confirm Delete\",\r\n        icon: \"pi pi-exclamation-triangle\",\r\n        acceptClassName: \"p-button-danger\",\r\n        accept: async () => {\r\n          try {\r\n            await questionnaireService.deleteQuestionnaire(questionnaire.id);\r\n            messageService.successToast(\"Questionnaire deleted successfully\");\r\n            loadQuestionnaires(Math.floor(first / rows), rows, globalFilter);\r\n          } catch (error) {\r\n            console.error(\"Error deleting questionnaire:\", error);\r\n            messageService.errorToast(error.message || \"Error deleting questionnaire\");\r\n          }\r\n        },\r\n      });\r\n    } catch (error) {\r\n      console.error(\"Error validating questionnaire for deletion:\", error);\r\n      messageService.errorToast(\"Error validating questionnaire for deletion\");\r\n    }\r\n  };\r\n\r\n  const statusBodyTemplate = (rowData) => {\r\n    const getStatusInfo = (status) => {\r\n      switch (status) {\r\n        case 0:\r\n          return { label: \"Draft\", severity: \"warning\" };\r\n        case 1:\r\n          return { label: \"Published\", severity: \"success\" };\r\n        case 2:\r\n          return { label: \"Closed\", severity: \"danger\" };\r\n        default:\r\n          return { label: \"Unknown\", severity: \"secondary\" };\r\n      }\r\n    };\r\n\r\n    const statusInfo = getStatusInfo(rowData.status);\r\n    return <Tag value={statusInfo.label} severity={statusInfo.severity} />;\r\n  };\r\n\r\n  const dateBodyTemplate = (rowData) => {\r\n    if (!rowData.modifiedOn) return \"-\";\r\n    return new Date(rowData.modifiedOn).toLocaleDateString();\r\n  };\r\n\r\n  const enabledCyclesBodyTemplate = (rowData) => {\r\n    if (!rowData.enableCycles) {\r\n      return \"-\";\r\n    }\r\n\r\n    // Parse the comma-separated cycle values and display their names\r\n    const cycleValues = rowData.enableCycles.split(\",\").map((val) => parseInt(val.trim()));\r\n    const cycleNames = cycleValues.map((cycle) => getCycleDisplayName(cycle)).join(\", \");\r\n    return cycleNames;\r\n  };\r\n\r\n  const validationMessageBodyTemplate = (rowData) => {\r\n    if (!rowData.validationMessage) {\r\n      return null;\r\n    }\r\n\r\n    // Use Tag component similar to statusBodyTemplate for consistent styling\r\n    return <Tag value={rowData.validationMessage} severity=\"warning\" icon=\"pi pi-exclamation-triangle\" />;\r\n  };\r\n\r\n  const actionsBodyTemplate = (rowData) => {\r\n    return (\r\n      <div className=\"flex gap-2\">\r\n        <Button\r\n          icon=\"pi pi-eye\"\r\n          className=\"p-button-rounded p-button-text\"\r\n          onClick={() => handleViewQuestionnaire(rowData)}\r\n          tooltip=\"View\"\r\n          tooltipOptions={{ position: \"top\" }}\r\n        />\r\n        <Button\r\n          icon=\"pi pi-pencil\"\r\n          className=\"p-button-rounded p-button-text\"\r\n          onClick={() => handleEditQuestionnaire(rowData)}\r\n          tooltip=\"Edit\"\r\n          tooltipOptions={{ position: \"top\" }}\r\n        />\r\n        <Button\r\n          icon=\"pi pi-copy\"\r\n          className=\"p-button-rounded p-button-text\"\r\n          onClick={() => handleCopyQuestionnaire(rowData)}\r\n          tooltip=\"Copy\"\r\n          tooltipOptions={{ position: \"top\" }}\r\n        />\r\n        {/* {(rowData.status === 0 || rowData.status === 1) && (\r\n          <Button\r\n            icon=\"pi pi-send\"\r\n            className=\"p-button-rounded p-button-text\"\r\n            onClick={() => handlePublishQuestionnaire(rowData)}\r\n            tooltip={rowData.status === 1 ? \"Republish\" : \"Publish\"}\r\n            tooltipOptions={{ position: \"top\" }}\r\n          />\r\n        )} */}\r\n        {/* {rowData.status === 1 && (\r\n          <Button\r\n            icon=\"pi pi-refresh\"\r\n            className=\"p-button-rounded p-button-text\"\r\n            onClick={() => handleUnpublishQuestionnaire(rowData)}\r\n            tooltip=\"Unpublish\"\r\n            tooltipOptions={{ position: \"top\" }}\r\n          />\r\n        )} */}\r\n        {rowData.status === 0 && (\r\n          <Button\r\n            icon=\"pi pi-trash\"\r\n            className=\"p-button-rounded p-button-text p-button-danger\"\r\n            onClick={() => handleDeleteQuestionnaire(rowData)}\r\n            tooltip=\"Delete\"\r\n            tooltipOptions={{ position: \"top\" }}\r\n          />\r\n        )}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  const header = (\r\n    <div className=\"questionnaire-management-header\">\r\n      <div className=\"top-row\">\r\n        <div className=\"search-field\">\r\n          <span className=\"p-input-icon-left search-input-wrapper\">\r\n            <i className=\"pi pi-search\" />\r\n            <InputText\r\n              type=\"search\"\r\n              value={globalFilter}\r\n              onChange={(e) => onGlobalFilterChange(e.target.value)}\r\n              placeholder=\"Search questionnaires...\"\r\n              className=\"search-input\"\r\n            />\r\n          </span>\r\n        </div>\r\n        <div className=\"filter-dropdowns\">{/* Add filter dropdowns here if needed in the future */}</div>\r\n        <div className=\"action-buttons\">\r\n          <Button\r\n            label=\"Create New Questionnaire\"\r\n            icon=\"pi pi-plus\"\r\n            className=\"p-button-red p-button-sm p-button-rounded\"\r\n            onClick={() => setShowCreateDialog(true)}\r\n          />\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  return (\r\n    <div className=\"questionnaire-management\">\r\n      <div className=\"banner\">\r\n        <div className=\"banner__site-title-area\">\r\n          <div className=\"page-title\">Questionnaire Management</div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"page-content-wrapper\">\r\n        <Toast ref={toast} />\r\n        <ConfirmDialog />\r\n\r\n        <DataTable\r\n          value={questionnaires}\r\n          loading={loading}\r\n          header={header}\r\n          emptyMessage=\"No questionnaires found\"\r\n          sortMode=\"multiple\"\r\n          paginator\r\n          lazy\r\n          rows={rows}\r\n          first={first}\r\n          totalRecords={totalRecords}\r\n          onPage={onPageChange}\r\n          rowsPerPageOptions={[5, 10, 25, 50]}\r\n          className=\"p-datatable-gridlines\"\r\n        >\r\n          <Column field=\"name\" header=\"Name\" sortable />\r\n          <Column field=\"year\" header=\"Year\" sortable />\r\n          <Column field=\"status\" header=\"Status\" body={statusBodyTemplate} sortable />\r\n          <Column field=\"enableCycles\" header=\"Enabled Cycles\" body={enabledCyclesBodyTemplate} sortable />\r\n          <Column field=\"validationMessage\" header=\"Validation\" body={validationMessageBodyTemplate} style={{ width: \"400px\" }} />\r\n          <Column field=\"modifiedOn\" header=\"Updated On\" body={dateBodyTemplate} sortable />\r\n          <Column field=\"modifiedByName\" header=\"Updated By\" sortable />\r\n          <Column header=\"Actions\" body={actionsBodyTemplate} style={{ width: \"220px\" }} />\r\n        </DataTable>\r\n\r\n        {/* Create New Questionnaire Dialog */}\r\n        <Dialog\r\n          header=\"Create New Questionnaire\"\r\n          visible={showCreateDialog}\r\n          style={{ width: \"450px\" }}\r\n          onHide={() => {\r\n            setShowCreateDialog(false);\r\n            setNewQuestionnaire({ name: \"\", year: currentYear });\r\n          }}\r\n          footer={\r\n            <div>\r\n              <Button\r\n                label=\"Cancel\"\r\n                icon=\"pi pi-times\"\r\n                onClick={() => {\r\n                  setShowCreateDialog(false);\r\n                  setNewQuestionnaire({ name: \"\", year: currentYear });\r\n                }}\r\n                className=\"p-button-text\"\r\n              />\r\n              <Button label=\"Create\" icon=\"pi pi-check\" onClick={handleCreateQuestionnaire} className=\"action\" />\r\n            </div>\r\n          }\r\n        >\r\n          <div className=\"field\">\r\n            <label htmlFor=\"questionnaire-name\" className=\"block\">\r\n              Name <span className=\"text-red-500\">*</span>\r\n            </label>\r\n            <InputText\r\n              id=\"questionnaire-name\"\r\n              value={newQuestionnaire.name}\r\n              onChange={(e) => setNewQuestionnaire({ ...newQuestionnaire, name: e.target.value })}\r\n              placeholder=\"Enter questionnaire name\"\r\n              className=\"w-full\"\r\n              autoFocus\r\n            />\r\n          </div>\r\n          <div className=\"field\">\r\n            <label htmlFor=\"questionnaire-year\" className=\"block\">\r\n              Year <span className=\"text-red-500\">*</span>\r\n            </label>\r\n            <Dropdown\r\n              id=\"questionnaire-year\"\r\n              value={newQuestionnaire.year}\r\n              options={yearOptions}\r\n              onChange={(e) => setNewQuestionnaire({ ...newQuestionnaire, year: e.value })}\r\n              placeholder=\"Select year\"\r\n              className=\"w-full\"\r\n            />\r\n          </div>\r\n        </Dialog>\r\n\r\n        {/* Copy Questionnaire Modal */}\r\n        <CopyQuestionnaireModal\r\n          visible={showCopyDialog}\r\n          onHide={handleCopyCancel}\r\n          onCopy={handleCopyConfirm}\r\n          questionnaire={questionnaireToCopy}\r\n          loading={copyLoading}\r\n        />\r\n\r\n        {/* Batch Reopen Popup for republish */}\r\n        {currentRepublishQuestionnaire && (\r\n          <BatchReopenPopup\r\n            visible={showBatchReopenPopup}\r\n            onHide={handleBatchReopenCancel}\r\n            onComplete={handleRepublishConfirmed}\r\n            questionnaireId={currentRepublishQuestionnaire.id}\r\n            surveyJson={\r\n              currentRepublishQuestionnaire.draftDefinitionJson\r\n                ? JSON.parse(currentRepublishQuestionnaire.draftDefinitionJson)\r\n                : { title: currentRepublishQuestionnaire.name, pages: [] }\r\n            }\r\n            latestEnabledCycle={\r\n              getEnabledCyclesFromString(currentRepublishQuestionnaire.enableCycles).length > 0\r\n                ? Math.max(...getEnabledCyclesFromString(currentRepublishQuestionnaire.enableCycles))\r\n                : 0\r\n            }\r\n            surveyName={currentRepublishQuestionnaire.name}\r\n            selectedYear={currentRepublishQuestionnaire.year}\r\n            enableCycles={currentRepublishQuestionnaire.enableCycles || \"\"}\r\n          />\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AACvE,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,aAAa,EAAEC,aAAa,QAAQ,0BAA0B;AACvE,SAASC,GAAG,QAAQ,gBAAgB;AACpC,OAAOC,oBAAoB,MAAM,qCAAqC;AACtE,SAASC,cAAc,QAAQ,mCAAmC;AAClE,SAASC,mBAAmB,QAAQ,yCAAyC;AAC7E,OAAOC,sBAAsB,MAAM,+CAA+C;AAClF,OAAOC,gBAAgB,MAAM,yCAAyC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvE,OAAO,MAAMC,uBAAuB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3C,MAAMC,QAAQ,GAAGpB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACqB,cAAc,EAAEC,iBAAiB,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC2B,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC6B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC+B,cAAc,EAAEC,iBAAiB,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACiC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EACpE,MAAM,CAACmC,WAAW,EAAEC,cAAc,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACqC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtC,QAAQ,CAAC;IACvDuC,IAAI,EAAE,EAAE;IACRC,IAAI,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;EAC/B,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC6C,YAAY,EAAEC,eAAe,CAAC,GAAG9C,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM,CAAC+C,KAAK,EAAEC,QAAQ,CAAC,GAAGhD,QAAQ,CAAC,CAAC,CAAC;EACrC,MAAM,CAACiD,IAAI,EAAEC,OAAO,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACmD,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACqD,6BAA6B,EAAEC,gCAAgC,CAAC,GAAGtD,QAAQ,CAAC,IAAI,CAAC;EACxF,MAAMuD,KAAK,GAAGrD,MAAM,CAAC,IAAI,CAAC;EAC1B,MAAMsD,UAAU,GAAGtD,MAAM,CAAC,KAAK,CAAC;;EAEhC;EACA,MAAMuD,WAAW,GAAG,IAAIhB,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;EAC5C,MAAMgB,WAAW,GAAG,EAAE;EACtB,KAAK,IAAIC,CAAC,GAAGF,WAAW,GAAG,CAAC,EAAEE,CAAC,IAAIF,WAAW,GAAG,CAAC,EAAEE,CAAC,EAAE,EAAE;IACvDD,WAAW,CAACE,IAAI,CAAC;MAAEC,KAAK,EAAEF,CAAC,CAACG,QAAQ,CAAC,CAAC;MAAEC,KAAK,EAAEJ;IAAE,CAAC,CAAC;EACrD;EAEA,MAAMK,kBAAkB,GAAG7D,WAAW,CACpC,OAAO8D,SAAS,GAAG,CAAC,EAAEC,QAAQ,GAAG,EAAE,EAAEC,UAAU,GAAG,EAAE,KAAK;IACvD;IACA,IAAIxC,OAAO,IAAI6B,UAAU,CAACY,OAAO,EAAE;MACjC;IACF;IAEA,IAAI;MACFxC,UAAU,CAAC,IAAI,CAAC;MAChB4B,UAAU,CAACY,OAAO,GAAG,IAAI;MAEzB,MAAMC,IAAI,GAAG,MAAMtD,oBAAoB,CAACuD,oBAAoB,CAC1DH,UAAU,EACV,IAAI;MAAE;MACN,IAAI;MAAE;MACN,IAAI;MAAE;MACNF,SAAS,EACTC,QACF,CAAC;MAEDxC,iBAAiB,CAAC2C,IAAI,CAACE,KAAK,CAAC;MAC7BzB,eAAe,CAACuB,IAAI,CAACG,UAAU,CAAC;IAClC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrDzD,cAAc,CAAC2D,UAAU,CAAC,8BAA8B,CAAC;MACzDjD,iBAAiB,CAAC,EAAE,CAAC;MACrBoB,eAAe,CAAC,CAAC,CAAC;IACpB,CAAC,SAAS;MACRlB,UAAU,CAAC,KAAK,CAAC;MACjB4B,UAAU,CAACY,OAAO,GAAG,KAAK;IAC5B;EACF,CAAC,EACD,CAACzC,OAAO,CACV,CAAC;EAED1B,SAAS,CAAC,MAAM;IACd+D,kBAAkB,CAAC,CAAC,EAAEf,IAAI,EAAEN,YAAY,CAAC;EAC3C,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER,MAAMiC,YAAY,GAAIC,KAAK,IAAK;IAC9B,MAAMC,QAAQ,GAAGD,KAAK,CAAC9B,KAAK;IAC5B,MAAMgC,OAAO,GAAGF,KAAK,CAAC5B,IAAI;IAC1B,MAAM+B,YAAY,GAAGC,IAAI,CAACC,KAAK,CAACJ,QAAQ,GAAGC,OAAO,CAAC,CAAC,CAAC;;IAErD/B,QAAQ,CAAC8B,QAAQ,CAAC;IAClB5B,OAAO,CAAC6B,OAAO,CAAC;IAChBf,kBAAkB,CAACgB,YAAY,EAAED,OAAO,EAAEpC,YAAY,CAAC;EACzD,CAAC;EAED,MAAMwC,oBAAoB,GAAIpB,KAAK,IAAK;IACtCnB,eAAe,CAACmB,KAAK,CAAC;IACtBf,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;IACbgB,kBAAkB,CAAC,CAAC,EAAEf,IAAI,EAAEc,KAAK,CAAC,CAAC,CAAC;EACtC,CAAC;EAED,MAAMqB,yBAAyB,GAAG,MAAAA,CAAA,KAAY;IAC5C,IAAI,CAAC/C,gBAAgB,CAACE,IAAI,CAAC8C,IAAI,CAAC,CAAC,EAAE;MACjCrE,cAAc,CAACsE,SAAS,CAAC,mCAAmC,CAAC;MAC7D;IACF;IAEA,IAAI;MACF;MACA,MAAMC,sBAAsB,GAAG,MAAMxE,oBAAoB,CAACyE,4BAA4B,CAACnD,gBAAgB,CAACG,IAAI,CAAC;MAE7G,IAAI+C,sBAAsB,EAAE;QAC1BvE,cAAc,CAAC2D,UAAU,CACvB,GAAGtC,gBAAgB,CAACG,IAAI,+FAC1B,CAAC;QACD;MACF;MAEA,MAAMiD,iBAAiB,GAAG;QACxBlD,IAAI,EAAEF,gBAAgB,CAACE,IAAI,CAAC8C,IAAI,CAAC,CAAC;QAClC7C,IAAI,EAAEH,gBAAgB,CAACG,IAAI;QAC3BkD,MAAM,EAAE,CAAC;QAAE;QACXC,QAAQ,EAAE,IAAI;QACdC,mBAAmB,EAAEC,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC,CAAC;QACvCC,eAAe,EAAE,KAAK;QACtBC,mBAAmB,EAAE,EAAE;QACvBC,eAAe,EAAE,KAAK;QACtBC,mBAAmB,EAAE,EAAE;QACvBC,iBAAiB,EAAE;MACrB,CAAC;MAED,MAAMC,oBAAoB,GAAG,MAAMrF,oBAAoB,CAACsF,mBAAmB,CAACZ,iBAAiB,CAAC;MAE9FzE,cAAc,CAACsF,YAAY,CAAC,oCAAoC,CAAC;MACjExE,mBAAmB,CAAC,KAAK,CAAC;MAC1BQ,mBAAmB,CAAC;QAAEC,IAAI,EAAE,EAAE;QAAEC,IAAI,EAAEiB;MAAY,CAAC,CAAC;MACpDO,kBAAkB,CAACiB,IAAI,CAACC,KAAK,CAACnC,KAAK,GAAGE,IAAI,CAAC,EAAEA,IAAI,EAAEN,YAAY,CAAC;;MAEhE;MACAnB,QAAQ,CAAC,iCAAiC4E,oBAAoB,CAACG,EAAE,EAAE,CAAC;IACtE,CAAC,CAAC,OAAO9B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrDzD,cAAc,CAAC2D,UAAU,CAACF,KAAK,CAAC+B,OAAO,IAAI,8BAA8B,CAAC;IAC5E;EACF,CAAC;EAED,MAAMC,uBAAuB,GAAIC,aAAa,IAAK;IACjDlF,QAAQ,CAAC,iCAAiCkF,aAAa,CAACH,EAAE,EAAE,CAAC;EAC/D,CAAC;EAED,MAAMI,uBAAuB,GAAID,aAAa,IAAK;IACjDlF,QAAQ,CAAC,+BAA+BkF,aAAa,CAACH,EAAE,EAAE,CAAC;EAC7D,CAAC;EAED,MAAMK,uBAAuB,GAAIF,aAAa,IAAK;IACjDxE,sBAAsB,CAACwE,aAAa,CAAC;IACrC1E,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAM6E,iBAAiB,GAAG,MAAAA,CAAOC,QAAQ,EAAEC,UAAU,KAAK;IACxD3E,cAAc,CAAC,IAAI,CAAC;IACpB,IAAI;MACF,MAAMrB,oBAAoB,CAACiG,iBAAiB,CAACF,QAAQ,EAAEC,UAAU,CAAC;MAClE/F,cAAc,CAACsF,YAAY,CAAC,6CAA6CS,UAAU,EAAE,CAAC;MACtF/E,iBAAiB,CAAC,KAAK,CAAC;MACxBE,sBAAsB,CAAC,IAAI,CAAC;MAC5B;MACA8B,kBAAkB,CAACiB,IAAI,CAACC,KAAK,CAACnC,KAAK,GAAGE,IAAI,CAAC,EAAEA,IAAI,EAAEN,YAAY,CAAC;IAClE,CAAC,CAAC,OAAO8B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDzD,cAAc,CAAC2D,UAAU,CAACF,KAAK,CAAC+B,OAAO,IAAI,6BAA6B,CAAC;IAC3E,CAAC,SAAS;MACRpE,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAM6E,gBAAgB,GAAGA,CAAA,KAAM;IAC7BjF,iBAAiB,CAAC,KAAK,CAAC;IACxBE,sBAAsB,CAAC,IAAI,CAAC;EAC9B,CAAC;EAED,MAAMgF,0BAA0B,GAAG,MAAOR,aAAa,IAAK;IAC1D,IAAI;MACF,MAAMS,WAAW,GAAGT,aAAa,CAAChB,MAAM,KAAK,CAAC;;MAE9C;MACA;MACA,MAAM0B,eAAe,GAAG,MAAMrG,oBAAoB,CAACyE,4BAA4B,CAACkB,aAAa,CAAClE,IAAI,EAAEkE,aAAa,CAACH,EAAE,CAAC;MAErH,IAAIa,eAAe,EAAE;QACnBpG,cAAc,CAAC2D,UAAU,CACvB,GAAG+B,aAAa,CAAClE,IAAI,+FACvB,CAAC;QACD;MACF;MAEA,IAAI2E,WAAW,EAAE;QACf;QACA,MAAME,aAAa,GAAGC,0BAA0B,CAACZ,aAAa,CAACa,YAAY,CAAC;QAC5E,IAAIF,aAAa,CAACG,MAAM,GAAG,CAAC,EAAE;UAC5B;UACAlE,gCAAgC,CAACoD,aAAa,CAAC;UAC/CtD,uBAAuB,CAAC,IAAI,CAAC;UAC7B;QACF,CAAC,MAAM;UACL;UACA,MAAMqE,oBAAoB,CAACf,aAAa,CAAC;QAC3C;MACF,CAAC,MAAM;QACL;QACA,MAAMe,oBAAoB,CAACf,aAAa,CAAC;MAC3C;IACF,CAAC,CAAC,OAAOjC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChEzD,cAAc,CAAC2D,UAAU,CAAC,kDAAkD,CAAC;IAC/E;EACF,CAAC;;EAED;EACA,MAAM2C,0BAA0B,GAAII,kBAAkB,IAAK;IACzD,IAAI,CAACA,kBAAkB,EAAE,OAAO,EAAE;IAClC,OAAOA,kBAAkB,CACtBC,KAAK,CAAC,GAAG,CAAC,CACVC,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACxC,IAAI,CAAC,CAAC,CAAC,CACpByC,MAAM,CAAED,CAAC,IAAKA,CAAC,KAAK,EAAE,CAAC,CACvBD,GAAG,CAAEC,CAAC,IAAKE,QAAQ,CAACF,CAAC,CAAC,CAAC,CACvBC,MAAM,CAAED,CAAC,IAAK,CAACG,KAAK,CAACH,CAAC,CAAC,CAAC;EAC7B,CAAC;;EAED;EACA,MAAMJ,oBAAoB,GAAG,MAAOf,aAAa,IAAK;IACpD,MAAMS,WAAW,GAAGT,aAAa,CAAChB,MAAM,KAAK,CAAC;;IAE9C;IACA,MAAMuC,cAAc,GAAGd,WAAW,GAC9B,uCAAuCT,aAAa,CAACnE,IAAI,2EAA2E,GACpI,qCAAqCmE,aAAa,CAACnE,IAAI,yCAAyC;IAEpG,MAAM2F,aAAa,GAAGf,WAAW,GAAG,mBAAmB,GAAG,iBAAiB;IAE3EtG,aAAa,CAAC;MACZ2F,OAAO,EAAEyB,cAAc;MACvBE,MAAM,EAAED,aAAa;MACrBE,IAAI,EAAE,4BAA4B;MAClCC,MAAM,EAAE,MAAAA,CAAA,KAAY;QAClB,IAAI;UACF,MAAMtH,oBAAoB,CAACuH,oBAAoB,CAAC5B,aAAa,CAACH,EAAE,CAAC;UAEjE,MAAMgC,cAAc,GAAGpB,WAAW,GAAG,wCAAwC,GAAG,sCAAsC;UAEtHnG,cAAc,CAACsF,YAAY,CAACiC,cAAc,CAAC;UAC3CvE,kBAAkB,CAACiB,IAAI,CAACC,KAAK,CAACnC,KAAK,GAAGE,IAAI,CAAC,EAAEA,IAAI,EAAEN,YAAY,CAAC;QAClE,CAAC,CAAC,OAAO8B,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;UACvDzD,cAAc,CAAC2D,UAAU,CAACF,KAAK,CAAC+B,OAAO,IAAI,gCAAgC,CAAC;QAC9E;MACF;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMgC,wBAAwB,GAAG,MAAOC,MAAM,IAAK;IACjD,IAAIA,MAAM,CAACC,OAAO,IAAIrF,6BAA6B,EAAE;MACnD;MACArC,cAAc,CAACsF,YAAY,CAACmC,MAAM,CAACjC,OAAO,CAAC;MAE3C,IAAI;QACF;QACA,MAAMzF,oBAAoB,CAACuH,oBAAoB,CAACjF,6BAA6B,CAACkD,EAAE,CAAC;QAEjFvF,cAAc,CAACsF,YAAY,CAAC,wCAAwC,CAAC;QACrEtC,kBAAkB,CAACiB,IAAI,CAACC,KAAK,CAACnC,KAAK,GAAGE,IAAI,CAAC,EAAEA,IAAI,EAAEN,YAAY,CAAC;MAClE,CAAC,CAAC,OAAO8B,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;QAClEzD,cAAc,CAAC2D,UAAU,CAAC,yCAAyC,CAAC;MACtE;IACF;;IAEA;IACArB,gCAAgC,CAAC,IAAI,CAAC;EACxC,CAAC;;EAED;EACA,MAAMqF,uBAAuB,GAAGA,CAAA,KAAM;IACpCvF,uBAAuB,CAAC,KAAK,CAAC;IAC9BE,gCAAgC,CAAC,IAAI,CAAC;EACxC,CAAC;EAED,MAAMsF,4BAA4B,GAAG,MAAOlC,aAAa,IAAK;IAC5D,IAAI;MACF7F,aAAa,CAAC;QACZ2F,OAAO,EAAE,uCAAuCE,aAAa,CAACnE,IAAI,+CAA+C;QACjH4F,MAAM,EAAE,mBAAmB;QAC3BC,IAAI,EAAE,4BAA4B;QAClCC,MAAM,EAAE,MAAAA,CAAA,KAAY;UAClB,IAAI;YACF,MAAMtH,oBAAoB,CAAC8H,sBAAsB,CAACnC,aAAa,CAACH,EAAE,CAAC;YAEnEvF,cAAc,CAACsF,YAAY,CAAC,wCAAwC,CAAC;YACrEtC,kBAAkB,CAACiB,IAAI,CAACC,KAAK,CAACnC,KAAK,GAAGE,IAAI,CAAC,EAAEA,IAAI,EAAEN,YAAY,CAAC;UAClE,CAAC,CAAC,OAAO8B,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;YACzDzD,cAAc,CAAC2D,UAAU,CAACF,KAAK,CAAC+B,OAAO,IAAI,kCAAkC,CAAC;UAChF;QACF;MACF,CAAC,CAAC;IACJ,CAAC,CAAC,OAAO/B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnDzD,cAAc,CAAC2D,UAAU,CAAC,4BAA4B,CAAC;IACzD;EACF,CAAC;EAED,MAAMmE,yBAAyB,GAAG,MAAOpC,aAAa,IAAK;IACzD,IAAI;MACF;MACA,MAAMqC,UAAU,GAAG,MAAMhI,oBAAoB,CAACiI,gCAAgC,CAACtC,aAAa,CAACH,EAAE,CAAC;MAEhG,IAAI,CAACwC,UAAU,CAACE,SAAS,EAAE;QACzB,IAAIC,cAAc,GAAG,EAAE;QAEvB,IAAIH,UAAU,CAACI,WAAW,EAAE;UAC1BD,cAAc,GACZ,oKAAoK;QACxK,CAAC,MAAM,IAAIH,UAAU,CAACK,iBAAiB,EAAE;UACvCF,cAAc,GAAG,gEAAgEH,UAAU,CAACM,kBAAkB,0FAA0F;QAC1M;QAEArI,cAAc,CAACsE,SAAS,CAAC4D,cAAc,CAAC;QACxC;MACF;;MAEA;MACArI,aAAa,CAAC;QACZ2F,OAAO,EAAE,oCAAoCE,aAAa,CAACnE,IAAI,kCAAkC;QACjG4F,MAAM,EAAE,gBAAgB;QACxBC,IAAI,EAAE,4BAA4B;QAClCkB,eAAe,EAAE,iBAAiB;QAClCjB,MAAM,EAAE,MAAAA,CAAA,KAAY;UAClB,IAAI;YACF,MAAMtH,oBAAoB,CAACwI,mBAAmB,CAAC7C,aAAa,CAACH,EAAE,CAAC;YAChEvF,cAAc,CAACsF,YAAY,CAAC,oCAAoC,CAAC;YACjEtC,kBAAkB,CAACiB,IAAI,CAACC,KAAK,CAACnC,KAAK,GAAGE,IAAI,CAAC,EAAEA,IAAI,EAAEN,YAAY,CAAC;UAClE,CAAC,CAAC,OAAO8B,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;YACrDzD,cAAc,CAAC2D,UAAU,CAACF,KAAK,CAAC+B,OAAO,IAAI,8BAA8B,CAAC;UAC5E;QACF;MACF,CAAC,CAAC;IACJ,CAAC,CAAC,OAAO/B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8CAA8C,EAAEA,KAAK,CAAC;MACpEzD,cAAc,CAAC2D,UAAU,CAAC,6CAA6C,CAAC;IAC1E;EACF,CAAC;EAED,MAAM6E,kBAAkB,GAAIC,OAAO,IAAK;IACtC,MAAMC,aAAa,GAAIhE,MAAM,IAAK;MAChC,QAAQA,MAAM;QACZ,KAAK,CAAC;UACJ,OAAO;YAAE7B,KAAK,EAAE,OAAO;YAAE8F,QAAQ,EAAE;UAAU,CAAC;QAChD,KAAK,CAAC;UACJ,OAAO;YAAE9F,KAAK,EAAE,WAAW;YAAE8F,QAAQ,EAAE;UAAU,CAAC;QACpD,KAAK,CAAC;UACJ,OAAO;YAAE9F,KAAK,EAAE,QAAQ;YAAE8F,QAAQ,EAAE;UAAS,CAAC;QAChD;UACE,OAAO;YAAE9F,KAAK,EAAE,SAAS;YAAE8F,QAAQ,EAAE;UAAY,CAAC;MACtD;IACF,CAAC;IAED,MAAMC,UAAU,GAAGF,aAAa,CAACD,OAAO,CAAC/D,MAAM,CAAC;IAChD,oBAAOrE,OAAA,CAACP,GAAG;MAACiD,KAAK,EAAE6F,UAAU,CAAC/F,KAAM;MAAC8F,QAAQ,EAAEC,UAAU,CAACD;IAAS;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACxE,CAAC;EAED,MAAMC,gBAAgB,GAAIR,OAAO,IAAK;IACpC,IAAI,CAACA,OAAO,CAACS,UAAU,EAAE,OAAO,GAAG;IACnC,OAAO,IAAIzH,IAAI,CAACgH,OAAO,CAACS,UAAU,CAAC,CAACC,kBAAkB,CAAC,CAAC;EAC1D,CAAC;EAED,MAAMC,yBAAyB,GAAIX,OAAO,IAAK;IAC7C,IAAI,CAACA,OAAO,CAAClC,YAAY,EAAE;MACzB,OAAO,GAAG;IACZ;;IAEA;IACA,MAAM8C,WAAW,GAAGZ,OAAO,CAAClC,YAAY,CAACI,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAE0C,GAAG,IAAKvC,QAAQ,CAACuC,GAAG,CAACjF,IAAI,CAAC,CAAC,CAAC,CAAC;IACtF,MAAMkF,UAAU,GAAGF,WAAW,CAACzC,GAAG,CAAE4C,KAAK,IAAKvJ,mBAAmB,CAACuJ,KAAK,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;IACpF,OAAOF,UAAU;EACnB,CAAC;EAED,MAAMG,6BAA6B,GAAIjB,OAAO,IAAK;IACjD,IAAI,CAACA,OAAO,CAACkB,iBAAiB,EAAE;MAC9B,OAAO,IAAI;IACb;;IAEA;IACA,oBAAOtJ,OAAA,CAACP,GAAG;MAACiD,KAAK,EAAE0F,OAAO,CAACkB,iBAAkB;MAAChB,QAAQ,EAAC,SAAS;MAACvB,IAAI,EAAC;IAA4B;MAAAyB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACvG,CAAC;EAED,MAAMY,mBAAmB,GAAInB,OAAO,IAAK;IACvC,oBACEpI,OAAA;MAAKwJ,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzBzJ,OAAA,CAACd,MAAM;QACL6H,IAAI,EAAC,WAAW;QAChByC,SAAS,EAAC,gCAAgC;QAC1CE,OAAO,EAAEA,CAAA,KAAMpE,uBAAuB,CAAC8C,OAAO,CAAE;QAChDuB,OAAO,EAAC,MAAM;QACdC,cAAc,EAAE;UAAEC,QAAQ,EAAE;QAAM;MAAE;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC,CAAC,eACF3I,OAAA,CAACd,MAAM;QACL6H,IAAI,EAAC,cAAc;QACnByC,SAAS,EAAC,gCAAgC;QAC1CE,OAAO,EAAEA,CAAA,KAAMtE,uBAAuB,CAACgD,OAAO,CAAE;QAChDuB,OAAO,EAAC,MAAM;QACdC,cAAc,EAAE;UAAEC,QAAQ,EAAE;QAAM;MAAE;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC,CAAC,eACF3I,OAAA,CAACd,MAAM;QACL6H,IAAI,EAAC,YAAY;QACjByC,SAAS,EAAC,gCAAgC;QAC1CE,OAAO,EAAEA,CAAA,KAAMnE,uBAAuB,CAAC6C,OAAO,CAAE;QAChDuB,OAAO,EAAC,MAAM;QACdC,cAAc,EAAE;UAAEC,QAAQ,EAAE;QAAM;MAAE;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC,CAAC,EAmBDP,OAAO,CAAC/D,MAAM,KAAK,CAAC,iBACnBrE,OAAA,CAACd,MAAM;QACL6H,IAAI,EAAC,aAAa;QAClByC,SAAS,EAAC,gDAAgD;QAC1DE,OAAO,EAAEA,CAAA,KAAMjC,yBAAyB,CAACW,OAAO,CAAE;QAClDuB,OAAO,EAAC,QAAQ;QAChBC,cAAc,EAAE;UAAEC,QAAQ,EAAE;QAAM;MAAE;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEV,CAAC;EAED,MAAM7B,MAAM,gBACV9G,OAAA;IAAKwJ,SAAS,EAAC,iCAAiC;IAAAC,QAAA,eAC9CzJ,OAAA;MAAKwJ,SAAS,EAAC,SAAS;MAAAC,QAAA,gBACtBzJ,OAAA;QAAKwJ,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC3BzJ,OAAA;UAAMwJ,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACtDzJ,OAAA;YAAGwJ,SAAS,EAAC;UAAc;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9B3I,OAAA,CAACZ,SAAS;YACR0K,IAAI,EAAC,QAAQ;YACbpH,KAAK,EAAEpB,YAAa;YACpByI,QAAQ,EAAGC,CAAC,IAAKlG,oBAAoB,CAACkG,CAAC,CAACC,MAAM,CAACvH,KAAK,CAAE;YACtDwH,WAAW,EAAC,0BAA0B;YACtCV,SAAS,EAAC;UAAc;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN3I,OAAA;QAAKwJ,SAAS,EAAC;MAAkB;QAAAhB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAA+D,CAAC,eACjG3I,OAAA;QAAKwJ,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAC7BzJ,OAAA,CAACd,MAAM;UACLsD,KAAK,EAAC,0BAA0B;UAChCuE,IAAI,EAAC,YAAY;UACjByC,SAAS,EAAC,2CAA2C;UACrDE,OAAO,EAAEA,CAAA,KAAMjJ,mBAAmB,CAAC,IAAI;QAAE;UAAA+H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,oBACE3I,OAAA;IAAKwJ,SAAS,EAAC,0BAA0B;IAAAC,QAAA,gBACvCzJ,OAAA;MAAKwJ,SAAS,EAAC,QAAQ;MAAAC,QAAA,eACrBzJ,OAAA;QAAKwJ,SAAS,EAAC,yBAAyB;QAAAC,QAAA,eACtCzJ,OAAA;UAAKwJ,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAwB;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN3I,OAAA;MAAKwJ,SAAS,EAAC,sBAAsB;MAAAC,QAAA,gBACnCzJ,OAAA,CAACV,KAAK;QAAC6K,GAAG,EAAEjI;MAAM;QAAAsG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrB3I,OAAA,CAACT,aAAa;QAAAiJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEjB3I,OAAA,CAAChB,SAAS;QACR0D,KAAK,EAAEtC,cAAe;QACtBE,OAAO,EAAEA,OAAQ;QACjBwG,MAAM,EAAEA,MAAO;QACfsD,YAAY,EAAC,yBAAyB;QACtCC,QAAQ,EAAC,UAAU;QACnBC,SAAS;QACTC,IAAI;QACJ3I,IAAI,EAAEA,IAAK;QACXF,KAAK,EAAEA,KAAM;QACbF,YAAY,EAAEA,YAAa;QAC3BgJ,MAAM,EAAEjH,YAAa;QACrBkH,kBAAkB,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAE;QACpCjB,SAAS,EAAC,uBAAuB;QAAAC,QAAA,gBAEjCzJ,OAAA,CAACf,MAAM;UAACyL,KAAK,EAAC,MAAM;UAAC5D,MAAM,EAAC,MAAM;UAAC6D,QAAQ;QAAA;UAAAnC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9C3I,OAAA,CAACf,MAAM;UAACyL,KAAK,EAAC,MAAM;UAAC5D,MAAM,EAAC,MAAM;UAAC6D,QAAQ;QAAA;UAAAnC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9C3I,OAAA,CAACf,MAAM;UAACyL,KAAK,EAAC,QAAQ;UAAC5D,MAAM,EAAC,QAAQ;UAAC8D,IAAI,EAAEzC,kBAAmB;UAACwC,QAAQ;QAAA;UAAAnC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5E3I,OAAA,CAACf,MAAM;UAACyL,KAAK,EAAC,cAAc;UAAC5D,MAAM,EAAC,gBAAgB;UAAC8D,IAAI,EAAE7B,yBAA0B;UAAC4B,QAAQ;QAAA;UAAAnC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjG3I,OAAA,CAACf,MAAM;UAACyL,KAAK,EAAC,mBAAmB;UAAC5D,MAAM,EAAC,YAAY;UAAC8D,IAAI,EAAEvB,6BAA8B;UAACwB,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAQ;QAAE;UAAAtC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACxH3I,OAAA,CAACf,MAAM;UAACyL,KAAK,EAAC,YAAY;UAAC5D,MAAM,EAAC,YAAY;UAAC8D,IAAI,EAAEhC,gBAAiB;UAAC+B,QAAQ;QAAA;UAAAnC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClF3I,OAAA,CAACf,MAAM;UAACyL,KAAK,EAAC,gBAAgB;UAAC5D,MAAM,EAAC,YAAY;UAAC6D,QAAQ;QAAA;UAAAnC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9D3I,OAAA,CAACf,MAAM;UAAC6H,MAAM,EAAC,SAAS;UAAC8D,IAAI,EAAErB,mBAAoB;UAACsB,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAQ;QAAE;UAAAtC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxE,CAAC,eAGZ3I,OAAA,CAACb,MAAM;QACL2H,MAAM,EAAC,0BAA0B;QACjCiE,OAAO,EAAEvK,gBAAiB;QAC1BqK,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAQ,CAAE;QAC1BE,MAAM,EAAEA,CAAA,KAAM;UACZvK,mBAAmB,CAAC,KAAK,CAAC;UAC1BQ,mBAAmB,CAAC;YAAEC,IAAI,EAAE,EAAE;YAAEC,IAAI,EAAEiB;UAAY,CAAC,CAAC;QACtD,CAAE;QACF6I,MAAM,eACJjL,OAAA;UAAAyJ,QAAA,gBACEzJ,OAAA,CAACd,MAAM;YACLsD,KAAK,EAAC,QAAQ;YACduE,IAAI,EAAC,aAAa;YAClB2C,OAAO,EAAEA,CAAA,KAAM;cACbjJ,mBAAmB,CAAC,KAAK,CAAC;cAC1BQ,mBAAmB,CAAC;gBAAEC,IAAI,EAAE,EAAE;gBAAEC,IAAI,EAAEiB;cAAY,CAAC,CAAC;YACtD,CAAE;YACFoH,SAAS,EAAC;UAAe;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eACF3I,OAAA,CAACd,MAAM;YAACsD,KAAK,EAAC,QAAQ;YAACuE,IAAI,EAAC,aAAa;YAAC2C,OAAO,EAAE3F,yBAA0B;YAACyF,SAAS,EAAC;UAAQ;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChG,CACN;QAAAc,QAAA,gBAEDzJ,OAAA;UAAKwJ,SAAS,EAAC,OAAO;UAAAC,QAAA,gBACpBzJ,OAAA;YAAOkL,OAAO,EAAC,oBAAoB;YAAC1B,SAAS,EAAC,OAAO;YAAAC,QAAA,GAAC,OAC/C,eAAAzJ,OAAA;cAAMwJ,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAC;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACR3I,OAAA,CAACZ,SAAS;YACR8F,EAAE,EAAC,oBAAoB;YACvBxC,KAAK,EAAE1B,gBAAgB,CAACE,IAAK;YAC7B6I,QAAQ,EAAGC,CAAC,IAAK/I,mBAAmB,CAAC;cAAE,GAAGD,gBAAgB;cAAEE,IAAI,EAAE8I,CAAC,CAACC,MAAM,CAACvH;YAAM,CAAC,CAAE;YACpFwH,WAAW,EAAC,0BAA0B;YACtCV,SAAS,EAAC,QAAQ;YAClB2B,SAAS;UAAA;YAAA3C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN3I,OAAA;UAAKwJ,SAAS,EAAC,OAAO;UAAAC,QAAA,gBACpBzJ,OAAA;YAAOkL,OAAO,EAAC,oBAAoB;YAAC1B,SAAS,EAAC,OAAO;YAAAC,QAAA,GAAC,OAC/C,eAAAzJ,OAAA;cAAMwJ,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAC;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACR3I,OAAA,CAACX,QAAQ;YACP6F,EAAE,EAAC,oBAAoB;YACvBxC,KAAK,EAAE1B,gBAAgB,CAACG,IAAK;YAC7BiK,OAAO,EAAE/I,WAAY;YACrB0H,QAAQ,EAAGC,CAAC,IAAK/I,mBAAmB,CAAC;cAAE,GAAGD,gBAAgB;cAAEG,IAAI,EAAE6I,CAAC,CAACtH;YAAM,CAAC,CAAE;YAC7EwH,WAAW,EAAC,aAAa;YACzBV,SAAS,EAAC;UAAQ;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAGT3I,OAAA,CAACH,sBAAsB;QACrBkL,OAAO,EAAErK,cAAe;QACxBsK,MAAM,EAAEpF,gBAAiB;QACzByF,MAAM,EAAE7F,iBAAkB;QAC1BH,aAAa,EAAEzE,mBAAoB;QACnCN,OAAO,EAAEQ;MAAY;QAAA0H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC,EAGD3G,6BAA6B,iBAC5BhC,OAAA,CAACF,gBAAgB;QACfiL,OAAO,EAAEjJ,oBAAqB;QAC9BkJ,MAAM,EAAE1D,uBAAwB;QAChCgE,UAAU,EAAEnE,wBAAyB;QACrCoE,eAAe,EAAEvJ,6BAA6B,CAACkD,EAAG;QAClDsG,UAAU,EACRxJ,6BAA6B,CAACuC,mBAAmB,GAC7CC,IAAI,CAACiH,KAAK,CAACzJ,6BAA6B,CAACuC,mBAAmB,CAAC,GAC7D;UAAEmH,KAAK,EAAE1J,6BAA6B,CAACd,IAAI;UAAEyK,KAAK,EAAE;QAAG,CAC5D;QACDC,kBAAkB,EAChB3F,0BAA0B,CAACjE,6BAA6B,CAACkE,YAAY,CAAC,CAACC,MAAM,GAAG,CAAC,GAC7EvC,IAAI,CAACiI,GAAG,CAAC,GAAG5F,0BAA0B,CAACjE,6BAA6B,CAACkE,YAAY,CAAC,CAAC,GACnF,CACL;QACD4F,UAAU,EAAE9J,6BAA6B,CAACd,IAAK;QAC/C6K,YAAY,EAAE/J,6BAA6B,CAACb,IAAK;QACjD+E,YAAY,EAAElE,6BAA6B,CAACkE,YAAY,IAAI;MAAG;QAAAsC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChE,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzI,EAAA,CAzkBWD,uBAAuB;EAAA,QACjBlB,WAAW;AAAA;AAAAiN,EAAA,GADjB/L,uBAAuB;AAAA,IAAA+L,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}