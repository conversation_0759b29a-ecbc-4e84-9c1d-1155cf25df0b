{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport { PrimeReactContext } from 'primereact/api';\nimport { ComponentBase, useHandleStyle } from 'primereact/componentbase';\nimport { useMergeProps } from 'primereact/hooks';\nimport { classNames, ObjectUtils } from 'primereact/utils';\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nfunction toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction toPropertyKey(t) {\n  var i = toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nfunction _defineProperty(e, r, t) {\n  return (r = toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nvar classes = {\n  root: function root(_ref) {\n    var props = _ref.props;\n    return classNames('p-badge p-component', _defineProperty({\n      'p-badge-no-gutter': ObjectUtils.isNotEmpty(props.value) && String(props.value).length === 1,\n      'p-badge-dot': ObjectUtils.isEmpty(props.value),\n      'p-badge-lg': props.size === 'large',\n      'p-badge-xl': props.size === 'xlarge'\n    }, \"p-badge-\".concat(props.severity), props.severity !== null));\n  }\n};\nvar styles = \"\\n@layer primereact {\\n    .p-badge {\\n        display: inline-block;\\n        border-radius: 10px;\\n        text-align: center;\\n        padding: 0 .5rem;\\n    }\\n    \\n    .p-overlay-badge {\\n        position: relative;\\n    }\\n    \\n    .p-overlay-badge .p-badge {\\n        position: absolute;\\n        top: 0;\\n        right: 0;\\n        transform: translate(50%,-50%);\\n        transform-origin: 100% 0;\\n        margin: 0;\\n    }\\n    \\n    .p-badge-dot {\\n        width: .5rem;\\n        min-width: .5rem;\\n        height: .5rem;\\n        border-radius: 50%;\\n        padding: 0;\\n    }\\n    \\n    .p-badge-no-gutter {\\n        padding: 0;\\n        border-radius: 50%;\\n    }\\n}\\n\";\nvar BadgeBase = ComponentBase.extend({\n  defaultProps: {\n    __TYPE: 'Badge',\n    __parentMetadata: null,\n    value: null,\n    severity: null,\n    size: null,\n    style: null,\n    className: null,\n    children: undefined\n  },\n  css: {\n    classes: classes,\n    styles: styles\n  }\n});\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nvar Badge = /*#__PURE__*/React.memo(/*#__PURE__*/React.forwardRef(function (inProps, ref) {\n  var mergeProps = useMergeProps();\n  var context = React.useContext(PrimeReactContext);\n  var props = BadgeBase.getProps(inProps, context);\n  var _BadgeBase$setMetaDat = BadgeBase.setMetaData(_objectSpread({\n      props: props\n    }, props.__parentMetadata)),\n    ptm = _BadgeBase$setMetaDat.ptm,\n    cx = _BadgeBase$setMetaDat.cx,\n    isUnstyled = _BadgeBase$setMetaDat.isUnstyled;\n  useHandleStyle(BadgeBase.css.styles, isUnstyled, {\n    name: 'badge'\n  });\n  var elementRef = React.useRef(null);\n  React.useImperativeHandle(ref, function () {\n    return {\n      props: props,\n      getElement: function getElement() {\n        return elementRef.current;\n      }\n    };\n  });\n  var rootProps = mergeProps({\n    ref: elementRef,\n    style: props.style,\n    className: classNames(props.className, cx('root'))\n  }, BadgeBase.getOtherProps(props), ptm('root'));\n  return /*#__PURE__*/React.createElement(\"span\", rootProps, props.value);\n}));\nBadge.displayName = 'Badge';\nexport { Badge };", "map": {"version": 3, "names": ["React", "PrimeReactContext", "ComponentBase", "useHandleStyle", "useMergeProps", "classNames", "ObjectUtils", "_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "toPrimitive", "t", "r", "e", "i", "call", "TypeError", "String", "Number", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_defineProperty", "Object", "defineProperty", "value", "enumerable", "configurable", "writable", "classes", "root", "_ref", "props", "isNotEmpty", "length", "isEmpty", "size", "concat", "severity", "styles", "BadgeBase", "extend", "defaultProps", "__TYPE", "__parentMetadata", "style", "className", "children", "undefined", "css", "ownKeys", "keys", "getOwnPropertySymbols", "filter", "getOwnPropertyDescriptor", "push", "apply", "_objectSpread", "arguments", "for<PERSON>ach", "getOwnPropertyDescriptors", "defineProperties", "Badge", "memo", "forwardRef", "inProps", "ref", "mergeProps", "context", "useContext", "getProps", "_BadgeBase$setMetaDat", "setMetaData", "ptm", "cx", "isUnstyled", "name", "elementRef", "useRef", "useImperativeHandle", "getElement", "current", "rootProps", "getOtherProps", "createElement", "displayName"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/node_modules/primereact/badge/badge.esm.js"], "sourcesContent": ["'use client';\nimport * as React from 'react';\nimport { PrimeReactContext } from 'primereact/api';\nimport { ComponentBase, useHandleStyle } from 'primereact/componentbase';\nimport { useMergeProps } from 'primereact/hooks';\nimport { classNames, ObjectUtils } from 'primereact/utils';\n\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\n\nfunction toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\n\nfunction toPropertyKey(t) {\n  var i = toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\n\nfunction _defineProperty(e, r, t) {\n  return (r = toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\n\nvar classes = {\n  root: function root(_ref) {\n    var props = _ref.props;\n    return classNames('p-badge p-component', _defineProperty({\n      'p-badge-no-gutter': ObjectUtils.isNotEmpty(props.value) && String(props.value).length === 1,\n      'p-badge-dot': ObjectUtils.isEmpty(props.value),\n      'p-badge-lg': props.size === 'large',\n      'p-badge-xl': props.size === 'xlarge'\n    }, \"p-badge-\".concat(props.severity), props.severity !== null));\n  }\n};\nvar styles = \"\\n@layer primereact {\\n    .p-badge {\\n        display: inline-block;\\n        border-radius: 10px;\\n        text-align: center;\\n        padding: 0 .5rem;\\n    }\\n    \\n    .p-overlay-badge {\\n        position: relative;\\n    }\\n    \\n    .p-overlay-badge .p-badge {\\n        position: absolute;\\n        top: 0;\\n        right: 0;\\n        transform: translate(50%,-50%);\\n        transform-origin: 100% 0;\\n        margin: 0;\\n    }\\n    \\n    .p-badge-dot {\\n        width: .5rem;\\n        min-width: .5rem;\\n        height: .5rem;\\n        border-radius: 50%;\\n        padding: 0;\\n    }\\n    \\n    .p-badge-no-gutter {\\n        padding: 0;\\n        border-radius: 50%;\\n    }\\n}\\n\";\nvar BadgeBase = ComponentBase.extend({\n  defaultProps: {\n    __TYPE: 'Badge',\n    __parentMetadata: null,\n    value: null,\n    severity: null,\n    size: null,\n    style: null,\n    className: null,\n    children: undefined\n  },\n  css: {\n    classes: classes,\n    styles: styles\n  }\n});\n\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nvar Badge = /*#__PURE__*/React.memo(/*#__PURE__*/React.forwardRef(function (inProps, ref) {\n  var mergeProps = useMergeProps();\n  var context = React.useContext(PrimeReactContext);\n  var props = BadgeBase.getProps(inProps, context);\n  var _BadgeBase$setMetaDat = BadgeBase.setMetaData(_objectSpread({\n      props: props\n    }, props.__parentMetadata)),\n    ptm = _BadgeBase$setMetaDat.ptm,\n    cx = _BadgeBase$setMetaDat.cx,\n    isUnstyled = _BadgeBase$setMetaDat.isUnstyled;\n  useHandleStyle(BadgeBase.css.styles, isUnstyled, {\n    name: 'badge'\n  });\n  var elementRef = React.useRef(null);\n  React.useImperativeHandle(ref, function () {\n    return {\n      props: props,\n      getElement: function getElement() {\n        return elementRef.current;\n      }\n    };\n  });\n  var rootProps = mergeProps({\n    ref: elementRef,\n    style: props.style,\n    className: classNames(props.className, cx('root'))\n  }, BadgeBase.getOtherProps(props), ptm('root'));\n  return /*#__PURE__*/React.createElement(\"span\", rootProps, props.value);\n}));\nBadge.displayName = 'Badge';\n\nexport { Badge };\n"], "mappings": "AAAA,YAAY;;AACZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,iBAAiB,QAAQ,gBAAgB;AAClD,SAASC,aAAa,EAAEC,cAAc,QAAQ,0BAA0B;AACxE,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,UAAU,EAAEC,WAAW,QAAQ,kBAAkB;AAE1D,SAASC,OAAOA,CAACC,CAAC,EAAE;EAClB,yBAAyB;;EAEzB,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,CAAC,EAAE;IAChG,OAAO,OAAOA,CAAC;EACjB,CAAC,GAAG,UAAUA,CAAC,EAAE;IACf,OAAOA,CAAC,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,CAAC,CAACG,WAAW,KAAKF,MAAM,IAAID,CAAC,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,CAAC;EACrH,CAAC,EAAED,OAAO,CAACC,CAAC,CAAC;AACf;AAEA,SAASK,WAAWA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACzB,IAAI,QAAQ,IAAIR,OAAO,CAACO,CAAC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAC1C,IAAIE,CAAC,GAAGF,CAAC,CAACL,MAAM,CAACI,WAAW,CAAC;EAC7B,IAAI,KAAK,CAAC,KAAKG,CAAC,EAAE;IAChB,IAAIC,CAAC,GAAGD,CAAC,CAACE,IAAI,CAACJ,CAAC,EAAEC,CAAC,IAAI,SAAS,CAAC;IACjC,IAAI,QAAQ,IAAIR,OAAO,CAACU,CAAC,CAAC,EAAE,OAAOA,CAAC;IACpC,MAAM,IAAIE,SAAS,CAAC,8CAA8C,CAAC;EACrE;EACA,OAAO,CAAC,QAAQ,KAAKJ,CAAC,GAAGK,MAAM,GAAGC,MAAM,EAAEP,CAAC,CAAC;AAC9C;AAEA,SAASQ,aAAaA,CAACR,CAAC,EAAE;EACxB,IAAIG,CAAC,GAAGJ,WAAW,CAACC,CAAC,EAAE,QAAQ,CAAC;EAChC,OAAO,QAAQ,IAAIP,OAAO,CAACU,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAC5C;AAEA,SAASM,eAAeA,CAACP,CAAC,EAAED,CAAC,EAAED,CAAC,EAAE;EAChC,OAAO,CAACC,CAAC,GAAGO,aAAa,CAACP,CAAC,CAAC,KAAKC,CAAC,GAAGQ,MAAM,CAACC,cAAc,CAACT,CAAC,EAAED,CAAC,EAAE;IAC/DW,KAAK,EAAEZ,CAAC;IACRa,UAAU,EAAE,CAAC,CAAC;IACdC,YAAY,EAAE,CAAC,CAAC;IAChBC,QAAQ,EAAE,CAAC;EACb,CAAC,CAAC,GAAGb,CAAC,CAACD,CAAC,CAAC,GAAGD,CAAC,EAAEE,CAAC;AAClB;AAEA,IAAIc,OAAO,GAAG;EACZC,IAAI,EAAE,SAASA,IAAIA,CAACC,IAAI,EAAE;IACxB,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;IACtB,OAAO5B,UAAU,CAAC,qBAAqB,EAAEkB,eAAe,CAAC;MACvD,mBAAmB,EAAEjB,WAAW,CAAC4B,UAAU,CAACD,KAAK,CAACP,KAAK,CAAC,IAAIN,MAAM,CAACa,KAAK,CAACP,KAAK,CAAC,CAACS,MAAM,KAAK,CAAC;MAC5F,aAAa,EAAE7B,WAAW,CAAC8B,OAAO,CAACH,KAAK,CAACP,KAAK,CAAC;MAC/C,YAAY,EAAEO,KAAK,CAACI,IAAI,KAAK,OAAO;MACpC,YAAY,EAAEJ,KAAK,CAACI,IAAI,KAAK;IAC/B,CAAC,EAAE,UAAU,CAACC,MAAM,CAACL,KAAK,CAACM,QAAQ,CAAC,EAAEN,KAAK,CAACM,QAAQ,KAAK,IAAI,CAAC,CAAC;EACjE;AACF,CAAC;AACD,IAAIC,MAAM,GAAG,grBAAgrB;AAC7rB,IAAIC,SAAS,GAAGvC,aAAa,CAACwC,MAAM,CAAC;EACnCC,YAAY,EAAE;IACZC,MAAM,EAAE,OAAO;IACfC,gBAAgB,EAAE,IAAI;IACtBnB,KAAK,EAAE,IAAI;IACXa,QAAQ,EAAE,IAAI;IACdF,IAAI,EAAE,IAAI;IACVS,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,IAAI;IACfC,QAAQ,EAAEC;EACZ,CAAC;EACDC,GAAG,EAAE;IACHpB,OAAO,EAAEA,OAAO;IAChBU,MAAM,EAAEA;EACV;AACF,CAAC,CAAC;AAEF,SAASW,OAAOA,CAACnC,CAAC,EAAED,CAAC,EAAE;EAAE,IAAID,CAAC,GAAGU,MAAM,CAAC4B,IAAI,CAACpC,CAAC,CAAC;EAAE,IAAIQ,MAAM,CAAC6B,qBAAqB,EAAE;IAAE,IAAI7C,CAAC,GAAGgB,MAAM,CAAC6B,qBAAqB,CAACrC,CAAC,CAAC;IAAED,CAAC,KAAKP,CAAC,GAAGA,CAAC,CAAC8C,MAAM,CAAC,UAAUvC,CAAC,EAAE;MAAE,OAAOS,MAAM,CAAC+B,wBAAwB,CAACvC,CAAC,EAAED,CAAC,CAAC,CAACY,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEb,CAAC,CAAC0C,IAAI,CAACC,KAAK,CAAC3C,CAAC,EAAEN,CAAC,CAAC;EAAE;EAAE,OAAOM,CAAC;AAAE;AAC9P,SAAS4C,aAAaA,CAAC1C,CAAC,EAAE;EAAE,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4C,SAAS,CAACxB,MAAM,EAAEpB,CAAC,EAAE,EAAE;IAAE,IAAID,CAAC,GAAG,IAAI,IAAI6C,SAAS,CAAC5C,CAAC,CAAC,GAAG4C,SAAS,CAAC5C,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGoC,OAAO,CAAC3B,MAAM,CAACV,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC8C,OAAO,CAAC,UAAU7C,CAAC,EAAE;MAAEQ,eAAe,CAACP,CAAC,EAAED,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGS,MAAM,CAACqC,yBAAyB,GAAGrC,MAAM,CAACsC,gBAAgB,CAAC9C,CAAC,EAAEQ,MAAM,CAACqC,yBAAyB,CAAC/C,CAAC,CAAC,CAAC,GAAGqC,OAAO,CAAC3B,MAAM,CAACV,CAAC,CAAC,CAAC,CAAC8C,OAAO,CAAC,UAAU7C,CAAC,EAAE;MAAES,MAAM,CAACC,cAAc,CAACT,CAAC,EAAED,CAAC,EAAES,MAAM,CAAC+B,wBAAwB,CAACzC,CAAC,EAAEC,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOC,CAAC;AAAE;AACtb,IAAI+C,KAAK,GAAG,aAAa/D,KAAK,CAACgE,IAAI,CAAC,aAAahE,KAAK,CAACiE,UAAU,CAAC,UAAUC,OAAO,EAAEC,GAAG,EAAE;EACxF,IAAIC,UAAU,GAAGhE,aAAa,CAAC,CAAC;EAChC,IAAIiE,OAAO,GAAGrE,KAAK,CAACsE,UAAU,CAACrE,iBAAiB,CAAC;EACjD,IAAIgC,KAAK,GAAGQ,SAAS,CAAC8B,QAAQ,CAACL,OAAO,EAAEG,OAAO,CAAC;EAChD,IAAIG,qBAAqB,GAAG/B,SAAS,CAACgC,WAAW,CAACf,aAAa,CAAC;MAC5DzB,KAAK,EAAEA;IACT,CAAC,EAAEA,KAAK,CAACY,gBAAgB,CAAC,CAAC;IAC3B6B,GAAG,GAAGF,qBAAqB,CAACE,GAAG;IAC/BC,EAAE,GAAGH,qBAAqB,CAACG,EAAE;IAC7BC,UAAU,GAAGJ,qBAAqB,CAACI,UAAU;EAC/CzE,cAAc,CAACsC,SAAS,CAACS,GAAG,CAACV,MAAM,EAAEoC,UAAU,EAAE;IAC/CC,IAAI,EAAE;EACR,CAAC,CAAC;EACF,IAAIC,UAAU,GAAG9E,KAAK,CAAC+E,MAAM,CAAC,IAAI,CAAC;EACnC/E,KAAK,CAACgF,mBAAmB,CAACb,GAAG,EAAE,YAAY;IACzC,OAAO;MACLlC,KAAK,EAAEA,KAAK;MACZgD,UAAU,EAAE,SAASA,UAAUA,CAAA,EAAG;QAChC,OAAOH,UAAU,CAACI,OAAO;MAC3B;IACF,CAAC;EACH,CAAC,CAAC;EACF,IAAIC,SAAS,GAAGf,UAAU,CAAC;IACzBD,GAAG,EAAEW,UAAU;IACfhC,KAAK,EAAEb,KAAK,CAACa,KAAK;IAClBC,SAAS,EAAE1C,UAAU,CAAC4B,KAAK,CAACc,SAAS,EAAE4B,EAAE,CAAC,MAAM,CAAC;EACnD,CAAC,EAAElC,SAAS,CAAC2C,aAAa,CAACnD,KAAK,CAAC,EAAEyC,GAAG,CAAC,MAAM,CAAC,CAAC;EAC/C,OAAO,aAAa1E,KAAK,CAACqF,aAAa,CAAC,MAAM,EAAEF,SAAS,EAAElD,KAAK,CAACP,KAAK,CAAC;AACzE,CAAC,CAAC,CAAC;AACHqC,KAAK,CAACuB,WAAW,GAAG,OAAO;AAE3B,SAASvB,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}