{"ast": null, "code": "import { Observable } from '../Observable';\nimport { argsArgArrayOrObject } from '../util/argsArgArrayOrObject';\nimport { from } from './from';\nimport { identity } from '../util/identity';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nimport { popResultSelector, popScheduler } from '../util/args';\nimport { createObject } from '../util/createObject';\nimport { createOperatorSubscriber } from '../operators/OperatorSubscriber';\nimport { executeSchedule } from '../util/executeSchedule';\nexport function combineLatest() {\n  var args = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    args[_i] = arguments[_i];\n  }\n  var scheduler = popScheduler(args);\n  var resultSelector = popResultSelector(args);\n  var _a = argsArgArrayOrObject(args),\n    observables = _a.args,\n    keys = _a.keys;\n  if (observables.length === 0) {\n    return from([], scheduler);\n  }\n  var result = new Observable(combineLatestInit(observables, scheduler, keys ? function (values) {\n    return createObject(keys, values);\n  } : identity));\n  return resultSelector ? result.pipe(mapOneOrManyArgs(resultSelector)) : result;\n}\nexport function combineLatestInit(observables, scheduler, valueTransform) {\n  if (valueTransform === void 0) {\n    valueTransform = identity;\n  }\n  return function (subscriber) {\n    maybeSchedule(scheduler, function () {\n      var length = observables.length;\n      var values = new Array(length);\n      var active = length;\n      var remainingFirstValues = length;\n      var _loop_1 = function (i) {\n        maybeSchedule(scheduler, function () {\n          var source = from(observables[i], scheduler);\n          var hasFirstValue = false;\n          source.subscribe(createOperatorSubscriber(subscriber, function (value) {\n            values[i] = value;\n            if (!hasFirstValue) {\n              hasFirstValue = true;\n              remainingFirstValues--;\n            }\n            if (!remainingFirstValues) {\n              subscriber.next(valueTransform(values.slice()));\n            }\n          }, function () {\n            if (! --active) {\n              subscriber.complete();\n            }\n          }));\n        }, subscriber);\n      };\n      for (var i = 0; i < length; i++) {\n        _loop_1(i);\n      }\n    }, subscriber);\n  };\n}\nfunction maybeSchedule(scheduler, execute, subscription) {\n  if (scheduler) {\n    executeSchedule(subscription, scheduler, execute);\n  } else {\n    execute();\n  }\n}", "map": {"version": 3, "names": ["Observable", "argsArgArrayOrObject", "from", "identity", "mapOneOrManyArgs", "popResultSelector", "popScheduler", "createObject", "createOperatorSubscriber", "executeSchedule", "combineLatest", "args", "_i", "arguments", "length", "scheduler", "resultSelector", "_a", "observables", "keys", "result", "combineLatestInit", "values", "pipe", "valueTransform", "subscriber", "maybeSchedule", "Array", "active", "remainingFirstValues", "i", "source", "hasFirstValue", "subscribe", "value", "next", "slice", "complete", "execute", "subscription"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\observable\\combineLatest.ts"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { ObservableInput, SchedulerLike, ObservedValueOf, ObservableInputTuple } from '../types';\nimport { argsArgArrayOrObject } from '../util/argsArgArrayOrObject';\nimport { Subscriber } from '../Subscriber';\nimport { from } from './from';\nimport { identity } from '../util/identity';\nimport { Subscription } from '../Subscription';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nimport { popResultSelector, popScheduler } from '../util/args';\nimport { createObject } from '../util/createObject';\nimport { createOperatorSubscriber } from '../operators/OperatorSubscriber';\nimport { AnyCatcher } from '../AnyCatcher';\nimport { executeSchedule } from '../util/executeSchedule';\n\n// combineLatest(any)\n// We put this first because we need to catch cases where the user has supplied\n// _exactly `any`_ as the argument. Since `any` literally matches _anything_,\n// we don't want it to randomly hit one of the other type signatures below,\n// as we have no idea at build-time what type we should be returning when given an any.\n\n/**\n * You have passed `any` here, we can't figure out if it is\n * an array or an object, so you're getting `unknown`. Use better types.\n * @param arg Something typed as `any`\n */\nexport function combineLatest<T extends AnyCatcher>(arg: T): Observable<unknown>;\n\n// combineLatest([a, b, c])\nexport function combineLatest(sources: []): Observable<never>;\nexport function combineLatest<A extends readonly unknown[]>(sources: readonly [...ObservableInputTuple<A>]): Observable<A>;\n/** @deprecated The `scheduler` parameter will be removed in v8. Use `scheduled` and `combineLatestAll`. Details: https://rxjs.dev/deprecations/scheduler-argument */\nexport function combineLatest<A extends readonly unknown[], R>(\n  sources: readonly [...ObservableInputTuple<A>],\n  resultSelector: (...values: A) => R,\n  scheduler: SchedulerLike\n): Observable<R>;\nexport function combineLatest<A extends readonly unknown[], R>(\n  sources: readonly [...ObservableInputTuple<A>],\n  resultSelector: (...values: A) => R\n): Observable<R>;\n/** @deprecated The `scheduler` parameter will be removed in v8. Use `scheduled` and `combineLatestAll`. Details: https://rxjs.dev/deprecations/scheduler-argument */\nexport function combineLatest<A extends readonly unknown[]>(\n  sources: readonly [...ObservableInputTuple<A>],\n  scheduler: SchedulerLike\n): Observable<A>;\n\n// combineLatest(a, b, c)\n/** @deprecated Pass an array of sources instead. The rest-parameters signature will be removed in v8. Details: https://rxjs.dev/deprecations/array-argument */\nexport function combineLatest<A extends readonly unknown[]>(...sources: [...ObservableInputTuple<A>]): Observable<A>;\n/** @deprecated The `scheduler` parameter will be removed in v8. Use `scheduled` and `combineLatestAll`. Details: https://rxjs.dev/deprecations/scheduler-argument */\nexport function combineLatest<A extends readonly unknown[], R>(\n  ...sourcesAndResultSelectorAndScheduler: [...ObservableInputTuple<A>, (...values: A) => R, SchedulerLike]\n): Observable<R>;\n/** @deprecated Pass an array of sources instead. The rest-parameters signature will be removed in v8. Details: https://rxjs.dev/deprecations/array-argument */\nexport function combineLatest<A extends readonly unknown[], R>(\n  ...sourcesAndResultSelector: [...ObservableInputTuple<A>, (...values: A) => R]\n): Observable<R>;\n/** @deprecated The `scheduler` parameter will be removed in v8. Use `scheduled` and `combineLatestAll`. Details: https://rxjs.dev/deprecations/scheduler-argument */\nexport function combineLatest<A extends readonly unknown[]>(\n  ...sourcesAndScheduler: [...ObservableInputTuple<A>, SchedulerLike]\n): Observable<A>;\n\n// combineLatest({a, b, c})\nexport function combineLatest(sourcesObject: { [K in any]: never }): Observable<never>;\nexport function combineLatest<T extends Record<string, ObservableInput<any>>>(\n  sourcesObject: T\n): Observable<{ [K in keyof T]: ObservedValueOf<T[K]> }>;\n\n/**\n * Combines multiple Observables to create an Observable whose values are\n * calculated from the latest values of each of its input Observables.\n *\n * <span class=\"informal\">Whenever any input Observable emits a value, it\n * computes a formula using the latest values from all the inputs, then emits\n * the output of that formula.</span>\n *\n * ![](combineLatest.png)\n *\n * `combineLatest` combines the values from all the Observables passed in the\n * observables array. This is done by subscribing to each Observable in order and,\n * whenever any Observable emits, collecting an array of the most recent\n * values from each Observable. So if you pass `n` Observables to this operator,\n * the returned Observable will always emit an array of `n` values, in an order\n * corresponding to the order of the passed Observables (the value from the first Observable\n * will be at index 0 of the array and so on).\n *\n * Static version of `combineLatest` accepts an array of Observables. Note that an array of\n * Observables is a good choice, if you don't know beforehand how many Observables\n * you will combine. Passing an empty array will result in an Observable that\n * completes immediately.\n *\n * To ensure the output array always has the same length, `combineLatest` will\n * actually wait for all input Observables to emit at least once,\n * before it starts emitting results. This means if some Observable emits\n * values before other Observables started emitting, all these values but the last\n * will be lost. On the other hand, if some Observable does not emit a value but\n * completes, resulting Observable will complete at the same moment without\n * emitting anything, since it will now be impossible to include a value from the\n * completed Observable in the resulting array. Also, if some input Observable does\n * not emit any value and never completes, `combineLatest` will also never emit\n * and never complete, since, again, it will wait for all streams to emit some\n * value.\n *\n * If at least one Observable was passed to `combineLatest` and all passed Observables\n * emitted something, the resulting Observable will complete when all combined\n * streams complete. So even if some Observable completes, the result of\n * `combineLatest` will still emit values when other Observables do. In case\n * of a completed Observable, its value from now on will always be the last\n * emitted value. On the other hand, if any Observable errors, `combineLatest`\n * will error immediately as well, and all other Observables will be unsubscribed.\n *\n * ## Examples\n *\n * Combine two timer Observables\n *\n * ```ts\n * import { timer, combineLatest } from 'rxjs';\n *\n * const firstTimer = timer(0, 1000); // emit 0, 1, 2... after every second, starting from now\n * const secondTimer = timer(500, 1000); // emit 0, 1, 2... after every second, starting 0,5s from now\n * const combinedTimers = combineLatest([firstTimer, secondTimer]);\n * combinedTimers.subscribe(value => console.log(value));\n * // Logs\n * // [0, 0] after 0.5s\n * // [1, 0] after 1s\n * // [1, 1] after 1.5s\n * // [2, 1] after 2s\n * ```\n *\n * Combine a dictionary of Observables\n *\n * ```ts\n * import { of, delay, startWith, combineLatest } from 'rxjs';\n *\n * const observables = {\n *   a: of(1).pipe(delay(1000), startWith(0)),\n *   b: of(5).pipe(delay(5000), startWith(0)),\n *   c: of(10).pipe(delay(10000), startWith(0))\n * };\n * const combined = combineLatest(observables);\n * combined.subscribe(value => console.log(value));\n * // Logs\n * // { a: 0, b: 0, c: 0 } immediately\n * // { a: 1, b: 0, c: 0 } after 1s\n * // { a: 1, b: 5, c: 0 } after 5s\n * // { a: 1, b: 5, c: 10 } after 10s\n * ```\n *\n * Combine an array of Observables\n *\n * ```ts\n * import { of, delay, startWith, combineLatest } from 'rxjs';\n *\n * const observables = [1, 5, 10].map(\n *   n => of(n).pipe(\n *     delay(n * 1000), // emit 0 and then emit n after n seconds\n *     startWith(0)\n *   )\n * );\n * const combined = combineLatest(observables);\n * combined.subscribe(value => console.log(value));\n * // Logs\n * // [0, 0, 0] immediately\n * // [1, 0, 0] after 1s\n * // [1, 5, 0] after 5s\n * // [1, 5, 10] after 10s\n * ```\n *\n * Use map operator to dynamically calculate the Body-Mass Index\n *\n * ```ts\n * import { of, combineLatest, map } from 'rxjs';\n *\n * const weight = of(70, 72, 76, 79, 75);\n * const height = of(1.76, 1.77, 1.78);\n * const bmi = combineLatest([weight, height]).pipe(\n *   map(([w, h]) => w / (h * h)),\n * );\n * bmi.subscribe(x => console.log('BMI is ' + x));\n *\n * // With output to console:\n * // BMI is 24.212293388429753\n * // BMI is 23.93948099205209\n * // BMI is 23.671253629592222\n * ```\n *\n * @see {@link combineLatestAll}\n * @see {@link merge}\n * @see {@link withLatestFrom}\n *\n * @param args Any number of `ObservableInput`s provided either as an array or as an object\n * to combine with each other. If the last parameter is the function, it will be used to project the\n * values from the combined latest values into a new value on the output Observable.\n * @return An Observable of projected values from the most recent values from each `ObservableInput`,\n * or an array of the most recent values from each `ObservableInput`.\n */\nexport function combineLatest<O extends ObservableInput<any>, R>(...args: any[]): Observable<R> | Observable<ObservedValueOf<O>[]> {\n  const scheduler = popScheduler(args);\n  const resultSelector = popResultSelector(args);\n\n  const { args: observables, keys } = argsArgArrayOrObject(args);\n\n  if (observables.length === 0) {\n    // If no observables are passed, or someone has passed an empty array\n    // of observables, or even an empty object POJO, we need to just\n    // complete (EMPTY), but we have to honor the scheduler provided if any.\n    return from([], scheduler as any);\n  }\n\n  const result = new Observable<ObservedValueOf<O>[]>(\n    combineLatestInit(\n      observables as ObservableInput<ObservedValueOf<O>>[],\n      scheduler,\n      keys\n        ? // A handler for scrubbing the array of args into a dictionary.\n          (values) => createObject(keys, values)\n        : // A passthrough to just return the array\n          identity\n    )\n  );\n\n  return resultSelector ? (result.pipe(mapOneOrManyArgs(resultSelector)) as Observable<R>) : result;\n}\n\nexport function combineLatestInit(\n  observables: ObservableInput<any>[],\n  scheduler?: SchedulerLike,\n  valueTransform: (values: any[]) => any = identity\n) {\n  return (subscriber: Subscriber<any>) => {\n    // The outer subscription. We're capturing this in a function\n    // because we may have to schedule it.\n    maybeSchedule(\n      scheduler,\n      () => {\n        const { length } = observables;\n        // A store for the values each observable has emitted so far. We match observable to value on index.\n        const values = new Array(length);\n        // The number of currently active subscriptions, as they complete, we decrement this number to see if\n        // we are all done combining values, so we can complete the result.\n        let active = length;\n        // The number of inner sources that still haven't emitted the first value\n        // We need to track this because all sources need to emit one value in order\n        // to start emitting values.\n        let remainingFirstValues = length;\n        // The loop to kick off subscription. We're keying everything on index `i` to relate the observables passed\n        // in to the slot in the output array or the key in the array of keys in the output dictionary.\n        for (let i = 0; i < length; i++) {\n          maybeSchedule(\n            scheduler,\n            () => {\n              const source = from(observables[i], scheduler as any);\n              let hasFirstValue = false;\n              source.subscribe(\n                createOperatorSubscriber(\n                  subscriber,\n                  (value) => {\n                    // When we get a value, record it in our set of values.\n                    values[i] = value;\n                    if (!hasFirstValue) {\n                      // If this is our first value, record that.\n                      hasFirstValue = true;\n                      remainingFirstValues--;\n                    }\n                    if (!remainingFirstValues) {\n                      // We're not waiting for any more\n                      // first values, so we can emit!\n                      subscriber.next(valueTransform(values.slice()));\n                    }\n                  },\n                  () => {\n                    if (!--active) {\n                      // We only complete the result if we have no more active\n                      // inner observables.\n                      subscriber.complete();\n                    }\n                  }\n                )\n              );\n            },\n            subscriber\n          );\n        }\n      },\n      subscriber\n    );\n  };\n}\n\n/**\n * A small utility to handle the couple of locations where we want to schedule if a scheduler was provided,\n * but we don't if there was no scheduler.\n */\nfunction maybeSchedule(scheduler: SchedulerLike | undefined, execute: () => void, subscription: Subscription) {\n  if (scheduler) {\n    executeSchedule(subscription, scheduler, execute);\n  } else {\n    execute();\n  }\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAE1C,SAASC,oBAAoB,QAAQ,8BAA8B;AAEnE,SAASC,IAAI,QAAQ,QAAQ;AAC7B,SAASC,QAAQ,QAAQ,kBAAkB;AAE3C,SAASC,gBAAgB,QAAQ,0BAA0B;AAC3D,SAASC,iBAAiB,EAAEC,YAAY,QAAQ,cAAc;AAC9D,SAASC,YAAY,QAAQ,sBAAsB;AACnD,SAASC,wBAAwB,QAAQ,iCAAiC;AAE1E,SAASC,eAAe,QAAQ,yBAAyB;AAwLzD,OAAM,SAAUC,aAAaA,CAAA;EAAoC,IAAAC,IAAA;OAAA,IAAAC,EAAA,IAAc,EAAdA,EAAA,GAAAC,SAAA,CAAAC,MAAc,EAAdF,EAAA,EAAc;IAAdD,IAAA,CAAAC,EAAA,IAAAC,SAAA,CAAAD,EAAA;;EAC/D,IAAMG,SAAS,GAAGT,YAAY,CAACK,IAAI,CAAC;EACpC,IAAMK,cAAc,GAAGX,iBAAiB,CAACM,IAAI,CAAC;EAExC,IAAAM,EAAA,GAA8BhB,oBAAoB,CAACU,IAAI,CAAC;IAAhDO,WAAW,GAAAD,EAAA,CAAAN,IAAA;IAAEQ,IAAI,GAAAF,EAAA,CAAAE,IAA+B;EAE9D,IAAID,WAAW,CAACJ,MAAM,KAAK,CAAC,EAAE;IAI5B,OAAOZ,IAAI,CAAC,EAAE,EAAEa,SAAgB,CAAC;;EAGnC,IAAMK,MAAM,GAAG,IAAIpB,UAAU,CAC3BqB,iBAAiB,CACfH,WAAoD,EACpDH,SAAS,EACTI,IAAI,GAEA,UAACG,MAAM;IAAK,OAAAf,YAAY,CAACY,IAAI,EAAEG,MAAM,CAAC;EAA1B,CAA0B,GAEtCnB,QAAQ,CACb,CACF;EAED,OAAOa,cAAc,GAAII,MAAM,CAACG,IAAI,CAACnB,gBAAgB,CAACY,cAAc,CAAC,CAAmB,GAAGI,MAAM;AACnG;AAEA,OAAM,SAAUC,iBAAiBA,CAC/BH,WAAmC,EACnCH,SAAyB,EACzBS,cAAiD;EAAjD,IAAAA,cAAA;IAAAA,cAAA,GAAArB,QAAiD;EAAA;EAEjD,OAAO,UAACsB,UAA2B;IAGjCC,aAAa,CACXX,SAAS,EACT;MACU,IAAAD,MAAM,GAAKI,WAAW,CAAAJ,MAAhB;MAEd,IAAMQ,MAAM,GAAG,IAAIK,KAAK,CAACb,MAAM,CAAC;MAGhC,IAAIc,MAAM,GAAGd,MAAM;MAInB,IAAIe,oBAAoB,GAAGf,MAAM;8BAGxBgB,CAAC;QACRJ,aAAa,CACXX,SAAS,EACT;UACE,IAAMgB,MAAM,GAAG7B,IAAI,CAACgB,WAAW,CAACY,CAAC,CAAC,EAAEf,SAAgB,CAAC;UACrD,IAAIiB,aAAa,GAAG,KAAK;UACzBD,MAAM,CAACE,SAAS,CACdzB,wBAAwB,CACtBiB,UAAU,EACV,UAACS,KAAK;YAEJZ,MAAM,CAACQ,CAAC,CAAC,GAAGI,KAAK;YACjB,IAAI,CAACF,aAAa,EAAE;cAElBA,aAAa,GAAG,IAAI;cACpBH,oBAAoB,EAAE;;YAExB,IAAI,CAACA,oBAAoB,EAAE;cAGzBJ,UAAU,CAACU,IAAI,CAACX,cAAc,CAACF,MAAM,CAACc,KAAK,EAAE,CAAC,CAAC;;UAEnD,CAAC,EACD;YACE,IAAI,CAAC,GAAER,MAAM,EAAE;cAGbH,UAAU,CAACY,QAAQ,EAAE;;UAEzB,CAAC,CACF,CACF;QACH,CAAC,EACDZ,UAAU,CACX;;MAlCH,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhB,MAAM,EAAEgB,CAAC,EAAE;gBAAtBA,CAAC;;IAoCZ,CAAC,EACDL,UAAU,CACX;EACH,CAAC;AACH;AAMA,SAASC,aAAaA,CAACX,SAAoC,EAAEuB,OAAmB,EAAEC,YAA0B;EAC1G,IAAIxB,SAAS,EAAE;IACbN,eAAe,CAAC8B,YAAY,EAAExB,SAAS,EAAEuB,OAAO,CAAC;GAClD,MAAM;IACLA,OAAO,EAAE;;AAEb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}