{"ast": null, "code": "var _jsxFileName = \"C:\\\\git\\\\Partner-Site\\\\BdoPartner.Plans\\\\Development\\\\ReactClientApp\\\\src\\\\core\\\\message\\\\components\\\\messageToast.jsx\";\nimport React, { Component } from \"react\";\nimport { messageService } from \"../messageService\";\nimport { Toast } from \"primereact/toast\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass MessageToast extends Component {\n  constructor(...args) {\n    super(...args);\n    this.life = 3000;\n    this.messages = [];\n    /**\r\n     * Clean up the messages from collection, hide the messages in UI.\r\n     */\n    this.cleanupMessages = () => {\n      this.message = [];\n      if (this.msgs) this.msgs.clear();\n    };\n  }\n  componentDidMount() {\n    this.subscription = messageService.get().subscribe(message => {\n      if (message && message.modalType === \"toast\") {\n        if (message.content !== \"IsEmitNotify\") {\n          //\n          // Save the received message into temporary message collection.\n          //\n          this.messages.push(message);\n          if (message.isEmit) {\n            //\n            // Show the messages in the snackbars message boxes.\n            //\n            this.showMessages();\n          }\n        } else {\n          this.showMessages();\n        }\n      }\n    });\n  }\n  showMessages() {\n    if (this.messages.length > 0) {\n      let primengMessages = [];\n      this.messages.map(message => primengMessages.push({\n        severity: message.messageType,\n        life: this.life,\n        detail: message.content\n      }));\n      this.msgs.show(primengMessages);\n      this.messages = [];\n    }\n  }\n  componentWillUnmount() {\n    // unsubscribe to ensure no memory leaks\n    this.subscription.unsubscribe();\n  }\n  render() {\n    return /*#__PURE__*/_jsxDEV(React.Fragment, {\n      children: /*#__PURE__*/_jsxDEV(Toast, {\n        ref: el => this.msgs = el\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this);\n  }\n}\nexport default MessageToast;", "map": {"version": 3, "names": ["React", "Component", "messageService", "Toast", "jsxDEV", "_jsxDEV", "MessageToast", "constructor", "args", "life", "messages", "cleanupMessages", "message", "msgs", "clear", "componentDidMount", "subscription", "get", "subscribe", "modalType", "content", "push", "isEmit", "showMessages", "length", "primengMessages", "map", "severity", "messageType", "detail", "show", "componentWillUnmount", "unsubscribe", "render", "Fragment", "children", "ref", "el", "fileName", "_jsxFileName", "lineNumber", "columnNumber"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/core/message/components/messageToast.jsx"], "sourcesContent": ["import React, { Component } from \"react\";\r\nimport { messageService } from \"../messageService\";\r\nimport { Toast } from \"primereact/toast\";\r\n\r\nclass MessageToast extends Component {\r\n  life = 3000;\r\n  messages = [];\r\n\r\n  componentDidMount() {\r\n    this.subscription = messageService.get().subscribe((message) => {\r\n      if (message && message.modalType === \"toast\") {\r\n        if (message.content !== \"IsEmitNotify\") {\r\n          //\r\n          // Save the received message into temporary message collection.\r\n          //\r\n          this.messages.push(message);\r\n          if (message.isEmit) {\r\n            //\r\n            // Show the messages in the snackbars message boxes.\r\n            //\r\n            this.showMessages();\r\n          }\r\n        } else {\r\n          this.showMessages();\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  showMessages() {\r\n    if (this.messages.length > 0) {\r\n      let primengMessages = [];\r\n      this.messages.map((message) =>\r\n        primengMessages.push({\r\n          severity: message.messageType,\r\n          life: this.life,\r\n          detail: message.content,\r\n        })\r\n      );\r\n      this.msgs.show(primengMessages);\r\n      this.messages = [];\r\n    }\r\n  }\r\n\r\n  componentWillUnmount() {\r\n    // unsubscribe to ensure no memory leaks\r\n    this.subscription.unsubscribe();\r\n  }\r\n\r\n  /**\r\n   * Clean up the messages from collection, hide the messages in UI.\r\n   */\r\n  cleanupMessages = () => {\r\n    this.message = [];\r\n    if (this.msgs) this.msgs.clear();\r\n  };\r\n\r\n  render() {\r\n    return (\r\n      <React.Fragment>\r\n        <Toast ref={(el) => (this.msgs = el)} />\r\n      </React.Fragment>\r\n    );\r\n  }\r\n}\r\n\r\nexport default MessageToast;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,KAAK,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,MAAMC,YAAY,SAASL,SAAS,CAAC;EAAAM,YAAA,GAAAC,IAAA;IAAA,SAAAA,IAAA;IAAA,KACnCC,IAAI,GAAG,IAAI;IAAA,KACXC,QAAQ,GAAG,EAAE;IA2Cb;AACF;AACA;IAFE,KAGAC,eAAe,GAAG,MAAM;MACtB,IAAI,CAACC,OAAO,GAAG,EAAE;MACjB,IAAI,IAAI,CAACC,IAAI,EAAE,IAAI,CAACA,IAAI,CAACC,KAAK,CAAC,CAAC;IAClC,CAAC;EAAA;EA/CDC,iBAAiBA,CAAA,EAAG;IAClB,IAAI,CAACC,YAAY,GAAGd,cAAc,CAACe,GAAG,CAAC,CAAC,CAACC,SAAS,CAAEN,OAAO,IAAK;MAC9D,IAAIA,OAAO,IAAIA,OAAO,CAACO,SAAS,KAAK,OAAO,EAAE;QAC5C,IAAIP,OAAO,CAACQ,OAAO,KAAK,cAAc,EAAE;UACtC;UACA;UACA;UACA,IAAI,CAACV,QAAQ,CAACW,IAAI,CAACT,OAAO,CAAC;UAC3B,IAAIA,OAAO,CAACU,MAAM,EAAE;YAClB;YACA;YACA;YACA,IAAI,CAACC,YAAY,CAAC,CAAC;UACrB;QACF,CAAC,MAAM;UACL,IAAI,CAACA,YAAY,CAAC,CAAC;QACrB;MACF;IACF,CAAC,CAAC;EACJ;EAEAA,YAAYA,CAAA,EAAG;IACb,IAAI,IAAI,CAACb,QAAQ,CAACc,MAAM,GAAG,CAAC,EAAE;MAC5B,IAAIC,eAAe,GAAG,EAAE;MACxB,IAAI,CAACf,QAAQ,CAACgB,GAAG,CAAEd,OAAO,IACxBa,eAAe,CAACJ,IAAI,CAAC;QACnBM,QAAQ,EAAEf,OAAO,CAACgB,WAAW;QAC7BnB,IAAI,EAAE,IAAI,CAACA,IAAI;QACfoB,MAAM,EAAEjB,OAAO,CAACQ;MAClB,CAAC,CACH,CAAC;MACD,IAAI,CAACP,IAAI,CAACiB,IAAI,CAACL,eAAe,CAAC;MAC/B,IAAI,CAACf,QAAQ,GAAG,EAAE;IACpB;EACF;EAEAqB,oBAAoBA,CAAA,EAAG;IACrB;IACA,IAAI,CAACf,YAAY,CAACgB,WAAW,CAAC,CAAC;EACjC;EAUAC,MAAMA,CAAA,EAAG;IACP,oBACE5B,OAAA,CAACL,KAAK,CAACkC,QAAQ;MAAAC,QAAA,eACb9B,OAAA,CAACF,KAAK;QAACiC,GAAG,EAAGC,EAAE,IAAM,IAAI,CAACxB,IAAI,GAAGwB;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CAAC;EAErB;AACF;AAEA,eAAenC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}