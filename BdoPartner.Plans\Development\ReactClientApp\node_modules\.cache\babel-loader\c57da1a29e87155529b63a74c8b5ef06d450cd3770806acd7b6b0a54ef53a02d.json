{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport { PrimeReactContext } from 'primereact/api';\nimport { useMergeProps, usePrevious, useStyle, useResizeListener, useEventListener, useUpdateEffect } from 'primereact/hooks';\nimport { SpinnerIcon } from 'primereact/icons/spinner';\nimport { DomHandler, ObjectUtils, classNames, IconUtils } from 'primereact/utils';\nimport { ComponentBase } from 'primereact/componentbase';\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nfunction toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction toPropertyKey(t) {\n  var i = toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nfunction _defineProperty(e, r, t) {\n  return (r = toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _arrayWithHoles(r) {\n  if (Array.isArray(r)) return r;\n}\nfunction _iterableToArrayLimit(r, l) {\n  var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"];\n  if (null != t) {\n    var e,\n      n,\n      i,\n      u,\n      a = [],\n      f = !0,\n      o = !1;\n    try {\n      if (i = (t = t.call(r)).next, 0 === l) {\n        if (Object(t) !== t) return;\n        f = !1;\n      } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0);\n    } catch (r) {\n      o = !0, n = r;\n    } finally {\n      try {\n        if (!f && null != t[\"return\"] && (u = t[\"return\"](), Object(u) !== u)) return;\n      } finally {\n        if (o) throw n;\n      }\n    }\n    return a;\n  }\n}\nfunction _arrayLikeToArray(r, a) {\n  (null == a || a > r.length) && (a = r.length);\n  for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e];\n  return n;\n}\nfunction _unsupportedIterableToArray(r, a) {\n  if (r) {\n    if (\"string\" == typeof r) return _arrayLikeToArray(r, a);\n    var t = {}.toString.call(r).slice(8, -1);\n    return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0;\n  }\n}\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _slicedToArray(r, e) {\n  return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest();\n}\nvar styles = \"\\n.p-virtualscroller {\\n    position: relative;\\n    overflow: auto;\\n    contain: strict;\\n    transform: translateZ(0);\\n    will-change: scroll-position;\\n    outline: 0 none;\\n}\\n\\n.p-virtualscroller-content {\\n    position: absolute;\\n    top: 0;\\n    left: 0;\\n    /*contain: content;*/\\n    min-height: 100%;\\n    min-width: 100%;\\n    will-change: transform;\\n}\\n\\n.p-virtualscroller-spacer {\\n    position: absolute;\\n    top: 0;\\n    left: 0;\\n    height: 1px;\\n    width: 1px;\\n    transform-origin: 0 0;\\n    pointer-events: none;\\n}\\n\\n.p-virtualscroller-loader {\\n    position: sticky;\\n    top: 0;\\n    left: 0;\\n    width: 100%;\\n    height: 100%;\\n}\\n\\n.p-virtualscroller-loader.p-component-overlay {\\n    display: flex;\\n    align-items: center;\\n    justify-content: center;\\n}\\n\\n.p-virtualscroller-loading-icon {\\n    font-size: 2rem;\\n}\\n\\n.p-virtualscroller-horizontal > .p-virtualscroller-content {\\n    display: flex;\\n}\\n\\n/* Inline */\\n.p-virtualscroller-inline .p-virtualscroller-content {\\n    position: static;\\n}\\n\";\nvar VirtualScrollerBase = ComponentBase.extend({\n  defaultProps: {\n    __TYPE: 'VirtualScroller',\n    __parentMetadata: null,\n    id: null,\n    style: null,\n    className: null,\n    tabIndex: 0,\n    items: null,\n    itemSize: 0,\n    scrollHeight: null,\n    scrollWidth: null,\n    orientation: 'vertical',\n    step: 0,\n    numToleratedItems: null,\n    delay: 0,\n    resizeDelay: 10,\n    appendOnly: false,\n    inline: false,\n    lazy: false,\n    disabled: false,\n    loaderDisabled: false,\n    loadingIcon: null,\n    columns: null,\n    loading: undefined,\n    autoSize: false,\n    showSpacer: true,\n    showLoader: false,\n    loadingTemplate: null,\n    loaderIconTemplate: null,\n    itemTemplate: null,\n    contentTemplate: null,\n    onScroll: null,\n    onScrollIndexChange: null,\n    onLazyLoad: null,\n    children: undefined\n  },\n  css: {\n    styles: styles\n  }\n});\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nvar VirtualScroller = /*#__PURE__*/React.memo(/*#__PURE__*/React.forwardRef(function (inProps, ref) {\n  var mergeProps = useMergeProps();\n  var context = React.useContext(PrimeReactContext);\n  var props = VirtualScrollerBase.getProps(inProps, context);\n  var prevProps = usePrevious(inProps) || {};\n  var vertical = props.orientation === 'vertical';\n  var horizontal = props.orientation === 'horizontal';\n  var both = props.orientation === 'both';\n  var _React$useState = React.useState(both ? {\n      rows: 0,\n      cols: 0\n    } : 0),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    firstState = _React$useState2[0],\n    setFirstState = _React$useState2[1];\n  var _React$useState3 = React.useState(both ? {\n      rows: 0,\n      cols: 0\n    } : 0),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    lastState = _React$useState4[0],\n    setLastState = _React$useState4[1];\n  var _React$useState5 = React.useState(0),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    pageState = _React$useState6[0],\n    setPageState = _React$useState6[1];\n  var _React$useState7 = React.useState(both ? {\n      rows: 0,\n      cols: 0\n    } : 0),\n    _React$useState8 = _slicedToArray(_React$useState7, 2),\n    numItemsInViewportState = _React$useState8[0],\n    setNumItemsInViewportState = _React$useState8[1];\n  var _React$useState9 = React.useState(props.numToleratedItems),\n    _React$useState10 = _slicedToArray(_React$useState9, 2),\n    numToleratedItemsState = _React$useState10[0],\n    setNumToleratedItemsState = _React$useState10[1];\n  var _React$useState11 = React.useState(props.loading || false),\n    _React$useState12 = _slicedToArray(_React$useState11, 2),\n    loadingState = _React$useState12[0],\n    setLoadingState = _React$useState12[1];\n  var _React$useState13 = React.useState([]),\n    _React$useState14 = _slicedToArray(_React$useState13, 2),\n    loaderArrState = _React$useState14[0],\n    setLoaderArrState = _React$useState14[1];\n  var _VirtualScrollerBase$ = VirtualScrollerBase.setMetaData({\n      props: props,\n      state: {\n        first: firstState,\n        last: lastState,\n        page: pageState,\n        numItemsInViewport: numItemsInViewportState,\n        numToleratedItems: numToleratedItemsState,\n        loading: loadingState,\n        loaderArr: loaderArrState\n      }\n    }),\n    ptm = _VirtualScrollerBase$.ptm;\n  useStyle(VirtualScrollerBase.css.styles, {\n    name: 'virtualscroller'\n  });\n  var elementRef = React.useRef(null);\n  var _contentRef = React.useRef(null);\n  var _spacerRef = React.useRef(null);\n  var _stickyRef = React.useRef(null);\n  var lastScrollPos = React.useRef(both ? {\n    top: 0,\n    left: 0\n  } : 0);\n  var scrollTimeout = React.useRef(null);\n  var resizeTimeout = React.useRef(null);\n  var contentStyle = React.useRef({});\n  var spacerStyle = React.useRef({});\n  var defaultWidth = React.useRef(null);\n  var defaultHeight = React.useRef(null);\n  var defaultContentWidth = React.useRef(null);\n  var defaultContentHeight = React.useRef(null);\n  var isItemRangeChanged = React.useRef(false);\n  var lazyLoadState = React.useRef(null);\n  var viewInitialized = React.useRef(false);\n  var _useResizeListener = useResizeListener({\n      listener: function listener(event) {\n        return onResize();\n      },\n      when: !props.disabled\n    }),\n    _useResizeListener2 = _slicedToArray(_useResizeListener, 1),\n    bindWindowResizeListener = _useResizeListener2[0];\n  var _useEventListener = useEventListener({\n      target: 'window',\n      type: 'orientationchange',\n      listener: function listener(event) {\n        return onResize();\n      },\n      when: !props.disabled\n    }),\n    _useEventListener2 = _slicedToArray(_useEventListener, 1),\n    bindOrientationChangeListener = _useEventListener2[0];\n  var getElementRef = function getElementRef() {\n    return elementRef;\n  };\n  var getPageByFirst = function getPageByFirst(first) {\n    return Math.floor((first + numToleratedItemsState * 4) / (props.step || 1));\n  };\n  var setContentElement = function setContentElement(element) {\n    _contentRef.current = element || _contentRef.current || DomHandler.findSingle(elementRef.current, '.p-virtualscroller-content');\n  };\n  var isPageChanged = function isPageChanged(first) {\n    return props.step ? pageState !== getPageByFirst(first) : true;\n  };\n  var scrollTo = function scrollTo(options) {\n    lastScrollPos.current = both ? {\n      top: 0,\n      left: 0\n    } : 0;\n    elementRef.current && elementRef.current.scrollTo(options);\n  };\n  var scrollToIndex = function scrollToIndex(index) {\n    var behavior = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'auto';\n    var _calculateNumItems = calculateNumItems(),\n      numToleratedItems = _calculateNumItems.numToleratedItems;\n    var contentPos = getContentPosition();\n    var calculateFirst = function calculateFirst() {\n      var _index = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n      var _numT = arguments.length > 1 ? arguments[1] : undefined;\n      return _index <= _numT ? 0 : _index;\n    };\n    var calculateCoord = function calculateCoord(_first, _size, _cpos) {\n      return _first * _size + _cpos;\n    };\n    var scrollToItem = function scrollToItem() {\n      var left = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n      var top = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n      return scrollTo({\n        left: left,\n        top: top,\n        behavior: behavior\n      });\n    };\n    var newFirst = both ? {\n      rows: 0,\n      cols: 0\n    } : 0;\n    var isRangeChanged = false;\n    if (both) {\n      newFirst = {\n        rows: calculateFirst(index[0], numToleratedItems[0]),\n        cols: calculateFirst(index[1], numToleratedItems[1])\n      };\n      scrollToItem(calculateCoord(newFirst.cols, props.itemSize[1], contentPos.left), calculateCoord(newFirst.rows, props.itemSize[0], contentPos.top));\n      isRangeChanged = firstState.rows !== newFirst.rows || firstState.cols !== newFirst.cols;\n    } else {\n      newFirst = calculateFirst(index, numToleratedItems);\n      horizontal ? scrollToItem(calculateCoord(newFirst, props.itemSize, contentPos.left), 0) : scrollToItem(0, calculateCoord(newFirst, props.itemSize, contentPos.top));\n      isRangeChanged = firstState !== newFirst;\n    }\n    isItemRangeChanged.current = isRangeChanged;\n    setFirstState(newFirst);\n  };\n  var scrollInView = function scrollInView(index, to) {\n    var behavior = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'auto';\n    if (to) {\n      var _getRenderedRange = getRenderedRange(),\n        first = _getRenderedRange.first,\n        viewport = _getRenderedRange.viewport;\n      var scrollToItem = function scrollToItem() {\n        var left = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n        var top = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n        return scrollTo({\n          left: left,\n          top: top,\n          behavior: behavior\n        });\n      };\n      var isToStart = to === 'to-start';\n      var isToEnd = to === 'to-end';\n      if (isToStart) {\n        if (both) {\n          if (viewport.first.rows - first.rows > index[0]) {\n            scrollToItem(viewport.first.cols * props.itemSize[1], (viewport.first.rows - 1) * props.itemSize[0]);\n          } else if (viewport.first.cols - first.cols > index[1]) {\n            scrollToItem((viewport.first.cols - 1) * props.itemSize[1], viewport.first.rows * props.itemSize[0]);\n          }\n        } else if (viewport.first - first > index) {\n          var pos = (viewport.first - 1) * props.itemSize;\n          horizontal ? scrollToItem(pos, 0) : scrollToItem(0, pos);\n        }\n      } else if (isToEnd) {\n        if (both) {\n          if (viewport.last.rows - first.rows <= index[0] + 1) {\n            scrollToItem(viewport.first.cols * props.itemSize[1], (viewport.first.rows + 1) * props.itemSize[0]);\n          } else if (viewport.last.cols - first.cols <= index[1] + 1) {\n            scrollToItem((viewport.first.cols + 1) * props.itemSize[1], viewport.first.rows * props.itemSize[0]);\n          }\n        } else if (viewport.last - first <= index + 1) {\n          var _pos2 = (viewport.first + 1) * props.itemSize;\n          horizontal ? scrollToItem(_pos2, 0) : scrollToItem(0, _pos2);\n        }\n      }\n    } else {\n      scrollToIndex(index, behavior);\n    }\n  };\n  var getRows = function getRows() {\n    return loadingState ? props.loaderDisabled ? loaderArrState : [] : loadedItems();\n  };\n  var getColumns = function getColumns() {\n    if (props.columns && both || horizontal) {\n      return loadingState && props.loaderDisabled ? both ? loaderArrState[0] : loaderArrState : props.columns.slice(both ? firstState.cols : firstState, both ? lastState.cols : lastState);\n    }\n    return props.columns;\n  };\n  var getRenderedRange = function getRenderedRange() {\n    var calculateFirstInViewport = function calculateFirstInViewport(_pos, _size) {\n      return Math.floor(_pos / (_size || _pos));\n    };\n    var firstInViewport = firstState;\n    var lastInViewport = 0;\n    if (elementRef.current) {\n      var _elementRef$current = elementRef.current,\n        scrollTop = _elementRef$current.scrollTop,\n        scrollLeft = _elementRef$current.scrollLeft;\n      if (both) {\n        firstInViewport = {\n          rows: calculateFirstInViewport(scrollTop, props.itemSize[0]),\n          cols: calculateFirstInViewport(scrollLeft, props.itemSize[1])\n        };\n        lastInViewport = {\n          rows: firstInViewport.rows + numItemsInViewportState.rows,\n          cols: firstInViewport.cols + numItemsInViewportState.cols\n        };\n      } else {\n        var scrollPos = horizontal ? scrollLeft : scrollTop;\n        firstInViewport = calculateFirstInViewport(scrollPos, props.itemSize);\n        lastInViewport = firstInViewport + numItemsInViewportState;\n      }\n    }\n    return {\n      first: firstState,\n      last: lastState,\n      viewport: {\n        first: firstInViewport,\n        last: lastInViewport\n      }\n    };\n  };\n  var calculateNumItems = function calculateNumItems() {\n    var contentPos = getContentPosition();\n    var contentWidth = elementRef.current ? elementRef.current.offsetWidth - contentPos.left : 0;\n    var contentHeight = elementRef.current ? elementRef.current.offsetHeight - contentPos.top : 0;\n    var calculateNumItemsInViewport = function calculateNumItemsInViewport(_contentSize, _itemSize) {\n      return Math.ceil(_contentSize / (_itemSize || _contentSize));\n    };\n    var calculateNumToleratedItems = function calculateNumToleratedItems(_numItems) {\n      return Math.ceil(_numItems / 2);\n    };\n    var numItemsInViewport = both ? {\n      rows: calculateNumItemsInViewport(contentHeight, props.itemSize[0]),\n      cols: calculateNumItemsInViewport(contentWidth, props.itemSize[1])\n    } : calculateNumItemsInViewport(horizontal ? contentWidth : contentHeight, props.itemSize);\n    var numToleratedItems = numToleratedItemsState || (both ? [calculateNumToleratedItems(numItemsInViewport.rows), calculateNumToleratedItems(numItemsInViewport.cols)] : calculateNumToleratedItems(numItemsInViewport));\n    return {\n      numItemsInViewport: numItemsInViewport,\n      numToleratedItems: numToleratedItems\n    };\n  };\n  var calculateOptions = function calculateOptions() {\n    var _calculateNumItems2 = calculateNumItems(),\n      numItemsInViewport = _calculateNumItems2.numItemsInViewport,\n      numToleratedItems = _calculateNumItems2.numToleratedItems;\n    var calculateLast = function calculateLast(_first, _num, _numT) {\n      var _isCols = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n      return getLast(_first + _num + (_first < _numT ? 2 : 3) * _numT, _isCols);\n    };\n    var last = both ? {\n      rows: calculateLast(firstState.rows, numItemsInViewport.rows, numToleratedItems[0]),\n      cols: calculateLast(firstState.cols, numItemsInViewport.cols, numToleratedItems[1], true)\n    } : calculateLast(firstState, numItemsInViewport, numToleratedItems);\n    setNumItemsInViewportState(numItemsInViewport);\n    setNumToleratedItemsState(numToleratedItems);\n    setLastState(last);\n    if (props.showLoader) {\n      setLoaderArrState(both ? Array.from({\n        length: numItemsInViewport.rows\n      }).map(function () {\n        return Array.from({\n          length: numItemsInViewport.cols\n        });\n      }) : Array.from({\n        length: numItemsInViewport\n      }));\n    }\n    if (props.lazy) {\n      Promise.resolve().then(function () {\n        lazyLoadState.current = {\n          first: props.step ? both ? {\n            rows: 0,\n            cols: firstState.cols\n          } : 0 : firstState,\n          last: Math.min(props.step ? props.step : last, (props.items || []).length)\n        };\n        props.onLazyLoad && props.onLazyLoad(lazyLoadState.current);\n      });\n    }\n  };\n  var calculateAutoSize = function calculateAutoSize(loading) {\n    if (props.autoSize && !loading) {\n      Promise.resolve().then(function () {\n        if (_contentRef.current) {\n          _contentRef.current.style.minHeight = _contentRef.current.style.minWidth = 'auto';\n          _contentRef.current.style.position = 'relative';\n          elementRef.current.style.contain = 'none';\n\n          /*const [contentWidth, contentHeight] = [DomHandler.getWidth(contentRef.current), DomHandler.getHeight(contentRef.current)];\n           contentWidth !== defaultContentWidth.current && (elementRef.current.style.width = '');\n          contentHeight !== defaultContentHeight.current && (elementRef.current.style.height = '');*/\n\n          var _ref = [DomHandler.getWidth(elementRef.current), DomHandler.getHeight(elementRef.current)],\n            width = _ref[0],\n            height = _ref[1];\n          (both || horizontal) && (elementRef.current.style.width = (width < defaultWidth.current ? width : props.scrollWidth || defaultWidth.current) + 'px');\n          (both || vertical) && (elementRef.current.style.height = (height < defaultHeight.current ? height : props.scrollHeight || defaultHeight.current) + 'px');\n          _contentRef.current.style.minHeight = _contentRef.current.style.minWidth = '';\n          _contentRef.current.style.position = '';\n          elementRef.current.style.contain = '';\n        }\n      });\n    }\n  };\n  var getLast = function getLast() {\n    var _ref2;\n    var last = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n    var isCols = arguments.length > 1 ? arguments[1] : undefined;\n    return props.items ? Math.min(isCols ? ((_ref2 = props.columns || props.items[0]) === null || _ref2 === void 0 ? void 0 : _ref2.length) || 0 : (props.items || []).length, last) : 0;\n  };\n  var getContentPosition = function getContentPosition() {\n    if (_contentRef.current) {\n      var style = getComputedStyle(_contentRef.current);\n      var left = parseFloat(style.paddingLeft) + Math.max(parseFloat(style.left) || 0, 0);\n      var right = parseFloat(style.paddingRight) + Math.max(parseFloat(style.right) || 0, 0);\n      var top = parseFloat(style.paddingTop) + Math.max(parseFloat(style.top) || 0, 0);\n      var bottom = parseFloat(style.paddingBottom) + Math.max(parseFloat(style.bottom) || 0, 0);\n      return {\n        left: left,\n        right: right,\n        top: top,\n        bottom: bottom,\n        x: left + right,\n        y: top + bottom\n      };\n    }\n    return {\n      left: 0,\n      right: 0,\n      top: 0,\n      bottom: 0,\n      x: 0,\n      y: 0\n    };\n  };\n  var setSize = function setSize() {\n    if (elementRef.current) {\n      var parentElement = elementRef.current.parentElement;\n      var width = props.scrollWidth || \"\".concat(elementRef.current.offsetWidth || parentElement.offsetWidth, \"px\");\n      var height = props.scrollHeight || \"\".concat(elementRef.current.offsetHeight || parentElement.offsetHeight, \"px\");\n      var setProp = function setProp(_name, _value) {\n        return elementRef.current.style[_name] = _value;\n      };\n      if (both || horizontal) {\n        setProp('height', height);\n        setProp('width', width);\n      } else {\n        setProp('height', height);\n      }\n    }\n  };\n  var setSpacerSize = function setSpacerSize() {\n    var items = props.items;\n    if (items) {\n      var contentPos = getContentPosition();\n      var setProp = function setProp(_name, _value, _size) {\n        var _cpos = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 0;\n        return spacerStyle.current = _objectSpread(_objectSpread({}, spacerStyle.current), _defineProperty({}, \"\".concat(_name), (_value || []).length * _size + _cpos + 'px'));\n      };\n      if (both) {\n        setProp('height', items, props.itemSize[0], contentPos.y);\n        setProp('width', props.columns || items[1], props.itemSize[1], contentPos.x);\n      } else {\n        horizontal ? setProp('width', props.columns || items, props.itemSize, contentPos.x) : setProp('height', items, props.itemSize, contentPos.y);\n      }\n    }\n  };\n  var setContentPosition = function setContentPosition(pos) {\n    if (_contentRef.current && !props.appendOnly) {\n      var first = pos ? pos.first : firstState;\n      var calculateTranslateVal = function calculateTranslateVal(_first, _size) {\n        return _first * _size;\n      };\n      var setTransform = function setTransform() {\n        var _x = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n        var _y = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n        _stickyRef.current && (_stickyRef.current.style.top = \"-\".concat(_y, \"px\"));\n        contentStyle.current = _objectSpread(_objectSpread({}, contentStyle.current), {\n          transform: \"translate3d(\".concat(_x, \"px, \").concat(_y, \"px, 0)\")\n        });\n      };\n      if (both) {\n        setTransform(calculateTranslateVal(first.cols, props.itemSize[1]), calculateTranslateVal(first.rows, props.itemSize[0]));\n      } else {\n        var translateVal = calculateTranslateVal(first, props.itemSize);\n        horizontal ? setTransform(translateVal, 0) : setTransform(0, translateVal);\n      }\n    }\n  };\n  var onScrollPositionChange = function onScrollPositionChange(event) {\n    var target = event.target;\n    var contentPos = getContentPosition();\n    var calculateScrollPos = function calculateScrollPos(_pos, _cpos) {\n      return _pos ? _pos > _cpos ? _pos - _cpos : _pos : 0;\n    };\n    var calculateCurrentIndex = function calculateCurrentIndex(_pos, _size) {\n      return Math.floor(_pos / (_size || _pos));\n    };\n    var calculateTriggerIndex = function calculateTriggerIndex(_currentIndex, _first, _last, _num, _numT, _isScrollDownOrRight) {\n      return _currentIndex <= _numT ? _numT : _isScrollDownOrRight ? _last - _num - _numT : _first + _numT - 1;\n    };\n    var calculateFirst = function calculateFirst(_currentIndex, _triggerIndex, _first, _last, _num, _numT, _isScrollDownOrRight) {\n      if (_currentIndex <= _numT) {\n        return 0;\n      }\n      return Math.max(0, _isScrollDownOrRight ? _currentIndex < _triggerIndex ? _first : _currentIndex - _numT : _currentIndex > _triggerIndex ? _first : _currentIndex - 2 * _numT);\n    };\n    var calculateLast = function calculateLast(_currentIndex, _first, _last, _num, _numT, _isCols) {\n      var lastValue = _first + _num + 2 * _numT;\n      if (_currentIndex >= _numT) {\n        lastValue = lastValue + (_numT + 1);\n      }\n      return getLast(lastValue, _isCols);\n    };\n    var scrollTop = calculateScrollPos(target.scrollTop, contentPos.top);\n    var scrollLeft = calculateScrollPos(target.scrollLeft, contentPos.left);\n    var newFirst = both ? {\n      rows: 0,\n      cols: 0\n    } : 0;\n    var newLast = lastState;\n    var isRangeChanged = false;\n    var newScrollPos = lastScrollPos.current;\n    if (both) {\n      var isScrollDown = lastScrollPos.current.top <= scrollTop;\n      var isScrollRight = lastScrollPos.current.left <= scrollLeft;\n      if (!props.appendOnly || props.appendOnly && (isScrollDown || isScrollRight)) {\n        var currentIndex = {\n          rows: calculateCurrentIndex(scrollTop, props.itemSize[0]),\n          cols: calculateCurrentIndex(scrollLeft, props.itemSize[1])\n        };\n        var triggerIndex = {\n          rows: calculateTriggerIndex(currentIndex.rows, firstState.rows, lastState.rows, numItemsInViewportState.rows, numToleratedItemsState[0], isScrollDown),\n          cols: calculateTriggerIndex(currentIndex.cols, firstState.cols, lastState.cols, numItemsInViewportState.cols, numToleratedItemsState[1], isScrollRight)\n        };\n        newFirst = {\n          rows: calculateFirst(currentIndex.rows, triggerIndex.rows, firstState.rows, lastState.rows, numItemsInViewportState.rows, numToleratedItemsState[0], isScrollDown),\n          cols: calculateFirst(currentIndex.cols, triggerIndex.cols, firstState.cols, lastState.cols, numItemsInViewportState.cols, numToleratedItemsState[1], isScrollRight)\n        };\n        newLast = {\n          rows: calculateLast(currentIndex.rows, newFirst.rows, lastState.rows, numItemsInViewportState.rows, numToleratedItemsState[0]),\n          cols: calculateLast(currentIndex.cols, newFirst.cols, lastState.cols, numItemsInViewportState.cols, numToleratedItemsState[1], true)\n        };\n        isRangeChanged = newFirst.rows !== firstState.rows || newLast.rows !== lastState.rows || newFirst.cols !== firstState.cols || newLast.cols !== lastState.cols || isItemRangeChanged.current;\n        newScrollPos = {\n          top: scrollTop,\n          left: scrollLeft\n        };\n      }\n    } else {\n      var scrollPos = horizontal ? scrollLeft : scrollTop;\n      var isScrollDownOrRight = lastScrollPos.current <= scrollPos;\n      if (!props.appendOnly || props.appendOnly && isScrollDownOrRight) {\n        var _currentIndex2 = calculateCurrentIndex(scrollPos, props.itemSize);\n        var _triggerIndex2 = calculateTriggerIndex(_currentIndex2, firstState, lastState, numItemsInViewportState, numToleratedItemsState, isScrollDownOrRight);\n        newFirst = calculateFirst(_currentIndex2, _triggerIndex2, firstState, lastState, numItemsInViewportState, numToleratedItemsState, isScrollDownOrRight);\n        newLast = calculateLast(_currentIndex2, newFirst, lastState, numItemsInViewportState, numToleratedItemsState);\n        isRangeChanged = newFirst !== firstState || newLast !== lastState || isItemRangeChanged.current;\n        newScrollPos = scrollPos;\n      }\n    }\n    return {\n      first: newFirst,\n      last: newLast,\n      isRangeChanged: isRangeChanged,\n      scrollPos: newScrollPos\n    };\n  };\n  var onScrollChange = function onScrollChange(event) {\n    var _onScrollPositionChan = onScrollPositionChange(event),\n      first = _onScrollPositionChan.first,\n      last = _onScrollPositionChan.last,\n      isRangeChanged = _onScrollPositionChan.isRangeChanged,\n      scrollPos = _onScrollPositionChan.scrollPos;\n    if (isRangeChanged) {\n      var newState = {\n        first: first,\n        last: last\n      };\n      setContentPosition(newState);\n      setFirstState(first);\n      setLastState(last);\n      lastScrollPos.current = scrollPos;\n      props.onScrollIndexChange && props.onScrollIndexChange(newState);\n      if (props.lazy && isPageChanged(first)) {\n        var newLazyLoadState = {\n          first: props.step ? Math.min(getPageByFirst(first) * props.step, (props.items || []).length - props.step) : first,\n          last: Math.min(props.step ? (getPageByFirst(first) + 1) * props.step : last, (props.items || []).length)\n        };\n        var isLazyStateChanged = !lazyLoadState.current || lazyLoadState.current.first !== newLazyLoadState.first || lazyLoadState.current.last !== newLazyLoadState.last;\n        isLazyStateChanged && props.onLazyLoad && props.onLazyLoad(newLazyLoadState);\n        lazyLoadState.current = newLazyLoadState;\n      }\n    }\n  };\n  var _onScroll = function onScroll(event) {\n    props.onScroll && props.onScroll(event);\n    if (props.delay) {\n      if (scrollTimeout.current) {\n        clearTimeout(scrollTimeout.current);\n      }\n      if (isPageChanged(firstState)) {\n        if (!loadingState && props.showLoader) {\n          var _onScrollPositionChan2 = onScrollPositionChange(event),\n            isRangeChanged = _onScrollPositionChan2.isRangeChanged;\n          var changed = isRangeChanged || (props.step ? isPageChanged(firstState) : false);\n          changed && setLoadingState(true);\n        }\n        scrollTimeout.current = setTimeout(function () {\n          onScrollChange(event);\n          if (loadingState && props.showLoader && (!props.lazy || props.loading === undefined)) {\n            setLoadingState(false);\n            setPageState(getPageByFirst(firstState));\n          }\n        }, props.delay);\n      }\n    } else {\n      onScrollChange(event);\n    }\n  };\n  var onResize = function onResize() {\n    if (resizeTimeout.current) {\n      clearTimeout(resizeTimeout.current);\n    }\n    resizeTimeout.current = setTimeout(function () {\n      if (elementRef.current) {\n        var _ref3 = [DomHandler.getWidth(elementRef.current), DomHandler.getHeight(elementRef.current)],\n          width = _ref3[0],\n          height = _ref3[1];\n        var isDiffWidth = width !== defaultWidth.current,\n          isDiffHeight = height !== defaultHeight.current;\n        var reinit = both ? isDiffWidth || isDiffHeight : horizontal ? isDiffWidth : vertical ? isDiffHeight : false;\n        if (reinit) {\n          setNumToleratedItemsState(props.numToleratedItems);\n          defaultWidth.current = width;\n          defaultHeight.current = height;\n          defaultContentWidth.current = DomHandler.getWidth(_contentRef.current);\n          defaultContentHeight.current = DomHandler.getHeight(_contentRef.current);\n        }\n      }\n    }, props.resizeDelay);\n  };\n  var getOptions = function getOptions(renderedIndex) {\n    var count = (props.items || []).length;\n    var index = both ? firstState.rows + renderedIndex : firstState + renderedIndex;\n    return {\n      index: index,\n      count: count,\n      first: index === 0,\n      last: index === count - 1,\n      even: index % 2 === 0,\n      odd: index % 2 !== 0,\n      props: props\n    };\n  };\n  var loaderOptions = function loaderOptions(index, extOptions) {\n    var count = loaderArrState.length || 0;\n    return _objectSpread({\n      index: index,\n      count: count,\n      first: index === 0,\n      last: index === count - 1,\n      even: index % 2 === 0,\n      odd: index % 2 !== 0,\n      props: props\n    }, extOptions);\n  };\n  var loadedItems = function loadedItems() {\n    var items = props.items;\n    if (items && !loadingState) {\n      if (both) {\n        return items.slice(props.appendOnly ? 0 : firstState.rows, lastState.rows).map(function (item) {\n          return props.columns ? item : item.slice(props.appendOnly ? 0 : firstState.cols, lastState.cols);\n        });\n      } else if (horizontal && props.columns) {\n        return items;\n      }\n      return items.slice(props.appendOnly ? 0 : firstState, lastState);\n    }\n    return [];\n  };\n  var viewInit = function viewInit() {\n    if (elementRef.current && isVisible()) {\n      setContentElement(_contentRef.current);\n      init();\n      bindWindowResizeListener();\n      bindOrientationChangeListener();\n      defaultWidth.current = DomHandler.getWidth(elementRef.current);\n      defaultHeight.current = DomHandler.getHeight(elementRef.current);\n      defaultContentWidth.current = DomHandler.getWidth(_contentRef.current);\n      defaultContentHeight.current = DomHandler.getHeight(_contentRef.current);\n    }\n  };\n  var init = function init() {\n    if (!props.disabled && isVisible()) {\n      setSize();\n      calculateOptions();\n      setSpacerSize();\n    }\n  };\n  var isVisible = function isVisible() {\n    if (DomHandler.isVisible(elementRef.current)) {\n      var rect = elementRef.current.getBoundingClientRect();\n      return rect.width > 0 && rect.height > 0;\n    }\n    return false;\n  };\n  React.useEffect(function () {\n    if (!viewInitialized.current && isVisible()) {\n      viewInit();\n      viewInitialized.current = true;\n    }\n  });\n  useUpdateEffect(function () {\n    init();\n  }, [props.itemSize, props.scrollHeight, props.scrollWidth]);\n  useUpdateEffect(function () {\n    if (props.numToleratedItems !== numToleratedItemsState) {\n      setNumToleratedItemsState(props.numToleratedItems);\n    }\n  }, [props.numToleratedItems]);\n  useUpdateEffect(function () {\n    if (props.numToleratedItems === numToleratedItemsState) {\n      init(); // reinit after resizing\n    }\n  }, [numToleratedItemsState]);\n  useUpdateEffect(function () {\n    // Check if the previous/current rows array exists\n    var prevRowsExist = prevProps.items !== undefined && prevProps.items !== null;\n    var currentRowsExist = props.items !== undefined && props.items !== null;\n\n    // Get the length of the previous/current rows array, or 0 if it doesn't exist\n    var prevRowsLength = prevRowsExist ? prevProps.items.length : 0;\n    var currentRowsLength = currentRowsExist ? props.items.length : 0;\n\n    // Check if the length of the rows arrays has changed\n    var valuesChanged = prevRowsLength !== currentRowsLength;\n\n    // If both is true, we also need to check the lengths of the first element (assuming it's a matrix)\n    if (both && !valuesChanged) {\n      // Get the length of the columns or 0\n      var prevColumnsLength = prevRowsExist && prevProps.items.length > 0 ? prevProps.items[0].length : 0;\n      var currentColumnsLength = currentRowsExist && props.items.length > 0 ? props.items[0].length : 0;\n\n      // Check if the length of the columns has changed\n      valuesChanged = prevColumnsLength !== currentColumnsLength;\n    }\n\n    // If the previous items array doesn't exist or if any values have changed, call the init function\n    if (!prevRowsExist || valuesChanged) {\n      init();\n    }\n    var loading = loadingState;\n    if (props.lazy && prevProps.loading !== props.loading && props.loading !== loadingState) {\n      setLoadingState(props.loading);\n      loading = props.loading;\n    }\n    calculateAutoSize(loading);\n  });\n  useUpdateEffect(function () {\n    lastScrollPos.current = both ? {\n      top: 0,\n      left: 0\n    } : 0;\n  }, [props.orientation]);\n  React.useImperativeHandle(ref, function () {\n    return {\n      props: props,\n      getElementRef: getElementRef,\n      scrollTo: scrollTo,\n      scrollToIndex: scrollToIndex,\n      scrollInView: scrollInView,\n      getRenderedRange: getRenderedRange\n    };\n  });\n  var createLoaderItem = function createLoaderItem(index) {\n    var extOptions = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var options = loaderOptions(index, extOptions);\n    var content = ObjectUtils.getJSXElement(props.loadingTemplate, options);\n    return /*#__PURE__*/React.createElement(React.Fragment, {\n      key: index\n    }, content);\n  };\n  var createLoader = function createLoader() {\n    var iconClassName = 'p-virtualscroller-loading-icon';\n    var loadingIconProps = mergeProps({\n      className: iconClassName\n    }, ptm('loadingIcon'));\n    var icon = props.loadingIcon || /*#__PURE__*/React.createElement(SpinnerIcon, _extends({}, loadingIconProps, {\n      spin: true\n    }));\n    var loadingIcon = IconUtils.getJSXIcon(icon, _objectSpread({}, loadingIconProps), {\n      props: props\n    });\n    if (!props.loaderDisabled && props.showLoader && loadingState) {\n      var _className = classNames('p-virtualscroller-loader', {\n        'p-component-overlay': !props.loadingTemplate\n      });\n      var _content = loadingIcon;\n      if (props.loadingTemplate) {\n        _content = loaderArrState.map(function (_, index) {\n          return createLoaderItem(index, both && {\n            numCols: numItemsInViewportState.cols\n          });\n        });\n      } else if (props.loaderIconTemplate) {\n        var defaultContentOptions = {\n          iconClassName: iconClassName,\n          element: _content,\n          props: props\n        };\n        _content = ObjectUtils.getJSXElement(props.loaderIconTemplate, defaultContentOptions);\n      }\n      var loaderProps = mergeProps({\n        className: _className\n      }, ptm('loader'));\n      return /*#__PURE__*/React.createElement(\"div\", loaderProps, _content);\n    }\n    return null;\n  };\n  var createSpacer = function createSpacer() {\n    if (props.showSpacer) {\n      var spacerProps = mergeProps({\n        ref: _spacerRef,\n        style: spacerStyle.current,\n        className: 'p-virtualscroller-spacer'\n      }, ptm('spacer'));\n      return /*#__PURE__*/React.createElement(\"div\", spacerProps);\n    }\n    return null;\n  };\n  var createItem = function createItem(item, index) {\n    var options = getOptions(index);\n    var content = ObjectUtils.getJSXElement(props.itemTemplate, item, options);\n    return /*#__PURE__*/React.createElement(React.Fragment, {\n      key: options.index\n    }, content);\n  };\n  var createItems = function createItems() {\n    var items = loadedItems();\n    return items.map(createItem);\n  };\n  var createContent = function createContent() {\n    var items = createItems();\n    var className = classNames('p-virtualscroller-content', {\n      'p-virtualscroller-loading': loadingState\n    });\n    var contentProps = mergeProps({\n      ref: _contentRef,\n      style: contentStyle.current,\n      className: className\n    }, ptm('content'));\n    var content = /*#__PURE__*/React.createElement(\"div\", contentProps, items);\n    if (props.contentTemplate) {\n      var defaultOptions = {\n        style: contentStyle.current,\n        className: className,\n        spacerStyle: spacerStyle.current,\n        contentRef: function contentRef(el) {\n          return _contentRef.current = ObjectUtils.getRefElement(el);\n        },\n        spacerRef: function spacerRef(el) {\n          return _spacerRef.current = ObjectUtils.getRefElement(el);\n        },\n        stickyRef: function stickyRef(el) {\n          return _stickyRef.current = ObjectUtils.getRefElement(el);\n        },\n        items: loadedItems(),\n        getItemOptions: function getItemOptions(index) {\n          return getOptions(index);\n        },\n        children: items,\n        element: content,\n        props: props,\n        loading: loadingState,\n        getLoaderOptions: function getLoaderOptions(index, ext) {\n          return loaderOptions(index, ext);\n        },\n        loadingTemplate: props.loadingTemplate,\n        itemSize: props.itemSize,\n        rows: getRows(),\n        columns: getColumns(),\n        vertical: vertical,\n        horizontal: horizontal,\n        both: both\n      };\n      return ObjectUtils.getJSXElement(props.contentTemplate, defaultOptions);\n    }\n    return content;\n  };\n  if (props.disabled) {\n    var _content2 = ObjectUtils.getJSXElement(props.contentTemplate, {\n      items: props.items,\n      rows: props.items,\n      columns: props.columns\n    });\n    return /*#__PURE__*/React.createElement(React.Fragment, null, props.children, _content2);\n  }\n  var className = classNames('p-virtualscroller', {\n    'p-virtualscroller-inline': props.inline,\n    'p-virtualscroller-both p-both-scroll': both,\n    'p-virtualscroller-horizontal p-horizontal-scroll': horizontal\n  }, props.className);\n  var loader = createLoader();\n  var content = createContent();\n  var spacer = createSpacer();\n  var rootProps = mergeProps({\n    ref: elementRef,\n    className: className,\n    tabIndex: props.tabIndex,\n    style: props.style,\n    onScroll: function onScroll(e) {\n      return _onScroll(e);\n    }\n  }, VirtualScrollerBase.getOtherProps(props), ptm('root'));\n  return /*#__PURE__*/React.createElement(\"div\", rootProps, content, spacer, loader);\n}));\nVirtualScroller.displayName = 'VirtualScroller';\nexport { VirtualScroller };", "map": {"version": 3, "names": ["React", "PrimeReactContext", "useMergeProps", "usePrevious", "useStyle", "useResizeListener", "useEventListener", "useUpdateEffect", "SpinnerIcon", "<PERSON><PERSON><PERSON><PERSON>", "ObjectUtils", "classNames", "IconUtils", "ComponentBase", "_extends", "Object", "assign", "bind", "n", "e", "arguments", "length", "t", "r", "hasOwnProperty", "call", "apply", "_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "toPrimitive", "i", "TypeError", "String", "Number", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_defineProperty", "defineProperty", "value", "enumerable", "configurable", "writable", "_arrayWithHoles", "Array", "isArray", "_iterableToArrayLimit", "l", "u", "a", "f", "next", "done", "push", "_arrayLikeToArray", "_unsupportedIterableToArray", "toString", "slice", "name", "from", "test", "_nonIterableRest", "_slicedToArray", "styles", "VirtualScrollerBase", "extend", "defaultProps", "__TYPE", "__parentMetadata", "id", "style", "className", "tabIndex", "items", "itemSize", "scrollHeight", "scrollWidth", "orientation", "step", "numToleratedItems", "delay", "resizeDelay", "appendOnly", "inline", "lazy", "disabled", "loaderDisabled", "loadingIcon", "columns", "loading", "undefined", "autoSize", "showSpacer", "<PERSON><PERSON><PERSON><PERSON>", "loadingTemplate", "loaderIconTemplate", "itemTemplate", "contentTemplate", "onScroll", "onScrollIndexChange", "onLazyLoad", "children", "css", "ownKeys", "keys", "getOwnPropertySymbols", "filter", "getOwnPropertyDescriptor", "_objectSpread", "for<PERSON>ach", "getOwnPropertyDescriptors", "defineProperties", "VirtualScroller", "memo", "forwardRef", "inProps", "ref", "mergeProps", "context", "useContext", "props", "getProps", "prevProps", "vertical", "horizontal", "both", "_React$useState", "useState", "rows", "cols", "_React$useState2", "firstState", "setFirstState", "_React$useState3", "_React$useState4", "lastState", "setLastState", "_React$useState5", "_React$useState6", "pageState", "setPageState", "_React$useState7", "_React$useState8", "numItemsInViewportState", "setNumItemsInViewportState", "_React$useState9", "_React$useState10", "numToleratedItemsState", "setNumToleratedItemsState", "_React$useState11", "_React$useState12", "loadingState", "setLoadingState", "_React$useState13", "_React$useState14", "loaderArrState", "setLoaderArrState", "_VirtualScrollerBase$", "setMetaData", "state", "first", "last", "page", "numItemsInViewport", "loaderArr", "ptm", "elementRef", "useRef", "_contentRef", "_spacerRef", "_stickyRef", "lastScrollPos", "top", "left", "scrollTimeout", "resizeTimeout", "contentStyle", "spacerStyle", "defaultWidth", "defaultHeight", "defaultContentWidth", "defaultContentHeight", "isItemRangeChanged", "lazyLoadState", "viewInitialized", "_useResizeListener", "listener", "event", "onResize", "when", "_useResizeListener2", "bindWindowResizeListener", "_useEventListener", "target", "type", "_useEventListener2", "bindOrientationChangeListener", "getElementRef", "getPageByFirst", "Math", "floor", "setContentElement", "element", "current", "findSingle", "isPageChanged", "scrollTo", "options", "scrollToIndex", "index", "behavior", "_calculateNumItems", "calculateNumItems", "contentPos", "getContentPosition", "calculateFirst", "_index", "_numT", "calculateCoord", "_first", "_size", "_cpos", "scrollToItem", "newFirst", "isRangeChanged", "scrollInView", "to", "_getR<PERSON>ed<PERSON><PERSON>e", "getRenderedRange", "viewport", "isToStart", "isToEnd", "pos", "_pos2", "getRows", "loadedItems", "getColumns", "calculateFirstInViewport", "_pos", "firstInViewport", "lastInViewport", "_elementRef$current", "scrollTop", "scrollLeft", "scrollPos", "contentWidth", "offsetWidth", "contentHeight", "offsetHeight", "calculateNumItemsInViewport", "_contentSize", "_itemSize", "ceil", "calculateNumToleratedItems", "_numItems", "calculateOptions", "_calculateNumItems2", "calculateLast", "_num", "_isCols", "getLast", "map", "Promise", "resolve", "then", "min", "calculateAutoSize", "minHeight", "min<PERSON><PERSON><PERSON>", "position", "contain", "_ref", "getWidth", "getHeight", "width", "height", "_ref2", "isCols", "getComputedStyle", "parseFloat", "paddingLeft", "max", "right", "paddingRight", "paddingTop", "bottom", "paddingBottom", "x", "y", "setSize", "parentElement", "concat", "setProp", "_name", "_value", "setSpacerSize", "setContentPosition", "calculateTranslateVal", "setTransform", "_x", "_y", "transform", "translateVal", "onScrollPositionChange", "calculateScrollPos", "calculateCurrentIndex", "calculateTriggerIndex", "_currentIndex", "_last", "_isScrollDownOrRight", "_triggerIndex", "lastValue", "newLast", "newScrollPos", "isScrollDown", "isScrollRight", "currentIndex", "triggerIndex", "isScrollDownOrRight", "_currentIndex2", "_triggerIndex2", "onScrollChange", "_onScrollPositionChan", "newState", "newLazyLoadState", "isLazyStateChanged", "_onScroll", "clearTimeout", "_onScrollPositionChan2", "changed", "setTimeout", "_ref3", "isDiffWidth", "isDiffHeight", "reinit", "getOptions", "renderedIndex", "count", "even", "odd", "loaderOptions", "extOptions", "item", "viewInit", "isVisible", "init", "rect", "getBoundingClientRect", "useEffect", "prevRowsExist", "currentRowsExist", "prevRowsLength", "currentRowsLength", "valuesChanged", "prevColumnsLength", "currentColumnsLength", "useImperativeHandle", "createLoaderItem", "content", "getJSXElement", "createElement", "Fragment", "key", "createLoader", "iconClassName", "loadingIconProps", "icon", "spin", "getJSXIcon", "_className", "_content", "_", "numCols", "defaultContentOptions", "loaderProps", "createSpacer", "spacerProps", "createItem", "createItems", "createContent", "contentProps", "defaultOptions", "contentRef", "el", "getRefElement", "spacerRef", "stickyRef", "getItemOptions", "getLoaderOptions", "ext", "_content2", "loader", "spacer", "rootProps", "getOtherProps", "displayName"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/node_modules/primereact/virtualscroller/virtualscroller.esm.js"], "sourcesContent": ["'use client';\nimport * as React from 'react';\nimport { PrimeReactContext } from 'primereact/api';\nimport { useMergeProps, usePrevious, useStyle, useResizeListener, useEventListener, useUpdateEffect } from 'primereact/hooks';\nimport { SpinnerIcon } from 'primereact/icons/spinner';\nimport { DomHandler, ObjectUtils, classNames, IconUtils } from 'primereact/utils';\nimport { ComponentBase } from 'primereact/componentbase';\n\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\n\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\n\nfunction toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\n\nfunction toPropertyKey(t) {\n  var i = toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\n\nfunction _defineProperty(e, r, t) {\n  return (r = toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\n\nfunction _arrayWithHoles(r) {\n  if (Array.isArray(r)) return r;\n}\n\nfunction _iterableToArrayLimit(r, l) {\n  var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"];\n  if (null != t) {\n    var e,\n      n,\n      i,\n      u,\n      a = [],\n      f = !0,\n      o = !1;\n    try {\n      if (i = (t = t.call(r)).next, 0 === l) {\n        if (Object(t) !== t) return;\n        f = !1;\n      } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0);\n    } catch (r) {\n      o = !0, n = r;\n    } finally {\n      try {\n        if (!f && null != t[\"return\"] && (u = t[\"return\"](), Object(u) !== u)) return;\n      } finally {\n        if (o) throw n;\n      }\n    }\n    return a;\n  }\n}\n\nfunction _arrayLikeToArray(r, a) {\n  (null == a || a > r.length) && (a = r.length);\n  for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e];\n  return n;\n}\n\nfunction _unsupportedIterableToArray(r, a) {\n  if (r) {\n    if (\"string\" == typeof r) return _arrayLikeToArray(r, a);\n    var t = {}.toString.call(r).slice(8, -1);\n    return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0;\n  }\n}\n\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nfunction _slicedToArray(r, e) {\n  return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest();\n}\n\nvar styles = \"\\n.p-virtualscroller {\\n    position: relative;\\n    overflow: auto;\\n    contain: strict;\\n    transform: translateZ(0);\\n    will-change: scroll-position;\\n    outline: 0 none;\\n}\\n\\n.p-virtualscroller-content {\\n    position: absolute;\\n    top: 0;\\n    left: 0;\\n    /*contain: content;*/\\n    min-height: 100%;\\n    min-width: 100%;\\n    will-change: transform;\\n}\\n\\n.p-virtualscroller-spacer {\\n    position: absolute;\\n    top: 0;\\n    left: 0;\\n    height: 1px;\\n    width: 1px;\\n    transform-origin: 0 0;\\n    pointer-events: none;\\n}\\n\\n.p-virtualscroller-loader {\\n    position: sticky;\\n    top: 0;\\n    left: 0;\\n    width: 100%;\\n    height: 100%;\\n}\\n\\n.p-virtualscroller-loader.p-component-overlay {\\n    display: flex;\\n    align-items: center;\\n    justify-content: center;\\n}\\n\\n.p-virtualscroller-loading-icon {\\n    font-size: 2rem;\\n}\\n\\n.p-virtualscroller-horizontal > .p-virtualscroller-content {\\n    display: flex;\\n}\\n\\n/* Inline */\\n.p-virtualscroller-inline .p-virtualscroller-content {\\n    position: static;\\n}\\n\";\nvar VirtualScrollerBase = ComponentBase.extend({\n  defaultProps: {\n    __TYPE: 'VirtualScroller',\n    __parentMetadata: null,\n    id: null,\n    style: null,\n    className: null,\n    tabIndex: 0,\n    items: null,\n    itemSize: 0,\n    scrollHeight: null,\n    scrollWidth: null,\n    orientation: 'vertical',\n    step: 0,\n    numToleratedItems: null,\n    delay: 0,\n    resizeDelay: 10,\n    appendOnly: false,\n    inline: false,\n    lazy: false,\n    disabled: false,\n    loaderDisabled: false,\n    loadingIcon: null,\n    columns: null,\n    loading: undefined,\n    autoSize: false,\n    showSpacer: true,\n    showLoader: false,\n    loadingTemplate: null,\n    loaderIconTemplate: null,\n    itemTemplate: null,\n    contentTemplate: null,\n    onScroll: null,\n    onScrollIndexChange: null,\n    onLazyLoad: null,\n    children: undefined\n  },\n  css: {\n    styles: styles\n  }\n});\n\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nvar VirtualScroller = /*#__PURE__*/React.memo(/*#__PURE__*/React.forwardRef(function (inProps, ref) {\n  var mergeProps = useMergeProps();\n  var context = React.useContext(PrimeReactContext);\n  var props = VirtualScrollerBase.getProps(inProps, context);\n  var prevProps = usePrevious(inProps) || {};\n  var vertical = props.orientation === 'vertical';\n  var horizontal = props.orientation === 'horizontal';\n  var both = props.orientation === 'both';\n  var _React$useState = React.useState(both ? {\n      rows: 0,\n      cols: 0\n    } : 0),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    firstState = _React$useState2[0],\n    setFirstState = _React$useState2[1];\n  var _React$useState3 = React.useState(both ? {\n      rows: 0,\n      cols: 0\n    } : 0),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    lastState = _React$useState4[0],\n    setLastState = _React$useState4[1];\n  var _React$useState5 = React.useState(0),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    pageState = _React$useState6[0],\n    setPageState = _React$useState6[1];\n  var _React$useState7 = React.useState(both ? {\n      rows: 0,\n      cols: 0\n    } : 0),\n    _React$useState8 = _slicedToArray(_React$useState7, 2),\n    numItemsInViewportState = _React$useState8[0],\n    setNumItemsInViewportState = _React$useState8[1];\n  var _React$useState9 = React.useState(props.numToleratedItems),\n    _React$useState10 = _slicedToArray(_React$useState9, 2),\n    numToleratedItemsState = _React$useState10[0],\n    setNumToleratedItemsState = _React$useState10[1];\n  var _React$useState11 = React.useState(props.loading || false),\n    _React$useState12 = _slicedToArray(_React$useState11, 2),\n    loadingState = _React$useState12[0],\n    setLoadingState = _React$useState12[1];\n  var _React$useState13 = React.useState([]),\n    _React$useState14 = _slicedToArray(_React$useState13, 2),\n    loaderArrState = _React$useState14[0],\n    setLoaderArrState = _React$useState14[1];\n  var _VirtualScrollerBase$ = VirtualScrollerBase.setMetaData({\n      props: props,\n      state: {\n        first: firstState,\n        last: lastState,\n        page: pageState,\n        numItemsInViewport: numItemsInViewportState,\n        numToleratedItems: numToleratedItemsState,\n        loading: loadingState,\n        loaderArr: loaderArrState\n      }\n    }),\n    ptm = _VirtualScrollerBase$.ptm;\n  useStyle(VirtualScrollerBase.css.styles, {\n    name: 'virtualscroller'\n  });\n  var elementRef = React.useRef(null);\n  var _contentRef = React.useRef(null);\n  var _spacerRef = React.useRef(null);\n  var _stickyRef = React.useRef(null);\n  var lastScrollPos = React.useRef(both ? {\n    top: 0,\n    left: 0\n  } : 0);\n  var scrollTimeout = React.useRef(null);\n  var resizeTimeout = React.useRef(null);\n  var contentStyle = React.useRef({});\n  var spacerStyle = React.useRef({});\n  var defaultWidth = React.useRef(null);\n  var defaultHeight = React.useRef(null);\n  var defaultContentWidth = React.useRef(null);\n  var defaultContentHeight = React.useRef(null);\n  var isItemRangeChanged = React.useRef(false);\n  var lazyLoadState = React.useRef(null);\n  var viewInitialized = React.useRef(false);\n  var _useResizeListener = useResizeListener({\n      listener: function listener(event) {\n        return onResize();\n      },\n      when: !props.disabled\n    }),\n    _useResizeListener2 = _slicedToArray(_useResizeListener, 1),\n    bindWindowResizeListener = _useResizeListener2[0];\n  var _useEventListener = useEventListener({\n      target: 'window',\n      type: 'orientationchange',\n      listener: function listener(event) {\n        return onResize();\n      },\n      when: !props.disabled\n    }),\n    _useEventListener2 = _slicedToArray(_useEventListener, 1),\n    bindOrientationChangeListener = _useEventListener2[0];\n  var getElementRef = function getElementRef() {\n    return elementRef;\n  };\n  var getPageByFirst = function getPageByFirst(first) {\n    return Math.floor((first + numToleratedItemsState * 4) / (props.step || 1));\n  };\n  var setContentElement = function setContentElement(element) {\n    _contentRef.current = element || _contentRef.current || DomHandler.findSingle(elementRef.current, '.p-virtualscroller-content');\n  };\n  var isPageChanged = function isPageChanged(first) {\n    return props.step ? pageState !== getPageByFirst(first) : true;\n  };\n  var scrollTo = function scrollTo(options) {\n    lastScrollPos.current = both ? {\n      top: 0,\n      left: 0\n    } : 0;\n    elementRef.current && elementRef.current.scrollTo(options);\n  };\n  var scrollToIndex = function scrollToIndex(index) {\n    var behavior = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'auto';\n    var _calculateNumItems = calculateNumItems(),\n      numToleratedItems = _calculateNumItems.numToleratedItems;\n    var contentPos = getContentPosition();\n    var calculateFirst = function calculateFirst() {\n      var _index = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n      var _numT = arguments.length > 1 ? arguments[1] : undefined;\n      return _index <= _numT ? 0 : _index;\n    };\n    var calculateCoord = function calculateCoord(_first, _size, _cpos) {\n      return _first * _size + _cpos;\n    };\n    var scrollToItem = function scrollToItem() {\n      var left = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n      var top = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n      return scrollTo({\n        left: left,\n        top: top,\n        behavior: behavior\n      });\n    };\n    var newFirst = both ? {\n      rows: 0,\n      cols: 0\n    } : 0;\n    var isRangeChanged = false;\n    if (both) {\n      newFirst = {\n        rows: calculateFirst(index[0], numToleratedItems[0]),\n        cols: calculateFirst(index[1], numToleratedItems[1])\n      };\n      scrollToItem(calculateCoord(newFirst.cols, props.itemSize[1], contentPos.left), calculateCoord(newFirst.rows, props.itemSize[0], contentPos.top));\n      isRangeChanged = firstState.rows !== newFirst.rows || firstState.cols !== newFirst.cols;\n    } else {\n      newFirst = calculateFirst(index, numToleratedItems);\n      horizontal ? scrollToItem(calculateCoord(newFirst, props.itemSize, contentPos.left), 0) : scrollToItem(0, calculateCoord(newFirst, props.itemSize, contentPos.top));\n      isRangeChanged = firstState !== newFirst;\n    }\n    isItemRangeChanged.current = isRangeChanged;\n    setFirstState(newFirst);\n  };\n  var scrollInView = function scrollInView(index, to) {\n    var behavior = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'auto';\n    if (to) {\n      var _getRenderedRange = getRenderedRange(),\n        first = _getRenderedRange.first,\n        viewport = _getRenderedRange.viewport;\n      var scrollToItem = function scrollToItem() {\n        var left = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n        var top = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n        return scrollTo({\n          left: left,\n          top: top,\n          behavior: behavior\n        });\n      };\n      var isToStart = to === 'to-start';\n      var isToEnd = to === 'to-end';\n      if (isToStart) {\n        if (both) {\n          if (viewport.first.rows - first.rows > index[0]) {\n            scrollToItem(viewport.first.cols * props.itemSize[1], (viewport.first.rows - 1) * props.itemSize[0]);\n          } else if (viewport.first.cols - first.cols > index[1]) {\n            scrollToItem((viewport.first.cols - 1) * props.itemSize[1], viewport.first.rows * props.itemSize[0]);\n          }\n        } else if (viewport.first - first > index) {\n          var pos = (viewport.first - 1) * props.itemSize;\n          horizontal ? scrollToItem(pos, 0) : scrollToItem(0, pos);\n        }\n      } else if (isToEnd) {\n        if (both) {\n          if (viewport.last.rows - first.rows <= index[0] + 1) {\n            scrollToItem(viewport.first.cols * props.itemSize[1], (viewport.first.rows + 1) * props.itemSize[0]);\n          } else if (viewport.last.cols - first.cols <= index[1] + 1) {\n            scrollToItem((viewport.first.cols + 1) * props.itemSize[1], viewport.first.rows * props.itemSize[0]);\n          }\n        } else if (viewport.last - first <= index + 1) {\n          var _pos2 = (viewport.first + 1) * props.itemSize;\n          horizontal ? scrollToItem(_pos2, 0) : scrollToItem(0, _pos2);\n        }\n      }\n    } else {\n      scrollToIndex(index, behavior);\n    }\n  };\n  var getRows = function getRows() {\n    return loadingState ? props.loaderDisabled ? loaderArrState : [] : loadedItems();\n  };\n  var getColumns = function getColumns() {\n    if (props.columns && both || horizontal) {\n      return loadingState && props.loaderDisabled ? both ? loaderArrState[0] : loaderArrState : props.columns.slice(both ? firstState.cols : firstState, both ? lastState.cols : lastState);\n    }\n    return props.columns;\n  };\n  var getRenderedRange = function getRenderedRange() {\n    var calculateFirstInViewport = function calculateFirstInViewport(_pos, _size) {\n      return Math.floor(_pos / (_size || _pos));\n    };\n    var firstInViewport = firstState;\n    var lastInViewport = 0;\n    if (elementRef.current) {\n      var _elementRef$current = elementRef.current,\n        scrollTop = _elementRef$current.scrollTop,\n        scrollLeft = _elementRef$current.scrollLeft;\n      if (both) {\n        firstInViewport = {\n          rows: calculateFirstInViewport(scrollTop, props.itemSize[0]),\n          cols: calculateFirstInViewport(scrollLeft, props.itemSize[1])\n        };\n        lastInViewport = {\n          rows: firstInViewport.rows + numItemsInViewportState.rows,\n          cols: firstInViewport.cols + numItemsInViewportState.cols\n        };\n      } else {\n        var scrollPos = horizontal ? scrollLeft : scrollTop;\n        firstInViewport = calculateFirstInViewport(scrollPos, props.itemSize);\n        lastInViewport = firstInViewport + numItemsInViewportState;\n      }\n    }\n    return {\n      first: firstState,\n      last: lastState,\n      viewport: {\n        first: firstInViewport,\n        last: lastInViewport\n      }\n    };\n  };\n  var calculateNumItems = function calculateNumItems() {\n    var contentPos = getContentPosition();\n    var contentWidth = elementRef.current ? elementRef.current.offsetWidth - contentPos.left : 0;\n    var contentHeight = elementRef.current ? elementRef.current.offsetHeight - contentPos.top : 0;\n    var calculateNumItemsInViewport = function calculateNumItemsInViewport(_contentSize, _itemSize) {\n      return Math.ceil(_contentSize / (_itemSize || _contentSize));\n    };\n    var calculateNumToleratedItems = function calculateNumToleratedItems(_numItems) {\n      return Math.ceil(_numItems / 2);\n    };\n    var numItemsInViewport = both ? {\n      rows: calculateNumItemsInViewport(contentHeight, props.itemSize[0]),\n      cols: calculateNumItemsInViewport(contentWidth, props.itemSize[1])\n    } : calculateNumItemsInViewport(horizontal ? contentWidth : contentHeight, props.itemSize);\n    var numToleratedItems = numToleratedItemsState || (both ? [calculateNumToleratedItems(numItemsInViewport.rows), calculateNumToleratedItems(numItemsInViewport.cols)] : calculateNumToleratedItems(numItemsInViewport));\n    return {\n      numItemsInViewport: numItemsInViewport,\n      numToleratedItems: numToleratedItems\n    };\n  };\n  var calculateOptions = function calculateOptions() {\n    var _calculateNumItems2 = calculateNumItems(),\n      numItemsInViewport = _calculateNumItems2.numItemsInViewport,\n      numToleratedItems = _calculateNumItems2.numToleratedItems;\n    var calculateLast = function calculateLast(_first, _num, _numT) {\n      var _isCols = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n      return getLast(_first + _num + (_first < _numT ? 2 : 3) * _numT, _isCols);\n    };\n    var last = both ? {\n      rows: calculateLast(firstState.rows, numItemsInViewport.rows, numToleratedItems[0]),\n      cols: calculateLast(firstState.cols, numItemsInViewport.cols, numToleratedItems[1], true)\n    } : calculateLast(firstState, numItemsInViewport, numToleratedItems);\n    setNumItemsInViewportState(numItemsInViewport);\n    setNumToleratedItemsState(numToleratedItems);\n    setLastState(last);\n    if (props.showLoader) {\n      setLoaderArrState(both ? Array.from({\n        length: numItemsInViewport.rows\n      }).map(function () {\n        return Array.from({\n          length: numItemsInViewport.cols\n        });\n      }) : Array.from({\n        length: numItemsInViewport\n      }));\n    }\n    if (props.lazy) {\n      Promise.resolve().then(function () {\n        lazyLoadState.current = {\n          first: props.step ? both ? {\n            rows: 0,\n            cols: firstState.cols\n          } : 0 : firstState,\n          last: Math.min(props.step ? props.step : last, (props.items || []).length)\n        };\n        props.onLazyLoad && props.onLazyLoad(lazyLoadState.current);\n      });\n    }\n  };\n  var calculateAutoSize = function calculateAutoSize(loading) {\n    if (props.autoSize && !loading) {\n      Promise.resolve().then(function () {\n        if (_contentRef.current) {\n          _contentRef.current.style.minHeight = _contentRef.current.style.minWidth = 'auto';\n          _contentRef.current.style.position = 'relative';\n          elementRef.current.style.contain = 'none';\n\n          /*const [contentWidth, contentHeight] = [DomHandler.getWidth(contentRef.current), DomHandler.getHeight(contentRef.current)];\n           contentWidth !== defaultContentWidth.current && (elementRef.current.style.width = '');\n          contentHeight !== defaultContentHeight.current && (elementRef.current.style.height = '');*/\n\n          var _ref = [DomHandler.getWidth(elementRef.current), DomHandler.getHeight(elementRef.current)],\n            width = _ref[0],\n            height = _ref[1];\n          (both || horizontal) && (elementRef.current.style.width = (width < defaultWidth.current ? width : props.scrollWidth || defaultWidth.current) + 'px');\n          (both || vertical) && (elementRef.current.style.height = (height < defaultHeight.current ? height : props.scrollHeight || defaultHeight.current) + 'px');\n          _contentRef.current.style.minHeight = _contentRef.current.style.minWidth = '';\n          _contentRef.current.style.position = '';\n          elementRef.current.style.contain = '';\n        }\n      });\n    }\n  };\n  var getLast = function getLast() {\n    var _ref2;\n    var last = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n    var isCols = arguments.length > 1 ? arguments[1] : undefined;\n    return props.items ? Math.min(isCols ? ((_ref2 = props.columns || props.items[0]) === null || _ref2 === void 0 ? void 0 : _ref2.length) || 0 : (props.items || []).length, last) : 0;\n  };\n  var getContentPosition = function getContentPosition() {\n    if (_contentRef.current) {\n      var style = getComputedStyle(_contentRef.current);\n      var left = parseFloat(style.paddingLeft) + Math.max(parseFloat(style.left) || 0, 0);\n      var right = parseFloat(style.paddingRight) + Math.max(parseFloat(style.right) || 0, 0);\n      var top = parseFloat(style.paddingTop) + Math.max(parseFloat(style.top) || 0, 0);\n      var bottom = parseFloat(style.paddingBottom) + Math.max(parseFloat(style.bottom) || 0, 0);\n      return {\n        left: left,\n        right: right,\n        top: top,\n        bottom: bottom,\n        x: left + right,\n        y: top + bottom\n      };\n    }\n    return {\n      left: 0,\n      right: 0,\n      top: 0,\n      bottom: 0,\n      x: 0,\n      y: 0\n    };\n  };\n  var setSize = function setSize() {\n    if (elementRef.current) {\n      var parentElement = elementRef.current.parentElement;\n      var width = props.scrollWidth || \"\".concat(elementRef.current.offsetWidth || parentElement.offsetWidth, \"px\");\n      var height = props.scrollHeight || \"\".concat(elementRef.current.offsetHeight || parentElement.offsetHeight, \"px\");\n      var setProp = function setProp(_name, _value) {\n        return elementRef.current.style[_name] = _value;\n      };\n      if (both || horizontal) {\n        setProp('height', height);\n        setProp('width', width);\n      } else {\n        setProp('height', height);\n      }\n    }\n  };\n  var setSpacerSize = function setSpacerSize() {\n    var items = props.items;\n    if (items) {\n      var contentPos = getContentPosition();\n      var setProp = function setProp(_name, _value, _size) {\n        var _cpos = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 0;\n        return spacerStyle.current = _objectSpread(_objectSpread({}, spacerStyle.current), _defineProperty({}, \"\".concat(_name), (_value || []).length * _size + _cpos + 'px'));\n      };\n      if (both) {\n        setProp('height', items, props.itemSize[0], contentPos.y);\n        setProp('width', props.columns || items[1], props.itemSize[1], contentPos.x);\n      } else {\n        horizontal ? setProp('width', props.columns || items, props.itemSize, contentPos.x) : setProp('height', items, props.itemSize, contentPos.y);\n      }\n    }\n  };\n  var setContentPosition = function setContentPosition(pos) {\n    if (_contentRef.current && !props.appendOnly) {\n      var first = pos ? pos.first : firstState;\n      var calculateTranslateVal = function calculateTranslateVal(_first, _size) {\n        return _first * _size;\n      };\n      var setTransform = function setTransform() {\n        var _x = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n        var _y = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n        _stickyRef.current && (_stickyRef.current.style.top = \"-\".concat(_y, \"px\"));\n        contentStyle.current = _objectSpread(_objectSpread({}, contentStyle.current), {\n          transform: \"translate3d(\".concat(_x, \"px, \").concat(_y, \"px, 0)\")\n        });\n      };\n      if (both) {\n        setTransform(calculateTranslateVal(first.cols, props.itemSize[1]), calculateTranslateVal(first.rows, props.itemSize[0]));\n      } else {\n        var translateVal = calculateTranslateVal(first, props.itemSize);\n        horizontal ? setTransform(translateVal, 0) : setTransform(0, translateVal);\n      }\n    }\n  };\n  var onScrollPositionChange = function onScrollPositionChange(event) {\n    var target = event.target;\n    var contentPos = getContentPosition();\n    var calculateScrollPos = function calculateScrollPos(_pos, _cpos) {\n      return _pos ? _pos > _cpos ? _pos - _cpos : _pos : 0;\n    };\n    var calculateCurrentIndex = function calculateCurrentIndex(_pos, _size) {\n      return Math.floor(_pos / (_size || _pos));\n    };\n    var calculateTriggerIndex = function calculateTriggerIndex(_currentIndex, _first, _last, _num, _numT, _isScrollDownOrRight) {\n      return _currentIndex <= _numT ? _numT : _isScrollDownOrRight ? _last - _num - _numT : _first + _numT - 1;\n    };\n    var calculateFirst = function calculateFirst(_currentIndex, _triggerIndex, _first, _last, _num, _numT, _isScrollDownOrRight) {\n      if (_currentIndex <= _numT) {\n        return 0;\n      }\n      return Math.max(0, _isScrollDownOrRight ? _currentIndex < _triggerIndex ? _first : _currentIndex - _numT : _currentIndex > _triggerIndex ? _first : _currentIndex - 2 * _numT);\n    };\n    var calculateLast = function calculateLast(_currentIndex, _first, _last, _num, _numT, _isCols) {\n      var lastValue = _first + _num + 2 * _numT;\n      if (_currentIndex >= _numT) {\n        lastValue = lastValue + (_numT + 1);\n      }\n      return getLast(lastValue, _isCols);\n    };\n    var scrollTop = calculateScrollPos(target.scrollTop, contentPos.top);\n    var scrollLeft = calculateScrollPos(target.scrollLeft, contentPos.left);\n    var newFirst = both ? {\n      rows: 0,\n      cols: 0\n    } : 0;\n    var newLast = lastState;\n    var isRangeChanged = false;\n    var newScrollPos = lastScrollPos.current;\n    if (both) {\n      var isScrollDown = lastScrollPos.current.top <= scrollTop;\n      var isScrollRight = lastScrollPos.current.left <= scrollLeft;\n      if (!props.appendOnly || props.appendOnly && (isScrollDown || isScrollRight)) {\n        var currentIndex = {\n          rows: calculateCurrentIndex(scrollTop, props.itemSize[0]),\n          cols: calculateCurrentIndex(scrollLeft, props.itemSize[1])\n        };\n        var triggerIndex = {\n          rows: calculateTriggerIndex(currentIndex.rows, firstState.rows, lastState.rows, numItemsInViewportState.rows, numToleratedItemsState[0], isScrollDown),\n          cols: calculateTriggerIndex(currentIndex.cols, firstState.cols, lastState.cols, numItemsInViewportState.cols, numToleratedItemsState[1], isScrollRight)\n        };\n        newFirst = {\n          rows: calculateFirst(currentIndex.rows, triggerIndex.rows, firstState.rows, lastState.rows, numItemsInViewportState.rows, numToleratedItemsState[0], isScrollDown),\n          cols: calculateFirst(currentIndex.cols, triggerIndex.cols, firstState.cols, lastState.cols, numItemsInViewportState.cols, numToleratedItemsState[1], isScrollRight)\n        };\n        newLast = {\n          rows: calculateLast(currentIndex.rows, newFirst.rows, lastState.rows, numItemsInViewportState.rows, numToleratedItemsState[0]),\n          cols: calculateLast(currentIndex.cols, newFirst.cols, lastState.cols, numItemsInViewportState.cols, numToleratedItemsState[1], true)\n        };\n        isRangeChanged = newFirst.rows !== firstState.rows || newLast.rows !== lastState.rows || newFirst.cols !== firstState.cols || newLast.cols !== lastState.cols || isItemRangeChanged.current;\n        newScrollPos = {\n          top: scrollTop,\n          left: scrollLeft\n        };\n      }\n    } else {\n      var scrollPos = horizontal ? scrollLeft : scrollTop;\n      var isScrollDownOrRight = lastScrollPos.current <= scrollPos;\n      if (!props.appendOnly || props.appendOnly && isScrollDownOrRight) {\n        var _currentIndex2 = calculateCurrentIndex(scrollPos, props.itemSize);\n        var _triggerIndex2 = calculateTriggerIndex(_currentIndex2, firstState, lastState, numItemsInViewportState, numToleratedItemsState, isScrollDownOrRight);\n        newFirst = calculateFirst(_currentIndex2, _triggerIndex2, firstState, lastState, numItemsInViewportState, numToleratedItemsState, isScrollDownOrRight);\n        newLast = calculateLast(_currentIndex2, newFirst, lastState, numItemsInViewportState, numToleratedItemsState);\n        isRangeChanged = newFirst !== firstState || newLast !== lastState || isItemRangeChanged.current;\n        newScrollPos = scrollPos;\n      }\n    }\n    return {\n      first: newFirst,\n      last: newLast,\n      isRangeChanged: isRangeChanged,\n      scrollPos: newScrollPos\n    };\n  };\n  var onScrollChange = function onScrollChange(event) {\n    var _onScrollPositionChan = onScrollPositionChange(event),\n      first = _onScrollPositionChan.first,\n      last = _onScrollPositionChan.last,\n      isRangeChanged = _onScrollPositionChan.isRangeChanged,\n      scrollPos = _onScrollPositionChan.scrollPos;\n    if (isRangeChanged) {\n      var newState = {\n        first: first,\n        last: last\n      };\n      setContentPosition(newState);\n      setFirstState(first);\n      setLastState(last);\n      lastScrollPos.current = scrollPos;\n      props.onScrollIndexChange && props.onScrollIndexChange(newState);\n      if (props.lazy && isPageChanged(first)) {\n        var newLazyLoadState = {\n          first: props.step ? Math.min(getPageByFirst(first) * props.step, (props.items || []).length - props.step) : first,\n          last: Math.min(props.step ? (getPageByFirst(first) + 1) * props.step : last, (props.items || []).length)\n        };\n        var isLazyStateChanged = !lazyLoadState.current || lazyLoadState.current.first !== newLazyLoadState.first || lazyLoadState.current.last !== newLazyLoadState.last;\n        isLazyStateChanged && props.onLazyLoad && props.onLazyLoad(newLazyLoadState);\n        lazyLoadState.current = newLazyLoadState;\n      }\n    }\n  };\n  var _onScroll = function onScroll(event) {\n    props.onScroll && props.onScroll(event);\n    if (props.delay) {\n      if (scrollTimeout.current) {\n        clearTimeout(scrollTimeout.current);\n      }\n      if (isPageChanged(firstState)) {\n        if (!loadingState && props.showLoader) {\n          var _onScrollPositionChan2 = onScrollPositionChange(event),\n            isRangeChanged = _onScrollPositionChan2.isRangeChanged;\n          var changed = isRangeChanged || (props.step ? isPageChanged(firstState) : false);\n          changed && setLoadingState(true);\n        }\n        scrollTimeout.current = setTimeout(function () {\n          onScrollChange(event);\n          if (loadingState && props.showLoader && (!props.lazy || props.loading === undefined)) {\n            setLoadingState(false);\n            setPageState(getPageByFirst(firstState));\n          }\n        }, props.delay);\n      }\n    } else {\n      onScrollChange(event);\n    }\n  };\n  var onResize = function onResize() {\n    if (resizeTimeout.current) {\n      clearTimeout(resizeTimeout.current);\n    }\n    resizeTimeout.current = setTimeout(function () {\n      if (elementRef.current) {\n        var _ref3 = [DomHandler.getWidth(elementRef.current), DomHandler.getHeight(elementRef.current)],\n          width = _ref3[0],\n          height = _ref3[1];\n        var isDiffWidth = width !== defaultWidth.current,\n          isDiffHeight = height !== defaultHeight.current;\n        var reinit = both ? isDiffWidth || isDiffHeight : horizontal ? isDiffWidth : vertical ? isDiffHeight : false;\n        if (reinit) {\n          setNumToleratedItemsState(props.numToleratedItems);\n          defaultWidth.current = width;\n          defaultHeight.current = height;\n          defaultContentWidth.current = DomHandler.getWidth(_contentRef.current);\n          defaultContentHeight.current = DomHandler.getHeight(_contentRef.current);\n        }\n      }\n    }, props.resizeDelay);\n  };\n  var getOptions = function getOptions(renderedIndex) {\n    var count = (props.items || []).length;\n    var index = both ? firstState.rows + renderedIndex : firstState + renderedIndex;\n    return {\n      index: index,\n      count: count,\n      first: index === 0,\n      last: index === count - 1,\n      even: index % 2 === 0,\n      odd: index % 2 !== 0,\n      props: props\n    };\n  };\n  var loaderOptions = function loaderOptions(index, extOptions) {\n    var count = loaderArrState.length || 0;\n    return _objectSpread({\n      index: index,\n      count: count,\n      first: index === 0,\n      last: index === count - 1,\n      even: index % 2 === 0,\n      odd: index % 2 !== 0,\n      props: props\n    }, extOptions);\n  };\n  var loadedItems = function loadedItems() {\n    var items = props.items;\n    if (items && !loadingState) {\n      if (both) {\n        return items.slice(props.appendOnly ? 0 : firstState.rows, lastState.rows).map(function (item) {\n          return props.columns ? item : item.slice(props.appendOnly ? 0 : firstState.cols, lastState.cols);\n        });\n      } else if (horizontal && props.columns) {\n        return items;\n      }\n      return items.slice(props.appendOnly ? 0 : firstState, lastState);\n    }\n    return [];\n  };\n  var viewInit = function viewInit() {\n    if (elementRef.current && isVisible()) {\n      setContentElement(_contentRef.current);\n      init();\n      bindWindowResizeListener();\n      bindOrientationChangeListener();\n      defaultWidth.current = DomHandler.getWidth(elementRef.current);\n      defaultHeight.current = DomHandler.getHeight(elementRef.current);\n      defaultContentWidth.current = DomHandler.getWidth(_contentRef.current);\n      defaultContentHeight.current = DomHandler.getHeight(_contentRef.current);\n    }\n  };\n  var init = function init() {\n    if (!props.disabled && isVisible()) {\n      setSize();\n      calculateOptions();\n      setSpacerSize();\n    }\n  };\n  var isVisible = function isVisible() {\n    if (DomHandler.isVisible(elementRef.current)) {\n      var rect = elementRef.current.getBoundingClientRect();\n      return rect.width > 0 && rect.height > 0;\n    }\n    return false;\n  };\n  React.useEffect(function () {\n    if (!viewInitialized.current && isVisible()) {\n      viewInit();\n      viewInitialized.current = true;\n    }\n  });\n  useUpdateEffect(function () {\n    init();\n  }, [props.itemSize, props.scrollHeight, props.scrollWidth]);\n  useUpdateEffect(function () {\n    if (props.numToleratedItems !== numToleratedItemsState) {\n      setNumToleratedItemsState(props.numToleratedItems);\n    }\n  }, [props.numToleratedItems]);\n  useUpdateEffect(function () {\n    if (props.numToleratedItems === numToleratedItemsState) {\n      init(); // reinit after resizing\n    }\n  }, [numToleratedItemsState]);\n  useUpdateEffect(function () {\n    // Check if the previous/current rows array exists\n    var prevRowsExist = prevProps.items !== undefined && prevProps.items !== null;\n    var currentRowsExist = props.items !== undefined && props.items !== null;\n\n    // Get the length of the previous/current rows array, or 0 if it doesn't exist\n    var prevRowsLength = prevRowsExist ? prevProps.items.length : 0;\n    var currentRowsLength = currentRowsExist ? props.items.length : 0;\n\n    // Check if the length of the rows arrays has changed\n    var valuesChanged = prevRowsLength !== currentRowsLength;\n\n    // If both is true, we also need to check the lengths of the first element (assuming it's a matrix)\n    if (both && !valuesChanged) {\n      // Get the length of the columns or 0\n      var prevColumnsLength = prevRowsExist && prevProps.items.length > 0 ? prevProps.items[0].length : 0;\n      var currentColumnsLength = currentRowsExist && props.items.length > 0 ? props.items[0].length : 0;\n\n      // Check if the length of the columns has changed\n      valuesChanged = prevColumnsLength !== currentColumnsLength;\n    }\n\n    // If the previous items array doesn't exist or if any values have changed, call the init function\n    if (!prevRowsExist || valuesChanged) {\n      init();\n    }\n    var loading = loadingState;\n    if (props.lazy && prevProps.loading !== props.loading && props.loading !== loadingState) {\n      setLoadingState(props.loading);\n      loading = props.loading;\n    }\n    calculateAutoSize(loading);\n  });\n  useUpdateEffect(function () {\n    lastScrollPos.current = both ? {\n      top: 0,\n      left: 0\n    } : 0;\n  }, [props.orientation]);\n  React.useImperativeHandle(ref, function () {\n    return {\n      props: props,\n      getElementRef: getElementRef,\n      scrollTo: scrollTo,\n      scrollToIndex: scrollToIndex,\n      scrollInView: scrollInView,\n      getRenderedRange: getRenderedRange\n    };\n  });\n  var createLoaderItem = function createLoaderItem(index) {\n    var extOptions = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var options = loaderOptions(index, extOptions);\n    var content = ObjectUtils.getJSXElement(props.loadingTemplate, options);\n    return /*#__PURE__*/React.createElement(React.Fragment, {\n      key: index\n    }, content);\n  };\n  var createLoader = function createLoader() {\n    var iconClassName = 'p-virtualscroller-loading-icon';\n    var loadingIconProps = mergeProps({\n      className: iconClassName\n    }, ptm('loadingIcon'));\n    var icon = props.loadingIcon || /*#__PURE__*/React.createElement(SpinnerIcon, _extends({}, loadingIconProps, {\n      spin: true\n    }));\n    var loadingIcon = IconUtils.getJSXIcon(icon, _objectSpread({}, loadingIconProps), {\n      props: props\n    });\n    if (!props.loaderDisabled && props.showLoader && loadingState) {\n      var _className = classNames('p-virtualscroller-loader', {\n        'p-component-overlay': !props.loadingTemplate\n      });\n      var _content = loadingIcon;\n      if (props.loadingTemplate) {\n        _content = loaderArrState.map(function (_, index) {\n          return createLoaderItem(index, both && {\n            numCols: numItemsInViewportState.cols\n          });\n        });\n      } else if (props.loaderIconTemplate) {\n        var defaultContentOptions = {\n          iconClassName: iconClassName,\n          element: _content,\n          props: props\n        };\n        _content = ObjectUtils.getJSXElement(props.loaderIconTemplate, defaultContentOptions);\n      }\n      var loaderProps = mergeProps({\n        className: _className\n      }, ptm('loader'));\n      return /*#__PURE__*/React.createElement(\"div\", loaderProps, _content);\n    }\n    return null;\n  };\n  var createSpacer = function createSpacer() {\n    if (props.showSpacer) {\n      var spacerProps = mergeProps({\n        ref: _spacerRef,\n        style: spacerStyle.current,\n        className: 'p-virtualscroller-spacer'\n      }, ptm('spacer'));\n      return /*#__PURE__*/React.createElement(\"div\", spacerProps);\n    }\n    return null;\n  };\n  var createItem = function createItem(item, index) {\n    var options = getOptions(index);\n    var content = ObjectUtils.getJSXElement(props.itemTemplate, item, options);\n    return /*#__PURE__*/React.createElement(React.Fragment, {\n      key: options.index\n    }, content);\n  };\n  var createItems = function createItems() {\n    var items = loadedItems();\n    return items.map(createItem);\n  };\n  var createContent = function createContent() {\n    var items = createItems();\n    var className = classNames('p-virtualscroller-content', {\n      'p-virtualscroller-loading': loadingState\n    });\n    var contentProps = mergeProps({\n      ref: _contentRef,\n      style: contentStyle.current,\n      className: className\n    }, ptm('content'));\n    var content = /*#__PURE__*/React.createElement(\"div\", contentProps, items);\n    if (props.contentTemplate) {\n      var defaultOptions = {\n        style: contentStyle.current,\n        className: className,\n        spacerStyle: spacerStyle.current,\n        contentRef: function contentRef(el) {\n          return _contentRef.current = ObjectUtils.getRefElement(el);\n        },\n        spacerRef: function spacerRef(el) {\n          return _spacerRef.current = ObjectUtils.getRefElement(el);\n        },\n        stickyRef: function stickyRef(el) {\n          return _stickyRef.current = ObjectUtils.getRefElement(el);\n        },\n        items: loadedItems(),\n        getItemOptions: function getItemOptions(index) {\n          return getOptions(index);\n        },\n        children: items,\n        element: content,\n        props: props,\n        loading: loadingState,\n        getLoaderOptions: function getLoaderOptions(index, ext) {\n          return loaderOptions(index, ext);\n        },\n        loadingTemplate: props.loadingTemplate,\n        itemSize: props.itemSize,\n        rows: getRows(),\n        columns: getColumns(),\n        vertical: vertical,\n        horizontal: horizontal,\n        both: both\n      };\n      return ObjectUtils.getJSXElement(props.contentTemplate, defaultOptions);\n    }\n    return content;\n  };\n  if (props.disabled) {\n    var _content2 = ObjectUtils.getJSXElement(props.contentTemplate, {\n      items: props.items,\n      rows: props.items,\n      columns: props.columns\n    });\n    return /*#__PURE__*/React.createElement(React.Fragment, null, props.children, _content2);\n  }\n  var className = classNames('p-virtualscroller', {\n    'p-virtualscroller-inline': props.inline,\n    'p-virtualscroller-both p-both-scroll': both,\n    'p-virtualscroller-horizontal p-horizontal-scroll': horizontal\n  }, props.className);\n  var loader = createLoader();\n  var content = createContent();\n  var spacer = createSpacer();\n  var rootProps = mergeProps({\n    ref: elementRef,\n    className: className,\n    tabIndex: props.tabIndex,\n    style: props.style,\n    onScroll: function onScroll(e) {\n      return _onScroll(e);\n    }\n  }, VirtualScrollerBase.getOtherProps(props), ptm('root'));\n  return /*#__PURE__*/React.createElement(\"div\", rootProps, content, spacer, loader);\n}));\nVirtualScroller.displayName = 'VirtualScroller';\n\nexport { VirtualScroller };\n"], "mappings": "AAAA,YAAY;;AACZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,iBAAiB,QAAQ,gBAAgB;AAClD,SAASC,aAAa,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,iBAAiB,EAAEC,gBAAgB,EAAEC,eAAe,QAAQ,kBAAkB;AAC7H,SAASC,WAAW,QAAQ,0BAA0B;AACtD,SAASC,UAAU,EAAEC,WAAW,EAAEC,UAAU,EAAEC,SAAS,QAAQ,kBAAkB;AACjF,SAASC,aAAa,QAAQ,0BAA0B;AAExD,SAASC,QAAQA,CAAA,EAAG;EAClB,OAAOA,QAAQ,GAAGC,MAAM,CAACC,MAAM,GAAGD,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUC,CAAC,EAAE;IACpE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MACzC,IAAIG,CAAC,GAAGF,SAAS,CAACD,CAAC,CAAC;MACpB,KAAK,IAAII,CAAC,IAAID,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEE,cAAc,CAACC,IAAI,CAACH,CAAC,EAAEC,CAAC,CAAC,KAAKL,CAAC,CAACK,CAAC,CAAC,GAAGD,CAAC,CAACC,CAAC,CAAC,CAAC;IAClE;IACA,OAAOL,CAAC;EACV,CAAC,EAAEJ,QAAQ,CAACY,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;AACpC;AAEA,SAASO,OAAOA,CAACC,CAAC,EAAE;EAClB,yBAAyB;;EAEzB,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,CAAC,EAAE;IAChG,OAAO,OAAOA,CAAC;EACjB,CAAC,GAAG,UAAUA,CAAC,EAAE;IACf,OAAOA,CAAC,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,CAAC,CAACG,WAAW,KAAKF,MAAM,IAAID,CAAC,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,CAAC;EACrH,CAAC,EAAED,OAAO,CAACC,CAAC,CAAC;AACf;AAEA,SAASK,WAAWA,CAACX,CAAC,EAAEC,CAAC,EAAE;EACzB,IAAI,QAAQ,IAAII,OAAO,CAACL,CAAC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAC1C,IAAIH,CAAC,GAAGG,CAAC,CAACO,MAAM,CAACI,WAAW,CAAC;EAC7B,IAAI,KAAK,CAAC,KAAKd,CAAC,EAAE;IAChB,IAAIe,CAAC,GAAGf,CAAC,CAACM,IAAI,CAACH,CAAC,EAAEC,CAAC,IAAI,SAAS,CAAC;IACjC,IAAI,QAAQ,IAAII,OAAO,CAACO,CAAC,CAAC,EAAE,OAAOA,CAAC;IACpC,MAAM,IAAIC,SAAS,CAAC,8CAA8C,CAAC;EACrE;EACA,OAAO,CAAC,QAAQ,KAAKZ,CAAC,GAAGa,MAAM,GAAGC,MAAM,EAAEf,CAAC,CAAC;AAC9C;AAEA,SAASgB,aAAaA,CAAChB,CAAC,EAAE;EACxB,IAAIY,CAAC,GAAGD,WAAW,CAACX,CAAC,EAAE,QAAQ,CAAC;EAChC,OAAO,QAAQ,IAAIK,OAAO,CAACO,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAC5C;AAEA,SAASK,eAAeA,CAACpB,CAAC,EAAEI,CAAC,EAAED,CAAC,EAAE;EAChC,OAAO,CAACC,CAAC,GAAGe,aAAa,CAACf,CAAC,CAAC,KAAKJ,CAAC,GAAGJ,MAAM,CAACyB,cAAc,CAACrB,CAAC,EAAEI,CAAC,EAAE;IAC/DkB,KAAK,EAAEnB,CAAC;IACRoB,UAAU,EAAE,CAAC,CAAC;IACdC,YAAY,EAAE,CAAC,CAAC;IAChBC,QAAQ,EAAE,CAAC;EACb,CAAC,CAAC,GAAGzB,CAAC,CAACI,CAAC,CAAC,GAAGD,CAAC,EAAEH,CAAC;AAClB;AAEA,SAAS0B,eAAeA,CAACtB,CAAC,EAAE;EAC1B,IAAIuB,KAAK,CAACC,OAAO,CAACxB,CAAC,CAAC,EAAE,OAAOA,CAAC;AAChC;AAEA,SAASyB,qBAAqBA,CAACzB,CAAC,EAAE0B,CAAC,EAAE;EACnC,IAAI3B,CAAC,GAAG,IAAI,IAAIC,CAAC,GAAG,IAAI,GAAG,WAAW,IAAI,OAAOM,MAAM,IAAIN,CAAC,CAACM,MAAM,CAACC,QAAQ,CAAC,IAAIP,CAAC,CAAC,YAAY,CAAC;EAChG,IAAI,IAAI,IAAID,CAAC,EAAE;IACb,IAAIH,CAAC;MACHD,CAAC;MACDgB,CAAC;MACDgB,CAAC;MACDC,CAAC,GAAG,EAAE;MACNC,CAAC,GAAG,CAAC,CAAC;MACNxB,CAAC,GAAG,CAAC,CAAC;IACR,IAAI;MACF,IAAIM,CAAC,GAAG,CAACZ,CAAC,GAAGA,CAAC,CAACG,IAAI,CAACF,CAAC,CAAC,EAAE8B,IAAI,EAAE,CAAC,KAAKJ,CAAC,EAAE;QACrC,IAAIlC,MAAM,CAACO,CAAC,CAAC,KAAKA,CAAC,EAAE;QACrB8B,CAAC,GAAG,CAAC,CAAC;MACR,CAAC,MAAM,OAAO,EAAEA,CAAC,GAAG,CAACjC,CAAC,GAAGe,CAAC,CAACT,IAAI,CAACH,CAAC,CAAC,EAAEgC,IAAI,CAAC,KAAKH,CAAC,CAACI,IAAI,CAACpC,CAAC,CAACsB,KAAK,CAAC,EAAEU,CAAC,CAAC9B,MAAM,KAAK4B,CAAC,CAAC,EAAEG,CAAC,GAAG,CAAC,CAAC,CAAC;IACzF,CAAC,CAAC,OAAO7B,CAAC,EAAE;MACVK,CAAC,GAAG,CAAC,CAAC,EAAEV,CAAC,GAAGK,CAAC;IACf,CAAC,SAAS;MACR,IAAI;QACF,IAAI,CAAC6B,CAAC,IAAI,IAAI,IAAI9B,CAAC,CAAC,QAAQ,CAAC,KAAK4B,CAAC,GAAG5B,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAEP,MAAM,CAACmC,CAAC,CAAC,KAAKA,CAAC,CAAC,EAAE;MACzE,CAAC,SAAS;QACR,IAAItB,CAAC,EAAE,MAAMV,CAAC;MAChB;IACF;IACA,OAAOiC,CAAC;EACV;AACF;AAEA,SAASK,iBAAiBA,CAACjC,CAAC,EAAE4B,CAAC,EAAE;EAC/B,CAAC,IAAI,IAAIA,CAAC,IAAIA,CAAC,GAAG5B,CAAC,CAACF,MAAM,MAAM8B,CAAC,GAAG5B,CAAC,CAACF,MAAM,CAAC;EAC7C,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAED,CAAC,GAAG4B,KAAK,CAACK,CAAC,CAAC,EAAEhC,CAAC,GAAGgC,CAAC,EAAEhC,CAAC,EAAE,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGI,CAAC,CAACJ,CAAC,CAAC;EACrD,OAAOD,CAAC;AACV;AAEA,SAASuC,2BAA2BA,CAAClC,CAAC,EAAE4B,CAAC,EAAE;EACzC,IAAI5B,CAAC,EAAE;IACL,IAAI,QAAQ,IAAI,OAAOA,CAAC,EAAE,OAAOiC,iBAAiB,CAACjC,CAAC,EAAE4B,CAAC,CAAC;IACxD,IAAI7B,CAAC,GAAG,CAAC,CAAC,CAACoC,QAAQ,CAACjC,IAAI,CAACF,CAAC,CAAC,CAACoC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACxC,OAAO,QAAQ,KAAKrC,CAAC,IAAIC,CAAC,CAACQ,WAAW,KAAKT,CAAC,GAAGC,CAAC,CAACQ,WAAW,CAAC6B,IAAI,CAAC,EAAE,KAAK,KAAKtC,CAAC,IAAI,KAAK,KAAKA,CAAC,GAAGwB,KAAK,CAACe,IAAI,CAACtC,CAAC,CAAC,GAAG,WAAW,KAAKD,CAAC,IAAI,0CAA0C,CAACwC,IAAI,CAACxC,CAAC,CAAC,GAAGkC,iBAAiB,CAACjC,CAAC,EAAE4B,CAAC,CAAC,GAAG,KAAK,CAAC;EAC7N;AACF;AAEA,SAASY,gBAAgBA,CAAA,EAAG;EAC1B,MAAM,IAAI5B,SAAS,CAAC,2IAA2I,CAAC;AAClK;AAEA,SAAS6B,cAAcA,CAACzC,CAAC,EAAEJ,CAAC,EAAE;EAC5B,OAAO0B,eAAe,CAACtB,CAAC,CAAC,IAAIyB,qBAAqB,CAACzB,CAAC,EAAEJ,CAAC,CAAC,IAAIsC,2BAA2B,CAAClC,CAAC,EAAEJ,CAAC,CAAC,IAAI4C,gBAAgB,CAAC,CAAC;AACrH;AAEA,IAAIE,MAAM,GAAG,uhCAAuhC;AACpiC,IAAIC,mBAAmB,GAAGrD,aAAa,CAACsD,MAAM,CAAC;EAC7CC,YAAY,EAAE;IACZC,MAAM,EAAE,iBAAiB;IACzBC,gBAAgB,EAAE,IAAI;IACtBC,EAAE,EAAE,IAAI;IACRC,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,IAAI;IACfC,QAAQ,EAAE,CAAC;IACXC,KAAK,EAAE,IAAI;IACXC,QAAQ,EAAE,CAAC;IACXC,YAAY,EAAE,IAAI;IAClBC,WAAW,EAAE,IAAI;IACjBC,WAAW,EAAE,UAAU;IACvBC,IAAI,EAAE,CAAC;IACPC,iBAAiB,EAAE,IAAI;IACvBC,KAAK,EAAE,CAAC;IACRC,WAAW,EAAE,EAAE;IACfC,UAAU,EAAE,KAAK;IACjBC,MAAM,EAAE,KAAK;IACbC,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE,KAAK;IACfC,cAAc,EAAE,KAAK;IACrBC,WAAW,EAAE,IAAI;IACjBC,OAAO,EAAE,IAAI;IACbC,OAAO,EAAEC,SAAS;IAClBC,QAAQ,EAAE,KAAK;IACfC,UAAU,EAAE,IAAI;IAChBC,UAAU,EAAE,KAAK;IACjBC,eAAe,EAAE,IAAI;IACrBC,kBAAkB,EAAE,IAAI;IACxBC,YAAY,EAAE,IAAI;IAClBC,eAAe,EAAE,IAAI;IACrBC,QAAQ,EAAE,IAAI;IACdC,mBAAmB,EAAE,IAAI;IACzBC,UAAU,EAAE,IAAI;IAChBC,QAAQ,EAAEX;EACZ,CAAC;EACDY,GAAG,EAAE;IACHvC,MAAM,EAAEA;EACV;AACF,CAAC,CAAC;AAEF,SAASwC,OAAOA,CAACtF,CAAC,EAAEI,CAAC,EAAE;EAAE,IAAID,CAAC,GAAGP,MAAM,CAAC2F,IAAI,CAACvF,CAAC,CAAC;EAAE,IAAIJ,MAAM,CAAC4F,qBAAqB,EAAE;IAAE,IAAI/E,CAAC,GAAGb,MAAM,CAAC4F,qBAAqB,CAACxF,CAAC,CAAC;IAAEI,CAAC,KAAKK,CAAC,GAAGA,CAAC,CAACgF,MAAM,CAAC,UAAUrF,CAAC,EAAE;MAAE,OAAOR,MAAM,CAAC8F,wBAAwB,CAAC1F,CAAC,EAAEI,CAAC,CAAC,CAACmB,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEpB,CAAC,CAACiC,IAAI,CAAC7B,KAAK,CAACJ,CAAC,EAAEM,CAAC,CAAC;EAAE;EAAE,OAAON,CAAC;AAAE;AAC9P,SAASwF,aAAaA,CAAC3F,CAAC,EAAE;EAAE,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,SAAS,CAACC,MAAM,EAAEE,CAAC,EAAE,EAAE;IAAE,IAAID,CAAC,GAAG,IAAI,IAAIF,SAAS,CAACG,CAAC,CAAC,GAAGH,SAAS,CAACG,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGkF,OAAO,CAAC1F,MAAM,CAACO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACyF,OAAO,CAAC,UAAUxF,CAAC,EAAE;MAAEgB,eAAe,CAACpB,CAAC,EAAEI,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGR,MAAM,CAACiG,yBAAyB,GAAGjG,MAAM,CAACkG,gBAAgB,CAAC9F,CAAC,EAAEJ,MAAM,CAACiG,yBAAyB,CAAC1F,CAAC,CAAC,CAAC,GAAGmF,OAAO,CAAC1F,MAAM,CAACO,CAAC,CAAC,CAAC,CAACyF,OAAO,CAAC,UAAUxF,CAAC,EAAE;MAAER,MAAM,CAACyB,cAAc,CAACrB,CAAC,EAAEI,CAAC,EAAER,MAAM,CAAC8F,wBAAwB,CAACvF,CAAC,EAAEC,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOJ,CAAC;AAAE;AACtb,IAAI+F,eAAe,GAAG,aAAalH,KAAK,CAACmH,IAAI,CAAC,aAAanH,KAAK,CAACoH,UAAU,CAAC,UAAUC,OAAO,EAAEC,GAAG,EAAE;EAClG,IAAIC,UAAU,GAAGrH,aAAa,CAAC,CAAC;EAChC,IAAIsH,OAAO,GAAGxH,KAAK,CAACyH,UAAU,CAACxH,iBAAiB,CAAC;EACjD,IAAIyH,KAAK,GAAGxD,mBAAmB,CAACyD,QAAQ,CAACN,OAAO,EAAEG,OAAO,CAAC;EAC1D,IAAII,SAAS,GAAGzH,WAAW,CAACkH,OAAO,CAAC,IAAI,CAAC,CAAC;EAC1C,IAAIQ,QAAQ,GAAGH,KAAK,CAAC3C,WAAW,KAAK,UAAU;EAC/C,IAAI+C,UAAU,GAAGJ,KAAK,CAAC3C,WAAW,KAAK,YAAY;EACnD,IAAIgD,IAAI,GAAGL,KAAK,CAAC3C,WAAW,KAAK,MAAM;EACvC,IAAIiD,eAAe,GAAGhI,KAAK,CAACiI,QAAQ,CAACF,IAAI,GAAG;MACxCG,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE;IACR,CAAC,GAAG,CAAC,CAAC;IACNC,gBAAgB,GAAGpE,cAAc,CAACgE,eAAe,EAAE,CAAC,CAAC;IACrDK,UAAU,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAChCE,aAAa,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACrC,IAAIG,gBAAgB,GAAGvI,KAAK,CAACiI,QAAQ,CAACF,IAAI,GAAG;MACzCG,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE;IACR,CAAC,GAAG,CAAC,CAAC;IACNK,gBAAgB,GAAGxE,cAAc,CAACuE,gBAAgB,EAAE,CAAC,CAAC;IACtDE,SAAS,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC/BE,YAAY,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACpC,IAAIG,gBAAgB,GAAG3I,KAAK,CAACiI,QAAQ,CAAC,CAAC,CAAC;IACtCW,gBAAgB,GAAG5E,cAAc,CAAC2E,gBAAgB,EAAE,CAAC,CAAC;IACtDE,SAAS,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC/BE,YAAY,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACpC,IAAIG,gBAAgB,GAAG/I,KAAK,CAACiI,QAAQ,CAACF,IAAI,GAAG;MACzCG,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE;IACR,CAAC,GAAG,CAAC,CAAC;IACNa,gBAAgB,GAAGhF,cAAc,CAAC+E,gBAAgB,EAAE,CAAC,CAAC;IACtDE,uBAAuB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC7CE,0BAA0B,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAClD,IAAIG,gBAAgB,GAAGnJ,KAAK,CAACiI,QAAQ,CAACP,KAAK,CAACzC,iBAAiB,CAAC;IAC5DmE,iBAAiB,GAAGpF,cAAc,CAACmF,gBAAgB,EAAE,CAAC,CAAC;IACvDE,sBAAsB,GAAGD,iBAAiB,CAAC,CAAC,CAAC;IAC7CE,yBAAyB,GAAGF,iBAAiB,CAAC,CAAC,CAAC;EAClD,IAAIG,iBAAiB,GAAGvJ,KAAK,CAACiI,QAAQ,CAACP,KAAK,CAAC/B,OAAO,IAAI,KAAK,CAAC;IAC5D6D,iBAAiB,GAAGxF,cAAc,CAACuF,iBAAiB,EAAE,CAAC,CAAC;IACxDE,YAAY,GAAGD,iBAAiB,CAAC,CAAC,CAAC;IACnCE,eAAe,GAAGF,iBAAiB,CAAC,CAAC,CAAC;EACxC,IAAIG,iBAAiB,GAAG3J,KAAK,CAACiI,QAAQ,CAAC,EAAE,CAAC;IACxC2B,iBAAiB,GAAG5F,cAAc,CAAC2F,iBAAiB,EAAE,CAAC,CAAC;IACxDE,cAAc,GAAGD,iBAAiB,CAAC,CAAC,CAAC;IACrCE,iBAAiB,GAAGF,iBAAiB,CAAC,CAAC,CAAC;EAC1C,IAAIG,qBAAqB,GAAG7F,mBAAmB,CAAC8F,WAAW,CAAC;MACxDtC,KAAK,EAAEA,KAAK;MACZuC,KAAK,EAAE;QACLC,KAAK,EAAE7B,UAAU;QACjB8B,IAAI,EAAE1B,SAAS;QACf2B,IAAI,EAAEvB,SAAS;QACfwB,kBAAkB,EAAEpB,uBAAuB;QAC3ChE,iBAAiB,EAAEoE,sBAAsB;QACzC1D,OAAO,EAAE8D,YAAY;QACrBa,SAAS,EAAET;MACb;IACF,CAAC,CAAC;IACFU,GAAG,GAAGR,qBAAqB,CAACQ,GAAG;EACjCnK,QAAQ,CAAC8D,mBAAmB,CAACsC,GAAG,CAACvC,MAAM,EAAE;IACvCL,IAAI,EAAE;EACR,CAAC,CAAC;EACF,IAAI4G,UAAU,GAAGxK,KAAK,CAACyK,MAAM,CAAC,IAAI,CAAC;EACnC,IAAIC,WAAW,GAAG1K,KAAK,CAACyK,MAAM,CAAC,IAAI,CAAC;EACpC,IAAIE,UAAU,GAAG3K,KAAK,CAACyK,MAAM,CAAC,IAAI,CAAC;EACnC,IAAIG,UAAU,GAAG5K,KAAK,CAACyK,MAAM,CAAC,IAAI,CAAC;EACnC,IAAII,aAAa,GAAG7K,KAAK,CAACyK,MAAM,CAAC1C,IAAI,GAAG;IACtC+C,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE;EACR,CAAC,GAAG,CAAC,CAAC;EACN,IAAIC,aAAa,GAAGhL,KAAK,CAACyK,MAAM,CAAC,IAAI,CAAC;EACtC,IAAIQ,aAAa,GAAGjL,KAAK,CAACyK,MAAM,CAAC,IAAI,CAAC;EACtC,IAAIS,YAAY,GAAGlL,KAAK,CAACyK,MAAM,CAAC,CAAC,CAAC,CAAC;EACnC,IAAIU,WAAW,GAAGnL,KAAK,CAACyK,MAAM,CAAC,CAAC,CAAC,CAAC;EAClC,IAAIW,YAAY,GAAGpL,KAAK,CAACyK,MAAM,CAAC,IAAI,CAAC;EACrC,IAAIY,aAAa,GAAGrL,KAAK,CAACyK,MAAM,CAAC,IAAI,CAAC;EACtC,IAAIa,mBAAmB,GAAGtL,KAAK,CAACyK,MAAM,CAAC,IAAI,CAAC;EAC5C,IAAIc,oBAAoB,GAAGvL,KAAK,CAACyK,MAAM,CAAC,IAAI,CAAC;EAC7C,IAAIe,kBAAkB,GAAGxL,KAAK,CAACyK,MAAM,CAAC,KAAK,CAAC;EAC5C,IAAIgB,aAAa,GAAGzL,KAAK,CAACyK,MAAM,CAAC,IAAI,CAAC;EACtC,IAAIiB,eAAe,GAAG1L,KAAK,CAACyK,MAAM,CAAC,KAAK,CAAC;EACzC,IAAIkB,kBAAkB,GAAGtL,iBAAiB,CAAC;MACvCuL,QAAQ,EAAE,SAASA,QAAQA,CAACC,KAAK,EAAE;QACjC,OAAOC,QAAQ,CAAC,CAAC;MACnB,CAAC;MACDC,IAAI,EAAE,CAACrE,KAAK,CAACnC;IACf,CAAC,CAAC;IACFyG,mBAAmB,GAAGhI,cAAc,CAAC2H,kBAAkB,EAAE,CAAC,CAAC;IAC3DM,wBAAwB,GAAGD,mBAAmB,CAAC,CAAC,CAAC;EACnD,IAAIE,iBAAiB,GAAG5L,gBAAgB,CAAC;MACrC6L,MAAM,EAAE,QAAQ;MAChBC,IAAI,EAAE,mBAAmB;MACzBR,QAAQ,EAAE,SAASA,QAAQA,CAACC,KAAK,EAAE;QACjC,OAAOC,QAAQ,CAAC,CAAC;MACnB,CAAC;MACDC,IAAI,EAAE,CAACrE,KAAK,CAACnC;IACf,CAAC,CAAC;IACF8G,kBAAkB,GAAGrI,cAAc,CAACkI,iBAAiB,EAAE,CAAC,CAAC;IACzDI,6BAA6B,GAAGD,kBAAkB,CAAC,CAAC,CAAC;EACvD,IAAIE,aAAa,GAAG,SAASA,aAAaA,CAAA,EAAG;IAC3C,OAAO/B,UAAU;EACnB,CAAC;EACD,IAAIgC,cAAc,GAAG,SAASA,cAAcA,CAACtC,KAAK,EAAE;IAClD,OAAOuC,IAAI,CAACC,KAAK,CAAC,CAACxC,KAAK,GAAGb,sBAAsB,GAAG,CAAC,KAAK3B,KAAK,CAAC1C,IAAI,IAAI,CAAC,CAAC,CAAC;EAC7E,CAAC;EACD,IAAI2H,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,OAAO,EAAE;IAC1DlC,WAAW,CAACmC,OAAO,GAAGD,OAAO,IAAIlC,WAAW,CAACmC,OAAO,IAAIpM,UAAU,CAACqM,UAAU,CAACtC,UAAU,CAACqC,OAAO,EAAE,4BAA4B,CAAC;EACjI,CAAC;EACD,IAAIE,aAAa,GAAG,SAASA,aAAaA,CAAC7C,KAAK,EAAE;IAChD,OAAOxC,KAAK,CAAC1C,IAAI,GAAG6D,SAAS,KAAK2D,cAAc,CAACtC,KAAK,CAAC,GAAG,IAAI;EAChE,CAAC;EACD,IAAI8C,QAAQ,GAAG,SAASA,QAAQA,CAACC,OAAO,EAAE;IACxCpC,aAAa,CAACgC,OAAO,GAAG9E,IAAI,GAAG;MAC7B+C,GAAG,EAAE,CAAC;MACNC,IAAI,EAAE;IACR,CAAC,GAAG,CAAC;IACLP,UAAU,CAACqC,OAAO,IAAIrC,UAAU,CAACqC,OAAO,CAACG,QAAQ,CAACC,OAAO,CAAC;EAC5D,CAAC;EACD,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,KAAK,EAAE;IAChD,IAAIC,QAAQ,GAAGhM,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKwE,SAAS,GAAGxE,SAAS,CAAC,CAAC,CAAC,GAAG,MAAM;IACzF,IAAIiM,kBAAkB,GAAGC,iBAAiB,CAAC,CAAC;MAC1CrI,iBAAiB,GAAGoI,kBAAkB,CAACpI,iBAAiB;IAC1D,IAAIsI,UAAU,GAAGC,kBAAkB,CAAC,CAAC;IACrC,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAAA,EAAG;MAC7C,IAAIC,MAAM,GAAGtM,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKwE,SAAS,GAAGxE,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;MAClF,IAAIuM,KAAK,GAAGvM,SAAS,CAACC,MAAM,GAAG,CAAC,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAGwE,SAAS;MAC3D,OAAO8H,MAAM,IAAIC,KAAK,GAAG,CAAC,GAAGD,MAAM;IACrC,CAAC;IACD,IAAIE,cAAc,GAAG,SAASA,cAAcA,CAACC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAE;MACjE,OAAOF,MAAM,GAAGC,KAAK,GAAGC,KAAK;IAC/B,CAAC;IACD,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAG;MACzC,IAAIjD,IAAI,GAAG3J,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKwE,SAAS,GAAGxE,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;MAChF,IAAI0J,GAAG,GAAG1J,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKwE,SAAS,GAAGxE,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;MAC/E,OAAO4L,QAAQ,CAAC;QACdjC,IAAI,EAAEA,IAAI;QACVD,GAAG,EAAEA,GAAG;QACRsC,QAAQ,EAAEA;MACZ,CAAC,CAAC;IACJ,CAAC;IACD,IAAIa,QAAQ,GAAGlG,IAAI,GAAG;MACpBG,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE;IACR,CAAC,GAAG,CAAC;IACL,IAAI+F,cAAc,GAAG,KAAK;IAC1B,IAAInG,IAAI,EAAE;MACRkG,QAAQ,GAAG;QACT/F,IAAI,EAAEuF,cAAc,CAACN,KAAK,CAAC,CAAC,CAAC,EAAElI,iBAAiB,CAAC,CAAC,CAAC,CAAC;QACpDkD,IAAI,EAAEsF,cAAc,CAACN,KAAK,CAAC,CAAC,CAAC,EAAElI,iBAAiB,CAAC,CAAC,CAAC;MACrD,CAAC;MACD+I,YAAY,CAACJ,cAAc,CAACK,QAAQ,CAAC9F,IAAI,EAAET,KAAK,CAAC9C,QAAQ,CAAC,CAAC,CAAC,EAAE2I,UAAU,CAACxC,IAAI,CAAC,EAAE6C,cAAc,CAACK,QAAQ,CAAC/F,IAAI,EAAER,KAAK,CAAC9C,QAAQ,CAAC,CAAC,CAAC,EAAE2I,UAAU,CAACzC,GAAG,CAAC,CAAC;MACjJoD,cAAc,GAAG7F,UAAU,CAACH,IAAI,KAAK+F,QAAQ,CAAC/F,IAAI,IAAIG,UAAU,CAACF,IAAI,KAAK8F,QAAQ,CAAC9F,IAAI;IACzF,CAAC,MAAM;MACL8F,QAAQ,GAAGR,cAAc,CAACN,KAAK,EAAElI,iBAAiB,CAAC;MACnD6C,UAAU,GAAGkG,YAAY,CAACJ,cAAc,CAACK,QAAQ,EAAEvG,KAAK,CAAC9C,QAAQ,EAAE2I,UAAU,CAACxC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAGiD,YAAY,CAAC,CAAC,EAAEJ,cAAc,CAACK,QAAQ,EAAEvG,KAAK,CAAC9C,QAAQ,EAAE2I,UAAU,CAACzC,GAAG,CAAC,CAAC;MACnKoD,cAAc,GAAG7F,UAAU,KAAK4F,QAAQ;IAC1C;IACAzC,kBAAkB,CAACqB,OAAO,GAAGqB,cAAc;IAC3C5F,aAAa,CAAC2F,QAAQ,CAAC;EACzB,CAAC;EACD,IAAIE,YAAY,GAAG,SAASA,YAAYA,CAAChB,KAAK,EAAEiB,EAAE,EAAE;IAClD,IAAIhB,QAAQ,GAAGhM,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKwE,SAAS,GAAGxE,SAAS,CAAC,CAAC,CAAC,GAAG,MAAM;IACzF,IAAIgN,EAAE,EAAE;MACN,IAAIC,iBAAiB,GAAGC,gBAAgB,CAAC,CAAC;QACxCpE,KAAK,GAAGmE,iBAAiB,CAACnE,KAAK;QAC/BqE,QAAQ,GAAGF,iBAAiB,CAACE,QAAQ;MACvC,IAAIP,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAG;QACzC,IAAIjD,IAAI,GAAG3J,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKwE,SAAS,GAAGxE,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;QAChF,IAAI0J,GAAG,GAAG1J,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKwE,SAAS,GAAGxE,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;QAC/E,OAAO4L,QAAQ,CAAC;UACdjC,IAAI,EAAEA,IAAI;UACVD,GAAG,EAAEA,GAAG;UACRsC,QAAQ,EAAEA;QACZ,CAAC,CAAC;MACJ,CAAC;MACD,IAAIoB,SAAS,GAAGJ,EAAE,KAAK,UAAU;MACjC,IAAIK,OAAO,GAAGL,EAAE,KAAK,QAAQ;MAC7B,IAAII,SAAS,EAAE;QACb,IAAIzG,IAAI,EAAE;UACR,IAAIwG,QAAQ,CAACrE,KAAK,CAAChC,IAAI,GAAGgC,KAAK,CAAChC,IAAI,GAAGiF,KAAK,CAAC,CAAC,CAAC,EAAE;YAC/Ca,YAAY,CAACO,QAAQ,CAACrE,KAAK,CAAC/B,IAAI,GAAGT,KAAK,CAAC9C,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC2J,QAAQ,CAACrE,KAAK,CAAChC,IAAI,GAAG,CAAC,IAAIR,KAAK,CAAC9C,QAAQ,CAAC,CAAC,CAAC,CAAC;UACtG,CAAC,MAAM,IAAI2J,QAAQ,CAACrE,KAAK,CAAC/B,IAAI,GAAG+B,KAAK,CAAC/B,IAAI,GAAGgF,KAAK,CAAC,CAAC,CAAC,EAAE;YACtDa,YAAY,CAAC,CAACO,QAAQ,CAACrE,KAAK,CAAC/B,IAAI,GAAG,CAAC,IAAIT,KAAK,CAAC9C,QAAQ,CAAC,CAAC,CAAC,EAAE2J,QAAQ,CAACrE,KAAK,CAAChC,IAAI,GAAGR,KAAK,CAAC9C,QAAQ,CAAC,CAAC,CAAC,CAAC;UACtG;QACF,CAAC,MAAM,IAAI2J,QAAQ,CAACrE,KAAK,GAAGA,KAAK,GAAGiD,KAAK,EAAE;UACzC,IAAIuB,GAAG,GAAG,CAACH,QAAQ,CAACrE,KAAK,GAAG,CAAC,IAAIxC,KAAK,CAAC9C,QAAQ;UAC/CkD,UAAU,GAAGkG,YAAY,CAACU,GAAG,EAAE,CAAC,CAAC,GAAGV,YAAY,CAAC,CAAC,EAAEU,GAAG,CAAC;QAC1D;MACF,CAAC,MAAM,IAAID,OAAO,EAAE;QAClB,IAAI1G,IAAI,EAAE;UACR,IAAIwG,QAAQ,CAACpE,IAAI,CAACjC,IAAI,GAAGgC,KAAK,CAAChC,IAAI,IAAIiF,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;YACnDa,YAAY,CAACO,QAAQ,CAACrE,KAAK,CAAC/B,IAAI,GAAGT,KAAK,CAAC9C,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC2J,QAAQ,CAACrE,KAAK,CAAChC,IAAI,GAAG,CAAC,IAAIR,KAAK,CAAC9C,QAAQ,CAAC,CAAC,CAAC,CAAC;UACtG,CAAC,MAAM,IAAI2J,QAAQ,CAACpE,IAAI,CAAChC,IAAI,GAAG+B,KAAK,CAAC/B,IAAI,IAAIgF,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;YAC1Da,YAAY,CAAC,CAACO,QAAQ,CAACrE,KAAK,CAAC/B,IAAI,GAAG,CAAC,IAAIT,KAAK,CAAC9C,QAAQ,CAAC,CAAC,CAAC,EAAE2J,QAAQ,CAACrE,KAAK,CAAChC,IAAI,GAAGR,KAAK,CAAC9C,QAAQ,CAAC,CAAC,CAAC,CAAC;UACtG;QACF,CAAC,MAAM,IAAI2J,QAAQ,CAACpE,IAAI,GAAGD,KAAK,IAAIiD,KAAK,GAAG,CAAC,EAAE;UAC7C,IAAIwB,KAAK,GAAG,CAACJ,QAAQ,CAACrE,KAAK,GAAG,CAAC,IAAIxC,KAAK,CAAC9C,QAAQ;UACjDkD,UAAU,GAAGkG,YAAY,CAACW,KAAK,EAAE,CAAC,CAAC,GAAGX,YAAY,CAAC,CAAC,EAAEW,KAAK,CAAC;QAC9D;MACF;IACF,CAAC,MAAM;MACLzB,aAAa,CAACC,KAAK,EAAEC,QAAQ,CAAC;IAChC;EACF,CAAC;EACD,IAAIwB,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAG;IAC/B,OAAOnF,YAAY,GAAG/B,KAAK,CAAClC,cAAc,GAAGqE,cAAc,GAAG,EAAE,GAAGgF,WAAW,CAAC,CAAC;EAClF,CAAC;EACD,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAAA,EAAG;IACrC,IAAIpH,KAAK,CAAChC,OAAO,IAAIqC,IAAI,IAAID,UAAU,EAAE;MACvC,OAAO2B,YAAY,IAAI/B,KAAK,CAAClC,cAAc,GAAGuC,IAAI,GAAG8B,cAAc,CAAC,CAAC,CAAC,GAAGA,cAAc,GAAGnC,KAAK,CAAChC,OAAO,CAAC/B,KAAK,CAACoE,IAAI,GAAGM,UAAU,CAACF,IAAI,GAAGE,UAAU,EAAEN,IAAI,GAAGU,SAAS,CAACN,IAAI,GAAGM,SAAS,CAAC;IACvL;IACA,OAAOf,KAAK,CAAChC,OAAO;EACtB,CAAC;EACD,IAAI4I,gBAAgB,GAAG,SAASA,gBAAgBA,CAAA,EAAG;IACjD,IAAIS,wBAAwB,GAAG,SAASA,wBAAwBA,CAACC,IAAI,EAAElB,KAAK,EAAE;MAC5E,OAAOrB,IAAI,CAACC,KAAK,CAACsC,IAAI,IAAIlB,KAAK,IAAIkB,IAAI,CAAC,CAAC;IAC3C,CAAC;IACD,IAAIC,eAAe,GAAG5G,UAAU;IAChC,IAAI6G,cAAc,GAAG,CAAC;IACtB,IAAI1E,UAAU,CAACqC,OAAO,EAAE;MACtB,IAAIsC,mBAAmB,GAAG3E,UAAU,CAACqC,OAAO;QAC1CuC,SAAS,GAAGD,mBAAmB,CAACC,SAAS;QACzCC,UAAU,GAAGF,mBAAmB,CAACE,UAAU;MAC7C,IAAItH,IAAI,EAAE;QACRkH,eAAe,GAAG;UAChB/G,IAAI,EAAE6G,wBAAwB,CAACK,SAAS,EAAE1H,KAAK,CAAC9C,QAAQ,CAAC,CAAC,CAAC,CAAC;UAC5DuD,IAAI,EAAE4G,wBAAwB,CAACM,UAAU,EAAE3H,KAAK,CAAC9C,QAAQ,CAAC,CAAC,CAAC;QAC9D,CAAC;QACDsK,cAAc,GAAG;UACfhH,IAAI,EAAE+G,eAAe,CAAC/G,IAAI,GAAGe,uBAAuB,CAACf,IAAI;UACzDC,IAAI,EAAE8G,eAAe,CAAC9G,IAAI,GAAGc,uBAAuB,CAACd;QACvD,CAAC;MACH,CAAC,MAAM;QACL,IAAImH,SAAS,GAAGxH,UAAU,GAAGuH,UAAU,GAAGD,SAAS;QACnDH,eAAe,GAAGF,wBAAwB,CAACO,SAAS,EAAE5H,KAAK,CAAC9C,QAAQ,CAAC;QACrEsK,cAAc,GAAGD,eAAe,GAAGhG,uBAAuB;MAC5D;IACF;IACA,OAAO;MACLiB,KAAK,EAAE7B,UAAU;MACjB8B,IAAI,EAAE1B,SAAS;MACf8F,QAAQ,EAAE;QACRrE,KAAK,EAAE+E,eAAe;QACtB9E,IAAI,EAAE+E;MACR;IACF,CAAC;EACH,CAAC;EACD,IAAI5B,iBAAiB,GAAG,SAASA,iBAAiBA,CAAA,EAAG;IACnD,IAAIC,UAAU,GAAGC,kBAAkB,CAAC,CAAC;IACrC,IAAI+B,YAAY,GAAG/E,UAAU,CAACqC,OAAO,GAAGrC,UAAU,CAACqC,OAAO,CAAC2C,WAAW,GAAGjC,UAAU,CAACxC,IAAI,GAAG,CAAC;IAC5F,IAAI0E,aAAa,GAAGjF,UAAU,CAACqC,OAAO,GAAGrC,UAAU,CAACqC,OAAO,CAAC6C,YAAY,GAAGnC,UAAU,CAACzC,GAAG,GAAG,CAAC;IAC7F,IAAI6E,2BAA2B,GAAG,SAASA,2BAA2BA,CAACC,YAAY,EAAEC,SAAS,EAAE;MAC9F,OAAOpD,IAAI,CAACqD,IAAI,CAACF,YAAY,IAAIC,SAAS,IAAID,YAAY,CAAC,CAAC;IAC9D,CAAC;IACD,IAAIG,0BAA0B,GAAG,SAASA,0BAA0BA,CAACC,SAAS,EAAE;MAC9E,OAAOvD,IAAI,CAACqD,IAAI,CAACE,SAAS,GAAG,CAAC,CAAC;IACjC,CAAC;IACD,IAAI3F,kBAAkB,GAAGtC,IAAI,GAAG;MAC9BG,IAAI,EAAEyH,2BAA2B,CAACF,aAAa,EAAE/H,KAAK,CAAC9C,QAAQ,CAAC,CAAC,CAAC,CAAC;MACnEuD,IAAI,EAAEwH,2BAA2B,CAACJ,YAAY,EAAE7H,KAAK,CAAC9C,QAAQ,CAAC,CAAC,CAAC;IACnE,CAAC,GAAG+K,2BAA2B,CAAC7H,UAAU,GAAGyH,YAAY,GAAGE,aAAa,EAAE/H,KAAK,CAAC9C,QAAQ,CAAC;IAC1F,IAAIK,iBAAiB,GAAGoE,sBAAsB,KAAKtB,IAAI,GAAG,CAACgI,0BAA0B,CAAC1F,kBAAkB,CAACnC,IAAI,CAAC,EAAE6H,0BAA0B,CAAC1F,kBAAkB,CAAClC,IAAI,CAAC,CAAC,GAAG4H,0BAA0B,CAAC1F,kBAAkB,CAAC,CAAC;IACtN,OAAO;MACLA,kBAAkB,EAAEA,kBAAkB;MACtCpF,iBAAiB,EAAEA;IACrB,CAAC;EACH,CAAC;EACD,IAAIgL,gBAAgB,GAAG,SAASA,gBAAgBA,CAAA,EAAG;IACjD,IAAIC,mBAAmB,GAAG5C,iBAAiB,CAAC,CAAC;MAC3CjD,kBAAkB,GAAG6F,mBAAmB,CAAC7F,kBAAkB;MAC3DpF,iBAAiB,GAAGiL,mBAAmB,CAACjL,iBAAiB;IAC3D,IAAIkL,aAAa,GAAG,SAASA,aAAaA,CAACtC,MAAM,EAAEuC,IAAI,EAAEzC,KAAK,EAAE;MAC9D,IAAI0C,OAAO,GAAGjP,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKwE,SAAS,GAAGxE,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;MACvF,OAAOkP,OAAO,CAACzC,MAAM,GAAGuC,IAAI,GAAG,CAACvC,MAAM,GAAGF,KAAK,GAAG,CAAC,GAAG,CAAC,IAAIA,KAAK,EAAE0C,OAAO,CAAC;IAC3E,CAAC;IACD,IAAIlG,IAAI,GAAGpC,IAAI,GAAG;MAChBG,IAAI,EAAEiI,aAAa,CAAC9H,UAAU,CAACH,IAAI,EAAEmC,kBAAkB,CAACnC,IAAI,EAAEjD,iBAAiB,CAAC,CAAC,CAAC,CAAC;MACnFkD,IAAI,EAAEgI,aAAa,CAAC9H,UAAU,CAACF,IAAI,EAAEkC,kBAAkB,CAAClC,IAAI,EAAElD,iBAAiB,CAAC,CAAC,CAAC,EAAE,IAAI;IAC1F,CAAC,GAAGkL,aAAa,CAAC9H,UAAU,EAAEgC,kBAAkB,EAAEpF,iBAAiB,CAAC;IACpEiE,0BAA0B,CAACmB,kBAAkB,CAAC;IAC9Cf,yBAAyB,CAACrE,iBAAiB,CAAC;IAC5CyD,YAAY,CAACyB,IAAI,CAAC;IAClB,IAAIzC,KAAK,CAAC3B,UAAU,EAAE;MACpB+D,iBAAiB,CAAC/B,IAAI,GAAGjF,KAAK,CAACe,IAAI,CAAC;QAClCxC,MAAM,EAAEgJ,kBAAkB,CAACnC;MAC7B,CAAC,CAAC,CAACqI,GAAG,CAAC,YAAY;QACjB,OAAOzN,KAAK,CAACe,IAAI,CAAC;UAChBxC,MAAM,EAAEgJ,kBAAkB,CAAClC;QAC7B,CAAC,CAAC;MACJ,CAAC,CAAC,GAAGrF,KAAK,CAACe,IAAI,CAAC;QACdxC,MAAM,EAAEgJ;MACV,CAAC,CAAC,CAAC;IACL;IACA,IAAI3C,KAAK,CAACpC,IAAI,EAAE;MACdkL,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,YAAY;QACjCjF,aAAa,CAACoB,OAAO,GAAG;UACtB3C,KAAK,EAAExC,KAAK,CAAC1C,IAAI,GAAG+C,IAAI,GAAG;YACzBG,IAAI,EAAE,CAAC;YACPC,IAAI,EAAEE,UAAU,CAACF;UACnB,CAAC,GAAG,CAAC,GAAGE,UAAU;UAClB8B,IAAI,EAAEsC,IAAI,CAACkE,GAAG,CAACjJ,KAAK,CAAC1C,IAAI,GAAG0C,KAAK,CAAC1C,IAAI,GAAGmF,IAAI,EAAE,CAACzC,KAAK,CAAC/C,KAAK,IAAI,EAAE,EAAEtD,MAAM;QAC3E,CAAC;QACDqG,KAAK,CAACpB,UAAU,IAAIoB,KAAK,CAACpB,UAAU,CAACmF,aAAa,CAACoB,OAAO,CAAC;MAC7D,CAAC,CAAC;IACJ;EACF,CAAC;EACD,IAAI+D,iBAAiB,GAAG,SAASA,iBAAiBA,CAACjL,OAAO,EAAE;IAC1D,IAAI+B,KAAK,CAAC7B,QAAQ,IAAI,CAACF,OAAO,EAAE;MAC9B6K,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,YAAY;QACjC,IAAIhG,WAAW,CAACmC,OAAO,EAAE;UACvBnC,WAAW,CAACmC,OAAO,CAACrI,KAAK,CAACqM,SAAS,GAAGnG,WAAW,CAACmC,OAAO,CAACrI,KAAK,CAACsM,QAAQ,GAAG,MAAM;UACjFpG,WAAW,CAACmC,OAAO,CAACrI,KAAK,CAACuM,QAAQ,GAAG,UAAU;UAC/CvG,UAAU,CAACqC,OAAO,CAACrI,KAAK,CAACwM,OAAO,GAAG,MAAM;;UAEzC;AACV;AACA;;UAEU,IAAIC,IAAI,GAAG,CAACxQ,UAAU,CAACyQ,QAAQ,CAAC1G,UAAU,CAACqC,OAAO,CAAC,EAAEpM,UAAU,CAAC0Q,SAAS,CAAC3G,UAAU,CAACqC,OAAO,CAAC,CAAC;YAC5FuE,KAAK,GAAGH,IAAI,CAAC,CAAC,CAAC;YACfI,MAAM,GAAGJ,IAAI,CAAC,CAAC,CAAC;UAClB,CAAClJ,IAAI,IAAID,UAAU,MAAM0C,UAAU,CAACqC,OAAO,CAACrI,KAAK,CAAC4M,KAAK,GAAG,CAACA,KAAK,GAAGhG,YAAY,CAACyB,OAAO,GAAGuE,KAAK,GAAG1J,KAAK,CAAC5C,WAAW,IAAIsG,YAAY,CAACyB,OAAO,IAAI,IAAI,CAAC;UACpJ,CAAC9E,IAAI,IAAIF,QAAQ,MAAM2C,UAAU,CAACqC,OAAO,CAACrI,KAAK,CAAC6M,MAAM,GAAG,CAACA,MAAM,GAAGhG,aAAa,CAACwB,OAAO,GAAGwE,MAAM,GAAG3J,KAAK,CAAC7C,YAAY,IAAIwG,aAAa,CAACwB,OAAO,IAAI,IAAI,CAAC;UACxJnC,WAAW,CAACmC,OAAO,CAACrI,KAAK,CAACqM,SAAS,GAAGnG,WAAW,CAACmC,OAAO,CAACrI,KAAK,CAACsM,QAAQ,GAAG,EAAE;UAC7EpG,WAAW,CAACmC,OAAO,CAACrI,KAAK,CAACuM,QAAQ,GAAG,EAAE;UACvCvG,UAAU,CAACqC,OAAO,CAACrI,KAAK,CAACwM,OAAO,GAAG,EAAE;QACvC;MACF,CAAC,CAAC;IACJ;EACF,CAAC;EACD,IAAIV,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAG;IAC/B,IAAIgB,KAAK;IACT,IAAInH,IAAI,GAAG/I,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKwE,SAAS,GAAGxE,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IAChF,IAAImQ,MAAM,GAAGnQ,SAAS,CAACC,MAAM,GAAG,CAAC,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAGwE,SAAS;IAC5D,OAAO8B,KAAK,CAAC/C,KAAK,GAAG8H,IAAI,CAACkE,GAAG,CAACY,MAAM,GAAG,CAAC,CAACD,KAAK,GAAG5J,KAAK,CAAChC,OAAO,IAAIgC,KAAK,CAAC/C,KAAK,CAAC,CAAC,CAAC,MAAM,IAAI,IAAI2M,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACjQ,MAAM,KAAK,CAAC,GAAG,CAACqG,KAAK,CAAC/C,KAAK,IAAI,EAAE,EAAEtD,MAAM,EAAE8I,IAAI,CAAC,GAAG,CAAC;EACtL,CAAC;EACD,IAAIqD,kBAAkB,GAAG,SAASA,kBAAkBA,CAAA,EAAG;IACrD,IAAI9C,WAAW,CAACmC,OAAO,EAAE;MACvB,IAAIrI,KAAK,GAAGgN,gBAAgB,CAAC9G,WAAW,CAACmC,OAAO,CAAC;MACjD,IAAI9B,IAAI,GAAG0G,UAAU,CAACjN,KAAK,CAACkN,WAAW,CAAC,GAAGjF,IAAI,CAACkF,GAAG,CAACF,UAAU,CAACjN,KAAK,CAACuG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;MACnF,IAAI6G,KAAK,GAAGH,UAAU,CAACjN,KAAK,CAACqN,YAAY,CAAC,GAAGpF,IAAI,CAACkF,GAAG,CAACF,UAAU,CAACjN,KAAK,CAACoN,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;MACtF,IAAI9G,GAAG,GAAG2G,UAAU,CAACjN,KAAK,CAACsN,UAAU,CAAC,GAAGrF,IAAI,CAACkF,GAAG,CAACF,UAAU,CAACjN,KAAK,CAACsG,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;MAChF,IAAIiH,MAAM,GAAGN,UAAU,CAACjN,KAAK,CAACwN,aAAa,CAAC,GAAGvF,IAAI,CAACkF,GAAG,CAACF,UAAU,CAACjN,KAAK,CAACuN,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;MACzF,OAAO;QACLhH,IAAI,EAAEA,IAAI;QACV6G,KAAK,EAAEA,KAAK;QACZ9G,GAAG,EAAEA,GAAG;QACRiH,MAAM,EAAEA,MAAM;QACdE,CAAC,EAAElH,IAAI,GAAG6G,KAAK;QACfM,CAAC,EAAEpH,GAAG,GAAGiH;MACX,CAAC;IACH;IACA,OAAO;MACLhH,IAAI,EAAE,CAAC;MACP6G,KAAK,EAAE,CAAC;MACR9G,GAAG,EAAE,CAAC;MACNiH,MAAM,EAAE,CAAC;MACTE,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE;IACL,CAAC;EACH,CAAC;EACD,IAAIC,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAG;IAC/B,IAAI3H,UAAU,CAACqC,OAAO,EAAE;MACtB,IAAIuF,aAAa,GAAG5H,UAAU,CAACqC,OAAO,CAACuF,aAAa;MACpD,IAAIhB,KAAK,GAAG1J,KAAK,CAAC5C,WAAW,IAAI,EAAE,CAACuN,MAAM,CAAC7H,UAAU,CAACqC,OAAO,CAAC2C,WAAW,IAAI4C,aAAa,CAAC5C,WAAW,EAAE,IAAI,CAAC;MAC7G,IAAI6B,MAAM,GAAG3J,KAAK,CAAC7C,YAAY,IAAI,EAAE,CAACwN,MAAM,CAAC7H,UAAU,CAACqC,OAAO,CAAC6C,YAAY,IAAI0C,aAAa,CAAC1C,YAAY,EAAE,IAAI,CAAC;MACjH,IAAI4C,OAAO,GAAG,SAASA,OAAOA,CAACC,KAAK,EAAEC,MAAM,EAAE;QAC5C,OAAOhI,UAAU,CAACqC,OAAO,CAACrI,KAAK,CAAC+N,KAAK,CAAC,GAAGC,MAAM;MACjD,CAAC;MACD,IAAIzK,IAAI,IAAID,UAAU,EAAE;QACtBwK,OAAO,CAAC,QAAQ,EAAEjB,MAAM,CAAC;QACzBiB,OAAO,CAAC,OAAO,EAAElB,KAAK,CAAC;MACzB,CAAC,MAAM;QACLkB,OAAO,CAAC,QAAQ,EAAEjB,MAAM,CAAC;MAC3B;IACF;EACF,CAAC;EACD,IAAIoB,aAAa,GAAG,SAASA,aAAaA,CAAA,EAAG;IAC3C,IAAI9N,KAAK,GAAG+C,KAAK,CAAC/C,KAAK;IACvB,IAAIA,KAAK,EAAE;MACT,IAAI4I,UAAU,GAAGC,kBAAkB,CAAC,CAAC;MACrC,IAAI8E,OAAO,GAAG,SAASA,OAAOA,CAACC,KAAK,EAAEC,MAAM,EAAE1E,KAAK,EAAE;QACnD,IAAIC,KAAK,GAAG3M,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKwE,SAAS,GAAGxE,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;QACjF,OAAO+J,WAAW,CAAC0B,OAAO,GAAG/F,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEqE,WAAW,CAAC0B,OAAO,CAAC,EAAEtK,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC8P,MAAM,CAACE,KAAK,CAAC,EAAE,CAACC,MAAM,IAAI,EAAE,EAAEnR,MAAM,GAAGyM,KAAK,GAAGC,KAAK,GAAG,IAAI,CAAC,CAAC;MACzK,CAAC;MACD,IAAIhG,IAAI,EAAE;QACRuK,OAAO,CAAC,QAAQ,EAAE3N,KAAK,EAAE+C,KAAK,CAAC9C,QAAQ,CAAC,CAAC,CAAC,EAAE2I,UAAU,CAAC2E,CAAC,CAAC;QACzDI,OAAO,CAAC,OAAO,EAAE5K,KAAK,CAAChC,OAAO,IAAIf,KAAK,CAAC,CAAC,CAAC,EAAE+C,KAAK,CAAC9C,QAAQ,CAAC,CAAC,CAAC,EAAE2I,UAAU,CAAC0E,CAAC,CAAC;MAC9E,CAAC,MAAM;QACLnK,UAAU,GAAGwK,OAAO,CAAC,OAAO,EAAE5K,KAAK,CAAChC,OAAO,IAAIf,KAAK,EAAE+C,KAAK,CAAC9C,QAAQ,EAAE2I,UAAU,CAAC0E,CAAC,CAAC,GAAGK,OAAO,CAAC,QAAQ,EAAE3N,KAAK,EAAE+C,KAAK,CAAC9C,QAAQ,EAAE2I,UAAU,CAAC2E,CAAC,CAAC;MAC9I;IACF;EACF,CAAC;EACD,IAAIQ,kBAAkB,GAAG,SAASA,kBAAkBA,CAAChE,GAAG,EAAE;IACxD,IAAIhE,WAAW,CAACmC,OAAO,IAAI,CAACnF,KAAK,CAACtC,UAAU,EAAE;MAC5C,IAAI8E,KAAK,GAAGwE,GAAG,GAAGA,GAAG,CAACxE,KAAK,GAAG7B,UAAU;MACxC,IAAIsK,qBAAqB,GAAG,SAASA,qBAAqBA,CAAC9E,MAAM,EAAEC,KAAK,EAAE;QACxE,OAAOD,MAAM,GAAGC,KAAK;MACvB,CAAC;MACD,IAAI8E,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAG;QACzC,IAAIC,EAAE,GAAGzR,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKwE,SAAS,GAAGxE,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;QAC9E,IAAI0R,EAAE,GAAG1R,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKwE,SAAS,GAAGxE,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;QAC9EwJ,UAAU,CAACiC,OAAO,KAAKjC,UAAU,CAACiC,OAAO,CAACrI,KAAK,CAACsG,GAAG,GAAG,GAAG,CAACuH,MAAM,CAACS,EAAE,EAAE,IAAI,CAAC,CAAC;QAC3E5H,YAAY,CAAC2B,OAAO,GAAG/F,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoE,YAAY,CAAC2B,OAAO,CAAC,EAAE;UAC5EkG,SAAS,EAAE,cAAc,CAACV,MAAM,CAACQ,EAAE,EAAE,MAAM,CAAC,CAACR,MAAM,CAACS,EAAE,EAAE,QAAQ;QAClE,CAAC,CAAC;MACJ,CAAC;MACD,IAAI/K,IAAI,EAAE;QACR6K,YAAY,CAACD,qBAAqB,CAACzI,KAAK,CAAC/B,IAAI,EAAET,KAAK,CAAC9C,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE+N,qBAAqB,CAACzI,KAAK,CAAChC,IAAI,EAAER,KAAK,CAAC9C,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1H,CAAC,MAAM;QACL,IAAIoO,YAAY,GAAGL,qBAAqB,CAACzI,KAAK,EAAExC,KAAK,CAAC9C,QAAQ,CAAC;QAC/DkD,UAAU,GAAG8K,YAAY,CAACI,YAAY,EAAE,CAAC,CAAC,GAAGJ,YAAY,CAAC,CAAC,EAAEI,YAAY,CAAC;MAC5E;IACF;EACF,CAAC;EACD,IAAIC,sBAAsB,GAAG,SAASA,sBAAsBA,CAACpH,KAAK,EAAE;IAClE,IAAIM,MAAM,GAAGN,KAAK,CAACM,MAAM;IACzB,IAAIoB,UAAU,GAAGC,kBAAkB,CAAC,CAAC;IACrC,IAAI0F,kBAAkB,GAAG,SAASA,kBAAkBA,CAAClE,IAAI,EAAEjB,KAAK,EAAE;MAChE,OAAOiB,IAAI,GAAGA,IAAI,GAAGjB,KAAK,GAAGiB,IAAI,GAAGjB,KAAK,GAAGiB,IAAI,GAAG,CAAC;IACtD,CAAC;IACD,IAAImE,qBAAqB,GAAG,SAASA,qBAAqBA,CAACnE,IAAI,EAAElB,KAAK,EAAE;MACtE,OAAOrB,IAAI,CAACC,KAAK,CAACsC,IAAI,IAAIlB,KAAK,IAAIkB,IAAI,CAAC,CAAC;IAC3C,CAAC;IACD,IAAIoE,qBAAqB,GAAG,SAASA,qBAAqBA,CAACC,aAAa,EAAExF,MAAM,EAAEyF,KAAK,EAAElD,IAAI,EAAEzC,KAAK,EAAE4F,oBAAoB,EAAE;MAC1H,OAAOF,aAAa,IAAI1F,KAAK,GAAGA,KAAK,GAAG4F,oBAAoB,GAAGD,KAAK,GAAGlD,IAAI,GAAGzC,KAAK,GAAGE,MAAM,GAAGF,KAAK,GAAG,CAAC;IAC1G,CAAC;IACD,IAAIF,cAAc,GAAG,SAASA,cAAcA,CAAC4F,aAAa,EAAEG,aAAa,EAAE3F,MAAM,EAAEyF,KAAK,EAAElD,IAAI,EAAEzC,KAAK,EAAE4F,oBAAoB,EAAE;MAC3H,IAAIF,aAAa,IAAI1F,KAAK,EAAE;QAC1B,OAAO,CAAC;MACV;MACA,OAAOlB,IAAI,CAACkF,GAAG,CAAC,CAAC,EAAE4B,oBAAoB,GAAGF,aAAa,GAAGG,aAAa,GAAG3F,MAAM,GAAGwF,aAAa,GAAG1F,KAAK,GAAG0F,aAAa,GAAGG,aAAa,GAAG3F,MAAM,GAAGwF,aAAa,GAAG,CAAC,GAAG1F,KAAK,CAAC;IAChL,CAAC;IACD,IAAIwC,aAAa,GAAG,SAASA,aAAaA,CAACkD,aAAa,EAAExF,MAAM,EAAEyF,KAAK,EAAElD,IAAI,EAAEzC,KAAK,EAAE0C,OAAO,EAAE;MAC7F,IAAIoD,SAAS,GAAG5F,MAAM,GAAGuC,IAAI,GAAG,CAAC,GAAGzC,KAAK;MACzC,IAAI0F,aAAa,IAAI1F,KAAK,EAAE;QAC1B8F,SAAS,GAAGA,SAAS,IAAI9F,KAAK,GAAG,CAAC,CAAC;MACrC;MACA,OAAO2C,OAAO,CAACmD,SAAS,EAAEpD,OAAO,CAAC;IACpC,CAAC;IACD,IAAIjB,SAAS,GAAG8D,kBAAkB,CAAC/G,MAAM,CAACiD,SAAS,EAAE7B,UAAU,CAACzC,GAAG,CAAC;IACpE,IAAIuE,UAAU,GAAG6D,kBAAkB,CAAC/G,MAAM,CAACkD,UAAU,EAAE9B,UAAU,CAACxC,IAAI,CAAC;IACvE,IAAIkD,QAAQ,GAAGlG,IAAI,GAAG;MACpBG,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE;IACR,CAAC,GAAG,CAAC;IACL,IAAIuL,OAAO,GAAGjL,SAAS;IACvB,IAAIyF,cAAc,GAAG,KAAK;IAC1B,IAAIyF,YAAY,GAAG9I,aAAa,CAACgC,OAAO;IACxC,IAAI9E,IAAI,EAAE;MACR,IAAI6L,YAAY,GAAG/I,aAAa,CAACgC,OAAO,CAAC/B,GAAG,IAAIsE,SAAS;MACzD,IAAIyE,aAAa,GAAGhJ,aAAa,CAACgC,OAAO,CAAC9B,IAAI,IAAIsE,UAAU;MAC5D,IAAI,CAAC3H,KAAK,CAACtC,UAAU,IAAIsC,KAAK,CAACtC,UAAU,KAAKwO,YAAY,IAAIC,aAAa,CAAC,EAAE;QAC5E,IAAIC,YAAY,GAAG;UACjB5L,IAAI,EAAEiL,qBAAqB,CAAC/D,SAAS,EAAE1H,KAAK,CAAC9C,QAAQ,CAAC,CAAC,CAAC,CAAC;UACzDuD,IAAI,EAAEgL,qBAAqB,CAAC9D,UAAU,EAAE3H,KAAK,CAAC9C,QAAQ,CAAC,CAAC,CAAC;QAC3D,CAAC;QACD,IAAImP,YAAY,GAAG;UACjB7L,IAAI,EAAEkL,qBAAqB,CAACU,YAAY,CAAC5L,IAAI,EAAEG,UAAU,CAACH,IAAI,EAAEO,SAAS,CAACP,IAAI,EAAEe,uBAAuB,CAACf,IAAI,EAAEmB,sBAAsB,CAAC,CAAC,CAAC,EAAEuK,YAAY,CAAC;UACtJzL,IAAI,EAAEiL,qBAAqB,CAACU,YAAY,CAAC3L,IAAI,EAAEE,UAAU,CAACF,IAAI,EAAEM,SAAS,CAACN,IAAI,EAAEc,uBAAuB,CAACd,IAAI,EAAEkB,sBAAsB,CAAC,CAAC,CAAC,EAAEwK,aAAa;QACxJ,CAAC;QACD5F,QAAQ,GAAG;UACT/F,IAAI,EAAEuF,cAAc,CAACqG,YAAY,CAAC5L,IAAI,EAAE6L,YAAY,CAAC7L,IAAI,EAAEG,UAAU,CAACH,IAAI,EAAEO,SAAS,CAACP,IAAI,EAAEe,uBAAuB,CAACf,IAAI,EAAEmB,sBAAsB,CAAC,CAAC,CAAC,EAAEuK,YAAY,CAAC;UAClKzL,IAAI,EAAEsF,cAAc,CAACqG,YAAY,CAAC3L,IAAI,EAAE4L,YAAY,CAAC5L,IAAI,EAAEE,UAAU,CAACF,IAAI,EAAEM,SAAS,CAACN,IAAI,EAAEc,uBAAuB,CAACd,IAAI,EAAEkB,sBAAsB,CAAC,CAAC,CAAC,EAAEwK,aAAa;QACpK,CAAC;QACDH,OAAO,GAAG;UACRxL,IAAI,EAAEiI,aAAa,CAAC2D,YAAY,CAAC5L,IAAI,EAAE+F,QAAQ,CAAC/F,IAAI,EAAEO,SAAS,CAACP,IAAI,EAAEe,uBAAuB,CAACf,IAAI,EAAEmB,sBAAsB,CAAC,CAAC,CAAC,CAAC;UAC9HlB,IAAI,EAAEgI,aAAa,CAAC2D,YAAY,CAAC3L,IAAI,EAAE8F,QAAQ,CAAC9F,IAAI,EAAEM,SAAS,CAACN,IAAI,EAAEc,uBAAuB,CAACd,IAAI,EAAEkB,sBAAsB,CAAC,CAAC,CAAC,EAAE,IAAI;QACrI,CAAC;QACD6E,cAAc,GAAGD,QAAQ,CAAC/F,IAAI,KAAKG,UAAU,CAACH,IAAI,IAAIwL,OAAO,CAACxL,IAAI,KAAKO,SAAS,CAACP,IAAI,IAAI+F,QAAQ,CAAC9F,IAAI,KAAKE,UAAU,CAACF,IAAI,IAAIuL,OAAO,CAACvL,IAAI,KAAKM,SAAS,CAACN,IAAI,IAAIqD,kBAAkB,CAACqB,OAAO;QAC3L8G,YAAY,GAAG;UACb7I,GAAG,EAAEsE,SAAS;UACdrE,IAAI,EAAEsE;QACR,CAAC;MACH;IACF,CAAC,MAAM;MACL,IAAIC,SAAS,GAAGxH,UAAU,GAAGuH,UAAU,GAAGD,SAAS;MACnD,IAAI4E,mBAAmB,GAAGnJ,aAAa,CAACgC,OAAO,IAAIyC,SAAS;MAC5D,IAAI,CAAC5H,KAAK,CAACtC,UAAU,IAAIsC,KAAK,CAACtC,UAAU,IAAI4O,mBAAmB,EAAE;QAChE,IAAIC,cAAc,GAAGd,qBAAqB,CAAC7D,SAAS,EAAE5H,KAAK,CAAC9C,QAAQ,CAAC;QACrE,IAAIsP,cAAc,GAAGd,qBAAqB,CAACa,cAAc,EAAE5L,UAAU,EAAEI,SAAS,EAAEQ,uBAAuB,EAAEI,sBAAsB,EAAE2K,mBAAmB,CAAC;QACvJ/F,QAAQ,GAAGR,cAAc,CAACwG,cAAc,EAAEC,cAAc,EAAE7L,UAAU,EAAEI,SAAS,EAAEQ,uBAAuB,EAAEI,sBAAsB,EAAE2K,mBAAmB,CAAC;QACtJN,OAAO,GAAGvD,aAAa,CAAC8D,cAAc,EAAEhG,QAAQ,EAAExF,SAAS,EAAEQ,uBAAuB,EAAEI,sBAAsB,CAAC;QAC7G6E,cAAc,GAAGD,QAAQ,KAAK5F,UAAU,IAAIqL,OAAO,KAAKjL,SAAS,IAAI+C,kBAAkB,CAACqB,OAAO;QAC/F8G,YAAY,GAAGrE,SAAS;MAC1B;IACF;IACA,OAAO;MACLpF,KAAK,EAAE+D,QAAQ;MACf9D,IAAI,EAAEuJ,OAAO;MACbxF,cAAc,EAAEA,cAAc;MAC9BoB,SAAS,EAAEqE;IACb,CAAC;EACH,CAAC;EACD,IAAIQ,cAAc,GAAG,SAASA,cAAcA,CAACtI,KAAK,EAAE;IAClD,IAAIuI,qBAAqB,GAAGnB,sBAAsB,CAACpH,KAAK,CAAC;MACvD3B,KAAK,GAAGkK,qBAAqB,CAAClK,KAAK;MACnCC,IAAI,GAAGiK,qBAAqB,CAACjK,IAAI;MACjC+D,cAAc,GAAGkG,qBAAqB,CAAClG,cAAc;MACrDoB,SAAS,GAAG8E,qBAAqB,CAAC9E,SAAS;IAC7C,IAAIpB,cAAc,EAAE;MAClB,IAAImG,QAAQ,GAAG;QACbnK,KAAK,EAAEA,KAAK;QACZC,IAAI,EAAEA;MACR,CAAC;MACDuI,kBAAkB,CAAC2B,QAAQ,CAAC;MAC5B/L,aAAa,CAAC4B,KAAK,CAAC;MACpBxB,YAAY,CAACyB,IAAI,CAAC;MAClBU,aAAa,CAACgC,OAAO,GAAGyC,SAAS;MACjC5H,KAAK,CAACrB,mBAAmB,IAAIqB,KAAK,CAACrB,mBAAmB,CAACgO,QAAQ,CAAC;MAChE,IAAI3M,KAAK,CAACpC,IAAI,IAAIyH,aAAa,CAAC7C,KAAK,CAAC,EAAE;QACtC,IAAIoK,gBAAgB,GAAG;UACrBpK,KAAK,EAAExC,KAAK,CAAC1C,IAAI,GAAGyH,IAAI,CAACkE,GAAG,CAACnE,cAAc,CAACtC,KAAK,CAAC,GAAGxC,KAAK,CAAC1C,IAAI,EAAE,CAAC0C,KAAK,CAAC/C,KAAK,IAAI,EAAE,EAAEtD,MAAM,GAAGqG,KAAK,CAAC1C,IAAI,CAAC,GAAGkF,KAAK;UACjHC,IAAI,EAAEsC,IAAI,CAACkE,GAAG,CAACjJ,KAAK,CAAC1C,IAAI,GAAG,CAACwH,cAAc,CAACtC,KAAK,CAAC,GAAG,CAAC,IAAIxC,KAAK,CAAC1C,IAAI,GAAGmF,IAAI,EAAE,CAACzC,KAAK,CAAC/C,KAAK,IAAI,EAAE,EAAEtD,MAAM;QACzG,CAAC;QACD,IAAIkT,kBAAkB,GAAG,CAAC9I,aAAa,CAACoB,OAAO,IAAIpB,aAAa,CAACoB,OAAO,CAAC3C,KAAK,KAAKoK,gBAAgB,CAACpK,KAAK,IAAIuB,aAAa,CAACoB,OAAO,CAAC1C,IAAI,KAAKmK,gBAAgB,CAACnK,IAAI;QACjKoK,kBAAkB,IAAI7M,KAAK,CAACpB,UAAU,IAAIoB,KAAK,CAACpB,UAAU,CAACgO,gBAAgB,CAAC;QAC5E7I,aAAa,CAACoB,OAAO,GAAGyH,gBAAgB;MAC1C;IACF;EACF,CAAC;EACD,IAAIE,SAAS,GAAG,SAASpO,QAAQA,CAACyF,KAAK,EAAE;IACvCnE,KAAK,CAACtB,QAAQ,IAAIsB,KAAK,CAACtB,QAAQ,CAACyF,KAAK,CAAC;IACvC,IAAInE,KAAK,CAACxC,KAAK,EAAE;MACf,IAAI8F,aAAa,CAAC6B,OAAO,EAAE;QACzB4H,YAAY,CAACzJ,aAAa,CAAC6B,OAAO,CAAC;MACrC;MACA,IAAIE,aAAa,CAAC1E,UAAU,CAAC,EAAE;QAC7B,IAAI,CAACoB,YAAY,IAAI/B,KAAK,CAAC3B,UAAU,EAAE;UACrC,IAAI2O,sBAAsB,GAAGzB,sBAAsB,CAACpH,KAAK,CAAC;YACxDqC,cAAc,GAAGwG,sBAAsB,CAACxG,cAAc;UACxD,IAAIyG,OAAO,GAAGzG,cAAc,KAAKxG,KAAK,CAAC1C,IAAI,GAAG+H,aAAa,CAAC1E,UAAU,CAAC,GAAG,KAAK,CAAC;UAChFsM,OAAO,IAAIjL,eAAe,CAAC,IAAI,CAAC;QAClC;QACAsB,aAAa,CAAC6B,OAAO,GAAG+H,UAAU,CAAC,YAAY;UAC7CT,cAAc,CAACtI,KAAK,CAAC;UACrB,IAAIpC,YAAY,IAAI/B,KAAK,CAAC3B,UAAU,KAAK,CAAC2B,KAAK,CAACpC,IAAI,IAAIoC,KAAK,CAAC/B,OAAO,KAAKC,SAAS,CAAC,EAAE;YACpF8D,eAAe,CAAC,KAAK,CAAC;YACtBZ,YAAY,CAAC0D,cAAc,CAACnE,UAAU,CAAC,CAAC;UAC1C;QACF,CAAC,EAAEX,KAAK,CAACxC,KAAK,CAAC;MACjB;IACF,CAAC,MAAM;MACLiP,cAAc,CAACtI,KAAK,CAAC;IACvB;EACF,CAAC;EACD,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAG;IACjC,IAAIb,aAAa,CAAC4B,OAAO,EAAE;MACzB4H,YAAY,CAACxJ,aAAa,CAAC4B,OAAO,CAAC;IACrC;IACA5B,aAAa,CAAC4B,OAAO,GAAG+H,UAAU,CAAC,YAAY;MAC7C,IAAIpK,UAAU,CAACqC,OAAO,EAAE;QACtB,IAAIgI,KAAK,GAAG,CAACpU,UAAU,CAACyQ,QAAQ,CAAC1G,UAAU,CAACqC,OAAO,CAAC,EAAEpM,UAAU,CAAC0Q,SAAS,CAAC3G,UAAU,CAACqC,OAAO,CAAC,CAAC;UAC7FuE,KAAK,GAAGyD,KAAK,CAAC,CAAC,CAAC;UAChBxD,MAAM,GAAGwD,KAAK,CAAC,CAAC,CAAC;QACnB,IAAIC,WAAW,GAAG1D,KAAK,KAAKhG,YAAY,CAACyB,OAAO;UAC9CkI,YAAY,GAAG1D,MAAM,KAAKhG,aAAa,CAACwB,OAAO;QACjD,IAAImI,MAAM,GAAGjN,IAAI,GAAG+M,WAAW,IAAIC,YAAY,GAAGjN,UAAU,GAAGgN,WAAW,GAAGjN,QAAQ,GAAGkN,YAAY,GAAG,KAAK;QAC5G,IAAIC,MAAM,EAAE;UACV1L,yBAAyB,CAAC5B,KAAK,CAACzC,iBAAiB,CAAC;UAClDmG,YAAY,CAACyB,OAAO,GAAGuE,KAAK;UAC5B/F,aAAa,CAACwB,OAAO,GAAGwE,MAAM;UAC9B/F,mBAAmB,CAACuB,OAAO,GAAGpM,UAAU,CAACyQ,QAAQ,CAACxG,WAAW,CAACmC,OAAO,CAAC;UACtEtB,oBAAoB,CAACsB,OAAO,GAAGpM,UAAU,CAAC0Q,SAAS,CAACzG,WAAW,CAACmC,OAAO,CAAC;QAC1E;MACF;IACF,CAAC,EAAEnF,KAAK,CAACvC,WAAW,CAAC;EACvB,CAAC;EACD,IAAI8P,UAAU,GAAG,SAASA,UAAUA,CAACC,aAAa,EAAE;IAClD,IAAIC,KAAK,GAAG,CAACzN,KAAK,CAAC/C,KAAK,IAAI,EAAE,EAAEtD,MAAM;IACtC,IAAI8L,KAAK,GAAGpF,IAAI,GAAGM,UAAU,CAACH,IAAI,GAAGgN,aAAa,GAAG7M,UAAU,GAAG6M,aAAa;IAC/E,OAAO;MACL/H,KAAK,EAAEA,KAAK;MACZgI,KAAK,EAAEA,KAAK;MACZjL,KAAK,EAAEiD,KAAK,KAAK,CAAC;MAClBhD,IAAI,EAAEgD,KAAK,KAAKgI,KAAK,GAAG,CAAC;MACzBC,IAAI,EAAEjI,KAAK,GAAG,CAAC,KAAK,CAAC;MACrBkI,GAAG,EAAElI,KAAK,GAAG,CAAC,KAAK,CAAC;MACpBzF,KAAK,EAAEA;IACT,CAAC;EACH,CAAC;EACD,IAAI4N,aAAa,GAAG,SAASA,aAAaA,CAACnI,KAAK,EAAEoI,UAAU,EAAE;IAC5D,IAAIJ,KAAK,GAAGtL,cAAc,CAACxI,MAAM,IAAI,CAAC;IACtC,OAAOyF,aAAa,CAAC;MACnBqG,KAAK,EAAEA,KAAK;MACZgI,KAAK,EAAEA,KAAK;MACZjL,KAAK,EAAEiD,KAAK,KAAK,CAAC;MAClBhD,IAAI,EAAEgD,KAAK,KAAKgI,KAAK,GAAG,CAAC;MACzBC,IAAI,EAAEjI,KAAK,GAAG,CAAC,KAAK,CAAC;MACrBkI,GAAG,EAAElI,KAAK,GAAG,CAAC,KAAK,CAAC;MACpBzF,KAAK,EAAEA;IACT,CAAC,EAAE6N,UAAU,CAAC;EAChB,CAAC;EACD,IAAI1G,WAAW,GAAG,SAASA,WAAWA,CAAA,EAAG;IACvC,IAAIlK,KAAK,GAAG+C,KAAK,CAAC/C,KAAK;IACvB,IAAIA,KAAK,IAAI,CAAC8E,YAAY,EAAE;MAC1B,IAAI1B,IAAI,EAAE;QACR,OAAOpD,KAAK,CAAChB,KAAK,CAAC+D,KAAK,CAACtC,UAAU,GAAG,CAAC,GAAGiD,UAAU,CAACH,IAAI,EAAEO,SAAS,CAACP,IAAI,CAAC,CAACqI,GAAG,CAAC,UAAUiF,IAAI,EAAE;UAC7F,OAAO9N,KAAK,CAAChC,OAAO,GAAG8P,IAAI,GAAGA,IAAI,CAAC7R,KAAK,CAAC+D,KAAK,CAACtC,UAAU,GAAG,CAAC,GAAGiD,UAAU,CAACF,IAAI,EAAEM,SAAS,CAACN,IAAI,CAAC;QAClG,CAAC,CAAC;MACJ,CAAC,MAAM,IAAIL,UAAU,IAAIJ,KAAK,CAAChC,OAAO,EAAE;QACtC,OAAOf,KAAK;MACd;MACA,OAAOA,KAAK,CAAChB,KAAK,CAAC+D,KAAK,CAACtC,UAAU,GAAG,CAAC,GAAGiD,UAAU,EAAEI,SAAS,CAAC;IAClE;IACA,OAAO,EAAE;EACX,CAAC;EACD,IAAIgN,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAG;IACjC,IAAIjL,UAAU,CAACqC,OAAO,IAAI6I,SAAS,CAAC,CAAC,EAAE;MACrC/I,iBAAiB,CAACjC,WAAW,CAACmC,OAAO,CAAC;MACtC8I,IAAI,CAAC,CAAC;MACN1J,wBAAwB,CAAC,CAAC;MAC1BK,6BAA6B,CAAC,CAAC;MAC/BlB,YAAY,CAACyB,OAAO,GAAGpM,UAAU,CAACyQ,QAAQ,CAAC1G,UAAU,CAACqC,OAAO,CAAC;MAC9DxB,aAAa,CAACwB,OAAO,GAAGpM,UAAU,CAAC0Q,SAAS,CAAC3G,UAAU,CAACqC,OAAO,CAAC;MAChEvB,mBAAmB,CAACuB,OAAO,GAAGpM,UAAU,CAACyQ,QAAQ,CAACxG,WAAW,CAACmC,OAAO,CAAC;MACtEtB,oBAAoB,CAACsB,OAAO,GAAGpM,UAAU,CAAC0Q,SAAS,CAACzG,WAAW,CAACmC,OAAO,CAAC;IAC1E;EACF,CAAC;EACD,IAAI8I,IAAI,GAAG,SAASA,IAAIA,CAAA,EAAG;IACzB,IAAI,CAACjO,KAAK,CAACnC,QAAQ,IAAImQ,SAAS,CAAC,CAAC,EAAE;MAClCvD,OAAO,CAAC,CAAC;MACTlC,gBAAgB,CAAC,CAAC;MAClBwC,aAAa,CAAC,CAAC;IACjB;EACF,CAAC;EACD,IAAIiD,SAAS,GAAG,SAASA,SAASA,CAAA,EAAG;IACnC,IAAIjV,UAAU,CAACiV,SAAS,CAAClL,UAAU,CAACqC,OAAO,CAAC,EAAE;MAC5C,IAAI+I,IAAI,GAAGpL,UAAU,CAACqC,OAAO,CAACgJ,qBAAqB,CAAC,CAAC;MACrD,OAAOD,IAAI,CAACxE,KAAK,GAAG,CAAC,IAAIwE,IAAI,CAACvE,MAAM,GAAG,CAAC;IAC1C;IACA,OAAO,KAAK;EACd,CAAC;EACDrR,KAAK,CAAC8V,SAAS,CAAC,YAAY;IAC1B,IAAI,CAACpK,eAAe,CAACmB,OAAO,IAAI6I,SAAS,CAAC,CAAC,EAAE;MAC3CD,QAAQ,CAAC,CAAC;MACV/J,eAAe,CAACmB,OAAO,GAAG,IAAI;IAChC;EACF,CAAC,CAAC;EACFtM,eAAe,CAAC,YAAY;IAC1BoV,IAAI,CAAC,CAAC;EACR,CAAC,EAAE,CAACjO,KAAK,CAAC9C,QAAQ,EAAE8C,KAAK,CAAC7C,YAAY,EAAE6C,KAAK,CAAC5C,WAAW,CAAC,CAAC;EAC3DvE,eAAe,CAAC,YAAY;IAC1B,IAAImH,KAAK,CAACzC,iBAAiB,KAAKoE,sBAAsB,EAAE;MACtDC,yBAAyB,CAAC5B,KAAK,CAACzC,iBAAiB,CAAC;IACpD;EACF,CAAC,EAAE,CAACyC,KAAK,CAACzC,iBAAiB,CAAC,CAAC;EAC7B1E,eAAe,CAAC,YAAY;IAC1B,IAAImH,KAAK,CAACzC,iBAAiB,KAAKoE,sBAAsB,EAAE;MACtDsM,IAAI,CAAC,CAAC,CAAC,CAAC;IACV;EACF,CAAC,EAAE,CAACtM,sBAAsB,CAAC,CAAC;EAC5B9I,eAAe,CAAC,YAAY;IAC1B;IACA,IAAIwV,aAAa,GAAGnO,SAAS,CAACjD,KAAK,KAAKiB,SAAS,IAAIgC,SAAS,CAACjD,KAAK,KAAK,IAAI;IAC7E,IAAIqR,gBAAgB,GAAGtO,KAAK,CAAC/C,KAAK,KAAKiB,SAAS,IAAI8B,KAAK,CAAC/C,KAAK,KAAK,IAAI;;IAExE;IACA,IAAIsR,cAAc,GAAGF,aAAa,GAAGnO,SAAS,CAACjD,KAAK,CAACtD,MAAM,GAAG,CAAC;IAC/D,IAAI6U,iBAAiB,GAAGF,gBAAgB,GAAGtO,KAAK,CAAC/C,KAAK,CAACtD,MAAM,GAAG,CAAC;;IAEjE;IACA,IAAI8U,aAAa,GAAGF,cAAc,KAAKC,iBAAiB;;IAExD;IACA,IAAInO,IAAI,IAAI,CAACoO,aAAa,EAAE;MAC1B;MACA,IAAIC,iBAAiB,GAAGL,aAAa,IAAInO,SAAS,CAACjD,KAAK,CAACtD,MAAM,GAAG,CAAC,GAAGuG,SAAS,CAACjD,KAAK,CAAC,CAAC,CAAC,CAACtD,MAAM,GAAG,CAAC;MACnG,IAAIgV,oBAAoB,GAAGL,gBAAgB,IAAItO,KAAK,CAAC/C,KAAK,CAACtD,MAAM,GAAG,CAAC,GAAGqG,KAAK,CAAC/C,KAAK,CAAC,CAAC,CAAC,CAACtD,MAAM,GAAG,CAAC;;MAEjG;MACA8U,aAAa,GAAGC,iBAAiB,KAAKC,oBAAoB;IAC5D;;IAEA;IACA,IAAI,CAACN,aAAa,IAAII,aAAa,EAAE;MACnCR,IAAI,CAAC,CAAC;IACR;IACA,IAAIhQ,OAAO,GAAG8D,YAAY;IAC1B,IAAI/B,KAAK,CAACpC,IAAI,IAAIsC,SAAS,CAACjC,OAAO,KAAK+B,KAAK,CAAC/B,OAAO,IAAI+B,KAAK,CAAC/B,OAAO,KAAK8D,YAAY,EAAE;MACvFC,eAAe,CAAChC,KAAK,CAAC/B,OAAO,CAAC;MAC9BA,OAAO,GAAG+B,KAAK,CAAC/B,OAAO;IACzB;IACAiL,iBAAiB,CAACjL,OAAO,CAAC;EAC5B,CAAC,CAAC;EACFpF,eAAe,CAAC,YAAY;IAC1BsK,aAAa,CAACgC,OAAO,GAAG9E,IAAI,GAAG;MAC7B+C,GAAG,EAAE,CAAC;MACNC,IAAI,EAAE;IACR,CAAC,GAAG,CAAC;EACP,CAAC,EAAE,CAACrD,KAAK,CAAC3C,WAAW,CAAC,CAAC;EACvB/E,KAAK,CAACsW,mBAAmB,CAAChP,GAAG,EAAE,YAAY;IACzC,OAAO;MACLI,KAAK,EAAEA,KAAK;MACZ6E,aAAa,EAAEA,aAAa;MAC5BS,QAAQ,EAAEA,QAAQ;MAClBE,aAAa,EAAEA,aAAa;MAC5BiB,YAAY,EAAEA,YAAY;MAC1BG,gBAAgB,EAAEA;IACpB,CAAC;EACH,CAAC,CAAC;EACF,IAAIiI,gBAAgB,GAAG,SAASA,gBAAgBA,CAACpJ,KAAK,EAAE;IACtD,IAAIoI,UAAU,GAAGnU,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKwE,SAAS,GAAGxE,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACvF,IAAI6L,OAAO,GAAGqI,aAAa,CAACnI,KAAK,EAAEoI,UAAU,CAAC;IAC9C,IAAIiB,OAAO,GAAG9V,WAAW,CAAC+V,aAAa,CAAC/O,KAAK,CAAC1B,eAAe,EAAEiH,OAAO,CAAC;IACvE,OAAO,aAAajN,KAAK,CAAC0W,aAAa,CAAC1W,KAAK,CAAC2W,QAAQ,EAAE;MACtDC,GAAG,EAAEzJ;IACP,CAAC,EAAEqJ,OAAO,CAAC;EACb,CAAC;EACD,IAAIK,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAG;IACzC,IAAIC,aAAa,GAAG,gCAAgC;IACpD,IAAIC,gBAAgB,GAAGxP,UAAU,CAAC;MAChC9C,SAAS,EAAEqS;IACb,CAAC,EAAEvM,GAAG,CAAC,aAAa,CAAC,CAAC;IACtB,IAAIyM,IAAI,GAAGtP,KAAK,CAACjC,WAAW,IAAI,aAAazF,KAAK,CAAC0W,aAAa,CAAClW,WAAW,EAAEM,QAAQ,CAAC,CAAC,CAAC,EAAEiW,gBAAgB,EAAE;MAC3GE,IAAI,EAAE;IACR,CAAC,CAAC,CAAC;IACH,IAAIxR,WAAW,GAAG7E,SAAS,CAACsW,UAAU,CAACF,IAAI,EAAElQ,aAAa,CAAC,CAAC,CAAC,EAAEiQ,gBAAgB,CAAC,EAAE;MAChFrP,KAAK,EAAEA;IACT,CAAC,CAAC;IACF,IAAI,CAACA,KAAK,CAAClC,cAAc,IAAIkC,KAAK,CAAC3B,UAAU,IAAI0D,YAAY,EAAE;MAC7D,IAAI0N,UAAU,GAAGxW,UAAU,CAAC,0BAA0B,EAAE;QACtD,qBAAqB,EAAE,CAAC+G,KAAK,CAAC1B;MAChC,CAAC,CAAC;MACF,IAAIoR,QAAQ,GAAG3R,WAAW;MAC1B,IAAIiC,KAAK,CAAC1B,eAAe,EAAE;QACzBoR,QAAQ,GAAGvN,cAAc,CAAC0G,GAAG,CAAC,UAAU8G,CAAC,EAAElK,KAAK,EAAE;UAChD,OAAOoJ,gBAAgB,CAACpJ,KAAK,EAAEpF,IAAI,IAAI;YACrCuP,OAAO,EAAErO,uBAAuB,CAACd;UACnC,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC,MAAM,IAAIT,KAAK,CAACzB,kBAAkB,EAAE;QACnC,IAAIsR,qBAAqB,GAAG;UAC1BT,aAAa,EAAEA,aAAa;UAC5BlK,OAAO,EAAEwK,QAAQ;UACjB1P,KAAK,EAAEA;QACT,CAAC;QACD0P,QAAQ,GAAG1W,WAAW,CAAC+V,aAAa,CAAC/O,KAAK,CAACzB,kBAAkB,EAAEsR,qBAAqB,CAAC;MACvF;MACA,IAAIC,WAAW,GAAGjQ,UAAU,CAAC;QAC3B9C,SAAS,EAAE0S;MACb,CAAC,EAAE5M,GAAG,CAAC,QAAQ,CAAC,CAAC;MACjB,OAAO,aAAavK,KAAK,CAAC0W,aAAa,CAAC,KAAK,EAAEc,WAAW,EAAEJ,QAAQ,CAAC;IACvE;IACA,OAAO,IAAI;EACb,CAAC;EACD,IAAIK,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAG;IACzC,IAAI/P,KAAK,CAAC5B,UAAU,EAAE;MACpB,IAAI4R,WAAW,GAAGnQ,UAAU,CAAC;QAC3BD,GAAG,EAAEqD,UAAU;QACfnG,KAAK,EAAE2G,WAAW,CAAC0B,OAAO;QAC1BpI,SAAS,EAAE;MACb,CAAC,EAAE8F,GAAG,CAAC,QAAQ,CAAC,CAAC;MACjB,OAAO,aAAavK,KAAK,CAAC0W,aAAa,CAAC,KAAK,EAAEgB,WAAW,CAAC;IAC7D;IACA,OAAO,IAAI;EACb,CAAC;EACD,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAACnC,IAAI,EAAErI,KAAK,EAAE;IAChD,IAAIF,OAAO,GAAGgI,UAAU,CAAC9H,KAAK,CAAC;IAC/B,IAAIqJ,OAAO,GAAG9V,WAAW,CAAC+V,aAAa,CAAC/O,KAAK,CAACxB,YAAY,EAAEsP,IAAI,EAAEvI,OAAO,CAAC;IAC1E,OAAO,aAAajN,KAAK,CAAC0W,aAAa,CAAC1W,KAAK,CAAC2W,QAAQ,EAAE;MACtDC,GAAG,EAAE3J,OAAO,CAACE;IACf,CAAC,EAAEqJ,OAAO,CAAC;EACb,CAAC;EACD,IAAIoB,WAAW,GAAG,SAASA,WAAWA,CAAA,EAAG;IACvC,IAAIjT,KAAK,GAAGkK,WAAW,CAAC,CAAC;IACzB,OAAOlK,KAAK,CAAC4L,GAAG,CAACoH,UAAU,CAAC;EAC9B,CAAC;EACD,IAAIE,aAAa,GAAG,SAASA,aAAaA,CAAA,EAAG;IAC3C,IAAIlT,KAAK,GAAGiT,WAAW,CAAC,CAAC;IACzB,IAAInT,SAAS,GAAG9D,UAAU,CAAC,2BAA2B,EAAE;MACtD,2BAA2B,EAAE8I;IAC/B,CAAC,CAAC;IACF,IAAIqO,YAAY,GAAGvQ,UAAU,CAAC;MAC5BD,GAAG,EAAEoD,WAAW;MAChBlG,KAAK,EAAE0G,YAAY,CAAC2B,OAAO;MAC3BpI,SAAS,EAAEA;IACb,CAAC,EAAE8F,GAAG,CAAC,SAAS,CAAC,CAAC;IAClB,IAAIiM,OAAO,GAAG,aAAaxW,KAAK,CAAC0W,aAAa,CAAC,KAAK,EAAEoB,YAAY,EAAEnT,KAAK,CAAC;IAC1E,IAAI+C,KAAK,CAACvB,eAAe,EAAE;MACzB,IAAI4R,cAAc,GAAG;QACnBvT,KAAK,EAAE0G,YAAY,CAAC2B,OAAO;QAC3BpI,SAAS,EAAEA,SAAS;QACpB0G,WAAW,EAAEA,WAAW,CAAC0B,OAAO;QAChCmL,UAAU,EAAE,SAASA,UAAUA,CAACC,EAAE,EAAE;UAClC,OAAOvN,WAAW,CAACmC,OAAO,GAAGnM,WAAW,CAACwX,aAAa,CAACD,EAAE,CAAC;QAC5D,CAAC;QACDE,SAAS,EAAE,SAASA,SAASA,CAACF,EAAE,EAAE;UAChC,OAAOtN,UAAU,CAACkC,OAAO,GAAGnM,WAAW,CAACwX,aAAa,CAACD,EAAE,CAAC;QAC3D,CAAC;QACDG,SAAS,EAAE,SAASA,SAASA,CAACH,EAAE,EAAE;UAChC,OAAOrN,UAAU,CAACiC,OAAO,GAAGnM,WAAW,CAACwX,aAAa,CAACD,EAAE,CAAC;QAC3D,CAAC;QACDtT,KAAK,EAAEkK,WAAW,CAAC,CAAC;QACpBwJ,cAAc,EAAE,SAASA,cAAcA,CAAClL,KAAK,EAAE;UAC7C,OAAO8H,UAAU,CAAC9H,KAAK,CAAC;QAC1B,CAAC;QACD5G,QAAQ,EAAE5B,KAAK;QACfiI,OAAO,EAAE4J,OAAO;QAChB9O,KAAK,EAAEA,KAAK;QACZ/B,OAAO,EAAE8D,YAAY;QACrB6O,gBAAgB,EAAE,SAASA,gBAAgBA,CAACnL,KAAK,EAAEoL,GAAG,EAAE;UACtD,OAAOjD,aAAa,CAACnI,KAAK,EAAEoL,GAAG,CAAC;QAClC,CAAC;QACDvS,eAAe,EAAE0B,KAAK,CAAC1B,eAAe;QACtCpB,QAAQ,EAAE8C,KAAK,CAAC9C,QAAQ;QACxBsD,IAAI,EAAE0G,OAAO,CAAC,CAAC;QACflJ,OAAO,EAAEoJ,UAAU,CAAC,CAAC;QACrBjH,QAAQ,EAAEA,QAAQ;QAClBC,UAAU,EAAEA,UAAU;QACtBC,IAAI,EAAEA;MACR,CAAC;MACD,OAAOrH,WAAW,CAAC+V,aAAa,CAAC/O,KAAK,CAACvB,eAAe,EAAE4R,cAAc,CAAC;IACzE;IACA,OAAOvB,OAAO;EAChB,CAAC;EACD,IAAI9O,KAAK,CAACnC,QAAQ,EAAE;IAClB,IAAIiT,SAAS,GAAG9X,WAAW,CAAC+V,aAAa,CAAC/O,KAAK,CAACvB,eAAe,EAAE;MAC/DxB,KAAK,EAAE+C,KAAK,CAAC/C,KAAK;MAClBuD,IAAI,EAAER,KAAK,CAAC/C,KAAK;MACjBe,OAAO,EAAEgC,KAAK,CAAChC;IACjB,CAAC,CAAC;IACF,OAAO,aAAa1F,KAAK,CAAC0W,aAAa,CAAC1W,KAAK,CAAC2W,QAAQ,EAAE,IAAI,EAAEjP,KAAK,CAACnB,QAAQ,EAAEiS,SAAS,CAAC;EAC1F;EACA,IAAI/T,SAAS,GAAG9D,UAAU,CAAC,mBAAmB,EAAE;IAC9C,0BAA0B,EAAE+G,KAAK,CAACrC,MAAM;IACxC,sCAAsC,EAAE0C,IAAI;IAC5C,kDAAkD,EAAED;EACtD,CAAC,EAAEJ,KAAK,CAACjD,SAAS,CAAC;EACnB,IAAIgU,MAAM,GAAG5B,YAAY,CAAC,CAAC;EAC3B,IAAIL,OAAO,GAAGqB,aAAa,CAAC,CAAC;EAC7B,IAAIa,MAAM,GAAGjB,YAAY,CAAC,CAAC;EAC3B,IAAIkB,SAAS,GAAGpR,UAAU,CAAC;IACzBD,GAAG,EAAEkD,UAAU;IACf/F,SAAS,EAAEA,SAAS;IACpBC,QAAQ,EAAEgD,KAAK,CAAChD,QAAQ;IACxBF,KAAK,EAAEkD,KAAK,CAAClD,KAAK;IAClB4B,QAAQ,EAAE,SAASA,QAAQA,CAACjF,CAAC,EAAE;MAC7B,OAAOqT,SAAS,CAACrT,CAAC,CAAC;IACrB;EACF,CAAC,EAAE+C,mBAAmB,CAAC0U,aAAa,CAAClR,KAAK,CAAC,EAAE6C,GAAG,CAAC,MAAM,CAAC,CAAC;EACzD,OAAO,aAAavK,KAAK,CAAC0W,aAAa,CAAC,KAAK,EAAEiC,SAAS,EAAEnC,OAAO,EAAEkC,MAAM,EAAED,MAAM,CAAC;AACpF,CAAC,CAAC,CAAC;AACHvR,eAAe,CAAC2R,WAAW,GAAG,iBAAiB;AAE/C,SAAS3R,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}