{"ast": null, "code": "import { KEY_PREFIX } from './constants';\nexport default function getStoredState(config) {\n  var transforms = config.transforms || [];\n  var storageKey = \"\".concat(config.keyPrefix !== undefined ? config.keyPrefix : KEY_PREFIX).concat(config.key);\n  var storage = config.storage;\n  var debug = config.debug;\n  var deserialize;\n  if (config.deserialize === false) {\n    deserialize = function deserialize(x) {\n      return x;\n    };\n  } else if (typeof config.deserialize === 'function') {\n    deserialize = config.deserialize;\n  } else {\n    deserialize = defaultDeserialize;\n  }\n  return storage.getItem(storageKey).then(function (serialized) {\n    if (!serialized) return undefined;else {\n      try {\n        var state = {};\n        var rawState = deserialize(serialized);\n        Object.keys(rawState).forEach(function (key) {\n          state[key] = transforms.reduceRight(function (subState, transformer) {\n            return transformer.out(subState, key, rawState);\n          }, deserialize(rawState[key]));\n        });\n        return state;\n      } catch (err) {\n        if (process.env.NODE_ENV !== 'production' && debug) console.log(\"redux-persist/getStoredState: Error restoring data \".concat(serialized), err);\n        throw err;\n      }\n    }\n  });\n}\nfunction defaultDeserialize(serial) {\n  return JSON.parse(serial);\n}", "map": {"version": 3, "names": ["KEY_PREFIX", "getStoredState", "config", "transforms", "storageKey", "concat", "keyPrefix", "undefined", "key", "storage", "debug", "deserialize", "x", "defaultDeserialize", "getItem", "then", "serialized", "state", "rawState", "Object", "keys", "for<PERSON>ach", "reduceRight", "subState", "transformer", "out", "err", "process", "env", "NODE_ENV", "console", "log", "serial", "JSON", "parse"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/node_modules/redux-persist/es/getStoredState.js"], "sourcesContent": ["import { KEY_PREFIX } from './constants';\nexport default function getStoredState(config) {\n  var transforms = config.transforms || [];\n  var storageKey = \"\".concat(config.keyPrefix !== undefined ? config.keyPrefix : KEY_PREFIX).concat(config.key);\n  var storage = config.storage;\n  var debug = config.debug;\n  var deserialize;\n\n  if (config.deserialize === false) {\n    deserialize = function deserialize(x) {\n      return x;\n    };\n  } else if (typeof config.deserialize === 'function') {\n    deserialize = config.deserialize;\n  } else {\n    deserialize = defaultDeserialize;\n  }\n\n  return storage.getItem(storageKey).then(function (serialized) {\n    if (!serialized) return undefined;else {\n      try {\n        var state = {};\n        var rawState = deserialize(serialized);\n        Object.keys(rawState).forEach(function (key) {\n          state[key] = transforms.reduceRight(function (subState, transformer) {\n            return transformer.out(subState, key, rawState);\n          }, deserialize(rawState[key]));\n        });\n        return state;\n      } catch (err) {\n        if (process.env.NODE_ENV !== 'production' && debug) console.log(\"redux-persist/getStoredState: Error restoring data \".concat(serialized), err);\n        throw err;\n      }\n    }\n  });\n}\n\nfunction defaultDeserialize(serial) {\n  return JSON.parse(serial);\n}"], "mappings": "AAAA,SAASA,UAAU,QAAQ,aAAa;AACxC,eAAe,SAASC,cAAcA,CAACC,MAAM,EAAE;EAC7C,IAAIC,UAAU,GAAGD,MAAM,CAACC,UAAU,IAAI,EAAE;EACxC,IAAIC,UAAU,GAAG,EAAE,CAACC,MAAM,CAACH,MAAM,CAACI,SAAS,KAAKC,SAAS,GAAGL,MAAM,CAACI,SAAS,GAAGN,UAAU,CAAC,CAACK,MAAM,CAACH,MAAM,CAACM,GAAG,CAAC;EAC7G,IAAIC,OAAO,GAAGP,MAAM,CAACO,OAAO;EAC5B,IAAIC,KAAK,GAAGR,MAAM,CAACQ,KAAK;EACxB,IAAIC,WAAW;EAEf,IAAIT,MAAM,CAACS,WAAW,KAAK,KAAK,EAAE;IAChCA,WAAW,GAAG,SAASA,WAAWA,CAACC,CAAC,EAAE;MACpC,OAAOA,CAAC;IACV,CAAC;EACH,CAAC,MAAM,IAAI,OAAOV,MAAM,CAACS,WAAW,KAAK,UAAU,EAAE;IACnDA,WAAW,GAAGT,MAAM,CAACS,WAAW;EAClC,CAAC,MAAM;IACLA,WAAW,GAAGE,kBAAkB;EAClC;EAEA,OAAOJ,OAAO,CAACK,OAAO,CAACV,UAAU,CAAC,CAACW,IAAI,CAAC,UAAUC,UAAU,EAAE;IAC5D,IAAI,CAACA,UAAU,EAAE,OAAOT,SAAS,CAAC,KAAK;MACrC,IAAI;QACF,IAAIU,KAAK,GAAG,CAAC,CAAC;QACd,IAAIC,QAAQ,GAAGP,WAAW,CAACK,UAAU,CAAC;QACtCG,MAAM,CAACC,IAAI,CAACF,QAAQ,CAAC,CAACG,OAAO,CAAC,UAAUb,GAAG,EAAE;UAC3CS,KAAK,CAACT,GAAG,CAAC,GAAGL,UAAU,CAACmB,WAAW,CAAC,UAAUC,QAAQ,EAAEC,WAAW,EAAE;YACnE,OAAOA,WAAW,CAACC,GAAG,CAACF,QAAQ,EAAEf,GAAG,EAAEU,QAAQ,CAAC;UACjD,CAAC,EAAEP,WAAW,CAACO,QAAQ,CAACV,GAAG,CAAC,CAAC,CAAC;QAChC,CAAC,CAAC;QACF,OAAOS,KAAK;MACd,CAAC,CAAC,OAAOS,GAAG,EAAE;QACZ,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAInB,KAAK,EAAEoB,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC1B,MAAM,CAACW,UAAU,CAAC,EAAEU,GAAG,CAAC;QAC9I,MAAMA,GAAG;MACX;IACF;EACF,CAAC,CAAC;AACJ;AAEA,SAASb,kBAAkBA,CAACmB,MAAM,EAAE;EAClC,OAAOC,IAAI,CAACC,KAAK,CAACF,MAAM,CAAC;AAC3B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}