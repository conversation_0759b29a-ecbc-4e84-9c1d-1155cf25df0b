{"ast": null, "code": "function _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _nonIterableSpread();\n}\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance\");\n}\nfunction _iterableToArray(iter) {\n  if (Symbol.iterator in Object(iter) || Object.prototype.toString.call(iter) === \"[object Arguments]\") return Array.from(iter);\n}\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) {\n    for (var i = 0, arr2 = new Array(arr.length); i < arr.length; i++) {\n      arr2[i] = arr[i];\n    }\n    return arr2;\n  }\n}\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    });\n    keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n    if (i % 2) {\n      ownKeys(source, true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(source).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n  return target;\n}\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nimport { createStore } from 'redux';\nimport { FLUSH, PAUSE, PERSIST, PURGE, REGISTER, REHYDRATE } from './constants';\nvar initialState = {\n  registry: [],\n  bootstrapped: false\n};\nvar persistorReducer = function persistorReducer() {\n  var state = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : initialState;\n  var action = arguments.length > 1 ? arguments[1] : undefined;\n  switch (action.type) {\n    case REGISTER:\n      return _objectSpread({}, state, {\n        registry: [].concat(_toConsumableArray(state.registry), [action.key])\n      });\n    case REHYDRATE:\n      var firstIndex = state.registry.indexOf(action.key);\n      var registry = _toConsumableArray(state.registry);\n      registry.splice(firstIndex, 1);\n      return _objectSpread({}, state, {\n        registry: registry,\n        bootstrapped: registry.length === 0\n      });\n    default:\n      return state;\n  }\n};\nexport default function persistStore(store, options, cb) {\n  // help catch incorrect usage of passing PersistConfig in as PersistorOptions\n  if (process.env.NODE_ENV !== 'production') {\n    var optionsToTest = options || {};\n    var bannedKeys = ['blacklist', 'whitelist', 'transforms', 'storage', 'keyPrefix', 'migrate'];\n    bannedKeys.forEach(function (k) {\n      if (!!optionsToTest[k]) console.error(\"redux-persist: invalid option passed to persistStore: \\\"\".concat(k, \"\\\". You may be incorrectly passing persistConfig into persistStore, whereas it should be passed into persistReducer.\"));\n    });\n  }\n  var boostrappedCb = cb || false;\n  var _pStore = createStore(persistorReducer, initialState, options && options.enhancer ? options.enhancer : undefined);\n  var register = function register(key) {\n    _pStore.dispatch({\n      type: REGISTER,\n      key: key\n    });\n  };\n  var rehydrate = function rehydrate(key, payload, err) {\n    var rehydrateAction = {\n      type: REHYDRATE,\n      payload: payload,\n      err: err,\n      key: key // dispatch to `store` to rehydrate and `persistor` to track result\n    };\n    store.dispatch(rehydrateAction);\n    _pStore.dispatch(rehydrateAction);\n    if (boostrappedCb && persistor.getState().bootstrapped) {\n      boostrappedCb();\n      boostrappedCb = false;\n    }\n  };\n  var persistor = _objectSpread({}, _pStore, {\n    purge: function purge() {\n      var results = [];\n      store.dispatch({\n        type: PURGE,\n        result: function result(purgeResult) {\n          results.push(purgeResult);\n        }\n      });\n      return Promise.all(results);\n    },\n    flush: function flush() {\n      var results = [];\n      store.dispatch({\n        type: FLUSH,\n        result: function result(flushResult) {\n          results.push(flushResult);\n        }\n      });\n      return Promise.all(results);\n    },\n    pause: function pause() {\n      store.dispatch({\n        type: PAUSE\n      });\n    },\n    persist: function persist() {\n      store.dispatch({\n        type: PERSIST,\n        register: register,\n        rehydrate: rehydrate\n      });\n    }\n  });\n  if (!(options && options.manualPersist)) {\n    persistor.persist();\n  }\n  return persistor;\n}", "map": {"version": 3, "names": ["_toConsumableArray", "arr", "_arrayWithoutHoles", "_iterableToArray", "_nonIterableSpread", "TypeError", "iter", "Symbol", "iterator", "Object", "prototype", "toString", "call", "Array", "from", "isArray", "i", "arr2", "length", "ownKeys", "object", "enumerableOnly", "keys", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "target", "arguments", "source", "for<PERSON>ach", "key", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "obj", "value", "configurable", "writable", "createStore", "FLUSH", "PAUSE", "PERSIST", "PURGE", "REGISTER", "REHYDRATE", "initialState", "registry", "bootstrapped", "persistorReducer", "state", "undefined", "action", "type", "concat", "firstIndex", "indexOf", "splice", "persistStore", "store", "options", "cb", "process", "env", "NODE_ENV", "optionsToTest", "<PERSON><PERSON><PERSON><PERSON>", "k", "console", "error", "boostrappedCb", "_pStore", "enhancer", "register", "dispatch", "rehydrate", "payload", "err", "rehydrateAction", "persistor", "getState", "purge", "results", "result", "purgeResult", "Promise", "all", "flush", "flushResult", "pause", "persist", "manualPersist"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/node_modules/redux-persist/es/persistStore.js"], "sourcesContent": ["function _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _nonIterableSpread(); }\n\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance\"); }\n\nfunction _iterableToArray(iter) { if (Symbol.iterator in Object(iter) || Object.prototype.toString.call(iter) === \"[object Arguments]\") return Array.from(iter); }\n\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) { for (var i = 0, arr2 = new Array(arr.length); i < arr.length; i++) { arr2[i] = arr[i]; } return arr2; } }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nimport { createStore } from 'redux';\nimport { FLUSH, PAUSE, PERSIST, PURGE, REGISTER, REHYDRATE } from './constants';\nvar initialState = {\n  registry: [],\n  bootstrapped: false\n};\n\nvar persistorReducer = function persistorReducer() {\n  var state = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : initialState;\n  var action = arguments.length > 1 ? arguments[1] : undefined;\n\n  switch (action.type) {\n    case REGISTER:\n      return _objectSpread({}, state, {\n        registry: [].concat(_toConsumableArray(state.registry), [action.key])\n      });\n\n    case REHYDRATE:\n      var firstIndex = state.registry.indexOf(action.key);\n\n      var registry = _toConsumableArray(state.registry);\n\n      registry.splice(firstIndex, 1);\n      return _objectSpread({}, state, {\n        registry: registry,\n        bootstrapped: registry.length === 0\n      });\n\n    default:\n      return state;\n  }\n};\n\nexport default function persistStore(store, options, cb) {\n  // help catch incorrect usage of passing PersistConfig in as PersistorOptions\n  if (process.env.NODE_ENV !== 'production') {\n    var optionsToTest = options || {};\n    var bannedKeys = ['blacklist', 'whitelist', 'transforms', 'storage', 'keyPrefix', 'migrate'];\n    bannedKeys.forEach(function (k) {\n      if (!!optionsToTest[k]) console.error(\"redux-persist: invalid option passed to persistStore: \\\"\".concat(k, \"\\\". You may be incorrectly passing persistConfig into persistStore, whereas it should be passed into persistReducer.\"));\n    });\n  }\n\n  var boostrappedCb = cb || false;\n\n  var _pStore = createStore(persistorReducer, initialState, options && options.enhancer ? options.enhancer : undefined);\n\n  var register = function register(key) {\n    _pStore.dispatch({\n      type: REGISTER,\n      key: key\n    });\n  };\n\n  var rehydrate = function rehydrate(key, payload, err) {\n    var rehydrateAction = {\n      type: REHYDRATE,\n      payload: payload,\n      err: err,\n      key: key // dispatch to `store` to rehydrate and `persistor` to track result\n\n    };\n    store.dispatch(rehydrateAction);\n\n    _pStore.dispatch(rehydrateAction);\n\n    if (boostrappedCb && persistor.getState().bootstrapped) {\n      boostrappedCb();\n      boostrappedCb = false;\n    }\n  };\n\n  var persistor = _objectSpread({}, _pStore, {\n    purge: function purge() {\n      var results = [];\n      store.dispatch({\n        type: PURGE,\n        result: function result(purgeResult) {\n          results.push(purgeResult);\n        }\n      });\n      return Promise.all(results);\n    },\n    flush: function flush() {\n      var results = [];\n      store.dispatch({\n        type: FLUSH,\n        result: function result(flushResult) {\n          results.push(flushResult);\n        }\n      });\n      return Promise.all(results);\n    },\n    pause: function pause() {\n      store.dispatch({\n        type: PAUSE\n      });\n    },\n    persist: function persist() {\n      store.dispatch({\n        type: PERSIST,\n        register: register,\n        rehydrate: rehydrate\n      });\n    }\n  });\n\n  if (!(options && options.manualPersist)) {\n    persistor.persist();\n  }\n\n  return persistor;\n}"], "mappings": "AAAA,SAASA,kBAAkBA,CAACC,GAAG,EAAE;EAAE,OAAOC,kBAAkB,CAACD,GAAG,CAAC,IAAIE,gBAAgB,CAACF,GAAG,CAAC,IAAIG,kBAAkB,CAAC,CAAC;AAAE;AAEpH,SAASA,kBAAkBA,CAAA,EAAG;EAAE,MAAM,IAAIC,SAAS,CAAC,iDAAiD,CAAC;AAAE;AAExG,SAASF,gBAAgBA,CAACG,IAAI,EAAE;EAAE,IAAIC,MAAM,CAACC,QAAQ,IAAIC,MAAM,CAACH,IAAI,CAAC,IAAIG,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACN,IAAI,CAAC,KAAK,oBAAoB,EAAE,OAAOO,KAAK,CAACC,IAAI,CAACR,IAAI,CAAC;AAAE;AAEjK,SAASJ,kBAAkBA,CAACD,GAAG,EAAE;EAAE,IAAIY,KAAK,CAACE,OAAO,CAACd,GAAG,CAAC,EAAE;IAAE,KAAK,IAAIe,CAAC,GAAG,CAAC,EAAEC,IAAI,GAAG,IAAIJ,KAAK,CAACZ,GAAG,CAACiB,MAAM,CAAC,EAAEF,CAAC,GAAGf,GAAG,CAACiB,MAAM,EAAEF,CAAC,EAAE,EAAE;MAAEC,IAAI,CAACD,CAAC,CAAC,GAAGf,GAAG,CAACe,CAAC,CAAC;IAAE;IAAE,OAAOC,IAAI;EAAE;AAAE;AAErK,SAASE,OAAOA,CAACC,MAAM,EAAEC,cAAc,EAAE;EAAE,IAAIC,IAAI,GAAGb,MAAM,CAACa,IAAI,CAACF,MAAM,CAAC;EAAE,IAAIX,MAAM,CAACc,qBAAqB,EAAE;IAAE,IAAIC,OAAO,GAAGf,MAAM,CAACc,qBAAqB,CAACH,MAAM,CAAC;IAAE,IAAIC,cAAc,EAAEG,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;MAAE,OAAOjB,MAAM,CAACkB,wBAAwB,CAACP,MAAM,EAAEM,GAAG,CAAC,CAACE,UAAU;IAAE,CAAC,CAAC;IAAEN,IAAI,CAACO,IAAI,CAACC,KAAK,CAACR,IAAI,EAAEE,OAAO,CAAC;EAAE;EAAE,OAAOF,IAAI;AAAE;AAEpV,SAASS,aAAaA,CAACC,MAAM,EAAE;EAAE,KAAK,IAAIhB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiB,SAAS,CAACf,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAIkB,MAAM,GAAGD,SAAS,CAACjB,CAAC,CAAC,IAAI,IAAI,GAAGiB,SAAS,CAACjB,CAAC,CAAC,GAAG,CAAC,CAAC;IAAE,IAAIA,CAAC,GAAG,CAAC,EAAE;MAAEG,OAAO,CAACe,MAAM,EAAE,IAAI,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;QAAEC,eAAe,CAACL,MAAM,EAAEI,GAAG,EAAEF,MAAM,CAACE,GAAG,CAAC,CAAC;MAAE,CAAC,CAAC;IAAE,CAAC,MAAM,IAAI3B,MAAM,CAAC6B,yBAAyB,EAAE;MAAE7B,MAAM,CAAC8B,gBAAgB,CAACP,MAAM,EAAEvB,MAAM,CAAC6B,yBAAyB,CAACJ,MAAM,CAAC,CAAC;IAAE,CAAC,MAAM;MAAEf,OAAO,CAACe,MAAM,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;QAAE3B,MAAM,CAAC+B,cAAc,CAACR,MAAM,EAAEI,GAAG,EAAE3B,MAAM,CAACkB,wBAAwB,CAACO,MAAM,EAAEE,GAAG,CAAC,CAAC;MAAE,CAAC,CAAC;IAAE;EAAE;EAAE,OAAOJ,MAAM;AAAE;AAErgB,SAASK,eAAeA,CAACI,GAAG,EAAEL,GAAG,EAAEM,KAAK,EAAE;EAAE,IAAIN,GAAG,IAAIK,GAAG,EAAE;IAAEhC,MAAM,CAAC+B,cAAc,CAACC,GAAG,EAAEL,GAAG,EAAE;MAAEM,KAAK,EAAEA,KAAK;MAAEd,UAAU,EAAE,IAAI;MAAEe,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAEH,GAAG,CAACL,GAAG,CAAC,GAAGM,KAAK;EAAE;EAAE,OAAOD,GAAG;AAAE;AAEhN,SAASI,WAAW,QAAQ,OAAO;AACnC,SAASC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,aAAa;AAC/E,IAAIC,YAAY,GAAG;EACjBC,QAAQ,EAAE,EAAE;EACZC,YAAY,EAAE;AAChB,CAAC;AAED,IAAIC,gBAAgB,GAAG,SAASA,gBAAgBA,CAAA,EAAG;EACjD,IAAIC,KAAK,GAAGvB,SAAS,CAACf,MAAM,GAAG,CAAC,IAAIe,SAAS,CAAC,CAAC,CAAC,KAAKwB,SAAS,GAAGxB,SAAS,CAAC,CAAC,CAAC,GAAGmB,YAAY;EAC5F,IAAIM,MAAM,GAAGzB,SAAS,CAACf,MAAM,GAAG,CAAC,GAAGe,SAAS,CAAC,CAAC,CAAC,GAAGwB,SAAS;EAE5D,QAAQC,MAAM,CAACC,IAAI;IACjB,KAAKT,QAAQ;MACX,OAAOnB,aAAa,CAAC,CAAC,CAAC,EAAEyB,KAAK,EAAE;QAC9BH,QAAQ,EAAE,EAAE,CAACO,MAAM,CAAC5D,kBAAkB,CAACwD,KAAK,CAACH,QAAQ,CAAC,EAAE,CAACK,MAAM,CAACtB,GAAG,CAAC;MACtE,CAAC,CAAC;IAEJ,KAAKe,SAAS;MACZ,IAAIU,UAAU,GAAGL,KAAK,CAACH,QAAQ,CAACS,OAAO,CAACJ,MAAM,CAACtB,GAAG,CAAC;MAEnD,IAAIiB,QAAQ,GAAGrD,kBAAkB,CAACwD,KAAK,CAACH,QAAQ,CAAC;MAEjDA,QAAQ,CAACU,MAAM,CAACF,UAAU,EAAE,CAAC,CAAC;MAC9B,OAAO9B,aAAa,CAAC,CAAC,CAAC,EAAEyB,KAAK,EAAE;QAC9BH,QAAQ,EAAEA,QAAQ;QAClBC,YAAY,EAAED,QAAQ,CAACnC,MAAM,KAAK;MACpC,CAAC,CAAC;IAEJ;MACE,OAAOsC,KAAK;EAChB;AACF,CAAC;AAED,eAAe,SAASQ,YAAYA,CAACC,KAAK,EAAEC,OAAO,EAAEC,EAAE,EAAE;EACvD;EACA,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAIC,aAAa,GAAGL,OAAO,IAAI,CAAC,CAAC;IACjC,IAAIM,UAAU,GAAG,CAAC,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,SAAS,EAAE,WAAW,EAAE,SAAS,CAAC;IAC5FA,UAAU,CAACrC,OAAO,CAAC,UAAUsC,CAAC,EAAE;MAC9B,IAAI,CAAC,CAACF,aAAa,CAACE,CAAC,CAAC,EAAEC,OAAO,CAACC,KAAK,CAAC,0DAA0D,CAACf,MAAM,CAACa,CAAC,EAAE,sHAAsH,CAAC,CAAC;IACrO,CAAC,CAAC;EACJ;EAEA,IAAIG,aAAa,GAAGT,EAAE,IAAI,KAAK;EAE/B,IAAIU,OAAO,GAAGhC,WAAW,CAACU,gBAAgB,EAAEH,YAAY,EAAEc,OAAO,IAAIA,OAAO,CAACY,QAAQ,GAAGZ,OAAO,CAACY,QAAQ,GAAGrB,SAAS,CAAC;EAErH,IAAIsB,QAAQ,GAAG,SAASA,QAAQA,CAAC3C,GAAG,EAAE;IACpCyC,OAAO,CAACG,QAAQ,CAAC;MACfrB,IAAI,EAAET,QAAQ;MACdd,GAAG,EAAEA;IACP,CAAC,CAAC;EACJ,CAAC;EAED,IAAI6C,SAAS,GAAG,SAASA,SAASA,CAAC7C,GAAG,EAAE8C,OAAO,EAAEC,GAAG,EAAE;IACpD,IAAIC,eAAe,GAAG;MACpBzB,IAAI,EAAER,SAAS;MACf+B,OAAO,EAAEA,OAAO;MAChBC,GAAG,EAAEA,GAAG;MACR/C,GAAG,EAAEA,GAAG,CAAC;IAEX,CAAC;IACD6B,KAAK,CAACe,QAAQ,CAACI,eAAe,CAAC;IAE/BP,OAAO,CAACG,QAAQ,CAACI,eAAe,CAAC;IAEjC,IAAIR,aAAa,IAAIS,SAAS,CAACC,QAAQ,CAAC,CAAC,CAAChC,YAAY,EAAE;MACtDsB,aAAa,CAAC,CAAC;MACfA,aAAa,GAAG,KAAK;IACvB;EACF,CAAC;EAED,IAAIS,SAAS,GAAGtD,aAAa,CAAC,CAAC,CAAC,EAAE8C,OAAO,EAAE;IACzCU,KAAK,EAAE,SAASA,KAAKA,CAAA,EAAG;MACtB,IAAIC,OAAO,GAAG,EAAE;MAChBvB,KAAK,CAACe,QAAQ,CAAC;QACbrB,IAAI,EAAEV,KAAK;QACXwC,MAAM,EAAE,SAASA,MAAMA,CAACC,WAAW,EAAE;UACnCF,OAAO,CAAC3D,IAAI,CAAC6D,WAAW,CAAC;QAC3B;MACF,CAAC,CAAC;MACF,OAAOC,OAAO,CAACC,GAAG,CAACJ,OAAO,CAAC;IAC7B,CAAC;IACDK,KAAK,EAAE,SAASA,KAAKA,CAAA,EAAG;MACtB,IAAIL,OAAO,GAAG,EAAE;MAChBvB,KAAK,CAACe,QAAQ,CAAC;QACbrB,IAAI,EAAEb,KAAK;QACX2C,MAAM,EAAE,SAASA,MAAMA,CAACK,WAAW,EAAE;UACnCN,OAAO,CAAC3D,IAAI,CAACiE,WAAW,CAAC;QAC3B;MACF,CAAC,CAAC;MACF,OAAOH,OAAO,CAACC,GAAG,CAACJ,OAAO,CAAC;IAC7B,CAAC;IACDO,KAAK,EAAE,SAASA,KAAKA,CAAA,EAAG;MACtB9B,KAAK,CAACe,QAAQ,CAAC;QACbrB,IAAI,EAAEZ;MACR,CAAC,CAAC;IACJ,CAAC;IACDiD,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;MAC1B/B,KAAK,CAACe,QAAQ,CAAC;QACbrB,IAAI,EAAEX,OAAO;QACb+B,QAAQ,EAAEA,QAAQ;QAClBE,SAAS,EAAEA;MACb,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EAEF,IAAI,EAAEf,OAAO,IAAIA,OAAO,CAAC+B,aAAa,CAAC,EAAE;IACvCZ,SAAS,CAACW,OAAO,CAAC,CAAC;EACrB;EAEA,OAAOX,SAAS;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}