{"ast": null, "code": "/**\r\n * Audit Utilities for parsing and comparing audit data\r\n * Provides helper functions for processing audit log data and identifying changes\r\n */\n\n/**\r\n * Safely parse JSON string, return null if invalid\r\n * @param {string} jsonString - JSON string to parse\r\n * @returns {Object|null} Parsed object or null if invalid\r\n */\nexport const safeJsonParse = jsonString => {\n  if (!jsonString || typeof jsonString !== 'string') {\n    return null;\n  }\n  try {\n    return JSON.parse(jsonString);\n  } catch (error) {\n    console.warn('Failed to parse JSON:', jsonString, error);\n    return null;\n  }\n};\n\n/**\r\n * Compare two objects and return only the fields that have changed\r\n * @param {Object} originalObj - Original object\r\n * @param {Object} currentObj - Current object\r\n * @returns {Array} Array of change objects with field, original, and current values\r\n */\nexport const getChangedFields = (originalObj, currentObj) => {\n  const changes = [];\n  if (!originalObj && !currentObj) {\n    return changes;\n  }\n\n  // If one is null/undefined, treat as complete change\n  if (!originalObj || !currentObj) {\n    return [{\n      field: 'Complete Object',\n      original: originalObj || '(empty)',\n      current: currentObj || '(empty)',\n      changeType: !originalObj ? 'added' : 'removed'\n    }];\n  }\n\n  // Get all unique keys from both objects\n  const allKeys = new Set([...Object.keys(originalObj), ...Object.keys(currentObj)]);\n  allKeys.forEach(key => {\n    const originalValue = originalObj[key];\n    const currentValue = currentObj[key];\n\n    // Skip if values are identical\n    if (JSON.stringify(originalValue) === JSON.stringify(currentValue)) {\n      return;\n    }\n\n    // Determine change type\n    let changeType = 'modified';\n    if (originalValue === undefined || originalValue === null) {\n      changeType = 'added';\n    } else if (currentValue === undefined || currentValue === null) {\n      changeType = 'removed';\n    }\n    changes.push({\n      field: key,\n      original: originalValue,\n      current: currentValue,\n      changeType\n    });\n  });\n  return changes;\n};\n\n/**\r\n * Process audit detail record to extract meaningful field changes\r\n * @param {Object} auditDetail - Audit detail record from API\r\n * @returns {Object} Processed audit detail with parsed changes\r\n */\nexport const processAuditDetail = auditDetail => {\n  if (!auditDetail) {\n    return null;\n  }\n  const originalJson = safeJsonParse(auditDetail.minCreatedOnOriginalValue);\n  const currentJson = safeJsonParse(auditDetail.maxCreatedOnCurrentValue);\n\n  // If both are valid JSON objects, compare them\n  if (originalJson && currentJson && typeof originalJson === 'object' && typeof currentJson === 'object') {\n    const changes = getChangedFields(originalJson, currentJson);\n    return {\n      ...auditDetail,\n      hasJsonChanges: true,\n      fieldChanges: changes,\n      originalParsed: originalJson,\n      currentParsed: currentJson\n    };\n  }\n\n  // If not JSON or parsing failed, treat as simple string comparison\n  const hasStringChange = auditDetail.minCreatedOnOriginalValue !== auditDetail.maxCreatedOnCurrentValue;\n  return {\n    ...auditDetail,\n    hasJsonChanges: false,\n    fieldChanges: hasStringChange ? [{\n      field: auditDetail.fieldName || 'Value',\n      original: auditDetail.minCreatedOnOriginalValue,\n      current: auditDetail.maxCreatedOnCurrentValue,\n      changeType: 'modified'\n    }] : [],\n    originalParsed: null,\n    currentParsed: null\n  };\n};\n\n/**\r\n * Format field value for display\r\n * @param {any} value - Value to format\r\n * @param {number} maxLength - Maximum length before truncation\r\n * @returns {string} Formatted value\r\n */\nexport const formatFieldValue = (value, maxLength = 100) => {\n  if (value === null || value === undefined) {\n    return '(empty)';\n  }\n  if (typeof value === 'object') {\n    const jsonString = JSON.stringify(value, null, 2);\n    return jsonString.length > maxLength ? jsonString.substring(0, maxLength) + '...' : jsonString;\n  }\n  const stringValue = String(value);\n  return stringValue.length > maxLength ? stringValue.substring(0, maxLength) + '...' : stringValue;\n};\n\n/**\r\n * Get change type severity for UI styling\r\n * @param {string} changeType - Type of change (added, removed, modified)\r\n * @returns {string} PrimeReact severity class\r\n */\nexport const getChangeTypeSeverity = changeType => {\n  switch (changeType) {\n    case 'added':\n      return 'success';\n    case 'removed':\n      return 'danger';\n    case 'modified':\n      return 'warning';\n    default:\n      return 'info';\n  }\n};\n\n/**\r\n * Get change type icon\r\n * @param {string} changeType - Type of change (added, removed, modified)\r\n * @returns {string} PrimeReact icon class\r\n */\nexport const getChangeTypeIcon = changeType => {\n  switch (changeType) {\n    case 'added':\n      return 'pi pi-plus-circle';\n    case 'removed':\n      return 'pi pi-minus-circle';\n    case 'modified':\n      return 'pi pi-pencil';\n    default:\n      return 'pi pi-info-circle';\n  }\n};\n\n/**\r\n * Process all audit details to extract meaningful changes\r\n * @param {Array} auditDetails - Array of audit detail records\r\n * @returns {Array} Array of processed audit details with changes\r\n */\nexport const processAllAuditDetails = auditDetails => {\n  if (!Array.isArray(auditDetails)) {\n    return [];\n  }\n  return auditDetails.map(processAuditDetail).filter(detail => detail && detail.fieldChanges && detail.fieldChanges.length > 0);\n};", "map": {"version": 3, "names": ["safeJsonParse", "jsonString", "JSON", "parse", "error", "console", "warn", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>s", "originalObj", "currentObj", "changes", "field", "original", "current", "changeType", "allKeys", "Set", "Object", "keys", "for<PERSON>ach", "key", "originalValue", "currentValue", "stringify", "undefined", "push", "processAuditDetail", "auditDetail", "original<PERSON>son", "minCreatedOnOriginalValue", "<PERSON><PERSON><PERSON>", "maxCreatedOnCurrentValue", "has<PERSON>son<PERSON><PERSON><PERSON>", "fieldChanges", "originalParsed", "currentParsed", "hasStringChange", "fieldName", "formatFieldValue", "value", "max<PERSON><PERSON><PERSON>", "length", "substring", "stringValue", "String", "getChangeTypeSeverity", "getChangeTypeIcon", "processAllAuditDetails", "auditDetails", "Array", "isArray", "map", "filter", "detail"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/core/utils/auditUtils.js"], "sourcesContent": ["/**\r\n * Audit Utilities for parsing and comparing audit data\r\n * Provides helper functions for processing audit log data and identifying changes\r\n */\r\n\r\n/**\r\n * Safely parse JSON string, return null if invalid\r\n * @param {string} jsonString - JSON string to parse\r\n * @returns {Object|null} Parsed object or null if invalid\r\n */\r\nexport const safeJsonParse = (jsonString) => {\r\n  if (!jsonString || typeof jsonString !== 'string') {\r\n    return null;\r\n  }\r\n  \r\n  try {\r\n    return JSON.parse(jsonString);\r\n  } catch (error) {\r\n    console.warn('Failed to parse JSON:', jsonString, error);\r\n    return null;\r\n  }\r\n};\r\n\r\n/**\r\n * Compare two objects and return only the fields that have changed\r\n * @param {Object} originalObj - Original object\r\n * @param {Object} currentObj - Current object\r\n * @returns {Array} Array of change objects with field, original, and current values\r\n */\r\nexport const getChangedFields = (originalObj, currentObj) => {\r\n  const changes = [];\r\n  \r\n  if (!originalObj && !currentObj) {\r\n    return changes;\r\n  }\r\n  \r\n  // If one is null/undefined, treat as complete change\r\n  if (!originalObj || !currentObj) {\r\n    return [{\r\n      field: 'Complete Object',\r\n      original: originalObj || '(empty)',\r\n      current: currentObj || '(empty)',\r\n      changeType: !originalObj ? 'added' : 'removed'\r\n    }];\r\n  }\r\n  \r\n  // Get all unique keys from both objects\r\n  const allKeys = new Set([\r\n    ...Object.keys(originalObj),\r\n    ...Object.keys(currentObj)\r\n  ]);\r\n  \r\n  allKeys.forEach(key => {\r\n    const originalValue = originalObj[key];\r\n    const currentValue = currentObj[key];\r\n    \r\n    // Skip if values are identical\r\n    if (JSON.stringify(originalValue) === JSON.stringify(currentValue)) {\r\n      return;\r\n    }\r\n    \r\n    // Determine change type\r\n    let changeType = 'modified';\r\n    if (originalValue === undefined || originalValue === null) {\r\n      changeType = 'added';\r\n    } else if (currentValue === undefined || currentValue === null) {\r\n      changeType = 'removed';\r\n    }\r\n    \r\n    changes.push({\r\n      field: key,\r\n      original: originalValue,\r\n      current: currentValue,\r\n      changeType\r\n    });\r\n  });\r\n  \r\n  return changes;\r\n};\r\n\r\n/**\r\n * Process audit detail record to extract meaningful field changes\r\n * @param {Object} auditDetail - Audit detail record from API\r\n * @returns {Object} Processed audit detail with parsed changes\r\n */\r\nexport const processAuditDetail = (auditDetail) => {\r\n  if (!auditDetail) {\r\n    return null;\r\n  }\r\n  \r\n  const originalJson = safeJsonParse(auditDetail.minCreatedOnOriginalValue);\r\n  const currentJson = safeJsonParse(auditDetail.maxCreatedOnCurrentValue);\r\n  \r\n  // If both are valid JSON objects, compare them\r\n  if (originalJson && currentJson && typeof originalJson === 'object' && typeof currentJson === 'object') {\r\n    const changes = getChangedFields(originalJson, currentJson);\r\n    \r\n    return {\r\n      ...auditDetail,\r\n      hasJsonChanges: true,\r\n      fieldChanges: changes,\r\n      originalParsed: originalJson,\r\n      currentParsed: currentJson\r\n    };\r\n  }\r\n  \r\n  // If not JSON or parsing failed, treat as simple string comparison\r\n  const hasStringChange = auditDetail.minCreatedOnOriginalValue !== auditDetail.maxCreatedOnCurrentValue;\r\n  \r\n  return {\r\n    ...auditDetail,\r\n    hasJsonChanges: false,\r\n    fieldChanges: hasStringChange ? [{\r\n      field: auditDetail.fieldName || 'Value',\r\n      original: auditDetail.minCreatedOnOriginalValue,\r\n      current: auditDetail.maxCreatedOnCurrentValue,\r\n      changeType: 'modified'\r\n    }] : [],\r\n    originalParsed: null,\r\n    currentParsed: null\r\n  };\r\n};\r\n\r\n/**\r\n * Format field value for display\r\n * @param {any} value - Value to format\r\n * @param {number} maxLength - Maximum length before truncation\r\n * @returns {string} Formatted value\r\n */\r\nexport const formatFieldValue = (value, maxLength = 100) => {\r\n  if (value === null || value === undefined) {\r\n    return '(empty)';\r\n  }\r\n  \r\n  if (typeof value === 'object') {\r\n    const jsonString = JSON.stringify(value, null, 2);\r\n    return jsonString.length > maxLength \r\n      ? jsonString.substring(0, maxLength) + '...' \r\n      : jsonString;\r\n  }\r\n  \r\n  const stringValue = String(value);\r\n  return stringValue.length > maxLength \r\n    ? stringValue.substring(0, maxLength) + '...' \r\n    : stringValue;\r\n};\r\n\r\n/**\r\n * Get change type severity for UI styling\r\n * @param {string} changeType - Type of change (added, removed, modified)\r\n * @returns {string} PrimeReact severity class\r\n */\r\nexport const getChangeTypeSeverity = (changeType) => {\r\n  switch (changeType) {\r\n    case 'added':\r\n      return 'success';\r\n    case 'removed':\r\n      return 'danger';\r\n    case 'modified':\r\n      return 'warning';\r\n    default:\r\n      return 'info';\r\n  }\r\n};\r\n\r\n/**\r\n * Get change type icon\r\n * @param {string} changeType - Type of change (added, removed, modified)\r\n * @returns {string} PrimeReact icon class\r\n */\r\nexport const getChangeTypeIcon = (changeType) => {\r\n  switch (changeType) {\r\n    case 'added':\r\n      return 'pi pi-plus-circle';\r\n    case 'removed':\r\n      return 'pi pi-minus-circle';\r\n    case 'modified':\r\n      return 'pi pi-pencil';\r\n    default:\r\n      return 'pi pi-info-circle';\r\n  }\r\n};\r\n\r\n/**\r\n * Process all audit details to extract meaningful changes\r\n * @param {Array} auditDetails - Array of audit detail records\r\n * @returns {Array} Array of processed audit details with changes\r\n */\r\nexport const processAllAuditDetails = (auditDetails) => {\r\n  if (!Array.isArray(auditDetails)) {\r\n    return [];\r\n  }\r\n  \r\n  return auditDetails\r\n    .map(processAuditDetail)\r\n    .filter(detail => detail && detail.fieldChanges && detail.fieldChanges.length > 0);\r\n};\r\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,aAAa,GAAIC,UAAU,IAAK;EAC3C,IAAI,CAACA,UAAU,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;IACjD,OAAO,IAAI;EACb;EAEA,IAAI;IACF,OAAOC,IAAI,CAACC,KAAK,CAACF,UAAU,CAAC;EAC/B,CAAC,CAAC,OAAOG,KAAK,EAAE;IACdC,OAAO,CAACC,IAAI,CAAC,uBAAuB,EAAEL,UAAU,EAAEG,KAAK,CAAC;IACxD,OAAO,IAAI;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMG,gBAAgB,GAAGA,CAACC,WAAW,EAAEC,UAAU,KAAK;EAC3D,MAAMC,OAAO,GAAG,EAAE;EAElB,IAAI,CAACF,WAAW,IAAI,CAACC,UAAU,EAAE;IAC/B,OAAOC,OAAO;EAChB;;EAEA;EACA,IAAI,CAACF,WAAW,IAAI,CAACC,UAAU,EAAE;IAC/B,OAAO,CAAC;MACNE,KAAK,EAAE,iBAAiB;MACxBC,QAAQ,EAAEJ,WAAW,IAAI,SAAS;MAClCK,OAAO,EAAEJ,UAAU,IAAI,SAAS;MAChCK,UAAU,EAAE,CAACN,WAAW,GAAG,OAAO,GAAG;IACvC,CAAC,CAAC;EACJ;;EAEA;EACA,MAAMO,OAAO,GAAG,IAAIC,GAAG,CAAC,CACtB,GAAGC,MAAM,CAACC,IAAI,CAACV,WAAW,CAAC,EAC3B,GAAGS,MAAM,CAACC,IAAI,CAACT,UAAU,CAAC,CAC3B,CAAC;EAEFM,OAAO,CAACI,OAAO,CAACC,GAAG,IAAI;IACrB,MAAMC,aAAa,GAAGb,WAAW,CAACY,GAAG,CAAC;IACtC,MAAME,YAAY,GAAGb,UAAU,CAACW,GAAG,CAAC;;IAEpC;IACA,IAAIlB,IAAI,CAACqB,SAAS,CAACF,aAAa,CAAC,KAAKnB,IAAI,CAACqB,SAAS,CAACD,YAAY,CAAC,EAAE;MAClE;IACF;;IAEA;IACA,IAAIR,UAAU,GAAG,UAAU;IAC3B,IAAIO,aAAa,KAAKG,SAAS,IAAIH,aAAa,KAAK,IAAI,EAAE;MACzDP,UAAU,GAAG,OAAO;IACtB,CAAC,MAAM,IAAIQ,YAAY,KAAKE,SAAS,IAAIF,YAAY,KAAK,IAAI,EAAE;MAC9DR,UAAU,GAAG,SAAS;IACxB;IAEAJ,OAAO,CAACe,IAAI,CAAC;MACXd,KAAK,EAAES,GAAG;MACVR,QAAQ,EAAES,aAAa;MACvBR,OAAO,EAAES,YAAY;MACrBR;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF,OAAOJ,OAAO;AAChB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMgB,kBAAkB,GAAIC,WAAW,IAAK;EACjD,IAAI,CAACA,WAAW,EAAE;IAChB,OAAO,IAAI;EACb;EAEA,MAAMC,YAAY,GAAG5B,aAAa,CAAC2B,WAAW,CAACE,yBAAyB,CAAC;EACzE,MAAMC,WAAW,GAAG9B,aAAa,CAAC2B,WAAW,CAACI,wBAAwB,CAAC;;EAEvE;EACA,IAAIH,YAAY,IAAIE,WAAW,IAAI,OAAOF,YAAY,KAAK,QAAQ,IAAI,OAAOE,WAAW,KAAK,QAAQ,EAAE;IACtG,MAAMpB,OAAO,GAAGH,gBAAgB,CAACqB,YAAY,EAAEE,WAAW,CAAC;IAE3D,OAAO;MACL,GAAGH,WAAW;MACdK,cAAc,EAAE,IAAI;MACpBC,YAAY,EAAEvB,OAAO;MACrBwB,cAAc,EAAEN,YAAY;MAC5BO,aAAa,EAAEL;IACjB,CAAC;EACH;;EAEA;EACA,MAAMM,eAAe,GAAGT,WAAW,CAACE,yBAAyB,KAAKF,WAAW,CAACI,wBAAwB;EAEtG,OAAO;IACL,GAAGJ,WAAW;IACdK,cAAc,EAAE,KAAK;IACrBC,YAAY,EAAEG,eAAe,GAAG,CAAC;MAC/BzB,KAAK,EAAEgB,WAAW,CAACU,SAAS,IAAI,OAAO;MACvCzB,QAAQ,EAAEe,WAAW,CAACE,yBAAyB;MAC/ChB,OAAO,EAAEc,WAAW,CAACI,wBAAwB;MAC7CjB,UAAU,EAAE;IACd,CAAC,CAAC,GAAG,EAAE;IACPoB,cAAc,EAAE,IAAI;IACpBC,aAAa,EAAE;EACjB,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMG,gBAAgB,GAAGA,CAACC,KAAK,EAAEC,SAAS,GAAG,GAAG,KAAK;EAC1D,IAAID,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKf,SAAS,EAAE;IACzC,OAAO,SAAS;EAClB;EAEA,IAAI,OAAOe,KAAK,KAAK,QAAQ,EAAE;IAC7B,MAAMtC,UAAU,GAAGC,IAAI,CAACqB,SAAS,CAACgB,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;IACjD,OAAOtC,UAAU,CAACwC,MAAM,GAAGD,SAAS,GAChCvC,UAAU,CAACyC,SAAS,CAAC,CAAC,EAAEF,SAAS,CAAC,GAAG,KAAK,GAC1CvC,UAAU;EAChB;EAEA,MAAM0C,WAAW,GAAGC,MAAM,CAACL,KAAK,CAAC;EACjC,OAAOI,WAAW,CAACF,MAAM,GAAGD,SAAS,GACjCG,WAAW,CAACD,SAAS,CAAC,CAAC,EAAEF,SAAS,CAAC,GAAG,KAAK,GAC3CG,WAAW;AACjB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAME,qBAAqB,GAAI/B,UAAU,IAAK;EACnD,QAAQA,UAAU;IAChB,KAAK,OAAO;MACV,OAAO,SAAS;IAClB,KAAK,SAAS;MACZ,OAAO,QAAQ;IACjB,KAAK,UAAU;MACb,OAAO,SAAS;IAClB;MACE,OAAO,MAAM;EACjB;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMgC,iBAAiB,GAAIhC,UAAU,IAAK;EAC/C,QAAQA,UAAU;IAChB,KAAK,OAAO;MACV,OAAO,mBAAmB;IAC5B,KAAK,SAAS;MACZ,OAAO,oBAAoB;IAC7B,KAAK,UAAU;MACb,OAAO,cAAc;IACvB;MACE,OAAO,mBAAmB;EAC9B;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMiC,sBAAsB,GAAIC,YAAY,IAAK;EACtD,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,YAAY,CAAC,EAAE;IAChC,OAAO,EAAE;EACX;EAEA,OAAOA,YAAY,CAChBG,GAAG,CAACzB,kBAAkB,CAAC,CACvB0B,MAAM,CAACC,MAAM,IAAIA,MAAM,IAAIA,MAAM,CAACpB,YAAY,IAAIoB,MAAM,CAACpB,YAAY,CAACQ,MAAM,GAAG,CAAC,CAAC;AACtF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}