{"ast": null, "code": "import { User<PERSON>anager, WebStorageStateStore, Log } from \"oidc-client\";\nimport APP_CONFIG from \"../config/appConfig\";\nimport { IDENTITY_CLIENT_CONFIG } from \"../config/identityClientConfig\";\nimport { IDENTITY_META_CONFIG } from \"../config/identityMetaConfig\";\n\n/**\r\n * Authentication/Authorization service.\r\n * Corporate with Identity Server 4.\r\n *\r\n * Reference: https://medium.com/@franciscopa91/how-to-implement-oidc-authentication-with-react-context-api-and-react-router-205e13f2d49\r\n */\nexport default class AuthService {\n  constructor() {\n    this.signinRedirectCallback = () => {\n      console.log(\"SigninRedirectCallback called\");\n      console.log(\"Current URL:\", window.location.href);\n\n      // Log all session storage keys for debugging\n      const sessionKeys = [];\n      for (let i = 0; i < sessionStorage.length; i++) {\n        const key = sessionStorage.key(i);\n        if (key && key.includes('oidc')) {\n          sessionKeys.push(key);\n        }\n      }\n      console.log(\"OIDC SessionStorage keys before callback:\", sessionKeys);\n      return this.userManager.signinRedirectCallback().then(user => {\n        console.log(\"SigninRedirectCallback success:\", user);\n        const redirectUrl = localStorage.getItem(\"redirectUri\");\n        if (redirectUrl && redirectUrl.length > 0) {\n          window.location.replace(redirectUrl);\n        } else {\n          this.navigateToHome();\n        }\n        return user;\n      }).catch(error => {\n        console.error(\"SigninRedirectCallback error:\", error);\n        console.error(\"Error details:\", error.message, error.stack);\n\n        // Log session storage state when error occurs\n        const sessionKeysOnError = [];\n        for (let i = 0; i < sessionStorage.length; i++) {\n          const key = sessionStorage.key(i);\n          if (key && key.includes('oidc')) {\n            sessionKeysOnError.push(key);\n          }\n        }\n        console.log(\"OIDC SessionStorage keys on error:\", sessionKeysOnError);\n\n        // Clear any stale state and redirect to home\n        this.userManager.clearStaleState();\n        localStorage.removeItem(\"redirectUri\");\n        throw error; // Re-throw to allow component to handle\n      });\n    };\n    /**\r\n     *  Get logon user object if system has. Otherwises, return null.\r\n     *  Note: Here only try to return user profile info. It does not require system login.\r\n     *  If no login user, then, return null.\r\n     *  Work for UI dispolay user profile information purpose.\r\n     *  For example, as for home page which is public page,\r\n     *  system also will try to publish logon user info on the menu\r\n     *  if there is any user login success.\r\n     *\r\n     *\r\n     * Return User object json format:\r\n     * {\r\n     *   id,\r\n     *   userName,\r\n     *   displayName,\r\n     *   firstName,\r\n     *   lastName,\r\n     *   email,\r\n     *   language,\r\n     *   roles,\r\n     *   permissions,\r\n     *   authProviderId\r\n     * }\r\n     *\r\n     */\n    this.getUser = () => {\n      const oidcStorage = JSON.parse(\n      /**\r\n       * Note: It is OIDC-Client library default storage.\r\n       * The key format is defined by oidc-client library.\r\n       * Note: Developer no needs to modify the session key.\r\n       * */\n      sessionStorage.getItem(`oidc.user:${APP_CONFIG.iamDomain}:${APP_CONFIG.clientId}`));\n      if (oidcStorage && oidcStorage.access_token && !oidcStorage.expired) {\n        const user = this.toAppUser(oidcStorage);\n        // console.log(\"getUser\", JSON.stringify(user));\n        return user;\n      } else {\n        return null;\n      }\n    };\n    this.parseJwt = token => {\n      const base64Url = token.split(\".\")[1];\n      const base64 = base64Url.replace(\"-\", \"+\").replace(\"_\", \"/\");\n      return JSON.parse(window.atob(base64));\n    };\n    this.signin = () => {\n      this.signinRedirect();\n    };\n    /** Redirect to Identity Server 4 login page for login requirement. */\n    this.signinRedirect = () => {\n      console.log(\"SigninRedirect called from:\", window.location.href);\n\n      // Clear any stale state before starting new authentication\n      this.userManager.clearStaleState();\n      localStorage.setItem(\"redirectUri\", window.location.pathname);\n      console.log(\"Redirect URI set to:\", window.location.pathname);\n      console.log(\"Starting signin redirect...\");\n      return this.userManager.signinRedirect({}).then(() => {\n        console.log(\"SigninRedirect initiated successfully\");\n      }).catch(error => {\n        console.error(\"SigninRedirect error:\", error);\n        throw error;\n      });\n    };\n    /** Redirect to Home page which is public page. */\n    this.navigateToHome = () => {\n      window.location.replace(APP_CONFIG.basePath);\n    };\n    this.isAuthenticated = () => {\n      const oidcStorage = JSON.parse(\n      /**\r\n       * Note: It is OIDC-Client library default storage.\r\n       * The key format is defined by oidc-client library.\r\n       * Note: Developer no needs to modify the session key.\r\n       * */\n      sessionStorage.getItem(`oidc.user:${APP_CONFIG.iamDomain}:${APP_CONFIG.clientId}`));\n      return !!oidcStorage && !!oidcStorage.access_token && !oidcStorage.expired;\n    };\n    this.signinSilent = () => {\n      console.log(\"signinSilent called\");\n      this.userManager.signinSilent().then(user => {\n        console.log(\"silent signed in success\", user);\n      }).catch(err => {\n        console.log(\"silent signed in error\", err);\n        // If silent signin fails, redirect to login\n        console.log(\"Silent signin failed, redirecting to login\");\n        this.signinRedirect();\n      });\n    };\n    this.signinSilentCallback = () => {\n      console.log(\"SigninSilentCallback called\");\n      this.userManager.signinSilentCallback();\n    };\n    this.createSigninRequest = () => {\n      return this.userManager.createSigninRequest();\n    };\n    this.signout = () => {\n      this.userManager.signoutRedirect({\n        id_token_hint: localStorage.getItem(\"id_token\")\n      });\n      this.userManager.clearStaleState();\n    };\n    this.signoutRedirectCallback = () => {\n      this.userManager.signoutRedirectCallback().then(() => {\n        localStorage.clear();\n        // Go to home page.\n        window.location.replace(\"/\");\n      });\n      this.userManager.clearStaleState();\n    };\n    /** Clear stale authentication state */\n    this.clearAuthState = () => {\n      this.userManager.clearStaleState();\n      localStorage.removeItem(\"redirectUri\");\n\n      // Clear all OIDC-related storage\n      const keysToRemove = [];\n      for (let i = 0; i < sessionStorage.length; i++) {\n        const key = sessionStorage.key(i);\n        if (key && key.includes('oidc')) {\n          keysToRemove.push(key);\n        }\n      }\n      keysToRemove.forEach(key => sessionStorage.removeItem(key));\n\n      // Also clear localStorage OIDC keys\n      const localKeysToRemove = [];\n      for (let i = 0; i < localStorage.length; i++) {\n        const key = localStorage.key(i);\n        if (key && key.includes('oidc')) {\n          localKeysToRemove.push(key);\n        }\n      }\n      localKeysToRemove.forEach(key => localStorage.removeItem(key));\n    };\n    /** Debug method to log current authentication state */\n    this.debugAuthState = () => {\n      console.log(\"=== Authentication Debug Info ===\");\n      console.log(\"APP_CONFIG:\", APP_CONFIG);\n      console.log(\"Current URL:\", window.location.href);\n      console.log(\"Redirect URI:\", localStorage.getItem(\"redirectUri\"));\n\n      // Parse URL parameters\n      const urlParams = new URLSearchParams(window.location.search);\n      console.log(\"URL Parameters:\", Object.fromEntries(urlParams));\n\n      // Check sessionStorage for OIDC state\n      const stateKeys = [];\n      for (let i = 0; i < sessionStorage.length; i++) {\n        const key = sessionStorage.key(i);\n        if (key && key.includes('oidc')) {\n          stateKeys.push({\n            key: key,\n            value: sessionStorage.getItem(key)\n          });\n        }\n      }\n      console.log(\"OIDC SessionStorage entries:\", stateKeys);\n\n      // Check if user is authenticated\n      console.log(\"Is Authenticated:\", this.isAuthenticated());\n      console.log(\"Current User:\", this.getUser());\n      console.log(\"=== End Debug Info ===\");\n    };\n    /** Manual callback handler for debugging */\n    this.manualCallbackHandler = () => {\n      const urlParams = new URLSearchParams(window.location.search);\n      const code = urlParams.get('code');\n      const state = urlParams.get('state');\n      const sessionState = urlParams.get('session_state');\n      console.log(\"Manual callback handler:\");\n      console.log(\"Code:\", code);\n      console.log(\"State:\", state);\n      console.log(\"Session State:\", sessionState);\n\n      // Check different possible state key formats\n      const possibleStateKeys = [`oidc.state:${state}`, `oidc.${state}`, state, `state:${state}`];\n      let foundState = null;\n      for (const stateKey of possibleStateKeys) {\n        const storedState = sessionStorage.getItem(stateKey);\n        console.log(`Checking state key: ${stateKey} -> ${storedState ? 'FOUND' : 'NOT FOUND'}`);\n        if (storedState) {\n          foundState = {\n            key: stateKey,\n            value: storedState\n          };\n          break;\n        }\n      }\n      if (!foundState) {\n        console.error(\"No matching state found for:\", state);\n        // List all state-related keys\n        const allStateKeys = [];\n        for (let i = 0; i < sessionStorage.length; i++) {\n          const key = sessionStorage.key(i);\n          if (key && (key.includes('oidc') || key.includes('state'))) {\n            allStateKeys.push({\n              key: key,\n              value: sessionStorage.getItem(key)\n            });\n          }\n        }\n        console.log(\"All OIDC/state keys:\", allStateKeys);\n      } else {\n        console.log(\"Found matching state:\", foundState);\n      }\n    };\n    // Create state store without explicit prefix - let library handle it\n    const stateStore = new WebStorageStateStore({\n      store: window.sessionStorage\n    });\n    const userStore = new WebStorageStateStore({\n      store: window.sessionStorage\n    });\n    this.userManager = new UserManager({\n      ...IDENTITY_CLIENT_CONFIG,\n      userStore: userStore,\n      stateStore: stateStore,\n      metadata: {\n        ...IDENTITY_META_CONFIG\n      }\n    });\n    //\n    // Register OIDC client Logger\n    // Exposed OIDC-Client library debugging information into console log.\n    //\n    Log.logger = console;\n    Log.level = Log.WARN; // Enable logging to help debug state issues\n\n    this.userManager.events.addUserLoaded(user => {\n      if (window.location.href.indexOf(\"auth-callback\") !== -1) {\n        this.navigateToHome();\n      }\n    });\n    this.userManager.events.addSilentRenewError(e => {\n      console.log(\"silent renew error\", e.message);\n      // If silent renewal fails, redirect to login\n      console.log(\"Silent renewal failed, redirecting to login\");\n      this.signinRedirect();\n    });\n    this.userManager.events.addAccessTokenExpired(() => {\n      console.log(\"token expired\");\n      this.signinSilent();\n    });\n\n    // Clear stale state on initialization\n    this.userManager.clearStaleState();\n  }\n  /**\r\n   * Convert ID4 user to DTO user. Only called by getUser.\r\n   * @param user ID4 returned user claims, refer to OIDC-Client library.\r\n   */\n  toAppUser(user) {\n    const result = {};\n    if (user && !user.expired) {\n      var _user$profile$name, _user$profile$email, _user$profile$given_n, _user$profile$family_, _user$profile$display;\n      // console.debug(\"toAppUser\", \"OIDC user: \" + JSON.stringify(user.profile));\n\n      result.id = user.sub;\n      result.userName = (_user$profile$name = user.profile.name) !== null && _user$profile$name !== void 0 ? _user$profile$name : \"\";\n      result.email = (_user$profile$email = user.profile.email) !== null && _user$profile$email !== void 0 ? _user$profile$email : \"\";\n      result.firstName = (_user$profile$given_n = user.profile.given_name) !== null && _user$profile$given_n !== void 0 ? _user$profile$given_n : \"\";\n      result.lastName = (_user$profile$family_ = user.profile.family_name) !== null && _user$profile$family_ !== void 0 ? _user$profile$family_ : \"\";\n      result.displayName = (_user$profile$display = user.profile.displayname) !== null && _user$profile$display !== void 0 ? _user$profile$display : \"\";\n      result.language = user.profile.locale;\n      result.accessToken = user.access_token;\n      result.authProviderId = user.profile.idp === \"local\" ? \"APP\" : user.profile.idp; // Database driving login is \"local\" for Identity Server. Convert it to \"APP\".\n      const strRoles = user.profile.role;\n      const strPermissions = user.profile.permissions;\n      if (strRoles) {\n        const roles = [];\n        strRoles.split(\",\").forEach(r => {\n          roles.push(r);\n        });\n        result.roles = roles;\n      }\n      if (strPermissions) {\n        const permissions = [];\n        strPermissions.split(\",\").forEach(p => {\n          permissions.push(p);\n        });\n        result.permissions = permissions;\n      }\n    }\n    return result;\n  }\n}", "map": {"version": 3, "names": ["UserManager", "WebStorageStateStore", "Log", "APP_CONFIG", "IDENTITY_CLIENT_CONFIG", "IDENTITY_META_CONFIG", "AuthService", "constructor", "signinRedirectCallback", "console", "log", "window", "location", "href", "<PERSON><PERSON><PERSON><PERSON>", "i", "sessionStorage", "length", "key", "includes", "push", "userManager", "then", "user", "redirectUrl", "localStorage", "getItem", "replace", "navigateToHome", "catch", "error", "message", "stack", "sessionKeysOnError", "clearStaleState", "removeItem", "getUser", "oidcStorage", "JSON", "parse", "iamDomain", "clientId", "access_token", "expired", "toAppUser", "parseJwt", "token", "base64Url", "split", "base64", "atob", "signin", "signinRedirect", "setItem", "pathname", "basePath", "isAuthenticated", "signinSilent", "err", "signinSilentCallback", "createSigninRequest", "signout", "signoutRedirect", "id_token_hint", "signoutRedirectCallback", "clear", "clearAuthState", "keysToRemove", "for<PERSON>ach", "localKeysToRemove", "debugAuthState", "urlParams", "URLSearchParams", "search", "Object", "fromEntries", "stateKeys", "value", "manualCallbackHandler", "code", "get", "state", "sessionState", "possibleStateKeys", "foundState", "stateKey", "storedState", "allStateKeys", "stateStore", "store", "userStore", "metadata", "logger", "level", "WARN", "events", "addUserLoaded", "indexOf", "addSilentRenewError", "e", "addAccessTokenExpired", "result", "_user$profile$name", "_user$profile$email", "_user$profile$given_n", "_user$profile$family_", "_user$profile$display", "id", "sub", "userName", "profile", "name", "email", "firstName", "given_name", "lastName", "family_name", "displayName", "displayname", "language", "locale", "accessToken", "authProviderId", "idp", "strRoles", "role", "strPermissions", "permissions", "roles", "r", "p"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/core/auth/authService.js"], "sourcesContent": ["import { UserManager, WebStorageStateStore, Log } from \"oidc-client\";\r\nimport APP_CONFIG from \"../config/appConfig\";\r\nimport { IDENTITY_CLIENT_CONFIG } from \"../config/identityClientConfig\";\r\nimport { IDENTITY_META_CONFIG } from \"../config/identityMetaConfig\";\r\n\r\n/**\r\n * Authentication/Authorization service.\r\n * Corporate with Identity Server 4.\r\n *\r\n * Reference: https://medium.com/@franciscopa91/how-to-implement-oidc-authentication-with-react-context-api-and-react-router-205e13f2d49\r\n */\r\nexport default class AuthService {\r\n  userManager;\r\n\r\n  constructor() {\r\n    // Create state store without explicit prefix - let library handle it\r\n    const stateStore = new WebStorageStateStore({\r\n      store: window.sessionStorage\r\n    });\r\n\r\n    const userStore = new WebStorageStateStore({\r\n      store: window.sessionStorage\r\n    });\r\n\r\n    this.userManager = new UserManager({\r\n      ...IDENTITY_CLIENT_CONFIG,\r\n      userStore: userStore,\r\n      stateStore: stateStore,\r\n      metadata: {\r\n        ...IDENTITY_META_CONFIG,\r\n      },\r\n    });\r\n    //\r\n    // Register OIDC client Logger\r\n    // Exposed OIDC-Client library debugging information into console log.\r\n    //\r\n    Log.logger = console;\r\n    Log.level = Log.WARN; // Enable logging to help debug state issues\r\n\r\n    this.userManager.events.addUserLoaded((user) => {\r\n      if (window.location.href.indexOf(\"auth-callback\") !== -1) {\r\n        this.navigateToHome();\r\n      }\r\n    });\r\n\r\n    this.userManager.events.addSilentRenewError((e) => {\r\n      console.log(\"silent renew error\", e.message);\r\n      // If silent renewal fails, redirect to login\r\n      console.log(\"Silent renewal failed, redirecting to login\");\r\n      this.signinRedirect();\r\n    });\r\n\r\n    this.userManager.events.addAccessTokenExpired(() => {\r\n      console.log(\"token expired\");\r\n      this.signinSilent();\r\n    });\r\n\r\n    // Clear stale state on initialization\r\n    this.userManager.clearStaleState();\r\n  }\r\n\r\n  signinRedirectCallback = () => {\r\n    console.log(\"SigninRedirectCallback called\");\r\n    console.log(\"Current URL:\", window.location.href);\r\n\r\n    // Log all session storage keys for debugging\r\n    const sessionKeys = [];\r\n    for (let i = 0; i < sessionStorage.length; i++) {\r\n      const key = sessionStorage.key(i);\r\n      if (key && key.includes('oidc')) {\r\n        sessionKeys.push(key);\r\n      }\r\n    }\r\n    console.log(\"OIDC SessionStorage keys before callback:\", sessionKeys);\r\n\r\n    return this.userManager.signinRedirectCallback().then((user) => {\r\n      console.log(\"SigninRedirectCallback success:\", user);\r\n      const redirectUrl = localStorage.getItem(\"redirectUri\");\r\n\r\n      if (redirectUrl && redirectUrl.length > 0) {\r\n        window.location.replace(redirectUrl);\r\n      } else {\r\n        this.navigateToHome();\r\n      }\r\n      return user;\r\n    }).catch((error) => {\r\n      console.error(\"SigninRedirectCallback error:\", error);\r\n      console.error(\"Error details:\", error.message, error.stack);\r\n\r\n      // Log session storage state when error occurs\r\n      const sessionKeysOnError = [];\r\n      for (let i = 0; i < sessionStorage.length; i++) {\r\n        const key = sessionStorage.key(i);\r\n        if (key && key.includes('oidc')) {\r\n          sessionKeysOnError.push(key);\r\n        }\r\n      }\r\n      console.log(\"OIDC SessionStorage keys on error:\", sessionKeysOnError);\r\n\r\n      // Clear any stale state and redirect to home\r\n      this.userManager.clearStaleState();\r\n      localStorage.removeItem(\"redirectUri\");\r\n      throw error; // Re-throw to allow component to handle\r\n    });\r\n  };\r\n\r\n  /**\r\n   *  Get logon user object if system has. Otherwises, return null.\r\n   *  Note: Here only try to return user profile info. It does not require system login.\r\n   *  If no login user, then, return null.\r\n   *  Work for UI dispolay user profile information purpose.\r\n   *  For example, as for home page which is public page,\r\n   *  system also will try to publish logon user info on the menu\r\n   *  if there is any user login success.\r\n   *\r\n   *\r\n   * Return User object json format:\r\n   * {\r\n   *   id,\r\n   *   userName,\r\n   *   displayName,\r\n   *   firstName,\r\n   *   lastName,\r\n   *   email,\r\n   *   language,\r\n   *   roles,\r\n   *   permissions,\r\n   *   authProviderId\r\n   * }\r\n   *\r\n   */\r\n  getUser = () => {\r\n    const oidcStorage = JSON.parse(\r\n      /**\r\n       * Note: It is OIDC-Client library default storage.\r\n       * The key format is defined by oidc-client library.\r\n       * Note: Developer no needs to modify the session key.\r\n       * */\r\n      sessionStorage.getItem(\r\n        `oidc.user:${APP_CONFIG.iamDomain}:${APP_CONFIG.clientId}`\r\n      )\r\n    );\r\n\r\n    if (oidcStorage && oidcStorage.access_token && !oidcStorage.expired) {\r\n      const user = this.toAppUser(oidcStorage);\r\n      // console.log(\"getUser\", JSON.stringify(user));\r\n      return user;\r\n    } else {\r\n      return null;\r\n    }\r\n  };\r\n\r\n  /**\r\n   * Convert ID4 user to DTO user. Only called by getUser.\r\n   * @param user ID4 returned user claims, refer to OIDC-Client library.\r\n   */\r\n  toAppUser(user) {\r\n    const result = {};\r\n    if (user && !user.expired) {\r\n      // console.debug(\"toAppUser\", \"OIDC user: \" + JSON.stringify(user.profile));\r\n\r\n      result.id = user.sub;\r\n      result.userName = user.profile.name ?? \"\";\r\n      result.email = user.profile.email ?? \"\";\r\n      result.firstName = user.profile.given_name ?? \"\";\r\n      result.lastName = user.profile.family_name ?? \"\";\r\n      result.displayName = user.profile.displayname ?? \"\";\r\n      result.language = user.profile.locale;\r\n      result.accessToken = user.access_token;\r\n      result.authProviderId =\r\n        user.profile.idp === \"local\" ? \"APP\" : user.profile.idp; // Database driving login is \"local\" for Identity Server. Convert it to \"APP\".\r\n      const strRoles = user.profile.role;\r\n      const strPermissions = user.profile.permissions;\r\n\r\n      if (strRoles) {\r\n        const roles = [];\r\n        strRoles.split(\",\").forEach((r) => {\r\n          roles.push(r);\r\n        });\r\n        result.roles = roles;\r\n      }\r\n\r\n      if (strPermissions) {\r\n        const permissions = [];\r\n        strPermissions.split(\",\").forEach((p) => {\r\n          permissions.push(p);\r\n        });\r\n\r\n        result.permissions = permissions;\r\n      }\r\n    }\r\n\r\n    return result;\r\n  }\r\n\r\n  parseJwt = (token) => {\r\n    const base64Url = token.split(\".\")[1];\r\n    const base64 = base64Url.replace(\"-\", \"+\").replace(\"_\", \"/\");\r\n    return JSON.parse(window.atob(base64));\r\n  };\r\n\r\n  signin= ()=> {\r\n     this.signinRedirect();\r\n  }\r\n  \r\n  /** Redirect to Identity Server 4 login page for login requirement. */\r\n  signinRedirect = () => {\r\n    console.log(\"SigninRedirect called from:\", window.location.href);\r\n\r\n    // Clear any stale state before starting new authentication\r\n    this.userManager.clearStaleState();\r\n    localStorage.setItem(\"redirectUri\", window.location.pathname);\r\n\r\n    console.log(\"Redirect URI set to:\", window.location.pathname);\r\n    console.log(\"Starting signin redirect...\");\r\n\r\n    return this.userManager.signinRedirect({}).then(() => {\r\n      console.log(\"SigninRedirect initiated successfully\");\r\n    }).catch((error) => {\r\n      console.error(\"SigninRedirect error:\", error);\r\n      throw error;\r\n    });\r\n  };\r\n\r\n  /** Redirect to Home page which is public page. */\r\n  navigateToHome = () => {\r\n    window.location.replace(APP_CONFIG.basePath);\r\n  };\r\n\r\n  isAuthenticated = () => {\r\n    const oidcStorage = JSON.parse(\r\n      /**\r\n       * Note: It is OIDC-Client library default storage.\r\n       * The key format is defined by oidc-client library.\r\n       * Note: Developer no needs to modify the session key.\r\n       * */\r\n      sessionStorage.getItem(\r\n        `oidc.user:${APP_CONFIG.iamDomain}:${APP_CONFIG.clientId}`\r\n      )\r\n    );\r\n    return !!oidcStorage && !!oidcStorage.access_token && !oidcStorage.expired;\r\n  };\r\n\r\n  signinSilent = () => {\r\n    console.log(\"signinSilent called\");\r\n    this.userManager\r\n      .signinSilent()\r\n      .then((user) => {\r\n        console.log(\"silent signed in success\", user);\r\n      })\r\n      .catch((err) => {\r\n        console.log(\"silent signed in error\", err);\r\n        // If silent signin fails, redirect to login\r\n        console.log(\"Silent signin failed, redirecting to login\");\r\n        this.signinRedirect();\r\n      });\r\n  };\r\n\r\n  signinSilentCallback = () => {\r\n    console.log(\"SigninSilentCallback called\");\r\n    this.userManager.signinSilentCallback();\r\n  };\r\n\r\n  createSigninRequest = () => {\r\n    return this.userManager.createSigninRequest();\r\n  };\r\n\r\n  signout = () => {\r\n    this.userManager.signoutRedirect({\r\n      id_token_hint: localStorage.getItem(\"id_token\"),\r\n    });\r\n    this.userManager.clearStaleState();\r\n  };\r\n\r\n  signoutRedirectCallback = () => {\r\n    this.userManager.signoutRedirectCallback().then(() => {\r\n      localStorage.clear();\r\n      // Go to home page.\r\n      window.location.replace(\"/\");\r\n    });\r\n    this.userManager.clearStaleState();\r\n  };\r\n\r\n  /** Clear stale authentication state */\r\n  clearAuthState = () => {\r\n    this.userManager.clearStaleState();\r\n    localStorage.removeItem(\"redirectUri\");\r\n\r\n    // Clear all OIDC-related storage\r\n    const keysToRemove = [];\r\n    for (let i = 0; i < sessionStorage.length; i++) {\r\n      const key = sessionStorage.key(i);\r\n      if (key && key.includes('oidc')) {\r\n        keysToRemove.push(key);\r\n      }\r\n    }\r\n    keysToRemove.forEach(key => sessionStorage.removeItem(key));\r\n\r\n    // Also clear localStorage OIDC keys\r\n    const localKeysToRemove = [];\r\n    for (let i = 0; i < localStorage.length; i++) {\r\n      const key = localStorage.key(i);\r\n      if (key && key.includes('oidc')) {\r\n        localKeysToRemove.push(key);\r\n      }\r\n    }\r\n    localKeysToRemove.forEach(key => localStorage.removeItem(key));\r\n  };\r\n\r\n  /** Debug method to log current authentication state */\r\n  debugAuthState = () => {\r\n    console.log(\"=== Authentication Debug Info ===\");\r\n    console.log(\"APP_CONFIG:\", APP_CONFIG);\r\n    console.log(\"Current URL:\", window.location.href);\r\n    console.log(\"Redirect URI:\", localStorage.getItem(\"redirectUri\"));\r\n\r\n    // Parse URL parameters\r\n    const urlParams = new URLSearchParams(window.location.search);\r\n    console.log(\"URL Parameters:\", Object.fromEntries(urlParams));\r\n\r\n    // Check sessionStorage for OIDC state\r\n    const stateKeys = [];\r\n    for (let i = 0; i < sessionStorage.length; i++) {\r\n      const key = sessionStorage.key(i);\r\n      if (key && key.includes('oidc')) {\r\n        stateKeys.push({\r\n          key: key,\r\n          value: sessionStorage.getItem(key)\r\n        });\r\n      }\r\n    }\r\n    console.log(\"OIDC SessionStorage entries:\", stateKeys);\r\n\r\n    // Check if user is authenticated\r\n    console.log(\"Is Authenticated:\", this.isAuthenticated());\r\n    console.log(\"Current User:\", this.getUser());\r\n    console.log(\"=== End Debug Info ===\");\r\n  };\r\n\r\n  /** Manual callback handler for debugging */\r\n  manualCallbackHandler = () => {\r\n    const urlParams = new URLSearchParams(window.location.search);\r\n    const code = urlParams.get('code');\r\n    const state = urlParams.get('state');\r\n    const sessionState = urlParams.get('session_state');\r\n\r\n    console.log(\"Manual callback handler:\");\r\n    console.log(\"Code:\", code);\r\n    console.log(\"State:\", state);\r\n    console.log(\"Session State:\", sessionState);\r\n\r\n    // Check different possible state key formats\r\n    const possibleStateKeys = [\r\n      `oidc.state:${state}`,\r\n      `oidc.${state}`,\r\n      state,\r\n      `state:${state}`\r\n    ];\r\n\r\n    let foundState = null;\r\n    for (const stateKey of possibleStateKeys) {\r\n      const storedState = sessionStorage.getItem(stateKey);\r\n      console.log(`Checking state key: ${stateKey} -> ${storedState ? 'FOUND' : 'NOT FOUND'}`);\r\n      if (storedState) {\r\n        foundState = { key: stateKey, value: storedState };\r\n        break;\r\n      }\r\n    }\r\n\r\n    if (!foundState) {\r\n      console.error(\"No matching state found for:\", state);\r\n      // List all state-related keys\r\n      const allStateKeys = [];\r\n      for (let i = 0; i < sessionStorage.length; i++) {\r\n        const key = sessionStorage.key(i);\r\n        if (key && (key.includes('oidc') || key.includes('state'))) {\r\n          allStateKeys.push({\r\n            key: key,\r\n            value: sessionStorage.getItem(key)\r\n          });\r\n        }\r\n      }\r\n      console.log(\"All OIDC/state keys:\", allStateKeys);\r\n    } else {\r\n      console.log(\"Found matching state:\", foundState);\r\n    }\r\n  };\r\n}\r\n"], "mappings": "AAAA,SAASA,WAAW,EAAEC,oBAAoB,EAAEC,GAAG,QAAQ,aAAa;AACpE,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,SAASC,sBAAsB,QAAQ,gCAAgC;AACvE,SAASC,oBAAoB,QAAQ,8BAA8B;;AAEnE;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,MAAMC,WAAW,CAAC;EAG/BC,WAAWA,CAAA,EAAG;IAAA,KA+CdC,sBAAsB,GAAG,MAAM;MAC7BC,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAC5CD,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEC,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAC;;MAEjD;MACA,MAAMC,WAAW,GAAG,EAAE;MACtB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,cAAc,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;QAC9C,MAAMG,GAAG,GAAGF,cAAc,CAACE,GAAG,CAACH,CAAC,CAAC;QACjC,IAAIG,GAAG,IAAIA,GAAG,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAE;UAC/BL,WAAW,CAACM,IAAI,CAACF,GAAG,CAAC;QACvB;MACF;MACAT,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAEI,WAAW,CAAC;MAErE,OAAO,IAAI,CAACO,WAAW,CAACb,sBAAsB,CAAC,CAAC,CAACc,IAAI,CAAEC,IAAI,IAAK;QAC9Dd,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEa,IAAI,CAAC;QACpD,MAAMC,WAAW,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;QAEvD,IAAIF,WAAW,IAAIA,WAAW,CAACP,MAAM,GAAG,CAAC,EAAE;UACzCN,MAAM,CAACC,QAAQ,CAACe,OAAO,CAACH,WAAW,CAAC;QACtC,CAAC,MAAM;UACL,IAAI,CAACI,cAAc,CAAC,CAAC;QACvB;QACA,OAAOL,IAAI;MACb,CAAC,CAAC,CAACM,KAAK,CAAEC,KAAK,IAAK;QAClBrB,OAAO,CAACqB,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrDrB,OAAO,CAACqB,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAACC,OAAO,EAAED,KAAK,CAACE,KAAK,CAAC;;QAE3D;QACA,MAAMC,kBAAkB,GAAG,EAAE;QAC7B,KAAK,IAAIlB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,cAAc,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;UAC9C,MAAMG,GAAG,GAAGF,cAAc,CAACE,GAAG,CAACH,CAAC,CAAC;UACjC,IAAIG,GAAG,IAAIA,GAAG,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAE;YAC/Bc,kBAAkB,CAACb,IAAI,CAACF,GAAG,CAAC;UAC9B;QACF;QACAT,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEuB,kBAAkB,CAAC;;QAErE;QACA,IAAI,CAACZ,WAAW,CAACa,eAAe,CAAC,CAAC;QAClCT,YAAY,CAACU,UAAU,CAAC,aAAa,CAAC;QACtC,MAAML,KAAK,CAAC,CAAC;MACf,CAAC,CAAC;IACJ,CAAC;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAxBE,KAyBAM,OAAO,GAAG,MAAM;MACd,MAAMC,WAAW,GAAGC,IAAI,CAACC,KAAK;MAC5B;AACN;AACA;AACA;AACA;MACMvB,cAAc,CAACU,OAAO,CACpB,aAAavB,UAAU,CAACqC,SAAS,IAAIrC,UAAU,CAACsC,QAAQ,EAC1D,CACF,CAAC;MAED,IAAIJ,WAAW,IAAIA,WAAW,CAACK,YAAY,IAAI,CAACL,WAAW,CAACM,OAAO,EAAE;QACnE,MAAMpB,IAAI,GAAG,IAAI,CAACqB,SAAS,CAACP,WAAW,CAAC;QACxC;QACA,OAAOd,IAAI;MACb,CAAC,MAAM;QACL,OAAO,IAAI;MACb;IACF,CAAC;IAAA,KA6CDsB,QAAQ,GAAIC,KAAK,IAAK;MACpB,MAAMC,SAAS,GAAGD,KAAK,CAACE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACrC,MAAMC,MAAM,GAAGF,SAAS,CAACpB,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;MAC5D,OAAOW,IAAI,CAACC,KAAK,CAAC5B,MAAM,CAACuC,IAAI,CAACD,MAAM,CAAC,CAAC;IACxC,CAAC;IAAA,KAEDE,MAAM,GAAE,MAAK;MACV,IAAI,CAACC,cAAc,CAAC,CAAC;IACxB,CAAC;IAED;IAAA,KACAA,cAAc,GAAG,MAAM;MACrB3C,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEC,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAC;;MAEhE;MACA,IAAI,CAACQ,WAAW,CAACa,eAAe,CAAC,CAAC;MAClCT,YAAY,CAAC4B,OAAO,CAAC,aAAa,EAAE1C,MAAM,CAACC,QAAQ,CAAC0C,QAAQ,CAAC;MAE7D7C,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEC,MAAM,CAACC,QAAQ,CAAC0C,QAAQ,CAAC;MAC7D7C,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;MAE1C,OAAO,IAAI,CAACW,WAAW,CAAC+B,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC9B,IAAI,CAAC,MAAM;QACpDb,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;MACtD,CAAC,CAAC,CAACmB,KAAK,CAAEC,KAAK,IAAK;QAClBrB,OAAO,CAACqB,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7C,MAAMA,KAAK;MACb,CAAC,CAAC;IACJ,CAAC;IAED;IAAA,KACAF,cAAc,GAAG,MAAM;MACrBjB,MAAM,CAACC,QAAQ,CAACe,OAAO,CAACxB,UAAU,CAACoD,QAAQ,CAAC;IAC9C,CAAC;IAAA,KAEDC,eAAe,GAAG,MAAM;MACtB,MAAMnB,WAAW,GAAGC,IAAI,CAACC,KAAK;MAC5B;AACN;AACA;AACA;AACA;MACMvB,cAAc,CAACU,OAAO,CACpB,aAAavB,UAAU,CAACqC,SAAS,IAAIrC,UAAU,CAACsC,QAAQ,EAC1D,CACF,CAAC;MACD,OAAO,CAAC,CAACJ,WAAW,IAAI,CAAC,CAACA,WAAW,CAACK,YAAY,IAAI,CAACL,WAAW,CAACM,OAAO;IAC5E,CAAC;IAAA,KAEDc,YAAY,GAAG,MAAM;MACnBhD,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;MAClC,IAAI,CAACW,WAAW,CACboC,YAAY,CAAC,CAAC,CACdnC,IAAI,CAAEC,IAAI,IAAK;QACdd,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEa,IAAI,CAAC;MAC/C,CAAC,CAAC,CACDM,KAAK,CAAE6B,GAAG,IAAK;QACdjD,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEgD,GAAG,CAAC;QAC1C;QACAjD,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;QACzD,IAAI,CAAC0C,cAAc,CAAC,CAAC;MACvB,CAAC,CAAC;IACN,CAAC;IAAA,KAEDO,oBAAoB,GAAG,MAAM;MAC3BlD,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;MAC1C,IAAI,CAACW,WAAW,CAACsC,oBAAoB,CAAC,CAAC;IACzC,CAAC;IAAA,KAEDC,mBAAmB,GAAG,MAAM;MAC1B,OAAO,IAAI,CAACvC,WAAW,CAACuC,mBAAmB,CAAC,CAAC;IAC/C,CAAC;IAAA,KAEDC,OAAO,GAAG,MAAM;MACd,IAAI,CAACxC,WAAW,CAACyC,eAAe,CAAC;QAC/BC,aAAa,EAAEtC,YAAY,CAACC,OAAO,CAAC,UAAU;MAChD,CAAC,CAAC;MACF,IAAI,CAACL,WAAW,CAACa,eAAe,CAAC,CAAC;IACpC,CAAC;IAAA,KAED8B,uBAAuB,GAAG,MAAM;MAC9B,IAAI,CAAC3C,WAAW,CAAC2C,uBAAuB,CAAC,CAAC,CAAC1C,IAAI,CAAC,MAAM;QACpDG,YAAY,CAACwC,KAAK,CAAC,CAAC;QACpB;QACAtD,MAAM,CAACC,QAAQ,CAACe,OAAO,CAAC,GAAG,CAAC;MAC9B,CAAC,CAAC;MACF,IAAI,CAACN,WAAW,CAACa,eAAe,CAAC,CAAC;IACpC,CAAC;IAED;IAAA,KACAgC,cAAc,GAAG,MAAM;MACrB,IAAI,CAAC7C,WAAW,CAACa,eAAe,CAAC,CAAC;MAClCT,YAAY,CAACU,UAAU,CAAC,aAAa,CAAC;;MAEtC;MACA,MAAMgC,YAAY,GAAG,EAAE;MACvB,KAAK,IAAIpD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,cAAc,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;QAC9C,MAAMG,GAAG,GAAGF,cAAc,CAACE,GAAG,CAACH,CAAC,CAAC;QACjC,IAAIG,GAAG,IAAIA,GAAG,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAE;UAC/BgD,YAAY,CAAC/C,IAAI,CAACF,GAAG,CAAC;QACxB;MACF;MACAiD,YAAY,CAACC,OAAO,CAAClD,GAAG,IAAIF,cAAc,CAACmB,UAAU,CAACjB,GAAG,CAAC,CAAC;;MAE3D;MACA,MAAMmD,iBAAiB,GAAG,EAAE;MAC5B,KAAK,IAAItD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGU,YAAY,CAACR,MAAM,EAAEF,CAAC,EAAE,EAAE;QAC5C,MAAMG,GAAG,GAAGO,YAAY,CAACP,GAAG,CAACH,CAAC,CAAC;QAC/B,IAAIG,GAAG,IAAIA,GAAG,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAE;UAC/BkD,iBAAiB,CAACjD,IAAI,CAACF,GAAG,CAAC;QAC7B;MACF;MACAmD,iBAAiB,CAACD,OAAO,CAAClD,GAAG,IAAIO,YAAY,CAACU,UAAU,CAACjB,GAAG,CAAC,CAAC;IAChE,CAAC;IAED;IAAA,KACAoD,cAAc,GAAG,MAAM;MACrB7D,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;MAChDD,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEP,UAAU,CAAC;MACtCM,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEC,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAC;MACjDJ,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEe,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC,CAAC;;MAEjE;MACA,MAAM6C,SAAS,GAAG,IAAIC,eAAe,CAAC7D,MAAM,CAACC,QAAQ,CAAC6D,MAAM,CAAC;MAC7DhE,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEgE,MAAM,CAACC,WAAW,CAACJ,SAAS,CAAC,CAAC;;MAE7D;MACA,MAAMK,SAAS,GAAG,EAAE;MACpB,KAAK,IAAI7D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,cAAc,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;QAC9C,MAAMG,GAAG,GAAGF,cAAc,CAACE,GAAG,CAACH,CAAC,CAAC;QACjC,IAAIG,GAAG,IAAIA,GAAG,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAE;UAC/ByD,SAAS,CAACxD,IAAI,CAAC;YACbF,GAAG,EAAEA,GAAG;YACR2D,KAAK,EAAE7D,cAAc,CAACU,OAAO,CAACR,GAAG;UACnC,CAAC,CAAC;QACJ;MACF;MACAT,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEkE,SAAS,CAAC;;MAEtD;MACAnE,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAAC8C,eAAe,CAAC,CAAC,CAAC;MACxD/C,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC0B,OAAO,CAAC,CAAC,CAAC;MAC5C3B,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;IACvC,CAAC;IAED;IAAA,KACAoE,qBAAqB,GAAG,MAAM;MAC5B,MAAMP,SAAS,GAAG,IAAIC,eAAe,CAAC7D,MAAM,CAACC,QAAQ,CAAC6D,MAAM,CAAC;MAC7D,MAAMM,IAAI,GAAGR,SAAS,CAACS,GAAG,CAAC,MAAM,CAAC;MAClC,MAAMC,KAAK,GAAGV,SAAS,CAACS,GAAG,CAAC,OAAO,CAAC;MACpC,MAAME,YAAY,GAAGX,SAAS,CAACS,GAAG,CAAC,eAAe,CAAC;MAEnDvE,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MACvCD,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEqE,IAAI,CAAC;MAC1BtE,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEuE,KAAK,CAAC;MAC5BxE,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEwE,YAAY,CAAC;;MAE3C;MACA,MAAMC,iBAAiB,GAAG,CACxB,cAAcF,KAAK,EAAE,EACrB,QAAQA,KAAK,EAAE,EACfA,KAAK,EACL,SAASA,KAAK,EAAE,CACjB;MAED,IAAIG,UAAU,GAAG,IAAI;MACrB,KAAK,MAAMC,QAAQ,IAAIF,iBAAiB,EAAE;QACxC,MAAMG,WAAW,GAAGtE,cAAc,CAACU,OAAO,CAAC2D,QAAQ,CAAC;QACpD5E,OAAO,CAACC,GAAG,CAAC,uBAAuB2E,QAAQ,OAAOC,WAAW,GAAG,OAAO,GAAG,WAAW,EAAE,CAAC;QACxF,IAAIA,WAAW,EAAE;UACfF,UAAU,GAAG;YAAElE,GAAG,EAAEmE,QAAQ;YAAER,KAAK,EAAES;UAAY,CAAC;UAClD;QACF;MACF;MAEA,IAAI,CAACF,UAAU,EAAE;QACf3E,OAAO,CAACqB,KAAK,CAAC,8BAA8B,EAAEmD,KAAK,CAAC;QACpD;QACA,MAAMM,YAAY,GAAG,EAAE;QACvB,KAAK,IAAIxE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,cAAc,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;UAC9C,MAAMG,GAAG,GAAGF,cAAc,CAACE,GAAG,CAACH,CAAC,CAAC;UACjC,IAAIG,GAAG,KAAKA,GAAG,CAACC,QAAQ,CAAC,MAAM,CAAC,IAAID,GAAG,CAACC,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE;YAC1DoE,YAAY,CAACnE,IAAI,CAAC;cAChBF,GAAG,EAAEA,GAAG;cACR2D,KAAK,EAAE7D,cAAc,CAACU,OAAO,CAACR,GAAG;YACnC,CAAC,CAAC;UACJ;QACF;QACAT,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE6E,YAAY,CAAC;MACnD,CAAC,MAAM;QACL9E,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE0E,UAAU,CAAC;MAClD;IACF,CAAC;IAnXC;IACA,MAAMI,UAAU,GAAG,IAAIvF,oBAAoB,CAAC;MAC1CwF,KAAK,EAAE9E,MAAM,CAACK;IAChB,CAAC,CAAC;IAEF,MAAM0E,SAAS,GAAG,IAAIzF,oBAAoB,CAAC;MACzCwF,KAAK,EAAE9E,MAAM,CAACK;IAChB,CAAC,CAAC;IAEF,IAAI,CAACK,WAAW,GAAG,IAAIrB,WAAW,CAAC;MACjC,GAAGI,sBAAsB;MACzBsF,SAAS,EAAEA,SAAS;MACpBF,UAAU,EAAEA,UAAU;MACtBG,QAAQ,EAAE;QACR,GAAGtF;MACL;IACF,CAAC,CAAC;IACF;IACA;IACA;IACA;IACAH,GAAG,CAAC0F,MAAM,GAAGnF,OAAO;IACpBP,GAAG,CAAC2F,KAAK,GAAG3F,GAAG,CAAC4F,IAAI,CAAC,CAAC;;IAEtB,IAAI,CAACzE,WAAW,CAAC0E,MAAM,CAACC,aAAa,CAAEzE,IAAI,IAAK;MAC9C,IAAIZ,MAAM,CAACC,QAAQ,CAACC,IAAI,CAACoF,OAAO,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE;QACxD,IAAI,CAACrE,cAAc,CAAC,CAAC;MACvB;IACF,CAAC,CAAC;IAEF,IAAI,CAACP,WAAW,CAAC0E,MAAM,CAACG,mBAAmB,CAAEC,CAAC,IAAK;MACjD1F,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEyF,CAAC,CAACpE,OAAO,CAAC;MAC5C;MACAtB,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;MAC1D,IAAI,CAAC0C,cAAc,CAAC,CAAC;IACvB,CAAC,CAAC;IAEF,IAAI,CAAC/B,WAAW,CAAC0E,MAAM,CAACK,qBAAqB,CAAC,MAAM;MAClD3F,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;MAC5B,IAAI,CAAC+C,YAAY,CAAC,CAAC;IACrB,CAAC,CAAC;;IAEF;IACA,IAAI,CAACpC,WAAW,CAACa,eAAe,CAAC,CAAC;EACpC;EA6FA;AACF;AACA;AACA;EACEU,SAASA,CAACrB,IAAI,EAAE;IACd,MAAM8E,MAAM,GAAG,CAAC,CAAC;IACjB,IAAI9E,IAAI,IAAI,CAACA,IAAI,CAACoB,OAAO,EAAE;MAAA,IAAA2D,kBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;MACzB;;MAEAL,MAAM,CAACM,EAAE,GAAGpF,IAAI,CAACqF,GAAG;MACpBP,MAAM,CAACQ,QAAQ,IAAAP,kBAAA,GAAG/E,IAAI,CAACuF,OAAO,CAACC,IAAI,cAAAT,kBAAA,cAAAA,kBAAA,GAAI,EAAE;MACzCD,MAAM,CAACW,KAAK,IAAAT,mBAAA,GAAGhF,IAAI,CAACuF,OAAO,CAACE,KAAK,cAAAT,mBAAA,cAAAA,mBAAA,GAAI,EAAE;MACvCF,MAAM,CAACY,SAAS,IAAAT,qBAAA,GAAGjF,IAAI,CAACuF,OAAO,CAACI,UAAU,cAAAV,qBAAA,cAAAA,qBAAA,GAAI,EAAE;MAChDH,MAAM,CAACc,QAAQ,IAAAV,qBAAA,GAAGlF,IAAI,CAACuF,OAAO,CAACM,WAAW,cAAAX,qBAAA,cAAAA,qBAAA,GAAI,EAAE;MAChDJ,MAAM,CAACgB,WAAW,IAAAX,qBAAA,GAAGnF,IAAI,CAACuF,OAAO,CAACQ,WAAW,cAAAZ,qBAAA,cAAAA,qBAAA,GAAI,EAAE;MACnDL,MAAM,CAACkB,QAAQ,GAAGhG,IAAI,CAACuF,OAAO,CAACU,MAAM;MACrCnB,MAAM,CAACoB,WAAW,GAAGlG,IAAI,CAACmB,YAAY;MACtC2D,MAAM,CAACqB,cAAc,GACnBnG,IAAI,CAACuF,OAAO,CAACa,GAAG,KAAK,OAAO,GAAG,KAAK,GAAGpG,IAAI,CAACuF,OAAO,CAACa,GAAG,CAAC,CAAC;MAC3D,MAAMC,QAAQ,GAAGrG,IAAI,CAACuF,OAAO,CAACe,IAAI;MAClC,MAAMC,cAAc,GAAGvG,IAAI,CAACuF,OAAO,CAACiB,WAAW;MAE/C,IAAIH,QAAQ,EAAE;QACZ,MAAMI,KAAK,GAAG,EAAE;QAChBJ,QAAQ,CAAC5E,KAAK,CAAC,GAAG,CAAC,CAACoB,OAAO,CAAE6D,CAAC,IAAK;UACjCD,KAAK,CAAC5G,IAAI,CAAC6G,CAAC,CAAC;QACf,CAAC,CAAC;QACF5B,MAAM,CAAC2B,KAAK,GAAGA,KAAK;MACtB;MAEA,IAAIF,cAAc,EAAE;QAClB,MAAMC,WAAW,GAAG,EAAE;QACtBD,cAAc,CAAC9E,KAAK,CAAC,GAAG,CAAC,CAACoB,OAAO,CAAE8D,CAAC,IAAK;UACvCH,WAAW,CAAC3G,IAAI,CAAC8G,CAAC,CAAC;QACrB,CAAC,CAAC;QAEF7B,MAAM,CAAC0B,WAAW,GAAGA,WAAW;MAClC;IACF;IAEA,OAAO1B,MAAM;EACf;AAkMF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}