{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nexport function throttle(durationSelector, config) {\n  return operate(function (source, subscriber) {\n    var _a = config !== null && config !== void 0 ? config : {},\n      _b = _a.leading,\n      leading = _b === void 0 ? true : _b,\n      _c = _a.trailing,\n      trailing = _c === void 0 ? false : _c;\n    var hasValue = false;\n    var sendValue = null;\n    var throttled = null;\n    var isComplete = false;\n    var endThrottling = function () {\n      throttled === null || throttled === void 0 ? void 0 : throttled.unsubscribe();\n      throttled = null;\n      if (trailing) {\n        send();\n        isComplete && subscriber.complete();\n      }\n    };\n    var cleanupThrottling = function () {\n      throttled = null;\n      isComplete && subscriber.complete();\n    };\n    var startThrottle = function (value) {\n      return throttled = innerFrom(durationSelector(value)).subscribe(createOperatorSubscriber(subscriber, endThrottling, cleanupThrottling));\n    };\n    var send = function () {\n      if (hasValue) {\n        hasValue = false;\n        var value = sendValue;\n        sendValue = null;\n        subscriber.next(value);\n        !isComplete && startThrottle(value);\n      }\n    };\n    source.subscribe(createOperatorSubscriber(subscriber, function (value) {\n      hasValue = true;\n      sendValue = value;\n      !(throttled && !throttled.closed) && (leading ? send() : startThrottle(value));\n    }, function () {\n      isComplete = true;\n      !(trailing && hasValue && throttled && !throttled.closed) && subscriber.complete();\n    }));\n  });\n}", "map": {"version": 3, "names": ["operate", "createOperatorSubscriber", "innerFrom", "throttle", "durationSelector", "config", "source", "subscriber", "_a", "_b", "leading", "_c", "trailing", "hasValue", "sendValue", "throttled", "isComplete", "endThrottling", "unsubscribe", "send", "complete", "cleanupThrottling", "startThrottle", "value", "subscribe", "next", "closed"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\throttle.ts"], "sourcesContent": ["import { Subscription } from '../Subscription';\n\nimport { MonoTypeOperatorFunction, ObservableInput } from '../types';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\n\n/**\n * An object interface used by {@link throttle} or {@link throttleTime} that ensure\n * configuration options of these operators.\n *\n * @see {@link throttle}\n * @see {@link throttleTime}\n */\nexport interface ThrottleConfig {\n  /**\n   * If `true`, the resulting Observable will emit the first value from the source\n   * Observable at the **start** of the \"throttling\" process (when starting an\n   * internal timer that prevents other emissions from the source to pass through).\n   * If `false`, it will not emit the first value from the source Observable at the\n   * start of the \"throttling\" process.\n   *\n   * If not provided, defaults to: `true`.\n   */\n  leading?: boolean;\n  /**\n   * If `true`, the resulting Observable will emit the last value from the source\n   * Observable at the **end** of the \"throttling\" process (when ending an internal\n   * timer that prevents other emissions from the source to pass through).\n   * If `false`, it will not emit the last value from the source Observable at the\n   * end of the \"throttling\" process.\n   *\n   * If not provided, defaults to: `false`.\n   */\n  trailing?: boolean;\n}\n\n/**\n * Emits a value from the source Observable, then ignores subsequent source\n * values for a duration determined by another Observable, then repeats this\n * process.\n *\n * <span class=\"informal\">It's like {@link throttleTime}, but the silencing\n * duration is determined by a second Observable.</span>\n *\n * ![](throttle.svg)\n *\n * `throttle` emits the source Observable values on the output Observable\n * when its internal timer is disabled, and ignores source values when the timer\n * is enabled. Initially, the timer is disabled. As soon as the first source\n * value arrives, it is forwarded to the output Observable, and then the timer\n * is enabled by calling the `durationSelector` function with the source value,\n * which returns the \"duration\" Observable. When the duration Observable emits a\n * value, the timer is disabled, and this process repeats for the\n * next source value.\n *\n * ## Example\n *\n * Emit clicks at a rate of at most one click per second\n *\n * ```ts\n * import { fromEvent, throttle, interval } from 'rxjs';\n *\n * const clicks = fromEvent(document, 'click');\n * const result = clicks.pipe(throttle(() => interval(1000)));\n *\n * result.subscribe(x => console.log(x));\n * ```\n *\n * @see {@link audit}\n * @see {@link debounce}\n * @see {@link delayWhen}\n * @see {@link sample}\n * @see {@link throttleTime}\n *\n * @param durationSelector A function that receives a value from the source\n * Observable, for computing the silencing duration for each source value,\n * returned as an `ObservableInput`.\n * @param config A configuration object to define `leading` and `trailing`\n * behavior. Defaults to `{ leading: true, trailing: false }`.\n * @return A function that returns an Observable that performs the throttle\n * operation to limit the rate of emissions from the source.\n */\nexport function throttle<T>(durationSelector: (value: T) => ObservableInput<any>, config?: ThrottleConfig): MonoTypeOperatorFunction<T> {\n  return operate((source, subscriber) => {\n    const { leading = true, trailing = false } = config ?? {};\n    let hasValue = false;\n    let sendValue: T | null = null;\n    let throttled: Subscription | null = null;\n    let isComplete = false;\n\n    const endThrottling = () => {\n      throttled?.unsubscribe();\n      throttled = null;\n      if (trailing) {\n        send();\n        isComplete && subscriber.complete();\n      }\n    };\n\n    const cleanupThrottling = () => {\n      throttled = null;\n      isComplete && subscriber.complete();\n    };\n\n    const startThrottle = (value: T) =>\n      (throttled = innerFrom(durationSelector(value)).subscribe(createOperatorSubscriber(subscriber, endThrottling, cleanupThrottling)));\n\n    const send = () => {\n      if (hasValue) {\n        // Ensure we clear out our value and hasValue flag\n        // before we emit, otherwise reentrant code can cause\n        // issues here.\n        hasValue = false;\n        const value = sendValue!;\n        sendValue = null;\n        // Emit the value.\n        subscriber.next(value);\n        !isComplete && startThrottle(value);\n      }\n    };\n\n    source.subscribe(\n      createOperatorSubscriber(\n        subscriber,\n        // Regarding the presence of throttled.closed in the following\n        // conditions, if a synchronous duration selector is specified - weird,\n        // but legal - an already-closed subscription will be assigned to\n        // throttled, so the subscription's closed property needs to be checked,\n        // too.\n        (value) => {\n          hasValue = true;\n          sendValue = value;\n          !(throttled && !throttled.closed) && (leading ? send() : startThrottle(value));\n        },\n        () => {\n          isComplete = true;\n          !(trailing && hasValue && throttled && !throttled.closed) && subscriber.complete();\n        }\n      )\n    );\n  });\n}\n"], "mappings": "AAGA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,SAASC,SAAS,QAAQ,yBAAyB;AA8EnD,OAAM,SAAUC,QAAQA,CAAIC,gBAAoD,EAAEC,MAAuB;EACvG,OAAOL,OAAO,CAAC,UAACM,MAAM,EAAEC,UAAU;IAC1B,IAAAC,EAAA,GAAuCH,MAAM,aAANA,MAAM,cAANA,MAAM,GAAI,EAAE;MAAjDI,EAAA,GAAAD,EAAA,CAAAE,OAAc;MAAdA,OAAO,GAAAD,EAAA,cAAG,IAAI,GAAAA,EAAA;MAAEE,EAAA,GAAAH,EAAA,CAAAI,QAAgB;MAAhBA,QAAQ,GAAAD,EAAA,cAAG,KAAK,GAAAA,EAAiB;IACzD,IAAIE,QAAQ,GAAG,KAAK;IACpB,IAAIC,SAAS,GAAa,IAAI;IAC9B,IAAIC,SAAS,GAAwB,IAAI;IACzC,IAAIC,UAAU,GAAG,KAAK;IAEtB,IAAMC,aAAa,GAAG,SAAAA,CAAA;MACpBF,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEG,WAAW,EAAE;MACxBH,SAAS,GAAG,IAAI;MAChB,IAAIH,QAAQ,EAAE;QACZO,IAAI,EAAE;QACNH,UAAU,IAAIT,UAAU,CAACa,QAAQ,EAAE;;IAEvC,CAAC;IAED,IAAMC,iBAAiB,GAAG,SAAAA,CAAA;MACxBN,SAAS,GAAG,IAAI;MAChBC,UAAU,IAAIT,UAAU,CAACa,QAAQ,EAAE;IACrC,CAAC;IAED,IAAME,aAAa,GAAG,SAAAA,CAACC,KAAQ;MAC7B,OAACR,SAAS,GAAGb,SAAS,CAACE,gBAAgB,CAACmB,KAAK,CAAC,CAAC,CAACC,SAAS,CAACvB,wBAAwB,CAACM,UAAU,EAAEU,aAAa,EAAEI,iBAAiB,CAAC,CAAC;IAAjI,CAAkI;IAEpI,IAAMF,IAAI,GAAG,SAAAA,CAAA;MACX,IAAIN,QAAQ,EAAE;QAIZA,QAAQ,GAAG,KAAK;QAChB,IAAMU,KAAK,GAAGT,SAAU;QACxBA,SAAS,GAAG,IAAI;QAEhBP,UAAU,CAACkB,IAAI,CAACF,KAAK,CAAC;QACtB,CAACP,UAAU,IAAIM,aAAa,CAACC,KAAK,CAAC;;IAEvC,CAAC;IAEDjB,MAAM,CAACkB,SAAS,CACdvB,wBAAwB,CACtBM,UAAU,EAMV,UAACgB,KAAK;MACJV,QAAQ,GAAG,IAAI;MACfC,SAAS,GAAGS,KAAK;MACjB,EAAER,SAAS,IAAI,CAACA,SAAS,CAACW,MAAM,CAAC,KAAKhB,OAAO,GAAGS,IAAI,EAAE,GAAGG,aAAa,CAACC,KAAK,CAAC,CAAC;IAChF,CAAC,EACD;MACEP,UAAU,GAAG,IAAI;MACjB,EAAEJ,QAAQ,IAAIC,QAAQ,IAAIE,SAAS,IAAI,CAACA,SAAS,CAACW,MAAM,CAAC,IAAInB,UAAU,CAACa,QAAQ,EAAE;IACpF,CAAC,CACF,CACF;EACH,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}