{"ast": null, "code": "export {};", "map": {"version": 3, "names": [], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\types.ts"], "sourcesContent": ["// https://github.com/microsoft/TypeScript/issues/40462#issuecomment-689879308\n/// <reference lib=\"esnext.asynciterable\" />\n\nimport { Observable } from './Observable';\nimport { Subscription } from './Subscription';\n\n/**\n * Note: This will add Symbol.observable globally for all TypeScript users,\n * however, we are no longer polyfilling Symbol.observable\n */\ndeclare global {\n  interface SymbolConstructor {\n    readonly observable: symbol;\n  }\n}\n\n/* OPERATOR INTERFACES */\n\n/**\n * A function type interface that describes a function that accepts one parameter `T`\n * and returns another parameter `R`.\n *\n * Usually used to describe {@link OperatorFunction} - it always takes a single\n * parameter (the source Observable) and returns another Observable.\n */\nexport interface UnaryFunction<T, R> {\n  (source: T): R;\n}\n\nexport interface OperatorFunction<T, R> extends UnaryFunction<Observable<T>, Observable<R>> {}\n\nexport type FactoryOrValue<T> = T | (() => T);\n\n/**\n * A function type interface that describes a function that accepts and returns a parameter of the same type.\n *\n * Used to describe {@link OperatorFunction} with the only one type: `OperatorFunction<T, T>`.\n *\n */\nexport interface MonoTypeOperatorFunction<T> extends OperatorFunction<T, T> {}\n\n/**\n * A value and the time at which it was emitted.\n *\n * Emitted by the `timestamp` operator\n *\n * @see {@link timestamp}\n */\nexport interface Timestamp<T> {\n  value: T;\n  /**\n   * The timestamp. By default, this is in epoch milliseconds.\n   * Could vary based on the timestamp provider passed to the operator.\n   */\n  timestamp: number;\n}\n\n/**\n * A value emitted and the amount of time since the last value was emitted.\n *\n * Emitted by the `timeInterval` operator.\n *\n * @see {@link timeInterval}\n */\nexport interface TimeInterval<T> {\n  value: T;\n\n  /**\n   * The amount of time between this value's emission and the previous value's emission.\n   * If this is the first emitted value, then it will be the amount of time since subscription\n   * started.\n   */\n  interval: number;\n}\n\n/* SUBSCRIPTION INTERFACES */\n\nexport interface Unsubscribable {\n  unsubscribe(): void;\n}\n\nexport type TeardownLogic = Subscription | Unsubscribable | (() => void) | void;\n\nexport interface SubscriptionLike extends Unsubscribable {\n  unsubscribe(): void;\n  readonly closed: boolean;\n}\n\n/**\n * @deprecated Do not use. Most likely you want to use `ObservableInput`. Will be removed in v8.\n */\nexport type SubscribableOrPromise<T> = Subscribable<T> | Subscribable<never> | PromiseLike<T> | InteropObservable<T>;\n\n/** OBSERVABLE INTERFACES */\n\nexport interface Subscribable<T> {\n  subscribe(observer: Partial<Observer<T>>): Unsubscribable;\n}\n\n/**\n * Valid types that can be converted to observables.\n */\nexport type ObservableInput<T> =\n  | Observable<T>\n  | InteropObservable<T>\n  | AsyncIterable<T>\n  | PromiseLike<T>\n  | ArrayLike<T>\n  | Iterable<T>\n  | ReadableStreamLike<T>;\n\n/**\n * @deprecated Renamed to {@link InteropObservable }. Will be removed in v8.\n */\nexport type ObservableLike<T> = InteropObservable<T>;\n\n/**\n * An object that implements the `Symbol.observable` interface.\n */\nexport interface InteropObservable<T> {\n  [Symbol.observable]: () => Subscribable<T>;\n}\n\n/* NOTIFICATIONS */\n\n/**\n * A notification representing a \"next\" from an observable.\n * Can be used with {@link dematerialize}.\n */\nexport interface NextNotification<T> {\n  /** The kind of notification. Always \"N\" */\n  kind: 'N';\n  /** The value of the notification. */\n  value: T;\n}\n\n/**\n * A notification representing an \"error\" from an observable.\n * Can be used with {@link dematerialize}.\n */\nexport interface ErrorNotification {\n  /** The kind of notification. Always \"E\" */\n  kind: 'E';\n  error: any;\n}\n\n/**\n * A notification representing a \"completion\" from an observable.\n * Can be used with {@link dematerialize}.\n */\nexport interface CompleteNotification {\n  kind: 'C';\n}\n\n/**\n * Valid observable notification types.\n */\nexport type ObservableNotification<T> = NextNotification<T> | ErrorNotification | CompleteNotification;\n\n/* OBSERVER INTERFACES */\n\nexport interface NextObserver<T> {\n  closed?: boolean;\n  next: (value: T) => void;\n  error?: (err: any) => void;\n  complete?: () => void;\n}\n\nexport interface ErrorObserver<T> {\n  closed?: boolean;\n  next?: (value: T) => void;\n  error: (err: any) => void;\n  complete?: () => void;\n}\n\nexport interface CompletionObserver<T> {\n  closed?: boolean;\n  next?: (value: T) => void;\n  error?: (err: any) => void;\n  complete: () => void;\n}\n\nexport type PartialObserver<T> = NextObserver<T> | ErrorObserver<T> | CompletionObserver<T>;\n\n/**\n * An object interface that defines a set of callback functions a user can use to get\n * notified of any set of {@link Observable}\n * {@link guide/glossary-and-semantics#notification notification} events.\n *\n * For more info, please refer to {@link guide/observer this guide}.\n */\nexport interface Observer<T> {\n  /**\n   * A callback function that gets called by the producer during the subscription when\n   * the producer \"has\" the `value`. It won't be called if `error` or `complete` callback\n   * functions have been called, nor after the consumer has unsubscribed.\n   *\n   * For more info, please refer to {@link guide/glossary-and-semantics#next this guide}.\n   */\n  next: (value: T) => void;\n  /**\n   * A callback function that gets called by the producer if and when it encountered a\n   * problem of any kind. The errored value will be provided through the `err` parameter.\n   * This callback can't be called more than one time, it can't be called if the\n   * `complete` callback function have been called previously, nor it can't be called if\n   * the consumer has unsubscribed.\n   *\n   * For more info, please refer to {@link guide/glossary-and-semantics#error this guide}.\n   */\n  error: (err: any) => void;\n  /**\n   * A callback function that gets called by the producer if and when it has no more\n   * values to provide (by calling `next` callback function). This means that no error\n   * has happened. This callback can't be called more than one time, it can't be called\n   * if the `error` callback function have been called previously, nor it can't be called\n   * if the consumer has unsubscribed.\n   *\n   * For more info, please refer to {@link guide/glossary-and-semantics#complete this guide}.\n   */\n  complete: () => void;\n}\n\nexport interface SubjectLike<T> extends Observer<T>, Subscribable<T> {}\n\n/* SCHEDULER INTERFACES */\n\nexport interface SchedulerLike extends TimestampProvider {\n  schedule<T>(work: (this: SchedulerAction<T>, state: T) => void, delay: number, state: T): Subscription;\n  schedule<T>(work: (this: SchedulerAction<T>, state?: T) => void, delay: number, state?: T): Subscription;\n  schedule<T>(work: (this: SchedulerAction<T>, state?: T) => void, delay?: number, state?: T): Subscription;\n}\n\nexport interface SchedulerAction<T> extends Subscription {\n  schedule(state?: T, delay?: number): Subscription;\n}\n\n/**\n * This is a type that provides a method to allow RxJS to create a numeric timestamp\n */\nexport interface TimestampProvider {\n  /**\n   * Returns a timestamp as a number.\n   *\n   * This is used by types like `ReplaySubject` or operators like `timestamp` to calculate\n   * the amount of time passed between events.\n   */\n  now(): number;\n}\n\n/**\n * Extracts the type from an `ObservableInput<any>`. If you have\n * `O extends ObservableInput<any>` and you pass in `Observable<number>`, or\n * `Promise<number>`, etc, it will type as `number`.\n */\nexport type ObservedValueOf<O> = O extends ObservableInput<infer T> ? T : never;\n\n/**\n * Extracts a union of element types from an `ObservableInput<any>[]`.\n * If you have `O extends ObservableInput<any>[]` and you pass in\n * `Observable<string>[]` or `Promise<string>[]` you would get\n * back a type of `string`.\n * If you pass in `[Observable<string>, Observable<number>]` you would\n * get back a type of `string | number`.\n */\nexport type ObservedValueUnionFromArray<X> = X extends Array<ObservableInput<infer T>> ? T : never;\n\n/**\n * @deprecated Renamed to {@link ObservedValueUnionFromArray}. Will be removed in v8.\n */\nexport type ObservedValuesFromArray<X> = ObservedValueUnionFromArray<X>;\n\n/**\n * Extracts a tuple of element types from an `ObservableInput<any>[]`.\n * If you have `O extends ObservableInput<any>[]` and you pass in\n * `[Observable<string>, Observable<number>]` you would get back a type\n * of `[string, number]`.\n */\nexport type ObservedValueTupleFromArray<X> = { [K in keyof X]: ObservedValueOf<X[K]> };\n\n/**\n * Used to infer types from arguments to functions like {@link forkJoin}.\n * So that you can have `forkJoin([Observable<A>, PromiseLike<B>]): Observable<[A, B]>`\n * et al.\n */\nexport type ObservableInputTuple<T> = {\n  [K in keyof T]: ObservableInput<T[K]>;\n};\n\n/**\n * Constructs a new tuple with the specified type at the head.\n * If you declare `Cons<A, [B, C]>` you will get back `[A, B, C]`.\n */\nexport type Cons<X, Y extends readonly any[]> = ((arg: X, ...rest: Y) => any) extends (...args: infer U) => any ? U : never;\n\n/**\n * Extracts the head of a tuple.\n * If you declare `Head<[A, B, C]>` you will get back `A`.\n */\nexport type Head<X extends readonly any[]> = ((...args: X) => any) extends (arg: infer U, ...rest: any[]) => any ? U : never;\n\n/**\n * Extracts the tail of a tuple.\n * If you declare `Tail<[A, B, C]>` you will get back `[B, C]`.\n */\nexport type Tail<X extends readonly any[]> = ((...args: X) => any) extends (arg: any, ...rest: infer U) => any ? U : never;\n\n/**\n * Extracts the generic value from an Array type.\n * If you have `T extends Array<any>`, and pass a `string[]` to it,\n * `ValueFromArray<T>` will return the actual type of `string`.\n */\nexport type ValueFromArray<A extends readonly unknown[]> = A extends Array<infer T> ? T : never;\n\n/**\n * Gets the value type from an {@link ObservableNotification}, if possible.\n */\nexport type ValueFromNotification<T> = T extends { kind: 'N' | 'E' | 'C' }\n  ? T extends NextNotification<any>\n    ? T extends { value: infer V }\n      ? V\n      : undefined\n    : never\n  : never;\n\n/**\n * A simple type to represent a gamut of \"falsy\" values... with a notable exception:\n * `NaN` is \"falsy\" however, it is not and cannot be typed via TypeScript. See\n * comments here: https://github.com/microsoft/TypeScript/issues/28682#issuecomment-707142417\n */\nexport type Falsy = null | undefined | false | 0 | -0 | 0n | '';\n\nexport type TruthyTypesOf<T> = T extends Falsy ? never : T;\n\n// We shouldn't rely on this type definition being available globally yet since it's\n// not necessarily available in every TS environment.\ninterface ReadableStreamDefaultReaderLike<T> {\n  // HACK: As of TS 4.2.2, The provided types for the iterator results of a `ReadableStreamDefaultReader`\n  // are significantly different enough from `IteratorResult` as to cause compilation errors.\n  // The type at the time is `ReadableStreamDefaultReadResult`.\n  read(): PromiseLike<\n    | {\n        done: false;\n        value: T;\n      }\n    | { done: true; value?: undefined }\n  >;\n  releaseLock(): void;\n}\n\n/**\n * The base signature RxJS will look for to identify and use\n * a [ReadableStream](https://streams.spec.whatwg.org/#rs-class)\n * as an {@link ObservableInput} source.\n */\nexport interface ReadableStreamLike<T> {\n  getReader(): ReadableStreamDefaultReaderLike<T>;\n}\n\n/**\n * An observable with a `connect` method that is used to create a subscription\n * to an underlying source, connecting it with all consumers via a multicast.\n */\nexport interface Connectable<T> extends Observable<T> {\n  /**\n   * (Idempotent) Calling this method will connect the underlying source observable to all subscribed consumers\n   * through an underlying {@link Subject}.\n   * @returns A subscription, that when unsubscribed, will \"disconnect\" the source from the connector subject,\n   * severing notifications to all consumers.\n   */\n  connect(): Subscription;\n}\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}