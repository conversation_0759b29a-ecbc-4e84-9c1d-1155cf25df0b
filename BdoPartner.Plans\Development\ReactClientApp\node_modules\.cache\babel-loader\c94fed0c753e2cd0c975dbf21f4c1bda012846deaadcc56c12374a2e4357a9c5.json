{"ast": null, "code": "import { Observable } from '../Observable';\nimport { innerFrom } from '../observable/innerFrom';\nimport { Subject } from '../Subject';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber, OperatorSubscriber } from './OperatorSubscriber';\nexport function groupBy(keySelector, elementOrOptions, duration, connector) {\n  return operate(function (source, subscriber) {\n    var element;\n    if (!elementOrOptions || typeof elementOrOptions === 'function') {\n      element = elementOrOptions;\n    } else {\n      duration = elementOrOptions.duration, element = elementOrOptions.element, connector = elementOrOptions.connector;\n    }\n    var groups = new Map();\n    var notify = function (cb) {\n      groups.forEach(cb);\n      cb(subscriber);\n    };\n    var handleError = function (err) {\n      return notify(function (consumer) {\n        return consumer.error(err);\n      });\n    };\n    var activeGroups = 0;\n    var teardownAttempted = false;\n    var groupBySourceSubscriber = new OperatorSubscriber(subscriber, function (value) {\n      try {\n        var key_1 = keySelector(value);\n        var group_1 = groups.get(key_1);\n        if (!group_1) {\n          groups.set(key_1, group_1 = connector ? connector() : new Subject());\n          var grouped = createGroupedObservable(key_1, group_1);\n          subscriber.next(grouped);\n          if (duration) {\n            var durationSubscriber_1 = createOperatorSubscriber(group_1, function () {\n              group_1.complete();\n              durationSubscriber_1 === null || durationSubscriber_1 === void 0 ? void 0 : durationSubscriber_1.unsubscribe();\n            }, undefined, undefined, function () {\n              return groups.delete(key_1);\n            });\n            groupBySourceSubscriber.add(innerFrom(duration(grouped)).subscribe(durationSubscriber_1));\n          }\n        }\n        group_1.next(element ? element(value) : value);\n      } catch (err) {\n        handleError(err);\n      }\n    }, function () {\n      return notify(function (consumer) {\n        return consumer.complete();\n      });\n    }, handleError, function () {\n      return groups.clear();\n    }, function () {\n      teardownAttempted = true;\n      return activeGroups === 0;\n    });\n    source.subscribe(groupBySourceSubscriber);\n    function createGroupedObservable(key, groupSubject) {\n      var result = new Observable(function (groupSubscriber) {\n        activeGroups++;\n        var innerSub = groupSubject.subscribe(groupSubscriber);\n        return function () {\n          innerSub.unsubscribe();\n          --activeGroups === 0 && teardownAttempted && groupBySourceSubscriber.unsubscribe();\n        };\n      });\n      result.key = key;\n      return result;\n    }\n  });\n}", "map": {"version": 3, "names": ["Observable", "innerFrom", "Subject", "operate", "createOperatorSubscriber", "OperatorSubscriber", "groupBy", "keySelector", "elementOrOptions", "duration", "connector", "source", "subscriber", "element", "groups", "Map", "notify", "cb", "for<PERSON>ach", "handleError", "err", "consumer", "error", "activeGroups", "teardownAttempted", "groupBySourceSubscriber", "value", "key_1", "group_1", "get", "set", "grouped", "createGroupedObservable", "next", "durationSubscriber_1", "complete", "unsubscribe", "undefined", "delete", "add", "subscribe", "clear", "key", "groupSubject", "result", "groupSubscriber", "innerSub"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\groupBy.ts"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { innerFrom } from '../observable/innerFrom';\nimport { Subject } from '../Subject';\nimport { ObservableInput, Observer, OperatorFunction, SubjectLike } from '../types';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber, OperatorSubscriber } from './OperatorSubscriber';\n\nexport interface BasicGroupByOptions<K, T> {\n  element?: undefined;\n  duration?: (grouped: GroupedObservable<K, T>) => ObservableInput<any>;\n  connector?: () => SubjectLike<T>;\n}\n\nexport interface GroupByOptionsWithElement<K, E, T> {\n  element: (value: T) => E;\n  duration?: (grouped: GroupedObservable<K, E>) => ObservableInput<any>;\n  connector?: () => SubjectLike<E>;\n}\n\nexport function groupBy<T, K>(key: (value: T) => K, options: BasicGroupByOptions<K, T>): OperatorFunction<T, GroupedObservable<K, T>>;\n\nexport function groupBy<T, K, E>(\n  key: (value: T) => K,\n  options: GroupByOptionsWithElement<K, E, T>\n): OperatorFunction<T, GroupedObservable<K, E>>;\n\nexport function groupBy<T, K extends T>(\n  key: (value: T) => value is K\n): OperatorFunction<T, GroupedObservable<true, K> | GroupedObservable<false, Exclude<T, K>>>;\n\nexport function groupBy<T, K>(key: (value: T) => K): OperatorFunction<T, GroupedObservable<K, T>>;\n\n/**\n * @deprecated use the options parameter instead.\n */\nexport function groupBy<T, K>(\n  key: (value: T) => K,\n  element: void,\n  duration: (grouped: GroupedObservable<K, T>) => Observable<any>\n): OperatorFunction<T, GroupedObservable<K, T>>;\n\n/**\n * @deprecated use the options parameter instead.\n */\nexport function groupBy<T, K, R>(\n  key: (value: T) => K,\n  element?: (value: T) => R,\n  duration?: (grouped: GroupedObservable<K, R>) => Observable<any>\n): OperatorFunction<T, GroupedObservable<K, R>>;\n\n/**\n * Groups the items emitted by an Observable according to a specified criterion,\n * and emits these grouped items as `GroupedObservables`, one\n * {@link GroupedObservable} per group.\n *\n * ![](groupBy.png)\n *\n * When the Observable emits an item, a key is computed for this item with the key function.\n *\n * If a {@link GroupedObservable} for this key exists, this {@link GroupedObservable} emits. Otherwise, a new\n * {@link GroupedObservable} for this key is created and emits.\n *\n * A {@link GroupedObservable} represents values belonging to the same group represented by a common key. The common\n * key is available as the `key` field of a {@link GroupedObservable} instance.\n *\n * The elements emitted by {@link GroupedObservable}s are by default the items emitted by the Observable, or elements\n * returned by the element function.\n *\n * ## Examples\n *\n * Group objects by `id` and return as array\n *\n * ```ts\n * import { of, groupBy, mergeMap, reduce } from 'rxjs';\n *\n * of(\n *   { id: 1, name: 'JavaScript' },\n *   { id: 2, name: 'Parcel' },\n *   { id: 2, name: 'webpack' },\n *   { id: 1, name: 'TypeScript' },\n *   { id: 3, name: 'TSLint' }\n * ).pipe(\n *   groupBy(p => p.id),\n *   mergeMap(group$ => group$.pipe(reduce((acc, cur) => [...acc, cur], [])))\n * )\n * .subscribe(p => console.log(p));\n *\n * // displays:\n * // [{ id: 1, name: 'JavaScript' }, { id: 1, name: 'TypeScript'}]\n * // [{ id: 2, name: 'Parcel' }, { id: 2, name: 'webpack'}]\n * // [{ id: 3, name: 'TSLint' }]\n * ```\n *\n * Pivot data on the `id` field\n *\n * ```ts\n * import { of, groupBy, mergeMap, reduce, map } from 'rxjs';\n *\n * of(\n *   { id: 1, name: 'JavaScript' },\n *   { id: 2, name: 'Parcel' },\n *   { id: 2, name: 'webpack' },\n *   { id: 1, name: 'TypeScript' },\n *   { id: 3, name: 'TSLint' }\n * ).pipe(\n *   groupBy(p => p.id, { element: p => p.name }),\n *   mergeMap(group$ => group$.pipe(reduce((acc, cur) => [...acc, cur], [`${ group$.key }`]))),\n *   map(arr => ({ id: parseInt(arr[0], 10), values: arr.slice(1) }))\n * )\n * .subscribe(p => console.log(p));\n *\n * // displays:\n * // { id: 1, values: [ 'JavaScript', 'TypeScript' ] }\n * // { id: 2, values: [ 'Parcel', 'webpack' ] }\n * // { id: 3, values: [ 'TSLint' ] }\n * ```\n *\n * @param key A function that extracts the key\n * for each item.\n * @param element A function that extracts the\n * return element for each item.\n * @param duration\n * A function that returns an Observable to determine how long each group should\n * exist.\n * @param connector Factory function to create an\n * intermediate Subject through which grouped elements are emitted.\n * @return A function that returns an Observable that emits GroupedObservables,\n * each of which corresponds to a unique key value and each of which emits\n * those items from the source Observable that share that key value.\n *\n * @deprecated Use the options parameter instead.\n */\nexport function groupBy<T, K, R>(\n  key: (value: T) => K,\n  element?: (value: T) => R,\n  duration?: (grouped: GroupedObservable<K, R>) => Observable<any>,\n  connector?: () => Subject<R>\n): OperatorFunction<T, GroupedObservable<K, R>>;\n\n// Impl\nexport function groupBy<T, K, R>(\n  keySelector: (value: T) => K,\n  elementOrOptions?: ((value: any) => any) | void | BasicGroupByOptions<K, T> | GroupByOptionsWithElement<K, R, T>,\n  duration?: (grouped: GroupedObservable<any, any>) => ObservableInput<any>,\n  connector?: () => SubjectLike<any>\n): OperatorFunction<T, GroupedObservable<K, R>> {\n  return operate((source, subscriber) => {\n    let element: ((value: any) => any) | void;\n    if (!elementOrOptions || typeof elementOrOptions === 'function') {\n      element = elementOrOptions as ((value: any) => any);\n    } else {\n      ({ duration, element, connector } = elementOrOptions);\n    }\n\n    // A lookup for the groups that we have so far.\n    const groups = new Map<K, SubjectLike<any>>();\n\n    // Used for notifying all groups and the subscriber in the same way.\n    const notify = (cb: (group: Observer<any>) => void) => {\n      groups.forEach(cb);\n      cb(subscriber);\n    };\n\n    // Used to handle errors from the source, AND errors that occur during the\n    // next call from the source.\n    const handleError = (err: any) => notify((consumer) => consumer.error(err));\n\n    // The number of actively subscribed groups\n    let activeGroups = 0;\n\n    // Whether or not teardown was attempted on this subscription.\n    let teardownAttempted = false;\n\n    // Capturing a reference to this, because we need a handle to it\n    // in `createGroupedObservable` below. This is what we use to\n    // subscribe to our source observable. This sometimes needs to be unsubscribed\n    // out-of-band with our `subscriber` which is the downstream subscriber, or destination,\n    // in cases where a user unsubscribes from the main resulting subscription, but\n    // still has groups from this subscription subscribed and would expect values from it\n    // Consider:  `source.pipe(groupBy(fn), take(2))`.\n    const groupBySourceSubscriber = new OperatorSubscriber(\n      subscriber,\n      (value: T) => {\n        // Because we have to notify all groups of any errors that occur in here,\n        // we have to add our own try/catch to ensure that those errors are propagated.\n        // OperatorSubscriber will only send the error to the main subscriber.\n        try {\n          const key = keySelector(value);\n\n          let group = groups.get(key);\n          if (!group) {\n            // Create our group subject\n            groups.set(key, (group = connector ? connector() : new Subject<any>()));\n\n            // Emit the grouped observable. Note that we can't do a simple `asObservable()` here,\n            // because the grouped observable has special semantics around reference counting\n            // to ensure we don't sever our connection to the source prematurely.\n            const grouped = createGroupedObservable(key, group);\n            subscriber.next(grouped);\n\n            if (duration) {\n              const durationSubscriber = createOperatorSubscriber(\n                // Providing the group here ensures that it is disposed of -- via `unsubscribe` --\n                // when the duration subscription is torn down. That is important, because then\n                // if someone holds a handle to the grouped observable and tries to subscribe to it\n                // after the connection to the source has been severed, they will get an\n                // `ObjectUnsubscribedError` and know they can't possibly get any notifications.\n                group as any,\n                () => {\n                  // Our duration notified! We can complete the group.\n                  // The group will be removed from the map in the finalization phase.\n                  group!.complete();\n                  durationSubscriber?.unsubscribe();\n                },\n                // Completions are also sent to the group, but just the group.\n                undefined,\n                // Errors on the duration subscriber are sent to the group\n                // but only the group. They are not sent to the main subscription.\n                undefined,\n                // Finalization: Remove this group from our map.\n                () => groups.delete(key)\n              );\n\n              // Start our duration notifier.\n              groupBySourceSubscriber.add(innerFrom(duration(grouped)).subscribe(durationSubscriber));\n            }\n          }\n\n          // Send the value to our group.\n          group.next(element ? element(value) : value);\n        } catch (err) {\n          handleError(err);\n        }\n      },\n      // Source completes.\n      () => notify((consumer) => consumer.complete()),\n      // Error from the source.\n      handleError,\n      // Free up memory.\n      // When the source subscription is _finally_ torn down, release the subjects and keys\n      // in our groups Map, they may be quite large and we don't want to keep them around if we\n      // don't have to.\n      () => groups.clear(),\n      () => {\n        teardownAttempted = true;\n        // We only kill our subscription to the source if we have\n        // no active groups. As stated above, consider this scenario:\n        // source$.pipe(groupBy(fn), take(2)).\n        return activeGroups === 0;\n      }\n    );\n\n    // Subscribe to the source\n    source.subscribe(groupBySourceSubscriber);\n\n    /**\n     * Creates the actual grouped observable returned.\n     * @param key The key of the group\n     * @param groupSubject The subject that fuels the group\n     */\n    function createGroupedObservable(key: K, groupSubject: SubjectLike<any>) {\n      const result: any = new Observable<T>((groupSubscriber) => {\n        activeGroups++;\n        const innerSub = groupSubject.subscribe(groupSubscriber);\n        return () => {\n          innerSub.unsubscribe();\n          // We can kill the subscription to our source if we now have no more\n          // active groups subscribed, and a finalization was already attempted on\n          // the source.\n          --activeGroups === 0 && teardownAttempted && groupBySourceSubscriber.unsubscribe();\n        };\n      });\n      result.key = key;\n      return result;\n    }\n  });\n}\n\n/**\n * An observable of values that is the emitted by the result of a {@link groupBy} operator,\n * contains a `key` property for the grouping.\n */\nexport interface GroupedObservable<K, T> extends Observable<T> {\n  /**\n   * The key value for the grouped notifications.\n   */\n  readonly key: K;\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,SAAS,QAAQ,yBAAyB;AACnD,SAASC,OAAO,QAAQ,YAAY;AAEpC,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,EAAEC,kBAAkB,QAAQ,sBAAsB;AAuInF,OAAM,SAAUC,OAAOA,CACrBC,WAA4B,EAC5BC,gBAAgH,EAChHC,QAAyE,EACzEC,SAAkC;EAElC,OAAOP,OAAO,CAAC,UAACQ,MAAM,EAAEC,UAAU;IAChC,IAAIC,OAAqC;IACzC,IAAI,CAACL,gBAAgB,IAAI,OAAOA,gBAAgB,KAAK,UAAU,EAAE;MAC/DK,OAAO,GAAGL,gBAAyC;KACpD,MAAM;MACFC,QAAQ,GAAyBD,gBAAgB,CAAAC,QAAzC,EAAEI,OAAO,GAAgBL,gBAAgB,CAAAK,OAAhC,EAAEH,SAAS,GAAKF,gBAAgB,CAAAE,SAArB;;IAIjC,IAAMI,MAAM,GAAG,IAAIC,GAAG,EAAuB;IAG7C,IAAMC,MAAM,GAAG,SAAAA,CAACC,EAAkC;MAChDH,MAAM,CAACI,OAAO,CAACD,EAAE,CAAC;MAClBA,EAAE,CAACL,UAAU,CAAC;IAChB,CAAC;IAID,IAAMO,WAAW,GAAG,SAAAA,CAACC,GAAQ;MAAK,OAAAJ,MAAM,CAAC,UAACK,QAAQ;QAAK,OAAAA,QAAQ,CAACC,KAAK,CAACF,GAAG,CAAC;MAAnB,CAAmB,CAAC;IAAzC,CAAyC;IAG3E,IAAIG,YAAY,GAAG,CAAC;IAGpB,IAAIC,iBAAiB,GAAG,KAAK;IAS7B,IAAMC,uBAAuB,GAAG,IAAIpB,kBAAkB,CACpDO,UAAU,EACV,UAACc,KAAQ;MAIP,IAAI;QACF,IAAMC,KAAG,GAAGpB,WAAW,CAACmB,KAAK,CAAC;QAE9B,IAAIE,OAAK,GAAGd,MAAM,CAACe,GAAG,CAACF,KAAG,CAAC;QAC3B,IAAI,CAACC,OAAK,EAAE;UAEVd,MAAM,CAACgB,GAAG,CAACH,KAAG,EAAGC,OAAK,GAAGlB,SAAS,GAAGA,SAAS,EAAE,GAAG,IAAIR,OAAO,EAAQ,CAAC;UAKvE,IAAM6B,OAAO,GAAGC,uBAAuB,CAACL,KAAG,EAAEC,OAAK,CAAC;UACnDhB,UAAU,CAACqB,IAAI,CAACF,OAAO,CAAC;UAExB,IAAItB,QAAQ,EAAE;YACZ,IAAMyB,oBAAkB,GAAG9B,wBAAwB,CAMjDwB,OAAY,EACZ;cAGEA,OAAM,CAACO,QAAQ,EAAE;cACjBD,oBAAkB,aAAlBA,oBAAkB,uBAAlBA,oBAAkB,CAAEE,WAAW,EAAE;YACnC,CAAC,EAEDC,SAAS,EAGTA,SAAS,EAET;cAAM,OAAAvB,MAAM,CAACwB,MAAM,CAACX,KAAG,CAAC;YAAlB,CAAkB,CACzB;YAGDF,uBAAuB,CAACc,GAAG,CAACtC,SAAS,CAACQ,QAAQ,CAACsB,OAAO,CAAC,CAAC,CAACS,SAAS,CAACN,oBAAkB,CAAC,CAAC;;;QAK3FN,OAAK,CAACK,IAAI,CAACpB,OAAO,GAAGA,OAAO,CAACa,KAAK,CAAC,GAAGA,KAAK,CAAC;OAC7C,CAAC,OAAON,GAAG,EAAE;QACZD,WAAW,CAACC,GAAG,CAAC;;IAEpB,CAAC,EAED;MAAM,OAAAJ,MAAM,CAAC,UAACK,QAAQ;QAAK,OAAAA,QAAQ,CAACc,QAAQ,EAAE;MAAnB,CAAmB,CAAC;IAAzC,CAAyC,EAE/ChB,WAAW,EAKX;MAAM,OAAAL,MAAM,CAAC2B,KAAK,EAAE;IAAd,CAAc,EACpB;MACEjB,iBAAiB,GAAG,IAAI;MAIxB,OAAOD,YAAY,KAAK,CAAC;IAC3B,CAAC,CACF;IAGDZ,MAAM,CAAC6B,SAAS,CAACf,uBAAuB,CAAC;IAOzC,SAASO,uBAAuBA,CAACU,GAAM,EAAEC,YAA8B;MACrE,IAAMC,MAAM,GAAQ,IAAI5C,UAAU,CAAI,UAAC6C,eAAe;QACpDtB,YAAY,EAAE;QACd,IAAMuB,QAAQ,GAAGH,YAAY,CAACH,SAAS,CAACK,eAAe,CAAC;QACxD,OAAO;UACLC,QAAQ,CAACV,WAAW,EAAE;UAItB,EAAEb,YAAY,KAAK,CAAC,IAAIC,iBAAiB,IAAIC,uBAAuB,CAACW,WAAW,EAAE;QACpF,CAAC;MACH,CAAC,CAAC;MACFQ,MAAM,CAACF,GAAG,GAAGA,GAAG;MAChB,OAAOE,MAAM;IACf;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}