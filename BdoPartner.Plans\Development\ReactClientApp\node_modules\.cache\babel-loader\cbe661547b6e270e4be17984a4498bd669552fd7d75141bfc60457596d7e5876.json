{"ast": null, "code": "import { __values } from \"tslib\";\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { arrRemove } from '../util/arrRemove';\nexport function bufferCount(bufferSize, startBufferEvery) {\n  if (startBufferEvery === void 0) {\n    startBufferEvery = null;\n  }\n  startBufferEvery = startBufferEvery !== null && startBufferEvery !== void 0 ? startBufferEvery : bufferSize;\n  return operate(function (source, subscriber) {\n    var buffers = [];\n    var count = 0;\n    source.subscribe(createOperatorSubscriber(subscriber, function (value) {\n      var e_1, _a, e_2, _b;\n      var toEmit = null;\n      if (count++ % startBufferEvery === 0) {\n        buffers.push([]);\n      }\n      try {\n        for (var buffers_1 = __values(buffers), buffers_1_1 = buffers_1.next(); !buffers_1_1.done; buffers_1_1 = buffers_1.next()) {\n          var buffer = buffers_1_1.value;\n          buffer.push(value);\n          if (bufferSize <= buffer.length) {\n            toEmit = toEmit !== null && toEmit !== void 0 ? toEmit : [];\n            toEmit.push(buffer);\n          }\n        }\n      } catch (e_1_1) {\n        e_1 = {\n          error: e_1_1\n        };\n      } finally {\n        try {\n          if (buffers_1_1 && !buffers_1_1.done && (_a = buffers_1.return)) _a.call(buffers_1);\n        } finally {\n          if (e_1) throw e_1.error;\n        }\n      }\n      if (toEmit) {\n        try {\n          for (var toEmit_1 = __values(toEmit), toEmit_1_1 = toEmit_1.next(); !toEmit_1_1.done; toEmit_1_1 = toEmit_1.next()) {\n            var buffer = toEmit_1_1.value;\n            arrRemove(buffers, buffer);\n            subscriber.next(buffer);\n          }\n        } catch (e_2_1) {\n          e_2 = {\n            error: e_2_1\n          };\n        } finally {\n          try {\n            if (toEmit_1_1 && !toEmit_1_1.done && (_b = toEmit_1.return)) _b.call(toEmit_1);\n          } finally {\n            if (e_2) throw e_2.error;\n          }\n        }\n      }\n    }, function () {\n      var e_3, _a;\n      try {\n        for (var buffers_2 = __values(buffers), buffers_2_1 = buffers_2.next(); !buffers_2_1.done; buffers_2_1 = buffers_2.next()) {\n          var buffer = buffers_2_1.value;\n          subscriber.next(buffer);\n        }\n      } catch (e_3_1) {\n        e_3 = {\n          error: e_3_1\n        };\n      } finally {\n        try {\n          if (buffers_2_1 && !buffers_2_1.done && (_a = buffers_2.return)) _a.call(buffers_2);\n        } finally {\n          if (e_3) throw e_3.error;\n        }\n      }\n      subscriber.complete();\n    }, undefined, function () {\n      buffers = null;\n    }));\n  });\n}", "map": {"version": 3, "names": ["operate", "createOperatorSubscriber", "arr<PERSON><PERSON><PERSON>", "bufferCount", "bufferSize", "startBufferEvery", "source", "subscriber", "buffers", "count", "subscribe", "value", "toEmit", "push", "buffers_1", "__values", "buffers_1_1", "next", "done", "buffer", "length", "toEmit_1", "toEmit_1_1", "buffers_2", "buffers_2_1", "complete", "undefined"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\bufferCount.ts"], "sourcesContent": ["import { OperatorFunction } from '../types';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { arrRemove } from '../util/arrRemove';\n\n/**\n * Buffers the source Observable values until the size hits the maximum\n * `bufferSize` given.\n *\n * <span class=\"informal\">Collects values from the past as an array, and emits\n * that array only when its size reaches `bufferSize`.</span>\n *\n * ![](bufferCount.png)\n *\n * Buffers a number of values from the source Observable by `bufferSize` then\n * emits the buffer and clears it, and starts a new buffer each\n * `startBufferEvery` values. If `startBufferEvery` is not provided or is\n * `null`, then new buffers are started immediately at the start of the source\n * and when each buffer closes and is emitted.\n *\n * ## Examples\n *\n * Emit the last two click events as an array\n *\n * ```ts\n * import { fromEvent, bufferCount } from 'rxjs';\n *\n * const clicks = fromEvent(document, 'click');\n * const buffered = clicks.pipe(bufferCount(2));\n * buffered.subscribe(x => console.log(x));\n * ```\n *\n * On every click, emit the last two click events as an array\n *\n * ```ts\n * import { fromEvent, bufferCount } from 'rxjs';\n *\n * const clicks = fromEvent(document, 'click');\n * const buffered = clicks.pipe(bufferCount(2, 1));\n * buffered.subscribe(x => console.log(x));\n * ```\n *\n * @see {@link buffer}\n * @see {@link bufferTime}\n * @see {@link bufferToggle}\n * @see {@link bufferWhen}\n * @see {@link pairwise}\n * @see {@link windowCount}\n *\n * @param bufferSize The maximum size of the buffer emitted.\n * @param startBufferEvery Interval at which to start a new buffer.\n * For example if `startBufferEvery` is `2`, then a new buffer will be started\n * on every other value from the source. A new buffer is started at the\n * beginning of the source by default.\n * @return A function that returns an Observable of arrays of buffered values.\n */\nexport function bufferCount<T>(bufferSize: number, startBufferEvery: number | null = null): OperatorFunction<T, T[]> {\n  // If no `startBufferEvery` value was supplied, then we're\n  // opening and closing on the bufferSize itself.\n  startBufferEvery = startBufferEvery ?? bufferSize;\n\n  return operate((source, subscriber) => {\n    let buffers: T[][] = [];\n    let count = 0;\n\n    source.subscribe(\n      createOperatorSubscriber(\n        subscriber,\n        (value) => {\n          let toEmit: T[][] | null = null;\n\n          // Check to see if we need to start a buffer.\n          // This will start one at the first value, and then\n          // a new one every N after that.\n          if (count++ % startBufferEvery! === 0) {\n            buffers.push([]);\n          }\n\n          // Push our value into our active buffers.\n          for (const buffer of buffers) {\n            buffer.push(value);\n            // Check to see if we're over the bufferSize\n            // if we are, record it so we can emit it later.\n            // If we emitted it now and removed it, it would\n            // mutate the `buffers` array while we're looping\n            // over it.\n            if (bufferSize <= buffer.length) {\n              toEmit = toEmit ?? [];\n              toEmit.push(buffer);\n            }\n          }\n\n          if (toEmit) {\n            // We have found some buffers that are over the\n            // `bufferSize`. Emit them, and remove them from our\n            // buffers list.\n            for (const buffer of toEmit) {\n              arrRemove(buffers, buffer);\n              subscriber.next(buffer);\n            }\n          }\n        },\n        () => {\n          // When the source completes, emit all of our\n          // active buffers.\n          for (const buffer of buffers) {\n            subscriber.next(buffer);\n          }\n          subscriber.complete();\n        },\n        // Pass all errors through to consumer.\n        undefined,\n        () => {\n          // Clean up our memory when we finalize\n          buffers = null!;\n        }\n      )\n    );\n  });\n}\n"], "mappings": ";AACA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,SAASC,SAAS,QAAQ,mBAAmB;AAqD7C,OAAM,SAAUC,WAAWA,CAAIC,UAAkB,EAAEC,gBAAsC;EAAtC,IAAAA,gBAAA;IAAAA,gBAAA,OAAsC;EAAA;EAGvFA,gBAAgB,GAAGA,gBAAgB,aAAhBA,gBAAgB,cAAhBA,gBAAgB,GAAID,UAAU;EAEjD,OAAOJ,OAAO,CAAC,UAACM,MAAM,EAAEC,UAAU;IAChC,IAAIC,OAAO,GAAU,EAAE;IACvB,IAAIC,KAAK,GAAG,CAAC;IAEbH,MAAM,CAACI,SAAS,CACdT,wBAAwB,CACtBM,UAAU,EACV,UAACI,KAAK;;MACJ,IAAIC,MAAM,GAAiB,IAAI;MAK/B,IAAIH,KAAK,EAAE,GAAGJ,gBAAiB,KAAK,CAAC,EAAE;QACrCG,OAAO,CAACK,IAAI,CAAC,EAAE,CAAC;;;QAIlB,KAAqB,IAAAC,SAAA,GAAAC,QAAA,CAAAP,OAAO,GAAAQ,WAAA,GAAAF,SAAA,CAAAG,IAAA,KAAAD,WAAA,CAAAE,IAAA,EAAAF,WAAA,GAAAF,SAAA,CAAAG,IAAA,IAAE;UAAzB,IAAME,MAAM,GAAAH,WAAA,CAAAL,KAAA;UACfQ,MAAM,CAACN,IAAI,CAACF,KAAK,CAAC;UAMlB,IAAIP,UAAU,IAAIe,MAAM,CAACC,MAAM,EAAE;YAC/BR,MAAM,GAAGA,MAAM,aAANA,MAAM,cAANA,MAAM,GAAI,EAAE;YACrBA,MAAM,CAACC,IAAI,CAACM,MAAM,CAAC;;;;;;;;;;;;;;MAIvB,IAAIP,MAAM,EAAE;;UAIV,KAAqB,IAAAS,QAAA,GAAAN,QAAA,CAAAH,MAAM,GAAAU,UAAA,GAAAD,QAAA,CAAAJ,IAAA,KAAAK,UAAA,CAAAJ,IAAA,EAAAI,UAAA,GAAAD,QAAA,CAAAJ,IAAA,IAAE;YAAxB,IAAME,MAAM,GAAAG,UAAA,CAAAX,KAAA;YACfT,SAAS,CAACM,OAAO,EAAEW,MAAM,CAAC;YAC1BZ,UAAU,CAACU,IAAI,CAACE,MAAM,CAAC;;;;;;;;;;;;;;IAG7B,CAAC,EACD;;;QAGE,KAAqB,IAAAI,SAAA,GAAAR,QAAA,CAAAP,OAAO,GAAAgB,WAAA,GAAAD,SAAA,CAAAN,IAAA,KAAAO,WAAA,CAAAN,IAAA,EAAAM,WAAA,GAAAD,SAAA,CAAAN,IAAA,IAAE;UAAzB,IAAME,MAAM,GAAAK,WAAA,CAAAb,KAAA;UACfJ,UAAU,CAACU,IAAI,CAACE,MAAM,CAAC;;;;;;;;;;;;;MAEzBZ,UAAU,CAACkB,QAAQ,EAAE;IACvB,CAAC,EAEDC,SAAS,EACT;MAEElB,OAAO,GAAG,IAAK;IACjB,CAAC,CACF,CACF;EACH,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}