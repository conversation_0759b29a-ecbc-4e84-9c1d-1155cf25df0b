{"ast": null, "code": "import { __read, __spreadArray } from \"tslib\";\nimport { combineLatest } from './combineLatest';\nexport function combineLatestWith() {\n  var otherSources = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    otherSources[_i] = arguments[_i];\n  }\n  return combineLatest.apply(void 0, __spreadArray([], __read(otherSources)));\n}", "map": {"version": 3, "names": ["combineLatest", "combineLatestWith", "otherSources", "_i", "arguments", "length", "apply", "__spread<PERSON><PERSON>y", "__read"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\combineLatestWith.ts"], "sourcesContent": ["import { ObservableInputTuple, OperatorFunction, Cons } from '../types';\nimport { combineLatest } from './combineLatest';\n\n/**\n * Create an observable that combines the latest values from all passed observables and the source\n * into arrays and emits them.\n *\n * Returns an observable, that when subscribed to, will subscribe to the source observable and all\n * sources provided as arguments. Once all sources emit at least one value, all of the latest values\n * will be emitted as an array. After that, every time any source emits a value, all of the latest values\n * will be emitted as an array.\n *\n * This is a useful operator for eagerly calculating values based off of changed inputs.\n *\n * ## Example\n *\n * Simple concatenation of values from two inputs\n *\n * ```ts\n * import { fromEvent, combineLatestWith, map } from 'rxjs';\n *\n * // Setup: Add two inputs to the page\n * const input1 = document.createElement('input');\n * document.body.appendChild(input1);\n * const input2 = document.createElement('input');\n * document.body.appendChild(input2);\n *\n * // Get streams of changes\n * const input1Changes$ = fromEvent(input1, 'change');\n * const input2Changes$ = fromEvent(input2, 'change');\n *\n * // Combine the changes by adding them together\n * input1Changes$.pipe(\n *   combineLatestWith(input2Changes$),\n *   map(([e1, e2]) => (<HTMLInputElement>e1.target).value + ' - ' + (<HTMLInputElement>e2.target).value)\n * )\n * .subscribe(x => console.log(x));\n * ```\n *\n * @param otherSources the other sources to subscribe to.\n * @return A function that returns an Observable that emits the latest\n * emissions from both source and provided Observables.\n */\nexport function combineLatestWith<T, A extends readonly unknown[]>(\n  ...otherSources: [...ObservableInputTuple<A>]\n): OperatorFunction<T, Cons<T, A>> {\n  return combineLatest(...otherSources);\n}\n"], "mappings": ";AACA,SAASA,aAAa,QAAQ,iBAAiB;AA0C/C,OAAM,SAAUC,iBAAiBA,CAAA;EAC/B,IAAAC,YAAA;OAAA,IAAAC,EAAA,IAA6C,EAA7CA,EAAA,GAAAC,SAAA,CAAAC,MAA6C,EAA7CF,EAAA,EAA6C;IAA7CD,YAAA,CAAAC,EAAA,IAAAC,SAAA,CAAAD,EAAA;;EAEA,OAAOH,aAAa,CAAAM,KAAA,SAAAC,aAAA,KAAAC,MAAA,CAAIN,YAAY;AACtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}