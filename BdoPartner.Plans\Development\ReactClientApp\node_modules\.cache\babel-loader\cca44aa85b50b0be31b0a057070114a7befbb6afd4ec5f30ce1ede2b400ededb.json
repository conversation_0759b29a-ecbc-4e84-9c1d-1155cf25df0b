{"ast": null, "code": "import { defer } from './defer';\nexport function iif(condition, trueResult, falseResult) {\n  return defer(function () {\n    return condition() ? trueResult : falseResult;\n  });\n}", "map": {"version": 3, "names": ["defer", "iif", "condition", "trueResult", "falseResult"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\observable\\iif.ts"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { defer } from './defer';\nimport { ObservableInput } from '../types';\n\n/**\n * Checks a boolean at subscription time, and chooses between one of two observable sources\n *\n * `iif` expects a function that returns a boolean (the `condition` function), and two sources,\n * the `trueResult` and the `falseResult`, and returns an Observable.\n *\n * At the moment of subscription, the `condition` function is called. If the result is `true`, the\n * subscription will be to the source passed as the `trueResult`, otherwise, the subscription will be\n * to the source passed as the `falseResult`.\n *\n * If you need to check more than two options to choose between more than one observable, have a look at the {@link defer} creation method.\n *\n * ## Examples\n *\n * Change at runtime which Observable will be subscribed\n *\n * ```ts\n * import { iif, of } from 'rxjs';\n *\n * let subscribeToFirst;\n * const firstOrSecond = iif(\n *   () => subscribeToFirst,\n *   of('first'),\n *   of('second')\n * );\n *\n * subscribeToFirst = true;\n * firstOrSecond.subscribe(value => console.log(value));\n *\n * // Logs:\n * // 'first'\n *\n * subscribeToFirst = false;\n * firstOrSecond.subscribe(value => console.log(value));\n *\n * // Logs:\n * // 'second'\n * ```\n *\n * Control access to an Observable\n *\n * ```ts\n * import { iif, of, EMPTY } from 'rxjs';\n *\n * let accessGranted;\n * const observableIfYouHaveAccess = iif(\n *   () => accessGranted,\n *   of('It seems you have an access...'),\n *   EMPTY\n * );\n *\n * accessGranted = true;\n * observableIfYouHaveAccess.subscribe({\n *   next: value => console.log(value),\n *   complete: () => console.log('The end')\n * });\n *\n * // Logs:\n * // 'It seems you have an access...'\n * // 'The end'\n *\n * accessGranted = false;\n * observableIfYouHaveAccess.subscribe({\n *   next: value => console.log(value),\n *   complete: () => console.log('The end')\n * });\n *\n * // Logs:\n * // 'The end'\n * ```\n *\n * @see {@link defer}\n *\n * @param condition Condition which Observable should be chosen.\n * @param trueResult An Observable that will be subscribed if condition is true.\n * @param falseResult An Observable that will be subscribed if condition is false.\n * @return An observable that proxies to `trueResult` or `falseResult`, depending on the result of the `condition` function.\n */\nexport function iif<T, F>(condition: () => boolean, trueResult: ObservableInput<T>, falseResult: ObservableInput<F>): Observable<T | F> {\n  return defer(() => (condition() ? trueResult : falseResult));\n}\n"], "mappings": "AACA,SAASA,KAAK,QAAQ,SAAS;AAiF/B,OAAM,SAAUC,GAAGA,CAAOC,SAAwB,EAAEC,UAA8B,EAAEC,WAA+B;EACjH,OAAOJ,KAAK,CAAC;IAAM,OAACE,SAAS,EAAE,GAAGC,UAAU,GAAGC,WAAW;EAAvC,CAAwC,CAAC;AAC9D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}