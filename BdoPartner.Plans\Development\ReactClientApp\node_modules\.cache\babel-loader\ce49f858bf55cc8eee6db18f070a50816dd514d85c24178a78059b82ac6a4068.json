{"ast": null, "code": "import { EMPTY } from '../observable/empty';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nimport { timer } from '../observable/timer';\nexport function repeat(countOrConfig) {\n  var _a;\n  var count = Infinity;\n  var delay;\n  if (countOrConfig != null) {\n    if (typeof countOrConfig === 'object') {\n      _a = countOrConfig.count, count = _a === void 0 ? Infinity : _a, delay = countOrConfig.delay;\n    } else {\n      count = countOrConfig;\n    }\n  }\n  return count <= 0 ? function () {\n    return EMPTY;\n  } : operate(function (source, subscriber) {\n    var soFar = 0;\n    var sourceSub;\n    var resubscribe = function () {\n      sourceSub === null || sourceSub === void 0 ? void 0 : sourceSub.unsubscribe();\n      sourceSub = null;\n      if (delay != null) {\n        var notifier = typeof delay === 'number' ? timer(delay) : innerFrom(delay(soFar));\n        var notifierSubscriber_1 = createOperatorSubscriber(subscriber, function () {\n          notifierSubscriber_1.unsubscribe();\n          subscribeToSource();\n        });\n        notifier.subscribe(notifierSubscriber_1);\n      } else {\n        subscribeToSource();\n      }\n    };\n    var subscribeToSource = function () {\n      var syncUnsub = false;\n      sourceSub = source.subscribe(createOperatorSubscriber(subscriber, undefined, function () {\n        if (++soFar < count) {\n          if (sourceSub) {\n            resubscribe();\n          } else {\n            syncUnsub = true;\n          }\n        } else {\n          subscriber.complete();\n        }\n      }));\n      if (syncUnsub) {\n        resubscribe();\n      }\n    };\n    subscribeToSource();\n  });\n}", "map": {"version": 3, "names": ["EMPTY", "operate", "createOperatorSubscriber", "innerFrom", "timer", "repeat", "countOrConfig", "count", "Infinity", "delay", "_a", "source", "subscriber", "soFar", "sourceSub", "resubscribe", "unsubscribe", "notifier", "notifierSubscriber_1", "subscribeToSource", "subscribe", "syncUnsub", "undefined", "complete"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\repeat.ts"], "sourcesContent": ["import { Subscription } from '../Subscription';\nimport { EMPTY } from '../observable/empty';\nimport { operate } from '../util/lift';\nimport { MonoTypeOperatorFunction, ObservableInput } from '../types';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nimport { timer } from '../observable/timer';\n\nexport interface RepeatConfig {\n  /**\n   * The number of times to repeat the source. Defaults to `Infinity`.\n   */\n  count?: number;\n\n  /**\n   * If a `number`, will delay the repeat of the source by that number of milliseconds.\n   * If a function, it will provide the number of times the source has been subscribed to,\n   * and the return value should be a valid observable input that will notify when the source\n   * should be repeated. If the notifier observable is empty, the result will complete.\n   */\n  delay?: number | ((count: number) => ObservableInput<any>);\n}\n\n/**\n * Returns an Observable that will resubscribe to the source stream when the source stream completes.\n *\n * <span class=\"informal\">Repeats all values emitted on the source. It's like {@link retry}, but for non error cases.</span>\n *\n * ![](repeat.png)\n *\n * Repeat will output values from a source until the source completes, then it will resubscribe to the\n * source a specified number of times, with a specified delay. Repeat can be particularly useful in\n * combination with closing operators like {@link take}, {@link takeUntil}, {@link first}, or {@link takeWhile},\n * as it can be used to restart a source again from scratch.\n *\n * Repeat is very similar to {@link retry}, where {@link retry} will resubscribe to the source in the error case, but\n * `repeat` will resubscribe if the source completes.\n *\n * Note that `repeat` will _not_ catch errors. Use {@link retry} for that.\n *\n * - `repeat(0)` returns an empty observable\n * - `repeat()` will repeat forever\n * - `repeat({ delay: 200 })` will repeat forever, with a delay of 200ms between repetitions.\n * - `repeat({ count: 2, delay: 400 })` will repeat twice, with a delay of 400ms between repetitions.\n * - `repeat({ delay: (count) => timer(count * 1000) })` will repeat forever, but will have a delay that grows by one second for each repetition.\n *\n * ## Example\n *\n * Repeat a message stream\n *\n * ```ts\n * import { of, repeat } from 'rxjs';\n *\n * const source = of('Repeat message');\n * const result = source.pipe(repeat(3));\n *\n * result.subscribe(x => console.log(x));\n *\n * // Results\n * // 'Repeat message'\n * // 'Repeat message'\n * // 'Repeat message'\n * ```\n *\n * Repeat 3 values, 2 times\n *\n * ```ts\n * import { interval, take, repeat } from 'rxjs';\n *\n * const source = interval(1000);\n * const result = source.pipe(take(3), repeat(2));\n *\n * result.subscribe(x => console.log(x));\n *\n * // Results every second\n * // 0\n * // 1\n * // 2\n * // 0\n * // 1\n * // 2\n * ```\n *\n * Defining two complex repeats with delays on the same source.\n * Note that the second repeat cannot be called until the first\n * repeat as exhausted it's count.\n *\n * ```ts\n * import { defer, of, repeat } from 'rxjs';\n *\n * const source = defer(() => {\n *    return of(`Hello, it is ${new Date()}`)\n * });\n *\n * source.pipe(\n *    // Repeat 3 times with a delay of 1 second between repetitions\n *    repeat({\n *      count: 3,\n *      delay: 1000,\n *    }),\n *\n *    // *Then* repeat forever, but with an exponential step-back\n *    // maxing out at 1 minute.\n *    repeat({\n *      delay: (count) => timer(Math.min(60000, 2 ^ count * 1000))\n *    })\n * )\n * ```\n *\n * @see {@link repeatWhen}\n * @see {@link retry}\n *\n * @param countOrConfig Either the number of times the source Observable items are repeated\n * (a count of 0 will yield an empty Observable) or a {@link RepeatConfig} object.\n */\nexport function repeat<T>(countOrConfig?: number | RepeatConfig): MonoTypeOperatorFunction<T> {\n  let count = Infinity;\n  let delay: RepeatConfig['delay'];\n\n  if (countOrConfig != null) {\n    if (typeof countOrConfig === 'object') {\n      ({ count = Infinity, delay } = countOrConfig);\n    } else {\n      count = countOrConfig;\n    }\n  }\n\n  return count <= 0\n    ? () => EMPTY\n    : operate((source, subscriber) => {\n        let soFar = 0;\n        let sourceSub: Subscription | null;\n\n        const resubscribe = () => {\n          sourceSub?.unsubscribe();\n          sourceSub = null;\n          if (delay != null) {\n            const notifier = typeof delay === 'number' ? timer(delay) : innerFrom(delay(soFar));\n            const notifierSubscriber = createOperatorSubscriber(subscriber, () => {\n              notifierSubscriber.unsubscribe();\n              subscribeToSource();\n            });\n            notifier.subscribe(notifierSubscriber);\n          } else {\n            subscribeToSource();\n          }\n        };\n\n        const subscribeToSource = () => {\n          let syncUnsub = false;\n          sourceSub = source.subscribe(\n            createOperatorSubscriber(subscriber, undefined, () => {\n              if (++soFar < count) {\n                if (sourceSub) {\n                  resubscribe();\n                } else {\n                  syncUnsub = true;\n                }\n              } else {\n                subscriber.complete();\n              }\n            })\n          );\n\n          if (syncUnsub) {\n            resubscribe();\n          }\n        };\n\n        subscribeToSource();\n      });\n}\n"], "mappings": "AACA,SAASA,KAAK,QAAQ,qBAAqB;AAC3C,SAASC,OAAO,QAAQ,cAAc;AAEtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,SAASC,SAAS,QAAQ,yBAAyB;AACnD,SAASC,KAAK,QAAQ,qBAAqB;AA6G3C,OAAM,SAAUC,MAAMA,CAAIC,aAAqC;;EAC7D,IAAIC,KAAK,GAAGC,QAAQ;EACpB,IAAIC,KAA4B;EAEhC,IAAIH,aAAa,IAAI,IAAI,EAAE;IACzB,IAAI,OAAOA,aAAa,KAAK,QAAQ,EAAE;MAClCI,EAAA,GAA4BJ,aAAa,CAAAC,KAAzB,EAAhBA,KAAK,GAAAG,EAAA,cAAGF,QAAQ,GAAAE,EAAA,EAAED,KAAK,GAAKH,aAAa,CAAAG,KAAlB;KAC3B,MAAM;MACLF,KAAK,GAAGD,aAAa;;;EAIzB,OAAOC,KAAK,IAAI,CAAC,GACb;IAAM,OAAAP,KAAK;EAAL,CAAK,GACXC,OAAO,CAAC,UAACU,MAAM,EAAEC,UAAU;IACzB,IAAIC,KAAK,GAAG,CAAC;IACb,IAAIC,SAA8B;IAElC,IAAMC,WAAW,GAAG,SAAAA,CAAA;MAClBD,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEE,WAAW,EAAE;MACxBF,SAAS,GAAG,IAAI;MAChB,IAAIL,KAAK,IAAI,IAAI,EAAE;QACjB,IAAMQ,QAAQ,GAAG,OAAOR,KAAK,KAAK,QAAQ,GAAGL,KAAK,CAACK,KAAK,CAAC,GAAGN,SAAS,CAACM,KAAK,CAACI,KAAK,CAAC,CAAC;QACnF,IAAMK,oBAAkB,GAAGhB,wBAAwB,CAACU,UAAU,EAAE;UAC9DM,oBAAkB,CAACF,WAAW,EAAE;UAChCG,iBAAiB,EAAE;QACrB,CAAC,CAAC;QACFF,QAAQ,CAACG,SAAS,CAACF,oBAAkB,CAAC;OACvC,MAAM;QACLC,iBAAiB,EAAE;;IAEvB,CAAC;IAED,IAAMA,iBAAiB,GAAG,SAAAA,CAAA;MACxB,IAAIE,SAAS,GAAG,KAAK;MACrBP,SAAS,GAAGH,MAAM,CAACS,SAAS,CAC1BlB,wBAAwB,CAACU,UAAU,EAAEU,SAAS,EAAE;QAC9C,IAAI,EAAET,KAAK,GAAGN,KAAK,EAAE;UACnB,IAAIO,SAAS,EAAE;YACbC,WAAW,EAAE;WACd,MAAM;YACLM,SAAS,GAAG,IAAI;;SAEnB,MAAM;UACLT,UAAU,CAACW,QAAQ,EAAE;;MAEzB,CAAC,CAAC,CACH;MAED,IAAIF,SAAS,EAAE;QACbN,WAAW,EAAE;;IAEjB,CAAC;IAEDI,iBAAiB,EAAE;EACrB,CAAC,CAAC;AACR", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}