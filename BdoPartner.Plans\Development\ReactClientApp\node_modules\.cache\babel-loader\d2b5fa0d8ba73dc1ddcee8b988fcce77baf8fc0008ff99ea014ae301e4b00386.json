{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport { PrimeReactContext, localeOption } from 'primereact/api';\nimport { ComponentBase, useHandleStyle } from 'primereact/componentbase';\nimport { useMergeProps } from 'primereact/hooks';\nimport { classNames, ObjectUtils, IconUtils, DomHandler } from 'primereact/utils';\nimport { Button } from 'primereact/button';\nimport { PlusIcon } from 'primereact/icons/plus';\nimport { TimesIcon } from 'primereact/icons/times';\nimport { UploadIcon } from 'primereact/icons/upload';\nimport { Messages } from 'primereact/messages';\nimport { ProgressBar } from 'primereact/progressbar';\nimport { Ripple } from 'primereact/ripple';\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nfunction toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction toPropertyKey(t) {\n  var i = toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nfunction _defineProperty(e, r, t) {\n  return (r = toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nfunction _arrayLikeToArray$1(r, a) {\n  (null == a || a > r.length) && (a = r.length);\n  for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e];\n  return n;\n}\nfunction _arrayWithoutHoles(r) {\n  if (Array.isArray(r)) return _arrayLikeToArray$1(r);\n}\nfunction _iterableToArray(r) {\n  if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r);\n}\nfunction _unsupportedIterableToArray$1(r, a) {\n  if (r) {\n    if (\"string\" == typeof r) return _arrayLikeToArray$1(r, a);\n    var t = {}.toString.call(r).slice(8, -1);\n    return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray$1(r, a) : void 0;\n  }\n}\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _toConsumableArray(r) {\n  return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray$1(r) || _nonIterableSpread();\n}\nfunction _readOnlyError(r) {\n  throw new TypeError('\"' + r + '\" is read-only');\n}\nfunction _arrayWithHoles(r) {\n  if (Array.isArray(r)) return r;\n}\nfunction _iterableToArrayLimit(r, l) {\n  var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"];\n  if (null != t) {\n    var e,\n      n,\n      i,\n      u,\n      a = [],\n      f = !0,\n      o = !1;\n    try {\n      if (i = (t = t.call(r)).next, 0 === l) {\n        if (Object(t) !== t) return;\n        f = !1;\n      } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0);\n    } catch (r) {\n      o = !0, n = r;\n    } finally {\n      try {\n        if (!f && null != t[\"return\"] && (u = t[\"return\"](), Object(u) !== u)) return;\n      } finally {\n        if (o) throw n;\n      }\n    }\n    return a;\n  }\n}\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _slicedToArray(r, e) {\n  return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray$1(r, e) || _nonIterableRest();\n}\nvar classes$1 = {\n  root: function root(_ref) {\n    var props = _ref.props;\n    return classNames('p-badge p-component', _defineProperty({\n      'p-badge-no-gutter': ObjectUtils.isNotEmpty(props.value) && String(props.value).length === 1,\n      'p-badge-dot': ObjectUtils.isEmpty(props.value),\n      'p-badge-lg': props.size === 'large',\n      'p-badge-xl': props.size === 'xlarge'\n    }, \"p-badge-\".concat(props.severity), props.severity !== null));\n  }\n};\nvar styles$1 = \"\\n@layer primereact {\\n    .p-badge {\\n        display: inline-block;\\n        border-radius: 10px;\\n        text-align: center;\\n        padding: 0 .5rem;\\n    }\\n    \\n    .p-overlay-badge {\\n        position: relative;\\n    }\\n    \\n    .p-overlay-badge .p-badge {\\n        position: absolute;\\n        top: 0;\\n        right: 0;\\n        transform: translate(50%,-50%);\\n        transform-origin: 100% 0;\\n        margin: 0;\\n    }\\n    \\n    .p-badge-dot {\\n        width: .5rem;\\n        min-width: .5rem;\\n        height: .5rem;\\n        border-radius: 50%;\\n        padding: 0;\\n    }\\n    \\n    .p-badge-no-gutter {\\n        padding: 0;\\n        border-radius: 50%;\\n    }\\n}\\n\";\nvar BadgeBase = ComponentBase.extend({\n  defaultProps: {\n    __TYPE: 'Badge',\n    __parentMetadata: null,\n    value: null,\n    severity: null,\n    size: null,\n    style: null,\n    className: null,\n    children: undefined\n  },\n  css: {\n    classes: classes$1,\n    styles: styles$1\n  }\n});\nfunction ownKeys$1(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread$1(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys$1(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$1(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nvar Badge = /*#__PURE__*/React.memo(/*#__PURE__*/React.forwardRef(function (inProps, ref) {\n  var mergeProps = useMergeProps();\n  var context = React.useContext(PrimeReactContext);\n  var props = BadgeBase.getProps(inProps, context);\n  var _BadgeBase$setMetaDat = BadgeBase.setMetaData(_objectSpread$1({\n      props: props\n    }, props.__parentMetadata)),\n    ptm = _BadgeBase$setMetaDat.ptm,\n    cx = _BadgeBase$setMetaDat.cx,\n    isUnstyled = _BadgeBase$setMetaDat.isUnstyled;\n  useHandleStyle(BadgeBase.css.styles, isUnstyled, {\n    name: 'badge'\n  });\n  var elementRef = React.useRef(null);\n  React.useImperativeHandle(ref, function () {\n    return {\n      props: props,\n      getElement: function getElement() {\n        return elementRef.current;\n      }\n    };\n  });\n  var rootProps = mergeProps({\n    ref: elementRef,\n    style: props.style,\n    className: classNames(props.className, cx('root'))\n  }, BadgeBase.getOtherProps(props), ptm('root'));\n  return /*#__PURE__*/React.createElement(\"span\", rootProps, props.value);\n}));\nBadge.displayName = 'Badge';\nvar classes = {\n  root: function root(_ref) {\n    var props = _ref.props;\n    return classNames(\"p-fileupload p-fileupload-\".concat(props.mode, \" p-component\"));\n  },\n  buttonbar: 'p-fileupload-buttonbar',\n  content: 'p-fileupload-content',\n  chooseButton: function chooseButton(_ref2) {\n    var iconOnly = _ref2.iconOnly,\n      disabled = _ref2.disabled,\n      focusedState = _ref2.focusedState;\n    return classNames('p-button p-fileupload-choose p-component', {\n      'p-disabled': disabled,\n      'p-focus': focusedState,\n      'p-button-icon-only': iconOnly\n    });\n  },\n  label: 'p-button-label p-clickable',\n  file: 'p-fileupload-row',\n  fileName: 'p-fileupload-filename',\n  thumbnail: 'p-fileupload-file-thumbnail',\n  chooseButtonLabel: 'p-button-label p-clickable',\n  basicButton: function basicButton(_ref3) {\n    var disabled = _ref3.disabled,\n      focusedState = _ref3.focusedState,\n      hasFiles = _ref3.hasFiles;\n    return classNames('p-button p-component p-fileupload-choose', {\n      'p-fileupload-choose-selected': hasFiles,\n      'p-disabled': disabled,\n      'p-focus': focusedState\n    });\n  },\n  chooseIcon: function chooseIcon(_ref4) {\n    var props = _ref4.props,\n      iconOnly = _ref4.iconOnly;\n    return props.mode === 'basic' ? classNames('p-button-icon', {\n      'p-button-icon-left': !iconOnly\n    }) : classNames('p-button-icon p-clickable', {\n      'p-button-icon-left': !iconOnly\n    });\n  },\n  uploadIcon: function uploadIcon(_ref5) {\n    var iconOnly = _ref5.iconOnly;\n    return classNames('p-button-icon p-c', {\n      'p-button-icon-left': !iconOnly\n    });\n  },\n  cancelIcon: function cancelIcon(_ref6) {\n    var iconOnly = _ref6.iconOnly;\n    return classNames('p-button-icon p-c', {\n      'p-button-icon-left': !iconOnly\n    });\n  }\n};\nvar styles = \"\\n@layer primereact {\\n    .p-fileupload-content {\\n        position: relative;\\n    }\\n    \\n    .p-fileupload-row {\\n        display: flex;\\n        align-items: center;\\n    }\\n    \\n    .p-fileupload-row > div {\\n        flex: 1 1 auto;\\n        width: 25%;\\n    }\\n    \\n    .p-fileupload-row > div:last-child {\\n        text-align: right;\\n    }\\n    \\n    .p-fileupload-content > .p-progressbar {\\n        width: 100%;\\n        position: absolute;\\n        top: 0;\\n        left: 0;\\n    }\\n    \\n    .p-button.p-fileupload-choose {\\n        position: relative;\\n        overflow: hidden;\\n    }\\n    \\n    .p-fileupload-buttonbar {\\n        display: flex;\\n        flex-wrap: wrap;\\n    }\\n    \\n    .p-button.p-fileupload-choose input[type='file'] {\\n        display: none;\\n    }\\n    \\n    .p-fileupload-choose.p-fileupload-choose-selected input[type='file'] {\\n        display: none;\\n    }\\n    \\n    .p-fileupload-filename {\\n        word-break: break-all;\\n    }\\n    \\n    .p-fileupload-file-thumbnail {\\n        flex-shrink: 0;\\n    }\\n    \\n    .p-fileupload-file-badge {\\n        margin: 0.5rem;\\n    }\\n    \\n    .p-fluid .p-fileupload .p-button {\\n        width: auto;\\n    }\\n}\\n\";\nvar FileUploadBase = ComponentBase.extend({\n  defaultProps: {\n    __TYPE: 'FileUpload',\n    id: null,\n    name: null,\n    url: null,\n    mode: 'advanced',\n    multiple: false,\n    accept: null,\n    removeIcon: null,\n    disabled: false,\n    auto: false,\n    maxFileSize: null,\n    invalidFileSizeMessageSummary: '{0}: Invalid file size, ',\n    invalidFileSizeMessageDetail: 'maximum upload size is {0}.',\n    style: null,\n    className: null,\n    withCredentials: false,\n    previewWidth: 50,\n    chooseLabel: null,\n    selectedFileLabel: null,\n    uploadLabel: null,\n    cancelLabel: null,\n    chooseOptions: {\n      label: null,\n      icon: null,\n      iconOnly: false,\n      className: null,\n      style: null\n    },\n    uploadOptions: {\n      label: null,\n      icon: null,\n      iconOnly: false,\n      className: null,\n      style: null\n    },\n    cancelOptions: {\n      label: null,\n      icon: null,\n      iconOnly: false,\n      className: null,\n      style: null\n    },\n    customUpload: false,\n    headerClassName: null,\n    headerStyle: null,\n    contentClassName: null,\n    contentStyle: null,\n    headerTemplate: null,\n    itemTemplate: null,\n    emptyTemplate: null,\n    progressBarTemplate: null,\n    onBeforeUpload: null,\n    onBeforeSend: null,\n    onBeforeDrop: null,\n    onBeforeSelect: null,\n    onUpload: null,\n    onError: null,\n    onClear: null,\n    onSelect: null,\n    onProgress: null,\n    onValidationFail: null,\n    uploadHandler: null,\n    onRemove: null,\n    children: undefined\n  },\n  css: {\n    classes: classes,\n    styles: styles\n  }\n});\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _createForOfIteratorHelper(r, e) {\n  var t = \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"];\n  if (!t) {\n    if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && \"number\" == typeof r.length) {\n      t && (r = t);\n      var _n = 0,\n        F = function F() {};\n      return {\n        s: F,\n        n: function n() {\n          return _n >= r.length ? {\n            done: !0\n          } : {\n            done: !1,\n            value: r[_n++]\n          };\n        },\n        e: function e(r) {\n          throw r;\n        },\n        f: F\n      };\n    }\n    throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n  }\n  var o,\n    a = !0,\n    u = !1;\n  return {\n    s: function s() {\n      t = t.call(r);\n    },\n    n: function n() {\n      var r = t.next();\n      return a = r.done, r;\n    },\n    e: function e(r) {\n      u = !0, o = r;\n    },\n    f: function f() {\n      try {\n        a || null == t[\"return\"] || t[\"return\"]();\n      } finally {\n        if (u) throw o;\n      }\n    }\n  };\n}\nfunction _unsupportedIterableToArray(r, a) {\n  if (r) {\n    if (\"string\" == typeof r) return _arrayLikeToArray(r, a);\n    var t = {}.toString.call(r).slice(8, -1);\n    return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0;\n  }\n}\nfunction _arrayLikeToArray(r, a) {\n  (null == a || a > r.length) && (a = r.length);\n  for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e];\n  return n;\n}\nvar FileUpload = /*#__PURE__*/React.memo(/*#__PURE__*/React.forwardRef(function (inProps, ref) {\n  var mergeProps = useMergeProps();\n  var context = React.useContext(PrimeReactContext);\n  var props = FileUploadBase.getProps(inProps, context);\n  var _React$useState = React.useState([]),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    uploadedFilesState = _React$useState2[0],\n    setUploadedFilesState = _React$useState2[1];\n  var _React$useState3 = React.useState([]),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    filesState = _React$useState4[0],\n    setFilesState = _React$useState4[1];\n  var _React$useState5 = React.useState(0),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    progressState = _React$useState6[0],\n    setProgressState = _React$useState6[1];\n  var _React$useState7 = React.useState(false),\n    _React$useState8 = _slicedToArray(_React$useState7, 2),\n    focusedState = _React$useState8[0],\n    setFocusedState = _React$useState8[1];\n  var _React$useState9 = React.useState(false),\n    _React$useState10 = _slicedToArray(_React$useState9, 2),\n    uploadingState = _React$useState10[0],\n    setUploadingState = _React$useState10[1];\n  var metaData = {\n    props: props,\n    state: {\n      progress: progressState,\n      uploading: uploadingState,\n      uploadedFiles: uploadedFilesState,\n      files: filesState,\n      focused: focusedState\n    }\n  };\n  var _FileUploadBase$setMe = FileUploadBase.setMetaData(metaData),\n    ptm = _FileUploadBase$setMe.ptm,\n    cx = _FileUploadBase$setMe.cx,\n    isUnstyled = _FileUploadBase$setMe.isUnstyled;\n  useHandleStyle(FileUploadBase.css.styles, isUnstyled, {\n    name: 'fileupload'\n  });\n  var fileInputRef = React.useRef(null);\n  var messagesRef = React.useRef(null);\n  var contentRef = React.useRef(null);\n  var uploadedFileCount = React.useRef(0);\n  var hasFiles = ObjectUtils.isNotEmpty(filesState);\n  var hasUploadedFiles = ObjectUtils.isNotEmpty(uploadedFilesState);\n  var disabled = props.disabled || uploadingState;\n  var chooseButtonLabel = props.chooseLabel || props.chooseOptions.label || localeOption('choose');\n  var uploadButtonLabel = props.uploadLabel || props.uploadOptions.label || localeOption('upload');\n  var cancelButtonLabel = props.cancelLabel || props.cancelOptions.label || localeOption('cancel');\n  var chooseDisabled = disabled || props.fileLimit && props.fileLimit <= filesState.length + uploadedFileCount;\n  var uploadDisabled = disabled || !hasFiles;\n  var cancelDisabled = disabled || !hasFiles;\n  var isImage = function isImage(file) {\n    return /^image\\//.test(file.type);\n  };\n  var remove = function remove(event, index) {\n    clearInput();\n    var currentFiles = _toConsumableArray(filesState);\n    var removedFile = filesState[index];\n    currentFiles.splice(index, 1);\n    setFilesState(currentFiles);\n    if (props.onRemove) {\n      props.onRemove({\n        originalEvent: event,\n        file: removedFile\n      });\n    }\n  };\n  var removeUploadedFiles = function removeUploadedFiles(event, index) {\n    clearInput();\n    var currentUploadedFiles = _toConsumableArray(uploadedFilesState);\n    var removedFile = filesState[index];\n    currentUploadedFiles.splice(index, 1);\n    setUploadedFilesState(currentUploadedFiles);\n    if (props.onRemove) {\n      props.onRemove({\n        originalEvent: event,\n        file: removedFile\n      });\n    }\n  };\n  var clearInput = function clearInput() {\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  };\n  var formatSize = function formatSize(bytes) {\n    var k = 1024;\n    var dm = 3;\n    var sizes = localeOption('fileSizeTypes');\n    if (bytes <= 0) {\n      return \"0 \".concat(sizes[0]);\n    }\n    var i = Math.floor(Math.log(bytes) / Math.log(k));\n    var formattedSize = parseFloat((bytes / Math.pow(k, i)).toFixed(dm));\n    return \"\".concat(formattedSize, \" \").concat(sizes[i]);\n  };\n  var onFileSelect = function onFileSelect(event) {\n    // give caller a chance to stop the selection\n    if (props.onBeforeSelect && props.onBeforeSelect({\n      originalEvent: event,\n      files: filesState\n    }) === false) {\n      return;\n    }\n    var currentFiles = [];\n    if (props.multiple) {\n      currentFiles = filesState ? _toConsumableArray(filesState) : [];\n    }\n    var selectedFiles = event.dataTransfer ? event.dataTransfer.files : event.target.files;\n    for (var i = 0; i < selectedFiles.length; i++) {\n      var file = selectedFiles[i];\n      var shouldAddFile = props.multiple ? !isFileSelected(file) && validate(file) : validate(file);\n      if (shouldAddFile) {\n        file.objectURL = window.URL.createObjectURL(file);\n        currentFiles.push(file);\n      }\n    }\n    setFilesState(currentFiles);\n    if (ObjectUtils.isNotEmpty(currentFiles) && props.auto) {\n      upload(currentFiles);\n    }\n    if (props.onSelect) {\n      props.onSelect({\n        originalEvent: event,\n        files: currentFiles\n      });\n    }\n    clearInput();\n    setFocusedState(false);\n    if (props.mode === 'basic' && currentFiles.length > 0) {\n      fileInputRef.current.style.display = 'none';\n    }\n  };\n  var isFileSelected = function isFileSelected(file) {\n    return filesState.some(function (f) {\n      return f.name + f.type + f.size === file.name + file.type + file.size;\n    });\n  };\n  var validate = function validate(file) {\n    if (props.maxFileSize && file.size > props.maxFileSize) {\n      var message = {\n        severity: 'error',\n        summary: props.invalidFileSizeMessageSummary.replace('{0}', file.name),\n        detail: props.invalidFileSizeMessageDetail.replace('{0}', formatSize(props.maxFileSize)),\n        sticky: true\n      };\n      if (props.mode === 'advanced') {\n        messagesRef.current.show(message);\n      }\n      props.onValidationFail && props.onValidationFail(file);\n      return false;\n    }\n    return true;\n  };\n  var upload = function upload(files) {\n    files = files || filesState;\n    if (files && files.nativeEvent) {\n      files = filesState;\n    }\n    if (props.customUpload) {\n      if (props.fileLimit) {\n        uploadedFileCount + files.length, _readOnlyError(\"uploadedFileCount\");\n      }\n      if (props.uploadHandler) {\n        props.uploadHandler({\n          files: files,\n          options: {\n            clear: clear,\n            props: props\n          }\n        });\n      }\n    } else {\n      setUploadingState(true);\n      var xhr = new XMLHttpRequest();\n      var formData = new FormData();\n      if (props.onBeforeUpload) {\n        props.onBeforeUpload({\n          xhr: xhr,\n          formData: formData\n        });\n      }\n      var _iterator = _createForOfIteratorHelper(files),\n        _step;\n      try {\n        for (_iterator.s(); !(_step = _iterator.n()).done;) {\n          var file = _step.value;\n          formData.append(props.name, file, file.name);\n        }\n      } catch (err) {\n        _iterator.e(err);\n      } finally {\n        _iterator.f();\n      }\n      xhr.upload.addEventListener('progress', function (event) {\n        if (event.lengthComputable) {\n          var progress = Math.round(event.loaded * 100 / event.total);\n          setProgressState(progress);\n          if (props.onProgress) {\n            props.onProgress({\n              originalEvent: event,\n              progress: progress\n            });\n          }\n        }\n      });\n      xhr.onreadystatechange = function () {\n        if (xhr.readyState === 4) {\n          setProgressState(0);\n          setUploadingState(false);\n          if (xhr.status >= 200 && xhr.status < 300) {\n            if (props.fileLimit) {\n              uploadedFileCount + files.length, _readOnlyError(\"uploadedFileCount\");\n            }\n            if (props.onUpload) {\n              props.onUpload({\n                xhr: xhr,\n                files: files\n              });\n            }\n          } else if (props.onError) {\n            props.onError({\n              xhr: xhr,\n              files: files\n            });\n          }\n          clear();\n          setUploadedFilesState(function (prevUploadedFiles) {\n            return [].concat(_toConsumableArray(prevUploadedFiles), _toConsumableArray(files));\n          });\n        }\n      };\n      xhr.open('POST', props.url, true);\n      if (props.onBeforeSend) {\n        props.onBeforeSend({\n          xhr: xhr,\n          formData: formData\n        });\n      }\n      xhr.withCredentials = props.withCredentials;\n      xhr.send(formData);\n    }\n  };\n  var clear = function clear() {\n    setFilesState([]);\n    setUploadedFilesState([]);\n    setUploadingState(false);\n    props.onClear && props.onClear();\n    clearInput();\n  };\n  var choose = function choose() {\n    fileInputRef.current.click();\n  };\n  var onFocus = function onFocus() {\n    setFocusedState(true);\n  };\n  var onBlur = function onBlur() {\n    setFocusedState(false);\n  };\n  var _onKeyDown = function onKeyDown(event) {\n    if (event.code === 'Enter' || event.code === 'NumpadEnter') {\n      choose();\n    }\n  };\n  var _onDragEnter = function onDragEnter(event) {\n    if (!disabled) {\n      event.dataTransfer.dropEffect = 'copy';\n      event.stopPropagation();\n      event.preventDefault();\n    }\n  };\n  var _onDragOver = function onDragOver(event) {\n    if (!disabled) {\n      event.dataTransfer.dropEffect = 'copy';\n      !isUnstyled() && DomHandler.addClass(contentRef.current, 'p-fileupload-highlight');\n      contentRef.current.setAttribute('data-p-highlight', true);\n      event.stopPropagation();\n      event.preventDefault();\n    }\n  };\n  var _onDragLeave = function onDragLeave(event) {\n    if (!disabled) {\n      event.dataTransfer.dropEffect = 'copy';\n      !isUnstyled() && DomHandler.removeClass(contentRef.current, 'p-fileupload-highlight');\n      contentRef.current.setAttribute('data-p-highlight', false);\n    }\n  };\n  var _onDrop = function onDrop(event) {\n    if (props.disabled) {\n      return;\n    }\n    !isUnstyled() && DomHandler.removeClass(contentRef.current, 'p-fileupload-highlight');\n    contentRef.current.setAttribute('data-p-highlight', false);\n    event.stopPropagation();\n    event.preventDefault();\n\n    // give caller a chance to stop the drop\n    if (props.onBeforeDrop && props.onBeforeDrop(event) === false) {\n      return;\n    }\n    var files = event.dataTransfer ? event.dataTransfer.files : event.target.files;\n    var allowDrop = props.multiple || ObjectUtils.isEmpty(filesState) && files && files.length === 1;\n    allowDrop && onFileSelect(event);\n  };\n  var onSimpleUploaderClick = function onSimpleUploaderClick() {\n    !disabled && hasFiles ? upload() : fileInputRef.current.click();\n  };\n  React.useImperativeHandle(ref, function () {\n    return {\n      props: props,\n      upload: upload,\n      clear: clear,\n      formatSize: formatSize,\n      onFileSelect: onFileSelect,\n      getInput: function getInput() {\n        return fileInputRef.current;\n      },\n      getContent: function getContent() {\n        return contentRef.current;\n      },\n      getFiles: function getFiles() {\n        return filesState;\n      },\n      setFiles: function setFiles(files) {\n        return setFilesState(files || []);\n      },\n      getUploadedFiles: function getUploadedFiles() {\n        return uploadedFilesState;\n      },\n      setUploadedFiles: function setUploadedFiles(files) {\n        return setUploadedFilesState(files || []);\n      }\n    };\n  });\n  var createChooseButton = function createChooseButton() {\n    var _props$chooseOptions = props.chooseOptions,\n      className = _props$chooseOptions.className,\n      style = _props$chooseOptions.style,\n      _icon = _props$chooseOptions.icon,\n      iconOnly = _props$chooseOptions.iconOnly;\n    var chooseButtonLabelProps = mergeProps({\n      className: cx('chooseButtonLabel')\n    }, ptm('chooseButtonLabel'));\n    var label = iconOnly ? /*#__PURE__*/React.createElement(\"span\", _extends({}, chooseButtonLabelProps, {\n      dangerouslySetInnerHTML: {\n        __html: '&nbsp;'\n      }\n    })) : /*#__PURE__*/React.createElement(\"span\", chooseButtonLabelProps, chooseButtonLabel);\n    var inputProps = mergeProps({\n      ref: fileInputRef,\n      type: 'file',\n      onChange: function onChange(e) {\n        return onFileSelect(e);\n      },\n      multiple: props.multiple,\n      accept: props.accept,\n      disabled: chooseDisabled\n    }, ptm('input'));\n    var input = /*#__PURE__*/React.createElement(\"input\", inputProps);\n    var chooseIconProps = mergeProps({\n      className: cx('chooseIcon', {\n        iconOnly: iconOnly\n      }),\n      'aria-hidden': 'true'\n    }, ptm('chooseIcon'));\n    var icon = _icon || /*#__PURE__*/React.createElement(PlusIcon, chooseIconProps);\n    var chooseIcon = IconUtils.getJSXIcon(icon, _objectSpread({}, chooseIconProps), {\n      props: props\n    });\n    var chooseButtonProps = mergeProps({\n      className: classNames(className, cx('chooseButton', {\n        iconOnly: iconOnly,\n        disabled: disabled,\n        className: className,\n        focusedState: focusedState\n      })),\n      style: style,\n      onClick: choose,\n      onKeyDown: function onKeyDown(e) {\n        return _onKeyDown(e);\n      },\n      onFocus: onFocus,\n      onBlur: onBlur,\n      tabIndex: 0,\n      'data-p-disabled': disabled,\n      'data-p-focus': focusedState\n    }, ptm('chooseButton'));\n    return /*#__PURE__*/React.createElement(\"span\", chooseButtonProps, input, chooseIcon, label, /*#__PURE__*/React.createElement(Ripple, null));\n  };\n  var onRemoveClick = function onRemoveClick(e, badgeOptions, index) {\n    if (badgeOptions.severity === 'warning') {\n      remove(e, index);\n    } else {\n      removeUploadedFiles(e, index);\n    }\n  };\n  var createFile = function createFile(file, index, badgeOptions) {\n    var key = file.name + file.type + file.size;\n    var thumbnailProps = mergeProps({\n      role: 'presentation',\n      className: cx('thumbnail'),\n      src: file.objectURL,\n      width: props.previewWidth\n    }, ptm('thumbnail'));\n    var preview = isImage(file) ? /*#__PURE__*/React.createElement(\"img\", _extends({}, thumbnailProps, {\n      alt: file.name\n    })) : null;\n    var detailsProps = mergeProps(ptm('details'));\n    var fileSizeProps = mergeProps(ptm('fileSize'));\n    var fileNameProps = mergeProps({\n      className: cx('fileName')\n    }, ptm('fileName'));\n    var actionsProps = mergeProps(ptm('actions'));\n    var fileName = /*#__PURE__*/React.createElement(\"div\", fileNameProps, file.name);\n    var size = /*#__PURE__*/React.createElement(\"div\", fileSizeProps, formatSize(file.size));\n    var contentBody = /*#__PURE__*/React.createElement(\"div\", detailsProps, /*#__PURE__*/React.createElement(\"div\", fileNameProps, \" \", file.name), /*#__PURE__*/React.createElement(\"span\", fileSizeProps, formatSize(file.size)), /*#__PURE__*/React.createElement(Badge, {\n      className: \"p-fileupload-file-badge\",\n      value: badgeOptions.value,\n      severity: badgeOptions.severity,\n      pt: ptm('badge'),\n      __parentMetadata: {\n        parent: metaData\n      }\n    }));\n    var removeButton = /*#__PURE__*/React.createElement(\"div\", actionsProps, /*#__PURE__*/React.createElement(Button, {\n      type: \"button\",\n      icon: props.removeIcon || /*#__PURE__*/React.createElement(TimesIcon, null),\n      text: true,\n      rounded: true,\n      severity: \"danger\",\n      onClick: function onClick(e) {\n        return onRemoveClick(e, badgeOptions, index);\n      },\n      disabled: disabled,\n      pt: ptm('removeButton'),\n      __parentMetadata: {\n        parent: metaData\n      },\n      unstyled: isUnstyled()\n    }));\n    var content = /*#__PURE__*/React.createElement(React.Fragment, null, preview, contentBody, removeButton);\n    if (props.itemTemplate) {\n      var defaultContentOptions = {\n        onRemove: function onRemove(event) {\n          return remove(event, index);\n        },\n        previewElement: preview,\n        fileNameElement: fileName,\n        sizeElement: size,\n        removeElement: removeButton,\n        formatSize: formatSize(file.size),\n        element: content,\n        index: index,\n        props: props\n      };\n      content = ObjectUtils.getJSXElement(props.itemTemplate, file, defaultContentOptions);\n    }\n    var fileProps = mergeProps({\n      key: key,\n      className: cx('file')\n    }, ptm('file'));\n    return /*#__PURE__*/React.createElement(\"div\", fileProps, content);\n  };\n  var createFiles = function createFiles() {\n    var badgeOptions = {\n      severity: 'warning',\n      value: localeOption('pending') || 'Pending'\n    };\n    var content = filesState.map(function (file, index) {\n      return createFile(file, index, badgeOptions);\n    });\n    return /*#__PURE__*/React.createElement(\"div\", null, content);\n  };\n  var createUploadedFiles = function createUploadedFiles() {\n    var badgeOptions = {\n      severity: 'success',\n      value: localeOption('completed') || 'Completed'\n    };\n    var content = uploadedFilesState && uploadedFilesState.map(function (file, index) {\n      return createFile(file, index, badgeOptions);\n    });\n    return /*#__PURE__*/React.createElement(\"div\", null, content);\n  };\n  var createEmptyContent = function createEmptyContent() {\n    return props.emptyTemplate && !hasFiles && !hasUploadedFiles ? ObjectUtils.getJSXElement(props.emptyTemplate, props) : null;\n  };\n  var createProgressBarContent = function createProgressBarContent() {\n    if (props.progressBarTemplate) {\n      var defaultProgressBarTemplateOptions = {\n        progress: progressState,\n        props: props\n      };\n      return ObjectUtils.getJSXElement(props.progressBarTemplate, defaultProgressBarTemplateOptions);\n    }\n    return /*#__PURE__*/React.createElement(ProgressBar, {\n      value: progressState,\n      showValue: false,\n      pt: ptm('progressbar'),\n      __parentMetadata: {\n        parent: metaData\n      }\n    });\n  };\n  var createAdvanced = function createAdvanced() {\n    var chooseButton = createChooseButton();\n    var emptyContent = createEmptyContent();\n    var uploadButton;\n    var cancelButton;\n    var filesList;\n    var uplaodedFilesList;\n    var progressBar;\n    if (!props.auto) {\n      var uploadOptions = props.uploadOptions;\n      var cancelOptions = props.cancelOptions;\n      var uploadLabel = !uploadOptions.iconOnly ? uploadButtonLabel : '';\n      var cancelLabel = !cancelOptions.iconOnly ? cancelButtonLabel : '';\n      var uploadIconProps = mergeProps({\n        className: cx('uploadIcon', {\n          iconOnly: uploadOptions.iconOnly\n        }),\n        'aria-hidden': 'true'\n      }, ptm('uploadIcon'));\n      var uploadIcon = IconUtils.getJSXIcon(uploadOptions.icon || /*#__PURE__*/React.createElement(UploadIcon, uploadIconProps), _objectSpread({}, uploadIconProps), {\n        props: props\n      });\n      var cancelIconProps = mergeProps({\n        className: cx('cancelIcon', {\n          iconOnly: cancelOptions.iconOnly\n        }),\n        'aria-hidden': 'true'\n      }, ptm('cancelIcon'));\n      var cancelIcon = IconUtils.getJSXIcon(cancelOptions.icon || /*#__PURE__*/React.createElement(TimesIcon, cancelIconProps), _objectSpread({}, cancelIconProps), {\n        props: props\n      });\n      uploadButton = /*#__PURE__*/React.createElement(Button, {\n        type: \"button\",\n        label: uploadLabel,\n        \"aria-hidden\": \"true\",\n        icon: uploadIcon,\n        onClick: upload,\n        disabled: uploadDisabled,\n        style: uploadOptions.style,\n        className: uploadOptions.className,\n        pt: ptm('uploadButton'),\n        __parentMetadata: {\n          parent: metaData\n        },\n        unstyled: isUnstyled()\n      });\n      cancelButton = /*#__PURE__*/React.createElement(Button, {\n        type: \"button\",\n        label: cancelLabel,\n        \"aria-hidden\": \"true\",\n        icon: cancelIcon,\n        onClick: clear,\n        disabled: cancelDisabled,\n        style: cancelOptions.style,\n        className: cancelOptions.className,\n        pt: ptm('cancelButton'),\n        __parentMetadata: {\n          parent: metaData\n        },\n        unstyled: isUnstyled()\n      });\n    }\n    if (hasFiles) {\n      filesList = createFiles();\n      progressBar = createProgressBarContent();\n    }\n    if (hasUploadedFiles) {\n      uplaodedFilesList = createUploadedFiles();\n    }\n    var buttonbarProps = mergeProps({\n      className: classNames(props.headerClassName, cx('buttonbar')),\n      style: props.headerStyle\n    }, ptm('buttonbar'));\n    var header = /*#__PURE__*/React.createElement(\"div\", buttonbarProps, chooseButton, uploadButton, cancelButton);\n    if (props.headerTemplate) {\n      var defaultContentOptions = {\n        className: classNames('p-fileupload-buttonbar', props.headerClassName),\n        chooseButton: chooseButton,\n        uploadButton: uploadButton,\n        cancelButton: cancelButton,\n        element: header,\n        props: props\n      };\n      header = ObjectUtils.getJSXElement(props.headerTemplate, defaultContentOptions);\n    }\n    var rootProps = mergeProps({\n      id: props.id,\n      className: classNames(props.className, cx('root')),\n      style: props.style\n    }, FileUploadBase.getOtherProps(props), ptm('root'));\n    var contentProps = mergeProps({\n      ref: contentRef,\n      className: classNames(props.contentClassName, cx('content')),\n      style: props.contentStyle,\n      onDragEnter: function onDragEnter(e) {\n        return _onDragEnter(e);\n      },\n      onDragOver: function onDragOver(e) {\n        return _onDragOver(e);\n      },\n      onDragLeave: function onDragLeave(e) {\n        return _onDragLeave(e);\n      },\n      onDrop: function onDrop(e) {\n        return _onDrop(e);\n      },\n      'data-p-highlight': false\n    }, ptm('content'));\n    return /*#__PURE__*/React.createElement(\"div\", rootProps, header, /*#__PURE__*/React.createElement(\"div\", contentProps, progressBar, /*#__PURE__*/React.createElement(Messages, {\n      ref: messagesRef,\n      __parentMetadata: {\n        parent: metaData\n      }\n    }), hasFiles ? filesList : null, hasUploadedFiles ? uplaodedFilesList : null, emptyContent));\n  };\n  var createBasic = function createBasic() {\n    var chooseOptions = props.chooseOptions;\n    var labelProps = mergeProps({\n      className: cx('label')\n    }, ptm('label'));\n    var chooseLabel = chooseOptions.iconOnly ? /*#__PURE__*/React.createElement(\"span\", _extends({}, labelProps, {\n      dangerouslySetInnerHTML: {\n        __html: '&nbsp;'\n      }\n    })) : /*#__PURE__*/React.createElement(\"span\", labelProps, chooseButtonLabel);\n    var label = props.auto ? chooseLabel : /*#__PURE__*/React.createElement(\"span\", labelProps, hasFiles ? props.selectedFileLabel || filesState[0].name : chooseLabel);\n    var chooseIconProps = mergeProps({\n      className: cx('chooseIcon', {\n        iconOnly: chooseOptions.iconOnly\n      })\n    }, ptm('chooseIcon'));\n    var icon = chooseOptions.icon ? chooseOptions.icon : !chooseOptions.icon && (!hasFiles || props.auto) ? /*#__PURE__*/React.createElement(PlusIcon, chooseIconProps) : !chooseOptions.icon && hasFiles && !props.auto && /*#__PURE__*/React.createElement(UploadIcon, chooseIconProps);\n    var chooseIcon = IconUtils.getJSXIcon(icon, _objectSpread({}, chooseIconProps), {\n      props: props,\n      hasFiles: hasFiles\n    });\n    var inputProps = mergeProps({\n      ref: fileInputRef,\n      type: 'file',\n      onChange: function onChange(e) {\n        return onFileSelect(e);\n      },\n      multiple: props.multiple,\n      accept: props.accept,\n      disabled: disabled\n    }, ptm('input'));\n    var input = !hasFiles && /*#__PURE__*/React.createElement(\"input\", inputProps);\n    var rootProps = mergeProps({\n      className: classNames(props.className, cx('root')),\n      style: props.style\n    }, FileUploadBase.getOtherProps(props), ptm('root'));\n    var basicButtonProps = mergeProps({\n      className: classNames(chooseOptions.className, cx('basicButton', {\n        hasFiles: hasFiles,\n        disabled: disabled,\n        focusedState: focusedState\n      })),\n      style: chooseOptions.style,\n      tabIndex: 0,\n      onClick: onSimpleUploaderClick,\n      onKeyDown: function onKeyDown(e) {\n        return _onKeyDown(e);\n      },\n      onFocus: onFocus,\n      onBlur: onBlur\n    }, FileUploadBase.getOtherProps(props), ptm('basicButton'));\n    return /*#__PURE__*/React.createElement(\"div\", rootProps, /*#__PURE__*/React.createElement(Messages, {\n      ref: messagesRef,\n      pt: ptm('message'),\n      __parentMetadata: {\n        parent: metaData\n      }\n    }), /*#__PURE__*/React.createElement(\"span\", basicButtonProps, chooseIcon, label, input, /*#__PURE__*/React.createElement(Ripple, null)));\n  };\n  if (props.mode === 'advanced') {\n    return createAdvanced();\n  } else if (props.mode === 'basic') {\n    return createBasic();\n  }\n}));\nFileUpload.displayName = 'FileUpload';\nexport { FileUpload };", "map": {"version": 3, "names": ["React", "PrimeReactContext", "localeOption", "ComponentBase", "useHandleStyle", "useMergeProps", "classNames", "ObjectUtils", "IconUtils", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "PlusIcon", "TimesIcon", "UploadIcon", "Messages", "ProgressBar", "<PERSON><PERSON><PERSON>", "_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "toPrimitive", "t", "r", "e", "i", "call", "TypeError", "String", "Number", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_defineProperty", "Object", "defineProperty", "value", "enumerable", "configurable", "writable", "_extends", "assign", "bind", "n", "arguments", "length", "hasOwnProperty", "apply", "_arrayLikeToArray$1", "a", "Array", "_arrayWithoutHoles", "isArray", "_iterableToArray", "from", "_unsupportedIterableToArray$1", "toString", "slice", "name", "test", "_nonIterableSpread", "_toConsumableArray", "_readOnly<PERSON><PERSON>r", "_arrayWithHoles", "_iterableToArrayLimit", "l", "u", "f", "next", "done", "push", "_nonIterableRest", "_slicedToArray", "classes$1", "root", "_ref", "props", "isNotEmpty", "isEmpty", "size", "concat", "severity", "styles$1", "BadgeBase", "extend", "defaultProps", "__TYPE", "__parentMetadata", "style", "className", "children", "undefined", "css", "classes", "styles", "ownKeys$1", "keys", "getOwnPropertySymbols", "filter", "getOwnPropertyDescriptor", "_objectSpread$1", "for<PERSON>ach", "getOwnPropertyDescriptors", "defineProperties", "Badge", "memo", "forwardRef", "inProps", "ref", "mergeProps", "context", "useContext", "getProps", "_BadgeBase$setMetaDat", "setMetaData", "ptm", "cx", "isUnstyled", "elementRef", "useRef", "useImperativeHandle", "getElement", "current", "rootProps", "getOtherProps", "createElement", "displayName", "mode", "buttonbar", "content", "chooseButton", "_ref2", "iconOnly", "disabled", "focusedState", "label", "file", "fileName", "thumbnail", "chooseButtonLabel", "basicButton", "_ref3", "hasFiles", "chooseIcon", "_ref4", "uploadIcon", "_ref5", "cancelIcon", "_ref6", "FileUploadBase", "id", "url", "multiple", "accept", "removeIcon", "auto", "maxFileSize", "invalidFileSizeMessageSummary", "invalidFileSizeMessageDetail", "withCredentials", "previewWidth", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uploadLabel", "cancelLabel", "chooseOptions", "icon", "uploadOptions", "cancelOptions", "customUpload", "headerClassName", "headerStyle", "contentClassName", "contentStyle", "headerTemplate", "itemTemplate", "emptyTemplate", "progressBarTemplate", "onBeforeUpload", "onBeforeSend", "onBeforeDrop", "onBeforeSelect", "onUpload", "onError", "onClear", "onSelect", "onProgress", "onValidationFail", "uploadHandler", "onRemove", "ownKeys", "_objectSpread", "_createForOfIteratorHelper", "_unsupportedIterableToArray", "_n", "F", "s", "_arrayLikeToArray", "FileUpload", "_React$useState", "useState", "_React$useState2", "uploadedFilesState", "setUploadedFilesState", "_React$useState3", "_React$useState4", "filesState", "setFilesState", "_React$useState5", "_React$useState6", "progressState", "setProgressState", "_React$useState7", "_React$useState8", "setFocusedState", "_React$useState9", "_React$useState10", "uploadingState", "setUploadingState", "metaData", "state", "progress", "uploading", "uploadedFiles", "files", "focused", "_FileUploadBase$setMe", "fileInputRef", "messagesRef", "contentRef", "uploadedFileCount", "hasUploadedFiles", "uploadButtonLabel", "cancelButtonLabel", "chooseDisabled", "fileLimit", "uploadDisabled", "cancelDisabled", "isImage", "type", "remove", "event", "index", "clearInput", "currentFiles", "removedFile", "splice", "originalEvent", "removeUploadedFiles", "currentUploadedFiles", "formatSize", "bytes", "k", "dm", "sizes", "Math", "floor", "log", "formattedSize", "parseFloat", "pow", "toFixed", "onFileSelect", "selectedFiles", "dataTransfer", "target", "shouldAddFile", "isFileSelected", "validate", "objectURL", "window", "URL", "createObjectURL", "upload", "display", "some", "message", "summary", "replace", "detail", "sticky", "show", "nativeEvent", "options", "clear", "xhr", "XMLHttpRequest", "formData", "FormData", "_iterator", "_step", "append", "err", "addEventListener", "lengthComputable", "round", "loaded", "total", "onreadystatechange", "readyState", "status", "prevUploadedFiles", "open", "send", "choose", "click", "onFocus", "onBlur", "_onKeyDown", "onKeyDown", "code", "_onDragEnter", "onDragEnter", "dropEffect", "stopPropagation", "preventDefault", "_onDragOver", "onDragOver", "addClass", "setAttribute", "_onDragLeave", "onDragLeave", "removeClass", "_onDrop", "onDrop", "allowDrop", "onSimpleUploaderClick", "getInput", "get<PERSON>ontent", "getFiles", "setFiles", "getUploadedFiles", "setUploadedFiles", "createChooseButton", "_props$chooseOptions", "_icon", "chooseButtonLabelProps", "dangerouslySetInnerHTML", "__html", "inputProps", "onChange", "input", "chooseIconProps", "getJSXIcon", "chooseButtonProps", "onClick", "tabIndex", "onRemoveClick", "badgeOptions", "createFile", "key", "thumbnailProps", "role", "src", "width", "preview", "alt", "detailsProps", "fileSizeProps", "fileNameProps", "actionsProps", "contentBody", "pt", "parent", "removeButton", "text", "rounded", "unstyled", "Fragment", "defaultContentOptions", "previewElement", "fileNameElement", "sizeElement", "removeElement", "element", "getJSXElement", "fileProps", "createFiles", "map", "createUploadedFiles", "createEmptyContent", "createProgressBarContent", "defaultProgressBarTemplateOptions", "showValue", "createAdvanced", "emptyContent", "uploadButton", "cancelButton", "filesList", "uplaodedFilesList", "progressBar", "uploadIconProps", "cancelIconProps", "buttonbarProps", "header", "contentProps", "createBasic", "labelProps", "basicButtonProps"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/node_modules/primereact/fileupload/fileupload.esm.js"], "sourcesContent": ["'use client';\nimport * as React from 'react';\nimport { PrimeReactContext, localeOption } from 'primereact/api';\nimport { ComponentBase, useHandleStyle } from 'primereact/componentbase';\nimport { useMergeProps } from 'primereact/hooks';\nimport { classNames, ObjectUtils, IconUtils, DomHandler } from 'primereact/utils';\nimport { Button } from 'primereact/button';\nimport { PlusIcon } from 'primereact/icons/plus';\nimport { TimesIcon } from 'primereact/icons/times';\nimport { UploadIcon } from 'primereact/icons/upload';\nimport { Messages } from 'primereact/messages';\nimport { ProgressBar } from 'primereact/progressbar';\nimport { Ripple } from 'primereact/ripple';\n\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\n\nfunction toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\n\nfunction toPropertyKey(t) {\n  var i = toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\n\nfunction _defineProperty(e, r, t) {\n  return (r = toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\n\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\n\nfunction _arrayLikeToArray$1(r, a) {\n  (null == a || a > r.length) && (a = r.length);\n  for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e];\n  return n;\n}\n\nfunction _arrayWithoutHoles(r) {\n  if (Array.isArray(r)) return _arrayLikeToArray$1(r);\n}\n\nfunction _iterableToArray(r) {\n  if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r);\n}\n\nfunction _unsupportedIterableToArray$1(r, a) {\n  if (r) {\n    if (\"string\" == typeof r) return _arrayLikeToArray$1(r, a);\n    var t = {}.toString.call(r).slice(8, -1);\n    return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray$1(r, a) : void 0;\n  }\n}\n\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nfunction _toConsumableArray(r) {\n  return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray$1(r) || _nonIterableSpread();\n}\n\nfunction _readOnlyError(r) {\n  throw new TypeError('\"' + r + '\" is read-only');\n}\n\nfunction _arrayWithHoles(r) {\n  if (Array.isArray(r)) return r;\n}\n\nfunction _iterableToArrayLimit(r, l) {\n  var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"];\n  if (null != t) {\n    var e,\n      n,\n      i,\n      u,\n      a = [],\n      f = !0,\n      o = !1;\n    try {\n      if (i = (t = t.call(r)).next, 0 === l) {\n        if (Object(t) !== t) return;\n        f = !1;\n      } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0);\n    } catch (r) {\n      o = !0, n = r;\n    } finally {\n      try {\n        if (!f && null != t[\"return\"] && (u = t[\"return\"](), Object(u) !== u)) return;\n      } finally {\n        if (o) throw n;\n      }\n    }\n    return a;\n  }\n}\n\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nfunction _slicedToArray(r, e) {\n  return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray$1(r, e) || _nonIterableRest();\n}\n\nvar classes$1 = {\n  root: function root(_ref) {\n    var props = _ref.props;\n    return classNames('p-badge p-component', _defineProperty({\n      'p-badge-no-gutter': ObjectUtils.isNotEmpty(props.value) && String(props.value).length === 1,\n      'p-badge-dot': ObjectUtils.isEmpty(props.value),\n      'p-badge-lg': props.size === 'large',\n      'p-badge-xl': props.size === 'xlarge'\n    }, \"p-badge-\".concat(props.severity), props.severity !== null));\n  }\n};\nvar styles$1 = \"\\n@layer primereact {\\n    .p-badge {\\n        display: inline-block;\\n        border-radius: 10px;\\n        text-align: center;\\n        padding: 0 .5rem;\\n    }\\n    \\n    .p-overlay-badge {\\n        position: relative;\\n    }\\n    \\n    .p-overlay-badge .p-badge {\\n        position: absolute;\\n        top: 0;\\n        right: 0;\\n        transform: translate(50%,-50%);\\n        transform-origin: 100% 0;\\n        margin: 0;\\n    }\\n    \\n    .p-badge-dot {\\n        width: .5rem;\\n        min-width: .5rem;\\n        height: .5rem;\\n        border-radius: 50%;\\n        padding: 0;\\n    }\\n    \\n    .p-badge-no-gutter {\\n        padding: 0;\\n        border-radius: 50%;\\n    }\\n}\\n\";\nvar BadgeBase = ComponentBase.extend({\n  defaultProps: {\n    __TYPE: 'Badge',\n    __parentMetadata: null,\n    value: null,\n    severity: null,\n    size: null,\n    style: null,\n    className: null,\n    children: undefined\n  },\n  css: {\n    classes: classes$1,\n    styles: styles$1\n  }\n});\n\nfunction ownKeys$1(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread$1(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys$1(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$1(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nvar Badge = /*#__PURE__*/React.memo(/*#__PURE__*/React.forwardRef(function (inProps, ref) {\n  var mergeProps = useMergeProps();\n  var context = React.useContext(PrimeReactContext);\n  var props = BadgeBase.getProps(inProps, context);\n  var _BadgeBase$setMetaDat = BadgeBase.setMetaData(_objectSpread$1({\n      props: props\n    }, props.__parentMetadata)),\n    ptm = _BadgeBase$setMetaDat.ptm,\n    cx = _BadgeBase$setMetaDat.cx,\n    isUnstyled = _BadgeBase$setMetaDat.isUnstyled;\n  useHandleStyle(BadgeBase.css.styles, isUnstyled, {\n    name: 'badge'\n  });\n  var elementRef = React.useRef(null);\n  React.useImperativeHandle(ref, function () {\n    return {\n      props: props,\n      getElement: function getElement() {\n        return elementRef.current;\n      }\n    };\n  });\n  var rootProps = mergeProps({\n    ref: elementRef,\n    style: props.style,\n    className: classNames(props.className, cx('root'))\n  }, BadgeBase.getOtherProps(props), ptm('root'));\n  return /*#__PURE__*/React.createElement(\"span\", rootProps, props.value);\n}));\nBadge.displayName = 'Badge';\n\nvar classes = {\n  root: function root(_ref) {\n    var props = _ref.props;\n    return classNames(\"p-fileupload p-fileupload-\".concat(props.mode, \" p-component\"));\n  },\n  buttonbar: 'p-fileupload-buttonbar',\n  content: 'p-fileupload-content',\n  chooseButton: function chooseButton(_ref2) {\n    var iconOnly = _ref2.iconOnly,\n      disabled = _ref2.disabled,\n      focusedState = _ref2.focusedState;\n    return classNames('p-button p-fileupload-choose p-component', {\n      'p-disabled': disabled,\n      'p-focus': focusedState,\n      'p-button-icon-only': iconOnly\n    });\n  },\n  label: 'p-button-label p-clickable',\n  file: 'p-fileupload-row',\n  fileName: 'p-fileupload-filename',\n  thumbnail: 'p-fileupload-file-thumbnail',\n  chooseButtonLabel: 'p-button-label p-clickable',\n  basicButton: function basicButton(_ref3) {\n    var disabled = _ref3.disabled,\n      focusedState = _ref3.focusedState,\n      hasFiles = _ref3.hasFiles;\n    return classNames('p-button p-component p-fileupload-choose', {\n      'p-fileupload-choose-selected': hasFiles,\n      'p-disabled': disabled,\n      'p-focus': focusedState\n    });\n  },\n  chooseIcon: function chooseIcon(_ref4) {\n    var props = _ref4.props,\n      iconOnly = _ref4.iconOnly;\n    return props.mode === 'basic' ? classNames('p-button-icon', {\n      'p-button-icon-left': !iconOnly\n    }) : classNames('p-button-icon p-clickable', {\n      'p-button-icon-left': !iconOnly\n    });\n  },\n  uploadIcon: function uploadIcon(_ref5) {\n    var iconOnly = _ref5.iconOnly;\n    return classNames('p-button-icon p-c', {\n      'p-button-icon-left': !iconOnly\n    });\n  },\n  cancelIcon: function cancelIcon(_ref6) {\n    var iconOnly = _ref6.iconOnly;\n    return classNames('p-button-icon p-c', {\n      'p-button-icon-left': !iconOnly\n    });\n  }\n};\nvar styles = \"\\n@layer primereact {\\n    .p-fileupload-content {\\n        position: relative;\\n    }\\n    \\n    .p-fileupload-row {\\n        display: flex;\\n        align-items: center;\\n    }\\n    \\n    .p-fileupload-row > div {\\n        flex: 1 1 auto;\\n        width: 25%;\\n    }\\n    \\n    .p-fileupload-row > div:last-child {\\n        text-align: right;\\n    }\\n    \\n    .p-fileupload-content > .p-progressbar {\\n        width: 100%;\\n        position: absolute;\\n        top: 0;\\n        left: 0;\\n    }\\n    \\n    .p-button.p-fileupload-choose {\\n        position: relative;\\n        overflow: hidden;\\n    }\\n    \\n    .p-fileupload-buttonbar {\\n        display: flex;\\n        flex-wrap: wrap;\\n    }\\n    \\n    .p-button.p-fileupload-choose input[type='file'] {\\n        display: none;\\n    }\\n    \\n    .p-fileupload-choose.p-fileupload-choose-selected input[type='file'] {\\n        display: none;\\n    }\\n    \\n    .p-fileupload-filename {\\n        word-break: break-all;\\n    }\\n    \\n    .p-fileupload-file-thumbnail {\\n        flex-shrink: 0;\\n    }\\n    \\n    .p-fileupload-file-badge {\\n        margin: 0.5rem;\\n    }\\n    \\n    .p-fluid .p-fileupload .p-button {\\n        width: auto;\\n    }\\n}\\n\";\nvar FileUploadBase = ComponentBase.extend({\n  defaultProps: {\n    __TYPE: 'FileUpload',\n    id: null,\n    name: null,\n    url: null,\n    mode: 'advanced',\n    multiple: false,\n    accept: null,\n    removeIcon: null,\n    disabled: false,\n    auto: false,\n    maxFileSize: null,\n    invalidFileSizeMessageSummary: '{0}: Invalid file size, ',\n    invalidFileSizeMessageDetail: 'maximum upload size is {0}.',\n    style: null,\n    className: null,\n    withCredentials: false,\n    previewWidth: 50,\n    chooseLabel: null,\n    selectedFileLabel: null,\n    uploadLabel: null,\n    cancelLabel: null,\n    chooseOptions: {\n      label: null,\n      icon: null,\n      iconOnly: false,\n      className: null,\n      style: null\n    },\n    uploadOptions: {\n      label: null,\n      icon: null,\n      iconOnly: false,\n      className: null,\n      style: null\n    },\n    cancelOptions: {\n      label: null,\n      icon: null,\n      iconOnly: false,\n      className: null,\n      style: null\n    },\n    customUpload: false,\n    headerClassName: null,\n    headerStyle: null,\n    contentClassName: null,\n    contentStyle: null,\n    headerTemplate: null,\n    itemTemplate: null,\n    emptyTemplate: null,\n    progressBarTemplate: null,\n    onBeforeUpload: null,\n    onBeforeSend: null,\n    onBeforeDrop: null,\n    onBeforeSelect: null,\n    onUpload: null,\n    onError: null,\n    onClear: null,\n    onSelect: null,\n    onProgress: null,\n    onValidationFail: null,\n    uploadHandler: null,\n    onRemove: null,\n    children: undefined\n  },\n  css: {\n    classes: classes,\n    styles: styles\n  }\n});\n\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _createForOfIteratorHelper(r, e) { var t = \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && \"number\" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t[\"return\"] || t[\"return\"](); } finally { if (u) throw o; } } }; }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nvar FileUpload = /*#__PURE__*/React.memo(/*#__PURE__*/React.forwardRef(function (inProps, ref) {\n  var mergeProps = useMergeProps();\n  var context = React.useContext(PrimeReactContext);\n  var props = FileUploadBase.getProps(inProps, context);\n  var _React$useState = React.useState([]),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    uploadedFilesState = _React$useState2[0],\n    setUploadedFilesState = _React$useState2[1];\n  var _React$useState3 = React.useState([]),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    filesState = _React$useState4[0],\n    setFilesState = _React$useState4[1];\n  var _React$useState5 = React.useState(0),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    progressState = _React$useState6[0],\n    setProgressState = _React$useState6[1];\n  var _React$useState7 = React.useState(false),\n    _React$useState8 = _slicedToArray(_React$useState7, 2),\n    focusedState = _React$useState8[0],\n    setFocusedState = _React$useState8[1];\n  var _React$useState9 = React.useState(false),\n    _React$useState10 = _slicedToArray(_React$useState9, 2),\n    uploadingState = _React$useState10[0],\n    setUploadingState = _React$useState10[1];\n  var metaData = {\n    props: props,\n    state: {\n      progress: progressState,\n      uploading: uploadingState,\n      uploadedFiles: uploadedFilesState,\n      files: filesState,\n      focused: focusedState\n    }\n  };\n  var _FileUploadBase$setMe = FileUploadBase.setMetaData(metaData),\n    ptm = _FileUploadBase$setMe.ptm,\n    cx = _FileUploadBase$setMe.cx,\n    isUnstyled = _FileUploadBase$setMe.isUnstyled;\n  useHandleStyle(FileUploadBase.css.styles, isUnstyled, {\n    name: 'fileupload'\n  });\n  var fileInputRef = React.useRef(null);\n  var messagesRef = React.useRef(null);\n  var contentRef = React.useRef(null);\n  var uploadedFileCount = React.useRef(0);\n  var hasFiles = ObjectUtils.isNotEmpty(filesState);\n  var hasUploadedFiles = ObjectUtils.isNotEmpty(uploadedFilesState);\n  var disabled = props.disabled || uploadingState;\n  var chooseButtonLabel = props.chooseLabel || props.chooseOptions.label || localeOption('choose');\n  var uploadButtonLabel = props.uploadLabel || props.uploadOptions.label || localeOption('upload');\n  var cancelButtonLabel = props.cancelLabel || props.cancelOptions.label || localeOption('cancel');\n  var chooseDisabled = disabled || props.fileLimit && props.fileLimit <= filesState.length + uploadedFileCount;\n  var uploadDisabled = disabled || !hasFiles;\n  var cancelDisabled = disabled || !hasFiles;\n  var isImage = function isImage(file) {\n    return /^image\\//.test(file.type);\n  };\n  var remove = function remove(event, index) {\n    clearInput();\n    var currentFiles = _toConsumableArray(filesState);\n    var removedFile = filesState[index];\n    currentFiles.splice(index, 1);\n    setFilesState(currentFiles);\n    if (props.onRemove) {\n      props.onRemove({\n        originalEvent: event,\n        file: removedFile\n      });\n    }\n  };\n  var removeUploadedFiles = function removeUploadedFiles(event, index) {\n    clearInput();\n    var currentUploadedFiles = _toConsumableArray(uploadedFilesState);\n    var removedFile = filesState[index];\n    currentUploadedFiles.splice(index, 1);\n    setUploadedFilesState(currentUploadedFiles);\n    if (props.onRemove) {\n      props.onRemove({\n        originalEvent: event,\n        file: removedFile\n      });\n    }\n  };\n  var clearInput = function clearInput() {\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  };\n  var formatSize = function formatSize(bytes) {\n    var k = 1024;\n    var dm = 3;\n    var sizes = localeOption('fileSizeTypes');\n    if (bytes <= 0) {\n      return \"0 \".concat(sizes[0]);\n    }\n    var i = Math.floor(Math.log(bytes) / Math.log(k));\n    var formattedSize = parseFloat((bytes / Math.pow(k, i)).toFixed(dm));\n    return \"\".concat(formattedSize, \" \").concat(sizes[i]);\n  };\n  var onFileSelect = function onFileSelect(event) {\n    // give caller a chance to stop the selection\n    if (props.onBeforeSelect && props.onBeforeSelect({\n      originalEvent: event,\n      files: filesState\n    }) === false) {\n      return;\n    }\n    var currentFiles = [];\n    if (props.multiple) {\n      currentFiles = filesState ? _toConsumableArray(filesState) : [];\n    }\n    var selectedFiles = event.dataTransfer ? event.dataTransfer.files : event.target.files;\n    for (var i = 0; i < selectedFiles.length; i++) {\n      var file = selectedFiles[i];\n      var shouldAddFile = props.multiple ? !isFileSelected(file) && validate(file) : validate(file);\n      if (shouldAddFile) {\n        file.objectURL = window.URL.createObjectURL(file);\n        currentFiles.push(file);\n      }\n    }\n    setFilesState(currentFiles);\n    if (ObjectUtils.isNotEmpty(currentFiles) && props.auto) {\n      upload(currentFiles);\n    }\n    if (props.onSelect) {\n      props.onSelect({\n        originalEvent: event,\n        files: currentFiles\n      });\n    }\n    clearInput();\n    setFocusedState(false);\n    if (props.mode === 'basic' && currentFiles.length > 0) {\n      fileInputRef.current.style.display = 'none';\n    }\n  };\n  var isFileSelected = function isFileSelected(file) {\n    return filesState.some(function (f) {\n      return f.name + f.type + f.size === file.name + file.type + file.size;\n    });\n  };\n  var validate = function validate(file) {\n    if (props.maxFileSize && file.size > props.maxFileSize) {\n      var message = {\n        severity: 'error',\n        summary: props.invalidFileSizeMessageSummary.replace('{0}', file.name),\n        detail: props.invalidFileSizeMessageDetail.replace('{0}', formatSize(props.maxFileSize)),\n        sticky: true\n      };\n      if (props.mode === 'advanced') {\n        messagesRef.current.show(message);\n      }\n      props.onValidationFail && props.onValidationFail(file);\n      return false;\n    }\n    return true;\n  };\n  var upload = function upload(files) {\n    files = files || filesState;\n    if (files && files.nativeEvent) {\n      files = filesState;\n    }\n    if (props.customUpload) {\n      if (props.fileLimit) {\n        uploadedFileCount + files.length, _readOnlyError(\"uploadedFileCount\");\n      }\n      if (props.uploadHandler) {\n        props.uploadHandler({\n          files: files,\n          options: {\n            clear: clear,\n            props: props\n          }\n        });\n      }\n    } else {\n      setUploadingState(true);\n      var xhr = new XMLHttpRequest();\n      var formData = new FormData();\n      if (props.onBeforeUpload) {\n        props.onBeforeUpload({\n          xhr: xhr,\n          formData: formData\n        });\n      }\n      var _iterator = _createForOfIteratorHelper(files),\n        _step;\n      try {\n        for (_iterator.s(); !(_step = _iterator.n()).done;) {\n          var file = _step.value;\n          formData.append(props.name, file, file.name);\n        }\n      } catch (err) {\n        _iterator.e(err);\n      } finally {\n        _iterator.f();\n      }\n      xhr.upload.addEventListener('progress', function (event) {\n        if (event.lengthComputable) {\n          var progress = Math.round(event.loaded * 100 / event.total);\n          setProgressState(progress);\n          if (props.onProgress) {\n            props.onProgress({\n              originalEvent: event,\n              progress: progress\n            });\n          }\n        }\n      });\n      xhr.onreadystatechange = function () {\n        if (xhr.readyState === 4) {\n          setProgressState(0);\n          setUploadingState(false);\n          if (xhr.status >= 200 && xhr.status < 300) {\n            if (props.fileLimit) {\n              uploadedFileCount + files.length, _readOnlyError(\"uploadedFileCount\");\n            }\n            if (props.onUpload) {\n              props.onUpload({\n                xhr: xhr,\n                files: files\n              });\n            }\n          } else if (props.onError) {\n            props.onError({\n              xhr: xhr,\n              files: files\n            });\n          }\n          clear();\n          setUploadedFilesState(function (prevUploadedFiles) {\n            return [].concat(_toConsumableArray(prevUploadedFiles), _toConsumableArray(files));\n          });\n        }\n      };\n      xhr.open('POST', props.url, true);\n      if (props.onBeforeSend) {\n        props.onBeforeSend({\n          xhr: xhr,\n          formData: formData\n        });\n      }\n      xhr.withCredentials = props.withCredentials;\n      xhr.send(formData);\n    }\n  };\n  var clear = function clear() {\n    setFilesState([]);\n    setUploadedFilesState([]);\n    setUploadingState(false);\n    props.onClear && props.onClear();\n    clearInput();\n  };\n  var choose = function choose() {\n    fileInputRef.current.click();\n  };\n  var onFocus = function onFocus() {\n    setFocusedState(true);\n  };\n  var onBlur = function onBlur() {\n    setFocusedState(false);\n  };\n  var _onKeyDown = function onKeyDown(event) {\n    if (event.code === 'Enter' || event.code === 'NumpadEnter') {\n      choose();\n    }\n  };\n  var _onDragEnter = function onDragEnter(event) {\n    if (!disabled) {\n      event.dataTransfer.dropEffect = 'copy';\n      event.stopPropagation();\n      event.preventDefault();\n    }\n  };\n  var _onDragOver = function onDragOver(event) {\n    if (!disabled) {\n      event.dataTransfer.dropEffect = 'copy';\n      !isUnstyled() && DomHandler.addClass(contentRef.current, 'p-fileupload-highlight');\n      contentRef.current.setAttribute('data-p-highlight', true);\n      event.stopPropagation();\n      event.preventDefault();\n    }\n  };\n  var _onDragLeave = function onDragLeave(event) {\n    if (!disabled) {\n      event.dataTransfer.dropEffect = 'copy';\n      !isUnstyled() && DomHandler.removeClass(contentRef.current, 'p-fileupload-highlight');\n      contentRef.current.setAttribute('data-p-highlight', false);\n    }\n  };\n  var _onDrop = function onDrop(event) {\n    if (props.disabled) {\n      return;\n    }\n    !isUnstyled() && DomHandler.removeClass(contentRef.current, 'p-fileupload-highlight');\n    contentRef.current.setAttribute('data-p-highlight', false);\n    event.stopPropagation();\n    event.preventDefault();\n\n    // give caller a chance to stop the drop\n    if (props.onBeforeDrop && props.onBeforeDrop(event) === false) {\n      return;\n    }\n    var files = event.dataTransfer ? event.dataTransfer.files : event.target.files;\n    var allowDrop = props.multiple || ObjectUtils.isEmpty(filesState) && files && files.length === 1;\n    allowDrop && onFileSelect(event);\n  };\n  var onSimpleUploaderClick = function onSimpleUploaderClick() {\n    !disabled && hasFiles ? upload() : fileInputRef.current.click();\n  };\n  React.useImperativeHandle(ref, function () {\n    return {\n      props: props,\n      upload: upload,\n      clear: clear,\n      formatSize: formatSize,\n      onFileSelect: onFileSelect,\n      getInput: function getInput() {\n        return fileInputRef.current;\n      },\n      getContent: function getContent() {\n        return contentRef.current;\n      },\n      getFiles: function getFiles() {\n        return filesState;\n      },\n      setFiles: function setFiles(files) {\n        return setFilesState(files || []);\n      },\n      getUploadedFiles: function getUploadedFiles() {\n        return uploadedFilesState;\n      },\n      setUploadedFiles: function setUploadedFiles(files) {\n        return setUploadedFilesState(files || []);\n      }\n    };\n  });\n  var createChooseButton = function createChooseButton() {\n    var _props$chooseOptions = props.chooseOptions,\n      className = _props$chooseOptions.className,\n      style = _props$chooseOptions.style,\n      _icon = _props$chooseOptions.icon,\n      iconOnly = _props$chooseOptions.iconOnly;\n    var chooseButtonLabelProps = mergeProps({\n      className: cx('chooseButtonLabel')\n    }, ptm('chooseButtonLabel'));\n    var label = iconOnly ? /*#__PURE__*/React.createElement(\"span\", _extends({}, chooseButtonLabelProps, {\n      dangerouslySetInnerHTML: {\n        __html: '&nbsp;'\n      }\n    })) : /*#__PURE__*/React.createElement(\"span\", chooseButtonLabelProps, chooseButtonLabel);\n    var inputProps = mergeProps({\n      ref: fileInputRef,\n      type: 'file',\n      onChange: function onChange(e) {\n        return onFileSelect(e);\n      },\n      multiple: props.multiple,\n      accept: props.accept,\n      disabled: chooseDisabled\n    }, ptm('input'));\n    var input = /*#__PURE__*/React.createElement(\"input\", inputProps);\n    var chooseIconProps = mergeProps({\n      className: cx('chooseIcon', {\n        iconOnly: iconOnly\n      }),\n      'aria-hidden': 'true'\n    }, ptm('chooseIcon'));\n    var icon = _icon || /*#__PURE__*/React.createElement(PlusIcon, chooseIconProps);\n    var chooseIcon = IconUtils.getJSXIcon(icon, _objectSpread({}, chooseIconProps), {\n      props: props\n    });\n    var chooseButtonProps = mergeProps({\n      className: classNames(className, cx('chooseButton', {\n        iconOnly: iconOnly,\n        disabled: disabled,\n        className: className,\n        focusedState: focusedState\n      })),\n      style: style,\n      onClick: choose,\n      onKeyDown: function onKeyDown(e) {\n        return _onKeyDown(e);\n      },\n      onFocus: onFocus,\n      onBlur: onBlur,\n      tabIndex: 0,\n      'data-p-disabled': disabled,\n      'data-p-focus': focusedState\n    }, ptm('chooseButton'));\n    return /*#__PURE__*/React.createElement(\"span\", chooseButtonProps, input, chooseIcon, label, /*#__PURE__*/React.createElement(Ripple, null));\n  };\n  var onRemoveClick = function onRemoveClick(e, badgeOptions, index) {\n    if (badgeOptions.severity === 'warning') {\n      remove(e, index);\n    } else {\n      removeUploadedFiles(e, index);\n    }\n  };\n  var createFile = function createFile(file, index, badgeOptions) {\n    var key = file.name + file.type + file.size;\n    var thumbnailProps = mergeProps({\n      role: 'presentation',\n      className: cx('thumbnail'),\n      src: file.objectURL,\n      width: props.previewWidth\n    }, ptm('thumbnail'));\n    var preview = isImage(file) ? /*#__PURE__*/React.createElement(\"img\", _extends({}, thumbnailProps, {\n      alt: file.name\n    })) : null;\n    var detailsProps = mergeProps(ptm('details'));\n    var fileSizeProps = mergeProps(ptm('fileSize'));\n    var fileNameProps = mergeProps({\n      className: cx('fileName')\n    }, ptm('fileName'));\n    var actionsProps = mergeProps(ptm('actions'));\n    var fileName = /*#__PURE__*/React.createElement(\"div\", fileNameProps, file.name);\n    var size = /*#__PURE__*/React.createElement(\"div\", fileSizeProps, formatSize(file.size));\n    var contentBody = /*#__PURE__*/React.createElement(\"div\", detailsProps, /*#__PURE__*/React.createElement(\"div\", fileNameProps, \" \", file.name), /*#__PURE__*/React.createElement(\"span\", fileSizeProps, formatSize(file.size)), /*#__PURE__*/React.createElement(Badge, {\n      className: \"p-fileupload-file-badge\",\n      value: badgeOptions.value,\n      severity: badgeOptions.severity,\n      pt: ptm('badge'),\n      __parentMetadata: {\n        parent: metaData\n      }\n    }));\n    var removeButton = /*#__PURE__*/React.createElement(\"div\", actionsProps, /*#__PURE__*/React.createElement(Button, {\n      type: \"button\",\n      icon: props.removeIcon || /*#__PURE__*/React.createElement(TimesIcon, null),\n      text: true,\n      rounded: true,\n      severity: \"danger\",\n      onClick: function onClick(e) {\n        return onRemoveClick(e, badgeOptions, index);\n      },\n      disabled: disabled,\n      pt: ptm('removeButton'),\n      __parentMetadata: {\n        parent: metaData\n      },\n      unstyled: isUnstyled()\n    }));\n    var content = /*#__PURE__*/React.createElement(React.Fragment, null, preview, contentBody, removeButton);\n    if (props.itemTemplate) {\n      var defaultContentOptions = {\n        onRemove: function onRemove(event) {\n          return remove(event, index);\n        },\n        previewElement: preview,\n        fileNameElement: fileName,\n        sizeElement: size,\n        removeElement: removeButton,\n        formatSize: formatSize(file.size),\n        element: content,\n        index: index,\n        props: props\n      };\n      content = ObjectUtils.getJSXElement(props.itemTemplate, file, defaultContentOptions);\n    }\n    var fileProps = mergeProps({\n      key: key,\n      className: cx('file')\n    }, ptm('file'));\n    return /*#__PURE__*/React.createElement(\"div\", fileProps, content);\n  };\n  var createFiles = function createFiles() {\n    var badgeOptions = {\n      severity: 'warning',\n      value: localeOption('pending') || 'Pending'\n    };\n    var content = filesState.map(function (file, index) {\n      return createFile(file, index, badgeOptions);\n    });\n    return /*#__PURE__*/React.createElement(\"div\", null, content);\n  };\n  var createUploadedFiles = function createUploadedFiles() {\n    var badgeOptions = {\n      severity: 'success',\n      value: localeOption('completed') || 'Completed'\n    };\n    var content = uploadedFilesState && uploadedFilesState.map(function (file, index) {\n      return createFile(file, index, badgeOptions);\n    });\n    return /*#__PURE__*/React.createElement(\"div\", null, content);\n  };\n  var createEmptyContent = function createEmptyContent() {\n    return props.emptyTemplate && !hasFiles && !hasUploadedFiles ? ObjectUtils.getJSXElement(props.emptyTemplate, props) : null;\n  };\n  var createProgressBarContent = function createProgressBarContent() {\n    if (props.progressBarTemplate) {\n      var defaultProgressBarTemplateOptions = {\n        progress: progressState,\n        props: props\n      };\n      return ObjectUtils.getJSXElement(props.progressBarTemplate, defaultProgressBarTemplateOptions);\n    }\n    return /*#__PURE__*/React.createElement(ProgressBar, {\n      value: progressState,\n      showValue: false,\n      pt: ptm('progressbar'),\n      __parentMetadata: {\n        parent: metaData\n      }\n    });\n  };\n  var createAdvanced = function createAdvanced() {\n    var chooseButton = createChooseButton();\n    var emptyContent = createEmptyContent();\n    var uploadButton;\n    var cancelButton;\n    var filesList;\n    var uplaodedFilesList;\n    var progressBar;\n    if (!props.auto) {\n      var uploadOptions = props.uploadOptions;\n      var cancelOptions = props.cancelOptions;\n      var uploadLabel = !uploadOptions.iconOnly ? uploadButtonLabel : '';\n      var cancelLabel = !cancelOptions.iconOnly ? cancelButtonLabel : '';\n      var uploadIconProps = mergeProps({\n        className: cx('uploadIcon', {\n          iconOnly: uploadOptions.iconOnly\n        }),\n        'aria-hidden': 'true'\n      }, ptm('uploadIcon'));\n      var uploadIcon = IconUtils.getJSXIcon(uploadOptions.icon || /*#__PURE__*/React.createElement(UploadIcon, uploadIconProps), _objectSpread({}, uploadIconProps), {\n        props: props\n      });\n      var cancelIconProps = mergeProps({\n        className: cx('cancelIcon', {\n          iconOnly: cancelOptions.iconOnly\n        }),\n        'aria-hidden': 'true'\n      }, ptm('cancelIcon'));\n      var cancelIcon = IconUtils.getJSXIcon(cancelOptions.icon || /*#__PURE__*/React.createElement(TimesIcon, cancelIconProps), _objectSpread({}, cancelIconProps), {\n        props: props\n      });\n      uploadButton = /*#__PURE__*/React.createElement(Button, {\n        type: \"button\",\n        label: uploadLabel,\n        \"aria-hidden\": \"true\",\n        icon: uploadIcon,\n        onClick: upload,\n        disabled: uploadDisabled,\n        style: uploadOptions.style,\n        className: uploadOptions.className,\n        pt: ptm('uploadButton'),\n        __parentMetadata: {\n          parent: metaData\n        },\n        unstyled: isUnstyled()\n      });\n      cancelButton = /*#__PURE__*/React.createElement(Button, {\n        type: \"button\",\n        label: cancelLabel,\n        \"aria-hidden\": \"true\",\n        icon: cancelIcon,\n        onClick: clear,\n        disabled: cancelDisabled,\n        style: cancelOptions.style,\n        className: cancelOptions.className,\n        pt: ptm('cancelButton'),\n        __parentMetadata: {\n          parent: metaData\n        },\n        unstyled: isUnstyled()\n      });\n    }\n    if (hasFiles) {\n      filesList = createFiles();\n      progressBar = createProgressBarContent();\n    }\n    if (hasUploadedFiles) {\n      uplaodedFilesList = createUploadedFiles();\n    }\n    var buttonbarProps = mergeProps({\n      className: classNames(props.headerClassName, cx('buttonbar')),\n      style: props.headerStyle\n    }, ptm('buttonbar'));\n    var header = /*#__PURE__*/React.createElement(\"div\", buttonbarProps, chooseButton, uploadButton, cancelButton);\n    if (props.headerTemplate) {\n      var defaultContentOptions = {\n        className: classNames('p-fileupload-buttonbar', props.headerClassName),\n        chooseButton: chooseButton,\n        uploadButton: uploadButton,\n        cancelButton: cancelButton,\n        element: header,\n        props: props\n      };\n      header = ObjectUtils.getJSXElement(props.headerTemplate, defaultContentOptions);\n    }\n    var rootProps = mergeProps({\n      id: props.id,\n      className: classNames(props.className, cx('root')),\n      style: props.style\n    }, FileUploadBase.getOtherProps(props), ptm('root'));\n    var contentProps = mergeProps({\n      ref: contentRef,\n      className: classNames(props.contentClassName, cx('content')),\n      style: props.contentStyle,\n      onDragEnter: function onDragEnter(e) {\n        return _onDragEnter(e);\n      },\n      onDragOver: function onDragOver(e) {\n        return _onDragOver(e);\n      },\n      onDragLeave: function onDragLeave(e) {\n        return _onDragLeave(e);\n      },\n      onDrop: function onDrop(e) {\n        return _onDrop(e);\n      },\n      'data-p-highlight': false\n    }, ptm('content'));\n    return /*#__PURE__*/React.createElement(\"div\", rootProps, header, /*#__PURE__*/React.createElement(\"div\", contentProps, progressBar, /*#__PURE__*/React.createElement(Messages, {\n      ref: messagesRef,\n      __parentMetadata: {\n        parent: metaData\n      }\n    }), hasFiles ? filesList : null, hasUploadedFiles ? uplaodedFilesList : null, emptyContent));\n  };\n  var createBasic = function createBasic() {\n    var chooseOptions = props.chooseOptions;\n    var labelProps = mergeProps({\n      className: cx('label')\n    }, ptm('label'));\n    var chooseLabel = chooseOptions.iconOnly ? /*#__PURE__*/React.createElement(\"span\", _extends({}, labelProps, {\n      dangerouslySetInnerHTML: {\n        __html: '&nbsp;'\n      }\n    })) : /*#__PURE__*/React.createElement(\"span\", labelProps, chooseButtonLabel);\n    var label = props.auto ? chooseLabel : /*#__PURE__*/React.createElement(\"span\", labelProps, hasFiles ? props.selectedFileLabel || filesState[0].name : chooseLabel);\n    var chooseIconProps = mergeProps({\n      className: cx('chooseIcon', {\n        iconOnly: chooseOptions.iconOnly\n      })\n    }, ptm('chooseIcon'));\n    var icon = chooseOptions.icon ? chooseOptions.icon : !chooseOptions.icon && (!hasFiles || props.auto) ? /*#__PURE__*/React.createElement(PlusIcon, chooseIconProps) : !chooseOptions.icon && hasFiles && !props.auto && /*#__PURE__*/React.createElement(UploadIcon, chooseIconProps);\n    var chooseIcon = IconUtils.getJSXIcon(icon, _objectSpread({}, chooseIconProps), {\n      props: props,\n      hasFiles: hasFiles\n    });\n    var inputProps = mergeProps({\n      ref: fileInputRef,\n      type: 'file',\n      onChange: function onChange(e) {\n        return onFileSelect(e);\n      },\n      multiple: props.multiple,\n      accept: props.accept,\n      disabled: disabled\n    }, ptm('input'));\n    var input = !hasFiles && /*#__PURE__*/React.createElement(\"input\", inputProps);\n    var rootProps = mergeProps({\n      className: classNames(props.className, cx('root')),\n      style: props.style\n    }, FileUploadBase.getOtherProps(props), ptm('root'));\n    var basicButtonProps = mergeProps({\n      className: classNames(chooseOptions.className, cx('basicButton', {\n        hasFiles: hasFiles,\n        disabled: disabled,\n        focusedState: focusedState\n      })),\n      style: chooseOptions.style,\n      tabIndex: 0,\n      onClick: onSimpleUploaderClick,\n      onKeyDown: function onKeyDown(e) {\n        return _onKeyDown(e);\n      },\n      onFocus: onFocus,\n      onBlur: onBlur\n    }, FileUploadBase.getOtherProps(props), ptm('basicButton'));\n    return /*#__PURE__*/React.createElement(\"div\", rootProps, /*#__PURE__*/React.createElement(Messages, {\n      ref: messagesRef,\n      pt: ptm('message'),\n      __parentMetadata: {\n        parent: metaData\n      }\n    }), /*#__PURE__*/React.createElement(\"span\", basicButtonProps, chooseIcon, label, input, /*#__PURE__*/React.createElement(Ripple, null)));\n  };\n  if (props.mode === 'advanced') {\n    return createAdvanced();\n  } else if (props.mode === 'basic') {\n    return createBasic();\n  }\n}));\nFileUpload.displayName = 'FileUpload';\n\nexport { FileUpload };\n"], "mappings": "AAAA,YAAY;;AACZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,iBAAiB,EAAEC,YAAY,QAAQ,gBAAgB;AAChE,SAASC,aAAa,EAAEC,cAAc,QAAQ,0BAA0B;AACxE,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,UAAU,EAAEC,WAAW,EAAEC,SAAS,EAAEC,UAAU,QAAQ,kBAAkB;AACjF,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,UAAU,QAAQ,yBAAyB;AACpD,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,WAAW,QAAQ,wBAAwB;AACpD,SAASC,MAAM,QAAQ,mBAAmB;AAE1C,SAASC,OAAOA,CAACC,CAAC,EAAE;EAClB,yBAAyB;;EAEzB,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,CAAC,EAAE;IAChG,OAAO,OAAOA,CAAC;EACjB,CAAC,GAAG,UAAUA,CAAC,EAAE;IACf,OAAOA,CAAC,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,CAAC,CAACG,WAAW,KAAKF,MAAM,IAAID,CAAC,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,CAAC;EACrH,CAAC,EAAED,OAAO,CAACC,CAAC,CAAC;AACf;AAEA,SAASK,WAAWA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACzB,IAAI,QAAQ,IAAIR,OAAO,CAACO,CAAC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAC1C,IAAIE,CAAC,GAAGF,CAAC,CAACL,MAAM,CAACI,WAAW,CAAC;EAC7B,IAAI,KAAK,CAAC,KAAKG,CAAC,EAAE;IAChB,IAAIC,CAAC,GAAGD,CAAC,CAACE,IAAI,CAACJ,CAAC,EAAEC,CAAC,IAAI,SAAS,CAAC;IACjC,IAAI,QAAQ,IAAIR,OAAO,CAACU,CAAC,CAAC,EAAE,OAAOA,CAAC;IACpC,MAAM,IAAIE,SAAS,CAAC,8CAA8C,CAAC;EACrE;EACA,OAAO,CAAC,QAAQ,KAAKJ,CAAC,GAAGK,MAAM,GAAGC,MAAM,EAAEP,CAAC,CAAC;AAC9C;AAEA,SAASQ,aAAaA,CAACR,CAAC,EAAE;EACxB,IAAIG,CAAC,GAAGJ,WAAW,CAACC,CAAC,EAAE,QAAQ,CAAC;EAChC,OAAO,QAAQ,IAAIP,OAAO,CAACU,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAC5C;AAEA,SAASM,eAAeA,CAACP,CAAC,EAAED,CAAC,EAAED,CAAC,EAAE;EAChC,OAAO,CAACC,CAAC,GAAGO,aAAa,CAACP,CAAC,CAAC,KAAKC,CAAC,GAAGQ,MAAM,CAACC,cAAc,CAACT,CAAC,EAAED,CAAC,EAAE;IAC/DW,KAAK,EAAEZ,CAAC;IACRa,UAAU,EAAE,CAAC,CAAC;IACdC,YAAY,EAAE,CAAC,CAAC;IAChBC,QAAQ,EAAE,CAAC;EACb,CAAC,CAAC,GAAGb,CAAC,CAACD,CAAC,CAAC,GAAGD,CAAC,EAAEE,CAAC;AAClB;AAEA,SAASc,QAAQA,CAAA,EAAG;EAClB,OAAOA,QAAQ,GAAGN,MAAM,CAACO,MAAM,GAAGP,MAAM,CAACO,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUC,CAAC,EAAE;IACpE,KAAK,IAAIjB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkB,SAAS,CAACC,MAAM,EAAEnB,CAAC,EAAE,EAAE;MACzC,IAAIF,CAAC,GAAGoB,SAAS,CAAClB,CAAC,CAAC;MACpB,KAAK,IAAID,CAAC,IAAID,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEsB,cAAc,CAAClB,IAAI,CAACJ,CAAC,EAAEC,CAAC,CAAC,KAAKkB,CAAC,CAAClB,CAAC,CAAC,GAAGD,CAAC,CAACC,CAAC,CAAC,CAAC;IAClE;IACA,OAAOkB,CAAC;EACV,CAAC,EAAEH,QAAQ,CAACO,KAAK,CAAC,IAAI,EAAEH,SAAS,CAAC;AACpC;AAEA,SAASI,mBAAmBA,CAACvB,CAAC,EAAEwB,CAAC,EAAE;EACjC,CAAC,IAAI,IAAIA,CAAC,IAAIA,CAAC,GAAGxB,CAAC,CAACoB,MAAM,MAAMI,CAAC,GAAGxB,CAAC,CAACoB,MAAM,CAAC;EAC7C,KAAK,IAAInB,CAAC,GAAG,CAAC,EAAEiB,CAAC,GAAGO,KAAK,CAACD,CAAC,CAAC,EAAEvB,CAAC,GAAGuB,CAAC,EAAEvB,CAAC,EAAE,EAAEiB,CAAC,CAACjB,CAAC,CAAC,GAAGD,CAAC,CAACC,CAAC,CAAC;EACrD,OAAOiB,CAAC;AACV;AAEA,SAASQ,kBAAkBA,CAAC1B,CAAC,EAAE;EAC7B,IAAIyB,KAAK,CAACE,OAAO,CAAC3B,CAAC,CAAC,EAAE,OAAOuB,mBAAmB,CAACvB,CAAC,CAAC;AACrD;AAEA,SAAS4B,gBAAgBA,CAAC5B,CAAC,EAAE;EAC3B,IAAI,WAAW,IAAI,OAAON,MAAM,IAAI,IAAI,IAAIM,CAAC,CAACN,MAAM,CAACC,QAAQ,CAAC,IAAI,IAAI,IAAIK,CAAC,CAAC,YAAY,CAAC,EAAE,OAAOyB,KAAK,CAACI,IAAI,CAAC7B,CAAC,CAAC;AACjH;AAEA,SAAS8B,6BAA6BA,CAAC9B,CAAC,EAAEwB,CAAC,EAAE;EAC3C,IAAIxB,CAAC,EAAE;IACL,IAAI,QAAQ,IAAI,OAAOA,CAAC,EAAE,OAAOuB,mBAAmB,CAACvB,CAAC,EAAEwB,CAAC,CAAC;IAC1D,IAAIzB,CAAC,GAAG,CAAC,CAAC,CAACgC,QAAQ,CAAC5B,IAAI,CAACH,CAAC,CAAC,CAACgC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACxC,OAAO,QAAQ,KAAKjC,CAAC,IAAIC,CAAC,CAACJ,WAAW,KAAKG,CAAC,GAAGC,CAAC,CAACJ,WAAW,CAACqC,IAAI,CAAC,EAAE,KAAK,KAAKlC,CAAC,IAAI,KAAK,KAAKA,CAAC,GAAG0B,KAAK,CAACI,IAAI,CAAC7B,CAAC,CAAC,GAAG,WAAW,KAAKD,CAAC,IAAI,0CAA0C,CAACmC,IAAI,CAACnC,CAAC,CAAC,GAAGwB,mBAAmB,CAACvB,CAAC,EAAEwB,CAAC,CAAC,GAAG,KAAK,CAAC;EAC/N;AACF;AAEA,SAASW,kBAAkBA,CAAA,EAAG;EAC5B,MAAM,IAAI/B,SAAS,CAAC,sIAAsI,CAAC;AAC7J;AAEA,SAASgC,kBAAkBA,CAACpC,CAAC,EAAE;EAC7B,OAAO0B,kBAAkB,CAAC1B,CAAC,CAAC,IAAI4B,gBAAgB,CAAC5B,CAAC,CAAC,IAAI8B,6BAA6B,CAAC9B,CAAC,CAAC,IAAImC,kBAAkB,CAAC,CAAC;AACjH;AAEA,SAASE,cAAcA,CAACrC,CAAC,EAAE;EACzB,MAAM,IAAII,SAAS,CAAC,GAAG,GAAGJ,CAAC,GAAG,gBAAgB,CAAC;AACjD;AAEA,SAASsC,eAAeA,CAACtC,CAAC,EAAE;EAC1B,IAAIyB,KAAK,CAACE,OAAO,CAAC3B,CAAC,CAAC,EAAE,OAAOA,CAAC;AAChC;AAEA,SAASuC,qBAAqBA,CAACvC,CAAC,EAAEwC,CAAC,EAAE;EACnC,IAAIzC,CAAC,GAAG,IAAI,IAAIC,CAAC,GAAG,IAAI,GAAG,WAAW,IAAI,OAAON,MAAM,IAAIM,CAAC,CAACN,MAAM,CAACC,QAAQ,CAAC,IAAIK,CAAC,CAAC,YAAY,CAAC;EAChG,IAAI,IAAI,IAAID,CAAC,EAAE;IACb,IAAIE,CAAC;MACHiB,CAAC;MACDhB,CAAC;MACDuC,CAAC;MACDjB,CAAC,GAAG,EAAE;MACNkB,CAAC,GAAG,CAAC,CAAC;MACNjD,CAAC,GAAG,CAAC,CAAC;IACR,IAAI;MACF,IAAIS,CAAC,GAAG,CAACH,CAAC,GAAGA,CAAC,CAACI,IAAI,CAACH,CAAC,CAAC,EAAE2C,IAAI,EAAE,CAAC,KAAKH,CAAC,EAAE;QACrC,IAAI/B,MAAM,CAACV,CAAC,CAAC,KAAKA,CAAC,EAAE;QACrB2C,CAAC,GAAG,CAAC,CAAC;MACR,CAAC,MAAM,OAAO,EAAEA,CAAC,GAAG,CAACzC,CAAC,GAAGC,CAAC,CAACC,IAAI,CAACJ,CAAC,CAAC,EAAE6C,IAAI,CAAC,KAAKpB,CAAC,CAACqB,IAAI,CAAC5C,CAAC,CAACU,KAAK,CAAC,EAAEa,CAAC,CAACJ,MAAM,KAAKoB,CAAC,CAAC,EAAEE,CAAC,GAAG,CAAC,CAAC,CAAC;IACzF,CAAC,CAAC,OAAO1C,CAAC,EAAE;MACVP,CAAC,GAAG,CAAC,CAAC,EAAEyB,CAAC,GAAGlB,CAAC;IACf,CAAC,SAAS;MACR,IAAI;QACF,IAAI,CAAC0C,CAAC,IAAI,IAAI,IAAI3C,CAAC,CAAC,QAAQ,CAAC,KAAK0C,CAAC,GAAG1C,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAEU,MAAM,CAACgC,CAAC,CAAC,KAAKA,CAAC,CAAC,EAAE;MACzE,CAAC,SAAS;QACR,IAAIhD,CAAC,EAAE,MAAMyB,CAAC;MAChB;IACF;IACA,OAAOM,CAAC;EACV;AACF;AAEA,SAASsB,gBAAgBA,CAAA,EAAG;EAC1B,MAAM,IAAI1C,SAAS,CAAC,2IAA2I,CAAC;AAClK;AAEA,SAAS2C,cAAcA,CAAC/C,CAAC,EAAEC,CAAC,EAAE;EAC5B,OAAOqC,eAAe,CAACtC,CAAC,CAAC,IAAIuC,qBAAqB,CAACvC,CAAC,EAAEC,CAAC,CAAC,IAAI6B,6BAA6B,CAAC9B,CAAC,EAAEC,CAAC,CAAC,IAAI6C,gBAAgB,CAAC,CAAC;AACvH;AAEA,IAAIE,SAAS,GAAG;EACdC,IAAI,EAAE,SAASA,IAAIA,CAACC,IAAI,EAAE;IACxB,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;IACtB,OAAOtE,UAAU,CAAC,qBAAqB,EAAE2B,eAAe,CAAC;MACvD,mBAAmB,EAAE1B,WAAW,CAACsE,UAAU,CAACD,KAAK,CAACxC,KAAK,CAAC,IAAIN,MAAM,CAAC8C,KAAK,CAACxC,KAAK,CAAC,CAACS,MAAM,KAAK,CAAC;MAC5F,aAAa,EAAEtC,WAAW,CAACuE,OAAO,CAACF,KAAK,CAACxC,KAAK,CAAC;MAC/C,YAAY,EAAEwC,KAAK,CAACG,IAAI,KAAK,OAAO;MACpC,YAAY,EAAEH,KAAK,CAACG,IAAI,KAAK;IAC/B,CAAC,EAAE,UAAU,CAACC,MAAM,CAACJ,KAAK,CAACK,QAAQ,CAAC,EAAEL,KAAK,CAACK,QAAQ,KAAK,IAAI,CAAC,CAAC;EACjE;AACF,CAAC;AACD,IAAIC,QAAQ,GAAG,grBAAgrB;AAC/rB,IAAIC,SAAS,GAAGhF,aAAa,CAACiF,MAAM,CAAC;EACnCC,YAAY,EAAE;IACZC,MAAM,EAAE,OAAO;IACfC,gBAAgB,EAAE,IAAI;IACtBnD,KAAK,EAAE,IAAI;IACX6C,QAAQ,EAAE,IAAI;IACdF,IAAI,EAAE,IAAI;IACVS,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,IAAI;IACfC,QAAQ,EAAEC;EACZ,CAAC;EACDC,GAAG,EAAE;IACHC,OAAO,EAAEpB,SAAS;IAClBqB,MAAM,EAAEZ;EACV;AACF,CAAC,CAAC;AAEF,SAASa,SAASA,CAACrE,CAAC,EAAED,CAAC,EAAE;EAAE,IAAID,CAAC,GAAGU,MAAM,CAAC8D,IAAI,CAACtE,CAAC,CAAC;EAAE,IAAIQ,MAAM,CAAC+D,qBAAqB,EAAE;IAAE,IAAI/E,CAAC,GAAGgB,MAAM,CAAC+D,qBAAqB,CAACvE,CAAC,CAAC;IAAED,CAAC,KAAKP,CAAC,GAAGA,CAAC,CAACgF,MAAM,CAAC,UAAUzE,CAAC,EAAE;MAAE,OAAOS,MAAM,CAACiE,wBAAwB,CAACzE,CAAC,EAAED,CAAC,CAAC,CAACY,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEb,CAAC,CAAC8C,IAAI,CAACvB,KAAK,CAACvB,CAAC,EAAEN,CAAC,CAAC;EAAE;EAAE,OAAOM,CAAC;AAAE;AAChQ,SAAS4E,eAAeA,CAAC1E,CAAC,EAAE;EAAE,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmB,SAAS,CAACC,MAAM,EAAEpB,CAAC,EAAE,EAAE;IAAE,IAAID,CAAC,GAAG,IAAI,IAAIoB,SAAS,CAACnB,CAAC,CAAC,GAAGmB,SAAS,CAACnB,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGsE,SAAS,CAAC7D,MAAM,CAACV,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC6E,OAAO,CAAC,UAAU5E,CAAC,EAAE;MAAEQ,eAAe,CAACP,CAAC,EAAED,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGS,MAAM,CAACoE,yBAAyB,GAAGpE,MAAM,CAACqE,gBAAgB,CAAC7E,CAAC,EAAEQ,MAAM,CAACoE,yBAAyB,CAAC9E,CAAC,CAAC,CAAC,GAAGuE,SAAS,CAAC7D,MAAM,CAACV,CAAC,CAAC,CAAC,CAAC6E,OAAO,CAAC,UAAU5E,CAAC,EAAE;MAAES,MAAM,CAACC,cAAc,CAACT,CAAC,EAAED,CAAC,EAAES,MAAM,CAACiE,wBAAwB,CAAC3E,CAAC,EAAEC,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOC,CAAC;AAAE;AAC5b,IAAI8E,KAAK,GAAG,aAAaxG,KAAK,CAACyG,IAAI,CAAC,aAAazG,KAAK,CAAC0G,UAAU,CAAC,UAAUC,OAAO,EAAEC,GAAG,EAAE;EACxF,IAAIC,UAAU,GAAGxG,aAAa,CAAC,CAAC;EAChC,IAAIyG,OAAO,GAAG9G,KAAK,CAAC+G,UAAU,CAAC9G,iBAAiB,CAAC;EACjD,IAAI2E,KAAK,GAAGO,SAAS,CAAC6B,QAAQ,CAACL,OAAO,EAAEG,OAAO,CAAC;EAChD,IAAIG,qBAAqB,GAAG9B,SAAS,CAAC+B,WAAW,CAACd,eAAe,CAAC;MAC9DxB,KAAK,EAAEA;IACT,CAAC,EAAEA,KAAK,CAACW,gBAAgB,CAAC,CAAC;IAC3B4B,GAAG,GAAGF,qBAAqB,CAACE,GAAG;IAC/BC,EAAE,GAAGH,qBAAqB,CAACG,EAAE;IAC7BC,UAAU,GAAGJ,qBAAqB,CAACI,UAAU;EAC/CjH,cAAc,CAAC+E,SAAS,CAACS,GAAG,CAACE,MAAM,EAAEuB,UAAU,EAAE;IAC/C3D,IAAI,EAAE;EACR,CAAC,CAAC;EACF,IAAI4D,UAAU,GAAGtH,KAAK,CAACuH,MAAM,CAAC,IAAI,CAAC;EACnCvH,KAAK,CAACwH,mBAAmB,CAACZ,GAAG,EAAE,YAAY;IACzC,OAAO;MACLhC,KAAK,EAAEA,KAAK;MACZ6C,UAAU,EAAE,SAASA,UAAUA,CAAA,EAAG;QAChC,OAAOH,UAAU,CAACI,OAAO;MAC3B;IACF,CAAC;EACH,CAAC,CAAC;EACF,IAAIC,SAAS,GAAGd,UAAU,CAAC;IACzBD,GAAG,EAAEU,UAAU;IACf9B,KAAK,EAAEZ,KAAK,CAACY,KAAK;IAClBC,SAAS,EAAEnF,UAAU,CAACsE,KAAK,CAACa,SAAS,EAAE2B,EAAE,CAAC,MAAM,CAAC;EACnD,CAAC,EAAEjC,SAAS,CAACyC,aAAa,CAAChD,KAAK,CAAC,EAAEuC,GAAG,CAAC,MAAM,CAAC,CAAC;EAC/C,OAAO,aAAanH,KAAK,CAAC6H,aAAa,CAAC,MAAM,EAAEF,SAAS,EAAE/C,KAAK,CAACxC,KAAK,CAAC;AACzE,CAAC,CAAC,CAAC;AACHoE,KAAK,CAACsB,WAAW,GAAG,OAAO;AAE3B,IAAIjC,OAAO,GAAG;EACZnB,IAAI,EAAE,SAASA,IAAIA,CAACC,IAAI,EAAE;IACxB,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;IACtB,OAAOtE,UAAU,CAAC,4BAA4B,CAAC0E,MAAM,CAACJ,KAAK,CAACmD,IAAI,EAAE,cAAc,CAAC,CAAC;EACpF,CAAC;EACDC,SAAS,EAAE,wBAAwB;EACnCC,OAAO,EAAE,sBAAsB;EAC/BC,YAAY,EAAE,SAASA,YAAYA,CAACC,KAAK,EAAE;IACzC,IAAIC,QAAQ,GAAGD,KAAK,CAACC,QAAQ;MAC3BC,QAAQ,GAAGF,KAAK,CAACE,QAAQ;MACzBC,YAAY,GAAGH,KAAK,CAACG,YAAY;IACnC,OAAOhI,UAAU,CAAC,0CAA0C,EAAE;MAC5D,YAAY,EAAE+H,QAAQ;MACtB,SAAS,EAAEC,YAAY;MACvB,oBAAoB,EAAEF;IACxB,CAAC,CAAC;EACJ,CAAC;EACDG,KAAK,EAAE,4BAA4B;EACnCC,IAAI,EAAE,kBAAkB;EACxBC,QAAQ,EAAE,uBAAuB;EACjCC,SAAS,EAAE,6BAA6B;EACxCC,iBAAiB,EAAE,4BAA4B;EAC/CC,WAAW,EAAE,SAASA,WAAWA,CAACC,KAAK,EAAE;IACvC,IAAIR,QAAQ,GAAGQ,KAAK,CAACR,QAAQ;MAC3BC,YAAY,GAAGO,KAAK,CAACP,YAAY;MACjCQ,QAAQ,GAAGD,KAAK,CAACC,QAAQ;IAC3B,OAAOxI,UAAU,CAAC,0CAA0C,EAAE;MAC5D,8BAA8B,EAAEwI,QAAQ;MACxC,YAAY,EAAET,QAAQ;MACtB,SAAS,EAAEC;IACb,CAAC,CAAC;EACJ,CAAC;EACDS,UAAU,EAAE,SAASA,UAAUA,CAACC,KAAK,EAAE;IACrC,IAAIpE,KAAK,GAAGoE,KAAK,CAACpE,KAAK;MACrBwD,QAAQ,GAAGY,KAAK,CAACZ,QAAQ;IAC3B,OAAOxD,KAAK,CAACmD,IAAI,KAAK,OAAO,GAAGzH,UAAU,CAAC,eAAe,EAAE;MAC1D,oBAAoB,EAAE,CAAC8H;IACzB,CAAC,CAAC,GAAG9H,UAAU,CAAC,2BAA2B,EAAE;MAC3C,oBAAoB,EAAE,CAAC8H;IACzB,CAAC,CAAC;EACJ,CAAC;EACDa,UAAU,EAAE,SAASA,UAAUA,CAACC,KAAK,EAAE;IACrC,IAAId,QAAQ,GAAGc,KAAK,CAACd,QAAQ;IAC7B,OAAO9H,UAAU,CAAC,mBAAmB,EAAE;MACrC,oBAAoB,EAAE,CAAC8H;IACzB,CAAC,CAAC;EACJ,CAAC;EACDe,UAAU,EAAE,SAASA,UAAUA,CAACC,KAAK,EAAE;IACrC,IAAIhB,QAAQ,GAAGgB,KAAK,CAAChB,QAAQ;IAC7B,OAAO9H,UAAU,CAAC,mBAAmB,EAAE;MACrC,oBAAoB,EAAE,CAAC8H;IACzB,CAAC,CAAC;EACJ;AACF,CAAC;AACD,IAAItC,MAAM,GAAG,mrCAAmrC;AAChsC,IAAIuD,cAAc,GAAGlJ,aAAa,CAACiF,MAAM,CAAC;EACxCC,YAAY,EAAE;IACZC,MAAM,EAAE,YAAY;IACpBgE,EAAE,EAAE,IAAI;IACR5F,IAAI,EAAE,IAAI;IACV6F,GAAG,EAAE,IAAI;IACTxB,IAAI,EAAE,UAAU;IAChByB,QAAQ,EAAE,KAAK;IACfC,MAAM,EAAE,IAAI;IACZC,UAAU,EAAE,IAAI;IAChBrB,QAAQ,EAAE,KAAK;IACfsB,IAAI,EAAE,KAAK;IACXC,WAAW,EAAE,IAAI;IACjBC,6BAA6B,EAAE,0BAA0B;IACzDC,4BAA4B,EAAE,6BAA6B;IAC3DtE,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,IAAI;IACfsE,eAAe,EAAE,KAAK;IACtBC,YAAY,EAAE,EAAE;IAChBC,WAAW,EAAE,IAAI;IACjBC,iBAAiB,EAAE,IAAI;IACvBC,WAAW,EAAE,IAAI;IACjBC,WAAW,EAAE,IAAI;IACjBC,aAAa,EAAE;MACb9B,KAAK,EAAE,IAAI;MACX+B,IAAI,EAAE,IAAI;MACVlC,QAAQ,EAAE,KAAK;MACf3C,SAAS,EAAE,IAAI;MACfD,KAAK,EAAE;IACT,CAAC;IACD+E,aAAa,EAAE;MACbhC,KAAK,EAAE,IAAI;MACX+B,IAAI,EAAE,IAAI;MACVlC,QAAQ,EAAE,KAAK;MACf3C,SAAS,EAAE,IAAI;MACfD,KAAK,EAAE;IACT,CAAC;IACDgF,aAAa,EAAE;MACbjC,KAAK,EAAE,IAAI;MACX+B,IAAI,EAAE,IAAI;MACVlC,QAAQ,EAAE,KAAK;MACf3C,SAAS,EAAE,IAAI;MACfD,KAAK,EAAE;IACT,CAAC;IACDiF,YAAY,EAAE,KAAK;IACnBC,eAAe,EAAE,IAAI;IACrBC,WAAW,EAAE,IAAI;IACjBC,gBAAgB,EAAE,IAAI;IACtBC,YAAY,EAAE,IAAI;IAClBC,cAAc,EAAE,IAAI;IACpBC,YAAY,EAAE,IAAI;IAClBC,aAAa,EAAE,IAAI;IACnBC,mBAAmB,EAAE,IAAI;IACzBC,cAAc,EAAE,IAAI;IACpBC,YAAY,EAAE,IAAI;IAClBC,YAAY,EAAE,IAAI;IAClBC,cAAc,EAAE,IAAI;IACpBC,QAAQ,EAAE,IAAI;IACdC,OAAO,EAAE,IAAI;IACbC,OAAO,EAAE,IAAI;IACbC,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE,IAAI;IAChBC,gBAAgB,EAAE,IAAI;IACtBC,aAAa,EAAE,IAAI;IACnBC,QAAQ,EAAE,IAAI;IACdnG,QAAQ,EAAEC;EACZ,CAAC;EACDC,GAAG,EAAE;IACHC,OAAO,EAAEA,OAAO;IAChBC,MAAM,EAAEA;EACV;AACF,CAAC,CAAC;AAEF,SAASgG,OAAOA,CAACpK,CAAC,EAAED,CAAC,EAAE;EAAE,IAAID,CAAC,GAAGU,MAAM,CAAC8D,IAAI,CAACtE,CAAC,CAAC;EAAE,IAAIQ,MAAM,CAAC+D,qBAAqB,EAAE;IAAE,IAAI/E,CAAC,GAAGgB,MAAM,CAAC+D,qBAAqB,CAACvE,CAAC,CAAC;IAAED,CAAC,KAAKP,CAAC,GAAGA,CAAC,CAACgF,MAAM,CAAC,UAAUzE,CAAC,EAAE;MAAE,OAAOS,MAAM,CAACiE,wBAAwB,CAACzE,CAAC,EAAED,CAAC,CAAC,CAACY,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEb,CAAC,CAAC8C,IAAI,CAACvB,KAAK,CAACvB,CAAC,EAAEN,CAAC,CAAC;EAAE;EAAE,OAAOM,CAAC;AAAE;AAC9P,SAASuK,aAAaA,CAACrK,CAAC,EAAE;EAAE,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmB,SAAS,CAACC,MAAM,EAAEpB,CAAC,EAAE,EAAE;IAAE,IAAID,CAAC,GAAG,IAAI,IAAIoB,SAAS,CAACnB,CAAC,CAAC,GAAGmB,SAAS,CAACnB,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGqK,OAAO,CAAC5J,MAAM,CAACV,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC6E,OAAO,CAAC,UAAU5E,CAAC,EAAE;MAAEQ,eAAe,CAACP,CAAC,EAAED,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGS,MAAM,CAACoE,yBAAyB,GAAGpE,MAAM,CAACqE,gBAAgB,CAAC7E,CAAC,EAAEQ,MAAM,CAACoE,yBAAyB,CAAC9E,CAAC,CAAC,CAAC,GAAGsK,OAAO,CAAC5J,MAAM,CAACV,CAAC,CAAC,CAAC,CAAC6E,OAAO,CAAC,UAAU5E,CAAC,EAAE;MAAES,MAAM,CAACC,cAAc,CAACT,CAAC,EAAED,CAAC,EAAES,MAAM,CAACiE,wBAAwB,CAAC3E,CAAC,EAAEC,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOC,CAAC;AAAE;AACtb,SAASsK,0BAA0BA,CAACvK,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAIF,CAAC,GAAG,WAAW,IAAI,OAAOL,MAAM,IAAIM,CAAC,CAACN,MAAM,CAACC,QAAQ,CAAC,IAAIK,CAAC,CAAC,YAAY,CAAC;EAAE,IAAI,CAACD,CAAC,EAAE;IAAE,IAAI0B,KAAK,CAACE,OAAO,CAAC3B,CAAC,CAAC,KAAKD,CAAC,GAAGyK,2BAA2B,CAACxK,CAAC,CAAC,CAAC,IAAIC,CAAC,IAAID,CAAC,IAAI,QAAQ,IAAI,OAAOA,CAAC,CAACoB,MAAM,EAAE;MAAErB,CAAC,KAAKC,CAAC,GAAGD,CAAC,CAAC;MAAE,IAAI0K,EAAE,GAAG,CAAC;QAAEC,CAAC,GAAG,SAASA,CAACA,CAAA,EAAG,CAAC,CAAC;MAAE,OAAO;QAAEC,CAAC,EAAED,CAAC;QAAExJ,CAAC,EAAE,SAASA,CAACA,CAAA,EAAG;UAAE,OAAOuJ,EAAE,IAAIzK,CAAC,CAACoB,MAAM,GAAG;YAAEwB,IAAI,EAAE,CAAC;UAAE,CAAC,GAAG;YAAEA,IAAI,EAAE,CAAC,CAAC;YAAEjC,KAAK,EAAEX,CAAC,CAACyK,EAAE,EAAE;UAAE,CAAC;QAAE,CAAC;QAAExK,CAAC,EAAE,SAASA,CAACA,CAACD,CAAC,EAAE;UAAE,MAAMA,CAAC;QAAE,CAAC;QAAE0C,CAAC,EAAEgI;MAAE,CAAC;IAAE;IAAE,MAAM,IAAItK,SAAS,CAAC,uIAAuI,CAAC;EAAE;EAAE,IAAIX,CAAC;IAAE+B,CAAC,GAAG,CAAC,CAAC;IAAEiB,CAAC,GAAG,CAAC,CAAC;EAAE,OAAO;IAAEkI,CAAC,EAAE,SAASA,CAACA,CAAA,EAAG;MAAE5K,CAAC,GAAGA,CAAC,CAACI,IAAI,CAACH,CAAC,CAAC;IAAE,CAAC;IAAEkB,CAAC,EAAE,SAASA,CAACA,CAAA,EAAG;MAAE,IAAIlB,CAAC,GAAGD,CAAC,CAAC4C,IAAI,CAAC,CAAC;MAAE,OAAOnB,CAAC,GAAGxB,CAAC,CAAC4C,IAAI,EAAE5C,CAAC;IAAE,CAAC;IAAEC,CAAC,EAAE,SAASA,CAACA,CAACD,CAAC,EAAE;MAAEyC,CAAC,GAAG,CAAC,CAAC,EAAEhD,CAAC,GAAGO,CAAC;IAAE,CAAC;IAAE0C,CAAC,EAAE,SAASA,CAACA,CAAA,EAAG;MAAE,IAAI;QAAElB,CAAC,IAAI,IAAI,IAAIzB,CAAC,CAAC,QAAQ,CAAC,IAAIA,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;MAAE,CAAC,SAAS;QAAE,IAAI0C,CAAC,EAAE,MAAMhD,CAAC;MAAE;IAAE;EAAE,CAAC;AAAE;AAC31B,SAAS+K,2BAA2BA,CAACxK,CAAC,EAAEwB,CAAC,EAAE;EAAE,IAAIxB,CAAC,EAAE;IAAE,IAAI,QAAQ,IAAI,OAAOA,CAAC,EAAE,OAAO4K,iBAAiB,CAAC5K,CAAC,EAAEwB,CAAC,CAAC;IAAE,IAAIzB,CAAC,GAAG,CAAC,CAAC,CAACgC,QAAQ,CAAC5B,IAAI,CAACH,CAAC,CAAC,CAACgC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAAE,OAAO,QAAQ,KAAKjC,CAAC,IAAIC,CAAC,CAACJ,WAAW,KAAKG,CAAC,GAAGC,CAAC,CAACJ,WAAW,CAACqC,IAAI,CAAC,EAAE,KAAK,KAAKlC,CAAC,IAAI,KAAK,KAAKA,CAAC,GAAG0B,KAAK,CAACI,IAAI,CAAC7B,CAAC,CAAC,GAAG,WAAW,KAAKD,CAAC,IAAI,0CAA0C,CAACmC,IAAI,CAACnC,CAAC,CAAC,GAAG6K,iBAAiB,CAAC5K,CAAC,EAAEwB,CAAC,CAAC,GAAG,KAAK,CAAC;EAAE;AAAE;AACzX,SAASoJ,iBAAiBA,CAAC5K,CAAC,EAAEwB,CAAC,EAAE;EAAE,CAAC,IAAI,IAAIA,CAAC,IAAIA,CAAC,GAAGxB,CAAC,CAACoB,MAAM,MAAMI,CAAC,GAAGxB,CAAC,CAACoB,MAAM,CAAC;EAAE,KAAK,IAAInB,CAAC,GAAG,CAAC,EAAEiB,CAAC,GAAGO,KAAK,CAACD,CAAC,CAAC,EAAEvB,CAAC,GAAGuB,CAAC,EAAEvB,CAAC,EAAE,EAAEiB,CAAC,CAACjB,CAAC,CAAC,GAAGD,CAAC,CAACC,CAAC,CAAC;EAAE,OAAOiB,CAAC;AAAE;AACnJ,IAAI2J,UAAU,GAAG,aAAatM,KAAK,CAACyG,IAAI,CAAC,aAAazG,KAAK,CAAC0G,UAAU,CAAC,UAAUC,OAAO,EAAEC,GAAG,EAAE;EAC7F,IAAIC,UAAU,GAAGxG,aAAa,CAAC,CAAC;EAChC,IAAIyG,OAAO,GAAG9G,KAAK,CAAC+G,UAAU,CAAC9G,iBAAiB,CAAC;EACjD,IAAI2E,KAAK,GAAGyE,cAAc,CAACrC,QAAQ,CAACL,OAAO,EAAEG,OAAO,CAAC;EACrD,IAAIyF,eAAe,GAAGvM,KAAK,CAACwM,QAAQ,CAAC,EAAE,CAAC;IACtCC,gBAAgB,GAAGjI,cAAc,CAAC+H,eAAe,EAAE,CAAC,CAAC;IACrDG,kBAAkB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACxCE,qBAAqB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAC7C,IAAIG,gBAAgB,GAAG5M,KAAK,CAACwM,QAAQ,CAAC,EAAE,CAAC;IACvCK,gBAAgB,GAAGrI,cAAc,CAACoI,gBAAgB,EAAE,CAAC,CAAC;IACtDE,UAAU,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAChCE,aAAa,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACrC,IAAIG,gBAAgB,GAAGhN,KAAK,CAACwM,QAAQ,CAAC,CAAC,CAAC;IACtCS,gBAAgB,GAAGzI,cAAc,CAACwI,gBAAgB,EAAE,CAAC,CAAC;IACtDE,aAAa,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACnCE,gBAAgB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACxC,IAAIG,gBAAgB,GAAGpN,KAAK,CAACwM,QAAQ,CAAC,KAAK,CAAC;IAC1Ca,gBAAgB,GAAG7I,cAAc,CAAC4I,gBAAgB,EAAE,CAAC,CAAC;IACtD9E,YAAY,GAAG+E,gBAAgB,CAAC,CAAC,CAAC;IAClCC,eAAe,GAAGD,gBAAgB,CAAC,CAAC,CAAC;EACvC,IAAIE,gBAAgB,GAAGvN,KAAK,CAACwM,QAAQ,CAAC,KAAK,CAAC;IAC1CgB,iBAAiB,GAAGhJ,cAAc,CAAC+I,gBAAgB,EAAE,CAAC,CAAC;IACvDE,cAAc,GAAGD,iBAAiB,CAAC,CAAC,CAAC;IACrCE,iBAAiB,GAAGF,iBAAiB,CAAC,CAAC,CAAC;EAC1C,IAAIG,QAAQ,GAAG;IACb/I,KAAK,EAAEA,KAAK;IACZgJ,KAAK,EAAE;MACLC,QAAQ,EAAEX,aAAa;MACvBY,SAAS,EAAEL,cAAc;MACzBM,aAAa,EAAErB,kBAAkB;MACjCsB,KAAK,EAAElB,UAAU;MACjBmB,OAAO,EAAE3F;IACX;EACF,CAAC;EACD,IAAI4F,qBAAqB,GAAG7E,cAAc,CAACnC,WAAW,CAACyG,QAAQ,CAAC;IAC9DxG,GAAG,GAAG+G,qBAAqB,CAAC/G,GAAG;IAC/BC,EAAE,GAAG8G,qBAAqB,CAAC9G,EAAE;IAC7BC,UAAU,GAAG6G,qBAAqB,CAAC7G,UAAU;EAC/CjH,cAAc,CAACiJ,cAAc,CAACzD,GAAG,CAACE,MAAM,EAAEuB,UAAU,EAAE;IACpD3D,IAAI,EAAE;EACR,CAAC,CAAC;EACF,IAAIyK,YAAY,GAAGnO,KAAK,CAACuH,MAAM,CAAC,IAAI,CAAC;EACrC,IAAI6G,WAAW,GAAGpO,KAAK,CAACuH,MAAM,CAAC,IAAI,CAAC;EACpC,IAAI8G,UAAU,GAAGrO,KAAK,CAACuH,MAAM,CAAC,IAAI,CAAC;EACnC,IAAI+G,iBAAiB,GAAGtO,KAAK,CAACuH,MAAM,CAAC,CAAC,CAAC;EACvC,IAAIuB,QAAQ,GAAGvI,WAAW,CAACsE,UAAU,CAACiI,UAAU,CAAC;EACjD,IAAIyB,gBAAgB,GAAGhO,WAAW,CAACsE,UAAU,CAAC6H,kBAAkB,CAAC;EACjE,IAAIrE,QAAQ,GAAGzD,KAAK,CAACyD,QAAQ,IAAIoF,cAAc;EAC/C,IAAI9E,iBAAiB,GAAG/D,KAAK,CAACqF,WAAW,IAAIrF,KAAK,CAACyF,aAAa,CAAC9B,KAAK,IAAIrI,YAAY,CAAC,QAAQ,CAAC;EAChG,IAAIsO,iBAAiB,GAAG5J,KAAK,CAACuF,WAAW,IAAIvF,KAAK,CAAC2F,aAAa,CAAChC,KAAK,IAAIrI,YAAY,CAAC,QAAQ,CAAC;EAChG,IAAIuO,iBAAiB,GAAG7J,KAAK,CAACwF,WAAW,IAAIxF,KAAK,CAAC4F,aAAa,CAACjC,KAAK,IAAIrI,YAAY,CAAC,QAAQ,CAAC;EAChG,IAAIwO,cAAc,GAAGrG,QAAQ,IAAIzD,KAAK,CAAC+J,SAAS,IAAI/J,KAAK,CAAC+J,SAAS,IAAI7B,UAAU,CAACjK,MAAM,GAAGyL,iBAAiB;EAC5G,IAAIM,cAAc,GAAGvG,QAAQ,IAAI,CAACS,QAAQ;EAC1C,IAAI+F,cAAc,GAAGxG,QAAQ,IAAI,CAACS,QAAQ;EAC1C,IAAIgG,OAAO,GAAG,SAASA,OAAOA,CAACtG,IAAI,EAAE;IACnC,OAAO,UAAU,CAAC7E,IAAI,CAAC6E,IAAI,CAACuG,IAAI,CAAC;EACnC,CAAC;EACD,IAAIC,MAAM,GAAG,SAASA,MAAMA,CAACC,KAAK,EAAEC,KAAK,EAAE;IACzCC,UAAU,CAAC,CAAC;IACZ,IAAIC,YAAY,GAAGvL,kBAAkB,CAACiJ,UAAU,CAAC;IACjD,IAAIuC,WAAW,GAAGvC,UAAU,CAACoC,KAAK,CAAC;IACnCE,YAAY,CAACE,MAAM,CAACJ,KAAK,EAAE,CAAC,CAAC;IAC7BnC,aAAa,CAACqC,YAAY,CAAC;IAC3B,IAAIxK,KAAK,CAACiH,QAAQ,EAAE;MAClBjH,KAAK,CAACiH,QAAQ,CAAC;QACb0D,aAAa,EAAEN,KAAK;QACpBzG,IAAI,EAAE6G;MACR,CAAC,CAAC;IACJ;EACF,CAAC;EACD,IAAIG,mBAAmB,GAAG,SAASA,mBAAmBA,CAACP,KAAK,EAAEC,KAAK,EAAE;IACnEC,UAAU,CAAC,CAAC;IACZ,IAAIM,oBAAoB,GAAG5L,kBAAkB,CAAC6I,kBAAkB,CAAC;IACjE,IAAI2C,WAAW,GAAGvC,UAAU,CAACoC,KAAK,CAAC;IACnCO,oBAAoB,CAACH,MAAM,CAACJ,KAAK,EAAE,CAAC,CAAC;IACrCvC,qBAAqB,CAAC8C,oBAAoB,CAAC;IAC3C,IAAI7K,KAAK,CAACiH,QAAQ,EAAE;MAClBjH,KAAK,CAACiH,QAAQ,CAAC;QACb0D,aAAa,EAAEN,KAAK;QACpBzG,IAAI,EAAE6G;MACR,CAAC,CAAC;IACJ;EACF,CAAC;EACD,IAAIF,UAAU,GAAG,SAASA,UAAUA,CAAA,EAAG;IACrC,IAAIhB,YAAY,CAACzG,OAAO,EAAE;MACxByG,YAAY,CAACzG,OAAO,CAACtF,KAAK,GAAG,EAAE;IACjC;EACF,CAAC;EACD,IAAIsN,UAAU,GAAG,SAASA,UAAUA,CAACC,KAAK,EAAE;IAC1C,IAAIC,CAAC,GAAG,IAAI;IACZ,IAAIC,EAAE,GAAG,CAAC;IACV,IAAIC,KAAK,GAAG5P,YAAY,CAAC,eAAe,CAAC;IACzC,IAAIyP,KAAK,IAAI,CAAC,EAAE;MACd,OAAO,IAAI,CAAC3K,MAAM,CAAC8K,KAAK,CAAC,CAAC,CAAC,CAAC;IAC9B;IACA,IAAInO,CAAC,GAAGoO,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACN,KAAK,CAAC,GAAGI,IAAI,CAACE,GAAG,CAACL,CAAC,CAAC,CAAC;IACjD,IAAIM,aAAa,GAAGC,UAAU,CAAC,CAACR,KAAK,GAAGI,IAAI,CAACK,GAAG,CAACR,CAAC,EAAEjO,CAAC,CAAC,EAAE0O,OAAO,CAACR,EAAE,CAAC,CAAC;IACpE,OAAO,EAAE,CAAC7K,MAAM,CAACkL,aAAa,EAAE,GAAG,CAAC,CAAClL,MAAM,CAAC8K,KAAK,CAACnO,CAAC,CAAC,CAAC;EACvD,CAAC;EACD,IAAI2O,YAAY,GAAG,SAASA,YAAYA,CAACrB,KAAK,EAAE;IAC9C;IACA,IAAIrK,KAAK,CAACyG,cAAc,IAAIzG,KAAK,CAACyG,cAAc,CAAC;MAC/CkE,aAAa,EAAEN,KAAK;MACpBjB,KAAK,EAAElB;IACT,CAAC,CAAC,KAAK,KAAK,EAAE;MACZ;IACF;IACA,IAAIsC,YAAY,GAAG,EAAE;IACrB,IAAIxK,KAAK,CAAC4E,QAAQ,EAAE;MAClB4F,YAAY,GAAGtC,UAAU,GAAGjJ,kBAAkB,CAACiJ,UAAU,CAAC,GAAG,EAAE;IACjE;IACA,IAAIyD,aAAa,GAAGtB,KAAK,CAACuB,YAAY,GAAGvB,KAAK,CAACuB,YAAY,CAACxC,KAAK,GAAGiB,KAAK,CAACwB,MAAM,CAACzC,KAAK;IACtF,KAAK,IAAIrM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4O,aAAa,CAAC1N,MAAM,EAAElB,CAAC,EAAE,EAAE;MAC7C,IAAI6G,IAAI,GAAG+H,aAAa,CAAC5O,CAAC,CAAC;MAC3B,IAAI+O,aAAa,GAAG9L,KAAK,CAAC4E,QAAQ,GAAG,CAACmH,cAAc,CAACnI,IAAI,CAAC,IAAIoI,QAAQ,CAACpI,IAAI,CAAC,GAAGoI,QAAQ,CAACpI,IAAI,CAAC;MAC7F,IAAIkI,aAAa,EAAE;QACjBlI,IAAI,CAACqI,SAAS,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACxI,IAAI,CAAC;QACjD4G,YAAY,CAAC9K,IAAI,CAACkE,IAAI,CAAC;MACzB;IACF;IACAuE,aAAa,CAACqC,YAAY,CAAC;IAC3B,IAAI7O,WAAW,CAACsE,UAAU,CAACuK,YAAY,CAAC,IAAIxK,KAAK,CAAC+E,IAAI,EAAE;MACtDsH,MAAM,CAAC7B,YAAY,CAAC;IACtB;IACA,IAAIxK,KAAK,CAAC6G,QAAQ,EAAE;MAClB7G,KAAK,CAAC6G,QAAQ,CAAC;QACb8D,aAAa,EAAEN,KAAK;QACpBjB,KAAK,EAAEoB;MACT,CAAC,CAAC;IACJ;IACAD,UAAU,CAAC,CAAC;IACZ7B,eAAe,CAAC,KAAK,CAAC;IACtB,IAAI1I,KAAK,CAACmD,IAAI,KAAK,OAAO,IAAIqH,YAAY,CAACvM,MAAM,GAAG,CAAC,EAAE;MACrDsL,YAAY,CAACzG,OAAO,CAAClC,KAAK,CAAC0L,OAAO,GAAG,MAAM;IAC7C;EACF,CAAC;EACD,IAAIP,cAAc,GAAG,SAASA,cAAcA,CAACnI,IAAI,EAAE;IACjD,OAAOsE,UAAU,CAACqE,IAAI,CAAC,UAAUhN,CAAC,EAAE;MAClC,OAAOA,CAAC,CAACT,IAAI,GAAGS,CAAC,CAAC4K,IAAI,GAAG5K,CAAC,CAACY,IAAI,KAAKyD,IAAI,CAAC9E,IAAI,GAAG8E,IAAI,CAACuG,IAAI,GAAGvG,IAAI,CAACzD,IAAI;IACvE,CAAC,CAAC;EACJ,CAAC;EACD,IAAI6L,QAAQ,GAAG,SAASA,QAAQA,CAACpI,IAAI,EAAE;IACrC,IAAI5D,KAAK,CAACgF,WAAW,IAAIpB,IAAI,CAACzD,IAAI,GAAGH,KAAK,CAACgF,WAAW,EAAE;MACtD,IAAIwH,OAAO,GAAG;QACZnM,QAAQ,EAAE,OAAO;QACjBoM,OAAO,EAAEzM,KAAK,CAACiF,6BAA6B,CAACyH,OAAO,CAAC,KAAK,EAAE9I,IAAI,CAAC9E,IAAI,CAAC;QACtE6N,MAAM,EAAE3M,KAAK,CAACkF,4BAA4B,CAACwH,OAAO,CAAC,KAAK,EAAE5B,UAAU,CAAC9K,KAAK,CAACgF,WAAW,CAAC,CAAC;QACxF4H,MAAM,EAAE;MACV,CAAC;MACD,IAAI5M,KAAK,CAACmD,IAAI,KAAK,UAAU,EAAE;QAC7BqG,WAAW,CAAC1G,OAAO,CAAC+J,IAAI,CAACL,OAAO,CAAC;MACnC;MACAxM,KAAK,CAAC+G,gBAAgB,IAAI/G,KAAK,CAAC+G,gBAAgB,CAACnD,IAAI,CAAC;MACtD,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb,CAAC;EACD,IAAIyI,MAAM,GAAG,SAASA,MAAMA,CAACjD,KAAK,EAAE;IAClCA,KAAK,GAAGA,KAAK,IAAIlB,UAAU;IAC3B,IAAIkB,KAAK,IAAIA,KAAK,CAAC0D,WAAW,EAAE;MAC9B1D,KAAK,GAAGlB,UAAU;IACpB;IACA,IAAIlI,KAAK,CAAC6F,YAAY,EAAE;MACtB,IAAI7F,KAAK,CAAC+J,SAAS,EAAE;QACnBL,iBAAiB,GAAGN,KAAK,CAACnL,MAAM,EAAEiB,cAAc,CAAC,mBAAmB,CAAC;MACvE;MACA,IAAIc,KAAK,CAACgH,aAAa,EAAE;QACvBhH,KAAK,CAACgH,aAAa,CAAC;UAClBoC,KAAK,EAAEA,KAAK;UACZ2D,OAAO,EAAE;YACPC,KAAK,EAAEA,KAAK;YACZhN,KAAK,EAAEA;UACT;QACF,CAAC,CAAC;MACJ;IACF,CAAC,MAAM;MACL8I,iBAAiB,CAAC,IAAI,CAAC;MACvB,IAAImE,GAAG,GAAG,IAAIC,cAAc,CAAC,CAAC;MAC9B,IAAIC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC7B,IAAIpN,KAAK,CAACsG,cAAc,EAAE;QACxBtG,KAAK,CAACsG,cAAc,CAAC;UACnB2G,GAAG,EAAEA,GAAG;UACRE,QAAQ,EAAEA;QACZ,CAAC,CAAC;MACJ;MACA,IAAIE,SAAS,GAAGjG,0BAA0B,CAACgC,KAAK,CAAC;QAC/CkE,KAAK;MACP,IAAI;QACF,KAAKD,SAAS,CAAC7F,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC8F,KAAK,GAAGD,SAAS,CAACtP,CAAC,CAAC,CAAC,EAAE0B,IAAI,GAAG;UAClD,IAAImE,IAAI,GAAG0J,KAAK,CAAC9P,KAAK;UACtB2P,QAAQ,CAACI,MAAM,CAACvN,KAAK,CAAClB,IAAI,EAAE8E,IAAI,EAAEA,IAAI,CAAC9E,IAAI,CAAC;QAC9C;MACF,CAAC,CAAC,OAAO0O,GAAG,EAAE;QACZH,SAAS,CAACvQ,CAAC,CAAC0Q,GAAG,CAAC;MAClB,CAAC,SAAS;QACRH,SAAS,CAAC9N,CAAC,CAAC,CAAC;MACf;MACA0N,GAAG,CAACZ,MAAM,CAACoB,gBAAgB,CAAC,UAAU,EAAE,UAAUpD,KAAK,EAAE;QACvD,IAAIA,KAAK,CAACqD,gBAAgB,EAAE;UAC1B,IAAIzE,QAAQ,GAAGkC,IAAI,CAACwC,KAAK,CAACtD,KAAK,CAACuD,MAAM,GAAG,GAAG,GAAGvD,KAAK,CAACwD,KAAK,CAAC;UAC3DtF,gBAAgB,CAACU,QAAQ,CAAC;UAC1B,IAAIjJ,KAAK,CAAC8G,UAAU,EAAE;YACpB9G,KAAK,CAAC8G,UAAU,CAAC;cACf6D,aAAa,EAAEN,KAAK;cACpBpB,QAAQ,EAAEA;YACZ,CAAC,CAAC;UACJ;QACF;MACF,CAAC,CAAC;MACFgE,GAAG,CAACa,kBAAkB,GAAG,YAAY;QACnC,IAAIb,GAAG,CAACc,UAAU,KAAK,CAAC,EAAE;UACxBxF,gBAAgB,CAAC,CAAC,CAAC;UACnBO,iBAAiB,CAAC,KAAK,CAAC;UACxB,IAAImE,GAAG,CAACe,MAAM,IAAI,GAAG,IAAIf,GAAG,CAACe,MAAM,GAAG,GAAG,EAAE;YACzC,IAAIhO,KAAK,CAAC+J,SAAS,EAAE;cACnBL,iBAAiB,GAAGN,KAAK,CAACnL,MAAM,EAAEiB,cAAc,CAAC,mBAAmB,CAAC;YACvE;YACA,IAAIc,KAAK,CAAC0G,QAAQ,EAAE;cAClB1G,KAAK,CAAC0G,QAAQ,CAAC;gBACbuG,GAAG,EAAEA,GAAG;gBACR7D,KAAK,EAAEA;cACT,CAAC,CAAC;YACJ;UACF,CAAC,MAAM,IAAIpJ,KAAK,CAAC2G,OAAO,EAAE;YACxB3G,KAAK,CAAC2G,OAAO,CAAC;cACZsG,GAAG,EAAEA,GAAG;cACR7D,KAAK,EAAEA;YACT,CAAC,CAAC;UACJ;UACA4D,KAAK,CAAC,CAAC;UACPjF,qBAAqB,CAAC,UAAUkG,iBAAiB,EAAE;YACjD,OAAO,EAAE,CAAC7N,MAAM,CAACnB,kBAAkB,CAACgP,iBAAiB,CAAC,EAAEhP,kBAAkB,CAACmK,KAAK,CAAC,CAAC;UACpF,CAAC,CAAC;QACJ;MACF,CAAC;MACD6D,GAAG,CAACiB,IAAI,CAAC,MAAM,EAAElO,KAAK,CAAC2E,GAAG,EAAE,IAAI,CAAC;MACjC,IAAI3E,KAAK,CAACuG,YAAY,EAAE;QACtBvG,KAAK,CAACuG,YAAY,CAAC;UACjB0G,GAAG,EAAEA,GAAG;UACRE,QAAQ,EAAEA;QACZ,CAAC,CAAC;MACJ;MACAF,GAAG,CAAC9H,eAAe,GAAGnF,KAAK,CAACmF,eAAe;MAC3C8H,GAAG,CAACkB,IAAI,CAAChB,QAAQ,CAAC;IACpB;EACF,CAAC;EACD,IAAIH,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAG;IAC3B7E,aAAa,CAAC,EAAE,CAAC;IACjBJ,qBAAqB,CAAC,EAAE,CAAC;IACzBe,iBAAiB,CAAC,KAAK,CAAC;IACxB9I,KAAK,CAAC4G,OAAO,IAAI5G,KAAK,CAAC4G,OAAO,CAAC,CAAC;IAChC2D,UAAU,CAAC,CAAC;EACd,CAAC;EACD,IAAI6D,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;IAC7B7E,YAAY,CAACzG,OAAO,CAACuL,KAAK,CAAC,CAAC;EAC9B,CAAC;EACD,IAAIC,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAG;IAC/B5F,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EACD,IAAI6F,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;IAC7B7F,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;EACD,IAAI8F,UAAU,GAAG,SAASC,SAASA,CAACpE,KAAK,EAAE;IACzC,IAAIA,KAAK,CAACqE,IAAI,KAAK,OAAO,IAAIrE,KAAK,CAACqE,IAAI,KAAK,aAAa,EAAE;MAC1DN,MAAM,CAAC,CAAC;IACV;EACF,CAAC;EACD,IAAIO,YAAY,GAAG,SAASC,WAAWA,CAACvE,KAAK,EAAE;IAC7C,IAAI,CAAC5G,QAAQ,EAAE;MACb4G,KAAK,CAACuB,YAAY,CAACiD,UAAU,GAAG,MAAM;MACtCxE,KAAK,CAACyE,eAAe,CAAC,CAAC;MACvBzE,KAAK,CAAC0E,cAAc,CAAC,CAAC;IACxB;EACF,CAAC;EACD,IAAIC,WAAW,GAAG,SAASC,UAAUA,CAAC5E,KAAK,EAAE;IAC3C,IAAI,CAAC5G,QAAQ,EAAE;MACb4G,KAAK,CAACuB,YAAY,CAACiD,UAAU,GAAG,MAAM;MACtC,CAACpM,UAAU,CAAC,CAAC,IAAI5G,UAAU,CAACqT,QAAQ,CAACzF,UAAU,CAAC3G,OAAO,EAAE,wBAAwB,CAAC;MAClF2G,UAAU,CAAC3G,OAAO,CAACqM,YAAY,CAAC,kBAAkB,EAAE,IAAI,CAAC;MACzD9E,KAAK,CAACyE,eAAe,CAAC,CAAC;MACvBzE,KAAK,CAAC0E,cAAc,CAAC,CAAC;IACxB;EACF,CAAC;EACD,IAAIK,YAAY,GAAG,SAASC,WAAWA,CAAChF,KAAK,EAAE;IAC7C,IAAI,CAAC5G,QAAQ,EAAE;MACb4G,KAAK,CAACuB,YAAY,CAACiD,UAAU,GAAG,MAAM;MACtC,CAACpM,UAAU,CAAC,CAAC,IAAI5G,UAAU,CAACyT,WAAW,CAAC7F,UAAU,CAAC3G,OAAO,EAAE,wBAAwB,CAAC;MACrF2G,UAAU,CAAC3G,OAAO,CAACqM,YAAY,CAAC,kBAAkB,EAAE,KAAK,CAAC;IAC5D;EACF,CAAC;EACD,IAAII,OAAO,GAAG,SAASC,MAAMA,CAACnF,KAAK,EAAE;IACnC,IAAIrK,KAAK,CAACyD,QAAQ,EAAE;MAClB;IACF;IACA,CAAChB,UAAU,CAAC,CAAC,IAAI5G,UAAU,CAACyT,WAAW,CAAC7F,UAAU,CAAC3G,OAAO,EAAE,wBAAwB,CAAC;IACrF2G,UAAU,CAAC3G,OAAO,CAACqM,YAAY,CAAC,kBAAkB,EAAE,KAAK,CAAC;IAC1D9E,KAAK,CAACyE,eAAe,CAAC,CAAC;IACvBzE,KAAK,CAAC0E,cAAc,CAAC,CAAC;;IAEtB;IACA,IAAI/O,KAAK,CAACwG,YAAY,IAAIxG,KAAK,CAACwG,YAAY,CAAC6D,KAAK,CAAC,KAAK,KAAK,EAAE;MAC7D;IACF;IACA,IAAIjB,KAAK,GAAGiB,KAAK,CAACuB,YAAY,GAAGvB,KAAK,CAACuB,YAAY,CAACxC,KAAK,GAAGiB,KAAK,CAACwB,MAAM,CAACzC,KAAK;IAC9E,IAAIqG,SAAS,GAAGzP,KAAK,CAAC4E,QAAQ,IAAIjJ,WAAW,CAACuE,OAAO,CAACgI,UAAU,CAAC,IAAIkB,KAAK,IAAIA,KAAK,CAACnL,MAAM,KAAK,CAAC;IAChGwR,SAAS,IAAI/D,YAAY,CAACrB,KAAK,CAAC;EAClC,CAAC;EACD,IAAIqF,qBAAqB,GAAG,SAASA,qBAAqBA,CAAA,EAAG;IAC3D,CAACjM,QAAQ,IAAIS,QAAQ,GAAGmI,MAAM,CAAC,CAAC,GAAG9C,YAAY,CAACzG,OAAO,CAACuL,KAAK,CAAC,CAAC;EACjE,CAAC;EACDjT,KAAK,CAACwH,mBAAmB,CAACZ,GAAG,EAAE,YAAY;IACzC,OAAO;MACLhC,KAAK,EAAEA,KAAK;MACZqM,MAAM,EAAEA,MAAM;MACdW,KAAK,EAAEA,KAAK;MACZlC,UAAU,EAAEA,UAAU;MACtBY,YAAY,EAAEA,YAAY;MAC1BiE,QAAQ,EAAE,SAASA,QAAQA,CAAA,EAAG;QAC5B,OAAOpG,YAAY,CAACzG,OAAO;MAC7B,CAAC;MACD8M,UAAU,EAAE,SAASA,UAAUA,CAAA,EAAG;QAChC,OAAOnG,UAAU,CAAC3G,OAAO;MAC3B,CAAC;MACD+M,QAAQ,EAAE,SAASA,QAAQA,CAAA,EAAG;QAC5B,OAAO3H,UAAU;MACnB,CAAC;MACD4H,QAAQ,EAAE,SAASA,QAAQA,CAAC1G,KAAK,EAAE;QACjC,OAAOjB,aAAa,CAACiB,KAAK,IAAI,EAAE,CAAC;MACnC,CAAC;MACD2G,gBAAgB,EAAE,SAASA,gBAAgBA,CAAA,EAAG;QAC5C,OAAOjI,kBAAkB;MAC3B,CAAC;MACDkI,gBAAgB,EAAE,SAASA,gBAAgBA,CAAC5G,KAAK,EAAE;QACjD,OAAOrB,qBAAqB,CAACqB,KAAK,IAAI,EAAE,CAAC;MAC3C;IACF,CAAC;EACH,CAAC,CAAC;EACF,IAAI6G,kBAAkB,GAAG,SAASA,kBAAkBA,CAAA,EAAG;IACrD,IAAIC,oBAAoB,GAAGlQ,KAAK,CAACyF,aAAa;MAC5C5E,SAAS,GAAGqP,oBAAoB,CAACrP,SAAS;MAC1CD,KAAK,GAAGsP,oBAAoB,CAACtP,KAAK;MAClCuP,KAAK,GAAGD,oBAAoB,CAACxK,IAAI;MACjClC,QAAQ,GAAG0M,oBAAoB,CAAC1M,QAAQ;IAC1C,IAAI4M,sBAAsB,GAAGnO,UAAU,CAAC;MACtCpB,SAAS,EAAE2B,EAAE,CAAC,mBAAmB;IACnC,CAAC,EAAED,GAAG,CAAC,mBAAmB,CAAC,CAAC;IAC5B,IAAIoB,KAAK,GAAGH,QAAQ,GAAG,aAAapI,KAAK,CAAC6H,aAAa,CAAC,MAAM,EAAErF,QAAQ,CAAC,CAAC,CAAC,EAAEwS,sBAAsB,EAAE;MACnGC,uBAAuB,EAAE;QACvBC,MAAM,EAAE;MACV;IACF,CAAC,CAAC,CAAC,GAAG,aAAalV,KAAK,CAAC6H,aAAa,CAAC,MAAM,EAAEmN,sBAAsB,EAAErM,iBAAiB,CAAC;IACzF,IAAIwM,UAAU,GAAGtO,UAAU,CAAC;MAC1BD,GAAG,EAAEuH,YAAY;MACjBY,IAAI,EAAE,MAAM;MACZqG,QAAQ,EAAE,SAASA,QAAQA,CAAC1T,CAAC,EAAE;QAC7B,OAAO4O,YAAY,CAAC5O,CAAC,CAAC;MACxB,CAAC;MACD8H,QAAQ,EAAE5E,KAAK,CAAC4E,QAAQ;MACxBC,MAAM,EAAE7E,KAAK,CAAC6E,MAAM;MACpBpB,QAAQ,EAAEqG;IACZ,CAAC,EAAEvH,GAAG,CAAC,OAAO,CAAC,CAAC;IAChB,IAAIkO,KAAK,GAAG,aAAarV,KAAK,CAAC6H,aAAa,CAAC,OAAO,EAAEsN,UAAU,CAAC;IACjE,IAAIG,eAAe,GAAGzO,UAAU,CAAC;MAC/BpB,SAAS,EAAE2B,EAAE,CAAC,YAAY,EAAE;QAC1BgB,QAAQ,EAAEA;MACZ,CAAC,CAAC;MACF,aAAa,EAAE;IACjB,CAAC,EAAEjB,GAAG,CAAC,YAAY,CAAC,CAAC;IACrB,IAAImD,IAAI,GAAGyK,KAAK,IAAI,aAAa/U,KAAK,CAAC6H,aAAa,CAAClH,QAAQ,EAAE2U,eAAe,CAAC;IAC/E,IAAIvM,UAAU,GAAGvI,SAAS,CAAC+U,UAAU,CAACjL,IAAI,EAAEyB,aAAa,CAAC,CAAC,CAAC,EAAEuJ,eAAe,CAAC,EAAE;MAC9E1Q,KAAK,EAAEA;IACT,CAAC,CAAC;IACF,IAAI4Q,iBAAiB,GAAG3O,UAAU,CAAC;MACjCpB,SAAS,EAAEnF,UAAU,CAACmF,SAAS,EAAE2B,EAAE,CAAC,cAAc,EAAE;QAClDgB,QAAQ,EAAEA,QAAQ;QAClBC,QAAQ,EAAEA,QAAQ;QAClB5C,SAAS,EAAEA,SAAS;QACpB6C,YAAY,EAAEA;MAChB,CAAC,CAAC,CAAC;MACH9C,KAAK,EAAEA,KAAK;MACZiQ,OAAO,EAAEzC,MAAM;MACfK,SAAS,EAAE,SAASA,SAASA,CAAC3R,CAAC,EAAE;QAC/B,OAAO0R,UAAU,CAAC1R,CAAC,CAAC;MACtB,CAAC;MACDwR,OAAO,EAAEA,OAAO;MAChBC,MAAM,EAAEA,MAAM;MACduC,QAAQ,EAAE,CAAC;MACX,iBAAiB,EAAErN,QAAQ;MAC3B,cAAc,EAAEC;IAClB,CAAC,EAAEnB,GAAG,CAAC,cAAc,CAAC,CAAC;IACvB,OAAO,aAAanH,KAAK,CAAC6H,aAAa,CAAC,MAAM,EAAE2N,iBAAiB,EAAEH,KAAK,EAAEtM,UAAU,EAAER,KAAK,EAAE,aAAavI,KAAK,CAAC6H,aAAa,CAAC7G,MAAM,EAAE,IAAI,CAAC,CAAC;EAC9I,CAAC;EACD,IAAI2U,aAAa,GAAG,SAASA,aAAaA,CAACjU,CAAC,EAAEkU,YAAY,EAAE1G,KAAK,EAAE;IACjE,IAAI0G,YAAY,CAAC3Q,QAAQ,KAAK,SAAS,EAAE;MACvC+J,MAAM,CAACtN,CAAC,EAAEwN,KAAK,CAAC;IAClB,CAAC,MAAM;MACLM,mBAAmB,CAAC9N,CAAC,EAAEwN,KAAK,CAAC;IAC/B;EACF,CAAC;EACD,IAAI2G,UAAU,GAAG,SAASA,UAAUA,CAACrN,IAAI,EAAE0G,KAAK,EAAE0G,YAAY,EAAE;IAC9D,IAAIE,GAAG,GAAGtN,IAAI,CAAC9E,IAAI,GAAG8E,IAAI,CAACuG,IAAI,GAAGvG,IAAI,CAACzD,IAAI;IAC3C,IAAIgR,cAAc,GAAGlP,UAAU,CAAC;MAC9BmP,IAAI,EAAE,cAAc;MACpBvQ,SAAS,EAAE2B,EAAE,CAAC,WAAW,CAAC;MAC1B6O,GAAG,EAAEzN,IAAI,CAACqI,SAAS;MACnBqF,KAAK,EAAEtR,KAAK,CAACoF;IACf,CAAC,EAAE7C,GAAG,CAAC,WAAW,CAAC,CAAC;IACpB,IAAIgP,OAAO,GAAGrH,OAAO,CAACtG,IAAI,CAAC,GAAG,aAAaxI,KAAK,CAAC6H,aAAa,CAAC,KAAK,EAAErF,QAAQ,CAAC,CAAC,CAAC,EAAEuT,cAAc,EAAE;MACjGK,GAAG,EAAE5N,IAAI,CAAC9E;IACZ,CAAC,CAAC,CAAC,GAAG,IAAI;IACV,IAAI2S,YAAY,GAAGxP,UAAU,CAACM,GAAG,CAAC,SAAS,CAAC,CAAC;IAC7C,IAAImP,aAAa,GAAGzP,UAAU,CAACM,GAAG,CAAC,UAAU,CAAC,CAAC;IAC/C,IAAIoP,aAAa,GAAG1P,UAAU,CAAC;MAC7BpB,SAAS,EAAE2B,EAAE,CAAC,UAAU;IAC1B,CAAC,EAAED,GAAG,CAAC,UAAU,CAAC,CAAC;IACnB,IAAIqP,YAAY,GAAG3P,UAAU,CAACM,GAAG,CAAC,SAAS,CAAC,CAAC;IAC7C,IAAIsB,QAAQ,GAAG,aAAazI,KAAK,CAAC6H,aAAa,CAAC,KAAK,EAAE0O,aAAa,EAAE/N,IAAI,CAAC9E,IAAI,CAAC;IAChF,IAAIqB,IAAI,GAAG,aAAa/E,KAAK,CAAC6H,aAAa,CAAC,KAAK,EAAEyO,aAAa,EAAE5G,UAAU,CAAClH,IAAI,CAACzD,IAAI,CAAC,CAAC;IACxF,IAAI0R,WAAW,GAAG,aAAazW,KAAK,CAAC6H,aAAa,CAAC,KAAK,EAAEwO,YAAY,EAAE,aAAarW,KAAK,CAAC6H,aAAa,CAAC,KAAK,EAAE0O,aAAa,EAAE,GAAG,EAAE/N,IAAI,CAAC9E,IAAI,CAAC,EAAE,aAAa1D,KAAK,CAAC6H,aAAa,CAAC,MAAM,EAAEyO,aAAa,EAAE5G,UAAU,CAAClH,IAAI,CAACzD,IAAI,CAAC,CAAC,EAAE,aAAa/E,KAAK,CAAC6H,aAAa,CAACrB,KAAK,EAAE;MACtQf,SAAS,EAAE,yBAAyB;MACpCrD,KAAK,EAAEwT,YAAY,CAACxT,KAAK;MACzB6C,QAAQ,EAAE2Q,YAAY,CAAC3Q,QAAQ;MAC/ByR,EAAE,EAAEvP,GAAG,CAAC,OAAO,CAAC;MAChB5B,gBAAgB,EAAE;QAChBoR,MAAM,EAAEhJ;MACV;IACF,CAAC,CAAC,CAAC;IACH,IAAIiJ,YAAY,GAAG,aAAa5W,KAAK,CAAC6H,aAAa,CAAC,KAAK,EAAE2O,YAAY,EAAE,aAAaxW,KAAK,CAAC6H,aAAa,CAACnH,MAAM,EAAE;MAChHqO,IAAI,EAAE,QAAQ;MACdzE,IAAI,EAAE1F,KAAK,CAAC8E,UAAU,IAAI,aAAa1J,KAAK,CAAC6H,aAAa,CAACjH,SAAS,EAAE,IAAI,CAAC;MAC3EiW,IAAI,EAAE,IAAI;MACVC,OAAO,EAAE,IAAI;MACb7R,QAAQ,EAAE,QAAQ;MAClBwQ,OAAO,EAAE,SAASA,OAAOA,CAAC/T,CAAC,EAAE;QAC3B,OAAOiU,aAAa,CAACjU,CAAC,EAAEkU,YAAY,EAAE1G,KAAK,CAAC;MAC9C,CAAC;MACD7G,QAAQ,EAAEA,QAAQ;MAClBqO,EAAE,EAAEvP,GAAG,CAAC,cAAc,CAAC;MACvB5B,gBAAgB,EAAE;QAChBoR,MAAM,EAAEhJ;MACV,CAAC;MACDoJ,QAAQ,EAAE1P,UAAU,CAAC;IACvB,CAAC,CAAC,CAAC;IACH,IAAIY,OAAO,GAAG,aAAajI,KAAK,CAAC6H,aAAa,CAAC7H,KAAK,CAACgX,QAAQ,EAAE,IAAI,EAAEb,OAAO,EAAEM,WAAW,EAAEG,YAAY,CAAC;IACxG,IAAIhS,KAAK,CAACmG,YAAY,EAAE;MACtB,IAAIkM,qBAAqB,GAAG;QAC1BpL,QAAQ,EAAE,SAASA,QAAQA,CAACoD,KAAK,EAAE;UACjC,OAAOD,MAAM,CAACC,KAAK,EAAEC,KAAK,CAAC;QAC7B,CAAC;QACDgI,cAAc,EAAEf,OAAO;QACvBgB,eAAe,EAAE1O,QAAQ;QACzB2O,WAAW,EAAErS,IAAI;QACjBsS,aAAa,EAAET,YAAY;QAC3BlH,UAAU,EAAEA,UAAU,CAAClH,IAAI,CAACzD,IAAI,CAAC;QACjCuS,OAAO,EAAErP,OAAO;QAChBiH,KAAK,EAAEA,KAAK;QACZtK,KAAK,EAAEA;MACT,CAAC;MACDqD,OAAO,GAAG1H,WAAW,CAACgX,aAAa,CAAC3S,KAAK,CAACmG,YAAY,EAAEvC,IAAI,EAAEyO,qBAAqB,CAAC;IACtF;IACA,IAAIO,SAAS,GAAG3Q,UAAU,CAAC;MACzBiP,GAAG,EAAEA,GAAG;MACRrQ,SAAS,EAAE2B,EAAE,CAAC,MAAM;IACtB,CAAC,EAAED,GAAG,CAAC,MAAM,CAAC,CAAC;IACf,OAAO,aAAanH,KAAK,CAAC6H,aAAa,CAAC,KAAK,EAAE2P,SAAS,EAAEvP,OAAO,CAAC;EACpE,CAAC;EACD,IAAIwP,WAAW,GAAG,SAASA,WAAWA,CAAA,EAAG;IACvC,IAAI7B,YAAY,GAAG;MACjB3Q,QAAQ,EAAE,SAAS;MACnB7C,KAAK,EAAElC,YAAY,CAAC,SAAS,CAAC,IAAI;IACpC,CAAC;IACD,IAAI+H,OAAO,GAAG6E,UAAU,CAAC4K,GAAG,CAAC,UAAUlP,IAAI,EAAE0G,KAAK,EAAE;MAClD,OAAO2G,UAAU,CAACrN,IAAI,EAAE0G,KAAK,EAAE0G,YAAY,CAAC;IAC9C,CAAC,CAAC;IACF,OAAO,aAAa5V,KAAK,CAAC6H,aAAa,CAAC,KAAK,EAAE,IAAI,EAAEI,OAAO,CAAC;EAC/D,CAAC;EACD,IAAI0P,mBAAmB,GAAG,SAASA,mBAAmBA,CAAA,EAAG;IACvD,IAAI/B,YAAY,GAAG;MACjB3Q,QAAQ,EAAE,SAAS;MACnB7C,KAAK,EAAElC,YAAY,CAAC,WAAW,CAAC,IAAI;IACtC,CAAC;IACD,IAAI+H,OAAO,GAAGyE,kBAAkB,IAAIA,kBAAkB,CAACgL,GAAG,CAAC,UAAUlP,IAAI,EAAE0G,KAAK,EAAE;MAChF,OAAO2G,UAAU,CAACrN,IAAI,EAAE0G,KAAK,EAAE0G,YAAY,CAAC;IAC9C,CAAC,CAAC;IACF,OAAO,aAAa5V,KAAK,CAAC6H,aAAa,CAAC,KAAK,EAAE,IAAI,EAAEI,OAAO,CAAC;EAC/D,CAAC;EACD,IAAI2P,kBAAkB,GAAG,SAASA,kBAAkBA,CAAA,EAAG;IACrD,OAAOhT,KAAK,CAACoG,aAAa,IAAI,CAAClC,QAAQ,IAAI,CAACyF,gBAAgB,GAAGhO,WAAW,CAACgX,aAAa,CAAC3S,KAAK,CAACoG,aAAa,EAAEpG,KAAK,CAAC,GAAG,IAAI;EAC7H,CAAC;EACD,IAAIiT,wBAAwB,GAAG,SAASA,wBAAwBA,CAAA,EAAG;IACjE,IAAIjT,KAAK,CAACqG,mBAAmB,EAAE;MAC7B,IAAI6M,iCAAiC,GAAG;QACtCjK,QAAQ,EAAEX,aAAa;QACvBtI,KAAK,EAAEA;MACT,CAAC;MACD,OAAOrE,WAAW,CAACgX,aAAa,CAAC3S,KAAK,CAACqG,mBAAmB,EAAE6M,iCAAiC,CAAC;IAChG;IACA,OAAO,aAAa9X,KAAK,CAAC6H,aAAa,CAAC9G,WAAW,EAAE;MACnDqB,KAAK,EAAE8K,aAAa;MACpB6K,SAAS,EAAE,KAAK;MAChBrB,EAAE,EAAEvP,GAAG,CAAC,aAAa,CAAC;MACtB5B,gBAAgB,EAAE;QAChBoR,MAAM,EAAEhJ;MACV;IACF,CAAC,CAAC;EACJ,CAAC;EACD,IAAIqK,cAAc,GAAG,SAASA,cAAcA,CAAA,EAAG;IAC7C,IAAI9P,YAAY,GAAG2M,kBAAkB,CAAC,CAAC;IACvC,IAAIoD,YAAY,GAAGL,kBAAkB,CAAC,CAAC;IACvC,IAAIM,YAAY;IAChB,IAAIC,YAAY;IAChB,IAAIC,SAAS;IACb,IAAIC,iBAAiB;IACrB,IAAIC,WAAW;IACf,IAAI,CAAC1T,KAAK,CAAC+E,IAAI,EAAE;MACf,IAAIY,aAAa,GAAG3F,KAAK,CAAC2F,aAAa;MACvC,IAAIC,aAAa,GAAG5F,KAAK,CAAC4F,aAAa;MACvC,IAAIL,WAAW,GAAG,CAACI,aAAa,CAACnC,QAAQ,GAAGoG,iBAAiB,GAAG,EAAE;MAClE,IAAIpE,WAAW,GAAG,CAACI,aAAa,CAACpC,QAAQ,GAAGqG,iBAAiB,GAAG,EAAE;MAClE,IAAI8J,eAAe,GAAG1R,UAAU,CAAC;QAC/BpB,SAAS,EAAE2B,EAAE,CAAC,YAAY,EAAE;UAC1BgB,QAAQ,EAAEmC,aAAa,CAACnC;QAC1B,CAAC,CAAC;QACF,aAAa,EAAE;MACjB,CAAC,EAAEjB,GAAG,CAAC,YAAY,CAAC,CAAC;MACrB,IAAI8B,UAAU,GAAGzI,SAAS,CAAC+U,UAAU,CAAChL,aAAa,CAACD,IAAI,IAAI,aAAatK,KAAK,CAAC6H,aAAa,CAAChH,UAAU,EAAE0X,eAAe,CAAC,EAAExM,aAAa,CAAC,CAAC,CAAC,EAAEwM,eAAe,CAAC,EAAE;QAC7J3T,KAAK,EAAEA;MACT,CAAC,CAAC;MACF,IAAI4T,eAAe,GAAG3R,UAAU,CAAC;QAC/BpB,SAAS,EAAE2B,EAAE,CAAC,YAAY,EAAE;UAC1BgB,QAAQ,EAAEoC,aAAa,CAACpC;QAC1B,CAAC,CAAC;QACF,aAAa,EAAE;MACjB,CAAC,EAAEjB,GAAG,CAAC,YAAY,CAAC,CAAC;MACrB,IAAIgC,UAAU,GAAG3I,SAAS,CAAC+U,UAAU,CAAC/K,aAAa,CAACF,IAAI,IAAI,aAAatK,KAAK,CAAC6H,aAAa,CAACjH,SAAS,EAAE4X,eAAe,CAAC,EAAEzM,aAAa,CAAC,CAAC,CAAC,EAAEyM,eAAe,CAAC,EAAE;QAC5J5T,KAAK,EAAEA;MACT,CAAC,CAAC;MACFsT,YAAY,GAAG,aAAalY,KAAK,CAAC6H,aAAa,CAACnH,MAAM,EAAE;QACtDqO,IAAI,EAAE,QAAQ;QACdxG,KAAK,EAAE4B,WAAW;QAClB,aAAa,EAAE,MAAM;QACrBG,IAAI,EAAErB,UAAU;QAChBwM,OAAO,EAAExE,MAAM;QACf5I,QAAQ,EAAEuG,cAAc;QACxBpJ,KAAK,EAAE+E,aAAa,CAAC/E,KAAK;QAC1BC,SAAS,EAAE8E,aAAa,CAAC9E,SAAS;QAClCiR,EAAE,EAAEvP,GAAG,CAAC,cAAc,CAAC;QACvB5B,gBAAgB,EAAE;UAChBoR,MAAM,EAAEhJ;QACV,CAAC;QACDoJ,QAAQ,EAAE1P,UAAU,CAAC;MACvB,CAAC,CAAC;MACF8Q,YAAY,GAAG,aAAanY,KAAK,CAAC6H,aAAa,CAACnH,MAAM,EAAE;QACtDqO,IAAI,EAAE,QAAQ;QACdxG,KAAK,EAAE6B,WAAW;QAClB,aAAa,EAAE,MAAM;QACrBE,IAAI,EAAEnB,UAAU;QAChBsM,OAAO,EAAE7D,KAAK;QACdvJ,QAAQ,EAAEwG,cAAc;QACxBrJ,KAAK,EAAEgF,aAAa,CAAChF,KAAK;QAC1BC,SAAS,EAAE+E,aAAa,CAAC/E,SAAS;QAClCiR,EAAE,EAAEvP,GAAG,CAAC,cAAc,CAAC;QACvB5B,gBAAgB,EAAE;UAChBoR,MAAM,EAAEhJ;QACV,CAAC;QACDoJ,QAAQ,EAAE1P,UAAU,CAAC;MACvB,CAAC,CAAC;IACJ;IACA,IAAIyB,QAAQ,EAAE;MACZsP,SAAS,GAAGX,WAAW,CAAC,CAAC;MACzBa,WAAW,GAAGT,wBAAwB,CAAC,CAAC;IAC1C;IACA,IAAItJ,gBAAgB,EAAE;MACpB8J,iBAAiB,GAAGV,mBAAmB,CAAC,CAAC;IAC3C;IACA,IAAIc,cAAc,GAAG5R,UAAU,CAAC;MAC9BpB,SAAS,EAAEnF,UAAU,CAACsE,KAAK,CAAC8F,eAAe,EAAEtD,EAAE,CAAC,WAAW,CAAC,CAAC;MAC7D5B,KAAK,EAAEZ,KAAK,CAAC+F;IACf,CAAC,EAAExD,GAAG,CAAC,WAAW,CAAC,CAAC;IACpB,IAAIuR,MAAM,GAAG,aAAa1Y,KAAK,CAAC6H,aAAa,CAAC,KAAK,EAAE4Q,cAAc,EAAEvQ,YAAY,EAAEgQ,YAAY,EAAEC,YAAY,CAAC;IAC9G,IAAIvT,KAAK,CAACkG,cAAc,EAAE;MACxB,IAAImM,qBAAqB,GAAG;QAC1BxR,SAAS,EAAEnF,UAAU,CAAC,wBAAwB,EAAEsE,KAAK,CAAC8F,eAAe,CAAC;QACtExC,YAAY,EAAEA,YAAY;QAC1BgQ,YAAY,EAAEA,YAAY;QAC1BC,YAAY,EAAEA,YAAY;QAC1Bb,OAAO,EAAEoB,MAAM;QACf9T,KAAK,EAAEA;MACT,CAAC;MACD8T,MAAM,GAAGnY,WAAW,CAACgX,aAAa,CAAC3S,KAAK,CAACkG,cAAc,EAAEmM,qBAAqB,CAAC;IACjF;IACA,IAAItP,SAAS,GAAGd,UAAU,CAAC;MACzByC,EAAE,EAAE1E,KAAK,CAAC0E,EAAE;MACZ7D,SAAS,EAAEnF,UAAU,CAACsE,KAAK,CAACa,SAAS,EAAE2B,EAAE,CAAC,MAAM,CAAC,CAAC;MAClD5B,KAAK,EAAEZ,KAAK,CAACY;IACf,CAAC,EAAE6D,cAAc,CAACzB,aAAa,CAAChD,KAAK,CAAC,EAAEuC,GAAG,CAAC,MAAM,CAAC,CAAC;IACpD,IAAIwR,YAAY,GAAG9R,UAAU,CAAC;MAC5BD,GAAG,EAAEyH,UAAU;MACf5I,SAAS,EAAEnF,UAAU,CAACsE,KAAK,CAACgG,gBAAgB,EAAExD,EAAE,CAAC,SAAS,CAAC,CAAC;MAC5D5B,KAAK,EAAEZ,KAAK,CAACiG,YAAY;MACzB2I,WAAW,EAAE,SAASA,WAAWA,CAAC9R,CAAC,EAAE;QACnC,OAAO6R,YAAY,CAAC7R,CAAC,CAAC;MACxB,CAAC;MACDmS,UAAU,EAAE,SAASA,UAAUA,CAACnS,CAAC,EAAE;QACjC,OAAOkS,WAAW,CAAClS,CAAC,CAAC;MACvB,CAAC;MACDuS,WAAW,EAAE,SAASA,WAAWA,CAACvS,CAAC,EAAE;QACnC,OAAOsS,YAAY,CAACtS,CAAC,CAAC;MACxB,CAAC;MACD0S,MAAM,EAAE,SAASA,MAAMA,CAAC1S,CAAC,EAAE;QACzB,OAAOyS,OAAO,CAACzS,CAAC,CAAC;MACnB,CAAC;MACD,kBAAkB,EAAE;IACtB,CAAC,EAAEyF,GAAG,CAAC,SAAS,CAAC,CAAC;IAClB,OAAO,aAAanH,KAAK,CAAC6H,aAAa,CAAC,KAAK,EAAEF,SAAS,EAAE+Q,MAAM,EAAE,aAAa1Y,KAAK,CAAC6H,aAAa,CAAC,KAAK,EAAE8Q,YAAY,EAAEL,WAAW,EAAE,aAAatY,KAAK,CAAC6H,aAAa,CAAC/G,QAAQ,EAAE;MAC9K8F,GAAG,EAAEwH,WAAW;MAChB7I,gBAAgB,EAAE;QAChBoR,MAAM,EAAEhJ;MACV;IACF,CAAC,CAAC,EAAE7E,QAAQ,GAAGsP,SAAS,GAAG,IAAI,EAAE7J,gBAAgB,GAAG8J,iBAAiB,GAAG,IAAI,EAAEJ,YAAY,CAAC,CAAC;EAC9F,CAAC;EACD,IAAIW,WAAW,GAAG,SAASA,WAAWA,CAAA,EAAG;IACvC,IAAIvO,aAAa,GAAGzF,KAAK,CAACyF,aAAa;IACvC,IAAIwO,UAAU,GAAGhS,UAAU,CAAC;MAC1BpB,SAAS,EAAE2B,EAAE,CAAC,OAAO;IACvB,CAAC,EAAED,GAAG,CAAC,OAAO,CAAC,CAAC;IAChB,IAAI8C,WAAW,GAAGI,aAAa,CAACjC,QAAQ,GAAG,aAAapI,KAAK,CAAC6H,aAAa,CAAC,MAAM,EAAErF,QAAQ,CAAC,CAAC,CAAC,EAAEqW,UAAU,EAAE;MAC3G5D,uBAAuB,EAAE;QACvBC,MAAM,EAAE;MACV;IACF,CAAC,CAAC,CAAC,GAAG,aAAalV,KAAK,CAAC6H,aAAa,CAAC,MAAM,EAAEgR,UAAU,EAAElQ,iBAAiB,CAAC;IAC7E,IAAIJ,KAAK,GAAG3D,KAAK,CAAC+E,IAAI,GAAGM,WAAW,GAAG,aAAajK,KAAK,CAAC6H,aAAa,CAAC,MAAM,EAAEgR,UAAU,EAAE/P,QAAQ,GAAGlE,KAAK,CAACsF,iBAAiB,IAAI4C,UAAU,CAAC,CAAC,CAAC,CAACpJ,IAAI,GAAGuG,WAAW,CAAC;IACnK,IAAIqL,eAAe,GAAGzO,UAAU,CAAC;MAC/BpB,SAAS,EAAE2B,EAAE,CAAC,YAAY,EAAE;QAC1BgB,QAAQ,EAAEiC,aAAa,CAACjC;MAC1B,CAAC;IACH,CAAC,EAAEjB,GAAG,CAAC,YAAY,CAAC,CAAC;IACrB,IAAImD,IAAI,GAAGD,aAAa,CAACC,IAAI,GAAGD,aAAa,CAACC,IAAI,GAAG,CAACD,aAAa,CAACC,IAAI,KAAK,CAACxB,QAAQ,IAAIlE,KAAK,CAAC+E,IAAI,CAAC,GAAG,aAAa3J,KAAK,CAAC6H,aAAa,CAAClH,QAAQ,EAAE2U,eAAe,CAAC,GAAG,CAACjL,aAAa,CAACC,IAAI,IAAIxB,QAAQ,IAAI,CAAClE,KAAK,CAAC+E,IAAI,IAAI,aAAa3J,KAAK,CAAC6H,aAAa,CAAChH,UAAU,EAAEyU,eAAe,CAAC;IACrR,IAAIvM,UAAU,GAAGvI,SAAS,CAAC+U,UAAU,CAACjL,IAAI,EAAEyB,aAAa,CAAC,CAAC,CAAC,EAAEuJ,eAAe,CAAC,EAAE;MAC9E1Q,KAAK,EAAEA,KAAK;MACZkE,QAAQ,EAAEA;IACZ,CAAC,CAAC;IACF,IAAIqM,UAAU,GAAGtO,UAAU,CAAC;MAC1BD,GAAG,EAAEuH,YAAY;MACjBY,IAAI,EAAE,MAAM;MACZqG,QAAQ,EAAE,SAASA,QAAQA,CAAC1T,CAAC,EAAE;QAC7B,OAAO4O,YAAY,CAAC5O,CAAC,CAAC;MACxB,CAAC;MACD8H,QAAQ,EAAE5E,KAAK,CAAC4E,QAAQ;MACxBC,MAAM,EAAE7E,KAAK,CAAC6E,MAAM;MACpBpB,QAAQ,EAAEA;IACZ,CAAC,EAAElB,GAAG,CAAC,OAAO,CAAC,CAAC;IAChB,IAAIkO,KAAK,GAAG,CAACvM,QAAQ,IAAI,aAAa9I,KAAK,CAAC6H,aAAa,CAAC,OAAO,EAAEsN,UAAU,CAAC;IAC9E,IAAIxN,SAAS,GAAGd,UAAU,CAAC;MACzBpB,SAAS,EAAEnF,UAAU,CAACsE,KAAK,CAACa,SAAS,EAAE2B,EAAE,CAAC,MAAM,CAAC,CAAC;MAClD5B,KAAK,EAAEZ,KAAK,CAACY;IACf,CAAC,EAAE6D,cAAc,CAACzB,aAAa,CAAChD,KAAK,CAAC,EAAEuC,GAAG,CAAC,MAAM,CAAC,CAAC;IACpD,IAAI2R,gBAAgB,GAAGjS,UAAU,CAAC;MAChCpB,SAAS,EAAEnF,UAAU,CAAC+J,aAAa,CAAC5E,SAAS,EAAE2B,EAAE,CAAC,aAAa,EAAE;QAC/D0B,QAAQ,EAAEA,QAAQ;QAClBT,QAAQ,EAAEA,QAAQ;QAClBC,YAAY,EAAEA;MAChB,CAAC,CAAC,CAAC;MACH9C,KAAK,EAAE6E,aAAa,CAAC7E,KAAK;MAC1BkQ,QAAQ,EAAE,CAAC;MACXD,OAAO,EAAEnB,qBAAqB;MAC9BjB,SAAS,EAAE,SAASA,SAASA,CAAC3R,CAAC,EAAE;QAC/B,OAAO0R,UAAU,CAAC1R,CAAC,CAAC;MACtB,CAAC;MACDwR,OAAO,EAAEA,OAAO;MAChBC,MAAM,EAAEA;IACV,CAAC,EAAE9J,cAAc,CAACzB,aAAa,CAAChD,KAAK,CAAC,EAAEuC,GAAG,CAAC,aAAa,CAAC,CAAC;IAC3D,OAAO,aAAanH,KAAK,CAAC6H,aAAa,CAAC,KAAK,EAAEF,SAAS,EAAE,aAAa3H,KAAK,CAAC6H,aAAa,CAAC/G,QAAQ,EAAE;MACnG8F,GAAG,EAAEwH,WAAW;MAChBsI,EAAE,EAAEvP,GAAG,CAAC,SAAS,CAAC;MAClB5B,gBAAgB,EAAE;QAChBoR,MAAM,EAAEhJ;MACV;IACF,CAAC,CAAC,EAAE,aAAa3N,KAAK,CAAC6H,aAAa,CAAC,MAAM,EAAEiR,gBAAgB,EAAE/P,UAAU,EAAER,KAAK,EAAE8M,KAAK,EAAE,aAAarV,KAAK,CAAC6H,aAAa,CAAC7G,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;EAC3I,CAAC;EACD,IAAI4D,KAAK,CAACmD,IAAI,KAAK,UAAU,EAAE;IAC7B,OAAOiQ,cAAc,CAAC,CAAC;EACzB,CAAC,MAAM,IAAIpT,KAAK,CAACmD,IAAI,KAAK,OAAO,EAAE;IACjC,OAAO6Q,WAAW,CAAC,CAAC;EACtB;AACF,CAAC,CAAC,CAAC;AACHtM,UAAU,CAACxE,WAAW,GAAG,YAAY;AAErC,SAASwE,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}