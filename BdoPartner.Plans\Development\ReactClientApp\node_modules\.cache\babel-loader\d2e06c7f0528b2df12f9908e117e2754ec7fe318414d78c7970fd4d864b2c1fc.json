{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function isEmpty() {\n  return operate(function (source, subscriber) {\n    source.subscribe(createOperatorSubscriber(subscriber, function () {\n      subscriber.next(false);\n      subscriber.complete();\n    }, function () {\n      subscriber.next(true);\n      subscriber.complete();\n    }));\n  });\n}", "map": {"version": 3, "names": ["operate", "createOperatorSubscriber", "isEmpty", "source", "subscriber", "subscribe", "next", "complete"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\isEmpty.ts"], "sourcesContent": ["import { OperatorFunction } from '../types';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\n\n/**\n * Emits `false` if the input Observable emits any values, or emits `true` if the\n * input Observable completes without emitting any values.\n *\n * <span class=\"informal\">Tells whether any values are emitted by an Observable.</span>\n *\n * ![](isEmpty.png)\n *\n * `isEmpty` transforms an Observable that emits values into an Observable that\n * emits a single boolean value representing whether or not any values were\n * emitted by the source Observable. As soon as the source Observable emits a\n * value, `isEmpty` will emit a `false` and complete.  If the source Observable\n * completes having not emitted anything, `isEmpty` will emit a `true` and\n * complete.\n *\n * A similar effect could be achieved with {@link count}, but `isEmpty` can emit\n * a `false` value sooner.\n *\n * ## Examples\n *\n * Emit `false` for a non-empty Observable\n *\n * ```ts\n * import { Subject, isEmpty } from 'rxjs';\n *\n * const source = new Subject<string>();\n * const result = source.pipe(isEmpty());\n *\n * source.subscribe(x => console.log(x));\n * result.subscribe(x => console.log(x));\n *\n * source.next('a');\n * source.next('b');\n * source.next('c');\n * source.complete();\n *\n * // Outputs\n * // 'a'\n * // false\n * // 'b'\n * // 'c'\n * ```\n *\n * Emit `true` for an empty Observable\n *\n * ```ts\n * import { EMPTY, isEmpty } from 'rxjs';\n *\n * const result = EMPTY.pipe(isEmpty());\n * result.subscribe(x => console.log(x));\n *\n * // Outputs\n * // true\n * ```\n *\n * @see {@link count}\n * @see {@link EMPTY}\n *\n * @return A function that returns an Observable that emits boolean value\n * indicating whether the source Observable was empty or not.\n */\nexport function isEmpty<T>(): OperatorFunction<T, boolean> {\n  return operate((source, subscriber) => {\n    source.subscribe(\n      createOperatorSubscriber(\n        subscriber,\n        () => {\n          subscriber.next(false);\n          subscriber.complete();\n        },\n        () => {\n          subscriber.next(true);\n          subscriber.complete();\n        }\n      )\n    );\n  });\n}\n"], "mappings": "AACA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AA+D/D,OAAM,SAAUC,OAAOA,CAAA;EACrB,OAAOF,OAAO,CAAC,UAACG,MAAM,EAAEC,UAAU;IAChCD,MAAM,CAACE,SAAS,CACdJ,wBAAwB,CACtBG,UAAU,EACV;MACEA,UAAU,CAACE,IAAI,CAAC,KAAK,CAAC;MACtBF,UAAU,CAACG,QAAQ,EAAE;IACvB,CAAC,EACD;MACEH,UAAU,CAACE,IAAI,CAAC,IAAI,CAAC;MACrBF,UAAU,CAACG,QAAQ,EAAE;IACvB,CAAC,CACF,CACF;EACH,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}