{"ast": null, "code": "import { createErrorClass } from './createErrorClass';\nexport var EmptyError = createErrorClass(function (_super) {\n  return function EmptyErrorImpl() {\n    _super(this);\n    this.name = 'EmptyError';\n    this.message = 'no elements in sequence';\n  };\n});", "map": {"version": 3, "names": ["createErrorClass", "EmptyError", "_super", "EmptyErrorImpl", "name", "message"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\util\\EmptyError.ts"], "sourcesContent": ["import { createErrorClass } from './createErrorClass';\n\nexport interface EmptyError extends Error {}\n\nexport interface EmptyErrorCtor {\n  /**\n   * @deprecated Internal implementation detail. Do not construct error instances.\n   * Cannot be tagged as internal: https://github.com/ReactiveX/rxjs/issues/6269\n   */\n  new (): EmptyError;\n}\n\n/**\n * An error thrown when an Observable or a sequence was queried but has no\n * elements.\n *\n * @see {@link first}\n * @see {@link last}\n * @see {@link single}\n * @see {@link firstValueFrom}\n * @see {@link lastValueFrom}\n */\nexport const EmptyError: EmptyErrorCtor = createErrorClass(\n  (_super) =>\n    function EmptyErrorImpl(this: any) {\n      _super(this);\n      this.name = 'EmptyError';\n      this.message = 'no elements in sequence';\n    }\n);\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,oBAAoB;AAsBrD,OAAO,IAAMC,UAAU,GAAmBD,gBAAgB,CACxD,UAACE,MAAM;EACL,gBAASC,cAAcA,CAAA;IACrBD,MAAM,CAAC,IAAI,CAAC;IACZ,IAAI,CAACE,IAAI,GAAG,YAAY;IACxB,IAAI,CAACC,OAAO,GAAG,yBAAyB;EAC1C,CAAC;AAJD,CAIC,CACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}