{"ast": null, "code": "var _jsxFileName = \"C:\\\\git\\\\Partner-Site\\\\BdoPartner.Plans\\\\Development\\\\ReactClientApp\\\\src\\\\components\\\\admin\\\\batchReopenPopup.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { Dialog } from \"primereact/dialog\";\nimport { Button } from \"primereact/button\";\nimport { RadioButton } from \"primereact/radiobutton\";\nimport { ProgressSpinner } from \"primereact/progressspinner\";\nimport { Message } from \"primereact/message\";\nimport { PartnerPlanCycle, getCycleDisplayName } from \"../../core/enumertions/partnerPlanCycle\";\nimport questionnaireService from \"../../services/questionnaireService\";\nimport \"./batchReopenPopup.scss\";\n\n/**\r\n * Work for batch reopen completed forms when admin republish questionnaire.\r\n * Note: Set reopen status based on the latest enable cycle set in the enableCycles in questionnaire record.\r\n * @param {Object} props - Component props\r\n * @param {boolean} props.visible - Whether the popup is visible\r\n * @param {Function} props.onHide - Function to call when hiding the popup\r\n * @param {Function} props.onComplete - Function to call when operation completes\r\n * @param {string} props.questionnaireId - ID of the questionnaire being republished\r\n * @param {Object} props.surveyJson - Survey JSON definition for republishing\r\n * @param {number} props.latestEnabledCycle - Latest enabled cycle (0=Planning, 1=MidYear, 2=YearEnd)\r\n * @param {string} props.surveyName - Survey name\r\n * @param {number} props.selectedYear - Selected year\r\n * @param {string} props.enableCycles - Comma-separated enabled cycles\r\n * @returns {JSX.Element} BatchReopenPopup component\r\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const BatchReopenPopup = ({\n  visible,\n  onHide,\n  onComplete,\n  questionnaireId,\n  surveyJson,\n  latestEnabledCycle,\n  surveyName,\n  selectedYear,\n  enableCycles\n}) => {\n  _s();\n  const [completedCount, setCompletedCount] = useState(null);\n  const [selectedOption, setSelectedOption] = useState(\"\");\n  const [loading, setLoading] = useState(false);\n  const [submitting, setSubmitting] = useState(false);\n  const [error, setError] = useState(null);\n\n  // Generate dynamic options based on the latest enabled cycle\n  const getOptionsForCycle = cycle => {\n    const cycleDisplayName = getCycleDisplayName(cycle);\n    const cycleLowerCase = cycleDisplayName.toLowerCase();\n    if (cycle === PartnerPlanCycle.Planning) {\n      return [{\n        value: \"reopen_keep_responses\",\n        label: \"Reopen completed forms\",\n        description: `Change planning status from Completed to Draft (keep all responses)`\n      }, {\n        value: \"reopen_clear_responses\",\n        label: \"Reopen completed forms and clear responses\",\n        description: `Change planning status from Completed to Draft and clear all planning cycle responses`\n      }, {\n        value: \"leave_as_is\",\n        label: \"Leave completed forms as is\",\n        description: \"No changes will be made to existing completed forms\"\n      }];\n    } else {\n      // Mid Year Review and Year End Review cycles\n      return [{\n        value: \"reopen_keep_responses\",\n        label: \"Reopen completed forms\",\n        description: `Change ${cycleLowerCase} status from Completed to Reopened (keep all responses)`\n      }, {\n        value: \"reopen_clear_responses\",\n        label: \"Reopen completed forms and clear responses\",\n        description: `Change ${cycleLowerCase} status from Completed to Reopened and clear all ${cycleLowerCase} cycle responses`\n      }, {\n        value: \"leave_as_is\",\n        label: \"Leave completed forms as is\",\n        description: \"No changes will be made to existing completed forms\"\n      }];\n    }\n  };\n  const options = getOptionsForCycle(latestEnabledCycle);\n\n  // Fetch completed forms count when popup opens\n  useEffect(() => {\n    if (visible) {\n      fetchCompletedCount();\n      setSelectedOption(\"\");\n      setError(null);\n    }\n  }, [visible]);\n  const fetchCompletedCount = async () => {\n    setLoading(true);\n    setError(null);\n    try {\n      const count = await questionnaireService.getCompletedFormsCount(questionnaireId, latestEnabledCycle);\n      setCompletedCount(count);\n    } catch (err) {\n      setError(\"Failed to load completed forms count. Please try again.\");\n      console.error(\"Error fetching completed count:\", err);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleSubmit = async () => {\n    // Only require option selection if there are completed forms\n    if (completedCount > 0 && !selectedOption) {\n      setError(\"Please select an option before proceeding.\");\n      return;\n    }\n    setSubmitting(true);\n    setError(null);\n    try {\n      // Prepare the request with data from props\n      const request = {\n        questionnaireId: questionnaireId,\n        surveyName: surveyName,\n        selectedYear: selectedYear,\n        definitionJson: JSON.stringify(surveyJson),\n        enableCycles: enableCycles,\n        latestEnabledCycle: latestEnabledCycle,\n        action: completedCount > 0 ? selectedOption : null // Send null action when no completed forms\n      };\n      const result = await questionnaireService.processBatchReopenAndRepublish(request);\n      const response = {\n        success: result.success,\n        message: getSuccessMessage(selectedOption),\n        processedCount: result.processedCount\n      };\n      if (onComplete) {\n        onComplete(response);\n      }\n      onHide();\n    } catch (err) {\n      setError(err.message || \"Failed to process batch reopen and republish action. Please try again.\");\n      console.error(\"Error processing batch reopen action:\", err);\n    } finally {\n      setSubmitting(false);\n    }\n  };\n  const getSuccessMessage = option => {\n    const cycleDisplayName = getCycleDisplayName(latestEnabledCycle);\n\n    // Handle case when no completed forms exist\n    if (completedCount === 0) {\n      return \"Questionnaire republished successfully. No completed forms to process.\";\n    }\n    switch (option) {\n      case \"reopen_keep_responses\":\n        return `Successfully reopened ${completedCount} completed ${cycleDisplayName.toLowerCase()} forms (responses preserved) and republished questionnaire`;\n      case \"reopen_clear_responses\":\n        return `Successfully reopened ${completedCount} completed ${cycleDisplayName.toLowerCase()} forms (responses cleared) and republished questionnaire`;\n      case \"leave_as_is\":\n        return \"No changes made to completed forms. Questionnaire republished successfully.\";\n      default:\n        return \"Action completed successfully\";\n    }\n  };\n  const renderContent = () => {\n    if (loading) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-container\",\n        children: [/*#__PURE__*/_jsxDEV(ProgressSpinner, {\n          size: \"50\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Loading completed forms data...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 9\n      }, this);\n    }\n    if (error && completedCount === null) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-container\",\n        children: [/*#__PURE__*/_jsxDEV(Message, {\n          severity: \"error\",\n          text: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          label: \"Retry\",\n          onClick: fetchCompletedCount,\n          className: \"p-button-primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 9\n      }, this);\n    }\n    const cycleDisplayName = getCycleDisplayName(latestEnabledCycle);\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"batch-reopen-content\",\n      children: [error && /*#__PURE__*/_jsxDEV(Message, {\n        severity: \"error\",\n        text: error,\n        className: \"error-message\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 19\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"completion-status\",\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"pi pi-info-circle status-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"status-text\",\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: completedCount\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this), \" partners have already completed their \", cycleDisplayName.toLowerCase(), \" forms.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 9\n      }, this), completedCount > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"options-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          children: \"Please select an action:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 13\n        }, this), options.map(option => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"option-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"option-header\",\n            children: [/*#__PURE__*/_jsxDEV(RadioButton, {\n              inputId: option.value,\n              value: option.value,\n              onChange: e => setSelectedOption(e.value),\n              checked: selectedOption === option.value\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: option.value,\n              className: \"option-label\",\n              children: option.label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"option-description\",\n            children: option.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 17\n          }, this)]\n        }, option.value, true, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 15\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"no-completed-forms\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"No completed forms need to be processed. The questionnaire will be republished without any form status changes.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 199,\n      columnNumber: 7\n    }, this);\n  };\n  const renderFooter = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"popup-footer\",\n    children: [/*#__PURE__*/_jsxDEV(Button, {\n      label: \"Cancel\",\n      onClick: onHide,\n      className: \"p-button-default\",\n      disabled: submitting\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 241,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      label: submitting ? \"Processing...\" : \"Apply Changes\",\n      onClick: handleSubmit,\n      className: \"p-button-primary\",\n      disabled: completedCount > 0 && !selectedOption || submitting,\n      loading: submitting\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 242,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 240,\n    columnNumber: 5\n  }, this);\n  const cycleDisplayName = getCycleDisplayName(latestEnabledCycle);\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    header: `Reopen Completed ${cycleDisplayName} Forms Options`,\n    visible: visible,\n    onHide: onHide,\n    footer: renderFooter(),\n    className: \"batch-reopen-popup\",\n    modal: true,\n    closable: !submitting,\n    dismissableMask: false,\n    draggable: false,\n    resizable: false,\n    style: {\n      width: \"600px\"\n    },\n    children: renderContent()\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 255,\n    columnNumber: 5\n  }, this);\n};\n_s(BatchReopenPopup, \"TW2Qk8Itg0wSfDwt4ONeiyeUbOQ=\");\n_c = BatchReopenPopup;\nexport default BatchReopenPopup;\nvar _c;\n$RefreshReg$(_c, \"BatchReopenPopup\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Dialog", "<PERSON><PERSON>", "RadioButton", "ProgressSpinner", "Message", "PartnerPlanCycle", "getCycleDisplayName", "questionnaireService", "jsxDEV", "_jsxDEV", "BatchReopenPopup", "visible", "onHide", "onComplete", "questionnaireId", "surveyJson", "latestEnabledCycle", "surveyName", "selected<PERSON>ear", "enableCycles", "_s", "completedCount", "setCompletedCount", "selectedOption", "setSelectedOption", "loading", "setLoading", "submitting", "setSubmitting", "error", "setError", "getOptionsForCycle", "cycle", "cycleDisplayName", "cycleLowerCase", "toLowerCase", "Planning", "value", "label", "description", "options", "fetchCompletedCount", "count", "getCompletedFormsCount", "err", "console", "handleSubmit", "request", "definitionJson", "JSON", "stringify", "action", "result", "processBatchReopenAndRepublish", "response", "success", "message", "getSuccessMessage", "processedCount", "option", "renderContent", "className", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "severity", "text", "onClick", "map", "inputId", "onChange", "e", "checked", "htmlFor", "renderFooter", "disabled", "header", "footer", "modal", "closable", "dismissableMask", "draggable", "resizable", "style", "width", "_c", "$RefreshReg$"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/components/admin/batchReopenPopup.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport { Dialog } from \"primereact/dialog\";\r\nimport { But<PERSON> } from \"primereact/button\";\r\nimport { RadioButton } from \"primereact/radiobutton\";\r\nimport { ProgressSpinner } from \"primereact/progressspinner\";\r\nimport { Message } from \"primereact/message\";\r\nimport { PartnerPlanCycle, getCycleDisplayName } from \"../../core/enumertions/partnerPlanCycle\";\r\nimport questionnaireService from \"../../services/questionnaireService\";\r\nimport \"./batchReopenPopup.scss\";\r\n\r\n/**\r\n * Work for batch reopen completed forms when admin republish questionnaire.\r\n * Note: Set reopen status based on the latest enable cycle set in the enableCycles in questionnaire record.\r\n * @param {Object} props - Component props\r\n * @param {boolean} props.visible - Whether the popup is visible\r\n * @param {Function} props.onHide - Function to call when hiding the popup\r\n * @param {Function} props.onComplete - Function to call when operation completes\r\n * @param {string} props.questionnaireId - ID of the questionnaire being republished\r\n * @param {Object} props.surveyJson - Survey JSON definition for republishing\r\n * @param {number} props.latestEnabledCycle - Latest enabled cycle (0=Planning, 1=MidYear, 2=YearEnd)\r\n * @param {string} props.surveyName - Survey name\r\n * @param {number} props.selectedYear - Selected year\r\n * @param {string} props.enableCycles - Comma-separated enabled cycles\r\n * @returns {JSX.Element} BatchReopenPopup component\r\n */\r\nexport const BatchReopenPopup = ({\r\n  visible,\r\n  onHide,\r\n  onComplete,\r\n  questionnaireId,\r\n  surveyJson,\r\n  latestEnabledCycle,\r\n  surveyName,\r\n  selectedYear,\r\n  enableCycles,\r\n}) => {\r\n  const [completedCount, setCompletedCount] = useState(null);\r\n  const [selectedOption, setSelectedOption] = useState(\"\");\r\n  const [loading, setLoading] = useState(false);\r\n  const [submitting, setSubmitting] = useState(false);\r\n  const [error, setError] = useState(null);\r\n\r\n  // Generate dynamic options based on the latest enabled cycle\r\n  const getOptionsForCycle = (cycle) => {\r\n    const cycleDisplayName = getCycleDisplayName(cycle);\r\n    const cycleLowerCase = cycleDisplayName.toLowerCase();\r\n\r\n    if (cycle === PartnerPlanCycle.Planning) {\r\n      return [\r\n        {\r\n          value: \"reopen_keep_responses\",\r\n          label: \"Reopen completed forms\",\r\n          description: `Change planning status from Completed to Draft (keep all responses)`,\r\n        },\r\n        {\r\n          value: \"reopen_clear_responses\",\r\n          label: \"Reopen completed forms and clear responses\",\r\n          description: `Change planning status from Completed to Draft and clear all planning cycle responses`,\r\n        },\r\n        {\r\n          value: \"leave_as_is\",\r\n          label: \"Leave completed forms as is\",\r\n          description: \"No changes will be made to existing completed forms\",\r\n        },\r\n      ];\r\n    } else {\r\n      // Mid Year Review and Year End Review cycles\r\n      return [\r\n        {\r\n          value: \"reopen_keep_responses\",\r\n          label: \"Reopen completed forms\",\r\n          description: `Change ${cycleLowerCase} status from Completed to Reopened (keep all responses)`,\r\n        },\r\n        {\r\n          value: \"reopen_clear_responses\",\r\n          label: \"Reopen completed forms and clear responses\",\r\n          description: `Change ${cycleLowerCase} status from Completed to Reopened and clear all ${cycleLowerCase} cycle responses`,\r\n        },\r\n        {\r\n          value: \"leave_as_is\",\r\n          label: \"Leave completed forms as is\",\r\n          description: \"No changes will be made to existing completed forms\",\r\n        },\r\n      ];\r\n    }\r\n  };\r\n\r\n  const options = getOptionsForCycle(latestEnabledCycle);\r\n\r\n  // Fetch completed forms count when popup opens\r\n  useEffect(() => {\r\n    if (visible) {\r\n      fetchCompletedCount();\r\n      setSelectedOption(\"\");\r\n      setError(null);\r\n    }\r\n  }, [visible]);\r\n\r\n  const fetchCompletedCount = async () => {\r\n    setLoading(true);\r\n    setError(null);\r\n\r\n    try {\r\n      const count = await questionnaireService.getCompletedFormsCount(questionnaireId, latestEnabledCycle);\r\n      setCompletedCount(count);\r\n    } catch (err) {\r\n      setError(\"Failed to load completed forms count. Please try again.\");\r\n      console.error(\"Error fetching completed count:\", err);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleSubmit = async () => {\r\n    // Only require option selection if there are completed forms\r\n    if (completedCount > 0 && !selectedOption) {\r\n      setError(\"Please select an option before proceeding.\");\r\n      return;\r\n    }\r\n\r\n    setSubmitting(true);\r\n    setError(null);\r\n\r\n    try {\r\n      // Prepare the request with data from props\r\n      const request = {\r\n        questionnaireId: questionnaireId,\r\n        surveyName: surveyName,\r\n        selectedYear: selectedYear,\r\n        definitionJson: JSON.stringify(surveyJson),\r\n        enableCycles: enableCycles,\r\n        latestEnabledCycle: latestEnabledCycle,\r\n        action: completedCount > 0 ? selectedOption : null, // Send null action when no completed forms\r\n      };\r\n\r\n      const result = await questionnaireService.processBatchReopenAndRepublish(request);\r\n\r\n      const response = {\r\n        success: result.success,\r\n        message: getSuccessMessage(selectedOption),\r\n        processedCount: result.processedCount,\r\n      };\r\n\r\n      if (onComplete) {\r\n        onComplete(response);\r\n      }\r\n\r\n      onHide();\r\n    } catch (err) {\r\n      setError(err.message || \"Failed to process batch reopen and republish action. Please try again.\");\r\n      console.error(\"Error processing batch reopen action:\", err);\r\n    } finally {\r\n      setSubmitting(false);\r\n    }\r\n  };\r\n\r\n  const getSuccessMessage = (option) => {\r\n    const cycleDisplayName = getCycleDisplayName(latestEnabledCycle);\r\n\r\n    // Handle case when no completed forms exist\r\n    if (completedCount === 0) {\r\n      return \"Questionnaire republished successfully. No completed forms to process.\";\r\n    }\r\n\r\n    switch (option) {\r\n      case \"reopen_keep_responses\":\r\n        return `Successfully reopened ${completedCount} completed ${cycleDisplayName.toLowerCase()} forms (responses preserved) and republished questionnaire`;\r\n      case \"reopen_clear_responses\":\r\n        return `Successfully reopened ${completedCount} completed ${cycleDisplayName.toLowerCase()} forms (responses cleared) and republished questionnaire`;\r\n      case \"leave_as_is\":\r\n        return \"No changes made to completed forms. Questionnaire republished successfully.\";\r\n      default:\r\n        return \"Action completed successfully\";\r\n    }\r\n  };\r\n\r\n  const renderContent = () => {\r\n    if (loading) {\r\n      return (\r\n        <div className=\"loading-container\">\r\n          <ProgressSpinner size=\"50\" />\r\n          <p>Loading completed forms data...</p>\r\n        </div>\r\n      );\r\n    }\r\n\r\n    if (error && completedCount === null) {\r\n      return (\r\n        <div className=\"error-container\">\r\n          <Message severity=\"error\" text={error} />\r\n          <Button label=\"Retry\" onClick={fetchCompletedCount} className=\"p-button-primary\" />\r\n        </div>\r\n      );\r\n    }\r\n\r\n    const cycleDisplayName = getCycleDisplayName(latestEnabledCycle);\r\n\r\n    return (\r\n      <div className=\"batch-reopen-content\">\r\n        {error && <Message severity=\"error\" text={error} className=\"error-message\" />}\r\n\r\n        <div className=\"completion-status\">\r\n          <i className=\"pi pi-info-circle status-icon\" />\r\n          <p className=\"status-text\">\r\n            <strong>{completedCount}</strong> partners have already completed their {cycleDisplayName.toLowerCase()} forms.\r\n          </p>\r\n        </div>\r\n\r\n        {completedCount > 0 ? (\r\n          <div className=\"options-section\">\r\n            <h4>Please select an action:</h4>\r\n\r\n            {options.map((option) => (\r\n              <div key={option.value} className=\"option-item\">\r\n                <div className=\"option-header\">\r\n                  <RadioButton\r\n                    inputId={option.value}\r\n                    value={option.value}\r\n                    onChange={(e) => setSelectedOption(e.value)}\r\n                    checked={selectedOption === option.value}\r\n                  />\r\n                  <label htmlFor={option.value} className=\"option-label\">\r\n                    {option.label}\r\n                  </label>\r\n                </div>\r\n                <p className=\"option-description\">{option.description}</p>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        ) : (\r\n          <div className=\"no-completed-forms\">\r\n            <p>No completed forms need to be processed. The questionnaire will be republished without any form status changes.</p>\r\n          </div>\r\n        )}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  const renderFooter = () => (\r\n    <div className=\"popup-footer\">\r\n      <Button label=\"Cancel\" onClick={onHide} className=\"p-button-default\" disabled={submitting} />\r\n      <Button\r\n        label={submitting ? \"Processing...\" : \"Apply Changes\"}\r\n        onClick={handleSubmit}\r\n        className=\"p-button-primary\"\r\n        disabled={(completedCount > 0 && !selectedOption) || submitting}\r\n        loading={submitting}\r\n      />\r\n    </div>\r\n  );\r\n\r\n  const cycleDisplayName = getCycleDisplayName(latestEnabledCycle);\r\n\r\n  return (\r\n    <Dialog\r\n      header={`Reopen Completed ${cycleDisplayName} Forms Options`}\r\n      visible={visible}\r\n      onHide={onHide}\r\n      footer={renderFooter()}\r\n      className=\"batch-reopen-popup\"\r\n      modal\r\n      closable={!submitting}\r\n      dismissableMask={false}\r\n      draggable={false}\r\n      resizable={false}\r\n      style={{ width: \"600px\" }}\r\n    >\r\n      {renderContent()}\r\n    </Dialog>\r\n  );\r\n};\r\n\r\nexport default BatchReopenPopup;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,WAAW,QAAQ,wBAAwB;AACpD,SAASC,eAAe,QAAQ,4BAA4B;AAC5D,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,SAASC,gBAAgB,EAAEC,mBAAmB,QAAQ,yCAAyC;AAC/F,OAAOC,oBAAoB,MAAM,qCAAqC;AACtE,OAAO,yBAAyB;;AAEhC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAdA,SAAAC,MAAA,IAAAC,OAAA;AAeA,OAAO,MAAMC,gBAAgB,GAAGA,CAAC;EAC/BC,OAAO;EACPC,MAAM;EACNC,UAAU;EACVC,eAAe;EACfC,UAAU;EACVC,kBAAkB;EAClBC,UAAU;EACVC,YAAY;EACZC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACyB,cAAc,EAAEC,iBAAiB,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC2B,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC6B,UAAU,EAAEC,aAAa,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC+B,KAAK,EAAEC,QAAQ,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;;EAExC;EACA,MAAMiC,kBAAkB,GAAIC,KAAK,IAAK;IACpC,MAAMC,gBAAgB,GAAG3B,mBAAmB,CAAC0B,KAAK,CAAC;IACnD,MAAME,cAAc,GAAGD,gBAAgB,CAACE,WAAW,CAAC,CAAC;IAErD,IAAIH,KAAK,KAAK3B,gBAAgB,CAAC+B,QAAQ,EAAE;MACvC,OAAO,CACL;QACEC,KAAK,EAAE,uBAAuB;QAC9BC,KAAK,EAAE,wBAAwB;QAC/BC,WAAW,EAAE;MACf,CAAC,EACD;QACEF,KAAK,EAAE,wBAAwB;QAC/BC,KAAK,EAAE,4CAA4C;QACnDC,WAAW,EAAE;MACf,CAAC,EACD;QACEF,KAAK,EAAE,aAAa;QACpBC,KAAK,EAAE,6BAA6B;QACpCC,WAAW,EAAE;MACf,CAAC,CACF;IACH,CAAC,MAAM;MACL;MACA,OAAO,CACL;QACEF,KAAK,EAAE,uBAAuB;QAC9BC,KAAK,EAAE,wBAAwB;QAC/BC,WAAW,EAAE,UAAUL,cAAc;MACvC,CAAC,EACD;QACEG,KAAK,EAAE,wBAAwB;QAC/BC,KAAK,EAAE,4CAA4C;QACnDC,WAAW,EAAE,UAAUL,cAAc,oDAAoDA,cAAc;MACzG,CAAC,EACD;QACEG,KAAK,EAAE,aAAa;QACpBC,KAAK,EAAE,6BAA6B;QACpCC,WAAW,EAAE;MACf,CAAC,CACF;IACH;EACF,CAAC;EAED,MAAMC,OAAO,GAAGT,kBAAkB,CAACf,kBAAkB,CAAC;;EAEtD;EACAjB,SAAS,CAAC,MAAM;IACd,IAAIY,OAAO,EAAE;MACX8B,mBAAmB,CAAC,CAAC;MACrBjB,iBAAiB,CAAC,EAAE,CAAC;MACrBM,QAAQ,CAAC,IAAI,CAAC;IAChB;EACF,CAAC,EAAE,CAACnB,OAAO,CAAC,CAAC;EAEb,MAAM8B,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtCf,UAAU,CAAC,IAAI,CAAC;IAChBI,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF,MAAMY,KAAK,GAAG,MAAMnC,oBAAoB,CAACoC,sBAAsB,CAAC7B,eAAe,EAAEE,kBAAkB,CAAC;MACpGM,iBAAiB,CAACoB,KAAK,CAAC;IAC1B,CAAC,CAAC,OAAOE,GAAG,EAAE;MACZd,QAAQ,CAAC,yDAAyD,CAAC;MACnEe,OAAO,CAAChB,KAAK,CAAC,iCAAiC,EAAEe,GAAG,CAAC;IACvD,CAAC,SAAS;MACRlB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMoB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B;IACA,IAAIzB,cAAc,GAAG,CAAC,IAAI,CAACE,cAAc,EAAE;MACzCO,QAAQ,CAAC,4CAA4C,CAAC;MACtD;IACF;IAEAF,aAAa,CAAC,IAAI,CAAC;IACnBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF;MACA,MAAMiB,OAAO,GAAG;QACdjC,eAAe,EAAEA,eAAe;QAChCG,UAAU,EAAEA,UAAU;QACtBC,YAAY,EAAEA,YAAY;QAC1B8B,cAAc,EAAEC,IAAI,CAACC,SAAS,CAACnC,UAAU,CAAC;QAC1CI,YAAY,EAAEA,YAAY;QAC1BH,kBAAkB,EAAEA,kBAAkB;QACtCmC,MAAM,EAAE9B,cAAc,GAAG,CAAC,GAAGE,cAAc,GAAG,IAAI,CAAE;MACtD,CAAC;MAED,MAAM6B,MAAM,GAAG,MAAM7C,oBAAoB,CAAC8C,8BAA8B,CAACN,OAAO,CAAC;MAEjF,MAAMO,QAAQ,GAAG;QACfC,OAAO,EAAEH,MAAM,CAACG,OAAO;QACvBC,OAAO,EAAEC,iBAAiB,CAAClC,cAAc,CAAC;QAC1CmC,cAAc,EAAEN,MAAM,CAACM;MACzB,CAAC;MAED,IAAI7C,UAAU,EAAE;QACdA,UAAU,CAACyC,QAAQ,CAAC;MACtB;MAEA1C,MAAM,CAAC,CAAC;IACV,CAAC,CAAC,OAAOgC,GAAG,EAAE;MACZd,QAAQ,CAACc,GAAG,CAACY,OAAO,IAAI,wEAAwE,CAAC;MACjGX,OAAO,CAAChB,KAAK,CAAC,uCAAuC,EAAEe,GAAG,CAAC;IAC7D,CAAC,SAAS;MACRhB,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAM6B,iBAAiB,GAAIE,MAAM,IAAK;IACpC,MAAM1B,gBAAgB,GAAG3B,mBAAmB,CAACU,kBAAkB,CAAC;;IAEhE;IACA,IAAIK,cAAc,KAAK,CAAC,EAAE;MACxB,OAAO,wEAAwE;IACjF;IAEA,QAAQsC,MAAM;MACZ,KAAK,uBAAuB;QAC1B,OAAO,yBAAyBtC,cAAc,cAAcY,gBAAgB,CAACE,WAAW,CAAC,CAAC,4DAA4D;MACxJ,KAAK,wBAAwB;QAC3B,OAAO,yBAAyBd,cAAc,cAAcY,gBAAgB,CAACE,WAAW,CAAC,CAAC,0DAA0D;MACtJ,KAAK,aAAa;QAChB,OAAO,6EAA6E;MACtF;QACE,OAAO,+BAA+B;IAC1C;EACF,CAAC;EAED,MAAMyB,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAInC,OAAO,EAAE;MACX,oBACEhB,OAAA;QAAKoD,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCrD,OAAA,CAACN,eAAe;UAAC4D,IAAI,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7B1D,OAAA;UAAAqD,QAAA,EAAG;QAA+B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC;IAEV;IAEA,IAAItC,KAAK,IAAIR,cAAc,KAAK,IAAI,EAAE;MACpC,oBACEZ,OAAA;QAAKoD,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BrD,OAAA,CAACL,OAAO;UAACgE,QAAQ,EAAC,OAAO;UAACC,IAAI,EAAExC;QAAM;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzC1D,OAAA,CAACR,MAAM;UAACqC,KAAK,EAAC,OAAO;UAACgC,OAAO,EAAE7B,mBAAoB;UAACoB,SAAS,EAAC;QAAkB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChF,CAAC;IAEV;IAEA,MAAMlC,gBAAgB,GAAG3B,mBAAmB,CAACU,kBAAkB,CAAC;IAEhE,oBACEP,OAAA;MAAKoD,SAAS,EAAC,sBAAsB;MAAAC,QAAA,GAClCjC,KAAK,iBAAIpB,OAAA,CAACL,OAAO;QAACgE,QAAQ,EAAC,OAAO;QAACC,IAAI,EAAExC,KAAM;QAACgC,SAAS,EAAC;MAAe;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAE7E1D,OAAA;QAAKoD,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCrD,OAAA;UAAGoD,SAAS,EAAC;QAA+B;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/C1D,OAAA;UAAGoD,SAAS,EAAC,aAAa;UAAAC,QAAA,gBACxBrD,OAAA;YAAAqD,QAAA,EAASzC;UAAc;YAAA2C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,2CAAuC,EAAClC,gBAAgB,CAACE,WAAW,CAAC,CAAC,EAAC,SAC1G;QAAA;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,EAEL9C,cAAc,GAAG,CAAC,gBACjBZ,OAAA;QAAKoD,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BrD,OAAA;UAAAqD,QAAA,EAAI;QAAwB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAEhC3B,OAAO,CAAC+B,GAAG,CAAEZ,MAAM,iBAClBlD,OAAA;UAAwBoD,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC7CrD,OAAA;YAAKoD,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BrD,OAAA,CAACP,WAAW;cACVsE,OAAO,EAAEb,MAAM,CAACtB,KAAM;cACtBA,KAAK,EAAEsB,MAAM,CAACtB,KAAM;cACpBoC,QAAQ,EAAGC,CAAC,IAAKlD,iBAAiB,CAACkD,CAAC,CAACrC,KAAK,CAAE;cAC5CsC,OAAO,EAAEpD,cAAc,KAAKoC,MAAM,CAACtB;YAAM;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC,eACF1D,OAAA;cAAOmE,OAAO,EAAEjB,MAAM,CAACtB,KAAM;cAACwB,SAAS,EAAC,cAAc;cAAAC,QAAA,EACnDH,MAAM,CAACrB;YAAK;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACN1D,OAAA;YAAGoD,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAEH,MAAM,CAACpB;UAAW;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA,GAZlDR,MAAM,CAACtB,KAAK;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAajB,CACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,gBAEN1D,OAAA;QAAKoD,SAAS,EAAC,oBAAoB;QAAAC,QAAA,eACjCrD,OAAA;UAAAqD,QAAA,EAAG;QAA+G;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEV,CAAC;EAED,MAAMU,YAAY,GAAGA,CAAA,kBACnBpE,OAAA;IAAKoD,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAC3BrD,OAAA,CAACR,MAAM;MAACqC,KAAK,EAAC,QAAQ;MAACgC,OAAO,EAAE1D,MAAO;MAACiD,SAAS,EAAC,kBAAkB;MAACiB,QAAQ,EAAEnD;IAAW;MAAAqC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC7F1D,OAAA,CAACR,MAAM;MACLqC,KAAK,EAAEX,UAAU,GAAG,eAAe,GAAG,eAAgB;MACtD2C,OAAO,EAAExB,YAAa;MACtBe,SAAS,EAAC,kBAAkB;MAC5BiB,QAAQ,EAAGzD,cAAc,GAAG,CAAC,IAAI,CAACE,cAAc,IAAKI,UAAW;MAChEF,OAAO,EAAEE;IAAW;MAAAqC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CACN;EAED,MAAMlC,gBAAgB,GAAG3B,mBAAmB,CAACU,kBAAkB,CAAC;EAEhE,oBACEP,OAAA,CAACT,MAAM;IACL+E,MAAM,EAAE,oBAAoB9C,gBAAgB,gBAAiB;IAC7DtB,OAAO,EAAEA,OAAQ;IACjBC,MAAM,EAAEA,MAAO;IACfoE,MAAM,EAAEH,YAAY,CAAC,CAAE;IACvBhB,SAAS,EAAC,oBAAoB;IAC9BoB,KAAK;IACLC,QAAQ,EAAE,CAACvD,UAAW;IACtBwD,eAAe,EAAE,KAAM;IACvBC,SAAS,EAAE,KAAM;IACjBC,SAAS,EAAE,KAAM;IACjBC,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAQ,CAAE;IAAAzB,QAAA,EAEzBF,aAAa,CAAC;EAAC;IAAAI,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEb,CAAC;AAAC/C,EAAA,CArPWV,gBAAgB;AAAA8E,EAAA,GAAhB9E,gBAAgB;AAuP7B,eAAeA,gBAAgB;AAAC,IAAA8E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}