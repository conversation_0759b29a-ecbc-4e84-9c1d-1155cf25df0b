{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { AsyncAction } from './AsyncAction';\nimport { animationFrameProvider } from './animationFrameProvider';\nvar AnimationFrameAction = function (_super) {\n  __extends(AnimationFrameAction, _super);\n  function AnimationFrameAction(scheduler, work) {\n    var _this = _super.call(this, scheduler, work) || this;\n    _this.scheduler = scheduler;\n    _this.work = work;\n    return _this;\n  }\n  AnimationFrameAction.prototype.requestAsyncId = function (scheduler, id, delay) {\n    if (delay === void 0) {\n      delay = 0;\n    }\n    if (delay !== null && delay > 0) {\n      return _super.prototype.requestAsyncId.call(this, scheduler, id, delay);\n    }\n    scheduler.actions.push(this);\n    return scheduler._scheduled || (scheduler._scheduled = animationFrameProvider.requestAnimationFrame(function () {\n      return scheduler.flush(undefined);\n    }));\n  };\n  AnimationFrameAction.prototype.recycleAsyncId = function (scheduler, id, delay) {\n    var _a;\n    if (delay === void 0) {\n      delay = 0;\n    }\n    if (delay != null ? delay > 0 : this.delay > 0) {\n      return _super.prototype.recycleAsyncId.call(this, scheduler, id, delay);\n    }\n    var actions = scheduler.actions;\n    if (id != null && id === scheduler._scheduled && ((_a = actions[actions.length - 1]) === null || _a === void 0 ? void 0 : _a.id) !== id) {\n      animationFrameProvider.cancelAnimationFrame(id);\n      scheduler._scheduled = undefined;\n    }\n    return undefined;\n  };\n  return AnimationFrameAction;\n}(AsyncAction);\nexport { AnimationFrameAction };", "map": {"version": 3, "names": ["AsyncAction", "animationFrameProvider", "AnimationFrameAction", "_super", "__extends", "scheduler", "work", "_this", "call", "prototype", "requestAsyncId", "id", "delay", "actions", "push", "_scheduled", "requestAnimationFrame", "flush", "undefined", "recycleAsyncId", "_a", "length", "cancelAnimationFrame"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\scheduler\\AnimationFrameAction.ts"], "sourcesContent": ["import { AsyncAction } from './AsyncAction';\nimport { AnimationFrameScheduler } from './AnimationFrameScheduler';\nimport { SchedulerAction } from '../types';\nimport { animationFrameProvider } from './animationFrameProvider';\nimport { TimerHandle } from './timerHandle';\n\nexport class AnimationFrameAction<T> extends AsyncAction<T> {\n  constructor(protected scheduler: AnimationFrameScheduler, protected work: (this: SchedulerAction<T>, state?: T) => void) {\n    super(scheduler, work);\n  }\n\n  protected requestAsyncId(scheduler: AnimationFrameScheduler, id?: TimerHandle, delay: number = 0): TimerHandle {\n    // If delay is greater than 0, request as an async action.\n    if (delay !== null && delay > 0) {\n      return super.requestAsyncId(scheduler, id, delay);\n    }\n    // Push the action to the end of the scheduler queue.\n    scheduler.actions.push(this);\n    // If an animation frame has already been requested, don't request another\n    // one. If an animation frame hasn't been requested yet, request one. Return\n    // the current animation frame request id.\n    return scheduler._scheduled || (scheduler._scheduled = animationFrameProvider.requestAnimationFrame(() => scheduler.flush(undefined)));\n  }\n\n  protected recycleAsyncId(scheduler: AnimationFrameScheduler, id?: TimerHandle, delay: number = 0): TimerHandle | undefined {\n    // If delay exists and is greater than 0, or if the delay is null (the\n    // action wasn't rescheduled) but was originally scheduled as an async\n    // action, then recycle as an async action.\n    if (delay != null ? delay > 0 : this.delay > 0) {\n      return super.recycleAsyncId(scheduler, id, delay);\n    }\n    // If the scheduler queue has no remaining actions with the same async id,\n    // cancel the requested animation frame and set the scheduled flag to\n    // undefined so the next AnimationFrameAction will request its own.\n    const { actions } = scheduler;\n    if (id != null && id === scheduler._scheduled && actions[actions.length - 1]?.id !== id) {\n      animationFrameProvider.cancelAnimationFrame(id as number);\n      scheduler._scheduled = undefined;\n    }\n    // Return undefined so the action knows to request a new async id if it's rescheduled.\n    return undefined;\n  }\n}\n"], "mappings": ";AAAA,SAASA,WAAW,QAAQ,eAAe;AAG3C,SAASC,sBAAsB,QAAQ,0BAA0B;AAGjE,IAAAC,oBAAA,aAAAC,MAAA;EAA6CC,SAAA,CAAAF,oBAAA,EAAAC,MAAA;EAC3C,SAAAD,qBAAsBG,SAAkC,EAAYC,IAAmD;IAAvH,IAAAC,KAAA,GACEJ,MAAA,CAAAK,IAAA,OAAMH,SAAS,EAAEC,IAAI,CAAC;IADFC,KAAA,CAAAF,SAAS,GAATA,SAAS;IAAqCE,KAAA,CAAAD,IAAI,GAAJA,IAAI;;EAExE;EAEUJ,oBAAA,CAAAO,SAAA,CAAAC,cAAc,GAAxB,UAAyBL,SAAkC,EAAEM,EAAgB,EAAEC,KAAiB;IAAjB,IAAAA,KAAA;MAAAA,KAAA,IAAiB;IAAA;IAE9F,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,GAAG,CAAC,EAAE;MAC/B,OAAOT,MAAA,CAAAM,SAAA,CAAMC,cAAc,CAAAF,IAAA,OAACH,SAAS,EAAEM,EAAE,EAAEC,KAAK,CAAC;;IAGnDP,SAAS,CAACQ,OAAO,CAACC,IAAI,CAAC,IAAI,CAAC;IAI5B,OAAOT,SAAS,CAACU,UAAU,KAAKV,SAAS,CAACU,UAAU,GAAGd,sBAAsB,CAACe,qBAAqB,CAAC;MAAM,OAAAX,SAAS,CAACY,KAAK,CAACC,SAAS,CAAC;IAA1B,CAA0B,CAAC,CAAC;EACxI,CAAC;EAEShB,oBAAA,CAAAO,SAAA,CAAAU,cAAc,GAAxB,UAAyBd,SAAkC,EAAEM,EAAgB,EAAEC,KAAiB;;IAAjB,IAAAA,KAAA;MAAAA,KAAA,IAAiB;IAAA;IAI9F,IAAIA,KAAK,IAAI,IAAI,GAAGA,KAAK,GAAG,CAAC,GAAG,IAAI,CAACA,KAAK,GAAG,CAAC,EAAE;MAC9C,OAAOT,MAAA,CAAAM,SAAA,CAAMU,cAAc,CAAAX,IAAA,OAACH,SAAS,EAAEM,EAAE,EAAEC,KAAK,CAAC;;IAK3C,IAAAC,OAAO,GAAKR,SAAS,CAAAQ,OAAd;IACf,IAAIF,EAAE,IAAI,IAAI,IAAIA,EAAE,KAAKN,SAAS,CAACU,UAAU,IAAI,EAAAK,EAAA,GAAAP,OAAO,CAACA,OAAO,CAACQ,MAAM,GAAG,CAAC,CAAC,cAAAD,EAAA,uBAAAA,EAAA,CAAET,EAAE,MAAKA,EAAE,EAAE;MACvFV,sBAAsB,CAACqB,oBAAoB,CAACX,EAAY,CAAC;MACzDN,SAAS,CAACU,UAAU,GAAGG,SAAS;;IAGlC,OAAOA,SAAS;EAClB,CAAC;EACH,OAAAhB,oBAAC;AAAD,CAAC,CApC4CF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}