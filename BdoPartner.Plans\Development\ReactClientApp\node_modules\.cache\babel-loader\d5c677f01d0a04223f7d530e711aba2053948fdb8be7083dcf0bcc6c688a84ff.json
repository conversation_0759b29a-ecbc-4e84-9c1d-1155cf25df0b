{"ast": null, "code": "import { map } from './map';\nimport { innerFrom } from '../observable/innerFrom';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function exhaustMap(project, resultSelector) {\n  if (resultSelector) {\n    return function (source) {\n      return source.pipe(exhaustMap(function (a, i) {\n        return innerFrom(project(a, i)).pipe(map(function (b, ii) {\n          return resultSelector(a, b, i, ii);\n        }));\n      }));\n    };\n  }\n  return operate(function (source, subscriber) {\n    var index = 0;\n    var innerSub = null;\n    var isComplete = false;\n    source.subscribe(createOperatorSubscriber(subscriber, function (outerValue) {\n      if (!innerSub) {\n        innerSub = createOperatorSubscriber(subscriber, undefined, function () {\n          innerSub = null;\n          isComplete && subscriber.complete();\n        });\n        innerFrom(project(outerValue, index++)).subscribe(innerSub);\n      }\n    }, function () {\n      isComplete = true;\n      !innerSub && subscriber.complete();\n    }));\n  });\n}", "map": {"version": 3, "names": ["map", "innerFrom", "operate", "createOperatorSubscriber", "exhaustMap", "project", "resultSelector", "source", "pipe", "a", "i", "b", "ii", "subscriber", "index", "innerSub", "isComplete", "subscribe", "outerValue", "undefined", "complete"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\exhaustMap.ts"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { Subscriber } from '../Subscriber';\nimport { ObservableInput, OperatorFunction, ObservedValueOf } from '../types';\nimport { map } from './map';\nimport { innerFrom } from '../observable/innerFrom';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\n\n/* tslint:disable:max-line-length */\nexport function exhaustMap<T, O extends ObservableInput<any>>(\n  project: (value: T, index: number) => O\n): OperatorFunction<T, ObservedValueOf<O>>;\n/** @deprecated The `resultSelector` parameter will be removed in v8. Use an inner `map` instead. Details: https://rxjs.dev/deprecations/resultSelector */\nexport function exhaustMap<T, O extends ObservableInput<any>>(\n  project: (value: T, index: number) => O,\n  resultSelector: undefined\n): OperatorFunction<T, ObservedValueOf<O>>;\n/** @deprecated The `resultSelector` parameter will be removed in v8. Use an inner `map` instead. Details: https://rxjs.dev/deprecations/resultSelector */\nexport function exhaustMap<T, I, R>(\n  project: (value: T, index: number) => ObservableInput<I>,\n  resultSelector: (outerValue: T, innerValue: I, outerIndex: number, innerIndex: number) => R\n): OperatorFunction<T, R>;\n/* tslint:enable:max-line-length */\n\n/**\n * Projects each source value to an Observable which is merged in the output\n * Observable only if the previous projected Observable has completed.\n *\n * <span class=\"informal\">Maps each value to an Observable, then flattens all of\n * these inner Observables using {@link exhaustAll}.</span>\n *\n * ![](exhaustMap.png)\n *\n * Returns an Observable that emits items based on applying a function that you\n * supply to each item emitted by the source Observable, where that function\n * returns an (so-called \"inner\") Observable. When it projects a source value to\n * an Observable, the output Observable begins emitting the items emitted by\n * that projected Observable. However, `exhaustMap` ignores every new projected\n * Observable if the previous projected Observable has not yet completed. Once\n * that one completes, it will accept and flatten the next projected Observable\n * and repeat this process.\n *\n * ## Example\n *\n * Run a finite timer for each click, only if there is no currently active timer\n *\n * ```ts\n * import { fromEvent, exhaustMap, interval, take } from 'rxjs';\n *\n * const clicks = fromEvent(document, 'click');\n * const result = clicks.pipe(\n *   exhaustMap(() => interval(1000).pipe(take(5)))\n * );\n * result.subscribe(x => console.log(x));\n * ```\n *\n * @see {@link concatMap}\n * @see {@link exhaust}\n * @see {@link mergeMap}\n * @see {@link switchMap}\n *\n * @param project A function that, when applied to an item emitted by the source\n * Observable, returns an Observable.\n * @return A function that returns an Observable containing projected\n * Observables of each item of the source, ignoring projected Observables that\n * start before their preceding Observable has completed.\n */\nexport function exhaustMap<T, R, O extends ObservableInput<any>>(\n  project: (value: T, index: number) => O,\n  resultSelector?: (outerValue: T, innerValue: ObservedValueOf<O>, outerIndex: number, innerIndex: number) => R\n): OperatorFunction<T, ObservedValueOf<O> | R> {\n  if (resultSelector) {\n    // DEPRECATED PATH\n    return (source: Observable<T>) =>\n      source.pipe(exhaustMap((a, i) => innerFrom(project(a, i)).pipe(map((b: any, ii: any) => resultSelector(a, b, i, ii)))));\n  }\n  return operate((source, subscriber) => {\n    let index = 0;\n    let innerSub: Subscriber<T> | null = null;\n    let isComplete = false;\n    source.subscribe(\n      createOperatorSubscriber(\n        subscriber,\n        (outerValue) => {\n          if (!innerSub) {\n            innerSub = createOperatorSubscriber(subscriber, undefined, () => {\n              innerSub = null;\n              isComplete && subscriber.complete();\n            });\n            innerFrom(project(outerValue, index++)).subscribe(innerSub);\n          }\n        },\n        () => {\n          isComplete = true;\n          !innerSub && subscriber.complete();\n        }\n      )\n    );\n  });\n}\n"], "mappings": "AAGA,SAASA,GAAG,QAAQ,OAAO;AAC3B,SAASC,SAAS,QAAQ,yBAAyB;AACnD,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AA6D/D,OAAM,SAAUC,UAAUA,CACxBC,OAAuC,EACvCC,cAA6G;EAE7G,IAAIA,cAAc,EAAE;IAElB,OAAO,UAACC,MAAqB;MAC3B,OAAAA,MAAM,CAACC,IAAI,CAACJ,UAAU,CAAC,UAACK,CAAC,EAAEC,CAAC;QAAK,OAAAT,SAAS,CAACI,OAAO,CAACI,CAAC,EAAEC,CAAC,CAAC,CAAC,CAACF,IAAI,CAACR,GAAG,CAAC,UAACW,CAAM,EAAEC,EAAO;UAAK,OAAAN,cAAc,CAACG,CAAC,EAAEE,CAAC,EAAED,CAAC,EAAEE,EAAE,CAAC;QAA3B,CAA2B,CAAC,CAAC;MAApF,CAAoF,CAAC,CAAC;IAAvH,CAAuH;;EAE3H,OAAOV,OAAO,CAAC,UAACK,MAAM,EAAEM,UAAU;IAChC,IAAIC,KAAK,GAAG,CAAC;IACb,IAAIC,QAAQ,GAAyB,IAAI;IACzC,IAAIC,UAAU,GAAG,KAAK;IACtBT,MAAM,CAACU,SAAS,CACdd,wBAAwB,CACtBU,UAAU,EACV,UAACK,UAAU;MACT,IAAI,CAACH,QAAQ,EAAE;QACbA,QAAQ,GAAGZ,wBAAwB,CAACU,UAAU,EAAEM,SAAS,EAAE;UACzDJ,QAAQ,GAAG,IAAI;UACfC,UAAU,IAAIH,UAAU,CAACO,QAAQ,EAAE;QACrC,CAAC,CAAC;QACFnB,SAAS,CAACI,OAAO,CAACa,UAAU,EAAEJ,KAAK,EAAE,CAAC,CAAC,CAACG,SAAS,CAACF,QAAQ,CAAC;;IAE/D,CAAC,EACD;MACEC,UAAU,GAAG,IAAI;MACjB,CAACD,QAAQ,IAAIF,UAAU,CAACO,QAAQ,EAAE;IACpC,CAAC,CACF,CACF;EACH,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}