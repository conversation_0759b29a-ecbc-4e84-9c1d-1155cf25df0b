{"ast": null, "code": "export var config = {\n  onUnhandledError: null,\n  onStoppedNotification: null,\n  Promise: undefined,\n  useDeprecatedSynchronousErrorHandling: false,\n  useDeprecatedNextContext: false\n};", "map": {"version": 3, "names": ["config", "onUnhandledError", "onStoppedNotification", "Promise", "undefined", "useDeprecatedSynchronousErrorHandling", "useDeprecatedNextContext"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\config.ts"], "sourcesContent": ["import { Subscriber } from './Subscriber';\nimport { ObservableNotification } from './types';\n\n/**\n * The {@link GlobalConfig} object for RxJS. It is used to configure things\n * like how to react on unhandled errors.\n */\nexport const config: GlobalConfig = {\n  onUnhandledError: null,\n  onStoppedNotification: null,\n  Promise: undefined,\n  useDeprecatedSynchronousErrorHandling: false,\n  useDeprecatedNextContext: false,\n};\n\n/**\n * The global configuration object for RxJS, used to configure things\n * like how to react on unhandled errors. Accessible via {@link config}\n * object.\n */\nexport interface GlobalConfig {\n  /**\n   * A registration point for unhandled errors from RxJS. These are errors that\n   * cannot were not handled by consuming code in the usual subscription path. For\n   * example, if you have this configured, and you subscribe to an observable without\n   * providing an error handler, errors from that subscription will end up here. This\n   * will _always_ be called asynchronously on another job in the runtime. This is because\n   * we do not want errors thrown in this user-configured handler to interfere with the\n   * behavior of the library.\n   */\n  onUnhandledError: ((err: any) => void) | null;\n\n  /**\n   * A registration point for notifications that cannot be sent to subscribers because they\n   * have completed, errored or have been explicitly unsubscribed. By default, next, complete\n   * and error notifications sent to stopped subscribers are noops. However, sometimes callers\n   * might want a different behavior. For example, with sources that attempt to report errors\n   * to stopped subscribers, a caller can configure RxJS to throw an unhandled error instead.\n   * This will _always_ be called asynchronously on another job in the runtime. This is because\n   * we do not want errors thrown in this user-configured handler to interfere with the\n   * behavior of the library.\n   */\n  onStoppedNotification: ((notification: ObservableNotification<any>, subscriber: Subscriber<any>) => void) | null;\n\n  /**\n   * The promise constructor used by default for {@link Observable#toPromise toPromise} and {@link Observable#forEach forEach}\n   * methods.\n   *\n   * @deprecated As of version 8, RxJS will no longer support this sort of injection of a\n   * Promise constructor. If you need a Promise implementation other than native promises,\n   * please polyfill/patch Promise as you see appropriate. Will be removed in v8.\n   */\n  Promise?: PromiseConstructorLike;\n\n  /**\n   * If true, turns on synchronous error rethrowing, which is a deprecated behavior\n   * in v6 and higher. This behavior enables bad patterns like wrapping a subscribe\n   * call in a try/catch block. It also enables producer interference, a nasty bug\n   * where a multicast can be broken for all observers by a downstream consumer with\n   * an unhandled error. DO NOT USE THIS FLAG UNLESS IT'S NEEDED TO BUY TIME\n   * FOR MIGRATION REASONS.\n   *\n   * @deprecated As of version 8, RxJS will no longer support synchronous throwing\n   * of unhandled errors. All errors will be thrown on a separate call stack to prevent bad\n   * behaviors described above. Will be removed in v8.\n   */\n  useDeprecatedSynchronousErrorHandling: boolean;\n\n  /**\n   * If true, enables an as-of-yet undocumented feature from v5: The ability to access\n   * `unsubscribe()` via `this` context in `next` functions created in observers passed\n   * to `subscribe`.\n   *\n   * This is being removed because the performance was severely problematic, and it could also cause\n   * issues when types other than POJOs are passed to subscribe as subscribers, as they will likely have\n   * their `this` context overwritten.\n   *\n   * @deprecated As of version 8, RxJS will no longer support altering the\n   * context of next functions provided as part of an observer to Subscribe. Instead,\n   * you will have access to a subscription or a signal or token that will allow you to do things like\n   * unsubscribe and test closed status. Will be removed in v8.\n   */\n  useDeprecatedNextContext: boolean;\n}\n"], "mappings": "AAOA,OAAO,IAAMA,MAAM,GAAiB;EAClCC,gBAAgB,EAAE,IAAI;EACtBC,qBAAqB,EAAE,IAAI;EAC3BC,OAAO,EAAEC,SAAS;EAClBC,qCAAqC,EAAE,KAAK;EAC5CC,wBAAwB,EAAE;CAC3B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}