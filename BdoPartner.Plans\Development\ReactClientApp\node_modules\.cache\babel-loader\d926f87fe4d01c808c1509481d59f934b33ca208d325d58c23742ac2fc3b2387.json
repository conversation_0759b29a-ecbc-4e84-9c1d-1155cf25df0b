{"ast": null, "code": "import { map } from './map';\nexport function pluck() {\n  var properties = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    properties[_i] = arguments[_i];\n  }\n  var length = properties.length;\n  if (length === 0) {\n    throw new Error('list of properties cannot be empty.');\n  }\n  return map(function (x) {\n    var currentProp = x;\n    for (var i = 0; i < length; i++) {\n      var p = currentProp === null || currentProp === void 0 ? void 0 : currentProp[properties[i]];\n      if (typeof p !== 'undefined') {\n        currentProp = p;\n      } else {\n        return undefined;\n      }\n    }\n    return currentProp;\n  });\n}", "map": {"version": 3, "names": ["map", "pluck", "properties", "_i", "arguments", "length", "Error", "x", "currentProp", "i", "p", "undefined"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\pluck.ts"], "sourcesContent": ["import { map } from './map';\nimport { OperatorFunction } from '../types';\n\n/* tslint:disable:max-line-length */\n/** @deprecated Use {@link map} and optional chaining: `pluck('foo', 'bar')` is `map(x => x?.foo?.bar)`. Will be removed in v8. */\nexport function pluck<T, K1 extends keyof T>(k1: K1): OperatorFunction<T, T[K1]>;\n/** @deprecated Use {@link map} and optional chaining: `pluck('foo', 'bar')` is `map(x => x?.foo?.bar)`. Will be removed in v8. */\nexport function pluck<T, K1 extends keyof T, K2 extends keyof T[K1]>(k1: K1, k2: K2): OperatorFunction<T, T[K1][K2]>;\n/** @deprecated Use {@link map} and optional chaining: `pluck('foo', 'bar')` is `map(x => x?.foo?.bar)`. Will be removed in v8. */\nexport function pluck<T, K1 extends keyof T, K2 extends keyof T[K1], K3 extends keyof T[K1][K2]>(\n  k1: K1,\n  k2: K2,\n  k3: K3\n): OperatorFunction<T, T[K1][K2][K3]>;\n/** @deprecated Use {@link map} and optional chaining: `pluck('foo', 'bar')` is `map(x => x?.foo?.bar)`. Will be removed in v8. */\nexport function pluck<T, K1 extends keyof T, K2 extends keyof T[K1], K3 extends keyof T[K1][K2], K4 extends keyof T[K1][K2][K3]>(\n  k1: K1,\n  k2: K2,\n  k3: K3,\n  k4: K4\n): OperatorFunction<T, T[K1][K2][K3][K4]>;\n/** @deprecated Use {@link map} and optional chaining: `pluck('foo', 'bar')` is `map(x => x?.foo?.bar)`. Will be removed in v8. */\nexport function pluck<\n  T,\n  K1 extends keyof T,\n  K2 extends keyof T[K1],\n  K3 extends keyof T[K1][K2],\n  K4 extends keyof T[K1][K2][K3],\n  K5 extends keyof T[K1][K2][K3][K4]\n>(k1: K1, k2: K2, k3: K3, k4: K4, k5: K5): OperatorFunction<T, T[K1][K2][K3][K4][K5]>;\n/** @deprecated Use {@link map} and optional chaining: `pluck('foo', 'bar')` is `map(x => x?.foo?.bar)`. Will be removed in v8. */\nexport function pluck<\n  T,\n  K1 extends keyof T,\n  K2 extends keyof T[K1],\n  K3 extends keyof T[K1][K2],\n  K4 extends keyof T[K1][K2][K3],\n  K5 extends keyof T[K1][K2][K3][K4],\n  K6 extends keyof T[K1][K2][K3][K4][K5]\n>(k1: K1, k2: K2, k3: K3, k4: K4, k5: K5, k6: K6): OperatorFunction<T, T[K1][K2][K3][K4][K5][K6]>;\n/** @deprecated Use {@link map} and optional chaining: `pluck('foo', 'bar')` is `map(x => x?.foo?.bar)`. Will be removed in v8. */\nexport function pluck<\n  T,\n  K1 extends keyof T,\n  K2 extends keyof T[K1],\n  K3 extends keyof T[K1][K2],\n  K4 extends keyof T[K1][K2][K3],\n  K5 extends keyof T[K1][K2][K3][K4],\n  K6 extends keyof T[K1][K2][K3][K4][K5]\n>(k1: K1, k2: K2, k3: K3, k4: K4, k5: K5, k6: K6, ...rest: string[]): OperatorFunction<T, unknown>;\n/** @deprecated Use {@link map} and optional chaining: `pluck('foo', 'bar')` is `map(x => x?.foo?.bar)`. Will be removed in v8. */\nexport function pluck<T>(...properties: string[]): OperatorFunction<T, unknown>;\n/* tslint:enable:max-line-length */\n\n/**\n * Maps each source value to its specified nested property.\n *\n * <span class=\"informal\">Like {@link map}, but meant only for picking one of\n * the nested properties of every emitted value.</span>\n *\n * ![](pluck.png)\n *\n * Given a list of strings or numbers describing a path to a property, retrieves\n * the value of a specified nested property from all values in the source\n * Observable. If a property can't be resolved, it will return `undefined` for\n * that value.\n *\n * ## Example\n *\n * Map every click to the tagName of the clicked target element\n *\n * ```ts\n * import { fromEvent, pluck } from 'rxjs';\n *\n * const clicks = fromEvent(document, 'click');\n * const tagNames = clicks.pipe(pluck('target', 'tagName'));\n *\n * tagNames.subscribe(x => console.log(x));\n * ```\n *\n * @see {@link map}\n *\n * @param properties The nested properties to pluck from each source\n * value.\n * @return A function that returns an Observable of property values from the\n * source values.\n * @deprecated Use {@link map} and optional chaining: `pluck('foo', 'bar')` is `map(x => x?.foo?.bar)`. Will be removed in v8.\n */\nexport function pluck<T, R>(...properties: Array<string | number | symbol>): OperatorFunction<T, R> {\n  const length = properties.length;\n  if (length === 0) {\n    throw new Error('list of properties cannot be empty.');\n  }\n  return map((x) => {\n    let currentProp: any = x;\n    for (let i = 0; i < length; i++) {\n      const p = currentProp?.[properties[i]];\n      if (typeof p !== 'undefined') {\n        currentProp = p;\n      } else {\n        return undefined;\n      }\n    }\n    return currentProp;\n  });\n}\n"], "mappings": "AAAA,SAASA,GAAG,QAAQ,OAAO;AAwF3B,OAAM,SAAUC,KAAKA,CAAA;EAAO,IAAAC,UAAA;OAAA,IAAAC,EAAA,IAA8C,EAA9CA,EAAA,GAAAC,SAAA,CAAAC,MAA8C,EAA9CF,EAAA,EAA8C;IAA9CD,UAAA,CAAAC,EAAA,IAAAC,SAAA,CAAAD,EAAA;;EAC1B,IAAME,MAAM,GAAGH,UAAU,CAACG,MAAM;EAChC,IAAIA,MAAM,KAAK,CAAC,EAAE;IAChB,MAAM,IAAIC,KAAK,CAAC,qCAAqC,CAAC;;EAExD,OAAON,GAAG,CAAC,UAACO,CAAC;IACX,IAAIC,WAAW,GAAQD,CAAC;IACxB,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,MAAM,EAAEI,CAAC,EAAE,EAAE;MAC/B,IAAMC,CAAC,GAAGF,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAGN,UAAU,CAACO,CAAC,CAAC,CAAC;MACtC,IAAI,OAAOC,CAAC,KAAK,WAAW,EAAE;QAC5BF,WAAW,GAAGE,CAAC;OAChB,MAAM;QACL,OAAOC,SAAS;;;IAGpB,OAAOH,WAAW;EACpB,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}