{"ast": null, "code": "import { KEY_PREFIX, REHYDRATE } from './constants';\n// @TODO remove once flow < 0.63 support is no longer required.\nexport default function createPersistoid(config) {\n  // defaults\n  var blacklist = config.blacklist || null;\n  var whitelist = config.whitelist || null;\n  var transforms = config.transforms || [];\n  var throttle = config.throttle || 0;\n  var storageKey = \"\".concat(config.keyPrefix !== undefined ? config.keyPrefix : KEY_PREFIX).concat(config.key);\n  var storage = config.storage;\n  var serialize;\n  if (config.serialize === false) {\n    serialize = function serialize(x) {\n      return x;\n    };\n  } else if (typeof config.serialize === 'function') {\n    serialize = config.serialize;\n  } else {\n    serialize = defaultSerialize;\n  }\n  var writeFailHandler = config.writeFailHandler || null; // initialize stateful values\n\n  var lastState = {};\n  var stagedState = {};\n  var keysToProcess = [];\n  var timeIterator = null;\n  var writePromise = null;\n  var update = function update(state) {\n    // add any changed keys to the queue\n    Object.keys(state).forEach(function (key) {\n      if (!passWhitelistBlacklist(key)) return; // is keyspace ignored? noop\n\n      if (lastState[key] === state[key]) return; // value unchanged? noop\n\n      if (keysToProcess.indexOf(key) !== -1) return; // is key already queued? noop\n\n      keysToProcess.push(key); // add key to queue\n    }); //if any key is missing in the new state which was present in the lastState,\n    //add it for processing too\n\n    Object.keys(lastState).forEach(function (key) {\n      if (state[key] === undefined && passWhitelistBlacklist(key) && keysToProcess.indexOf(key) === -1 && lastState[key] !== undefined) {\n        keysToProcess.push(key);\n      }\n    }); // start the time iterator if not running (read: throttle)\n\n    if (timeIterator === null) {\n      timeIterator = setInterval(processNextKey, throttle);\n    }\n    lastState = state;\n  };\n  function processNextKey() {\n    if (keysToProcess.length === 0) {\n      if (timeIterator) clearInterval(timeIterator);\n      timeIterator = null;\n      return;\n    }\n    var key = keysToProcess.shift();\n    var endState = transforms.reduce(function (subState, transformer) {\n      return transformer.in(subState, key, lastState);\n    }, lastState[key]);\n    if (endState !== undefined) {\n      try {\n        stagedState[key] = serialize(endState);\n      } catch (err) {\n        console.error('redux-persist/createPersistoid: error serializing state', err);\n      }\n    } else {\n      //if the endState is undefined, no need to persist the existing serialized content\n      delete stagedState[key];\n    }\n    if (keysToProcess.length === 0) {\n      writeStagedState();\n    }\n  }\n  function writeStagedState() {\n    // cleanup any removed keys just before write.\n    Object.keys(stagedState).forEach(function (key) {\n      if (lastState[key] === undefined) {\n        delete stagedState[key];\n      }\n    });\n    writePromise = storage.setItem(storageKey, serialize(stagedState)).catch(onWriteFail);\n  }\n  function passWhitelistBlacklist(key) {\n    if (whitelist && whitelist.indexOf(key) === -1 && key !== '_persist') return false;\n    if (blacklist && blacklist.indexOf(key) !== -1) return false;\n    return true;\n  }\n  function onWriteFail(err) {\n    // @TODO add fail handlers (typically storage full)\n    if (writeFailHandler) writeFailHandler(err);\n    if (err && process.env.NODE_ENV !== 'production') {\n      console.error('Error storing data', err);\n    }\n  }\n  var flush = function flush() {\n    while (keysToProcess.length !== 0) {\n      processNextKey();\n    }\n    return writePromise || Promise.resolve();\n  }; // return `persistoid`\n\n  return {\n    update: update,\n    flush: flush\n  };\n} // @NOTE in the future this may be exposed via config\n\nfunction defaultSerialize(data) {\n  return JSON.stringify(data);\n}", "map": {"version": 3, "names": ["KEY_PREFIX", "REHYDRATE", "createPersistoid", "config", "blacklist", "whitelist", "transforms", "throttle", "storageKey", "concat", "keyPrefix", "undefined", "key", "storage", "serialize", "x", "defaultSerialize", "writeFailHandler", "lastState", "stagedState", "keysToProcess", "timeIterator", "writePromise", "update", "state", "Object", "keys", "for<PERSON>ach", "<PERSON><PERSON><PERSON><PERSON><PERSON>lacklist", "indexOf", "push", "setInterval", "processNextKey", "length", "clearInterval", "shift", "endState", "reduce", "subState", "transformer", "in", "err", "console", "error", "writeStagedState", "setItem", "catch", "onWriteFail", "process", "env", "NODE_ENV", "flush", "Promise", "resolve", "data", "JSON", "stringify"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/node_modules/redux-persist/es/createPersistoid.js"], "sourcesContent": ["import { KEY_PREFIX, REHYDRATE } from './constants';\n// @TODO remove once flow < 0.63 support is no longer required.\nexport default function createPersistoid(config) {\n  // defaults\n  var blacklist = config.blacklist || null;\n  var whitelist = config.whitelist || null;\n  var transforms = config.transforms || [];\n  var throttle = config.throttle || 0;\n  var storageKey = \"\".concat(config.keyPrefix !== undefined ? config.keyPrefix : KEY_PREFIX).concat(config.key);\n  var storage = config.storage;\n  var serialize;\n\n  if (config.serialize === false) {\n    serialize = function serialize(x) {\n      return x;\n    };\n  } else if (typeof config.serialize === 'function') {\n    serialize = config.serialize;\n  } else {\n    serialize = defaultSerialize;\n  }\n\n  var writeFailHandler = config.writeFailHandler || null; // initialize stateful values\n\n  var lastState = {};\n  var stagedState = {};\n  var keysToProcess = [];\n  var timeIterator = null;\n  var writePromise = null;\n\n  var update = function update(state) {\n    // add any changed keys to the queue\n    Object.keys(state).forEach(function (key) {\n      if (!passWhitelistBlacklist(key)) return; // is keyspace ignored? noop\n\n      if (lastState[key] === state[key]) return; // value unchanged? noop\n\n      if (keysToProcess.indexOf(key) !== -1) return; // is key already queued? noop\n\n      keysToProcess.push(key); // add key to queue\n    }); //if any key is missing in the new state which was present in the lastState,\n    //add it for processing too\n\n    Object.keys(lastState).forEach(function (key) {\n      if (state[key] === undefined && passWhitelistBlacklist(key) && keysToProcess.indexOf(key) === -1 && lastState[key] !== undefined) {\n        keysToProcess.push(key);\n      }\n    }); // start the time iterator if not running (read: throttle)\n\n    if (timeIterator === null) {\n      timeIterator = setInterval(processNextKey, throttle);\n    }\n\n    lastState = state;\n  };\n\n  function processNextKey() {\n    if (keysToProcess.length === 0) {\n      if (timeIterator) clearInterval(timeIterator);\n      timeIterator = null;\n      return;\n    }\n\n    var key = keysToProcess.shift();\n    var endState = transforms.reduce(function (subState, transformer) {\n      return transformer.in(subState, key, lastState);\n    }, lastState[key]);\n\n    if (endState !== undefined) {\n      try {\n        stagedState[key] = serialize(endState);\n      } catch (err) {\n        console.error('redux-persist/createPersistoid: error serializing state', err);\n      }\n    } else {\n      //if the endState is undefined, no need to persist the existing serialized content\n      delete stagedState[key];\n    }\n\n    if (keysToProcess.length === 0) {\n      writeStagedState();\n    }\n  }\n\n  function writeStagedState() {\n    // cleanup any removed keys just before write.\n    Object.keys(stagedState).forEach(function (key) {\n      if (lastState[key] === undefined) {\n        delete stagedState[key];\n      }\n    });\n    writePromise = storage.setItem(storageKey, serialize(stagedState)).catch(onWriteFail);\n  }\n\n  function passWhitelistBlacklist(key) {\n    if (whitelist && whitelist.indexOf(key) === -1 && key !== '_persist') return false;\n    if (blacklist && blacklist.indexOf(key) !== -1) return false;\n    return true;\n  }\n\n  function onWriteFail(err) {\n    // @TODO add fail handlers (typically storage full)\n    if (writeFailHandler) writeFailHandler(err);\n\n    if (err && process.env.NODE_ENV !== 'production') {\n      console.error('Error storing data', err);\n    }\n  }\n\n  var flush = function flush() {\n    while (keysToProcess.length !== 0) {\n      processNextKey();\n    }\n\n    return writePromise || Promise.resolve();\n  }; // return `persistoid`\n\n\n  return {\n    update: update,\n    flush: flush\n  };\n} // @NOTE in the future this may be exposed via config\n\nfunction defaultSerialize(data) {\n  return JSON.stringify(data);\n}"], "mappings": "AAAA,SAASA,UAAU,EAAEC,SAAS,QAAQ,aAAa;AACnD;AACA,eAAe,SAASC,gBAAgBA,CAACC,MAAM,EAAE;EAC/C;EACA,IAAIC,SAAS,GAAGD,MAAM,CAACC,SAAS,IAAI,IAAI;EACxC,IAAIC,SAAS,GAAGF,MAAM,CAACE,SAAS,IAAI,IAAI;EACxC,IAAIC,UAAU,GAAGH,MAAM,CAACG,UAAU,IAAI,EAAE;EACxC,IAAIC,QAAQ,GAAGJ,MAAM,CAACI,QAAQ,IAAI,CAAC;EACnC,IAAIC,UAAU,GAAG,EAAE,CAACC,MAAM,CAACN,MAAM,CAACO,SAAS,KAAKC,SAAS,GAAGR,MAAM,CAACO,SAAS,GAAGV,UAAU,CAAC,CAACS,MAAM,CAACN,MAAM,CAACS,GAAG,CAAC;EAC7G,IAAIC,OAAO,GAAGV,MAAM,CAACU,OAAO;EAC5B,IAAIC,SAAS;EAEb,IAAIX,MAAM,CAACW,SAAS,KAAK,KAAK,EAAE;IAC9BA,SAAS,GAAG,SAASA,SAASA,CAACC,CAAC,EAAE;MAChC,OAAOA,CAAC;IACV,CAAC;EACH,CAAC,MAAM,IAAI,OAAOZ,MAAM,CAACW,SAAS,KAAK,UAAU,EAAE;IACjDA,SAAS,GAAGX,MAAM,CAACW,SAAS;EAC9B,CAAC,MAAM;IACLA,SAAS,GAAGE,gBAAgB;EAC9B;EAEA,IAAIC,gBAAgB,GAAGd,MAAM,CAACc,gBAAgB,IAAI,IAAI,CAAC,CAAC;;EAExD,IAAIC,SAAS,GAAG,CAAC,CAAC;EAClB,IAAIC,WAAW,GAAG,CAAC,CAAC;EACpB,IAAIC,aAAa,GAAG,EAAE;EACtB,IAAIC,YAAY,GAAG,IAAI;EACvB,IAAIC,YAAY,GAAG,IAAI;EAEvB,IAAIC,MAAM,GAAG,SAASA,MAAMA,CAACC,KAAK,EAAE;IAClC;IACAC,MAAM,CAACC,IAAI,CAACF,KAAK,CAAC,CAACG,OAAO,CAAC,UAAUf,GAAG,EAAE;MACxC,IAAI,CAACgB,sBAAsB,CAAChB,GAAG,CAAC,EAAE,OAAO,CAAC;;MAE1C,IAAIM,SAAS,CAACN,GAAG,CAAC,KAAKY,KAAK,CAACZ,GAAG,CAAC,EAAE,OAAO,CAAC;;MAE3C,IAAIQ,aAAa,CAACS,OAAO,CAACjB,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC;;MAE/CQ,aAAa,CAACU,IAAI,CAAClB,GAAG,CAAC,CAAC,CAAC;IAC3B,CAAC,CAAC,CAAC,CAAC;IACJ;;IAEAa,MAAM,CAACC,IAAI,CAACR,SAAS,CAAC,CAACS,OAAO,CAAC,UAAUf,GAAG,EAAE;MAC5C,IAAIY,KAAK,CAACZ,GAAG,CAAC,KAAKD,SAAS,IAAIiB,sBAAsB,CAAChB,GAAG,CAAC,IAAIQ,aAAa,CAACS,OAAO,CAACjB,GAAG,CAAC,KAAK,CAAC,CAAC,IAAIM,SAAS,CAACN,GAAG,CAAC,KAAKD,SAAS,EAAE;QAChIS,aAAa,CAACU,IAAI,CAAClB,GAAG,CAAC;MACzB;IACF,CAAC,CAAC,CAAC,CAAC;;IAEJ,IAAIS,YAAY,KAAK,IAAI,EAAE;MACzBA,YAAY,GAAGU,WAAW,CAACC,cAAc,EAAEzB,QAAQ,CAAC;IACtD;IAEAW,SAAS,GAAGM,KAAK;EACnB,CAAC;EAED,SAASQ,cAAcA,CAAA,EAAG;IACxB,IAAIZ,aAAa,CAACa,MAAM,KAAK,CAAC,EAAE;MAC9B,IAAIZ,YAAY,EAAEa,aAAa,CAACb,YAAY,CAAC;MAC7CA,YAAY,GAAG,IAAI;MACnB;IACF;IAEA,IAAIT,GAAG,GAAGQ,aAAa,CAACe,KAAK,CAAC,CAAC;IAC/B,IAAIC,QAAQ,GAAG9B,UAAU,CAAC+B,MAAM,CAAC,UAAUC,QAAQ,EAAEC,WAAW,EAAE;MAChE,OAAOA,WAAW,CAACC,EAAE,CAACF,QAAQ,EAAE1B,GAAG,EAAEM,SAAS,CAAC;IACjD,CAAC,EAAEA,SAAS,CAACN,GAAG,CAAC,CAAC;IAElB,IAAIwB,QAAQ,KAAKzB,SAAS,EAAE;MAC1B,IAAI;QACFQ,WAAW,CAACP,GAAG,CAAC,GAAGE,SAAS,CAACsB,QAAQ,CAAC;MACxC,CAAC,CAAC,OAAOK,GAAG,EAAE;QACZC,OAAO,CAACC,KAAK,CAAC,yDAAyD,EAAEF,GAAG,CAAC;MAC/E;IACF,CAAC,MAAM;MACL;MACA,OAAOtB,WAAW,CAACP,GAAG,CAAC;IACzB;IAEA,IAAIQ,aAAa,CAACa,MAAM,KAAK,CAAC,EAAE;MAC9BW,gBAAgB,CAAC,CAAC;IACpB;EACF;EAEA,SAASA,gBAAgBA,CAAA,EAAG;IAC1B;IACAnB,MAAM,CAACC,IAAI,CAACP,WAAW,CAAC,CAACQ,OAAO,CAAC,UAAUf,GAAG,EAAE;MAC9C,IAAIM,SAAS,CAACN,GAAG,CAAC,KAAKD,SAAS,EAAE;QAChC,OAAOQ,WAAW,CAACP,GAAG,CAAC;MACzB;IACF,CAAC,CAAC;IACFU,YAAY,GAAGT,OAAO,CAACgC,OAAO,CAACrC,UAAU,EAAEM,SAAS,CAACK,WAAW,CAAC,CAAC,CAAC2B,KAAK,CAACC,WAAW,CAAC;EACvF;EAEA,SAASnB,sBAAsBA,CAAChB,GAAG,EAAE;IACnC,IAAIP,SAAS,IAAIA,SAAS,CAACwB,OAAO,CAACjB,GAAG,CAAC,KAAK,CAAC,CAAC,IAAIA,GAAG,KAAK,UAAU,EAAE,OAAO,KAAK;IAClF,IAAIR,SAAS,IAAIA,SAAS,CAACyB,OAAO,CAACjB,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,KAAK;IAC5D,OAAO,IAAI;EACb;EAEA,SAASmC,WAAWA,CAACN,GAAG,EAAE;IACxB;IACA,IAAIxB,gBAAgB,EAAEA,gBAAgB,CAACwB,GAAG,CAAC;IAE3C,IAAIA,GAAG,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MAChDR,OAAO,CAACC,KAAK,CAAC,oBAAoB,EAAEF,GAAG,CAAC;IAC1C;EACF;EAEA,IAAIU,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAG;IAC3B,OAAO/B,aAAa,CAACa,MAAM,KAAK,CAAC,EAAE;MACjCD,cAAc,CAAC,CAAC;IAClB;IAEA,OAAOV,YAAY,IAAI8B,OAAO,CAACC,OAAO,CAAC,CAAC;EAC1C,CAAC,CAAC,CAAC;;EAGH,OAAO;IACL9B,MAAM,EAAEA,MAAM;IACd4B,KAAK,EAAEA;EACT,CAAC;AACH,CAAC,CAAC;;AAEF,SAASnC,gBAAgBA,CAACsC,IAAI,EAAE;EAC9B,OAAOC,IAAI,CAACC,SAAS,CAACF,IAAI,CAAC;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}