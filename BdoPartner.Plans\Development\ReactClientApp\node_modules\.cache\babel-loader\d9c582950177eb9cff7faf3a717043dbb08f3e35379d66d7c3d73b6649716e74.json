{"ast": null, "code": "import { asyncScheduler } from '../scheduler/async';\nimport { timer } from './timer';\nexport function interval(period, scheduler) {\n  if (period === void 0) {\n    period = 0;\n  }\n  if (scheduler === void 0) {\n    scheduler = asyncScheduler;\n  }\n  if (period < 0) {\n    period = 0;\n  }\n  return timer(period, period, scheduler);\n}", "map": {"version": 3, "names": ["asyncScheduler", "timer", "interval", "period", "scheduler"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\observable\\interval.ts"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { asyncScheduler } from '../scheduler/async';\nimport { SchedulerLike } from '../types';\nimport { timer } from './timer';\n\n/**\n * Creates an Observable that emits sequential numbers every specified\n * interval of time, on a specified {@link SchedulerLike}.\n *\n * <span class=\"informal\">Emits incremental numbers periodically in time.</span>\n *\n * ![](interval.png)\n *\n * `interval` returns an Observable that emits an infinite sequence of\n * ascending integers, with a constant interval of time of your choosing\n * between those emissions. The first emission is not sent immediately, but\n * only after the first period has passed. By default, this operator uses the\n * `async` {@link SchedulerLike} to provide a notion of time, but you may pass any\n * {@link SchedulerLike} to it.\n *\n * ## Example\n *\n * Emits ascending numbers, one every second (1000ms) up to the number 3\n *\n * ```ts\n * import { interval, take } from 'rxjs';\n *\n * const numbers = interval(1000);\n *\n * const takeFourNumbers = numbers.pipe(take(4));\n *\n * takeFourNumbers.subscribe(x => console.log('Next: ', x));\n *\n * // Logs:\n * // Next: 0\n * // Next: 1\n * // Next: 2\n * // Next: 3\n * ```\n *\n * @see {@link timer}\n * @see {@link delay}\n *\n * @param period The interval size in milliseconds (by default) or the time unit determined\n * by the scheduler's clock.\n * @param scheduler The {@link SchedulerLike} to use for scheduling the emission of values,\n * and providing a notion of \"time\".\n * @return An Observable that emits a sequential number each time interval.\n */\nexport function interval(period = 0, scheduler: SchedulerLike = asyncScheduler): Observable<number> {\n  if (period < 0) {\n    // We cannot schedule an interval in the past.\n    period = 0;\n  }\n\n  return timer(period, period, scheduler);\n}\n"], "mappings": "AACA,SAASA,cAAc,QAAQ,oBAAoB;AAEnD,SAASC,KAAK,QAAQ,SAAS;AA8C/B,OAAM,SAAUC,QAAQA,CAACC,MAAU,EAAEC,SAAyC;EAArD,IAAAD,MAAA;IAAAA,MAAA,IAAU;EAAA;EAAE,IAAAC,SAAA;IAAAA,SAAA,GAAAJ,cAAyC;EAAA;EAC5E,IAAIG,MAAM,GAAG,CAAC,EAAE;IAEdA,MAAM,GAAG,CAAC;;EAGZ,OAAOF,KAAK,CAACE,MAAM,EAAEA,MAAM,EAAEC,SAAS,CAAC;AACzC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}