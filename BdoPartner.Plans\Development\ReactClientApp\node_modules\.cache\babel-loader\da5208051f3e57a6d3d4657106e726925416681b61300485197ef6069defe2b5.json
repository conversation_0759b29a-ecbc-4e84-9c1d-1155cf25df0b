{"ast": null, "code": "var _jsxFileName = \"C:\\\\git\\\\Partner-Site\\\\BdoPartner.Plans\\\\Development\\\\ReactClientApp\\\\src\\\\core\\\\auth\\\\components\\\\logout.jsx\";\n/* /src/components/auth/logout.jsx */\n\nimport * as React from \"react\";\nimport { AuthConsumer } from \"./authProvider\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const Logout = () => /*#__PURE__*/_jsxDEV(AuthConsumer, {\n  children: ({\n    logout\n  }) => {\n    logout();\n    return /*#__PURE__*/_jsxDEV(\"span\", {\n      children: \"loading\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 14\n    }, this);\n  }\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 7,\n  columnNumber: 3\n}, this);\n_c = Logout;\nvar _c;\n$RefreshReg$(_c, \"Logout\");", "map": {"version": 3, "names": ["React", "AuthConsumer", "jsxDEV", "_jsxDEV", "Logout", "children", "logout", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/core/auth/components/logout.jsx"], "sourcesContent": ["/* /src/components/auth/logout.jsx */\r\n\r\nimport * as React from \"react\";\r\nimport { AuthConsumer } from \"./authProvider\";\r\n\r\nexport const Logout = () => (\r\n  <AuthConsumer>\r\n    {({ logout }) => {\r\n      logout();\r\n      return <span>loading</span>;\r\n    }}\r\n  </AuthConsumer>\r\n);\r\n"], "mappings": ";AAAA;;AAEA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,YAAY,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,OAAO,MAAMC,MAAM,GAAGA,CAAA,kBACpBD,OAAA,CAACF,YAAY;EAAAI,QAAA,EACVA,CAAC;IAAEC;EAAO,CAAC,KAAK;IACfA,MAAM,CAAC,CAAC;IACR,oBAAOH,OAAA;MAAAE,QAAA,EAAM;IAAO;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAC7B;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACW,CACf;AAACC,EAAA,GAPWP,MAAM;AAAA,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}