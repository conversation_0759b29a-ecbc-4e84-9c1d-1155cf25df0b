{"ast": null, "code": "import { identity } from '../util/identity';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nimport { pipe } from '../util/pipe';\nimport { mergeMap } from './mergeMap';\nimport { toArray } from './toArray';\nexport function joinAllInternals(joinFn, project) {\n  return pipe(toArray(), mergeMap(function (sources) {\n    return joinFn(sources);\n  }), project ? mapOneOrManyArgs(project) : identity);\n}", "map": {"version": 3, "names": ["identity", "mapOneOrManyArgs", "pipe", "mergeMap", "toArray", "joinAllInternals", "joinFn", "project", "sources"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\joinAllInternals.ts"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { ObservableInput, OperatorFunction } from '../types';\nimport { identity } from '../util/identity';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nimport { pipe } from '../util/pipe';\nimport { mergeMap } from './mergeMap';\nimport { toArray } from './toArray';\n\n/**\n * Collects all of the inner sources from source observable. Then, once the\n * source completes, joins the values using the given static.\n *\n * This is used for {@link combineLatestAll} and {@link zipAll} which both have the\n * same behavior of collecting all inner observables, then operating on them.\n *\n * @param joinFn The type of static join to apply to the sources collected\n * @param project The projection function to apply to the values, if any\n */\nexport function joinAllInternals<T, R>(joinFn: (sources: ObservableInput<T>[]) => Observable<T>, project?: (...args: any[]) => R) {\n  return pipe(\n    // Collect all inner sources into an array, and emit them when the\n    // source completes.\n    toArray() as OperatorFunction<ObservableInput<T>, ObservableInput<T>[]>,\n    // Run the join function on the collected array of inner sources.\n    mergeMap((sources) => joinFn(sources)),\n    // If a projection function was supplied, apply it to each result.\n    project ? mapOneOrManyArgs(project) : (identity as any)\n  );\n}\n"], "mappings": "AAEA,SAASA,QAAQ,QAAQ,kBAAkB;AAC3C,SAASC,gBAAgB,QAAQ,0BAA0B;AAC3D,SAASC,IAAI,QAAQ,cAAc;AACnC,SAASC,QAAQ,QAAQ,YAAY;AACrC,SAASC,OAAO,QAAQ,WAAW;AAYnC,OAAM,SAAUC,gBAAgBA,CAAOC,MAAwD,EAAEC,OAA+B;EAC9H,OAAOL,IAAI,CAGTE,OAAO,EAAgE,EAEvED,QAAQ,CAAC,UAACK,OAAO;IAAK,OAAAF,MAAM,CAACE,OAAO,CAAC;EAAf,CAAe,CAAC,EAEtCD,OAAO,GAAGN,gBAAgB,CAACM,OAAO,CAAC,GAAIP,QAAgB,CACxD;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}