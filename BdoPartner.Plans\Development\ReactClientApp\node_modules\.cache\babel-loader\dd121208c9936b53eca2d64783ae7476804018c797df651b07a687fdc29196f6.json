{"ast": null, "code": "import { SurveyPDF } from \"survey-pdf\";\nimport { configureSurveyJSLicense } from \"../../core/surveyjs/licenseConfig\";\nimport { messageService } from \"../../core/message/messageService\";\nimport { getFormStatusName } from \"../../core/enumertions/formStatus\";\n\n/**\r\n * Shared PDF export utilities for Partner Plan Questionnaire\r\n */\n\n/**\r\n * Recursively processes survey elements including nested panels for PDF export\r\n * @param {Array} elements - Array of survey elements to process\r\n * @returns {Array} Processed elements array\r\n */\nconst processElementsRecursively = elements => {\n  if (!elements || !Array.isArray(elements)) {\n    return [];\n  }\n  const processedElements = [];\n  elements.forEach((element, elementIndex) => {\n    if (!element || !element.type) {\n      return;\n    }\n\n    // Skip invisible elements\n    if (element.visible === false) {\n      return;\n    }\n    const cleanElement = {\n      type: element.type,\n      name: element.name || `element${elementIndex + 1}`,\n      title: element.title || element.name || `Element ${elementIndex + 1}`\n    };\n\n    // For panels, only show title if it's meaningful (not just the auto-generated name)\n    if (element.type === \"panel\") {\n      // Hide panel title if it's just the auto-generated name (like \"panel5\", \"panel6\", etc.)\n      const isAutoGeneratedName = !element.title || element.title === element.name;\n      const isGenericPanelName = element.name && /^panel\\d*$/i.test(element.name);\n      if (isAutoGeneratedName && isGenericPanelName) {\n        cleanElement.title = \"\"; // Hide the title for auto-generated panel names\n      }\n    }\n\n    // Copy all relevant properties for different element types\n    if (element.isRequired) cleanElement.isRequired = element.isRequired;\n    if (element.choices) cleanElement.choices = element.choices;\n    if (element.rateMin !== undefined) cleanElement.rateMin = element.rateMin;\n    if (element.rateMax !== undefined) cleanElement.rateMax = element.rateMax;\n    if (element.rateStep !== undefined) cleanElement.rateStep = element.rateStep;\n    if (element.html) cleanElement.html = element.html;\n    if (element.description) cleanElement.description = element.description;\n    if (element.placeholder) cleanElement.placeholder = element.placeholder;\n    if (element.defaultValue !== undefined) cleanElement.defaultValue = element.defaultValue;\n    if (element.readOnly) cleanElement.readOnly = element.readOnly;\n    if (element.visible !== undefined) cleanElement.visible = element.visible;\n    if (element.width) cleanElement.width = element.width;\n    if (element.startWithNewLine !== undefined) cleanElement.startWithNewLine = element.startWithNewLine;\n    if (element.state) cleanElement.state = element.state;\n    if (element.autoGrow) cleanElement.autoGrow = element.autoGrow;\n\n    // Handle nested elements for panels recursively\n    if (element.type === \"panel\" && element.elements && element.elements.length > 0) {\n      cleanElement.elements = processElementsRecursively(element.elements);\n    }\n    processedElements.push(cleanElement);\n  });\n  return processedElements;\n};\n\n/**\r\n * Handles PDF export using Survey.js built-in PDF functionality\r\n * @param {Object} surveyModel - The Survey.js model instance\r\n * @param {Object} form - The form data object\r\n * @returns {Promise<void>}\r\n */\nconst handleExportToPDF = async (surveyModel, form) => {\n  if (!surveyModel) {\n    messageService.errorToast(\"Survey not loaded. Please try again.\");\n    return;\n  }\n  try {\n    // Show loading message\n    messageService.infoToast(\"Generating PDF... Please wait.\");\n\n    // Get the survey JSON and ensure it's properly formatted\n    const surveyJson = surveyModel.toJSON();\n\n    // Validate survey JSON structure\n    if (!surveyJson || !surveyJson.pages || surveyJson.pages.length === 0) {\n      throw new Error(\"Invalid survey structure for PDF export\");\n    }\n    console.log(\"Original survey JSON for PDF:\", surveyJson);\n\n    // Create a comprehensive survey JSON for PDF export with recursive element processing\n    const cleanSurveyJson = {\n      title: surveyJson.title || \"2025 Partner Plan\",\n      showTitle: true,\n      showPageTitles: true,\n      showQuestionNumbers: \"on\",\n      progressBarLocation: \"none\",\n      showProgressBar: \"none\",\n      questionsOnPageMode: \"singlePage\",\n      pages: []\n    };\n\n    // Add partner details as the first page if form data is available\n    if (form) {\n      cleanSurveyJson.pages.push({\n        name: \"partnerDetailsPage\",\n        title: \"Partner Details\",\n        elements: [{\n          type: \"html\",\n          name: \"partnerDetailsHtml\",\n          html: `\n              <table style=\"width: 100%; border-collapse: collapse; margin-bottom: 15px;\">\n                <tr style=\"background: #f8f9fa;\">\n                  <td style=\"padding: 8px; border: 1px solid #ddd; font-weight: bold; width: 25%;\">Partner Name:</td>\n                  <td style=\"padding: 8px; border: 1px solid #ddd; width: 25%;\">${form.partnerName || \"N/A\"}</td>\n                  <td style=\"padding: 8px; border: 1px solid #ddd; font-weight: bold; width: 25%;\">Service Line:</td>\n                  <td style=\"padding: 8px; border: 1px solid #ddd; width: 25%;\">${form.serviceLine || \"N/A\"}</td>\n                </tr>\n                <tr>\n                  <td style=\"padding: 8px; border: 1px solid #ddd; font-weight: bold;\">Sub-Service Line:</td>\n                  <td style=\"padding: 8px; border: 1px solid #ddd;\">${form.subServiceLine || \"N/A\"}</td>\n                  <td style=\"padding: 8px; border: 1px solid #ddd; font-weight: bold;\">Location:</td>\n                  <td style=\"padding: 8px; border: 1px solid #ddd;\">${form.location || \"N/A\"}</td>\n                </tr>\n                <tr style=\"background: #f8f9fa;\">\n                  <td style=\"padding: 8px; border: 1px solid #ddd; font-weight: bold;\">Primary Reviewer:</td>\n                  <td style=\"padding: 8px; border: 1px solid #ddd;\">${form.primaryReviewerName || \"N/A\"}</td>\n                  <td style=\"padding: 8px; border: 1px solid #ddd; font-weight: bold;\">Secondary Reviewer:</td>\n                  <td style=\"padding: 8px; border: 1px solid #ddd;\">${form.secondaryReviewerName || \"N/A\"}</td>\n                </tr>\n                <tr>\n                  <td style=\"padding: 8px; border: 1px solid #ddd; font-weight: bold;\">Status:</td>\n                  <td style=\"padding: 8px; border: 1px solid #ddd;\">${form.statusString || getFormStatusName(form.status) || \"DRAFT\"}</td>\n                  <td style=\"padding: 8px; border: 1px solid #ddd; font-weight: bold;\">Generated:</td>\n                  <td style=\"padding: 8px; border: 1px solid #ddd;\">${new Date().toLocaleDateString()}</td>\n                </tr>\n              </table>\n            `\n        }]\n      });\n    }\n\n    // Add all survey pages with recursive element processing\n    const surveyPages = surveyJson.pages.filter(page => page && page.elements && page.elements.length > 0 && page.visible !== false).map((page, pageIndex) => ({\n      name: page.name || `page${pageIndex + 1}`,\n      title: page.title || page.name || `Page ${pageIndex + 1}`,\n      description: page.description || \"\",\n      elements: processElementsRecursively(page.elements)\n    }));\n    cleanSurveyJson.pages.push(...surveyPages);\n\n    // Create PDF export options with proper A4 configuration\n    const options = {\n      fontSize: 11,\n      fontName: \"Helvetica\",\n      margins: {\n        left: 10,\n        // Smaller margins for better page utilization\n        right: 10,\n        top: 15,\n        bot: 15\n      },\n      format: \"a4\",\n      orientation: \"p\",\n      // 'p' for portrait, 'l' for landscape\n      // PDF generation options\n      compress: false,\n      htmlRenderAs: \"auto\",\n      // Let SurveyJS decide how to render HTML\n      matrixRenderAs: \"auto\",\n      // Render matrices as tables when possible\n      readonlyRenderAs: \"text\",\n      // Render readonly elements as text\n      applyImageFit: true,\n      // Apply image fit settings\n      useCustomFontInHtml: false\n    };\n\n    // Configure Survey.js commercial license before creating SurveyPDF\n    configureSurveyJSLicense();\n\n    // Create SurveyPDF instance with cleaned JSON\n    const surveyPDF = new SurveyPDF(cleanSurveyJson, options);\n\n    // Set to read-only mode for better PDF generation (filled form)\n    surveyPDF.readOnly = true;\n\n    // Set the survey data for the PDF if available\n    const surveyData = surveyModel.data || {};\n    console.log(\"Survey data for PDF:\", surveyData);\n    if (Object.keys(surveyData).length > 0) {\n      // Clean the data to ensure all values are serializable and safe\n      const cleanData = {};\n      Object.keys(surveyData).forEach(key => {\n        const value = surveyData[key];\n        if (value !== undefined && value !== null && key && typeof key === \"string\") {\n          if (typeof value === \"string\" || typeof value === \"number\" || typeof value === \"boolean\") {\n            cleanData[key] = value;\n          } else if (Array.isArray(value)) {\n            cleanData[key] = value.join(\", \");\n          } else if (typeof value === \"object\") {\n            cleanData[key] = JSON.stringify(value);\n          }\n        }\n      });\n      console.log(\"Cleaned data for PDF:\", cleanData);\n      surveyPDF.data = cleanData;\n    }\n\n    // Generate and download the PDF\n    const partnerName = form !== null && form !== void 0 && form.partnerName ? form.partnerName.replace(/[^a-zA-Z0-9\\s]/g, \"\").replace(/\\s+/g, \"_\").substring(0, 50) : \"Survey\";\n    const fileName = `Partner_Plan_${partnerName}_${new Date().toISOString().split(\"T\")[0]}.pdf`;\n\n    // Try to generate the PDF\n    try {\n      await surveyPDF.save(fileName);\n      console.log(\"PDF save method completed successfully\");\n      messageService.successToast(\"PDF exported successfully!\");\n    } catch (saveError) {\n      console.error(\"Error in surveyPDF.save():\", saveError);\n\n      // Fallback: Try to generate PDF as blob and download manually\n      try {\n        console.log(\"Attempting fallback PDF generation...\");\n        const pdfBlob = await surveyPDF.raw();\n        if (pdfBlob) {\n          // Create download link manually\n          const url = window.URL.createObjectURL(pdfBlob);\n          const link = document.createElement(\"a\");\n          link.href = url;\n          link.download = fileName;\n          document.body.appendChild(link);\n          link.click();\n          document.body.removeChild(link);\n          window.URL.revokeObjectURL(url);\n          console.log(\"Fallback PDF download completed\");\n          messageService.successToast(\"PDF exported successfully using fallback method!\");\n        } else {\n          throw new Error(\"Failed to generate PDF blob\");\n        }\n      } catch (fallbackError) {\n        console.error(\"Fallback PDF generation also failed:\", fallbackError);\n        throw saveError; // Re-throw original error\n      }\n    }\n  } catch (error) {\n    console.error(\"Error exporting to PDF:\", error);\n\n    // Provide more specific error messages\n    let errorMessage = \"Failed to export PDF. \";\n    if (error.message && error.message.includes(\"indexOf\")) {\n      errorMessage += \"There may be an issue with the survey data format.\";\n    } else if (error.message) {\n      errorMessage += error.message;\n    } else {\n      errorMessage += \"Please try again or contact support if the issue persists.\";\n    }\n    messageService.errorToast(errorMessage);\n  }\n};\n\n/**\r\n * Generates HTML content for print/PDF export\r\n * @param {Object} form - The form data object\r\n * @param {Object} surveyJson - The survey JSON structure\r\n * @param {Object} surveyData - The survey response data\r\n * @returns {string} Complete HTML content for printing\r\n */\nconst generatePrintHTMLContent = (form, surveyJson, surveyData) => {\n  // Create HTML content for printing\n  let htmlContent = `\n    <!DOCTYPE html>\n    <html>\n    <head>\n      <title>Partner Plan - ${(form === null || form === void 0 ? void 0 : form.partnerName) || \"Survey\"}</title>\n      <style>\n        /* A4 Portrait dimensions: 210mm x 297mm (8.27\" x 11.69\") */\n        @page {\n          size: A4 portrait;\n          margin: 20mm 15mm 20mm 15mm;\n        }\n\n        body {\n          font-family: Arial, sans-serif;\n          margin: 0;\n          padding: 10px;\n          line-height: 1.4;\n          font-size: 10px;\n          width: 100%;\n          max-width: 210mm; /* A4 width minus margins */\n          box-sizing: border-box;\n          margin: 0 auto; /* Center content */\n        }\n\n        .header {\n          color: #ED1A3B;\n          border-bottom: 2px solid #ED1A3B;\n          padding-bottom: 8px;\n          margin-bottom: 15px;\n          text-align: center;\n          clear: both;\n          width: 100%;\n        }\n\n        .header h1 {\n          margin: 0;\n          font-size: 16px;\n          font-weight: bold;\n        }\n\n        .partner-info {\n          background: #f8f9fa;\n          padding: 8px;\n          margin-bottom: 15px;\n          border: 1px solid #ddd;\n          border-radius: 3px;\n          font-size: 9px;\n          clear: both;\n          width: 100%;\n          display: block;\n        }\n\n        /* Main content area */\n        .content-area {\n          width: 100%;\n          clear: both;\n          display: block;\n        }\n\n        .question {\n          margin-bottom: 12px;\n          page-break-inside: avoid;\n          width: 100%;\n          clear: both;\n          display: block;\n        }\n\n        .question-title {\n          font-weight: bold;\n          color: #333;\n          margin-bottom: 5px;\n          font-size: 10px;\n          display: block;\n          width: 100%;\n        }\n\n        .answer {\n          background: #fff;\n          padding: 6px;\n          border: 1px solid #ddd;\n          border-radius: 2px;\n          min-height: 18px;\n          font-size: 9px;\n          word-wrap: break-word;\n          overflow-wrap: break-word;\n          display: block;\n          width: 100%;\n          box-sizing: border-box;\n        }\n\n        .page-title {\n          color: #ED1A3B;\n          font-size: 14px;\n          font-weight: bold;\n          margin: 15px 0 10px 0;\n          border-bottom: 1px solid #ED1A3B;\n          page-break-before: auto;\n          clear: both;\n          width: 100%;\n          display: block;\n        }\n\n        .panel-title-main {\n          color: #ED1A3B;\n          font-size: 12px;\n          font-weight: bold;\n          margin: 15px 0 10px 0;\n          border-bottom: 1px solid #ED1A3B;\n          clear: both;\n          width: 100%;\n          display: block;\n        }\n\n        .panel-title-sub {\n          color: #666;\n          font-size: 11px;\n          font-weight: bold;\n          margin: 12px 0 8px 0;\n          clear: both;\n          width: 100%;\n          display: block;\n        }\n\n        .panel-description {\n          margin-bottom: 10px;\n          font-style: italic;\n          color: #666;\n          font-size: 9px;\n          clear: both;\n          width: 100%;\n          display: block;\n        }\n\n        .html-content {\n          margin: 10px 0;\n          font-size: 9px;\n          width: 100%;\n          clear: both;\n          display: block;\n        }\n\n        /* Auto-fitting table layout */\n        .html-content table {\n          border-collapse: collapse;\n          width: 100%;\n          margin: 8px 0;\n          font-size: 8px;\n          table-layout: auto;\n          word-wrap: break-word;\n        }\n\n        .html-content th, .html-content td {\n          border: 1px solid #ddd;\n          padding: 3px;\n          text-align: left;\n          word-wrap: break-word;\n          overflow-wrap: break-word;\n          max-width: 0;\n          overflow: hidden;\n        }\n\n        .html-content th {\n          background-color: #f2f2f2;\n          font-weight: bold;\n        }\n\n        /* Additional table styles for better auto-fitting */\n        table {\n          width: 100%;\n          border-collapse: collapse;\n          margin: 12px 0;\n          table-layout: auto;\n          font-size: 8px;\n          clear: both;\n          display: table;\n        }\n\n        th, td {\n          border: 1px solid #ddd;\n          padding: 4px;\n          text-align: left;\n          word-wrap: break-word;\n          overflow-wrap: break-word;\n          vertical-align: top;\n        }\n\n        th {\n          background-color: #f2f2f2;\n          font-weight: bold;\n        }\n      </style>\n    </head>\n    <body>\n      <div class=\"header\">\n        <h1>BDO Partner Planning Tool - 2025</h1>\n      </div>\n      <div class=\"partner-info\">\n        <table>\n          <tr>\n            <th>Partner Name:</th>\n            <td>${(form === null || form === void 0 ? void 0 : form.partnerName) || \"N/A\"}</td>\n            <th>Service Line:</th>\n            <td>${(form === null || form === void 0 ? void 0 : form.serviceLine) || \"N/A\"}</td>\n          </tr>\n          <tr>\n            <th>Sub-Service Line:</th>\n            <td>${(form === null || form === void 0 ? void 0 : form.subServiceLine) || \"N/A\"}</td>\n            <th>Location:</th>\n            <td>${(form === null || form === void 0 ? void 0 : form.location) || \"N/A\"}</td>\n          </tr>\n          <tr>\n            <th>Primary Reviewer:</th>\n            <td>${(form === null || form === void 0 ? void 0 : form.primaryReviewerName) || \"N/A\"}</td>\n            <th>Secondary Reviewer:</th>\n            <td>${(form === null || form === void 0 ? void 0 : form.secondaryReviewerName) || \"N/A\"}</td>\n          </tr>\n          <tr>\n            <th>Status:</th>\n            <td>${(form === null || form === void 0 ? void 0 : form.statusString) || getFormStatusName(form === null || form === void 0 ? void 0 : form.status) || \"DRAFT\"}</td>\n            <th>Generated:</th>\n            <td>${new Date().toLocaleDateString()}</td>\n          </tr>\n        </table>\n      </div>\n      <div class=\"content-area survey-content\">\n  `;\n\n  // Recursive function to render elements including nested panels\n  const renderElementsRecursively = (elements, level = 0) => {\n    if (!elements || !Array.isArray(elements)) {\n      return \"\";\n    }\n    let content = \"\";\n    elements.forEach(element => {\n      if (!element || element.visible === false) {\n        return;\n      }\n      if (element.type === \"panel\") {\n        // Only render panel title if it's meaningful (not just auto-generated name)\n        const isAutoGeneratedName = !element.title || element.title === element.name;\n        const isGenericPanelName = element.name && /^panel\\d*$/i.test(element.name);\n        const shouldShowTitle = !(isAutoGeneratedName && isGenericPanelName);\n        if (shouldShowTitle && element.title) {\n          const panelTitleClass = level === 0 ? \"panel-title-main\" : \"panel-title-sub\";\n          content += `<div class=\"${panelTitleClass}\" style=\"margin-top: ${level === 0 ? \"20px\" : \"15px\"}; font-weight: bold; color: #ED1A3B; font-size: ${level === 0 ? \"16px\" : \"14px\"};\">${element.title}</div>`;\n        }\n\n        // Render panel description if exists\n        if (element.description) {\n          content += `<div class=\"panel-description\" style=\"margin-bottom: 10px; font-style: italic;\">${element.description}</div>`;\n        }\n\n        // Recursively render nested elements\n        if (element.elements) {\n          content += renderElementsRecursively(element.elements, level + 1);\n        }\n      } else if (element.type === \"html\") {\n        // Render HTML elements\n        content += `<div class=\"html-content\" style=\"margin: 10px 0;\">${element.html || \"\"}</div>`;\n      } else {\n        // Render regular questions\n        const answer = surveyData[element.name] || \"No answer provided\";\n        const questionStyle = level > 0 ? \"margin-left: 20px;\" : \"\";\n        content += `\n          <div class=\"question\" style=\"${questionStyle}\">\n            <div class=\"question-title\">${element.title || element.name}</div>\n            <div class=\"answer\">${Array.isArray(answer) ? answer.join(\", \") : answer}</div>\n          </div>\n        `;\n      }\n    });\n    return content;\n  };\n\n  // Add survey content\n  if (surveyJson.pages) {\n    surveyJson.pages.forEach(page => {\n      if (page.elements && page.elements.length > 0 && page.visible !== false) {\n        htmlContent += `<div class=\"page-container\">`;\n        htmlContent += `<div class=\"page-title\">${page.title || page.name || \"Survey Page\"}</div>`;\n        htmlContent += renderElementsRecursively(page.elements);\n        htmlContent += `</div>`;\n      }\n    });\n  }\n  htmlContent += `\n      </div>\n    </body>\n    </html>\n  `;\n  return htmlContent;\n};\n\n/**\r\n * Alternative PDF export method using browser's print functionality\r\n * @param {Object} surveyModel - The Survey.js model instance\r\n * @param {Object} form - The form data object\r\n * @returns {void}\r\n */\nconst handleAlternativePDFExport = (surveyModel, form) => {\n  if (!surveyModel) {\n    messageService.errorToast(\"Survey not loaded. Please try again.\");\n    return;\n  }\n  try {\n    // Create a new window with the survey content\n    const printWindow = window.open(\"\", \"_blank\");\n    if (!printWindow) {\n      messageService.errorToast(\"Please allow pop-ups for PDF export to work.\");\n      return;\n    }\n\n    // Get survey data\n    const surveyData = surveyModel.data || {};\n    const surveyJson = surveyModel.toJSON();\n\n    // Create HTML content for printing - this will be continued in the next chunk\n    let htmlContent = generatePrintHTMLContent(form, surveyJson, surveyData);\n\n    // Write content to new window and trigger print\n    printWindow.document.write(htmlContent);\n    printWindow.document.close();\n\n    // Wait for content to load, then print\n    printWindow.onload = () => {\n      setTimeout(() => {\n        printWindow.print();\n        printWindow.close();\n      }, 1000);\n    };\n\n    //messageService.infoToast(\"Print dialog opened. Choose 'Save as PDF' in your browser's print options.\");\n  } catch (error) {\n    console.error(\"Error with alternative PDF export:\", error);\n    messageService.errorToast(\"Failed to open print dialog. Please try the main PDF export.\");\n  }\n};\nconst PDFExportUtilities = {\n  handleExportToPDF,\n  handleAlternativePDFExport,\n  processElementsRecursively,\n  generatePrintHTMLContent\n};\nexport default PDFExportUtilities;", "map": {"version": 3, "names": ["SurveyPDF", "configureSurveyJSLicense", "messageService", "getFormStatusName", "processElementsRecursively", "elements", "Array", "isArray", "processedElements", "for<PERSON>ach", "element", "elementIndex", "type", "visible", "cleanElement", "name", "title", "isAutoGeneratedName", "isGenericPanelName", "test", "isRequired", "choices", "rateMin", "undefined", "rateMax", "rateStep", "html", "description", "placeholder", "defaultValue", "readOnly", "width", "startWithNewLine", "state", "autoGrow", "length", "push", "handleExportToPDF", "surveyModel", "form", "errorToast", "infoToast", "surveyJson", "toJSON", "pages", "Error", "console", "log", "cleanSurveyJson", "showTitle", "showPageTitles", "showQuestionNumbers", "progressBarLocation", "showProgressBar", "questionsOnPageMode", "partner<PERSON>ame", "serviceLine", "subServiceLine", "location", "primaryReviewerName", "secondaryReviewerName", "statusString", "status", "Date", "toLocaleDateString", "surveyPages", "filter", "page", "map", "pageIndex", "options", "fontSize", "fontName", "margins", "left", "right", "top", "bot", "format", "orientation", "compress", "htmlRenderAs", "matrixRenderAs", "readonlyRenderAs", "applyImageFit", "useCustomFontInHtml", "surveyPDF", "surveyData", "data", "Object", "keys", "cleanData", "key", "value", "join", "JSON", "stringify", "replace", "substring", "fileName", "toISOString", "split", "save", "successToast", "saveError", "error", "pdfBlob", "raw", "url", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "fallback<PERSON><PERSON>r", "errorMessage", "message", "includes", "generatePrintHTMLContent", "htmlContent", "renderElementsRecursively", "level", "content", "shouldShowTitle", "panelTitleClass", "answer", "questionStyle", "handleAlternativePDFExport", "printWindow", "open", "write", "close", "onload", "setTimeout", "print", "PDFExportUtilities"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/components/questionnaire/pdfExportUtilities.js"], "sourcesContent": ["import { SurveyPDF } from \"survey-pdf\";\r\nimport { configureSurveyJSLicense } from \"../../core/surveyjs/licenseConfig\";\r\nimport { messageService } from \"../../core/message/messageService\";\r\nimport { getFormStatusName } from \"../../core/enumertions/formStatus\";\r\n\r\n/**\r\n * Shared PDF export utilities for Partner Plan Questionnaire\r\n */\r\n\r\n/**\r\n * Recursively processes survey elements including nested panels for PDF export\r\n * @param {Array} elements - Array of survey elements to process\r\n * @returns {Array} Processed elements array\r\n */\r\nconst processElementsRecursively = (elements) => {\r\n  if (!elements || !Array.isArray(elements)) {\r\n    return [];\r\n  }\r\n\r\n  const processedElements = [];\r\n\r\n  elements.forEach((element, elementIndex) => {\r\n    if (!element || !element.type) {\r\n      return;\r\n    }\r\n\r\n    // Skip invisible elements\r\n    if (element.visible === false) {\r\n      return;\r\n    }\r\n\r\n    const cleanElement = {\r\n      type: element.type,\r\n      name: element.name || `element${elementIndex + 1}`,\r\n      title: element.title || element.name || `Element ${elementIndex + 1}`,\r\n    };\r\n\r\n    // For panels, only show title if it's meaningful (not just the auto-generated name)\r\n    if (element.type === \"panel\") {\r\n      // Hide panel title if it's just the auto-generated name (like \"panel5\", \"panel6\", etc.)\r\n      const isAutoGeneratedName = !element.title || element.title === element.name;\r\n      const isGenericPanelName = element.name && /^panel\\d*$/i.test(element.name);\r\n\r\n      if (isAutoGeneratedName && isGenericPanelName) {\r\n        cleanElement.title = \"\"; // Hide the title for auto-generated panel names\r\n      }\r\n    }\r\n\r\n    // Copy all relevant properties for different element types\r\n    if (element.isRequired) cleanElement.isRequired = element.isRequired;\r\n    if (element.choices) cleanElement.choices = element.choices;\r\n    if (element.rateMin !== undefined) cleanElement.rateMin = element.rateMin;\r\n    if (element.rateMax !== undefined) cleanElement.rateMax = element.rateMax;\r\n    if (element.rateStep !== undefined) cleanElement.rateStep = element.rateStep;\r\n    if (element.html) cleanElement.html = element.html;\r\n    if (element.description) cleanElement.description = element.description;\r\n    if (element.placeholder) cleanElement.placeholder = element.placeholder;\r\n    if (element.defaultValue !== undefined) cleanElement.defaultValue = element.defaultValue;\r\n    if (element.readOnly) cleanElement.readOnly = element.readOnly;\r\n    if (element.visible !== undefined) cleanElement.visible = element.visible;\r\n    if (element.width) cleanElement.width = element.width;\r\n    if (element.startWithNewLine !== undefined) cleanElement.startWithNewLine = element.startWithNewLine;\r\n    if (element.state) cleanElement.state = element.state;\r\n    if (element.autoGrow) cleanElement.autoGrow = element.autoGrow;\r\n\r\n    // Handle nested elements for panels recursively\r\n    if (element.type === \"panel\" && element.elements && element.elements.length > 0) {\r\n      cleanElement.elements = processElementsRecursively(element.elements);\r\n    }\r\n\r\n    processedElements.push(cleanElement);\r\n  });\r\n\r\n  return processedElements;\r\n};\r\n\r\n/**\r\n * Handles PDF export using Survey.js built-in PDF functionality\r\n * @param {Object} surveyModel - The Survey.js model instance\r\n * @param {Object} form - The form data object\r\n * @returns {Promise<void>}\r\n */\r\nconst handleExportToPDF = async (surveyModel, form) => {\r\n  if (!surveyModel) {\r\n    messageService.errorToast(\"Survey not loaded. Please try again.\");\r\n    return;\r\n  }\r\n\r\n  try {\r\n    // Show loading message\r\n    messageService.infoToast(\"Generating PDF... Please wait.\");\r\n\r\n    // Get the survey JSON and ensure it's properly formatted\r\n    const surveyJson = surveyModel.toJSON();\r\n\r\n    // Validate survey JSON structure\r\n    if (!surveyJson || !surveyJson.pages || surveyJson.pages.length === 0) {\r\n      throw new Error(\"Invalid survey structure for PDF export\");\r\n    }\r\n\r\n    console.log(\"Original survey JSON for PDF:\", surveyJson);\r\n\r\n    // Create a comprehensive survey JSON for PDF export with recursive element processing\r\n    const cleanSurveyJson = {\r\n      title: surveyJson.title || \"2025 Partner Plan\",\r\n      showTitle: true,\r\n      showPageTitles: true,\r\n      showQuestionNumbers: \"on\",\r\n      progressBarLocation: \"none\",\r\n      showProgressBar: \"none\",\r\n      questionsOnPageMode: \"singlePage\",\r\n      pages: [],\r\n    };\r\n\r\n    // Add partner details as the first page if form data is available\r\n    if (form) {\r\n      cleanSurveyJson.pages.push({\r\n        name: \"partnerDetailsPage\",\r\n        title: \"Partner Details\",\r\n        elements: [\r\n          {\r\n            type: \"html\",\r\n            name: \"partnerDetailsHtml\",\r\n            html: `\r\n              <table style=\"width: 100%; border-collapse: collapse; margin-bottom: 15px;\">\r\n                <tr style=\"background: #f8f9fa;\">\r\n                  <td style=\"padding: 8px; border: 1px solid #ddd; font-weight: bold; width: 25%;\">Partner Name:</td>\r\n                  <td style=\"padding: 8px; border: 1px solid #ddd; width: 25%;\">${form.partnerName || \"N/A\"}</td>\r\n                  <td style=\"padding: 8px; border: 1px solid #ddd; font-weight: bold; width: 25%;\">Service Line:</td>\r\n                  <td style=\"padding: 8px; border: 1px solid #ddd; width: 25%;\">${form.serviceLine || \"N/A\"}</td>\r\n                </tr>\r\n                <tr>\r\n                  <td style=\"padding: 8px; border: 1px solid #ddd; font-weight: bold;\">Sub-Service Line:</td>\r\n                  <td style=\"padding: 8px; border: 1px solid #ddd;\">${form.subServiceLine || \"N/A\"}</td>\r\n                  <td style=\"padding: 8px; border: 1px solid #ddd; font-weight: bold;\">Location:</td>\r\n                  <td style=\"padding: 8px; border: 1px solid #ddd;\">${form.location || \"N/A\"}</td>\r\n                </tr>\r\n                <tr style=\"background: #f8f9fa;\">\r\n                  <td style=\"padding: 8px; border: 1px solid #ddd; font-weight: bold;\">Primary Reviewer:</td>\r\n                  <td style=\"padding: 8px; border: 1px solid #ddd;\">${form.primaryReviewerName || \"N/A\"}</td>\r\n                  <td style=\"padding: 8px; border: 1px solid #ddd; font-weight: bold;\">Secondary Reviewer:</td>\r\n                  <td style=\"padding: 8px; border: 1px solid #ddd;\">${form.secondaryReviewerName || \"N/A\"}</td>\r\n                </tr>\r\n                <tr>\r\n                  <td style=\"padding: 8px; border: 1px solid #ddd; font-weight: bold;\">Status:</td>\r\n                  <td style=\"padding: 8px; border: 1px solid #ddd;\">${form.statusString || getFormStatusName(form.status) || \"DRAFT\"}</td>\r\n                  <td style=\"padding: 8px; border: 1px solid #ddd; font-weight: bold;\">Generated:</td>\r\n                  <td style=\"padding: 8px; border: 1px solid #ddd;\">${new Date().toLocaleDateString()}</td>\r\n                </tr>\r\n              </table>\r\n            `,\r\n          },\r\n        ],\r\n      });\r\n    }\r\n\r\n    // Add all survey pages with recursive element processing\r\n    const surveyPages = surveyJson.pages\r\n      .filter((page) => page && page.elements && page.elements.length > 0 && page.visible !== false)\r\n      .map((page, pageIndex) => ({\r\n        name: page.name || `page${pageIndex + 1}`,\r\n        title: page.title || page.name || `Page ${pageIndex + 1}`,\r\n        description: page.description || \"\",\r\n        elements: processElementsRecursively(page.elements),\r\n      }));\r\n\r\n    cleanSurveyJson.pages.push(...surveyPages);\r\n\r\n    // Create PDF export options with proper A4 configuration\r\n    const options = {\r\n      fontSize: 11,\r\n      fontName: \"Helvetica\",\r\n      margins: {\r\n        left: 10, // Smaller margins for better page utilization\r\n        right: 10,\r\n        top: 15,\r\n        bot: 15,\r\n      },\r\n      format: \"a4\",\r\n      orientation: \"p\", // 'p' for portrait, 'l' for landscape\r\n      // PDF generation options\r\n      compress: false,\r\n      htmlRenderAs: \"auto\", // Let SurveyJS decide how to render HTML\r\n      matrixRenderAs: \"auto\", // Render matrices as tables when possible\r\n      readonlyRenderAs: \"text\", // Render readonly elements as text\r\n      applyImageFit: true, // Apply image fit settings\r\n      useCustomFontInHtml: false,\r\n    };\r\n\r\n    // Configure Survey.js commercial license before creating SurveyPDF\r\n    configureSurveyJSLicense();\r\n\r\n    // Create SurveyPDF instance with cleaned JSON\r\n    const surveyPDF = new SurveyPDF(cleanSurveyJson, options);\r\n\r\n    // Set to read-only mode for better PDF generation (filled form)\r\n    surveyPDF.readOnly = true;\r\n\r\n    // Set the survey data for the PDF if available\r\n    const surveyData = surveyModel.data || {};\r\n    console.log(\"Survey data for PDF:\", surveyData);\r\n\r\n    if (Object.keys(surveyData).length > 0) {\r\n      // Clean the data to ensure all values are serializable and safe\r\n      const cleanData = {};\r\n      Object.keys(surveyData).forEach((key) => {\r\n        const value = surveyData[key];\r\n        if (value !== undefined && value !== null && key && typeof key === \"string\") {\r\n          if (typeof value === \"string\" || typeof value === \"number\" || typeof value === \"boolean\") {\r\n            cleanData[key] = value;\r\n          } else if (Array.isArray(value)) {\r\n            cleanData[key] = value.join(\", \");\r\n          } else if (typeof value === \"object\") {\r\n            cleanData[key] = JSON.stringify(value);\r\n          }\r\n        }\r\n      });\r\n      console.log(\"Cleaned data for PDF:\", cleanData);\r\n      surveyPDF.data = cleanData;\r\n    }\r\n\r\n    // Generate and download the PDF\r\n    const partnerName = form?.partnerName\r\n      ? form.partnerName\r\n          .replace(/[^a-zA-Z0-9\\s]/g, \"\")\r\n          .replace(/\\s+/g, \"_\")\r\n          .substring(0, 50)\r\n      : \"Survey\";\r\n    const fileName = `Partner_Plan_${partnerName}_${new Date().toISOString().split(\"T\")[0]}.pdf`;\r\n\r\n    // Try to generate the PDF\r\n    try {\r\n      await surveyPDF.save(fileName);\r\n      console.log(\"PDF save method completed successfully\");\r\n      messageService.successToast(\"PDF exported successfully!\");\r\n    } catch (saveError) {\r\n      console.error(\"Error in surveyPDF.save():\", saveError);\r\n\r\n      // Fallback: Try to generate PDF as blob and download manually\r\n      try {\r\n        console.log(\"Attempting fallback PDF generation...\");\r\n        const pdfBlob = await surveyPDF.raw();\r\n\r\n        if (pdfBlob) {\r\n          // Create download link manually\r\n          const url = window.URL.createObjectURL(pdfBlob);\r\n          const link = document.createElement(\"a\");\r\n          link.href = url;\r\n          link.download = fileName;\r\n          document.body.appendChild(link);\r\n          link.click();\r\n          document.body.removeChild(link);\r\n          window.URL.revokeObjectURL(url);\r\n\r\n          console.log(\"Fallback PDF download completed\");\r\n          messageService.successToast(\"PDF exported successfully using fallback method!\");\r\n        } else {\r\n          throw new Error(\"Failed to generate PDF blob\");\r\n        }\r\n      } catch (fallbackError) {\r\n        console.error(\"Fallback PDF generation also failed:\", fallbackError);\r\n        throw saveError; // Re-throw original error\r\n      }\r\n    }\r\n  } catch (error) {\r\n    console.error(\"Error exporting to PDF:\", error);\r\n\r\n    // Provide more specific error messages\r\n    let errorMessage = \"Failed to export PDF. \";\r\n    if (error.message && error.message.includes(\"indexOf\")) {\r\n      errorMessage += \"There may be an issue with the survey data format.\";\r\n    } else if (error.message) {\r\n      errorMessage += error.message;\r\n    } else {\r\n      errorMessage += \"Please try again or contact support if the issue persists.\";\r\n    }\r\n\r\n    messageService.errorToast(errorMessage);\r\n  }\r\n};\r\n\r\n/**\r\n * Generates HTML content for print/PDF export\r\n * @param {Object} form - The form data object\r\n * @param {Object} surveyJson - The survey JSON structure\r\n * @param {Object} surveyData - The survey response data\r\n * @returns {string} Complete HTML content for printing\r\n */\r\nconst generatePrintHTMLContent = (form, surveyJson, surveyData) => {\r\n  // Create HTML content for printing\r\n  let htmlContent = `\r\n    <!DOCTYPE html>\r\n    <html>\r\n    <head>\r\n      <title>Partner Plan - ${form?.partnerName || \"Survey\"}</title>\r\n      <style>\r\n        /* A4 Portrait dimensions: 210mm x 297mm (8.27\" x 11.69\") */\r\n        @page {\r\n          size: A4 portrait;\r\n          margin: 20mm 15mm 20mm 15mm;\r\n        }\r\n\r\n        body {\r\n          font-family: Arial, sans-serif;\r\n          margin: 0;\r\n          padding: 10px;\r\n          line-height: 1.4;\r\n          font-size: 10px;\r\n          width: 100%;\r\n          max-width: 210mm; /* A4 width minus margins */\r\n          box-sizing: border-box;\r\n          margin: 0 auto; /* Center content */\r\n        }\r\n\r\n        .header {\r\n          color: #ED1A3B;\r\n          border-bottom: 2px solid #ED1A3B;\r\n          padding-bottom: 8px;\r\n          margin-bottom: 15px;\r\n          text-align: center;\r\n          clear: both;\r\n          width: 100%;\r\n        }\r\n\r\n        .header h1 {\r\n          margin: 0;\r\n          font-size: 16px;\r\n          font-weight: bold;\r\n        }\r\n\r\n        .partner-info {\r\n          background: #f8f9fa;\r\n          padding: 8px;\r\n          margin-bottom: 15px;\r\n          border: 1px solid #ddd;\r\n          border-radius: 3px;\r\n          font-size: 9px;\r\n          clear: both;\r\n          width: 100%;\r\n          display: block;\r\n        }\r\n\r\n        /* Main content area */\r\n        .content-area {\r\n          width: 100%;\r\n          clear: both;\r\n          display: block;\r\n        }\r\n\r\n        .question {\r\n          margin-bottom: 12px;\r\n          page-break-inside: avoid;\r\n          width: 100%;\r\n          clear: both;\r\n          display: block;\r\n        }\r\n\r\n        .question-title {\r\n          font-weight: bold;\r\n          color: #333;\r\n          margin-bottom: 5px;\r\n          font-size: 10px;\r\n          display: block;\r\n          width: 100%;\r\n        }\r\n\r\n        .answer {\r\n          background: #fff;\r\n          padding: 6px;\r\n          border: 1px solid #ddd;\r\n          border-radius: 2px;\r\n          min-height: 18px;\r\n          font-size: 9px;\r\n          word-wrap: break-word;\r\n          overflow-wrap: break-word;\r\n          display: block;\r\n          width: 100%;\r\n          box-sizing: border-box;\r\n        }\r\n\r\n        .page-title {\r\n          color: #ED1A3B;\r\n          font-size: 14px;\r\n          font-weight: bold;\r\n          margin: 15px 0 10px 0;\r\n          border-bottom: 1px solid #ED1A3B;\r\n          page-break-before: auto;\r\n          clear: both;\r\n          width: 100%;\r\n          display: block;\r\n        }\r\n\r\n        .panel-title-main {\r\n          color: #ED1A3B;\r\n          font-size: 12px;\r\n          font-weight: bold;\r\n          margin: 15px 0 10px 0;\r\n          border-bottom: 1px solid #ED1A3B;\r\n          clear: both;\r\n          width: 100%;\r\n          display: block;\r\n        }\r\n\r\n        .panel-title-sub {\r\n          color: #666;\r\n          font-size: 11px;\r\n          font-weight: bold;\r\n          margin: 12px 0 8px 0;\r\n          clear: both;\r\n          width: 100%;\r\n          display: block;\r\n        }\r\n\r\n        .panel-description {\r\n          margin-bottom: 10px;\r\n          font-style: italic;\r\n          color: #666;\r\n          font-size: 9px;\r\n          clear: both;\r\n          width: 100%;\r\n          display: block;\r\n        }\r\n\r\n        .html-content {\r\n          margin: 10px 0;\r\n          font-size: 9px;\r\n          width: 100%;\r\n          clear: both;\r\n          display: block;\r\n        }\r\n\r\n        /* Auto-fitting table layout */\r\n        .html-content table {\r\n          border-collapse: collapse;\r\n          width: 100%;\r\n          margin: 8px 0;\r\n          font-size: 8px;\r\n          table-layout: auto;\r\n          word-wrap: break-word;\r\n        }\r\n\r\n        .html-content th, .html-content td {\r\n          border: 1px solid #ddd;\r\n          padding: 3px;\r\n          text-align: left;\r\n          word-wrap: break-word;\r\n          overflow-wrap: break-word;\r\n          max-width: 0;\r\n          overflow: hidden;\r\n        }\r\n\r\n        .html-content th {\r\n          background-color: #f2f2f2;\r\n          font-weight: bold;\r\n        }\r\n\r\n        /* Additional table styles for better auto-fitting */\r\n        table {\r\n          width: 100%;\r\n          border-collapse: collapse;\r\n          margin: 12px 0;\r\n          table-layout: auto;\r\n          font-size: 8px;\r\n          clear: both;\r\n          display: table;\r\n        }\r\n\r\n        th, td {\r\n          border: 1px solid #ddd;\r\n          padding: 4px;\r\n          text-align: left;\r\n          word-wrap: break-word;\r\n          overflow-wrap: break-word;\r\n          vertical-align: top;\r\n        }\r\n\r\n        th {\r\n          background-color: #f2f2f2;\r\n          font-weight: bold;\r\n        }\r\n      </style>\r\n    </head>\r\n    <body>\r\n      <div class=\"header\">\r\n        <h1>BDO Partner Planning Tool - 2025</h1>\r\n      </div>\r\n      <div class=\"partner-info\">\r\n        <table>\r\n          <tr>\r\n            <th>Partner Name:</th>\r\n            <td>${form?.partnerName || \"N/A\"}</td>\r\n            <th>Service Line:</th>\r\n            <td>${form?.serviceLine || \"N/A\"}</td>\r\n          </tr>\r\n          <tr>\r\n            <th>Sub-Service Line:</th>\r\n            <td>${form?.subServiceLine || \"N/A\"}</td>\r\n            <th>Location:</th>\r\n            <td>${form?.location || \"N/A\"}</td>\r\n          </tr>\r\n          <tr>\r\n            <th>Primary Reviewer:</th>\r\n            <td>${form?.primaryReviewerName || \"N/A\"}</td>\r\n            <th>Secondary Reviewer:</th>\r\n            <td>${form?.secondaryReviewerName || \"N/A\"}</td>\r\n          </tr>\r\n          <tr>\r\n            <th>Status:</th>\r\n            <td>${form?.statusString || getFormStatusName(form?.status) || \"DRAFT\"}</td>\r\n            <th>Generated:</th>\r\n            <td>${new Date().toLocaleDateString()}</td>\r\n          </tr>\r\n        </table>\r\n      </div>\r\n      <div class=\"content-area survey-content\">\r\n  `;\r\n\r\n  // Recursive function to render elements including nested panels\r\n  const renderElementsRecursively = (elements, level = 0) => {\r\n    if (!elements || !Array.isArray(elements)) {\r\n      return \"\";\r\n    }\r\n\r\n    let content = \"\";\r\n    elements.forEach((element) => {\r\n      if (!element || element.visible === false) {\r\n        return;\r\n      }\r\n\r\n      if (element.type === \"panel\") {\r\n        // Only render panel title if it's meaningful (not just auto-generated name)\r\n        const isAutoGeneratedName = !element.title || element.title === element.name;\r\n        const isGenericPanelName = element.name && /^panel\\d*$/i.test(element.name);\r\n        const shouldShowTitle = !(isAutoGeneratedName && isGenericPanelName);\r\n\r\n        if (shouldShowTitle && element.title) {\r\n          const panelTitleClass = level === 0 ? \"panel-title-main\" : \"panel-title-sub\";\r\n          content += `<div class=\"${panelTitleClass}\" style=\"margin-top: ${\r\n            level === 0 ? \"20px\" : \"15px\"\r\n          }; font-weight: bold; color: #ED1A3B; font-size: ${level === 0 ? \"16px\" : \"14px\"};\">${element.title}</div>`;\r\n        }\r\n\r\n        // Render panel description if exists\r\n        if (element.description) {\r\n          content += `<div class=\"panel-description\" style=\"margin-bottom: 10px; font-style: italic;\">${element.description}</div>`;\r\n        }\r\n\r\n        // Recursively render nested elements\r\n        if (element.elements) {\r\n          content += renderElementsRecursively(element.elements, level + 1);\r\n        }\r\n      } else if (element.type === \"html\") {\r\n        // Render HTML elements\r\n        content += `<div class=\"html-content\" style=\"margin: 10px 0;\">${element.html || \"\"}</div>`;\r\n      } else {\r\n        // Render regular questions\r\n        const answer = surveyData[element.name] || \"No answer provided\";\r\n        const questionStyle = level > 0 ? \"margin-left: 20px;\" : \"\";\r\n        content += `\r\n          <div class=\"question\" style=\"${questionStyle}\">\r\n            <div class=\"question-title\">${element.title || element.name}</div>\r\n            <div class=\"answer\">${Array.isArray(answer) ? answer.join(\", \") : answer}</div>\r\n          </div>\r\n        `;\r\n      }\r\n    });\r\n    return content;\r\n  };\r\n\r\n  // Add survey content\r\n  if (surveyJson.pages) {\r\n    surveyJson.pages.forEach((page) => {\r\n      if (page.elements && page.elements.length > 0 && page.visible !== false) {\r\n        htmlContent += `<div class=\"page-container\">`;\r\n        htmlContent += `<div class=\"page-title\">${page.title || page.name || \"Survey Page\"}</div>`;\r\n        htmlContent += renderElementsRecursively(page.elements);\r\n        htmlContent += `</div>`;\r\n      }\r\n    });\r\n  }\r\n\r\n  htmlContent += `\r\n      </div>\r\n    </body>\r\n    </html>\r\n  `;\r\n\r\n  return htmlContent;\r\n};\r\n\r\n/**\r\n * Alternative PDF export method using browser's print functionality\r\n * @param {Object} surveyModel - The Survey.js model instance\r\n * @param {Object} form - The form data object\r\n * @returns {void}\r\n */\r\nconst handleAlternativePDFExport = (surveyModel, form) => {\r\n  if (!surveyModel) {\r\n    messageService.errorToast(\"Survey not loaded. Please try again.\");\r\n    return;\r\n  }\r\n\r\n  try {\r\n    // Create a new window with the survey content\r\n    const printWindow = window.open(\"\", \"_blank\");\r\n    if (!printWindow) {\r\n      messageService.errorToast(\"Please allow pop-ups for PDF export to work.\");\r\n      return;\r\n    }\r\n\r\n    // Get survey data\r\n    const surveyData = surveyModel.data || {};\r\n    const surveyJson = surveyModel.toJSON();\r\n\r\n    // Create HTML content for printing - this will be continued in the next chunk\r\n    let htmlContent = generatePrintHTMLContent(form, surveyJson, surveyData);\r\n\r\n    // Write content to new window and trigger print\r\n    printWindow.document.write(htmlContent);\r\n    printWindow.document.close();\r\n\r\n    // Wait for content to load, then print\r\n    printWindow.onload = () => {\r\n      setTimeout(() => {\r\n        printWindow.print();\r\n        printWindow.close();\r\n      }, 1000);\r\n    };\r\n\r\n    //messageService.infoToast(\"Print dialog opened. Choose 'Save as PDF' in your browser's print options.\");\r\n  } catch (error) {\r\n    console.error(\"Error with alternative PDF export:\", error);\r\n    messageService.errorToast(\"Failed to open print dialog. Please try the main PDF export.\");\r\n  }\r\n};\r\n\r\nconst PDFExportUtilities = {\r\n  handleExportToPDF,\r\n  handleAlternativePDFExport,\r\n  processElementsRecursively,\r\n  generatePrintHTMLContent,\r\n};\r\n\r\nexport default PDFExportUtilities;\r\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,YAAY;AACtC,SAASC,wBAAwB,QAAQ,mCAAmC;AAC5E,SAASC,cAAc,QAAQ,mCAAmC;AAClE,SAASC,iBAAiB,QAAQ,mCAAmC;;AAErE;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMC,0BAA0B,GAAIC,QAAQ,IAAK;EAC/C,IAAI,CAACA,QAAQ,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,QAAQ,CAAC,EAAE;IACzC,OAAO,EAAE;EACX;EAEA,MAAMG,iBAAiB,GAAG,EAAE;EAE5BH,QAAQ,CAACI,OAAO,CAAC,CAACC,OAAO,EAAEC,YAAY,KAAK;IAC1C,IAAI,CAACD,OAAO,IAAI,CAACA,OAAO,CAACE,IAAI,EAAE;MAC7B;IACF;;IAEA;IACA,IAAIF,OAAO,CAACG,OAAO,KAAK,KAAK,EAAE;MAC7B;IACF;IAEA,MAAMC,YAAY,GAAG;MACnBF,IAAI,EAAEF,OAAO,CAACE,IAAI;MAClBG,IAAI,EAAEL,OAAO,CAACK,IAAI,IAAI,UAAUJ,YAAY,GAAG,CAAC,EAAE;MAClDK,KAAK,EAAEN,OAAO,CAACM,KAAK,IAAIN,OAAO,CAACK,IAAI,IAAI,WAAWJ,YAAY,GAAG,CAAC;IACrE,CAAC;;IAED;IACA,IAAID,OAAO,CAACE,IAAI,KAAK,OAAO,EAAE;MAC5B;MACA,MAAMK,mBAAmB,GAAG,CAACP,OAAO,CAACM,KAAK,IAAIN,OAAO,CAACM,KAAK,KAAKN,OAAO,CAACK,IAAI;MAC5E,MAAMG,kBAAkB,GAAGR,OAAO,CAACK,IAAI,IAAI,aAAa,CAACI,IAAI,CAACT,OAAO,CAACK,IAAI,CAAC;MAE3E,IAAIE,mBAAmB,IAAIC,kBAAkB,EAAE;QAC7CJ,YAAY,CAACE,KAAK,GAAG,EAAE,CAAC,CAAC;MAC3B;IACF;;IAEA;IACA,IAAIN,OAAO,CAACU,UAAU,EAAEN,YAAY,CAACM,UAAU,GAAGV,OAAO,CAACU,UAAU;IACpE,IAAIV,OAAO,CAACW,OAAO,EAAEP,YAAY,CAACO,OAAO,GAAGX,OAAO,CAACW,OAAO;IAC3D,IAAIX,OAAO,CAACY,OAAO,KAAKC,SAAS,EAAET,YAAY,CAACQ,OAAO,GAAGZ,OAAO,CAACY,OAAO;IACzE,IAAIZ,OAAO,CAACc,OAAO,KAAKD,SAAS,EAAET,YAAY,CAACU,OAAO,GAAGd,OAAO,CAACc,OAAO;IACzE,IAAId,OAAO,CAACe,QAAQ,KAAKF,SAAS,EAAET,YAAY,CAACW,QAAQ,GAAGf,OAAO,CAACe,QAAQ;IAC5E,IAAIf,OAAO,CAACgB,IAAI,EAAEZ,YAAY,CAACY,IAAI,GAAGhB,OAAO,CAACgB,IAAI;IAClD,IAAIhB,OAAO,CAACiB,WAAW,EAAEb,YAAY,CAACa,WAAW,GAAGjB,OAAO,CAACiB,WAAW;IACvE,IAAIjB,OAAO,CAACkB,WAAW,EAAEd,YAAY,CAACc,WAAW,GAAGlB,OAAO,CAACkB,WAAW;IACvE,IAAIlB,OAAO,CAACmB,YAAY,KAAKN,SAAS,EAAET,YAAY,CAACe,YAAY,GAAGnB,OAAO,CAACmB,YAAY;IACxF,IAAInB,OAAO,CAACoB,QAAQ,EAAEhB,YAAY,CAACgB,QAAQ,GAAGpB,OAAO,CAACoB,QAAQ;IAC9D,IAAIpB,OAAO,CAACG,OAAO,KAAKU,SAAS,EAAET,YAAY,CAACD,OAAO,GAAGH,OAAO,CAACG,OAAO;IACzE,IAAIH,OAAO,CAACqB,KAAK,EAAEjB,YAAY,CAACiB,KAAK,GAAGrB,OAAO,CAACqB,KAAK;IACrD,IAAIrB,OAAO,CAACsB,gBAAgB,KAAKT,SAAS,EAAET,YAAY,CAACkB,gBAAgB,GAAGtB,OAAO,CAACsB,gBAAgB;IACpG,IAAItB,OAAO,CAACuB,KAAK,EAAEnB,YAAY,CAACmB,KAAK,GAAGvB,OAAO,CAACuB,KAAK;IACrD,IAAIvB,OAAO,CAACwB,QAAQ,EAAEpB,YAAY,CAACoB,QAAQ,GAAGxB,OAAO,CAACwB,QAAQ;;IAE9D;IACA,IAAIxB,OAAO,CAACE,IAAI,KAAK,OAAO,IAAIF,OAAO,CAACL,QAAQ,IAAIK,OAAO,CAACL,QAAQ,CAAC8B,MAAM,GAAG,CAAC,EAAE;MAC/ErB,YAAY,CAACT,QAAQ,GAAGD,0BAA0B,CAACM,OAAO,CAACL,QAAQ,CAAC;IACtE;IAEAG,iBAAiB,CAAC4B,IAAI,CAACtB,YAAY,CAAC;EACtC,CAAC,CAAC;EAEF,OAAON,iBAAiB;AAC1B,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,MAAM6B,iBAAiB,GAAG,MAAAA,CAAOC,WAAW,EAAEC,IAAI,KAAK;EACrD,IAAI,CAACD,WAAW,EAAE;IAChBpC,cAAc,CAACsC,UAAU,CAAC,sCAAsC,CAAC;IACjE;EACF;EAEA,IAAI;IACF;IACAtC,cAAc,CAACuC,SAAS,CAAC,gCAAgC,CAAC;;IAE1D;IACA,MAAMC,UAAU,GAAGJ,WAAW,CAACK,MAAM,CAAC,CAAC;;IAEvC;IACA,IAAI,CAACD,UAAU,IAAI,CAACA,UAAU,CAACE,KAAK,IAAIF,UAAU,CAACE,KAAK,CAACT,MAAM,KAAK,CAAC,EAAE;MACrE,MAAM,IAAIU,KAAK,CAAC,yCAAyC,CAAC;IAC5D;IAEAC,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEL,UAAU,CAAC;;IAExD;IACA,MAAMM,eAAe,GAAG;MACtBhC,KAAK,EAAE0B,UAAU,CAAC1B,KAAK,IAAI,mBAAmB;MAC9CiC,SAAS,EAAE,IAAI;MACfC,cAAc,EAAE,IAAI;MACpBC,mBAAmB,EAAE,IAAI;MACzBC,mBAAmB,EAAE,MAAM;MAC3BC,eAAe,EAAE,MAAM;MACvBC,mBAAmB,EAAE,YAAY;MACjCV,KAAK,EAAE;IACT,CAAC;;IAED;IACA,IAAIL,IAAI,EAAE;MACRS,eAAe,CAACJ,KAAK,CAACR,IAAI,CAAC;QACzBrB,IAAI,EAAE,oBAAoB;QAC1BC,KAAK,EAAE,iBAAiB;QACxBX,QAAQ,EAAE,CACR;UACEO,IAAI,EAAE,MAAM;UACZG,IAAI,EAAE,oBAAoB;UAC1BW,IAAI,EAAE;AAClB;AACA;AACA;AACA,kFAAkFa,IAAI,CAACgB,WAAW,IAAI,KAAK;AAC3G;AACA,kFAAkFhB,IAAI,CAACiB,WAAW,IAAI,KAAK;AAC3G;AACA;AACA;AACA,sEAAsEjB,IAAI,CAACkB,cAAc,IAAI,KAAK;AAClG;AACA,sEAAsElB,IAAI,CAACmB,QAAQ,IAAI,KAAK;AAC5F;AACA;AACA;AACA,sEAAsEnB,IAAI,CAACoB,mBAAmB,IAAI,KAAK;AACvG;AACA,sEAAsEpB,IAAI,CAACqB,qBAAqB,IAAI,KAAK;AACzG;AACA;AACA;AACA,sEAAsErB,IAAI,CAACsB,YAAY,IAAI1D,iBAAiB,CAACoC,IAAI,CAACuB,MAAM,CAAC,IAAI,OAAO;AACpI;AACA,sEAAsE,IAAIC,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC;AACrG;AACA;AACA;QACU,CAAC;MAEL,CAAC,CAAC;IACJ;;IAEA;IACA,MAAMC,WAAW,GAAGvB,UAAU,CAACE,KAAK,CACjCsB,MAAM,CAAEC,IAAI,IAAKA,IAAI,IAAIA,IAAI,CAAC9D,QAAQ,IAAI8D,IAAI,CAAC9D,QAAQ,CAAC8B,MAAM,GAAG,CAAC,IAAIgC,IAAI,CAACtD,OAAO,KAAK,KAAK,CAAC,CAC7FuD,GAAG,CAAC,CAACD,IAAI,EAAEE,SAAS,MAAM;MACzBtD,IAAI,EAAEoD,IAAI,CAACpD,IAAI,IAAI,OAAOsD,SAAS,GAAG,CAAC,EAAE;MACzCrD,KAAK,EAAEmD,IAAI,CAACnD,KAAK,IAAImD,IAAI,CAACpD,IAAI,IAAI,QAAQsD,SAAS,GAAG,CAAC,EAAE;MACzD1C,WAAW,EAAEwC,IAAI,CAACxC,WAAW,IAAI,EAAE;MACnCtB,QAAQ,EAAED,0BAA0B,CAAC+D,IAAI,CAAC9D,QAAQ;IACpD,CAAC,CAAC,CAAC;IAEL2C,eAAe,CAACJ,KAAK,CAACR,IAAI,CAAC,GAAG6B,WAAW,CAAC;;IAE1C;IACA,MAAMK,OAAO,GAAG;MACdC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,WAAW;MACrBC,OAAO,EAAE;QACPC,IAAI,EAAE,EAAE;QAAE;QACVC,KAAK,EAAE,EAAE;QACTC,GAAG,EAAE,EAAE;QACPC,GAAG,EAAE;MACP,CAAC;MACDC,MAAM,EAAE,IAAI;MACZC,WAAW,EAAE,GAAG;MAAE;MAClB;MACAC,QAAQ,EAAE,KAAK;MACfC,YAAY,EAAE,MAAM;MAAE;MACtBC,cAAc,EAAE,MAAM;MAAE;MACxBC,gBAAgB,EAAE,MAAM;MAAE;MAC1BC,aAAa,EAAE,IAAI;MAAE;MACrBC,mBAAmB,EAAE;IACvB,CAAC;;IAED;IACApF,wBAAwB,CAAC,CAAC;;IAE1B;IACA,MAAMqF,SAAS,GAAG,IAAItF,SAAS,CAACgD,eAAe,EAAEsB,OAAO,CAAC;;IAEzD;IACAgB,SAAS,CAACxD,QAAQ,GAAG,IAAI;;IAEzB;IACA,MAAMyD,UAAU,GAAGjD,WAAW,CAACkD,IAAI,IAAI,CAAC,CAAC;IACzC1C,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEwC,UAAU,CAAC;IAE/C,IAAIE,MAAM,CAACC,IAAI,CAACH,UAAU,CAAC,CAACpD,MAAM,GAAG,CAAC,EAAE;MACtC;MACA,MAAMwD,SAAS,GAAG,CAAC,CAAC;MACpBF,MAAM,CAACC,IAAI,CAACH,UAAU,CAAC,CAAC9E,OAAO,CAAEmF,GAAG,IAAK;QACvC,MAAMC,KAAK,GAAGN,UAAU,CAACK,GAAG,CAAC;QAC7B,IAAIC,KAAK,KAAKtE,SAAS,IAAIsE,KAAK,KAAK,IAAI,IAAID,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;UAC3E,IAAI,OAAOC,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,SAAS,EAAE;YACxFF,SAAS,CAACC,GAAG,CAAC,GAAGC,KAAK;UACxB,CAAC,MAAM,IAAIvF,KAAK,CAACC,OAAO,CAACsF,KAAK,CAAC,EAAE;YAC/BF,SAAS,CAACC,GAAG,CAAC,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC;UACnC,CAAC,MAAM,IAAI,OAAOD,KAAK,KAAK,QAAQ,EAAE;YACpCF,SAAS,CAACC,GAAG,CAAC,GAAGG,IAAI,CAACC,SAAS,CAACH,KAAK,CAAC;UACxC;QACF;MACF,CAAC,CAAC;MACF/C,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE4C,SAAS,CAAC;MAC/CL,SAAS,CAACE,IAAI,GAAGG,SAAS;IAC5B;;IAEA;IACA,MAAMpC,WAAW,GAAGhB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEgB,WAAW,GACjChB,IAAI,CAACgB,WAAW,CACb0C,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAC9BA,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CACpBC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GACnB,QAAQ;IACZ,MAAMC,QAAQ,GAAG,gBAAgB5C,WAAW,IAAI,IAAIQ,IAAI,CAAC,CAAC,CAACqC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM;;IAE5F;IACA,IAAI;MACF,MAAMf,SAAS,CAACgB,IAAI,CAACH,QAAQ,CAAC;MAC9BrD,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MACrD7C,cAAc,CAACqG,YAAY,CAAC,4BAA4B,CAAC;IAC3D,CAAC,CAAC,OAAOC,SAAS,EAAE;MAClB1D,OAAO,CAAC2D,KAAK,CAAC,4BAA4B,EAAED,SAAS,CAAC;;MAEtD;MACA,IAAI;QACF1D,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;QACpD,MAAM2D,OAAO,GAAG,MAAMpB,SAAS,CAACqB,GAAG,CAAC,CAAC;QAErC,IAAID,OAAO,EAAE;UACX;UACA,MAAME,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACL,OAAO,CAAC;UAC/C,MAAMM,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;UACxCF,IAAI,CAACG,IAAI,GAAGP,GAAG;UACfI,IAAI,CAACI,QAAQ,GAAGjB,QAAQ;UACxBc,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;UAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;UACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;UAC/BH,MAAM,CAACC,GAAG,CAACW,eAAe,CAACb,GAAG,CAAC;UAE/B9D,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;UAC9C7C,cAAc,CAACqG,YAAY,CAAC,kDAAkD,CAAC;QACjF,CAAC,MAAM;UACL,MAAM,IAAI1D,KAAK,CAAC,6BAA6B,CAAC;QAChD;MACF,CAAC,CAAC,OAAO6E,aAAa,EAAE;QACtB5E,OAAO,CAAC2D,KAAK,CAAC,sCAAsC,EAAEiB,aAAa,CAAC;QACpE,MAAMlB,SAAS,CAAC,CAAC;MACnB;IACF;EACF,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd3D,OAAO,CAAC2D,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;;IAE/C;IACA,IAAIkB,YAAY,GAAG,wBAAwB;IAC3C,IAAIlB,KAAK,CAACmB,OAAO,IAAInB,KAAK,CAACmB,OAAO,CAACC,QAAQ,CAAC,SAAS,CAAC,EAAE;MACtDF,YAAY,IAAI,oDAAoD;IACtE,CAAC,MAAM,IAAIlB,KAAK,CAACmB,OAAO,EAAE;MACxBD,YAAY,IAAIlB,KAAK,CAACmB,OAAO;IAC/B,CAAC,MAAM;MACLD,YAAY,IAAI,4DAA4D;IAC9E;IAEAzH,cAAc,CAACsC,UAAU,CAACmF,YAAY,CAAC;EACzC;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,wBAAwB,GAAGA,CAACvF,IAAI,EAAEG,UAAU,EAAE6C,UAAU,KAAK;EACjE;EACA,IAAIwC,WAAW,GAAG;AACpB;AACA;AACA;AACA,8BAA8B,CAAAxF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgB,WAAW,KAAI,QAAQ;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,CAAAhB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgB,WAAW,KAAI,KAAK;AAC5C;AACA,kBAAkB,CAAAhB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiB,WAAW,KAAI,KAAK;AAC5C;AACA;AACA;AACA,kBAAkB,CAAAjB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,cAAc,KAAI,KAAK;AAC/C;AACA,kBAAkB,CAAAlB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmB,QAAQ,KAAI,KAAK;AACzC;AACA;AACA;AACA,kBAAkB,CAAAnB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoB,mBAAmB,KAAI,KAAK;AACpD;AACA,kBAAkB,CAAApB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqB,qBAAqB,KAAI,KAAK;AACtD;AACA;AACA;AACA,kBAAkB,CAAArB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsB,YAAY,KAAI1D,iBAAiB,CAACoC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuB,MAAM,CAAC,IAAI,OAAO;AAClF;AACA,kBAAkB,IAAIC,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC;AACjD;AACA;AACA;AACA;AACA,GAAG;;EAED;EACA,MAAMgE,yBAAyB,GAAGA,CAAC3H,QAAQ,EAAE4H,KAAK,GAAG,CAAC,KAAK;IACzD,IAAI,CAAC5H,QAAQ,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,QAAQ,CAAC,EAAE;MACzC,OAAO,EAAE;IACX;IAEA,IAAI6H,OAAO,GAAG,EAAE;IAChB7H,QAAQ,CAACI,OAAO,CAAEC,OAAO,IAAK;MAC5B,IAAI,CAACA,OAAO,IAAIA,OAAO,CAACG,OAAO,KAAK,KAAK,EAAE;QACzC;MACF;MAEA,IAAIH,OAAO,CAACE,IAAI,KAAK,OAAO,EAAE;QAC5B;QACA,MAAMK,mBAAmB,GAAG,CAACP,OAAO,CAACM,KAAK,IAAIN,OAAO,CAACM,KAAK,KAAKN,OAAO,CAACK,IAAI;QAC5E,MAAMG,kBAAkB,GAAGR,OAAO,CAACK,IAAI,IAAI,aAAa,CAACI,IAAI,CAACT,OAAO,CAACK,IAAI,CAAC;QAC3E,MAAMoH,eAAe,GAAG,EAAElH,mBAAmB,IAAIC,kBAAkB,CAAC;QAEpE,IAAIiH,eAAe,IAAIzH,OAAO,CAACM,KAAK,EAAE;UACpC,MAAMoH,eAAe,GAAGH,KAAK,KAAK,CAAC,GAAG,kBAAkB,GAAG,iBAAiB;UAC5EC,OAAO,IAAI,eAAeE,eAAe,wBACvCH,KAAK,KAAK,CAAC,GAAG,MAAM,GAAG,MAAM,mDACoBA,KAAK,KAAK,CAAC,GAAG,MAAM,GAAG,MAAM,MAAMvH,OAAO,CAACM,KAAK,QAAQ;QAC7G;;QAEA;QACA,IAAIN,OAAO,CAACiB,WAAW,EAAE;UACvBuG,OAAO,IAAI,mFAAmFxH,OAAO,CAACiB,WAAW,QAAQ;QAC3H;;QAEA;QACA,IAAIjB,OAAO,CAACL,QAAQ,EAAE;UACpB6H,OAAO,IAAIF,yBAAyB,CAACtH,OAAO,CAACL,QAAQ,EAAE4H,KAAK,GAAG,CAAC,CAAC;QACnE;MACF,CAAC,MAAM,IAAIvH,OAAO,CAACE,IAAI,KAAK,MAAM,EAAE;QAClC;QACAsH,OAAO,IAAI,qDAAqDxH,OAAO,CAACgB,IAAI,IAAI,EAAE,QAAQ;MAC5F,CAAC,MAAM;QACL;QACA,MAAM2G,MAAM,GAAG9C,UAAU,CAAC7E,OAAO,CAACK,IAAI,CAAC,IAAI,oBAAoB;QAC/D,MAAMuH,aAAa,GAAGL,KAAK,GAAG,CAAC,GAAG,oBAAoB,GAAG,EAAE;QAC3DC,OAAO,IAAI;AACnB,yCAAyCI,aAAa;AACtD,0CAA0C5H,OAAO,CAACM,KAAK,IAAIN,OAAO,CAACK,IAAI;AACvE,kCAAkCT,KAAK,CAACC,OAAO,CAAC8H,MAAM,CAAC,GAAGA,MAAM,CAACvC,IAAI,CAAC,IAAI,CAAC,GAAGuC,MAAM;AACpF;AACA,SAAS;MACH;IACF,CAAC,CAAC;IACF,OAAOH,OAAO;EAChB,CAAC;;EAED;EACA,IAAIxF,UAAU,CAACE,KAAK,EAAE;IACpBF,UAAU,CAACE,KAAK,CAACnC,OAAO,CAAE0D,IAAI,IAAK;MACjC,IAAIA,IAAI,CAAC9D,QAAQ,IAAI8D,IAAI,CAAC9D,QAAQ,CAAC8B,MAAM,GAAG,CAAC,IAAIgC,IAAI,CAACtD,OAAO,KAAK,KAAK,EAAE;QACvEkH,WAAW,IAAI,8BAA8B;QAC7CA,WAAW,IAAI,2BAA2B5D,IAAI,CAACnD,KAAK,IAAImD,IAAI,CAACpD,IAAI,IAAI,aAAa,QAAQ;QAC1FgH,WAAW,IAAIC,yBAAyB,CAAC7D,IAAI,CAAC9D,QAAQ,CAAC;QACvD0H,WAAW,IAAI,QAAQ;MACzB;IACF,CAAC,CAAC;EACJ;EAEAA,WAAW,IAAI;AACjB;AACA;AACA;AACA,GAAG;EAED,OAAOA,WAAW;AACpB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,MAAMQ,0BAA0B,GAAGA,CAACjG,WAAW,EAAEC,IAAI,KAAK;EACxD,IAAI,CAACD,WAAW,EAAE;IAChBpC,cAAc,CAACsC,UAAU,CAAC,sCAAsC,CAAC;IACjE;EACF;EAEA,IAAI;IACF;IACA,MAAMgG,WAAW,GAAG3B,MAAM,CAAC4B,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC;IAC7C,IAAI,CAACD,WAAW,EAAE;MAChBtI,cAAc,CAACsC,UAAU,CAAC,8CAA8C,CAAC;MACzE;IACF;;IAEA;IACA,MAAM+C,UAAU,GAAGjD,WAAW,CAACkD,IAAI,IAAI,CAAC,CAAC;IACzC,MAAM9C,UAAU,GAAGJ,WAAW,CAACK,MAAM,CAAC,CAAC;;IAEvC;IACA,IAAIoF,WAAW,GAAGD,wBAAwB,CAACvF,IAAI,EAAEG,UAAU,EAAE6C,UAAU,CAAC;;IAExE;IACAiD,WAAW,CAACvB,QAAQ,CAACyB,KAAK,CAACX,WAAW,CAAC;IACvCS,WAAW,CAACvB,QAAQ,CAAC0B,KAAK,CAAC,CAAC;;IAE5B;IACAH,WAAW,CAACI,MAAM,GAAG,MAAM;MACzBC,UAAU,CAAC,MAAM;QACfL,WAAW,CAACM,KAAK,CAAC,CAAC;QACnBN,WAAW,CAACG,KAAK,CAAC,CAAC;MACrB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC;;IAED;EACF,CAAC,CAAC,OAAOlC,KAAK,EAAE;IACd3D,OAAO,CAAC2D,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;IAC1DvG,cAAc,CAACsC,UAAU,CAAC,8DAA8D,CAAC;EAC3F;AACF,CAAC;AAED,MAAMuG,kBAAkB,GAAG;EACzB1G,iBAAiB;EACjBkG,0BAA0B;EAC1BnI,0BAA0B;EAC1B0H;AACF,CAAC;AAED,eAAeiB,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}