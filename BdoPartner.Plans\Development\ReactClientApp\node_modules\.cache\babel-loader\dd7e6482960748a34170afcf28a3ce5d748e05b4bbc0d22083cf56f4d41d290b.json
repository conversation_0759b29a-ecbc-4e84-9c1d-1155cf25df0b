{"ast": null, "code": "var _jsxFileName = \"C:\\\\git\\\\Partner-Site\\\\BdoPartner.Plans\\\\Development\\\\ReactClientApp\\\\src\\\\components\\\\admin\\\\QuestionnaireDesignerHeader.jsx\";\nimport React from \"react\";\nimport { InputText } from \"primereact/inputtext\";\nimport { Dropdown } from \"primereact/dropdown\";\nimport { Button } from \"primereact/button\";\nimport { getCycleOptions, PartnerPlanCycle } from \"../../core/enumertions/partnerPlanCycle\";\nimport { QuestionnaireStatus } from \"../../core/enumertions/questionnaireStatus\";\nimport { confirmDialog } from \"primereact/confirmdialog\";\n\n/**\r\n * Header component for questionnaire designer with form controls and action buttons\r\n * @param {Object} props - Component props\r\n * @param {string} props.surveyName - The survey name\r\n * @param {number} props.selectedYear - The selected year\r\n * @param {Array} props.enabledCycles - Array of enabled cycles\r\n * @param {boolean} props.isQuestionnaireLoaded - Whether questionnaire is loaded\r\n * @param {Object} props.questionnaire - The questionnaire data\r\n * @param {boolean} props.isReadonly - Whether the form fields should be readonly (for published forms)\r\n * @param {Function} props.onSurveyNameChange - Callback for survey name changes\r\n * @param {Function} props.onYearChange - Callback for year changes\r\n * @param {Function} props.onEnabledCyclesChange - Callback for enabled cycles changes\r\n * @param {Function} props.onPublishCycle - Callback for publishing individual cycles\r\n * @param {Function} props.onSave - Callback for save action\r\n * @param {Function} props.onPublish - Callback for publish action\r\n * @param {Function} props.onClose - Callback for close action\r\n * @param {Function} props.onBackToList - Callback for back to list action\r\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const QuestionnaireDesignerHeader = ({\n  surveyName,\n  selectedYear,\n  enabledCycles,\n  isQuestionnaireLoaded,\n  questionnaire,\n  isReadonly,\n  onSurveyNameChange,\n  onYearChange,\n  onEnabledCyclesChange,\n  onPublishCycle,\n  onSave,\n  onPublish,\n  onClose,\n  onBackToList\n}) => {\n  // Year options for dropdown\n  const currentYear = new Date().getFullYear();\n  const yearOptions = [];\n  for (let i = currentYear - 5; i <= currentYear + 5; i++) {\n    yearOptions.push({\n      label: i.toString(),\n      value: i\n    });\n  }\n\n  // Validate if a cycle can be published based on sequence order\n  const canPublishCycle = cycleValue => {\n    // Planning (0) can always be published first\n    if (cycleValue === PartnerPlanCycle.Planning) {\n      return {\n        canPublish: true,\n        reason: \"\"\n      };\n    }\n\n    // Mid Year Review (1) can only be published if Planning (0) is already published\n    if (cycleValue === PartnerPlanCycle.MidYearReview) {\n      if (!enabledCycles.includes(PartnerPlanCycle.Planning)) {\n        return {\n          canPublish: false,\n          reason: \"Planning cycle must be published before Mid Year Review cycle\"\n        };\n      }\n      return {\n        canPublish: true,\n        reason: \"\"\n      };\n    }\n\n    // Year End Review (2) can only be published if both Planning (0) and Mid Year Review (1) are published\n    if (cycleValue === PartnerPlanCycle.YearEndReview) {\n      if (!enabledCycles.includes(PartnerPlanCycle.Planning)) {\n        return {\n          canPublish: false,\n          reason: \"Planning cycle must be published before Year End Review cycle\"\n        };\n      }\n      if (!enabledCycles.includes(PartnerPlanCycle.MidYearReview)) {\n        return {\n          canPublish: false,\n          reason: \"Mid Year Review cycle must be published before Year End Review cycle\"\n        };\n      }\n      return {\n        canPublish: true,\n        reason: \"\"\n      };\n    }\n    return {\n      canPublish: false,\n      reason: \"Invalid cycle value\"\n    };\n  };\n  const handlePublishCycle = (cycleValue, cycleName) => {\n    const isAlreadyEnabled = enabledCycles.includes(cycleValue);\n    if (isAlreadyEnabled) {\n      return; // Button should be disabled, but just in case\n    }\n\n    // Check if cycle can be published based on sequence validation\n    const validation = canPublishCycle(cycleValue);\n    if (!validation.canPublish) {\n      confirmDialog({\n        message: validation.reason,\n        header: \"Cannot Enable Cycle\",\n        icon: \"pi pi-exclamation-triangle\",\n        acceptLabel: \"OK\",\n        rejectLabel: null,\n        accept: () => {\n          // Just close the dialog\n        }\n      });\n      return;\n    }\n\n    // Call the parent's onPublishCycle method which will handle getting uncompleted forms count\n    // and showing the proper confirmation popup with detailed information\n    if (onPublishCycle) {\n      onPublishCycle(cycleValue);\n    }\n  };\n  const handleClose = () => {\n    confirmDialog({\n      message: \"Are you sure you want to close this questionnaire? This action will set the questionnaire status to Closed and cannot be undone.\",\n      header: \"Confirm Close Questionnaire\",\n      icon: \"pi pi-exclamation-triangle\",\n      accept: () => {\n        if (onClose) {\n          onClose();\n        }\n      }\n    });\n  };\n\n  // Check if Close button should be enabled\n  // Close button is enabled only when questionnaire is published and YearEndReview cycle is enabled\n  const canClose = (questionnaire === null || questionnaire === void 0 ? void 0 : questionnaire.status) === QuestionnaireStatus.Published && enabledCycles.includes(PartnerPlanCycle.YearEndReview);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"profile-card mb-3\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex align-items-center gap-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-column\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"font-semibold mb-1\",\n          children: \"Name *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(InputText, {\n          value: surveyName,\n          onChange: e => !isReadonly && onSurveyNameChange && onSurveyNameChange(e.target.value),\n          placeholder: \"Enter questionnaire name\",\n          style: {\n            width: \"300px\"\n          },\n          readOnly: isReadonly,\n          className: isReadonly ? \"p-inputtext-readonly\" : \"\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-column\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"font-semibold mb-1\",\n          children: \"Year *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n          value: selectedYear,\n          options: yearOptions,\n          onChange: e => !isReadonly && onYearChange && onYearChange(e.value),\n          placeholder: \"Select year\",\n          style: {\n            width: \"150px\"\n          },\n          disabled: isReadonly\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-column\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"font-semibold mb-1\",\n          children: \"Enable Cycles\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-wrap gap-2\",\n          children: getCycleOptions().map(cycleOption => {\n            const isEnabled = enabledCycles.includes(cycleOption.value);\n            const validation = canPublishCycle(cycleOption.value);\n            const isDisabled = isEnabled || !validation.canPublish;\n            let tooltipText;\n            if (isEnabled) {\n              tooltipText = \"Already published\";\n            } else if (!validation.canPublish) {\n              tooltipText = validation.reason;\n            } else {\n              tooltipText = `Publish ${cycleOption.label} cycle`;\n            }\n            return /*#__PURE__*/_jsxDEV(Button, {\n              label: cycleOption.label,\n              size: \"small\",\n              disabled: isDisabled,\n              severity: isEnabled ? \"success\" : validation.canPublish ? \"secondary\" : \"danger\",\n              icon: isEnabled ? \"pi pi-check\" : validation.canPublish ? \"pi pi-plus\" : \"pi pi-lock\",\n              onClick: () => handlePublishCycle(cycleOption.value, cycleOption.label),\n              tooltip: tooltipText,\n              tooltipOptions: {\n                position: \"top\"\n              }\n            }, cycleOption.value, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex align-items-end\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          label: \"Save Draft\",\n          icon: \"pi pi-save\",\n          onClick: () => {\n            console.log(\"External save button clicked\");\n            onSave && onSave(true); // Pass showMessage = true for manual save\n          },\n          className: \"mr-2\",\n          disabled: !isQuestionnaireLoaded || !questionnaire || !questionnaire.id || (questionnaire === null || questionnaire === void 0 ? void 0 : questionnaire.status) === QuestionnaireStatus.Closed\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          label: (questionnaire === null || questionnaire === void 0 ? void 0 : questionnaire.status) === 1 ? \"Republish\" : \"Publish\",\n          icon: \"pi pi-send\",\n          onClick: () => onPublish && onPublish(),\n          className: \"mr-2\",\n          disabled: !isQuestionnaireLoaded || !questionnaire || !questionnaire.id || (questionnaire === null || questionnaire === void 0 ? void 0 : questionnaire.status) === QuestionnaireStatus.Closed\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          label: \"Close\",\n          icon: (questionnaire === null || questionnaire === void 0 ? void 0 : questionnaire.status) === QuestionnaireStatus.Closed ? \"pi pi-lock\" : \"pi pi-unlock\",\n          onClick: handleClose,\n          className: \"mr-2\",\n          disabled: !canClose,\n          severity: \"warning\",\n          tooltip: canClose ? \"Close questionnaire\" : \"Questionnaire must be published and Year End Review cycle must be enabled to close\",\n          tooltipOptions: {\n            position: \"top\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          label: \"Back to List\",\n          icon: \"pi pi-arrow-left\",\n          onClick: () => onBackToList && onBackToList(),\n          className: \"p-button-secondary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 136,\n    columnNumber: 5\n  }, this);\n};\n_c = QuestionnaireDesignerHeader;\nvar _c;\n$RefreshReg$(_c, \"QuestionnaireDesignerHeader\");", "map": {"version": 3, "names": ["React", "InputText", "Dropdown", "<PERSON><PERSON>", "getCycleOptions", "PartnerPlanCycle", "QuestionnaireStatus", "confirmDialog", "jsxDEV", "_jsxDEV", "QuestionnaireDesignerHeader", "surveyName", "selected<PERSON>ear", "enabledCycles", "isQuestionnaireLoaded", "questionnaire", "is<PERSON><PERSON><PERSON>ly", "onSurveyNameChange", "onYearChange", "onEnabledCyclesChange", "onPublishCycle", "onSave", "onPublish", "onClose", "onBackToList", "currentYear", "Date", "getFullYear", "yearOptions", "i", "push", "label", "toString", "value", "canPublishCycle", "cycleValue", "Planning", "canPublish", "reason", "MidYearReview", "includes", "YearEndReview", "handlePublishCycle", "cycleName", "isAlreadyEnabled", "validation", "message", "header", "icon", "acceptLabel", "<PERSON><PERSON><PERSON><PERSON>", "accept", "handleClose", "canClose", "status", "Published", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onChange", "e", "target", "placeholder", "style", "width", "readOnly", "options", "disabled", "map", "cycleOption", "isEnabled", "isDisabled", "tooltipText", "size", "severity", "onClick", "tooltip", "tooltipOptions", "position", "console", "log", "id", "Closed", "_c", "$RefreshReg$"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/components/admin/QuestionnaireDesignerHeader.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { InputText } from \"primereact/inputtext\";\r\nimport { Dropdown } from \"primereact/dropdown\";\r\nimport { But<PERSON> } from \"primereact/button\";\r\nimport { getCycleOptions, PartnerPlanCycle } from \"../../core/enumertions/partnerPlanCycle\";\r\nimport { QuestionnaireStatus } from \"../../core/enumertions/questionnaireStatus\";\r\nimport { confirmDialog } from \"primereact/confirmdialog\";\r\n\r\n/**\r\n * Header component for questionnaire designer with form controls and action buttons\r\n * @param {Object} props - Component props\r\n * @param {string} props.surveyName - The survey name\r\n * @param {number} props.selectedYear - The selected year\r\n * @param {Array} props.enabledCycles - Array of enabled cycles\r\n * @param {boolean} props.isQuestionnaireLoaded - Whether questionnaire is loaded\r\n * @param {Object} props.questionnaire - The questionnaire data\r\n * @param {boolean} props.isReadonly - Whether the form fields should be readonly (for published forms)\r\n * @param {Function} props.onSurveyNameChange - Callback for survey name changes\r\n * @param {Function} props.onYearChange - Callback for year changes\r\n * @param {Function} props.onEnabledCyclesChange - Callback for enabled cycles changes\r\n * @param {Function} props.onPublishCycle - Callback for publishing individual cycles\r\n * @param {Function} props.onSave - Callback for save action\r\n * @param {Function} props.onPublish - Callback for publish action\r\n * @param {Function} props.onClose - Callback for close action\r\n * @param {Function} props.onBackToList - Callback for back to list action\r\n */\r\nexport const QuestionnaireDesignerHeader = ({\r\n  surveyName,\r\n  selectedYear,\r\n  enabledCycles,\r\n  isQuestionnaireLoaded,\r\n  questionnaire,\r\n  isReadonly,\r\n  onSurveyNameChange,\r\n  onYearChange,\r\n  onEnabledCyclesChange,\r\n  onPublishCycle,\r\n  onSave,\r\n  onPublish,\r\n  onClose,\r\n  onBackToList,\r\n}) => {\r\n  // Year options for dropdown\r\n  const currentYear = new Date().getFullYear();\r\n  const yearOptions = [];\r\n  for (let i = currentYear - 5; i <= currentYear + 5; i++) {\r\n    yearOptions.push({ label: i.toString(), value: i });\r\n  }\r\n\r\n  // Validate if a cycle can be published based on sequence order\r\n  const canPublishCycle = (cycleValue) => {\r\n    // Planning (0) can always be published first\r\n    if (cycleValue === PartnerPlanCycle.Planning) {\r\n      return { canPublish: true, reason: \"\" };\r\n    }\r\n\r\n    // Mid Year Review (1) can only be published if Planning (0) is already published\r\n    if (cycleValue === PartnerPlanCycle.MidYearReview) {\r\n      if (!enabledCycles.includes(PartnerPlanCycle.Planning)) {\r\n        return {\r\n          canPublish: false,\r\n          reason: \"Planning cycle must be published before Mid Year Review cycle\",\r\n        };\r\n      }\r\n      return { canPublish: true, reason: \"\" };\r\n    }\r\n\r\n    // Year End Review (2) can only be published if both Planning (0) and Mid Year Review (1) are published\r\n    if (cycleValue === PartnerPlanCycle.YearEndReview) {\r\n      if (!enabledCycles.includes(PartnerPlanCycle.Planning)) {\r\n        return {\r\n          canPublish: false,\r\n          reason: \"Planning cycle must be published before Year End Review cycle\",\r\n        };\r\n      }\r\n      if (!enabledCycles.includes(PartnerPlanCycle.MidYearReview)) {\r\n        return {\r\n          canPublish: false,\r\n          reason: \"Mid Year Review cycle must be published before Year End Review cycle\",\r\n        };\r\n      }\r\n      return { canPublish: true, reason: \"\" };\r\n    }\r\n\r\n    return { canPublish: false, reason: \"Invalid cycle value\" };\r\n  };\r\n\r\n  const handlePublishCycle = (cycleValue, cycleName) => {\r\n    const isAlreadyEnabled = enabledCycles.includes(cycleValue);\r\n\r\n    if (isAlreadyEnabled) {\r\n      return; // Button should be disabled, but just in case\r\n    }\r\n\r\n    // Check if cycle can be published based on sequence validation\r\n    const validation = canPublishCycle(cycleValue);\r\n    if (!validation.canPublish) {\r\n      confirmDialog({\r\n        message: validation.reason,\r\n        header: \"Cannot Enable Cycle\",\r\n        icon: \"pi pi-exclamation-triangle\",\r\n        acceptLabel: \"OK\",\r\n        rejectLabel: null,\r\n        accept: () => {\r\n          // Just close the dialog\r\n        },\r\n      });\r\n      return;\r\n    }\r\n\r\n    // Call the parent's onPublishCycle method which will handle getting uncompleted forms count\r\n    // and showing the proper confirmation popup with detailed information\r\n    if (onPublishCycle) {\r\n      onPublishCycle(cycleValue);\r\n    }\r\n  };\r\n\r\n  const handleClose = () => {\r\n    confirmDialog({\r\n      message: \"Are you sure you want to close this questionnaire? This action will set the questionnaire status to Closed and cannot be undone.\",\r\n      header: \"Confirm Close Questionnaire\",\r\n      icon: \"pi pi-exclamation-triangle\",\r\n      accept: () => {\r\n        if (onClose) {\r\n          onClose();\r\n        }\r\n      },\r\n    });\r\n  };\r\n\r\n  // Check if Close button should be enabled\r\n  // Close button is enabled only when questionnaire is published and YearEndReview cycle is enabled\r\n  const canClose = questionnaire?.status === QuestionnaireStatus.Published && enabledCycles.includes(PartnerPlanCycle.YearEndReview);\r\n\r\n  return (\r\n    <div className=\"profile-card mb-3\">\r\n      <div className=\"flex align-items-center gap-3\">\r\n        <div className=\"flex flex-column\">\r\n          <label className=\"font-semibold mb-1\">Name *</label>\r\n          <InputText\r\n            value={surveyName}\r\n            onChange={(e) => !isReadonly && onSurveyNameChange && onSurveyNameChange(e.target.value)}\r\n            placeholder=\"Enter questionnaire name\"\r\n            style={{ width: \"300px\" }}\r\n            readOnly={isReadonly}\r\n            className={isReadonly ? \"p-inputtext-readonly\" : \"\"}\r\n          />\r\n        </div>\r\n        <div className=\"flex flex-column\">\r\n          <label className=\"font-semibold mb-1\">Year *</label>\r\n          <Dropdown\r\n            value={selectedYear}\r\n            options={yearOptions}\r\n            onChange={(e) => !isReadonly && onYearChange && onYearChange(e.value)}\r\n            placeholder=\"Select year\"\r\n            style={{ width: \"150px\" }}\r\n            disabled={isReadonly}\r\n          />\r\n        </div>\r\n        <div className=\"flex flex-column\">\r\n          <label className=\"font-semibold mb-1\">Enable Cycles</label>\r\n          <div className=\"flex flex-wrap gap-2\">\r\n            {getCycleOptions().map((cycleOption) => {\r\n              const isEnabled = enabledCycles.includes(cycleOption.value);\r\n              const validation = canPublishCycle(cycleOption.value);\r\n              const isDisabled = isEnabled || !validation.canPublish;\r\n\r\n              let tooltipText;\r\n              if (isEnabled) {\r\n                tooltipText = \"Already published\";\r\n              } else if (!validation.canPublish) {\r\n                tooltipText = validation.reason;\r\n              } else {\r\n                tooltipText = `Publish ${cycleOption.label} cycle`;\r\n              }\r\n\r\n              return (\r\n                <Button\r\n                  key={cycleOption.value}\r\n                  label={cycleOption.label}\r\n                  size=\"small\"\r\n                  disabled={isDisabled}\r\n                  severity={isEnabled ? \"success\" : validation.canPublish ? \"secondary\" : \"danger\"}\r\n                  icon={isEnabled ? \"pi pi-check\" : validation.canPublish ? \"pi pi-plus\" : \"pi pi-lock\"}\r\n                  onClick={() => handlePublishCycle(cycleOption.value, cycleOption.label)}\r\n                  tooltip={tooltipText}\r\n                  tooltipOptions={{ position: \"top\" }}\r\n                />\r\n              );\r\n            })}\r\n          </div>\r\n        </div>\r\n        <div className=\"flex align-items-end\">\r\n          <Button\r\n            label=\"Save Draft\"\r\n            icon=\"pi pi-save\"\r\n            onClick={() => {\r\n              console.log(\"External save button clicked\");\r\n              onSave && onSave(true); // Pass showMessage = true for manual save\r\n            }}\r\n            className=\"mr-2\"\r\n            disabled={!isQuestionnaireLoaded || !questionnaire || !questionnaire.id || questionnaire?.status === QuestionnaireStatus.Closed}\r\n          />\r\n          <Button\r\n            label={questionnaire?.status === 1 ? \"Republish\" : \"Publish\"}\r\n            icon=\"pi pi-send\"\r\n            onClick={() => onPublish && onPublish()}\r\n            className=\"mr-2\"\r\n            disabled={!isQuestionnaireLoaded || !questionnaire || !questionnaire.id || questionnaire?.status === QuestionnaireStatus.Closed}\r\n          />\r\n          <Button\r\n            label=\"Close\"\r\n            icon={questionnaire?.status === QuestionnaireStatus.Closed ? \"pi pi-lock\" : \"pi pi-unlock\"}\r\n            onClick={handleClose}\r\n            className=\"mr-2\"\r\n            disabled={!canClose}\r\n            severity=\"warning\"\r\n            tooltip={canClose ? \"Close questionnaire\" : \"Questionnaire must be published and Year End Review cycle must be enabled to close\"}\r\n            tooltipOptions={{ position: \"top\" }}\r\n          />\r\n          <Button label=\"Back to List\" icon=\"pi pi-arrow-left\" onClick={() => onBackToList && onBackToList()} className=\"p-button-secondary\" />\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,eAAe,EAAEC,gBAAgB,QAAQ,yCAAyC;AAC3F,SAASC,mBAAmB,QAAQ,4CAA4C;AAChF,SAASC,aAAa,QAAQ,0BAA0B;;AAExD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAjBA,SAAAC,MAAA,IAAAC,OAAA;AAkBA,OAAO,MAAMC,2BAA2B,GAAGA,CAAC;EAC1CC,UAAU;EACVC,YAAY;EACZC,aAAa;EACbC,qBAAqB;EACrBC,aAAa;EACbC,UAAU;EACVC,kBAAkB;EAClBC,YAAY;EACZC,qBAAqB;EACrBC,cAAc;EACdC,MAAM;EACNC,SAAS;EACTC,OAAO;EACPC;AACF,CAAC,KAAK;EACJ;EACA,MAAMC,WAAW,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;EAC5C,MAAMC,WAAW,GAAG,EAAE;EACtB,KAAK,IAAIC,CAAC,GAAGJ,WAAW,GAAG,CAAC,EAAEI,CAAC,IAAIJ,WAAW,GAAG,CAAC,EAAEI,CAAC,EAAE,EAAE;IACvDD,WAAW,CAACE,IAAI,CAAC;MAAEC,KAAK,EAAEF,CAAC,CAACG,QAAQ,CAAC,CAAC;MAAEC,KAAK,EAAEJ;IAAE,CAAC,CAAC;EACrD;;EAEA;EACA,MAAMK,eAAe,GAAIC,UAAU,IAAK;IACtC;IACA,IAAIA,UAAU,KAAK9B,gBAAgB,CAAC+B,QAAQ,EAAE;MAC5C,OAAO;QAAEC,UAAU,EAAE,IAAI;QAAEC,MAAM,EAAE;MAAG,CAAC;IACzC;;IAEA;IACA,IAAIH,UAAU,KAAK9B,gBAAgB,CAACkC,aAAa,EAAE;MACjD,IAAI,CAAC1B,aAAa,CAAC2B,QAAQ,CAACnC,gBAAgB,CAAC+B,QAAQ,CAAC,EAAE;QACtD,OAAO;UACLC,UAAU,EAAE,KAAK;UACjBC,MAAM,EAAE;QACV,CAAC;MACH;MACA,OAAO;QAAED,UAAU,EAAE,IAAI;QAAEC,MAAM,EAAE;MAAG,CAAC;IACzC;;IAEA;IACA,IAAIH,UAAU,KAAK9B,gBAAgB,CAACoC,aAAa,EAAE;MACjD,IAAI,CAAC5B,aAAa,CAAC2B,QAAQ,CAACnC,gBAAgB,CAAC+B,QAAQ,CAAC,EAAE;QACtD,OAAO;UACLC,UAAU,EAAE,KAAK;UACjBC,MAAM,EAAE;QACV,CAAC;MACH;MACA,IAAI,CAACzB,aAAa,CAAC2B,QAAQ,CAACnC,gBAAgB,CAACkC,aAAa,CAAC,EAAE;QAC3D,OAAO;UACLF,UAAU,EAAE,KAAK;UACjBC,MAAM,EAAE;QACV,CAAC;MACH;MACA,OAAO;QAAED,UAAU,EAAE,IAAI;QAAEC,MAAM,EAAE;MAAG,CAAC;IACzC;IAEA,OAAO;MAAED,UAAU,EAAE,KAAK;MAAEC,MAAM,EAAE;IAAsB,CAAC;EAC7D,CAAC;EAED,MAAMI,kBAAkB,GAAGA,CAACP,UAAU,EAAEQ,SAAS,KAAK;IACpD,MAAMC,gBAAgB,GAAG/B,aAAa,CAAC2B,QAAQ,CAACL,UAAU,CAAC;IAE3D,IAAIS,gBAAgB,EAAE;MACpB,OAAO,CAAC;IACV;;IAEA;IACA,MAAMC,UAAU,GAAGX,eAAe,CAACC,UAAU,CAAC;IAC9C,IAAI,CAACU,UAAU,CAACR,UAAU,EAAE;MAC1B9B,aAAa,CAAC;QACZuC,OAAO,EAAED,UAAU,CAACP,MAAM;QAC1BS,MAAM,EAAE,qBAAqB;QAC7BC,IAAI,EAAE,4BAA4B;QAClCC,WAAW,EAAE,IAAI;QACjBC,WAAW,EAAE,IAAI;QACjBC,MAAM,EAAEA,CAAA,KAAM;UACZ;QAAA;MAEJ,CAAC,CAAC;MACF;IACF;;IAEA;IACA;IACA,IAAI/B,cAAc,EAAE;MAClBA,cAAc,CAACe,UAAU,CAAC;IAC5B;EACF,CAAC;EAED,MAAMiB,WAAW,GAAGA,CAAA,KAAM;IACxB7C,aAAa,CAAC;MACZuC,OAAO,EAAE,kIAAkI;MAC3IC,MAAM,EAAE,6BAA6B;MACrCC,IAAI,EAAE,4BAA4B;MAClCG,MAAM,EAAEA,CAAA,KAAM;QACZ,IAAI5B,OAAO,EAAE;UACXA,OAAO,CAAC,CAAC;QACX;MACF;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA;EACA,MAAM8B,QAAQ,GAAG,CAAAtC,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEuC,MAAM,MAAKhD,mBAAmB,CAACiD,SAAS,IAAI1C,aAAa,CAAC2B,QAAQ,CAACnC,gBAAgB,CAACoC,aAAa,CAAC;EAElI,oBACEhC,OAAA;IAAK+C,SAAS,EAAC,mBAAmB;IAAAC,QAAA,eAChChD,OAAA;MAAK+C,SAAS,EAAC,+BAA+B;MAAAC,QAAA,gBAC5ChD,OAAA;QAAK+C,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BhD,OAAA;UAAO+C,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACpDpD,OAAA,CAACR,SAAS;UACRgC,KAAK,EAAEtB,UAAW;UAClBmD,QAAQ,EAAGC,CAAC,IAAK,CAAC/C,UAAU,IAAIC,kBAAkB,IAAIA,kBAAkB,CAAC8C,CAAC,CAACC,MAAM,CAAC/B,KAAK,CAAE;UACzFgC,WAAW,EAAC,0BAA0B;UACtCC,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAQ,CAAE;UAC1BC,QAAQ,EAAEpD,UAAW;UACrBwC,SAAS,EAAExC,UAAU,GAAG,sBAAsB,GAAG;QAAG;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNpD,OAAA;QAAK+C,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BhD,OAAA;UAAO+C,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACpDpD,OAAA,CAACP,QAAQ;UACP+B,KAAK,EAAErB,YAAa;UACpByD,OAAO,EAAEzC,WAAY;UACrBkC,QAAQ,EAAGC,CAAC,IAAK,CAAC/C,UAAU,IAAIE,YAAY,IAAIA,YAAY,CAAC6C,CAAC,CAAC9B,KAAK,CAAE;UACtEgC,WAAW,EAAC,aAAa;UACzBC,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAQ,CAAE;UAC1BG,QAAQ,EAAEtD;QAAW;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNpD,OAAA;QAAK+C,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BhD,OAAA;UAAO+C,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC3DpD,OAAA;UAAK+C,SAAS,EAAC,sBAAsB;UAAAC,QAAA,EAClCrD,eAAe,CAAC,CAAC,CAACmE,GAAG,CAAEC,WAAW,IAAK;YACtC,MAAMC,SAAS,GAAG5D,aAAa,CAAC2B,QAAQ,CAACgC,WAAW,CAACvC,KAAK,CAAC;YAC3D,MAAMY,UAAU,GAAGX,eAAe,CAACsC,WAAW,CAACvC,KAAK,CAAC;YACrD,MAAMyC,UAAU,GAAGD,SAAS,IAAI,CAAC5B,UAAU,CAACR,UAAU;YAEtD,IAAIsC,WAAW;YACf,IAAIF,SAAS,EAAE;cACbE,WAAW,GAAG,mBAAmB;YACnC,CAAC,MAAM,IAAI,CAAC9B,UAAU,CAACR,UAAU,EAAE;cACjCsC,WAAW,GAAG9B,UAAU,CAACP,MAAM;YACjC,CAAC,MAAM;cACLqC,WAAW,GAAG,WAAWH,WAAW,CAACzC,KAAK,QAAQ;YACpD;YAEA,oBACEtB,OAAA,CAACN,MAAM;cAEL4B,KAAK,EAAEyC,WAAW,CAACzC,KAAM;cACzB6C,IAAI,EAAC,OAAO;cACZN,QAAQ,EAAEI,UAAW;cACrBG,QAAQ,EAAEJ,SAAS,GAAG,SAAS,GAAG5B,UAAU,CAACR,UAAU,GAAG,WAAW,GAAG,QAAS;cACjFW,IAAI,EAAEyB,SAAS,GAAG,aAAa,GAAG5B,UAAU,CAACR,UAAU,GAAG,YAAY,GAAG,YAAa;cACtFyC,OAAO,EAAEA,CAAA,KAAMpC,kBAAkB,CAAC8B,WAAW,CAACvC,KAAK,EAAEuC,WAAW,CAACzC,KAAK,CAAE;cACxEgD,OAAO,EAAEJ,WAAY;cACrBK,cAAc,EAAE;gBAAEC,QAAQ,EAAE;cAAM;YAAE,GAR/BT,WAAW,CAACvC,KAAK;cAAAyB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OASvB,CAAC;UAEN,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNpD,OAAA;QAAK+C,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACnChD,OAAA,CAACN,MAAM;UACL4B,KAAK,EAAC,YAAY;UAClBiB,IAAI,EAAC,YAAY;UACjB8B,OAAO,EAAEA,CAAA,KAAM;YACbI,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;YAC3C9D,MAAM,IAAIA,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;UAC1B,CAAE;UACFmC,SAAS,EAAC,MAAM;UAChBc,QAAQ,EAAE,CAACxD,qBAAqB,IAAI,CAACC,aAAa,IAAI,CAACA,aAAa,CAACqE,EAAE,IAAI,CAAArE,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEuC,MAAM,MAAKhD,mBAAmB,CAAC+E;QAAO;UAAA3B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjI,CAAC,eACFpD,OAAA,CAACN,MAAM;UACL4B,KAAK,EAAE,CAAAhB,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEuC,MAAM,MAAK,CAAC,GAAG,WAAW,GAAG,SAAU;UAC7DN,IAAI,EAAC,YAAY;UACjB8B,OAAO,EAAEA,CAAA,KAAMxD,SAAS,IAAIA,SAAS,CAAC,CAAE;UACxCkC,SAAS,EAAC,MAAM;UAChBc,QAAQ,EAAE,CAACxD,qBAAqB,IAAI,CAACC,aAAa,IAAI,CAACA,aAAa,CAACqE,EAAE,IAAI,CAAArE,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEuC,MAAM,MAAKhD,mBAAmB,CAAC+E;QAAO;UAAA3B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjI,CAAC,eACFpD,OAAA,CAACN,MAAM;UACL4B,KAAK,EAAC,OAAO;UACbiB,IAAI,EAAE,CAAAjC,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEuC,MAAM,MAAKhD,mBAAmB,CAAC+E,MAAM,GAAG,YAAY,GAAG,cAAe;UAC3FP,OAAO,EAAE1B,WAAY;UACrBI,SAAS,EAAC,MAAM;UAChBc,QAAQ,EAAE,CAACjB,QAAS;UACpBwB,QAAQ,EAAC,SAAS;UAClBE,OAAO,EAAE1B,QAAQ,GAAG,qBAAqB,GAAG,oFAAqF;UACjI2B,cAAc,EAAE;YAAEC,QAAQ,EAAE;UAAM;QAAE;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC,eACFpD,OAAA,CAACN,MAAM;UAAC4B,KAAK,EAAC,cAAc;UAACiB,IAAI,EAAC,kBAAkB;UAAC8B,OAAO,EAAEA,CAAA,KAAMtD,YAAY,IAAIA,YAAY,CAAC,CAAE;UAACgC,SAAS,EAAC;QAAoB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACyB,EAAA,GAvMW5E,2BAA2B;AAAA,IAAA4E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}