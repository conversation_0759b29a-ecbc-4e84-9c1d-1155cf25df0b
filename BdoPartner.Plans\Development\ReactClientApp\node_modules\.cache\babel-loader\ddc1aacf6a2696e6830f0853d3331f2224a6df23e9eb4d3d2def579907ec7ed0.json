{"ast": null, "code": "var _jsxFileName = \"C:\\\\git\\\\Partner-Site\\\\BdoPartner.Plans\\\\Development\\\\ReactClientApp\\\\src\\\\pages\\\\admin\\\\partnerRefereneDataManagement.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { TabView, TabPanel } from \"primereact/tabview\";\nimport { Card } from \"primereact/card\";\nimport { UploadPartnerReviewerAssignment } from \"../../components/admin/UploadPartnerReviewerAssignment\";\nimport { PartnerReviewerManagement } from \"../../components/admin/PartnerReviewerManagement\";\nimport { UploadPartnerReferenceData } from \"../../components/admin/UploadPartnerReferenceData\";\nimport { PartnerReferenceDataMaintain } from \"../../components/admin/PartnerReferenceDataMaintain\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const PartnerReferenceDataManagement = () => {\n  _s();\n  // Local storage key for persisting the selected tab\n  const STORAGE_KEY = 'partnerReferenceDataManagement_activeTab';\n\n  // Initialize activeIndex from localStorage or default to 0\n  const [activeIndex, setActiveIndex] = useState(() => {\n    const savedIndex = localStorage.getItem(STORAGE_KEY);\n    return savedIndex !== null ? parseInt(savedIndex, 10) : 0;\n  });\n\n  // Save activeIndex to localStorage whenever it changes\n  useEffect(() => {\n    localStorage.setItem(STORAGE_KEY, activeIndex.toString());\n  }, [activeIndex]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"banner\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"banner__site-title-area\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          class: \"page-title\",\n          children: \"Partner Reference Data Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(TabView, {\n        activeIndex: activeIndex,\n        onTabChange: e => setActiveIndex(e.index),\n        children: [/*#__PURE__*/_jsxDEV(TabPanel, {\n          header: \"Upload Partner Reviewer Assignment\",\n          leftIcon: \"pi pi-upload\",\n          children: /*#__PURE__*/_jsxDEV(UploadPartnerReviewerAssignment, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n          header: \"Manage Partner Reviewer\",\n          leftIcon: \"pi pi-users\",\n          children: /*#__PURE__*/_jsxDEV(PartnerReviewerManagement, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n          header: \"Upload Partner Reference Data\",\n          leftIcon: \"pi pi-cloud-upload\",\n          children: /*#__PURE__*/_jsxDEV(UploadPartnerReferenceData, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n          header: \"Manage Partner Reference Data\",\n          leftIcon: \"pi pi-database\",\n          children: /*#__PURE__*/_jsxDEV(PartnerReferenceDataMaintain, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 25,\n    columnNumber: 5\n  }, this);\n};\n_s(PartnerReferenceDataManagement, \"EXPi9/A+/pBJywImNZsCki9q4G8=\");\n_c = PartnerReferenceDataManagement;\nvar _c;\n$RefreshReg$(_c, \"PartnerReferenceDataManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "TabView", "TabPanel", "Card", "UploadPartnerReviewerAssignment", "PartnerReviewerManagement", "UploadPartnerReferenceData", "PartnerReferenceDataMaintain", "jsxDEV", "_jsxDEV", "PartnerReferenceDataManagement", "_s", "STORAGE_KEY", "activeIndex", "setActiveIndex", "savedIndex", "localStorage", "getItem", "parseInt", "setItem", "toString", "children", "className", "class", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onTabChange", "e", "index", "header", "leftIcon", "_c", "$RefreshReg$"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/pages/admin/partnerRefereneDataManagement.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport { TabView, TabPanel } from \"primereact/tabview\";\r\nimport { Card } from \"primereact/card\";\r\nimport { UploadPartnerReviewerAssignment } from \"../../components/admin/UploadPartnerReviewerAssignment\";\r\nimport { PartnerReviewerManagement } from \"../../components/admin/PartnerReviewerManagement\";\r\nimport { UploadPartnerReferenceData } from \"../../components/admin/UploadPartnerReferenceData\";\r\nimport { PartnerReferenceDataMaintain } from \"../../components/admin/PartnerReferenceDataMaintain\";\r\n\r\nexport const PartnerReferenceDataManagement = () => {\r\n  // Local storage key for persisting the selected tab\r\n  const STORAGE_KEY = 'partnerReferenceDataManagement_activeTab';\r\n\r\n  // Initialize activeIndex from localStorage or default to 0\r\n  const [activeIndex, setActiveIndex] = useState(() => {\r\n    const savedIndex = localStorage.getItem(STORAGE_KEY);\r\n    return savedIndex !== null ? parseInt(savedIndex, 10) : 0;\r\n  });\r\n\r\n  // Save activeIndex to localStorage whenever it changes\r\n  useEffect(() => {\r\n    localStorage.setItem(STORAGE_KEY, activeIndex.toString());\r\n  }, [activeIndex]);\r\n\r\n  return (\r\n    <div>\r\n      <div className=\"banner\">\r\n        <div className=\"banner__site-title-area\">\r\n          <div class=\"page-title\">Partner Reference Data Management</div>\r\n        </div>\r\n      </div>\r\n\r\n      <Card>\r\n        <TabView\r\n          activeIndex={activeIndex}\r\n          onTabChange={(e) => setActiveIndex(e.index)}\r\n        >\r\n          <TabPanel header=\"Upload Partner Reviewer Assignment\" leftIcon=\"pi pi-upload\">\r\n            <UploadPartnerReviewerAssignment />\r\n          </TabPanel>\r\n          <TabPanel header=\"Manage Partner Reviewer\" leftIcon=\"pi pi-users\">\r\n            <PartnerReviewerManagement />\r\n          </TabPanel>\r\n          <TabPanel header=\"Upload Partner Reference Data\" leftIcon=\"pi pi-cloud-upload\">\r\n            <UploadPartnerReferenceData />\r\n          </TabPanel>\r\n          <TabPanel header=\"Manage Partner Reference Data\" leftIcon=\"pi pi-database\">\r\n            <PartnerReferenceDataMaintain />\r\n          </TabPanel>\r\n        </TabView>\r\n      </Card>\r\n    </div>\r\n  );\r\n};"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,EAAEC,QAAQ,QAAQ,oBAAoB;AACtD,SAASC,IAAI,QAAQ,iBAAiB;AACtC,SAASC,+BAA+B,QAAQ,wDAAwD;AACxG,SAASC,yBAAyB,QAAQ,kDAAkD;AAC5F,SAASC,0BAA0B,QAAQ,mDAAmD;AAC9F,SAASC,4BAA4B,QAAQ,qDAAqD;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnG,OAAO,MAAMC,8BAA8B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClD;EACA,MAAMC,WAAW,GAAG,0CAA0C;;EAE9D;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGf,QAAQ,CAAC,MAAM;IACnD,MAAMgB,UAAU,GAAGC,YAAY,CAACC,OAAO,CAACL,WAAW,CAAC;IACpD,OAAOG,UAAU,KAAK,IAAI,GAAGG,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC,GAAG,CAAC;EAC3D,CAAC,CAAC;;EAEF;EACAf,SAAS,CAAC,MAAM;IACdgB,YAAY,CAACG,OAAO,CAACP,WAAW,EAAEC,WAAW,CAACO,QAAQ,CAAC,CAAC,CAAC;EAC3D,CAAC,EAAE,CAACP,WAAW,CAAC,CAAC;EAEjB,oBACEJ,OAAA;IAAAY,QAAA,gBACEZ,OAAA;MAAKa,SAAS,EAAC,QAAQ;MAAAD,QAAA,eACrBZ,OAAA;QAAKa,SAAS,EAAC,yBAAyB;QAAAD,QAAA,eACtCZ,OAAA;UAAKc,KAAK,EAAC,YAAY;UAAAF,QAAA,EAAC;QAAiC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENlB,OAAA,CAACN,IAAI;MAAAkB,QAAA,eACHZ,OAAA,CAACR,OAAO;QACNY,WAAW,EAAEA,WAAY;QACzBe,WAAW,EAAGC,CAAC,IAAKf,cAAc,CAACe,CAAC,CAACC,KAAK,CAAE;QAAAT,QAAA,gBAE5CZ,OAAA,CAACP,QAAQ;UAAC6B,MAAM,EAAC,oCAAoC;UAACC,QAAQ,EAAC,cAAc;UAAAX,QAAA,eAC3EZ,OAAA,CAACL,+BAA+B;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,eACXlB,OAAA,CAACP,QAAQ;UAAC6B,MAAM,EAAC,yBAAyB;UAACC,QAAQ,EAAC,aAAa;UAAAX,QAAA,eAC/DZ,OAAA,CAACJ,yBAAyB;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC,eACXlB,OAAA,CAACP,QAAQ;UAAC6B,MAAM,EAAC,+BAA+B;UAACC,QAAQ,EAAC,oBAAoB;UAAAX,QAAA,eAC5EZ,OAAA,CAACH,0BAA0B;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eACXlB,OAAA,CAACP,QAAQ;UAAC6B,MAAM,EAAC,+BAA+B;UAACC,QAAQ,EAAC,gBAAgB;UAAAX,QAAA,eACxEZ,OAAA,CAACF,4BAA4B;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAChB,EAAA,CA5CWD,8BAA8B;AAAAuB,EAAA,GAA9BvB,8BAA8B;AAAA,IAAAuB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}