{"ast": null, "code": "/**\r\n * Request deduplication utility to prevent duplicate API calls\r\n * Especially useful in React StrictMode where effects run twice\r\n */\n\nclass RequestDeduplication {\n  constructor() {\n    this.pendingRequests = new Map();\n    this.requestTimeouts = new Map();\n  }\n\n  /**\r\n   * Create a unique key for a request\r\n   * @param {string} method - HTTP method\r\n   * @param {string} url - Request URL\r\n   * @param {object} params - Request parameters\r\n   * @returns {string} Unique request key\r\n   */\n  createRequestKey(method, url, params = {}) {\n    const sortedParams = JSON.stringify(params, Object.keys(params).sort());\n    return `${method.toUpperCase()}:${url}:${sortedParams}`;\n  }\n\n  /**\r\n   * Execute a request with deduplication\r\n   * @param {string} key - Unique request key\r\n   * @param {Function} requestFn - Function that returns a promise for the request\r\n   * @param {number} timeout - Timeout in ms to clear the cache (default: 1000ms)\r\n   * @returns {Promise} The request promise\r\n   */\n  async execute(key, requestFn, timeout = 1000) {\n    // If request is already pending, return the existing promise\n    if (this.pendingRequests.has(key)) {\n      return this.pendingRequests.get(key);\n    }\n\n    // Create and store the request promise\n    const requestPromise = requestFn().finally(() => {\n      // Clean up after request completes\n      this.pendingRequests.delete(key);\n      if (this.requestTimeouts.has(key)) {\n        clearTimeout(this.requestTimeouts.get(key));\n        this.requestTimeouts.delete(key);\n      }\n    });\n    this.pendingRequests.set(key, requestPromise);\n\n    // Set a timeout to clean up the cache in case of hanging requests\n    const timeoutId = setTimeout(() => {\n      this.pendingRequests.delete(key);\n      this.requestTimeouts.delete(key);\n    }, timeout);\n    this.requestTimeouts.set(key, timeoutId);\n    return requestPromise;\n  }\n\n  /**\r\n   * Clear all pending requests (useful for cleanup)\r\n   */\n  clear() {\n    // Clear all timeouts\n    this.requestTimeouts.forEach(timeoutId => clearTimeout(timeoutId));\n\n    // Clear maps\n    this.pendingRequests.clear();\n    this.requestTimeouts.clear();\n  }\n\n  /**\r\n   * Check if a request is currently pending\r\n   * @param {string} key - Request key\r\n   * @returns {boolean} True if request is pending\r\n   */\n  isPending(key) {\n    return this.pendingRequests.has(key);\n  }\n}\n\n// Export singleton instance\nexport const requestDeduplication = new RequestDeduplication();\nexport default requestDeduplication;", "map": {"version": 3, "names": ["RequestDeduplication", "constructor", "pendingRequests", "Map", "requestTimeouts", "createRequestKey", "method", "url", "params", "sortedParams", "JSON", "stringify", "Object", "keys", "sort", "toUpperCase", "execute", "key", "requestFn", "timeout", "has", "get", "requestPromise", "finally", "delete", "clearTimeout", "set", "timeoutId", "setTimeout", "clear", "for<PERSON>ach", "isPending", "requestDeduplication"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/core/http/requestDeduplication.js"], "sourcesContent": ["/**\r\n * Request deduplication utility to prevent duplicate API calls\r\n * Especially useful in React StrictMode where effects run twice\r\n */\r\n\r\nclass RequestDeduplication {\r\n  constructor() {\r\n    this.pendingRequests = new Map();\r\n    this.requestTimeouts = new Map();\r\n  }\r\n\r\n  /**\r\n   * Create a unique key for a request\r\n   * @param {string} method - HTTP method\r\n   * @param {string} url - Request URL\r\n   * @param {object} params - Request parameters\r\n   * @returns {string} Unique request key\r\n   */\r\n  createRequestKey(method, url, params = {}) {\r\n    const sortedParams = JSON.stringify(params, Object.keys(params).sort());\r\n    return `${method.toUpperCase()}:${url}:${sortedParams}`;\r\n  }\r\n\r\n  /**\r\n   * Execute a request with deduplication\r\n   * @param {string} key - Unique request key\r\n   * @param {Function} requestFn - Function that returns a promise for the request\r\n   * @param {number} timeout - Timeout in ms to clear the cache (default: 1000ms)\r\n   * @returns {Promise} The request promise\r\n   */\r\n  async execute(key, requestFn, timeout = 1000) {\r\n    // If request is already pending, return the existing promise\r\n    if (this.pendingRequests.has(key)) {\r\n      return this.pendingRequests.get(key);\r\n    }\r\n\r\n    // Create and store the request promise\r\n    const requestPromise = requestFn()\r\n      .finally(() => {\r\n        // Clean up after request completes\r\n        this.pendingRequests.delete(key);\r\n        if (this.requestTimeouts.has(key)) {\r\n          clearTimeout(this.requestTimeouts.get(key));\r\n          this.requestTimeouts.delete(key);\r\n        }\r\n      });\r\n\r\n    this.pendingRequests.set(key, requestPromise);\r\n\r\n    // Set a timeout to clean up the cache in case of hanging requests\r\n    const timeoutId = setTimeout(() => {\r\n      this.pendingRequests.delete(key);\r\n      this.requestTimeouts.delete(key);\r\n    }, timeout);\r\n\r\n    this.requestTimeouts.set(key, timeoutId);\r\n\r\n    return requestPromise;\r\n  }\r\n\r\n  /**\r\n   * Clear all pending requests (useful for cleanup)\r\n   */\r\n  clear() {\r\n    // Clear all timeouts\r\n    this.requestTimeouts.forEach(timeoutId => clearTimeout(timeoutId));\r\n    \r\n    // Clear maps\r\n    this.pendingRequests.clear();\r\n    this.requestTimeouts.clear();\r\n  }\r\n\r\n  /**\r\n   * Check if a request is currently pending\r\n   * @param {string} key - Request key\r\n   * @returns {boolean} True if request is pending\r\n   */\r\n  isPending(key) {\r\n    return this.pendingRequests.has(key);\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const requestDeduplication = new RequestDeduplication();\r\nexport default requestDeduplication;\r\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA,MAAMA,oBAAoB,CAAC;EACzBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,eAAe,GAAG,IAAIC,GAAG,CAAC,CAAC;IAChC,IAAI,CAACC,eAAe,GAAG,IAAID,GAAG,CAAC,CAAC;EAClC;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACEE,gBAAgBA,CAACC,MAAM,EAAEC,GAAG,EAAEC,MAAM,GAAG,CAAC,CAAC,EAAE;IACzC,MAAMC,YAAY,GAAGC,IAAI,CAACC,SAAS,CAACH,MAAM,EAAEI,MAAM,CAACC,IAAI,CAACL,MAAM,CAAC,CAACM,IAAI,CAAC,CAAC,CAAC;IACvE,OAAO,GAAGR,MAAM,CAACS,WAAW,CAAC,CAAC,IAAIR,GAAG,IAAIE,YAAY,EAAE;EACzD;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,MAAMO,OAAOA,CAACC,GAAG,EAAEC,SAAS,EAAEC,OAAO,GAAG,IAAI,EAAE;IAC5C;IACA,IAAI,IAAI,CAACjB,eAAe,CAACkB,GAAG,CAACH,GAAG,CAAC,EAAE;MACjC,OAAO,IAAI,CAACf,eAAe,CAACmB,GAAG,CAACJ,GAAG,CAAC;IACtC;;IAEA;IACA,MAAMK,cAAc,GAAGJ,SAAS,CAAC,CAAC,CAC/BK,OAAO,CAAC,MAAM;MACb;MACA,IAAI,CAACrB,eAAe,CAACsB,MAAM,CAACP,GAAG,CAAC;MAChC,IAAI,IAAI,CAACb,eAAe,CAACgB,GAAG,CAACH,GAAG,CAAC,EAAE;QACjCQ,YAAY,CAAC,IAAI,CAACrB,eAAe,CAACiB,GAAG,CAACJ,GAAG,CAAC,CAAC;QAC3C,IAAI,CAACb,eAAe,CAACoB,MAAM,CAACP,GAAG,CAAC;MAClC;IACF,CAAC,CAAC;IAEJ,IAAI,CAACf,eAAe,CAACwB,GAAG,CAACT,GAAG,EAAEK,cAAc,CAAC;;IAE7C;IACA,MAAMK,SAAS,GAAGC,UAAU,CAAC,MAAM;MACjC,IAAI,CAAC1B,eAAe,CAACsB,MAAM,CAACP,GAAG,CAAC;MAChC,IAAI,CAACb,eAAe,CAACoB,MAAM,CAACP,GAAG,CAAC;IAClC,CAAC,EAAEE,OAAO,CAAC;IAEX,IAAI,CAACf,eAAe,CAACsB,GAAG,CAACT,GAAG,EAAEU,SAAS,CAAC;IAExC,OAAOL,cAAc;EACvB;;EAEA;AACF;AACA;EACEO,KAAKA,CAAA,EAAG;IACN;IACA,IAAI,CAACzB,eAAe,CAAC0B,OAAO,CAACH,SAAS,IAAIF,YAAY,CAACE,SAAS,CAAC,CAAC;;IAElE;IACA,IAAI,CAACzB,eAAe,CAAC2B,KAAK,CAAC,CAAC;IAC5B,IAAI,CAACzB,eAAe,CAACyB,KAAK,CAAC,CAAC;EAC9B;;EAEA;AACF;AACA;AACA;AACA;EACEE,SAASA,CAACd,GAAG,EAAE;IACb,OAAO,IAAI,CAACf,eAAe,CAACkB,GAAG,CAACH,GAAG,CAAC;EACtC;AACF;;AAEA;AACA,OAAO,MAAMe,oBAAoB,GAAG,IAAIhC,oBAAoB,CAAC,CAAC;AAC9D,eAAegC,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}