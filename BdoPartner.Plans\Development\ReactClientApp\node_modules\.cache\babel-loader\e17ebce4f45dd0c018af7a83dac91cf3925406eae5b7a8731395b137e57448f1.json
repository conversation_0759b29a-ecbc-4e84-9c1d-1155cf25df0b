{"ast": null, "code": "var _jsxFileName = \"C:\\\\git\\\\Partner-Site\\\\BdoPartner.Plans\\\\Development\\\\ReactClientApp\\\\src\\\\components\\\\base\\\\navbar.jsx\",\n  _s = $RefreshSig$();\nimport React, { useContext, useEffect } from \"react\";\nimport { <PERSON><PERSON><PERSON> } from \"primereact/menubar\";\nimport { Dropdown } from \"primereact/dropdown\";\nimport { Button } from \"primereact/button\";\nimport { AuthContext } from \"../../core/auth/components/authProvider\";\nimport APP_CONFIG from \"../../core/config/appConfig\";\nimport { loadingService } from \"../../core/loading/loadingService\";\nimport { useTranslation } from \"react-i18next\";\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { bindActionCreators } from \"redux\";\nimport { actionCreators } from \"../../redux/actionCreatorsExport\";\nimport { Role } from \"../../core/enumertions/role\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport const NavBar = () => {\n  _s();\n  var _user$roles;\n  //\n  // Try to get selected language code from the global redux store.\n  //\n  const state = useSelector(state => state);\n  const dispatch = useDispatch();\n  //\n  // Binding Redux custom action creators.\n  // Work for update selected language code.\n  //\n  const {\n    setLanguage\n  } = bindActionCreators(actionCreators, dispatch);\n  const authService = useContext(AuthContext);\n  const user = authService.getUser();\n  const isAuthorized = authService.isAuthenticated();\n  const {\n    t,\n    i18n\n  } = useTranslation();\n  useEffect(() => {\n    if (state.language && state.language.length > 0) {\n      i18n.changeLanguage(state.language);\n    }\n  }, [i18n, state]);\n\n  // Check if user has PPAdministrator role\n  const isSystemAdmin = isAuthorized && (user === null || user === void 0 ? void 0 : (_user$roles = user.roles) === null || _user$roles === void 0 ? void 0 : _user$roles.includes(Role.PPAdministrator));\n  const items = [{\n    label: t(\"Nav.Home\"),\n    icon: \"pi pi-fw pi-home\",\n    url: `${APP_CONFIG.basePath}/home`,\n    visible: true // It is custom property. Work for show/hide menu items based on user authentication.\n  }, {\n    label: \"Admin\",\n    icon: \"pi pi-fw pi-cog\",\n    visible: isSystemAdmin,\n    items: [{\n      label: \"Partner Reference Data\",\n      icon: \"pi pi-fw pi-users\",\n      url: `${APP_CONFIG.basePath}/admin/partner-reference-data-management`\n    }, {\n      label: \"Partner Annual Plans\",\n      icon: \"pi pi-fw pi-calendar\",\n      url: `${APP_CONFIG.basePath}/admin/partner-annual-plans`\n    }, {\n      label: \"Questionnaire Management\",\n      icon: \"pi pi-fw pi-file-edit\",\n      url: `${APP_CONFIG.basePath}/admin/questionnaire-management`\n    }]\n  }];\n  const languages = [{\n    label: t(\"Nav.English\"),\n    value: \"en\"\n  }, {\n    label: t(\"Nav.French\"),\n    value: \"fr\"\n  }];\n  const login = () => {\n    // Show progress spinner.\n    loadingService.httpRequestSent();\n    authService.signin();\n  };\n  const logout = () => {\n    // Show progress spinner.\n    loadingService.httpRequestSent();\n    authService.signout();\n  };\n  const languageDropdown = /*#__PURE__*/_jsxDEV(Dropdown, {\n    value: state.language,\n    options: languages,\n    onChange: e => {\n      i18n.changeLanguage(e.value);\n      setLanguage(e.value);\n    },\n    placeholder: \"Select a language\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 89,\n    columnNumber: 5\n  }, this);\n  const start = /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"navbar-brand-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"img\", {\n      alt: \"BDO logo\",\n      src: `${APP_CONFIG.basePath}/logo.png`,\n      onError: e => e.target.src = `/images/BDO.png`\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 102,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"navbar-brand-text\",\n      children: \"Partner Planning\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 101,\n    columnNumber: 5\n  }, this);\n  const end = /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [isAuthorized && /*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"p-2\",\n      children: user.displayName\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 24\n    }, this), isAuthorized &&\n    /*#__PURE__*/\n    // it is logout button.\n    _jsxDEV(Button, {\n      label: \"Logout\",\n      rounded: true,\n      className: \"p-button-gray p-button-rounded\",\n      title: t(\"Nav.Logout\"),\n      onClick: logout\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 9\n    }, this), !isAuthorized &&\n    /*#__PURE__*/\n    // It is login button.\n    _jsxDEV(Button, {\n      label: \"Login\",\n      rounded: true,\n      className: \"p-button-gray\",\n      title: t(\"Nav.Login\"),\n      onClick: login\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"navbar-wrapper\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"navbar-container\",\n        children: /*#__PURE__*/_jsxDEV(Menubar, {\n          model: items.filter(i => i.visible === true),\n          start: start,\n          end: end\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 7\n    }, this)\n  }, void 0, false);\n};\n_s(NavBar, \"j1l3VbCeP4I/A2EaU/qv2r2wHgw=\", false, function () {\n  return [useSelector, useDispatch, useTranslation];\n});\n_c = NavBar;\nvar _c;\n$RefreshReg$(_c, \"NavBar\");", "map": {"version": 3, "names": ["React", "useContext", "useEffect", "Men<PERSON><PERSON>", "Dropdown", "<PERSON><PERSON>", "AuthContext", "APP_CONFIG", "loadingService", "useTranslation", "useSelector", "useDispatch", "bindActionCreators", "actionCreators", "Role", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "NavBar", "_s", "_user$roles", "state", "dispatch", "setLanguage", "authService", "user", "getUser", "isAuthorized", "isAuthenticated", "t", "i18n", "language", "length", "changeLanguage", "isSystemAdmin", "roles", "includes", "PPAdministrator", "items", "label", "icon", "url", "basePath", "visible", "languages", "value", "login", "httpRequestSent", "signin", "logout", "signout", "languageDropdown", "options", "onChange", "e", "placeholder", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "start", "className", "children", "alt", "src", "onError", "target", "end", "displayName", "rounded", "title", "onClick", "model", "filter", "i", "_c", "$RefreshReg$"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/components/base/navbar.jsx"], "sourcesContent": ["import React, { useContext, useEffect } from \"react\";\r\nimport { <PERSON><PERSON><PERSON> } from \"primereact/menubar\";\r\nimport { Dropdown } from \"primereact/dropdown\";\r\nimport { <PERSON><PERSON> } from \"primereact/button\";\r\nimport { AuthContext } from \"../../core/auth/components/authProvider\";\r\nimport APP_CONFIG from \"../../core/config/appConfig\";\r\nimport { loadingService } from \"../../core/loading/loadingService\";\r\nimport { useTranslation } from \"react-i18next\";\r\nimport { useSelector, useDispatch } from \"react-redux\";\r\nimport { bindActionCreators } from \"redux\";\r\nimport { actionCreators } from \"../../redux/actionCreatorsExport\";\r\nimport { Role } from \"../../core/enumertions/role\";\r\n\r\nexport const NavBar = () => {\r\n  //\r\n  // Try to get selected language code from the global redux store.\r\n  //\r\n  const state = useSelector((state) => state);\r\n  const dispatch = useDispatch();\r\n  //\r\n  // Binding Redux custom action creators.\r\n  // Work for update selected language code.\r\n  //\r\n  const { setLanguage } = bindActionCreators(actionCreators, dispatch);\r\n\r\n  const authService = useContext(AuthContext);\r\n  const user = authService.getUser();\r\n  const isAuthorized = authService.isAuthenticated();\r\n  const { t, i18n } = useTranslation();\r\n\r\n  useEffect(() => {\r\n    if (state.language && state.language.length > 0) {\r\n      i18n.changeLanguage(state.language);\r\n    }\r\n  }, [i18n, state]);\r\n\r\n  // Check if user has PPAdministrator role\r\n  const isSystemAdmin = isAuthorized && user?.roles?.includes(Role.PPAdministrator);\r\n\r\n  const items = [\r\n    {\r\n      label: t(\"Nav.Home\"),\r\n      icon: \"pi pi-fw pi-home\",\r\n      url: `${APP_CONFIG.basePath}/home`,\r\n      visible: true, // It is custom property. Work for show/hide menu items based on user authentication.\r\n    },\r\n    {\r\n      label: \"Admin\",\r\n      icon: \"pi pi-fw pi-cog\",\r\n      visible: isSystemAdmin,\r\n      items: [\r\n        {\r\n          label: \"Partner Reference Data\",\r\n          icon: \"pi pi-fw pi-users\",\r\n          url: `${APP_CONFIG.basePath}/admin/partner-reference-data-management`,\r\n        },\r\n        {\r\n          label: \"Partner Annual Plans\",\r\n          icon: \"pi pi-fw pi-calendar\",\r\n          url: `${APP_CONFIG.basePath}/admin/partner-annual-plans`,\r\n        },\r\n        {\r\n          label: \"Questionnaire Management\",\r\n          icon: \"pi pi-fw pi-file-edit\",\r\n          url: `${APP_CONFIG.basePath}/admin/questionnaire-management`,\r\n        },\r\n      ],\r\n    },\r\n  ];\r\n\r\n  const languages = [\r\n    { label: t(\"Nav.English\"), value: \"en\" },\r\n    { label: t(\"Nav.French\"), value: \"fr\" },\r\n  ];\r\n\r\n  const login = () => {\r\n    // Show progress spinner.\r\n    loadingService.httpRequestSent();\r\n    authService.signin();\r\n  };\r\n\r\n  const logout = () => {\r\n    // Show progress spinner.\r\n    loadingService.httpRequestSent();\r\n    authService.signout();\r\n  };\r\n\r\n  const languageDropdown = (\r\n    <Dropdown\r\n      value={state.language}\r\n      options={languages}\r\n      onChange={(e) => {\r\n        i18n.changeLanguage(e.value);\r\n        setLanguage(e.value);\r\n      }}\r\n      placeholder=\"Select a language\"\r\n    />\r\n  );\r\n\r\n  const start = (\r\n    <div className=\"navbar-brand-container\">\r\n      <img\r\n        alt=\"BDO logo\"\r\n        src={`${APP_CONFIG.basePath}/logo.png`}\r\n        onError={(e) => (e.target.src = `/images/BDO.png`)}\r\n      />\r\n      <span className=\"navbar-brand-text\">\r\n        Partner Planning\r\n      </span>\r\n    </div>\r\n  );\r\n  const end = (\r\n    <>\r\n      {isAuthorized && <span className=\"p-2\">{user.displayName}</span>}\r\n      {/* {languageDropdown} */}\r\n      {isAuthorized && (\r\n        // it is logout button.\r\n        <Button label=\"Logout\" rounded className=\"p-button-gray p-button-rounded\"\r\n          title={t(\"Nav.Logout\")}\r\n          onClick={logout}\r\n        ></Button>\r\n      )}\r\n      {!isAuthorized && (\r\n        // It is login button.\r\n        <Button label=\"Login\" rounded className=\"p-button-gray\"\r\n          title={t(\"Nav.Login\")}\r\n          onClick={login}\r\n        ></Button>\r\n      )}\r\n    </>\r\n  );\r\n\r\n  return (\r\n    <>\r\n      <div className=\"navbar-wrapper\">\r\n        <div className=\"navbar-container\">\r\n          <Menubar\r\n            model={items.filter((i) => i.visible === true)}\r\n            start={start}\r\n            end={end}\r\n          ></Menubar>\r\n        </div>\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AACpD,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,WAAW,QAAQ,yCAAyC;AACrE,OAAOC,UAAU,MAAM,6BAA6B;AACpD,SAASC,cAAc,QAAQ,mCAAmC;AAClE,SAASC,cAAc,QAAQ,eAAe;AAC9C,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,kBAAkB,QAAQ,OAAO;AAC1C,SAASC,cAAc,QAAQ,kCAAkC;AACjE,SAASC,IAAI,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEnD,OAAO,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,WAAA;EAC1B;EACA;EACA;EACA,MAAMC,KAAK,GAAGZ,WAAW,CAAEY,KAAK,IAAKA,KAAK,CAAC;EAC3C,MAAMC,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAC9B;EACA;EACA;EACA;EACA,MAAM;IAAEa;EAAY,CAAC,GAAGZ,kBAAkB,CAACC,cAAc,EAAEU,QAAQ,CAAC;EAEpE,MAAME,WAAW,GAAGxB,UAAU,CAACK,WAAW,CAAC;EAC3C,MAAMoB,IAAI,GAAGD,WAAW,CAACE,OAAO,CAAC,CAAC;EAClC,MAAMC,YAAY,GAAGH,WAAW,CAACI,eAAe,CAAC,CAAC;EAClD,MAAM;IAAEC,CAAC;IAAEC;EAAK,CAAC,GAAGtB,cAAc,CAAC,CAAC;EAEpCP,SAAS,CAAC,MAAM;IACd,IAAIoB,KAAK,CAACU,QAAQ,IAAIV,KAAK,CAACU,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;MAC/CF,IAAI,CAACG,cAAc,CAACZ,KAAK,CAACU,QAAQ,CAAC;IACrC;EACF,CAAC,EAAE,CAACD,IAAI,EAAET,KAAK,CAAC,CAAC;;EAEjB;EACA,MAAMa,aAAa,GAAGP,YAAY,KAAIF,IAAI,aAAJA,IAAI,wBAAAL,WAAA,GAAJK,IAAI,CAAEU,KAAK,cAAAf,WAAA,uBAAXA,WAAA,CAAagB,QAAQ,CAACvB,IAAI,CAACwB,eAAe,CAAC;EAEjF,MAAMC,KAAK,GAAG,CACZ;IACEC,KAAK,EAAEV,CAAC,CAAC,UAAU,CAAC;IACpBW,IAAI,EAAE,kBAAkB;IACxBC,GAAG,EAAE,GAAGnC,UAAU,CAACoC,QAAQ,OAAO;IAClCC,OAAO,EAAE,IAAI,CAAE;EACjB,CAAC,EACD;IACEJ,KAAK,EAAE,OAAO;IACdC,IAAI,EAAE,iBAAiB;IACvBG,OAAO,EAAET,aAAa;IACtBI,KAAK,EAAE,CACL;MACEC,KAAK,EAAE,wBAAwB;MAC/BC,IAAI,EAAE,mBAAmB;MACzBC,GAAG,EAAE,GAAGnC,UAAU,CAACoC,QAAQ;IAC7B,CAAC,EACD;MACEH,KAAK,EAAE,sBAAsB;MAC7BC,IAAI,EAAE,sBAAsB;MAC5BC,GAAG,EAAE,GAAGnC,UAAU,CAACoC,QAAQ;IAC7B,CAAC,EACD;MACEH,KAAK,EAAE,0BAA0B;MACjCC,IAAI,EAAE,uBAAuB;MAC7BC,GAAG,EAAE,GAAGnC,UAAU,CAACoC,QAAQ;IAC7B,CAAC;EAEL,CAAC,CACF;EAED,MAAME,SAAS,GAAG,CAChB;IAAEL,KAAK,EAAEV,CAAC,CAAC,aAAa,CAAC;IAAEgB,KAAK,EAAE;EAAK,CAAC,EACxC;IAAEN,KAAK,EAAEV,CAAC,CAAC,YAAY,CAAC;IAAEgB,KAAK,EAAE;EAAK,CAAC,CACxC;EAED,MAAMC,KAAK,GAAGA,CAAA,KAAM;IAClB;IACAvC,cAAc,CAACwC,eAAe,CAAC,CAAC;IAChCvB,WAAW,CAACwB,MAAM,CAAC,CAAC;EACtB,CAAC;EAED,MAAMC,MAAM,GAAGA,CAAA,KAAM;IACnB;IACA1C,cAAc,CAACwC,eAAe,CAAC,CAAC;IAChCvB,WAAW,CAAC0B,OAAO,CAAC,CAAC;EACvB,CAAC;EAED,MAAMC,gBAAgB,gBACpBpC,OAAA,CAACZ,QAAQ;IACP0C,KAAK,EAAExB,KAAK,CAACU,QAAS;IACtBqB,OAAO,EAAER,SAAU;IACnBS,QAAQ,EAAGC,CAAC,IAAK;MACfxB,IAAI,CAACG,cAAc,CAACqB,CAAC,CAACT,KAAK,CAAC;MAC5BtB,WAAW,CAAC+B,CAAC,CAACT,KAAK,CAAC;IACtB,CAAE;IACFU,WAAW,EAAC;EAAmB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAChC,CACF;EAED,MAAMC,KAAK,gBACT7C,OAAA;IAAK8C,SAAS,EAAC,wBAAwB;IAAAC,QAAA,gBACrC/C,OAAA;MACEgD,GAAG,EAAC,UAAU;MACdC,GAAG,EAAE,GAAG1D,UAAU,CAACoC,QAAQ,WAAY;MACvCuB,OAAO,EAAGX,CAAC,IAAMA,CAAC,CAACY,MAAM,CAACF,GAAG,GAAG;IAAmB;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpD,CAAC,eACF5C,OAAA;MAAM8C,SAAS,EAAC,mBAAmB;MAAAC,QAAA,EAAC;IAEpC;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CACN;EACD,MAAMQ,GAAG,gBACPpD,OAAA,CAAAE,SAAA;IAAA6C,QAAA,GACGnC,YAAY,iBAAIZ,OAAA;MAAM8C,SAAS,EAAC,KAAK;MAAAC,QAAA,EAAErC,IAAI,CAAC2C;IAAW;MAAAZ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,EAE/DhC,YAAY;IAAA;IACX;IACAZ,OAAA,CAACX,MAAM;MAACmC,KAAK,EAAC,QAAQ;MAAC8B,OAAO;MAACR,SAAS,EAAC,gCAAgC;MACvES,KAAK,EAAEzC,CAAC,CAAC,YAAY,CAAE;MACvB0C,OAAO,EAAEtB;IAAO;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CACV,EACA,CAAChC,YAAY;IAAA;IACZ;IACAZ,OAAA,CAACX,MAAM;MAACmC,KAAK,EAAC,OAAO;MAAC8B,OAAO;MAACR,SAAS,EAAC,eAAe;MACrDS,KAAK,EAAEzC,CAAC,CAAC,WAAW,CAAE;MACtB0C,OAAO,EAAEzB;IAAM;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CACV;EAAA,eACD,CACH;EAED,oBACE5C,OAAA,CAAAE,SAAA;IAAA6C,QAAA,eACE/C,OAAA;MAAK8C,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC7B/C,OAAA;QAAK8C,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/B/C,OAAA,CAACb,OAAO;UACNsE,KAAK,EAAElC,KAAK,CAACmC,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAAC/B,OAAO,KAAK,IAAI,CAAE;UAC/CiB,KAAK,EAAEA,KAAM;UACbO,GAAG,EAAEA;QAAI;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC,gBACN,CAAC;AAEP,CAAC;AAACxC,EAAA,CApIWD,MAAM;EAAA,QAIHT,WAAW,EACRC,WAAW,EAURF,cAAc;AAAA;AAAAmE,EAAA,GAfvBzD,MAAM;AAAA,IAAAyD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}