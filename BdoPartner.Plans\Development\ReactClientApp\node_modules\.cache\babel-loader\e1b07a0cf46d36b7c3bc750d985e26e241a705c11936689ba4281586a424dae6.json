{"ast": null, "code": "import { filter } from './filter';\nexport function skip(count) {\n  return filter(function (_, index) {\n    return count <= index;\n  });\n}", "map": {"version": 3, "names": ["filter", "skip", "count", "_", "index"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\skip.ts"], "sourcesContent": ["import { MonoTypeOperatorFunction } from '../types';\nimport { filter } from './filter';\n\n/**\n * Returns an Observable that skips the first `count` items emitted by the source Observable.\n *\n * ![](skip.png)\n *\n * Skips the values until the sent notifications are equal or less than provided skip count. It raises\n * an error if skip count is equal or more than the actual number of emits and source raises an error.\n *\n * ## Example\n *\n * Skip the values before the emission\n *\n * ```ts\n * import { interval, skip } from 'rxjs';\n *\n * // emit every half second\n * const source = interval(500);\n * // skip the first 10 emitted values\n * const result = source.pipe(skip(10));\n *\n * result.subscribe(value => console.log(value));\n * // output: 10...11...12...13...\n * ```\n *\n * @see {@link last}\n * @see {@link skipWhile}\n * @see {@link skipUntil}\n * @see {@link skipLast}\n *\n * @param count The number of times, items emitted by source Observable should be skipped.\n * @return A function that returns an Observable that skips the first `count`\n * values emitted by the source Observable.\n */\nexport function skip<T>(count: number): MonoTypeOperatorFunction<T> {\n  return filter((_, index) => count <= index);\n}\n"], "mappings": "AACA,SAASA,MAAM,QAAQ,UAAU;AAmCjC,OAAM,SAAUC,IAAIA,CAAIC,KAAa;EACnC,OAAOF,MAAM,CAAC,UAACG,CAAC,EAAEC,KAAK;IAAK,OAAAF,KAAK,IAAIE,KAAK;EAAd,CAAc,CAAC;AAC7C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}