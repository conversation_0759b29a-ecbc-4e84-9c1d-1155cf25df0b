{"ast": null, "code": "/** Define current running application's environment.\r\n * value = \"dev_cors\" or \"prod_nocors\". \r\n * Other settings get from server side api\r\n * */\nconst APP_ENV = process.env.REACT_APP_ENV;\nconst APP_CONFIG_API = process.env.REACT_APP_CONFIG_API;\nconsole.log(\"APP_CONFIG_API\", APP_CONFIG_API);\nlet app_config = null;\nconst getConfig = function () {\n  if (app_config) return app_config;\n\n  // create a new XMLHttpRequest\n  var xhr = new XMLHttpRequest();\n  // get a callback when the server responds\n  xhr.addEventListener(\"load\", () => {\n    //console.log(\"config\", xhr.responseText);\n    app_config = JSON.parse(xhr.responseText);\n    // update the state of the component with the result here\n    console.log(xhr.responseText);\n  });\n  var domain = window.location.protocol + \"//\" + window.location.host;\n\n  //\n  // Note:\n  // As for APP_ENV = \"dev_cors\"\n  // Hardcode url as \"https://localhost:5001/api/Settings/GetIDSSettings\",\n  // since it only works for local machine development environment.\n  // Send request to web api endpoint to get react app's config settings.\n  // Note: Since React App is hosting in seperated domain (or different port number),\n  // so, here, need to access web api end point's full url.\n  // Get config settings from web api end point defined in \"REACT_APP_CONFIG_API\"\n  // Setings stay in web api's appsettings.[environment].json file, section \"Environment\".\n  //\n  // As for APP_ENV = \"prod_nocors\"\n  // Work for scenario of hosting react app under web api domain's sub path\n  // Send request to web api endpoint to get react app's config settings.\n  // Note: Since react app is hosting under web api domain sub path.\n  // so, here, it can use relative url.\n  //\n  if (APP_ENV.indexOf(\"_cors\") > 0) {\n    // For any \"**_cors\" environments, need to get web api endpoint full path from environment file.\n    // Currently, this setting only work for local development environment and corporate with environment file called \".env.development.local\".\n    xhr.open(\"GET\", APP_CONFIG_API, false);\n  } else {\n    // For any \"**_nocors\" environments, call web api endpoint with current domain.\n    // corporate with environment file called \".env.production.local\".\n    xhr.open(\"GET\", domain + APP_CONFIG_API, false);\n  }\n\n  // send the request\n  xhr.send();\n  let stillWating = true;\n  //we only wait for 30s to avoid chrome freeze\n  setTimeout(() => {\n    stillWating = false;\n  }, 30000);\n  while (!app_config && stillWating) {\n    // Holding here.\n  }\n  return app_config;\n};\nconst APP_CONFIG = getConfig();\n/**\r\n *  Contains global config settings for application associated\r\n *  to current running environment.\r\n *\r\n *  As for debugging system:\r\n *  Modify setting in env.development.local before run: npm start\r\n *\r\n *  As for build system:\r\n *  Modify setting in env.production.local before run: npm build\r\n */\n\nexport default APP_CONFIG;", "map": {"version": 3, "names": ["APP_ENV", "process", "env", "REACT_APP_ENV", "APP_CONFIG_API", "REACT_APP_CONFIG_API", "console", "log", "app_config", "getConfig", "xhr", "XMLHttpRequest", "addEventListener", "JSON", "parse", "responseText", "domain", "window", "location", "protocol", "host", "indexOf", "open", "send", "stillWating", "setTimeout", "APP_CONFIG"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/core/config/appConfig.js"], "sourcesContent": ["/** Define current running application's environment.\r\n * value = \"dev_cors\" or \"prod_nocors\". \r\n * Other settings get from server side api\r\n * */\r\nconst APP_ENV = process.env.REACT_APP_ENV;\r\nconst APP_CONFIG_API = process.env.REACT_APP_CONFIG_API;\r\n\r\nconsole.log(\"APP_CONFIG_API\", APP_CONFIG_API);\r\n\r\nlet app_config = null;\r\n\r\nconst getConfig = function () {\r\n  if (app_config) return app_config;\r\n\r\n  // create a new XMLHttpRequest\r\n  var xhr = new XMLHttpRequest();\r\n  // get a callback when the server responds\r\n  xhr.addEventListener(\"load\", () => {\r\n    //console.log(\"config\", xhr.responseText);\r\n    app_config = JSON.parse(xhr.responseText);\r\n    // update the state of the component with the result here\r\n    console.log(xhr.responseText);\r\n  });\r\n\r\n  var domain = window.location.protocol + \"//\" + window.location.host;\r\n\r\n  //\r\n  // Note:\r\n  // As for APP_ENV = \"dev_cors\"\r\n  // Hardcode url as \"https://localhost:5001/api/Settings/GetIDSSettings\",\r\n  // since it only works for local machine development environment.\r\n  // Send request to web api endpoint to get react app's config settings.\r\n  // Note: Since React App is hosting in seperated domain (or different port number),\r\n  // so, here, need to access web api end point's full url.\r\n  // Get config settings from web api end point defined in \"REACT_APP_CONFIG_API\"\r\n  // Setings stay in web api's appsettings.[environment].json file, section \"Environment\".\r\n  //\r\n  // As for APP_ENV = \"prod_nocors\"\r\n  // Work for scenario of hosting react app under web api domain's sub path\r\n  // Send request to web api endpoint to get react app's config settings.\r\n  // Note: Since react app is hosting under web api domain sub path.\r\n  // so, here, it can use relative url.\r\n  //\r\n  if (APP_ENV.indexOf(\"_cors\") > 0) {\r\n    // For any \"**_cors\" environments, need to get web api endpoint full path from environment file.\r\n    // Currently, this setting only work for local development environment and corporate with environment file called \".env.development.local\".\r\n    xhr.open(\"GET\", APP_CONFIG_API, false);\r\n  } else {\r\n    // For any \"**_nocors\" environments, call web api endpoint with current domain.\r\n    // corporate with environment file called \".env.production.local\".\r\n    xhr.open(\"GET\", domain + APP_CONFIG_API, false);\r\n  }\r\n\r\n  // send the request\r\n  xhr.send();\r\n\r\n  let stillWating = true;\r\n  //we only wait for 30s to avoid chrome freeze\r\n  setTimeout(() => {\r\n    stillWating = false;\r\n  }, 30000);\r\n\r\n  while (!app_config && stillWating) {\r\n    // Holding here.\r\n  }\r\n\r\n  return app_config;\r\n};\r\n\r\nconst APP_CONFIG = getConfig();\r\n/**\r\n *  Contains global config settings for application associated\r\n *  to current running environment.\r\n *\r\n *  As for debugging system:\r\n *  Modify setting in env.development.local before run: npm start\r\n *\r\n *  As for build system:\r\n *  Modify setting in env.production.local before run: npm build\r\n */\r\n\r\nexport default APP_CONFIG;\r\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,MAAMA,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,aAAa;AACzC,MAAMC,cAAc,GAAGH,OAAO,CAACC,GAAG,CAACG,oBAAoB;AAEvDC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEH,cAAc,CAAC;AAE7C,IAAII,UAAU,GAAG,IAAI;AAErB,MAAMC,SAAS,GAAG,SAAAA,CAAA,EAAY;EAC5B,IAAID,UAAU,EAAE,OAAOA,UAAU;;EAEjC;EACA,IAAIE,GAAG,GAAG,IAAIC,cAAc,CAAC,CAAC;EAC9B;EACAD,GAAG,CAACE,gBAAgB,CAAC,MAAM,EAAE,MAAM;IACjC;IACAJ,UAAU,GAAGK,IAAI,CAACC,KAAK,CAACJ,GAAG,CAACK,YAAY,CAAC;IACzC;IACAT,OAAO,CAACC,GAAG,CAACG,GAAG,CAACK,YAAY,CAAC;EAC/B,CAAC,CAAC;EAEF,IAAIC,MAAM,GAAGC,MAAM,CAACC,QAAQ,CAACC,QAAQ,GAAG,IAAI,GAAGF,MAAM,CAACC,QAAQ,CAACE,IAAI;;EAEnE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAIpB,OAAO,CAACqB,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;IAChC;IACA;IACAX,GAAG,CAACY,IAAI,CAAC,KAAK,EAAElB,cAAc,EAAE,KAAK,CAAC;EACxC,CAAC,MAAM;IACL;IACA;IACAM,GAAG,CAACY,IAAI,CAAC,KAAK,EAAEN,MAAM,GAAGZ,cAAc,EAAE,KAAK,CAAC;EACjD;;EAEA;EACAM,GAAG,CAACa,IAAI,CAAC,CAAC;EAEV,IAAIC,WAAW,GAAG,IAAI;EACtB;EACAC,UAAU,CAAC,MAAM;IACfD,WAAW,GAAG,KAAK;EACrB,CAAC,EAAE,KAAK,CAAC;EAET,OAAO,CAAChB,UAAU,IAAIgB,WAAW,EAAE;IACjC;EAAA;EAGF,OAAOhB,UAAU;AACnB,CAAC;AAED,MAAMkB,UAAU,GAAGjB,SAAS,CAAC,CAAC;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAeiB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}