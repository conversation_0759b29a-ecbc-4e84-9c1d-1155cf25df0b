{"ast": null, "code": "import { operate } from '../util/lift';\nimport { noop } from '../util/noop';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nexport function debounce(durationSelector) {\n  return operate(function (source, subscriber) {\n    var hasValue = false;\n    var lastValue = null;\n    var durationSubscriber = null;\n    var emit = function () {\n      durationSubscriber === null || durationSubscriber === void 0 ? void 0 : durationSubscriber.unsubscribe();\n      durationSubscriber = null;\n      if (hasValue) {\n        hasValue = false;\n        var value = lastValue;\n        lastValue = null;\n        subscriber.next(value);\n      }\n    };\n    source.subscribe(createOperatorSubscriber(subscriber, function (value) {\n      durationSubscriber === null || durationSubscriber === void 0 ? void 0 : durationSubscriber.unsubscribe();\n      hasValue = true;\n      lastValue = value;\n      durationSubscriber = createOperatorSubscriber(subscriber, emit, noop);\n      innerFrom(durationSelector(value)).subscribe(durationSubscriber);\n    }, function () {\n      emit();\n      subscriber.complete();\n    }, undefined, function () {\n      lastValue = durationSubscriber = null;\n    }));\n  });\n}", "map": {"version": 3, "names": ["operate", "noop", "createOperatorSubscriber", "innerFrom", "debounce", "durationSelector", "source", "subscriber", "hasValue", "lastValue", "durationSubscriber", "emit", "unsubscribe", "value", "next", "subscribe", "complete", "undefined"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\debounce.ts"], "sourcesContent": ["import { Subscriber } from '../Subscriber';\nimport { MonoTypeOperatorFunction, ObservableInput } from '../types';\nimport { operate } from '../util/lift';\nimport { noop } from '../util/noop';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\n\n/**\n * Emits a notification from the source Observable only after a particular time span\n * determined by another Observable has passed without another source emission.\n *\n * <span class=\"informal\">It's like {@link debounceTime}, but the time span of\n * emission silence is determined by a second Observable.</span>\n *\n * ![](debounce.svg)\n *\n * `debounce` delays notifications emitted by the source Observable, but drops previous\n * pending delayed emissions if a new notification arrives on the source Observable.\n * This operator keeps track of the most recent notification from the source\n * Observable, and spawns a duration Observable by calling the\n * `durationSelector` function. The notification is emitted only when the duration\n * Observable emits a next notification, and if no other notification was emitted on\n * the source Observable since the duration Observable was spawned. If a new\n * notification appears before the duration Observable emits, the previous notification will\n * not be emitted and a new duration is scheduled from `durationSelector` is scheduled.\n * If the completing event happens during the scheduled duration the last cached notification\n * is emitted before the completion event is forwarded to the output observable.\n * If the error event happens during the scheduled duration or after it only the error event is\n * forwarded to the output observable. The cache notification is not emitted in this case.\n *\n * Like {@link debounceTime}, this is a rate-limiting operator, and also a\n * delay-like operator since output emissions do not necessarily occur at the\n * same time as they did on the source Observable.\n *\n * ## Example\n *\n * Emit the most recent click after a burst of clicks\n *\n * ```ts\n * import { fromEvent, scan, debounce, interval } from 'rxjs';\n *\n * const clicks = fromEvent(document, 'click');\n * const result = clicks.pipe(\n *   scan(i => ++i, 1),\n *   debounce(i => interval(200 * i))\n * );\n * result.subscribe(x => console.log(x));\n * ```\n *\n * @see {@link audit}\n * @see {@link auditTime}\n * @see {@link debounceTime}\n * @see {@link delay}\n * @see {@link sample}\n * @see {@link sampleTime}\n * @see {@link throttle}\n * @see {@link throttleTime}\n *\n * @param durationSelector A function\n * that receives a value from the source Observable, for computing the timeout\n * duration for each source value, returned as an Observable or a Promise.\n * @return A function that returns an Observable that delays the emissions of\n * the source Observable by the specified duration Observable returned by\n * `durationSelector`, and may drop some values if they occur too frequently.\n */\nexport function debounce<T>(durationSelector: (value: T) => ObservableInput<any>): MonoTypeOperatorFunction<T> {\n  return operate((source, subscriber) => {\n    let hasValue = false;\n    let lastValue: T | null = null;\n    // The subscriber/subscription for the current debounce, if there is one.\n    let durationSubscriber: Subscriber<any> | null = null;\n\n    const emit = () => {\n      // Unsubscribe any current debounce subscription we have,\n      // we only cared about the first notification from it, and we\n      // want to clean that subscription up as soon as possible.\n      durationSubscriber?.unsubscribe();\n      durationSubscriber = null;\n      if (hasValue) {\n        // We have a value! Free up memory first, then emit the value.\n        hasValue = false;\n        const value = lastValue!;\n        lastValue = null;\n        subscriber.next(value);\n      }\n    };\n\n    source.subscribe(\n      createOperatorSubscriber(\n        subscriber,\n        (value: T) => {\n          // Cancel any pending debounce duration. We don't\n          // need to null it out here yet tho, because we're just going\n          // to create another one in a few lines.\n          durationSubscriber?.unsubscribe();\n          hasValue = true;\n          lastValue = value;\n          // Capture our duration subscriber, so we can unsubscribe it when we're notified\n          // and we're going to emit the value.\n          durationSubscriber = createOperatorSubscriber(subscriber, emit, noop);\n          // Subscribe to the duration.\n          innerFrom(durationSelector(value)).subscribe(durationSubscriber);\n        },\n        () => {\n          // Source completed.\n          // Emit any pending debounced values then complete\n          emit();\n          subscriber.complete();\n        },\n        // Pass all errors through to consumer\n        undefined,\n        () => {\n          // Finalization.\n          lastValue = durationSubscriber = null;\n        }\n      )\n    );\n  });\n}\n"], "mappings": "AAEA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,IAAI,QAAQ,cAAc;AACnC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,SAASC,SAAS,QAAQ,yBAAyB;AA4DnD,OAAM,SAAUC,QAAQA,CAAIC,gBAAoD;EAC9E,OAAOL,OAAO,CAAC,UAACM,MAAM,EAAEC,UAAU;IAChC,IAAIC,QAAQ,GAAG,KAAK;IACpB,IAAIC,SAAS,GAAa,IAAI;IAE9B,IAAIC,kBAAkB,GAA2B,IAAI;IAErD,IAAMC,IAAI,GAAG,SAAAA,CAAA;MAIXD,kBAAkB,aAAlBA,kBAAkB,uBAAlBA,kBAAkB,CAAEE,WAAW,EAAE;MACjCF,kBAAkB,GAAG,IAAI;MACzB,IAAIF,QAAQ,EAAE;QAEZA,QAAQ,GAAG,KAAK;QAChB,IAAMK,KAAK,GAAGJ,SAAU;QACxBA,SAAS,GAAG,IAAI;QAChBF,UAAU,CAACO,IAAI,CAACD,KAAK,CAAC;;IAE1B,CAAC;IAEDP,MAAM,CAACS,SAAS,CACdb,wBAAwB,CACtBK,UAAU,EACV,UAACM,KAAQ;MAIPH,kBAAkB,aAAlBA,kBAAkB,uBAAlBA,kBAAkB,CAAEE,WAAW,EAAE;MACjCJ,QAAQ,GAAG,IAAI;MACfC,SAAS,GAAGI,KAAK;MAGjBH,kBAAkB,GAAGR,wBAAwB,CAACK,UAAU,EAAEI,IAAI,EAAEV,IAAI,CAAC;MAErEE,SAAS,CAACE,gBAAgB,CAACQ,KAAK,CAAC,CAAC,CAACE,SAAS,CAACL,kBAAkB,CAAC;IAClE,CAAC,EACD;MAGEC,IAAI,EAAE;MACNJ,UAAU,CAACS,QAAQ,EAAE;IACvB,CAAC,EAEDC,SAAS,EACT;MAEER,SAAS,GAAGC,kBAAkB,GAAG,IAAI;IACvC,CAAC,CACF,CACF;EACH,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}