{"ast": null, "code": "var _jsxFileName = \"C:\\\\git\\\\Partner-Site\\\\BdoPartner.Plans\\\\Development\\\\ReactClientApp\\\\src\\\\components\\\\admin\\\\CopyQuestionnaireModal.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Dialog } from 'primereact/dialog';\nimport { Button } from 'primereact/button';\nimport { Dropdown } from 'primereact/dropdown';\nimport { Message } from 'primereact/message';\n\n/**\r\n * Modal component for copying a questionnaire to a new year\r\n * @param {Object} props - Component props\r\n * @param {boolean} props.visible - Whether the modal is visible\r\n * @param {Function} props.onHide - Function to call when modal is hidden\r\n * @param {Function} props.onCopy - Function to call when copy is confirmed\r\n * @param {Object} props.questionnaire - The questionnaire to copy\r\n * @param {boolean} props.loading - Whether the copy operation is in progress\r\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const CopyQuestionnaireModal = ({\n  visible,\n  onHide,\n  onCopy,\n  questionnaire,\n  loading = false\n}) => {\n  _s();\n  const [selectedYear, setSelectedYear] = useState(null);\n  const [yearOptions, setYearOptions] = useState([]);\n\n  // Generate year options (current year + 10 years into the future)\n  useEffect(() => {\n    const currentYear = new Date().getFullYear();\n    const years = [];\n    for (let i = 0; i <= 10; i++) {\n      const year = currentYear + i;\n      years.push({\n        label: year.toString(),\n        value: year\n      });\n    }\n    setYearOptions(years);\n\n    // Set default to next year if questionnaire exists\n    if (questionnaire) {\n      const nextYear = questionnaire.year + 1;\n      setSelectedYear(nextYear);\n    }\n  }, [questionnaire]);\n  const handleCopy = () => {\n    if (selectedYear && questionnaire) {\n      onCopy(questionnaire.id, selectedYear);\n    }\n  };\n  const handleHide = () => {\n    setSelectedYear(null);\n    onHide();\n  };\n  const isValidSelection = selectedYear && questionnaire && selectedYear !== questionnaire.year;\n  const footer = /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Button, {\n      label: \"Cancel\",\n      icon: \"pi pi-times\",\n      onClick: handleHide,\n      className: \"p-button-text\",\n      disabled: loading\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      label: \"Copy\",\n      icon: \"pi pi-copy\",\n      onClick: handleCopy,\n      className: \"action\",\n      disabled: !isValidSelection || loading,\n      loading: loading\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 56,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    header: \"Copy Questionnaire\",\n    visible: visible,\n    style: {\n      width: \"450px\"\n    },\n    onHide: handleHide,\n    footer: footer,\n    modal: true,\n    closable: !loading,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-fluid\",\n      children: [questionnaire && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Source Questionnaire:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 15\n          }, this), \" \", questionnaire.name]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Current Year:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 15\n          }, this), \" \", questionnaire.year]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"field\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"targetYear\",\n          className: \"block mb-2\",\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Copy to Year:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n          id: \"targetYear\",\n          value: selectedYear,\n          options: yearOptions,\n          onChange: e => setSelectedYear(e.value),\n          placeholder: \"Select target year\",\n          disabled: loading,\n          className: \"w-full\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this), selectedYear && questionnaire && selectedYear === questionnaire.year && /*#__PURE__*/_jsxDEV(Message, {\n        severity: \"warn\",\n        text: \"Please select a different year than the current questionnaire year.\",\n        className: \"mt-2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-3\",\n        children: /*#__PURE__*/_jsxDEV(Message, {\n          severity: \"info\",\n          text: \"The copied questionnaire will be created with Draft status and can be modified before publishing.\",\n          className: \"mt-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 76,\n    columnNumber: 5\n  }, this);\n};\n_s(CopyQuestionnaireModal, \"ds21OWLI8MALJqqUrflzLnirUZA=\");\n_c = CopyQuestionnaireModal;\nexport default CopyQuestionnaireModal;\nvar _c;\n$RefreshReg$(_c, \"CopyQuestionnaireModal\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Dialog", "<PERSON><PERSON>", "Dropdown", "Message", "jsxDEV", "_jsxDEV", "CopyQuestionnaireModal", "visible", "onHide", "onCopy", "questionnaire", "loading", "_s", "selected<PERSON>ear", "setSelectedYear", "yearOptions", "setYearOptions", "currentYear", "Date", "getFullYear", "years", "i", "year", "push", "label", "toString", "value", "nextYear", "handleCopy", "id", "handleHide", "isValidSelection", "footer", "children", "icon", "onClick", "className", "disabled", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "header", "style", "width", "modal", "closable", "name", "htmlFor", "options", "onChange", "e", "placeholder", "severity", "text", "_c", "$RefreshReg$"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/components/admin/CopyQuestionnaireModal.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { Dialog } from 'primereact/dialog';\r\nimport { Button } from 'primereact/button';\r\nimport { Dropdown } from 'primereact/dropdown';\r\nimport { Message } from 'primereact/message';\r\n\r\n/**\r\n * Modal component for copying a questionnaire to a new year\r\n * @param {Object} props - Component props\r\n * @param {boolean} props.visible - Whether the modal is visible\r\n * @param {Function} props.onHide - Function to call when modal is hidden\r\n * @param {Function} props.onCopy - Function to call when copy is confirmed\r\n * @param {Object} props.questionnaire - The questionnaire to copy\r\n * @param {boolean} props.loading - Whether the copy operation is in progress\r\n */\r\nexport const CopyQuestionnaireModal = ({ visible, onHide, onCopy, questionnaire, loading = false }) => {\r\n  const [selectedYear, setSelectedYear] = useState(null);\r\n  const [yearOptions, setYearOptions] = useState([]);\r\n\r\n  // Generate year options (current year + 10 years into the future)\r\n  useEffect(() => {\r\n    const currentYear = new Date().getFullYear();\r\n    const years = [];\r\n    \r\n    for (let i = 0; i <= 10; i++) {\r\n      const year = currentYear + i;\r\n      years.push({\r\n        label: year.toString(),\r\n        value: year\r\n      });\r\n    }\r\n    \r\n    setYearOptions(years);\r\n    \r\n    // Set default to next year if questionnaire exists\r\n    if (questionnaire) {\r\n      const nextYear = questionnaire.year + 1;\r\n      setSelectedYear(nextYear);\r\n    }\r\n  }, [questionnaire]);\r\n\r\n  const handleCopy = () => {\r\n    if (selectedYear && questionnaire) {\r\n      onCopy(questionnaire.id, selectedYear);\r\n    }\r\n  };\r\n\r\n  const handleHide = () => {\r\n    setSelectedYear(null);\r\n    onHide();\r\n  };\r\n\r\n  const isValidSelection = selectedYear && questionnaire && selectedYear !== questionnaire.year;\r\n\r\n  const footer = (\r\n    <div>\r\n      <Button\r\n        label=\"Cancel\"\r\n        icon=\"pi pi-times\"\r\n        onClick={handleHide}\r\n        className=\"p-button-text\"\r\n        disabled={loading}\r\n      />\r\n      <Button\r\n        label=\"Copy\"\r\n        icon=\"pi pi-copy\"\r\n        onClick={handleCopy}\r\n        className=\"action\"\r\n        disabled={!isValidSelection || loading}\r\n        loading={loading}\r\n      />\r\n    </div>\r\n  );\r\n\r\n  return (\r\n    <Dialog\r\n      header=\"Copy Questionnaire\"\r\n      visible={visible}\r\n      style={{ width: \"450px\" }}\r\n      onHide={handleHide}\r\n      footer={footer}\r\n      modal\r\n      closable={!loading}\r\n    >\r\n      <div className=\"p-fluid\">\r\n        {questionnaire && (\r\n          <div className=\"mb-4\">\r\n            <div className=\"mb-2\">\r\n              <strong>Source Questionnaire:</strong> {questionnaire.name}\r\n            </div>\r\n            <div className=\"mb-3\">\r\n              <strong>Current Year:</strong> {questionnaire.year}\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        <div className=\"field\">\r\n          <label htmlFor=\"targetYear\" className=\"block mb-2\">\r\n            <strong>Copy to Year:</strong>\r\n          </label>\r\n          <Dropdown\r\n            id=\"targetYear\"\r\n            value={selectedYear}\r\n            options={yearOptions}\r\n            onChange={(e) => setSelectedYear(e.value)}\r\n            placeholder=\"Select target year\"\r\n            disabled={loading}\r\n            className=\"w-full\"\r\n          />\r\n        </div>\r\n\r\n        {selectedYear && questionnaire && selectedYear === questionnaire.year && (\r\n          <Message\r\n            severity=\"warn\"\r\n            text=\"Please select a different year than the current questionnaire year.\"\r\n            className=\"mt-2\"\r\n          />\r\n        )}\r\n\r\n        <div className=\"mt-3\">\r\n          <Message\r\n            severity=\"info\"\r\n            text=\"The copied questionnaire will be created with Draft status and can be modified before publishing.\"\r\n            className=\"mt-2\"\r\n          />\r\n        </div>\r\n      </div>\r\n    </Dialog>\r\n  );\r\n};\r\n\r\nexport default CopyQuestionnaireModal;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,OAAO,QAAQ,oBAAoB;;AAE5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA,SAAAC,MAAA,IAAAC,OAAA;AASA,OAAO,MAAMC,sBAAsB,GAAGA,CAAC;EAAEC,OAAO;EAAEC,MAAM;EAAEC,MAAM;EAAEC,aAAa;EAAEC,OAAO,GAAG;AAAM,CAAC,KAAK;EAAAC,EAAA;EACrG,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACiB,WAAW,EAAEC,cAAc,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;;EAElD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMkB,WAAW,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;IAC5C,MAAMC,KAAK,GAAG,EAAE;IAEhB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC5B,MAAMC,IAAI,GAAGL,WAAW,GAAGI,CAAC;MAC5BD,KAAK,CAACG,IAAI,CAAC;QACTC,KAAK,EAAEF,IAAI,CAACG,QAAQ,CAAC,CAAC;QACtBC,KAAK,EAAEJ;MACT,CAAC,CAAC;IACJ;IAEAN,cAAc,CAACI,KAAK,CAAC;;IAErB;IACA,IAAIV,aAAa,EAAE;MACjB,MAAMiB,QAAQ,GAAGjB,aAAa,CAACY,IAAI,GAAG,CAAC;MACvCR,eAAe,CAACa,QAAQ,CAAC;IAC3B;EACF,CAAC,EAAE,CAACjB,aAAa,CAAC,CAAC;EAEnB,MAAMkB,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAIf,YAAY,IAAIH,aAAa,EAAE;MACjCD,MAAM,CAACC,aAAa,CAACmB,EAAE,EAAEhB,YAAY,CAAC;IACxC;EACF,CAAC;EAED,MAAMiB,UAAU,GAAGA,CAAA,KAAM;IACvBhB,eAAe,CAAC,IAAI,CAAC;IACrBN,MAAM,CAAC,CAAC;EACV,CAAC;EAED,MAAMuB,gBAAgB,GAAGlB,YAAY,IAAIH,aAAa,IAAIG,YAAY,KAAKH,aAAa,CAACY,IAAI;EAE7F,MAAMU,MAAM,gBACV3B,OAAA;IAAA4B,QAAA,gBACE5B,OAAA,CAACJ,MAAM;MACLuB,KAAK,EAAC,QAAQ;MACdU,IAAI,EAAC,aAAa;MAClBC,OAAO,EAAEL,UAAW;MACpBM,SAAS,EAAC,eAAe;MACzBC,QAAQ,EAAE1B;IAAQ;MAAA2B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CAAC,eACFpC,OAAA,CAACJ,MAAM;MACLuB,KAAK,EAAC,MAAM;MACZU,IAAI,EAAC,YAAY;MACjBC,OAAO,EAAEP,UAAW;MACpBQ,SAAS,EAAC,QAAQ;MAClBC,QAAQ,EAAE,CAACN,gBAAgB,IAAIpB,OAAQ;MACvCA,OAAO,EAAEA;IAAQ;MAAA2B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CACN;EAED,oBACEpC,OAAA,CAACL,MAAM;IACL0C,MAAM,EAAC,oBAAoB;IAC3BnC,OAAO,EAAEA,OAAQ;IACjBoC,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAQ,CAAE;IAC1BpC,MAAM,EAAEsB,UAAW;IACnBE,MAAM,EAAEA,MAAO;IACfa,KAAK;IACLC,QAAQ,EAAE,CAACnC,OAAQ;IAAAsB,QAAA,eAEnB5B,OAAA;MAAK+B,SAAS,EAAC,SAAS;MAAAH,QAAA,GACrBvB,aAAa,iBACZL,OAAA;QAAK+B,SAAS,EAAC,MAAM;QAAAH,QAAA,gBACnB5B,OAAA;UAAK+B,SAAS,EAAC,MAAM;UAAAH,QAAA,gBACnB5B,OAAA;YAAA4B,QAAA,EAAQ;UAAqB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAAC/B,aAAa,CAACqC,IAAI;QAAA;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC,eACNpC,OAAA;UAAK+B,SAAS,EAAC,MAAM;UAAAH,QAAA,gBACnB5B,OAAA;YAAA4B,QAAA,EAAQ;UAAa;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAAC/B,aAAa,CAACY,IAAI;QAAA;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAEDpC,OAAA;QAAK+B,SAAS,EAAC,OAAO;QAAAH,QAAA,gBACpB5B,OAAA;UAAO2C,OAAO,EAAC,YAAY;UAACZ,SAAS,EAAC,YAAY;UAAAH,QAAA,eAChD5B,OAAA;YAAA4B,QAAA,EAAQ;UAAa;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,eACRpC,OAAA,CAACH,QAAQ;UACP2B,EAAE,EAAC,YAAY;UACfH,KAAK,EAAEb,YAAa;UACpBoC,OAAO,EAAElC,WAAY;UACrBmC,QAAQ,EAAGC,CAAC,IAAKrC,eAAe,CAACqC,CAAC,CAACzB,KAAK,CAAE;UAC1C0B,WAAW,EAAC,oBAAoB;UAChCf,QAAQ,EAAE1B,OAAQ;UAClByB,SAAS,EAAC;QAAQ;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,EAEL5B,YAAY,IAAIH,aAAa,IAAIG,YAAY,KAAKH,aAAa,CAACY,IAAI,iBACnEjB,OAAA,CAACF,OAAO;QACNkD,QAAQ,EAAC,MAAM;QACfC,IAAI,EAAC,qEAAqE;QAC1ElB,SAAS,EAAC;MAAM;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CACF,eAEDpC,OAAA;QAAK+B,SAAS,EAAC,MAAM;QAAAH,QAAA,eACnB5B,OAAA,CAACF,OAAO;UACNkD,QAAQ,EAAC,MAAM;UACfC,IAAI,EAAC,mGAAmG;UACxGlB,SAAS,EAAC;QAAM;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAAC7B,EAAA,CAlHWN,sBAAsB;AAAAiD,EAAA,GAAtBjD,sBAAsB;AAoHnC,eAAeA,sBAAsB;AAAC,IAAAiD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}