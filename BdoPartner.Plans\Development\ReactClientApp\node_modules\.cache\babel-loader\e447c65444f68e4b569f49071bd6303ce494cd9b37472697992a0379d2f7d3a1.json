{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nimport { noop } from '../util/noop';\nexport function takeUntil(notifier) {\n  return operate(function (source, subscriber) {\n    innerFrom(notifier).subscribe(createOperatorSubscriber(subscriber, function () {\n      return subscriber.complete();\n    }, noop));\n    !subscriber.closed && source.subscribe(subscriber);\n  });\n}", "map": {"version": 3, "names": ["operate", "createOperatorSubscriber", "innerFrom", "noop", "takeUntil", "notifier", "source", "subscriber", "subscribe", "complete", "closed"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\takeUntil.ts"], "sourcesContent": ["import { MonoTypeOperatorFunction, ObservableInput } from '../types';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nimport { noop } from '../util/noop';\n\n/**\n * Emits the values emitted by the source Observable until a `notifier`\n * Observable emits a value.\n *\n * <span class=\"informal\">Lets values pass until a second Observable,\n * `notifier`, emits a value. Then, it completes.</span>\n *\n * ![](takeUntil.png)\n *\n * `takeUntil` subscribes and begins mirroring the source Observable. It also\n * monitors a second Observable, `notifier` that you provide. If the `notifier`\n * emits a value, the output Observable stops mirroring the source Observable\n * and completes. If the `notifier` doesn't emit any value and completes\n * then `takeUntil` will pass all values.\n *\n * ## Example\n *\n * Tick every second until the first click happens\n *\n * ```ts\n * import { interval, fromEvent, takeUntil } from 'rxjs';\n *\n * const source = interval(1000);\n * const clicks = fromEvent(document, 'click');\n * const result = source.pipe(takeUntil(clicks));\n * result.subscribe(x => console.log(x));\n * ```\n *\n * @see {@link take}\n * @see {@link takeLast}\n * @see {@link takeWhile}\n * @see {@link skip}\n *\n * @param notifier The `ObservableInput` whose first emitted value will cause the output\n * Observable of `takeUntil` to stop emitting values from the source Observable.\n * @return A function that returns an Observable that emits the values from the\n * source Observable until `notifier` emits its first value.\n */\nexport function takeUntil<T>(notifier: ObservableInput<any>): MonoTypeOperatorFunction<T> {\n  return operate((source, subscriber) => {\n    innerFrom(notifier).subscribe(createOperatorSubscriber(subscriber, () => subscriber.complete(), noop));\n    !subscriber.closed && source.subscribe(subscriber);\n  });\n}\n"], "mappings": "AACA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,SAASC,SAAS,QAAQ,yBAAyB;AACnD,SAASC,IAAI,QAAQ,cAAc;AAwCnC,OAAM,SAAUC,SAASA,CAAIC,QAA8B;EACzD,OAAOL,OAAO,CAAC,UAACM,MAAM,EAAEC,UAAU;IAChCL,SAAS,CAACG,QAAQ,CAAC,CAACG,SAAS,CAACP,wBAAwB,CAACM,UAAU,EAAE;MAAM,OAAAA,UAAU,CAACE,QAAQ,EAAE;IAArB,CAAqB,EAAEN,IAAI,CAAC,CAAC;IACtG,CAACI,UAAU,CAACG,MAAM,IAAIJ,MAAM,CAACE,SAAS,CAACD,UAAU,CAAC;EACpD,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}