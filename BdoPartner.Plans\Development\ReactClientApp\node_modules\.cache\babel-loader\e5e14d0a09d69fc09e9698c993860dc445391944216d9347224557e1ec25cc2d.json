{"ast": null, "code": "import { Observable } from '../Observable';\nimport { iterator as Symbol_iterator } from '../symbol/iterator';\nimport { isFunction } from '../util/isFunction';\nimport { executeSchedule } from '../util/executeSchedule';\nexport function scheduleIterable(input, scheduler) {\n  return new Observable(function (subscriber) {\n    var iterator;\n    executeSchedule(subscriber, scheduler, function () {\n      iterator = input[Symbol_iterator]();\n      executeSchedule(subscriber, scheduler, function () {\n        var _a;\n        var value;\n        var done;\n        try {\n          _a = iterator.next(), value = _a.value, done = _a.done;\n        } catch (err) {\n          subscriber.error(err);\n          return;\n        }\n        if (done) {\n          subscriber.complete();\n        } else {\n          subscriber.next(value);\n        }\n      }, 0, true);\n    });\n    return function () {\n      return isFunction(iterator === null || iterator === void 0 ? void 0 : iterator.return) && iterator.return();\n    };\n  });\n}", "map": {"version": 3, "names": ["Observable", "iterator", "Symbol_iterator", "isFunction", "executeSchedule", "scheduleIterable", "input", "scheduler", "subscriber", "value", "done", "_a", "next", "err", "error", "complete", "return"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\scheduled\\scheduleIterable.ts"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { SchedulerLike } from '../types';\nimport { iterator as Symbol_iterator } from '../symbol/iterator';\nimport { isFunction } from '../util/isFunction';\nimport { executeSchedule } from '../util/executeSchedule';\n\n/**\n * Used in {@link scheduled} to create an observable from an Iterable.\n * @param input The iterable to create an observable from\n * @param scheduler The scheduler to use\n */\nexport function scheduleIterable<T>(input: Iterable<T>, scheduler: SchedulerLike) {\n  return new Observable<T>((subscriber) => {\n    let iterator: Iterator<T, T>;\n\n    // Schedule the initial creation of the iterator from\n    // the iterable. This is so the code in the iterable is\n    // not called until the scheduled job fires.\n    executeSchedule(subscriber, scheduler, () => {\n      // Create the iterator.\n      iterator = (input as any)[Symbol_iterator]();\n\n      executeSchedule(\n        subscriber,\n        scheduler,\n        () => {\n          let value: T;\n          let done: boolean | undefined;\n          try {\n            // Pull the value out of the iterator\n            ({ value, done } = iterator.next());\n          } catch (err) {\n            // We got an error while pulling from the iterator\n            subscriber.error(err);\n            return;\n          }\n\n          if (done) {\n            // If it is \"done\" we just complete. This mimics the\n            // behavior of JavaScript's `for..of` consumption of\n            // iterables, which will not emit the value from an iterator\n            // result of `{ done: true: value: 'here' }`.\n            subscriber.complete();\n          } else {\n            // The iterable is not done, emit the value.\n            subscriber.next(value);\n          }\n        },\n        0,\n        true\n      );\n    });\n\n    // During finalization, if we see this iterator has a `return` method,\n    // then we know it is a Generator, and not just an Iterator. So we call\n    // the `return()` function. This will ensure that any `finally { }` blocks\n    // inside of the generator we can hit will be hit properly.\n    return () => isFunction(iterator?.return) && iterator.return();\n  });\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAE1C,SAASC,QAAQ,IAAIC,eAAe,QAAQ,oBAAoB;AAChE,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,SAASC,eAAe,QAAQ,yBAAyB;AAOzD,OAAM,SAAUC,gBAAgBA,CAAIC,KAAkB,EAAEC,SAAwB;EAC9E,OAAO,IAAIP,UAAU,CAAI,UAACQ,UAAU;IAClC,IAAIP,QAAwB;IAK5BG,eAAe,CAACI,UAAU,EAAED,SAAS,EAAE;MAErCN,QAAQ,GAAIK,KAAa,CAACJ,eAAe,CAAC,EAAE;MAE5CE,eAAe,CACbI,UAAU,EACVD,SAAS,EACT;;QACE,IAAIE,KAAQ;QACZ,IAAIC,IAAyB;QAC7B,IAAI;UAEDC,EAAA,GAAkBV,QAAQ,CAACW,IAAI,EAAE,EAA/BH,KAAK,GAAAE,EAAA,CAAAF,KAAA,EAAEC,IAAI,GAAAC,EAAA,CAAAD,IAAA;SACf,CAAC,OAAOG,GAAG,EAAE;UAEZL,UAAU,CAACM,KAAK,CAACD,GAAG,CAAC;UACrB;;QAGF,IAAIH,IAAI,EAAE;UAKRF,UAAU,CAACO,QAAQ,EAAE;SACtB,MAAM;UAELP,UAAU,CAACI,IAAI,CAACH,KAAK,CAAC;;MAE1B,CAAC,EACD,CAAC,EACD,IAAI,CACL;IACH,CAAC,CAAC;IAMF,OAAO;MAAM,OAAAN,UAAU,CAACF,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEe,MAAM,CAAC,IAAIf,QAAQ,CAACe,MAAM,EAAE;IAAjD,CAAiD;EAChE,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}