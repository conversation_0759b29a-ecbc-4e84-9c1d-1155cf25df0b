{"ast": null, "code": "import { Observable } from '../Observable';\nimport { isFunction } from './isFunction';\nexport function isObservable(obj) {\n  return !!obj && (obj instanceof Observable || isFunction(obj.lift) && isFunction(obj.subscribe));\n}", "map": {"version": 3, "names": ["Observable", "isFunction", "isObservable", "obj", "lift", "subscribe"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\util\\isObservable.ts"], "sourcesContent": ["/** prettier */\nimport { Observable } from '../Observable';\nimport { isFunction } from './isFunction';\n\n/**\n * Tests to see if the object is an RxJS {@link Observable}\n * @param obj the object to test\n */\nexport function isObservable(obj: any): obj is Observable<unknown> {\n  // The !! is to ensure that this publicly exposed function returns\n  // `false` if something like `null` or `0` is passed.\n  return !!obj && (obj instanceof Observable || (isFunction(obj.lift) && isFunction(obj.subscribe)));\n}\n"], "mappings": "AACA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,UAAU,QAAQ,cAAc;AAMzC,OAAM,SAAUC,YAAYA,CAACC,GAAQ;EAGnC,OAAO,CAAC,CAACA,GAAG,KAAKA,GAAG,YAAYH,UAAU,IAAKC,UAAU,CAACE,GAAG,CAACC,IAAI,CAAC,IAAIH,UAAU,CAACE,GAAG,CAACE,SAAS,CAAE,CAAC;AACpG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}