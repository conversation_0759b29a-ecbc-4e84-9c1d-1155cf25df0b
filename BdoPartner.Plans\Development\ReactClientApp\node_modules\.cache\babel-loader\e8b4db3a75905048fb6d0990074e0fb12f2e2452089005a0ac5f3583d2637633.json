{"ast": null, "code": "export var isArrayLike = function (x) {\n  return x && typeof x.length === 'number' && typeof x !== 'function';\n};", "map": {"version": 3, "names": ["isArrayLike", "x", "length"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\util\\isArrayLike.ts"], "sourcesContent": ["export const isArrayLike = (<T>(x: any): x is ArrayLike<T> => x && typeof x.length === 'number' && typeof x !== 'function');"], "mappings": "AAAA,OAAO,IAAMA,WAAW,GAAI,SAAAA,CAAIC,CAAM;EAAwB,OAAAA,CAAC,IAAI,OAAOA,CAAC,CAACC,MAAM,KAAK,QAAQ,IAAI,OAAOD,CAAC,KAAK,UAAU;AAA5D,CAA6D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}