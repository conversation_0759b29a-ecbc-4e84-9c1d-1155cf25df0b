{"ast": null, "code": "import { createErrorClass } from './createErrorClass';\nexport var ArgumentOutOfRangeError = createErrorClass(function (_super) {\n  return function ArgumentOutOfRangeErrorImpl() {\n    _super(this);\n    this.name = 'ArgumentOutOfRangeError';\n    this.message = 'argument out of range';\n  };\n});", "map": {"version": 3, "names": ["createErrorClass", "ArgumentOutOfRangeError", "_super", "ArgumentOutOfRangeErrorImpl", "name", "message"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\util\\ArgumentOutOfRangeError.ts"], "sourcesContent": ["import { createErrorClass } from './createErrorClass';\n\nexport interface ArgumentOutOfRangeError extends Error {}\n\nexport interface ArgumentOutOfRangeErrorCtor {\n  /**\n   * @deprecated Internal implementation detail. Do not construct error instances.\n   * Cannot be tagged as internal: https://github.com/ReactiveX/rxjs/issues/6269\n   */\n  new (): ArgumentOutOfRangeError;\n}\n\n/**\n * An error thrown when an element was queried at a certain index of an\n * Observable, but no such index or position exists in that sequence.\n *\n * @see {@link elementAt}\n * @see {@link take}\n * @see {@link takeLast}\n */\nexport const ArgumentOutOfRangeError: ArgumentOutOfRangeErrorCtor = createErrorClass(\n  (_super) =>\n    function ArgumentOutOfRangeErrorImpl(this: any) {\n      _super(this);\n      this.name = 'ArgumentOutOfRangeError';\n      this.message = 'argument out of range';\n    }\n);\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,oBAAoB;AAoBrD,OAAO,IAAMC,uBAAuB,GAAgCD,gBAAgB,CAClF,UAACE,MAAM;EACL,gBAASC,2BAA2BA,CAAA;IAClCD,MAAM,CAAC,IAAI,CAAC;IACZ,IAAI,CAACE,IAAI,GAAG,yBAAyB;IACrC,IAAI,CAACC,OAAO,GAAG,uBAAuB;EACxC,CAAC;AAJD,CAIC,CACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}