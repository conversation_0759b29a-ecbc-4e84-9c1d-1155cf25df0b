{"ast": null, "code": "// Save global object in a variable\nvar __global__ = typeof globalThis !== 'undefined' && globalThis || typeof self !== 'undefined' && self || typeof global !== 'undefined' && global;\n// Create an object that extends from __global__ without the fetch function\nvar __globalThis__ = function () {\n  function F() {\n    this.fetch = false;\n    this.DOMException = __global__.DOMException;\n  }\n  F.prototype = __global__; // Needed for feature detection on whatwg-fetch's code\n  return new F();\n}();\n// Wraps whatwg-fetch with a function scope to hijack the global object\n// \"globalThis\" that's going to be patched\n(function (globalThis) {\n  var irrelevant = function (exports) {\n    var global = typeof globalThis !== 'undefined' && globalThis || typeof self !== 'undefined' && self || typeof global !== 'undefined' && global;\n    var support = {\n      searchParams: 'URLSearchParams' in global,\n      iterable: 'Symbol' in global && 'iterator' in Symbol,\n      blob: 'FileReader' in global && 'Blob' in global && function () {\n        try {\n          new Blob();\n          return true;\n        } catch (e) {\n          return false;\n        }\n      }(),\n      formData: 'FormData' in global,\n      arrayBuffer: 'ArrayBuffer' in global\n    };\n    function isDataView(obj) {\n      return obj && DataView.prototype.isPrototypeOf(obj);\n    }\n    if (support.arrayBuffer) {\n      var viewClasses = ['[object Int8Array]', '[object Uint8Array]', '[object Uint8ClampedArray]', '[object Int16Array]', '[object Uint16Array]', '[object Int32Array]', '[object Uint32Array]', '[object Float32Array]', '[object Float64Array]'];\n      var isArrayBufferView = ArrayBuffer.isView || function (obj) {\n        return obj && viewClasses.indexOf(Object.prototype.toString.call(obj)) > -1;\n      };\n    }\n    function normalizeName(name) {\n      if (typeof name !== 'string') {\n        name = String(name);\n      }\n      if (/[^a-z0-9\\-#$%&'*+.^_`|~!]/i.test(name) || name === '') {\n        throw new TypeError('Invalid character in header field name: \"' + name + '\"');\n      }\n      return name.toLowerCase();\n    }\n    function normalizeValue(value) {\n      if (typeof value !== 'string') {\n        value = String(value);\n      }\n      return value;\n    }\n\n    // Build a destructive iterator for the value list\n    function iteratorFor(items) {\n      var iterator = {\n        next: function () {\n          var value = items.shift();\n          return {\n            done: value === undefined,\n            value: value\n          };\n        }\n      };\n      if (support.iterable) {\n        iterator[Symbol.iterator] = function () {\n          return iterator;\n        };\n      }\n      return iterator;\n    }\n    function Headers(headers) {\n      this.map = {};\n      if (headers instanceof Headers) {\n        headers.forEach(function (value, name) {\n          this.append(name, value);\n        }, this);\n      } else if (Array.isArray(headers)) {\n        headers.forEach(function (header) {\n          this.append(header[0], header[1]);\n        }, this);\n      } else if (headers) {\n        Object.getOwnPropertyNames(headers).forEach(function (name) {\n          this.append(name, headers[name]);\n        }, this);\n      }\n    }\n    Headers.prototype.append = function (name, value) {\n      name = normalizeName(name);\n      value = normalizeValue(value);\n      var oldValue = this.map[name];\n      this.map[name] = oldValue ? oldValue + ', ' + value : value;\n    };\n    Headers.prototype['delete'] = function (name) {\n      delete this.map[normalizeName(name)];\n    };\n    Headers.prototype.get = function (name) {\n      name = normalizeName(name);\n      return this.has(name) ? this.map[name] : null;\n    };\n    Headers.prototype.has = function (name) {\n      return this.map.hasOwnProperty(normalizeName(name));\n    };\n    Headers.prototype.set = function (name, value) {\n      this.map[normalizeName(name)] = normalizeValue(value);\n    };\n    Headers.prototype.forEach = function (callback, thisArg) {\n      for (var name in this.map) {\n        if (this.map.hasOwnProperty(name)) {\n          callback.call(thisArg, this.map[name], name, this);\n        }\n      }\n    };\n    Headers.prototype.keys = function () {\n      var items = [];\n      this.forEach(function (value, name) {\n        items.push(name);\n      });\n      return iteratorFor(items);\n    };\n    Headers.prototype.values = function () {\n      var items = [];\n      this.forEach(function (value) {\n        items.push(value);\n      });\n      return iteratorFor(items);\n    };\n    Headers.prototype.entries = function () {\n      var items = [];\n      this.forEach(function (value, name) {\n        items.push([name, value]);\n      });\n      return iteratorFor(items);\n    };\n    if (support.iterable) {\n      Headers.prototype[Symbol.iterator] = Headers.prototype.entries;\n    }\n    function consumed(body) {\n      if (body.bodyUsed) {\n        return Promise.reject(new TypeError('Already read'));\n      }\n      body.bodyUsed = true;\n    }\n    function fileReaderReady(reader) {\n      return new Promise(function (resolve, reject) {\n        reader.onload = function () {\n          resolve(reader.result);\n        };\n        reader.onerror = function () {\n          reject(reader.error);\n        };\n      });\n    }\n    function readBlobAsArrayBuffer(blob) {\n      var reader = new FileReader();\n      var promise = fileReaderReady(reader);\n      reader.readAsArrayBuffer(blob);\n      return promise;\n    }\n    function readBlobAsText(blob) {\n      var reader = new FileReader();\n      var promise = fileReaderReady(reader);\n      reader.readAsText(blob);\n      return promise;\n    }\n    function readArrayBufferAsText(buf) {\n      var view = new Uint8Array(buf);\n      var chars = new Array(view.length);\n      for (var i = 0; i < view.length; i++) {\n        chars[i] = String.fromCharCode(view[i]);\n      }\n      return chars.join('');\n    }\n    function bufferClone(buf) {\n      if (buf.slice) {\n        return buf.slice(0);\n      } else {\n        var view = new Uint8Array(buf.byteLength);\n        view.set(new Uint8Array(buf));\n        return view.buffer;\n      }\n    }\n    function Body() {\n      this.bodyUsed = false;\n      this._initBody = function (body) {\n        /*\n          fetch-mock wraps the Response object in an ES6 Proxy to\n          provide useful test harness features such as flush. However, on\n          ES5 browsers without fetch or Proxy support pollyfills must be used;\n          the proxy-pollyfill is unable to proxy an attribute unless it exists\n          on the object before the Proxy is created. This change ensures\n          Response.bodyUsed exists on the instance, while maintaining the\n          semantic of setting Request.bodyUsed in the constructor before\n          _initBody is called.\n        */\n        this.bodyUsed = this.bodyUsed;\n        this._bodyInit = body;\n        if (!body) {\n          this._bodyText = '';\n        } else if (typeof body === 'string') {\n          this._bodyText = body;\n        } else if (support.blob && Blob.prototype.isPrototypeOf(body)) {\n          this._bodyBlob = body;\n        } else if (support.formData && FormData.prototype.isPrototypeOf(body)) {\n          this._bodyFormData = body;\n        } else if (support.searchParams && URLSearchParams.prototype.isPrototypeOf(body)) {\n          this._bodyText = body.toString();\n        } else if (support.arrayBuffer && support.blob && isDataView(body)) {\n          this._bodyArrayBuffer = bufferClone(body.buffer);\n          // IE 10-11 can't handle a DataView body.\n          this._bodyInit = new Blob([this._bodyArrayBuffer]);\n        } else if (support.arrayBuffer && (ArrayBuffer.prototype.isPrototypeOf(body) || isArrayBufferView(body))) {\n          this._bodyArrayBuffer = bufferClone(body);\n        } else {\n          this._bodyText = body = Object.prototype.toString.call(body);\n        }\n        if (!this.headers.get('content-type')) {\n          if (typeof body === 'string') {\n            this.headers.set('content-type', 'text/plain;charset=UTF-8');\n          } else if (this._bodyBlob && this._bodyBlob.type) {\n            this.headers.set('content-type', this._bodyBlob.type);\n          } else if (support.searchParams && URLSearchParams.prototype.isPrototypeOf(body)) {\n            this.headers.set('content-type', 'application/x-www-form-urlencoded;charset=UTF-8');\n          }\n        }\n      };\n      if (support.blob) {\n        this.blob = function () {\n          var rejected = consumed(this);\n          if (rejected) {\n            return rejected;\n          }\n          if (this._bodyBlob) {\n            return Promise.resolve(this._bodyBlob);\n          } else if (this._bodyArrayBuffer) {\n            return Promise.resolve(new Blob([this._bodyArrayBuffer]));\n          } else if (this._bodyFormData) {\n            throw new Error('could not read FormData body as blob');\n          } else {\n            return Promise.resolve(new Blob([this._bodyText]));\n          }\n        };\n        this.arrayBuffer = function () {\n          if (this._bodyArrayBuffer) {\n            var isConsumed = consumed(this);\n            if (isConsumed) {\n              return isConsumed;\n            }\n            if (ArrayBuffer.isView(this._bodyArrayBuffer)) {\n              return Promise.resolve(this._bodyArrayBuffer.buffer.slice(this._bodyArrayBuffer.byteOffset, this._bodyArrayBuffer.byteOffset + this._bodyArrayBuffer.byteLength));\n            } else {\n              return Promise.resolve(this._bodyArrayBuffer);\n            }\n          } else {\n            return this.blob().then(readBlobAsArrayBuffer);\n          }\n        };\n      }\n      this.text = function () {\n        var rejected = consumed(this);\n        if (rejected) {\n          return rejected;\n        }\n        if (this._bodyBlob) {\n          return readBlobAsText(this._bodyBlob);\n        } else if (this._bodyArrayBuffer) {\n          return Promise.resolve(readArrayBufferAsText(this._bodyArrayBuffer));\n        } else if (this._bodyFormData) {\n          throw new Error('could not read FormData body as text');\n        } else {\n          return Promise.resolve(this._bodyText);\n        }\n      };\n      if (support.formData) {\n        this.formData = function () {\n          return this.text().then(decode);\n        };\n      }\n      this.json = function () {\n        return this.text().then(JSON.parse);\n      };\n      return this;\n    }\n\n    // HTTP methods whose capitalization should be normalized\n    var methods = ['DELETE', 'GET', 'HEAD', 'OPTIONS', 'POST', 'PUT'];\n    function normalizeMethod(method) {\n      var upcased = method.toUpperCase();\n      return methods.indexOf(upcased) > -1 ? upcased : method;\n    }\n    function Request(input, options) {\n      if (!(this instanceof Request)) {\n        throw new TypeError('Please use the \"new\" operator, this DOM object constructor cannot be called as a function.');\n      }\n      options = options || {};\n      var body = options.body;\n      if (input instanceof Request) {\n        if (input.bodyUsed) {\n          throw new TypeError('Already read');\n        }\n        this.url = input.url;\n        this.credentials = input.credentials;\n        if (!options.headers) {\n          this.headers = new Headers(input.headers);\n        }\n        this.method = input.method;\n        this.mode = input.mode;\n        this.signal = input.signal;\n        if (!body && input._bodyInit != null) {\n          body = input._bodyInit;\n          input.bodyUsed = true;\n        }\n      } else {\n        this.url = String(input);\n      }\n      this.credentials = options.credentials || this.credentials || 'same-origin';\n      if (options.headers || !this.headers) {\n        this.headers = new Headers(options.headers);\n      }\n      this.method = normalizeMethod(options.method || this.method || 'GET');\n      this.mode = options.mode || this.mode || null;\n      this.signal = options.signal || this.signal;\n      this.referrer = null;\n      if ((this.method === 'GET' || this.method === 'HEAD') && body) {\n        throw new TypeError('Body not allowed for GET or HEAD requests');\n      }\n      this._initBody(body);\n      if (this.method === 'GET' || this.method === 'HEAD') {\n        if (options.cache === 'no-store' || options.cache === 'no-cache') {\n          // Search for a '_' parameter in the query string\n          var reParamSearch = /([?&])_=[^&]*/;\n          if (reParamSearch.test(this.url)) {\n            // If it already exists then set the value with the current time\n            this.url = this.url.replace(reParamSearch, '$1_=' + new Date().getTime());\n          } else {\n            // Otherwise add a new '_' parameter to the end with the current time\n            var reQueryString = /\\?/;\n            this.url += (reQueryString.test(this.url) ? '&' : '?') + '_=' + new Date().getTime();\n          }\n        }\n      }\n    }\n    Request.prototype.clone = function () {\n      return new Request(this, {\n        body: this._bodyInit\n      });\n    };\n    function decode(body) {\n      var form = new FormData();\n      body.trim().split('&').forEach(function (bytes) {\n        if (bytes) {\n          var split = bytes.split('=');\n          var name = split.shift().replace(/\\+/g, ' ');\n          var value = split.join('=').replace(/\\+/g, ' ');\n          form.append(decodeURIComponent(name), decodeURIComponent(value));\n        }\n      });\n      return form;\n    }\n    function parseHeaders(rawHeaders) {\n      var headers = new Headers();\n      // Replace instances of \\r\\n and \\n followed by at least one space or horizontal tab with a space\n      // https://tools.ietf.org/html/rfc7230#section-3.2\n      var preProcessedHeaders = rawHeaders.replace(/\\r?\\n[\\t ]+/g, ' ');\n      // Avoiding split via regex to work around a common IE11 bug with the core-js 3.6.0 regex polyfill\n      // https://github.com/github/fetch/issues/748\n      // https://github.com/zloirock/core-js/issues/751\n      preProcessedHeaders.split('\\r').map(function (header) {\n        return header.indexOf('\\n') === 0 ? header.substr(1, header.length) : header;\n      }).forEach(function (line) {\n        var parts = line.split(':');\n        var key = parts.shift().trim();\n        if (key) {\n          var value = parts.join(':').trim();\n          headers.append(key, value);\n        }\n      });\n      return headers;\n    }\n    Body.call(Request.prototype);\n    function Response(bodyInit, options) {\n      if (!(this instanceof Response)) {\n        throw new TypeError('Please use the \"new\" operator, this DOM object constructor cannot be called as a function.');\n      }\n      if (!options) {\n        options = {};\n      }\n      this.type = 'default';\n      this.status = options.status === undefined ? 200 : options.status;\n      this.ok = this.status >= 200 && this.status < 300;\n      this.statusText = options.statusText === undefined ? '' : '' + options.statusText;\n      this.headers = new Headers(options.headers);\n      this.url = options.url || '';\n      this._initBody(bodyInit);\n    }\n    Body.call(Response.prototype);\n    Response.prototype.clone = function () {\n      return new Response(this._bodyInit, {\n        status: this.status,\n        statusText: this.statusText,\n        headers: new Headers(this.headers),\n        url: this.url\n      });\n    };\n    Response.error = function () {\n      var response = new Response(null, {\n        status: 0,\n        statusText: ''\n      });\n      response.type = 'error';\n      return response;\n    };\n    var redirectStatuses = [301, 302, 303, 307, 308];\n    Response.redirect = function (url, status) {\n      if (redirectStatuses.indexOf(status) === -1) {\n        throw new RangeError('Invalid status code');\n      }\n      return new Response(null, {\n        status: status,\n        headers: {\n          location: url\n        }\n      });\n    };\n    exports.DOMException = global.DOMException;\n    try {\n      new exports.DOMException();\n    } catch (err) {\n      exports.DOMException = function (message, name) {\n        this.message = message;\n        this.name = name;\n        var error = Error(message);\n        this.stack = error.stack;\n      };\n      exports.DOMException.prototype = Object.create(Error.prototype);\n      exports.DOMException.prototype.constructor = exports.DOMException;\n    }\n    function fetch(input, init) {\n      return new Promise(function (resolve, reject) {\n        var request = new Request(input, init);\n        if (request.signal && request.signal.aborted) {\n          return reject(new exports.DOMException('Aborted', 'AbortError'));\n        }\n        var xhr = new XMLHttpRequest();\n        function abortXhr() {\n          xhr.abort();\n        }\n        xhr.onload = function () {\n          var options = {\n            status: xhr.status,\n            statusText: xhr.statusText,\n            headers: parseHeaders(xhr.getAllResponseHeaders() || '')\n          };\n          options.url = 'responseURL' in xhr ? xhr.responseURL : options.headers.get('X-Request-URL');\n          var body = 'response' in xhr ? xhr.response : xhr.responseText;\n          setTimeout(function () {\n            resolve(new Response(body, options));\n          }, 0);\n        };\n        xhr.onerror = function () {\n          setTimeout(function () {\n            reject(new TypeError('Network request failed'));\n          }, 0);\n        };\n        xhr.ontimeout = function () {\n          setTimeout(function () {\n            reject(new TypeError('Network request failed'));\n          }, 0);\n        };\n        xhr.onabort = function () {\n          setTimeout(function () {\n            reject(new exports.DOMException('Aborted', 'AbortError'));\n          }, 0);\n        };\n        function fixUrl(url) {\n          try {\n            return url === '' && global.location.href ? global.location.href : url;\n          } catch (e) {\n            return url;\n          }\n        }\n        xhr.open(request.method, fixUrl(request.url), true);\n        if (request.credentials === 'include') {\n          xhr.withCredentials = true;\n        } else if (request.credentials === 'omit') {\n          xhr.withCredentials = false;\n        }\n        if ('responseType' in xhr) {\n          if (support.blob) {\n            xhr.responseType = 'blob';\n          } else if (support.arrayBuffer && request.headers.get('Content-Type') && request.headers.get('Content-Type').indexOf('application/octet-stream') !== -1) {\n            xhr.responseType = 'arraybuffer';\n          }\n        }\n        if (init && typeof init.headers === 'object' && !(init.headers instanceof Headers)) {\n          Object.getOwnPropertyNames(init.headers).forEach(function (name) {\n            xhr.setRequestHeader(name, normalizeValue(init.headers[name]));\n          });\n        } else {\n          request.headers.forEach(function (value, name) {\n            xhr.setRequestHeader(name, value);\n          });\n        }\n        if (request.signal) {\n          request.signal.addEventListener('abort', abortXhr);\n          xhr.onreadystatechange = function () {\n            // DONE (success or failure)\n            if (xhr.readyState === 4) {\n              request.signal.removeEventListener('abort', abortXhr);\n            }\n          };\n        }\n        xhr.send(typeof request._bodyInit === 'undefined' ? null : request._bodyInit);\n      });\n    }\n    fetch.polyfill = true;\n    if (!global.fetch) {\n      global.fetch = fetch;\n      global.Headers = Headers;\n      global.Request = Request;\n      global.Response = Response;\n    }\n    exports.Headers = Headers;\n    exports.Request = Request;\n    exports.Response = Response;\n    exports.fetch = fetch;\n    return exports;\n  }({});\n})(__globalThis__);\n// This is a ponyfill, so...\n__globalThis__.fetch.ponyfill = true;\ndelete __globalThis__.fetch.polyfill;\n// Choose between native implementation (__global__) or custom implementation (__globalThis__)\nvar ctx = __global__.fetch ? __global__ : __globalThis__;\nexports = ctx.fetch; // To enable: import fetch from 'cross-fetch'\nexports.default = ctx.fetch; // For TypeScript consumers without esModuleInterop.\nexports.fetch = ctx.fetch; // To enable: import {fetch} from 'cross-fetch'\nexports.Headers = ctx.Headers;\nexports.Request = ctx.Request;\nexports.Response = ctx.Response;\nmodule.exports = exports;", "map": {"version": 3, "names": ["__global__", "globalThis", "self", "global", "__globalThis__", "F", "fetch", "DOMException", "prototype", "irrelevant", "exports", "support", "searchParams", "iterable", "Symbol", "blob", "Blob", "e", "formData", "arrayBuffer", "isDataView", "obj", "DataView", "isPrototypeOf", "viewClasses", "isArrayBuffer<PERSON>iew", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "indexOf", "Object", "toString", "call", "normalizeName", "name", "String", "test", "TypeError", "toLowerCase", "normalizeValue", "value", "iteratorFor", "items", "iterator", "next", "shift", "done", "undefined", "Headers", "headers", "map", "for<PERSON>ach", "append", "Array", "isArray", "header", "getOwnPropertyNames", "oldValue", "get", "has", "hasOwnProperty", "set", "callback", "thisArg", "keys", "push", "values", "entries", "consumed", "body", "bodyUsed", "Promise", "reject", "fileReaderReady", "reader", "resolve", "onload", "result", "onerror", "error", "readBlobAsArrayBuffer", "FileReader", "promise", "readAsA<PERSON>y<PERSON><PERSON>er", "readBlobAsText", "readAsText", "readArrayBufferAsText", "buf", "view", "Uint8Array", "chars", "length", "i", "fromCharCode", "join", "bufferClone", "slice", "byteLength", "buffer", "Body", "_initBody", "_bodyInit", "_bodyText", "_bodyBlob", "FormData", "_bodyFormData", "URLSearchParams", "_body<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type", "rejected", "Error", "isConsumed", "byteOffset", "then", "text", "decode", "json", "JSON", "parse", "methods", "normalizeMethod", "method", "upcased", "toUpperCase", "Request", "input", "options", "url", "credentials", "mode", "signal", "referrer", "cache", "reParamSearch", "replace", "Date", "getTime", "reQueryString", "clone", "form", "trim", "split", "bytes", "decodeURIComponent", "parseHeaders", "rawHeaders", "preProcessedHeaders", "substr", "line", "parts", "key", "Response", "bodyInit", "status", "ok", "statusText", "response", "redirectStatuses", "redirect", "RangeError", "location", "err", "message", "stack", "create", "constructor", "init", "request", "aborted", "xhr", "XMLHttpRequest", "abortXhr", "abort", "getAllResponseHeaders", "responseURL", "responseText", "setTimeout", "ontimeout", "<PERSON>ab<PERSON>", "fixUrl", "href", "open", "withCredentials", "responseType", "setRequestHeader", "addEventListener", "onreadystatechange", "readyState", "removeEventListener", "send", "polyfill", "ponyfill", "ctx", "default", "module"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/node_modules/cross-fetch/dist/browser-ponyfill.js"], "sourcesContent": ["// Save global object in a variable\nvar __global__ =\n(typeof globalThis !== 'undefined' && globalThis) ||\n(typeof self !== 'undefined' && self) ||\n(typeof global !== 'undefined' && global);\n// Create an object that extends from __global__ without the fetch function\nvar __globalThis__ = (function () {\nfunction F() {\nthis.fetch = false;\nthis.DOMException = __global__.DOMException\n}\nF.prototype = __global__; // Needed for feature detection on whatwg-fetch's code\nreturn new F();\n})();\n// Wraps whatwg-fetch with a function scope to hijack the global object\n// \"globalThis\" that's going to be patched\n(function(globalThis) {\n\nvar irrelevant = (function (exports) {\n\n  var global =\n    (typeof globalThis !== 'undefined' && globalThis) ||\n    (typeof self !== 'undefined' && self) ||\n    (typeof global !== 'undefined' && global);\n\n  var support = {\n    searchParams: 'URLSearchParams' in global,\n    iterable: 'Symbol' in global && 'iterator' in Symbol,\n    blob:\n      'FileReader' in global &&\n      'Blob' in global &&\n      (function() {\n        try {\n          new Blob();\n          return true\n        } catch (e) {\n          return false\n        }\n      })(),\n    formData: 'FormData' in global,\n    arrayBuffer: 'ArrayBuffer' in global\n  };\n\n  function isDataView(obj) {\n    return obj && DataView.prototype.isPrototypeOf(obj)\n  }\n\n  if (support.arrayBuffer) {\n    var viewClasses = [\n      '[object Int8Array]',\n      '[object Uint8Array]',\n      '[object Uint8ClampedArray]',\n      '[object Int16Array]',\n      '[object Uint16Array]',\n      '[object Int32Array]',\n      '[object Uint32Array]',\n      '[object Float32Array]',\n      '[object Float64Array]'\n    ];\n\n    var isArrayBufferView =\n      ArrayBuffer.isView ||\n      function(obj) {\n        return obj && viewClasses.indexOf(Object.prototype.toString.call(obj)) > -1\n      };\n  }\n\n  function normalizeName(name) {\n    if (typeof name !== 'string') {\n      name = String(name);\n    }\n    if (/[^a-z0-9\\-#$%&'*+.^_`|~!]/i.test(name) || name === '') {\n      throw new TypeError('Invalid character in header field name: \"' + name + '\"')\n    }\n    return name.toLowerCase()\n  }\n\n  function normalizeValue(value) {\n    if (typeof value !== 'string') {\n      value = String(value);\n    }\n    return value\n  }\n\n  // Build a destructive iterator for the value list\n  function iteratorFor(items) {\n    var iterator = {\n      next: function() {\n        var value = items.shift();\n        return {done: value === undefined, value: value}\n      }\n    };\n\n    if (support.iterable) {\n      iterator[Symbol.iterator] = function() {\n        return iterator\n      };\n    }\n\n    return iterator\n  }\n\n  function Headers(headers) {\n    this.map = {};\n\n    if (headers instanceof Headers) {\n      headers.forEach(function(value, name) {\n        this.append(name, value);\n      }, this);\n    } else if (Array.isArray(headers)) {\n      headers.forEach(function(header) {\n        this.append(header[0], header[1]);\n      }, this);\n    } else if (headers) {\n      Object.getOwnPropertyNames(headers).forEach(function(name) {\n        this.append(name, headers[name]);\n      }, this);\n    }\n  }\n\n  Headers.prototype.append = function(name, value) {\n    name = normalizeName(name);\n    value = normalizeValue(value);\n    var oldValue = this.map[name];\n    this.map[name] = oldValue ? oldValue + ', ' + value : value;\n  };\n\n  Headers.prototype['delete'] = function(name) {\n    delete this.map[normalizeName(name)];\n  };\n\n  Headers.prototype.get = function(name) {\n    name = normalizeName(name);\n    return this.has(name) ? this.map[name] : null\n  };\n\n  Headers.prototype.has = function(name) {\n    return this.map.hasOwnProperty(normalizeName(name))\n  };\n\n  Headers.prototype.set = function(name, value) {\n    this.map[normalizeName(name)] = normalizeValue(value);\n  };\n\n  Headers.prototype.forEach = function(callback, thisArg) {\n    for (var name in this.map) {\n      if (this.map.hasOwnProperty(name)) {\n        callback.call(thisArg, this.map[name], name, this);\n      }\n    }\n  };\n\n  Headers.prototype.keys = function() {\n    var items = [];\n    this.forEach(function(value, name) {\n      items.push(name);\n    });\n    return iteratorFor(items)\n  };\n\n  Headers.prototype.values = function() {\n    var items = [];\n    this.forEach(function(value) {\n      items.push(value);\n    });\n    return iteratorFor(items)\n  };\n\n  Headers.prototype.entries = function() {\n    var items = [];\n    this.forEach(function(value, name) {\n      items.push([name, value]);\n    });\n    return iteratorFor(items)\n  };\n\n  if (support.iterable) {\n    Headers.prototype[Symbol.iterator] = Headers.prototype.entries;\n  }\n\n  function consumed(body) {\n    if (body.bodyUsed) {\n      return Promise.reject(new TypeError('Already read'))\n    }\n    body.bodyUsed = true;\n  }\n\n  function fileReaderReady(reader) {\n    return new Promise(function(resolve, reject) {\n      reader.onload = function() {\n        resolve(reader.result);\n      };\n      reader.onerror = function() {\n        reject(reader.error);\n      };\n    })\n  }\n\n  function readBlobAsArrayBuffer(blob) {\n    var reader = new FileReader();\n    var promise = fileReaderReady(reader);\n    reader.readAsArrayBuffer(blob);\n    return promise\n  }\n\n  function readBlobAsText(blob) {\n    var reader = new FileReader();\n    var promise = fileReaderReady(reader);\n    reader.readAsText(blob);\n    return promise\n  }\n\n  function readArrayBufferAsText(buf) {\n    var view = new Uint8Array(buf);\n    var chars = new Array(view.length);\n\n    for (var i = 0; i < view.length; i++) {\n      chars[i] = String.fromCharCode(view[i]);\n    }\n    return chars.join('')\n  }\n\n  function bufferClone(buf) {\n    if (buf.slice) {\n      return buf.slice(0)\n    } else {\n      var view = new Uint8Array(buf.byteLength);\n      view.set(new Uint8Array(buf));\n      return view.buffer\n    }\n  }\n\n  function Body() {\n    this.bodyUsed = false;\n\n    this._initBody = function(body) {\n      /*\n        fetch-mock wraps the Response object in an ES6 Proxy to\n        provide useful test harness features such as flush. However, on\n        ES5 browsers without fetch or Proxy support pollyfills must be used;\n        the proxy-pollyfill is unable to proxy an attribute unless it exists\n        on the object before the Proxy is created. This change ensures\n        Response.bodyUsed exists on the instance, while maintaining the\n        semantic of setting Request.bodyUsed in the constructor before\n        _initBody is called.\n      */\n      this.bodyUsed = this.bodyUsed;\n      this._bodyInit = body;\n      if (!body) {\n        this._bodyText = '';\n      } else if (typeof body === 'string') {\n        this._bodyText = body;\n      } else if (support.blob && Blob.prototype.isPrototypeOf(body)) {\n        this._bodyBlob = body;\n      } else if (support.formData && FormData.prototype.isPrototypeOf(body)) {\n        this._bodyFormData = body;\n      } else if (support.searchParams && URLSearchParams.prototype.isPrototypeOf(body)) {\n        this._bodyText = body.toString();\n      } else if (support.arrayBuffer && support.blob && isDataView(body)) {\n        this._bodyArrayBuffer = bufferClone(body.buffer);\n        // IE 10-11 can't handle a DataView body.\n        this._bodyInit = new Blob([this._bodyArrayBuffer]);\n      } else if (support.arrayBuffer && (ArrayBuffer.prototype.isPrototypeOf(body) || isArrayBufferView(body))) {\n        this._bodyArrayBuffer = bufferClone(body);\n      } else {\n        this._bodyText = body = Object.prototype.toString.call(body);\n      }\n\n      if (!this.headers.get('content-type')) {\n        if (typeof body === 'string') {\n          this.headers.set('content-type', 'text/plain;charset=UTF-8');\n        } else if (this._bodyBlob && this._bodyBlob.type) {\n          this.headers.set('content-type', this._bodyBlob.type);\n        } else if (support.searchParams && URLSearchParams.prototype.isPrototypeOf(body)) {\n          this.headers.set('content-type', 'application/x-www-form-urlencoded;charset=UTF-8');\n        }\n      }\n    };\n\n    if (support.blob) {\n      this.blob = function() {\n        var rejected = consumed(this);\n        if (rejected) {\n          return rejected\n        }\n\n        if (this._bodyBlob) {\n          return Promise.resolve(this._bodyBlob)\n        } else if (this._bodyArrayBuffer) {\n          return Promise.resolve(new Blob([this._bodyArrayBuffer]))\n        } else if (this._bodyFormData) {\n          throw new Error('could not read FormData body as blob')\n        } else {\n          return Promise.resolve(new Blob([this._bodyText]))\n        }\n      };\n\n      this.arrayBuffer = function() {\n        if (this._bodyArrayBuffer) {\n          var isConsumed = consumed(this);\n          if (isConsumed) {\n            return isConsumed\n          }\n          if (ArrayBuffer.isView(this._bodyArrayBuffer)) {\n            return Promise.resolve(\n              this._bodyArrayBuffer.buffer.slice(\n                this._bodyArrayBuffer.byteOffset,\n                this._bodyArrayBuffer.byteOffset + this._bodyArrayBuffer.byteLength\n              )\n            )\n          } else {\n            return Promise.resolve(this._bodyArrayBuffer)\n          }\n        } else {\n          return this.blob().then(readBlobAsArrayBuffer)\n        }\n      };\n    }\n\n    this.text = function() {\n      var rejected = consumed(this);\n      if (rejected) {\n        return rejected\n      }\n\n      if (this._bodyBlob) {\n        return readBlobAsText(this._bodyBlob)\n      } else if (this._bodyArrayBuffer) {\n        return Promise.resolve(readArrayBufferAsText(this._bodyArrayBuffer))\n      } else if (this._bodyFormData) {\n        throw new Error('could not read FormData body as text')\n      } else {\n        return Promise.resolve(this._bodyText)\n      }\n    };\n\n    if (support.formData) {\n      this.formData = function() {\n        return this.text().then(decode)\n      };\n    }\n\n    this.json = function() {\n      return this.text().then(JSON.parse)\n    };\n\n    return this\n  }\n\n  // HTTP methods whose capitalization should be normalized\n  var methods = ['DELETE', 'GET', 'HEAD', 'OPTIONS', 'POST', 'PUT'];\n\n  function normalizeMethod(method) {\n    var upcased = method.toUpperCase();\n    return methods.indexOf(upcased) > -1 ? upcased : method\n  }\n\n  function Request(input, options) {\n    if (!(this instanceof Request)) {\n      throw new TypeError('Please use the \"new\" operator, this DOM object constructor cannot be called as a function.')\n    }\n\n    options = options || {};\n    var body = options.body;\n\n    if (input instanceof Request) {\n      if (input.bodyUsed) {\n        throw new TypeError('Already read')\n      }\n      this.url = input.url;\n      this.credentials = input.credentials;\n      if (!options.headers) {\n        this.headers = new Headers(input.headers);\n      }\n      this.method = input.method;\n      this.mode = input.mode;\n      this.signal = input.signal;\n      if (!body && input._bodyInit != null) {\n        body = input._bodyInit;\n        input.bodyUsed = true;\n      }\n    } else {\n      this.url = String(input);\n    }\n\n    this.credentials = options.credentials || this.credentials || 'same-origin';\n    if (options.headers || !this.headers) {\n      this.headers = new Headers(options.headers);\n    }\n    this.method = normalizeMethod(options.method || this.method || 'GET');\n    this.mode = options.mode || this.mode || null;\n    this.signal = options.signal || this.signal;\n    this.referrer = null;\n\n    if ((this.method === 'GET' || this.method === 'HEAD') && body) {\n      throw new TypeError('Body not allowed for GET or HEAD requests')\n    }\n    this._initBody(body);\n\n    if (this.method === 'GET' || this.method === 'HEAD') {\n      if (options.cache === 'no-store' || options.cache === 'no-cache') {\n        // Search for a '_' parameter in the query string\n        var reParamSearch = /([?&])_=[^&]*/;\n        if (reParamSearch.test(this.url)) {\n          // If it already exists then set the value with the current time\n          this.url = this.url.replace(reParamSearch, '$1_=' + new Date().getTime());\n        } else {\n          // Otherwise add a new '_' parameter to the end with the current time\n          var reQueryString = /\\?/;\n          this.url += (reQueryString.test(this.url) ? '&' : '?') + '_=' + new Date().getTime();\n        }\n      }\n    }\n  }\n\n  Request.prototype.clone = function() {\n    return new Request(this, {body: this._bodyInit})\n  };\n\n  function decode(body) {\n    var form = new FormData();\n    body\n      .trim()\n      .split('&')\n      .forEach(function(bytes) {\n        if (bytes) {\n          var split = bytes.split('=');\n          var name = split.shift().replace(/\\+/g, ' ');\n          var value = split.join('=').replace(/\\+/g, ' ');\n          form.append(decodeURIComponent(name), decodeURIComponent(value));\n        }\n      });\n    return form\n  }\n\n  function parseHeaders(rawHeaders) {\n    var headers = new Headers();\n    // Replace instances of \\r\\n and \\n followed by at least one space or horizontal tab with a space\n    // https://tools.ietf.org/html/rfc7230#section-3.2\n    var preProcessedHeaders = rawHeaders.replace(/\\r?\\n[\\t ]+/g, ' ');\n    // Avoiding split via regex to work around a common IE11 bug with the core-js 3.6.0 regex polyfill\n    // https://github.com/github/fetch/issues/748\n    // https://github.com/zloirock/core-js/issues/751\n    preProcessedHeaders\n      .split('\\r')\n      .map(function(header) {\n        return header.indexOf('\\n') === 0 ? header.substr(1, header.length) : header\n      })\n      .forEach(function(line) {\n        var parts = line.split(':');\n        var key = parts.shift().trim();\n        if (key) {\n          var value = parts.join(':').trim();\n          headers.append(key, value);\n        }\n      });\n    return headers\n  }\n\n  Body.call(Request.prototype);\n\n  function Response(bodyInit, options) {\n    if (!(this instanceof Response)) {\n      throw new TypeError('Please use the \"new\" operator, this DOM object constructor cannot be called as a function.')\n    }\n    if (!options) {\n      options = {};\n    }\n\n    this.type = 'default';\n    this.status = options.status === undefined ? 200 : options.status;\n    this.ok = this.status >= 200 && this.status < 300;\n    this.statusText = options.statusText === undefined ? '' : '' + options.statusText;\n    this.headers = new Headers(options.headers);\n    this.url = options.url || '';\n    this._initBody(bodyInit);\n  }\n\n  Body.call(Response.prototype);\n\n  Response.prototype.clone = function() {\n    return new Response(this._bodyInit, {\n      status: this.status,\n      statusText: this.statusText,\n      headers: new Headers(this.headers),\n      url: this.url\n    })\n  };\n\n  Response.error = function() {\n    var response = new Response(null, {status: 0, statusText: ''});\n    response.type = 'error';\n    return response\n  };\n\n  var redirectStatuses = [301, 302, 303, 307, 308];\n\n  Response.redirect = function(url, status) {\n    if (redirectStatuses.indexOf(status) === -1) {\n      throw new RangeError('Invalid status code')\n    }\n\n    return new Response(null, {status: status, headers: {location: url}})\n  };\n\n  exports.DOMException = global.DOMException;\n  try {\n    new exports.DOMException();\n  } catch (err) {\n    exports.DOMException = function(message, name) {\n      this.message = message;\n      this.name = name;\n      var error = Error(message);\n      this.stack = error.stack;\n    };\n    exports.DOMException.prototype = Object.create(Error.prototype);\n    exports.DOMException.prototype.constructor = exports.DOMException;\n  }\n\n  function fetch(input, init) {\n    return new Promise(function(resolve, reject) {\n      var request = new Request(input, init);\n\n      if (request.signal && request.signal.aborted) {\n        return reject(new exports.DOMException('Aborted', 'AbortError'))\n      }\n\n      var xhr = new XMLHttpRequest();\n\n      function abortXhr() {\n        xhr.abort();\n      }\n\n      xhr.onload = function() {\n        var options = {\n          status: xhr.status,\n          statusText: xhr.statusText,\n          headers: parseHeaders(xhr.getAllResponseHeaders() || '')\n        };\n        options.url = 'responseURL' in xhr ? xhr.responseURL : options.headers.get('X-Request-URL');\n        var body = 'response' in xhr ? xhr.response : xhr.responseText;\n        setTimeout(function() {\n          resolve(new Response(body, options));\n        }, 0);\n      };\n\n      xhr.onerror = function() {\n        setTimeout(function() {\n          reject(new TypeError('Network request failed'));\n        }, 0);\n      };\n\n      xhr.ontimeout = function() {\n        setTimeout(function() {\n          reject(new TypeError('Network request failed'));\n        }, 0);\n      };\n\n      xhr.onabort = function() {\n        setTimeout(function() {\n          reject(new exports.DOMException('Aborted', 'AbortError'));\n        }, 0);\n      };\n\n      function fixUrl(url) {\n        try {\n          return url === '' && global.location.href ? global.location.href : url\n        } catch (e) {\n          return url\n        }\n      }\n\n      xhr.open(request.method, fixUrl(request.url), true);\n\n      if (request.credentials === 'include') {\n        xhr.withCredentials = true;\n      } else if (request.credentials === 'omit') {\n        xhr.withCredentials = false;\n      }\n\n      if ('responseType' in xhr) {\n        if (support.blob) {\n          xhr.responseType = 'blob';\n        } else if (\n          support.arrayBuffer &&\n          request.headers.get('Content-Type') &&\n          request.headers.get('Content-Type').indexOf('application/octet-stream') !== -1\n        ) {\n          xhr.responseType = 'arraybuffer';\n        }\n      }\n\n      if (init && typeof init.headers === 'object' && !(init.headers instanceof Headers)) {\n        Object.getOwnPropertyNames(init.headers).forEach(function(name) {\n          xhr.setRequestHeader(name, normalizeValue(init.headers[name]));\n        });\n      } else {\n        request.headers.forEach(function(value, name) {\n          xhr.setRequestHeader(name, value);\n        });\n      }\n\n      if (request.signal) {\n        request.signal.addEventListener('abort', abortXhr);\n\n        xhr.onreadystatechange = function() {\n          // DONE (success or failure)\n          if (xhr.readyState === 4) {\n            request.signal.removeEventListener('abort', abortXhr);\n          }\n        };\n      }\n\n      xhr.send(typeof request._bodyInit === 'undefined' ? null : request._bodyInit);\n    })\n  }\n\n  fetch.polyfill = true;\n\n  if (!global.fetch) {\n    global.fetch = fetch;\n    global.Headers = Headers;\n    global.Request = Request;\n    global.Response = Response;\n  }\n\n  exports.Headers = Headers;\n  exports.Request = Request;\n  exports.Response = Response;\n  exports.fetch = fetch;\n\n  return exports;\n\n})({});\n})(__globalThis__);\n// This is a ponyfill, so...\n__globalThis__.fetch.ponyfill = true;\ndelete __globalThis__.fetch.polyfill;\n// Choose between native implementation (__global__) or custom implementation (__globalThis__)\nvar ctx = __global__.fetch ? __global__ : __globalThis__;\nexports = ctx.fetch // To enable: import fetch from 'cross-fetch'\nexports.default = ctx.fetch // For TypeScript consumers without esModuleInterop.\nexports.fetch = ctx.fetch // To enable: import {fetch} from 'cross-fetch'\nexports.Headers = ctx.Headers\nexports.Request = ctx.Request\nexports.Response = ctx.Response\nmodule.exports = exports\n"], "mappings": "AAAA;AACA,IAAIA,UAAU,GACb,OAAOC,UAAU,KAAK,WAAW,IAAIA,UAAU,IAC/C,OAAOC,IAAI,KAAK,WAAW,IAAIA,IAAK,IACpC,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAO;AACzC;AACA,IAAIC,cAAc,GAAI,YAAY;EAClC,SAASC,CAACA,CAAA,EAAG;IACb,IAAI,CAACC,KAAK,GAAG,KAAK;IAClB,IAAI,CAACC,YAAY,GAAGP,UAAU,CAACO,YAAY;EAC3C;EACAF,CAAC,CAACG,SAAS,GAAGR,UAAU,CAAC,CAAC;EAC1B,OAAO,IAAIK,CAAC,CAAC,CAAC;AACd,CAAC,CAAE,CAAC;AACJ;AACA;AACA,CAAC,UAASJ,UAAU,EAAE;EAEtB,IAAIQ,UAAU,GAAI,UAAUC,OAAO,EAAE;IAEnC,IAAIP,MAAM,GACP,OAAOF,UAAU,KAAK,WAAW,IAAIA,UAAU,IAC/C,OAAOC,IAAI,KAAK,WAAW,IAAIA,IAAK,IACpC,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAO;IAE3C,IAAIQ,OAAO,GAAG;MACZC,YAAY,EAAE,iBAAiB,IAAIT,MAAM;MACzCU,QAAQ,EAAE,QAAQ,IAAIV,MAAM,IAAI,UAAU,IAAIW,MAAM;MACpDC,IAAI,EACF,YAAY,IAAIZ,MAAM,IACtB,MAAM,IAAIA,MAAM,IACf,YAAW;QACV,IAAI;UACF,IAAIa,IAAI,CAAC,CAAC;UACV,OAAO,IAAI;QACb,CAAC,CAAC,OAAOC,CAAC,EAAE;UACV,OAAO,KAAK;QACd;MACF,CAAC,CAAE,CAAC;MACNC,QAAQ,EAAE,UAAU,IAAIf,MAAM;MAC9BgB,WAAW,EAAE,aAAa,IAAIhB;IAChC,CAAC;IAED,SAASiB,UAAUA,CAACC,GAAG,EAAE;MACvB,OAAOA,GAAG,IAAIC,QAAQ,CAACd,SAAS,CAACe,aAAa,CAACF,GAAG,CAAC;IACrD;IAEA,IAAIV,OAAO,CAACQ,WAAW,EAAE;MACvB,IAAIK,WAAW,GAAG,CAChB,oBAAoB,EACpB,qBAAqB,EACrB,4BAA4B,EAC5B,qBAAqB,EACrB,sBAAsB,EACtB,qBAAqB,EACrB,sBAAsB,EACtB,uBAAuB,EACvB,uBAAuB,CACxB;MAED,IAAIC,iBAAiB,GACnBC,WAAW,CAACC,MAAM,IAClB,UAASN,GAAG,EAAE;QACZ,OAAOA,GAAG,IAAIG,WAAW,CAACI,OAAO,CAACC,MAAM,CAACrB,SAAS,CAACsB,QAAQ,CAACC,IAAI,CAACV,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;MAC7E,CAAC;IACL;IAEA,SAASW,aAAaA,CAACC,IAAI,EAAE;MAC3B,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;QAC5BA,IAAI,GAAGC,MAAM,CAACD,IAAI,CAAC;MACrB;MACA,IAAI,4BAA4B,CAACE,IAAI,CAACF,IAAI,CAAC,IAAIA,IAAI,KAAK,EAAE,EAAE;QAC1D,MAAM,IAAIG,SAAS,CAAC,2CAA2C,GAAGH,IAAI,GAAG,GAAG,CAAC;MAC/E;MACA,OAAOA,IAAI,CAACI,WAAW,CAAC,CAAC;IAC3B;IAEA,SAASC,cAAcA,CAACC,KAAK,EAAE;MAC7B,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7BA,KAAK,GAAGL,MAAM,CAACK,KAAK,CAAC;MACvB;MACA,OAAOA,KAAK;IACd;;IAEA;IACA,SAASC,WAAWA,CAACC,KAAK,EAAE;MAC1B,IAAIC,QAAQ,GAAG;QACbC,IAAI,EAAE,SAAAA,CAAA,EAAW;UACf,IAAIJ,KAAK,GAAGE,KAAK,CAACG,KAAK,CAAC,CAAC;UACzB,OAAO;YAACC,IAAI,EAAEN,KAAK,KAAKO,SAAS;YAAEP,KAAK,EAAEA;UAAK,CAAC;QAClD;MACF,CAAC;MAED,IAAI5B,OAAO,CAACE,QAAQ,EAAE;QACpB6B,QAAQ,CAAC5B,MAAM,CAAC4B,QAAQ,CAAC,GAAG,YAAW;UACrC,OAAOA,QAAQ;QACjB,CAAC;MACH;MAEA,OAAOA,QAAQ;IACjB;IAEA,SAASK,OAAOA,CAACC,OAAO,EAAE;MACxB,IAAI,CAACC,GAAG,GAAG,CAAC,CAAC;MAEb,IAAID,OAAO,YAAYD,OAAO,EAAE;QAC9BC,OAAO,CAACE,OAAO,CAAC,UAASX,KAAK,EAAEN,IAAI,EAAE;UACpC,IAAI,CAACkB,MAAM,CAAClB,IAAI,EAAEM,KAAK,CAAC;QAC1B,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM,IAAIa,KAAK,CAACC,OAAO,CAACL,OAAO,CAAC,EAAE;QACjCA,OAAO,CAACE,OAAO,CAAC,UAASI,MAAM,EAAE;UAC/B,IAAI,CAACH,MAAM,CAACG,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;QACnC,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM,IAAIN,OAAO,EAAE;QAClBnB,MAAM,CAAC0B,mBAAmB,CAACP,OAAO,CAAC,CAACE,OAAO,CAAC,UAASjB,IAAI,EAAE;UACzD,IAAI,CAACkB,MAAM,CAAClB,IAAI,EAAEe,OAAO,CAACf,IAAI,CAAC,CAAC;QAClC,CAAC,EAAE,IAAI,CAAC;MACV;IACF;IAEAc,OAAO,CAACvC,SAAS,CAAC2C,MAAM,GAAG,UAASlB,IAAI,EAAEM,KAAK,EAAE;MAC/CN,IAAI,GAAGD,aAAa,CAACC,IAAI,CAAC;MAC1BM,KAAK,GAAGD,cAAc,CAACC,KAAK,CAAC;MAC7B,IAAIiB,QAAQ,GAAG,IAAI,CAACP,GAAG,CAAChB,IAAI,CAAC;MAC7B,IAAI,CAACgB,GAAG,CAAChB,IAAI,CAAC,GAAGuB,QAAQ,GAAGA,QAAQ,GAAG,IAAI,GAAGjB,KAAK,GAAGA,KAAK;IAC7D,CAAC;IAEDQ,OAAO,CAACvC,SAAS,CAAC,QAAQ,CAAC,GAAG,UAASyB,IAAI,EAAE;MAC3C,OAAO,IAAI,CAACgB,GAAG,CAACjB,aAAa,CAACC,IAAI,CAAC,CAAC;IACtC,CAAC;IAEDc,OAAO,CAACvC,SAAS,CAACiD,GAAG,GAAG,UAASxB,IAAI,EAAE;MACrCA,IAAI,GAAGD,aAAa,CAACC,IAAI,CAAC;MAC1B,OAAO,IAAI,CAACyB,GAAG,CAACzB,IAAI,CAAC,GAAG,IAAI,CAACgB,GAAG,CAAChB,IAAI,CAAC,GAAG,IAAI;IAC/C,CAAC;IAEDc,OAAO,CAACvC,SAAS,CAACkD,GAAG,GAAG,UAASzB,IAAI,EAAE;MACrC,OAAO,IAAI,CAACgB,GAAG,CAACU,cAAc,CAAC3B,aAAa,CAACC,IAAI,CAAC,CAAC;IACrD,CAAC;IAEDc,OAAO,CAACvC,SAAS,CAACoD,GAAG,GAAG,UAAS3B,IAAI,EAAEM,KAAK,EAAE;MAC5C,IAAI,CAACU,GAAG,CAACjB,aAAa,CAACC,IAAI,CAAC,CAAC,GAAGK,cAAc,CAACC,KAAK,CAAC;IACvD,CAAC;IAEDQ,OAAO,CAACvC,SAAS,CAAC0C,OAAO,GAAG,UAASW,QAAQ,EAAEC,OAAO,EAAE;MACtD,KAAK,IAAI7B,IAAI,IAAI,IAAI,CAACgB,GAAG,EAAE;QACzB,IAAI,IAAI,CAACA,GAAG,CAACU,cAAc,CAAC1B,IAAI,CAAC,EAAE;UACjC4B,QAAQ,CAAC9B,IAAI,CAAC+B,OAAO,EAAE,IAAI,CAACb,GAAG,CAAChB,IAAI,CAAC,EAAEA,IAAI,EAAE,IAAI,CAAC;QACpD;MACF;IACF,CAAC;IAEDc,OAAO,CAACvC,SAAS,CAACuD,IAAI,GAAG,YAAW;MAClC,IAAItB,KAAK,GAAG,EAAE;MACd,IAAI,CAACS,OAAO,CAAC,UAASX,KAAK,EAAEN,IAAI,EAAE;QACjCQ,KAAK,CAACuB,IAAI,CAAC/B,IAAI,CAAC;MAClB,CAAC,CAAC;MACF,OAAOO,WAAW,CAACC,KAAK,CAAC;IAC3B,CAAC;IAEDM,OAAO,CAACvC,SAAS,CAACyD,MAAM,GAAG,YAAW;MACpC,IAAIxB,KAAK,GAAG,EAAE;MACd,IAAI,CAACS,OAAO,CAAC,UAASX,KAAK,EAAE;QAC3BE,KAAK,CAACuB,IAAI,CAACzB,KAAK,CAAC;MACnB,CAAC,CAAC;MACF,OAAOC,WAAW,CAACC,KAAK,CAAC;IAC3B,CAAC;IAEDM,OAAO,CAACvC,SAAS,CAAC0D,OAAO,GAAG,YAAW;MACrC,IAAIzB,KAAK,GAAG,EAAE;MACd,IAAI,CAACS,OAAO,CAAC,UAASX,KAAK,EAAEN,IAAI,EAAE;QACjCQ,KAAK,CAACuB,IAAI,CAAC,CAAC/B,IAAI,EAAEM,KAAK,CAAC,CAAC;MAC3B,CAAC,CAAC;MACF,OAAOC,WAAW,CAACC,KAAK,CAAC;IAC3B,CAAC;IAED,IAAI9B,OAAO,CAACE,QAAQ,EAAE;MACpBkC,OAAO,CAACvC,SAAS,CAACM,MAAM,CAAC4B,QAAQ,CAAC,GAAGK,OAAO,CAACvC,SAAS,CAAC0D,OAAO;IAChE;IAEA,SAASC,QAAQA,CAACC,IAAI,EAAE;MACtB,IAAIA,IAAI,CAACC,QAAQ,EAAE;QACjB,OAAOC,OAAO,CAACC,MAAM,CAAC,IAAInC,SAAS,CAAC,cAAc,CAAC,CAAC;MACtD;MACAgC,IAAI,CAACC,QAAQ,GAAG,IAAI;IACtB;IAEA,SAASG,eAAeA,CAACC,MAAM,EAAE;MAC/B,OAAO,IAAIH,OAAO,CAAC,UAASI,OAAO,EAAEH,MAAM,EAAE;QAC3CE,MAAM,CAACE,MAAM,GAAG,YAAW;UACzBD,OAAO,CAACD,MAAM,CAACG,MAAM,CAAC;QACxB,CAAC;QACDH,MAAM,CAACI,OAAO,GAAG,YAAW;UAC1BN,MAAM,CAACE,MAAM,CAACK,KAAK,CAAC;QACtB,CAAC;MACH,CAAC,CAAC;IACJ;IAEA,SAASC,qBAAqBA,CAAChE,IAAI,EAAE;MACnC,IAAI0D,MAAM,GAAG,IAAIO,UAAU,CAAC,CAAC;MAC7B,IAAIC,OAAO,GAAGT,eAAe,CAACC,MAAM,CAAC;MACrCA,MAAM,CAACS,iBAAiB,CAACnE,IAAI,CAAC;MAC9B,OAAOkE,OAAO;IAChB;IAEA,SAASE,cAAcA,CAACpE,IAAI,EAAE;MAC5B,IAAI0D,MAAM,GAAG,IAAIO,UAAU,CAAC,CAAC;MAC7B,IAAIC,OAAO,GAAGT,eAAe,CAACC,MAAM,CAAC;MACrCA,MAAM,CAACW,UAAU,CAACrE,IAAI,CAAC;MACvB,OAAOkE,OAAO;IAChB;IAEA,SAASI,qBAAqBA,CAACC,GAAG,EAAE;MAClC,IAAIC,IAAI,GAAG,IAAIC,UAAU,CAACF,GAAG,CAAC;MAC9B,IAAIG,KAAK,GAAG,IAAIrC,KAAK,CAACmC,IAAI,CAACG,MAAM,CAAC;MAElC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,IAAI,CAACG,MAAM,EAAEC,CAAC,EAAE,EAAE;QACpCF,KAAK,CAACE,CAAC,CAAC,GAAGzD,MAAM,CAAC0D,YAAY,CAACL,IAAI,CAACI,CAAC,CAAC,CAAC;MACzC;MACA,OAAOF,KAAK,CAACI,IAAI,CAAC,EAAE,CAAC;IACvB;IAEA,SAASC,WAAWA,CAACR,GAAG,EAAE;MACxB,IAAIA,GAAG,CAACS,KAAK,EAAE;QACb,OAAOT,GAAG,CAACS,KAAK,CAAC,CAAC,CAAC;MACrB,CAAC,MAAM;QACL,IAAIR,IAAI,GAAG,IAAIC,UAAU,CAACF,GAAG,CAACU,UAAU,CAAC;QACzCT,IAAI,CAAC3B,GAAG,CAAC,IAAI4B,UAAU,CAACF,GAAG,CAAC,CAAC;QAC7B,OAAOC,IAAI,CAACU,MAAM;MACpB;IACF;IAEA,SAASC,IAAIA,CAAA,EAAG;MACd,IAAI,CAAC7B,QAAQ,GAAG,KAAK;MAErB,IAAI,CAAC8B,SAAS,GAAG,UAAS/B,IAAI,EAAE;QAC9B;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACM,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACA,QAAQ;QAC7B,IAAI,CAAC+B,SAAS,GAAGhC,IAAI;QACrB,IAAI,CAACA,IAAI,EAAE;UACT,IAAI,CAACiC,SAAS,GAAG,EAAE;QACrB,CAAC,MAAM,IAAI,OAAOjC,IAAI,KAAK,QAAQ,EAAE;UACnC,IAAI,CAACiC,SAAS,GAAGjC,IAAI;QACvB,CAAC,MAAM,IAAIzD,OAAO,CAACI,IAAI,IAAIC,IAAI,CAACR,SAAS,CAACe,aAAa,CAAC6C,IAAI,CAAC,EAAE;UAC7D,IAAI,CAACkC,SAAS,GAAGlC,IAAI;QACvB,CAAC,MAAM,IAAIzD,OAAO,CAACO,QAAQ,IAAIqF,QAAQ,CAAC/F,SAAS,CAACe,aAAa,CAAC6C,IAAI,CAAC,EAAE;UACrE,IAAI,CAACoC,aAAa,GAAGpC,IAAI;QAC3B,CAAC,MAAM,IAAIzD,OAAO,CAACC,YAAY,IAAI6F,eAAe,CAACjG,SAAS,CAACe,aAAa,CAAC6C,IAAI,CAAC,EAAE;UAChF,IAAI,CAACiC,SAAS,GAAGjC,IAAI,CAACtC,QAAQ,CAAC,CAAC;QAClC,CAAC,MAAM,IAAInB,OAAO,CAACQ,WAAW,IAAIR,OAAO,CAACI,IAAI,IAAIK,UAAU,CAACgD,IAAI,CAAC,EAAE;UAClE,IAAI,CAACsC,gBAAgB,GAAGZ,WAAW,CAAC1B,IAAI,CAAC6B,MAAM,CAAC;UAChD;UACA,IAAI,CAACG,SAAS,GAAG,IAAIpF,IAAI,CAAC,CAAC,IAAI,CAAC0F,gBAAgB,CAAC,CAAC;QACpD,CAAC,MAAM,IAAI/F,OAAO,CAACQ,WAAW,KAAKO,WAAW,CAAClB,SAAS,CAACe,aAAa,CAAC6C,IAAI,CAAC,IAAI3C,iBAAiB,CAAC2C,IAAI,CAAC,CAAC,EAAE;UACxG,IAAI,CAACsC,gBAAgB,GAAGZ,WAAW,CAAC1B,IAAI,CAAC;QAC3C,CAAC,MAAM;UACL,IAAI,CAACiC,SAAS,GAAGjC,IAAI,GAAGvC,MAAM,CAACrB,SAAS,CAACsB,QAAQ,CAACC,IAAI,CAACqC,IAAI,CAAC;QAC9D;QAEA,IAAI,CAAC,IAAI,CAACpB,OAAO,CAACS,GAAG,CAAC,cAAc,CAAC,EAAE;UACrC,IAAI,OAAOW,IAAI,KAAK,QAAQ,EAAE;YAC5B,IAAI,CAACpB,OAAO,CAACY,GAAG,CAAC,cAAc,EAAE,0BAA0B,CAAC;UAC9D,CAAC,MAAM,IAAI,IAAI,CAAC0C,SAAS,IAAI,IAAI,CAACA,SAAS,CAACK,IAAI,EAAE;YAChD,IAAI,CAAC3D,OAAO,CAACY,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC0C,SAAS,CAACK,IAAI,CAAC;UACvD,CAAC,MAAM,IAAIhG,OAAO,CAACC,YAAY,IAAI6F,eAAe,CAACjG,SAAS,CAACe,aAAa,CAAC6C,IAAI,CAAC,EAAE;YAChF,IAAI,CAACpB,OAAO,CAACY,GAAG,CAAC,cAAc,EAAE,iDAAiD,CAAC;UACrF;QACF;MACF,CAAC;MAED,IAAIjD,OAAO,CAACI,IAAI,EAAE;QAChB,IAAI,CAACA,IAAI,GAAG,YAAW;UACrB,IAAI6F,QAAQ,GAAGzC,QAAQ,CAAC,IAAI,CAAC;UAC7B,IAAIyC,QAAQ,EAAE;YACZ,OAAOA,QAAQ;UACjB;UAEA,IAAI,IAAI,CAACN,SAAS,EAAE;YAClB,OAAOhC,OAAO,CAACI,OAAO,CAAC,IAAI,CAAC4B,SAAS,CAAC;UACxC,CAAC,MAAM,IAAI,IAAI,CAACI,gBAAgB,EAAE;YAChC,OAAOpC,OAAO,CAACI,OAAO,CAAC,IAAI1D,IAAI,CAAC,CAAC,IAAI,CAAC0F,gBAAgB,CAAC,CAAC,CAAC;UAC3D,CAAC,MAAM,IAAI,IAAI,CAACF,aAAa,EAAE;YAC7B,MAAM,IAAIK,KAAK,CAAC,sCAAsC,CAAC;UACzD,CAAC,MAAM;YACL,OAAOvC,OAAO,CAACI,OAAO,CAAC,IAAI1D,IAAI,CAAC,CAAC,IAAI,CAACqF,SAAS,CAAC,CAAC,CAAC;UACpD;QACF,CAAC;QAED,IAAI,CAAClF,WAAW,GAAG,YAAW;UAC5B,IAAI,IAAI,CAACuF,gBAAgB,EAAE;YACzB,IAAII,UAAU,GAAG3C,QAAQ,CAAC,IAAI,CAAC;YAC/B,IAAI2C,UAAU,EAAE;cACd,OAAOA,UAAU;YACnB;YACA,IAAIpF,WAAW,CAACC,MAAM,CAAC,IAAI,CAAC+E,gBAAgB,CAAC,EAAE;cAC7C,OAAOpC,OAAO,CAACI,OAAO,CACpB,IAAI,CAACgC,gBAAgB,CAACT,MAAM,CAACF,KAAK,CAChC,IAAI,CAACW,gBAAgB,CAACK,UAAU,EAChC,IAAI,CAACL,gBAAgB,CAACK,UAAU,GAAG,IAAI,CAACL,gBAAgB,CAACV,UAC3D,CACF,CAAC;YACH,CAAC,MAAM;cACL,OAAO1B,OAAO,CAACI,OAAO,CAAC,IAAI,CAACgC,gBAAgB,CAAC;YAC/C;UACF,CAAC,MAAM;YACL,OAAO,IAAI,CAAC3F,IAAI,CAAC,CAAC,CAACiG,IAAI,CAACjC,qBAAqB,CAAC;UAChD;QACF,CAAC;MACH;MAEA,IAAI,CAACkC,IAAI,GAAG,YAAW;QACrB,IAAIL,QAAQ,GAAGzC,QAAQ,CAAC,IAAI,CAAC;QAC7B,IAAIyC,QAAQ,EAAE;UACZ,OAAOA,QAAQ;QACjB;QAEA,IAAI,IAAI,CAACN,SAAS,EAAE;UAClB,OAAOnB,cAAc,CAAC,IAAI,CAACmB,SAAS,CAAC;QACvC,CAAC,MAAM,IAAI,IAAI,CAACI,gBAAgB,EAAE;UAChC,OAAOpC,OAAO,CAACI,OAAO,CAACW,qBAAqB,CAAC,IAAI,CAACqB,gBAAgB,CAAC,CAAC;QACtE,CAAC,MAAM,IAAI,IAAI,CAACF,aAAa,EAAE;UAC7B,MAAM,IAAIK,KAAK,CAAC,sCAAsC,CAAC;QACzD,CAAC,MAAM;UACL,OAAOvC,OAAO,CAACI,OAAO,CAAC,IAAI,CAAC2B,SAAS,CAAC;QACxC;MACF,CAAC;MAED,IAAI1F,OAAO,CAACO,QAAQ,EAAE;QACpB,IAAI,CAACA,QAAQ,GAAG,YAAW;UACzB,OAAO,IAAI,CAAC+F,IAAI,CAAC,CAAC,CAACD,IAAI,CAACE,MAAM,CAAC;QACjC,CAAC;MACH;MAEA,IAAI,CAACC,IAAI,GAAG,YAAW;QACrB,OAAO,IAAI,CAACF,IAAI,CAAC,CAAC,CAACD,IAAI,CAACI,IAAI,CAACC,KAAK,CAAC;MACrC,CAAC;MAED,OAAO,IAAI;IACb;;IAEA;IACA,IAAIC,OAAO,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,KAAK,CAAC;IAEjE,SAASC,eAAeA,CAACC,MAAM,EAAE;MAC/B,IAAIC,OAAO,GAAGD,MAAM,CAACE,WAAW,CAAC,CAAC;MAClC,OAAOJ,OAAO,CAAC1F,OAAO,CAAC6F,OAAO,CAAC,GAAG,CAAC,CAAC,GAAGA,OAAO,GAAGD,MAAM;IACzD;IAEA,SAASG,OAAOA,CAACC,KAAK,EAAEC,OAAO,EAAE;MAC/B,IAAI,EAAE,IAAI,YAAYF,OAAO,CAAC,EAAE;QAC9B,MAAM,IAAIvF,SAAS,CAAC,4FAA4F,CAAC;MACnH;MAEAyF,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;MACvB,IAAIzD,IAAI,GAAGyD,OAAO,CAACzD,IAAI;MAEvB,IAAIwD,KAAK,YAAYD,OAAO,EAAE;QAC5B,IAAIC,KAAK,CAACvD,QAAQ,EAAE;UAClB,MAAM,IAAIjC,SAAS,CAAC,cAAc,CAAC;QACrC;QACA,IAAI,CAAC0F,GAAG,GAAGF,KAAK,CAACE,GAAG;QACpB,IAAI,CAACC,WAAW,GAAGH,KAAK,CAACG,WAAW;QACpC,IAAI,CAACF,OAAO,CAAC7E,OAAO,EAAE;UACpB,IAAI,CAACA,OAAO,GAAG,IAAID,OAAO,CAAC6E,KAAK,CAAC5E,OAAO,CAAC;QAC3C;QACA,IAAI,CAACwE,MAAM,GAAGI,KAAK,CAACJ,MAAM;QAC1B,IAAI,CAACQ,IAAI,GAAGJ,KAAK,CAACI,IAAI;QACtB,IAAI,CAACC,MAAM,GAAGL,KAAK,CAACK,MAAM;QAC1B,IAAI,CAAC7D,IAAI,IAAIwD,KAAK,CAACxB,SAAS,IAAI,IAAI,EAAE;UACpChC,IAAI,GAAGwD,KAAK,CAACxB,SAAS;UACtBwB,KAAK,CAACvD,QAAQ,GAAG,IAAI;QACvB;MACF,CAAC,MAAM;QACL,IAAI,CAACyD,GAAG,GAAG5F,MAAM,CAAC0F,KAAK,CAAC;MAC1B;MAEA,IAAI,CAACG,WAAW,GAAGF,OAAO,CAACE,WAAW,IAAI,IAAI,CAACA,WAAW,IAAI,aAAa;MAC3E,IAAIF,OAAO,CAAC7E,OAAO,IAAI,CAAC,IAAI,CAACA,OAAO,EAAE;QACpC,IAAI,CAACA,OAAO,GAAG,IAAID,OAAO,CAAC8E,OAAO,CAAC7E,OAAO,CAAC;MAC7C;MACA,IAAI,CAACwE,MAAM,GAAGD,eAAe,CAACM,OAAO,CAACL,MAAM,IAAI,IAAI,CAACA,MAAM,IAAI,KAAK,CAAC;MACrE,IAAI,CAACQ,IAAI,GAAGH,OAAO,CAACG,IAAI,IAAI,IAAI,CAACA,IAAI,IAAI,IAAI;MAC7C,IAAI,CAACC,MAAM,GAAGJ,OAAO,CAACI,MAAM,IAAI,IAAI,CAACA,MAAM;MAC3C,IAAI,CAACC,QAAQ,GAAG,IAAI;MAEpB,IAAI,CAAC,IAAI,CAACV,MAAM,KAAK,KAAK,IAAI,IAAI,CAACA,MAAM,KAAK,MAAM,KAAKpD,IAAI,EAAE;QAC7D,MAAM,IAAIhC,SAAS,CAAC,2CAA2C,CAAC;MAClE;MACA,IAAI,CAAC+D,SAAS,CAAC/B,IAAI,CAAC;MAEpB,IAAI,IAAI,CAACoD,MAAM,KAAK,KAAK,IAAI,IAAI,CAACA,MAAM,KAAK,MAAM,EAAE;QACnD,IAAIK,OAAO,CAACM,KAAK,KAAK,UAAU,IAAIN,OAAO,CAACM,KAAK,KAAK,UAAU,EAAE;UAChE;UACA,IAAIC,aAAa,GAAG,eAAe;UACnC,IAAIA,aAAa,CAACjG,IAAI,CAAC,IAAI,CAAC2F,GAAG,CAAC,EAAE;YAChC;YACA,IAAI,CAACA,GAAG,GAAG,IAAI,CAACA,GAAG,CAACO,OAAO,CAACD,aAAa,EAAE,MAAM,GAAG,IAAIE,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC;UAC3E,CAAC,MAAM;YACL;YACA,IAAIC,aAAa,GAAG,IAAI;YACxB,IAAI,CAACV,GAAG,IAAI,CAACU,aAAa,CAACrG,IAAI,CAAC,IAAI,CAAC2F,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,GAAG,IAAIQ,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;UACtF;QACF;MACF;IACF;IAEAZ,OAAO,CAACnH,SAAS,CAACiI,KAAK,GAAG,YAAW;MACnC,OAAO,IAAId,OAAO,CAAC,IAAI,EAAE;QAACvD,IAAI,EAAE,IAAI,CAACgC;MAAS,CAAC,CAAC;IAClD,CAAC;IAED,SAASc,MAAMA,CAAC9C,IAAI,EAAE;MACpB,IAAIsE,IAAI,GAAG,IAAInC,QAAQ,CAAC,CAAC;MACzBnC,IAAI,CACDuE,IAAI,CAAC,CAAC,CACNC,KAAK,CAAC,GAAG,CAAC,CACV1F,OAAO,CAAC,UAAS2F,KAAK,EAAE;QACvB,IAAIA,KAAK,EAAE;UACT,IAAID,KAAK,GAAGC,KAAK,CAACD,KAAK,CAAC,GAAG,CAAC;UAC5B,IAAI3G,IAAI,GAAG2G,KAAK,CAAChG,KAAK,CAAC,CAAC,CAACyF,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;UAC5C,IAAI9F,KAAK,GAAGqG,KAAK,CAAC/C,IAAI,CAAC,GAAG,CAAC,CAACwC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;UAC/CK,IAAI,CAACvF,MAAM,CAAC2F,kBAAkB,CAAC7G,IAAI,CAAC,EAAE6G,kBAAkB,CAACvG,KAAK,CAAC,CAAC;QAClE;MACF,CAAC,CAAC;MACJ,OAAOmG,IAAI;IACb;IAEA,SAASK,YAAYA,CAACC,UAAU,EAAE;MAChC,IAAIhG,OAAO,GAAG,IAAID,OAAO,CAAC,CAAC;MAC3B;MACA;MACA,IAAIkG,mBAAmB,GAAGD,UAAU,CAACX,OAAO,CAAC,cAAc,EAAE,GAAG,CAAC;MACjE;MACA;MACA;MACAY,mBAAmB,CAChBL,KAAK,CAAC,IAAI,CAAC,CACX3F,GAAG,CAAC,UAASK,MAAM,EAAE;QACpB,OAAOA,MAAM,CAAC1B,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG0B,MAAM,CAAC4F,MAAM,CAAC,CAAC,EAAE5F,MAAM,CAACoC,MAAM,CAAC,GAAGpC,MAAM;MAC9E,CAAC,CAAC,CACDJ,OAAO,CAAC,UAASiG,IAAI,EAAE;QACtB,IAAIC,KAAK,GAAGD,IAAI,CAACP,KAAK,CAAC,GAAG,CAAC;QAC3B,IAAIS,GAAG,GAAGD,KAAK,CAACxG,KAAK,CAAC,CAAC,CAAC+F,IAAI,CAAC,CAAC;QAC9B,IAAIU,GAAG,EAAE;UACP,IAAI9G,KAAK,GAAG6G,KAAK,CAACvD,IAAI,CAAC,GAAG,CAAC,CAAC8C,IAAI,CAAC,CAAC;UAClC3F,OAAO,CAACG,MAAM,CAACkG,GAAG,EAAE9G,KAAK,CAAC;QAC5B;MACF,CAAC,CAAC;MACJ,OAAOS,OAAO;IAChB;IAEAkD,IAAI,CAACnE,IAAI,CAAC4F,OAAO,CAACnH,SAAS,CAAC;IAE5B,SAAS8I,QAAQA,CAACC,QAAQ,EAAE1B,OAAO,EAAE;MACnC,IAAI,EAAE,IAAI,YAAYyB,QAAQ,CAAC,EAAE;QAC/B,MAAM,IAAIlH,SAAS,CAAC,4FAA4F,CAAC;MACnH;MACA,IAAI,CAACyF,OAAO,EAAE;QACZA,OAAO,GAAG,CAAC,CAAC;MACd;MAEA,IAAI,CAAClB,IAAI,GAAG,SAAS;MACrB,IAAI,CAAC6C,MAAM,GAAG3B,OAAO,CAAC2B,MAAM,KAAK1G,SAAS,GAAG,GAAG,GAAG+E,OAAO,CAAC2B,MAAM;MACjE,IAAI,CAACC,EAAE,GAAG,IAAI,CAACD,MAAM,IAAI,GAAG,IAAI,IAAI,CAACA,MAAM,GAAG,GAAG;MACjD,IAAI,CAACE,UAAU,GAAG7B,OAAO,CAAC6B,UAAU,KAAK5G,SAAS,GAAG,EAAE,GAAG,EAAE,GAAG+E,OAAO,CAAC6B,UAAU;MACjF,IAAI,CAAC1G,OAAO,GAAG,IAAID,OAAO,CAAC8E,OAAO,CAAC7E,OAAO,CAAC;MAC3C,IAAI,CAAC8E,GAAG,GAAGD,OAAO,CAACC,GAAG,IAAI,EAAE;MAC5B,IAAI,CAAC3B,SAAS,CAACoD,QAAQ,CAAC;IAC1B;IAEArD,IAAI,CAACnE,IAAI,CAACuH,QAAQ,CAAC9I,SAAS,CAAC;IAE7B8I,QAAQ,CAAC9I,SAAS,CAACiI,KAAK,GAAG,YAAW;MACpC,OAAO,IAAIa,QAAQ,CAAC,IAAI,CAAClD,SAAS,EAAE;QAClCoD,MAAM,EAAE,IAAI,CAACA,MAAM;QACnBE,UAAU,EAAE,IAAI,CAACA,UAAU;QAC3B1G,OAAO,EAAE,IAAID,OAAO,CAAC,IAAI,CAACC,OAAO,CAAC;QAClC8E,GAAG,EAAE,IAAI,CAACA;MACZ,CAAC,CAAC;IACJ,CAAC;IAEDwB,QAAQ,CAACxE,KAAK,GAAG,YAAW;MAC1B,IAAI6E,QAAQ,GAAG,IAAIL,QAAQ,CAAC,IAAI,EAAE;QAACE,MAAM,EAAE,CAAC;QAAEE,UAAU,EAAE;MAAE,CAAC,CAAC;MAC9DC,QAAQ,CAAChD,IAAI,GAAG,OAAO;MACvB,OAAOgD,QAAQ;IACjB,CAAC;IAED,IAAIC,gBAAgB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IAEhDN,QAAQ,CAACO,QAAQ,GAAG,UAAS/B,GAAG,EAAE0B,MAAM,EAAE;MACxC,IAAII,gBAAgB,CAAChI,OAAO,CAAC4H,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;QAC3C,MAAM,IAAIM,UAAU,CAAC,qBAAqB,CAAC;MAC7C;MAEA,OAAO,IAAIR,QAAQ,CAAC,IAAI,EAAE;QAACE,MAAM,EAAEA,MAAM;QAAExG,OAAO,EAAE;UAAC+G,QAAQ,EAAEjC;QAAG;MAAC,CAAC,CAAC;IACvE,CAAC;IAEDpH,OAAO,CAACH,YAAY,GAAGJ,MAAM,CAACI,YAAY;IAC1C,IAAI;MACF,IAAIG,OAAO,CAACH,YAAY,CAAC,CAAC;IAC5B,CAAC,CAAC,OAAOyJ,GAAG,EAAE;MACZtJ,OAAO,CAACH,YAAY,GAAG,UAAS0J,OAAO,EAAEhI,IAAI,EAAE;QAC7C,IAAI,CAACgI,OAAO,GAAGA,OAAO;QACtB,IAAI,CAAChI,IAAI,GAAGA,IAAI;QAChB,IAAI6C,KAAK,GAAG+B,KAAK,CAACoD,OAAO,CAAC;QAC1B,IAAI,CAACC,KAAK,GAAGpF,KAAK,CAACoF,KAAK;MAC1B,CAAC;MACDxJ,OAAO,CAACH,YAAY,CAACC,SAAS,GAAGqB,MAAM,CAACsI,MAAM,CAACtD,KAAK,CAACrG,SAAS,CAAC;MAC/DE,OAAO,CAACH,YAAY,CAACC,SAAS,CAAC4J,WAAW,GAAG1J,OAAO,CAACH,YAAY;IACnE;IAEA,SAASD,KAAKA,CAACsH,KAAK,EAAEyC,IAAI,EAAE;MAC1B,OAAO,IAAI/F,OAAO,CAAC,UAASI,OAAO,EAAEH,MAAM,EAAE;QAC3C,IAAI+F,OAAO,GAAG,IAAI3C,OAAO,CAACC,KAAK,EAAEyC,IAAI,CAAC;QAEtC,IAAIC,OAAO,CAACrC,MAAM,IAAIqC,OAAO,CAACrC,MAAM,CAACsC,OAAO,EAAE;UAC5C,OAAOhG,MAAM,CAAC,IAAI7D,OAAO,CAACH,YAAY,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;QAClE;QAEA,IAAIiK,GAAG,GAAG,IAAIC,cAAc,CAAC,CAAC;QAE9B,SAASC,QAAQA,CAAA,EAAG;UAClBF,GAAG,CAACG,KAAK,CAAC,CAAC;QACb;QAEAH,GAAG,CAAC7F,MAAM,GAAG,YAAW;UACtB,IAAIkD,OAAO,GAAG;YACZ2B,MAAM,EAAEgB,GAAG,CAAChB,MAAM;YAClBE,UAAU,EAAEc,GAAG,CAACd,UAAU;YAC1B1G,OAAO,EAAE+F,YAAY,CAACyB,GAAG,CAACI,qBAAqB,CAAC,CAAC,IAAI,EAAE;UACzD,CAAC;UACD/C,OAAO,CAACC,GAAG,GAAG,aAAa,IAAI0C,GAAG,GAAGA,GAAG,CAACK,WAAW,GAAGhD,OAAO,CAAC7E,OAAO,CAACS,GAAG,CAAC,eAAe,CAAC;UAC3F,IAAIW,IAAI,GAAG,UAAU,IAAIoG,GAAG,GAAGA,GAAG,CAACb,QAAQ,GAAGa,GAAG,CAACM,YAAY;UAC9DC,UAAU,CAAC,YAAW;YACpBrG,OAAO,CAAC,IAAI4E,QAAQ,CAAClF,IAAI,EAAEyD,OAAO,CAAC,CAAC;UACtC,CAAC,EAAE,CAAC,CAAC;QACP,CAAC;QAED2C,GAAG,CAAC3F,OAAO,GAAG,YAAW;UACvBkG,UAAU,CAAC,YAAW;YACpBxG,MAAM,CAAC,IAAInC,SAAS,CAAC,wBAAwB,CAAC,CAAC;UACjD,CAAC,EAAE,CAAC,CAAC;QACP,CAAC;QAEDoI,GAAG,CAACQ,SAAS,GAAG,YAAW;UACzBD,UAAU,CAAC,YAAW;YACpBxG,MAAM,CAAC,IAAInC,SAAS,CAAC,wBAAwB,CAAC,CAAC;UACjD,CAAC,EAAE,CAAC,CAAC;QACP,CAAC;QAEDoI,GAAG,CAACS,OAAO,GAAG,YAAW;UACvBF,UAAU,CAAC,YAAW;YACpBxG,MAAM,CAAC,IAAI7D,OAAO,CAACH,YAAY,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;UAC3D,CAAC,EAAE,CAAC,CAAC;QACP,CAAC;QAED,SAAS2K,MAAMA,CAACpD,GAAG,EAAE;UACnB,IAAI;YACF,OAAOA,GAAG,KAAK,EAAE,IAAI3H,MAAM,CAAC4J,QAAQ,CAACoB,IAAI,GAAGhL,MAAM,CAAC4J,QAAQ,CAACoB,IAAI,GAAGrD,GAAG;UACxE,CAAC,CAAC,OAAO7G,CAAC,EAAE;YACV,OAAO6G,GAAG;UACZ;QACF;QAEA0C,GAAG,CAACY,IAAI,CAACd,OAAO,CAAC9C,MAAM,EAAE0D,MAAM,CAACZ,OAAO,CAACxC,GAAG,CAAC,EAAE,IAAI,CAAC;QAEnD,IAAIwC,OAAO,CAACvC,WAAW,KAAK,SAAS,EAAE;UACrCyC,GAAG,CAACa,eAAe,GAAG,IAAI;QAC5B,CAAC,MAAM,IAAIf,OAAO,CAACvC,WAAW,KAAK,MAAM,EAAE;UACzCyC,GAAG,CAACa,eAAe,GAAG,KAAK;QAC7B;QAEA,IAAI,cAAc,IAAIb,GAAG,EAAE;UACzB,IAAI7J,OAAO,CAACI,IAAI,EAAE;YAChByJ,GAAG,CAACc,YAAY,GAAG,MAAM;UAC3B,CAAC,MAAM,IACL3K,OAAO,CAACQ,WAAW,IACnBmJ,OAAO,CAACtH,OAAO,CAACS,GAAG,CAAC,cAAc,CAAC,IACnC6G,OAAO,CAACtH,OAAO,CAACS,GAAG,CAAC,cAAc,CAAC,CAAC7B,OAAO,CAAC,0BAA0B,CAAC,KAAK,CAAC,CAAC,EAC9E;YACA4I,GAAG,CAACc,YAAY,GAAG,aAAa;UAClC;QACF;QAEA,IAAIjB,IAAI,IAAI,OAAOA,IAAI,CAACrH,OAAO,KAAK,QAAQ,IAAI,EAAEqH,IAAI,CAACrH,OAAO,YAAYD,OAAO,CAAC,EAAE;UAClFlB,MAAM,CAAC0B,mBAAmB,CAAC8G,IAAI,CAACrH,OAAO,CAAC,CAACE,OAAO,CAAC,UAASjB,IAAI,EAAE;YAC9DuI,GAAG,CAACe,gBAAgB,CAACtJ,IAAI,EAAEK,cAAc,CAAC+H,IAAI,CAACrH,OAAO,CAACf,IAAI,CAAC,CAAC,CAAC;UAChE,CAAC,CAAC;QACJ,CAAC,MAAM;UACLqI,OAAO,CAACtH,OAAO,CAACE,OAAO,CAAC,UAASX,KAAK,EAAEN,IAAI,EAAE;YAC5CuI,GAAG,CAACe,gBAAgB,CAACtJ,IAAI,EAAEM,KAAK,CAAC;UACnC,CAAC,CAAC;QACJ;QAEA,IAAI+H,OAAO,CAACrC,MAAM,EAAE;UAClBqC,OAAO,CAACrC,MAAM,CAACuD,gBAAgB,CAAC,OAAO,EAAEd,QAAQ,CAAC;UAElDF,GAAG,CAACiB,kBAAkB,GAAG,YAAW;YAClC;YACA,IAAIjB,GAAG,CAACkB,UAAU,KAAK,CAAC,EAAE;cACxBpB,OAAO,CAACrC,MAAM,CAAC0D,mBAAmB,CAAC,OAAO,EAAEjB,QAAQ,CAAC;YACvD;UACF,CAAC;QACH;QAEAF,GAAG,CAACoB,IAAI,CAAC,OAAOtB,OAAO,CAAClE,SAAS,KAAK,WAAW,GAAG,IAAI,GAAGkE,OAAO,CAAClE,SAAS,CAAC;MAC/E,CAAC,CAAC;IACJ;IAEA9F,KAAK,CAACuL,QAAQ,GAAG,IAAI;IAErB,IAAI,CAAC1L,MAAM,CAACG,KAAK,EAAE;MACjBH,MAAM,CAACG,KAAK,GAAGA,KAAK;MACpBH,MAAM,CAAC4C,OAAO,GAAGA,OAAO;MACxB5C,MAAM,CAACwH,OAAO,GAAGA,OAAO;MACxBxH,MAAM,CAACmJ,QAAQ,GAAGA,QAAQ;IAC5B;IAEA5I,OAAO,CAACqC,OAAO,GAAGA,OAAO;IACzBrC,OAAO,CAACiH,OAAO,GAAGA,OAAO;IACzBjH,OAAO,CAAC4I,QAAQ,GAAGA,QAAQ;IAC3B5I,OAAO,CAACJ,KAAK,GAAGA,KAAK;IAErB,OAAOI,OAAO;EAEhB,CAAC,CAAE,CAAC,CAAC,CAAC;AACN,CAAC,EAAEN,cAAc,CAAC;AAClB;AACAA,cAAc,CAACE,KAAK,CAACwL,QAAQ,GAAG,IAAI;AACpC,OAAO1L,cAAc,CAACE,KAAK,CAACuL,QAAQ;AACpC;AACA,IAAIE,GAAG,GAAG/L,UAAU,CAACM,KAAK,GAAGN,UAAU,GAAGI,cAAc;AACxDM,OAAO,GAAGqL,GAAG,CAACzL,KAAK,EAAC;AACpBI,OAAO,CAACsL,OAAO,GAAGD,GAAG,CAACzL,KAAK,EAAC;AAC5BI,OAAO,CAACJ,KAAK,GAAGyL,GAAG,CAACzL,KAAK,EAAC;AAC1BI,OAAO,CAACqC,OAAO,GAAGgJ,GAAG,CAAChJ,OAAO;AAC7BrC,OAAO,CAACiH,OAAO,GAAGoE,GAAG,CAACpE,OAAO;AAC7BjH,OAAO,CAAC4I,QAAQ,GAAGyC,GAAG,CAACzC,QAAQ;AAC/B2C,MAAM,CAACvL,OAAO,GAAGA,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}