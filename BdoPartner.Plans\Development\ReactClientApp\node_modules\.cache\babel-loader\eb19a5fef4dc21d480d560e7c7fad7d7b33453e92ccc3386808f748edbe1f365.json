{"ast": null, "code": "import { createErrorClass } from './createErrorClass';\nexport var NotFoundError = createErrorClass(function (_super) {\n  return function NotFoundErrorImpl(message) {\n    _super(this);\n    this.name = 'NotFoundError';\n    this.message = message;\n  };\n});", "map": {"version": 3, "names": ["createErrorClass", "NotFoundError", "_super", "NotFoundErrorImpl", "message", "name"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\util\\NotFoundError.ts"], "sourcesContent": ["import { createErrorClass } from './createErrorClass';\n\nexport interface NotFoundError extends Error {}\n\nexport interface NotFoundErrorCtor {\n  /**\n   * @deprecated Internal implementation detail. Do not construct error instances.\n   * Cannot be tagged as internal: https://github.com/ReactiveX/rxjs/issues/6269\n   */\n  new (message: string): NotFoundError;\n}\n\n/**\n * An error thrown when a value or values are missing from an\n * observable sequence.\n *\n * @see {@link operators/single}\n */\nexport const NotFoundError: NotFoundErrorCtor = createErrorClass(\n  (_super) =>\n    function NotFoundErrorImpl(this: any, message: string) {\n      _super(this);\n      this.name = 'NotFoundError';\n      this.message = message;\n    }\n);\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,oBAAoB;AAkBrD,OAAO,IAAMC,aAAa,GAAsBD,gBAAgB,CAC9D,UAACE,MAAM;EACL,gBAASC,iBAAiBA,CAAYC,OAAe;IACnDF,MAAM,CAAC,IAAI,CAAC;IACZ,IAAI,CAACG,IAAI,GAAG,eAAe;IAC3B,IAAI,CAACD,OAAO,GAAGA,OAAO;EACxB,CAAC;AAJD,CAIC,CACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}