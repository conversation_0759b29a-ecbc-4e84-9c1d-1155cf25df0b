{"ast": null, "code": "import { scheduleAsyncIterable } from './scheduleAsyncIterable';\nimport { readableStreamLikeToAsyncGenerator } from '../util/isReadableStreamLike';\nexport function scheduleReadableStreamLike(input, scheduler) {\n  return scheduleAsyncIterable(readableStreamLikeToAsyncGenerator(input), scheduler);\n}", "map": {"version": 3, "names": ["scheduleAsyncIterable", "readableStreamLikeToAsyncGenerator", "scheduleReadableStreamLike", "input", "scheduler"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\scheduled\\scheduleReadableStreamLike.ts"], "sourcesContent": ["import { SchedulerLike, ReadableStreamLike } from '../types';\nimport { Observable } from '../Observable';\nimport { scheduleAsyncIterable } from './scheduleAsyncIterable';\nimport { readableStreamLikeToAsyncGenerator } from '../util/isReadableStreamLike';\n\nexport function scheduleReadableStreamLike<T>(input: ReadableStreamLike<T>, scheduler: SchedulerLike): Observable<T> {\n  return scheduleAsyncIterable(readableStreamLikeToAsyncGenerator(input), scheduler);\n}\n"], "mappings": "AAEA,SAASA,qBAAqB,QAAQ,yBAAyB;AAC/D,SAASC,kCAAkC,QAAQ,8BAA8B;AAEjF,OAAM,SAAUC,0BAA0BA,CAAIC,KAA4B,EAAEC,SAAwB;EAClG,OAAOJ,qBAAqB,CAACC,kCAAkC,CAACE,KAAK,CAAC,EAAEC,SAAS,CAAC;AACpF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}