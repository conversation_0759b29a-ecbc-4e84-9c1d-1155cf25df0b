{"ast": null, "code": "/**\r\n * Define reducer action's type name here.\r\n * Only works for \"core\" folder functions. \r\n */\nconst CORE_ACTIONS = {\n  SET_LANGUAGE: \"SET_LANGUAGE\"\n};\nexport default CORE_ACTIONS;", "map": {"version": 3, "names": ["CORE_ACTIONS", "SET_LANGUAGE"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/redux/actions.js"], "sourcesContent": ["/**\r\n * Define reducer action's type name here.\r\n * Only works for \"core\" folder functions. \r\n */\r\nconst CORE_ACTIONS = {\r\n    \r\n   SET_LANGUAGE: \"SET_LANGUAGE\",\r\n}\r\n\r\nexport default CORE_ACTIONS;"], "mappings": "AAAA;AACA;AACA;AACA;AACA,MAAMA,YAAY,GAAG;EAElBC,YAAY,EAAE;AACjB,CAAC;AAED,eAAeD,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}