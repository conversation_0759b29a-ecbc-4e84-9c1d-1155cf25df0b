{"ast": null, "code": "import axios from \"axios\";\nimport { loadingService } from \"../loading/loadingService\";\nimport APP_CONFIG from \"../config/appConfig\";\nimport { messageService } from \"../message/messageService\";\nimport AuthService from \"../auth/authService\";\n/**\r\n * It is instance of axios Http client, which works for http call.\r\n * Corporate with loading.jsx for a global progress bar process.\r\n * Corporate with loadingReducer.js\r\n *\r\n * Note: It is single tone.\r\n *\r\n * Reference: https://sumn2u.medium.com/global-progress-bar-on-api-s-call-in-react-5133f818d12a\r\n *\r\n */\nconst http = axios.create();\n\n/**\r\n * Corporate with http const to inject access_token to http request header.\r\n * Work for Identity Server 4 single sign on.\r\n * @returns Bearer token header string.\r\n */\nconst getAccessTokenHeader = () => {\n  const oidc = JSON.parse(\n  /**\r\n   * Note: It is OIDC-Client library default storage.\r\n   * The key format is defined by oidc-client library.\r\n   * Note: Developer no needs to modify the session key.\r\n   * */\n  sessionStorage.getItem(`oidc.user:${APP_CONFIG.iamDomain}:${APP_CONFIG.clientId}`));\n  if (!!oidc && !!oidc.access_token && !oidc.expired) {\n    return `Bearer ${oidc.access_token}`;\n  } else return \"\";\n};\n\n/** Get current language codes. */\nexport const getLanguageCode = () => {\n  //\n  // corporate with Redux/store.js\n  //\n  var languageCode = localStorage.getItem(`${APP_CONFIG.clientId}_CurrentLanguage`);\n  if (languageCode && languageCode.length > 0) return languageCode;else return \"en\";\n};\nhttp.interceptors.request.use(request => {\n  // Notify loading service to show progress spinner.\n  loadingService.httpRequestSent();\n  const tokenHeader = getAccessTokenHeader();\n  if (tokenHeader && tokenHeader.length > 0) {\n    request.headers[\"authorization\"] = tokenHeader;\n  }\n  const languageCode = getLanguageCode();\n  //\n  // Corporate with web api project middleware called \"RequestCultureMiddleware.cs\"\n  //\n  request.headers[\"currentLanguage\"] = languageCode;\n  return request;\n}, error => {\n  loadingService.error();\n  // Notify loading service to hide progress spinner.\n  return Promise.reject(error);\n});\nhttp.interceptors.response.use(response => {\n  // Notify loding service to hide progress spinner.\n  loadingService.httpResponseReceived();\n  return response;\n}, error => {\n  // Notify loading service to hide progress spinner.\n  loadingService.error();\n  if (error.response) {\n    switch (error.response.status) {\n      case 400:\n        messageService.errorToast(\"It is Bad Request\");\n        break;\n      case 401:\n        messageService.errorToast(\"Unauthorized Access. Redirecting to login...\");\n        // Auto-redirect to login on unauthorized access\n        handleUnauthorizedAccess();\n        break;\n      //case 404:\n      //  messageService.errorToast(\"Required access endpoint is No Found\");\n      //  break;\n      case 406:\n        messageService.errorToast(\"Request Not Acceptable\");\n        break;\n      case 403:\n        messageService.errorToast(\"Access Forbidden. Redirecting to login...\");\n        // Auto-redirect to login on forbidden access\n        handleUnauthorizedAccess();\n        break;\n      case 408:\n        messageService.errorToast(\"Request is Timeout\");\n        break;\n      default:\n        messageService.errorToast(\"Request process failed.\");\n        break;\n    }\n  }\n  return Promise.reject(error);\n});\n\n/**\r\n * Handle unauthorized access by redirecting to login\r\n * This function ensures consistent behavior across the entire application\r\n */\nconst handleUnauthorizedAccess = () => {\n  try {\n    // Create a new instance of AuthService to handle the redirect\n    const authService = new AuthService();\n\n    // Clear any stale authentication state\n    authService.userManager.clearStaleState();\n\n    // Store the current location for redirect after login\n    const currentPath = window.location.pathname + window.location.search;\n    localStorage.setItem(\"redirectUri\", currentPath);\n    console.log(\"Unauthorized access detected. Redirecting to login...\");\n    console.log(\"Current path stored for redirect:\", currentPath);\n\n    // Redirect to login\n    authService.signinRedirect().catch(error => {\n      console.error(\"Error during signin redirect:\", error);\n      // Fallback: navigate to home page if signin redirect fails\n      window.location.replace(APP_CONFIG.basePath);\n    });\n  } catch (error) {\n    console.error(\"Error handling unauthorized access:\", error);\n    // Fallback: navigate to home page\n    window.location.replace(APP_CONFIG.basePath);\n  }\n};\nexport default http;", "map": {"version": 3, "names": ["axios", "loadingService", "APP_CONFIG", "messageService", "AuthService", "http", "create", "getAccessTokenHeader", "oidc", "JSON", "parse", "sessionStorage", "getItem", "iamDomain", "clientId", "access_token", "expired", "getLanguageCode", "languageCode", "localStorage", "length", "interceptors", "request", "use", "httpRequestSent", "<PERSON><PERSON><PERSON><PERSON>", "headers", "error", "Promise", "reject", "response", "httpResponseReceived", "status", "errorToast", "handleUnauthorizedAccess", "authService", "userManager", "clearStaleState", "currentPath", "window", "location", "pathname", "search", "setItem", "console", "log", "signinRedirect", "catch", "replace", "basePath"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/core/http/httpClient.js"], "sourcesContent": ["import axios from \"axios\";\r\nimport { loadingService } from \"../loading/loadingService\";\r\nimport APP_CONFIG from \"../config/appConfig\";\r\nimport { messageService } from \"../message/messageService\";\r\nimport AuthService from \"../auth/authService\";\r\n/**\r\n * It is instance of axios Http client, which works for http call.\r\n * Corporate with loading.jsx for a global progress bar process.\r\n * Corporate with loadingReducer.js\r\n *\r\n * Note: It is single tone.\r\n *\r\n * Reference: https://sumn2u.medium.com/global-progress-bar-on-api-s-call-in-react-5133f818d12a\r\n *\r\n */\r\nconst http = axios.create();\r\n\r\n/**\r\n * Corporate with http const to inject access_token to http request header.\r\n * Work for Identity Server 4 single sign on.\r\n * @returns Bearer token header string.\r\n */\r\nconst getAccessTokenHeader = () => {\r\n  const oidc = JSON.parse(\r\n    /**\r\n     * Note: It is OIDC-Client library default storage.\r\n     * The key format is defined by oidc-client library.\r\n     * Note: Developer no needs to modify the session key.\r\n     * */\r\n    sessionStorage.getItem(\r\n      `oidc.user:${APP_CONFIG.iamDomain}:${APP_CONFIG.clientId}`\r\n    )\r\n  );\r\n  if (!!oidc && !!oidc.access_token && !oidc.expired) {\r\n    return `Bearer ${oidc.access_token}`;\r\n  } else return \"\";\r\n};\r\n\r\n/** Get current language codes. */\r\nexport const getLanguageCode = () => {\r\n  //\r\n  // corporate with Redux/store.js\r\n  //\r\n  var languageCode = localStorage.getItem(\r\n    `${APP_CONFIG.clientId}_CurrentLanguage`\r\n  );\r\n\r\n  if (languageCode && languageCode.length > 0) return languageCode;\r\n  else return \"en\";\r\n};\r\n\r\nhttp.interceptors.request.use(\r\n  (request) => {\r\n    // Notify loading service to show progress spinner.\r\n    loadingService.httpRequestSent();\r\n    const tokenHeader = getAccessTokenHeader();\r\n    if (tokenHeader && tokenHeader.length > 0) {\r\n      request.headers[\"authorization\"] = tokenHeader;\r\n    }\r\n    const languageCode = getLanguageCode();\r\n    //\r\n    // Corporate with web api project middleware called \"RequestCultureMiddleware.cs\"\r\n    //\r\n    request.headers[\"currentLanguage\"] = languageCode;\r\n    return request;\r\n  },\r\n  (error) => {\r\n    loadingService.error();\r\n    // Notify loading service to hide progress spinner.\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\nhttp.interceptors.response.use(\r\n  (response) => {\r\n    // Notify loding service to hide progress spinner.\r\n    loadingService.httpResponseReceived();\r\n    return response;\r\n  },\r\n  (error) => {\r\n    // Notify loading service to hide progress spinner.\r\n    loadingService.error();\r\n\r\n    if (error.response) {\r\n      switch (error.response.status) {\r\n        case 400:\r\n          messageService.errorToast(\"It is Bad Request\");\r\n          break;\r\n        case 401:\r\n          messageService.errorToast(\"Unauthorized Access. Redirecting to login...\");\r\n          // Auto-redirect to login on unauthorized access\r\n          handleUnauthorizedAccess();\r\n          break;\r\n        //case 404:\r\n        //  messageService.errorToast(\"Required access endpoint is No Found\");\r\n        //  break;\r\n        case 406:\r\n          messageService.errorToast(\"Request Not Acceptable\");\r\n          break;\r\n        case 403:\r\n          messageService.errorToast(\"Access Forbidden. Redirecting to login...\");\r\n          // Auto-redirect to login on forbidden access\r\n          handleUnauthorizedAccess();\r\n          break;\r\n        case 408:\r\n          messageService.errorToast(\"Request is Timeout\");\r\n          break;\r\n        default:\r\n          messageService.errorToast(\"Request process failed.\");\r\n          break;\r\n      }\r\n    }\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n/**\r\n * Handle unauthorized access by redirecting to login\r\n * This function ensures consistent behavior across the entire application\r\n */\r\nconst handleUnauthorizedAccess = () => {\r\n  try {\r\n    // Create a new instance of AuthService to handle the redirect\r\n    const authService = new AuthService();\r\n\r\n    // Clear any stale authentication state\r\n    authService.userManager.clearStaleState();\r\n\r\n    // Store the current location for redirect after login\r\n    const currentPath = window.location.pathname + window.location.search;\r\n    localStorage.setItem(\"redirectUri\", currentPath);\r\n\r\n    console.log(\"Unauthorized access detected. Redirecting to login...\");\r\n    console.log(\"Current path stored for redirect:\", currentPath);\r\n\r\n    // Redirect to login\r\n    authService.signinRedirect().catch((error) => {\r\n      console.error(\"Error during signin redirect:\", error);\r\n      // Fallback: navigate to home page if signin redirect fails\r\n      window.location.replace(APP_CONFIG.basePath);\r\n    });\r\n\r\n  } catch (error) {\r\n    console.error(\"Error handling unauthorized access:\", error);\r\n    // Fallback: navigate to home page\r\n    window.location.replace(APP_CONFIG.basePath);\r\n  }\r\n};\r\n\r\nexport default http;\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,cAAc,QAAQ,2BAA2B;AAC1D,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,SAASC,cAAc,QAAQ,2BAA2B;AAC1D,OAAOC,WAAW,MAAM,qBAAqB;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,IAAI,GAAGL,KAAK,CAACM,MAAM,CAAC,CAAC;;AAE3B;AACA;AACA;AACA;AACA;AACA,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;EACjC,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK;EACrB;AACJ;AACA;AACA;AACA;EACIC,cAAc,CAACC,OAAO,CACpB,aAAaV,UAAU,CAACW,SAAS,IAAIX,UAAU,CAACY,QAAQ,EAC1D,CACF,CAAC;EACD,IAAI,CAAC,CAACN,IAAI,IAAI,CAAC,CAACA,IAAI,CAACO,YAAY,IAAI,CAACP,IAAI,CAACQ,OAAO,EAAE;IAClD,OAAO,UAAUR,IAAI,CAACO,YAAY,EAAE;EACtC,CAAC,MAAM,OAAO,EAAE;AAClB,CAAC;;AAED;AACA,OAAO,MAAME,eAAe,GAAGA,CAAA,KAAM;EACnC;EACA;EACA;EACA,IAAIC,YAAY,GAAGC,YAAY,CAACP,OAAO,CACrC,GAAGV,UAAU,CAACY,QAAQ,kBACxB,CAAC;EAED,IAAII,YAAY,IAAIA,YAAY,CAACE,MAAM,GAAG,CAAC,EAAE,OAAOF,YAAY,CAAC,KAC5D,OAAO,IAAI;AAClB,CAAC;AAEDb,IAAI,CAACgB,YAAY,CAACC,OAAO,CAACC,GAAG,CAC1BD,OAAO,IAAK;EACX;EACArB,cAAc,CAACuB,eAAe,CAAC,CAAC;EAChC,MAAMC,WAAW,GAAGlB,oBAAoB,CAAC,CAAC;EAC1C,IAAIkB,WAAW,IAAIA,WAAW,CAACL,MAAM,GAAG,CAAC,EAAE;IACzCE,OAAO,CAACI,OAAO,CAAC,eAAe,CAAC,GAAGD,WAAW;EAChD;EACA,MAAMP,YAAY,GAAGD,eAAe,CAAC,CAAC;EACtC;EACA;EACA;EACAK,OAAO,CAACI,OAAO,CAAC,iBAAiB,CAAC,GAAGR,YAAY;EACjD,OAAOI,OAAO;AAChB,CAAC,EACAK,KAAK,IAAK;EACT1B,cAAc,CAAC0B,KAAK,CAAC,CAAC;EACtB;EACA,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAEDtB,IAAI,CAACgB,YAAY,CAACS,QAAQ,CAACP,GAAG,CAC3BO,QAAQ,IAAK;EACZ;EACA7B,cAAc,CAAC8B,oBAAoB,CAAC,CAAC;EACrC,OAAOD,QAAQ;AACjB,CAAC,EACAH,KAAK,IAAK;EACT;EACA1B,cAAc,CAAC0B,KAAK,CAAC,CAAC;EAEtB,IAAIA,KAAK,CAACG,QAAQ,EAAE;IAClB,QAAQH,KAAK,CAACG,QAAQ,CAACE,MAAM;MAC3B,KAAK,GAAG;QACN7B,cAAc,CAAC8B,UAAU,CAAC,mBAAmB,CAAC;QAC9C;MACF,KAAK,GAAG;QACN9B,cAAc,CAAC8B,UAAU,CAAC,8CAA8C,CAAC;QACzE;QACAC,wBAAwB,CAAC,CAAC;QAC1B;MACF;MACA;MACA;MACA,KAAK,GAAG;QACN/B,cAAc,CAAC8B,UAAU,CAAC,wBAAwB,CAAC;QACnD;MACF,KAAK,GAAG;QACN9B,cAAc,CAAC8B,UAAU,CAAC,2CAA2C,CAAC;QACtE;QACAC,wBAAwB,CAAC,CAAC;QAC1B;MACF,KAAK,GAAG;QACN/B,cAAc,CAAC8B,UAAU,CAAC,oBAAoB,CAAC;QAC/C;MACF;QACE9B,cAAc,CAAC8B,UAAU,CAAC,yBAAyB,CAAC;QACpD;IACJ;EACF;EACA,OAAOL,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACA;AACA;AACA;AACA,MAAMO,wBAAwB,GAAGA,CAAA,KAAM;EACrC,IAAI;IACF;IACA,MAAMC,WAAW,GAAG,IAAI/B,WAAW,CAAC,CAAC;;IAErC;IACA+B,WAAW,CAACC,WAAW,CAACC,eAAe,CAAC,CAAC;;IAEzC;IACA,MAAMC,WAAW,GAAGC,MAAM,CAACC,QAAQ,CAACC,QAAQ,GAAGF,MAAM,CAACC,QAAQ,CAACE,MAAM;IACrEvB,YAAY,CAACwB,OAAO,CAAC,aAAa,EAAEL,WAAW,CAAC;IAEhDM,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;IACpED,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEP,WAAW,CAAC;;IAE7D;IACAH,WAAW,CAACW,cAAc,CAAC,CAAC,CAACC,KAAK,CAAEpB,KAAK,IAAK;MAC5CiB,OAAO,CAACjB,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD;MACAY,MAAM,CAACC,QAAQ,CAACQ,OAAO,CAAC9C,UAAU,CAAC+C,QAAQ,CAAC;IAC9C,CAAC,CAAC;EAEJ,CAAC,CAAC,OAAOtB,KAAK,EAAE;IACdiB,OAAO,CAACjB,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;IAC3D;IACAY,MAAM,CAACC,QAAQ,CAACQ,OAAO,CAAC9C,UAAU,CAAC+C,QAAQ,CAAC;EAC9C;AACF,CAAC;AAED,eAAe5C,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}