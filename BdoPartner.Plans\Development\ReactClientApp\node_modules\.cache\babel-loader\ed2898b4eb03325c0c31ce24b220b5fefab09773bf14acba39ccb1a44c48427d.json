{"ast": null, "code": "var _jsxFileName = \"C:\\\\git\\\\Partner-Site\\\\BdoPartner.Plans\\\\Development\\\\ReactClientApp\\\\src\\\\pages\\\\notfound.jsx\";\nimport React from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const NotFound = () => {\n  return /*#__PURE__*/_jsxDEV(\"h2\", {\n    children: \"The specified page is not existing.\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 4,\n    columnNumber: 10\n  }, this);\n};\n_c = NotFound;\nvar _c;\n$RefreshReg$(_c, \"NotFound\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "NotFound", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/pages/notfound.jsx"], "sourcesContent": ["import React from \"react\";\r\n\r\nexport const NotFound = () => {\r\n  return <h2>The specified page is not existing.</h2>;\r\n};\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,OAAO,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAC5B,oBAAOD,OAAA;IAAAE,QAAA,EAAI;EAAmC;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAI,CAAC;AACrD,CAAC;AAACC,EAAA,GAFWN,QAAQ;AAAA,IAAAM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}