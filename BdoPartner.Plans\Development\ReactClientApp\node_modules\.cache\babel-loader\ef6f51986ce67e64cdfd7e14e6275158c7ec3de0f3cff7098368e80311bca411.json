{"ast": null, "code": "import { reduce } from './reduce';\nexport function count(predicate) {\n  return reduce(function (total, value, i) {\n    return !predicate || predicate(value, i) ? total + 1 : total;\n  }, 0);\n}", "map": {"version": 3, "names": ["reduce", "count", "predicate", "total", "value", "i"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\count.ts"], "sourcesContent": ["import { OperatorFunction } from '../types';\nimport { reduce } from './reduce';\n\n/**\n * Counts the number of emissions on the source and emits that number when the\n * source completes.\n *\n * <span class=\"informal\">Tells how many values were emitted, when the source\n * completes.</span>\n *\n * ![](count.png)\n *\n * `count` transforms an Observable that emits values into an Observable that\n * emits a single value that represents the number of values emitted by the\n * source Observable. If the source Observable terminates with an error, `count`\n * will pass this error notification along without emitting a value first. If\n * the source Observable does not terminate at all, `count` will neither emit\n * a value nor terminate. This operator takes an optional `predicate` function\n * as argument, in which case the output emission will represent the number of\n * source values that matched `true` with the `predicate`.\n *\n * ## Examples\n *\n * Counts how many seconds have passed before the first click happened\n *\n * ```ts\n * import { interval, fromEvent, takeUntil, count } from 'rxjs';\n *\n * const seconds = interval(1000);\n * const clicks = fromEvent(document, 'click');\n * const secondsBeforeClick = seconds.pipe(takeUntil(clicks));\n * const result = secondsBeforeClick.pipe(count());\n * result.subscribe(x => console.log(x));\n * ```\n *\n * Counts how many odd numbers are there between 1 and 7\n *\n * ```ts\n * import { range, count } from 'rxjs';\n *\n * const numbers = range(1, 7);\n * const result = numbers.pipe(count(i => i % 2 === 1));\n * result.subscribe(x => console.log(x));\n * // Results in:\n * // 4\n * ```\n *\n * @see {@link max}\n * @see {@link min}\n * @see {@link reduce}\n *\n * @param predicate A function that is used to analyze the value and the index and\n * determine whether or not to increment the count. Return `true` to increment the count,\n * and return `false` to keep the count the same.\n * If the predicate is not provided, every value will be counted.\n * @return A function that returns an Observable that emits one number that\n * represents the count of emissions.\n */\nexport function count<T>(predicate?: (value: T, index: number) => boolean): OperatorFunction<T, number> {\n  return reduce((total, value, i) => (!predicate || predicate(value, i) ? total + 1 : total), 0);\n}\n"], "mappings": "AACA,SAASA,MAAM,QAAQ,UAAU;AAyDjC,OAAM,SAAUC,KAAKA,CAAIC,SAAgD;EACvE,OAAOF,MAAM,CAAC,UAACG,KAAK,EAAEC,KAAK,EAAEC,CAAC;IAAK,OAAC,CAACH,SAAS,IAAIA,SAAS,CAACE,KAAK,EAAEC,CAAC,CAAC,GAAGF,KAAK,GAAG,CAAC,GAAGA,KAAK;EAAtD,CAAuD,EAAE,CAAC,CAAC;AAChG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}