{"ast": null, "code": "var _jsxFileName = \"C:\\\\git\\\\Partner-Site\\\\BdoPartner.Plans\\\\Development\\\\ReactClientApp\\\\src\\\\components\\\\admin\\\\QuestionnaireDesignerCore.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef, forwardRef, useImperativeHandle } from \"react\";\nimport { SurveyCreatorComponent, SurveyCreator } from \"survey-creator-react\";\nimport { Action, SvgRegistry } from \"survey-core\";\nimport { Button } from \"primereact/button\";\nimport { Toast } from \"primereact/toast\";\nimport { Dialog } from \"primereact/dialog\";\nimport { ConfirmDialog, confirmDialog } from \"primereact/confirmdialog\";\nimport { loadingService } from \"../../core/loading/loadingService\";\nimport { messageService } from \"../../core/message/messageService\";\nimport { configureSurveyJSLicense } from \"../../core/surveyjs/licenseConfig\";\nimport { registerCustomPropertiesForRuntime } from \"../../core/utils/surveyCustomPropertiesUtils\";\n\n// Import Survey.js CSS\nimport \"survey-creator-core/survey-creator-core.css\";\n\n/**\r\n * Setup custom properties for SurveyJS Creator\r\n * Uses the shared utility for consistent property registration\r\n * @param {number} questionnaireYear - The year of the questionnaire being designed\r\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst setupCustomProperties = async (questionnaireYear = null) => {\n  await registerCustomPropertiesForRuntime({\n    isDesigner: true,\n    questionnaireYear,\n    skipDuplicateCheck: true\n  });\n};\n\n/**\r\n * Register custom SVG icon for Custom Settings category\r\n */\nconst registerCustomSettingsIcon = () => {\n  // Register the icon with SurveyJS SvgRegistry\n  SvgRegistry.registerIconFromSvg(\"icon-customsettings\", '<svg viewBox=\"0 0 24 24\"><path d=\"M21,9L17,5V8H10V10H17V13M7,11L3,15L7,19V16H14V14H7V11Z\"/></svg>');\n};\n\n/**\r\n * Core questionnaire designer component that handles the Survey.js creator logic\r\n * @param {Object} props - Component props\r\n * @param {Object} props.questionnaire - The questionnaire data\r\n * @param {string} props.surveyName - The survey name\r\n * @param {number} props.selectedYear - The selected year\r\n * @param {boolean} props.isQuestionnaireLoaded - Whether questionnaire is loaded\r\n * @param {boolean} props.isReadonly - Whether the form creator should be in readonly mode\r\n * @param {Function} props.onSave - Callback for save action\r\n * @param {Function} props.onPublish - Callback for publish action\r\n * @param {Function} props.onBackToList - Callback for back to list action\r\n */\nexport const QuestionnaireDesignerCore = /*#__PURE__*/_s(/*#__PURE__*/forwardRef(_c = _s(({\n  questionnaire,\n  surveyName,\n  selectedYear,\n  isQuestionnaireLoaded,\n  isReadonly,\n  onSave,\n  onPublish,\n  onBackToList\n}, ref) => {\n  _s();\n  const [showPublishDialog, setShowPublishDialog] = useState(false);\n  const [creatorReady, setCreatorReady] = useState(false);\n  const toast = useRef(null);\n  const creatorRef = useRef(null);\n  //const autoSaveTimeoutRef = useRef(null);\n\n  // Year options for dropdown\n  const currentYear = new Date().getFullYear();\n  const yearOptions = [];\n  for (let i = currentYear - 5; i <= currentYear + 5; i++) {\n    yearOptions.push({\n      label: i.toString(),\n      value: i\n    });\n  }\n  useEffect(() => {\n    const initializeCreator = async () => {\n      // Configure Survey.js commercial license before initializing creator\n      configureSurveyJSLicense();\n\n      // Initialize Survey Creator with BDO theme first\n      const creator = new SurveyCreator({\n        showLogicTab: true,\n        showTranslationTab: false,\n        showEmbeddedSurveyTab: false,\n        showJSONEditorTab: true,\n        showTestSurveyTab: true,\n        showPropertyGrid: true,\n        showToolbox: true,\n        isAutoSave: false,\n        readOnly: isReadonly || false\n      });\n\n      // Apply BDO theme colors\n      creator.theme = {\n        cssVariables: {\n          \"--sjs-primary-backcolor\": \"#ED1A3B\",\n          \"--sjs-primary-forecolor\": \"#ffffff\",\n          \"--sjs-secondary-backcolor\": \"#f3f2f1\",\n          \"--sjs-secondary-forecolor\": \"#1f1f1f\",\n          \"--sjs-shadow-small\": \"0px 1px 2px 0px rgba(0, 0, 0, 0.15)\",\n          \"--sjs-shadow-medium\": \"0px 2px 6px 0px rgba(0, 0, 0, 0.1)\",\n          \"--sjs-shadow-large\": \"0px 8px 16px 0px rgba(0, 0, 0, 0.1)\",\n          \"--sjs-border-default\": \"#e0e0e0\",\n          \"--sjs-border-light\": \"#f0f0f0\"\n        }\n      };\n\n      // Disable custom toolbar buttons (they are now external)\n      const saveAction = new Action({\n        id: \"save-survey\",\n        visible: false,\n        // Disabled - now external\n        title: \"Save\",\n        tooltip: \"Save questionnaire as draft\",\n        action: () => {\n          // Use the creator instance directly instead of the ref\n          saveSurveyWithCreator(creator);\n        }\n      });\n      creator.toolbarItems.push(saveAction);\n      creator.toolbarItems.push(new Action({\n        id: \"publish-survey\",\n        visible: false,\n        // Disabled - now external\n        title: \"Publish\",\n        tooltip: \"Publish questionnaire\",\n        action: () => setShowPublishDialog(true)\n      }));\n      creator.toolbarItems.push(new Action({\n        id: \"back-to-list\",\n        visible: false,\n        // Disabled - now external\n        title: \"Back to List\",\n        tooltip: \"Return to questionnaire list\",\n        action: () => handleBackToList()\n      }));\n\n      // // Enable auto-save\n      // creator.onModified.add(() => {\n      //   clearAutoSaveTimeout();\n      //   autoSaveTimeoutRef.current = setTimeout(() => {\n      //     if (onSave && creatorRef.current) {\n      //       const surveyJson = creatorRef.current.JSON;\n      //       onSave(surveyJson, false); // Auto-save without showing success message\n      //     }\n      //   }, 10000); // Auto-save after 10 seconds of inactivity\n      // });\n\n      // Register the custom icon\n      registerCustomSettingsIcon();\n\n      // Assign icon to the custom category in the property grid\n      creator.onSurveyInstanceCreated.add((_, options) => {\n        if (options.area === \"property-grid\") {\n          const customCategory = options.survey.getPageByName(\"customSettings\");\n          if (customCategory) {\n            customCategory.iconName = \"icon-customsettings\";\n            console.log(\"Assigned custom icon to Custom Settings category\");\n          }\n        }\n      });\n\n      // Setup custom properties for Form Creator with questionnaire year - AWAIT this to ensure it completes\n      await setupCustomProperties(selectedYear);\n      console.log(\"Custom properties setup completed\");\n      creatorRef.current = creator;\n      setCreatorReady(true);\n      console.log(\"Creator initialized and assigned to ref:\", creator);\n    };\n    initializeCreator();\n\n    // return () => {\n    //   clearAutoSaveTimeout();\n    // };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [isReadonly]);\n\n  //\n  // Load questionnaire data into creator when both are available\n  // Note: We only load the draft definition from the questionnaire record to form creator.\n  // Wait for creatorReady to ensure custom properties are set up before loading JSON\n  //\n  useEffect(() => {\n    if (creatorRef.current && questionnaire && creatorReady) {\n      const surveyJson = questionnaire.draftDefinitionJson ? JSON.parse(questionnaire.draftDefinitionJson) : {\n        title: questionnaire.name,\n        pages: []\n      };\n      creatorRef.current.JSON = surveyJson;\n      console.log(\"Loaded questionnaire data into creator via useEffect:\", surveyJson);\n    }\n  }, [questionnaire, creatorReady]);\n\n  // Update save button enabled state based on questionnaire loading\n  useEffect(() => {\n    if (creatorRef.current && creatorReady) {\n      const saveAction = creatorRef.current.toolbarItems.find(item => item.id === \"save-survey\");\n      if (saveAction) {\n        saveAction.enabled = isQuestionnaireLoaded && !!questionnaire && !!questionnaire.id;\n        console.log(\"Save button enabled state:\", saveAction.enabled, {\n          isQuestionnaireLoaded,\n          hasQuestionnaire: !!questionnaire,\n          hasId: !!(questionnaire !== null && questionnaire !== void 0 && questionnaire.id)\n        });\n      }\n    }\n  }, [isQuestionnaireLoaded, questionnaire, creatorReady]);\n\n  // Expose methods to parent component\n  useImperativeHandle(ref, () => ({\n    getSurveyJson: () => {\n      return creatorRef.current ? creatorRef.current.JSON : null;\n    }\n  }));\n\n  // const clearAutoSaveTimeout = () => {\n  //   if (autoSaveTimeoutRef.current) {\n  //     clearTimeout(autoSaveTimeoutRef.current);\n  //     autoSaveTimeoutRef.current = null;\n  //   }\n  // };\n\n  // Save survey using creator reference (for external save button)\n  const saveSurvey = async (showMessage = true) => {\n    if (!creatorRef.current) {\n      console.error(\"Creator reference is null - cannot save survey\");\n      if (showMessage) {\n        messageService.errorToast(\"Form designer not ready. Please try again.\");\n      }\n      return;\n    }\n    return await saveSurveyWithCreator(creatorRef.current, showMessage);\n  };\n\n  // Save survey using creator instance (for internal save button)\n  const saveSurveyWithCreator = async (creator, showMessage = true) => {\n    console.log(\"Save attempt - Creator:\", !!creator, \"Questionnaire:\", !!questionnaire, \"Loaded:\", isQuestionnaireLoaded);\n    console.log(\"Save attempt - Survey name:\", surveyName, \"Year:\", selectedYear);\n    if (!creator) {\n      console.error(\"Creator instance is null - cannot save survey\");\n      if (showMessage) {\n        messageService.errorToast(\"Form designer not ready. Please try again.\");\n      }\n      return;\n    }\n\n    // Check if questionnaire data is loaded\n    if (!questionnaire || !isQuestionnaireLoaded || !questionnaire.id) {\n      console.error(\"Questionnaire data not loaded or invalid - cannot save survey\", {\n        questionnaire: !!questionnaire,\n        isQuestionnaireLoaded,\n        questionnaireId: questionnaire === null || questionnaire === void 0 ? void 0 : questionnaire.id\n      });\n      if (showMessage) {\n        messageService.errorToast(\"Questionnaire data not loaded or invalid. Please wait and try again.\");\n      }\n      return;\n    }\n\n    // Check if survey name and year are available\n    if (!surveyName || !selectedYear) {\n      console.error(\"Survey name or year not available - cannot save survey\", {\n        surveyName,\n        selectedYear\n      });\n      if (showMessage) {\n        messageService.errorToast(\"Survey name or year not available. Please check the form fields.\");\n      }\n      return;\n    }\n    try {\n      if (showMessage) {\n        loadingService.httpRequestSent();\n      }\n\n      // Get the current survey JSON from the creator\n      const surveyJson = creator.JSON;\n      console.log(\"Current survey JSON:\", surveyJson);\n\n      // Ensure we have valid survey data\n      if (!surveyJson || Object.keys(surveyJson).length === 0) {\n        console.error(\"Survey JSON is empty or invalid\");\n        if (showMessage) {\n          messageService.errorToast(\"No form data to save. Please add some questions first.\");\n        }\n        return;\n      }\n\n      // Call the parent's save handler\n      if (onSave) {\n        await onSave(surveyJson, showMessage);\n      }\n    } catch (error) {\n      console.error(\"Error saving questionnaire:\", error);\n      if (showMessage) {\n        messageService.errorToast(error.message || \"Error saving questionnaire\");\n      }\n    } finally {\n      if (showMessage) {\n        loadingService.httpResponseReceived();\n      }\n    }\n  };\n  const publishSurvey = async () => {\n    if (!creatorRef.current) return;\n    try {\n      loadingService.httpRequestSent();\n      const surveyJson = creatorRef.current.JSON;\n\n      // Call the parent's publish handler\n      if (onPublish) {\n        await onPublish(surveyJson);\n      }\n      setShowPublishDialog(false);\n    } catch (error) {\n      console.error(\"Error publishing questionnaire:\", error);\n      messageService.errorToast(error.message || \"Error publishing questionnaire\");\n    } finally {\n      loadingService.httpResponseReceived();\n    }\n  };\n  const handleBackToList = () => {\n    if (creatorRef.current && creatorRef.current.isModified) {\n      confirmDialog({\n        message: \"You have unsaved changes. Do you want to save before leaving?\",\n        header: \"Unsaved Changes\",\n        icon: \"pi pi-exclamation-triangle\",\n        accept: async () => {\n          await saveSurvey();\n          if (onBackToList) {\n            onBackToList();\n          }\n        },\n        reject: () => {\n          if (onBackToList) {\n            onBackToList();\n          }\n        }\n      });\n    } else {\n      if (onBackToList) {\n        onBackToList();\n      }\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Toast, {\n      ref: toast\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 355,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(ConfirmDialog, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 356,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        height: \"calc(100vh - 200px)\"\n      },\n      children: creatorReady && creatorRef.current ? /*#__PURE__*/_jsxDEV(SurveyCreatorComponent, {\n        creator: creatorRef.current\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 361,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-content-center align-items-center\",\n        style: {\n          height: \"100%\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"pi pi-spinner pi-spin\",\n          style: {\n            fontSize: \"2rem\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 364,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"ml-2\",\n          children: \"Initializing form designer...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 363,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 359,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      header: (questionnaire === null || questionnaire === void 0 ? void 0 : questionnaire.status) === 1 ? \"Republish Questionnaire\" : \"Publish Questionnaire\",\n      visible: showPublishDialog,\n      style: {\n        width: \"450px\"\n      },\n      onHide: () => setShowPublishDialog(false),\n      footer: /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          label: \"Cancel\",\n          icon: \"pi pi-times\",\n          onClick: () => setShowPublishDialog(false),\n          className: \"p-button-text\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          label: (questionnaire === null || questionnaire === void 0 ? void 0 : questionnaire.status) === 1 ? \"Republish\" : \"Publish\",\n          icon: \"pi pi-send\",\n          onClick: publishSurvey,\n          className: \"action\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 379,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 377,\n        columnNumber: 13\n      }, this),\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: (questionnaire === null || questionnaire === void 0 ? void 0 : questionnaire.status) === 1 ? \"Are you sure you want to republish this questionnaire? This will update the published version with your current draft changes.\" : \"Are you sure you want to publish this questionnaire? This will make it available for use and cannot be easily undone.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 383,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 371,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 354,\n    columnNumber: 7\n  }, this);\n}, \"qPDhHNaWz/wi20BTGbDQC849fT4=\")), \"qPDhHNaWz/wi20BTGbDQC849fT4=\");\n\n// Export the utility functions for use in other components\n_c2 = QuestionnaireDesignerCore;\nexport { setupCustomProperties, registerCustomSettingsIcon };\nvar _c, _c2;\n$RefreshReg$(_c, \"QuestionnaireDesignerCore$forwardRef\");\n$RefreshReg$(_c2, \"QuestionnaireDesignerCore\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "forwardRef", "useImperativeHandle", "SurveyCreatorComponent", "SurveyCreator", "Action", "SvgRegistry", "<PERSON><PERSON>", "Toast", "Dialog", "ConfirmDialog", "confirmDialog", "loadingService", "messageService", "configureSurveyJSLicense", "registerCustomPropertiesForRuntime", "jsxDEV", "_jsxDEV", "setupCustomProperties", "questionnaireYear", "isDesigner", "skipDuplicate<PERSON><PERSON>ck", "registerCustomSettingsIcon", "registerIconFromSvg", "QuestionnaireDesignerCore", "_s", "_c", "questionnaire", "surveyName", "selected<PERSON>ear", "isQuestionnaireLoaded", "is<PERSON><PERSON><PERSON>ly", "onSave", "onPublish", "onBackToList", "ref", "showPublishDialog", "setShowPublishDialog", "<PERSON><PERSON><PERSON><PERSON>", "setCreatorReady", "toast", "creator<PERSON><PERSON>", "currentYear", "Date", "getFullYear", "yearOptions", "i", "push", "label", "toString", "value", "initializeCreator", "creator", "showLogicTab", "showTranslationTab", "showEmbeddedSurveyTab", "showJSONEditorTab", "showTestSurveyTab", "showPropertyGrid", "showToolbox", "isAutoSave", "readOnly", "theme", "cssVariables", "saveAction", "id", "visible", "title", "tooltip", "action", "saveSurveyWithCreator", "toolbarItems", "handleBackToList", "onSurveyInstanceCreated", "add", "_", "options", "area", "customCategory", "survey", "getPageByName", "iconName", "console", "log", "current", "surveyJson", "draftDefinitionJson", "JSON", "parse", "name", "pages", "find", "item", "enabled", "hasQuestionnaire", "hasId", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "save<PERSON>urvey", "showMessage", "error", "errorToast", "questionnaireId", "httpRequestSent", "Object", "keys", "length", "message", "httpResponseReceived", "publishSurvey", "isModified", "header", "icon", "accept", "reject", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "height", "className", "fontSize", "status", "width", "onHide", "footer", "onClick", "_c2", "$RefreshReg$"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/components/admin/QuestionnaireDesignerCore.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef, forwardRef, useImperativeHandle } from \"react\";\r\nimport { SurveyCreatorComponent, SurveyCreator } from \"survey-creator-react\";\r\nimport { Action, SvgRegistry } from \"survey-core\";\r\nimport { Button } from \"primereact/button\";\r\nimport { Toast } from \"primereact/toast\";\r\nimport { Dialog } from \"primereact/dialog\";\r\nimport { ConfirmDialog, confirmDialog } from \"primereact/confirmdialog\";\r\nimport { loadingService } from \"../../core/loading/loadingService\";\r\nimport { messageService } from \"../../core/message/messageService\";\r\nimport { configureSurveyJSLicense } from \"../../core/surveyjs/licenseConfig\";\r\nimport { registerCustomPropertiesForRuntime } from \"../../core/utils/surveyCustomPropertiesUtils\";\r\n\r\n// Import Survey.js CSS\r\nimport \"survey-creator-core/survey-creator-core.css\";\r\n\r\n/**\r\n * Setup custom properties for SurveyJS Creator\r\n * Uses the shared utility for consistent property registration\r\n * @param {number} questionnaireYear - The year of the questionnaire being designed\r\n */\r\nconst setupCustomProperties = async (questionnaireYear = null) => {\r\n  await registerCustomPropertiesForRuntime({\r\n    isDesigner: true,\r\n    questionnaireYear,\r\n    skipDuplicateCheck: true,\r\n  });\r\n};\r\n\r\n/**\r\n * Register custom SVG icon for Custom Settings category\r\n */\r\nconst registerCustomSettingsIcon = () => {\r\n  // Register the icon with SurveyJS SvgRegistry\r\n  SvgRegistry.registerIconFromSvg(\r\n    \"icon-customsettings\",\r\n    '<svg viewBox=\"0 0 24 24\"><path d=\"M21,9L17,5V8H10V10H17V13M7,11L3,15L7,19V16H14V14H7V11Z\"/></svg>'\r\n  );\r\n};\r\n\r\n/**\r\n * Core questionnaire designer component that handles the Survey.js creator logic\r\n * @param {Object} props - Component props\r\n * @param {Object} props.questionnaire - The questionnaire data\r\n * @param {string} props.surveyName - The survey name\r\n * @param {number} props.selectedYear - The selected year\r\n * @param {boolean} props.isQuestionnaireLoaded - Whether questionnaire is loaded\r\n * @param {boolean} props.isReadonly - Whether the form creator should be in readonly mode\r\n * @param {Function} props.onSave - Callback for save action\r\n * @param {Function} props.onPublish - Callback for publish action\r\n * @param {Function} props.onBackToList - Callback for back to list action\r\n */\r\nexport const QuestionnaireDesignerCore = forwardRef(\r\n  ({ questionnaire, surveyName, selectedYear, isQuestionnaireLoaded, isReadonly, onSave, onPublish, onBackToList }, ref) => {\r\n    const [showPublishDialog, setShowPublishDialog] = useState(false);\r\n    const [creatorReady, setCreatorReady] = useState(false);\r\n    const toast = useRef(null);\r\n    const creatorRef = useRef(null);\r\n    //const autoSaveTimeoutRef = useRef(null);\r\n\r\n    // Year options for dropdown\r\n    const currentYear = new Date().getFullYear();\r\n    const yearOptions = [];\r\n    for (let i = currentYear - 5; i <= currentYear + 5; i++) {\r\n      yearOptions.push({ label: i.toString(), value: i });\r\n    }\r\n\r\n    useEffect(() => {\r\n      const initializeCreator = async () => {\r\n        // Configure Survey.js commercial license before initializing creator\r\n        configureSurveyJSLicense();\r\n\r\n        // Initialize Survey Creator with BDO theme first\r\n        const creator = new SurveyCreator({\r\n          showLogicTab: true,\r\n          showTranslationTab: false,\r\n          showEmbeddedSurveyTab: false,\r\n          showJSONEditorTab: true,\r\n          showTestSurveyTab: true,\r\n          showPropertyGrid: true,\r\n          showToolbox: true,\r\n          isAutoSave: false,\r\n          readOnly: isReadonly || false,\r\n        });\r\n\r\n        // Apply BDO theme colors\r\n        creator.theme = {\r\n          cssVariables: {\r\n            \"--sjs-primary-backcolor\": \"#ED1A3B\",\r\n            \"--sjs-primary-forecolor\": \"#ffffff\",\r\n            \"--sjs-secondary-backcolor\": \"#f3f2f1\",\r\n            \"--sjs-secondary-forecolor\": \"#1f1f1f\",\r\n            \"--sjs-shadow-small\": \"0px 1px 2px 0px rgba(0, 0, 0, 0.15)\",\r\n            \"--sjs-shadow-medium\": \"0px 2px 6px 0px rgba(0, 0, 0, 0.1)\",\r\n            \"--sjs-shadow-large\": \"0px 8px 16px 0px rgba(0, 0, 0, 0.1)\",\r\n            \"--sjs-border-default\": \"#e0e0e0\",\r\n            \"--sjs-border-light\": \"#f0f0f0\",\r\n          },\r\n        };\r\n\r\n        // Disable custom toolbar buttons (they are now external)\r\n        const saveAction = new Action({\r\n          id: \"save-survey\",\r\n          visible: false, // Disabled - now external\r\n          title: \"Save\",\r\n          tooltip: \"Save questionnaire as draft\",\r\n          action: () => {\r\n            // Use the creator instance directly instead of the ref\r\n            saveSurveyWithCreator(creator);\r\n          },\r\n        });\r\n\r\n        creator.toolbarItems.push(saveAction);\r\n\r\n        creator.toolbarItems.push(\r\n          new Action({\r\n            id: \"publish-survey\",\r\n            visible: false, // Disabled - now external\r\n            title: \"Publish\",\r\n            tooltip: \"Publish questionnaire\",\r\n            action: () => setShowPublishDialog(true),\r\n          })\r\n        );\r\n\r\n        creator.toolbarItems.push(\r\n          new Action({\r\n            id: \"back-to-list\",\r\n            visible: false, // Disabled - now external\r\n            title: \"Back to List\",\r\n            tooltip: \"Return to questionnaire list\",\r\n            action: () => handleBackToList(),\r\n          })\r\n        );\r\n\r\n        // // Enable auto-save\r\n        // creator.onModified.add(() => {\r\n        //   clearAutoSaveTimeout();\r\n        //   autoSaveTimeoutRef.current = setTimeout(() => {\r\n        //     if (onSave && creatorRef.current) {\r\n        //       const surveyJson = creatorRef.current.JSON;\r\n        //       onSave(surveyJson, false); // Auto-save without showing success message\r\n        //     }\r\n        //   }, 10000); // Auto-save after 10 seconds of inactivity\r\n        // });\r\n\r\n        // Register the custom icon\r\n        registerCustomSettingsIcon();\r\n\r\n        // Assign icon to the custom category in the property grid\r\n        creator.onSurveyInstanceCreated.add((_, options) => {\r\n          if (options.area === \"property-grid\") {\r\n            const customCategory = options.survey.getPageByName(\"customSettings\");\r\n            if (customCategory) {\r\n              customCategory.iconName = \"icon-customsettings\";\r\n              console.log(\"Assigned custom icon to Custom Settings category\");\r\n            }\r\n          }\r\n        });\r\n\r\n        // Setup custom properties for Form Creator with questionnaire year - AWAIT this to ensure it completes\r\n        await setupCustomProperties(selectedYear);\r\n        console.log(\"Custom properties setup completed\");\r\n\r\n        creatorRef.current = creator;\r\n        setCreatorReady(true);\r\n        console.log(\"Creator initialized and assigned to ref:\", creator);\r\n      };\r\n\r\n      initializeCreator();\r\n\r\n      // return () => {\r\n      //   clearAutoSaveTimeout();\r\n      // };\r\n      // eslint-disable-next-line react-hooks/exhaustive-deps\r\n    }, [isReadonly]);\r\n\r\n    //\r\n    // Load questionnaire data into creator when both are available\r\n    // Note: We only load the draft definition from the questionnaire record to form creator.\r\n    // Wait for creatorReady to ensure custom properties are set up before loading JSON\r\n    //\r\n    useEffect(() => {\r\n      if (creatorRef.current && questionnaire && creatorReady) {\r\n        const surveyJson = questionnaire.draftDefinitionJson\r\n          ? JSON.parse(questionnaire.draftDefinitionJson)\r\n          : { title: questionnaire.name, pages: [] };\r\n\r\n        creatorRef.current.JSON = surveyJson;\r\n        console.log(\"Loaded questionnaire data into creator via useEffect:\", surveyJson);\r\n      }\r\n    }, [questionnaire, creatorReady]);\r\n\r\n    // Update save button enabled state based on questionnaire loading\r\n    useEffect(() => {\r\n      if (creatorRef.current && creatorReady) {\r\n        const saveAction = creatorRef.current.toolbarItems.find((item) => item.id === \"save-survey\");\r\n        if (saveAction) {\r\n          saveAction.enabled = isQuestionnaireLoaded && !!questionnaire && !!questionnaire.id;\r\n          console.log(\"Save button enabled state:\", saveAction.enabled, {\r\n            isQuestionnaireLoaded,\r\n            hasQuestionnaire: !!questionnaire,\r\n            hasId: !!questionnaire?.id,\r\n          });\r\n        }\r\n      }\r\n    }, [isQuestionnaireLoaded, questionnaire, creatorReady]);\r\n\r\n    // Expose methods to parent component\r\n    useImperativeHandle(ref, () => ({\r\n      getSurveyJson: () => {\r\n        return creatorRef.current ? creatorRef.current.JSON : null;\r\n      },\r\n    }));\r\n\r\n    // const clearAutoSaveTimeout = () => {\r\n    //   if (autoSaveTimeoutRef.current) {\r\n    //     clearTimeout(autoSaveTimeoutRef.current);\r\n    //     autoSaveTimeoutRef.current = null;\r\n    //   }\r\n    // };\r\n\r\n    // Save survey using creator reference (for external save button)\r\n    const saveSurvey = async (showMessage = true) => {\r\n      if (!creatorRef.current) {\r\n        console.error(\"Creator reference is null - cannot save survey\");\r\n        if (showMessage) {\r\n          messageService.errorToast(\"Form designer not ready. Please try again.\");\r\n        }\r\n        return;\r\n      }\r\n\r\n      return await saveSurveyWithCreator(creatorRef.current, showMessage);\r\n    };\r\n\r\n    // Save survey using creator instance (for internal save button)\r\n    const saveSurveyWithCreator = async (creator, showMessage = true) => {\r\n      console.log(\"Save attempt - Creator:\", !!creator, \"Questionnaire:\", !!questionnaire, \"Loaded:\", isQuestionnaireLoaded);\r\n      console.log(\"Save attempt - Survey name:\", surveyName, \"Year:\", selectedYear);\r\n\r\n      if (!creator) {\r\n        console.error(\"Creator instance is null - cannot save survey\");\r\n        if (showMessage) {\r\n          messageService.errorToast(\"Form designer not ready. Please try again.\");\r\n        }\r\n        return;\r\n      }\r\n\r\n      // Check if questionnaire data is loaded\r\n      if (!questionnaire || !isQuestionnaireLoaded || !questionnaire.id) {\r\n        console.error(\"Questionnaire data not loaded or invalid - cannot save survey\", {\r\n          questionnaire: !!questionnaire,\r\n          isQuestionnaireLoaded,\r\n          questionnaireId: questionnaire?.id,\r\n        });\r\n        if (showMessage) {\r\n          messageService.errorToast(\"Questionnaire data not loaded or invalid. Please wait and try again.\");\r\n        }\r\n        return;\r\n      }\r\n\r\n      // Check if survey name and year are available\r\n      if (!surveyName || !selectedYear) {\r\n        console.error(\"Survey name or year not available - cannot save survey\", {\r\n          surveyName,\r\n          selectedYear,\r\n        });\r\n        if (showMessage) {\r\n          messageService.errorToast(\"Survey name or year not available. Please check the form fields.\");\r\n        }\r\n        return;\r\n      }\r\n\r\n      try {\r\n        if (showMessage) {\r\n          loadingService.httpRequestSent();\r\n        }\r\n\r\n        // Get the current survey JSON from the creator\r\n        const surveyJson = creator.JSON;\r\n        console.log(\"Current survey JSON:\", surveyJson);\r\n\r\n        // Ensure we have valid survey data\r\n        if (!surveyJson || Object.keys(surveyJson).length === 0) {\r\n          console.error(\"Survey JSON is empty or invalid\");\r\n          if (showMessage) {\r\n            messageService.errorToast(\"No form data to save. Please add some questions first.\");\r\n          }\r\n          return;\r\n        }\r\n\r\n        // Call the parent's save handler\r\n        if (onSave) {\r\n          await onSave(surveyJson, showMessage);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error saving questionnaire:\", error);\r\n        if (showMessage) {\r\n          messageService.errorToast(error.message || \"Error saving questionnaire\");\r\n        }\r\n      } finally {\r\n        if (showMessage) {\r\n          loadingService.httpResponseReceived();\r\n        }\r\n      }\r\n    };\r\n\r\n    const publishSurvey = async () => {\r\n      if (!creatorRef.current) return;\r\n\r\n      try {\r\n        loadingService.httpRequestSent();\r\n\r\n        const surveyJson = creatorRef.current.JSON;\r\n\r\n        // Call the parent's publish handler\r\n        if (onPublish) {\r\n          await onPublish(surveyJson);\r\n        }\r\n\r\n        setShowPublishDialog(false);\r\n      } catch (error) {\r\n        console.error(\"Error publishing questionnaire:\", error);\r\n        messageService.errorToast(error.message || \"Error publishing questionnaire\");\r\n      } finally {\r\n        loadingService.httpResponseReceived();\r\n      }\r\n    };\r\n\r\n    const handleBackToList = () => {\r\n      if (creatorRef.current && creatorRef.current.isModified) {\r\n        confirmDialog({\r\n          message: \"You have unsaved changes. Do you want to save before leaving?\",\r\n          header: \"Unsaved Changes\",\r\n          icon: \"pi pi-exclamation-triangle\",\r\n          accept: async () => {\r\n            await saveSurvey();\r\n            if (onBackToList) {\r\n              onBackToList();\r\n            }\r\n          },\r\n          reject: () => {\r\n            if (onBackToList) {\r\n              onBackToList();\r\n            }\r\n          },\r\n        });\r\n      } else {\r\n        if (onBackToList) {\r\n          onBackToList();\r\n        }\r\n      }\r\n    };\r\n\r\n    return (\r\n      <div>\r\n        <Toast ref={toast} />\r\n        <ConfirmDialog />\r\n\r\n        {/* Survey Creator */}\r\n        <div style={{ height: \"calc(100vh - 200px)\" }}>\r\n          {creatorReady && creatorRef.current ? (\r\n            <SurveyCreatorComponent creator={creatorRef.current} />\r\n          ) : (\r\n            <div className=\"flex justify-content-center align-items-center\" style={{ height: \"100%\" }}>\r\n              <i className=\"pi pi-spinner pi-spin\" style={{ fontSize: \"2rem\" }}></i>\r\n              <span className=\"ml-2\">Initializing form designer...</span>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Publish Confirmation Dialog */}\r\n        <Dialog\r\n          header={questionnaire?.status === 1 ? \"Republish Questionnaire\" : \"Publish Questionnaire\"}\r\n          visible={showPublishDialog}\r\n          style={{ width: \"450px\" }}\r\n          onHide={() => setShowPublishDialog(false)}\r\n          footer={\r\n            <div>\r\n              <Button label=\"Cancel\" icon=\"pi pi-times\" onClick={() => setShowPublishDialog(false)} className=\"p-button-text\" />\r\n              <Button label={questionnaire?.status === 1 ? \"Republish\" : \"Publish\"} icon=\"pi pi-send\" onClick={publishSurvey} className=\"action\" />\r\n            </div>\r\n          }\r\n        >\r\n          <p>\r\n            {questionnaire?.status === 1\r\n              ? \"Are you sure you want to republish this questionnaire? This will update the published version with your current draft changes.\"\r\n              : \"Are you sure you want to publish this questionnaire? This will make it available for use and cannot be easily undone.\"}\r\n          </p>\r\n        </Dialog>\r\n      </div>\r\n    );\r\n  }\r\n);\r\n\r\n// Export the utility functions for use in other components\r\nexport { setupCustomProperties, registerCustomSettingsIcon };\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,UAAU,EAAEC,mBAAmB,QAAQ,OAAO;AAC3F,SAASC,sBAAsB,EAAEC,aAAa,QAAQ,sBAAsB;AAC5E,SAASC,MAAM,EAAEC,WAAW,QAAQ,aAAa;AACjD,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,aAAa,EAAEC,aAAa,QAAQ,0BAA0B;AACvE,SAASC,cAAc,QAAQ,mCAAmC;AAClE,SAASC,cAAc,QAAQ,mCAAmC;AAClE,SAASC,wBAAwB,QAAQ,mCAAmC;AAC5E,SAASC,kCAAkC,QAAQ,8CAA8C;;AAEjG;AACA,OAAO,6CAA6C;;AAEpD;AACA;AACA;AACA;AACA;AAJA,SAAAC,MAAA,IAAAC,OAAA;AAKA,MAAMC,qBAAqB,GAAG,MAAAA,CAAOC,iBAAiB,GAAG,IAAI,KAAK;EAChE,MAAMJ,kCAAkC,CAAC;IACvCK,UAAU,EAAE,IAAI;IAChBD,iBAAiB;IACjBE,kBAAkB,EAAE;EACtB,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA,MAAMC,0BAA0B,GAAGA,CAAA,KAAM;EACvC;EACAhB,WAAW,CAACiB,mBAAmB,CAC7B,qBAAqB,EACrB,mGACF,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,yBAAyB,gBAAAC,EAAA,cAAGxB,UAAU,CAAAyB,EAAA,GAAAD,EAAA,CACjD,CAAC;EAAEE,aAAa;EAAEC,UAAU;EAAEC,YAAY;EAAEC,qBAAqB;EAAEC,UAAU;EAAEC,MAAM;EAAEC,SAAS;EAAEC;AAAa,CAAC,EAAEC,GAAG,KAAK;EAAAV,EAAA;EACxH,MAAM,CAACW,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACwC,YAAY,EAAEC,eAAe,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM0C,KAAK,GAAGxC,MAAM,CAAC,IAAI,CAAC;EAC1B,MAAMyC,UAAU,GAAGzC,MAAM,CAAC,IAAI,CAAC;EAC/B;;EAEA;EACA,MAAM0C,WAAW,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;EAC5C,MAAMC,WAAW,GAAG,EAAE;EACtB,KAAK,IAAIC,CAAC,GAAGJ,WAAW,GAAG,CAAC,EAAEI,CAAC,IAAIJ,WAAW,GAAG,CAAC,EAAEI,CAAC,EAAE,EAAE;IACvDD,WAAW,CAACE,IAAI,CAAC;MAAEC,KAAK,EAAEF,CAAC,CAACG,QAAQ,CAAC,CAAC;MAAEC,KAAK,EAAEJ;IAAE,CAAC,CAAC;EACrD;EAEA/C,SAAS,CAAC,MAAM;IACd,MAAMoD,iBAAiB,GAAG,MAAAA,CAAA,KAAY;MACpC;MACArC,wBAAwB,CAAC,CAAC;;MAE1B;MACA,MAAMsC,OAAO,GAAG,IAAIhD,aAAa,CAAC;QAChCiD,YAAY,EAAE,IAAI;QAClBC,kBAAkB,EAAE,KAAK;QACzBC,qBAAqB,EAAE,KAAK;QAC5BC,iBAAiB,EAAE,IAAI;QACvBC,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBC,WAAW,EAAE,IAAI;QACjBC,UAAU,EAAE,KAAK;QACjBC,QAAQ,EAAE9B,UAAU,IAAI;MAC1B,CAAC,CAAC;;MAEF;MACAqB,OAAO,CAACU,KAAK,GAAG;QACdC,YAAY,EAAE;UACZ,yBAAyB,EAAE,SAAS;UACpC,yBAAyB,EAAE,SAAS;UACpC,2BAA2B,EAAE,SAAS;UACtC,2BAA2B,EAAE,SAAS;UACtC,oBAAoB,EAAE,qCAAqC;UAC3D,qBAAqB,EAAE,oCAAoC;UAC3D,oBAAoB,EAAE,qCAAqC;UAC3D,sBAAsB,EAAE,SAAS;UACjC,oBAAoB,EAAE;QACxB;MACF,CAAC;;MAED;MACA,MAAMC,UAAU,GAAG,IAAI3D,MAAM,CAAC;QAC5B4D,EAAE,EAAE,aAAa;QACjBC,OAAO,EAAE,KAAK;QAAE;QAChBC,KAAK,EAAE,MAAM;QACbC,OAAO,EAAE,6BAA6B;QACtCC,MAAM,EAAEA,CAAA,KAAM;UACZ;UACAC,qBAAqB,CAAClB,OAAO,CAAC;QAChC;MACF,CAAC,CAAC;MAEFA,OAAO,CAACmB,YAAY,CAACxB,IAAI,CAACiB,UAAU,CAAC;MAErCZ,OAAO,CAACmB,YAAY,CAACxB,IAAI,CACvB,IAAI1C,MAAM,CAAC;QACT4D,EAAE,EAAE,gBAAgB;QACpBC,OAAO,EAAE,KAAK;QAAE;QAChBC,KAAK,EAAE,SAAS;QAChBC,OAAO,EAAE,uBAAuB;QAChCC,MAAM,EAAEA,CAAA,KAAMhC,oBAAoB,CAAC,IAAI;MACzC,CAAC,CACH,CAAC;MAEDe,OAAO,CAACmB,YAAY,CAACxB,IAAI,CACvB,IAAI1C,MAAM,CAAC;QACT4D,EAAE,EAAE,cAAc;QAClBC,OAAO,EAAE,KAAK;QAAE;QAChBC,KAAK,EAAE,cAAc;QACrBC,OAAO,EAAE,8BAA8B;QACvCC,MAAM,EAAEA,CAAA,KAAMG,gBAAgB,CAAC;MACjC,CAAC,CACH,CAAC;;MAED;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACAlD,0BAA0B,CAAC,CAAC;;MAE5B;MACA8B,OAAO,CAACqB,uBAAuB,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,OAAO,KAAK;QAClD,IAAIA,OAAO,CAACC,IAAI,KAAK,eAAe,EAAE;UACpC,MAAMC,cAAc,GAAGF,OAAO,CAACG,MAAM,CAACC,aAAa,CAAC,gBAAgB,CAAC;UACrE,IAAIF,cAAc,EAAE;YAClBA,cAAc,CAACG,QAAQ,GAAG,qBAAqB;YAC/CC,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;UACjE;QACF;MACF,CAAC,CAAC;;MAEF;MACA,MAAMjE,qBAAqB,CAACW,YAAY,CAAC;MACzCqD,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;MAEhD1C,UAAU,CAAC2C,OAAO,GAAGhC,OAAO;MAC5Bb,eAAe,CAAC,IAAI,CAAC;MACrB2C,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE/B,OAAO,CAAC;IAClE,CAAC;IAEDD,iBAAiB,CAAC,CAAC;;IAEnB;IACA;IACA;IACA;EACF,CAAC,EAAE,CAACpB,UAAU,CAAC,CAAC;;EAEhB;EACA;EACA;EACA;EACA;EACAhC,SAAS,CAAC,MAAM;IACd,IAAI0C,UAAU,CAAC2C,OAAO,IAAIzD,aAAa,IAAIW,YAAY,EAAE;MACvD,MAAM+C,UAAU,GAAG1D,aAAa,CAAC2D,mBAAmB,GAChDC,IAAI,CAACC,KAAK,CAAC7D,aAAa,CAAC2D,mBAAmB,CAAC,GAC7C;QAAEnB,KAAK,EAAExC,aAAa,CAAC8D,IAAI;QAAEC,KAAK,EAAE;MAAG,CAAC;MAE5CjD,UAAU,CAAC2C,OAAO,CAACG,IAAI,GAAGF,UAAU;MACpCH,OAAO,CAACC,GAAG,CAAC,uDAAuD,EAAEE,UAAU,CAAC;IAClF;EACF,CAAC,EAAE,CAAC1D,aAAa,EAAEW,YAAY,CAAC,CAAC;;EAEjC;EACAvC,SAAS,CAAC,MAAM;IACd,IAAI0C,UAAU,CAAC2C,OAAO,IAAI9C,YAAY,EAAE;MACtC,MAAM0B,UAAU,GAAGvB,UAAU,CAAC2C,OAAO,CAACb,YAAY,CAACoB,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAAC3B,EAAE,KAAK,aAAa,CAAC;MAC5F,IAAID,UAAU,EAAE;QACdA,UAAU,CAAC6B,OAAO,GAAG/D,qBAAqB,IAAI,CAAC,CAACH,aAAa,IAAI,CAAC,CAACA,aAAa,CAACsC,EAAE;QACnFiB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEnB,UAAU,CAAC6B,OAAO,EAAE;UAC5D/D,qBAAqB;UACrBgE,gBAAgB,EAAE,CAAC,CAACnE,aAAa;UACjCoE,KAAK,EAAE,CAAC,EAACpE,aAAa,aAAbA,aAAa,eAAbA,aAAa,CAAEsC,EAAE;QAC5B,CAAC,CAAC;MACJ;IACF;EACF,CAAC,EAAE,CAACnC,qBAAqB,EAAEH,aAAa,EAAEW,YAAY,CAAC,CAAC;;EAExD;EACApC,mBAAmB,CAACiC,GAAG,EAAE,OAAO;IAC9B6D,aAAa,EAAEA,CAAA,KAAM;MACnB,OAAOvD,UAAU,CAAC2C,OAAO,GAAG3C,UAAU,CAAC2C,OAAO,CAACG,IAAI,GAAG,IAAI;IAC5D;EACF,CAAC,CAAC,CAAC;;EAEH;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA,MAAMU,UAAU,GAAG,MAAAA,CAAOC,WAAW,GAAG,IAAI,KAAK;IAC/C,IAAI,CAACzD,UAAU,CAAC2C,OAAO,EAAE;MACvBF,OAAO,CAACiB,KAAK,CAAC,gDAAgD,CAAC;MAC/D,IAAID,WAAW,EAAE;QACfrF,cAAc,CAACuF,UAAU,CAAC,4CAA4C,CAAC;MACzE;MACA;IACF;IAEA,OAAO,MAAM9B,qBAAqB,CAAC7B,UAAU,CAAC2C,OAAO,EAAEc,WAAW,CAAC;EACrE,CAAC;;EAED;EACA,MAAM5B,qBAAqB,GAAG,MAAAA,CAAOlB,OAAO,EAAE8C,WAAW,GAAG,IAAI,KAAK;IACnEhB,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE,CAAC,CAAC/B,OAAO,EAAE,gBAAgB,EAAE,CAAC,CAACzB,aAAa,EAAE,SAAS,EAAEG,qBAAqB,CAAC;IACtHoD,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEvD,UAAU,EAAE,OAAO,EAAEC,YAAY,CAAC;IAE7E,IAAI,CAACuB,OAAO,EAAE;MACZ8B,OAAO,CAACiB,KAAK,CAAC,+CAA+C,CAAC;MAC9D,IAAID,WAAW,EAAE;QACfrF,cAAc,CAACuF,UAAU,CAAC,4CAA4C,CAAC;MACzE;MACA;IACF;;IAEA;IACA,IAAI,CAACzE,aAAa,IAAI,CAACG,qBAAqB,IAAI,CAACH,aAAa,CAACsC,EAAE,EAAE;MACjEiB,OAAO,CAACiB,KAAK,CAAC,+DAA+D,EAAE;QAC7ExE,aAAa,EAAE,CAAC,CAACA,aAAa;QAC9BG,qBAAqB;QACrBuE,eAAe,EAAE1E,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEsC;MAClC,CAAC,CAAC;MACF,IAAIiC,WAAW,EAAE;QACfrF,cAAc,CAACuF,UAAU,CAAC,sEAAsE,CAAC;MACnG;MACA;IACF;;IAEA;IACA,IAAI,CAACxE,UAAU,IAAI,CAACC,YAAY,EAAE;MAChCqD,OAAO,CAACiB,KAAK,CAAC,wDAAwD,EAAE;QACtEvE,UAAU;QACVC;MACF,CAAC,CAAC;MACF,IAAIqE,WAAW,EAAE;QACfrF,cAAc,CAACuF,UAAU,CAAC,kEAAkE,CAAC;MAC/F;MACA;IACF;IAEA,IAAI;MACF,IAAIF,WAAW,EAAE;QACftF,cAAc,CAAC0F,eAAe,CAAC,CAAC;MAClC;;MAEA;MACA,MAAMjB,UAAU,GAAGjC,OAAO,CAACmC,IAAI;MAC/BL,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEE,UAAU,CAAC;;MAE/C;MACA,IAAI,CAACA,UAAU,IAAIkB,MAAM,CAACC,IAAI,CAACnB,UAAU,CAAC,CAACoB,MAAM,KAAK,CAAC,EAAE;QACvDvB,OAAO,CAACiB,KAAK,CAAC,iCAAiC,CAAC;QAChD,IAAID,WAAW,EAAE;UACfrF,cAAc,CAACuF,UAAU,CAAC,wDAAwD,CAAC;QACrF;QACA;MACF;;MAEA;MACA,IAAIpE,MAAM,EAAE;QACV,MAAMA,MAAM,CAACqD,UAAU,EAAEa,WAAW,CAAC;MACvC;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdjB,OAAO,CAACiB,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,IAAID,WAAW,EAAE;QACfrF,cAAc,CAACuF,UAAU,CAACD,KAAK,CAACO,OAAO,IAAI,4BAA4B,CAAC;MAC1E;IACF,CAAC,SAAS;MACR,IAAIR,WAAW,EAAE;QACftF,cAAc,CAAC+F,oBAAoB,CAAC,CAAC;MACvC;IACF;EACF,CAAC;EAED,MAAMC,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI,CAACnE,UAAU,CAAC2C,OAAO,EAAE;IAEzB,IAAI;MACFxE,cAAc,CAAC0F,eAAe,CAAC,CAAC;MAEhC,MAAMjB,UAAU,GAAG5C,UAAU,CAAC2C,OAAO,CAACG,IAAI;;MAE1C;MACA,IAAItD,SAAS,EAAE;QACb,MAAMA,SAAS,CAACoD,UAAU,CAAC;MAC7B;MAEAhD,oBAAoB,CAAC,KAAK,CAAC;IAC7B,CAAC,CAAC,OAAO8D,KAAK,EAAE;MACdjB,OAAO,CAACiB,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvDtF,cAAc,CAACuF,UAAU,CAACD,KAAK,CAACO,OAAO,IAAI,gCAAgC,CAAC;IAC9E,CAAC,SAAS;MACR9F,cAAc,CAAC+F,oBAAoB,CAAC,CAAC;IACvC;EACF,CAAC;EAED,MAAMnC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI/B,UAAU,CAAC2C,OAAO,IAAI3C,UAAU,CAAC2C,OAAO,CAACyB,UAAU,EAAE;MACvDlG,aAAa,CAAC;QACZ+F,OAAO,EAAE,+DAA+D;QACxEI,MAAM,EAAE,iBAAiB;QACzBC,IAAI,EAAE,4BAA4B;QAClCC,MAAM,EAAE,MAAAA,CAAA,KAAY;UAClB,MAAMf,UAAU,CAAC,CAAC;UAClB,IAAI/D,YAAY,EAAE;YAChBA,YAAY,CAAC,CAAC;UAChB;QACF,CAAC;QACD+E,MAAM,EAAEA,CAAA,KAAM;UACZ,IAAI/E,YAAY,EAAE;YAChBA,YAAY,CAAC,CAAC;UAChB;QACF;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAIA,YAAY,EAAE;QAChBA,YAAY,CAAC,CAAC;MAChB;IACF;EACF,CAAC;EAED,oBACEjB,OAAA;IAAAiG,QAAA,gBACEjG,OAAA,CAACT,KAAK;MAAC2B,GAAG,EAAEK;IAAM;MAAA2E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACrBrG,OAAA,CAACP,aAAa;MAAAyG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGjBrG,OAAA;MAAKsG,KAAK,EAAE;QAAEC,MAAM,EAAE;MAAsB,CAAE;MAAAN,QAAA,EAC3C5E,YAAY,IAAIG,UAAU,CAAC2C,OAAO,gBACjCnE,OAAA,CAACd,sBAAsB;QAACiD,OAAO,EAAEX,UAAU,CAAC2C;MAAQ;QAAA+B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAEvDrG,OAAA;QAAKwG,SAAS,EAAC,gDAAgD;QAACF,KAAK,EAAE;UAAEC,MAAM,EAAE;QAAO,CAAE;QAAAN,QAAA,gBACxFjG,OAAA;UAAGwG,SAAS,EAAC,uBAAuB;UAACF,KAAK,EAAE;YAAEG,QAAQ,EAAE;UAAO;QAAE;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtErG,OAAA;UAAMwG,SAAS,EAAC,MAAM;UAAAP,QAAA,EAAC;QAA6B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNrG,OAAA,CAACR,MAAM;MACLqG,MAAM,EAAE,CAAAnF,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEgG,MAAM,MAAK,CAAC,GAAG,yBAAyB,GAAG,uBAAwB;MAC1FzD,OAAO,EAAE9B,iBAAkB;MAC3BmF,KAAK,EAAE;QAAEK,KAAK,EAAE;MAAQ,CAAE;MAC1BC,MAAM,EAAEA,CAAA,KAAMxF,oBAAoB,CAAC,KAAK,CAAE;MAC1CyF,MAAM,eACJ7G,OAAA;QAAAiG,QAAA,gBACEjG,OAAA,CAACV,MAAM;UAACyC,KAAK,EAAC,QAAQ;UAAC+D,IAAI,EAAC,aAAa;UAACgB,OAAO,EAAEA,CAAA,KAAM1F,oBAAoB,CAAC,KAAK,CAAE;UAACoF,SAAS,EAAC;QAAe;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClHrG,OAAA,CAACV,MAAM;UAACyC,KAAK,EAAE,CAAArB,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEgG,MAAM,MAAK,CAAC,GAAG,WAAW,GAAG,SAAU;UAACZ,IAAI,EAAC,YAAY;UAACgB,OAAO,EAAEnB,aAAc;UAACa,SAAS,EAAC;QAAQ;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClI,CACN;MAAAJ,QAAA,eAEDjG,OAAA;QAAAiG,QAAA,EACG,CAAAvF,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEgG,MAAM,MAAK,CAAC,GACxB,gIAAgI,GAChI;MAAuH;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1H;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC,iCACH,CAAC;;AAED;AAAAU,GAAA,GAtVaxG,yBAAyB;AAuVtC,SAASN,qBAAqB,EAAEI,0BAA0B;AAAG,IAAAI,EAAA,EAAAsG,GAAA;AAAAC,YAAA,CAAAvG,EAAA;AAAAuG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}