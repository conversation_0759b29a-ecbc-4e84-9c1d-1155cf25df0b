{"ast": null, "code": "'use client';\n\nimport { <PERSON><PERSON><PERSON><PERSON> } from 'primereact/utils';\nfunction _arrayLikeToArray(r, a) {\n  (null == a || a > r.length) && (a = r.length);\n  for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e];\n  return n;\n}\nfunction _arrayWithoutHoles(r) {\n  if (Array.isArray(r)) return _arrayLikeToArray(r);\n}\nfunction _iterableToArray(r) {\n  if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r);\n}\nfunction _unsupportedIterableToArray(r, a) {\n  if (r) {\n    if (\"string\" == typeof r) return _arrayLikeToArray(r, a);\n    var t = {}.toString.call(r).slice(8, -1);\n    return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0;\n  }\n}\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _toConsumableArray(r) {\n  return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread();\n}\nvar KeyFilter = {\n  /* eslint-disable */\n  DEFAULT_MASKS: {\n    pint: /[\\d]/,\n    \"int\": /[\\d\\-]/,\n    pnum: /[\\d\\.]/,\n    money: /[\\d\\.\\s,]/,\n    num: /[\\d\\-\\.]/,\n    hex: /[0-9a-f]/i,\n    email: /[a-z0-9_\\.\\-@]/i,\n    alpha: /[a-z_]/i,\n    alphanum: /[a-z0-9_]/i\n  },\n  /* eslint-enable */getRegex: function getRegex(keyfilter) {\n    return KeyFilter.DEFAULT_MASKS[keyfilter] ? KeyFilter.DEFAULT_MASKS[keyfilter] : keyfilter;\n  },\n  onBeforeInput: function onBeforeInput(e, keyfilter, validateOnly) {\n    // android devices must use beforeinput https://stackoverflow.com/questions/36753548/keycode-on-android-is-always-229\n    if (validateOnly || !DomHandler.isAndroid()) {\n      return;\n    }\n    this.validateKey(e, e.data, keyfilter);\n  },\n  onKeyPress: function onKeyPress(e, keyfilter, validateOnly) {\n    // non android devices use keydown\n    if (validateOnly || DomHandler.isAndroid()) {\n      return;\n    }\n    if (e.ctrlKey || e.altKey || e.metaKey) {\n      return;\n    }\n    this.validateKey(e, e.key, keyfilter);\n  },\n  onPaste: function onPaste(e, keyfilter, validateOnly) {\n    if (validateOnly) {\n      return;\n    }\n    var regex = this.getRegex(keyfilter);\n    var clipboard = e.clipboardData.getData('text');\n\n    // loop over each letter pasted and if any fail prevent the paste\n    _toConsumableArray(clipboard).forEach(function (c) {\n      if (!regex.test(c)) {\n        e.preventDefault();\n        return false;\n      }\n    });\n  },\n  validateKey: function validateKey(e, key, keyfilter) {\n    if (key === null || key === undefined) {\n      return;\n    }\n\n    // some AZERTY keys come in with 2 chars like ´ç if Dead key is pressed first\n    var isPrintableKey = key.length <= 2;\n    if (!isPrintableKey) {\n      return;\n    }\n    var regex = this.getRegex(keyfilter);\n    if (!regex.test(key)) {\n      e.preventDefault();\n    }\n  },\n  validate: function validate(e, keyfilter) {\n    var value = e.target.value;\n    var validatePattern = true;\n    var regex = this.getRegex(keyfilter);\n    if (value && !regex.test(value)) {\n      validatePattern = false;\n    }\n    return validatePattern;\n  }\n};\nexport { KeyFilter };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "_arrayLikeToArray", "r", "a", "length", "e", "n", "Array", "_arrayWithoutHoles", "isArray", "_iterableToArray", "Symbol", "iterator", "from", "_unsupportedIterableToArray", "t", "toString", "call", "slice", "constructor", "name", "test", "_nonIterableSpread", "TypeError", "_toConsumableArray", "<PERSON><PERSON><PERSON>er", "DEFAULT_MASKS", "pint", "pnum", "money", "num", "hex", "email", "alpha", "alphanum", "getRegex", "keyfilter", "onBeforeInput", "validateOnly", "isAndroid", "validate<PERSON><PERSON>", "data", "onKeyPress", "ctrl<PERSON>ey", "altKey", "metaKey", "key", "onPaste", "regex", "clipboard", "clipboardData", "getData", "for<PERSON>ach", "c", "preventDefault", "undefined", "isPrintableKey", "validate", "value", "target", "validatePattern"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/node_modules/primereact/keyfilter/keyfilter.esm.js"], "sourcesContent": ["'use client';\nimport { <PERSON><PERSON><PERSON><PERSON> } from 'primereact/utils';\n\nfunction _arrayLikeToArray(r, a) {\n  (null == a || a > r.length) && (a = r.length);\n  for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e];\n  return n;\n}\n\nfunction _arrayWithoutHoles(r) {\n  if (Array.isArray(r)) return _arrayLikeToArray(r);\n}\n\nfunction _iterableToArray(r) {\n  if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r);\n}\n\nfunction _unsupportedIterableToArray(r, a) {\n  if (r) {\n    if (\"string\" == typeof r) return _arrayLikeToArray(r, a);\n    var t = {}.toString.call(r).slice(8, -1);\n    return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0;\n  }\n}\n\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nfunction _toConsumableArray(r) {\n  return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread();\n}\n\nvar KeyFilter = {\n  /* eslint-disable */\n  DEFAULT_MASKS: {\n    pint: /[\\d]/,\n    \"int\": /[\\d\\-]/,\n    pnum: /[\\d\\.]/,\n    money: /[\\d\\.\\s,]/,\n    num: /[\\d\\-\\.]/,\n    hex: /[0-9a-f]/i,\n    email: /[a-z0-9_\\.\\-@]/i,\n    alpha: /[a-z_]/i,\n    alphanum: /[a-z0-9_]/i\n  },\n  /* eslint-enable */getRegex: function getRegex(keyfilter) {\n    return KeyFilter.DEFAULT_MASKS[keyfilter] ? KeyFilter.DEFAULT_MASKS[keyfilter] : keyfilter;\n  },\n  onBeforeInput: function onBeforeInput(e, keyfilter, validateOnly) {\n    // android devices must use beforeinput https://stackoverflow.com/questions/36753548/keycode-on-android-is-always-229\n    if (validateOnly || !DomHandler.isAndroid()) {\n      return;\n    }\n    this.validateKey(e, e.data, keyfilter);\n  },\n  onKeyPress: function onKeyPress(e, keyfilter, validateOnly) {\n    // non android devices use keydown\n    if (validateOnly || DomHandler.isAndroid()) {\n      return;\n    }\n    if (e.ctrlKey || e.altKey || e.metaKey) {\n      return;\n    }\n    this.validateKey(e, e.key, keyfilter);\n  },\n  onPaste: function onPaste(e, keyfilter, validateOnly) {\n    if (validateOnly) {\n      return;\n    }\n    var regex = this.getRegex(keyfilter);\n    var clipboard = e.clipboardData.getData('text');\n\n    // loop over each letter pasted and if any fail prevent the paste\n    _toConsumableArray(clipboard).forEach(function (c) {\n      if (!regex.test(c)) {\n        e.preventDefault();\n        return false;\n      }\n    });\n  },\n  validateKey: function validateKey(e, key, keyfilter) {\n    if (key === null || key === undefined) {\n      return;\n    }\n\n    // some AZERTY keys come in with 2 chars like ´ç if Dead key is pressed first\n    var isPrintableKey = key.length <= 2;\n    if (!isPrintableKey) {\n      return;\n    }\n    var regex = this.getRegex(keyfilter);\n    if (!regex.test(key)) {\n      e.preventDefault();\n    }\n  },\n  validate: function validate(e, keyfilter) {\n    var value = e.target.value;\n    var validatePattern = true;\n    var regex = this.getRegex(keyfilter);\n    if (value && !regex.test(value)) {\n      validatePattern = false;\n    }\n    return validatePattern;\n  }\n};\n\nexport { KeyFilter };\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,UAAU,QAAQ,kBAAkB;AAE7C,SAASC,iBAAiBA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAC/B,CAAC,IAAI,IAAIA,CAAC,IAAIA,CAAC,GAAGD,CAAC,CAACE,MAAM,MAAMD,CAAC,GAAGD,CAAC,CAACE,MAAM,CAAC;EAC7C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGC,KAAK,CAACJ,CAAC,CAAC,EAAEE,CAAC,GAAGF,CAAC,EAAEE,CAAC,EAAE,EAAEC,CAAC,CAACD,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACrD,OAAOC,CAAC;AACV;AAEA,SAASE,kBAAkBA,CAACN,CAAC,EAAE;EAC7B,IAAIK,KAAK,CAACE,OAAO,CAACP,CAAC,CAAC,EAAE,OAAOD,iBAAiB,CAACC,CAAC,CAAC;AACnD;AAEA,SAASQ,gBAAgBA,CAACR,CAAC,EAAE;EAC3B,IAAI,WAAW,IAAI,OAAOS,MAAM,IAAI,IAAI,IAAIT,CAAC,CAACS,MAAM,CAACC,QAAQ,CAAC,IAAI,IAAI,IAAIV,CAAC,CAAC,YAAY,CAAC,EAAE,OAAOK,KAAK,CAACM,IAAI,CAACX,CAAC,CAAC;AACjH;AAEA,SAASY,2BAA2BA,CAACZ,CAAC,EAAEC,CAAC,EAAE;EACzC,IAAID,CAAC,EAAE;IACL,IAAI,QAAQ,IAAI,OAAOA,CAAC,EAAE,OAAOD,iBAAiB,CAACC,CAAC,EAAEC,CAAC,CAAC;IACxD,IAAIY,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAACC,IAAI,CAACf,CAAC,CAAC,CAACgB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACxC,OAAO,QAAQ,KAAKH,CAAC,IAAIb,CAAC,CAACiB,WAAW,KAAKJ,CAAC,GAAGb,CAAC,CAACiB,WAAW,CAACC,IAAI,CAAC,EAAE,KAAK,KAAKL,CAAC,IAAI,KAAK,KAAKA,CAAC,GAAGR,KAAK,CAACM,IAAI,CAACX,CAAC,CAAC,GAAG,WAAW,KAAKa,CAAC,IAAI,0CAA0C,CAACM,IAAI,CAACN,CAAC,CAAC,GAAGd,iBAAiB,CAACC,CAAC,EAAEC,CAAC,CAAC,GAAG,KAAK,CAAC;EAC7N;AACF;AAEA,SAASmB,kBAAkBA,CAAA,EAAG;EAC5B,MAAM,IAAIC,SAAS,CAAC,sIAAsI,CAAC;AAC7J;AAEA,SAASC,kBAAkBA,CAACtB,CAAC,EAAE;EAC7B,OAAOM,kBAAkB,CAACN,CAAC,CAAC,IAAIQ,gBAAgB,CAACR,CAAC,CAAC,IAAIY,2BAA2B,CAACZ,CAAC,CAAC,IAAIoB,kBAAkB,CAAC,CAAC;AAC/G;AAEA,IAAIG,SAAS,GAAG;EACd;EACAC,aAAa,EAAE;IACbC,IAAI,EAAE,MAAM;IACZ,KAAK,EAAE,QAAQ;IACfC,IAAI,EAAE,QAAQ;IACdC,KAAK,EAAE,WAAW;IAClBC,GAAG,EAAE,UAAU;IACfC,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,iBAAiB;IACxBC,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE;EACZ,CAAC;EACD,mBAAmBC,QAAQ,EAAE,SAASA,QAAQA,CAACC,SAAS,EAAE;IACxD,OAAOX,SAAS,CAACC,aAAa,CAACU,SAAS,CAAC,GAAGX,SAAS,CAACC,aAAa,CAACU,SAAS,CAAC,GAAGA,SAAS;EAC5F,CAAC;EACDC,aAAa,EAAE,SAASA,aAAaA,CAAChC,CAAC,EAAE+B,SAAS,EAAEE,YAAY,EAAE;IAChE;IACA,IAAIA,YAAY,IAAI,CAACtC,UAAU,CAACuC,SAAS,CAAC,CAAC,EAAE;MAC3C;IACF;IACA,IAAI,CAACC,WAAW,CAACnC,CAAC,EAAEA,CAAC,CAACoC,IAAI,EAAEL,SAAS,CAAC;EACxC,CAAC;EACDM,UAAU,EAAE,SAASA,UAAUA,CAACrC,CAAC,EAAE+B,SAAS,EAAEE,YAAY,EAAE;IAC1D;IACA,IAAIA,YAAY,IAAItC,UAAU,CAACuC,SAAS,CAAC,CAAC,EAAE;MAC1C;IACF;IACA,IAAIlC,CAAC,CAACsC,OAAO,IAAItC,CAAC,CAACuC,MAAM,IAAIvC,CAAC,CAACwC,OAAO,EAAE;MACtC;IACF;IACA,IAAI,CAACL,WAAW,CAACnC,CAAC,EAAEA,CAAC,CAACyC,GAAG,EAAEV,SAAS,CAAC;EACvC,CAAC;EACDW,OAAO,EAAE,SAASA,OAAOA,CAAC1C,CAAC,EAAE+B,SAAS,EAAEE,YAAY,EAAE;IACpD,IAAIA,YAAY,EAAE;MAChB;IACF;IACA,IAAIU,KAAK,GAAG,IAAI,CAACb,QAAQ,CAACC,SAAS,CAAC;IACpC,IAAIa,SAAS,GAAG5C,CAAC,CAAC6C,aAAa,CAACC,OAAO,CAAC,MAAM,CAAC;;IAE/C;IACA3B,kBAAkB,CAACyB,SAAS,CAAC,CAACG,OAAO,CAAC,UAAUC,CAAC,EAAE;MACjD,IAAI,CAACL,KAAK,CAAC3B,IAAI,CAACgC,CAAC,CAAC,EAAE;QAClBhD,CAAC,CAACiD,cAAc,CAAC,CAAC;QAClB,OAAO,KAAK;MACd;IACF,CAAC,CAAC;EACJ,CAAC;EACDd,WAAW,EAAE,SAASA,WAAWA,CAACnC,CAAC,EAAEyC,GAAG,EAAEV,SAAS,EAAE;IACnD,IAAIU,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKS,SAAS,EAAE;MACrC;IACF;;IAEA;IACA,IAAIC,cAAc,GAAGV,GAAG,CAAC1C,MAAM,IAAI,CAAC;IACpC,IAAI,CAACoD,cAAc,EAAE;MACnB;IACF;IACA,IAAIR,KAAK,GAAG,IAAI,CAACb,QAAQ,CAACC,SAAS,CAAC;IACpC,IAAI,CAACY,KAAK,CAAC3B,IAAI,CAACyB,GAAG,CAAC,EAAE;MACpBzC,CAAC,CAACiD,cAAc,CAAC,CAAC;IACpB;EACF,CAAC;EACDG,QAAQ,EAAE,SAASA,QAAQA,CAACpD,CAAC,EAAE+B,SAAS,EAAE;IACxC,IAAIsB,KAAK,GAAGrD,CAAC,CAACsD,MAAM,CAACD,KAAK;IAC1B,IAAIE,eAAe,GAAG,IAAI;IAC1B,IAAIZ,KAAK,GAAG,IAAI,CAACb,QAAQ,CAACC,SAAS,CAAC;IACpC,IAAIsB,KAAK,IAAI,CAACV,KAAK,CAAC3B,IAAI,CAACqC,KAAK,CAAC,EAAE;MAC/BE,eAAe,GAAG,KAAK;IACzB;IACA,OAAOA,eAAe;EACxB;AACF,CAAC;AAED,SAASnC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}