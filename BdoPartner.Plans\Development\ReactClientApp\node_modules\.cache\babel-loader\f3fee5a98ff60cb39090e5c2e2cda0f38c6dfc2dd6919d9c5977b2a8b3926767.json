{"ast": null, "code": "/**\r\n *  Reference to Enumerations.Role in server side.\r\n *  Reference to records in table dbo.[Role].\r\n *  Work for Authorizatoin check.\r\n */\nexport const Role = {\n  // Partner Plans System Admin\n  PPAdministrator: \"15\",\n  // Active Partner.\n  ActivePartner: \"3\",\n  // New Partner.\n  NewPartner: \"16\",\n  // Reviewer role.\n  ExecutiveLeadership: \"17\"\n};", "map": {"version": 3, "names": ["Role", "PPAdministrator", "ActivePartner", "<PERSON><PERSON><PERSON><PERSON>", "ExecutiveLeadership"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/core/enumertions/role.js"], "sourcesContent": ["/**\r\n *  Reference to Enumerations.Role in server side.\r\n *  Reference to records in table dbo.[Role].\r\n *  Work for Authorizatoin check.\r\n */\r\nexport const Role = {\r\n  // Partner Plans System Admin\r\n  PPAdministrator: \"15\",\r\n  // Active Partner.\r\n  ActivePartner: \"3\",\r\n  // New Partner.\r\n  NewPartner: \"16\",\r\n  // Reviewer role.\r\n  ExecutiveLeadership: \"17\",\r\n};\r\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,IAAI,GAAG;EAClB;EACAC,eAAe,EAAE,IAAI;EACrB;EACAC,aAAa,EAAE,GAAG;EAClB;EACAC,UAAU,EAAE,IAAI;EAChB;EACAC,mBAAmB,EAAE;AACvB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}