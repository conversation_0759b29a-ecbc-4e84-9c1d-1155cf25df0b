{"ast": null, "code": "import { asyncScheduler } from '../scheduler/async';\nimport { audit } from './audit';\nimport { timer } from '../observable/timer';\nexport function auditTime(duration, scheduler) {\n  if (scheduler === void 0) {\n    scheduler = asyncScheduler;\n  }\n  return audit(function () {\n    return timer(duration, scheduler);\n  });\n}", "map": {"version": 3, "names": ["asyncScheduler", "audit", "timer", "auditTime", "duration", "scheduler"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\auditTime.ts"], "sourcesContent": ["import { asyncScheduler } from '../scheduler/async';\nimport { audit } from './audit';\nimport { timer } from '../observable/timer';\nimport { MonoTypeOperatorFunction, SchedulerLike } from '../types';\n\n/**\n * Ignores source values for `duration` milliseconds, then emits the most recent\n * value from the source Observable, then repeats this process.\n *\n * <span class=\"informal\">When it sees a source value, it ignores that plus\n * the next ones for `duration` milliseconds, and then it emits the most recent\n * value from the source.</span>\n *\n * ![](auditTime.png)\n *\n * `auditTime` is similar to `throttleTime`, but emits the last value from the\n * silenced time window, instead of the first value. `auditTime` emits the most\n * recent value from the source Observable on the output Observable as soon as\n * its internal timer becomes disabled, and ignores source values while the\n * timer is enabled. Initially, the timer is disabled. As soon as the first\n * source value arrives, the timer is enabled. After `duration` milliseconds (or\n * the time unit determined internally by the optional `scheduler`) has passed,\n * the timer is disabled, then the most recent source value is emitted on the\n * output Observable, and this process repeats for the next source value.\n * Optionally takes a {@link SchedulerLike} for managing timers.\n *\n * ## Example\n *\n * Emit clicks at a rate of at most one click per second\n *\n * ```ts\n * import { fromEvent, auditTime } from 'rxjs';\n *\n * const clicks = fromEvent(document, 'click');\n * const result = clicks.pipe(auditTime(1000));\n * result.subscribe(x => console.log(x));\n * ```\n *\n * @see {@link audit}\n * @see {@link debounceTime}\n * @see {@link delay}\n * @see {@link sampleTime}\n * @see {@link throttleTime}\n *\n * @param duration Time to wait before emitting the most recent source value,\n * measured in milliseconds or the time unit determined internally by the\n * optional `scheduler`.\n * @param scheduler The {@link SchedulerLike} to use for managing the timers\n * that handle the rate-limiting behavior.\n * @return A function that returns an Observable that performs rate-limiting of\n * emissions from the source Observable.\n */\nexport function auditTime<T>(duration: number, scheduler: SchedulerLike = asyncScheduler): MonoTypeOperatorFunction<T> {\n  return audit(() => timer(duration, scheduler));\n}\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,oBAAoB;AACnD,SAASC,KAAK,QAAQ,SAAS;AAC/B,SAASC,KAAK,QAAQ,qBAAqB;AAkD3C,OAAM,SAAUC,SAASA,CAAIC,QAAgB,EAAEC,SAAyC;EAAzC,IAAAA,SAAA;IAAAA,SAAA,GAAAL,cAAyC;EAAA;EACtF,OAAOC,KAAK,CAAC;IAAM,OAAAC,KAAK,CAACE,QAAQ,EAAEC,SAAS,CAAC;EAA1B,CAA0B,CAAC;AAChD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}