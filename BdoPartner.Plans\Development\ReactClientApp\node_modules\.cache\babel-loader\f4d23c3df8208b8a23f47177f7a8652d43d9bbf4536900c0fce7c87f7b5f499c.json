{"ast": null, "code": "import { __read, __spreadArray } from \"tslib\";\nimport { concat } from './concat';\nexport function concatWith() {\n  var otherSources = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    otherSources[_i] = arguments[_i];\n  }\n  return concat.apply(void 0, __spreadArray([], __read(otherSources)));\n}", "map": {"version": 3, "names": ["concat", "concatWith", "otherSources", "_i", "arguments", "length", "apply", "__spread<PERSON><PERSON>y", "__read"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\concatWith.ts"], "sourcesContent": ["import { ObservableInputTuple, OperatorFunction } from '../types';\nimport { concat } from './concat';\n\n/**\n * Emits all of the values from the source observable, then, once it completes, subscribes\n * to each observable source provided, one at a time, emitting all of their values, and not subscribing\n * to the next one until it completes.\n *\n * `concat(a$, b$, c$)` is the same as `a$.pipe(concatWith(b$, c$))`.\n *\n * ## Example\n *\n * Listen for one mouse click, then listen for all mouse moves.\n *\n * ```ts\n * import { fromEvent, map, take, concatWith } from 'rxjs';\n *\n * const clicks$ = fromEvent(document, 'click');\n * const moves$ = fromEvent(document, 'mousemove');\n *\n * clicks$.pipe(\n *   map(() => 'click'),\n *   take(1),\n *   concatWith(\n *     moves$.pipe(\n *       map(() => 'move')\n *     )\n *   )\n * )\n * .subscribe(x => console.log(x));\n *\n * // 'click'\n * // 'move'\n * // 'move'\n * // 'move'\n * // ...\n * ```\n *\n * @param otherSources Other observable sources to subscribe to, in sequence, after the original source is complete.\n * @return A function that returns an Observable that concatenates\n * subscriptions to the source and provided Observables subscribing to the next\n * only once the current subscription completes.\n */\nexport function concatWith<T, A extends readonly unknown[]>(\n  ...otherSources: [...ObservableInputTuple<A>]\n): OperatorFunction<T, T | A[number]> {\n  return concat(...otherSources);\n}\n"], "mappings": ";AACA,SAASA,MAAM,QAAQ,UAAU;AA0CjC,OAAM,SAAUC,UAAUA,CAAA;EACxB,IAAAC,YAAA;OAAA,IAAAC,EAAA,IAA6C,EAA7CA,EAAA,GAAAC,SAAA,CAAAC,MAA6C,EAA7CF,EAAA,EAA6C;IAA7CD,YAAA,CAAAC,EAAA,IAAAC,SAAA,CAAAD,EAAA;;EAEA,OAAOH,MAAM,CAAAM,KAAA,SAAAC,aAAA,KAAAC,MAAA,CAAIN,YAAY;AAC/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}