{"ast": null, "code": "export var KEY_PREFIX = 'persist:';\nexport var FLUSH = 'persist/FLUSH';\nexport var REHYDRATE = 'persist/REHYDRATE';\nexport var PAUSE = 'persist/PAUSE';\nexport var PERSIST = 'persist/PERSIST';\nexport var PURGE = 'persist/PURGE';\nexport var REGISTER = 'persist/REGISTER';\nexport var DEFAULT_VERSION = -1;", "map": {"version": 3, "names": ["KEY_PREFIX", "FLUSH", "REHYDRATE", "PAUSE", "PERSIST", "PURGE", "REGISTER", "DEFAULT_VERSION"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/node_modules/redux-persist/es/constants.js"], "sourcesContent": ["export var KEY_PREFIX = 'persist:';\nexport var FLUSH = 'persist/FLUSH';\nexport var REHYDRATE = 'persist/REHYDRATE';\nexport var PAUSE = 'persist/PAUSE';\nexport var PERSIST = 'persist/PERSIST';\nexport var PURGE = 'persist/PURGE';\nexport var REGISTER = 'persist/REGISTER';\nexport var DEFAULT_VERSION = -1;"], "mappings": "AAAA,OAAO,IAAIA,UAAU,GAAG,UAAU;AAClC,OAAO,IAAIC,KAAK,GAAG,eAAe;AAClC,OAAO,IAAIC,SAAS,GAAG,mBAAmB;AAC1C,OAAO,IAAIC,KAAK,GAAG,eAAe;AAClC,OAAO,IAAIC,OAAO,GAAG,iBAAiB;AACtC,OAAO,IAAIC,KAAK,GAAG,eAAe;AAClC,OAAO,IAAIC,QAAQ,GAAG,kBAAkB;AACxC,OAAO,IAAIC,eAAe,GAAG,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}