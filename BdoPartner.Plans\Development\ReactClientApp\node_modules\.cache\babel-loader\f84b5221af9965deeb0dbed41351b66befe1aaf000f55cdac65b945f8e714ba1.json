{"ast": null, "code": "import { BehaviorSubject } from '../BehaviorSubject';\nimport { ConnectableObservable } from '../observable/ConnectableObservable';\nexport function publishBehavior(initialValue) {\n  return function (source) {\n    var subject = new BehaviorSubject(initialValue);\n    return new ConnectableObservable(source, function () {\n      return subject;\n    });\n  };\n}", "map": {"version": 3, "names": ["BehaviorSubject", "ConnectableObservable", "publish<PERSON>eh<PERSON>or", "initialValue", "source", "subject"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\publishBehavior.ts"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { BehaviorSubject } from '../BehaviorSubject';\nimport { ConnectableObservable } from '../observable/ConnectableObservable';\nimport { UnaryFunction } from '../types';\n\n/**\n * Creates a {@link ConnectableObservable} that utilizes a {@link BehaviorSubject}.\n *\n * @param initialValue The initial value passed to the {@link BehaviorSubject}.\n * @return A function that returns a {@link ConnectableObservable}\n * @deprecated Will be removed in v8. To create a connectable observable that uses a\n * {@link BehaviorSubject} under the hood, use {@link connectable}.\n * `source.pipe(publishBehavior(initValue))` is equivalent to\n * `connectable(source, { connector: () => new BehaviorSubject(initValue), resetOnDisconnect: false })`.\n * If you're using {@link refCount} after `publishBehavior`, use the {@link share} operator instead.\n * `source.pipe(publishBehavior(initValue), refCount())` is equivalent to\n * `source.pipe(share({ connector: () => new BehaviorSubject(initValue), resetOnError: false, resetOnComplete: false, resetOnRefCountZero: false  }))`.\n * Details: https://rxjs.dev/deprecations/multicasting\n */\nexport function publishBehavior<T>(initialValue: T): UnaryFunction<Observable<T>, ConnectableObservable<T>> {\n  // Note that this has *never* supported the selector function.\n  return (source) => {\n    const subject = new BehaviorSubject<T>(initialValue);\n    return new ConnectableObservable(source, () => subject);\n  };\n}\n"], "mappings": "AACA,SAASA,eAAe,QAAQ,oBAAoB;AACpD,SAASC,qBAAqB,QAAQ,qCAAqC;AAiB3E,OAAM,SAAUC,eAAeA,CAAIC,YAAe;EAEhD,OAAO,UAACC,MAAM;IACZ,IAAMC,OAAO,GAAG,IAAIL,eAAe,CAAIG,YAAY,CAAC;IACpD,OAAO,IAAIF,qBAAqB,CAACG,MAAM,EAAE;MAAM,OAAAC,OAAO;IAAP,CAAO,CAAC;EACzD,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}