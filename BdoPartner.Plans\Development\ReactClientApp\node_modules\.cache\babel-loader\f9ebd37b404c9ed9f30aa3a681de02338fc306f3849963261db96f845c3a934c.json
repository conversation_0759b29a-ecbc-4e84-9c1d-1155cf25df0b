{"ast": null, "code": "import { exhaustAll } from './exhaustAll';\nexport var exhaust = exhaustAll;", "map": {"version": 3, "names": ["exhaustAll", "exhaust"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\exhaust.ts"], "sourcesContent": ["import { exhaustAll } from './exhaustAll';\n\n/**\n * @deprecated Renamed to {@link exhaustAll}. Will be removed in v8.\n */\nexport const exhaust = exhaustAll;\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,cAAc;AAKzC,OAAO,IAAMC,OAAO,GAAGD,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}