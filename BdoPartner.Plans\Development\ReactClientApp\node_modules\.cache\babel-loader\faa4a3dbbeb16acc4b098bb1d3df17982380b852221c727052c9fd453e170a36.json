{"ast": null, "code": "\"use strict\";\n\nexports.__esModule = true;\nexports.default = createWebStorage;\nvar _getStorage = _interopRequireDefault(require(\"./getStorage\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction createWebStorage(type) {\n  var storage = (0, _getStorage.default)(type);\n  return {\n    getItem: function getItem(key) {\n      return new Promise(function (resolve, reject) {\n        resolve(storage.getItem(key));\n      });\n    },\n    setItem: function setItem(key, item) {\n      return new Promise(function (resolve, reject) {\n        resolve(storage.setItem(key, item));\n      });\n    },\n    removeItem: function removeItem(key) {\n      return new Promise(function (resolve, reject) {\n        resolve(storage.removeItem(key));\n      });\n    }\n  };\n}", "map": {"version": 3, "names": ["exports", "__esModule", "default", "createWebStorage", "_getStorage", "_interopRequireDefault", "require", "obj", "type", "storage", "getItem", "key", "Promise", "resolve", "reject", "setItem", "item", "removeItem"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/node_modules/redux-persist/lib/storage/createWebStorage.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.default = createWebStorage;\n\nvar _getStorage = _interopRequireDefault(require(\"./getStorage\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction createWebStorage(type) {\n  var storage = (0, _getStorage.default)(type);\n  return {\n    getItem: function getItem(key) {\n      return new Promise(function (resolve, reject) {\n        resolve(storage.getItem(key));\n      });\n    },\n    setItem: function setItem(key, item) {\n      return new Promise(function (resolve, reject) {\n        resolve(storage.setItem(key, item));\n      });\n    },\n    removeItem: function removeItem(key) {\n      return new Promise(function (resolve, reject) {\n        resolve(storage.removeItem(key));\n      });\n    }\n  };\n}"], "mappings": "AAAA,YAAY;;AAEZA,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACE,OAAO,GAAGC,gBAAgB;AAElC,IAAIC,WAAW,GAAGC,sBAAsB,CAACC,OAAO,CAAC,cAAc,CAAC,CAAC;AAEjE,SAASD,sBAAsBA,CAACE,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACN,UAAU,GAAGM,GAAG,GAAG;IAAEL,OAAO,EAAEK;EAAI,CAAC;AAAE;AAE9F,SAASJ,gBAAgBA,CAACK,IAAI,EAAE;EAC9B,IAAIC,OAAO,GAAG,CAAC,CAAC,EAAEL,WAAW,CAACF,OAAO,EAAEM,IAAI,CAAC;EAC5C,OAAO;IACLE,OAAO,EAAE,SAASA,OAAOA,CAACC,GAAG,EAAE;MAC7B,OAAO,IAAIC,OAAO,CAAC,UAAUC,OAAO,EAAEC,MAAM,EAAE;QAC5CD,OAAO,CAACJ,OAAO,CAACC,OAAO,CAACC,GAAG,CAAC,CAAC;MAC/B,CAAC,CAAC;IACJ,CAAC;IACDI,OAAO,EAAE,SAASA,OAAOA,CAACJ,GAAG,EAAEK,IAAI,EAAE;MACnC,OAAO,IAAIJ,OAAO,CAAC,UAAUC,OAAO,EAAEC,MAAM,EAAE;QAC5CD,OAAO,CAACJ,OAAO,CAACM,OAAO,CAACJ,GAAG,EAAEK,IAAI,CAAC,CAAC;MACrC,CAAC,CAAC;IACJ,CAAC;IACDC,UAAU,EAAE,SAASA,UAAUA,CAACN,GAAG,EAAE;MACnC,OAAO,IAAIC,OAAO,CAAC,UAAUC,OAAO,EAAEC,MAAM,EAAE;QAC5CD,OAAO,CAACJ,OAAO,CAACQ,UAAU,CAACN,GAAG,CAAC,CAAC;MAClC,CAAC,CAAC;IACJ;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}