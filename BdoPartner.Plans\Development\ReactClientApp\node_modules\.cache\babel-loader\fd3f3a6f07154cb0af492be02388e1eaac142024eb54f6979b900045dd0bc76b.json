{"ast": null, "code": "import { identity } from './identity';\nexport function pipe() {\n  var fns = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    fns[_i] = arguments[_i];\n  }\n  return pipeFromArray(fns);\n}\nexport function pipeFromArray(fns) {\n  if (fns.length === 0) {\n    return identity;\n  }\n  if (fns.length === 1) {\n    return fns[0];\n  }\n  return function piped(input) {\n    return fns.reduce(function (prev, fn) {\n      return fn(prev);\n    }, input);\n  };\n}", "map": {"version": 3, "names": ["identity", "pipe", "fns", "_i", "arguments", "length", "pipeFromArray", "piped", "input", "reduce", "prev", "fn"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\util\\pipe.ts"], "sourcesContent": ["import { identity } from './identity';\nimport { UnaryFunction } from '../types';\n\nexport function pipe(): typeof identity;\nexport function pipe<T, A>(fn1: UnaryFunction<T, A>): UnaryFunction<T, A>;\nexport function pipe<T, A, B>(fn1: UnaryFunction<T, A>, fn2: UnaryFunction<A, B>): UnaryFunction<T, B>;\nexport function pipe<T, A, B, C>(fn1: UnaryFunction<T, A>, fn2: UnaryFunction<A, B>, fn3: UnaryFunction<B, C>): UnaryFunction<T, C>;\nexport function pipe<T, A, B, C, D>(\n  fn1: UnaryFunction<T, A>,\n  fn2: UnaryFunction<A, B>,\n  fn3: UnaryFunction<B, C>,\n  fn4: UnaryFunction<C, D>\n): UnaryFunction<T, D>;\nexport function pipe<T, A, B, C, D, E>(\n  fn1: UnaryFunction<T, A>,\n  fn2: UnaryFunction<A, B>,\n  fn3: UnaryFunction<B, C>,\n  fn4: UnaryFunction<C, D>,\n  fn5: UnaryFunction<D, E>\n): UnaryFunction<T, E>;\nexport function pipe<T, A, B, C, D, E, F>(\n  fn1: UnaryFunction<T, A>,\n  fn2: UnaryFunction<A, B>,\n  fn3: UnaryFunction<B, C>,\n  fn4: UnaryFunction<C, D>,\n  fn5: UnaryFunction<D, E>,\n  fn6: UnaryFunction<E, F>\n): UnaryFunction<T, F>;\nexport function pipe<T, A, B, C, D, E, F, G>(\n  fn1: UnaryFunction<T, A>,\n  fn2: UnaryFunction<A, B>,\n  fn3: UnaryFunction<B, C>,\n  fn4: UnaryFunction<C, D>,\n  fn5: UnaryFunction<D, E>,\n  fn6: UnaryFunction<E, F>,\n  fn7: UnaryFunction<F, G>\n): UnaryFunction<T, G>;\nexport function pipe<T, A, B, C, D, E, F, G, H>(\n  fn1: UnaryFunction<T, A>,\n  fn2: UnaryFunction<A, B>,\n  fn3: UnaryFunction<B, C>,\n  fn4: UnaryFunction<C, D>,\n  fn5: UnaryFunction<D, E>,\n  fn6: UnaryFunction<E, F>,\n  fn7: UnaryFunction<F, G>,\n  fn8: UnaryFunction<G, H>\n): UnaryFunction<T, H>;\nexport function pipe<T, A, B, C, D, E, F, G, H, I>(\n  fn1: UnaryFunction<T, A>,\n  fn2: UnaryFunction<A, B>,\n  fn3: UnaryFunction<B, C>,\n  fn4: UnaryFunction<C, D>,\n  fn5: UnaryFunction<D, E>,\n  fn6: UnaryFunction<E, F>,\n  fn7: UnaryFunction<F, G>,\n  fn8: UnaryFunction<G, H>,\n  fn9: UnaryFunction<H, I>\n): UnaryFunction<T, I>;\nexport function pipe<T, A, B, C, D, E, F, G, H, I>(\n  fn1: UnaryFunction<T, A>,\n  fn2: UnaryFunction<A, B>,\n  fn3: UnaryFunction<B, C>,\n  fn4: UnaryFunction<C, D>,\n  fn5: UnaryFunction<D, E>,\n  fn6: UnaryFunction<E, F>,\n  fn7: UnaryFunction<F, G>,\n  fn8: UnaryFunction<G, H>,\n  fn9: UnaryFunction<H, I>,\n  ...fns: UnaryFunction<any, any>[]\n): UnaryFunction<T, unknown>;\n\n/**\n * pipe() can be called on one or more functions, each of which can take one argument (\"UnaryFunction\")\n * and uses it to return a value.\n * It returns a function that takes one argument, passes it to the first UnaryFunction, and then\n * passes the result to the next one, passes that result to the next one, and so on.  \n */\nexport function pipe(...fns: Array<UnaryFunction<any, any>>): UnaryFunction<any, any> {\n  return pipeFromArray(fns);\n}\n\n/** @internal */\nexport function pipeFromArray<T, R>(fns: Array<UnaryFunction<T, R>>): UnaryFunction<T, R> {\n  if (fns.length === 0) {\n    return identity as UnaryFunction<any, any>;\n  }\n\n  if (fns.length === 1) {\n    return fns[0];\n  }\n\n  return function piped(input: T): R {\n    return fns.reduce((prev: any, fn: UnaryFunction<T, R>) => fn(prev), input as any);\n  };\n}\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,YAAY;AA6ErC,OAAM,SAAUC,IAAIA,CAAA;EAAC,IAAAC,GAAA;OAAA,IAAAC,EAAA,IAAsC,EAAtCA,EAAA,GAAAC,SAAA,CAAAC,MAAsC,EAAtCF,EAAA,EAAsC;IAAtCD,GAAA,CAAAC,EAAA,IAAAC,SAAA,CAAAD,EAAA;;EACnB,OAAOG,aAAa,CAACJ,GAAG,CAAC;AAC3B;AAGA,OAAM,SAAUI,aAAaA,CAAOJ,GAA+B;EACjE,IAAIA,GAAG,CAACG,MAAM,KAAK,CAAC,EAAE;IACpB,OAAOL,QAAmC;;EAG5C,IAAIE,GAAG,CAACG,MAAM,KAAK,CAAC,EAAE;IACpB,OAAOH,GAAG,CAAC,CAAC,CAAC;;EAGf,OAAO,SAASK,KAAKA,CAACC,KAAQ;IAC5B,OAAON,GAAG,CAACO,MAAM,CAAC,UAACC,IAAS,EAAEC,EAAuB;MAAK,OAAAA,EAAE,CAACD,IAAI,CAAC;IAAR,CAAQ,EAAEF,KAAY,CAAC;EACnF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}