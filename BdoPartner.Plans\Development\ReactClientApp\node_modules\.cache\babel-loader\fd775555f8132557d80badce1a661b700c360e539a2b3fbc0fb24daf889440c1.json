{"ast": null, "code": "/** Corporate with BusinessResultOf<T>.\r\n *  Corporate with server side project, Enumerations.ResultStatus definitions. */\nexport const ResultStatus = {\n  Success: 1,\n  Failure: 2,\n  Warning: 3\n};", "map": {"version": 3, "names": ["ResultStatus", "Success", "Failure", "Warning"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/src/core/enumertions/resultStatus.js"], "sourcesContent": ["/** Corporate with BusinessResultOf<T>.\r\n *  Corporate with server side project, Enumerations.ResultStatus definitions. */\r\nexport const ResultStatus = {\r\n  Success: 1,\r\n  Failure: 2,\r\n  Warning: 3,\r\n};\r\n"], "mappings": "AAAA;AACA;AACA,OAAO,MAAMA,YAAY,GAAG;EAC1BC,OAAO,EAAE,CAAC;EACVC,OAAO,EAAE,CAAC;EACVC,OAAO,EAAE;AACX,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}