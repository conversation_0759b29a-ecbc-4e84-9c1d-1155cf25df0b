{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport { PrimeReactContext } from 'primereact/api';\nimport { ComponentBase, useHandleStyle } from 'primereact/componentbase';\nimport { useMergeProps } from 'primereact/hooks';\nimport { classNames } from 'primereact/utils';\nvar classes = {\n  root: 'p-progress-spinner',\n  spinner: 'p-progress-spinner-svg',\n  circle: 'p-progress-spinner-circle'\n};\nvar styles = \"\\n@layer primereact {\\n    .p-progress-spinner {\\n        position: relative;\\n        margin: 0 auto;\\n        width: 100px;\\n        height: 100px;\\n        display: inline-block;\\n    }\\n    \\n    .p-progress-spinner::before {\\n        content: '';\\n        display: block;\\n        padding-top: 100%;\\n    }\\n    \\n    .p-progress-spinner-svg {\\n        animation: p-progress-spinner-rotate 2s linear infinite;\\n        height: 100%;\\n        transform-origin: center center;\\n        width: 100%;\\n        position: absolute;\\n        top: 0;\\n        bottom: 0;\\n        left: 0;\\n        right: 0;\\n        margin: auto;\\n    }\\n    \\n    .p-progress-spinner-circle {\\n        stroke-dasharray: 89, 200;\\n        stroke-dashoffset: 0;\\n        stroke: #d62d20;\\n        animation: p-progress-spinner-dash 1.5s ease-in-out infinite, p-progress-spinner-color 6s ease-in-out infinite;\\n        stroke-linecap: round;\\n    }\\n}\\n\\n@keyframes p-progress-spinner-rotate {\\n    100% {\\n        transform: rotate(360deg);\\n    }\\n}\\n\\n@keyframes p-progress-spinner-dash {\\n    0% {\\n        stroke-dasharray: 1, 200;\\n        stroke-dashoffset: 0;\\n    }\\n    50% {\\n        stroke-dasharray: 89, 200;\\n        stroke-dashoffset: -35px;\\n    }\\n    100% {\\n        stroke-dasharray: 89, 200;\\n        stroke-dashoffset: -124px;\\n    }\\n}\\n\\n@keyframes p-progress-spinner-color {\\n    100%,\\n    0% {\\n        stroke: #d62d20;\\n    }\\n    40% {\\n        stroke: #0057e7;\\n    }\\n    66% {\\n        stroke: #008744;\\n    }\\n    80%,\\n    90% {\\n        stroke: #ffa700;\\n    }\\n}\\n\";\nvar inlineStyles = {\n  spinner: function spinner(_ref) {\n    var props = _ref.props;\n    return {\n      animationDuration: props.animationDuration\n    };\n  }\n};\nvar ProgressSpinnerBase = ComponentBase.extend({\n  defaultProps: {\n    __TYPE: 'ProgressSpinner',\n    id: null,\n    style: null,\n    className: null,\n    strokeWidth: '2',\n    fill: 'none',\n    animationDuration: '2s',\n    children: undefined\n  },\n  css: {\n    classes: classes,\n    styles: styles,\n    inlineStyles: inlineStyles\n  }\n});\nvar ProgressSpinner = /*#__PURE__*/React.memo(/*#__PURE__*/React.forwardRef(function (inProps, ref) {\n  var mergeProps = useMergeProps();\n  var context = React.useContext(PrimeReactContext);\n  var props = ProgressSpinnerBase.getProps(inProps, context);\n  var elementRef = React.useRef(null);\n  var _ProgressSpinnerBase$ = ProgressSpinnerBase.setMetaData({\n      props: props\n    }),\n    ptm = _ProgressSpinnerBase$.ptm,\n    cx = _ProgressSpinnerBase$.cx,\n    sx = _ProgressSpinnerBase$.sx,\n    isUnstyled = _ProgressSpinnerBase$.isUnstyled;\n  useHandleStyle(ProgressSpinnerBase.css.styles, isUnstyled, {\n    name: 'progressspinner'\n  });\n  React.useImperativeHandle(ref, function () {\n    return {\n      props: props,\n      getElement: function getElement() {\n        return elementRef.current;\n      }\n    };\n  });\n  var rootProps = mergeProps({\n    id: props.id,\n    ref: elementRef,\n    style: props.style,\n    className: classNames(props.className, cx('root')),\n    role: 'progressbar',\n    'aria-busy': true\n  }, ProgressSpinnerBase.getOtherProps(props), ptm('root'));\n  var spinnerProps = mergeProps({\n    className: cx('spinner'),\n    viewBox: '25 25 50 50',\n    style: sx('spinner')\n  }, ptm('spinner'));\n  var circleProps = mergeProps({\n    className: cx('circle'),\n    cx: '50',\n    cy: '50',\n    r: '20',\n    fill: props.fill,\n    strokeWidth: props.strokeWidth,\n    strokeMiterlimit: '10'\n  }, ptm('circle'));\n  return /*#__PURE__*/React.createElement(\"div\", rootProps, /*#__PURE__*/React.createElement(\"svg\", spinnerProps, /*#__PURE__*/React.createElement(\"circle\", circleProps)));\n}));\nProgressSpinner.displayName = 'ProgressSpinner';\nexport { ProgressSpinner };", "map": {"version": 3, "names": ["React", "PrimeReactContext", "ComponentBase", "useHandleStyle", "useMergeProps", "classNames", "classes", "root", "spinner", "circle", "styles", "inlineStyles", "_ref", "props", "animationDuration", "ProgressSpinnerBase", "extend", "defaultProps", "__TYPE", "id", "style", "className", "strokeWidth", "fill", "children", "undefined", "css", "ProgressSpinner", "memo", "forwardRef", "inProps", "ref", "mergeProps", "context", "useContext", "getProps", "elementRef", "useRef", "_ProgressSpinnerBase$", "setMetaData", "ptm", "cx", "sx", "isUnstyled", "name", "useImperativeHandle", "getElement", "current", "rootProps", "role", "getOtherProps", "spinnerProps", "viewBox", "circleProps", "cy", "r", "strokeMiterlimit", "createElement", "displayName"], "sources": ["C:/git/Partner-Site/BdoPartner.Plans/Development/ReactClientApp/node_modules/primereact/progressspinner/progressspinner.esm.js"], "sourcesContent": ["'use client';\nimport * as React from 'react';\nimport { PrimeReactContext } from 'primereact/api';\nimport { ComponentBase, useHandleStyle } from 'primereact/componentbase';\nimport { useMergeProps } from 'primereact/hooks';\nimport { classNames } from 'primereact/utils';\n\nvar classes = {\n  root: 'p-progress-spinner',\n  spinner: 'p-progress-spinner-svg',\n  circle: 'p-progress-spinner-circle'\n};\nvar styles = \"\\n@layer primereact {\\n    .p-progress-spinner {\\n        position: relative;\\n        margin: 0 auto;\\n        width: 100px;\\n        height: 100px;\\n        display: inline-block;\\n    }\\n    \\n    .p-progress-spinner::before {\\n        content: '';\\n        display: block;\\n        padding-top: 100%;\\n    }\\n    \\n    .p-progress-spinner-svg {\\n        animation: p-progress-spinner-rotate 2s linear infinite;\\n        height: 100%;\\n        transform-origin: center center;\\n        width: 100%;\\n        position: absolute;\\n        top: 0;\\n        bottom: 0;\\n        left: 0;\\n        right: 0;\\n        margin: auto;\\n    }\\n    \\n    .p-progress-spinner-circle {\\n        stroke-dasharray: 89, 200;\\n        stroke-dashoffset: 0;\\n        stroke: #d62d20;\\n        animation: p-progress-spinner-dash 1.5s ease-in-out infinite, p-progress-spinner-color 6s ease-in-out infinite;\\n        stroke-linecap: round;\\n    }\\n}\\n\\n@keyframes p-progress-spinner-rotate {\\n    100% {\\n        transform: rotate(360deg);\\n    }\\n}\\n\\n@keyframes p-progress-spinner-dash {\\n    0% {\\n        stroke-dasharray: 1, 200;\\n        stroke-dashoffset: 0;\\n    }\\n    50% {\\n        stroke-dasharray: 89, 200;\\n        stroke-dashoffset: -35px;\\n    }\\n    100% {\\n        stroke-dasharray: 89, 200;\\n        stroke-dashoffset: -124px;\\n    }\\n}\\n\\n@keyframes p-progress-spinner-color {\\n    100%,\\n    0% {\\n        stroke: #d62d20;\\n    }\\n    40% {\\n        stroke: #0057e7;\\n    }\\n    66% {\\n        stroke: #008744;\\n    }\\n    80%,\\n    90% {\\n        stroke: #ffa700;\\n    }\\n}\\n\";\nvar inlineStyles = {\n  spinner: function spinner(_ref) {\n    var props = _ref.props;\n    return {\n      animationDuration: props.animationDuration\n    };\n  }\n};\nvar ProgressSpinnerBase = ComponentBase.extend({\n  defaultProps: {\n    __TYPE: 'ProgressSpinner',\n    id: null,\n    style: null,\n    className: null,\n    strokeWidth: '2',\n    fill: 'none',\n    animationDuration: '2s',\n    children: undefined\n  },\n  css: {\n    classes: classes,\n    styles: styles,\n    inlineStyles: inlineStyles\n  }\n});\n\nvar ProgressSpinner = /*#__PURE__*/React.memo(/*#__PURE__*/React.forwardRef(function (inProps, ref) {\n  var mergeProps = useMergeProps();\n  var context = React.useContext(PrimeReactContext);\n  var props = ProgressSpinnerBase.getProps(inProps, context);\n  var elementRef = React.useRef(null);\n  var _ProgressSpinnerBase$ = ProgressSpinnerBase.setMetaData({\n      props: props\n    }),\n    ptm = _ProgressSpinnerBase$.ptm,\n    cx = _ProgressSpinnerBase$.cx,\n    sx = _ProgressSpinnerBase$.sx,\n    isUnstyled = _ProgressSpinnerBase$.isUnstyled;\n  useHandleStyle(ProgressSpinnerBase.css.styles, isUnstyled, {\n    name: 'progressspinner'\n  });\n  React.useImperativeHandle(ref, function () {\n    return {\n      props: props,\n      getElement: function getElement() {\n        return elementRef.current;\n      }\n    };\n  });\n  var rootProps = mergeProps({\n    id: props.id,\n    ref: elementRef,\n    style: props.style,\n    className: classNames(props.className, cx('root')),\n    role: 'progressbar',\n    'aria-busy': true\n  }, ProgressSpinnerBase.getOtherProps(props), ptm('root'));\n  var spinnerProps = mergeProps({\n    className: cx('spinner'),\n    viewBox: '25 25 50 50',\n    style: sx('spinner')\n  }, ptm('spinner'));\n  var circleProps = mergeProps({\n    className: cx('circle'),\n    cx: '50',\n    cy: '50',\n    r: '20',\n    fill: props.fill,\n    strokeWidth: props.strokeWidth,\n    strokeMiterlimit: '10'\n  }, ptm('circle'));\n  return /*#__PURE__*/React.createElement(\"div\", rootProps, /*#__PURE__*/React.createElement(\"svg\", spinnerProps, /*#__PURE__*/React.createElement(\"circle\", circleProps)));\n}));\nProgressSpinner.displayName = 'ProgressSpinner';\n\nexport { ProgressSpinner };\n"], "mappings": "AAAA,YAAY;;AACZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,iBAAiB,QAAQ,gBAAgB;AAClD,SAASC,aAAa,EAAEC,cAAc,QAAQ,0BAA0B;AACxE,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,UAAU,QAAQ,kBAAkB;AAE7C,IAAIC,OAAO,GAAG;EACZC,IAAI,EAAE,oBAAoB;EAC1BC,OAAO,EAAE,wBAAwB;EACjCC,MAAM,EAAE;AACV,CAAC;AACD,IAAIC,MAAM,GAAG,4iDAA4iD;AACzjD,IAAIC,YAAY,GAAG;EACjBH,OAAO,EAAE,SAASA,OAAOA,CAACI,IAAI,EAAE;IAC9B,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;IACtB,OAAO;MACLC,iBAAiB,EAAED,KAAK,CAACC;IAC3B,CAAC;EACH;AACF,CAAC;AACD,IAAIC,mBAAmB,GAAGb,aAAa,CAACc,MAAM,CAAC;EAC7CC,YAAY,EAAE;IACZC,MAAM,EAAE,iBAAiB;IACzBC,EAAE,EAAE,IAAI;IACRC,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,IAAI;IACfC,WAAW,EAAE,GAAG;IAChBC,IAAI,EAAE,MAAM;IACZT,iBAAiB,EAAE,IAAI;IACvBU,QAAQ,EAAEC;EACZ,CAAC;EACDC,GAAG,EAAE;IACHpB,OAAO,EAAEA,OAAO;IAChBI,MAAM,EAAEA,MAAM;IACdC,YAAY,EAAEA;EAChB;AACF,CAAC,CAAC;AAEF,IAAIgB,eAAe,GAAG,aAAa3B,KAAK,CAAC4B,IAAI,CAAC,aAAa5B,KAAK,CAAC6B,UAAU,CAAC,UAAUC,OAAO,EAAEC,GAAG,EAAE;EAClG,IAAIC,UAAU,GAAG5B,aAAa,CAAC,CAAC;EAChC,IAAI6B,OAAO,GAAGjC,KAAK,CAACkC,UAAU,CAACjC,iBAAiB,CAAC;EACjD,IAAIY,KAAK,GAAGE,mBAAmB,CAACoB,QAAQ,CAACL,OAAO,EAAEG,OAAO,CAAC;EAC1D,IAAIG,UAAU,GAAGpC,KAAK,CAACqC,MAAM,CAAC,IAAI,CAAC;EACnC,IAAIC,qBAAqB,GAAGvB,mBAAmB,CAACwB,WAAW,CAAC;MACxD1B,KAAK,EAAEA;IACT,CAAC,CAAC;IACF2B,GAAG,GAAGF,qBAAqB,CAACE,GAAG;IAC/BC,EAAE,GAAGH,qBAAqB,CAACG,EAAE;IAC7BC,EAAE,GAAGJ,qBAAqB,CAACI,EAAE;IAC7BC,UAAU,GAAGL,qBAAqB,CAACK,UAAU;EAC/CxC,cAAc,CAACY,mBAAmB,CAACW,GAAG,CAAChB,MAAM,EAAEiC,UAAU,EAAE;IACzDC,IAAI,EAAE;EACR,CAAC,CAAC;EACF5C,KAAK,CAAC6C,mBAAmB,CAACd,GAAG,EAAE,YAAY;IACzC,OAAO;MACLlB,KAAK,EAAEA,KAAK;MACZiC,UAAU,EAAE,SAASA,UAAUA,CAAA,EAAG;QAChC,OAAOV,UAAU,CAACW,OAAO;MAC3B;IACF,CAAC;EACH,CAAC,CAAC;EACF,IAAIC,SAAS,GAAGhB,UAAU,CAAC;IACzBb,EAAE,EAAEN,KAAK,CAACM,EAAE;IACZY,GAAG,EAAEK,UAAU;IACfhB,KAAK,EAAEP,KAAK,CAACO,KAAK;IAClBC,SAAS,EAAEhB,UAAU,CAACQ,KAAK,CAACQ,SAAS,EAAEoB,EAAE,CAAC,MAAM,CAAC,CAAC;IAClDQ,IAAI,EAAE,aAAa;IACnB,WAAW,EAAE;EACf,CAAC,EAAElC,mBAAmB,CAACmC,aAAa,CAACrC,KAAK,CAAC,EAAE2B,GAAG,CAAC,MAAM,CAAC,CAAC;EACzD,IAAIW,YAAY,GAAGnB,UAAU,CAAC;IAC5BX,SAAS,EAAEoB,EAAE,CAAC,SAAS,CAAC;IACxBW,OAAO,EAAE,aAAa;IACtBhC,KAAK,EAAEsB,EAAE,CAAC,SAAS;EACrB,CAAC,EAAEF,GAAG,CAAC,SAAS,CAAC,CAAC;EAClB,IAAIa,WAAW,GAAGrB,UAAU,CAAC;IAC3BX,SAAS,EAAEoB,EAAE,CAAC,QAAQ,CAAC;IACvBA,EAAE,EAAE,IAAI;IACRa,EAAE,EAAE,IAAI;IACRC,CAAC,EAAE,IAAI;IACPhC,IAAI,EAAEV,KAAK,CAACU,IAAI;IAChBD,WAAW,EAAET,KAAK,CAACS,WAAW;IAC9BkC,gBAAgB,EAAE;EACpB,CAAC,EAAEhB,GAAG,CAAC,QAAQ,CAAC,CAAC;EACjB,OAAO,aAAaxC,KAAK,CAACyD,aAAa,CAAC,KAAK,EAAET,SAAS,EAAE,aAAahD,KAAK,CAACyD,aAAa,CAAC,KAAK,EAAEN,YAAY,EAAE,aAAanD,KAAK,CAACyD,aAAa,CAAC,QAAQ,EAAEJ,WAAW,CAAC,CAAC,CAAC;AAC3K,CAAC,CAAC,CAAC;AACH1B,eAAe,CAAC+B,WAAW,GAAG,iBAAiB;AAE/C,SAAS/B,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}