{"ast": null, "code": "import { executeSchedule } from '../util/executeSchedule';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function observeOn(scheduler, delay) {\n  if (delay === void 0) {\n    delay = 0;\n  }\n  return operate(function (source, subscriber) {\n    source.subscribe(createOperatorSubscriber(subscriber, function (value) {\n      return executeSchedule(subscriber, scheduler, function () {\n        return subscriber.next(value);\n      }, delay);\n    }, function () {\n      return executeSchedule(subscriber, scheduler, function () {\n        return subscriber.complete();\n      }, delay);\n    }, function (err) {\n      return executeSchedule(subscriber, scheduler, function () {\n        return subscriber.error(err);\n      }, delay);\n    }));\n  });\n}", "map": {"version": 3, "names": ["executeSchedule", "operate", "createOperatorSubscriber", "observeOn", "scheduler", "delay", "source", "subscriber", "subscribe", "value", "next", "complete", "err", "error"], "sources": ["C:\\git\\Partner-Site\\BdoPartner.Plans\\Development\\ReactClientApp\\node_modules\\rxjs\\src\\internal\\operators\\observeOn.ts"], "sourcesContent": ["/** @prettier */\nimport { MonoTypeOperatorFunction, SchedulerLike } from '../types';\nimport { executeSchedule } from '../util/executeSchedule';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\n\n/**\n * Re-emits all notifications from source Observable with specified scheduler.\n *\n * <span class=\"informal\">Ensure a specific scheduler is used, from outside of an Observable.</span>\n *\n * `observeOn` is an operator that accepts a scheduler as a first parameter, which will be used to reschedule\n * notifications emitted by the source Observable. It might be useful, if you do not have control over\n * internal scheduler of a given Observable, but want to control when its values are emitted nevertheless.\n *\n * Returned Observable emits the same notifications (nexted values, complete and error events) as the source Observable,\n * but rescheduled with provided scheduler. Note that this doesn't mean that source Observables internal\n * scheduler will be replaced in any way. Original scheduler still will be used, but when the source Observable emits\n * notification, it will be immediately scheduled again - this time with scheduler passed to `observeOn`.\n * An anti-pattern would be calling `observeOn` on Observable that emits lots of values synchronously, to split\n * that emissions into asynchronous chunks. For this to happen, scheduler would have to be passed into the source\n * Observable directly (usually into the operator that creates it). `observeOn` simply delays notifications a\n * little bit more, to ensure that they are emitted at expected moments.\n *\n * As a matter of fact, `observeOn` accepts second parameter, which specifies in milliseconds with what delay notifications\n * will be emitted. The main difference between {@link delay} operator and `observeOn` is that `observeOn`\n * will delay all notifications - including error notifications - while `delay` will pass through error\n * from source Observable immediately when it is emitted. In general it is highly recommended to use `delay` operator\n * for any kind of delaying of values in the stream, while using `observeOn` to specify which scheduler should be used\n * for notification emissions in general.\n *\n * ## Example\n *\n * Ensure values in subscribe are called just before browser repaint\n *\n * ```ts\n * import { interval, observeOn, animationFrameScheduler } from 'rxjs';\n *\n * const someDiv = document.createElement('div');\n * someDiv.style.cssText = 'width: 200px;background: #09c';\n * document.body.appendChild(someDiv);\n * const intervals = interval(10);      // Intervals are scheduled\n *                                      // with async scheduler by default...\n * intervals.pipe(\n *   observeOn(animationFrameScheduler) // ...but we will observe on animationFrame\n * )                                    // scheduler to ensure smooth animation.\n * .subscribe(val => {\n *   someDiv.style.height = val + 'px';\n * });\n * ```\n *\n * @see {@link delay}\n *\n * @param scheduler Scheduler that will be used to reschedule notifications from source Observable.\n * @param delay Number of milliseconds that states with what delay every notification should be rescheduled.\n * @return A function that returns an Observable that emits the same\n * notifications as the source Observable, but with provided scheduler.\n */\nexport function observeOn<T>(scheduler: SchedulerLike, delay = 0): MonoTypeOperatorFunction<T> {\n  return operate((source, subscriber) => {\n    source.subscribe(\n      createOperatorSubscriber(\n        subscriber,\n        (value) => executeSchedule(subscriber, scheduler, () => subscriber.next(value), delay),\n        () => executeSchedule(subscriber, scheduler, () => subscriber.complete(), delay),\n        (err) => executeSchedule(subscriber, scheduler, () => subscriber.error(err), delay)\n      )\n    );\n  });\n}\n"], "mappings": "AAEA,SAASA,eAAe,QAAQ,yBAAyB;AACzD,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAsD/D,OAAM,SAAUC,SAASA,CAAIC,SAAwB,EAAEC,KAAS;EAAT,IAAAA,KAAA;IAAAA,KAAA,IAAS;EAAA;EAC9D,OAAOJ,OAAO,CAAC,UAACK,MAAM,EAAEC,UAAU;IAChCD,MAAM,CAACE,SAAS,CACdN,wBAAwB,CACtBK,UAAU,EACV,UAACE,KAAK;MAAK,OAAAT,eAAe,CAACO,UAAU,EAAEH,SAAS,EAAE;QAAM,OAAAG,UAAU,CAACG,IAAI,CAACD,KAAK,CAAC;MAAtB,CAAsB,EAAEJ,KAAK,CAAC;IAA3E,CAA2E,EACtF;MAAM,OAAAL,eAAe,CAACO,UAAU,EAAEH,SAAS,EAAE;QAAM,OAAAG,UAAU,CAACI,QAAQ,EAAE;MAArB,CAAqB,EAAEN,KAAK,CAAC;IAA1E,CAA0E,EAChF,UAACO,GAAG;MAAK,OAAAZ,eAAe,CAACO,UAAU,EAAEH,SAAS,EAAE;QAAM,OAAAG,UAAU,CAACM,KAAK,CAACD,GAAG,CAAC;MAArB,CAAqB,EAAEP,KAAK,CAAC;IAA1E,CAA0E,CACpF,CACF;EACH,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}