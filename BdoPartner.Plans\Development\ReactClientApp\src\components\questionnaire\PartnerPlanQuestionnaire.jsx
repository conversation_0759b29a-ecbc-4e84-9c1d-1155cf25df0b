import { useState, useEffect, useContext, useCallback, useRef } from "react";
import { Survey } from "survey-react-ui";
import { Model } from "survey-core";
import { configureSurveyJSLicense } from "../../core/surveyjs/licenseConfig";

import { useNavigate } from "react-router-dom";
import { AuthContext } from "../../core/auth/components/authProvider";
import { messageService } from "../../core/message/messageService";
import { useLoadingControl } from "../../core/loading/hooks/useLoadingControl";
import formService from "../../services/formService";
import partnerReferenceDataUploadService from "../../services/partnerReferenceDataUploadService";
import { FormStatus, getFormStatusName, getFormStatusClass, isFormEditableByOwner } from "../../core/enumertions/formStatus";
import { UserFormRole } from "../../core/enumertions/userFormRole";
import { PartnerPlanCycle } from "../../core/enumertions/partnerPlanCycle";
import http from "../../core/http/httpClient";
import APP_CONFIG from "../../core/config/appConfig";
import FormAnswerUtility from "./formAnswerUtilities";
import PDFExportUtilities from "./pdfExportUtilities";
import ReviewerCommentsDialog from "../common/ReviewerCommentsDialog";
import AdminModificationAuditHistory from "../audit/AdminModificationAuditHistory";
import { Panel } from "primereact/panel";
import { formatDateTime } from "../../core/utils/dateUtils";
import { registerCustomPropertiesForRuntime } from "../../core/utils/surveyCustomPropertiesUtils";

// Import the latest Survey.js CSS themes
import "survey-core/survey-core.css";
import "../../survey-v2-styles.css"; // Import our custom Survey.js v2 styles
import "./PartnerPlanQuestionnaire.css"; // Import component-specific styles

/**
 * Recursively traverses the survey JSON to build a map of element names to their tags.
 * @param {Array} elements - The array of elements (questions or panels) to process.
 * @param {Object} map - The map to populate.
 */
const buildNameTagMap = (elements, map) => {
  if (!elements || !Array.isArray(elements)) {
    return;
  }

  elements.forEach((element) => {
    if (element.name && element.tag) {
      map[element.name] = element.tag;
    }

    // Recurse into nested elements (e.g., in panels)
    if (element.elements) {
      buildNameTagMap(element.elements, map);
    }
  });
};

/**
 * Recursively traverses the survey JSON to build a map of element names to their leadership roles.
 * @param {Array} elements - The array of elements (questions or panels) to process.
 * @param {Object} map - The map to populate.
 */
const buildNameLeadershipRolesMap = (elements, map) => {
  if (!elements || !Array.isArray(elements)) {
    return;
  }

  elements.forEach((element) => {
    if (element.name && element.leadershipRoles) {
      map[element.name] = element.leadershipRoles;
    }

    // Recurse into nested elements (e.g., in panels)
    if (element.elements) {
      buildNameLeadershipRolesMap(element.elements, map);
    }
  });
};

/**
 * Recursively traverses the survey JSON to build a map of element names to their service lines.
 * @param {Array} elements - The array of elements (questions or panels) to process.
 * @param {Object} map - The map to populate.
 */
const buildNameServiceLinesMap = (elements, map) => {
  if (!elements || !Array.isArray(elements)) {
    return;
  }

  elements.forEach((element) => {
    if (element.name && element.serviceLines) {
      map[element.name] = element.serviceLines;
    }

    // Recurse into nested elements (e.g., in panels)
    if (element.elements) {
      buildNameServiceLinesMap(element.elements, map);
    }
  });
};

/**
 * Recursively traverses the survey JSON to build a map of element names to their sub-service lines.
 * @param {Array} elements - The array of elements (questions or panels) to process.
 * @param {Object} map - The map to populate.
 */
const buildNameSubServiceLinesMap = (elements, map) => {
  if (!elements || !Array.isArray(elements)) {
    return;
  }

  elements.forEach((element) => {
    if (element.name && element.subServiceLines) {
      map[element.name] = element.subServiceLines;
    }

    // Recurse into nested elements (e.g., in panels)
    if (element.elements) {
      buildNameSubServiceLinesMap(element.elements, map);
    }
  });
};

/**
 * Normalize leadership role for consistent comparison
 * @param {string} role - Original leadership role
 * @returns {string} Normalized leadership role
 */
const normalizeLeadershipRole = (role) => {
  if (!role || typeof role !== "string") {
    return "";
  }
  // Apply normalization: trim whitespace, convert to uppercase for consistent comparison
  return role.trim().toUpperCase();
};
/**
 * Applies tag configuration from planData to survey elements for visibility and readonly rules
 * @param {Object} survey - The SurveyJS model instance
 * @param {Object} config - The form access configuration object from planData
 * @param {Object} nameTagMap - Map of element names to their tag values
 * @returns {Object|null} The form access configuration object or null if not found
 */
const applyTagConfiguration = (survey, config, nameTagMap) => {
  try {
    // Validate inputs
    if (!survey) {
      console.warn("🏷️ Survey model is null, skipping tag configuration");
      return null;
    }

    if (!config) {
      console.log("🏷️ No Form Access configuration found for this form. Using default behavior.");
      return null;
    }

    console.log("📋 Form Access configuration received:", config);

    // Validate config structure
    if (typeof config !== "object") {
      console.warn("🏷️ Invalid tag configuration format received. Using default behavior.");
      return null;
    }

    let appliedRulesCount = 0;

    const applyRulesToElements = (elements, elementType) => {
      console.log(`🏷️ Processing ${elements.length} ${elementType} for tag rules`);
      elements.forEach((element) => {
        const tagName = nameTagMap[element.name] || element.tag;
        console.log(`🔍 ${elementType} ${element.name}: tag="${tagName}"`);
        if (tagName) {
          const configKey = Object.keys(config).find((k) => k.toLowerCase() === tagName.toLowerCase());
          if (configKey && config[configKey]) {
            const tagConfig = config[configKey];

            if (typeof tagConfig.readOnly === "boolean") {
              element.readOnly = tagConfig.readOnly;
              console.log(`🔒 Applied readOnly=${tagConfig.readOnly} to ${elementType} ${element.name} with tag ${tagName}`);
              appliedRulesCount++;
            }

            if (typeof tagConfig.visible === "boolean") {
              element.visible = tagConfig.visible;
              console.log(`👁️ Applied visible=${tagConfig.visible} to ${elementType} ${element.name} with tag ${tagName}`);
              appliedRulesCount++;
            }
          }
        }
      });
    };

    applyRulesToElements(survey.getAllQuestions(), "question");
    applyRulesToElements(survey.getAllPanels(), "panel");

    console.log(`✅ Tag configuration applied successfully. ${appliedRulesCount} rules applied.`);

    // Return the configuration so it can be used by the caller
    return config;
  } catch (error) {
    console.error("❌ Error fetching or applying tag configuration:", error);
    console.log("📝 Form will load with default behavior (all visible, all editable)");
    // Don't throw the error - let the form load with default behavior
    return null;
  }
};

/**
 * Applies leadership roles configuration to survey panels for visibility rules
 * @param {Object} survey - The SurveyJS model instance
 * @param {Array} userLeadershipRoles - Current user's leadership roles
 * @param {Object} nameLeadershipRolesMap - Map of element names to their leadership roles
 * @returns {number} Number of panels processed
 */
const applyLeadershipRolesConfiguration = (survey, userLeadershipRoles, nameLeadershipRolesMap) => {
  try {
    // Validate inputs
    if (!survey) {
      console.warn("👤 Survey model is null, skipping leadership roles configuration");
      return 0;
    }

    if (!userLeadershipRoles) {
      console.log("👤 No user leadership roles provided, treating as empty array");
      userLeadershipRoles = [];
    }

    console.log("👤 Applying leadership roles configuration...");
    console.log("👤 User leadership roles:", userLeadershipRoles);
    console.log("👤 Name-to-leadership-roles map:", nameLeadershipRolesMap);

    let processedPanelsCount = 0;

    // Only apply to panels (not questions)
    const panels = survey.getAllPanels();
    console.log(`👤 Processing ${panels.length} panels for leadership roles rules`);

    panels.forEach((panel) => {
      // Skip leadership roles check if panel is already hidden by tag configuration
      if (!panel.visible) {
        console.log(`⏭️ Skipping panel ${panel.name} - already hidden by tag configuration`);
        processedPanelsCount++;
        return;
      }

      // Get leadership roles from the map or directly from the panel
      const panelLeadershipRoles = nameLeadershipRolesMap[panel.name] || panel.leadershipRoles;

      console.log(`🔍 Panel ${panel.name}: leadershipRoles="${panelLeadershipRoles}"`);

      if (panelLeadershipRoles) {
        // Parse leadership roles (could be array or comma-separated string)
        let requiredRoles = [];
        if (Array.isArray(panelLeadershipRoles)) {
          requiredRoles = panelLeadershipRoles;
        } else if (typeof panelLeadershipRoles === "string") {
          requiredRoles = panelLeadershipRoles
            .split(",")
            .map((role) => role.trim())
            .filter((role) => role);
        }

        console.log(`👤 Panel ${panel.name} requires roles:`, requiredRoles);

        if (requiredRoles.length > 0) {
          // Normalize required roles for consistent comparison
          const normalizedRequiredRoles = requiredRoles.map((role) => normalizeLeadershipRole(role));

          // Check if user has any of the required leadership roles (both are normalized)
          const hasRequiredRole = normalizedRequiredRoles.some((requiredRole) => userLeadershipRoles.some((userRole) => userRole === requiredRole));

          if (!hasRequiredRole) {
            // User doesn't have required leadership role, hide the panel
            panel.visible = false;
            console.log(`👁️ Hidden panel ${panel.name} - user lacks required leadership roles`);
            console.log(`   Required (normalized): [${normalizedRequiredRoles.join(", ")}]`);
            console.log(`   User has (normalized): [${userLeadershipRoles.join(", ")}]`);
          } else {
            console.log(`✅ Panel ${panel.name} visible - user has required leadership role`);
          }
        } else {
          console.log(`📝 Panel ${panel.name} has empty leadership roles - visible to all`);
        }
      } else {
        console.log(`📝 Panel ${panel.name} has no leadership roles restriction - visible to all`);
      }

      processedPanelsCount++;
    });

    console.log(`✅ Leadership roles configuration applied successfully. ${processedPanelsCount} panels processed.`);
    return processedPanelsCount;
  } catch (error) {
    console.error("❌ Error applying leadership roles configuration:", error);
    console.log("📝 Form will load with default behavior (all panels visible)");
    // Don't throw the error - let the form load with default behavior
    return 0;
  }
};

/**
 * Applies service lines configuration to survey panels for visibility rules
 * @param {Object} survey - The SurveyJS model instance
 * @param {Array} formOwnerServiceLines - Form owner's service lines
 * @param {Array} formOwnerSubServiceLines - Form owner's sub-service lines
 * @param {Object} nameServiceLinesMap - Map of element names to their service lines
 * @param {Object} nameSubServiceLinesMap - Map of element names to their sub-service lines
 * @returns {number} Number of panels processed
 */
const applyServiceLinesConfiguration = (survey, formOwnerServiceLines, formOwnerSubServiceLines, nameServiceLinesMap, nameSubServiceLinesMap) => {
  try {
    // Validate inputs
    if (!survey) {
      console.warn("🏢 Survey model is null, skipping service lines configuration");
      return 0;
    }

    if (!formOwnerServiceLines) {
      console.log("🏢 No form owner service lines provided, treating as empty array");
      formOwnerServiceLines = [];
    }

    if (!formOwnerSubServiceLines) {
      console.log("🏢 No form owner sub-service lines provided, treating as empty array");
      formOwnerSubServiceLines = [];
    }

    console.log("🏢 Applying service lines configuration...");
    console.log("🏢 Form owner service lines:", formOwnerServiceLines);
    console.log("🏢 Form owner sub-service lines:", formOwnerSubServiceLines);
    console.log("🏢 Name-to-service-lines map:", nameServiceLinesMap);
    console.log("🏢 Name-to-sub-service-lines map:", nameSubServiceLinesMap);

    let processedPanelsCount = 0;

    // Only apply to panels (not questions)
    const panels = survey.getAllPanels();
    console.log(`🏢 Found ${panels.length} panels to process`);

    panels.forEach((panel) => {
      // Skip if panel is already hidden (e.g., by tag configuration)
      if (!panel.visible) {
        console.log(`🏢 Panel ${panel.name} already hidden, skipping service lines check`);
        processedPanelsCount++;
        return;
      }

      // Get service lines and sub-service lines for this panel
      const panelServiceLines = nameServiceLinesMap[panel.name] || panel.serviceLines;
      const panelSubServiceLines = nameSubServiceLinesMap[panel.name] || panel.subServiceLines;

      console.log(`🏢 Panel ${panel.name}:`);
      console.log(`   Service lines: ${panelServiceLines}`);
      console.log(`   Sub-service lines: ${panelSubServiceLines}`);

      // Check service lines first
      let hiddenByServiceLines = false;
      if (panelServiceLines) {
        // Handle both array and string formats for panelServiceLines
        let requiredServiceLines = [];
        if (Array.isArray(panelServiceLines)) {
          requiredServiceLines = panelServiceLines.filter((sl) => sl && sl.trim() !== "");
        } else if (typeof panelServiceLines === "string" && panelServiceLines.trim() !== "") {
          requiredServiceLines = panelServiceLines
            .split(",")
            .map((sl) => sl.trim())
            .filter((sl) => sl !== "");
        }

        if (requiredServiceLines.length > 0) {
          // Check if form owner has any of the required service lines
          const hasRequiredServiceLine = requiredServiceLines.some((requiredSL) =>
            formOwnerServiceLines.some((ownerSL) => ownerSL.trim() === requiredSL)
          );

          if (!hasRequiredServiceLine) {
            // Form owner doesn't have required service line, hide the panel
            panel.visible = false;
            hiddenByServiceLines = true;
            console.log(`👁️ Hidden panel ${panel.name} - form owner lacks required service lines`);
            console.log(`   Required: [${requiredServiceLines.join(", ")}]`);
            console.log(`   Form owner has: [${formOwnerServiceLines.join(", ")}]`);
          } else {
            console.log(`✅ Panel ${panel.name} visible - form owner has required service line`);
          }
        }
      }

      // Check sub-service lines only if not already hidden by service lines
      if (!hiddenByServiceLines && panelSubServiceLines) {
        // Handle both array and string formats for panelSubServiceLines
        let requiredSubServiceLines = [];
        if (Array.isArray(panelSubServiceLines)) {
          requiredSubServiceLines = panelSubServiceLines.filter((ssl) => ssl && ssl.trim() !== "");
        } else if (typeof panelSubServiceLines === "string" && panelSubServiceLines.trim() !== "") {
          requiredSubServiceLines = panelSubServiceLines
            .split(",")
            .map((ssl) => ssl.trim())
            .filter((ssl) => ssl !== "");
        }

        if (requiredSubServiceLines.length > 0) {
          // Check if form owner has any of the required sub-service lines
          const hasRequiredSubServiceLine = requiredSubServiceLines.some((requiredSSL) =>
            formOwnerSubServiceLines.some((ownerSSL) => ownerSSL.trim() === requiredSSL)
          );

          if (!hasRequiredSubServiceLine) {
            // Form owner doesn't have required sub-service line, hide the panel
            panel.visible = false;
            console.log(`👁️ Hidden panel ${panel.name} - form owner lacks required sub-service lines`);
            console.log(`   Required: [${requiredSubServiceLines.join(", ")}]`);
            console.log(`   Form owner has: [${formOwnerSubServiceLines.join(", ")}]`);
          } else {
            console.log(`✅ Panel ${panel.name} visible - form owner has required sub-service line`);
          }
        }
      }

      // If both service lines and sub-service lines are empty, panel is visible to all
      const hasServiceLinesRestriction =
        panelServiceLines &&
        ((Array.isArray(panelServiceLines) && panelServiceLines.length > 0) ||
          (typeof panelServiceLines === "string" && panelServiceLines.trim() !== ""));
      const hasSubServiceLinesRestriction =
        panelSubServiceLines &&
        ((Array.isArray(panelSubServiceLines) && panelSubServiceLines.length > 0) ||
          (typeof panelSubServiceLines === "string" && panelSubServiceLines.trim() !== ""));

      if (!hasServiceLinesRestriction && !hasSubServiceLinesRestriction) {
        console.log(`📝 Panel ${panel.name} has no service lines restrictions - visible to all`);
      }

      processedPanelsCount++;
    });

    console.log(`✅ Service lines configuration applied successfully. ${processedPanelsCount} panels processed.`);
    return processedPanelsCount;
  } catch (error) {
    console.error("❌ Error applying service lines configuration:", error);
    console.log("📝 Form will load with default behavior (all panels visible)");
    // Don't throw the error - let the form load with default behavior
    return 0;
  }
};

/**
 *
 * @param {*} year Work for partner click "View My Plan" from landing page.
 * @param {*} formId Work for Admin/ELT user click "View" icon button in "Partner Annual Plans" grid.
 * @param {*} backHandler handle Go back to previous page
 * @returns
 */
export const PartnerPlanQuestionnaire = ({ year, formId, backHandler }) => {
  const [surveyModel, setSurveyModel] = useState(null);
  const [questionnaire, setQuestionnaire] = useState(null);
  const [form, setForm] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [hasLoaded, setHasLoaded] = useState(false); // Track if data has been loaded
  const [currentPageInfo, setCurrentPageInfo] = useState({ pageNo: 0, pageCount: 0, pageTitle: "" });
  // Keep a live ref of form to avoid stale closures in SurveyJS handlers
  const formRef = useRef(null);
  useEffect(() => {
    formRef.current = form;
  }, [form]);

  // State for reviewer comments dialog
  const [showCommentsDialog, setShowCommentsDialog] = useState(false);
  const [sendingBack, setSendingBack] = useState(false);

  // State for current user's form role (from form access configuration)
  const [currentUserFormRole, setCurrentUserFormRole] = useState(null);
  const [formAccessConfig, setFormAccessConfig] = useState(null);

  // State for reviewer comments history
  const [reviewerComments, setReviewerComments] = useState([]);

  // State for submission allowed flag
  const [isSubmissionAllowed, setIsSubmissionAllowed] = useState(true);

  // State for admin editing flag
  const [isAdminEditing, setIsAdminEditing] = useState(false);

  const authService = useContext(AuthContext);
  const navigate = useNavigate();

  // Disable loading interceptor for survey component
  useLoadingControl("survey", true);

  // Determine scenario: MyPartnerPlan (year provided) vs PartnerPlan (formId provided)
  const isMyPartnerPlan = !!year && !formId;
  const isPartnerPlan = !!formId && !year;

  // Reset hasLoaded when year or formId changes to trigger data reload
  useEffect(() => {
    setHasLoaded(false);
  }, [year, formId]);

  // Use useEffect directly instead of useCallback to avoid dependency issues
  useEffect(() => {
    // Prevent multiple calls if already loaded or not authenticated
    if (hasLoaded || !authService || !authService.isAuthenticated()) {
      return;
    }

    const loadData = async () => {
      try {
        setLoading(true);
        setError(null);

        let planData = null;

        // Determine which API endpoint to call based on scenario
        if (formId) {
          // Scenario 2: Admin/ELT/Reviewer accessing other partner's plan
          console.log("🔄 Loading partner plan data for formId:", formId);
          planData = await formService.getPartnerPlan(formId);
        } else {
          // Scenario 1: MyPartnerPlan - form owner accessing their own plan
          const targetYear = year ? parseInt(year) : new Date().getFullYear();
          console.log("🔄 Loading my plan data for year:", targetYear);
          planData = await formService.getMyPlan(targetYear);
        }

        if (planData) {
          // Set all the data from the consolidated response
          setQuestionnaire(planData.questionnaire);

          // Set current user's form role from planData
          if (planData.currentUserFormRole !== undefined) {
            setCurrentUserFormRole(planData.currentUserFormRole);
            console.log("👤 Current user form role:", planData.currentUserFormRole);
          }

          // Set submission allowed flag from planData
          if (planData.isSubmissionAllowed !== undefined) {
            setIsSubmissionAllowed(planData.isSubmissionAllowed);
            console.log("🔒 Submission allowed:", planData.isSubmissionAllowed);
          }

          // Set admin editing flag from planData
          if (planData.isAdminEditing !== undefined) {
            setIsAdminEditing(planData.isAdminEditing);
            console.log("👨‍💼 Admin editing:", planData.isAdminEditing);
          }

          // Ensure form includes userAnswer to prevent null reference errors
          const formWithUserAnswer = {
            ...planData.form,
            userAnswer: planData.userAnswer,
          };
          setForm(formWithUserAnswer);

          // Fetch reviewer comments history for this form (only for MyPartnerPlan scenario)
          if (formWithUserAnswer.id && !formId) {
            try {
              const comments = await formService.getReviewerCommentsHistory(formWithUserAnswer.id);
              setReviewerComments(comments || []);
            } catch (error) {
              console.error(`Error fetching reviewer comments for form ${formWithUserAnswer.id}:`, error);
              setReviewerComments([]);
            }
          }

          // Get existing answers if available
          const existingAnswers = planData.userAnswer?.answer;

          // Parse the DefinitionJson to create the survey model
          if (planData.questionnaire.definitionJson) {
            try {
              const surveyJson = JSON.parse(planData.questionnaire.definitionJson);

              // Validate that the parsed JSON is a valid object
              if (!surveyJson || typeof surveyJson !== "object") {
                throw new Error("Invalid survey JSON: not a valid object");
              }

              // Ensure the survey JSON has required properties
              if (!surveyJson.pages && !surveyJson.elements) {
                throw new Error("Invalid survey JSON: missing pages or elements");
              }

              // Debug: Check if tag properties exist in the original JSON
              console.log("🔍 Checking original survey JSON for tag properties...");
              if (surveyJson.pages) {
                surveyJson.pages.forEach((page, pageIndex) => {
                  if (page.elements) {
                    page.elements.forEach((element, elementIndex) => {
                      if (element.tag) {
                        console.log(`🏷️ Found tag in JSON - ${element.type} "${element.name}": tag="${element.tag}"`);
                      }
                      // Check nested elements (panels can contain questions)
                      if (element.elements) {
                        element.elements.forEach((nestedElement, nestedIndex) => {
                          if (nestedElement.tag) {
                            console.log(`🏷️ Found tag in nested JSON - ${nestedElement.type} "${nestedElement.name}": tag="${nestedElement.tag}"`);
                          }
                        });
                      }
                    });
                  }
                });
              }

              // Configure Survey.js commercial license before creating survey model
              configureSurveyJSLicense();

              // Register custom properties BEFORE creating the Survey model to avoid race conditions
              await registerCustomPropertiesForRuntime({ isDesigner: false });

              const survey = new Model(surveyJson);
              console.log("Survey model created:", surveyJson);

              // Validate that the survey was created successfully
              if (!survey) {
                throw new Error("Failed to create survey model");
              }

              // Debug: Check if custom properties are available
              const questions = survey.getAllQuestions();
              const panels = survey.getAllPanels();

              if (questions.length > 0) {
                const firstQuestion = questions[0];
              }

              if (panels.length > 0) {
                const firstPanel = panels[0];
              }

              // Validate that the survey has pages
              if (!survey.pages || survey.pages.length === 0) {
                throw new Error("Survey has no pages");
              }

              // Load existing answers if available
              if (existingAnswers) {
                try {
                  let parsedAnswers = JSON.parse(existingAnswers);
                  // Handle legacy double-encoded payloads (JSON string inside JSON string)
                  if (typeof parsedAnswers === "string") {
                    try {
                      parsedAnswers = JSON.parse(parsedAnswers);
                    } catch {}
                  }
                  if (parsedAnswers && typeof parsedAnswers === "object") {
                    survey.data = parsedAnswers;
                  }
                } catch (parseError) {
                  console.warn("Failed to parse existing answers:", parseError);
                }
              }

              // Configure survey settings for wizard mode
              survey.clearInvisibleValues = false;
              // survey.allowShowPreview = true;
              survey.showPreviewBeforeComplete = "showAllQuestions";
              survey.showProgressBar = true;
              survey.progressBarLocation = "top";
              // survey.showQuestionNumbers = "onPage";
              survey.questionsOnPageMode = "standard"; // Enable multi-page wizard mode
              survey.showPageNumbers = false;
              survey.showPageTitles = true;
              survey.pagePrevText = "Previous";
              survey.pageNextText = "Next";
              survey.goNextPageAutomatic = false; // Don't auto-advance pages

              //
              // Check if form is editable. Updated to check FormAccessConfig matrix,
              // if any of three panels are visible and not readonly, the form is editable for current user.
              //
              const formEditable = planData.isEditable;

              if (!formEditable) {
                // Set survey to read-only mode for non-editable forms (based on workflow rules and user roles)
                survey.mode = "display";
                survey.showPreviewBeforeComplete = "noPreview";
              } else {
                // Customize button text for editable forms
                survey.completeText = "Submit Partner Plan";
                survey.previewText = "Preview";
                survey.editText = "Previous"; // Change Edit button text to Previous for clarity
                // Ensure navigation buttons are shown in preview mode
                survey.showPrevButton = true;
                // Ensure preview mode shows navigation buttons
                survey.showPreviewBeforeComplete = "showAllQuestions";
                // survey.allowShowPreview = true;

                // Hide complete button if submission is not allowed
                if (!planData.isSubmissionAllowed) {
                  survey.showCompleteButton = false;
                  console.log("🚫 Complete button hidden - submission not allowed");
                }
              }
              // Add custom CSS classes
              survey.css = {
                ...survey.css,
                root: `sv-root partner-plan-survey ${!formEditable ? "survey-readonly" : ""}`,
                header: "sv-header",
                body: "sv-body",
                footer: "sv-footer",
              };

              // Apply modern theme for better styling
              // Apply BDO red theme using modern Survey.js v2.2.2 approach
              survey.applyTheme({
                themeName: "default-light",
                colorPalette: "light",
                isPanelless: false,
                // Custom CSS variables for BDO branding (equivalent to old StylesManager)
                cssVariables: {
                  "--sjs-primary-backcolor": "#ED1A3B", // Main color
                  "--sjs-primary-forecolor": "#FFFFFF", // Text on primary
                  "--sjs-primary-backcolor-light": "#F5E6EA", // Light variant
                  "--sjs-primary-backcolor-dark": "#AF273C", // Hover color
                  "--sjs-secondary-backcolor": "#ED1A3B", // Secondary color
                  "--sjs-secondary-forecolor": "#FFFFFF", // Text on secondary
                  "--sjs-general-backcolor-dim": "#F3F2F1", // Background
                  "--sjs-border-default": "#959597", // Border color
                  "--sjs-border-light": "#D4D4D4", // Light border
                  "--ctr-font-family": "primeicons",
                  "--lbr-font-family": "primeicons",
                  "--sjs-font-family": "primeicons",
                  "--font-family": "primeicons",
                  "--sjs-font-pagetitle-family": "primeicons",
                  "--sjs-default-font-family": "primeicons",

                  "--sjs-article-font-default": "24",

                  "--sd-base-padding": "20px",
                  "--sd-base-vertical-padding": "calc(1.4 * var(--sjs-base-unit, var(--base-unit, 8px)))",
                  "--sd-page-vertical-padding": "calc(1.2 * var(--sjs-base-unit, var(--base-unit, 8px)))",
                },
              });
              survey.onTextMarkdown.add(function (survey, options) {
                //convert the markdown text to html
                var str = FormAnswerUtility.convertToHtml(options.text);
                //set html
                options.html = str;
              });
              // Only add completion handler for editable forms
              if (formEditable) {
                // Create a completion handler for form submission
                const completionHandler = async (sender, options) => {
                  // Prevent default completion
                  options.allowComplete = false;

                  // Show confirmation dialog
                  messageService.confirmDialog(
                    "Are you sure you want to submit your partner plan? Once submitted, you may not be able to make changes.",
                    async (confirmed) => {
                      if (!confirmed) {
                        return;
                      }

                      try {
                        // Save the final answers
                        await formService.saveUserAnswer(planData.form.id, JSON.stringify(sender.data));

                        // Submit the form using the new workflow-aware API
                        const updatedForm = await formService.submitForm(planData.form.id);

                        console.log("Survey completed and submitted successfully");

                        // Show success message with workflow information
                        if (updatedForm && updatedForm.message) {
                          messageService.successToast(updatedForm.message);
                        } else {
                          messageService.successToast("Partner plan submitted successfully!");
                        }

                        // Update form status with the actual returned status from the workflow
                        if (updatedForm) {
                          setForm((prev) => ({
                            ...prev,
                            status: updatedForm.status,
                            statusString: getFormStatusName(updatedForm.status),
                          }));
                        }

                        // Complete the survey
                        sender.doComplete();
                      } catch (error) {
                        console.error("Error handling survey completion:", error);
                        messageService.errorToast(error.message || "Failed to submit partner plan. Please try again.");
                      }
                    }
                  );

                // Set up survey completion handler
                survey.onComplete.add(completionHandler);
              }

              // Only add auto-save for editable forms
              if (formEditable) {
                // Set up immediate save on value change (following Retirement project pattern)
                survey.onValueChanged.add(surveyValueChanged);
              }

              // Set up page change handler for wizard navigation
              survey.onCurrentPageChanged.add((sender, options) => {
                handlePageChanged(sender, options);

                // Check if we're on the last page and submission is not allowed
                if (!planData.isSubmissionAllowed && planData.currentUserFormRole === UserFormRole.FormOwner) {
                  const isLastPage = sender.isLastPage;
                  if (isLastPage) {
                    // Add a message to the last page about reviewer not being assigned
                    const lastPage = sender.currentPage;
                    if (lastPage && !lastPage.hasCustomMessage) {
                      // Create a custom HTML element to show the message
                      const submissionMessage = planData.submissionNotAllowedMessage;

                      // Only render the message if submissionMessage is not null or empty
                      if (submissionMessage && submissionMessage.trim() !== "") {
                        const messageHtml = `
                          <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px; padding: 15px; margin: 20px 0; color: #856404;">
                            <div style="display: flex; flex-direction: column; align-items: center; text-align: center;">
                              <div style="display: flex; align-items: center; margin-bottom: 10px;">
                                <i class="pi pi-exclamation-triangle" style="font-size: 1.2rem; margin-right: 10px; color: #f39c12;"></i>
                                <strong>Unable to Submit Partner Plan</strong>
                              </div>
                              <p style="margin: 0;">${submissionMessage}</p>
                            </div>
                          </div>
                        `;

                        // Add the message as HTML content to the page
                        if (!lastPage.description) {
                          lastPage.description = messageHtml;
                        } else {
                          lastPage.description = lastPage.description + messageHtml;
                        }
                        lastPage.hasCustomMessage = true;
                      }
                    }
                  }
                }
              });

              // Add page validation handler
              survey.onCurrentPageChanging.add((_, options) => {
                // You can add custom validation logic here if needed
                console.log(`Navigating from page ${options.oldCurrentPage?.name} to ${options.newCurrentPage?.name}`);
              });

              // Add preview mode handler to ensure navigation buttons are shown
              // Note: onShowPreview might not be available in all SurveyJS versions
              if (survey.onShowPreview) {
                survey.onShowPreview.add((sender) => {
                  console.log("Survey entered preview mode");
                  // Ensure navigation buttons are visible in preview mode
                  sender.showNavigationButtons = "both";
                  sender.showPrevButton = true;
                });
              }

              // Set completion message
              survey.completedHtml = `<h3>Thank you for completing ${survey.title || planData.questionnaire.name}!</h3>`;

              // Build the name-to-tag map from the raw survey JSON
              const nameTagMap = {};
              if (surveyJson.pages) {
                surveyJson.pages.forEach((page) => {
                  buildNameTagMap(page.elements, nameTagMap);
                });
              }
              console.log("🏷️ Name-to-tag map created:", nameTagMap);

              // Apply tag-based visibility and readonly rules using configuration from planData
              const formAccessConfig = applyTagConfiguration(survey, planData.formTagConfiguration, nameTagMap);

              // Set form access config for other uses
              if (formAccessConfig) {
                setFormAccessConfig(formAccessConfig);
              }

              // Build the name-to-leadership-roles map from the raw survey JSON
              const nameLeadershipRolesMap = {};
              if (surveyJson.pages) {
                surveyJson.pages.forEach((page) => {
                  buildNameLeadershipRolesMap(page.elements, nameLeadershipRolesMap);
                });
              }
              console.log("👤 Name-to-leadership-roles map created:", nameLeadershipRolesMap);

              // Apply leadership roles-based visibility rules using user's leadership roles from planData
              // Only apply if tag configuration hasn't already hidden the panels
              applyLeadershipRolesConfiguration(survey, planData.leadershipRoles, nameLeadershipRolesMap);

              // Build the name-to-service-lines maps from the raw survey JSON
              const nameServiceLinesMap = {};
              const nameSubServiceLinesMap = {};
              if (surveyJson.pages) {
                surveyJson.pages.forEach((page) => {
                  buildNameServiceLinesMap(page.elements, nameServiceLinesMap);
                  buildNameSubServiceLinesMap(page.elements, nameSubServiceLinesMap);
                });
              }
              console.log("🏢 Name-to-service-lines map created:", nameServiceLinesMap);
              console.log("🏢 Name-to-sub-service-lines map created:", nameSubServiceLinesMap);

              // Apply service lines-based visibility rules using form owner's service lines from planData
              // Only apply if tag configuration and leadership roles haven't already hidden the panels
              applyServiceLinesConfiguration(
                survey,
                planData.serviceLines || [],
                planData.subServiceLines || [],
                nameServiceLinesMap,
                nameSubServiceLinesMap
              );

              // Set up partner data auto-population for dropdowns with mapFromGroup
              await setupPartnerReferenceDataAutoPopulation(survey, planData.partnerId, planData.questionnaire.year);

              setSurveyModel(survey);

              // Initialize page info for wizard
              if (survey.pageCount > 1) {
                const visiblePageIndex = survey.visiblePages.indexOf(survey.currentPage);
                const processedTitle = survey.getProcessedText(survey.currentPage?.title || survey.currentPage?.name);

                setCurrentPageInfo({
                  pageNo: visiblePageIndex > -1 ? visiblePageIndex : 0,
                  pageCount: survey.visiblePages.length,
                  pageTitle: processedTitle,
                });
              }

              setHasLoaded(true); // Mark as loaded
            } catch (parseError) {
              console.error("Error parsing survey JSON:", parseError);
              setError("Failed to load survey form. Invalid survey definition.");
            }
          } else {
            setError("No survey definition found for the current year.");
          }
        } else {
          setError(planData?.message || "Failed to load partner plan data.");
        }
      } catch (err) {
        console.error("Error loading my plan:", err);
        setError(err.message || "Failed to load partner plan. Please try again later.");
      } finally {
        setLoading(false);
      }
    };

    loadData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [hasLoaded, year, formId]); // Depend on hasLoaded, year, and formId to reload when parameters change

  // Handle return to home navigation
  const handleReturnToHome = useCallback(() => {
    navigate("/home");
  }, [navigate]);

  // Handle Send Back to Partner button click
  const handleSendBackToPartner = () => {
    setShowCommentsDialog(true);
  };

  // Handle reviewer comments dialog confirm
  const handleCommentsConfirm = async (comments) => {
    if (!form?.id) return;

    setSendingBack(true);
    try {
      await formService.sendBackToPartner(form.id, comments);
      messageService.successDialog("Form sent back to partner successfully");

      // Close dialog
      setShowCommentsDialog(false);

      // Navigate back or refresh
      if (backHandler) {
        backHandler();
      } else {
        navigate("/home");
      }
    } catch (error) {
      messageService.errorDialog(error.message || "Failed to send form back to partner");
    } finally {
      setSendingBack(false);
    }
  };

  // Handle Submit Admin Update button click
  const handleSubmitAdminUpdate = async () => {
    if (!form?.id) return;

    // Show confirmation dialog
    const confirmed = window.confirm("Are you sure you want to submit this admin update? This action will update the form status.");

    if (!confirmed) {
      return;
    }

    try {
      // Save the current answers first
      if (surveyModel?.data) {
        await formService.saveUserAnswer(form.id, JSON.stringify(surveyModel.data));
      }

      // Submit the form using the same endpoint as the regular submit
      const updatedForm = await formService.submitForm(form.id);

      console.log("Admin update submitted successfully");

      // Show success message
      if (updatedForm && updatedForm.message) {
        messageService.successToast(updatedForm.message);
      } else {
        messageService.successToast("Admin update submitted successfully!");
      }

      // Update form status with the actual returned status
      if (updatedForm) {
        setForm((prev) => ({
          ...prev,
          status: updatedForm.status,
          statusString: getFormStatusName(updatedForm.status),
        }));
      }

      // Navigate back or refresh
      if (backHandler) {
        backHandler();
      } else {
        navigate("/home");
      }
    } catch (error) {
      console.error("Error submitting admin update:", error);
      messageService.errorToast(error.message || "Failed to submit admin update. Please try again.");
    }
  };

  // Handle reviewer comments dialog hide
  const handleCommentsDialogHide = () => {
    if (!sendingBack) {
      setShowCommentsDialog(false);
    }
  };

  // Check if current user can send form back to partner
  const canSendBackToPartner = () => {
    if (!form) return false;

    // Check if current user is a reviewer based on the form access configuration
    if (currentUserFormRole !== UserFormRole.Reviewer) {
      return false;
    }

    // Check if form is in "Under Review" status for MidYear or YearEnd cycles
    return form.status === FormStatus.MidYearReviewUnderReview || form.status === FormStatus.YearEndReviewUnderReview;
  };

  //
  // Handle PDF export with Survey.js build function.
  //
  const handleExportToPDF = useCallback(async () => {
    await PDFExportUtilities.handleExportToPDF(surveyModel, form);
  }, [surveyModel, form]);

  // Alternative PDF export method using browser's print functionality
  const handleAlternativePDFExport = useCallback(() => {
    PDFExportUtilities.handleAlternativePDFExport(surveyModel, form);
  }, [surveyModel, form]);

  const handlePageChanged = (sender, options) => {
    // Update page information for better UX
    if (sender && sender.currentPage) {
      const visiblePageIndex = sender.visiblePages.indexOf(sender.currentPage);

      const processedTitle = sender.currentPage.getProcessedText(sender.currentPage?.title || sender.currentPage?.name);

      const pageInfo = {
        pageNo: visiblePageIndex > -1 ? visiblePageIndex : 0,
        pageCount: sender.visiblePages.length,
        pageTitle: processedTitle,
      };
      setCurrentPageInfo(pageInfo);
    }
  };

  // Save questionnaire data without submitting (use backend AutoSave API)
  const saveQuestionnaire = async (callback, dataOverride = null) => {
    try {
      const currentForm = formRef.current;
      if (!currentForm) {
        // Form not ready yet; skip this autosave quietly
        callback && callback();
        return;
      }

      const answers = dataOverride ?? surveyModel?.data ?? {};

      // Call auto-save endpoint which will create/update UserAnswer and set form status server-side
      await formService.autoSaveUserAnswer(currentForm.id, answers);
    } catch (e) {
      console.warn("Auto-save failed:", e);
    } finally {
      callback && callback();
    }
  };

  // Survey value changed handler - immediate save (following Retirement project pattern)
  function surveyValueChanged(sender, options) {
    console.log("Survey value changed:", options);

    // Save immediately using the values from the sender (always up-to-date)
    saveQuestionnaire(undefined, sender?.data);
  }

  // Admin reopen function following Retirement project pattern
  const adminReopen = () => {
    function returnForm(confirmed) {
      if (!confirmed) {
        return;
      }
      http
        .put(APP_CONFIG.apiDomain + "/api/Form/AdminReopenForms", [form.id], {
          headers: {
            "Content-Type": "application/json",
          },
        })
        .then((response) => {
          // popup success if OK
          if (response.data.resultStatus === 1) {
            messageService.successDialog("Questionnaire reopened.");
            backHandler && backHandler();
          } else {
            messageService.errorDialog(response.data.message);
          }
        })
        .catch(() => {
          messageService.errorDialog("Failed to reopen questionnaire.");
        });
    }

    const surveyTitle = questionnaire ? questionnaire.name : "Partner Plan";
    const partnerName = form ? form.partnerName : "Partner";
    messageService.confirmDialog(`Are you sure to reopen the ${surveyTitle} to ${partnerName}`, returnForm);
  };

  // Render reviewer comments history section (only for form owners)
  const renderReviewerCommentsHistory = () => {
    // Only show for form owners and when there are comments to display
    if (currentUserFormRole !== UserFormRole.FormOwner || !reviewerComments || reviewerComments.length === 0) {
      return null;
    }

    return (
      <div className="reviewer-comments-section">
        <Panel header="Reviewer Comments History" toggleable collapsed={true} className="reviewer-comments-panel">
          <div className="comments-list">
            {reviewerComments.map((comment, index) => (
              <div
                key={comment.id || index}
                className="comment-item"
                style={{
                  marginBottom: index < reviewerComments.length - 1 ? "1rem" : "0",
                  padding: "1rem",
                  border: "1px solid #e0e0e0",
                  borderRadius: "4px",
                  backgroundColor: "#f9f9f9",
                }}
              >
                <div
                  className="comment-header"
                  style={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    marginBottom: "0.5rem",
                    fontSize: "0.9rem",
                    color: "#666",
                  }}
                >
                  <span className="reviewer-name">
                    <strong>{comment.submittedByName || "Reviewer"}</strong>
                  </span>
                  <span className="comment-date">{comment.submittedOn ? formatDateTime(comment.submittedOn) : "Date not available"}</span>
                </div>
                <div
                  className="comment-cycle"
                  style={{
                    fontSize: "0.8rem",
                    color: "#888",
                    marginBottom: "0.5rem",
                  }}
                >
                  <em>{comment.cycleDescription} Cycle</em>
                </div>
                <div
                  className="comment-text"
                  style={{
                    lineHeight: "1.5",
                    whiteSpace: "pre-wrap",
                  }}
                >
                  {comment.comments}
                </div>
              </div>
            ))}
          </div>
        </Panel>
      </div>
    );
  };

  if (!authService || !authService.isAuthenticated()) {
    return (
      <div className="survey-container">
        <h2>Partner Plans</h2>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="survey-container">
        <div className="p-4">
          <h2>Partner Plans</h2>
          <p>Loading plan...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div id="surveyContainer">
        <div>
          <div className="p-message p-message-error">
            <div className="p-message-wrapper">
              <span className="p-message-icon pi pi-times-circle"></span>
              <div className="p-message-text">
                <span className="p-message-summary">Error</span>
                <div className="p-message-detail">{error}</div>
              </div>
            </div>
          </div>
          <button className="p-button p-component p-button-outlined mt-3" onClick={handleReturnToHome}>
            <span className="p-button-icon pi pi-arrow-left"></span>
            <span className="p-button-label">Back</span>
          </button>
        </div>
      </div>
    );
  }

  if (!surveyModel) {
    return (
      <div className="survey-container">
        <div className="p-4">
          <h2>Partner Plans</h2>
          <p>No plan available at this time.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="survey-container">
      {/* Header Section */}
      <div className="partner-planning-header">
        <div className="header-content">
          <div className="bdo-logo">
            <img
              alt="BDO logo"
              src={`${APP_CONFIG.basePath}/logo.png`}
              onError={(e) => (e.target.src = `/images/BDO.png`)}
              className="bdo-logo-image"
            />
            <span className="partner-planning-text">
              {formId
                ? `${
                    form?.year || questionnaire?.year || year ? `${form?.year || questionnaire?.year || year}` : ""
                  } Partner Planning Tool - Review Mode`
                : `${form?.year || questionnaire?.year || year ? `${form?.year || questionnaire?.year || year}` : ""} Partner Planning Tool`}
            </span>
          </div>
          <div className="header-actions">
            {/* Submit Admin Update button - only visible when IsAdminEditing is true */}
            {isAdminEditing && (
              <button
                className="submit-admin-update-btn"
                onClick={handleSubmitAdminUpdate}
                style={{
                  backgroundColor: "#28a745",
                  color: "white",
                  border: "none",
                  padding: "8px 16px",
                  borderRadius: "4px",
                  cursor: "pointer",
                  marginRight: "10px",
                  fontSize: "14px",
                  fontWeight: "600",
                }}
              >
                <i className="pi pi-check" style={{ marginRight: "5px" }}></i>
                Submit Admin Update
              </button>
            )}

            {/* Send Back to Partner button - only visible for reviewers when form is Under Review */}
            {canSendBackToPartner() && (
              <button
                className="send-back-btn"
                onClick={handleSendBackToPartner}
                style={{
                  backgroundColor: "#f39c12",
                  color: "white",
                  border: "none",
                  padding: "8px 16px",
                  borderRadius: "4px",
                  cursor: "pointer",
                  marginRight: "10px",
                  fontSize: "14px",
                }}
              >
                <i className="pi pi-send" style={{ marginRight: "5px" }}></i>
                Send Back to Partner
              </button>
            )}

            {/* <button className="export-pdf-btn" onClick={handleExportToPDF}>
                <i className="pi pi-file-pdf"></i>
                Export to PDF
              </button>  */}
            <button className="export-pdf-btn" onClick={handleAlternativePDFExport} style={{ marginLeft: "10px" }}>
              <i className="pi pi-print"></i>
              Print/Export to PDF
            </button>
            <button className="return-home-btn" onClick={handleReturnToHome}>
              Return to Home
            </button>
          </div>
        </div>
        <div className="form-description">
          This form guides partners in creating a personal plan to achieve the firm's {year} strategy. Complete all sections to set qualitative and
          quantitative targets.
        </div>
      </div>

      {/* Admin Modification Audit History - Collapsible section at the top */}
      {formId && (
        <div style={{ marginBottom: "1rem" }}>
          <AdminModificationAuditHistory formId={formId} visible={true} />
        </div>
      )}

      {/* Partner Details Section - Collapsible, only for Form Owners */}
      {currentUserFormRole === UserFormRole.FormOwner && form && (
        <div className="partner-details-section" style={{ marginBottom: "1rem" }}>
          <Panel header="Partner Details" toggleable collapsed={false} className="partner-details-panel">
            <div className="partner-details-table">
              <div className="partner-details-row">
                <div className="detail-item">
                  <span className="detail-label">Partner Name:</span>
                  <span className="detail-value">{form?.partnerName || ""}</span>
                </div>
                <div className="detail-item">
                  <span className="detail-label">Service Line:</span>
                  <span className="detail-value">{form?.serviceLine || ""}</span>
                </div>
                <div className="detail-item">
                  <span className="detail-label">Sub-service Line:</span>
                  <span className="detail-value">{form?.subServiceLine || ""}</span>
                </div>
                <div className="detail-item">
                  <span className="detail-label">Location:</span>
                  <span className="detail-value">{form?.location || ""}</span>
                </div>
                <div className="detail-item">
                  <span className="detail-label">Primary Reviewer:</span>
                  <span className="detail-value">{form?.primaryReviewerName || ""}</span>
                </div>
                <div className="detail-item">
                  <span className="detail-label">Secondary Reviewer:</span>
                  <span className="detail-value">{form?.secondaryReviewerName || ""}</span>
                </div>
                <div className="status-container">
                  <div className="status-badge">
                    <span className="status-text">STATUS: </span>
                    <span className={`status-value ${getFormStatusClass(form?.status)}`}>
                      {form?.statusString || getFormStatusName(form?.status) || "DRAFT"}
                    </span>
                  </div>
                  <div className="autosaved-container">
                    {form?.partnerSubmittionDate ? (
                      <div className="autosaved-info">
                        Autosaved: {new Date(form.partnerSubmittionDate).toLocaleDateString()} at{" "}
                        {new Date(form.partnerSubmittionDate).toLocaleTimeString()}
                      </div>
                    ) : (
                      <div className="autosaved-info">{/* Show blank when no autosave date available */}</div>
                    )}
                  </div>
                </div>
              </div>
            </div>
            {/* Reviewer Comments History Section - only for Form Owners */}
            {renderReviewerCommentsHistory()}
          </Panel>
        </div>
      )}
      {/* Wizard Progress Indicator. Temporarily hidden no need at this moment. */}
      {/* {surveyModel && currentPageInfo.pageCount > 1 && (
        <div className="wizard-progress-info">
          <div className="progress-text">
            <span className="current-step">Step {currentPageInfo.pageNo + 1}</span>
            <span className="total-steps"> of {currentPageInfo.pageCount}</span>
            {currentPageInfo.pageTitle && <span className="page-title">: {currentPageInfo.pageTitle}</span>}
          </div>
        </div>
      )} */}

      {/* Partner Information Section - only show for non-Form Owners (Reviewers, Admins, ELT) */}
      {currentUserFormRole !== UserFormRole.FormOwner && form && (
        <div
          className="partner-info-section"
          style={{
            background: "#f8f9fa",
            padding: "1rem",
            marginBottom: "1rem",
            borderRadius: "4px",
          }}
        >
          <h3>Partner Information</h3>
          <div style={{ display: "grid", gridTemplateColumns: "repeat(auto-fit, minmax(250px, 1fr))", gap: "1rem" }}>
            <div>
              <strong>Partner Name:</strong> {form.partnerName || "N/A"}
            </div>
            <div>
              <strong>Service Line:</strong> {form.serviceLine || "N/A"}
            </div>
            <div>
              <strong>Sub-Service Line:</strong> {form.subServiceLine || "N/A"}
            </div>
            <div>
              <strong>Location:</strong> {form.location || "N/A"}
            </div>
            <div>
              <strong>Primary Reviewer:</strong> {form.primaryReviewerName || "N/A"}
            </div>
            <div>
              <strong>Secondary Reviewer:</strong> {form.secondaryReviewerName || "N/A"}
            </div>
            <div>
              <strong>Status:</strong> {form.statusString || getFormStatusName(form.status) || "DRAFT"}
            </div>
            <div>
              <strong>Year:</strong> {form.year || "N/A"}
            </div>
          </div>
        </div>
      )}

      <div className="survey-wrapper mt-1">
        <Survey model={surveyModel} />
      </div>

      {/* Auto-save indicator - only show for editable forms */}
      {/* {form && isFormEditableByOwner(form.status) && (
          <div className="auto-save-indicator mt-2">
            <small className="text-muted">
              <i className="pi pi-info-circle mr-1"></i>
              Your progress is automatically saved as you work
            </small>
          </div>
        )} */}

      {/* <div className="flexbox flexbox--flex-end" style={{marginTop: "30px", marginRight:"17px"}}>
          {user_role === 'PPAdministrator' && (
            <>
              <Button className="action"
                disabled={!(form && form.status !== FormStatus.PlanningDraft && form.status !== FormStatus.PlanningReopened 
                  && form.status !== FormStatus.MidYearReviewDraft && form.status !== FormStatus.MidYearReviewReopened
                  && form.status !== FormStatus.YearEndReviewDraft && form.status !== FormStatus.YearEndReviewReopened)}
                label="Reopen"
                style={{ padding: '15px 20px' }}
                onClick={adminReopen} />
            </>
          )}
          {backHandler && (
            <>
              <Button className="action"
                label="Exit"
                style={{ padding: '15px 20px', marginLeft: '10px'}}
                onClick={() => {backHandler();}} />
            </>
          )}
        </div> */}

      {/* Reviewer Comments Dialog, when Reviewer clicks "Send Back to Partner", 
      this popup modal displays and asks for comments */}
      <ReviewerCommentsDialog
        visible={showCommentsDialog}
        onHide={handleCommentsDialogHide}
        onConfirm={handleCommentsConfirm}
        loading={sendingBack}
        title="Send Back to Partner"
        message={`Please provide comments explaining why the form for ${form?.partnerName || "this partner"} needs to be revised:`}
      />
    </div>
  );
};

/**
 * Set up partner data auto-population for dropdowns with mapFromGroup
 * @param {Object} survey - The SurveyJS model instance
 * @param {string} partnerId - Partner ID for data retrieval
 * @param {number} year - Year for data retrieval
 */
async function setupPartnerReferenceDataAutoPopulation(survey, partnerId, year) {
  console.log("🔧 setupPartnerReferenceDataAutoPopulation called with:", { partnerId, year });

  // Find all text questions with auto-population enabled (legacy and cycle-specific)
  const autoPopulateFields = survey.getAllQuestions().filter((q) => {
    if (q.getType() !== "text") return false;

    // Check for any linkedToGroup[Cycle] property (legacy or cycle-specific)
    return q.linkedToGroupPlanning || q.linkedToGroupMidYear || q.linkedToGroupYearEnd;
  });

  console.log(
    "📝 Found auto-populate text fields:",
    autoPopulateFields.map((f) => ({
      name: f.name,
      linkedToPlanning: f.linkedToGroupPlanning,
      linkedToMidYear: f.linkedToGroupMidYear,
      linkedToYearEnd: f.linkedToGroupYearEnd,
    }))
  );

  // Create mapping of dropdown -> text fields with cycle information
  const dropdownToTextMapping = {};
  autoPopulateFields.forEach((textField) => {
    // Determine which linkedToGroup[Cycle] property to use and the corresponding cycle
    let dropdownName;
    let cycle;

    if (textField.linkedToGroupPlanning) {
      dropdownName = textField.linkedToGroupPlanning;
      cycle = PartnerPlanCycle.Planning;
    } else if (textField.linkedToGroupMidYear) {
      dropdownName = textField.linkedToGroupMidYear;
      cycle = PartnerPlanCycle.MidYearReview;
    } else if (textField.linkedToGroupYearEnd) {
      dropdownName = textField.linkedToGroupYearEnd;
      cycle = PartnerPlanCycle.YearEndReview;
    }

    if (dropdownName) {
      if (!dropdownToTextMapping[dropdownName]) {
        dropdownToTextMapping[dropdownName] = [];
      }
      // Store both the text field and its associated cycle
      dropdownToTextMapping[dropdownName].push({ textField, cycle });
    }
  });

  // Debug: Log all questions first
  const allQuestions = survey.getAllQuestions();
  console.log(
    "🔍 All questions in survey:",
    allQuestions.map((q) => ({
      name: q.name,
      type: q.getType(),
      mapFromGroup: q.mapFromGroup,
      hasMapFromGroup: !!q.mapFromGroup,
    }))
  );

  // Set up dynamic choice loading for dropdowns with mapFromGroup
  const mapFromGroupDropdowns = survey.getAllQuestions().filter((q) => q.getType() === "dropdown" && q.mapFromGroup);
  console.log(
    "🎯 Found dropdowns with mapFromGroup:",
    mapFromGroupDropdowns.map((d) => ({ name: d.name, group: d.mapFromGroup }))
  );

  // Load choices for mapFromGroup dropdowns
  for (const dropdown of mapFromGroupDropdowns) {
    try {
      console.log(`🌐 Calling API: getColumnsByGroup(${year}, "${dropdown.mapFromGroup}") for dropdown "${dropdown.name}"`);
      const columns = await partnerReferenceDataUploadService.getColumnsByGroup(year, dropdown.mapFromGroup);
      dropdown.choices = columns;
      console.log(`✅ Loaded ${columns.length} choices for dropdown ${dropdown.name}:`, columns);
    } catch (error) {
      console.error(`❌ Error loading choices for dropdown ${dropdown.name}:`, error);
    }
  }

  // Set up value change handlers
  survey.onValueChanged.add(async (sender, options) => {
    await handlePartnerReferenceDataAutoPopulation(sender, options, dropdownToTextMapping, partnerId, year);
  });

  // Set up initial population for existing values
  Object.keys(dropdownToTextMapping).forEach((dropdownName) => {
    const dropdown = survey.getQuestionByName(dropdownName);
    if (dropdown && dropdown.value) {
      // Populate on initial load if dropdown already has a value
      handlePartnerReferenceDataAutoPopulation(survey, { name: dropdownName, value: dropdown.value }, dropdownToTextMapping, partnerId, year);
    }
  });
}

/**
 * Handle partner data auto-population when dropdown values change
 * @param {Object} sender - The survey instance
 * @param {Object} options - The value change options
 * @param {Object} dropdownToTextMapping - Mapping of dropdown names to text fields with cycle info
 * @param {string} partnerId - Partner ID for data retrieval
 * @param {number} year - Year for data retrieval
 */
async function handlePartnerReferenceDataAutoPopulation(sender, options, dropdownToTextMapping, partnerId, year) {
  const dropdownName = options.name;
  const selectedColumn = options.value;

  // Check if this dropdown has linked text fields
  const linkedTextFieldsWithCycles = dropdownToTextMapping[dropdownName];
  if (!linkedTextFieldsWithCycles || linkedTextFieldsWithCycles.length === 0) {
    return;
  }

  const dropdown = sender.getQuestionByName(dropdownName);
  if (!dropdown || !dropdown.mapFromGroup) {
    return;
  }

  try {
    if (!selectedColumn) {
      // Clear linked text fields when no selection
      linkedTextFieldsWithCycles.forEach(({ textField }) => {
        sender.setValue(textField.name, "");
      });
      return;
    }

    // Show loading state in text fields
    linkedTextFieldsWithCycles.forEach(({ textField }) => {
      sender.setValue(textField.name, "Loading...");
    });

    // Process each text field with its specific cycle
    for (const { textField, cycle } of linkedTextFieldsWithCycles) {
      try {
        // Fetch partner reference data value with the text field's specific cycle
        const dataValue = await partnerReferenceDataUploadService.getPartnerReferenceDataValue(
          year,
          cycle,
          dropdown.mapFromGroup,
          selectedColumn,
          partnerId
        );

        // Update the text field with the fetched value
        const displayValue = dataValue !== null && dataValue !== undefined ? String(dataValue) : "No data available";
        sender.setValue(textField.name, displayValue);

        console.log(`Auto-populated ${textField.name} with value: ${displayValue} (cycle: ${cycle})`);
      } catch (error) {
        console.error(`Error fetching data for ${textField.name} (cycle: ${cycle}):`, error);
        sender.setValue(textField.name, "Error loading data");
      }
    }
  } catch (error) {
    console.error("Error in partner data auto-population:", error);

    // Show error state in all text fields
    linkedTextFieldsWithCycles.forEach(({ textField }) => {
      sender.setValue(textField.name, "Error loading data");
    });
  }
}

export default PartnerPlanQuestionnaire;
