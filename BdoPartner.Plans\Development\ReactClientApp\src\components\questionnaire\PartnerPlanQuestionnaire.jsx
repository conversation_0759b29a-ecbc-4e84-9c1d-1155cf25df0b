import React, { useState, useEffect, useContext, useCallback } from "react";
import { Survey } from "survey-react-ui";
import { Model } from "survey-core";
import { AuthContext } from "../../core/auth/components/authProvider";
import { messageService } from "../../core/message/messageService";
import { useLoadingControl } from "../../core/loading/hooks/useLoadingControl";
import surveyService from "../../services/surveyService";
import FormAnswerUtility from "./formAnswerUtilities";

// Import the latest Survey.js CSS themes
import "survey-core/survey-core.css";
import "../../survey-v2-styles.css"; // Import our custom Survey.js v2 styles

export const PartnerPlanQuestionnaire = ({ year }) => {
  const [surveyModel, setSurveyModel] = useState(null);
  const [questionnaire, setQuestionnaire] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [hasLoaded, setHasLoaded] = useState(false); // Track if data has been loaded

  const authService = useContext(AuthContext);

  // Disable loading interceptor for survey component
  useLoadingControl('survey', true);

  // Reset hasLoaded when year changes to trigger data reload
  useEffect(() => {
    setHasLoaded(false);
  }, [year]);

  // Use useEffect directly instead of useCallback to avoid dependency issues
  useEffect(() => {
    // Prevent multiple calls if already loaded or not authenticated
    if (hasLoaded || !authService || !authService.isAuthenticated()) {
      return;
    }

    const loadData = async () => {
      try {
        console.log('🔄 Loading questionnaire data - this should only appear once per component mount');
        setLoading(true);
        setError(null);

        // Get the questionnaire for the specified year
        const targetYear = year ? parseInt(year) : new Date().getFullYear();
        const questionnaires = await surveyService.getQuestionnairesByYear(targetYear);

        if (questionnaires && questionnaires.length > 0) {
          const activeQuestionnaire = questionnaires[0]; // Get the first active questionnaire
          setQuestionnaire(activeQuestionnaire);

          // Parse the DefinitionJson to create the survey model
          if (activeQuestionnaire.definitionJson) {
            try {
              const surveyJson = JSON.parse(activeQuestionnaire.definitionJson);
              const survey = new Model(surveyJson);

              // Configure survey settings
              survey.clearInvisibleValues = false;
              survey.allowShowPreview = true;
              survey.showPreviewBeforeComplete = "showAllQuestions";

              // Apply modern theme for better styling
              // Apply BDO red theme using modern Survey.js v2.2.2 approach
              survey.applyTheme({
                "themeName": "default-light",
                "colorPalette": "light",
                "isPanelless": false,
                // Custom CSS variables for BDO branding (equivalent to old StylesManager)
                "cssVariables": {
                  "--sjs-primary-backcolor": "#ED1A3B",           // Main color
                  "--sjs-primary-forecolor": "#FFFFFF",           // Text on primary
                  "--sjs-primary-backcolor-light": "#F5E6EA",     // Light variant
                  "--sjs-primary-backcolor-dark": "#AF273C",      // Hover color
                  "--sjs-secondary-backcolor": "#ED1A3B",         // Secondary color
                  "--sjs-secondary-forecolor": "#FFFFFF",         // Text on secondary
                  "--sjs-general-backcolor-dim": "#F3F2F1",       // Background
                  "--sjs-border-default": "#959597",              // Border color
                  "--sjs-border-light": "#D4D4D4"                 // Light border
                }
              });

              survey.showProgressBar = true;

              // Create a completion handler that doesn't depend on state
              const completionHandler = (sender) => {
                try {
                  const surveyData = {
                    questionnaireId: activeQuestionnaire.id,
                    userId: authService?.user?.profile?.sub,
                    data: sender.data,
                    completedAt: new Date().toISOString()
                  };

                  console.log("Survey completed with data:", surveyData);
                  messageService.successToast("Survey completed successfully!");

                  // TODO: Implement survey data saving to backend
                  // await surveyService.saveSurveyData(surveyData);

                } catch (error) {
                  console.error("Error handling survey completion:", error);
                  messageService.errorToast("Failed to save survey data. Please try again.");
                }
              };

              // Set up survey completion handler
              survey.onComplete.add(completionHandler);

              // Set up page change handler
              survey.onCurrentPageChanged.add(handlePageChanged);

              // Set completion message
              survey.completedHtml = `<h3>Thank you for completing ${survey.title || activeQuestionnaire.name}!</h3>`;

              // Add markdown support
              survey.onTextMarkdown.add(function (survey, options) {
                // Convert markdown text to HTML
                var htmlContent = FormAnswerUtility.convertToHtml(options.text);
                // Set the HTML content
                options.html = htmlContent;
              });

              setSurveyModel(survey);
              setHasLoaded(true); // Mark as loaded
            } catch (parseError) {
              console.error("Error parsing survey JSON:", parseError);
              setError("Failed to load survey form. Invalid survey definition.");
            }
          } else {
            setError("No survey definition found for the current year.");
          }
        } else {
          setError(`No active questionnaire found for year ${targetYear}.`);
        }
      } catch (err) {
        console.error("Error loading questionnaire:", err);
        setError("Failed to load questionnaire. Please try again later.");
      } finally {
        setLoading(false);
      }
    };

    loadData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [hasLoaded, year]); // Depend on hasLoaded and year to reload when year changes

  // Create a separate function for retry that can be called from the button
  const retryLoadQuestionnaire = useCallback(() => {
    setHasLoaded(false); // This will trigger the useEffect above
  }, []);

  const handlePageChanged = (_sender, options) => {
    console.log("Survey page changed:", options);
    // You can add page change logic here if needed
  };

  const handleValueChanged = (_sender, options) => {
    console.log("Survey value changed:", options);
    // You can add value change logic here if needed
  };

  if (!authService || !authService.isAuthenticated()) {
    return (
      <div className="survey-container">
          <h2>Partner Plans</h2>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="survey-container">
        <div className="p-4">
          <h2>Partner Plans</h2>
          <p>Loading plan...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div id ="surveyContainer">
        <div>
          <div className="p-message p-message-error">
            <div className="p-message-wrapper">
              <span className="p-message-icon pi pi-times-circle"></span>
              <div className="p-message-text">
                <span className="p-message-summary">Error</span>
                <div className="p-message-detail">{error}</div>
              </div>
            </div>
          </div>
          <button
            className="p-button p-component p-button-outlined mt-3"
            onClick={retryLoadQuestionnaire}
          >
            <span className="p-button-icon pi pi-refresh"></span>
            <span className="p-button-label">Retry</span>
          </button>
        </div>
      </div>
    );
  }

  if (!surveyModel) {
    return (
      <div className="survey-container">
        <div className="p-4">
          <h2>Partner Plans</h2>
          <p>No plan available at this time.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="survey-container">
        <h2>{questionnaire?.name}</h2>
        <div className="survey-wrapper mt-4">
          <Survey 
            model={surveyModel}
            onValueChanged={handleValueChanged}
          />
        </div>
    </div>
  );
};

export default PartnerPlanQuestionnaire;
