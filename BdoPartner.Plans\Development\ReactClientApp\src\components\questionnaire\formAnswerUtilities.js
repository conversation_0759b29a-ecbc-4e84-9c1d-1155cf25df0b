import { format } from "date-fns";

/**
 * Set mode and visibility for different user roles and form states
 * @param {string} viewer_role - The role of the current viewer
 * @param {object} activeForm - The active form object
 * @param {object} answers - The answers object to modify
 * @param {boolean} hideCompleteButton - Whether to hide the complete button
 */
function setMode(viewer_role, activeForm, answers, hideCompleteButton) {
    // Initialize default visibility settings
    answers.enablePartnerValues = false;
    answers.showPartnerComment = false;
    answers.enablePartnerComment = false;
    answers.showReviewerComment = false;
    answers.enableReviewerComment = false;
    answers.showSignature = false;
    answers.showPartnerSignature = false;
    answers.showReviewerSignature = false;
    
    // Set visibility based on role and form status
    switch(viewer_role) {
        case 'partner':
            if (activeForm.status === 0 || activeForm.status === 1) { // Draft or In Progress
                answers.enablePartnerValues = true;
                answers.enablePartnerComment = true;
            }
            break;
        case 'reviewer':
            if (activeForm.status === 2 || activeForm.status === 3) { // Submitted or Under Review
                answers.showPartnerComment = true;
                answers.enableReviewerComment = true;
            }
            break;
        default:
            // Admin or other roles - show all
            answers.showPartnerComment = true;
            answers.showReviewerComment = true;
            break;
    }
}

/**
 * Convert UTC date to local time format
 * @param {string} value - UTC date string
 * @returns {string} Formatted local time string
 */
function formToLocalTime(value) {
    try {
        if (!value) 
            return '';
        
        let processValue = value;
        if (processValue.slice(-1).toUpperCase() !== "Z") {
            processValue = processValue + "Z";
        }

        return format(new Date(processValue), 'MMM dd, yyyy hh:mm a');
    }
    catch(err) {
        console.log('Error formatting date:', err);
    }

    return value;
}

/**
 * Transfer form information to answers object
 * @param {object} form - The form object
 * @param {object} answers - The answers object to populate
 */
function transferFormInfoToAnswer(form, answers) {
    // Transfer basic form data
    answers.partnerName = form.partnerName || '';
    answers.employeeNumber = form.employeeNumber || '';
    answers.year = form.year || new Date().getFullYear();
    
    // Transfer comments
    answers.partnerComments = form.partnerComments || '';
    answers.reviewerComments = form.reviewerComments || '';
    
    // Transfer signatures and dates
    if (form.partnerSignature) {
        answers.partnerSignature = form.partnerSignature;
        answers.partnerSignatureName = (form.partnerName || '') + " at " + formToLocalTime(form.partnerSubmissionDate);
    }
    
    if (form.reviewerSignature) {
        answers.reviewerSignature = form.reviewerSignature;
        answers.reviewerSignatureName = (form.reviewerName || '') + " at " + formToLocalTime(form.reviewerApprovedDate);
    }
    
    // Update the form's answer JSON
    if (form.userAnswer) {
        form.userAnswer.answer = JSON.stringify(answers);
    }
}

/**
 * Determine if the form should be shown in preview mode
 * @param {string} viewer_role - The role of the current viewer
 * @param {object} form - The form object
 * @returns {boolean} Whether to show in preview mode
 */
const showInPreviewMode = (viewer_role, form) => {
    switch(viewer_role) {
        case 'partner':
            switch(form.status) {
                case 0: // Draft
                case 1: // In Progress
                case 4: // Rejected
                    return false; // Allow editing
            }
            break;
        case 'reviewer':
            switch(form.status){
                case 2: // Submitted
                case 3: // Under Review
                    return false; // Allow editing
            }
            break;
    }

    return true; // Show in preview mode by default
}

/**
 * Convert markdown text to HTML
 * This function handles basic markdown syntax and converts it to HTML
 * @param {string} text - The markdown text to convert
 * @returns {string} HTML string
 */
const makeHtml = (text) => {
    if (!text)
        return text;

    const htmlText = text
        // Headers
        .replace(/^### (.*$)/gim, '<h3>$1</h3>')
        .replace(/^## (.*$)/gim, '<h2>$1</h2>')
        .replace(/^# (.*$)/gim, '<h1>$1</h1>')
        // Blockquotes
        .replace(/^\> (.*$)/gim, '<blockquote>$1</blockquote>')
        // Bold and italic
        .replace(/\*\*(.*?)\*\*/gim, '<b>$1</b>')
        .replace(/\*(.*?)\*/gim, '<i>$1</i>')
        // Images
        .replace(/!\[(.*?)\]\((.*?)\)/gim, "<img alt='$1' src='$2' />")
        // Links
        .replace(/\[(.*?)\]\((.*?)\)/gim, "<a href='$2' target='_blank'>$1</a>")
        // Line breaks
        .replace(/\n$/gim, '<br />')
        // Convert newlines to <br> tags
        .replace(/\n/gim, '<br />');

    return htmlText.trim();
}

/**
 * Utility object containing all form answer helper functions
 */
const FormAnswerUtility = {
    setMode: setMode,
    transferFormInfoToAnswer: transferFormInfoToAnswer,
    showInPreviewMode: showInPreviewMode,
    formToLocalTime: formToLocalTime,
    convertToHtml: makeHtml
}

export default FormAnswerUtility;
